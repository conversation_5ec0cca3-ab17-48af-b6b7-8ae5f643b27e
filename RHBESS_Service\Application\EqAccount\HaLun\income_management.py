#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJF
#@Date         : 2023-02-15 10:47:27
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\HaLun\fault.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-22 16:05:41
import json
import os
import logging
import tornado.web
from sqlalchemy import func

from Application.Models.User.income_r import RIncome
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.User.doc_t import Tdoc
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import Translate_cls
import uuid

file_title = ['文档编号','文档名称','文档格式','归属组织','描述','上传日期','上传人员']

class IncomeManagementIntetface(BaseHandler):
    ''' 收益管理 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)
        try:
        # if 1:
            data = []
            if kt == 'GetIncomeType':#收益类别
                if lang=='en':
                    d = [{'id':1,"name":'Primary FM revenue'},{'id':2,"name":'AGC income'},{'id':3,"name":'Spot income'},{'id':4,"name":'Ancillary service revenue'},{'id':5,"name":'Profit from peak cutting and valley filling'},{'id':6,"name":'Demand side response benefits'},{'id':7,"name":'Other income'}]
                else:
                    d = [{'id': 1, "name": '一次调频收益'}, {'id': 2, "name": 'AGC收益'}, {'id': 3, "name": '现货收益'},
                         {'id': 4, "name": '辅助服务收益'}, {'id': 5, "name": '削峰填谷收益'}, {'id': 6, "name": '需求侧响应收益'},
                         {'id': 7, "name": '其他收益'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetIncomeList': # 所有收益记录
                descr = self.get_argument('descr',None)# 描述
                mon = self.get_argument('mon', None)  # 所在月份
                income_ty = self.get_argument('income_ty', None)  # 收益类别
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'

                filter = [RIncome.is_use==1,RIncome.in_ts==2]
                if DEBUG:
                    logging.info('descr:%s,mon:%s,income_ty:%s,pageNum:%s,pageSize:%s'%(descr,mon,income_ty,pageNum,pageSize))
                if descr:
                    filter.append(RIncome.descr==descr)
                if mon:
                    filter.append(RIncome.day==mon)
                if income_ty:
                    filter.append(RIncome.income_ty==income_ty)
                total = user_session.query(func.count(RIncome.id)).filter(*filter).scalar()
                pages = user_session.query(RIncome).filter(*filter).order_by(RIncome.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for pag in pages:
                    o = eval(str(pag))
                    if o['annex']!='':
                        o['annex'] = json.loads(o['annex'])
                    if o['filename']!='':
                        if lang == 'en':
                            if o['en_filename']:
                                en_filename = o['en_filename'].replace('"[', '[').replace(']"', ']')
                                o['filename'] = json.loads(en_filename)
                            else:
                                o['filename'] = ''
                        else:
                            o['filename'] = json.loads(o['filename'])
                        # o['filename'] = json.loads(o['en_filename']) if lang == 'en' else json.loads(o['filename'])
                    if o['annex_in']!='':
                        o['annex_in'] = json.loads(o['annex_in'])
                    if lang == 'en':
                        o['descr'] = o.get('en_descr')
                        o['remark'] = o.get('en_remark')
                        o['income_ty'] = o.get('en_income_ty')
                    data.append(o)
                return self.returnTotalSuc(data,total)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

        # user_session.close()
        # user_session.rollback()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)
        try:
        # if 1:
            if kt == 'AddIncome': # 添加收益
                db = self.get_argument('db', None)#电站
                descr = self.get_argument('descr', None)#描述
                income_ty = self.get_argument('income_ty', None)#收益类别
                mon = self.get_argument('mon', None)  # 所在月份
                time_frame = self.get_argument('time_frame', None)#时间范围
                income_yu = self.get_argument('income_yu', None)#收益(元)
                chag = self.get_argument('chag', None)#充电量
                disg = self.get_argument('disg', None)#放电量
                remark = self.get_argument('remark', None)#备注
                if not lang:
                    lang = 'zh'
                if not db or not descr or not mon or not income_ty or not income_yu:
                    return self.customError('参数不完整') if lang == 'zh' else self.customError("Incomplete parameters")
                remark = ';'.join(remark.split()) if remark else ''

                if DEBUG:
                    logging.info('mon:%s,time_frame:%s,income_yu:%s,descr:%s'%(mon,time_frame,income_yu,descr))

                p= self._income_exist(descr,mon,income_ty)
                if p:
                    logging.info("收益已重复")
                    return self.customError('收益已重复') if lang == 'zh' else self.customError("Revenue has been duplicated")

                files = self.request.files

                file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                # file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)

                imgs = files.get('files')

                path_all=[]
                filename_all=[]
                if imgs:
                    for i in imgs:
                        data = i.get('body')
                        filename = i.get('filename')
                        doc_format = str(os.path.splitext(filename)[1])  # 格式
                        uploadfilename = str(uuid.uuid1()) + doc_format
                        path = '%s/%s' % (file_path, uploadfilename)
                        path_all.append(path)
                        filename_all.append(filename)
                        file = open(path, 'wb')
                        file.write(data)
                        file.close()
                annex_in=[]
                if path_all:
                    for i in path_all:
                        indx = path_all.index(i)
                        annex_in.append(indx)
                path_all = json.dumps(path_all, ensure_ascii=False)
                filename_all = json.dumps(filename_all, ensure_ascii=False)
                annex_in = json.dumps(annex_in, ensure_ascii=False)

                page = user_session.query(RIncome).filter(RIncome.descr == descr, RIncome.is_use == 1, RIncome.day == mon[:4], RIncome.in_ts == 1).first()
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                descr_res = t_cls.str_chinese(descr)
                filename_res = t_cls.str_chinese(filename_all)
                remark_res = t_cls.str_chinese(remark)
                income_ty_res = t_cls.str_chinese(income_ty)
                if ty == 2:
                    en_descr = descr_res
                    en_filename = json.dumps(filename_res)
                    en_remark = remark_res
                    en_income_ty = income_ty_res
                else:
                    en_descr = descr
                    descr = descr_res
                    en_filename = filename_all
                    filename_all = filename_res
                    en_remark = remark
                    remark = remark_res
                    en_income_ty = income_ty
                    income_ty = income_ty_res
                if page:
                    income_y=  float(page.income)+(float(income_yu)/10000) #年收益
                    page.income=income_y
                    page.income_yu=income_y*10000
                    # page.target_income=float(page.target_income) #年目标收益
                else:
                    income_y = float(income_yu) / 10000  # 年收益
                    p1 = RIncome(income=income_y, is_use='1',op_ts=timeUtils.getNewTimeStr(),descr=descr,en_descr=en_descr, day=mon[:4], in_ts='1', station_name=db,target_income=0,income_yu=0)
                    user_session.add(p1)
                p2 = RIncome(income=float(income_yu)/10000,annex=path_all,filename=filename_all,en_filename=en_filename,income_yu=income_yu,remark=remark,en_remark=en_remark,disg=disg,chag=chag,income_ty=income_ty,
                             en_income_ty=en_income_ty,is_use='1',op_ts=timeUtils.getNewTimeStr(),time_frame=time_frame
                            ,descr=descr,en_descr=en_descr,day=mon,in_ts='2',station_name=db,annex_in=annex_in,target_income=0)
                # p3 = RIncome(income=float(income_yu) / 10000, annex=[], filename=[],income_yu=float(income_yu) / 10000, remark='', disg='', chag='', income_ty='收益', is_use='1',op_ts=timeUtils.getNewTimeStr(), time_frame=mon[:4]
                #              , descr=descr, day=mon[:4], in_ts='1', station_name=db, annex_in=[], target_income=0)
                user_session.add(p2)
                # user_session.add(p3)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UpdateIncome': # 修改记录
                id = self.get_argument('id',None)
                db = self.get_argument('db', None)  # 电站
                descr = self.get_argument('descr', None)  # 描述
                income_ty = self.get_argument('income_ty', None)  # 收益类别
                mon = self.get_argument('mon', None)  # 所在月份
                time_frame = self.get_argument('time_frame', None)  # 时间范围
                income_yu = self.get_argument('income_yu', None)  # 收益(元)
                chag = self.get_argument('chag', None)  # 充电量
                disg = self.get_argument('disg', None)  # 放电量
                remark = self.get_argument('remark', None)  # 备注
                annex_in = self.get_argument('annex_in', [])  # 附件地址下标
                if not lang:
                    lang = 'zh'

                if not db or not descr or not mon or not income_ty or not income_yu:
                    return self.customError('参数不完整')
                remark = ';'.join(remark.split()) if remark else ''

                if DEBUG:
                    logging.info('mon:%s,time_frame:%s,income_yu:%s,descr:%s' % (mon, time_frame, income_yu, descr))

                p = self._income_exist(descr, mon,income_ty, id)
                if p:
                    logging.info("收益已重复")
                    return self.customError('收益已重复') if lang == 'zh' else self.customError("Revenue has been duplicated")

                page = user_session.query(RIncome).filter(RIncome.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                files = self.request.files
                file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                # file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                Annexs = user_session.query(RIncome).filter(RIncome.id == id).first()
                annex_in = json.loads(annex_in)
                Annexs_in=[]
                filename_=[]
                annex_=[]
                if Annexs.annex_in:
                    Annexs_in = json.loads(Annexs.annex_in)
                if Annexs.filename:
                    filename_ = json.loads(Annexs.filename)
                if Annexs.annex:
                    annex_ = json.loads(Annexs.annex)
                indx_list=[]#删除下标的列表
                if Annexs_in!=[]:
                    for i in Annexs_in:
                        indx = Annexs_in.index(i)
                        if i not in annex_in:
                            indx_list.append(indx)
                if indx_list!=[]:
                    indx_list.reverse()
                    for l in indx_list:
                        del filename_[l]
                        del annex_[l]
                        user_session.commit()

                imgs = files.get('files')
                if imgs:
                    for i in imgs:
                        data = i.get('body')
                        filename = i.get('filename')
                        doc_format = str(os.path.splitext(filename)[1])  # 格式
                        uploadfilename = str(uuid.uuid1()) + doc_format
                        path = '%s/%s' % (file_path, uploadfilename)
                        file = open(path, 'wb')
                        file.write(data)
                        file.close()
                        filename_.append(filename)
                        annex_.append(path)
                annex_in_=[]
                if annex_:
                    for i in annex_:
                        indx = annex_.index(i)
                        annex_in_.append(indx)

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                descr_res = t_cls.str_chinese(descr)
                filename_res = t_cls.str_chinese(filename_)
                remark_res = t_cls.str_chinese(remark)
                income_ty_res = t_cls.str_chinese(income_ty)
                if ty == 2:
                    en_descr = descr_res
                    en_filename = json.dumps(filename_res)
                    en_remark = remark_res
                    en_income_ty = income_ty_res
                else:
                    en_descr = descr
                    descr = descr_res
                    en_filename = filename_
                    filename_ = filename_res
                    en_remark = remark
                    remark = remark_res
                    en_income_ty = income_ty
                    income_ty = income_ty_res

                annex_ = json.dumps(annex_, ensure_ascii=False)
                filename_ = json.dumps(filename_, ensure_ascii=False)
                annex_in_ = json.dumps(annex_in_, ensure_ascii=False)

                Annexs.station_name = db
                Annexs.descr = descr
                Annexs.en_descr = en_descr
                Annexs.income_ty = income_ty
                Annexs.en_income_ty = en_income_ty
                Annexs.day = mon[:7]
                Annexs.time_frame = time_frame
                Annexs.income_yu = income_yu
                Annexs.chag = chag
                Annexs.disg = disg
                Annexs.remark = remark
                Annexs.en_remark = en_remark
                Annexs.annex_in = annex_in_
                Annexs.filename = filename_
                Annexs.en_filename = en_filename
                Annexs.annex = annex_
                Annexs.op_ts = timeUtils.getNewTimeStr()
                Annexs.income = float(income_yu) / 10000
                user_session.commit()
                # page = user_session.query(RIncome).filter(RIncome.descr == descr, RIncome.is_use == 1,RIncome.day == mon[:4], RIncome.in_ts == 1).first()
                page = user_session.query(RIncome).filter(RIncome.station_name == db, RIncome.is_use == 1,RIncome.day == mon[:4], RIncome.in_ts == 1).first()
                if page:
                    page_sum = user_session.query(func.sum(RIncome.income_yu)).filter(RIncome.station_name == db, RIncome.is_use == 1,RIncome.day.like(mon[:4]+'%'), RIncome.in_ts == 2).first()
                    income = float(page_sum[0])
                    page.income=income/10000
                    page.income_yu = income
                else:
                    income_y = (float(income_yu))/ 10000  # 年收益
                    p1 = RIncome(income=income_y, is_use='1', op_ts=timeUtils.getNewTimeStr(), descr=descr,day=mon[:4], in_ts=1, station_name=db,income_yu=income_y,target_income=0)
                    user_session.add(p1)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeleteIncome': # 逻辑删除
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s'%(id))

                page = user_session.query(RIncome).filter(RIncome.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                descr=page.descr
                day=page.day[:4]

                page_year = user_session.query(RIncome).filter(RIncome.descr ==descr, RIncome.is_use == 1,RIncome.day == day, RIncome.in_ts == 1).first()
                page_year.income=float(page_year.income)-float(page.income)
                page_year.income_yu=(float(page_year.income)-float(page.income))*10000

                if float(page.target_income)>0.01:
                    page_year.target_income=float(page_year.target_income)-float(page.target_income)

                page.is_use = 0
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

        # user_session.close()
        # user_session.rollback()


    def _income_exist(self, descr,mon,income_ty,id = None):
        '''判断收益是否存在'''
        filter = [RIncome.descr == descr,RIncome.income_ty==income_ty, RIncome.is_use == 1,RIncome.day==mon,RIncome.in_ts==2]
        if id:
            filter.append(RIncome.id != id)
        page = user_session.query(RIncome).filter(*filter).first()
        if page:
            return True
        else:
            return False