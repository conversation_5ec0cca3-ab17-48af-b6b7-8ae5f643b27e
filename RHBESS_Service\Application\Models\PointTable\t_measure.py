#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:20:33
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_measure.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 14:24:14


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class MeasurePT(scada_Base):
    ''' 测量量配置表 '''
    __tablename__ = "t_measure"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    device = Column(Integer, nullable=False, comment=u"所属设备")
    no = Column(Integer, nullable=False,comment=u"编号")
    descr = Column(String(256), nullable=False,comment=u"名称")
    unit = Column(CHAR(64), nullable=True,comment=u"单位")
    value_base = Column(Float, nullable=False,comment=u"")
    value_coef = Column(Float, nullable=False,comment=u"系数")
    delta = Column(Float, nullable=False,comment=u"变化值")
    store_flag = Column(Integer, nullable=True,comment=u"")
    gen_timed_flag = Column(Integer, nullable=True,comment=u"")
    rpt_timed_flag = Column(Integer, nullable=True,comment=u"")
    comment = Column(Integer, nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','device':'%s','no':%s,'descr':'%s','unit':'%s','delta':'%s','value_base':'%s','value_coef':'%s','store_flag':'%s','gen_timed_flag':'%s','rpt_timed_flag':'%s','comment':'%s'}" % (
            self.id,self.name,self.device,self.no,self.descr,self.unit,self.delta,self.value_base,self.value_coef,self.store_flag,self.gen_timed_flag,self.rpt_timed_flag,self.comment)