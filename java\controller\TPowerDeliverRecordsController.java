package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsCreateDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsQueryDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsUpdateDTO;
import com.robestec.analysis.service.TPowerDeliverRecordsService;
import com.robestec.analysis.vo.TPowerDeliverRecordsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功率计划下发记录管理API
 */
@RestController
@RequestMapping("/power-deliver-records")
@RequiredArgsConstructor
@Api(tags = "功率计划下发记录管理API")
public class TPowerDeliverRecordsController {

    private final TPowerDeliverRecordsService tPowerDeliverRecordsService;

    @GetMapping
    @ApiOperation("分页查询功率计划下发记录")
    public PageResult<TPowerDeliverRecordsVO> queryTPowerDeliverRecords(@Validated TPowerDeliverRecordsQueryDTO queryDTO) {
        return tPowerDeliverRecordsService.queryTPowerDeliverRecords(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增功率计划下发记录")
    public Result<Long> createTPowerDeliverRecords(@Validated @RequestBody TPowerDeliverRecordsCreateDTO createDTO) {
        return Result.succeed(tPowerDeliverRecordsService.createTPowerDeliverRecords(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增功率计划下发记录")
    public Result createTPowerDeliverRecordsList(@Validated @RequestBody List<TPowerDeliverRecordsCreateDTO> createDTOList) {
        tPowerDeliverRecordsService.createTPowerDeliverRecordsList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改功率计划下发记录")
    public Result updateTPowerDeliverRecords(@PathVariable Long id, @Validated @RequestBody TPowerDeliverRecordsUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tPowerDeliverRecordsService.updateTPowerDeliverRecords(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除功率计划下发记录")
    public Result deleteTPowerDeliverRecords(@PathVariable Long id) {
        tPowerDeliverRecordsService.deleteTPowerDeliverRecords(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取功率计划下发记录详情")
    public Result<TPowerDeliverRecordsVO> getTPowerDeliverRecords(@PathVariable Long id) {
        return Result.succeed(tPowerDeliverRecordsService.getTPowerDeliverRecords(id));
    }

    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询功率计划下发记录")
    public Result<List<TPowerDeliverRecordsVO>> getTPowerDeliverRecordsByUserId(@PathVariable Long userId) {
        return Result.succeed(tPowerDeliverRecordsService.getTPowerDeliverRecordsByUserId(userId));
    }

    @GetMapping("/plan-type/{planType}")
    @ApiOperation("根据计划类型查询功率计划下发记录")
    public Result<List<TPowerDeliverRecordsVO>> getTPowerDeliverRecordsByPlanType(@PathVariable Integer planType) {
        return Result.succeed(tPowerDeliverRecordsService.getTPowerDeliverRecordsByPlanType(planType));
    }

    @GetMapping("/count/user/{userId}")
    @ApiOperation("统计用户的功率计划下发记录数量")
    public Result<Long> countByUserId(@PathVariable Long userId) {
        return Result.succeed(tPowerDeliverRecordsService.countByUserId(userId));
    }
}
