# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/9/2 下午4:27
# <AUTHOR> LiGang
# @Project  : RHBESS_Service
# @File     : view_v2.py
# @Software : PyCharm

import json
import logging
import traceback

import tornado.web
from sqlalchemy import func

from Application.EqAccount.ElePriceDecision.tools import merge_dicts, merge_dicts_with_default, split_time, \
    dws_es_station_db_tool, datatool
from Application.EqAccount.ElePriceDecision.view import RealMeteorologicalSigns
from Application.Models.base_handler_ele import BaseHandler
from Application.Models.ElePriceDescision.models import *
from Tools.DecisionDB.ele_base import get_user_session


class GetRealPartMeteorologicalData(BaseHandler):
    """查询指定区域的实时天气数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            city_id = self.get_argument("city_id", default=None)
            county_id = self.get_argument("county_id", default=None)
            # start_day = self.get_argument("start_day", default=(datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%Y-%m-%d"))
            # day_ = self.get_argument("day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            # moment = self.get_argument("moment", default=datetime.datetime.now().strftime("%H:%M"))
            # date_type = self.get_argument("date_type", default="real")
            # sign = self.get_argument("sign", default="tem")

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数缺失或不存在")

            # 校验参数city_id
            if city_id:
                cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
                cities_id = [city.id for city in cities if city.is_use == '1']
                if int(city_id) not in cities_id:
                    return self.customError("城市/区ID不存在")

                # 校验参数county_id
                if county_id:
                    counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                    counties_id = [county.id for county in counties if county.is_use == "1"]
                    if int(county_id) not in counties_id:
                        return self.customError("县ID不存在")
            #
            if county_id and not city_id:
                return self.customError("城市/区ID参数缺失")

            # 校验参数start_day和end_day
            # try:
            #     start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            #     end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            # except Exception as e:
            #     logging.error(e)
            #     return self.customError("日期参数错误")
            #
            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            # if start_day > end_day:
            #     return self.customError("开始日期不能大于结束日期")

            # 校验参数start_day和end_day
            # try:
            #     day = datetime.datetime.strptime(day_, "%Y-%m-%d")
            #     moment = datetime.datetime.strptime(day_ + ' ' + moment, "%Y-%m-%d %H:%M")
            # except Exception as e:
            #     logging.error(e)
            #     return self.customError("日期或时间参数格式错误")

            # 校验参数date_type
            # if date_type not in ["real", "pre"]:
            #     return self.customError("参数date_type错误")

            # # 校验参数sign
            # if date_type == "real":
            #     if sign not in RealMeteorologicalSigns.__members__:
            #         return self.customError(f"实时气象数据无{sign}数据")
            # else:
            #     if sign not in PreMeteorologicalSigns.__members__:
            #         return self.customError(f"预报天气数据无{sign}数据")

            detail = {}

            # 山西省
            if int(province_id) == 2:
                # 查询县的数据
                if county_id and city_id and province_id:
                    d = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.flag == 3,
                        RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id),
                        RWeatherReal.county_id == int(county_id))
                         .order_by(RWeatherReal.day.desc(), RWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            "county": d.county.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询市的数据
                elif city_id and provinces_id and not county_id:
                    # elif not county_id and city_id:
                    d = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id),
                        RWeatherReal.flag == '2').order_by(RWeatherReal.day.desc(), RWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询省的数据
                else:
                    if int(province_id) == 2:
                        provincial_city_id = 1  # 省会城市id
                        d = user_session.query(RWeatherReal).filter(
                            RWeatherReal.flag == '2',
                            RWeatherReal.city_id == provincial_city_id,
                            RWeatherReal.province_id == int(province_id)).order_by(RWeatherReal.day.desc(),
                                                                                   RWeatherReal.moment.desc()).first()

                    # 处理返回数据
                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

            # 山东省
            elif int(province_id) == 5:
                # 查询县的数据
                if county_id and city_id and province_id:
                    d = (user_session.query(ShanDongRWeatherReal).filter(
                        ShanDongRWeatherReal.flag == 3,
                        ShanDongRWeatherReal.province_id == int(province_id),
                        ShanDongRWeatherReal.city_id == int(city_id),
                        ShanDongRWeatherReal.county_id == int(county_id))
                         .order_by(ShanDongRWeatherReal.day.desc(), ShanDongRWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            "county": d.county.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询市的数据
                elif city_id and provinces_id and not county_id:
                    # elif not county_id and city_id:
                    d = (user_session.query(ShanDongRWeatherReal).filter(
                        ShanDongRWeatherReal.province_id == int(province_id),
                        ShanDongRWeatherReal.city_id == int(city_id),
                        ShanDongRWeatherReal.flag == '2').order_by(ShanDongRWeatherReal.day.desc(), ShanDongRWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询省的数据
                else:
                    if int(province_id) == 2:
                        provincial_city_id = 1  # 省会城市id
                        d = user_session.query(ShanDongRWeatherReal).filter(
                            ShanDongRWeatherReal.flag == '2',
                            ShanDongRWeatherReal.city_id == provincial_city_id,
                            ShanDongRWeatherReal.province_id == int(province_id)).order_by(ShanDongRWeatherReal.day.desc(),
                                                                                   ShanDongRWeatherReal.moment.desc()).first()

                    # 处理返回数据
                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

            # 内蒙古
            elif int(province_id) == 6:
                # 查询县的数据
                if county_id and city_id and province_id:
                    d = (user_session.query(NeiMengGuRWeatherReal).filter(
                        NeiMengGuRWeatherReal.flag == 3,
                        NeiMengGuRWeatherReal.province_id == int(province_id),
                        NeiMengGuRWeatherReal.city_id == int(city_id),
                        NeiMengGuRWeatherReal.county_id == int(county_id))
                         .order_by(NeiMengGuRWeatherReal.day.desc(), NeiMengGuRWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            "county": d.county.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询市的数据
                elif city_id and provinces_id and not county_id:
                    # elif not county_id and city_id:
                    d = (user_session.query(NeiMengGuRWeatherReal).filter(
                        NeiMengGuRWeatherReal.province_id == int(province_id),
                        NeiMengGuRWeatherReal.city_id == int(city_id),
                        NeiMengGuRWeatherReal.flag == '2').order_by(NeiMengGuRWeatherReal.day.desc(),
                                                                   NeiMengGuRWeatherReal.moment.desc()).first())

                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }

                # 查询省的数据
                else:
                    if int(province_id) == 2:
                        provincial_city_id = 1  # 省会城市id
                        d = user_session.query(NeiMengGuRWeatherReal).filter(
                            NeiMengGuRWeatherReal.flag == '2',
                            NeiMengGuRWeatherReal.city_id == provincial_city_id,
                            NeiMengGuRWeatherReal.province_id == int(province_id)).order_by(
                            NeiMengGuRWeatherReal.day.desc(),
                            NeiMengGuRWeatherReal.moment.desc()).first()

                    # 处理返回数据
                    if d:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        detail = {
                            "datetime": datetime_,
                            "moment": datetime_.split(" ")[1],
                            "longitude": float(d.longitude),
                            "latitude": float(d.latitude),
                            "province": d.province.name,
                            "city": d.city.name,
                            # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                            "tem": float(d.tem),
                            "feel_tem": float(d.feel_tem),
                            "weather_con": d.weather_con,
                            "wind_angle": d.wind_angle,
                            "wind": d.wind,
                            "wind_power": d.wind_power,
                            "wind_speed": float(d.wind_speed),
                            "hum": d.hum,
                            "rain_hour": d.rain_hour,
                            "rain_probab": d.rain_probab,
                            "air_press": d.air_press,
                            "see": float(d.see),
                            "cloud_cover": d.cloud_cover,
                            "dew_tem": float(d.dew_tem)
                        }


            temp_map_dict = {}
            for k, v in RealMeteorologicalSigns.items():
                temp_map_dict[k] = v

            data_ = {
                "detail": detail,
                "maps": temp_map_dict
            }

            # user_session.close()
            self.returnTypeSuc(data_)
        except Exception as e:
            print(traceback.print_exc())
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class RealElePriceRelation(BaseHandler):
    """
    量价散点图
    """""

    @staticmethod
    async def get_series_data(start_day, end_day, province_id, table_name, user_session):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        if dict_serie_id == 13:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.name == "总加",
                                                                 RSeriesDataShanxi.is_use == "1").all())
        else:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--",
            }
            temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            start_day = self.get_argument("start_day", default=(datetime.datetime.now() - datetime.timedelta(days=3)).strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=(datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d"))

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数不存在")

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            if start_day > datetime.datetime.now():
                return self.customError("开始日期不能大于当前日期")
            if end_day > datetime.datetime.now():
                return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")
            if end_day - start_day > datetime.timedelta(days=2):
                return self.customError("日期范围不能超过3天")

            days_obj = {}
            target_day = start_day
            while target_day <= end_day:
                days_obj[target_day.strftime("%Y-%m-%d")] = {"moments": [], "ele_prices": [], "els": []}
                target_day += datetime.timedelta(days=1)

            temp_array = []
            temp_map_dict = {
                "value1": "日前出清电量",
                "value2": "日前出清价格"
            }
            # for option in options:
            # 日前出清电量
            data1 = await self.get_series_data(start_day, end_day, province_id, "日前出清电量", user_session)
            if data1:
                temp_list_1 = []
                for i in range(len(data1)):
                    temp_dict = {
                        "datetime": data1[i]["datetime"],
                        "value1": data1[i]["value1"],
                    }
                    temp_list_1.append(temp_dict)
                temp_array.append(temp_list_1)

            # 日前出清价格
            # elif option == "日前出清价格":
            # temp_map_dict['value2'] = '日前出清价格'
            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息", user_session)
            if data2:
                temp_list_2 = []
                for i in range(len(data2)):
                    temp_dict = {
                        "datetime": data2[i]["datetime"],
                        "value2": data2[i]["value2"],
                    }
                    temp_list_2.append(temp_dict)
                temp_array.append(temp_list_2)

            result = merge_dicts(*temp_array)
            result = sorted(result, key=lambda x: x['datetime'])

            for i in result:
                for k in temp_map_dict.keys():
                    if k not in i.keys():
                        i[k] = '--'

            for k, v in days_obj.items():
                for j in result:
                    if j['datetime'][:10] == k:
                        v['moments'].append(j['datetime'])
                        v['els'].append(j['value1'])
                        v['ele_prices'].append(j['value2'])

            data = {"days": [], "array": []}
            for k, v in days_obj.items():
                data['days'].append(k)
                data['array'].append(v)

            self.returnTypeSuc(data)

        except Exception as e:
            logging.error(e)
            print(traceback.print_exc())
            self.requestError()
        finally:
            user_session.close()


class ProvinceCityLadingCap(BaseHandler):
    """
    指定省份各个市装机容量数据
    """""
    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            # city_id = self.get_argument("city_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            if not province_id:
                return self.customError("省份ID参数不能为空")

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数不存在")

            # # 校验参数city_id
            # if not city_id:
            #     return self.customError("城市/区ID参数不能为空")
            #
            # cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
            # cities_id = [city.id for city in cities if city.is_use == '1']
            # if int(city_id) not in cities_id:
            #     return self.customError("城市/区ID参数错误")

            cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()

            temp_array = []
            for city in cities:

                # 风电装机量、光伏装机量、储能装机量
                data = {"city": city.name, "id": city.id}
                data1 = user_session.query(ProvinceCountCap).filter(ProvinceCountCap.name == '风电',
                                                                    ProvinceCountCap.type == 1,
                                                                    ).order_by(ProvinceCountCap.day.desc()).first()
                if data1:
                    data['wind_pow_cap'] = [data1.value, data1.unit]
                else:
                    data['wind_pow_cap'] = ['--', '--']

                data2 = user_session.query(ProvinceCountCap).filter(ProvinceCountCap.name == '光伏',
                                                                    ProvinceCountCap.type == 1,
                                                                    ).order_by(ProvinceCountCap.day.desc()).first()
                if data2:
                    data['pv_pow_cap'] = [data2.value, data2.unit]
                else:
                    data['pv_pow_cap'] = ['--', '--']

                data3 = user_session.query(ProvinceCountCap).filter(ProvinceCountCap.name == '储能',
                                                                    ProvinceCountCap.type == 1,
                                                                    ).order_by(ProvinceCountCap.day.desc()).first()
                if data3:
                    data['energe_storage_cap'] = [data3.value]
                else:
                    data['energe_storage_cap'] = ['--']

                temp_array.append(data)

            self.returnTypeSuc(temp_array)

        except Exception as e:
            logging.error(e)
            print(traceback.print_exc())
            self.requestError()
        finally:
            user_session.close()


class PowerFlagAndChagDisg(BaseHandler):
    """
    查询电源结构和充放电量数据
    """""

    @staticmethod
    async def get_series_data(user_session, province_id, table_name):
        try:
            today_end_time = datetime.datetime.now().replace(hour=12, minute=0, second=0).strftime("%H:%M:%S")
            dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                                  CDictSery.province_id == province_id,
                                                                  CDictSery.is_use == "1", ).first()).id

            if dict_serie_id == 13:
                d = (user_session.query(RSeriesDataShanxi).filter(
                    RSeriesDataShanxi.day == datetime.datetime.now().strftime("%Y-%m-%d"),
                    RSeriesDataShanxi.moment <= today_end_time,
                    RSeriesDataShanxi.series_id == dict_serie_id,
                    RSeriesDataShanxi.name == "总加",
                    RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.moment.desc(),
                                                              RSeriesDataShanxi.op_ts.desc()).first())
            else:
                d = (user_session.query(RSeriesDataShanxi).filter(
                    RSeriesDataShanxi.day == datetime.datetime.now().strftime("%Y-%m-%d"),
                    RSeriesDataShanxi.moment <= today_end_time,
                    RSeriesDataShanxi.series_id == dict_serie_id,
                    RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.moment.desc(),
                                                              RSeriesDataShanxi.op_ts.desc()).first())

            if not d:
                if dict_serie_id == 13:
                    d = (user_session.query(RSeriesDataShanxi).filter(
                        RSeriesDataShanxi.day <= datetime.datetime.now().strftime("%Y-%m-%d"),
                        RSeriesDataShanxi.series_id == dict_serie_id,
                        RSeriesDataShanxi.name == "总加",
                        RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.day.desc(),
                                                                  RSeriesDataShanxi.moment.desc(),
                                                                  RSeriesDataShanxi.op_ts.desc()).first())

                else:
                    d = (user_session.query(RSeriesDataShanxi).filter(
                        RSeriesDataShanxi.day <= datetime.datetime.now().strftime("%Y-%m-%d"),
                        RSeriesDataShanxi.series_id == dict_serie_id,
                        RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.day.desc(),
                                                                  RSeriesDataShanxi.moment.desc(),
                                                                  RSeriesDataShanxi.op_ts.desc()).first())

            if d:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                    "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                    "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                    "name": d.name if d.name is not None else "--"
                }
            else:
                temp_dict = {
                    "datetime": '--',
                    "value1": '--',
                    "value2": '--',
                    "value3": '--',
                    "name":  "--"
                }

            # temp_list按照 datetime 字段分组
            # grouped_datas = defaultdict(list)
            # for i in temp_list:
            #     grouped_datas[i['datetime']].append(i)

            # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
            return temp_dict
        except Exception as e:
            logging.error(e)
            temp_dict = {
                "datetime": '--',
                "value1": '--',
                "value2": '--',
                "value3": '--',
                "name": "--"
            }
            return temp_dict
    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            # city_id = self.get_argument("city_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            if not province_id:
                return self.customError("省份ID参数不能为空")

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数不存在")

            province_id = int(province_id)

            return_data = {"power_struct": {}, "energy_statisc": {}}
            keys_mapping = {
                "cont_line_plan": "联络线计划信息",
                "new_energy_load_wind": "新能源负荷预测（风电）",
                "new_energy_load_pv": "新能源负荷预测（光伏）",
                "new_energy_load_wf": "新能源负荷预测（水电）",
                "day_ahead_non_market_units": "日前非市场化机组",
                "fire_gen_output": "火电机组出力"
                # "total_count": "加总"
            }
            return_data['keys_map'] = keys_mapping

            # te_list = []

            # 联络线计划信息
            data1 = await self.get_series_data(user_session, province_id=province_id, table_name="实时联络线计划（加总）")
            if data1:
                return_data['power_struct']['cont_line_plan'] = data1['value1']
                # te_list.append(data1['value1'] if data1['value1'] != '--' else 0)
            else:
                return_data['power_struct']['cont_line_plan'] = '--'

            # 新能源负荷预测（风电/光伏）
            data2 = await self.get_series_data(user_session, province_id=province_id, table_name="日前新能源负荷预测")
            if data2:
                return_data['power_struct']['new_energy_load_wind'] = data2['value3']
                return_data['power_struct']['new_energy_load_pv'] = data2['value2']
                # te_list.append(data2['value3'] if data2['value3'] != '--' else 0)
                # te_list.append(data2['value2'] if data2['value2'] != '--' else 0)
            else:
                return_data['power_struct']['new_energy_load_wind'] = '--'
                return_data['power_struct']['new_energy_load_pv'] = '--'

            # 新能源负荷预测（水电）
            data3 = await self.get_series_data(user_session, province_id=province_id, table_name="水电处理预测-日前预测")
            if data3:
                return_data['power_struct']['new_energy_load_wf'] = data3['value1']
                # te_list.append(data3['value1'] if data3['value1'] != '--' else 0)
            else:
                return_data['power_struct']['new_energy_load_wf'] = '--'

            # 日前非市场化机组
            data4 = await self.get_series_data(user_session, province_id=province_id, table_name="日前非市场化机组出力预测")
            if data4:
                return_data['power_struct']['day_ahead_non_market_units'] = data4['value1']
                # te_list.append(data4['value1'] if data4['value1'] != '--' else 0)
            else:
                return_data['power_struct']['day_ahead_non_market_units'] = '--'

            # 火电机组出力：火电机组出力 = 系统负荷实际值（系统实时负荷频率备用情况） - 水电总实时出力 - 实时非市场化机组出力曲线 - 实时联络线计划（加总） - 新能源总实时出力
            # 系统负荷实际值（系统实时负荷频率备用情况）
            data5 = await self.get_series_data(user_session, province_id=province_id, table_name="系统实时负荷频率备用情况")
            value_1 = data5['value1'] if data5['value1'] != '--' else 0

            # 水电总实时出力
            data6 = await self.get_series_data(user_session, province_id=province_id, table_name="水电总实时出力")
            value_2 = data6['value1'] if data6['value1'] != '--' else 0

            # 实时非市场化机组出力曲线
            data7 = await self.get_series_data(user_session, province_id=province_id, table_name="实时非市场化机组出力曲线")
            value_3 = data7['value1'] if data7['value1'] != '--' else 0

            # 新能源总实时出力
            data8 = await self.get_series_data(user_session, province_id=province_id, table_name="新能源总实时出力")
            value_4 = data8['value3'] if data8['value3'] != '--' else 0

            # 实时联络线计划（加总）
            value_5 = data1['value1'] if data1['value1'] != '--' else 0

            # 火电机组出力
            value_6 = round(value_1 - value_2 - value_3 - value_5 - value_4, 2)
            return_data['power_struct']['fire_gen_output'] = value_6

            # # 总数
            # return_data['power_struct']['total_count'] = round(value_6 + value_3 + value_5 + (data2['value3'] if
            #                                                                                   data2['value3'] != '--'
            #                                                                                   else 0) + (
            #                                                        data2['value2'] if data2['value2'] != '--' else 0) +
            #                                                    data3['value1'] if data3['value1'] != '--' else 0, 2)

            # 充放电统计: 返回大储大同&阳泉链各个站的累计充放电量，和效率值（放电量/充电量），百分比返回，所数据为0的校验
            sql = "select sum(chag) as v_chag,sum(disg) as v_disg from dws_pcs_measure_1d where station_cname in ('大同', '阳泉')"
            res = dws_es_station_db_tool.select_one(sql)
            if res:
                cu_chag = res['v_chag'] if res['v_chag'] else None
                cu_disg = res['v_disg'] if res['v_disg'] else None
                data = {
                    "cu_chag": cu_chag,
                    "cu_disg": cu_disg,
                    "cu_eff": round(cu_disg / cu_chag * 100, 2) if cu_chag and cu_disg else '--'
                }
            else:
                cu_chag = '--'
                cu_disg = '--'
                data = {
                    "cu_chag": cu_chag,
                    "cu_disg": cu_disg,
                    "cu_eff": '--'
                }

            return_data['energy_statisc'] = data

            self.returnTypeSuc(return_data)

        except Exception as e:
            logging.error(e)
            print(traceback.print_exc())
            self.requestError()

        finally:
            user_session.close()


class RealForecastPower(BaseHandler):
    """
    获取实时及预测需求量数据
    """""

    @staticmethod
    async def get_series_data(province_id, table_name, user_session):
        today_end_time = datetime.datetime.now().replace(hour=12, minute=0, second=0).strftime("%H:%M:%S")
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        # d = (user_session.query(RSeriesDataShanxi).filter(
        #     RSeriesDataShanxi.day == datetime.datetime.now().strftime("%Y-%m-%d"),
        #     RSeriesDataShanxi.moment <= today_end_time,
        #     RSeriesDataShanxi.series_id == dict_serie_id,
        #     RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.moment.desc()).first())
        # if not d:
        d = (user_session.query(RSeriesDataShanxi).filter(
            # RSeriesDataShanxi.day <= datetime.datetime.now().strftime("%Y-%m-%d"),
            RSeriesDataShanxi.series_id == dict_serie_id,
            RSeriesDataShanxi.value1 != None,
            RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.day.desc(), RSeriesDataShanxi.moment.desc()).first())

        if d:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--"
            }
        else:
            temp_dict = {
                "datetime": '--',
                "value1": '--',
                "value2": '--',
                "value3": '--',
                "name": "--"
            }

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def get_series_data_2(province_id, table_name, user_session):
        today_end_time = datetime.datetime.now().replace(hour=12, minute=0, second=0).strftime("%H:%M:%S")
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        d = (user_session.query(RSeriesDataShanxi).filter(
            RSeriesDataShanxi.day == datetime.datetime.now().strftime("%Y-%m-%d"),
            RSeriesDataShanxi.moment <= today_end_time,
            RSeriesDataShanxi.series_id == dict_serie_id,
            RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.moment.desc()).first())
        if not d:
            d = (user_session.query(RSeriesDataShanxi).filter(
                RSeriesDataShanxi.day <= datetime.datetime.now().strftime("%Y-%m-%d"),
                RSeriesDataShanxi.series_id == dict_serie_id,
                RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.day.desc(), RSeriesDataShanxi.moment.desc()).first())

        if d:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--"
            }
        else:
            temp_dict = {
                "datetime": '--',
                "value1": '--',
                "value2": '--',
                "value3": '--',
                "name": "--"
            }

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def get_range_series_data(start_day, end_day, province_id, table_name, user_session):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        data = (user_session.query(RSeriesDataShanxi).filter(
            RSeriesDataShanxi.series_id == dict_serie_id,
            RSeriesDataShanxi.day >= start_day,
            RSeriesDataShanxi.day <= end_day,
            RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--",
            }
            temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def get_max_series_data(start_day, end_day, province_id, table_name, user_session):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        data = (user_session.query(func.max(RSeriesDataShanxi.value1).label('max_value1')).filter(
            RSeriesDataShanxi.series_id == dict_serie_id,
            RSeriesDataShanxi.day >= start_day,
            RSeriesDataShanxi.day <= end_day,
            RSeriesDataShanxi.is_use == "1").first())

        if data[0] is None:
            return None
        d = (user_session.query(RSeriesDataShanxi).filter(
            RSeriesDataShanxi.series_id == dict_serie_id,
            RSeriesDataShanxi.day >= start_day,
            RSeriesDataShanxi.day <= end_day,
            RSeriesDataShanxi.is_use == "1",
            RSeriesDataShanxi.value1 == data[0]
            ).first())
        if d:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--",
            }
        else:
            temp_dict = {}

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        # print(735, temp_dict)
        return temp_dict

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            # city_id = self.get_argument("city_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            if not province_id:
                return self.customError("省份ID参数不能为空")

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数不存在")

            province_id = int(province_id)
            return_data = {}
            today = datetime.datetime.now().date()
            yesterday = today - datetime.timedelta(days=1)
            one_year_ago = today - datetime.timedelta(days=365)

            # 当前需求量：（2）系统负荷实际值(MW)
            data1 = await self.get_series_data(province_id, "系统实时负荷频率备用情况", user_session)
            if data1:
                return_data['demand_count'] = data1['value1']
            else:
                return_data['demand_count'] = '--'

            # 容量能力：当前省份所有的发电设施的装机量加总
            data2 = user_session.query(ProvinceCountCap).filter(ProvinceCountCap.name == '加总',
                                                                ProvinceCountCap.type == 1,
                                                                ).order_by(ProvinceCountCap.day.desc()).first()
            if data2:
                return_data['capacity'] = data2.value
                return_data['capacity_unit'] = data2.unit
            else:
                return_data['capacity'] = '--'
                return_data['capacity_unit'] = '--'

            # 今日需求峰值（日前）：max（今日“日前统调负荷”）
            data3 = await self.get_max_series_data(today, today, province_id, "全省用电负荷预测信息", user_session)
            if data3:
                return_data['today_demand_peak_p'] = data3['value1']
                return_data['today_demand_peak_p_moment'] = data3['datetime'][11:]
            else:
                return_data['today_demand_peak_p'] = '--'
                return_data['today_demand_peak_p_moment'] = '--'

            # 历史需求峰值：max（近一年的“系统负荷实际值(MW)”）
            # one_year_ago = today - datetime.timedelta(days=365)
            data4 = await self.get_max_series_data(one_year_ago, today, province_id, "系统实时负荷频率备用情况", user_session)
            if data4:
                return_data['history_demand_peak_p'] = data4['value1']
                return_data['his_demand_peak_datetime'] = data4['datetime']
            else:
                return_data['history_demand_peak_p'] = '--'
                return_data['his_demand_peak_datetime'] = '--'


            # 今日需求峰值（实时）：max（今天到当前时刻的“系统负荷实际值(MW)”）
            data5 = await self.get_max_series_data(today, today, province_id, "系统实时负荷频率备用情况", user_session)
            if data5:
                return_data['today_demand_peak'] = data5['value1']
                return_data['today_demand_peak_moment'] = data5['datetime'][11:]
            else:
                return_data['today_demand_peak'] = '--'
                return_data['today_demand_peak_moment'] = '--'

            # 明日需求峰值：max（明日“日前统调负荷”）
            # yesterday = today - datetime.timedelta(days=1)
            data6 = await self.get_max_series_data(yesterday, yesterday, province_id, "全省用电负荷预测信息", user_session)
            if data6:
                return_data['yesterday_demand_peak_p'] = data6['value1']
                return_data['yesterday_demand_peak_p_moment'] = data6['datetime'][11:]
            else:
                return_data['yesterday_demand_peak_p'] = '--'
                return_data['yesterday_demand_peak_p_moment'] = '--'

            self.returnTypeSuc(return_data)


        except Exception as e:
            logging.error(e)
            print(traceback.print_exc())
            self.requestError()

        finally:
            user_session.close()


all_options = {
    "price_trend": {
        "title": "电价趋势",
        "options": ["日前出清价格", "实时出清价格", "预测电价"],
    },
    "power_trend": {
        "title": "电源趋势",
        "options": ["日前新能源出力", "实时新能源出力", "水电计划", "水电实时", "机组竞价空间", "火电机组出力", "非市场化机组预测出力", "非市场化机组实际出力"],
    },
    "load_trend": {
        "title": "负荷趋势",
        "options": ["日前总出力", "实时总出力", "联络计划", "实时外送"]
    }
}


class MarketInfoOptionsV2(BaseHandler):
    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            date_type = self.get_argument("date_type", default="price_trend")

            # 校验参数date_type
            if date_type not in all_options.keys():
                return self.customError(f"参数date_type: {date_type}错误")

            temp_array = []

            item = all_options[date_type]
            temp_dict = {
                "key": date_type,
                "name": item['title'],
                "sub_items": item['options']
            }
            temp_array.append(temp_dict)

            self.returnTypeSuc(temp_dict)
        except Exception as e:
            logging.error(e)
            logging.error(traceback.print_exc())
            self.requestError()
        finally:
            user_session.close()


class MarketInfoV2(BaseHandler):
    """获取指定类型下指定指标数据: 电价趋势/电源趋势/负荷趋势."""""

    @staticmethod
    async def get_series_data(start_day, end_day, province_id, table_name, user_session):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        if dict_serie_id == 13:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.name == "总加",
                                                                 RSeriesDataShanxi.is_use == "1").all())
        else:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.is_use == "1").all())
        unic_list = []
        temp_list = []
        for d in data:
            if d.value1 is None and d.value2 is None and d.value3 is None:
                continue
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--",
            }
            if temp_dict['datetime'] not in unic_list:
                temp_list.append(temp_dict)
                unic_list.append(temp_dict['datetime'])

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

        return data

    @staticmethod
    async def get_series_data_v2(user_session, start_day, end_day, province_id, table_name):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id

        if dict_serie_id == 13:
            sql = f"select * from r_series_data_shanxi where day between '{start_day}' and '{end_day}' and series_id={dict_serie_id} and name='总加' and is_use='1'"
        else:
            sql = f"select * from r_series_data_shanxi where day between '{start_day}' and '{end_day}' and series_id={dict_serie_id} and is_use='1'"
        sql += ' ORDER BY op_ts desc'
        data = datatool.select_many(sql)

        unic_list = []
        temp_list = []
        for d in data:
            if d['value1'] is None and d['value2'] is None and d['value3'] is None:
                continue
            temp_dict = {
                "datetime": d['day'].strftime("%Y-%m-%d") + " " + d['moment'],
                "value1": round(float(d['value1']), 2) if d['value1'] is not None else '--',
                "value2": round(eval(d['value2']), 2) if d['value2'] is not None else '--',
                "value3": round(eval(d['value3']), 2) if d['value3'] is not None else '--',
                "name": d['name'] if d['name'] is not None else "--",
            }
            if temp_dict['datetime'] not in unic_list:
                temp_list.append(temp_dict)
                unic_list.append(temp_dict['datetime'])

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

        return data


    @staticmethod
    async def get_non_series_data(start_day, end_day, non_series_id, user_session):  # 非时序数据
        data = (user_session.query(RNonSeriesDataShanxi).filter(RNonSeriesDataShanxi.day >= start_day,
                                                                RNonSeriesDataShanxi.day <= end_day,
                                                                RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                                RNonSeriesDataShanxi.is_use == '1').all())
        unic_list = []
        temp_list = []
        if data:
            for d in data:
                if d.value1 is None and d.value2 is None and d.value3 is None and d.value4 is None:
                    continue
                if d.name:
                    data_ = (user_session.query(RNotSeriesNamesDataShanxi).filter(
                        RNotSeriesNamesDataShanxi.id == eval(d.name),
                        RNotSeriesNamesDataShanxi.is_use == '1').first())
                    temp_dict = {
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                            "%Y-%m-%d"),
                        "day": d.day,
                        "moment": d.moment if d.moment else '',
                        "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                        "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                        "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                        "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                        "descr": d.descr if d.descr else '--',
                        "name": data_.name if data_ else '--',
                        "index_type": d.index_type if d.index_type else '--',
                        "start_time": d.start_time if d.start_time else '--',
                        "end_time": d.end_time if d.end_time else '--'
                    }
                else:
                    temp_dict = {
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                            "%Y-%m-%d"),
                        "day": d.day,
                        "moment": d.moment,
                        "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                        "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                        "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                        "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                        "descr": d.descr if d.descr is not None else '--',
                        "name": d.name if d.name is not None else '--',
                        "index_type": d.index_type if d.index_type else '--',
                        "start_time": d.start_time if d.start_time else '--',
                        "end_time": d.end_time if d.end_time else '--'
                    }

                if temp_dict['datetime'] + temp_dict['name'] not in unic_list:
                    temp_list.append(temp_dict)
                    unic_list.append(temp_dict['datetime'] + temp_dict['name'])


        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id", default=None)
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            date_type = self.get_argument("date_type", default="price_trend")
            # sign = self.get_argument("sign", default=None)
            options = self.get_argument("options", default=None)
            models = self.get_argument("models", default=None)

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID缺失或不存在")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数格式错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数date_type
            if date_type not in all_options.keys():
                return self.customError(f"参数date_type: {date_type}错误")

            # 校验参数sign
            # if sign not in all_options[date_type].keys():
            #     return self.customError(f"参数sign:{sign}错误")

            # 校验参数options
            if options:
                options = options.split(",")
                for option in options:
                    if option not in all_options[date_type]['options']:
                        return self.customError(f"参数options: '{option}'错误")

                    # 校验参数：models
                    if '预测电价' == option:
                        if not models:
                            return self.customError("参数models不能为空")
                        models = models.split(",")
                        models = [int(i) for i in models]

                        session = self.getOrNewSession()
                        _id = session.user.get('id')

                        filter = [TModel.is_use == 1]
                        role_id = user_session.query(TUser).filter(TUser.id == _id).first().role_id
                        if role_id != 1:
                            model_users = user_session.query(ModelUser).filter(ModelUser.user_id == _id).all()
                            model_ids = [i.model_id for i in model_users]
                            filter.append(TModel.id.in_(model_ids))

                        res = user_session.query(TModel).filter(*filter).order_by(TModel.id.desc())
                        data = []

                        models_id = [i.id for i in res]

                        for model in models:
                            if model not in models_id:
                                return self.customError(f"参数models: '{model}'错误")

            else:
                options = all_options[date_type]['options']
                if date_type == "price_trend":
                    if not models:
                        return self.customError("参数models不能为空")
                    models = models.split(",")
                    models = [int(i) for i in models]

                    session = self.getOrNewSession()
                    _id = session.user.get('id')

                    filter = [TModel.is_use == 1]
                    role_id = user_session.query(TUser).filter(TUser.id == _id).first().role_id
                    if role_id != 1:
                        model_users = user_session.query(ModelUser).filter(ModelUser.user_id == _id).all()
                        model_ids = [i.model_id for i in model_users]
                        filter.append(TModel.id.in_(model_ids))

                    res = user_session.query(TModel).filter(*filter).order_by(TModel.id.desc())
                    data = []

                    models_id = [i.id for i in res]

                    for model in models:
                        if model not in models_id:
                            return self.customError(f"参数models: '{model}'错误")

            temp_array = []
            temp_map_dict = {}

            # 电价趋势
            if date_type == "price_trend":
                for option in options:
                    # 日前出清价格
                    if option == "日前出清价格":
                        temp_map_dict['value1'] = '日前出清价格'
                        data1 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "现货出清电价信息")
                        if data1:
                            temp_list_1 = []
                            for i in range(len(data1)):
                                temp_dict = {
                                    "datetime": data1[i]["datetime"],
                                    "value1": data1[i]["value1"],
                                }
                                temp_list_1.append(temp_dict)
                            temp_array.append(temp_list_1)

                    # 实时出清价格
                    elif option == '实时出清价格':
                        temp_map_dict['value2'] = '实时出清价格'
                        # data2 = await self.get_series_data_v2(start_day, end_day, province_id, "实时节点边际电价")

                        # 跟楚天确认的：用'现货出清电价信息'里的实时电价
                        data2 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "现货出清电价信息")
                        if data2:
                            temp_list_2 = []
                            for i in range(len(data2)):
                                temp_dict = {
                                    "datetime": data2[i]["datetime"],
                                    "value2": data2[i]["value2"],
                                }
                                temp_list_2.append(temp_dict)
                            temp_array.append(temp_list_2)

                    # 预测电价：跟随模型
                    elif option == '预测电价':
                        # temp_map_dict['value3'] = '预测电价'
                        num = 3
                        for model in models:
                            model_ins = user_session.query(TModel).filter(TModel.id == model).first()
                            temp_map_dict[f'value{num}'] = f'预测电价：{model_ins.name}'

                            temp_list_3 = []
                            target_day = start_day
                            while target_day <= end_day:
                                price_res = user_session.query(ModelFile).filter(ModelFile.model_id == model,
                                                                                 # ModelFile.type == 1,
                                                                                 ModelFile.op_ts == target_day,
                                                                                 ModelFile.is_use == 1).first()
                                if price_res:
                                    price_data = json.loads(price_res.price_data)
                                    for p in price_data:
                                        temp_dict = {
                                            "datetime": target_day.strftime("%Y-%m-%d") + " " + p.get('time'),
                                            f'value{num}': p.get('value')
                                        }
                                        temp_list_3.append(temp_dict)

                                target_day += datetime.timedelta(days=1)

                            temp_array.append(temp_list_3)
                            num += 1

            # 电源趋势
            elif date_type == "power_trend":
                for option in options:
                    if option == "日前新能源出力":
                        temp_map_dict['value1'] = '日前新能源出力'
                        data1 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "日前新能源负荷预测")
                        if data1:
                            temp_list = []
                            for i in range(len(data1)):
                                temp_dict = {
                                    "datetime": data1[i]["datetime"],
                                    "value1": data1[i]["value1"]
                                }
                                temp_list.append(temp_dict)
                            temp_array.append(temp_list)

                    elif option == "实时新能源出力":
                        temp_map_dict['value2'] = '实时新能源出力'
                        data2 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "新能源总实时出力")
                        if data2:
                            temp_list = []
                            for i in range(len(data2)):
                                temp_dict = {
                                    "datetime": data2[i]["datetime"],
                                    "value2": data2[i]["value3"]
                                }
                                temp_list.append(temp_dict)
                            temp_array.append(temp_list)

                    elif option == "水电计划":
                        temp_map_dict['value3'] = '水电计划'
                        data3 = await self.get_non_series_data(start_day, end_day, 16, user_session)
                        if data3:
                            temp_list = []
                            for i in range(len(data3)):
                                temp_dict = {
                                    "datetime": data3[i]["datetime"],
                                    # "value3": data3[i]["value1"],
                                    "value3": 0
                                }
                                temp_list.append(temp_dict)
                            temp_array.append(temp_list)

                    elif option == "水电实时":
                        temp_map_dict['value4'] = '水电实时'
                        data4 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "水电总实时出力")
                        if data4:
                            temp_list_4 = []
                            for i in range(len(data4)):
                                temp_dict = {
                                    "datetime": data4[i]["datetime"],
                                    "value4": data4[i]["value1"],
                                }
                                temp_list_4.append(temp_dict)
                            temp_array.append(temp_list_4)

                    elif option == "机组竞价空间":
                        temp_map_dict['value5'] = '机组竞价空间'
                        temp_map_dict_a = {}

                        temp_array_a = []
                        # 机组竞价空间 = 全省用电负荷预测信息 - 水电出力预测 - 日前联络计划（加总） - 日前非市场化机组出力预测 - 日前新能源负荷预测
                        # 全省用电负荷预测信息
                        temp_map_dict_a['value_a_1'] = '全省用电负荷预测信息'
                        data_a_1 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id,
                                                              table_name="全省用电负荷预测信息")
                        if data_a_1:
                            temp_list_a_1 = []
                            for i in range(len(data_a_1)):
                                temp_dict = {
                                    "datetime": data_a_1[i]["datetime"],
                                    "value_a_1": data_a_1[i]["value1"]
                                }
                                temp_list_a_1.append(temp_dict)
                            temp_array_a.append(temp_list_a_1)

                        # 水电出力预测
                        temp_map_dict_a['value_a_2'] = '水电出力预测'
                        data_a_2 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id, table_name="水电处理预测-日前预测")
                        if data_a_2:
                            temp_list_a_2 = []
                            for i in range(len(data_a_2)):
                                temp_dict = {
                                    "datetime": data_a_2[i]["datetime"],
                                    "value_a_2": data_a_2[i]["value1"]
                                }
                                temp_list_a_2.append(temp_dict)
                            temp_array_a.append(temp_list_a_2)

                        # 日前联络计划（加总）
                        temp_map_dict_a['value_a_3'] = '日前联络计划（加总）'
                        data_a_3 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id,
                                                              table_name="日前联络线计划信息（加总）")
                        if data_a_3:
                            temp_list_a_3 = []
                            for i in range(len(data_a_3)):
                                temp_dict = {
                                    "datetime": data_a_3[i]["datetime"],
                                    "value_a_3": data_a_3[i]["value1"]
                                }
                                temp_list_a_3.append(temp_dict)
                            temp_array_a.append(temp_list_a_3)

                        # 日前非市场化机组出力预测
                        temp_map_dict_a['value_a_4'] = '日前非市场化机组出力预测'
                        data_a_4 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id, table_name="日前非市场化机组出力预测")
                        if data_a_4:
                            temp_list_a_4 = []
                            for i in range(len(data_a_4)):
                                temp_dict = {
                                    "datetime": data_a_4[i]["datetime"],
                                    "value_a_4": data_a_4[i]["value1"]
                                }
                                temp_list_a_4.append(temp_dict)
                            temp_array_a.append(temp_list_a_4)

                        # 日前新能源负荷预测
                        temp_map_dict_a['value_a_5'] = '日前新能源负荷预测'
                        data_a_5 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id,
                                                              table_name="日前新能源负荷预测")
                        if data_a_5:
                            temp_list_5 = []
                            for i in range(len(data_a_5)):
                                temp_dict = {
                                    "datetime": data_a_5[i]["datetime"],
                                    "value_a_5": data_a_5[i]["value1"]
                                }
                                temp_list_5.append(temp_dict)
                            temp_array_a.append(temp_list_5)

                        # 机组竞价空间
                        result_a = merge_dicts(*temp_array_a)
                        result_a = sorted(result_a, key=lambda x: x['datetime'])

                        array_a = []
                        for i in result_a:
                            for k in temp_map_dict_a.keys():
                                if k not in i.keys():
                                    i[k] = 0

                            value_5 = round(
                                i['value_a_1'] - i['value_a_2'] - i['value_a_3'] - i['value_a_5'] - i['value_a_4'], 2)
                            array_a.append({"datetime": i['datetime'], "value5": value_5})

                        temp_array.append(array_a)

                    elif option == "火电机组出力":
                        temp_map_dict['value6'] = '火电机组出力'
                        temp_map_dict_b = {}

                        temp_array_b = []
                        # 火电机组出力：火电机组出力 = 系统负荷实际值（系统实时负荷频率备用情况） - 水电总实时出力 - 实时非市场化机组出力曲线 - 实时联络线计划（加总） - 新能源总实时出力
                        # 系统负荷实际值（系统实时负荷频率备用情况）
                        temp_map_dict_b['value_b_1'] = '系统负荷实际值'
                        data_b_1 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id,
                                                           table_name="系统实时负荷频率备用情况")
                        # value_1 = data_a['value1'] if data_a['value1'] != '--' else 0  # todo 取哪个value待确定
                        if data_b_1:
                            temp_list_b_1 = []
                            for i in range(len(data_b_1)):
                                temp_dict = {
                                    "datetime": data_b_1[i]["datetime"],
                                    "value_b_1": data_b_1[i]["value1"]
                                }
                                temp_list_b_1.append(temp_dict)
                            temp_array_b.append(temp_list_b_1)

                        # 水电总实时出力
                        temp_map_dict_b['value_b_2'] = '水电总实时出力'
                        data_b_2 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id, table_name="水电总实时出力")
                        # value_2 = data6['value1'] if data6['value1'] != '--' else 0
                        if data_b_2:
                            temp_list_b_2 = []
                            for i in range(len(data_b_2)):
                                temp_dict = {
                                    "datetime": data_b_2[i]["datetime"],
                                    "value_b_2": data_b_2[i]["value1"]
                                }
                                temp_list_b_2.append(temp_dict)
                            temp_array_b.append(temp_list_b_2)

                        # 实时非市场化机组出力曲线
                        temp_map_dict_b['value_b_3'] = '实时非市场化机组出力曲线'
                        data_b_3 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id,
                                                           table_name="实时非市场化机组出力曲线")
                        # value_3 = data7['value1'] if data7['value1'] != '--' else 0
                        if data_b_3:
                            temp_list_b_3 = []
                            for i in range(len(data_b_3)):
                                temp_dict = {
                                    "datetime": data_b_3[i]["datetime"],
                                    "value_b_3": data_b_3[i]["value1"]
                                }
                                temp_list_b_3.append(temp_dict)
                            temp_array_b.append(temp_list_b_3)

                        # 新能源总实时出力
                        temp_map_dict_b['value_b_4'] = '新能源总实时出力'
                        data_b_4 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id, table_name="新能源总实时出力")
                        # value_4 = data8['value1'] if data8['value1'] != '--' else 0
                        if data_b_4:
                            temp_list_b_4 = []
                            for i in range(len(data_b_4)):
                                temp_dict = {
                                    "datetime": data_b_4[i]["datetime"],
                                    "value_b_4": data_b_4[i]["value1"]
                                }
                                temp_list_b_4.append(temp_dict)
                            temp_array_b.append(temp_list_b_4)

                        # 实时联络线计划（加总）
                        temp_map_dict_b['value_b_5'] = '实时联络线计划（加总）'
                        data_b_5 = await self.get_series_data_v2(user_session, start_day, end_day, province_id=province_id, table_name="实时联络线计划（加总）")
                        if data_b_5:
                            temp_list_5 = []
                            for i in range(len(data_b_5)):
                                temp_dict = {
                                    "datetime": data_b_5[i]["datetime"],
                                    "value_b_5": data_b_5[i]["value1"]
                                }
                                temp_list_5.append(temp_dict)
                            temp_array_b.append(temp_list_5)

                        # 火电机组出力
                        # value_6 = round(value_1 - value_2 - value_3 - value_5 - value_4, 2)
                        result_b = merge_dicts(*temp_array_b)
                        result_b = sorted(result_b, key=lambda x: x['datetime'])

                        array_b = []
                        for i in result_b:
                            for k in temp_map_dict_b.keys():
                                if k not in i.keys():
                                    i[k] = 0
                            value_b_1 = i['value_b_1'] if i['value_b_1'] != '--' else 0
                            value_b_2 = i['value_b_2'] if i['value_b_2'] != '--' else 0
                            value_b_3 = i['value_b_3'] if i['value_b_3'] != '--' else 0
                            value_b_4 = i['value_b_4'] if i['value_b_4'] != '--' else 0
                            value_b_5 = i['value_b_5'] if i['value_b_5'] != '--' else 0
                            value_6 = round(value_b_1 - value_b_2 - value_b_3 - value_b_5 - value_b_4, 2)
                            array_b.append({"datetime": i['datetime'], "value6": value_6})

                        temp_array.append(array_b)

                    elif option == "非市场化机组预测出力":
                        temp_map_dict['value7'] = '非市场化机组预测出力'
                        data7 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "日前非市场化机组出力预测")
                        if data7:
                            temp_list_2 = []
                            for i in range(len(data7)):
                                temp_dict = {
                                    "datetime": data7[i]["datetime"],
                                    "value7": data7[i]["value1"],
                                }
                                temp_list_2.append(temp_dict)
                            temp_array.append(temp_list_2)

                    elif option == "非市场化机组实际出力":
                        temp_map_dict['value8'] = '非市场化机组实际出力'
                        data8 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "实时非市场化机组出力曲线")
                        if data8:
                            temp_list_2 = []
                            for i in range(len(data8)):
                                temp_dict = {
                                    "datetime": data8[i]["datetime"],
                                    "value8": data8[i]["value1"],
                                }
                                temp_list_2.append(temp_dict)
                            temp_array.append(temp_list_2)

            # 负荷趋势
            elif date_type == "load_trend":
                for option in options:
                    # 实时出清电量
                    if option == '日前总出力':
                        temp_map_dict['value1'] = '日前总出力'
                        data1 = await self.get_series_data_v2(user_session, start_day, end_day, province_id,
                                                           "全省用电负荷预测信息")
                        if data1:
                            temp_list_2 = []
                            for i in range(len(data1)):
                                temp_dict = {
                                    "datetime": data1[i]["datetime"],
                                    "value1": data1[i]["value1"],
                                }
                                temp_list_2.append(temp_dict)
                            temp_array.append(temp_list_2)

                    elif option == '实时总出力':
                        temp_map_dict['value2'] = '实时总出力'
                        data2 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "系统实时负荷频率备用情况")
                        if data2:
                            temp_list = []
                            for i in range(len(data2)):
                                temp_dict = {
                                    "datetime": data2[i]["datetime"],
                                    "value2": data2[i]["value1"]
                                }
                                temp_list.append(temp_dict)
                            temp_array.append(temp_list)

                    elif option == '联络计划':
                        temp_map_dict['value3'] = '联络计划'
                        data3 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "日前联络线计划信息（加总）")
                        if data3:
                            temp_list_6 = []
                            for i in range(len(data3)):
                                if data3[i]["name"] == '总加':
                                    temp_dict = {
                                        "datetime": data3[i]["datetime"],
                                        "value3": data3[i]["value1"],
                                    }
                                    temp_list_6.append(temp_dict)
                            temp_array.append(temp_list_6)

                    elif option == '实时外送':
                        temp_map_dict['value4'] = '实时外送'
                        data4 = await self.get_series_data_v2(user_session, start_day, end_day, province_id, "实时联络线计划（加总）")
                        if data4:
                            temp_list_6 = []
                            for i in range(len(data4)):
                                # if data6[i]["name"] == '总加':
                                temp_dict = {
                                    "datetime": data4[i]["datetime"],
                                    "value4": data4[i]["value1"],
                                }
                                temp_list_6.append(temp_dict)
                            temp_array.append(temp_list_6)

            result = merge_dicts(*temp_array)
            result = sorted(result, key=lambda x: x['datetime'])

            for i in result:
                for k in temp_map_dict.keys():
                    if k not in i.keys():
                        i[k] = '--'

            # user_session.close()
            data = {
                "value_maps": temp_map_dict,
                "detail": result
            }
            self.returnTypeSuc(data)
        except Exception as e:
            print(traceback.print_exc())
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()