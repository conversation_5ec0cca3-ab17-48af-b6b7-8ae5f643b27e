#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:16:38
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_discrete.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 14:18:48


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class DiscretePT(scada_Base):
    ''' 离散量配置表 '''
    __tablename__ = "t_discrete"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    device = Column(Integer, nullable=False, comment=u"所属设备")
    no = Column(Integer, nullable=False,comment=u"编号")
    descr = Column(String(256), nullable=False,comment=u"名称")
    desc_start = Column(Integer, nullable=False,comment=u"起始值")
    desc_num = Column(Integer, nullable=False,comment=u"数量")
    coef = Column(Float, nullable=False,comment=u"变化值")
    store_flag = Column(Integer, nullable=True,comment=u"")
    gen_timed_flag = Column(Integer, nullable=True,comment=u"")
    rpt_timed_flag = Column(Integer, nullable=True,comment=u"")
    comment = Column(Integer, nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','device':'%s','no':%s,'descr':'%s','desc_start':'%s','desc_num':'%s','coef':'%s','store_flag':'%s','gen_timed_flag':'%s','rpt_timed_flag':'%s','comment':'%s'}" % (
            self.id,self.name,self.device,self.no,self.descr,self.desc_start,self.desc_num,self.coef,self.store_flag,self.gen_timed_flag,self.rpt_timed_flag,self.comment)