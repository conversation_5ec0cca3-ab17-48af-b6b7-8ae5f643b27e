import re
import traceback

import pymysql
import json
import datetime
import logging
import numpy
from dbutils.persistent_db import PersistentDB

from TianLuAppBackend import settings
from apis.user import models
from common.database_pools import dwd_db_tool, dwd_tables, MT_KEYS, MU_kEYS, VERSION_KEYS, LOAD_KEYS, CUMU_LOAD_KEYS
from settings.meter_settings import METER_DIC

# 连接数据库
pool = PersistentDB(pymysql, 10,**{
            "host": settings.DATABASES['doris_origin_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_origin_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_origin_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_origin_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_origin_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


def time_range_by_dwd(station_name, point_type, device_type, device, start_time, end_time, *args):
    # 兼容单体电压和单体温度在单独的表dwd_measure_bms_data_storage_2和dwd_measure_bms_data_storage_3中查询
    if point_type == 'measure' and device_type == 'bms':
        pattern_mu = re.compile(r'^mu\d+')  # 单体电压
        pattern_mt = re.compile(r'^mt\d+')  # 单体温度

        table_name1 = dwd_tables[point_type]['bms_1']
        table_name2 = dwd_tables[point_type]['bms_2']
        table_name3 = dwd_tables[point_type]['bms_3']
        select_sql_1 = (
            f"SELECT * FROM {table_name1} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time ASC")
        select_sql_2 = (
            f"SELECT * FROM {table_name2} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time ASC")
        select_sql_3 = (
            f"SELECT * FROM {table_name3} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time ASC")

        try:
            temp_list = list()
            results_1 = dwd_db_tool.select_many(select_sql_1, *(station_name, device, start_time, end_time))
            results_2 = dwd_db_tool.select_many(select_sql_2, *(station_name, device, start_time, end_time))
            results_3 = dwd_db_tool.select_many(select_sql_3, *(station_name, device, start_time, end_time))
            # error_log.debug(results)
            if results_1 and results_2 and results_3:
                for i in range(0, len(results_1)):
                    time_ = results_3[i]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    temp_dict = {"time": time_}
                    for arg in args:
                        if pattern_mu.match(arg.lower()):
                            temp_dict[arg] = results_1[i].get(arg.lower(), 0)
                        elif pattern_mt.match(arg.lower()):
                            temp_dict[arg] = results_2[i].get(arg.lower(), 0)
                        else:
                            temp_dict[arg] = results_3[i].get(arg.lower(), 0)
                    temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None

    # 兼容控制策略在单独的表dwd_measure_ems_data_storage_tscale中查询
    elif point_type == 'measure' and device_type == 'ems':
        pattern_rlh = re.compile(r'^rlh\d+\w')  # 控制策略

        table_name1 = dwd_tables[point_type]['ems']
        table_name2 = dwd_tables[point_type]['ems_tscale']

        select_sql_1 = (
            f"SELECT * FROM {table_name1} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time ASC")
        select_sql_2 = (
            f"SELECT * FROM {table_name2} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time ASC")

        try:
            temp_list = list()
            results_1 = dwd_db_tool.select_many(select_sql_1, *(station_name, device, start_time, end_time))
            results_2 = dwd_db_tool.select_many(select_sql_2, *(station_name, device, start_time, end_time))
            # error_log.debug(results)
            if results_1 and results_2:
                for i in range(0, len(results_1)):
                    time_ = results_1[i]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    temp_dict = {"time": time_}
                    for arg in args:
                        if pattern_rlh.match(arg.lower()):
                            if re.match(r'^rlh\d+p', arg.lower()):
                                temp_dict[arg] = results_2[i].get(arg.lower()) * 100 if results_2[i].get(arg.lower(), 0) else '--'
                            else:
                                temp_dict[arg] = results_2[i].get(arg.lower(), 0)
                        else:
                            temp_dict[arg] = results_1[i].get(arg.lower(), 0)
                    temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None
    else:

        table_name = dwd_tables[point_type][device_type]

        select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                      f" time ASC")
        try:
            temp_list = list()
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # error_log.debug(results)
            if results:
                for result in results:
                    time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
                    temp_dict = {"time": time_}
                    for i in args:
                        temp_dict[i] = result.get(i.lower(), 0)
                    temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None


def time_range_by_dwd_for_web(station_name, point_type, device_type, device, start_time, end_time, *args):
    # 兼容单体电压和单体温度在单独的表dwd_measure_bms_data_storage_2和dwd_measure_bms_data_storage_3中查询
    if point_type == 'measure' and device_type == 'bms':

        table_name1 = dwd_tables[point_type]['bms_1']
        table_name2 = dwd_tables[point_type]['bms_2']
        table_name3 = dwd_tables[point_type]['bms_3']

        # select_sql = f"SELECT * FROM {table_name1} t1 JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device right JOIN {table_name3} t3 ON t1.station_name = t3.station_name and t1.time = t3.time and t1.device = t3.device WHERE t3.station_name=%s and t3.device=%s and t3.time between %s and %s ORDER BY t3.time ASC;"
        select_sql = f"""SELECT *
                        FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1 
                        JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2 
                        ON t1.station_name = t2.station_name 
                        and t1.time = t2.time 
                        and t1.device = t2.device 
                        right JOIN (select * from {table_name3} where station_name=%s and device=%s and time between %s and %s) t3 
                        ON t1.station_name = t3.station_name 
                        and t1.time = t3.time 
                        and t1.device = t3.device 
                        ORDER BY t3.time ASC ;"""

        try:
            temp_list = list()
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        if result['time'].minute % 5 != 0:
                            time_ = datetime.datetime.fromtimestamp(result['time'].replace(second=0, microsecond=0).timestamp() - result['time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        temp_dict = {"time": time_}
                        for i in args:
                            temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
                        temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None

    # 兼容控制策略在单独的表dwd_measure_ems_data_storage_tscale中查询; 新增符合字段在dwd_measure_load_data_storage 中查询
    elif point_type == 'measure' and device_type == 'ems':

        table_name1 = dwd_tables[point_type]['ems']
        table_name2 = dwd_tables[point_type]['ems_tscale']
        table_name3 = dwd_tables[point_type]['load']

        # select_sql = f"SELECT * FROM {table_name1} t1 left JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device left JOIN {table_name3} t3 ON t1.station_name = t3.station_name and t1.time = t3.time and t1.device = t3.device WHERE t1.station_name=%s and t1.device=%s and t1.time between %s and %s ORDER BY t1.time ASC;"
        select_sql = f"""SELECT *
                        FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1 
                        left JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2 
                        ON t1.station_name = t2.station_name 
                        and t1.time = t2.time 
                        and t1.device = t2.device 
                        left JOIN (select * from {table_name3} where station_name=%s and device=%s and time between %s and %s) t3 
                        ON t1.station_name = t3.station_name 
                        and t1.time = t3.time 
                        and t1.device = t3.device 
                        ORDER BY t3.time ASC ;"""

        try:
            temp_list = list()
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        if result['time'].minute % 5 != 0:
                            time_ = datetime.datetime.fromtimestamp(
                                result['time'].replace(second=0, microsecond=0).timestamp() - result[
                                    'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        temp_dict = {"time": time_}
                        for i in args:
                            temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
                        temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None

    elif point_type == 'cumulant' and device_type == 'ems':
        table_name1 = dwd_tables[point_type]['ems']
        table_name2 = dwd_tables[point_type]['load']
        # select_sql = f"SELECT * FROM {table_name1} t1 left JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device WHERE t1.station_name=%s and t1.device=%s and t1.time between %s and %s ORDER BY t1.time ASC;"
        select_sql = f"""SELECT *
                        FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1 
                        left JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2 
                        ON t1.station_name = t2.station_name 
                        and t1.time = t2.time 
                        and t1.device = t2.device 
                        ORDER BY t1.time ASC ;"""

        try:
            temp_list = list()
            results = dwd_db_tool.select_many(select_sql, *(
            station_name, device, start_time, end_time, station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        if result['time'].minute % 5 != 0:
                            time_ = datetime.datetime.fromtimestamp(
                                result['time'].replace(second=0, microsecond=0).timestamp() - result[
                                    'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        temp_dict = {"time": time_}
                        for i in args:
                            temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
                        temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(traceback.format_exc())
            error_log.error(e)
            return None

    else:

        table_name = dwd_tables[point_type][device_type]

        select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                      f" time ASC")

        try:
            temp_list = list()
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        if result['time'].minute % 5 != 0:
                            time_ = datetime.datetime.fromtimestamp(
                                result['time'].replace(second=0, microsecond=0).timestamp() - result[
                                    'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        temp_dict = {"time": time_}
                        for i in args:
                            temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
                        temp_list.append(temp_dict)
            return temp_list
        except Exception as e:
            error_log.error(e)
            return None


def time_range_by_dwd_for_web_v2(station_name, point_type, device_type, device, start_time, end_time, time_points, *args):
    lower_args = {i.lower(): i for i in args}
    # 兼容单体电压和单体温度在单独的表dwd_measure_bms_data_storage_2和dwd_measure_bms_data_storage_3中查询
    # 查询数据项中是否存在温度、电压、版本号信息
    mu_list = []
    mt_list = []
    load_list = []
    version_list = []
    cumu_load_list = []
    query_list = []
    for i in lower_args.keys():
        if i in MT_KEYS:  # 温度
            mt_list.append(i)
        elif i in MU_kEYS:  # 电压
            mu_list.append(i)
        elif i in VERSION_KEYS:  # 版本号
            version_list.append(i)
        elif i in LOAD_KEYS:
            load_list.append(i)
        elif i in CUMU_LOAD_KEYS:
            cumu_load_list.append(i)
        else:
            query_list.append(i)
    temp_list = {i: {} for i in time_points}
    if mu_list:
        table_name = dwd_tables[point_type]['bms_1']
        select_sql = f"""SELECT station_name, device, time,{','.join(mu_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in mu_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None
    if mt_list:
        table_name = dwd_tables[point_type]['bms_2']
        select_sql = f"""SELECT station_name, device, time,{','.join(mt_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in mt_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None
    if version_list:
        table_name = dwd_tables[point_type]['version']
        select_sql = f"""SELECT station_name, device, time,{','.join(version_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in version_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None
    if load_list:
        table_name = dwd_tables['measure']['load']
        select_sql = f"""SELECT station_name, device, time,{','.join(load_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in load_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None

    if cumu_load_list:
        table_name = dwd_tables['cumulant']['load']
        select_sql = f"""SELECT station_name, device, time,{','.join(cumu_load_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            # print(162, select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in cumu_load_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None

    if query_list:
        if device_type == 'bms' and point_type == 'measure':
            device_type = 'bms_3'
        table_name = dwd_tables[point_type][device_type]
        select_sql = f"""SELECT station_name, device, time, {','.join(query_list)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY time ASC"""
        try:
            results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
            if results:
                for result in results:
                    if result['time']:
                        # if result['time'].minute % 5 != 0:
                        #     time_ = datetime.datetime.fromtimestamp(
                        #         result['time'].replace(second=0, microsecond=0).timestamp() - result[
                        #             'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
                        # else:
                        time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                        for i in query_list:
                            if temp_list.get(time_) != None:
                                temp_list[time_][lower_args[i]] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
        except Exception as e:
            error_log.error(e)
            return None
    return temp_list
    # if point_type == 'measure' and device_type == 'bms':
    #
    #     table_name1 = dwd_tables[point_type]['bms_1']
    #     table_name2 = dwd_tables[point_type]['bms_2']
    #     table_name3 = dwd_tables[point_type]['bms_3']
    #     table_version = dwd_tables[point_type]['version']
    #
    #     # select_sql = f"SELECT * FROM {table_name1} t1 JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device right JOIN {table_name3} t3 ON t1.station_name = t3.station_name and t1.time = t3.time and t1.device = t3.device WHERE t3.station_name=%s and t3.device=%s and t3.time between %s and %s ORDER BY t3.time ASC;"
    #     select_sql = f"""SELECT t1.station_name, t1.device, t1.time,{','.join(lower_args)}
    #                     FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1
    #                     JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2
    #                     ON t1.station_name = t2.station_name
    #                     and t1.time = t2.time
    #                     and t1.device = t2.device
    #                     right JOIN (select * from {table_name3} where station_name=%s and device=%s and time between %s and %s) t3
    #                     ON t1.station_name = t3.station_name
    #                     and t1.time = t3.time
    #                     and t1.device = t3.device
    #                     right JOIN (select * from {table_version} where station_name=%s and device=%s and time between %s and %s) t4
    #                     on t1.station_name = t4.station_name
    #                     and t1.time = t4.time
    #                     and t1.device = t4.device
    #                     ORDER BY t3.time ASC ;"""
    #
    #     try:
    #         temp_list = dict()
    #         results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time))
    #         # print(162, select_sql, *(station_name, device, start_time, end_time))
    #         if results:
    #             for result in results:
    #                 if result['time']:
    #                     if result['time'].minute % 5 != 0:
    #                         time_ = datetime.datetime.fromtimestamp(result['time'].replace(second=0, microsecond=0).timestamp() - result['time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
    #                     else:
    #                         time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
    #                     temp_dict = {}
    #                     for i in args:
    #                         temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
    #                     temp_list[time_] = temp_dict
    #         return temp_list
    #     except Exception as e:
    #         print(163, traceback.format_exc())
    #         error_log.error(e)
    #         return None
    #
    # # 兼容控制策略在单独的表dwd_measure_ems_data_storage_tscale中查询; 新增符合字段在dwd_measure_load_data_storage 中查询
    # elif point_type == 'measure' and device_type == 'ems':
    #
    #     table_name1 = dwd_tables[point_type]['ems']
    #     table_name2 = dwd_tables[point_type]['ems_tscale']
    #     table_name3 = dwd_tables[point_type]['load']
    #     table_version = dwd_tables[point_type]['version']
    #
    #     if 'pcc' in lower_args:
    #         lower_args[lower_args.index('pcc')] = 't1.pcc'
    #
    #     # select_sql = f"SELECT * FROM {table_name1} t1 left JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device left JOIN {table_name3} t3 ON t1.station_name = t3.station_name and t1.time = t3.time and t1.device = t3.device WHERE t1.station_name=%s and t1.device=%s and t1.time between %s and %s ORDER BY t1.time ASC;"
    #     select_sql = f"""SELECT t1.station_name, t1.device, t1.time,{','.join(lower_args)}
    #                     FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1
    #                     left JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2
    #                     ON t1.station_name = t2.station_name
    #                     and t1.time = t2.time
    #                     and t1.device = t2.device
    #                     left JOIN (select * from {table_name3} where station_name=%s and device=%s and time between %s and %s) t3
    #                     ON t1.station_name = t3.station_name
    #                     and t1.time = t3.time
    #                     and t1.device = t3.device
    #                     left JOIN (select * from {table_version} where station_name=%s and device=%s and time between %s and %s) t4
    #                     ON t1.station_name = t4.station_name
    #                     and t1.time = t4.time
    #                     and t1.device = t4.device
    #                     ORDER BY t3.time ASC ;"""
    #
    #     try:
    #         temp_list = dict()
    #         results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time, station_name, device, start_time, end_time))
    #         # print(162, select_sql, *(station_name, device, start_time, end_time))
    #         if results:
    #             for result in results:
    #                 if result['time']:
    #                     if result['time'].minute % 5 != 0:
    #                         time_ = datetime.datetime.fromtimestamp(
    #                             result['time'].replace(second=0, microsecond=0).timestamp() - result[
    #                                 'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
    #                     else:
    #                         time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
    #                     temp_dict = {}
    #                     for i in args:
    #                         temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
    #                     temp_list[time_] = temp_dict
    #         return temp_list
    #     except Exception as e:
    #         error_log.error(e)
    #         return None
    #
    # elif point_type == 'cumulant' and device_type == 'ems':
    #     table_name1 = dwd_tables[point_type]['ems']
    #     table_name2 = dwd_tables[point_type]['load']
    #     # select_sql = f"SELECT * FROM {table_name1} t1 left JOIN {table_name2} t2 ON t1.station_name = t2.station_name and t1.time = t2.time and t1.device = t2.device WHERE t1.station_name=%s and t1.device=%s and t1.time between %s and %s ORDER BY t1.time ASC;"
    #     select_sql = f"""SELECT t1.station_name, t1.device, t1.time,{','.join(lower_args)}
    #                     FROM (select * from {table_name1} where station_name=%s and device=%s and time between %s and %s) t1
    #                     left JOIN (select * from {table_name2} where station_name=%s and device=%s and time between %s and %s) t2
    #                     ON t1.station_name = t2.station_name
    #                     and t1.time = t2.time
    #                     and t1.device = t2.device
    #                     ORDER BY t1.time ASC ;"""
    #
    #     try:
    #         temp_list = dict()
    #         results = dwd_db_tool.select_many(select_sql, *(
    #         station_name, device, start_time, end_time, station_name, device, start_time, end_time))
    #         # print(162, select_sql, *(station_name, device, start_time, end_time))
    #         if results:
    #             for result in results:
    #                 if result['time']:
    #                     if result['time'].minute % 5 != 0:
    #                         time_ = datetime.datetime.fromtimestamp(
    #                             result['time'].replace(second=0, microsecond=0).timestamp() - result[
    #                                 'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
    #                     else:
    #                         time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
    #                     temp_dict = {}
    #                     for i in args:
    #                         temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
    #                     temp_list[time_] = temp_dict
    #         return temp_list
    #     except Exception as e:
    #         error_log.error(traceback.format_exc())
    #         error_log.error(e)
    #         return None
    #
    # # 兼容pcs 版本号在dwd_measure_version_data中查询;
    # elif point_type == 'measure' and device_type == 'pcs':
    #
    #     table_name = dwd_tables[point_type][device_type]
    #     table_version = dwd_tables[point_type]['version']
    #
    #     select_sql = f"""SELECT t1.station_name, t1.device, t1.time,{','.join(lower_args)}
    #                         FROM (select * from {table_name} where station_name=%s and device=%s and time between %s and %s) t1
    #                         left JOIN (select * from {table_version} where station_name=%s and device=%s and time between %s and %s) t2
    #                         ON t1.station_name = t2.station_name
    #                         and t1.time = t2.time
    #                         and t1.device = t2.device
    #                         ORDER BY t1.time ASC ;"""
    #
    #     try:
    #         temp_list = dict()
    #         results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time, station_name, device, start_time, end_time))
    #         # print(162, select_sql, *(station_name, device, start_time, end_time))
    #         if results:
    #             for result in results:
    #                 if result['time']:
    #                     if result['time'].minute % 5 != 0:
    #                         time_ = datetime.datetime.fromtimestamp(
    #                             result['time'].replace(second=0, microsecond=0).timestamp() - result[
    #                                 'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
    #                     else:
    #                         time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
    #                     temp_dict = {}
    #                     for i in args:
    #                         temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
    #                     temp_list[time_] = temp_dict
    #         return temp_list
    #     except Exception as e:
    #         error_log.error(e)
    #         return None
    #
    # else:
    #
    #     table_name = dwd_tables[point_type][device_type]
    #
    #     select_sql = (f"SELECT station_name, device, time,{','.join(lower_args)} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
    #                   f" time ASC")
    #
    #     try:
    #         temp_list = dict()
    #         results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
    #         # print(162, select_sql, *(station_name, device, start_time, end_time))
    #         if results:
    #             for result in results:
    #                 if result['time']:
    #                     if result['time'].minute % 5 != 0:
    #                         time_ = datetime.datetime.fromtimestamp(
    #                             result['time'].replace(second=0, microsecond=0).timestamp() - result[
    #                                 'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M:%S")
    #                     else:
    #                         time_ = result['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
    #                     temp_dict = {}
    #                     for i in args:
    #                         temp_dict[i] = result.get(i.lower()) if result.get(i.lower()) is not None else '--'
    #                     temp_list[time_] = temp_dict
    #         return temp_list
    #     except Exception as e:
    #         error_log.error(e)
    #         return None


# def time_range_by_measure_soc(station_name, point_type, device_type, device, start_time, end_time, *args):
#     table_name = dwd_tables[point_type][device_type]
#
#     select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
#                   f" time ASC")
#     try:
#         temp_list = list()
#         results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
#         error_log.debug(results)
#         if results:
#             for result in results:
#                 time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
#                 temp_dict = {"time": time_}
#                 for i in args:
#                     temp_dict[i] = result.get(i.lower(), None)
#                 temp_list.append(temp_dict)
#         return temp_list
#     except Exception as e:
#         error_log.error(e)
#         return None


def time_range_by_measure_pu(station_name, point_type, device_type, device, start_time, end_time, *args):
    table_name = dwd_tables[point_type][device_type]

    select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    try:
        temp_list = list()
        results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
        error_log.debug(results)
        if results:
            for result in results:
                time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
                temp_dict = {"time": time_}
                if station_name == 'HZDC101':  # 千章公园单独处理
                    temp_dict['PUa'] = result.get('PUab'.lower(), None)
                    temp_dict['PUb'] = result.get('PUbc'.lower(), None)
                    temp_dict['PUc'] = result.get('PUca'.lower(), None)
                else:
                    temp_dict['PUa'] = result.get('PUa'.lower(), None)
                    temp_dict['PUb'] = result.get('PUb'.lower(), None)
                    temp_dict['PUc'] = result.get('PUc'.lower(), None)
                temp_list.append(temp_dict)
        return temp_list
    except Exception as e:
        error_log.error(e)
        return None

# def time_range_by_measure_bl(app_name, station_name, start_time, end_time, device):
#     # 三相电流
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT time ,get_json_string(data_info,"$.body.Ia") as Ia,get_json_string(data_info,"$.body.Ib") as Ib,get_json_string(data_info,"$.body.Ic") as Ic
#         FROM device_notify_measure_record WHERE 1=1 and app_name='{}' and station_name='{}' and type=1
#                     and time BETWEEN '{}' AND '{}'
#                     ORDER BY time ASC
#                     """.format(
#             app_name, station_name, start_time, end_time
#         )
#
#         cursor.execute(sql)
#         # 获取查询结果
#         result = cursor.fetchall()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return []
#
#         for row in result:
#             try:
#                 Ia = list(map(float, json.loads(row['Ia'])))
#                 Ib = list(map(float, json.loads(row['Ib'])))
#                 Ic = list(map(float, json.loads(row['Ic'])))
#                 body_result.append({"time": row['time'].strftime("%Y-%m-%d %H:%M:%S"), "Ia": round(numpy.sum(Ia), 2),
#                                     "Ib": round(numpy.sum(Ib), 2),
#                                     "Ic": round(numpy.sum(Ic), 2)})
#             except:
#                 pass
#         return body_result[::3]
#
#     except Exception as e:
#         error_log.error(e)
#         return []
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass


# def time_range_by_measure_cd(app_name, station_name, start_time, end_time, device):
#     # 当日预估可充可放
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT data_info FROM device_notify_measure_record
#                     WHERE 1=1
#                     and app_name='{}'
#                     and station_name='{}'
#                     and type=1
#                     and time BETWEEN '{}' AND '{}'
#                     ORDER BY time ASC
#                     """.format(
#             app_name, station_name, start_time, end_time
#         )
#         try:
#             cursor.execute(sql)
#         except Exception as e:
#             error_log.error(e)
#             return []
#
#         # 获取查询结果
#         result = cursor.fetchall()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return []
#         for row in result:
#             if row:
#                 data_info = json.loads(row['data_info'])
#                 body_list = data_info['body']
#                 for body in body_list:
#                     try:
#                         if "PCS" in body['device']:
#                             timestamp = int(data_info['time'])  # 用您的时间戳替换这个示例时间戳
#                             # 将时间戳转换为日期时间
#                             dt = datetime.datetime.fromtimestamp(timestamp)
#                             # 打印日期时间
#                             # print(dt)
#                             formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S")
#                             # print(formatted_date)
#                             body_result.append(
#                                 {"time": formatted_date, "BPCE": float(body["BPCE"]), "BPDE": float(body["BPDE"])})
#                     except:
#                         pass
#         return body_result[::3]
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass

def time_range_by_cumulant(station_name, point_type, device_type, device, start_time, end_time, *args):
    table_name = dwd_tables[point_type][device_type]

    select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    try:
        temp_list = list()
        results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
        error_log.debug(results)
        # print(202, results)
        if results:
            for result in results:
                time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
                temp_dict = {"time": time_}
                if station_name == 'NBLS001':  # 单独处理
                    temp_dict['PAE'] = result.get('CuCha'.lower()) if result.get('CuCha'.lower()) else '0'
                    temp_dict['NAE'] = result.get('CuDis'.lower()) if result.get('CuDis'.lower()) else '0'
                else:
                    temp_dict['PAE'] = result.get('PAE'.lower()) if result.get('PAE'.lower()) else '0'
                    temp_dict['NAE'] = result.get('NAE'.lower()) if result.get('NAE'.lower()) else '0'
                temp_list.append(temp_dict)
        return temp_list
    except Exception as e:
        error_log.error(e)
        return None


def time_range_by_measure_lc(station, start_time, end_time, electrical_meter_location, meter_device):
    point_type = 'measure'
    table_name_ems = dwd_tables[point_type]['ems']
    table_name_bms_3 = dwd_tables[point_type]['bms_3']
    table_name_pcs = dwd_tables[point_type]['pcs']

    select_sql_ems = (f"SELECT * FROM {table_name_ems} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    select_sql_bms_3 = (f"SELECT * FROM {table_name_bms_3} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    select_sql_pcs = (f"SELECT * FROM {table_name_pcs} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    try:
        temp_list = list()
        results_ems = dwd_db_tool.select_many(select_sql_ems, *(station.english_name, 'EMS', start_time, end_time))
        # error_log.debug(results)
        if results_ems:
            for index in range(len(results_ems)):
                # pcs_p_list = list()  # 天禄并网点PCS 直采功率集合
                # p_es_meter_list = list()
                # transformer_capacity = None  # 变压器容量
                # anti_reflux_threshold_value = None  # 变压器有功比例
                # safety_transformer_capacity = None  # 变压器安全容量
                # p_es_meter = None  # 天禄电表直采功率
                # p_load_meter = None  # 负载电表直采功率

                time_ = results_ems[index]['time'].strftime("%Y-%m-%d %H:%M:%S")

                transformer_capacity = float(results_ems[index].get("TFM".lower()) or 0)
                anti_reflux_threshold_value = float(results_ems[index].get("TPRT".lower()) or 0.9)
                p_load_meter = round(float(results_ems[index].get('PCC'.lower()) or 0), 2)
                safety_transformer_capacity = transformer_capacity * anti_reflux_threshold_value

                temp_list.append(
                    {"time": time_, "transformer_capacity": transformer_capacity,
                     "safety_transformer_capacity": round(safety_transformer_capacity, 2), "p_load_meter": p_load_meter})

        units = station.unit_set.filter(is_delete=0).all()

        pcs_p_array = list()  # 天禄并网点PCS 直采功率集合
        p_es_meter_array = list()
        for unit in units:
            pcs_p_list_unit = []
            p_es_meter_list_unit = []
            bms = unit.bms
            pcs = unit.pcs
            results_bms = dwd_db_tool.select_many(select_sql_bms_3,
                                                  *(station.english_name, bms, start_time, end_time))
            results_pcs = dwd_db_tool.select_many(select_sql_pcs, *(station.english_name, pcs, start_time, end_time))
            if results_bms:
                for index in range(len(results_bms)):
                    p_es_meter_list_unit.append(abs(float(results_bms[index]['BP'.lower()])) if results_bms[index]['BP'.lower()] else 0)

            if results_pcs:
                for index in range(len(results_pcs)):
                    pcs_p_list_unit.append(abs(float(results_pcs[index]['P'.lower()])) if results_pcs[index]['P'.lower()] else 0)

            p_es_meter_array.append(p_es_meter_list_unit)
            pcs_p_array.append(pcs_p_list_unit)

        pcs_p_list = [sum(x) for x in zip(*pcs_p_array)]
        p_es_meter_list = [sum(x) for x in zip(*p_es_meter_array)]

        for ind in range(len(temp_list)):
            # 电表前置
            if electrical_meter_location == 1:
                temp_list[ind]['p_load'] = temp_list[ind]['p_load_meter']
            # 电表后置
            else:
                # 现场充放电计量设备为电表时
                if meter_device == 'bms':
                    temp_list[ind]['p_load'] = round(temp_list[ind]['p_load_meter'] - abs(p_es_meter_list[ind]), 2)
                    temp_list[ind]['p_es_meter'] = p_es_meter_list[ind]
                # 现场充放电计量设备为PCS时
                else:
                    temp_list[ind]['p_load'] = round(temp_list[ind]['p_load_meter'] - abs(pcs_p_list[ind]), 2)
                    temp_list[ind]['pcs_p'] = pcs_p_list[ind]

        return temp_list
    except Exception as e:
        error_log.error(e)
        raise e


def master_station_time_range_by_measure_lc(master_station, start_time, end_time):
    slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
    if slave_stations.exists():
        for station in slave_stations:
            if station.slave == 0:
                table1 = "dwd_measure_ems_data_storage"
                sql1 = """SELECT tfm as TFM, tprt as TPRT, pcc as PCC, time FROM {}
                            WHERE 1=1
                            and device='{}'
                            and station_name='{}'
                            and type=1
                            and time BETWEEN '{}' AND '{}'
                            ORDER BY time ASC
                            """.format(
                    table1, 'EMS', station.english_name, start_time, end_time
                )
                result1 = dwd_db_tool.select_many(sql1)
                if result1:
                    ems_array = [
                        {"time": i['time'].strftime("%Y-%m-%d %H:%M:%S"), "transformer_capacity": float(i['TFM']), "anti_reflux_threshold_value": float(i['TPRT']),
                         "p_load_meter": float(i["PCC"]),
                         "safety_transformer_capacity": round((float(i['TFM']) * float(i['TPRT'])), 2), "p_load": 0} for i in
                        result1]

            else:
                device = METER_DIC.get(station.meter_type).get('device').upper()
                units = station.unit_set.filter(is_delete=0).all()
                if units.exists():
                    p_es_meter_list = []
                    pcs_p_list = []
                    for ind, unit in enumerate(units):
                        table2 = "dwd_measure_bms_data_storage_3"
                        sql2 = """SELECT bp as BP, time FROM {}
                                    WHERE 1=1
                                    and device='{}'
                                    and station_name='{}'
                                    and type=1
                                    and time BETWEEN '{}' AND '{}'
                                    ORDER BY time ASC
                                    """.format(
                            table2, unit.bms, station.english_name, start_time, end_time
                        )
                        result2 = dwd_db_tool.select_many(sql2)

                        table3 = "dwd_measure_pcs_data_storage"
                        sql3 = """SELECT p as P, time FROM {}
                                    WHERE 1=1
                                    and device='{}'
                                    and station_name='{}'
                                    and type=1
                                    and time BETWEEN '{}' AND '{}'
                                    ORDER BY time ASC
                                    """.format(
                            table3, unit.pcs, station.english_name, start_time, end_time
                        )
                        result3 = dwd_db_tool.select_many(sql3)

                        if result2 and result3:
                            if ind == 0:
                                for i in range(0, len(result2)):
                                    p_es_meter_list.append(float(result2[i]['BP']))
                                    # 单台PCS直采功率
                                    pcs_p_list.append(float(result3[i]['P']))
                            else:
                                for i in range(0, len(result2)):
                                    p_es_meter_list[i] += float(result2[i]['BP'])
                                    pcs_p_list[i] += float(result3[i]['P'])

                meter_position = station.meter_position
                meter_position = 1 if meter_position is None else meter_position

                temp_array = []
                for index, item in enumerate(ems_array):
                    # 电表前置
                    if meter_position == 1:
                        p_load = item['p_load_meter']
                        item['p_load'] = p_load
                    # 电表后置
                    else:
                        # 现场充放电计量设备为电表时
                        if device == 'bms':
                            p_load = round(item['p_load_meter'] - abs(p_es_meter_list[index]), 2)
                        # 现场充放电计量设备为PCS时
                        else:
                            p_load = round(item['p_load_meter'] - abs(pcs_p_list[index]), 2)
                        item['p_load'] += p_load

        for index, item_ in enumerate(ems_array):
            item_['transformer_capacity'] = item_['transformer_capacity']
            item_['safety_transformer_capacity'] = item_['safety_transformer_capacity']
            del item_['anti_reflux_threshold_value']

        return ems_array
    else:
        return []


# def time_range_by_measure_pcc(app_name, station_name, start_time, end_time):
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT data_info FROM device_notify_measure_record
#                     WHERE 1=1
#                     and app_name='{}'
#                     and station_name='{}'
#                     and type=1
#                     and time BETWEEN '{}' AND '{}'
#                     ORDER BY time ASC
#                     """.format(
#             app_name, station_name, start_time, end_time
#         )
#         try:
#             cursor.execute(sql)
#         except Exception as e:
#             error_log.error(e)
#             return []
#
#         # 获取查询结果
#         result = cursor.fetchall()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return []
#
#         temp_list = list()
#         for row in result:
#             try:
#                 if row:
#                     data_info = json.loads(row['data_info'])
#
#                     timestamp = int(data_info['time'])  # 用您的时间戳替换这个示例时间戳
#                     # 将时间戳转换为日期时间
#                     dt = datetime.datetime.fromtimestamp(timestamp)
#                     # 打印日期时间
#                     # print(dt)
#                     formatted_date = dt.strftime("%Y-%m-%d %H:%M")
#
#                     body_list = data_info['body']
#
#                     pcc = 0
#                     for body in body_list:
#                         try:
#                             if body['device'] == "EMS":
#                                 pcc = round(float(body['PCC']),1)
#                                 break
#                         except:
#                             pass
#
#                     body_result.append(
#                         {"time": formatted_date, "pcc": pcc,
#                          })
#             except:
#                 pass
#         return body_result
#
#     except Exception as e:
#         error_log.error(e)
#         return []
#
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass


# def get_history_data(table_name, app_name, station_name, start_time, end_time):
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT time, get_json_string(data_info,"$.body") as body
#                     FROM device_notify_{}_record
#                     WHERE 1=1
#                     and app_name='{}'
#                     and station_name='{}'
#                     and type=1
#                     and time BETWEEN '{}' AND '{}'
#                     ORDER BY time ASC
#                     """.format(
#             table_name,app_name, station_name, start_time, end_time
#         )
#         error_log.debug(sql)
#         try:
#             cursor.execute(sql)
#         except Exception as e:
#             error_log.error(e)
#             return []
#
#         # 获取查询结果
#         result = cursor.fetchall()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return []
#
#         for row in result:
#             if row:
#                 body_result.append(row)
#
#         return body_result
#
#     except Exception as e:
#         error_log.error(e)
#         return []
#
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass

# def del_role_data(role_id):
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """DELETE FROM t_role_permissions WHERE role_id = '{0}' """.format(role_id)
#         error_log.debug(sql)
#
#     except Exception as e:
#         error_log.error(e)
#         return []
#
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass


# def select_least_by_measure_m(app_name, station_name):
#     # 查询最新的一条数据
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT data_info FROM device_notify_measure_record
#                     WHERE 1=1
#                     and app_name='{}'
#                     and station_name='{}'
#                     and type=1
#                     order by utime DESC LIMIT 1
#                     """.format(
#             app_name, station_name
#         )
#         try:
#             cursor.execute(sql)
#         except Exception as e:
#             error_log.error(e)
#             return []
#
#         # 获取查询结果
#         result = cursor.fetchone()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return None
#         return json.loads(result['data_info'])['body']
#
#     except Exception as e:
#         error_log.error("查询测量量数据失败：{}".format(e))
#         return None
#
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass


# def select_least_by_status_m(app_name, station_name):
#     # 查询最新的一条数据
#     conn = pool.connection()
#     cursor = conn.cursor()
#     try:
#         # 执行SQL查询
#         sql = """SELECT data_info FROM device_notify_status_record
#                     WHERE 1=1
#                     and app_name='{}'
#                     and station_name='{}'
#                     and type=1
#                     order by utime DESC LIMIT 1
#                     """.format(
#             app_name, station_name
#         )
#         try:
#             cursor.execute(sql)
#         except Exception as e:
#             error_log.error(e)
#             return []
#
#         # 获取查询结果
#         result = cursor.fetchone()
#         body_result = []
#         # 处理查询结果
#         if not result:
#             return None
#         return json.loads(result['data_info'])['body']
#
#     except Exception as e:
#         error_log.error("查询状态量数据失败：{}".format(e))
#         return None
#
#     finally:
#         try:
#             cursor.close()
#             conn.close()
#         except Exception as e:
#             pass


# if __name__ == '__main__':
    # app_name = 'TN001'
    # station_name = 'NBLS001'
    # start_time = '2023-09-01'
    # end_time = '2023-09-07'
    # device = 'PCS'
    # a = time_range_by_cumulant(app_name, station_name, start_time, end_time, device)
    # print(a)
