#!/usr/bin/env python
# coding=utf-8
#@Information:用户侧现货交易价格查询
#<AUTHOR> WYJ
#@Date         : 2023-01-09 09:00:39
#@FilePath     : \RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseMarketHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-31 14:47:34


import os
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_market_price import ForecaseMarketPrice
import requests
import logging,json
from sqlalchemy import func
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
class SideForecaseMarketHandleIntetface(BaseHandler):

    # @tornado.web.authenticated
    def get(self,kt):
        try:
            if kt == 'GetMarketPrices':  # 查询价格  部制度暂时按默认两部制
                # province_id = self.get_argument('province_id',None) # 省份id
                startTime = self.get_argument('startTime',None) # 开始时间
                endTime = self.get_argument('endTime',None) # 截止时间
                value_type = self.get_argument('value_type',None) # 数据类型1电价，2电量
                
                if DEBUG:
                    logging.info('startTime:%s,endTime:%s,value_type:%s'%(startTime,endTime,value_type))
                data,filte = [],[ForecaseMarketPrice.province_id==5]
                # if province_id:
                #     filte.append(ForecaseMarketPrice.province_id==province_id)
                if startTime:
                    filte.append(ForecaseMarketPrice.day>=startTime)
                if endTime:
                    filte.append(ForecaseMarketPrice.day<=endTime)
                if value_type:
                    filte.append(ForecaseMarketPrice.value_type==value_type)
               
                total = user_session.query(func.count(ForecaseMarketPrice.day)).filter(*filte).scalar()
                pages = user_session.query(ForecaseMarketPrice).filter(*filte).order_by(ForecaseMarketPrice.day.asc()).order_by(ForecaseMarketPrice.moment.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTotalSuccess(data,total)
            elif kt == 'WXRealMessage':  # 获取时政消息
                return self.returnTypeSuc(self._get_data())
            elif kt == 'WXORealMessage':  # 另一个微信获取时政消息
                return self.returnTypeSuc(self._get_data_other())
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    # @tornado.web.authenticated
    def post(self,kt):
        pass
        # self.refreshSession()
        # try:
            
            
        # except Exception as E:
        #     user_session.rollback()
        #     logging.error(E)
        #     return self.requestError()
        # finally:
        #     user_session.close()
        # user_session.close()
    

    def _get_token(self):
        '''获取token'''
        appid='wx2469d431f3aaf4d6'
        secret='fa43219ef769aa7ad08ed0acb730d74a'
        obj = {'grant_type':'client_credential','appid':appid,'secret':secret}
        headers = {'content-type': "application/json"}
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://api.weixin.qq.com/cgi-bin/token'
        else:
            url = 'http://172.17.5.187:50000/wxsz/'
        reponse = requests.get(url=url,params=obj,headers=headers)
        return reponse.json()['access_token']
    
    def _get_data(self):
        '''获取数据'''  
        token= self._get_token()

        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url_ = 'https://api.weixin.qq.com/cgi-bin/draft/batchget'
        else:
            url_ = 'http://172.17.5.187:50000/wxsz2/'
        url = '%s?access_token=%s'%(url_,token)
        header = {'content-type': "application/x-www-form-urlencoded"}
        reponse = requests.post(url=url,data='{"no_content":"1","offset":0,"count":5}',headers=header)
        reponse.encoding = 'utf-8'
        da = reponse.json()['item']
        return da
    
    def _get_other_token(self):
        '''获取token'''
        appid='wxb6b2a3776e040672'
        secret='412a6fc9fd103b5e5facc7dcb8769033'
        obj = {'grant_type':'client_credential','appid':appid,'secret':secret}
        headers = {'content-type': "application/json"}
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://api.weixin.qq.com/cgi-bin/token'
        else:
            url = 'http://172.17.5.187:50000/wxsz/'
        reponse = requests.get(url=url,params=obj,headers=headers)
        return reponse.json()['access_token']
    
    def _get_data_other(self):
        '''获取数据'''  
        token= self._get_other_token()
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url_ = 'https://api.weixin.qq.com/cgi-bin/draft/batchget'
        else:
            url_ = 'http://172.17.5.187:50000/wxsz2/'
        url = '%s?access_token=%s'%(url_,token)
        header = {'content-type': "application/x-www-form-urlencoded"}
        reponse = requests.post(url=url,data='{"no_content":"1","offset":0,"count":5}',headers=header)
        reponse.encoding = 'utf-8'
        da = reponse.json()['item']
        return da