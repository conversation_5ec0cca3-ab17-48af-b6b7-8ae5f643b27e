#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-16 17:34:50
#@FilePath     : \RHBESS_Service\Application\Models\User\authority_new.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-17 15:58:43


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.station import Station
class AuthorityNew(user_Base):
    u'权限静态配置表-新表'
    __tablename__ = "t_authority_new"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    label = Column(String(256), nullable=False, comment=u"名称")
    en_label = Column(String(256), nullable=False, comment=u"名称-英文")
    index = Column(String(256), nullable=False,comment=u"连接")
    authority = Column(<PERSON>olean, nullable=False,comment=u"是否有权限")
    tags = Column(Text, nullable=True,comment=u"一个集合")
    en_tags = Column(Text, nullable=True,comment=u"一个集合-英文")
    icon = Column(String(256), nullable=True,comment=u"图标")
    parent_id = Column(String(256), nullable=True,comment=u"父节点id,用index")
    disabled = Column(Boolean, nullable=True, comment=u"是否禁用")
    sort_order = Column(Integer, nullable=True, server_default='1', comment=u"菜单排序 升序")
   

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

        
    def __repr__(self):
        return "{'label':'%s','index':'%s','authority':%s,'icon':'%s','parent_id':'%s','disabled':%s,'en_tags':%s,'en_label':%s}" % (
            self.label,self.index,self.authority,self.icon,self.parent_id,self.disabled,self.en_tags,self.en_label)