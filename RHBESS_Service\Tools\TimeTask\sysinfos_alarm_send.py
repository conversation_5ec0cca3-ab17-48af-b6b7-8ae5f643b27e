#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:26:06
#@FilePath     : \emot_pjt_rh\RHBESS_Service\Tools\TimeTask\sysinfos_alarm_send.py
#@Email        : <EMAIL>
#@LastEditTime : 2025-03-26 13:08:27

'''
获取最新告警信息推送给指定人员
'''
import sys,os
from sqlalchemy.sql import or_,and_
from Tools.Utils.num_utils import num_retain
from Tools.Utils.time_utils import timeUtils 
from Tools.DB.mysql_user import user_session
from Tools.Utils.send_mail import sendMail_
from Application.Models.User.event import Event
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.for_zhil_coupleback import ForCouplebackR
# reload(sys)
# sys.setdefaultencoding("utf-8")

basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)

from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()

halun_send_to = ["<EMAIL>","<EMAIL>","<EMAIL>"]
taicang_send_to = ["<EMAIL>","<EMAIL>","<EMAIL>"]
binhai_send_to = ["<EMAIL>","<EMAIL>","<EMAIL>"]
ygzhen_send_to = ["<EMAIL>","<EMAIL>"]
baodian_send_to = ["<EMAIL>","<EMAIL>","<EMAIL>"]
dongmu_send_to = ["<EMAIL>"]
station_obj = {"halun":halun_send_to,"taicang":taicang_send_to,"binhai":binhai_send_to,"ygzhen":ygzhen_send_to,"baodian":baodian_send_to,"dongmu":dongmu_send_to} 
station_name = {"halun":"哈伦电站","taicang":"太仓电站","binhai":"滨海电站","ygzhen":"永臻电站","baodian":"广州保电","dongmu":"东睦电站"}


def RunClearFileAndData():
    scheduler.add_job(calculation, 'interval', minutes=5)  # 5分钟执行一次
    # scheduler.add_job(fault, 'interval', seconds=60*15)  # 每 15分钟执行一次

    # scheduler.add_job(run_database, 'cron',  hour=1)  # 每天1点执行
    scheduler.start()


def calculation():
    '''
    每5分钟查询一下告警记录
    '''
    try:
        tim_n = timeUtils.nowSecs()
        start = timeUtils.ssTtimes(tim_n-300)
        end = timeUtils.ssTtimes(tim_n)
        for station in station_obj.keys():
            msg,bms_yujing = '',''
            alarms = user_session.query(Event.descr,AlarmR.ts,AlarmR.id).filter(Event.type_id == 5,AlarmR.event_id==Event.id,Event.station==station,AlarmR.ts.between(start,end),
            or_(AlarmR.value_descr==u'故障',AlarmR.value_descr==u'告警',AlarmR.value_descr==u'报警',AlarmR.value_descr==u'异常') ).all()
            for al in alarms:
                msg = msg+"告警部位：%s,告警时间：%s \n"%(al[0],str(al[1]))
                # 更新已发送状态
                user_session.query(AlarmR).filter(AlarmR.id == al[2]).update({"send":1})
            if msg:
                title = "%s告警通知"%station_name[station]
                sendMail_(msg,title,"RHBESS","XXX",station_obj[station])
                # print msg,title,"RHBESS","XXX",station_obj[station]
            # 查询预警功能
            zhil_coupleback = user_session.query(ForCouplebackR).filter(ForCouplebackR.station==station,ForCouplebackR.op_ts.between(start,end)).all()
            for zh in zhil_coupleback:
                # if station == 'taicang':
                bams = zh.pcs.split('-')
                bam = (bams[0]-1)*2+bams[1]
                bms_yujing = bms_yujing+"告警部位：第%s堆 第%s簇,告警时间：%s \n"%(bam,zh.bank,zh.start_time)
                
                zh.send = 1  # 更新已推送状态

            if bms_yujing:
                title = "%s电池故障预判告警通知"%station_name[station]
                sendMail_(bms_yujing,title,"RHBESS","XXX",station_obj[station])


        user_session.commit()
    except Exception as E:
        print (E)
    finally:
        user_session.close()
  


if __name__ == '__main__':
    
    RunClearFileAndData()
    # calculation()
   
    



