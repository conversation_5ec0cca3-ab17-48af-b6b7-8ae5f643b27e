import logging
import math
import datetime
import base64
import tornado.web
from sqlalchemy import func, or_

from Application.Models.ElePriceDescision.models import <PERSON><PERSON><PERSON>, TUser, TRoleAuthority, TAuthority, TEvent, TUsersRole
from Application.Models.base_handler_ele import BaseHandler
from Tools.DB.redis_decision import r
from Tools.DecisionDB.ele_base import get_user_session
# from Tools.DecisionDB.ele_base import user_session
from Tools.Utils.num_utils import judge_phone, computeMD5, password_is_valid, judge_email
from Tools.Utils.time_utils import timeUtils
from sqlalchemy.orm import joinedload
from Application.EqAccount.encryption.jwt_encryption import create_token
class UserManageIntetface(BaseHandler):
    ''' 用户（组织、权限）管理 '''

    @tornado.web.authenticated
    def get(self, kt):
        user_session = get_user_session()
        self.refreshSession()
        try:
            if kt == 'RoleList':
                pageNum = int(self.get_argument('pageNum', default='1'))
                pageSize = int(self.get_argument('pageSize', default='10'))
                name = self.get_argument('name', None)
                province_id = self.get_argument('province_id', None)
                data = []
                filter = [TRole.is_use == 1]
                if name:
                    filter.append(TRole.name.like('%' + name + '%'))
                if province_id:
                    filter.append(TRole.province_id == int(province_id))
                role_res = user_session.query(TRole).filter(*filter).order_by(
                    TRole.op_ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize)
                for role in role_res:
                    data.append({
                        'id': role.id,
                        'name': role.name,
                        'remark': role.remark,
                        'province': role.province.name
                    })
                total = user_session.query(func.count(TRole.id)).filter(*filter).scalar()
                return self.returnTotalSuc(data, total)

            elif kt == 'UserList':
                pageNum = int(self.get_argument('pageNum', default='1'))
                pageSize = int(self.get_argument('pageSize', default='10'))
                name = self.get_argument('name', None)
                role_ids = self.get_argument('role_ids', default='[]')
                # 查询启用的用户
                filters = [TUser.is_use == '1']
                if name:
                    filters.append(TUser.name.like('%' + name + '%'))
                if role_ids and eval(role_ids):
                    role_ids = eval(role_ids)
                    filters.append(TUser.role_id.in_(role_ids))

                total = user_session.query(func.count(TUser.id)).filter(*filters).scalar()
                users = user_session.query(TUser).options(joinedload(TUser.user_roles)).filter(*filters).order_by(
                    TUser.op_ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize)

                data = []
                for u in users:
                    u = u.__dict__
                    # u['role_id'] = [role.role_id for role in u['user_roles'] if role.is_use == 1]
                    del u['user_roles']
                    del u['_sa_instance_state']
                    del u['passwd']
                    del u['op_ts']
                    data.append(u)
                return self.returnTotalSuc(data, total)
            elif kt == 'RoleDetail':
                """角色详情"""
                _id = self.get_argument('id', default=None)
                if not _id:
                    return self.customError('角色ID错误！')
                role_info = user_session.query(TRole).filter(TRole.id==int(_id)).first()
                permession_res = user_session.query(TRoleAuthority).filter(TRoleAuthority.role_id==int(_id)).all()
                data = {
                    'id': role_info.id,
                    'name': role_info.name,
                    'remark': role_info.remark,
                    'authority_list': [i.authority_id for i in permession_res if i.is_use == 1],
                }
                return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()



    @tornado.web.authenticated
    def post(self, kt):
        user_session = get_user_session()
        self.refreshSession()
        try:
            if kt == 'RoleAdd':
                """新增角色"""
                name = self.get_argument('name', None)
                authority_list = self.get_argument('authority_list', '[1]')
                remark = self.get_argument('remark', None)
                if not name:
                    return self.customError('角色名不允许为空！')
                if not authority_list:
                    return self.customError('权限为空！')
                if user_session.query(TRole).filter(TRole.name == name, TRole.is_use == 1).count() > 0:
                    return self.customError('角色名已存在！')
                authority_list = authority_list.split(",")
                if '1' not in authority_list:
                    authority_list.insert(0, '1')
                province_id = self.get_argument('province_id', '2')
                role = TRole(name=name, province_id=int(province_id), is_use='1', remark=remark, op_ts=datetime.datetime.now())
                user_session.add(role)
                user_session.commit()

                role_id = user_session.query(TRole).filter(TRole.name == name, TRole.is_use == 1).first().id
                # 保存
                data_to_insert = [
                    {
                        "role_id": role_id,
                        "authority_id": int(item)
                    }
                    for item in authority_list
                ]
                # 执行批量插入
                user_session.bulk_insert_mappings(TRoleAuthority, data_to_insert)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'RoleUpdate':
                """修改角色"""
                id = self.get_argument('id')
                name = self.get_argument('name')
                if not name or not id:
                    return self.customError('参数不完整！')
                province_id = self.get_argument('province_id', None)
                remark = self.get_argument('remark', None)
                authority_list = self.get_argument('authority_list', None)
                role = user_session.query(TRole).get(int(id))
                if role.name != name:
                    if user_session.query(TRole).filter(TRole.name == name, TRole.is_use == 1).count() > 0:
                        return self.customError('角色名已存在！')
                role.name = name
                if province_id and province_id is not None:
                    role.province_id = province_id
                if remark is not None:
                    role.remark = remark
                if authority_list:
                    authority_list = list(map(int, authority_list.split(',')))
                    if len(authority_list) == 0:
                        return self.customError('权限为空！')
                    authority_id_now = user_session.query(TRoleAuthority).filter(
                        TRoleAuthority.role_id == int(id), TRoleAuthority.is_use == 1).all()
                    authority_id_now_list = [item.authority_id for item in authority_id_now]
                    if set(authority_id_now_list) != set(authority_list):
                        set1 = set(authority_id_now_list)
                        set2 = set(authority_list)
                        # 获取各自独有元素
                        unique_in_list1 = list(set1.difference(set2))  # 将这个集合的逻辑删除
                        unique_in_list2 = list(set2.difference(set1))  # 将这个集合的做增加操作

                        # 逻辑删除
                        if len(unique_in_list1) > 0:
                            user_session.query(TRoleAuthority).filter(
                                TRoleAuthority.role_id == int(id),
                                TRoleAuthority.authority_id.in_(unique_in_list1)).update(
                                {TRoleAuthority.is_use: 0}, synchronize_session=False)

                        if len(unique_in_list2) > 0:
                            # 保存
                            data_to_insert = [
                                {
                                    "role_id": int(id),
                                    "authority_id": item
                                }
                                for item in unique_in_list2
                            ]
                            # 执行批量插入
                            user_session.bulk_insert_mappings(TRoleAuthority, data_to_insert)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'RoleDelete':
                """删除角色"""
                id = self.get_argument('id', None)
                if not id:
                    return self.customError('ID为空！')
                user_session.query(TRole).filter(TRole.id == int(id)).update({TRole.is_use: 0})
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'UserAdd':
                """添加用户"""
                name = self.get_argument('name', None)
                phone_no = self.get_argument('phone_no', None)
                account = self.get_argument('account', None)
                email = self.get_argument('email', None)
                passwd = self.get_argument('passwd', 'RHYc@2024')
                role_id = self.get_argument('role_id', None)
                sex = self.get_argument('sex', default='0')
                province_id = self.get_argument('province_id', default='2')

                if not password_is_valid(passwd):
                    return self.customError("密码无效，必须包括大小写字母，数字和特殊字符长度大于8")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效")
                if not name:
                    return self.customError("名称为空")
                if not account:
                    return self.customError("登录名为空")
                if not role_id:
                    return self.customError("角色为空")
                if email:
                    if not judge_email(email):
                        return self.customError("邮箱无效!!")
                role_ids = role_id.split(',')
                if len(role_ids) == 0:
                    return self.customError("角色为空")
                user_name = user_session.query(TUser).filter(TUser.account == account).first()
                if user_name:
                    if user_name.is_use == '1':
                        return self.customError("用户名已存在")
                    else:
                        return self.customError("用户已停用，启用请联系管理员")
                phone = user_session.query(TUser).filter(TUser.phone_no == phone_no).first()
                if phone:
                    if phone.is_use == '1':
                        return self.customError("手机号已存在")
                    else:
                        return self.customError("当前手机号绑定用户已停用，启用请联系管理员")
                for ch in account:
                    if u'\u4e00' <= ch <= u'\u9fa5':
                        return self.customError("登录名不允许存在中文")
                passwd = computeMD5(passwd)

                user = TUser(name=name, sex=sex, account=account, passwd=passwd, op_ts=datetime.datetime.now(),
                            phone_no=phone_no, email=email, province_id=province_id, is_use=1, role_id=int(role_id))

                user_session.add(user)
                user_session.flush()
                user_id = user.id
                # 保存
                data_to_insert = [
                    {
                        "user_id": user_id,
                        "role_id": item
                    }
                    for item in role_ids
                ]
                # 执行批量插入
                user_session.bulk_insert_mappings(TUsersRole, data_to_insert)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'Modify':
                """编辑用户"""
                u_id = self.get_argument('id')
                name = self.get_argument('name', None)
                phone_no = self.get_argument('phone_no', None)
                account = self.get_argument('account', None)
                email = self.get_argument('email', None)
                role_id = self.get_argument('role_id', None)
                sex = self.get_argument('sex', default='0')
                province_id = self.get_argument('province_id', default='2')

                if not u_id:
                    return self.customError("ID无效")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效")
                if not name:
                    return self.customError("名称为空")
                if not account:
                    return self.customError("登录名为空")
                if not role_id:
                    return self.customError("角色为空")
                if email:
                    if not judge_email(email):
                        return self.customError("邮箱无效!")
                user = user_session.query(TUser).get(u_id)
                if not user:
                    return self.customError("用户ID不生效")

                if user_session.query(TUser).filter(TUser.id != u_id, TUser.account == account).first():
                    return self.customError("用户名已存在")
                if user_session.query(TUser).filter(TUser.id != u_id, TUser.phone_no == phone_no).first():
                    return self.customError("手机号已存在")
                for ch in account:
                    if u'\u4e00' <= ch <= u'\u9fa5':
                        return self.customError("登录名不允许存在中文")
                role_ids = list(map(int, role_id.split(',')))
                if len(role_ids) == 0:
                    return self.customError("角色为空")

                user.name = name
                user.account = account
                user.phone_no = phone_no
                user.role_id = role_id
                user.sex = sex
                user.email = email
                user.province_id = province_id

                role_id_now = user_session.query(TUsersRole).filter(
                    TUsersRole.user_id == u_id, TUsersRole.is_use == 1).all()
                role_id_now_list = [item.role_id for item in role_id_now]
                if set(role_id_now_list) != set(role_ids):
                    set1 = set(role_id_now_list)
                    set2 = set(role_ids)
                    # 获取各自独有元素
                    unique_in_list1 = list(set1.difference(set2))  # 将这个集合的逻辑删除
                    unique_in_list2 = list(set2.difference(set1))  # 将这个集合的做增加操作

                    # 逻辑删除
                    if len(unique_in_list1) > 0:
                        user_session.query(TUsersRole).filter(
                            TUsersRole.user_id == int(u_id),
                            TUsersRole.role_id.in_(unique_in_list1)).update(
                            {TUsersRole.is_use: 0}, synchronize_session=False)

                    if len(unique_in_list2) > 0:
                        # 保存
                        data_to_insert = [
                            {
                                "user_id": u_id,
                                "role_id": item
                            }
                            for item in unique_in_list2
                        ]
                        # 执行批量插入
                        user_session.bulk_insert_mappings(TUsersRole, data_to_insert)

                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'UserDelete':
                """删除用户"""
                id = self.get_argument('id')
                if not id:
                    return self.customError('ID为空！')
                user_session.query(TUser).filter(TUser.id == int(id)).update({TUser.is_use: 0})
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'UpdatePwd':
                """修改密码"""
                id = self.get_argument('id')
                passwd = self.get_argument('passwd')
                if not id:
                    return self.customError('ID为空！')
                user = user_session.query(TUser).get(id)
                if not user:
                    return self.customError('ID无效！')
                if not password_is_valid(passwd):
                    return self.customError("密码无效，必须包括大小写字母，数字和特殊字符长度大于8")
                passwd = computeMD5(passwd)  # md5加密
                if passwd == user.passwd:  # 修改后和原密码一样
                    return self.customError("新旧密码一致")

                user.passwd = passwd
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'Reset':
                """初始化密码"""
                id = self.get_argument('id')
                if not id:
                    return self.customError('ID为空！')
                user = user_session.query(TUser).get(id)
                if not user:
                    return self.customError('ID无效！')
                passwd = computeMD5('RHYc@2024')  # md5加密
                user.passwd = passwd
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'UpdateInfo':
                """修改个人基础信息"""
                id = self.get_argument('id')
                phone_no = self.get_argument('phone_no')
                email = self.get_argument('email')
                if not id:
                    return self.customError('ID为空！')
                user = user_session.query(TUser).get(id)
                if not user:
                    return self.customError('ID无效！')

                if user_session.query(TUser).filter(TUser.id != id, TUser.phone_no == phone_no).first():
                    return self.customError("手机号已存在")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效")
                if email:
                    if not judge_email(email):
                        return self.customError("邮箱无效")
                user.phone_no = phone_no
                user.email = email
                user_session.commit()
                return self.returnTypeSuc('')

            # elif kt == 'Login':  # 登录
            #     """登录"""
            #     try:
            #         Session = self.getOrNewSession()
            #         user = Session.user

            #         if user:
            #             logging.info("user is in session")
            #             return self.userExist("用户已登录", user)
            #         account = self.get_argument("account", None)  # 登录名
            #         passwd = self.get_argument("passwd", None)  # 密码
            #         if not account or not passwd:
            #             return self.customError("用户名或密码为空")
            #         passwd = computeMD5(passwd)
            #         user = user_session.query(TUser).filter(or_(TUser.account == account, TUser.phone_no == account),
            #                                                 TUser.is_use == 1).first()
            #         if not user:
            #             Session.remove()
            #             return self.customError("用户名或密码错误")
            #         else:
            #             retry = r.get(str(user.id) + self.request.remote_ip)
            #             if retry and int(retry) > 3:
            #                 Session.remove()
            #                 disable = r.ttl(str(user.id) + self.request.remote_ip)
            #                 disable_minute = 0
            #                 if disable:
            #                     disable_minute = int(math.ceil(disable / 60.0))
            #                 return self.customError("账户已锁定请" + str(disable_minute) + "分钟再试")
            #             if user.passwd != passwd:
            #                 r.incr(str(user.id) + self.request.remote_ip, )
            #                 r.expire(str(user.id) + self.request.remote_ip, 600)
            #                 retry = r.get(str(user.id) + self.request.remote_ip)
            #                 Session.remove()
            #                 if retry and int(retry) == 3:
            #                     return self.customError("还有一次机会账户被锁定10分钟")
            #                 elif retry and int(retry) >= 3:
            #                     return self.customError("账户已锁定")
            #                 return self.customError("用户名或密码错误")

            #         r.delete(str(user.id) + self.request.remote_ip)
            #         u = user.__dict__
            #         del u['_sa_instance_state']

            #         user = u

            #         user['authority_list'] = []
            #         authority_ids = user_session.query(TRoleAuthority.authority_id).filter(
            #             TRoleAuthority.role_id == user.get('role_id')).all()
            #         authority_ids = [i[0] for i in authority_ids]
            #         authority_res = user_session.query(TAuthority).filter(TAuthority.id.in_(authority_ids),
            #                                                             TAuthority.is_use == '1',
            #                                                             TAuthority.flag == '1',
            #                                                             TAuthority.province_id == user.get(
            #                                                                 'province_id')).all()

            #         for info in authority_res:
            #             user['authority_list'].append(
            #                 {
            #                     'id': info.id,
            #                     'title': info.title,
            #                     'url': info.url,
            #                     'parent_id': info.parent_id,
            #                 }
            #             )

            #         event = TEvent(op_ts=timeUtils.getNewTimeStr(), event='登录', user_id=user.get('id'), IP=self.request.remote_ip)
            #         user_session.add(event)
            #         user_session.commit()
            #         Session.user = user
            #         self.updateSession(Session)
            #         return self.returnTypeSuc(user)
            #     except Exception as E:
            #         logging.error(E)
            #         user_session.rollback()
            #         return self.requestError()

            elif kt == 'LogOut':
                """登出"""
                session = self.getOrNewSession()
                if session.user:
                    event = TEvent(op_ts=timeUtils.getNewTimeStr(), event='登出', user_id=session.user.get('id'), IP=self.request.remote_ip)
                    user_session.add(event)
                    user_session.commit()
                    session.user = None
                    self.updateSession(session)
                return self.returnTypeSuc('')

            elif kt == 'permissionsList':
                """角色权限列表"""
                id = self.get_argument("id")  # 角色ID
                ty = self.get_argument("type", '1')  # 1：WEB, 0：小程序
                province_id = self.get_argument('province_id', '2')
                data = []
                if id:
                    permission_ids = user_session.query(TRoleAuthority.authority_id).filter(
                        TRoleAuthority.role_id == int(id), TRoleAuthority.is_use == 1).all()
                else:
                    permission_ids = user_session.query(TRoleAuthority.authority_id).filter(
                        TRoleAuthority.is_use == 1).all()
                permission_ids = [i[0] for i in permission_ids]
                filters = [TAuthority.is_use == '1']
                if ty is not None:
                    filters.append(TAuthority.flag == ty)
                if province_id is not None:
                    filters.append(TAuthority.province_id == int(province_id))
                permission_res = user_session.query(TAuthority).filter(*filters).order_by(TAuthority.parent_id.asc(), TAuthority.id.asc()).all()
                if id:
                    for p in permission_res:
                        data.append(
                            {
                                'url': p.url,
                                'id': p.id,
                                'province_id': p.province_id,
                                'flag': p.flag,
                                'label': p.title,
                                'parent_id': p.parent_id,
                                'icon': p.icon,
                                'is_btn': p.is_btn,
                                'enable': True if p.id in permission_ids else False
                            }
                    )
                else:
                    for p in permission_res:
                        data.append(
                            {
                                'url': p.url,
                                'id': p.id,
                                'province_id': p.province_id,
                                'flag': p.flag,
                                'label': p.title,
                                'icon': p.icon,
                                'is_btn': p.is_btn,
                                'parent_id': p.parent_id
                            }
                        )

                data = generate_tree(data, None)
                # for p in permission_res:
                #     p = p.__dict__
                #     del p['_sa_instance_state']
                #     if p.get('id') in permission_ids:
                #         p['enable'] = True
                #     else:
                #         p['enable'] = False
                #     data.append(p)
                return self.returnTypeSuc(data)

            elif kt == 'permissionUpdate':
                """修改用户角色权限"""
                id = self.get_argument("id")  # 角色ID
                permission_ids = self.get_argument("permission_ids", "[]")  # 角色ID
                if not id or not permission_ids:
                    return self.customError("参数错误！")
                try:
                    user_session.query(TRoleAuthority).filter(TRoleAuthority.role_id == int(id)).delete()

                    permission_ids = eval(permission_ids)
                    data = []
                    for p_id in permission_ids:
                        role_authority = TRoleAuthority(role_id=int(id), authority_id=p_id)
                        data.append(role_authority)

                    user_session.add_all(data)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    user_session.rollback()
                    return self.requestError()
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class UserLoginHandler(BaseHandler):

    def post(self,kt):
        user_session = get_user_session()
        try:
            if kt == 'Login':  # 登录
                """登录"""
                if self.request.headers.get('Authorization'):
                    Session = self.getOrNewSession()
                    user = Session.user
                    if user:
                        logging.info("user is in session")
                        return self.userExist("用户已登录", user)
                account = self.get_argument("account", None)  # 登录名
                passwd = self.get_argument("passwd", None)  # 密码
                if not account or not passwd:
                    return self.customError("用户名或密码为空")
                passwd = computeMD5(passwd)
                user = user_session.query(TUser).filter(or_(TUser.account == account, TUser.phone_no == account),
                                                        TUser.is_use == 1).first()
                if not user:
                    # Session.remove()
                    return self.customError("用户名或密码错误")
                else:
                    retry = r.get(str(user.id) + self.request.remote_ip)
                    if retry and int(retry) > 3:
                        # Session.remove()
                        disable = r.ttl(str(user.id) + self.request.remote_ip)
                        disable_minute = 0
                        if disable:
                            disable_minute = int(math.ceil(disable / 60.0))
                        return self.customError("账户已锁定请" + str(disable_minute) + "分钟再试")
                    if user.passwd != passwd:
                        r.incr(str(user.id) + self.request.remote_ip, )
                        r.expire(str(user.id) + self.request.remote_ip, 600)
                        retry = r.get(str(user.id) + self.request.remote_ip)
                        # Session.remove()
                        if retry and int(retry) == 3:
                            return self.customError("还有一次机会账户被锁定10分钟")
                        elif retry and int(retry) >= 3:
                            return self.customError("账户已锁定")
                        return self.customError("用户名或密码错误")

                r.delete(str(user.id) + self.request.remote_ip)
                u = user.__dict__
                del u['_sa_instance_state']

                user = u

                user['authority_list'] = []
                if user.get('op_ts'):
                    user['op_ts'] = user.get('op_ts').strftime("%Y-%m-%d %H:%M:%S")
                authority_ids = user_session.query(TRoleAuthority.authority_id).filter(
                    TRoleAuthority.role_id == user.get('role_id')).all()
                authority_ids = [i[0] for i in authority_ids]
                authority_res = user_session.query(TAuthority).filter(TAuthority.id.in_(authority_ids),
                                                                        TAuthority.is_use == '1',
                                                                        TAuthority.flag == '1',
                                                                        TAuthority.province_id == user.get(
                                                                            'province_id'),
                                                                      TAuthority.parent_id==None).all()

                for info in authority_res:
                    child_list = []
                    _authority_res = user_session.query(TAuthority).filter(TAuthority.id.in_(authority_ids),
                                                                        TAuthority.is_use == '1',
                                                                        TAuthority.flag == '1',
                                                                        TAuthority.province_id == user.get(
                                                                            'province_id'),
                                                                      TAuthority.parent_id==info.id).all()
                    if _authority_res:
                        for y in _authority_res:
                            child_list.append({
                                'id': y.id,
                                'title': y.title,
                                'url': y.url,
                                'parent_id': y.parent_id,
                            })
                    user['authority_list'].append(
                        {
                            'id': info.id,
                            'title': info.title,
                            'url': info.url,
                            'parent_id': info.parent_id,
                            'child_list': child_list
                        }
                    )


                event = TEvent(op_ts=timeUtils.getNewTimeStr(), event='登录', user_id=user.get('id'), IP=self.request.remote_ip)
                user_session.add(event)
                user_session.commit()
                success_token = create_token({"user_id": user.get('id')})
                if isinstance(success_token, bytes):
                    success_token = base64.b64encode(success_token).decode('utf-8')
                Session = self.getOrNewSession(success_token)
                Session.user = user
                user['success_token'] = success_token
                self.updateSession(Session, success_token)
                return self.returnTypeSuc(user)

            elif kt == 'LogOut':
                """登出"""
                session = self.getOrNewSession()
                if session.user:
                    event = TEvent(op_ts=timeUtils.getNewTimeStr(), event='登出', user_id=session.user.get('id'), IP=self.request.remote_ip)
                    user_session.add(event)
                    user_session.commit()
                    session.user = None
                    self.updateSession(session)
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

def generate_tree(data, parent):
    tree = []
    for item in data:
        if item["parent_id"] == parent:
            item["children"] = generate_tree(data, item["id"])
            tree.append(item)
    return tree