#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-10 08:38:14
#@FilePath     : \RHBESS_Service\Application\EqAccount\HaLun\custom.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-25 08:53:39

import ast
from sqlalchemy import func
from Application.Models.User.authority import Authority
from Application.Models.User.user import User
from Tools.DB.mysql_scada import DEBUG
from Tools.DB.mysql_user import  user_session
from Application.Models.User.page import Page
from Application.Models.User.page_data import PageData
from Application.Models.User.bit import Bit
from Application.Models.User.event import Event
from Application.Models.User.event_alarm_type import EventAlarmType
from Application.Models.User.chars import Charts
from Application.Models.User.report import Report
from Application.Models.User.station import Station
from Application.Models.User.station_relation import StationR
from Application.HistoryData.his_bams import *
from Tools.Utils.num_utils import Translate_cls
# 查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
gz_name = ast.literal_eval(model_config.get('peizhi', 'gz_name'))

class CustomIntetface(BaseHandler):
    ''' 自定义功能汇总 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            data = []
            if kt == 'GetPageList': # 所有配置页面
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                db = self.get_argument('db',None)
                filter = []
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,db:%s'%(descr,pageNum,pageSize,db))
                if descr:
                    filter.append(Page.descr.like('%' + descr + '%'))
                if db:  # 区分站
                    filter.append(Page.station==db)
                total = user_session.query(func.count(Page.id)).filter(*filter).scalar()
                pages = user_session.query(Page).filter(*filter).order_by(Page.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    data.append(eval(str(pag)))
                return self.returnTotalSuc(data,total)
            elif kt == 'GetPageDataByPageId': #获取指定配置页面的具体数据内容
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                type_name = self.get_argument('type_name',None)
                isUse = self.get_argument('isUse',1)  # 是否再使用，0否1是
                id = self.get_argument('id',None)  # 页面id
                db = self.get_argument('db',None)
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,type_name:%s,isUse:%s,id:%s,db:%s'%(descr,pageNum,pageSize,type_name,isUse,id,db))
                if not id:
                    return self.customError("请携带完整参数")
                filter = [PageData.is_use==isUse,PageData.page_id==id]
                if descr:
                    filter.append(PageData.descr.like('%' + descr + '%'))
                if type_name:
                    filter.append(PageData.type_name == type_name)
                if db:  # 区分站
                    filter.append(Page.station==db)
                total = user_session.query(func.count(PageData.id)).filter(*filter).scalar()
                pages = user_session.query(PageData).filter(*filter).order_by(PageData.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for pag in pages:
                    clas = eval(str(pag))
                    clas['bit_descr'] = pag.bit_pageData.descr if pag.bit_pageData else ''
                    clas['page_descr'] = pag.page_pageData.descr
                    data.append(clas)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetBitList': #获取所有bit类型
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                station = self.get_argument('db',None)  # 所属站
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,station:%s'%(descr,pageNum,pageSize,station))
                filter = []
                if descr:
                    filter.append(Bit.descr.like('%' + descr + '%'))
                if station:
                    filter.append(Bit.station.like('%' + station + '%'))
                total = user_session.query(func.count(Bit.id)).filter(*filter).scalar()
                pages = user_session.query(Bit).filter(*filter).order_by(Bit.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    data.append(eval(str(pag)))
                return self.returnTotalSuc(data,total)
            elif kt == 'GetEventAlarmTypes': #获取告警或事件分类集合
                id = self.get_argument('id',None)  # 父级id
                descr = self.get_argument('descr',None)  # 描述
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = int(self.get_argument('isUse',1))
                db = self.get_argument('db', None)
                if DEBUG:
                    logging.info('id:%s,descr:%s,pageNum:%s,pageSize:%s'%(id,descr,pageNum,pageSize))
                filter = [EventAlarmType.is_use==isUse]
                if descr:
                    filter.append(EventAlarmType.descr.like('%' + descr + '%'))
                if id:
                    filter.append(EventAlarmType.parent_id==id)
                else :
                    filter.append(EventAlarmType.parent_id==None)

                total = user_session.query(func.count(EventAlarmType.id)).filter(*filter).scalar()
                pages = user_session.query(EventAlarmType).filter(*filter).order_by(EventAlarmType.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for pag in pages:
                    e = eval(str(pag))
                    e['parent_descr'] = ''
                    if lang=='en':
                        e['descr'] = e['en_descr']
                    if pag.parent_id:
                        e['parent_descr'] = user_session.query(EventAlarmType.descr).filter(EventAlarmType.id==pag.parent_id).first()[0]
                        if lang=='en':
                            e['parent_descr'] = user_session.query(EventAlarmType.en_descr).filter(EventAlarmType.id==pag.parent_id).first()[0]
                    data.append(e)
                if lang == 'en':
                    return self.returnTotalSuc(data,total, lang='en')
                else:
                    return self.returnTotalSuc(data,total, lang=None)
            elif kt == 'GetEventList': #获取告警或事件配置集合
                descr = self.get_argument('descr',None)  # 描述
                point = self.get_argument('point',None)  # 点号
                type_ = self.get_argument('type_',None)  # 1事件，2告警
                type_name = self.get_argument('type_name',None)  # 类型
                class_id = self.get_argument('class_id',None)  # 类别id
                type_id = self.get_argument('type_id',None)  # 类型 id
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = int(self.get_argument('isUse',1))
                # db = self.get_argument('db:[0,1]},{his')
                db = self.get_argument('db',None)
                if DEBUG:
                    logging.info('descr:%s,point:%s,type_:%s,type_name:%s,class_id:%s,type_id:%s,isUse:%s,pageNum:%s,pageSize:%s,db:%s'%(descr,
                    point,type_,type_name,class_id,type_id,isUse,pageNum,pageSize,db))
                if not type_:
                    return self.customError("请携带完整参数") if lang == 'zh' else self.customError("Please bring complete parameters")
                filter = [Event.is_use==isUse,Event.type==type_,Event.id!=1]
                if descr:
                    filter.append(Event.descr.like('%' + descr + '%'))
                if point:
                    filter.append(Event.point==point)
                if type_name:
                    filter.append(Event.type_name==type_name)
                if class_id:
                    filter.append(Event.class_id==class_id)
                if type_id:
                    filter.append(Event.type_id==type_id)
                if db:  # 区分站
                    filter.append(Event.station==db)
                total = user_session.query(func.count(Event.id)).filter(*filter).scalar()
                pages = user_session.query(Event).filter(*filter).order_by(Event.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    js_on=eval(str(pag))
                    if lang=='en':
                        js_on['class_descr'] = js_on['en_class_descr']
                        js_on['descr'] = js_on['en_descr']
                        js_on['type_descr'] = js_on['en_type_descr']
                        js_on['user_descr'] = js_on['en_user_descr']
                    data.append(js_on)
                if lang == 'en':
                    return self.returnTotalSuc(data, total, lang='en')
                else:
                    return self.returnTotalSuc(data, total, lang=None)
            elif kt == 'GetChartsList': #获取曲线配置
                descr = self.get_argument('descr',None)  # 描述
                type_name = self.get_argument('type_name',None)  # 类型
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = int(self.get_argument('isUse',1))
                if DEBUG:
                    logging.info('descr:%s,type_name:%s,isUse:%s,pageNum:%s,pageSize:%s'%(descr,type_name,isUse,pageNum,pageSize))
                filter = [Charts.is_use==isUse]
                if descr:
                    filter.append(Charts.descr.like('%' + descr + '%'))
                if type_name:
                    filter.append(Charts.type_name==type_name)
                total = user_session.query(func.count(Charts.id)).filter(*filter).scalar()
                pages = user_session.query(Charts).filter(*filter).order_by(Charts.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    data.append(eval(str(pag)))
                return self.returnTotalSuc(data,total)
            elif kt == 'GetReportList': #获取表表配置
                descr = self.get_argument('descr',None)  # 描述
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = int(self.get_argument('isUse',1))
                db = self.get_argument('db',None)
                if DEBUG:
                    logging.info('descr:%s,db:%s,isUse:%s,pageNum:%s,pageSize:%s'%(descr,db,isUse,pageNum,pageSize))
                filter = [Report.is_use==isUse,Report.station==db]
                if descr:
                    filter.append(Report.descr.like('%' + descr + '%'))
                total = user_session.query(func.count(Report.id)).filter(*filter).scalar()
                pages = user_session.query(Report).filter(*filter).order_by(Report.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    data.append(eval(str(pag)))
                return self.returnTotalSuc(data,total)
            elif kt == 'GetStationList':  # 所有站
                type = self.get_argument('type', None)  # 储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能
                descr = self.get_argument('descr', None)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter = [StationR.running_state != 4]
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s' % (descr, pageNum, pageSize))
                if descr:
                    filter.append(Station.descr.like('%' + descr + '%'))
                if type:
                    filter.append(StationR.energy_storage==type)
                filter.append(StationR.station_name == Station.name)
                total = user_session.query(func.count(Station.id)).filter(*filter).scalar()
                pages = user_session.query(Station, StationR.energy_storage.label("type")).filter(*filter).order_by(Station.index.asc()).limit(
                    pageSize).offset((pageNum - 1) * pageSize).all()  # 查询电站other_infos里的信息
                for pag in pages:
                    pag, _type = pag
                    o = eval(str(pag))
                    o["type"] = _type
                    if lang=='en':
                        replaced_data = Station.replace_en_fields(o, "")
                        o.update(replaced_data)
                    if pag.other_infos:
                        for k, v in eval(str(pag.other_infos)).items():
                            o[k] = v
                    data.append(o)
                pages_ = user_session.query(StationR.station_name, StationR.energy_storage).filter(StationR.running_state != 4).all()  # 查询电站名称和类型
                for sta in pages_:  # 循环所有站
                    for d in data:
                        if sta[0] == d['name']:
                            if sta[0] in gz_name.keys() and sta[0] != 'dongmu':
                                oo = gz_name[sta[0]]  # 配置告警的对象
                                for name in oo['status']:
                                    bean = real_data('status', name, 'db')
                                    if sta[0]=='guizhou':
                                        if bean['value'] == 1:  # 状态有告警
                                            if lang == 'en':
                                                d['status'] = 'breakdown'
                                            else:
                                                d['status'] = '故障'
                                            break
                                        else:
                                            if lang == 'en':
                                                d['status'] = 'normal'
                                            else:
                                                d['status'] = '正常'
                                    else:
                                        if bean['value'] == 2:  # 状态有告警
                                            if lang=='en':
                                                d['status'] = 'breakdown'
                                            else:
                                                d['status'] = '故障'
                                            break
                                        else:
                                            if lang=='en':
                                                d['status'] = 'normal'
                                            else:
                                                d['status'] = '正常'
                                for names in oo['discrete']:
                                    name = list(names.keys())[0]
                                    bean = real_data('discrete', name, 'db')
                                    if bean['value'] in names[name]:
                                        if lang=='en':
                                            d['status'] = 'breakdown'
                                        else:
                                            d['status'] = '故障'
                                        break
                                    else:
                                        if lang=='en':
                                            d['status'] = 'normal'
                                        else:
                                            d['status'] = '正常'
                                d['statement'] = sta[1]
                            elif sta[0] == 'baodian':
                                if 'bodian' in gz_name.keys():
                                    oo = gz_name[sta]  # 配置告警的对象
                                    for names in oo['discrete']:
                                        name = list(names.keys())[0]
                                        bean = real_data('discrete', name, 'db')
                                        if bean['value'] in names[name]:
                                            if lang=='en':
                                                d['status'] = 'breakdown'
                                            else:
                                                d['status'] = '故障'
                                            break
                                        else:
                                            if lang=='en':
                                                d['status'] = 'normal'
                                            else:
                                                d['status'] = '正常'
                                    d['statement'] = sta[1]
                            elif sta[0] == 'dongmu':
                                v2 = real_data('status', 'dongmu', 'db')
                                if v2:
                                    try:
                                        e2 = v2['body']
                                        for i in e2:
                                            if i['device'][:3] == 'PCS':
                                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                                bit_list_n.reverse()
                                                if bit_list_n[14] == '1':  # pcs告警.故障（取同一个值）
                                                    if lang=='en':
                                                        d['status'] = 'breakdown'
                                                    else:
                                                        d['status'] = '故障'
                                                    break
                                                else:
                                                    if lang=='en':
                                                        d['status'] = 'normal'
                                                    else:
                                                        d['status'] = '正常'
                                            elif i['device'][:3] == 'BMS':
                                                bit_c = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                                bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                                                bit_list_c.reverse()
                                                if bit_list_c[4] == '1':  # bms告警.故障
                                                    if lang=='en':
                                                        d['status'] = 'breakdown'
                                                    else:
                                                        d['status'] = '故障'
                                                    break
                                                else:
                                                    if lang=='en':
                                                        d['status'] = 'normal'
                                                    else:
                                                        d['status'] = '正常'
                                    except:
                                        if lang == 'en':
                                            d['status'] = 'breakdown'
                                        else:
                                            d['status'] = '故障'
                                    d['statement'] = sta[1]
                if lang == 'en':
                    return self.returnTotalSuc(data,total, lang='en')
                else:
                    return self.returnTotalSuc(data,total, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)
        try:
        # if 1:
            if kt == 'PageAdd':  # 添加页面
                descr = self.get_argument('descr',None)
                redirect = self.get_argument('redirect',None)
                component = self.get_argument('component',None)
                icon = self.get_argument('icon',None)
                # db = self.get_argument('db:[0,1]},{his')
                db = self.get_argument('db', None)
                if DEBUG:
                    logging.info('descr:%s,redirect:%s,component:%s,icon:%s,db:%s'%(descr,redirect,component,icon,db))
                if not descr:
                    return self.customError("页面名称为空")
                page = user_session.query(Page).filter(Page.descr==descr).first()
                if page:
                    return self.customError("页面已存在")
                p = Page(descr=descr,redirect=redirect,component=component,icon=icon,op_ts=timeUtils.getNewTimeStr(),station=db)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'PageUpdate':  # 修改页面
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                redirect = self.get_argument('redirect',None)
                component = self.get_argument('component',None)
                icon = self.get_argument('icon',None)
                db = self.get_argument('db',None)
                if DEBUG:
                    logging.info('id:%s,descr:%s,redirect:%s,component:%s,icon:%s,db:%s'%(id,descr,redirect,component,icon,db))
                page = user_session.query(Page).filter(Page.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(Page).filter(Page.descr == descr).first()
                if pa and pa.id != int(id):
                    return self.customError("页面名称已存在")
                if descr :
                    page.descr = descr
                if redirect :
                    page.redirect = redirect
                if component:
                    page.component = component
                if icon :
                    page.icon = icon
                if db :
                    page.station = db
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'PageDelete':  # 删除页面
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Page).filter(Page.id==id).first()
                if not page:
                    return self.customError("无效id")
                user_session.query(Page).filter(Page.id==id).delete()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'PageDataAdd':  # 添加页面数据配置内容
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                page_area = self.get_argument('page_area',None)  # 页面区域
                return_key = self.get_argument('return_key',None)  # 返回的key
                type_name = self.get_argument('type_name',None)  # 数据类型
                page_id = self.get_argument('page_id',None)  # 所属页面
                method = self.get_argument('method',None)  # 计算方式
                bit_id = self.get_argument('bit_id',None)  # 所属配置
                unit = self.get_argument('unit',None)  # 单位
                db = self.get_argument('db',None)  # 所属站
                if DEBUG:
                    logging.info('name:%s,descr:%s,page_area:%s,return_key:%s,type_name:%s,page_id:%s,method:%s,bit_id:%s,unit:%s,db:%s'%(name,descr,
                    page_area,return_key,type_name,page_id,method,bit_id,unit,db))
                if not name or not page_area or not return_key or not page_id or not type_name:
                    return self.customError("请携带完整参数")
                page = user_session.query(PageData).filter(PageData.name==name,PageData.page_id==page_id).first()
                if page:
                    return self.customError("名称已存在")
                if bit_id:
                    if type_name != 'status' or method != 'bit':
                        return self.customError('二进制显示配置有误')
                    if len(name.split('#')) != 16:
                        return self.customError('二进制显示配置个数有误')
                if method == 'bit' and not bit_id:
                    return self.customError('二进制显示配置有误1')
                if type_name == 'status':
                    if not method and (method != 'bit' or method != 'or'):
                        return self.customError('状态量配置有误')
                pd = PageData(name=name,descr=descr,page_area=page_area,return_key=return_key,type_name=type_name,page_id=page_id,
                method=method,bit_id=bit_id,is_use=1,unit=unit,op_ts=timeUtils.getNewTimeStr(),station=db)
                user_session.add(pd)
                user_session.commit()
                # 配置缓存重新初始化
                from main_frame import datas_obj
                return self.returnTypeSuc('')
            elif kt == 'PageDataUpdate':  # 修改页面数据配置内容
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                page_area = self.get_argument('page_area',None)  # 页面区域
                return_key = self.get_argument('return_key',None)  # 返回的key
                type_name = self.get_argument('type_name',None)  # 数据类型
                page_id = self.get_argument('page_id',None)  # 所属页面
                method = self.get_argument('method',None)  # 计算方式
                bit_id = self.get_argument('bit_id',None)  # 所属配置
                isUse = self.get_argument('isUse',None)  # 是否只用
                unit = self.get_argument('unit',None)  # 单位
                db = self.get_argument('db',None)  # 单位
                if DEBUG:
                    logging.info('id:%s,name:%s,descr:%s,page_area:%s,return_key:%s,type_name:%s,page_id:%s,method:%s,bit_id:%s,isUse:%s,unit:%s,db:%s'%(id,name,
                    descr,page_area,return_key,type_name,page_id,method,bit_id,isUse,unit,db))
                page = user_session.query(PageData).filter(PageData.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(PageData).filter(PageData.name == name,PageData.page_id==page_id).first()
                if pa and pa.id != int(id):
                    return self.customError("名称已存在")
                if name :
                    page.name = name
                if descr :
                    page.descr = descr
                if page_area :
                    page.page_area = page_area
                if return_key :
                    page.return_key = return_key
                if type_name :
                    page.type_name = type_name
                if page_id :
                    page.page_id = page_id
                if method :
                    page.method = method
                if bit_id :
                    page.bit_id = bit_id
                page.is_use = isUse
                if unit :
                    page.unit = unit
                if db :
                    page.station = db
                user_session.commit()
                # 配置缓存重新初始化
                from main_frame import datas_obj
                return self.returnTypeSuc('')
            elif kt == 'PageDataDelete':  # 删除页面数据配置项
                id = self.get_argument('id',None)
                page = user_session.query(PageData).filter(PageData.id==id).first()
                if not page:
                    return self.customError("无效id")
                user_session.query(PageData).filter(PageData.id==id).delete()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'BitAdd':  # 添加bit配置
                descr = self.get_argument('descr',None)
                name = self.get_argument('name',None)
                station = self.get_argument('db',None)  # 所属站，多个站用逗号隔开
                if DEBUG:
                    logging.info('descr:%s,name:%s,station:%s'%(descr,name,station))
                if not name:
                    return self.customError("请携带完整参数")
                if len(name.split('#')) != 16:  # 名称必须是16个且以#分割
                    return self.customError("参数名称错误")
                page = user_session.query(Bit).filter(Bit.name==name).first()
                if page:
                    return self.customError("名称已存在")
                p = Bit(descr=descr,name=name,station=station,op_ts=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'BitUpdate':  # 修改页bit配置
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                name = self.get_argument('name',None)
                station = self.get_argument('db',None)  # 所属站，多个站用逗号隔开
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,station:%s'%(id,descr,name,station))
                page = user_session.query(Bit).filter(Bit.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(Bit).filter(Bit.name == name).first()
                if pa and pa.id != int(id):
                    return self.customError("名称已存在")
                if descr :
                    page.descr = descr
                if name :
                    page.name = name
                if station:
                    page.station = station
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'BitDelete':  # 删除bit配置
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Bit).filter(Bit.id==id).first()
                if not page:
                    return self.customError("无效id")
                user_session.query(Bit).filter(Bit.id==id).delete()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'event_rAlarmTypeAdd':  # 添加事件类型
                descr = self.get_argument('descr',None)
                parent_id = self.get_argument('parent_id',None)  # 父级id
                type_ = self.get_argument('type_',None)
                if DEBUG:
                    logging.info('descr:%s,parent_id:%s,type_:%s'%(descr,parent_id,type_))
                if not descr or not type_:
                    return self.customError("请携带完整参数")
                ea = user_session.query(EventAlarmType).filter(EventAlarmType.descr==descr).first()
                if ea:
                    return self.customError("名称已存在")
                p = EventAlarmType(descr=descr,parent_id=parent_id,type = type_)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'EventAlarmTypeUpdate':  # 修改事件类型
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                parent_id = self.get_argument('parent_id',None)
                isUse = self.get_argument('isUse',None)
                type_ = self.get_argument('type_',None)
                if DEBUG:
                    logging.info('id:%s,descr:%s,parent_id:%s,isUse:%s'%(id,descr,parent_id,isUse))
                page = user_session.query(EventAlarmType).filter(EventAlarmType.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(EventAlarmType).filter(EventAlarmType.descr == descr).first()
                if pa and pa.id != int(id):
                    return self.customError("名称已存在")
                if descr :
                    page.descr = descr
                if parent_id :
                    page.parent_id = parent_id
                if type_ :
                    page.type = type_
                page.is_use = isUse
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'EventAlarmTypeDelete':  # 删除事件类型
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(EventAlarmType).filter(EventAlarmType.id==id).first()
                if not page:
                    return self.customError("无效id")
                page.deleteEventAlarmType(id)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'EventAdd':  # 添加事件（告警）配置
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                point = self.get_argument('point',None)
                type_ = self.get_argument('type_',None)
                type_name = self.get_argument('type_name',None)
                class_id = self.get_argument('class_id',None)
                type_id = self.get_argument('type_id',None)
                opt1 = self.get_argument('opt1', '')
                opt2 = self.get_argument('opt2', '')
                opt3 = self.get_argument('opt3', '')
                opt4 = self.get_argument('opt4', '')
                opt5 = self.get_argument('opt5', '')
                opt6 = self.get_argument('opt6', '')
                opt7 = self.get_argument('opt7', '')
                opt8 = self.get_argument('opt8', '')
                opt9 = self.get_argument('opt9', '')
                opt10 = self.get_argument('opt10', '')
                type_name1 = self.get_argument('type_name1', '')
                type_name2 = self.get_argument('type_name2', '')
                type_name3 = self.get_argument('type_name3', '')
                type_name4 = self.get_argument('type_name4', '')
                type_name5 = self.get_argument('type_name5', '')
                type_name6 = self.get_argument('type_name6', '')
                type_name7 = self.get_argument('type_name7', '')
                type_name8 = self.get_argument('type_name8', '')
                type_name9 = self.get_argument('type_name9', '')
                type_name10 = self.get_argument('type_name10', '')
                db = self.get_argument('db',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('descr:%s,name:%s,point:%s,type_:%s，type_name:%s,class_id：%s，type_id：%s,opt1:%s,opt2:%s,opt3:%s,opt4:%s,opt5:%s,opt6:%s,opt7:%s,opt8:%s,opt9:%s,\
                        opt10:%s,type_name1:%s,type_name2:%s,type_name3:%s,type_name4:%s,type_name5:%s,type_name6:%s,type_name7:%s,type_name8:%s,type_name9:%s,type_name10:%s,db:%s'%(
                        descr,name,point,type_,type_name,class_id,type_id,opt1,opt2,opt3,opt4,opt5,opt6,opt7,opt8,opt9,opt10,type_name1,type_name2,type_name3,type_name4,type_name5,
                        type_name6,type_name7,type_name8,type_name9,type_name10,db))
                if not name or not type_ or not type_name or not class_id or not type_id or not descr:
                    return self.customError("请携带完整参数") if lang == 'zh' else self.customError("Please bring complete parameters")

                ea = user_session.query(Event).filter(Event.name==name,Event.type_name==type_name,Event.type==type_).first()
                if ea:
                    return self.customError("名称已存在") if lang == 'zh' else self.customError("Name already exists")
                session = self.getOrNewSession()
                user_id = session.user['id']
                bean = real_data(type_name,name,'db')
                if not bean['desc']:
                    bean = real_data(type_name,name,'ram')
                index = bean['index']
                op1 = '{}#{}'.format(opt1,type_name1);op2 = '{}#{}'.format(opt2,type_name2);op3 = '{}#{}'.format(opt3,type_name3)
                op4 = '{}#{}'.format(opt4,type_name4);op5 = '{}#{}'.format(opt5,type_name5);op6 = '{}#{}'.format(opt6,type_name6);
                op7 = '{}#{}'.format(opt7,type_name7);op8 = '{}#{}'.format(opt8,type_name8);op9 = '{}#{}'.format(opt9,type_name9);op10 = '{}#{}'.format(opt10,type_name10);
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                t_res = t_cls.str_chinese(descr)
                if ty == 2:
                    en_descr = t_res
                else:
                    en_descr = descr
                    descr = t_res
                p = Event(name=name,descr=descr,en_descr=en_descr,point=point,type=type_,type_name=type_name,class_id=class_id,type_id=type_id,is_use=1,op_ts=timeUtils.getNewTimeStr(),user_id=user_id,
                index=index,opt1=op1,opt2=op2,opt3=op3,opt4=op4,opt5=op5,opt6=op6,opt7=op7,opt8=op8,opt9=op9,opt10=op10,station=db)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'EventUpdate':  # 修改事件（告警）配置
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                point = self.get_argument('point',None)
                type_ = self.get_argument('type_',None)
                type_name = self.get_argument('type_name',None)
                class_id = self.get_argument('class_id',None)
                type_id = self.get_argument('type_id',None)
                opt1 = self.get_argument('opt1', '')
                opt2 = self.get_argument('opt2', '')
                opt3 = self.get_argument('opt3', '')
                opt4 = self.get_argument('opt4', '')
                opt5 = self.get_argument('opt5', '')
                opt6 = self.get_argument('opt6', '')
                opt7 = self.get_argument('opt7', '')
                opt8 = self.get_argument('opt8', '')
                opt9 = self.get_argument('opt9', '')
                opt10 = self.get_argument('opt10', '')
                isUse = self.get_argument('isUse', None)
                type_name1 = self.get_argument('type_name1', '')
                type_name2 = self.get_argument('type_name2', '')
                type_name3 = self.get_argument('type_name3', '')
                type_name4 = self.get_argument('type_name4', '')
                type_name5 = self.get_argument('type_name5', '')
                type_name6 = self.get_argument('type_name6', '')
                type_name7 = self.get_argument('type_name7', '')
                type_name8 = self.get_argument('type_name8', '')
                type_name9 = self.get_argument('type_name9', '')
                type_name10 = self.get_argument('type_name10', '')
                db = self.get_argument('db',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,point:%s,type_:%s，type_name:%s,class_id：%s，type_id：%s,opt1:%s,opt2:%s,opt3:%s,opt4:%s,opt5:%s,opt6:%s,opt7:%s,opt8:%s,opt9:%s,\
                    opt10:%s,isUse:%s,type_name1:%s,type_name2:%s,type_name3:%s,type_name4:%s,type_name5:%s,type_name6:%s,type_name7:%s,type_name8:%s,type_name9:%s,type_name10:%s,db:%s'%(id,
                    descr,name,point,type_,type_name,class_id,type_id,opt1,opt2,opt3,opt4,opt5,opt6,opt7,opt8,opt9,opt10,isUse,type_name1,type_name2,type_name3,type_name4,type_name5,
                        type_name6,type_name7,type_name8,type_name9,type_name10,db))
                page = user_session.query(Event).filter(Event.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                pa = user_session.query(Event).filter(Event.name==name,Event.type_name==type_name,Event.type==type_).first()
                if pa and pa.id != int(id):
                    return self.customError("名称已存在") if lang == 'zh' else self.customError("Name already exists")
                op1 = '{}#{}'.format(opt1,type_name1);op2 = '{}#{}'.format(opt2,type_name2);op3 = '{}#{}'.format(opt3,type_name3)
                op4 = '{}#{}'.format(opt4,type_name4);op5 = '{}#{}'.format(opt5,type_name5);op6 = '{}#{}'.format(opt6,type_name6);
                op7 = '{}#{}'.format(opt7,type_name7);op8 = '{}#{}'.format(opt8,type_name8);op9 = '{}#{}'.format(opt9,type_name9);op10 = '{}#{}'.format(opt10,type_name10);
                if descr:
                    ty = 1 if lang == 'en' else 2
                    t_cls = Translate_cls(ty)
                    t_res = t_cls.str_chinese(descr)
                    if ty == 2:
                        en_descr = t_res
                    else:
                        en_descr = descr
                        descr = t_res
                    page.descr = descr
                    page.en_descr = en_descr
                if name :
                    page.name = name
                if point :
                    page.point = point
                if type_ :
                    page.type = type_
                if type_name :
                    page.type_name = type_name
                    bean = real_data(type_name,name,'db')
                    if not bean['desc']:
                        bean = real_data(type_name,name,'ram')
                    page.index = bean['index']
                if class_id :
                    page.class_id = class_id
                if type_id :
                    page.type_id = type_id
                page.opt1 = op1
                page.opt2 = op2
                page.opt3 = op3
                page.opt4 = op4
                page.opt5 = op5
                page.opt6 = op6
                page.opt7 = op7
                page.opt8 = op8
                page.opt9 = op9
                page.opt10 = op10
                page.is_use = int(isUse)
                if db:
                    page.station=db
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'EventDelete':  # 删除事件（告警）配置
                id = self.get_argument('id',None)
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Event).filter(Event.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                page.deleteEvent(id)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'ChartsAdd':  # 添加曲线配置
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                type_name = self.get_argument('type_name',None)
                if DEBUG:
                    logging.info('descr:%s,name:%s,type_name:%s'%(descr,name,type_name))
                if not name or not type_name :
                    return self.customError("请携带完整参数")

                ea = user_session.query(Charts).filter(Charts.name==name,Charts.type_name==type_name).first()
                if ea:
                    return self.customError("名称已存在")
                session = self.getOrNewSession()
                user_id = session.user['id']
                bean = real_data(type_name,name,'db')
                if not bean['desc']:
                    bean = real_data(type_name,name,'ram')
                index = bean['index']
                p = Charts(name=name,descr=descr,type_name=type_name,is_use=1,op_ts=timeUtils.getNewTimeStr(),user_id=user_id,index=index)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ChartsUpdate':  # 修改曲线配置
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                type_name = self.get_argument('type_name',None)
                isUse = self.get_argument('isUse',None)
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,type_name:%s,isUse:%s'%(id,descr,name,type_name,isUse))
                page = user_session.query(Charts).filter(Charts.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(Charts).filter(Charts.name==name,Charts.type_name==type_name).first()
                if pa and pa.id != int(id):
                    return self.customError("名称已存在")
                if descr :
                    page.descr = descr
                if name :
                    page.name = name
                if type_name :
                    page.type_name = type_name
                    bean = real_data(type_name,name,'db')
                    if not bean['desc']:
                        bean = real_data(type_name,name,'ram')
                    page.index = bean['index']
                page.is_use = isUse
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ChartsDelete':  # 删除曲线配置
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Charts).filter(Charts.id==id).first()
                if not page:
                    return self.customError("无效id")
                page.deleteCharts(id)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ReportAdd':  # 添加报表配置
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                start_time = self.get_argument('start_time',None)
                end_time = self.get_argument('end_time',None)
                rate = self.get_argument('rate',None)  # 费率
                station = self.get_argument('db:[0,1]},{his')
                months = self.get_argument('months',None)
                years = self.get_argument('years',None)
                is_use = self.get_argument('is_use',1)
                if DEBUG:
                    logging.info('name:%s,descr:%s,start_time:%s,end_time:%s,rate:%s,station:%s,months:%s,is_use:%s,years:%s'%(name,descr,start_time,end_time,rate,station,months,is_use,years))
                if not descr or not name or not start_time or not end_time or not rate or not years:
                    return self.customError("请携带完整参数")
                p = re.compile(r'^(0?[0-9]|1[0-9]|2[0-3]):(0?[0-9]|[1-5][0-9]):(0?[0-9]|[1-5][0-9])$')
                s1 = p.search(start_time)
                s2 = p.search(end_time)
                if not s1 or not s2:
                    return self.customError("时间格式有误")
                page = user_session.query(Report).filter(Report.descr==descr,Report.start_time==start_time,Report.end_time==end_time,Report.rate==rate,Report.station==station).first()
                if page:
                    return self.customError("数据已存在")
                p = Report(name=name,descr=descr,start_time=start_time,end_time=end_time,rate=rate,op_ts=timeUtils.getNewTimeStr(),station=station,months=months,is_use=is_use,years=years)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ReportUpdate':  # 修改页面
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                start_time = self.get_argument('start_time',None)
                end_time = self.get_argument('end_time',None)
                rate = self.get_argument('rate',None)  # 费率
                station = self.get_argument('db:[0,1]},{his')
                months = self.get_argument('months',None)
                is_use = self.get_argument('is_use',None)
                years = self.get_argument('years',None)
                if DEBUG:
                    logging.info('id:%s,name:%s,descr:%s,start_time:%s,end_time:%s,rate:%s,station:%s,months:%s,is_use:%s,years:%s'%(id,name,
                    descr,start_time,end_time,rate,station,months,is_use,years))
                page = user_session.query(Report).filter(Report.id==id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(Report).filter(Report.descr==descr,Report.start_time==start_time,Report.end_time==end_time,Report.rate==rate,Report.station==station).first()

                if pa and pa.id != int(id):
                    return self.customError("数据已存在")
                if name :
                    page.name = name
                if descr :
                    page.descr = descr
                if start_time :
                    page.start_time = start_time
                if end_time:
                    page.end_time = end_time
                if rate :
                    page.rate = rate
                if station :
                    page.station = station
                if months:
                    page.months = months
                if years:
                    page.years = years
                page.is_use = is_use
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ReportDelete':  # 删除报表配置
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Report).filter(Report.id==id).first()
                if not page:
                    return self.customError("无效id")
                user_session.query(Report).filter(Report.id==id).delete()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'StationAdd':  # 添加子站配置
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                address = self.get_argument('address',None)  # 地址
                index = self.get_argument('index',None)
                station_type = self.get_argument('station_type', None)
                project_info = self.get_argument('project_info', None)  # 项目介绍
                longitude = self.get_argument('longitude', None)  # 经度
                dimension = self.get_argument('dimension', None)  # 纬度
                scene_picture = self.get_argument('scene_picture', None)  # 现场图片
                volume = self.get_argument('volume', None)  # 电站容量
                start_ts = self.get_argument('start_ts', None)  # 电站开始时间
                in_no = self.get_argument('in_no',"")  # 内部订单 o
                delivery_time = self.get_argument('delivery_time',"")  # 项目交付日期 o
                direct_customer_name  = self.get_argument('direct_customer_name',"")  #直接用户名o
                direct_customer_user  = self.get_argument('direct_customer_user',"")  #联系人 o
                direct_customer_tel  = self.get_argument('direct_customer_tel',"")  #联系电话o
                direct_customer_fax  = self.get_argument('direct_customer_fax',"")  #传真o
                final_customer_name  = self.get_argument('final_customer_name',"")  #最终用户名o
                final_customer_user  = self.get_argument('final_customer_user',"")  #联系人o
                final_customer_tel  = self.get_argument('final_customer_tel',"")  #联系电话o
                address_fax  = self.get_argument('address_fax',"")  #现场传真o
                project_name = self.get_argument('project_name', "")  # 项目名称 o
                date_of_commissioning = self.get_argument('date_of_commissioning', "")  # 投运日期 o
                number_of_subsystems = self.get_argument('number_of_subsystems', "")  # 分系统数量o
                user_type = self.get_argument('user_type', "")  # 用户类型 o
                operational_strategy = self.get_argument('operational_strategy', "")  # 运行策略o
                battery_type = self.get_argument('battery_type', "")  # 电池类型o
                rated_capacity = self.get_argument('rated_capacity', "")  # 额定容量o
                rated_power = self.get_argument('rated_power', "")  # 额定功率o
                access_voltage_level = self.get_argument('access_voltage_level', "")  # 接入电压等级o
                connection_voltage_class = self.get_argument('connection_voltage_class', "")  # 连接电压等级o
                if not descr or not name or not index:
                    if lang=='en':
                        return self.customError("Please bring full parameters")
                    else:
                        return self.customError("请携带完整参数")
                if lang == 'en':
                    page = user_session.query(Station).filter(Station.en_descr==descr,Station.name==name).first()
                else:
                    page = user_session.query(Station).filter(Station.descr == descr, Station.name == name).first()
                if page:
                    if lang == 'en':
                        return self.customError("Data already exists")
                    else:
                        return self.customError("数据已存在")
                if lang == 'en':#翻译成中文
                    zh_descr = translate_text(descr,1)
                    zh_address = translate_text(address,1)
                    zh_name = translate_text(name,1)
                    zh_index = translate_text(index,1)
                    other_infos = {"in_no": in_no, "server_depart": "工程服务部", "delivery_time": delivery_time,
                                   "direct_customer_name": direct_customer_name,
                                      "direct_customer_user": direct_customer_user,
                                      "direct_customer_tel": direct_customer_tel,
                                      "direct_customer_fax": direct_customer_fax,
                                      "final_customer_name": final_customer_name,
                                      "final_customer_user": final_customer_user,
                                      "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    basic_info = {"project_name": project_name, "date_of_commissioning": date_of_commissioning,
                                   "user_type": user_type,
                                     "operational_strategy": operational_strategy,
                                     "battery_type": battery_type, "rated_capacity": rated_capacity,
                                     "rrated_power": rated_power, "access_voltage_level": access_voltage_level,
                                  "connection_voltage_class": connection_voltage_class}
                    en_other_infos = {"in_no": in_no, "server_depart": "Engineering service department", "delivery_time": delivery_time,
                                      "direct_customer_name": direct_customer_name,
                                      "direct_customer_user": direct_customer_user,
                                      "direct_customer_tel": direct_customer_tel,
                                      "direct_customer_fax": direct_customer_fax,
                                      "final_customer_name": final_customer_name,
                                      "final_customer_user": final_customer_user,
                                      "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    en_basic_info = {"project_name": project_name, "date_of_commissioning": date_of_commissioning,
                                     "number_of_subsystems": number_of_subsystems,
                                     "user_type": user_type,
                                     "operational_strategy": operational_strategy,
                                     "battery_type": battery_type, "rated_capacity": rated_capacity,
                                     "rrated_power": rated_power, "access_voltage_level": access_voltage_level,
                                     "connection_voltage_class": connection_voltage_class}
                    if DEBUG:
                        logging.info('name:%s,descr:%s,address:%s,index:%s,other_infos:%s' % (
                        name, descr, address, index, other_infos))
                else:
                    en_descr = translate_text(descr,2)
                    en_address = translate_text(address,2)
                    other_infos = {"in_no":in_no,"server_depart":"工程服务部","delivery_time":delivery_time,
                                   "direct_customer_name":direct_customer_name,"direct_customer_user":direct_customer_user,
                    "direct_customer_tel":direct_customer_tel,"direct_customer_fax":direct_customer_fax,"final_customer_name":final_customer_name,"final_customer_user":final_customer_user,
                    "final_customer_tel":final_customer_tel,"address_fax":address_fax}
                    basic_info = {"project_name": project_name, "date_of_commissioning": date_of_commissioning, "number_of_subsystems": number_of_subsystems,
                                   "user_type": user_type,
                                   "operational_strategy": operational_strategy,
                                   "battery_type": battery_type, "rated_capacity": rated_capacity,
                                   "rrated_power": rated_power, "access_voltage_level": access_voltage_level,
                                   "connection_voltage_class": connection_voltage_class}
                    en_other_infos = {"in_no": in_no, "server_depart": "Engineering service department", "delivery_time": delivery_time,
                                   "direct_customer_name":direct_customer_name,"direct_customer_user":direct_customer_user,
                    "direct_customer_tel":direct_customer_tel,"direct_customer_fax":direct_customer_fax,"final_customer_name":final_customer_name,"final_customer_user":final_customer_user,
                    "final_customer_tel":final_customer_tel,"address_fax":address_fax}
                    en_basic_info = {"project_name": project_name, "date_of_commissioning": date_of_commissioning,
                                  "number_of_subsystems": number_of_subsystems,
                                  "user_type": user_type,
                                   "operational_strategy": operational_strategy,
                                   "battery_type": battery_type, "rated_capacity": rated_capacity,
                                  "rrated_power": rated_power, "access_voltage_level": access_voltage_level,
                                  "connection_voltage_class": connection_voltage_class}
                    if DEBUG:
                        logging.info('name:%s,descr:%s,address:%s,index:%s,other_infos:%s'%(name,descr,address,index,other_infos))
                basic_info = json.dumps(basic_info, ensure_ascii=False)
                other_infos = json.dumps(other_infos, ensure_ascii=False)
                en_basic_info = json.dumps(en_basic_info, ensure_ascii=False)
                en_other_infos = json.dumps(en_other_infos, ensure_ascii=False)
                if lang == 'en':  # 翻译成中文
                    p = Station(name=zh_name,descr=zh_descr,address=zh_address,op_ts=timeUtils.getNewTimeStr(),index=zh_index,other_infos=other_infos,station_type=station_type,project_info=project_info,
                                longitude=longitude,dimension=dimension,scene_picture=scene_picture,volume=volume,basic_info=basic_info,start_ts=start_ts,
                                en_address=address,en_descr=descr,en_other_infos=en_other_infos,en_project_info=project_info,en_basic_info=en_basic_info)
                else:
                    p = Station(name=name, descr=descr, address=address, op_ts=timeUtils.getNewTimeStr(), index=index,
                                other_infos=other_infos, station_type=station_type, project_info=project_info,
                                longitude=longitude, dimension=dimension, scene_picture=scene_picture, volume=volume,
                                basic_info=basic_info, start_ts=start_ts,
                                en_address=en_address, en_descr=en_descr, en_other_infos=en_other_infos,
                                en_project_info=project_info, en_basic_info=en_basic_info)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'StationUpdate':  # 修改页面
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                descr = self.get_argument('descr',None)
                address = self.get_argument('address',None)
                index = self.get_argument('index',None)
                in_no = self.get_argument('in_no',"")  # 内部订单 o
                delivery_time = self.get_argument('delivery_time',"")  # 项目交付日期 o
                direct_customer_name  = self.get_argument('direct_customer_name',"")  #直接用户名o
                direct_customer_user  = self.get_argument('direct_customer_user',"")  #联系人 o
                direct_customer_tel  = self.get_argument('direct_customer_tel',"")  #联系电话o
                direct_customer_fax  = self.get_argument('direct_customer_fax',"")  #传真o
                final_customer_name  = self.get_argument('final_customer_name',"")  #最终用户名o
                final_customer_user  = self.get_argument('final_customer_user',"")  #联系人o
                final_customer_tel  = self.get_argument('final_customer_tel',"")  #联系电话o
                address_fax  = self.get_argument('address_fax',"")  #现场传真o
                page = user_session.query(Station).filter(Station.id == id).first()
                if not page:
                    return self.customError("无效id")
                if lang == 'en':
                    pa = user_session.query(Station).filter(Station.en_descr == descr, Station.name == name).first()
                else:
                    pa = user_session.query(Station).filter(Station.descr == descr, Station.name == name).first()
                if pa and pa.id != int(id):
                    if lang == 'en':
                        return self.customError("Data already exists")
                    else:
                        return self.customError("数据已存在")
                if lang=='en':
                    zh_descr = translate_text(descr,1)
                    zh_address = translate_text(address,1)
                    other_infos = {"in_no": in_no, "server_depart": "工程服务部", "delivery_time": delivery_time,
                                   "direct_customer_name": direct_customer_name,
                                      "direct_customer_user": direct_customer_user,
                                      "direct_customer_tel": direct_customer_tel,
                                      "direct_customer_fax": direct_customer_fax,
                                      "final_customer_name": final_customer_name,
                                      "final_customer_user": final_customer_user,
                                      "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    en_other_infos = {"in_no": in_no, "server_depart": "Engineering service department", "delivery_time": delivery_time,
                                      "direct_customer_name": direct_customer_name,
                                      "direct_customer_user": direct_customer_user,
                                      "direct_customer_tel": direct_customer_tel,
                                      "direct_customer_fax": direct_customer_fax,
                                      "final_customer_name": final_customer_name,
                                      "final_customer_user": final_customer_user,
                                      "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    if in_no:
                        other_infos["in_no"]=in_no
                        en_other_infos["in_no"]=in_no
                    if delivery_time:
                        other_infos["delivery_time"]=delivery_time
                        en_other_infos["delivery_time"]=delivery_time
                    if direct_customer_name:
                        other_infos["direct_customer_name"]=direct_customer_name
                        en_other_infos["direct_customer_name"]=direct_customer_name
                    if direct_customer_user:
                        other_infos["in_no"]=direct_customer_user
                        en_other_infos["in_no"]=direct_customer_user
                    if direct_customer_tel:
                        other_infos["direct_customer_tel"]=direct_customer_tel
                        en_other_infos["direct_customer_tel"]=direct_customer_tel
                    if direct_customer_fax:
                        other_infos["direct_customer_fax"]=direct_customer_fax
                        en_other_infos["direct_customer_fax"]=direct_customer_fax
                    if final_customer_name:
                        other_infos["final_customer_name"]=final_customer_name
                        en_other_infos["final_customer_name"]=final_customer_name
                    if final_customer_user:
                        other_infos["final_customer_user"]=final_customer_user
                        en_other_infos["final_customer_user"]=final_customer_user
                    if final_customer_tel:
                        other_infos["final_customer_tel"]=final_customer_tel
                        en_other_infos["final_customer_tel"]=final_customer_tel
                    if address_fax:
                        other_infos["address_fax"]=address_fax
                        en_other_infos["address_fax"]=address_fax
                    if address_fax:
                        other_infos["address_fax"]=address_fax
                        en_other_infos["address_fax"]=address_fax
                    if descr:
                        page.en_descr = descr
                        page.descr = zh_descr
                    if address:
                        page.en_address = address
                        page.address = zh_address
                else:
                    en_descr = translate_text(descr,2)
                    en_address = translate_text(address,2)
                    other_infos = {"in_no": in_no, "server_depart": "工程服务部", "delivery_time": delivery_time,
                                   "direct_customer_name": direct_customer_name,
                                   "direct_customer_user": direct_customer_user,
                                   "direct_customer_tel": direct_customer_tel,
                                   "direct_customer_fax": direct_customer_fax,
                                   "final_customer_name": final_customer_name,
                                   "final_customer_user": final_customer_user,
                                   "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    en_other_infos = {"in_no": in_no, "server_depart": "Engineering service department",
                                      "direct_customer_name": direct_customer_name,
                                   "direct_customer_user": direct_customer_user,
                                   "direct_customer_tel": direct_customer_tel,
                                   "direct_customer_fax": direct_customer_fax,
                                   "final_customer_name": final_customer_name,
                                   "final_customer_user": final_customer_user,
                                   "final_customer_tel": final_customer_tel, "address_fax": address_fax}
                    if in_no:
                        other_infos["in_no"] = in_no
                        en_other_infos["in_no"] = in_no
                    if delivery_time:
                        other_infos["delivery_time"] = delivery_time
                        en_other_infos["delivery_time"] = delivery_time
                    if direct_customer_name:
                        other_infos["direct_customer_name"] = direct_customer_name
                        en_other_infos["direct_customer_name"] = direct_customer_name
                    if direct_customer_user:
                        other_infos["in_no"] = direct_customer_user
                        en_other_infos["in_no"] = direct_customer_user
                    if direct_customer_tel:
                        other_infos["direct_customer_tel"] = direct_customer_tel
                        en_other_infos["direct_customer_tel"] = direct_customer_tel
                    if direct_customer_fax:
                        other_infos["direct_customer_fax"] = direct_customer_fax
                        en_other_infos["direct_customer_fax"] = direct_customer_fax
                    if final_customer_name:
                        other_infos["final_customer_name"] = final_customer_name
                        en_other_infos["final_customer_name"] = final_customer_name
                    if final_customer_user:
                        other_infos["final_customer_user"] = final_customer_user
                        en_other_infos["final_customer_user"] = final_customer_user
                    if final_customer_tel:
                        other_infos["final_customer_tel"] = final_customer_tel
                        en_other_infos["final_customer_tel"] = final_customer_tel
                    if address_fax:
                        other_infos["address_fax"] = address_fax
                        en_other_infos["address_fax"] = address_fax
                    if address_fax:
                        other_infos["address_fax"] = address_fax
                        en_other_infos["address_fax"] = address_fax
                    if descr:
                        page.en_descr = en_descr
                        page.descr = descr
                    if address:
                        page.en_address = en_address
                        page.address = address
                other_infos = json.dumps(other_infos,ensure_ascii=False)
                en_other_infos = json.dumps(en_other_infos,ensure_ascii=False)
                if DEBUG:
                    logging.info('id:%s,name:%s,descr:%s,address:%s,index:%s,other_infos:%s'%(id,name,descr,address,index,other_infos))
                if name :
                    page.name = name
                if index :
                    page.index = index
                page.other_infos = other_infos
                page.en_other_infos = en_other_infos
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'StationDelete':  # 删除报表配置
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(Station).filter(Station.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                p = user_session.query(User).filter(User.station_id.like("%"+id+"%")).first()
                if p:
                    if lang=='en':
                        return self.customError("Delete the associated data first")
                    else:
                        return self.customError("请先删除关联数据")
                p2 = user_session.query(Authority).filter(Authority.station_id.like("%"+id+"%")).first()
                if p2:
                    if lang=='en':
                        return self.customError("Delete the associated data first!")
                    else:
                        return self.customError("请先删除关联数据!")
                user_session.query(Station).filter(Station.id==id).delete()
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()


