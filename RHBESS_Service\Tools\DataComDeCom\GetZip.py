#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \em_pjt_rh\RHBESS_empower\Tools\DataComDeCom\GetZip.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-27 10:05:52

import StringIO
import gzip


class GetZiP(object):

    @classmethod
    def gzip_compress(cls, buf):    # gzip 压缩字符串
        out = StringIO.StringIO()
        with gzip.GzipFile(fileobj=out, mode="w") as f:
            f.write(buf)
        return out.getvalue()

    @classmethod
    def gzip_decompress(cls, buf):  # gzip 解压字符串
        obj = StringIO.StringIO(buf)
        with gzip.GzipFile(fileobj=obj) as f:
            result = f.read()
        return result
