#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \HY_empowerd:\em_pjt_rh\RHBESS_empower\Application\Models\manufacturer.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-28 09:42:08

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, \
    VARCHAR


class Manufacturer(user_Base):
    u'厂商表'
    __tablename__ = "t_manufacturer"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    descr = Column(String(256), nullable=False, comment=u"描述")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
 
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'descr':'%s','op_ts':'%s'}" % (self.id,self.descr,self.op_ts)


    def deleteManufacturer(self,id):
       
        try:
            
            user_session.query(Manufacturer).filter(Manufacturer.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False