#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-05 17:28:02
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Mqtt\dongmu_mqtt_client.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-11-14 23:03:46

'''
实时数据中心app运行情况
'''
import sys,json
import os,time
import logging
import paho.mqtt.client as mqtt
# from Application.Cfg.dir_cfg import model_config
from Application.Models.User.event import Event
# from Tools.DB.mongodb_con import  dongmu_mongodb_client,dongmu_mongo_db
from Application.Models.His.r_ACDMS import HisDM
from Application.Models.SelfStationPoint.t_device import DevicePT
from Application.Models.SelfStationPoint.t_status import StatusPT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT
from Application.Models.User.alarm_r import AlarmR
from Tools.DB.redis_con import r_real
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_his import dongmu_session,_dm_his_engine,HIS_DATABASE_
from Tools.Utils.time_utils import timeUtils
from Tools.Cfg.get_cnf import work_dir
import redis
path = os.path.join(work_dir, 'log/dongmumqtt.log')
# reload(sys)
# sys.setdefaultencoding("utf-8")
data_arr = []
json_data = {}

real_pool = redis.ConnectionPool(host='***********',port=6379,db=1,username='',password='A45lEdj&f335@3s5h*8g',decode_responses=False, encoding='UTF-8')
r_real_45 = redis.Redis(connection_pool=real_pool)


def on_connect(client, userdata, flags, rc):
    '''连接成功回调'''
    if rc == 0:  # 连接成功
        logging.info('start success')
        print('Connection Succeed!')
    else:
        print('Connected with result code '+str(rc))
    # 订阅主题
    client.subscribe("dongmuems/dongmu/timing",0)
    
def on_message(client, userdata, msg):
    '''消息接收回调'''
    print ('start----',time.time())
    # mm = timeUtils.getNewTimeStr()[:7].replace('-','')
    topic = msg.topic  # 订阅主题
    data = msg.payload  # 返回值
    msg_t = 'topic:%s'%topic
    msg_p = 'payload:%s'%data
    json_data = json.loads(data)
    datatype = json_data['datatype']  # 上报的数据类型
    table = 'r_%s'%(datatype)
    
    HisTable = HisDM(table)
    dm = HisTable(time=json_data['time'],utime=json_data['utime'],ntime=timeUtils.nowSecs(),datainfo=data.decode())
    dongmu_session.add(dm)
    dongmu_session.commit()
   
    # obj = {"time":json_data['time'],"utime":json_data['utime'],"ntime":timeUtils.nowSecs(),"datainfo":data.decode()}
    # mycol = dongmu_mongo_db[table]
    # result = mycol.insert(obj)
    r_real.hset(datatype,'dongmu',data.decode())
    r_real_45.hset(datatype,'dongmu',data.decode())

    print ('msg_t:',msg_t)
    if json_data['datatype'] == 'status':  # 只有状态量才会存储
        alarm_save(json_data)  # 调用存储
        # delExceed()  # 调用删除
   
    logging.info(msg_t)
    print ('end----',time.time())
    user_session.close()
    mqtt_session.close()
    # logging.info(msg_p)
    

def get_data_peizhi():
    # 取告警配置
    statuss = user_session.query(Event).filter(Event.station=='dongmu',Event.is_use==1,Event.type==2).all()
    for bean in statuss:
        dvice = {}
        names = bean.name.split('.')
        bits = mqtt_session.query(StatusBitsPT).filter(StatusBitsPT.status_id==StatusPT.id,StatusPT.device_id==DevicePT.id,DevicePT.name==names[0],
                                                       StatusPT.name==names[1],StatusBitsPT.name==names[2]).first()
        dvice['name']=bean.name
        dvice['id']=bean.id
        dvice['index']=bits.bits
        dvice['descr']=[bits.desc_off,bits.desc_on]
        dvice['value']=0
        data_arr.append(dvice)
    
    # print 'arr:',data_arr
    
def alarm_save(json_data):
    '''
    存储告警信息
    '''
    # print 'json_data:',json_data
    for a in data_arr:
        names = a['name'].split('.')
        for j in json_data['body']:
            if j['device'] == names[0]:  # 同一个设备
                ST_V = int(j[names[1]])
                if ST_V:  # 不等于0
                    bit_n='{:016b}'.format(ST_V)  # 转成2进制，高位补零
                    bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                    bit_list.reverse()
                    now_v = int(bit_list[a['index']])
                    if now_v != a['value']:   # 和上一次值不一样
                        a['value'] = now_v  # 重新赋值
                        al = AlarmR(value=now_v+1,value_descr=a['descr'][now_v],event_id=a['id'],op_ts=timeUtils.getNewTimeStr(),ts=timeUtils.ssTtimes(json_data['time']),station='dongmu')
                        user_session.add(al)
    
    user_session.commit()
    
def delExceed():
    '''
    删除超过一定数量的数据
    '''
    try:
        # 删除多余的告警记录
        child_alarm = user_session.query(AlarmR.id.label('id')).filter(AlarmR.station=='dongmu').order_by(AlarmR.id.desc()).limit(50).offset(5000).subquery()
        user_session.query(AlarmR).filter(AlarmR.id==child_alarm.c.id).delete()
        user_session.commit()
    except Exception as E:
        logging.error(E)
        user_session.rollback()
if __name__ == "__main__":
    get_data_peizhi()  # 查询配置
    ip = "************"
    port = 1883
    keepalive = 60
    username = "rhbess1"
    password = "rhbess123"

    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message

    client.username_pw_set(username, password=password)  # 用户名和密码
    client.connect(ip, port, keepalive)
    client.loop_forever()  # 守护连接状态
    
    
    
    
    

        



