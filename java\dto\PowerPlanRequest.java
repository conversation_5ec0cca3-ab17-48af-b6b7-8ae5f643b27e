package com.robestec.analysis.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 功率计划请求DTO
 */
@Data
public class PowerPlanRequest {

    /**
     * 计划类型: 1-自定义, 2-周期性, 3-节假日
     */
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    /**
     * 功率列表(JSON字符串)
     */
    @NotBlank(message = "功率列表不能为空")
    private String powerList;

    /**
     * 电站列表(JSON字符串)
     */
    @NotBlank(message = "电站列表不能为空")
    private String stationList;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    private String planName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 语言
     */
    private String lang;

    /**
     * 记录ID（用于更新操作）
     */
    private Long id;

    /**
     * 状态（用于查询操作）
     */
    private Integer status;

    /**
     * 开始时间（用于查询操作）
     */
    private String startTime;

    /**
     * 结束时间（用于查询操作）
     */
    private String endTime;

    /**
     * 页大小（用于分页查询）
     */
    private Integer pageSize = 10;

    /**
     * 页码（用于分页查询）
     */
    private Integer pageNum = 1;
}

/**
 * 功率计划更新请求DTO
 */
@Data
class PowerPlanUpdateRequest extends PowerPlanRequest {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;
}

/**
 * 功率计划详情请求DTO
 */
@Data
class PowerPlanDetailRequest {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;
}

/**
 * 功率计划停止请求DTO
 */
@Data
class PowerPlanStopRequest {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;

    /**
     * 语言
     */
    private String lang;
}

/**
 * 功率计划删除请求DTO
 */
@Data
class PowerPlanDeleteRequest {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
}

/**
 * 功率计划列表查询请求DTO
 */
@Data
class PowerPlanListRequest {

    /**
     * 计划名称
     */
    private String name;

    /**
     * 状态: 1已保存；2已下发；3执行中，4已完成，5下发失败 6已停止
     */
    private Integer status;

    /**
     * 计划类型
     */
    private Integer planType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 语言
     */
    private String lang;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum = 1;
}

/**
 * 电站容量刷新请求DTO
 */
@Data
class PowerPlanStationsRefreshRequest {

    /**
     * 电站ID列表
     */
    @NotNull(message = "电站ID不能为空")
    private List<Long> stationId;
}
