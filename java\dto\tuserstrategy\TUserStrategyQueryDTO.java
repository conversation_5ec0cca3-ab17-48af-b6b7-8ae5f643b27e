package com.robestec.analysis.dto.tuserstrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户策略查询DTO
 */
@Data
@ApiModel("用户策略查询DTO")
public class TUserStrategyQueryDTO {

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("英文策略名称")
    private String enName;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("是否删除: 1-删除, 0-不删除")
    private Integer isDelete;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
