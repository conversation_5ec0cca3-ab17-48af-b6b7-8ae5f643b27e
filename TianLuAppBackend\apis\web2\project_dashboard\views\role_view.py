import datetime
import json
import uuid
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,)
import geocoder
from django.db.backends import mysql
from django.db.models import Q
from rest_framework.response import Response
from rest_framework.views import APIView
from dbutils.persistent_db import PersistentDB
import pymysql
from apis.user import models
from common import common_response_code
from serializers import user_serializers
from django.conf import settings
from django.db import transaction
from middlewares.authentications import JWTHeaderAuthentication, DenyAuthentication, JwtParamAuthentication

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
    "host": settings.DATABASES["default"]["HOST"],  # 数据库主机地址
    "user": settings.DATABASES["default"]["USER"],  # 数据库用户名
    "password": settings.DATABASES["default"]["PASSWORD"],  # 数据库密码
    "database": settings.DATABASES["default"]["NAME"],  # 数据库名称
    "port": settings.DATABASES["default"]["PORT"],
    "cursorclass": pymysql.cursors.DictCursor
})

class RoleViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''查询角色'''
    def post(self, request):
        role_name = request.data.get("role_name")
        type_ = request.data.get("type")
        query = Q()
        if role_name:
            query &= Q(role_name__icontains=role_name)
        if type_:
            query &= Q(type__icontains=type_)

        roles_ins = models.Role.objects.filter(query,is_used=1).values("id", "role_name", "remark", "type")

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": roles_ins},
            }
        )

class RolePermissionsViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''获取角色权限'''
    def post(self, request):
        try:
            ty = request.data.get("ty")  # 权限类型(0, "小程序"), (1, "Web")
            id = request.data.get("id")  # 角色id
            detail_list = []
            ty=int(ty)
            if ty==0:
                Permiss_ins = models.Permissions.objects.filter(ty=2).order_by('id')  # 获取全部权限
                roles_ins = models.Role.objects.get(id=id, is_used=1)  # 获取角色权限id
                roles_dic = {
                    'permissions': roles_ins.permissions.filter(ty=2),
                }
                related_permiss_name = list(roles_dic['permissions'].all())
                permissions_id = []
                for j in related_permiss_name:
                    permissions_id.append(j.id)
                for Permiss in Permiss_ins:
                    list_of_dicts = 0
                    for r in permissions_id:
                        if r == Permiss.id:
                            list_of_dicts = 1
                    detail = {}
                    detail["permiss_id"] = Permiss.id
                    detail["role_permiss"] = list_of_dicts
                    detail["title"] = Permiss.title
                    detail["url"] = Permiss.url
                    detail_list.append(detail)
            elif ty==1:
                Permiss_ins = models.Permissions.objects.filter(ty=ty).order_by('id')# 获取全部权限
                roles_ins= models.Role.objects.get(id=id, is_used=1)  # 获取角色权限id
                roles_dic = {
                    'permissions': roles_ins.permissions,
                }
                related_permiss_name = list(roles_dic['permissions'].all())
                permissions_id = []
                for j in related_permiss_name:
                    permissions_id.append(j.id)
                for Permiss in Permiss_ins:
                    list_of_dicts=0
                    for r in permissions_id:
                        dicts_ = {}
                        if r==Permiss.id:
                            list_of_dicts=1

                    if Permiss.parent_id != None:
                        for d in detail_list:
                            if int(Permiss.parent_id)==d['permiss_id']:
                                detail_1 = {}
                                detail_1["permiss_id"] = Permiss.id
                                detail_1["role_permiss"] = list_of_dicts
                                detail_1["title"] = Permiss.title
                                detail_1["url"] = Permiss.url
                                d["children"].append(detail_1)
                    else:
                        detail = {}
                        detail["permiss_id"] = Permiss.id
                        detail["role_permiss"] = list_of_dicts
                        detail["title"] = Permiss.title
                        detail["url"] = Permiss.url
                        detail["children"] = []
                        detail_list.append(detail)

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": detail_list},
            })
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到"},
            })

class RoleAddViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''添加角色'''
    @transaction.atomic
    def post(self, request):
        try:
        # if 1:
            data = request.data
            role_name = request.data.get("role_name")
            _type = json.loads(data.get('type',[]))  #(0, "小程序"), (1, "Web")
            remark = request.data.get("remark", '')
            for t in _type:
                role_name_ = models.Role.objects.filter(role_name=role_name,type=t).first()
            if role_name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "角色已存在！"},
                })
            for t in _type:
                Role_ins = models.Role.objects.create(
                    role_name=role_name,
                    type=t,
                    remark=remark if remark else '',
                )
                Role_ins.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "添加成功！"},
            })
        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "添加失败！"},
            })

class RolePermissionsAddViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''保存权限'''
    @transaction.atomic
    def post(self, request):
        try:
        # if 1:
            data = request.data
            role_id = request.data.get("role_id")#角色id
            ty = request.data.get("ty",1)#权限(0, "小程序"), (1, "Web")
            permiss_id = json.loads(data.get('permiss_id'))  # 权限id列表

            conn = pool.connection()
            cursor = conn.cursor()
            try:
                # 执行SQL查询
                sql = """DELETE FROM t_role_permissions WHERE role_id = {0}
                    """.format(int(role_id))
                cursor.execute(sql)
                conn.commit()
            except Exception as e:
                error_log.error(e)
            finally:
                try:
                    cursor.close()
                    conn.close()
                except:
                    pass


            role = models.Role.objects.get(id=role_id)
            list_permiss_id=[]
            for r in permiss_id:

                # web
                if ty == 1:
                    role__ = models.Permissions.objects.filter(id=r, ty=ty).values('parent_id')

                # 小程序v1.0==>v2.0:   ty == 1
                else:
                    role__ = models.Permissions.objects.filter(id=r, ty=2).values('parent_id')

                if role__:
                    if role__[0]['parent_id']:
                        list_permiss_id.append(int(role__[0]['parent_id']))

            permiss_id_list=sorted(set(permiss_id+list_permiss_id))
            for p in permiss_id_list:
                if p ==22:
                    role.permissions.add(22)
                    role.permissions.add(35)
                    role.permissions.add(36)
                    role.permissions.add(37)
                    role.permissions.add(38)
                    role.permissions.add(39)
                    role.permissions.add(40)
                    role.permissions.add(41)
                    role.permissions.add(42)
                else:
                    role.permissions.add(p)

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "保存成功！"},
            })
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "保存失败！"},
            })
        finally:
            try:
                cursor.close()
                conn.close()
            except Exception as e:
                pass


class RoleDeViews(APIView):#删除
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def post(self, request):
        p_id = request.data.get('id')
        if p_id:
            obj = models.Role.objects.get(id=p_id)
            obj.is_used = 2
            obj.save()
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "删除成功！"},
            })
        else:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "id为必填项"},
            })

class RoleInfoViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, *args, **kwargs):
        p_id = self.kwargs['id']#角色id
        try:
            role_ins = models.Role.objects.filter(role_name=p_id,is_used=1).values('type','remark')
        except:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到"},
            })
        list_ty=[]
        for r in role_ins:
            list_ty.append(r['type'])

        role_dic = {
            'role_name': p_id,
            'type': list_ty,
            'remark': role_ins[0]['remark']
        }
        return Response(role_dic)

    def put(self, request, *args, **kwargs):
        try:
            data = request.data
            p_id = data['id']
            role_name = request.data.get("role_name")
            _type = json.loads(data.get('type', []))  # (0, "小程序"), (1, "Web")
            remark = request.data.get("remark",'')

            name_ = models.Role.objects.filter(role_name=role_name).exclude(id=p_id).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "角色已存在！"},
                })

            # 修改角色表
            for t in _type:
                role_ins = models.Role.objects.get(id=p_id)
                role_ins.role_name = role_name
                role_ins.type = t
                role_ins.remark = remark if remark else ''
                role_ins.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "修改成功！"},
            })

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "修改失败！"},
            })

