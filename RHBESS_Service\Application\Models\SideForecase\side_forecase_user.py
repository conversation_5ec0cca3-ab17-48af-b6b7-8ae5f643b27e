#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:33:20
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_user.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 14:51:45


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_organization import ForecaseOrganization
from Application.Models.SideForecase.side_forecase_role import ForecaseRole
from Application.Models.SideForecase.side_forecase_group import ForecaseGroup
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseUser(user_Base):
    u'用户表'
    __tablename__ = "t_side_forecase_user"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(VARCHAR(256), nullable=False, comment="描述")
    head_img  = Column(VARCHAR(256), nullable=True, comment="头像")
    phone_no = Column(VARCHAR(13), nullable=True, comment="电话")
    account = Column(VARCHAR(256), nullable=False, comment="登录名")
    passwd = Column(VARCHAR(256), nullable=True, comment="密码")
    email = Column(VARCHAR(256), nullable=True, comment="邮箱")
    organization_id = Column(Integer, ForeignKey("t_side_forecase_organization.id"),nullable=False, comment="组织id")
    user_role_id = Column(Integer, ForeignKey("t_side_forecase_role.id"),nullable=False, comment="角色id")
    group_id = Column(Integer, ForeignKey("t_side_forecase_group.id"),nullable=False, comment="小组id")
    rank_id = Column(Integer, nullable=True,  comment="职级id")
    area_flag = Column(VARCHAR(50), nullable=True, comment="区域标识")
    op_ts = Column(DateTime, nullable=False, comment="时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment="是否使用1是0否")

    organization_group_user = relationship("ForecaseOrganization", backref="organization_group_user")
    forecase_role_user = relationship("ForecaseRole", backref="forecase_role_user")
    forecase_group_user = relationship("ForecaseGroup", backref="forecase_group_user")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'name':'%s','phone_no':'%s','account':'%s','email':'%s','user_role_id':'%s','organization_id':'%s','organization_descr':'%s','user_role_descr':'%s',\
            'group_id':'%s','group_descr':'%s','head_img':'%s','rank_id':'%s'}" %(self.id,self.name,self.phone_no,self.account,self.email,self.user_role_id,self.organization_id,self.organization_group_user.descr,
            self.forecase_role_user.name,self.group_id,self.forecase_group_user.name,self.head_img,self.rank_id)
        return bean.replace('None','')

   