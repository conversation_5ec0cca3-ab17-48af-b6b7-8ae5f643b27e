# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/6/18 上午9:52
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : CustomPriceSerialzier.py
# @Software : PyCharm
from rest_framework import serializers

from apis.user import models
from apis.user.models import MaterStation, UserDetails

class PeakValleySerializer(serializers.ModelSerializer):
    """削峰填谷电价序列化器"""

    type_display = serializers.SerializerMethodField()
    level_display = serializers.SerializerMethodField()
    province_id = serializers.SerializerMethodField()

    # province_name = serializers.CharField(source='province.name', read_only=True)
    # province_id = serializers.CharField(source='province.id', read_only=True)

    class Meta:
        model = models.PeakValleyNew
        fields = ["type_display", "level_display", "type", "level", "province_id"]

    def get_province_id(self, obj):
        return obj['province']

    def get_level_display(self, obj):
        LEVEL_CHOICE = dict(models.PeakValleyNew.LEVEL_CHOICE)
        return LEVEL_CHOICE.get(obj['level'], '')

    def get_type_display(self, obj):
        TYPE_CHOICE = dict(models.PeakValleyNew.TYPE_CHOICE)
        return TYPE_CHOICE.get(obj['type'], '')


class EnPeakValleySerializer(serializers.ModelSerializer):
    """削峰填谷电价序列化器"""

    type_display = serializers.SerializerMethodField()
    level_display = serializers.SerializerMethodField()
    province_id = serializers.SerializerMethodField()

    # province_name = serializers.CharField(source='province.name', read_only=True)
    # province_id = serializers.CharField(source='province.id', read_only=True)

    class Meta:
        model = models.PeakValleyNew
        fields = ["type_display", "level_display", "type", "level", "province_id"]

    def get_province_id(self, obj):
        lang = self.context.get("lang", 'zh')
        return obj['province']

    def get_level_display(self, obj):
        LEVEL_CHOICE = dict(models.PeakValleyNew.EN_LEVEL_CHOICE)
        return LEVEL_CHOICE.get(obj['level'], '')

    def get_type_display(self, obj):
        TYPE_CHOICE =  dict(models.PeakValleyNew.EN_TYPE_CHOICE)
        return TYPE_CHOICE.get(obj['type'], '')


class CustomizationDetailSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=True, write_only=True)
    # province_id = serializers.IntegerField(required=True, write_only=True)
    # level = serializers.IntegerField(required=True, write_only=True)
    # type = serializers.IntegerField(required=True, write_only=True)

    price_spike = serializers.SerializerMethodField()  # 尖峰
    price_peak = serializers.SerializerMethodField()  # 峰
    price_flat = serializers.SerializerMethodField()  # 平
    price_valley = serializers.SerializerMethodField()  # 谷
    price_dvalley = serializers.SerializerMethodField()  # 深谷

    class Meta:
        model = models.PeakValleyNew
        fields = "__all__"
        # extra_kwargs = {
        #     'level': {'required': True, 'write_only': True},
        #     'type': {'required': True, 'write_only': True},
        #     'moment': {'read_only': True},
        #     'price': {'read_only': True},
        #     'pv': {'read_only': True}
        # }

    # def get_price_spike(self, obj):
    #     price = None
    #     for i in range(0, 23):
    #         if price:
    #             return price
    #         if int(getattr(obj, f'pv{i}')) == 2:
    #             price = getattr(obj, f'h{i}')
    #     if not price:
    #         price = "___"
    #     return price

    # def get_price_peak(self, obj):
    #     price = None
    #     for i in range(0, 23):
    #         if price:
    #             return price
    #         if int(getattr(obj, f'pv{i}')) == 1:
    #             price = getattr(obj, f'h{i}')
    #     if not price:
    #         price = "___"
    #     return price
    #
    # def get_price_flat(self, obj):
    #     price = None
    #     for i in range(0, 23):
    #         if price:
    #             return price
    #         if int(getattr(obj, f'pv{i}')) == 0:
    #             price = getattr(obj, f'h{i}')
    #     if not price:
    #         price = "___"
    #     return price
    #
    # def get_price_valley(self, obj):
    #     price = None
    #     for i in range(0, 23):
    #         if price:
    #             return price
    #         if getattr(obj, f'pv{i}') == -1:
    #             price = getattr(obj, f'h{i}')
    #     if not price:
    #         price = "___"
    #     return price
    #
    # def get_price_dvalley(self, obj):
    #     price = None
    #     for i in range(0, 23):
    #         if price:
    #             return price
    #         if getattr(obj, f'pv{i}') == -2:
    #             price = getattr(obj, f'h{i}')
    #     if not price:
    #         price = "___"
    #     return price


class CustomizationAddSerializer(serializers.ModelSerializer):
    """
    单位电价添加
    """""

    stations_list = serializers.ListSerializer(child=serializers.IntegerField(), required=True)

    class Meta:
        model = models.UnitPrice
        exclude = ["user", "station"]
        extra_kwargs = {"uid": {"required": False}}

    # def create(self, validated_data):
    #     user = self.context.get('user_id')
    #     user_ins = models.UserDetails.objects.get(id=user)
    #     obj = models.UnitPrice.objects.create(user=user_ins, **validated_data)
    #     return obj

    def validate_name(self, value):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        exist = models.UnitPrice.objects.filter(name=value, delete=0).exists()
        if exist:
            raise Exception("单位电价名称已存在" if lang == 'zh' else 'Unit electricity price name already exists.')
        return value

    def validate(self, attrs):
        lang = self.context.get('lang', 'zh')
        start = attrs["start"]
        end = attrs["end"]
        name = attrs["name"]
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        if end < start:
            raise Exception("结束时间不能早于起始时间" if lang == 'zh' else 'The end time cannot be earlier than the start time.')

        if start == end:
            raise Exception("开始时间和结束时间不能选择同一天" if lang == 'zh' else 'The start time and end time cannot be on the same day.')

        for station_id in attrs["stations_list"]:
            tim_ins = models.UnitPrice.objects.filter(station=station_id, delete=0).all().values("start", 'end', "name")

            for tim in tim_ins:
                if tim["start"] <= start <= tim['end'] or tim['start'] <= end <= tim["end"]:
                    raise Exception(f"<{name}>时间段与已存在的<{tim['name']}>发生冲突" if lang == 'zh' else f"The time period of <{name}> conflicts with the existing <{tim['name']}>")

        return attrs


class CustomizationUpdateSerializer(serializers.ModelSerializer):
    """
    单位电价更新
    """""

    stations_list = serializers.ListSerializer(child=serializers.IntegerField(), required=False)

    class Meta:
        model = models.UnitPrice
        exclude = ['user', 'delete', 'delete_user_id', 'project', 'station', 'stations_name']
        # extra_kwargs = {"uid": {"required": False}}

    def validate(self, attrs):
        lang = self.context.get('lang', 'zh')
        uid = attrs['uid']
        start = attrs["start"]
        end = attrs["end"]
        name = attrs["name"]
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)

        # 校验名称是否已存在
        unit_prices = models.UnitPrice.objects.filter(name=name, delete=0).exclude(uid=uid).all()
        if unit_prices.exists():
            raise Exception("单位电价名称已存在" if lang == 'zh' else 'The name of the unit electricity price already exists.')

        # 校验时间
        if end < start:
            raise Exception("结束时间不能早于起始时间" if lang == 'zh' else 'The end time cannot be earlier than the start time.')

        if start == end:
            raise Exception("开始时间和结束时间不能选择同一天" if lang == 'zh' else 'The start time and end time cannot be on the same day.')

        for station_id in attrs["stations_list"]:
            tim_ins = (models.UnitPrice.objects.filter(station=station_id, delete=0).
                       exclude(uid=uid).all().values("start", 'end', "name"))
            for tim in tim_ins:
                if tim["start"] <= start <= tim['end'] or tim['start'] <= end <= tim["end"]:
                    raise Exception(f"<{name}>时间段与已存在的<{tim['name']}>发生冲突" if lang == 'zh' else f"The time period of <{name}> conflicts with the existing <{tim['name']}>")

        return attrs


class CustomizationDetSerializer(serializers.ModelSerializer):
    """
    单位电价详情
    """""

    class Meta:
        model = models.UnitPrice
        exclude = ['user', 'delete', 'delete_user_id', 'project', 'station']
        # extra_kwargs = {"uid": {"required": False}}
