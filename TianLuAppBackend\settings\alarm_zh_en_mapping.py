ALARM_ZH_EN_MAP = {
    "总告警": {
        "zh": "总告警",
        "en": "Common Alarm"
    },
    "告警": {
        "zh": "告警",
        "en": "Alarm"
    },
    "警告": {
        "zh": "警告",
        "en": "Warning"
    },
    "设备在线状态": {
        "zh": "设备在线状态",
        "en": "Equipment online status"
    },
    "离线": {
        "zh": "离线",
        "en": "off-line"
    },
    "在线": {
        "zh": "在线",
        "en": "on-line"
    },
    "变压器超限故障": {
        "zh": "变压器超限故障",
        "en": "Transformer over limit fault"
    },
    "已恢复": {
        "zh": "已恢复",
        "en": "Recovered"
    },
    "已 恢 复": {
        "zh": "已恢复",
        "en": "Recovered"
    },
    "已修复": {
        "zh": "已恢复",
        "en": "Recovered"
    },
    "未修复": {
        "zh": "未恢复",
        "en": "Not recovered"
    },
    "未恢复警告": {
        "zh": "未恢复警告",
        "en": "Warning not restored"
    },
    "组态自动化运行模式": {
        "zh": "组态自动化运行模式",
        "en": "Configuration automation operation mode"
    },
    "停止": {
        "zh": "停止",
        "en": "stop"
    },
    "运行": {
        "zh": "运行",
        "en": "function"
    },
    "组态计划调度模式": {
        "zh": "组态计划调度模式",
        "en": "Configuration plan scheduling mode"
    },
    "功能策略启停": {
        "zh": "功能策略启停",
        "en": "Function strategy start stop"
    },
    "接收云端调度": {
        "zh": "接收云端调度",
        "en": "Receive cloud scheduling"
    },
    "通道3 虚拟点 PCS停机状态": {
        "zh": "通道3 虚拟点 PCS停机状态",
        "en": "Channel 3 virtual point PCS shutdown status"
    },
    "通道3 虚拟点 PCS充电运行": {
        "zh": "通道3 虚拟点 PCS充电运行",
        "en": "Channel 3 virtual point PCS charging operation"
    },
    "通道3 虚拟点 PCS放电运行": {
        "zh": "通道3 虚拟点 PCS放电运行",
        "en": "Channel 3 virtual point PCS discharge operation"
    },
    "通道3 虚拟点 PCS运行": {
        "zh": "通道3 虚拟点 PCS运行",
        "en": "Channel 3 Virtual Point PCS Running"
    },
    "通道3 虚拟点 设备状态": {
        "zh": "通道3 虚拟点 设备状态",
        "en": "Channel 3 Virtual Point Device Status"
    },
    "组态自动化运行负荷跟随": {
        "zh": "组态自动化运行负荷跟随",
        "en": "Configuration automation operation load following"
    },
    "组态计划运行负荷跟随": {
        "zh": "组态计划运行负荷跟随",
        "en": "Configuration plan operation load following"
    },
    "云端负荷跟随调度": {
        "zh": "云端负荷跟随调度",
        "en": "Cloud load following scheduling"
    },
    "通道3 虚拟点 PCS待机状态": {
        "zh": "通道3 虚拟点 PCS待机状态",
        "en": "Channel 3 virtual point PCS standby state"
    },
    "通道3 虚拟点 PCS零功率运行": {
        "zh": "通道3 虚拟点 PCS零功率运行",
        "en": "Channel 3 virtual point PCS zero power operation"
    },
    "云端自动化模式": {
        "zh": "云端自动化模式",
        "en": "Cloud automation mode"
    },
    "网关机电池故障": {
        "zh": "网关机电池故障",
        "en": "Gateway machine battery failure"
    },
    "无故障": {
        "zh": "无故障",
        "en": "No malfunction"
    },
    "有故障": {
        "zh": "有故障",
        "en": "There is a malfunction"
    },
    "告警字储能电池极性反接故障": {
        "zh": "告警字储能电池极性反接故障",
        "en": "Warning word: reverse polarity fault of energy storage battery"
    },
    "未恢复报警": {
        "zh": "未恢复报警",
        "en": "Unresolved alarm"
    },
    "告警字储能电池电压异常": {
        "zh": "告警字储能电池电压异常",
        "en": "Warning word: abnormal voltage of energy storage battery"
    },
    "告警字直流侧半母线硬件过压": {
        "zh": "告警字直流侧半母线硬件过压",
        "en": "Alarm word: DC side half bus hardware overvoltage"
    },
    "告警字交流硬件过流": {
        "zh": "告警字交流硬件过流",
        "en": "Alarm word communication hardware overcurrent"
    },
    "告警字IGBT A 相故障": {
        "zh": "告警字IGBT A 相故障",
        "en": "Alarm word IGBT A-phase fault"
    },
    "告警字IGBT B 相故障": {
        "zh": "告警字IGBT B 相故障",
        "en": "Alarm word IGBT B-phase fault"
    },
    "告警字IGBT C 相故障": {
        "zh": "告警字IGBT C 相故障",
        "en": "Alarm word IGBT C-phase fault"
    },
    "告警字直流开关故障": {
        "zh": "告警字直流开关故障",
        "en": "Alarm word DC switch fault"
    },
    "告警字IGBT 风机故障": {
        "zh": "告警字IGBT 风机故障",
        "en": "Alarm word IGBT fan fault"
    },
    "告警字半母线电压偏差过大": {
        "zh": "告警字半母线电压偏差过大",
        "en": "Alarm word: Half bus voltage deviation too large"
    },
    "告警字输出缺相故障": {
        "zh": "告警字输出缺相故障",
        "en": "Alarm word output phase loss fault"
    },
    "告警字IGBT 模块 A 相过温": {
        "zh": "告警字IGBT 模块 A 相过温",
        "en": "Alarm word IGBT module phase A overheating"
    },
    "告警字IGBT 模块 B 相过温": {
        "zh": "告警字IGBT 模块 B 相过温",
        "en": "Alarm word IGBT module B phase overheating"
    },
    "告警字IGBT 模块 C 相过温": {
        "zh": "告警字IGBT 模块 C 相过温",
        "en": "Alarm word IGBT module C-phase overheating"
    },
    "告警字IGBT 模块 A 相低温": {
        "zh": "告警字IGBT 模块 A 相低温",
        "en": "Alarm word IGBT module A-phase low temperature"
    },
    "告警字IGBT 模块 B 相低温": {
        "zh": "告警字IGBT 模块 B 相低温",
        "en": "Alarm word IGBT module B-phase low temperature"
    },
    "告警字IGBT 模块 C 相低温": {
        "zh": "告警字IGBT 模块 C 相低温",
        "en": "Alarm word IGBT module C-phase low temperature"
    },
    "告警字绝缘电阻偏低": {
        "zh": "告警字绝缘电阻偏低",
        "en": "Alarm word: low insulation resistance"
    },
    "告警字BMS 通讯故障": {
        "zh": "告警字BMS 通讯故障",
        "en": "Alarm message BMS communication failure"
    },
    "告警字EMS 通讯故障": {
        "zh": "告警字EMS 通讯故障",
        "en": "Alarm word EMS communication failure"
    },
    "告警字BMS 系统故障": {
        "zh": "告警字BMS 系统故障",
        "en": "Alarm message BMS system malfunction"
    },
    "告警字急停告警": {
        "zh": "告警字急停告警",
        "en": "Alarm word emergency stop alarm"
    },
    "告警字防雷器故障": {
        "zh": "告警字防雷器故障",
        "en": "Warning word lightning arrester malfunction"
    },
    "告警字绝缘检测失败": {
        "zh": "告警字绝缘检测失败",
        "en": "Alarm word insulation detection failed"
    },
    "告警字并机通讯故障": {
        "zh": "告警字并机通讯故障",
        "en": "Alarm word parallel communication failure"
    },
    "告警字EEPROM 故障": {
        "zh": "告警字EEPROM 故障",
        "en": "Alarm word EEPROM fault"
    },
    "告警字SPI 故障": {
        "zh": "告警字SPI 故障",
        "en": "Alarm word SPI fault"
    },
    "告警字CBC 过流": {
        "zh": "告警字CBC 过流",
        "en": "Alarm word CBC overcurrent"
    },
    "告警字设备过载": {
        "zh": "告警字设备过载",
        "en": "Alarm word device overload"
    },
    "告警字电网电压反序": {
        "zh": "告警字电网电压反序",
        "en": "Alarm word: reverse sequence of grid voltage"
    },
    "告警字母线软启动失败": {
        "zh": "告警字母线软启动失败",
        "en": "Alarm letter line soft start failed"
    },
    "告警字交流侧脱扣器故障": {
        "zh": "告警字交流侧脱扣器故障",
        "en": "Alarm word: AC side release fault"
    },
    "告警字交流侧开关故障": {
        "zh": "告警字交流侧开关故障",
        "en": "Alarm word AC side switch malfunction"
    },
    "告警字载波同步故障": {
        "zh": "告警字载波同步故障",
        "en": "Alarm word carrier synchronization fault"
    },
    "告警字直流侧全母线软件过压": {
        "zh": "告警字直流侧全母线软件过压",
        "en": "Alarm word: DC side full bus software overvoltage"
    },
    "告警字直流侧全母线软件欠压": {
        "zh": "告警字直流侧全母线软件欠压",
        "en": "Alarm word: DC side full bus software undervoltage"
    },
    "告警字电网交流过压": {
        "zh": "告警字电网交流过压",
        "en": "Alarm word: AC overvoltage in the power grid"
    },
    "告警字电网交流欠压": {
        "zh": "告警字电网交流欠压",
        "en": "Alarm word: AC undervoltage in the power grid"
    },
    "告警字离网交流过压": {
        "zh": "告警字离网交流过压",
        "en": "Alarm message: Off grid AC overvoltage"
    },
    "告警字离网交流欠压": {
        "zh": "告警字离网交流欠压",
        "en": "Alarm message: Off grid AC undervoltage"
    },
    "告警字电网交流过频": {
        "zh": "告警字电网交流过频",
        "en": "Alarm word: AC over frequency in the power grid"
    },
    "告警字电网交流欠频": {
        "zh": "告警字电网交流欠频",
        "en": "Alarm word: AC underfrequency in the power grid"
    },
    "告警字环境过温": {
        "zh": "告警字环境过温",
        "en": "Warning word: Environment overheating"
    },
    "告警字环境低温": {
        "zh": "告警字环境低温",
        "en": "Warning word: Low temperature environment"
    },
    "告警字电池电压过高": {
        "zh": "告警字电池电压过高",
        "en": "Warning word: Battery voltage too high"
    },
    "告警字电池电压过低": {
        "zh": "告警字电池电压过低",
        "en": "Warning word: Low battery voltage"
    },
    "告警字电池欠压告警": {
        "zh": "告警字电池欠压告警",
        "en": "Alarm word: Battery undervoltage alarm"
    },
    "告警字交流侧 A 相霍尔断线（+15V）": {
        "zh": "告警字交流侧 A 相霍尔断线（+15V）",
        "en": "Alarm word: AC side A-phase Hall disconnection (+15V)"
    },
    "告警字交流侧 A 相霍尔断线（-15V）": {
        "zh": "告警字交流侧 A 相霍尔断线（-15V）",
        "en": "Alarm word: AC side A-phase Hall disconnection (-15V)"
    },
    "告警字交流侧 A 相霍尔断线（IR）": {
        "zh": "告警字交流侧 A 相霍尔断线（IR）",
        "en": "Alarm word AC side A-phase Hall disconnection (IR)"
    },
    "告警字交流侧 A 相霍尔断线（GND）": {
        "zh": "告警字交流侧 A 相霍尔断线（GND）",
        "en": "Alarm word: AC side A-phase Hall disconnection (GND)"
    },
    "告警字交流侧 B 相电流霍尔断线（+15V）": {
        "zh": "告警字交流侧 B 相电流霍尔断线（+15V）",
        "en": "Alarm word: AC side B-phase current Hall break (+15V)"
    },
    "告警字交流侧 B 相霍尔断线（-15V）": {
        "zh": "告警字交流侧 B 相霍尔断线（-15V）",
        "en": "Alarm word: AC side B-phase Hall disconnection (-15V)"
    },
    "告警字交流侧 B 相霍尔断线（IR）": {
        "zh": "告警字交流侧 B 相霍尔断线（IR）",
        "en": "Alarm word: AC side B-phase Hall break (IR)"
    },
    "告警字交流侧 B 相霍尔断线（GND）": {
        "zh": "告警字交流侧 B 相霍尔断线（GND）",
        "en": "Alarm word: AC side B-phase Hall disconnection (GND)"
    },
    "告警字交流侧 C 相电流霍尔断线（+15V）": {
        "zh": "告警字交流侧 C 相电流霍尔断线（+15V）",
        "en": "Alarm word: AC side C-phase current Hall break (+15V)"
    },
    "告警字交流侧 C 相霍尔断线（-15V）": {
        "zh": "告警字交流侧 C 相霍尔断线（-15V）",
        "en": "Alarm word: AC side C-phase Hall disconnection (-15V)"
    },
    "告警字交流侧 C 相霍尔断线（IR）": {
        "zh": "告警字交流侧 C 相霍尔断线（IR）",
        "en": "Alarm word: AC side C-phase Hall break (IR)"
    },
    "告警字交流侧 C 相霍尔断线（GND）": {
        "zh": "告警字交流侧 C 相霍尔断线（GND）",
        "en": "Alarm word: AC side C-phase Hall disconnection (GND)"
    },
    "告警字直流侧电流霍尔断线（+15V）": {
        "zh": "告警字直流侧电流霍尔断线（+15V）",
        "en": "Alarm word: DC side current Hall break (+15V)"
    },
    "告警字直流侧霍尔断线（-15V）": {
        "zh": "告警字直流侧霍尔断线（-15V）",
        "en": "Alarm word: DC side Hall disconnection (-15V)"
    },
    "告警字直流侧霍尔断线（IR）": {
        "zh": "告警字直流侧霍尔断线（IR）",
        "en": "Alarm word: DC side Hall break (IR)"
    },
    "告警字直流侧霍尔断线（GND）": {
        "zh": "告警字直流侧霍尔断线（GND）",
        "en": "Alarm word: DC side Hall disconnection (GND)"
    },
    "告警字电流控制偏差过大": {
        "zh": "告警字电流控制偏差过大",
        "en": "Alarm word current control deviation is too large"
    },
    "告警字电网电压不平衡": {
        "zh": "告警字电网电压不平衡",
        "en": "Alarm word: unbalanced power grid voltage"
    },
    "告警字交流侧电流直流分量超限": {
        "zh": "告警字交流侧电流直流分量超限",
        "en": "Alarm word: AC side current DC component exceeds the limit"
    },
    "告警字交流开关合闸电压不匹配": {
        "zh": "告警字交流开关合闸电压不匹配",
        "en": "Alarm word: AC switch closing voltage mismatch"
    },
    "告警字AD 零漂过大": {
        "zh": "告警字AD 零漂过大",
        "en": "Alarm word AD zero drift too large"
    },
    "告警字HMI 通讯故障": {
        "zh": "告警字HMI 通讯故障",
        "en": "Alarm word HMI communication failure"
    },
    "告警字EMS 连接超时 1（外部总线）": {
        "zh": "告警字EMS 连接超时 1（外部总线）",
        "en": "Alarm word EMS connection timeout 1 (external bus)"
    },
    "告警字物联网通讯故障": {
        "zh": "告警字物联网通讯故障",
        "en": "Warning word: IoT communication failure"
    },
    "告警字客户后台通讯故障": {
        "zh": "告警字客户后台通讯故障",
        "en": "Alarm message: Customer backend communication failure"
    },
    "告警字EMS 连接超时 1（内部通讯）": {
        "zh": "告警字EMS 连接超时 1（内部通讯）",
        "en": "Alarm word EMS connection timeout 1 (internal communication)"
    },
    "告警字EMS 连接超时 2（外部总线）": {
        "zh": "告警字EMS 连接超时 2（外部总线）",
        "en": "Alarm word EMS connection timeout 2 (external bus)"
    },
    "告警字EMS 连接超时 2（内部通讯）": {
        "zh": "告警字EMS 连接超时 2（内部通讯）",
        "en": "Alarm word EMS connection timeout 2 (internal communication)"
    },
    "告警字产品版本号不匹配": {
        "zh": "告警字产品版本号不匹配",
        "en": "Alarm word product version number mismatch"
    },
    "告警字检测板型号不匹配": {
        "zh": "告警字检测板型号不匹配",
        "en": "Alarm word detection board model mismatch"
    },
    "告警字RTC 故障": {
        "zh": "告警字RTC 故障",
        "en": "Alarm word RTC fault"
    },
    "告警字黑匣子存储故障": {
        "zh": "告警字黑匣子存储故障",
        "en": "Alarm word black box storage failure"
    },
    "告警字电源盒检测异常": {
        "zh": "告警字电源盒检测异常",
        "en": "Alarm word power box detection abnormality"
    },
    "告警字主机异常停机": {
        "zh": "告警字主机异常停机",
        "en": "Alarm word: Host abnormal shutdown"
    },
    "告警字锁相异常": {
        "zh": "告警字锁相异常",
        "en": "Alarm word lock abnormal"
    },
    "告警字直流侧中点采样断线": {
        "zh": "告警字直流侧中点采样断线",
        "en": "Alarm word: DC side midpoint sampling disconnected"
    },
    "告警字不满足并网运行条件": {
        "zh": "告警字不满足并网运行条件",
        "en": "The alarm message does not meet the conditions for grid connected operation"
    },
    "PCS 故障状态": {
        "zh": "PCS 故障状态 ",
        "en": "PCS fault status"
    },
    "未恢复故障": {
        "zh": "未恢复故障",
        "en": "Unresolved fault"
    },
    "故障字储能电池极性反接故障": {
        "zh": "故障字储能电池极性反接故障",
        "en": "Fault word: reverse polarity fault of energy storage battery"
    },
    "故障字储能电池电压异常": {
        "zh": "故障字储能电池电压异常",
        "en": "Fault word: abnormal voltage of energy storage battery"
    },
    "故障字直流侧半母线硬件过压": {
        "zh": "故障字直流侧半母线硬件过压",
        "en": "Fault word: DC side half bus hardware overvoltage"
    },
    "故障字交流硬件过流": {
        "zh": "故障字交流硬件过流",
        "en": "Fault message communication hardware overcurrent"
    },
    "故障字IGBT A 相故障": {
        "zh": "故障字IGBT A 相故障",
        "en": "Fault word IGBT A-phase fault"
    },
    "故障字IGBT B 相故障": {
        "zh": "故障字IGBT B 相故障",
        "en": "Fault word IGBT B-phase fault"
    },
    "故障字IGBT C 相故障": {
        "zh": "故障字IGBT C 相故障",
        "en": "Fault word IGBT C-phase fault"
    },
    "故障字直流开关故障": {
        "zh": "故障字直流开关故障",
        "en": "Fault word: DC switch fault"
    },
    "故障字IGBT 风机故障": {
        "zh": "故障字IGBT 风机故障",
        "en": "Fault word IGBT fan fault"
    },
    "故障字直流侧全母线软件过压": {
        "zh": "故障字直流侧全母线软件过压",
        "en": "Fault word: DC side full bus software overvoltage"
    },
    "故障字直流侧全母线软件欠压": {
        "zh": "故障字直流侧全母线软件欠压",
        "en": "Fault word: DC side full bus software undervoltage"
    },
    "故障字半母线电压偏差过大": {
        "zh": "故障字半母线电压偏差过大",
        "en": "Fault word: Half bus voltage deviation too large"
    },
    "故障字电网交流过压": {
        "zh": "故障字电网交流过压",
        "en": "Fault word: AC overvoltage in the power grid"
    },
    "故障字电网交流欠压": {
        "zh": "故障字电网交流欠压",
        "en": "Fault word: AC undervoltage in the power grid"
    },
    "故障字离网交流过压": {
        "zh": "故障字离网交流过压",
        "en": "Fault message: Off grid AC overvoltage"
    },
    "故障字离网交流欠压": {
        "zh": "故障字离网交流欠压",
        "en": "Fault message: Off grid AC undervoltage"
    },
    "故障字电网交流过频": {
        "zh": "故障字电网交流过频",
        "en": "Fault word: AC overclocking in the power grid"
    },
    "故障字电网交流欠频": {
        "zh": "故障字电网交流欠频",
        "en": "Fault word: AC underfrequency in the power grid"
    },
    "故障字输出缺相故障": {
        "zh": "故障字输出缺相故障",
        "en": "Fault word output phase loss fault"
    },
    "故障字环境过温": {
        "zh": "故障字环境过温",
        "en": "Fault word: excessive temperature in the environment"
    },
    "故障字环境低温": {
        "zh": "故障字环境低温",
        "en": "Fault word: Low temperature environment"
    },
    "故障字IGBT 模块 A 相过温": {
        "zh": "故障字IGBT 模块 A 相过温",
        "en": "Fault IGBT module phase A overheating"
    },
    "故障字IGBT 模块 B 相过温": {
        "zh": "故障字IGBT 模块 B 相过温",
        "en": "Fault IGBT module B phase overheating"
    },
    "故障字IGBT 模块 C 相过温": {
        "zh": "故障字IGBT 模块 C 相过温",
        "en": "Fault IGBT module C-phase overheating"
    },
    "故障字IGBT 模块 A 相低温": {
        "zh": "故障字IGBT 模块 A 相低温",
        "en": "Fault IGBT module A-phase low temperature"
    },
    "故障字IGBT 模块 B 相低温": {
        "zh": "故障字IGBT 模块 B 相低温",
        "en": "Fault IGBT module B-phase low temperature"
    },
    "故障字IGBT 模块 C 相低温": {
        "zh": "故障字IGBT 模块 C 相低温",
        "en": "Fault IGBT module C-phase low temperature"
    },
    "故障字电池电压过低": {
        "zh": "故障字电池电压过低",
        "en": "Fault word: Low battery voltage"
    },
    "故障字BMS 系统故障": {
        "zh": "故障字BMS 系统故障",
        "en": "Fault message: BMS system malfunction"
    },
    "故障字交流侧 A 相霍尔断线（+15V）": {
        "zh": "故障字交流侧 A 相霍尔断线（+15V）",
        "en": "Fault word: AC side A-phase Hall disconnection (+15V)"
    },
    "故障字交流侧 A 相霍尔断线（-15V）": {
        "zh": "故障字交流侧 A 相霍尔断线（-15V）",
        "en": "Fault word: AC side A-phase Hall disconnection (-15V)"
    },
    "故障字交流侧 A 相霍尔断线（IR）": {
        "zh": "故障字交流侧 A 相霍尔断线（IR）",
        "en": "Fault word: AC side A-phase Hall break (IR)"
    },
    "故障字交流侧 A 相霍尔断线（GND）": {
        "zh": "故障字交流侧 A 相霍尔断线（GND）",
        "en": "Fault word: AC side A-phase Hall disconnection (GND)"
    },
    "故障字交流侧 B 相电流霍尔断线（+15V）": {
        "zh": "故障字交流侧 B 相电流霍尔断线（+15V）",
        "en": "Fault word: AC side B-phase current Hall break (+15V)"
    },
    "故障字交流侧 B 相霍尔断线（-15V）": {
        "zh": "故障字交流侧 B 相霍尔断线（-15V）",
        "en": "Fault word: AC side B-phase Hall break (-15V)"
    },
    "故障字交流侧 B 相霍尔断线（IR）": {
        "zh": "故障字交流侧 B 相霍尔断线（IR）",
        "en": "Fault word: AC side B-phase Hall break (IR)"
    },
    "故障字交流侧 B 相霍尔断线（GND）": {
        "zh": "故障字交流侧 B 相霍尔断线（GND）",
        "en": "Fault word: AC side B-phase Hall disconnection (GND)"
    },
    "故障字交流侧 C 相电流霍尔断线（+15V）": {
        "zh": "故障字交流侧 C 相电流霍尔断线（+15V）",
        "en": "Fault word: AC side C-phase current Hall break (+15V)"
    },
    "故障字交流侧 C 相霍尔断线（-15V）": {
        "zh": "故障字交流侧 C 相霍尔断线（-15V）",
        "en": "Fault word: AC side C-phase Hall break (-15V)"
    },
    "故障字交流侧 C 相霍尔断线（IR）": {
        "zh": "故障字交流侧 C 相霍尔断线（IR）",
        "en": "Fault word: AC side C-phase Hall break (IR)"
    },
    "故障字交流侧 C 相霍尔断线（GND）": {
        "zh": "故障字交流侧 C 相霍尔断线（GND）",
        "en": "Fault word: AC side C-phase Hall disconnection (GND)"
    },
    "故障字直流侧电流霍尔断线（+15V）": {
        "zh": "故障字直流侧电流霍尔断线（+15V）",
        "en": "Fault word: DC side current Hall break (+15V)"
    },
    "故障字直流侧霍尔断线（-15V）": {
        "zh": "故障字直流侧霍尔断线（-15V）",
        "en": "Fault word: DC side Hall disconnection (-15V)"
    },
    "故障字直流侧霍尔断线（IR）": {
        "zh": "故障字直流侧霍尔断线（IR）",
        "en": "Fault word: DC side Hall break (IR)"
    },
    "故障字直流侧霍尔断线（GND）": {
        "zh": "故障字直流侧霍尔断线（GND）",
        "en": "Fault word: DC side Hall disconnection (GND)"
    },
    "故障字防雷器故障": {
        "zh": "故障字防雷器故障",
        "en": "Fault word lightning arrester malfunction"
    },
    "故障字电流控制偏差过大": {
        "zh": "故障字电流控制偏差过大",
        "en": "The current control deviation of the fault word is too large"
    },
    "故障字电网电压不平衡": {
        "zh": "故障字电网电压不平衡",
        "en": "Fault word: unbalanced voltage in the power grid"
    },
    "故障字交流侧电流直流分量超限": {
        "zh": "故障字交流侧电流直流分量超限",
        "en": "Fault word: AC side current DC component exceeds the limit"
    },
    "故障字交流开关合闸电压不匹配": {
        "zh": "故障字交流开关合闸电压不匹配",
        "en": "Fault word: AC switch closing voltage mismatch"
    },
    "故障字AD 零漂过大": {
        "zh": "故障字AD 零漂过大",
        "en": "Fault word AD zero drift too large"
    },
    "故障字绝缘电阻偏低": {
        "zh": "故障字绝缘电阻偏低",
        "en": "Fault word: low insulation resistance"
    },
    "故障字绝缘检测失败": {
        "zh": "故障字绝缘检测失败",
        "en": "Fault word insulation detection failed"
    },
    "故障字HMI 通讯故障": {
        "zh": "故障字HMI 通讯故障",
        "en": "Fault message HMI communication failure"
    },
    "故障字BMS 通讯故障": {
        "zh": "故障字BMS 通讯故障",
        "en": "Fault message BMS communication failure"
    },
    "故障字EMS 连接超时 1（外部总线）": {
        "zh": "故障字EMS 连接超时 1（外部总线）",
        "en": "Fault word EMS connection timeout 1 (external bus)"
    },
    "故障字并机通讯故障": {
        "zh": "故障字并机通讯故障",
        "en": "Fault message parallel communication failure"
    },
    "故障字EEPROM 故障": {
        "zh": "故障字EEPROM 故障",
        "en": "Fault word EEPROM malfunction"
    },
    "故障字SPI 故障": {
        "zh": "故障字SPI 故障",
        "en": "Fault word SPI fault"
    },
    "故障字物联网通讯故障": {
        "zh": "故障字物联网通讯故障",
        "en": "Fault word: IoT communication failure"
    },
    "故障字客户后台通讯故障": {
        "zh": "故障字客户后台通讯故障",
        "en": "Fault message: Customer backend communication failure"
    },
    "故障字CBC 过流": {
        "zh": "故障字CBC 过流",
        "en": "Fault word CBC overcurrent"
    },
    "故障字设备过载": {
        "zh": "故障字设备过载",
        "en": "Fault word device overload"
    },
    "故障字电网电压反序": {
        "zh": "故障字电网电压反序",
        "en": "Fault word: reverse sequence of grid voltage"
    },
    "故障字母线软启动失败": {
        "zh": "故障字母线软启动失败",
        "en": "Fault letter line soft start failed"
    },
    "故障字交流侧脱扣器故障": {
        "zh": "故障字交流侧脱扣器故障",
        "en": "Fault word: AC side release fault"
    },
    "故障字交流侧开关故障": {
        "zh": "故障字交流侧开关故障",
        "en": "Fault word: AC side switch malfunction"
    },
    "故障字载波同步故障": {
        "zh": "故障字载波同步故障",
        "en": "Fault word carrier synchronization fault"
    },
    "故障字EMS 连接超时 1（内部通讯）": {
        "zh": "故障字EMS 连接超时 1（内部通讯）",
        "en": "Fault word EMS connection timeout 1 (internal communication)"
    },
    "故障字EMS 连接超时 2（外部总线）": {
        "zh": "故障字EMS 连接超时 2（外部总线）",
        "en": "Fault word EMS connection timeout 2 (external bus)"
    },
    "故障字EMS 连接超时 2（内部通讯）": {
        "zh": "故障字EMS 连接超时 2（内部通讯）",
        "en": "Fault word EMS connection timeout 2 (internal communication)"
    },
    "故障字EMS 通讯故障": {
        "zh": "故障字EMS 通讯故障",
        "en": "Fault word EMS communication failure"
    },
    "故障字产品版本号不匹配": {
        "zh": "故障字产品版本号不匹配",
        "en": "Fault word product version number mismatch"
    },
    "故障字检测板型号不匹配": {
        "zh": "故障字检测板型号不匹配",
        "en": "The model of the fault detection board does not match"
    },
    "故障字RTC 故障": {
        "zh": "故障字RTC 故障",
        "en": "Fault word RTC fault"
    },
    "故障字电池电压过高": {
        "zh": "故障字电池电压过高",
        "en": "Fault word: High battery voltage"
    },
    "故障字黑匣子存储故障": {
        "zh": "故障字黑匣子存储故障",
        "en": "Fault word black box storage failure"
    },
    "故障字电源盒检测异常": {
        "zh": "故障字电源盒检测异常",
        "en": "Fault word power box detection abnormality"
    },
    "故障字主机异常停机": {
        "zh": "故障字主机异常停机",
        "en": "Fault word: Host abnormal shutdown"
    },
    "故障字锁相异常": {
        "zh": "故障字锁相异常",
        "en": "Fault word lock abnormality"
    },
    "故障字直流侧中点采样断线": {
        "zh": "故障字直流侧中点采样断线",
        "en": "Fault word: DC side midpoint sampling disconnected"
    },
    "直流接触器状态": {
        "zh": "直流接触器状态",
        "en": "DC contactor status"
    },
    "断开": {
        "zh": "断开",
        "en": "off"
    },
    "闭合": {
        "zh": "闭合",
        "en": "close"
    },
    "急停按钮状态": {
        "zh": "急停按钮状态",
        "en": "Emergency stop button status"
    },
    "正常": {
        "zh": "正常",
        "en": "normal"
    },
    "急停": {
        "zh": "急停",
        "en": "Emergency stop"
    },
    "IGBT 风机状态": {
        "zh": "IGBT 风机状态 ",
        "en": "IGBT fan status"
    },
    "停转": {
        "zh": "停转",
        "en": "Stop spinning"
    },
    "绝缘检测接触器状态": {
        "zh": "绝缘检测接触器状态",
        "en": "Insulation detection contactor status"
    },
    "防雷器状态": {
        "zh": "防雷器状态 ",
        "en": "Status of lightning arrester"
    },
    "故障": {
        "zh": "故障",
        "en": "fault"
    },
    "PCS与EMS 通讯状态": {
        "zh": "PCS与EMS 通讯状态",
        "en": "PCS and EMS communication status"
    },
    "中断": {
        "zh": "中断",
        "en": "interrupt"
    },
    "降额运行": {
        "zh": "降额运行 ",
        "en": "Reduced operation"
    },
    "未降额": {
        "zh": "未降额",
        "en": "Unreduced amount"
    },
    "间隔": {
        "zh": "间隔",
        "en": "interval"
    },
    "PCS 开关": {
        "zh": "PCS 开关",
        "en": "PCS switch"
    },
    "停机": {
        "zh": "停机",
        "en": "Shutdown"
    },
    "一级故障1总压过压": {
        "zh": "一级故障1总压过压",
        "en": "Level 1 fault: Total pressure overvoltage"
    },
    "一级故障2总压欠压": {
        "zh": "一级故障2总压欠压",
        "en": "Level 1 Fault 2: Total Voltage Undervoltage"
    },
    "一级故障3单体过压": {
        "zh": "一级故障3单体过压",
        "en": "Level 1 fault 3: Overvoltage of a single unit"
    },
    "一级故障4单体欠压": {
        "zh": "一级故障4单体欠压",
        "en": "Level 1 fault 4: Under voltage of individual unit"
    },
    "一级故障5单体过温": {
        "zh": "一级故障5单体过温",
        "en": "Level 1 fault: Unit 5 overheating"
    },
    "一级故障6单体低温": {
        "zh": "一级故障6单体低温",
        "en": "Level 1 fault 6: Low temperature of individual unit"
    },
    "一级故障7压差过大": {
        "zh": "一级故障7压差过大",
        "en": "Level 1 fault 7: Excessive pressure difference"
    },
    "一级故障8温差过大": {
        "zh": "一级故障8温差过大",
        "en": "Level 1 fault 8: Excessive temperature difference"
    },
    "一级故障9电流过大": {
        "zh": "一级故障9电流过大",
        "en": "Level 1 fault 9: Excessive current"
    },
    "一级故障10高压异常": {
        "zh": "一级故障10高压异常",
        "en": "Level 1 Fault 10 High Voltage Abnormality"
    },
    "一级故障11主从通讯": {
        "zh": "一级故障11主从通讯",
        "en": "Level 1 fault 11 master-slave communication"
    },
    "一级故障12单体电压排线": {
        "zh": "一级故障12单体电压排线",
        "en": "Level 1 fault 12 individual voltage cable"
    },
    "一级故障13单体": {
        "zh": "一级故障13单体",
        "en": "Level 1 fault 13 individual units"
    },
    "一级故障15SOC过低": {
        "zh": "一级故障15SOC过低",
        "en": "Level 1 fault: 15SOC too low"
    },
    "一级故障16绝缘漏电": {
        "zh": "一级故障16绝缘漏电",
        "en": "Level 1 fault 16 insulation leakage"
    },
    "一级故障17粘连": {
        "zh": "一级故障17粘连",
        "en": "Level 1 fault 17 adhesion"
    },
    "一级故障18预充状态": {
        "zh": "一级故障18预充状态",
        "en": "Level 1 Fault 18 Pre charge Status"
    },
    "一级故障19供电过高": {
        "zh": "一级故障19供电过高",
        "en": "Level 1 fault 19: High power supply"
    },
    "一级故障20供电过低": {
        "zh": "一级故障20供电过低",
        "en": "Level 1 fault 20: Low power supply"
    },
    "一级故障21T1高温": {
        "zh": "一级故障21T1高温",
        "en": "First level fault 21T1 high temperature"
    },
    "一级故障22T1低温": {
        "zh": "一级故障22T1低温",
        "en": "Level 1 fault 22T1 low temperature"
    },
    "一级故障23T2高温": {
        "zh": "一级故障23T2高温",
        "en": "Level 1 fault 23T2 high temperature"
    },
    "一级故障24T2低温": {
        "zh": "一级故障24T2低温",
        "en": "Level 1 fault 24T2 low temperature"
    },
    "一级故障27MSD故障": {
        "zh": "一级故障27MSD故障",
        "en": "Level 1 fault 27MSD fault"
    },
    "一级故障28电流异常": {
        "zh": "一级故障28电流异常",
        "en": "Level 1 fault 28 abnormal current"
    },
    "一级故障29极柱高温": {
        "zh": "一级故障29极柱高温",
        "en": "Level 1 fault 29 pole high temperature"
    },
    "一级故障32PCS故障状态": {
        "zh": "一级故障32PCS故障状态",
        "en": "Level 1 fault: 32PCS fault status"
    },
    "一级故障33EMS故障状态": {
        "zh": "一级故障33EMS故障状态",
        "en": "Level 1 fault 33EMS fault status"
    },
    "一级故障34保险丝状态": {
        "zh": "一级故障34保险丝状态",
        "en": "Level 1 fault 34 fuse status"
    },
    "一级故障40反馈异常": {
        "zh": "一级故障40反馈异常",
        "en": "Level 1 fault 40 feedback abnormality"
    },
    "一级故障42极限故障": {
        "zh": "一级故障42极限故障",
        "en": "Level 1 fault 42 extreme fault"
    },
    "一级故障43开路": {
        "zh": "一级故障43开路",
        "en": "Level 1 fault 43 open circuit"
    },
    "一级故障44急停": {
        "zh": "一级故障44急停",
        "en": "Level 1 fault 44 emergency stop"
    },
    "一级故障45消防火警": {
        "zh": "一级故障45消防火警",
        "en": "Level 1 fault 45 fire alarm"
    },
    "一级故障46控制指令超时": {
        "zh": "一级故障46控制指令超时",
        "en": "Level 1 fault 46: Control command timeout"
    },
    "一级故障49电池箱过压": {
        "zh": "一级故障49电池箱过压",
        "en": "Level 1 fault 49: Battery box overvoltage"
    },
    "一级故障50电池箱欠压": {
        "zh": "一级故障50电池箱欠压",
        "en": "Level 1 fault: 50 battery box undervoltage"
    },
    "一级故障52水冷机通讯故障": {
        "zh": "一级故障52水冷机通讯故障",
        "en": "Level 1 fault 52: Communication failure of water cooler"
    },
    "一级故障54水浸故障": {
        "zh": "一级故障54水浸故障",
        "en": "Level 1 fault 54 water immersion fault"
    },
    "一级故障消防喷洒": {
        "zh": "一级故障消防喷洒",
        "en": "First level fault fire sprinkler"
    },
    "一级故障消防设备故障": {
        "zh": "一级故障消防设备故障",
        "en": "Level 1 fire equipment malfunction"
    },
    "一级故障59水冷机自身故障": {
        "zh": "一级故障59水冷机自身故障",
        "en": "Level 1 fault 59: Self malfunction of water cooler"
    },
    "一级故障60电表通讯故障": {
        "zh": "一级故障60电表通讯故障",
        "en": "Level 1 fault: 60 meter communication failure"
    },
    "二级故障1总压过压": {
        "zh": "二级故障1总压过压",
        "en": "Level 2 fault 1: Total pressure overvoltage"
    },
    "二级故障2总压欠压": {
        "zh": "二级故障2总压欠压",
        "en": "Level 2 Fault 2: Total Voltage Undervoltage"
    },
    "二级故障3单体过压": {
        "zh": "二级故障3单体过压",
        "en": "Level 2 fault 3: Overvoltage of a single unit"
    },
    "二级故障4单体欠压": {
        "zh": "二级故障4单体欠压",
        "en": "Level 2 fault 4: Under voltage of individual unit"
    },
    "二级故障5单体过温": {
        "zh": "二级故障5单体过温",
        "en": "Level 2 fault 5: Unit overheating"
    },
    "二级故障6单体低温": {
        "zh": "二级故障6单体低温",
        "en": "Level 2 fault 6: Low temperature of individual unit"
    },
    "二级故障7压差过大": {
        "zh": "二级故障7压差过大",
        "en": "Level 2 fault 7: Excessive pressure difference"
    },
    "二级故障8温差过大": {
        "zh": "二级故障8温差过大",
        "en": "Level 2 fault 8: Excessive temperature difference"
    },
    "二级故障15SOC过低": {
        "zh": "二级故障15SOC过低",
        "en": "Level 2 fault: 15SOC too low"
    },
    "二级故障49电池箱过压": {
        "zh": "二级故障49电池箱过压",
        "en": "Level 2 fault 49: Battery box overvoltage"
    },
    "二级故障50电池箱欠压": {
        "zh": "二级故障50电池箱欠压",
        "en": "Level 2 fault: 50 battery box undervoltage"
    },
    "二级故障52水冷机通讯故障": {
        "zh": "二级故障52水冷机通讯故障",
        "en": "Level 2 fault 52: Communication failure of water cooler"
    },
    "二级故障54水浸故障": {
        "zh": "二级故障54水浸故障",
        "en": "Level 2 fault 54 water immersion fault"
    },
    "总故障": {
        "zh": "总故障",
        "en": "Total malfunction"
    },
    "三级故障1总压过压": {
        "zh": "三级故障1总压过压",
        "en": "Level 3 fault 1: Total pressure overvoltage"
    },
    "三级故障2总压欠压": {
        "zh": "三级故障2总压欠压",
        "en": "Level 3 Fault 2: Total Voltage Undervoltage"
    },
    "三级故障3单体过压": {
        "zh": "三级故障3单体过压",
        "en": "Level 3 fault 3: Overvoltage of individual units"
    },
    "三级故障4单体欠压": {
        "zh": "三级故障4单体欠压",
        "en": "Level 3 fault 4: Under voltage of individual unit"
    },
    "三级故障5单体过温": {
        "zh": "三级故障5单体过温",
        "en": "Level 3 fault: Unit 5 overheating"
    },
    "三级故障6单体低温": {
        "zh": "三级故障6单体低温",
        "en": "Level 3 fault 6: Low temperature of individual unit"
    },
    "三级故障7压差过大": {
        "zh": "三级故障7压差过大",
        "en": "Level 3 fault 7: Excessive pressure difference"
    },
    "三级故障8温差过大": {
        "zh": "三级故障8温差过大",
        "en": "Level 3 fault 8: Excessive temperature difference"
    },
    "三级故障9电流过大": {
        "zh": "三级故障9电流过大",
        "en": "Level 3 fault 9: Excessive current"
    },
    "三级故障10高压异常": {
        "zh": "三级故障10高压异常",
        "en": "Level 3 Fault 10 High Voltage Abnormality"
    },
    "三级故障11主从通讯": {
        "zh": "三级故障11主从通讯",
        "en": "Level 3 fault 11 master-slave communication"
    },
    "三级故障12单体电压排线": {
        "zh": "三级故障12单体电压排线",
        "en": "Level 3 fault 12 individual voltage cable"
    },
    "三级故障13单体温感排线": {
        "zh": "三级故障13单体温感排线",
        "en": "Level 3 fault 13 single temperature sensing cable"
    },
    "三级故障15SOC过低": {
        "zh": "三级故障15SOC过低",
        "en": "Level 3 fault: 15SOC too low"
    },
    "三级故障16绝缘漏电": {
        "zh": "三级故障16绝缘漏电",
        "en": "Level 3 fault 16 insulation leakage"
    },
    "三级故障17粘连": {
        "zh": "三级故障17粘连",
        "en": "Level 3 fault 17 adhesion"
    },
    "三级故障18预充状态": {
        "zh": "三级故障18预充状态",
        "en": "Level 3 Fault 18 Pre charge Status"
    },
    "三级故障19供电过高": {
        "zh": "三级故障19供电过高",
        "en": "Level 3 fault 19: High power supply"
    },
    "三级故障20供电过": {
        "zh": "三级故障20供电过",
        "en": "Level 3 fault 20 power supply exceeded"
    },
    "三级故障21T1高温": {
        "zh": "三级故障21T1高温",
        "en": "Level 3 fault 21T1 high temperature"
    },
    "三级故障22T1低温": {
        "zh": "三级故障22T1低温",
        "en": "Level 3 fault 22T1 low temperature"
    },
    "三级故障23T2高温": {
        "zh": "三级故障23T2高温",
        "en": "Level 3 fault 23T2 high temperature"
    },
    "三级故障24T2低温": {
        "zh": "三级故障24T2低温",
        "en": "Level 3 fault 24T2 low temperature"
    },
    "三级均衡过温": {
        "zh": "三级均衡过温",
        "en": "Third level equilibrium overheating"
    },
    "三级预留故障位": {
        "zh": "三级预留故障位",
        "en": "Third level reserved fault position"
    },
    "三级故障27MSD故障": {
        "zh": "三级故障27MSD故障",
        "en": "Level 3 fault 27MSD fault"
    },
    "三级故障28电流异常": {
        "zh": "三级故障28电流异常",
        "en": "Level 3 fault 28 abnormal current"
    },
    "三级故障29极柱高温": {
        "zh": "三级故障29极柱高温",
        "en": "Level 3 fault 29 pole high temperature"
    },
    "三级故障32PCS故障状态": {
        "zh": "三级故障32PCS故障状态",
        "en": "Level 3 fault: 32PCS fault status"
    },
    "三级故障33EMS故障状态": {
        "zh": "三级故障33EMS故障状态",
        "en": "Level 3 fault 33EMS fault status"
    },
    "三级故障34保险丝状态": {
        "zh": "三级故障34保险丝状态",
        "en": "Level 3 fault 34 fuse status"
    },
    "三级模拟前端故障": {
        "zh": "三级模拟前端故障",
        "en": "Level 3 simulation front-end fault"
    },
    "三级EEPROM故障": {
        "zh": "三级EEPROM故障",
        "en": "Level 3 EEPROM malfunction"
    },
    "三级RTC故障": {
        "zh": "三级RTC故障",
        "en": "Level 3 RTC malfunction"
    },
    "三级ADC故障": {
        "zh": "三级ADC故障",
        "en": "Third level ADC malfunction"
    },
    "三级SD卡故障": {
        "zh": "三级SD卡故障",
        "en": "Third level SD card malfunction"
    },
    "三级故障40反馈异常": {
        "zh": "三级故障40反馈异常",
        "en": "Level 3 fault 40 feedback abnormality"
    },
    "三级温升过快": {
        "zh": "三级温升过快",
        "en": "The third level temperature rise is too fast"
    },
    "三级故障42极限故障": {
        "zh": "三级故障42极限故障",
        "en": "Level 3 fault 42 extreme fault"
    },
    "三级故障43开路": {
        "zh": "三级故障43开路",
        "en": "Level 3 fault 43 open circuit"
    },
    "三级故障44急停": {
        "zh": "三级故障44急停",
        "en": "Level 3 fault 44 emergency stop"
    },
    "三级故障45消防火警": {
        "zh": "三级故障45消防火警",
        "en": "Level 3 fault 45 fire alarm"
    },
    "三级故障46控制指令超时": {
        "zh": "三级故障46控制指令超时",
        "en": "Level 3 fault 46: Control command timeout"
    },
    "三级故障49电池箱过压": {
        "zh": "三级故障49电池箱过压",
        "en": "Level 3 Fault 49 Battery Box Overvoltage"
    },
    "三级故障50电池箱欠压": {
        "zh": "三级故障50电池箱欠压",
        "en": "Level 3 fault: 50 battery box undervoltage"
    },
    "三级防雷器故障": {
        "zh": "三级防雷器故障",
        "en": "Third level lightning arrester malfunction"
    },
    "三级故障52水冷机通讯故障": {
        "zh": "三级故障52水冷机通讯故障",
        "en": "Level 3 fault 52: Communication failure of water cooler"
    },
    "三级UPS故障": {
        "zh": "三级UPS故障",
        "en": "Level 3 UPS malfunction"
    },
    "三级故障54水浸故障": {
        "zh": "三级故障54水浸故障",
        "en": "Level 3 fault 54 water immersion fault"
    },
    "三级门禁故障": {
        "zh": "三级门禁故障",
        "en": "Level 3 access control malfunction"
    },
    "三级其他传感器故障": {
        "zh": "三级其他传感器故障",
        "en": "Third level other sensor malfunction"
    },
    "三级故障消防喷洒": {
        "zh": "三级故障消防喷洒",
        "en": "Level 3 malfunction fire sprinkler"
    },
    "三级故障消防设备故障": {
        "zh": "三级故障消防设备故障",
        "en": "Level 3 fire equipment malfunction"
    },
    "三级故障59水冷机自身故障": {
        "zh": "三级故障59水冷机自身故障",
        "en": "Level 3 fault: 59 Water cooling machine itself malfunction"
    },
    "三级故障60电表通讯故障": {
        "zh": "三级故障60电表通讯故障",
        "en": "Level 3 fault: 60 meter communication failure"
    },
    "二级故障9电流过大": {
        "zh": "二级故障9电流过大",
        "en": "Level 2 fault 9: Excessive current"
    },
    "二级故障10高压异常": {
        "zh": "二级故障10高压异常",
        "en": "Level 2 Fault 10 High Voltage Abnormality"
    },
    "二级故障11主从通讯": {
        "zh": "二级故障11主从通讯",
        "en": "Level 2 fault 11 master-slave communication"
    },
    "二级故障12单体电压排线": {
        "zh": "二级故障12单体电压排线",
        "en": "Level 2 fault 12 individual voltage cable"
    },
    "二级故障13单体温感排线": {
        "zh": "二级故障13单体温感排线",
        "en": "Level 2 fault 13 single temperature sensing cable"
    },
    "二级故障16绝缘漏电": {
        "zh": "二级故障16绝缘漏电",
        "en": "Level 2 fault 16: Insulation leakage"
    },
    "二级故障17粘连": {
        "zh": "二级故障17粘连",
        "en": "Level 2 fault 17 adhesion"
    },
    "二级故障18预充状态": {
        "zh": "二级故障18预充状态",
        "en": "Level 2 Fault 18 Pre charge Status"
    },
    "二级故障19供电过高": {
        "zh": "二级故障19供电过高",
        "en": "Level 2 fault 19: High power supply"
    },
    "二级故障20供电过低": {
        "zh": "二级故障20供电过低",
        "en": "Level 2 fault 20: Low power supply"
    },
    "二级故障21T1高温": {
        "zh": "二级故障21T1高温",
        "en": "Level 2 fault 21T1 high temperature"
    },
    "二级故障22T1低温": {
        "zh": "二级故障22T1低温",
        "en": "Level 2 fault 22T1 low temperature"
    },
    "二级故障23T2高温": {
        "zh": "二级故障23T2高温",
        "en": "Level 2 fault 23T2 high temperature"
    },
    "二级故障24T2低温": {
        "zh": "二级故障24T2低温",
        "en": "Level 2 fault 24T2 low temperature"
    },
    "二级故障27MSD故障": {
        "zh": "二级故障27MSD故障",
        "en": "Level 2 fault 27MSD fault"
    },
    "二级故障28电流异常": {
        "zh": "二级故障28电流异常",
        "en": "Level 2 fault 28 abnormal current"
    },
    "二级故障29极柱高温": {
        "zh": "二级故障29极柱高温",
        "en": "Level 2 fault 29 pole high temperature"
    },
    "二级故障32PCS故障状态": {
        "zh": "二级故障32PCS故障状态",
        "en": "Level 2 fault: 32PCS fault status"
    },
    "二级故障33EMS故障状态": {
        "zh": "二级故障33EMS故障状态",
        "en": "Level 2 fault 33EMS fault status"
    },
    "二级故障34保险丝状态": {
        "zh": "二级故障34保险丝状态",
        "en": "Level 2 fault 34 fuse status"
    },
    "二级故障40反馈异常": {
        "zh": "二级故障40反馈异常",
        "en": "Level 2 fault 40 feedback abnormality"
    },
    "二级故障42极限故障": {
        "zh": "二级故障42极限故障",
        "en": "Level 2 fault 42 extreme fault"
    },
    "二级故障43开路": {
        "zh": "二级故障43开路",
        "en": "Level 2 fault 43 open circuit"
    },
    "二级故障44急停": {
        "zh": "二级故障44急停",
        "en": "Level 2 fault 44 emergency stop"
    },
    "二级故障45消防火警": {
        "zh": "二级故障45消防火警",
        "en": "Level 2 fault 45 fire alarm"
    },
    "二级故障46控制指令超时": {
        "zh": "二级故障46控制指令超时",
        "en": "Level 2 fault 46: Control command timeout"
    },
    "二级故障消防喷洒": {
        "zh": "二级故障消防喷洒",
        "en": "Secondary fault fire sprinkler"
    },
    "二级故障消防设备故障": {
        "zh": "二级故障消防设备故障",
        "en": "Level 2 fire equipment malfunction"
    },
    "二级故障59水冷机自身故障": {
        "zh": "二级故障59水冷机自身故障",
        "en": "Level 2 fault 59: Self malfunction of water cooler"
    },
    "二级故障60电表通讯故障": {
        "zh": "二级故障60电表通讯故障",
        "en": "Level 2 fault: 60 meter communication failure"
    },
    "继电器总正": {
        "zh": "继电器总正",
        "en": "Relay overall positive"
    },
    "继电器总负": {
        "zh": "继电器总负",
        "en": "Relay total negative"
    },
    "继电器预充": {
        "zh": "继电器预充",
        "en": "Relay pre charging"
    },
    "继电器断路器分励": {
        "zh": "继电器断路器分励",
        "en": "Relay circuit breaker shunt"
    },
    "继电器PCS干接点": {
        "zh": "继电器PCS干接点",
        "en": "Relay PCS dry contact"
    },
    "继电器故障灯": {
        "zh": "继电器故障灯",
        "en": "Relay malfunction light"
    },
    "继电器运行": {
        "zh": "继电器运行",
        "en": "Relay operation"
    },
    "继电器直流供电控制": {
        "zh": "继电器直流供电控制",
        "en": "Relay DC power supply control"
    },
    "充电状态": {
        "zh": "充电状态",
        "en": "Charging status"
    },
    "允许充电": {
        "zh": "允许充电 ",
        "en": "Allow charging"
    },
    "禁止充电": {
        "zh": "禁止充电",
        "en": "No charging allowed"
    },
    "放电状态": {
        "zh": "放电状态",
        "en": "Discharge state"
    },
    "允许放电": {
        "zh": "允许放电 ",
        "en": "Allow discharge"
    },
    "禁止放电": {
        "zh": "禁止放电",
        "en": "No discharge allowed"
    },
    "高压闭合状态": {
        "zh": "高压闭合状态",
        "en": "High voltage closed state"
    },
    "高压上下电指令": {
        "zh": "高压上下电指令",
        "en": "High voltage power on/off command"
    },
    "高压下电": {
        "zh": "高压下电 ",
        "en": "Under high voltage"
    },
    "高压上电": {
        "zh": "高压上电",
        "en": "High voltage power on"
    },
    "PTC温度开关状态": {
        "zh": "PTC温度开关状态",
        "en": "PTC temperature switch status"
    },
    "水泵开关": {
        "zh": "水泵开关",
        "en": "Water pump switch"
    },
    "通道2 bms 设备状态": {
        "zh": "通道2 bms 设备状态",
        "en": "Channel 2 BMS device status"
    },
    "BCU主回路状态": {
        "zh": "BCU主回路状态",
        "en": "BCU main circuit status"
    },
    "BCU回路闭合指令": {
        "zh": "BCU回路闭合指令",
        "en": "BCU circuit closure command"
    },
    "BCU是否允许闭合主回路": {
        "zh": "BCU是否允许闭合主回路",
        "en": "Does BCU allow the closure of the main circuit"
    },
    "不允许": {
        "zh": "不允许",
        "en": "not allow"
    },
    "允许": {
        "zh": "允许",
        "en": "allow"
    },
    "FPGA硬件故障-A相硬件过流": {
        "zh": "FPGA硬件故障-A相硬件过流",
        "en": "FPGA hardware failure - Phase A hardware overcurrent"
    },
    "FPGA硬件故障-B相硬件过流": {
        "zh": "FPGA硬件故障-B相硬件过流",
        "en": "FPGA hardware failure - B-phase hardware overcurrent"
    },
    "FPGA硬件故障-C相硬件过流": {
        "zh": "FPGA硬件故障-C相硬件过流",
        "en": "FPGA hardware failure - C-phase hardware overcurrent"
    },
    "FPGA硬件故障-N相硬件过流": {
        "zh": "FPGA硬件故障-N相硬件过流",
        "en": "FPGA hardware failure - N-phase hardware overcurrent"
    },
    "FPGA硬件故障-保留": {
        "zh": "FPGA硬件故障-保留",
        "en": "FPGA hardware failure - reserved"
    },
    "FPGA硬件故障-单元直压故障": {
        "zh": "FPGA硬件故障-单元直压故障",
        "en": "FPGA hardware failure - Unit direct voltage failure"
    },
    "FPGA硬件故障-开关电源欠压": {
        "zh": "FPGA硬件故障-开关电源欠压",
        "en": "FPGA hardware failure - switch power supply undervoltage"
    },
    "FPGA硬件故障-N相IGBT故障": {
        "zh": "FPGA硬件故障-N相IGBT故障",
        "en": "FPGA hardware failure - N-phase IGBT failure"
    },
    "ARM软件故障-A相输出过流": {
        "zh": "ARM软件故障-A相输出过流",
        "en": "ARM software malfunction - Phase A output overcurrent"
    },
    "ARM软件故障-A相输出速断": {
        "zh": "ARM软件故障-A相输出速断",
        "en": "ARM software malfunction - A-phase output quick break"
    },
    "ARM软件故障-B相输出过流": {
        "zh": "ARM软件故障-B相输出过流",
        "en": "ARM software malfunction - B-phase output overcurrent"
    },
    "ARM软件故障-B相输出速断": {
        "zh": "ARM软件故障-B相输出速断",
        "en": "ARM software malfunction - B-phase output fast break"
    },
    "ARM软件故障-C相输出过流": {
        "zh": "ARM软件故障-C相输出过流",
        "en": "ARM software malfunction - C-phase output overcurrent"
    },
    "ARM软件故障-C相输出速断": {
        "zh": "ARM软件故障-C相输出速断",
        "en": "ARM software malfunction - C-phase output quick break"
    },
    "ARM软件故障-N相输出速断": {
        "zh": "ARM软件故障-N相输出速断",
        "en": "ARM software malfunction - N-phase output fast break"
    },
    "ARM软件故障-电压THDU超限": {
        "zh": "ARM软件故障-电压THDU超限",
        "en": "ARM software malfunction - THDU voltage exceeding limit"
    },
    "ARM软件故障-系统过频率": {
        "zh": "ARM软件故障-系统过频率",
        "en": "ARM software malfunction - system overclocking"
    },
    "ARM软件故障-系统欠频率": {
        "zh": "ARM软件故障-系统欠频率",
        "en": "ARM software malfunction - system under frequency"
    },
    "ARM软件故障-直流充电过流": {
        "zh": "ARM软件故障-直流充电过流",
        "en": "ARM software malfunction - DC charging overcurrent"
    },
    "ARM软件故障-直流放电过流": {
        "zh": "ARM软件故障-直流放电过流",
        "en": "ARM software malfunction - DC discharge overcurrent"
    },
    "ARM软件故障-孤岛保护": {
        "zh": "ARM软件故障-孤岛保护",
        "en": "ARM software failure - islanding protection"
    },
    "ARM软件故障-交流主接合闸故障": {
        "zh": "ARM软件故障-交流主接合闸故障",
        "en": "ARM software malfunction - AC main connection closing fault"
    },
    "ARM软件故障-交流主接分闸故障": {
        "zh": "ARM软件故障-交流主接分闸故障",
        "en": "ARM software malfunction - AC main connection disconnection fault"
    },
    "ARM软件故障-交流软启合闸故障": {
        "zh": "ARM软件故障-交流软启合闸故障",
        "en": "ARM software malfunction - AC soft opening and closing fault"
    },
    "ARM软件故障-交流软启分闸故障": {
        "zh": "ARM软件故障-交流软启分闸故障",
        "en": "ARM software malfunction - AC soft start/disconnect fault"
    },
    "ARM软件故障-直流主接合闸故障": {
        "zh": "ARM软件故障-直流主接合闸故障",
        "en": "ARM software malfunction - DC main connection closing fault"
    },
    "ARM软件故障-直流主接分闸故障": {
        "zh": "ARM软件故障-直流主接分闸故障",
        "en": "ARM software malfunction - DC main connection disconnection fault"
    },
    "ARM软件故障-直流软启合闸故障": {
        "zh": "ARM软件故障-直流软启合闸故障",
        "en": "ARM software malfunction - DC soft opening and closing fault"
    },
    "ARM软件故障-铁电参数存储错误": {
        "zh": "ARM软件故障-铁电参数存储错误",
        "en": "ARM software malfunction - ferroelectric parameter storage error"
    },
    "ARM软件故障-保留": {
        "zh": "ARM软件故障-保留",
        "en": "ARM software malfunction - reserved"
    },
    "ARM软件故障-起机条件不满足": {
        "zh": "ARM软件故障-起机条件不满足",
        "en": "ARM software malfunction - startup conditions not met"
    },
    "ARM软件故障-逆变启动超时": {
        "zh": "ARM软件故障-逆变启动超时",
        "en": "ARM software failure - Inverter startup timeout"
    },
    "ARM软件故障-参数下发设置错误": {
        "zh": "ARM软件故障-参数下发设置错误",
        "en": "ARM software malfunction - parameter distribution setting error"
    },
    "ARM软件故障-BMS温度异常": {
        "zh": "ARM软件故障-BMS温度异常",
        "en": "ARM software malfunction - BMS temperature abnormality"
    },
    "ARM软件故障-BMS跳机": {
        "zh": "ARM软件故障-BMS跳机",
        "en": "ARM software malfunction - BMS trip"
    },
    "ARM软件故障-BMS电池告警": {
        "zh": "ARM软件故障-BMS电池告警",
        "en": "ARM software malfunction - BMS battery alarm"
    },
    "ARM软件故障-DCDC通讯故障": {
        "zh": "ARM软件故障-DCDC通讯故障",
        "en": "ARM software malfunction - DCDC communication malfunction"
    },
    "ARM软件故障-急停或熔芯故障": {
        "zh": "ARM软件故障-急停或熔芯故障",
        "en": "ARM software malfunction - emergency stop or melt failure"
    },
    "ARM软件故障-PCS光纤通讯故障": {
        "zh": "ARM软件故障-PCS光纤通讯故障",
        "en": "ARM software malfunction - PCS fiber optic communication malfunction"
    },
    "ARM软件故障-母线半直压过压": {
        "zh": "ARM软件故障-母线半直压过压",
        "en": "ARM software malfunction - Bus half direct voltage overvoltage"
    },
    "ARM软件故障-DCDC启动超时": {
        "zh": "ARM软件故障-DCDC启动超时",
        "en": "ARM software failure - CDC startup timeout"
    },
    "ARM软件故障-交流漏电流保护": {
        "zh": "ARM软件故障-交流漏电流保护",
        "en": "ARM software malfunction - AC leakage current protection"
    },
    "CANA通信故障": {
        "zh": "CANA通信故障",
        "en": "CANA communication malfunction"
    },
    "辅助电源故障1": {
        "zh": "辅助电源故障1",
        "en": "Auxiliary power supply fault 1"
    },
    "辅助电源故障2": {
        "zh": "辅助电源故障2",
        "en": "Auxiliary power supply fault 2"
    },
    "辅助电源故障3": {
        "zh": "辅助电源故障3",
        "en": "Auxiliary power failure 3"
    },
    "模块过温1": {
        "zh": "模块过温1",
        "en": "Module overheating 1"
    },
    "模块过温2": {
        "zh": "模块过温2",
        "en": "Module overheating 2"
    },
    "模块过温降额": {
        "zh": "模块过温降额",
        "en": "Module overheating and derating"
    },
    "环境过温降额": {
        "zh": "环境过温降额",
        "en": "Environmental overheating and temperature reduction"
    },
    "控制器采样不一致": {
        "zh": "控制器采样不一致",
        "en": "Inconsistent sampling of controller"
    },
    "电池电量不足": {
        "zh": "电池电量不足",
        "en": "Low battery level"
    },
    "绝缘检测异常1": {
        "zh": "绝缘检测异常1",
        "en": "Insulation detection abnormality 1"
    },
    "CANB通信故障": {
        "zh": "CANB通信故障",
        "en": "CANB communication failure"
    },
    "绝缘检测异常2": {
        "zh": "绝缘检测异常2",
        "en": "Insulation detection anomaly 2"
    },
    "模块限流告警1": {
        "zh": "模块限流告警1",
        "en": "Module current limit alarm 1"
    },
    "模块限流告警2": {
        "zh": "模块限流告警2",
        "en": "Module current limit alarm 2"
    },
    "模块限流告警3": {
        "zh": "模块限流告警3",
        "en": "Module current limit alarm 3"
    },
    "模块限流告警4": {
        "zh": "模块限流告警4",
        "en": "Module current limit alarm 4"
    },
    "模块限流告警5": {
        "zh": "模块限流告警5",
        "en": "Module current limit alarm 5"
    },
    "模块限流告警6": {
        "zh": "模块限流告警6",
        "en": "Module current limit alarm 6"
    },
    "模块限流告警7": {
        "zh": "模块限流告警7",
        "en": "Module current limit alarm 7"
    },
    "模块电流异常1": {
        "zh": "模块电流异常1",
        "en": "Module current abnormality 1"
    },
    "模块电流异常2": {
        "zh": "模块电流异常2",
        "en": "Module current abnormality 2"
    },
    "CANC通信故障": {
        "zh": "CANC通信故障",
        "en": "CANC communication malfunction"
    },
    "模块电流异常3": {
        "zh": "模块电流异常3",
        "en": "Module current abnormality 3"
    },
    "模块电流异常4": {
        "zh": "模块电流异常4",
        "en": "Module current abnormality 4"
    },
    "模块电流异常5": {
        "zh": "模块电流异常5",
        "en": "Module current abnormality 5"
    },
    "模块电流异常6": {
        "zh": "模块电流异常6",
        "en": "Module current abnormality 6"
    },
    "模块电流异常7": {
        "zh": "模块电流异常7",
        "en": "Module current abnormality 7"
    },
    "直流过载报警": {
        "zh": "直流过载报警",
        "en": "DC overload alarm"
    },
    "直流过载超时": {
        "zh": "直流过载超时",
        "en": "DC overload timeout"
    },
    "直流输入软启动失败": {
        "zh": "直流输入软启动失败",
        "en": "DC input soft start failure"
    },
    "直流输入电控开关开路": {
        "zh": "直流输入电控开关开路",
        "en": "DC input electric control switch open circuit"
    },
    "直流输入电控开关短路": {
        "zh": "直流输入电控开关短路",
        "en": "Short circuit of DC input electric control switch"
    },
    "同步信号1故障": {
        "zh": "同步信号1故障",
        "en": "Synchronization signal 1 fault"
    },
    "直流母线电压不平衡": {
        "zh": "直流母线电压不平衡",
        "en": "DC bus voltage imbalance"
    },
    "直流母线软启动失败": {
        "zh": "直流母线软启动失败",
        "en": "DC bus soft start failure"
    },
    "BMS关机故障": {
        "zh": "BMS关机故障",
        "en": "BMS shutdown fault"
    },
    "交流母线电压异常": {
        "zh": "交流母线电压异常",
        "en": "Abnormal AC bus voltage"
    },
    "孤岛保护": {
        "zh": "孤岛保护",
        "en": "Island protection"
    },
    "交流过载超时": {
        "zh": "交流过载超时",
        "en": "Communication overload timeout"
    },
    "交流母线禁止接入": {
        "zh": "交流母线禁止接入",
        "en": "Communication bus is prohibited from being connected"
    },
    "交流母线缺N": {
        "zh": "交流母线缺N",
        "en": "The communication bus is missing N"
    },
    "离网电压震荡": {
        "zh": "离网电压震荡",
        "en": "Off grid voltage oscillation"
    },
    "并离网切换错误": {
        "zh": "并离网切换错误",
        "en": "Off grid switching error"
    },
    "辅助控制板通信故障": {
        "zh": "辅助控制板通信故障",
        "en": "Communication failure of auxiliary control board"
    },
    "交流电控开关开路": {
        "zh": "交流电控开关开路",
        "en": "AC electric control switch open circuit"
    },
    "交流电控开关短路": {
        "zh": "交流电控开关短路",
        "en": "Short circuit of AC electric control switch"
    },
    "紧急停机": {
        "zh": "紧急停机",
        "en": "Emergency stop"
    },
    "校准参数异常": {
        "zh": "校准参数异常",
        "en": "Abnormal calibration parameters"
    },
    "采样零点异常": {
        "zh": "采样零点异常",
        "en": "Sampling zero point anomaly"
    },
    "Flash异常": {
        "zh": "Flash异常",
        "en": "Flash exception"
    },
    "DSP初始化异常": {
        "zh": "DSP初始化异常",
        "en": "DSP initialization exception"
    },
    "监控初始化异常": {
        "zh": "监控初始化异常",
        "en": "Monitoring initialization exception"
    },
    "DSP版本异常": {
        "zh": "DSP版本异常",
        "en": "DSP version exception"
    },
    "CPLD版本异常": {
        "zh": "CPLD版本异常",
        "en": "CPLD version exception"
    },
    "SPI通信故障": {
        "zh": "SPI通信故障",
        "en": "SPI communication failure"
    },
    "硬件版本异常": {
        "zh": "硬件版本异常",
        "en": "Hardware version exception"
    },
    "重号故障": {
        "zh": "重号故障",
        "en": "Duplicate fault"
    },
    "主机冲突故障": {
        "zh": "主机冲突故障",
        "en": "Host conflict fault"
    },
    "DSP参数设置不匹配": {
        "zh": "DSP参数设置不匹配",
        "en": "DSP parameter settings do not match"
    },
    "监控参数设置不匹配": {
        "zh": "监控参数设置不匹配",
        "en": "Monitoring parameter settings do not match"
    },
    "Drm0 关机": {
        "zh": "Drm0 关机",
        "en": "Drm0 shutdown"
    },
    "模块数量异常": {
        "zh": "模块数量异常",
        "en": "Abnormal number of modules"
    },
    "BMS通信连接超时": {
        "zh": "BMS通信连接超时",
        "en": "BMS communication connection timeout"
    },
    "电网电表通信超时": {
        "zh": "电网电表通信超时",
        "en": "Grid meter communication timeout"
    },
    "模块风扇故障1": {
        "zh": "模块风扇故障1",
        "en": "Module fan fault 1"
    },
    "直流输入软启电控开关故障": {
        "zh": "直流输入软启电控开关故障",
        "en": "DC input soft start electronic control switch fault"
    },
    "直流母线过压": {
        "zh": "直流母线过压",
        "en": "DC bus overvoltage"
    },
    "直流母线欠压": {
        "zh": "直流母线欠压",
        "en": "DC bus undervoltage"
    },
    "交流母线过压": {
        "zh": "交流母线过压",
        "en": "Overvoltage of communication bus"
    },
    "交流母线欠压": {
        "zh": "交流母线欠压",
        "en": "Communication bus undervoltage"
    },
    "交流母线过频": {
        "zh": "交流母线过频",
        "en": "Overfrequency of communication bus"
    },
    "交流母线欠频": {
        "zh": "交流母线欠频",
        "en": "Communication bus under frequency"
    },
    "交流母线缺相": {
        "zh": "交流母线缺相",
        "en": "AC bus phase loss"
    },
    "环境过温故障": {
        "zh": "环境过温故障",
        "en": "Environmental overheating fault"
    },
    "直流输入欠压": {
        "zh": "直流输入欠压",
        "en": "DC input undervoltage"
    },
    "交流母线电压不平衡": {
        "zh": "交流母线电压不平衡",
        "en": "Unbalanced AC bus voltage"
    },
    "交流过载报警": {
        "zh": "交流过载报警",
        "en": "Communication overload alarm"
    },
    "交流母线反序": {
        "zh": "交流母线反序",
        "en": "Reverse sequence of communication bus"
    },
    "交流软启动失败": {
        "zh": "交流软启动失败",
        "en": "Communication soft start failed"
    },
    "EMS通信连接超时": {
        "zh": "EMS通信连接超时",
        "en": "EMS communication connection timeout"
    },
    "直流输入过压": {
        "zh": "直流输入过压",
        "en": "DC input overvoltage"
    },
    "二级故障56温度传感器故障": {
        "zh": "二级故障56温度传感器故障",
        "en": "Level 2 fault 56: Temperature sensor malfunction"
    },
    "三级故障48极限故障": {
        "zh": "三级故障48极限故障",
        "en": "Level 3 fault 48 extreme fault"
    },
    "三级故障62Pack级消防故障": {
        "zh": "三级故障62Pack级消防故障",
        "en": "Level 3 fault, 62Pack level fire protection fault"
    },
    "三级故障63霍尔超量程": {
        "zh": "三级故障63霍尔超量程",
        "en": "Level 3 fault 63 Hall overrange"
    },
    "三级故障64霍尔电压超限": {
        "zh": "三级故障64霍尔电压超限",
        "en": "Level 3 fault 64 Hall voltage exceeds the limit"
    },
    "二级故障12从控概要": {
        "zh": "二级故障12从控概要",
        "en": "Level 2 Fault 12 Control Summary"
    },
    "二级故障61水机制热失效": {
        "zh": "二级故障61水机制热失效",
        "en": "Level 2 Fault 61: Thermal Failure of Water Mechanism"
    },
    "二级故障62水机制冷失效": {
        "zh": "二级故障62水机制冷失效",
        "en": "Level 2 malfunction: Cooling failure of water machine 62"
    },
    "继电器液冷机组唤醒信号": {
        "zh": "继电器液冷机组唤醒信号",
        "en": "Relay liquid cooling unit wake-up signal"
    },
    "继电器直流微断": {
        "zh": "继电器直流微断",
        "en": "Relay DC micro break"
    },
    "继电器直流塑壳断路器": {
        "zh": "继电器直流塑壳断路器",
        "en": "Relay DC molded case circuit breaker"
    },
    "EMS 设备在线状态": {
        "zh": "EMS 设备在线状态",
        "en": "EMS device online status"
    },
    "BMS 设备在线状态": {
        "zh": "BMS 设备在线状态",
        "en": "BMS device online status"
    },
    "BMS1 设备在线状态": {
        "zh": "BMS1 设备在线状态",
        "en": "BMS1 device online status"
    },
    "BMS2 设备在线状态": {
        "zh": "BMS2 设备在线状态",
        "en": "BMS2 device online status"
    },
    "BMS3 设备在线状态": {
        "zh": "BMS3 设备在线状态",
        "en": "BMS3 device online status"
    },
    "BMS4 设备在线状态": {
        "zh": "BMS4 设备在线状态",
        "en": "BMS4 device online status"
    },
    "PCS 设备在线状态": {
        "zh": "PCS 设备在线状态",
        "en": "PCS device online status"
    },
    "PCS1 设备在线状态": {
        "zh": "PCS1 设备在线状态",
        "en": "PCS1 device online status"
    },
    "PCS2 设备在线状态": {
        "zh": "PCS2 设备在线状态",
        "en": "PCS2 device online status"
    },
    "PCS3 设备在线状态": {
        "zh": "PCS3 设备在线状态",
        "en": "PCS3 device online status"
    },
    "PCS4 设备在线状态": {
        "zh": "PCS4 设备在线状态",
        "en": "PCS4 device online status"
    },
    "EMS 设备通信状态": {
        "zh": "EMS 设备通信状态",
        "en": "EMS device communication status"
    },
    "BMS 设备通信状态": {
        "zh": "BMS 设备通信状态",
        "en": "BMS device communication status"
    },
    "BMS1 设备通信状态": {
        "zh": "BMS1 设备通信状态",
        "en": "BMS1 device communication status"
    },
    "BMS2 设备通信状态": {
        "zh": "BMS2 设备通信状态",
        "en": "BMS2 device communication status"
    },
    "BMS3 设备通信状态": {
        "zh": "BMS3 设备通信状态",
        "en": "BMS3 device communication status"
    },
    "BMS4 设备通信状态": {
        "zh": "BMS4 设备通信状态",
        "en": "BMS4 device communication status"
    },
    "PCS 设备通信状态": {
        "zh": "PCS 设备通信状态",
        "en": "PCS device communication status"
    },
    "PCS1 设备通信状态": {
        "zh": "PCS1 设备通信状态",
        "en": "PCS1 device communication status"
    },
    "PCS2 设备通信状态": {
        "zh": "PCS2 设备通信状态",
        "en": "PCS2 device communication status"
    },
    "PCS3 设备通信状态": {
        "zh": "PCS3 设备通信状态",
        "en": "PCS3 device communication status"
    },
    "PCS4 设备通信状态": {
        "zh": "PCS4 设备通信状态",
        "en": "PCS4 device communication status"
    },
    'PCS 警告状态': {'zh': 'PCS 警告状态', 'en': 'PCS warning status'},
    '继电器预留': {'zh': '继电器预留', 'en': 'Relay reservation'},
    '通道1 pcs 故障字储能电池极性反接故障': {'zh': '通道1 pcs 故障字储能电池极性反接故障',
                              'en': 'Channel 1 pcs fault word energy storage battery polarity reversal fault'},
    '通道1 pcs 故障字储能电池电压异常': {'zh': '通道1 pcs 故障字储能电池电压异常',
                            'en': 'Channel 1 pcs fault word: abnormal voltage of energy storage battery'},
    '一级故障总压过压': {'zh': '一级故障总压过压', 'en': 'Level 1 fault total pressure overvoltage'},
    '通道1 pcs 故障字直流侧半母线硬件过压': {'zh': '通道1 pcs 故障字直流侧半母线硬件过压',
                              'en': 'Channel 1 pcs fault word DC side half bus hardware overvoltage'},
    '一级故障总压欠压': {'zh': '一级故障总压欠压', 'en': 'Level 1 fault total voltage undervoltage'},
    '通道1 pcs 故障字交流硬件过流': {'zh': '通道1 pcs 故障字交流硬件过流', 'en': 'Channel 1 pcs fault word AC hardware overcurrent'},
    '一级故障单体过压': {'zh': '一级故障单体过压', 'en': 'First level fault single unit overvoltage'},
    '通道1 pcs 故障字IGBT A 相故障': {'zh': '通道1 pcs 故障字IGBT A 相故障', 'en': 'Channel 1 pcs fault word IGBT A-phase fault'},
    '一级故障单体欠压': {'zh': '一级故障单体欠压', 'en': 'Level 1 fault single unit undervoltage'},
    '通道1 pcs 故障字IGBT B 相故障': {'zh': '通道1 pcs 故障字IGBT B 相故障', 'en': 'Channel 1 pcs fault word IGBT B-phase fault'},
    '一级故障单体过温': {'zh': '一级故障单体过温', 'en': 'First level fault unit overheating'},
    '通道1 pcs 故障字IGBT C 相故障': {'zh': '通道1 pcs 故障字IGBT C 相故障', 'en': 'Channel 1 pcs fault word IGBT C-phase fault'},
    '一级故障单体低温': {'zh': '一级故障单体低温', 'en': 'Low temperature of primary fault unit'},
    '通道1 pcs 故障字直流开关故障': {'zh': '通道1 pcs 故障字直流开关故障', 'en': 'Channel 1 pcs fault word DC switch fault'},
    '一级故障压差过大': {'zh': '一级故障压差过大', 'en': 'Level 1 fault with excessive pressure difference'},
    '通道1 pcs 故障字IGBT 风机故障': {'zh': '通道1 pcs 故障字IGBT 风机故障', 'en': 'Channel 1 pcs fault word IGBT fan fault'},
    '一级故障温差过大': {'zh': '一级故障温差过大', 'en': 'Level 1 fault with excessive temperature difference'},
    '通道1 pcs 故障字BMS 系统故障': {'zh': '通道1 pcs 故障字BMS 系统故障', 'en': 'Channel 1 pcs fault word BMS system fault'},
    '一级故障电流过大': {'zh': '一级故障电流过大', 'en': 'Level 1 fault current is too high'},
    '一级故障预留': {'zh': '一级故障预留', 'en': 'Level 1 fault reservation'},
    '压缩机故障': {'zh': '压缩机故障', 'en': 'Compressor malfunction'},
    '通道3 虚拟点 PCS故障状态': {'zh': '通道3 虚拟点 PCS故障状态', 'en': 'Channel 3 virtual point PCS fault status'},
    '通道3 虚拟点 PCS告警状态': {'zh': '通道3 虚拟点 PCS告警状态', 'en': 'Channel 3 virtual point PCS alarm status'},
    '一级故障单体温感排线': {'zh': '一级故障单体温感排线', 'en': 'Level 1 fault single temperature sensing cable'},
    '并离网切换初始模式': {'zh': '并离网切换初始模式', 'en': 'Off grid switching to initial mode'},
    '启动命令': {'zh': '启动命令', 'en': 'Start command'},
    '一级故障SOC过低': {'zh': '一级故障SOC过低', 'en': 'First level fault SOC too low'},
    '停机命令': {'zh': '停机命令', 'en': 'Shutdown command'},
    '一级故障绝缘漏电': {'zh': '一级故障绝缘漏电', 'en': 'Level 1 fault insulation leakage'},
    '待机命令': {'zh': '待机命令', 'en': 'Standby command'}, '故障复位': {'zh': '故障复位', 'en': 'Fault reset'},
    '被动绝缘检测命令': {'zh': '被动绝缘检测命令', 'en': 'Passive insulation detection command'},
    '一级故障供电过高': {'zh': '一级故障供电过高', 'en': 'Level 1 fault power supply too high'},
    'BMS 参数保护功能': {'zh': 'BMS 参数保护功能', 'en': 'BMS parameter protection function'},
    '一级故障供电过低': {'zh': '一级故障供电过低', 'en': 'Level 1 fault: Low power supply'},
    '自启停模式使能': {'zh': '自启停模式使能', 'en': 'Enable self start stop mode'},
    '一级故障T1高温': {'zh': '一级故障T1高温', 'en': 'Level 1 fault T1 high temperature'},
    '离网并机模式': {'zh': '离网并机模式', 'en': 'Off grid parallel mode'},
    '一级故障T1低温': {'zh': '一级故障T1低温', 'en': 'Level 1 fault T1 low temperature'}, '告警字不满足离网运行条件': {'zh': '告警字不满足离网运行条件',
                                                                                               'en': 'The alarm message does not meet the conditions for off grid operation'},
    '一级故障T2高温': {'zh': '一级故障T2高温', 'en': 'Level 1 fault T2 high temperature'},
    '继电器运行灯': {'zh': '继电器运行灯', 'en': 'Relay running light'},
    '一级故障T2低温': {'zh': '一级故障T2低温', 'en': 'Level 1 fault T2 low temperature'},
    '继电器风扇': {'zh': '继电器风扇', 'en': 'Relay fan'}, '无功功率设定模式': {'zh': '无功功率设定模式', 'en': 'Reactive power setting mode'},
    '继电器状态': {'zh': '继电器状态', 'en': 'Relay status'}, '开关量状态': {'zh': '开关量状态', 'en': 'Switching status'},
    '一级故障13单体温感排线': {'zh': '一级故障13单体温感排线', 'en': 'Level 1 fault 13 single temperature sensing cable'},
    '一级故障极柱高温': {'zh': '一级故障极柱高温', 'en': 'Level 1 fault pole high temperature'},
    '一级故障14预留故障': {'zh': '一级故障14预留故障', 'en': 'Level 1 fault 14 reserved fault'},
    '一级故障25预留故障': {'zh': '一级故障25预留故障', 'en': 'Level 1 fault 25 reserved fault'},
    '一级故障26预留故障': {'zh': '一级故障26预留故障', 'en': 'Level 1 fault 26 reserved fault'},
    '一级故障故PCS故障状态': {'zh': '一级故障故PCS故障状态', 'en': 'Level 1 fault, PCS fault status'},
    '一级故障30预留故障': {'zh': '一级故障30预留故障', 'en': 'Level 1 fault 30 reserved fault'},
    '一级故障EMS故障状态': {'zh': '一级故障EMS故障状态', 'en': 'Level 1 fault EMS fault status'},
    '一级故障31预留故障': {'zh': '一级故障31预留故障', 'en': 'Level 1 fault 31 reserved fault'},
    '一级故障35预留故障': {'zh': '一级故障35预留故障', 'en': 'Level 1 fault 35 reserved fault'},
    '一级故障36预留故障': {'zh': '一级故障36预留故障', 'en': 'Level 1 fault 36 reserved fault'},
    '一级故障37预留故障': {'zh': '一级故障37预留故障', 'en': 'Level 1 fault 37 reserved fault'},
    '一级故障38预留故障': {'zh': '一级故障38预留故障', 'en': 'Level 1 fault 38 reserved fault'},
    '一级故障39预留故障': {'zh': '一级故障39预留故障', 'en': 'Level 1 fault 39 reserved fault'},
    '一级故障41预留故障': {'zh': '一级故障41预留故障', 'en': 'Level 1 fault 41 reserved fault'},
    '一级故障47预留故障': {'zh': '一级故障47预留故障', 'en': 'Level 1 fault 47 reserved fault'},
    '一级故障48预留故障': {'zh': '一级故障48预留故障', 'en': 'Level 1 fault 48 reserved fault'},
    '一级故障51预留故障': {'zh': '一级故障51预留故障', 'en': 'Level 1 fault 51 reserved fault'},
    '一级故障53预留故障': {'zh': '一级故障53预留故障', 'en': 'Level 1 fault 53 reserved fault'},
    '一级故障55预留故障': {'zh': '一级故障55预留故障', 'en': 'Level 1 fault 55 reserved fault'},
    '一级故障56预留故障': {'zh': '一级故障56预留故障', 'en': 'Level 1 fault 56 reserved fault'},
    '一级故障57预留故障': {'zh': '一级故障57预留故障', 'en': 'Level 1 fault 57 reserved fault'},
    '一级故障58预留故障': {'zh': '一级故障58预留故障', 'en': 'Level 1 fault 58 reserved fault'},
    '一级故障61预留故障': {'zh': '一级故障61预留故障', 'en': 'Level 1 fault 61 reserved fault'},
    '一级故障电池箱过压': {'zh': '一级故障电池箱过压', 'en': 'First level fault battery box overvoltage'},
    '一级故障62预留故障': {'zh': '一级故障62预留故障', 'en': 'Level 1 fault 62 reserved fault'},
    '一级故障电池箱欠压': {'zh': '一级故障电池箱欠压', 'en': 'Level 1 fault battery box undervoltage'},
    '一级故障63预留故障': {'zh': '一级故障63预留故障', 'en': 'Level 1 fault 63 reserved fault'},
    '一级故障64预留故障': {'zh': '一级故障64预留故障', 'en': 'Level 1 fault 64 reserved fault'},
    '二级故障14预留故障': {'zh': '二级故障14预留故障', 'en': 'Level 2 fault 14 reserved fault'},
    '二级故障25预留故障': {'zh': '二级故障25预留故障', 'en': 'Level 2 fault 25 reserved fault'},
    '二级故障26预留故障': {'zh': '二级故障26预留故障', 'en': 'Level 2 fault 26 reserved fault'},
    '二级故障30预留故障': {'zh': '二级故障30预留故障', 'en': 'Level 2 fault 30 reserved fault'},
    '二级故障31预留故障': {'zh': '二级故障31预留故障', 'en': 'Level 2 fault 31 reserved fault'},
    '二级故障35预留故障': {'zh': '二级故障35预留故障', 'en': 'Level 2 fault 35 reserved fault'},
    '二级故障36预留故障': {'zh': '二级故障36预留故障', 'en': 'Level 2 fault 36 reserved fault'},
    '一级故障水冷机自身故障': {'zh': '一级故障水冷机自身故障', 'en': 'Level 1 malfunction: The water chiller itself is malfunctioning'},
    '二级故障37预留故障': {'zh': '二级故障37预留故障', 'en': 'Level 2 fault 37 reserved fault'},
    '一级故障电表通讯故障': {'zh': '一级故障电表通讯故障', 'en': 'Level 1 fault: Communication failure of electricity meter'},
    '二级故障38预留故障': {'zh': '二级故障38预留故障', 'en': 'Level 2 fault 38 reserved fault'},
    '一级故障除湿机通讯故障': {'zh': '一级故障除湿机通讯故障', 'en': 'Level 1 malfunction dehumidifier communication failure'},
    '二级故障39预留故障': {'zh': '二级故障39预留故障', 'en': 'Level 2 fault 39 reserved fault'},
    '二级故障41预留故障': {'zh': '二级故障41预留故障', 'en': 'Level 2 fault 41 reserved fault'},
    '二级故障47预留故障': {'zh': '二级故障47预留故障', 'en': 'Level 2 fault 47 reserved fault'},
    '二级故障48预留故障': {'zh': '二级故障48预留故障', 'en': 'Level 2 fault 48 reserved fault'},
    '二级故障总压过压': {'zh': '二级故障总压过压', 'en': 'Secondary fault total pressure overvoltage'},
    '二级故障51预留故障': {'zh': '二级故障51预留故障', 'en': 'Second level fault 51 reserved fault'},
    '二级故障总压欠压': {'zh': '二级故障总压欠压', 'en': 'Secondary fault total voltage undervoltage'},
    '二级故障53预留故障': {'zh': '二级故障53预留故障', 'en': 'Level 2 fault 53 reserved fault'},
    '二级故障单体过压': {'zh': '二级故障单体过压', 'en': 'Secondary fault single unit overvoltage'},
    '二级故障55预留故障': {'zh': '二级故障55预留故障', 'en': 'Level 2 fault 55 reserved fault'},
    '二级故障单体欠压': {'zh': '二级故障单体欠压', 'en': 'Under voltage of secondary fault unit'},
    '二级故障56预留故障': {'zh': '二级故障56预留故障', 'en': 'Level 2 fault 56 reserved fault'},
    '二级故障单体过温': {'zh': '二级故障单体过温', 'en': 'Secondary fault: Unit overheating'},
    '二级故障57预留故障': {'zh': '二级故障57预留故障', 'en': 'Level 2 fault 57 reserved fault'},
    '二级故障单体低温': {'zh': '二级故障单体低温', 'en': 'Low temperature of secondary fault unit'},
    '二级故障58预留故障': {'zh': '二级故障58预留故障', 'en': 'Level 2 fault 58 reserved fault'},
    '二级故障压差过大': {'zh': '二级故障压差过大', 'en': 'Secondary fault with excessive pressure difference'},
    '二级故障61预留故障': {'zh': '二级故障61预留故障', 'en': 'Level 2 fault 61 reserved fault'},
    '二级故障温差过大': {'zh': '二级故障温差过大', 'en': 'Second level fault with excessive temperature difference'},
    '二级故障62预留故障': {'zh': '二级故障62预留故障', 'en': 'Level 2 fault 62 reserved fault'},
    '二级故障电流过大': {'zh': '二级故障电流过大', 'en': 'Secondary fault current too high'},
    '二级故障63预留故障': {'zh': '二级故障63预留故障', 'en': 'Level 2 fault 63 reserved fault'},
    '二级故障预留': {'zh': '二级故障预留', 'en': 'Secondary fault reservation'},
    '二级故障64预留故障': {'zh': '二级故障64预留故障', 'en': 'Secondary fault 64 reserved fault'},
    '三级故障14预留故障': {'zh': '三级故障14预留故障', 'en': 'Level 3 fault 14 reserved fault'},
    '二级故障从控概要': {'zh': '二级故障从控概要', 'en': 'Summary of Level 2 Fault Control'},
    '三级故障20供电过低': {'zh': '三级故障20供电过低', 'en': 'Level 3 fault 20: Low power supply'},
    '三级故障25预留故障': {'zh': '三级故障25预留故障', 'en': 'Level 3 fault 25 reserved fault'},
    '三级故障26预留故障': {'zh': '三级故障26预留故障', 'en': 'Level 3 fault 26 reserved fault'},
    '二级故障SOC过低': {'zh': '二级故障SOC过低', 'en': 'Second level fault SOC too low'},
    '三级故障30预留故障': {'zh': '三级故障30预留故障', 'en': 'Level 3 fault 30 reserved fault'},
    '二级故障绝缘漏电': {'zh': '二级故障绝缘漏电', 'en': 'Secondary fault insulation leakage'},
    '三级故障31预留故障': {'zh': '三级故障31预留故障', 'en': 'Level 3 fault 31 reserved fault'},
    '三级故障35预留故障（模拟前端故障）': {'zh': '三级故障35预留故障（模拟前端故障）',
                           'en': 'Level 3 fault 35 reserved fault (simulating front-end fault)'},
    '三级故障36预留故障': {'zh': '三级故障36预留故障', 'en': 'Level 3 fault 36 reserved fault'},
    '二级故障供电过高': {'zh': '二级故障供电过高', 'en': 'Level 2 fault power supply too high'},
    '三级故障37预留故障': {'zh': '三级故障37预留故障', 'en': 'Level 3 fault 37 reserved fault'},
    '二级故障供电过低': {'zh': '二级故障供电过低', 'en': 'Level 2 fault: Low power supply'},
    '三级故障38预留故障': {'zh': '三级故障38预留故障', 'en': 'Level 3 fault 38 reserved fault'},
    '二级故障T1高温': {'zh': '二级故障T1高温', 'en': 'Level 2 fault T1 high temperature'},
    '三级故障39预留故障': {'zh': '三级故障39预留故障', 'en': 'Level 3 fault 39 reserved fault'},
    '二级故障T1低温': {'zh': '二级故障T1低温', 'en': 'Level 2 fault T1 low temperature'},
    '三级故障41预留故障': {'zh': '三级故障41预留故障', 'en': 'Level 3 fault 41 reserved fault'},
    '二级故障T2高温': {'zh': '二级故障T2高温', 'en': 'Level 2 fault T2 high temperature'},
    '三级故障47预留故障': {'zh': '三级故障47预留故障', 'en': 'Level 3 fault 47 reserved fault'},
    '二级故障T2低温': {'zh': '二级故障T2低温', 'en': 'Level 2 fault T2 low temperature'},
    '三级故障48预留故障': {'zh': '三级故障48预留故障', 'en': 'Level 3 fault 48 reserved fault'},
    '三级故障51预留故障': {'zh': '三级故障51预留故障', 'en': 'Level 3 fault 51 reserved fault'},
    '三级故障53预留故障': {'zh': '三级故障53预留故障', 'en': 'Level 3 fault 53 reserved fault'},
    '三级故障55预留故障': {'zh': '三级故障55预留故障', 'en': 'Level 3 fault 55 reserved fault'},
    '三级故障56预留故障': {'zh': '三级故障56预留故障', 'en': 'Level 3 fault 56 reserved fault'},
    '二级故障极柱高温': {'zh': '二级故障极柱高温', 'en': 'Secondary fault pole high temperature'},
    '三级故障57预留故障': {'zh': '三级故障57预留故障', 'en': 'Level 3 fault 57 reserved fault'},
    '三级故障58预留故障': {'zh': '三级故障58预留故障', 'en': 'Level 3 fault 58 reserved fault'},
    '三级故障61预留故障': {'zh': '三级故障61预留故障', 'en': 'Level 3 fault 61 reserved fault'},
    '二级故障故PCS故障状态': {'zh': '二级故障故PCS故障状态', 'en': 'Level 2 fault, PCS fault status'},
    '三级故障62预留故障': {'zh': '三级故障62预留故障', 'en': 'Level 3 fault 62 reserved fault'},
    '二级故障EMS故障状态': {'zh': '二级故障EMS故障状态', 'en': 'Secondary fault EMS fault status'},
    '三级故障63预留故障': {'zh': '三级故障63预留故障', 'en': 'Level 3 fault 63 reserved fault'},
    '三级故障64预留故障': {'zh': '三级故障64预留故障', 'en': 'Level 3 fault 64 reserved fault'},
    '云端自动化模式下负荷跟随': {'zh': '云端自动化模式下负荷跟随', 'en': 'Load following in cloud automation mode'},
    '变压器超限告警': {'zh': '变压器超限告警', 'en': 'Transformer over limit alarm'},
    '防逆流通讯故障': {'zh': '防逆流通讯故障', 'en': 'Anti backflow communication failure'},
    '水机故障码': {'zh': '水机故障码', 'en': 'Water machine fault code'},
    '二级故障消防火警（四合一探测器）': {'zh': '二级故障消防火警（四合一探测器）', 'en': 'Level 2 malfunction fire alarm (four in one detector)'},
    '二级故障电池箱过压': {'zh': '二级故障电池箱过压', 'en': 'Secondary fault battery box overvoltage'},
    '二级故障电池箱欠压': {'zh': '二级故障电池箱欠压', 'en': 'Secondary fault battery box undervoltage'},
    '二级故障水冷机通讯故障': {'zh': '二级故障水冷机通讯故障', 'en': 'Secondary fault: Communication failure of water cooling machine'},
    '二级故障温度传感器故障（烟温感）': {'zh': '二级故障温度传感器故障（烟温感）',
                         'en': 'Secondary fault: Temperature sensor malfunction (smoke temperature sensor)'},
    '二级故障水冷机自身故障': {'zh': '二级故障水冷机自身故障',
                    'en': 'Level 2 malfunction: The water cooling machine itself is malfunctioning'},
    '二级故障电表通讯故障': {'zh': '二级故障电表通讯故障', 'en': 'Secondary fault: Communication failure of the electric meter'},
    '二级故障水机制热失效': {'zh': '二级故障水机制热失效', 'en': 'Level 2 malfunction: thermal failure of the water mechanism'},
    '二级故障水机制冷失效': {'zh': '二级故障水机制冷失效', 'en': 'Level 2 malfunction: Cooling failure of water machine'},
    '三级故障总压过压': {'zh': '三级故障总压过压', 'en': 'Level 3 fault total pressure overvoltage'},
    '三级故障总压欠压': {'zh': '三级故障总压欠压', 'en': 'Level 3 fault total voltage undervoltage'},
    '三级故障单体过压': {'zh': '三级故障单体过压', 'en': 'Third level fault single unit overvoltage'},
    '三级故障单体欠压': {'zh': '三级故障单体欠压', 'en': 'Level 3 fault single unit undervoltage'},
    '三级故障单体过温': {'zh': '三级故障单体过温', 'en': 'Third level fault: Unit overheating'},
    '三级故障单体低温': {'zh': '三级故障单体低温', 'en': 'Level 3 fault: low temperature of individual unit'},
    '三级故障压差过大': {'zh': '三级故障压差过大', 'en': 'Level 3 fault with excessive pressure difference'},
    '三级故障温差过大': {'zh': '三级故障温差过大', 'en': 'Level 3 fault with excessive temperature difference'},
    '三级故障电流过大': {'zh': '三级故障电流过大', 'en': 'Level 3 fault current is too high'},
    '三级高压异常': {'zh': '三级高压异常', 'en': 'Abnormal Level 3 High Voltage'},
    '三级主从通讯': {'zh': '三级主从通讯', 'en': 'Third level master-slave communication'},
    '三级单体电压排线': {'zh': '三级单体电压排线', 'en': 'Third level individual voltage cable'},
    '三级单体温感排线': {'zh': '三级单体温感排线', 'en': 'Third level single temperature sensing cable'},
    '三级SOC过低': {'zh': '三级SOC过低', 'en': 'Level 3 SOC is too low'},
    '三级故障绝缘漏电': {'zh': '三级故障绝缘漏电', 'en': 'Level 3 fault insulation leakage'},
    '三级粘连': {'zh': '三级粘连', 'en': 'Third grade adhesion'},
    '三级预充状态': {'zh': '三级预充状态', 'en': 'Level 3 pre charging status'},
    '三级故障供电过高': {'zh': '三级故障供电过高', 'en': 'Level 3 fault power supply too high'},
    '三级故障供电过低': {'zh': '三级故障供电过低', 'en': 'Level 3 fault: Low power supply'},
    '三级故障T1高温': {'zh': '三级故障T1高温', 'en': 'Level 3 fault T1 high temperature'},
    '三级故障T1低温': {'zh': '三级故障T1低温', 'en': 'Level 3 fault T1 low temperature'},
    '三级故障T2高温': {'zh': '三级故障T2高温', 'en': 'Level 3 fault T2 high temperature'},
    '三级故障T2低温': {'zh': '三级故障T2低温', 'en': 'Level 3 fault T2 low temperature'},
    '三级MSD故障': {'zh': '三级MSD故障', 'en': 'Level 3 MSD malfunction'},
    '三级电流异常': {'zh': '三级电流异常', 'en': 'Abnormal third level current'},
    '三级故障极柱高温': {'zh': '三级故障极柱高温', 'en': 'Level 3 fault pole high temperature'},
    '三级故障PCS故障': {'zh': '三级故障PCS故障', 'en': 'Level 3 fault PCS fault'},
    '三级故障EMS故障': {'zh': '三级故障EMS故障', 'en': 'Level 3 malfunction EMS malfunction'},
    '三级保险丝故障': {'zh': '三级保险丝故障', 'en': 'Third level fuse malfunction'},
    '三级反馈异常': {'zh': '三级反馈异常', 'en': 'Level 3 feedback abnormality'},
    '三级极限故障': {'zh': '三级极限故障', 'en': 'Level 3 extreme fault'},
    '三级开路': {'zh': '三级开路', 'en': 'Third level open circuit'}, '三级急停': {'zh': '三级急停', 'en': 'Level 3 emergency stop'},
    '三级控制指令超时': {'zh': '三级控制指令超时', 'en': 'Third level control instruction timeout'},
    '三级故障电池箱过压': {'zh': '三级故障电池箱过压', 'en': 'Level 3 fault battery box overvoltage'},
    '三级故障电池箱欠压': {'zh': '三级故障电池箱欠压', 'en': 'Level 3 fault battery box undervoltage'},
    '三级水冷机故障': {'zh': '三级水冷机故障', 'en': 'Third stage water chiller malfunction'},
    '三级水浸故障': {'zh': '三级水浸故障', 'en': 'Level 3 water immersion fault'},
    '三级故障安全功能故障': {'zh': '三级故障安全功能故障', 'en': 'Level 3 fault safety function failure'},
    '三级故障水冷机自身故障': {'zh': '三级故障水冷机自身故障', 'en': 'Level 3 malfunction: The water chiller itself is malfunctioning'},
    '三级故障电表通讯故障': {'zh': '三级故障电表通讯故障', 'en': 'Level 3 fault: Communication failure of electric meter'},
    '三级故障Pack级消防故障': {'zh': '三级故障Pack级消防故障', 'en': 'Level 3 Fault Pack Level Fire Failure'},
    '三级故障霍尔超量程': {'zh': '三级故障霍尔超量程', 'en': 'Level 3 fault Hall overrange'},
    '无告警': {'zh': '无告警', 'en': 'no alarm'}, '有告警': {'zh': '有告警', 'en': 'There is an alarm'},
    '一级故障': {'zh': '一级故障', 'en': 'Level 1 malfunction'}, '二级故障': {'zh': '二级故障', 'en': 'Secondary fault'},
    '三级故障': {'zh': '三级故障', 'en': 'Level 3 malfunction'}

}
