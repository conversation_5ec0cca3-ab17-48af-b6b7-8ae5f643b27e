from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Column, VARCHAR


class PointType(user_Base):
    u'设备类型'
    __tablename__ = "t_point_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    equipment_name = Column(VARCHAR(128), nullable=False, comment=u"设备类型名称")
    en_equipment_name = Column(VARCHAR(128), nullable=False, comment=u"设备类型名称-英文")
    station = Column(VARCHAR(32), nullable=False, comment=u"电站")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
