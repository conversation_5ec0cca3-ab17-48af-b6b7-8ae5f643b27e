#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Tools\DB\mysql_scada.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:11:13


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
DEBUG = model_config.get('broker', "log_debug")
# scada库连接 mysql

SCADA_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
SCADA_PORT = model_config.get('mysql', "DB_PORT")
SCADA_DATABASE = model_config.get('mysql', "DB_DATABASE")
SCADA_USERNAME = model_config.get('mysql', "DB_USERNAME")
SCADA_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

scadadb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SCADA_USERNAME,
    SCADA_PASSWORD,
    SCADA_HOSTNAME,
    SCADA_PORT,
    SCADA_DATABASE
)
scada_engine = create_engine(scadadb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=20, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

# PostgreSQL  连接
# SCADA_HOSTNAME = model_config.get('postgresql', "DB_HOSTNAME")
# SCADA_PORT = model_config.get('postgresql', "DB_PORT")
# SCADA_DATABASE = model_config.get('postgresql', "DB_DATABASE")
# SCADA_USERNAME = model_config.get('postgresql', "DB_USERNAME")
# SCADA_PASSWORD = model_config.get('postgresql', "DB_PASSWORD")

# scadadb_mysql_url='postgresql+psycopg2://{}:{}@{}:{}/{}'.format(
#     SCADA_USERNAME,
#     SCADA_PASSWORD,
#     SCADA_HOSTNAME,
#     SCADA_PORT,
#     SCADA_DATABASE
# )
# scada_engine = create_engine(scadadb_mysql_url,
#                        echo=(os.getenv('DEBUG_DB', 'null') != 'null'),
#                        max_overflow=10, pool_size=100, pool_timeout=30, pool_recycle=-1)


_scada_session = scoped_session(sessionmaker(scada_engine,autoflush=True))

scada_Base = declarative_base(scada_engine)
scada_session = _scada_session()

mqtt_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
mqtt_PORT = model_config.get('mysql', "DB_PORT")
mqtt_DATABASE = "sda_mqttstation"
mqtt_USERNAME = model_config.get('mysql', "DB_USERNAME")
mqtt_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

mqttdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    mqtt_USERNAME,
    mqtt_PASSWORD,
    mqtt_HOSTNAME,
    mqtt_PORT,
    mqtt_DATABASE
)
mqtt_engine = create_engine(mqttdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=20, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_mqtt_session = scoped_session(sessionmaker(mqtt_engine,autoflush=True))

mqtt_Base = declarative_base(mqtt_engine)
mqtt_session = _mqtt_session()


