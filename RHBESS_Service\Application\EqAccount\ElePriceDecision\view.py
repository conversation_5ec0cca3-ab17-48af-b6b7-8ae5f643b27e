import asyncio
import logging
import os
import traceback
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
import concurrent.futures

import math
import openpyxl
import requests
import tornado.web
from sqlalchemy import func

from Application.EqAccount.ElePriceDecision.tools import merge_dicts, merge_dicts_with_default, split_time
from Application.Models.base_handler_ele import BaseHandler
from Application.Models.ElePriceDescision.models import *
from Tools.DecisionDB.ele_base import get_user_session
from Tools.Utils.mimio_tool import MinioTool


async def upload_file(file_name, file_path, bucket_name='rhyc'):
    minioClient = MinioTool()
    minioClient.create_bucket(bucket_name)
    url = minioClient.upload_local_file(file_name, file_path)
    print('文件地址为【文件在浏览器打开会直接下载，放到index.html 中使用img引入查看】：\n', url)
    return url


class GetCProvinces(BaseHandler):
    """省份列表"""""

    async def get_counties(self, city_id, user_session):
        return [{"id": county.id, "name": county.name} for county in
                user_session.query(CCounty).filter(CCounty.city_id == city_id).all() if county.is_use == "1"]

    @tornado.web.authenticated
    async def get(self):
        user_session_ = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()
            provinces = user_session_.query(CProvince).all()
            provinces_ = [{"id": province.id, "name": province.name} for province in provinces if province.is_use == '1'
                          and province.name != "中国"]

            for province in provinces_:
                cities = user_session_.query(CCity).filter(CCity.province_id == province["id"]).all()
                cities_ = [{"id": city.id, "name": city.name} for city in cities if city.is_use == '1']
                province["cities"] = cities_

                tasks = []
                for city in province["cities"]:
                    tasks.append(self.get_counties(city["id"], user_session_))
                results = await asyncio.gather(*tasks)
                for i, city in enumerate(province["cities"]):
                    counties_ = results[i]
                    city["counties"] = counties_

                    # counties = user_session.query(CCounty).filter(CCounty.city_id == city["id"]).all()
                    # counties_ = [{"id": county.id, "name": county.name} for county in counties if county.is_use == "1"]
                    # city["counties"] = counties_

            # user_session.close()
            self.returnTypeSuc(provinces_)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session_.close()


class GetSOP(BaseHandler):
    """获取交易SOP流程图"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()
            province_id = self.get_argument("province_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province = user_session.query(CProvince).filter(CProvince.id == province_id).first()
            if not province:
                return self.customError("省份不存在")

            province_name = province.name
            minioClient = MinioTool()
            filename = province_name + "省_SOP.png"
            sop_url = minioClient.get_download_url('rhyc', filename)

            # user_session.close()
            self.returnTypeSuc(sop_url)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


RealMeteorologicalSigns = {
    "tem": "温度（℃）",
    "feel_tem": "体感温度（℃）",
    "weather_con": "天气状况",
    "wind_angle": "风向角（°）",
    "wind": "风向",
    "wind_power": "风力等级",
    "wind_speed": "风速（km/h）",
    "hum": "相对湿度（%）",
    "rain_hour": "小时累积降水量（mm）",
    "rain_probab": "降水概率（%）",
    "air_press": "大气压强（hPa）",
    "see": "能见度（km）",
    "cloud_cover": "云量（%）",
    "dew_tem": "露点温度（°）",
}

PreMeteorologicalSigns = {
    "tem": "温度（℃）",
    "weather_con": "天气状况",
    "wind_angle": "风向角（°）",
    "wind": "风向",
    "wind_power": "风力等级",
    "wind_speed": "风速（km/h）",
    "hum": "相对湿度（%）",
    "rain_hour": "小时累积降水量（mm）",
    "rain_probab": "降水概率（%）",
    "air_press": "大气压强（hPa）",
    "cloud_cover": "云量（%）",
    "dew_tem": "露点温度（°）"
}

sign_filename_maps = {
    "tem": "温度",
    "wind": "风向",
    "air_press": "大气压强",
    "wind_speed": "风速",
    "see": "能见度",
    "rain_probab": "降水概率",
    "hum": "相对湿度",
    "cloud_cover": "云量"
}


class GetMeteorologicalData(BaseHandler):
    """获取气象地图数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            # city_id = self.get_argument("city_id", default=None)
            # county_id = self.get_argument("county_id", default=None)
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            date_type = self.get_argument("date_type", default="real")
            sign = self.get_argument("sign", default="tem")

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            # # 校验参数city_id
            # if city_id:
            #     cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
            #     cities_id = [city.id for city in cities if city.is_use == '1']
            #     if int(city_id) not in cities_id:
            #         return self.customError("城市/区ID参数错误")
            #
            #     # 校验参数county_id
            #     if county_id:
            #         counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
            #         counties_id = [county.id for county in counties if county.is_use == "1"]
            #         if int(county_id) not in counties_id:
            #             return self.customError("县ID参数错误")
            #
            # if county_id and not city_id:
            #     return self.customError("城市/区ID参数缺失")

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数date_type
            if date_type not in ["real", "pre"]:
                return self.customError("参数date_type错误")

            # 校验参数sign
            if date_type == "real":
                if sign not in sign_filename_maps.keys():
                    return self.customError(f"实时气象数据无{sign}数据")
            else:
                if sign not in sign_filename_maps.keys():
                    return self.customError(f"预报天气数据无{sign}数据")

            # 查询省的数据
            today = datetime.datetime.today()
            minio_client = MinioTool()

            data_1 = []
            data_2 = []

            start_day_ = datetime.datetime.strptime('2024-03-18', "%Y-%m-%d")

            if end_day.date() < start_day_.date():
                return self.customError("所选时间范围内无数据")

            if end_day.date() < today.date():
                end_day_ = end_day
            else:
                end_day_ = today

            # if start_day_.date() <= start_day.date() <= today.date():
            #     begin_day = start_day
            # else:
            begin_day = start_day_

            target_day = end_day_
            total = (target_day - begin_day).days
            for i in range(total+1):
                day_str = (target_day - datetime.timedelta(days=i)).strftime("%Y%m%d")
                filename_1 = f"{sign_filename_maps.get(sign)}_{str(province_id)}_{day_str}_S.json"
                filename_2 = f"{sign_filename_maps.get(sign)}_{str(province_id)}_{day_str}_F.json"
                try:
                    # stat_object判断文件是否存在；不存在则捕获异常，存在继续执行
                    minio_client.minioClient.stat_object('shanxi', filename_1)
                    minio_client.minioClient.stat_object('shanxi', filename_2)
                    url_1 = minio_client.get_download_url('shanxi', filename_1)
                    url_2 = minio_client.get_download_url('shanxi', filename_2)
                    data_1 = requests.get(url_1).json()
                    data_2 = requests.get(url_2).json()
                    break
                except Exception as e:
                    pass
            # while begin_day.date() <= target_day.date() <= end_day_.date():
            #     try:
            #         day_str = target_day.strftime("%Y%m%d")
            #         # day_str_ = target_day.strftime("%Y-%m-%d")
            #         filename_1 = f"{sign_filename_maps.get(sign)}_{str(province_id)}_{day_str}_S.json"
            #         url_1 = minio_client.get_download_url('shanxi', filename_1)
            #
            #         data_1 = requests.get(url_1).json()
            #
            #         filename_2 = f"{sign_filename_maps.get(sign)}_{str(province_id)}_{day_str}_F.json"
            #         url_2 = minio_client.get_download_url('shanxi', filename_2)
            #
            #         data_2 = requests.get(url_2).json()
            #
            #         break
            #     except Exception as e:
            #         logging.info(e)
            #         target_day -= datetime.timedelta(days=1)

            if not data_1 and not data_2:
                return self.customError("获取数据失败")
            else:
                data = {
                    "province_id": province_id,
                    "start_day": start_day.strftime("%Y-%m-%d"),
                    "end_day": end_day.strftime("%Y-%m-%d"),
                    "date_type": date_type,
                    "sign": sign,
                    "data_1": data_1,
                    "data_2": data_2
                }
                self.returnTypeSuc(data)

        except Exception as e:
            logging.error(e)
            print(279, traceback.print_exc())
            return self.requestError()
        finally:
            user_session.close()

class GetMeteorologicalCityData(BaseHandler):
    """获取气象地图城市数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            city_id = self.get_argument("city_id", default=None)
            county_id = self.get_argument("county_id", default=None)
            # start_day = self.get_argument("start_day", default=(datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%Y-%m-%d"))
            day_ = self.get_argument("day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            moment_ = self.get_argument("moment", default=datetime.datetime.now().strftime("%H:%M"))
            date_type = self.get_argument("date_type", default="real")
            # sign = self.get_argument("sign", default="tem")

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID缺失或不存在")

            # 校验参数city_id
            if city_id:
                cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
                cities_id = [city.id for city in cities if city.is_use == '1']
                if int(city_id) not in cities_id:
                    return self.customError("城市/区ID参数错误")

            #     # 校验参数county_id
            #     if county_id:
            #         counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
            #         counties_id = [county.id for county in counties if county.is_use == "1"]
            #         if int(county_id) not in counties_id:
            #             return self.customError("县ID参数错误")
            #
            # if county_id and not city_id:
            #     return self.customError("城市/区ID参数缺失")

            # 校验参数start_day和end_day
            # try:
            #     start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            #     end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            # except Exception as e:
            #     logging.error(e)
            #     return self.customError("日期参数错误")
            #
            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            # if start_day > end_day:
            #     return self.customError("开始日期不能大于结束日期")

            # 校验参数start_day和end_day
            try:
                day = datetime.datetime.strptime(day_, "%Y-%m-%d")
                moment = datetime.datetime.strptime(day_ + ' ' + moment_, "%Y-%m-%d %H:%M")
            except Exception as e:
                logging.error(e)
                return self.customError("日期或时间参数格式错误")

            # 校验参数date_type
            if date_type not in ["real", "pre"]:
                return self.customError("参数date_type错误")

            # # 校验参数sign
            # if date_type == "real":
            #     if sign not in RealMeteorologicalSigns.__members__:
            #         return self.customError(f"实时气象数据无{sign}数据")
            # else:
            #     if sign not in PreMeteorologicalSigns.__members__:
            #         return self.customError(f"预报天气数据无{sign}数据")

            detail = {}
            # start = datetime.datetime.now()
            # print('start:', start)
            # 查询省的数据
            if not city_id and province_id:
                if date_type == "real":
                    data = user_session.query(RWeatherReal).filter(
                        RWeatherReal.day == day,
                        RWeatherReal.flag == '2',
                        RWeatherReal.province_id == int(province_id)).all()
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day == day,
                        RWeatherPre.flag == '2',
                        RWeatherPre.province_id == int(province_id)).all()

                # start1 = datetime.datetime.now()
                # print('start1:', start1 - start)

                # 处理返回数据
                temp_list = []
                if data:
                    for d in data:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        if datetime.datetime.strptime(datetime_, "%Y-%m-%d %H:%M") <= moment:
                            if date_type == "real":
                                temp_dict = {
                                    "datetime": datetime_,
                                    "moment": datetime_.split(" ")[1],
                                    "longitude": float(d.longitude),
                                    "latitude": float(d.latitude),
                                    "province": d.province.name,
                                    "city": d.city.name,
                                    # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                                    "tem": float(d.tem),
                                    "feel_tem": float(d.feel_tem),
                                    "weather_con": d.weather_con,
                                    "wind_angle": d.wind_angle,
                                    "wind": d.wind,
                                    "wind_power": d.wind_power,
                                    "wind_speed": float(d.wind_speed),
                                    "hum": d.hum,
                                    "rain_hour": d.rain_hour,
                                    "rain_probab": d.rain_probab,
                                    "air_press": d.air_press,
                                    "see": float(d.see),
                                    "cloud_cover": d.cloud_cover,
                                    "dew_tem": float(d.dew_tem)
                                }
                            else:
                                temp_dict = {
                                    "datetime": datetime_,
                                    "moment": datetime_.split(" ")[1],
                                    "longitude": float(d.longitude),
                                    "latitude": float(d.latitude),
                                    "province": d.province.name,
                                    "city": d.city.name,

                                    "tem": float(d.tem),
                                    # "feel_tem": float(d.feel_tem),
                                    "weather_con": d.weather_con,
                                    "wind_angle": d.wind_angle,
                                    "wind": d.wind,
                                    "wind_power": d.wind_power,
                                    "wind_speed": float(d.wind_speed),
                                    "hum": d.hum,
                                    "rain_hour": d.rain_hour,
                                    "rain_probab": d.rain_probab,
                                    "air_press": d.air_press,
                                    # "see": float(d.see),
                                    "cloud_cover": d.cloud_cover,
                                    "dew_tem": float(d.dew_tem)
                                }
                            temp_list.append(temp_dict)

                    # start2 = datetime.datetime.now()
                    # print('start2:', start2 - start1)
                    # temp_list按照 city 字段分组
                    grouped_datas = defaultdict(list)
                    for i in temp_list:
                        grouped_datas[i['city']].append(i)

                    # start3 = datetime.datetime.now()
                    # print('start3:', start3 - start2)

                    detail = []
                    for k, v in grouped_datas.items():
                        if len(v) == 1:
                            city_d = v[0]
                        else:
                            p_list = sorted(v, key=lambda x: x['datetime'])
                            city_d = p_list[-1]
                        detail.append(city_d)
                    # start4 = datetime.datetime.now()
                    # print('start4:', start4 - start3)

            # 查询市的数据
            elif city_id and provinces_id:
                # elif not county_id and city_id:
                if date_type == "real":
                    data = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.day == day,
                        RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id),
                        RWeatherReal.flag == '2').all())
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day == day,
                        RWeatherPre.province_id == int(province_id),
                        RWeatherPre.city_id == int(city_id),
                        RWeatherPre.flag == '2',).all()

                # 处理返回数据
                temp_list = []
                if data:
                    for d in data:
                        datetime_ = d.day.strftime("%Y-%m-%d") + " " + d.moment
                        if datetime.datetime.strptime(datetime_, "%Y-%m-%d %H:%M") <= moment:
                            if date_type == "real":
                                temp_dict = {
                                    "datetime": datetime_,
                                    "moment": datetime_.split(" ")[1],
                                    "longitude": float(d.longitude),
                                    "latitude": float(d.latitude),
                                    "province": d.province.name,
                                    "city": d.city.name,
                                    # "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                                    "tem": float(d.tem),
                                    "feel_tem": float(d.feel_tem),
                                    "weather_con": d.weather_con,
                                    "wind_angle": d.wind_angle,
                                    "wind": d.wind,
                                    "wind_power": d.wind_power,
                                    "wind_speed": float(d.wind_speed),
                                    "hum": d.hum,
                                    "rain_hour": d.rain_hour,
                                    "rain_probab": d.rain_probab,
                                    "air_press": d.air_press,
                                    "see": float(d.see),
                                    "cloud_cover": d.cloud_cover,
                                    "dew_tem": float(d.dew_tem)
                                }
                            else:
                                temp_dict = {
                                    "datetime": datetime_,
                                    "moment": datetime_.split(" ")[1],
                                    "longitude": float(d.longitude),
                                    "latitude": float(d.latitude),
                                    "province": d.province.name,
                                    "city": d.city.name,

                                    "tem": float(d.tem),
                                    # "feel_tem": float(d.feel_tem),
                                    "weather_con": d.weather_con,
                                    "wind_angle": d.wind_angle,
                                    "wind": d.wind,
                                    "wind_power": d.wind_power,
                                    "wind_speed": float(d.wind_speed),
                                    "hum": d.hum,
                                    "rain_hour": d.rain_hour,
                                    "rain_probab": d.rain_probab,
                                    "air_press": d.air_press,
                                    # "see": float(d.see),
                                    "cloud_cover": d.cloud_cover,
                                    "dew_tem": float(d.dew_tem)
                                }
                            temp_list.append(temp_dict)

                    # temp_list按照 datetime 字段分组
                    # grouped_datas = defaultdict(list)
                    # for i in temp_list:
                    #     grouped_datas[i['datetime']].append(i)

                    if temp_list:
                        temp_list = sorted(temp_list, key=lambda x: x['datetime'])
                        detail = temp_list[-1]
                    else:
                        detail = {}

            # 查询县的数据
            # else:
            #     if date_type == "real":
            #         data = (user_session.query(RWeatherReal).filter(
            #             RWeatherReal.day == day,
            #             RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
            #             RWeatherReal.city_id == int(city_id),
            #             RWeatherReal.county_id == int(county_id))
            #                 .all())
            #     else:
            #         data = (user_session.query(RWeatherPre).filter(
            #             RWeatherPre.day == day,
            #             RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
            #             RWeatherPre.city_id == int(city_id),
            #             RWeatherPre.county_id == int(county_id))
            #                 .all())

            temp_map_dict = {}
            if date_type == "real":
                for k, v in RealMeteorologicalSigns.items():
                    temp_map_dict[k] = v
            else:
                for k, v in PreMeteorologicalSigns.items():
                    temp_map_dict[k] = v

            data_ = {
                "detail": detail,
                "maps": temp_map_dict
            }

            # user_session.close()
            self.returnTypeSuc(data_)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class GetMeteorologicalDataForTable(BaseHandler):
    """获取气象表格数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id", default=None)
            city_id = self.get_argument("city_id", default=None)
            county_id = self.get_argument("county_id", default=None)
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            date_type = self.get_argument("date_type", default="real")
            page = int(self.get_argument("page")) if self.get_argument("page", None) else None
            page_size = int(self.get_argument("page_size")) if self.get_argument("page_size", None) else 33
            # sign = self.get_argument("sign", default="tem")

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID缺失或不存在")

            # 校验参数city_id
            if city_id:
                cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
                cities_id = [city.id for city in cities if city.is_use == '1']
                if int(city_id) not in cities_id:
                    return self.customError("城市/区ID不存在")

                # 校验参数county_id
                if county_id:
                    counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                    counties_id = [county.id for county in counties if county.is_use == "1"]
                    if int(county_id) not in counties_id:
                        return self.customError("区县ID不存在")

            if county_id and not city_id:
                return self.customError("城市/区ID参数缺失")

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数date_type
            if date_type not in ["real", "pre"]:
                return self.customError("参数date_type错误")

            # # 校验参数sign
            # if date_type == "real":
            #     if sign not in RealMeteorologicalSigns.__members__:
            #         return self.customError(f"实时气象数据无{sign}数据")
            # else:
            #     if sign not in PreMeteorologicalSigns.__members__:
            #         return self.customError(f"预报天气数据无{sign}数据")

            # 查询省的数据
            if not county_id and not city_id:
                if date_type == "real":
                    data = user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.province_id == int(province_id)).all()
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.province_id == int(province_id)).all()

            # 查询市的数据
            elif not county_id and city_id:
                if date_type == "real":
                    data = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id))
                            .all())
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.province_id == int(province_id),
                        RWeatherPre.city_id == int(city_id)).all()

            # 查询县的数据
            else:
                if date_type == "real":
                    data = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id),
                        RWeatherReal.county_id == int(county_id))
                            .all())
                else:
                    data = (user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
                        RWeatherPre.city_id == int(city_id),
                        RWeatherPre.county_id == int(county_id))
                            .all())

            # 处理返回数据
            temp_list = []
            if data:
                if date_type == "real":
                    for d in data:
                        for moment in split_time(d.moment):  # 将小时数据拆分成15分钟一组数据，同小时内的数据复制4份
                            temp_dict = {
                                "day": d.day.strftime("%Y-%m-%d"),
                                "moment": moment,
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + moment,
                                "province": d.province.name,
                                "city": d.city.name,
                                "county": d.county.name if d.county else d.city.name,
                                "longitude": float(d.longitude),
                                "latitude": float(d.latitude),

                                "tem": float(d.tem),
                                "feel_tem": float(d.feel_tem),
                                "weather_con": d.weather_con,
                                "wind_angle": d.wind_angle,
                                "wind": d.wind,
                                "wind_power": d.wind_power,
                                "wind_speed": float(d.wind_speed),
                                "hum": d.hum,
                                "rain_hour": d.rain_hour,
                                "rain_probab": d.rain_probab,
                                "air_press": d.air_press,
                                "see": float(d.see),
                                "cloud_cover": d.cloud_cover,
                                "dew_tem": float(d.dew_tem)
                            }
                            temp_list.append(temp_dict)
                else:
                    for d in data:
                        for moment in split_time(d.moment):  # 将小时数据拆分成15分钟一组数据，同小时内的数据复制4份
                            temp_dict = {
                                "day": d.day.strftime("%Y-%m-%d"),
                                "moment": moment,
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + moment,
                                "province": d.province.name,
                                "city": d.city.name,
                                "county": d.county.name if d.county else d.city.name,
                                "longitude": float(d.longitude),
                                "latitude": float(d.latitude),

                                "tem": float(d.tem),
                                "weather_con": d.weather_con,
                                "wind_angle": d.wind_angle,
                                "wind": d.wind,
                                "wind_power": d.wind_power,
                                "wind_speed": float(d.wind_speed),
                                "hum": d.hum,
                                "rain_hour": d.rain_hour,
                                "rain_probab": d.rain_probab,
                                "air_press": d.air_press,
                                "cloud_cover": d.cloud_cover,
                                "dew_tem": float(d.dew_tem)
                            }
                            temp_list.append(temp_dict)

            # # temp_list按照 datetime 字段分组
            # grouped_datas = defaultdict(list)
            # for i in temp_list:
            #     grouped_datas[i['datetime']].append(i)

            data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

            temp_map_dict = {}
            temp_map_dict.update(day='日期', moment='时间', county='地区', longitude='经度', latitude='纬度')
            if date_type == "real":
                for k, v in RealMeteorologicalSigns.items():
                    temp_map_dict[k] = v
            else:
                for k, v in PreMeteorologicalSigns.items():
                    temp_map_dict[k] = v

            # 手动分页
            if page:
                total_pages = math.ceil(len(data) / page_size)
                start_index = (page - 1) * page_size
                end_index = page * page_size if page < total_pages else len(data) + 1

                data_ = {
                    "total": len(data),
                    "total_pages": total_pages,
                    "page": page,
                    "detail": data[start_index:end_index],
                    "maps": temp_map_dict
                }
            else:
                data_ = {
                    "total": len(data),
                    "total_pages": 1,
                    "page": page,
                    "detail": data,
                    "maps": temp_map_dict
                }

            # user_session.close()
            self.returnTypeSuc(data_)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class ExportMeteorologicalDataForTable(BaseHandler):
    """导出气象表格数据"""""

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            province_id = self.get_argument("province_id")
            city_id = self.get_argument("city_id", default=None)
            county_id = self.get_argument("county_id", default=None)
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            date_type = self.get_argument("date_type", default="real")
            # page = int(self.get_argument("page", default="1"))
            # page_size = 33
            # sign = self.get_argument("sign", default="tem")

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            # 校验参数city_id
            if city_id:
                cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
                cities_id = [city.id for city in cities if city.is_use == '1']
                if int(city_id) not in cities_id:
                    return self.customError("城市/区ID参数错误")

                # 校验参数county_id
                if county_id:
                    counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                    counties_id = [county.id for county in counties if county.is_use == "1"]
                    if int(county_id) not in counties_id:
                        return self.customError("县ID参数错误")

            if county_id and not city_id:
                return self.customError("城市/区ID参数缺失")

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数date_type
            if date_type not in ["real", "pre"]:
                return self.customError("参数date_type错误")

            # # 校验参数sign
            # if date_type == "real":
            #     if sign not in RealMeteorologicalSigns.__members__:
            #         return self.customError(f"实时气象数据无{sign}数据")
            # else:
            #     if sign not in PreMeteorologicalSigns.__members__:
            #         return self.customError(f"预报天气数据无{sign}数据")

            # 查询省的数据
            if not county_id and not city_id:
                if date_type == "real":
                    data = user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.province_id == int(province_id)).all()
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.province_id == int(province_id)).all()

            # 查询市的数据
            elif not county_id and city_id:
                if date_type == "real":
                    data = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id))
                            .all())
                else:
                    data = user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.province_id == int(province_id),
                        RWeatherPre.city_id == int(city_id)).all()

            # 查询县的数据
            else:
                if date_type == "real":
                    data = (user_session.query(RWeatherReal).filter(
                        RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                        RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
                        RWeatherReal.city_id == int(city_id),
                        RWeatherReal.county_id == int(county_id))
                            .all())
                else:
                    data = (user_session.query(RWeatherPre).filter(
                        RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                        RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
                        RWeatherPre.city_id == int(city_id),
                        RWeatherPre.county_id == int(county_id))
                            .all())

            # 处理返回数据
            temp_list = []
            if data:
                if date_type == "real":
                    for d in data:
                        for moment in split_time(d.moment):
                            temp_dict = {
                                "day": d.day.strftime("%Y-%m-%d"),
                                "moment": moment,
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + moment,
                                "province": d.province.name,
                                "city": d.city.name,
                                "county": d.county.name if d.county else d.city.name,
                                "longitude": float(d.longitude),
                                "latitude": float(d.latitude),

                                "tem": float(d.tem),
                                "feel_tem": float(d.feel_tem),
                                "weather_con": d.weather_con,
                                "wind_angle": d.wind_angle,
                                "wind": d.wind,
                                "wind_power": d.wind_power,
                                "wind_speed": float(d.wind_speed),
                                "hum": d.hum,
                                "rain_hour": d.rain_hour,
                                "rain_probab": d.rain_probab,
                                "air_press": d.air_press,
                                "see": float(d.see),
                                "cloud_cover": d.cloud_cover,
                                "dew_tem": float(d.dew_tem)
                            }
                            temp_list.append(temp_dict)
                else:
                    for d in data:
                        for moment in split_time(d.moment):
                            temp_dict = {
                                "day": d.day.strftime("%Y-%m-%d"),
                                "moment": moment,
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + moment,
                                "province": d.province.name,
                                "city": d.city.name,
                                "county": d.county.name if d.county else d.city.name,
                                "longitude": float(d.longitude),
                                "latitude": float(d.latitude),

                                "tem": float(d.tem),
                                "weather_con": d.weather_con,
                                "wind_angle": d.wind_angle,
                                "wind": d.wind,
                                "wind_power": d.wind_power,
                                "wind_speed": float(d.wind_speed),
                                "hum": d.hum,
                                "rain_hour": d.rain_hour,
                                "rain_probab": d.rain_probab,
                                "air_press": d.air_press,
                                "cloud_cover": d.cloud_cover,
                                "dew_tem": float(d.dew_tem)
                            }
                            temp_list.append(temp_dict)

            # # temp_list按照 datetime 字段分组
            # grouped_datas = defaultdict(list)
            # for i in temp_list:
            #     grouped_datas[i['datetime']].append(i)

            data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

            # 获取地区名称
            if not county_id and not city_id:
                location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
            elif city_id and not county_id:
                province = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
                location = province + '-' + user_session.query(CCity).filter(CCity.id == int(city_id)).first().name
            elif county_id:
                province = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
                city = province + '-' + user_session.query(CCity).filter(CCity.id == int(city_id)).first().name
                location = city + '-' + user_session.query(CCounty).filter(CCounty.id == int(county_id)).first().name
            else:
                location = ''

            # 上传并返回下载url
            workbook = openpyxl.Workbook()
            sheet1 = workbook.active

            BASE_DIR = Path(__file__).resolve().parent
            start_day_str = start_day.strftime("%Y-%m-%d")
            end_day_str = end_day.strftime("%Y-%m-%d")
            file_name = f"{start_day_str}~{end_day_str}{location}气象数据.xlsx"
            path = os.path.join(BASE_DIR, file_name)
            sheet1.title = '气象数据'

            if date_type == 'real':
                table_titles = ["日期", "时间", "地区", "经度", "纬度", "温度（℃）", "体感温度（℃）", "天气状况", "风向角（°）", "风向",
                                "风力等级",
                                "风速（km/h）", "相对湿度（%）", "小时累积降水量（mm）", "降水概率（%）", "大气压强（hPa）", "能见度", "云量（%）", "露点温度（°）"]
            else:
                table_titles = ["日期", "时间", "地区", "经度", "纬度", "温度（℃）", "天气状况", "风向角（°）", "风向", "风力等级",
                                "风速（km/h）", "相对湿度（%）", "小时累积降水量（mm）", "降水概率（%）", "大气压强（hPa）", "云量（%）", "露点温度（°）"]
            # sheet1 写入标头
            for col_num, header in enumerate(table_titles, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}1'] = header

            # 准备数据
            data_array = list()
            if data:
                if date_type == 'real':
                    for t in data:
                        li = [t['day'],
                              t['moment'],
                              t['county'] if t['county'] else t['city'],
                              t['longitude'],
                              t['latitude'],
                              t['tem'],
                              t['feel_tem'],
                              t['weather_con'],
                              t['wind_angle'],
                              t['wind'],
                              t['wind_power'],
                              t['wind_speed'],
                              t['hum'],
                              t['rain_hour'],
                              t['rain_probab'],
                              t['air_press'],
                              t['see'],
                              t['cloud_cover'],
                              t['dew_tem']]

                        data_array.append(li)
                else:
                    for t in data:
                        li = [t['day'],
                              t['moment'],
                              t['county'] if t['county'] else t['city'],
                              t['longitude'],
                              t['latitude'],
                              t['tem'],
                              t['weather_con'],
                              t['wind_angle'],
                              t['wind'],
                              t['wind_power'],
                              t['wind_speed'],
                              t['hum'],
                              t['rain_hour'],
                              t['rain_probab'],
                              t['air_press'],
                              t['cloud_cover'],
                              t['dew_tem']]

                        data_array.append(li)
                # sorted(data_array, key=lambda x: x[0])

            # 写入工作表
            for row_num, row_data in enumerate(data_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet1[f'{col_letter}{row_num}'] = cell_value if cell_value is not None and cell_value != '--' else ''
            workbook.save(path)

            # 对上传文件大小做限值，超过40M时提示查询指标太多，数据量太大，请重新选择
            if os.path.getsize(path) > 1 * 1024 * 1024:
                # print(1052, "查询指标太多，数据量太大，请重新选择")
                try:
                    os.remove(path)
                except Exception as e:
                    pass
                return self.customError("查询指标太多，数据量太大，请重新选择")

            url = await upload_file(file_name, path)

            try:
                os.remove(path)
            except Exception as e:
                pass

            # user_session.close()
            return self.returnTypeSuc(url)
        except Exception as e:
            logging.error(e)
            return self.requestError()
        finally:
            user_session.close()


class MarketInfoDataTypes(Enum):
    current = "日前市场",
    realtime = "实时市场",
    overview = "总览",


class MarketInfoSigns(Enum):
    clear_data = "出清数据",
    new_energy = "新能源出力",
    total_load = "发电总出力",


all_options = {
    "current": {
        "clear_data": {
            "title": "出清数据",
            "options": ["日前出清电量", "日前出清价格"],
        },
        "new_energy": {
            "title": "新能源出力",
            "options": ["预测光伏出力", "预测风电出力", "预测新能源总出力"]
        },
        "total_load": {
            "title": "发电总出力",
            "options": ["日前非市场化机组出力预测", "机组检修总容量", "日前联络线计划信息（加总）", "预测新能源总出力", "全省用电负荷预测信息"],
        }
    },
    "realtime": {
        "clear_data": {
            "title": "出清数据",
            "options": ["实时出清电量", "实时出清价格"],
        },
        "new_energy": {
            "title": "新能源出力",
            "options": ["光伏实际值", "风电实际值", "新能源总加实际值"]
        },
        "total_load": {
            "title": "发电总出力",
            "options": ["实时非市场化机组出力曲线", "新能源总实时出力", "实时联络线计划（加总）", "水电总实时出力", "系统负荷实际值"],
        }
    },
    "overview": {
        "power_trend_1": {
            "title": "电价趋势",
            "options": ["日前出清价格", "预测出清价格", "实时出清价格", "预测实时价格"],
        },
        "load_trend": {
            "title": "负荷及出清量趋势",
            "options": ["预测光伏出力", "预测风电出力", "预测外送联络", "预测机组出力", "预测发电总出力",
                        "实际光伏出力", "实际风电出力", "实际外送联络", "实际机组出力", "实际发电总出力"]
        },
        "power_trend_2": {
            "title": "电源趋势",
            "options": ["日前出清电量", "日前预测负荷", "实时出清电量", "实时实际负荷"],
        }
    },
}


class MarketInfoOptions(BaseHandler):
    @tornado.web.authenticated
    def get(self):
        # user_session = get_user_session()
        self.refreshSession()
        date_type = self.get_argument("date_type", default="realtime")

        # 校验参数date_type
        if date_type not in all_options.keys():
            return self.customError(f"参数date_type: {date_type}错误")

        temp_array = []

        for k,v in all_options[date_type].items():
            temp_dict = {
                "key": k,
                "name": v['title'],
                "sub_items": v['options']
            }
            temp_array.append(temp_dict)

        self.returnTypeSuc(all_options)


class MarketInfo(BaseHandler):
    """当前市场/实时市场/总览--出清数据/新能源出力/总负荷/总览数据...."""""

    @staticmethod
    async def get_series_data(start_day, end_day, province_id, table_name, user_session):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id
        
        if dict_serie_id == 13:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.name == "总加",
                                                                 RSeriesDataShanxi.is_use == "1").all())
        else:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == dict_serie_id,
                                                                 RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else "--",
            }
            temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            date_type = self.get_argument("date_type", default="real")
            sign = self.get_argument("sign", default=None)
            options = self.get_argument("options", default=None)

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数date_type
            if date_type not in all_options.keys():
                return self.customError(f"参数date_type: {date_type}错误")

            # 校验参数sign
            if sign not in all_options[date_type].keys():
                return self.customError(f"参数sign:{sign}错误")

            # 校验参数options
            if options:
                options = options.split(",")
                for option in options:
                    if option not in all_options[date_type][sign]['options']:
                        return self.customError(f"参数options: '{option}'错误")
            else:
                options = all_options[date_type][sign]['options']

            temp_array = []
            temp_map_dict = {}
            # 日前市场
            if date_type == "current":

                # 日前市场--出清数据
                if sign == MarketInfoSigns.clear_data.name:
                    temp_map_dict = {
                        # "value1": "日前出清电量",
                        # "value2": "日前出清价格"
                    }
                    for option in options:
                        # 日前出清电量
                        if option == "日前出清电量":
                            temp_map_dict['value1'] = '日前出清电量'
                            data1 = await self.get_series_data(start_day, end_day, province_id, "日前出清电量", user_session)
                            if data1:
                                temp_list_1 = []
                                for i in range(len(data1)):
                                    temp_dict = {
                                        "datetime": data1[i]["datetime"],
                                        "value1": data1[i]["value1"],
                                    }
                                    temp_list_1.append(temp_dict)
                                temp_array.append(temp_list_1)

                        # 日前出清价格
                        elif option == "日前出清价格":
                            temp_map_dict['value2'] = '日前出清价格'
                            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value2": data2[i]["value2"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                # 日前市场--新能源出力
                elif sign == MarketInfoSigns.new_energy.name:
                    temp_map_dict = {
                        # "value1": "预测光伏出力",
                        # "value2": "预测风电出力",
                        # "value3": "预测新能源总出力"
                    }
                    # 预测光伏出力/预测风电出力/预测新能源总出力
                    data = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测", user_session)
                    if data:
                        for option in options:
                            if option == "预测光伏出力":
                                temp_map_dict['value1'] = '预测光伏出力'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value1": data[i]["value2"]
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)
                            elif option == "预测风电出力":
                                temp_map_dict['value2'] = '预测风电出力'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value2": data[i]["value3"]
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)
                            elif option == "预测新能源总出力":
                                temp_map_dict['value3'] = '预测新能源总出力'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value3": data[i]["value1"]
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)

                # 日前市场--发电总出力
                elif sign == MarketInfoSigns.total_load.name:
                    temp_map_dict = {
                        # "value1": "日前非市场化机组出力预测",
                        # "value2": "机组检修总容量",
                        # "value3": "日前联络线计划信息（加总）",
                        # "value4": "预测新能源总出力",
                        # "value5": "全省用电负荷预测信息"
                    }
                    for option in options:
                        # 日前非市场化机组出力预测
                        if option == "日前非市场化机组出力预测":
                            temp_map_dict['value1'] = '日前非市场化机组出力预测'
                            data1 = await self.get_series_data(start_day, end_day, province_id, "日前非市场化机组出力预测", user_session)
                            if data1:
                                temp_list_1 = []
                                for i in range(len(data1)):
                                    temp_dict = {
                                        "datetime": data1[i]["datetime"],
                                        "value1": data1[i]["value1"],
                                    }
                                    temp_list_1.append(temp_dict)
                                temp_array.append(temp_list_1)

                        # 机组检修总容量
                        elif option == "机组检修总容量":
                            temp_map_dict['value2'] = '机组检修总容量'
                            data2 = await self.get_series_data(start_day, end_day, province_id, "机组检修总容量", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value2": round(float(data2[i]["value1"]), 2) if data2[i]["value1"] is not None else '--',
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        # 日前联络线计划信息（加总）
                        elif option == "日前联络线计划信息（加总）":
                            temp_map_dict['value3'] = '日前联络线计划信息（加总）'
                            data3 = await self.get_series_data(start_day, end_day, province_id, "日前联络线计划信息（加总）", user_session)
                            if data3:
                                temp_list_3 = []
                                for i in range(len(data3)):
                                    temp_dict = {
                                        "datetime": data3[i]["datetime"],
                                        "value3": data3[i]["value1"],
                                    }
                                    temp_list_3.append(temp_dict)
                                temp_array.append(temp_list_3)

                        # 预测新能源总出力
                        elif option == "预测新能源总出力":
                            temp_map_dict['value4'] = '预测新能源总出力'
                            data4 = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测", user_session)
                            if data4:
                                temp_list_4 = []
                                for i in range(len(data4)):
                                    temp_dict = {
                                        "datetime": data4[i]["datetime"],
                                        "value4": data4[i]["value1"],
                                    }
                                    temp_list_4.append(temp_dict)
                                temp_array.append(temp_list_4)

                        # 全省用电负荷预测信息
                        elif option == "全省用电负荷预测信息":
                            temp_map_dict['value5'] = '全省用电负荷预测信息'
                            data5 = await self.get_series_data(start_day, end_day, province_id, "全省用电负荷预测信息", user_session)
                            if data5:
                                temp_list_5 = []
                                for i in range(len(data5)):
                                    temp_dict = {
                                        "datetime": data5[i]["datetime"],
                                        "value5": data5[i]["value1"]
                                    }
                                    temp_list_5.append(temp_dict)
                                temp_array.append(temp_list_5)

            # 实时市场
            elif date_type == "realtime":
                # 实时市场--出清数据
                if sign == MarketInfoSigns.clear_data.name:
                    temp_map_dict = {
                        # "value1": "实时出清电量",
                        # "value2": "实时出清价格"
                    }
                    for option in options:
                        # 实时出清电量
                        if option == '实时出清电量':
                            temp_map_dict['value1'] = '实时出清电量'
                            data1 = await self.get_series_data(start_day, end_day, province_id, "实时出清电量", user_session)
                            if data1:
                                temp_list_1 = []
                                for i in range(len(data1)):
                                    temp_dict = {
                                        "datetime": data1[i]["datetime"],
                                        "value1": data1[i]["value1"],
                                    }
                                    temp_list_1.append(temp_dict)
                                temp_array.append(temp_list_1)

                        # 实时出清价格
                        elif option == '实时出清价格':
                            temp_map_dict['value2'] = '实时出清价格'
                            data2 = await self.get_series_data(start_day, end_day, province_id, "实时节点边际电价", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value2": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                # 实时市场--新能源出力
                elif sign == MarketInfoSigns.new_energy.name:
                    temp_map_dict = {
                        # "value1": "光伏实际值",
                        # "value2": "风电实际值",
                        # "value3": "新能源总加实际值"
                    }
                    # 光伏实际值/风电实际值/新能源总加实际值
                    data = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力", user_session)
                    if data:
                        for option in options:
                            if option == "光伏实际值":
                                temp_map_dict['value1'] = '光伏实际值'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value1": data[i]["value2"]
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)
                            elif option == "风电实际值":
                                temp_map_dict['value2'] = '风电实际值'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value2": data[i]["value1"],
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)
                            elif option == "新能源总加实际值":
                                temp_map_dict['value3'] = '新能源总加实际值'
                                temp_list = []
                                for i in range(len(data)):
                                    temp_dict = {
                                        "datetime": data[i]["datetime"],
                                        "value3": data[i]["value3"]
                                    }
                                    temp_list.append(temp_dict)
                                temp_array.append(temp_list)

                # 实时市场---发电总出力
                elif sign == MarketInfoSigns.total_load.name:
                    temp_map_dict = {
                        # "value1": "实时非市场化机组出力曲线",
                        # "value2": "新能源总实时出力",
                        # "value3": "实时联络线计划（加总）",
                        # "value4": "水电总实时出力",
                        # "value5": "系统负荷实际值"
                    }
                    for option in options:

                        # 实时非市场化机组出力曲线
                        if option == "实时非市场化机组出力曲线":
                            temp_map_dict['value1'] = '实时非市场化机组出力曲线'
                            data1 = await self.get_series_data(start_day, end_day, province_id, "实时非市场化机组出力曲线", user_session)
                            if data1:
                                temp_list_1 = []
                                for i in range(len(data1)):
                                    temp_dict = {
                                        "datetime": data1[i]["datetime"],
                                        "value1": data1[i]["value1"],
                                    }
                                    temp_list_1.append(temp_dict)
                                temp_array.append(temp_list_1)

                        # 新能源总实时出力
                        elif option == "新能源总实时出力":
                            temp_map_dict['value2'] = '新能源总实时出力'
                            data2 = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value2": data2[i]["value3"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        # 实时联络线计划（加总）
                        elif option == "实时联络线计划（加总）":
                            temp_map_dict['value3'] = '实时联络线计划（加总）'
                            data3 = await self.get_series_data(start_day, end_day, province_id, "实时联络线计划（加总）", user_session)
                            if data3:
                                temp_list_3 = []
                                for i in range(len(data3)):
                                    temp_dict = {
                                        "datetime": data3[i]["datetime"],
                                        "value3": data3[i]["value1"],
                                    }
                                    temp_list_3.append(temp_dict)
                                temp_array.append(temp_list_3)

                        # 水电总实时出力
                        elif option == "水电总实时出力":
                            temp_map_dict['value4'] = '水电总实时出力'
                            data4 = await self.get_series_data(start_day, end_day, province_id, "水电总实时出力", user_session)
                            if data4:
                                temp_list_4 = []
                                for i in range(len(data4)):
                                    temp_dict = {
                                        "datetime": data4[i]["datetime"],
                                        "value4": data4[i]["value1"],
                                    }
                                    temp_list_4.append(temp_dict)
                                temp_array.append(temp_list_4)

                        # 系统负荷实际值
                        elif option == "系统负荷实际值":
                            temp_map_dict['value5'] = '系统负荷实际值'
                            data5 = await self.get_series_data(start_day, end_day, province_id, "系统实时负荷频率备用情况", user_session)
                            if data5:
                                temp_list_5 = []
                                for i in range(len(data5)):
                                    temp_dict = {
                                        "datetime": data5[i]["datetime"],
                                        "value5": data5[i]["value1"],
                                    }
                                    temp_list_5.append(temp_dict)
                                temp_array.append(temp_list_5)

            # 总览
            else:
                # 总览--电价趋势
                if sign == 'power_trend_1':
                    temp_map_dict = {}
                    for option in options:
                        if option == "日前出清价格":
                            temp_map_dict['value1'] = '日前出清价格'
                            # 日前出清价格
                            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value1": data2[i]["value2"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)
                        elif option == "预测出清价格":
                            temp_map_dict['value2'] = '预测出清价格'    # TODO 现阶段为空
                            # 预测出清价格
                            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        # "value2": float(data2[i]["value1"]) if data2[i]["value1"] is not None else '--',
                                        "value2": '--',
                                    }
                                    temp_list_2.append(temp_dict)
                        elif option == "实时出清价格":
                            temp_map_dict['value3'] = '实时出清价格'
                            # 实时出清价格
                            data2 = await self.get_series_data(start_day, end_day, province_id, "实时节点边际电价", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value3": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)
                        elif option == "预测实时价格":
                            temp_map_dict['value4'] = '预测实时价格'      # TODO 现阶段为空
                            # 预测出清价格
                            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        # "value4": float(data2[i]["value1"]) if data2[i]["value1"] is not None else '--',
                                        "value4": '--',
                                    }
                                    temp_list_2.append(temp_dict)

                # 总览--负荷及出清量趋势
                elif sign == 'load_trend':
                    temp_map_dict = {}
                    for option in options:
                        if option == "预测光伏出力":
                            temp_map_dict['value1'] = '预测光伏出力'
                            # 预测光伏出力
                            data3 = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测", user_session)
                            if data3:
                                temp_list_3 = []
                                for i in range(len(data3)):
                                    temp_dict = {
                                        "datetime": data3[i]["datetime"],
                                        "value1": data3[i]["value2"]
                                    }
                                    temp_list_3.append(temp_dict)
                                temp_array.append(temp_list_3)

                        elif option == "预测风电出力":
                            temp_map_dict['value2'] = '预测风电出力'
                            # 预测风电出力
                            data3 = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测", user_session)
                            if data3:
                                temp_list_3 = []
                                for i in range(len(data3)):
                                    temp_dict = {
                                        "datetime": data3[i]["datetime"],
                                        "value2": data3[i]["value3"],
                                    }
                                    temp_list_3.append(temp_dict)
                                temp_array.append(temp_list_3)

                        elif option == "预测外送联络":
                            temp_map_dict['value3'] = '预测外送联络'
                            # 预测外送联络
                            data6 = await self.get_series_data(start_day, end_day, province_id, "日前联络线计划信息（加总）", user_session)
                            if data6:
                                temp_list_6 = []
                                for i in range(len(data6)):
                                    if data6[i]["name"] == '总加':
                                        temp_dict = {
                                            "datetime": data6[i]["datetime"],
                                            "value3": data6[i]["value1"],
                                        }
                                        temp_list_6.append(temp_dict)
                                temp_array.append(temp_list_6)
                        elif option == "预测机组出力":
                            temp_map_dict['value4'] = '预测机组出力'
                            # 预测机组出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "日前非市场化机组出力预测", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value4": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        elif option == "预测发电总出力":
                            temp_map_dict['value5'] = '预测发电总出力'
                            # 预测发电总出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "预测发电总出力", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value5": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        elif option == "实际光伏出力":
                            temp_map_dict['value6'] = '实际光伏出力'
                            # 实际光伏出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value6": data2[i]["value2"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        elif option == "实际风电出力":
                            temp_map_dict['value7'] = '实际风电出力'
                            # 实际风电出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value7": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        elif option == "实际外送联络":
                            temp_map_dict['value8'] = '实际外送联络'
                            # 实际外送联络
                            data6 = await self.get_series_data(start_day, end_day, province_id, "实时联络线计划（加总）", user_session)
                            if data6:
                                temp_list_6 = []
                                for i in range(len(data6)):
                                    # if data6[i]["name"] == '总加':
                                    temp_dict = {
                                        "datetime": data6[i]["datetime"],
                                        "value8": data6[i]["value1"],
                                    }
                                    temp_list_6.append(temp_dict)
                                temp_array.append(temp_list_6)

                        elif option == "实际机组出力":
                            temp_map_dict['value9'] = '实际机组出力'
                            # 实际风电出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "实时非市场化机组出力曲线", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value9": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                        elif option == "水电总实时出力":
                            temp_map_dict['value10'] = '水电总实时出力'
                            # 水电总实时出力
                            data2 = await self.get_series_data(start_day, end_day, province_id, "水电总实时出力", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value10": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

                # 总览--电源趋势2
                if sign == 'power_trend_2':
                    temp_map_dict = {}
                    for option in options:
                        if option == "日前出清电量":
                            temp_map_dict['value1'] = '日前出清电量'
                            # 日前出清电量
                            data2 = await self.get_series_data(start_day, end_day, province_id,
                                                               "日前出清电量", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value1": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)
                        elif option == "日前预测负荷":
                            temp_map_dict['value2'] = '日前预测负荷'
                            # 日前预测负荷
                            data2 = await self.get_series_data(start_day, end_day, province_id,
                                                               "全省用电负荷预测信息", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value2": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)
                        elif option == "实时出清电量":
                            temp_map_dict['value3'] = '实时出清电量'
                            # 实时出清电量
                            data2 = await self.get_series_data(start_day, end_day, province_id,
                                                               "实时出清电量", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value3": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)
                        elif option == "实时实际负荷":
                            temp_map_dict['value4'] = '实时实际负荷'
                            # 实时实际负荷
                            data2 = await self.get_series_data(start_day, end_day, province_id,
                                                               "系统实时负荷频率备用情况", user_session)
                            if data2:
                                temp_list_2 = []
                                for i in range(len(data2)):
                                    temp_dict = {
                                        "datetime": data2[i]["datetime"],
                                        "value4": data2[i]["value1"],
                                    }
                                    temp_list_2.append(temp_dict)
                                temp_array.append(temp_list_2)

            result = merge_dicts(*temp_array)
            result = sorted(result, key=lambda x: x['datetime'])

            for i in result:
                for k in temp_map_dict.keys():
                    if k not in i.keys():
                        i[k] = '--'

            # user_session.close()
            data = {
                "value_maps": temp_map_dict,
                "detail": result
            }
            self.returnTypeSuc(data)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class SeriesDictData(BaseHandler):
    """时序字典数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            data = (user_session.query(CDictSery).filter(CDictSery.province_id == province_id,
                                                         CDictSery.is_use == "1").order_by(CDictSery.id).all())
            temp_array = []
            if data:
                for d in data:
                    if not d.parent_id:
                        temp_dict = {
                            "id": d.id,
                            "name": d.name,
                            "sub_items": []
                        }
                        temp_array.append(temp_dict)
                    else:
                        temp_dict = {
                            "id": d.id,
                            "name": d.name,
                            "parent_id": d.parent_id
                        }
                        for i in temp_array:
                            if i["id"] == d.parent_id:
                                i["sub_items"].append(temp_dict)

            self.returnTypeSuc(temp_array)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class SeriesData(BaseHandler):
    """时序交易数据"""""

    @staticmethod
    def get_max_opts_per_moment(target_day, series_id, user_session):
        if series_id == 13:
            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                         RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.name == "总加",
                         RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()

        elif series_id == 17:
            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                         RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.name == "山西.释容储能电站/220kV.A母线",
                         RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()

        else:

            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                        RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()

        return res[0].latest_opts if res else None

    @staticmethod
    async def get_series_data(start_day, end_day, series_id, user_session):
        target_day = start_day
        temp_list = []
        while target_day <= end_day:

            latest_opts = SeriesData.get_max_opts_per_moment(target_day, series_id, user_session)
            if latest_opts:

                if series_id == 13:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                         RSeriesDataShanxi.series_id == series_id,
                                                                         RSeriesDataShanxi.name == "总加",
                                                                         RSeriesDataShanxi.op_ts == latest_opts,
                                                                         RSeriesDataShanxi.is_use == "1").all())

                elif series_id == 17:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                         RSeriesDataShanxi.series_id == series_id,
                                                                         RSeriesDataShanxi.name == "山西.释容储能电站/220kV.A母线",
                                                                         RSeriesDataShanxi.op_ts == latest_opts,
                                                                         RSeriesDataShanxi.is_use == "1").all())

                else:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                          RSeriesDataShanxi.series_id == series_id,
                                                                          RSeriesDataShanxi.op_ts == latest_opts,
                                                                          RSeriesDataShanxi.is_use == "1").all())

                temp_list_ = []
                if data_:
                    for d in data_:
                        temp_dict = {
                            "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                            "day": d.day.strftime("%Y-%m-%d"),
                            "moment": d.moment,
                            "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                            "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                            "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                            "name": d.name if d.name else '--'
                        }
                        temp_list_.append(temp_dict)

                    data = sorted(temp_list_, key=lambda x: x['datetime']) if temp_list_ else []

                    for i in range(len(data)):

                        if series_id == 3:  # 日前非市场化机组出力预测
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value1": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 4:  # 日前新能源负荷预测
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value2": data[i]["value3"],
                                "value3": data[i]["value2"],
                                "value4": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 5:  # 全省用电负荷预测信息
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value5": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 6:  # 日前出清电量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value6": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 7:  # 日前联络线计划信息（加总）
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name2": data[i]['name'],
                                "value7": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 8:  # 机组检修总容量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name1": data[i]['name'],
                                "value8": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 9:  # 实时非市场化机组出力曲线
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value9": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 10:  # 新能源总实时出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value10": data[i]["value1"],
                                "value11": data[i]["value2"],
                                "value12": data[i]["value3"],
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 11:  # 系统实时负荷频率备用情况
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value13": data[i]["value1"],
                                "value14": data[i]["value2"],
                                "value15": data[i]["value3"],
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 12:  # 实时出清电量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value16": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 13:  # 实时联络线计划（加总）
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name2": data[i]['name'],
                                "value17": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 14:  # 水电总实时出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value18": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 15:  # 现货出清电价信息
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value19": data[i]["value1"],
                                "value20": data[i]["value2"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 16:  # 预测发电总出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value21": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 17:  # 实时节点边际电价
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value22": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

            else:
                if series_id == 3:  # 日前非市场化机组出力预测
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value1": '--'
                    }
                    temp_list.append(temp_dict)

                elif series_id == 4:  # 日前新能源负荷预测
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value2": '--',
                        "value3": '--',
                        "value4": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 5:  # 全省用电负荷预测信息
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value5": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 6:  # 日前出清电量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value6": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 7:  # 日前联络线计划信息（加总）
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name2": '--',
                        "value7": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 8:  # 机组检修总容量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name1": '--',
                        "value8": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 9:  # 实时非市场化机组出力曲线
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value9": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 10:  # 新能源总实时出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value10": '--',
                        "value11": '--',
                        "value12": '--',
                    }
                    temp_list.append(temp_dict)
                elif series_id == 11:  # 系统实时负荷频率备用情况
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value13": '--',
                        "value14": '--',
                        "value15": '--',
                    }
                    temp_list.append(temp_dict)
                elif series_id == 12:  # 实时出清电量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value16": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 13:  # 实时联络线计划（加总）
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name2": '--',
                        "value17": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 14:  # 水电总实时出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value18": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 15:  # 现货出清电价信息
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value19": '--',
                        "value20": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 16:  # 预测发电总出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value21": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 17:  # 实时节点边际电价
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value22": '--',
                    }
                    temp_list.append(temp_dict)

            target_day += timedelta(days=1)

        if temp_list:
            return temp_list
        else:
            print(2141, f"{series_id}没有查询到数据.................")
            return None

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            series_id_set = self.get_argument("series_id_set", default='[]')
            page = int(self.get_argument("page")) if self.get_argument("page", None) else None
            page_size = int(self.get_argument("page_size")) if self.get_argument("page_size", None) else 33

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数series_id
            if not isinstance(eval(series_id_set), list):
                return self.customError("series_id_set参数格式错误")
            series_id_set = eval(series_id_set)

            # 查询所有的 series_id
            data = (user_session.query(CDictSery).filter(CDictSery.province_id == province_id,
                                                         CDictSery.parent_id is not None,
                                                         CDictSery.is_use == "1").all())
            all_series_id_set = [d.id for d in data]

            if not series_id_set:
                series_id_set = all_series_id_set
            else:
                for series_id in series_id_set:
                    if series_id not in all_series_id_set:
                        return self.customError(f"series_id: {series_id}参数错误")

            temp_array = []

            tasks = []
            for series_id in series_id_set:
                tasks.append(self.get_series_data(start_day, end_day, series_id, user_session))
            data_list = await asyncio.gather(*tasks)

            for data in data_list:
                if data is None:
                    continue
                temp_array.append(data)

            temp_map_dict = {
                "datetime": '时间',
                "day": '日期',
                "moment": '时刻',
                "value1": "非市场化机组出力预测(MW)",
                "value2": "预测风电出力(MW)",
                "value3": "预测光伏出力(MW)",
                "value4": "预测新能源总出力(MW)",
                "value5": "全省用电负荷预测信息 (电力值)",
                "value6": "现货日前出清电量(MWh)",
                "value7": "日前联络线计划信息（加总）(电力值)",
                "value8": "检修容量",
                "value9": "非市场化机组实际出力(MW)",
                "value10": "风电实际值(MW)",
                "value11": "光伏实际值(MW)",
                "value12": "新能源总加实际值(MW)",
                "value13": "系统负荷实际值(MW)",
                "value14": "频率实际值(MW)",
                "value15": "实际上旋备用",
                "value16": "现货实时出清电量(MWh)",
                "value17": "实时联络线计划（加总）电力值(MW)",
                "value18": "水电实际值(MW)",
                "value19": "日前价格",
                "value20": "实时价格",
                "value21": "预测发电总出力(MW)",
                "value22": "实时节点边际电价",
                "name1": "市场主体名称",
                "name2": "通道类型"
            }

            result = merge_dicts(*temp_array)
            result = sorted(result, key=lambda x: x['datetime'])

            # 手动分页
            if page:
                total_pages = math.ceil(len(result) / page_size)
                start_index = (page - 1) * page_size
                end_index = page * page_size if page < total_pages else len(result) + 1

                data_ = {
                    "total": len(result),
                    "total_pages": total_pages,
                    "page": page,
                    "detail": result[start_index:end_index],
                    "maps": temp_map_dict
                }
            else:
                data_ = {
                    "total": len(result),
                    "total_pages": 1,
                    "page": page,
                    "detail": result,
                    "maps": temp_map_dict
                }

            # user_session.close()
            self.returnTypeSuc(data_)
        except Exception as e:
            print(2265, traceback.print_exc())
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class ExportSeriesData(BaseHandler):
    """导出时序交易数据"""""

    @staticmethod
    def get_max_opts_per_moment(target_day, series_id, user_session):
        if series_id == 13:
            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                        RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.name == "总加",
                        RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()

        elif series_id == 17:
            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                        RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.name == "山西.释容储能电站/220kV.A母线",
                        RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()

        else:
            res = (
                user_session.query(RSeriesDataShanxi.moment, func.max(RSeriesDataShanxi.op_ts).label('latest_opts'))
                .filter(RSeriesDataShanxi.day == target_day,
                        RSeriesDataShanxi.series_id == series_id,
                        RSeriesDataShanxi.is_use == "1")
                .group_by(RSeriesDataShanxi.moment)
            ).all()
        return res[0].latest_opts if res else None

    @staticmethod
    async def get_series_data(start_day, end_day, series_id, user_session):
        target_day = start_day
        temp_list = []
        while target_day <= end_day:

            latest_opts = SeriesData.get_max_opts_per_moment(target_day, series_id, user_session)
            if latest_opts:

                if series_id == 13:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                          RSeriesDataShanxi.series_id == series_id,
                                                                          RSeriesDataShanxi.name == "总加",
                                                                          RSeriesDataShanxi.op_ts == latest_opts,
                                                                          RSeriesDataShanxi.is_use == "1").all())

                elif series_id == 17:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                          RSeriesDataShanxi.series_id == series_id,
                                                                          RSeriesDataShanxi.name == "山西.释容储能电站/220kV.A母线",
                                                                          RSeriesDataShanxi.op_ts == latest_opts,
                                                                          RSeriesDataShanxi.is_use == "1").all())

                else:
                    data_ = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == target_day,
                                                                          RSeriesDataShanxi.series_id == series_id,
                                                                          RSeriesDataShanxi.op_ts == latest_opts,
                                                                          RSeriesDataShanxi.is_use == "1").all())

                temp_list_ = []
                if data_:
                    for d in data_:
                        temp_dict = {
                            "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                            "day": d.day.strftime("%Y-%m-%d"),
                            "moment": d.moment,
                            "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                            "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                            "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                            "name": d.name if d.name else '--'
                        }
                        temp_list_.append(temp_dict)

                    data = sorted(temp_list_, key=lambda x: x['datetime']) if temp_list_ else []

                    for i in range(len(data)):

                        if series_id == 3:  # 日前非市场化机组出力预测
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value1": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 4:  # 日前新能源负荷预测
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value2": data[i]["value3"],
                                "value3": data[i]["value2"],
                                "value4": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 5:  # 全省用电负荷预测信息
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value5": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 6:  # 日前出清电量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value6": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 7:  # 日前联络线计划信息（加总）
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name2": data[i]['name'],
                                "value7": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 8:  # 机组检修总容量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name1": data[i]['name'],
                                "value8": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 9:  # 实时非市场化机组出力曲线
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value9": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 10:  # 新能源总实时出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value10": data[i]["value1"],
                                "value11": data[i]["value2"],
                                "value12": data[i]["value3"],
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 11:  # 系统实时负荷频率备用情况
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value13": data[i]["value1"],
                                "value14": data[i]["value2"],
                                "value15": data[i]["value3"],
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 12:  # 实时出清电量
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value16": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 13:  # 实时联络线计划（加总）
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "name2": data[i]['name'],
                                "value17": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 14:  # 水电总实时出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value18": data[i]["value1"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 15:  # 现货出清电价信息
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value19": data[i]["value1"],
                                "value20": data[i]["value2"]
                            }
                            temp_list.append(temp_dict)
                        elif series_id == 16:  # 预测发电总出力
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value21": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

                        elif series_id == 17:  # 实时节点边际电价
                            temp_dict = {
                                "datetime": data[i]["datetime"],
                                "day": data[i]["day"],
                                "moment": data[i]['moment'],
                                "value22": data[i]["value1"],
                            }
                            temp_list.append(temp_dict)

            else:
                if series_id == 3:  # 日前非市场化机组出力预测
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value1": '--'
                    }
                    temp_list.append(temp_dict)

                elif series_id == 4:  # 日前新能源负荷预测
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value2": '--',
                        "value3": '--',
                        "value4": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 5:  # 全省用电负荷预测信息
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value5": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 6:  # 日前出清电量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value6": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 7:  # 日前联络线计划信息（加总）
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name2": '--',
                        "value7": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 8:  # 机组检修总容量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name1": '--',
                        "value8": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 9:  # 实时非市场化机组出力曲线
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value9": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 10:  # 新能源总实时出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value10": '--',
                        "value11": '--',
                        "value12": '--',
                    }
                    temp_list.append(temp_dict)
                elif series_id == 11:  # 系统实时负荷频率备用情况
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value13": '--',
                        "value14": '--',
                        "value15": '--',
                    }
                    temp_list.append(temp_dict)
                elif series_id == 12:  # 实时出清电量
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value16": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 13:  # 实时联络线计划（加总）
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "name2": '--',
                        "value17": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 14:  # 水电总实时出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value18": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 15:  # 现货出清电价信息
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value19": '--',
                        "value20": '--'
                    }
                    temp_list.append(temp_dict)
                elif series_id == 16:  # 预测发电总出力
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value21": '--',
                    }
                    temp_list.append(temp_dict)

                elif series_id == 17:  # 实时节点边际电价
                    temp_dict = {
                        "datetime": target_day.strftime("%Y-%m-%d") + " 00:15",
                        "day": target_day.strftime("%Y-%m-%d"),
                        "moment": '00:15',
                        "value22": '--',
                    }
                    temp_list.append(temp_dict)

            target_day += timedelta(days=1)

        if temp_list:
            return temp_list
        else:
            print(2141, f"{series_id}没有查询到数据.................")
            return None

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            # date_type = self.get_argument("date_type", default="real")
            series_id_set = self.get_argument("series_id_set", default='[]')

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # # 校验参数date_type
            # if date_type not in MarketInfoDataTypes.__members__:
            #     return self.customError(f"参数date_type: {date_type}错误")
            #
            # # 校验参数sign
            # if sign not in MarketInfoSigns.__members__:
            #     return self.customError(f"参数sign:{sign}错误")

            # 校验参数series_id
            if not isinstance(eval(series_id_set), list):
                return self.customError("series_id_set参数格式错误")
            series_id_set = eval(series_id_set)

            # 查询所有的 series_id
            data = (user_session.query(CDictSery).filter(CDictSery.province_id == province_id,
                                                         CDictSery.parent_id is not None,
                                                         CDictSery.is_use == "1").all())
            all_series_id_set = [d.id for d in data]

            if not series_id_set:
                series_id_set = all_series_id_set
            else:
                for series_id in series_id_set:
                    if series_id not in all_series_id_set:
                        return self.customError(f"series_id: {series_id}参数错误")

            temp_array = []

            tasks = []
            for series_id in series_id_set:
                tasks.append(self.get_series_data(start_day, end_day, series_id, user_session))
            data_list = await asyncio.gather(*tasks)

            for data in data_list:
                if data is None:
                    continue
                temp_array.append(data)

            temp_map_dict = {
                "datetime": '时间',
                "day": '日期',
                "moment": '时刻',
                "value1": "非市场化机组出力预测(MW)",
                "value2": "预测风电出力(MW)",
                "value3": "预测光伏出力(MW)",
                "value4": "预测新能源总出力(MW)",
                "value5": "全省用电负荷预测信息 (电力值)",
                "value6": "现货日前出清电量(MWh)",
                "value7": "日前联络线计划信息（加总）(电力值)",
                "value8": "检修容量",
                "value9": "非市场化机组实际出力(MW)",
                "value10": "风电实际值(MW)",
                "value11": "光伏实际值(MW)",
                "value12": "新能源总加实际值(MW)",
                "value13": "系统负荷实际值(MW)",
                "value14": "频率实际值(MW)",
                "value15": "实际上旋备用",
                "value16": "现货实时出清电量(MWh)",
                "value17": "实时联络线计划（加总）电力值(MW)",
                "value18": "水电实际值(MW)",
                "value19": "日前价格",
                "value20": "实时价格",
                "value21": "预测发电总出力(MW)",
                "value22": "实时节点边际电价",
                "name1": "市场主体名称",
                "name2": "通道类型"
            }

            result = merge_dicts(*temp_array)
            result = sorted(result, key=lambda x: x['datetime'])

            location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name

            # 上传并返回下载url
            workbook = openpyxl.Workbook()
            sheet1 = workbook.active

            BASE_DIR = Path(__file__).resolve().parent
            start_day_str = start_day.strftime("%Y-%m-%d")
            end_day_str = end_day.strftime("%Y-%m-%d")
            file_name = f"{start_day_str}~{end_day_str}{location}时序交易数据.xlsx"
            path = os.path.join(BASE_DIR, file_name)
            sheet1.title = '时序交易数据'

            table_titles = ['日期', '时刻']
            key_arr = []

            # 准备数据
            data_array = list()
            if result:
                for k in temp_map_dict.keys():
                    if k in result[0].keys() and k not in ['datetime', 'day', 'moment']:
                        table_titles.append(temp_map_dict[k])
                        key_arr.append(k)

                for t in result:
                    li = [t['day'], t['moment']]
                    for k in key_arr:
                        if t.get(k) is not None:
                            li.append(t[k])
                        else:
                            li.append('')
                    data_array.append(li)

            # sheet1 写入标头
            for col_num, header in enumerate(table_titles, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}1'] = header

            # 写入工作表
            for row_num, row_data in enumerate(data_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet1[f'{col_letter}{row_num}'] = cell_value if cell_value is not None and cell_value != '--' else ""
            workbook.save(path)

            # 对上传文件大小做限值，超过40M时提示查询指标太多，数据量太大，请重新选择
            if os.path.getsize(path) > 40 * 1024 * 1024:
                return self.customError("查询指标太多，数据量太大，请重新选择")

            url = await upload_file(file_name, path)

            try:
                os.remove(path)
            except Exception as e:
                pass

            # user_session.close()
            self.returnTypeSuc(url)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class NonSeriesDictData(BaseHandler):
    """非时序字典数据"""""

    @tornado.web.authenticated
    def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            data = (user_session.query(CDictNonSery).filter(CDictNonSery.province_id == province_id).all())
            temp_array = []
            if data:
                for d in data:
                    temp_dict = {
                        "id": d.id,
                        "name": d.name,
                    }
                    temp_array.append(temp_dict)

            self.returnTypeSuc(temp_array)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()


class NonSeriesData(BaseHandler):
    """非时序交易数据/导出非时序交易数据"""""

    @staticmethod
    def get_max_opts_per_moment(target_day, non_series_id, user_session):
        res = (
            user_session.query(RNonSeriesDataShanxi.moment, func.max(RNonSeriesDataShanxi.op_ts).label('latest_opts'))
            .filter(RNonSeriesDataShanxi.day == target_day, RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                                RNonSeriesDataShanxi.is_use == '1')
            .group_by(RNonSeriesDataShanxi.moment)
        ).all()
        return res[0].latest_opts if res else None

    @staticmethod
    async def get_non_series_data(start_day, end_day, non_series_id, user_session):  # 非时序数据

        target_day = start_day
        temp_list = []

        while target_day <= end_day:
            latest_opts = NonSeriesData.get_max_opts_per_moment(target_day, non_series_id, user_session)
            if latest_opts:
                data = (user_session.query(RNonSeriesDataShanxi).filter(RNonSeriesDataShanxi.day >= start_day,
                                                                        RNonSeriesDataShanxi.day <= end_day,
                                                                        RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                                        RNonSeriesDataShanxi.op_ts == latest_opts,
                                                                        RNonSeriesDataShanxi.is_use == '1').all())

                if data:
                    for d in data:
                        if d.name:
                            data_ = (user_session.query(RNotSeriesNamesDataShanxi).filter(RNotSeriesNamesDataShanxi.id == eval(d.name),
                                                                                    RNotSeriesNamesDataShanxi.is_use == '1').first())
                            temp_dict = {
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime("%Y-%m-%d"),
                                "day": d.day,
                                "moment": d.moment if d.moment else '',
                                "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                                "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                                "descr": d.descr if d.descr else '--',
                                "name": data_.name if data_ else '--',
                                "index_type": d.index_type if d.index_type else '--',
                                "start_time": d.start_time if d.start_time else '--',
                                "end_time": d.end_time if d.end_time else '--'
                            }
                        else:
                            temp_dict = {
                                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime("%Y-%m-%d"),
                                "day": d.day,
                                "moment": d.moment,
                                "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                                "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                                "descr": d.descr if d.descr is not None else '--',
                                "name": d.name if d.name is not None else '--',
                                "index_type": d.index_type if d.index_type else '--',
                                "start_time": d.start_time if d.start_time else '--',
                                "end_time": d.end_time if d.end_time else '--'
                            }
                        temp_list.append(temp_dict)
            target_day += timedelta(days=1)
        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRSectionsShadowDataShanxiData(start_day, end_day, user_session):  # 1、断面约束情况及影子价格
        data = (user_session.query(RSectionsShadowDataShanxi).filter(RSectionsShadowDataShanxi.day >= start_day,
                                                                RSectionsShadowDataShanxi.day <= end_day,
                                                                RSectionsShadowDataShanxi.is_use == '1').all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealAccessEleShanxiData(start_day, end_day, user_session):  # 2、重要通道实际输电情况
        data = (user_session.query(RRealAccessEleShanxi).filter(RRealAccessEleShanxi.day >= start_day,
                                                                RRealAccessEleShanxi.day <= end_day,
                                                                RRealAccessEleShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealNodePriceShanxiData(start_day, end_day, user_session):  # ６、实时节点边际电价
        data = (user_session.query(RRealNodePriceShanxi).filter(RRealNodePriceShanxi.day >= start_day,
                                                                RRealNodePriceShanxi.day <= end_day,
                                                                RRealNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                    "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                    "node": d.node,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreNodePriceShanxiData(start_day, end_day, user_session):  # ７、日前节点边际电价
        data = (user_session.query(RPreNodePriceShanxi).filter(RPreNodePriceShanxi.day >= start_day,
                                                               RPreNodePriceShanxi.day <= end_day,
                                                               RPreNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                    "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                    "node": d.node,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealSectionsBlockDataShanxiData(start_day, end_day, user_session):  # 8、实时输电断面约束及阻塞
        data = (user_session.query(RRealSectionsBlockDataShanxi).filter(RRealSectionsBlockDataShanxi.day >= start_day,
                                                                        RRealSectionsBlockDataShanxi.day <= end_day,
                                                                        RRealSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": d.value if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreSectionsBlockDataShanxiData(start_day, end_day, user_session):  # 9、日前输电断面约束及阻塞
        data = (user_session.query(RPreSectionsBlockDataShanxi).filter(RPreSectionsBlockDataShanxi.day >= start_day,
                                                                       RPreSectionsBlockDataShanxi.day <= end_day,
                                                                       RPreSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": d.value if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealCallWireDataShanxiData(start_day, end_day, user_session):  # 10、实时联络线计划（站点）
        data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                              RSeriesDataShanxi.day <= end_day,
                                                              RSeriesDataShanxi.series_id == 13,
                                                              RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value1), 2) if d.value1 is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreCallWireDataShanxiData(start_day, end_day, user_session):  # 11、日前联络线计划信息（站点）
        data = (user_session.query(RPreCallWireDataShanxi).filter(RPreCallWireDataShanxi.day >= start_day,
                                                                  RPreCallWireDataShanxi.day <= end_day,
                                                                  RPreCallWireDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealUnitPowerDataShanxiData(start_day, end_day, user_session):  # 20、机组实时出力
        data = (user_session.query(RRealUnitPowerDataShanxi).filter(RRealUnitPowerDataShanxi.day >= start_day,
                                                                    RRealUnitPowerDataShanxi.day <= end_day,
                                                                    RRealUnitPowerDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitOpenDataShanxiData(start_day, end_day, user_session):  # 21、日前必开机组
        data = (user_session.query(RPreUnitOpenDataShanxi).filter(RPreUnitOpenDataShanxi.day >= start_day,
                                                                  RPreUnitOpenDataShanxi.day <= end_day,
                                                                  RPreUnitOpenDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitCloseDataShanxiData(start_day, end_day, user_session):  # 22、日前必停机组
        data = (user_session.query(RPreUnitCloseDataShanxi).filter(RPreUnitCloseDataShanxi.day >= start_day,
                                                                   RPreUnitCloseDataShanxi.day <= end_day,
                                                                   RPreUnitCloseDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get_non_series_info(self, start_day, end_day, non_series_id, user_session):
        temp_list = []
        temp_map_dict = {}
        if non_series_id in [1, 2, 6, 7, 8, 9, 10, 11, 20, 21, 22]:
            if non_series_id == 1:  # 断面约束情况及影子价格
                data = await self.getRSectionsShadowDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "阻塞价格(元/MWh)"}

            elif non_series_id == 2:  # 重要通道实际输电情况
                data = await self.getRRealAccessEleShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "名称",
                                     "value": "潮流(MW)"}

            elif non_series_id == 6:  # 实时节点边际电价
                data = await self.getRRealNodePriceShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 7:  # 日前节点边际电价
                data = await self.getRPreNodePriceShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 8:  # 实时输电断面约束及阻塞
                data = await self.getRRealSectionsBlockDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 9:  # 日前输电断面约束及阻塞
                data = await self.getRPreSectionsBlockDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 10:  # 实时联络线计划（站点）
                data = await self.getRRealCallWireDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 11:  # 日前联络线计划信息（站点）
                data = await self.getRPreCallWireDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 20:  # 机组实时出力
                data = await self.getRRealUnitPowerDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                            "descr": "停机/待机"
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     "value": "电力值(MW)", "descr": "机组状态"}
            elif non_series_id == 21:  # 日前必开机组
                data = await self.getRPreUnitOpenDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
            elif non_series_id == 22:  # 日前必停机组
                data = await self.getRPreUnitCloseDataShanxiData(start_day, end_day, user_session)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
        else:
            data = await self.get_non_series_data(start_day, end_day, int(non_series_id), user_session)
            if data:
                if non_series_id == 3:  # 市场力分析指标
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value": data[i]["value1"],
                            "index_type": data[i]["index_type"],
                            "name": data[i]["name"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "电厂", 'value': "指标值",
                                     "index_type": "指标类型"}

                elif non_series_id == 4:  # 实时电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'descr': "出清情况"}

                elif non_series_id == 5:  # 日前电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'descr': "出清情况"}

                elif non_series_id == 12:  # 实时备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 13:  # 日前备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 14:  # 实时调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}
                elif non_series_id == 15:  # 日前调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}

                elif non_series_id == 16:  # 水电发电计划预测
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'value1': "电量(MWh)"}

                elif non_series_id == 17:  # 抽蓄电站蓄水水位
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'value1': "水位值", "descr": "描述"}

                elif non_series_id == 18:  # 调频辅助服务需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频需求容量(MW)"}

                elif non_series_id == 19:  # 输电通道容量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "name": data[i]['name'],
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "通道", 'value1': "输电通道容量"}

                elif non_series_id == 23:  # 断面约束
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "index_type": data[i]["index_type"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "index_type": "市场成员名称", "descr": "断面描述",
                                     "value1": "正向传输极限", 'value2': "反向传输极限", "name": "断面名称"}

                elif non_series_id == 24:  # 日前正负备用需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "value1": data[i]["value1"],
                            "index_type": data[i]["index_type"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "市场成员名称", "index_type": "类型",
                                     "value1": "备用负荷"}

                elif non_series_id == 25:  # 输变电设备检修计划信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "index_type": data[i]['index_type'],
                            "start_time": data[i]["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                            "end_time": data[i]["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "设备名称", "index_type": "设备类型",
                                     "start_time": "设备检修开始时间", "end_time": "设备检修结束时间"}

                elif non_series_id == 26:  # 开机不满七天机组信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "name": data[i]["name"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'name': "机组名称"}

        result = sorted(temp_list, key=lambda x: x['datetime'])
        return result, temp_map_dict

    @tornado.web.authenticated
    async def get(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            non_series_id = self.get_argument("non_series_id", default=None)
            page = int(self.get_argument("page")) if self.get_argument("page", None) else None
            page_size = int(self.get_argument("page_size")) if self.get_argument("page_size", None) else 33

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数non_series_id
            if not non_series_id:
                return self.customError("non_series_id参数缺失")

            data = (user_session.query(CDictNonSery).filter(CDictNonSery.province_id == province_id).all())
            temp_array = [d.id for d in data]
            if int(non_series_id) not in temp_array:
                return self.customError("non_series_id参数不存在")

            non_series_id = eval(non_series_id)

            result, temp_map_dict = await self.get_non_series_info(start_day, end_day, non_series_id, user_session)

            # 分页
            # 手动分页
            if page:
                total_pages = math.ceil(len(result) / page_size)
                start_index = (page - 1) * page_size
                end_index = page * page_size if page < total_pages else len(result) + 1

                data_ = {
                    "total": len(result),
                    "total_pages": total_pages,
                    "page": page,
                    "detail": result[start_index:end_index],
                    "maps": temp_map_dict
                }
            else:
                data_ = {
                    "total": len(result),
                    "total_pages": 1,
                    "page": page,
                    "detail": result,
                    "maps": temp_map_dict
                }

            # user_session.close()
            self.returnTypeSuc(data_)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    async def post(self):
        user_session = get_user_session()
        try:
            self.refreshSession()
            user = self.get_current_user()

            province_id = self.get_argument("province_id")
            start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
            non_series_id = self.get_argument("non_series_id", default='[]')

            provinces = user_session.query(CProvince).all()
            provinces_id = [province.id for province in provinces if province.is_use == '1' and province.name != "中国"]

            # 校验参数province_id
            if not province_id or int(province_id) not in provinces_id:
                return self.customError("省份ID参数错误")

            province_id = int(province_id)

            # 校验参数start_day和end_day
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            # if start_day > datetime.datetime.now():
            #     return self.customError("开始日期不能大于当前日期")
            # if end_day > datetime.datetime.now():
            #     return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 校验参数non_series_id
            if not non_series_id:
                return self.customError("non_series_id参数格式错误")

            data = (user_session.query(CDictNonSery).filter(CDictNonSery.province_id == province_id,
                                                            CDictNonSery.is_use == "1").all())
            temp_array = [d.id for d in data]
            if int(non_series_id) not in temp_array:
                return self.customError("non_series_id参数不存在")

            non_series_id = eval(non_series_id)

            # 查询数据
            result, temp_map_dict = await self.get_non_series_info(start_day, end_day, non_series_id, user_session)

            location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
            non_series_name = user_session.query(CDictNonSery).filter(CDictNonSery.id == non_series_id).first().name

            # 上传并返回下载url
            workbook = openpyxl.Workbook()
            sheet1 = workbook.active

            BASE_DIR = Path(__file__).resolve().parent
            start_day_str = start_day.strftime("%Y-%m-%d")
            end_day_str = end_day.strftime("%Y-%m-%d")
            file_name = f"{start_day_str}~{end_day_str}{location}非时序交易数据.xlsx"
            path = os.path.join(BASE_DIR, file_name)
            sheet1.title = non_series_name

            table_titles = []
            # 准备表头
            for k, v in temp_map_dict.items():
                table_titles.append(v)

            # 准备数据
            data_array = list()
            if result:
                for t in result:
                    li = []
                    for k in temp_map_dict.keys():
                        if k != "datetime":
                            li.append(t[k])
                    data_array.append(li)

            # sheet1 写入标头
            for col_num, header in enumerate(table_titles, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}1'] = header

            # 写入工作表
            for row_num, row_data in enumerate(data_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet1[f'{col_letter}{row_num}'] = cell_value if cell_value is not None and cell_value != '--' else ""
            workbook.save(path)

            # 对上传文件大小做限值，超过40M时提示查询指标太多，数据量太大，请重新选择
            if os.path.getsize(path) > 40 * 1024 * 1024:
                return self.customError("查询指标太多，数据量太大，请重新选择")

            url = await upload_file(file_name, path)

            try:
                os.remove(path)
            except Exception as e:
                pass

            # user_session.close()
            self.returnTypeSuc(url)
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session.close()
