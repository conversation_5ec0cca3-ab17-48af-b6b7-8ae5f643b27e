#!/usr/bin/env python
# coding=utf-8

'''
获取最新告警信息推送给指定人员
'''
import ast, json
import configparser
import sys, os,requests
from Application.Models.User.station import Station
from Application.Models.User.station_relation import StationR
from Tools.Utils.num_utils import real_data
from Tools.DB.mysql_user import user_session
from Tools.DB.redis_con import r_real
from apscheduler.schedulers.blocking import BlockingScheduler

basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)

model_config = configparser.ConfigParser()
_basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = _basepath + "/Application/Cfg/test.ini"
print(path)
model_config.read(path, encoding='utf-8')
scheduler = BlockingScheduler()
gz_name = ast.literal_eval(model_config.get('peizhi', 'gz_name') ) #故障名称
key = "province_middle"
key_b = "battery_cluster_datas_all"  # 定义电池健康度从预警平台获取的原始数据
def RunGetMiddleData():
    scheduler.add_job(get_middle_data, 'interval', hours=1)  # 1小时执行一次
    scheduler.add_job(get_middle_data, 'interval', hours=70)  # 70小时执行一次
    # scheduler.add_job(get_middle_data, 'interval', seconds=2)
    # scheduler.add_job(battery_cluster, 'interval', seconds=4)
    scheduler.start()

stations_list = [["binhai","taicang","ygzhen","zgtian","ygqn","datong"],["shgyu","taicgxr"],["guizhou","tczj","sikly"]]

def battery_cluster():
    '''电池健康度数据'''
 
    headers = {'content-type': "application/json","starkcode":"S003"}
    url = 'http://192.168.1.98:82/yc/HisData/healthScore'
    # reponse1 = requests.post(url=url,json=["binhai","taicang","ygzhen","zgtian","ygqn","datong","shgyu","taicgxr","guizhou","tczj","sikly"],headers=headers)
    BH = []
    for stations in stations_list:
        reponse1 = requests.post(url=url,json=stations,headers=headers)
        BH = BH + reponse1.json()['data']
    print (len(BH))
    r_real.setex(key_b, 72 * 3600, json.dumps(BH))  # 过期时间72小时

def get_middle_data():
    '''
    每1小时去更新地图大屏中间部分
    '''
   
    try:
        pages = user_session.query(Station.name.label('name'),Station.longitude.label('lg'),
                                   Station.dimension.label('ds'), StationR.running_state.label('state'),
                                    StationR.province.label('pro')).filter(Station.name == StationR.station_name,
                                                StationR.id!=37,Station.id!=37,StationR.running_state != 4).order_by(
            Station.index.asc()).all()  # 获取电站
        obj = {}
        zj, ty = 0, 0  # z在建；投运
        province_station = {}
        for pag in pages:
            if pag.pro in obj.keys():  # 已存入
                if pag.state == '1':  # 投运
                    obj[pag.pro][2] = obj[pag.pro][2] + 1
                elif pag.state == '2':  # 在建
                    obj[pag.pro][3] = obj[pag.pro][3] + 1
                province_station[pag.pro].append(pag.name)
            else:
                if pag.state == '1':  # 投运
                    ty = 1
                    zj = 0
                elif pag.state == '2':  # 在建
                    zj = 1
                    ty = 0
                province_station[pag.pro] = [pag.name]
                obj[pag.pro] = [pag.lg, pag.ds, ty, zj, 0]
        for k, v in province_station.items():
            for sta in v:  # 循环所有站
                if sta in gz_name.keys() and sta != 'dongmu':
                    oo = gz_name[sta]  # 配置告警的对象
                    print(len(oo['status']))
                    for name in oo['status']:
                        bean = real_data('status', name, 'db')
                        if bean['value'] == 2:  # 状态有告警
                            obj[k][4] = 1
                            break
                    print(len(oo['discrete']))
                    for names in oo['discrete']:
                        name = list(names.keys())[0]
                        bean = real_data('discrete', name, 'db')
                        if bean['value'] in names[name]:
                            obj[k][4] = 1
                            break
                elif sta == 'baodian':
                    if 'bodian' in gz_name.keys():
                        oo = gz_name[sta]  # 配置告警的对象
                        for names in oo['discrete']:
                            name = list(names.keys())[0]
                            bean = real_data('discrete', name, 'db')
                            if bean['value'] in names[name]:
                                obj[k][4] = 1
                                break
                elif sta == 'dongmu':
                    v2 = real_data('status', 'dongmu', 'db')
                    if v2:
                        e2 = v2['body']
                        for i in e2:
                            if i['device'][:3] == 'PCS':
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()
                                if bit_list_n[14] == '1':  # pcs告警.故障（取同一个值）
                                    obj[k][4] = 1
                                    break

                            elif i['device'][:3] == 'BMS':
                                bit_c = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                                bit_list_c.reverse()
                                if bit_list_c[4] == '1':  # bms告警.故障
                                    obj[k][4] = 1
                                    break
        obj = json.dumps(obj)
        r_real.set(key, obj)
    except Exception as E:
        print(E)
    finally:
        user_session.close()


if __name__ == '__main__':
    # get_middle_data()
    # calculation()
    # battery_cluster()
    RunGetMiddleData()
   