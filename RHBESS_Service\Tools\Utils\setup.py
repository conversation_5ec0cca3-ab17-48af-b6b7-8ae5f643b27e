#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-01 15:14:29
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\setup.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-18 15:06:52

import numpy as np
import requests
soc = [78,75,71,68,65,62,58,55,52,48,45,42,39,35,32,29,25,22,19,15,12,9,5,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,2,5,9,12,15,18,21,23,26,29,33,36,40,43,46,50,53,57,60,64,67,70,73,77,80,84,87,91,94,95,92,88,85,82,79,75,72,69,65,62,59,56,52,49,46,42,39,36,32,29,26,22,19,18,22,25,29,32,36,39,43,46,50,53,57,60,63,66,69,73,76,80,83,87,90,94,97,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,99,99,99,97,95,91,88,85,82]

chag = [6985,6976,6968,6960,6951,6943,6935,6926,6918,6910,6901,6893,6885,6876,6868,6860,6851,6843,6835,6827,6818,6810,6802,6793,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6788,6785,6777,6768,6760,6752,6743,6735,6727,6718,6710,6702,6693,6685,6677,6668,6660,6652,6643,6635,6627,6618,6610,6602,6593,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6588,6587,6585,6580,6575,6568,6560,6551,6543,6535]

disg = [6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6387,6385,6383,6383,6381,6373,6365,6358,6351,6344,6337,6330,6323,6316,6308,6300,6292,6284,6276,6268,6259,6251,6243,6234,6226,6219,6211,6202,6194,6186,6178,6169,6161,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6156,6153,6145,6136,6128,6120,6111,6103,6095,6086,6078,6070,6062,6055,6047,6039,6032,6023,6015,6007,5998,5990,5982,5973,5965,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960,5960]



def count_rate_soc(soc,chag,disg):
    '''
    根据soc计算效率
    soc:soc值，时间倒叙排
    chag：累计充电量，时间倒叙，都是正数
    disg：累计放电量，时间倒叙，都是正数
    '''
    x = np.array(soc)
    d_soc = np.diff(x)  # 后值减去前一个值
    d_chag,d_disg,cc_soc,dd_soc = [],[],[],[]  # 充电差值；放电差值；soc负数；soc正数

    for d in range(len(d_soc)):
        if d_soc[d]<0:
            cc_soc.append(d_soc[d])
            d_chag.append(chag[d+1]-chag[d])
        else:
            dd_soc.append(d_soc[d])
            d_disg.append(disg[d]-disg[d+1])
    # print ('d_chag:',d_chag,len(d_chag),sum(d_chag))
    # print ('d_disg:',d_disg,len(d_disg),sum(d_disg))
    # print (sum(cc_soc),'------',sum(dd_soc))
    if np.sum(dd_soc) == 0 or np.sum(cc_soc) == 0 or np.sum(d_chag)==0:
        return 0
    else:
        return np.sum(d_disg)/np.sum(dd_soc)/(np.sum(d_chag)/np.sum(cc_soc))
    
# print (count_rate_soc(soc,chag,disg))

# 登录
# print ('start login')
# res4 = requests.post('http://172.17.6.48:19094/UserLogin/Login',
#                     params={'account': 'wangyanjie', 'passwd':'123456'})
# print ('res4:',res4.headers)

# cookie = res4.headers.get('Set-Cookie')
# print ('cookie:',cookie)

# # cookie = 'sid="2|1:0|10:**********|3:sid|44:MEJCMTg5OTgzMjYzMTFFRTk5QTcwMDE2M0UwMjY3OEU=|f7eb6c403edbb60471daa0f3e4a2476043ba90397f68f66fa3f86e174545693e"; expires=Sun, 03 Sep 2023 01:05:47 GMT; Path=/'
# sid = cookie.split(';')[0][4:]

# resp = requests.get('http://172.17.6.48:19094/Organization/GetAllType', cookies={'sid':sid})
# print ('组织类型****',resp.text.encode('utf-8').decode('unicode_escape'))

# # 子站
# station = requests.get('http://172.17.6.48:19094/Custom/GetStationList', params={'pageNum': 1, 'pageSize':2},cookies={'sid':sid})
# print ('子站---',station.text.encode('utf-8').decode('unicode_escape'))

# # 所有审核权限的人
# schecku = requests.get('http://172.17.6.48:19095/WorkOrderManager/GetCheckUsers', cookies={'sid':sid})
# print ('审核人---',schecku.text.encode('utf-8').decode('unicode_escape'))
# # 所有区域
# area = requests.get('http://172.17.6.48:19098/SideForecaseUser/GetAreas', cookies={'sid':sid})
# print ('区域---',area.text.encode('utf-8').decode('unicode_escape'))


import xlsxwriter as xw
import time
from Tools.Utils.time_utils import timeUtils
workbook = xw.Workbook('aaa.xls')  # 创建工作簿
def xw_toExcel(data,shell):  # xlsxwriter库储存数据到excel
    worksheet1 = workbook.add_worksheet(shell)  # 创建子表
    worksheet1.activate()  # 激活表
    title = ['尖峰充电','尖峰放电','峰段充电','峰段放电','平段充电','平段放电','谷段充电','谷段放电','时间']  # 设置表头
    worksheet1.write_row('A1', title)  # 从A1单元格开始写入表头
    i = 2  # 从第二行开始写入数据
    for j in range(len(data[0])):
        insertData = []
        for a in range(len(data)):
            insertData.append(data[a][j])
        row = 'A' + str(i)
        worksheet1.write_row(row, insertData)
        i += 1
        print ('------------',time.time(),'*****',j)

def tianlu_chogfang_shiduan():
    # 添禄充放分时段显示
    days=timeUtils.dateToDataList('2023-12-01','2023-12-31')
    stations = ['NBLS001',
    'NBLS002',
    'LJQC001',
    'GLTY201',
    'QXYJ201',
    'QNKX101',
    'QNKX001',
    'HZDC101',
    'RHTBA001',
    'RHTBA002',
    'QNGS201',
    'QNGS202',
    'ZXFN301',
    'JSGQ001',
    'JSBD001',
    'JSXQ001',
    'JSJY001',
    'JSJY002',
    'JSJY101',
    'JSJY102',
    'QNKM301',
    'NRTYN101',
    'DCLR101',
    'DCLR102',
    'NFXR001',
    'NBLS003',
    'QNYX001',
    'QNYX002',
    'HFDZ101',
    'RHRY101',
    'JSJB001',
    'TJBC001',
    'QNKLD101',
    'QNKLD001',
    'SAMPLE1',
    'QNGF101',
    'QNJH001',
    'NBJM001',
    'NBJD001',
    'QNYI001',
    'QNZB101',
    'JTJC301',
    'JTJC302',
    'SAMPLE5',
    'HYRX301',
    'QNHB101',
    'QNHB001',
    'NFHX001',
    'QNXT202',
    'QNDP101',
    'SDTR301',
    'FSHJ101',
    'HZCS001',
    'HZCS101',
    'DNJK201',
    'DZND101',
    'DZND102',
    'SGFS201',
    'CQGS001',
    'HTHJ101',
    'YKLC001',
    'JSSY201',
    'OHJO101',
    'YDDZ101',
    'MJKJ201',
    'FYDX101',
    'MRKJ101',
    'HCGJ101',
    'SHCY001',
    'CPZC301',
    'JSHZ301',
    'DFDR301',
    'DFDR302',
    'DFDR001',]
    
    for station in stations:
        all = [[],[],[],[],[],[],[],[],[]]
        for d in days:
            data = {'station': station, 'time':d}
            res4 = requests.post('http://47.92.91.184:19192/statistics/electricity_count/type/',headers={"Authorization":"eyJhbGciOiJIUzI1NiIsInR5cCI6Imp3dCJ9.eyJ1c2VyX2lkIjo2LCJleHAiOjE3MDU2MzMzMjN9.3o0oUyvgQLQrQMv53oJ_Mvupap48yOdluZRY7-8Bw0U"},
            data=data)
            try :
                re_data = eval(res4.text)['data']
            except Exception as e:
                continue
            # station = re_data['station']

            disg_chag = re_data['detail']
            if disg_chag:
                # 尖峰
                spike_d =  disg_chag['spike']['total_discharge']
                spike_c =  disg_chag['spike']['total_charge']
                # 峰
                peak_d = disg_chag['peak']['total_discharge']
                peak_c = disg_chag['peak']['total_charge']
                # 平
                flat_d = disg_chag['flat']['total_discharge']
                flat_c = disg_chag['flat']['total_charge']
                # 谷
                valley_d = disg_chag['valley']['total_discharge']
                valley_c = disg_chag['valley']['total_charge']

                # print ('尖峰：',spike_c,spike_d)
                # print ('峰：',peak_c,peak_d)
                # print ('平：',flat_c,flat_d)
                # print ('谷：',valley_c,valley_d)
                all[0].append(spike_c)
                all[1].append(spike_d)
                all[2].append(peak_c)
                all[3].append(peak_d)
                all[4].append(flat_c)
                all[5].append(flat_d)
                all[6].append(valley_c)
                all[7].append(valley_d)
                all[8].append(d)
        print ('station:',station,' SUCCESS')
        xw_toExcel(all,station)  # 添禄的
    
if __name__ == "__main__":
    tianlu_chogfang_shiduan()
    

    workbook.close()  # 关闭表








