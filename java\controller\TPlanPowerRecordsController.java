package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsCreateDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsQueryDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsUpdateDTO;
import com.robestec.analysis.service.TPlanPowerRecordsService;
import com.robestec.analysis.vo.TPlanPowerRecordsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功率计划关联记录管理API
 */
@RestController
@RequestMapping("/plan-power-records")
@RequiredArgsConstructor
@Api(tags = "功率计划关联记录管理API")
public class TPlanPowerRecordsController {

    private final TPlanPowerRecordsService tPlanPowerRecordsService;

    @GetMapping
    @ApiOperation("分页查询功率计划关联记录")
    public PageResult<TPlanPowerRecordsVO> queryTPlanPowerRecords(@Validated TPlanPowerRecordsQueryDTO queryDTO) {
        return tPlanPowerRecordsService.queryTPlanPowerRecords(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增功率计划关联记录")
    public Result<Long> createTPlanPowerRecords(@Validated @RequestBody TPlanPowerRecordsCreateDTO createDTO) {
        return Result.succeed(tPlanPowerRecordsService.createTPlanPowerRecords(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增功率计划关联记录")
    public Result createTPlanPowerRecordsList(@Validated @RequestBody List<TPlanPowerRecordsCreateDTO> createDTOList) {
        tPlanPowerRecordsService.createTPlanPowerRecordsList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改功率计划关联记录")
    public Result updateTPlanPowerRecords(@PathVariable Long id, @Validated @RequestBody TPlanPowerRecordsUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tPlanPowerRecordsService.updateTPlanPowerRecords(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除功率计划关联记录")
    public Result deleteTPlanPowerRecords(@PathVariable Long id) {
        tPlanPowerRecordsService.deleteTPlanPowerRecords(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取功率计划关联记录详情")
    public Result<TPlanPowerRecordsVO> getTPlanPowerRecords(@PathVariable Long id) {
        return Result.succeed(tPlanPowerRecordsService.getTPlanPowerRecords(id));
    }

    @GetMapping("/plan/{planId}")
    @ApiOperation("根据计划ID查询功率计划关联记录")
    public Result<List<TPlanPowerRecordsVO>> getTPlanPowerRecordsByPlanId(@PathVariable Long planId) {
        return Result.succeed(tPlanPowerRecordsService.getTPlanPowerRecordsByPlanId(planId));
    }

    @GetMapping("/power/{powerId}")
    @ApiOperation("根据功率ID查询功率计划关联记录")
    public Result<List<TPlanPowerRecordsVO>> getTPlanPowerRecordsByPowerId(@PathVariable Long powerId) {
        return Result.succeed(tPlanPowerRecordsService.getTPlanPowerRecordsByPowerId(powerId));
    }

    @GetMapping("/serial/{serialNumber}")
    @ApiOperation("根据序号查询功率计划关联记录")
    public Result<List<TPlanPowerRecordsVO>> getTPlanPowerRecordsBySerialNumber(@PathVariable Integer serialNumber) {
        return Result.succeed(tPlanPowerRecordsService.getTPlanPowerRecordsBySerialNumber(serialNumber));
    }

    @GetMapping("/count/plan/{planId}")
    @ApiOperation("统计计划ID的关联记录数量")
    public Result<Long> countByPlanId(@PathVariable Long planId) {
        return Result.succeed(tPlanPowerRecordsService.countByPlanId(planId));
    }

    @GetMapping("/count/power/{powerId}")
    @ApiOperation("统计功率ID的关联记录数量")
    public Result<Long> countByPowerId(@PathVariable Long powerId) {
        return Result.succeed(tPlanPowerRecordsService.countByPowerId(powerId));
    }
}
