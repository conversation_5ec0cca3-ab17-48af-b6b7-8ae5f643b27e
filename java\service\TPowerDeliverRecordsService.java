package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsCreateDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsQueryDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsUpdateDTO;
import com.robestec.analysis.entity.TPowerDeliverRecords;
import com.robestec.analysis.vo.TPowerDeliverRecordsVO;

import java.util.List;

/**
 * 功率计划下发记录服务接口
 */
public interface TPowerDeliverRecordsService extends ISuperService<TPowerDeliverRecords> {

    /**
     * 分页查询功率计划下发记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TPowerDeliverRecordsVO> queryTPowerDeliverRecords(TPowerDeliverRecordsQueryDTO queryDTO);

    /**
     * 创建功率计划下发记录
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTPowerDeliverRecords(TPowerDeliverRecordsCreateDTO createDTO);

    /**
     * 更新功率计划下发记录
     * @param updateDTO 更新参数
     */
    void updateTPowerDeliverRecords(TPowerDeliverRecordsUpdateDTO updateDTO);

    /**
     * 删除功率计划下发记录
     * @param id 记录ID
     */
    void deleteTPowerDeliverRecords(Long id);

    /**
     * 获取功率计划下发记录详情
     * @param id 记录ID
     * @return 记录详情
     */
    TPowerDeliverRecordsVO getTPowerDeliverRecords(Long id);

    /**
     * 批量创建功率计划下发记录
     * @param createDTOList 创建参数列表
     */
    void createTPowerDeliverRecordsList(List<TPowerDeliverRecordsCreateDTO> createDTOList);

    /**
     * 根据用户ID查询功率计划下发记录
     * @param userId 用户ID
     * @return 记录列表
     */
    List<TPowerDeliverRecordsVO> getTPowerDeliverRecordsByUserId(Long userId);

    /**
     * 根据计划类型查询功率计划下发记录
     * @param planType 计划类型
     * @return 记录列表
     */
    List<TPowerDeliverRecordsVO> getTPowerDeliverRecordsByPlanType(Integer planType);

    /**
     * 统计用户的功率计划下发记录数量
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(Long userId);
}
