#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/10/14 下午4:07
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me


from django.http import HttpResponseForbidden
from settings.types_dict import IP_WHITE_LIST
from rest_framework.response import Response
from common import common_response_code
from functools import wraps
import ipaddress

def require_ip_whitelist():

    def decorated_function(f):
        @wraps(f)
        def __decorated_function(*args, **kwargs):
            x_forwarded_for = args[1].META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                client_ip = x_forwarded_for.split(',')[0]
            else:
                client_ip = args[1].META.get('REMOTE_ADDR')

            # if client_ip not in IP_WHITE_LIST:
            if not is_ip_in_whitelist(client_ip, IP_WHITE_LIST):
                return Response(
                            {
                                "code": common_response_code.AUTHENTICATION_FIELD,
                                "data": {"message": "error", "detail": "认证失败."},
                            }
                        )
            return f(*args, **kwargs)
        return __decorated_function

    return decorated_function


    # def wrapper(request, *args, **kwargs):
    #     print(args[1].META.get('HTTP_X_FORWARDED_FOR'))
    #     print("aaaa")
    #     ip_whitelist = IP_WHITE_LIST  # 白名单
    #     client_ip = get_client_ip(args)
    #     if client_ip not in ip_whitelist:
    #         return Response(
    #             {
    #                 "code": common_response_code.AUTHENTICATION_FIELD,
    #                 "data": {"message": "error", "detail": "认证失败."},
    #             }
    #         )
    #     return view_func(request, *args, **kwargs)
    #
    # return wrapper

def get_client_ip(args):
    x_forwarded_for = args[1].META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = args[1].META.get('REMOTE_ADDR')
    return ip


def is_ip_in_whitelist(ip, whitelist):
    """
    检查给定的IP地址是否在白名单中。

    :param ip: 要检查的IP地址，字符串格式
    :param whitelist: 白名单，包含子网和固定IP地址的列表
    :return: 如果IP地址在白名单内返回True，否则返回False
    """
    ip_obj = ipaddress.ip_address(ip)

    # 检查固定IP地址
    if ip in whitelist:
        return True

    # 检查子网
    for subnet in whitelist:
        try:
            if ip_obj in ipaddress.ip_network(subnet):
                return True
        except ValueError:
            # 忽略无效的子网
            continue

    return False