# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/25 下午2:03
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : common.py
# @Software : PyCharm
import json
import random
import time
import traceback

import redis
import requests

from LocaleTool import settings
from LocaleTool.MD5 import MD5Tool
from LocaleTool.db_tools import DatabaseTool

appid = '20240109001935690'
appkey = '0ORDSzOBSpmyLpkXKbWk'
url = "https://fanyi-api.baidu.com/api/trans/vip/translate"


user_email = ["<EMAIL>", "<EMAIL>"]

# real_pool = redis.ConnectionPool(host='***********',
#                                  port=6379,
#                                  db=2,
#                                  password='A45lEdj&f335@3s5h*8g',
#                                  decode_responses=False, encoding='UTF-8')

redis_conn = redis.ConnectionPool(host=settings.REDIS_CONF['HOST'],
                                 port=settings.REDIS_CONF['PORT'],
                                 db=settings.REDIS_CONF['DB'],
                                 password=settings.REDIS_CONF['PASSWORD'],
                                 decode_responses=False, encoding='UTF-8')

redis_pool = redis.Redis(connection_pool=redis_conn)


db_tool = DatabaseTool()


def translate_text(text_, ty=None):
    '''
    text_: 需要翻译的文本
    user_email: 异常需要接收邮件的人员，列表[]
    ty: 1: 转中文 ; 2: 转英文to_Lang: 目标语言
    '''
    from_lang = 'zh' if ty == 2 else 'en'
    to_lang = 'en' if ty == 2 else 'zh'

    # translator = Translator(from_lang=from_lang, to_lang=to_lang)
    # translated_text_ = translator.translate(text_)
    # # if 'MYMEMORY WARNING' or 'HTTPS://' in translated_text_:
    # if 'MYMEMORY WARNING' in translated_text_:
    result = ''
    try:
        salt = random.randint(32768, 65536)  # 生产随机数
        try:
            float(text_)
            return text_
        except:
            if '“' in text_:
                text_ = text_.replace('“', '<')
            if '”' in text_:
                text_ = text_.replace('”', '>')
            sign = MD5Tool.get_str_md5(appid + text_ + str(salt) + appkey)  # 签名加密 固定顺序
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            payload = {'appid': appid, 'q': text_, 'from': from_lang, 'to': to_lang, 'salt': salt, 'sign': sign}
            r = requests.post(url, params=payload, headers=headers).json()
            # result = r.json()
            if 'error_code' in r:
                print(81, '翻译失败:', r)
                time.sleep(3)
                r = requests.post(url, params=payload, headers=headers).json()
            print(87, '翻译结果：', r["trans_result"][0]['dst'], '************')
            # print(json.dumps(result, indent=4, ensure_ascii=False),'************')
            return r["trans_result"][0]['dst']
    except Exception as e:
        print(traceback.print_exc())
        print('翻译异常，请关注:', e)
        # sendMail_("您的实时翻译异常，请关注 %s" % e, "实时翻译异常消息通知", "天禄", "XXX", user_email)


class TranslateCls(object):
    """
    翻译类
    """

    def __init__(self, ty):
        """
        :param ty: 1: 英文转中文 ; 2: 中文转英文
        """
        self.ty = ty

    def is_chinese(self, string):
        """
        判断是否存在中文或英文
        :param string:
        :return:
        """
        if self.ty == 2:
            if not string:
                return False
            for ch in string:
                if u'\u4e00' <= ch <= u'\u9fa5':
                    return True
        else:
            for ch in string:
                ch = ord(ch)
                if 65 <= ch <= 90 or (97 <= ch <= 122):
                    return True
        return False

    def list_chinese(self, ch_list):
        """
        处理列表类型
        :param ch_list:
        :return:
        """
        new_list = []
        for i in ch_list:
            if isinstance(i, str):
                if self.is_chinese(i):
                    if '{' in i or '[' in i:
                        i = eval(i)
                        if isinstance(i, dict):
                            i = self.dict_chinese(i)
                        if isinstance(i, list):
                            return self.list_chinese(i)
                    else:
                        new_list.append(translate_text(i, self.ty))
                else:
                    new_list.append(i)
            elif isinstance(i, dict):
                new_list.append(self.dict_chinese(i))
            elif isinstance(i, list):
                new_list.append(self.list_chinese(i))
            else:
                new_list.append(i)
        return new_list

    def dict_chinese(self, ch_dict):
        """
        处理字典类型
        :param ch_dict:
        :return:
        """
        for k, v in ch_dict.items():
            if isinstance(v, str):
                if self.is_chinese(v):
                    ch_dict[k] = self.str_chinese(v)
                else:
                    ch_dict[k] = v
            elif isinstance(v, dict):
                ch_dict[k] = self.dict_chinese(v)
            elif isinstance(v, list):
                ch_dict[k] = self.list_chinese(v)
        return ch_dict

    def str_chinese(self, string):
        """
        字符串类型
        :param string:
        :param ty: 1: 转中文 ; 2: 转英文
        :return:
        """
        if self.is_chinese(string):
            if '{' in string or '[' in string:
                string = eval(string)
                if isinstance(string, dict):
                    string = self.dict_chinese(string)
                elif isinstance(string, list):
                    string = self.list_chinese(string)
            else:
                string = translate_text(string, self.ty)
        if not isinstance(string, str):
            string = json.dumps(string)
        if '\\n' in string:
            string = string.replace('\\n', '/')
        if "'s" in string:
            string = string.replace("'s", " is")
        if "' s" in string:
            string = string.replace("' s", " is")
        if "'an" in string:
            string = string.replace("'an", " an")
        if "o'clock" in string:
            string = string.replace("o'clock", 'of the clock')
        return string
