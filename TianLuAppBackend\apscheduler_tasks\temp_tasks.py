from apis.user import models


def create_master_station_for_origin_station():
    """
    给每个标准站添加一个逻辑主站
    """""
    origin_stations = models.StationDetails.objects.filter(is_delete=0, slave=-1).all()

    for origin_station in origin_stations:
        users = origin_station.userdetails_set.all()
        master_station_is_exist = models.MaterStation.objects.filter(name=origin_station.station_name,
                                                                     english_name=origin_station.english_name,
                                                                     project=origin_station.project, is_delete=0).exists()
        if not master_station_is_exist:
            master_station = models.MaterStation.objects.create(name=origin_station.station_name,
                                                                english_name=origin_station.english_name,
                                                                project=origin_station.project,
                                                                is_v3=1, is_delete=0)
            for user in users:
                master_station.userdetails_set.add(user)
            origin_station.master_station = master_station
            origin_station.save()

create_master_station_for_origin_station()


def add_station_income_table_master_station():
    """
    给历史收益表关联的标准站添加关联的逻辑主站
    """""
    incomes = models.StationIncome.objects.all()
    for income in incomes:
        try:
            if income.station_id and income.master_station is None:
                # print(37, f"收益{income.id}的逻辑主站{income.station_id}不存在！")
                income.master_station = income.station_id.master_station
                income.save()
        except Exception as e:
            print(e)


add_station_income_table_master_station()

