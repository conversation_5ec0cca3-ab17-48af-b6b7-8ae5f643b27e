ARG BASE_NAMESPACE="docker.io"
ARG BASE_IMG="mshroom47/python_base_env:latest"
ARG DIR_APP="/root/apps"
ARG MIRROR_PROFILE="common"


FROM ${BASE_NAMESPACE:+$BASE_NAMESPACE/}${BASE_IMG}
LABEL maintainer=<EMAIL>

ARG DIR_APP
ARG MIRROR_PROFILE
RUN mkdir -pv ${DIR_APP}
COPY . /root/apps
ARG PORT_SVC
USER root
WORKDIR ${DIR_APP}
ENV PORT_SVC=${PORT_SVC:-8001}
RUN pip install -r requirements.txt

VOLUME ${DIR_APP}/logs
EXPOSE ${PORT_SVC}
CMD ["uwsgi", "--ini", "uwsgi_docker_workorder.ini"]


