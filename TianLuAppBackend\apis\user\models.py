import uuid
from django.db import models
from django.forms import model_to_dict
from django.utils import timezone
from common.base_models import BaseModel
from settings.alarm_settings import alarm_detail
# from django_compositefield.models import CompositeUniqueKey

"""
1.站的表(多对多) 1.1 站的收益表 1.2 站的设备表 1.3 点表

"""


class Parameter(BaseModel):
    """参数"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)
    unit = models.CharField(verbose_name="单位", max_length=16, null=True)
    calculate_flag = models.SmallIntegerField(verbose_name="系数计算开关", null=True)
    rate = models.DecimalField(verbose_name="倍率", max_digits=10, decimal_places=2, null=True)
    start_value = models.IntegerField(verbose_name="基数", null=True)

    class Meta:
        db_table = "t_parameter"


class RemoteControl(BaseModel):
    """远控"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)

    class Meta:
        db_table = "t_remotectl"


class Status(BaseModel):
    """状态量"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    status_character = models.CharField(verbose_name="状态字", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)
    a_status_name = models.CharField(verbose_name="状态 0 名称", max_length=32, null=True)
    b_status_name = models.CharField(verbose_name="状态 1 名称", max_length=32, null=True)

    class Meta:
        db_table = "t_status"


class Cumulant(BaseModel):
    """积累量"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)
    unit = models.CharField(verbose_name="单位", max_length=16, null=True)
    calculate_flag = models.SmallIntegerField(verbose_name="系数计算开关", null=True)
    rate = models.DecimalField(verbose_name="倍率", max_digits=10, decimal_places=2, null=True)
    start_value = models.IntegerField(verbose_name="基数", null=True)
    delta = models.IntegerField(verbose_name="变化限值", null=True)
    coef = models.DecimalField(verbose_name="系数", max_digits=10, decimal_places=2, null=True)
    store_flag = models.SmallIntegerField(verbose_name="是否存盘", choices=((0, "否"), (1, "是")), null=True)
    rep_flag = models.SmallIntegerField(verbose_name="是否上报", choices=((0, "否"), (1, "是")), null=True)

    class Meta:
        db_table = "t_cumulant"


class Discrete(BaseModel):
    """离散量"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)
    store_flag = models.SmallIntegerField(verbose_name="是否存盘", choices=((0, "否"), (1, "是")), null=True)
    rep_flag = models.SmallIntegerField(verbose_name="是否上报", choices=((0, "否"), (1, "是")), null=True)
    alarm_flag = models.SmallIntegerField(verbose_name="是否开启预警", choices=((0, "否"), (1, "是")), null=True)
    remark = models.CharField(verbose_name="备注", max_length=128, null=True)

    class Meta:
        db_table = "t_discrete"


class Measure(BaseModel):
    """测量量"""

    unit_description = models.CharField(verbose_name="单元描述", max_length=32, null=True)
    english_name = models.CharField(verbose_name="设备英文名", max_length=8, null=True)
    type = models.CharField(verbose_name="数据类型", max_length=16, null=True)
    data_description = models.CharField(verbose_name="数据描述", max_length=32, null=True)
    point_english = models.CharField(verbose_name="点位英文", max_length=32, null=True)
    calculate_flag = models.SmallIntegerField(verbose_name="系数计算开关", null=True)
    rate = models.DecimalField(verbose_name="倍率", max_digits=10, decimal_places=2, null=True)
    start_value = models.IntegerField(verbose_name="基数", null=True)
    unit = models.CharField(verbose_name="单位", max_length=16, null=True)
    coef = models.DecimalField(verbose_name="系数", max_digits=10, decimal_places=2, null=True)
    store_flag = models.SmallIntegerField(verbose_name="是否存盘", choices=((0, "否"), (1, "是")), null=True)
    rep_flag = models.SmallIntegerField(verbose_name="是否上报", choices=((0, "否"), (1, "是")), null=True)
    alarm_flag = models.SmallIntegerField(verbose_name="是否开启预警", choices=((0, "否"), (1, "是")), null=True)

    class Meta:
        db_table = "t_measure"


class ElectricityProvince(BaseModel):
    name = models.CharField(verbose_name="省份", max_length=128)
    en_name = models.CharField(verbose_name="省份", max_length=128, blank=True, null=True)
    code = models.SmallIntegerField(verbose_name="省份编码", blank=True, null=True)

    class Meta:
        db_table = "t_electricity_province"


# class PeakValley(BaseModel):
#     """峰谷电价表"""
#
#     year_month = models.SmallIntegerField(verbose_name="月", blank=True, null=True)
#     h0 = models.DecimalField(verbose_name="0时电价", max_digits=10, decimal_places=5, default=0)
#     h1 = models.DecimalField(verbose_name="1时电价", max_digits=10, decimal_places=5, default=0)
#     h2 = models.DecimalField(verbose_name="2时电价", max_digits=10, decimal_places=5, default=0)
#     h3 = models.DecimalField(verbose_name="3时电价", max_digits=10, decimal_places=5, default=0)
#     h7 = models.DecimalField(verbose_name="4时电价", max_digits=10, decimal_places=5, default=0)
#     h5 = models.DecimalField(verbose_name="5时电价", max_digits=10, decimal_places=5, default=0)
#     h6 = models.DecimalField(verbose_name="6时电价", max_digits=10, decimal_places=5, default=0)
#     h4 = models.DecimalField(verbose_name="7时电价", max_digits=10, decimal_places=5, default=0)
#     h8 = models.DecimalField(verbose_name="8时电价", max_digits=10, decimal_places=5, default=0)
#     h9 = models.DecimalField(verbose_name="9时电价", max_digits=10, decimal_places=5, default=0)
#     h10 = models.DecimalField(verbose_name="10时电价", max_digits=10, decimal_places=5, default=0)
#     h11 = models.DecimalField(verbose_name="11时电价", max_digits=10, decimal_places=5, default=0)
#     h12 = models.DecimalField(verbose_name="12时电价", max_digits=10, decimal_places=5, default=0)
#     h13 = models.DecimalField(verbose_name="13时电价", max_digits=10, decimal_places=5, default=0)
#     h14 = models.DecimalField(verbose_name="14时电价", max_digits=10, decimal_places=5, default=0)
#     h15 = models.DecimalField(verbose_name="15时电价", max_digits=10, decimal_places=5, default=0)
#     h16 = models.DecimalField(verbose_name="16时电价", max_digits=10, decimal_places=5, default=0)
#     h17 = models.DecimalField(verbose_name="17时电价", max_digits=10, decimal_places=5, default=0)
#     h18 = models.DecimalField(verbose_name="18时电价", max_digits=10, decimal_places=5, default=0)
#     h19 = models.DecimalField(verbose_name="19时电价", max_digits=10, decimal_places=5, default=0)
#     h20 = models.DecimalField(verbose_name="20时电价", max_digits=10, decimal_places=5, default=0)
#     h21 = models.DecimalField(verbose_name="21时电价", max_digits=10, decimal_places=5, default=0)
#     h22 = models.DecimalField(verbose_name="22时电价", max_digits=10, decimal_places=5, default=0)
#     h23 = models.DecimalField(verbose_name="23时电价", max_digits=10, decimal_places=5, default=0)
#     pv0 = models.SmallIntegerField(
#         verbose_name="0时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv1 = models.SmallIntegerField(
#         verbose_name="1时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv2 = models.SmallIntegerField(
#         verbose_name="2时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv3 = models.SmallIntegerField(
#         verbose_name="3时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv7 = models.SmallIntegerField(
#         verbose_name="4时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv5 = models.SmallIntegerField(
#         verbose_name="5时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv6 = models.SmallIntegerField(
#         verbose_name="6时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv4 = models.SmallIntegerField(
#         verbose_name="7时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv8 = models.SmallIntegerField(
#         verbose_name="8时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv9 = models.SmallIntegerField(
#         verbose_name="9时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv10 = models.SmallIntegerField(
#         verbose_name="10时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv11 = models.SmallIntegerField(
#         verbose_name="11时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv12 = models.SmallIntegerField(
#         verbose_name="12时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv13 = models.SmallIntegerField(
#         verbose_name="13时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv14 = models.SmallIntegerField(
#         verbose_name="14时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv15 = models.SmallIntegerField(
#         verbose_name="15时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv16 = models.SmallIntegerField(
#         verbose_name="16时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv17 = models.SmallIntegerField(
#         verbose_name="17时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv18 = models.SmallIntegerField(
#         verbose_name="18时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv19 = models.SmallIntegerField(
#         verbose_name="19时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv20 = models.SmallIntegerField(
#         verbose_name="20时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv21 = models.SmallIntegerField(
#         verbose_name="21时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv22 = models.SmallIntegerField(
#         verbose_name="22时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     pv23 = models.SmallIntegerField(
#         verbose_name="23时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         default=0,
#     )
#     TYPE_CHOICE = (
#         (1, "大工业"),
#         (2, "一般工商业"),
#         (3, "单一制工商业用户"),
#         (4, "大工业用电(两部制)"),
#         (5, "单一制"),
#         (6, "两部制"),
#         (7, "两部制工商业用户"),
#         (8, "100千伏安及以上(两部制)大工业"),
#         (9, "100千伏安及以上(两部制)非大工业"),
#         (10, "100千伏安以下(单一制,含行政事业单位办公场所用电)"),
#         (11, "大量工商业及其他用电(250kWh及以下/千伏安•月)"),
#         (12, "大量工商业及其他用电(250kWh及以上/千伏安•月)"),
#         (13, "高需求工商业及其他用电(400kWh及以下/千伏安•月)"),
#         (14, "高需求工商业及其他用电（400kWh及以上/千伏安•月)"),
#     )
#     type = models.SmallIntegerField(verbose_name="用电类型", choices=TYPE_CHOICE, blank=True, null=True)
#     province = models.ForeignKey(
#         to="ElectricityProvince",
#         verbose_name="省份",
#         on_delete=models.DO_NOTHING,
#         blank=True,
#         null=True,
#     )
#     LEVEL_CHOICE = (
#         (1, "不满1千伏"),
#         (2, "1-10千伏"),
#         (3, "35千伏"),
#         (4, "110千伏"),
#         (5, "220千伏及以上"),
#         (6, "35-110千伏（不含）"),
#         (7, "110-220千伏（不含）"),
#         (8, "35千伏及以上"),
#         (9, "35-110千伏以下"),
#         (10, "110-220千伏以下"),
#         (11, "110 (66)千伏"),
#         (12, "20千伏"),
#         (13, "66 千伏"),
#         (14, "220 千伏"),
#         (15, "110千伏及以上"),
#         (16, "20-35 千伏以下"),
#         (17, "110-330 千伏"),
#         (18, "330千伏及以上"),
#         (19, "35-110千伏"),
#         (20, "10 (20)千伏"),
#         (21, "35 千伏以下"),
#         (22, "10千伏"),
#         (23, "1-10（20）千伏"),
#         (24, "220 (330)千伏"),
#         (25, "100千伏安及以下和公变接入用电"),
#         (26, "10千伏高供低计（380V/220V计量"),
#         (27, "10千伏高供低计"),
#     )
#     level = models.SmallIntegerField(verbose_name="电压等级", choices=LEVEL_CHOICE, blank=True, null=True)
#
#     class Meta:
#         db_table = "t_price"


class StationIncome(BaseModel):  # 加字段判断是否是录入的
    """站对应的收益"""

    day_income = models.DecimalField(verbose_name="每日收益金额", max_digits=10, decimal_places=2, default=0)
    peak_load_shifting = models.DecimalField(verbose_name="削峰填谷收益", max_digits=10, decimal_places=2, default=0)
    demand_side_response = models.DecimalField(verbose_name="需求侧响应", max_digits=10, decimal_places=2, default=0)
    station_id = models.ForeignKey(to="StationDetails", verbose_name="站名", blank=True, null=True, on_delete=models.DO_NOTHING)
    master_station = models.ForeignKey(to="MaterStation", verbose_name="主站名", blank=True, null=True, on_delete=models.DO_NOTHING)
    income_date = models.DateField(verbose_name="收益时间", blank=True)
    income_type = models.SmallIntegerField(verbose_name="收益类型", choices=((1, "削峰填谷"), (2, "需求侧响应")), default=1)
    notes = models.CharField(verbose_name="备注", max_length=256, null=True)
    record = models.SmallIntegerField(verbose_name="是否手动录入", choices=((1, "是"), (2, "否")), default=2)

    class Meta:
        db_table = "t_station_income"


class ElectricityQuantity(BaseModel):
    """电量表"""

    in_electricity = models.CharField(verbose_name="充电量", max_length=32)
    out_electricity = models.CharField(verbose_name="放电量", max_length=32)
    station_id = models.ForeignKey(to="StationDetails", verbose_name="站名", blank=True, on_delete=models.DO_NOTHING)
    electricity_date = models.DateField(verbose_name="电量日期", blank=True)

    class Meta:
        db_table = "t_quantity_electricity"


class StationPlanHistory(BaseModel):
    """下发历史记录表"""

    status = models.SmallIntegerField(verbose_name="下发状态", choices=((1, "已保存"), (2, "已下发"), (3, "已执行"), (4, "已跳过")), default=1)
    power = models.CharField(verbose_name="功率", max_length=32, null=True)
    start_time = models.DateTimeField(verbose_name="计划开始时间")
    end_time = models.DateTimeField(verbose_name="计划结束时间")
    power_follow = models.SmallIntegerField(verbose_name="符合跟随", choices=((1, "是"), (2, "否")))
    user = models.ForeignKey(
        to="UserDetails",
        verbose_name="用户",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    station = models.ForeignKey(
        to="MaterStation",
        verbose_name="站名",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    unique = models.CharField(verbose_name="唯一 id", max_length=64, default=uuid.uuid4())

    class Meta:
        db_table = "t_plan_history"


class Unit(BaseModel):
    """单元"""

    unit_name = models.CharField(verbose_name="单元名", max_length=32, blank=True)
    en_unit_name = models.CharField(verbose_name="单元名", max_length=128, blank=True, null=True)
    english_name = models.CharField(verbose_name="单元英文名", max_length=32, blank=True)
    unit_new_name = models.CharField(verbose_name="别名", max_length=32, blank=True)
    en_unit_new_name = models.CharField(verbose_name="别名", max_length=128, blank=True, null=True)
    unit_status = models.SmallIntegerField(verbose_name="单元开关状态", choices=((1, "开"), (2, "关")), default=2)
    status = models.SmallIntegerField(
        verbose_name="状态",
        choices=((1, "正常"), (2, "告警"), (3, "故障"), (4, "离线")),
        default=4,
    )
    rated_power = models.CharField(verbose_name="额定功率", max_length=32, blank=True, default=100)
    rated_power_unit = models.CharField(verbose_name="额定功率单位", max_length=16, default="kW")
    rated_capacity = models.CharField(verbose_name="额定容量", max_length=32, blank=True, default=232.96)
    rated_capacity_unit = models.CharField(verbose_name="额定容量单位", max_length=16, default="kWh")
    meter = models.SmallIntegerField(verbose_name="电表数量", blank=True, default=1)
    pcs_number = models.SmallIntegerField(verbose_name="pcs数量", blank=True, default=1)
    fire_fighting = models.SmallIntegerField(verbose_name="消防数量", blank=True, default=1)
    cabinet = models.SmallIntegerField(verbose_name=" ", blank=True, default=1)
    battery_cluster = models.SmallIntegerField(verbose_name="电池簇数量", blank=True, default=1)
    user = models.ForeignKey(
        to="UserDetails",
        verbose_name="用户",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="站名",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    bms = models.CharField(verbose_name="BMS名称", max_length=32, blank=True)
    pcs = models.CharField(verbose_name="PCS名称", max_length=32, blank=True)
    # meter_position = models.SmallIntegerField(verbose_name="电表位置", choices=((1, "电表前置"), (2, "电表后置")), default=1)
    # transformer_capacity = models.CharField(verbose_name="变压器容量", max_length=255, blank=True, null=True)
    v_number = models.SmallIntegerField(verbose_name="版本号", blank=True, default=1)
    is_delete = models.BooleanField(verbose_name="是否删除", default=0)

    class Meta:
        db_table = "t_unit"


class Project(models.Model):
    """项目名"""

    name = models.CharField(verbose_name="站名", max_length=32)
    en_name = models.CharField(verbose_name="站名", max_length=256, blank=True, null=True)
    city = models.CharField(verbose_name="市名", max_length=32, blank=True, null=True)
    en_city = models.CharField(verbose_name="市名", max_length=256, blank=True, null=True)
    address = models.CharField(verbose_name="地址", max_length=128, blank=True, null=True)
    en_address = models.CharField(verbose_name="地址", max_length=512, blank=True, null=True)
    longitude = models.CharField(verbose_name="经度", max_length=32, blank=True, null=True)
    latitude = models.CharField(verbose_name="纬度", max_length=32, blank=True, null=True)
    english_name = models.CharField(verbose_name="站英文名", max_length=32)
    rated_power = models.CharField(verbose_name="额定功率", max_length=32, blank=True)
    rated_power_unit = models.CharField(verbose_name="额定功率单位", max_length=16, default="kW")
    rated_capacity = models.CharField(verbose_name="额定容量", max_length=32, blank=True)
    rated_capacity_unit = models.CharField(verbose_name="额定容量单位", max_length=16, default="kWh")
    station_number = models.SmallIntegerField(verbose_name="单元数量", blank=True, default=10)
    in_time = models.DateTimeField(verbose_name="接入时间", blank=True, null=True)
    user = models.ManyToManyField(to="UserDetails", verbose_name="关联用户", blank=True)
    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    type = models.SmallIntegerField(verbose_name="默认用电类型", blank=True, null=True)
    level = models.SmallIntegerField(verbose_name="默认用电等级", blank=True, null=True)
    is_used = models.SmallIntegerField(verbose_name="是否可用", choices=((1, "是"), (2, "否"), (3, '不启用')), default=3)
    # 客户经理
    manager = models.CharField(verbose_name="客户经理", max_length=32, blank=True, null=True)
    en_manager = models.CharField(verbose_name="客户经理", max_length=128, blank=True, null=True)
    # 客户经理电话
    manager_phone = models.CharField(verbose_name="客户经理电话", max_length=32, blank=True, null=True)
    contact_person = models.CharField(verbose_name="项目客户联系人", max_length=32, blank=True, null=True)
    contact_person_phone = models.CharField(verbose_name="项目客户联系人电话", max_length=32, blank=True, null=True)
    organization = models.ForeignKey(
        to="Organization",
        verbose_name="组织",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    # meter_position = models.SmallIntegerField(verbose_name="电表位置", choices=((1, "电表前置"), (2, "电表后置")), default=1)
    # transformer_capacity = models.CharField(verbose_name="变压器容量", max_length=255, blank=True, null=True)
    price_type = models.SmallIntegerField(verbose_name="电价类型", choices=((1, "代理购电"), (2, "自定义电价")), default=1)
    counties = models.ForeignKey(
        to="Counties",
        verbose_name="区县",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    connect_time = models.DateTimeField(verbose_name="并网日期", blank=True, null=True)
    compant_name = models.CharField(verbose_name="企业名称", max_length=64, blank=True, null=True)
    en_compant_name = models.CharField(verbose_name="企业名称", max_length=512, blank=True, null=True)
    compant_code = models.CharField(verbose_name="企业编码", max_length=32, blank=True, null=True)
    industry = models.ForeignKey(
        to="Industry",
        verbose_name="行业",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    images = models.TextField(verbose_name="文件连接，多个以英文逗号隔开", blank=True, null=True)
    application_scenario = models.SmallIntegerField(verbose_name="应用场景：0：峰谷套利；1：台区治理", blank=True, null=True, default=0)
    project_type = models.SmallIntegerField(verbose_name="项目类型：0:EMC-自投、1:经租类、2:销售类", default=0,  blank=True, null=True)
    gf_cap = models.FloatField(verbose_name="光伏规模",  blank=True, null=True)
    demand_cumput = models.SmallIntegerField(verbose_name="需量计算方式：0:按需量计费; 1:按容量计费", default=0,  blank=True, null=True)
    demand_cap = models.FloatField(verbose_name="需量", blank=True, null=True)
    debug_user = models.CharField(verbose_name="调试人员", max_length=128, blank=True, null=True)
    en_debug_user = models.CharField(verbose_name="调试人员-英文", max_length=256, blank=True, null=True)
    afftet_sale_user = models.CharField(verbose_name="售后负责人", max_length=128, blank=True, null=True)
    en_afftet_sale_user = models.CharField(verbose_name="售后负责人-英文", max_length=256, blank=True, null=True)
    create_time = models.DateTimeField(verbose_name="注册时间")
    class Meta:
        db_table = "t_project"


class City(BaseModel):
    """城市"""

    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.CASCADE
    )
    name = models.CharField(verbose_name="城市名称", max_length=32)
    en_name = models.CharField(verbose_name="城市名称", max_length=128, blank=True, null=True)
    code = models.SmallIntegerField(verbose_name="城市编码", blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_citys"

class Counties(BaseModel):
    """区县"""

    city = models.ForeignKey(
        to="City",
        verbose_name="城市",
        on_delete=models.CASCADE
    )
    name = models.CharField(verbose_name="区县名称", max_length=32)
    en_name = models.CharField(verbose_name="区县名称", max_length=128, blank=True, null=True)
    code = models.IntegerField(verbose_name="区县编码", blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_counties"


class Industry(BaseModel):
    """行业"""

    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    name = models.CharField(verbose_name="行业名称", max_length=32)
    en_name = models.CharField(verbose_name="行业名称", max_length=256, blank=True, null=True)
    code = models.IntegerField(verbose_name="行业编码", blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_industry"


class PriceType(BaseModel):
    """行业类型"""

    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    name = models.CharField(verbose_name="电价类别名称", max_length=32)
    en_name = models.CharField(verbose_name="电价类别名称", max_length=128, blank=True, null=True)
    code = models.IntegerField(verbose_name="电价类别编码", blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_price_type"


class PriceLevel(BaseModel):
    """电压等级"""

    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    name = models.CharField(verbose_name="电压等级名称", max_length=32)
    code = models.IntegerField(verbose_name="电压等级编码", blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_price_level"



class SideRegisterAccount(BaseModel):
    """需求侧相应注册记录表"""

    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.CASCADE
    )
    station = models.ForeignKey(
        to="MaterStation",
        verbose_name="主站",
        on_delete=models.SET_NULL,
        null=True
    )
    project = models.ForeignKey(
        to="Project",
        verbose_name="项目",
        on_delete=models.CASCADE
    )
    compant_name = models.CharField(verbose_name="企业名称", max_length=64)
    en_compant_name = models.CharField(verbose_name="企业名称", max_length=256)
    compant_code = models.CharField(verbose_name="企业编码", max_length=32)
    price_type = models.ForeignKey(
        to="PriceType",
        verbose_name="电价类别",
        on_delete=models.CASCADE
    )
    city = models.ForeignKey(
        to="City",
        verbose_name="城市",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    city_counties = models.CharField(verbose_name="地区：市+区县", max_length=32)
    en_city_counties = models.CharField(verbose_name="地区：市+区县", max_length=256, blank=True, null=True)
    counties = models.ForeignKey(
        to="Counties",
        verbose_name="区县",
        on_delete=models.CASCADE
    )
    industry = models.ForeignKey(
        to="Industry",
        verbose_name="行业",
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    compact_cap = models.FloatField(verbose_name="合同容量")
    level_name = models.CharField(verbose_name="电压等级", max_length=32)
    run_cap = models.FloatField(verbose_name="运行容量")
    address = models.CharField(verbose_name="用电地址", max_length=128)
    en_address = models.CharField(verbose_name="用电地址", max_length=512, blank=True, null=True)
    user_code = models.CharField(verbose_name="用户编号", max_length=20, blank=True, null=True)
    station_code = models.CharField(verbose_name='监测点编号', max_length=32)
    connect_time = models.DateTimeField(verbose_name="接入时间", blank=True, null=True)
    is_register = models.SmallIntegerField(verbose_name="是否注册1是0否默认0", default=0)
    is_use = models.SmallIntegerField(verbose_name="是否使用1是0否默认1", default=1)

    class Meta:
        db_table = "t_side_register_account"

class Organization(BaseModel):
    """组织架构"""

    name = models.CharField(verbose_name="组织名", max_length=32)
    en_name = models.CharField(verbose_name="组织名", max_length=128, blank=True, null=True)
    remark = models.CharField(verbose_name="备注", max_length=1024, null=True)
    en_remark = models.CharField(verbose_name="备注", max_length=2048, blank=True, null=True)
    is_used = models.SmallIntegerField(verbose_name="是否可用", choices=((1, "是"), (2, "否")), default=1)
    uuid = models.CharField(max_length=64, null=False)
    file = models.FileField(upload_to='picture/')  # 用于存储文件本身
    file_name = models.CharField(max_length=255)  # 文件名
    url = models.CharField(max_length=255)  # 文件路径
    class Meta:
        db_table = "t_organization"

    def __str__(self):
        return self.file_name

    def get_download_url(self):
        return self.file.url


class MaterStation(BaseModel):
    """
    主站（主从模式功能兼容性开发.新增）
    """""
    name = models.CharField(verbose_name="主站名", max_length=32)
    en_name = models.CharField(verbose_name="主站名", max_length=256, null=True, blank=True)
    english_name = models.CharField(verbose_name="站英文名", max_length=32, blank=True)
    is_v3 = models.BooleanField(verbose_name="是否v3", default=1)
    is_account = models.BooleanField(verbose_name="是否外接结算电表", default=0)
    rate = models.FloatField(verbose_name="倍率", null=True, blank=True)
    meter_number = models.CharField(verbose_name="表编号", max_length=64, blank=True, null=True)
    is_delete = models.BooleanField(verbose_name="是否删除", default=0)
    project = models.ForeignKey(
        to="Project",
        verbose_name="项目名",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    efficiency = models.CharField(verbose_name="充放电效率", blank=True, null=True, max_length=16, default='--')
    update_time = models.DateTimeField(verbose_name="修改时间", auto_now=True)
    mode = models.SmallIntegerField(verbose_name="配置模式", choices=((1, "常规模式"), (2, "标准主从模式"),
                                                                      (3, "级联主从模式"), (4, "逻辑主从模式")), default=1)
    up_vol = models.SmallIntegerField(verbose_name="并网点变压器上限阈值", null=True, blank=True)
    low_vol = models.SmallIntegerField(verbose_name="并网点变压器下限阈值", null=True, blank=True)

    class Meta:
        db_table = "t_master_stations"
        unique_together = ('name', 'english_name')


class StationDetails(BaseModel):  # 网关 + 国家省市区
    """(从)站"""

    station_name = models.CharField(verbose_name="站名", max_length=32)
    en_station_name = models.CharField(verbose_name="站名", max_length=256, blank=True, null=True)
    db = models.CharField(verbose_name="数据库名", max_length=32, blank=True, null=True)
    app = models.CharField(verbose_name="app名称", max_length=32, null=True)
    english_name = models.CharField(verbose_name="站英文名", max_length=32, blank=True)
    meter = models.SmallIntegerField(verbose_name="电表数量", blank=True, default=0)
    specifications = models.CharField(verbose_name="电站规格", max_length=32, blank=True)
    rated_power = models.CharField(verbose_name="额定功率", max_length=32, blank=True)
    rated_power_unit = models.CharField(verbose_name="额定功率单位", max_length=16, default="kW")
    rated_capacity = models.CharField(verbose_name="额定容量", max_length=32, blank=True)
    rated_capacity_unit = models.CharField(verbose_name="额定容量单位", max_length=16, default="kWh")
    pcs_number = models.SmallIntegerField(verbose_name="pcs数量", blank=True, default=0)
    unit_number = models.SmallIntegerField(verbose_name="单元数量", blank=True, default=10)
    fire_fighting = models.SmallIntegerField(verbose_name="消防数量", blank=True, default=0)
    cabinet = models.SmallIntegerField(verbose_name="柜体数量", blank=True, default=0)
    battery_cluster = models.SmallIntegerField(verbose_name="电池簇数量", blank=True, default=0)
    address = models.CharField(verbose_name="地址", max_length=256)
    en_address = models.CharField(verbose_name="地址", max_length=512, blank=True, null=True)
    emq_user = models.CharField(verbose_name="emq用户名", max_length=32, blank=True, null=True)
    emq_pwd = models.CharField(verbose_name="emq密码", max_length=32, blank=True, null=True)
    emq_clid = models.CharField(verbose_name="emq clid", max_length=128, blank=True, null=True)
    ini_num = models.CharField(verbose_name="出厂编号", max_length=128, blank=True, null=True)
    int_num = models.CharField(verbose_name="物联网卡编号", max_length=128, blank=True, null=True)
    meter_position = models.SmallIntegerField(verbose_name="电表位置", choices=((1, "电表前置"), (2, "电表后置")), default=1)
    transformer_capacity = models.CharField(verbose_name="变压器容量", max_length=255, blank=True, null=True)
    slave = models.SmallIntegerField(verbose_name="是否有从站", blank=True, default=-1)
    pack = models.SmallIntegerField(verbose_name="是否分包", blank=True, default=-1)
    is_account = models.BooleanField(verbose_name="是否外接结算电表", default=0)
    # rate = models.FloatField(verbose_name="倍率", null=True, blank=True)
    meter_number = models.CharField(verbose_name="表编号", max_length=64, blank=True, null=True)

    project = models.ForeignKey(
        to="Project",
        verbose_name="项目名",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    master_station = models.ForeignKey(
        to="MaterStation",
        verbose_name="主站名",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    circulate = models.IntegerField(verbose_name="总循环次数", default=0)  # 准备删除
    control_strategy = models.SmallIntegerField(verbose_name="控制策略", choices=((1, "open"), (0, "close")), default=1)  # 准备删除
    station_status = models.SmallIntegerField(
        verbose_name="状态",
        choices=((1, "正常"), (2, "告警"), (3, "故障"), (4, "离线")),
        default=4,
    )  # 准备删除
    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    type = models.SmallIntegerField(verbose_name="默认用电类型", blank=True, null=True)
    level = models.SmallIntegerField(verbose_name="默认用电等级", blank=True, null=True)
    meter_type = models.SmallIntegerField(
        verbose_name="电表类型(图表专用)",
        blank=True,
        null=True,
        choices=((1, "自带电表(CuCha)"), (2, "外接电表(PAE)"), (3, "PCS")),
    )
    meter_count = models.SmallIntegerField(
        verbose_name="电表类型(计算循环次数专用)",
        blank=True,
        null=True,
        choices=((1, "自带电表(CuCha)"), (2, "外接点表(PAE)"), (3, "PCS")),
    )
    is_delete = models.BooleanField(verbose_name="是否删除", default=0)

    class Meta:
        db_table = "t_stations"


class UserDetails(BaseModel):
    """用户表"""

    user_name = models.CharField(verbose_name="用户名", max_length=32)
    en_user_name = models.CharField(verbose_name="用户名", max_length=256, blank=True, null=True)
    login_name = models.CharField(verbose_name="登录账号", max_length=32)
    mobile = models.CharField(verbose_name="手机号", max_length=11)
    password = models.CharField(verbose_name="密码", max_length=64)
    gender = models.SmallIntegerField(verbose_name="性别", choices=((1, "男"), (2, "女"), (3, "保密")), default=3)
    type = models.SmallIntegerField(verbose_name="用户类型", choices=((0, "内部人员"), (1, "客户"), (2, "开发人员")), blank=True, null=True)
    roles = models.ManyToManyField(to="Role", verbose_name="用户角色", blank=True)
    stations = models.ManyToManyField(to="StationDetails", verbose_name="用户关联的站", blank=True)
    master_stations = models.ManyToManyField(to="MaterStation", verbose_name="用户关联的主站", blank=True)
    email = models.EmailField(verbose_name="邮箱", null=True)
    tissue = models.CharField(verbose_name="组织", max_length=256)
    en_tissue = models.CharField(verbose_name="组织", max_length=256, blank=True, null=True)
    tissue_id = models.CharField(verbose_name="组织id", max_length=64)
    remark = models.CharField(verbose_name="备注", max_length=1028, null=True)
    is_used = models.SmallIntegerField(verbose_name="是否可用", choices=((1, "是"), (2, "否")), default=1)
    is_send_message = models.SmallIntegerField(verbose_name="是否发送短信", choices=(
        (1, "是"),
        (0, "否")
    ), default=0, blank=True, null=True)
    is_send_email = models.SmallIntegerField(verbose_name="是否发送邮件", choices=(
        (1, "是"),
        (0, "否")
    ), default=0, blank=True, null=True)

    def __repr__(self):
        bean = "{'id':%s,'user_name':'%s','login_name':'%s','mobile':'%s','password':'%s','gender':'%s','type':'%s','roles':'%s','stations':'%s','email':'%s','tissue':'%s','remark':'%s','is_used':'%s'}" % (
            self.id,self.user_name,self.login_name,self.mobile,self.password,self.gender,self.type,self.roles,self.stations,self.email,self.tissue,self.remark,self.is_used)
        return bean.replace("None",'')

    def __str__(self):
        return self.user_name

    class Meta:
        db_table = "t_user"

class Record(BaseModel):
    """下发指令记录"""

    user = models.CharField(verbose_name="用户", max_length=64)
    topic = models.CharField(verbose_name="主题", max_length=512)
    message = models.TextField(verbose_name="指令", max_length=4096 * 2)
    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="站",
        blank=True,
        null=True,
        on_delete=models.DO_NOTHING,
    )
    plan = models.ForeignKey(
        to="UserAutomation",
        verbose_name="下发策略",
        blank=True,
        null=True,
        on_delete=models.DO_NOTHING,
    )
    strategy = models.ForeignKey(
        to="statistics_apis.UserStrategy",
        verbose_name="下发策略",
        blank=True,
        null=True,
        on_delete=models.DO_NOTHING,
    )

    class Meta:
        db_table = "t_record"


class Role(models.Model):
    """角色表"""

    role_name = models.CharField(verbose_name="角色名称", max_length=32)
    en_role_name = models.CharField(verbose_name="角色名称", max_length=128, blank=True, null=True)
    permissions = models.ManyToManyField(verbose_name="拥有的所有权限", to="Permissions", blank=True)
    type = models.SmallIntegerField(verbose_name="角色类型", choices=((0, "小程序"), (1, "Web")))
    remark = models.CharField(verbose_name="备注", max_length=1028, null=True)
    en_remark = models.CharField(verbose_name="备注", max_length=256, null=True, blank=True)
    is_used = models.SmallIntegerField(verbose_name="是否可用", choices=((1, "是"), (2, "否")), default=1)


    def __repr__(self):
        bean = "{'id':%s,'role_name':'%s','type':'%s','remark':'%s','is_used':'%s'}" % (
            self.id,self.role_name,self.type,self.remark,self.is_used)
        return bean.replace("None",'')

    class Meta:
        db_table = "t_role"



class Permissions(models.Model):
    """权限表"""

    title = models.CharField(verbose_name="标题", max_length=32)
    en_title = models.CharField(verbose_name="标题", max_length=256, blank=True, null=True)
    ty = models.SmallIntegerField(verbose_name="权限类型", choices=((0, "小程序"), (1, "Web"), (2, "小程序v2.0")))
    url = models.CharField(verbose_name="URL", max_length=128)
    parent_id = models.SmallIntegerField(verbose_name="父id")

    class Meta:
        db_table = "t_permission"




class Alarm(BaseModel):
    """告警上报"""

    app_name = models.CharField(verbose_name="app", max_length=32)
    station_name = models.CharField(verbose_name="站点名称", max_length=32)
    data_type = models.CharField(verbose_name="数据类型(测量值/状态量/离散量等)", max_length=32)
    time = models.CharField(verbose_name="告警时间 设备-app", max_length=32)
    utime = models.CharField(verbose_name="上报时间，app->mq", max_length=32)
    data_info = models.JSONField(verbose_name="测点内容")
    report_type = models.SmallIntegerField(verbose_name="上报类型", choices=((1, "网关上报"), (2, "数据分析上报")))

    # status = models.SmallIntegerField(verbose_name="状态", choices=((1, "网关上报"), (2, "数据分析上报")), default=1)

    class Meta:
        db_table = "t_alarm"


class Unalarm(BaseModel):
    """告警上报"""

    app_name = models.CharField(verbose_name="app", max_length=32)
    station_name = models.CharField(verbose_name="站点名称", max_length=32)
    data_type = models.CharField(verbose_name="数据类型(测量值/状态量/离散量等)", max_length=32)
    time = models.CharField(verbose_name="告警时间 设备-app", max_length=32)
    utime = models.CharField(verbose_name="上报时间，app->mq", max_length=32)
    data_info = models.JSONField(verbose_name="测点内容")
    report_type = models.SmallIntegerField(verbose_name="上报类型", choices=((1, "网关上报"), (2, "数据分析上报")))

    # status = models.SmallIntegerField(verbose_name="状态", choices=((1, "网关上报"), (2, "数据分析上报")), default=1)

    class Meta:
        db_table = "t_unalarm"


class SummerAutomation(BaseModel):
    """峰谷标识"""

    name = models.CharField(verbose_name="策略名", max_length=128, blank=True, null=True)
    uid = models.UUIDField(verbose_name="唯一标识", blank=True, null=True)
    summer_start = models.SmallIntegerField(verbose_name="夏季开始月份")
    summer_end = models.SmallIntegerField(verbose_name="夏季结束")
    stations = models.ForeignKey(
        to="StationDetails",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )

    user = models.SmallIntegerField(verbose_name="下发用户id", null=True, blank=True)
    pv0 = models.SmallIntegerField(
        verbose_name="0时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv1 = models.SmallIntegerField(
        verbose_name="1时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv2 = models.SmallIntegerField(
        verbose_name="2时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv3 = models.SmallIntegerField(
        verbose_name="3时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv7 = models.SmallIntegerField(
        verbose_name="4时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv5 = models.SmallIntegerField(
        verbose_name="5时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv6 = models.SmallIntegerField(
        verbose_name="6时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv4 = models.SmallIntegerField(
        verbose_name="7时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv8 = models.SmallIntegerField(
        verbose_name="8时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv9 = models.SmallIntegerField(
        verbose_name="9时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv10 = models.SmallIntegerField(
        verbose_name="10时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv11 = models.SmallIntegerField(
        verbose_name="11时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv12 = models.SmallIntegerField(
        verbose_name="12时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv13 = models.SmallIntegerField(
        verbose_name="13时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv14 = models.SmallIntegerField(
        verbose_name="14时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv15 = models.SmallIntegerField(
        verbose_name="15时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv16 = models.SmallIntegerField(
        verbose_name="16时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv17 = models.SmallIntegerField(
        verbose_name="17时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv18 = models.SmallIntegerField(
        verbose_name="18时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv19 = models.SmallIntegerField(
        verbose_name="19时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv20 = models.SmallIntegerField(
        verbose_name="20时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv21 = models.SmallIntegerField(
        verbose_name="21时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv22 = models.SmallIntegerField(
        verbose_name="22时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv23 = models.SmallIntegerField(
        verbose_name="23时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv0_no = models.SmallIntegerField(
        verbose_name="0时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv1_no = models.SmallIntegerField(
        verbose_name="非夏季1时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv2_no = models.SmallIntegerField(
        verbose_name="非夏季2时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv3_no = models.SmallIntegerField(
        verbose_name="非夏季3时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv7_no = models.SmallIntegerField(
        verbose_name="非夏季4时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv5_no = models.SmallIntegerField(
        verbose_name="非夏季5时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv6_no = models.SmallIntegerField(
        verbose_name="非夏季6时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv4_no = models.SmallIntegerField(
        verbose_name="非夏季7时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv8_no = models.SmallIntegerField(
        verbose_name="非夏季8时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv9_no = models.SmallIntegerField(
        verbose_name="非夏季9时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv10_no = models.SmallIntegerField(
        verbose_name="非夏季10时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv11_no = models.SmallIntegerField(
        verbose_name="非夏季11时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv12_no = models.SmallIntegerField(
        verbose_name="非夏季12时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv13_no = models.SmallIntegerField(
        verbose_name="非夏季13时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv14_no = models.SmallIntegerField(
        verbose_name="非夏季14时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv15_no = models.SmallIntegerField(
        verbose_name="非夏季15时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv16_no = models.SmallIntegerField(
        verbose_name="非夏季16时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv17_no = models.SmallIntegerField(
        verbose_name="非夏季17时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv18_no = models.SmallIntegerField(
        verbose_name="非夏季18时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv19_no = models.SmallIntegerField(
        verbose_name="非夏季19时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv20_no = models.SmallIntegerField(
        verbose_name="非夏季20时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv21_no = models.SmallIntegerField(
        verbose_name="非夏季21时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv22_no = models.SmallIntegerField(
        verbose_name="非夏季22时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv23_no = models.SmallIntegerField(
        verbose_name="非夏季23时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    nosummer_start = models.SmallIntegerField(verbose_name="非夏季开始月份", default=9)
    nosummer_end = models.SmallIntegerField(verbose_name="非夏季结束月份", default=5)

    class Meta:
        db_table = "t_automation"


class UserAutomation(BaseModel):
    """用户自动控制策略"""

    name = models.CharField(verbose_name="策略名", max_length=128, blank=True, null=True)
    user = models.ForeignKey(
        to="UserDetails",
        verbose_name="用户",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    summer_start = models.SmallIntegerField(verbose_name="夏季开始月份", default=6, null=True, blank=True)
    summer_end = models.SmallIntegerField(verbose_name="夏季结束", default=8, null=True, blank=True)
    nosummer_start = models.SmallIntegerField(verbose_name="非夏季开始月份", default=9, null=True, blank=True)
    nosummer_end = models.SmallIntegerField(verbose_name="非夏季结束月份", default=5, null=True, blank=True)
    summer = models.CharField(verbose_name="夏季24小时充放电量配置", max_length=512, null=True, blank=True)
    none_summer = models.CharField(verbose_name="非夏季24小时充放电量配置", max_length=512, null=True, blank=True)
    follow = models.SmallIntegerField(verbose_name="负荷跟随", choices=((1, "跟随"), (0, "不跟随")), null=True, blank=True)
    rl_list = models.CharField(verbose_name="阈值", max_length=5120, null=True, blank=True)
    delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), null=True, blank=True)
    pv0 = models.SmallIntegerField(
        verbose_name="0时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv1 = models.SmallIntegerField(
        verbose_name="1时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv2 = models.SmallIntegerField(
        verbose_name="2时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv3 = models.SmallIntegerField(
        verbose_name="3时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv7 = models.SmallIntegerField(
        verbose_name="4时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv5 = models.SmallIntegerField(
        verbose_name="5时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv6 = models.SmallIntegerField(
        verbose_name="6时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv4 = models.SmallIntegerField(
        verbose_name="7时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv8 = models.SmallIntegerField(
        verbose_name="8时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv9 = models.SmallIntegerField(
        verbose_name="9时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv10 = models.SmallIntegerField(
        verbose_name="10时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv11 = models.SmallIntegerField(
        verbose_name="11时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv12 = models.SmallIntegerField(
        verbose_name="12时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv13 = models.SmallIntegerField(
        verbose_name="13时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv14 = models.SmallIntegerField(
        verbose_name="14时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv15 = models.SmallIntegerField(
        verbose_name="15时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv16 = models.SmallIntegerField(
        verbose_name="16时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv17 = models.SmallIntegerField(
        verbose_name="17时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv18 = models.SmallIntegerField(
        verbose_name="18时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv19 = models.SmallIntegerField(
        verbose_name="19时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv20 = models.SmallIntegerField(
        verbose_name="20时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv21 = models.SmallIntegerField(
        verbose_name="21时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv22 = models.SmallIntegerField(
        verbose_name="22时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv23 = models.SmallIntegerField(
        verbose_name="23时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv0_no = models.SmallIntegerField(
        verbose_name="0时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv1_no = models.SmallIntegerField(
        verbose_name="非夏季1时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv2_no = models.SmallIntegerField(
        verbose_name="非夏季2时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv3_no = models.SmallIntegerField(
        verbose_name="非夏季3时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv7_no = models.SmallIntegerField(
        verbose_name="非夏季4时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv5_no = models.SmallIntegerField(
        verbose_name="非夏季5时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv6_no = models.SmallIntegerField(
        verbose_name="非夏季6时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv4_no = models.SmallIntegerField(
        verbose_name="非夏季7时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv8_no = models.SmallIntegerField(
        verbose_name="非夏季8时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv9_no = models.SmallIntegerField(
        verbose_name="非夏季9时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv10_no = models.SmallIntegerField(
        verbose_name="非夏季10时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv11_no = models.SmallIntegerField(
        verbose_name="非夏季11时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv12_no = models.SmallIntegerField(
        verbose_name="非夏季12时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv13_no = models.SmallIntegerField(
        verbose_name="非夏季13时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv14_no = models.SmallIntegerField(
        verbose_name="非夏季14时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv15_no = models.SmallIntegerField(
        verbose_name="非夏季15时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv16_no = models.SmallIntegerField(
        verbose_name="非夏季16时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv17_no = models.SmallIntegerField(
        verbose_name="非夏季17时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv18_no = models.SmallIntegerField(
        verbose_name="非夏季18时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv19_no = models.SmallIntegerField(
        verbose_name="非夏季19时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv20_no = models.SmallIntegerField(
        verbose_name="非夏季20时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv21_no = models.SmallIntegerField(
        verbose_name="非夏季21时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv22_no = models.SmallIntegerField(
        verbose_name="非夏季22时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )
    pv23_no = models.SmallIntegerField(
        verbose_name="非夏季23时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "t_user_automation"


class UnitPrice(BaseModel):
    """峰平谷价格"""

    station = models.ForeignKey(
        to="MaterStation",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    project = models.ForeignKey(
        to="Project",
        verbose_name="项目",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    uid = models.UUIDField(verbose_name="标识", blank=True, null=True)
    name = models.CharField(verbose_name="定制化名称", max_length=64, null=True, blank=True)
    en_name = models.CharField(verbose_name="定制化名称", max_length=256, null=True, blank=True)
    user = models.ForeignKey(
        verbose_name="下发用户id",
        to="UserDetails",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    start = models.DateField(verbose_name="开始日期", blank=True, null=True)
    end = models.DateField(verbose_name="结束日期", blank=True, null=True)
    spike_chag_price = models.DecimalField(verbose_name="尖时充电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    peak_chag_price = models.DecimalField(verbose_name="峰时充电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    flat_chag_price = models.DecimalField(verbose_name="平时充电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    valley_chag_price = models.DecimalField(verbose_name="谷时充电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    dvalley_chag_price = models.DecimalField(verbose_name="深谷时充电电价", max_digits=10, decimal_places=5, null=True,
                                            blank=True)
    spike_disg_price = models.DecimalField(verbose_name="尖时放电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    peak_disg_price = models.DecimalField(verbose_name="峰时放电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    flat_disg_price = models.DecimalField(verbose_name="平时放电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    valley_disg_price = models.DecimalField(verbose_name="谷时放电电价", max_digits=10, decimal_places=5, null=True, blank=True)
    dvalley_disg_price = models.DecimalField(verbose_name="深谷时放电电价", max_digits=10, decimal_places=5, null=True,
                                            blank=True)
    delete = models.SmallIntegerField(verbose_name="逻辑删除", default=0, choices=((1, "删除"), (0, "未删除")))
    delete_user_id = models.SmallIntegerField(verbose_name="删除用户 id", null=True, blank=True)
    stations_name = models.CharField(verbose_name="关联所有的站英文名", blank=True, null=True, max_length=1024)
    note = models.CharField(verbose_name="备注", blank=True, null=True, max_length=512)
    en_note = models.CharField(verbose_name="备注", blank=True, null=True, max_length=512)

    class Meta:
        db_table = "t_unit_price"


class MeterUseTime(BaseModel):
    """
    结算电表使用时间配置表
    """""
    start_time = models.DateTimeField(verbose_name='起始时间')
    end_time = models.DateTimeField(verbose_name='结束时间', blank=True, null=True)
    user = models.ForeignKey(
        to=UserDetails,
        verbose_name='操作用户',
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True
    )
    station = models.ForeignKey(
        to="MaterStation",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    is_use = models.SmallIntegerField(verbose_name="是否启用", default=1, choices=((1, "启用"), (0, "未启用")))

    class Meta:
        db_table = "t_meter_use_time"


class StationMeterUseTime(BaseModel):
    """
    从站结算电表使用时间配置表
    """""
    start_time = models.DateTimeField(verbose_name='起始时间')
    end_time = models.DateTimeField(verbose_name='结束时间', blank=True, null=True)
    user = models.ForeignKey(
        to=UserDetails,
        verbose_name='操作用户',
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True
    )
    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    is_use = models.SmallIntegerField(verbose_name="是否启用", default=1, choices=((1, "启用"), (0, "未启用")))

    class Meta:
        db_table = "t_meter_use_time_station"


class FaultAlarm(BaseModel):
    """告警及故障总状态表"""

    setting_dic = alarm_detail
    joint_primary_key = models.CharField(verbose_name="联合主键（point-type-start_time-station_id-device）", max_length=128, blank=True, null=True)
    point = models.CharField(verbose_name="点名", max_length=32, blank=True, null=True)
    status = models.SmallIntegerField(verbose_name="状态", default=0, choices=((1, "已修复"), (0, "未修复")))
    type = models.SmallIntegerField(verbose_name="类型", default=0, choices=((1, "故障"), (0, "警告"), (2, "报警"), (3, "事件"), (4, "离线"), (5, "通讯异常")))
    start_time = models.DateTimeField(verbose_name="开始时间", default=timezone.now)
    end_time = models.DateTimeField(verbose_name="结束时间", blank=True, null=True)
    details = models.CharField(verbose_name="详情", max_length=128, blank=True, null=True)
    note = models.CharField(verbose_name="状态展示", max_length=128, blank=True, null=True)
    # station = models.ForeignKey(
    #     to="StationDetails",
    #     verbose_name="站",
    #     on_delete=models.DO_NOTHING,
    #     blank=True,
    #     null=True,
    # )
    station_id = models.IntegerField(verbose_name="关联的并网点ID")
    device = models.CharField(verbose_name="device", max_length=32, blank=True, null=True)
    device_another_name = models.CharField(verbose_name="设备别名", max_length=32, blank=True, null=True)
    # aggr_alarm = models.ForeignKey(to='AggrFaultAlarm', verbose_name="告警及故障总状态表--汇总表", on_delete=models.DO_NOTHING, blank=True, null=True)
    aggr_alarm_id = models.CharField(verbose_name="关联的聚合告警id", max_length=128, blank=True, null=True)

    class Meta:
        db_table = "t_fault_alarm"


class AggrFaultAlarm(BaseModel):
    """告警及故障总状态表--汇总表"""""
    joint_primary_key = models.CharField(verbose_name="联合主键（point-type-start_time-station_id-device）", max_length=128, blank=True, null=True)
    point = models.CharField(verbose_name="点名", max_length=32, blank=True, null=True)
    status = models.SmallIntegerField(verbose_name="状态", default=0, choices=((1, "已修复"), (0, "未修复")))
    type = models.SmallIntegerField(verbose_name="类型", default=0, choices=((1, "故障"), (0, "警告"), (2, "报警"), (3, "事件"), (4, "离线"), (5, "通讯异常")))
    start_time = models.DateTimeField(verbose_name="开始时间", default=timezone.now)
    end_time = models.DateTimeField(verbose_name="结束时间", blank=True, null=True)
    details = models.CharField(verbose_name="详情", max_length=128, blank=True, null=True)
    note = models.CharField(verbose_name="状态展示", max_length=128, blank=True, null=True)
    # station = models.ForeignKey(
    #     to="StationDetails",
    #     verbose_name="站",
    #     on_delete=models.DO_NOTHING,
    #     blank=True,
    #     null=True,
    # )
    station_id = models.IntegerField(verbose_name="关联的并网点ID")
    device = models.CharField(verbose_name="device", max_length=32, blank=True, null=True)
    device_another_name = models.CharField(verbose_name="设备别名", max_length=32, blank=True, null=True)
    times = models.CharField(verbose_name="时间段", max_length=512, blank=True, null=True)
    duration = models.DecimalField(verbose_name="时长", max_digits=10, decimal_places=2, null=True, blank=True)
    # feedback = models.OneToOneField(to='AggrFaultAlarmFeedback', verbose_name="反馈表", on_delete=models.DO_NOTHING, blank=True, null=True)
    feedback_id = models.BigIntegerField(verbose_name="关联的反馈id", blank=True, null=True)

    class Meta:
        db_table = "t_fault_alarm_aggr"


class AggrFaultAlarmFeedback(BaseModel):
    """
    告警及故障总状态表--汇总表--反馈表
    """""
    content = models.CharField(verbose_name="反馈内容", max_length=512, blank=True, null=True)
    en_content = models.CharField(verbose_name="反馈内容", max_length=512, blank=True, null=True)
    feedback_user = models.ForeignKey(to="user.UserDetails", verbose_name="反馈人", on_delete=models.DO_NOTHING, blank=True, null=True)
    feedback_time = models.DateTimeField(verbose_name="反馈时间", blank=True, null=True)
    is_dispatch_worker = models.SmallIntegerField(verbose_name="是否派发工单", default=0, choices=((1, "是"), (0, "否")), blank=True, null=True)
    worker_order = models.CharField(verbose_name="工单编号", max_length=32, blank=True, null=True)
    question_type = models.CharField(verbose_name="问题类型", max_length=32, blank=True, null=True)
    en_question_type = models.CharField(verbose_name="问题类型", max_length=256, blank=True, null=True)
    expected_closing_date = models.DateField(verbose_name="预计闭环日期", blank=True, null=True)
    real_closing_date = models.DateTimeField(verbose_name="实际恢复日期", blank=True, null=True)
    note = models.CharField(verbose_name="备注", max_length=128, blank=True, null=True)
    en_note = models.CharField(verbose_name="备注", max_length=256, blank=True, null=True)

    class Meta:
        db_table = "t_fault_alarm_aggr_feedback"


class MessageCenter(BaseModel):
    """
    消息中心
    """
    title = models.CharField(verbose_name="策略标题", max_length=256)
    en_title = models.CharField(verbose_name="策略标题", max_length=1024, blank=True, null=True)
    type = models.SmallIntegerField(verbose_name="消息类型：0：策略; 1：告警；2:运行分析；3：电池电压分析；4：电池温度分析, 5:电池过放风险")
    is_read = models.SmallIntegerField(verbose_name="是否已读：1：已读；0：未读", default=0, choices=((1, "已读"), (0, "未读")))
    is_verify = models.SmallIntegerField(verbose_name="确认状态：1：已确认/已反馈；0：未确认/为反馈；", default=0,
                                         choices=((1, "已确认/已反馈"), (0, "未确认/未反馈")))
    is_handle = models.SmallIntegerField(verbose_name="是否处理：1：已处理；0：未处理；2：不展示(策略确认结果消息)", default=0, choices=((1, "已处理"), (0, "未处理"), (2, "不展示")))
    files = models.TextField(verbose_name='附件url，多个以引文逗号隔开', blank=True, null=True)
    opinion = models.TextField(verbose_name='策略反馈意见', blank=True, null=True)
    en_opinion = models.TextField(verbose_name='策略反馈意见', blank=True, null=True)
    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    user = models.ForeignKey(
        to="user.UserDetails",
        verbose_name="抄送用户",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    issue_user = models.IntegerField(verbose_name="下发用户", blank=True, null=True)
    strategy = models.ForeignKey(
        to="statistics_apis.UserStrategy",
        verbose_name="用户控制策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    alarm = models.ForeignKey(to='AggrFaultAlarm', verbose_name="告警及故障总状态表--汇总表",
                                       on_delete=models.DO_NOTHING, blank=True, null=True)

    # related_id = models.IntegerField(verbose_name="通用关联id", blank=True, null=True)
    related_id = models.CharField(verbose_name="通用关联id", max_length=128, blank=True, null=True)


    class Meta:
        db_table = "t_message_center"


class CustomIncome(BaseModel):
    """收益录入"""
    project = models.CharField(verbose_name="项目名", max_length=32, null=False)
    en_project = models.CharField(verbose_name="项目名", max_length=256, null=True, blank=True)
    station = models.CharField(verbose_name="站名", max_length=32, null=False)
    en_station = models.CharField(verbose_name="站名", max_length=256, null=True, blank=True)
    income = models.DecimalField(verbose_name="收益值", max_digits=10, decimal_places=2)
    income_type = models.SmallIntegerField(verbose_name="收益类型", default=1, choices=((1, "削峰填谷收益"),
                                                                                        (2, "需求侧响应收益"), (3, "其他收益")))
    income_month = models.CharField(verbose_name="收益所属月份", max_length=10)
    meter_charge = models.DecimalField(verbose_name="计量充电量", max_digits=10, decimal_places=2)
    meter_discharge = models.DecimalField(verbose_name="计量放电量", max_digits=10, decimal_places=2)
    start_day = models.DateField(verbose_name="起始日期")
    end_day = models.DateField(verbose_name="结束日期")
    creator = models.CharField(verbose_name="录入者", max_length=32, null=True)
    en_creator = models.CharField(verbose_name="录入者", max_length=128, null=True)
    updator = models.CharField(verbose_name="更新者", max_length=32, null=True)
    en_updator = models.CharField(verbose_name="更新者", max_length=128, null=True)
    create_time = models.DateTimeField(verbose_name="录入时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="修改时间", auto_now=True)
    attar_url = models.CharField(verbose_name="附件路径", max_length=256, null=True, blank=True)
    attar_name = models.CharField(verbose_name="附件名称", max_length=256, null=True, blank=True)
    note = models.CharField(verbose_name="备注", max_length=256, null=True)
    is_delete = models.IntegerField(verbose_name="逻辑删除", default=0)

    class Meta:
        db_table = "t_custom_income"


class PointType(models.Model):
    """类型"""

    type = models.SmallIntegerField(verbose_name="类型", choices=((1, "一类"), (2, "二类"), (3, "三类")))
    device = models.CharField(verbose_name="设备名称", max_length=8)
    is_stand = models.SmallIntegerField(verbose_name="是否标准类型", choices=((0, "非标"), (1, "标准")), default=1)

    class Meta:
        db_table = "t_point_type"


class PointMeasure(models.Model):
    """测量量"""
    name = models.CharField(verbose_name="点位英文", max_length=16)
    description = models.CharField(verbose_name="数据描述", max_length=32)
    en_description = models.CharField(verbose_name="数据描述", max_length=256, null=True, blank=True)
    data_type = models.CharField(verbose_name="数据类型", max_length=8)
    unit = models.CharField(verbose_name="单位", max_length=8, null=True)
    point_type = models.ForeignKey(
        to="PointType",
        verbose_name="点表类型",
        on_delete=models.DO_NOTHING
    )
    version = models.SmallIntegerField(verbose_name='天禄并网点版本：2：2.0；3：3.0；', default=1)
    class Meta:
        db_table = "t_point_measure"


class PointStatus(models.Model):
    """状态量"""
    name = models.CharField(verbose_name="点位英文", max_length=16)
    description = models.CharField(verbose_name="数据描述", max_length=32)
    en_description = models.CharField(verbose_name="数据描述", max_length=256, null=True, blank=True)
    data_type = models.CharField(verbose_name="数据类型", max_length=8)
    a_status_name = models.CharField(verbose_name='a_status', max_length=32)
    b_status_name = models.CharField(verbose_name='b_status', max_length=32)
    point_type = models.ForeignKey(
        to="PointType",
        verbose_name="点表类型",
        on_delete=models.DO_NOTHING
    )
    version = models.SmallIntegerField(verbose_name='天禄并网点版本：2：2.0；3：3.0；', default=1)
    class Meta:
        db_table = "t_point_status"


class PointDiscrete(models.Model):
    """离散量"""
    name = models.CharField(verbose_name="点位英文", max_length=16)
    description = models.CharField(verbose_name="数据描述", max_length=32)
    en_description = models.CharField(verbose_name="数据描述", max_length=256, null=True, blank=True)
    data_type = models.CharField(verbose_name="数据类型", max_length=8)
    n_status = models.CharField(verbose_name="离散值", max_length=16)
    desc_status = models.CharField(verbose_name='离散描述', max_length=32)
    point_type = models.ForeignKey(
        to="PointType",
        verbose_name="点表类型",
        on_delete=models.DO_NOTHING
    )
    version = models.SmallIntegerField(verbose_name='天禄并网点版本：2：2.0；3：3.0；', default=1)
    class Meta:
        db_table = "t_point_discrete"


class PointCumulant(models.Model):
    """累积量"""
    name = models.CharField(verbose_name="点位英文", max_length=16)
    description = models.CharField(verbose_name="数据描述", max_length=32)
    en_description = models.CharField(verbose_name="数据描述", max_length=256, null=True, blank=True)
    data_type = models.CharField(verbose_name="数据类型", max_length=8)
    unit = models.CharField(verbose_name="单位", max_length=8)
    point_type = models.ForeignKey(
        to="PointType",
        verbose_name="点表类型",
        on_delete=models.DO_NOTHING
    )
    version = models.SmallIntegerField(verbose_name='天禄并网点版本：2：2.0；3：3.0；', default=1)
    class Meta:
        db_table = "t_point_cumulant"


class FReport(BaseModel):
    """冻结电量表"""

    day = models.CharField(verbose_name="日期", max_length=32)
    hour = models.CharField(verbose_name="小时数", max_length=10)
    # composite_key = CompositeUniqueKey(['day', 'hour'])
    chag = models.CharField(verbose_name="充电量",  max_length=32)
    disg = models.CharField(verbose_name="放电量", max_length=32)
    soc = models.CharField(verbose_name="SOC变化值", max_length=32)
    station = models.CharField(verbose_name="站名", max_length=10)
    app = models.CharField(verbose_name="app名称", max_length=10)
    station_type = models.CharField(verbose_name="类别，1并网点，2单元", max_length=10, default=1)
    unit_name = models.CharField(verbose_name="单元名称", max_length=10, default=0)
    create_time = models.DateTimeField(verbose_name="录入时间", auto_now_add=True)
    soc_2 = models.CharField(verbose_name="SOC变化绝对值", max_length=32)

    class Meta:
        db_table = "f_report"
        unique_together = ('day', 'hour', 'station','unit_name')

class FReportJFPG(BaseModel):
    """冻结电量尖峰平谷表"""

    day = models.CharField(verbose_name="日期", max_length=32)
    jchag = models.CharField(verbose_name="尖充电量",  max_length=32)
    fchag = models.CharField(verbose_name="峰充电量",  max_length=32)
    pchag = models.CharField(verbose_name="平充电量",  max_length=32)
    gchag = models.CharField(verbose_name="谷充电量",  max_length=32)
    sgchag = models.CharField(verbose_name="深谷充电量",  max_length=32)
    jdisg = models.CharField(verbose_name="尖放电量", max_length=32)
    fdisg = models.CharField(verbose_name="峰放电量", max_length=32)
    pdisg = models.CharField(verbose_name="平放电量", max_length=32)
    gdisg = models.CharField(verbose_name="谷放电量", max_length=32)
    sgdisg = models.CharField(verbose_name="深谷放电量", max_length=32)
    station = models.CharField(verbose_name="站名", max_length=10)
    app = models.CharField(verbose_name="app名称", max_length=10)
    station_type = models.CharField(verbose_name="类别，1并网点，2单元", max_length=10)
    unit_name = models.CharField(verbose_name="单元名称", max_length=10)
    create_time = models.DateTimeField(verbose_name="录入时间", auto_now_add=True)

    class Meta:
        db_table = "f_report_jfpg"
        unique_together = ('day','station','unit_name')


class Feedback(BaseModel):
    """小程序： 用户反馈"""""
    content = models.TextField(verbose_name="反馈内容", null=False, blank=False)
    en_content = models.TextField(verbose_name="反馈内容", null=False, blank=False)
    user = models.ForeignKey(UserDetails, models.DO_NOTHING, verbose_name="反馈人", blank=True, null=True)

    is_delete = models.IntegerField(verbose_name="逻辑删除", default=0)

    class Meta:
        db_table = "t_feedback"


class FormerActic(BaseModel):
    """默认策略-字典表"""
    year_month = models.CharField(verbose_name="年月：YYYY-mm", max_length=16, blank=True, null=True)
    h1f = models.SmallIntegerField(verbose_name="1点充放标识: -1充电0静置1放电", blank=True, null=True)
    h2f = models.SmallIntegerField(verbose_name="2点充放标识: -1充电0静置1放电", blank=True, null=True)
    h3f = models.SmallIntegerField(verbose_name="3点充放标识: -1充电0静置1放电", blank=True, null=True)
    h4f = models.SmallIntegerField(verbose_name="4点充放标识: -1充电0静置1放电", blank=True, null=True)
    h5f = models.SmallIntegerField(verbose_name="5点充放标识: -1充电0静置1放电", blank=True, null=True)
    h6f = models.SmallIntegerField(verbose_name="6点充放标识: -1充电0静置1放电", blank=True, null=True)
    h7f = models.SmallIntegerField(verbose_name="7点充放标识: -1充电0静置1放电", blank=True, null=True)
    h8f = models.SmallIntegerField(verbose_name="8点充放标识: -1充电0静置1放电", blank=True, null=True)
    h9f = models.SmallIntegerField(verbose_name="9点充放标识: -1充电0静置1放电", blank=True, null=True)
    h10f = models.SmallIntegerField(verbose_name="10点充放标识: -1充电0静置1放电", blank=True, null=True)
    h11f = models.SmallIntegerField(verbose_name="11点充放标识: -1充电0静置1放电", blank=True, null=True)
    h12f = models.SmallIntegerField(verbose_name="12点充放标识: -1充电0静置1放电", blank=True, null=True)
    h13f = models.SmallIntegerField(verbose_name="13点充放标识: -1充电0静置1放电", blank=True, null=True)
    h14f = models.SmallIntegerField(verbose_name="14点充放标识: -1充电0静置1放电", blank=True, null=True)
    h15f = models.SmallIntegerField(verbose_name="15点充放标识: -1充电0静置1放电", blank=True, null=True)
    h16f = models.SmallIntegerField(verbose_name="16点充放标识: -1充电0静置1放电", blank=True, null=True)
    h17f = models.SmallIntegerField(verbose_name="17点充放标识: -1充电0静置1放电", blank=True, null=True)
    h18f = models.SmallIntegerField(verbose_name="18点充放标识: -1充电0静置1放电", blank=True, null=True)
    h19f = models.SmallIntegerField(verbose_name="19点充放标识: -1充电0静置1放电", blank=True, null=True)
    h20f = models.SmallIntegerField(verbose_name="20点充放标识: -1充电0静置1放电", blank=True, null=True)
    h21f = models.SmallIntegerField(verbose_name="21点充放标识: -1充电0静置1放电", blank=True, null=True)
    h22f = models.SmallIntegerField(verbose_name="22点充放标识: -1充电0静置1放电", blank=True, null=True)
    h23f = models.SmallIntegerField(verbose_name="23点充放标识: -1充电0静置1放电", blank=True, null=True)
    h24f = models.SmallIntegerField(verbose_name="24点充放标识: -1充电0静置1放电", blank=True, null=True)
    h1p = models.FloatField(verbose_name="1点功率值，正数", blank=True, null=True)
    h2p = models.FloatField(verbose_name="2点功率值，正数", blank=True, null=True)
    h3p = models.FloatField(verbose_name="3点功率值，正数", blank=True, null=True)
    h4p = models.FloatField(verbose_name="4点功率值，正数", blank=True, null=True)
    h5p = models.FloatField(verbose_name="5点功率值，正数", blank=True, null=True)
    h6p = models.FloatField(verbose_name="6点功率值，正数", blank=True, null=True)
    h7p = models.FloatField(verbose_name="7点功率值，正数", blank=True, null=True)
    h8p = models.FloatField(verbose_name="8点功率值，正数", blank=True, null=True)
    h9p = models.FloatField(verbose_name="9点功率值，正数", blank=True, null=True)
    h10p = models.FloatField(verbose_name="10点功率值，正数", blank=True, null=True)
    h11p = models.FloatField(verbose_name="11点功率值，正数", blank=True, null=True)
    h12p = models.FloatField(verbose_name="12点功率值，正数", blank=True, null=True)
    h13p = models.FloatField(verbose_name="13点功率值，正数", blank=True, null=True)
    h14p = models.FloatField(verbose_name="14点功率值，正数", blank=True, null=True)
    h15p = models.FloatField(verbose_name="15点功率值，正数", blank=True, null=True)
    h16p = models.FloatField(verbose_name="16点功率值，正数", blank=True, null=True)
    h17p = models.FloatField(verbose_name="17点功率值，正数", blank=True, null=True)
    h18p = models.FloatField(verbose_name="18点功率值，正数", blank=True, null=True)
    h19p = models.FloatField(verbose_name="19点功率值，正数", blank=True, null=True)
    h20p = models.FloatField(verbose_name="20点功率值，正数", blank=True, null=True)
    h21p = models.FloatField(verbose_name="21点功率值，正数", blank=True, null=True)
    h22p = models.FloatField(verbose_name="22点功率值，正数", blank=True, null=True)
    h23p = models.FloatField(verbose_name="23点功率值，正数", blank=True, null=True)
    h24p = models.FloatField(verbose_name="24点功率值，正数", blank=True, null=True)
    power = models.FloatField(verbose_name="额定功率", blank=True, null=True)

    class Meta:
        db_table = "t_former_actic"


class StationActic(BaseModel):
    """默认策略&站表"""
    station_id = models.IntegerField(verbose_name="站ID", blank=True, null=True)
    former_actic_id = models.IntegerField(verbose_name="默认策略-字典表ID", blank=True, null=True)

    class Meta:
        db_table = "t_station_actic"


class StationBaseIncome(BaseModel):
    """放基准充放电量和收益表"""
    station_name = models.CharField(verbose_name="站英文名称", max_length=16)
    day = models.CharField(verbose_name="时间；YYYY-mm-dd", max_length=16)
    day_income = models.FloatField(verbose_name="日收益/元")
    day_chag = models.FloatField(verbose_name="日充电量/kWh")
    day_disg = models.FloatField(verbose_name="日放电量/kWh")

    class Meta:
        db_table = 't_station_base_income'


class StationStatus(BaseModel):
    """站状态表（数据来自定时任务）"""

    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    status = models.SmallIntegerField(
        verbose_name="状态",
        choices=((1, "正常"), (2, "告警"), (3, "故障"), (4, "离线"), (5, '通讯异常')),
        default=4,
    )

    class Meta:
        db_table = "t_station_status"


class FReportDay(BaseModel):
    """冻结电量表-天"""
    day = models.CharField(verbose_name="日期", max_length=32)
    station = models.CharField(verbose_name="站名", max_length=32)
    app = models.CharField(verbose_name="APP名称", max_length=32)
    chag = models.FloatField(verbose_name="日充电量")
    disg = models.FloatField(verbose_name="日放电量")
    chag_soc = models.FloatField(verbose_name="日放电量soc变化值")
    disg_soc = models.FloatField(verbose_name="日放电量soc变化值")
    station_type = models.SmallIntegerField(verbose_name="类别，1并网点，2单元")
    unit_name = models.CharField(verbose_name="单元名称", max_length=10, default=0)

    class Meta:
        db_table = "f_report_day"

class FormerBase(BaseModel):
    """
    理论充放字典表
    """
    year_month = models.CharField(verbose_name="月份", max_length=16, blank=True, null=True)
    h1f = models.SmallIntegerField(verbose_name="1点充放标识: -1充电0静置1放电", blank=True, null=True)
    h2f = models.SmallIntegerField(verbose_name="2点充放标识: -1充电0静置1放电", blank=True, null=True)
    h3f = models.SmallIntegerField(verbose_name="3点充放标识: -1充电0静置1放电", blank=True, null=True)
    h4f = models.SmallIntegerField(verbose_name="4点充放标识: -1充电0静置1放电", blank=True, null=True)
    h5f = models.SmallIntegerField(verbose_name="5点充放标识: -1充电0静置1放电", blank=True, null=True)
    h6f = models.SmallIntegerField(verbose_name="6点充放标识: -1充电0静置1放电", blank=True, null=True)
    h7f = models.SmallIntegerField(verbose_name="7点充放标识: -1充电0静置1放电", blank=True, null=True)
    h8f = models.SmallIntegerField(verbose_name="8点充放标识: -1充电0静置1放电", blank=True, null=True)
    h9f = models.SmallIntegerField(verbose_name="9点充放标识: -1充电0静置1放电", blank=True, null=True)
    h10f = models.SmallIntegerField(verbose_name="10点充放标识: -1充电0静置1放电", blank=True, null=True)
    h11f = models.SmallIntegerField(verbose_name="11点充放标识: -1充电0静置1放电", blank=True, null=True)
    h12f = models.SmallIntegerField(verbose_name="12点充放标识: -1充电0静置1放电", blank=True, null=True)
    h13f = models.SmallIntegerField(verbose_name="13点充放标识: -1充电0静置1放电", blank=True, null=True)
    h14f = models.SmallIntegerField(verbose_name="14点充放标识: -1充电0静置1放电", blank=True, null=True)
    h15f = models.SmallIntegerField(verbose_name="15点充放标识: -1充电0静置1放电", blank=True, null=True)
    h16f = models.SmallIntegerField(verbose_name="16点充放标识: -1充电0静置1放电", blank=True, null=True)
    h17f = models.SmallIntegerField(verbose_name="17点充放标识: -1充电0静置1放电", blank=True, null=True)
    h18f = models.SmallIntegerField(verbose_name="18点充放标识: -1充电0静置1放电", blank=True, null=True)
    h19f = models.SmallIntegerField(verbose_name="19点充放标识: -1充电0静置1放电", blank=True, null=True)
    h20f = models.SmallIntegerField(verbose_name="20点充放标识: -1充电0静置1放电", blank=True, null=True)
    h21f = models.SmallIntegerField(verbose_name="21点充放标识: -1充电0静置1放电", blank=True, null=True)
    h22f = models.SmallIntegerField(verbose_name="22点充放标识: -1充电0静置1放电", blank=True, null=True)
    h23f = models.SmallIntegerField(verbose_name="23点充放标识: -1充电0静置1放电", blank=True, null=True)
    h24f = models.SmallIntegerField(verbose_name="24点充放标识: -1充电0静置1放电", blank=True, null=True)
    h1p = models.FloatField(verbose_name="1点功率值，正数", blank=True, null=True)
    h2p = models.FloatField(verbose_name="2点功率值，正数", blank=True, null=True)
    h3p = models.FloatField(verbose_name="3点功率值，正数", blank=True, null=True)
    h4p = models.FloatField(verbose_name="4点功率值，正数", blank=True, null=True)
    h5p = models.FloatField(verbose_name="5点功率值，正数", blank=True, null=True)
    h6p = models.FloatField(verbose_name="6点功率值，正数", blank=True, null=True)
    h7p = models.FloatField(verbose_name="7点功率值，正数", blank=True, null=True)
    h8p = models.FloatField(verbose_name="8点功率值，正数", blank=True, null=True)
    h9p = models.FloatField(verbose_name="9点功率值，正数", blank=True, null=True)
    h10p = models.FloatField(verbose_name="10点功率值，正数", blank=True, null=True)
    h11p = models.FloatField(verbose_name="11点功率值，正数", blank=True, null=True)
    h12p = models.FloatField(verbose_name="12点功率值，正数", blank=True, null=True)
    h13p = models.FloatField(verbose_name="13点功率值，正数", blank=True, null=True)
    h14p = models.FloatField(verbose_name="14点功率值，正数", blank=True, null=True)
    h15p = models.FloatField(verbose_name="15点功率值，正数", blank=True, null=True)
    h16p = models.FloatField(verbose_name="16点功率值，正数", blank=True, null=True)
    h17p = models.FloatField(verbose_name="17点功率值，正数", blank=True, null=True)
    h18p = models.FloatField(verbose_name="18点功率值，正数", blank=True, null=True)
    h19p = models.FloatField(verbose_name="19点功率值，正数", blank=True, null=True)
    h20p = models.FloatField(verbose_name="20点功率值，正数", blank=True, null=True)
    h21p = models.FloatField(verbose_name="21点功率值，正数", blank=True, null=True)
    h22p = models.FloatField(verbose_name="22点功率值，正数", blank=True, null=True)
    h23p = models.FloatField(verbose_name="23点功率值，正数", blank=True, null=True)
    h24p = models.FloatField(verbose_name="24点功率值，正数", blank=True, null=True)
    power = models.FloatField(verbose_name="额定功率", blank=True, null=True)

    class Meta:
        db_table = "t_former_base"


class StationBase(BaseModel):
    station_id = models.SmallIntegerField(verbose_name='站ID')
    former_base_id = models.SmallIntegerField(verbose_name='理论充放字典ID')

    class Meta:
        db_table = "t_station_base"


class PeakValleyIncome(BaseModel):
    """尖峰平谷充放电量、收益表"""
    day = models.DateField(verbose_name="日期", blank=True, null=True)
    pointed_chag = models.FloatField(verbose_name="充电量-尖", blank=True, null=True)
    peak_chag = models.FloatField(verbose_name="充电量-峰", blank=True, null=True)
    flat_chag = models.FloatField(verbose_name="充电量-平", blank=True, null=True)
    valley_chag = models.FloatField(verbose_name="充电量-谷", blank=True, null=True)
    dvalley_chag = models.FloatField(verbose_name="充电量-深谷", blank=True, null=True)
    pointed_disg = models.FloatField(verbose_name="放电量-尖", blank=True, null=True)
    peak_disg = models.FloatField(verbose_name="放电量-峰", blank=True, null=True)
    flat_disg = models.FloatField(verbose_name="放电量-平", blank=True, null=True)
    valley_disg = models.FloatField(verbose_name="放电量-谷", blank=True, null=True)
    dvalley_disg = models.FloatField(verbose_name="放电量-深谷", blank=True, null=True)
    pointed_chag_income = models.FloatField(verbose_name="充电成本-尖", blank=True, null=True)
    peak_chag_income = models.FloatField(verbose_name="充电成本-峰", blank=True, null=True)
    flat_chag_income = models.FloatField(verbose_name="充电成本-平", blank=True, null=True)
    valley_chag_income = models.FloatField(verbose_name="充电成本-谷", blank=True, null=True)
    dvalley_chag_income = models.FloatField(verbose_name="充电成本-深谷", blank=True, null=True)
    pointed_disg_income = models.FloatField(verbose_name="放电收益-尖", blank=True, null=True)
    peak_disg_income = models.FloatField(verbose_name="放电收益-峰", blank=True, null=True)
    flat_disg_income = models.FloatField(verbose_name="放电收益-平", blank=True, null=True)
    valley_disg_income = models.FloatField(verbose_name="放电收益-谷", blank=True, null=True)
    dvalley_disg_income = models.FloatField(verbose_name="放电收益-深谷", blank=True, null=True)
    station = models.ForeignKey(
        to="StationDetails",
        verbose_name="从站",
        on_delete=models.CASCADE,
        blank=True,
        null=True,)
    master_station = models.ForeignKey(
        to="MaterStation",
        verbose_name="主站",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    update_time = models.DateTimeField(verbose_name="修改时间", auto_now=True)
    is_account = models.BooleanField(verbose_name="是否外接结算电表", default=0)

    class Meta:
        db_table = "t_peakvalley_income"


class PeakValleyNew(BaseModel):
    """峰谷电价表-new"""
    year_month = models.CharField(verbose_name="年月：YYYY-mm", max_length=16, blank=True, null=True)
    day = models.SmallIntegerField(verbose_name="日：默认1号", blank=True, null=True)
    moment = models.CharField(verbose_name="时分；间隔15分钟", max_length=16, blank=True, null=True)
    price = models.DecimalField(verbose_name="小时电价", max_digits=10, decimal_places=6, default=0)
    pv = models.SmallIntegerField(
        verbose_name="小时峰谷标识",
        choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
        default=0)
    TYPE_CHOICE = (
        (1, "大工业"),
        (2, "一般工商业"),
        (3, "一般工商业（单一制）"),
        (4, "大工业（两部制）"),
        (5, "单一制"),
        (6, "两部制"),
        (7, "一般工商业（两部制）"),
        (8, "两部制(大工业100kVA以上)"),
        (9, "两部制(非大工业100kVA以上)"),
        (10, "单一制(100kVA以下)"),
        (11, "大量工商业及其他用电(250kWh及以下/千伏安•月)"),
        (12, "大量工商业及其他用电(250kWh及以上/千伏安•月)"),
        (13, "高需求工商业及其他用电(400kWh及以下/千伏安•月)"),
        (14, "高需求工商业及其他用电（400kWh及以上/千伏安•月)"),
        (15, "充换电站工商业用户"),
        (16, "电动汽车")

    )

    EN_TYPE_CHOICE = (
        (1, "Large-scale Industry"),
        (2, "General Industrial and Commercial"),
        (3, "General Industrial and Commercial (unitary system)"),
        (4, "Large Industry (two-part system)"),
        (5, "Single-part Tariff"),
        (6, "Two-part Tariff"),
        (7, "General Industrial and commercial (two-part system)"),
        (8, "Two-part system (large industry 100 k VA or more)"),
        (9, "Two-part system (non-large industrial 100 k VA and above)"),
        (10, "Single system (up to 100 k VA)"),
        (11, "High-volume Industrial and Commercial and Other Electricity Use (250kWh and below per kVA per month)"),
        (12, "High-volume Industrial and Commercial and Other Electricity Use (250kWh and above per kVA per month)"),
        (13, "High-demand Industrial and Commercial and Other Electricity Use (400kWh and below per kVA per month)"),
        (14, "High-demand Industrial and Commercial and Other Electricity Use (400kWh and above per kVA per month)"),
        (15, "Industrial and commercial users of charging and replacing power stations"),
        (16, "electric_vehicle")
    )

    type = models.SmallIntegerField(verbose_name="用电类型", choices=TYPE_CHOICE, blank=True, null=True)
    province = models.ForeignKey(
        to="ElectricityProvince",
        verbose_name="省份",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    LEVEL_CHOICE = (
        (1, "不满1千伏"),
        (2, "1-10千伏"),
        (3, "35千伏"),
        (4, "110千伏"),
        (5, "220千伏及以上"),
        (6, "35-110千伏（不含）"),
        (7, "110-220千伏（不含）"),
        (8, "35千伏及以上"),
        (9, "35-110千伏以下"),
        (10, "110-220千伏以下"),
        (11, "110 (66)千伏"),
        (12, "20千伏"),
        (13, "66 千伏"),
        (14, "220 千伏"),
        (15, "110千伏及以上"),
        (16, "20-35 千伏以下"),
        (17, "110-330 千伏"),
        (18, "330千伏及以上"),
        (19, "35-110千伏"),
        (20, "10 (20)千伏"),
        (21, "35 千伏以下"),
        (22, "10千伏"),
        (23, "1-10（20）千伏"),
        (24, "220 (330)千伏"),
        (25, "100千伏安及以下和公变接入用电"),
        (26, "10千伏高供低计（380V/220V计量"),
        (27, "10千伏高供低计"),
    )

    EN_LEVEL_CHOICE = (
        (1, "less than 1 KV"),
        (2, "1-10 kV"),
        (3, "35 kV"),
        (4, "110 kV"),
        (5, "220 kV and above"),
        (6, "35-110 kV (not included)"),
        (7, "110-220 kV (not included)"),
        (8, "35 kV and above"),
        (9, "35-110 kV"),
        (10, "110-220 kV"),
        (11, "110 (66) kV"),
        (12, "20 kV"),
        (13, "66 kV"),
        (14, "220 kV"),
        (15, "110 kV and above"),
        (16, "20-35 kV"),
        (17, "110-330 kV"),
        (18, "330 kV and above"),
        (19, "35-110 kV"),
        (20, "10 (20) kV"),
        (21, "Below 35 kV"),
        (22, "10 kV"),
        (23, "1-10 (20) kV"),
        (24, "220 (330) kV"),
        (25, "100 kVA and below and public transformer access electricity"),
        (26, "10 kV high supply low meter (380V/220V) metering"),
        (27, "10 kV high supply low meter"),
    )

    level = models.SmallIntegerField(verbose_name="电压等级", choices=LEVEL_CHOICE, blank=True, null=True)
    class Meta:
        db_table = "t_price_new"


class FormerActicNew(BaseModel):
    """默认策略-字典表-new"""

    year_month = models.CharField(verbose_name="年月：YYYY-mm", max_length=16, blank=True, null=True)
    day = models.SmallIntegerField(verbose_name="日：默认1号", blank=True, null=True)
    moment = models.CharField(verbose_name="时分；间隔15分钟", max_length=16, blank=True, null=True)
    mark = models.SmallIntegerField(verbose_name="小时充放标识: -1充电0静置1放电", blank=True, null=True)
    power_value = models.FloatField(verbose_name="小时功率值，正数", blank=True, null=True)
    power = models.FloatField(verbose_name="额定功率", blank=True, null=True)
    station = models.ForeignKey(to="StationDetails", verbose_name="站名", blank=True, on_delete=models.DO_NOTHING)
    class Meta:
        db_table = "t_former_actic_new"


class StationActicNew(BaseModel):
    """默认策略&站表-new"""
    station_id = models.IntegerField(verbose_name="站ID", blank=True, null=True)
    former_actic_id = models.IntegerField(verbose_name="默认策略new-字典表ID", blank=True, null=True)

    class Meta:
        db_table = "t_station_actic_new"


class FormerActicNewInfo(BaseModel):
    """默认策略基础信息表"""
    name = models.CharField(verbose_name="策略名称", max_length=128, blank=True, null=True)
    en_name = models.TextField(verbose_name="策略名称-英文",  blank=False, null=False)
    station_id = models.CharField(verbose_name="并网点ID，多个用英文逗号隔开", max_length=512, blank=True, null=True)
    is_use = models.SmallIntegerField(verbose_name="是否启用", default=1, choices=((1, "启用"), (0, "未启用")))
    stype = models.SmallIntegerField(verbose_name="策略类型", default=1, choices=((2, "一小时"), (1, "半小时")))
    province = models.ForeignKey(to="ElectricityProvince", verbose_name="省份", blank=True, on_delete=models.DO_NOTHING)
    ele_type = models.IntegerField(verbose_name="用电类型", blank=True, null=True)
    year = models.IntegerField(verbose_name="年份", blank=True, null=True)
    file_url = models.CharField(verbose_name="url", max_length=1024, blank=True, null=True)

    class Meta:
        db_table = "t_former_actic_new_info"

class FormerBaseNewInfo(BaseModel):
    """基准策略基础信息表"""
    name = models.CharField(verbose_name="策略名称", max_length=128, blank=True, null=True)
    en_name = models.TextField(verbose_name="策略名称-英文",  blank=False, null=False)
    station = models.ForeignKey(to="StationDetails", verbose_name="站名", blank=True, on_delete=models.DO_NOTHING)
    is_use = models.SmallIntegerField(verbose_name="是否启用", default=1, choices=((1, "启用"), (0, "未启用")))
    stype = models.SmallIntegerField(verbose_name="策略类型", default=1, choices=((2, "一小时"), (1, "半小时")))
    year = models.IntegerField(verbose_name="年份", blank=True, null=True)
    file_url = models.CharField(verbose_name="url", max_length=1024, blank=True, null=True)

    class Meta:
        db_table = "t_former_base_new_info"


class PriceNewInfo(BaseModel):
    """电价基础信息表"""
    is_use = models.SmallIntegerField(verbose_name="是否启用", default=1, choices=((1, "启用"), (0, "未启用")))
    stype = models.SmallIntegerField(verbose_name="策略类型", default=1, choices=((2, "一小时"), (1, "半小时")))
    province = models.ForeignKey(to="ElectricityProvince", verbose_name="省份", blank=True, on_delete=models.DO_NOTHING)
    ele_type = models.IntegerField(verbose_name="用电类型", blank=True, null=True)
    level = models.IntegerField(verbose_name="用电等级", blank=True, null=True)
    year_month = models.CharField(verbose_name="年份", max_length=64, blank=True, null=True)
    file_url = models.CharField(verbose_name="url", max_length=1024, blank=True, null=True)

    class Meta:
        db_table = "t_price_new_info"


class FormerBaseNew(BaseModel):
    """
    理论充放字典表-new
    """
    year_month = models.CharField(verbose_name="年月：YYYY-mm", max_length=16, blank=True, null=True)
    day = models.SmallIntegerField(verbose_name="日：默认1号", blank=True, null=True)
    moment = models.CharField(verbose_name="时分；间隔15分钟", max_length=16, blank=True, null=True)
    mark = models.SmallIntegerField(verbose_name="小时充放标识: -1充电0静置1放电", blank=True, null=True)
    power_value = models.FloatField(verbose_name="小时功率值，正数", blank=True, null=True)
    power = models.FloatField(verbose_name="额定功率", blank=True, null=True)
    station_id = models.IntegerField(verbose_name="并网点ID", blank=True, null=True)

    class Meta:
        db_table = "t_former_base_new"


class StationBaseNew(BaseModel):
    """理论充放字典表&站表-new"""
    station_id = models.SmallIntegerField(verbose_name='站ID')
    former_base_id = models.SmallIntegerField(verbose_name='理论充放字典ID')

    class Meta:
        db_table = "t_station_base_new"


class RunningAnalysis(BaseModel):
    """
    运行分析--运行分析表
    """""
    station = models.ForeignKey(to="MaterStation", verbose_name="站", on_delete=models.DO_NOTHING, blank=True, null=True)
    topic = models.CharField(verbose_name="分析名称", max_length=64, blank=True, null=True)
    en_topic = models.CharField(verbose_name="分析名称", max_length=128, blank=True, null=True)
    status = models.SmallIntegerField(verbose_name="状态", choices=((0, "未恢复"), (1, "已恢复")), default=0)
    start_time = models.DateTimeField(verbose_name="开始时间", blank=True, null=True)
    end_time = models.DateTimeField(verbose_name="结束时间", blank=True, null=True)
    is_delete = models.SmallIntegerField(verbose_name="逻辑删除", choices=((1, "删除"), (0, "未删除")), default=0)
    keyword = models.CharField(verbose_name="关键参数", max_length=128, blank=True, null=True)
    en_keyword = models.CharField(verbose_name="关键参数", max_length=256, blank=True, null=True)
    ref_threshold = models.CharField(verbose_name="参考阈值", max_length=128, blank=True, null=True)
    en_ref_threshold = models.CharField(verbose_name="参考阈值", max_length=256, blank=True, null=True)
    feedback = models.OneToOneField(to='RunningAnalysisFeedback', verbose_name="反馈表", on_delete=models.DO_NOTHING,
                                    blank=True, null=True)
    note = models.CharField(verbose_name="备注", max_length=128, blank=True, null=True)

    class Meta:
        db_table = "t_running_analysis"


class RunningAnalysisFeedback(BaseModel):
    """
    运行分析--反馈表
    """""
    content = models.CharField(verbose_name="反馈内容", max_length=512, blank=True, null=True)
    en_content = models.CharField(verbose_name="反馈内容", max_length=1024, blank=True, null=True)
    feedback_user = models.ForeignKey(to="user.UserDetails", verbose_name="反馈人", on_delete=models.DO_NOTHING, blank=True, null=True)
    feedback_time = models.DateTimeField(verbose_name="反馈时间", blank=True, null=True)
    is_dispatch_worker = models.SmallIntegerField(verbose_name="是否派发工单", default=0, choices=((1, "是"), (0, "否")))
    worker_order = models.CharField(verbose_name="工单编号", max_length=32, blank=True, null=True)
    question_type = models.CharField(verbose_name="问题类型", max_length=32, blank=True, null=True)
    en_question_type = models.CharField(verbose_name="问题类型", max_length=128, blank=True, null=True)
    expected_closing_date = models.DateField(verbose_name="预计闭环日期", blank=True, null=True)
    real_closing_date = models.DateField(verbose_name="实际恢复日期", blank=True, null=True)
    note = models.CharField(verbose_name="备注", max_length=128, blank=True, null=True)
    en_note = models.CharField(verbose_name="备注", max_length=256, blank=True, null=True)

    class Meta:
        db_table = "t_running_analysis_feedback"


class BatteryAnalysisRecord(BaseModel):
    """
    电池电压分析--评估记录表
    """""
    related_id = models.CharField(verbose_name="关联ID", max_length=16)
    start_time = models.CharField(verbose_name="开始时间", max_length=16, blank=True, null=True)
    end_time = models.CharField(verbose_name="结束时间", max_length=16, blank=True, null=True)
    record_user_id = models.IntegerField(verbose_name="分析更新人ID")
    station_name = models.CharField(verbose_name="站英文名称", max_length=16)
    unit_name = models.CharField(verbose_name="单元名称", max_length=16)
    date_time = models.DateField(verbose_name="数据时间")
    before_s_time = models.CharField(verbose_name="维护前最新的充电开始时间", max_length=16, blank=True, null=True)
    before_e_time = models.CharField(verbose_name="维护前最新的充电结束时间", max_length=16, blank=True, null=True)
    before_chag = models.CharField(verbose_name="维护前最新的充电电量", max_length=16, blank=True, null=True)
    after_s_time = models.CharField(verbose_name="维护后最新的充电开始时间", max_length=16, blank=True, null=True)
    after_e_time = models.CharField(verbose_name="维护后最新的充电结束时间", max_length=16, blank=True, null=True)
    after_chag = models.CharField(verbose_name="维护后最近的充电电量", max_length=16, blank=True, null=True)
    effe_rate = models.CharField(verbose_name="提升效率", max_length=16, blank=True, null=True)
    note = models.CharField(verbose_name="备注", max_length=128, blank=True, null=True)
    en_note = models.CharField(verbose_name="备注", max_length=128, blank=True, null=True)

    class Meta:
        db_table = "t_battery_analysis_record"



class CompantInfo(models.Model):
    """
    代理主体信息表
    """
    id = models.BigAutoField(primary_key=True, verbose_name="ID")
    compant_name = models.CharField(max_length=32, verbose_name="代理主体企业名称", null=False)
    en_compant_name = models.CharField(max_length=256, verbose_name="代理主体企业名称", null=True, blank=True)
    compant_code = models.CharField(max_length=32, verbose_name="代理主体户号", null=False)
    project_name = models.CharField(max_length=32, verbose_name="项目名称", null=True, blank=True)
    project_en_name = models.CharField(max_length=32, verbose_name="项目英文名称", null=True, blank=True)
    type = models.SmallIntegerField(verbose_name="类型", null=False)
    sn = models.CharField(max_length=32, verbose_name="代理主体统一信用代码", null=False)

    class Meta:
        db_table = "t_compant_info"
        verbose_name = "代理主体信息表"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.company_name


class TModelConfig(BaseModel):
    """
    电池温度分析--模型配置信息表
    """""
    name = models.CharField(verbose_name="模型指标名称", max_length=32)
    descr = models.CharField(verbose_name="模型描述", max_length=64, blank=True, null=True)
    limit_value = models.DecimalField(verbose_name="阈值", decimal_places=2, max_digits=10, blank=True, null=True)
    level = models.SmallIntegerField(verbose_name="等级: 1紧急; 2重要; 3一般", blank=True, null=True)
    cont_time = models.DateTimeField(verbose_name="告警持续时间", blank=True, null=True)

    class Meta:
        db_table = "t_model_config"


class TModelUnitRelation(models.Model):
    """
    电池温度分析--模型和设备关联表
    """""
    unit_id = models.IntegerField(verbose_name="单元id，t_unit表主键")
    model_id = models.IntegerField(verbose_name="模型配置信息id")

    class Meta:
        db_table = "t_model_unit_relation"


class TmsCodeName(BaseModel):
    code = models.CharField(max_length=255, null=True, blank=True, verbose_name='编码')
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name='编码对应名称')
    TYPE_CHOICES = (
        (2, '天禄2.0'),
        (3, '天禄3.0'),
    )
    type = models.IntegerField(choices=TYPE_CHOICES, null=True, blank=True, verbose_name='类型')

    class Meta:
        db_table = 't_tms_code_name'
        # indexes = [
        #     models.Index(fields=['id'], name='idx_tms_code_name_id'),
        # ]


class ForecasePrice(models.Model):
    u'用户侧预算电价'

    year_month = models.CharField(max_length=32, null=False, verbose_name=u"年月，YYYY-mm")
    h0 = models.CharField(max_length=32, null=False, verbose_name=u"0时")
    h1 = models.CharField(max_length=32, null=False, verbose_name=u"1时")
    h2 = models.CharField(max_length=32, null=False, verbose_name=u"2时")
    h3 = models.CharField(max_length=32, null=False, verbose_name=u"3时")
    h4 = models.CharField(max_length=32, null=False, verbose_name=u"4时")
    h5 = models.CharField(max_length=32, null=False, verbose_name=u"5时")
    h6 = models.CharField(max_length=32, null=False, verbose_name=u"6时")
    h7 = models.CharField(max_length=32, null=False, verbose_name=u"7时")
    h8 = models.CharField(max_length=32, null=False, verbose_name=u"8时")
    h9 = models.CharField(max_length=32, null=False, verbose_name=u"9时")
    h10 = models.CharField(max_length=32, null=False, verbose_name=u"10时")
    h11 = models.CharField(max_length=32, null=False, verbose_name=u"11时")
    h12 = models.CharField(max_length=32, null=False, verbose_name=u"12时")
    h13 = models.CharField(max_length=32, null=False, verbose_name=u"13时")
    h14 = models.CharField(max_length=32, null=False, verbose_name=u"14时")
    h15 = models.CharField(max_length=32, null=False, verbose_name=u"15时")
    h16 = models.CharField(max_length=32, null=False, verbose_name=u"16时")
    h17 = models.CharField(max_length=32, null=False, verbose_name=u"17时")
    h18 = models.CharField(max_length=32, null=False, verbose_name=u"18时")
    h19 = models.CharField(max_length=32, null=False, verbose_name=u"19时")
    h20 = models.CharField(max_length=32, null=False, verbose_name=u"20时")
    h21 = models.CharField(max_length=32, null=False, verbose_name=u"21时")
    h22 = models.CharField(max_length=32, null=False, verbose_name=u"22时")
    h23 = models.CharField(max_length=32, null=False, verbose_name=u"23时")
    pv0 = models.IntegerField(null=False, verbose_name=u"0时峰谷标识-2深谷-1谷0平1高峰2尖峰")
    pv1 = models.IntegerField(null=False, verbose_name=u"1时峰谷标识")
    pv2 = models.IntegerField(null=False, verbose_name=u"2时峰谷标识")
    pv3 = models.IntegerField(null=False, verbose_name=u"3时峰谷标识")
    pv4 = models.IntegerField(null=False, verbose_name=u"4时峰谷标识")
    pv5 = models.IntegerField(null=False, verbose_name=u"5时峰谷标识")
    pv6 = models.IntegerField(null=False, verbose_name=u"6时峰谷标识")
    pv7 = models.IntegerField(null=False, verbose_name=u"7时峰谷标识")
    pv8 = models.IntegerField(null=False, verbose_name=u"8时峰谷标识")
    pv9 = models.IntegerField(null=False, verbose_name=u"9时峰谷标识")
    pv10 = models.IntegerField(null=False, verbose_name=u"10时峰谷标识")
    pv11 = models.IntegerField(null=False, verbose_name=u"11时峰谷标识")
    pv12 = models.IntegerField(null=False, verbose_name=u"12时峰谷标识")
    pv13 = models.IntegerField(null=False, verbose_name=u"13时峰谷标识")
    pv14 = models.IntegerField(null=False, verbose_name=u"14时峰谷标识")
    pv15 = models.IntegerField(null=False, verbose_name=u"15时峰谷标识")
    pv16 = models.IntegerField(null=False, verbose_name=u"16时峰谷标识")
    pv17 = models.IntegerField(null=False, verbose_name=u"17时峰谷标识")
    pv18 = models.IntegerField(null=False, verbose_name=u"18时峰谷标识")
    pv19 = models.IntegerField(null=False, verbose_name=u"19时峰谷标识")
    pv20 = models.IntegerField(null=False, verbose_name=u"20时峰谷标识")
    pv21 = models.IntegerField(null=False, verbose_name=u"21时峰谷标识")
    pv22 = models.IntegerField(null=False, verbose_name=u"22时峰谷标识")
    pv23 = models.IntegerField(null=False, verbose_name=u"23时峰谷标识")
    province_id = models.IntegerField(null=False, verbose_name=u"所属省份")
    ele_id = models.IntegerField(null=False, verbose_name=u"用电分类")
    part_id = models.IntegerField(null=False, verbose_name=u"用电分类部制度")
    vol_id = models.IntegerField(null=False, verbose_name=u"电压等级")
    is_use = models.CharField(max_length=1, null=False, default='1', verbose_name=u"是否使用1是0否")

    class Meta:
        db_table = "t_side_forecase_price"