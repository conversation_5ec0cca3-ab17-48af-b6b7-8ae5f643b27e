package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tpanlogs.TPanLogsCreateDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsQueryDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsUpdateDTO;
import com.robestec.analysis.entity.TPanLogs;
import com.robestec.analysis.mapper.TPanLogsMapper;
import com.robestec.analysis.service.TPanLogsService;
import com.robestec.analysis.vo.TPanLogsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 下发日志记录服务实现类
 */
@Slf4j
@Service
public class TPanLogsServiceImpl extends SuperServiceImpl<TPanLogsMapper, TPanLogs>
        implements TPanLogsService {

    @Override
    public PageResult<TPanLogsVO> queryTPanLogs(TPanLogsQueryDTO queryDTO) {
        LambdaQueryWrapper<TPanLogs> wrapper = new LambdaQueryWrapper<TPanLogs>()
                .like(StringUtils.hasText(queryDTO.getProjectName()), TPanLogs::getProjectName, queryDTO.getProjectName())
                .like(StringUtils.hasText(queryDTO.getStation()), TPanLogs::getStation, queryDTO.getStation())
                .eq(queryDTO.getUserId() != null, TPanLogs::getUserId, queryDTO.getUserId())
                .like(StringUtils.hasText(queryDTO.getUserName()), TPanLogs::getUserName, queryDTO.getUserName())
                .like(StringUtils.hasText(queryDTO.getTypeName()), TPanLogs::getTypeName, queryDTO.getTypeName())
                .like(StringUtils.hasText(queryDTO.getContent()), TPanLogs::getContent, queryDTO.getContent())
                .eq(queryDTO.getStatus() != null, TPanLogs::getStatus, queryDTO.getStatus())
                .eq(queryDTO.getIsUse() != null, TPanLogs::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TPanLogs::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TPanLogs::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(TPanLogs::getCreateTime);

        Page<TPanLogs> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TPanLogs> result = this.page(page, wrapper);

        List<TPanLogsVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPanLogsVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTPanLogs(TPanLogsCreateDTO createDTO) {
        TPanLogs entity = BeanUtil.copyProperties(createDTO, TPanLogs.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTPanLogs(TPanLogsUpdateDTO updateDTO) {
        TPanLogs entity = BeanUtil.copyProperties(updateDTO, TPanLogs.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTPanLogs(Long id) {
        this.removeById(id);
    }

    @Override
    public TPanLogsVO getTPanLogs(Long id) {
        TPanLogs entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTPanLogsList(List<TPanLogsCreateDTO> createDTOList) {
        List<TPanLogs> entityList = BeanUtil.copyToList(createDTOList, TPanLogs.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TPanLogsVO> getTPanLogsByUserId(Long userId) {
        List<TPanLogs> entityList = this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getUserId, userId)
                .orderByDesc(TPanLogs::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPanLogsVO> getTPanLogsByStation(String station) {
        List<TPanLogs> entityList = this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getStation, station)
                .orderByDesc(TPanLogs::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPanLogsVO> getTPanLogsByStatus(Integer status) {
        List<TPanLogs> entityList = this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getStatus, status)
                .orderByDesc(TPanLogs::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPanLogsVO> getTPanLogsByTypeName(String typeName) {
        List<TPanLogs> entityList = this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getTypeName, typeName)
                .orderByDesc(TPanLogs::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByUserId(Long userId) {
        return this.count(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getUserId, userId));
    }

    @Override
    public Long countByStatus(Integer status) {
        return this.count(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getStatus, status));
    }

    /**
     * 转换为VO对象
     */
    private TPanLogsVO convertToVO(TPanLogs entity) {
        if (entity == null) {
            return null;
        }
        TPanLogsVO vo = BeanUtil.copyProperties(entity, TPanLogsVO.class);
        vo.setStatusName(getStatusName(entity.getStatus()));
        return vo;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "成功";
            case 2:
                return "失败";
            default:
                return "未知";
        }
    }

    // ==================== 从Mapper迁移的方法 ====================

    /**
     * 分页查询下发记录列表
     * 对应Python中GetPlanHis方法的查询逻辑
     */
    public PageResult<TPanLogsVO> selectPlanHistoryList(
            Integer pageNum, Integer pageSize, String station, Integer status,
            List<String> typeNames, String startTime, String endTime) {

        Page<TPanLogs> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<TPanLogs> wrapper = new LambdaQueryWrapper<TPanLogs>()
                .eq(TPanLogs::getIsUse, 1)
                .like(StringUtils.hasText(station), TPanLogs::getStation, station)
                .eq(status != null, TPanLogs::getStatus, status)
                .in(typeNames != null && !typeNames.isEmpty(), TPanLogs::getTypeName, typeNames)
                .ge(StringUtils.hasText(startTime), TPanLogs::getCreateTime, startTime)
                .le(StringUtils.hasText(endTime), TPanLogs::getCreateTime, endTime)
                .orderByDesc(TPanLogs::getCreateTime);

        Page<TPanLogs> result = this.page(page, wrapper);

        List<TPanLogsVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPanLogsVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    /**
     * 查询所有下发记录（用于导出）
     * 对应Python中planHisExport方法的查询逻辑
     */
    public List<TPanLogs> selectAllPlanHistory(
            String station, Integer status, List<String> typeNames,
            String startTime, String endTime) {

        return this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getIsUse, 1)
                .like(StringUtils.hasText(station), TPanLogs::getStation, station)
                .eq(status != null, TPanLogs::getStatus, status)
                .in(typeNames != null && !typeNames.isEmpty(), TPanLogs::getTypeName, typeNames)
                .ge(StringUtils.hasText(startTime), TPanLogs::getCreateTime, startTime)
                .le(StringUtils.hasText(endTime), TPanLogs::getCreateTime, endTime)
                .orderByDesc(TPanLogs::getCreateTime));
    }

    /**
     * 根据用户ID查询下发记录
     */
    public List<TPanLogs> selectLogsByUserId(Long userId) {
        return this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getUserId, userId)
                .eq(TPanLogs::getIsUse, 1)
                .orderByDesc(TPanLogs::getCreateTime));
    }

    /**
     * 根据电站名称查询下发记录
     */
    public List<TPanLogs> selectLogsByStation(String station) {
        return this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getStation, station)
                .eq(TPanLogs::getIsUse, 1)
                .orderByDesc(TPanLogs::getCreateTime));
    }

    /**
     * 根据类型名称查询下发记录
     */
    public List<TPanLogs> selectLogsByTypeName(String typeName) {
        return this.list(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getTypeName, typeName)
                .eq(TPanLogs::getIsUse, 1)
                .orderByDesc(TPanLogs::getCreateTime));
    }

    /**
     * 统计下发记录数量
     */
    public int countLogs(Integer status, Long userId) {
        return Math.toIntExact(this.count(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getIsUse, 1)
                .eq(status != null, TPanLogs::getStatus, status)
                .eq(userId != null, TPanLogs::getUserId, userId)));
    }

    /**
     * 根据时间范围统计下发记录数量
     */
    public int countLogsByTimeRange(String startTime, String endTime) {
        return Math.toIntExact(this.count(Wrappers.<TPanLogs>lambdaQuery()
                .eq(TPanLogs::getIsUse, 1)
                .ge(StringUtils.hasText(startTime), TPanLogs::getCreateTime, startTime)
                .le(StringUtils.hasText(endTime), TPanLogs::getCreateTime, endTime)));
    }
}
