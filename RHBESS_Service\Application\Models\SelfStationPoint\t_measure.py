#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 17:31:13
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_measure.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:22:54



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_device import DevicePT

class MeasurePT(mqtt_Base):
    ''' 测量量说明表 '''
    __tablename__ = "t_measure"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    device_id = Column(Integer, ForeignKey("t_device.id"),nullable=False, comment=u"所属设备")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    unit = Column(String(10), nullable=True,comment=u"单位")
    low_value = Column(Float, nullable=True,comment=u"下限合理值")
    up_value = Column(Float, nullable=True,comment=u"上限合理值")
    start_value = Column(Float, nullable=True,comment=u"基数")
    delta = Column(Float, nullable=True,comment=u"变化限值")
    coef = Column(Float, nullable=True,comment=u"系数")

    u_limit = Column(Float, nullable=True,comment=u"告警上限阈值1（最小）")
    uu_limit = Column(Float, nullable=True,comment=u"告警上限阈值2（中间）")
    uuu_limit = Column(Float, nullable=True,comment=u"告警上限阈值3（最大）")
    l_limi = Column(Float, nullable=True,comment=u"告警下限阈值1")
    ll_limit = Column(Float, nullable=True,comment=u"告警下限阈值2")
    lll_limit = Column(Float, nullable=True,comment=u"告警下限阈值3")
    u_duration = Column(Integer, nullable=True,comment=u"上限1持续时长(单位：秒)")
    uu_duration = Column(Integer, nullable=True,comment=u"上限2持续时长(单位：秒)")
    uuu_duration = Column(Integer, nullable=True,comment=u"上限3持续时长(单位：秒)")
    l_duration = Column(Integer, nullable=True,comment=u"上限1持续时长(单位：秒)")
    ll_duration = Column(Integer, nullable=True,comment=u"上限2持续时长(单位：秒)")
    lll_duration = Column(Integer, nullable=True,comment=u"上限3持续时长(单位：秒)")

    store_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否存盘（0否1是，默认1）")
    rep_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否上报（0否1是，默认1）")
    

    device_measure = relationship("DevicePT",passive_deletes=True,backref='device_measure')

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','delta':'%s','coef':'%s','start_value':'%s','store_flag':'%s','rep_flag':'%s','unit':'%s'}" % (
            self.id,self.name,self.descr,self.delta,self.coef,self.start_value,self.store_flag,self.rep_flag,self.unit)