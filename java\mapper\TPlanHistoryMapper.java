package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.TPlanHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 计划历史记录Mapper
 * 对应Python中的TPlanHistory相关数据库操作
 */
@Mapper
public interface TPlanHistoryMapper extends BaseMapper<TPlanHistory> {

    /**
     * 根据ID列表软删除记录
     * 对应Python中的批量删除逻辑
     */
    @Update("<script>" +
            "UPDATE t_plan_history SET is_use = 0 " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    int softDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据计划ID查询历史记录
     */
    @Select("SELECT * FROM t_plan_history " +
            "WHERE plan_id = #{planId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPlanHistory> selectByPlanId(@Param("planId") Long planId);

    /**
     * 根据用户ID和状态查询记录
     */
    @Select("<script>" +
            "SELECT * FROM t_plan_history " +
            "WHERE user_id = #{userId} AND is_use = 1 " +
            "<if test='status != null'>" +
            "  AND status = #{status} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<TPlanHistory> selectByUserIdAndStatus(
            @Param("userId") Long userId,
            @Param("status") Integer status
    );

    /**
     * 根据电站名称查询记录
     */
    @Select("<script>" +
            "SELECT * FROM t_plan_history " +
            "WHERE is_use = 1 " +
            "<if test='station != null and station != \"\"'>" +
            "  AND station LIKE CONCAT('%', #{station}, '%') " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<TPlanHistory> selectByStation(@Param("station") String station);

    /**
     * 更新计划状态
     * 对应Python中powerPlanStop方法的状态更新逻辑
     */
    @Update("UPDATE t_plan_history SET status = #{status}, update_time = NOW() " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据计划类型查询记录
     */
    @Select("SELECT * FROM t_plan_history " +
            "WHERE plan_type = #{planType} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPlanHistory> selectByPlanType(@Param("planType") Integer planType);

    /**
     * 根据时间范围查询记录
     */
    @Select("<script>" +
            "SELECT * FROM t_plan_history " +
            "WHERE is_use = 1 " +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND start_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND end_time <= #{endTime} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<TPlanHistory> selectByTimeRange(
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 统计用户的计划数量
     */
    @Select("SELECT COUNT(*) FROM t_plan_history " +
            "WHERE user_id = #{userId} AND is_use = 1")
    int countByUserId(@Param("userId") Long userId);

    /**
     * 根据状态统计计划数量
     */
    @Select("SELECT COUNT(*) FROM t_plan_history " +
            "WHERE status = #{status} AND is_use = 1")
    int countByStatus(@Param("status") Integer status);
}
