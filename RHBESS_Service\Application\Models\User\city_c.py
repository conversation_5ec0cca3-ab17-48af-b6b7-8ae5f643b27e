#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_Service\Application\Models\User\city_c.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-24 14:32:12


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class City(user_Base):
    u'市表'
    __tablename__ = "c_city"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    city = Column(VARCHAR(256), nullable=False, comment=u"城市名称")
    pro_id = Column(Integer, ForeignKey("c_province.id"), nullable=False, comment=u"所属省/直辖市/自治区")
    en_city = Column(VARCHAR(256), nullable=False, comment=u"城市名称")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
                
    def __repr__(self):
        
        return "{'id':'%s','city':'%s','pro_id':'%s','en_city':'%s'}" % (
            self.id,self.city,self.pro_id,self.en_city)
        
    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}