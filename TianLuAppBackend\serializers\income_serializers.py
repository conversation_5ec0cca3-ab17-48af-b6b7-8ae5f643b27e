import json

from django.core.validators import RegexValidator
from django_redis import get_redis_connection
from rest_framework import serializers
from apis.user import models
from rest_framework import exceptions

from apis.user.models import CustomIncome
from LocaleTool.common import redis_pool


class IncomeSerializer(serializers.Serializer):
    date = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    income_type = serializers.IntegerField(required=False)
    day_income = serializers.IntegerField(required=True)
    english_name = serializers.CharField(required=True)
    notes = serializers.CharField(required=False, default=None)


class AddIncomesSerializer(serializers.Serializer):
    data = IncomeSerializer(many=True)


class SummerBaseSerializer(serializers.Serializer):
    hour_1 = serializers.IntegerField(required=True)
    hour_2 = serializers.IntegerField(required=True)
    hour_3 = serializers.IntegerField(required=True)
    hour_4 = serializers.IntegerField(required=True)
    hour_5 = serializers.IntegerField(required=True)
    hour_6 = serializers.IntegerField(required=True)
    hour_7 = serializers.IntegerField(required=True)
    hour_8 = serializers.IntegerField(required=True)
    hour_9 = serializers.IntegerField(required=True)
    hour_10 = serializers.IntegerField(required=True)
    hour_11 = serializers.IntegerField(required=True)
    hour_12 = serializers.IntegerField(required=True)
    hour_13 = serializers.IntegerField(required=True)
    hour_14 = serializers.IntegerField(required=True)
    hour_15 = serializers.IntegerField(required=True)
    hour_16 = serializers.IntegerField(required=True)
    hour_17 = serializers.IntegerField(required=True)
    hour_18 = serializers.IntegerField(required=True)
    hour_19 = serializers.IntegerField(required=True)
    hour_20 = serializers.IntegerField(required=True)
    hour21 = serializers.IntegerField(required=True)
    hour_22 = serializers.IntegerField(required=True)
    hour_23 = serializers.IntegerField(required=True)
    hour_24 = serializers.IntegerField(required=True)


class NoneSummerBaseSerializer(serializers.Serializer):
    hour_1 = serializers.IntegerField(required=True)
    hour_2 = serializers.IntegerField(required=True)
    hour_3 = serializers.IntegerField(required=True)
    hour_4 = serializers.IntegerField(required=True)
    hour_5 = serializers.IntegerField(required=True)
    hour_6 = serializers.IntegerField(required=True)
    hour_7 = serializers.IntegerField(required=True)
    hour_8 = serializers.IntegerField(required=True)
    hour_9 = serializers.IntegerField(required=True)
    hour_10 = serializers.IntegerField(required=True)
    hour_11 = serializers.IntegerField(required=True)
    hour_12 = serializers.IntegerField(required=True)
    hour_13 = serializers.IntegerField(required=True)
    hour_14 = serializers.IntegerField(required=True)
    hour_15 = serializers.IntegerField(required=True)
    hour_16 = serializers.IntegerField(required=True)
    hour_17 = serializers.IntegerField(required=True)
    hour_18 = serializers.IntegerField(required=True)
    hour_19 = serializers.IntegerField(required=True)
    hour_20 = serializers.IntegerField(required=True)
    hour21 = serializers.IntegerField(required=True)
    hour_22 = serializers.IntegerField(required=True)
    hour_23 = serializers.IntegerField(required=True)
    hour_24 = serializers.IntegerField(required=True)


class SummerAutomaticControlSerializer(serializers.ModelSerializer):
    """夏季尖峰填谷"""
    # 手机号校验

    mobile = serializers.CharField(required=True,
                                   validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")],
                                   write_only=True)
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")],
                                 write_only=True)
    # 站名
    summer = serializers.ListField(child=serializers.IntegerField(), required=True, allow_empty=False)
    follow = serializers.IntegerField(required=True, write_only=True)  # 负荷跟随
    none_summer = serializers.ListField(child=serializers.IntegerField(), required=True, allow_empty=False)
    rl_list = serializers.ListField(child=serializers.DictField(), required=False, write_only=True)
    plan_id = serializers.IntegerField(required=True, write_only=True)
    class Meta:
        model = models.SummerAutomation
        exclude = ['stations', "user"]

    def create(self, validated_data):
        user = self.context.get('user_id')
        station = self.context.get('strategy')
        uid = self.context.get('uid')
        if not station:
            raise serializers.ValidationError("站名不能为空")

        station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
        if not station_ins:
            raise serializers.ValidationError("站点不存在")
        validated_data.pop("mobile")
        validated_data.pop("code")
        validated_data.pop("follow")
        validated_data.pop("summer")
        validated_data.pop("none_summer")
        validated_data.pop("rl_list")
        obj = models.SummerAutomation.objects.create(user=user, **validated_data, stations=station_ins, uid=uid)
        return obj

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "auto" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if cache_mobile_code.decode('utf-8') != value:
            raise exceptions.ValidationError("验证码错误")
        return value


class PostAutomaticPlanAddSerializer(serializers.ModelSerializer):
    """夏季尖峰填谷"""
    name = serializers.CharField(required=True, max_length=128)
    summer = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    none_summer = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    rl_list = serializers.ListSerializer(required=False, child=serializers.DictField())

    class Meta:
        model = models.UserAutomation
        exclude = ['user']
        extra_kwargs = {
            "name": {"required": True, },
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }

    def validate_name(self, value):
        print(value)
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        automation = models.UserAutomation.objects.filter(user=user_ins, name=value)
        if automation:
            raise exceptions.ValidationError("策略名已存在")
        return value

    def update(self, instance, validated_data):
        summer = validated_data.get("summer", None)
        none_summer = validated_data.get("none_summer", None)
        rl_list = validated_data.get("rl_list", None)
        if summer:
            validated_data["summer"] = str(summer)
        if none_summer:
            validated_data["none_summer"] = str(none_summer)
        if rl_list:
            validated_data["rl_list"] = str(rl_list)
        instance = instance.update(**validated_data)
        # instance.save()
        return instance

    def create(self, validated_data):
        user = self.context.get('user_id')
        summer = validated_data.get("summer", None)
        none_summer = validated_data.get("none_summer", None)
        rl_list = validated_data.get("rl_list", None)
        if summer:
            validated_data["summer"] = str(summer)
        if none_summer:
            validated_data["none_summer"] = str(none_summer)
        if rl_list:
            validated_data["rl_list"] = str(rl_list)
        user_ins = models.UserDetails.objects.get(id=user)
        obj = models.UserAutomation.objects.create(user=user_ins, **validated_data)
        return obj
class PutAutomaticPlanAddSerializer(serializers.ModelSerializer):
    """夏季尖峰填谷"""
    name = serializers.CharField(required=True, max_length=128)
    summer = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    none_summer = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    rl_list = serializers.ListSerializer(required=False, child=serializers.DictField())

    class Meta:
        model = models.UserAutomation
        exclude = ['user']
        extra_kwargs = {
            "name": {"required": True, },
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }

    # def validate_name(self, value):
    #     print(value)
    #     user = self.context.get('user_id')
    #     user_ins = models.UserDetails.objects.get(id=user)
    #     automation = models.UserAutomation.objects.filter(user=user_ins, name=value)
    #     if automation:
    #         raise exceptions.ValidationError("策略名已存在")
    #     return value

    def update(self, instance, validated_data):
        summer = validated_data.get("summer", None)
        none_summer = validated_data.get("none_summer", None)
        rl_list = validated_data.get("rl_list", None)
        if summer:
            validated_data["summer"] = str(summer)
        if none_summer:
            validated_data["none_summer"] = str(none_summer)
        if rl_list:
            validated_data["rl_list"] = str(rl_list)
        instance = instance.update(**validated_data)
        # instance.save()
        return instance

    def create(self, validated_data):
        user = self.context.get('user_id')
        summer = validated_data.get("summer", None)
        none_summer = validated_data.get("none_summer", None)
        rl_list = validated_data.get("rl_list", None)
        if summer:
            validated_data["summer"] = str(summer)
        if none_summer:
            validated_data["none_summer"] = str(none_summer)
        if rl_list:
            validated_data["rl_list"] = str(rl_list)
        user_ins = models.UserDetails.objects.get(id=user)
        obj = models.UserAutomation.objects.create(user=user_ins, **validated_data)
        return obj


class AutomaticGetSerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)

    def validate_station(self, value):
        exist = models.StationDetails.objects.filter(is_delete=0, english_name=value)
        if not exist:
            return exceptions.ValidationError("站名不存在")
        return value


# class AutomaticResSerializers(serializers.ModelSerializer):
#     class Meta:
#         model = models.PeakValley
#         exclude = ["province", "type", "level", "year_month", "id", "create_time"]


class CustomIncomeSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    project = serializers.CharField(required=True, max_length=32)
    station = serializers.CharField(required=True, max_length=32)
    en_project = serializers.CharField(read_only=True)
    en_station = serializers.CharField(read_only=True)
    income = serializers.DecimalField(required=True,  max_digits=10, decimal_places=2)
    income_type = serializers.IntegerField(required=True)
    income_month = serializers.CharField(required=True, max_length=10)
    meter_charge = serializers.DecimalField(required=False,  max_digits=10, decimal_places=2)
    meter_discharge = serializers.DecimalField(required=False,  max_digits=10, decimal_places=2)
    start_day = serializers.DateField(required=False, input_formats=['%Y-%m-%d'])
    end_day = serializers.DateField(required=False, input_formats=['%Y-%m-%d'])
    creator = serializers.CharField(required=False, read_only=True)
    updator = serializers.CharField(required=False, read_only=True)
    en_creator = serializers.CharField(read_only=True)
    en_updator = serializers.CharField(read_only=True)
    create_time = serializers.DateTimeField(read_only=True)
    update_time = serializers.DateTimeField(read_only=True)
    attar_url = serializers.CharField(required=False, read_only=True)
    attar_name = serializers.CharField(required=False, read_only=True)
    note = serializers.CharField(required=False, max_length=256)
    is_delete = serializers.IntegerField(default=0, write_only=True)

    def create(self, validated_data):
        """新建"""
        lang = self.context.get('lang', 'zh')
        ins = CustomIncome.objects.create(**validated_data, en_station=validated_data.get('station'),
                                          en_creator=validated_data.get('creator'),
                                          en_updator=validated_data.get('updator'))

        # 异步翻译
        pdr_data = {'id': ins.id,
                    'table': 't_custom_income',
                    'update_data': {'station': validated_data.get('station')}}
        if validated_data.get('creator'):
            pdr_data['update_data']['creator'] = validated_data.get('creator')
        if validated_data.get('updator'):
            pdr_data['update_data']['updator'] = validated_data.get('updator')

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return ins

    def update(self, instance, validated_data):
        """更新，instance为要更新的对象实例"""
        lang = self.context.get('lang', 'zh')

        instance.project = validated_data.get('project', instance.project)
        instance.station = validated_data.get('station', instance.station)
        instance.income = validated_data.get('income', instance.income)
        instance.income_type = validated_data.get('income_type', instance.income_type)
        instance.income_month = validated_data.get('income_month', instance.income_month)
        instance.meter_charge = validated_data.get('meter_charge', instance.meter_charge)
        instance.meter_discharge = validated_data.get('meter_discharge', instance.meter_discharge)
        instance.start_day = validated_data.get('start_day', instance.start_day)
        instance.end_day = validated_data.get('end_day', instance.end_day)
        instance.updator = validated_data.get('updator', instance.updator)
        instance.attar_url = validated_data.get('attar_url', instance.attar_url)
        instance.attar_name = validated_data.get('attar_name', instance.attar_name)
        instance.en_project = validated_data.get('project', instance.en_station)
        instance.en_station = validated_data.get('station', instance.en_station)
        instance.en_updator = validated_data.get('updator', instance.en_updator)
        instance.en_creator = validated_data.get('creator', instance.en_creator)

        instance.save()

        # 异步翻译
        pdr_data = {'id': instance.id,
                    'table': 't_custom_income',
                    'update_data': {'station': validated_data.get('station'), 'project': validated_data.get('project')}}
        if validated_data.get('creator'):
            pdr_data['update_data']['creator'] = validated_data.get('creator')
        if validated_data.get('updator'):
            pdr_data['update_data']['updator'] = validated_data.get('updator')

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return instance
