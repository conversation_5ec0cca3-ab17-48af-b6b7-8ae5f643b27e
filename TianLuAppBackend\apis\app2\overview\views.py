import datetime
from decimal import Decimal
from django.core.cache import caches
from django.conf import settings
from django.db.models import Sum, Q, Max, Min, Avg
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.app2.overview.main import IncomeMain
from apis.app2.station_task import  station_base_income_day
from apis.app2.utils import paging
from apis.user import models
from common import common_response_code
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from tools.count import unit_convert

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
cache = caches['1']

# class FreezeDataDay(APIView):
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证

#     def post(self, request):
#         """逐天功率冻结数据"""
#         s_time = request.data.get('s_time')
#         e_time = request.data.get('e_time')
#         timing_chgdig_s_day(s_time, e_time)
#         return Response({
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#                 "detail": [],
#             },
#         })


class BaseIncomeDay(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def post(self, request):
        """逐天收益冻结数据"""
        s_time = request.data.get('s_time')
        e_time = request.data.get('e_time')
        r = station_base_income_day(s_time, e_time)
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": r,
            },
        })


class Test(APIView):
    def get(self, r):
        """
        写入基准充放电量和收益表
        :return:
        """
        # from apis.app2.station_task import station_efficiency
        # print(station_efficiency())
        from django.db.models import Min
        now_date = datetime.datetime.now().date()
        income_res = models.StationIncome.objects.values('station_id').annotate(date=Min('income_date'))
        # wxsm_stations = models.StationDetails.objects.filter(id__in=(174, 175))
        # print(wxsm_stations)
        # for station in wxsm_stations:
        for income in income_res:
            # print(79, station.station_name)
            try:
                # 查询该站下理论充放字典
                # station_base = models.StationBase.objects.filter(station_id=station.id).all()
                station_base = models.StationBase.objects.filter(station_id=income.get('station_id')).all()
                station = models.StationDetails.objects.get(id=income.get('station_id'))
                english_name = station.english_name     # 站英文标识
                former_ids = [base.former_base_id for base in station_base]
                income_list = []
                # time_ = datetime.datetime.strptime('2024-05-10', '%Y-%m-%d').date()
                time_ = income.get('date')
                r_count = (now_date - time_).days
                # print(89, former_ids)
                if former_ids:
                    # 查询该站下12个月的电价
                    prices = models.PeakValley.objects.filter(type=station.type, level=station.level,
                                                       province_id=station.province_id).all()
                    former_data = models.FormerBase.objects.filter(id__in=former_ids).all()
                    former_data = {former.year_month: former.__dict__ for former in former_data}
                    price_data = {str(price.year_month): price.__dict__ for price in prices}
                    for d in range(r_count + 1):
                        day_time1 = time_ + datetime.timedelta(days=d)
                        month = str(day_time1.month)

                        price = price_data.get(month)
                        former = former_data.get(month)
                        chag = 0  # 日充电
                        disg = 0  # 日放电
                        income = 0  # 日收益
                        if former and price:
                            for y in range(1, 25):
                                if former.get(f'h{y}f') == -1:  # 放电
                                    chag += former.get(f'h{y}p')
                                    income -= former.get(f'h{y}p') * float(price.get(f'h{y - 1}'))
                                elif former.get(f'h{y}f') == 1:  # 充电
                                    disg += former.get(f'h{y}p')
                                    income += former.get(f'h{y}p') * float(price.get(f'h{y - 1}'))
                                else:
                                    continue
                        station_base_income = models.StationBaseIncome()
                        station_base_income.station_name = english_name
                        station_base_income.day = str(day_time1)
                        station_base_income.day_income = round(income, 3)
                        station_base_income.day_chag = round(chag, 3)
                        station_base_income.day_disg = round(disg, 3)
                        income_list.append(station_base_income)

                # if len(income_list) > 1000:
                #     print('-'*100)
                #     models.StationBaseIncome.objects.bulk_create(income_list)
                #     income_list = []
                #     print('执行1000条！！！')

                models.StationBaseIncome.objects.bulk_create(income_list)
            except Exception as e:
                print(e)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "health": 'count_dic',
                },
            }
        )



def station_list(request):
    request_id = request.user["user_id"]
    station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0).all()
    project_ids = []
    master_station_ids = []
    efficiency_list = []
    for i in station_obj:
        project_ids.append(i.project_id)
        master_station_ids.append(i.id)
        if i.efficiency != '--':
            efficiency = float(i.efficiency)
            if efficiency > 100:
                efficiency_list.append(100)
            elif efficiency >= 85:
                efficiency_list.append(efficiency)
            else:
                efficiency_list.append(85)
    if efficiency_list:
        discharge_efficiency = round(sum(efficiency_list) / len(efficiency_list) / 100, 2)
    else:
        discharge_efficiency = 0.85
    # 运行天数
    project_res = models.Project.objects.filter(id__in=project_ids)
    in_time = project_res.aggregate(Min('in_time'))
    station_count = len(station_obj)
    rated_power_all = project_res.aggregate(Sum('rated_power'))
    rated_capacity_all = project_res.aggregate(Sum('rated_capacity'))
    return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                # "detail": data,
                "station_count": station_count,
                "rated_power_all": (unit_convert(Decimal(rated_power_all.get('rated_power__sum')), 'kW')),
                "rated_capacity_all": (unit_convert(Decimal(rated_capacity_all.get('rated_capacity__sum')), 'kWh')),
                "run_days": (datetime.datetime.now() - in_time.get('in_time__min')).days,
                "discharge_efficiency": discharge_efficiency if discharge_efficiency > 0.85 else 0.85
            },
        })

class StationListView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def get(self, request):
        data = station_list(request)
        return data
        # request_id = request.user["user_id"]
        # station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0).all()
        # project_ids = []
        # master_station_ids = []
        # efficiency_list = []
        # for i in station_obj:
        #     project_ids.append(i.project_id)
        #     master_station_ids.append(i.id)
        #     if i.efficiency != '--':
        #         efficiency = float(i.efficiency)
        #         if efficiency > 100:
        #             efficiency_list.append(100)
        #         elif efficiency >= 85:
        #             efficiency_list.append(efficiency)
        #         else:
        #             efficiency_list.append(85)
        #
        # discharge_efficiency = round(sum(efficiency_list) / len(efficiency_list) /100, 2)
        # # 运行天数
        # project_res = models.Project.objects.filter(id__in=project_ids)
        # in_time = project_res.aggregate(Min('in_time'))
        # station_count = len(station_obj)
        # rated_power_all = project_res.aggregate(Sum('rated_power'))
        # rated_capacity_all = project_res.aggregate(Sum('rated_capacity'))
        # return Response(
        #     {
        #         "code": common_response_code.SUCCESS,
        #         "data": {
        #             "message": "success",
        #             # "detail": data,
        #             "station_count": station_count,
        #             "rated_power_all": (unit_convert(int(rated_power_all.get('rated_power__sum')), 'kW')),
        #             "rated_capacity_all": (unit_convert(int(rated_capacity_all.get('rated_capacity__sum')), 'kWh')),
        #             "run_days": (datetime.datetime.now() - in_time.get('in_time__min')).days,
        #             "discharge_efficiency": discharge_efficiency if discharge_efficiency > 0.85 else 0.85
        #         },
        #     }
        # )

        # station_count = len(station_obj)
        # in_time_list = []
        # rated_power_all = []
        # rated_capacity_all = []
        # discharge_efficiency_list = []
        # data = []
        # station_english_name = [-1]  # 防止查询失败
        # for m_station in station_obj:
        #     station_dict = {
        #         'id': m_station.id,
        #         'english_name': m_station.english_name,
        #         'address': None,
        #         'rated_power': 0,
        #         'rated_capacity': 0,
        #         'station_status': 1
        #     }
        #     try:
        #         in_time_list.append(m_station.project.in_time)
        #     except Exception as e:
        #         error_log.error('根据station信息，未找到对对应的project')
        #
        #     # stations = m_station.stationdetails_set.filter(~Q(slave=0), ~Q(pack=0)).all()
        #     stations = m_station.stationdetails_set.all()
        #     status_ids = []
        #     station_list = [-1]
        #     if stations:
        #         for i, station in enumerate(stations):
        #             if i == 0:
        #                 station_dict['address'] = station.address
        #             station_english_name.append(station.english_name)
        #             # 计算总功率和容量
        #             rated_power_all.append(float(station.rated_power))
        #             rated_capacity_all.append(float(station.rated_capacity))
        #             station_list.append(station.english_name)
        #             status_ids.append(station.id)
        #     data.append(station_dict)
        #     status_data = models.StationStatus.objects.filter(station_id__in=status_ids).aggregate(Max('status'))
        #     station_dict['station_status'] = status_data.get('status__max')
        #
        # with connections['doris_ads_rhyc'].cursor() as ads_cursor:
        #     # 切换查询IDC库
        #     discharg_sql = """
        #                 SELECT AVG(res.d) FROM (SELECT
        #                 CASE
        #                     WHEN
        #                     sum( v_disg ) / sum( disg_soc ) / (sum( v_chag ) / sum( chag_soc )) < 0.85
        #                     THEN 0.85
        #                     WHEN
        #                     sum( v_disg ) / sum( disg_soc ) / ( sum( v_chag ) / sum( chag_soc )) > 1
        #                     THEN 1
        #                     WHEN
        #                     sum( v_disg ) / sum( disg_soc ) / ( sum( v_chag ) / sum( chag_soc )) is NULL
        #                     THEN 1
        #                     ELSE
        #                     sum( v_disg ) / sum( disg_soc ) / ( sum( v_chag ) / sum( chag_soc ))
        #                     END as d
        #                     FROM
        #                         ads_report_chag_disg_union_cw_cm_cq_cy
        #                     WHERE
        #                         date_type='year' and
        #                         station_type <= 1
        #                         AND station in {}
        #                 GROUP BY
        #                 station ) as res""".format(tuple(station_english_name))
        #
        #     # 获取查询结果
        #     try:
        #         ads_cursor.execute(discharg_sql)
        #         discharg_res = ads_cursor.fetchall()
        #     except Exception as e:
        #         error_log.error(e)
        #         return Response(
        #             {
        #                 "code": common_response_code.SUMMARY_CODE,
        #                 "data": {
        #                     "message": "error",
        #                     "detail": '查询失败！',
        #                 }
        #             }
        #         )
        #
        # first_day = min(in_time_list)
        # run_days = datetime.datetime.now() - first_day
        # discharge_efficiency = round(discharg_res[0][0], 2)  # 充放电效率
        # return Response(
        #     {
        #         "code": common_response_code.SUCCESS,
        #         "data": {
        #             "message": "success",
        #             "detail": data,
        #             "station_count": station_count,
        #             "rated_power_all": (unit_convert(int(sum(rated_power_all)), 'kW')),
        #             "rated_capacity_all": (unit_convert(int(sum(rated_capacity_all)), 'kWh')),
        #             "run_days": run_days.days,
        #             "discharge_efficiency": discharge_efficiency if discharge_efficiency > 0.85 else 0.85
        #         },
        #     }
        # )


def income_view_get(request):
    request_id = request.user["user_id"]
    station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0).all()
    query_type = request.query_params.get('type', 1)  # 0：累计；1：日；2：月；3：年  默认查询日
    # key = f'IncomeView_get_{request_id}_{query_type}'
    # if cache.get(key):
    #     return Response(cache.get(key))

    income_main = IncomeMain()
    detail_dic = income_main.income_res(station_obj, int(query_type))

    for k, v in detail_dic.items():
        # if detail_dic[k]['income'] >= 10000:
        #     detail_dic[k]['income'] = [str(round(detail_dic[k]['income'] / 10000, 2)), "万"]
        # else:
        detail_dic[k]['income'] = [str(detail_dic[k]['income'])]
        if detail_dic[k]['target_income'] >= 10000:
            detail_dic[k]['target_income'] = [str(round(detail_dic[k]['target_income'] / 10000, 2)), "万"]
        else:
            detail_dic[k]['target_income'] = [str(round(detail_dic[k]['target_income'], 2)), "元"]
    # cache.set(key, {
    #         "code": common_response_code.SUCCESS,
    #         "data": {
    #             "message": "success",
    #             "detail": detail_dic,
    #         },
    #     })
    return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": detail_dic,
            },
        })

class IncomeView(APIView):
    """收益总视图"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        try:
            data = income_view_get(request)
        except Exception as e:
            error_log.error("收益视图查询失败：", e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！',
                    }
                }
            )
        return data

    def post(self, request):
        """
        收益展开列表
        :param request:
        :return:
        """
        user_id = request.user['user_id']
        # 0：昨日收益；1：当月收益；2：当年收益；3：累计收益
        time_type = request.data.get('time_type', '0')
        page = int(request.data.get('page', 1))
        size = int(request.data.get('size', 8))
        # key = f'IncomeView_post_{user_id}_{time_type}_{page}_{size}'
        # if cache.get(key):
        #     return Response(cache.get(key))

        station_obj = models.MaterStation.objects.filter(userdetails__id=user_id, is_delete=0).all()

        if time_type not in ['0', '1', '2', '3']:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": "收益类型错误，不在范围内！",
                    },
                }
            )
        try:
            income_main = IncomeMain()
            page_res = paging(page, size, station_obj)
            data = income_main.income_list(page_res.get('data'), time_type)
        except Exception as e:
            error_log.error("收益视图查询失败：", e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！',
                    }
                }
            )
        # cache.set(key, {
        #         "code": common_response_code.SUCCESS,
        #         "data": {
        #             "message": "success",
        #             "detail": data,
        #         },
        #         "total": page_res.get('total'),
        #         "totalpage": page_res.get('totalpage'),
        #         "page": page_res.get('page'),
        #     }, timeout=60*15)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )


class MapHealthView(APIView):
    """地图大屏健康度"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]

        station_obj = models.MaterStation.objects.filter(userdetails__id=user_id, is_delete=0).all()
        # unit_count = []
        # for station in station_obj:
        #     battery_cluster = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).aggregate(
        #         battery_cluster_sum=Sum('battery_cluster'))
        #     if battery_cluster.get('battery_cluster_sum'):
        #         unit_count.append(Decimal(battery_cluster.get('battery_cluster_sum')))
        battery_cluster = models.StationDetails.objects.filter(is_delete=0, master_station__in=station_obj).filter(~Q(slave=0), ~Q(pack=0)).aggregate(
            battery_cluster_sum=Sum('battery_cluster'))
        unit_count = Decimal(battery_cluster.get('battery_cluster_sum'))
        # 判断data中大于等于 90 的数量
        count_dic = {"count_gte_90": unit_count, "count_gte_80": 0, "count_gte_60": 0, "count_lte_60": 0}
        # 百分比计算
        count_dic["count_gte_90_per"] = Decimal(unit_count / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_gte_80_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_gte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_lte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "health": count_dic,
                },
            }
        )
