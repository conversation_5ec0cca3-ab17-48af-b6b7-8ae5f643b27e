#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-17 16:38:59
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\juzhen_test.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-03-08 08:46:35

import numpy as np
import pandas as pd
import xarray as xr
import cfgrib

# df = pd.DataFrame({'名字': ['张三', '李四', '王五'],

#                    '年龄': [20, 25, 30],

#                    '性别': ['男', '男', '女']})
# df.to_csv('file.csv', index=False)

data = xr.open_dataset('qiya.grb2',engine='cfgrib') 

filters = list(data.variables)  # 所有key
obj_all = {}
for filter in filters:
    # df = data[filter].to_pandas()
    print ('_______________',filter,'--------------',data[filter])
    obj_all[filter] = data[filter]
# print ('obj_all:',obj_all)
# df = pd.DataFrame(obj_all)
# df.to_csv('output.csv', index=False)
    


# df = data['variable'].to_pandas().reset_index(drop=True)
# df.to_csv('output.csv', index=False)
# print (df,'-------------')
# data = pd.read_csv('r1_cumulant.csv')
# youdata = data.to_xarray()
# print(youdata)
# to_grib(youdata, 'out2.grib')

# element = data.variables['#名称'][:]
# youdata = np.array(element)

# ds2 = xr.DataArray(
#     youdata,                                #只接收array格式数据
#     coords=[
#         np.linspace( 12,15,1 ),    #目前cfgrib只接收np.linspace方法写入经纬度
#         np.linspace(13.345,23.342,1),      #第一个和第二个为范围第三个为份数            
#     ],        
#     dims=['latitude', 'longitude'],
#  ).to_dataset(name='skin_temperature')
# ds2.skin_temperature.attrs['GRIB_shortName'] = 'skt'
# to_grib(ds2, 'xxx.grib')

