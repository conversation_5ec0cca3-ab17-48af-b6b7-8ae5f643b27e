#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-01 15:45:57
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\custom_report_t.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-02 09:27:20
import json
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from sqlalchemy.databases import mysql


class Availability(user_Base):
    u'系统可用率'
    __tablename__ = "t_availability"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(VARCHAR(255), nullable=False,comment=u"电站描述")
    table_list = Column(mysql.MEDIUMTEXT, nullable=False,comment=u"表内容")
    date = Column(VARCHAR(255), nullable=False,comment=u"日期")
    station = Column(VARCHAR(50), nullable=False,comment=u"所属站")
    week = Column(CHAR(1), nullable=False,comment=u"周")
    user = Column(VARCHAR(10), nullable=True, comment=u"用户")
    day = Column(VARCHAR(10), nullable=True, comment=u"运行天数")
    type_ = Column(CHAR(1), nullable=True, comment=u"1可用率，2AGC")

    en_user = Column(VARCHAR(10), nullable=True, comment=u"英文用户")
    en_descr = Column(VARCHAR(255), nullable=False, comment=u"英文电站描述")
    en_table_list = Column(mysql.MEDIUMTEXT, nullable=False,comment=u"表内容-英文")
    en_date = Column(VARCHAR(255), nullable=False,comment=u"日期-英文")

       
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'descr':%s,'table_list':%s,'date':'%s','station':'%s','week':'%s','user':'%s','day':'%s','en_descr':'%s','en_user':'%s', 'en_table_list':'%s', 'type_':'%s'," \
               "'en_date':'%s'}" % (
            self.id, self.descr, self.table_list, self.date, self.station, self.week, self.user, self.day,
            self.en_descr, self.en_user, self.en_table_list, self.type_, self.en_date)
        return bean.replace("None", '')

    def deleteAlarm(self, id):
        try:
            user_session.query(Availability).filter(Availability.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}