# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/31 下午4:02
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : translate_exist_tables.py
# @Software : PyCharm
import json
import time

from LocaleTool.common import db_tool, redis_pool
from LocaleTool.local_db_tables import test_related_tables, related_tables, side_related_tables


def translate_exist_tables(related_tables):
    """
    1.将数据库中已有的数据的响应的中文字段翻译成英文，并写入新的"en_"开头的新字段中；
    注意：有的字段为可选字段，响应的值为空

    :return:
    """""
    for k, v in related_tables.items():
        print(20, f"正在翻译表：%s" % k)

        sql_count = f"select count(1) as count from {k}"
        res_count = db_tool.select_one(sql_count)
        print(21, f"表：{k} 中，有 {res_count['count']} 条数据")
        max_num = int(res_count['count'])

        for item in v:
            print(23, f"正在翻译表：{k} 的 {item} 字段")

            limit = 200
            offset = 0

            while offset <= max_num:

                sql = f"select id, {item} from {k} where {item} is not null and {item} not in ('--', '') and ({'en_' + item} is null or {'en_' + item} = '') limit {limit} offset {offset}"

                res = db_tool.select_many(sql)

                if res:
                    for i in res:
                        # 异步翻译
                        pdr_data = {'id': i['id'],
                                    'table': k,
                                    'update_data': {item: i[item]}}

                        pub_name = 'en_translate_pub'

                        try:
                            redis_pool.publish(pub_name, json.dumps(pdr_data))
                            print(50, f"发布成功：{pub_name}")
                        except Exception as e:
                            print(51, f"发布失败：{pub_name}，{e}")

                        print(25, f"正在翻译表：{k} 的 {item} 字段，id：{i['id']}")

                        time.sleep(0.1)

                else:
                    print(24, f"表：{k} 的 {item} 字段翻译完成！")
                    # continue

                offset += limit
                print(26, f"已翻译表：{k} 的 {item} 字段，offset：{offset}")


def api_translate_exist_tables():
    translate_exist_tables(related_tables)


if __name__ == '__main__':
    # translate_exist_tables(side_related_tables)
    translate_exist_tables(related_tables)