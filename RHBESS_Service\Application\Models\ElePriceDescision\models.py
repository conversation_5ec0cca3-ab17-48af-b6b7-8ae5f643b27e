# coding: utf-8
import datetime

from sqlalchemy import CHAR, Column, Date, DateTime, Float, ForeignKey, String, BigInteger, Integer, TEXT, SmallInteger
from sqlalchemy.dialects.mysql import BIGINT, INTEGER
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class CProvince(Base):
    __tablename__ = 'c_provinces'
    __table_args__ = {'comment': '省份集合,第一条为全国，不可更改'}

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(50), comment='名称')
    latitude = Column(Float(asdecimal=True), comment='纬度')
    longitude = Column(Float(asdecimal=True), comment='经度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')


class RPreCallWireDataShanxi(Base):
    __tablename__ = 'r_pre_call_wire_data_shanxi'
    __table_args__ = {'comment': '日前联络线计划信息（站点）'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='通道类型', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(Float(asdecimal=True), comment='电力值(MW)')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RPreNodePriceShanxi(Base):
    __tablename__ = 'r_pre_node_price_shanxi'
    __table_args__ = {'comment': '日前节点边际电价'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='机组名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    ele_price = Column(Float(asdecimal=True), comment='电能量价格(元/MWh)')
    block_price = Column(Float(asdecimal=True), comment='阻塞价格(元/MWh)')
    node = Column(String(100), comment='节点')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RPreSectionsBlockDataShanxi(Base):
    __tablename__ = 'r_pre_sections_block_data_shanxi'
    __table_args__ = {'comment': '日前输电断面约束及阻塞'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='断面名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(String(10), comment='是否越限')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RPreUnitCloseDataShanxi(Base):
    __tablename__ = 'r_pre_unit_close_data_shanxi'
    __table_args__ = {'comment': '日前必停机组'}

    # id = Column(BIGINT(20), primary_key=True)
    mem_name = Column(String(100), comment='市场成员名称', primary_key=True)
    unit_name = Column(String(100), comment='机组名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RPreUnitOpenDataShanxi(Base):
    __tablename__ = 'r_pre_unit_open_data_shanxi'
    __table_args__ = {'comment': '日前必开机组'}

    # id = Column(BIGINT(20), primary_key=True)
    mem_name = Column(String(100), comment='市场成员名称', primary_key=True)
    unit_name = Column(String(100), comment='机组名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RRealAccessEleShanxi(Base):
    __tablename__ = 'r_real_access_ele_shanxi'
    __table_args__ = {'comment': '重要通道实际输电情况'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(Float(asdecimal=True), comment='潮流(MW)')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RRealCallWireDataShanxi(Base):
    __tablename__ = 'r_real_call_wire_data_shanxi'
    __table_args__ = {'comment': '实时联络线计划（站点）'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='通道类型', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(Float(asdecimal=True), comment='电力值(MW)')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RRealNodePriceShanxi(Base):
    __tablename__ = 'r_real_node_price_shanxi'
    __table_args__ = {'comment': '实时节点边际电价'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='机组名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    ele_price = Column(Float(asdecimal=True), comment='电能量价格(元/MWh)')
    block_price = Column(Float(asdecimal=True), comment='阻塞价格(元/MWh)')
    node = Column(String(100), comment='节点')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RRealSectionsBlockDataShanxi(Base):
    __tablename__ = 'r_real_sections_block_data_shanxi'
    __table_args__ = {'comment': '实时输电断面约束及阻塞'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='断面名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(String(10), comment='是否越限')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RRealUnitPowerDataShanxi(Base):
    __tablename__ = 'r_real_unit_power_data_shanxi'
    __table_args__ = {'comment': '机组实时出力'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='机组名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(Float(asdecimal=True), comment='电力值')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class RSectionsShadowDataShanxi(Base):
    __tablename__ = 'r_sections_shadow_data_shanxi'
    __table_args__ = {'comment': '断面约束情况及影子价格'}

    # id = Column(BIGINT(20), primary_key=True)
    name = Column(String(100), comment='断面名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), comment='时点', primary_key=True)
    value = Column(Float(asdecimal=True), comment='阻塞价格(元/MWh)')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')


class CCity(Base):
    __tablename__ = 'c_citys'
    __table_args__ = {'comment': '城市集合'}

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(50), comment='名称')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id')
    latitude = Column(Float(asdecimal=True), comment='纬度')
    longitude = Column(Float(asdecimal=True), comment='经度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')

    province = relationship('CProvince')


class CDictNonSery(Base):
    __tablename__ = 'c_dict_non_series'
    __table_args__ = {'comment': '非时序配置'}

    id = Column(INTEGER(11), primary_key=True, comment='主键')
    name = Column(String(200), comment='分类名称')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    parent_id = Column(ForeignKey('c_dict_non_series.id'), index=True, comment='父id，自关联')
    table_name = Column(String(20), comment='表名或模型名')

    parent = relationship('CDictNonSery', remote_side=[id])
    province = relationship('CProvince')


class CDictSery(Base):
    __tablename__ = 'c_dict_series'
    __table_args__ = {'comment': '时序配置'}

    id = Column(INTEGER(11), primary_key=True, comment='主键')
    name = Column(String(100), comment='分类名称')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    parent_id = Column(ForeignKey('c_dict_series.id'), index=True, comment='父id，自关联')
    table_name = Column(String(20), comment='表名或模型名')

    parent = relationship('CDictSery', remote_side=[id])
    province = relationship('CProvince')


class CFile(Base):
    __tablename__ = 'c_files'
    __table_args__ = {'comment': '静态文件表'}

    id = Column(INTEGER(10), primary_key=True)
    file_name = Column(INTEGER(32), comment='文件名')
    file_url = Column(String(512), comment='文件路径')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='录入时间')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)
    descr = Column(String(200), comment='文件描述')

    province = relationship('CProvince')


class TAuthority(Base):
    __tablename__ = 't_authoritys'
    __table_args__ = {'comment': '静态路由表'}

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(50), comment='路由名称')
    icon = Column(String(50), comment='icon')
    is_btn = Column(String(50), comment='is_btn')
    url = Column(String(150), comment='路由地址')
    flag = Column(CHAR(1), comment='路由分类1web，0是小程序，默认1')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用1是0否，默认1')
    parent_id = Column(ForeignKey('t_authoritys.id'), nullable=True, index=True, comment='父id，自关联')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)

    parent = relationship('TAuthority', remote_side=[id])
    province = relationship('CProvince')


class TRole(Base):
    __tablename__ = 't_role'
    __table_args__ = {'comment': '角色表'}

    id = Column(INTEGER(10), primary_key=True, comment='主键')
    op_ts = Column(DateTime, comment='录入时间', default=datetime.datetime.now())
    name = Column(String(50), comment='角色名称')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)
    is_use = Column(CHAR(2), nullable=False, comment='是否启用1是0否，默认1')
    remark = Column(String(500), comment='角色说明')

    province = relationship('CProvince')


class CCounty(Base):
    __tablename__ = 'c_countys'
    __table_args__ = {'comment': '区县集合'}

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(50), comment='名称')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id')
    city_id = Column(ForeignKey('c_citys.id'), nullable=False, index=True)
    latitude = Column(Float(asdecimal=True), comment='维度')
    longitude = Column(Float(asdecimal=True), comment='经度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')

    city = relationship('CCity')
    province = relationship('CProvince')


class RNonSeriesDataShanxi(Base):
    __tablename__ = 'r_non_series_data_shanxi'
    __table_args__ = {'comment': '非时序数据'}

    # id = Column(BIGINT(20), primary_key=True, comment='主键')
    name = Column(INTEGER(10), comment='分类名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时点', primary_key=True)
    value1 = Column(String(100), comment='数值1')
    value2 = Column(String(50), comment='数值2')
    value3 = Column(String(100), comment='数值3')
    value4 = Column(String(200), comment='数值4')
    descr = Column(String(255), comment='描述')
    non_series_id = Column(ForeignKey('c_dict_non_series.id'), nullable=False, unique=True, comment='非时序分类id', primary_key=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='插入时间')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    index_type = Column(String(100), comment='类型')

    non_series = relationship('CDictNonSery')


class RNotSeriesNamesDataShanxi(Base):
    __tablename__ = 'c_dict_not_series_names'
    __table_args__ = {'comment': '非时序数据名称集合'}

    id = Column(BIGINT(20), primary_key=True, comment='主键')
    name = Column(String(200), comment='名称')
    non_series_id = Column(INTEGER(100), comment='非时序分类id')
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='插入时间')


class RSeriesDataShanxi(Base):
    __tablename__ = 'r_series_data_shanxi'
    __table_args__ = {'comment': '时序数据'}

    # id = Column(BIGINT(20), primary_key=True, comment='主键')
    name = Column(INTEGER(10), comment='分类名称', primary_key=True)
    day = Column(Date, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时点', primary_key=True)
    value1 = Column(Float(asdecimal=True), comment='数值1')
    value2 = Column(String(50), comment='数值2')
    value3 = Column(String(100), comment='数值3')
    series_id = Column(ForeignKey('c_dict_series.id'), nullable=False, unique=True, comment='时序分类id', primary_key=True)
    is_use = Column(CHAR(2), comment='是否使用1是0否默认1')
    op_ts = Column(DateTime, comment='插入时间')

    series = relationship('CDictSery')


class TRoleAuthority(Base):
    __tablename__ = 't_role_authority'
    __table_args__ = {'comment': '路由和角色关联表'}

    id = Column(BIGINT(20), primary_key=True)
    role_id = Column(ForeignKey('t_role.id'), nullable=False, index=True)
    authority_id = Column(ForeignKey('t_authoritys.id'), nullable=False, index=True)
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')

    authority = relationship('TAuthority')
    role = relationship('TRole')


class TUser(Base):
    __tablename__ = 't_users'
    __table_args__ = {'comment': '用户表'}

    id = Column(INTEGER(10), primary_key=True, comment='主键id')
    op_ts = Column(DateTime, comment='录入时间', default=datetime.datetime.now())
    name = Column(String(50), comment='用户名')
    account = Column(String(50), unique=True, comment='登录名')
    passwd = Column(String(100), comment='密码，MD5密文')
    phone_no = Column(String(20), unique=True, comment='联系方式')
    role_id = Column(ForeignKey('t_role.id'), nullable=True, index=True)
    sex = Column(CHAR(1), nullable=False, comment='性别，1男2女0保密')
    email = Column(String(50), comment='邮箱')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True)

    province = relationship('CProvince')
    user_roles = relationship('TUsersRole', back_populates='users')
    # role = relationship('TRole')


class RWeatherPre(Base):
    __tablename__ = 'r_weather_pre'
    __table_args__ = {'comment': '天气预测'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='创建时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class RWeatherReal(Base):
    __tablename__ = 'r_weather_real'
    __table_args__ = {'comment': '实时天气'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), nullable=False, index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    feel_tem = Column(Float(asdecimal=True), comment='体感温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    see = Column(Float(asdecimal=True), comment='能见度')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='录入时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class NeiMengGuRWeatherPre(Base):
    __tablename__ = 'r_weather_pre_neimenggu'
    __table_args__ = {'comment': '天气预测'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='创建时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class NeiMengGuRWeatherReal(Base):
    __tablename__ = 'r_weather_real_neimenggu'
    __table_args__ = {'comment': '实时天气'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), nullable=False, index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    feel_tem = Column(Float(asdecimal=True), comment='体感温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    see = Column(Float(asdecimal=True), comment='能见度')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='录入时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class ShanDongRWeatherPre(Base):
    __tablename__ = 'r_weather_pre_shandong'
    __table_args__ = {'comment': '天气预测'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='创建时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class ShanDongRWeatherReal(Base):
    __tablename__ = 'r_weather_real_shandong'
    __table_args__ = {'comment': '实时天气'}

    # id = Column(BIGINT(20), primary_key=True)
    day = Column(Date, nullable=False, comment='日期', primary_key=True)
    moment = Column(String(10), nullable=False, comment='时刻', primary_key=True)
    longitude = Column(Float(asdecimal=True), comment='经度')
    latitude = Column(Float(asdecimal=True), comment='维度')
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id', primary_key=True)
    city_id = Column(ForeignKey('c_citys.id'), nullable=False, index=True, primary_key=True)
    county_id = Column(ForeignKey('c_countys.id'), nullable=False, index=True, primary_key=True)
    tem = Column(Float(asdecimal=True), comment='温度')
    feel_tem = Column(Float(asdecimal=True), comment='体感温度')
    weather_con = Column(String(255), comment='天气状况')
    wind_angle = Column(String(50), comment='风向角')
    wind = Column(String(255), comment='风向')
    wind_power = Column(String(50), comment='风力等级')
    wind_speed = Column(Float(asdecimal=True), comment='风速')
    hum = Column(String(200), comment='相对湿度')
    rain_hour = Column(String(50), comment='小时累积降水量')
    rain_probab = Column(String(50), comment='降水概率')
    air_press = Column(String(50), comment='大气压强')
    see = Column(Float(asdecimal=True), comment='能见度')
    cloud_cover = Column(String(255), comment='云量')
    dew_tem = Column(Float(asdecimal=True), comment='露点温度')
    flag = Column(CHAR(2), nullable=False, comment='1:省份天气；2：市/区温度；3:县温度')
    is_use = Column(CHAR(2), nullable=False, comment='是否使用，1是0否，默认1')
    op_ts = Column(DateTime, comment='录入时间')

    city = relationship('CCity')
    county = relationship('CCounty')
    province = relationship('CProvince')


class TEvent(Base):
    __tablename__ = 't_event'
    __table_args__ = {'comment': '用户登录事件表'}

    id = Column(BigInteger, primary_key=True)
    op_ts = Column(DateTime, comment='录入时间')
    user_id = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    event = Column(String(16), comment='操作事件')
    IP = Column(String(16), comment='IP地址')

    user = relationship('TUser')

class ApplicationMarket(Base):
    __tablename__ = 't_application_market'
    __table_args__ = {'comment': '应用市场'}
    id = Column(BigInteger, primary_key=True)
    op_ts = Column(DateTime, comment='录入时间', default=datetime.datetime.now())
    name = Column(String(32), comment='应用市场名称')



class TModel(Base):
    __tablename__ = 't_model'
    __table_args__ = {'comment': '模型管理'}

    id = Column(BigInteger, primary_key=True)
    op_ts = Column(DateTime, comment='录入时间', default=datetime.datetime.now())
    update_time = Column(DateTime, comment='更新准确率时间', default=datetime.datetime.now())
    name = Column(String(32), comment='预测模型名称；同个省份下不允许重复')
    status = Column(Integer, comment=u"状态(0,关闭，1,启用)", default=1)
    accuracy_rate = Column(Float, comment='准确率', default=100)
    province_id = Column(ForeignKey('c_provinces.id'), nullable=False, index=True, comment='省份id')
    application_market_id = Column(ForeignKey('t_application_market.id'), nullable=False, index=True, comment='应用市场ID')
    create_user = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    is_use = Column(Integer, comment=u"状态(0,删除，1,启用)", default=1)
    province = relationship('CProvince')
    application_market = relationship('ApplicationMarket')
    user = relationship('TUser')



class ModelFile(Base):
    __tablename__ = 't_model_files'
    __table_args__ = {'comment': '模型附件'}
    id = Column(BigInteger, primary_key=True)
    op_ts = Column(Date, comment='录入时间', default=datetime.datetime.now())
    create_time = Column(DateTime, comment='创建时间', default=datetime.datetime.now())
    name = Column(String(128), comment='附件名称')
    file_path = Column(String(1024), comment='附件地址')
    is_use = Column(Integer, comment=u"状态(0,删除，1,启用)", default=1)
    model_id = Column(ForeignKey('t_model.id'))
    province_id = Column(ForeignKey('c_provinces.id'))
    type = Column(Integer, comment='附件类型：1,电价；2,预测模型；3,决策模型')
    price_data = Column(TEXT, comment=u"电价数据")

    model = relationship('TModel')
    province = relationship('CProvince')

class ModelUser(Base):
    __tablename__ = 't_model_user'
    __table_args__ = {'comment': '模型可见人'}
    id = Column(BigInteger, primary_key=True)
    user_id = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    model_id = Column(ForeignKey('t_model.id'), nullable=False, index=True)

    user = relationship('TUser')
    model = relationship('TModel')


class TMainBody(Base):
    __tablename__ = 't_mainbody'
    __table_args__ = {'comment': '主体管理'}

    id = Column(BigInteger, primary_key=True)
    op_ts = Column(DateTime, comment='录入时间', default=datetime.datetime.now())
    name = Column(String(32), comment='主体名称；全局唯一')
    create_user = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    is_use = Column(Integer, comment=u"状态(0,删除，1,启用)", default=1)
    power = Column(Float, comment="功率（MW）：数字输入，支持小数点两位；")
    capacity = Column(Float, comment="容量（MWh）：数字输入，支持小数点两位；")
    chag_efficiency = Column(Float, comment="充电效率（%）：数字输入，支持小数点两位；")
    disg_efficiency = Column(Float, comment="放电效率（%）：数字输入，支持小数点两位；")
    dod = Column(Float, comment="DOD（%）: 数字输入，支持小数点两位；")
    threshold_value = Column(Float, comment="充放阈值（元/MW）：数字输入，支持小数点两位；")

    user = relationship('TUser')


class TMainBodyUser(Base):
    __tablename__ = 't_mainbody_user'
    __table_args__ = {'comment': '主体可见人'}
    id = Column(BigInteger, primary_key=True)
    user_id = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    mainbody_id = Column(ForeignKey('t_mainbody.id'), nullable=False, index=True)

    user = relationship('TUser')
    model = relationship('TMainBody')


class TUsersRole(Base):
    __tablename__ = 't_users_role'
    __table_args__ = {'comment': '用户角色关联关系表'}

    id = Column(BIGINT(20), primary_key=True)
    role_id = Column(ForeignKey('t_role.id'), nullable=False, index=True)
    user_id = Column(ForeignKey('t_users.id'), nullable=False, index=True)
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')

    users = relationship('TUser')
    role = relationship('TRole')


class ProvinceCountCap(Base):
    __tablename__ = 't_province_count_cap'
    id = Column(Integer, primary_key=True)
    name = Column(String(255), default=None, comment='名称')
    day = Column(String(255), default=None, comment='日期年月')
    value = Column(String(255), default=None, comment='值')
    unit = Column(String(255), default=None, comment='瓦')
    province_id = Column(Integer, default=2, comment='外键id')
    type = Column(Integer, default=2, comment='类别，1装机2发电')
    city_id = Column(Integer, default=None, comment='城市id，外键')
    op_ts = Column(DateTime, default=None, onupdate=datetime.datetime.now, comment='录入时间')





