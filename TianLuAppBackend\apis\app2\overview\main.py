import datetime
from django.conf import settings
from dateutil.relativedelta import relativedelta
from django.db.models import Sum, Max, Q
from django.db import connections
from apis.user import models
from common.constant import EMPTY_STR_LIST
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
class IncomeMain(object):
    """
    收益信息
    """

    def day_income(self, datas):
        t_peak_income = 0
        t_demand_income = 0
        if datas:
            for data in datas:
                if hasattr(data, "peak_load_shifting"):
                    t_peak_income += data.peak_load_shifting
                else:
                    pass
                if hasattr(data, "demand_side_response"):
                    t_demand_income += data.demand_side_response
                else:
                    pass
        t_incomes = t_peak_income + t_demand_income

        return t_incomes

    def month_income(self, incomes_ins, subquery):
        monthly_income = incomes_ins.filter(id__in=subquery).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not monthly_income['total_peak']:
            monthly_income['total_peak'] = 0
        if not monthly_income['total_demand']:
            monthly_income['total_demand'] = 0
        total_month = monthly_income.get('total_peak', 0) + monthly_income.get('total_demand', 0)
        return total_month

    def year_income(self, incomes_ins, year_subquery):
        year_income = incomes_ins.filter(id__in=year_subquery).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not year_income['total_peak']:
            year_income['total_peak'] = 0
        if not year_income['total_demand']:
            year_income['total_demand'] = 0
        total_year = year_income['total_peak'] + year_income['total_demand']
        return total_year

    def income_res(self, station_obj, query_type):
        """
        实时收益
        query_type: 0：累计；1：日；2：月；3：年
        :param station_obj:
        :return:
        """

        todaty = datetime.date.today()

        detail_dic = {
            "yesterday": {  # 昨日收益
                "income": 0,  # 收益
                "chain": '--',  # 环比
                "year_on_year": '--',  # 同比
                "electricity_income": 0,  # 度电收益
                "target_income": 0,  # 目标收益
                "reach_productivity": 0,  # 达产率
            },
            "month": {  # 当月收益
                "income": 0,  # 收益
                "chain": '--',  # 环比
                "year_on_year": '--',  # 同比
                "electricity_income": 0,  # 度电收益
                "target_income": 0,  # 目标收益
                "reach_productivity": 0,  # 达产率
            },
            "year": {  # 当年收益
                "income": 0,  # 收益
                "chain": '--',  # 环比
                "year_on_year": '--',  # 同比
                "electricity_income": 0,  # 度电收益
                "target_income": 0,  # 目标收益
                "reach_productivity": 0,  # 达产率
            },
            "all": {  # 累计收益
                "income": 0,  # 收益
                "electricity_income": 0,  # 度电收益
                "target_income": 0,  # 目标收益
                "reach_productivity": 0,  # 达产率
            },
        }

        english_names = [-1]  # 防止查询失败
        for i in models.StationDetails.objects.filter(is_delete=0, master_station__in=station_obj):
            english_names.append(i.english_name)
        if query_type == 1:  # 昨日
            income_date = todaty - datetime.timedelta(days=1)  # 昨日
            y_day = (income_date - datetime.timedelta(days=1)).strftime('%Y-%m-%d')  # 前日
            y_year_date = (income_date - relativedelta(years=1)).strftime('%Y-%m-%d')
            income_date = income_date.strftime('%Y-%m-%d')
            income_sql = f"""SELECT
                                ele.day, ele.v_disg, incomes.day_income,  incomes.base_income
                            FROM
                                (  select 
                                   SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(day_income) as day_income, 
                                    sum(base_income) as base_income
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                    DAY in {(income_date, y_day, y_year_date)}
                                    AND station IN {tuple(english_names)}
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY 
                                ) AS incomes
                                INNER JOIN (
                                SELECT  SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(v_disg) as v_disg
                                FROM
                                    ads_report_chag_disg_union_1d 
                                WHERE
                                    DAY in {(income_date, y_day, y_year_date)}
                                    AND station IN {tuple(english_names)}
                                    AND station_type <= 1 
                                GROUP BY
                                DAY 
                                ORDER BY
                            DAY 
                                ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            income, y_income, y_year_income = 0, 0, 0  # 昨日、前日、去年
            for i in income_res:
                if i[0] == income_date:  # 昨日收益：
                    income = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    v_disg = i[1] if i[1] not in EMPTY_STR_LIST else 0  # 放电量
                    detail_dic['yesterday']['electricity_income'] = round(income / v_disg, 2) if income != 0 and v_disg != 0 else 0
                    base_income = i[3] if i[3] not in EMPTY_STR_LIST else 0  # 目标收益
                    detail_dic['yesterday']['target_income'] = round(base_income, 2)
                    detail_dic['yesterday']['reach_productivity'] = round(income / base_income * 100, 2) if base_income != 0 else 0

                elif i[0] == y_day:
                    y_income = round(i[2], 2) if i[2] not in EMPTY_STR_LIST else '--'
                else:
                    y_year_income = round(i[2], 2) if i[2] not in EMPTY_STR_LIST else '--'
            detail_dic["yesterday"]["income"] = round(income, 2)
            detail_dic["yesterday"]["chain"] = round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--'
            detail_dic["yesterday"]["year_on_year"] = round((income - y_year_income) / y_year_income * 100,
                                                            2) if y_year_income != 0 else '--'


        elif query_type == 2: # 当月
            todaty = datetime.date.today()
            y_month_start = (todaty - relativedelta(months=1)).strftime('%Y%m')  # 上个月
            chain_month_day_start = (todaty - relativedelta(years=1)).strftime('%Y%m')  # 去年今月
            now_month = todaty.strftime('%Y%m') # 当月
            income_sql = f"""SELECT
                                ele.day, ele.v_disg, incomes.income,  incomes.base_income
                            FROM
                                (  select 
                                   SUBSTRING( date_value, 1, 7 ) as DAY,
                                    sum(total_income) as income, 
                                    sum(base_income) as base_income
                                FROM
                                    ads_report_station_income_cw_cm_cy_tt 
                                WHERE
                                    date_value in {(now_month, y_month_start, chain_month_day_start)}
                                    AND station IN {tuple(english_names)}
                                    AND date_type = 'year_month'
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY 
                                ) AS incomes
                                INNER JOIN (
                                SELECT  SUBSTRING( date_value, 1, 7 ) as DAY,
                                    sum(v_disg) as v_disg
                                FROM
                                    ads_report_chag_disg_union_cw_cm_cq_cy 
                                WHERE
                                    date_value in {(now_month, y_month_start, chain_month_day_start)}
                                    AND station IN {tuple(english_names)}
                                    AND date_type = 'year_month'
                                    AND station_type <= 1 
                                GROUP BY
                                DAY 
                                ORDER BY
                            DAY 
                                ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()

            income, y_income, y_year_income = 0, 0, 0  # 当月、上一个月、去年
            for i in income_res:
                if i[0] == now_month:  # 月收益：
                    income = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    v_disg = i[1] if i[1] not in EMPTY_STR_LIST else 0  # 放电量
                    detail_dic['month']['electricity_income'] = round(income / v_disg, 2) if income != 0 and v_disg != 0 else 0
                    base_income = i[3] if i[3] not in EMPTY_STR_LIST else 0  # 目标收益
                    detail_dic['month']['target_income'] = round(base_income, 2)
                    detail_dic['month']['reach_productivity'] = round(income / base_income * 100, 2) if base_income != 0 else 0
                elif i[0] == y_month_start:
                    y_income = round(i[2], 2) if i[2] not in EMPTY_STR_LIST else '--'
                else:
                    y_year_income = round(i[2], 2) if i[2] not in EMPTY_STR_LIST else '--'
            detail_dic["month"]["income"] = round(income, 2)
            detail_dic["month"]["chain"] = round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--'
            detail_dic["month"]["year_on_year"] = round((income - y_year_income) / y_year_income * 100,
                                                            2) if y_year_income != 0 else '--'

        elif query_type == 3: # 当前年
            year_start = todaty.strftime('%Y')
            y_year_start = (todaty - relativedelta(years=1)).strftime('%Y')  # 去年起始时间

            income_sql = f"""SELECT
                                            ele.day, ele.v_disg, incomes.income,  incomes.base_income
                                        FROM
                                            (  select 
                                               SUBSTRING( date_value, 1, 5 ) as DAY,
                                                sum(total_income) as income, 
                                                sum(base_income) as base_income
                                            FROM
                                                ads_report_station_income_cw_cm_cy_tt 
                                            WHERE
                                                date_value in {(year_start, y_year_start)}
                                                AND station IN {tuple(english_names)}
                                                AND date_type = 'year'
                                            GROUP BY
                                            DAY 
                                            ORDER BY
                                            DAY 
                                            ) AS incomes
                                            INNER JOIN (
                                            SELECT  SUBSTRING( date_value, 1, 5 ) as DAY,
                                                sum(v_disg) as v_disg
                                            FROM
                                                ads_report_chag_disg_union_cw_cm_cq_cy 
                                            WHERE
                                                date_value in {(year_start, y_year_start)}
                                                AND station IN {tuple(english_names)}
                                                AND date_type = 'year'
                                                AND station_type <= 1 
                                            GROUP BY
                                            DAY 
                                            ORDER BY
                                        DAY 
                                            ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()

            income, y_incomee = 0, 0  # 当年、去年
            for i in income_res:
                if i[0] == year_start:  # 年收益：
                    income = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    v_disg = i[1] if i[1] not in EMPTY_STR_LIST else 0  # 放电量
                    detail_dic['year']['electricity_income'] = round(income / v_disg,
                                                                      2) if income != 0 and v_disg != 0 else 0
                    base_income = i[3] if i[3] not in EMPTY_STR_LIST else 0  # 目标收益
                    detail_dic['year']['target_income'] = round(base_income, 2)
                    detail_dic['year']['reach_productivity'] = round(income / base_income * 100,
                                                                      2) if base_income != 0 else 0
                else:
                    y_income = round(i[2], 2) if i[2] not in EMPTY_STR_LIST else '--'
            detail_dic["year"]["income"] = round(income, 2)
            detail_dic["year"]["chain"] = round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--'
            detail_dic["year"]["year_on_year"] = round((income - y_income) / y_income * 100,
                                                        2) if y_income != 0 else '--'

        else:  # 累积
            income_sql = f""" select 
                                date_value as DAY,
                                sum(total_income) as income, 
                                sum(base_income) as base_income
                            FROM
                                ads_report_station_income_cw_cm_cy_tt 
                            WHERE
                                date_value = '0'
                                AND station IN {tuple(english_names)}
                                AND date_type = 'total'
                            GROUP BY
                            DAY 
                            ORDER BY
                            DAY 
                            """
            disg_sql = f"""SELECT 
                            sum(v_disg) as v_disg
                        FROM
                            ads_report_chag_disg_union_cw_cm_cq_cy 
                        WHERE
                            station IN {tuple(english_names)}
                            AND date_type = 'year'
                            AND station_type <= 1 
                         """
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchone()
                ads_cursor.execute(disg_sql)
                disg_res = ads_cursor.fetchone()

            income = 0  # 累计收益
            base_income = 0  # 累计度电收益
            disg = 0  # 累计放电量
            if income_res:
                income = income_res[1] if income_res[1] not in EMPTY_STR_LIST else 0
                base_income = income_res[2] if income_res[2] not in EMPTY_STR_LIST else 0
            if disg_res:
                disg = disg_res[0] if disg_res[0] not in EMPTY_STR_LIST else 0
            detail_dic["all"]["income"] = round(income, 2)
            detail_dic["all"]["electricity_income"] = round(income / disg, 3) if disg != 0 else 0
            detail_dic["all"]["target_income"] = round(base_income, 2)
            detail_dic["all"][
                "reach_productivity"] = round(income / base_income * 100, 2) if base_income != 0 else 0

        return detail_dic

    def income_list(self, stations_ins, time_type):
        """
        收益展开列表
        :param stations_ins:
        :param time_type: 0：昨日收益；1：当月收益；2：当年收益；3：累计收益
        :return:
        """
        todaty = datetime.date.today()
        income_date = todaty
        slave_station = models.StationDetails.objects.filter(master_station__in=stations_ins).filter(
            is_delete=0).all()
        slave_station_res = {}
        slave_station_name_list = []
        english_names = [-1]  # 防止查询失败
        station_dict = {}
        slave_station_id_dict = {}
        for i in slave_station:
            _id = i.master_station_id
            name = i.english_name
            if i.slave != 0 and i.pack != 0:
                slave_station_name_list.append(name)
                if slave_station_res.get(_id):
                    slave_station_res[_id].append(name)
                else:
                    slave_station_res[_id] = [name]
            slave_station_id_dict[name] = _id
            english_names.append(name)
            master_name = i.master_station.name
            if station_dict.get(master_name):
                station_dict[master_name].append(i.english_name)
            else:
                station_dict[master_name] = [i.english_name]

        if time_type == '0':  # 昨日收益
            income_date = income_date - datetime.timedelta(days=1)
            y_day = (income_date - datetime.timedelta(days=1)).strftime('%Y-%m-%d')  # 前日
            y_year_date = (income_date - relativedelta(years=1)).strftime('%Y-%m-%d')

            income_date = income_date.strftime('%Y-%m-%d')
            income_sql = f"""SELECT
                                ele.day, ele.v_disg, incomes.day_income,  incomes.base_income, incomes.station
                            FROM
                                (  select 
                                   SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(day_income) as day_income, 
                                    sum(base_income) as base_income,
                                    station
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                    DAY in {(income_date, y_day, y_year_date)}
                                    AND station IN {tuple(english_names)}
                                GROUP BY
                                DAY, station
                                ) AS incomes
                                INNER JOIN (
                                SELECT  SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(v_disg) as v_disg,
                                    station
                                FROM
                                    ads_report_chag_disg_union_1d 
                                WHERE
                                    DAY in {(income_date, y_day, y_year_date)}
                                    AND station IN {tuple(english_names)}
                                    AND station_type <= 1 
                                GROUP BY
                                DAY, station
                             
                                ) AS ele ON ele.DAY = incomes.DAY AND ele.station = incomes.station ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            res = {}
            for i in income_res:
                if not res.get(i[4]):
                    res[i[4]] = {
                        'income':  0,  # 昨日收益
                        'y_income': 0,  # 前日收益
                        'y_year_income': 0,  # 去年收益
                        'base_income':  0,  # 目标收益
                        'disg': 0  # 放电量
                    }
                if i[0] == income_date:
                    res[i[4]]['income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    res[i[4]]['base_income'] = i[3] if i[3] not in EMPTY_STR_LIST else 0
                    res[i[4]]['disg'] = i[1] if i[1] not in EMPTY_STR_LIST else 0
                elif i[0] == y_day:
                    res[i[4]]['y_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                else:
                    res[i[4]]['y_year_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
            data = []
            for master, info in station_dict.items():
                income, y_income, y_year_income, target_income, disg = 0, 0, 0, 0, 0  # 昨日收益、前日收益、去年收益、目标收益、放电量
                for slave in info:
                    slave_info = res.get(slave)
                    if slave_info:
                        income += slave_info.get('income')
                        y_income += slave_info.get('y_income')
                        y_year_income += slave_info.get('y_year_income')
                        target_income += slave_info.get('base_income')
                        disg += slave_info.get('disg')
                d = {
                    'name': master,
                    'income': round(income, 2),
                    'year_on_year': round((income - y_year_income) / y_year_income * 100,
                                          2) if y_year_income != 0 else '--',
                    'chain': round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--',
                    'electricity_income': round(income / disg, 3) if disg != 0 else 0,
                    'target_income': round(target_income, 2) if target_income != 0 else 0,
                    'reach_productivity': round(income / target_income * 100, 2) if target_income != 0 else 0,
                }
                data.append(d)




        elif time_type == '1':  # 当月收益
            y_month_start = (todaty - relativedelta(months=1)).strftime('%Y%m')  # 上个月
            chain_month_day_start = (todaty - relativedelta(years=1)).strftime('%Y%m')  # 去年今月
            now_month = todaty.strftime('%Y%m') # 当月
            income_sql = f"""SELECT
                                ele.day, ele.v_disg, incomes.total_income,  incomes.base_income, incomes.station
                            FROM
                                (  select 
                                   SUBSTRING( date_value, 1, 7 ) as DAY,
                                    sum(total_income) as total_income, 
                                    sum(base_income) as base_income,
                                    station
                                FROM
                                    ads_report_station_income_cw_cm_cy_tt 
                                WHERE
                                    date_value in {(now_month, y_month_start, chain_month_day_start)}
                                    AND station IN {tuple(english_names)}
                                    AND date_type = 'year_month'
                                GROUP BY
                                DAY, station
                                ) AS incomes
                                INNER JOIN (
                                SELECT  SUBSTRING( date_value, 1, 7 ) as DAY,
                                    sum(v_disg) as v_disg,
                                    station
                                FROM
                                    ads_report_chag_disg_union_cw_cm_cq_cy 
                                WHERE
                                    date_value in {(now_month, y_month_start, chain_month_day_start)}
                                    AND station IN {tuple(english_names)}
                                    AND date_type = 'year_month'
                                    AND station_type <= 1 
                                GROUP BY
                                DAY, station

                                ) AS ele ON ele.DAY = incomes.DAY AND ele.station = incomes.station ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            res = {}
            for i in income_res:
                if not res.get(i[4]):
                    res[i[4]] = {
                        'income': 0,  # 当月收益
                        'y_income': 0,  # 上一个月收益
                        'y_year_income': 0,  # 去年当月收益
                        'base_income': 0,  # 目标收益
                        'disg': 0  # 放电量
                    }
                if i[0] == now_month:
                    res[i[4]]['income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    res[i[4]]['base_income'] = i[3] if i[3] not in EMPTY_STR_LIST else 0
                    res[i[4]]['disg'] = i[1] if i[1] not in EMPTY_STR_LIST else 0
                elif i[0] == y_month_start:
                    res[i[4]]['y_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                else:
                    res[i[4]]['y_year_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
            data = []
            for master, info in station_dict.items():
                income, y_income, y_year_income, target_income, disg = 0, 0, 0, 0, 0  # 当月收益、上一个收益、去年当月收益、目标收益、放电量
                for slave in info:
                    slave_info = res.get(slave)
                    if slave_info:
                        income += slave_info.get('income')
                        y_income += slave_info.get('y_income')
                        y_year_income += slave_info.get('y_year_income')
                        target_income += slave_info.get('base_income')
                        disg += slave_info.get('disg')
                d = {
                    'name': master,
                    'income': round(income, 2),
                    'year_on_year': round((income - y_year_income) / y_year_income * 100,
                                          2) if y_year_income != 0 else '--',
                    'chain': round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--',
                    'electricity_income': round(income / disg, 3) if disg != 0 else 0,
                    'target_income': round(target_income, 2) if target_income != 0 else 0,
                    'reach_productivity': round(income / target_income * 100, 2) if target_income != 0 else 0,
                }
                data.append(d)


        elif time_type == '2':
            year_start = todaty.strftime('%Y')
            y_year_start = (todaty - relativedelta(years=1)).strftime('%Y')  # 去年起始时间

            income_sql = f"""SELECT
                                 ele.day, ele.v_disg, incomes.total_income,  incomes.base_income, incomes.station
                             FROM
                                 (  select 
                                    SUBSTRING( date_value, 1, 5 ) as DAY,
                                     sum(total_income) as total_income, 
                                     sum(base_income) as base_income,
                                     station
                                 FROM
                                     ads_report_station_income_cw_cm_cy_tt 
                                 WHERE
                                     date_value in {(year_start, y_year_start)}
                                     AND station IN {tuple(english_names)}
                                     AND date_type = 'year'
                                 GROUP BY
                                 DAY, station
                                 ) AS incomes
                                 INNER JOIN (
                                 SELECT  SUBSTRING( date_value, 1, 5 ) as DAY,
                                     sum(v_disg) as v_disg,
                                     station
                                 FROM
                                     ads_report_chag_disg_union_cw_cm_cq_cy 
                                 WHERE
                                     date_value in {(year_start, y_year_start)}
                                     AND station IN {tuple(english_names)}
                                     AND date_type = 'year'
                                     AND station_type <= 1 
                                 GROUP BY
                                 DAY, station

                                 ) AS ele ON ele.DAY = incomes.DAY AND ele.station = incomes.station ORDER BY incomes.day"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            res = {}
            for i in income_res:
                if not res.get(i[4]):
                    res[i[4]] = {
                        'income': 0,  # 当年收益
                        'y_income': 0,  # 去年收益
                        'y_year_income': 0,  # 去年收益
                        'base_income': 0,  # 目标收益
                        'disg': 0  # 放电量
                    }
                if i[0] == year_start:
                    res[i[4]]['income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    res[i[4]]['base_income'] = i[3] if i[3] not in EMPTY_STR_LIST else 0
                    res[i[4]]['disg'] = i[1] if i[1] not in EMPTY_STR_LIST else 0
                else:
                    res[i[4]]['y_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
                    res[i[4]]['y_year_income'] = i[2] if i[2] not in EMPTY_STR_LIST else 0
            data = []
            for master, info in station_dict.items():
                income, y_income, y_year_income, target_income, disg = 0, 0, 0, 0, 0  # 当年收益、去年收益、去年收益、目标收益、放电量
                for slave in info:
                    slave_info = res.get(slave)
                    if slave_info:
                        income += slave_info.get('income')
                        y_income += slave_info.get('y_income')
                        y_year_income += slave_info.get('y_year_income')
                        target_income += slave_info.get('base_income')
                        disg += slave_info.get('disg')
                d = {
                    'name': master,
                    'income': round(income, 2),
                    'year_on_year': round((income - y_year_income) / y_year_income * 100,
                                          2) if y_year_income != 0 else '--',
                    'chain': round((income - y_income) / y_income * 100, 2) if y_income != 0 else '--',
                    'electricity_income': round(income / disg, 3) if disg != 0 else 0,
                    'target_income': round(target_income, 2) if target_income != 0 else 0,
                    'reach_productivity': round(income / target_income * 100, 2) if target_income != 0 else 0,
                }
                data.append(d)

        else:
            income_sql = f""" select 
                                           date_value as DAY,
                                           sum(total_income) as income, 
                                           sum(base_income) as base_income,
                                           station
                                       FROM
                                           ads_report_station_income_cw_cm_cy_tt 
                                       WHERE
                                           date_value = '0'
                                           AND station IN {tuple(english_names)}
                                           AND date_type = 'total'
                                       GROUP BY
                                       DAY , station
                                       """
            disg_sql = f"""SELECT 
                                       sum(v_disg) as v_disg, station
                                   FROM
                                       ads_report_chag_disg_union_cw_cm_cq_cy 
                                   WHERE
                                       station IN {tuple(english_names)}
                                       AND date_type = 'year'
                                       AND station_type <= 1 
                                   GROUP BY
                                    station """

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
                ads_cursor.execute(disg_sql)
                disg_res = ads_cursor.fetchall()

            res = {}
            for i in income_res:
                if not res.get(i[3]):
                    res[i[3]] = {
                        'income': i[1] if i[1] not in EMPTY_STR_LIST else 0,  # 累计收益
                        'base_income': i[2] if i[2] not in EMPTY_STR_LIST else 0,  # 目标收益
                        'disg': 0  # 放电量
                    }
            for i in disg_res:
                if res.get(i[1]):
                    res[i[1]]['disg'] = i[0] if i[0] not in EMPTY_STR_LIST else 0

            data = []
            for master, info in station_dict.items():
                income, target_income, disg = 0, 0, 0,  # 当年收益、目标收益、放电量
                for slave in info:
                    slave_info = res.get(slave)
                    if slave_info:
                        income += slave_info.get('income')
                        target_income += slave_info.get('base_income')
                        disg += slave_info.get('disg')
                d = {
                    'name': master,
                    'income': round(income, 2),
                    'electricity_income': round(income / disg, 3) if disg != 0 else 0,
                    'target_income': round(target_income, 2) if target_income != 0 else 0,
                    'reach_productivity': round(income / target_income * 100, 2) if target_income != 0 else 0,
                }
                data.append(d)


        return data
