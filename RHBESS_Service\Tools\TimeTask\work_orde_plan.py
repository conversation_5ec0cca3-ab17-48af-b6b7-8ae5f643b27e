#!/usr/bin/env python
# coding=utf-8
#@Information: 工单定时任务
#<AUTHOR> WYJ
#@Date         : 2023-02-13 09:42:34
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\work_orde_plan.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-16 16:00:53

import sys,os,time
from sqlalchemy.sql import or_,and_
from Tools.Utils.num_utils import num_retain
from Tools.Utils.time_utils import timeUtils 
from Tools.DB.mysql_user import user_session
from Tools.Utils.send_mail import sendMail_
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Application.Models.WorkOrder.dispatch_step_t import DispatchStep
from Application.Models.WorkOrder.dispatch_r import DispatchR
from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
from Application.Models.User.user import User
from Application.Models.WorkOrder.dispatch_plan import DispatchPlan
import pandas as pd
# sys.setdefaultencoding("utf-8")
from timedtask_log import app_log
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)
import numpy as np

from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()
day_int,week_int,month_int,year_int,main_,send_ = None,None,None,None,None,None


def calculation(**bean):
    uid = bean['create_user']
    oid = bean['oid']
    dispatch_type = bean['dispatch_type']
    db = bean['db']
    handle_user = bean['handle_user']
    first_apply_user = bean['first_apply_user']
    second_apply_user = bean['second_apply_user']
    finall_apply_user = bean['finall_apply_user']
    copy_users = str(bean['copy_users'])
    examine_time = bean['examine_time']+':00'
    p_time = bean['plan_time']  # 持续时长
    content = bean['content']  # 设定内容
    device = ';'.join(bean['device'])  # 设备
    now_ = timeUtils.getNewTimeStr()
    working_no = 'PD%s'%(now_.replace('-','').replace(':','').replace(' ',''))  # 工单号
    start_time = now_[:10]+" "+examine_time
    e = timeUtils.timeStrToTamp(start_time)+float(p_time)*3600  # 预计结束时间
    plan_time = timeUtils.ssTtimes(e)


    model = DispatchModel(descr=u'设备%s巡检工作流程'%device,op_ts=now_,create_user=uid,organization_id=oid,dispatch_type=dispatch_type,working_flag=3)
    user_session.add(model)
    user_session.commit()
    dstep1 = DispatchStep(model_id=model.id,descr=u'准备工作内容',content=content,is_team=0 ,handle_users=handle_user,stage=1)  #准备工作内容
    user_session.add(dstep1)
    user_session.commit()
    dstep2 = DispatchStep(model_id=model.id,descr=u'准备工作初步审核',content=u'请仔细核对现场准备工作是否符合进场操作规范，或注意事项',is_team= 0,check_user=first_apply_user,pre_step=dstep1.id,stage=1)  # 准备工作审核第一步
    user_session.add(dstep2)
    user_session.commit()
    dstep3 = DispatchStep(model_id=model.id,descr=u'准备工作最终审核',content=u'核对是否符合进场操作规范',is_team= 0,check_user=second_apply_user,pre_step=dstep2.id,stage=1)  # 准备工作最终审核
    user_session.add(dstep3)
    user_session.commit()
    dstep4 = DispatchStep(model_id=model.id,descr=u'此次工作汇总',content=u'请填写或上传工作汇总相关资料',is_team= 0,handle_users=handle_user,pre_step=dstep3.id,stage=2)  # 工作汇总内容提交
    user_session.add(dstep4)
    user_session.commit()
    dstep5 = DispatchStep(model_id=model.id,descr=u'工作总结审核',content=u'请对此次工作总结进行审核',is_team=0,check_user=finall_apply_user,pre_step=dstep4.id,stage=2)  # 工作汇总审核
    user_session.add(dstep5)
    user_session.commit()
    dstep1.next_step=dstep2.id
    dstep2.next_step=dstep3.id
    dstep3.next_step=dstep4.id
    dstep4.next_step=dstep5.id
    
    user_session.commit()
    rd = DispatchR(descr=u'设备%s巡检工作流程记录'%device,op_ts=now_,model_id=model.id,start_time=start_time,plan_time=plan_time,create_user=uid,content=u'设备%s巡检流程记录内容'%device,station=db,
    working_no=working_no,working_flag=3,status=dstep1.id,copy_users=copy_users)
    user_session.add(rd)
    user_session.commit()
    rdr = DispatchStepR(dispatch_id=rd.id,status=1,send_time=now_,dispatch_step_id=dstep1.id,stage=1)
    user_session.add(rdr)
    user_session.commit()
    rd.status=rdr.id
    user_session.commit()
    emails = []
    HUS = user_session.query(User).filter(User.id==handle_user,User.unregister==1).first()
    emails.append(HUS.email)
    app_log.info('send users is %s'%emails)
    sendMail_("您有新的待处理工单，请及时登录系统处理","工单处理消息通知","RHBESS","XXX",emails)
    
    
def RunClearFileAndData():
    send_eml_objs = {}
    plans = user_session.query(DispatchPlan).filter(DispatchPlan.is_use==1).all()
    now_ = timeUtils.getNewTimeStr()
    now = now_[:10]
    app_log.info('%s--我被重启了 新的配置任务 %s'%(now_,len(plans)))
    nowSecs = timeUtils.nowSecs()  # 当前绝对秒
    for plan in plans:
        send_eml_days = []
        start_time = str(plan.start_time)[:10]  # 任务开始时间
        end_time = str(plan.end_time)[:10]  # 任务结束时间
        ex_hour = int(plan.examine_time[:2])  # 获取整点指定
        ex_minu = int(plan.examine_time[3:5])  # 获取整分
        repet_week = eval(plan.repet_week)
        obj = {"first_apply_user":plan.first_apply_user,"second_apply_user":plan.second_apply_user,"handle_user":plan.handle_user,'device':plan.device,
               "finall_apply_user":plan.finall_apply_user,'copy_users':plan.copy_users,'dispatch_type':plan.dispatch_type,'db':plan.station,
               'create_user':plan.create_user,'oid':plan.organization_id,'plan_time':plan.plan_time,'examine_time':plan.examine_time,'content':plan.content}

        if now>end_time and plan.is_use==1:  # 截止时间在当前时间之前 且在生效中
            plan.is_use = 0
        else:
            if plan.repet == 1 :  # 按天重复
                m,d = [],[]
                date_range = pd.date_range(start=start_time, end=end_time, freq='%sD'%(plan.repet_interval))
                days = date_range.array
                for dd in days:
                    send_eml_days.append(str(dd))
                    m.append(int(str(dd)[5:7]))
                    d.append(int(str(dd)[8:10]))
                month_ = str(list(set(m)))[1:-1]
                day_ = str(list(set(d)))[1:-1]
                day_int = scheduler.add_job(calculation, 'cron', month =month_,day=day_,hour=ex_hour, minute=ex_minu,start_date=start_time,end_date=end_time,kwargs=obj)  # 间隔执行
            elif plan.repet == 2 and repet_week:  # 按周重复 （完成）
                arr = str((np.array(repet_week)-1).tolist())[1:-1].replace(' ','')  # 获取周 1,2,3
                date_range = pd.date_range(start=start_time, end=end_time, freq='2D')  # 按周重复，默认是查找2天间隔
                days = date_range.array
                for dd in days:
                    send_eml_days.append(str(dd))

                week_int = scheduler.add_job(calculation, 'cron', day_of_week=arr, hour=ex_hour, minute=ex_minu,start_date=start_time,end_date=end_time,kwargs=obj)
            elif plan.repet == 3:  # 按月重复
                m,d = [],[]
                date_range = pd.date_range(start=start_time, end=end_time, freq='%sM'%(plan.repet_interval))
                dd = date_range.array
                for i in dd:
                    send_eml_days.append(str(i))
                    if timeUtils.timeStrToTamp(str(i))>=nowSecs:
                        m=int(str(i)[5:7])
                        d=int(str(i)[8:10])
                    # month_ = str(list(set(m)))[1:-1]
                    # day_ = str(list(set(d)))[1:-1]
                        month_int = scheduler.add_job(calculation, 'cron', month =m,day=d,hour=ex_hour, minute=ex_minu,start_date=start_time,end_date=end_time,kwargs=obj)
            elif plan.repet == 4:  # 按年重复
                y,m,d = [],[],[]
                date_range = pd.date_range(start=start_time, end=end_time, freq='%sY'%(plan.repet_interval))
                days = date_range.array
                for dd in days:
                    send_eml_days.append(str(dd))
                    y.append(int(str(dd)[:4]))
                    m.append(int(str(dd)[5:7]))
                    d.append(int(str(dd)[8:10]))
                year_ = str(list(set(y)))[1:-1]
                month_ = str(list(set(m)))[1:-1]
                day_ = str(list(set(d)))[1:-1]
                year_int = scheduler.add_job(calculation, 'cron', year =year_,month =month_,day=day_,hour=ex_hour, minute=ex_minu,start_date=start_time,end_date=end_time,kwargs=obj)
        send_eml_objs[plan.id] = send_eml_days
   
    # main_ = scheduler.add_job(main_hand, 'interval', seconds=5)  # 每 15分钟执行一次
   
    main_ = scheduler.add_job(main_hand, 'cron', day_of_week='6', hour=23, minute=10,id='main_')  # 每个周日的23点10分重启一下任务
    send_= scheduler.add_job(send_email, 'cron', hour='8', minute='15', args=[send_eml_objs])  # 每天8点15执行一次
    # send_= scheduler.add_job(send_email, 'interval', seconds=5, args=[send_eml_objs])  # 每天8点15执行一次
    
    scheduler.start()
    user_session.commit()

def send_email(bean):
    now_time = timeUtils.getNewTimeStr()  # 当前时间
    for key,values in bean.items():
        if values: 
            for v in values:
                nt = timeUtils.timeSeconds(now_time,v)  # 计算时间差 得到秒值
                if nt>0 and nt<=86400*3:  # 三天
                    user = user_session.query(User).filter(User.id==DispatchPlan.handle_user,DispatchPlan.id==key,User.unregister==1).first()
                    app_log.info('send users is %s'%user.email)
                    sendMail_("您有新的待处理工单，请及时登录系统处理","工单处理消息通知","RHBESS","XXX",[user.email])
                    break;
            


def main_hand():
    app_log.info('dinsghi restart is :%s'%timeUtils.getNewTimeStr())
    
    day_int.remove()  # 关闭指定任务
    week_int.remove()
    month_int.remove()
    year_int.remove()
    main_.remove()
    send_.remove()
   
    RunClearFileAndData()


if __name__ == '__main__':
    RunClearFileAndData()
   
    

   
   
   
    



