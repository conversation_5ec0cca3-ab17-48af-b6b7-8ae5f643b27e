package com.robestec.analysis.dto.stationr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 电站关系创建DTO
 */
@Data
@ApiModel("电站关系创建DTO")
public class StationRCreateDTO {

    @ApiModelProperty(value = "电站名称", required = true)
    @NotBlank(message = "电站名称不能为空")
    private String stationName;

    @ApiModelProperty(value = "电功率")
    private Double electricPower;

    @ApiModelProperty(value = "运行状态")
    private String runningState;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "储能")
    private String energyStorage;

    @ApiModelProperty(value = "有功功率名称(JSON格式)")
    private String activePowerName;

    @ApiModelProperty(value = "充电容量名称")
    private String chagCapacityName;

    @ApiModelProperty(value = "放电容量名称")
    private String disgCapacityName;

    @ApiModelProperty(value = "电池簇")
    private String batteryCluster;

    @ApiModelProperty(value = "放电容量")
    private String disgCapy;

    @ApiModelProperty(value = "充电容量")
    private String chagCapy;

    @ApiModelProperty(value = "SOC")
    private String soc;

    @ApiModelProperty(value = "监控")
    private String monitor;
}
