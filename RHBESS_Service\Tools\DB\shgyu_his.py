#!/usr/bin/env python
# coding=utf-8


from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")

SHGYU_HOSTNAME = model_config.get('mysql', "SHGYU_HOSTNAME")
SHGYU_PORT = model_config.get('mysql', "SHGYU_PORT")

SHGYU_HOSTNAMES = model_config.get('mysql', "SHGYU_HOSTNAMES")
SHGYU_PORTS = model_config.get('mysql', "SHGYU_PORTS")

SSHGYUDATABASE = model_config.get('mysql', "SSHGYUDATABASE")
SHGYUDATABASE = model_config.get('mysql', "SHGYUDATABASE")
SHGYU1_DATABASE = model_config.get('mysql', "SHGYU1_DATABASE")
SHGYU2_DATABASE = model_config.get('mysql', "SHGYU2_DATABASE")
SHGYU3_DATABASE = model_config.get('mysql', "SHGYU3_DATABASE")
SHGYU4_DATABASE = model_config.get('mysql', "SHGYU4_DATABASE")

SHGYU_USERNAME = model_config.get('mysql', "SHGYU_USERNAME")
SHGYU_PASSWORD = model_config.get('mysql', "SHGYU_PASSWORD")

SHGYU_USERNAMES = model_config.get('mysql', "SHGYU_USERNAMES")
SHGYU_PASSWORDS = model_config.get('mysql', "SHGYU_PASSWORDS")


shisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAMES,
    SHGYU_PASSWORDS,
    SHGYU_HOSTNAMES,
    SHGYU_PORTS,
    SSHGYUDATABASE
)
sshgyu_engine = create_engine(shisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sshgyu_session = scoped_session(sessionmaker(sshgyu_engine,autoflush=True))
sshgyu_Base = declarative_base(sshgyu_engine)
sshgyu_session = _sshgyu_session()


hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAME,
    SHGYU_PASSWORD,
    SHGYU_HOSTNAME,
    SHGYU_PORT,
    SHGYUDATABASE
)
shgyu_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shgyu_session = scoped_session(sessionmaker(shgyu_engine,autoflush=True))
shgyu_Base = declarative_base(shgyu_engine)
shgyu_session = _shgyu_session()


hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAME,
    SHGYU_PASSWORD,
    SHGYU_HOSTNAME,
    SHGYU_PORT,
    SHGYU1_DATABASE
)
shgyu1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shgyu1_session = scoped_session(sessionmaker(shgyu1_engine,autoflush=True))
shgyu1_Base = declarative_base(shgyu1_engine)
shgyu1_session = _shgyu1_session()


hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAME,
    SHGYU_PASSWORD,
    SHGYU_HOSTNAME,
    SHGYU_PORT,
    SHGYU2_DATABASE
)
shgyu2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shgyu2_session = scoped_session(sessionmaker(shgyu2_engine,autoflush=True))
shgyu2_Base = declarative_base(shgyu2_engine)
shgyu2_session = _shgyu2_session()


hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAME,
    SHGYU_PASSWORD,
    SHGYU_HOSTNAME,
    SHGYU_PORT,
    SHGYU3_DATABASE
)
shgyu3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shgyu3_session = scoped_session(sessionmaker(shgyu3_engine,autoflush=True))
shgyu3_Base = declarative_base(shgyu3_engine)
shgyu3_session = _shgyu3_session()


hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHGYU_USERNAME,
    SHGYU_PASSWORD,
    SHGYU_HOSTNAME,
    SHGYU_PORT,
    SHGYU4_DATABASE
)
shgyu4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shgyu4_session = scoped_session(sessionmaker(shgyu4_engine,autoflush=True))
shgyu4_Base = declarative_base(shgyu4_engine)
shgyu4_session = _shgyu4_session()




