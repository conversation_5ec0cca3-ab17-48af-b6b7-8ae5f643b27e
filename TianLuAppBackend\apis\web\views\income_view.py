import datetime
import concurrent.futures
from rest_framework.response import Response
from django.db.models import Max, Sum
from rest_framework.views import APIView
from apis.user import models
from common import common_response_code
from middlewares.authentications import JWTHeaderAuthentication, DenyAuthentication, JwtParamAuthentication
from decimal import Decimal

from tools.day_hours_used import NewDayHourUsed


class UnitCapacityIncomeView(APIView):
    """Web单位容量收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def _get_station_income(self, master_station):
        month_general_income = []
        last_month_general_income = []
        capacity_count_this_month = []
        capacity_count_last_month = []

        income_date = datetime.date.today()
        incomes_ins = models.StationIncome.objects.filter(master_station=master_station)

        slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
        rated_capacity_sum = sum([Decimal(station.rated_capacity) for station in slave_stations])

        # 本月收益
        this_month_first_day = income_date.replace(day=1)
        in_this_month = models.StationIncome.objects.filter(
            master_station=master_station, income_date__range=(this_month_first_day, income_date)
        ).exists()
        if in_this_month:
            capacity_count_this_month.append(rated_capacity_sum)
        subquery_ = (
            incomes_ins.filter(income_date__range=(this_month_first_day, income_date))
            .values('income_date')
            .annotate(last_entry=Max('id'))
            .values('last_entry')
        )
        monthly_income_ = incomes_ins.filter(id__in=subquery_).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not monthly_income_['total_peak']:
            monthly_income_['total_peak'] = 0
        if not monthly_income_['total_demand']:
            monthly_income_['total_demand'] = 0
        total_month_ = monthly_income_.get('total_peak', 0) + monthly_income_.get('total_demand', 0)
        month_general_income.append(total_month_)

        # 上月收益
        last_month_last_day = income_date.replace(day=1) - datetime.timedelta(days=1)
        this_month = income_date.month
        if this_month == 1:  # 当年1月
            last_month_first_day = datetime.date(income_date.year, income_date.month, 1) - datetime.timedelta(31)
        else:
            last_month_first_day = income_date.replace(month=this_month - 1, day=1)
        in_last_month = models.StationIncome.objects.filter(
            master_station=master_station, income_date__range=(last_month_first_day, last_month_last_day)
        ).exists()
        if in_last_month:
            capacity_count_last_month.append(rated_capacity_sum)
        subquery_ = (
            incomes_ins.filter(income_date__range=(last_month_first_day, last_month_last_day))
            .values('income_date')
            .annotate(last_entry=Max('id'))
            .values('last_entry')
        )
        monthly_income_ = incomes_ins.filter(id__in=subquery_).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not monthly_income_['total_peak']:
            monthly_income_['total_peak'] = 0
        if not monthly_income_['total_demand']:
            monthly_income_['total_demand'] = 0
        total_month_ = monthly_income_.get('total_peak', 0) + monthly_income_.get('total_demand', 0)
        last_month_general_income.append(total_month_)

        return {
            'month_general_income': month_general_income,
            'last_month_general_income': last_month_general_income,
            'capacity_count_this_month': capacity_count_this_month,
            'capacity_count_last_month': capacity_count_last_month
        }

    def get(self, request):
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
        master_stations = models.MaterStation.objects.filter(project__in=project_ins, is_delete=0)

        detail_dic = {}
        month_general_income = []
        last_month_general_income = []
        capacity_count_this_month = []
        capacity_count_last_month = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = list()
            for station in master_stations:
                future = executor.submit(self._get_station_income, station)
                futures.append(future)
            # results = [f.result() for f in concurrent.futures.as_completed(futures)]
            for i in concurrent.futures.as_completed(futures):
                month_general_income += i.result()['month_general_income']
                last_month_general_income += i.result()['last_month_general_income']
                capacity_count_this_month += i.result()['capacity_count_this_month']
                capacity_count_last_month += i.result()['capacity_count_last_month']

        month_general_incomes = sum(month_general_income)
        last_month_general_incomes = sum(last_month_general_income)
        count_this_month = sum(capacity_count_this_month)
        count_last_month = sum(capacity_count_last_month)

        try:
            detail_dic["this_month_unit_income"] = Decimal(Decimal(month_general_incomes) / (Decimal(count_this_month))).quantize(
                Decimal("0.0")
            )
        except Exception as e:
            detail_dic["this_month_unit_income"] = 0
        try:
            detail_dic["last_month_unit_income"] = Decimal(
                Decimal(last_month_general_incomes) / (Decimal(count_last_month))
            ).quantize(Decimal("0.0"))
        except Exception as e:
            detail_dic["last_month_unit_income"] = 0
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail_dic,
                },
            }
        )


class WebIncomeView(APIView):
    """Web大屏收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def post(self, request):
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
        # stations_ins = models.StationDetails.objects.filter(project__user=user_ins, project__in=project_ins)
        master_stations = models.MaterStation.objects.filter(project__in=project_ins, is_delete=0).all()
        income_date = datetime.date.today()
        detail_dic = {
            "today_general_income": 0,
            "month_general_income": 0,
            "year_general_income": 0,
            "all_general_income": 0,
        }  # 今日总收益  # 本月总收益  # 本年总收益  # 历史总收益
        today_general_income = []
        year_general_income = []
        month_general_income = []
        all_general_income = []
        for station in master_stations:
            incomes_ins = models.StationIncome.objects.filter(master_station=station)

            # 日总收益
            t_peak = incomes_ins.filter(income_date=income_date).order_by("id").first()
            t_peak_income = 0
            t_demand_income = 0

            if t_peak:
                if hasattr(t_peak, "peak_load_shifting"):
                    t_peak_income = t_peak.peak_load_shifting
                else:
                    t_peak_income = 0
                if hasattr(t_peak, "demand_side_response"):
                    t_demand_income = t_peak.demand_side_response
                else:
                    t_demand_income = 0
            t_incomes = t_peak_income + t_demand_income
            today_general_income.append(t_incomes)
            # 月收益
            first_day = income_date.replace(day=1)

            subquery = (
                incomes_ins.filter(income_date__range=(first_day, income_date))
                .values('income_date')
                .annotate(last_entry=Max('id'))
                .values('last_entry')
            )
            monthly_income = incomes_ins.filter(id__in=subquery).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0
            total_month = monthly_income.get('total_peak', 0) + monthly_income.get('total_demand', 0)
            month_general_income.append(total_month)
            # 年收益
            year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()

            year_subquery = (
                incomes_ins.filter(income_date__range=(year_start, income_date))
                .values('income_date')
                .annotate(last_entry=Max('id'))
                .values('last_entry')
            )

            year_income = incomes_ins.filter(id__in=year_subquery).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not year_income['total_peak']:
                year_income['total_peak'] = 0
            if not year_income['total_demand']:
                year_income['total_demand'] = 0
            total_year = year_income['total_peak'] + year_income['total_demand']
            year_general_income.append(total_year)
            # 所有收益
            all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()

            all_subquery = (
                incomes_ins.filter(income_date__range=(all_start, income_date))
                .values('income_date')
                .annotate(last_entry=Max('id'))
                .values('last_entry')
            )

            all_income = incomes_ins.filter(id__in=all_subquery).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not all_income['total_peak']:
                all_income['total_peak'] = 0
            if not all_income['total_demand']:
                all_income['total_demand'] = 0
            total_all = all_income['total_peak'] + all_income['total_demand']
            all_general_income.append(total_all)
        detail_dic["today_general_income"] = sum(today_general_income)
        detail_dic["year_general_income"] = sum(year_general_income)
        detail_dic["month_general_income"] = sum(month_general_income)
        detail_dic["all_general_income"] = sum(all_general_income)
        if detail_dic["today_general_income"] >= 10000:
            detail_dic["today_general_income"] = [str(round(detail_dic["today_general_income"] / 10000, 2)), "万元"]
        else:
            detail_dic["today_general_income"] = [str(detail_dic["today_general_income"]), "元"]
        if detail_dic["month_general_income"] >= 10000:
            detail_dic["month_general_income"] = [str(round(detail_dic["month_general_income"] / 10000, 2)), "万元"]
        else:
            detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "元"]
        if detail_dic["year_general_income"] >= 10000:
            detail_dic["year_general_income"] = [str(round(detail_dic["year_general_income"] / 10000, 2)), "万元"]
        else:
            detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "元"]

        if detail_dic["all_general_income"] >= 10000:
            detail_dic["all_general_income"] = [str(round(detail_dic["all_general_income"] / 10000, 2)), "万元"]
        else:
            detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "元"]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail_dic,
                },
            }
        )


class DayUtilizationView(APIView):
    """日利用小时数"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.get(id=user_id)
        used = NewDayHourUsed(user_ins)
        hours_used = used.hours_used
        history_hours_used = used.history_hours_used
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": {"day_hours_used": hours_used, "history_hours_used":
                        history_hours_used, "default_hours_used": 4.440},

                },
            }
        )
