#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\side_forecase_vol.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-19 11:13:50


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseVol(user_Base):
    u'用户侧预算电压等级'
    __tablename__ = "t_side_forecase_vol"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"电压等级名称")
    en_name = Column(VARCHAR(256), nullable=False, comment=u"电压等级英文名称")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
    def __repr__(self):
        
        return "{'id':%s,'name':'%s','en_name':'%s', 'is_use':%s}" % (self.id,self.name,self.en_name,self.is_use)
        
    