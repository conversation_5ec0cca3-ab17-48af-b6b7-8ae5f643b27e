#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:42:37
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_policy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-30 17:50:36



from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseHandleLogs(user_Base):
    u'操作日志记录表'
    __tablename__ = "t_side_forecase_handle_logs"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_id = Column(Integer, nullable=False, comment=u"操作人id")
    project_id = Column(Integer, nullable=False, comment=u"项目id")
    create_time = Column(DateTime, nullable=False, comment=u"日志记录时间")
    # dic_log_id = Column(Integer, ForeignKey("t_side_forecase_dic_log.id"),nullable=False, comment=u"操作类型外键id")
    dic_log_id = Column(Integer, nullable=False, comment=u"操作类型外键id")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否默认1")
    descr = Column(Text, nullable=True, comment=u"动作内容")
    filename = Column(Text, nullable=True, comment=u"文件名称")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'user_id':'%s','project_id':'%s','create_time':'%s','dic_log_id':%s,'is_use':'%s','descr':'%s','filename':'%s'}" \
               %(self.id,self.user_id,self.project_id,self.create_time,self.dic_log_id,self.is_use,self.descr,self.filename)

   