package com.robestec.analysis.dto.projectpack;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 项目包更新DTO
 */
@Data
@ApiModel("项目包更新DTO")
public class ProjectPackUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "项目包数据(JSON格式)", required = true)
    @NotBlank(message = "项目包数据不能为空")
    private String data;

    @ApiModelProperty(value = "项目包名称", required = true)
    @NotBlank(message = "项目包名称不能为空")
    private String name;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
