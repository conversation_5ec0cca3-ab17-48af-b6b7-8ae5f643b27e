#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_price.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-24 09:50:11


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_part import ForecasePart
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol

class ForecasePrice(user_Base):
    u'用户侧预算电价'
    __tablename__ = "t_side_forecase_price"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    year_month = Column(VARCHAR(20), nullable=False, comment=u"年月，YYYY-mm")
    h0 = Column(VARCHAR(20), nullable=False, comment=u"0时")
    h1 = Column(VARCHAR(20), nullable=False, comment=u"1时")
    h2 = Column(VARCHAR(20), nullable=False, comment=u"2时")
    h3 = Column(VARCHAR(20), nullable=False, comment=u"3时")
    h4 = Column(VARCHAR(20), nullable=False, comment=u"4时")
    h5 = Column(VARCHAR(20), nullable=False, comment=u"5时")
    h6 = Column(VARCHAR(20), nullable=False, comment=u"6时")
    h7 = Column(VARCHAR(20), nullable=False, comment=u"7时")
    h8 = Column(VARCHAR(20), nullable=False, comment=u"8时")
    h9 = Column(VARCHAR(20), nullable=False, comment=u"9时")
    h10 = Column(VARCHAR(20), nullable=False, comment=u"10时")
    h11 = Column(VARCHAR(20), nullable=False, comment=u"11时")
    h12 = Column(VARCHAR(20), nullable=False, comment=u"12时")
    h13 = Column(VARCHAR(20), nullable=False, comment=u"13时")
    h14 = Column(VARCHAR(20), nullable=False, comment=u"14时")
    h15 = Column(VARCHAR(20), nullable=False, comment=u"15时")
    h16 = Column(VARCHAR(20), nullable=False, comment=u"16时")
    h17 = Column(VARCHAR(20), nullable=False, comment=u"17时")
    h18 = Column(VARCHAR(20), nullable=False, comment=u"18时")
    h19 = Column(VARCHAR(20), nullable=False, comment=u"19时")
    h20 = Column(VARCHAR(20), nullable=False, comment=u"20时")
    h21 = Column(VARCHAR(20), nullable=False, comment=u"21时")
    h22 = Column(VARCHAR(20), nullable=False, comment=u"22时")
    h23 = Column(VARCHAR(20), nullable=False, comment=u"23时")
    pv0 = Column(Integer, nullable=False, comment=u"0时峰谷标识-2深谷-1谷0平1高峰2尖峰")
    pv1 = Column(Integer, nullable=False, comment=u"1时峰谷标识")
    pv2 = Column(Integer, nullable=False, comment=u"2时峰谷标识")
    pv3 = Column(Integer, nullable=False, comment=u"3时峰谷标识")
    pv4 = Column(Integer, nullable=False, comment=u"4时峰谷标识")
    pv5 = Column(Integer, nullable=False, comment=u"5时峰谷标识")
    pv6 = Column(Integer, nullable=False, comment=u"6时峰谷标识")
    pv7 = Column(Integer, nullable=False, comment=u"7时峰谷标识")
    pv8 = Column(Integer, nullable=False, comment=u"8时峰谷标识")
    pv9 = Column(Integer, nullable=False, comment=u"9时峰谷标识")
    pv10 = Column(Integer, nullable=False, comment=u"10时峰谷标识")
    pv11 = Column(Integer, nullable=False, comment=u"11时峰谷标识")
    pv12 = Column(Integer, nullable=False, comment=u"12时峰谷标识")
    pv13 = Column(Integer, nullable=False, comment=u"13时峰谷标识")
    pv14 = Column(Integer, nullable=False, comment=u"14时峰谷标识")
    pv15 = Column(Integer, nullable=False, comment=u"15时峰谷标识")
    pv16 = Column(Integer, nullable=False, comment=u"16时峰谷标识")
    pv17 = Column(Integer, nullable=False, comment=u"17时峰谷标识")
    pv18 = Column(Integer, nullable=False, comment=u"18时峰谷标识")
    pv19 = Column(Integer, nullable=False, comment=u"19时峰谷标识")
    pv20 = Column(Integer, nullable=False, comment=u"20时峰谷标识")
    pv21 = Column(Integer, nullable=False, comment=u"21时峰谷标识")
    pv22 = Column(Integer, nullable=False, comment=u"22时峰谷标识")
    pv23 = Column(Integer, nullable=False, comment=u"23时峰谷标识")
    province_id = Column(Integer, ForeignKey("t_side_forecase_province.id"),nullable=False, comment=u"所属省份")
    ele_id = Column(Integer, ForeignKey("t_side_forecase_ele.id"),nullable=False, comment=u"用电分类")
    part_id = Column(Integer, ForeignKey("t_side_forecase_part.id"),nullable=False, comment=u"用电分类部制度")
    vol_id = Column(Integer, ForeignKey("t_side_forecase_vol.id"),nullable=False, comment=u"电压等级")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
   
    province_price = relationship("ForecaseProvince",backref='province_price',foreign_keys=[province_id])
    ele_price = relationship("ForecaseEle",backref='ele_price',foreign_keys=[ele_id])
    part_price = relationship("ForecasePart",backref='part_price',foreign_keys=[part_id])
    vol_price = relationship("ForecaseVol",backref='vol_price',foreign_keys=[vol_id])
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
    def __repr__(self):
        
        return "{'id':%s,'year_month':'%s','h0':%s,'h1':%s,'h2':%s,'h3':%s,'h4':%s,'h5':%s,'h6':%s,'h7':%s,'h8':%s,'h9':%s,\
            'h10':%s,'h11':%s,'h12':%s,'h13':%s,'h14':%s,'h15':%s,'h16':%s,'h17':%s,'h18':%s,'h19':%s,'h20':%s,'h21':%s,'h22':%s,\
                'h23':%s,'pv0':%s,'pv1':%s,'pv2':%s,'pv3':%s,'pv4':%s,'pv5':%s,'pv6':%s,'pv7':%s,'pv8':%s,'pv9':%s,'pv10':%s,'pv11':%s,'pv12':%s,\
                'pv13':%s,'pv14':%s,'pv15':%s,'pv16':%s,'pv17':%s,'pv18':%s,'pv19':%s,'pv20':%s,'pv21':%s,'pv22':%s,'pv23':%s,'is_use':%s}" % (
            self.id,self.year_month,self.h0,self.h1,self.h2,self.h3,self.h4,self.h5,self.h6,self.h7,self.h8,self.h9,self.h10,self.h11,self.h12,self.h13,self.h14,
            self.h15,self.h16,self.h17,self.h18,self.h19,self.h20,self.h21,self.h22,self.h23,self.pv0,self.pv1,self.pv2,self.pv3,self.pv4,self.pv5,self.pv6,self.pv7,
            self.pv8,self.pv9,self.pv10,self.pv11,self.pv12,self.pv13,self.pv14,self.pv15,self.pv16,self.pv17,self.pv18,self.pv19,self.pv20,self.pv21,self.pv22,self.pv23,self.is_use)
        
    