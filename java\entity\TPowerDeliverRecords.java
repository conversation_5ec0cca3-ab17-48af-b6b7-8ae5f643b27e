package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 功率计划下发记录表
 * 对应Python模型: TPowerDeliverRecords
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_power_deliver_records")
public class TPowerDeliverRecords extends SuperEntity {

    /**
     * 任务名称
     */
    @TableField("name")
    private String name;

    /**
     * 英文任务名称
     */
    @TableField("en_name")
    private String enName;

    /**
     * 联系人电话
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 功率计划列表(JSON格式)
     */
    @TableField("power_list")
    private String powerList;

    /**
     * 关联并网点列表(JSON格式)
     */
    @TableField("station_list")
    private String stationList;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 计划类型: 1-自定义, 2-周期性, 3-节假日
     */
    @TableField("plan_type")
    private Integer planType;

}
