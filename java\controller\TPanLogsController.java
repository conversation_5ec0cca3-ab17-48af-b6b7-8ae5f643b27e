package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tpanlogs.TPanLogsCreateDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsQueryDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsUpdateDTO;
import com.robestec.analysis.service.TPanLogsService;
import com.robestec.analysis.vo.TPanLogsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 下发日志记录管理API
 */
@RestController
@RequestMapping("/pan-logs")
@RequiredArgsConstructor
@Api(tags = "下发日志记录管理API")
public class TPanLogsController {

    private final TPanLogsService tPanLogsService;

    @GetMapping
    @ApiOperation("分页查询下发日志记录")
    public PageResult<TPanLogsVO> queryTPanLogs(@Validated TPanLogsQueryDTO queryDTO) {
        return tPanLogsService.queryTPanLogs(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增下发日志记录")
    public Result<Long> createTPanLogs(@Validated @RequestBody TPanLogsCreateDTO createDTO) {
        return Result.succeed(tPanLogsService.createTPanLogs(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增下发日志记录")
    public Result createTPanLogsList(@Validated @RequestBody List<TPanLogsCreateDTO> createDTOList) {
        tPanLogsService.createTPanLogsList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改下发日志记录")
    public Result updateTPanLogs(@PathVariable Long id, @Validated @RequestBody TPanLogsUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tPanLogsService.updateTPanLogs(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除下发日志记录")
    public Result deleteTPanLogs(@PathVariable Long id) {
        tPanLogsService.deleteTPanLogs(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取下发日志记录详情")
    public Result<TPanLogsVO> getTPanLogs(@PathVariable Long id) {
        return Result.succeed(tPanLogsService.getTPanLogs(id));
    }

    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询下发日志记录")
    public Result<List<TPanLogsVO>> getTPanLogsByUserId(@PathVariable Long userId) {
        return Result.succeed(tPanLogsService.getTPanLogsByUserId(userId));
    }

    @GetMapping("/station/{station}")
    @ApiOperation("根据电站名称查询下发日志记录")
    public Result<List<TPanLogsVO>> getTPanLogsByStation(@PathVariable String station) {
        return Result.succeed(tPanLogsService.getTPanLogsByStation(station));
    }

    @GetMapping("/status/{status}")
    @ApiOperation("根据状态查询下发日志记录")
    public Result<List<TPanLogsVO>> getTPanLogsByStatus(@PathVariable Integer status) {
        return Result.succeed(tPanLogsService.getTPanLogsByStatus(status));
    }

    @GetMapping("/type-name/{typeName}")
    @ApiOperation("根据类型名称查询下发日志记录")
    public Result<List<TPanLogsVO>> getTPanLogsByTypeName(@PathVariable String typeName) {
        return Result.succeed(tPanLogsService.getTPanLogsByTypeName(typeName));
    }

    @GetMapping("/count/user/{userId}")
    @ApiOperation("统计用户的下发日志记录数量")
    public Result<Long> countByUserId(@PathVariable Long userId) {
        return Result.succeed(tPanLogsService.countByUserId(userId));
    }

    @GetMapping("/count/status/{status}")
    @ApiOperation("统计指定状态的下发日志记录数量")
    public Result<Long> countByStatus(@PathVariable Integer status) {
        return Result.succeed(tPanLogsService.countByStatus(status));
    }
}
