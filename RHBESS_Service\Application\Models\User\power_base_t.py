#!/usr/bin/env python
# coding=utf-8
# @Information:

from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Column, DateTime, CHAR, VARCHAR


class PowerBase(user_Base):
    u'基准功率基础表'
    __tablename__ = "t_power_base"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    op_ts = Column(DateTime, nullable=False, comment=u"录入时间")
    station = Column(VARCHAR(50), nullable=False, comment=u"所属站")
    descr = Column(VARCHAR(50), nullable=False, comment=u"所属站描述")
    start_time = Column(VARCHAR(50), nullable=False, comment=u"开始时间YYYY - mm - dd")
    end_time = Column(VARCHAR(50), nullable=True, comment=u"结束时间YYYY - mm - dd")
    update_time = Column(DateTime, nullable=True, comment=u"更新时间YYYY - mm - dd")
    create_user = Column(Integer, nullable=False, comment=u"创建人id")
    update_user = Column(Integer, nullable=False, comment=u"更新人id")
    chag_times = Column(VARCHAR(100), nullable=True, comment=u"充电时段")
    disg_times = Column(VARCHAR(100), nullable=True, comment=u"放电时段")
    static_times = Column(VARCHAR(100), nullable=True, comment=u"静止时段")
    create_user_name = Column(VARCHAR(100), nullable=False, comment=u"创建人名称")
    is_use = Column(CHAR(2), nullable=False, comment=u"是否使用1是0否")

    en_create_user_name = Column(VARCHAR(100), nullable=True, comment=u"英文创建人名称")
    en_descr = Column(VARCHAR(50), nullable=False, comment=u"所属站描述")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'op_ts':'%s','start_time':'%s','station':'%s','end_time':'%s','update_time':'%s','create_user':'%s','update_user':'%s','chag_times':'%s'," \
               "'disg_times':'%s','static_times':'%s','create_user_name':'%s','is_use':'%s','en_create_user_name':'%s','descr':'%s','en_descr':'%s'}" % (
               self.id, self.op_ts, self.start_time, self.station, self.end_time,
               self.update_time, self.create_user, self.update_user, self.chag_times, self.disg_times,
               self.static_times, self.create_user_name, self.is_use, self.en_create_user_name, self.descr,
               self.en_descr)
        return bean.replace("None", '')




