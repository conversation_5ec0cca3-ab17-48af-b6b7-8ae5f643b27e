import datetime
import json
import logging

import math
import requests

from Application.Models.ElePriceDescision.models import *
from Application.Models.base_handler import <PERSON>Handler
from Tools.DataEnDe.MD5 import MD5Tool
from Tools.DataEnDe.aes_cbc import AESUtil
from Tools.DecisionDB.ele_base import user_session

zhili_openId = 'yuanchu1'  # 智锂企业标识
zhili_key = b"fastfuncn1234567"
zhili_iv = b"fastfuncn1234567"
# 自身的加密密匙和向量
openId = 'RHBESS01'
key = b"RHBESS1101022022"  # 西城区地区编码
iv = b"1101020090002022"  # 展览路街道


class PriceRealDataHandler(BaseHandler):
    """
    获取指定参数的实时数据，暂定一次获取一个实时值，获取已有的最新数据。
    """""
    @staticmethod
    async def get_series_data(flag):
        if flag == 13:  # 实时联络线计划（总加）
            d = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.series_id == flag,
                                                              RSeriesDataShanxi.name == '总加',
                                                              RSeriesDataShanxi.is_use == "1").order_by(
                RSeriesDataShanxi.day.desc(), RSeriesDataShanxi.moment.desc()).limit(1).first())
        else:
            d = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.series_id == flag,
                                                              RSeriesDataShanxi.is_use == "1").order_by(
                RSeriesDataShanxi.day.desc(), RSeriesDataShanxi.moment.desc()).limit(1).first())
        if d:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                "name": d.name if d.name is not None else '--'
            }
        else:
            temp_dict = {
                "datetime": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "day": datetime.datetime.now().strftime("%Y-%m-%d"),
                "moment": datetime.datetime.now().strftime("%H:%M:%S"),
                "value1": '--',
                "value2": '--',
                "value3": '--',
                "name": '无数据'
            }

        return temp_dict

    async def get_series_info(self, flag):
        temp_map_dict = {
            "day": "日期",
            "moment": "时刻",
            "datetime": "时间"}

        data = await self.get_series_data(flag)

        if data:
            # temp_list = []
            if flag == 3:  # 日前非市场化机组出力预测
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"] if data["value1"] is not None else '--',
                }
                temp_map_dict.update({
                    "value1": "非市场化机组出力预测(MW)"
                })
            elif flag == 4:  # 日前新能源负荷预测
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value3"],
                    "value2": data["value2"],
                    "value3": data["value1"],
                }
                temp_map_dict.update({
                    "value1": "预测风电出力(MW)",
                    "value2": "预测光伏出力(MW)",
                    "value3": "预测新能源总出力(MW)"
                })
            elif flag == 5:  # 全省用电负荷预测信息
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "value1": "全省用电负荷预测信息 (电力值)"
                })
            elif flag == 6:  # 日前出清电量
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "value1": "现货日前出清电量(MWh)"
                })
            elif flag == 7:  # 日前联络线计划信息（加总）
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "name": data['name'],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "name": "通道类型",
                    "value1": "日前联络线计划信息（加总）(电力值)"
                })
            elif flag == 8:  # 机组检修总容量
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "name": data["name"],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "name": "市场主体名称",
                    "value1": "检修容量"
                })
            elif flag == 9:  # 实时非市场化机组出力曲线
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"]
                }
                temp_map_dict.update({
                    "value1": "非市场化机组实际出力(MW)"
                })
            elif flag == 10:  # 新能源总实时出力
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                    "value2": data["value2"],
                    "value3": data["value3"],
                }
                temp_map_dict.update({
                    "value1": "风电实际值(MW)",
                    "value2": "光伏实际值(MW)",
                    "value3": "新能源总加实际值(MW)"
                })
            elif flag == 11:  # 系统实时负荷频率备用情况
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                    "value2": data["value2"],
                    "value3": data["value3"],
                }
                temp_map_dict.update({
                    "value1": "系统负荷实际值(MW)",
                    "value2": "频率实际值(MW)",
                    "value3": "实际上旋备用"
                })
            elif flag == 12:  # 实时出清电量
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"]
                }
                temp_map_dict.update({
                    "value1": "现货实时出清电量(MWh)"
                })
            elif flag == 13:  # 实时联络线计划（加总）
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "name": data["name"],
                    "value1": data["value1"]
                }
                temp_map_dict.update({
                    "name": "通道类型",
                    "value1": "实时联络线计划（加总）电力值(MW)"
                })
            elif flag == 14:  # 水电总实时出力
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"]
                }
                temp_map_dict.update({
                    "value1": "水电实际值(MW)"
                })
            elif flag == 15:  # 现货出清电价信息
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                    "value2": data["value2"]
                }
                temp_map_dict.update({
                    "value1": "日前价格",
                    "value2": "实时价格"
                })
            elif flag == 16:  # 预测发电总出力
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "value1": "预测发电总出力(MW)"
                })
            elif flag == 17:  # 实时节点边际电价
                temp_dict = {
                    "datetime": data["datetime"],
                    "day": data["day"].strftime("%Y-%m-%d"),
                    "moment": data['moment'],
                    "value1": data["value1"],
                }
                temp_map_dict.update({
                    "value1": "实时出清价格"
                })
            else:
                temp_dict = {}

            result = temp_dict
        else:
            result = {}

        return result, temp_map_dict

    @staticmethod
    async def get_non_series_data(non_series_id):  # 非时序数据
        d = (user_session.query(RNonSeriesDataShanxi).filter(RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                                RNonSeriesDataShanxi.is_use == '1').order_by(RNonSeriesDataShanxi.day.desc(), RNonSeriesDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            if d.name:
                data_ = (user_session.query(RNotSeriesNamesDataShanxi).filter(
                    RNotSeriesNamesDataShanxi.id == eval(d.name),
                    RNotSeriesNamesDataShanxi.is_use == '1').first())
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                        "%Y-%m-%d"),
                    "day": d.day,
                    "moment": d.moment if d.moment else '',
                    "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                    "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                    "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                    "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                    "descr": d.descr if d.descr is not None else '--',
                    "name": data_.name if data_.name is not None else '--',
                    "index_type": d.index_type if d.index_type is not None else '--',
                    "start_time": d.start_time,
                    "end_time": d.end_time
                }
            else:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                        "%Y-%m-%d"),
                    "day": d.day,
                    "moment": d.moment,
                    "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                    "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                    "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                    "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                    "descr": d.descr if d.descr is not None else '--',
                    "name": d.name if d.name is not None else '--',
                    "index_type": d.index_type if d.index_type is not None else '--',
                    "start_time": d.start_time,
                    "end_time": d.end_time
                }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRSectionsShadowDataShanxiData():  # 1、断面约束情况及影子价格
        d = (user_session.query(RSectionsShadowDataShanxi).filter(RSectionsShadowDataShanxi.is_use == '1').order_by(RSectionsShadowDataShanxi.day.desc(), RSectionsShadowDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": round(float(d.value), 2) if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRRealAccessEleShanxiData():  # 2、重要通道实际输电情况
        d = (user_session.query(RRealAccessEleShanxi).filter(RRealAccessEleShanxi.is_use == "1").order_by(RRealAccessEleShanxi.day.desc(), RRealAccessEleShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": round(float(d.value), 2) if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRRealNodePriceShanxiData():  # ６、实时节点边际电价
        d = (user_session.query(RRealNodePriceShanxi).filter(RRealNodePriceShanxi.is_use == "1").order_by(RRealNodePriceShanxi.day.desc(), RRealNodePriceShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                "node": d.node,
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRPreNodePriceShanxiData():  # ７、日前节点边际电价
        d = (user_session.query(RPreNodePriceShanxi).filter(RPreNodePriceShanxi.is_use == "1").order_by(RPreNodePriceShanxi.day.desc(), RPreNodePriceShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                "node": d.node,
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRRealSectionsBlockDataShanxiData():  # 8、实时输电断面约束及阻塞
        d = (user_session.query(RRealSectionsBlockDataShanxi).filter(RRealSectionsBlockDataShanxi.is_use == "1").order_by(RRealSectionsBlockDataShanxi.day.desc(), RRealSectionsBlockDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": d.value if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRPreSectionsBlockDataShanxiData():  # 9、日前输电断面约束及阻塞
        d = (user_session.query(RPreSectionsBlockDataShanxi).filter(RPreSectionsBlockDataShanxi.is_use == "1").order_by(RPreSectionsBlockDataShanxi.day.desc(), RPreSectionsBlockDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": d.value if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRRealCallWireDataShanxiData():  # 10、实时联络线计划（站点）=====》 改查时序表：实时联络线计划（加总）
        d = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.series_id == 13,
                                                             RSeriesDataShanxi.is_use == "1").
             order_by(RSeriesDataShanxi.day.desc(), RSeriesDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": round(float(d.value1), 2) if d.value1 is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRPreCallWireDataShanxiData():  # 11、日前联络线计划信息（站点）
        d = (user_session.query(RPreCallWireDataShanxi).filter(RPreCallWireDataShanxi.is_use == "1").order_by(RPreCallWireDataShanxi.day.desc(), RPreCallWireDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": round(float(d.value), 2) if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRRealUnitPowerDataShanxiData():  # 20、机组实时出力
        d = (user_session.query(RRealUnitPowerDataShanxi).filter(RRealUnitPowerDataShanxi.is_use == "1").order_by(RRealUnitPowerDataShanxi.day.desc(), RRealUnitPowerDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value": round(float(d.value), 2) if d.value is not None else '--',
                "name": d.name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRPreUnitOpenDataShanxiData():  # 21、日前必开机组
        d = (user_session.query(RPreUnitOpenDataShanxi).filter(RPreUnitOpenDataShanxi.is_use == "1").order_by(RPreUnitOpenDataShanxi.day.desc(), RPreUnitOpenDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "mem_name": d.mem_name,
                "unit_name": d.unit_name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    @staticmethod
    async def getRPreUnitCloseDataShanxiData():  # 22、日前必停机组
        d = (user_session.query(RPreUnitCloseDataShanxi).filter(RPreUnitCloseDataShanxi.is_use == "1").order_by(RPreUnitCloseDataShanxi.day.desc(), RPreUnitCloseDataShanxi.moment.desc()).limit(1).first())
        # temp_list = []
        if d:
            # for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "mem_name": d.mem_name,
                "unit_name": d.unit_name
            }
                # temp_list.append(temp_dict)
        else:
            temp_dict = {}

        # data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return temp_dict

    async def get_non_series_info(self, non_series_id):
        temp_dict = {}
        temp_map_dict = {}
        if non_series_id in [1, 2, 6, 7, 8, 9, 10, 11, 20, 21, 22]:
            if non_series_id == 1:  # 断面约束情况及影子价格
                data = await self.getRSectionsShadowDataShanxiData()
                if data:
                    # for i in range(len(data)):
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                    }
                        # temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "阻塞价格(元/MWh)"}

            elif non_series_id == 2:  # 重要通道实际输电情况
                data = await self.getRRealAccessEleShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                    }
                    temp_map_dict = {"id": "序号", 'datetime': '时间', "day": "日期", "moment": "时刻", 'name': "名称",
                                     "value": "潮流(MW)"}

            elif non_series_id == 6:  # 实时节点边际电价
                data = await self.getRRealNodePriceShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "node": data['node'],
                        "value1": data["value1"],
                        "value2": data["value2"],
                        "value3": data["value1"] + data["value2"]
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 7:  # 日前节点边际电价
                data = await self.getRPreNodePriceShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "node": data['node'],
                        "value1": data["value1"],
                        "value2": data["value2"],
                        "value3": data["value1"] + data["value2"]
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 8:  # 实时输电断面约束及阻塞
                data = await self.getRRealSectionsBlockDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 9:  # 日前输电断面约束及阻塞
                data = await self.getRPreSectionsBlockDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 10:  # 实时联络线计划（站点）
                data = await self.getRRealCallWireDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"]
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 11:  # 日前联络线计划信息（站点）
                data = await self.getRPreCallWireDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 20:  # 机组实时出力
                data = await self.getRRealUnitPowerDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data["name"],
                        "value": data["value"],
                        "descr": "停机/待机"
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "机组名称",
                                     "value": "电力值(MW)", "descr": "机组状态"}
            elif non_series_id == 21:  # 日前必开机组
                data = await self.getRPreUnitOpenDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "mem_name": data["mem_name"],
                        "unit_name": data["unit_name"],
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
            elif non_series_id == 22:  # 日前必停机组
                data = await self.getRPreUnitCloseDataShanxiData()
                if data:
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "mem_name": data["mem_name"],
                        "unit_name": data["unit_name"],
                    }
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
        else:
            data = await self.get_non_series_data(int(non_series_id))
            if data:
                if non_series_id == 3:  # 市场力分析指标
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "value": data["value1"],
                        "index_type": data["index_type"],
                        "name": data["name"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "电厂", 'value': "指标值",
                                     "index_type": "指标类型"}

                elif non_series_id == 4:  # 实时电能量市场出清概况
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "descr": data["descr"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'descr': "出清情况"}

                elif non_series_id == 5:  # 日前电能量市场出清概况
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "descr": data["descr"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'descr': "出清情况"}

                elif non_series_id == 12:  # 实时备用总量
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "value1": data["value1"],
                        "value2": data["value2"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 13:  # 日前备用总量
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "value1": data["value1"],
                        "value2": data["value2"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 14:  # 实时调频容量里程价格
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "value1": data["value1"],
                        "value2": data["value2"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}
                elif non_series_id == 15:  # 日前调频容量里程价格
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "value1": data["value1"],
                        "value2": data["value2"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}

                elif non_series_id == 16:  # 水电发电计划预测
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "value1": data["value1"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "电量(MWh)"}

                elif non_series_id == 17:  # 抽蓄电站蓄水水位
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "value1": data["value1"],
                        "descr": data["descr"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "水位值", "descr": "描述"}

                elif non_series_id == 18:  # 调频辅助服务需求
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "value1": data["value1"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'value1': "调频需求容量(MW)"}

                elif non_series_id == 19:  # 输电通道容量
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "value1": data["value1"],
                        "name": data['name'],
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'name': "通道",
                                     'value1': "输电通道容量"}

                elif non_series_id == 23:  # 断面约束
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data['name'],
                        "descr": data['descr'],
                        "value1": data["value1"],
                        "value2": data["value2"],
                        "index_type": data["index_type"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "index_type": "市场成员名称", "descr": "断面描述",
                                     "value1": "正向传输极限", 'value2': "反向传输极限", "name": "断面名称"}

                elif non_series_id == 24:  # 日前正负备用需求
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data['name'],
                        "value1": data["value1"],
                        "index_type": data["index_type"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "市场成员名称", "index_type": "类型",
                                     "value1": "备用负荷"}

                elif non_series_id == 25:  # 输变电设备检修计划信息
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "moment": data['moment'],
                        "name": data['name'],
                        "descr": data['descr'],
                        "start_time": data["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                        "end_time": data["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "设备名称", "descr": "设备类型",
                                     "start_time": "设备检修开始时间", "end_time": "设备检修结束时间"}

                elif non_series_id == 26:  # 开机不满七天机组信息
                    temp_dict = {
                        "datetime": data["datetime"],
                        "day": data["day"].strftime("%Y-%m-%d"),
                        "name": data["name"]
                    }

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'name': "机组名称"}

        # result = sorted(temp_list, key=lambda x: x['datetime'])
        return temp_dict, temp_map_dict

    # @tornado.web.authenticated
    async def post(self):
        self.refreshSession()  # 刷新session
        try:
            is_authed, data = self._verifyToken()
            if not is_authed:
                return data

            type_ = data.get("type")
            flag = data.get("flag")

            # type_ = int(self.get_argument("type"))
            # flag = int(self.get_argument("flag"))

            if flag == 1:  # 时序交易数据
                result, temp_map_dict = await self.get_series_info(type_)

            else:  # 非时序交易数据
                result, temp_map_dict = await self.get_non_series_info(type_)

            data_ = {
                "detail": result,
                "maps": temp_map_dict
            }

            user_session.close()
            self._getEncryptValue(data_)
            # return self.returnTypeSuc(data_)

        except ValueError as e:
            return self.customError(str(e).encode('utf8'))
        except Exception as e:
            import traceback
            logging.error(traceback.print_exc())
            logging.info(e)
            return self.requestError()

    def _getEncryptValue(self, data):

        if not data:  # 未传入数据
            return None
        if not isinstance(data, dict):
            return data

        value = json.dumps(data)

        dat = AESUtil.encryt(value, key, iv)
        dat = str(dat, encoding='utf-8')

        self.set_header('Bean', MD5Tool.get_str_md5(dat + openId))

        return self.returnTypeSuc(dat)

    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean', None)
        logging.info('Bean: %s' % Bean)
        data = self.get_argument('data',None)
        logging.info('密文---data-------:%s ' %data)

        if not data:  # 未传入密文
            return False, self.customError('缺失参数：<data>')

        data = data.replace(' ', '+')

        md5 = MD5Tool.get_str_md5(data + zhili_openId)
        if not Bean or md5 != Bean:  # 身份验证失败
            return False, self.tokenError()

        data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
        keys = data.keys()

        if 'type' not in keys or 'flag' not in keys:
            return False, self.customError('缺失参数：<type> or <flag>')

        if data['flag'] not in {'时序': 1, '非时序': 0}.values():
            return False, self.customError(f'参数<type>：{data["flag"]}不合规')

        if data['flag'] == 1:
            if not data['type'] in range(3, 17):
                return False, self.customError('参数不合规: <type> 取值范围为[3~16]')
        else:
            if not data['type'] in range(3, 17):
                return False, self.customError("参数不合规: <type> 取值范围为[1~26]")

        logging.info('明文*****data*************:%s' % data)

        return True, data


class PriceHistoryDataHandler(BaseHandler):
    """
    历史数据暂定最早可查询30天前的数据，即开始时间距离当前时间最长跨度为30天。所有返回的数据均按时间正序排列。
    """""
    @staticmethod
    async def get_series_data(start_day, end_day, flag):
        if flag == 13:    # 实时联络线计划（总加）
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == flag,
                                                                 RSeriesDataShanxi.name == '总加',
                                                                 RSeriesDataShanxi.is_use == "1").all())
        else:
            data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                                 RSeriesDataShanxi.day <= end_day,
                                                                 RSeriesDataShanxi.series_id == flag,
                                                                 RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment if d.moment else '--',
                    "value1": round(float(d.value1), 2) if d.value1 is not None else '--',
                    "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                    "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                    "name": d.name if d.name else '--'
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get_series_info(self, start_day, end_day, flag):
        temp_map_dict = {
            "id": "序号",
            "day": "日期",
            "moment": "时刻",
            "datetime": "时间"}

        data = await self.get_series_data(start_day, end_day, flag)

        if data:
            temp_list = []
            for i in range(len(data)):
                if flag == 3:  # 日前非市场化机组出力预测
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "非市场化机组出力预测(MW)"
                    })
                elif flag == 4:  # 日前新能源负荷预测
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value3"],
                        "value2": data[i]["value2"],
                        "value3": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "预测风电出力(MW)",
                        "value2": "预测光伏出力(MW)",
                        "value3": "预测新能源总出力(MW)"
                    })
                elif flag == 5:  # 全省用电负荷预测信息
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "全省用电负荷预测信息 (电力值)"
                    })
                elif flag == 6:  # 日前出清电量
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "现货日前出清电量(MWh)"
                    })
                elif flag == 7:  # 日前联络线计划信息（加总）
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "name": data[i]['name'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "name": "通道类型",
                        "value1": "日前联络线计划信息（加总）(电力值)"
                    })
                elif flag == 8:  # 机组检修总容量
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "name": data[i]["name"],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "name": "市场主体名称",
                        "value1": "检修容量"
                    })
                elif flag == 9:  # 实时非市场化机组出力曲线
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"]
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "非市场化机组实际出力(MW)"
                    })
                elif flag == 10:  # 新能源总实时出力
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                        "value2": data[i]["value2"],
                        "value3": data[i]["value3"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "风电实际值(MW)",
                        "value2": "光伏实际值(MW)",
                        "value3": "新能源总加实际值(MW)"
                    })
                elif flag == 11:  # 系统实时负荷频率备用情况
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                        "value2": data[i]["value2"],
                        "value3": data[i]["value3"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "系统负荷实际值(MW)",
                        "value2": "频率实际值(MW)",
                        "value3": "实际上旋备用"
                    })
                elif flag == 12:  # 实时出清电量
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"]
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "现货实时出清电量(MWh)"
                    })
                elif flag == 13:  # 实时联络线计划（加总）
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "name": data[i]['name'],
                        "value1": data[i]["value1"]
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "name": "通道类型",
                        "value1": "实时联络线计划（加总）电力值(MW)"
                    })
                elif flag == 14:  # 水电总实时出力
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"]
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "水电实际值(MW)"
                    })
                elif flag == 15:  # 现货出清电价信息
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                        "value2": data[i]["value2"]
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "日前价格",
                        "value2": "实时价格"
                    })
                elif flag == 16:  # 预测发电总出力
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "预测发电总出力(MW)"
                    })
                elif flag == 17:  # 实时节点边际电价
                    temp_dict = {
                        "id": i + 1,
                        "datetime": data[i]["datetime"],
                        "day": data[i]["day"].strftime("%Y-%m-%d"),
                        "moment": data[i]['moment'],
                        "value1": data[i]["value1"],
                    }
                    temp_list.append(temp_dict)
                    temp_map_dict.update({
                        "value1": "实时出清价格"
                    })

            result = sorted(temp_list, key=lambda x: x['datetime'])
        else:
            result = []

        return result, temp_map_dict

    @staticmethod
    async def get_non_series_data(start_day, end_day, non_series_id):  # 非时序数据
        data = (user_session.query(RNonSeriesDataShanxi).filter(RNonSeriesDataShanxi.day >= start_day,
                                                                RNonSeriesDataShanxi.day <= end_day,
                                                                RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                                RNonSeriesDataShanxi.is_use == '1').all())
        temp_list = []
        if data:
            for d in data:
                if d.name:
                    data_ = (user_session.query(RNotSeriesNamesDataShanxi).filter(
                        RNotSeriesNamesDataShanxi.id == eval(d.name),
                        RNotSeriesNamesDataShanxi.is_use == '1').first())
                    temp_dict = {
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                            "%Y-%m-%d"),
                        "day": d.day,
                        "moment": d.moment if d.moment else '--',
                        "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                        "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                        "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                        "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                        "descr": d.descr,
                        "name": data_.name,
                        "index_type": d.index_type,
                        "start_time": d.start_time,
                        "end_time": d.end_time
                    }
                else:
                    temp_dict = {
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment if d.moment else d.day.strftime(
                            "%Y-%m-%d"),
                        "day": d.day,
                        "moment": d.moment,
                        "value1": round(eval(d.value1), 2) if d.value1 is not None else '--',
                        "value2": round(eval(d.value2), 2) if d.value2 is not None else '--',
                        "value3": round(eval(d.value3), 2) if d.value3 is not None else '--',
                        "value4": round(eval(d.value4), 2) if d.value4 is not None else '--',
                        "descr": d.descr,
                        "name": d.name,
                        "index_type": d.index_type,
                        "start_time": d.start_time,
                        "end_time": d.end_time
                    }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRSectionsShadowDataShanxiData(start_day, end_day):  # 1、断面约束情况及影子价格
        data = (user_session.query(RSectionsShadowDataShanxi).filter(RSectionsShadowDataShanxi.day >= start_day,
                                                                     RSectionsShadowDataShanxi.day <= end_day,
                                                                     RSectionsShadowDataShanxi.is_use == '1').all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealAccessEleShanxiData(start_day, end_day):  # 2、重要通道实际输电情况
        data = (user_session.query(RRealAccessEleShanxi).filter(RRealAccessEleShanxi.day >= start_day,
                                                                RRealAccessEleShanxi.day <= end_day,
                                                                RRealAccessEleShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealNodePriceShanxiData(start_day, end_day):  # ６、实时节点边际电价
        data = (user_session.query(RRealNodePriceShanxi).filter(RRealNodePriceShanxi.day >= start_day,
                                                                RRealNodePriceShanxi.day <= end_day,
                                                                RRealNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                    "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                    "node": d.node,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreNodePriceShanxiData(start_day, end_day):  # ７、日前节点边际电价
        data = (user_session.query(RPreNodePriceShanxi).filter(RPreNodePriceShanxi.day >= start_day,
                                                               RPreNodePriceShanxi.day <= end_day,
                                                               RPreNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": round(float(d.ele_price), 2) if d.ele_price is not None else '--',
                    "value2": round(float(d.block_price), 2) if d.block_price is not None else '--',
                    "node": d.node if d.node else '--',
                    "name": d.name if d.name else '--',
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealSectionsBlockDataShanxiData(start_day, end_day):  # 8、实时输电断面约束及阻塞
        data = (user_session.query(RRealSectionsBlockDataShanxi).filter(RRealSectionsBlockDataShanxi.day >= start_day,
                                                                        RRealSectionsBlockDataShanxi.day <= end_day,
                                                                        RRealSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": d.value if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreSectionsBlockDataShanxiData(start_day, end_day):  # 9、日前输电断面约束及阻塞
        data = (user_session.query(RPreSectionsBlockDataShanxi).filter(RPreSectionsBlockDataShanxi.day >= start_day,
                                                                       RPreSectionsBlockDataShanxi.day <= end_day,
                                                                       RPreSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": d.value if d.value is not None else '--',
                    "name": d.name if d.name is not None else '--',
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealCallWireDataShanxiData(start_day, end_day):  # 10、实时联络线计划（站点）=====》 改查时序表：实时联络线计划（加总）
        data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                             RSeriesDataShanxi.day <= end_day,
                                                             RSeriesDataShanxi.series_id == 13,
                                                             RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value1), 2) if d.value1 is not None else '--',
                    "name": d.name if d.name is not None else '--'
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreCallWireDataShanxiData(start_day, end_day):  # 11、日前联络线计划信息（站点）
        data = (user_session.query(RPreCallWireDataShanxi).filter(RPreCallWireDataShanxi.day >= start_day,
                                                                  RPreCallWireDataShanxi.day <= end_day,
                                                                  RPreCallWireDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealUnitPowerDataShanxiData(start_day, end_day):  # 20、机组实时出力
        data = (user_session.query(RRealUnitPowerDataShanxi).filter(RRealUnitPowerDataShanxi.day >= start_day,
                                                                    RRealUnitPowerDataShanxi.day <= end_day,
                                                                    RRealUnitPowerDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": round(float(d.value), 2) if d.value is not None else '--',
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitOpenDataShanxiData(start_day, end_day):  # 21、日前必开机组
        data = (user_session.query(RPreUnitOpenDataShanxi).filter(RPreUnitOpenDataShanxi.day >= start_day,
                                                                  RPreUnitOpenDataShanxi.day <= end_day,
                                                                  RPreUnitOpenDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitCloseDataShanxiData(start_day, end_day):  # 22、日前必停机组
        data = (user_session.query(RPreUnitCloseDataShanxi).filter(RPreUnitCloseDataShanxi.day >= start_day,
                                                                   RPreUnitCloseDataShanxi.day <= end_day,
                                                                   RPreUnitCloseDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get_non_series_info(self, start_day, end_day, non_series_id):
        temp_list = []
        temp_map_dict = {}
        if non_series_id in [1, 2, 6, 7, 8, 9, 10, 11, 20, 21, 22]:
            if non_series_id == 1:  # 断面约束情况及影子价格
                data = await self.getRSectionsShadowDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "阻塞价格(元/MWh)"}

            elif non_series_id == 2:  # 重要通道实际输电情况
                data = await self.getRRealAccessEleShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", 'datetime': '时间', "day": "日期", "moment": "时刻", 'name': "名称",
                                     "value": "潮流(MW)"}

            elif non_series_id == 6:  # 实时节点边际电价
                data = await self.getRRealNodePriceShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 7:  # 日前节点边际电价
                data = await self.getRPreNodePriceShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 8:  # 实时输电断面约束及阻塞
                data = await self.getRRealSectionsBlockDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 9:  # 日前输电断面约束及阻塞
                data = await self.getRPreSectionsBlockDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 10:  # 实时联络线计划（站点）
                data = await self.getRRealCallWireDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 11:  # 日前联络线计划信息（站点）
                data = await self.getRPreCallWireDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 20:  # 机组实时出力
                data = await self.getRRealUnitPowerDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                            "descr": "停机/待机"
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'name': "机组名称",
                                     "value": "电力值(MW)", "descr": "机组状态"}
            elif non_series_id == 21:  # 日前必开机组
                data = await self.getRPreUnitOpenDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
            elif non_series_id == 22:  # 日前必停机组
                data = await self.getRPreUnitCloseDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
        else:
            data = await self.get_non_series_data(start_day, end_day, int(non_series_id))
            if data:
                if non_series_id == 3:  # 市场力分析指标
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value": data[i]["value1"],
                            "index_type": data[i]["index_type"],
                            "name": data[i]["name"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "电厂", 'value': "指标值",
                                     "index_type": "指标类型"}

                elif non_series_id == 4:  # 实时电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'descr': "出清情况"}

                elif non_series_id == 5:  # 日前电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'descr': "出清情况"}

                elif non_series_id == 12:  # 实时备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 13:  # 日前备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 14:  # 实时调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}
                elif non_series_id == 15:  # 日前调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时刻", 'datetime': '时间', 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}

                elif non_series_id == 16:  # 水电发电计划预测
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "电量(MWh)"}

                elif non_series_id == 17:  # 抽蓄电站蓄水水位
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "value1": data[i]["value1"],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'value1': "水位值", "descr": "描述"}

                elif non_series_id == 18:  # 调频辅助服务需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'value1': "调频需求容量(MW)"}

                elif non_series_id == 19:  # 输电通道容量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "name": data[i]['name'],
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "moment": "时刻", 'name': "通道",
                                     'value1': "输电通道容量"}

                elif non_series_id == 23:  # 断面约束
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "index_type": data[i]["index_type"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "index_type": "市场成员名称", "descr": "断面描述",
                                     "value1": "正向传输极限", 'value2': "反向传输极限", "name": "断面名称"}

                elif non_series_id == 24:  # 日前正负备用需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "value1": data[i]["value1"],
                            "index_type": data[i]["index_type"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "市场成员名称", "index_type": "类型",
                                     "value1": "备用负荷"}

                elif non_series_id == 25:  # 输变电设备检修计划信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "start_time": data[i]["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                            "end_time": data[i]["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', "name": "设备名称", "descr": "设备类型",
                                     "start_time": "设备检修开始时间", "end_time": "设备检修结束时间"}

                elif non_series_id == 26:  # 开机不满七天机组信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "name": data[i]["name"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'datetime': '时间', 'name': "机组名称"}

        result = sorted(temp_list, key=lambda x: x['datetime'])
        return result, temp_map_dict

    # @tornado.web.authenticated
    async def post(self):
        self.refreshSession()  # 刷新session
        try:
            is_authed, data = self._verifyToken()
            if not is_authed:
                return data

            type_ = data.get("type")
            flag = data.get("flag")
            start_day = data.get("startTime")
            end_day = data.get("endTime")
            page = data.get("page", None)
            page_size = data.get("pageSize", None)

            # type_ = self.get_argument("type")
            # flag = self.get_argument("flag")
            # start_day = self.get_argument("startTime")
            # end_day = self.get_argument("endTime")
            # page = self.get_argument("page", None)
            # page_size = self.get_argument("pageSize", None)

            # 校验参数 startTime 和 endTime
            try:
                start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
                end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            except Exception as e:
                logging.error(e)
                return self.customError("日期参数错误")

            if start_day > datetime.datetime.now():
                return self.customError("开始日期不能大于当前日期")
            if end_day > datetime.datetime.now():
                return self.customError("结束日期不能大于当前日期")
            if start_day > end_day:
                return self.customError("开始日期不能大于结束日期")

            # 暂定最早可查询30天前的数据，即开始时间距离当前时间最长跨度为30天
            if datetime.datetime.now() - start_day > datetime.timedelta(days=30):
                return self.customError("最早可查询30天前的数据，开始日期不能大于当前日期30天")

            if int(flag) == 1:  # 时序交易数据
                result, temp_map_dict = await self.get_series_info(start_day, end_day, int(type_))

            else:  # 非时序交易数据
                result, temp_map_dict = await self.get_non_series_info(start_day, end_day, int(type_))

            # 手动分页
            if page and page_size and len(result) > int(page_size):
                total_pages = math.ceil(len(result) / int(page_size))
                start_index = (int(page) - 1) * int(page_size)
                end_index = int(page) * int(page_size) if int(page) < total_pages else len(result) + 1

                data_ = {
                    "total": len(result),
                    "total_pages": total_pages,
                    "page": page,
                    "page_size": page_size,
                    "detail": result[start_index:end_index],
                    "maps": temp_map_dict
                }
            else:
                data_ = {
                    "total": len(result),
                    "detail": result,
                    "maps": temp_map_dict
                }

            user_session.close()
            self._getEncryptValue(data_)
            # return self.returnTypeSuc(data_)

        except ValueError as e:
            return self.customError(str(e).encode('utf8'))
        except Exception as e:
            logging.info(e)
            import traceback
            logging.error(traceback.print_exc())
            return self.requestError()

    def _getEncryptValue(self, data):

        if not data:  # 未传入数据
            return None
        if not isinstance(data, dict):
            return data

        value = json.dumps(data)

        dat = AESUtil.encryt(value, key, iv)
        dat = str(dat, encoding='utf-8')

        self.set_header('Bean', MD5Tool.get_str_md5(dat + openId))

        return self.returnTypeSuc(dat)

    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean', None)
        logging.info('Bean: %s ' % Bean)
        data = self.get_argument('data',None)
        logging.info('密文---data-------:%s ' %data)

        if not data:  # 未传入密文
            return False, self.customError('缺失参数：<data>')

        data = data.replace(' ', '+')

        md5 = MD5Tool.get_str_md5(data + zhili_openId)

        if not Bean or md5 != Bean:  # 身份验证失败
            return False, self.tokenError()

        data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
        logging.info('明文*****data*************:%s' % data)

        keys = data.keys()

        if 'type' not in keys or 'flag' not in keys:
            return False, self.customError('缺失参数：<type> or <flag>')

        if data['flag'] not in {'时序': 1, '非时序': 0}.values():
            return False, self.customError(f'参数<flag>：{data["flag"]}不合规')

        if data['flag'] == 1:
            if not data['type'] in range(3, 17):
                return False, self.customError('参数不合规: <type> 取值范围为[3~16]')
        else:
            if not data['type'] in range(3, 17):
                return False, self.customError("参数不合规: <type> 取值范围为[1~26]")

        logging.info('明文*****data*************:%s' % data)

        return True, data
