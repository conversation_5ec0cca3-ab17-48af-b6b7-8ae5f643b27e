package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 功率计划操作DTO
 */
@Data
@ApiModel("功率计划操作DTO")
public class PowerPlanOperationDTO {

    @ApiModelProperty(value = "记录ID", required = true)
    @NotNull(message = "记录ID不能为空")
    private Long id;

    @ApiModelProperty(value = "操作类型: stop-停止, delete-删除")
    private String operationType;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "语言")
    private String lang;
}
