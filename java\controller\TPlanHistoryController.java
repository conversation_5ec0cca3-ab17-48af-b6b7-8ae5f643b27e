package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryCreateDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryQueryDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryUpdateDTO;
import com.robestec.analysis.service.TPlanHistoryService;
import com.robestec.analysis.vo.TPlanHistoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计划历史记录管理API
 */
@RestController
@RequestMapping("/plan-history")
@RequiredArgsConstructor
@Api(tags = "计划历史记录管理API")
public class TPlanHistoryController {

    private final TPlanHistoryService tPlanHistoryService;

    @GetMapping
    @ApiOperation("分页查询计划历史记录")
    public PageResult<TPlanHistoryVO> queryTPlanHistory(@Validated TPlanHistoryQueryDTO queryDTO) {
        return tPlanHistoryService.queryTPlanHistory(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增计划历史记录")
    public Result<Long> createTPlanHistory(@Validated @RequestBody TPlanHistoryCreateDTO createDTO) {
        return Result.succeed(tPlanHistoryService.createTPlanHistory(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增计划历史记录")
    public Result createTPlanHistoryList(@Validated @RequestBody List<TPlanHistoryCreateDTO> createDTOList) {
        tPlanHistoryService.createTPlanHistoryList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改计划历史记录")
    public Result updateTPlanHistory(@PathVariable Long id, @Validated @RequestBody TPlanHistoryUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tPlanHistoryService.updateTPlanHistory(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除计划历史记录")
    public Result deleteTPlanHistory(@PathVariable Long id) {
        tPlanHistoryService.deleteTPlanHistory(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取计划历史记录详情")
    public Result<TPlanHistoryVO> getTPlanHistory(@PathVariable Long id) {
        return Result.succeed(tPlanHistoryService.getTPlanHistory(id));
    }

    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询计划历史记录")
    public Result<List<TPlanHistoryVO>> getTPlanHistoryByUserId(@PathVariable Long userId) {
        return Result.succeed(tPlanHistoryService.getTPlanHistoryByUserId(userId));
    }

    @GetMapping("/status/{status}")
    @ApiOperation("根据状态查询计划历史记录")
    public Result<List<TPlanHistoryVO>> getTPlanHistoryByStatus(@PathVariable Integer status) {
        return Result.succeed(tPlanHistoryService.getTPlanHistoryByStatus(status));
    }

    @GetMapping("/station/{station}")
    @ApiOperation("根据电站名称查询计划历史记录")
    public Result<List<TPlanHistoryVO>> getTPlanHistoryByStation(@PathVariable String station) {
        return Result.succeed(tPlanHistoryService.getTPlanHistoryByStation(station));
    }

    @GetMapping("/count/user/{userId}")
    @ApiOperation("统计用户的计划历史记录数量")
    public Result<Long> countByUserId(@PathVariable Long userId) {
        return Result.succeed(tPlanHistoryService.countByUserId(userId));
    }

    @GetMapping("/count/status/{status}")
    @ApiOperation("统计指定状态的计划历史记录数量")
    public Result<Long> countByStatus(@PathVariable Integer status) {
        return Result.succeed(tPlanHistoryService.countByStatus(status));
    }
}
