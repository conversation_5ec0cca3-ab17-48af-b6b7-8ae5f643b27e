#!/usr/bin/env python
# coding=utf-8
#@Information:智锂物联历史数据
#<AUTHOR> WYJ
#@Date         : 2022-10-18 09:38:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\foreign\zhilHisHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-18 09:38:39


import json,os
import requests
import logging
from sqlalchemy import func
import pandas as pd
from Application.Models.base_handler import BaseHandler
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import *
from Tools.DataEnDe.aes_cbc import AESUtil
from Tools.DataEnDe.MD5 import MD5Tool
from Application.Models.His.r_ACDMS import HisACDMS
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.DB.taicang_his import taicang_session
from Application.EqAccount.Foreign.zhilRealHandle import gz_no,zhili_openId,zhili_key,zhili_iv,openId,key,iv,_station_name
from Application.Models.User.for_zhil_coupleback import ForCouplebackR
from Application.Models.User.user import User
from Application.HistoryData.his_bams import bmsdb

class ZhiLHisHandleIntetface(BaseHandler):
    # @tornado.web.authenticated
    def post(self, kt):
        # self.refreshSession()  # 刷新session
        try:
            if kt == 'DcContStatus':  # 继电器状态
                f,data = self._verifyToken()
                if not f:
                    return data
                db = data['db']  # 具体电站
                name = "%s%s.Lp%s.DcContAllowRk%s"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                # name = "tpSttaicang.PcsSt%s.Lp%s.DcContAllowRk%s"%(data['pcsSt'],data['lp'],data['bank'])
                return self._getValue(data,'ods_r_status1',name,db)
                
            elif kt == 'ACInrTmp':  # 空调温度
                f,data = self._verifyToken()
                if not f:
                    return data
                db = data['db']  # 具体电站
                name = "%s%s.Lp%s.HVAC%s_Temp"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                # name = "tpSttaicang.PcsSt%s.Lp%s.HVAC%s_Temp"%(data['pcsSt'],data['lp'],data['bank'])
                return self._getValue(data,'ods_r_measure1',name,db)
                
            elif kt == 'FaultStats':  # 故障字
                f,data = self._verifyToken()
                if not f:
                    return data
                if 'code' not in data.keys():
                    return self.customError('非法参数')
                elif data['code'] not in gz_no.keys():
                    return self.customError('参数不合规')
                db = data['db']  # 具体电站
                # name = "tpSttaicang.PcsSt%s.Lp%s.Rk%s%s"%(data['pcsSt'],data['lp'],data['bank'],gz_no[data['code']])
                name = "%s%s.Lp%s.Rk%s%s"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'],gz_no[data['code']])
                return self._getValue(data,'ods_r_status1',name,db)
            elif kt == 'fault':  # 预测结果存储
                head = self.request.headers
                Bean = head.get('Bean',None)
                logging.info('Bean:%s'%Bean)
                data = self.get_argument('data',None)
                logging.info('结果---data-------:%s'%data)
                md5 = MD5Tool.get_str_md5(data+zhili_openId)
                if not Bean or md5 != Bean:  # 身份验证失败
                    return self.tokenError()
                data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
                db = data['db']  # 电站
                if 'workNet' in data.keys():  # 工商业特有
                    weizhi = '%s-%s'%(data['workNet'],fill0(data['bank']))
                else:
                    pcs = data['pcsSt']
                    lp = data['lp']
                    d=''
                    if db == 'binhai' or db == 'taicang' or db == 'guizhou' or db == 'shgyu':
                        d = (pcs-1)*2 +lp
                    elif db == 'zgtian' or db == 'datong':
                        d = (pcs-1)*4 +lp
                    elif db == 'ygqn':
                        d = (pcs-1)*2 +lp
                        # d = (pcs-1)*10 +lp
                    elif db == 'taicgxr':
                        d = (pcs - 1) * 2 + lp
                        # if pcs <= 20:
                        #     d = (pcs-1)*2 +lp
                        # else:
                        #     d = (pcs-1-20)*14 + lp + 40
                    elif db == 'ygzhen':
                        if pcs == 1:
                            d=lp
                        else:
                            d = 4 +lp
                    weizhi = '堆%s-簇%s'%(fill0(d),fill0(data['bank']))
                user_session.commit()
                alarm_type = data['alarmType'] if data['alarmType'] else 1  # 告警等级1,2,3
                fr = user_session.query(ForCouplebackR).filter(ForCouplebackR.fault_no == data['faultNo']).first()
                if not fr:  # 不存在
                    e = ForCouplebackR(pcs=weizhi,fault_no=data['faultNo'],fault_info=data['faultInfo'],fault_duration=data['faultDuration'],fault_location=data['faultLocation'],
                    advice=data['advice'],start_time=data['startTime'],end_time=data['endTime'],status=data['status']+1,ignore=data['isIgnore']+1,op_ts=timeUtils.getNewTimeStr(),
                    station=db,send=0,handle_f=1,feed_back=1,alarm_type = alarm_type,fault_code=data['faultCode'],fault_title=data['faultTitle'])
                    user_session.add(e)
                    user_session.commit()
                else:
                    logging.info('update-------------')
                    fr.pcs = weizhi
                    fr.fault_info = data['faultInfo']
                    fr.fault_duration = data['faultDuration']
                    fr.fault_location = data['faultLocation']
                    fr.advice = data['advice']
                    fr.start_time = data['startTime']
                    fr.end_time = data['endTime']
                    fr.status = data['status']+1
                    fr.alarm_type = alarm_type
                    user_session.commit()
                dat = AESUtil.encryt("", key, iv)
                dat = str(dat,encoding='utf-8')
                self.set_header('Bean', MD5Tool.get_str_md5(dat+openId)) 
                return self.returnTypeSuc(dat)
            elif kt == 'FeedBack':  # 结果反馈
                fault_no = self.get_argument('fault_no',None)  # 预警编号  唯一，作为主键使用
                back_info = self.get_argument('back_info','')  # 反馈意见
                hand_info = self.get_argument('hand_info','')  # 处理意见
                read = int(self.get_argument('read',0))
                ignore_handle = int(self.get_argument('ignore_handle',0)) # 选择释放抑制状态0保持现状，1否2是
                if not fault_no:
                    return self.customError('参数不完整')
                notime = timeUtils.getNewTimeStr()
                back_info = ';'.join(back_info.split()) if back_info else ''
                hand_info = ';'.join(hand_info.split()) if hand_info else ''
                logging.info('fault_no:%s,back_info:%s,hand_info:%s,read:%s,ignore_handle:%s'%(fault_no,back_info,hand_info,read,ignore_handle))
                data = {"faultNo":[fault_no],"updateTime":notime,"advice":hand_info,"status":read-1 if read else read}
               
                session = self.getOrNewSession()
                user = session.user
                dobj = {"handle_f":2,"feed_back":2,"read":read,"user_descr":user['name'],"hand_ts":notime,"back_info":back_info,"hand_info":hand_info}
                if ignore_handle:
                    dobj['ignore'] = ignore_handle
                    data['isignore'] = ignore_handle - 1
                # data = json.dumps(data,ensure_ascii=False)  # 保留汉字
                data = json.dumps(data)
                logging.info('data:%s'%data)
                user_session.query(ForCouplebackR).filter(ForCouplebackR.fault_no == fault_no).update(dobj)  # 更新对应处理状态
                user_session.commit()
                dat = AESUtil.encryt(json.dumps(data), key, iv)
                dat = str(dat,encoding="utf-8")
                return self._requestPost(dat,MD5Tool.get_str_md5(dat+openId))  # 调用发送机制
                
            elif kt == 'AllFaults':  # 查询结果
                data,filter = [],[]
                # startTime = self.get_argument('startTime',None)  # 开始时间
                pcs = self.get_argument('pcs',None)  # 设备编号
                station = self.get_argument('db',None)  # 电站
                Time = eval(self.get_argument('timeLine', '[]'))  # 时间
                status = int(self.get_argument('status',0))  # 预警状态  0全部1,未处理，2已处理
                # fault_info = self.get_argument('faultInfo',None)  # 预警原因
                ignore = int(self.get_argument('ignore',0))  # 是否抑制
                handle = int(self.get_argument('handle',0))  # 处理状态
                feed_back = int(self.get_argument('feedBack',0))  # 反馈状态
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                alarmType = int(self.get_argument('alarmType',3))  # 告警等级
                logging.info('pcs:%s,Time:%s,status:%s,feed_back:%s,ignore:%s,handle:%s,alarmType:%s,station:%s'%(pcs,Time,status,feed_back,ignore,handle,alarmType,station))
                if Time:
                    filter.append(ForCouplebackR.start_time.between(Time[0],Time[1]))
                if status:
                    filter.append(ForCouplebackR.status==status-1)
                if ignore :
                    filter.append(ForCouplebackR.ignore==ignore)
                if handle:  # 
                    filter.append(ForCouplebackR.handle_f==handle)
                if feed_back :  # 反馈
                    filter.append(ForCouplebackR.feed_back==feed_back)
                if pcs :  # 反馈
                    filter.append(ForCouplebackR.pcs.like('%' + pcs + '%'))
                if alarmType :  # 告警等级
                    filter.append(ForCouplebackR.alarm_type==alarmType)
                if station:
                    filter.append(ForCouplebackR.station==station)
                total = user_session.query(func.count(ForCouplebackR.fault_no)).filter(*filter).scalar()
                all = user_session.query(ForCouplebackR).filter(*filter).order_by(ForCouplebackR.fault_no.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for org in all:
                    o = eval(str(org))
                    o['fault_info'] = org.fault_info
                    o['fault_location'] = org.fault_location
                    data.append(o)
                self.returnTotalSuccess(data,total)
          
            elif kt == 'FaultsBatchVerify':  # 预警批量确认  只适用于一二级告警
                filter = []
                notime = timeUtils.getNewTimeStr()
                pcs = self.get_argument('pcs',None)  # 设备编号
                Time = eval(self.get_argument('timeLine', '[]'))  # 时间
                status = int(self.get_argument('status',0))  # 预警状态  预警状态(0全部（可不传）1,预警中，2已恢复)
                handle = int(self.get_argument('handle',0))  # 处理状态
                alarmType = self.get_argument('alarmType',1)  # 告警等级
                ignore = int(self.get_argument('ignore',0))  # 是否抑制筛选
                station = self.get_argument('db',None)  # 电站
                read = int(self.get_argument('read',1))  # 人工标识 1已处理2误报
                ignore_handle = int(self.get_argument('ignore_handle',0))  # 选择释放抑制状态0保持现状，1否2是
                feed_back = int(self.get_argument('feedBack',0))  # 反馈状态
                back_info = self.get_argument('back_info','')  # 反馈意见
                hand_info = self.get_argument('hand_info','')  # 处理意见
                logging.info("pcs:%s,Time:%s,status:%s,ignore:%s,handle:%s,feed_back:%s,hand_info:%s,read:%s,alarmType:%s,back_info:%s,ignore_handle:%s,station:%s"%(
                    pcs,Time,status,ignore,handle,feed_back,hand_info,read,alarmType,back_info,ignore_handle,station))
                if int(alarmType) == 3 or handle != 1:
                    return self.customError("入参不合理！")
                if Time:
                    filter.append(ForCouplebackR.start_time.between(Time[0],Time[1]))
                if status:
                    filter.append(ForCouplebackR.status==status-1)
                if ignore :
                    filter.append(ForCouplebackR.ignore==ignore)
                if handle:  # 
                    filter.append(ForCouplebackR.handle_f==handle)
                if feed_back :  # 反馈
                    filter.append(ForCouplebackR.feed_back==feed_back)
                if pcs :  # 反馈
                    filter.append(ForCouplebackR.pcs.like('%' + pcs + '%'))
                if alarmType :  # 告警等级
                    filter.append(ForCouplebackR.alarm_type==alarmType)
                if station:
                    filter.append(ForCouplebackR.station==station)
                session = self.getOrNewSession()
                user = session.user
                all = user_session.query(ForCouplebackR).filter(*filter).order_by(ForCouplebackR.fault_no.desc()).all()
                logging.info('select count is %s'%len(all))
                ass = []
                dobj = {"handle_f":2,"feed_back":2,"read":read,"user_descr":user['name'],"hand_ts":notime,"back_info":back_info,"hand_info":hand_info}
                if ignore_handle:
                    data = {"updateTime":notime,"advice":hand_info,"status":read-1,"isignore":ignore_handle-1}
                    dobj['ignore'] = ignore_handle
                else:
                    data = {"updateTime":notime,"advice":hand_info,"status":read-1}
                    
                for a in all:
                    ass.append(a.fault_no)
                    user_session.query(ForCouplebackR).filter(ForCouplebackR.fault_no == a.fault_no).update(dobj)  # 更新对应处理状态
                # user_session.commit()
                if ass:
                    data["faultNo"] = ass
                    # data = json.dumps(data,ensure_ascii=False)  # 保留汉字
                    data = json.dumps(data)
                    logging.info('piliang data:%s'%data)
                    dat = AESUtil.encryt(data, key, iv)
                    dat = str(dat,encoding='utf-8')
                    md = dat+openId
                    return self._requestPost(dat,MD5Tool.get_str_md5(md))  # 调用发送机制
                return self.returnTypeSuc("")
                
            else:
                return self.pathError()
        except ValueError as E:
            return self.customError(str(E).encode('utf8'))
        except Exception as E:
            logging.info(E)
            return self.requestError()
        finally:
            user_session.close()
   
     
       
    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean',None)
        logging.info('Bean:%s'%Bean)
        data = self.get_argument('data',None)
        logging.info('密文---data-------:%s'%data)
        md5 = MD5Tool.get_str_md5(data+zhili_openId)
        if not Bean or md5 != Bean:  # 身份验证失败
            return False ,self.tokenError()

        data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
        keys = data.keys()
        if 'pcsSt' not in keys or 'lp' not in keys or 'bank' not in keys or 'startTime' not in keys or 'endTime' not in keys:
            return False ,self.customError('非法参数')
        elif data['pcsSt']<1 or data['pcsSt']>4:
            return False ,self.customError('参数不合规')
        elif data['lp']<1 or data['lp']>2:
            return False ,self.customError('参数不合规')
        elif data['bank']<1 or data['bank']>6:
            return False ,self.customError('参数不合规')
        elif timeUtils.betweenDayNum(data['startTime'],data['endTime'])>30:
            return False ,self.customError('参数不合规')
        
        logging.info('明文*****data*************:%s'%data)
        return True ,data


    def _getValue(self,data,type,name,db):
        startTime = data['startTime']
        endTime = data['endTime']
        startss = timeUtils.timeStrToTamp(startTime)
        endss = timeUtils.timeStrToTamp(endTime)
        a = timeUtils.getBetweenMonth(startTime,endTime)
        al = type+pd.Series(a)
        # print '&&&&&&&&&&&&&',al.tolist()
        # print name, startss,endss
        db_con = self._db_return_con(db,name)
        times,value = [],[]
        for table in al.tolist():
            HisTable = HisACDMS(table)
            values = db_con.query(HisTable.value.label('value'),func.from_unixtime((HisTable.dts_s), "%Y-%m-%d %H:%i:%s").label('dts_s')
            ).filter(HisTable.name==name,HisTable.dts_s.between(startss,endss)).order_by(HisTable.dts_s.asc()).all()
            for val in values:
                value.append(val.value)
                times.append(str(val.dts_s))
        db_con.close()
        dat = AESUtil.encryt(str({"value":value,"time":times}), key, iv)
        dat = str(dat,encoding='utf-8')
        self.set_header('Bean', MD5Tool.get_str_md5(dat+openId)) 
        return self.returnTypeSuc(dat)

    def _requestPost(self,post_json,bean):
        header = {
            "accept":"*/*",
            "user-agent":"ApiPOST Runtime +https://www.apipost.cn",
            # "Content-Type": "application/x-www-form-urlencoed",
            "accept-encoding":"gzip, deflate, br",
            "accept-language":"zh-CN",
            "Connection":"keep-alive",
            "Bean":bean,
        }
        url = "http://************:82/yc/HisData/faultIgnore"
        # 公网服务器内网ip  https://rframe.robestec.cn/yc/HisData/faultIgnore
        # print 'header:',header,type(header)
        # print ('post_json:',post_json,'----bean:',bean)
        r1 = requests.post(url,data={"data":post_json},headers=header)
        logging.info('post send feedback is %s'%r1.text)
        return self.returnTypeSuc("")
    
    def _db_return_con(self,db,name):
        '''返回数据库链接'''
        if db == 'binhai' or db == 'ygzhen':
            i = int(name.split('.')[0][-1])
            return bmsdb[db][i-1]
