import datetime

from rest_framework import serializers
from apis.work_order import models
from .user_serializers import UserInfoSerializer
from django.core.validators import RegexValidator


class CustomInfoSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    tel = serializers.CharField(required=True,
                                validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])

    class Meta:
        model = models.CustomInfoModel
        fields = '__all__'


class ProjectInfoSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.ProjectInfoModel
        fields = '__all__'


class WorkOrderStationInfoSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.WorkOrderStationInfoModel
        fields = '__all__'


class ExecutorSerializer(serializers.ModelSerializer):
    exec_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', default=datetime.datetime.now())

    class Meta:
        model = models.ExecutorModel
        fields = '__all__'


class WorkOrderCompExecutorSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.WorkOrderCompExecutorModel
        exclude = ('create_time',)


class WorkOrderCompSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.WorkOrderCompModel
        exclude = ('create_time',)


class WorkOrderSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    custom_id = CustomInfoSerializer()
    project_id = ProjectInfoSerializer()
    stations = WorkOrderStationInfoSerializer(many=True, read_only=True)
    # user_id = UserInfoSerializer()
    executor_id = ExecutorSerializer()
    c_executor = WorkOrderCompExecutorSerializer()
    user = serializers.StringRelatedField(label='创建人', read_only=True)

    class Meta:
        model = models.WorkOrderModel
        fields = '__all__'


class ExecContentSerializer(serializers.ModelSerializer):
    finish_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.ExecContentModel
        fields = '__all__'


class WorkOrderScheduleSerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    first_check_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    exec_id = ExecutorSerializer()
    exec_content_id = ExecContentSerializer()
    # user_id = UserInfoSerializer()
    user_id = serializers.StringRelatedField(label='创建人', read_only=True)

    class Meta:
        model = models.WorkOrderScheduleModel
        fields = '__all__'


class DraftWorkOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.DraftWorkOrderModel
        fields = '__all__'


class WorkOrderAuthoritySerializer(serializers.ModelSerializer):
    create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.WorkOrderAuthorityModel
        fields = '__all__'
