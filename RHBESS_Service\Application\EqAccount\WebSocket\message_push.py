#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-18 14:03:10
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WebSocket\message_push.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-18 17:32:36


import datetime

from sqlalchemy import and_
from Tools.DB.redis_con import r

from Application.Models.User.organization import Organization
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.User.user import User
from Application.Models.WebSocket.user_message import UserMessage
from Application.Models.WebSocket.message import Message


def authenticated_user(sender_id, accept_org):
    """
    检验发送者，是否有为组织推送消息的权限
    :sender_id 发送者id
    :accept_org 推送组织
    """
    sender_user = user_session.query(User).filter(User.id == sender_id).first()
    user_lower_org = sender_user.organization_U.get_lower_node()
    if accept_org in user_lower_org:
        return True
    return False


def push_user_message(user_id, content, msg_type, op_ts, push_type='new_msg'):
    """
    产生新消息为具体用户推送
    : user_id 用户id
    : content 消息内容 str
    : msg_type 消息类型 error/info/success
    : op_ts 消息时间 datetime
    : push_type 推送消息类型  默认new_msg(新消息）/notification(广播公告) 前端处理方式不同
    """
    message = Message(descr=content, type=msg_type, op_ts=op_ts)
    user_session.add(message)
    user_session.commit()
    user_msg = UserMessage(user_id=user_id, message_id=message.id, read=False)
    user_session.add(user_msg)
    user_session.commit()
    r.publish("rhbess_ws", str(user_msg.id)+'+'+push_type)
    


def push_role_message(role_id, org_id, content, msg_type, op_ts, push_type='new_msg', org_lower=False):
    """
    产生新消息为区域角色推送
    : role_id 角色id
    : org_id 组织id
    : content 消息内容 str
    : msg_type 消息类型 error/info/success
    : op_ts 消息时间 datetime
    : push_type 推送消息类型  默认new_msg(新消息）/notification(广播公告) 前端处理方式不同
    : org_lower 标识推送只推送org_id这一级，还是属于该组织下的组织都推送默认不推送
    """
    org = user_session.query(Organization).filter(Organization.id == org_id).first()
    if not org:
        return u'组织不存在'
    if org_lower:
        org_list = org.get_lower_node()
    else:
        org_list = [org_id, ]
    users = user_session.query(User.id).filter(and_(User.user_role_id == role_id, User.organization_id.in_(org_list))).all()
    message = Message(descr=content, type=msg_type, op_ts=op_ts)
    user_session.add(message)
    user_session.commit()
    for user_id in users:
        user_msg = UserMessage(user_id=user_id[0], message_id=message.id, read=False)
        print (message.id, user_id,)
        user_session.add(user_msg)
        user_session.commit()
        r.publish("rhbess_ws", str(user_msg.id)+'+'+push_type)

if __name__ == '__main__':
    push_user_message(1, '测试', 'info', datetime.datetime.now())
    # push_role_message('1', '180', '群发', 'info', datetime.datetime.now(), 'new_msg', True)
