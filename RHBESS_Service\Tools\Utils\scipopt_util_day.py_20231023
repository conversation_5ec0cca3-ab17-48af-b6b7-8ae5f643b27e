#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-10 11:21:35
#@FilePath     : \RHBESS_Service\Tools\Utils\scipopt_util_day.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-31 11:03:19

from pyscipopt import Model
import numpy as np
import math

def calrate(p_max,eff_charge,eff_discharge,price,w_day,H0,SS,H1):
    '''
    构造求解函数求解一天的最佳充放电量
    '''
    m = Model('one_day')
    m.hideOutput( quiet = True )  # 隐藏打印
    
    p_chag = [m.addVar(vtype="C", lb=0, ub=p_max) for i in range(24)] #定义变量  充电功率
    p_disg = [m.addVar(vtype="C", lb=0, ub=p_max) for i in range(24,48)]  # 放电功率
    f_chag = [m.addVar(vtype="I", lb=0, ub=1) for i in range(48,72)] # 充电标识
    f_disg = [m.addVar(vtype="I", lb=0, ub=1) for i in range(72,96)]  # 放电标识
    
    a_ub = np.tril(np.ones((24,24)),0)*eff_charge*p_chag-np.tril(np.ones((24,24)),0)/eff_discharge*p_disg  # A端下三角充放容量
    
    # soc = 0
    for i in range(24):  # 添加soc约束
        m.addCons(np.sum(a_ub[i])>=0) 
        m.addCons(np.sum(a_ub[i])<=SS) #添加约束条件
        
        # 添加充放电标识约束
        m.addCons(f_chag[i]+f_disg[i]<=1)
        m.addCons(f_chag[i]+f_disg[i]>=0)

        # 充放电约束上限&用户侧负荷曲线
        # 充电约束
        m.addCons(p_chag[i]>=0)
        m.addCons(p_chag[i]<=f_chag[i]*p_max)
        # 放电约束
        m.addCons(p_disg[i]>=0)
        m.addCons(p_disg[i]<=f_disg[i]*p_max)

        # 用户侧充电上限
        m.addCons(p_chag[i]<=(float(H1/H0)-w_day[i])*H0)
        # 用户侧放电上限
        m.addCons(p_disg[i]<=w_day[i]*H0)

        # 添加价格约束
        # m.addCons(price[i]*(p_disg[i] - p_chag[i])-base_price*p_disg[i]>=0)

    func = 0
    for i in range(24):
        func = func + price[i] * (p_disg[i] - p_chag[i] )
    # m.addCons(func = 0)
    m.setObjective(-func) #设定目标优化函数
    m.optimize() #启动求解器
    avl = m.getObjVal()
    result_chag,result_disg,char_f,disg_f = [],[],[],[]
    resultsum = []
    
    for i in range(24):
        result_chag.append(round(m.getVal(p_chag[i]),4)) #获取最优解时变量x的值
        result_disg.append(round(m.getVal(p_disg[i]),4))
        char_f.append(round(m.getVal(f_chag[i]),4)) 
        disg_f.append(round(m.getVal(f_disg[i]),4))
        resultsum.append(round(m.getVal(p_chag[i]) + m.getVal(p_disg[i]),4))
   
    p_total = (-np.array(result_chag)+np.array(result_disg)).tolist() # 24小时的充放电功率
    # print 'p_total:',p_total
    # pp,all = 0,[0]
    # for p in p_total:
    #     if p<0:
    #         pp = pp-p*eff_charge  # 功率为负，补电
    #     elif p>0:
    #         pp = pp -p/eff_discharge  # 功率为正，放电
    #     all.append(round(pp,3))
    # soc = np.array(all)

    # 优化算法，做平滑处理
    y = 0
    a = [[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]]
    for ind in range(24):
        p = price[ind]
        if ind == 0:
            l_v = p  # 上一个值
            a[0].append(p)
        else:
            if p != l_v:
                y = y+1  # 更新上一个值
            l_v = p
            a[y].append(p)
    all = []
    l = 0
    for ar in a:
        if ar:
            le = len(ar)
            arr = p_total[l:le+l]
            if max(arr)<=0:  # 都是负值
                arr = sorted(arr)
            else:  # 都是正值
                arr = sorted(arr,reverse=True)
            all = all+arr
            l = l+le
    # print 'all:',all
    chag_cost = np.sum(np.array(result_chag)*np.array(price,dtype='float64'))  # 充电费用
    disg_cost = np.sum(np.array(result_disg)*np.array(price,dtype='float64'))  # 放电费用
    # print price
    # print chag_cost,disg_cost,avl,'*********'
    
    return sum(result_chag),sum(result_disg),chag_cost,disg_cost,avl,(np.around(np.array(all)/1000,2)).tolist()
    

# eff_charge = 0.92  # 充电效率
# eff_discharge = 0.92  # 放电效率
# p_max = 5000  #交流侧最大功率（kW）pcs功率，推荐功率
# Cycle = 0.5  #系统设计循环倍率X
# H0 = 5000  #变压器容量/最大需量 KVA
# DOD = 0.95  #充放深度
# s_max = 8000;  #直流侧最大容量（kWh）
# SS = s_max*DOD  # 直流侧有效容量

# # 一天的电价
# # price=[0.3707,0.3707,0.3707,0.3707,0.3707,0.3707,0.7123,0.7123,1.1222,1.1222,1.1222,0.7123,0.7123,0.7123,0.7123,0.7123,0.7123,0.7123,1.1222,1.1222,1.1222,0.7123,0.3707,0.3707]  # 4月
# price = [0.2916,0.2916,0.2916,0.2916,0.2916,0.2916,0.6854,0.6854,1.2105,1.2105,1.2105,1.2105,1.5058,1.5058,1.2105,0.6854,0.6854,0.6854,1.2105,1.2105,1.2105,0.6854,0.2916,0.2916]  # 7月
# # price = [0.3254,0.3254,0.3254,0.3254,0.3254,0.3254,0.7698,0.7698,1.3624,1.3624,1.3624,1.3624,1.3624,1.3624,1.3624,0.7698,0.7698,0.7698,1.3624,1.3624,1.3624,0.7698,0.3254,0.3254]  # 9月
# # price = [0.3309,0.3309,0.3309,0.3309,0.3309,0.3309,0.7835,0.7835,1.3870,1.3870,1.3870,0.7835,0.7835,0.7835,0.7835,0.7835,0.7835,0.7835,1.3870,1.7265,1.7265,0.7835,0.3309,0.3309]  # 12月

# # base_price= 0.25  # 放电价差，0.3基数除放电效率
# w_day = np.ones((24,1))*0.5  # 一天的负荷

# calrate(p_max,eff_charge,eff_discharge,price,w_day,H0,SS)


def power_calculate(Y,Peak_rate,High_rate,Normal_rate,Low_rate,Power_Fix,Demand_flage,Battery_hour):
    '''功率计算
    Y:一年电价标识矩阵
    Peak_rate:尖负荷率 小数
    High_rate:峰负荷率
    Normal_rate:平负荷率
    Low_rate:谷负荷率  
    Power_Fix:变压器容量MVA
    Demand_flage:1按容收费 2 按需收费
    Battery_hour:储能时长，小时
    '''
    a = np.array(Y)
    X = np.diff(a)  # 后一个值减去前一个值
    
    #逐月尖峰标识统计
    A = np.zeros((12,1)) #早峰时长
    AA = np.zeros((12,1)) #早尖时长
    B = np.zeros((12,1))  #晚峰时长
    BB = np.zeros((12,1))  #晚尖时长
    chargeC = np.zeros((12,1))  #午间平充电时长
    chargeD = np.zeros((12,1))  #午间谷充电时长
    
    for j in range(12):
        c,d = 0,0
        S=[]
        x=X[j] #;%第j月的峰谷24h标识
        if max(Y[j][:15])>=1:  # 截止到14点有高峰执行此代码
            for i in range(23):
                if x[i]<0:
                    S.append(i+1)
            # S2 = [S(1),S(size(S,2))];  # S第一个元素和S列数
            # S2 = [S[0],np.array(S).shape[1]]  # 第一个个最后一个元素，这点不确定
            
            for i in range(S[0]):
                if Y[j][i] ==1:
                    A[j][0] =A[j][0]+1
            
            for i in range(S[0]):
                if Y[j][i] ==2:
                    AA[j][0] =AA[j][0]+1

            for i in range(S[0],S[-1]):
                if Y[j][i] ==1:
                    B[j][0] =B[j][0]+1
                
            for i in range(S[0],S[-1]):
                if Y[j][i] ==2:
                    BB[j][0] =BB[j][0]+1;
            
            if S[1]-S[0] != 1 :
                st,ed = S[0]-1,S[1]
            else:
                st,ed = S[1]-1,S[2]
            for i in range(st,ed):
                if Y[j][i] == 0:
                    c =  c+1
                elif Y[j][i] == -1:
                    d = d+1
            chargeC[j][0] = c
            chargeD[j][0] = d
    Time_discharge_charge2 = [[],[],[],[],[],[],[],[],[],[],[],[]]
    Time_discharge_charge1 = [A,AA,B,BB,chargeC,chargeD]
    for i in range(12):
        for j in range(6):
            dat = Time_discharge_charge1[j]
            Time_discharge_charge2[i].append(dat[0][0])
    
    # print 'Time_discharge_charge2:',Time_discharge_charge2

    # 负荷
    # Peak_rate = 0.3  #%尖负荷率
    # High_rate = 0.4  #峰负荷率
    # Normal_rate = 0.5  #平负荷率
    # Low_rate = 0.5  #谷负荷率
    # Load_curve = Y  # 12行24列数据 计算负荷率
    # for i in range(12):
    #     for j in range(24):
    #         if Load_curve[i][j] == 2:
    #             Load_curve[i][j] = Peak_rate
    #         elif Load_curve[i][j] == 1:
    #             Load_curve[i][j] = High_rate
    #         elif Load_curve[i][j] == 0:
    #             Load_curve[i][j] = Normal_rate
    #         elif Load_curve[i][j] == -1:
    #             Load_curve[i][j] = Low_rate
    # print Load_curve

    if Demand_flage == 1:  # 按荣收费
        Power_max = max([Peak_rate,High_rate,Normal_rate,Low_rate])*Power_Fix;
    else:
        Power_max = Power_Fix
    Power_time0 = [High_rate*Power_Fix,Peak_rate*Power_Fix,High_rate*Power_Fix,Peak_rate*Power_Fix,Normal_rate*Power_Fix,Low_rate*Power_Fix]
    Power_time1 = Power_time0
    Power_time2 = np.zeros((12,6))
    ii = []
    for i in Power_time1[-2:]:  # 最后两个元素减去最大功率
        ii.append(i-Power_max)
    Power_time1[-2:] = ii

    for i in range(12):  # 转换成二维矩阵
        Power_time2[i] = Power_time1
    
    # print 'Power_time2:',Power_time2
    # Battery_hour = 2
    Check0 = Time_discharge_charge2*Power_time2
    double_normal_charge_flage = 1
    discharge_morning_hour,discharge_afternoon_hour,charge_noon_hour = np.zeros((12,1)),np.zeros((12,1)),np.zeros((12,1))
    for i in range(12):
        discharge_morning_hour[i][0] = Time_discharge_charge2[i][0]+Time_discharge_charge2[i][1]
        discharge_afternoon_hour[i][0] = Time_discharge_charge2[i][2]+Time_discharge_charge2[i][3]
        if double_normal_charge_flage == 1:
            charge_noon_hour[i][0] = Time_discharge_charge2[i][4]+Time_discharge_charge2[i][5]
        else:
            charge_noon_hour[i][0] = Time_discharge_charge2[i][5]
   
    Time_check = [[],[],[],[],[],[],[],[],[],[],[],[]]
    Time_check2 = [discharge_morning_hour,discharge_afternoon_hour,charge_noon_hour]
    for i in range(12):
        for j in range(3):
            dat = Time_check2[j]
            Time_check[i].append(dat[0][0])
    # print 'Time_check:',Time_check
    Time_check = np.array(Time_check)
    min_Time_check =np.min(Time_check);
    idx = np.argwhere(Time_check == min_Time_check)  # 获取最小值索引
    # print '--------',idx
    yy = idx[0][1]
    # print '*******',yy
    discharge_morning_room,discharge_afternoon_room,charge_noon_room = np.zeros((12,1)),np.zeros((12,1)),np.zeros((12,1))
    for i in range(12):
        discharge_morning_room[i][0] = Check0[i][0]+Check0[i][1]
        discharge_afternoon_room[i][0] = Check0[i][2]+Check0[i][3]
        if double_normal_charge_flage == 1:
            charge_noon_room[i][0] = Check0[i][4]+Check0[i][5]
        else:
            charge_noon_room[i][0] = Check0[i][5]
    Room_check = [[],[],[],[],[],[],[],[],[],[],[],[]]
    Room_check2 = [discharge_morning_room,discharge_afternoon_room,charge_noon_room]
    for i in range(12):
        for j in range(3):
            dat = Room_check2[j]
            Room_check[i].append(dat[0][0])
    # print 'Room_check:',Room_check
    l = []
    for i in range(12):
        l.append(Time_check[i][yy])
        
    min_v = min(min(l),Battery_hour)
    
    Room_check = np.array(Room_check)
    min_Power_check = np.min(np.abs(Room_check/min_v))
    min_Power_check = float(min_Power_check)
    s_max = 0
    if np.isnan(min_Power_check):  # 没有值 没有早高峰
        rp1 = Power_Fix*High_rate
        rp2 = Power_Fix*(1-Low_rate)
        min_Power_check = rp1 if rp1<rp2 else rp2  # 交流测PCS最大功率
        s_max = min_Power_check*Battery_hour  # 电池标定容量 min_Power_check/(1/Battery_hour)
    else:
        min_Power_check = min_Power_check
        s_max = min_Power_check*Battery_hour
        
    # formatSpec1 = "最优配置功率： %s MW"%min_Power_check;
    # formatSpec2 = "最优配置容量： %s MWh"%(min_Power_check*Battery_hour);
    # if Battery_hour>min_Time_check:
    #     print ('储能配置时长过长')
    # else:
    #     if Battery_hour<min_Time_check:
    #         print('储能配置时长较短')
    #     else:
    #         print('储能配置时长合适')
    return min_Power_check,s_max

# Y =[[-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 2, 2, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 2, 2, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 2, 2, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [-1, -1, -1, -1, -1, -1, -1, 0, 1, 1, 1, -1, -1, 0, 0, 0, 0, 1, 2, 2, 1, 1, 1, 0]]

# Peak_rate = 0.3  #%尖负荷率
# High_rate = 0.4  #峰负荷率
# Normal_rate = 0.5  #平负荷率
# Low_rate = 0.5  #谷负荷率  
# Power_Fix = 5
# Demand_flage = 2  #容量电费  1按容收费 2 按需收费
# Battery_hour = 4

# print power_calculate(Y,Peak_rate,High_rate,Normal_rate,Low_rate,Power_Fix,Demand_flage,Battery_hour)


def power_calculate2(Y,price_12month,Peak_rate,High_rate,Normal_rate,Low_rate,Power_Fix,Demand_flage,Battery_hour,DOD,eff_charge,eff_discharge,Best_Mode,EMC_Rate):
    '''功率计算
    Y：峰平谷标识，二维矩阵,
    price_12month：电价矩阵,
    Peak_rate：尖峰负荷,
    High_rate：峰负荷,
    Normal_rate：平负荷,
    Low_rate：谷负荷,
    Power_Fix：变压器容量 MWA,
    Demand_flage：#  1按容收费 2 按需收费,
    Battery_hour：储能市场,
    DOD：放电深度,
    eff_charge：充电深度,
    eff_discharge：放电深度
    Best_Mode:1 投资回收最快，2总收益最大化
    EMC_Rate:分享比例
    '''
    
    if Demand_flage == 2:  # 按需
        Power_max = max([Peak_rate,High_rate,Normal_rate,Low_rate])*Power_Fix
    else:
        Power_max = Power_Fix

    Load_curve = np.zeros((12,24))
    # power_max_minus = np.zeros((12,24))
    for i in range(12):
        for j in range(24):
            if Y[i][j] == 2:
                Load_curve[i][j] = Peak_rate
            elif Y[i][j] == 1:
                Load_curve[i][j] = High_rate
            elif Y[i][j] == 0:
                Load_curve[i][j] = Normal_rate
                # power_max_minus[i][j] = -Power_max
            elif Y[i][j] == -1:
                Load_curve[i][j] = Low_rate
                # power_max_minus[i][j] = -Power_max
    # print 'Load_curve:',Load_curve
    # print 'YYYYYYYY:',Y
    Load_curve1 = Load_curve*Power_Fix
    # print 'Load_curve1000000---',Load_curve1
    # Power_room=Load_curve1 
    # Power_room = Power_room+power_max_minus
    # print 'Power_room:',Power_room

    # 最优化配置容量
    H0 = Power_Fix
    H1 = Power_max
    
    # price_charge_lim = 0.25  #充电阈值上限
    # Price_charge_lim = np.ones((24,1))*price_charge_lim  #%充电阈值上限
    # # 均衡充电要求
    # balance_charge_flage = 0
    ma_p = []
    for a in range(12):
        for b in range(24):
            if Y[a][b]>=1:  # 尖或峰
                ma_p.append(Load_curve1[a][b])

    Power_intital = max(ma_p)
    # print 'Power_intital:',Power_intital
   
    # step_long = round(Power_Fix/20.0,2)
    if Power_Fix<=2:
        step_long = 0.1  # 计算步长
    elif Power_Fix<=5:
        step_long = 0.25  # 计算步长
    elif Power_Fix<=10:
        step_long = 0.5  # 计算步长
    elif Power_Fix<=50:
        step_long = 1.0  # 计算步长
    elif Power_Fix<=100:
        step_long = 2.5  # 计算步长
    else:
        step_long = Power_Fix/40.0  # 计算步长


    step_times = int(math.ceil(Power_intital/step_long))  # 向上取整数
    
    Per_gain = np.zeros((step_times,12)) 
    Cycle_check = np.zeros((step_times,12))
    Judge_average_kWh_gain = np.zeros((step_times,3))
    # print 'step_times----------',step_times
    Rank = [0 for _ in range(step_times)]
    index_ = 0
    for i in range(step_times):
        arr = []
        for j in [0,3,6,9]:  # 只取具有代表性的1,4,7,10数据
            price = price_12month[j]  #'; %逐月电价]
            w_day = Load_curve[j]  # '; %逐月负荷率
            
            P_max = Power_intital - i*step_long
            Capacity = P_max*Battery_hour*DOD 
            # print 'P_max:',P_max

            # 充电电量;放电电量;充电成本;放电收入;--;功率
            Q_charge,Q_discharge,Cost,Income,cou,power = calrate(P_max,eff_charge,eff_discharge,price,w_day,H0,Capacity,H1)
            arr.append([P_max,eff_charge,eff_discharge,price,w_day,H0,Capacity,H1])
            # print 'power---',Q_charge,Q_discharge,Cost,Income,cou,power
            if Q_discharge == 0:
                best_per_gain = 0 
            else:
                best_per_gain = (Income-Cost)/Q_discharge*eff_discharge
                cycle_check = Q_discharge/eff_discharge/Capacity
            Per_gain[i][j] = best_per_gain
            Cycle_check[i][j] = cycle_check
        gain_1 = round(sum(Per_gain[i])/4,4)
        gain_3 = round(sum(Cycle_check[i])/4,4)
        Judge_average_kWh_gain[i][0]=gain_1
        Judge_average_kWh_gain[i][1]=P_max
        Judge_average_kWh_gain[i][2]=gain_3
        Rank[i] = round(gain_1*gain_3*EMC_Rate,4)
        if i>1 and Rank[i] == Rank[i-1]:
            index_ = i-1
            break
    
    # Rank_Max = max(Rank)
    # Rank_Max_index = []
    # for i in range(len(Rank)):  # 取最大值索引
    #     if Rank[i] == Rank_Max:
    #         Rank_Max_index.append(i)
    #         break;
    # print 'Rank_Max_index:',Rank_Max_index
    # index_ = min(Rank_Max_index)
   
    if Best_Mode == 2:  # 总收益最大化
        # Rank = np.array(Rank)
        m = np.max(Rank)
        if m<=0.85:
            return 0,0,'该条件不建议配置储能'
        index_ = np.min(np.where(Rank>0.85))
    
    Power_best = Judge_average_kWh_gain[index_][1]*1000
    Cycle_best = Judge_average_kWh_gain[index_][2]
    if Cycle_best<1:
        shichang = '储能配置时长过长'
    else:
        if Battery_hour<2:
            shichang = '储能配置时长较短' 
        else:
            shichang = '储能配置时长合适'
            
    return Power_best,Power_best*Battery_hour,shichang

# Y =[[-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 2, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 1, 2, 2, 1, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 1, 2, 2, 1, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, -1, -1], [-1, -1, -1, -1, -1, -1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 2, 0, -1, -1]]

# price_12month = [[0.3324, 0.3324, 0.3324, 0.3324, 0.3324, 0.3324, 0.8309, 0.8309, 1.4956, 1.4956, 1.4956, 0.8309, 0.8309, 0.8309, 0.8309, 0.8309, 0.8309, 0.8309, 1.4956, 1.8695, 1.8695, 0.8309, 0.3324, 0.3324], [0.4153, 0.4153, 0.4153, 0.4153, 0.4153, 0.4153, 0.8306, 0.8306, 1.3290, 1.3290, 1.3290, 0.8306, 0.8306, 0.8306, 0.8306, 0.8306, 0.8306, 0.8306, 1.3290, 1.3290, 1.3290, 0.8306, 0.4153, 0.4153], [0.4066, 0.4066, 0.4066, 0.4066, 0.4066, 0.4066, 0.8131, 0.8131, 1.3010, 1.3010, 1.3010, 0.8131, 0.8131, 0.8131, 0.8131, 0.8131, 0.8131, 0.8131, 1.3010, 1.3010, 1.3010, 0.8131, 0.4066, 0.4066], [0.4107, 0.4107, 0.4107, 0.4107, 0.4107, 0.4107, 0.8214, 0.8214, 1.3142, 1.3142, 1.3142, 0.8214, 0.8214, 0.8214, 0.8214, 0.8214, 0.8214, 0.8214, 1.3142, 1.3142, 1.3142, 0.8214, 0.4107, 0.4107], [0.3832, 0.3832, 0.3832, 0.3832, 0.3832, 0.3832, 0.7663, 0.7663, 1.2261, 1.2261, 1.2261, 0.7663, 0.7663, 0.7663, 0.7663, 0.7663, 0.7663, 0.7663, 1.2261, 1.2261, 1.2261, 0.7663, 0.3832, 0.3832], [0.3809, 0.3809, 0.3809, 0.3809, 0.3809, 0.3809, 0.7618, 0.7618, 1.2189, 1.2189, 1.2189, 0.7618, 0.7618, 0.7618, 0.7618, 0.7618, 0.7618, 0.7618, 1.2189, 1.2189, 1.2189, 0.7618, 0.3809, 0.3809], [0.2978, 0.2978, 0.2978, 0.2978, 0.2978, 0.2978, 0.7446, 0.7446, 1.3403, 1.3403, 1.3403, 1.3403, 1.6754, 1.6754, 1.3403, 0.7446, 0.7446, 0.7446, 1.3403, 1.3403, 1.3403, 0.7446, 0.2978, 0.2978], [0.3080, 0.3080, 0.3080, 0.3080, 0.3080, 0.3080, 0.7701, 0.7701, 1.3862, 1.3862, 1.3862, 1.3862, 1.7327, 1.7327, 1.3862, 0.7701, 0.7701, 0.7701, 1.3862, 1.3862, 1.3862, 0.7701, 0.3080, 0.3080], [0.3316, 0.3316, 0.3316, 0.3316, 0.3316, 0.3316, 0.8290, 0.8290, 1.4922, 1.4922, 1.4922, 1.4922, 1.4922, 1.4922, 1.4922, 0.8290, 0.8290, 0.8290, 1.4922, 1.4922, 1.4922, 0.8290, 0.3316, 0.3316], [0.4016, 0.4016, 0.4016, 0.4016, 0.4016, 0.4016, 0.8031, 0.8031, 1.2850, 1.2850, 1.2850, 0.8031, 0.8031, 0.8031, 0.8031, 0.8031, 0.8031, 0.8031, 1.2850, 1.2850, 1.2850, 0.8031, 0.4016, 0.4016], [0.4047, 0.4047, 0.4047, 0.4047, 0.4047, 0.4047, 0.8094, 0.8094, 1.2950, 1.2950, 1.2950, 0.8094, 0.8094, 0.8094, 0.8094, 0.8094, 0.8094, 0.8094, 1.2950, 1.2950, 1.2950, 0.8094, 0.4047, 0.4047], [0.3371, 0.3371, 0.3371, 0.3371, 0.3371, 0.3371, 0.8427, 0.8427, 1.5169, 1.5169, 1.5169, 0.8427, 0.8427, 0.8427, 0.8427, 0.8427, 0.8427, 0.8427, 1.5169, 1.8961, 1.8961, 0.8427, 0.3371, 0.3371]]
# #负荷曲线输入
# Peak_rate = 0.25  #%尖负荷率
# High_rate = 0.68  #峰负荷率
# Normal_rate = 0.5  #平负荷率
# Low_rate = 0.2  #谷负荷率

# Power_Fix = 5  #变压器容量
# Demand_flage = 2  #  1按容收费 2 按需收费

# Battery_hour = 2
# DOD = 0.95   #充放深度
# eff_charge = 0.92
# eff_discharge = 0.92
# Best_Mode = 1  # 1为度电收益最佳的最大容量（投资回收最快）2为满足收益率要求下的最大容量（总收益最大化）
# EMC_Rate = 0.85  #;%EMC分享比例              
# import time
# print 'start---',time.time()
# print power_calculate2(Y,price_12month,Peak_rate,High_rate,Normal_rate,Low_rate,Power_Fix,Demand_flage,Battery_hour,DOD,eff_charge,eff_discharge,Best_Mode,EMC_Rate)

# print 'end-----',time.time()

