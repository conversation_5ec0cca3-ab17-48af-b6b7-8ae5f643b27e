#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-30 17:14:17
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_price_files.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-30 17:19:26


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecasePriceFiles(user_Base):
    u'电价原始表'
    __tablename__ = "t_side_forecase_price_files"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    province_name = Column(VARCHAR(50), nullable=False, comment=u"省份名称")
    year= Column(VARCHAR(20), nullable=False, comment=u"年，YYYY")
    month= Column(VARCHAR(5), nullable=False, comment=u"月")
    file_name = Column(String(256), nullable=True, comment=u"文件名称")
    file_path = Column(String(256), nullable=True, comment=u"文件路径加自定义命名")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
    def __repr__(self):
        
        return "{'id':%s,'province_name':'%s','year':%s,'month':%s,'file_name':%s,'file_path':%s}" % (
            self.id,self.province_name,self.year,self.month,self.file_name,self.file_path)
        
    