#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \em_pjt_rh\RHBESS_empower\Tools\TimeTask\license_client.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-27 10:37:06


'''
实时监控消息主题
实时值，所有模型，具体模型详情
'''
import sys
import os
import sys
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)
import logging
import paho.mqtt.client as mqtt
from Tools.DB.redis_con import r
from Application.Cfg.dir_cfg import model_config
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
logging.basicConfig(filename='../../log/brokermqtt.log', level=logging.DEBUG, format=LOG_FORMAT)

'''
订阅主题
'''
topics = ['manager/get/response/client/license']


def on_connect(client, userdata, flags, rc):
    '''连接成功回调'''
    print('Connected with result code '+str(rc))
    # 订阅主题
    for topic in topics:
        client.subscribe(topic)
    
def on_message(client, userdata, msg):
    '''消息接收回调'''
    topic = msg.topic  # 订阅主题
    data = msg.payload  # 返回值
    logging.info('topic:%s'%topic)
    logging.info('payload:%s'%data)
    r.hset('license_body','name',str(data))
    r.hset('license_body','index',1)
   

ip = str(model_config.get('broker', "ip"))
port = int(model_config.get('broker', "port"))
keepalive = int(model_config.get('broker', "keepalive"))

client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message

client.connect(ip, port, keepalive)
client.loop_forever()
