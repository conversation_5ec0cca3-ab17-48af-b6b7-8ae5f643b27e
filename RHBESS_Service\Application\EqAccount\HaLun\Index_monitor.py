#!/usr/bin/env python v
# coding=utf-8
#@Information: 关键指标监控

import math
from time import strptime
import time
from datetime import datetime,timedelta

from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.event import Event
from Application.Models.User.power_base_infos_t import PowerBaseInfos
from Application.Models.User.power_base_t import PowerBase
from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
from Application.Models.User.report_dongmu_f import FReportDongmu
from Application.Models.User.report_f import FReport
from Application.Models.User.report_pcs_f import FReportPcs
from Application.Models.User.sda_pcs_cu_name import SdaPcsCu
from Application.Models.User.station import Station
from sqlalchemy import or_, func
from Tools.DB.mysql_scada import DEBUG
from Tools.DB.mysql_user import user_session
from Application.HistoryData.his_bams import *
import configparser
import os
import pandas as pd
import pymysql
from Application.Cfg.dir_cfg import model_config
from dbutils.persistent_db import PersistentDB
from natsort import natsorted 

# 连接数据库
pool = PersistentDB(pymysql, 10,**{
            "host": model_config.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")
dongmu_num = 7
#查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = basepath + "/Cfg/test.ini"
model_config.read(path,encoding='utf-8')

def get_conf_name(db):
    if db == 'dongmu':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电,簇功率,簇SOC,模组最高电压,模组最低电压,
        # 模组最高温度,模组最低温度,BMS累计充电量,BMS累计放电量,BMS单体电压极差,BMS单体温度极差
        return 'soc_4', 'PCS_yougong_4', 'PCS_PF_4', 'PCS_jldy_4', 'PCS_jldl_4', 'PCS_zldy_4', 'PCS_zldl_4', 'PCS_T_4', 'PCS_Disg_4', 'PCS_Chag_4',\
               'PCS_ClusF_4','cu_CSOC_4','cu_MMaxV_4','cu_MMinV_4','cu_Mmaxt_4','cu_Mmint_4','cu_CuCha_4','cu_CuDis_4','cu_SVRan_4','cu_StRan_4'
#表名称
st_table_name= {'1':'dws_st_measure_win_15min','2':'dws_st_measure_1d','3':'dws_st_measure_1d','4':'dws_st_measure_cw_cm_cy'}
pcs_table_name= {'1':'dws_pcs_measure_win_15min','2':'dws_pcs_measure_cw_cm_cy','3':'dws_pcs_measure_cw_cm_cy','4':'dws_pcs_measure_cw_cm_cy','5':'dws_pcs_measure_win_1h_2h_6h'}
bc_table_name= {'1':'dws_bc_measure_win_15min','2':'dws_bc_measure_cw_cm_cy','3':'dws_bc_measure_cw_cm_cy','4':'dws_bc_measure_cw_cm_cy'}
#值名称
value_name= {'yougong': 'real_pw', 'pf': 'pf', 'SOC': 'soc', 'SOH': 'soh', 'ac_vol':'ac_vol','ac_cur':'ac_cur','dc_vol':'dc_vol','dc_cur':'dc_cur','bc_pw':'pw','vol_diff':'vol_diff','temp_diff':'temp_diff',
             'max_vol_diff':'max_vol_diff', 'max_temp_diff':'max_temp_diff','pcs_name':'pcs_name','temp':'temp',
             'pf_s_i':{'max_pf':'max_pf','min_pf':'min_pf'},'temp_s_i':{'max_temp':'max_temp','min_temp':'min_temp'},'bu_name':{'zh':'cluster_cname','en':'cluster_name'},
             'chagdisg':{'chag':'chag','disg':'disg','num':'chag_disg_times'}}

jiange_time='15T'
jiange_time_zhou='1 HOUR'
jiange_time_yue='2 HOUR'
jiange_time_nian='6 HOUR'

class IndexMonitorIntetface(BaseHandler):
    ''' 关键指标监测 '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        db = self.get_argument('db', None)  # 电站名称
        report_type = self.get_argument('report_type', '1')  # 1日，2周，3月，4年，5累计，6自定义时间
        start_Time = self.get_argument('start_Time', None)  # 开始时间
        end_Time = self.get_argument('end_Time', None)  # 结束时间
        old_end_Time = end_Time + ' 23:59:59'
        if DEBUG:
            logging.info('report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
        startTime = start_Time + ' 00:00:00'
        endTime_ = timeUtils.returnRealTime(end_Time)
        endTime = endTime_ + ' 23:59:59'
        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
        ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
        from datetime import datetime, timedelta
        try:
        # if 1:
            obj = {}
            obj['time'] = []
            obj['data'] = []
            if db =='dongmu':
                now_time = timeUtils.getNewTimeStr()
                if kt == 'GetAlarmR':  # 三级故障和报警的数据
                    report_type = self.get_argument('report_type', '1')  # 类型
                    db = self.get_argument('db', None)  # 电站名称
                    start_Time = self.get_argument('start_Time', None)  # 开始时间
                    end_Time = self.get_argument('end_Time', None)  # 结束时间
                    pageNum = int(self.get_argument('pageNum', 1))
                    pageSize = int(self.get_argument('pageSize', 20))
                    if DEBUG:
                        logging.info('startTime:%s,endTime:%s,db:%s,pageNum:%s,pageSize:%s' % (
                            start_Time, end_Time, db, pageNum, pageSize))
                    startTime = start_Time + ' 00:00:00'
                    endTime = end_Time + ' 23:59:59'
                    if report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                    obj = {}
                    obj['data'] = []
                    total_all = user_session.query(func.count(AlarmR.id)).filter(AlarmR.event_id == Event.id,
                                                                                 Event.type_id == 5,
                                                                                 Event.type == 2, Event.station == db,
                                                                                 AlarmR.ts.between(startTime, endTime),
                                                                                 or_(AlarmR.value_descr == '故障',
                                                                                     AlarmR.value_descr == '报警')).scalar()
                    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5,
                                                               Event.type == 2, Event.station == db,
                                                               AlarmR.ts.between(startTime, endTime),
                                                               or_(AlarmR.value_descr == '故障',
                                                                   AlarmR.value_descr == '报警')).order_by(
                        AlarmR.ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                    for alarm in alarms:
                        alarm = eval(str(alarm))
                        if lang == 'en':
                            if alarm['point'] != 'None' and alarm['point'] != '':
                                point = translate_text(alarm['point'], 2)
                            else:
                                point = alarm['en_descr']
                            if int(alarm['value']) == 2:
                                point = point + " Give an alarm"
                            else:
                                point = point + " Recovered"
                        else:
                            if alarm['point'] != 'None' and alarm['point'] != '':
                                point = alarm['point']
                            else:
                                point = alarm['descr']
                            if int(alarm['value']) == 2:
                                point = point + " 报警"
                            else:
                                point = point + " 已恢复"
                        obj['data'].append({'ts': alarm['ts'], 'alarm_id': alarm['id'], 'point': point})
                    return self.returnTotalSuc(obj, total_all)
                elif kt == 'GetSysYoCoFa':  # 系统有功功率，充放电量
                    names_ = model_config.get('peizhi', db)  # 充放电量name
                    names = json.loads(names_)
                    conf_name = get_conf_name(db)
                    list2 = []
                    name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                    name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                    if endTime[0:8] == now_time[0:8]:
                        list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                    if report_type == '1':  # 日报（有功功率）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu_yougong(ed, obj, st, 'PCS_yougong_4', '15T', 1, obj_sys='系统',vmax=100000, lang=lang)
                        else:
                            self.data_day_dongmu_yougong(ed, obj, st, 'PCS_yougong_4', '15T', 1, obj_sys='系统',vmax=100000, lang=lang)
                        Power = user_session.query(PowerBase).filter(PowerBase.start_time <= start_Time,
                                                                     PowerBase.is_use == '1',
                                                                     PowerBase.station == db).order_by(
                            PowerBase.id.desc()).first()
                        if Power:
                            Power_infos = user_session.query(PowerBaseInfos).filter(
                                PowerBaseInfos.base_id == Power.id).order_by(PowerBaseInfos.id.asc()).all()
                            da = {'time': [], 'value': []}
                            for p in Power_infos:
                                da['time'].append(start_Time + ' ' + p.start_time + ':00')
                                da['value'].append(p.power)
                            if da['time']:  # 有值
                                da['time'].insert(0, startTime)
                                if da['value']:
                                    da['value'].insert(0, da['value'][0])
                                else:
                                    da['value'].insert(0, da['value'][0])
                            da['time'].append(old_end_Time)
                            da['value'].append(da['value'][-1])
                            df = pd.DataFrame(da)
                            df = df.drop_duplicates(subset=["time"], keep="first")
                            # 转换回字典
                            da["time"] = df["time"].tolist()
                            da["value"] = df["value"].tolist()
                            da_T = complete_data(da, '15T')
                            list_time = []
                            if obj['time'] == []:
                                if da_T['time']:
                                    for i in da_T['time']:
                                        list_time.append(i[8:16])
                                obj['time'] = list_time
                            if lang == 'en':
                                obj['data'].append(
                                    {'name': 'Reference power(kw)', 'value': np.round(da_T['value'], 3).tolist()})
                            else:
                                obj['data'].append(
                                    {'name': '基准功率(kw)', 'value': np.round(da_T['value'], 3).tolist()})
                    elif report_type == '2' or report_type == '3':  # 周，月充放电量
                        list22 = []
                        if endTime[0:10] == now_time[0:10]:
                            list22 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                        two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                        if endTime[0:8] == now_time[0:8]:
                            two_time_lists.append(endTime[0:10])
                        obj['time'] = sorted(set(two_time_lists))
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.sys_chag_disg(freports, obj, 10, obj['time'])
                        if endTime[0:8] == now_time[0:8]:
                            if list22 != []:
                                disg_ = 0
                                chag_ = 0
                                ratio = 0
                                for l in list22:
                                    if l['disg'] == '--' or l['chag'] == '--':
                                        disg_ = '--'
                                        chag_ = '--'
                                        break
                                    disg_ += float(l['disg'])
                                    chag_ += float(l['chag'])
                                if disg_ == '--' or chag_ == '--':
                                    obj['data'][-1]['disg'] = '--'
                                    obj['data'][-1]['chag'] = '--'
                                    obj['data'][-1]['ratio'] = '--'
                                else:
                                    if float(chag_) == 0:
                                        ratio = 0
                                    else:
                                        ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                        if float(ratio) > 100:
                                            ratio = 100
                                    obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                                    obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                                    if obj['data'][-1]['chag'] == 0:
                                        obj['data'][-1]['ratio'] = 0
                                    else:
                                        obj['data'][-1]['ratio'] = ('%.2f' % (
                                                (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                                        if float(obj['data'][-1]['ratio']) > 100:
                                            obj['data'][-1]['ratio'] = 100
                    elif report_type == '4':  # 年充放电量
                        two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                        if endTime[0:7] == now_time[0:7]:
                            two_time_lists.append(endTime[0:7])
                        obj['time'] = sorted(set(two_time_lists))

                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.sys_chag_disg(freports, obj, 7, obj['time'])
                        if endTime[0:7] == now_time[0:7]:
                            if list2 != []:
                                disg_ = 0
                                chag_ = 0
                                ratio = 0
                                for l in list2:
                                    if l['disg'] == '--' or l['chag'] == '--':
                                        disg_ = '--'
                                        chag_ = '--'
                                        break
                                    disg_ += float(l['disg'])
                                    chag_ += float(l['chag'])
                                if disg_ == '--' or chag_ == '--':
                                    obj['data'][-1]['disg'] = '--'
                                    obj['data'][-1]['chag'] = '--'
                                    obj['data'][-1]['ratio'] = '--'
                                else:
                                    if float(chag_) == 0:
                                        ratio = 0
                                    else:
                                        ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                        if float(ratio) > 100:
                                            ratio = 100
                                    obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                                    obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                                    if obj['data'][-1]['chag'] == 0:
                                        obj['data'][-1]['ratio'] = 0
                                    else:
                                        obj['data'][-1]['ratio'] = ('%.2f' % (
                                                (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                                        if float(obj['data'][-1]['ratio']) > 100:
                                            obj['data'][-1]['ratio'] = 100
                    elif report_type == '5':  # 累计充放电量
                        y_time_lists = self.sys_5_time_list(db, end_Time)
                        if endTime[0:8] == now_time[0:8]:
                            y_time_lists.append(int(endTime[0:4]))
                        obj['time'] = sorted(set(y_time_lists))
                        freports = user_session.query(FReport).filter(FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.sys_chag_disg(freports, obj, 4, obj['time'])
                        if list2 != []:
                            disg_ = 0
                            chag_ = 0
                            ratio = 0
                            for l in list2:
                                if l['disg'] == '--' or l['chag'] == '--':
                                    disg_ = '--'
                                    chag_ = '--'
                                    break
                                disg_ += float(l['disg'])
                                chag_ += float(l['chag'])
                            if disg_ == '--' or chag_ == '--':
                                obj['data'][-1]['disg'] = '--'
                                obj['data'][-1]['chag'] = '--'
                                obj['data'][-1]['ratio'] = '--'
                            else:
                                if float(chag_) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                                obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                                if obj['data'][-1]['chag'] == 0:
                                    obj['data'][-1]['ratio'] = 0
                                else:
                                    obj['data'][-1]['ratio'] = ('%.2f' % (
                                            (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                                    if float(obj['data'][-1]['ratio']) > 100:
                                        obj['data'][-1]['ratio'] = 100
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysPf':  # 系统功率因数
                    if report_type == '1':  # 日报
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_sys_SOC_d('PCS_PF_4', ed, obj, st, 1, lang=lang)
                        else:
                            self.dongmu_sys_SOC_d('PCS_PF_4', ed, obj, st, 1, lang=lang)
                    elif report_type == '2' or report_type == '3':  # 周，月，
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                                FReportPcs.day).filter(FReportPcs.station == db,
                                                                       FReportPcs.day.between(startTime,
                                                                                              endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                        obj['time'] = two_time_lists
                        dict_pf = {}
                        for ti in two_time_lists:
                            if PF:
                                for f in PF:
                                    ti_Time = ti + ' 00:00:00'
                                    if ti_Time == str(f[3]):
                                        if f[3] not in dict_pf.keys():
                                            dict_pf[f[3]] = {'max': [], 'min': []}
                                        else:
                                            if float(f[1]) > 1:
                                                dict_pf[f[3]]['max'].append(1)
                                                dict_pf[f[3]]['min'].append(float(f[2]))
                                            else:
                                                dict_pf[f[3]]['max'].append(float(f[1]))
                                                dict_pf[f[3]]['min'].append(float(f[2]))
                        for i in obj['time']:
                            min_ = 0
                            max_ = 0
                            for d in dict_pf.keys():
                                if i == str(d)[:10]:
                                    min_ = min(dict_pf[d]['min'])
                                    max_ = max(dict_pf[d]['max'])
                                    break
                                else:
                                    min_ = 0
                                    max_ = 1
                            obj['data'].append({'max': max_, 'min': min_})
                        if obj['data'] == []:
                            if endTime[0:10] == now_time[0:10]:
                                dict_ = {}
                                dict_['data'] = []
                                now_time_st = now_time + ' 00:00:00'
                                st = timeUtils.timeStrToTamp(now_time_st)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                                self.dongmu_sys_SOC_d('PCS_PF_4', ed, dict_, st, 1, lang=lang)
                                if dict_['data'] != []:
                                    obj['data'] = [{'max': max(dict_['data'][0]['value']), 'min': min(dict_['data'][0]['value'])}]
                        else:
                            if endTime[0:10] == now_time[0:10]:
                                dict_ = {}
                                dict_['data'] = []
                                now_time_st = now_time + ' 00:00:00'
                                st = timeUtils.timeStrToTamp(now_time)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                                self.dongmu_sys_SOC_d('PCS_PF_4', ed, dict_, st, 1, lang=lang)
                                if dict_['data'] != []:
                                    obj['data'][-1] = {'max': max(dict_['data'][0]['value']),
                                                       'min': min(dict_['data'][0]['value'])}
                    elif report_type == '4':  # 年
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                                FReportPcs.day).filter(FReportPcs.station == db,
                                                                       FReportPcs.day.between(startTime,
                                                                                              endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        obj['time'] = []
                        two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                        time_list = two_time_lists
                        dict_pf = {}
                        for ti in two_time_lists:
                            if PF:
                                for f in PF:
                                    if ti == str(f[3])[:7]:
                                        if str(f[3])[:7] not in dict_pf.keys():
                                            dict_pf[str(f[3])[:7]] = {'max': [], 'min': []}
                                        else:
                                            if float(f[1]) > 1:
                                                dict_pf[str(f[3])[:7]]['max'].append(1)
                                                dict_pf[str(f[3])[:7]]['min'].append(float(f[2]))
                                            else:
                                                dict_pf[str(f[3])[:7]]['max'].append(float(f[1]))
                                                dict_pf[str(f[3])[:7]]['min'].append(float(f[2]))
                            else:
                                obj['data'] = []
                        if PF:
                            for i in time_list:
                                for d in dict_pf.keys():
                                    if i == str(d):
                                        min_ = min(dict_pf[d]['min'])
                                        max_ = max(dict_pf[d]['max'])
                                        obj['time'].append(i)
                                        obj['data'].append({'max': max_, 'min': min_})
                                        break
                    elif report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        st = timeUtils.timeStrToTamp(str(pages[0]))  # 起始时间绝对秒
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                                FReportPcs.day).filter(FReportPcs.station == db,
                                                                       FReportPcs.day.between(startTime,
                                                                                              endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        y_time_lists = self.sys_5_time_list(db, end_Time)
                        obj['time'] = []
                        dict_pf = {}
                        for ti in y_time_lists:
                            if PF:
                                for f in PF:
                                    if ti == str(f[3])[:4]:
                                        if str(f[3])[:4] not in dict_pf.keys():
                                            dict_pf[str(f[3])[:4]] = {'max': [], 'min': []}
                                        else:
                                            if float(f[1]) > 1:
                                                dict_pf[str(f[3])[:4]]['max'].append(1)
                                                dict_pf[str(f[3])[:4]]['min'].append(float(f[2]))
                                            else:
                                                dict_pf[str(f[3])[:4]]['max'].append(float(f[1]))
                                                dict_pf[str(f[3])[:4]]['min'].append(float(f[2]))
                        for i in y_time_lists:
                            for d in dict_pf.keys():
                                if i == str(d):
                                    min_ = min(dict_pf[d]['min'])
                                    max_ = max(dict_pf[d]['max'])
                                    obj['time'].append(i)
                                    obj['data'].append({'max': max_, 'min': min_})
                                    break
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysSOCCD':  # 系统 SOC,充放电次数
                    names = model_config.get('peizhi', db)  # 充放电量name
                    names = json.loads(names)
                    conf_name = get_conf_name(db)
                    volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                    list2 = []
                    name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                    name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                    if endTime[0:8] == now_time[0:8]:
                        list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                    if report_type == '1':  # 日报 SOC
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_sys_SOC_d('soc_4', ed, obj, st, 2, lang=lang)
                        else:
                            self.dongmu_sys_SOC_d('soc_4', ed, obj, st, 2, lang=lang)

                    elif report_type == '2' or report_type == '3':  # 周，月充放电次数
                        list22 = []
                        if endTime[0:10] == now_time[0:10]:
                            list22 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                        two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                        if endTime[0:8] == now_time[0:8]:
                            two_time_lists.append(endTime[0:10])
                        obj['time'] = sorted(set(two_time_lists))
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.ys_chag_disg_num(db, freports, obj, obj['time'], 10, list22)
                    elif report_type == '4':  # 年，充放电次数
                        two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                        obj['time'] = sorted(set(two_time_lists))
                        obj['time'] = two_time_lists
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.ys_chag_disg_num(db, freports, obj, obj['time'], 7, list2)

                    elif report_type == '5':  # 累计充放电次数
                        y_time_lists = self.sys_5_time_list(db, end_Time)
                        obj['time'] = sorted(set(y_time_lists))
                        freports = user_session.query(FReport).filter(FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                        self.ys_chag_disg_num(db, freports, obj, obj['time'], 4, list2)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysSOH':  # 系统SOH
                    if report_type == '1':  # 日报 SOH
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.sys_soh_day_dongmu(ed, obj, st)
                        else:
                            self.sys_soh_day_dongmu(ed, obj, st)
                    elif report_type == '2' or report_type == '3':  # 周，月SOH
                        two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                        obj['time'] = []
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)

                        if endTime[0:10] == now_time[0:10]:
                            list_ = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            SOH_name = model_config.get('peizhi', 'cu_SOH_4')  # name
                            two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                            for ti in two_time_lists:
                                d = []
                                stTime = ti + ' 00:00:00'
                                enTime = ti + ' 23:59:59'
                                list_ = self.dongmu_sys_soh(SOH_name, d, enTime, max, min, stTime)
                            dongmu_session.close()
                            if obj['data']:
                                if list_ != []:
                                    obj['data'][-1] = list_[0]
                    elif report_type == '4':  # 年
                        two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                        obj['time'] = []
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)

                    elif report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        y_time_lists = self.sys_5_time_list(db, end_Time)
                        obj['time'] = []
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)

                    return self.returnTypeSuc(obj)

                elif kt == 'GetPCSYoCoFa':  # PCS有功功率，充放电量
                    if report_type == '1':  # 日报（有功功率）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_yougong_4', '15T', obj_sys='PCS', vmax=100000,
                                                 lang=lang)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_yougong_4', '15T', obj_sys='PCS', vmax=100000,
                                                 lang=lang)
                    elif report_type == '2' or report_type == '3' or report_type == '4' or report_type == '5':  # 周，月，年,累计充放电量
                        names_ = model_config.get('peizhi', db)  # 充放电量
                        names = json.loads(names_)
                        conf_name = get_conf_name(db)
                        name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                        name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                        list2 = []
                        if report_type == '2' or report_type == '3':
                            if endTime[0:10] == now_time[0:10]:
                                list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                        else:
                            if endTime[0:8] == now_time[0:8]:
                                list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                        list1 = []  # 单个pcs
                        obj = []
                        n = 1
                        if report_type == '5':
                            freports = user_session.query(FReport).filter(FReport.name.in_(names),
                                                                          FReport.cause == 1).all()
                        else:
                            freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                          FReport.name.in_(names),
                                                                          FReport.cause == 1).all()
                        freport_obj = {}
                        for f in freports:
                            if f.name in freport_obj:
                                freport_obj[f.name].append(f)
                            else:
                                freport_obj[f.name] = []
                                freport_obj[f.name].append(f)

                        for name in freport_obj:
                            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                                      SdaPcsCu.station_name == db).first()
                            if not v_pcs_name:
                                logging.error("未找到对应的pcs_name")
                                continue
                            PCS_name_f = v_pcs_name[0]
                            freport = freport_obj[name]
                            disg = 0  # 放电量
                            chag = 0  # 充电量
                            if freport:
                                for f in freport:
                                    disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                                        eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                                    chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                                        eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                            list1.append({'name': PCS_name_f, 'disg': int(disg), 'chag': int(chag)})

                            n += 1
                        list3 = []
                        if list2 != []:
                            for l in list2:
                                if l['disg'] == '--' or l['chag'] == '--':
                                    list3.append(
                                        {'name': l['name'], 'disg': l['disg'], 'chag': l['chag'],
                                         'ratio': '--'})
                                else:
                                    if list1 != []:
                                        for ll in list1:
                                            if l['name'] == ll['name']:
                                                disg_ = float(l['disg']) + float(ll['disg'])
                                                chag_ = float(l['chag']) + float(ll['chag'])
                                                if float(chag_) == 0:
                                                    ratio = 0
                                                else:
                                                    ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                                    if float(ratio) > 100:
                                                        ratio = 100
                                                list3.append(
                                                    {'name': l['name'], 'disg': disg_, 'chag': chag_,
                                                     'ratio': float(ratio)})
                                    else:
                                        disg_ = float(l['disg'])
                                        chag_ = float(l['chag'])
                                        if float(chag_) == 0:
                                            ratio = 0
                                        else:
                                            ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                            if float(ratio) > 100:
                                                ratio = 100
                                        list3.append(
                                            {'name': l['name'], 'disg': disg_, 'chag': chag_, 'ratio': float(ratio)})
                        else:
                            if list1 != []:
                                for ll in list1:
                                    disg_ = float(ll['disg'])
                                    chag_ = float(ll['chag'])
                                    if float(chag_) == 0:
                                        ratio = 0
                                    else:
                                        ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                        if float(ratio) > 100:
                                            ratio = 100
                                    list3.append(
                                        {'name': ll['name'], 'disg': disg_, 'chag': chag_, 'ratio': float(ratio)})
                        obj = natsorted(list3, key=lambda x: x['name'], reverse=False)

                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSPf':  # PCS功率因数
                    if report_type == '1':  # 日报
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_PF_4', '15T', obj_sys='PCS', vmax=1)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_PF_4', '15T', obj_sys='PCS', vmax=1)
                    elif report_type == '2' or report_type == '3':  # 周，月
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                            FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        if PF:
                            dict_pf = {}
                            for f in PF:
                                if f[0] not in dict_pf.keys():
                                    dict_pf[f[0]] = {'max': [], 'min': []}
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                            for d in dict_pf.keys():
                                if dict_pf[d]['max'] != [] and dict_pf[d]['min'] != []:
                                    obj['data'].append(
                                        {'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            dict_1 = self.dongmu_PCS_pf('PCS_PF_4', ed, st)
                            dongmu_session.close()
                            obj_list = []
                            if dict_1['data']:
                                for d in dict_1['data']:
                                    if obj['data']:
                                        for p in obj['data']:
                                            if p['name'] == d['name']:
                                                max_2 = 0
                                                min_2 = 0
                                                if float(p['max']) > d['max']:
                                                    max_2 = p['max']
                                                else:
                                                    max_2 = p['max']
                                                if float(p['min']) < d['min']:
                                                    min_2 = p['min']
                                                else:
                                                    min_2 = p['min']
                                                obj_list.append({'name': p['name'], 'max': max_2, 'min': min_2})
                                    else:
                                        obj['data'] = dict_1['data']
                                obj['data'] = obj_list
                            else:
                                obj['data'] = obj['data']
                    elif report_type == '4':  # 年，
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                            FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        if PF:
                            dict_pf = {}
                            for f in PF:
                                if f[0] not in dict_pf.keys():
                                    dict_pf[f[0]] = {'max': [], 'min': []}
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                            for d in dict_pf.keys():
                                obj['data'].append({'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                    elif report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                            FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                            FReportPcs.pcs_name.asc()).all()
                        if PF:
                            dict_pf = {}
                            for f in PF:
                                if f[0] not in dict_pf.keys():
                                    dict_pf[f[0]] = {'max': [], 'min': []}
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    if float(f[1]) > 1:
                                        dict_pf[f[0]]['max'].append(1)
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                                    else:
                                        dict_pf[f[0]]['max'].append(float(f[1]))
                                        dict_pf[f[0]]['min'].append(float(f[2]))
                            for d in dict_pf.keys():
                                obj['data'].append({'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSSOCCD':  # PCS SOC,充放电次数
                    if report_type == '1':  # 日报 SOC
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'soc_4', '15T', obj_sys='PCS', vmax=101)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'soc_4', '15T', obj_sys='PCS', vmax=101)
                    elif report_type == '2' or report_type == '3' or report_type == '4' or report_type == '5':  # 周，月，年，累计充放电次数
                        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                        names = model_config.get('peizhi', db)  # 充放电量
                        names = json.loads(names)
                        conf_name = get_conf_name(db)
                        list1 = []  # 单个pcs
                        list2 = []
                        name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                        name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                        if report_type == '2' or report_type == '3':
                            if endTime[0:10] == now_time[0:10]:
                                list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                        else:
                            if endTime[0:8] == now_time[0:8]:
                                list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                        n = 0
                        if report_type == '5':
                            freports = user_session.query(FReport).filter(FReport.name.in_(names),
                                                                          FReport.cause == 1).all()
                        else:
                            freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                          FReport.name.in_(names),
                                                                          FReport.cause == 1).all()
                        freport_obj = {}  # 按name为key将值放到列表里
                        for f in freports:
                            if f.name in freport_obj:
                                freport_obj[f.name].append(f)
                            else:
                                freport_obj[f.name] = []
                                freport_obj[f.name].append(f)
                        for name in freport_obj:
                            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                                      SdaPcsCu.station_name == db).first()
                            if not v_pcs_name:
                                logging.error("未找到对应的pcs_name")
                                continue
                            PCS_name_f = v_pcs_name[0]
                            freport = freport_obj[name]
                            disg = 0  # 放电量
                            chag = 0  # 充电量
                            if freport:
                                for f in freport:
                                    disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                                        eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                                    chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                                        eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                            list1.append({'name': PCS_name_f, 'disg': int(disg), 'chag': int(chag)})
                            n += 1
                        list3 = []
                        if list2 != []:
                            for l in list2:
                                if l['disg'] == '--' or l['chag'] == '--':
                                        list3.append({'name': l['name'], 'chag_disg_num': '--'})
                                else:
                                    if list1 != []:
                                        for ll in list1:
                                            if l['name'] == ll['name']:
                                                disg_ = float(l['disg']) + float(ll['disg'])
                                                chag_ = float(l['chag']) + float(ll['chag'])
                                                chag_disg_num = math.floor(
                                                    (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list1)))
                                                list3.append({'name': l['name'], 'chag_disg_num': chag_disg_num})
                                    else:
                                        disg_ = float(l['disg'])
                                        chag_ = float(l['chag'])
                                        chag_disg_num = math.floor(
                                            (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list2)))
                                        list3.append({'name': l['name'], 'chag_disg_num': chag_disg_num})
                        else:
                            if list1 != []:
                                for ll in list1:
                                    disg_ = float(ll['disg'])
                                    chag_ = float(ll['chag'])
                                    chag_disg_num = math.floor(
                                        (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list1)))
                                    list3.append({'name': ll['name'], 'chag_disg_num': chag_disg_num})
                        obj['data'] = natsorted(list3, key=lambda x: x['name'], reverse=False)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCST':  # PCS 温度
                    if report_type == '1':  # 日报
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_T_4', '15T', obj_sys='PCS', vmax=100)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_T_4', '15T', obj_sys='PCS', vmax=100)
                    elif report_type == '2' or report_type == '3' or report_type == '4':  # 周，月，年
                        obj = self.dongmu_PCS('PCS_T_4', ed, st, obj)
                        dongmu_session.close()
                    elif report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        st = timeUtils.timeStrToTamp(str(pages[0]))  # 起始时间绝对秒
                        obj = self.dongmu_PCS('PCS_T_4', ed, st, obj)
                        dongmu_session.close()
                    return self.returnTypeSuc(obj)

                elif kt == 'GetPCSAcVol':  # PCS交流电压
                    if report_type == '1':  # 日报PCS交流电压
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '15T', obj_sys='PCS', vmax=100000)
                    elif report_type == '2':  # 周PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '1H', obj_sys='PCS', vmax=100000)
                    elif report_type == '3':  # 月，PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '2H', obj_sys='PCS', vmax=100000)
                    elif report_type == '4':  # 年PCS交流电压
                        startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldy_4', '6H', 4)
                    elif report_type == '5':  # 累计PCS交流电压
                        startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldy_4', '6H', 5)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSAcCurr':  # PCS交流电流
                    timeall = []
                    if report_type == '1':  # 日报PCS交流电压
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '15T', obj_sys='PCS', vmax=100000)
                    elif report_type == '2':  # 周PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '1H', obj_sys='PCS', vmax=100000)
                    elif report_type == '3':  # 月PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '2H', obj_sys='PCS', vmax=100000)
                    elif report_type == '4':  # 年PCS交流电压
                        startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldl_4', '6H', 4)
                    elif report_type == '5':  # 累计PCS交流电压
                        startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldl_4', '6H', 5)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSDcVolt':  # PCS直流电压
                    timeall = []
                    if report_type == '1':  # 日报PCS交流电压
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '15T', obj_sys='PCS', vmax=100000)
                    elif report_type == '2':  # 周PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '1H', obj_sys='PCS', vmax=100000)
                    elif report_type == '3':  # 月PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '2H', obj_sys='PCS', vmax=100000)
                    elif report_type == '4':  # 年PCS交流电压
                        startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldy_4', '6H', 4)
                    elif report_type == '5':  # 累计PCS交流电压
                        startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldy_4', '6H', 5)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSDcCurr':  # PCS直流电流
                    timeall = []
                    if report_type == '1':  # 日报PCS交流电压
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '15T', obj_sys='PCS', vmax=100000)
                    elif report_type == '2':  # 周PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '1H', obj_sys='PCS', vmax=100000)
                    elif report_type == '3':  # 月PCS交流电压
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '2H', obj_sys='PCS', vmax=100000)
                    elif report_type == '4':  # 年PCS交流电压
                        startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldl_4', '6H', 4)
                    elif report_type == '5':  # 累计PCS交流电压
                        startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldl_4', '6H', 5)
                    return self.returnTypeSuc(obj)

                elif kt == 'GetCuYoCoFa':  # 簇功率，充放电量
                    if report_type == '1':  # 日报（有功功率）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_cu_day('PCS_ClusF_4', ed, obj, st, lang=lang)
                        else:
                            self.dongmu_cu_day('PCS_ClusF_4', ed, obj, st, lang=lang)
                    elif report_type == '2' or report_type == '3':  # 周，月充放电量
                        obj = []
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)

                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            obj_list = []
                            obj_list = []
                            list1 = []
                            dm_table = HisDM('r_cumulant')
                            values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()

                            if values_mong:
                                values_1_s = {'datainfo': {}}
                                values_1_e = {'datainfo': {}}
                                values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
                                values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
                                value_s = json.loads(values_1_s['datainfo'])['body']
                                value_e = json.loads(values_1_e['datainfo'])['body']
                                BDcap_cu_s = []  # 电池总放电量
                                BCCap_cu_s = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_s:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                                            BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
                                BDcap_cu_e = []  # 电池总放电量
                                BCCap_cu_e = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_e:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                                            BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
                                BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
                                BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量

                                ratio = 0
                                e = 1
                                for f in BDcap_cu.tolist():  #
                                    if BCCap_cu.tolist()[e - 1] == 0:
                                        ratio = 0
                                    else:
                                        ratio = ('%.2f' % ((f / BCCap_cu.tolist()[e - 1]) * 100))
                                        if float(ratio) > 100:
                                            ratio = 100.0
                                    obj_list.append({'pcs_name': 'PCS-%s' % e, 'cu': [
                                        {'cu_name': '电池簇%s' % e, 'disg': ('%.1f' % f),
                                         'chag': ('%.1f' % BCCap_cu.tolist()[e - 1]), 'ratio': ratio}]})
                                    e += 1
                            dongmu_session.close()
                            if obj:
                                if obj_list:
                                    for o in obj:
                                        indx_1 = obj.index(o)
                                        for c in o['cu']:
                                            indx_2 = o['cu'].index(c)
                                            c['chag'] = int(c['chag']) + int(
                                                '%.0f' % float((obj_list[indx_1]['cu'][indx_2]['chag'])))
                                            c['disg'] = int(c['disg']) + int(
                                                '%.0f' % float((obj_list[indx_1]['cu'][indx_2]['disg'])))
                                            if c['chag'] == 0:
                                                c['ratio'] = 0
                                            else:
                                                c['ratio'] = float('%.2f' % ((c['disg'] / c['chag']) * 100))
                                                if c['ratio'] > 100:
                                                    c['ratio'] = 100
                                            o['cu'] = natsorted(o['cu'], key=lambda x: x['cu_name'], reverse=False)
                                else:
                                    obj = obj
                            else:
                                obj = obj_list
                    elif report_type == '4':  # 年充放电量
                        obj = []
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)


                    elif report_type == '5':  # 累计充放电量
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        obj = []
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSOCCD':  # 簇SOC,充放电次数
                    if report_type == '1':  # 日报（SOC）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_cu_day('cu_CSOC_4', ed, obj, st, lang)
                        else:
                            self.dongmu_cu_day('cu_CSOC_4', ed, obj, st, lang)
                    elif report_type == '2' or report_type == '3':  # 周，月，充放电次数
                        obj = []
                        obj_c_d = []
                        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            obj_list = []
                            obj_list_c_d = []
                            volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                            if report_type != '4':
                                if startTime < '2023-04-01 00:00:00':
                                    return self.customError('此开始时间下无数据！')
                            dm_table = HisDM('r_cumulant')
                            values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
                            # print (values_mong)
                            if values_mong:
                                values_1_s = {'datainfo': {}}
                                values_1_e = {'datainfo': {}}
                                values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
                                values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
                                value_s = json.loads(values_1_s['datainfo'])['body']
                                value_e = json.loads(values_1_e['datainfo'])['body']

                                BDcap_cu_s = []  # 电池总放电量
                                BCCap_cu_s = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_s:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                                            BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
                                BDcap_cu_e = []  # 电池总放电量
                                BCCap_cu_e = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_e:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                                            BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
                                BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
                                BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量
                                e = 1
                                for f in BDcap_cu.tolist():  #
                                    obj_list.append({'pcs_name': 'PCS-%s' % e, 'cu': [{'cu_name': '电池簇%s' % e,
                                                                                       'chag_disg_num': math.floor((math.sqrt(float(f) * float(BCCap_cu.tolist()[e - 1]))) / ((volume[0] * 1000) / 7))}]})
                                    obj_list_c_d.append({'pcs_name': 'PCS-%s' % e, 'cu': [{'cu_name': '电池簇%s' % e, 'chag': float(BCCap_cu.tolist()[e - 1]),
                                         'disg': float(f)}]})
                                    e += 1
                            dongmu_session.close()
                            if obj:
                                if obj_list:
                                    obj_list_ = []
                                    for o in obj_c_d:
                                        indx_1 = obj_c_d.index(o)
                                        cu__ = []
                                        for c in o['cu']:
                                            indx_2 = o['cu'].index(c)
                                            c['chag'] = int(c['chag']) + int(
                                                '%.0f' % float((obj_list_c_d[indx_1]['cu'][indx_2]['chag'])))
                                            c['disg'] = int(c['disg']) + int(
                                                '%.0f' % float((obj_list_c_d[indx_1]['cu'][indx_2]['disg'])))
                                            # c['chag']=int(c['chag'])+int(obj_list_c_d[indx_1]['cu'][indx_2]['chag'])
                                            # c['disg']=int(c['disg'])+int(obj_list_c_d[indx_1]['cu'][indx_2]['disg'])
                                            chag_disg_num = math.floor(
                                                (math.sqrt(float(c['chag']) * float(c['disg']))) / (
                                                        (volume[0] * 1000) / (len(obj) * len(o['cu']))))
                                            cu__.append({'cu_name': c['cu_name'], 'chag_disg_num': chag_disg_num})
                                        list3 = sorted(cu__, key=lambda x: x.get("chag_disg_num"), reverse=True)
                                        obj_list_.append({'pcs_name': o['pcs_name'], 'cu': list3})
                                    obj = obj_list_
                                else:
                                    obj = obj
                            else:
                                obj = obj_list
                    elif report_type == '4':  # 累计充放电量
                        obj = []
                        obj_c_d = []
                        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif report_type == '5':  # 累计充放电量
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        obj = []
                        obj_c_d = []
                        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSOH':  # 簇SOH
                    if report_type == '1':  # 日报（SOH）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_cu_day('cu_SOH_4', ed, obj, st)
                        else:
                            self.dongmu_cu_day('cu_SOH_4', ed, obj, st)
                    elif report_type == '2' or report_type == '3':  # 周，月，年,累计SOH最小值
                        obj = []
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                        if obj == []:
                            if endTime[0:10] == now_time[0:10]:
                                dict_1 = {}
                                dict_1['data'] = []
                                startTime = endTime[0:10] + ' 00:00:00'
                                endTime = endTime[0:10] + ' 23:59:59'
                                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                                obj_list = []
                                if db == 'dongmu':
                                    cu_name = model_config.get('peizhi', 'cu_SOH_4')  # name
                                    SOH = 0  # SOH
                                    dm_table = HisDM('r_measure')
                                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                        dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
                                    for f in range(7):
                                        d = []
                                        for v in values_mong:
                                            body = json.loads(v['datainfo'])['body']
                                            for b in body:
                                                if b['device'][:4] == 'BMS%s' % (f + 1):
                                                    value = float(b[cu_name])  #
                                                    if 0 < value <= 100:
                                                        d.append(value)
                                                    else:
                                                        d.append(100)
                                        obj_list.append({'pcs_name': 'PCS-%s' % (f + 1), 'cu': [
                                            {'cu_name': '电池簇%s' % (f + 1), 'min': min(d) if d else 100}]})
                                    dongmu_session.close()
                                obj = obj_list
                    elif report_type == '4':  # 周，月，年,累计SOH最小值
                        obj = []
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)


                    elif report_type == '5':  # 周，月，年,累计SOH最小值
                        obj = []
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuStRan':  # 簇温度极差
                    time_S = timeUtils.getAgoTime()  # 获取N天前时间,默认一周'
                    datestart = now_time[0:8]
                    if report_type == '1':  # 日报（温度极差）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_cu_day('cu_StRan_4', ed, obj, st)
                        else:
                            self.dongmu_cu_day('cu_StRan_4', ed, obj, st)
                    elif report_type == '2' or report_type == '3':  # 周，月温度极差最大值
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                        if obj == []:
                            obj_list = []
                            if endTime[0:10] == now_time[0:10]:
                                dict_1 = {}
                                dict_1['data'] = []
                                startTime = endTime[0:10] + ' 00:00:00'
                                endTime = endTime[0:10] + ' 23:59:59'
                                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                                obj_list = []
                            list1 = []
                            cu_name = model_config.get('peizhi', 'cu_StRan_4')  # name
                            obj_list = self.dongmu_cu_v_t_j(cu_name, ed, list1, obj_list, st)
                            obj = obj_list
                    elif report_type == '4':  # 年温度极差最大值
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)

                    elif report_type == '5':  # 累计温度极差最大值
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSVRan':  # 簇电压极差
                    timeall = []
                    if report_type == '1':  # 日报（温度极差）
                        if end_Time[0:10] == now_time[0:10]:  # 当天数据
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            self.dongmu_cu_day('cu_SVRan_4', ed, obj, st)
                        else:
                            self.dongmu_cu_day('cu_SVRan_4', ed, obj, st)

                    elif report_type == '2' or report_type == '3':  # 周，月，电压极差最大值
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                        if obj == []:
                            if endTime[0:10] == now_time[0:10]:
                                dict_1 = {}
                                dict_1['data'] = []
                                startTime = endTime[0:10] + ' 00:00:00'
                                endTime = endTime[0:10] + ' 23:59:59'
                                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                                obj_list = []
                                list1 = []
                                if report_type != '4':
                                    if startTime < '2023-04-01 00:00:00':
                                        return self.customError('此开始时间下无数据！')
                                cu_name = model_config.get('peizhi', 'cu_SVRan_4')  # name
                                obj_list = self.dongmu_cu_v_t_j(cu_name, ed, list1, obj_list, st)
                                obj = obj_list
                    elif report_type == '4':  # ，年电压极差最大值
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)

                    elif report_type == '5':  # 累计电压极差最大值
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                        obj = []
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(
                            FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    return self.returnTypeSuc(obj)
                else:
                    return self.pathError()
            else:
                if kt == 'GetAlarmR':  # 三级故障和报警的数据
                    pageNum = int(self.get_argument('pageNum', 1))
                    pageSize = int(self.get_argument('pageSize', 20))
                    if DEBUG:
                        logging.info('startTime:%s,endTime:%s,db:%s,pageNum:%s,pageSize:%s' % (start_Time, end_Time, db, pageNum, pageSize))
                    if report_type == '5':  # 累计
                        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                        startTime = str(pages[0])
                    obj['data'] = []
                    total_all = user_session.query(func.count(AlarmR.id)).filter(AlarmR.event_id == Event.id,
                                                                                 Event.type_id == 5,
                                                                                 Event.type == 2, Event.station == db,
                                                                                 AlarmR.ts.between(startTime, endTime),
                                                                                 or_(AlarmR.value_descr == '故障',
                                                                                     AlarmR.value_descr == '报警')).scalar()
                    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5,
                                                               Event.type == 2, Event.station == db,
                                                               AlarmR.ts.between(startTime, endTime),
                                                               or_(AlarmR.value_descr == '故障',
                                                                   AlarmR.value_descr == '报警')).order_by(
                        AlarmR.ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                    for alarm in alarms:
                        alarm = eval(str(alarm))
                        if lang == 'en':
                            if alarm['point'] != 'None' and alarm['point'] != '':
                                point = translate_text(alarm['point'], 2)
                            else:
                                point = alarm['en_descr']
                            if int(alarm['value']) == 2:
                                point = point + " Give an alarm"
                            else:
                                point = point + " Recovered"
                        else:
                            if alarm['point'] != 'None' and alarm['point'] != '':
                                point = alarm['point']
                            else:
                                point = alarm['descr']
                            if int(alarm['value']) == 2:
                                point = point + " 报警"
                            else:
                                point = point + " 已恢复"
                        obj['data'].append({'ts': alarm['ts'], 'alarm_id': alarm['id'], 'point': point})
                    return self.returnTotalSuc(obj, total_all)
                elif kt == 'GetSysYoCoFa':  # 系统有功功率，充放电量
                    if report_type=='1':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang,'yougong',jiange_time,'st',-99999999,99999999)
                        Power = user_session.query(PowerBase).filter(PowerBase.start_time <= start_Time,
                                                                     PowerBase.is_use == '1',
                                                                     PowerBase.station == db).order_by(
                            PowerBase.id.desc()).first()
                        if Power:
                            Power_infos = user_session.query(PowerBaseInfos).filter(PowerBaseInfos.base_id == Power.id).order_by(PowerBaseInfos.id.asc()).all()
                            da = {'time': [], 'value': []}
                            for p in Power_infos:
                                da['time'].append(start_Time + ' ' + p.start_time + ':00')
                                da['value'].append(p.power)
                            if da['time']:  # 有值
                                da['time'].insert(0, startTime)
                                if da['value']:
                                    da['value'].insert(0, da['value'][0])
                                else:
                                    da['value'].insert(0, da['value'][0])
                            da['time'].append(old_end_Time)
                            da['value'].append(da['value'][-1])
                            df = pd.DataFrame(da)
                            df = df.drop_duplicates(subset=["time"], keep="first")
                            # 转换回字典
                            da["time"] = df["time"].tolist()
                            da["value"] = df["value"].tolist()
                            da_T = complete_data(da, '15T')
                            list_time = []
                            if obj['time'] == []:
                                if da_T['time']:
                                    for i in da_T['time']:
                                        list_time.append(i[8:16])
                                obj['time'] = list_time
                            if lang == 'en':
                                obj['data'].append(
                                    {'name': 'Reference power(kw)', 'value': np.round(da_T['value'], 3).tolist()})
                            else:
                                obj['data'].append(
                                    {'name': '基准功率(kw)', 'value': np.round(da_T['value'], 3).tolist()})
                        # 获取当天的起始时间，即00:00:00
                        if isinstance(end_Time,str):
                            end_Time = datetime.strptime(end_Time,"%Y-%m-%d")
                        start_time = end_Time.replace(hour=0, minute=0, second=0, microsecond=0)
                        time_list = []
                        # 循环生成当天每15分钟的时间
                        while start_time.day == end_Time.day:
                            time_list.append(start_time)
                            start_time += timedelta(minutes=15)
                        for ind ,time in enumerate(time_list):
                            _time = time.strftime('%d %H:%M')
                        if _time not in obj.get("time"):
                            obj.get("time").insert(ind , _time)
                            for item in obj['data']:
                                item.get("value").insert(ind , '--')
                        # for time in time_list:
                        #     _time = time.strftime('%d %H:%M')
                        #     if _time not in obj.get("time"):
                        #         obj.get("time").append(_time)
                        #         for item in obj['data']:
                        #             item.get("value").append("--")
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang,'chagdisg', jiange_time,'st', -99999999, 99999999,cd=1)
                    elif report_type == '4':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_Time, end_Time, st, ed,obj, lang, 'chagdisg', jiange_time,'st', -99999999, 1000000000,cd=1)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts=start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4]+'-01-01'
                        end_ts=endTime[:4]+'-12-31'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_ts, end_ts, st, ed,obj, lang, 'chagdisg', jiange_time, 'st', -9999999999, 1000000000,cd=1)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysPf':  # 系统功率因数
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang,'pf',jiange_time,'st' ,-99999999,99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang,'pf_s_i', jiange_time,'st', -99999999,99999999)
                    elif report_type == '4':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_Time, end_Time, st, ed,obj, lang, 'pf_s_i', jiange_time,'st',  -99999999,99999999)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_ts, end_ts, st, ed,obj, lang, 'pf_s_i', jiange_time, 'st', -99999999,99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysSOCCD':  # 系统 SOC,充放电次数(年和累计无数据)
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed,obj, lang, 'SOC', jiange_time, 'st',-99999999, 99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang, 'chagdisg', jiange_time, 'st', -99999999, 100000000)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed,obj, lang, 'chagdisg', jiange_time,'st', -99999999, 100000000)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_ts, end_ts, st, ed,obj, lang, 'chagdisg', jiange_time, 'st', -99999999, 100000000)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetSysSOH':  # 系统SOH
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed,obj, lang, 'SOH', jiange_time, 'st',-999999999, 99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, startTime, endTime, st, ed, obj,lang, 'SOH', jiange_time, 'st', -99999999, 99999999)
                    elif report_type == '4':
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_Time, end_Time, st, ed,obj, lang, 'SOH', jiange_time,'st', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, st_table_name, start_ts, end_ts, st, ed,obj, lang, 'SOH', jiange_time, 'st',-99999999, 99999999)
                    return self.returnTypeSuc(obj)

                elif kt == 'GetPCSYoCoFa':  # PCS有功功率，充放电量
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'yougong', jiange_time,'pcs',-99999999,99999999)
                        obj['data'] = self.sorted_pcs_pcs(db, obj['data'])
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'chagdisg', jiange_time, 'pcs', -99999999, 99999999,cd=1)
                        obj = self.sorted_pcs_pcs(db, obj)
                    elif report_type == '4':
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_Time, end_Time, st, ed,obj, lang, 'chagdisg', jiange_time,'pcs', -99999999, 99999999,cd=1)
                        obj = self.sorted_pcs_pcs(db, obj)
                    elif report_type == '5':
                        obj = []
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'chagdisg', jiange_time, 'pcs', -99999999, 99999999,cd=1)
                        obj = self.sorted_pcs_pcs(db, obj)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSPf':  # PCS功率因数
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'pf', jiange_time, 'pcs', -99999999, 99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang,'pf_s_i', jiange_time,'pcs', -99999999,99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    elif report_type == '4':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_Time, end_Time, st, ed,obj, lang, 'pf_s_i', jiange_time,'pcs', -99999999,99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'pf_s_i', jiange_time, 'pcs',-99999999,99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSSOCCD':  # PCS SOC,充放电次数
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'SOC', jiange_time, 'pcs',-99999999, 99999999)
                        obj['data'] = self.sorted_pcs_pcs(db, obj['data'])
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'chagdisg', jiange_time, 'pcs', -99999999, 99999999)
                        obj['data'] = self.sorted_pcs_pcs(db, obj['data'])
                    elif report_type == '4':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_Time, end_Time, st, ed,obj, lang, 'chagdisg', jiange_time,'pcs', -99999999, 99999999)
                        obj['data'] = self.sorted_pcs_pcs(db, obj['data'])
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'chagdisg', jiange_time, 'pcs', -99999999, 99999999)
                        obj['data'] = self.sorted_pcs_pcs(db, obj['data'])
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCST':  # PCS 温度
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'temp', jiange_time, 'pcs', -99999999,99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'temp_s_i', jiange_time, 'pcs', -99999999, 99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_Time, end_Time, st, ed,obj, lang, 'temp_s_i', jiange_time,'pcs',  -99999999, 99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    elif report_type == '5':
                        #最高温超过65度没取
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'temp_s_i', jiange_time, 'pcs',  -99999999, 99999999)
                        obj['data'] = sorted(obj['data'], key=lambda x: x.get("max"), reverse=True)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSAcVol':  # PCS交流电压
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_vol', jiange_time, 'pcs', -99999999, 99999999)
                    elif report_type == '2':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_vol', jiange_time_zhou, 'pcs', -99999999, 99999999)
                    elif report_type == '3':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_vol', jiange_time_yue, 'pcs', -99999999, 99999999)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'ac_vol', jiange_time_nian, 'pcs', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'ac_vol', jiange_time_nian, 'pcs', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSAcCurr':  # PCS交流电流
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_cur', jiange_time, 'pcs', -99999999, 99999999)
                    elif report_type == '2':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_cur', jiange_time_zhou, 'pcs', -99999999, 99999999)
                    elif report_type == '3':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'ac_cur', jiange_time_yue, 'pcs', -99999999, 99999999)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'ac_cur', jiange_time_nian, 'pcs', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'ac_cur', jiange_time_nian, 'pcs', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSDcVolt':  # PCS直流电压
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_vol', jiange_time, 'pcs', -99999999, 99999999)
                    elif report_type == '2':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_vol', jiange_time_zhou, 'pcs', -99999999, 99999999)
                    elif report_type == '3':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_vol', jiange_time_yue, 'pcs', -99999999, 99999999)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'dc_vol', jiange_time_nian, 'pcs', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'dc_vol', jiange_time_nian, 'pcs', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetPCSDcCurr':  # PCS直流电流
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_cur', jiange_time, 'pcs', -99999999, 99999999)
                    elif report_type == '2':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_cur', jiange_time_zhou, 'pcs', -99999999, 99999999)
                    elif report_type == '3':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed, obj,lang, 'dc_cur', jiange_time_yue, 'pcs', -99999999, 99999999)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, startTime, endTime, st, ed,obj, lang, 'dc_cur', jiange_time_nian, 'pcs', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts_ = user_session.query(Station.start_ts).filter(Station.name == db).first()
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        self.report_type_biaos(report_type, db, value_name, pcs_table_name, start_ts, end_ts, st, ed,obj, lang, 'dc_cur', jiange_time_nian, 'pcs', -99999999, 99999999)
                    return self.returnTypeSuc(obj)

                elif kt == 'GetCuYoCoFa':  # 簇功率，充放电量
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed,obj, lang, 'bc_pw', jiange_time,'bc',-99999999,99999999)
                        obj['data'] = self.sorted_pcs(db, obj['data'])
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj=[]
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'chagdisg', jiange_time, 'bc', -99999999, 100000000, cd=1)
                        obj = self.sorted_pcs(db, obj)
                    elif report_type == '4':
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_Time, end_Time, st, ed,obj, lang, 'chagdisg', jiange_time, 'bc', -99999999, 100000000, cd=1)
                        obj = self.sorted_pcs(db, obj)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_ts, end_ts, st, ed, obj, lang,'chagdisg', jiange_time, 'bc', -99999999, 100000000, cd=1)
                        obj = self.sorted_pcs(db, obj)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSOCCD':  # 簇SOC,充放电次数
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed,obj, lang, 'SOC', jiange_time, 'bc',-99999999, 99999999)
                        obj['data'] = self.sorted_pcs(db, obj['data'])
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'chagdisg', jiange_time, 'bc', -99999999, 99999999)
                        obj = self.sorted_pcs(db, obj)
                    elif report_type == '4':
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_Time, end_Time, st, ed, obj,lang, 'chagdisg', jiange_time, 'bc', -99999999, 99999999)
                        obj = self.sorted_pcs(db, obj)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_ts, end_ts, st, ed, obj, lang,'chagdisg', jiange_time, 'bc', -99999999, 99999999)
                        obj = self.sorted_pcs(db, obj)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSOH':  # 簇SOH
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'SOH', jiange_time, 'bc',-99999999, 99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'SOH', jiange_time, 'bc',-99999999, 99999999)
                    elif report_type == '4':
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'SOH', jiange_time, 'bc', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_ts, end_ts, st, ed, obj, lang,'SOH', jiange_time, 'bc', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuStRan':  # 簇温度极差
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'temp_diff', jiange_time, 'bc', -99999999, 99999999)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        obj = []
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'max_temp_diff', jiange_time, 'bc',  -99999999, 99999999)
                    elif report_type == '4':
                        obj = []
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'max_temp_diff', jiange_time, 'bc', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_ts, end_ts, st, ed, obj, lang,'max_temp_diff', jiange_time, 'bc', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                elif kt == 'GetCuSVRan':  # 簇电压极差
                    if report_type == '1':
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'vol_diff', jiange_time, 'bc', -99999999, 99999999,cd=1)
                    elif report_type == '2' or report_type == '3':  # 周报，月报
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'max_vol_diff', jiange_time, 'bc', -99999999, 99999999,cd=1)
                    elif report_type == '4':
                        startTime = start_Time + ' 00:00:00'
                        endTime = end_Time + ' 23:59:59'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, startTime, endTime, st, ed, obj,lang, 'max_vol_diff', jiange_time, 'bc', -99999999, 99999999)
                    elif report_type == '5':
                        start_ts = start_ts_[0].strftime("%Y-%m-%d %H:%M:%S")[:4] + '-01-01'
                        end_ts = endTime[:4] + '-12-31'
                        obj = []
                        self.report_type_biaos(report_type, db, value_name, bc_table_name, start_ts, end_ts, st, ed, obj, lang,'max_vol_diff', jiange_time, 'bc', -99999999, 99999999)
                    return self.returnTypeSuc(obj)
                else:
                    return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()
            dongmu_session.close()

    def sorted_pcs_pcs(self, db, obj):
        if db == 'ygqn' or db == 'houma':
            # obj = sorted(obj, key=lambda x: (x['name'][0], extract_number(x['name'])), reverse=False)
            obj = natsorted(obj, key=lambda x: (x['name'][0], x['name']), reverse=False)
        else:
            # obj = sorted(obj, key=lambda x: (extract_number(x['name'])), reverse=False)
            obj = natsorted(obj, key=lambda x: x['name'], reverse=False)
        return obj

    def dongmu_PCS_pf(self, name, ed, st):
        '''东睦PCS获取因数，温度'''
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        list_ = {}
        list_['data'] = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            if values_mong:
                for v in values_mong:
                    body = json.loads(v['datainfo'])['body']
                    for b in body:
                        if b['device'][:4] == 'PCS%s' % (f + 1):
                            value = float(b[cu_name])  #
                            if 0 < value <= 100:
                                d.append(value)
                            else:
                                d.append(0)
            else:
                d.extend([1, 0])
            list_['data'].append(
                {'name': 'PCS-%s' % (f + 1), 'max': float('%.3f' % max(d)), 'min': float('%.3f' % min(d))})
        return list_

    def dongmu_sys_soh(self, SOH_name, d, enTime, max, min, stTime):
        '''东睦系统soh计算'''
        st = timeUtils.timeStrToTamp(stTime)  # 起始时间绝对秒
        ed = timeUtils.timeStrToTamp(enTime)  # 截止时间绝对秒
        dm_table = HisDM('r_measure')
        list_ = []
        values = dongmu_session.query(dm_table.utime, dm_table.datainfo).filter(dm_table.time.between(st, ed)).all()
        if values:
            for v in values:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][0:3] == 'BMS':
                        value = float(b[SOH_name])  #
                        if not value:
                            d.append(100)
                        if 0 < value <= 100:
                            d.append(value)
                        else:
                            d.append(100)
        else:
            d.append(100)
        if d != []:
            list_.append({'min': min(d)})
        return list_


    def dongmu_cu_v_t_j(self, cu_name, ed, list1, obj, st, lang=None):
        '''东睦电池簇温度极差，电压极差计算方式'''
        dm_table = HisDM('r_measure')
        obj = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            for v in values_mong:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][:4] == 'BMS%s' % (f + 1):
                        value = float(b[cu_name])  #
                        if 0 <= value <= 100:
                            d.append(value)
            if lang == 'en':
                obj.append({'pcs_name': 'PCS-%s' % (f + 1),
                            'cu': [{'cu_name': 'Battery cluster%s' % (f + 1), 'max': max(d) if d else 100}]})
            else:
                obj.append({'pcs_name': 'PCS-%s' % (f + 1),
                            'cu': [{'cu_name': '电池簇%s' % (f + 1), 'max': max(d) if d else 100}]})

        dongmu_session.close()

        return obj

    def sorted_pcs(self, db, obj):
        if db == 'ygqn' or db == 'houma':
            obj = natsorted(obj, key=lambda x: (x['pcs_name'][0], x['pcs_name']), reverse=False)
        else:
            obj = natsorted(obj, key=lambda x: x['pcs_name'], reverse=False)
        return obj

    def report_type_biaos(self, report_type,station_name, value_name, table_name, startTime, endTime,st,ed, obj,lang,z,jiange_ti,s,min_, max_,cd=None):
        '''z值名称的key,s设备名称,'''
        if report_type == '1':  # 日报
            self.data_day(station_name, value_name, table_name['1'], startTime, endTime,'window_end', obj, z, lang,s,min_,max_,cd)
        elif report_type == '2' or report_type == '3':# 周报，月报
            self.data_zhou_yue(report_type,station_name, value_name, table_name, startTime, endTime,'start_date', obj, z, lang, s, min_, max_,cd,jiange_ti)
        else:#time_ty='star_date'
            self.data_nian_leij(report_type,station_name, value_name, table_name, startTime,endTime,'start_date', obj, z, lang, s, min_, max_,cd,jiange_ti)
    def data_day(self, station_name,value_name,table_name,startTime,endTime, time_ty,obj,z,lang,s,min_,max_,cd=None):
        """查询系统日数据"""""
        from datetime import datetime, timedelta
        if s=='st':
            result = self.data_sql_(value_name[z],0,0,0,0,0, table_name, station_name, startTime[:10],endTime[:10],time_ty, min_, max_,'2',report_ty='min')
            value = []
            if result:
                for r in result:
                    # l_time = r['window_start'].strftime("%d %H:%M")
                    l_time = (r['window_end']+timedelta(seconds=1)).strftime("%d %H:%M")
                    if l_time not in obj['time']:
                        obj['time'].append(l_time)
                    value.append('%.1f' % r[value_name[z]])
            if z == 'yougong':#有功功率
                if lang == 'en':
                    obj['data'].append({'name': 'Active power(kw)', 'value': value})
                else:
                    obj['data'].append({'name': '有功功率(kw)',  'value': value})
            elif z == 'pf':#功率因数
                if lang == 'en':
                    obj['data'].append({'name': 'Power factor', 'value': value})
                else:
                    obj['data'].append({'name': '功率因数',  'value': value})
            else:
                obj['data'].append({'name': z+'(%)', 'value': value})
            
            # 获取当天的起始时间，即00:00:00
            if isinstance(endTime,str):
                end_Time = datetime.strptime(endTime[:10],"%Y-%m-%d")
            start_time = end_Time.replace(hour=0, minute=0, second=0, microsecond=0)
            time_list = []
            # 循环生成当天每15分钟的时间
            # print ('1925-----',obj)
            while start_time.day == end_Time.day:
                time_list.append(start_time)
                start_time += timedelta(minutes=15)
            for ind ,time in enumerate(time_list):
                _time = time.strftime('%d %H:%M')
                if _time not in obj.get("time"):
                    obj.get("time").insert(ind , _time)
                    for item in obj['data']:
                        item.get("value").insert(ind , '--')
            # for time in time_list:
            #     _time = time.strftime('%d %H:%M')
            #     if _time not in obj.get("time"):
            #         obj.get("time").append(_time)
            #         for item in obj['data']:
            #             item.get("value").append("--")
        elif s == 'pcs':
            result = self.data_sql_(value_name[z],value_name['pcs_name'], 0, 0,0,0, table_name, station_name, startTime,endTime, time_ty, min_, max_, '3',report_ty='min')
            value_n = {}
            for r in result:
                # l_time = r['window_start'].strftime("%d %H:%M")
                l_time = (r['window_end']+timedelta(seconds=1)).strftime("%d %H:%M")
                if l_time not in obj['time']:
                    obj['time'].append(l_time)
                if r['pcs_name'] not in value_n.keys():
                    value_n[r['pcs_name']] = []
                    if r[value_name[z]] or r[value_name[z]] == 0:
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                    else:
                        value_n[r['pcs_name']].append('--')
                else:
                    if r[value_name[z]] or r[value_name[z]] == 0:
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                    else:
                        value_n[r['pcs_name']].append('--')
            for d in value_n.keys():
                obj['data'].append({'name': d, 'value': value_n[d]})
            return obj
        elif s == 'bc':
            result = self.data_sql_(value_name[z],value_name['pcs_name'],value_name['bu_name']['zh'], value_name['bu_name']['en'],0,0,table_name, station_name,
                                    startTime, endTime, time_ty, min_, max_, '5',report_ty='min')
            value_n = {}
            for r in result:
                # l_time = r['window_start'].strftime("%d %H:%M")
                l_time = (r['window_end']+timedelta(seconds=1)).strftime("%d %H:%M")
                if l_time not in obj['time']:
                    obj['time'].append(l_time)
                if cd==1:
                    dd='%.3f'
                else:
                    dd = '%.1f'
                if r['pcs_name'] not in value_n.keys():
                    value_n[r['pcs_name']] = {}
                    if lang == 'en':
                        if r[value_name['bu_name']['en']] not in value_n[r['pcs_name']].keys():
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]] = []
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                        else:
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                    else:
                        if r[value_name['bu_name']['zh']] not in value_n[r['pcs_name']].keys():
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]] = []
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                        else:
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                else:
                    if lang == 'en':
                        if r[value_name['bu_name']['en']] not in value_n[r['pcs_name']].keys():
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]] = []
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                        else:
                            value_n[r['pcs_name']][r[value_name['bu_name']['en']]].append(dd % r[value_name[z]]  if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                    else:
                        if r[value_name['bu_name']['zh']] not in value_n[r['pcs_name']].keys():
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]] = []
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]].append(dd % r[value_name[z]] if r[value_name[z]] or r[value_name[z]] == 0 else '--')
                        else:
                            value_n[r['pcs_name']][r[value_name['bu_name']['zh']]].append(dd % r[value_name[z]] if r[value_name[z]] or r[value_name[z]] == 0 else '--')
            for d in value_n.keys():
                cu = []
                for rr in value_n[d].keys():
                    cu.append({'cu_name': rr, 'value': value_n[d][rr]})
                cu = natsorted(cu, key=lambda x: x['cu_name'],reverse=False)
                obj['data'].append({'pcs_name': d, 'cu':cu})
        return obj
    def data_zhou_yue(self, report_type,station_name,value_name,table_name,startTime,endTime, time_ty,obj,z,lang,s,min_,max_,cd,jiange_ti):
        """查询系统周月数据，time_ty时间类型"""""
        if report_type == '2':
            date_type = 'year_week'
        else:
            date_type = 'year_month'
        if s=='st':
            if z=='chagdisg':
                result = self.data_sql_(value_name['chagdisg']['chag'], value_name['chagdisg']['disg'],value_name['chagdisg']['num'],0,0,0,table_name[report_type], station_name, startTime[:10], endTime[:10],
                                        date_type,min_, max_, '4',report_ty='day')
                if result:
                    for r in result:
                        obj['time'].append(r['day'].strftime("%Y-%m-%d"))
                        chag_=r[value_name['chagdisg']['chag']]
                        disg_=r[value_name['chagdisg']['disg']]
                        chag_disg_num=r[value_name['chagdisg']['num']]
                        if cd == 1:#返回充放电数据
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj['data'].append({'chag':chag_,'disg':disg_,'ratio':ratio})
                        else:
                            obj['data'].append(chag_disg_num)
            elif z == 'pf_s_i':
                result = self.data_sql_(value_name['pf_s_i']['max_pf'], value_name['pf_s_i']['min_pf'], 0, 0,0,0,
                                        table_name[report_type], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '3',report_ty='day')
                if result:
                    for r in result:
                        obj['time'].append(r['day'].strftime("%Y-%m-%d"))
                        obj['data'].append({'max': r[value_name['pf_s_i']['max_pf']], 'min': r[value_name['pf_s_i']['min_pf']]})
            elif z=='SOH':
                result = self.data_sql_(value_name[z], 0,0, 0,0,0, table_name[report_type], station_name, startTime[:10], endTime[:10],date_type,
                                        min_,max_, '2',report_ty='day')
                if result:
                    for r in result:
                        obj['time'].append(r['day'].strftime("%Y-%m-%d"))
                        obj['data'].append({'min':('%.1f' % r[value_name[z]])})
        elif s == 'pcs':
            if z=='chagdisg':
                result = self.data_sql_(value_name['chagdisg']['chag'], value_name['chagdisg']['disg'], value_name['chagdisg']['num'],0,0,0,table_name[report_type], station_name, startTime[:10], endTime[:10],date_type,min_, max_,'4')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']]={'chag':[],'disg':[],'chag_disg_num':[]}
                            ch_di[r['pcs_name']]['chag'].append(r[value_name['chagdisg']['chag']])
                            ch_di[r['pcs_name']]['disg'].append(r[value_name['chagdisg']['disg']])
                            ch_di[r['pcs_name']]['chag_disg_num'].append(r[value_name['chagdisg']['num']])
                        else:
                            ch_di[r['pcs_name']]['chag'].append(r[value_name['chagdisg']['chag']])
                            ch_di[r['pcs_name']]['disg'].append(r[value_name['chagdisg']['disg']])
                            ch_di[r['pcs_name']]['chag_disg_num'].append(r[value_name['chagdisg']['num']])

                    for k,v in ch_di.items():
                        chag_=sum(ch_di[k]['chag'])
                        disg_=sum(ch_di[k]['disg'])
                        if cd == 1:#返回充放电数据
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj.append({'name':k,'chag':chag_,'disg':disg_,'ratio':ratio})
                        else:
                            chag_disg_num = sum(ch_di[k]['chag_disg_num'])
                            obj['data'].append({'name':k,'chag_disg_num':chag_disg_num})
            elif z == 'pf_s_i':
                result = self.data_sql_(value_name['pf_s_i']['max_pf'], value_name['pf_s_i']['min_pf'], 'pcs_name', 0, 0,0,
                                        table_name[report_type], station_name, startTime[:10], endTime[:10], date_type,min_, max_, '3')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']] = {'max':[],'min':[]}
                            ch_di[r['pcs_name']]['max'].append(r[value_name['pf_s_i']['max_pf']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['pf_s_i']['min_pf']])
                        else:
                            ch_di[r['pcs_name']]['max'].append(r[value_name['pf_s_i']['max_pf']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['pf_s_i']['min_pf']])
                    for k, v in ch_di.items():
                        max_1 = max(ch_di[k]['max'])
                        min_1 = min(ch_di[k]['min'])
                        obj['data'].append({'name': k, 'max':max_1,'min': min_1})
            elif z == 'temp_s_i':
                result = self.data_sql_(value_name['temp_s_i']['max_temp'], value_name['temp_s_i']['min_temp'], 'pcs_name', 0, 0,0,
                                        table_name[report_type], station_name, startTime[:10], endTime[:10], date_type,min_, max_, '3')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']] = {'max':[],'min':[]}
                            ch_di[r['pcs_name']]['max'].append(r[value_name['temp_s_i']['max_temp']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['temp_s_i']['min_temp']])
                        else:
                            ch_di[r['pcs_name']]['max'].append(r[value_name['temp_s_i']['max_temp']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['temp_s_i']['min_temp']])
                    for k, v in ch_di.items():
                        max_1 = max(ch_di[k]['max'])
                        min_1 = min(ch_di[k]['min'])
                        obj['data'].append({'name': k, 'max': max_1, 'min': min_1})
            else:
                # 查询历史数据
                result = self.data_sql_(value_name[z], jiange_ti,0,0,0,0, table_name['5'], station_name, startTime,endTime, date_type, min_, max_, '6',report_type)
                value_n = {}
                for r in result:
                    l_time = r['window_end'].strftime("%m-%d %H")
                    if l_time not in obj['time']:
                        obj['time'].append(l_time)
                    if r['pcs_name'] not in value_n.keys():
                        value_n[r['pcs_name']] = []
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                    else:
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                for d in value_n.keys():
                    obj['data'].append({'name': d, 'value': value_n[d]})
        elif s == 'bc':
            if z=='chagdisg':
                result = self.data_sql_(value_name['bu_name']['zh'],value_name['bu_name']['en'],value_name['chagdisg']['chag'],
                                        value_name['chagdisg']['disg'],value_name['chagdisg']['num'],date_type,table_name[report_type], station_name, startTime[:10], endTime[:10],time_ty, min_, max_,'8')
                if result:
                    value_n = {}
                    for r in result:
                        chag_ = r[value_name['chagdisg']['chag']]
                        disg_ = r[value_name['chagdisg']['disg']]
                        chag_disg_num = r[value_name['chagdisg']['num']]
                        cu_name = self.bc_name(lang, r, value_name)
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = []
                            if cd == 1:#返回充放电数据
                                if float(chag_) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                value_n[r['pcs_name']].append({'chag':chag_,'disg':disg_,'ratio':ratio,'cu_name':cu_name})
                            else:
                                value_n[r['pcs_name']].append({'chag_disg_num': chag_disg_num,'cu_name': cu_name})
                        else:
                            if cd == 1:#返回充放电数据
                                if float(chag_) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                value_n[r['pcs_name']].append({'chag':chag_,'disg':disg_,'ratio':ratio,'cu_name':cu_name})
                            else:
                                value_n[r['pcs_name']].append({'chag_disg_num': chag_disg_num,'cu_name': cu_name})
                        if cd == 1:  # 返回充放电数据
                            value_n[r['pcs_name']] = natsorted(value_n[r['pcs_name']], key=lambda x: x['cu_name'], reverse=False)
                        else:
                            value_n[r['pcs_name']] = natsorted(value_n[r['pcs_name']], key=lambda x:x['cu_name'],reverse=False)
                    for k,v in value_n.items():
                        obj.append({'pcs_name':k, 'cu':v})
            elif z=='SOH':#查4个值
                result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0, 0,0,
                                        table_name[report_type], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '4')
                if result:
                    value_n = {}
                    for r in result:
                        cu_name = self.bc_name(lang, r, value_name)
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = []
                            value_n[r['pcs_name']].append({'cu_name': cu_name,'min': ('%.1f' % r[value_name[z]])})
                        else:
                            value_n[r['pcs_name']].append({'cu_name': cu_name,'min': ('%.1f' % r[value_name[z]])})
                        value_n[r['pcs_name']] = sorted(value_n[r['pcs_name']], key=lambda x: x.get("min"),reverse=True)
                    for k,v in value_n.items():
                        obj.append({'pcs_name':k, 'cu':v})
            else:
                result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0, 0,0,
                                        table_name[report_type], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '4')
                if result:
                    value_n = {}
                    for r in result:
                        cu_name = self.bc_name(lang, r, value_name)
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = []
                            if cd==1:
                                value_n[r['pcs_name']].append({'cu_name': cu_name,'max': ('%.3f' % r[value_name[z]])})
                            else:
                                value_n[r['pcs_name']].append({'cu_name': cu_name, 'max': ('%.1f' % r[value_name[z]])})
                        else:
                            if cd == 1:
                                value_n[r['pcs_name']].append({'cu_name': cu_name,'max': ('%.3f' % r[value_name[z]])})
                            else:
                                value_n[r['pcs_name']].append({'cu_name': cu_name, 'max': ('%.1f' % r[value_name[z]])})
                        value_n[r['pcs_name']] = sorted(value_n[r['pcs_name']], key=lambda x: x.get("max"),reverse=True)
                    for k,v in value_n.items():
                        obj.append({'pcs_name':k, 'cu':v})
        return obj
    def data_nian_leij(self, report_type,station_name,value_name,table_name,startTime,endTime,time_ty,obj,z,lang,s,min_,max_,cd,jiange_ti):
        """查询系统年累计数据，time_ty时间类型"""""
        if report_type == '5':
            date_type = 'year'
        else:
            date_type = 'year_month'
        if s=='st':#系统年和累计不计算当天
            if z=='chagdisg':
                result = self.data_sql_(value_name['chagdisg']['chag'], value_name['chagdisg']['disg'],value_name['chagdisg']['num'],0, 0,0,
                                        table_name['4'], station_name, startTime[:10], endTime[:10], date_type,min_, max_, '4','st')
                if result:
                    for r in result:
                        self.nian_leij_time(obj, r, report_type, time_ty)
                        chag_=r[value_name['chagdisg']['chag']] if r[value_name['chagdisg']['chag']] else 0
                        disg_=r[value_name['chagdisg']['disg']] if r[value_name['chagdisg']['disg']] else 0
                        chag_disg_num = r[value_name['chagdisg']['num']] if r[value_name['chagdisg']['num']] else 0
                        if cd == 1:#返回充放电数据
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj['data'].append({'chag':chag_,'disg':disg_,'ratio':ratio})
                        else:
                            obj['data'].append(chag_disg_num)
            elif z == 'pf_s_i':
                result = self.data_sql_(value_name['pf_s_i']['max_pf'], value_name['pf_s_i']['min_pf'], time_ty, 0, 0,0,
                                          table_name['4'], station_name, startTime[:10], endTime[:10],
                                          date_type, min_, max_, '3')
                if result:
                    for r in result:
                        self.nian_leij_time(obj, r, report_type, time_ty)
                        obj['data'].append({'max': r[value_name['pf_s_i']['max_pf']], 'min': r[value_name['pf_s_i']['min_pf']]})
            elif z=='SOH':
                result = self.data_sql_(value_name[z], time_ty, 0, 0, 0,0, table_name['4'], station_name,startTime[:10], endTime[:10], date_type, min_, max_, '2')
                if result:
                    for r in result:
                        self.nian_leij_time(obj, r, report_type, time_ty)
                        obj['data'].append({'min':('%.1f' % r[value_name[z]])})
        elif s == 'pcs':
            if z=='chagdisg':
                result = self.data_sql_(value_name['chagdisg']['chag'], value_name['chagdisg']['disg'], value_name['chagdisg']['num'], 0, 0,0,
                                        table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '4')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']] = {'chag': [], 'disg': [] ,'chag_disg_num': []}
                            ch_di[r['pcs_name']]['chag'].append(r[value_name['chagdisg']['chag']])
                            ch_di[r['pcs_name']]['disg'].append(r[value_name['chagdisg']['disg']])
                            ch_di[r['pcs_name']]['chag_disg_num'].append(r[value_name['chagdisg']['num']])
                        else:
                            ch_di[r['pcs_name']]['chag'].append(r[value_name['chagdisg']['chag']])
                            ch_di[r['pcs_name']]['disg'].append(r[value_name['chagdisg']['disg']])
                            ch_di[r['pcs_name']]['chag_disg_num'].append(r[value_name['chagdisg']['num']])
                    for k, v in ch_di.items():
                        chag_ = sum(ch_di[k]['chag'])
                        disg_ = sum(ch_di[k]['disg'])
                        if cd == 1:#返回充放电数据
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.2f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj.append({'name':k,'chag':chag_,'disg':disg_,'ratio':ratio})
                        else:
                            chag_disg_num = sum(ch_di[k]['chag_disg_num'])
                            obj['data'].append({'name':k,'chag_disg_num':chag_disg_num})
            elif z == 'pf_s_i':
                result = self.data_sql_(value_name['pf_s_i']['max_pf'], value_name['pf_s_i']['min_pf'], 'pcs_name', 0, 0,0,
                                        table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '3')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']] = {'max':[],'min':[]}
                            ch_di[r['pcs_name']]['max'].append(r[value_name['pf_s_i']['max_pf']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['pf_s_i']['min_pf']])
                        else:
                            ch_di[r['pcs_name']]['max'].append(r[value_name['pf_s_i']['max_pf']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['pf_s_i']['min_pf']])
                    for k, v in ch_di.items():
                        max_1 = max(ch_di[k]['max'])
                        min_1 = min(ch_di[k]['min'])
                        obj['data'].append({'name': k, 'max':max_1,'min': min_1})
            elif z == 'temp_s_i':
                result = self.data_sql_(value_name['temp_s_i']['max_temp'], value_name['temp_s_i']['min_temp'], 'pcs_name', 0, 0,0,
                                        table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                        min_, max_, '3')
                if result:
                    ch_di = {}
                    for r in result:
                        if r['pcs_name'] not in ch_di.keys():
                            ch_di[r['pcs_name']] = {'max':[],'min':[]}
                            ch_di[r['pcs_name']]['max'].append(r[value_name['temp_s_i']['max_temp']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['temp_s_i']['min_temp']])
                        else:
                            ch_di[r['pcs_name']]['max'].append(r[value_name['temp_s_i']['max_temp']])
                            ch_di[r['pcs_name']]['min'].append(r[value_name['temp_s_i']['min_temp']])
                    for k, v in ch_di.items():
                        max_1 = max(ch_di[k]['max'])
                        min_1 = min(ch_di[k]['min'])
                        obj['data'].append({'name': k, 'max': max_1, 'min': min_1})
            else:
                # 查询历史数据
                date_type = 'year'
                result = self.data_sql_(value_name[z], jiange_ti, 0, 0, 0, 0, table_name['5'], station_name, startTime, endTime,date_type, min_, max_, '6', report_type)
                value_n = {}
                for r in result:
                    if report_type == '5':
                        l_time = r['window_end'].strftime("%Y-%m-%d %H")
                    else:
                        l_time = r['window_end'].strftime("%m-%d %H")
                    if l_time not in obj['time']:
                        obj['time'].append(l_time)
                    if r['pcs_name'] not in value_n.keys():
                        value_n[r['pcs_name']] = []
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                    else:
                        value_n[r['pcs_name']].append('%.1f' % r[value_name[z]])
                for d in value_n.keys():
                    obj['data'].append({'name': d, 'value': value_n[d]})
        elif s == 'bc':
            if z=='chagdisg':
                if report_type == '5':
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'],
                                            value_name['chagdisg']['chag'], value_name['chagdisg']['disg'],
                                            value_name['chagdisg']['num'], date_type,
                                            table_name['4'], station_name, startTime[:10], endTime[:10], time_ty,
                                            min_, max_, '8')
                else:
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'],
                                            value_name['chagdisg']['chag'], value_name['chagdisg']['disg'], value_name['chagdisg']['num'],date_type,
                                            table_name['4'], station_name, startTime[:10], endTime[:10], time_ty,
                                            min_, max_, '8')
                if result:
                    value_n = {}
                    for r in result:
                        cu_name = self.bc_name(lang, r, value_name)
                        chag_ = r[value_name['chagdisg']['chag']]
                        disg_ = r[value_name['chagdisg']['disg']]
                        chag_disg_num = r[value_name['chagdisg']['num']]
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = {}
                            if cu_name not in value_n[r['pcs_name']].keys():
                                value_n[r['pcs_name']][cu_name] = {'chag':[],'disg':[],'chag_disg_num':[]}
                                if cd == 1:#返回充放电数据
                                    value_n[r['pcs_name']][cu_name]['chag'].append(chag_)
                                    value_n[r['pcs_name']][cu_name]['disg'].append(disg_)
                                else:
                                    value_n[r['pcs_name']][cu_name]['chag_disg_num'].append(chag_disg_num)
                            else:
                                if cd == 1:  # 返回充放电数据
                                    value_n[r['pcs_name']][cu_name]['chag'].append(chag_)
                                    value_n[r['pcs_name']][cu_name]['disg'].append(disg_)
                                else:
                                    value_n[r['pcs_name']][cu_name].append({'chag_disg_num': chag_disg_num})
                        else:
                            if cu_name not in value_n[r['pcs_name']].keys():
                                value_n[r['pcs_name']][cu_name] = {'chag':[],'disg':[],'chag_disg_num':[]}
                                if cd == 1:#返回充放电数据
                                    value_n[r['pcs_name']][cu_name]['chag'].append(chag_)
                                    value_n[r['pcs_name']][cu_name]['disg'].append(disg_)
                                else:
                                    value_n[r['pcs_name']][cu_name]['chag_disg_num'].append(chag_disg_num)
                            else:
                                if cd == 1:  # 返回充放电数据
                                    value_n[r['pcs_name']][cu_name]['chag'].append(chag_)
                                    value_n[r['pcs_name']][cu_name]['disg'].append(disg_)
                                else:
                                    value_n[r['pcs_name']][cu_name]['chag_disg_num'].append(chag_disg_num)
                    for k, v in value_n.items():
                        cu = []
                        for vv in v.keys():
                            if cd == 1:  # 返回充放电数据
                                chag_cu=sum(v[vv]['chag'])
                                disg_cu=sum(v[vv]['disg'])
                                if float(chag_cu) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.2f' % ((float(disg_cu) / float(chag_cu)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                cu.append({'cu_name':vv,'chag':chag_cu,'disg':disg_cu,'ratio':ratio})
                            else:
                                cu.append({'cu_name': vv, 'chag_disg_num': sum(v[vv]['chag_disg_num'])})
                        if cd == 1:  # 返回充放电数据
                            cu= natsorted(cu, key=lambda x: x['cu_name'],reverse=False)
                        else:
                            cu = natsorted(cu, key=lambda x: x['cu_name'], reverse=False)
                        obj.append({'pcs_name': k, 'cu': cu})
            elif z=='SOH':
                if report_type == '5':
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0, 0,0,
                                            table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                            min_, max_, '4',report_ty='yea')
                else:
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0,
                                            0, 0,
                                            table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                            min_, max_, '4')
                if result:
                    value_n = {}
                    for r in result:
                        cu_name = self.bc_name(lang, r, value_name)
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = []
                            value_n[r['pcs_name']].append({'cu_name': cu_name,'min': ('%.1f' % r[value_name[z]])})
                        else:
                            value_n[r['pcs_name']].append({'cu_name': cu_name,'min': ('%.1f' % r[value_name[z]])})
                        value_n[r['pcs_name']] = sorted(value_n[r['pcs_name']], key=lambda x: x.get("min"),reverse=True)
                    for k,v in value_n.items():
                        obj.append({'pcs_name':k, 'cu':v})
            else:
                if report_type == '5':
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0, 0,0,
                                            table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                            min_, max_, '4',report_ty='yea')
                else:
                    result = self.data_sql_(value_name['bu_name']['zh'], value_name['bu_name']['en'], value_name[z], 0,
                                            0, 0,table_name['4'], station_name, startTime[:10], endTime[:10], date_type,
                                            min_, max_, '4')
                if result:
                    value_n = {}
                    for r in result:
                        cu_name = self.bc_name(lang, r, value_name)
                        if r['pcs_name'] not in value_n.keys():
                            value_n[r['pcs_name']] = {}
                            if cu_name not in value_n[r['pcs_name']].keys():
                                value_n[r['pcs_name']][cu_name] = {'max':[]}
                                value_n[r['pcs_name']][cu_name]['max'].append(r[value_name[z]])
                            else:
                                value_n[r['pcs_name']][cu_name]['max'].append(r[value_name[z]])
                        else:
                            if cu_name not in value_n[r['pcs_name']].keys():
                                value_n[r['pcs_name']][cu_name] = {'max': []}
                                value_n[r['pcs_name']][cu_name]['max'].append(r[value_name[z]])
                            else:
                                value_n[r['pcs_name']][cu_name]['max'].append(r[value_name[z]])
                    for k, v in value_n.items():
                        cu = []
                        for vv in v.keys():
                            max_cu='%.1f' % max(v[vv]['max'])
                            cu.append({'cu_name':vv,'max':max_cu})
                            cu= sorted(cu, key=lambda x: x.get("max"),reverse=True)
                        obj.append({'pcs_name': k, 'cu': cu})
        return obj
    def bc_name(self,lang, r, value_name):
        '''簇名称'''
        if lang == 'en':
            cu_name = r[value_name['bu_name']['en']]
        else:
            cu_name = r[value_name['bu_name']['zh']]
        return cu_name
    def nian_leij_time(self, obj, r, report_type, time_ty):
        if report_type == '4':
            obj['time'].append(r[time_ty].strftime("%Y-%m-%d")[:7])
        elif report_type == '5':
            obj['time'].append(r[time_ty].strftime("%Y-%m-%d")[:4])
    def data_sql_(self, value_name_1,value_name_2,value_name_3,value_name_4,value_name_5,value_name_6,table_name,station_name,startTime,endTime,time_ty,min_,max_,o,report_ty=None):
        """查询数据"""""
        conn = pool.connection()
        cursor = conn.cursor()
        try:
            sql=[]
            # 执行SQL查询
            if o =='2':#查询2个值
                if report_ty=='min':#查询分钟表
                    sql = self.sql_2_min(max_, min_, startTime, station_name, table_name, value_name_1)
                elif report_ty=='day':#查询天表
                    sql = self.sql_2_day(report_ty,endTime, max_, min_, startTime, station_name, table_name, value_name_1)
                else:
                    sql = self.sql_2(value_name_2, endTime, max_, min_, startTime, station_name, table_name, time_ty,value_name_1)
            elif o == '3':#查询3个值
                if report_ty=='min':#查询分钟表
                    sql = self.sql_3_min(value_name_2,max_, min_, startTime, station_name, table_name, value_name_1)
                elif report_ty == 'day':  # 查询天表
                    sql = self.sql_3_day(report_ty, endTime, max_, min_, startTime, station_name, table_name,value_name_1, value_name_2)
                else:
                    sql = self.sql_3(value_name_3, endTime, max_, min_, startTime, station_name, table_name, time_ty,value_name_1, value_name_2)
            elif o == '4':#查询4个值
                if report_ty == 'YL':  # 查询分钟表
                    sql = self.sql_4_YL(endTime, max_, min_, startTime, station_name, table_name, time_ty, value_name_1,value_name_2,value_name_3)
                elif report_ty == 'day':  # 查询天表
                    sql = self.sql_4_day(report_ty, endTime, max_, min_, startTime, station_name, table_name,time_ty,value_name_1, value_name_2,value_name_3)
                elif report_ty == 'yea':  #
                    sql = self.sql_4_yea(endTime, max_, min_, startTime, station_name, table_name, time_ty,value_name_1, value_name_2, value_name_3)
                elif report_ty == 'st':  #
                    sql = self.sql_4_st(endTime, max_, min_, startTime, station_name, table_name, time_ty,value_name_1, value_name_2, value_name_3)
                else:
                    sql = self.sql_4(endTime, max_, min_, startTime, station_name, table_name, time_ty, value_name_1,value_name_2, value_name_3)
            elif o == '5':#查询5个值
                if report_ty == 'min':  # 查询分钟表
                    sql = self.sql_5_min(value_name_2,max_, min_, startTime, station_name, table_name, value_name_1,value_name_3,value_name_4)
            elif o == '6':#查询pcs电流和电压数据
                sql = self.sql_6(endTime, max_, min_, startTime, station_name, table_name,value_name_1,value_name_2,time_ty)
            elif o == '7':#查询5个值(簇充放电)
                sql = self.sql_7(max_, min_, startTime,endTime, station_name, table_name,value_name_1,value_name_2,value_name_3,value_name_4,value_name_5)
            elif o == '8':#
                if report_ty == 'YL':  # 查询分钟表
                    sql = self.sql_8_YL(max_, min_, startTime, endTime, station_name, table_name, value_name_1,value_name_2, value_name_3, value_name_4, value_name_5, value_name_6)
                else:
                    sql = self.sql_8(max_, min_, startTime,endTime, station_name, table_name,value_name_1,value_name_2,value_name_3,value_name_4,value_name_5,value_name_6)
            try:
                cursor.execute(sql)
            except Exception as e:
                logging.error(e)
                return []
            # 获取查询结果
            result = cursor.fetchall()
            return result
        except Exception as e:
            logging.error(e)
            raise
        finally:
            cursor.close()
            conn.close()
    def sql_2_min(self,max_, min_, startTime, station_name, table_name,value_name):
        sql = """SELECT window_end, {}
                    FROM {}
                    where station_name ='{}'
                    and day ='{}'
                    and {} BETWEEN '{}' AND '{}'
                    order by window_end asc
                    """.format(value_name, table_name, station_name,startTime,
                               value_name, min_, max_)
        return sql
    def sql_2_day(self, ti_pcs, endTime, max_, min_, startTime, station_name, table_name,value_name):
        sql = """SELECT {}, {}
                    FROM {}
                    where station_name ='{}'
                    and day>='{}'
                    and day<='{}'
                    and {} BETWEEN '{}' AND '{}'
                    order by day asc
                    """.format(ti_pcs, value_name, table_name, station_name,startTime, endTime,
                               value_name, min_, max_)
        return sql
    def sql_2(self, ti_pcs, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name):
        sql = """SELECT {}, {}
                    FROM {}
                    where station_name ='{}'
                    and date_type ='{}'
                    and date_value like '{}'
                    and {} BETWEEN '{}' AND '{}'
                    order by {} asc
                    """.format(ti_pcs, value_name, table_name, station_name,date_type, (startTime[:4]+'%'),
                               value_name, min_, max_,ti_pcs)
        return sql
    def sql_3_min(self,value_name_2,max_, min_, startTime, station_name, table_name,value_name):
        sql = """SELECT window_end, {}, {}
                    FROM {}
                    where station_name ='{}'
                    and day ='{}'
                    and ({} BETWEEN '{}' AND '{}' OR {} is NUll)
                    order by window_end asc
                    """.format(value_name,value_name_2, table_name, station_name,startTime[:10],
                               value_name, min_, max_, value_name)
        return sql
    def sql_3_day(self, ti_pcs,endTime, max_, min_, startTime, station_name, table_name,  value_name_1, value_name_2):#ti_pcs表示时间或者pcs名称
        sql = """SELECT {},{},{}
                            FROM {}
                            where station_name ='{}'
                            and '{}'<=day
                            and day<='{}'
                            and {} BETWEEN '{}' AND '{}'
                            and {} BETWEEN '{}' AND '{}'
                            order by day asc
                            """.format(ti_pcs,value_name_2, value_name_1, table_name, station_name,startTime, endTime, value_name_1, min_, max_,value_name_2, min_, max_)
        return sql
    def sql_3(self, ti_pcs,endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2):#ti_pcs表示时间或者pcs名称
        sql = """SELECT {},{},{}
                            FROM {}
                            where station_name ='{}'
                            and start_date>='{}'
                            and end_date<='{}'
                            and date_type ='{}'
                            and ({} BETWEEN '{}' AND '{}' or {} BETWEEN '{}' AND '{}')
                            order by {} asc
                            """.format(ti_pcs,value_name_2, value_name_1, table_name, station_name,startTime,endTime,date_type, value_name_1, min_, max_,value_name_2, min_, max_,ti_pcs)
        return sql
    def sql_4(self, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2,value_name_3):
        sql = """SELECT pcs_name,{},{},{}
                        FROM {}
                        where station_name ='{}'
                        and date_type ='{}'
                        and start_date>='{}'
                        and end_date<='{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_1, value_name_2,value_name_3,
                                   table_name, station_name,date_type, startTime, endTime, value_name_3, min_, max_)
        return sql
    def sql_4_YL(self, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2,value_name_3):
        sql = """SELECT pcs_name,{},{},{}
                        FROM {}
                        where station_name ='{}'
                        and date_type ='{}'
                        and date_value like '{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_1, value_name_2,value_name_3,
                                   table_name, station_name,date_type, (startTime[:4]+'%'), value_name_3, min_, max_)
        return sql
    def sql_4_day(self,ti_pcs, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2,value_name_3):
        sql = """SELECT {},{},{},{}
                        FROM {}
                        where station_name ='{}'
                        and day>='{}'
                        and day<='{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by day asc
                        """.format(ti_pcs,value_name_1, value_name_2,value_name_3,table_name, station_name,startTime, endTime, value_name_3, min_, max_)
        return sql
    def sql_4_yea(self, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2,value_name_3):
        sql = """SELECT pcs_name ,{},{},{}
                        FROM {}
                        where station_name ='{}'
                        and date_type ='{}'
                        and start_date>='{}'
                        and end_date<='{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_1, value_name_2,value_name_3,
                                   table_name, station_name,date_type, startTime, endTime, value_name_3, min_, max_)
        return sql
    def sql_4_st(self, endTime, max_, min_, startTime, station_name, table_name, date_type, value_name_1, value_name_2,value_name_3):
        sql = """SELECT start_date,{},{},{}
                        FROM {}
                        where station_name ='{}'
                        and date_type ='{}'
                        and start_date>='{}'
                        and end_date<='{}'
                        order by start_date asc
                        """.format(value_name_1, value_name_2,value_name_3,table_name, station_name,date_type, startTime, endTime)
        return sql
    def sql_5_min(self,value_name_2,max_, min_, startTime, station_name, table_name, value_name_1,value_name_3,value_name_4):
        sql = """SELECT window_end, {}, {},{},{}
                    FROM {}
                    where station_name ='{}'
                    and day ='{}'
                    and ({} BETWEEN '{}' AND '{}' OR {} is NUll)
                    order by window_end asc
                    """.format(value_name_1,value_name_2,value_name_3,value_name_4, table_name, station_name,startTime,
                               value_name_1, min_, max_, value_name_1)
        return sql
    def sql_6(self, endTime, max_, min_, startTime, station_name, table_name,value_name_1,value_name_2,report_type):
        sql = """SELECT window_end,pcs_name,{}
                        FROM {}
                        where station_name ='{}'
                        and start_date>='{}'
                        and start_date<='{}'
                        and {} BETWEEN '{}' AND '{}'
                        and date_type = '{}'
                        order by window_end asc
                        """.format(value_name_1,table_name, station_name,startTime,  endTime, value_name_1, min_, max_,report_type)

        return sql
    def sql_7(self, max_, min_, startTime,endTime, station_name, table_name,value_name_1, value_name_2,value_name_3,value_name_4,date_type):
        sql = """SELECT pcs_name,{},{},{},{}
                        FROM {}
                        where date_type ='{}'
                        and station_name ='{}'
                        and start_date ='{}'
                        and end_date ='{}'
                        and {} BETWEEN '{}' AND '{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_3, value_name_1, value_name_2,value_name_4,
                                   table_name,date_type, station_name,startTime,endTime, value_name_3, min_, max_,value_name_4, min_, max_)
        return sql
    def sql_8(self, max_, min_, startTime,endTime, station_name, table_name,value_name_1, value_name_2,value_name_3,value_name_4,date_type,value_name_6):
        sql = """SELECT pcs_name,{},{},{},{},{}
                        FROM {}
                        where date_type ='{}'
                        and station_name ='{}'
                        and start_date>='{}'
                        and end_date<='{}'
                        and {} BETWEEN '{}' AND '{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_3, value_name_1, value_name_2,value_name_4,date_type,
                                   table_name,value_name_6, station_name,startTime,endTime, value_name_3, min_, max_,value_name_4, min_, max_)
        return sql
    def sql_8_YL(self, max_, min_, startTime,endTime, station_name, table_name,value_name_1, value_name_2,value_name_3,value_name_4,date_type,value_name_6):
        sql = """SELECT pcs_name,{},{},{},{},{}
                        FROM {}
                        where date_type ='{}'
                        and station_name ='{}'
                        and date_value like '{}'
                        and {} BETWEEN '{}' AND '{}'
                        and {} BETWEEN '{}' AND '{}'
                        order by start_date asc
                        """.format(value_name_3, value_name_1, value_name_2,value_name_4,date_type,
                                   table_name,value_name_6, station_name,(startTime[:4]+'%'), value_name_3, min_, max_,value_name_4, min_, max_)
        return sql


    def sys_5_time_list(self, db, end_Time):
        '''系统累计时间列表'''
        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
        two_time_lists = []
        st = int(str(pages[0])[0:4])
        et = int(end_Time[0:4])
        if et - st > 0:
            tt = et - 1
            if tt - st > 1:
                ttt = tt - 1
                if ttt - st > 0:
                    two_time_lists.append(st)
                    two_time_lists.append(tt)
                    two_time_lists.append(ttt)
                    two_time_lists.append(et)
            else:
                two_time_lists.append(st)
                two_time_lists.append(tt)
                two_time_lists.append(et)
        y_time_lists = list(set(two_time_lists))
        return y_time_lists

    def dongmu_sys_SOC_d(self, na, ed, obj, st, z, lang=None):
        '''系统东睦SOC,因数日数据计算'''
        dataP1, data2 = [], {}  # 有功,
        PCS_name = model_config.get('peizhi', na)  # name
        dm_table = HisDM('r_measure')
        obj['time'] = []
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []
                for ii in alldata:
                    if ii['device'] == PCS_:
                        value = ii[PCS_name]  #
                        if float(value) < 0:
                            d2.append('0')
                        else:
                            d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()
                # 取相同的间隔时间(采用补数的方法)
                if data12["time"]:
                    data22 = complete_data(data12, '15T')
                    list_time = []
                if data22['value']:
                    for d in data22['value']:
                        try:
                            if math.isnan(d) is True:
                                indx = data22['value'].index(d)
                                data22['value'][indx] = 0
                        except:
                            break
                    if data22:
                        for i in data22['time']:
                            list_time.append(i[8:16])
                    data2['time'] = list_time
                    obj['time'] = list_time if list_time else []
                    obj_sys_list.append(list(map(float, data22['value'] if data22['value'] else 0)))
            # value_=np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()
            # if math.isnan(value_) is True:
            #     value_ =[]
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor',
                                            'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                    else:
                        obj['data'].append(
                            {'name': '功率因数', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'System SOC(%)',
                                            'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                    else:
                        obj['data'].append(
                            {'name': '系统SOC(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})

        dongmu_session.close()

    def sys_chag_disg(self, freports, obj, m, two_time_lists):
        '''系统充放电量'''
        n = 1
        for t in two_time_lists:  # 按时间循环
            disg = 0  # 放电量
            chag = 0  # 充电量
            ratio = 0  # 效率
            if freports:
                for f in freports:
                    if str(f.day)[:m] == str(t)[:m]:
                        disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                            eval(f.pd_disg)) + np.sum(eval(f.gd_disg))
                        chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                            eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
            if float(chag) == 0:
                ratio = 0
            else:
                ratio = ('%.2f' % ((float(disg) / float(chag)) * 100))
                if float(ratio) > 100:
                    ratio = 100
            obj['data'].append({'disg': int(disg), 'chag': int(chag), 'ratio': float(ratio)})
            n += 1

    def data_day_dongmu_yougong(self, ed=None, obj=None, st=None, name=None, t=None, z=None, obj_sys=None, vmax=None,
                                lang=None):
        '''系统，PCS东睦日数据'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    # index = alldata.index(ii)
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                # data12['time'].insert(0,'2023-06-17 00:00:00')
                # data12['time'].append('2023-06-19 00:00:00')
                # data12['value'].insert(0,data12['value'][0])
                # data12['value'].append(data12['value'][-1])
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                list_time.append(i[8:16])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if obj_sys == '系统':
                    if obj_sys_list != []:
                        obj_sys_list = np.array(obj_sys_list).astype(float) + np.array(data22['value']).astype(float)
                    else:
                        obj_sys_list = data22['value']
                else:
                    obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                        'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Active power(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})
        dongmu_session.close()

    def data_day_dongmu(self, ed=None, obj=None, st=None, name=None, t=None, z=None, obj_sys=None, vmax=None,
                        lang=None):
        '''系统，PCS东睦日数据'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    # index = alldata.index(ii)
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        if value < 0:
                            d2.append(0)
                        elif value > 1:
                            d2.append(1)
                        else:
                            d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                list_time.append(i[8:16])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if obj_sys == '系统':
                    if obj_sys_list != []:
                        obj_sys_list = np.array(obj_sys_list).astype(float) + np.array(data22['value']).astype(float)
                    else:
                        obj_sys_list = data22['value']
                else:
                    obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                        'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Active power(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})
        dongmu_session.close()
    def ys_chag_disg_num(self, db, freports, obj, two_time_lists, m, list2):
        '''系统充放电次数'''
        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
        n = 1
        freport_obj = {}  # 按name为key将值放到列表里
        for f in freports:
            if f.name in freport_obj:
                freport_obj[f.name].append(f)
            else:
                freport_obj[f.name] = []
                freport_obj[f.name].append(f)
        le = len(two_time_lists)
        for t in two_time_lists:
            index = two_time_lists.index(t)
            disg = 0  # 放电量
            chag = 0  # 充电量
            if freports:
                for f in freports:
                    if str(f.day)[:m] == str(t)[:m]:
                        disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                            eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                        chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                            eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
            if index == le - 1:
                if list2 != []:  # index == le-1（拿到最后一个值）
                    disg_ = 0
                    chag_ = 0
                    for l in list2:
                        if l['disg'] == '--' or l['chag'] == '--':
                            disg_ = '--'
                            chag_ = '--'
                            break
                        disg_ += float(l['disg'])
                        chag_ += float(l['chag'])
                    if disg_ == '--' or chag_ == '--':
                        disg1 = '--'
                        chag1 = '--'
                    else:
                        disg1 = disg + disg_  # 放电量
                        chag1 = chag + chag_  # 充电量
            else:
                disg1 = disg
                chag1 = chag
            if disg1 == '--' or chag1 == '--':
                obj['data'].append('--')
            else:
                chag_disg_num = math.floor((math.sqrt(disg1 * chag1)) / (volume[0] * 1000))
                obj['data'].append(chag_disg_num)
            n += 1

    def sys_soh_day_dongmu(self, ed, obj, st):
        '''系统东睦SOH'''
        dataP1, data2 = [], {}  # 有功,
        cu_name = model_config.get('peizhi', 'cu_SOH_4')  # name
        dm_table = HisDM('r_measure')
        obj_sys_list = []
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            for e in range(dongmu_num):
                BMS_ = 'BMS%s' % (e + 1)
                d2 = []
                for ii in alldata:
                    if ii['device'] == BMS_:
                        value = ii[cu_name]  #
                        if float(value) <= 100:
                            d2.append(value)
                        # 取相同的间隔时间(采用补数的方法)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()
                if data12["time"]:
                    data22 = complete_data(data12, '15T')
                    list_time = []
                    if data22:
                        for i in data22['time']:
                            list_time.append(i[8:16])
                    data2['time'] = list_time
                    obj['time'] = list_time
                    for d in data22['value']:
                        try:
                            if math.isnan(d) is True:
                                indx = data22['value'].index(d)
                                data22['value'][indx] = 80
                        except:
                            break
                    obj_sys_list.append(list(map(float, data22['value'])))
            obj['data'].append({'name': 'SOH(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
            dongmu_session.close()

    def sys_soh(self, SOH, min, obj, two_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if s[1] not in dict_SOH.keys():
                    dict_SOH[s[1]] = []
                    if float(s[0]) != 0:
                        dict_SOH[s[1]].append(s[0])
            for i in two_time_lists:
                min_ = 0
                for d in dict_SOH.keys():
                    if i == str(d)[:10]:
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def sys_soh_year(self, SOH, min, obj, two_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                s0 = float(s[0]) if s[0] else float(0)
                if str(s[1])[:7] not in dict_SOH.keys():
                    dict_SOH[str(s[1])[:7]] = []
                    if s0 != 0:
                        dict_SOH[str(s[1])[:7]].append(s0)
                else:
                    if s0 != 0:
                        dict_SOH[str(s[1])[:7]].append(s0)
            for i in two_time_lists:
                for d in dict_SOH.keys():
                    if i == d:
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def sys_soh_5(self, SOH, min, obj, y_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if str(s[1])[:4] not in dict_SOH.keys():
                    dict_SOH[str(s[1])[:4]] = []
                    if s[0] and float(s[0]) != 0:
                        dict_SOH[str(s[1])[:4]].append(float(s[0]))
                else:
                    if s[0] and float(s[0]) != 0:
                        dict_SOH[str(s[1])[:4]].append(float(s[0]))
            for i in y_time_lists:
                for d in dict_SOH.keys():
                    if i == d:
                        # print (dict_SOH[d])
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def dongmu_PCS(self, name, ed, st, obj):
        '''东睦PCS获取因数，温度'''
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        list_ = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            if values_mong:
                for v in values_mong:
                    body = json.loads(v['datainfo'])['body']
                    for b in body:
                        if b['device'][:4] == 'PCS%s' % (f + 1):
                            value = float(b[cu_name])  #
                            if 0 < value <= 100:
                                d.append(value)
                            else:
                                d.append(0)
                list_.append({'name': 'PCS-%s' % (f + 1), 'max': float('%.1f' % max(d)), 'min': float('%.1f' % min(d))})
        obj['data'] = sorted(list_, key=lambda x: x.get('max'), reverse=True)
        return obj

    def data_day_dongmu_A_V(self, ed=None, obj=None, st=None, name=None, t=None, report_type=None):
        '''PCS东睦电流电压（4,5）'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                if report_type == 4:
                                    list_time.append(i[5:13])
                                elif report_type == 5:
                                    list_time.append(i[0:13])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                    'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
        dongmu_session.close()

    def dongmu_cu_day(self, name, ed, obj, st, lang=None):
        '''东睦簇日数据计算'''
        dataP1, data2 = [], {}  # 有功,
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        for i in values:
            time_1 = json.loads(i['datainfo'])['utime']
            timeall_.append(timeUtils.ssTtimes(time_1))
            value = json.loads(i['datainfo'])['body']
            for v in value:
                alldata.append(v)
        for e in range(dongmu_num):
            BMS_ = 'BMS%s' % (e + 1)
            d2 = []  # 有功
            for ii in alldata:
                if ii['device'] == BMS_:
                    value = ii[cu_name]  #
                    d2.append(value)
                    # 取相同的间隔时间(采用补数的方法)
            data12 = {}
            data12['time'] = timeall_
            data12['value'] = d2
            df = pd.DataFrame(data12)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data12["time"] = df["time"].tolist()
            data12["value"] = df["value"].tolist()
            if data12["time"]:
                data22 = complete_data(data12, '15T')
                list_time = []
                if data22:
                    for i in data22['time']:
                        list_time.append(i[8:16])
                data2['time'] = list_time
                # dataP1.append(data22['value'])
                obj['time'] = list_time

                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if lang == 'en':
                    obj['data'].append({'pcs_name': 'PCS-%s' % (e + 1),
                                        'cu': [{'cu_name': 'Battery cluster%s' % (e + 1), 'value': np.round(
                                            np.array(data22['value']).astype(float), 3).tolist()}]})
                else:
                    obj['data'].append(
                        {'pcs_name': 'PCS-%s' % (e + 1), 'cu': [{'cu_name': '电池簇%s' % (e + 1), 'value': np.round(
                            np.array(data22['value']).astype(float), 3).tolist()}]})
        dongmu_session.close()

    def cu_chag_disg(self, chag_disg, db, obj, lang=None):
        '''簇充放电量'''
        if chag_disg:
            dict_chag_disg = {}
            for s in chag_disg:
                if lang == 'en':
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                    else:
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                else:
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                    else:
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
            for d in dict_chag_disg.keys():
                cu = []
                for ss in dict_chag_disg[d].keys():
                    chag_ = float('%.0f' % sum(dict_chag_disg[d][ss]['chag']))
                    disg_ = float('%.0f' % sum(dict_chag_disg[d][ss]['disg']))
                    if chag_ == 0:
                        ratio_ = 0
                    else:
                        ratio_ = float('%.2f' % ((disg_ / chag_) * 100))
                        if ratio_ > 100:
                            ratio_ = 100
                    cu.append({'cu_name': ss, 'chag': chag_, 'disg': disg_, 'ratio': ratio_})
                list3 = natsorted(cu, key=lambda x: x['cu_name'], reverse=False)
                obj.append({'pcs_name': d, 'cu': list3})

    def cu_chag_disg_num(self, chag_disg, db, obj, obj_c_d, volume, lang=None):
        '''簇充放电次数'''
        if chag_disg:
            dict_chag_disg = {}
            for s in chag_disg:
                if lang == 'en':
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                    else:
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                else:
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                    else:
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
            for d in dict_chag_disg.keys():
                cu = []
                cu_ = []
                for ss in dict_chag_disg[d].keys():
                    chag_ = sum(dict_chag_disg[d][ss]['chag'])
                    disg_ = sum(dict_chag_disg[d][ss]['disg'])
                    chag_disg_num = math.floor((math.sqrt(float(chag_) * float(disg_))) / (
                                (volume[0] * 1000) / (len(dict_chag_disg) * len(dict_chag_disg[d]))))
                    cu_.append({'cu_name': ss, 'chag': chag_, 'disg': disg_})
                    cu.append({'cu_name': ss, 'chag_disg_num': chag_disg_num})
                list3 = sorted(cu, key=lambda x: x.get("chag_disg_num"), reverse=True)
                list4 = sorted(cu_, key=lambda x: x.get("chag"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list3})
                obj_c_d.append({'pcs_name': d, 'cu': list4})

    def cu_soh(self, SOH, db, min, obj, lang=None):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if lang == 'en':
                    if s[0] not in dict_SOH.keys():
                        dict_SOH[s[0]] = {}
                        if s[3] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[3]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                    else:
                        if s[3] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[3]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                        else:
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                else:
                    if s[0] not in dict_SOH.keys():
                        dict_SOH[s[0]] = {}
                        if s[1] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[1]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
                    else:
                        if s[1] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[1]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
                        else:
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
            for d in dict_SOH.keys():
                cu = []
                for ss in dict_SOH[d].keys():
                    cu.append({'cu_name': ss, 'min': min(dict_SOH[d][ss])})
                list3 = sorted(cu, key=lambda x: x.get("min"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list3})

    def cu_dongmu_baodian_t_v(self, max, obj, tt, lang=None):
        '''簇东睦保电温度电压（2345）'''
        if tt:
            dict_tt = {}
            for s in tt:
                if lang == 'en':
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[3] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[3]] = []
                            dict_tt[s[0]][s[3]].append(float(s[2]) if s[2] else float(0))
                    else:
                        if s[3] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[3]] = []
                            dict_tt[s[0]][s[3]].append(float(s[2]) if s[2] else float(0))
                        else:
                            dict_tt[s[0]][s[3]].append(float(s[2]) if s[2] else float(0))
                else:
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            dict_tt[s[0]][s[1]].append(float(s[2]) if s[2] else float(0))
                    else:
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            dict_tt[s[0]][s[1]].append(float(s[2]) if s[2] else float(0))
                        else:
                            dict_tt[s[0]][s[1]].append(float(s[2]) if s[2] else float(0))
            for d in dict_tt.keys():
                cu = []
                for ss in dict_tt[d].keys():
                    cu.append({'cu_name': ss, 'max': max(dict_tt[d][ss])})
                list2 = sorted(cu, key=lambda x: x.get("max"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list2})

def _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=None):
    list1 = []
    if db == 'dongmu':
        for i in range(7):
            redisdata = real_data('measure', 'dongmu', 'db')
            time_real = redisdata['utime']
            now_time = timeUtils.getNewTimeStr()
            now_time_ = timeUtils.timeStrToTamp(now_time)
            time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
            if time_ss > 125:
                list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': 0, 'chag': 0})
            else:
                for ii in redisdata['body']:
                    if ii['device'][:4] == ('PCS%s' % (i + 1)):
                        disg_ = ii[name_disg]  # PCS日放电
                        chag_ = ii[name_chag]  # PCS日充电
                        list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': ('%.1f' % float(disg_)),
                                      'chag': ('%.1f' % float(chag_))})

    else:
        for name in names:
            if db == 'baodian':
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == (name+'s.'),
                                                                          SdaPcsCu.station_name == db).first()
            else:
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                          SdaPcsCu.station_name == db).first()
            if not v_pcs_name:
                logging.error("未找到对应的pcs_name")
                continue
            PCS_name_f = v_pcs_name[0]
            o = real_data('measure', name + name_disg, 'db')
            disg_ = ('%.1f' % float(o['value'])) if o['value'] != '--' else '--' # PCS日放电
            o = real_data('measure', name + name_chag, 'db')
            chag_ = ('%.1f' % float(o['value'])) if o['value'] != '--' else '--'  # PCS日充电
            list1.append({'name': PCS_name_f, 'disg': disg_, 'chag': chag_})
    return list1

def extract_number(s):
    return int(''.join(filter(str.isdigit, s)))


