#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-04 08:59:49
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\dispatch_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-20 14:43:29
from sqlalchemy.dialects.mysql import MEDIUMTEXT

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Application.Models.User.user import User
from Application.Models.User.organization import Organization
from Application.Models.User.station import Station

class DispatchR(user_Base):
    u'具体工单工单模板'
    __tablename__ = "r_dispatch"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"工单名称")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    model_id = Column(Integer,ForeignKey("t_dispatch_model.id"), nullable=False,comment=u"模板id")
    start_time = Column(DateTime, nullable=True,comment=u"预计开始时间")
    plan_time = Column(DateTime, nullable=False,comment=u"预计结束时间")
    finish_time = Column(DateTime, nullable=True,comment=u"实际技术时间")
    create_user = Column(Integer, nullable=False, comment=u"创建者")
    status = Column(String(50), nullable=False, comment=u"工单状态，run使用off禁用，end执行完,其他存放当前步骤id")
    imgs = Column(String(256), nullable=True, comment=u"图片，多个用#号分割")
    content = Column(String(256), nullable=True, comment=u"希望的工作内容")
    other = Column(MEDIUMTEXT, nullable=True, comment=u"其他")
    copy_users = Column(String(256), nullable=True, comment=u"抄送人,[]")
    station = Column(String(256), nullable=False, comment=u"所属站")
    files = Column(String(256), nullable=True, comment=u"文件，多个用#号分割")
    working_no = Column(String(256), nullable=True, comment=u"工号")
    working_flag = Column(Integer, nullable=True,comment=u"工单模板类型，1工程服务申请单,2工作流程3含有标准工作票工作流程")
    update_time = Column(DateTime, nullable=True,comment=u"修改时间")

    en_descr = Column(String(256), nullable=False, comment=u"英文工单名称")
    en_content = Column(MEDIUMTEXT, nullable=True, comment=u"英文希望的工作内容")
    en_other = Column(MEDIUMTEXT, nullable=True, comment=u"英文其他")

    dispatch_model= relationship("DispatchModel", backref="dispatch_model")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self,lang=None):
        CU = user_session.query(User).filter(User.id==self.create_user,User.unregister==1).first()
        CUS = user_session.query(User).filter(User.id.in_(eval(str(self.copy_users))),User.unregister==1).all()
        sta = user_session.query(Station).filter(Station.name==self.station).first()
        if sta:
            station = sta.descr
            en_station = sta.en_descr
        else:
            station = ''
            en_station = ''
        create_descr = CU.name if CU else ''
        en_create_descr = CU.en_name if CU else ''
        model_descr = self.dispatch_model.descr if self.dispatch_model else ''
        # a1 = self.content.split() if self.content else []
        copy_descr = []
        en_copy_descr = []
        for u in CUS:
            en_copy_descr.append(u.en_name)
            copy_descr.append(u.name)

        if self.status == 'off':
            status = '已关闭'
            en_status = 'Target is closed'

        elif self.status == 'end':
            status = '已结束'
            en_status = 'Already ended'
        else:
            status = '进行中'
            en_status = 'Under way'
        bean = "{'id':%s,'descr':'%s','op_ts':'%s','model_id':%s,'model_descr':'%s','start_time':'%s','plan_time':'%s','finish_time':'%s','create_user':%s,\
            'create_descr':'%s','en_create_descr':'%s','imgs':'%s','files':'%s','content':'%s','copy_users':'%s','copy_descr':'%s','en_copy_descr':'%s','station':'%s','en_station':'%s','working_no':'%s',\
            'working_flag':'%s','status':'%s','en_status':'%s','update_time':'%s','en_descr':'%s','en_content':'%s','en_other':'%s'}" %\
               (self.id,self.descr,self.op_ts,self.model_id,model_descr,self.start_time,self.plan_time,self.finish_time,self.create_user,create_descr,en_create_descr,self.imgs,self.files,self.content,
                self.copy_users,','.join(copy_descr),','.join(en_copy_descr),station,en_station,self.working_no,self.working_flag,status,en_status,self.update_time,self.en_descr,self.en_content,self.en_other)
        
        return bean.replace("None",'')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}