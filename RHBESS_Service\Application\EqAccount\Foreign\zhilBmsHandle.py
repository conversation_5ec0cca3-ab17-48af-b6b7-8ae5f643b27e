#!/usr/bin/env python
# coding=utf-8
# @Information:智锂物联电芯数据
# <AUTHOR> WYJ
# @Date         : 2022-11-16 16:16:36
# @FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\Foreign\zhilHisHandle copy.py
# @Email        : <EMAIL>
# @LastEditTime : 2022-11-16 16:16:36


import json, os, time
import requests
import logging
from sqlalchemy import func
import pandas as pd
from Application.Models.base_handler import BaseHandler
import tornado.web
from tornado import gen
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import *
from Tools.DataEnDe.aes_cbc import AESUtil
from Tools.DataEnDe.MD5 import MD5Tool
from Application.Models.His.r_ACDMS import HisTL
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session, DEBUG
from Tools.DB.tl_his import tl_session
from Tools.DB.taicang_his import taicang_session
from Application.EqAccount.Foreign.zhilRealHandle import gz_no, zhili_openId, zhili_key, zhili_iv, openId, key, iv, \
    files_base, files_rk, cell_V, cell_T, cell_SOC, cell_SOH
from Application.Models.User.for_zhil_coupleback import ForCouplebackR
from Application.HistoryData.his_bams import _return_db_con, _select_get_des_value, _select_get_his_value, _select_get_des_value_new, _select_get_his_value_new, get_the_new_data
import gc
import ast
import datetime

# 贵州名字拼接
guizhou_station_name = {1: "guizhou1a", 2: "guizhou1a", 3: "guizhou1a", 4: "guizhou1b", 5: "guizhou1b", 6: "guizhou1b",
                        7: "guizhou1c", 8: "guizhou1c",
                        9: "guizhou2a", 10: "guizhou2a", 11: "guizhou2a", 12: "guizhou2b", 13: "guizhou2b",
                        14: "guizhou2b", 15: "guizhou2c", 16: "guizhou2c",
                        17: "guizhou3a", 18: "guizhou3a", 19: "guizhou3a", 20: "guizhou3b", 21: "guizhou3b",
                        22: "guizhou3b", 23: "guizhou3c", 24: "guizhou3c",
                        25: "guizhou4a", 26: "guizhou4a", 27: "guizhou4a", 28: "guizhou4b", 29: "guizhou4b",
                        30: "guizhou4b", 31: "guizhou4c", 32: "guizhou4c",
                        33: "guizhou5a", 34: "guizhou5a", 35: "guizhou5a", 36: "guizhou5b", 37: "guizhou5b",
                        38: "guizhou5b", 39: "guizhou5c", 40: "guizhou5c",
                        41: "guizhou6a", 42: "guizhou6a", 43: "guizhou6a", 44: "guizhou6b", 45: "guizhou6b",
                        46: "guizhou6b", 47: "guizhou6c", 48: "guizhou6c",
                        49: "guizhou7a", 50: "guizhou7a", 51: "guizhou7a", 52: "guizhou7b", 53: "guizhou7b",
                        54: "guizhou7b", 55: "guizhou7c", 56: "guizhou7c",
                        57: "guizhou8a", 58: "guizhou8a", 59: "guizhou8a", 60: "guizhou8b"
                        }

# 堆
pile_arr = {"tm":"opt","skVol":"vol","skCur":"cur","skSOC":"soc","skSOH":"soh","skSgVmaxRkNm":"max_vol_bc_rk","skSgVmaxPkNm":"max_vol_cell_rk","skSgVmax":"max_vol","skSgVminRkNm":"min_vol_bc_rk","skSgVminPkNm":"min_vol_cell_rk",
            "skSgVmin":"min_vol","skSgTmaxRkNm":"max_temp_bc_rk","skSgTmaxPkNm":"max_temp_cell_rk","skSgTmax":"max_temp","skSgTminRkNm":"min_temp_bc_rk","skSgTminPkNm":"min_temp_cell_rk","skSgTmin":"min_temp","skChagCapy":"chag_cap",
            "skDisgCapy":"disg_cap","skCmlChagCapy":"chag_cum","skCmlDisgCapy":"disg_cum"}
# 簇
# cu_arr = {"rkVol":"bc_vol","rkCur":"bc_cur","rkInsoPos":"bc_pos_ins_res","rkInsoNeg":"bc_neg_ins_res","rkSOC":"bc_soc","rkSOH":"bc_soh","rkSgVavg":"bc_avg_vol","rkSgVmaxNm":"bc_max_vol_rk","rkSgVmax":"bc_max_vol","rkSgVminNm":"bc_min_vol_rk","rkSgVmin":"bc_min_vol",
#                       "rkSgTavg":"bc_avg_temp","rkSgTmaxNm":"bc_max_temp_rk","rkSgTmax":"bc_max_temp","rkSgTminNm":"bc_min_temp_rk","rkSgTmin":"bc_min_temp","rkCmlChagCapy":"bc_chag","rkCmlDisgCapy":"bc_disg"}

cu_arr = {"rkVol":"vol","rkCur":"cur","rkInsoPos":"pos_ins_res","rkInsoNeg":"neg_ins_res","rkSOC":"soc","rkSOH":"soh","rkSgVavg":"avg_vol","rkSgVmaxNm":"max_vol_rk","rkSgVmax":"max_vol","rkSgVminNm":"min_vol_rk","rkSgVmin":"min_vol",
                      "rkSgTavg":"avg_temp","rkSgTmaxNm":"max_temp_rk","rkSgTmax":"max_temp","rkSgTminNm":"min_temp_rk","rkSgTmin":"min_temp","rkCmlChagCapy":"chag_cum","rkCmlDisgCapy":"disg_cum"}

dingding_url = "https://oapi.dingtalk.com/robot/send?access_token=7535e66f31ba81cf3769e49e3b3b9c44e6491f2b2b66732fa3a4dd5f421f7605"

db_name_arr = {"taicang": "太仓", "binhai": "滨海", "ygzhen": "永臻", "zgtian": "中天", "guizhou": "贵州", "datong": "大同", "ygqn": "阳泉", "shgyu": "上虞", "taicgxr": "太仓鑫融"}

class ZhiLBmsHandleIntetface(BaseHandler):
    # @tornado.web.authenticated
    @gen.coroutine
    def post(self, kt):
        # self.refreshSession()  # 刷新session
        try:
            if kt == 'OldRealDatas':  # 实时数据
                return_key = {}
                f, data = self._verifyToken()
                if not f:
                    return data
                # 获得需要查询的原始名
                all_names, datas = [], {}
                pcsSt = data['pcsSt']  # 对应电池堆G
                lp = data['lp']  # 对应电池堆H、
                bank = data['bank']  # 电池簇
                db = data['db']  # 对应电站
                # print ('***************************************************',pcsSt,lp,bank,db)
                if db == 'ygzhen':
                    db_con = _return_db_con(db, 'G%s' % (pcsSt + 1))  # 获取具体的数据库链接
                elif db == 'guizhou':
                    db_con = _return_db_con(db, guizhou_station_name[pcsSt])
                elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                    a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                    db_con = _return_db_con(db, b)
                else:
                    db_con = _return_db_con(db, 'G%s' % pcsSt)
                # print ('*******************',db_con)
                fields = data['field'].split(",")
                for fi in fields:  # 所有传输的变量名
                    ind = fields.index(fi)
                    if ind == 100:
                        time.sleep(0.2)
                    if fi != 'tm':  # 时间单独处理
                        if fi in files_base[db].keys():
                            na = self._name_join(db, pcsSt, lp, None, files_base[db][fi], 1)
                        elif fi in files_rk[db].keys():
                            na = self._name_join(db, pcsSt, lp, bank, files_rk[db][fi], 2)
                        elif fi in cell_V.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_V[fi], 3)
                        elif fi in cell_T.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_T[fi], 4)
                        elif fi in cell_SOC.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOC[fi], 5)
                        elif fi in cell_SOH.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOH[fi], 6)
                        all_names.append(na)
                        if na not in return_key:
                            return_key[na] = fi
                # print ('-------------------------all_names:',all_names)
                # t = timeUtils.getNowHouse()
                # table = 'r_measure%s' % t
                table = "ods_r_measure1"  # IDC表
                now_t = timeUtils.getNewTimeStr()
                datas['tm'] = now_t
                # for n1 in all_names:
                #     if 'SgV' in n1:  # 单体电压
                #         Vmax = 4
                #     elif 'SgT' in n1:  # 单体温度
                #         Vmax = 65
                #     elif 'Vol' in n1:  # 堆，簇电压
                #         Vmax = 1000
                #     elif 'Soc' in n1 or 'Soh' in n1:  # soc  或soh
                #         Vmax = 101
                #     elif 'Cur' in n1:  # 电流
                #         Vmax = 2500
                #     else:
                #         Vmax = ********
                #     v = _select_get_des_value(db_con, table, n1, Vmax, -9999)
                #     datas[return_key[n1]] = v
                Vmax = ********
                v = _select_get_des_value_new(db_con, table, all_names, Vmax, -9999)
                for item in v.keys():
                    datas[return_key[item]] = v[item]
                # exit()
                # datas[return_key[n1]] = v
                dat = AESUtil.encryt(str(datas), key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                db_con.close()  # 关闭数据库连接
                logging.info('real infault is success')
                del all_names
                del datas
                del data
                del fields
                del return_key
                gc.collect()  # 手动回收
                return self.returnTypeSuc(dat)

            elif kt == 'OldHisDatas':  # 历史数据
                return_key = {}
                f, data = self._verifyToken()
                if not f:
                    return data
                # data = {'bank': 3, 'db': 'taicang', 'endTime': '2024-12-02 00:05:00', 'field': 'tm,skVol,skCur,rkVol,rkCur,V1,V2,T1,T2,', 'lp': 2, 'pcsSt': 2, 'startTime': '2024-12-02 00:00:00'}
                all_names, datas = [], {'tm': []}
                pcsSt = data['pcsSt']  # 对应电池堆G
                lp = data['lp']  # 对应电池堆H、
                bank = data['bank']  # 电池簇
                db = data['db']  # 对应电站
                startTime = data['startTime']
                endTime = data['endTime']
                # now_time = timeUtils.getNewTimeStr()  # 后去当前时间
                timelen = timeUtils.timeSeconds(startTime, endTime)
                if timelen > 259200:  # 72小时
                    return self.customError('时间跨度请小于三天！')
                elif timelen < 0:
                    return self.customError('时间跨度不合理！')
                # if timeUtils.timeSeconds(endTime,now_time)>345600:  # 96小时:
                #     return self.customError('最多可获取四天前数据')
                if startTime <= '2023-02-01 00:00:00':
                    return self.customError('时间太早，无数据！')
                # a = timeUtils.getBetweenMonth(startTime, endTime)
                # tables = 'r_measure' + pd.Series(a)
                table = "ods_r_measure1"  # IDC表

                st = startTime  # 起始时间绝对秒
                ed = endTime  # 截止时间绝对秒
                # db_con = _return_db_con(db,'G%s'%pcsSt)  # 获取具体的数据库链接
                if db == 'ygzhen':
                    db_con = _return_db_con(db, 'G%s' % (pcsSt + 1))  # 获取具体的数据库链接
                elif db == 'guizhou':
                    db_con = _return_db_con(db, guizhou_station_name[pcsSt])
                elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                    a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                    db_con = _return_db_con(db, b)
                else:
                    db_con = _return_db_con(db, 'G%s' % pcsSt)
                fields = data['field'].split(",")
                for fi in fields:  # 所有传输的变量名
                    # ind = fields.index(fi)
                    # if ind == 100:
                    #     time.sleep(0.02)
                    if fi != 'tm':  # 时间单独处理
                        if fi in files_base[db].keys():
                            na = self._name_join(db, pcsSt, lp, None, files_base[db][fi], 1)
                        elif fi in files_rk[db].keys():
                            na = self._name_join(db, pcsSt, lp, bank, files_rk[db][fi], 2)
                        elif fi in cell_V.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_V[fi], 3)
                        elif fi in cell_T.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_T[fi], 4)
                        elif fi in cell_SOC.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOC[fi], 5)
                        elif fi in cell_SOH.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOH[fi], 6)
                        all_names.append(na)
                        if na not in return_key:
                            return_key[na] = fi

                # for table in tables.to_list():  # 需要查询的数据库
                for name in all_names:
                    if 'SgV' in name:  # 单体电压
                        Vmax = 4
                    elif 'SgT' in name:  # 单体温度
                        Vmax = 65
                    elif 'Vol' in name:  # 堆，簇电压
                        Vmax = 1000
                    elif 'Soc' in name or 'Soh' in name:  # soc  或soh
                        Vmax = 101
                    elif 'Cur' in name:  # 电流
                        Vmax = 2500
                    else:
                        Vmax = ********
                    v = _select_get_his_value(db_con, table, name, st, ed, startTime, endTime, '1T', Vmax)
                    datas[return_key[name]] = v['value']
                    if not datas['tm'] and v['time']:
                        datas['tm'] = v['time']
                db_con.close()
                print(datas)
                dat = AESUtil.encryt(str(datas), key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                logging.info('his infault is success')
                del all_names
                del datas
                del data
                del fields
                del return_key
                gc.collect()  # 手动回收

                return self.returnTypeSuc(dat)
            elif kt == 'RealCommerceDatas':  # 工商业实时数据
                head = self.request.headers
                Bean = head.get('Bean', None)
                logging.info('Bean:%s' % Bean)
                data = self.get_argument('data', None)
                logging.info('密文---data-------:%s' % data)
                md5 = MD5Tool.get_str_md5(data + zhili_openId)
                if not Bean or md5 != Bean:  # 身份验证失败
                    return self.tokenError()
                data = json.loads(AESUtil.decrypt(data, zhili_key, zhili_iv))
                tab = HisTL('device_notify_%s_record' % data.get('type_'))
                work_net = data.get('workNet')
                nowhour = timeUtils.getNewTimeStr()  # 当前时间
                beforehour = timeUtils.getAgoTime()  # 前7天时间
                fr = tl_session.query(tab.data_info).filter(tab.station_name == work_net, 
                                                            tab.time.between(beforehour, nowhour)).order_by(
                    tab.time.desc()).first()
                if not fr:
                    fr = tl_session.query(tab.data_info).filter(tab.station_name == work_net, 
                                                                tab.time <= beforehour).order_by(
                        tab.time.desc()).first()
                dat = AESUtil.encryt(fr[0], key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                return self.returnTypeSuc(dat)
            elif kt == 'HisCommerceDatas':  # 工商业历史数据
                head = self.request.headers
                Bean = head.get('Bean', None)
                logging.info('Bean:%s' % Bean)
                data = self.get_argument('data', None)
                logging.info('密文---data-------:%s' % data)
                md5 = MD5Tool.get_str_md5(data + zhili_openId)
                if not Bean or md5 != Bean:  # 身份验证失败
                    return self.tokenError()
                data = json.loads(AESUtil.decrypt(data, zhili_key, zhili_iv))
                # data = eval(data)
                tab = HisTL('device_notify_%s_record' % data.get('type_'))
                work_net = data.get('workNet')
                startTime = data['startTime']
                endTime = data['endTime']
                chargeTime = '2024-03-26 10:00:00'  # 工厂测试修改站名时间点
                all = []
                if work_net == 'CSGC001':  # 工厂的单独处理
                    if chargeTime > startTime and chargeTime < endTime:
                        fr = tl_session.query(tab.data_info).filter(tab.station_name == "SAMPLE1", 
                                                                    tab.time.between(startTime, chargeTime)).order_by(
                            tab.time.asc()).all()
                        for f in fr:
                            all.append(eval(f[0]))
                        fr1 = tl_session.query(tab.data_info).filter(tab.station_name == work_net, 
                                                                     tab.time.between(chargeTime, endTime)).order_by(
                            tab.time.asc()).all()
                        for f in fr1:
                            all.append(eval(f[0]))
                    elif startTime >= chargeTime:
                        fr1 = tl_session.query(tab.data_info).filter(tab.station_name == work_net, 
                                                                     tab.time.between(startTime, endTime)).order_by(
                            tab.time.asc()).all()
                        for f in fr1:
                            all.append(eval(f[0]))
                    else:
                        fr1 = tl_session.query(tab.data_info).filter(tab.station_name == "SAMPLE1", 
                                                                     tab.time.between(startTime, endTime)).order_by(
                            tab.time.asc()).all()
                        for f in fr1:
                            all.append(eval(f[0]))
                else:
                    fr = tl_session.query(tab.data_info).filter(tab.station_name == work_net, 
                                                                tab.time.between(startTime, endTime)).order_by(
                        tab.time.asc()).all()

                    for f in fr:
                        all.append(eval(f[0]))
                dat = AESUtil.encryt(str(all), key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                return self.returnTypeSuc(dat)

            elif kt == 'HisDatas':  # 历史数据
                return_key = {}
                f, data = self._verifyToken()
                if not f:
                    return data
                # data = {'bank': 7, 'db': 'shgyu', 'endTime': '2024-12-02 00:07:00',
                #         'field': 'tm,skVol,skCur,rkVol,rkCur,V1,V2,T1,T2,', 'lp': 2, 'pcsSt': 20,
                #         'startTime': '2024-12-02 00:00:00'}

                all_names, datas = [], {'tm': []}
                pcsSt = data['pcsSt']  # 对应电池堆G
                lp = data['lp']  # 对应电池堆H、
                bank = data['bank']  # 电池簇
                db = data['db']  # 对应电站
                if 'startTime' not in data or 'endTime' not in data:
                    return self.customError('请选择时间区间！')
                startTime = data['startTime']
                endTime = data['endTime']
                # now_time = timeUtils.getNewTimeStr()  # 后去当前时间
                timelen = timeUtils.timeSeconds(startTime, endTime)
                if timelen > 259200:  # 72小时
                    return self.customError('时间跨度请小于三天！')
                elif timelen < 0:
                    return self.customError('时间跨度不合理！')
                # if timeUtils.timeSeconds(endTime,now_time)>345600:  # 96小时:
                #     return self.customError('最多可获取四天前数据')
                if startTime <= '2023-02-01 00:00:00':
                    return self.customError('时间太早，无数据！')

                # 获取当前时间
                current_time = datetime.datetime.now()

                # 计算15分钟前的时间
                fifteen_minutes_ago = current_time - datetime.timedelta(minutes=15)
                end_time_datetime = datetime.datetime.strptime(endTime, '%Y-%m-%d %H:%M:%S')
                start_time_datetime = datetime.datetime.strptime(startTime, '%Y-%m-%d %H:%M:%S')
                if fifteen_minutes_ago <= end_time_datetime and start_time_datetime >= fifteen_minutes_ago:
                    datas['value'] = []
                    dat = AESUtil.encryt(str(datas), key, iv)
                    dat = str(dat, encoding='utf-8')
                    b = MD5Tool.get_str_md5(dat + openId)
                    self.set_header('Bean', b)
                    return self.returnTypeSuc(dat)
                if end_time_datetime >= fifteen_minutes_ago > start_time_datetime:
                    endTime = fifteen_minutes_ago.strftime('%Y-%m-%d %H:%M:%S')

                st = startTime  # 起始时间绝对秒
                ed = endTime  # 截止时间绝对秒
                fields = data['field'].split(",")
                # 组装需要查询的字段值
                dui_fields = []
                cu_fields = []
                cell_v = []
                cell_t = []
                for fi in fields:  # 所有传输的变量名
                    na = None
                    ind = fields.index(fi)
                    if ind == 100:
                        time.sleep(0.02)
                    if fi != 'tm':  # 时间单独处理
                        if fi in pile_arr.keys():
                            dui_fields.append(pile_arr[fi])
                        elif fi in cu_arr.keys():
                            cu_fields.append(cu_arr[fi])
                        elif fi in cell_V.keys():
                            if "cell_vol_list" not in cu_fields:
                                cu_fields.append("cell_vol_list")
                            cell_v.append(fi)
                        elif fi in cell_T.keys():
                            if "cell_temp_list" not in cu_fields:
                                cu_fields.append("cell_temp_list")
                            cell_t.append(fi)
                        elif fi in cell_SOC.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOC[fi], 5)
                        elif fi in cell_SOH.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOH[fi], 6)
                        else:
                            continue
                        if na is not None:
                            all_names.append(na)
                            if na not in return_key:
                                return_key[na] = fi
                        if len(all_names) > 0:
                            table = "ods_r_measure1"  # IDC表
                            if db == 'ygzhen':
                                db_con = _return_db_con(db, 'G%s' % (pcsSt + 1))  # 获取具体的数据库链接
                            elif db == 'guizhou':
                                db_con = _return_db_con(db, guizhou_station_name[pcsSt])
                            elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                                a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                                db_con = _return_db_con(db, b)
                            else:
                                db_con = _return_db_con(db, 'G%s' % pcsSt)
                            for name in all_names:
                                if 'SgV' in name:  # 单体电压
                                    Vmax = 4
                                elif 'SgT' in name:  # 单体温度
                                    Vmax = 65
                                elif 'Vol' in name:  # 堆，簇电压
                                    Vmax = 1000
                                elif 'Soc' in name or 'Soh' in name:  # soc  或soh
                                    Vmax = 101
                                elif 'Cur' in name:  # 电流
                                    Vmax = 2500
                                else:
                                    Vmax = ********
                                v = _select_get_his_value(db_con, table, name, st, ed, startTime, endTime, '1T', Vmax)
                                datas[return_key[name]] = v['value']

                dui_data = {}
                cu_data = {}
                if len(dui_fields)>0:

                    unit_rk = self._get_rk(db, pcsSt, lp, None)
                    select_fields = ','.join(dui_fields)
                    dui_data = _select_get_his_value_new(select_fields,db, 'dwd_unit_measure_5min', st, ed, startTime, endTime,
                                                              '1T','pile', pile_arr, unit_rk)
                if len(cu_fields)>0:
                    unit_cluster_rk = self._get_rk(db, pcsSt, lp, bank)
                    select_fields = ','.join(cu_fields)
                    cu_data = _select_get_his_value_new(select_fields, db, 'dwd_bc_measure_5min', st, ed,
                                                              startTime, endTime,'1T', 'cu', cu_arr, unit_cluster_rk, cell_v, cell_t)
                # 合并两个字典
                merged_dict = {}
                for d in (dui_data, cu_data):
                    for key_v, value in d.items():
                        if key_v in merged_dict:
                            datas[key_v].extend(value)
                        else:
                            datas[key_v] = value
                all_none, datas = self.process_dict(datas)
                if all_none:
                    # 发送钉钉消息
                    db_name = db_name_arr[db] if db in db_name_arr.keys() else db
                    # print("{}智锂获取历史数据test  WARNING!   \n储能单元：{}, 第{}堆{}簇  \n数据缺失,时间范围：{}-{}".format(db_name, pcsSt, lp, bank, startTime, endTime))
                    self.send_msg_dingding("{}智锂获取历史数据  WARNING!   \n储能单元：{}, 第{}堆{}簇  \n数据缺失,时间范围：{}-{}".format(db_name, pcsSt, lp, bank, startTime, endTime))
                    datas['tm'] = []

                dat = AESUtil.encryt(str(datas), key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                logging.info('his infault is success')
                del all_names
                del datas
                del data
                del fields
                del return_key
                gc.collect()  # 手动回收

                return self.returnTypeSuc(dat)

            if kt == 'RealDatas':  # 实时数据
                return_key = {}
                f, data = self._verifyToken()
                if not f:
                    return data
                # 获得需要查询的原始名
                all_names, datas = [], {}
                pcsSt = data['pcsSt']  # 对应电池堆G
                lp = data['lp']  # 对应电池堆H、
                bank = data['bank']  # 电池簇
                db = data['db']  # 对应电站

                # print ('*******************',db_con)
                fields = data['field'].split(",")
                dui_fields = []
                cu_fields = []
                cell_v = []
                cell_t = []
                for fi in fields:  # 所有传输的变量名
                    ind = fields.index(fi)
                    if ind == 100:
                        time.sleep(0.02)
                    if fi != 'tm':  # 时间单独处理
                        na = None
                        if fi in pile_arr.keys():
                            dui_fields.append(pile_arr[fi])
                        elif fi in cu_arr.keys():
                            cu_fields.append(cu_arr[fi])
                        elif fi in cell_V.keys():
                            if "cell_vol_list" not in cu_fields:
                                cu_fields.append("cell_vol_list")
                            cell_v.append(fi)
                        elif fi in cell_T.keys():
                            if "cell_temp_list" not in cu_fields:
                                cu_fields.append("cell_temp_list")
                            cell_t.append(fi)
                        elif fi in cell_SOC.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOC[fi], 5)
                        elif fi in cell_SOH.keys():
                            na = self._name_join(db, pcsSt, lp, bank, cell_SOH[fi], 6)
                        else:
                            continue
                        if na is not None:
                            all_names.append(na)
                            if na not in return_key:
                                return_key[na] = fi
                # 针对单体soc 和 soh 单独处理
                if all_names:
                    if db == 'ygzhen':
                        db_con = _return_db_con(db, 'G%s' % (pcsSt + 1))  # 获取具体的数据库链接
                    elif db == 'guizhou':
                        db_con = _return_db_con(db, guizhou_station_name[pcsSt])
                    elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                        a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                        db_con = _return_db_con(db, b)
                    else:
                        db_con = _return_db_con(db, 'G%s' % pcsSt)
                    table = "ods_r_measure1"  # IDC表
                    now_t = timeUtils.getNewTimeStr()
                    datas['tm'] = now_t

                    Vmax = ********
                    v = _select_get_des_value_new(db_con, table, all_names, Vmax, -9999)
                    for item in v.keys():
                        datas[return_key[item]] = v[item]

                dui_data = {}
                cu_data = {}
                if len(dui_fields) > 0:
                    unit_rk = self._get_rk(db, pcsSt, lp, None)
                    select_fields = ','.join(dui_fields)
                    dui_data = get_the_new_data(select_fields, 'dwd_unit_measure_5min', db, 'pile', unit_rk, pile_arr)
                if len(cu_fields) > 0:
                    unit_cluster_rk = self._get_rk(db, pcsSt, lp, bank)
                    select_fields = ','.join(cu_fields)
                    cu_data = get_the_new_data(select_fields,'dwd_bc_measure_5min', db, 'cu', unit_cluster_rk, cu_arr, cell_v, cell_t)
                if dui_data:
                    datas.update(dui_data)
                if cu_data:
                    datas.update(cu_data)
                now_t = timeUtils.getNewTimeStr()
                datas['tm'] = now_t
                # exit()
                # datas[return_key[n1]] = v
                dat = AESUtil.encryt(str(datas), key, iv)
                dat = str(dat, encoding='utf-8')
                b = MD5Tool.get_str_md5(dat + openId)
                self.set_header('Bean', b)
                logging.info('real infault is success')
                del all_names
                del datas
                del data
                del fields
                del return_key
                gc.collect()  # 手动回收
                return self.returnTypeSuc(dat)

            else:
                return self.pathError()
        except ValueError as E:
            return self.customError(str(E).encode('utf8'))
        except Exception as E:
            logging.info(E)
            return self.requestError()
        finally:
            tl_session.close()
            # torch.cuda.empty_cache()

    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean', None)
        logging.info('Bean:%s' % Bean)
        data = str(self.get_argument('data', None))
        logging.info('密文---data-------:%s' % data)
        md5 = MD5Tool.get_str_md5(data + zhili_openId)
        if not Bean or md5 != Bean:  # 身份验证失败
            return False, self.tokenError()

        data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
        keys = data.keys()
        if 'pcsSt' not in keys or 'lp' not in keys or 'bank' not in keys or 'db' not in keys:
            return False, self.customError('非法参数')
        elif data['pcsSt'] < 1:
            return False, self.customError('参数不合规,PCS')
        elif data['lp'] < 1:
            return False, self.customError('参数不合规,lp')
        elif data['bank'] < 1:
            return False, self.customError('参数不合规,bank')

        logging.info('明文*****data*************:%s' % data)
        return True, data

    def process_dict(self, data):
        all_none = True  # 初始化标识，假设所有列表都是 None
        for key in list(data.keys()):
            if key != 'tm':
                if all(x is None for x in data[key]):
                    data[key] = []
                else:
                    all_none = False  # 如果有列表不全是 None，设置标识为 False

        return all_none, data

    def _name_join(self, db, pcsSt, lp, bank, na, ty):
        '''电池名称拼接
        ty:1堆信息；2簇信息；3单体电压；4单体温度，5单体SOC，6单体SOH
        '''
        if bank:  # 电池簇的名称
            if db == 'binhai' or db == 'taicang':
                return 'G%s.H%s.R%s%s' % (pcsSt, lp, bank, na)
            elif db == 'ygzhen':
                dui = divmod(bank, 2)
                a = self._ygzhen_join(pcsSt, lp)
                return '%s.S%s.R%s%s' % (a, sum(dui), bank, na)
            elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                return '%s%s' % (a, na)
            elif db == 'guizhou':
                if ty == 2:
                    a = self._guizhou_join(pcsSt, lp)
                    return '%s%s%s' % (a, na, bank)
                elif ty == 3 or ty == 4:
                    a = self._guizhou_join(pcsSt, lp)
                    return '%s%s' % (a, na.replace(".", ".R%s" % bank))

        else:  # 电池堆
            if db == 'binhai' or db == 'taicang':
                return 'G%s.H%s%s' % (pcsSt, lp, na)
            elif db == 'ygzhen':
                a = self._ygzhen_join(pcsSt, lp)
                return '%s%s' % (a, na)
            elif db == 'zgtian' or db == 'datong' or db == 'ygqn':
                a, b = self._zg_dt_yq_join(pcsSt, lp, bank, db)
                # a,b =  '%s%s'%(self._zgtian_join(pcsSt,lp,bank),na)
                return '%s%s' % (a, na)
            elif db == 'guizhou':
                a = self._guizhou_join(pcsSt, lp)
                return '%s%s' % (a, na)
            # elif db == 'ygzhen':
            #     dui = divmod(bank,2)
            #     return 'G%s.H%s.S%s.R%s.%s'%(pcsSt,lp,sum(dui),bank,na)

    def _get_rk(self, db, pcsSt, lp, bank):
        """
        获取堆或者簇的编号
        """
        # 每个单元的堆数量
        # pile_num = {"binhai": 2, "taicang": 2, "ygzhen": 4, "zgtian": 4, "guizhou": 2, "datong": 4, "ygqn": 10, "shgyu": 2, "taicgxr": [2, 14]}
        pile_num = {"binhai": 2, "taicang": 2, "ygzhen": 4, "zgtian": 4, "guizhou": 2, "datong": 4, "ygqn": 2, "shgyu": 2, "taicgxr": 2}
        # 每个堆的簇数量
        cu_name = {"binhai": 7, "taicang": 6, "ygzhen": 8, "zgtian": 18, "guizhou": 9, "datong": 7, "ygqn": [7, 10], "shgyu": 7, "taicgxr": [7, 1]}
        if db == "taicgxr":
            if bank:
                if pcsSt <= 20:
                    return (int(pcsSt) - 1) * pile_num[db] * cu_name[db][0] + (int(lp) - 1) * cu_name[db][0] + int(bank)
                return 20 *  pile_num[db] * cu_name[db][0] + (int(pcsSt) - 1 - 20) * pile_num[db] * cu_name[db][1] + (int(lp) - 1) * cu_name[db][1] + int(bank)
            else:
                if pcsSt <= 20:
                    return (int(pcsSt) - 1) * pile_num[db] + int(lp)
                return (int(pcsSt) - 1 - 20) * pile_num[db] + int(lp) + (20*pile_num[db])
        elif db == 'ygqn':
            if bank:
                if pcsSt <= 40:
                    return (int(pcsSt) - 1) * pile_num[db] * cu_name[db][0] + (int(lp) - 1) * cu_name[db][0] + int(bank)
                return 40 * pile_num[db] * cu_name[db][0] +(int(pcsSt) - 1 - 40) * pile_num[db] * cu_name[db][1] + (int(lp) - 1) * cu_name[db][1] + int(bank)
            else:
                return (int(pcsSt) - 1) * pile_num[db] + int(lp)
        else:
            if bank:  # 电池簇的名称
                return (int(pcsSt) - 1) * pile_num[db] * cu_name[db] + (int(lp) - 1) * cu_name[db] + int(bank)
            else:  # 电池堆
                return (int(pcsSt) - 1) * pile_num[db] + int(lp)
    def _ygzhen_join(self, pcsSt, lp):
        '''永臻拼接'''
        nam = 'G1.H1'
        if pcsSt == 1:
            if lp == 1:
                nam = 'G1.H1'
            elif lp == 2:
                nam = 'G1.H2'
            elif lp == 3:
                nam = 'G2.H1'
            elif lp == 4:
                nam = 'G2.H2'
        elif pcsSt == 2:
            if lp == 1:
                nam = 'G3.H1'
            elif lp == 2:
                nam = 'G3.H2'
            elif lp == 3:
                nam = 'G4.H1'
            elif lp == 4:
                nam = 'G4.H2'
        return nam

    def _zg_dt_yq_join(self, pcsSt, lp, bank, db):
        '''中天的'''
        dui = ''
        nam = ''
        if bank:  # 拼接电池簇
            dui = divmod(bank, 3)
            if dui[1] == 2:
                dui = '.S%s.R%s' % (dui[0] + dui[1] - 1, bank)
            else:
                dui = '.S%s.R%s' % (sum(dui), bank)

        if db == 'zgtian':
            if pcsSt == 1:
                if lp < 3:
                    nam = 'G1.H%s%s' % (lp, dui)
                else:
                    nam = 'G2.H%s%s' % (lp - 2, dui)
            elif pcsSt == 2:
                if lp < 3:
                    nam = 'G3.H%s%s' % (lp, dui)
                else:
                    nam = 'G4.H%s%s' % (lp - 2, dui)
            elif pcsSt == 3:
                if lp < 3:
                    nam = 'G5.H%s%s' % (lp, dui)
                else:
                    nam = 'G6.H%s%s' % (lp - 2, dui)
            elif pcsSt == 4:
                if lp < 3:
                    nam = 'G7.H%s%s' % (lp, dui)
                else:
                    nam = 'G8.H%s%s' % (lp - 2, dui)
        elif db == 'datong':
            if pcsSt == 1:
                if lp in [1, 2]:
                    nam = 'G1.H%s%s' % (lp, dui)
                elif lp in [3, 4]:
                    nam = 'G2.H%s%s' % (lp, dui)
            elif pcsSt == 2:
                if lp in [1, 2]:
                    nam = 'G3.H%s%s' % (lp + 4, dui)
                elif lp in [3, 4]:
                    nam = 'G4.H%s%s' % (lp + 4, dui)
            elif pcsSt == 3:
                if lp in [1, 2]:
                    nam = 'G5.H%s%s' % (lp + 8, dui)
                elif lp in [3, 4]:
                    nam = 'G6.H%s%s' % (lp + 8, dui)
            elif pcsSt == 4:
                if lp in [1, 2]:
                    nam = 'G7.H%s%s' % (lp + 12, dui)
                elif lp in [3, 4]:
                    nam = 'G8.H%s%s' % (lp + 12, dui)
        elif db == 'ygqn':
            if pcsSt in [1, 2, 3, 4]:
                nam = 'G%s.H%s%s' % (pcsSt, (lp + 10 * (pcsSt - 1)), dui)
            elif pcsSt in [5, 6, 7, 8]:
                nam = 'G%s.H%s%s' % (pcsSt, (lp + 10 * ((pcsSt - 4) - 1)), dui)
            elif pcsSt in [9, 10, 11]:
                nam = 'G%s.H%s%s' % (pcsSt, (lp + 10 * ((pcsSt - 8) - 1)), dui)
        return nam, nam[:2]

    def _guizhou_join(self, pcsSt, lp):
        '''贵州名字拼接'''
        return '%s.Energy%s.BMS%s' % (guizhou_station_name[pcsSt], pcsSt, lp)

    def send_msg_dingding(self, content):
        headers = {'Content-Type': 'application/json;charset=utf-8'}
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            },
            "at": {
                "isAtAll": False
            }
        }
        r = requests.post(dingding_url, headers=headers, data=json.dumps(data))
        print('dingding告警信息-------------:', r.json())






