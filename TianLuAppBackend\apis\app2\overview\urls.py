#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-08-06 10:10:02
#@FilePath     : \emot_pjt_rh\TianLuAppBackend\apis\app2\overview\urls.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-12-24 15:42:16


from django.urls import path
from apis.app2.overview import views

urlpatterns = [
    path('station/list/', views.StationListView.as_view()),  # 项目汇总
    path('income/view/', views.IncomeView.as_view()),  # 收益视图
    path('income/list/', views.IncomeView.as_view()),  # 收益列表
    path("map/health/", views.MapHealthView.as_view()),  # web地图健康度
    # path("chgdig_day/", views.FreezeDataDay.as_view()),  # 逐天功率冻结数据
    path("income_day/", views.BaseIncomeDay.as_view()),  # 逐天收益冻结数据
    path("get_base_incomes", views.Test.as_view())
]
