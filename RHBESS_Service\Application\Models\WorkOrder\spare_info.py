#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-03-14 17:02:25
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\spare_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-14 16:32:32

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.station import Station

class SpareInfo(user_Base):
    u'备件基本表'
    __tablename__ = "t_spare_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False, comment=u"备件名称")
    en_name = Column(String(256), nullable=False, comment=u"备件名称-英文")
    manufacturer = Column(String(256), nullable=True, comment=u"厂家")
    en_manufacturer = Column(String(256), nullable=True, comment=u"厂家-英文")
    spec = Column(String(256), nullable=True, comment=u"规格型号")
    en_spec = Column(String(256), nullable=True, comment=u"规格型号-英文")
    device = Column(String(256), nullable=True, comment=u"主设备")
    en_device = Column(String(256), nullable=True, comment=u"主设备-英文")
    num = Column(Integer, nullable=True,comment=u"数量")
    unit = Column(String(10), nullable=True,comment=u"单位")
    en_unit = Column(String(128), nullable=True,comment=u"单位-英文")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    create_descr = Column(String(256), nullable=False,comment=u"创建者")
    en_create_descr = Column(String(256), nullable=False,comment=u"创建者-英文")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")
    remarks = Column(Text, nullable=True, comment=u"备注")
    en_remarks = Column(Text, nullable=True, comment=u"备注-英文")
    station = Column(String(256), nullable=True, comment=u"所属站")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        sta = user_session.query(Station).filter(Station.name==self.station).first()
        if sta:
            station = sta.descr
            en_station = sta.en_descr
        else:
            station = ''
            en_station = ''
        bean = "{'id':%s,'name':'%s','op_ts':'%s','create_descr':'%s','acc':'%s','type':'%s','mainStation':'%s','RealNum':%s,'unit':'%s','station':'%s','remarks':'%s'," \
               "'en_name':'%s','en_manufacturer':'%s','en_device':'%s','en_unit':'%s','en_create_descr':'%s','en_remarks':'%s','en_station':'%s', 'en_spec':'%s'}" % (
            self.id,self.name,self.op_ts,self.create_descr,self.manufacturer,self.spec,self.device,self.num,self.unit,station,self.remarks,
        self.en_name,self.en_manufacturer,self.en_device,self.en_unit,self.en_create_descr,self.en_remarks,en_station, self.en_spec)
        return bean.replace("None",'')

    def to_dict(self):
        sta = user_session.query(Station).filter(Station.name == self.station).first()
        if sta:
            station = sta.descr
            en_station = sta.en_descr
        else:
            station = ''
            en_station = ''
        return {
            'id': self.id,
            'name': self.name,
            'op_ts': self.op_ts.strftime('%Y-%m-%d %H:%M:%S') if self.op_ts else None,
            'create_descr': self.create_descr,
            'acc': self.manufacturer,
            'type': self.spec,
            'mainStation': self.device,
            'RealNum': self.num,
            'unit': self.unit,
            'station': station,
            'remarks': self.remarks,
            'en_name': self.en_name,
            'en_manufacturer': self.en_manufacturer,
            'en_device': self.en_device,
            'en_unit': self.en_unit,
            'en_create_descr': self.en_create_descr,
            'en_remarks': self.en_remarks,
            'en_station': en_station,
            'en_spec': self.en_spec
        }
        
