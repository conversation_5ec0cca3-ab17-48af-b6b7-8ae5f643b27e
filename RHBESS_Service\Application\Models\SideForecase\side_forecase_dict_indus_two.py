#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-30 10:03:57
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_dict_indus_two.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 11:30:49


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseDictIndesTwo(user_Base):
    '所属行业（二级级菜单）'
    __tablename__ = "t_side_forecase_dict_indes_two"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"所属行业")
    parent_id = Column(Integer, ForeignKey("t_side_forecase_dict_indes_one.id"),nullable=False, comment=u"一级")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=True,comment=u"排序索引")

    dict_indes_two_one = relationship("ForecaseDictIndesOne", backref="dict_indes_two_one")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        return "{'id':%s,'name':'%s'}" % (self.id,self.name)
        
    