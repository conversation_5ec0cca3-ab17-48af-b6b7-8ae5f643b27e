package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.stationr.StationRCreateDTO;
import com.robestec.analysis.dto.stationr.StationRQueryDTO;
import com.robestec.analysis.dto.stationr.StationRUpdateDTO;
import com.robestec.analysis.entity.StationR;
import com.robestec.analysis.vo.StationRVO;

import java.util.List;

/**
 * 电站关系服务接口
 */
public interface StationRService extends ISuperService<StationR> {

    /**
     * 分页查询电站关系
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<StationRVO> queryStationR(StationRQueryDTO queryDTO);

    /**
     * 创建电站关系
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createStationR(StationRCreateDTO createDTO);

    /**
     * 更新电站关系
     * @param updateDTO 更新参数
     */
    void updateStationR(StationRUpdateDTO updateDTO);

    /**
     * 删除电站关系
     * @param id 记录ID
     */
    void deleteStationR(Long id);

    /**
     * 获取电站关系详情
     * @param id 记录ID
     * @return 记录详情
     */
    StationRVO getStationR(Long id);

    /**
     * 批量创建电站关系
     * @param createDTOList 创建参数列表
     */
    void createStationRList(List<StationRCreateDTO> createDTOList);

    /**
     * 根据电站名称查询电站关系
     * @param stationName 电站名称
     * @return 记录列表
     */
    List<StationRVO> getStationRByStationName(String stationName);

    /**
     * 根据省份查询电站关系
     * @param province 省份
     * @return 记录列表
     */
    List<StationRVO> getStationRByProvince(String province);

    /**
     * 根据运行状态查询电站关系
     * @param runningState 运行状态
     * @return 记录列表
     */
    List<StationRVO> getStationRByRunningState(String runningState);

    /**
     * 统计省份的电站关系数量
     * @param province 省份
     * @return 记录数量
     */
    Long countByProvince(String province);
}
