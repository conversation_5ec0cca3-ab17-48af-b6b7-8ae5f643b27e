package com.robestec.dailyproduce.mqtt;

import com.robestec.dailyproduce.telecontrol.service.TelecontrolStrategyService;
import com.robestec.dailyproduce.telecontrol.vo.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class TelecontrolStrategyServiceTest {

    @Autowired
    private TelecontrolStrategyService telecontrolStrategyService;

    @Test
    public void testGetPowerPlanStations() {
        List<StationCapacityVO> result = telecontrolStrategyService.getPowerPlanStations();
        System.out.println("获取电站容量信息结果: " + result);
    }

    @Test
    public void testRefreshPowerPlanStations() {
        List<Long> stationIds = List.of(1L, 2L, 3L);
        List<StationCapacityVO> result = telecontrolStrategyService.refreshPowerPlanStations(stationIds);
        System.out.println("刷新电站容量信息结果: " + result);
    }

    @Test
    public void testAddPowerPlan() {
        PowerPlanRequest request = new PowerPlanRequest();
        // 设置请求参数
        CommonResultVO<String> result = telecontrolStrategyService.addPowerPlan(request);
        System.out.println("新增功率计划结果: " + result);
    }

    @Test
    public void testGetStrategyImport() {
        StrategyImportVO result = telecontrolStrategyService.getStrategyImport();
        System.out.println("获取策略导入结果: " + result);
    }

    @Test
    public void testGetIssuanceType() {
        IssuanceTypeVO result = telecontrolStrategyService.getIssuanceType();
        System.out.println("获取下发类型结果: " + result);
    }

    @Test
    public void testAddProjectPack() {
        StrategyRequest.ProjectPackAddRequest request = new StrategyRequest.ProjectPackAddRequest();
        // 设置请求参数
        CommonResultVO<String> result = telecontrolStrategyService.addProjectPack(request);
        System.out.println("新增项目包结果: " + result);
    }

    @Test
    public void testGetProjectPackList() {
        Long userId = 1L;
        List<ProjectPackVO> result = telecontrolStrategyService.getProjectPackList(userId);
        System.out.println("获取项目包列表结果: " + result);
    }

    @Test
    public void testGetProjectStations() {
        Long userId = 1L;
        List<ProjectStationVO> stations = telecontrolStrategyService.getProjectStations(userId);
        System.out.println("获取项目并网点列表结果: " + stations);
    }
} 