import concurrent.futures
import datetime
import logging
import math
import traceback

import pymysql
from dbutils.persistent_db import PersistentDB

from TianLuAppBackend import settings
from apis.user import models
from apis.web.models import RunningReport
from common.database_pools import ads_db_tool
from tools.day_hours_used import select_target_day_latest_data_from_dwd_rhyc, \
    select_target_day_latest_data_from_ads_rhyc
from common.constant import EMPTY_STR_LIST
from django.db import close_old_connections
# 连接数据库doris_dwd_rhyc
pool_ = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")

# 连接数据库default
pool_1 = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['default']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['default']['USER'],  # 数据库用户名
            "password": settings.DATABASES['default']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['default']['NAME'],  # 数据库名称
            "port": settings.DATABASES['default']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


def get_month_start_end(year, month):
    """
    获取一个月的第一天和最后一天
    """""
    first_day = datetime.datetime(year, month, 1)
    if month == 12:
        next_month_start = datetime.datetime(year + 1, 1, 1)
    else:
        next_month_start = datetime.datetime(year, month + 1, 1)
    last_day = next_month_start - datetime.timedelta(days=1)
    return first_day, last_day


def get_week_start_end(year_, week_):
    start = datetime.datetime.strptime('%s-W%s-1' % (year_, week_), "%Y-W%W-%w")
    end = start + datetime.timedelta(days=6)
    return start, end


def get_history_data_f(table_name, subzone, de, station_name, start_time, end_time):
    """查询小时历史数据"""""

    conn = pool_.connection()
    cursor = conn.cursor()

    try:
        # 执行SQL查询
        sql = """SELECT {},time
                    FROM {} {}
                    WHERE 1=1
                    and device='{}'
                    and station_name='{}'
                    and time BETWEEN '{}' AND '{}'
                    ORDER BY time ASC
                    """.format(
            'soc', table_name, subzone, de, station_name, start_time, end_time
        )
        # logging.error(sql)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            # return []

        # 获取查询结果
        result = cursor.fetchall()
        result_soc = []
        result_soc_3 = []

        # 处理查询结果
        if result:
            for r in result:
                result_soc_time_3 = r['time']
                result_soc_3_ = round(float(r['soc']), 2) if r['soc'] else 0
                result_soc.append(result_soc_3_)
                result_soc_3.append({'time': result_soc_time_3, 'value': result_soc_3_})

        return result_soc_3, result_soc
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()


def get_station_soc(station, target_day):
    month_ = str(target_day.month) if len(str(target_day.month)) > 1 else '0' + str(target_day.month)
    day_ = str(target_day.day) if len(str(target_day.day)) > 1 else '0' + str(target_day.day)
    subzone = 'PARTITION ' + 'p' + str(target_day.year) + month_ + day_  # 分区组合

    start_time = datetime.datetime.combine(target_day, datetime.datetime.min.time()).strftime('%Y-%m-%d %H:%M:%S')
    end_time = datetime.datetime.combine(target_day, datetime.datetime.max.time()).strftime('%Y-%m-%d %H:%M:%S')

    last_dict = {}
    last_dict['english_name'] = station.english_name
    devices = models.Unit.objects.values("bms", "pcs").filter(station_id=station.id).all()  # 取单元的bms pcs
    device_2 = []
    if devices:
        for d in devices:
            if station.meter_type == 1 or station.meter_type == 2:
                device_2.append(d['bms'])
            elif station.meter_type == 3:
                device_2.append(d['pcs'])
    result_soc_tall = []
    result_soc_tall_value = []
    if device_2 != []:
        for de in device_2:
            if de[:3] == 'BMS':
                try:
                    result_soc_3, result_soc = get_history_data_f('dwd_measure_bms_data_storage_3', subzone,
                                                                       de, station.english_name, start_time, end_time)
                    result_soc_tall.extend(result_soc_3)
                    result_soc_tall_value.extend(result_soc)
                except:
                    continue

        result_soc_tall_value_set = sorted(result_soc_tall_value)
        result_soc_tall_set = sorted(result_soc_tall, key=lambda x: x['time'])
        if result_soc_tall_value_set:
            last_dict['initial_soc'] = result_soc_tall_set[0]['value']  # 逐日SOC初始值
            last_dict['final_soc'] = result_soc_tall_set[-1]['value']  # 逐日SoC终值
            last_dict['maximum_soc'] = max(result_soc_tall_value_set)  # 逐日SOC最大值
            last_dict['minimum_soc'] = min(result_soc_tall_value_set)  # 逐日SOC最小值
        else:
            last_dict['initial_soc'] = 0  # 逐日SOC初始值
            last_dict['final_soc'] = 0  # 逐日SoC终值
            last_dict['maximum_soc'] = 0  # 逐日SOC最大值
            last_dict['minimum_soc'] = 0  # 逐日SOC最小值

    return last_dict


def get_station_theory_charge_discharge(station, target_day):
    """
    获取一个并网点的某一天的收益及基准充放电量: 切换 ads_report_station_income_1d
    """""
    # station_bases = models.StationBaseIncome.objects.filter(station_name=station.english_name,
    #                                                        day=target_day.strftime('%Y-%m-%d'))
    # if station_bases.exists():
    #     station_base = station_bases.first()
    #     return station_base.day_chag, station_base.day_disg
    # else:
    #     return 0, 0

    select_sql = """
            select * from ads_report_station_income_1d where station = %s and day = %s
            """
    res = ads_db_tool.select_one(select_sql, station.english_name, target_day)

    if res:
        income = float(res['day_income']) if res['day_income'] is not None else '--'
        base_income = float(res['base_income']) if res['base_income'] is not None else '--'
        return income, base_income, float(res['base_chag']) if res['base_chag'] is not None else '--', float(res['base_disg']) if res['base_disg'] is not None else '--'
    return '--', '--', '--', '--'


def get_station_income_all(station, date_value):
    """获取并网点年/月/累计收益和基准收益"""
    if isinstance(date_value, datetime.datetime):
        start_year = date_value.replace(month=1, day=1).date()  # 当前年
        start_month = date_value.replace(day=1).date()  # 当前月
    else:
        start_year = date_value.replace(month=1, day=1)
        start_month = date_value.replace(day=1)
    select_sql = """
            select day_income, base_income, day  from ads_report_station_income_1d where station = %s  and day <= %s  
            """
    res = ads_db_tool.select_many(select_sql, station.english_name, date_value)
    income = [0, 0, 0]  # 累计、年、月 收益
    base_income = [0, 0, 0]  # 累计、年、月 基准收益
    if res:
        for i in res:
            income[0] += i['day_income'] if i['day_income'] else 0
            base_income[0] += i['base_income'] if i['base_income'] else 0
            if i['day'] >= start_year:
                income[1] += i['day_income'] if i['day_income'] else 0
                base_income[1] += i['base_income'] if i['base_income'] else 0
            if i['day'] >= start_month:
                income[2] += i['day_income'] if i['day_income'] else 0
                base_income[2] += i['base_income'] if i['base_income'] else 0
    return income, base_income

def get_station_income_day(station, date_value):
    """获取并网点年/月/累计收益和基准收益"""

    select_sql = """
            select day_income, base_income, day  from ads_report_station_income_1d where station = %s  and day = %s  
            """
    res = ads_db_tool.select_many(select_sql, station.english_name, date_value)
    income = 0  # 0收益
    base_income = 0  # 0基准收益
    if res:
        for i in res:
            income += i['day_income'] if i['day_income'] else 0
            base_income += i['base_income'] if i['base_income'] else 0
    return income, base_income

def get_station_accu_charge_discharge(station, target_day):
    """
    获取一个并网点的某一天的累计充放电量~~~~~弃用~~~复用
    """""

    # target_day_ = datetime.datetime.combine(target_day, datetime.time())

    units = station.unit_set.filter(is_delete=0).all()
    accu_charge = 0
    accu_discharge = 0

    # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
    meter_use_time = models.StationMeterUseTime.objects.filter(station=station, is_use=1).first()
    if meter_use_time:
        if meter_use_time.end_time:
            is_use_account = (station.is_account and
                              meter_use_time.start_time.date() <= target_day.date() <= meter_use_time.end_time.date())
        else:
            if isinstance(target_day, datetime.datetime):
                is_use_account = (station.is_account and meter_use_time.start_time <= target_day)
            else:
                is_use_account = (station.is_account and meter_use_time.start_time.date() <= target_day)
    else:
        is_use_account = False

    # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
    if is_use_account:
        res = select_target_day_latest_data_from_ads_rhyc(station.english_name, 'EMS',
                                                          target_day, 'TPAP', 'TAPIR')
        if res:
            accu_charge = abs(float(res.get('TAPIR'))) if res.get('TAPIR') else 0
            accu_discharge = abs(float(res.get('TPAP'))) if res.get('TPAP') else 0

        print(279, accu_charge, accu_discharge, station.station_name, target_day)

    else:
        for unit in units:
            bms = unit.bms
            res = select_target_day_latest_data_from_dwd_rhyc(station.english_name, 'cumulant', 'bms', bms,
                                                    target_day, 'NAE', 'PAE', 'CuCha', 'CuDis')
            if res:
                if station.english_name == 'NBLS001':  # 单独处理
                    temp_charge = abs(float(res.get('CuCha'))) if res.get('CuCha') else 0
                    temp_discharge = abs(float(res.get('CuDis'))) if res.get('CuDis') else 0
                else:
                    temp_charge = abs(float(res.get('NAE'))) if res.get('NAE') else 0
                    temp_discharge = abs(float(res.get('PAE'))) if res.get('PAE') else 0
                accu_charge += temp_charge
                accu_discharge += temp_discharge

    return accu_charge, accu_discharge


def get_station_accu_charge_discharge_soc(station, target_day):
    """
    获取一个并网点的某一天的累计充放电量及对应soc差值总和
    """""
    conn = pool_1.connection()
    cursor = conn.cursor()

    # 执行SQL查询
    sql_1 = """select sum(abs(soc)) as soc_sum from f_report where station='{}' and station_type=1 and day<='{}' and soc_2>'0';
                """.format(station.english_name, target_day.strftime('%Y-%m-%d'))
    sql_2 = """select sum(abs(soc)) as soc_sum from f_report where station='{}' and station_type=1 and day<='{}' and soc_2<'0';
                """.format(station.english_name, target_day.strftime('%Y-%m-%d'))
    sql_3 = """select sum(chag) as chag, sum(disg) as disg from f_report where station='{}' and station_type=1 and day<='{}';
                """.format(station.english_name, target_day.strftime('%Y-%m-%d'))
    # logging.debug(sql_1)
    # logging.debug(sql_2)
    # logging.debug(sql_3)
    try:
        cursor.execute(sql_1)
        result_1 = cursor.fetchone()
        cursor.execute(sql_2)
        result_2 = cursor.fetchone()
        cursor.execute(sql_3)
        result_3 = cursor.fetchone()
        # 获取查询结果
        charge_soc_diff_sum = result_1['soc_sum'] if result_1['soc_sum'] else 0
        discharge_soc_diff_sum = result_2['soc_sum'] if result_2['soc_sum'] else 0
        charge = result_3['chag'] if result_3['chag'] else 0
        discharge = result_3['disg'] if result_3['disg'] else 0
        return charge_soc_diff_sum, discharge_soc_diff_sum, charge, discharge
    except Exception as e:
        logging.error(e)
        return 0, 0, 0, 0
    finally:
        cursor.close()
        conn.close()


def get_station_accu_charge_discharge_soc_from_ads_report_chag_disg_union_data(station, target_day):
    """
    获取一个并网点的某一天的累计充放电量及对应soc差值总和
    """""
    select_sql_1 = """select sum(chag_soc) as chag_soc, sum(disg_soc) as disg_soc, sum(v_chag) as chag, sum(v_disg) as disg from ads_report_chag_disg_union_1d where station=%s and station_type<=1 and day<=%s;"""
    # select_sql_2 = """select sum(abs(soc)) as chag_soc from ads_report_chag_disg_union_1h where station=%s and station_type<=1 and soc>0 and day<=%s;"""
    # select_sql_3 = """select sum(abs(soc)) as disg_soc from ads_report_chag_disg_union_1h where station=%s and station_type<=1 and soc<0 and day<=%s;"""
    try:
        result_1 = ads_db_tool.select_one(select_sql_1, station.english_name, target_day.strftime('%Y-%m-%d'))
        # result_2 = ads_db_tool.select_one(select_sql_2, station.english_name, target_day.strftime('%Y-%m-%d'))
        # result_3 = ads_db_tool.select_one(select_sql_3, station.english_name, target_day.strftime('%Y-%m-%d'))
        # 获取查询结果
        charge_soc_diff_sum = result_1['chag_soc'] if result_1['chag_soc'] else 0
        discharge_soc_diff_sum = result_1['disg_soc'] if result_1['disg_soc'] else 0
        charge = result_1['chag'] if result_1['chag'] else 0
        discharge = result_1['disg'] if result_1['disg'] else 0
        return charge_soc_diff_sum, discharge_soc_diff_sum, charge, discharge

    except Exception as e:
        logging.error(e)
        return 0, 0, 0, 0


def get_station_day_alarms(station, target_day):
    """
    获取一个并网点的某一天的故障告警数量
    """""
    start_time = datetime.datetime.combine(target_day, datetime.datetime.min.time())
    end_time = datetime.datetime.combine(target_day, datetime.datetime.max.time())

    filters = {
        "start_time__gte": start_time,
        "start_time__lte": end_time,
        "station_id": station.id,
        "type__in": [1, 2]
    }

    alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).all()
    alarm_count = alarm_instances.filter(type=2).count()
    fault_count = alarm_instances.filter(type=1).count()
    return fault_count, alarm_count


def getAgoTimeS(startdate, days=7):
    u'获取N天前时间,默认一周'
    return (startdate - datetime.timedelta(days=days)).strftime("%Y-%m-%d")


def get_day_data_for_station(master_station, target_day):
    """
    获取一个(主)并网点的某一天的运行报告
    """""
    # 先查询是否存在结算表
    slave_stations = master_station.stationdetails_set.filter(is_delete=0, is_account=1).all()
    if not slave_stations:  # 不使用结算表
        slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
    return_dict = {
        'datetime': target_day.strftime("%Y-%m-%d"),
        'station_name': master_station.name,

        # "day_charge_list": [],
        # "day_discharge_list": [],
        # "day_soc_list": [],
        # "day_type_list": [],
        # "day_time_list": [],

        'charge_cap': 0,
        'discharge_cap': 0,
        'comp_rate': 0,
        # 'count': count,

        'theory_charge': 0,
        'theory_discharge': 0,
        'theory_charge_comp_rate': 0,
        'theory_discharge_comp_rate': 0,

        'soc_init': 0,
        'soc_final': 0,
        'soc_max': 0,
        'soc_min': 0,

        'spike_charge': 0,
        'spike_discharge': 0,

        'peak_charge': 0,
        'peak_discharge': 0,

        'flat_charge': 0,
        'flat_discharge': 0,

        'valley_charge': 0,
        'valley_discharge': 0,

        'dvalley_charge': 0,
        'dvalley_discharge': 0,

        'accu_charge': 0,
        'accu_discharge': 0,
        'accu_charge_soc_sum': 0,
        'accu_discharge_soc_sum': 0,
        'effic': 0,

        'income': 0,
        'fault': 0,
        'alarm': 0,

        "analyze_ids": [],
        "theory_income_day": 0,
        "income_day_reach_yield": 0,
        "theory_income_month": 0,
        "income_month": 0,
        "theory_income_year": 0,
        "income_year": 0,
        "theory_income_all": 0,
        "income_all": 0,
        "reach_the_standard": 0,
        "income_all_reach_yield": 0,
        "star_level": 0,
    }

    accu_charge_report = 0
    accu_discharge_report = 0

    total_accu_charge_soc_sum, total_accu_discharge_soc_sum = 0, 0
    day_discharge_list_array = []
    day_charge_list_array = []
    day_type_list_array = []
    day_time_list_array = []
    day_soc_list_array = []

    # 改查新综合过结算表和计量表的新1d冻结表：表名未定  ========> 注意：由原来的查1h表改查1d表，数据处理逻辑也需要修改
    select_sql = ("SELECT day, v_chag as chag, v_disg as disg, pointed_chag, pointed_disg, peak_chag, peak_disg, flat_chag, flat_disg, valley_chag, valley_disg, dvalley_chag, dvalley_disg  FROM ads_report_chag_disg_union_1d"
                  " where station=%s and station_type<=1 and day=%s")

    # select_sql_account = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_ems_chag_disg_1h"
    #               " where station=%s and day=%s ORDER BY hour ASC")
    # 年、月、累计收益/基准收益 用前一天的数据加今日的数据；判断前一天是否有收益
    yesterday_report = RunningReport.objects.filter(report_type=1, station_name=master_station.name).order_by('-datetime').first()
    is_compute = 0
    if yesterday_report and yesterday_report.income_all:
        is_compute = 1
        if datetime.datetime(target_day.year, target_day.month, 1) != target_day:
            return_dict['income_month'] = yesterday_report.income_month
            return_dict['theory_income_month'] = yesterday_report.theory_income_month
        if datetime.datetime(target_day.year, 1, 1) != target_day:
            return_dict['theory_income_year'] = yesterday_report.theory_income_year
            return_dict['income_year'] = yesterday_report.income_year
        return_dict['theory_income_all'] = yesterday_report.theory_income_all
        return_dict['income_all'] = yesterday_report.income_all
    try:
        for alarm_station in master_station.stationdetails_set.filter(is_delete=0).all():
            # 故障及告警
            fault, alarm = get_station_day_alarms(alarm_station, target_day)
            return_dict['fault'] += fault
            return_dict['alarm'] += alarm
        for station in slave_stations:
            result = ads_db_tool.select_one(select_sql, station.english_name, target_day.strftime('%Y-%m-%d'))

            if result:

                # 当天的充放电量及SOC
                day_charge = round(result['chag'], 2)
                day_discharge = round(result['disg'], 2)

                # comp_rate = round(day_discharge/day_charge * 100, 2) if day_charge != 0 else 0

                return_dict['charge_cap'] += day_charge
                return_dict['discharge_cap'] += day_discharge

                # 尖峰充放电量
                return_dict['spike_charge'] += round(result['pointed_chag'], 2)
                return_dict['spike_discharge'] += round(result['pointed_disg'], 2)

                # 峰时充放电量
                return_dict['peak_charge'] += round(result['peak_chag'], 2)
                return_dict['peak_discharge'] += round(result['peak_disg'], 2)

                # 平时充放电量
                return_dict['flat_charge'] += round(result['flat_chag'], 2)
                return_dict['flat_discharge'] += round(result['flat_disg'], 2)

                # 谷时充放电量
                return_dict['valley_charge'] += round(result['valley_chag'], 2)
                return_dict['valley_discharge'] += round(result['valley_disg'], 2)

                # 深谷时充放电量
                return_dict['dvalley_charge'] += round(result['dvalley_chag'], 2)
                return_dict['dvalley_discharge'] += round(result['dvalley_disg'], 2)

            # 当天的收益
            # day_income = round(sum(hour_income_list), 2) if sum(hour_income_list) >= 0 else 0
            # station_incomes = models.StationIncome.objects.filter(station_id=station, master_station=master_station,
            #                                                       income_date=target_day.strftime('%Y-%m-%d'))
            # if station_incomes.exists():
            #     station_income = station_incomes.first()
            #     day_income = station_income.peak_load_shifting + station_income.demand_side_response
            # else:
            #     day_income = 0

            # 基准充放电量: 改查 ads_report_station_income_1d
            day_income, theory_income_day, theory_charge, theory_discharge = get_station_theory_charge_discharge(station, target_day)
            return_dict['income'] += (round(day_income, 2) if day_income != '--' else 0)
            return_dict['theory_income_day'] += (round(theory_income_day, 2) if theory_income_day != '--' else 0)

            return_dict['theory_charge'] += (round(theory_charge, 2) if theory_charge != '--' else 0)
            return_dict['theory_discharge'] += (round(theory_discharge, 2) if theory_discharge != '--' else 0)

            # 基准收益/实际收益 月/年/累计数据
            if is_compute == 1:  # 昨日报告数据加今日
                income, base_income = get_station_income_day(station, target_day)
                return_dict['theory_income_month'] += round(base_income, 2)
                return_dict['income_month'] += round(income, 2)
                return_dict['theory_income_year'] += round(base_income, 2)
                return_dict['income_year'] += round(income, 2)

                return_dict['theory_income_all'] += round(base_income, 2)
                return_dict['income_all'] += round(income, 2)

            else:  # 重新计算
                income, base_income = get_station_income_all(station, target_day)
                return_dict['theory_income_month'] += round(base_income[2], 2)
                return_dict['income_month'] += round(income[2], 2)
                return_dict['theory_income_year'] += round(base_income[1], 2)
                return_dict['income_year'] += round(income[1], 2)
                return_dict['theory_income_all'] += round(base_income[0], 2)
                return_dict['income_all'] += round(income[0], 2)


            # 累计充放电量及累计充放电效率
            accu_charge_soc_sum, accu_discharge_soc_sum, accu_charge_, accu_discharge_ = (
                get_station_accu_charge_discharge_soc_from_ads_report_chag_disg_union_data(station, target_day))

            accu_charge_report += round(accu_charge_, 2)
            accu_discharge_report += round(accu_discharge_, 2)

            accu_charge, accu_discharge = get_station_accu_charge_discharge(station, target_day)

            return_dict['accu_charge'] += round(accu_charge, 2)
            return_dict['accu_discharge'] += round(accu_discharge, 2)

            total_accu_charge_soc_sum += accu_charge_soc_sum
            total_accu_discharge_soc_sum += accu_discharge_soc_sum



            if station.slave != 0:
                # soc
                soc_dict = get_station_soc(station, target_day)
                print(577, station.station_name, soc_dict)
                soc_init = soc_dict['initial_soc']
                soc_final = soc_dict['final_soc']
                soc_max = soc_dict['maximum_soc']
                soc_min = soc_dict['minimum_soc']

                return_dict['soc_init'] += soc_init / len(slave_stations.exclude(slave=0))
                return_dict['soc_final'] += soc_final / len(slave_stations.exclude(slave=0))
                return_dict['soc_max'] += soc_max / len(slave_stations.exclude(slave=0))
                return_dict['soc_min'] += soc_min / len(slave_stations.exclude(slave=0))

        # 计算主站信息
        return_dict['income_day_reach_yield'] = round(return_dict['income'] / return_dict['theory_income_day'] * 100, 2) if return_dict['theory_income_day'] != 0 else '--'
        return_dict['income_all_reach_yield'] = round(return_dict['income_all'] / return_dict['theory_income_all'] * 100, 2) if return_dict['theory_income_all'] != 0 else '--'
        return_dict['reach_the_standard'] = 1 if return_dict['income_day_reach_yield'] != '--' and return_dict['income_day_reach_yield'] >= 100 else 0


        return_dict['comp_rate'] = round(return_dict['discharge_cap']/return_dict['charge_cap'] * 100, 2) if return_dict[
            'charge_cap'] else 0

        return_dict['theory_charge_comp_rate'] = round(float(return_dict['charge_cap'])/return_dict['theory_charge'] * 100, 2)\
            if return_dict['theory_charge'] != 0 else 0

        return_dict['theory_discharge_comp_rate'] = round(float(return_dict['discharge_cap'])/return_dict['theory_discharge']
                                                          * 100, 2) if return_dict['theory_discharge'] != 0 else 0


        star_level = 1
        if return_dict['income_day_reach_yield'] != '--':
            if return_dict['theory_charge_comp_rate'] != '--' and return_dict['theory_discharge_comp_rate'] != '--':
                if return_dict['income_day_reach_yield'] >= 100:
                    if return_dict['theory_discharge_comp_rate'] >= 100 and return_dict['theory_charge_comp_rate'] >= 100:
                        star_level = 5
                    else:
                        star_level = 4
                else:
                    if 90 <= return_dict['income_day_reach_yield'] < 100:
                        star_level = 3
                    elif 80 <= return_dict['income_day_reach_yield'] < 90:
                        star_level = 2

        return_dict['star_level'] = star_level

        analyze_ids = []
        if return_dict['theory_charge_comp_rate'] != '--' and return_dict['theory_discharge_comp_rate'] != '--' and \
                return_dict['income_day_reach_yield'] != '--':
            if return_dict['theory_discharge_comp_rate'] - return_dict['theory_charge_comp_rate'] > 10 and \
                    return_dict['income_day_reach_yield'] >= 100:
                analyze_ids.append('5')  # 起始SOC差异
            if return_dict['theory_discharge_comp_rate'] - return_dict['theory_charge_comp_rate'] > 10 and \
                    return_dict['income_day_reach_yield'] < 100:
                analyze_ids.append('10')  # 充电抑制或放电抑制
            if return_dict['theory_discharge_comp_rate'] == 0 and return_dict['theory_charge_comp_rate'] == 0:
                analyze_ids.append('11')  # 业主停电或设备故障
            if return_dict['theory_discharge_comp_rate'] == 0 and 0 < return_dict['theory_charge_comp_rate'] <= 1:
                analyze_ids.append('12')  # 业主要求策略
            if return_dict['theory_charge_comp_rate'] >= 100 and return_dict['theory_discharge_comp_rate'] >= 100 and \
                    return_dict['income_day_reach_yield'] < 100:
                analyze_ids.append('2')  # 放电抑制
        return_dict['analyze_ids'] = ','.join(analyze_ids) if analyze_ids else ''

        if (accu_charge_report and accu_discharge_report and
                total_accu_charge_soc_sum and total_accu_discharge_soc_sum):
            return_dict['effic'] = abs(round((accu_discharge_report / total_accu_discharge_soc_sum) / (
                    accu_charge_report / total_accu_charge_soc_sum) * 100, 2))
            return_dict['effic'] = return_dict['effic'] if return_dict['effic'] <= 100.00 else 100.00
        elif accu_charge_report and not accu_discharge_report:
            return_dict['effic'] = 0
        elif accu_discharge_report and not accu_charge_report:
            return_dict['effic'] = 100.00
        else:
            return_dict['effic'] = 0

        return_dict['accu_discharge_'] = accu_discharge_report
        return_dict['accu_charge_'] = accu_charge_report

        # return_dict['day_charge_list'] = [round(sum(x), 1) for x in zip(*day_charge_list_array)]
        # return_dict['day_discharge_list'] = [round(sum(x), 1) for x in zip(*day_discharge_list_array)]
        # return_dict['day_soc_list'] = [round(sum(x)/len(slave_stations), 1) for x in zip(*day_soc_list_array)]
        # return_dict['day_type_list'] = day_type_list_array[0] if day_type_list_array else []
        # return_dict['day_time_list'] = day_time_list_array[0] if day_time_list_array else []
        return_dict['accu_charge_soc_sum'] = round(total_accu_charge_soc_sum, 2)
        return_dict['accu_discharge_soc_sum'] = round(total_accu_discharge_soc_sum, 2)

        if (return_dict['charge_cap'] or return_dict['discharge_cap'] or return_dict['accu_charge'] or
                return_dict['accu_discharge'] or return_dict['theory_charge'] or
                return_dict['theory_discharge']):
            return return_dict
        else:
            print(297, f"{master_station.name}的{target_day}的运行报告数据获取失败：无数据！！！！")
            return None
    except Exception as e:
        print(629, f"{master_station.name}的{target_day}的运行报告数据获取失败：{traceback.print_exc()}！！！！")
        raise e


def get_day_report(master_station, target_day):
    """
    获取某个站某一天的运行报告数据
    """""
    # target_day = datetime(year_, month_, day_)
    date_day = target_day.strftime('%Y-%m-%d')

    # 已存在对应站对应天的报告则跳过====> 改为已存在则删除
    has_reports = RunningReport.objects.filter(report_type=1, station_name=master_station.name, datetime=date_day).all()
    if has_reports.exists():
        print(624, f"{master_station.name}的日{date_day}报告已存在！")
        for report in has_reports:
            report.delete()
            print(691, f"{master_station.name}的 {date_day} 旧日报告已删除！")

    print(626, f"{master_station.name}的日{date_day}报告开始生成。。。。。。")

    res = get_day_data_for_station(master_station, target_day)

    try:
        if res:
            # res.pop('day_charge_list')
            # res.pop('day_discharge_list')
            # res.pop('day_soc_list')
            # res.pop('day_type_list')
            # res.pop('day_time_list')
            res.pop('accu_charge_soc_sum')
            res.pop('datetime')
            res.pop('accu_discharge_soc_sum')
            res.pop('accu_charge_')
            res.pop('accu_discharge_')
            RunningReport.objects.create(
                datetime=date_day, report_type=1, **res
            )
            print(654, f"{master_station.name}的日{target_day}报告生成成功了！")

            # 写入收益表
            # income_ins = models.StationIncome.objects.filter(
            #     master_station=master_station,
            #     income_date__day=target_day.day,
            #     income_date__month=target_day.month,
            #     income_date__year=target_day.year,
            #     record=2,
            # )
            # if not income_ins:
            #     models.StationIncome.objects.create(
            #         peak_load_shifting=res['income'], master_station=master_station, income_date=target_day, income_type=1, record=2
            #     )
            # else:
            #     income_ins.update(peak_load_shifting=res['income'], income_type=1, record=2)

        else:
            print(660, f"{master_station.name}的日{target_day}报告生成跳过了！！")

    except Exception as e:
        if 'Duplicate entry' in e.args:
            pass
        else:
            print(779, traceback.print_exc())
            print(663, f"{master_station.name}的日{target_day}报告生成失败了: {e}！！!!!")
            raise e


def get_all_stations_pre_day_report():
    """
    获取所有站前一天的运行报告数据
    """""
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    close_old_connections()  # 关闭旧链接
    for master_station in master_stations:
        # print(338, master_station.name)
        try:
            get_day_report(master_station, yesterday)
        except Exception as e:
            print(traceback.print_exc())
            error_log.error(f"{master_station.name} 生成前一天的运行报告数据失败:{e}")


def get_all_stations_his_day_report():
    """
    获取所有站当年所有历史日运行报告数据
    """""
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    # end_day = datetime.datetime(year=2024, month=8, day=13).date()
    first_day_of_year = today.replace(year=2024, month=6, day=15)
    target_day = first_day_of_year
    # stations = models.StationDetails.objects.all()
    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    if master_stations.exists():
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            while target_day <= yesterday:
                for master_station in master_stations:
                    # print(702, master_station.name, target_day)
                    future = executor.submit(get_day_report, master_station, target_day)
                # get_day_report(station, target_day)
                target_day += datetime.timedelta(days=1)


def get_week_report(master_station, year_, week_):
    """
    获取某个站某一周的运行报告数据
    """""
    today = datetime.date.today()

    first_day, last_day = get_week_start_end(year_, week_)
    # first_day, last_day = get_week_start_end(2023, 52)
    # date_month = first_day.strftime('%Y-%m')
    datetime_ = first_day.strftime('%Y-%m-%d')
    datetime_end = last_day.strftime('%Y-%m-%d')

    # 已存在对应站对应月的报告则跳过====> 改为已存在则删除，再新建
    reports = RunningReport.objects.filter(report_type=2, station_name=master_station.name, datetime=datetime_).all()
    if reports.exists():
        print(376, f"{master_station.name}的{year_}-{week_}周报告已存在！")
        for report in reports:
            report.delete()
            print(376, f"{master_station}的 {year_} 年-第{week_} 周的旧周报告已删除！")

    print(681, f"{master_station.name}的{year_}-{week_}周报告生成中。。。。。。。")

    # 当周结束日期不能超过当天日期
    if last_day.date() > today:
        last_day = today

    # 周为目前周之后的无法获取报告数据
    if first_day.date() > today:
        return

    charge_cap = 0
    discharge_cap = 0
    income = 0
    theory_charge = 0
    theory_discharge = 0

    accu_charge = 0
    accu_discharge = 0

    spike_charge = 0
    spike_discharge = 0
    peak_charge = 0
    peak_discharge = 0
    flat_charge = 0
    flat_discharge = 0
    valley_charge = 0
    valley_discharge = 0

    dvalley_charge = 0
    dvalley_discharge = 0

    fault = 0
    alarm = 0

    accu_charge_ = 0
    accu_discharge_ = 0
    accu_charge_soc_sum = 0
    accu_discharge_soc_sum = 0

    target_day = first_day
    target_day = target_day.date() if isinstance(target_day,datetime.datetime) else target_day
    last_day = last_day.date() if isinstance(last_day,datetime.datetime) else last_day
    while target_day <= last_day:
        res = get_day_data_for_station(master_station, target_day)
        if res:
            print(724, f"{master_station.name}的{year_}-{week_}周报告生成中，{target_day}数据获取成功！")
            charge_cap += res['charge_cap']
            discharge_cap += res['discharge_cap']

            income += res['income']
            theory_charge += res['theory_charge']
            theory_discharge += res['theory_discharge']

            spike_charge += res['spike_charge']
            spike_discharge += res['spike_discharge']
            peak_charge += res['peak_charge']
            peak_discharge += res['peak_discharge']
            flat_charge += res['flat_charge']
            flat_discharge += res['flat_discharge']
            valley_charge += res['valley_charge']
            valley_discharge += res['valley_discharge']

            dvalley_charge += res['dvalley_charge']
            dvalley_discharge += res['dvalley_discharge']

            fault += res['fault']
            alarm += res['alarm']

            if target_day == last_day:
                accu_charge_ = res['accu_charge_']
                accu_discharge_ = res['accu_discharge_']
                accu_charge = res['accu_charge']
                accu_discharge = res['accu_discharge']
                accu_charge_soc_sum = res['accu_charge_soc_sum']
                accu_discharge_soc_sum = res['accu_discharge_soc_sum']
        else:
            print(738, f"{master_station.name}的{year_}-{week_}周报告生成中，{target_day}数据为空，已跳过！！！")

        target_day += datetime.timedelta(days=1)

    try:
        print(754, f"{master_station.name}的{year_}-{week_}周报告生成中，charge_cap: {charge_cap}")
        comp_rate = discharge_cap/charge_cap * 100 if charge_cap != 0 else 0

        if sum([float(station.rated_capacity) for station in master_station.stationdetails_set.filter(is_delete=0).all()]):
            count = round(math.sqrt(charge_cap*discharge_cap)/sum([float(station.rated_capacity) for station in
                                                                   master_station.stationdetails_set.filter(is_delete=0).all()]))
        else:
            count = 0

        theory_charge_comp_rate = round(float(charge_cap)/theory_charge * 100, 2) if theory_charge != 0 else 0
        theory_discharge_comp_rate = round(float(discharge_cap)/theory_discharge * 100, 2) if theory_discharge != 0 else 0

        if accu_charge_ and accu_discharge_ and accu_charge_soc_sum and accu_discharge_soc_sum:
            effic = abs(round((accu_discharge_ / accu_discharge_soc_sum) / (
                    accu_charge_ / accu_charge_soc_sum) * 100, 2))
            effic = effic if effic <= 100.00 else 100.00
        elif accu_charge_ and not accu_discharge_:
            effic = 0
        elif accu_discharge_ and not accu_charge_:
            effic = 100.00
        else:
            effic = 0
    except Exception as e:
        print(754, f"{master_station.name}的{year_}-{week_}周报告生成中，{target_day}数据获取失败！!!!!!!!!!!!!!!!!!!!!!")
        print(755, e)
        raise e

    print(762, f"{master_station.name}的{year_}-{week_}周报告生成中，充放电数据{effic}获取成功！****************************")

    try:
        if charge_cap or discharge_cap or income or theory_charge or theory_discharge or accu_charge or accu_discharge:
            r = RunningReport.objects.create(
                report_type=2,
                datetime=datetime_,
                datetime_end=datetime_end,
                station_name=master_station.name,

                charge_cap=charge_cap,
                discharge_cap=discharge_cap,
                comp_rate=comp_rate,
                count=count,

                theory_charge=theory_charge,
                theory_discharge=theory_discharge,
                theory_charge_comp_rate=theory_charge_comp_rate,
                theory_discharge_comp_rate=theory_discharge_comp_rate,

                spike_charge=spike_charge,
                spike_discharge=spike_discharge,

                peak_charge=peak_charge,
                peak_discharge=peak_discharge,

                flat_charge=flat_charge,
                flat_discharge=flat_discharge,

                valley_charge=valley_charge,
                valley_discharge=valley_discharge,

                dvalley_charge=dvalley_charge,
                dvalley_discharge=dvalley_discharge,

                accu_charge=accu_charge,
                accu_discharge=accu_discharge,
                effic=effic,

                income=income,
                fault=fault,
                alarm=alarm
            )
            print(844, f"{master_station.name}的第{week_}周的周报告生成成功了！")
        else:
            print(845, f"{master_station.name}的第{week_}周的周报告生成无数据跳过了！！")
    except Exception as e:
        if 'Duplicate entry' in e.args:
            print(845, f"{master_station.name}的第{week_}周的周报告生成重复：{e}！！！！！！")
            pass
        else:
            print(845, f"{master_station.name}的第{week_}周的周报告生成失败：{e}！！！！！！")
            raise e


def get_all_stations_pre_week_report():
    """
    获取所有站上一周的周运行报告数据
    """""
    today = datetime.date.today() - datetime.timedelta(days=1)
    year_ = today.year
    pre_week = today.strftime('%W')
    print(838, f"获取所有站上一周的周运行报告数据，年份：{year_}，周：{pre_week}")

    # master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    close_old_connections()  # 关闭旧链接
    if master_stations.exists():
        for master_station in master_stations:
            try:
                get_week_report(master_station, year_, pre_week)
            except Exception as e:
                print(845, traceback.print_exc())
                error_log.error(f"{master_station.name}的第{pre_week}周的周报告生成失败：{e}！！！！！！")


def get_all_stations_his_week_report():
    """
    获取所有站今年的历史周运行报告数据
    """""
    today = datetime.date.today()
    pre_week = today.isocalendar()[1] - 1 if today.isocalendar()[1] > 1 else 52
    last_week = pre_week

    # first_day_of_year = today.replace(month=1, day=1)
    # first_month = first_day_of_year.month
    year_ = today.year if pre_week < 52 else today.year - 1
    # 手动修改需要跑数据年份和周
    # year_ = 2023
    # current_month = today.month

    # first_day_of_this_year = datetime.date(today.year, 1, 1)
    # first_weed_of_year = first_day_of_this_year.isocalendar()[1]

    target_week = 1

    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    if master_stations.exists():
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            # futures = list()

            # for unit_item in total_units.items():
            #     future = executor.submit(tem_get_project_charge_discharge_by_day, unit_item, start_day, end_day)
            #     futures.append(future)
            while target_week <= last_week:
                for master_station in master_stations:
                    future = executor.submit(get_week_report, master_station, year_, target_week)
                    # futures.append(future)
                    # print(518, target_week, station.station_name)
                    # get_week_report(station, year_, target_week)

                target_week += 1


def get_month_report(master_station, year_, month_):
    """
    获取某个站某一月的运行报告数据
    """""
    today = datetime.datetime.today()

    first_day, last_day = get_month_start_end(year_, month_)
    date_month = first_day.strftime('%Y-%m')

    # 已存在对应站对应月的报告则跳过======>  改为已存在则删除再新建
    reports = RunningReport.objects.filter(report_type=3, station_name=master_station.name, datetime=date_month)
    if reports.exists():
        print(874, f"{master_station.name}的{year_}年，第{month_}月的月报告已存在。")
        for report in reports:
            report.delete()
            print(f"{master_station.name}的{year_}年，第{month_}月的旧月报告已删除！！！！！")
    print(875, f"{master_station.name}的{year_}年，第{month_}月的月报告开始生成...")

    # 当月结束日期不能超过当天日期
    if last_day.date() > today.date():
        last_day = today
        error_log.error(f"{master_station.name}的{year_}年，第{month_}月的月报告生成失败，当月结束日期不能超过当天日期！")

    # 月份为目前月份之后的无法获取报告数据
    if first_day.date() > today.date():
        error_log.error(f"{master_station.name}的{year_}年，第{month_}月的月报告生成失败，当月开始日期不能超过当天日期！")
        return

    charge_cap = 0
    discharge_cap = 0
    income = 0
    theory_charge = 0
    theory_discharge = 0

    accu_charge = 0
    accu_discharge = 0
    accu_charge_ = 0
    accu_discharge_ = 0

    spike_charge = 0
    spike_discharge = 0
    peak_charge = 0
    peak_discharge = 0
    flat_charge = 0
    flat_discharge = 0
    valley_charge = 0
    valley_discharge = 0

    dvalley_charge = 0
    dvalley_discharge = 0

    fault = 0
    alarm = 0

    accu_charge_soc_sum = 0
    accu_discharge_soc_sum = 0
    target_day = first_day
    while target_day.date() < last_day.date() or target_day.date() == last_day.date():
        try:
            res = get_day_data_for_station(master_station, target_day.date())
        except Exception as e:
            # print(950, '*' * 20, master_station.name, target_day, e)
            raise e
        if res:
            # print(844, f"{master_station.name}的{year_}年，第{target_day.strftime('%Y-%m-%d')}日的数据已获取...")
            charge_cap += res['charge_cap']
            discharge_cap += res['discharge_cap']

            income += res['income']
            theory_charge += res['theory_charge']
            theory_discharge += res['theory_discharge']

            spike_charge += res['spike_charge']
            spike_discharge += res['spike_discharge']
            peak_charge += res['peak_charge']
            peak_discharge += res['peak_discharge']
            flat_charge += res['flat_charge']
            flat_discharge += res['flat_discharge']
            valley_charge += res['valley_charge']
            valley_discharge += res['valley_discharge']
            dvalley_charge += res['dvalley_charge']
            dvalley_discharge += res['dvalley_discharge']

            fault += res['fault']
            alarm += res['alarm']

            if target_day.date() == last_day.date():
                accu_charge = res['accu_charge']
                accu_discharge = res['accu_discharge']
                accu_charge_ = res['accu_charge_']
                accu_discharge_ = res['accu_discharge_']
                accu_charge_soc_sum = res['accu_charge_soc_sum']
                accu_discharge_soc_sum = res['accu_discharge_soc_sum']

        target_day += datetime.timedelta(days=1)

    comp_rate = discharge_cap/charge_cap * 100 if charge_cap != 0 else 0
    count = round(math.sqrt(charge_cap*discharge_cap)/sum([float(station.rated_capacity) for station in
                                                           master_station.stationdetails_set.filter(is_delete=0).all()]))
    theory_charge_comp_rate = round(float(charge_cap)/theory_charge * 100, 2) if theory_charge != 0 else 0
    theory_discharge_comp_rate = round(float(discharge_cap)/theory_discharge * 100, 2) if theory_discharge != 0 else 0

    # effic = round(accu_discharge/accu_charge * 100, 2) if accu_charge != 0 else 0
    if accu_charge_ and accu_discharge_ and accu_charge_soc_sum and accu_discharge_soc_sum:
        effic = abs(round((accu_discharge_ / accu_discharge_soc_sum) / (
                accu_charge_ / accu_charge_soc_sum) * 100, 2))
        effic = effic if effic <= 100.00 else 100.00
    elif accu_charge_ and not accu_discharge_:
        effic = 0
    elif accu_discharge_ and not accu_charge_:
        effic = 100.00
    else:
        effic = 0

    try:
        if charge_cap or discharge_cap or income or theory_charge or theory_discharge or accu_charge or accu_discharge:
            r = RunningReport.objects.create(
                report_type=3,
                datetime=date_month,
                station_name=master_station.name,

                charge_cap=charge_cap,
                discharge_cap=discharge_cap,
                comp_rate=comp_rate,
                count=count,

                theory_charge=theory_charge,
                theory_discharge=theory_discharge,
                theory_charge_comp_rate=theory_charge_comp_rate,
                theory_discharge_comp_rate=theory_discharge_comp_rate,

                spike_charge=spike_charge,
                spike_discharge=spike_discharge,

                peak_charge=peak_charge,
                peak_discharge=peak_discharge,

                flat_charge=flat_charge,
                flat_discharge=flat_discharge,

                valley_charge=valley_charge,
                valley_discharge=valley_discharge,

                dvalley_charge=dvalley_charge,
                dvalley_discharge=dvalley_discharge,

                accu_charge=accu_charge,
                accu_discharge=accu_discharge,
                effic=effic,

                income=income,
                fault=fault,
                alarm=alarm
            )
            print(1033, f"{master_station.name}的{r.datetime}月的月报告生成成功了！")
        else:
            print(997, f"{master_station.name}的{year_}-{month_}月的月报告无数据跳过了！!")
    except Exception as e:
        if 'Duplicate entry' in e.args:
            pass
        else:
            print(1000, f"{master_station.name}的{r.datetime}月的月报告生成失败:{e}了！!!!!")
            raise e


def get_all_stations_pre_month_report():
    """
    获取所有站上一个月的月运行报告数据
    """""
    today = datetime.date.today()
    pre_month = today.month - 1 if today.month > 1 else 12
    year_ = today.year if today.month > 1 else today.year - 1

    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    close_old_connections()  # 关闭旧链接
    if master_stations.exists():
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            for master_station in master_stations:
                # get_month_report(master_station, year_, pre_month)
                executor.submit(get_month_report, master_station, year_, pre_month)


def get_all_stations_his_month_report():
    """
    获取所有站今年的历史月运行报告数据, 如果是1月则获取前一年的历史月运行报告数据
    """""
    today = datetime.date.today()
    pre_month_day = today.replace(day=1) - datetime.timedelta(days=1)
    last_month = pre_month_day.month

    first_day_of_year = today.replace(month=1, day=1)
    first_month = first_day_of_year.month
    year_ = today.year if today.month > 1 else today.year - 1
    # current_month = today.month

    # year_ = 2023
    target_month = first_month

    master_stations = models.MaterStation.objects.filter(is_delete=0, project__is_used=1).all()
    if master_stations.exists():
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            while target_month <= last_month:
                # print(516, target_month)
                for master_station in master_stations:
                    future = executor.submit(get_month_report, master_station, year_, target_month)
                    # print(518, target_month, station.station_name)
                    # get_month_report(station, year_, target_month)

                target_month += 1

