from django.core.exceptions import ValidationError
from django.db import models

from common.base_models import BaseModel

# Create your models here.

# from common.base_models import BaseModel

"""
点表

"""


# class PointType(models.Model):
#     """类型"""
#     id = models.IntegerField(verbose_name="ID", primary_key=True)
#     type = models.SmallIntegerField(verbose_name="类型", choices=((1, "一类"), (2, "二类"), (3, "三类")))
#     device = models.CharField(verbose_name="设备名称", max_length=8)
#     is_stand = models.SmallIntegerField(verbose_name="是否标准类型", choices=((0, "非标"), (1, "标准")), default=1)
#
#     class Meta:
#         db_table = "t_point_type"
#
#
# class PointMeasure(models.Model):
#     """测量量"""
#     name = models.CharField(verbose_name="点位英文", max_length=16)
#     description = models.Char<PERSON><PERSON>(verbose_name="数据描述", max_length=32)
#     data_type = models.CharField(verbose_name="数据类型", max_length=8)
#     unit = models.CharField(verbose_name="单位", max_length=8, null=True)
#     point_type = models.ForeignKey(
#         to="PointType",
#         verbose_name="点表类型",
#         on_delete=models.DO_NOTHING
#     )
#
#     class Meta:
#         db_table = "t_point_measure"
#
#
# class PointStatus(models.Model):
#     """状态量"""
#     name = models.CharField(verbose_name="点位英文", max_length=16)
#     description = models.CharField(verbose_name="数据描述", max_length=32)
#     data_type = models.CharField(verbose_name="数据类型", max_length=8)
#     a_status_name = models.CharField(verbose_name='a_status', max_length=32)
#     b_status_name = models.CharField(verbose_name='b_status', max_length=32)
#     point_type = models.ForeignKey(
#         to="PointType",
#         verbose_name="点表类型",
#         # on_delete=models.DO_NOTHING
#     )
#
#     class Meta:
#         db_table = "t_point_status"
#
#
# class PointDiscrete(models.Model):
#     """离散量"""
#     name = models.CharField(verbose_name="点位英文", max_length=16)
#     description = models.CharField(verbose_name="数据描述", max_length=32)
#     data_type = models.CharField(verbose_name="数据类型", max_length=8)
#     n_status = models.CharField(verbose_name="离散值", max_length=16)
#     desc_status = models.CharField(verbose_name='离散描述', max_length=32)
#     point_type = models.ForeignKey(
#         to="PointType",
#         verbose_name="点表类型",
#         on_delete=models.DO_NOTHING
#     )
#
#     class Meta:
#         db_table = "t_point_discrete"
#
#
# class PointCumulant(models.Model):
#     """累积量"""
#     name = models.CharField(verbose_name="点位英文", max_length=16)
#     description = models.CharField(verbose_name="数据描述", max_length=32)
#     data_type = models.CharField(verbose_name="数据类型", max_length=8)
#     unit = models.CharField(verbose_name="单位", max_length=8)
#     point_type = models.ForeignKey(
#         to="PointType",
#         verbose_name="点表类型",
#         on_delete=models.DO_NOTHING
#     )
#
#     class Meta:
#         db_table = "t_point_cumulant"

class UploadedFile(models.Model):
    Uuid = models.CharField(max_length=64, null=False)
    file = models.FileField(upload_to='uploads/')  # 用于存储文件本身
    upload_date = models.DateTimeField(auto_now_add=True)  # 上传日期
    file_name = models.CharField(max_length=255)  # 文件名

    class Meta:
        db_table = "t_uploaded_file"

    def __str__(self):
        return self.file_name

    def get_download_url(self):
        return self.file.url


class UserStrategy(BaseModel):
    """新.用户自动控制策略"""""

    name = models.CharField(verbose_name="策略名", max_length=128, blank=True, null=True)
    en_name = models.CharField(verbose_name="策略名", max_length=256, blank=True, null=True)
    user = models.ForeignKey(
        to="user.UserDetails",
        verbose_name="用户",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )

    is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0,
                                         null=True, blank=True)

    class Meta:
        db_table = "t_user_strategy"

    def __str__(self):
        return self.name


class Month(models.Model):
    """
    用户自动控制策略及其分类的月份
    """""
    month_number = models.IntegerField()
    strategy = models.ForeignKey(
        to="UserStrategy",
        verbose_name="用户策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    user_Strategy_Category = models.ForeignKey(
        to="UserStrategyCategoryNew",
        verbose_name="策略分类",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    is_valid = models.BooleanField(default=True)

    # def clean(self):
    #     if not self.is_valid:
    #         raise ValidationError("This month has already been used in another strategy")
    class Meta:
        db_table = "t_user_strategy_category_month"


# class UserStrategyCategory(BaseModel):
#     """
#     用户自动控制策略-分类
#     """""
#     name = models.CharField(verbose_name="分类名称", max_length=128, blank=False, null=False)
#     en_name = models.CharField(verbose_name="分类名称", max_length=256, blank=False, null=False)
#     strategy = models.ForeignKey(
#         to="UserStrategy",
#         verbose_name="用户控制策略",
#         on_delete=models.CASCADE,
#         blank=True,
#         null=True,
#     )
#
#     charge_config = models.CharField(verbose_name="24小时充放电量配置", max_length=512, null=True, blank=True)
#     is_follow = models.SmallIntegerField(verbose_name="负荷跟随", choices=((1, "跟随"), (0, "不跟随")), null=True,
#                                         blank=True)
#     rl_list = models.CharField(verbose_name="阈值", max_length=5120, null=True, blank=True)
#     is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0, null=True,
#                                         blank=True)
#     pv0 = models.SmallIntegerField(
#         verbose_name="0时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv1 = models.SmallIntegerField(
#         verbose_name="1时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv2 = models.SmallIntegerField(
#         verbose_name="2时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv3 = models.SmallIntegerField(
#         verbose_name="3时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv7 = models.SmallIntegerField(
#         verbose_name="4时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv5 = models.SmallIntegerField(
#         verbose_name="5时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv6 = models.SmallIntegerField(
#         verbose_name="6时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv4 = models.SmallIntegerField(
#         verbose_name="7时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv8 = models.SmallIntegerField(
#         verbose_name="8时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv9 = models.SmallIntegerField(
#         verbose_name="9时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv10 = models.SmallIntegerField(
#         verbose_name="10时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv11 = models.SmallIntegerField(
#         verbose_name="11时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv12 = models.SmallIntegerField(
#         verbose_name="12时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv13 = models.SmallIntegerField(
#         verbose_name="13时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv14 = models.SmallIntegerField(
#         verbose_name="14时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv15 = models.SmallIntegerField(
#         verbose_name="15时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv16 = models.SmallIntegerField(
#         verbose_name="16时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv17 = models.SmallIntegerField(
#         verbose_name="17时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv18 = models.SmallIntegerField(
#         verbose_name="18时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv19 = models.SmallIntegerField(
#         verbose_name="19时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv20 = models.SmallIntegerField(
#         verbose_name="20时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv21 = models.SmallIntegerField(
#         verbose_name="21时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv22 = models.SmallIntegerField(
#         verbose_name="22时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv23 = models.SmallIntegerField(
#         verbose_name="23时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     explain_json = models.TextField(verbose_name="说明", null=True, blank=True)
#     remark = models.TextField(verbose_name="备注", null=True, blank=True)
#
#     class Meta:
#         db_table = "t_user_strategy_category"


class StrategyApplyHistory(BaseModel):
    """策略下发历史记录"""""

    name = models.CharField(verbose_name="策略名", max_length=128, blank=True, null=True)
    en_name = models.CharField(verbose_name="策略名", max_length=256, blank=True, null=True)
    uid = models.UUIDField(verbose_name="唯一标识", blank=True, null=True)
    station = models.ForeignKey(
        to="user.StationDetails",
        verbose_name="站",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )

    user = models.SmallIntegerField(verbose_name="下发用户id", null=True, blank=True)
    is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0,
                                         null=True, blank=True)

    class Meta:
        db_table = "t_user_strategy_apply_history"


class StrategyApplyHistoryMonth(models.Model):
    """
    策略下发历史记录的月份
    """""
    month_number = models.IntegerField()
    strategy = models.ForeignKey(
        to="StrategyApplyHistory",
        verbose_name="用户策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    user_Strategy_Category = models.ForeignKey(
        to="StrategyApplyHistoryCategoryNew",
        verbose_name="策略分类",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    is_valid = models.BooleanField(default=True)

    # def clean(self):
    #     if not self.is_valid:
    #         raise ValidationError("This month has already been used in another strategy")
    class Meta:
        db_table = "t_user_strategy_apply_history_month"


# class StrategyApplyHistoryCategory(BaseModel):
#     """
#     策略下发历史记录-分类
#     """""
#     name = models.CharField(verbose_name="分类名称", max_length=128, blank=False, null=False)
#     en_name = models.CharField(verbose_name="分类名称", max_length=256, blank=False, null=False)
#     strategy = models.ForeignKey(
#         to="StrategyApplyHistory",
#         verbose_name="用户控制策略",
#         on_delete=models.CASCADE,
#         blank=True,
#         null=True,
#     )
#
#     charge_config = models.CharField(verbose_name="24小时充放电量配置", max_length=512, null=True, blank=True)
#     is_follow = models.SmallIntegerField(verbose_name="负荷跟随", choices=((1, "跟随"), (0, "不跟随")), null=True,
#                                         blank=True)
#     rl_list = models.CharField(verbose_name="阈值", max_length=5120, null=True, blank=True)
#     is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0, null=True,
#                                         blank=True)
#     pv0 = models.SmallIntegerField(
#         verbose_name="0时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv1 = models.SmallIntegerField(
#         verbose_name="1时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv2 = models.SmallIntegerField(
#         verbose_name="2时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv3 = models.SmallIntegerField(
#         verbose_name="3时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv7 = models.SmallIntegerField(
#         verbose_name="4时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv5 = models.SmallIntegerField(
#         verbose_name="5时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv6 = models.SmallIntegerField(
#         verbose_name="6时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv4 = models.SmallIntegerField(
#         verbose_name="7时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv8 = models.SmallIntegerField(
#         verbose_name="8时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv9 = models.SmallIntegerField(
#         verbose_name="9时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv10 = models.SmallIntegerField(
#         verbose_name="10时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv11 = models.SmallIntegerField(
#         verbose_name="11时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv12 = models.SmallIntegerField(
#         verbose_name="12时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv13 = models.SmallIntegerField(
#         verbose_name="13时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv14 = models.SmallIntegerField(
#         verbose_name="14时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv15 = models.SmallIntegerField(
#         verbose_name="15时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv16 = models.SmallIntegerField(
#         verbose_name="16时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv17 = models.SmallIntegerField(
#         verbose_name="17时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv18 = models.SmallIntegerField(
#         verbose_name="18时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv19 = models.SmallIntegerField(
#         verbose_name="19时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv20 = models.SmallIntegerField(
#         verbose_name="20时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv21 = models.SmallIntegerField(
#         verbose_name="21时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv22 = models.SmallIntegerField(
#         verbose_name="22时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#     pv23 = models.SmallIntegerField(
#         verbose_name="23时峰谷标识",
#         choices=((-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")),
#         null=True,
#         blank=True,
#     )
#
#     class Meta:
#         db_table = "t_user_strategy_apply_history_category"


class UserStrategyCategoryNew(BaseModel):
    """
    用户自动控制策略-分类--新
    """""
    name = models.CharField(verbose_name="分类名称", max_length=128, blank=False, null=False)
    en_name = models.CharField(verbose_name="分类名称", max_length=256, blank=False, null=False)
    strategy = models.ForeignKey(
        to="UserStrategy",
        verbose_name="用户控制策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )

    charge_config = models.CharField(verbose_name="24小时充放电量配置", max_length=5120, null=True, blank=True)
    is_follow = models.SmallIntegerField(verbose_name="负荷跟随", choices=((1, "跟随"), (0, "不跟随")), null=True,
                                        blank=True)
    rl_list = models.CharField(verbose_name="阈值", max_length=5120, null=True, blank=True)
    is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0, null=True,
                                        blank=True)
    pv_list = models.CharField(verbose_name="0时峰谷标识", max_length=512, null=True, blank=True)
    explain_json = models.TextField(verbose_name="说明", null=True, blank=True)
    remark = models.TextField(verbose_name="备注", null=True, blank=True)
    en_remark = models.TextField(verbose_name="备注", null=True, blank=True)

    class Meta:
        db_table = "t_user_strategy_category_new"


class UserStrategyHours(BaseModel):
    """
    用户自动控制策略-分类--半小时
    """""
    strategy = models.ForeignKey(
        to="UserStrategy",
        verbose_name="用户控制策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    data = models.TextField(verbose_name="半小时数据", null=False, blank=False)

    class Meta:
        db_table = "t_user_strategy_hours"


class StrategyApplyHistoryCategoryNew(BaseModel):
    """
    策略下发历史记录-分类--新
    """""
    name = models.CharField(verbose_name="分类名称", max_length=128, blank=False, null=False)
    en_name = models.CharField(verbose_name="分类名称", max_length=256, blank=False, null=False)
    strategy = models.ForeignKey(
        to="StrategyApplyHistory",
        verbose_name="用户控制策略",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )

    charge_config = models.CharField(verbose_name="24小时充放电量配置", max_length=512, null=True, blank=True)
    is_follow = models.SmallIntegerField(verbose_name="负荷跟随", choices=((1, "跟随"), (0, "不跟随")), null=True,
                                        blank=True)
    rl_list = models.CharField(verbose_name="阈值", max_length=5120, null=True, blank=True)
    is_delete = models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0, null=True,
                                        blank=True)
    pv_list = models.CharField(verbose_name="峰谷标识", max_length=512, null=True, blank=True)

    class Meta:
        db_table = "t_user_strategy_apply_history_category_new"