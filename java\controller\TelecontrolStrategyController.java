package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.telecontrol.*;
import com.robestec.analysis.service.TelecontrolStrategyService;
import com.robestec.analysis.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 远程控制策略控制器
 * 对应Python中telecontrol_strategy.py的REST API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/telecontrol-strategy")
@Api(tags = "远程控制策略管理")
public class TelecontrolStrategyController {

    @Autowired
    private TelecontrolStrategyService telecontrolStrategyService;

    /**
     * 策略模板下载
     * 对应Python中的StrategyTemplate方法
     */
    @GetMapping("/strategy-template")
    @ApiOperation("策略模板下载")
    public void downloadStrategyTemplate(HttpServletResponse response) {
        log.info("策略模板下载请求");
        telecontrolStrategyService.downloadStrategyTemplate(response);
    }

    /**
     * 策略模板解析导入
     * 对应Python中的StrategyImport方法
     */
    @PostMapping("/strategy-import")
    @ApiOperation("策略模板解析导入")
    public Result<StrategyImportVO> importStrategy(
            @ApiParam("上传的Excel文件") @RequestParam("files") MultipartFile file,
            @ApiParam("语言") @RequestParam(value = "lang", defaultValue = "zh") String lang) {

        log.info("策略模板导入请求，文件名: {}, 语言: {}", file.getOriginalFilename(), lang);

        StrategyImportDTO importDTO = new StrategyImportDTO();
        importDTO.setFiles(file);
        importDTO.setLang(lang);

        StrategyImportVO result = telecontrolStrategyService.importStrategy(importDTO);
        return Result.succeed(result, "策略模板导入成功");
    }

    /**
     * 获取电站容量信息
     * 对应Python中的PowerPlanStations方法
     */
    @GetMapping("/power-plan-stations")
    @ApiOperation("获取电站容量信息")
    public Result<List<StationCapacityVO>> getPowerPlanStations() {
        log.info("获取电站容量信息请求");

        List<StationCapacityVO> result = telecontrolStrategyService.getPowerPlanStations();
        return Result.succeed(result, "获取电站容量信息成功");
    }

    /**
     * 刷新电站容量信息
     * 对应Python中的PowerPlanStationsRefresh方法
     */
    @PostMapping("/power-plan-stations-refresh")
    @ApiOperation("刷新电站容量信息")
    public Result<List<StationCapacityVO>> refreshPowerPlanStations(
            @ApiParam("刷新参数") @RequestBody @Valid StationRefreshDTO refreshDTO) {

        log.info("刷新电站容量信息请求，电站ID: {}", refreshDTO.getStationIds());

        List<StationCapacityVO> result = telecontrolStrategyService.refreshPowerPlanStations(refreshDTO);
        return Result.succeed(result, "刷新电站容量信息成功");
    }

    /**
     * 功率计划下发列表
     * 对应Python中的PowerPlanList方法
     */
    @PostMapping("/power-plan-list")
    @ApiOperation("功率计划下发列表")
    public Result<PageResult<PowerPlanVO>> getPowerPlanList(
            @ApiParam("查询参数") @RequestBody @Valid PowerPlanQueryDTO queryDTO) {

        log.info("功率计划列表查询请求，参数: {}", queryDTO);

        PageResult<PowerPlanVO> result = telecontrolStrategyService.getPowerPlanList(queryDTO);
        return Result.succeed(result, "查询功率计划列表成功");
    }

    /**
     * 新增计划功率
     * 对应Python中的PowerPlanAdd方法
     */
    @PostMapping("/power-plan")
    @ApiOperation("新增计划功率")
    public Result<Long> createPowerPlan(
            @ApiParam("新增参数") @RequestBody @Valid PowerPlanCreateDTO createDTO) {

        log.info("新增功率计划请求，参数: {}", createDTO);

        Long id = telecontrolStrategyService.createPowerPlan(createDTO);
        return Result.succeed(id, "新增功率计划成功");
    }

    /**
     * 功率计划详情
     * 对应Python中的PowerPlanDetail方法
     */
    @GetMapping("/power-plan-detail/{id}")
    @ApiOperation("功率计划详情")
    public TelecontrolResponse.CommonResult<TelecontrolResponse.PowerPlanDetailResponse> getPowerPlanDetail(
            @ApiParam("计划ID") @PathVariable Long id) {
        
        log.info("功率计划详情查询请求，ID: {}", id);
        
        try {
            TelecontrolResponse.PowerPlanDetailResponse result = 
                telecontrolStrategyService.getPowerPlanDetail(id);
            return TelecontrolResponse.CommonResult.success("查询功率计划详情成功", result);
        } catch (Exception e) {
            log.error("查询功率计划详情失败", e);
            return TelecontrolResponse.CommonResult.error("查询功率计划详情失败: " + e.getMessage());
        }
    }

    /**
     * 修改计划功率
     * 对应Python中的PowerPlanUpdate方法
     */
    @PutMapping("/power-plan-update")
    @ApiOperation("修改计划功率")
    public TelecontrolResponse.CommonResult<String> updatePowerPlan(
            @ApiParam("修改参数") @RequestBody @Valid PowerPlanRequest request) {
        
        log.info("修改功率计划请求，参数: {}", request);
        
        try {
            return telecontrolStrategyService.updatePowerPlan(request);
        } catch (Exception e) {
            log.error("修改功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("修改功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 停止计划功率
     * 对应Python中的powerPlanStop方法
     */
    @PostMapping("/power-plan-stop/{id}")
    @ApiOperation("停止计划功率")
    public TelecontrolResponse.CommonResult<String> stopPowerPlan(
            @ApiParam("计划ID") @PathVariable Long id,
            @ApiParam("语言") @RequestParam(value = "lang", defaultValue = "zh") String lang) {
        
        log.info("停止功率计划请求，ID: {}, 语言: {}", id, lang);
        
        try {
            return telecontrolStrategyService.stopPowerPlan(id, lang);
        } catch (Exception e) {
            log.error("停止功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("停止功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 删除功率计划
     * 对应Python中的powerPlanDelete方法
     */
    @DeleteMapping("/power-plan-delete/{id}")
    @ApiOperation("删除功率计划")
    public TelecontrolResponse.CommonResult<String> deletePowerPlan(
            @ApiParam("计划ID") @PathVariable Long id,
            @ApiParam("账号") @RequestParam String account,
            @ApiParam("密码") @RequestParam String password) {

        log.info("删除功率计划请求，ID: {}, 账号: {}", id, account);

        try {
            return telecontrolStrategyService.deletePowerPlan(id, account, password);
        } catch (Exception e) {
            log.error("删除功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("删除功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 查询下发记录列表
     * 对应Python中的GetPlanHis方法
     */
    @PostMapping("/plan-history")
    @ApiOperation("查询下发记录列表")
    public TelecontrolResponse.CommonResult<TelecontrolResponse.PageResult<TelecontrolResponse.PlanHistoryResponse>> getPlanHistory(
            @ApiParam("查询参数") @RequestBody @Valid StrategyRequest.PlanHistoryRequest request) {

        log.info("查询下发记录列表请求，参数: {}", request);

        try {
            IPage<TelecontrolResponse.PlanHistoryResponse> page =
                telecontrolStrategyService.getPlanHistory(request);

            TelecontrolResponse.PageResult<TelecontrolResponse.PlanHistoryResponse> result =
                new TelecontrolResponse.PageResult<>(
                    page.getRecords(), page.getTotal(),
                    (int) page.getCurrent(), (int) page.getSize()
                );

            return TelecontrolResponse.CommonResult.success("查询下发记录列表成功", result);
        } catch (Exception e) {
            log.error("查询下发记录列表失败", e);
            return TelecontrolResponse.CommonResult.error("查询下发记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 导出下发记录
     * 对应Python中的planHisExport方法
     */
    @PostMapping("/plan-history-export")
    @ApiOperation("导出下发记录")
    public void exportPlanHistory(
            @ApiParam("导出参数") @RequestBody @Valid StrategyRequest.PlanHistoryExportRequest request,
            HttpServletResponse response) {

        log.info("导出下发记录请求，参数: {}", request);
        telecontrolStrategyService.exportPlanHistory(request, response);
    }

    /**
     * 查询下发类型
     * 对应Python中的GetIssuanceType方法
     */
    @GetMapping("/issuance-type")
    @ApiOperation("查询下发类型")
    public TelecontrolResponse.CommonResult<TelecontrolResponse.IssuanceTypeResponse> getIssuanceType() {
        log.info("查询下发类型请求");

        try {
            TelecontrolResponse.IssuanceTypeResponse result =
                telecontrolStrategyService.getIssuanceType();
            return TelecontrolResponse.CommonResult.success("查询下发类型成功", result);
        } catch (Exception e) {
            log.error("查询下发类型失败", e);
            return TelecontrolResponse.CommonResult.error("查询下发类型失败: " + e.getMessage());
        }
    }

    /**
     * 另存项目包
     * 对应Python中的ProjectPackAdd方法
     */
    @PostMapping("/project-pack-add")
    @ApiOperation("另存项目包")
    public TelecontrolResponse.CommonResult<String> addProjectPack(
            @ApiParam("项目包参数") @RequestBody @Valid StrategyRequest.ProjectPackAddRequest request) {

        log.info("另存项目包请求，参数: {}", request);

        try {
            return telecontrolStrategyService.addProjectPack(request);
        } catch (Exception e) {
            log.error("另存项目包失败", e);
            return TelecontrolResponse.CommonResult.error("另存项目包失败: " + e.getMessage());
        }
    }

    /**
     * 加载项目包列表
     * 对应Python中的ProjectPackList方法
     */
    @GetMapping("/project-pack-list/{userId}")
    @ApiOperation("加载项目包列表")
    public TelecontrolResponse.CommonResult<List<TelecontrolResponse.ProjectPackResponse>> getProjectPackList(
            @ApiParam("用户ID") @PathVariable Long userId) {

        log.info("加载项目包列表请求，用户ID: {}", userId);

        try {
            List<TelecontrolResponse.ProjectPackResponse> result =
                telecontrolStrategyService.getProjectPackList(userId);
            return TelecontrolResponse.CommonResult.success("加载项目包列表成功", result);
        } catch (Exception e) {
            log.error("加载项目包列表失败", e);
            return TelecontrolResponse.CommonResult.error("加载项目包列表失败: " + e.getMessage());
        }
    }
}
