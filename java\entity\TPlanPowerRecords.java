package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 功率计划关联记录表
 * 对应Python模型: TPlanPowerRecords
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_plan_power_records")
public class TPlanPowerRecords extends SuperEntity {

    /**
     * 计划历史记录ID
     */
    @TableField("plan_id")
    private Long planId;

    /**
     * 功率下发记录ID
     */
    @TableField("power_id")
    private Long powerId;

    /**
     * 功率计划序号
     */
    @TableField("serial_number")
    private Integer serialNumber;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
