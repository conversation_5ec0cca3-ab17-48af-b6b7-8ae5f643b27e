#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-10-09 09:33:51
#@FilePath     : \RHBESS_Service\Tools\DB\zgtian_bms_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:14:06


#!/usr/bin/env python
# coding=utf-8
#@Information: 中天bms
#<AUTHOR> WYJ
#@Date         : 2023-06-26 17:46:35
#@FilePath     : \RHBESS_Service\Tools\DB\zgtian_his copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-26 17:46:37

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


ZGTIAN_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
ZGTIAN_PORT = model_config.get('mysql', "HIS_PORT")
ZGTIAN_DATABASE1 =  model_config.get('mysql', "ZGTIAN_DATABASE1")
ZGTIAN_DATABASE2 =  model_config.get('mysql', "ZGTIAN_DATABASE2")
ZGTIAN_DATABASE3 =  model_config.get('mysql', "ZGTIAN_DATABASE3")
ZGTIAN_DATABASE4 =  model_config.get('mysql', "ZGTIAN_DATABASE4")
ZGTIAN_USERNAME = model_config.get('mysql', "HIS_USERNAME")
ZGTIAN_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")



hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN_USERNAME,
    ZGTIAN_PASSWORD,
    ZGTIAN_HOSTNAME,
    ZGTIAN_PORT,
    ZGTIAN_DATABASE1
)
zgtian1_bms_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_zgtian1_bms_session = scoped_session(sessionmaker(zgtian1_bms_engine,autoflush=True))
zgtian1_bms_Base = declarative_base(zgtian1_bms_engine)
zgtian1_bms_session = _zgtian1_bms_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN_USERNAME,
    ZGTIAN_PASSWORD,
    ZGTIAN_HOSTNAME,
    ZGTIAN_PORT,
    ZGTIAN_DATABASE2
)
zgtian2_bms_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_zgtian2_bms_session = scoped_session(sessionmaker(zgtian2_bms_engine,autoflush=True))
zgtian2_bms_Base = declarative_base(zgtian2_bms_engine)
zgtian2_bms_session = _zgtian2_bms_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN_USERNAME,
    ZGTIAN_PASSWORD,
    ZGTIAN_HOSTNAME,
    ZGTIAN_PORT,
    ZGTIAN_DATABASE3
)
zgtian3_bms_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_zgtian3_bms_session = scoped_session(sessionmaker(zgtian3_bms_engine,autoflush=True))
zgtian3_bms_Base = declarative_base(zgtian3_bms_engine)
zgtian3_bms_session = _zgtian3_bms_session()

hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN_USERNAME,
    ZGTIAN_PASSWORD,
    ZGTIAN_HOSTNAME,
    ZGTIAN_PORT,
    ZGTIAN_DATABASE4
)
zgtian4_bms_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_zgtian4_bms_session = scoped_session(sessionmaker(zgtian4_bms_engine,autoflush=True))
zgtian4_bms_Base = declarative_base(zgtian4_bms_engine)
zgtian4_bms_session = _zgtian4_bms_session()







