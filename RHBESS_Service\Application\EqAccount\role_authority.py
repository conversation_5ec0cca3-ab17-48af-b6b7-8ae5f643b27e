# -*- coding:utf8 -*-
import json
import logging
from Application.Models.User.role import Role
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.user import User
from Application.Models.User.authority import Authority 
from Application.Models.User.role_authority import RoleAuthority
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import judge_phone,computeMD5
import tornado.web
from sqlalchemy import func


class RoleAuthorityIntetface(BaseHandler):
    '''
    @description: 用户权限管理
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        try:
            if kt == 'GetAll':  # 获取所有权限
                name = self.get_argument('name',None)  # 权限描述
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if DEBUG:
                    logging.info("name:%s,pageNum:%s,pageSize:%s"%(name,pageNum,pageSize))
                all,filter = [],[]

                if name:
                    filter.append(Authority.name.like('%' + name + '%'))
                total = user_session.query(func.count(Authority.id)).filter(*filter).scalar()
                authority = user_session.query(Authority).filter(*filter).order_by(Authority.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                
                for u in authority:
                    all.append(
                        {
                            "id":u.id,
                            "path":u.path,
                            "name":u.name,
                            "redirect":u.redirect,
                            "component":u.component,
                            "parent_id":u.parent_id,
                            "title_":u.title_ ,
                            "icon":u.icon,
                            "hidden":u.hidden,
                            "station_id":u.station_id
                        }
                    )   
                return self.returnTotalSuc(all,total)
               
            elif kt == 'GetAuthorityByUser':  # 获取指定用户权限
                id = self.get_argument('id',None)  # 用户id
                flag = int(self.get_argument('flag',0))  # 0返回用户权限所有信息，1返回用户权限id集合
                if DEBUG:
                    logging.info("id:%s,flag:%s"%(id,flag))
                user = user_session.query(User).filter(User.id==id).first()
                if not user:
                    return self.customError('用户id无效')
                aus = user_session.query(RoleAuthority).filter(RoleAuthority.role_id == user.user_role_id).order_by(RoleAuthority.authority_id.asc()).all()
                all = []
                if flag == 1:  # 非0即为True
                    for u in aus: 
                        all.append(str(u.authority_id))
                else:
                    for u in aus:
                        all.append(eval(str(u)))
                        
                return self.returnTypeSuc(all)
               
            elif kt == 'GetAuthorityByRole':  # 获取指定角色权限
                id = self.get_argument('id',None)  # 角色id
                flag = int(self.get_argument('flag',0))  # 0返回用户权限所有信息，1返回用户权限id集合
                if DEBUG:
                    logging.info("id:%s,flag:%s"%(id,flag))
                user = user_session.query(Role).get(id)
                if not user:
                    return self.customError('角色id无效')
                aus = user_session.query(RoleAuthority).filter(RoleAuthority.role_id == id).all()
                all = []
                if flag == 1:  # 非0即为True
                    for u in aus: 
                        all.append(str(u.authority_id))
                else:
                    for u in aus:
                        all.append(eval(str(u)))
                return self.returnTypeSuc(all)
               
            elif kt == 'GetAuthorityTreeByUser':  # 获取指定用户权限层级结构
                id = self.get_argument('id',None)  # 用户id
                if DEBUG:
                    logging.info("id:%s"%(id))
                if not id:
                    Session = self.getOrNewSession()
                    user = Session.user
                else:
                    user = user_session.query(User).get(id)
                if not user:
                    return self.customError('用户id无效')
               
                aus = user_session.query(Authority).filter(RoleAuthority.role_id == user.user_role_id,RoleAuthority.authority_id==Authority.id,
                Authority.parent_id==None).first()
                all = []
                if aus:
                    obj = {}
                    obj['path'] = aus.path
                    obj['hidden'] = True if aus.hidden == 1 else False
                    obj['name'] = aus.name
                    if aus.redirect:
                        obj['redirect'] = aus.redirect 
                    obj['component'] = aus.component
                    obj['title_'] = aus.title_
                    obj['icon'] = aus.icon
                    # obj['id'] = aus.authority_.id
                    obj['station_id'] = aus.station_id
                    obj['children'] = self.rolechile(aus,user)
                    all.append(obj)
                return self.returnTypeSuc(all)
        
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        try:
            if kt == 'Delete':  # 删除权限
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(Authority).filter(Authority.id==id).first()
                if not delete_apex:
                    return self.customError("id无效")
                user_session.query(RoleAuthority).filter(RoleAuthority.authority_id == id).delete()
                user_session.query(Authority).filter(Authority.id == id).delete()
                user_session.commit()
                return self.returnTypeSuc('')
            
            elif kt == 'Add':  # 添加权限
                hidden = self.get_argument('hidden',None)
                path = self.get_argument('path',None)
                name = self.get_argument('name',None)
                redirect = self.get_argument('redirect',None)
                component = self.get_argument('component',None)
                title_ = self.get_argument('title_',None)
                icon = self.get_argument('icon','')
                parent_id = self.get_argument('parent_id',None)
                station_id = self.get_argument('station_id',[])
            
                if DEBUG:
                    logging.info("hidden:%s,path:%s,name:%s,redirect:%s,component:%s,title_:%s,icon:%s,parent_id:%s,station_id:%s"%(hidden,path,name,redirect,
                    component,title_,icon,parent_id,station_id))
                if not path or not name :
                    return self.customError("路径或名称为空")
                elif user_session.query(Authority).filter(Authority.name == name).first():
                    return self.customError("名称已存在")
                # maxid = user_session.query(func.max(Authority.id)).scalar()
                author = Authority(hidden=hidden,path=path,name=name,redirect=redirect,component=component,title_=title_,icon=icon,parent_id=parent_id,station_id=station_id)
                user_session.merge(author)
                user_session.commit()
                return self.returnTypeSuc('')
                
            elif kt == 'Modify':  # 修改权限信息
                id = self.get_argument('id',None)
                hidden = self.get_argument('hidden',None)
                path = self.get_argument('path',None)
                name = self.get_argument('name',None)
                redirect = self.get_argument('redirect',None)
                component = self.get_argument('component',None)
                title_ = self.get_argument('title_',None)
                icon = self.get_argument('icon','')
                parent_id = self.get_argument('parent_id',None)
                station_id = self.get_argument('station_id',[])
                if DEBUG:
                    logging.info("id:%s,hidden:%s,path:%s,name:%s,redirect:%s,component:%s,title_:%s,icon:%s,parent_id:%s,station_id:%s"%(id,hidden,path,name,
                    redirect,component,title_,icon,parent_id,station_id))
                to_update = user_session.query(Authority).get(id)
            
                if not to_update:
                    return self.customError("未查询到更新节点")
                if not name or not path:
                    return self.customError("路径或名称为空")
                
                to_update.hidden = hidden
                if path:
                    to_update.path = path
                if name:
                    to_update.name = name
                if not redirect or redirect == 'None':
                    redirect = None
                to_update.redirect = redirect
                if not component or component == 'None':
                    component = None
                if component:
                    to_update.component = component
                if title_:
                    to_update.title_ = title_
                if icon:
                    to_update.icon = icon
                if not parent_id or parent_id == 'None':
                    parent_id = None
                to_update.parent_id = parent_id
                if station_id:
                    to_update.station_id = station_id
                user_session.commit()
                return self.returnTypeSuc('')
        
            elif kt == 'ChangeUserAuthority':  # 修改角色权限
                if DEBUG:
                    logging.info( 'body_arguments %s'%self.request.body_arguments)
                id = self.get_argument('id',None)  # id为角色id
                authos = self.get_argument('authos',None)
                
                if DEBUG:
                    logging.info("id:%s,authos:%s"%(id,authos))
            
                to_update = user_session.query(Role).filter(Role.id==id).first()
                if not to_update:
                    return self.customError("id为空或无效")
                user_session.query(RoleAuthority).filter(RoleAuthority.role_id == id).delete()
                if authos:
                    for a in eval(authos):
                        author = RoleAuthority(role_id=id,authority_id=a,op_ts=timeUtils.getNewTimeStr())
                        user_session.add(author)
                user_session.commit()
                return self.returnTypeSuc('')
            
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
       

    def rolechile(self, Bean,user):
        # 递归构建树状权限
        all = []
        auts = user_session.query(Authority).filter(Authority.parent_id==Bean.id,RoleAuthority.role_id==user.user_role_id,
                RoleAuthority.authority_id==Authority.id).order_by(Authority.id.asc()).all()
        
        for u in auts:
            ob = {
                'path' : u.path,
                'hidden' : True if u.hidden == 1 else False,
                'name' : u.name,
                'component': u.component,
                'title_' : u.title_,
                'icon' : u.icon,
                'id':u.id,
                'station_id' : u.station_id,
            }
            if u.redirect:
                ob['redirect'] = u.redirect
            ob['children'] = self.rolechile(u,user)
            all.append(ob)
        return all
