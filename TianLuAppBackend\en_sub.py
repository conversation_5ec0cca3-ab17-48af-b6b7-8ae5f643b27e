import json
import logging
import traceback

from LocaleTool import settings
from LocaleTool.common import TranslateCls, redis_pool, db_tool

# 中文转英文
t_cls = TranslateCls(2)


def zh2en_sub():
    """
    订阅
    :return:
    """
    # 获取中译英翻译数据订阅
    pub = redis_pool.pubsub()
    pub.subscribe('en_translate_pub')
    # 监听
    msg_stream = pub.listen()
    for msg in msg_stream:
        if msg["type"] == "message":
            data = json.loads(msg['data'])
            id = data.get('id')
            table = data.get('table')
            info = data.get('update_data')
            sql = ''
            for k, v in info.items():
                sql += f"{'en_' + k} = {json.dumps(t_cls.str_chinese(v)).replace('%', ' percent')},"
            if sql:
                sql = sql[:-1]
                u_sql = f"UPDATE `{settings.DATABASES['default']['NAME']}`.`{table}` SET {sql} WHERE `id` = {id};"
                try:
                    db_tool.execute_sql(u_sql)
                    print('执行完成:', u_sql)
                except Exception as e:
                    print(f'！！！！执行sql失败:', u_sql)
                    print(traceback.print_exc())
                    logging.error(f'中译英翻译写入数据失败：翻译表：{table}；数据ID：{id}, 错误信息：{e}')
                    # sendMail_(f"您的异步翻译异常，请关注 数据表：{table}；数据ID：{id}, 错误信息：{e}", "异步翻译异常消息通知：中译英", "山海系统", "XXX", user_email)
        elif msg["type"] == "subscribe":
            print(str(msg["channel"]), "订阅成功")


if __name__ == '__main__':
    zh2en_sub()