# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/31 下午3:16
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : local_db_tables.py
# @Software : PyCharm

related_tables = {
    "t_battery_analysis_record": ['note'],
    "t_citys": ['name'],
    "t_compant_info": ['compant_name', 'project_name'],
    "t_counties": ['name'],
    "t_custom_income": ['project', 'station', 'creator', 'updator'],
    "t_dict_model_target": ['name'],
    "t_electricity_province": ['name'],
    "t_fault_alarm_aggr_feedback": ['content', 'question_type', 'note'],
    "t_feedback": ['content'],
    "t_industry": ['name'],
    "t_message_center": ['title', 'opinion'],
    "t_organization": ['name', 'remark'],
    "t_point_cumulant": ['description'],
    "t_point_discrete": ['description'],
    "t_point_measure": ['description'],
    "t_point_status": ['description'],
    "t_power_deliver_records": ['name'],
    "t_price_type": ['name'],
    "t_project": ['name', 'city', 'manager', 'address', 'compant_name'],
    "t_project_pack": ['name'],
    "t_role": ['role_name', 'remark'],
    "t_running_analysis": ['topic', 'keyword', 'ref_threshold'],
    "t_running_analysis_feedback": ['content', 'question_type', 'note'],
    "t_side_register_account": ['address', 'city_counties', 'compant_name'],
    "t_stations": ['station_name', 'address'],
    't_master_stations': ['name'],
    "t_unit": ['unit_name', 'unit_new_name'],
    "t_unit_price": ['name', 'note'],
    "t_user": ['user_name', 'tissue'],
    "t_user_strategy": ['name'],
    "t_user_strategy_apply_history": ['name'],
    "t_user_strategy_apply_history_category_new": ['name'],
    "t_user_strategy_category_new": ['name', 'remark']

}


test_related_tables = {
    "t_citys": ['name']
}

side_related_tables = {
    "t_side_forecase_vol": ['name'],
    "t_side_forecase_ele": ['name'],
    "t_side_forecase_province": ['province'],
}


if __name__ == '__main__':
    for k, v in related_tables.items():
        for item in v:
            sql = f"ALTER TABLE {k} ADD COLUMN {'en_' + item} varchar(256) DEFAULT NULL COMMENT '英文字段';"
            print(sql)
