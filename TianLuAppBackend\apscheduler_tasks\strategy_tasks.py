import datetime
from django.db import connection
from common import common_response_code
from apis.user.models import StationBaseNew, FormerBaseNew, StationActicNew, FormerActicNew, StationDetails, PeakValleyNew, FormerActicNewInfo, FormerBaseNewInfo, ElectricityProvince, ForecasePrice, PriceNewInfo, StationIncome, UnitPrice, StationBaseIncome



def strategy_generate():
    """
    默认、基准、电价策略同步至下一年
    """

    year = datetime.datetime.now().year
    now = datetime.datetime.now()

    # 默认策略
    actic_res = FormerActicNew.objects.filter(year_month__icontains=str(year)).all()
    actic_dict = {i.id: i for i in actic_res}
    sql = f"""SELECT
                s.station_id, GROUP_CONCAT(s.former_actic_id) as ids
            FROM
                `t_station_actic_new` AS s
                INNER JOIN ( SELECT * FROM t_former_actic_new WHERE `year_month` LIKE "{year}%" ) AS f ON f.id = s.former_actic_id
            GROUP BY
                s.station_id"""
    station_list = []

    with connection.cursor() as cursor:
        cursor.execute("""SET SESSION group_concat_max_len = 100000;""")
        cursor.execute(sql)
        res = cursor.fetchall()
        for i in res:
            station_id = i[0]
            former_actic_ids = i[1].split(',')
            actic_list = []

            for y in former_actic_ids:
                y = int(y)
                former = FormerActicNew()
                info = actic_dict.get(y)
                former.year_month = f"{year + 1}-{info.year_month.split('-')[1]}"
                former.day = 1
                former.moment = info.moment
                former.mark = info.mark
                former.power_value = info.power_value
                former.power = info.power
                former.station_id = station_id
                actic_list.append(former)
            FormerActicNew.objects.bulk_create(actic_list)
            ids = FormerActicNew.objects.filter(station_id=station_id, year_month__icontains=str(year + 1)).all()
            for _i in ids:
                station_actic = StationActicNew()
                station_actic.station_id = station_id
                station_actic.former_actic_id = _i.id
                station_list.append(station_actic)

    StationActicNew.objects.bulk_create(station_list)

    # info表补充
    actic_station_res = StationActicNew.objects.filter(create_time__gte=now).values('station_id').distinct()

    actic_station_list = []
    for i in actic_station_res:
        actic_station_list.append(i['station_id'])

    actic_list = []
    name_dict = {}
    for k in actic_station_list:
        station = StationDetails.objects.filter(id=k).first()
        if station:
            name = station.province.name + common_response_code.ConfLevType.TYPE['zh'][
                station.type] + '默认运行策略'
            if name not in name_dict.keys():
                name_dict[name] = {
                    'station_ids': [str(k)],
                    'province': station.province,
                    'ele_type': station.type
                }

            else:
                name_dict[name]['station_ids'].append(str(k))

    for k, v in name_dict.items():
        info = FormerActicNewInfo()
        info.name = k
        info.station_id = ','.join(v['station_ids'])
        info.is_use = 1
        info.stype = 1
        info.year = year + 1
        info.province = v['province']
        info.ele_type = v['ele_type']
        info.file_url = ''
        actic_list.append(info)

    FormerActicNewInfo.objects.bulk_create(actic_list)
    FormerActicNewInfo.objects.filter(year=year).update(is_use=0)

    # # 基准策略
    base_res = FormerBaseNew.objects.filter(year_month__icontains=str(year)).all()
    base_dict = {i.id: i for i in base_res}
    sql = f"""SELECT
                        s.station_id, GROUP_CONCAT(s.former_base_id) as ids
                    FROM
                        `t_station_base_new` AS s
                        INNER JOIN ( SELECT * FROM t_former_base_new WHERE `year_month` LIKE "{year}%" ) AS f ON f.id = s.former_base_id
                    GROUP BY
                        s.station_id"""
    station_list = []

    with connection.cursor() as cursor:
        cursor.execute("""SET SESSION group_concat_max_len = 100000;""")
        cursor.execute(sql)
        res = cursor.fetchall()
        for i in res:
            station_id = i[0]
            former_base_ids = i[1].split(',')
            base_list = []

            for y in former_base_ids:
                y = int(y)
                former = FormerBaseNew()
                info = base_dict.get(y)
                former.year_month = f"{year + 1}-{info.year_month.split('-')[1]}"
                former.day = 1
                former.moment = info.moment
                former.mark = info.mark
                former.power_value = info.power_value
                former.power = info.power
                former.station_id = station_id
                base_list.append(former)
            FormerBaseNew.objects.bulk_create(base_list)
            ids = FormerBaseNew.objects.filter(station_id=station_id, year_month__icontains=str(year + 1)).all()
            for _i in ids:
                station_base = StationBaseNew()
                station_base.station_id = station_id
                station_base.former_base_id = _i.id
                station_list.append(station_base)

    StationBaseNew.objects.bulk_create(station_list)

    # info表补充
    base_station_res = StationBaseNew.objects.filter(create_time__gte=now).values('station_id').distinct()

    base_station_list = []
    for i in base_station_res:
        base_station_list.append(i['station_id'])

    base_list = []
    for k in base_station_list:
        info = FormerBaseNewInfo()
        station = StationDetails.objects.filter(id=k).first()
        if station:
            info.name = station.station_name + station.province.name + common_response_code.ConfLevType.TYPE['zh'][
                station.type] + \
                        common_response_code.ConfLevType.LEVEL['zh'][station.level] + '基准运行策略'
            info.station_id = k
            info.is_use = 1
            info.stype = 1
            info.year = year + 1
            info.file_url = ''
            base_list.append(info)
    FormerBaseNewInfo.objects.bulk_create(base_list)

    # 电价更新
    price_res = PeakValleyNew.objects.filter(year_month__icontains=str(year)).all()
    price_list = []
    for i in price_res:
        info = PeakValleyNew()
        info.year_month = f"{year + 1}-{i.year_month.split('-')[1]}"
        info.day = 1
        info.moment = i.moment
        info.price = i.price
        info.pv = i.pv
        info.type = i.type
        info.province = i.province
        info.level = i.level
        price_list.append(info)

    PeakValleyNew.objects.bulk_create(price_list)

    price_res = PeakValleyNew.objects.filter(year_month__icontains=str(year + 1)).values('province_id', 'type',
                                                                                         'level',
                                                                                         'year_month').distinct()
    price_dict = {}
    price_list = []
    for i in price_res:
        k = f"{i['province_id']}-{i['type']}-{i['level']}"
        if k not in price_dict.keys():
            price_dict[k] = [i['year_month']]
        else:
            price_dict[k].append(i['year_month'])

    for k, v in price_dict.items():
        province_id, ele_type, level = k.split('-')
        for _v in v:
            info = PriceNewInfo()
            info.is_use = 1
            info.stype = 1
            info.province_id = province_id
            info.ele_type = ele_type
            info.level = level
            info.year_month = _v
            info.file_url = ''
            price_list.append(info)
    PriceNewInfo.objects.bulk_create(price_list)

    return 1