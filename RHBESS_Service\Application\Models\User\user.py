#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\user.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-29 14:39:22


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.organization import Organization
from Application.Models.User.role import Role
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.station import Station

class User(user_Base):
    u'用户表'
    __tablename__ = "t_user"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(String(256), nullable=False, comment=u"姓名")
    en_name = Column(String(256), nullable=True, comment=u"姓名")
    sex = Column(CHAR, nullable=True, comment=u"性别")
    phone_no = Column(String(13), nullable=True, comment=u"电话")
    account = Column(String(256), nullable=False, comment=u"登录名")
    passwd = Column(String(256), nullable=True, comment=u"密码")
    email = Column(String(256), nullable=True, comment=u"邮箱")
    organization_id = Column(Integer, ForeignKey("t_organization.id"),nullable=False, comment=u"组织id")
    user_role_id = Column(Integer, ForeignKey("c_user_role.id"),nullable=False, comment=u"角色id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    unregister = Column(CHAR, nullable=False, server_default='1',comment=u"是否有效")
    wx_connect = Column(String(256), nullable=False, comment=u"和微信通信校验标识")
    station_id = Column(Integer, nullable=True, comment=u"所属站")

    organization_U = relationship("Organization", backref="organization_U")
    userRole = relationship("Role", backref="userRole")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(User(id=1,name='超级管理员',sex=0,phone_no='***********',account='root',passwd='e10adc3949ba59abbe56e057f20f883e',
            organization_id=1,user_role_id=1,op_ts=timeUtils.getNewTimeStr(),unregister=1,wx_connect='0eddbec937865010edfa3ed9e5838cdc9602'))
        user_session.merge(User(id=2,name='普通管理员',sex=0,phone_no='***********',account='admin',passwd='e10adc3949ba59abbe56e057f20f883e',
            organization_id=1,user_role_id=1,op_ts=timeUtils.getNewTimeStr(),unregister=1,wx_connect='b04b58f4812bb5e5ebbeed49e96d65731551'))
        user_session.commit()
        user_session.close()

    def __repr__(self):
        o_descr = self.organization_U.descr if self.organization_U else ''
        u_descr = self.userRole.descr if self.userRole else ''
        en_o_descr = self.organization_U.en_descr if self.organization_U else ''
        en_u_descr = self.userRole.en_descr if self.userRole else ''
        # station_name = []
        # for s in eval(str(self.station_id)):
        #     p = user_session.query(Station).filter(Station.id==s).first()
        #     if p:
        #         station_name.append(p.name)
        #     else:
        #         station_name.append('')
        bean = "{'id':%s,'descr':'%s','sex':%s,'phone_no':%s,'account':'%s','passwd':'%s','organization_id':'%s','organization_descr':'%s'," \
               "'user_role_id':'%s','user_role_descr':'%s','op_ts':'%s','charts':'%s','station_id':'%s','name':'%s','email':'%s','en_name':'%s'," \
               "'en_descr': '%s','en_organization_descr': '%s','en_user_role_descr': '%s'}" % (self.id,self.name,self.sex,self.phone_no,
            self.account,self.passwd,self.organization_id,o_descr,self.user_role_id,u_descr,self.op_ts,{},self.station_id,self.name,self.email,self.en_name,
            self.en_name,en_o_descr,en_u_descr)
        return bean.replace("None",'')
    
    def deleteUser(self,id):
        try:
            user_session.query(User).filter(User.id == id).delete()
           
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}