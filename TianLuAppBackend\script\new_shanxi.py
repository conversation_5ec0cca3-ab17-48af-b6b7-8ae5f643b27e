import json
import re
import time
import requests
import execjs
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import base64

"""AES对称加密"""
import base64
from Crypto.Cipher import AES


class EncryptDate:
    def __init__(self, key):
        self.key = key.encode('utf-8')  # 初始化密钥
        self.length = AES.block_size  # 初始化数据块大小，为16位
        self.aes = AES.new(self.key, AES.MODE_ECB)  # 初始化AES,ECB模式的实例
        self.unpad = lambda date: date[0:-ord(date[-1])]  # 截断函数，去除填充的字符

    def pad(self, text):
        '''
        填充函数，使被加密数据的字节码长度是block_size的整数倍
        '''
        count = len(text.encode('utf-8'))
        add = self.length - (count % self.length)
        entext = text + (chr(add) * add)
        return entext

    # 加密函数
    def encrypt(self, encrData):
        res = self.aes.encrypt(self.pad(encrData).encode('utf-8'))
        # Base64是网络上最常见的用于传输8Bit字节码的编码方式之一
        msg = str(base64.b64encode(res), encoding='utf-8')
        return msg

    # 解密函数
    def decrypt(self, decrData):
        res = base64.decodebytes(decrData.encode('utf-8'))  # 转为二进制字节流
        msg = self.aes.decrypt(res).decode('utf-8')
        return self.unpad(msg)  # 把之前为了填充为16位多余的那部分截掉


class YdmVerify(object):
    _custom_url = "http://api.jfbym.com/api/YmServer/customApi"
    _token = "8nf2Ru8y7owJBXmkHW4XTE6GPllMXYgDiODBw7Pmaec"
    _headers = {
        'Content-Type': 'application/json'
    }

    def common_verify(self, image, verify_type="10110"):
        # 数英汉字类型
        # 通用数英1-4位 10110
        # 通用数英5-8位 10111
        # 通用数英9~11位 10112
        # 通用数英12位及以上 10113
        # 通用数英1~6位plus 10103
        # 定制-数英5位~qcs 9001
        # 定制-纯数字4位 193
        # 中文类型
        # 通用中文字符1~2位 10114
        # 通用中文字符 3~5位 10115
        # 通用中文字符6~8位 10116
        # 通用中文字符9位及以上 10117
        # 中文字符 1~4位 plus 10118
        # 定制-XX西游苦行中文字符 10107
        # 计算类型
        # 通用数字计算题 50100
        # 通用中文计算题 50101
        # 定制-计算题 cni 452
        payload = {
            "image": base64.b64encode(image).decode(),
            "token": self._token,
            "type": verify_type
        }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def slide_verify(self, slide_image, background_image, verify_type="20101"):
        # 滑块类型
        # 通用双图滑块  20111
        payload = {
            "slide_image": slide_image,
            "background_image": background_image,
            "token": self._token,
            "type": verify_type
        }

        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        # print(resp.text)
        # print(resp.json())
        return resp.json()['data']['data']

    def sin_slide_verify(self, image, verify_type="20110"):
        # 通用单图滑块(截图)  20110
        payload = {
            "image": base64.b64encode(image).decode(),
            "token": self._token,
            "type": verify_type
        }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def traffic_slide_verify(self, seed, data, href, verify_type="900010"):
        # 定制-滑块协议slide_traffic  900010
        payload = {
            "seed": seed,
            "data": data,
            "href": href,
            "token": self._token,
            "type": verify_type
        }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def click_verify(self, image, extra=None, verify_type="30100"):
        # 通用任意点选1~4个坐标 30009
        # 通用文字点选1(通用,xd;extra,点选文字逗号隔开,原图) 30100
        # 定制-文字点选2(xy3,extra="click",原图) 30103
        # 定制-单图文字点选(xd) 30102
        # 定制-图标点选1(xd,原图) 30104
        # 定制-图标点选2(xy3,原图,extra="icon") 30105
        # 定制-语序点选1(xy3,原图,extra="phrase") 30106
        # 定制-语序点选2(xd,原图) 30107
        # 定制-空间推理点选1(xd,原图,extra="请点击xxx") 30109
        # 定制-空间推理点选1(xy3,原图,extra="请_点击_小尺寸绿色物体。") 30110
        # 定制-tx空间点选(extra="请点击侧对着你的字母") 50009
        # 定制-tt_空间点选 30101
        # 定制-推理拼图1(xd,原图,extra="交换2个图块") 30108
        # 定制-xy4九宫格点选(label_image,image) 30008
        # 点选二字TX  30111
        # 定制-文字点选3(extra="je4_click") 30112
        # 定制-图标点选3(extra="je4_icon") 30113
        # 定制-语序点选3(extra="je4_phrase") 30114
        payload = {
            "image": base64.b64encode(image).decode(),
            "token": self._token,
            "type": verify_type
        }
        if extra:
            payload['extra'] = extra
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def rotate(self, image):
        # 定制-X度单图旋转  90007
        payload = {
            "image": base64.b64encode(image).decode(),
            "token": self._token,
            "type": "90007"
        }
        # 定制-Tt双图旋转,2张图,内圈图,外圈图  90004
        # payload = {
        #     "out_ring_image": base64.b64encode(image).decode(),
        #     "inner_circle_image": base64.b64encode(image).decode(),
        #     "token": self._token,
        #     "type": "90004"
        # }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def google_verify(self, googlekey, pageurl, invisible=1, data_s=""):
        _headers = {
            'Content-Type': 'application/json'
        }
        """
        第一步，创建验证码任务
        :param
        :return taskId : string 创建成功的任务ID
        """
        url = "http://api.jfbym.com/api/YmServer/funnelApi"
        payload = json.dumps({
            "token": self._token,
            "type": "40010",  ## v2
            # "type": "40011", ## v3
            "googlekey": googlekey,
            "enterprise": 0,  ## 是否为企业版
            "pageurl": pageurl,
            "invisible": invisible,
            "data-s": data_s,  ## V2+企业如果能找到，找不到传空字符串
            # 'action':"" #V3必传
            # 'min_score':"" #V3才支持的可选参数
        })
        # 发送JSON格式的数据
        result = requests.request("POST", url, headers=_headers, data=payload).json()
        print(result)
        # {'msg': '识别成功', 'code': 10000, 'data': {'code': 0, 'captchaId': '51436618130', 'recordId': '74892'}}
        captcha_id = result.get('data').get("captchaId")
        record_id = result.get('data').get("recordId")
        times = 0
        is_solved = 0
        while times < 150:
            try:
                url = f"http://api.jfbym.com/api/YmServer/funnelApiResult"
                data = {
                    "token": self._token,
                    "captchaId": captcha_id,
                    "recordId": record_id
                }
                result = requests.post(url, headers=_headers, json=data).json()
                print(result)
                # {'msg': '结果准备中，请稍后再试', 'code': 10009, 'data': []}
                if result['msg'] == "结果准备中，请稍后再试":
                    continue
                if result['msg'] == '请求成功' and result['code'] == 10001:
                    is_solved = 1
                    return result['data']['data']
            except Exception as e:
                print(e)
            finally:
                if is_solved:
                    break
                print("sleep 5s...")
                time.sleep(5)
                times += 5

    def hcaptcha_verify(self, site_key, site_url, verify_type="50001"):
        # 定制类接口-Hcaptcha
        payload = {
            "site_key": site_key,
            "site_url": site_url,
            "token": self._token,
            "type": verify_type
        }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']

    def fun_captcha_verify(self, publickey, pageurl, verify_type="40007"):
        # 定制类接口-Hcaptcha
        payload = {
            "publickey": publickey,
            "pageurl": pageurl,
            "token": self._token,
            "type": verify_type
        }
        resp = requests.post(self._custom_url, headers=self._headers, data=json.dumps(payload))
        print(resp.text)
        return resp.json()['data']['data']


#
headers = {
    "Content-Type": "application/json;charset=UTF-8",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
}  # 构造请求头
#
"""获取图片 及 AES 加密公钥"""
data = {"captchaType": "blockPuzzle"}
url = "https://pmos.sx.sgcc.com.cn/px-common-authority/captcha/get"
request = requests.post(url=url, headers=headers, json=data)
secretKey = request.json()["data"]["repData"].get("secretKey")  # aes公钥
token = request.json()["data"]["repData"].get("token")

back_image_base64 = request.json()["data"]["repData"].get("originalImageBase64")  # 滑块验证码后
front_image_base64 = request.json()["data"]["repData"].get("jigsawImageBase64")  # 滑块验证码前
Y = YdmVerify()
code = Y.slide_verify(front_image_base64, back_image_base64)
print(code)
code = float(code) + 0.57575757575756
request_data = {"x": code, "y": 5}  # 坐标
json_request_data = json.dumps(request_data, separators=(',', ':'))
eg = EncryptDate(secretKey)  # 这里密钥的长度必须是16的倍数
res = eg.encrypt(json_request_data)  # aes加密
next_url = "https://pmos.sx.sgcc.com.cn/px-common-authority/captcha/check"
next_data = {"captchaType": "blockPuzzle", "pointJson": res,
             "token": token}
ins = requests.post(url=next_url, json=next_data, headers=headers)
check_token = ins.json()["data"]["repData"]["token"]  # 与坐标相加构建 captchaVerification 明文
"""构建 captchaVerification 参数"""
captchaVerification_str = check_token + "---" + json_request_data  # 构建captchaVerification字符串
captchaVerification_bs = eg.encrypt(captchaVerification_str)  # captchaVerification  aes加密
"""构建 authKey 参数"""
sm2_js = """
const sm2 = require('sm-crypto').sm2
const cipherMode = 1 // 1 - C1C3C2，0 - C1C2C3，默认为1
function encrypt (value, publicKey) {
	// 给后端传值时需要在加密的密文前面加04 ,这样后端才能解密正确不报错
	return '04' + sm2.doEncrypt(value, publicKey, cipherMode)
}
"""

sm2_entry = execjs.compile(sm2_js)

sm2_url = "https://pmos.sx.sgcc.com.cn/px-common-authority/framework/getSecureKey"
#
sm2_text = requests.post(url=sm2_url, headers=headers)
sm2_json = sm2_text.json()  # 获取sm2加密的公钥
sm2_secureKey = sm2_json["data"]["secureKey"]  # sm2 密码加密的公钥
secureCode = sm2_json["data"]["secureCode"]  # secureCode 参数
authKey = sm2_entry.call("encrypt", "cxjy@300", sm2_secureKey, 0)  # authKey 密文

request_dic = {
    "cookieTicketKey": "Admin-Token",
    "loginName": "ZT00001",
    "username": "ZT00001",
    "authKey": authKey,
    "secureCode": secureCode,
    "dnInfo": "",
    "isCfcaLogin": False,
    "loginFrom": 1,
    "clientTag": "OUTNET_BROWSE",
    "randomCode": "ERWEFX",
    "twoFactorType": 11,
    "captchaVerification":captchaVerification_bs,
    "origin": ""}
login_url = "https://pmos.sx.sgcc.com.cn/px-common-authority/user/login2"

resp = requests.post(url=login_url, json=request_dic, headers=headers)
x_ticket = resp.json()["data"]['data']

headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
get_url = "https://pmos.sx.sgcc.com.cn/px-settlement-infpubquery/spotgoods/queryIpSpotgoodsInfoBytableName"  # 获取sessid获取的 url
get_dic = {"tableName": "ip_ems_load_curv"}
res = requests.post(url=get_url, json=get_dic, headers=headers).json()
session_id_url = "https://pmos.sx.sgcc.com.cn" + res['data'].get("url")
session_id_headers = {
    "Cookie": f"ClientTag=OUTNET_BROWSE;X-Ticket={x_ticket};",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
d = requests.get(url=session_id_url, headers=session_id_headers)
pattern = r"sessionID=([\w-]+)"
session_id = re.search(pattern, d.text).group(1)
check_url = "https://pmos.sx.sgcc.com.cn/px-basesystem-reportform/decision/view/form?op=fr_dialog&cmd=parameters_d"
from urllib import parse
form_data = {"__parameters__": '{"LABEL[65e5][671f]_C":"[65e5][671f]","[65e5][671f]":"2023-06-05"}'}
payload=parse.urlencode(form_data)
session_headers = {
    "Sessionid":session_id,
    "Content-Type":"application/x-www-form-urlencoded",
    "Cookie": f"ClientTag=OUTNET_BROWSE;X-Ticket={x_ticket};",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
z = requests.post(url=check_url, headers=session_headers, data= payload)


new_url = "https://pmos.sx.sgcc.com.cn/px-basesystem-reportform/decision/view/form?op=fr_form&cmd=form_getsource&__chartsourcename__=CHART0&__chartsize__=%7B%22width%22%3A662%2C%22height%22%3A298%7D"
session_id_headers["Sessionid"] = session_id
reqeust_json = {"__parameters__":"{}"}
e = requests.post(url=new_url,headers=session_id_headers,data=reqeust_json)
finally_url = e.json()["items"][0]['url']
finally_headers = {
    "Cookie": f"ClientTag=OUTNET_BROWSE;X-Ticket={x_ticket};",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
last_url = "https://pmos.sx.sgcc.com.cn/px-basesystem-reportform/decision/view/form" + finally_url + '&__time=' + str(int(time.time()*1000))
f = requests.get(url=last_url, headers=session_id_headers)
print(f.text)