import datetime
import json
import logging
import time

from django.conf import settings
from django.db import transaction
from openpyxl import load_workbook
from rest_framework.response import Response
from rest_framework.views import APIView
from apis.app2.utils import paging
from apis.user.models import StationBaseNew, FormerBaseNew, StationActicNew, FormerActicNew, StationDetails, PeakValleyNew, FormerActicNewInfo, FormerBaseNewInfo, ElectricityProvince, ForecasePrice, PriceNewInfo, StationIncome, UnitPrice, StationBaseIncome
from common import common_response_code
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from tools.minio_tool import MinioTool

error_log = settings.ERROR_LOG


class StrategyUploadViews(APIView):
    """策略上传"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        files = request.FILES.get('files')  # 上传附件信息
        t = request.data.get('type')  # 1:默认策略；2：基准策略
        column_title = [i for i in range(1, 13)]  # 列表头
        time_t = request.data.get('stype')  # 1:半小时；2：1小时
        if not t:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '请选择上传策略类型！', "detail": ''}
                }
            )
        if not time_t:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '请选择上传策略时间类型！', "detail": ''}
                }
            )
        error_name = '默认策略' if int(t) == 1 else '基准策略'
        time_t = int(time_t)
        if time_t == 2:  # 一小时模板
            line_title = [i for i in range(24)]  # 行表头
            for_total = 4  # 循环次数
            line_total = 25 if int(t) == 2 else 27  # 行数
        elif time_t == 1:  # 半小时模板
            line_title = [i for i in range(48)]  # 行表头
            for_total = 2  # 循环次数
            line_total = 49 if int(t) == 2 else 51  # 行数
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '请选择上传策略时间类型！', "detail": ''}
                }
            )
        if files:
            file_name = files.name
            size = files.size
            if file_name.split('.')[-1] not in ['xls', 'xlsx']:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": '上传附件格式错误， 请检查后重试！', "detail": ''}
                    }
                )
            if size > 1024 * 1024 * 40:  # 限值文件大小 40MB
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": '上传附件超过40MB限值！', "detail": ''}
                    }
                )
            files.name = datetime.datetime.now().strftime('%y-%m-%d %H:%M:%S') + ' ' + file_name
            minio_client = MinioTool()
            file_url = minio_client.upload_object(files, 'strategy')
            save_id = transaction.savepoint()
            try:
                wb = load_workbook(files, data_only=True)  # 读取附件
                sheet_names = wb.sheetnames
                sheet_count = len(sheet_names)
                station_error_mes = []
                row_error_mes = []
                line_error_mes = []
                error_mes = []
                if t == 2:
                    for name in sheet_names:
                        station = StationDetails.objects.filter(station_name=name, is_delete=0).first()
                        if not station:
                            station_error_mes.append(name)

                    if station_error_mes:  # 并网点错误
                        mes = ''
                        for i in station_error_mes:
                            mes += f'<{i}>与系统中的名字不一致，请检查后上传！\n'
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": mes, "detail": ''}
                            }
                        )

                not_station_detail = None
                for st_ in wb:  # 读取每个sheet页
                    try:
                        save_id = transaction.savepoint()

                        st = wb[st_.title]  # 读取每个sheet页

                        if int(t) == 2:  # 基准策略
                            station = StationDetails.objects.filter(station_name=st_.title, is_delete=0).first()
                            station_id = station.id  # 获取并网点-从站ID
                            year = st['C1'].value.split('-')[0]
                            res = FormerBaseNewInfo.objects.filter(station_id=station_id, is_use=1, year=int(year)).first()
                            if res:
                                res.is_use = 0
                                res.save()

                            info = FormerBaseNewInfo()
                            info.name = st_.title + station.province.name + common_response_code.ConfLevType.TYPE['zh'][station.type] + \
                                   common_response_code.ConfLevType.LEVEL['zh'][station.level] + '基准运行策略'
                            info.en_name = st_.title + station.province.en_name + common_response_code.ConfLevType.TYPE['en'][station.type] + \
                                   common_response_code.ConfLevType.LEVEL['en'][station.level] + 'Benchmark Operation Strategy'
                            info.station = station
                            info.stype = time_t
                            info.file_url = file_url
                            info.year = year
                            info.save()

                            # 校验行和列
                            _column_title = [int(i.value.split('-')[1]) for i in list(st[f'C{1}:N{1}'])[0]]
                            _line_title = [i[0].value for i in list(st[f'A{2}:A{line_total}'])]
                            row = st.max_row  # 总行数
                            if row != line_total:
                                row_error_mes.append(st_.title)
                                transaction.savepoint_rollback(save_id)
                                continue

                            if _column_title != column_title or _line_title != line_title:
                                line_error_mes.append(st_.title)
                                transaction.savepoint_rollback(save_id)
                                continue
                        else:
                            province = st['B2'].value
                            ele_type = st['H2'].value
                            privince_info = ElectricityProvince.objects.filter(name=province).first()
                            ele_type_id = ele_dict.get(ele_type)
                            if not privince_info:
                                error_mes.append(f'<{st_.title}>省份匹配失败！，请检查后上传！\n')
                                continue
                            if not ele_type_id:
                                error_mes.append(f'<{st_.title}>用电类型匹配失败！，请检查后上传！\n')
                                continue
                            year = datetime.datetime.now().year
                            res = FormerActicNewInfo.objects.filter(is_use=1, province_id=privince_info.id, ele_type=ele_type_id).first()

                            if res:
                                res.is_use = 0
                                res.save()
                            station_res = StationDetails.objects.filter(is_delete=0, province_id=privince_info.id, type=ele_type_id).all()
                            station_ids = [str(i.id) for i in station_res]
                            if not station_ids:
                                not_station_detail = '上传默认策略未匹配到并网点！请检查！'
                                break

                            info = FormerActicNewInfo()
                            info.name = privince_info.name + common_response_code.ConfLevType.TYPE['zh'][
                                ele_type_id] + '默认运行策略'
                            info.en_name = privince_info.en_name + \
                                           common_response_code.ConfLevType.TYPE['en'][ele_type_id] + 'Default Running Strategy'
                            info.station_id = ','.join(station_ids)
                            info.stype = time_t
                            info.province = privince_info
                            info.ele_type = ele_type_id
                            info.file_url = file_url
                            info.year = year
                            info.save()

                            # 校验行和列
                            _column_title = [int(i.value.split('月')[0]) for i in list(st[f'C{3}:N{3}'])[0]]
                            _line_title = [i[0].value for i in list(st[f'A{4}:A{line_total}'])]
                            row = st.max_row  # 总行数

                            if row != line_total:
                                row_error_mes.append(st_.title)
                                transaction.savepoint_rollback(save_id)
                                continue

                            if _column_title != column_title or _line_title != line_title:
                                line_error_mes.append(st_.title)
                                transaction.savepoint_rollback(save_id)
                                continue





                        moment = datetime.datetime.strptime('00:00', '%H:%M')
                        create_time = datetime.datetime.now()
                        logging.warning(f'执行{st_.title}')
                        if int(t) == 2:  # 基准策略
                            mode = station.master_station.mode
                            if mode != 3:
                                power = station.rated_power
                                if power == '0':  # 主站，需要计算所有从站功率累加
                                    stations = station.master_station.stationdetails_set.all()
                                    power = sum([int(info.rated_power) for info in stations])
                            else:
                                stations = station.master_station.stationdetails_set.all()
                                power = sum([int(info.rated_power) for info in stations])
                            former_base_ids = StationBaseNew.objects.filter(station_id=station.id).values_list('former_base_id',
                                                                                                                 flat=True)
                            former_base_ids_new = FormerBaseNew.objects.filter(id__in=former_base_ids,
                                                                       year_month__gte=f'{year}-01',
                                                                       year_month__lte=f'{year}-12').values_list('id',
                                                                                                                 flat=True)
                            if former_base_ids_new:
                                ids = [i for i in former_base_ids_new]
                                StationBaseNew.objects.filter(former_base_id__in=ids).delete()
                                FormerBaseNew.objects.filter(id__in=ids).delete()



                            strategy_list = []
                            base_list = []
                            for line in range(3, 15):  # 读取每列
                                year_month = st.cell(1, line).value
                                for column in range(2, line_total + 1):  # 读取每行
                                    value = st.cell(column, line).value
                                    if value < 0:
                                        f = -1  # 充电
                                    elif value > 0:
                                        f = 1  # 放电
                                    else:
                                        f = 0

                                    for _i in range(for_total):
                                        # 1：默认策略；2：基准策略
                                        strategy = FormerBaseNew()
                                        strategy.create_time = create_time
                                        strategy.year_month = year_month
                                        strategy.mark = f
                                        strategy.day = 1
                                        strategy.moment = moment.strftime('%H:%M')
                                        strategy.power_value = abs(value)
                                        strategy.power = power
                                        strategy.station_id = station_id

                                        strategy_list.append(strategy)

                                        moment = moment + datetime.timedelta(minutes=15)
                            FormerBaseNew.objects.bulk_create(strategy_list)
                            base_ids = FormerBaseNew.objects.filter(station_id=station_id,  year_month__gte=f'{year}-01',
                                                                       year_month__lte=f'{year}-12').values_list('id', flat=True)
                            for _id in base_ids:
                                base_list.append(StationBaseNew(station_id=station_id, former_base_id=_id))
                            StationBaseNew.objects.bulk_create(base_list)

                        else:  # 默认策略
                            for station_info in station_res:
                                former_actic_ids = StationActicNew.objects.filter(station_id=station_info.id).values_list(
                                    'former_actic_id',
                                    flat=True)
                                if former_actic_ids:
                                    former_actic_ids = [i for i in former_actic_ids]
                                    FormerActicNew.objects.filter(id__in=former_actic_ids).delete()
                                    StationActicNew.objects.filter(station_id=station_info.id).delete()

                                strategy_list = []
                                actic_list = []
                                # 补充默认运行策略
                                mode = station_info.master_station.mode
                                if mode != 3:
                                    power = station_info.rated_power
                                    if power == '0':  # 主站，需要计算所有从站功率累加
                                        stations = station_info.master_station.stationdetails_set.all()
                                        power = sum([int(info.rated_power) for info in stations])
                                else:
                                    stations = station_info.master_station.stationdetails_set.all()
                                    power = sum([int(info.rated_power) for info in stations])
                                for line in range(3, 15):  # 读取每列
                                    year_month = st.cell(3, line).value.split('月')[0]
                                    for column in range(4, line_total+1):  # 读取每行
                                        value = st.cell(column, line).value
                                        for _i in range(for_total):
                                            # 1：默认策略；2：基准策略
                                            strategy = FormerActicNew()
                                            strategy.create_time = create_time
                                            strategy.year_month = f'{year}-{year_month}' if int(year_month) >= 10 else f'{year}-0{year_month}'
                                            strategy.mark = value
                                            strategy.day = 1
                                            strategy.moment = moment.strftime('%H:%M')
                                            strategy.power_value = power
                                            strategy.power = power
                                            strategy.station_id = station_info.id
                                            strategy_list.append(strategy)
                                            moment = moment + datetime.timedelta(minutes=15)
                                FormerActicNew.objects.bulk_create(strategy_list)
                                actic_ids = FormerActicNew.objects.filter(station_id=station_info.id).values_list('id', flat=True)
                                for _id in actic_ids:
                                    actic_list.append(StationActicNew(station_id=station_info.id, former_actic_id=_id))
                                StationActicNew.objects.bulk_create(actic_list)


                    except Exception as e:
                        logging.error(f'{st_.title}解析模板错误，{e}')
                        error_mes.append(f'<{st_.title}>解析失败！请检查后上传！\n')
                        transaction.savepoint_rollback(save_id)
                    finally:
                        continue

            except Exception as e:
                error_log.error(f'{error_name}录入错误信息：{e}')
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f'{error_name}写入数据失败！', "detail": ''}
                    }
                )

            if not_station_detail:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": not_station_detail, "detail": ''}
                    }
                )

            if row_error_mes:
                mes = ''
                if len(row_error_mes) == sheet_count:
                    code = common_response_code.FIELD_ERROR
                else:
                    code = 10
                for i in row_error_mes:
                    mes += f'<{i}>行数错误，请检查后上传！\n'
                return Response(
                    {
                        "code": code,
                        "data": {"message": mes, "detail": ''}
                    }
                )

            if line_error_mes:
                mes = ''
                if len(line_error_mes) == sheet_count:
                    code = common_response_code.FIELD_ERROR
                else:
                    code = 10
                for i in line_error_mes:
                    mes += f'<{i}>模板行标题/列标题错误，请检查后上传！\n'
                return Response(
                    {
                        "code": code,
                        "data": {"message": mes, "detail": ''}
                    }
                )

            if error_mes:
                mes = ''
                if len(error_mes) == sheet_count:
                    code = common_response_code.FIELD_ERROR
                else:
                    code = 10
                for i in error_mes:
                    mes += i
                return Response(
                    {
                        "code": code,
                        "data": {"message": mes, "detail": ''}
                    }
                )

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": ''}
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "请上传附件！", "detail": ''}
                }
            )




vol_dict = {
    '不满1千伏': 1,
    '不满1干伏': 1,
    '1一10千伏': 2,
    '1-10干伏': 2,
    '1-10千伏': 2,
    '35千伏': 3,
    '110千伏': 4,
    '220千伏及以上': 5,
    '220干伏及以上': 5,
    '35-110千伏（不含）': 6,
    '110-220千伏（不含）': 7,
    '35千伏及以上': 8,
    '35-110千伏以下': 9,
    '35-110干伏以下': 9,
    '110-220千伏以下': 10,
    '110-220千伏以\n下': 10,
    '110-220干伏以下': 10,
    '110（66）千伏': 11,
    '20千伏': 12,
    '66千伏': 13,
    '220千伏': 14,
    '110千伏及以上': 15,
    '20-35千伏以下': 16,
    '110-330千伏': 17,
    '330千伏及以上': 18,
    '35-110千伏': 19,
    '10 (20)千伏': 20,
    '35千伏以下': 21,
    '10千伏': 22,
    '1-10 (20)千伏': 23,
    '1-10（20）千伏': 23,
    '220(330)千伏': 24,
    '100千伏安及以下和公变接入用电': 25,
    '10千伏高供低计（380V/220V计量）': 26,
    '10千伏高供高计': 27
}

ele_dict = {
    '单一制': 5,
    '两部制': 6,
    '一般工商业及其他用电': 2,
    '大工业用电': 1,
    '一般工商业及其他用电（单一制）': 3,
    '大工业用电（两部制）': 4,
    '100千伏安以下(单一制,含行政事业单位办公场所用电)': 10,
    '单一制(100kVA以下)': 10,
    '100千伏安及以上(两部制)大工业': 8,
    '两部制(大工业100kVA以上)': 8,
    '两部制(非大工业100kVA以上)': 9,
    '大量工商业及其他用电（250kWh及以下/千伏安•月）': 11,
    '大量工商业及其他用电（250kWh及以上/千伏安•月）': 12,
    '高需求工商业及其他用电（400kWh及以下/千伏安•月）': 13,
    '高需求工商业及其他用电（400kWh及以上/千伏安•月）': 14,
    '一般工商业及其他用电(单一制)': 3,
    '一般工商业及其他用电(两部制)': 7,
    '一般工商业（两部制）': 7,
    '大工业用电(两部制)': 4,
    '单一制一般工商业用电': 3,
    '两部制大工业用电': 4,
    '两部制一般工商业用电': 7,
    '一般工商业': 2,
    '大工业': 1,
    '大工业（两部制）': 4,
    '单一制工商业': 3,
    '两部制工商业': 7,
    '充换电站工商业用户': 15,
    '海南电动车': 16,
    '电动汽车': 16,
    '一般工商业（单一制）': 3,

}

province_dict = {
    '安徽': 13,
    '福建': 14,
    '甘肃': 22,
    '广东（珠三角五市）': 38,
    '广东（恵州市）': 39,
    '广东（江门市）': 40,
    '广东（东西两翼地区）': 41,
    '广东（粤北山区）': 42,
    '河南': 16,
    '天津': 2,
    '重庆': 19,
    '湖北': 17,
    '湖南': 18,
    '蒙西': 32,
    '海南': 29,
    '江苏': 11,
    '深圳市': 44,
    '深汕特别合作区': 45,
    '上海': 10,
    '浙江': 12,
    '内蒙古（东部）': 6,
    '辽宁': 7,
    '黑龙江': 9,
    '河北（南网）': 3,
    '广西': 28,
    '贵州': 30,
    '吉林': 8,
    '江西': 15,
    '云南': 31,
    '宁夏': 24,
    '青海': 23,
    '陕西（陕西电网）': 36,
    '陕西（榆林电网）': 37,
    '山西': 4,
    '新疆': 25,
    '河北（冀北）': 43,
    '四川': 20,
    '山东': 5,
    '北京': 1
}


class PriceUploadViews(APIView):
    """
    电价附件上传
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        files = request.FILES.get('files')  # 上传电价附件信息
        months = request.data.get('year_month')  # 上传月份
        stype = request.data.get('stype')  # 1:半小时,2:1小时

        if files and stype:
            file_name = files.name
            size = files.size
            if file_name.split('.')[-1] not in ['xls', 'xlsx']:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": '上传附件格式错误， 请检查后重试！', "detail": ''}
                    }
                )
            if size > 1024 * 1024 * 40:  # 限值文件大小 40MB
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": '上传附件超过40MB限值！', "detail": ''}
                    }
                )
            files.name = datetime.datetime.now().strftime('%y-%m-%d %H:%M:%S') + ' ' + file_name
            minio_client = MinioTool()
            file_url = minio_client.upload_object(files, 'strategy')
            if months:
                months = months.split(',')
            save_id = transaction.savepoint()
            try:
                wb = load_workbook(files, data_only=True)  # 读取附件
                st = wb.active  # sheet页
                if int(stype) == 1:  # 半小时
                    _column_title = [i.value for i in list(st[f'B{2}:CW{2}'])[0]]
                    column_title = ['year_month', 'h0', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'h7', 'h8', 'h9', 'h10',
                                    'h11', 'h12',
                                    'h13', 'h14', 'h15', 'h16', 'h17', 'h18', 'h19', 'h20', 'h21', 'h22', 'h23', 'h24',
                                    'h25',
                                    'h26', 'h27', 'h28', 'h29', 'h30', 'h31', 'h32', 'h33', 'h34', 'h35', 'h36', 'h37',
                                    'h38',
                                    'h39', 'h40', 'h41', 'h42', 'h43', 'h44', 'h45', 'h46', 'h47', 'pv0', 'pv1', 'pv2',
                                    'pv3',
                                    'pv4', 'pv5', 'pv6', 'pv7', 'pv8', 'pv9', 'pv10', 'pv11', 'pv12', 'pv13', 'pv14',
                                    'pv15',
                                    'pv16', 'pv17', 'pv18', 'pv19', 'pv20', 'pv21', 'pv22', 'pv23', 'pv24', 'pv25',
                                    'pv26', 'pv27',
                                    'pv28', 'pv29', 'pv30', 'pv31', 'pv32', 'pv33', 'pv34', 'pv35', 'pv36', 'pv37',
                                    'pv38', 'pv39',
                                    'pv40', 'pv41', 'pv42', 'pv43', 'pv44', 'pv45', 'pv46', 'pv47', 'province_id',
                                    'ele_id',
                                    'vol_id']  # 列表头
                else:
                    _column_title = [i.value for i in list(st[f'B{2}:BA{2}'])[0]]
                    column_title = ['year_month', 'h0', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'h7', 'h8', 'h9',
                                         'h10', 'h11', 'h12',
                                         'h13', 'h14', 'h15', 'h16', 'h17', 'h18', 'h19', 'h20', 'h21', 'h22', 'h23',
                                         'pv0', 'pv1',
                                         'pv2', 'pv3',
                                         'pv4', 'pv5', 'pv6', 'pv7', 'pv8', 'pv9', 'pv10', 'pv11', 'pv12', 'pv13',
                                         'pv14', 'pv15',
                                         'pv16', 'pv17', 'pv18', 'pv19', 'pv20', 'pv21', 'pv22', 'pv23', 'province_id',
                                         'ele_id',
                                         'vol_id']  # 列表头
                if len(_column_title) != len(column_title):
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": f'模板行标题错误，请检查！', "detail": ''}
                        }
                    )
                else:
                    for i in range(len(column_title)):
                        if column_title[i] != _column_title[i]:
                            return Response(
                                {
                                    "code": common_response_code.FIELD_ERROR,
                                    "data": {"message": f'模板行标题第{i}列名称错误，正确名称为：{column_title[i]}！', "detail": ''}
                                }
                            )
                print('模板校验完成')
                row_total = st.max_row  # 行数
                price_obj_list = []
                side_price_obj_list = []
                price_info_list = []
                if int(stype) == 1:   # 半小时
                    # 写入天禄/情报中心
                    for row in range(3, row_total + 1):  # 读取每行
                        year_month = st.cell(row, 2).value
                        if not months or year_month in months:
                            print(f'执行第{row}行---------')
                            moment = datetime.datetime.strptime('00:00', '%H:%M')
                            _type = ele_dict.get(st[f'CV{row}'].value)
                            level = vol_dict.get(st[f'CW{row}'].value)
                            province_id = province_dict.get(st[f'CU{row}'].value)
                            p = PriceNewInfo.objects.filter(province_id=province_id, ele_type=_type, level=level, year_month=year_month, is_use=1).first()
                            if p:
                                p.is_use = 0
                                p.save()

                            price_info = PriceNewInfo()
                            price_info.province_id = province_id
                            price_info.stype = int(stype)
                            price_info.ele_type = _type
                            price_info.level = level
                            price_info.year_month = year_month
                            price_info.file_url = file_url
                            price_info_list.append(price_info)


                            PeakValleyNew.objects.filter(year_month=year_month, type=_type, level=level,
                                                         province_id=province_id).delete()
                            ForecasePrice.objects.using('dm_user').filter(year_month=year_month, ele_id=_type,
                                                                          vol_id=level,
                                                                          province_id=province_id).delete()
                            side_dict = {}

                            for column in range(48):  # 读取每列
                                price = round(float(st.cell(row, column+3).value), 6)
                                pv = int(st.cell(row, column+51).value)
                                if column % 2 == 0:
                                    side_dict[f'h{int(column/2)}'] = price
                                    side_dict[f'p{int(column/2)}'] = pv
                                for i in range(2):  # 半小时数据拆分15分钟
                                    _moment = moment.strftime('%H:%M')
                                    info = PeakValleyNew()
                                    info.year_month = year_month
                                    info.moment = _moment
                                    info.type = _type
                                    info.level = level
                                    info.province_id = province_id
                                    info.price = price
                                    info.pv = pv
                                    info.day = 1
                                    price_obj_list.append(info)
                                    if len(price_obj_list) == 1152:
                                        PeakValleyNew.objects.bulk_create(price_obj_list)
                                        price_obj_list = []

                                    moment = moment + datetime.timedelta(minutes=15)

                            side = ForecasePrice()
                            side.year_month = year_month
                            side.ele_id = _type
                            side.vol_id = level
                            side.province_id = province_id
                            side.part_id = 1
                            side.h0 = side_dict['h0']
                            side.h1 = side_dict['h1']
                            side.h2 = side_dict['h2']
                            side.h3 = side_dict['h3']
                            side.h4 = side_dict['h4']
                            side.h5 = side_dict['h5']
                            side.h6 = side_dict['h6']
                            side.h7 = side_dict['h7']
                            side.h8 = side_dict['h8']
                            side.h9 = side_dict['h9']
                            side.h10 = side_dict['h10']
                            side.h11 = side_dict['h11']
                            side.h12 = side_dict['h12']
                            side.h13 = side_dict['h13']
                            side.h14 = side_dict['h14']
                            side.h15 = side_dict['h15']
                            side.h16 = side_dict['h16']
                            side.h17 = side_dict['h17']
                            side.h18 = side_dict['h18']
                            side.h19 = side_dict['h19']
                            side.h20 = side_dict['h20']
                            side.h21 = side_dict['h21']
                            side.h22 = side_dict['h22']
                            side.h23 = side_dict['h23']
                            side.pv0 = side_dict['p0']
                            side.pv1 = side_dict['p1']
                            side.pv2 = side_dict['p2']
                            side.pv3 = side_dict['p3']
                            side.pv4 = side_dict['p4']
                            side.pv5 = side_dict['p5']
                            side.pv6 = side_dict['p6']
                            side.pv7 = side_dict['p7']
                            side.pv8 = side_dict['p8']
                            side.pv9 = side_dict['p9']
                            side.pv10 = side_dict['p10']
                            side.pv11 = side_dict['p11']
                            side.pv12 = side_dict['p12']
                            side.pv13 = side_dict['p13']
                            side.pv14 = side_dict['p14']
                            side.pv15 = side_dict['p15']
                            side.pv16 = side_dict['p16']
                            side.pv17 = side_dict['p17']
                            side.pv18 = side_dict['p18']
                            side.pv19 = side_dict['p19']
                            side.pv20 = side_dict['p20']
                            side.pv21 = side_dict['p21']
                            side.pv22 = side_dict['p22']
                            side.pv23 = side_dict['p23']
                            side_price_obj_list.append(side)

                else:  # 一小时
                    # 写入天禄\情报中心
                    for row in range(3, row_total + 1):  # 读取每行
                        year_month = st[f'B{row}'].value
                        if not months or year_month in months:
                            moment = datetime.datetime.strptime('00:00', '%H:%M')
                            _type = ele_dict.get(st[f'AZ{row}'].value)
                            level = vol_dict.get(st[f'BA{row}'].value)
                            province_id = province_dict.get(st[f'AY{row}'].value)

                            p = PriceNewInfo.objects.filter(province_id=province_id, ele_type=_type, level=level,
                                                            year_month=year_month, is_use=1).first()
                            if p:
                                p.is_use = 0
                                p.save()

                            price_info = PriceNewInfo()
                            price_info.province_id = province_id
                            price_info.stype = int(stype)
                            price_info.ele_type = _type
                            price_info.level = level
                            price_info.year_month = year_month
                            price_info.file_url = file_url
                            price_info_list.append(price_info)

                            PeakValleyNew.objects.filter(year_month=year_month, type=_type, level=level,
                                                         province_id=province_id).delete()
                            ForecasePrice.objects.using('dm_user').filter(year_month=year_month, ele_id=_type,
                                                                          vol_id=level,
                                                                          province_id=province_id).delete()
                            side_dict = {}
                            print(f'执行第{row}行---------')

                            for column in range(24):  # 读取列
                                price = round(float(st.cell(row, column+3).value), 6)
                                pv = int(st.cell(row, column+27).value)
                                side_dict[f'h{column}'] = price
                                side_dict[f'p{column}'] = pv
                                for i in range(4):  # 1小时数据拆分15分钟
                                    _moment = moment.strftime('%H:%M')
                                    info = PeakValleyNew()
                                    info.year_month = year_month
                                    info.moment = _moment
                                    info.type = _type
                                    info.level = level
                                    info.province_id = province_id
                                    info.price = price
                                    info.pv = pv
                                    info.day = 1
                                    price_obj_list.append(info)
                                    if len(price_obj_list) == 1152:
                                        PeakValleyNew.objects.bulk_create(price_obj_list)
                                        price_obj_list = []


                                    moment = moment + datetime.timedelta(minutes=15)


                            side = ForecasePrice()
                            side.year_month = year_month
                            side.ele_id = _type
                            side.vol_id = level
                            side.province_id = province_id
                            side.part_id = 1
                            side.h0 = side_dict['h0']
                            side.h1 = side_dict['h1']
                            side.h2 = side_dict['h2']
                            side.h3 = side_dict['h3']
                            side.h4 = side_dict['h4']
                            side.h5 = side_dict['h5']
                            side.h6 = side_dict['h6']
                            side.h7 = side_dict['h7']
                            side.h8 = side_dict['h8']
                            side.h9 = side_dict['h9']
                            side.h10 = side_dict['h10']
                            side.h11 = side_dict['h11']
                            side.h12 = side_dict['h12']
                            side.h13 = side_dict['h13']
                            side.h14 = side_dict['h14']
                            side.h15 = side_dict['h15']
                            side.h16 = side_dict['h16']
                            side.h17 = side_dict['h17']
                            side.h18 = side_dict['h18']
                            side.h19 = side_dict['h19']
                            side.h20 = side_dict['h20']
                            side.h21 = side_dict['h21']
                            side.h22 = side_dict['h22']
                            side.h23 = side_dict['h23']
                            side.pv0 = side_dict['p0']
                            side.pv1 = side_dict['p1']
                            side.pv2 = side_dict['p2']
                            side.pv3 = side_dict['p3']
                            side.pv4 = side_dict['p4']
                            side.pv5 = side_dict['p5']
                            side.pv6 = side_dict['p6']
                            side.pv7 = side_dict['p7']
                            side.pv8 = side_dict['p8']
                            side.pv9 = side_dict['p9']
                            side.pv10 = side_dict['p10']
                            side.pv11 = side_dict['p11']
                            side.pv12 = side_dict['p12']
                            side.pv13 = side_dict['p13']
                            side.pv14 = side_dict['p14']
                            side.pv15 = side_dict['p15']
                            side.pv16 = side_dict['p16']
                            side.pv17 = side_dict['p17']
                            side.pv18 = side_dict['p18']
                            side.pv19 = side_dict['p19']
                            side.pv20 = side_dict['p20']
                            side.pv21 = side_dict['p21']
                            side.pv22 = side_dict['p22']
                            side.pv23 = side_dict['p23']

                            side_price_obj_list.append(side)


                if price_obj_list:
                    PeakValleyNew.objects.bulk_create(price_obj_list)

                PriceNewInfo.objects.bulk_create(price_info_list)
                ForecasePrice.objects.using('dm_user').bulk_create(side_price_obj_list)


                transaction.savepoint_commit(save_id)

            except Exception as e:
                error_log.error(f'电价信息录入错误信息：{e}')
                transaction.savepoint_rollback(save_id)
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": '电价写入数据失败！', "detail": ''}
                    }
                )

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": ''}
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "请上传附件！", "detail": ''}
                }
            )



class StrategyListViews(APIView):
    """
    默认/基准策略列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        province_id = request.query_params.get('province_id')  # 省份ID
        ele_type = request.query_params.get('ele_type')   # 用电类型
        _type = request.query_params.get('type', 2)  # 1：默认策略；2：基准策略
        station_id = request.query_params.get('station_id')  # 并网点id
        year = request.query_params.get('year')  # 年份
        page = int(request.query_params.get("pageNum", 1))
        page_size = int(request.query_params.get("pageSize", 10))
        lang = request.headers.get("lang", 'zh')
        user_id = request.user['user_id']
        # 基准策略
        if int(_type) == 2:
            q = FormerBaseNewInfo.objects.filter(is_use=1)

            if station_id:
                station_id = station_id.split(',')
                station_ids = StationDetails.objects.filter(userdetails__id=user_id, id__in=station_id)
            else:
                station_ids = StationDetails.objects.filter(userdetails__id=user_id)
            q = q.filter(station_id__in=station_ids)
            if year:
                q = q.filter(year=int(year))
            res = q.order_by('-id').all()
            page_res = paging(page, page_size, res)
            data = []
            for i in page_res.get('data'):
                if i.stype == 2:
                    strategy_name = '一小时' if lang == 'zh' else 'AN HOUR'
                else:
                    strategy_name = '半小时' if lang == 'zh' else 'HALF AN HOUR'

                data.append({
                    'id': i.id,
                    'name': i.name if lang == 'zh' else i.en_name,
                    # 'province_name': i.station.province.name if lang == 'zh' else i.station.province.en_name,
                    # 'ele_type_name': common_response_code.ConfLevType.TYPE[lang][i.ele_type],
                    'stype': strategy_name,
                    'station_name': i.station.station_name if lang == 'zh' else i.station.en_station_name,
                    'year': i.year
                })
        else:
            q = FormerActicNewInfo.objects.filter(is_use=1)
            if province_id:
                q = q.filter(province_id=int(province_id))
            if ele_type:
                q = q.filter(ele_type=int(ele_type))


            res = q.order_by('-id').all()
            page_res = paging(page, page_size, res)
            data = []
            for i in page_res.get('data'):
                if i.stype == 2:
                    strategy_name = '一小时' if lang == 'zh' else 'AN HOUR'
                else:
                    strategy_name = '半小时' if lang == 'zh' else 'HALF AN HOUR'

                data.append({
                    'id': i.id,
                    'name': i.name if lang == 'zh' else i.en_name,
                    'province_name': i.province.name if lang == 'zh' else i.province.en_name,
                    'ele_type_name': common_response_code.ConfLevType.TYPE[lang][i.ele_type],
                    'stype': strategy_name

                })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data, "total": page_res.get('total'), "page": page_res.get('page'), "page_size": page_size}
            }
        )


class StrategyDownloadViews(APIView):
    """
    默认/基准策略模板下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        _type = request.query_params.get('type')  # 1：默认策略；2：基准策略
        stype = request.query_params.get('stype')   # 1:半小时；2：1小时
        if not _type or not stype:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '请选择下载模板类型！', "detail": ''}
                }
            )
        if int(_type) == 2:
            if int(stype) == 1:
                name = '基准策略模板-半小时.xlsx'
            else:
                name = '基准策略模板-1小时.xlsx'
        else:
            if int(stype) == 1:
                name = '默认策略模板-半小时.xlsx'
            else:
                name = '默认策略模板-1小时.xlsx'

        minio_client = MinioTool()
        file_path = minio_client.get_download_url('strategy', name)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )

class StrategyInfoViews(APIView):
    """
    默认/基准策略详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        _type = request.query_params.get('type')  # 1：默认策略；2：基准策略
        stype = request.query_params.get('stype')   # 1:半小时；2：1小时
        id = request.query_params.get('id')
        _time = request.query_params.get('time')   # 查询时间段

        if not all([_type, stype, id]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '参数不完整，请检查参数！', "detail": ''}
                }
            )
        moment_list = []
        if _time:
            for i in _time.split(','):
                for y in [':00', ':15', ':30', ':45']:
                    moment_list.append(f'0{i}{y}' if int(i) < 10 else f'{i}{y}')

        data = {
            'time': []
        }
        for i in range(1, 13):
            data[f'm{i}'] = []
            data[f'pv{i}'] = []

        interval = 2 if int(stype) == 1 else 4
        if int(_type) == 2:
            res = FormerBaseNewInfo.objects.filter(is_use=1, id=id, stype=int(stype)).first()
            former_base_ids = StationBaseNew.objects.filter(station_id=res.station_id).all()
            former_base_ids = [i.former_base_id for i in former_base_ids]
            if res:
                if moment_list:
                    base_res = FormerBaseNew.objects.filter(id__in=former_base_ids, year_month__contains=res.year, moment__in=moment_list).order_by('year_month', 'moment').all()
                else:
                    base_res = FormerBaseNew.objects.filter(id__in=former_base_ids, year_month__contains=res.year).order_by('year_month', 'moment').all()
                for base in base_res[::interval]:
                    y = int(base.year_month.split('-')[-1])
                    if y == 1:
                        data['time'].append(base.moment)
                    data[f'm{y}'].append(round(base.power_value * base.mark,2))
                station = res.station
                pv_res = PeakValleyNew.objects.filter(province=station.province, type=station.type, level=station.level, year_month__contains=res.year).order_by('year_month', 'moment').all()
                if pv_res:
                    for pv in pv_res[::interval]:
                        y = int(pv.year_month.split('-')[-1])
                        data[f'pv{y}'].append(pv.pv)

        else:
            res = FormerActicNewInfo.objects.filter(is_use=1, id=id, stype=int(stype)).first()
            if res:
                year = datetime.datetime.now().year
                station_id = res.station_id.split(',')[0]
                former_actic_ids = StationActicNew.objects.filter(station_id=station_id).all()
                former_actic_ids = [i.former_actic_id for i in former_actic_ids]
                if moment_list:
                    actic_res = FormerActicNew.objects.filter(id__in=former_actic_ids, year_month__contains=year, moment__in=moment_list).order_by('year_month', 'moment').all()
                else:
                    actic_res = FormerActicNew.objects.filter(id__in=former_actic_ids, year_month__contains=year).order_by('year_month', 'moment').all()
                for actic in actic_res[::interval]:
                    y = int(actic.year_month.split('-')[-1])
                    if y == 1:
                        data['time'].append(actic.moment)
                    data[f'm{y}'].append(actic.mark)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class StrategyUpdateViews(APIView):
    """
    默认/基准策略更新
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        id = request.data.get('id')
        _type = request.data.get('type')  # 1：默认策略；2：基准策略
        update_list = request.data.get('update_list')
        if not id or not type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '参数不完整，请检查参数！', "detail": ''}
                }
            )
        if update_list:
            update_list = eval(update_list)
        _list = []
        if int(_type) == 1:
            info = FormerActicNewInfo.objects.filter(id=id).first()
            year = datetime.datetime.now().year
            if info:
                cycle_num = 4 if info.stype == 2 else 2
                for station_id in info.station_id.split(','):
                    for i in update_list:
                        year_month = f"{year}-{i['month']}" if int(i['month']) >= 10 else f"{year}-0{i['month']}"
                        _moment = datetime.datetime.strptime(i['moment'], '%H:%M')
                        moment_list = []
                        for c_num in range(cycle_num):
                            moment = _moment + datetime.timedelta(minutes=c_num * 15)
                            moment_list.append(moment.strftime('%H:%M'))
                        former_actic_ids = StationActicNew.objects.filter(station_id=station_id).all()
                        former_actic_ids = [i.former_actic_id for i in former_actic_ids]
                        res = FormerActicNew.objects.filter(id__in=former_actic_ids, moment__in=moment_list,
                                                            year_month=year_month).all()
                        if not res:
                            return Response(
                                {
                                    "code": common_response_code.FIELD_ERROR,
                                    "data": {"message": '参数错误查询失败，请检查参数！', "detail": ''}
                                }
                            )
                        for r in res:
                            r.mark = i['value']
                            _list.append(r)
            FormerActicNew.objects.bulk_update(_list, ['mark'])
        else:
            info = FormerBaseNewInfo.objects.filter(id=id).first()
            if info:
                cycle_num = 4 if info.stype == 2 else 2
                for i in update_list:
                    year_month = f"{info.year}-{i['month']}" if int(i['month']) >= 10 else f"{info.year}-0{i['month']}"
                    _moment = datetime.datetime.strptime(i['moment'], '%H:%M')
                    moment_list = []
                    if i['value'] > 0:
                        mark = 1
                    elif i['value'] == 0:
                        mark = 0
                    else:
                        mark = -1
                    for c_num in range(cycle_num):
                        moment = _moment + datetime.timedelta(minutes=c_num * 15)
                        moment_list.append(moment.strftime('%H:%M'))
                    former_base_ids = StationBaseNew.objects.filter(station_id=info.station_id).all()
                    former_base_ids = [i.former_base_id for i in former_base_ids]
                    res = FormerBaseNew.objects.filter(id__in=former_base_ids, moment__in=moment_list,
                                                        year_month=year_month).all()
                    if not res:
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": '参数错误查询失败，请检查参数！', "detail": ''}
                            }
                        )
                    for r in res:
                        r.mark = mark
                        r.power_value = abs(i['value'])
                        _list.append(r)

            FormerBaseNew.objects.bulk_update(_list, ['mark', 'power_value'])
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": ''}
            }
        )


class StrategyDelViews(APIView):
    """
    默认/基准策略删除
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        _type = request.query_params.get('type')  # 1：默认策略；2：基准策略
        id = request.query_params.get('id')
        if not id or not _type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '参数不完整，请检查参数！', "detail": ''}
                }
            )
        if int(_type) == 1:
            info = FormerActicNewInfo.objects.filter(id=id, is_use=1).first()
            station_ids = info.station_id.split(',')
            former_actic_res = StationActicNew.objects.filter(station_id__in=station_ids).all()
            former_actic_ids = [i.former_actic_id for i in former_actic_res]
            FormerActicNew.objects.filter(id__in=former_actic_ids).delete()
            former_actic_res.delete()
            info.is_use = 0
            info.save()
        else:
            info = FormerBaseNewInfo.objects.filter(id=id, is_use=1).first()
            station_id = info.station_id
            former_base_res = StationBaseNew.objects.filter(station_id=station_id).all()
            former_base_ids = [i.former_base_id for i in former_base_res]
            res = FormerBaseNew.objects.filter(id__in=former_base_ids, year_month__icontains=info.year).all()
            ids = [i.id for i in res]
            StationBaseNew.objects.filter(station_id=station_id, former_base_id__in=ids).delete()
            res.delete()
            info.is_use = 0
            info.save()
            
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": ""}
            }
        )


class PriceDownloadViews(APIView):
    """
    电价模板下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        stype = request.query_params.get('stype')   # 1:半小时；2：1小时
        if not stype:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '请选择下载模板类型！', "detail": ''}
                }
            )

        if int(stype) == 1:
            name = '电价上传模板-半小时.xlsx'
        else:
            name = '电价上传模板-1小时.xlsx'


        minio_client = MinioTool()
        file_path = minio_client.get_download_url('strategy', name)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )


class PriceListViews(APIView):
    """
    电价列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        province_id = request.query_params.get('province_id')  # 省份ID
        ele_type = request.query_params.get('ele_type')   # 用电类型
        level = request.query_params.get('level')  # 用电等级
        page = int(request.query_params.get("pageNum", 1))
        page_size = int(request.query_params.get("pageSize", 10))
        lang = request.headers.get("lang", 'zh')
        # 基准策略

        q = PriceNewInfo.objects.filter(is_use=1)
        if province_id:
            q = q.filter(province_id=int(province_id))
        if ele_type:
            q = q.filter(ele_type=int(ele_type))
        if level:
            q = q.filter(level=int(level))


        res = q.order_by('-id').all()
        page_res = paging(page, page_size, res)
        data = []
        for i in page_res.get('data'):
            if i.stype == 2:
                strategy_name = '一小时' if lang == 'zh' else 'AN HOUR'
            else:
                strategy_name = '半小时' if lang == 'zh' else 'HALF AN HOUR'
            data.append({
                'id': i.id,
                'province_name': i.province.name if lang == 'zh' else i.province.en_name,
                'ele_type_name': common_response_code.ConfLevType.TYPE[lang][i.ele_type],
                'level_name': common_response_code.ConfLevType.LEVEL[lang][i.level],
                'stype': strategy_name,
                'year_month': i.year_month
            })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data, "total": page_res.get('total'), "page": page_res.get('page'), "page_size": page_size}
            }
        )


class PriceInfoViews(APIView):
    """
    电价详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        stype = request.query_params.get('stype')   # 1:半小时；2：1小时
        id = request.query_params.get('id')
        interval = 2 if int(stype) == 1 else 4
        if not all([stype, id]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '参数不完整，请检查参数！', "detail": ''}
                }
            )
        data = {
            'time': [],
            'price': [],
            'pv': []
        }
        res = PriceNewInfo.objects.filter(is_use=1, id=id, stype=int(stype)).first()
        if res:
            price_res = PeakValleyNew.objects.filter(province=res.province, type=res.ele_type, level=res.level, year_month=res.year_month).order_by('moment').all()
            for price in price_res[::interval]:
                data['time'].append(price.moment)
                data['price'].append(round(price.price,4))
                data['pv'].append(price.pv)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )

class PriceDelViews(APIView):
    """
    电价删除
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        id = request.query_params.get('id')
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": '参数不完整，请检查参数！', "detail": ''}
                }
            )

        info = PriceNewInfo.objects.filter(id=id, is_use=1).first()
        PeakValleyNew.objects.filter(province=info.province, type=info.ele_type, level=info.level, year_month=info.year_month).delete()
        info.is_use = 0
        info.save()

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": ""}
            }
        )


class StaitonViews(APIView):
    """
    用户关联并网点列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        name = request.query_params.get("name")
        # 查询该用户下所有站(模糊匹配站名)
        station_obj = StationDetails.objects.filter(userdetails__id=user_id, is_delete=0,
                                                         station_name__contains=name).all()
        data = [{'name': i.station_name, 'id': i.id} for i in station_obj]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )




class BaseIncomeView(APIView):
    """
    生成基准收益
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        ids = request.query_params.get("ids")  # 从站ID，多个用英文逗号隔开
        start_time = request.query_params.get("start_time")  # 开始时间
        if not ids:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "参数为空！", "detail": ''}
                }
            )
        ids = [int(i) for i in ids.split(',')]
        end_time = datetime.datetime.now().date()
        for _id in ids:
            income_res = StationIncome.objects.filter(station_id=_id,income_date__gte=start_time).order_by('income_date').first()
            if income_res:
                income_list = []
                start_time = income_res.income_date
                r_count = (end_time - start_time).days
                station = StationDetails.objects.get(id=_id)
                print('-'*100, station.station_name, f'开始执行, 共{r_count}天')
                price_res = PeakValleyNew.objects.filter(province=station.province, type=station.type, level=station.level).order_by(
                    'moment').all()

                price_new_dict = {}
                for price in price_res:
                    k = price.year_month  # 定义唯一key
                    # moment = (datetime.datetime.strptime(price.moment, '%H:%M') + datetime.timedelta(hours=1)).strftime('%H:%M')
                    moment = price.moment
                    if price_new_dict.get(k):
                        price_new_dict[k][moment] = price
                    else:
                        price_new_dict[k] = {
                            moment: price
                        }

                station_base = StationBaseNew.objects.filter(station_id=_id).all()
                former_ids = [base.former_base_id for base in station_base]

                for d in range(r_count):
                    day_time1 = start_time + datetime.timedelta(days=d)
                    month = f"{day_time1.year}-{day_time1.month}" if day_time1.month >= 10 else f"{day_time1.year}-0{day_time1.month}"
                    former_data = FormerBaseNew.objects.filter(id__in=former_ids, year_month=month).order_by('moment').all()
                    former_dict = {i.moment: i for i in former_data}
                    chag = 0  # 日充电
                    disg = 0  # 日放电
                    income = 0  # 日收益
                    # 考虑存在单位电价的情况
                    user_price = UnitPrice.objects.filter(station=station.master_station, start__lte=day_time1,
                                                                 end__gte=day_time1,
                                                                 delete=0)

                    if user_price.exists():
                        user_price = user_price.last()
                        price_dic = {
                            "2_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                            "1_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                            "0_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                            "-1_chag_price": float(
                                user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                            "-2_chag_price": float(
                                user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                            "2_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                            "1_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                            "0_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                            "-1_disg_price": float(
                                user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                            "-2_disg_price": float(
                                user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
                        }
                    else:
                        price_dic = None

                    price_dict = price_new_dict.get(month)
               
                    if former_dict and price_dict:
                        for k, y in price_dict.items():  # k: 时刻; y：电价信息
                            former = former_dict.get(k)

                            if price_dic:  # 单位电价
                                chag_price, disg_price = price_dic[f"{str(y.pv)}_chag_price"], price_dic[f"{str(y.pv)}_disg_price"]
                            else:  # 电价
                                chag_price = disg_price = y.price

                            power_value = former.power_value / 4
                            # print ('---------',k,former.power_value,power_value * float(chag_price),power_value * float(disg_price))
                            if former.mark == -1:  # 充电
                                chag += power_value
                                income -= power_value * float(chag_price)
                            elif former.mark == 1:  # 放电
                                disg += power_value
                                income += power_value * float(disg_price)
                            else:
                                continue

                    station_base_income = StationBaseIncome()
                    station_base_income.station_name = station.english_name
                    station_base_income.day = str(day_time1)
                    station_base_income.day_income = round(income, 3)
                    station_base_income.day_chag = round(chag, 3)
                    station_base_income.day_disg = round(disg, 3)
                    income_list.append(station_base_income)

                StationBaseIncome.objects.bulk_create(income_list)
                print('-'*100, station.station_name, f'执行完成')
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": ''}
            }
        )



class MergeStrategy(APIView):
    """
    默认、基准、电价合并
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):

        # 基准策略
        base_station_res = StationBaseNew.objects.values('station_id').distinct()

        base_station_list = []
        for i in base_station_res:
            base_station_list.append(i['station_id'])

        base_list = []
        for k in base_station_list:

            info = FormerBaseNewInfo()
            station = StationDetails.objects.filter(id=k).first()
            if station:
                info.name = station.station_name + station.province.name + common_response_code.ConfLevType.TYPE['zh'][station.type] + \
                                   common_response_code.ConfLevType.LEVEL['zh'][station.level] + '基准运行策略'
                info.station_id = k
                info.is_use = 1
                info.stype = 1
                info.year = 2025
                info.file_url = ''
                base_list.append(info)
        FormerBaseNewInfo.objects.bulk_create(base_list)

        # 默认策略
        actic_station_res = StationActicNew.objects.values('station_id').distinct()

        actic_station_list = []
        for i in actic_station_res:
            actic_station_list.append(i['station_id'])

        actic_list = []
        name_dict = {}
        for k in actic_station_list:
            station = StationDetails.objects.filter(id=k).first()
            if station:
                name = station.province.name + common_response_code.ConfLevType.TYPE['zh'][
                            station.type] + '默认运行策略'
                if name not in name_dict.keys():
                    name_dict[name] = {
                        'station_ids': [str(k)],
                        'province': station.province,
                        'ele_type': station.type
                    }

                else:
                    name_dict[name]['station_ids'].append(str(k))
        for k, v in name_dict.items():
            info = FormerActicNewInfo()
            info.name = k
            info.station_id = ','.join(v['station_ids'])
            info.is_use = 1
            info.stype = 1
            info.year = 2025
            info.province = v['province']
            info.ele_type = v['ele_type']
            info.file_url = ''
            actic_list.append(info)


        FormerActicNewInfo.objects.bulk_create(actic_list)

        # 电价
        price_res = PeakValleyNew.objects.values('province_id', 'type', 'level', 'year_month').distinct()
        price_dict = {}
        price_list = []
        for i in price_res:
            k = f"{i['province_id']}-{i['type']}-{i['level']}"
            if k not in price_dict.keys():
                price_dict[k] = [i['year_month']]
            else:
                price_dict[k].append(i['year_month'])
        # print(price_dict)
        for k, v in price_dict.items():
            province_id, ele_type, level = k.split('-')
            for _v in v:
                info = PriceNewInfo()
                info.is_use = 1
                info.stype = 1
                info.province_id = province_id
                info.ele_type = ele_type
                info.level = level
                info.year_month = _v
                info.file_url = ''
                price_list.append(info)
        PriceNewInfo.objects.bulk_create(price_list)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": ''}
            }
        )


