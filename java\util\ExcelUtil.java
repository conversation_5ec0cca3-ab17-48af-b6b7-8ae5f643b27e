package com.robestec.analysis.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * Excel工具类
 * 严格对应Python中的pandas.DataFrame.to_excel功能
 */
@Slf4j
@Component
public class ExcelUtil {

    /**
     * 生成Excel文件
     * 严格对应Python中的df = pd.DataFrame(execl_data); df.to_excel(path, index=False)
     * 
     * @param fileName 文件名
     * @param excelData Excel数据
     * @return 文件路径
     */
    public String generateExcelFile(String fileName, Map<String, List<Object>> excelData) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("历史数据");
            
            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 创建数据行样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            
            // 获取列名和数据
            String[] columnNames = excelData.keySet().toArray(new String[0]);
            int rowCount = excelData.values().iterator().next().size();
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < columnNames.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columnNames[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据行
            for (int rowIndex = 0; rowIndex < rowCount; rowIndex++) {
                Row dataRow = sheet.createRow(rowIndex + 1);
                
                for (int colIndex = 0; colIndex < columnNames.length; colIndex++) {
                    String columnName = columnNames[colIndex];
                    List<Object> columnData = excelData.get(columnName);
                    
                    Cell cell = dataRow.createCell(colIndex);
                    cell.setCellStyle(dataStyle);
                    
                    if (rowIndex < columnData.size()) {
                        Object value = columnData.get(rowIndex);
                        if (value != null) {
                            if (value instanceof Number) {
                                cell.setCellValue(((Number) value).doubleValue());
                            } else {
                                cell.setCellValue(value.toString());
                            }
                        } else {
                            cell.setCellValue("--");
                        }
                    } else {
                        cell.setCellValue("--");
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < columnNames.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 生成文件路径
            String tempDir = System.getProperty("java.io.tmpdir");
            String filePath = Paths.get(tempDir, fileName).toString();
            
            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            }
            
            workbook.close();
            
            log.info("Excel文件生成成功: {}", filePath);
            return filePath;
            
        } catch (IOException e) {
            log.error("生成Excel文件失败", e);
            throw new RuntimeException("生成Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     * 对应Python中的os.remove(path)
     * 
     * @param filePath 文件路径
     */
    public void deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            Files.deleteIfExists(path);
            log.info("临时文件删除成功: {}", filePath);
        } catch (IOException e) {
            log.error("删除临时文件失败: {}", filePath, e);
        }
    }
}
