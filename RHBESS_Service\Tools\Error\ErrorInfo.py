#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\Error\ErrorInfo.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-05-12 14:12:05


# 具体实例
# errNotData = {"code": 1010, "msg": "数据字典不存在，数据为空"}
errLJ = {"code": 400, "msg": "路径错误"}
errRZ = {"code": 401, "msg": "用户未认证/用户未登录"}
errQX = {"code": 403, "message": '无权限'}
errZY = {"code": 404, "msg": "资源不存在"}
errDL = {"code": 406, "message": '请求未授权'}
errCS = {"code": 408, "msg": "请求超时"}


errDataEx = {"code": 1001, "message": '数据已经存在'}
errDataNotEx = {"code": 1002, "message": '数据不存在'}


errUserEx = {"code": 1101, "message": '手机号或者邮箱已经存在'}
errUserNotEx = {"code": 1102, "message": '用户不存在'}


errRoleMenuEx = {"code": 1103, "message": '该角色已经分配'}
errRoleMenuNotEx = {"code": 1104, "message": '该角色未分配'}


new_errLJ = {"code": 400, "message": "路径错误"}
new_errRZ = {"code": 401, "message": "用户未认证/用户未登录"}
new_errQX = {"code": 403, "message": '无权限'}
new_errZY = {"code": 404, "message": "资源不存在"}
new_errDL = {"code": 406, "message": '请求未授权'}
new_errCS = {"code": 408, "message": "请求超时"}
new_errParameter = {"code": 410, "message": "参数错误"}
successIF = {"code": 200, "message": "请求成功"}

