# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/26 9:35
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : login.py
# @Software : PyCharm
import concurrent.futures
import datetime
import json
import logging
import os
import traceback
import calendar
from decimal import Decimal
from django.db.models import Sum, Count
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from TianLuAppBackend.settings import BASE_DIR
from apis.monitor.tasks import plan_history
from apis.user import models
from apis.web2.project_dashboard.tools import get_last_12_months_v1
from common import common_response_code
from common.database_pools import ads_db_tool
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from tools.day_hours_used import pool
from tools.gen_excel import create_excel, post2minio
from django.core.cache import caches
cache = caches['1']

class AuthMapView(APIView):
    """web内部人员大屏地图"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def _get_province_data(self, province, user_ins, lang='zh'):

        conn = pool.connection()
        cursor = conn.cursor()

        offline_list = []
        Fault_list = []
        alarm_list = []

        province_ins = models.Project.objects.filter(province__name=province["province__name"], is_used=1).first()
        province["longitude"] = province_ins.longitude
        province["latitude"] = province_ins.latitude

        if lang == 'en':
            province["province__name"] = province['province__en_name']
        # station_instances = models.StationDetails.objects.filter(province__name=province["province__name"],
        #                                                          userdetails=user_ins)

        master_stations = models.MaterStation.objects.filter(project=province_ins,
                                                             userdetails=user_ins, is_delete=0).all()

        if master_stations.exists():
            for master_station in master_stations:
                units = models.Unit.objects.filter(is_delete=0, station__master_station=master_station).all()
                for unit in units:
                    conn_ = get_redis_connection("3")
                    key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name,
                                                                   unit.pcs)
                    status_pcs = conn_.get(key1)
                    if status_pcs:
                        status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                    else:
                        status_pcs_dict = {}

                    key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name,
                                                                   unit.bms)
                    status_bms = conn_.get(key2)
                    if status_bms:
                        status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                    else:
                        status_bms_dict = {}

                    if not status_pcs_dict:
                        offline_list.append(1)  # 离线状态

                    else:
                        GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
                        GAlarm = status_bms_dict.get("GAlarm", -2)  # bms警告状态
                        Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
                        alarm = status_pcs_dict.get("alarm", -2)  # pcs警告状态
                        # print(station.english_name, GFault, GAlarm, Fault, alarm)
                        if Fault == '1' or Fault == 1:
                            Fault_list.append(1)
                        if GFault == '1' or GFault == 1:
                            Fault_list.append(1)
                        if alarm == '1' or alarm == 1:
                            alarm_list.append(1)
                        if GAlarm == '1' or GAlarm == 1:
                            alarm_list.append(1)
        province["status"] = 1
        if 1 in alarm_list:
            province["status"] = 2
        if 1 in Fault_list:
            province["status"] = 3
        if 1 in offline_list:
            province["status"] = 4

        # province['province_name'] = province['province_name'] if lang == 'zh' else province['province__en_name']

        try:
            cursor.close()
            conn.close()
        except Exception as e:
            pass

        return province

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)
        count_province = models.Project.objects.values("province__name", "province__en_name").filter(user=user_ins, is_used=1).annotate(
            count=Count('id'))

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = list()
            for province in count_province:
                future = executor.submit(self._get_province_data, province, user_ins, lang)
                futures.append(future)
            results = [f.result() for f in concurrent.futures.as_completed(futures)]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": results,
                },
            }
        )


class UserMapView(APIView):
    """web客户大屏地图"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)
        projects = models.Project.objects.filter(user=user_ins, is_used=1).values(
            "province__name", "name", "city", "longitude", "latitude", "english_name"
        )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": projects,
                },
            }
        )


class WebIncomeView(APIView):
    """Web大屏收益"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get_master_station_data(self, master_station, income_date):
        incomes_ins = models.StationIncome.objects.filter(master_station=master_station)

        # 日总收益
        t_peak = incomes_ins.filter(income_date=income_date).order_by("id").first()
        t_peak_income = 0
        t_demand_income = 0

        if t_peak:
            if hasattr(t_peak, "peak_load_shifting"):
                t_peak_income = t_peak.peak_load_shifting
            else:
                t_peak_income = 0
            if hasattr(t_peak, "demand_side_response"):
                t_demand_income = t_peak.demand_side_response
            else:
                t_demand_income = 0
        t_incomes = t_peak_income + t_demand_income
        # today_general_income.append(t_incomes)
        # 月收益
        first_day = income_date.replace(day=1)

        # subquery = (
        #     incomes_ins.filter(income_date__range=(first_day, income_date + datetime.timedelta(days=1)))
        #     .values('income_date')
        #     .annotate(last_entry=Max('id'))
        #     .values('last_entry')
        # )
        monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date + datetime.timedelta(days=1))).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not monthly_income['total_peak']:
            monthly_income['total_peak'] = 0
        if not monthly_income['total_demand']:
            monthly_income['total_demand'] = 0
        total_month = monthly_income.get('total_peak', 0) + monthly_income.get('total_demand', 0)
        # month_general_income.append(total_month)
        # 年收益
        year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()

        # year_subquery = (
        #     incomes_ins.filter(income_date__range=(year_start, income_date + datetime.timedelta(days=1)))
        #     .values('income_date')
        #     .annotate(last_entry=Max('id'))
        #     .values('last_entry')
        # )

        year_income = incomes_ins.filter(income_date__range=(year_start, income_date + datetime.timedelta(days=1))).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not year_income['total_peak']:
            year_income['total_peak'] = 0
        if not year_income['total_demand']:
            year_income['total_demand'] = 0
        total_year = year_income['total_peak'] + year_income['total_demand']
        # year_general_income.append(total_year)
        # 所有收益
        all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()

        # all_subquery = (
        #     incomes_ins.filter(income_date__range=(all_start, income_date + datetime.timedelta(days=1)))
        #     .values('income_date')
        #     .annotate(last_entry=Max('id'))
        #     .values('last_entry')
        # )

        all_income = incomes_ins.filter(income_date__range=(all_start, income_date + datetime.timedelta(days=1))).aggregate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
        )
        if not all_income['total_peak']:
            all_income['total_peak'] = 0
        if not all_income['total_demand']:
            all_income['total_demand'] = 0
        total_all = all_income['total_peak'] + all_income['total_demand']
        # all_general_income.append(total_all)

        return t_incomes, total_month, total_year, total_all

    def get_master_station_datas(self,master_station,income_date):
        incomes_ins = models.StationIncome.objects.filter(master_station__in=master_station)

        # 日总收益
        t_peak = incomes_ins.filter(income_date=income_date).values('master_station').annotate(peak_load_shifting=Sum('peak_load_shifting'), demand_side_response=Sum('demand_side_response'))
        total_day = 0
        for item in t_peak:
            if not item['peak_load_shifting']:
                item['peak_load_shifting'] = 0
            if not item['demand_side_response']:
                item['demand_side_response'] = 0
            _total_day = item["peak_load_shifting"] + item["demand_side_response"]
            total_day += _total_day
        # 月总收益
        first_day = income_date.replace(day=1)
        monthly_incomes = incomes_ins.filter(income_date__year=income_date.year,income_date__month=income_date.month).values('master_station').annotate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response'))
        total_month = 0
        for monthly_income in monthly_incomes:
            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0
            _total_month = monthly_income.get('total_peak', 0) + monthly_income.get('total_demand', 0)
            total_month += _total_month

        # 年收益
        year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()
        year_incomes = incomes_ins.filter(income_date__year=income_date.year).values('master_station').annotate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response'))
        total_year = 0
        for year_income in year_incomes:
            if not year_income['total_peak']:
                year_income['total_peak'] = 0
            if not year_income['total_demand']:
                year_income['total_demand'] = 0
            _total_year = year_income['total_peak'] + year_income['total_demand']
            total_year += _total_year

        # 所有收益
        all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()

        all_incomes = incomes_ins.filter().values('master_station').annotate(
            total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response'))

        total_all = 0
        for all_income in all_incomes:
            if not all_income['total_peak']:
                all_income['total_peak'] = 0
            if not all_income['total_demand']:
                all_income['total_demand'] = 0
            _total_all = all_income['total_peak'] + all_income['total_demand']
            total_all += _total_all
        return total_day,total_month,total_year,total_all

    def get_master_station_datas_v2(self, stations_names, income_date):
        """
        切换 ads_report_station_income_1d
        """""
        # incomes_ins = models.StationIncome.objects.filter(master_station__in=master_station)

        # 日总收益
        sql_day = """
        select sum(peak_load_shifting) as peak_load_shifting,sum(demand_side_response) as demand_side_response,station from ads_report_station_income_1d
        where day='%s' and station in ('%s') group by station;
        """ % (income_date, "','".join(stations_names))
        t_peaks = ads_db_tool.select_many(sql_day)
        total_day = []
        for t_peak in t_peaks:
            peak_load_shifting = t_peak['peak_load_shifting'] if t_peak['peak_load_shifting'] != None else 0
            demand_side_response = t_peak['demand_side_response'] if t_peak['demand_side_response'] != None else 0
            total_day.append(peak_load_shifting + demand_side_response)

        total_day = sum(total_day)

        # 月总收益
        # first_day = income_date.replace(day=1)
        current_month_str = datetime.datetime.strftime(income_date, '%Y%m')
        sql_month = """
                        select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                        where date_type='year_month' and date_value='%s' and station in ('%s');
                        """ % (current_month_str, "','".join(stations_names))

        monthly_income = ads_db_tool.select_one(sql_month)
        total_month = monthly_income['total_income'] if monthly_income['total_income'] else '--'

        # 年收益
        # year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()
        current_year_str = income_date.year
        sql_year = """
                        select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                        where date_type='year' and date_value='%s' and station in ('%s');
                        """ % (current_year_str, "','".join(stations_names))
        year_income = ads_db_tool.select_one(sql_year)

        total_year = year_income['total_income'] if year_income['total_income'] else '--'

        # 所有收益
        sql_all = """
                        select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                        where date_type='total' and station in ('%s');
                        """ % ("','".join(stations_names))

        total_income = ads_db_tool.select_one(sql_all)
        total_all = total_income['total_income'] if total_income['total_income'] else '--'

        return total_day, total_month, total_year, total_all

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
        stations = models.StationDetails.objects.filter(project__user=user_ins, project__in=project_ins, is_delete=0).all()
        # master_stations = models.MaterStation.objects.filter(project__in=project_ins, is_delete=0).all()
        # master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        income_date = datetime.date.today()
        detail_dic = {
            "today_general_income": 0,
            "month_general_income": 0,
            "year_general_income": 0,
            "all_general_income": 0,
        }  # 今日总收益  # 本月总收益  # 本年总收益  # 历史总收益

        stations_names = [item.english_name for item in stations]
        detail_dic["today_general_income"], detail_dic["month_general_income"], detail_dic["year_general_income"], detail_dic["all_general_income"] = self.get_master_station_datas_v2(stations_names, income_date)

        if detail_dic["today_general_income"] != '--':
            detail_dic["today_general_income"] = [round(float(detail_dic["today_general_income"]), 2), "元" if lang == 'zh' else 'Yuan']
        else:
            detail_dic["today_general_income"] = ['--', "元" if lang == 'zh' else 'Yuan']

        if detail_dic["month_general_income"] != '--':
            detail_dic["month_general_income"] = [round(float(detail_dic["month_general_income"]), 2), "元" if lang == 'zh' else 'Yuan']
        else:
            detail_dic["month_general_income"] = ['--', "元" if lang == 'zh' else 'Yuan']

        if detail_dic["year_general_income"] != '--':
            detail_dic["year_general_income"] = [round(float(detail_dic["year_general_income"]), 2), "元" if lang == 'zh' else 'Yuan']
        else:
            detail_dic["year_general_income"] = ['--', "元" if lang == 'zh' else 'Yuan']

        if detail_dic["all_general_income"] != '--':
            detail_dic["all_general_income"] = [round(float(detail_dic["all_general_income"]), 2), "元" if lang == 'zh' else 'Yuan']
        else:
            detail_dic["all_general_income"] = ['--', "元" if lang == 'zh' else 'Yuan']

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail_dic,
                },
            }
        )


class MapHealthView(APIView):
    """地图大屏健康度"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]

        user_ins = models.UserDetails.objects.get(id=user_id)
        project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
        stations_ins = models.StationDetails.objects.filter(is_delete=0, master_station__project__in=project_ins,
                                                            master_station__is_delete=0).all()

        unit_count = sum([Decimal(station_ins.battery_cluster) for station_ins in stations_ins])
        # 判断data中大于等于 90 的数量
        count_dic = {"count_gte_90": unit_count, "count_gte_80": 0, "count_gte_60": 0, "count_lte_60": 0}

        # 百分比计算
        count_dic["count_gte_90_per"] = Decimal(unit_count / unit_count * 100).quantize(Decimal("0.00"))

        count_dic["count_gte_80_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_gte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_lte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": count_dic,
                },
            }
        )


def get_months_reach_yield(month_str_list, stations):
    """
    获取指定月份的达产率
    :param month_str:
    :param stations:
    :return:
    """
    english_names = [item.english_name for item in stations]

    # 切换 ads_report_station_income_cw_cm_cy_tt
    sql_str = """
                    SELECT 
                        date_value,
                        SUM(total_income)/SUM(base_income) * 100 as reach_yield
                    FROM 
                        ads_report_station_income_cw_cm_cy_tt 
                    WHERE 
                        date_type = 'year_month'
                    AND 
                        date_value IN %s
                    AND 
                        station IN %s 
                    GROUP BY 
                        date_value
                """

    results = ads_db_tool.select_many(sql_str, tuple(month_str_list), tuple(english_names))

    t_list = []
    for item in results:
        # total_income = float(item['month_income']) if item['month_income'] else '--'
        # total_base_income = float(item['month_base_income']) if item['month_base_income'] else '--'
        # month_reach_yield = round(total_income / float(total_base_income) * 100, 2) if total_base_income != '--' and total_income != '--' and total_base_income != 0 else '--'
        month_reach_yield = round(float(item['reach_yield']), 2) if item['reach_yield'] else '--'

        temp_dict = {
            "month": item['date_value'][:4] + '-' + item['date_value'][4:],
            "reach_yield": month_reach_yield
        }
        t_list.append(temp_dict)

    return t_list


def map_reach(request):
    user_id = request.user["user_id"]
    user_ins = models.UserDetails.objects.get(id=user_id)
    project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
    stations_ins = models.StationDetails.objects.filter(is_delete=0, master_station__project__in=project_ins,
                                                        master_station__is_delete=0).all()
    key = f'MapReachYieldByMonthView_{user_id}'
    if cache.get(key):
        return Response(cache.get(key))
    month_list = get_last_12_months_v1()

    temp_array = get_months_reach_yield(month_list, stations_ins)

    detail = sorted(temp_array, key=lambda x: x['month'])
    cache.set(key, {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": detail,
            },
        })
    return Response(
        {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": detail,
            },
        }
    )


class MapReachYieldByMonthView(APIView):
    """地图大屏--逐月达产率"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        return map_reach(request)


def get_target_project_reach_yield(project):
    """
    获取指定月份的达产率
    :param month_str:
    :param stations:
    :return:
    """
    stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project,
                                                        master_station__is_delete=0).all()

    sql = """
            SELECT
                SUM(total_income) as total_income,
                sum(base_income) as total_base_income
            FROM
                ads_report_station_income_cw_cm_cy_tt
            WHERE
                date_type = 'total'
            AND
                station IN %s
    """

    res = ads_db_tool.select_one(sql, tuple([s.english_name for s in stations]))
    if res:
        total_income = float(res['total_income']) if res['total_income'] else '--'
        total_base_income = float(res['total_base_income']) if res['total_base_income'] else '--'

        reach_yield = round(total_income / total_base_income * 100, 2) if total_base_income != '--' and total_base_income != 0 else '--'

    else:
        reach_yield = '--'

    return project.name, reach_yield


def mapprojreacg(request):
    user_id = request.user["user_id"]
    user_ins = models.UserDetails.objects.get(id=user_id)
    projects = models.Project.objects.filter(user=user_ins, is_used=1).all()
    key = f'MapProjReachYieldTopView_{user_id}'
    if cache.get(key):
        return Response(cache.get(key))
    temp_array = []
    # month_list = get_last_12_months()
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        futures = list()
        for project in projects:
            future = executor.submit(get_target_project_reach_yield, project)
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            try:
                project_name, reach_yield = future.result()
                if reach_yield != '--':
                    temp_array.append(
                        {
                            'project': project_name,
                            'reach_yield': reach_yield
                        }
                    )
            except Exception as e:
                logging.error(traceback.print_tb)

    detail = sorted(temp_array, key=lambda x: x['reach_yield'], reverse=True)
    cache.set(key, {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": detail[:10] if len(detail) > 10 else detail,
            },
        })
    return Response(
        {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": detail[:10] if len(detail) > 10 else detail,
            },
        }
    )


class MapProjReachYieldTopView(APIView):
    """地图大屏--项目达产率排名"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def get(self, request):
        return mapprojreacg(request)


class WebIncomeDiffView(APIView):
    """Web大屏收益: 测试"""""

    def get(self, request, *args, **kwargs):

        e_name = request.query_params.get('e_name', None)

        if e_name:
            stations = models.StationDetails.objects.filter(is_delete=0, english_name=e_name).all()
        else:
            stations = models.StationDetails.objects.filter(is_delete=0).all()
        months = ['2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12']

        for station in stations:
            incomes = models.StationIncome.objects.filter(station_id=station.id).order_by("income_date").all()
            if not incomes.exists():
                continue
            for income in incomes:
                date = income.income_date
                base_income = models.StationBaseIncome.objects.filter(station_name=station.english_name,
                                                                      day=date.strftime('%Y-%m-%d')).first()

                if not base_income:
                    print(510, f"{station.station_name}的{date.strftime('%Y-%m-%d')}的基准收益缺失！！！")
                    continue

                # if float(income.peak_load_shifting) - float(base_income.day_income) > 300:
                #     print(514, f"{station.station_name}的{date.strftime('%Y-%m-%d')}的收益和基准收益相差超过300元！！！")
                # elif float(income.peak_load_shifting) - float(base_income.day_income) < -300:
                #     print(516, f"{station.station_name}的{date.strftime('%Y-%m-%d')}的收益和基准收益相差超过-300元！！！")

                if base_income.day_income:
                    r = float(income.peak_load_shifting) / float(base_income.day_income) * 100
                    if r < -200 or r > 200:
                        print(521, f"!!!!!{station.station_name}的{date.strftime('%Y-%m-%d')}的日达产率超过200%！！！")

            for month in months:
                month_income = (models.StationIncome.objects.filter(station_id=station.id,
                                                                   income_date__startswith=month)
                                .aggregate(sum=Sum('peak_load_shifting')))

                base_month_income = models.StationBaseIncome.objects.filter(station_name=station.english_name,
                                                                      day__startswith=month).aggregate(sum=Sum('day_income'))

                if base_month_income['sum']:
                    s = round(float(month_income['sum']) / float(base_month_income['sum']) * 100, 2)
                    print(536,
                          f"!!!!!{station.station_name}的{month}的月达产率为：{s}，月收益为：{round(month_income['sum'], 2)}， 月基准收益为：{round(base_month_income['sum'], 2)}")

            print('*'*100)
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        })


class SyncMasterStationsEnglishNameView(APIView):
    """同步主站的英文名称为逻辑主站的英文名称"""""

    def get(self, request, *args, **kwargs):

        master_stations = models.MaterStation.objects.filter(is_delete=0).all()

        for master_station in master_stations:
            stations = master_station.stationdetails_set.filter(is_delete=0).all()
            if len(stations) == 1:
                ems_station = stations[0]
                master_station.english_name = ems_station.english_name
                master_station.save()

            if len(stations) > 1:
                ems_station = stations.filter(slave=0).first()
                if ems_station:
                    master_station.english_name = ems_station.english_name
                    master_station.save()

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        })


class TestView(APIView):
    """功率计划下发： 测试"""""

    def get(self, request, *args, **kwargs):
        plan_history()

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        })


class TestIncomeView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_project_data(self, project):
        """
        获取指定月份的达产率
        :param month_str:
        :param stations:
        :return:
        """

        data_array = []

        stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project,
                                                        master_station__is_delete=0).all()

        p_total_income = 0
        p_total_base_income = 0

        end_date = datetime.datetime.strptime('2024-05-13', '%Y-%m-%d')

        months = ['2024-01', '2024-02', '2024-03', '2024-04']

        for station in stations:
            incomes = models.StationIncome.objects.filter(station_id=station.id, income_date__lte=end_date.date()).order_by("income_date").all()
            if not incomes.exists():
                continue

            start_date = incomes[0].income_date

            print(627, start_date, end_date)

            total_base_income = models.StationBaseIncome.objects.filter(station_name=station.english_name,
                                                                  day__gte=start_date.strftime('%Y-%m-%d'),
                                                                  day__lte=end_date.strftime('%Y-%m-%d')).aggregate(sum=Sum('day_income'))['sum']

            total_income = incomes.aggregate(sum=Sum('peak_load_shifting'))['sum']

            print(633, f"截止到{end_date.strftime('%Y-%m-%d')}, {station.station_name}的累计收益为：{total_income}， "
                       f"基准累计收益为：{total_base_income}")

            p_total_income += total_income if total_income else 0
            p_total_base_income += total_base_income if total_base_income else 0

            # 核对站的月收益和月基准收益
            for month in months:
                month_income = (models.StationIncome.objects.filter(station_id=station.id,
                                                                   income_date__startswith=month)
                                .aggregate(sum=Sum('peak_load_shifting')))

                base_month_income = models.StationBaseIncome.objects.filter(station_name=station.english_name,
                                                                      day__startswith=month).aggregate(sum=Sum('day_income'))

                if base_month_income['sum'] and month_income['sum']:
                    s = round(float(month_income['sum']) / float(base_month_income['sum']) * 100, 2)
                    print(536,
                          f"!!!!!{station.station_name}的{month}的月收益为：{round(month_income['sum'], 2)}， 月基准收益为：{round(base_month_income['sum'], 2)}, 月达产率为：{s}")
                else:
                    print(655, f"!!!!!{station.station_name}的{month}的月收益为：{month_income['sum']}， 月基准收益为：{base_month_income['sum']}")

            print('*'*100)

        reach_yield = round(float(p_total_income) / float(p_total_base_income) * 100, 2) if p_total_base_income else '--'

        print(639, f"截止到{end_date.strftime('%Y-%m-%d')}, {project.name}的累计收益为：{p_total_income}， "
                   f"基准累计收益为：{round(p_total_base_income, 2)}, 累计收益率为： {reach_yield}")

        print('*' * 200)


        return reach_yield

    def get(self, request, *args, **kwargs):
        user_id = request.user["user_id"]
        user = models.UserDetails.objects.get(id=user_id)
        projects = models.Project.objects.filter(user=user, is_used=1).all()

        for project in projects:
            self.get_project_data(project)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        })


class TestIncome2excelView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_project_data(self, project):
        """
        获取指定月份的达产率
        :param month_str:
        :param stations:
        :return:
        """

        data_array = []

        stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project,
                                                        master_station__is_delete=0).all()

        p_total_income = 0
        p_total_base_income = 0

        # end_date = datetime.datetime.strptime('2024-0-20', '%Y-%m-%d')
        end_date = datetime.datetime.now()

        months = ['2023-05', '2023-06', '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06']

        for station in stations:
            incomes = models.StationIncome.objects.filter(station_id=station.id,
                                                          income_date__lte=end_date.date()).order_by(
                "income_date").all()
            if not incomes.exists():
                continue

            start_date = incomes[0].income_date

            print(627, start_date, end_date)

            total_base_income = models.StationBaseIncome.objects.filter(station_name=station.english_name,
                                                                        day__gte=start_date.strftime('%Y-%m-%d'),
                                                                        day__lte=end_date.strftime(
                                                                            '%Y-%m-%d')).aggregate(
                sum=Sum('day_income'))['sum']

            total_income = incomes.aggregate(sum=Sum('peak_load_shifting'))['sum']

            print(633, f"截止到{end_date.strftime('%Y-%m-%d')}, {station.station_name}的累计收益为：{total_income}， "
                       f"基准累计收益为：{total_base_income}")

            p_total_income += total_income if total_income else 0
            p_total_base_income += total_base_income if total_base_income else 0

        reach_yield = round(float(p_total_income) / float(p_total_base_income) * 100,
                            2) if p_total_base_income else '--'

        print(639, f"截止到{end_date.strftime('%Y-%m-%d')}, {project.name}的累计收益为：{p_total_income}， "
                   f"基准累计收益为：{round(p_total_base_income, 2)}, 累计收益率为： {reach_yield}")

        project_data = {
            "project_name": project.name,
            "project_id": project.id,
            "reach_yield": reach_yield,
            "total_income": p_total_income,
            "total_base_income": p_total_base_income
        }

        m_station_array = []

        # 核对对应月份的主站的月收益和月基准收益

        m_stations = project.materstation_set.filter(is_delete=0).all()
        for month in months:
            for m_station in m_stations:
                stations = m_station.stationdetails_set.filter(is_delete=0).all()

                station_name_set = [station.english_name for station in stations]
                station_id_set = [station.id for station in stations]

                month_income = (models.StationIncome.objects.filter(station_id__in=station_id_set,
                                                                    income_date__startswith=month)
                                .aggregate(sum=Sum('peak_load_shifting')))

                base_month_income = models.StationBaseIncome.objects.filter(station_name__in=station_name_set,
                                                                            day__startswith=month).aggregate(
                    sum=Sum('day_income'))

                if base_month_income['sum'] and month_income['sum']:
                    s = round(float(month_income['sum']) / float(base_month_income['sum']) * 100, 2)
                    print(536,
                          f"!!!!!{station.station_name}的{month}的月收益为：{round(month_income['sum'], 2)}， 月基准收益为：{round(base_month_income['sum'], 2)}, 月达产率为：{s}")
                else:
                    print(655,
                          f"!!!!!{station.station_name}的{month}的月收益为：{month_income['sum']}， 月基准收益为：{base_month_income['sum']}")

                m_station_dict = {
                    "month": month,
                    "station_name": m_station.name,
                    "station_id": m_station.id,
                    "month_income": month_income['sum'],
                    "base_month_income": base_month_income['sum'],
                }

                m_station_array.append(m_station_dict)

        return project_data, m_station_array

    def get(self, request, *args, **kwargs):
        user_id = request.user["user_id"]
        user = models.UserDetails.objects.get(id=user_id)
        projects = models.Project.objects.filter(is_used=1).all()

        projects_data = []
        m_stations_array = []

        for project in projects:
            project_data, m_station_array = self.get_project_data(project)
            projects_data.append(project_data)
            m_stations_array.extend(m_station_array)

        end_date = datetime.date.today()

        filename = f"达产率核对数据（截止到{end_date.strftime('%Y-%m-%d')}）.xlsx"

        file_path = os.path.join(os.path.join(BASE_DIR, 'static'), filename)

        sheet1 = '累计收益数据'
        sheet2 = '月收益数据'

        sheet1_headers = ['项目名称', '累计收益（kWh）', '累计基准收益（kWh）', '累计收益完成率（%）']
        sheet1_datas = []

        sheet2_headers = ['月份', '电站名称', '月收益', '月基准收益']
        sheet2_datas = []

        for i in projects_data:
            temp_list_1 = [i['project_name'], i['total_income'], i['total_base_income'], i['reach_yield']]

            sheet1_datas.append(temp_list_1)

        for j in m_stations_array:
            temp_list = [j['month'], j['station_name'], j['month_income'], j['base_month_income']]
            sheet2_datas.append(temp_list)

        create_excel(file_path, sheet_name=sheet1, headers=sheet1_headers, data=sheet1_datas, sheet_name1=sheet2,
                     headers1=sheet2_headers, data1=sheet2_datas)

        download_url = post2minio(file_path, object_name=filename)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "url": download_url
            },
        })

