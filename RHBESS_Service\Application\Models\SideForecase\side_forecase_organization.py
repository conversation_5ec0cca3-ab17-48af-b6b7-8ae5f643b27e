#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-29 19:07:45
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_organization.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 14:32:15



from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR
import pandas as pd
from Tools.Utils.time_utils import timeUtils

class ForecaseOrganization(user_Base):
    u'组织关系表'
    __tablename__ = "t_side_forecase_organization"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    
    descr = Column(String(256), nullable=False, comment="描述")
    parent_id = Column(Integer, nullable=True, comment="父节点id")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment="是否使用1是0否")
    op_ts = Column(DateTime, nullable=False, comment="时间")

   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'id':%s,'descr':'%s','parent_id':'%s'}" % (self.id,self.descr,self.parent_id)
        return bean.replace("None", '')

    

    def get_end_node(self):

        """获取末端节点方便查找子站"""
        all_organization = user_session.query(ForecaseOrganization).filter(ForecaseOrganization.is_use==1).all()
        pd_organization = pd.DataFrame([[i.id, i.descr, i.parent_id] for i in all_organization],
        columns=['id', 'descr',  'parent_id'])
        end_org_list = self.get_end_organization(pd_organization[pd_organization['id'].isin([self.id])],
                                     pd_organization)
        return end_org_list

    def get_lower_node(self):
        """
        获取该组织下端的所有组织id
        """
        all_organization = user_session.query(ForecaseOrganization).filter(ForecaseOrganization.is_use==1).all()
        pd_organization = pd.DataFrame([[i.id, i.descr, i.parent_id] for i in all_organization],
                                       columns=['id', 'descr',  'parent_id'])
        end_org_list = self.get_end_organization(apex=pd_organization[pd_organization['id'].isin([self.id])],
                                                df= pd_organization, end=False)
        return end_org_list

   # 获取末端线路
    def get_end_organization(self, apex, df, org_list=None, end=True):
        if org_list == None:
            org_list = []
        for indx, row in apex.iterrows():
            isin = df[df['parent_id'].isin([row['id']])]
            if len(isin.index):
                child = self.get_end_organization(isin, df, org_list,end)
                if not end:
                    org_list.append((row['id']))
            else:
                org_list.append((row['id']))
        return org_list

    # 获取组织的顶端
    def get_start_node(self):
        if self.parent_id is None:
            return self
        else:
            return self.get_up_node(self.parent_id)


    def get_up_node(self,parent):
        up_node = user_session.query(ForecaseOrganization).filter(ForecaseOrganization.id == parent,ForecaseOrganization.is_use==1).first()
        if not up_node:
            return
        else:
            if up_node.parent_id is None:
                return up_node
            else:
                self.get_up_node(up_node.parent_id)


