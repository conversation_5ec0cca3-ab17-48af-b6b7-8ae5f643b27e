package com.robestec.analysis.exception;

/**
 * 远程控制策略异常类
 * 对应Python中的异常处理
 */
public class TelecontrolException extends RuntimeException {

    private Integer code;

    public TelecontrolException(String message) {
        super(message);
        this.code = 500;
    }

    public TelecontrolException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public TelecontrolException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public TelecontrolException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 文件处理异常
     */
    public static class FileProcessException extends TelecontrolException {
        public FileProcessException(String message) {
            super(1001, message);
        }

        public FileProcessException(String message, Throwable cause) {
            super(1001, message, cause);
        }
    }

    /**
     * 数据验证异常
     */
    public static class ValidationException extends TelecontrolException {
        public ValidationException(String message) {
            super(1002, message);
        }

        public ValidationException(String message, Throwable cause) {
            super(1002, message, cause);
        }
    }

    /**
     * 业务逻辑异常
     */
    public static class BusinessException extends TelecontrolException {
        public BusinessException(String message) {
            super(1003, message);
        }

        public BusinessException(String message, Throwable cause) {
            super(1003, message, cause);
        }
    }

    /**
     * 数据库操作异常
     */
    public static class DatabaseException extends TelecontrolException {
        public DatabaseException(String message) {
            super(1004, message);
        }

        public DatabaseException(String message, Throwable cause) {
            super(1004, message, cause);
        }
    }

    /**
     * 权限异常
     */
    public static class PermissionException extends TelecontrolException {
        public PermissionException(String message) {
            super(1005, message);
        }

        public PermissionException(String message, Throwable cause) {
            super(1005, message, cause);
        }
    }
}
