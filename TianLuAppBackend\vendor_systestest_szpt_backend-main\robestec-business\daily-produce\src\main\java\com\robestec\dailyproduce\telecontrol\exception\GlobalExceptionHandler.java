package com.robestec.dailyproduce.telecontrol.exception;

import com.robestec.dailyproduce.telecontrol.vo.CommonResultVO;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(com.robestec.analysis.exception.TelecontrolException.class)
    public CommonResultVO<String> handleTelecontrolException(com.robestec.analysis.exception.TelecontrolException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(com.robestec.analysis.exception.TelecontrolException.FileProcessException.class)
    public CommonResultVO<String> handleFileProcessException(com.robestec.analysis.exception.TelecontrolException.FileProcessException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(TelecontrolException.ValidationException.class)
    public CommonResultVO<String> handleValidationException(TelecontrolException.ValidationException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(TelecontrolException.BusinessException.class)
    public CommonResultVO<String> handleBusinessException(TelecontrolException.BusinessException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(TelecontrolException.DatabaseException.class)
    public CommonResultVO<String> handleDatabaseException(TelecontrolException.DatabaseException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(TelecontrolException.PermissionException.class)
    public CommonResultVO<String> handlePermissionException(TelecontrolException.PermissionException e) {
        return CommonResultVO.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResultVO<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        return CommonResultVO.error(400, "参数验证失败: " + message);
    }

    @ExceptionHandler(BindException.class)
    public CommonResultVO<String> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        return CommonResultVO.error(400, "参数绑定失败: " + message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public CommonResultVO<String> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        return CommonResultVO.error(400, "约束验证失败: " + message);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public CommonResultVO<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        return CommonResultVO.error(400, "文件大小超过限制");
    }

    @ExceptionHandler(RuntimeException.class)
    public CommonResultVO<String> handleRuntimeException(RuntimeException e) {
        return CommonResultVO.error(500, "系统内部错误: " + e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public CommonResultVO<String> handleException(Exception e) {
        return CommonResultVO.error(500, "系统异常，请联系管理员");
    }
} 