#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-19 14:01:03
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event_alarm_type.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-07-21 13:42:36



from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class EventAlarmType(user_Base):
    u'事件/告警类型表'
    __tablename__ = "c_even_alarm_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    parent_id = Column(Integer, nullable=True,comment=u"自身id")
    is_use = Column(Integer, nullable=False, comment=u"是否启用，1是0否，默认1",server_default='1')
    type = Column(CHAR(1), nullable=False,comment=u"1事件，2告警")
    en_descr = Column(VARCHAR(256), nullable=False,comment=u"描述-英文")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(EventAlarmType(id=1,descr='错误',type=2));
        user_session.merge(EventAlarmType(id=2,descr='系统，无确认',type=1));
        # user_session.commit()
        user_session.merge(EventAlarmType(id=3,descr='一级告警',parent_id=1,type=2));
        user_session.merge(EventAlarmType(id=4,descr='二级告警',parent_id=1,type=2));
        user_session.merge(EventAlarmType(id=5,descr='三级告警',parent_id=1,type=2));
        user_session.merge(EventAlarmType(id=6,descr='过程控制',parent_id=2,type=1));
        user_session.merge(EventAlarmType(id=7,descr='操作输入',parent_id=2,type=1));
        user_session.commit()
        user_session.close()
        

    def __repr__(self):
        return "{'id':%s,'descr':'%s','isUse':%s,'parent_id':'%s','type_':'%s','en_descr':'%s'}" % (self.id,self.descr,self.is_use,self.parent_id,self.type,self.en_descr)
        
    def deleteEventAlarmType(self,id):
        try:
            user_session.query(EventAlarmType).filter(EventAlarmType.id == id).update({"is_use":0})
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False