#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-16 17:34:50
#@FilePath     : \RHBESS_Service\Application\Models\User\authority_new.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-17 15:58:43


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.station import Station
class ProjectInfo(user_Base):
    u'项目信息表'
    __tablename__ = "t_project_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(VARCHAR(256), nullable=False, comment=u"项目名称")
    com_name = Column(VARCHAR(256), nullable=True, comment=u"项目公司名称")
    name = Column(VARCHAR(256), nullable=False, comment=u"英文名称")
    short_name = Column(VARCHAR(256), nullable=False, comment=u"项目简称")
    energy_storage = Column(CHAR(1), nullable=False, comment=u"储能形式，1火储能，2一般工商业储能，3天禄工商业储能，4独立储能,5新能源配套储能")
    electric_power = Column(Float, nullable=False, comment=u"额定功率,单位MW")
    volume = Column(Float, nullable=False, comment=u"电站容量,单位MWh")
    start_ts = Column(VARCHAR(50), nullable=False, comment=u"投运日期：YYYY-mm-dd")
    volt_class = Column(VARCHAR(256), nullable=True, comment=u"电压等级")
    country = Column(VARCHAR(50), nullable=True, comment=u"国家")
    province = Column(VARCHAR(50), nullable=False, comment=u"电站所在省份")
    city = Column(VARCHAR(50), nullable=False, comment=u"市")
    address = Column(VARCHAR(256), nullable=True,comment=u"地址")
    remarks = Column(VARCHAR(256), nullable=True,comment=u"基础信息备注")
    own_name = Column(VARCHAR(256), nullable=False, comment=u"业主名称")
    con_per = Column(VARCHAR(256), nullable=False, comment=u"运维合同期限")
    tar_inc_y = Column(VARCHAR(50), nullable=False, comment=u"年度运营目标收益,单位元")
    ser_pers = Column(VARCHAR(50), nullable=True, comment=u"服务人员")
    phone = Column(VARCHAR(50), nullable=True, comment=u"联系电话")
    met_read_c = Column(VARCHAR(50), nullable=True, comment=u"抄表周期")
    met_read_d = Column(VARCHAR(50), nullable=True, comment=u"抄表日")
    y_fm_re = Column(VARCHAR(50), nullable=True, comment=u"年度一次调频收益,单位元")
    y_agc_re = Column(VARCHAR(50), nullable=True, comment=u"年度AGC收益,单位元")
    y_spot_re = Column(VARCHAR(50), nullable=True, comment=u"年度现货收益,单位元")
    y_f_e_s_re = Column(VARCHAR(50), nullable=True, comment=u"年度火储能辅助服务收益,单位元")
    y_p_c_g_f_re = Column(VARCHAR(50), nullable=True, comment=u"年度削峰填谷收益,单位元")
    y_d_r_re = Column(VARCHAR(50), nullable=True, comment=u"年度需求侧响应收益,单位元")
    y_oth_re = Column(VARCHAR(50), nullable=True, comment=u"年度其他收益,单位元")
    tar_ava_y = Column(VARCHAR(50), nullable=True, comment=u"目标年度系统可用率")
    tar_li_ch_ef = Column(VARCHAR(50), nullable=True, comment=u"目标集电线路充电效率")
    tar_li_di_ef = Column(VARCHAR(50), nullable=True, comment=u"目标集电线路放电效率")
    tar_vo_ch_ef = Column(VARCHAR(50), nullable=True, comment=u"目标升压站充电效率")
    tar_vo_di_ef = Column(VARCHAR(50), nullable=True, comment=u"目标升压站放电效率")
    tar_pcs_ch_ef = Column(VARCHAR(50), nullable=True, comment=u"目标PCS充电效率")
    tar_pcs_di_ef = Column(VARCHAR(50), nullable=True, comment=u"目标PCS放电效率")
    tar_dc_ch_ef = Column(VARCHAR(50), nullable=True, comment=u"目标直流侧充电效率")
    tar_dc_di_ef = Column(VARCHAR(50), nullable=True, comment=u"目标直流侧放电效率")
    head_main = Column(VARCHAR(50), nullable=False, comment=u"运维负责人")
    dev_ops = Column(VARCHAR(50), nullable=True, comment=u"运维人员")
    other_dev_ops = Column(VARCHAR(50), nullable=True, comment=u"其他运维人员")
    data_acc_date = Column(VARCHAR(50), nullable=False, comment=u"数据接入日期")
    data_int = Column(Integer, nullable=True, comment=u"数据间隔,单位min")
    commu_mode = Column(VARCHAR(50), nullable=True, comment=u"通讯方式")
    iot_card_num = Column(VARCHAR(50), nullable=True, comment=u"物联卡编号")
    is_use = Column(CHAR(2), nullable=False,server_default='1', comment=u"是否使用1是0否")

    en_descr = Column(VARCHAR(256), nullable=False, comment=u"项目名称")
    en_com_name = Column(VARCHAR(256), nullable=True, comment=u"项目公司名称")
    en_short_name = Column(VARCHAR(256), nullable=False, comment=u"项目简称")
    en_country = Column(VARCHAR(50), nullable=True, comment=u"国家")
    en_province = Column(VARCHAR(50), nullable=False, comment=u"电站所在省份")
    en_city = Column(VARCHAR(50), nullable=False, comment=u"市")
    en_address = Column(VARCHAR(256), nullable=True, comment=u"地址")
    en_remarks = Column(VARCHAR(256), nullable=True, comment=u"基础信息备注")
    en_own_name = Column(VARCHAR(256), nullable=False, comment=u"业主名称")
    en_ser_pers = Column(VARCHAR(50), nullable=True, comment=u"服务人员")
    en_head_main = Column(VARCHAR(50), nullable=False, comment=u"运维负责人")
    en_dev_ops = Column(VARCHAR(50), nullable=True, comment=u"运维人员")
    en_other_dev_ops = Column(VARCHAR(50), nullable=True, comment=u"其他运维人员")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):

        bean = "{'id':'%s','descr':'%s','com_name':'%s','name':'%s','short_name':'%s','energy_storage':'%s','electric_power':'%s','volume':'%s','start_ts':'%s','volt_class':'%s','country':'%s','province':'%s','city':'%s','address':'%s','remarks':'%s','own_name':'%s','con_per':'%s','tar_inc_y':'%s','ser_pers':'%s','phone':'%s','met_read_c':'%s','met_read_d':'%s','y_fm_re':'%s'," \
               "'y_agc_re':'%s','y_spot_re':'%s','y_f_e_s_re':'%s','y_p_c_g_f_re':'%s','y_d_r_re':'%s','y_oth_re':'%s','tar_ava_y':'%s','tar_li_ch_ef':'%s','tar_li_di_ef':'%s','tar_vo_ch_ef':'%s','tar_vo_di_ef':'%s'," \
               "'tar_pcs_ch_ef':'%s','tar_pcs_di_ef':'%s','tar_dc_ch_ef':'%s','tar_dc_di_ef':'%s','head_main':'%s','dev_ops':'%s','other_dev_ops':'%s','data_acc_date':'%s','data_int':'%s','commu_mode':'%s'," \
               "'iot_card_num':'%s','en_descr':'%s','en_com_name':'%s','en_short_name':'%s','en_country':'%s','en_province':'%s','en_city':'%s','en_address':'%s','en_remarks':'%s','en_own_name':'%s','en_ser_pers':'%s','en_head_main':'%s','en_dev_ops':'%s','en_other_dev_ops':'%s'}" % (
            self.id,self.descr,self.com_name,self.name,self.short_name,self.energy_storage,self.electric_power,self.volume,self.start_ts,self.volt_class,self.country,self.province,self.city,self.address,self.remarks,self.own_name,self.con_per,self.tar_inc_y,self.ser_pers,self.phone,self.met_read_c,self.met_read_d,self.y_fm_re,
            self.y_agc_re,self.y_spot_re,self.y_f_e_s_re,self.y_p_c_g_f_re,self.y_d_r_re,self.y_oth_re,self.tar_ava_y,self.tar_li_ch_ef,self.tar_li_di_ef,self.tar_vo_ch_ef,self.tar_vo_di_ef,
            self.tar_pcs_ch_ef,self.tar_pcs_di_ef,self.tar_dc_ch_ef,self.tar_dc_di_ef,self.head_main,self.dev_ops,self.other_dev_ops,self.data_acc_date,self.data_int,self.commu_mode,self.iot_card_num,self.en_descr,self.en_com_name,self.en_short_name,self.en_country
        ,self.en_province,self.en_city,self.en_address,self.en_remarks,self.en_own_name,self.en_ser_pers,self.en_head_main,self.en_dev_ops,self.en_other_dev_ops)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}