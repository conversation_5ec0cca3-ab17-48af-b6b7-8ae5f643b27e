#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-27 15:49:11
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Running\HaLun\init_user.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-08-10 14:41:05

from Application.Models.User.role import Role
from Application.Models.User.organization_type import OrganizationType
from Application.Models.User.organization import Organization
from Application.Models.User.user import User
from Tools.Utils.time_utils import timeUtils
from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.User.authority import Authority
from Application.Models.User.role_authority import RoleAuthority

def init_user():
    
    user_session.merge(OrganizationType(descr=u'私企'));
    user_session.commit()
    user_session.merge(Organization(descr=u'RHBESS',type_id=1,op_ts=timeUtils.getNewTimeStr()));

    user_session.merge(Role(descr=u'系统管理员'));
    # 初始化静态路由
    
    # user_session.merge(Authority(path=u'/',name='home',component='city',title_='储能电站监控平台',hidden=0));
    # user_session.commit()
  
    user_session.merge(RoleAuthority(role_id=1,authority_id=1,op_ts=timeUtils.getNewTimeStr()));
   
    user_session.merge(User(name=u'admin',sex=1,phone_no='***********',account='root',passwd='e10adc3949ba59abbe56e057f20f883e',
        organization_id=1,user_role_id=1,unregister=1,wx_connect='0eddbec937865010edfa3ed9e5838cdc9602',op_ts=timeUtils.getNewTimeStr()));


    user_session.commit()
    if user_session==None:
        user_session.close()

def pg_create():
    '''
    创建数据库数据库
    数据库必须有mysql供连接
    所有数据库必须在一个服务器上
    '''
    import os
    from sqlalchemy import create_engine
    from Application.Cfg.dir_cfg import model_config
    print ('开始创建数据库……')
    

    USER_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
    USER_PORT = model_config.get('mysql', "DB_PORT")
    USER_DATABASE = model_config.get('mysql', "DB_DATABASE")
    USER_USERNAME = model_config.get('mysql', "DB_USERNAME")
    USER_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

    userdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
        USER_USERNAME,
        USER_PASSWORD,
        USER_HOSTNAME,
        USER_PORT,
        USER_DATABASE
    )

    engine = create_engine(userdb_mysql_url,isolation_level="AUTOCOMMIT",
                       echo=(os.getenv('DEBUG_DB', 'null') != 'null'),
                       max_overflow=10, pool_size=100, pool_timeout=30, pool_recycle=-1)
    conn = engine.connect()
    # conn.execute("commit")
    db_cfg = ['DB_DATABASE','HIS_DATABASE','USER_DATABASE']
    for db in db_cfg:
        pg = model_config.get('mysql', db)
        sql = "SELECT SCHEMA_NAME  FROM information_schema.SCHEMATA  where SCHEMA_NAME='%s'"%pg
        f = conn.execute(sql).first()
        if f:
            print ('数据库 %s 已存在'%str(pg))
            continue
        conn.execute("create database %s"%(pg))
       
    conn.close()
    print ('数据库创建成功！')


if __name__ == '__main__':
    pg_create()
    print ('初始化所有表和用户')
    # 初始化所有表
    from Application.Models.User import *
    create_all()
    # 初始化用户
    print ('初始化完成')