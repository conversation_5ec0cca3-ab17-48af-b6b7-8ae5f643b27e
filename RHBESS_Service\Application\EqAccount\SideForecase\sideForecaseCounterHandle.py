#!/usr/bin/env python
# coding=utf-8
#@Information:  计算器使用
#<AUTHOR> WYJ
#@Date         : 2023-05-31 10:51:11
#@FilePath     : \RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseUserHandle copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-31 10:51:13


import os
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_area import ForecaseArea
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_price import ForecasePrice
from Application.Models.SideForecase.side_forecase_customer import ForecaseCustomer
from Application.Models.SideForecase.side_forecase_proele import ForecaseProele
import logging,json
from sqlalchemy import func
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import *
ele_desc_pro=[38,39,40,41,42,30,10,29]
class SideForecaseCounterHandleIntetface(BaseHandler):

    # @tornado.web.authenticated
    def get(self,kt):
        try:
            if kt == 'GetAreas':  # 查询区域
                data = []
                pages = user_session.query(ForecaseArea).filter(ForecaseArea.is_use==1).order_by(ForecaseArea.index.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetVols':  # 查询电压等级
                province_id = self.get_argument('province_id',None) # 省份id
                ele_id = self.get_argument('ele_id',None) # 用电分类id
                if not province_id or not ele_id:
                    return self.customError("参数不完成")
                province_id = int(province_id)
                ele_id = int(ele_id)
                if DEBUG:
                    logging.info('province_id:%s,ele_id:%s'%(province_id,ele_id))
                data = []
                page = user_session.query(ForecaseProele).filter(ForecaseProele.is_use==1,ForecaseProele.province_id==province_id,ForecaseProele.ele_id==ele_id).first()
                if not page:  # 无数据
                    return self.returnTypeSuc(data)
                if province_id in ele_desc_pro:  # 保证第一个电压等级是自定义的
                    pagess = user_session.query(ForecaseVol).filter(ForecaseVol.is_use==1,ForecaseVol.id.in_(page.vols.split(','))).order_by(ForecaseVol.id.desc()).all()
                else:
                    pagess = user_session.query(ForecaseVol).filter(ForecaseVol.is_use==1,ForecaseVol.id.in_(page.vols.split(','))).all()
                for page in pagess:
                    if province_id == 2 and ele_id==4 and page.id == 2:  # 天津大工业两部制1-10千伏
                        data.insert(0,eval(str(page)))
                    elif province_id == 36 and ele_id==1 and page.id == 23:  # 陕西（陕西电网）大工业1-10（20）千伏
                        data.insert(0,eval(str(page)))
                    elif province_id == 31 and ele_id==1 and page.id == 2:  # 云南大工业1-10千伏
                        data.insert(0,eval(str(page)))
                    else:
                        data.append(eval(str(page)))
                return self.returnTypeSuc(data)
          
            elif kt == 'GetEles':  # 查询用电类型
                data = []
                province_id = self.get_argument('province_id',None) # 省份id
                if not province_id:
                    return self.customError("参数不完成")
                if DEBUG:
                    logging.info('province_id:%s'%(province_id))
                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                sql = "select id,name,is_use from t_side_forecase_ele where find_in_set(%s,provinces) and is_use=1 order by index_ asc"%(province_id)
                cursor.execute(sql)
                result = cursor.fetchall()
                for nam in result:
                    data.append({'id':nam[0],'name':nam[1],'is_use':int(nam[2])})
              
                conn.close()
                return self.returnTypeSuc(data)
            elif kt == 'GetProvinces':  # 查询省份
                area_id = self.get_argument('area_id',None) 
                if DEBUG:
                    logging.info('area_id:%s'%area_id)
                data,filte = [],[ForecaseProvince.is_use==1]
                if area_id:
                    filte.append(ForecaseProvince.area_id==area_id)
                
                pages = user_session.query(ForecaseProvince).filter(*filte).order_by(ForecaseProvince.index.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetPrices':  # 查询价格  部制度暂时按默认两部制
                province_id = self.get_argument('province_id',None) # 省份id
                ele_id = self.get_argument('ele_id',None) # 用电分类id
                vol_id = self.get_argument('vol_id',None) # 电压等级id
                year_month = self.get_argument('year_month',None) # 时间，格式必须YYYY-mm
                if DEBUG:
                    logging.info('province_id:%s,ele_id:%s,vol_id:%s,year_month:%s'%(province_id,ele_id,vol_id,year_month))
                data,filte = [],[ForecasePrice.is_use==1,ForecasePrice.part_id==1]
                if province_id:
                    filte.append(ForecasePrice.province_id==province_id)
                if ele_id:
                    filte.append(ForecasePrice.ele_id==ele_id)
                if vol_id:
                    filte.append(ForecasePrice.vol_id==vol_id)
                if year_month:
                    filte.append(ForecasePrice.year_month==year_month[:7])
                total = user_session.query(func.count(ForecasePrice.id)).filter(*filte).scalar()
                pages = user_session.query(ForecasePrice).filter(*filte).order_by(ForecasePrice.year_month.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTotalSuccess(data,total)
            elif kt=='GetCustomer':
                # name = self.get_argument('name',None) # 姓名
                # company = self.get_argument('company',None) # 公司名称
                # address = self.get_argument('address',None) # 地址
                startTime = self.get_argument('startTime',None) # 开始时间
                endTime = self.get_argument('endTime',None) # 截止时间
                if DEBUG:
                    logging.info("endTime:%s,startTime:%s"%(endTime,startTime))
                data,filte = [],[]
                if startTime:
                    filte.append(ForecaseCustomer.op_ts>=startTime)
                if endTime:
                    filte.append(ForecaseCustomer.op_ts<=endTime)
                total = user_session.query(func.count(ForecaseCustomer.id)).filter(*filte).scalar()
                pages = user_session.query(ForecaseCustomer).filter(*filte).order_by(ForecaseCustomer.op_ts.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTotalSuccess(data,total)

        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    # @tornado.web.authenticated
    def post(self,kt):
        # self.refreshSession()
        try:
            if kt=='AddCustomer':
                name = self.get_argument('name',None) # 姓名
                phone_no = self.get_argument('phone_no',None) # 手机号
                company = self.get_argument('company',None) # 公司名称
                address = self.get_argument('address',None) # 地址
                if DEBUG:
                    logging.info("name:%s,phone_no:%s,company:%s,address:%s"%(name,phone_no,company,address))
                if not name or not phone_no or not company:
                    return self.customError('参数不完成')
                if not judge_phone(phone_no):
                    return self.customError('请输入正确手机号')
                page = user_session.query(ForecaseCustomer).filter(ForecaseCustomer.phone_no==phone_no).first()
                if page:
                    return self.customError('手机号已存在')
                p = ForecaseCustomer(name=name,phone_no=phone_no,company=company,address=address,op_ts=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()

                return self.returnTypeSuc('')
          
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
        user_session.close()
    

