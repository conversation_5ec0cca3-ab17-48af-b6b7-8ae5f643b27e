# import concurrent.futures
# import datetime
import json
import paho.mqtt.client as mqtt
# from copy import deepcopy
#
from TianLuAppBackend import settings
from django_redis import get_redis_connection
# from apis.statistics_apis.db_link import time_range_by_dwd_for_web, time_range_by_dwd
from apis.user import models
# from common import common_response_code
# from common.common_fields import TypeOptions
# from common.utils import select_project_subitems_by_name, accumulate_dic
# from tools.get_ac_http import ChargeDischargeCount, ChargeDischargeType, newclassification, \
#     get_common_results_from_dwd_measure_pcs_data_storage, \
#     get_common_results_for_soc_average_from_dwd_measure_bms_data_storage_3, \
#     get_common_results_2_from_ads_report_chag_disg_data
#
# success_dic = {
#     "code": common_response_code.SUCCESS,
#     "data": {
#         "message": "success",
#         "detail": {
#             "chart_data": None,
#             "table_data": None
#         },
#         "keys1": [],
#         "keys2": []
#     },
# }
#
# delta = datetime.timedelta(days=1)
#
# error_log = settings.ERROR_LOG
#
#
# def get_shortest_list(time_array):
#     one_time_list = []
#     shortest_length = min(len(lst) for lst in time_array)
#     for i in time_array:
#         if len(i) == shortest_length:
#             one_time_list = i
#             break
#
#     return one_time_list
#
#
# def tem_get_project_p(master_station_item, start_day, end_day):
#     temp_title = master_station_item[1]  # 宁波朗盛001
#     # station_instance = models.StationDetails.objects.filter(id=station_item[0]).first()
#     master_station = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#     units_inst = models.Unit.objects.filter(station__master_station=master_station).all()
#
#     last_result = []
#     temp_array = []
#     for index, unit in enumerate(units_inst):
#         station_english_name = unit.station.english_name
#         station_app = unit.station.app
#         pcs = unit.pcs
#         start_time = str(start_day)
#         end_time = str(end_day).replace('00:00:00', '23:59:59')
#         start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#         end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#         # error_log.error(f'start:{start_datatime}, end:{end_datatime}')
#         result = time_range_by_dwd(station_english_name, 'measure', 'pcs', pcs,
#                                               start_datatime, end_datatime, 'P', 'Q')
#         # error_log.error(f'{result}')
#         if result:
#             # for idx, one in enumerate(result):
#             #     if not one['P']:
#             #         one['P'] = 0
#             #     if index == 0:
#             #         last_result.append(one)
#             #     else:
#             #         if idx < len(last_result) and idx < len(result):
#             #             last_result[idx]['P'] = round(last_result[idx]['P'] + one['P'], 1)
#             temp_array.append(result)
#     # print(78, temp_array, units_inst, master_station_item[1])
#     last_result = get_common_results_from_dwd_measure_pcs_data_storage(temp_array)
#     return last_result
#
#
# def get_project_p(project_name, start_day, end_day):
#     """
#     查询项目有功功率
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     history_data = dict()
#     temp_list = list()
#     temp_array = list()
#     history_data['time'] = list()
#     # for station_item in total_stations.items():
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for master_station_item in total_master_stations.items():
#             future = executor.submit(tem_get_project_p, master_station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             temp_array.append(f.result())
#
#     if temp_array:
#         results = get_common_results_from_dwd_measure_pcs_data_storage(temp_array)
#
#         if results:
#             temp_list = [i['P'] for i in results] if results else []
#             history_data["time"] = [i['time'] for i in results] if results else []
#
#             # temp_list = [i['P'] for i in f.result()] if f.result() else []
#             # history_data["time"] = [i['time'] for i in f.result()] if f.result() else []
#
#     # new_list = [round(sum(x), 1) for x in zip(*temp_list)]
#     history_data['title'] = "有功功率（kW）"
#     history_data['p_value'] = temp_list
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def tem_get_station_p(master_station_item, start_day, end_day):
#     temp_title = master_station_item[1]  # 宁波朗盛001
#     # station_instance = models.StationDetails.objects.filter(id=station_item[0]).first()
#     master_station = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#     units_inst = models.Unit.objects.filter(station__master_station=master_station).all()
#
#     last_result = []
#     temp_array = []
#     for index, unit in enumerate(units_inst):
#         station_english_name = unit.station.english_name
#         # station_app = unit.station.app
#         pcs = unit.pcs
#         start_time = str(start_day)
#         end_time = str(end_day).replace('00:00:00', '23:59:59')
#         start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#         end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#         # error_log.error(f'start:{start_datatime}, end:{end_datatime}')
#         result = time_range_by_dwd(station_english_name, 'measure', 'pcs', pcs,
#                                           start_datatime, end_datatime, 'P', 'Q')
#         # error_log.error(f'{result}')
#         if result:
#         # for idx, one in enumerate(result):
#         #     if not one['P']:
#         #         one['P'] = 0
#         #     if index == 0:
#         #         last_result.append(one)
#         #     else:
#         #         if idx < len(last_result) and idx < len(result):
#         #             last_result[idx]['P'] = round(last_result[idx]['P'] + one['P'], 1)
#             temp_array.append(result)
#     last_result = get_common_results_from_dwd_measure_pcs_data_storage(temp_array)
#
#     station_dict = {
#         "station_id": master_station_item[0],
#         "station_name": temp_title,
#         "title": "有功功率（kW）",
#         "p_value": [i['P'] for i in last_result] if last_result else []
#     }
#     return station_dict, last_result
#
#
# def get_station_p(project_name, start_day, end_day):
#     """
#     查询并网点有功功率
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     temp_arr = list()
#
#     # for station_item in total_stations.items():
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for station_item in total_master_stations.items():
#             future = executor.submit(tem_get_station_p, station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             station_dict, last_result = f.result()
#             temp_arr.append(station_dict)
#             history_data["time"] = [i['time'] for i in last_result] if last_result else []
#     temp_arr = sorted(temp_arr, key=lambda x: x['station_id'])
#     history_data['array'] = temp_arr
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     return success_dic
#
#
# def tem_get_unit_p(unit_item, start_day, end_day):
#     temp_title = unit_item[1]  # "宁波朗声-储能单元001"
#     unit_instance = models.Unit.objects.filter(id=unit_item[0]).first()
#     station_english_name = unit_instance.station.english_name
#     # station_app = unit_instance.station.app
#     pcs = unit_instance.pcs
#
#     start_time_ = str(start_day)
#     end_time_ = str(end_day).replace('00:00:00', '23:59:59')
#     start_datatime = datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S")
#     end_datatime = datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
#     result = time_range_by_dwd_for_web(station_english_name, 'measure', 'pcs', pcs,
#                                       start_datatime, end_datatime, 'P', 'Q')
#
#     unit_dict = {
#         "unit_id": unit_item[0],
#         "unit_name": temp_title,
#         "title": "有功功率（kW）",
#         "p_value": [i['P'] for i in result] if result else []
#     }
#
#     return unit_dict, result
#
#
# def get_unit_p(project_name, start_day, end_day):
#     """
#     查询储能单元有功功率
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(tem_get_unit_p, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             # temp_list.append([i['P'] for i in f.result()] if f.result() else [])
#             # history_data["time"] = [i['time'] for i in f.result()] if f.result() else []
#             unit_dict, last_result = f.result()
#             temp_arr.append(unit_dict)
#             history_data["time"] = [i['time'] for i in last_result] if last_result else []
#
#         temp_arr = sorted(temp_arr, key=lambda x: x['unit_id'])
#
#         history_data["array"] = temp_arr
#         # history_data["time"] = [i['time'] for i in result] if result else []
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def tem_get_project_soc(unit_item, start_day, end_day):
#     unit_inst = models.Unit.objects.filter(id=unit_item[0]).first()
#     station_english_name = unit_inst.station.english_name
#     station_app = unit_inst.station.app
#     bms = unit_inst.bms
#     start_time = str(start_day)
#     end_time = str(end_day).replace('00:00:00', '23:59:59')
#     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#     result = time_range_by_dwd(station_english_name, 'measure', 'bms_3', bms,
#                                           start_datatime, end_datatime, 'soc')
#     return result
#
#
# def get_project_soc(project_name, start_day, end_day):
#     """
#     查询项目SOC
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     history_data = dict()
#     temp_list_ = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(tem_get_project_soc, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             temp_list_.append(f.result())
#
#     # ======>优化注释
#     # time_li = list()
#     # tem_array = list()
#     # min_list_len = min(len(x) for x in temp_list_)
#     # for ind, item in enumerate(temp_list_):
#     #     v_li = []
#     #     for i in item:
#     #         soc = i['soc'] if i['soc'] else 0
#     #         v_li.append(soc)
#     #     # if ind == 0:
#     #     #     v_li = [i['soc'] for i in item]
#     #     # else:
#     #     #     v_li = [i['soc'] for i in item]
#     #     tem_array.append(v_li)
#     #
#     #     if len(item) == min_list_len:
#     #         time_li = [i['time'] for i in item]
#     #
#     # las_arr = [round(sum(x)/len(x), 1) for x in zip(*tem_array)]
#     # ======> end
#
#     # 把每个储能单元对应时间的soc值求平均值
#     # las_arr = list()
#     # for j in range(0, len(tem_array[0])):
#     #     te_list = [float(t[j]) for t in tem_array]
#     #     a = round(sum(te_list) / len(te_list), 1)
#     #     las_arr.append(a)
#
#     last_result = get_common_results_for_soc_average_from_dwd_measure_bms_data_storage_3(temp_list_)
#     time_li = [i['time'] for i in last_result]
#     las_arr = [i['soc'] for i in last_result]
#
#     history_data['time'] = time_li
#     history_data['soc'] = las_arr
#     history_data['title'] = "SOC(%)"
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def temp_get_station_soc(station_instance, unit_inst, start_day, end_day):
#     # unit_inst = models.Unit.objects.filter(id=unit_item[0]).first()
#     station_english_name = station_instance.english_name
#     station_app = station_instance.app
#     bms = unit_inst.bms
#     start_time = str(start_day)
#     end_time = str(end_day).replace('00:00:00', '23:59:59')
#     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#     result = time_range_by_dwd(station_english_name, 'measure', 'bms_3', bms,
#                                start_datatime, end_datatime, 'soc')
#     return result
#
#
# def get_station_soc(project_name, start_day, end_day):
#     """
#     查询并网点SOC
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#
#     temp_arr = list()
#     time_list = list()
#
#     _last_array = list()
#
#     for master_station_item in total_master_stations.items():
#         station_index = list(total_master_stations.items()).index(master_station_item)
#         temp_title = master_station_item[1]  # 宁波朗盛001
#         # station_instance = models.StationDetails.objects.filter(id=station_item[0]).first()
#         master_station = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#         station_dict = {
#             "station_id": master_station_item[0],
#             "station_name": temp_title,
#             "title": "SOC(%)",
#             "soc_list": []
#         }
#
#         unit_instances = models.Unit.objects.filter(station__master_station_id=master_station_item[0]).all()
#         temp_list_ = list()
#
#         with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#             futures = list()
#             for unit_inst in unit_instances:
#                 future = executor.submit(temp_get_station_soc, unit_inst.station, unit_inst, start_day, end_day)
#                 futures.append(future)
#             # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#             for f in concurrent.futures.as_completed(futures):
#                 temp_list_.append(f.result())
#
#         # ======>优化注释
#         # tem_array = list()
#         # min_list_len = min(len(x) for x in temp_list_)
#         # for ind, item in enumerate(temp_list_):
#         #     v_li = list()
#         #     for i in item:
#         #         soc = i['soc'] if i['soc'] else 0
#         #         v_li.append(soc)
#         #     if station_index == 0 and len(item) == min_list_len:
#         #         time_list = [i['time'] for i in item]
#         #     tem_array.append(v_li)
#         #
#         # # 把每个储能单元对应时间的soc值求平均值
#         # las_arr = [round(sum(x) / len(x), 1) for x in zip(*tem_array)]
#         # =======> end
#
#         # las_arr = list()
#         # for j in range(0, len(tem_array[0])):
#         #     te_list = [float(t[j]) for t in tem_array]
#         #     a = round(sum(te_list) / len(te_list), 1)
#         #     las_arr.append(a)
#
#         last_result = get_common_results_for_soc_average_from_dwd_measure_bms_data_storage_3(temp_list_)
#         _last_array.append(last_result)
#         # time_li = [i['time'] for i in last_result]
#         las_arr = [i['soc'] for i in last_result]
#
#         station_dict['soc_list'] = las_arr
#
#         temp_arr.append(station_dict)
#
#     # 找到n个子列表中都存在的时间点
#     common_times = set(_last_array[0][i]["time"] for i in range(len(_last_array[0])))
#     for sublist in _last_array[1:]:
#         common_times.intersection_update(entry["time"] for entry in sublist)
#     time_list = sorted(list(common_times))
#
#     for item in temp_arr:
#         if len(item['soc_list']) > len(time_list):
#             item['soc_list'] = item['soc_list'][:len(time_list)]
#
#     temp_arr = sorted(temp_arr, key=lambda x: x['station_id'])
#
#     history_data['time'] = time_list
#     history_data['array'] = temp_arr
#     history_data['title'] = "SOC(%)"
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def temp_get_unit_soc(unit_item, start_day, end_day):
#     temp_title = unit_item[1]  # "宁波朗声-储能单元001"
#
#     unit_dict = {
#         "unit_id": unit_item[0],
#         "unit_name": temp_title,
#         "title": "SOC(%)",
#         "soc_list": []
#     }
#
#     unit_inst = models.Unit.objects.filter(id=unit_item[0]).first()
#     station_english_name = unit_inst.station.english_name
#     station_app = unit_inst.station.app
#     bms = unit_inst.bms
#     start_time = str(start_day)
#     end_time = str(end_day).replace('00:00:00', '23:59:59')
#     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#     results = time_range_by_dwd_for_web(station_english_name, 'measure', 'bms_3', bms,
#                                start_datatime, end_datatime, 'soc')
#     error_log.debug(f'temp_title: {results}')
#
#     if results:
#         for i in results:
#             soc = round(float(i['soc']), 1) if i['soc'] != '--' else '--'
#             unit_dict['soc_list'].append(soc)
#
#     return unit_dict, results
#
#
# def get_unit_soc(project_name, start_day, end_day):
#     """
#     查询并网点SOC
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     time_list = list()
#     history_data = dict()
#     # temp_response_details = list()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(temp_get_unit_soc, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             unit_dict, result = f.result()
#
#             temp_arr.append(unit_dict)
#             # if list(total_units.items()).index(unit_item) == 0:
#             time_list = [i['time'] for i in result]
#
#     temp_arr = sorted(temp_arr, key=lambda x: x['unit_id'])
#     history_data['time'] = time_list
#     history_data['array'] = temp_arr
#     # history_data['title'] = "SOC(%)"
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def tem_get_project_charge_discharge_by_hour(master_station_item, start_day, end_day):
#     target_day = start_day
#     # temp_title = station_item[1]  # 宁波朗盛001
#     master_station = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#
#     time_list = list()
#
#     temp_array_charge = list()
#     temp_array_discharge = list()
#     temp_array_soc = list()
#     temp_array_type = list()
#
#     # 备用注释
#     # while target_day.date() <= end_day.date():
#     #     count_ins = ChargeDischargeCount(target_day)
#     #     details = count_ins.new_get_stations(master_station)
#     #     for detail in details:
#     #         temp_array_charge.append(detail['charge'])
#     #         temp_array_discharge.append(detail['discharge'])
#     #         temp_array_soc.append(detail['soc'])
#     #         temp_array_type.append(detail['type'])
#     #         time_list.append(detail['time'])
#     #     target_day += delta
#     # =======> end
#
#     count_ins = ChargeDischargeCount()
#     details = count_ins.new_get_stations(master_station, target_day.date(), end_day.date())
#     # for detail in details:
#     #     temp_array_charge.append(detail['charge'])
#     #     temp_array_discharge.append(detail['discharge'])
#     #     temp_array_soc.append(detail['soc'])
#     #     temp_array_type.append(detail['type'])
#     #     time_list.append(detail['time'])
#     #
#     # return temp_array_charge, temp_array_discharge, temp_array_soc, temp_array_type, time_list
#
#     return details
#
#
# def get_project_charge_discharge_by_hour(project_name, start_day, end_day, type_='all'):
#     """
#     查询项目逐时充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     # type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#     history_data = dict()
#
#     temp_list_charge = list()
#     temp_list_discharge = list()
#     temp_list_soc = list()
#     temp_list_type = list()
#
#     temp_array = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for master_station_item in total_master_stations.items():
#             future = executor.submit(tem_get_project_charge_discharge_by_hour, master_station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             # temp_array_charge, temp_array_discharge, temp_array_soc, temp_array_type, time_list_ = f.result()
#             # temp_list_charge.append(temp_array_charge)
#             # temp_list_discharge.append(temp_array_discharge)
#             # temp_list_soc.append(temp_array_soc)
#             # temp_list_type = temp_array_type
#
#             details = f.result()
#             temp_array.append(details)
#
#     results = get_common_results_2_from_ads_report_chag_disg_data(temp_array)
#     results = sorted(results, key=lambda x: x['time']) if results else []
#
#     # new_list_charge = [round(sum(x), 1) for x in zip(*temp_list_charge)]
#     # new_list_discharge = [round(sum(x), 1) for x in zip(*temp_list_discharge)]
#     # new_list_soc = [abs(round(sum(x) / len(x), 1)) for x in zip(*temp_list_soc)]
#     # new_list_type = temp_list_type
#
#     # history_data['type'] = new_list_type
#     # history_data['time'] = time_list_
#     # history_data['charge'] = new_list_charge
#     # history_data['charge_title'] = "充电量（kWh）"
#     # history_data['discharge'] = new_list_discharge
#     # history_data['discharge_title'] = "放电量（kWh）"
#     # history_data['soc'] = new_list_soc
#     # history_data['soc_title'] = "SOC差值（%）"
#
#     history_data['type'] = [i['type'] for i in results] if results else []
#     history_data['time'] = [i['time'] for i in results] if results else []
#     history_data['charge'] = [i['charge'] for i in results] if results else []
#     history_data['charge_title'] = "充电量（kWh）"
#     history_data['discharge'] = [i['discharge'] for i in results] if results else []
#     history_data['discharge_title'] = "放电量（kWh）"
#     history_data['soc'] = [i['soc'] for i in results] if results else []
#     history_data['soc_title'] = "SOC差值（%）"
#
#     copy_history_data = deepcopy(history_data)
#     # 图表数据支持尖峰平谷筛选
#     if type_ == 'all':
#         chart_data_ = history_data
#     elif type_ == 'spike':
#         for index_ in range(0, len(history_data['time'])):
#             if history_data['type'][index_] != 2:
#                 history_data['charge'][index_] = 0
#                 history_data['discharge'][index_] = 0
#                 history_data['soc'][index_] = 0
#     elif type_ == 'peak':
#         for index_ in range(0, len(history_data['time'])):
#             if history_data['type'][index_] != 1:
#                 history_data['charge'][index_] = 0
#                 history_data['discharge'][index_] = 0
#                 history_data['soc'][index_] = 0
#     elif type_ == 'flat':
#         for index_ in range(0, len(history_data['time'])):
#             if history_data['type'][index_] != 0:
#                 history_data['charge'][index_] = 0
#                 history_data['discharge'][index_] = 0
#                 history_data['soc'][index_] = 0
#     elif type_ == 'valley':
#         for index_ in range(0, len(history_data['time'])):
#             if history_data['type'][index_] != -1:
#                 history_data['charge'][index_] = 0
#                 history_data['discharge'][index_] = 0
#                 history_data['soc'][index_] = 0
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = copy_history_data
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     return success_dic
#
#
# def tem_get_station_charge_discharge_by_hour(master_station_item, start_day, end_day):
#     temp_time_list = list()
#     type_list = list()  # 尖峰平谷标识列表
#     target_day = start_day
#     temp_title = master_station_item[1]  # 宁波朗盛001
#     station_instance = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#
#     station_dict = {
#         "station_id": master_station_item[0],
#         "station_name": temp_title,
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "title_soc": "SOC(%)",
#         "charge_list": [],
#         "discharge_list": [],
#         "soc_list": []
#     }
#
#     # while target_day.date() <= end_day.date():
#     #     count_ins = ChargeDischargeCount(target_day)
#     #     details = count_ins.new_get_stations(station_instance)
#     #     for detail in details:
#     #         station_dict['charge_list'].append(round(detail['charge'], 1))
#     #         station_dict['discharge_list'].append(round(detail['discharge'], 1))
#     #         station_dict['soc_list'].append(round(abs(detail['soc']), 1))
#     #         type_list.append(detail['type'])
#     #
#     #         # t_time = target_day.strftime("%Y-%m-%d") + ' ' + datetime.datetime.strptime(detail['time'],
#     #         #                                                                             '%H:%M:%S').strftime("%H:%M")
#     #         t_time = detail['time']
#     #         # if t_time not in time_list:
#     #         temp_time_list.append(t_time)
#     #     target_day += delta
#
#     count_ins = ChargeDischargeCount()
#     details = count_ins.new_get_stations(station_instance, target_day.date(), end_day.date())
#     if details:
#         for item in details:
#             station_dict['charge_list'].append(round(item['charge'], 1))
#             station_dict['discharge_list'].append(round(item['discharge'], 1))
#             station_dict['soc_list'].append(round(abs(item['soc']), 1))
#             type_list.append(item['type'])
#         temp_time_list = [item['time'] for item in details]
#
#     return temp_time_list, station_dict, type_list
#
#
# def get_station_charge_discharge_by_hour(project_name, start_day, end_day, type_='all', tem_station_id=0):
#     """
#     查询并网点逐时充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     if int(tem_station_id) != 0 and int(tem_station_id) not in total_master_stations.keys():
#         return {
#                 "code": common_response_code.ERROR,
#                 "data": {
#                     "message": "error",
#                     "detail": "运行数据：并网点 ID 不是选择的项目的并网点"
#                 }
#             }
#
#     history_data = dict()
#     time_array = list()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for station_item in total_master_stations.items():
#             future = executor.submit(tem_get_station_charge_discharge_by_hour, station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             temp_time_list, station_dict, type_list = f.result()
#             time_array.append(temp_time_list)
#             temp_arr.append(station_dict)
#
#     time_list = get_shortest_list(time_array)
#
#     temp_arr = sorted(temp_arr, key=lambda x: x['station_id'])
#     history_data['time'] = time_list
#     history_data['array'] = temp_arr
#
#     if int(tem_station_id) == 0:  # 并网点选择全部时
#         new_charge_detail = list()
#         new_discharge_detail = list()
#         new_soc_detail = list()
#         for t in time_list:
#             index = time_list.index(t)
#             charge_t_v = 0  # 某时全部并网点的充电量
#             discharge_t_v = 0  # 某时全部并网点的放电量
#             soc_t_v = 0  # 某时全部储能单元的SCO
#             soc_t_v_list = list()
#             for ind, v in enumerate(total_master_stations.keys()):
#                 u_t_charge = history_data['array'][ind]['charge_list'][index] if len(
#                     history_data['array'][ind]['charge_list']) else 0
#                 u_t_discharge = history_data['array'][ind]['discharge_list'][index] if len(
#                     history_data['array'][ind]['discharge_list']) else 0
#                 soc_t_v_list.append(history_data['array'][ind]['soc_list'][index] if len(
#                     history_data['array'][ind]['soc_list']) else 0)
#                 charge_t_v += u_t_charge
#                 discharge_t_v += u_t_discharge
#             soc_t_v = sum(soc_t_v_list) / len(soc_t_v_list)
#             new_charge_detail.append(round(abs(charge_t_v), 2))
#             new_discharge_detail.append(round(abs(discharge_t_v), 2))
#             new_soc_detail.append(round(abs(soc_t_v), 2))
#
#         # 图表数据支持尖峰平谷筛选
#         if type_ == 'spike':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 2:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'peak':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 1:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'flat':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 0:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'valley':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != -1:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#
#         chart_data = {
#             "time": history_data['time'],
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "charge_detail": new_charge_detail,
#             "discharge_detail": new_discharge_detail,
#             "soc_detail": new_soc_detail
#         }
#
#     else:  # 选择某个并网点时
#         target_index = sorted(list(total_master_stations.keys())).index(int(tem_station_id))
#         t_charge_list = deepcopy(history_data["array"][target_index]['charge_list'])
#         t_discharge_list = deepcopy(history_data["array"][target_index]['discharge_list'])
#         t_soc_list = deepcopy(history_data["array"][target_index]['soc_list'])
#
#         if type_ == 'spike':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 2:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'peak':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 1:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'flat':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 0:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'valley':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != -1:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#
#         chart_data = {
#             "time": history_data['time'],
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "charge_detail": t_charge_list,
#             "discharge_detail": t_discharge_list,
#             "soc_detail": t_soc_list
#         }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in total_master_stations.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         }
#     )
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#     return success_dic
#
#
# def temp_get_unit_charge_discharge_by_hour(unit_item, start_day, end_day):
#     tem_time_list = list()
#     target_day = start_day
#     temp_title = unit_item[1]  # "宁波朗声-储能单元001"
#     unit_instance = models.Unit.objects.filter(id=unit_item[0]).first()
#
#     type_list = list()
#
#     unit_dict = {
#         "unit_id": unit_item[0],
#         "unit_name": temp_title,
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "title_soc": "SOC(%)",
#         "charge_list": [],
#         "discharge_list": [],
#         "soc_list": []
#     }
#
#     # while target_day.date() <= end_day.date():
#     count_ins = ChargeDischargeCount()
#     details = count_ins.new_get_unit(unit_instance, start_day.date(), end_day.date())
#     for detail in details:
#         unit_dict['charge_list'].append(detail['charge'])
#         unit_dict['discharge_list'].append(detail['discharge'])
#         unit_dict['soc_list'].append(abs(detail['soc']) if detail['soc'] != '--' else '--')
#         type_list.append(detail['type'])
#
#         t_time = detail['time']
#         # if t_time not in time_list:
#         tem_time_list.append(t_time)
#
#         # target_day += delta
#         # time_array.append(tem_time_list)
#     return unit_dict, tem_time_list, type_list
#
#
# def get_unit_charge_discharge_by_hour(project_name, start_day, end_day, type_='all', tem_unit_id=0):
#     """
#     查询储能单元逐时充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     # type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#     # tem_unit_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001-储能单元1”
#
#     # time_list = list()
#     time_array = list()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(temp_get_unit_charge_discharge_by_hour, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             unit_dict, tem_time_list, type_list = f.result()
#             time_array.append(tem_time_list)
#             temp_arr.append(unit_dict)
#
#     time_list = get_shortest_list(time_array)
#     history_data['time'] = time_list
#     temp_arr = sorted(temp_arr, key=lambda k: k['unit_id'])
#     history_data['array'] = temp_arr
#
#     if int(tem_unit_id) == 0:  # 储能单元选择全部时
#         new_charge_detail = list()
#         new_discharge_detail = list()
#         new_soc_detail = list()
#         for t in time_list:
#             index = time_list.index(t)
#             charge_t_v = 0  # 某时全部储能单元的充电量
#             discharge_t_v = 0  # 某时全部储能单元的放电量
#
#             soc_list = list()
#             for ind, v in enumerate(total_units.keys()):
#                 u_t_charge = history_data['array'][ind]['charge_list'][index] if len(
#                     history_data['array'][ind]['charge_list']) else '--'
#                 u_t_discharge = history_data['array'][ind]['discharge_list'][index] if len(
#                     history_data['array'][ind]['discharge_list']) else '--'
#                 soc_list.append(
#                     history_data['array'][ind]['soc_list'][index] if len(history_data['array'][ind]['soc_list']) else '--')
#
#                 if u_t_charge != '--' and charge_t_v != '--':
#                     charge_t_v += u_t_charge
#                 else:
#                     charge_t_v = '--'
#
#                 if u_t_discharge != '--' and discharge_t_v != '--':
#                     discharge_t_v += u_t_discharge
#                 else:
#                     discharge_t_v = '--'
#
#             if '--' not in soc_list:
#                 soc_t_v = sum(soc_list) / len(soc_list)
#             else:
#                 soc_t_v = '--'
#
#             charge_t_v = round(charge_t_v, 1)
#             discharge_t_v = round(discharge_t_v, 1)
#
#             new_charge_detail.append(charge_t_v)
#             new_discharge_detail.append(discharge_t_v)
#             new_soc_detail.append(round(soc_t_v, 1) if soc_t_v != '--' else '--')
#
#         if type_ == 'spike':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 2:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'peak':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 1:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'flat':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 0:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#         elif type_ == 'valley':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != -1:
#                     new_charge_detail[index_] = 0
#                     new_discharge_detail[index_] = 0
#                     new_soc_detail[index_] = 0
#
#         chart_data = {
#             "time": history_data['time'],
#             "charge_detail": new_charge_detail,
#             "discharge_detail": new_discharge_detail,
#             "soc_detail": new_soc_detail
#         }
#
#     else:  # 选择某个储能单元时
#         target_index = sorted(list(total_units.keys())).index(int(tem_unit_id))
#         t_charge_list = deepcopy(history_data['array'][target_index]['charge_list'])
#         t_discharge_list = deepcopy(history_data['array'][target_index]['discharge_list'])
#         t_soc_list = deepcopy(history_data['array'][target_index]['soc_list'])
#
#         if type_ == 'spike':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 2:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'peak':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 1:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'flat':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != 0:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#         elif type_ == 'valley':
#             for index_ in range(0, len(type_list)):
#                 if type_list[index_] != -1:
#                     t_charge_list[index_] = 0
#                     t_discharge_list[index_] = 0
#                     t_soc_list[index_] = 0
#
#         chart_data = {
#             "time": history_data['time'],
#             "charge_detail": t_charge_list,
#             "discharge_detail": t_discharge_list,
#             "soc_detail": t_soc_list
#         }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in total_units.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         }
#     )
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#     return success_dic
#
#
# def tem_get_project_charge_discharge_count(station_item, start_day, end_day):
#     # target_day = start_day
#     # temp_title = station_item[1]  # 宁波朗盛001
#     master_station_instance = models.MaterStation.objects.filter(id=station_item[0], is_delete=0).first()
#
#     # temp_array = list()
#     # while target_day.date() <= end_day.date():
#     #     count_ins = ChargeDischargeType(target_day)
#     #     day_detail = count_ins.new_get_stations(master_station_instance)
#     #     day_detail = newclassification(day_detail)
#     #     """"
#     #         return_dic = {
#     #                         "spike": {"total_charge": 0, "total_discharge": 0, "times": []},  # 尖峰
#     #                         "peak": {"total_charge": 0, "total_discharge": 0, "times": []},  # 峰
#     #                         "flat": {"total_charge": 0, "total_discharge": 0, "times": []},  # 平
#     #                         "valley": {"total_charge": 0, "total_discharge": 0, "times": []},  # 谷
#     #                     }
#     #     """
#     #     temp_array.append(day_detail)
#     #     target_day += delta
#     #
#     # # 将n天的数据合并
#     # days_total_detail = accumulate_dic(temp_array)
#     # return days_total_detail
#
#     temp_array = list()
#     # while target_day.date() <= end_day.date():
#     count_ins = ChargeDischargeType()
#     day_detail = count_ins.new_get_stations(master_station_instance, start_day.date(), end_day.date())
#     day_detail = newclassification(day_detail)
#     """"
#         return_dic = {
#                         "spike": {"total_charge": 0, "total_discharge": 0, "times": []},  # 尖峰
#                         "peak": {"total_charge": 0, "total_discharge": 0, "times": []},  # 峰
#                         "flat": {"total_charge": 0, "total_discharge": 0, "times": []},  # 平
#                         "valley": {"total_charge": 0, "total_discharge": 0, "times": []},  # 谷
#                     }
#     """
#
#     # 将n天的数据合并
#     # days_total_detail = accumulate_dic(temp_array)
#     return day_detail
#
#
# def get_project_charge_discharge_count(project_name, start_day, end_day, type_='all'):
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     history_data = dict()
#     chart_data = dict()
#     # type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#     # tem_station_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001”
#
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for station_item in total_master_stations.items():
#             future = executor.submit(tem_get_project_charge_discharge_count, station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             temp_arr.append(f.result())
#
#     all_total_detail = accumulate_dic(temp_arr)  # 将n个电站的数据合并
#     history_data['data_detail'] = all_total_detail
#     history_data['charge_title'] = "充电量（kWh）"
#     history_data['discharge_title'] = "充电量（kWh）"
#
#     # 图表数据
#     temp_list_2 = history_data['data_detail']
#
#     if type_ == 'all':
#         chart_data_ = temp_list_2
#     else:
#         chart_data_ = {type_: temp_list_2[type_]}
#
#     chart_data['title'] = "充/放电量[kWh]"
#     chart_data['data_detail'] = chart_data_
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     return success_dic
#
#
# def tem_get_station_charge_discharge_count(station_item, start_day, end_day):
#     target_day = start_day
#     temp_title = station_item[1]  # 宁波朗盛001
#     station_instance = models.MaterStation.objects.filter(id=station_item[0], is_delete=0).first()
#
#     # temp_array = list()
#     # while target_day.date() <= end_day.date():
#     #     count_ins = ChargeDischargeType(target_day)
#     #     day_detail = count_ins.new_get_stations(station_instance)
#     #     day_detail = newclassification(day_detail)
#     #     """"
#     #         return_dic = {
#     #                         "spike": {"total_charge": 0, "total_discharge": 0, "times": []},  # 尖峰
#     #                         "peak": {"total_charge": 0, "total_discharge": 0, "times": []},  # 峰
#     #                         "flat": {"total_charge": 0, "total_discharge": 0, "times": []},  # 平
#     #                         "valley": {"total_charge": 0, "total_discharge": 0, "times": []},  # 谷
#     #                     }
#     #     """
#     #     temp_array.append(day_detail)
#     #     target_day += delta
#     #
#     # # 将n天的数据合并
#     # total_detail = accumulate_dic(temp_array)
#
#     count_ins = ChargeDischargeType(target_day)
#     day_detail = count_ins.new_get_stations(station_instance, start_day.date(), end_day.date())
#     total_detail = newclassification(day_detail)
#
#     d_dict = {
#             "title": temp_title,
#             "data_detail": total_detail
#         }
#
#     return station_item, total_detail, d_dict
#
#
# def get_station_charge_discharge_count(project_name, start_day, end_day, type_='all', tem_station_id=0):
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for station_item in total_master_stations.items():
#             future = executor.submit(tem_get_station_charge_discharge_count, station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             station_item, total_detail, d_dict = f.result()
#
#             # print(1248, tem_station_id, station_item)
#             if int(tem_station_id) != 0:  # 并网点选择的不是”全部“时
#                 if station_item[0] == int(tem_station_id):
#                     if type_ == 'all':
#                         chart_data = total_detail
#                     else:
#                         chart_data = {type_: total_detail[type_]}
#
#             history_data.append(d_dict)
#
#     history_data = sorted(history_data, key=lambda x: x['title'])
#
#     # 图表数据
#     if int(tem_station_id) == 0:
#         temp_list_2 = [item['data_detail'] for item in history_data]
#         all_data_detail = accumulate_dic(temp_list_2)
#         if type_ == 'all':
#             chart_data = all_data_detail
#         else:
#             chart_data = {type_: all_data_detail[type_]}
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in total_master_stations.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         }
#     )
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#
#     return success_dic
#
#
# def tem_get_unit_charge_discharge_count(unit_item, start_day, end_day):
#     target_day = start_day
#     # temp_unit_detail_dict = list()
#
#     temp_title = unit_item[1]  # "宁波朗声-储能单元001"
#     unit_instance = models.Unit.objects.filter(id=unit_item[0]).first()
#
#     temp_array = list()
#     # while target_day.date() <= end_day.date():
#     #     count_ins = ChargeDischargeType(target_day)
#     #     day_detail = count_ins.new_get_unit(unit_instance)
#     #     day_detail = newclassification(day_detail)
#     #     """"
#     #         return_dic = {
#     #                         "spike": {"total_charge": 0, "total_discharge": 0, "time": []},  # 尖峰
#     #                         "peak": {"total_charge": 0, "total_discharge": 0, "time": []},  # 峰
#     #                         "flat": {"total_charge": 0, "total_discharge": 0, "time": []},  # 平
#     #                         "valley": {"total_charge": 0, "total_discharge": 0, "time": []},  # 谷
#     #                     }
#     #     """
#     #     temp_array.append(day_detail)
#     #     target_day += delta
#
#     count_ins = ChargeDischargeType(target_day)
#     day_detail = count_ins.new_get_unit(unit_instance, start_day.date(), end_day.date())
#     total_detail = newclassification(day_detail)
#
#     # 将n天的数据合并
#     # total_detail = accumulate_dic(temp_array)
#
#     d_dict = {
#             "title": temp_title,
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "data_detail": total_detail
#         }
#
#     return unit_item, total_detail, d_dict
#
#
# def get_unit_charge_discharge_count(project_name, start_day, end_day, type_='all', tem_unit_id=0):
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     temp_response_details = list()
#     history_data = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(tem_get_unit_charge_discharge_count, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             unit_item, total_detail, d_dict = f.result()
#
#             if int(tem_unit_id) != 0:  # 储能单元选择的不是”全部“时
#                 if unit_item[0] == int(tem_unit_id):
#                     if type_ == 'all':
#                         chart_data = total_detail
#                     else:
#                         chart_data = {type_: total_detail[type_]}
#
#             history_data.append(d_dict)
#
#     history_data = sorted(history_data, key=lambda x: x['title'])
#
#     # 图表数据
#     if int(tem_unit_id) == 0:
#         temp_list_2 = [item['data_detail'] for item in history_data]
#         all_data_detail = accumulate_dic(temp_list_2)
#         if type_ == 'all':
#             chart_data = all_data_detail
#         else:
#             # print(3960, type_, all_data_detail)
#             chart_data = {type_: all_data_detail[type_]}
#
#     chart_data["charge_title"] = "充电量（kWh)"
#     chart_data["discharge_title"] = "放电量（kWh)"
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in total_units.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         }
#     )
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#     return success_dic
#
#
# def tem_get_project_charge_discharge_by_day(station_item, start_day, end_day):
#     days = list()
#     target_day = start_day
#     temp_charge_arr = list()
#     temp_discharge_arr = list()
#
#     station_instance = models.MaterStation.objects.filter(id=station_item[0], is_delete=0).first()
#
#     while target_day.date() <= end_day.date():
#         count_ins = ChargeDischargeCount(target_day)
#         details = count_ins.get_stations(station_instance)
#
#         day_charge = 0  # 每天充电量
#         day_discharge = 0  # 每天放电量
#         for detail in details:
#             day_charge += detail['charge']
#             day_discharge += detail['discharge']
#
#         if target_day.date() not in days:
#             days.append(target_day.date())
#
#         temp_charge_arr.append(round(float(day_charge), 1))
#         temp_discharge_arr.append(round(float(day_discharge), 1))
#
#         target_day += delta
#     return temp_charge_arr, temp_discharge_arr, days
#
#
# def old_get_project_charge_discharge_by_day(project_name, start_day, end_day):
#     """
#     查询项目逐日充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#
#     history_data = dict()
#     days = list()
#     temp_charge_list = list()
#     temp_discharge_list = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(tem_get_project_charge_discharge_by_day, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             temp_charge_arr, temp_discharge_arr, days = f.result()
#             temp_charge_list.append(temp_charge_arr)
#             temp_discharge_list.append(temp_discharge_arr)
#
#     history_data["time"] = days
#
#     new_charge_detail = [round(sum(x), 1) for x in zip(*temp_charge_list)]
#     new_discharge_detail = [round(sum(x), 1) for x in zip(*temp_discharge_list)]
#
#     history_data['charge_detail'] = new_charge_detail
#     history_data['charge_title'] = "充电量（kWh)"
#     history_data['discharge_detail'] = new_discharge_detail
#     history_data['discharge_title'] = "放电量（kWh)"
#     history_data['charge_discharge_effic'] = [round((x / y) * 100, 1) for x, y in
#                                               zip(new_discharge_detail, new_charge_detail) if y != 0]
#     history_data['effic_title'] = "充放电效率（%）"
#
#     chart_data = {
#         "time": history_data['time'],
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "charge_detail": new_charge_detail,
#         "discharge_detail": new_discharge_detail
#     }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def get_project_charge_discharge_by_day(project_name, start_day, end_day):
#     """
#     查询项目逐日充放电量
#     """""
#     days = list()
#     target_day = start_day
#     project = models.Project.objects.filter(name=project_name, is_used=1).first()
#
#     days_charge_detail = list()
#     days_discharge_detail = list()
#     days_dis_charge_effic_ditail = list()
#     # day_charge_soc_diff_sum_list = list()
#     # day_discharge_soc_diff_sum_list = list()
#
#     while target_day.date() <= end_day.date():
#         count_ins = ChargeDischargeCount(target_day)
#         detail = count_ins.new_get_projects(project)
#
#         day_charge = 0  # 每天充电量
#         day_discharge = 0  # 每天放电量
#
#         if detail:
#             day_charge = detail['charge']
#             day_discharge = detail['discharge']
#             chag_soc = detail['chag_soc']
#             disg_soc = detail['disg_soc']
#
#             # 计算充放电效率
#             day_charge_soc_diff_sum = chag_soc
#             day_discharge_soc_diff_sum = disg_soc
#
#             if day_charge_soc_diff_sum and day_discharge_soc_diff_sum and day_charge and day_discharge and day_charge_soc_diff_sum != '--' and day_discharge_soc_diff_sum != '--' and day_charge != '--' and day_discharge != '--':
#                 dis_charge_effic = abs(round((day_discharge / day_discharge_soc_diff_sum) / (
#                         day_charge / day_charge_soc_diff_sum) * 100, 2))
#                 dis_charge_effic = dis_charge_effic if dis_charge_effic <= 100.00 else 100.00
#             elif (day_charge != '--' and day_discharge == '--') or (day_charge and not day_discharge):
#                 dis_charge_effic = 0
#             elif (day_discharge != '--' and day_charge == '--') or (day_discharge and not day_charge):
#                 dis_charge_effic = 100.00
#             else:
#                 dis_charge_effic = 0
#
#             if target_day.date() not in days:
#                 days.append(target_day.date())
#
#             # day_charge_soc_diff_sum_list.append(day_charge_soc_diff_sum)
#             # day_discharge_soc_diff_sum_list.append(day_discharge_soc_diff_sum)
#
#             days_charge_detail.append(round(day_charge, 1))
#             days_discharge_detail.append(round(day_discharge, 1))
#             days_dis_charge_effic_ditail.append(dis_charge_effic)
#         target_day += delta
#
#     history_data = dict()
#     history_data["time"] = days
#     history_data['charge_detail'] = days_charge_detail
#     history_data['charge_title'] = "充电量（kWh)"
#     history_data['discharge_detail'] = days_discharge_detail
#     # history_data['charge_soc'] = day_charge_soc_diff_sum_list
#     # history_data['discharge_soc'] = day_discharge_soc_diff_sum_list
#     history_data['discharge_title'] = "放电量（kWh)"
#     history_data['charge_discharge_effic'] = days_dis_charge_effic_ditail
#     history_data['effic_title'] = "充放电效率（%）"
#
#     chart_data = {
#         "time": history_data['time'],
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "charge_detail": days_charge_detail,
#         "discharge_detail": days_discharge_detail
#     }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
# def tmp_get_station_charge_discharge_by_day(master_station_item, start_day, end_day):
#     days = list()
#     target_day = start_day
#     temp_title = master_station_item[1]  # 宁波朗盛001
#     # station_instance = models.StationDetails.objects.filter(id=station_item[0]).first()
#     master_station = models.MaterStation.objects.filter(id=master_station_item[0], is_delete=0).first()
#
#     days_charge_detail = list()
#     days_discharge_detail = list()
#     days_dis_charge_effic_ditail = list()
#     day_charge_soc_diff_sum_list = list()
#     day_discharge_soc_diff_sum_list = list()
#
#     station_dict = {
#         "station_id": master_station_item[0],
#         "station_name": temp_title,
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "effic_title": "充放电效率（%）",
#         "charge_list": [],
#         "discharge_list": [],
#         "charge_discharge_effic": [],
#         # "day_charge_soc_diff_sum_list": [],
#         # "day_discharge_soc_diff_sum_list": []
#     }
#
#     while target_day.date() <= end_day.date():
#         count_ins = ChargeDischargeCount(target_day)
#         detail = count_ins.new_get_stations_by_day(master_station)
#
#         day_charge = 0  # 每天充电量
#         day_discharge = 0  # 每天放电量
#         day_charge_list = list()
#         day_discharge_list = list()
#         day_soc_diff_list = list()
#         if detail:
#             day_charge = detail['charge']
#             day_discharge = detail['discharge']
#             chag_soc = detail['chag_soc']
#             disg_soc = detail['disg_soc']
#
#             # 计算充放电效率
#             day_charge_soc_diff_sum = chag_soc
#             day_discharge_soc_diff_sum = disg_soc
#             if day_charge_soc_diff_sum and day_discharge_soc_diff_sum and day_charge and day_discharge and day_charge_soc_diff_sum != '--' and day_discharge_soc_diff_sum != '--' and day_charge != '--' and day_discharge != '--':
#                 dis_charge_effic = abs(round((day_discharge / day_discharge_soc_diff_sum) / (
#                         day_charge / day_charge_soc_diff_sum) * 100, 2))
#                 dis_charge_effic = dis_charge_effic if dis_charge_effic <= 100.00 else 100.00
#             elif (day_charge != '--' and day_discharge == '--') or (day_charge and not day_discharge):
#                 dis_charge_effic = 0
#             elif (day_discharge != '--' and day_charge == '--') or (day_discharge and not day_charge):
#                 dis_charge_effic = 100.00
#             else:
#                 dis_charge_effic = 0
#
#             if target_day.date() not in days:
#                 days.append(target_day.date())
#
#             # day_charge_soc_diff_sum_list.append(day_charge_soc_diff_sum)
#             # day_discharge_soc_diff_sum_list.append(day_discharge_soc_diff_sum)
#
#             days_charge_detail.append(round(day_charge, 1))
#             days_discharge_detail.append(round(day_discharge, 1))
#             days_dis_charge_effic_ditail.append(dis_charge_effic)
#         target_day += delta
#
#     station_dict['charge_list'] = days_charge_detail
#     station_dict['discharge_list'] = days_discharge_detail
#     station_dict['charge_discharge_effic'] = days_dis_charge_effic_ditail
#     # station_dict['day_charge_soc_diff_sum_list'] = day_charge_soc_diff_sum_list
#     # station_dict['day_discharge_soc_diff_sum_list'] = day_discharge_soc_diff_sum_list
#     return station_dict, days
#
#
# def get_station_charge_discharge_by_day(project_name, start_day, end_day,  type_='all', tem_station_id=0):
#     """
#     查询并网点逐日充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     days = list()
#     temp_arr = list()
#     # type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#     # tem_station_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001”
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         # for station_item in total_stations.items():
#         for master_station_item in total_master_stations.items():
#             future = executor.submit(tmp_get_station_charge_discharge_by_day, master_station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             station_dict, days = f.result()
#             temp_arr.append(station_dict)
#
#     temp_arr = sorted(temp_arr, key=lambda x: x['station_id'])
#     history_data["time"] = days
#     history_data['array'] = temp_arr
#
#     if int(tem_station_id) == 0:  # 并网点选择全部时
#         new_charge_detail = list()
#         new_discharge_detail = list()
#         for t in days:
#             index = days.index(t)
#             charge_t_v = 0  # 某天全部并网点的充电量
#             discharge_t_v = 0  # 某天全部并网点的放电量
#             for ind, v in enumerate(total_master_stations):
#                 u_day_charge = history_data['array'][ind]['charge_list'][index] if len(
#                     history_data['array'][ind]['charge_list']) else 0
#                 u_day_discharge = history_data['array'][ind]['discharge_list'][index] if len(
#                     history_data['array'][ind]['discharge_list']) else 0
#                 charge_t_v += u_day_charge
#                 discharge_t_v += u_day_discharge
#             new_charge_detail.append(charge_t_v)
#             new_discharge_detail.append(discharge_t_v)
#
#         chart_data = {
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "time": history_data['time'],
#             "charge_detail": new_charge_detail,
#             "discharge_detail": new_discharge_detail
#         }
#
#     else:  # 选择某个并网点时
#         target_index = sorted(list(total_master_stations.keys())).index(int(tem_station_id))
#         chart_data = {
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "time": history_data['time'],
#             "charge_detail": history_data['array'][target_index]['charge_list'],
#             "discharge_detail": history_data['array'][target_index]['discharge_list']
#         }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in total_master_stations.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         })
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#     return success_dic
#
#
# def tem_get_unit_charge_discharge_by_day(unit_item, start_day, end_day):
#     days = list()
#     target_day = start_day
#
#     temp_title = unit_item[1]  # "宁波朗声-储能单元001"
#     unit_instance = models.Unit.objects.filter(id=unit_item[0]).first()
#
#     days_charge_detail = list()
#     days_discharge_detail = list()
#     days_dis_charge_effic_ditail = list()
#     # day_charge_soc_diff_sum_list = list()
#     # day_discharge_soc_diff_sum_list = list()
#
#     unit_dict = {
#         "unit_id": unit_item[0],
#         "unit_name": temp_title,
#         "charge_title": "充电量（kWh)",
#         "discharge_title": "放电量（kWh)",
#         "effic_title": "充放电效率（%）",
#         "charge_list": [],
#         "discharge_list": [],
#         "charge_discharge_effic": []
#     }
#
#     while target_day.date() <= end_day.date():
#         count_ins = ChargeDischargeCount(target_day)
#         detail = count_ins.new_get_unit_by_day(unit_instance)
#
#         day_charge = 0  # 每天充电量
#         day_discharge = 0  # 每天放电量
#         # day_charge_list = list()
#         # day_discharge_list = list()
#         # day_soc_diff_list = list()
#
#         if detail:
#             day_charge = detail['charge']
#             day_discharge = detail['discharge']
#             chag_soc = detail['chag_soc']
#             disg_soc = detail['disg_soc']
#
#             # 计算充放电效率
#             day_charge_soc_diff_sum = chag_soc
#             day_discharge_soc_diff_sum = disg_soc
#
#             if day_charge_soc_diff_sum and day_discharge_soc_diff_sum and day_charge and day_discharge and day_charge_soc_diff_sum != '--' and day_discharge_soc_diff_sum != '--' and day_charge != '--' and day_discharge != '--':
#                 dis_charge_effic = abs(round((day_discharge / day_discharge_soc_diff_sum) / (
#                         day_charge / day_charge_soc_diff_sum) * 100, 2))
#                 dis_charge_effic = dis_charge_effic if dis_charge_effic <= 100.00 else 100.00
#             elif (day_charge != '--' and day_discharge == '--') or (day_charge and not day_discharge):
#                 dis_charge_effic = 0
#             elif (day_discharge != '--' and day_charge == '--') or (day_discharge and not day_charge):
#                 dis_charge_effic = 100.00
#             else:
#                 dis_charge_effic = 0
#
#             # day_charge_soc_diff_sum_list.append(day_charge_soc_diff_sum)
#             # day_discharge_soc_diff_sum_list.append(day_discharge_soc_diff_sum)
#
#             if target_day.date() not in days:
#                 days.append(target_day.date())
#             days_charge_detail.append(day_charge)
#             days_discharge_detail.append(day_discharge)
#             days_dis_charge_effic_ditail.append(dis_charge_effic)
#
#         target_day += delta
#
#     unit_dict['charge_list'] = days_charge_detail
#     unit_dict['discharge_list'] = days_discharge_detail
#     unit_dict['charge_discharge_effic'] = days_dis_charge_effic_ditail
#     # unit_dict['day_charge_soc_diff_sum_list'] = day_charge_soc_diff_sum_list
#     # unit_dict['day_discharge_soc_diff_sum_list'] = day_discharge_soc_diff_sum_list
#
#     return unit_dict, days
#
#
# def get_unit_charge_discharge_by_day(project_name, start_day, end_day,  type_='all', tem_unit_id=0):
#     """
#     查询储能单元逐日充放电量
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     days = list()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for unit_item in total_units.items():
#             future = executor.submit(tem_get_unit_charge_discharge_by_day, unit_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             unit_dict, days = f.result()
#             temp_arr.append(unit_dict)
#
#     temp_arr = sorted(temp_arr, key=lambda k: k['unit_id'])
#     history_data["time"] = days
#     history_data['array'] = temp_arr
#
#     if int(tem_unit_id) == 0:  # 储能单元选择全部时
#         new_charge_detail = list()
#         new_discharge_detail = list()
#         for t in days:
#             index = days.index(t)
#             charge_t_v = 0  # 某天全部储能单元的充电量
#             discharge_t_v = 0  # 某天全部储能单元的放电量
#             for ind, v in enumerate(total_units):
#                 u_day_charge = history_data['array'][ind]['charge_list'][index] if len(
#                     history_data['array'][ind]['charge_list']) else 0
#                 u_day_discharge = history_data['array'][ind]['discharge_list'][index] if len(
#                     history_data['array'][ind]['discharge_list']) else 0
#                 charge_t_v += u_day_charge
#                 discharge_t_v += u_day_discharge
#
#             new_charge_detail.append(round(charge_t_v, 1))
#             new_discharge_detail.append(round(discharge_t_v, 1))
#
#         chart_data = {
#             "time": history_data['time'],
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "charge_detail": new_charge_detail,
#             "discharge_detail": new_discharge_detail
#         }
#
#     else:  # 选择某个储能单元时
#         target_index = sorted(list(total_units.keys())).index(int(tem_unit_id))
#         chart_data = {
#             "time": history_data['time'],
#             "charge_title": "充电量（kWh)",
#             "discharge_title": "放电量（kWh)",
#             "charge_detail": history_data['array'][target_index]['charge_list'],
#             "discharge_detail": history_data['array'][target_index]['discharge_list']
#         }
#
#     success_dic['data']['detail']['chart_data'] = chart_data
#     success_dic['data']['detail']['table_data'] = history_data
#
#     success_dic['data']['keys1'] = [{"key": i[0], "value": i[1]} for i in TypeOptions.items()]
#     success_dic['data']['keys2'] = [{"key": i[0], "value": i[1]} for i in
#                                     total_units.items()]
#     success_dic['data']['keys2'].append(
#         {
#             "key": 0,
#             "value": "全部"
#         }
#     )
#     success_dic['data']['keys2'] = sorted(success_dic['data']['keys2'], key=lambda x: x['key'])
#     return success_dic
#
#
# def tem_get_station_load_data(master_station_item, start_day, end_day):
#     temp_title = master_station_item[1]  # "宁波朗声-储能单元001"
#     master_station = models.MaterStation.objects.get(id=master_station_item[0])
#     # station = master_station.stationdetails_set.filter(slave__lte=0).first()
#     # 兼容标准站 & 标准主从站 & EMS级联主从站模式
#     station = master_station.stationdetails_set.filter(english_name=master_station.english_name).first()
#
#     station_english_name = station.english_name
#     # station_app = station.app
#
#     start_time_ = str(start_day)
#     end_time_ = str(end_day).replace('00:00:00', '23:59:59')
#     start_datatime = datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S")
#     end_datatime = datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
#     # result = time_range_by_measure_pcc(station_app, station_english_name, start_datatime, end_datatime)
#     result = time_range_by_dwd(station_english_name, 'measure', 'ems', 'EMS', start_datatime, end_datatime, 'PCC')
#
#     unit_dict = {
#         "unit_id": master_station_item[0],
#         "unit_name": temp_title,
#         "title": "负荷有功功率（kW）",
#         "p_value": [i['PCC'] for i in result] if result else []
#     }
#     return unit_dict, result
#
#
# def get_station_load_data(project_name, start_day, end_day):
#     """
#     查询并网点负荷数据
#     """""
#     # 查询 project 下所有的 stations 和 units
#     total_master_stations, total_stations, total_units = select_project_subitems_by_name(project_name)
#     history_data = dict()
#     temp_arr = list()
#
#     with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#         futures = list()
#         for master_station_item in total_master_stations.items():
#             future = executor.submit(tem_get_station_load_data, master_station_item, start_day, end_day)
#             futures.append(future)
#         # results = [f.result() for f in concurrent.futures.as_completed(futures)]
#         for f in concurrent.futures.as_completed(futures):
#             station_dict, result = f.result()
#             temp_arr.append(station_dict)
#             history_data["time"] = [i['time'] for i in result] if result else []
#
#     temp_arr = sorted(temp_arr, key=lambda k: k['unit_id'])
#     history_data["array"] = temp_arr
#
#     success_dic['data']['detail']['chart_data'] = history_data
#     success_dic['data']['detail']['table_data'] = history_data
#     return success_dic
#
#
def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    conn = get_redis_connection("3")
    topic = msg.topic.split('/')
    months = eval(msg.payload).get('body')[0].get('body')
    if months:
        month = list(months.keys())[0].split('H')
        month = list(months.keys())[0][1:2] if len(month[0]) == 2 else list(months.keys())[0][1:3]
        conn.set('{}-{}-mqtt'.format(topic[-2], month), msg.payload)
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")

def on_disconnect(mqtt_client, userdata, rc):
    print(f"关闭mqtt连接")

def mtqq_station_strategy(station_id, month):
    """
    获取指定月份的自动控制策略新信息
    :param station_id:
    :param month:
    :return:
    """
    station_instance = models.StationDetails.objects.filter(is_delete=0, id=station_id).first()
    station_app = station_instance.app
    station_english_name = station_instance.english_name

    temp_list = list()
    for i in range(0, 48):
        temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'F')
        temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'P')

    import time
    now_time = str(int(time.time()))

    message = {
        "time": now_time,
        "body": [
            {
                "device": "EMS",
                "datatype": "measure",
                "totalcall": "0",
                "body": temp_list
            }
        ]
    }

    req_topic = f"req/database/realtime/{station_english_name}/{station_app}"
    res_topic = f"res/database/realtime/{station_english_name}/{station_app}"

    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
    client.connect(
        host=settings.MQTT_SERVER,
        port=settings.MQTT_PORT,
        keepalive=settings.MQTT_KEEPALIVE,
    )

    json_message = json.dumps(message)
    # 发布
    client.publish(req_topic, json_message)
    # 获取订阅结果
    client.subscribe(res_topic)
    client.loop_start()
    return client