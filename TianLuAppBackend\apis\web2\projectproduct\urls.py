from django.urls import path
from apis.web2.projectproduct import views

urlpatterns = [
    path('station/operation/soc_power/', views.StationOperationSocView.as_view()),  # 储能SOC&功率      en
    path('station/device/', views.StationODeviceView.as_view()),  # 并网点设备清单     en
    path('station/operation/grid_side_power/', views.StationOperationPowerView.as_view()),  # 电网侧功率     en
    path('station/operation/voltammeter/', views.StationOperationVoltammeterView.as_view()),  # 电压&电流       en
    path('station/operation/demand/', views.StationOperationDemandView.as_view()),  # 需量分析      en
    path('station/operation/efficiency/', views.StationOperationEfficiencyView.as_view()),  # 充放电效率     en
    path('station/operation/temperature/', views.StationOperationTemperatureView.as_view()),  # PCS温度       en
    path('station/operation/water_cooler/', views.StationOperationWaterCoolerView.as_view()),  # 水冷机压力      en
    path('station/operation/cell_voltage/', views.StationOperationCellVoltgeView.as_view()),  # 电芯电压        en
    path('station/operation/cell_temperature/', views.StationOperationCellTemperatureView.as_view()),  # 电芯温度       en
    path('station/statistics/kwh/', views.StationStatisticsKwhView.as_view()),  # 生产统计-电量       en
    path('station/statistics/income/', views.StationStatisticsIncomeView.as_view()),  # 生产统计-收益     en
    path('station/statistics/kilowatt_income/', views.StationStatisticsKilowattIncomeView.as_view()),  # 生产统计-度电收益
    #      en
    path('station/statistics/project_start_year/', views.StationStartYearView.as_view()),  # 生产统计-项目投入使用初始年份        en

    path('station/statistics/income/task/', views.StationStatisticsIncomeTask.as_view()),  # 峰谷标识充放电量、收益手动任务
    path('station/income/task/', views.StationIncomeTask.as_view()),  # 实际收益手动任务
    #        no need
]