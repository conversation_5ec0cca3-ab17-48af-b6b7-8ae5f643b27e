# #!/usr/bin/env python
# # coding=utf-8

import math
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.event import Event
from Application.Models.User.report_bms_baodian_f import FReportBmsBaodian
from Application.Models.User.report_bms_binhai_f import FReportBmsbinhai
from Application.Models.User.report_bms_datong_f import FReportBmsDatong
from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
from Application.Models.User.report_bms_guizhou_f import FReportBmsGuizhou
from Application.Models.User.report_bms_halun_f import FReportBmsHalun
from Application.Models.User.report_bms_taicang_f import FReportBmsTaicang
from Application.Models.User.report_bms_ygzhen_f import FReportBmsYgzhen
from Application.Models.User.report_bms_zgtian_f import FReportBmsZgtian
from Application.Models.User.report_bms_houma_f import FReportBmsHouma
from Application.Models.User.report_f import FReport
from Application.Models.User.report_pcs_f import FReportPcs
from Application.Models.User.sda_pcs_cu_name import SdaPcsCu
from Application.Models.User.station import Station
from sqlalchemy import func, or_, and_
from Tools.DB.datong_his import datong1_engine, datong1_session, DATONG1_DATABASE, datong2_engine, datong2_session, \
    DATONG2_DATABASE, datong3_engine, datong3_session, DATONG3_DATABASE, datong4_engine, datong4_session, \
    DATONG4_DATABASE
from Tools.DB.guizhou_bms_his import bguizhou1_session, bguizhou2_session, bguizhou3_session, bguizhou4_session, \
    bguizhou5_session, bguizhou6_session, bguizhou7_session, bguizhou8_session
from Tools.DB.guizhou_his import guizhou1_session, guizhou2_session, guizhou3_session, guizhou4_session, \
    guizhou5_session, guizhou6_session, guizhou7_session, guizhou8_session, guizhou1_engine, GUIZHOU1_DATABASE, \
    guizhou2_engine, GUIZHOU2_DATABASE, guizhou5_engine, GUIZHOU5_DATABASE, guizhou6_engine, GUIZHOU6_DATABASE, \
    guizhou3_engine, guizhou7_engine, GUIZHOU3_DATABASE, GUIZHOU7_DATABASE, guizhou4_engine, GUIZHOU4_DATABASE, \
    guizhou8_engine, GUIZHOU8_DATABASE
from Tools.DB.mysql_scada import DEBUG
from Tools.DB.mysql_user import user_session
from Application.HistoryData.his_bams import *
import configparser
import os
from Application.HistoryData.his_bams import _return_db_con, _select_get_des_value, _select_get_his_value
from Tools.DB.halun_his import halun_session, halun_engine, HALUN_DATABASE
from Tools.DB.taicang_his import taicang_session, taicang_engine, TAICANG_DATABASE
from Tools.DB.binhai1_his import binhai1_session, binhai1_engine, BINHAI1_DATABASE
from Tools.DB.binhai2_his import binhai2_session, binhai2_engine, BINHAI2_DATABASE
from Tools.DB.ygzhen_his import ygzhen1_engine, ygzhen2_engine, ygzhen1_session, ygzhen2_session, YGZHEN1_DATABASE, \
    YGZHEN2_DATABASE
from Tools.DB.baodian_his import BAODIAN1_DATABASE, BAODIAN2_DATABASE, BAODIAN3_DATABASE, BAODIAN4_DATABASE, \
    BAODIAN5_DATABASE, baodian1_engine, \
    baodian2_engine, baodian3_engine, baodian4_engine, baodian5_engine
from Tools.DB.zgtian_his import zgtian1_session, zgtian2_session, zgtian3_session, zgtian4_session, ZGTIAN1_DATABASE, \
    ZGTIAN2_DATABASE, ZGTIAN3_DATABASE, ZGTIAN4_DATABASE, zgtian1_engine, zgtian2_engine, zgtian3_engine, zgtian4_engine
from Tools.DB.baodian_his import baodian1_session, baodian2_session, baodian3_session, baodian4_session, \
    baodian5_session
from Tools.DB.binhai_bms_his import binhai_session1, binhai_session2
from Tools.DB.taicang_bms_his import taicang_session1
from Tools.DB.ygzhen_bms_his import ygzhen_session1, ygzhen_session2
import pandas as pd
from Tools.DB.houma_his import houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE, houmaa2_engine, houmaa2_session, \
    HOUMAA2_DATABASE, houmab2_engine, houmab1_session, HOUMAB1_DATABASE, houmab1_engine, houmab2_session
from Tools.TimeTask.froze_report import _tableIsExist

# 定义各项目查询数据的配置(PCS)
db_his = {"taicang": [taicang_session], "binhai": [binhai1_session, binhai2_session], "halun": [halun_session],
          "ygzhen": [ygzhen1_session, ygzhen2_session],
          "zgtian": [zgtian1_session, zgtian2_session, zgtian3_session, zgtian4_session],
          "houma": [houmaa1_session, houmaa2_session, houmab1_session, houmab2_session],
          "datong": [datong1_session, datong2_session, datong3_session, datong4_session],
          "guizhou": [guizhou1_session, guizhou2_session, guizhou3_session, guizhou4_session, guizhou5_session,
                      guizhou6_session, guizhou7_session, guizhou8_session]}

baodian_his = {"tfStbodian1": baodian1_session, "tfStbodian2": baodian2_session, "tfStbodian3": baodian3_session,
               "tfStbodian4": baodian4_session, "tfStbodian5": baodian5_session}

# 定义各项目查询数据的配置（BMS）
bmsdb = {"taicang": [taicang_session1], "binhai": [binhai_session1, binhai_session2], "halun": [],
         "ygzhen": [ygzhen_session1, ygzhen_session2],
         "zgtian": [zgtian1_bms_session, zgtian2_bms_session, zgtian3_bms_session, zgtian4_bms_session],
         "dongmu": [dongmu_session],
         "houma": [houma_a1_g_bms_session, houma_a2_g_bms_session, houma_b1_g_bms_session, houma_b2_g_bms_session],
         "datong": [datong1_g_bms_session, datong2_g_bms_session, datong3_g_bms_session, datong4_g_bms_session],
         "guizhou": [bguizhou1_session, bguizhou2_session, bguizhou3_session, bguizhou4_session, bguizhou5_session,
                     bguizhou6_session, bguizhou7_session, bguizhou8_session]}

baodian_siss = {"tfStbodian1": baodian1_session, "tfStbodian2": baodian2_session, "tfStbodian3": baodian3_session,
                "tfStbodian4": baodian4_session, "tfStbodian5": baodian5_session}

# 所有查询数据的配置结合(PCS,判断表存不存在)
all_datas = {'halun': [[halun_engine, halun_session, HALUN_DATABASE]],
             'taicang': [[taicang_engine, taicang_session, TAICANG_DATABASE]],
             'binhai': [[binhai1_engine, binhai1_session, BINHAI1_DATABASE],
                        [binhai2_engine, binhai2_session, BINHAI2_DATABASE]],
             'ygzhen': [[ygzhen1_engine, ygzhen1_session, YGZHEN1_DATABASE],
                        [ygzhen2_engine, ygzhen2_session, YGZHEN2_DATABASE]],
             'baodian': [[baodian1_engine, baodian1_session, BAODIAN1_DATABASE],
                         [baodian2_engine, baodian2_session, BAODIAN2_DATABASE],
                         [baodian3_engine, baodian3_session, BAODIAN3_DATABASE],
                         [baodian4_engine, baodian4_session, BAODIAN4_DATABASE],
                         [baodian5_engine, baodian5_session, BAODIAN5_DATABASE]],
             'zgtian': [[zgtian1_engine, zgtian1_session, ZGTIAN1_DATABASE],
                        [zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE],
                        [zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE],
                        [zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE]],
             'houma': [[houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE],
                       [houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE],
                       [houmab1_engine, houmab1_session, HOUMAB1_DATABASE],
                       [houmab2_engine, houmab2_session, HOUMAB1_DATABASE]],
             'datong': [[datong1_engine, datong1_session, DATONG1_DATABASE],
                        [datong2_engine, datong2_session, DATONG2_DATABASE],
                        [datong3_engine, datong3_session, DATONG3_DATABASE],
                        [datong4_engine, datong4_session, DATONG4_DATABASE]],
             'guizhou': [[guizhou1_engine, guizhou1_session, GUIZHOU1_DATABASE],
                         [guizhou2_engine, guizhou2_session, GUIZHOU2_DATABASE],
                         [guizhou3_engine, guizhou3_session, GUIZHOU3_DATABASE],
                         [guizhou4_engine, guizhou4_session, GUIZHOU4_DATABASE],
                         [guizhou5_engine, guizhou5_session, GUIZHOU5_DATABASE],
                         [guizhou6_engine, guizhou6_session, GUIZHOU6_DATABASE],
                         [guizhou7_engine, guizhou7_session, GUIZHOU7_DATABASE],
                         [guizhou8_engine, guizhou8_session, GUIZHOU8_DATABASE]]}

dongmu_num = 7
def _return_db_con_pcs(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_his[d]
    else:
        if db == 'taicang' or db == 'halun':
            return db_his[db][0]
        elif db == 'houma':
            if d == 'A1':
                return db_his[db][0]
            elif d == 'A2':
                return db_his[db][1]
            elif d == 'B1':
                return db_his[db][2]
            elif d == 'B2':
                return db_his[db][3]
            return db_his[db][0]
        else:
            return db_his[db][int(d) - 1]
def _return_db_con_cu(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_siss[d]
    elif db == 'houma':
        if d < 3:
            return bmsdb[db][0]
        elif d < 5:
            return bmsdb[db][1]
        elif d < 7:
            return bmsdb[db][2]
        else:
            return bmsdb[db][3]
    else:
        d = int(d[1:2]) if db != 'dongmu' else 0
        if db == 'binhai':  # 滨海电站
            if d < 4:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'taicang' or db == 'dongmu':
            return bmsdb[db][0]
        elif db == 'ygzhen':
            if d < 3:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'zgtian' or db == 'datong':
            if d < 3:
                return bmsdb[db][0]
            elif d < 5:
                return bmsdb[db][1]
            elif d < 7:
                return bmsdb[db][2]
            else:
                return bmsdb[db][3]
def _return_db_con_(db, psc_font_name):
    '''返回数据库链接（PCS）'''
    if db == 'baodian':
        return baodian_his[psc_font_name.split('.')[0]]
    else:
        if db == 'taicang' or db == 'halun':
            return db_his[db][0]
        elif db == 'guizhou':
            d = int(psc_font_name.split('.')[0][-2])
            return db_his[db][d - 1]
        elif db == 'houma':
            d = psc_font_name.split('.')[0][-2:]
            if d == 'A1':
                return db_his[db][0]
            elif d == 'A2':
                return db_his[db][1]
            elif d == 'B1':
                return db_his[db][2]
            elif d == 'B2':
                return db_his[db][3]
        else:
            d = int(psc_font_name.split('.')[0][-1])
            return db_his[db][d - 1]
def get_conf_name(db):
    if db == 'halun' or db == 'taicang' or db == 'binhai':  # PCS_SOC，PCS有功功率，功率因数，PCS交流电压，PCS交流电流，PCS直流电压，PCS直流电流,PCS温度，PCS日放电，PCS日充电,PCS SOH
        return 'soc_1', 'PCS_yougong_1', 'PCS_PF_1', 'PCS_jldy_1', 'PCS_jldl_1', 'PCS_zldy_1', 'PCS_zldl_1', 'PCS_T_1', 'PCS_Disg_1', 'PCS_Chag_1', 'PCS_SOH_1'
    elif db == 'ygzhen' or db == 'zgtian':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_2', 'PCS_yougong_2', 'PCS_PF_2', 'PCS_jldy_2', 'PCS_jldl_2', 'PCS_zldy_2', 'PCS_zldl_2', 'PCS_T_2', 'PCS_Disg_2', 'PCS_Chag_2'
    elif db == 'baodian':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度（保电）内部温度,PCS日放电，PCS日充电
        return 'soc_3', 'PCS_yougong_3', 'PCS_PF_3', 'PCS_jldy_3', 'PCS_jldl_3', 'PCS_zldy_3', 'PCS_zldl_3', 'PCS_T_3', 'PCS_Disg_3', 'PCS_Chag_3'
    elif db == 'dongmu':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_4', 'PCS_yougong_4', 'PCS_PF_4', 'PCS_jldy_4', 'PCS_jldl_4', 'PCS_zldy_4', 'PCS_zldl_4', 'PCS_T_4', 'PCS_Disg_4', 'PCS_Chag_4'
    elif db == 'houma':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_5', 'PCS_yougong_5', 'PCS_PF_5', 'PCS_jldy_5', 'PCS_jldl_5', 'PCS_zldy_5', 'PCS_zldl_5', 'PCS_T_5', 'PCS_Disg_5', 'PCS_Chag_5'
    elif db == 'datong':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_6', 'PCS_yougong_6', 'PCS_PF_6', 'PCS_jldy_6', 'PCS_jldl_6', 'PCS_zldy_6', 'PCS_zldl_6', 'PCS_T_6', 'PCS_Disg_6', 'PCS_Chag_6'
    elif db == 'guizhou':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_7', 'PCS_yougong_7', 'PCS_PF_7', 'PCS_jldy_7', 'PCS_jldl_7', 'PCS_zldy_4', 'PCS_zldl_7', 'PCS_T_7', 'PCS_Disg_7', 'PCS_Chag_7'

# 查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
dongmu_num = 7

class IndexMonitorIntetface(BaseHandler):
    ''' 关键指标监测 '''
    @tornado.web.authenticated
    def get(self, kt):
        global max, min
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            data = []
            if kt == 'GetAlarmR':  # 三级故障和报警的数据
                report_type = self.get_argument('report_type', '1')  # 类型
                db = self.get_argument('db', None)  # 电站名称
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                if DEBUG:
                    logging.info('startTime:%s,endTime:%s,db:%s,pageNum:%s,pageSize:%s' % (
                    start_Time, end_Time, db, pageNum, pageSize))
                startTime = start_Time + ' 00:00:00'
                endTime = end_Time + ' 23:59:59'
                if report_type == '5':  # 累计
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                obj = {}
                obj['data'] = []
                total_all = user_session.query(func.count(AlarmR.id)).filter(AlarmR.event_id == Event.id,
                                                                             Event.type_id == 5,
                                                                             Event.type == 2, Event.station == db,
                                                                             AlarmR.ts.between(startTime, endTime),
                                                                             or_(AlarmR.value_descr == '故障',
                                                                                 AlarmR.value_descr == '报警')).scalar()
                alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5,
                                                           Event.type == 2, Event.station == db,
                                                           AlarmR.ts.between(startTime, endTime),
                                                           or_(AlarmR.value_descr == '故障',
                                                               AlarmR.value_descr == '报警')).order_by(
                    AlarmR.ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                for alarm in alarms:
                    alarm = eval(str(alarm))
                    if lang == 'en':
                        if alarm['point'] != 'None' and alarm['point'] != '':
                            point = translate_text(alarm['point'], 2)
                        else:
                            point = alarm['en_descr']
                        if int(alarm['value']) == 2:
                            point = point + " Give an alarm"
                        else:
                            point = point + " Recovered"
                    else:
                        if alarm['point'] != 'None' and alarm['point'] != '':
                            point = alarm['point']
                        else:
                            point = alarm['descr']
                        if int(alarm['value']) == 2:
                            point = point + " 报警"
                        else:
                            point = point + " 已恢复"
                    obj['data'].append({'ts': alarm['ts'], 'alarm_id': alarm['id'], 'point': point})
                return self.returnTotalSuc(obj, total_all)
            elif kt == 'GetSysYoCoFa':  # 系统有功功率，充放电量
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', '1')  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info('report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []
                names_ = model_config.get('peizhi', db)  # 充放电量name
                names = json.loads(names_)
                conf_name = get_conf_name(db)
                list2 = []
                name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                if endTime[0:8] == now_time[0:8]:
                    list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                if report_type == '1':  # 日报（有功功率）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu_yougong(ed, obj, st, 'PCS_yougong_4', '15T', 1, obj_sys='系统',
                                                         vmax=100000, lang=lang)
                        else:
                            self.data_day(db, 1, tables, ed, now_time, obj, st, startTime, '15T', 1, obj_sys='系统',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.returnTypeSuc(data)
                            self.data_day_dongmu_yougong(ed, obj, st, 'PCS_yougong_4', '15T', 1, obj_sys='系统',
                                                         vmax=100000, lang=lang)
                        else:
                            self.data_day(db, 1, tables, ed, endTime, obj, st, startTime, '15T', 1, obj_sys='系统',
                                          vmax=100000, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月充放电量
                    list22 = []
                    if endTime[0:10] == now_time[0:10]:
                        list22 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                    two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                    if endTime[0:8] == now_time[0:8]:
                        two_time_lists.append(endTime[0:10])
                    obj['time'] = sorted(set(two_time_lists))
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names), FReport.cause == 1).all()
                    self.sys_chag_disg(freports, obj, 10, obj['time'])
                    if endTime[0:8] == now_time[0:8]:
                        if list22 != []:
                            disg_ = 0
                            chag_ = 0
                            ratio = 0
                            for l in list22:
                                disg_ += float(l['disg'])
                                chag_ += float(l['chag'])
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                            obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                            if obj['data'][-1]['chag'] == 0:
                                obj['data'][-1]['ratio'] = 0
                            else:
                                obj['data'][-1]['ratio'] = ('%.3f' % (
                                            (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                                if float(obj['data'][-1]['ratio']) > 100:
                                    obj['data'][-1]['ratio'] = 100
                elif report_type == '4':  # 年充放电量
                    two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                    if endTime[0:7] == now_time[0:7]:
                        two_time_lists.append(endTime[0:7])
                    obj['time'] = sorted(set(two_time_lists))
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names), FReport.cause == 1).all()
                    self.sys_chag_disg(freports, obj, 7, obj['time'])
                    if endTime[0:7] == now_time[0:7]:
                        if list2 != []:
                            disg_ = 0
                            chag_ = 0
                            ratio = 0
                            for l in list2:
                                disg_ += float(l['disg'])
                                chag_ += float(l['chag'])
                            if float(chag_) == 0:
                                ratio = 0
                            else:
                                ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                                if float(ratio) > 100:
                                    ratio = 100
                            obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                            obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                            if obj['data'][-1]['chag'] == 0:
                                obj['data'][-1]['ratio'] = 0
                            else:
                                obj['data'][-1]['ratio'] = ('%.3f' % (
                                            (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                                if float(obj['data'][-1]['ratio']) > 100:
                                    obj['data'][-1]['ratio'] = 100
                elif report_type == '5':  # 累计充放电量
                    y_time_lists = self.sys_5_time_list(db, end_Time)
                    if endTime[0:8] == now_time[0:8]:
                        y_time_lists.append(int(endTime[0:4]))
                    obj['time'] = sorted(set(y_time_lists))
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.name.in_(names), FReport.cause == 1).all()
                    self.sys_chag_disg(freports, obj, 4, obj['time'])
                    if list2 != []:
                        disg_ = 0
                        chag_ = 0
                        ratio = 0
                        for l in list2:
                            disg_ += float(l['disg'])
                            chag_ += float(l['chag'])
                        if float(chag_) == 0:
                            ratio = 0
                        else:
                            ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                            if float(ratio) > 100:
                                ratio = 100
                        obj['data'][-1]['disg'] = math.floor(obj['data'][-1]['disg'] + disg_)
                        obj['data'][-1]['chag'] = math.floor(obj['data'][-1]['chag'] + chag_)
                        if obj['data'][-1]['chag'] == 0:
                            obj['data'][-1]['ratio'] = 0
                        else:
                            obj['data'][-1]['ratio'] = ('%.3f' % (
                                        (float(obj['data'][-1]['disg']) / float(obj['data'][-1]['chag'])) * 100))
                            if float(obj['data'][-1]['ratio']) > 100:
                                obj['data'][-1]['ratio'] = 100
                return self.returnTypeSuc(obj)
            elif kt == 'GetSysPf':  # 系统功率因数
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个pcs
                conf_name = get_conf_name(db)
                if report_type == '1':  # 日报
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.dongmu_sys_SOC_d('PCS_PF_4', ed, obj, st, 1, lang=lang)
                        else:
                            self.other_sys_SOC_d(db, conf_name[2], ed, now_time, obj, st, startTime, tables, 1,
                                                 lang=lang)
                    else:
                        if db == 'dongmu':
                            self.dongmu_sys_SOC_d('PCS_PF_4', ed, obj, st, 1, lang=lang)
                        else:
                            self.other_sys_SOC_d(db, conf_name[2], ed, endTime, obj, st, startTime, tables, 1,
                                                 lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月，
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                            FReportPcs.day).filter(FReportPcs.station == db,
                                                                   FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    pf_list = []
                    two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                    obj['time'] = two_time_lists
                    dict_pf = {}
                    for ti in two_time_lists:
                        if PF:
                            for f in PF:
                                ti_Time = ti + ' 00:00:00'
                                if ti_Time == str(f[3]):
                                    if f[3] not in dict_pf.keys():
                                        dict_pf[f[3]] = {'max': [], 'min': []}
                                    else:
                                        if float(f[1]) > 1:
                                            dict_pf[f[3]]['max'].append(1)
                                            dict_pf[f[3]]['min'].append(float(f[2]))
                                        else:
                                            dict_pf[f[3]]['max'].append(float(f[1]))
                                            dict_pf[f[3]]['min'].append(float(f[2]))
                    for i in obj['time']:
                        min_ = 0
                        max_ = 0
                        for d in dict_pf.keys():
                            if i == str(d)[:10]:
                                min_ = min(dict_pf[d]['min'])
                                max_ = max(dict_pf[d]['max'])
                                break
                            else:
                                min_ = 0
                                max_ = 1
                        obj['data'].append({'max': max_, 'min': min_})
                    if obj['data'] == []:
                        if endTime[0:10] == now_time[0:10]:
                            dict_ = {}
                            dict_['data'] = []
                            now_time_st = now_time + ' 00:00:00'
                            st = timeUtils.timeStrToTamp(now_time_st)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            if db == 'dongmu':
                                self.dongmu_sys_SOC_d('PCS_PF_4', ed, dict_, st, 1, lang=lang)
                            else:
                                self.other_sys_SOC_d(db, conf_name[2], ed, now_time, dict_, st, now_time_st, tables, 1,
                                                     lang=lang)
                            if dict_['data'] != []:
                                obj['data'] = [
                                    {'max': max(dict_['data'][0]['value']), 'min': min(dict_['data'][0]['value'])}]
                    else:
                        if endTime[0:10] == now_time[0:10]:
                            dict_ = {}
                            dict_['data'] = []
                            now_time_st = now_time + ' 00:00:00'
                            st = timeUtils.timeStrToTamp(now_time)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                            if db == 'dongmu':
                                self.dongmu_sys_SOC_d('PCS_PF_4', ed, dict_, st, 1, lang=lang)
                            else:
                                self.other_sys_SOC_d(db, conf_name[2], ed, now_time, dict_, st, now_time_st, tables, 1,
                                                     lang=lang)
                            if dict_['data'] != []:
                                obj['data'][-1] = {'max': max(dict_['data'][0]['value']),
                                                   'min': min(dict_['data'][0]['value'])}
                elif report_type == '4':  # 年
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                            FReportPcs.day).filter(FReportPcs.station == db,
                                                                   FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    pf_list = []
                    # two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                    obj['time'] = []
                    two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                    time_list = two_time_lists
                    dict_pf = {}
                    for ti in two_time_lists:
                        if PF:
                            for f in PF:
                                if ti == str(f[3])[:7]:
                                    if str(f[3])[:7] not in dict_pf.keys():
                                        dict_pf[str(f[3])[:7]] = {'max': [], 'min': []}
                                    else:
                                        if float(f[1]) > 1:
                                            dict_pf[str(f[3])[:7]]['max'].append(1)
                                            dict_pf[str(f[3])[:7]]['min'].append(float(f[2]))
                                        else:
                                            dict_pf[str(f[3])[:7]]['max'].append(float(f[1]))
                                            dict_pf[str(f[3])[:7]]['min'].append(float(f[2]))
                        else:
                            obj['data'] = []
                    if PF:
                        for i in time_list:
                            for d in dict_pf.keys():
                                if i == str(d):
                                    min_ = min(dict_pf[d]['min'])
                                    max_ = max(dict_pf[d]['max'])
                                    obj['time'].append(i)
                                    obj['data'].append({'max': max_, 'min': min_})
                                    break
                elif report_type == '5':  # 累计
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    st = timeUtils.timeStrToTamp(str(pages[0]))  # 起始时间绝对秒
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p,
                                            FReportPcs.day).filter(FReportPcs.station == db,
                                                                   FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    y_time_lists = self.sys_5_time_list(db, end_Time)
                    obj['time'] = []
                    dict_pf = {}
                    for ti in y_time_lists:
                        if PF:
                            for f in PF:
                                if ti == str(f[3])[:4]:
                                    if str(f[3])[:4] not in dict_pf.keys():
                                        dict_pf[str(f[3])[:4]] = {'max': [], 'min': []}
                                    else:
                                        if float(f[1]) > 1:
                                            dict_pf[str(f[3])[:4]]['max'].append(1)
                                            dict_pf[str(f[3])[:4]]['min'].append(float(f[2]))
                                        else:
                                            dict_pf[str(f[3])[:4]]['max'].append(float(f[1]))
                                            dict_pf[str(f[3])[:4]]['min'].append(float(f[2]))
                    for i in y_time_lists:
                        for d in dict_pf.keys():
                            if i == str(d):
                                min_ = min(dict_pf[d]['min'])
                                max_ = max(dict_pf[d]['max'])
                                obj['time'].append(i)
                                obj['data'].append({'max': max_, 'min': min_})
                                break
                return self.returnTypeSuc(obj)
            elif kt == 'GetSysSOCCD':  # 系统 SOC,充放电次数
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                time_S = timeUtils.getAgoTime()  # 获取N天前时间,默认一周'
                datestart = now_time[0:4]
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                names = model_config.get('peizhi', db)  # 充放电量name
                names = json.loads(names)
                conf_name = get_conf_name(db)
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                list2 = []
                name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                if endTime[0:8] == now_time[0:8]:
                    list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                obj = {}
                obj['data'] = []  #
                if report_type == '1':  # 日报 SOC
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.dongmu_sys_SOC_d('soc_4', ed, obj, st, 2, lang=lang)
                        else:
                            self.other_sys_SOC_d(db, conf_name[0], ed, now_time, obj, st, startTime, tables, 2,
                                                 lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_sys_SOC_d('soc_4', ed, obj, st, 2, lang=lang)
                        else:
                            self.other_sys_SOC_d(db, conf_name[0], ed, endTime, obj, st, startTime, tables, 2,
                                                 lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月充放电次数
                    list22 = []
                    if endTime[0:10] == now_time[0:10]:
                        list22 = _pcs_day_disg_chag(db, names, name_chag, name_disg)
                    two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                    if endTime[0:8] == now_time[0:8]:
                        two_time_lists.append(endTime[0:10])
                    obj['time'] = sorted(set(two_time_lists))
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names), FReport.cause == 1).all()
                    self.ys_chag_disg_num(db, freports, obj, obj['time'], 10, list22)
                elif report_type == '4':  # 年，充放电次数
                    two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                    # if endTime[0:4] == now_time[0:4]:
                    #     two_time_lists.append(endTime[0:7])
                    obj['time'] = sorted(set(two_time_lists))
                    obj['time'] = two_time_lists
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names), FReport.cause == 1).all()
                    self.ys_chag_disg_num(db, freports, obj, obj['time'], 7, list2)

                elif report_type == '5':  # 累计充放电次数
                    y_time_lists = self.sys_5_time_list(db, end_Time)
                    # if endTime[0:8] == now_time[0:8]:
                    #     y_time_lists.append(endTime[0:4])
                    obj['time'] = sorted(set(y_time_lists))
                    if db == 'ygzhen':
                        freports = user_session.query(FReport).filter(FReport.name == 'tfStygzhen1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    elif db == 'zgtian':
                        freports = user_session.query(FReport).filter(FReport.name == 'tfStzgtian1.EMS.MET.',
                                                                      FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.name.in_(names), FReport.cause == 1).all()
                    self.ys_chag_disg_num(db, freports, obj, obj['time'], 4, list2)
                return self.returnTypeSuc(obj)
            elif kt == 'GetSysSOH':  # 系统SOH
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)

                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒

                obj = {}
                obj['data'] = []  # 单个pcs2
                obj['time'] = []
                value1 = []
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                if report_type == '1':  # 日报 SOH
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.sys_soh_day_dongmu(ed, obj, st)
                        elif db == 'baodian':
                            self.sys_other_SOH_day(a, b, db, ed, now_time, list4[1], obj, st, startTime, tables, value1)
                        elif db == 'halun':
                            self.sys_halun_SOH(db, ed, now_time, obj, st, startTime, tables, value1)
                        else:
                            self.sys_other_SOH_day(a, b, db, ed, now_time, list3[2], obj, st, startTime, tables, value1)
                    else:
                        if db == 'dongmu':
                            self.sys_soh_day_dongmu(ed, obj, st)
                        elif db == 'baodian':
                            self.sys_other_SOH_day(a, b, db, ed, endTime, list4[1], obj, st, startTime, tables, value1)
                        elif db == 'halun':
                            self.sys_halun_SOH(db, ed, endTime, obj, st, startTime, tables, value1)
                        else:
                            self.sys_other_SOH_day(a, b, db, ed, endTime, list3[2], obj, st, startTime, tables, value1)
                elif report_type == '2' or report_type == '3':  # 周，月SOH
                    two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                    obj['time'] = []
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.min_soh, FReportBmsBaodian.day).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.min_soh, FReportBmsYgzhen.day).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.min_soh, FReportBmsTaicang.day).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.min_soh, FReportBmsbinhai.day).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'halun':
                        SOH = user_session.query(FReportBmsHalun.min_soh, FReportBmsHalun.day).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.min_soh, FReportBmsZgtian.day).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.min_soh, FReportBmsHouma.day).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.min_soh, FReportBmsDatong.day).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.sys_soh(SOH, min, obj, two_time_lists)
                    if endTime[0:10] == now_time[0:10]:
                        list_ = []
                        startTime = endTime[0:10] + ' 00:00:00'
                        endTime = endTime[0:10] + ' 23:59:59'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                        if db == 'dongmu':
                            SOH_name = model_config.get('peizhi', 'cu_SOH_4')  # name
                            two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                            for ti in two_time_lists:
                                d = []
                                stTime = ti + ' 00:00:00'
                                enTime = ti + ' 23:59:59'
                                list_ = self.dongmu_sys_soh(SOH_name, d, enTime, max, min, stTime)
                            dongmu_session.close()
                        elif db == 'baodian':
                            two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                            for ti in two_time_lists:
                                data = []
                                stTime = ti + ' 00:00:00'
                                enTime = ti + ' 23:59:59'
                                list_ = self.other_sys_soh(a, b, data, db, enTime, list4[1], max, min, stTime, tables)
                        else:
                            two_time_lists = timeUtils.dateToDataList(startTime, endTime)
                            for ti in two_time_lists:
                                data = []
                                stTime = ti + ' 00:00:00'
                                enTime = ti + ' 23:59:59'
                                list_ = self.other_sys_soh(a, b, data, db, enTime, list3[2], max, min, stTime, tables)
                        if obj['data']:
                            if list_ != []:
                                obj['data'][-1] = list_[0]
                elif report_type == '4':  # 年
                    two_time_lists = timeUtils.getBetweenMonthYm(startTime, endTime)
                    obj['time'] = []
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.min_soh, FReportBmsBaodian.day).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.min_soh, FReportBmsYgzhen.day).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.min_soh, FReportBmsTaicang.day).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.min_soh, FReportBmsbinhai.day).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'halun':
                        SOH = user_session.query(FReportBmsHalun.min_soh, FReportBmsHalun.day).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.min_soh, FReportBmsZgtian.day).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.min_soh, FReportBmsHouma.day).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.min_soh, FReportBmsDatong.day).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.sys_soh_year(SOH, min, obj, two_time_lists)
                elif report_type == '5':  # 累计
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    y_time_lists = self.sys_5_time_list(db, end_Time)
                    obj['time'] = []
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.min_soh, FReportBmsDongmu.day).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.min_soh, FReportBmsBaodian.day).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.min_soh, FReportBmsYgzhen.day).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.min_soh, FReportBmsTaicang.day).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.min_soh, FReportBmsbinhai.day).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'halun':
                        SOH = user_session.query(FReportBmsHalun.min_soh, FReportBmsHalun.day).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.min_soh, FReportBmsZgtian.day).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.min_soh, FReportBmsHouma.day).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.min_soh, FReportBmsDatong.day).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.sys_soh_5(SOH, min, obj, y_time_lists)

                return self.returnTypeSuc(obj)

            elif kt == 'GetPCSYoCoFa':  # PCS有功功率，充放电量
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                # time_S = timeUtils.getAgoTime()#获取N天前时间,默认一周'
                # datestart = now_time[0:8]
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报（有功功率）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_yougong_4', '15T', obj_sys='PCS', vmax=100000,
                                                 lang=lang)
                        else:
                            self.data_day(db, 1, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.data_day_dongmu(ed, obj, st, 'PCS_yougong_4', '15T', obj_sys='PCS', vmax=100000,
                                                 lang=lang)
                        else:
                            self.data_day(db, 1, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                elif report_type == '2' or report_type == '3' or report_type == '4' or report_type == '5':  # 周，月，年,累计充放电量
                    names_ = model_config.get('peizhi', db)  # 充放电量
                    names = json.loads(names_)
                    conf_name = get_conf_name(db)
                    name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                    name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                    list2 = []
                    if report_type == '2' or report_type == '3':
                        if endTime[0:10] == now_time[0:10]:
                            list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                    else:
                        if endTime[0:8] == now_time[0:8]:
                            list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                    list1 = []  # 单个pcs
                    obj = []
                    n = 1
                    if report_type == '5':
                        freports = user_session.query(FReport).filter(FReport.name.in_(names), FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names), FReport.cause == 1).all()
                    freport_obj = {}
                    for f in freports:
                        if f.name in freport_obj:
                            freport_obj[f.name].append(f)
                        else:
                            freport_obj[f.name] = []
                            freport_obj[f.name].append(f)

                    for name in freport_obj:
                        v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                                  SdaPcsCu.station_name == db).first()
                        if not v_pcs_name:
                            logging.error("未找到对应的pcs_name")
                            continue
                        PCS_name_f = v_pcs_name[0]
                        freport = freport_obj[name]
                        disg = 0  # 放电量
                        chag = 0  # 充电量
                        if freport:
                            for f in freport:
                                disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                                    eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                                chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                                    eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                        # list1.append({'name': ('PCS-%s' % n), 'disg': int(disg),'chag': int(chag)})
                        list1.append({'name': PCS_name_f, 'disg': int(disg), 'chag': int(chag)})

                        n += 1
                    list3 = []
                    if list2 != []:
                        for l in list2:
                            if list1 != []:
                                for ll in list1:
                                    if l['name'] == ll['name']:
                                        disg_ = float(l['disg']) + float(ll['disg'])
                                        chag_ = float(l['chag']) + float(ll['chag'])
                                        if float(chag_) == 0:
                                            ratio = 0
                                        else:
                                            ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                                            if float(ratio) > 100:
                                                ratio = 100
                                        list3.append(
                                            {'name': l['name'], 'disg': disg_, 'chag': chag_, 'ratio': float(ratio)})
                            else:
                                disg_ = float(l['disg'])
                                chag_ = float(l['chag'])
                                if float(chag_) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                list3.append({'name': l['name'], 'disg': disg_, 'chag': chag_, 'ratio': float(ratio)})
                    else:
                        if list1 != []:
                            for ll in list1:
                                disg_ = float(ll['disg'])
                                chag_ = float(ll['chag'])
                                if float(chag_) == 0:
                                    ratio = 0
                                else:
                                    ratio = ('%.3f' % ((float(disg_) / float(chag_)) * 100))
                                    if float(ratio) > 100:
                                        ratio = 100
                                list3.append(
                                    {'name': ll['name'], 'disg': disg_, 'chag': chag_, 'ratio': float(ratio)})
                    obj = sorted(list3, key=lambda x: x.get("chag"), reverse=True)

                return self.returnTypeSuc(obj)
            elif kt == 'GetPCSPf':  # PCS功率因数
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_PF_4', '15T', obj_sys='PCS', vmax=1)
                        else:
                            self.data_day_Pf(db, 2, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                             vmax=1, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_PF_4', '15T', obj_sys='PCS', vmax=1)
                        else:
                            self.data_day_Pf(db, 2, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                             vmax=1, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                        FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    if PF:
                        dict_pf = {}
                        for f in PF:
                            if f[0] not in dict_pf.keys():
                                dict_pf[f[0]] = {'max': [], 'min': []}
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                            else:
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                        for d in dict_pf.keys():
                            if dict_pf[d]['max'] != [] and dict_pf[d]['min'] != []:
                                obj['data'].append(
                                    {'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                    if endTime[0:10] == now_time[0:10]:
                        dict_1 = {}
                        dict_1['data'] = []
                        startTime = endTime[0:10] + ' 00:00:00'
                        endTime = endTime[0:10] + ' 23:59:59'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                        if db == 'dongmu':
                            dict_1 = self.dongmu_PCS_pf('PCS_PF_4', ed, st)
                            dongmu_session.close()
                        else:
                            result_obj = self.other_PCS(db, 2, endTime, start_Time, tables, lang=lang)
                            for na in result_obj:  # 各个表里最大和最小再次比较
                                max_ = max(result_obj[na])
                                min_ = min(result_obj[na])

                                dict_1['data'].append(
                                    {'name': na, 'max': float('%.3f' % max_), 'min': float('%.3f' % min_)})
                        obj_list = []
                        if dict_1['data']:
                            for d in dict_1['data']:
                                if obj['data']:
                                    for p in obj['data']:
                                        if p['name'] == d['name']:
                                            max_2 = 0
                                            min_2 = 0
                                            if float(p['max']) > d['max']:
                                                max_2 = p['max']
                                            else:
                                                max_2 = p['max']
                                            if float(p['min']) < d['min']:
                                                min_2 = p['min']
                                            else:
                                                min_2 = p['min']
                                            obj_list.append({'name': p['name'], 'max': max_2, 'min': min_2})
                                else:
                                    obj['data'] = dict_1['data']
                            obj['data'] = obj_list
                        else:
                            obj['data'] = obj['data']
                elif report_type == '4':  # 年，
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                        FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    if PF:
                        dict_pf = {}
                        for f in PF:
                            if f[0] not in dict_pf.keys():
                                dict_pf[f[0]] = {'max': [], 'min': []}
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                            else:
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                        for d in dict_pf.keys():
                            obj['data'].append(
                                {'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                elif report_type == '5':  # 累计
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    PF = user_session.query(FReportPcs.pcs_name, FReportPcs.max_p, FReportPcs.min_p).filter(
                        FReportPcs.station == db, FReportPcs.day.between(startTime, endTime)).order_by(
                        FReportPcs.pcs_name.asc()).all()
                    if PF:
                        dict_pf = {}
                        for f in PF:
                            if f[0] not in dict_pf.keys():
                                dict_pf[f[0]] = {'max': [], 'min': []}
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                            else:
                                if float(f[1]) > 1:
                                    dict_pf[f[0]]['max'].append(1)
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                                else:
                                    dict_pf[f[0]]['max'].append(float(f[1]))
                                    dict_pf[f[0]]['min'].append(float(f[2]))
                        for d in dict_pf.keys():
                            obj['data'].append(
                                {'name': d, 'max': max(dict_pf[d]['max']), 'min': min(dict_pf[d]['min'])})
                return self.returnTypeSuc(obj)
            elif kt == 'GetPCSSOCCD':  # PCS SOC,充放电次数
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                # name_f_list = model_config.get('peizhi', db)  # name前半部分
                # timeall = []
                obj = {}

                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报 SOC
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'soc_4', '15T', obj_sys='PCS', vmax=101)
                        elif db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong':
                            self.data_day_ygzhen(db, 0, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                                 vmax=101, lang=lang)
                        else:
                            self.data_day(db, 0, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=101, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.data_day_dongmu(ed, obj, st, 'soc_4', '15T', obj_sys='PCS', vmax=101)
                        elif db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong':
                            self.data_day_ygzhen(db, 0, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                                 vmax=101, lang=lang)
                        else:
                            self.data_day(db, 0, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=101, lang=lang)
                elif report_type == '2' or report_type == '3' or report_type == '4' or report_type == '5':  # 周，月，年，累计充放电次数
                    volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                    names = model_config.get('peizhi', db)  # 充放电量
                    names = json.loads(names)
                    conf_name = get_conf_name(db)
                    list1 = []  # 单个pcs
                    list2 = []
                    name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
                    name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
                    if report_type == '2' or report_type == '3':
                        if endTime[0:10] == now_time[0:10]:
                            list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                    else:
                        if endTime[0:8] == now_time[0:8]:
                            list2 = _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=lang)
                    n = 0
                    if report_type == '5':
                        freports = user_session.query(FReport).filter(FReport.name.in_(names), FReport.cause == 1).all()
                    else:
                        freports = user_session.query(FReport).filter(FReport.day.between(startTime, endTime),
                                                                      FReport.name.in_(names),
                                                                      FReport.cause == 1).all()
                    freport_obj = {}  # 按name为key将值放到列表里
                    for f in freports:
                        if f.name in freport_obj:
                            freport_obj[f.name].append(f)
                        else:
                            freport_obj[f.name] = []
                            freport_obj[f.name].append(f)
                    for name in freport_obj:
                        v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                                  SdaPcsCu.station_name == db).first()
                        if not v_pcs_name:
                            logging.error("未找到对应的pcs_name")
                            continue
                        PCS_name_f = v_pcs_name[0]
                        freport = freport_obj[name]
                        disg = 0  # 放电量
                        chag = 0  # 充电量
                        if freport:
                            for f in freport:
                                disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                                    eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                                chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                                    eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                        list1.append({'name': PCS_name_f, 'disg': int(disg), 'chag': int(chag)})
                        n += 1
                    list3 = []
                    if list2 != []:
                        for l in list2:
                            if list1 != []:
                                for ll in list1:
                                    if l['name'] == ll['name']:
                                        disg_ = float(l['disg']) + float(ll['disg'])
                                        chag_ = float(l['chag']) + float(ll['chag'])
                                        chag_disg_num = math.floor(
                                            (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list1)))
                                        list3.append({'name': l['name'], 'chag_disg_num': chag_disg_num})
                            else:
                                disg_ = float(l['disg'])
                                chag_ = float(l['chag'])
                                chag_disg_num = math.floor(
                                    (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list2)))
                                list3.append({'name': l['name'], 'chag_disg_num': chag_disg_num})
                    else:
                        if list1 != []:
                            for ll in list1:
                                disg_ = float(ll['disg'])
                                chag_ = float(ll['chag'])
                                chag_disg_num = math.floor(
                                    (math.sqrt(disg_ * chag_)) / ((volume[0] * 1000) / len(list1)))
                                list3.append({'name': ll['name'], 'chag_disg_num': chag_disg_num})
                    obj['data'] = sorted(list3, key=lambda x: x.get("chag_disg_num"), reverse=True)
                return self.returnTypeSuc(obj)
            elif kt == 'GetPCST':  # PCS 温度
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))

                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_T_4', '15T', obj_sys='PCS', vmax=100)
                        else:
                            self.data_day(db, 7, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_T_4', '15T', obj_sys='PCS', vmax=100)
                        else:
                            self.data_day(db, 7, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100, lang=lang)
                elif report_type == '2' or report_type == '3' or report_type == '4':  # 周，月，年
                    if db == 'dongmu':
                        obj = self.dongmu_PCS('PCS_T_4', ed, st, obj)
                        dongmu_session.close()
                    else:
                        result_obj = self.other_PCS(db, 7, endTime, start_Time, tables, lang=lang)
                        list_11 = []
                        for na in result_obj:  # 各个表里最大和最小再次比较
                            max_ = max(result_obj[na])
                            min_ = min(result_obj[na])
                            list_11.append({'name': na, 'max': float('%.3f' % max_), 'min': float('%.3f' % min_)})
                        obj['data'] = sorted(list_11, key=lambda x: x.get("max"), reverse=True)
                elif report_type == '5':  # 累计
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    st = timeUtils.timeStrToTamp(str(pages[0]))  # 起始时间绝对秒
                    if db == 'dongmu':
                        obj = self.dongmu_PCS('PCS_T_4', ed, st, obj)
                        dongmu_session.close()
                    else:
                        a = timeUtils.getBetweenMonth(startTime, endTime)
                        tables = 'r_measure' + pd.Series(a)
                        result_obj = self.other_PCS(db, 7, endTime, startTime, tables, lang=lang)
                        list_11 = []
                        for na in result_obj:  # 各个表里最大和最小再次比较
                            max_ = max(result_obj[na])
                            min_ = min(result_obj[na])
                            list_11.append({'name': na, 'max': float('%.3f' % max_), 'min': float('%.3f' % min_)})
                        obj['data'] = sorted(list_11, key=lambda x: x.get("max"), reverse=True)
                return self.returnTypeSuc(obj)

            elif kt == 'GetPCSAcVol':  # PCS交流电压
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报PCS交流电压
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 3, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 3, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                elif report_type == '2':  # 周PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '1H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_2_4_5(db, 3, tables, ed, endTime, obj, st, startTime, '1H', 2, vmax=100000, lang=lang)

                elif report_type == '3':  # 月，PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldy_4', '2H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_day(db, 3, tables, ed, endTime, obj, st, startTime, '2H', obj_sys='PCS', vmax=100000,
                                      lang=lang)
                elif report_type == '4':  # 年PCS交流电压
                    startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldy_4', '6H', 4)
                    else:
                        self.data_2_4_5(db, 3, tables, ed, endTime, obj, st, startTime, '6H', 4, vmax=100000, lang=lang)
                elif report_type == '5':  # 累计PCS交流电压
                    startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldy_4', '6H', 5)
                    else:
                        self.data_2_4_5(db, 3, tables, ed, endTime, obj, st, startTime, '6H', 5, vmax=100000, lang=lang)
                return self.returnTypeSuc(obj)
            elif kt == 'GetPCSAcCurr':  # PCS交流电流
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                timeall = []
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报PCS交流电压
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 4, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 4, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                elif report_type == '2':  # 周PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '1H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_2_4_5(db, 4, tables, ed, endTime, obj, st, startTime, '1H', 2, vmax=100000, lang=lang)
                elif report_type == '3':  # 月PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_jldl_4', '2H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_day(db, 4, tables, ed, endTime, obj, st, startTime, '2H', obj_sys='PCS', vmax=100000,
                                      lang=lang)
                elif report_type == '4':  # 年PCS交流电压
                    startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldl_4', '6H', 4)
                    else:
                        self.data_2_4_5(db, 4, tables, ed, endTime, obj, st, startTime, '6H', 4, vmax=100000, lang=lang)
                elif report_type == '5':  # 累计PCS交流电压
                    # pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    # startTime = str(pages[0])
                    startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_jldl_4', '6H', 5)
                    else:
                        self.data_2_4_5(db, 4, tables, ed, endTime, obj, st, startTime, '6H', 5, vmax=100000, lang=lang)
                return self.returnTypeSuc(obj)
            elif kt == 'GetPCSDcVolt':  # PCS直流电压
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                timeall = []
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报PCS交流电压
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 5, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 5, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                elif report_type == '2':  # 周PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '1H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_2_4_5(db, 5, tables, ed, endTime, obj, st, startTime, '1H', 2, vmax=100000, lang=lang)
                elif report_type == '3':  # 月PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldy_4', '2H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_day(db, 5, tables, ed, endTime, obj, st, startTime, '2H', obj_sys='PCS', vmax=100000,
                                      lang=lang)
                elif report_type == '4':  # 年PCS交流电压
                    startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldy_4', '6H', 4)
                    else:
                        self.data_2_4_5(db, 5, tables, ed, endTime, obj, st, startTime, '6H', 4, vmax=100000, lang=lang)
                elif report_type == '5':  # 累计PCS交流电压
                    startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldy_4', '6H', 5)
                    else:
                        self.data_2_4_5(db, 5, tables, ed, endTime, obj, st, startTime, '6H', 5, vmax=100000, lang=lang)
                return self.returnTypeSuc(obj)
            elif kt == 'GetPCSDcCurr':  # PCS直流电流
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                timeall = []
                obj['data'] = []  # 单个pcs
                if report_type == '1':  # 日报PCS交流电压
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 6, tables, ed, now_time, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '15T', obj_sys='PCS', vmax=100000)
                        else:
                            self.data_day(db, 6, tables, ed, endTime, obj, st, startTime, '15T', obj_sys='PCS',
                                          vmax=100000, lang=lang)
                elif report_type == '2':  # 周PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '1H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_2_4_5(db, 6, tables, ed, endTime, obj, st, startTime, '1H', 2, vmax=100000, lang=lang)
                elif report_type == '3':  # 月PCS交流电压
                    if db == 'dongmu':
                        self.data_day_dongmu(ed, obj, st, 'PCS_zldl_4', '2H', obj_sys='PCS', vmax=100000)
                    else:
                        self.data_day(db, 6, tables, ed, endTime, obj, st, startTime, '2H', obj_sys='PCS', vmax=100000,
                                      lang=lang)
                elif report_type == '4':  # 年PCS交流电压
                    startTime = timeUtils.getAgoMonthD(endTime, 1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldl_4', '6H', 4)
                    else:
                        self.data_2_4_5(db, 6, tables, ed, endTime, obj, st, startTime, '6H', 4, vmax=100000, lang=lang)
                elif report_type == '5':  # 累计PCS交流电压
                    startTime = timeUtils.getAgoMonth(1) + '-01 00:00:00'
                    st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                    a = timeUtils.getBetweenMonth(startTime, endTime)
                    tables = 'r_measure' + pd.Series(a)
                    if db == 'dongmu':
                        self.data_day_dongmu_A_V(ed, obj, st, 'PCS_zldl_4', '6H', 5)
                    else:
                        self.data_2_4_5(db, 6, tables, ed, endTime, obj, st, startTime, '6H', 5, vmax=100000, lang=lang)
                return self.returnTypeSuc(obj)

            elif kt == 'GetCuYoCoFa':  # 簇功率，充放电量
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    name_f_list4 = model_config.get('peizhi', 'halun_cu')  # 哈伦簇name
                    list5 = eval(name_f_list4)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                obj = {}
                obj['data'] = []  # 单个PCS下的簇
                if report_type == '1':  # 日报（有功功率）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('PCS_ClusF_4', ed, obj, st, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_other_PwOtInvt(a, b, db, ed, now_time, list5[1], list5[2], obj, st, startTime,
                                                         tables, lang=lang)
                        else:
                            obj = self.cu_other_PwOtInvt(a, b, db, ed, now_time, list3[8], list3[9], obj, st, startTime,
                                                         tables, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('PCS_ClusF_4', ed, obj, st, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_other_PwOtInvt(a, b, db, ed, now_time, list5[1], list5[2], obj, st, startTime,
                                                         tables, lang=lang)
                        else:
                            obj = self.cu_other_PwOtInvt(a, b, db, ed, endTime, list3[8], list3[9], obj, st, startTime,
                                                         tables, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月充放电量
                    obj = []
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg,
                                                       FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg,
                                                       FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg,
                                                       FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg,
                                                       FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg,
                                                       FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg,
                                                       FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg,
                                                       FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg,
                                                       FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    if endTime[0:10] == now_time[0:10]:
                        dict_1 = {}
                        dict_1['data'] = []
                        startTime = endTime[0:10] + ' 00:00:00'
                        endTime = endTime[0:10] + ' 23:59:59'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                        obj_list = []

                        if db == 'dongmu':
                            obj_list = []
                            list1 = []
                            dm_table = HisDM('r_cumulant')
                            values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()

                            if values_mong:
                                values_1_s = {'datainfo': {}}
                                values_1_e = {'datainfo': {}}
                                values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
                                values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
                                value_s = json.loads(values_1_s['datainfo'])['body']
                                value_e = json.loads(values_1_e['datainfo'])['body']
                                BDcap_cu_s = []  # 电池总放电量
                                BCCap_cu_s = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_s:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                                            BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
                                BDcap_cu_e = []  # 电池总放电量
                                BCCap_cu_e = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_e:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                                            BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
                                BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
                                BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量

                                ratio = 0
                                e = 1
                                for f in BDcap_cu.tolist():  #
                                    if BCCap_cu.tolist()[e - 1] == 0:
                                        ratio = 0
                                    else:
                                        ratio = ('%.3f' % ((f / BCCap_cu.tolist()[e - 1]) * 100))
                                        if float(ratio) > 100:
                                            ratio = 100.0
                                    obj_list.append({'pcs_name': 'PCS-%s' % e, 'cu': [
                                        {'cu_name': '电池簇%s' % e, 'disg': ('%.3f' % f),
                                         'chag': ('%.3f' % BCCap_cu.tolist()[e - 1]), 'ratio': ratio}]})
                                    e += 1
                            dongmu_session.close()
                        else:
                            data, obj_list = self.cu_other_chag_disg(a, b, data, db, ed, list3, max, min, obj_list, st,
                                                                     tables, lang=lang)
                            for k in data.keys():
                                cu = []
                                for kk in data[k].keys():
                                    cu_n = {}
                                    for kkk in data[k][kk].keys():
                                        if kkk == 'chag':
                                            cu_n['chag'] = max(data[k][kk][kkk]) - min(data[k][kk][kkk])
                                        elif kkk == 'disg':
                                            cu_n['disg'] = max(data[k][kk][kkk]) - min(data[k][kk][kkk])
                                    if cu_n['chag'] == 0:
                                        cu_n['ratio'] = 0
                                    else:
                                        cu_n['ratio'] = ('%.3f' % ((cu_n['disg'] / cu_n['chag']) * 100))
                                        if float(cu_n['ratio']) > 100:
                                            cu_n['ratio'] = 100
                                    cu.append({'cu_name': kk, 'chag': ('%.0f' % (cu_n['chag'])),
                                               'disg': ('%.0f' % (cu_n['disg'])), 'ratio': cu_n['ratio']})
                                list3 = sorted(cu, key=lambda x: x.get("chag"), reverse=True)
                                obj_list.append({'pcs_name': k, 'cu': list3})
                        if obj:
                            if obj_list:
                                for o in obj:
                                    indx_1 = obj.index(o)
                                    for c in o['cu']:
                                        indx_2 = o['cu'].index(c)
                                        c['chag'] = int(c['chag']) + int(
                                            '%.0f' % float((obj_list[indx_1]['cu'][indx_2]['chag'])))
                                        c['disg'] = int(c['disg']) + int(
                                            '%.0f' % float((obj_list[indx_1]['cu'][indx_2]['disg'])))
                                        if c['chag'] == 0:
                                            c['ratio'] = 0
                                        else:
                                            c['ratio'] = float('%.3f' % ((c['disg'] / c['chag']) * 100))
                                            if c['ratio'] > 100:
                                                c['ratio'] = 100
                                        o['cu'] = sorted(o['cu'], key=lambda x: x.get("chag"), reverse=True)
                            else:
                                obj = obj
                        else:
                            obj = obj_list
                elif report_type == '4':  # 年充放电量
                    obj = []
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)

                elif report_type == '5':  # 累计充放电量
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    obj = []
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg(chag_disg, db, obj, lang=lang)

                return self.returnTypeSuc(obj)
            elif kt == 'GetCuSOCCD':  # 簇SOC,充放电次数
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'

                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个PCS下的簇
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    name_f_list4 = model_config.get('peizhi', 'halun_cu')  # 哈伦簇name
                    list5 = eval(name_f_list4)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                obj = {}
                obj['data'] = []  # 单个PCS下的簇
                if report_type == '1':  # 日报（SOC）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('cu_CSOC_4', ed, obj, st, lang)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list4[0], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        elif db == 'halun':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list5[0], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        else:
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list3[7], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('cu_CSOC_4', ed, obj, st, lang)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list4[0], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        elif db == 'halun':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list5[0], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        else:
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list3[7], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月，充放电次数
                    obj = []
                    obj_c_d = []
                    volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        # self.cu_chag_disg_num(chag_disg, db,obj, obj_c_d, volume,lang=lang)
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg,
                                                       FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg,
                                                       FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg,
                                                       FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg,
                                                       FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg,
                                                       FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg,
                                                       FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg,
                                                       FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg,
                                                       FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)

                    if endTime[0:10] == now_time[0:10]:
                        dict_1 = {}
                        dict_1['data'] = []
                        startTime = endTime[0:10] + ' 00:00:00'
                        endTime = endTime[0:10] + ' 23:59:59'
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                        obj_list = []
                        obj_list_c_d = []
                        if db == 'dongmu':
                            volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                            if report_type != '4':
                                if startTime < '2023-04-01 00:00:00':
                                    return self.customError('此开始时间下无数据！')
                            dm_table = HisDM('r_cumulant')
                            values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
                            # print (values_mong)
                            if values_mong:
                                values_1_s = {'datainfo': {}}
                                values_1_e = {'datainfo': {}}
                                values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
                                values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
                                value_s = json.loads(values_1_s['datainfo'])['body']
                                value_e = json.loads(values_1_e['datainfo'])['body']

                                BDcap_cu_s = []  # 电池总放电量
                                BCCap_cu_s = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_s:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                                            BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
                                BDcap_cu_e = []  # 电池总放电量
                                BCCap_cu_e = []  # 电池总充电量
                                for r in range(7):
                                    for ii in value_e:
                                        if ii['device'][:4] == 'PCS%s' % (r + 1):
                                            BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                                            BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
                                BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
                                BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量
                                e = 1
                                for f in BDcap_cu.tolist():  #
                                    obj_list.append({'pcs_name': 'PCS-%s' % e, 'cu': [{'cu_name': '电池簇%s' % e,
                                                                                       'chag_disg_num': math.floor((
                                                                                                                       math.sqrt(
                                                                                                                           float(
                                                                                                                               f) * float(
                                                                                                                               BCCap_cu.tolist()[
                                                                                                                                   e - 1]))) / (
                                                                                                                               (
                                                                                                                                           volume[
                                                                                                                                               0] * 1000) / 7))}]})
                                    obj_list_c_d.append({'pcs_name': 'PCS-%s' % e, 'cu': [
                                        {'cu_name': '电池簇%s' % e, 'chag': float(BCCap_cu.tolist()[e - 1]),
                                         'disg': float(f)}]})
                                    e += 1
                            dongmu_session.close()
                        else:
                            volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                            data, obj_list = self.cu_other_chag_disg(a, b, data, db, ed, list3, max, min, obj_list, st,
                                                                     tables, lang=lang)
                            for k in data.keys():
                                cu = []
                                cu_ = []
                                for kk in data[k].keys():
                                    cu_n = {}
                                    for kkk in data[k][kk].keys():
                                        if kkk == 'chag':
                                            cu_n['chag'] = max(data[k][kk][kkk]) - min(data[k][kk][kkk])
                                        elif kkk == 'disg':
                                            cu_n['disg'] = max(data[k][kk][kkk]) - min(data[k][kk][kkk])
                                    if cu_n['chag'] == 0:
                                        chag_disg_num = 0
                                    else:
                                        chag_disg_num = math.floor((math.sqrt(cu_n['disg'] * cu_n['chag'])) / (
                                                    (volume[0] * 1000) / (len(data) * len(data[k]))))
                                    cu.append({'cu_name': kk, 'chag_disg_num': chag_disg_num})
                                    cu_.append({'cu_name': kk, 'chag': cu_n['chag'], 'disg': cu_n['disg']})
                                list3 = sorted(cu, key=lambda x: x.get("chag_disg_num"), reverse=True)
                                list4 = sorted(cu_, key=lambda x: x.get("chag"), reverse=True)
                                obj_list.append({'pcs_name': k, 'cu': list3})
                                obj_list_c_d.append({'pcs_name': k, 'cu': list4})
                        if obj:
                            if obj_list:
                                obj_list_ = []
                                for o in obj_c_d:
                                    indx_1 = obj_c_d.index(o)
                                    cu__ = []
                                    for c in o['cu']:
                                        indx_2 = o['cu'].index(c)
                                        c['chag'] = int(c['chag']) + int(
                                            '%.0f' % float((obj_list_c_d[indx_1]['cu'][indx_2]['chag'])))
                                        c['disg'] = int(c['disg']) + int(
                                            '%.0f' % float((obj_list_c_d[indx_1]['cu'][indx_2]['disg'])))
                                        # c['chag']=int(c['chag'])+int(obj_list_c_d[indx_1]['cu'][indx_2]['chag'])
                                        # c['disg']=int(c['disg'])+int(obj_list_c_d[indx_1]['cu'][indx_2]['disg'])
                                        chag_disg_num = math.floor((math.sqrt(float(c['chag']) * float(c['disg']))) / (
                                                    (volume[0] * 1000) / (len(obj) * len(o['cu']))))
                                        cu__.append({'cu_name': c['cu_name'], 'chag_disg_num': chag_disg_num})
                                    list3 = sorted(cu__, key=lambda x: x.get("chag_disg_num"), reverse=True)
                                    obj_list_.append({'pcs_name': o['pcs_name'], 'cu': list3})
                                obj = obj_list_
                            else:
                                obj = obj
                        else:
                            obj = obj_list
                elif report_type == '4':  # 累计充放电量
                    obj = []
                    obj_c_d = []
                    volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg,
                                                       FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg,
                                                       FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg,
                                                       FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg,
                                                       FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg,
                                                       FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg,
                                                       FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg,
                                                       FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg,
                                                       FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)

                elif report_type == '5':  # 累计充放电量
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    obj = []
                    obj_c_d = []
                    volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                    if db == 'dongmu':
                        chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                       FReportBmsDongmu.chag, FReportBmsDongmu.disg,
                                                       FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'binhai':
                        chag_disg = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                       FReportBmsbinhai.chag, FReportBmsbinhai.disg,
                                                       FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'baodian':
                        chag_disg = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                       FReportBmsBaodian.chag, FReportBmsBaodian.disg,
                                                       FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'taicang':
                        chag_disg = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                       FReportBmsTaicang.chag, FReportBmsTaicang.disg,
                                                       FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'ygzhen':
                        chag_disg = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                       FReportBmsYgzhen.chag, FReportBmsYgzhen.disg,
                                                       FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'zgtian':
                        chag_disg = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                       FReportBmsZgtian.chag, FReportBmsZgtian.disg,
                                                       FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'houma':
                        chag_disg = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                       FReportBmsHouma.chag, FReportBmsHouma.disg,
                                                       FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'datong':
                        chag_disg = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                       FReportBmsDatong.chag, FReportBmsDatong.disg,
                                                       FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)
                    elif db == 'guizhou':
                        chag_disg = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                       FReportBmsGuizhou.chag, FReportBmsGuizhou.disg,
                                                       FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_chag_disg_num(chag_disg, db, obj, obj_c_d, volume, lang=lang)

                return self.returnTypeSuc(obj)
            elif kt == 'GetCuSOH':  # 簇SOH
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                obj['data'] = []  # 单个PCS下的簇
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    b = []
                    for k in list2:
                        b.append(k['value'])

                    a = []
                    for i in list1:
                        a.append(i['value'])
                if report_type == '1':  # 日报（SOH）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('cu_SOH_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list4[1], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        else:
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list3[2], obj, st, startTime, tables,
                                                         vmax=102, lang=lang)
                    else:
                        if db == 'dongmu':
                            if startTime < '2023-04-01 00:00:00':
                                return self.customError('此开始时间下无数据！')
                            self.dongmu_cu_day('cu_SOH_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list4[1], obj, st, startTime, tables,
                                                         vmax=100, lang=lang)
                        else:
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list3[2], obj, st, startTime, tables,
                                                         vmax=102, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月，年,累计SOH最小值
                    obj = []
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                 FReportBmsbinhai.min_soh, FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                 FReportBmsBaodian.min_soh, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                 FReportBmsTaicang.min_soh, FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                 FReportBmsYgzhen.min_soh, FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                 FReportBmsZgtian.min_soh, FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                 FReportBmsHouma.min_soh, FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                 FReportBmsDatong.min_soh, FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'guizhou':
                        SOH = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                 FReportBmsGuizhou.min_soh, FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    if obj == []:
                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            obj_list = []
                            if db == 'dongmu':
                                cu_name = model_config.get('peizhi', 'cu_SOH_4')  # name
                                SOH = 0  # SOH
                                dm_table = HisDM('r_measure')
                                values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                    dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
                                for f in range(7):
                                    d = []
                                    for v in values_mong:
                                        body = json.loads(v['datainfo'])['body']
                                        for b in body:
                                            if b['device'][:4] == 'BMS%s' % (f + 1):
                                                value = float(b[cu_name])  #
                                                if 0 < value <= 100:
                                                    d.append(value)
                                                else:
                                                    d.append(100)
                                    obj_list.append({'pcs_name': 'PCS-%s' % (f + 1), 'cu': [
                                        {'cu_name': '电池簇%s' % (f + 1), 'min': min(d) if d else 100}]})
                                dongmu_session.close()
                            elif db == 'baodian':
                                obj_list = self.cu_soh_other(a, b, db, ed, list4[1], min, st, tables, lang)
                            else:
                                obj_list = self.cu_soh_other(a, b, db, ed, list3[2], min, st, tables, lang)
                            obj = obj_list
                elif report_type == '4':  # 周，月，年,累计SOH最小值
                    obj = []
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                 FReportBmsbinhai.min_soh, FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                 FReportBmsBaodian.min_soh, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                 FReportBmsTaicang.min_soh, FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                 FReportBmsYgzhen.min_soh, FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                 FReportBmsZgtian.min_soh, FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                 FReportBmsHouma.min_soh, FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                 FReportBmsDatong.min_soh, FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'guizhou':
                        SOH = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                 FReportBmsGuizhou.min_soh, FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)

                elif report_type == '5':  # 周，月，年,累计SOH最小值
                    obj = []
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    if db == 'dongmu':
                        SOH = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                 FReportBmsDongmu.min_soh, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'binhai':
                        SOH = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                 FReportBmsbinhai.min_soh, FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'baodian':
                        SOH = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                 FReportBmsBaodian.min_soh, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'taicang':
                        SOH = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                 FReportBmsTaicang.min_soh, FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'ygzhen':
                        SOH = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                 FReportBmsYgzhen.min_soh, FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'zgtian':
                        SOH = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                 FReportBmsZgtian.min_soh, FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'houma':
                        SOH = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                 FReportBmsHouma.min_soh, FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'datong':
                        SOH = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                 FReportBmsDatong.min_soh, FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                    elif db == 'guizhou':
                        SOH = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                 FReportBmsGuizhou.min_soh, FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_soh(SOH, db, min, obj, lang=lang)
                return self.returnTypeSuc(obj)
            elif kt == 'GetCuStRan':  # 簇温度极差
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                time_S = timeUtils.getAgoTime()  # 获取N天前时间,默认一周'
                datestart = now_time[0:8]
                if DEBUG:
                    logging.info(
                        'report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    name_f_list4 = model_config.get('peizhi', 'halun_cu')  # 哈伦簇name
                    list5 = eval(name_f_list4)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                obj['data'] = []  # 单个PCS下的簇
                if report_type == '1':  # 日报（温度极差）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.dongmu_cu_day('cu_StRan_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list4[7], obj, st, startTime, tables,
                                                         vmax=65, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, now_time, list5[5], list5[6], obj, st,
                                                                startTime, tables, vmax=65, lang=lang)
                        else:
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, now_time, list3[6], list3[5], obj, st,
                                                                startTime, tables, vmax=65, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.dongmu_cu_day('cu_StRan_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list4[7], obj, st, startTime, tables,
                                                         vmax=65, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, endTime, list5[5], list5[6], obj, st,
                                                                startTime, tables, vmax=65, lang=lang)
                        else:
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, endTime, list3[6], list3[5], obj, st,
                                                                startTime, tables, vmax=65, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月温度极差最大值
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_t, FReportBmsbinhai.min_t,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_t, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_t, FReportBmsTaicang.min_t,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_t, FReportBmsYgzhen.min_t,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_t, FReportBmsHalun.min_t,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_t, FReportBmsZgtian.min_t,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_t, FReportBmsHouma.min_t,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_t, FReportBmsDatong.min_t,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_t, FReportBmsGuizhou.min_t,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    if obj == []:
                        obj_list = []
                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            obj_list = []
                        if db == 'dongmu':
                            list1 = []
                            cu_name = model_config.get('peizhi', 'cu_StRan_4')  # name
                            obj_list = self.dongmu_cu_v_t_j(cu_name, ed, list1, obj_list, st)
                        elif db == 'baodian':
                            obj_list = self.cu_baodian_StRan_SVRan(a, b, db, ed, list4[7], max, obj_list, st, tables,
                                                                   lang, vmax=65)
                        else:
                            obj_list = self.cu_other_StRan_SVRan(a, b, db, ed, list3[6], list3[5], max, min, obj_list,
                                                                 st, tables, lang, vmax=65)
                        obj = obj_list

                elif report_type == '4':  # 年温度极差最大值
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_t, FReportBmsbinhai.min_t,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_t, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_t, FReportBmsTaicang.min_t,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_t, FReportBmsYgzhen.min_t,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_t, FReportBmsHalun.min_t,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_t, FReportBmsZgtian.min_t,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_t, FReportBmsHouma.min_t,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_t, FReportBmsDatong.min_t,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_t, FReportBmsGuizhou.min_t,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)

                elif report_type == '5':  # 累计温度极差最大值
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_t, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_t, FReportBmsbinhai.min_t,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_t, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_t, FReportBmsTaicang.min_t,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_t, FReportBmsYgzhen.min_t,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_t, FReportBmsHalun.min_t,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_t, FReportBmsZgtian.min_t,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_t, FReportBmsHouma.min_t,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_t, FReportBmsDatong.min_t,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_t, FReportBmsGuizhou.min_t,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)

                return self.returnTypeSuc(obj)
            elif kt == 'GetCuSVRan':  # 簇电压极差
                db = self.get_argument('db', None)  # 电站名称
                report_type = self.get_argument('report_type', 1)  # 1日，2周，3月，4年，5累计，6自定义时间
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                now_time = timeUtils.getNewTimeStr()
                if DEBUG:
                    logging.info('report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, start_Time, end_Time, db))
                startTime = start_Time + ' 00:00:00'
                endTime_ = timeUtils.returnRealTime(end_Time)
                endTime = endTime_ + ' 23:59:59'
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                timeall = []
                obj['data'] = []  # 单个PCS下的簇
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
                    list4 = eval(name_f_list3)
                    name_f_list4 = model_config.get('peizhi', 'halun_cu')  # 哈伦簇name
                    list5 = eval(name_f_list4)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                if report_type == '1':  # 日报（温度极差）
                    if end_Time[0:10] == now_time[0:10]:  # 当天数据
                        st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                        ed = timeUtils.timeStrToTamp(now_time)  # 截止时间绝对秒
                        if db == 'dongmu':
                            self.dongmu_cu_day('cu_SVRan_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, now_time, list4[6], obj, st, startTime, tables,
                                                         vmax=65535, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, now_time, list5[3], list5[4], obj, st,
                                                                startTime, tables, vmax=65535, lang=lang)
                        else:
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, now_time, list3[4], list3[3], obj, st,
                                                                startTime, tables, vmax=65535, lang=lang)
                    else:
                        if db == 'dongmu':
                            self.dongmu_cu_day('cu_SVRan_4', ed, obj, st)
                        elif db == 'baodian':
                            obj = self.other_cu_day_data(a, b, db, ed, endTime, list4[6], obj, st, startTime, tables,
                                                         vmax=65535, lang=lang)
                        elif db == 'halun':
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, endTime, list5[3], list5[4], obj, st,
                                                                startTime, tables, vmax=65535, lang=lang)
                        else:
                            obj = self.cu_StRan_SVRan_other_day(a, b, db, ed, endTime, list3[4], list3[3], obj, st,
                                                                startTime, tables, vmax=65535, lang=lang)
                elif report_type == '2' or report_type == '3':  # 周，月，电压极差最大值
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_v, FReportBmsbinhai.min_v,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_v, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_v, FReportBmsTaicang.min_v,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_v, FReportBmsYgzhen.min_v,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_v, FReportBmsHalun.min_v,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_v, FReportBmsZgtian.min_v,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_v, FReportBmsHouma.min_v,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_v, FReportBmsDatong.min_v,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_v, FReportBmsGuizhou.min_v,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    if obj == []:
                        if endTime[0:10] == now_time[0:10]:
                            dict_1 = {}
                            dict_1['data'] = []
                            startTime = endTime[0:10] + ' 00:00:00'
                            endTime = endTime[0:10] + ' 23:59:59'
                            st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                            ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                            obj_list = []

                            if db == 'dongmu':
                                list1 = []
                                if report_type != '4':
                                    if startTime < '2023-04-01 00:00:00':
                                        return self.customError('此开始时间下无数据！')
                                cu_name = model_config.get('peizhi', 'cu_SVRan_4')  # name
                                obj_list = self.dongmu_cu_v_t_j(cu_name, ed, list1, obj_list, st)
                            elif db == 'baodian':
                                obj_list = self.cu_baodian_StRan_SVRan(a, b, db, ed, list4[6], max, obj_list, st,
                                                                       tables, lang, vmax=65535)
                            else:
                                obj_list = self.cu_other_StRan_SVRan(a, b, db, ed, list3[4], list3[3], max, min,
                                                                     obj_list, st, tables, lang, vmax=65535)
                            obj = obj_list
                elif report_type == '4':  # ，年电压极差最大值
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_v, FReportBmsbinhai.min_v,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_v, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_v, FReportBmsTaicang.min_v,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_v, FReportBmsYgzhen.min_v,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_v, FReportBmsHalun.min_v,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_v, FReportBmsZgtian.min_v,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_v, FReportBmsHouma.min_v,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_v, FReportBmsDatong.min_v,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_v, FReportBmsGuizhou.min_v,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                elif report_type == '5':  # 累计电压极差最大值
                    pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
                    startTime = str(pages[0])
                    obj = []
                    if db == 'dongmu':
                        tt = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                FReportBmsDongmu.max_v, FReportBmsDongmu.en_cu_name).filter(
                            FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'binhai':
                        tt = user_session.query(FReportBmsbinhai.pcs_name, FReportBmsbinhai.cu_name,
                                                FReportBmsbinhai.max_v, FReportBmsbinhai.min_v,
                                                FReportBmsbinhai.en_cu_name).filter(
                            FReportBmsbinhai.day.between(startTime, endTime)).order_by(FReportBmsbinhai.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'baodian':
                        tt = user_session.query(FReportBmsBaodian.pcs_name, FReportBmsBaodian.cu_name,
                                                FReportBmsBaodian.max_v, FReportBmsBaodian.en_cu_name).filter(
                            FReportBmsBaodian.day.between(startTime, endTime)).order_by(
                            FReportBmsBaodian.id.asc()).all()
                        self.cu_dongmu_baodian_t_v(max, obj, tt, lang=lang)
                    elif db == 'taicang':
                        tt = user_session.query(FReportBmsTaicang.pcs_name, FReportBmsTaicang.cu_name,
                                                FReportBmsTaicang.max_v, FReportBmsTaicang.min_v,
                                                FReportBmsTaicang.en_cu_name).filter(
                            FReportBmsTaicang.day.between(startTime, endTime)).order_by(
                            FReportBmsTaicang.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'ygzhen':
                        tt = user_session.query(FReportBmsYgzhen.pcs_name, FReportBmsYgzhen.cu_name,
                                                FReportBmsYgzhen.max_v, FReportBmsYgzhen.min_v,
                                                FReportBmsYgzhen.en_cu_name).filter(
                            FReportBmsYgzhen.day.between(startTime, endTime)).order_by(FReportBmsYgzhen.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'halun':
                        tt = user_session.query(FReportBmsHalun.pcs_name, FReportBmsHalun.cu_name,
                                                FReportBmsHalun.max_v, FReportBmsHalun.min_v,
                                                FReportBmsHalun.en_cu_name).filter(
                            FReportBmsHalun.day.between(startTime, endTime)).order_by(FReportBmsHalun.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'zgtian':
                        tt = user_session.query(FReportBmsZgtian.pcs_name, FReportBmsZgtian.cu_name,
                                                FReportBmsZgtian.max_v, FReportBmsZgtian.min_v,
                                                FReportBmsZgtian.en_cu_name).filter(
                            FReportBmsZgtian.day.between(startTime, endTime)).order_by(FReportBmsZgtian.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'houma':
                        tt = user_session.query(FReportBmsHouma.pcs_name, FReportBmsHouma.cu_name,
                                                FReportBmsHouma.max_v, FReportBmsHouma.min_v,
                                                FReportBmsHouma.en_cu_name).filter(
                            FReportBmsHouma.day.between(startTime, endTime)).order_by(FReportBmsHouma.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'datong':
                        tt = user_session.query(FReportBmsDatong.pcs_name, FReportBmsDatong.cu_name,
                                                FReportBmsDatong.max_v, FReportBmsDatong.min_v,
                                                FReportBmsDatong.en_cu_name).filter(
                            FReportBmsDatong.day.between(startTime, endTime)).order_by(FReportBmsDatong.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                    elif db == 'guizhou':
                        tt = user_session.query(FReportBmsGuizhou.pcs_name, FReportBmsGuizhou.cu_name,
                                                FReportBmsGuizhou.max_v, FReportBmsGuizhou.min_v,
                                                FReportBmsGuizhou.en_cu_name).filter(
                            FReportBmsGuizhou.day.between(startTime, endTime)).order_by(
                            FReportBmsGuizhou.id.asc()).all()
                        self.cu_other_t_v(obj, tt, db, lang=lang)
                return self.returnTypeSuc(obj)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()
            dongmu_session.close()

    def sys_soh(self, SOH, min, obj, two_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if s[1] not in dict_SOH.keys():
                    dict_SOH[s[1]] = []
                    if float(s[0]) != 0:
                        dict_SOH[s[1]].append(s[0])
            for i in two_time_lists:
                min_ = 0
                for d in dict_SOH.keys():
                    if i == str(d)[:10]:
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break
    def sys_soh_5(self, SOH, min, obj, y_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if str(s[1])[:4] not in dict_SOH.keys():
                    dict_SOH[str(s[1])[:4]] = []
                    if s[0] and float(s[0]) != 0:
                        dict_SOH[str(s[1])[:4]].append(float(s[0]))
                else:
                    if s[0] and float(s[0]) != 0:
                        dict_SOH[str(s[1])[:4]].append(float(s[0]))
            for i in y_time_lists:
                for d in dict_SOH.keys():
                    if i == d:
                        # print (dict_SOH[d])
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def sys_soh_year(self, SOH, min, obj, two_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if str(s[1])[:7] not in dict_SOH.keys():
                    dict_SOH[str(s[1])[:7]] = []
                    if float(s[0]) != 0:
                        dict_SOH[str(s[1])[:7]].append(float(s[0]))
                else:
                    if float(s[0]) != 0:
                        dict_SOH[str(s[1])[:7]].append(float(s[0]))
            for i in two_time_lists:
                for d in dict_SOH.keys():
                    if i == d:
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def sys_halun_SOH(self, db, ed, endTime, obj, st, startTime, tables, value1):
        '''系统哈伦SOH'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        me = model_config.get('peizhi', conf_name[10])  # name后半部分
        name_n = []
        for name_f in name_f_list:
            n_ = '%s%s' % (name_f, me)
            name_n.append(n_)
        name_nStr = "','".join(name_n)
        result_obj = {}  # 先查询出各表的最大和最小
        list_time = []
        for table in tables.to_list():
            z = 1
            for d in name_n:
                dd = d.split(".")[0][-1]
                pcs_name = 'PCS-%s' % (z)
                z += 1
                db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                try:
                    v1 = _select_get_his_value_22(db_con, table, d, st, ed, startTime, endTime, '15T', vmax=100.0001,
                                                  minV=0.1)  # 电池簇SOH
                    if v1['value'] != []:
                        value1.append(v1['value'])
                        value2 = v1['time']
                        if obj['time'] == []:
                            obj['time'] = value2
                    db_con.close()
                except:
                    break
                db_con.close()
        if obj['time']:
            for i in obj['time']:
                list_time.append(i[8:16])
        obj['time'] = list_time
        if value1 != []:
            obj['data'].append({'name': 'SOH(%)', 'value': np.round((np.array(value1).min(axis=0)), 3).tolist()})

    def cu_dongmu_baodian_t_v(self, max, obj, tt, lang=None):
        '''簇东睦保电温度电压（2345）'''
        if tt:
            dict_tt = {}
            for s in tt:
                if lang == 'en':
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[3] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[3]] = []
                            dict_tt[s[0]][s[3]].append(float(s[2]))
                    else:
                        if s[3] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[3]] = []
                            dict_tt[s[0]][s[3]].append(float(s[2]))
                        else:
                            dict_tt[s[0]][s[3]].append(float(s[2]))
                else:
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            if s[2]:
                                dict_tt[s[0]][s[1]].append(float(s[2]))
                    else:
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            if s[2]:
                                dict_tt[s[0]][s[1]].append(float(s[2]))
                        else:
                            if s[2]:
                                dict_tt[s[0]][s[1]].append(float(s[2]))
            for d in dict_tt.keys():
                cu = []
                for ss in dict_tt[d].keys():
                    cu.append({'cu_name': ss, 'max': max(dict_tt[d][ss])})
                list2 = sorted(cu, key=lambda x: x.get("max"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list2})

    def cu_other_t_v(self, obj, tt, db, lang=None):
        '''簇其他电站温度电压（2345）'''
        if tt:
            dict_tt = {}
            for s in tt:
                if lang == 'en':
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[4] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[4]] = []
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                    else:
                        if s[4] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[4]] = []
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                        else:
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[4]].append(max_)
                else:
                    if s[0] not in dict_tt.keys():
                        dict_tt[s[0]] = {}
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
                    else:
                        if s[1] not in dict_tt[s[0]].keys():
                            dict_tt[s[0]][s[1]] = []
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
                        else:
                            if float(s[3]) != 0:
                                max_ = float(s[2]) - float(s[3])
                                if max_ < 0:
                                    max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
                            elif float(s[3]) == 0:
                                max_ = 0
                                dict_tt[s[0]][s[1]].append(max_)
            for d in dict_tt.keys():
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                          SdaPcsCu.station_name == db).first()
                cu = []
                for ss in dict_tt[d].keys():
                    max_tt = ('%.3f' % max(dict_tt[d][ss]))
                    cu.append({'cu_name': ss, 'max': max_tt})
                list2 = sorted(cu, key=lambda x: x.get("max"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list2})

    def cu_chag_disg_num(self, chag_disg, db, obj, obj_c_d, volume, lang=None):
        '''簇充放电次数'''
        if chag_disg:
            dict_chag_disg = {}
            for s in chag_disg:
                if lang == 'en':
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                    else:
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                else:
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                    else:
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
            for d in dict_chag_disg.keys():
                cu = []
                cu_ = []
                for ss in dict_chag_disg[d].keys():
                    chag_ = sum(dict_chag_disg[d][ss]['chag'])
                    disg_ = sum(dict_chag_disg[d][ss]['disg'])
                    chag_disg_num = math.floor((math.sqrt(float(chag_) * float(disg_))) / (
                                (volume[0] * 1000) / (len(dict_chag_disg) * len(dict_chag_disg[d]))))
                    cu_.append({'cu_name': ss, 'chag': chag_, 'disg': disg_})
                    cu.append({'cu_name': ss, 'chag_disg_num': chag_disg_num})
                list3 = sorted(cu, key=lambda x: x.get("chag_disg_num"), reverse=True)
                list4 = sorted(cu_, key=lambda x: x.get("chag"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list3})
                obj_c_d.append({'pcs_name': d, 'cu': list4})

    def cu_chag_disg(self, chag_disg, db, obj, lang=None):
        '''簇充放电量'''
        if chag_disg:
            dict_chag_disg = {}
            for s in chag_disg:
                if lang == 'en':
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                    else:
                        if s[4] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[4]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[4]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[4]]['disg'].append(float(s[3]))
                else:
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                    else:
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
            for d in dict_chag_disg.keys():
                cu = []
                for ss in dict_chag_disg[d].keys():
                    chag_ = float('%.0f' % sum(dict_chag_disg[d][ss]['chag']))
                    disg_ = float('%.0f' % sum(dict_chag_disg[d][ss]['disg']))
                    if chag_ == 0:
                        ratio_ = 0
                    else:
                        ratio_ = float('%.3f' % ((disg_ / chag_) * 100))
                        if ratio_ > 100:
                            ratio_ = 100
                    cu.append({'cu_name': ss, 'chag': chag_, 'disg': disg_, 'ratio': ratio_})
                list3 = sorted(cu, key=lambda x: x.get("chag"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list3})

    def sys_soh_year(self, SOH, min, obj, two_time_lists):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if str(s[1])[:7] not in dict_SOH.keys():
                    dict_SOH[str(s[1])[:7]] = []
                    if float(s[0]) != 0:
                        dict_SOH[str(s[1])[:7]].append(s[0])
                else:
                    if float(s[0]) != 0:
                        dict_SOH[str(s[1])[:7]].append(s[0])
            for i in two_time_lists:
                for d in dict_SOH.keys():
                    if i == d:
                        if dict_SOH[d] == []:
                            min_ = 100
                        else:
                            min_ = min(dict_SOH[d])
                        obj['time'].append(i)
                        obj['data'].append({'min': min_})
                        break

    def cu_soh(self, SOH, db, min, obj, lang=None):
        if SOH:
            dict_SOH = {}
            for s in SOH:
                if lang == 'en':
                    if s[0] not in dict_SOH.keys():
                        dict_SOH[s[0]] = {}
                        if s[3] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[3]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                    else:
                        if s[3] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[3]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                        else:
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[3]].append(float(s[2]))
                else:
                    if s[0] not in dict_SOH.keys():
                        dict_SOH[s[0]] = {}
                        if s[1] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[1]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
                    else:
                        if s[1] not in dict_SOH[s[0]].keys():
                            dict_SOH[s[0]][s[1]] = []
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
                        else:
                            if s[2] and float(s[2]) != 0:
                                dict_SOH[s[0]][s[1]].append(float(s[2]))
            for d in dict_SOH.keys():
                cu = []
                for ss in dict_SOH[d].keys():
                    cu.append({'cu_name': ss, 'min': min(dict_SOH[d][ss])})
                list3 = sorted(cu, key=lambda x: x.get("min"), reverse=True)
                obj.append({'pcs_name': d, 'cu': list3})

    def cu_other_chag_disg(self, a, b, data, db, ed, list3, max, min, obj, st, tables, lang=None):
        '''簇其他电站充放电量'''
        obj = []
        data = {}
        for table in tables.to_list():
            z = 0
            for d in a:
                if db == 'baodian':
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id ==  (d+'.'),
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,SdaPcsCu.station_name == db).first()

                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = {}
                z += 1
                aa = 1
                for d1 in b:
                    cu_name = '电池簇%s' % (aa)
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = {}
                    aa += 1
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, list3[0])  # 累计充电电量的name
                    n = '%s%s%s' % (d, d1, list3[1])  # 累计放电电量的name
                    try:
                        v1 = _select_get_des_value(db_con, table, m, maxV=1000000, minV=0.1)  # 总充电量
                        value1 = v1['value']
                    except:
                        value1 = None
                    if value1 == None:
                        max_value = 0
                        min_value = 0
                    elif value1 != []:
                        max_value = max(value1)
                        min_value = min(value1)
                    else:
                        max_value = 0
                        min_value = 0

                    if 'chag' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['chag'] = []
                        data[pcs_name][cu_name]['chag'].append(max_value)
                        data[pcs_name][cu_name]['chag'].append(min_value)
                    else:
                        data[pcs_name][cu_name]['chag'].append(max_value)
                        data[pcs_name][cu_name]['chag'].append(min_value)
                    try:
                        v2 = _select_get_des_value(db_con, table, n, maxV=1000000, minV=0.1)  # 总放电量
                        value2 = v2['value']
                    except:
                        value2 = None
                    if value2 == None:
                        max_value = 0
                        min_value = 0
                    elif value2 != []:
                        max_value = max(value2)
                        min_value = min(value2)
                        # f_value = max_value - min_value
                        # data[pcs_name][cu_name].append({'disg': f_value})
                    else:
                        max_value = 0
                        min_value = 0
                    if 'disg' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['disg'] = []
                        data[pcs_name][cu_name]['disg'].append(max_value)
                        data[pcs_name][cu_name]['disg'].append(min_value)
                    else:
                        data[pcs_name][cu_name]['disg'].append(max_value)
                        data[pcs_name][cu_name]['disg'].append(min_value)
                    db_con.close()
        return data, obj

    def cu_other_chag_disg_5(self, a=None, b=None, db=None, ed=None, list3=None, max=None, st=None, tables=None):
        '''簇其他电站充放电量'''
        obj = []
        data = {}
        for table in tables.to_list():
            z = 1
            for d in a:
                pcs_name = 'PCS-%s' % (z)
                if pcs_name not in data:
                    data[pcs_name] = {}
                z += 1
                aa = 1
                for d1 in b:
                    cu_name = '电池簇%s' % (aa)
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = {}
                    aa += 1
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, list3[0])  # 累计充电电量的name
                    n = '%s%s%s' % (d, d1, list3[1])  # 累计放电电量的name
                    try:
                        v1 = _select_get_his_value_n_y(db_con, table, m, st, ed, vmax=1000000)  # 总充电量
                        value1 = v1['value']
                    except:
                        value1 = None
                    if value1 == None:
                        max_value = 0
                    elif value1 != []:
                        max_value = max(value1)
                    else:
                        max_value = 0

                    if 'chag' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['chag'] = []
                        data[pcs_name][cu_name]['chag'].append(max_value)
                    else:
                        data[pcs_name][cu_name]['chag'].append(max_value)
                    try:
                        v2 = _select_get_his_value_n_y(db_con, table, n, st, ed, vmax=1000000)  # 总放电量
                        value2 = v2['value']
                    except:
                        value2 = None
                    if value2 == None:
                        max_value = 0
                    elif value2 != []:
                        max_value = max(value2)
                        # f_value = max_value - min_value
                        # data[pcs_name][cu_name].append({'disg': f_value})
                    else:
                        max_value = 0
                    if 'disg' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['disg'] = []
                        data[pcs_name][cu_name]['disg'].append(max_value)
                    else:
                        data[pcs_name][cu_name]['disg'].append(max_value)
                    db_con.close()
        return data, obj

    def cu_other_PwOtInvt(self, a, b, db, ed, endTime, name_1, name_2, obj, st, startTime, tables, lang=None):
        '''簇其他站功率计算'''
        data = {}
        obj['data'] = []
        obj['time'] = []
        n = 0
        time_ = []
        for table in tables.to_list():
            for d in a:
                d_ = d + '.' if db == 'baodian' else d
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                          SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                PCS_name_f = v_pcs_name[0]
                if PCS_name_f not in data:
                    data[PCS_name_f] = []
                    n += 1
                    aa = 1
                    if db == 'halun':
                        b = [1, 2, 3, 4, 5, 6, 7]
                    for d1 in b:
                        if lang == 'en':
                            dcc_name = 'Battery cluster%s' % (aa)
                        else:
                            dcc_name = '电池簇%s' % (aa)
                        aa += 1
                        if db == 'baodian':
                            db_con = _return_db_con(db, d)  # 生成数据库连接
                        elif db == 'halun':
                            dd = d.split(".")[0][-1]
                            db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                        elif db == 'houma':
                            dd = int(d[1])
                            db_con = _return_db_con_cu(db, dd)  # 生成数据库连接
                        else:
                            db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                        if db == 'halun':
                            n0 = '%s%s%s' % (d, name_1, d1)  # 电池簇电压
                            n1 = '%s%s%s' % (d, name_2, d1)  # 电池簇电流
                        else:
                            n0 = '%s%s%s' % (d, d1, name_1)  # 电池簇电压
                            n1 = '%s%s%s' % (d, d1, name_2)  # 电池簇电流
                        try:
                            v1 = _select_get_his_value(db_con, table, n0, st, ed, startTime, endTime, '15T',
                                                       vmax=1000000)  # 电池簇电压
                            value1 = v1['value']  # 电池簇电压
                            v2 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, '15T',
                                                       vmax=1000000)  # 电池簇电流
                            value2 = v2['value']  # 电池簇电流
                        except:
                            break
                        try:
                            PwOtInvt = np.array(value2) * np.array(value1)  # 电池簇功率
                        except:
                            if len(value1) == 1:
                                PwOtInvt = value2
                            else:
                                PwOtInvt = value1

                        if time_ == []:
                            time_ = v1['time']
                        data[PCS_name_f].append({'cu_name': dcc_name,
                                                 'value': np.round([float(item) / 1000 for item in PwOtInvt],
                                                                   3).tolist()})
                        db_con.close()
        if time_:
            for i in time_:
                obj['time'].append(i[8:16])
        for k in data.keys():
            obj['data'].append({'pcs_name': k, 'cu': data[k]})
        return obj

    def sys_other_SOH_day(self, a, b, db, ed, endTime, name_a, obj, st, startTime, tables, value1):
        '''系统SOH日数据'''
        list_time = []
        for table in tables.to_list():
            for d in a:
                for d1 in b:
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, name_a)
                    try:
                        v1 = _select_get_his_value_22(db_con, table, m, st, ed, startTime, endTime, '15T',
                                                      vmax=100)  # 电池簇SOH
                        if v1['value'] != []:
                            value1.append(v1['value'])
                            value2 = v1['time']
                            if obj['time'] == []:
                                obj['time'] = value2
                        db_con.close()
                    except:
                        break
        if obj['time']:
            for i in obj['time']:
                list_time.append(i[8:16])
        obj['time'] = list_time
        if value1 != []:
            # obj['data'].append({'name': 'SOH(%)', 'value': np.round((np.array(value1).mean(axis=0)), 3).tolist()})
            obj['data'].append({'name': 'SOH(%)', 'value': np.round((np.array(value1).min(axis=0)), 3).tolist()})

    def sys_soh_day_dongmu(self, ed, obj, st):
        '''系统东睦SOH'''
        dataP1, data2 = [], {}  # 有功,
        cu_name = model_config.get('peizhi', 'cu_SOH_4')  # name
        dm_table = HisDM('r_measure')
        obj_sys_list = []
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            for e in range(dongmu_num):
                BMS_ = 'BMS%s' % (e + 1)
                d2 = []
                for ii in alldata:
                    if ii['device'] == BMS_:
                        value = ii[cu_name]  #
                        if float(value) <= 100:
                            d2.append(value)
                        # 取相同的间隔时间(采用补数的方法)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()
                if data12["time"]:
                    data22 = complete_data(data12, '15T')
                    list_time = []
                    if data22:
                        for i in data22['time']:
                            list_time.append(i[8:16])
                    data2['time'] = list_time
                    obj['time'] = list_time
                    for d in data22['value']:
                        try:
                            if math.isnan(d) is True:
                                indx = data22['value'].index(d)
                                data22['value'][indx] = 80
                        except:
                            break
                    obj_sys_list.append(list(map(float, data22['value'])))
            obj['data'].append({'name': 'SOH(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
            dongmu_session.close()

    def cu_other_StRan_SVRan(self, a, b, db, ed, name_1, name_2, max, min, obj, st, tables, lang, vmax=None):
        '''簇其他电站温度和电压极差(2345)'''
        data = {}
        obj = []
        for table in tables.to_list():
            for d in a:
                if db == 'baodian':
                    d_ = d + '.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                        SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = {}
                for d1 in b:
                    if db == 'baodian':
                        d1_ = d1[1:]
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1_, SdaPcsCu.station_name == db).first()
                    else:
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                    if lang == 'en':
                        cu_name = v_cu_name[1]
                    else:
                        cu_name = v_cu_name[0]
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = []
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    n0 = '%s%s%s' % (d, d1, name_1)  # 电池簇最低温度
                    n1 = '%s%s%s' % (d, d1, name_2)  # 电池簇最高温度
                    try:
                        v1 = _select_get_his_value_n_y(db_con, table, n0, st, ed, vmax=vmax)  # 电池簇最低温度
                        value1 = v1['value']  # 最低温度
                    except:
                        value1 = [0]

                    if value1 != []:
                        min_ = min(value1) if value1 else 0
                    else:
                        min_ = 0
                    try:
                        v2 = _select_get_his_value_n_y(db_con, table, n1, st, ed, vmax=vmax)  # 电池簇最高温度
                        value2 = v2['value']  # 最高温度
                    except:
                        value2 = [0]

                    if value2 != []:
                        max_ = max(value2) if value2 else 0
                    else:
                        max_ = 0
                    BCTD = max_ - min_  # 电池簇温度差
                    if BCTD < 0:
                        BCTD = 0
                    data[pcs_name][cu_name].append('%.3f' % BCTD)
        for k in data.keys():
            cu = []
            for kk in data[k].keys():
                cu.append({'cu_name': kk, 'max': max(data[k][kk]) if data[k][kk] else 0})
            list2 = sorted(cu, key=lambda x: x.get("max"), reverse=True)
            obj.append({'pcs_name': k, 'cu': list2})
        return obj

    def cu_baodian_StRan_SVRan(self, a, b, db, ed, name_a, max, obj, st, tables, lang, vmax=None):
        '''簇保电温度和电压极差（2345）'''
        data = {}
        obj = []
        for table in tables.to_list():
            for d in a:
                if db == 'baodian':
                    d_ = d + '.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                              SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = {}
                for d1 in b:
                    if db == 'baodian':
                        d1_ = d1[1:]
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1_, SdaPcsCu.station_name == db).first()
                    else:
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                    if lang == 'en':
                        cu_name = v_cu_name[1]
                    else:
                        cu_name = v_cu_name[0]
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = []
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, name_a)
                    try:
                        v1 = _select_get_his_value_n_y(db_con, table, m, st, ed, vmax=vmax)  # 电池簇SOH
                        value1 = v1['value']
                    except:
                        value1 = [0]
                    if value1 != []:
                        max_ = max(value1)
                    else:
                        max_ = 0
                    data[pcs_name][cu_name].append('%.3f' % max_)

                    db_con.close()
        for k in data.keys():
            cu = []
            for kk in data[k].keys():
                cu.append({'cu_name': kk, 'max': max(data[k][kk] if data[k][kk] else 0)})
            list2 = sorted(cu, key=lambda x: x.get("max"), reverse=True)
            obj.append({'pcs_name': k, 'cu': list2})
        return obj

    def cu_StRan_SVRan_other_day(self, a, b, db, ed, endTime, name_1, name_2, obj, st, startTime, tables, vmax=None,
                                 lang=None):
        '''其他电站日温度和电压极差'''
        data = {}
        obj['data'] = []
        obj['time'] = []
        time_ = []
        for table in tables.to_list():
            for d in a:
                if db == 'baodian':
                    d_ = d + '.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                              SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = []
                    if db == 'halun':
                        b = [1, 2, 3, 4, 5, 6, 7]
                    for d1 in b:
                        if db == 'baodian':
                            d1_ = d1[1:]
                            v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                                SdaPcsCu.cluster_id == d1_, SdaPcsCu.station_name == db).first()
                        else:
                            v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                                SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                        if lang == 'en':
                            dcc_name = v_cu_name[1]
                        else:
                            dcc_name = v_cu_name[0]
                        if db == 'halun':
                            dd = d.split(",")[0][-1]
                            db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                        elif db == 'houma':
                            dd = int(d[1])
                            db_con = _return_db_con_cu(db, dd)  # 生成数据库连接
                        else:
                            db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                        if db == 'halun':
                            n0 = '%s%s%s' % (d, name_1, d1)  # 电池簇最低温度
                            n1 = '%s%s%s' % (d, name_2, d1)  # 电池簇最高温度
                        else:
                            n0 = '%s%s%s' % (d, d1, name_1)  # 电池簇最低温度
                            n1 = '%s%s%s' % (d, d1, name_2)  # 电池簇最高温度
                            n = '%s%s' % (d, d1)
                        try:
                            v1 = _select_get_his_value(db_con, table, n0, st, ed, startTime, endTime, '15T',
                                                       vmax=vmax)  # 电池簇最低温度
                            value1 = v1['value']  # 最低温度
                            if time_ == []:
                                time_ = v1['time']
                        except:
                            value1 = [0]
                        try:
                            v2 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, '15T',
                                                       vmax=vmax)  # 电池簇最高温度
                            value2 = v2['value']  # 最高温度
                        except:
                            value2 = [0]
                        try:
                            BCTD = (np.array(value2) - np.array(value1)).tolist()  # 电池簇温度差
                        except:
                            if len(value1) == 1:
                                BCTD = value2
                            else:
                                BCTD = value1
                        for B in BCTD:
                            if B < 0:
                                indx = BCTD.index(B)
                                BCTD[indx] = 0
                        data[pcs_name].append({'cu_name': dcc_name, 'value': np.round(BCTD, 3).tolist()})
                        db_con.close()
        if time_:
            for i in time_:
                obj['time'].append(i[8:16])
        for k in data.keys():
            obj['data'].append({'pcs_name': k, 'cu': data[k]})
        return obj

    def cu_soh_other(self, a, b, db, ed, name_a, min, st, tables, lang):
        '''簇SOH其他电站计算（2345）'''
        data = {}
        obj = []
        for table in tables.to_list():
            for d in a:
                if db == 'baodian':
                    d_ = d + '.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                              SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = {}
                for d1 in b:
                    if db == 'baodian':
                        d1_ = d1[1:]
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1_, SdaPcsCu.station_name == db).first()
                    else:
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                            SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                    if lang == 'en':
                        cu_name = v_cu_name[1]
                    else:
                        cu_name = v_cu_name[0]
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = []
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, name_a)
                    try:
                        v1 = _select_get_his_value_n_y(db_con, table, m, st, ed, vmax=100)  # 电池簇SOH
                        value1 = v1['value']
                    except:
                        value1 = [0]
                    if value1 != []:
                        min_ = min(value1)
                        if min_ == 0:
                            min_ = 100
                    else:
                        min_ = 100
                    data[pcs_name][cu_name].append('%.3f' % min_)
                    db_con.close()
        for k in data.keys():
            cu = []
            for kk in data[k].keys():
                cu.append({'cu_name': kk, 'min': min(data[k][kk]) if data[k][kk] else 0})
            list3 = sorted(cu, key=lambda x: x.get("min"), reverse=True)
            obj.append({'pcs_name': k, 'cu': list3})
        return obj

    def other_cu_day_data(self, a, b, db, ed, endTime, name_a, obj, st, startTime, tables, vmax=None, lang=None):
        '''其他电站簇SOH，保电日数据'''
        data = {}
        time_ = []
        for table in tables.to_list():
            for d in a:
                if db == 'baodian':
                    d_ = d + '.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,
                                                                              SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,
                                                                              SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = []
                    if db == 'halun':
                        b = [1, 2, 3, 4, 5, 6, 7]
                    for d1 in b:
                        if db == 'baodian':
                            d1_ = d1[1:]
                            v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                                SdaPcsCu.cluster_id == d1_, SdaPcsCu.station_name == db).first()
                        else:
                            v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(
                                SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                        if lang == 'en':
                            dcc_name = v_cu_name[1]
                        else:
                            dcc_name = v_cu_name[0]
                        if db == 'baodian':
                            db_con = _return_db_con(db, d)  # 生成数据库连接
                        elif db == 'halun':
                            dd = d.split(".")[0][-1]
                            db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                        elif db == 'houma':
                            dd = int(d[1])
                            db_con = _return_db_con_cu(db, dd)  # 生成数据库连接
                        else:
                            db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                        if db == 'halun':
                            m = '%s%s%s' % (d, name_a, d1)
                        else:
                            m = '%s%s%s' % (d, d1, name_a)
                        try:
                            v1 = _select_get_his_value(db_con, table, m, st, ed, startTime, endTime, '15T',
                                                       vmax=vmax)  # 电池簇SOH
                        except:
                            break
                        value = v1['value']
                        if time_ == []:
                            time_ = v1['time']
                        data[pcs_name].append({'cu_name': dcc_name, 'value': np.round(value, 3).tolist()})
                        db_con.close()

        list_ti = []
        if time_:
            for i in time_:
                list_ti.append(i[8:16])
        obj['time'] = list_ti
        for k in data.keys():
            obj['data'].append({'pcs_name': k, 'cu': data[k]})
        return obj

    def dongmu_PCS(self, name, ed, st, obj):
        '''东睦PCS获取因数，温度'''
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        list_ = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            if values_mong:
                for v in values_mong:
                    body = json.loads(v['datainfo'])['body']
                    for b in body:
                        if b['device'][:4] == 'PCS%s' % (f + 1):
                            value = float(b[cu_name])  #
                            if 0 < value <= 100:
                                d.append(value)
                            else:
                                d.append(0)
                list_.append({'name': 'PCS-%s' % (f + 1), 'max': float('%.3f' % max(d)), 'min': float('%.3f' % min(d))})
        obj['data'] = sorted(list_, key=lambda x: x.get('max'), reverse=True)
        return obj

    def dongmu_PCS_pf(self, name, ed, st):
        '''东睦PCS获取因数，温度'''
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        list_ = {}
        list_['data'] = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            if values_mong:
                for v in values_mong:
                    body = json.loads(v['datainfo'])['body']
                    for b in body:
                        if b['device'][:4] == 'PCS%s' % (f + 1):
                            value = float(b[cu_name])  #
                            if 0 < value <= 100:
                                d.append(value)
                            else:
                                d.append(0)
            else:
                d.extend([1, 0])
            list_['data'].append(
                {'name': 'PCS-%s' % (f + 1), 'max': float('%.3f' % max(d)), 'min': float('%.3f' % min(d))})
        return list_

    def sys_5_time_list(self, db, end_Time):
        '''系统累计时间列表'''
        pages = user_session.query(Station.start_ts).filter(Station.name == db).first()  # 获取电站
        two_time_lists = []
        st = int(str(pages[0])[0:4])
        et = int(end_Time[0:4])
        if et - st > 0:
            tt = et - 1
            if tt - st > 1:
                ttt = tt - 1
                if ttt - st > 0:
                    two_time_lists.append(st)
                    two_time_lists.append(tt)
                    two_time_lists.append(ttt)
                    two_time_lists.append(et)
            else:
                two_time_lists.append(st)
                two_time_lists.append(tt)
                two_time_lists.append(et)
        y_time_lists = list(set(two_time_lists))
        return y_time_lists

    def ys_chag_disg_num(self, db, freports, obj, two_time_lists, m, list2):
        '''系统充放电次数'''
        volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
        n = 1
        freport_obj = {}  # 按name为key将值放到列表里
        for f in freports:
            if f.name in freport_obj:
                freport_obj[f.name].append(f)
            else:
                freport_obj[f.name] = []
                freport_obj[f.name].append(f)
        le = len(two_time_lists)
        for t in two_time_lists:
            index = two_time_lists.index(t)
            disg = 0  # 放电量
            chag = 0  # 充电量
            if freports:
                for f in freports:
                    if str(f.day)[:m] == str(t)[:m]:
                        disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                            eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                        chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                            eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
            if index == le - 1:
                if list2 != []:  # index == le-1（拿到最后一个值）
                    disg_ = 0
                    chag_ = 0
                    for l in list2:
                        disg_ += float(l['disg'])
                        chag_ += float(l['chag'])
                    disg1 = disg + disg_  # 放电量
                    chag1 = chag + chag_  # 充电量
            else:
                disg1 = disg
                chag1 = chag
            chag_disg_num = math.floor((math.sqrt(disg1 * chag1)) / (volume[0] * 1000))
            obj['data'].append(chag_disg_num)
            n += 1

    def other_sys_PF_(self, db, ti, endTime, max, min, start_Time, tables):
        '''系统其他电站因数计算'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        PCS_PF = model_config.get('peizhi', conf_name[2])  # name后半部分
        PCS_PF_n = []
        for name_f in name_f_list:
            n_ = '%s%s' % (name_f, PCS_PF)
            PCS_PF_n.append(n_)
        PCS_PF_nStr = "','".join(PCS_PF_n)
        result_obj = {}  # 先查询出各表的最大和最小
        for table in tables.to_list():
            for dict_db in all_datas[db]:  # 需要查询的数据库
                if _tableIsExist(dict_db[0], dict_db[2], table):  # 确定表存在
                    sql = "select DATE_FORMAT(FROM_UNIXTIME(dts_s),'{4}') ,max(value)mm,min(value)mi from {0} where value>0 and value<=1 and " \
                          "FROM_UNIXTIME(dts_s) Between '{1}' and '{2}' and " \
                          "name in('{3}')group by DATE_FORMAT(FROM_UNIXTIME(dts_s),'{5}') order by DATE_FORMAT(FROM_UNIXTIME(dts_s),'{6}') asc".format(
                        table, start_Time, endTime, PCS_PF_nStr, ti, ti, ti)
                    conn = dict_db[0].raw_connection()  # 拿原生的连接
                    cursor = conn.cursor()
                    cursor.execute(sql)
                    result = cursor.fetchall()
                    cursor.close()
                    conn.close()
                    if result:  # 先查询出各表的最大和最小
                        for r in result:
                            max_ = r[1]
                            min_ = r[2]
                            time_n = r[0]
                            if time_n in result_obj:
                                result_obj[time_n].append(max_)
                                result_obj[time_n].append(min_)
                            else:
                                result_obj[time_n] = []
                                result_obj[time_n].append(max_)
                                result_obj[time_n].append(min_)
        list_ = []
        for key in result_obj.keys():  # 各个表里最大和最小再次比较

            max_ = max(result_obj[key])
            min_ = min(result_obj[key])
            list_.append({'max': str(max_), 'min': str(min_)})
        # time_list = timeUtils.dateToDataList(start_Time, endTime)
        # for t in time_list:
        #     if t not in result_obj.keys():
        #         result_obj[t] = [0]
        # dict_s =sorted(result_obj)#字典key排序
        # # dict_s =sorted(result_obj.keys(), reverse=True)
        # for key in dict_s:  # 各个表里最大和最小再次比较
        #     max_ = max(result_obj[key])
        #     min_ = min(result_obj[key])
        #     obj['data'].append({'max': max_, 'min': min_})
        return list_

    def sys_chag_disg(self, freports, obj, m, two_time_lists):
        '''系统充放电量'''
        n = 1
        for t in two_time_lists:  # 按时间循环
            disg = 0  # 放电量
            chag = 0  # 充电量
            ratio = 0  # 效率
            if freports:
                for f in freports:
                    if str(f.day)[:m] == str(t)[:m]:
                        disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                            eval(f.pd_disg)) + np.sum(eval(f.gd_disg))
                        chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                            eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
            if float(chag) == 0:
                ratio = 0
            else:
                ratio = ('%.3f' % ((float(disg) / float(chag)) * 100))
                if float(ratio) > 100:
                    ratio = 100
            obj['data'].append({'disg': int(disg), 'chag': int(chag), 'ratio': float(ratio)})
            n += 1

    def other_sys_SOC_d(self, db, na, ed, endTime, obj, st, startTime, tables, z, lang=None):
        '''系统其他电站SOC,因数日数据计算'''
        if z == 1:
            if db == 'ygzhen':
                name_f_list = model_config.get('peizhi', 'ygzhen')  # name前半部分
            elif db == 'zgtian':
                name_f_list = model_config.get('peizhi', 'zgtian')  # name前半部分
            elif db == 'houma':
                name_f_list = model_config.get('peizhi', 'houma')  # name前半部分
            elif db == 'datong':
                name_f_list = model_config.get('peizhi', 'datong')  # name前半部分
            else:
                name_f_list = model_config.get('peizhi', db)  # name前半部分
        else:
            if db == 'ygzhen':
                name_f_list = model_config.get('peizhi', 'ygzhen_bms')  # name前半部分
            elif db == 'zgtian':
                name_f_list = model_config.get('peizhi', 'zgtian_bms')  # name前半部分
            elif db == 'houma':
                name_f_list = model_config.get('peizhi', 'houma_bms')  # name前半部分
            elif db == 'datong':
                name_f_list = model_config.get('peizhi', 'datong_bms')  # name前半部分
            else:
                name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        name = model_config.get('peizhi', na)  # name后半部分
        obj_sys_list = []
        list_time = []
        obj['time'] = []
        for table in tables.to_list():  # 需要查询的表
            # for dict_db in all_datas[db]:  # 需要查询的数据库
            #     if _tableIsExist(dict_db[0], dict_db[2], table):  # 确定表存在
            for name_f in list(name_f_list):
                name_n = '%s%s' % (name_f, name)
                db_con = _return_db_con_(db, name_f)
                if z == 1:
                    # if db =='houma':
                    #     v1 = _select_get_his_value_houma(db_con, table, name_n, st, ed, startTime, endTime, '15T', vmax=1,minV=0.1)
                    # else:
                    v1 = _select_get_his_value_22(db_con, table, name_n, st, ed, startTime, endTime, '15T', vmax=1,
                                                  minV=0.1)
                else:
                    # if db =='houma':
                    #     v1 = _select_get_his_value_houma(db_con, table, name_n, st, ed, startTime, endTime, '15T', vmax=1,minV=0.1)
                    # else:
                    v1 = _select_get_his_value(db_con, table, name_n, st, ed, startTime, endTime, '15T', vmax=101)

                if v1:
                    value = v1['value']
                    timeall = v1['time']
                    if obj['time'] == []:
                        obj['time'] = timeall
                    if value != []:
                        obj_sys_list.append(value)
                db_con.close()
        if obj['time'] != []:
            for i in obj['time']:
                list_time.append(i[8:16])
        obj['time'] = list_time
        if obj_sys_list != []:
            if z == 1:
                if lang == 'en':
                    obj['data'].append(
                        {'name': 'Power factor', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                else:
                    obj['data'].append(
                        {'name': '功率因数', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
            if z == 2:
                if lang == 'en':
                    obj['data'].append(
                        {'name': 'System SOC(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                else:
                    obj['data'].append(
                        {'name': '系统SOC(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})

    def dongmu_sys_SOC_d(self, na, ed, obj, st, z, lang=None):
        '''系统东睦SOC,因数日数据计算'''
        dataP1, data2 = [], {}  # 有功,
        PCS_name = model_config.get('peizhi', na)  # name
        dm_table = HisDM('r_measure')
        obj['time'] = []
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []
                for ii in alldata:
                    if ii['device'] == PCS_:
                        value = ii[PCS_name]  #
                        if float(value) < 0:
                            d2.append('0')
                        else:
                            d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()
                # 取相同的间隔时间(采用补数的方法)
                if data12["time"]:
                    data22 = complete_data(data12, '15T')
                    list_time = []
                if data22['value']:
                    for d in data22['value']:
                        try:
                            if math.isnan(d) is True:
                                indx = data22['value'].index(d)
                                data22['value'][indx] = 0
                        except:
                            break
                    if data22:
                        for i in data22['time']:
                            list_time.append(i[8:16])
                    data2['time'] = list_time
                    obj['time'] = list_time if list_time else []
                    obj_sys_list.append(list(map(float, data22['value'] if data22['value'] else 0)))
            # value_=np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()
            # if math.isnan(value_) is True:
            #     value_ =[]
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor',
                                            'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                    else:
                        obj['data'].append(
                            {'name': '功率因数', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'System SOC(%)',
                                            'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})
                    else:
                        obj['data'].append(
                            {'name': '系统SOC(%)', 'value': np.round((np.array(obj_sys_list).mean(axis=0)), 3).tolist()})

        dongmu_session.close()

    def other_sys_soh(self, a, b, data, db, enTime, name_a, max, min, stTime, tables):
        '''系统其他电站SOH'''
        st = timeUtils.timeStrToTamp(stTime)  # 起始时间绝对秒
        ed = timeUtils.timeStrToTamp(enTime)  # 截止时间绝对秒
        list_ss = []
        for table in tables.to_list():
            for d in a:
                for d1 in b:
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, name_a)
                    try:
                        # def _select_get_his_value_day(db_con, table, name, st, ed, startTime, endTime, vmax=65):
                        v1 = _select_get_his_value_day(db_con, table, m, st, ed, stTime, enTime, vmax=100)  # 电池簇SOH
                        if v1['value'] != []:
                            data.extend(v1['value'])
                        else:
                            data.extend([99])
                    except:
                        data.extend([99])
                    db_con.close()
        list_ = []
        if data != []:
            for i in data:
                if i != 0:
                    list_.append(i)
            if list_ != []:
                list_ss.append({'min': min(list_)})
        return list_ss

    def dongmu_sys_soh(self, SOH_name, d, enTime, max, min, stTime):
        '''东睦系统soh计算'''
        st = timeUtils.timeStrToTamp(stTime)  # 起始时间绝对秒
        ed = timeUtils.timeStrToTamp(enTime)  # 截止时间绝对秒
        dm_table = HisDM('r_measure')
        list_ = []
        values = dongmu_session.query(dm_table.utime, dm_table.datainfo).filter(dm_table.time.between(st, ed)).all()
        if values:
            for v in values:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][0:3] == 'BMS':
                        value = float(b[SOH_name])  #
                        if not value:
                            d.append(100)
                        if 0 < value <= 100:
                            d.append(value)
                        else:
                            d.append(100)
        else:
            d.append(100)
        if d != []:
            list_.append({'min': min(d)})
        return list_

    def sys_dongmu_PF(self, PF_name, d, enTime, max, min, stTime):
        '''系统东睦因数计算（周，月，年，累计）'''
        st = timeUtils.timeStrToTamp(stTime)  # 起始时间绝对秒
        ed = timeUtils.timeStrToTamp(enTime)  # 截止时间绝对秒
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.utime, dm_table.datainfo).filter(dm_table.time.between(st, ed)).all()
        if values:
            for v in values:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][0:3] == 'PCS':
                        value = float(b[PF_name])  #
                        if not value:
                            d.append(0)
                        elif 0 < value <= 1:
                            d.append(value)
                        else:
                            d.append(0)
        else:
            d.append(0)
        list_ = []
        if d != []:
            list_.append({'max': str(max(d)), 'min': str(min(d))})
        return list_

    def dongmu_cu_v_t_j(self, cu_name, ed, list1, obj, st, lang=None):
        '''东睦电池簇温度极差，电压极差计算方式'''
        dm_table = HisDM('r_measure')
        obj = []
        values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        for f in range(7):
            d = []
            for v in values_mong:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][:4] == 'BMS%s' % (f + 1):
                        value = float(b[cu_name])  #
                        if 0 <= value <= 100:
                            d.append(value)
            if lang == 'en':
                obj.append({'pcs_name': 'PCS-%s' % (f + 1),
                            'cu': [{'cu_name': 'Battery cluster%s' % (f + 1), 'max': max(d) if d else 100}]})
            else:
                obj.append({'pcs_name': 'PCS-%s' % (f + 1),
                            'cu': [{'cu_name': '电池簇%s' % (f + 1), 'max': max(d) if d else 100}]})

        dongmu_session.close()

        return obj

    def dongmu_cu_day(self, name, ed, obj, st, lang=None):
        '''东睦簇日数据计算'''
        dataP1, data2 = [], {}  # 有功,
        cu_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata = [], []  # 时间，所有数据
        for i in values:
            time_1 = json.loads(i['datainfo'])['utime']
            timeall_.append(timeUtils.ssTtimes(time_1))
            value = json.loads(i['datainfo'])['body']
            for v in value:
                alldata.append(v)
        for e in range(dongmu_num):
            BMS_ = 'BMS%s' % (e + 1)
            d2 = []  # 有功
            for ii in alldata:
                if ii['device'] == BMS_:
                    value = ii[cu_name]  #
                    d2.append(value)
                    # 取相同的间隔时间(采用补数的方法)
            data12 = {}
            data12['time'] = timeall_
            data12['value'] = d2
            df = pd.DataFrame(data12)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data12["time"] = df["time"].tolist()
            data12["value"] = df["value"].tolist()
            if data12["time"]:
                data22 = complete_data(data12, '15T')
                list_time = []
                if data22:
                    for i in data22['time']:
                        list_time.append(i[8:16])
                data2['time'] = list_time
                # dataP1.append(data22['value'])
                obj['time'] = list_time

                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if lang == 'en':
                    obj['data'].append({'pcs_name': 'PCS-%s' % (e + 1),
                                        'cu': [{'cu_name': 'Battery cluster%s' % (e + 1), 'value': np.round(
                                            np.array(data22['value']).astype(float), 3).tolist()}]})
                else:
                    obj['data'].append(
                        {'pcs_name': 'PCS-%s' % (e + 1), 'cu': [{'cu_name': '电池簇%s' % (e + 1), 'value': np.round(
                            np.array(data22['value']).astype(float), 3).tolist()}]})
        dongmu_session.close()

    def other_PCS(self, db, y, endTime, start_Time, tables, lang=None):
        '''其他电站因数，温度'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        me = model_config.get('peizhi', conf_name[y])  # name后半部分
        name_n = []
        for name_f in name_f_list:
            n_ = '%s%s' % (name_f, me)
            name_n.append(n_)
        name_nStr = "','".join(name_n)
        result_obj = {}  # 先查询出各表的最大和最小
        for table in tables.to_list():
            n = 0
            for dict_db in all_datas[db]:  # 需要查询的数据库
                # b = name_f.split(".")[0][4:]
                if _tableIsExist(dict_db[0], dict_db[2], table):  # 确定表存在
                    sql = "select name,max(value)mm,min(value)mi from {0} where value<65 and " \
                          "FROM_UNIXTIME(dts_s) Between '{1}' and '{2}' and " \
                          "name in('{3}')group by name".format(table, start_Time, endTime, name_nStr)
                    conn = dict_db[0].raw_connection()  # 拿原生的连接
                    cursor = conn.cursor()
                    cursor.execute(sql)
                    result = cursor.fetchall()
                    cursor.close()
                    conn.close()
                    if result:  # 先查询出各表的最大和最小
                        for r in result:
                            ii = ('.'.join(r[0].split('.')[:-1])) + '.'
                            max_ = r[1]
                            min_ = r[2]
                            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == ii,
                                                                                      SdaPcsCu.station_name == db).first()
                            if not v_pcs_name:
                                logging.error("未找到对应的pcs_name")
                                continue
                            PCS_name_f = v_pcs_name[0]
                            if PCS_name_f in result_obj:
                                result_obj[PCS_name_f].append(max_)
                                result_obj[PCS_name_f].append(min_)
                            else:
                                result_obj[PCS_name_f] = []
                                result_obj[PCS_name_f].append(max_)
                                result_obj[PCS_name_f].append(min_)
                            n += 1
        return result_obj

    def data_day(self, db, n, tables, ed, endTime, obj, st, startTime, t, z=None, obj_sys=None, vmax=None, lang=None):
        '''系统，PCS其他电站日数据(调了小数表)(当日)'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        name = model_config.get('peizhi', conf_name[n])  # name后半部分
        obj_sys_list = []
        obj['time'] = []
        m = 0
        for name_f in list(name_f_list):
            name_n = '%s%s' % (name_f, name)
            db_con = _return_db_con_(db, name_f)
            if db == 'baodian':
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name_f+'s.',
                                                                          SdaPcsCu.station_name == db).first()
            else:
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name_f,
                                                                          SdaPcsCu.station_name == db).first()
            if not v_pcs_name:
                logging.error("未找到对应的pcs_name")
                continue
            PCS_name_f = v_pcs_name[0]
            try:
                v1 = _select_get_his_value(db_con, tables[0], name_n, st, ed, startTime, endTime, t, vmax)
                if v1:
                    value = v1['value']
                    list_time = []
                    if obj['time'] == []:
                        if v1['time']:
                            for i in v1['time']:
                                list_time.append(i[8:16])
                        obj['time'] = list_time
            except:
                value = []
            if obj_sys == '系统':
                if obj_sys_list != []:
                    if value != []:
                        obj_sys_list = np.array(obj_sys_list) + np.array(value)
                else:
                    if value != []:
                        obj_sys_list = value
            else:
                obj['data'].append({'name': PCS_name_f, 'value': np.round(value, 3).tolist()})
            m += 1
            db_con.close()
        if obj_sys_list != []:
            if z == 1:
                if lang == 'en':
                    obj['data'].append({'name': 'Active power(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                else:
                    obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
            if z == 2:
                if lang == 'en':
                    obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
                else:
                    obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})

    def data_day_ygzhen(self, db, n, tables, ed, endTime, obj, st, startTime, t, z=None, obj_sys=None, vmax=None,
                        lang=None):
        '''系统，PCS其他电站日数据(调了小数表)(当日)'''
        if db == 'zgtian':
            name_f_list = model_config.get('peizhi', 'zgtian_bms')  # name前半部分
        elif db == 'houma':
            name_f_list = model_config.get('peizhi', 'houma_bms')  # name前半部分
        elif db == 'datong':
            name_f_list = model_config.get('peizhi', 'datong_bms')  # name前半部分
        else:
            name_f_list = model_config.get('peizhi', 'ygzhen_bms')  # name前半部分
        # name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        name = model_config.get('peizhi', conf_name[n])  # name后半部分
        obj_sys_list = []
        obj['time'] = []
        # for dict_db in all_datas[db]:  # 需要查询的数据库
        #     if _tableIsExist(dict_db[0], dict_db[2], tables[0]):  # 确定表存在
        m = 0
        for name_f in list(name_f_list):
            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name_f,
                                                                      SdaPcsCu.station_name == db).first()
            if not v_pcs_name:
                logging.error("未找到对应的pcs_name")
                continue
            PCS_name_f = v_pcs_name[0]
            name_n = '%s%s' % (name_f, name)
            db_con = _return_db_con_(db, name_f)
            try:
                v1 = _select_get_his_value(db_con, tables[0], name_n, st, ed, startTime, endTime, t, vmax)
                if v1:
                    value = v1['value']
                    list_time = []
                    if obj['time'] == []:
                        if v1['time']:
                            for i in v1['time']:
                                list_time.append(i[8:16])
                        obj['time'] = list_time
            except:
                value = []

            if obj_sys == '系统':
                if obj_sys_list != []:
                    if value != []:
                        obj_sys_list = np.array(obj_sys_list) + np.array(value)
                else:
                    if value != []:
                        obj_sys_list = value
            else:
                obj['data'].append({'name': PCS_name_f, 'value': np.round(value, 3).tolist()})
                # obj['data'].append({'name': ('PCS-%s' % m), 'value':np.round(value,3).tolist()})
                m += 1
            db_con.close()
        if obj_sys_list != []:
            if z == 1:
                obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
            if z == 2:
                obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})

    def data_day_Pf(self, db, n, tables, ed, endTime, obj, st, startTime, t, obj_sys=None, vmax=None, lang=None):
        '''系统，PCS其他电站日数据(调了小数表)(当日)'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        name = model_config.get('peizhi', conf_name[n])  # name后半部分
        obj_sys_list = []
        obj['time'] = []
        m = 0
        for name_f in list(name_f_list):
            name_n = '%s%s' % (name_f, name)
            db_con = _return_db_con_(db, name_f)
            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name_f,
                                                                      SdaPcsCu.station_name == db).first()
            if not v_pcs_name:
                logging.error("未找到对应的pcs_name")
                continue
            PCS_name_f = v_pcs_name[0]
            try:
                v1 = _select_get_his_value_22(db_con, tables[0], name_n, st, ed, startTime, endTime, '15T', vmax=1,
                                              minV=0.1)
                if v1:
                    value = v1['value']
                    list_time = []
                    if obj['time'] == []:
                        if v1['time']:
                            for i in v1['time']:
                                list_time.append(i[8:16])
                        obj['time'] = list_time
            except:
                value = []
            if obj_sys == '系统':
                if obj_sys_list != []:
                    if value != []:
                        obj_sys_list = np.array(obj_sys_list) + np.array(value)
                else:
                    if value != []:
                        obj_sys_list = value
            else:
                obj['data'].append({'name': PCS_name_f, 'value': np.round(value, 3).tolist()})
                m += 1
            db_con.close()
        if obj_sys_list != []:
            if lang == 'en':
                obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
            else:
                obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})
            # if z==2:
            #     obj['data'].append({'name': '功率因数', 'value':np.round(obj_sys_list,3).tolist()})

    def data_2_4_5(self, db, n, tables, ed, endTime, obj, st, startTime, t, report_type=None, vmax=None, lang=None):
        '''PCS其他电站电压电流'''
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        name = model_config.get('peizhi', conf_name[n])  # name后半部分
        obj_sys_list = []
        obj['time'] = []
        dict_a = {}  # pcs为key的字典
        list_time = []
        for table in tables:  # 需要查询的表
            m = 0
            for name_f in list(name_f_list):
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name_f,
                                                                          SdaPcsCu.station_name == db).first()
                if not v_pcs_name:
                    logging.error("未找到对应的pcs_name")
                    continue
                PCS_name_f = v_pcs_name[0]
                name_n = '%s%s' % (name_f, name)
                db_con = _return_db_con_(db, name_f)
                v1 = {'time': [], 'value': []}
                try:
                    if report_type == 4 or report_type == 5:
                        if db == 'houma' or db == 'datong':  # 不往前补值
                            v1 = _select_get_his_value_V_A_houma(db_con, table, name_n, st, ed, startTime, endTime, t,
                                                                 vmax)
                        else:
                            v1 = _select_get_his_value_V_A(db_con, table, name_n, st, ed, startTime, endTime, t, vmax)
                    else:
                        v1 = _select_get_his_value_V_A(db_con, table, name_n, st, ed, startTime, endTime, t, vmax)

                    db_con.close()
                except:
                    v1 = {'time': [], 'value': []}
                if PCS_name_f not in dict_a.keys():
                    dict_a[PCS_name_f] = {'time': v1['time'], 'value': v1['value']}
                else:
                    dict_a[PCS_name_f]['time'].extend(v1['time'])
                    dict_a[PCS_name_f]['value'].extend(v1['value'])
                m += 1
        for data in dict_a.keys():
            df = pd.DataFrame(dict_a[data])
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            dict_a[data]["time"] = df["time"].tolist()
            dict_a[data]["value"] = df["value"].tolist()
            if dict_a[data]["value"]:
                dict_a[data]['value'][0] = 1

            if dict_a[data]["time"]:
                dict_v = complete_data(dict_a[data], t)
                if list_time == []:
                    if dict_v['time']:
                        for i in dict_v['time']:
                            if report_type == 2:
                                list_time.append(i[8:16])
                            if report_type == 4:
                                list_time.append(i[5:13])
                            elif report_type == 5:
                                list_time.append(i[0:13])
                obj['data'].append({'name': data, 'value': np.round(dict_v['value'], 3).tolist()})
        obj['time'] = list_time

    def data_day_dongmu(self, ed=None, obj=None, st=None, name=None, t=None, z=None, obj_sys=None, vmax=None,
                        lang=None):
        '''系统，PCS东睦日数据'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    # index = alldata.index(ii)
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        if value < 0:
                            d2.append(0)
                        elif value > 1:
                            d2.append(1)
                        else:
                            d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                list_time.append(i[8:16])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if obj_sys == '系统':
                    if obj_sys_list != []:
                        obj_sys_list = np.array(obj_sys_list).astype(float) + np.array(data22['value']).astype(float)
                    else:
                        obj_sys_list = data22['value']
                else:
                    obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                        'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Active power(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})
        dongmu_session.close()

    def data_day_dongmu_yougong(self, ed=None, obj=None, st=None, name=None, t=None, z=None, obj_sys=None, vmax=None,
                                lang=None):
        '''系统，PCS东睦日数据'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    # index = alldata.index(ii)
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                # data12['time'].insert(0,'2023-06-17 00:00:00')
                # data12['time'].append('2023-06-19 00:00:00')
                # data12['value'].insert(0,data12['value'][0])
                # data12['value'].append(data12['value'][-1])
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                list_time.append(i[8:16])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                if obj_sys == '系统':
                    if obj_sys_list != []:
                        obj_sys_list = np.array(obj_sys_list).astype(float) + np.array(data22['value']).astype(float)
                    else:
                        obj_sys_list = data22['value']
                else:
                    obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                        'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
            if obj_sys_list != []:
                if z == 1:
                    if lang == 'en':
                        obj['data'].append({'name': 'Active power(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '有功功率(kw)', 'value': np.round(obj_sys_list, 3).tolist()})
                if z == 2:
                    if lang == 'en':
                        obj['data'].append({'name': 'Power factor', 'value': np.round(obj_sys_list, 3).tolist()})
                    else:
                        obj['data'].append({'name': '功率因数', 'value': np.round(obj_sys_list, 3).tolist()})
        dongmu_session.close()

    def data_day_dongmu_A_V(self, ed=None, obj=None, st=None, name=None, t=None, report_type=None):
        '''PCS东睦电流电压（4,5）'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2
                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                if report_type == 4:
                                    list_time.append(i[5:13])
                                elif report_type == 5:
                                    list_time.append(i[0:13])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break
                obj['data'].append({'name': ('PCS-%s' % (e + 1)),
                                    'value': np.round(np.array(data22['value']).astype(float), 3).tolist()})
        dongmu_session.close()


def _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=None):
    list1 = []
    if db == 'dongmu':
        for i in range(7):
            redisdata = real_data('measure', 'dongmu', 'db')
            time_real = redisdata['utime']
            now_time = timeUtils.getNewTimeStr()
            now_time_ = timeUtils.timeStrToTamp(now_time)
            time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
            if time_ss > 125:
                list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': 0, 'chag': 0})
            else:
                for ii in redisdata['body']:
                    if ii['device'][:4] == ('PCS%s' % (i + 1)):
                        disg_ = ii[name_disg]  # PCS日放电
                        chag_ = ii[name_chag]  # PCS日充电
                        list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': ('%.3f' % float(disg_)),
                                      'chag': ('%.3f' % float(chag_))})

    else:
        for name in names:
            if db == 'baodian':
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == (name+'s.'),
                                                                          SdaPcsCu.station_name == db).first()
            else:
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                          SdaPcsCu.station_name == db).first()
            if not v_pcs_name:
                logging.error("未找到对应的pcs_name")
                continue
            PCS_name_f = v_pcs_name[0]
            o = real_data('measure', name + name_disg, 'db')
            disg_ = o['value']  # PCS日放电
            o = real_data('measure', name + name_chag, 'db')
            chag_ = o['value']  # PCS日充电
            list1.append({'name': PCS_name_f, 'disg': ('%.3f' % float(disg_)), 'chag': ('%.3f' % float(chag_))})
    return list1


def _select_get_his_value_n_y(db_con, table, name, st, ed, vmax=None, minV=0.1):
    '''获取周，月，年，累计历史数据不补值'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    return data


def _select_get_his_value_day_(db_con, table, name, st, ed, t=None, vmax=None):
    '''获取日历史数据不调小时表'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value < vmax).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询月表
    if values:
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

            data['value'].append(data['value'][-1])
            df = pd.DataFrame(data)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data["time"] = df["time"].tolist()
            data["value"] = df["value"].tolist()
    return complete_data(data, t) if data["time"] else {}


def _select_get_his_value_22(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65, minV=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])
    l = timeUtils.getBeforeHouseStr()[:14] + "00:00"  # 获取上一小时时间 格式 换算整点值
    t1 = timeUtils.timeSeconds(l, endTime)  # 计算截止时间和前一小时时间的差值

    if t1 > 0:  # 截止时间大
        # 查询上一小时表里的数据
        try:
            HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
            values = db_con.query(HisTable_l.value, HisTable_l.dts_s).filter(HisTable_l.name == name,
                                                                             HisTable_l.dts_s.between(st, ed),
                                                                             HisTable_l.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])
    n = timeUtils.getNewTimeStr()[:14] + "00:00"  # 取整点值
    t2 = timeUtils.timeSeconds(n, endTime)  # 计算截止时间和当前小时的差值

    if t2 > 0:  # 截止时间大于当前时间
        # 查询当前小时表里的数据
        HisTable_n = HisACDMS('r_measure%s' % timeUtils.getNowHouse())  #
        try:
            values = db_con.query(HisTable_n.value, HisTable_n.dts_s).filter(HisTable_n.name == name,
                                                                             HisTable_n.dts_s.between(st, ed),
                                                                             HisTable_n.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_n.dts_s.asc()).all()  # 当前小时表数据
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

    tl = timeUtils.timeSeconds(l, startTime)  # 计算起始时间和前一小时时间的差值

    if tl > 0:  # 起始时间大
        try:
            value = db_con.query(HisTable_l.value).filter(HisTable_l.name == name, HisTable_l.dts_s <= st,
                                                          HisTable_l.value.between(minV, vmax)).order_by(
                HisTable_l.dts_s.desc()).first()  # 查询前一小时
        except:
            value = []
    else:
        nt = data['time'][0] if data['time'] else startTime
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name,
                                                      HisTable_m.dts_s <= timeUtils.timeStrToTamp(nt[:19]),
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月

    if not value and not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()

    return complete_data(data, jiange)


def _select_get_his_value_houma(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65, minV=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    if not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()

    return complete_data(data, jiange)


def _select_get_his_value_222(db_con, table, name, st, ed, startTime, endTime, vmax=65, minV=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    l = timeUtils.getBeforeHouseStr()[:14] + "00:00"  # 获取上一小时时间 格式 换算整点值
    t1 = timeUtils.timeSeconds(l, endTime)  # 计算截止时间和前一小时时间的差值

    if t1 > 0:  # 截止时间大
        # 查询上一小时表里的数据
        HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
        try:
            values = db_con.query(HisTable_l.value, HisTable_l.dts_s).filter(HisTable_l.name == name,
                                                                             HisTable_l.dts_s.between(st, ed),
                                                                             HisTable_l.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])
    n = timeUtils.getNewTimeStr()[:14] + "00:00"  # 取整点值
    t2 = timeUtils.timeSeconds(n, endTime)  # 计算截止时间和当前小时的差值

    if t2 > 0:  # 截止时间大于当前时间
        # 查询当前小时表里的数据
        HisTable_n = HisACDMS('r_measure%s' % timeUtils.getNowHouse())  #
        try:
            values = db_con.query(HisTable_n.value, HisTable_n.dts_s).filter(HisTable_n.name == name,
                                                                             HisTable_n.dts_s.between(st, ed),
                                                                             HisTable_n.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_n.dts_s.asc()).all()  # 当前小时表数据
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

    tl = timeUtils.timeSeconds(l, startTime)  # 计算起始时间和前一小时时间的差值

    if tl > 0:  # 起始时间大
        try:
            value = db_con.query(HisTable_l.value).filter(HisTable_l.name == name, HisTable_l.dts_s <= st,
                                                          HisTable_l.value.between(minV, vmax)).order_by(
                HisTable_l.dts_s.desc()).first()  # 查询前一小时
        except:
            value = []

    else:
        nt = data['time'][0] if data['time'] else startTime
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name,
                                                      HisTable_m.dts_s <= timeUtils.timeStrToTamp(nt[:19]),
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月

    if not value and not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()

    return data


def _select_get_his_value_V_A(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65):
    '''获取历史数据(pcs电压电流245)'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value < vmax).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])
    return data


def _select_get_his_value_V_A_houma(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65):
    '''获取历史数据(pcs电压电流245)'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value < vmax).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    if not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    return data


def _select_get_his_value_day(db_con, table, name, st, ed, startTime, endTime, vmax=65):
    '''获取历史数据(不补值)'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value < vmax).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    l = timeUtils.getBeforeHouseStr()[:14] + "00:00"  # 获取上一小时时间 格式 换算整点值
    t1 = timeUtils.timeSeconds(l, endTime)  # 计算截止时间和前一小时时间的差值

    if t1 > 0:  # 截止时间大
        # 查询上一小时表里的数据
        HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
        try:
            values = db_con.query(HisTable_l.value, HisTable_l.dts_s).filter(HisTable_l.name == name,
                                                                             HisTable_l.dts_s.between(st, ed),
                                                                             HisTable_l.value < vmax).order_by(
                HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])
    n = timeUtils.getNewTimeStr()[:14] + "00:00"  # 取整点值
    t2 = timeUtils.timeSeconds(n, endTime)  # 计算截止时间和当前小时的差值

    if t2 > 0:  # 截止时间大于当前时间
        # 查询当前小时表里的数据
        HisTable_n = HisACDMS('r_measure%s' % timeUtils.getNowHouse())  #
        try:
            values = db_con.query(HisTable_n.value, HisTable_n.dts_s).filter(HisTable_n.name == name,
                                                                             HisTable_n.dts_s.between(st, ed),
                                                                             HisTable_n.value < vmax).order_by(
                HisTable_n.dts_s.asc()).all()  # 当前小时表数据
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

    tl = timeUtils.timeSeconds(l, startTime)  # 计算起始时间和前一小时时间的差值

    if tl > 0:  # 起始时间大
        try:
            value = db_con.query(HisTable_l.value).filter(HisTable_l.name == name, HisTable_l.dts_s <= st,
                                                          HisTable_l.value < vmax).order_by(
                HisTable_l.dts_s.desc()).first()  # 查询前一小时
        except:
            value = []

    else:
        nt = data['time'][0] if data['time'] else startTime
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name,
                                                      HisTable_m.dts_s <= timeUtils.timeStrToTamp(nt[:19]),
                                                      HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月

    if not value and not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()
    return data