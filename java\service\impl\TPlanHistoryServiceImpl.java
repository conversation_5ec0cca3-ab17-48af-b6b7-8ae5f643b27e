package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryCreateDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryQueryDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryUpdateDTO;
import com.robestec.analysis.entity.TPlanHistory;
import com.robestec.analysis.mapper.TPlanHistoryMapper;
import com.robestec.analysis.service.TPlanHistoryService;
import com.robestec.analysis.vo.TPlanHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 计划历史记录服务实现类
 */
@Slf4j
@Service
public class TPlanHistoryServiceImpl extends SuperServiceImpl<TPlanHistoryMapper, TPlanHistory>
        implements TPlanHistoryService {

    @Override
    public PageResult<TPlanHistoryVO> queryTPlanHistory(TPlanHistoryQueryDTO queryDTO) {
        LambdaQueryWrapper<TPlanHistory> wrapper = new LambdaQueryWrapper<TPlanHistory>()
                .like(StringUtils.hasText(queryDTO.getName()), TPlanHistory::getName, queryDTO.getName())
                .eq(queryDTO.getStatus() != null, TPlanHistory::getStatus, queryDTO.getStatus())
                .eq(queryDTO.getPlanType() != null, TPlanHistory::getPlanType, queryDTO.getPlanType())
                .eq(queryDTO.getIsFollow() != null, TPlanHistory::getIsFollow, queryDTO.getIsFollow())
                .eq(queryDTO.getUserId() != null, TPlanHistory::getUserId, queryDTO.getUserId())
                .like(StringUtils.hasText(queryDTO.getUserName()), TPlanHistory::getUserName, queryDTO.getUserName())
                .like(StringUtils.hasText(queryDTO.getTypeName()), TPlanHistory::getTypeName, queryDTO.getTypeName())
                .like(StringUtils.hasText(queryDTO.getStation()), TPlanHistory::getStation, queryDTO.getStation())
                .eq(queryDTO.getIsUse() != null, TPlanHistory::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TPlanHistory::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TPlanHistory::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(TPlanHistory::getCreateTime);

        Page<TPlanHistory> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TPlanHistory> result = this.page(page, wrapper);

        List<TPlanHistoryVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPlanHistoryVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTPlanHistory(TPlanHistoryCreateDTO createDTO) {
        // 检查同一用户下计划名称是否重复
        long count = this.count(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getName, createDTO.getName())
                .eq(TPlanHistory::getUserId, createDTO.getUserId()));
        if (count > 0) {
            throw new RuntimeException("计划名称已存在");
        }

        TPlanHistory entity = BeanUtil.copyProperties(createDTO, TPlanHistory.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTPlanHistory(TPlanHistoryUpdateDTO updateDTO) {
        TPlanHistory entity = BeanUtil.copyProperties(updateDTO, TPlanHistory.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTPlanHistory(Long id) {
        this.removeById(id);
    }

    @Override
    public TPlanHistoryVO getTPlanHistory(Long id) {
        TPlanHistory entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTPlanHistoryList(List<TPlanHistoryCreateDTO> createDTOList) {
        List<TPlanHistory> entityList = BeanUtil.copyToList(createDTOList, TPlanHistory.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TPlanHistoryVO> getTPlanHistoryByUserId(Long userId) {
        List<TPlanHistory> entityList = this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getUserId, userId)
                .orderByDesc(TPlanHistory::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPlanHistoryVO> getTPlanHistoryByStatus(Integer status) {
        List<TPlanHistory> entityList = this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getStatus, status)
                .orderByDesc(TPlanHistory::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPlanHistoryVO> getTPlanHistoryByStation(String station) {
        List<TPlanHistory> entityList = this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getStation, station)
                .orderByDesc(TPlanHistory::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByUserId(Long userId) {
        return this.count(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getUserId, userId));
    }

    @Override
    public Long countByStatus(Integer status) {
        return this.count(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getStatus, status));
    }

    /**
     * 转换为VO对象
     */
    private TPlanHistoryVO convertToVO(TPlanHistory entity) {
        if (entity == null) {
            return null;
        }
        TPlanHistoryVO vo = BeanUtil.copyProperties(entity, TPlanHistoryVO.class);
        vo.setStatusName(getStatusName(entity.getStatus()));
        vo.setPlanTypeName(getPlanTypeName(entity.getPlanType()));
        vo.setIsFollowName(getIsFollowName(entity.getIsFollow()));
        return vo;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1: return "已保存";
            case 2: return "已下发";
            case 3: return "执行中";
            case 4: return "已完成";
            case 5: return "下发失败";
            case 6: return "已停止";
            default: return "未知";
        }
    }

    /**
     * 获取计划类型名称
     */
    private String getPlanTypeName(Integer planType) {
        if (planType == null) {
            return "";
        }
        switch (planType) {
            case 1: return "自定义";
            case 2: return "周期性";
            case 3: return "节假日";
            default: return "未知";
        }
    }

    /**
     * 获取是否跟随名称
     */
    private String getIsFollowName(Integer isFollow) {
        if (isFollow == null) {
            return "";
        }
        return isFollow == 1 ? "是" : "否";
    }

    // ==================== 从Mapper迁移的方法 ====================

    /**
     * 根据ID列表软删除记录
     * 对应Python中的批量删除逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean softDeleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return this.update(Wrappers.<TPlanHistory>lambdaUpdate()
                .in(TPlanHistory::getId, ids)
                .set(TPlanHistory::getIsUse, 0));
    }

    /**
     * 根据计划ID查询历史记录
     */
    public List<TPlanHistory> selectByPlanId(Long planId) {
        return this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getPlanId, planId)
                .eq(TPlanHistory::getIsUse, 1)
                .orderByDesc(TPlanHistory::getCreateTime));
    }

    /**
     * 根据用户ID和状态查询记录
     */
    public List<TPlanHistory> selectByUserIdAndStatus(Long userId, Integer status) {
        return this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getUserId, userId)
                .eq(TPlanHistory::getIsUse, 1)
                .eq(status != null, TPlanHistory::getStatus, status)
                .orderByDesc(TPlanHistory::getCreateTime));
    }

    /**
     * 根据电站名称查询记录
     */
    public List<TPlanHistory> selectByStation(String station) {
        return this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getIsUse, 1)
                .like(StringUtils.hasText(station), TPlanHistory::getStation, station)
                .orderByDesc(TPlanHistory::getCreateTime));
    }

    /**
     * 更新计划状态
     * 对应Python中powerPlanStop方法的状态更新逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        return this.update(Wrappers.<TPlanHistory>lambdaUpdate()
                .eq(TPlanHistory::getId, id)
                .set(TPlanHistory::getStatus, status));
    }

    /**
     * 根据计划类型查询记录
     */
    public List<TPlanHistory> selectByPlanType(Integer planType) {
        return this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getPlanType, planType)
                .eq(TPlanHistory::getIsUse, 1)
                .orderByDesc(TPlanHistory::getCreateTime));
    }

    /**
     * 根据时间范围查询记录
     */
    public List<TPlanHistory> selectByTimeRange(String startTime, String endTime) {
        return this.list(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getIsUse, 1)
                .ge(StringUtils.hasText(startTime), TPlanHistory::getStartTime, startTime)
                .le(StringUtils.hasText(endTime), TPlanHistory::getEndTime, endTime)
                .orderByDesc(TPlanHistory::getCreateTime));
    }

    /**
     * 统计用户的计划数量
     */
    public int countPlansByUserId(Long userId) {
        return Math.toIntExact(this.count(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getUserId, userId)
                .eq(TPlanHistory::getIsUse, 1)));
    }

    /**
     * 根据状态统计计划数量
     */
    public int countPlansByStatus(Integer status) {
        return Math.toIntExact(this.count(Wrappers.<TPlanHistory>lambdaQuery()
                .eq(TPlanHistory::getStatus, status)
                .eq(TPlanHistory::getIsUse, 1)));
    }
}
