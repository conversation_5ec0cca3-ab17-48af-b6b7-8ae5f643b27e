#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/6/25 上午9:35
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import os
import uuid
import re
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session, DEBUG
import logging, json
from Application.Models.WorkOrder.sale_work_order_model import (RoleWorkerOrder, SaleWorkerOrderRunType,
                                                                SaleWorkerOrderProject, SaleWorkerOrderType,
                                                                SaleWorkerOrderBase, SaleWorkerOrderStep,
                                                                SaleWorkerOrderSolve, SaleWorkerOrderCopyUser)
from Application.Models.User.user import User
from datetime import datetime
from Tools.Utils.mimio_tool import upload_file, MinioTool
from sqlalchemy import func, distinct, or_
from Tools.Utils.send_mail import sendMail_
from itertools import product
import pandas as pd
import io


class SaleWorkerOrderHanddleIntetface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        try:
            session = self.getOrNewSession()
            user_id = session.user['id']
            user_role_id = session.user['user_role_id']
            if kt == 'GetWorkerOrderRole':  # 获取用户售后工单的权限
                role_enbale_lables = []
                # if user_role_id == 1:  # 超级管理员可以拥有所有权限
                #     role_data = user_session.query(SaleWorkerOrderRunType).filter(SaleWorkerOrderRunType.is_use == 1,
                #                                                                   SaleWorkerOrderRunType.type == 1).all()
                #     for role_lable in role_data:
                #         if role_lable.lable:
                #             role_enbale_lables.append(role_lable.lable)
                #     return self.returnTypeSuc({"role_enable_lables": role_enbale_lables})
                result = user_session.query(SaleWorkerOrderRunType). \
                    join(RoleWorkerOrder, SaleWorkerOrderRunType.id == RoleWorkerOrder.run_type). \
                    filter(RoleWorkerOrder.user_id == user_id, SaleWorkerOrderRunType.is_use == 1,
                           SaleWorkerOrderRunType.type == 1).all()
                for run_type in result:
                    if run_type.lable:
                        role_enbale_lables.append(run_type.lable)
                return self.returnTypeSuc({"role_enable_lables": role_enbale_lables})
            elif kt == 'GetProjectList':  # 获取项目列表
                data = []
                name = self.get_argument('name', None)
                filter = [SaleWorkerOrderProject.is_use == 1]
                if name:
                    filter.append(SaleWorkerOrderProject.name.like('%' + name + '%'))

                res = user_session.query(SaleWorkerOrderProject).filter(*filter).order_by(
                    SaleWorkerOrderProject.id.desc()).all()
                if res:
                    for pag in res:
                        data.append({
                            "id": pag.id,
                            "name": pag.name,
                            "project_type": pag.project_type,
                            "device_type": pag.device_type,
                            "product_type": pag.product_type,
                            "user": pag.user,
                            "phone": pag.phone,
                            "address": pag.address
                        })
                return self.returnTypeSuc(data)
            elif kt == 'GetAllTypeList':  # 获取所有类型列表和任务重要度列表 type=1任务类型, type=3任务重要度
                type = self.get_argument('type', '1')
                try:
                    type = int(type)
                except ValueError:
                    return self.customError('参数类型错误')
                if type == 1:  # 获取任务类型列表
                    root_nodes = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.type == 1,
                                                                                SaleWorkerOrderType.is_use == 1).all()
                    type_list = []
                    for root_node in root_nodes:
                        children = []
                        if root_node.children:
                            children = [{'id': item.id, 'name': item.name} for item in root_node.children
                                        if item.is_use == 1]
                        node = {
                            'id': root_node.id,
                            'name': root_node.name,
                            'children': children
                        }
                        type_list.append(node)
                elif type == 3:  # 获取任务重要度列表
                    root_nodes = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.type == 3,
                                                                                SaleWorkerOrderType.is_use == 1).all()
                    type_list = [{'id': item.id, 'name': item.name} for item in root_nodes]
                else:
                    return self.customError('参数类型传参错误')
                return self.returnTypeSuc(type_list)
            elif kt == 'GetWorkerOrderDetail':  # 获取工单详情
                woker_id = self.get_argument('id', None)
                if not woker_id:
                    return self.customError('参数不完整')
                res = user_session.query(SaleWorkerOrderBase).filter(SaleWorkerOrderBase.id == woker_id).first()
                if not res:
                    return self.customError('工单不存在')
                # 判断当前用户是否是此工单参与人
                judge_status = self.judge_user_participation(woker_id, user_id, res.copy_user)
                if not judge_status:
                    return self.customError('非当前任务参与人，无法查看详情！')
                base_info = {
                    "working_no": res.working_no,
                    "project_id": res.project_id,
                    "project_name": res.project.name,
                    "project_user": res.project.user,
                    "project_phone": res.project.phone,
                    "project_address": res.project.address,
                    "project_type": res.project.project_type,
                    "device_type": res.project.device_type,
                    "product_type": res.project.product_type,
                    "task_level": res.task_level,
                    "task_level_name": res.task_level_d.name,
                    "order_type_id": res.order_type_id,
                    "order_type_name": res.order_type_p.name,
                    "order_type_child_id": res.order_type_child_id,
                    "order_type_child_name": res.order_type_c.name if res.order_type_child_id else '',
                    "run_type": res.run_type,
                    "run_type_name": res.run_type_d.name,
                    "problem_descr": res.problem_descr,
                    "plan_finish_time": res.plan_finish_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "creat_time": res.creat_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "real_finish_time": res.real_finish_time.strftime('%Y-%m-%d %H:%M:%S') if res.real_finish_time else '',
                    "files": json.loads(res.files) if res.files else [],
                    "remark": res.remark,
                    "copy_user": json.loads(res.copy_user) if res.copy_user else []
                }

                # 获取当前应该执行的step_id
                current_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.working_id == woker_id, SaleWorkerOrderStep.real_user.is_(None),
                    SaleWorkerOrderStep.run_type == res.run_type).order_by(SaleWorkerOrderStep.creat_time.desc(), SaleWorkerOrderStep.id.asc()).first()
                current_step_id = 0
                if current_step:
                    current_step_id = current_step.id
                    base_info['current_step_id'] = current_step.id
                else:
                    # 查看是否需要修改才会出现这种情况
                    if res.run_type == self.get_run_type_by_name("修改"):
                        up_step = user_session.query(SaleWorkerOrderStep).filter(
                            SaleWorkerOrderStep.working_id == woker_id, SaleWorkerOrderStep.run_type == res.run_type).first()
                        if up_step:
                            base_info['current_step_id'] = up_step.id
                            current_step_id = up_step.id
                        else:
                            base_info['current_step_id'] = 0
                            current_step_id = 0

                # 查询步骤中节点为审核时的第一条记录，设置为可以添加抄送人
                step_8_id = self.get_run_type_by_name("审核")
                # step_8_data = user_session.query(SaleWorkerOrderStep).filter(
                #     SaleWorkerOrderStep.working_id == woker_id,
                #     SaleWorkerOrderStep.run_type == step_8_id).order_by(
                #     SaleWorkerOrderStep.creat_time.desc(), SaleWorkerOrderStep.id.asc()).first()
                step_list = []
                step_4_id = self.get_run_type_by_name("接单")
                # worker_step_data = user_session.query(SaleWorkerOrderStep).filter(
                #     SaleWorkerOrderStep.working_id == woker_id).order_by(
                #     SaleWorkerOrderStep.run_type.asc(), SaleWorkerOrderStep.id.asc()).first()
                # print(worker_step_data)
                step_7_id = self.get_run_type_by_name("执行")
                if res.working_step:
                    for i in res.working_step:
                        if not i.real_user and i.run_type != res.run_type:
                            # 如果没有实际执行人且当前步骤不是当前状态，则不展示
                            continue
                        if not i.real_user and i.run_type == res.run_type and i.id != current_step_id:
                            continue
                        if i.real_user:
                            performer = user_session.query(User).filter(User.id == i.real_user).first()
                        else:
                            performer = user_session.query(User).filter(User.id == i.plan_user).first()
                        # 驳回 新增一条修改记录，如果是撤回则更改新增里的状态且进度信息不展示预审记录，修改完成后更新状态为新增，
                        yushen_log = {}
                        if i.run_type == self.get_run_type_by_name("预审") and i.check_flag == 1:
                            # 查出通过后产生的记录
                            tg_info_list = user_session.query(SaleWorkerOrderStep).filter(
                                SaleWorkerOrderStep.working_id == i.working_id,
                                SaleWorkerOrderStep.platform_id == i.id).all()
                            jd_step_id = self.get_run_type_by_name("接单")
                            # lpsc_step_id = self.get_run_type_by_name("两票文件上传")
                            lpsh_step_id = self.get_run_type_by_name("两票审核")
                            sh_step_id = self.get_run_type_by_name("审核")
                            two_ticket_review_users = []
                            execute_check_user = []
                            for tg_info in tg_info_list:
                                if tg_info.run_type == jd_step_id:
                                    yushen_log['taking_order_user'] = tg_info.plan_user,
                                elif tg_info.run_type == lpsh_step_id:
                                    two_ticket_review_users.append(tg_info.plan_user)
                                elif tg_info.run_type == sh_step_id:
                                    execute_check_user.append(tg_info.plan_user)
                                else:
                                    continue
                            yushen_log['two_ticket_review_users'] = two_ticket_review_users
                            yushen_log['execute_check_user'] = execute_check_user
                        step_info = {
                            "id": i.id,
                            "working_no": i.working_no,
                            "working_id": i.working_id,
                            "create_user_id": i.create_user_id,
                            "performer": performer.name,
                            "run_type": i.run_type,
                            "run_type_name": i.run_type_r.name,
                            "creat_time": i.creat_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "implement_time": i.implement_time.strftime(
                                '%Y-%m-%d %H:%M:%S') if i.implement_time else '',
                            "check_flag": i.check_flag,
                            "care_flag": i.care_flag,
                            "remark": i.remark,
                            "content": i.content,
                            "solve_id": i.solve_id,
                        }

                        if i.solve_id:
                            solve_info = user_session.query(SaleWorkerOrderSolve).filter(
                                SaleWorkerOrderSolve.id == i.solve_id).first()
                            step_info['slove_name'] = solve_info.name
                        if i.run_type == self.get_run_type_by_name("预审") and i.check_flag == 1:
                            step_info['yushen_log'] = yushen_log
                        if i.run_type == step_4_id and i.care_flag == 1:
                            zp_info = user_session.query(SaleWorkerOrderStep).filter(
                                SaleWorkerOrderStep.working_id == i.working_id,
                                SaleWorkerOrderStep.run_type == step_4_id,
                                SaleWorkerOrderStep.id > i.id
                            ).order_by(SaleWorkerOrderStep.id.asc()).first()
                            if zp_info:
                                step_info['care_flag_user_id'] = zp_info.plan_user
                                step_info['care_flag_user_name'] = user_session.query(User).filter(
                                    User.id == zp_info.plan_user).first().name
                        if i.run_type == step_7_id:
                            step_info['real_finish_time'] = base_info['real_finish_time']
                        # if i.run_type == self.get_run_type_by_name("两票文件上传") and i.real_user is not None:
                        #     step_info['imgs'] = json.loads(i.imgs) if i.imgs else []
                        #     step_info['files'] = json.loads(i.files) if i.files else []
                        # if i.run_type == self.get_run_type_by_name("执行") and i.real_user is not None:
                        #     step_info['files'] = json.loads(i.files) if i.files else []
                        # if i.run_type == self.get_run_type_by_name("审核") and i.real_user is not None:
                        #     step_info['files'] = json.loads(i.files) if i.files else []
                        step_info['imgs'] = json.loads(i.imgs) if i.imgs else []
                        step_info['files'] = json.loads(i.files) if i.files else []
                        if i.run_type == step_8_id:
                            if user_id == 6 and user_id == i.plan_user:  # 只有当前用户登录的是吴晗账号才显示抄送人
                                step_info['is_add_copy_user'] = 1  # 可以添加抄送人
                            else:
                                step_info['is_add_copy_user'] = 0  # 不可以添加
                        if i.run_type == self.get_run_type_by_name("两票审核"):
                            s_file_two_tickets = user_session.query(SaleWorkerOrderStep).filter(
                                SaleWorkerOrderStep.working_id == i.working_id,
                                SaleWorkerOrderStep.run_type == self.get_run_type_by_name("两票文件上传"),
                                SaleWorkerOrderStep.real_user.isnot(None),
                                SaleWorkerOrderStep.id < i.id
                            ).order_by(SaleWorkerOrderStep.id.desc()).first()
                            if s_file_two_tickets:
                                step_info['two_ticket_id'] = s_file_two_tickets.id
                            else:
                                step_info['two_ticket_id'] = 0
                        if i.run_type == self.get_run_type_by_name("审核"):
                            s_file_to_check = user_session.query(SaleWorkerOrderStep).filter(
                                SaleWorkerOrderStep.working_id == i.working_id,
                                SaleWorkerOrderStep.run_type == self.get_run_type_by_name("执行"),
                                SaleWorkerOrderStep.real_user.isnot(None),
                                SaleWorkerOrderStep.id < i.id
                            ).order_by(SaleWorkerOrderStep.id.desc()).first()
                            if s_file_to_check:
                                step_info['to_check_id'] = s_file_to_check.id
                            else:
                                step_info['to_check_id'] = 0

                        step_list.append(step_info)
                res = {
                    "base_info": base_info,
                    "step_list": step_list
                }
                return self.returnTypeSuc(res)
            elif kt == 'GetWorkerOrderList':  # 售后任务记录清单
                data = []
                working_no = self.get_argument('working_no', None)  # 工单编号
                project_ids = self.get_argument('project_ids', None)  # 项目id集合
                order_type_ids = self.get_argument('order_type_ids', None)  # 任务类型id集合
                creat_start_time = self.get_argument('creat_start_time', None)  # 创建开始时间
                creat_end_time = self.get_argument('creat_end_time', None)  # 创建截止时间
                plan_start_time = self.get_argument('plan_start_time', None)  # 要求完成开始时间
                plan_end_time = self.get_argument('plan_end_time', None)  # 要求完成截止时间
                run_type_ids = self.get_argument('run_type_ids', None)  # 处理状态id集合
                project_type = self.get_argument('project_type', None)  # 项目类型
                product_type = self.get_argument('product_type', None)  # 产品类型
                task_level = self.get_argument('task_level', None)  # 任务重要度
                create_user_id = self.get_argument('create_user_id', None)  # 创建人
                run_user_id = self.get_argument('run_user_id', None)  # 节点负责人
                tab_type = self.get_argument('tab_type', "all")  # tab页
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))

                filter = []
                if working_no:
                    filter.append(SaleWorkerOrderBase.working_no.like('%' + working_no + '%'))
                if project_ids:
                    if not self.is_valid_json(project_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(project_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.project_id.in_(json.loads(project_ids)))
                if order_type_ids:
                    if not self.is_valid_json(order_type_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(order_type_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.order_type_id.in_(json.loads(order_type_ids)))
                if creat_start_time:
                    filter.append(SaleWorkerOrderBase.creat_time >= creat_start_time)
                if creat_end_time:
                    filter.append(SaleWorkerOrderBase.creat_time <= creat_end_time)
                if plan_start_time:
                    filter.append(SaleWorkerOrderBase.plan_finish_time >= plan_start_time)
                if plan_end_time:
                    filter.append(SaleWorkerOrderBase.plan_finish_time <= plan_end_time)
                if run_type_ids:
                    if not self.is_valid_json(run_type_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(run_type_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.run_type.in_(json.loads(run_type_ids)))
                if project_type and project_type != "全部":
                    filter.append(SaleWorkerOrderBase.project.has(SaleWorkerOrderProject.project_type == project_type))
                if product_type and product_type != "全部":
                    filter.append(SaleWorkerOrderBase.project.has(SaleWorkerOrderProject.product_type == product_type))
                if task_level:
                    if not re.compile(r'^-?\d+$').match(task_level):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.task_level == task_level)
                if create_user_id:
                    if not re.compile(r'^-?\d+$').match(create_user_id):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.create_user_id == create_user_id)
                if run_user_id:
                    if not re.compile(r'^-?\d+$').match(run_user_id):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.run_user_id == run_user_id)
                if tab_type:
                    # session = self.getOrNewSession()
                    # user_id = session.user['id']
                    if tab_type == "wait":  # 待我处理 且状态不是废止和完成
                        filter.append(SaleWorkerOrderBase.run_user_id == user_id)
                        filter.append(SaleWorkerOrderBase.run_type != self.get_run_type_by_name("废止"))
                        filter.append(SaleWorkerOrderBase.run_type != self.get_run_type_by_name("完成"))
                    elif tab_type == "my_dispose":  # 我处理的
                        working_ids = [step.working_id for step in
                                       user_session.query(SaleWorkerOrderStep.working_id)
                                       .filter(SaleWorkerOrderStep.real_user == user_id)
                                       ]
                        if len(working_ids) == 0:
                            return self.returnTotalSuc(data, 0)
                        filter.append(SaleWorkerOrderBase.id.in_(working_ids))
                    elif tab_type == "my_submit":  # 我提交的
                        step_9_id = self.get_run_type_by_name("完成")
                        step_10_id = self.get_run_type_by_name("废止")
                        filter.append(SaleWorkerOrderBase.create_user_id == user_id)
                        filter.append(SaleWorkerOrderBase.run_type != step_9_id)
                        filter.append(SaleWorkerOrderBase.run_type != step_10_id)
                    else:
                        pass
                total = user_session.query(func.count(SaleWorkerOrderBase.id)).filter(*filter).scalar()
                res = user_session.query(SaleWorkerOrderBase).filter(*filter).order_by(
                    SaleWorkerOrderBase.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                if res:
                    work_order_ids = self.get_user_all_workorder(user_id) # 当前用户参与的工单id
                    for item in res:
                        info = {
                            "id": item.id,
                            "working_no": item.working_no,
                            "project_id": item.project_id,
                            "project_name": item.project.name,
                            "project_user": item.project.user,
                            "project_phone": item.project.phone,
                            "project_address": item.project.address,
                            "project_type": item.project.project_type,
                            "device_type": item.project.device_type,
                            "product_type": item.project.product_type,
                            "task_level": item.task_level,
                            "task_level_name": item.task_level_d.name,
                            "order_type_id": item.order_type_id,
                            "order_type_name": item.order_type_p.name,
                            "order_type_child_id": item.order_type_child_id,
                            "order_type_child_name": item.order_type_c.name if item.order_type_child_id else '',
                            "run_type": item.run_type,
                            "run_type_name": item.run_type_d.name,
                            "problem_descr": item.problem_descr,
                            "plan_finish_time": item.plan_finish_time.strftime('%Y-%m-%d'),
                            "creat_time": item.creat_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "files": json.loads(item.files) if item.files else [],
                            "remark": item.remark,
                            "create_user_id": item.create_user_id,
                            "creat_user_name": item.create_user.name,
                            "run_user_id": item.run_user_id,
                            "run_user_name": item.run_user.name if item.run_user_id else '',
                            "over_time": "否" if datetime.now() <= item.plan_finish_time else "是",
                            "handle_time": item.handle_time if item.handle_time else '--',
                        }
                        if item.real_finish_time:
                            if item.over_time == 0:
                                info['over_time'] = "否"
                            else:
                                info['over_time'] = "是"
                        else:
                            if datetime.now() <= item.plan_finish_time:
                                info['over_time'] = "否"
                            else:
                                info['over_time'] = "是"
                        # 查询当前用户是否是此工单参与人
                        # judge_status = self.judge_user_participation(item.id, user_id, item.copy_user)
                        copy_user = []
                        if item.copy_user:
                            copy_user = json.loads(item.copy_user)
                        if item.id in work_order_ids or user_id in copy_user:
                            info['participation'] = 1
                        else:
                            info['participation'] = 0
                        data.append(info)
                return self.returnTotalSuc(data, total)
            elif kt == 'GetRunTypeList':  # 获取处理状态列表
                name = self.get_argument('name', None)
                filter = [SaleWorkerOrderRunType.is_use == 1, SaleWorkerOrderRunType.type == 1]
                if name:
                    filter.append(SaleWorkerOrderRunType.name.like('%' + name + '%'))
                run_type_data = user_session.query(SaleWorkerOrderRunType).filter(*filter).all()
                data = [{'id': item.id, 'name': item.name} for item in run_type_data]
                return self.returnTypeSuc(data)
            elif kt == 'GetprojectTypeList':  # 获取项目类型列表 和 产品类型列表
                name = self.get_argument('name', None)
                # filter = [SaleWorkerOrderProject.project_type.isnot(None), SaleWorkerOrderProject.project_type != '']
                # if name:
                #     filter.append(SaleWorkerOrderProject.project_type.like('%' + name + '%'))
                # distinct_project_types = user_session.query(SaleWorkerOrderProject.project_type) \
                #     .filter(*filter) \
                #     .distinct() \
                #     .all()
                # project_types_result = [project_type[0] for project_type in distinct_project_types]
                #
                # filters = [SaleWorkerOrderProject.product_type.isnot(None), SaleWorkerOrderProject.product_type != '']
                # if name:
                #     filters.append(SaleWorkerOrderProject.product_type.like('%' + name + '%'))
                # distinct_product_types = user_session.query(SaleWorkerOrderProject.product_type) \
                #     .filter(*filters) \
                #     .distinct() \
                #     .all()
                # product_types_result = [product_type[0] for product_type in distinct_product_types]
                # 产品类型
                filter = [SaleWorkerOrderType.is_use == 1, SaleWorkerOrderType.type == 4]
                # 项目类型
                filter1 = [SaleWorkerOrderType.is_use == 1, SaleWorkerOrderType.type == 5]
                if name:
                    filter.append(SaleWorkerOrderType.name.like('%' + name + '%'))
                    filter1.append(SaleWorkerOrderType.name.like('%' + name + '%'))
                product_nodes = user_session.query(SaleWorkerOrderType).filter(*filter).all()
                project_nodes = user_session.query(SaleWorkerOrderType).filter(*filter1).all()
                product_list = [item.name for item in product_nodes]
                project_list = [item.name for item in project_nodes]

                return self.returnTypeSuc({'project_type': project_list, 'product_type': product_list})
            elif kt == 'GetSolveList':  # 获取解决方法列表
                solve_data = user_session.query(SaleWorkerOrderSolve).all()
                data = [{'id': item.id, 'name': item.name} for item in solve_data]
                return self.returnTypeSuc(data)
            elif kt == 'GetUserListByType':  # 根据权限类型获取用户列表 1:新增 4:接单 6:两票审核 8:审核 不传:全部 20：转派（去除自己）
                user_role_type = self.get_argument('role_type', None)
                filter = []
                if user_role_type:
                    if not re.compile(r'^-?\d+$').match(user_role_type):
                        return self.customError("参数错误")
                    allow_role_type = [1, 4, 6, 8, 20]
                    if int(user_role_type) not in allow_role_type:
                        return self.customError("参数错误")
                    have_role_type = [1, 4, 6, 8]
                    if int(user_role_type) in have_role_type:
                        filter.append(RoleWorkerOrder.run_type == int(user_role_type))
                    if int(user_role_type) == 20:
                        filter.append(RoleWorkerOrder.run_type == 1)
                        filter.append(RoleWorkerOrder.user_id != user_id)
                    if int(user_role_type) in [6, 8]:
                        filter.append(RoleWorkerOrder.user_id != 6)

                user_data = user_session.query(distinct(RoleWorkerOrder.user_id), User.name).join(
                    User, RoleWorkerOrder.user_id == User.id).filter(*filter).all()
                user_list = []
                for user_info in user_data:
                    # print(user_info)
                    user_list.append({
                        "id": user_info[0],
                        "name": user_info[1]
                    })
                return self.returnTypeSuc(user_list)
            elif kt == 'GetCopyUserUserList':  # 获取最终审核抄送人列表名单
                user_list = user_session.query(SaleWorkerOrderCopyUser).filter(
                    SaleWorkerOrderCopyUser.type == 1).all()
                copy_users = []
                for user in user_list:
                    copy_users.append({
                        "id": user.user_id,
                        "name": user.users.name
                    })
                return self.returnTypeSuc(copy_users)
            elif kt == 'GetDownloadWorkerOrderUrl':
                """
                下载售后工单地址
                """
                working_no = self.get_argument('working_no', None)  # 工单编号
                project_ids = self.get_argument('project_ids', None)  # 项目id集合
                order_type_ids = self.get_argument('order_type_ids', None)  # 任务类型id集合
                creat_start_time = self.get_argument('creat_start_time', None)  # 创建开始时间
                creat_end_time = self.get_argument('creat_end_time', None)  # 创建截止时间
                plan_start_time = self.get_argument('plan_start_time', None)  # 要求完成开始时间
                plan_end_time = self.get_argument('plan_end_time', None)  # 要求完成截止时间
                run_type_ids = self.get_argument('run_type_ids', None)  # 处理状态id集合
                project_type = self.get_argument('project_type', None)  # 项目类型
                product_type = self.get_argument('product_type', None)  # 产品类型
                task_level = self.get_argument('task_level', None)  # 任务重要度
                create_user_id = self.get_argument('create_user_id', None)  # 创建人
                run_user_id = self.get_argument('run_user_id', None)  # 节点负责人
                tab_type = self.get_argument('tab_type', "all")  # tab页

                filter = []
                if working_no:
                    filter.append(SaleWorkerOrderBase.working_no.like('%' + working_no + '%'))
                if project_ids:
                    if not self.is_valid_json(project_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(project_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.project_id.in_(json.loads(project_ids)))
                if order_type_ids:
                    if not self.is_valid_json(order_type_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(order_type_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.order_type_id.in_(json.loads(order_type_ids)))
                if creat_start_time:
                    filter.append(SaleWorkerOrderBase.creat_time >= creat_start_time)
                if creat_end_time:
                    filter.append(SaleWorkerOrderBase.creat_time <= creat_end_time)
                if plan_start_time:
                    filter.append(SaleWorkerOrderBase.plan_finish_time >= plan_start_time)
                if plan_end_time:
                    filter.append(SaleWorkerOrderBase.plan_finish_time <= plan_end_time)
                if run_type_ids:
                    if not self.is_valid_json(run_type_ids):
                        return self.customError('参数不合规')
                    if len(json.loads(run_type_ids)) > 0:
                        filter.append(SaleWorkerOrderBase.run_type.in_(json.loads(run_type_ids)))
                if project_type and project_type != "全部":
                    filter.append(SaleWorkerOrderBase.project.has(SaleWorkerOrderProject.project_type == project_type))
                if product_type and product_type != "全部":
                    filter.append(SaleWorkerOrderBase.project.has(SaleWorkerOrderProject.product_type == product_type))
                if task_level:
                    if not re.compile(r'^-?\d+$').match(task_level):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.task_level == task_level)
                if create_user_id:
                    if not re.compile(r'^-?\d+$').match(create_user_id):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.create_user_id == create_user_id)
                if run_user_id:
                    if not re.compile(r'^-?\d+$').match(run_user_id):
                        return self.customError('参数不合规')
                    filter.append(SaleWorkerOrderBase.run_user_id == run_user_id)
                if tab_type:
                    # session = self.getOrNewSession()
                    # user_id = session.user['id']
                    if tab_type == "wait":  # 待我处理 且状态不是废止和完成
                        filter.append(SaleWorkerOrderBase.run_user_id == user_id)
                        filter.append(SaleWorkerOrderBase.run_type != self.get_run_type_by_name("废止"))
                        filter.append(SaleWorkerOrderBase.run_type != self.get_run_type_by_name("完成"))
                    elif tab_type == "my_dispose":  # 我处理的
                        working_ids = [step.working_id for step in
                                       user_session.query(SaleWorkerOrderStep.working_id)
                                       .filter(SaleWorkerOrderStep.real_user == user_id)
                                       ]
                        if len(working_ids) != 0:
                            filter.append(SaleWorkerOrderBase.id.in_(working_ids))
                    elif tab_type == "my_submit":  # 我提交的
                        step_9_id = self.get_run_type_by_name("完成")
                        step_10_id = self.get_run_type_by_name("废止")
                        filter.append(SaleWorkerOrderBase.create_user_id == user_id)
                        filter.append(SaleWorkerOrderBase.run_type != step_9_id)
                        filter.append(SaleWorkerOrderBase.run_type != step_10_id)
                    else:
                        pass
                res = user_session.query(SaleWorkerOrderBase).filter(*filter).order_by(
                    SaleWorkerOrderBase.id.desc()).all()

                data_list = [['项目名称', '项目类型', '故障编号', '故障所在分区', '故障影响范围', '停运容量（kWh）', '故障设备编号', '设备类型',
                              '故障设备部件', '故障设备子部件', '设备厂家', '设备型号', '停运等级', '修复进度', '发现时间', '发生时间', '预计修复时间', '实际修复时间',
                              '售后负责人', '故障现象', '故障原因及影响因素分析', '现场解决方案', '后续预防措施', '消耗备件']]
                if res:
                    for item in res:
                        step_7_id = self.get_run_type_by_name("执行")
                        # 获取是否有执行记录，有则获取执行过程描述作为现场解决方案
                        step_7_data = user_session.query(SaleWorkerOrderStep).filter(
                            SaleWorkerOrderStep.working_id == item.id,
                            SaleWorkerOrderStep.run_type == step_7_id).order_by(SaleWorkerOrderStep.creat_time.desc()).first()
                        if step_7_data:
                            scene_solution = step_7_data.content
                        else:
                            scene_solution = ''
                        project_name = item.project.name
                        if item.real_finish_time:
                            reality_repair_time = item.real_finish_time.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            reality_repair_time = ''
                        order_type_child_name = item.order_type_c.name if item.order_type_child_id else ''
                        data_list.append([project_name, item.project.project_type, item.working_no, '', '', '', '',
                                          item.order_type_p.name, order_type_child_name, '', '', '', item.task_level_d.name, item.run_type_d.name, item.creat_time.strftime('%Y-%m-%d %H:%M:%S'),
                                          '', item.plan_finish_time.strftime('%Y-%m-%d'), reality_repair_time, item.create_user.name, item.problem_descr,
                                          '', scene_solution, '', ''])

                # 将数据转换为DataFrame
                df = pd.DataFrame(data_list[1:], columns=data_list[0])

                # 创建一个BytesIO对象，用于保存Excel文件的二进制数据
                excel_buffer = io.BytesIO()

                # 将DataFrame写入Excel文件
                df.to_excel(excel_buffer, index=False)

                # 将BytesIO对象的位置重置到开始，以便从头读取数据
                excel_buffer.seek(0)

                # 将二进制数据转换为字节
                binary_data = excel_buffer.read()

                # 定义存储桶名称和对象名称（即文件名）
                bucket_name = 'rhyc'
                object_name = (str(timeUtils.getNewTimeStr()[0:10].replace('-', '') + "售后任务记录") + "-" +
                               str(timeUtils.nowSecs()) + ".xlsx")

                # 调用upload_file方法上传Excel文件
                storage_url = upload_file(binary_data, bucket_name, object_name)
                storage_url = storage_url.split('?', 1)[0]

                return self.returnTypeSuc({'down_url': storage_url})
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()
        try:
            session = self.getOrNewSession()
            user_id = session.user['id']
            user_role_id = session.user['user_role_id']
            if kt == 'AddTypeChild':
                name = self.get_argument('name', None)
                parent_id = self.get_argument('parent_id', None)
                if DEBUG:
                    logging.info('name:%s, parent_id:%s' % (name, parent_id))
                if not name or not parent_id:
                    return self.customError("参数不完整")
                # 查询父级是否存在
                parent_data = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.id == int(parent_id),
                                                                             SaleWorkerOrderType.is_use == 1).first()
                if not parent_data:
                    return self.customError("父级类型不存在")

                pa = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.name == name,
                                                                    SaleWorkerOrderType.parent_id == parent_id,
                                                                    SaleWorkerOrderType.is_use == 1).first()
                if pa:
                    return self.customError("名称已存在")
                p = SaleWorkerOrderType(name=name, parent_id=int(parent_id), type=2,
                                        creat_time=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'AddSaleWorkerOrder':  # 新增工单
                working_no = datetime.now().strftime('%Y%m%d%H%M%S')  # 工单编号
                project_id = self.get_argument('project_id', None)  # 项目id
                order_type_id = self.get_argument('order_type_id', None)  # 任务类型id
                order_type_child_id = self.get_argument('order_type_child_id', None)  # 任务子类型id
                problem_descr = self.get_argument('problem_descr', None)  # 问题描述
                task_level = self.get_argument('task_level', None)  # 任务重要度
                plan_finish_time = self.get_argument('plan_finish_time', None)  # 要求完成时间
                # files = self.get_argument('files', None)  # 附件内容集合
                remark = self.get_argument('remark', None)  # 备注
                user_role_status = self.get_user_role_status("新增", user_id)
                if not user_role_status:
                    return self.customError("权限不足")
                if not project_id or not order_type_id or not problem_descr or not task_level or not plan_finish_time:
                    return self.customError("参数不完整")
                if order_type_id:
                    order_type_id = int(order_type_id)
                    root_nodes = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.type == 2,
                                                                                SaleWorkerOrderType.is_use == 1,
                                                                                SaleWorkerOrderType.parent_id ==
                                                                                order_type_id).all()
                    if root_nodes and not order_type_child_id:
                        return self.customError("请选择任务子类型")
                if DEBUG:
                    logging.info('working_no:%s, project_id:%s, order_type_id:%s, order_type_child_id:%s, '
                                 'problem_descr:%s, task_level:%s, plan_finish_time:%s, remark:%s' % (
                                     working_no, project_id, order_type_id, order_type_child_id, problem_descr,
                                     task_level,
                                     plan_finish_time, remark))
                # session = self.getOrNewSession()
                # user_id = session.user['id']
                # user_role_id = session.user['user_role_id']
                # if user_role_id != 1:

                files = self.request.files
                file_list = files.get('files')
                file_arr = []
                max_file_size = 40 * 1024 * 1024  # 40MB限制
                if file_list:
                    for i in file_list:
                        data = i.get('body')
                        # file_size = len(data)
                        # if file_size > max_file_size:
                        #     return self.customError("文件大小超过40M限制")
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        file_arr.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                files_string = ''
                if len(file_arr) > 0:
                    files_string = json.dumps(file_arr)
                if order_type_child_id:
                    order_type_child_id = int(order_type_child_id)
                else:
                    order_type_child_id = None
                ys_id = self.get_run_type_by_name("预审")
                if not ys_id:
                    return self.customError("参数错误")
                time_format = "%Y-%m-%d %H:%M:%S"
                plan_finish_time_datetime = datetime.strptime(plan_finish_time[:10] + " 23:59:59", time_format)
                add_base = SaleWorkerOrderBase(creat_time=timeUtils.getNewTimeStr(), working_no=working_no,
                                               project_id=int(project_id), create_user_id=user_id,
                                               problem_descr=problem_descr,
                                               order_type_id=order_type_id, order_type_child_id=order_type_child_id,
                                               task_level=int(task_level), plan_finish_time=plan_finish_time_datetime,
                                               files=files_string, run_type=ys_id, remark=remark, run_user_id=6)
                user_session.add(add_base)
                user_session.flush()
                # 添加新增执行步骤
                xz_id = self.get_run_type_by_name("新增")
                kwargs = {
                    'creat_time': timeUtils.getNewTimeStr(),
                    'working_no': working_no,
                    'working_id': add_base.id,
                    'create_user_id': user_id,
                    'plan_user': user_id,
                    'real_user': user_id,
                    'run_type': xz_id,
                    'check_flag': 1
                }
                order_step_xz = SaleWorkerOrderStep(**kwargs)
                user_session.add(order_step_xz)
                # 添加售后工单执行步骤表
                kwargs1 = {
                    'creat_time': timeUtils.getNewTimeStr(),
                    'working_no': working_no,
                    'working_id': add_base.id,
                    'create_user_id': user_id,
                    'plan_user': 6,
                    'run_type': ys_id
                }
                order_step = SaleWorkerOrderStep(**kwargs1)
                user_session.add(order_step)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'WithdrawOrder':  # 撤回工单
                working_id = self.get_argument('working_id', None)
                if not working_id:
                    return self.customError("参数不完整")
                if not re.compile(r'^-?\d+$').match(working_id):
                    return self.customError("参数错误")
                if DEBUG:
                    logging.info('working_id:%s' % working_id)
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == working_id).first()
                if not worker_order:
                    return self.customError("工单不存在")
                if worker_order.run_type_d.name != "预审":
                    return self.customError("工单不可撤回")
                if user_id != worker_order.create_user_id:
                    return self.customError("权限不足")

                # 执行撤回操作
                # 1、修改base表run_type、run_user_id 2、更新step中新增的状态为修改
                step_up_id = self.get_run_type_by_name("修改")
                worker_order.run_type = step_up_id
                worker_order.run_user_id = user_id
                step_new_id = self.get_run_type_by_name("新增")
                affected_rows = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.run_type == step_new_id, SaleWorkerOrderStep.working_id == working_id).update(
                    {SaleWorkerOrderStep.run_type: step_up_id}, synchronize_session=False)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'EditOrder':  # 修改工单
                # 先检查当前用户是否有此权限
                role_status = self.get_user_role_status("修改", user_id)
                if not role_status:
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                project_id = self.get_argument('project_id', None)  # 项目id
                order_type_id = self.get_argument('order_type_id', None)  # 任务类型id
                order_type_child_id = self.get_argument('order_type_child_id', None)  # 任务子类型id
                problem_descr = self.get_argument('problem_descr', None)  # 问题描述
                task_level = self.get_argument('task_level', None)  # 任务重要度
                plan_finish_time = self.get_argument('plan_finish_time', None)  # 要求完成时间
                # files = self.get_argument('files', None)  # 附件内容集合
                remark = self.get_argument('remark', None)  # 备注
                del_file_keys = self.get_argument('del_file_keys', None)
                if DEBUG:
                    logging.info('working_id:%s, project_id:%s, order_type_id:%s, order_type_child_id:%s, '
                                 'problem_descr:%s, task_level:%s, plan_finish_time:%s, remark:%s' % (
                                     working_id, project_id, order_type_id, order_type_child_id, problem_descr,
                                     task_level,
                                     plan_finish_time, remark))
                if not working_id:
                    return self.customError("参数不完整")
                if not re.compile(r'^-?\d+$').match(working_id):
                    return self.customError("参数错误")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == working_id).first()
                if worker_order.run_user_id != user_id:
                    return self.customError("权限不足")
                if not worker_order:
                    return self.customError("工单不存在")
                if order_type_id:
                    order_type_id = int(order_type_id)
                    root_nodes = user_session.query(SaleWorkerOrderType).filter(SaleWorkerOrderType.type == 2,
                                                                                SaleWorkerOrderType.is_use == 1,
                                                                                SaleWorkerOrderType.parent_id ==
                                                                                order_type_id).all()
                    if root_nodes and not order_type_child_id:
                        return self.customError("请选择任务子类型")
                    worker_order.order_type_id = int(order_type_id)
                if project_id:
                    worker_order.project_id = int(project_id)
                if order_type_child_id:
                    worker_order.order_type_child_id = int(order_type_child_id)
                if problem_descr:
                    worker_order.problem_descr = problem_descr
                if task_level:
                    worker_order.task_level = int(task_level)
                if plan_finish_time:
                    time_format = "%Y-%m-%d %H:%M:%S"
                    plan_finish_time_datetime = datetime.strptime(plan_finish_time[:10] + " 23:59:59", time_format)
                    worker_order.plan_finish_time = plan_finish_time_datetime
                if remark:
                    worker_order.remark = remark
                if worker_order.files:
                    file_new_list = json.loads(worker_order.files)
                else:
                    file_new_list = []
                if del_file_keys:
                    del_file_keys = json.loads(del_file_keys)
                    if len(del_file_keys) > 0:
                        file_list = json.loads(worker_order.files)
                        file_new_list = [item for item in file_list if item["file_key"] not in del_file_keys]
                files = self.request.files
                file_list = files.get('files')
                max_file_size = 40 * 1024 * 1024  # 40MB限制
                if file_list:
                    for i in file_list:
                        data = i.get('body')
                        # file_size = len(data)
                        # if file_size > max_file_size:
                        #     return self.customError("文件大小超过40M限制")
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        file_new_list.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                if len(file_new_list) >= 0:
                    files_string = json.dumps(file_new_list)
                    worker_order.files = files_string
                # 状态要改为预审状态
                ys_id = self.get_run_type_by_name("预审")
                if not ys_id:
                    return self.customError("参数错误")
                worker_order.run_type = ys_id
                worker_order.run_user_id = 6

                # step修改的step需要改为新增
                step_up_id = self.get_run_type_by_name("修改")
                step_new_id = self.get_run_type_by_name("新增")
                user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.run_type == step_up_id, SaleWorkerOrderStep.working_id == working_id).update(
                    {SaleWorkerOrderStep.run_type: step_new_id}, synchronize_session=False)
                # 如果是驳回修改，需要新增一条预审记录
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.run_type == ys_id, SaleWorkerOrderStep.working_id == working_id).order_by(
                    SaleWorkerOrderStep.id.desc()).first()
                if worker_step and getattr(worker_step, 'check_flag', None) == 2:
                    kwargs1 = {
                        'creat_time': timeUtils.getNewTimeStr(),
                        'working_no': worker_order.working_no,
                        'working_id': worker_order.id,
                        'create_user_id': user_id,
                        'plan_user': 6,
                        'run_type': ys_id
                    }
                    order_step = SaleWorkerOrderStep(**kwargs1)
                    user_session.add(order_step)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'PreExaminationOrder':  # 预审工单
                role_status = self.get_user_role_status("预审", user_id)
                if not role_status:
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                check_flag = self.get_argument('check_flag', None)  # 审核状态: 1通过, 2驳回, 3废止
                taking_order_user = self.get_argument('taking_order_user', None)  # 接单人id
                two_ticket_review_status = self.get_argument('two_ticket_review_status', None)  # 是否需要两票审核 1 是 2否
                two_ticket_review_users = self.get_argument('two_ticket_review_users', None)  # 两票审核人 按顺序传
                # cc_users = self.get_argument('cc_users', None)  # 预审审核抄送人列表
                execute_check_user = self.get_argument('execute_check_user', None)  # 执行后审核人列表
                check_opinion = self.get_argument('check_opinion', None)  # 审核意见
                if not working_id or not step_id or not check_flag:
                    return self.customError("参数不完整")
                if (not re.compile(r'^-?\d+$').match(working_id) or not re.compile(r'^-?\d+$').match(step_id)
                        or not re.compile(r'^-?\d+$').match(check_flag)):
                    return self.customError("参数错误")
                if int(check_flag) == 1 and not taking_order_user:
                    # if not taking_order_user or not execute_check_user:
                    return self.customError("参数不完整")
                # else:
                if int(check_flag) != 1 and not check_opinion:
                    return self.customError("参数不完整")
                if two_ticket_review_status:
                    if not re.compile(r'^-?\d+$').match(two_ticket_review_status):
                            return self.customError("参数错误")
                    # if int(two_ticket_review_status) == 1 and not two_ticket_review_users:
                    #     return self.customError("参数不完整")
                    if int(two_ticket_review_status) == 1 and two_ticket_review_users:
                        if not self.is_valid_json(two_ticket_review_users):
                            return self.customError("参数格式错误")
                        two_ticket_review_users_list = json.loads(two_ticket_review_users)
                        # if len(two_ticket_review_users_list) == 0:
                        #     return self.customError("参数错误")
                        for t_t_user_id in two_ticket_review_users_list:
                            # 判断接单人是否有接单权限
                            if not self.get_user_role_status("两票审核", int(t_t_user_id)):
                                user_session.rollback()
                                return self.customError("新增两票审核人权限不足")
                e_c_user_list = []
                if execute_check_user:
                    e_c_user_list = json.loads(execute_check_user)
                    for e_c_user_id in e_c_user_list:
                        if not self.get_user_role_status("审核", int(e_c_user_id)):
                            user_session.rollback()
                            return self.customError("新增执行完审核人权限不足")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == working_id).first()
                if not worker_order:
                    return self.customError("工单不存在")
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == step_id, SaleWorkerOrderStep.working_id == working_id).first()
                if not worker_step:
                    return self.customError("工单进度信息不存在")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                # 先处理通过时的逻辑
                # email_list = []
                # if cc_users:
                #     ids = json.loads(cc_users)
                #     # 查询抄送人邮箱
                #     emails = user_session.query(User).filter(User.id.in_(ids)).all()
                #     email_list = [email.email for email in emails if email.email]
                if int(check_flag) == 1:
                    if worker_step.run_type != self.get_run_type_by_name("预审"):
                        return self.customError("工单状态错误")
                    # 判断接单人是否有接单权限
                    if not re.compile(r'^-?\d+$').match(taking_order_user):
                            return self.customError("参数错误")
                    if not self.get_user_role_status("接单", int(taking_order_user)):
                        return self.customError("接单人权限不足")
                    # 1、更新步骤审核状态、更新实际执行人、更新执行时间、更新审核意见
                    worker_step.check_flag = 1
                    worker_step.real_user = user_id
                    worker_step.implement_time = timeUtils.getNewTimeStr()
                    worker_step.content = check_opinion
                    # 2、更新base表的处理状态、节点负责人
                    worker_order.run_type = self.get_run_type_by_name("接单")
                    worker_order.run_user_id = int(taking_order_user)
                    # if cc_users:
                    #     cc_user_list = json.loads(cc_users)
                    #     cc_user_data = {'start_check': cc_user_list}
                    #     worker_order.copy_user = json.dumps(cc_user_data)
                    # 3、新增step:接单人step、两票文件上传step、两票审核人step、执行step、执行后审核step
                    step_kwargs = {
                        'platform_id': worker_step.id,
                        'creat_time': timeUtils.getNewTimeStr()
                    }
                    self.add_step_by_run_type(working_id, "接单", worker_order.working_no, user_id, taking_order_user,
                                              step_kwargs)
                    if int(two_ticket_review_status) == 1:
                        self.add_step_by_run_type(working_id, "两票文件上传", worker_order.working_no,
                                                  user_id, taking_order_user, step_kwargs)
                    step_kwargs['check_type'] = 1
                    # 先增加吴晗两票审核节点，吴晗user_id固定6
                    if int(two_ticket_review_status) == 1:
                        self.add_step_by_run_type(working_id, "两票审核", worker_order.working_no,
                                                  user_id, 6, step_kwargs)
                    if two_ticket_review_users:
                        t_t_users_list = json.loads(two_ticket_review_users)
                        for t_t_user_id in t_t_users_list:
                            self.add_step_by_run_type(working_id, "两票审核", worker_order.working_no,
                                                      user_id, t_t_user_id, step_kwargs)
                    self.add_step_by_run_type(working_id, "执行", worker_order.working_no, user_id, taking_order_user,
                                              step_kwargs)
                    # 增加吴晗执行后审核节点，吴晗user_id固定6
                    self.add_step_by_run_type(working_id, "审核", worker_order.working_no,
                                              user_id, 6, step_kwargs)
                    if execute_check_user:
                        for e_c_user_id in e_c_user_list:
                            self.add_step_by_run_type(working_id, "审核", worker_order.working_no,
                                                      user_id, e_c_user_id, step_kwargs)
                    user_session.commit()
                    # 4、给抄送人发送邮件
                    # if email_list:
                    #     message = """
                    #     工单描述：%s
                    #     项目名称：%s
                    #     审核结果：%s
                    #     接单人：%s
                    #     是否需要两票审核：%s
                    #     两票审核人：%s
                    #     执行后审核人：%s
                    #     审核意见：%s
                    #     """
                    #     taking_order_user_data = user_session.query(User).filter(
                    #         User.id == int(taking_order_user)).first()
                    #     msg_xy = "否"
                    #     tt_user = ""
                    #     ec_user = ""
                    #     if int(two_ticket_review_status) == 1:
                    #         msg_xy = "是"
                    #         t_t_users_list = json.loads(two_ticket_review_users)
                    #         for t_t_user_id in t_t_users_list:
                    #             user_info = user_session.query(User).filter(User.id == t_t_user_id).first()
                    #             tt_user += user_info.name + ","
                    #         tt_user = tt_user.rstrip(',')
                    #     if execute_check_user:
                    #         ec_user_list = json.loads(execute_check_user)
                    #         for ec_user_id in ec_user_list:
                    #             user_info = user_session.query(User).filter(User.id == ec_user_id).first()
                    #             ec_user += user_info.name + ","
                    #         ec_user = ec_user.rstrip(',')
                    #     formatted_message = message % (
                    #         worker_order.problem_descr, worker_order.project.name, "通过", taking_order_user_data.name,
                    #         msg_xy, tt_user,
                    #         ec_user, check_opinion if check_opinion else "无")
                    #     sendMail_(formatted_message, worker_order.working_no + "预审通过记录", "山海系统", "XXX",
                    #               email_list)
                # 驳回
                elif int(check_flag) == 2:
                    # 1、修改base表run_type、run_user_id 2、更新step中新增的状态为修改
                    if worker_order.run_type_d.name != "预审":
                        return self.customError("工单不可驳回")
                    step_up_id = self.get_run_type_by_name("修改")
                    worker_order.run_type = step_up_id
                    worker_order.run_user_id = worker_order.create_user_id
                    step_ys_id = self.get_run_type_by_name("预审")
                    step_xz_id = self.get_run_type_by_name("新增")
                    # 更新预审状态下的 check_flag 为驳回状态，real_user、check_opinion、implement_time字段
                    user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.run_type == step_ys_id,
                        SaleWorkerOrderStep.id == step_id,
                        SaleWorkerOrderStep.working_id == working_id).update(
                        {SaleWorkerOrderStep.check_flag: 2, SaleWorkerOrderStep.real_user: user_id,
                         SaleWorkerOrderStep.content: check_opinion,
                         SaleWorkerOrderStep.implement_time: timeUtils.getNewTimeStr()}, synchronize_session=False)
                    user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.run_type == step_xz_id,
                        SaleWorkerOrderStep.working_id == working_id).update(
                        {SaleWorkerOrderStep.run_type: step_up_id},
                        synchronize_session=False)
                    user_session.commit()
                    # if email_list:
                    #     message = """
                    #     工单描述：%s
                    #     项目名称：%s
                    #     审核结果：%s
                    #     审核意见：%s
                    #     """
                    #     formatted_message = message % (
                    #         worker_order.problem_descr, worker_order.project.name, "驳回",
                    #         check_opinion if check_opinion else "无")
                    #     sendMail_(formatted_message, worker_order.working_no + "预审驳回", "山海系统", "XXX",
                    #               email_list)
                # 废止
                elif int(check_flag) == 3:
                    if worker_order.run_type_d.name != "预审":
                        return self.customError("工单不可废止")
                    if not check_opinion:
                        return self.customError("请填写审核意见")
                    # 更新base状态为废止，更新step状态为废止，更新审核意见
                    fz_step_id = self.get_run_type_by_name("废止")
                    worker_order.run_type = fz_step_id
                    worker_order.sys_finish_time = timeUtils.getNewTimeStr()
                    worker_step.real_user = user_id
                    worker_step.check_flag = 3
                    worker_step.implement_time = timeUtils.getNewTimeStr()
                    worker_step.content = check_opinion
                    user_session.commit()
                    # if email_list:
                    #     message = """
                    #     工单描述：%s
                    #     项目名称：%s
                    #     审核结果：%s
                    #     审核意见：%s
                    #     """
                    #     formatted_message = message % (
                    #         worker_order.problem_descr, worker_order.project.name, "废止",
                    #         check_opinion if check_opinion else "无")
                    #     sendMail_(formatted_message, worker_order.working_no + "预审废止", "山海系统", "XXX",
                    #               email_list)
                else:
                    return self.customError("参数错误")
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'ConfirmOrder':  # 确认接单/转派
                role_status = self.get_user_role_status("接单", user_id)
                if not role_status:
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                remark = self.get_argument('remark', None)
                take_order_type = self.get_argument('take_order_type', None)  # 1确认接单 2转派
                redeploy_order_user = self.get_argument('redeploy_order_user', None)  # 转派用户
                if not working_id or not step_id or not take_order_type:
                    return self.customError("参数不完整")
                if int(take_order_type) != 1 and not redeploy_order_user:
                    return self.customError("请选择转派人")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == working_id).first()
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == step_id, SaleWorkerOrderStep.care_flag == 0).first()
                if not worker_order or not worker_step:
                    return self.customError("工单不存在或者工单已经转派")
                if worker_order.run_type != self.get_run_type_by_name("接单"):
                    return self.customError("工单不可确认接单或转派")
                if worker_step.run_type != self.get_run_type_by_name("接单"):
                    return self.customError("工单不可确认接单或转派")
                if redeploy_order_user:
                    if not self.get_user_role_status("接单", redeploy_order_user):
                        return self.customError("转派用户权限不足")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                # 查询此工单是否需要两票审核
                count = user_session.query(func.count(SaleWorkerOrderStep.id)).filter(
                    # SaleWorkerOrderStep.platform_id == worker_step.platform_id,
                    SaleWorkerOrderStep.working_id == working_id,
                    SaleWorkerOrderStep.run_type == self.get_run_type_by_name("两票审核")).scalar()
                if int(take_order_type) == 1:  # 确认接单
                    # 更新当前步骤实际操作人和执行时间以及备注
                    if remark:
                        worker_step.remark = remark
                    worker_step.real_user = user_id
                    worker_step.implement_time = timeUtils.getNewTimeStr()
                    if count:
                        # 需要两票审核 则需要 更新base表run_type 和 run_user_id 因为两票文件上传的步骤已经在预审通过时添加了所以不需要添加了
                        worker_order.run_type = self.get_run_type_by_name("两票文件上传")
                        user_session.commit()
                    else:
                        # 没有查到，说明预审时选择的不需要两票审核，那么则直接将base表run_type改为执行，并在setp记录表增加一条执行记录
                        worker_order.run_type = self.get_run_type_by_name("执行")
                        # self.add_step_by_run_type(working_id, "执行", worker_order.working_no, user_id,
                        #                           worker_step.plan_user)
                        user_session.commit()
                else:
                    # 如果是转派 则需要判断当前工单已经转派次数，大于等于3时则只能确认接单
                    care_count = user_session.query(func.count(SaleWorkerOrderStep.id)).filter(
                        SaleWorkerOrderStep.working_id == working_id,
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("接单"),
                        SaleWorkerOrderStep.care_flag == 1).scalar()
                    if care_count >= 3:
                        return self.customError("工单转派次数已超过3次，请确认接单")
                    # 还需要判断被转派人是否参与过当前订单的接单状态
                    care_user_count = user_session.query(func.count(SaleWorkerOrderStep.id)).filter(
                        SaleWorkerOrderStep.working_id == working_id,
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("接单"),
                        SaleWorkerOrderStep.real_user == redeploy_order_user).scalar()
                    if care_user_count:
                        return self.customError("转派人已参与过此工单接单，请选择其他人进行转派")

                    # 转派 新增一条转派人接单的step,
                    # 更新原接单人的转单状态为1 新增接单后的所有其它节点
                    # 更新base表 run_user_id

                    redeploy_order_user = int(redeploy_order_user)
                    self.add_step_by_run_type(working_id, "接单", worker_order.working_no, user_id,
                                              redeploy_order_user)
                    worker_step.care_flag = 1
                    if remark:
                        worker_step.remark = remark
                    worker_step.real_user = user_id
                    worker_step.implement_time = timeUtils.getNewTimeStr()
                    worker_order.run_user_id = redeploy_order_user
                    worker_order.run_type = self.get_run_type_by_name("接单")
                    # 新增接单后的其它节点
                    step_5_id = self.get_run_type_by_name("两票文件上传")
                    check_two_tickets_id = self.get_run_type_by_name("两票审核")
                    two_tickets_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.real_user.is_(None),
                        SaleWorkerOrderStep.run_type == step_5_id).order_by(
                        SaleWorkerOrderStep.id.desc()).first()
                    # 新增两票文件上传step记录、新增两票审核step记录 查询当前工单所有的两票审核记录以plan_user去重的结果
                    creat_time = timeUtils.getNewTimeStr()
                    if two_tickets_data:
                        self.add_step_by_run_type(working_id, "两票文件上传", worker_order.working_no, user_id,
                                                  redeploy_order_user, {"creat_time": creat_time})

                        step_6_data = user_session.query(distinct(SaleWorkerOrderStep.plan_user)).filter(
                            SaleWorkerOrderStep.working_id == int(working_id),
                            SaleWorkerOrderStep.run_type == check_two_tickets_id
                        ).all()
                        for i in step_6_data:
                            plan_user = i[0]
                            self.add_step_by_run_type(working_id, "两票审核", worker_order.working_no, user_id,
                                                      plan_user, {"check_type": 1, "creat_time": creat_time})

                    # 添加执行节点
                    self.add_step_by_run_type(working_id, "执行", worker_order.working_no, user_id,
                                              redeploy_order_user, {"creat_time": creat_time})
                    step_8_data = user_session.query(distinct(SaleWorkerOrderStep.plan_user)).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("审核")
                    ).all()
                    for i in step_8_data:
                        plan_user = i[0]
                        self.add_step_by_run_type(working_id, "审核", worker_order.working_no, user_id,
                                                  plan_user, {"check_type": 1, "creat_time": creat_time})
                    user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'UploadTwoTickets':  # 两票文件上传
                if not self.get_user_role_status("两票文件上传", user_id):
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                remark = self.get_argument('remark', None)
                if not working_id or not step_id:
                    return self.customError("参数不完整")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == int(working_id)).first()
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == int(step_id)).first()
                if not worker_order or not worker_step:
                    return self.customError("工单不存在")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                two_tickets_id = self.get_run_type_by_name("两票文件上传")
                if worker_order.run_type != two_tickets_id:
                    return self.customError("工单节点错误")
                if worker_step.run_type != two_tickets_id:
                    return self.customError("工单节点错误")

                files = self.request.files
                file_list = files.get('files')
                img_list = files.get('imgs')
                if not file_list and not img_list:
                    return self.customError("请至少上传一张图片或上传一个文件")
                file_new_list = []
                img_new_list = []
                max_file_size = 40 * 1024 * 1024  # 40MB限制
                if file_list:
                    for i in file_list:
                        data = i.get('body')
                        # file_size = len(data)
                        # if file_size > max_file_size:
                        #     return self.customError("文件大小超过40M限制")
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        new_filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', new_filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        file_new_list.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                if img_list:
                    allow_type = ['jpg', 'jpeg', 'png', 'svg']
                    for i in img_list:
                        data = i.get('body')
                        # file_size = len(data)
                        # if file_size > max_file_size:
                        #     return self.customError("文件大小超过40M限制")
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        if filename_data[1][1:] not in allow_type:
                            return self.customError("图片格式错误")
                        new_filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', new_filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        img_new_list.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                if remark:
                    worker_step.remark = remark
                worker_step.real_user = user_id
                worker_step.implement_time = timeUtils.getNewTimeStr()
                if file_new_list:
                    worker_step.files = json.dumps(file_new_list)
                if img_new_list:
                    worker_step.imgs = json.dumps(img_new_list)
                # 更新状态为两票审核 更新节点负责人
                next_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.working_id == working_id,
                    SaleWorkerOrderStep.check_flag.is_(None),
                    SaleWorkerOrderStep.run_type == self.get_run_type_by_name("两票审核")).order_by(
                    SaleWorkerOrderStep.creat_time.desc(), SaleWorkerOrderStep.id.asc()).first()
                worker_order.run_user_id = next_step.plan_user
                worker_order.run_type = self.get_run_type_by_name("两票审核")
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'CheckTwoTickets':  # 两票文件审核
                if not self.get_user_role_status("两票审核", user_id):
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                remark = self.get_argument('remark', None)
                check_flag = self.get_argument('check_flag', None)
                if not working_id or not step_id or not check_flag:
                    return self.customError("参数不完整")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == int(working_id)).first()
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == int(step_id)).first()
                if not worker_order or not worker_step:
                    return self.customError("工单不存在")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                if worker_step.real_user or worker_step.check_flag:
                    return self.customError("工单节点错误")
                check_two_tickets_id = self.get_run_type_by_name("两票审核")
                if worker_order.run_type != check_two_tickets_id:
                    return self.customError("工单节点错误")
                if worker_step.run_type != check_two_tickets_id:
                    return self.customError("工单节点错误")
                if int(check_flag) == 1:  # 通过
                    # 通过有两种情况 1、还存在两票审核未审核情况 2、没有两票审核未审核情况
                    step_6_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.check_flag.is_(None),
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("两票审核"),
                        SaleWorkerOrderStep.id > int(step_id),
                        SaleWorkerOrderStep.plan_user != worker_step.plan_user).order_by(
                        SaleWorkerOrderStep.id.asc()).first()
                    if step_6_data:
                        # 1、还存在两票审核未审核情况的情况：base节点负责人更新为下一个审核人、step表更新当前记录的real_user等字段
                        worker_order.run_user_id = step_6_data.plan_user
                        worker_step.real_user = user_id
                        if remark:
                            worker_step.remark = remark
                        worker_step.check_flag = 1
                        worker_step.implement_time = timeUtils.getNewTimeStr()
                        user_session.commit()
                    else:
                        # 2、没有两票审核未审核情况:base节点变更为执行(即上传两票的人)、节点状态变更执行、新增执行节点步骤、step表更新当前记录的real_user等字段
                        step_7_id = self.get_run_type_by_name("执行")
                        step_7_data = user_session.query(SaleWorkerOrderStep).filter(
                            SaleWorkerOrderStep.working_id == int(working_id),
                            SaleWorkerOrderStep.real_user.is_(None),
                            SaleWorkerOrderStep.run_type == step_7_id).order_by(
                            SaleWorkerOrderStep.id.desc()).first()
                        worker_order.run_user_id = step_7_data.plan_user
                        worker_order.run_type = step_7_id
                        worker_step.check_flag = 1
                        worker_step.real_user = user_id
                        if remark:
                            worker_step.remark = remark
                        worker_step.implement_time = timeUtils.getNewTimeStr()
                        step_8_first_data = user_session.query(SaleWorkerOrderStep).filter(
                            SaleWorkerOrderStep.working_id == int(working_id),
                            SaleWorkerOrderStep.run_type == self.get_run_type_by_name("审核"),
                            SaleWorkerOrderStep.real_user.is_(None),
                        ).order_by(SaleWorkerOrderStep.id.asc()).first()
                        if not step_8_first_data.plan_user:
                            user_session.rollback()
                            return self.customError("工单节点错误")
                        user_session.commit()
                elif int(check_flag) == 2:  # 驳回
                    if not remark:
                        return self.customError("请填写驳回原因")
                    # 两票审核驳回
                    worker_step.check_flag = 2
                    worker_step.real_user = user_id
                    worker_step.remark = remark
                    worker_step.implement_time = timeUtils.getNewTimeStr()
                    # 节点状态更新为两票文件上传 更新节点负责人：查询出最新的一条节点状态是两票文件上传的步骤信息，获取其预计执行人id
                    step_5_id = self.get_run_type_by_name("两票文件上传")
                    worker_order.run_type = step_5_id
                    two_tickets_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.real_user.isnot(None),
                        SaleWorkerOrderStep.run_type == step_5_id).order_by(
                        SaleWorkerOrderStep.id.desc()).first()
                    worker_order.run_user_id = two_tickets_data.plan_user
                    # 新增两票文件上传step记录、新增两票审核step记录 查询当前工单所有的两票审核记录以plan_user去重的结果
                    creat_time = timeUtils.getNewTimeStr()
                    self.add_step_by_run_type(working_id, "两票文件上传", worker_order.working_no, user_id,
                                              two_tickets_data.plan_user, {"creat_time": creat_time})

                    step_6_data = user_session.query(distinct(SaleWorkerOrderStep.plan_user)).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.run_type == check_two_tickets_id
                    ).all()
                    for i in step_6_data:
                        plan_user = i[0]
                        self.add_step_by_run_type(working_id, "两票审核", worker_order.working_no, user_id,
                                                  plan_user, {"check_type": 1, "creat_time": creat_time})

                    # 查询最大的一条执行人的记录
                    step_7_id = self.get_run_type_by_name("执行")
                    step_7_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.run_type == step_7_id).order_by(SaleWorkerOrderStep.id.desc()).first()
                    self.add_step_by_run_type(working_id, "执行", worker_order.working_no, user_id,
                                              step_7_data.plan_user, {"creat_time": creat_time})
                    step_8_data = user_session.query(distinct(SaleWorkerOrderStep.plan_user)).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("审核")
                    ).all()
                    for i in step_8_data:
                        plan_user = i[0]
                        self.add_step_by_run_type(working_id, "审核", worker_order.working_no, user_id,
                                                  plan_user, {"check_type": 1, "creat_time": creat_time})
                    user_session.commit()
                else:
                    return self.customError("参数错误")
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'ExecuteOrder':  # 执行任务记录
                if not self.get_user_role_status("执行", user_id):
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                real_finish_time = self.get_argument('real_finish_time', None)
                content = self.get_argument('content', None)
                solve_id = self.get_argument('solve_id', None)
                remark = self.get_argument('remark', None)
                if not working_id or not step_id or not content or not real_finish_time:
                    return self.customError("参数不完整")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == int(working_id)).first()
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == int(step_id)).first()
                if not worker_order or not worker_step:
                    return self.customError("工单不存在")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                if worker_step.real_user or worker_step.check_flag:
                    return self.customError("工单节点错误")
                step_7_id = self.get_run_type_by_name("执行")
                if worker_order.run_type != step_7_id or worker_step.run_type != step_7_id:
                    return self.customError("工单节点错误")
                format_str = "%Y-%m-%d %H:%M:%S"
                real_finish_time = datetime.strptime(real_finish_time, format_str)
                if real_finish_time < worker_order.creat_time:
                    return self.customError("实际完成时间不能小于工单录入时间")
                # 获取执行接收时间
                step_4_data = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.working_id == int(working_id),
                    SaleWorkerOrderStep.run_type == self.get_run_type_by_name("接单"),
                    SaleWorkerOrderStep.real_user.isnot(None),
                    SaleWorkerOrderStep.care_flag == 0
                ).order_by(SaleWorkerOrderStep.id.asc()).first()
                if real_finish_time < step_4_data.implement_time:
                    return self.customError("实际完成时间不能小于接单时间")
                files = self.request.files
                file_list = files.get('files')
                file_new_list = []
                max_file_size = 40 * 1024 * 1024  # 40MB限制
                if file_list:
                    for i in file_list:
                        data = i.get('body')
                        # file_size = len(data)
                        # if file_size > max_file_size:
                        #     return self.customError("文件大小超过40M限制")
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        new_filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', new_filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        file_new_list.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                # 更新step表
                worker_step.real_user = user_id
                worker_step.content = content
                if file_new_list:
                    worker_step.files = json.dumps(file_new_list)
                if solve_id:
                    worker_step.solve_id = int(solve_id)
                if remark:
                    worker_step.remark = remark
                worker_step.implement_time = timeUtils.getNewTimeStr()
                # 更新base表
                step_8_first_data = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.working_id == int(working_id),
                    SaleWorkerOrderStep.run_type == self.get_run_type_by_name("审核"),
                    SaleWorkerOrderStep.real_user.is_(None),
                    SaleWorkerOrderStep.id > int(step_id),
                ).order_by(SaleWorkerOrderStep.id.asc()).first()
                if not step_8_first_data.plan_user:
                    user_session.rollback()
                    return self.customError("工单节点错误")
                worker_order.run_type = step_8_first_data.run_type
                worker_order.run_user_id = step_8_first_data.plan_user

                worker_order.real_finish_time = real_finish_time
                if real_finish_time > worker_order.plan_finish_time:
                    worker_order.over_time = 1
                else:
                    worker_order.over_time = 0

                # 计算时间差
                time_diff = real_finish_time - step_4_data.implement_time
                # 将时间差转换为小时，保留两位小数
                hours_diff = time_diff.total_seconds() / 3600.0  # total_seconds()得到总秒数，然后转换为小时
                hours_diff_rounded = round(hours_diff, 2)  # 保留两位小数
                worker_order.handle_time = hours_diff_rounded
                worker_order.sys_finish_time = timeUtils.getNewTimeStr()
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif kt == 'Addsolution':  # 添加解决方法归类
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                solve_name = self.get_argument('solve_name', None)
                if not solve_name or not working_id or not step_id:
                    return self.customError("参数不完整")
                if not re.compile(r'^-?\d+$').match(working_id) or not re.compile(r'^-?\d+$').match(step_id):
                    return self.customError("参数错误")
                p = SaleWorkerOrderSolve(name=solve_name, worker_id=int(working_id), step_id=int(step_id),
                                         user_id=user_id,
                                         creat_time=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc(data={"solve_id": p.id}, info=None)
            elif kt == 'AuditOrder':  # 审核任务记录
                if not self.get_user_role_status("审核", user_id):
                    return self.customError("权限不足")
                working_id = self.get_argument('working_id', None)
                step_id = self.get_argument('step_id', None)
                content = self.get_argument('content', None)
                remark = self.get_argument('remark', None)  # 客户回访意见
                audit_type = self.get_argument('audit_type', None)  # 审核结果1通过 2驳回
                cc_users = self.get_argument('cc_users', None)  # 抄送人id列表
                if not working_id or not step_id or not audit_type:
                    return self.customError("参数不完整")
                if int(audit_type) == 2 and not content:
                    return self.customError("审核意见不能为空")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == int(working_id)).first()
                worker_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.id == int(step_id)).first()
                if not worker_order or not worker_step:
                    return self.customError("工单不存在")
                if worker_order.run_user_id != user_id or worker_step.plan_user != user_id:
                    return self.customError("权限不足")
                if worker_step.real_user or worker_step.check_flag:
                    return self.customError("工单节点错误")
                step_8_id = self.get_run_type_by_name("审核")
                if worker_order.run_type != step_8_id or worker_step.run_type != step_8_id:
                    return self.customError("工单节点错误")
                files = self.request.files
                file_list = files.get('files')
                file_new_list = []
                if file_list:
                    for i in file_list:
                        data = i.get('body')
                        filename = i.get('filename')
                        filename_data = os.path.splitext(filename)
                        new_filename = filename_data[0] + '--' + str(timeUtils.nowSecs()) + filename_data[1]
                        url = upload_file(data, 'rhyc', new_filename)
                        if not url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        file_new_list.append({
                            'file_key': str(uuid.uuid4()),
                            'filename': filename,
                            'url': url.split('?', 1)[0]
                        })
                worker_step.real_user = user_id
                worker_step.implement_time = timeUtils.getNewTimeStr()
                if file_new_list:
                    worker_step.files = json.dumps(file_new_list)
                if remark:
                    worker_step.remark = remark
                if content:
                    worker_step.content = content
                email_list = []
                if cc_users and user_id == 6:  # 只有审核人是吴晗 才会有此字段
                    ids = json.loads(cc_users)
                    # 查询抄送人邮箱
                    emails = user_session.query(User).filter(User.id.in_(ids)).all()
                    email_list = [email.email for email in emails if email.email]
                    worker_order.copy_user = cc_users
                    # if worker_order.copy_user:
                    #     copy_user_data = json.loads(worker_order.copy_user)
                    #     copy_user_data['end_check'] = ids
                    #     worker_order.copy_user = json.dumps(copy_user_data)
                    # else:
                    #     cc_user_data = {'end_check': ids}
                    #     worker_order.copy_user = json.dumps(cc_user_data)
                if int(audit_type) == 1:  # 审核通过
                    # 通过有两种情况 1、还存在审核未审核情况 2、没有审核未审核情况
                    step_8_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.check_flag.is_(None),
                        SaleWorkerOrderStep.run_type == self.get_run_type_by_name("审核"),
                        SaleWorkerOrderStep.id >= worker_step.id,
                        SaleWorkerOrderStep.plan_user != worker_step.plan_user).order_by(
                        SaleWorkerOrderStep.id.asc()).first()
                    worker_step.check_flag = 1
                    if step_8_data:
                        # 1、还存在审核未审核情况的情况：base节点负责人更新为下一个审核人、step表更新当前记录的real_user等字段
                        worker_order.run_user_id = step_8_data.plan_user
                        user_session.commit()
                    else:
                        # 2、没有审核未审核情况:base节点变更为完成、step表更新当前记录的real_user等字段
                        step_9_id = self.get_run_type_by_name("完成")
                        worker_order.run_type = step_9_id
                        user_session.commit()
                else:  # 驳回
                    # 两票审核驳回
                    worker_step.check_flag = 2
                    # 节点状态更新为两票文件上传 更新节点负责人：查询出最新的一条节点状态是两票文件上传的步骤信息，获取其预计执行人id
                    step_7_id = self.get_run_type_by_name("执行")
                    worker_order.run_type = step_7_id
                    the_last_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.real_user.isnot(None),
                        SaleWorkerOrderStep.run_type == step_7_id).order_by(
                        SaleWorkerOrderStep.id.desc()).first()
                    worker_order.run_user_id = the_last_data.plan_user
                    # 新增执行step记录、新增审核step记录 查询当前工单所有的审核记录以plan_user去重的结果
                    creat_time = timeUtils.getNewTimeStr()
                    self.add_step_by_run_type(working_id, "执行", worker_order.working_no, user_id,
                                              the_last_data.plan_user, {"creat_time": creat_time})
                    step_8_id = self.get_run_type_by_name("审核")
                    step_8_data = user_session.query(distinct(SaleWorkerOrderStep.plan_user)).filter(
                        SaleWorkerOrderStep.working_id == int(working_id),
                        SaleWorkerOrderStep.run_type == step_8_id
                    ).all()
                    for i in step_8_data:
                        plan_user = i[0]
                        self.add_step_by_run_type(working_id, "审核", worker_order.working_no, user_id,
                                                  plan_user, {"check_type": 1, "creat_time": creat_time})
                    user_session.commit()
                if email_list:
                    step_8_id = self.get_run_type_by_name("审核")
                    step_8_data = user_session.query(SaleWorkerOrderStep).filter(
                        SaleWorkerOrderStep.working_id == working_id,
                        SaleWorkerOrderStep.run_type == step_8_id).order_by(
                        SaleWorkerOrderStep.creat_time.desc(), SaleWorkerOrderStep.id.asc()).first()
                    if step_8_data.id == worker_step.id:
                        message = """
                        工单描述：%s
                        项目名称：%s
                        """

                        formatted_message = message % (worker_order.problem_descr, worker_order.project.name)
                        sendMail_(formatted_message, worker_order.working_no + "工单信息", "山海系统", "XXX",
                                  email_list)
                return self.returnTypeSuc(data="", info=None)
            elif kt == 'AddUserRole':  # 添加权限
                user_ids = self.get_argument('user_ids', None)
                run_types = self.get_argument('run_types', None)
                if not user_ids or not run_types:
                    return self.customError("参数不完整")
                if not self.can_be_parsed_as_list(user_ids) or not self.can_be_parsed_as_list(run_types):
                    return self.customError("参数不完整")
                data_to_insert = [
                    {
                        "creat_time": timeUtils.getNewTimeStr(),
                        "run_type": run_type,
                        "user_id": user_id
                    }
                    for user_id, run_type in product(json.loads(user_ids), json.loads(run_types))
                ]
                # 执行批量插入
                user_session.bulk_insert_mappings(RoleWorkerOrder, data_to_insert)
                user_session.commit()
                return self.returnTypeSuc(data="", info=None)
            elif kt == 'AbolishWorkerOrder':  # 废止工单
                working_id = self.get_argument('working_id', None)
                abolish_content = self.get_argument('abolish_content', None)
                if not working_id or not abolish_content:
                    return self.customError("参数不完整")
                worker_order = user_session.query(SaleWorkerOrderBase).filter(
                    SaleWorkerOrderBase.id == int(working_id)).first()
                if not worker_order:
                    return self.customError("工单不存在")
                if worker_order.run_type == self.get_run_type_by_name("废止"):
                    return self.customError("该工单已作废")
                if worker_order.run_type == self.get_run_type_by_name("完成"):
                    return self.customError("该工单已完成")
                # 查询当前user是否拥有预审权限
                current_step = user_session.query(SaleWorkerOrderStep).filter(
                    SaleWorkerOrderStep.working_id == working_id, SaleWorkerOrderStep.real_user.is_(None),
                    SaleWorkerOrderStep.run_type == worker_order.run_type).order_by(
                    SaleWorkerOrderStep.creat_time.desc(), SaleWorkerOrderStep.id.asc()).first()
                if not current_step:
                    if worker_order.run_type == self.get_run_type_by_name("修改"):
                        current_step = user_session.query(SaleWorkerOrderStep).filter(
                            SaleWorkerOrderStep.working_id == working_id, SaleWorkerOrderStep.run_type == worker_order.run_type).first()

                ys_status = self.get_user_role_status("预审", user_id)
                if (user_id == worker_order.create_user_id and worker_order.run_type == self.get_run_type_by_name("预审")) or ys_status:
                    worker_order.run_type = self.get_run_type_by_name("废止")
                    worker_order.sys_finish_time = timeUtils.getNewTimeStr()
                    current_step.real_user = user_id
                    current_step.implement_time = timeUtils.getNewTimeStr()
                    current_step.check_flag = 3
                    current_step.content = abolish_content
                    user_session.commit()
                else:
                    return self.customError("权限不足")
                return self.returnTypeSuc(data="", info=None)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @staticmethod
    def get_user_role_status(run_type_name, user_id):
        role_run_type = user_session.query(SaleWorkerOrderRunType).filter(
            SaleWorkerOrderRunType.name == run_type_name,
            SaleWorkerOrderRunType.is_use == 1, SaleWorkerOrderRunType.type == 1).first()
        if not role_run_type:
            return False
        role_nodes = user_session.query(RoleWorkerOrder).filter(RoleWorkerOrder.user_id == user_id,
                                                                RoleWorkerOrder.run_type == role_run_type.id).first()
        if not role_nodes:
            return False
        return True

    @staticmethod
    def get_run_type_by_name(run_type_name):
        role_run_type = user_session.query(SaleWorkerOrderRunType).filter(
            SaleWorkerOrderRunType.name == run_type_name,
            SaleWorkerOrderRunType.is_use == 1, SaleWorkerOrderRunType.type == 1).first()
        if not role_run_type:
            return False
        return role_run_type.id

    @staticmethod
    def is_valid_json(json_str):
        try:
            json.loads(json_str)
        except json.JSONDecodeError:
            return False
        return True

    @staticmethod
    def get_ys_user_id():
        ys_id = user_session.query(SaleWorkerOrderRunType).filter(
            SaleWorkerOrderRunType.name == "预审",
            SaleWorkerOrderRunType.is_use == 1, SaleWorkerOrderRunType.type == 1).first()
        if not ys_id:
            return False
        ys_user_id = user_session.query(RoleWorkerOrder).filter(
            RoleWorkerOrder.run_type == ys_id).first()
        return ys_user_id.user_id

    @staticmethod
    def add_step_by_run_type(working_id, run_type_name, working_no, create_user_id, plan_user, kwargs=None):
        if kwargs is None:
            kwargs = {}
        run_type_id = SaleWorkerOrderHanddleIntetface.get_run_type_by_name(run_type_name)
        # 基础信息 若有独特需要添加 则根据步骤名称增加kwargs的值
        new_data = {
            'creat_time': timeUtils.getNewTimeStr(),
            'working_no': working_no,
            'working_id': working_id,
            'create_user_id': create_user_id,
            'plan_user': plan_user,
            'run_type': run_type_id
        }
        merged_dict = {**new_data, **kwargs}
        order_step_xz = SaleWorkerOrderStep(**merged_dict)
        user_session.add(order_step_xz)
        return order_step_xz

    @staticmethod
    def can_be_parsed_as_list(json_str):
        try:
            result = json.loads(json_str)
            return isinstance(result, list)
        except json.JSONDecodeError:
            return False

    # 判断当前用户是否是此工单参与人
    @staticmethod
    def judge_user_participation(working_id, user_id, copy_user):
        # 查询当前用户是否是此工单参与人
        is_step_data = user_session.query(SaleWorkerOrderStep).filter(
            SaleWorkerOrderStep.working_id == working_id,
            or_(SaleWorkerOrderStep.plan_user == user_id, SaleWorkerOrderStep.real_user == user_id)
        ).first()
        # 查询当前人是否是此工单抄送人
        copy_user_ids = []
        if copy_user:
            copy_user_ids = json.loads(copy_user)
        if is_step_data or user_id in copy_user_ids:
            return True
        return False


    @staticmethod
    def get_user_all_workorder(user_id):
        # 查询出当前用户参与的工单
        work_orders = user_session.query(SaleWorkerOrderStep).filter(
            or_(SaleWorkerOrderStep.plan_user == user_id, SaleWorkerOrderStep.real_user == user_id)
        ).all()
        work_order_id_list = []
        if work_orders:
            work_order_id_list = list({work_order.working_id for work_order in work_orders})
        return work_order_id_list
