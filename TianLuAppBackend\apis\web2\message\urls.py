# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/4/1 14:08
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Project  : TianLuAppBackend
# @File     : urls.py
# @Software : PyCharm
from django.urls import path

from apis.web2.message import views

urlpatterns = [
    path('list/', views.MessageList.as_view()),     # 消息中心列表        en
    path('read/', views.MessageRead.as_view()),     # 标记已读      en
    path('read/all/', views.MessageRead.as_view()),     # 标记全部已读        en
    path('strategy/detail/', views.MessageStrategyDetail.as_view()),    # 抄送策略详情        en
    path('strategy/verify/', views.MessageFeedback.as_view()),     # 策略确认       en
    path('strategy/feedback/', views.MessageFeedback.as_view()),     # 策略反馈     en
    path('strategy/sendcopy/', views.MessageSendCopy.as_view()),     # 抄送项目校验       en
    path('mapping/', views.MessageMapping.as_view())    # 消息中心下拉框字典         en
]