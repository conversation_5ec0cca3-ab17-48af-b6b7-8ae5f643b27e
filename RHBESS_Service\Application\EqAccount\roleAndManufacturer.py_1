# -*- coding:utf-8 -*-
import json
import logging
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.role import Role
from Application.Models.User.manufacturer import Manufacturer 
from Application.Models.User.organization_type import OrganizationType
from Tools.Utils.num_utils import translate_text
from Tools.Utils.time_utils import timeUtils

class RoleAndManufacturerIntetface(BaseHandler):
    '''
    @description: 角色和厂商管理
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    # @tornado.web.authenticated
    def get(self, kt):
        # self.refreshSession()  # 刷新session
        try:
            if kt == 'GetAllRole':  # 获取所有角色
                data = []
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                ppageSize = int(self.get_argument('pageSize',20))
                lang = self.get_argument('lang', None)  # 英文网址
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,ppageSize:%s" %(descr, pageNum, ppageSize))
                if descr :
                    if lang == 'en':
                        total = user_session.query(func.count(Role.id)).filter(Role.en_descr.like('%' + descr + '%')).scalar()
                        all = user_session.query(Role).filter(Role.en_descr.like('%' + descr + '%')).order_by(Role.id.desc()).limit(ppageSize).offset((pageNum - 1) * ppageSize).all()
                    else:
                        total = user_session.query(func.count(Role.id)).filter(
                            Role.descr.like('%' + descr + '%')).scalar()
                        all = user_session.query(Role).filter(Role.descr.like('%' + descr + '%')).order_by(
                            Role.id.desc()).limit(ppageSize).offset((pageNum - 1) * ppageSize).all()
                else:
                    total = user_session.query(func.count(Role.id)).filter(Role.descr!='en').scalar()
                    all = user_session.query(Role).filter(Role.descr!='en').order_by(Role.id.asc()).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                for org in all:
                    d = eval(str(org))
                    if lang == 'en':
                        d['descr'] = d['en_descr']
                    d['station_id'] = eval(org.station_id)
                    data.append(d)
                return self.returnTotalSuc(data,total)
            
            elif kt == 'GetAllFacture':  # 获取所有厂商
                data = []
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                ppageSize = int(self.get_argument('pageSize',20))
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,ppageSize:%s"%(descr,pageNum,ppageSize))
                if descr :
                    total = user_session.query(func.count(Manufacturer.id)).filter(Manufacturer.descr.like('%' + descr + '%')).scalar()
                    all = user_session.query(Manufacturer).filter(Manufacturer.descr.like('%' + descr + '%')).order_by(Manufacturer.id.desc()
                    ).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                else:
                    total = user_session.query(func.count(Manufacturer.id)).scalar()
                    all = user_session.query(Manufacturer).order_by(Manufacturer.id.asc()).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                for org in all:
                    data.append(eval(str(org)))
                return self.returnTotalSuc(data,total)

            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close() 


    # @tornado.web.authenticated
    def post(self, kt):
        # self.refreshSession()  # 刷新session
        try:
        # if 1:
            if kt == 'DeleteRole':  # 角色及对应用户
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                org = user_session.query(Role).filter(Role.id==id).first()
                if not org:
                    return self.customError("id为空或无效")
                org.delete_role(id)
                user_session.commit()
                return self.returnTypeSuc('')
        
            elif kt == 'AddRole':  # 添加角色
                descr = self.get_argument('descr',None)
                station_id = self.get_argument('station_id',None)
                lang = self.get_argument('lang', None)  # 英文网址
                if DEBUG:
                    logging.info("descr:%s,station_id:%s"%(descr,station_id))
                if not descr:
                    if lang=='en':
                        return self.customError("Name is empty")
                    else:
                        return self.customError("名称为空")
                elif lang == 'en':
                    if user_session.query(Role).filter(Role.en_descr == descr).first():
                        return self.customError("Role already exists")
                elif user_session.query(Role).filter(Role.descr == descr).first():
                    return self.customError("角色已存在")

                else:
                    if lang == 'en':
                        org = Role(en_descr=descr, descr='en',station_id=station_id, authority='[]', select_key='["Home"]')
                        user_session.add(org)
                        user_session.commit()
                    else:
                        org = Role(descr=descr,station_id=station_id,authority='[]',select_key='["Home"]')
                        user_session.add(org)
                        user_session.commit()
                return self.returnTypeSuc('')
               
            elif kt == 'DeleteFacture':  # 删除厂商
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(Manufacturer).filter(Manufacturer.id==id).first()
                if not delete_apex:
                    return self.customError("id无效")
                delete_apex.delete_structure(id)
                user_session.commit()
                return self.returnTypeSuc('')
            
            elif kt == 'AddFacture':  # 添加厂商
                descr = self.get_argument('descr',None)
                if DEBUG:
                    logging.info("descr:%s"%(descr))
                if not descr:
                    return self.customError("名称为空")
                elif user_session.query(Manufacturer).filter(Manufacturer.descr == descr).first():
                    return self.customError("厂商已存在")
                else:
                    org = Manufacturer(descr=descr,op_ts=timeUtils.getNewTimeStr())
                    user_session.add(org)
                    user_session.commit()
                return self.returnTypeSuc('')
                
            elif kt == 'UpdateRole':  # 修改角色
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                station_id = self.get_argument('station_id',None)
                authority = self.get_argument('authority',None)
                select_key = self.get_argument('select_key',None)
                lang = self.get_argument('lang', None)  # 英文网址
                if DEBUG:
                    logging.info("id:%s,descr:%s,station_id:%s,authority:%s,select_key:%s"%(id,descr,station_id,authority,select_key))
                
                to_update = user_session.query(Role).filter(Role.id==id).first()
                
                if not to_update:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("id无效")
                if descr:
                    if lang=='en':
                        to_update.en_descr = descr
                    else:
                        to_update.descr = descr
                if authority:
                    authority = str(authority).replace('false', 'False').replace('true', 'True')
                    if lang == 'en':
                        to_update.en_authority = authority
                    else:
                        to_update.authority = authority
                if station_id:
                    if list(to_update.station_id) != list(station_id):
                        to_update.authority = '[]'
                    to_update.station_id = station_id
                # else:
                #     if authority:
                #         authority = str(authority).replace('false','False').replace('true','True')
                #         to_update.authority = authority
                if select_key:
                    to_update.select_key = select_key
                
                user_session.commit()
                return self.returnTypeSuc('')
              
            elif kt == 'UpdateFacture':  # 修改厂商
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                if DEBUG:
                    logging.info("id:%s,descr:%s"%(id,descr))
                to_update = user_session.query(Manufacturer).filter(Manufacturer.id==id).first()
            
                if not descr:
                    return self.customError("名称为空")
                if not to_update:
                    return self.customError("id无效")
                manufacturer = user_session.query(Manufacturer).filter(Manufacturer.descr == descr).first()
                if manufacturer and manufacturer.id != int(id):
                    return self.customError("厂商已存在")
                to_update.descr = descr
                user_session.commit()
                return self.returnTypeSuc('')
              
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

        # user_session.rollback()
        # user_session.close()

    
