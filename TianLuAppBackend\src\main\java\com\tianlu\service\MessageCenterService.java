package com.tianlu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tianlu.entity.MessageCenter;
import com.tianlu.vo.MessageCenterVO;

import java.time.LocalDateTime;

public interface MessageCenterService extends IService<MessageCenter> {
    
    /**
     * 获取消息详情
     * @param id 消息ID
     * @return 消息详情
     */
    MessageCenterVO getMessageDetail(Long id);

    /**
     * 更新消息状态
     * @param id 消息ID
     * @param isRead 是否已读
     * @param isHandle 是否处理
     * @param isVerify 是否确认
     */
    void updateMessageStatus(Long id, Integer isRead, Integer isHandle, Integer isVerify);

    /**
     * 更新消息反馈意见
     * @param id 消息ID
     * @param opinion 反馈意见
     * @param enOpinion 英文反馈意见
     */
    void updateMessageOpinion(Long id, String opinion, String enOpinion);

    /**
     * 推送告警消息到消息中心
     * @param alarmId 告警ID
     * @param userId 当前用户ID
     * @param projectName 项目名称
     * @param stationName 站点名称
     * @param alarmType 告警类型
     * @param alarmNote 告警备注
     * @param alarmDetails 告警详情
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 告警状态
     */
    void pushAlarmMessage(Long alarmId, Long userId, String projectName, String stationName,
                         Integer alarmType, String alarmNote, String alarmDetails,
                         LocalDateTime startTime, LocalDateTime endTime, Integer status);

    /**
     * 获取告警消息详情
     * @param messageId 消息ID
     * @return 告警消息详情
     */
    MessageCenterVO getAlarmMessageDetail(Long messageId);
} 