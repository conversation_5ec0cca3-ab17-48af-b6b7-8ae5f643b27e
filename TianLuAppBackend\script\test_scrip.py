import django
import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TianLuAppBackend.settings')
django.setup()
import requests
import datetime
from settings.meter_settings import METER_DIC
from tools.count import get_price
from apis.user import models


def number_insert():
    current_date = datetime.date.today()
    # current_date = datetime.date(year, month, day)

    dt = datetime.datetime.combine(current_date, datetime.time(hour=1))
    # 转换为时间戳（以秒为单位）
    timestamp = int(dt.timestamp())

    url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        # print("station:", station)
        meter_type = station.meter_type
        meter_dic = METER_DIC.get(meter_type)
        charge = meter_dic.get("charge")
        discharge = meter_dic.get("discharge")
        device = meter_dic.get("device")
        device_ = getattr(station, device)
        print(charge, discharge, device_)
        province_ins = station.province
        peak_ins = models.PeakValley.objects.filter(year_month=current_date.month, province=province_ins, level=station.level, type=station.type)
        # print("peak_ins", peak_ins)
        exists = models.StationIncome.objects.filter(station_id=station, income_date__day=current_date.day, income_date__month=current_date.month, income_date__year=current_date.year,
                                                     record=1)
        if not exists:
            power = station.rated_power
            # print("power", power)
            if str(power) == "100":
                CuCha_list = []
                CuDis_list = []
                request_json = {
                    "time": str(timestamp),
                    "datatype": "cumulant",
                    "app": station.app,
                    "station": station.english_name,
                    "body": [
                        {
                            "device": device_,
                            "body": [
                                charge,
                                discharge
                            ]
                        }
                    ]}
                print(request_json)
                response = requests.post(url=url, json=request_json)
                datas = response.json()['datas']
                if datas:
                    new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
                    new_list = new_list_[::2]

                    new_list.append(new_list_[-1])
                    for i in range(len(new_list) - 1):
                        price = get_price(station, current_date, i)
                        # print("自定义电价为:", price)
                        if not price:
                            price = float(peak_ins.values()[0][f"h{i}"])
                        CuDis_list.append(float(
                            new_list[i + 1]["body"]["data"][0][discharge]) * price - float(
                            new_list[i]["body"]["data"][0][discharge]) * price)

                        CuCha_list.append(float(
                            new_list[i + 1]["body"]["data"][0][charge]) * price - float(
                            new_list[i]["body"]["data"][0][charge]) * price)
                    total = sum(CuDis_list) - sum(CuCha_list)
                    print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元")
                    if total < 0:
                        total = 0
                        print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(station_id=station, income_date__day=current_date.day, income_date__month=current_date.month,
                                                                     income_date__year=current_date.year, record=2)
                    # print("100", income_ins)
                    if not income_ins:
                        # print("100 不存在手动录入 进行创建")
                        models.StationIncome.objects.create(peak_load_shifting=total, station_id=station, income_date=current_date, income_type=1, record=2)
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                    # print("定时任务数据库修改完成================")
                    # print("定时任务执行完成=========================")

            if str(power) == "200":
                CuCha_list = []
                CuDis_list = []
                request_json = {
                    "time": str(timestamp),
                    "datatype": "cumulant",
                    "app": station.app,
                    "station": station.english_name,
                    "body": [
                        {
                            "device": f"{device_}1",
                            "body": [
                                charge,
                                discharge
                            ]
                        },
                        {
                            "device": f"{device_}2",
                            "body": [
                                charge,
                                discharge
                            ]
                        }
                    ]}
                print(request_json)
                response = requests.post(url=url, json=request_json)
                datas = response.json()['datas']
                if datas:
                    new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
                    new_list = new_list_[::2]

                    new_list.append(new_list_[-1])
                    for i in range(len(new_list) - 1):
                        # print(i)
                        price = get_price(station, current_date, i)
                        if not price:
                            price = float(peak_ins.values()[0][f"h{i}"])
                        # print(f"h{i}", "电价为", price, "++++++++++++++++++++++++++++++")
                        # print(f"第{i}小时放电收益为{Cudis}")

                        CuDis_list.append(float(
                            new_list[i + 1]["body"]["data"][0][discharge]) * price - float(
                            new_list[i]["body"]["data"][0][discharge]) * price + float(
                            new_list[i + 1]["body"]["data"][1][discharge]) * price - float(
                            new_list[i]["body"]["data"][1][discharge]) * price)
                        # print(f"第{i}小时充电花费为{CuCha}")
                        # print(f"第{i}小时收益为{Cudis - CuCha}")

                        CuCha_list.append(float(
                            new_list[i + 1]["body"]["data"][0][charge]) * price - float(
                            new_list[i]["body"]["data"][0][charge]) * price + float(
                            new_list[i + 1]["body"]["data"][1][charge]) * price - float(
                            new_list[i]["body"]["data"][1][charge]) * price)
                    total = sum(CuDis_list) - sum(CuCha_list)
                    print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元")
                    if total < 0:
                        total = 0
                        print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(station_id=station,
                                                                     income_date__day=current_date.day,
                                                                     income_date__month=current_date.month,
                                                                     income_date__year=current_date.year, record=2)
                    if not income_ins:
                        models.StationIncome.objects.create(peak_load_shifting=total, station_id=station,
                                                            income_date=current_date, income_type=1, record=2)
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                    print("定时任务数据库修改完成================")
                    print("定时任务执行完成=========================")

            if str(power) == "300":
                CuCha_list = []
                CuDis_list = []
                request_json = {
                    "time": str(timestamp),
                    "datatype": "cumulant",
                    "app": station.app,
                    "station": station.english_name,
                    "body": [
                        {
                            "device": f"{device_}1",
                            "body": [
                                charge,
                                discharge
                            ]
                        },
                        {
                            "device": f"{device_}2",
                            "body": [
                                charge,
                                discharge
                            ]
                        },
                        {
                            "device": "BMS3",
                            "body": [
                                charge,
                                discharge
                            ]
                        }
                    ]}
                print(request_json)
                response = requests.post(url=url, json=request_json)
                datas = response.json()['datas']
                if datas:
                    new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
                    new_list = new_list_[::2]

                    new_list.append(new_list_[-1])
                    for i in range(len(new_list) - 1):
                        # print(i)
                        price = get_price(station, current_date, i)
                        if not price:
                            price = float(peak_ins.values()[0][f"h{i}"])
                        # print(f"h{i}", "电价为", price, "++++++++++++++++++++++++++++++")
                        # print(new_list[i + 1]["body"]["data"])
                        # print(f"第{i}小时放电收益为{Cudis}")

                        CuDis_list.append(float(
                            new_list[i + 1]["body"]["data"][0][discharge]) * price - float(
                            new_list[i]["body"]["data"][0][discharge]) * price + float(
                            new_list[i + 1]["body"]["data"][1][discharge]) * price - float(
                            new_list[i]["body"]["data"][1][discharge]) * price + float(
                            new_list[i + 1]["body"]["data"][2][discharge]) * price - float(
                            new_list[i]["body"]["data"][2][discharge]) * price)
                        # print(f"第{i}小时充电花费为{CuCha}")
                        # print(f"第{i}小时收益为{Cudis - CuCha}")

                        CuCha_list.append(float(
                            new_list[i + 1]["body"]["data"][0][charge]) * price - float(
                            new_list[i]["body"]["data"][0][charge]) * price + float(
                            new_list[i + 1]["body"]["data"][1][charge]) * price - float(
                            new_list[i]["body"]["data"][1][charge]) * price + float(
                            new_list[i + 1]["body"]["data"][2][charge]) * price - float(
                            new_list[i]["body"]["data"][2][charge]) * price)
                    total = sum(CuDis_list) - sum(CuCha_list)
                    print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元")
                    if total < 0:
                        total = 0
                        print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(station_id=station,
                                                                     income_date__day=current_date.day,
                                                                     income_date__month=current_date.month,
                                                                     income_date__year=current_date.year, record=2)
                    if not income_ins:
                        models.StationIncome.objects.create(peak_load_shifting=total, station_id=station,
                                                            income_date=current_date, income_type=1, record=2)
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                    print("定时任务数据库修改完成================")
                    print("定时任务执行完成=========================")
        else:
            print("已存在手动添加的收入 不进行自动添加=====================")


if __name__ == '__main__':
    number_insert()
