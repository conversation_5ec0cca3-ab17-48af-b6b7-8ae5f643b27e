#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-10-09 14:58:48
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\DB\taicang_bms_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-12-02 13:53:54


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 太仓项目历史库连接 mysql


TAICANG_HOSTNAME = model_config.get('mysql', "TAICANG_HOSTNAME")
TAICANG_PORT = model_config.get('mysql', "TAICANG_PORT")
TAICANG_DATABASE1 = model_config.get('mysql', "TAICANG_DATABASE1")
TAICANG_USERNAME = model_config.get('mysql', "TAICANG_USERNAME")
TAICANG_PASSWORD = model_config.get('mysql', "TAICANG_PASSWORD")

hisdb1='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TAICANG_USERNAME,
    TAICANG_PASSWORD,
    TAICANG_HOSTNAME,
    TAICANG_PORT,
    TAICANG_DATABASE1
)
taicang_engine1 = create_engine(hisdb1,echo=False,max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_taicang_session1 = scoped_session(sessionmaker(taicang_engine1,autoflush=True))
taicang_Base1 = declarative_base(taicang_engine1)
taicang_session1 = _taicang_session1()
