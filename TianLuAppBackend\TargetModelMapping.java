public enum TargetModelMapping {
    // Format: TARGET_ID_MODEL_ID(targetId, modelId)
    TARGET_12_MODEL_12(12, 12),
    TARGET_11_MODEL_13(11, 13),
    TARGET_3_MODEL_4(3, 4),
    TARGET_2_MODEL_1(2, 1),
    TARGET_2_MODEL_2(2, 2),
    TARGET_2_MODEL_3(2, 3),
    TARGET_1_MODEL_1(1, 1),
    TARGET_1_MODEL_2(1, 2),
    TARGET_1_MODEL_3(1, 3),
    TARGET_1_MODEL_11(1, 11);

    private final int targetId;
    private final int modelId;

    TargetModelMapping(int targetId, int modelId) {
        this.targetId = targetId;
        this.modelId = modelId;
    }

    public int getTargetId() {
        return targetId;
    }

    public int getModelId() {
        return modelId;
    }

    // Helper method to find mapping by targetId and modelId
    public static TargetModelMapping findByTargetAndModel(int targetId, int modelId) {
        for (TargetModelMapping mapping : values()) {
            if (mapping.targetId == targetId && mapping.modelId == modelId) {
                return mapping;
            }
        }
        return null;
    }

    // Helper method to get all modelIds for a given targetId
    public static int[] getModelIdsForTarget(int targetId) {
        return java.util.Arrays.stream(values())
                .filter(mapping -> mapping.targetId == targetId)
                .mapToInt(TargetModelMapping::getModelId)
                .toArray();
    }
} 