# EN+DEV组合分组功能使用指南

## 功能概述

EN+DEV组合分组功能可以根据SQL查询结果中的 `en` 和 `dev` 字段进行分组，将数据按照 `en+dev` 的组合进行归类，构造出以组合键为主键的时间序列数据结构。

### 数据结构

**输入数据格式：**
```sql
time                | station | en   | dev  | vol   | cur    | soc  | soh
2025-06-01 00:00:00 | station1| EN01 | BMS1 | 750.1 | -100.2 | 50.5 | 95.0
2025-06-01 00:05:00 | station1| EN01 | BMS1 | 750.3 | -100.5 | 50.7 | 95.1
2025-06-01 00:00:00 | station1| EN01 | BMS2 | 800.1 | -120.2 | 55.5 | 96.0
2025-06-01 00:00:00 | station1| EN02 | BMS1 | 850.1 | -150.2 | 60.5 | 97.0
```

**输出数据结构：**
```java
Map<String, Map<String, Object>> result = {
    "EN01+BMS1": {
        "vol": {
            "2025-06-01 00:00:00": 750.1,
            "2025-06-01 00:05:00": 750.3
        },
        "cur": {
            "2025-06-01 00:00:00": -100.2,
            "2025-06-01 00:05:00": -100.5
        },
        "soc": {
            "2025-06-01 00:00:00": 50.5,
            "2025-06-01 00:05:00": 50.7
        }
    },
    "EN01+BMS2": {
        "vol": {
            "2025-06-01 00:00:00": 800.1
        },
        // ... 其他字段
    },
    "EN02+BMS1": {
        "vol": {
            "2025-06-01 00:00:00": 850.1
        },
        // ... 其他字段
    }
}
```

## 核心方法

### 1. TimeSeriesDataService

#### 基础分组方法

```java
// 基础分组（不过滤）
Map<String, Map<String, Object>> getDataGroupedByEnAndDev(List<Map<String, Object>> sqlResults)

// 带15分钟过滤的分组
Map<String, Map<String, Object>> getDataGroupedByEnAndDev(List<Map<String, Object>> sqlResults, boolean filter15Minutes)
```

#### 统计信息方法

```java
// 获取分组统计信息
Map<String, Object> getGroupedDataStatistics(List<Map<String, Object>> sqlResults, boolean filter15Minutes)
```

#### 辅助方法

```java
// 获取EN+DEV组合键列表
List<String> getEnDevCombinationKeys(List<Map<String, Object>> sqlResults)

// 获取指定组合的数据
Map<String, Map<String, Object>> getDataByEnDevKey(List<Map<String, Object>> sqlResults, String enDevKey, boolean filter15Minutes)

// 获取指定EN和DEV的数据
Map<String, Map<String, Object>> getDataByEnAndDev(List<Map<String, Object>> sqlResults, String enValue, String devValue, boolean filter15Minutes)
```

## 使用示例

### 1. 基础使用

```java
@Autowired
private TimeSeriesDataService timeSeriesDataService;

// 从数据库查询原始数据
List<Map<String, Object>> sqlResults = queryFromDatabase();

// 按EN+DEV组合分组
Map<String, Map<String, Object>> groupedData = 
    timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults);

// 遍历分组结果
for (Map.Entry<String, Map<String, Object>> entry : groupedData.entrySet()) {
    String enDevKey = entry.getKey();  // 例如："EN01+BMS1"
    Map<String, Object> fieldData = entry.getValue();
    
    System.out.println("组合: " + enDevKey);
    for (Map.Entry<String, Object> fieldEntry : fieldData.entrySet()) {
        String fieldName = fieldEntry.getKey();  // 例如："vol"
        Map<String, Object> timeValues = (Map<String, Object>) fieldEntry.getValue();
        System.out.println("  字段 " + fieldName + ": " + timeValues.size() + " 个数据点");
    }
}
```

### 2. 带15分钟过滤的使用

```java
// 启用15分钟过滤的分组
Map<String, Map<String, Object>> filteredGroupedData = 
    timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, true);

// 获取统计信息
Map<String, Object> statistics = 
    timeSeriesDataService.getGroupedDataStatistics(sqlResults, true);

System.out.println("总组合数: " + statistics.get("totalCombinations"));
System.out.println("唯一EN数: " + statistics.get("uniqueEnCount"));
System.out.println("唯一DEV数: " + statistics.get("uniqueDevCount"));
```

### 3. 获取指定组合的数据

```java
// 方式1：通过组合键获取
String targetKey = "EN01+BMS1";
Map<String, Map<String, Object>> specificData = 
    timeSeriesDataService.getDataByEnDevKey(sqlResults, targetKey, false);

// 方式2：通过EN和DEV分别指定
Map<String, Map<String, Object>> specificData2 = 
    timeSeriesDataService.getDataByEnAndDev(sqlResults, "EN01", "BMS1", false);

// 两种方式结果相同
```

### 4. 获取组合键列表

```java
// 获取所有EN+DEV组合键
List<String> combinationKeys = timeSeriesDataService.getEnDevCombinationKeys(sqlResults);
System.out.println("所有组合键: " + combinationKeys);
// 输出：[EN01+BMS1, EN01+BMS2, EN02+BMS1, ...]

// 获取兼容格式的组合列表
List<Map<String, String>> combinations = timeSeriesDataService.getEnDevCombinations(sqlResults);
for (Map<String, String> combination : combinations) {
    System.out.println("EN: " + combination.get("en") + 
                      ", DEV: " + combination.get("dev") + 
                      ", 组合键: " + combination.get("enDevKey"));
}
```

## API接口使用

### 1. 获取分组数据

```bash
# 基础分组数据
GET /api/monitoring/grouped-data?stationId=station1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59

# 启用15分钟过滤的分组数据
GET /api/monitoring/grouped-data?stationId=station1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59&filter15Minutes=true
```

**响应格式：**
```json
{
  "success": true,
  "data": {
    "groupedData": {
      "EN01+BMS1": {
        "vol": {
          "2025-06-01 00:00:00": 750.1,
          "2025-06-01 00:15:00": 750.3
        },
        "cur": {
          "2025-06-01 00:00:00": -100.2,
          "2025-06-01 00:15:00": -100.5
        }
      }
    },
    "statistics": {
      "totalCombinations": 9,
      "uniqueEnCount": 3,
      "uniqueDevCount": 3
    },
    "combinationKeys": ["EN01+BMS1", "EN01+BMS2", "EN02+BMS1"]
  },
  "message": "en+dev组合分组数据获取成功"
}
```

### 2. 获取指定组合的数据

```bash
# 通过组合键获取
GET /api/monitoring/en-dev-data?stationId=station1&enDevKey=EN01+BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59

# 通过EN和DEV分别指定（兼容接口）
GET /api/monitoring/en-dev-data-legacy?stationId=station1&en=EN01&dev=BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59
```

## 业务场景应用

### 1. 设备监控面板

```java
// 获取所有设备组合的最新状态
Map<String, Map<String, Object>> allDeviceData = 
    timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, true);

// 为每个设备组合构建监控卡片
for (Map.Entry<String, Map<String, Object>> entry : allDeviceData.entrySet()) {
    String deviceKey = entry.getKey();
    Map<String, Object> deviceFields = entry.getValue();
    
    // 构建设备监控卡片数据
    DeviceCard card = new DeviceCard();
    card.setDeviceKey(deviceKey);
    card.setVoltage(getLatestValue(deviceFields, "vol"));
    card.setCurrent(getLatestValue(deviceFields, "cur"));
    card.setSoc(getLatestValue(deviceFields, "soc"));
    card.setSoh(getLatestValue(deviceFields, "soh"));
    
    deviceCards.add(card);
}
```

### 2. 设备对比分析

```java
// 比较不同设备的同一指标
String compareField = "vol";
Map<String, Double> deviceAverages = new HashMap<>();

Map<String, Map<String, Object>> groupedData = 
    timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, false);

for (Map.Entry<String, Map<String, Object>> entry : groupedData.entrySet()) {
    String deviceKey = entry.getKey();
    Map<String, Object> fieldData = entry.getValue();
    
    if (fieldData.containsKey(compareField)) {
        Map<String, Object> timeValues = (Map<String, Object>) fieldData.get(compareField);
        double average = calculateAverage(timeValues);
        deviceAverages.put(deviceKey, average);
    }
}

// 输出对比结果
deviceAverages.forEach((device, avg) -> {
    System.out.println(device + " 的 " + compareField + " 平均值: " + avg);
});
```

### 3. 异常设备检测

```java
// 检测异常设备
List<String> abnormalDevices = new ArrayList<>();

Map<String, Map<String, Object>> groupedData = 
    timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, false);

for (Map.Entry<String, Map<String, Object>> entry : groupedData.entrySet()) {
    String deviceKey = entry.getKey();
    Map<String, Object> fieldData = entry.getValue();
    
    // 检查SOC是否异常
    if (fieldData.containsKey("soc")) {
        Map<String, Object> socValues = (Map<String, Object>) fieldData.get("soc");
        boolean hasAbnormalSoc = socValues.values().stream()
            .filter(Objects::nonNull)
            .mapToDouble(v -> Double.parseDouble(v.toString()))
            .anyMatch(soc -> soc < 10 || soc > 90);
        
        if (hasAbnormalSoc) {
            abnormalDevices.add(deviceKey);
        }
    }
}

System.out.println("异常设备列表: " + abnormalDevices);
```

## 性能优化

### 1. 自动过滤策略

```java
public Map<String, Object> getOptimizedGroupedData(List<Map<String, Object>> sqlResults) {
    // 根据数据量自动决定是否启用15分钟过滤
    boolean autoFilter = sqlResults.size() > 1000;
    
    Map<String, Map<String, Object>> groupedData = 
        timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, autoFilter);
    
    Map<String, Object> result = new HashMap<>();
    result.put("groupedData", groupedData);
    result.put("filterEnabled", autoFilter);
    
    if (autoFilter) {
        result.put("optimizationNote", "数据量较大，已自动启用15分钟间隔采样");
    }
    
    return result;
}
```

### 2. 分页处理

```java
// 对于大量设备组合，可以分页处理
public Map<String, Object> getGroupedDataWithPagination(
        List<Map<String, Object>> sqlResults, int page, int size) {
    
    // 获取所有组合键
    List<String> allKeys = timeSeriesDataService.getEnDevCombinationKeys(sqlResults);
    
    // 分页处理
    int start = page * size;
    int end = Math.min(start + size, allKeys.size());
    List<String> pageKeys = allKeys.subList(start, end);
    
    // 只获取当前页的数据
    Map<String, Map<String, Object>> allGroupedData = 
        timeSeriesDataService.getDataGroupedByEnAndDev(sqlResults, true);
    
    Map<String, Map<String, Object>> pageData = new HashMap<>();
    for (String key : pageKeys) {
        if (allGroupedData.containsKey(key)) {
            pageData.put(key, allGroupedData.get(key));
        }
    }
    
    return Map.of(
        "data", pageData,
        "pagination", Map.of(
            "page", page,
            "size", size,
            "total", allKeys.size(),
            "totalPages", (allKeys.size() + size - 1) / size
        )
    );
}
```

## 注意事项

1. **组合键格式**：组合键使用 `+` 连接EN和DEV，格式为 `en+dev`
2. **字段过滤**：自动排除 `time`、`en`、`dev` 字段，只处理数据字段
3. **空值处理**：跳过EN、DEV或时间为空的数据行
4. **15分钟过滤**：与时间序列转换功能兼容，支持15分钟间隔过滤
5. **内存使用**：大数据量时建议启用15分钟过滤或分页处理

## 总结

EN+DEV组合分组功能提供了灵活的设备数据分组能力，支持：
- 按设备组合自动分组
- 15分钟间隔过滤优化
- 丰富的统计信息
- 多种数据获取方式
- 完整的API接口支持

这个功能特别适用于多设备监控、设备对比分析、异常检测等业务场景。
