# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/4/9 14:32
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : test4.py
# @Software : PyCharm
import subprocess
import time
import traceback

from docx import Document, shared
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_ALIGN_VERTICAL

import os
# import comtypes.client


def generate_word_with_table(return_dic, out_path, lang='zh'):


    # 创建一个新的 Word 文档对象
    doc = Document()

    # 设置纸张方向为横向
    section = doc.sections[0]
    new_width, new_height = section.page_height, section.page_width
    section.page_width = new_width
    section.page_height = new_height

    # 添加标题一
    if lang == 'zh':
        title = doc.add_heading(f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单", level=1)
    else:
        title = doc.add_heading(f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet", level=1)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 添加标题二
    if lang == 'zh':
        doc.add_heading(f"一、{return_dic['account_month']}--{return_dic['project']}--削峰填谷收益", level=2)
    else:
        doc.add_heading(f"一、{return_dic['account_month']}--{return_dic['project']}--Peak Shaving Profit", level=2)

    station_count = len(return_dic['stations_info'])

    rows = 5 + station_count * 11 + 2
    cols = 10

    # 添加表格
    table = doc.add_table(rows=rows, cols=cols)  # 创建一个m行n列的表格，根据电站数量动态生成表格

    # 合并表格的第一行的所有单元格
    hdr_cells = table.rows[0].cells
    for i in range(1, cols):
        hdr_cells[0].merge(hdr_cells[i])
    # hdr_cells[0].merge(hdr_cells[1])
    # hdr_cells[0].merge(hdr_cells[2])
    hdr_cells[0].text = '天禄智控运营管理系统' if lang == 'zh' else 'C&I ESS Operation Online Portal'

    # 合并表格的第二行的单元格
    hdr_cells_1 = table.rows[1].cells
    for i in range(2, 6):
        hdr_cells_1[1].merge(hdr_cells_1[i])
    hdr_cells_1[0].text = '项目名称' if lang == 'zh' else 'Project'
    hdr_cells_1[6].text = '结算月份' if lang == 'zh' else 'Month'
    for i in range(8, 10):
        hdr_cells_1[7].merge(hdr_cells_1[i])

    hdr_cells_1[1].text = return_dic['project']
    hdr_cells_1[7].text = return_dic['account_month']

    # 合并表格的第三行的单元格
    hdr_cells_2 = table.rows[2].cells
    for i in range(2, 6):
        hdr_cells_2[1].merge(hdr_cells_2[i])
    hdr_cells_2[0].text = '电站地址' if lang == 'zh' else 'Station Address'
    hdr_cells_2[6].text = '结算开始时间'  if lang == 'zh' else 'From'
    for i in range(8, 10):
        hdr_cells_2[7].merge(hdr_cells_2[i])

    hdr_cells_2[1].text = return_dic['address'] if return_dic['address'] else '--'
    hdr_cells_2[7].text = return_dic['account_start']

    # 合并表格的第四行的单元格
    hdr_cells_3 = table.rows[3].cells
    for i in range(2, 6):
        hdr_cells_3[1].merge(hdr_cells_3[i])
    hdr_cells_3[0].text = '电站编号' if lang == 'zh' else 'Station ID'
    hdr_cells_3[6].text = '结算结束时间' if lang == 'zh' else 'To'
    for i in range(8, 10):
        hdr_cells_3[7].merge(hdr_cells_3[i])

    hdr_cells_3[1].text = return_dic['station_number']
    hdr_cells_3[7].text = return_dic['account_end']

    # 表格的第五行的单元格
    hdr_cells_4 = table.rows[4].cells
    if lang == 'zh':
        hdr_cells_4[0].text = '并网点名称'
        hdr_cells_4[1].text = '电表编号'
        hdr_cells_4[2].text = '计量类型'
        hdr_cells_4[3].text = '峰谷标识'
        hdr_cells_4[4].text = '电表倍率'
        hdr_cells_4[5].text = '上次抄表底码'
        hdr_cells_4[6].text = '本次抄表底码'
        hdr_cells_4[7].text = '结算电量 (kWh)'
        hdr_cells_4[8].text = '结算电价 (元/kWh)'
        hdr_cells_4[9].text = '结算金额 (元)'
    else:
        hdr_cells_4[0].text = 'Installation'
        hdr_cells_4[1].text = 'Meter ID'
        hdr_cells_4[2].text = 'Metering Type'
        hdr_cells_4[3].text = 'Identification'
        hdr_cells_4[4].text = 'Multiplier'
        hdr_cells_4[5].text = 'Last Reading'
        hdr_cells_4[6].text = 'Current Reading'
        hdr_cells_4[7].text = 'Settlement Energy(kWh)'
        hdr_cells_4[8].text = 'Settlement Price(￥/kWh)'
        hdr_cells_4[9].text = 'Settlement Amount(￥)'

    m = 10
    for index, station in enumerate(sorted(return_dic['stations_info'], key=lambda x: x['station_name'])):
        n = index

        # 合并第1列的第6行到第16行------并网点名称
        cell_1 = table.cell(5 + n + n * m, 0)
        cell_1.text = station['station_name']
        for j in range(5 + n + n * m + 1, 5 + n + n * m + m + 1):
            cell_j = table.cell(j, 0)
            cell_1.merge(cell_j)

        # 合并第2列的第6行到第16行------电表编号
        cell_1 = table.cell(5 + n + n * m, 1)
        cell_1.text = station['station_number']
        for j in range(5 + n + n * m + 1, 5 + n + n * m + m + 1):
            cell_j = table.cell(j, 1)
            cell_1.merge(cell_j)

        # 合并第3列的第6行到第10行-----计量类型--充电
        cell_1 = table.cell(5 + n + n * m, 2)
        cell_1.text = '反向有功（充电）' if lang == 'zh' else 'Charging'
        for j in range(5 + n + n * m + 1, 5 + n + n * m + 5):
            cell_j = table.cell(j, 2)
            cell_1.merge(cell_j)
        # 合并第3列的第11行到第15行-----计量类型--放电
        cell_1 = table.cell(5 + n + n * m + 4, 2)
        cell_1.text = '正向有功（放电）' if lang == 'zh' else 'Discharging'
        for j in range(5 + n + n * m + 4 + 2, 5 + n + n * m + 4 + 6):
            cell_j = table.cell(j, 2)
            cell_1.merge(cell_j)

        if lang == 'zh':
            table.cell(5 + n + n * m + 0, 3).text = '尖'
            table.cell(5 + n + n * m + 1, 3).text = '峰'
            table.cell(5 + n + n * m + 2, 3).text = '平'
            table.cell(5 + n + n * m + 3, 3).text = '谷'
            table.cell(5 + n + n * m + 4, 3).text = '深谷'
            table.cell(5 + n + n * m + 5, 3).text = '尖'
            table.cell(5 + n + n * m + 6, 3).text = '峰'
            table.cell(5 + n + n * m + 7, 3).text = '平'
            table.cell(5 + n + n * m + 8, 3).text = '谷'
            table.cell(5 + n + n * m + 9, 3).text = '深谷'
        else:
            table.cell(5 + n + n * m + 0, 3).text = 'Rush-hour'
            table.cell(5 + n + n * m + 1, 3).text = 'Peak-hour'
            table.cell(5 + n + n * m + 2, 3).text = 'Flat-hour'
            table.cell(5 + n + n * m + 3, 3).text = 'Valley-hour'
            table.cell(5 + n + n * m + 4, 3).text = 'dValley-hour'
            table.cell(5 + n + n * m + 5, 3).text = 'Rush-hour'
            table.cell(5 + n + n * m + 6, 3).text = 'Peak-hour'
            table.cell(5 + n + n * m + 7, 3).text = 'Flat-hour'
            table.cell(5 + n + n * m + 8, 3).text = 'Valley-hour'
            table.cell(5 + n + n * m + 9, 3).text = 'dValley-hour'


        # 合并第5列的第6行到第15行-----电表倍率
        cell_1 = table.cell(5 + n + n * m, 4)
        cell_1.text = str(station['rate'])
        for j in range(5 + n + n * m + 1, 5 + n + n * m + m):
            cell_j = table.cell(j, 4)
            cell_1.merge(cell_j)

        table.cell(5 + n + n * m + 0, 5).text = str(station['account_data']['charge']['spike']['first'])
        table.cell(5 + n + n * m + 1, 5).text = str(station['account_data']['charge']['peak']['first'])
        table.cell(5 + n + n * m + 2, 5).text = str(station['account_data']['charge']['flat']['first'])
        table.cell(5 + n + n * m + 3, 5).text = str(station['account_data']['charge']['valley']['first'])
        table.cell(5 + n + n * m + 4, 5).text = str(station['account_data']['charge']['dvalley']['first'])
        table.cell(5 + n + n * m + 5, 5).text = str(station['account_data']['discharge']['spike']['first'])
        table.cell(5 + n + n * m + 6, 5).text = str(station['account_data']['discharge']['peak']['first'])
        table.cell(5 + n + n * m + 7, 5).text = str(station['account_data']['discharge']['flat']['first'])
        table.cell(5 + n + n * m + 8, 5).text = str(station['account_data']['discharge']['valley']['first'])
        table.cell(5 + n + n * m + 9, 5).text = str(station['account_data']['discharge']['dvalley']['first'])

        table.cell(5 + n + n * m + 0, 6).text = str(station['account_data']['charge']['spike']['last'])
        table.cell(5 + n + n * m + 1, 6).text = str(station['account_data']['charge']['peak']['last'])
        table.cell(5 + n + n * m + 2, 6).text = str(station['account_data']['charge']['flat']['last'])
        table.cell(5 + n + n * m + 3, 6).text = str(station['account_data']['charge']['valley']['last'])
        table.cell(5 + n + n * m + 4, 6).text = str(station['account_data']['charge']['dvalley']['last'])
        table.cell(5 + n + n * m + 5, 6).text = str(station['account_data']['discharge']['spike']['last'])
        table.cell(5 + n + n * m + 6, 6).text = str(station['account_data']['discharge']['peak']['last'])
        table.cell(5 + n + n * m + 7, 6).text = str(station['account_data']['discharge']['flat']['last'])
        table.cell(5 + n + n * m + 8, 6).text = str(station['account_data']['discharge']['valley']['last'])
        table.cell(5 + n + n * m + 9, 6).text = str(station['account_data']['discharge']['dvalley']['last'])

        table.cell(5 + n + n * m + 0, 7).text = str(station['account_data']['charge']['spike']['count'])
        table.cell(5 + n + n * m + 1, 7).text = str(station['account_data']['charge']['peak']['count'])
        table.cell(5 + n + n * m + 2, 7).text = str(station['account_data']['charge']['flat']['count'])
        table.cell(5 + n + n * m + 3, 7).text = str(station['account_data']['charge']['valley']['count'])
        table.cell(5 + n + n * m + 4, 7).text = str(station['account_data']['charge']['dvalley']['count'])
        table.cell(5 + n + n * m + 5, 7).text = str(station['account_data']['discharge']['spike']['count'])
        table.cell(5 + n + n * m + 6, 7).text = str(station['account_data']['discharge']['peak']['count'])
        table.cell(5 + n + n * m + 7, 7).text = str(station['account_data']['discharge']['flat']['count'])
        table.cell(5 + n + n * m + 8, 7).text = str(station['account_data']['discharge']['valley']['count'])
        table.cell(5 + n + n * m + 9, 7).text = str(station['account_data']['discharge']['dvalley']['count'])

        table.cell(5 + n + n * m + 0, 9).text = str(station['account_data']['charge']['spike']['income'])
        table.cell(5 + n + n * m + 1, 9).text = str(station['account_data']['charge']['peak']['income'])
        table.cell(5 + n + n * m + 2, 9).text = str(station['account_data']['charge']['flat']['income'])
        table.cell(5 + n + n * m + 3, 9).text = str(station['account_data']['charge']['valley']['income'])
        table.cell(5 + n + n * m + 4, 9).text = str(station['account_data']['charge']['dvalley']['income'])
        table.cell(5 + n + n * m + 5, 9).text = str(station['account_data']['discharge']['spike']['income'])
        table.cell(5 + n + n * m + 6, 9).text = str(station['account_data']['discharge']['peak']['income'])
        table.cell(5 + n + n * m + 7, 9).text = str(station['account_data']['discharge']['flat']['income'])
        table.cell(5 + n + n * m + 8, 9).text = str(station['account_data']['discharge']['valley']['income'])
        table.cell(5 + n + n * m + 9, 9).text = str(station['account_data']['discharge']['dvalley']['income'])

        # 合并第9列的第6行到第15行-----结算电价
        cell_1 = table.cell(5 + n + n * m, 8)
        cell_1.text = str(station['account_data']['price_url'])
        for j in range(5 + n + n * m + 1, 5 + n + n * m + m):
            cell_j = table.cell(j, 8)
            cell_1.merge(cell_j)

        # 合并表格的第14行的单元格
        hdr_cells_last = table.rows[5 + n + n * m + 4 + 4 + 2].cells
        for i in range(3, 5):
            hdr_cells_last[2].merge(hdr_cells_last[i])
        hdr_cells_last[2].text = '削峰填谷收益' if lang == 'zh' else 'Peak Shaving Profit'
        for i in range(6, 9):
            hdr_cells_last[5].merge(hdr_cells_last[i])

        hdr_cells_last[5].text = str(station['account_data']['total_income'])
        hdr_cells_last[9].text = '元' if lang == 'zh' else 'Yuan'

    # 合并表格的第15行的单元格
    hdr_cells_last = table.rows[5 + n + n * m + 4 + 4 + 1 + 2].cells
    for i in range(1, 5):
        hdr_cells_last[0].merge(hdr_cells_last[i])
    hdr_cells_last[0].text = '智能电站削峰填谷总收益' if lang == 'zh' else 'Amount for Peak Shaving'
    for i in range(6, 9):
        hdr_cells_last[5].merge(hdr_cells_last[i])

    hdr_cells_last[5].text = str(return_dic['project_total_income'])
    hdr_cells_last[9].text = '元' if lang == 'zh' else 'Yuan'

    # 表格底部说明
    hdr_cells_last_ = table.rows[5 + n + n * m + 4 + 4 + 2 + 2].cells
    for i in range(1, 10):
        hdr_cells_last_[0].merge(hdr_cells_last_[i])
    hdr_cells_last_[0].text = return_dic['note']

    # 设置每个单元格居中对齐（水平+垂直）,并设置边框线
    for row in table.rows[:-1]:
        for cell in row.cells:
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER

    # 设置单元格边框宽度
    table.style = 'Table Grid'

    # 插入3个空行--分割
    for _ in range(2):
        doc.add_paragraph()


    # 添加标题二
    if lang == 'zh':
        doc.add_heading('二、每日充放电量-详情', level=2)
    else:
        doc.add_heading('II. Daily Charge/Discharge', level=2)

    # 尖峰平谷每日充放电量
    for index, station_info in enumerate(sorted(return_dic['stations_info'], key=lambda x: x['station_name'])):

        # 添加标题三
        doc.add_heading(f"{index + 1}、{station_info['station_name']}", level=3)

        rows = 2 + len(station_info['days_data'])
        cols = 14

        # 添加表格
        table = doc.add_table(rows=rows, cols=cols)  # 新创建一个m行n列的表格，根据电站数量动态生成表格

        # 合并第1列的第1行到第2行-----并网点名称
        cell_1 = table.cell(0, 0)
        cell_1.text = '并网点名称' if lang == 'zh' else 'Installation'
        cell_2 = table.cell(1, 0)
        cell_1.merge(cell_2)

        # 合并第2列的第1行到第2行-----日期
        cell_1 = table.cell(0, 1)
        cell_1.text = '日期' if lang == 'zh' else 'Date'
        cell_2 = table.cell(1, 1)
        cell_1.merge(cell_2)

        # 合并表格的第一行的所有单元格-----反向有功电量
        hdr_cells = table.rows[0].cells
        for i in range(3, 8):
            hdr_cells[2].merge(hdr_cells[i])
        hdr_cells[2].text = '反向有功电量（kWh）' if lang == 'zh' else 'Charging'

        # 合并表格的第一行的所有单元格-----正向有功电量
        for i in range(9, 14):
            hdr_cells[8].merge(hdr_cells[i])
        hdr_cells[8].text = '正向有功电量（kWh）' if lang == 'zh' else 'Discharging'

        hdr_cells_2 = table.rows[1].cells
        if lang == 'zh':
            hdr_cells_2[2].text = '尖'
            hdr_cells_2[3].text = '峰'
            hdr_cells_2[4].text = '平'
            hdr_cells_2[5].text = '谷'
            hdr_cells_2[6].text = '深谷'
            hdr_cells_2[7].text = '总'
            hdr_cells_2[8].text = '尖'
            hdr_cells_2[9].text = '峰'
            hdr_cells_2[10].text = '平'
            hdr_cells_2[11].text = '谷'
            hdr_cells_2[12].text = '深谷'
            hdr_cells_2[13].text = '总'
        else:
            hdr_cells_2[2].text = 'Rush-hour'
            hdr_cells_2[3].text = 'Peak-hour'
            hdr_cells_2[4].text = 'Flat-hour'
            hdr_cells_2[5].text = 'Valley-hour'
            hdr_cells_2[6].text = 'dValley-hour'
            hdr_cells_2[7].text = 'Total'
            hdr_cells_2[8].text = 'Rush-hour'
            hdr_cells_2[9].text = 'Peak-hour'
            hdr_cells_2[10].text = 'Flat-hour'
            hdr_cells_2[11].text = 'Valley-hour'
            hdr_cells_2[12].text = 'dValley-hour'
            hdr_cells_2[13].text = 'Total'

        for i, day_data in enumerate(station_info['days_data']):
            # 合并第1列的第1行到第2行-----日期
            cells = table.rows[2+i].cells
            cells[0].text = station_info['station_name']
            cells[1].text = day_data['date']
            cells[2].text = str(day_data['day_spike_charge'])
            cells[3].text = str(day_data['day_peak_charge'])
            cells[4].text = str(day_data['day_flat_charge'])
            cells[5].text = str(day_data['day_valley_charge'])
            cells[6].text = str(day_data['day_dvalley_charge'])
            cells[7].text = str(day_data['day_total_charge'])
            cells[8].text = str(day_data['day_spike_discharge'])
            cells[9].text = str(day_data['day_peak_discharge'])
            cells[10].text = str(day_data['day_flat_discharge'])
            cells[11].text = str(day_data['day_valley_discharge'])
            cells[12].text = str(day_data['day_dvalley_discharge'])
            cells[13].text = str(day_data['day_total_discharge'])

        # 设置每个单元格居中对齐（水平+垂直）,并设置边框线
        for row in table.rows:
            for cell in row.cells:
                paragraph = cell.paragraphs[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER

        # 设置单元格边框宽度
        table.style = 'Table Grid'

        # 插入3个空行
        for _ in range(2):
            doc.add_paragraph()


    # 每日电价明细
    # 添加标题二
    # doc.add_heading(f"三、{return_dic['stations_info'][0]['account_data']['price_url']}", level=2)

    if len(return_dic['price_infos']):
        if lang == 'zh':
            doc.add_heading(f"三、每日购电价格详情", level=2)
        else:
            doc.add_heading(f"III. Daily Purchase Price Details", level=2)
        for index, price_info in enumerate(return_dic['price_infos']):
            if price_info:
                # 添加标题三
                doc.add_heading(f"{index + 1}、{price_info[0]['title']}", level=3)

                rows = 1 + len(price_info)
                cols = 12

                # 添加表格
                table = doc.add_table(rows=rows, cols=cols)  # 新创建一个m行n列的表格，根据电站数量动态生成表格

                hdr_cells_2 = table.rows[0].cells
                if lang == 'zh':
                    hdr_cells_2[0].text = '日期'
                    hdr_cells_2[1].text = '单位电价'
                    hdr_cells_2[2].text = '尖峰充电价格（元/kWh）'
                    hdr_cells_2[3].text = '尖峰放电价格（元/kWh）'
                    hdr_cells_2[4].text = '峰时充电价格（元/kWh）'
                    hdr_cells_2[5].text = '峰时放电价格（元/kWh）'
                    hdr_cells_2[6].text = '平时充电价格（元/kWh）'
                    hdr_cells_2[7].text = '平时放电价格（元/kWh）'
                    hdr_cells_2[8].text = '谷时充电价格（元/kWh）'
                    hdr_cells_2[9].text = '谷时放电价格（元/kWh）'
                    hdr_cells_2[10].text = '深谷时充电价格（元/kWh）'
                    hdr_cells_2[11].text = '深谷时放电价格（元/kWh）'
                else:
                    hdr_cells_2[0].text = 'Date'
                    hdr_cells_2[1].text = 'Settlement Price'
                    hdr_cells_2[2].text = 'Rush-hour Charging Price(￥/kWh)'
                    hdr_cells_2[3].text = 'Rush-hour Discharging Price(￥/kWh)'
                    hdr_cells_2[4].text = 'Peak-hour Charging Price(￥/kWh)'
                    hdr_cells_2[5].text = 'Peak-hour Discharging Price(￥/kWh)'
                    hdr_cells_2[6].text = 'Flat-hour Charging Price(￥/kWh)'
                    hdr_cells_2[7].text = 'Flat-hour Discharging Price(￥/kWh)'
                    hdr_cells_2[8].text = 'Valley-hour Charging Price(￥/kWh)'
                    hdr_cells_2[9].text = 'Valley-hour Discharging Price Price(￥/kWh)'
                    hdr_cells_2[10].text = 'DValley-hour Charging Price Price(￥/kWh)'
                    hdr_cells_2[11].text = 'DValley-hour Discharging Price Price(￥/kWh)'

                for i, day_data in enumerate(price_info):
                    # 合并第1列的第1行到第2行-----日期
                    cells = table.rows[1 + i].cells
                    cells[0].text = day_data['date']
                    cells[1].text = day_data['title']
                    cells[2].text = str(day_data['spike']['charge_price'])
                    cells[3].text = str(day_data['spike']['charge_price'])
                    cells[4].text = str(day_data['peak']['charge_price'])
                    cells[5].text = str(day_data['peak']['charge_price'])
                    cells[6].text = str(day_data['flat']['charge_price'])
                    cells[7].text = str(day_data['flat']['charge_price'])
                    cells[8].text = str(day_data['valley']['charge_price'])
                    cells[9].text = str(day_data['valley']['charge_price'])
                    cells[10].text = str(day_data['dvalley']['charge_price'])
                    cells[11].text = str(day_data['dvalley']['charge_price'])

                # 设置每个单元格居中对齐（水平+垂直）,并设置边框线
                for row in table.rows:
                    for cell in row.cells:
                        paragraph = cell.paragraphs[0]
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER

                # 设置单元格边框宽度
                table.style = 'Table Grid'

    # 保存文档
    doc.save(out_path)


# def windows_word2pdf(doc_path, pdf_path):
#     word = comtypes.client.CreateObject('Word.Application')
#     word.Visiable = 0
#     path = os.getcwd()
#     word_path = os.path.join(path, doc_path)
#     pdf_path = os.path.join(path, pdf_path)
#     new_pdf = word.Documents.Open(word_path)
#
#     # 设置页面为横向
#     word.ActiveDocument.PageSetup.Orientation = 0  # 0为纵向，1为横向
#
#     new_pdf.SaveAs(pdf_path, 17)
#     new_pdf.Close()


def linux_word2pdf(input_file, output_file):
    try:
        # 构建转换命令
        command = ['libreoffice', '--convert-to', 'pdf', '--outdir', output_file, input_file]
        # command = ['libreoffice', '--convert-to', 'pdf', input_file]
        # 执行转换命令
        subprocess.run(command, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print('转换命令执行异常：', e)
        return False
    except Exception as e:
        print('转换命令执行异常：', e)
        return False


def gen_pdf(return_dic, excel_path, doc_file, pdf_file, lang='zh'):
    # doc_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.docx"
    doc_path = os.path.join(excel_path, doc_file)
    # pdf_path = os.path.join(excel_path, pdf_file)

    try:
        generate_word_with_table(return_dic, doc_path, lang)
        linux_word2pdf(doc_path, excel_path)
        # 删除doc文件
        # os.remove(doc_path)
        return True
    except Exception as e:
        print('转换pdf执行异常：', e)
        print(traceback.print_exc())
        return False


if __name__ == '__main__':
    start_time = time.time()
    generate_word_with_table("output222.docx")
    print(2214, time.time() - start_time)
    linux_word2pdf()
    print(2216, time.time() - start_time)


    # word2pdf_2()

    # word2pdf_3()


