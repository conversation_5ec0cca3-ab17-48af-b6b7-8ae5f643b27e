#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-20 13:53:58
#@FilePath     : \RHBESS_Service\Tools\Utils\connent_broker_realdata.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:36:17


# -*- coding:utf-8 -*-
from time_utils import timeUtils
import paho.mqtt.client as mqtt
import json
from Tools.DB.RealRedisCon import r

guid='31661d6d30e28b3f494064e84498f8c3'
ff = r.hgetall(guid)
print (type(ff),ff)
for f in ff.keys():
    # print f,eval(ff[f])['val']
    value = eval(ff[f])
    print ('guid:',guid,'key:',f,'value:',value['val'],'time:',value['ts'].replace('/',' '))

# data = r.hkeys(guid)
# print data
# for d in data:
#     value =  eval(r.hget(guid,d))
#     print 'guid:',guid,'key:',d,'value:',value['val'],'time:',value['ts']