# 负荷预测Java实现

本项目是将Python中的`power_load_forecasting_views.py`转换为Java实现的完整解决方案。

## 项目结构

```
java/
├── entity/                          # 实体类
│   ├── DictModel.java              # 模型表实体类
│   ├── DictModelTarget.java        # 模型指标表实体类
│   ├── MaterStation.java           # 主站表实体类
│   ├── StationDetails.java         # 站点详情表实体类
│   ├── ModelForecastValue.java     # 预测值表实体类
│   ├── UserStrategy.java           # 用户策略表实体类
│   ├── UserStrategyCategoryNew.java # 用户策略分类表实体类
│   └── Month.java                  # 月份表实体类
├── dto/                            # 数据传输对象
│   ├── PowerLoadForecastingRequest.java   # 负荷预测请求DTO
│   ├── PowerLoadForecastingResponse.java  # 负荷预测响应DTO
│   ├── ModelManagementRequest.java        # 模型管理请求DTO
│   └── ModelManagementResponse.java       # 模型管理响应DTO
├── mapper/                         # 数据访问层
│   └── PowerLoadForecastingMapper.java    # 负荷预测数据访问接口
├── service/                        # 服务层
│   ├── PowerLoadForecastingService.java   # 负荷预测服务接口
│   └── impl/
│       └── PowerLoadForecastingServiceImpl.java # 负荷预测服务实现
├── controller/                     # 控制器层
│   ├── PowerLoadForecastingController.java      # 负荷预测控制器
│   └── PowerLoadForecastingModelController.java # 模型管理控制器
├── config/                         # 配置类
│   └── PowerLoadForecastingConfig.java    # 数据库和Redis配置
├── util/                          # 工具类
│   └── PowerLoadForecastingUtil.java      # 负荷预测工具类
└── README.md                      # 说明文档
```

## 功能对应关系

### Python -> Java 功能映射

| Python方法/类 | Java对应实现 | 功能说明 |
|--------------|-------------|----------|
| `PowerLoadForecastingView.get()` | `PowerLoadForecastingController.getPowerLoadForecastingData()` | 获取负荷预测数据 |
| `_get_load_true_data()` | `PowerLoadForecastingService.getLoadTrueData()` | 获取负荷实测值 |
| `_get_chag_disg_data()` | `PowerLoadForecastingService.getChagDisgData()` | 获取电量实测值 |
| `get_is_have_data()` | `PowerLoadForecastingService.checkHasData()` | 检查是否有数据 |
| `is_over_by_days()` | `PowerLoadForecastingUtil.isOverByDays()` | 判断时间范围 |
| `judge_is_date()` | `PowerLoadForecastingUtil.judgeIsDate()` | 日期格式校验 |
| `create_date_time_mapping()` | `PowerLoadForecastingUtil.createDateTimeMapping()` | 创建时间映射 |
| `get_all_date_between()` | `PowerLoadForecastingUtil.getAllDateBetween()` | 获取日期范围 |
| `get_best_model_id()` | `PowerLoadForecastingService.getBestModelId()` | 获取最佳模型ID |
| `creat_recommend_strategy_data()` | `PowerLoadForecastingService.createRecommendStrategyData()` | 创建推荐策略 |
| `add_recommend_strategy()` | `PowerLoadForecastingService.addRecommendStrategy()` | 保存推荐策略 |
| `PowerLoadForecastingModelView` | `PowerLoadForecastingModelController` | 模型管理接口 |

## 主要特性

### 1. 实体类设计
- 使用MyBatis-Plus注解进行ORM映射
- 支持自动填充创建时间和更新时间
- 完整的字段映射和约束

### 2. 数据访问层
- 使用MyBatis动态SQL实现复杂查询
- 支持多数据源配置（Doris DWD/ADS数据库）
- 参数化查询防止SQL注入

### 3. 服务层
- 完整的业务逻辑实现
- 异常处理和日志记录
- 事务管理支持

### 4. 控制器层
- RESTful API设计
- 参数校验和国际化支持
- Swagger API文档

### 5. 配置管理
- 多数据源配置
- Redis连接池配置
- 可配置的业务参数

## 使用方法

### 1. 依赖配置

在`pom.xml`中添加以下依赖：

```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.3</version>
    </dependency>
    
    <!-- Redis -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- MySQL Driver -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    
    <!-- HikariCP -->
    <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
    </dependency>
    
    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    
    <!-- Swagger -->
    <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger2</artifactId>
        <version>3.0.0</version>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
    </dependency>
</dependencies>
```

### 2. 配置文件

在`application.yml`中配置数据库和Redis连接：

```yaml
spring:
  datasource:
    doris-dwd-rhyc:
      host: localhost
      port: 9030
      database: dwd_rhyc
      username: root
      password: 
    doris-ads-rhyc:
      host: localhost
      port: 9030
      database: ads_rhyc
      username: root
      password: 
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: isDelete
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  success:
    enabled: true
  error:
    enabled: true
```

### 3. API使用示例

#### 获取负荷预测数据
```http
GET /api/power-load-forecasting?mstationId=1&targetId=1&startTime=2024-01-01&endTime=2024-01-31&modelIds=[1,2,3]
Headers:
  lang: zh
```

#### 检查数据是否存在
```http
GET /api/power-load-forecasting/check-data?stationId=1&startTime=2024-01-01&endTime=2024-01-31
Headers:
  lang: zh
```

#### 获取模型管理列表
```http
GET /api/power-load-forecasting-model?page=1&pageSize=10
Headers:
  lang: zh
```

#### 更新模型状态
```http
PUT /api/power-load-forecasting-model/status
Headers:
  Content-Type: application/json
  lang: zh
Body:
{
  "modelId": 1,
  "isActive": 1
}
```

## 注意事项

1. **数据库连接**：需要确保Doris数据库连接配置正确
2. **Redis连接**：策略数据存储在Redis database 3中
3. **权限认证**：需要实现JWT认证中间件
4. **异步处理**：推荐策略的翻译功能需要配置消息队列
5. **文件上传**：Excel导出功能需要配置MinIO或其他文件存储服务

## 扩展功能

- 支持Excel数据导出
- 支持多语言国际化
- 支持异步任务处理
- 支持缓存优化
- 支持监控和日志

## 技术栈

- Spring Boot 2.7+
- MyBatis Plus 3.5+
- Redis
- MySQL/Doris
- HikariCP
- Swagger
- Lombok
