package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 远程控制策略VO
 */
@Data
@ApiModel("远程控制策略VO")
public class TelecontrolStrategyVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("英文策略名称")
    private String enName;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
