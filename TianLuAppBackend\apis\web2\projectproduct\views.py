import datetime
import os
import time
import traceback
import pymysql
import calendar

from django.conf import settings
from django.db import connections
from django.db.models import Q, Sum
from django_redis import get_redis_connection
from openpyxl import Workbook
from rest_framework.response import Response
from rest_framework.views import APIView

from common.constant import EMPTY_STR_LIST
from tools.hour_setting import create_time_mapping
from apis.user import models

from common import common_response_code
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from tools.minio_tool import MinioTool
from apis.app2.station_task import station_statistics_income
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


class StationODeviceView(APIView):
    """
    设备名称清单
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        unit_type = request.query_params.get('unit_type')  # 1：pcs：0:bms
        if not id or not unit_type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'The field validation failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission, please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        station_id_list = []
        slave_station = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0)).all()
        for s_info in slave_station:
            station_id_list.append(s_info.id)
        data = {}
        units = models.Unit.objects.filter(is_delete=0, station_id__in=station_id_list).all()
        t = 'bms' if unit_type == '0' else 'pcs'
        for i in units:
            i = i.__dict__
            data[i.get(t)] = i.get('id')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationSocView(APIView):
    """
    储能SOC&功率
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'The field validation failed!',
                    }
                }
            )
        try:
            master_station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permissions, please check the parameters!',
                    }
                }
            )

        # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
        # meter_use_time = MeterUseTime.objects.filter(station=master_station, is_use=1).first()
        # is_use_account = (master_station.is_account and meter_use_time and
        #                   meter_use_time.start_time.date() <= datetime.datetime.strptime(inquire_time, '%Y-%m-%d').date() <= meter_use_time.end_time.date())

        # 处理主从模式
        # station_list = []
        master_station_list = []
        all_station_list = []
        # master_slave_station = station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0), Q(pack=-1) | Q(pack=0)).all()
        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        master_slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).all()

        for m_info in master_slave_station:
            master_station_list.append(m_info.english_name)
        station_id_list = []
        all_station_id_list = []
        all_slave_station = master_station.stationdetails_set.filter(is_delete=0).all()
        slave_station = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
        for s_info in slave_station:
            # station_list.append(s_info.english_name)
            station_id_list.append(s_info.id)

        for s_info in all_slave_station:
            all_station_list.append(s_info.english_name)
            all_station_id_list.append(s_info.id)

        bms_unit_list = []
        is_unit = False  # 判断是否读取station_type小于等于1的数据
        if unit_id:
            unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
            bms_unit_list.append(unit.bms)
            all_station_list = [unit.station.english_name, -1]
        else:
            unit = models.Unit.objects.filter(is_delete=0, station__id__in=station_id_list).all()
            bms_unit_list = [i.bms for i in unit]
            is_unit = True

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'

        sql = """SELECT  avg(soc) as soc, DATE_FORMAT(time,'%H:%i') as new_time
                                      FROM dwd_measure_bms_data_storage_3
                                      WHERE 
                                      time 
                                      BETWEEN '{}'
                                      AND '{}'
                                      AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time)

        if len(all_station_list) == 1:
            sql += "AND station_name = '{}'".format(all_station_list[0])
        else:
            sql += "AND station_name in {}".format(tuple(all_station_list))
        if len(bms_unit_list) == 1:
            sql += "AND device = '{}'".format(bms_unit_list[0])
        else:
            sql += 'AND device in {}'.format(tuple(bms_unit_list))
        sql += ' GROUP BY new_time  ORDER BY new_time'
        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'The query failed!',
                        }
                    }
                )
        soc_res = create_time_mapping()
        for i in result:
            t = i[1]
            if soc_res.get(t):
                soc = round(i[0], 1)
                soc_res[t] = soc

        data = {
            "soc": soc_res,
            'pcc': create_time_mapping(),  # 负荷功率
            'p': create_time_mapping(),  # 储能功率
            'aim_power': create_time_mapping(),  # 目标功率
            'chag_day': [],  # 日充电量
            'chag': [],  # 尖峰平谷深充电量
            'disg_day': [],  # 日放电量
            'disg': [],  # 尖峰平谷深放电量
            'discharge_efficiency': 0  #
        }
        bms_count = len(bms_unit_list)
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            # 功率
            p_sql = """SELECT
                           DATE_FORMAT(time,'%H:%i') as new_time, sum(p_load) as p_load, sum(p) as p
                       FROM
                           ads_report_loading_data
                       WHERE
                           time
                           BETWEEN '{}'
                           AND '{}'
                           AND state_pcc = 0
                           AND MOD(MINUTE(time), 5) = 0 
                           """.format(s_time, e_time)
            # 尖峰平谷充放电量 & SOC变化值 =========> 改查新综合过结算表和计量表的新1d冻结表：表名未定
            peak_valley_sql = """SELECT
                                        sum(pointed_chag),
                                        sum(peak_chag),
                                        sum(flat_chag),
                                        sum(valley_chag),
                                        sum(dvalley_chag),
                                        sum(pointed_disg),
                                        sum(peak_disg),
                                        sum(flat_disg),
                                        sum(valley_disg),
                                        sum(dvalley_disg),
                                        sum(chag), 
                                        sum(disg), 
                                        avg(chag_soc), 
                                        avg(disg_soc)
                                 FROM
                                    ads_report_chag_disg_union_1d 
                                 WHERE
                                    `day` = '{}' 
                                 """.format(inquire_time)
            # SOC 变化值
            # soc_sql = """SELECT
            #                 sum(chag), sum(disg), avg(chag_soc), avg(disg_soc)
            #             FROM
            #                 ads_report_chag_disg_1d
            #             WHERE
            #                 day = '{}'
            #                 """.format(inquire_time)
            if len(all_station_list) == 1:
                peak_valley_sql += f" AND station = '{all_station_list[0]}'"
                # soc_sql += f" AND station = '{station_list[0]}'"
            else:

                peak_valley_sql += f" AND station in {tuple(all_station_list)}"
                # soc_sql += f" AND station in {tuple(station_list)}"
            if is_unit:
                peak_valley_sql += " AND station_type <= 1"            # 查非单元的信息
                # soc_sql += " AND station_type = 1"
            else:
                peak_valley_sql += f" AND unit_name = '{bms_unit_list[0]}'"
                # soc_sql += f" AND unit_name = '{bms_unit_list[0]}'"

            p_sql += f" AND station = '{master_station.english_name}' GROUP BY new_time ORDER BY new_time"
            try:
                # 获取查询结果
                ads_cursor.execute(p_sql)
                p_result = ads_cursor.fetchall()
                ads_cursor.execute(peak_valley_sql)
                peak_valley_result = ads_cursor.fetchone()
                # ads_cursor.execute(soc_sql)
                # soc_result = ads_cursor.fetchone()
            except Exception as e:
                error_log.error(traceback.print_exc())
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'The query failed!',
                        }
                    }
                )

        # 计算目标功率
        if unit_id:
            power = unit.rated_power
        else:
            power = master_station.stationdetails_set.filter(is_delete=0).aggregate(p_sum=Sum('rated_power'))
            power = power.get('p_sum')
        # 连接清洗库
        connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
        dwd_cursor = connection.cursor()
        tactics_sql = f"""
                                SELECT
                                            time, station_name, device, slave, pack, type, ots, rlh1p, rlh2p, rlh3p, rlh4p, rlh5p, rlh6p, rlh7p, rlh8p, rlh9p, 
                                            rlh10p, rlh11p, rlh12p, rlh13p, rlh14p, rlh15p, rlh16p, rlh17p, rlh18p, rlh19p, rlh20p, rlh21p, rlh22p, rlh23p, rlh24p,
                                             rlh1f, rlh2f, rlh3f, rlh4f, rlh5f, rlh6f, rlh7f, rlh8f, rlh9f, rlh10f, rlh11f, rlh12f, rlh13f, rlh14f, rlh15f, rlh16f, 
                                             rlh17f, rlh18f, rlh19f, rlh20f, rlh21f, rlh22f, rlh23f, rlh24f, rlh25p, rlh26p, rlh27p, rlh28p, rlh29p, rlh30p, rlh31p, 
                                             rlh32p, rlh33p, rlh34p, rlh35p, rlh36p, rlh37p, rlh38p, rlh39p, rlh40p, rlh41p, rlh42p, rlh43p, rlh44p, rlh45p, rlh46p,
                                              rlh47p, rlh48p, rlh25f, rlh26f, rlh27f, rlh28f, rlh29f, rlh30f, rlh31f, rlh32f, rlh33f, rlh34f, rlh35f, rlh36f, rlh37f,
                                               rlh38f, rlh39f, rlh40f, rlh41f, rlh42f, rlh43f, rlh44f, rlh45f, rlh46f, rlh47f, rlh48f
                                FROM
                                    `dwd_measure_ems_data_storage_tscale` 
                                WHERE
                                    station_name = '{master_station.english_name}' 
                                    AND time BETWEEN '{inquire_time}' 
                                    AND '{inquire_time + ' 23:59:59'}' 
                                    AND device='EMS'
                                    AND MOD(MINUTE(time), 5) = 0 
                                ORDER BY
                                    time DESC 
                                    LIMIT 1
                                """
        _p_sql = """SELECT
                           DATE_FORMAT(time,'%H:%i') as new_time, sum(p) as p
                       FROM
                           dwd_measure_pcs_data_storage
                       WHERE
                           time
                           BETWEEN '{}'
                           AND '{}'
                           AND MOD(MINUTE(time), 5) = 0 
                           """.format(s_time, e_time)
        if unit_id:
            _p_sql += f' AND device = "{unit.pcs}"'
        if len(all_station_list) == 1:
            _p_sql += f" AND station_name = '{all_station_list[0]}' GROUP BY new_time ORDER BY new_time"
        else:
            _p_sql += f" AND station_name in {tuple(all_station_list)} GROUP BY new_time ORDER BY new_time"
        try:
            # 获取查询结果
            dwd_cursor.execute(tactics_sql)
            tactics_result = dwd_cursor.fetchone()
            dwd_cursor.execute(_p_sql)
            _p_result = dwd_cursor.fetchall()
        except Exception as e:
            dwd_cursor.close()
            dwd_cursor.close()
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！' if lang == 'zh' else 'The query failed!',
                    }
                }
            )
        for i in p_result:
            t = i[0]
            if i[1] is not None:
                if data['pcc'].get(t):
                    data['pcc'][t] = i[1]
                # k1：小时；k2：分钟;
                k1 = int(t.split(':')[0])
                k1 = 24 if k1 == 0 else k1
                k2 = int(t.split(':')[1])
                k = k1 if k2 <= 30 else k1 + 24  # 策略修改为半小时一个点位，通过判断分钟是否小于等于半小时，来计算取对应的点位
                if tactics_result:
                    if k > 24 and tactics_result.get(f'rlh{k}p') is None:
                        k = k - 24
                    if tactics_result.get(f'rlh{k}p') is not None and tactics_result.get(f'rlh{k}f') is not None:
                        if data['aim_power'].get(t):
                            data['aim_power'][t] = float(tactics_result.get(f'rlh{k}p')) * float(
                                tactics_result.get(f'rlh{k}f')) * float(power)
                #     else:
                #         data['aim_power'][t] = '--'
                # else:
                #     data['aim_power'][t] = '--'
            # if i[2] is not None:
            #     data['p'][t] = i[2]

        for i in _p_result:
            t = i.get('new_time')
            if i.get('p') is not None:
                if data['p'].get(t):
                    data['p'][t] = i.get('p')
            # else:
            #     data['p'][t] = '--'
        # 计算充放电效率和尖峰平谷
        if None in peak_valley_result:
            peak_valley_result = [0 for i in range(13)]
        data['chag'] = peak_valley_result[:5] if peak_valley_result else []
        data['disg'] = peak_valley_result[5:] if peak_valley_result else []

        data['disg'] = peak_valley_result[5:10] if peak_valley_result else []
        data['chag_day'] = round(sum(data['chag']), 2)
        data['disg_day'] = round(sum(data['disg']), 2)
        # if soc_result:
        if 0 in peak_valley_result[10:14]:
            data['discharge_efficiency'] = '--'
        else:
            eff = peak_valley_result[10+1] / peak_valley_result[10+3] / (peak_valley_result[10+0] / peak_valley_result[10+2])
            if eff > 1:
                data['discharge_efficiency'] = '--'
            elif eff < 0.85:
                data['discharge_efficiency'] = 0.85
            else:
                data['discharge_efficiency'] = round(eff, 4)
        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            st.title = '储能SOC&功率' if lang == 'zh' else 'SOC &ESS power'
            title = ['日期时间', 'SOC（%）', '储能功率（kW）', '目标功率（kW）', '负荷功率（kW）'] if lang == 'zh' else ['DateTime', 'SOC（%）', 'ESS Power（kW）', 'Expected Power（kW）', 'Load Power（kW）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['soc', 'p', 'aim_power', 'pcc']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                for _i, _k in enumerate(['soc', 'p', 'aim_power', 'pcc']):
                    st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(k) or data[_k].get(k) == 0 else ''

            file_name = f"{master_station.name}-储能SOC&功率{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{master_station.name}-SOC &ESS POWER{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationPowerView(APIView):
    """
     电网测功率
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        station_list = []
        # slave_station = station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0), Q(pack=-1) | Q(pack=0)).all()
        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        slave_station = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).all()

        for s_info in slave_station:
            station_list.append(s_info.english_name)

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        dwd_sql = """SELECT
                            mfixdemandref,
                            tfm,
                            tprt,
                            device,
                            DATE_FORMAT(time,'%H:%i') as time
                    FROM dwd_measure_ems_data_storage
                    WHERE
                        time
                        BETWEEN '{}'
                        AND '{}'
                        AND device = 'EMS'
                        AND MOD(MINUTE(time), 5) = 0 
                         """.format(s_time, e_time)

        _p_sql = """SELECT
                              DATE_FORMAT(time,'%H:%i') as new_time, sum(p) as p
                          FROM
                              dwd_measure_pcs_data_storage
                          WHERE
                              time
                              BETWEEN '{}'
                              AND '{}'
                              AND MOD(MINUTE(time), 5) = 0 
                              """.format(s_time, e_time)

        if len(station_list) == 1:
            dwd_sql += "AND station_name = '{}'".format(station_list[0])
            _p_sql += "AND station_name = '{}'".format(station_list[0])
        else:
            dwd_sql += "AND station_name in {}".format(tuple(station_list))
            _p_sql += "AND station_name in {}".format(tuple(station_list))
        _p_sql += " GROUP BY new_time ORDER BY new_time"
        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(dwd_sql)
                dwd_result = dwd_cursor.fetchall()
                dwd_cursor.execute(_p_sql)
                _p_result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'The query failed.',
                        }
                    }
                )

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                DATE_FORMAT(time,'%H:%i') as new_time, avg(p_gird), sum(p_load) as p_load
                            FROM
                                ads_report_loading_data
                            WHERE
                                time
                                BETWEEN '{}'
                                AND '{}'
                                AND state_pcc = 0
                                """.format(s_time, e_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                p_sql += " GROUP BY new_time ORDER BY new_time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'The query failed.',
                            }
                        }
                    )

        data = {
            'pcc': create_time_mapping(),  # 负荷功率
            'p': create_time_mapping(),  # 储能功率
            'required_power_day': create_time_mapping(),  # 需量功率
            'transformer_safety_capacity_day': create_time_mapping(),  # 变压器安全容量
            'grid_side_power_day': create_time_mapping(),  # 电网侧功率
            'required_power': '--',  # 需量功率
            'transformer_safety_capacity': '--',  # 变压器安全容量
            'grid_side_power': 0,  # 电网侧功率
        }
        dwd_res = {i[4]: [i[0], i[1], i[2]] for i in dwd_result}
        for k, v in dwd_res.items():
            if v[0] is not None:
                if data['required_power_day'].get(k):
                    data['required_power_day'][k] = v[0]
            if v[2] is not None:
                if data['transformer_safety_capacity_day'].get(k):
                    data['transformer_safety_capacity_day'][k] = v[2] * v[1]
        for i in p_result:
            t = i[0]
            if i[1] is not None:
                if data['grid_side_power_day'].get(t):
                    data['grid_side_power_day'][t] = i[1]
            if i[2] is not None:
                if data['pcc'].get(t):
                    data['pcc'][t] = i[2]

        for i in _p_result:
            t = i[0]
            if i[1] is not None:
                if data['p'].get(t):
                    data['p'][t] = i[1]

        grid_side_power = data['grid_side_power_day'].values()
        grid_side_power = [i for i in grid_side_power if i != '--']
        data['grid_side_power'] = max(grid_side_power) if grid_side_power else 0

        conn = get_redis_connection("3")
        # 查询主站
        # station_ems = station.stationdetails_set.filter(
        #     Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0)).first()

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).first()

        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems:
            ems = eval(eval(ems))
        else:
            ems = {}
        tfm = float(ems.get('TFM')) if ems.get('TFM') is not None and ems.get('TFM') not in EMPTY_STR_LIST else '--'
        tprt = float(ems.get('TPRT')) if ems.get('TPRT') is not None and ems.get('TPRT') not in EMPTY_STR_LIST else '--'
        data['transformer_safety_capacity'] = '--' if '--' in (tprt, tfm) else round(tfm * tprt, 2)  # 变压器安全容量
        data['required_power'] = round(float(ems.get('TFM')), 2) if (ems.get('TFM') is not None and ems.get('TFM')
                                                                     not in EMPTY_STR_LIST) else '--'

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            st.title = '电网侧功率' if lang == 'zh' else 'Utility Power'
            title = ['日期时间', '电网侧功率（kW）', '变压器安全容量（kVA）', '需量功率（kW）','储能功率（kW）','负荷功率（kW）'] if lang == 'zh' else ['DateTime', 'Utility Power（kW）', 'Safe Capacity for Transformer（kVA）', 'Power Demand（kW）', 'ESS Power（kW）', 'Load Power（kW）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['grid_side_power_day', 'transformer_safety_capacity_day', 'required_power_day', 'p', 'pcc']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                for _i, _k in enumerate(
                        ['grid_side_power_day', 'transformer_safety_capacity_day', 'required_power_day', 'p', 'pcc']):
                    st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(k) or data[_k].get(k) == 0 else ''

            file_name = f"{station.name}-电网侧功率数据{datetime.datetime.now().strftime('%Y-%m-%d')}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Utility Power-{datetime.datetime.now().strftime('%Y-%m-%d')}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationVoltammeterView(APIView):
    """
     电压&电流
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id or not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'The field validation failed.',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permissions, please check the parameters.',
                    }
                }
            )
        # 处理主从模式
        # station_list = []
        # station_id_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # for s_info in slave_station:
        #     station_list.append(s_info.english_name)
        #     station_id_list.append(s_info.id)

        unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
        pcs = unit.pcs
        english_name = unit.station.english_name

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%Y-%m-%d %H:%i') as new_time, avg(puab), avg(pubc), avg(puca), avg(ia), avg(ib), avg(ic)
                                      FROM dwd_measure_pcs_data_storage
                                      WHERE 
                                      time 
                                      BETWEEN '{}'
                                      AND '{}' 
                                      AND device = '{}'
                                      AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time, pcs)

        # if len(station_list) == 1:
        sql += "AND station_name = '{}'".format(english_name)
        # else:
        #     sql += "AND station_name in {}".format(tuple(station_list))

        sql += ' GROUP BY new_time ORDER BY new_time'
        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                        }
                    }
                )
        data = {
            'ab_voltage': create_time_mapping(day=inquire_time),  # AB线电压
            'ab_voltage_avg': 0,  # AB线电压平均值
            'bc_voltage': create_time_mapping(day=inquire_time),  # BC线电压
            'bc_voltage_avg': 0,  # BC线电压平均值
            'ca_voltage': create_time_mapping(day=inquire_time),  # CA线电压
            'ca_voltage_avg': 0,  # CA线电压平均值
            'a_electricity': create_time_mapping(day=inquire_time),  # A线电流
            'a_electricity_avg': 0,  # A线电流平均值
            'b_electricity': create_time_mapping(day=inquire_time),  # B线电流
            'b_electricity_avg': 0,  # B线电流平均值
            'c_electricity': create_time_mapping(day=inquire_time),  # C线电流
            'c_electricity_avg': 0,  # C线电流平均值

        }
        for i in result:
            t = i[0]
            if i[1] is not None:
                if data['ab_voltage'].get(t):
                    data['ab_voltage'][t] = round(i[1], 2)
            if i[2] is not None:
                if data['bc_voltage'].get(t):
                    data['bc_voltage'][t] = round(i[2], 2)
            if i[3] is not None:
                if data['ca_voltage'].get(t):
                    data['ca_voltage'][t] = round(i[3], 2)
            if i[4] is not None:
                if data['a_electricity'].get(t):
                    data['a_electricity'][t] = round(i[4], 2)
            if i[5] is not None:
                if data['b_electricity'].get(t):
                    data['b_electricity'][t] = round(i[5], 2)
            if i[6] is not None:
                if data['c_electricity'].get(t):
                    data['c_electricity'][t] = round(i[6], 2)
        ab_voltage = [i for i in data['ab_voltage'].values() if i != '--']
        data['ab_voltage_avg'] = round(sum(ab_voltage) / len(ab_voltage), 2) if ab_voltage else '--'
        bc_voltage = [i for i in data['bc_voltage'].values() if i != '--']
        data['bc_voltage_avg'] = round(sum(bc_voltage) / len(bc_voltage), 2) if bc_voltage else '--'
        ca_voltage = [i for i in data['ca_voltage'].values() if i != '--']
        data['ca_voltage_avg'] = round(sum(ca_voltage) / len(ca_voltage), 2) if ca_voltage else '--'
        a_electricity = [i for i in data['a_electricity'].values() if i != '--']
        data['a_electricity_avg'] = round(sum(a_electricity) / len(a_electricity),
                                          2) if a_electricity else '--'
        b_electricity = [i for i in data['b_electricity'].values() if i != '--']
        data['b_electricity_avg'] = round(sum(b_electricity) / len(b_electricity),
                                          2) if b_electricity else '--'
        c_electricity = [i for i in data['c_electricity'].values() if i != '--']
        data['c_electricity_avg'] = round(sum(c_electricity) / len(c_electricity),
                                          2) if c_electricity else '--'

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '电流&电压'
                title = ['日期时间', 'AB线电压（V）', 'BC线电压（V）', 'CA线电压（V）', 'A相电流（A）', 'B相电流（A）', 'C相电流（A）']
            else:
                st.title = 'Current & Voltage'
                title = ['DateTime', 'Line A-B Voltage（V）', 'Line B-C Voltage（V）', 'Line C-A Voltage（V）', 'Phase A Current（A）', 'Phase B Current（A）',
                         'Phase C Current（A）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['ab_voltage', 'bc_voltage', 'ca_voltage', 'a_electricity', 'b_electricity', 'c_electricity']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                for _i, _k in enumerate(
                        ['ab_voltage', 'bc_voltage', 'ca_voltage', 'a_electricity', 'b_electricity', 'c_electricity']):
                    st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(k) or data[_k].get(k) == 0 else ''

            file_name = f"{station.name}-电流&电压{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Current & Voltage{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationDemandView(APIView):
    """
     需量分析
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        s_time = request.query_params.get('start_time')  # 查询开始日期
        e_time = request.query_params.get('end_time')  # 查询结束日期
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not s_time or not e_time or not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permissions, please check the parameters.',
                    }
                }
            )
        # 处理主从模式
        station_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # slave_station = station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0), Q(pack=-1) | Q(pack=0)).all()
        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        slave_station = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).all()

        for s_info in slave_station:
            station_list.append(s_info.english_name)
        s_time_ = s_time + ' 00:00:00'
        e_time_ = e_time + ' 23:59:59'
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            p_sql = """SELECT
                             max(p_gird), DATE_FORMAT(time,'%Y-%m-%d') as time1
                         FROM
                             ads_report_loading_data
                         WHERE
                             time
                             BETWEEN '{}'
                             AND '{}'
                             AND state_pcc = 0
                             """.format(s_time_, e_time_)

            if len(station_list) == 1:
                p_sql += "AND station = '{}'".format(station_list[0])
            else:
                p_sql += "AND station in {}".format(tuple(station_list))
            p_sql += " GROUP BY time1 ORDER BY time1"

            now_time = datetime.datetime.now()
            # 30天内最大功率
            month_p_sql = """SELECT
                             max(p_gird)
                         FROM
                             ads_report_loading_data
                         WHERE
                             time
                             BETWEEN '{}'
                             AND '{}'
                             AND state_pcc = 0
                             """.format((now_time - datetime.timedelta(days=30)).strftime('%Y-%m-%d 00:00:00'), now_time.strftime('%Y-%m-%d 23:59:59'))
            if len(station_list) == 1:
                month_p_sql += "AND station = '{}'".format(station_list[0])
            else:
                month_p_sql += "AND station in {}".format(tuple(station_list))
            try:
                # 获取查询结果
                ads_cursor.execute(p_sql)
                p_result = ads_cursor.fetchall()
                ads_cursor.execute(month_p_sql)
                month_p_result = ads_cursor.fetchone()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )
        max_power = month_p_result[0] if month_p_result else 0
        data = {
            'data': {},
            # 'avg_power': 0,     # 平均功率
            'max_power': round(max_power, 2)      # 最大功率
        }
        for i in p_result:
            t = i[1]
            if i[0] is not None:
                data['data'][t] = [round(float(i[0]), 2), round(max_power, 2)]

        # data['avg_power'] = round(sum([i[0] for i in data['data'].values()]) / len(data['data'].values()), 2) if data[
        #     'data'].values() else '--'
        # data['max_power'] = max([i[1] for i in data['data'].values()]) if data['data'].values() else '--'

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '需量分析'
                title = ['日期时间', '逐日最大功率（kW）', '30天最大功率（kW）']
            else:
                st.title = 'Demand Power'
                title = ['DateTime', 'Daily maximum power（kW）', '30 day max power（kW）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            for k, v in data['data'].items():
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                st.cell(row=max_row, column=2).value = v[0]
                st.cell(row=max_row, column=3).value = v[1]
            file_name = f"{station.name}-需量分析{s_time}-{e_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Demand Power {s_time}-{e_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationEfficiencyView(APIView):
    """
     充放电效率
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        s_time = request.query_params.get('start_time')  # 查询开始日期
        e_time = request.query_params.get('end_time')  # 查询结束日期
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not s_time or not e_time or not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        station_list = []
        station_id_list = []
        slave_station = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0)).all()
        for s_info in slave_station:
            station_list.append(s_info.english_name)
            station_id_list.append(s_info.id)
        # 查询结算表
        s_time = f'{s_time[:4]}-{s_time[-2:]}'
        e_time = f'{e_time[:4]}-{e_time[-2:]}'
        y, m = int(e_time[:4]), int(e_time[-2:])
        d = calendar.monthrange(y, m)[1]
        s_time = datetime.datetime.strptime(s_time, '%Y-%m').strftime('%Y-%m-%d')
        e_time = datetime.datetime.strptime(e_time, '%Y-%m').replace(day=d).strftime('%Y-%m-%d')
        sql = """SELECT
	                sum(v_chag), sum(v_disg), avg(chag_soc), avg(disg_soc), SUBSTRING( `day`, 1, 7 ) AS new_time
                 FROM ads_report_ems_chag_disg_1d
                 WHERE
                     device_type=0
                    AND
                    day
                    BETWEEN '{}'
                    AND '{}' 
                    AND station = '{}'""".format(s_time, e_time, station.english_name)

        sql += " GROUP by new_time ORDER BY new_time"
        # 查询计量表
        discharg_sql = """
                        SELECT  
                            sum(v_chag), sum(v_disg), avg(chag_soc), avg(disg_soc), SUBSTRING( `day`, 1, 7 ) AS new_time
                        FROM
                            ads_report_chag_disg_1d
                        WHERE
                            station_type = 1 
                        AND
                            day
                        BETWEEN '{}'
                        AND '{}'""".format(s_time, e_time)
        if len(station_list) == 1:
            discharg_sql += "AND station = '{}'".format(station_list[0])
        else:
            discharg_sql += "AND station in {}".format(tuple(station_list))
        discharg_sql += " GROUP by new_time ORDER BY new_time"
        # 连接清洗库
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                # 获取查询结果
                ads_cursor.execute(sql)
                closing_res = ads_cursor.fetchall()
                ads_cursor.execute(discharg_sql)
                measure_res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

        data = {
            'closing_data': {},  # 结算表数据
            'measure_data': {},  # 计量表数据
            'closing': {  # 结算表
                'efficiency': 0,  # 充放电效率
                'chag': 0,  # 充电量
                'chag_soc': [],  # 充电量SOC
                'disg': 0,  # 放电量
                'disg_soc': []  # 放电量SOC
            },
            'measure': {  # 计量表
                'efficiency': 0,  # 充放电效率
                'chag': 0,  # 充电量
                'chag_soc': [],  # 充电量SOC
                'disg': 0,  # 放电量
                'disg_soc': []  # 放电量SOC
            }
        }
        closing_electric = {}  # 结算表充放电量
        measure_electric = {}  # 计量表充放电量
        for i in closing_res:
            t = i[4]
            if i[0] is not None and i[2] is not None:
                chag_eff = i[0] / i[2] if i[2] != 0 else '--'
                disg_eff = i[1] / i[3] if i[3] != 0 else '--'
                if chag_eff != '--' and disg_eff != '--' and chag_eff != 0:
                    eff = float(disg_eff) / float(chag_eff) * 100
                    if eff > 100:
                        data['closing_data'][t] = '--'
                    elif eff < 85:
                        data['closing_data'][t] = 85
                    else:
                        data['closing_data'][t] = round(disg_eff / chag_eff * 100, 2)
                else:
                    data['closing_data'][t] = '--'
                data['closing']['chag'] += i[0]
                data['closing']['chag_soc'].append(i[2])
                data['closing']['disg'] += i[1]
                data['closing']['disg_soc'].append(i[3])
                closing_electric[t] = [i[0], i[1]]


        for i in measure_res:
            t = i[4]
            if i[0] is not None and i[2] is not None:
                chag_eff = i[0] / i[2] if i[2] != 0 else '--'
                disg_eff = i[1] / i[3] if i[3] != 0 else '--'
                if chag_eff != '--' and disg_eff != '--':
                    eff = disg_eff / chag_eff * 100 if chag_eff != 0 else 0
                    if eff > 100:
                        data['measure_data'][t] = '--'
                    elif eff < 85:
                        data['measure_data'][t] = 85
                    else:
                        data['measure_data'][t] = round(disg_eff / chag_eff * 100, 2)
                else:
                    data['measure_data'][t] = '--'
                data['measure']['chag'] += i[0]
                data['measure']['chag_soc'].append(i[2])
                data['measure']['disg'] += i[1]
                data['measure']['disg_soc'].append(i[3])
                measure_electric[t] = [i[0], i[1]]
        for i in data['measure_data']:  # 补充月份
            if not data['closing_data'].get(i):
                data['closing_data'][i] = '--'
        if data['closing']['chag_soc'] and data['closing']['disg_soc']:
            data['closing']['chag_soc'] = sum(data['closing']['chag_soc']) / len(data['closing']['chag_soc'])
            data['closing']['disg_soc'] = sum(data['closing']['disg_soc']) / len(data['closing']['disg_soc'])
        else:
            data['closing']['chag_soc'], data['closing']['disg_soc'] = 0, 0
        data['closing_data'] = dict(sorted(data['closing_data'].items(), key=lambda x: x[0]))
        if data['closing']['chag'] is not None and data['closing']['disg'] is not None:
            closing_chag_eff = data['closing']['chag'] / data['closing']['chag_soc'] if data['closing'][
                                                                                            'chag_soc'] != 0 else 0
            closing_disg_eff = data['closing']['disg'] / data['closing']['disg_soc'] if data['closing'][
                                                                                            'disg_soc'] != 0 else 0

            if closing_chag_eff == 0 or closing_disg_eff == 0:
                eff = '--'
            else:
                eff = closing_disg_eff / closing_chag_eff * 100
                if eff < 85:
                    eff = 85
                elif eff > 100:
                    eff = '--'
                else:
                    eff = round(eff, 2)
            data['closing']['efficiency'] = eff

        if data['measure']['chag_soc'] and data['measure']['disg_soc']:
            data['measure']['chag_soc'] = sum(data['measure']['chag_soc']) / len(data['measure']['chag_soc'])
            data['measure']['disg_soc'] = sum(data['measure']['disg_soc']) / len(data['measure']['disg_soc'])
        else:
            data['measure']['chag_soc'], data['measure']['disg_soc'] = 0, 0
            data['measure']['chag_soc'], data['measure']['disg_soc'] = 0, 0
        if data['measure']['chag'] is not None and data['measure']['disg'] is not None:
            measure_chag_eff = data['measure']['chag'] / data['measure']['chag_soc'] if data['measure'][
                                                                                            'chag_soc'] != 0 else 0
            measure_disg_eff = data['measure']['disg'] / data['measure']['disg_soc'] if data['measure'][
                                                                                            'disg_soc'] != 0 else 0
            if measure_disg_eff == 0 or measure_disg_eff == 0:
                eff = '--'
            else:
                eff = measure_disg_eff / measure_chag_eff * 100 if measure_chag_eff != 0 else 0
                if eff < 85:
                    eff = 85
                elif eff > 100:
                    eff = '--'
                else:
                    eff = round(eff, 2)
            data['measure']['efficiency'] = eff

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '充放电效率'
                title = ['并网点名称+月份', '结算电表效率', '结算电表充电量', '结算电表放电量', '设备自带计量点表效率', '设备自带计量点表充电量', '设备自带计量点表放电量']
            else:
                st.title = 'Efficiency'
                title = ['Installation+Month', 'Efficiency(Settlement Meter)', 'Energy Charged(Settlement Meter)', 'Energy Discharged(Settlement Meter)', 'Efficiency(Multimeter)',
                         'Energy Charged(Multimeter)', 'Energy Discharged(Multimeter)']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            clo_len = len(data['closing_data'])
            mea_len = len(data['measure_data'])
            for k, v in data['closing_data' if clo_len >= mea_len else 'measure_data'].items():
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = station.name + '-' + k
                st.cell(row=max_row, column=2).value = data['closing_data'].get(k)
                st.cell(row=max_row, column=3).value = closing_electric.get(k)[0] if closing_electric.get(k) else '--'
                st.cell(row=max_row, column=4).value = closing_electric.get(k)[1] if closing_electric.get(k) else '--'
                st.cell(row=max_row, column=5).value = data['measure_data'].get(k)
                st.cell(row=max_row, column=6).value = measure_electric.get(k)[0] if measure_electric.get(k) else '--'
                st.cell(row=max_row, column=7).value = measure_electric.get(k)[1] if measure_electric.get(k) else '--'

            file_name = f"{station.name}-充放电效率{s_time}-{e_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Efficiency{s_time}-{e_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationTemperatureView(APIView):
    """
     PCS温度
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id or not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        station_list = []
        # station_id_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # for s_info in slave_station:
        #     station_list.append(s_info.english_name)
        #     station_id_list.append(s_info.id)

        unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
        pcs = unit.pcs
        station_list = [unit.station.english_name]

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%Y-%m-%d %H:%i') as new_time, avg(pat), avg(igbtat), avg(tgbtbt), avg(tgbtct)
                                              FROM dwd_measure_pcs_data_storage
                                              WHERE 
                                              time 
                                              BETWEEN '{}'
                                              AND '{}' 
                                              AND device = '{}'
                                              AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time, pcs)

        if len(station_list) == 1:
            sql += "AND station_name = '{}'".format(station_list[0])
        else:
            sql += "AND station_name in {}".format(tuple(station_list))
        sql += " GROUP BY new_time ORDER BY new_time"
        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

        data = {
            'environment_temperature': create_time_mapping(day=inquire_time),  # 环境温度
            'a_temperature': create_time_mapping(day=inquire_time),  # A 相 IGBT 温度
            'b_temperature': create_time_mapping(day=inquire_time),  # B 相 IGBT 温度
            'c_temperature': create_time_mapping(day=inquire_time),  # C 相 IGBT 温度
            'threshold': create_time_mapping(day=inquire_time, value=95 if unit.v_number == 3 else 75),  # 温度阈值
            'max_temperature': [],  # 温度最大值
            'min_temperature': [],  # 温度最小值
            'avg_temperature': []  # 温度平均值
        }

        for i in result:
            t = i[0]
            if i[1]:
                if data['environment_temperature'].get(t):
                    data['environment_temperature'][t] = i[1]
            if i[2]:
                if data['a_temperature'].get(t):
                    data['a_temperature'][t] = i[2]

            if i[3]:
                if data['b_temperature'].get(t):
                    data['b_temperature'][t] = i[3]
            if i[4]:
                if data['c_temperature'].get(t):
                    data['c_temperature'][t] = i[4]
        for i in ['a_temperature', 'b_temperature', 'c_temperature', 'environment_temperature']:
            d = [v for v in data[i].values() if v != '--']
            if d:
                data['max_temperature'].append(max(d))
                data['min_temperature'].append(min(d))
                data['avg_temperature'].append(round(sum(d) / len(d), 2))
            else:
                data['max_temperature'].append('--')
                data['min_temperature'].append('--')
                data['avg_temperature'].append('--')

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）

            if lang == 'zh':
                st.title = 'PCS温度'
                title = ['日期时间', 'A相IGBT温度（℃）', 'B相IGBT温度（℃）', 'C相IGBT温度（℃）', '环境温度（℃）', '温度阈值（℃）']
            else:
                st.title = 'PCS Temperature'
                title = ['DateTime', 'Phase A IGBT（℃）', 'Phase B IGBT（℃）', 'Phase C IGBT（℃）', 'Ambient（℃）', 'Threshold（℃）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['a_temperature', 'b_temperature', 'c_temperature', 'environment_temperature', 'threshold']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                for _i, _k in enumerate(['a_temperature', 'b_temperature', 'c_temperature', 'environment_temperature', 'threshold']):
                    st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(k) or data[_k].get(k) == 0 else ''

            file_name = f"{station.name}-PCS温度{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-PCS Temperature{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationWaterCoolerView(APIView):
    """
     水冷机压力
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id or not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        # station_list = []
        # station_id_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # for s_info in slave_station:
        #     station_list.append(s_info.english_name)
        #     station_id_list.append(s_info.id)

        unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
        bms = unit.bms
        station_list = [unit.station.english_name]

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%Y-%m-%d %H:%i') as new_time, avg(opv), avg(ipv)
                                              FROM dwd_measure_bms_data_storage_3
                                              WHERE 
                                              time 
                                              BETWEEN '{}'
                                              AND '{}' 
                                              AND device = '{}'
                                              AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time, bms)

        if len(station_list) == 1:
            sql += "AND station_name = '{}'".format(station_list[0])
        else:
            sql += "AND station_name in {}".format(tuple(station_list))
        sql += " GROUP BY new_time ORDER BY new_time"
        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

        data = {
            'inlet': create_time_mapping(day=inquire_time),  # 进水口
            'outfall': create_time_mapping(day=inquire_time),  # 出水口
            'max_list': [],  # 压力最大值
            'min_list': [],  # 压力最小值
            'avg_list': [],  # 压力平均值,
            'max_pressure_threshold': create_time_mapping(day=inquire_time, value=2.8 if unit.v_number == 3 else 3.5),  # 压力阈值-最大值
            'min_pressure_threshold': create_time_mapping(day=inquire_time, value=0.3 if unit.v_number == 3 else 0.5)  # 压力阈值-最小值
        }
        for i in result:
            t = i[0]
            if i[1] is not None:
                if data['outfall'].get(t):
                    data['outfall'][t] = i[1]
            if i[2] is not None:
                if data['inlet'].get(t):
                    data['inlet'][t] = i[2]

        for i in ['inlet', 'outfall']:
            d = [v for v in data[i].values() if v != '--']
            if d:
                data['max_list'].append(max(d))
                data['min_list'].append(min(d))
                data['avg_list'].append(round(sum(d) / len(d), 2))
            else:
                data['max_list'].append('--')
                data['min_list'].append('--')
                data['avg_list'].append('--')

        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）

            if lang == 'zh':
                st.title = '水冷机压力'
                title = ['日期时间', '进水口压力（bar）', '出水口压力（bar）', '压力最大阈值（bar）', '压力最小阈值（bar）']
            else:
                st.title = 'LCS Pressure'
                title = ['DateTime', 'Inlet Pressure（bar）', 'Outlet Pressure（bar）', 'Maximum pressure threshold（bar）', 'Minimum pressure threshold（bar）']

            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['inlet', 'outfall', 'max_pressure_threshold', 'min_pressure_threshold']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                for _i, _k in enumerate(['inlet', 'outfall', 'max_pressure_threshold', 'min_pressure_threshold']):
                    st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(k) or data[_k].get(k) == 0 else ''

            file_name = f"{station.name}-水冷机压力{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-LCS Pressure{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationCellVoltgeView(APIView):
    """
     电芯电压
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id or not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permissions, please check the parameters.',
                    }
                }
            )
        # 处理主从模式
        # station_list = []
        # station_id_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # for s_info in slave_station:
        #     station_list.append(s_info.english_name)
        #     station_id_list.append(s_info.id)

        unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
        bms = unit.bms
        english_name = unit.station.english_name

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%Y-%m-%d %H:%i') as time, mumax, mumaxn, mumin, muminn, aveu
                                              FROM dwd_measure_bms_data_storage_3
                                              WHERE 
                                              time 
                                              BETWEEN '{}'
                                              AND '{}' 
                                              AND device = '{}'
                                              AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time, bms)

        # if len(station_list) == 1:
        sql += "AND station_name = '{}' ORDER BY time".format(english_name)
        # else:
        #     sql += "AND station_name in {}".format(tuple(station_list))

        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )
        data = {
            'max_voltage': create_time_mapping(day=inquire_time, value={'voltage': '--', 'number': '--'}),  # 最高单体电压
            'min_voltage': create_time_mapping(day=inquire_time, value={'voltage': '--', 'number': '--'}),  # 最低单体电压
            'avg_voltage': create_time_mapping(day=inquire_time, value={'voltage': '--'}),  # 平均单体电压
            'max_voltage_threshold': create_time_mapping(day=inquire_time, value=3.6),  # 电压最大阈值
            'min_voltage_threshold': create_time_mapping(day=inquire_time, value=2.8),  # 电压最小阈值
            '_max_voltage': {},  # 最高单体电压-统计量
            '_min_voltage': {},  # 最低单体电压-统计量
            '_avg_voltage': 0  # 平均单体电压-统计量
        }
        for i in result:
            t = i[0]
            if i[1] is not None:
                if data['max_voltage'].get(t):
                    data['max_voltage'][t] = {'voltage': round(i[1], 3), 'number': i[2]}
            if i[3] is not None:
                if data['min_voltage'].get(t):
                    data['min_voltage'][t] = {'voltage': round(i[3], 3), 'number': i[4]}
            if i[5] is not None:
                if data['avg_voltage'].get(t):
                    data['avg_voltage'][t] = {'voltage': round(i[5], 3)}

        for i in ['max_voltage', 'min_voltage', 'avg_voltage']:
            if i != 'avg_voltage':
                voltage_dict = {data[i].get(k).get('voltage'): [data[i].get(k).get('number'), k] for k in data[i].keys() if '--' not in data[i].get(k).values()}
                d = [v for v in voltage_dict.keys() if v != '--']
                if d:
                    if i == 'max_voltage':
                        data['_max_voltage'] = {'voltage': max(d), 'number': voltage_dict.get(max(d))[0], 'time': voltage_dict.get(max(d))[1]}
                    else:
                        data['_min_voltage'] = {'voltage': min(d), 'number': voltage_dict.get(min(d))[0], 'time': voltage_dict.get(min(d))[1]}
                else:
                    if i == 'max_voltage':
                        data['_max_voltage'] = {'voltage': '--', 'number': '--',
                                                'time': '--'}
                    else:
                        data['_min_voltage'] = {'voltage': '--', 'number': '--',
                                                'time': '--'}
            else:
                d = [v.get('voltage') for v in data[i].values() if '--' not in v.values()]
                data['_avg_voltage'] = round(sum(d) / len(d), 3) if d else '--'
        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '电芯电压'
                title = ['日期时间', '最高单体电压（V）', '最高单体电压编号', '最低单体电压（V）', '最低单体电压编号', '平均单体电压（V）', '电压最大阈值（V）', '电压最小阈值（V）']
            else:
                st.title = 'Cell Voltage'
                title = ['DateTime', 'Cell Voltage Max（V）', 'Cell Voltage Max No.', 'Cell Voltage Min（V）', 'Cell Voltage Min No.',
                         'Cell Voltage Mean Value（V）', 'Maximum voltage threshold（V）', 'Minimum voltage threshold（V）',]

            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['max_voltage', 'min_voltage', 'avg_voltage']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                index = 0
                for _i, _k in enumerate(
                        ['max_voltage', 'max_voltage_number', 'min_voltage', 'min_voltage_number', 'avg_voltage', 'max_voltage_threshold', 'min_voltage_threshold']):
                    if _k == 'max_voltage_number' or _k == 'min_voltage_number':
                        st.cell(row=max_row, column=_i + 2).value = data[_k[:-7]].get(k).get('number') if data[
                            _k[:-7]].get(k) or data[_k[:-7]].get(k) == 0 else ''
                    elif _k in ['max_voltage_threshold', 'min_voltage_threshold']:
                        st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(
                            k) or data[_k].get(k) == 0 else ''
                    else:
                        st.cell(row=max_row, column=_i + 2).value = data[_k].get(k).get('voltage') if data[_k].get(
                            k) or data[_k].get(k) == 0 else ''
                    index += 1

            file_name = f"{station.name}-电芯电压{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Cell Voltage{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationOperationCellTemperatureView(APIView):
    """
     电芯温度
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        inquire_time = request.query_params.get('inquire_time')  # 查询日期
        unit_id = request.query_params.get('unit_id')  # 储能单元ID
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not inquire_time or not id or not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 处理主从模式
        # station_list = []
        # station_id_list = []
        # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
        # for s_info in slave_station:
        #     station_list.append(s_info.english_name)
        #     station_id_list.append(s_info.id)

        unit = models.Unit.objects.filter(is_delete=0, id=unit_id).first()
        bms = unit.bms
        english_name = unit.station.english_name
        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%Y-%m-%d %H:%i') as time, mtmax, mtmaxn, mtmin, mtminn, avet
                                              FROM dwd_measure_bms_data_storage_3
                                              WHERE 
                                              time 
                                              BETWEEN '{}'
                                              AND '{}' 
                                              AND device = '{}'
                                              AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time, bms)

        # if len(station_list) == 1:
        sql += "AND station_name = '{}' ORDER BY time".format(english_name)
        # else:
        #     sql += "AND station_name in {}".format(tuple(station_list))

        # 连接清洗库
        with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
            try:
                # 获取查询结果
                dwd_cursor.execute(sql)
                result = dwd_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

        data = {
            'max_temperature': create_time_mapping(day=inquire_time, value={'temperature': '--', 'number': '--'}),  # 最高单体温度
            'min_temperature': create_time_mapping(day=inquire_time, value={'temperature': '--', 'number': '--'}),  # 最低单体温度
            'avg_temperature': create_time_mapping(day=inquire_time, value={'temperature': '--'}),  # 平均温度
            'max_temperature_threshold': create_time_mapping(day=inquire_time, value=45),  # 温度最大阈值
            'min_temperature_threshold': create_time_mapping(day=inquire_time, value=20),  # 温度最小阈值
            '_max_temperature': {
                'temperature': '--',
                'number': '--',
                'time': '--'
            },  # 最高单体温度-统计量
            '_min_temperature': {
                'temperature': '--',
                'number': '--',
                'time': '--'
            },  # 最低单体温度-统计量
            '_avg_temperature': '--'  # 平均温度-统计量
        }
        for i in result:
            t = i[0]
            if i[1] is not None:
                if data['max_temperature'].get(t):
                    data['max_temperature'][t] = {'temperature': round(i[1], 3), 'number': i[2]}
            if i[3] is not None:
                if data['min_temperature'].get(t):
                    data['min_temperature'][t] = {'temperature': round(i[3], 3), 'number': i[4]}
            if i[5] is not None:
                if data['avg_temperature'].get(t):
                    data['avg_temperature'][t] = {'temperature': round(i[5], 3)}

        for i in ['max_temperature', 'min_temperature', 'avg_temperature']:
            if i != 'avg_temperature':
                temperature_dict = {data[i].get(k).get('temperature'): [data[i].get(k).get('number'), k] for k in
                                data[i].keys() if '--' not in data[i].get(k).values()}
                d = temperature_dict.keys()
                if d:
                    if i == 'max_temperature':
                        data['_max_temperature'] = {'temperature': max(d), 'number': temperature_dict.get(max(d))[0], 'time': temperature_dict.get(max(d))[1]}
                    if i == 'min_temperature':
                        data['_min_temperature'] = {'temperature': min(d), 'number': temperature_dict.get(min(d))[0], 'time': temperature_dict.get(min(d))[1]}
            else:
                d = [v.get('temperature') for v in data[i].values() if '--' not in v.values()]
                if d:
                    data['_avg_temperature'] = round(sum(d) / len(d), 3)
        if is_download == '1':
            # 下载数据
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）

            if lang == 'zh':
                st.title = '电芯温度'
                title = ['日期时间', '最高温度（℃）', '最高温度编号', '最低温度（℃）', '最低温度编号', '平均温度（℃）', '温度最大阈值（℃）', '温度最小阈值（℃）']
            else:
                st.title = 'Cell Temperature'
                title = ['DateTime', 'Temperature Max（℃）', 'Temperature Max No.', 'Temperature Min（℃）', 'Temperature Min No.', 'Average Temperature（℃）', 'Maximum temperature threshold（℃）', 'Minimum temperature threshold（℃）']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            length_dict = {}  # 计算excel最大列
            for t in ['max_temperature', 'min_temperature', 'avg_temperature']:
                length_dict[t] = len(data[t].keys())
            length = sorted(length_dict.items(), key=lambda d: d[1], reverse=True)[0]

            for k in data[length[0]]:
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                index = 0
                for _i, _k in enumerate(
                        ['max_temperature', 'max_temperature_number', 'min_temperature', 'min_temperature_number',
                         'avg_temperature', 'max_temperature_threshold', 'min_temperature_threshold']):
                    if _k == 'max_temperature_number' or _k == 'min_temperature_number':
                        st.cell(row=max_row, column=_i + 2).value = data[_k[:-7]].get(k).get('number') if data[
                            _k[:-7]].get(k) or data[_k[:-7]].get(k) == 0 else ''
                    elif _k in ['max_temperature_threshold', 'min_temperature_threshold']:
                        st.cell(row=max_row, column=_i + 2).value = data[_k].get(k) if data[_k].get(
                            k) or data[_k].get(k) == 0 else ''
                    else:
                        st.cell(row=max_row, column=_i + 2).value = data[_k].get(k).get('temperature') if data[_k].get(
                            k) or data[_k].get(k) == 0 else ''
                    index += 1

            file_name = f"{station.name}-电芯温度{inquire_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{station.name}-Cell Temperature{inquire_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationStartYearView(APIView):
    """
    生产统计-项目初始年份
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        project_id = request.query_params.get('project_id')  # 项目ID

        if not project_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '项目ID字段缺失！' if lang == 'zh' else 'The project ID field is missing!',
                    }
                }
            )
        project = models.Project.objects.filter(id=project_id).first()
        if not project:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '项目ID不存在！' if lang == 'zh' else 'The project ID does not exist！',
                    }
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": project.in_time.year,
                }
            }
        )


class StationStatisticsKwhView(APIView):
    """
     生产统计-电量
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        project_id = request.query_params.get('project_id')  # 项目ID
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        start_time = request.query_params.get('start_time')  # 查询日期
        end_time = request.query_params.get('end_time')  # 查询日期
        time_type = request.query_params.get('time_type')  # 日期类型: 0:日 1:月 2:年
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not start_time or not end_time or not project_id or not time_type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed！',
                    }
                }
            )
        stations = models.MaterStation.objects.filter(project_id=project_id, is_delete=0)
        if id:
            stations = stations.filter(id=id)
        stations = stations.all()
        data = {
            'chag': {},  # 充电量 (尖峰平谷、深谷)
            'disg': {},  # 放电量 (尖峰平谷、深谷)
            'chag_statistics': [0, 0, 0, 0, 0],  # 充电量统计 (尖峰平谷、深谷)
            'disg_statistics': [0, 0, 0, 0, 0]  # 放电量统计 (尖峰平谷、深谷)
        }
        chag = 0
        chag_soc = 0
        chag_soc_list = []
        disg = 0
        disg_soc = 0
        disg_soc_list = []
        # 连接IDC
        is_account = False
        station_list = [] # 计量电表查询站
        # m_station_list = [-1]  # EMS;-1是防止in失败
        for station in stations:
            # m_station_list.append(station.english_name)

            # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
            # meter_use_time = models.MeterUseTime.objects.filter(station=station.master_station, is_use=1).first()
            # is_use_account = (station.master_station.is_account and meter_use_time and
            #                   start_time >= meter_use_time.start_time and end_time <= meter_use_time.end_time)
            #
            # if is_use_account == 1:  # 结算电表
            #     is_account = True
            # slave_station = station.stationdetails_set.filter(~Q(slave=0)).all()
            slave_station = station.stationdetails_set.filter(is_delete=0).all()
            for s_info in slave_station:
                station_list.append(s_info.english_name)

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:

            if time_type == '0':
                sql = """SELECT day, 
                                sum(pointed_chag), 
                                sum(peak_chag), 
                                sum(flat_chag), 
                                sum(valley_chag),
                                sum(dvalley_chag),
                                sum(pointed_disg), 
                                sum(peak_disg), 
                                sum(flat_disg), 
                                sum(valley_disg),
                                sum(dvalley_disg)
                          FROM ads_report_chag_disg_union_1d
                          WHERE 
                            station_type <= 1 
                          AND day 
                          BETWEEN '{}'
                          AND '{}'""".format(start_time, end_time)
                efficiency_sql = """
                                SELECT
                                    sum( v_chag ),
                                    sum( v_disg ),
                                    avg( chag_soc ),
                                    avg( disg_soc )
                                FROM
                                    ads_report_chag_disg_union_1d
                                WHERE
                                    station_type <= 1
                                AND day
                                BETWEEN '{}'
                                AND '{}'""".format(start_time, end_time)
                if len(station_list) == 1:
                    sql += "AND station = '{}'".format(station_list[0])
                    efficiency_sql += "AND station = '{}'".format(station_list[0])
                else:
                    sql += "AND station in {}".format(tuple(station_list))
                    efficiency_sql += "AND station in {}".format(tuple(station_list))
                sql += " GROUP BY day ORDER BY day"
            else:
                year_month = 'year_month' if time_type == '1' else 'year'
                sql = """SELECT date_value, 
                                sum(pointed_chag), 
                                sum(peak_chag), 
                                sum(flat_chag), 
                                sum(valley_chag),
                                sum(dvalley_chag),
                                sum(pointed_disg), 
                                sum(peak_disg), 
                                sum(flat_disg), 
                                sum(valley_disg),
                                sum(dvalley_disg)
                          FROM ads_report_chag_disg_union_cw_cm_cq_cy
                          WHERE 
                          station_type <= 1 
                          AND date_type='{}'
                          AND date_value 
                          BETWEEN '{}'
                          AND '{}'""".format(year_month, start_time,
                                             end_time)
                # efficiency_table = 'ads_report_chag_disg_cm' if time_type == '1' else 'ads_report_chag_disg_cy'
                efficiency_sql = """
                                SELECT
                                    sum( v_chag ),
                                    sum( v_disg ),
                                    avg( chag_soc ),
                                    avg( disg_soc )
                                FROM
                                    ads_report_chag_disg_union_cw_cm_cq_cy
                                WHERE
                                station_type <= 1
                                AND date_type='{}'
                                AND date_value
                                BETWEEN '{}'
                                AND '{}'""".format(year_month, start_time, end_time)
                if len(station_list) == 1:
                    sql += " AND station = '{}'".format(station_list[0])
                    efficiency_sql += "AND station = '{}'".format(station_list[0])
                else:
                    sql += " AND station in {}".format(tuple(station_list))
                    efficiency_sql += "AND station in {}".format(tuple(station_list))
                sql += f" GROUP BY date_value ORDER BY date_value"
            try:
                # 获取查询结果
                ads_cursor.execute(sql)
                result = ads_cursor.fetchall()
                ads_cursor.execute(efficiency_sql)
                efficiency_result = ads_cursor.fetchall()
            except Exception as e:
                raise e
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

            if efficiency_result[0][0] is not None and efficiency_result[0][1] is not None:
                chag = efficiency_result[0][0]
                disg = efficiency_result[0][1]
                chag_soc = efficiency_result[0][2]
                disg_soc = efficiency_result[0][3]

            for i in result:
                k = str(i[0])
                k = k if len(k) != 6 else k[:4] + '-' + k[4:]
                for y in range(5):
                    if data['chag'].get(k):
                        if y == 2 or y == 3:
                            data['chag'][k][y] += i[y + 1]
                        else:
                            data['chag'][k][y] += (i[y + 1] if not -1 < i[y + 1] < 1 else 0)
                    else:
                        data['chag'][k] = [i[1] if not -1 < i[1] < 1 else 0, i[2] if not -1 < i[2] < 1 else 0, i[3],
                                           i[4], i[5]]
                        break
                for y in range(5):
                    if data['disg'].get(k):
                        if y != 3:
                            data['disg'][k][y] += i[y + 6]
                        else:
                            data['disg'][k][y] += (i[y + 6] if not -1 < i[y + 6] < 1 else 0)
                    else:
                        data['disg'][k] = [i[6], i[7], i[8], i[9], i[10] if not -1 < i[9] < 1 else 0]
                        break

                # data['chag'][k] = [i[1], i[2], i[3], i[4]]
                # data['disg'][k] = [i[5], i[6], i[7], i[8]]
                data['chag_statistics'][0] += i[1]
                data['chag_statistics'][1] += i[2]
                data['chag_statistics'][2] += i[3]
                data['chag_statistics'][3] += i[4]
                data['chag_statistics'][4] += i[5]
                data['disg_statistics'][0] += i[6]
                data['disg_statistics'][1] += i[7]
                data['disg_statistics'][2] += i[8]
                data['disg_statistics'][3] += i[9]
                data['disg_statistics'][4] += i[10]
        discharge_efficiency = (disg / disg_soc) / (chag / chag_soc) if disg_soc != 0 and chag_soc != 0 and chag != 0 and disg != 0  else '--'  # 充放电效率
        if discharge_efficiency != '--':
            if discharge_efficiency > 1:
                discharge_efficiency = '--'
            elif discharge_efficiency < 0.85:
                discharge_efficiency = 0.85
            else:
                discharge_efficiency = round(discharge_efficiency, 4)
        data['discharge_efficiency'] = discharge_efficiency
        data['chag_statistics'].append(sum(data['chag_statistics']))  # 添加总计
        data['disg_statistics'].append(sum(data['disg_statistics']))  # 添加总计
        # 数据下载
        if is_download == '1':
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '电量'
                title = ['日期时间', '放电（尖）', '放电（峰）', '放电（平）', '放电（谷）', '放电（深谷）', '用电（尖）', '用电（峰）', '充电（平）', '充电（谷）', '充电（深谷）']
            else:
                st.title = 'Energy'
                title = ['DateTime', 'Rush-hour Energy Discharged', 'Peak-hour Energy Discharged', 'Flat-hour Energy Discharged', 'Valley-hour Energy Discharged', 'Deep-Valley-hour Energy Discharged',
                         'Rush-hour Energy Electricity',
                         'Peak-hour Energy Electricity', 'Flat-hour Energy Charged', 'Valley-hour Energy Charged', 'Deep-Valley-hour Energy Charged']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            disg = data['disg']
            for k, v in data['chag'].items():
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                st.cell(row=max_row, column=2).value = disg.get(k)[0]
                st.cell(row=max_row, column=3).value = disg.get(k)[1]
                st.cell(row=max_row, column=4).value = disg.get(k)[2]
                st.cell(row=max_row, column=5).value = disg.get(k)[3]
                st.cell(row=max_row, column=6).value = disg.get(k)[4]
                st.cell(row=max_row, column=7).value = v[0]
                st.cell(row=max_row, column=8).value = v[1]
                st.cell(row=max_row, column=9).value = v[2]
                st.cell(row=max_row, column=10).value = v[3]
                st.cell(row=max_row, column=11).value = v[4]
            name = models.Project.objects.get(id=project_id).name
            file_name = f"{name}-电量{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{name}-Energy{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationStatisticsIncomeView(APIView):
    """
     生产统计-收益
     """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        project_id = request.query_params.get('project_id')  # 项目ID
        id = request.query_params.get('id')  # 主站ID
        request_id = request.user["user_id"]
        start_time = request.query_params.get('start_time')  # 查询日期
        end_time = request.query_params.get('end_time')  # 查询日期
        time_type = request.query_params.get('time_type')  # 日期类型: 0:日 1:月 2:年
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not start_time or not end_time or not project_id or not time_type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        stations = models.StationDetails.objects.filter(project_id=project_id, is_delete=0)
        if id:
            stations = stations.filter(master_station_id=id)
        stations = stations.all()
        station_names = [i.english_name for i in stations]
        if len(station_names) == 1:
            station_names.append(-1)  # 防止查询异常
        data = {
            'income': {},  # 收益
            'chag_cost': [0, 0, 0, 0, 0],  # 充电成本-尖峰平谷、深谷
            'disg_income': [0, 0, 0, 0, 0],  # 放电收入-尖峰平谷、深谷
            'total': [0, 0, 0]  # 合计收益、目标收益、收益完成率
        }
        if time_type == '0':
            exclude_time = '2024-04-01'
            if start_time < exclude_time:
                start_time = exclude_time
            income_sql = f"""SELECT 
                                SUBSTRING( day, 1, 10 ) as DAY,
	                            sum( dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income ),
                                sum(base_income),
                                sum(pointed_chag_income),
                                sum(peak_chag_income),
                                sum(flat_chag_income),
                                sum(valley_chag_income),
                                sum(dvalley_chag_income),
                                sum(pointed_disg_income),
                                sum(peak_disg_income),
                                sum(flat_disg_income),
                                sum(valley_disg_income),
                                sum(dvalley_disg_income)
                            FROM
                                ads_report_station_income_1d 
                            WHERE
                                DAY >= '{start_time}' AND DAY <= '{end_time}' AND
                                station in {tuple(station_names)}  GROUP BY `day` ORDER BY `day`"""

        else:
            if time_type == '1':  # 月
                exclude_time = '202404'
                s_time = start_time[:4] + start_time[5:7]
                e_time = end_time[:4] + end_time[5:7]
                if s_time < exclude_time:
                    s_time = exclude_time
                income_sql = f"""SELECT 
                                          CONCAT(SUBSTRING(date_value,1,4) , '-' , SUBSTRING(date_value,5,7)) as day,
                                          sum( dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income ),
                                          sum(base_income),
                                          sum(pointed_chag_income),
                                          sum(peak_chag_income),
                                          sum(flat_chag_income),
                                          sum(valley_chag_income),
                                          sum(dvalley_chag_income),
                                          sum(pointed_disg_income),
                                          sum(peak_disg_income),
                                          sum(flat_disg_income),
                                          sum(valley_disg_income),
                                          sum(dvalley_disg_income)
                                      FROM
                                          ads_report_station_income_cw_cm_cy_tt 
                                      WHERE
                                          date_value >= '{s_time}' AND date_value <= '{e_time}' AND
                                          station in {tuple(station_names)} AND date_type = 'year_month' GROUP BY `day` ORDER BY `day`"""
            else:
                exclude_time = '202404'
                s_time = start_time[:4] + start_time[5:7]
                e_time = end_time[:4] + end_time[5:7]
                if s_time < exclude_time:
                    s_time = exclude_time
                income_sql = f"""SELECT 
                                      SUBSTRING(date_value,1,4)  as day,
                                      sum( dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income ),
                                      sum(base_income),
                                      sum(pointed_chag_income),
                                      sum(peak_chag_income),
                                      sum(flat_chag_income),
                                      sum(valley_chag_income),
                                      sum(dvalley_chag_income),
                                      sum(pointed_disg_income),
                                      sum(peak_disg_income),
                                      sum(flat_disg_income),
                                      sum(valley_disg_income),
                                      sum(dvalley_disg_income)
                                  FROM
                                      ads_report_station_income_cw_cm_cy_tt 
                                  WHERE
                                          date_value >= '{s_time}' AND date_value <= '{e_time}' AND
                                          station in {tuple(station_names)} AND date_type = 'year_month' GROUP BY `day` ORDER BY `day`"""

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                print(income_sql)
                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error("生产统计-收益查询失败：", e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'QUERY FAILURE！',
                        }
                    }
                )

        for income in income_res:
            t = income[0]
            if not data['income'].get(t):
                data['income'][t] = {
                    'income': 0,
                    'target_income': 0,
                    'reach_yield': 0
                }
                data['total'][1] += round(income[2], 2) if income[2] not in EMPTY_STR_LIST else 0
                data['income'][t]['income'] = round(float(income[1]), 2) if income[1] not in EMPTY_STR_LIST else '--'
                data['income'][t]['target_income'] = round(float(income[2]), 2) if income[2] not in EMPTY_STR_LIST else '--'
                data['income'][t]['reach_yield'] = round(data['income'][t]['income'] / data['income'][t]['target_income'] * 100, 2) if data['income'][t]['income'] != '--' and data['income'][t]['target_income'] != '--' and data['income'][t]['target_income'] != 0 else '--'
                data['chag_cost'][0] += income[3] if income[3] not in EMPTY_STR_LIST else 0
                data['chag_cost'][1] += income[4] if income[4] not in EMPTY_STR_LIST else 0
                data['chag_cost'][2] += income[5] if income[5] not in EMPTY_STR_LIST else 0
                data['chag_cost'][3] += income[6] if income[6] not in EMPTY_STR_LIST else 0
                data['chag_cost'][4] += income[7] if income[7] not in EMPTY_STR_LIST else 0
                data['disg_income'][0] += income[8] if income[8] not in EMPTY_STR_LIST else 0
                data['disg_income'][1] += income[9] if income[9] not in EMPTY_STR_LIST else 0
                data['disg_income'][2] += income[10] if income[10] not in EMPTY_STR_LIST else 0
                data['disg_income'][3] += income[11] if income[11] not in EMPTY_STR_LIST else 0
                data['disg_income'][4] += income[12] if income[12] not in EMPTY_STR_LIST else 0
        data['total'][2] = round((data['total'][0] / data['total'][1]) * 100, 2) if data['total'][1] > 0 else 0  # 达产率
        data['chag_cost'].append(sum(data['chag_cost']))
        data['disg_income'].append(sum(data['disg_income']))
        for i in range(6):
            data['chag_cost'][i] = round(data['chag_cost'][i], 2)
            data['disg_income'][i] = round(data['disg_income'][i], 2)
        data['total'][0] = round(data['disg_income'][5] - data['chag_cost'][5], 2)  # 合计收益
        data['total'][2] = round((data['total'][0] / data['total'][1]) * 100, 2) if data['total'][1] > 0 else 0  # 达产率
        data['total'][1] = round(data['total'][1], 2)
        # 数据下载
        if is_download == '1':
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）

            if lang == 'zh':
                st.title = '收益'
                title = ['日期时间', '收益', '目标收益', '收益完成率']
            else:
                st.title = 'Profit'
                title = ['DateTime', 'Profit', 'Target Profit', 'Profit Completion Rate']

            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            for k, v in data['income'].items():
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                st.cell(row=max_row, column=2).value = v.get('income')
                st.cell(row=max_row, column=3).value = v.get('target_income')
                st.cell(row=max_row, column=4).value = v.get('reach_yield')
            name = models.Project.objects.get(id=project_id).name
            file_name = f"{name}-收益{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{name}-Profit-{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationStatisticsKilowattIncomeView(APIView):
    """
    生产统计-度电收益
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 主站ID
        project_id = request.query_params.get('project_id')  # 项目ID
        request_id = request.user["user_id"]
        start_time = request.query_params.get('start_time')  # 查询日期
        end_time = request.query_params.get('end_time')  # 查询日期
        time_type = request.query_params.get('time_type')  # 日期类型: 0:日 1:月 2:年
        is_download = request.query_params.get('is_download')  # 是否下载数据 1：下载
        if not start_time or not end_time or not project_id or not time_type:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field check failed!',
                    }
                }
            )
        stations = models.StationDetails.objects.filter(project_id=project_id, is_delete=0)
        if id:
            stations = stations.filter(master_station_id=id)
        stations = stations.all()
        station_names = [i.english_name for i in stations]
        if len(station_names) == 1:
            station_names.append(-1)  # 防止查询异常
        data = {
            'kilowatt_income': {},  # 度电收益
            'chag': 0,  # 充电量
            'chag_income': 0,  # 充电成本
            'disg': 0,  # 放电量
            'disg_income': 0,  # 放电收入
            'income_all': 0,  # 合计收益
            'kilowatt_income_all': 0  # 合计度电收益
        }

        if time_type == '0':
            exclude_time = '2024-04-01'
            if start_time < exclude_time:
                start_time = exclude_time
            income_sql = f"""SELECT
                                ele.day, ele.chag, incomes.chag_income, ele.disg, incomes.disg_income, incomes.income
                            FROM
                                (
                                SELECT SUBSTRING( day, 1, 10 ) as DAY
                                    ,
                                    sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income) AS income,
                                    sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income) as disg_income,
                                    sum(dvalley_chag_income + valley_chag_income + flat_chag_income + peak_chag_income + pointed_chag_income) as chag_income
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                    DAY >= '{start_time}' 
                                    AND DAY <= '{end_time}' 
                                    AND station IN {tuple(station_names)}
                                   
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY 
                                ) AS incomes
                                INNER JOIN (
                                SELECT SUBSTRING( day, 1, 10 ) as DAY
                                    ,
                                    sum(pointed_disg + peak_disg + flat_disg + valley_disg + dvalley_disg) as disg,
                                    sum(pointed_chag + peak_chag + flat_chag + valley_chag + dvalley_chag) as chag
                                FROM
                                    ads_report_chag_disg_union_1d 
                                WHERE
                                    DAY >= '{start_time}' 
                                    AND DAY <= '{end_time}' 
                                    AND station IN {tuple(station_names)}
                                    AND station_type <= 1 
                                GROUP BY
                                DAY 
                                ORDER BY
                            DAY 
                                ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day"""

        else:
            if time_type == '1':  # 月
                exclude_time = '202404'
                s_time = start_time[:4] + start_time[5:7]
                e_time = end_time[:4] + end_time[5:7]
                if s_time < exclude_time:
                    s_time = exclude_time
                income_sql = f"""SELECT
                                    ele.day, ele.chag, incomes.chag_income, ele.disg, incomes.disg_income, incomes.income
                                FROM
                                    (
                                    SELECT CONCAT(SUBSTRING(date_value,1,4) , '-' , SUBSTRING(date_value,5,7)) as day
                                        ,
                                        sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income) AS income,
                                        sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income) as disg_income,
                                        sum(dvalley_chag_income + valley_chag_income + flat_chag_income + peak_chag_income + pointed_chag_income) as chag_income
                                    FROM
                                        ads_report_station_income_cw_cm_cy_tt 
                                    WHERE
                                        date_value >= '{s_time}' 
                                        AND date_value <= '{e_time}' 
                                        AND station IN {tuple(station_names)}
                                        AND date_type = 'year_month'
                                    GROUP BY
                                    day 
                                    ORDER BY
                                    day 
                                    ) AS incomes
                                    INNER JOIN (
                                   SELECT CONCAT(SUBSTRING(date_value,1,4) , '-' , SUBSTRING(date_value,5,7)) as day
                                        ,
                                        sum(pointed_disg + peak_disg + flat_disg + valley_disg + dvalley_disg) as disg,
                                        sum(pointed_chag + peak_chag + flat_chag + valley_chag + dvalley_chag) as chag
                                    FROM
                                        ads_report_chag_disg_union_cw_cm_cq_cy 
                                    WHERE
                                        date_value >= '{s_time}' 
                                        AND date_value <= '{e_time}' 
                                        AND station IN {tuple(station_names)}
                                        AND date_type = 'year_month'
                                        AND station_type <= 1 
                                    GROUP BY
                                    day 
                                    ORDER BY
                                    day 
                                    ) AS ele ON ele.day = incomes.day ORDER BY incomes.day"""
            else:
                exclude_time = '202404'
                s_time = start_time[:4] + start_time[5:7]
                e_time = end_time[:4] + end_time[5:7]
                if s_time < exclude_time:
                    s_time = exclude_time
                income_sql = f"""SELECT
                                     ele.day, ele.chag, incomes.chag_income, ele.disg, incomes.disg_income, incomes.income
                                 FROM
                                     (
                                     SELECT SUBSTRING(date_value,1,4) as day
                                         ,
                                         sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income - dvalley_chag_income - valley_chag_income - flat_chag_income - peak_chag_income - pointed_chag_income) AS income,
                                         sum(dvalley_disg_income + valley_disg_income + flat_disg_income + peak_disg_income + pointed_disg_income) as disg_income,
                                         sum(dvalley_chag_income + valley_chag_income + flat_chag_income + peak_chag_income + pointed_chag_income) as chag_income
                                     FROM
                                         ads_report_station_income_cw_cm_cy_tt 
                                     WHERE
                                         date_value >= '{s_time}' 
                                         AND date_value <= '{e_time}' 
                                         AND station IN {tuple(station_names)}
                                         AND date_type = 'year_month'
                                     GROUP BY
                                     day 
                                     ORDER BY
                                     day 
                                     ) AS incomes
                                     INNER JOIN (
                                    SELECT SUBSTRING(date_value,1,4) as day
                                         ,
                                         sum(pointed_disg + peak_disg + flat_disg + valley_disg + dvalley_disg) as disg,
                                         sum(pointed_chag + peak_chag + flat_chag + valley_chag + dvalley_chag) as chag
                                     FROM
                                         ads_report_chag_disg_union_cw_cm_cq_cy 
                                     WHERE
                                         date_value >= '{s_time}' 
                                         AND date_value <= '{e_time}' 
                                         AND station IN {tuple(station_names)}
                                         AND date_type = 'year_month'
                                         AND station_type <= 1 
                                     GROUP BY
                                     day 
                                     ORDER BY
                                        day 
                                     ) AS ele ON ele.day = incomes.day ORDER BY incomes.day"""

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:

                ads_cursor.execute(income_sql)
                income_res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error("生产统计-收益查询失败：", e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'QUERY FAILURE！',
                        }
                    }
                )

        for i in income_res:
            chag = i[1] if i[1] not in EMPTY_STR_LIST else 0
            disg = i[3] if i[3] not in EMPTY_STR_LIST else 0
            chag_income = i[2] if i[2] not in EMPTY_STR_LIST else 0
            disg_income = i[4] if i[4] not in EMPTY_STR_LIST else 0
            data['chag'] += chag
            data['chag_income'] += chag_income
            data['disg'] += disg
            data['disg_income'] += disg_income
            kilowatt_income = round((disg_income - chag_income) / disg, 3) if disg > 0 else 0
            data['kilowatt_income'][i[0]] = 0 if kilowatt_income < 0 else kilowatt_income
        data['income_all'] = round(float(data['disg_income'] - data['chag_income']), 3)
        kilowatt_income_all = round(float((data['disg_income'] - data['chag_income'])) / float(data['disg']), 3) if data['disg'] else 0
        data['kilowatt_income_all'] = 0 if kilowatt_income_all < 0 else kilowatt_income_all
        data['chag'] = round(float(data['chag']), 3)
        data['chag_income'] = round(float(data['chag_income']), 3)
        data['disg'] = round(float(data['disg']), 3)
        data['disg_income'] = round(float(data['disg_income']), 3)
        # # 查询尖峰平谷收益
        # if time_type == '0':
        #     time_type = {'day': "DATE_FORMAT(day, '%%Y-%%m-%%d')"}
        # elif time_type == '1':
        #     time_type = {'day': "DATE_FORMAT(day, '%%Y-%%m')"}
        # else:
        #     time_type = {'day': "DATE_FORMAT(day, '%%Y')"}
        # income = models.PeakValleyIncome.objects.extra(select=time_type).values('day').filter(master_station_id__in=station_ids, day__gte=start_time, day__lte=end_time).order_by('day').annotate(
        #     pointed_chag_income=Sum('pointed_chag_income'), peak_chag_income=Sum('peak_chag_income'), flat_chag_income=Sum('flat_chag_income'),
        #     valley_chag_income=Sum('valley_chag_income'), dvalley_chag_income=Sum('dvalley_chag_income'), pointed_disg_income=Sum('pointed_disg_income'), peak_disg_income=Sum('peak_disg_income'),
        #     flat_disg_income=Sum('flat_disg_income'), valley_disg_income=Sum('valley_disg_income'), dvalley_disg_income=Sum('dvalley_disg_income'), pointed_chag=Sum('pointed_chag'),
        #     peak_chag=Sum('peak_chag'), flat_chag=Sum('flat_chag'), valley_chag=Sum('valley_chag'), dvalley_chag=Sum('dvalley_chag'), pointed_disg=Sum('pointed_disg'),
        #     peak_disg=Sum('peak_disg'), flat_disg=Sum('flat_disg'), valley_disg=Sum('valley_disg'), dvalley_disg=Sum('dvalley_disg')
        # )
        # for i in income:
        #     chag = i.get('pointed_chag') + i.get('peak_chag') + i.get('flat_chag') + i.get('valley_chag') + i.get('dvalley_chag')
        #     disg = i.get('pointed_disg') + i.get('peak_disg') + i.get('flat_disg') + i.get('valley_disg') + i.get('dvalley_disg')
        #     chag_income = i.get('pointed_chag_income') + i.get('peak_chag_income') + i.get('flat_chag_income') + i.get('valley_chag_income') + i.get('dvalley_chag_income')
        #     disg_income = i.get('pointed_disg_income') + i.get('peak_disg_income') + i.get('flat_disg_income') + i.get('valley_disg_income') + i.get('dvalley_disg_income')
        #     data['chag'] += chag
        #     data['chag_income'] += chag_income
        #     data['disg'] += disg
        #     data['disg_income'] += disg_income
        #     kilowatt_income = round((disg_income - chag_income) / disg, 3) if disg > 0 else 0
        #     data['kilowatt_income'][i.get('day')] = 0 if kilowatt_income < 0 else kilowatt_income
        #
        # data['income_all'] = round(data['disg_income'] - data['chag_income'], 3)
        # kilowatt_income_all = round((data['disg_income'] - data['chag_income']) / data['disg'], 3) if data['disg'] else 0
        # data['kilowatt_income_all'] = 0 if kilowatt_income_all < 0 else kilowatt_income_all
        # data['chag'] = round(data['chag'], 3)
        # data['chag_income'] = round(data['chag_income'], 3)
        # data['disg'] = round(data['disg'], 3)
        # data['disg_income'] = round(data['disg_income'], 3)

        # 数据下载
        if is_download == '1':
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            if lang == 'zh':
                st.title = '度电收益'
                title = ['日期时间', '度电收益']
            else:
                st.title = 'Normalized Energy'
                title = ['DateTime', 'Normalized Energy']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            for k, v in data['kilowatt_income'].items():
                max_row = st.max_row + 1
                st.cell(row=max_row, column=1).value = k
                st.cell(row=max_row, column=2).value = v

            name = models.Project.objects.get(id=project_id).name
            file_name = f"{name}-度电收益{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{name}-Normalized Energy{start_time}-{end_time}--{int(time.time() * 1000)}.xlsx"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            wb.save(file_path)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": file_path}
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationStatisticsIncomeTask(APIView):
    """
    峰谷标识充放电量、收益手动任务
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        station_statistics_income(start_time, end_time)
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": [],
            },
        })


class StationIncomeTask(APIView):
    """
    实际收益手动任务
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        from common.database_pools import ads_db_tool
        from tools.count import get_station_price_optimize
        start_time = request.query_params.get('start_time')
        # time_ins = datetime.datetime.now() - datetime.timedelta(days=1)
        # time_str = time_ins.strftime("%Y-%m-%d")
        time_ins = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        time_str = start_time
        # year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
        error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

        # 改查新综合过结算表和计量表的新1h冻结表：表名未定
        select_sql = (
            "SELECT day,station, pointed_chag, pointed_disg, peak_chag, peak_disg, flat_chag, flat_disg, valley_chag, valley_disg, dvalley_chag, dvalley_disg FROM ads_report_chag_disg_union_1d"
            " where station_type<=1 and day=%s")

        # select_sql_2 = (
        #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
        #     " where station=%s and day=%s order by hour")

        # 查询当月所有电价
        day_price_res = models.PeakValleyNew.objects.filter(year_month=time_ins.strftime("%Y-%m")).all()
        price_new_dict = {}
        for price in day_price_res:
            k = f"{price.province_id}-{price.type}-{price.level}"  # 定义唯一key
            pv_k = price.pv  # 小时峰谷标识
            if price_new_dict.get(k):
                if price_new_dict.get(k).get(pv_k):
                    continue
                else:
                    price_new_dict[k][pv_k] = price.price
            else:
                price_new_dict[k] = {
                    pv_k: price.price
                }

        stations_ins = models.StationDetails.objects.filter(is_delete=0, id=358).all()

        # 查询所有电站的电量数据
        all_data = {}
        stations_data = ads_db_tool.select_many(select_sql, time_str)
        if stations_data:
            for s_data in stations_data:
                all_data[s_data['station']] = s_data

        for station in stations_ins:
            try:
                # province_ins = station.province
                # 先判断是否有手动添加的收益记录
                is_exists_station_income = models.StationIncome.objects.filter(
                    station_id=station,
                    master_station=station.master_station,
                    income_date__day=time_ins.day,
                    income_date__month=time_ins.month,
                    income_date__year=time_ins.year,
                    record=1,
                ).exists()

                # 不存在手动添加的收益记录
                if not is_exists_station_income:

                    total = 0

                    i = all_data.get(station.english_name)
                    if i:

                        temp_dict = {
                            "pointed_chag": float(i["pointed_chag"]),
                            "pointed_disg": float(i["pointed_disg"]),
                            "peak_chag": float(i["peak_chag"]),
                            "peak_disg": float(i["peak_disg"]),
                            "flat_chag": float(i["flat_chag"]),
                            "flat_disg": float(i["flat_disg"]),
                            "valley_chag": float(i["valley_chag"]),
                            "valley_disg": float(i["valley_disg"]),
                            "dvalley_chag": float(i["dvalley_chag"]),
                            "dvalley_disg": float(i["dvalley_disg"])
                        }

                        price_dict = get_station_price_optimize(station, time_ins, price_new_dict)

                        # print(1486, temp_dict, price_dict)

                        # error_log.error(
                        #     f"######{station.station_name}-{time_ins}计算收益使用电价为：尖峰电价：{price_dict['spike_chag_price']}--平峰电价：{price_dict['flat_price']}--谷峰电价：{price_dict['valley_price']}"){temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}, 峰谷标识：{temp_dict['type']}")

                        moment_income = (round(temp_dict['pointed_disg'] * price_dict['spike_disg_price'], 4) - round(
                            temp_dict['pointed_chag'] * price_dict['spike_chag_price'], 4) +
                                         round((temp_dict['peak_disg'] * price_dict['peak_disg_price']), 4) - round(
                                    temp_dict['peak_chag'] * price_dict['peak_chag_price'], 4) +
                                         round((temp_dict['flat_disg'] * price_dict['flat_disg_price']), 4) - round(
                                    temp_dict['flat_chag'] * price_dict['flat_chag_price'], 4) +
                                         round((temp_dict['valley_disg'] * price_dict['valley_disg_price']), 4) - round(
                                    temp_dict['valley_chag'] * price_dict['valley_chag_price'], 4) +
                                         round((temp_dict['dvalley_disg'] * price_dict['dvalley_disg_price']),
                                               4) - round(
                                    temp_dict['dvalley_chag'] * price_dict['dvalley_chag_price'], 4))

                        total = round(moment_income, 2)

                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{round(total, 2)}元")
                        if total < 0:
                            total = 0
                            error_log.error(
                                f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
                        income_ins = models.StationIncome.objects.filter(
                            station_id=station,
                            master_station=station.master_station,
                            income_date__day=time_ins.day,
                            income_date__month=time_ins.month,
                            income_date__year=time_ins.year,
                            record=2,
                        )
                        if not income_ins.exists():
                            try:
                                models.StationIncome.objects.create(
                                    peak_load_shifting=round(total, 2), station_id=station, income_date=time_ins,
                                    income_type=1,
                                    record=2, master_station=station.master_station
                                )
                            except Exception as e:
                                raise e
                        else:
                            income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
                    else:
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

                # 已存在手动添加的收入 不进行自动添加
                else:
                    error_log.error(
                        f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
            except Exception as e:
                error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')
                print(traceback.print_exc())

        error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": [],
            },
        })