#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-12 09:27:05


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseArea(user_Base):
    u'用户侧预算区域划分'
    __tablename__ = "t_side_forecase_area"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"所属区域")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=False,comment=u"排序索引")

    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(ForecaseArea(id=1,name='华北',is_use=1,index=1));
        user_session.merge(ForecaseArea(id=2,name='东北',is_use=1,index=2));
        user_session.merge(ForecaseArea(id=3,name='华东',is_use=1,index=3));
        user_session.merge(ForecaseArea(id=4,name='华中',is_use=1,index=4));
        user_session.merge(ForecaseArea(id=5,name='西南',is_use=1,index=5));
        user_session.merge(ForecaseArea(id=6,name='西北',is_use=1,index=6));
        user_session.merge(ForecaseArea(id=7,name='华南',is_use=1,index=7));
        user_session.merge(ForecaseArea(id=8,name='蒙西',is_use=1,index=8));
       
        user_session.commit()
        user_session.close()
        
    
    def __repr__(self):
        
        return "{'id':%s,'name':'%s','is_use':%s}" % (self.id,self.name,self.is_use)
        
    