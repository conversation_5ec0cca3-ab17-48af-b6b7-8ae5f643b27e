# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/30 下午3:05
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : serializers.py
# @Software : PyCharm
import json
from datetime import datetime

from rest_framework import serializers

from apis.user.models import RunningAnalysisFeedback
from LocaleTool.common import redis_pool


class AnalysisSerializer(serializers.Serializer):
    station_id = serializers.CharField(required=False)
    station_name = serializers.Char<PERSON>ield(required=False)
    topic = serializers.Char<PERSON><PERSON>(required=False, max_length=256)
    status = serializers.Char<PERSON>ield(required=False, max_length=128)
    start_time = serializers.DateField(input_formats=['%Y-%m-%d'], required=False)
    end_time = serializers.DateField(input_formats=['%Y-%m-%d'], required=False)
    # keyword = serializers.<PERSON><PERSON><PERSON><PERSON>(required=False, max_length=32)

    def validate(self, attrs):
        """校验取值范围"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")
        status = attrs.get('status')
        # type_ = attrs.get('type')

        # if start_time:
        #     try:
        #         datetime.strptime(start_time, '%Y-%m-%d')
        #     except ValueError:
        #         raise Exception('Invalid start_time format. Use YYYY-MM-DDS.')
        #
        # if end_time:
        #     try:
        #         datetime.strptime(end_time, '%Y-%m-%dS')
        #     except ValueError:
        #         raise Exception('Invalid end_time format. Use YYYY-MM-DD.')

        if start_time and end_time:
            # start_time_ = str(start_time)
            # end_time_ = str(end_time).replace('00:00:00', '23:59:59')
            # start_datatime = datetime.strptime(start_time_, "%Y-%m-%d")
            # end_datatime = datetime.strptime(end_time_, "%Y-%m-%d")

            if start_time > end_time:
                raise Exception('开始时间不能晚于结束时间.')
        if status:
            if int(status) not in [0, 1]:
                raise Exception('状态字段取值范围不在[0,1]范围内.')

        return attrs


class AnalysisAlarmSerializer(serializers.Serializer):
    # station_id = serializers.IntegerField(required=False)
    # topic = serializers.CharField(required=False, max_length=8)
    # status = serializers.CharField(required=False, max_length=8)
    start_time = serializers.DateField(input_formats=['%Y-%m-%d'], required=False)
    end_time = serializers.DateField(input_formats=['%Y-%m-%d'], required=False)
    # keyword = serializers.CharField(required=False, max_length=32)

    def validate(self, attrs):
        """校验取值范围"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")
        # status = attrs.get('status')
        # type_ = attrs.get('type')

        # if start_time:
        #     try:
        #         datetime.strptime(start_time, '%Y-%m-%d')
        #     except ValueError:
        #         raise Exception('Invalid start_time format. Use YYYY-MM-DDS.')
        #
        # if end_time:
        #     try:
        #         datetime.strptime(end_time, '%Y-%m-%dS')
        #     except ValueError:
        #         raise Exception('Invalid end_time format. Use YYYY-MM-DD.')

        if start_time and end_time:
            # start_time_ = str(start_time)
            # end_time_ = str(end_time).replace('00:00:00', '23:59:59')
            # start_datatime = datetime.strptime(start_time_, "%Y-%m-%d")
            # end_datatime = datetime.strptime(end_time_, "%Y-%m-%d")

            if start_time > end_time:
                raise Exception('开始时间不能晚于结束时间.')
        # if status:
        #     if int(status) not in [0, 1]:
        #         raise Exception('Invalid status.')

        return attrs


class AnalysisFeedbackSerializer(serializers.Serializer):
    content = serializers.CharField(required=True, max_length=256)
    is_dispatch_worker = serializers.BooleanField(required=False)
    worker_order = serializers.CharField(required=False, max_length=64)
    question_type = serializers.CharField(required=True, max_length=64)
    expected_closing_date = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    real_closing_date = serializers.DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'], required=False)
    note = serializers.CharField(required=False, max_length=256)

    class Meta:
        model = RunningAnalysisFeedback
        # exclude = ['feedback_time', "id"]
        fields = '__all__'
        read_only_fields = ('id', 'feedback_time', 'feedback_user')

    def validate(self, attrs):

        expected_closing_date = attrs.get("expected_closing_date")
        if expected_closing_date:
            if expected_closing_date < datetime.now():
                raise Exception("预计闭环日期不能小于当前日期" if self.context.get('lang') == 'zh' else
                                "Expected closing date cannot be less than the current date.")

        return attrs

    def create(self, validated_data):
        lang = self.context.get('lang')
        if lang == 'zh':
            ins = RunningAnalysisFeedback.objects.create(**validated_data,
                                                          feedback_user_id=self.context["user_id"],
                                                          feedback_time=datetime.now())

        else:
            ins = RunningAnalysisFeedback.objects.create(**validated_data,
                                                          en_content=validated_data.get("content"),
                                                          en_question_type=validated_data.get("question_type"),
                                                          en_note=validated_data.get("note"),
                                                          feedback_user_id=self.context["user_id"],
                                                          feedback_time=datetime.now())

        # 异步翻译
        pdr_data = {'id': ins.id,
                    'table': 't_running_analysis_feedback',
                    'update_data': {'content': validated_data.get("content"),
                                    'question_type': validated_data.get("question_type")}}
        if validated_data.get('note'):
            pdr_data['update_data']['note'] = validated_data.get('note')

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return ins

    def update(self, instance, validated_data):
        lang = self.context.get('lang')
        instance.content = validated_data.get("feedback", instance.feedback)
        instance.en_content = validated_data.get("feedback", instance.feedback)
        instance.feedback_time = datetime.now()
        instance.feedback_user_id = self.context["user_id"]
        instance.is_dispatch_worker = validated_data.get("is_dispatch_worker", instance.is_dispatch_worker)
        instance.worker_order = validated_data.get("worker_order", instance.worker_order)
        instance.question_type = validated_data.get("question_type", instance.question_type)
        instance.en_question_type = validated_data.get("question_type", instance.question_type)
        instance.note = validated_data.get("note", instance.note)
        instance.en_note = validated_data.get("note", instance.note)
        # instance.feedback_content = validated_data.get("feedback_content", instance.feedback_content)
        instance.expected_closing_date = validated_data.get("expected_closing_date", instance.expected_closing_date)
        instance.save()

        # 异步翻译
        pdr_data = {'id': instance.id,
                    'table': 't_running_analysis_feedback',
                    'update_data': {'content': validated_data.get("content"),
                                    'question_type': validated_data.get("question_type")}}
        if validated_data.get('note'):
            pdr_data['update_data']['note'] = validated_data.get('note')

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return instance


class AnalysisFeedbackDetailSerializer(serializers.Serializer):

    class Meta:
        model = RunningAnalysisFeedback
        # exclude = ['feedback_time', "id"]
        fields = '__all__'
