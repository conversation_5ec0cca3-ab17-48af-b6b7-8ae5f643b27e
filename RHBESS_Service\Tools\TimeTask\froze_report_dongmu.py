#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> 东睦报表冻结

import sys,getopt

from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
from Application.Models.User.report_dongmu_f import FReportDongmu
from Tools.Utils.num_utils import *
from Tools.DB.mysql_user import user_session
from Tools.DB.mysql_his import dongmu_session
from Tools.Utils.time_utils import timeUtils
from Application.Models.His.r_ACDMS import  HisDM
from apscheduler.schedulers.blocking import BlockingScheduler
import json
import logging
logging.basicConfig()

#查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

path = basepath + "/Application/Cfg/test.ini"
model_config.read(path,encoding='utf-8')
dongmu_num = 7

def frozeDongMuDataDay_(day):
    # 冻结一天的数据（东睦）
    startT = ('%s 00:00:01' % day)
    endT = ('%s 23:59:59' % day)

    st = timeUtils.timeStrToTamp(startT)  # 起始时间绝对秒
    ed = timeUtils.timeStrToTamp(endT)  # 截止时间绝对秒
    dm_table = HisDM('r_cumulant')
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()

    if values_mong:
        values_1_s = {'datainfo': {}}
        values_1_e = {'datainfo': {}}
        values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
        values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
        value_s = json.loads(values_1_s['datainfo'])['body']
        value_e = json.loads(values_1_e['datainfo'])['body']
        BDcap_cu_s = []  # 电池总放电量
        BCCap_cu_s = []  # 电池总充电量
        for r in range(7):
            for ii in value_s:
                if ii['device'][:4] == 'PCS%s' % (r + 1):
                    BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                    BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
        BDcap_cu_e = []  # 电池总放电量
        BCCap_cu_e = []  # 电池总充电量
        for r in range(7):
            for ii in value_e:
                if ii['device'][:4] == 'PCS%s' % (r + 1):
                    BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                    BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
        BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
        BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量
        e = 1
        for f in BDcap_cu.tolist():  #
            if BCCap_cu.tolist()[e - 1] == 0:
                ratio = 0
            else:
                ratio = ('%.3f' % ((f / BCCap_cu.tolist()[e - 1]) * 100))
                if float(ratio) > 100:
                    ratio = 100.0
                elif float(ratio) < 0:
                    ratio = 0
            print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % e)
            user_session.merge(FReportBmsDongmu(pcs_name='PCS-%s' % e, cu_name='电池簇%s' % e,en_cu_name='Battery cluster %s' % e, chag=('%.3f' % BCCap_cu.tolist()[e - 1]), disg=('%.3f' % f), day=day, ratio=ratio, op_ts=timeUtils.getNewTimeStr(), cause=1))
            e += 1
    else:
        for f in range(7):
            print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % (f+1))
            user_session.merge(FReportBmsDongmu(pcs_name='PCS-%s' % (f+1), cu_name='电池簇%s' % (f+1),en_cu_name='Battery cluster %s' % (f+1), chag=0,disg=0, day=day, ratio=0, op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    dm_table = HisDM('r_measure')
    obj = []
    cu_name_t = model_config.get('peizhi', 'cu_StRan_4')  # name温度极差
    cu_name_v = model_config.get('peizhi', 'cu_SVRan_4')  # name电压极差
    cu_name_s = model_config.get('peizhi', 'cu_SOH_4')  # name
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
    for f in range(7):
        d1 = []
        d2 = []
        d3 = []
        if values_mong:
            for v in values_mong:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][:4] == 'BMS%s' % (f + 1):
                        value1 = float(b[cu_name_t])  #
                        if 0 < value1 <= 65:
                            d1.append(value1)
                        else:
                            d1.append(0)
                        value2 = float(b[cu_name_v])  #
                        if 0 < value2 <= 100:
                            d2.append(value2)
                        else:
                            d2.append(0)
                        value3 = float(b[cu_name_s])  #
                        if 0 < value3 <= 100:
                            d3.append(value3)
                        else:
                            d3.append(100)
        else:
            d1 = [0]
            d2 = [0]
            d3 = [0]
        print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % (f + 1))
        page = user_session.query(FReportBmsDongmu).filter(FReportBmsDongmu.pcs_name=='PCS-%s' % (f + 1),FReportBmsDongmu.day== day+' 00:00:00').first()
        page.max_t= max(d1)
        page.max_v= max(d2)
        page.min_soh= max(d3)
    dm_table = HisDM('r_measure')
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
    dict_ = {'PCS_PF_4':[], 'PCS_T_4':[]}
    for ll in dict_.keys():
        value_name = model_config.get('peizhi', ll)  # name功率因数
        for f in range(7):
            if values_mong:
                max_=1
                min_=0
                if ll=='PCS_PF_4':
                    max_=1.0001
                    min_=0,0001
                elif ll=='PCS_T_4':
                    max_ = 65
                    min_ = 0
                for v in values_mong:
                    body = json.loads(v['datainfo'])['body']
                    for b in body:
                        if b['device'][:4] == 'PCS%s' % (f + 1):
                            value = float(b[value_name])  #
                            if min_ < value <= max_:
                                dict_[ll].append(value)
                            else:
                                dict_[ll].append(0)
            else:
                dict_[ll] = [1,0]
            print ('db:', 'dongmu', '--day:', day, 'PCS--name:', 'PCS-%s' % (f + 1))
            user_session.merge(FReportDongmu(pcs_name='%s#PCS' % (f + 1), max_p=max(dict_['PCS_PF_4']), min_p=min(dict_['PCS_T_4']), max_T=max(dict_['PCS_T_4']), min_T=min(dict_['PCS_T_4']), day=day, station='dongmu',op_ts=timeUtils.getNewTimeStr(), cause=1))

    user_session.commit()
    user_session.close()
    dongmu_session.close()
    print ('SUCCESS')

def frozeDayaByTime(startTime,endTime):
    days = timeUtils.dateToDataList(startTime,endTime)  # 计算间隔的天
    for day in days:
        calculation(day)

# def calculation(day='2023-11-04'):
def calculation(day=None):
    if not day:
        day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
        frozeDongMuDataDay_(day)

    else:
        frozeDongMuDataDay_(day)

def RunClearFileAndData():
    scheduler = BlockingScheduler()
    # scheduler.add_job(calculation, 'interval', seconds=10)  # 10秒获取一次数据
    # scheduler.add_job(calculation, 'interval', seconds=60*2)  # 每 15分钟执行一次
    # scheduler.add_job(calculation, 'cron', hour=19,minute=15,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.add_job(calculation, 'cron', hour=3,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.start()

if __name__ == '__main__':
    try:
        opts, args = getopt.getopt(sys.argv[1:],"h",["help","froze","start=","end="])
        cmd = None
        start = None
        end = None
        for opt,arg in opts:
            if opt=="--froze":
                cmd = "froze"
            elif opt=="--start":
                start = arg
            elif opt=="--end":
                end = arg
            else:
                print ('''%s 数据冻结工具
                选项
                    -h|--help 查看帮助
                    --froze 冻结数据
                    --start 起始时刻（含）。yyyy-mm-dd
                    --end 结束时刻（含）。yyyy-mm-dd

                ''' )% sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            RunClearFileAndData()
            # calculation()
        elif cmd=="froze":
            if not start:
                print ("请指定开始时刻")
                quit()
            if not end:
                print ("请指定结束时刻")
                quit()
            frozeDayaByTime(start,end)
            print ('SUCCESS')

    except Exception as e:
        print (e)