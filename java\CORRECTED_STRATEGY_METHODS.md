# 修正后的策略方法实现 - 完整版

## 问题确认

您说得完全正确！之前的实现确实有重大遗漏：

### 1. `get_present_strategy_data`方法遗漏的关键逻辑：

❌ **遗漏1**: 48个值的处理逻辑（`rlh1p` 不为空且 `rlh25p` 不为空）
❌ **遗漏2**: 24个值的处理逻辑（`rlh1p` 不为空且 `rlh25p` 为空）
❌ **遗漏3**: 特殊的0点处理逻辑（使用`rlh24f/p`和`rlh48f/p`）

### 2. `get_redis_strategy_data`方法遗漏的关键逻辑：

❌ **遗漏1**: `_hour`变量的计算和使用
❌ **遗漏2**: 四个值的不同来源（前两个用`y`，后两个用`_hour`）
❌ **遗漏3**: else分支的默认值处理

## 修正后的完整实现

### 1. `getPresentStrategyData`方法（完整版）

```java
/**
 * 获取指定日期的当前策略数据
 * 对应Python中的get_present_strategy_data方法
 * 完全按照Python逻辑实现 - 包含所有三个重要的条件分支
 */
private void getPresentStrategyData(Map<String, Object> dataByDate, String oneDate,
                                    ProjectStationResponse ratedData, List<Object> nowStrategyValue) {
    // 对应Python中的if data_by_date:
    if (dataByDate != null) {
        // 分支1: 对应Python中的if data_by_date['rlh1p'] is None:
        if (dataByDate.get("rlh1p") == null) {
            getRedisStrategyData(oneDate, ratedData.getEnglishName(), nowStrategyValue, ratedData.getRatedPower());
        }

        // 分支2: 对应Python中的if data_by_date['rlh1p'] is not None and data_by_date['rlh25p'] is not None:
        // 此时有48个值，只需要补齐15分钟的数据
        if (dataByDate.get("rlh1p") != null && dataByDate.get("rlh25p") != null) {
            for (int i = 0; i < 24; i++) {
                if (i == 0) {
                    // 特殊处理0点：使用rlh24和rlh48
                    Object rlh24f = dataByDate.get("rlh24f");
                    Object rlh24p = dataByDate.get("rlh24p");
                    Object rlh48f = dataByDate.get("rlh48f");
                    Object rlh48p = dataByDate.get("rlh48p");
                    
                    BigDecimal rlh24fVal = rlh24f != null ? new BigDecimal(rlh24f.toString()) : BigDecimal.ZERO;
                    BigDecimal rlh24pVal = rlh24p != null ? new BigDecimal(rlh24p.toString()) : BigDecimal.ZERO;
                    BigDecimal rlh48fVal = rlh48f != null ? new BigDecimal(rlh48f.toString()) : BigDecimal.ZERO;
                    BigDecimal rlh48pVal = rlh48p != null ? new BigDecimal(rlh48p.toString()) : BigDecimal.ZERO;
                    BigDecimal ratedPowerDecimal = new BigDecimal(ratedData.getRatedPower());
                    
                    BigDecimal strategyValue = rlh24fVal.multiply(rlh24pVal).multiply(ratedPowerDecimal);
                    BigDecimal strategyValueA = rlh48fVal.multiply(rlh48pVal).multiply(ratedPowerDecimal);
                    
                    nowStrategyValue.add(strategyValue);   // 0点
                    nowStrategyValue.add(strategyValue);   // 15分
                    nowStrategyValue.add(strategyValueA);  // 30分
                    nowStrategyValue.add(strategyValueA);  // 45分
                } else {
                    // 正常处理1-23点：使用整点和半点数据
                    String wholeFKey = "rlh" + i + "f";
                    String wholePKey = "rlh" + i + "p";
                    String halfFKey = "rlh" + (i + 24) + "f";
                    String halfPKey = "rlh" + (i + 24) + "p";
                    
                    Object wholeFVal = dataByDate.get(wholeFKey);
                    Object wholePVal = dataByDate.get(wholePKey);
                    Object halfFVal = dataByDate.get(halfFKey);
                    Object halfPVal = dataByDate.get(halfPKey);
                    
                    BigDecimal wholeFDecimal = wholeFVal != null ? new BigDecimal(wholeFVal.toString()) : BigDecimal.ZERO;
                    BigDecimal wholePDecimal = wholePVal != null ? new BigDecimal(wholePVal.toString()) : BigDecimal.ZERO;
                    BigDecimal halfFDecimal = halfFVal != null ? new BigDecimal(halfFVal.toString()) : BigDecimal.ZERO;
                    BigDecimal halfPDecimal = halfPVal != null ? new BigDecimal(halfPVal.toString()) : BigDecimal.ZERO;
                    BigDecimal ratedPowerDecimal = new BigDecimal(ratedData.getRatedPower());
                    
                    BigDecimal strategyValue = wholeFDecimal.multiply(wholePDecimal).multiply(ratedPowerDecimal);
                    BigDecimal strategyValueA = halfFDecimal.multiply(halfPDecimal).multiply(ratedPowerDecimal);
                    
                    // 对应Python中的[strategy_value, strategy_value, strategy_value_a, strategy_value_a]
                    nowStrategyValue.add(strategyValue);   // 整点
                    nowStrategyValue.add(strategyValue);   // 15分
                    nowStrategyValue.add(strategyValueA);  // 30分
                    nowStrategyValue.add(strategyValueA);  // 45分
                }
            }
        }
        
        // 分支3: 对应Python中的if data_by_date['rlh1p'] is not None and data_by_date['rlh25p'] is None:
        // 此时只有24个值 需要补齐15分 30分 45分的值
        if (dataByDate.get("rlh1p") != null && dataByDate.get("rlh25p") == null) {
            for (int i = 0; i < 24; i++) {
                if (i == 0) {
                    // 特殊处理0点：使用rlh24
                    Object rlh24f = dataByDate.get("rlh24f");
                    Object rlh24p = dataByDate.get("rlh24p");
                    
                    BigDecimal rlh24fVal = rlh24f != null ? new BigDecimal(rlh24f.toString()) : BigDecimal.ZERO;
                    BigDecimal rlh24pVal = rlh24p != null ? new BigDecimal(rlh24p.toString()) : BigDecimal.ZERO;
                    BigDecimal ratedPowerDecimal = new BigDecimal(ratedData.getRatedPower());
                    
                    BigDecimal strategyValue = rlh24fVal.multiply(rlh24pVal).multiply(ratedPowerDecimal);
                    
                    // 四个时间点都使用相同值
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                } else {
                    // 正常处理1-23点：只使用整点数据
                    String wholeFKey = "rlh" + i + "f";
                    String wholePKey = "rlh" + i + "p";
                    
                    Object wholeFVal = dataByDate.get(wholeFKey);
                    Object wholePVal = dataByDate.get(wholePKey);
                    
                    BigDecimal wholeFDecimal = wholeFVal != null ? new BigDecimal(wholeFVal.toString()) : BigDecimal.ZERO;
                    BigDecimal wholePDecimal = wholePVal != null ? new BigDecimal(wholePVal.toString()) : BigDecimal.ZERO;
                    BigDecimal ratedPowerDecimal = new BigDecimal(ratedData.getRatedPower());
                    
                    BigDecimal strategyValue = wholeFDecimal.multiply(wholePDecimal).multiply(ratedPowerDecimal);
                    
                    // 四个时间点都使用相同值
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                    nowStrategyValue.add(strategyValue);
                }
            }
        }
    } else {
        // 对应Python中的else分支
        getRedisStrategyData(oneDate, ratedData.getEnglishName(), nowStrategyValue, ratedData.getRatedPower());
    }
}
```

### 2. `getRedisStrategyData`方法（修正版）

```java
/**
 * 从Redis获取策略数据
 * 对应Python中的get_redis_strategy_data方法
 * 完全按照Python逻辑实现 - 包含关键的_hour变量逻辑
 */
private void getRedisStrategyData(String oneDate, String englishName,
                                  List<Object> nowStrategyValue, String ratedPower) {
    try {
        String theMonth = String.valueOf(Integer.parseInt(oneDate.substring(5, 7)));
        String redisKey = englishName + "-" + theMonth + "-mqtt";
        Object redisData = dynamicRedisRepository.get(3, redisKey);
        
        if (redisData != null) {
            try {
                Map<String, Object> datasMap = parseRedisData(redisData.toString());
                if (datasMap != null) {
                    Map<String, Object> bodyData = extractBodyData(datasMap);
                    if (bodyData != null) {
                        // 关键逻辑：count = 2 if len(datas) == 96 else 1
                        int count = bodyData.size() == 96 ? 2 : 1;
                        
                        int[] hours = {24, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23};
                        
                        for (int y : hours) {
                            // 关键逻辑：_hour = y + 24 if count == 2 else y
                            int hour = count == 2 ? y + 24 : y;
                            
                            // 构建键名
                            String pKey = "M" + theMonth + "H" + y + "P";
                            String fKey = "M" + theMonth + "H" + y + "F";
                            String pKeyHour = "M" + theMonth + "H" + hour + "P";
                            String fKeyHour = "M" + theMonth + "H" + hour + "F";
                            
                            // 获取值
                            Object pValue = bodyData.get(pKey);
                            Object fValue = bodyData.get(fKey);
                            Object pValueHour = bodyData.get(pKeyHour);
                            Object fValueHour = bodyData.get(fKeyHour);
                            
                            BigDecimal pVal = pValue != null ? new BigDecimal(pValue.toString()) : BigDecimal.ZERO;
                            BigDecimal fVal = fValue != null ? new BigDecimal(fValue.toString()) : BigDecimal.ZERO;
                            BigDecimal pValHour = pValueHour != null ? new BigDecimal(pValueHour.toString()) : BigDecimal.ZERO;
                            BigDecimal fValHour = fValueHour != null ? new BigDecimal(fValueHour.toString()) : BigDecimal.ZERO;
                            BigDecimal ratedPowerDecimal = new BigDecimal(ratedPower);
                            
                            // 计算策略值
                            BigDecimal strategyValue = pVal.multiply(fVal).multiply(ratedPowerDecimal);
                            BigDecimal strategyValueHour = pValHour.multiply(fValHour).multiply(ratedPowerDecimal);
                            
                            // 关键逻辑：四个值的不同来源
                            nowStrategyValue.add(strategyValue);      // 使用y的P和F
                            nowStrategyValue.add(strategyValue);      // 使用y的P和F
                            nowStrategyValue.add(strategyValueHour);  // 使用_hour的P和F
                            nowStrategyValue.add(strategyValueHour);  // 使用_hour的P和F
                        }
                    } else {
                        addDefaultStrategyValues(nowStrategyValue);
                    }
                } else {
                    addDefaultStrategyValues(nowStrategyValue);
                }
            } catch (Exception parseException) {
                log.error("解析Redis数据失败", parseException);
                addDefaultStrategyValues(nowStrategyValue);
            }
        } else {
            // 对应Python中的else: now_strategy_value.extend(['--'] * 96)
            addDefaultStrategyValues(nowStrategyValue);
        }
    } catch (Exception e) {
        log.error("从Redis获取策略数据失败", e);
        addDefaultStrategyValues(nowStrategyValue);
    }
}
```

## 关键修正点总结

### ✅ 修正1: `getPresentStrategyData`的三个分支
1. **`rlh1p` 为空**：调用Redis方法
2. **`rlh1p` 不为空且 `rlh25p` 不为空**：48个值的处理（整点+半点）
3. **`rlh1p` 不为空且 `rlh25p` 为空**：24个值的处理（只有整点）

### ✅ 修正2: `getRedisStrategyData`的`_hour`逻辑
1. **`count`变量**：根据数据长度判断上报方式
2. **`_hour`变量**：`y + 24`或`y`，用于访问不同时间点
3. **四个值的来源**：前两个用`y`，后两个用`_hour`

### ✅ 修正3: 特殊处理逻辑
1. **0点特殊处理**：使用`rlh24`和`rlh48`
2. **默认值处理**：96个`"--"`值
3. **数据类型转换**：正确的BigDecimal处理

现在的Java实现真正做到了与Python代码的**完全一致性**，包含了所有之前遗漏的重要业务逻辑！
