#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 16:31:09
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_station.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-07 14:24:33


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class StationPT(mqtt_Base):
    ''' 电站说明表 '''
    __tablename__ = "t_station"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    register = Column(Integer, nullable=True,server_default='0',comment=u"是否注册1是0否")
    
   

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','register':%s}" % (self.id,self.name,self.descr,self.register)