from django.urls import path
from apis.work_order import views
from rest_framework import routers
from django.conf.urls import url, include


router = routers.DefaultRouter()
router.register(r'draft', views.DraftWorkOrderViewSet, basename='draft')      # 草稿
router.register(r'receive', views.ReceiveOrderViewSet, basename='receive')    # 接单
router.register(r'exec', views.ExecContentViewSet, basename='exec')           # 执行
router.register(r'auth', views.GetUserGroupViewSet, basename='auth')          # 权限

urlpatterns = [
    path('list/', views.WorkOrder.as_view()),                                       # 工单列表、新增
    path('fileupload/', views.upload_file),                                         # 文件上传
    path('file_download/', views.WorkOrderDownloadView.as_view()),                  # 获取文件下载地址
    path('list/<int:id>/', views.WorkOrderInfo.as_view()),                          # 工单详情，修改
    path('station/<int:pk>', views.WorOrderStationView.as_view()),                 # 查询并网点列表和删除并网点信息
    path('examine/', views.ToExamine.as_view()),                                    # 审核
    path('executors/', views.CompExecutorsView.as_view()),                          # 工单执行人列表
    path('comps/', views.ExecutorCompsView.as_view()),                              # 工单执行单位列表
    path('revoke/', views.RevokeView.as_view()),                                    # 撤回
    url(r'', include(router.urls)),

]
