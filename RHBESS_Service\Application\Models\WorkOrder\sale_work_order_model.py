#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/6/24 下午4:32
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import (Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,
                                 Boolean, Text)


# 售后工单类型表
class SaleWorkerOrderType(user_Base):
    __tablename__ = "c_sale_worker_order_type"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    name = Column(VARCHAR(100), comment='名称')
    parent_id = Column(Integer, ForeignKey('c_sale_worker_order_type.id'), comment='父级ID，自关联，type=2时不为空')
    type = Column(Integer, nullable=False, comment='1 任务类型;2任务子类型；3任务重要度')
    is_use = Column(Integer, nullable=True, server_default='1', comment='是否使用1是0否，默认1')

    # 自关联关系
    parent = relationship("SaleWorkerOrderType", remote_side=[id], uselist=False, back_populates="children")
    children = relationship("SaleWorkerOrderType", foreign_keys=[parent_id], back_populates="parent")

    def __repr__(self):
        return f"<SaleWorkerOrderType(id={self.id}, name='{self.name}', type={self.type})>"


# 售后工单项目字典表
class SaleWorkerOrderProject(user_Base):
    __tablename__ = 'c_sale_worker_order_project'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    name = Column(String(255), comment='项目名称')
    user = Column(String(100), comment='项目联系人')
    phone = Column(String(50), comment='项目联系人电话')
    address = Column(String(255), comment='项目地址')
    project_type = Column(String(50), comment='项目类型')
    device_type = Column(String(50), comment='设备类型')
    product_type = Column(String(50), comment='产品类型')
    is_use = Column(Integer, default=1, comment='是否使用：1是0否，默认1')

    working_base = relationship("SaleWorkerOrderBase", back_populates="project")
    def __repr__(self):
        return f"<SaleWorkerOrderProject(id={self.id}, name='{self.name}')>"


# 售后工单运行状态表
class SaleWorkerOrderRunType(user_Base):
    __tablename__ = 'c_sale_worker_order_run_type'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    name = Column(String(100), comment='步骤名称')
    is_use = Column(Integer, default=1, comment='是否使用：1是0否，默认1')
    type = Column(Integer, comment='类型：1售后工单，其他的待定')
    lable = Column(String(255), comment='路由名称')

    def __repr__(self):
        return f"<SaleWorkerOrderRunType(id={self.id}, name='{self.name}')>"


# 售后工单权限表
class RoleWorkerOrder(user_Base):
    __tablename__ = "c_role_worker_order"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    run_type = Column(Integer, ForeignKey('c_sale_worker_order_run_type.id'), nullable=False,
                         comment='处理状态，c_sale_worker_order_run_type外键')
    user_id = Column(Integer, comment='录入用户id')

    run_type_detail = relationship("SaleWorkerOrderRunType", foreign_keys=[run_type])

    def __repr__(self):
        return f"<RoleWorkerOrder(id={self.id}, run_type_id={self.run_type_id})>"


# 售后工单记录表
class SaleWorkerOrderBase(user_Base):
    __tablename__ = "t_sale_worker_order_base"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    working_no = Column(VARCHAR(50), nullable=False, comment='工单编号, 后端自动生成, 年月日时分秒')
    project_id = Column(Integer, ForeignKey('c_sale_worker_order_project.id'), comment='项目id, 售后工单项目字典表外键')
    create_user_id = Column(Integer, ForeignKey('t_user.id'), comment='创建人id')
    problem_descr = Column(VARCHAR(500), nullable=False, comment='问题描述')
    task_level = Column(Integer, ForeignKey('c_sale_worker_order_type.id'),
                        comment='任务重要程度id, 售后工单类型表外键, type=3')
    run_type = Column(Integer, ForeignKey('c_sale_worker_order_run_type.id'), nullable=False,
                      comment='处理状态, c_sale_worker_order_run_type外键')
    order_type_id = Column(Integer, ForeignKey('c_sale_worker_order_type.id'),
                           comment='任务类型id')
    order_type_child_id = Column(Integer, ForeignKey('c_sale_worker_order_type.id'),
                                 comment='任务子类型id')
    run_user_id = Column(Integer, ForeignKey('t_user.id'), comment='根据工单状态, 系统自动填充节点负责人（即正在执行环节的处理人）')
    plan_finish_time = Column(DateTime, nullable=False, comment='要求完成时间')
    real_finish_time = Column(DateTime, comment='实际完成时间, 录入的')
    over_time = Column(Integer, comment='是否超时: 0否1是')
    handle_time = Column(VARCHAR(30), comment='处理时长')
    sys_finish_time = Column(DateTime, comment='系统判定完成时间, 在执行步骤填完自动记录')
    files = Column(Text, comment='附件内容集合')
    remark = Column(VARCHAR(255), comment='备注')
    copy_user = Column(VARCHAR(255), comment='抄送人, list')

    # 关系定义
    project = relationship("SaleWorkerOrderProject", foreign_keys="[SaleWorkerOrderBase.project_id]", back_populates="working_base")
    task_level_d = relationship("SaleWorkerOrderType", foreign_keys="[SaleWorkerOrderBase.task_level]", backref="task_level_d")
    run_type_d = relationship("SaleWorkerOrderRunType", foreign_keys="[SaleWorkerOrderBase.run_type]", backref="run_type_d")
    order_type_p = relationship("SaleWorkerOrderType", foreign_keys="[SaleWorkerOrderBase.order_type_id]", backref="order_type_p")
    order_type_c = relationship("SaleWorkerOrderType", foreign_keys="[SaleWorkerOrderBase.order_type_child_id]", backref="order_type_c")
    create_user = relationship("User", foreign_keys="[SaleWorkerOrderBase.create_user_id]", backref="create_user")
    run_user = relationship("User", foreign_keys="[SaleWorkerOrderBase.run_user_id]", backref="run_user")

    working_step = relationship("SaleWorkerOrderStep", back_populates="working_order")

    def __repr__(self):
        return f"<SaleWorkerOrderBase(id={self.id}, working_no='{self.working_no}')>"


# 售后工单执行步骤表
class SaleWorkerOrderStep(user_Base):
    __tablename__ = "t_sale_worker_order_step"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    working_no = Column(VARCHAR(50), nullable=False, comment='工单编号')
    working_id = Column(Integer, ForeignKey('t_sale_worker_order_base.id'), nullable=False,
                        comment='工单id, t_sale_worker_order_base外键')
    create_user_id = Column(Integer, ForeignKey('t_user.id'), comment='创建人id, 用户表外键')
    plan_user = Column(Integer, comment='预计执行人')
    real_user = Column(Integer, comment='实际执行人')
    imgs = Column(Text, comment='图片内容集合')
    files = Column(Text, comment='文件内容集合')
    run_type = Column(Integer, ForeignKey('c_sale_worker_order_run_type.id'), nullable=False,
                         comment='处理状态, c_sale_worker_order_run_type外键')
    content = Column(VARCHAR(255), comment='执行内容')
    remark = Column(VARCHAR(255), comment='备注')
    solve_id = Column(Integer, ForeignKey('t_sale_worker_order_solve.id'),
                      comment='问题汇总, t_sale_worker_order_solve外键')
    check_flag = Column(Integer, comment='审核状态: 1通过, 2驳回')
    care_flag = Column(Integer, default=0, comment='是否转单: 1是, 0否, 默认0')
    check_type = Column(Integer, comment='审核规则: 1顺序审核, 0任何一人审核')
    implement_time = Column(DateTime, comment='步骤执行时间')
    platform_id = Column(Integer, comment='关联自身id 由哪一步生成的')


    # 关系定义
    working_order = relationship("SaleWorkerOrderBase", back_populates="working_step")
    run_type_r = relationship("SaleWorkerOrderRunType", backref="run_type_r")
    # solve_r = relationship("SaleWorkerOrderSolve", backref="solve_r")

    def __repr__(self):
        return f"<SaleWorkerOrderStep(id={self.id}, working_no='{self.working_no}')>"


# 售后工单解决办法表
class SaleWorkerOrderSolve(user_Base):
    __tablename__ = "t_sale_worker_order_solve"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    name = Column(VARCHAR(255), comment='内容名称')
    user_id = Column(Integer, comment='录入用户id')
    worker_id = Column(Integer, comment='工单id')
    step_id = Column(Integer, comment='步骤id')

    def __repr__(self):
        return f"<SaleWorkerOrderSolve(id={self.id}, name='{self.name}')>"


# 售后工单抄送人名单
class SaleWorkerOrderCopyUser(user_Base):
    __tablename__ = "c_sale_worker_order_copy_user"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    creat_time = Column(DateTime, nullable=False, comment='录入时间')
    user_id = Column(Integer, ForeignKey('t_user.id'), nullable=False, comment='抄送人id')
    type = Column(Integer, nullable=False, comment='类型 1最终审核抄送')

    # 关系定义
    users = relationship("User", foreign_keys="[SaleWorkerOrderCopyUser.user_id]", backref="sale_worker_order_copy_users")

    def __repr__(self):
        return f"<SaleWorkerOrderCopyUser(id={self.id}')>"