package com.tianlu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_message_center")
public class MessageCenter extends SuperEntity {
    /**
     * 策略标题
     */
    private String title;

    /**
     * 消息类型：0：策略; 1：告警；2:运行分析；3：电池电压分析；4：电池温度分析, 5:电池过放风险
     */
    private Integer type;

    /**
     * 是否已读：1：已读；0：未读
     */
    private Integer isRead;

    /**
     * 是否处理：1：已处理；0：未处理；2：不展示(策略确认结果消息)
     */
    private Integer isHandle;

    /**
     * 确认状态：1：已确认/已反馈；0：未确认/为反馈
     */
    private Integer isVerify;

    /**
     * 附件url，多个以引文逗号隔开
     */
    private String files;

    /**
     * 策略反馈意见
     */
    private String opinion;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 告警ID
     */
    private Long alarmId;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 站点ID
     */
    private Long stationId;

    /**
     * 通用关联id
     */
    private String relatedId;

    /**
     * 下发用户
     */
    private Long issueUser;

    /**
     * 策略标题(英文)
     */
    private String enTitle;

    /**
     * 策略反馈意见(英文)
     */
    private String enOpinion;
} 