# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/11 下午2:18
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : async_tasks.py
# @Software : PyCharm
import calendar
import datetime
import os
import traceback
import concurrent.futures
from operator import itemgetter

import openpyxl
from dateutil.relativedelta import relativedelta
from django_redis import get_redis_connection

from TianLuAppBackend import settings
from apis.user import models
from apis.web.models import RunningReport
from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
from tools.minio_tool import MinioTool


def get_month_start_end(year, month):
    first_day = datetime.datetime(year, month, 1)
    if month == 12:
        next_month_start = datetime.datetime(year + 1, 1, 1)
    else:
        next_month_start = datetime.datetime(year, month + 1, 1)
    last_day = next_month_start - datetime.timedelta(days=1)
    return first_day, last_day


def get_last_day_of_month(year, month):
    _, last_day = calendar.monthrange(year, month)
    last_day_date = datetime.datetime(year, month, last_day)
    return last_day_date


def save_RunningReportsView_to_minio(user_id, start_time, end_time, project_ids=None, lang='zh'):
    user = models.UserDetails.objects.get(id=user_id)
    project_ids = project_ids if project_ids else []
    report_type = 3

    master_stations = user.master_stations.filter(is_delete=0).all()
    station_names = [station.name for station in master_stations]
    reports = RunningReport.objects.filter(station_name__in=station_names).all()

    today = datetime.date.today()

    # start_time = request.data.get('start_time') if request.data.get('start_time') else today.strftime('%Y-%m')
    # end_time = request.data.get('end_time') if request.data.get('end_time') else today.strftime('%Y-%m')

    try:
        datetime.datetime.strptime(start_time, '%Y-%m')
        datetime.datetime.strptime(end_time, '%Y-%m')
    except Exception as e:
        print(traceback.print_exc())
        return

    # 计算月份集合
    temp_months = list()
    target_month = datetime.datetime.strptime(start_time, '%Y-%m')
    end_month = datetime.datetime.strptime(end_time, '%Y-%m')
    while target_month <= end_month:
        temp_months.append(target_month.strftime('%Y-%m'))
        target_month = target_month + relativedelta(months=1)

    # 计算所选月份得开始日和结束日
    temp_days = list()
    first_day = datetime.datetime.strptime(start_time, '%Y-%m')
    end_year, end_month = end_time.split('-')
    end_day = get_last_day_of_month(int(end_year), int(end_month)).date() if get_last_day_of_month(
        int(end_year), int(end_month)).date() <= today else today

    target_day = first_day.date()
    while target_day <= end_day:
        temp_days.append(target_day.strftime('%Y-%m-%d'))
        target_day += datetime.timedelta(days=1)

    reports = reports.filter(report_type=report_type, datetime__in=temp_months)

    if project_ids:
        master_stations = models.MaterStation.objects.filter(project_id__in=project_ids, is_delete=0)
        if not master_stations.exists():
            return
    else:
        master_stations = models.MaterStation.objects.filter(userdetails=user, is_delete=0).order_by('id')

    total_reports = []
    for master_station in master_stations:
        reports_s = reports.filter(station_name=master_station.name).all().order_by('datetime')
        if reports_s.exists():
            total_reports.extend(reports_s)

    workbook = openpyxl.Workbook()

    if lang == 'zh':
    # time_str = str(int(time.time()))
        file_name = f"{start_time}~{end_time}天禄运行月报_{user_id}.xlsx"
        path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

        t_station_names = list(set([r.station_name for r in reports]))
        # t_stations = models.StationDetails.objects.filter(station_name__in=t_station_names)
        t_master_stations = models.MaterStation.objects.filter(name__in=t_station_names, is_delete=0)
        t_stations = models.StationDetails.objects.filter(is_delete=0, master_station__in=t_master_stations)

        table1_titles = ["开始日期", "结束日期", "并网点名称", "充电量(kWh)", "放电量(kWh)", "充放电效率(%)",
                         "充放电次数",
                         "尖时充电量(kWh)", "峰时充电量(kWh)", "平时充电量(kWh)", "谷时充电量(kWh)", "深谷时充电量(kWh)",
                         "尖时放电量(kWh)", "峰时放电量(kWh)", "平时放电量(kWh)", "谷时放电量(kWh)", "深谷时放电量(kWh)",
                         "基准充电量(kWh)", "基准充电量完成率（%）", "基准放电量(kWh)", "基准放电量完成率(%)",
                         "累计充电量(kWh)",
                         "累计放电量（kWh）", "累计充放电量效率(%)", "收益(元)", "故障告警数量", "分析内容"]

        table2_titles = ["日期", "并网点名称", "充电量(kWh)", "放电量(kWh)", "充放电效率(%)",
                         "基准充电量(kWh)", "基准充电量完成率（%）", "基准放电量(kWh)", "基准放电量完成率(%)",
                         "尖时充电量(kWh)", "峰时充电量(kWh)", "平时充电量(kWh)", "谷时充电量(kWh)", "深谷时充电量(kWh)",
                         "尖时放电量(kWh)", "峰时放电量(kWh)", "平时放电量(kWh)", "谷时放电量(kWh)", "深谷时放电量(kWh)",
                         "收益(元)", "故障告警数量"]

        table3_titles = ["序号", "项目名称", "设备名称", "报文", "告警分类", "开始时间", "结束时间"]

        sheet1 = workbook.active
        sheet1.title = '运行数据'

    else:
        file_name = f"{start_time}~{end_time}_Tianlu_Operation_Monthly_Report_{user_id}.xlsx"
        path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

        t_station_names = list(set([r.station_name for r in total_reports]))
        # t_stations = models.StationDetails.objects.filter(station_name__in=t_station_names)
        t_master_stations = models.MaterStation.objects.filter(name__in=t_station_names, is_delete=0)
        t_stations = models.StationDetails.objects.filter(is_delete=0, master_station__in=t_master_stations)

        table1_titles = ["From", "To", "Installation", "Energy Charged(kWh)", "Energy Discharged(kWh)", "Efficiency(%)",
                         "Energy Charge and Discharge Cycles",
                         "Peak-hour Energy Charged(kWh)", "Shoulder-hour Energy Charged(kWh)", "Off-peak-hour Energy Charged(kWh)", "Valley-hour Energy Charged(kWh)",
                         "Deep Valley-hour Energy Charged(kWh)",
                         "Peak-hour Energy Discharged(kWh)", "Shoulder-hour Energy Discharged(kWh)", "Off-peak-hour Energy Discharged(kWh)", "Valley-hour Energy Discharged(kWh)",
                         "Deep Valley-hour Energy Discharged(kWh)",
                         "Reference Energy Charged(kWh)", "Energy Charge Completion Rate(%)", "Reference Energy Charged(kWh)", "Reference Energy Disharged Completion Rate(%)",
                         "Cumulative Energy Charged(kWh)",
                         "Cumulative Energy Discharged(kWh)", "General Efficiency(%)", "Profit(Yuan)", "Number of Fault Alarms", "Analysis"]

        table2_titles = ["Date", "Installation", "Energy Charged(kWh)", "Energy Discharged(kWh)", "Efficiency(%)",
                         "Reference Energy Charged(kWh)", "Reference Energy Charge Completion Rate（%）", "Reference Energy Charged(kWh)", "Reference Energy Disharged Completion Rate(%)",
                         "Peak-hour Energy Charged(kWh)", "Shoulder-hour Energy Charged(kWh)", "Off-peak-hour Energy Charged(kWh)", "Valley-hour Energy Charged(kWh)",
                         "Deep Valley-hour Energy Charged(kWh)",
                         "Peak-hour Energy Discharged(kWh)", "Shoulder-hour Energy Discharged(kWh)", "Off-peak-hour Energy Discharged(kWh)", "Valley-hour Energy Discharged(kWh)",
                         "Deep Valley-hour Energy Discharged(kWh)",
                         "Profit(Yuan)", "Number of Fault Alarms"]

        table3_titles = ["Number", "Installation", "Device", "Message", "Alarm Types", "From", "To"]

        sheet1 = workbook.active
        sheet1.title = 'Operation Data'

    # sheet1 写入标头
    for col_num, header in enumerate(table1_titles, 1):
        col_letter = openpyxl.utils.get_column_letter(col_num)
        sheet1[f'{col_letter}1'] = header

    # 准备数据
    data_array = list()
    if total_reports:
        for t in total_reports:
            year_month = t.datetime.split('-')
            year_, month_ = int(year_month[0]), int(year_month[1])
            first_day_, last_day_ = get_month_start_end(year_, month_)

            li = [first_day_.strftime('%Y-%m-%d'), last_day_.strftime('%Y-%m-%d'), t.station_name, t.charge_cap,
                  t.discharge_cap, t.comp_rate, t.count,
                  t.spike_charge, t.peak_charge, t.flat_charge, t.valley_charge, t.dvalley_charge, t.spike_discharge,
                  t.peak_discharge,
                  t.flat_discharge, t.valley_discharge, t.dvalley_discharge, t.theory_charge, t.theory_charge_comp_rate,
                  t.theory_discharge,
                  t.theory_discharge_comp_rate, t.accu_charge, t.accu_discharge, t.effic, t.income, t.alarm + t.fault,
                  t.analyse
                  ]
            data_array.append(li)
        sorted(data_array, key=lambda x: x[0])

        # 写入工作表
        for row_num, row_data in enumerate(data_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}{row_num}'] = cell_value

    if lang == 'zh':
        sheet2 = workbook.create_sheet("逐日运行数据")
    else:
        sheet2 = workbook.create_sheet('Daily Operation Data')

    # sheet2 写入标头
    for col_num, header in enumerate(table2_titles, 1):
        col_letter = openpyxl.utils.get_column_letter(col_num)
        sheet2[f'{col_letter}1'] = header
    # 准备表2数据
    reports_days = RunningReport.objects.filter(report_type=1, station_name__in=t_station_names,
                                                datetime__in=temp_days).all()
    days_data_array = list()
    for master_station in master_stations:
        reports_day_s = reports_days.filter(station_name=master_station.name).all().order_by('datetime')
        if reports_day_s.exists():
            for t in reports_day_s:
                li = [t.datetime, t.station_name, t.charge_cap, t.discharge_cap, t.comp_rate, t.theory_charge,
                      t.theory_charge_comp_rate,
                      t.theory_discharge, t.theory_discharge_comp_rate, t.spike_charge, t.peak_charge, t.flat_charge,
                      t.valley_charge, t.dvalley_charge, t.spike_discharge, t.peak_discharge,
                      t.flat_discharge, t.valley_discharge, t.dvalley_discharge, t.income, t.alarm + t.fault]
                days_data_array.append(li)

    # sorted(days_data_array, key=lambda x: x[0])
    for row_num, row_data in enumerate(days_data_array, 2):
        for col_num, cell_value in enumerate(row_data, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet2[f'{col_letter}{row_num}'] = cell_value

    if lang == 'zh':
        sheet3 = workbook.create_sheet("故障告警记录")
    else:
        sheet3 = workbook.create_sheet('Fault Alarm Record')
    # sheet3 写入标头
    for col_num, header in enumerate(table3_titles, 1):
        col_letter = openpyxl.utils.get_column_letter(col_num)
        sheet3[f'{col_letter}1'] = header

    end_time_ = datetime.datetime.combine(end_day, datetime.datetime.max.time())

    t_stations_ids = [s.id for s in t_stations]
    filters = {
        "start_time__gte": first_day,
        "start_time__lte": end_time_,
        "station_id__in": t_stations_ids,
        "type__in": [1, 2]
    }

    alarm_array = list()
    alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
                                                                         "type", "start_time",
                                                                         "end_time", "details",
                                                                         "id",
                                                                         "note", "device",
                                                                         "station_id"
                                                                         ).order_by("-start_time").all()
    if alarm_instances.exists():
        for ind, ins in enumerate(alarm_instances):
            # ins["details"] = ins["device"] + ":" + ins["details"]
            ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
            if lang == 'zh':
                ins['type'] = "故障" if ins['type'] == 1 else '报警'
            else:
                ins['type'] = "Fault" if ins['type'] == 1 else 'Alarm'
            if ins["end_time"]:
                ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
            else:
                if lang == 'zh':
                    ins["end_time"] = "未恢复故障" if ins['type'] == 1 else "未恢复报警"
                else:
                    ins["end_time"] = "Not recovered Fault" if ins['type'] == 1 else "Not recovered Alarm"
            try:
                ins['station_name'] = models.StationDetails.objects.get(id=ins['station_id']).station_name
            except Exception as e:
                pass
            li = [ind + 1, ins['station_name'], ins['device'], ALARM_ZH_EN_MAP[ins['details'].strip()][lang], ins['type'], ins['start_time'],
                  ins['end_time']]
            alarm_array.append(li)

        sorted(alarm_array, key=lambda x: x[5])
        for row_num, row_data in enumerate(alarm_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet3[f'{col_letter}{row_num}'] = cell_value

    workbook.save(path)

    # 上传至minio
    try:
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        url = minio_client.upload_local_file(file_name, path, bucket_name='download')
        os.remove(path)

        # 缓存下载链接到redis
        key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
        redis_conn = get_redis_connection("default")
        redis_conn.set(key, url, 60 * 5)

    except Exception as e:
        print(traceback.print_exc())


def get_master_station_day_report_data_for_download(master_station, t, lang='zh'):
    execl_data_2_list = []
    execl_data_list = []

    running_reports = RunningReport.objects.filter(
        station_name=master_station.name,
        datetime=t, report_type=1).order_by('id')
    if running_reports:
        for s in running_reports:
            s_time_ = t + " 00:00:00"
            e_time_ = t + " 23:59:59"
            tt = t[:4] + '年' + t[5:7] + '月' + t[8:] + '日' if lang == 'zh' else t[:4] + '-' + t[5:7] + '-' + t[8:]
            execl_data = {}
            if lang == 'zh':
                execl_data['日期'] = tt
                execl_data['项目名称'] = master_station.name

                execl_data['充电量(kWh)'] = s.charge_cap
                execl_data['放电量(kWh)'] = s.discharge_cap
                execl_data['充放电量完成率(%)'] = s.comp_rate
            else:
                execl_data['Date'] = tt
                execl_data['Installation'] = master_station.name

                execl_data['Energy Charged(kWh)'] = s.charge_cap
                execl_data['Energy Disharged(kWh)'] = s.discharge_cap
                execl_data['Charge and Discharge Completion Rate(%)'] = s.comp_rate

            stations = master_station.stationdetails_set.all()
            stations_ids = [s.id for s in stations]

            # 故障告警
            ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=stations_ids,
                                                   start_time__gte=s_time_,
                                                   end_time__lte=e_time_)
            alarm_ins = (
                ins.filter(type__in=[1, 2]).values("status", "type", "start_time", "end_time", "details", "id",
                                              "note",
                                              "station_id",
                                              "device")
                .order_by("-start_time")
            )
            for alarm in alarm_ins:
                station = models.StationDetails.objects.filter(id=alarm['station_id']).first()
                execl_data_2 = {}
                execl_data_2['序号'] = alarm["id"]
                execl_data_2['日期'] = tt
                execl_data_2['项目名称'] = station.station_name if station else '--'
                execl_data_2['设备名称'] = alarm["device"]
                execl_data_2['报文'] = ALARM_ZH_EN_MAP[alarm['details'].strip()][lang]
                if alarm["type"] == 0:
                    execl_data_2['告警分类'] = '告警'
                elif alarm["type"] == 1:
                    execl_data_2['告警分类'] = '故障'
                elif alarm["type"] == 2:
                    execl_data_2['告警分类'] = '报警'
                alarm["start_time"] = alarm["start_time"].strftime('%Y-%m-%d %H:%M:%S')
                alarm["end_time"] = alarm["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                execl_data_2['开始时间'] = alarm["start_time"]
                execl_data_2['结束时间'] = alarm["end_time"]
                execl_data_2_list.append(execl_data_2)
                alarm["details"] = (station.station_name if station else '--') + ":" + alarm["device"] + ALARM_ZH_EN_MAP[alarm["details"].strip()][lang]

            execl_data['基准充电量(kWh)'] = s.theory_charge
            execl_data['基准充电量完成率(%)'] = s.theory_charge_comp_rate
            execl_data['基准放电量(kWh)'] = s.theory_discharge
            execl_data['基准放电量完成率(%)'] = s.theory_discharge_comp_rate

            execl_data['SOC初始值（%）'] = s.soc_init
            execl_data['SOC终值（%）'] = s.soc_final
            execl_data['SOC最大值（%）'] = s.soc_max
            execl_data['SOC最小值（%）'] = s.soc_max

            execl_data['尖时充电量(kWh)'] = s.spike_charge
            execl_data['尖时放电量(kWh)'] = s.spike_discharge

            execl_data['峰时充电量(kWh)'] = s.peak_charge
            execl_data['峰时放电量(kWh)'] = s.peak_discharge

            execl_data['平时充电量(kWh)'] = s.flat_charge
            execl_data['平时放电量(kWh)'] = s.flat_discharge

            execl_data['谷时充电量(kWh)'] = s.valley_charge
            execl_data['谷时放电量(kWh)'] = s.valley_discharge

            execl_data['深谷时充电量(kWh)'] = s.dvalley_charge
            execl_data['深谷时放电量(kWh)'] = s.dvalley_discharge

            execl_data['累计充电量（kWh）'] = s.accu_charge
            execl_data['累计放电量（kWh）'] = s.accu_discharge

            execl_data['充放电效率（%）'] = s.effic
            execl_data['收益（元）'] = s.income
            execl_data['故障告警数量'] = s.fault + s.alarm
            execl_data['分析'] = s.analyse

            execl_data_list.append(execl_data)

    return execl_data_list, execl_data_2_list


def upload_day_report_for_download(master_stations, e_time, s_time, user_id, lang='zh'):
    num = 1
    if lang == 'zh':
        key = f"tianlu_{s_time}-{e_time}天禄运行日报_{user_id}"
        execl_data_11 = {"日期": "日期", "项目名称": "项目名称", "充电量(kWh)": "充电量(kWh)",
                         "放电量(kWh)": "放电量(kWh)",
                         "充放电量完成率(%)": "充放电量完成率(%)", "基准充电量(kWh)": "基准充电量(kWh)",
                         "基准充电量完成率(%)": "基准充电量完成率(%)",
                         "基准放电量(kWh)": "基准放电量(kWh)", "基准放电量完成率(%)": "基准放电量完成率(%)",
                         "SOC初始值（%）": "SOC初始值（%）",
                         "SOC终值（%）": "SOC终值（%）", "SOC最大值（%）": "SOC最大值（%）", "SOC最小值（%）": "SOC最小值（%）",
                         "尖时充电量(kWh)": "尖时充电量(kWh)", "尖时放电量(kWh)": "尖时放电量(kWh)",
                         "峰时充电量(kWh)": "峰时充电量(kWh)", "峰时放电量(kWh)": "峰时放电量(kWh)",
                         "平时充电量(kWh)": "平时充电量(kWh)", "平时放电量(kWh)": "平时放电量(kWh)",
                         "谷时充电量(kWh)": "谷时充电量(kWh)", "谷时放电量(kWh)": "谷时放电量(kWh)",
                         "深谷时充电量(kWh)": "深谷时充电量(kWh)", "深谷时放电量(kWh)": "深谷时放电量(kWh)",
                         "累计充电量（kWh）": "累计充电量（kWh）", "累计放电量（kWh）": "累计放电量（kWh）",
                         "充放电效率（%）": "充放电效率（%）",
                         "收益（元）": "收益（元）", "故障告警数量": "故障告警数量", "分析": "分析"}
        execl_data_22 = {"序号": "序号", "日期": "日期", "项目名称": "项目名称", "设备名称": "设备名称", "报文": "报文",
                         "告警分类": "告警分类",
                         "开始时间": "开始时间", "结束时间": "结束时间"}
        time_list = dateToDataList(s_time, e_time)
        execl_data_list = []
        execl_data_2_list = []
        last_execl_data_list = []
        last_execl_data_2_list = []
        last_execl_data_list.append(execl_data_11)
        last_execl_data_2_list.append(execl_data_22)

        # 使用多线程优化处理速度
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            futures = list()
            for master_station in master_stations:
                for t in time_list:
                    future = executor.submit(get_master_station_day_report_data_for_download, master_station, t, lang)
                    futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                execl_data_list_, execl_data_2_list_ = future.result()
                if execl_data_list_:
                    execl_data_list += execl_data_list_
                if execl_data_2_list_:
                    execl_data_2_list += execl_data_2_list_
        execl_data_list = sorted(execl_data_list, key=itemgetter('项目名称', '日期'))
        execl_data_2_list = sorted(execl_data_2_list, key=itemgetter('项目名称', '日期'), reverse=True)

        last_execl_data_list = last_execl_data_list + execl_data_list
        last_execl_data_2_list = last_execl_data_2_list + execl_data_2_list

        file_name = f"{s_time}~{e_time}天禄运行日报_{user_id}.xlsx"
        path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

        workbook = openpyxl.Workbook()

        data = {
            "运行日报": last_execl_data_list,
            "故障告警记录": last_execl_data_2_list,
        }
        # 遍历字典，将数据写入不同的sheet
        for sheet_name, sheet_data in data.items():
            sheet = workbook.create_sheet(title=sheet_name)
            for index, row_data in enumerate(sheet_data, start=1):
                for col_num, cell_value in enumerate(row_data.values(), start=1):
                    cell = sheet.cell(row=index, column=col_num)
                    cell.value = cell_value
        # 删除sheet页
        del workbook['Sheet']
        # 保存工作簿到文件夹
        workbook.save(path)

    else:
        key = f"tianlu_{s_time}-{e_time}_operation_daily_{user_id}"
        execl_data_11 = {"日期": "Date", "项目名称": "Installation", "充电量(kWh)": "Energy Charged(kWh)",
                         "放电量(kWh)": "Energy Discharged(kWh)",
                         "充放电量完成率(%)": "Energy Charge and Discharge Completion Rate(%)",
                         "基准充电量(kWh)": "Reference Energy Charged(kWh)",
                         "基准充电量完成率(%)": "Reference Energy Charged Completion Rate(%)",
                         "基准放电量(kWh)": "Reference Energy Disharged(kWh)",
                         "基准放电量完成率(%)": "Reference Energy Disharged Completion Rate(%)",
                         "SOC初始值（%）": "SOC Initial Value（%）",
                         "SOC终值（%）": "SOC Final Value（%）", "SOC最大值（%）": "SOC Maximum Value（%）",
                         "SOC最小值（%）": "SOC Minimum Value（%）",
                         "尖时充电量(kWh)": "Peak-hour Energy Charged(kWh)",
                         "尖时放电量(kWh)": "Peak-hour Energy Discharged(kWh)",
                         "峰时充电量(kWh)": "Shoulder-hour Energy Charged(kWh)",
                         "峰时放电量(kWh)": "Shoulder-hour Energy Discharged(kWh)",
                         "平时充电量(kWh)": "Off-peak-hour Energy Charged(kWh)",
                         "平时放电量(kWh)": "Off-peak-hour Energy Discharged(kWh)",
                         "谷时充电量(kWh)": "Valley-hour Energy Charged(kWh)",
                         "谷时放电量(kWh)": "Valley-hour Energy Discharged(kWh)",
                         "深谷时充电量(kWh)": "Deep Valley-hour Energy Charged(kWh)",
                         "深谷时放电量(kWh)": "Deep Valley-hour Energy Discharged(kWh)",
                         "累计充电量（kWh）": "Cumulative Energy Charged（kWh）",
                         "累计放电量（kWh）": "Cumulative Energy Discharged（kWh）",
                         "充放电效率（%）": "Efficiency（%）",
                         "收益（元）": "Profit（Yuan）", "故障告警数量": "Number of Fault Alarms", "分析": "Analysis"}
        execl_data_22 = {"序号": "Number", "日期": "Date", "项目名称": "Installation", "设备名称": "Device",
                         "报文": "Message", "告警分类": "Alarm Types",
                         "开始时间": "From", "结束时间": "To"}
        time_list = dateToDataList(s_time, e_time)
        execl_data_list = []
        execl_data_2_list = []
        last_execl_data_list = []
        last_execl_data_2_list = []
        last_execl_data_list.append(execl_data_11)
        last_execl_data_2_list.append(execl_data_22)

        # 使用多线程优化处理速度
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            futures = list()
            for master_station in master_stations:
                for t in time_list:
                    future = executor.submit(get_master_station_day_report_data_for_download, master_station, t, lang)
                    futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                execl_data_list_, execl_data_2_list_ = future.result()
                if execl_data_list_:
                    execl_data_list += execl_data_list_
                if execl_data_2_list_:
                    execl_data_2_list += execl_data_2_list_
        execl_data_list = sorted(execl_data_list, key=itemgetter('项目名称', '日期'))
        execl_data_2_list = sorted(execl_data_2_list, key=itemgetter('项目名称', '日期'), reverse=True)

        last_execl_data_list = last_execl_data_list + execl_data_list
        last_execl_data_2_list = last_execl_data_2_list + execl_data_2_list

        file_name = f"{s_time}~{e_time}_Tianlu Operation Daily_{user_id}.xlsx"
        path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

        workbook = openpyxl.Workbook()

        data = {
            "Operation Daily": last_execl_data_list,
            "Fault Alarm Record": last_execl_data_2_list,
        }
        # 遍历字典，将数据写入不同的sheet
        for sheet_name, sheet_data in data.items():
            sheet = workbook.create_sheet(title=sheet_name)
            for index, row_data in enumerate(sheet_data, start=1):
                for col_num, cell_value in enumerate(row_data.values(), start=1):
                    cell = sheet.cell(row=index, column=col_num)
                    cell.value = cell_value
        # 删除sheet页
        del workbook['Sheet']
        # 保存工作簿到文件夹
        workbook.save(path)

    # 上传至minio
    try:
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        url = minio_client.upload_local_file(file_name, path, bucket_name='download')
        os.remove(path)

        # 缓存下载链接到redis
        # key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
        redis_conn = get_redis_connection("default")
        redis_conn.set(key, url, 60 * 5)

    except Exception as e:
        print(traceback.print_exc())


def dateToDataList(start, end):
    # 计算时间段内的时间列表,包含首位
    datestart = datetime.datetime.strptime(start[0:10], '%Y-%m-%d')
    dateend = datetime.datetime.strptime(end[0:10], '%Y-%m-%d')
    data_list = list()
    while datestart <= dateend:
        data_list.append(datestart.strftime('%Y-%m-%d'))
        datestart += datetime.timedelta(days=1)
    return data_list


def deal_one_report(master_station, smonday, esunday):
    execl_data_list = []
    execl_data_2_list = []
    execl_data_3_list = []

    running_reports = RunningReport.objects.filter(station_name=master_station.name, report_type=2,
                                                   datetime__gte=smonday, datetime_end__lte=esunday).order_by(
        'datetime')
    if running_reports:
        for s in running_reports:
            execl_data = {}
            execl_data['开始日期'] = s.datetime
            execl_data['结束日期'] = s.datetime_end
            execl_data['项目名称'] = master_station.name

            execl_data['充电量(kWh)'] = s.charge_cap
            execl_data['放电量(kWh)'] = s.discharge_cap
            execl_data['充放电效率(%)'] = s.comp_rate
            execl_data['充放电次数'] = s.count

            stations_ids = [s.id for s in master_station.stationdetails_set.all()]

            # 故障告警
            ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=stations_ids,
                                                   start_time__gte=s.datetime, end_time__lte=s.datetime_end)
            alarm_ins = (
                ins.filter(type__in=[1, 2]).values("status", "type", "start_time", "end_time", "details", "id",
                                              "note", "station_id", "device").order_by("-start_time"))
            for alarm in alarm_ins:
                station = models.StationDetails.objects.filter(id=alarm['station_id']).first()
                execl_data_2 = {}
                execl_data_2['序号'] = alarm['id']
                execl_data_2['项目名称'] = station.station_name if station else '--'
                execl_data_2['设备名称'] = alarm["device"]
                execl_data_2['报文'] = alarm["details"]
                if alarm["type"] == 0:
                    execl_data_2['告警分类'] = '告警'
                elif alarm["type"] == 1:
                    execl_data_2['告警分类'] = '故障'
                elif alarm["type"] == 2:
                    execl_data_2['告警分类'] = '报警'
                alarm["start_time"] = alarm["start_time"].strftime('%Y-%m-%d %H:%M:%S')
                alarm["end_time"] = alarm["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                execl_data_2['开始时间'] = alarm["start_time"]
                execl_data_2['结束时间'] = alarm["end_time"]

                execl_data_2_list.append(execl_data_2)
                alarm["details"] = (station.station_name if station else '--') + ":" + alarm["device"] + alarm["details"]

            # 统计 type = 0的告警数量
            # alarm_count = ins.filter(type=0).count()
            # 统计 type = 1的故障数量
            # fault_count = ins.filter(type=1).count()
            # 统计 type = 2的报警数量
            # warning_count = ins.filter(type=2).count()
            # alarm_count_ = alarm_count + fault_count + warning_count

            execl_data['尖时充电量（kWh）'] = s.spike_charge
            execl_data['峰时充电量（kWh）'] = s.peak_charge
            execl_data['平时充电量（kWh）'] = s.flat_charge
            execl_data['谷时充电量（kWh）'] = s.valley_charge
            execl_data['深谷时充电量（kWh）'] = s.dvalley_charge
            execl_data['尖时放电量（kWh）'] = s.spike_discharge
            execl_data['峰时放电量（kWh）'] = s.peak_discharge
            execl_data['平时放电量（kWh）'] = s.flat_discharge
            execl_data['谷时放电量（kWh）'] = s.valley_discharge
            execl_data['深谷时放电量（kWh）'] = s.dvalley_discharge

            execl_data['基准充电量(kWh)'] = s.theory_charge
            execl_data['基准充电量完成率(%)'] = s.theory_charge_comp_rate
            execl_data['基准放电量(kWh)'] = s.theory_discharge
            execl_data['基准放电量完成率(%)'] = s.theory_discharge_comp_rate
            # execl_data['基准充电量完成率(%)'] = last_dict['cumulative_charge_disg_efficiency']
            execl_data['累计充电量（kWh）'] = s.accu_charge
            execl_data['累计放电量（kWh）'] = s.accu_discharge
            execl_data['累计充放电效率（%）'] = s.effic
            execl_data['收益（元）'] = s.income
            execl_data['故障告警数量'] = s.fault + s.alarm  # 故障告警数量
            execl_data['分析'] = s.analyse

            execl_data_list.append(execl_data)

            time_list = dateToDataList(s.datetime, s.datetime_end)

            for ll in time_list:
                execl_data_3 = {}
                day_dict = {}
                execl_data_3['日期'] = ll
                execl_data_3['并网点名称'] = master_station.name
                day_dict['time'] = ll
                day_dict['daily_chag_amount'] = 0  # 充电量
                day_dict['daily_disg_amount'] = 0  # 放电量
                day_dict['charge_disg_ratio'] = 0
                day_dict['spike_charge'] = 0  # 尖峰充电量
                day_dict['spike_discharge'] = 0  # 尖峰放电量
                day_dict['peak_charge'] = 0  # 峰时充电量
                day_dict['peak_discharge'] = 0  # 峰时放电量
                day_dict['flat_charge'] = 0  # 平时充电量
                day_dict['flat_discharge'] = 0  # 平时放电量
                day_dict['valley_charge'] = 0  # 谷时充电量
                day_dict['valley_discharge'] = 0  # 谷时放电量
                day_dict['dvalley_charge'] = 0  # 深谷时充电量
                day_dict['dvalley_discharge'] = 0  # 深谷时放电量
                station_insss = RunningReport.objects.filter(station_name=master_station.name, datetime=ll,
                                                             report_type=1).order_by('id')
                if station_insss:
                    for s in station_insss:
                        # day_dict['id'] = s.id  #
                        day_dict['daily_chag_amount'] = s.charge_cap  # 充电量
                        day_dict['daily_disg_amount'] = s.discharge_cap  # 放电量
                        day_dict['charge_disg_ratio'] = 0
                        if day_dict['daily_chag_amount']:
                            if day_dict['daily_chag_amount'] == 0:
                                day_dict['charge_disg_ratio'] = 0
                            else:
                                day_dict['charge_disg_ratio'] = float('%.2f' % (
                                        (day_dict['daily_disg_amount'] / day_dict[
                                            'daily_chag_amount']) * 100))

                        execl_data_3['充电量(kWh)'] = day_dict['daily_chag_amount']
                        execl_data_3['放电量(kWh)'] = day_dict['daily_disg_amount']
                        execl_data_3['充放电量完成率(%)'] = day_dict['charge_disg_ratio']

                        day_dict['spike_charge'] = s.spike_charge  # 尖峰充电量
                        day_dict['spike_discharge'] = s.spike_discharge  # 尖峰放电量
                        day_dict['peak_charge'] = s.peak_charge  # 峰时充电量
                        day_dict['peak_discharge'] = s.peak_discharge  # 峰时放电量
                        day_dict['flat_charge'] = s.flat_charge  # 平时充电量
                        day_dict['flat_discharge'] = s.flat_discharge  # 平时放电量
                        day_dict['valley_charge'] = s.valley_charge  # 谷时充电量
                        day_dict['valley_discharge'] = s.valley_discharge  # 谷时放电量

                        day_dict['dvalley_charge'] = s.dvalley_charge  # 深谷时充电量
                        day_dict['dvalley_discharge'] = s.dvalley_discharge  # 深谷时放电量

                        execl_data_3['基准充电量(kWh)'] = s.theory_charge  # 逐日基准充电量
                        execl_data_3['基准充电量完成率(%)'] = s.theory_charge_comp_rate  # 基准充电量完成率
                        execl_data_3['基准放电量(kWh)'] = s.theory_discharge  # 逐日基准放电量
                        execl_data_3['基准放电量完成率(%)'] = s.theory_discharge_comp_rate  # 基准放电量完成率

                        execl_data_3['尖时充电量（kWh）'] = day_dict['spike_charge']
                        execl_data_3['峰时充电量（kWh）'] = day_dict['peak_charge']
                        execl_data_3['平时充电量（kWh）'] = day_dict['flat_charge']
                        execl_data_3['谷时充电量（kWh）'] = day_dict['valley_charge']
                        execl_data_3['深谷时充电量（kWh）'] = day_dict['dvalley_charge']
                        execl_data_3['尖时放电量（kWh）'] = day_dict['spike_discharge']
                        execl_data_3['峰时放电量（kWh）'] = day_dict['peak_discharge']
                        execl_data_3['平时放电量（kWh）'] = day_dict['flat_discharge']
                        execl_data_3['谷时放电量（kWh）'] = day_dict['valley_discharge']
                        execl_data_3['深谷时放电量（kWh）'] = day_dict['dvalley_discharge']

                        execl_data_3['收益（元）'] = s.income  # 当日收益
                        execl_data_3['故障告警数量'] = s.fault + s.alarm

                        execl_data_3_list.append(execl_data_3)

    return execl_data_list, execl_data_2_list, execl_data_3_list


def upload_week_report_for_download(e_time, esunday, master_stations, s_time, smonday, user_id):
    execl_data_111 = {"开始日期": "开始日期", "结束日期": "结束日期", "项目名称": "项目名称", "充电量(kWh)": "充电量(kWh)",
                      "放电量(kWh)": "放电量(kWh)", "充放电效率(%)": "充放电效率(%)", "充放电次数": "充放电次数", "尖时充电量（kWh）": "尖时充电量（kWh）",
                      "峰时充电量（kWh）": "峰时充电量（kWh）", "平时充电量（kWh）": "平时充电量（kWh）", "谷时充电量（kWh）": "谷时充电量（kWh）",
                      "深谷时充电量（kWh）": "深谷时充电量（kWh）",
                      "尖时放电量（kWh）": "尖时放电量（kWh）", "峰时放电量（kWh）": "峰时放电量（kWh）", "平时放电量（kWh）": "平时放电量（kWh）",
                      "谷时放电量（kWh）": "谷时放电量（kWh）", "深谷时放电量（kWh）": "深谷时放电量（kWh）",
                      "基准充电量(kWh)": "基准充电量(kWh)", "基准充电量完成率(%)": "基准充电量完成率(%)",
                      "基准放电量(kWh)": "基准放电量(kWh)", "基准放电量完成率(%)": "基准放电量完成率(%)",
                      "累计充电量（kWh）": "累计充电量（kWh）", "累计放电量（kWh）": "累计放电量（kWh）", "累计充放电效率（%）": "累计充放电效率（%）",
                      "收益（元）": "收益（元）", "故障告警数量": "故障告警数量", "分析内容": "分析内容"}
    execl_data_333 = {"日期": "日期", "并网点名称": "并网点名称", "充电量(kWh)": "充电量(kWh)", "放电量(kWh)": "放电量(kWh)",
                      "充放电效率(%)": "充放电效率(%)", "基准充电量(kWh)": "基准充电量(kWh)", "基准充电量完成率(%)": "基准充电量完成率(%)",
                      "基准放电量(kWh)": "基准放电量(kWh)", "基准放电量完成率(%)": "基准放电量完成率(%)",
                      "尖时充电量（kWh）": "尖时充电量（kWh）", "峰时充电量（kWh）": "峰时充电量（kWh）", "平时充电量（kWh）": "平时充电量（kWh）",
                      "谷时充电量（kWh）": "谷时充电量（kWh）", "深谷时充电量（kWh）": "深谷时充电量（kWh）","尖时放电量（kWh）": "尖时放电量（kWh）",
                      "峰时放电量（kWh）": "峰时放电量（kWh）",
                      "平时放电量（kWh）": "平时放电量（kWh）", "谷时放电量（kWh）": "谷时放电量（kWh）", "深谷时放电量（kWh）": "深谷时放电量（kWh）",
                      "收益（元）": "收益（元）", "故障告警数量": "故障告警数量"}
    execl_data_222 = {"序号": "序号", "并网点名称": "并网点名称", "设备名称": "设备名称", "报文": "报文", "告警分类": "告警分类", "开始时间": "开始时间",
                      "结束时间": "结束时间"}
    execl_data_list = []
    execl_data_2_list = []
    execl_data_3_list = []
    execl_data_list.append(execl_data_111)
    execl_data_2_list.append(execl_data_222)
    execl_data_3_list.append(execl_data_333)

    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        futures = list()
        for master_station in master_stations:
            future = executor.submit(deal_one_report, master_station, smonday, esunday)
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            execl_data_list_, execl_data_2_list_, execl_data_3_list_ = future.result()
            if execl_data_list_:
                execl_data_list += execl_data_list_
            if execl_data_2_list_:
                execl_data_2_list += execl_data_2_list_
            if execl_data_3_list_:
                execl_data_3_list += execl_data_3_list_

    file_name = f"{s_time}~{e_time}天禄运行周报_{user_id}.xlsx"
    path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

    workbook = openpyxl.Workbook()
    data = {
        "运行数据": execl_data_list,
        "逐日运行数据": execl_data_3_list,
        "故障告警记录": execl_data_2_list,
    }
    # 遍历字典，将数据写入不同的sheet
    for sheet_name, sheet_data in data.items():
        sheet = workbook.create_sheet(title=sheet_name)
        for index, row_data in enumerate(sheet_data, start=1):
            for col_num, cell_value in enumerate(row_data.values(), start=1):
                cell = sheet.cell(row=index, column=col_num)
                cell.value = cell_value
    # 删除sheet页
    del workbook['Sheet']
    # 保存工作簿到文件夹
    workbook.save(path)

    # 上传至minio
    try:
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        url = minio_client.upload_local_file(file_name, path, bucket_name='download')
        os.remove(path)

        # 缓存下载链接到redis
        key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
        redis_conn = get_redis_connection("default")
        redis_conn.set(key, url, 60 * 5)
        print(644, '缓存周报结束')

    except Exception as e:
        print(traceback.print_exc())
