package com.robestec.dailyproduce.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimCreateDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimOperationDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimQueryDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimUpdateDTO;
import com.robestec.dailyproduce.project.mapper.ProjectSimMapper;
import com.robestec.dailyproduce.project.model.ProjectSim;
import com.robestec.dailyproduce.project.service.ProjectSimService;
import com.robestec.dailyproduce.project.vo.ProjectSimVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectSimServiceImpl
        extends SuperServiceImpl<ProjectSimMapper, ProjectSim>
        implements ProjectSimService {

    @Override
    public PageResult<ProjectSimVO> querySims(ProjectSimQueryDTO queryDTO) {
        LambdaQueryWrapper<ProjectSim> wrapper = new LambdaQueryWrapper<ProjectSim>()
                .eq(queryDTO.getProjectId() != null, ProjectSim::getProjectId, queryDTO.getProjectId());

        Page<ProjectSim> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<ProjectSim> result = this.page(page, wrapper);

        List<ProjectSimVO> voList = result.getRecords().stream()
                .map(item -> BeanUtil.copyProperties(item, ProjectSimVO.class))
                .collect(Collectors.toList());

        return PageResult.<ProjectSimVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    public Long createSim(ProjectSimCreateDTO createDTO) {
        ProjectSim entity = BeanUtil.copyProperties(createDTO, ProjectSim.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    public void updateSim(ProjectSimUpdateDTO updateDTO) {
        ProjectSim entity = BeanUtil.copyProperties(updateDTO, ProjectSim.class);
        this.updateById(entity);
    }

    @Override
    public void deleteSim(Long id) {
        this.removeById(id);
    }

    @Override
    public ProjectSimVO getSim(Long id) {
        ProjectSim entity = this.getById(id);
        return BeanUtil.copyProperties(entity, ProjectSimVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSimList(List<ProjectSimCreateDTO> projectSimCreateDTOList) {
        this.saveBatch(BeanUtil.copyToList(projectSimCreateDTOList, ProjectSim.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateSimList(List<ProjectSimUpdateDTO> projectSimUpdateDTOList) {
        for (ProjectSimUpdateDTO projectSimUpdateDTO : projectSimUpdateDTOList) {
            this.saveOrUpdate(BeanUtil.copyProperties(projectSimUpdateDTO, ProjectSim.class));
        }
    }

    @Override
    public void deleteSimListByProjectId(Long projectId) {
        this.update(new ProjectSim(), Wrappers.<ProjectSim>lambdaUpdate()
                .eq(ProjectSim::getProjectId, projectId)
                .set(ProjectSim::getIsDelete, 1)
        );
    }

    @Override
    public List<ProjectSimVO> getProjectSimByProjectId(Long projectId) {
        List<ProjectSim> projectSimList = this.list(Wrappers.<ProjectSim>lambdaQuery()
                .eq(ProjectSim::getProjectId, projectId)
                .eq(ProjectSim::getIsDelete, 0));
        return BeanUtil.copyToList(projectSimList, ProjectSimVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationProjectSim(Long projectId, List<ProjectSimOperationDTO> operationDTOList) {
        //传值为空 删除所有项目下关系
        if (CollectionUtil.isEmpty(operationDTOList)) {
            deleteSimListByProjectId(projectId);
            return;
        }
        //是否存在修改
        List<ProjectSimOperationDTO> updateList = operationDTOList.stream().filter(item ->
                ObjectUtil.isNotNull(item.getId())).collect(Collectors.toList());

        //如果无修改的存在 先删再增加
        if (CollectionUtil.isEmpty(updateList)) {
            deleteSimListByProjectId(projectId);
            operationDTOList.forEach(operationDTO -> {
                operationDTO.setProjectId(projectId);
                createSimList(BeanUtil.copyToList(operationDTOList, ProjectSimCreateDTO.class));
            });
            return;
        }

        List<Long> idList = updateList.stream().map(ProjectSimOperationDTO::getId).collect(Collectors.toList());

        //如果有修改的存在 先删再修改
        this.update(Wrappers.<ProjectSim>lambdaUpdate()
                .set(ProjectSim::getIsDelete, 1)
                .eq(ProjectSim::getProjectId, projectId)
                .notIn(ProjectSim::getId, idList));

        updateList.forEach(item -> {
            ProjectSim entity = BeanUtil.copyProperties(item, ProjectSim.class);
            this.updateById(entity);
        });

        List<ProjectSimOperationDTO> addList = operationDTOList.stream().filter(item ->
                ObjectUtil.isNull(item.getId())).collect(Collectors.toList());

        //存在全部新增
        if (CollectionUtil.isEmpty(addList)) return;

        addList.forEach(operationDTO -> {
            operationDTO.setProjectId(projectId);
            this.save(BeanUtil.copyProperties(operationDTO, ProjectSim.class));
        });
    }
}