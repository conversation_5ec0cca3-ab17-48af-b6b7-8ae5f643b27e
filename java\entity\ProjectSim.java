package com.robestec.dailyproduce.project.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 5G专用卡信息
 */
@Data
@TableName("t_project_sim")
public class ProjectSim extends SuperEntity {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 卡号（唯一）
     */
    private String cardNumber;

    /**
     * 运营商（枚举：中国移动、中国联通、中国电信、其他）
     */
    private String operator;

    /**
     * 有效日期
     */
    private Date expiryDate;

    /**
     * 安装位置
     */
    private String installLocation;

    /**
     * 保管人
     */
    private String keeper;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 备注
     */
    private String remark;
}