import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "TianLuAppBackend.settings")
django.setup()

"""AES对称加密"""
import base64
from Crypto.Cipher import AES


class EncryptDate:
    def __init__(self, key):
        self.key = key.encode("utf-8")  # 初始化密钥
        self.length = AES.block_size  # 初始化数据块大小，为16位
        self.aes = AES.new(self.key, AES.MODE_ECB)  # 初始化AES,ECB模式的实例
        self.unpad = lambda date: date[0 : -ord(date[-1])]  # 截断函数，去除填充的字符

    def pad(self, text):
        """
        填充函数，使被加密数据的字节码长度是block_size的整数倍
        """
        count = len(text.encode("utf-8"))
        add = self.length - (count % self.length)
        entext = text + (chr(add) * add)
        return entext

    # 加密函数
    def encrypt(self, encrData):
        res = self.aes.encrypt(self.pad(encrData).encode("utf-8"))
        # Base64是网络上最常见的用于传输8Bit字节码的编码方式之一
        msg = str(base64.b64encode(res), encoding="utf-8")
        return msg

    # 解密函数
    def decrypt(self, decrData):
        res = base64.decodebytes(decrData.encode("utf-8"))  # 转为二进制字节流
        msg = self.aes.decrypt(res).decode("utf-8")
        return self.unpad(msg)  # 把之前为了填充为16位多余的那部分截掉


if __name__ == "__main__":
    print("============加密==================")
    key = "jhuijghdnijhygba"  # key 密码,服务器指定
    data = "SAMPLE1"  # 数据
    eg = EncryptDate(key)  # 这里密钥的长度必须是16的倍数
    res1 = eg.encrypt(data)
    print(res1)
    res1 = eg.encrypt(data)

    # print(res1)
    print("============解密==================")
    res2 = eg.decrypt(res1)
    print(res2)
