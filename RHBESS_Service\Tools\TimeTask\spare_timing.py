#!/usr/bin/env python
# coding=utf-8
#@Information:  备件管理定时任务
#<AUTHOR> WYJ
#@Date         : 2023-03-23 16:15:03
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\work_orde_plan copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-23 16:15:05

import sys,os,time
from Tools.Utils.time_utils import timeUtils 
from Tools.DB.mysql_user import user_session
from Application.Models.WorkOrder.spare_disk import SpareDisk
from Application.Models.WorkOrder.spare_info import SpareInfo
# reload(sys)
# sys.setdefaultencoding("utf-8")
from timedtask_log import app_log
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)

from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()


def RunClearFileAndData():
    send_= scheduler.add_job(send_email, 'cron', day='1',hour='0', minute='5')  # 每月第一天0点5分执行
    
    scheduler.start()

def send_email():
    l_time = timeUtils.getAgoMonth(1)  # 上一个月
    app_log.info('timing %s'%l_time)
    plans = user_session.query(SpareInfo).filter(SpareInfo.is_use==1).all()  # 所有备件
    for p in plans:
        disk = SpareDisk(id=p.id,year_mon=l_time,before_num=p.num,is_use=1)
        user_session.merge(disk)
    user_session.commit()
    user_session.close()
      
if __name__ == '__main__':
    RunClearFileAndData()
   
   
    

   
   
   
    



