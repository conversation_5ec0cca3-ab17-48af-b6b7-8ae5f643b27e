package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.robestec.analysis.entity.TPowerDeliverRecords;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 功率计划下发记录Mapper
 * 对应Python中的TPowerDeliverRecords相关数据库操作
 */
@Mapper
public interface TPowerDeliverRecordsMapper extends BaseMapper<TPowerDeliverRecords> {

    /**
     * 分页查询功率计划下发记录
     * 对应Python中PowerPlanList方法的查询逻辑
     */
    @Select("<script>" +
            "SELECT tpdr.*, tph.status, tph.start_time, tph.end_time " +
            "FROM (" +
            "  SELECT power_id, MAX(id) as max_id " +
            "  FROM t_plan_power_records " +
            "  GROUP BY power_id" +
            ") subq " +
            "JOIN t_plan_power_records tppr ON tppr.id = subq.max_id " +
            "JOIN t_power_deliver_records tpdr ON tppr.power_id = tpdr.id " +
            "JOIN t_plan_history tph ON tppr.plan_id = tph.id " +
            "WHERE tpdr.is_use = 1 AND tph.is_use = 1 " +
            "<if test='name != null and name != \"\"'>" +
            "  AND tpdr.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "  AND tph.status = #{status} " +
            "</if>" +
            "<if test='planType != null'>" +
            "  AND tpdr.plan_type = #{planType} " +
            "</if>" +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND tph.start_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND tph.start_time <= #{endTime} " +
            "</if>" +
            "ORDER BY tppr.power_id DESC" +
            "</script>")
    IPage<TPowerDeliverRecords> selectPowerPlanList(
            Page<TPowerDeliverRecords> page,
            @Param("name") String name,
            @Param("status") Integer status,
            @Param("planType") Integer planType,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 根据名称和用户ID查询是否存在重复记录
     * 对应Python中的重复性检查逻辑
     */
    @Select("SELECT COUNT(*) FROM t_power_deliver_records " +
            "WHERE name = #{name} AND user_id = #{userId} AND is_use = 1")
    int countByNameAndUserId(@Param("name") String name, @Param("userId") Long userId);

    /**
     * 根据名称和用户ID查询记录
     * 对应Python中的重复性检查逻辑
     */
    @Select("SELECT * FROM t_power_deliver_records " +
            "WHERE name = #{name} AND user_id = #{userId} AND is_use = 1")
    TPowerDeliverRecords selectByNameAndUserId(@Param("name") String name, @Param("userId") Long userId);

    /**
     * 根据ID和用户ID查询记录
     * 对应Python中的权限验证查询
     */
    @Select("SELECT * FROM t_power_deliver_records " +
            "WHERE id = #{id} AND user_id = #{userId} AND is_use = 1")
    TPowerDeliverRecords selectByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 软删除记录
     * 对应Python中的删除逻辑
     */
    @Update("UPDATE t_power_deliver_records SET is_use = 0 WHERE id = #{id}")
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据用户ID查询记录列表
     */
    @Select("SELECT * FROM t_power_deliver_records " +
            "WHERE user_id = #{userId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPowerDeliverRecords> selectByUserId(@Param("userId") Long userId);

    /**
     * 更新记录信息
     * 对应Python中PowerPlanUpdate方法的更新逻辑
     */
    @Update("<script>" +
            "UPDATE t_power_deliver_records SET " +
            "name = #{name}, " +
            "power_list = #{powerList}, " +
            "station_list = #{stationList}, " +
            "user_id = #{userId}, " +
            "user_name = #{userName}, " +
            "plan_type = #{planType}, " +
            "update_time = NOW() " +
            "WHERE id = #{id}" +
            "</script>")
    int updatePowerPlan(
            @Param("id") Long id,
            @Param("name") String name,
            @Param("powerList") String powerList,
            @Param("stationList") String stationList,
            @Param("userId") Long userId,
            @Param("userName") String userName,
            @Param("planType") Integer planType
    );
}
