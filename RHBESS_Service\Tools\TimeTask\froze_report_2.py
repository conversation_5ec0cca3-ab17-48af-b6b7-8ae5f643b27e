#!/usr/bin/env python
# coding=utf-8
#@Information:

import sys,os,getopt
from Application.HistoryData.his_bams import _return_db_con
from Application.Models.User.report_bms_halun_f import FReportBmsHalun
from Application.Models.User.report_bms_ygqn_f import FReportBmsYgqn
from Application.Models.User.report_pcs_f import FReportPcs
from Application.Models.User.sda_pcs_cu_name import SdaPcsCu
from Tools.DB.mysql_user import user_session
from apscheduler.schedulers.blocking import BlockingScheduler
import logging
from Tools.DB.ygqn_his import ygqn7_session, ygqn8_session
logging.basicConfig()
from Application.Models.User.report_bms_baodian_f import FReportBmsBaodian
from Application.Models.User.report_bms_binhai_f import FReportBmsbinhai
from Application.Models.User.report_bms_datong_f import FReportBmsDatong
from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
from Application.Models.User.report_bms_guizhou_f import FReportBmsGuizhou
from Application.Models.User.report_bms_houma_f import FReportBmsHouma
from Application.Models.User.report_bms_taicang_f import FReportBmsTaicang
from Application.Models.User.report_bms_ygzhen_f import FReportBmsYgzhen
from Application.Models.User.report_bms_zgtian_f import FReportBmsZgtian
from Application.HistoryData.his_bams import *
from Tools.DB.datong_his import datong1_session, datong2_session, datong3_session, datong4_session
from Tools.DB.guizhou_his import guizhou8_session, guizhou7_session, guizhou5_session, guizhou4_session, \
    guizhou3_session, guizhou2_session, guizhou1_session, guizhou6_session
from Tools.DB.houma_his import  houmaa1_session, houmaa2_session, houmab1_session, houmab2_session
from Tools.DB.halun_his import halun_session
from Tools.DB.taicang_his import taicang_session
from Tools.DB.binhai1_his import binhai1_session
from Tools.DB.binhai2_his import binhai2_session
from Tools.DB.ygzhen_his import ygzhen1_session,ygzhen2_session
from Tools.DB.baodian_his import baodian1_session,baodian2_session,baodian3_session,baodian4_session,baodian5_session
from Tools.DB.zgtian_his import zgtian1_session,zgtian2_session,zgtian3_session,zgtian4_session
from Tools.DB.mysql_his import dongmu_session
# 定义各项目查询数据的配置(PCS)
db_his = {"taicang": [taicang_session], "binhai": [binhai1_session, binhai2_session], "halun": [halun_session],"ygzhen": [ygzhen1_session, ygzhen2_session],"zgtian": [zgtian1_session, zgtian2_session, zgtian3_session, zgtian4_session],"houma": [houmaa1_session, houmaa2_session, houmab1_session, houmab2_session],
  "datong": [datong1_session, datong2_session, datong3_session, datong4_session],
 "guizhou": [guizhou1_session, guizhou2_session, guizhou3_session, guizhou4_session,guizhou5_session,guizhou6_session, guizhou7_session, guizhou8_session],"ygqn": [ygqn7_session, ygqn8_session]}
baodian_his = {"tfStbodian1": baodian1_session, "tfStbodian2": baodian2_session, "tfStbodian3": baodian3_session,"tfStbodian4": baodian4_session, "tfStbodian5": baodian5_session}


#查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

path = basepath + "/Application/Cfg/test.ini"
model_config.read(path,encoding='utf-8')
dongmu_num = 7

def _return_db_con_pcs(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_his[d]
    else:
        if db == 'taicang' or db == 'halun':
            return db_his[db][0]
        elif db == 'houma':
            if d == 'A1':
                return db_his[db][0]
            elif d == 'A2':
                return db_his[db][1]
            elif d == 'B1':
                return db_his[db][2]
            elif d == 'B2':
                return db_his[db][3]
            return db_his[db][0]
        else:
            return db_his[db][int(d) - 1]

station = ['binhai','taicang','ygzhen','halun','baodian','dongmu','zgtian','houma','datong','guizhou','ygqn']
# station = ['ygqn']

def frozeDataDay(day,db):
    # 冻结一天的数据
    startT = ('%s 00:00:01' % day)
    endT = ('%s 23:59:59' % day)
    a = timeUtils.getBetweenMonth(('%s 00:00:01'%day), ('%s 23:59:59'%day))
    tables = 'r_measure' + pd.Series(a)
    st = timeUtils.timeStrToTamp(startT)  # 起始时间绝对秒
    ed = timeUtils.timeStrToTamp(endT)  # 截止时间绝对秒
    name_f_list = model_config.get('peizhi', 'device_arr')
    list1 = eval(name_f_list)[db][1:]
    name_f_list1 = model_config.get('peizhi', 'piece_arr')
    list2 = eval(name_f_list1)[db][1:]
    name_f_list2 = model_config.get('peizhi', 'device_obj')
    list3 = eval(name_f_list2)
    name_f_list3 = model_config.get('peizhi', 'device_obj_cu')
    list4 = eval(name_f_list3)
    name_f_list4 = model_config.get('peizhi', 'halun_cu')#哈伦簇name
    list5 = eval(name_f_list4)
    name_f_list5 = model_config.get('peizhi', 'device_obj_gz')  # 贵州簇name
    list6 = eval(name_f_list5)

    b = []
    for k in list2:
        b.append(k['value'])
    a = []
    for i in list1:
        a.append(i['value'])
    data = {}
    for table in tables.to_list():
        if db == 'halun':
            for d in a:
                b = ['1', '2', '3', '4', '5', '6', '7']
                ii = ('.'.join(d.split('.')[:-1])) + '.'
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == ii).first()
                pcs_name = v_pcs_name[0]
                b.append(pcs_name[-1])
                if pcs_name not in data:
                    data[pcs_name] = {}
                for d1 in b:
                    dd = d.split(".")[0][-1]
                    db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, list3[0])  # 累计充电电量的name
                    n = '%s%s%s' % (d, d1, list3[1])  # 累计放电电量的name

                    h_CellVmin = '%s%s%s' % (d, list5[3], d1)  # 哈伦电压最低值
                    h_CellVmax = '%s%s%s' % (d, list5[4], d1)  # 哈伦电压最高值
                    h_CellTmin = '%s%s%s' % (d, list5[5], d1)  # 哈伦温度最低值
                    h_CellTmax = '%s%s%s' % (d, list5[6], d1)  # 哈伦温度最高值
                    h_Sys_SOH = '%s%s' % (d, list5[7])  # 哈伦系统SOH
                    try:
                        v1 = _select_get_his_value_222(db_con, table, m, st, ed, startT, endT, vmax=1000000,minV=0.1)  # 总充电量
                        if v1:
                            cu_name = '电池簇%s' % (d1)
                            en_cu_name = 'Cluster %s' % (d1)
                            if cu_name not in data[pcs_name]:
                                data[pcs_name][cu_name] = {}
                            value1 = v1['value']
                            max_value1 = max(value1)
                            min_value1 = min(value1)
                        else:
                            max_value1 = 0
                            min_value1 = 0
                    except:
                        max_value1 = 0
                        min_value1 = 0
                    chag = max_value1 - min_value1
                    try:
                        v2 = _select_get_his_value_222(db_con, table, n, st, ed, startT, endT, vmax=1000000,minV=0.1)  # 总放电量
                        if v2:
                            cu_name = '电池簇%s' % (d1)
                            en_cu_name = 'Cluster %s' % (d1)
                            if cu_name not in data[pcs_name]:
                                data[pcs_name][cu_name] = {}
                            value2 = v2['value']
                            max_value2 = max(value2)
                            min_value2 = min(value2)
                        else:
                            max_value2 = 0
                            min_value2 = 0
                    except:
                        max_value2 = 0
                        min_value2 = 0
                    disg = max_value2 - min_value2
                    if int(chag) == 0:
                        ratio = 0
                    elif int(disg) == 0:
                        ratio = 0
                    else:
                        ratio = ('%.3f' % ((float(disg) / float(chag)) * 100))
                        if float(ratio) > 100:
                            ratio = 100
                        elif float(ratio) < 0:
                            ratio = 0
                    try:
                        v1_s = _select_get_his_value_22(db_con, table, m_s, st, ed, startT, endT, '6H', vmax=100.0001,minV=0.1)  # 电池簇SOH
                        if v1_s['value'] != []:
                            cu_name = '电池簇%s' % (d1)
                            en_cu_name = 'Cluster %s' % (d1)
                            if cu_name not in data[pcs_name]:
                                data[pcs_name][cu_name] = {}
                            try:
                                min_s = min(v1_s['value'].remove(0))
                            except:
                                min_s = min(v1_s['value'])
                        else:
                            min_s = 99
                    except:
                        min_s = 99
                    db_con.close()

                    value2_T = []
                    value2_V = []
                    try:
                        v1_s = _select_get_his_value_22(db_con, table, h_Sys_SOH, st, ed, startT, endT, '6H',vmax=100.0001, minV=0.1)  # 电池簇SOH
                        if v1_s['value'] != []:
                            try:
                                min_s = min(v1_s['value'].remove(0))
                            except:
                                min_s = min(v1_s['value'])
                        else:
                            min_s = 100
                    except:
                        min_s = 100
                    max_T, max_V, min_T, min_V, value2_T, value2_V = cu_T_V_max(db_con, ed, endT, h_CellTmin,
                                                                                h_CellTmax, h_CellVmin, h_CellVmax,
                                                                                st, startT, table, value2_T,
                                                                                value2_V)
                    db_con.close()


                    print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                    user_session.merge(
                        FReportBmsHalun(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag, disg=disg,
                                        max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                        ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
        else:
            for d in a:
                inx= a.index(d)
                if db == 'baodian':
                    d_ = d+'.'
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d_,SdaPcsCu.station_name == db).first()
                else:
                    v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.unit_id == d,SdaPcsCu.station_name == db).first()
                pcs_name = v_pcs_name[0]
                if pcs_name not in data:
                    data[pcs_name] = {}
                for d1 in b:
                    if db == 'baodian':
                        d1_=d1[1:]
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname,SdaPcsCu.cluster_name).filter(SdaPcsCu.cluster_id == d1_,SdaPcsCu.station_name == db).first()
                    else:
                        v_cu_name = user_session.query(SdaPcsCu.cluster_cname, SdaPcsCu.cluster_name).filter(SdaPcsCu.cluster_id == d1, SdaPcsCu.station_name == db).first()
                    cu_name = v_cu_name[0]
                    en_cu_name = v_cu_name[1]
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = {}
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    if db =='guizhou':
                        m = '%s%s%s' % (d,  list6[0],d1)  # 累计充电电量的name
                        n = '%s%s%s' % (d,  list6[1],d1)  # 累计放电电量的name
                        n0 = '%s%s%s' % (d, list6[6],d1)  # 电池簇最低温度
                        n1 = '%s%s%s' % (d, list6[5],d1)  # 电池簇最高温度
                        n2 = '%s%s%s' % (d,  list6[4],d1)  # 电池簇最低电压
                        n3 = '%s%s%s' % (d, list6[3],d1)  # 电池簇最高电压
                        m_s = '%s%s%s' % (d, list6[2],d1)  # 电池簇SOh
                    else:
                        m = '%s%s%s' % (d, d1, list3[0])  # 累计充电电量的name
                        n = '%s%s%s' % (d, d1, list3[1])  # 累计放电电量的name
                        n0 = '%s%s%s' % (d, d1, list3[6])  # 电池簇最低温度
                        n1 = '%s%s%s' % (d, d1, list3[5])  # 电池簇最高温度
                        n2 = '%s%s%s' % (d, d1, list3[4])  # 电池簇最低电压
                        n3 = '%s%s%s' % (d, d1, list3[3])  # 电池簇最高电压
                        m_s = '%s%s%s' % (d, d1, list3[2])# 电池簇SOh

                    b_m_t = '%s%s%s' % (d, d1, list4[7])# 保电温度极差
                    b_m_v = '%s%s%s' % (d, d1, list4[6])# 保电电压极差
                    try:
                        v1 = _select_get_his_value_222(db_con, table, m, st, ed,startT, endT, vmax=1000000,minV=0.1)  # 总充电量
                        value1 = v1['value']
                        max_value1 = max(value1)
                        min_value1 = min(value1)
                    except:
                        max_value1 = 0
                        min_value1 = 0

                    chag = max_value1-min_value1

                    try:
                        v2 = _select_get_his_value_222(db_con, table, n, st, ed,startT, endT, vmax=1000000,minV=0.1)  # 总放电量
                        value2 = v2['value']
                        max_value2 = max(value2)
                        min_value2 = min(value2)
                    except:
                        max_value2 = 0
                        min_value2 = 0

                    disg = max_value2-min_value2
                    if int(chag) == 0:
                        ratio = 0
                    elif int(disg) == 0:
                        ratio = 0
                    else:
                        ratio = ('%.3f' % ((float(disg) / float(chag)) * 100))
                        if float(ratio) > 100:
                            ratio = 100
                        elif float(ratio) < 0:
                            ratio = 0
                    try:
                        v1_s = _select_get_his_value_22(db_con, table, m_s, st, ed, startT, endT,'6H',vmax=100.0001,minV=0.1)  # 电池簇SOH
                        if v1_s['value'] != []:
                            try:
                                min_s=min(v1_s['value'].remove(0))
                            except:
                                min_s = min(v1_s['value'])
                        else:
                            min_s=99
                    except:
                        min_s=99
                    db_con.close()
                    if db == 'baodian':
                        try:
                            v2_T = _select_get_his_value_22(db_con, table, b_m_t, st, ed, startT, endT,'6H', vmax=65,minV=0.1)  # 电池簇最高温度极差
                            v2_V = _select_get_his_value_22(db_con, table, b_m_v, st, ed, startT, endT,'6H',vmax=5,minV=0.1)  # 电池簇最高电压极差
                            value2_T = v2_T['value']  # 最高温度极差
                            value2_V = v2_V['value']  # 最高电压极差
                            try:
                                max_T = min(value2_T.remove(0))
                            except:
                                max_T = min(value2_T)
                            try:
                                max_V = min(value2_V.remove(0))
                            except:
                                max_V = min(value2_V)
                        except:
                            max_T = 0
                            max_V = 0

                        db_con.close()
                    else:
                        value2_T = []
                        value2_V = []
                        max_T, max_V, min_T, min_V, value2_T, value2_V = cu_T_V_max(db_con, ed, endT, n0, n1, n2, n3,st, startT, table, value2_T,value2_V)
                        db_con.close()
                    if db == 'binhai':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsbinhai(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                             disg=disg,
                                             max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                             ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'baodian':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsBaodian(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                              disg=disg,
                                              max_t=max_T, max_v=max_V, day=day, min_soh=min_s, ratio=float(ratio),
                                              op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'ygzhen':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsYgzhen(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                             disg=disg,
                                             max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                             ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'zgtian':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsZgtian(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                             disg=disg,
                                             max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                             ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'houma':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsHouma(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                            disg=disg,
                                            max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                            ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'taicang':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsTaicang(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                              disg=disg,
                                              max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day,
                                              min_soh=min_s, ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(),
                                              cause=1))
                    elif db == 'datong':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsDatong(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                             disg=disg,
                                             max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day, min_soh=min_s,
                                             ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(), cause=1))
                    elif db == 'guizhou':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsGuizhou(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                              disg=disg,
                                              max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day,
                                              min_soh=min_s, ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(),
                                              cause=1))
                    elif db == 'ygqn':
                        print ('db:', db, '--day:', day, 'PCS--name:', pcs_name, 'cu--name:', cu_name)
                        user_session.merge(
                            FReportBmsYgqn(pcs_name=pcs_name, cu_name=cu_name, en_cu_name=en_cu_name, chag=chag,
                                              disg=disg,
                                              max_t=max_T, min_t=min_T, max_v=max_V, min_v=min_V, day=day,
                                              min_soh=min_s, ratio=float(ratio), op_ts=timeUtils.getNewTimeStr(),
                                              cause=1))
    if db =='ygqn':
        pass
    else:
        name_f_list = model_config.get('peizhi', db)  # name前半部分
        name_f_list = json.loads(name_f_list)
        conf_name = get_conf_name(db)
        me = model_config.get('peizhi', conf_name[2])  # name后半部分
        name_n = []
        for name_f in name_f_list:
            n_ = '%s%s' % (name_f, me)
            name_n.append(n_)
        for table in tables.to_list():
            for d in name_n:
                if db =='guizhou':
                    dd = d.split(".")[0][-2]
                else:
                    dd = d.split(".")[0][-1]
                ii = ('.'.join(d.split('.')[:-1]))+'.'
                v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == ii).first()
                pcs_name = v_pcs_name[0]
                if db == 'baodian':
                    ddd=d.split(".")[0]
                    db_con = _return_db_con_pcs(db, ddd)  # 生成数据库连接
                else:
                    db_con = _return_db_con_pcs(db, dd)  # 生成数据库连接
                try:
                    v1 = _select_get_his_value_dm(db_con, table, d, st, ed, max_=1.0001,min_=0.0001)
                    max_value1 = max(v1['value'])
                    if v1['value'] != []:
                        try:
                            min_value1 = min(v1['value'].remove(0))
                        except:
                            min_value1 = min(v1['value'])
                except:
                    max_value1 = 1
                    min_value1 = 0
                db_con.close()
                print ('db:', db, '--day:', day, 'PCS--name:', pcs_name)
                user_session.merge(FReportPcs(pcs_name=pcs_name, max_p=max_value1, min_p=min_value1, day=day, station=db,op_ts=timeUtils.getNewTimeStr(), cause=1))
        user_session.commit()
        user_session.close()
def cu_T_V_max(db_con, ed, endT, n0, n1, n2, n3, st, startT, table, value2_T, value2_V):
    '''电池簇温度电压极差'''
    try:
        v1_T = _select_get_his_value_22(db_con, table, n0, st, ed, startT, endT,'6H', vmax=65,minV=0.1)  # 电池簇最低温度
        v1_V = _select_get_his_value_22(db_con, table, n2, st, ed, startT, endT,'6H', vmax=5,minV=0.1)  # 电池簇最低电压
        value1_T = v1_T['value']  # 最低温度
        value1_V = v1_V['value']  # 最低电压
        try:
            min_T = min(value1_T.remove(0))
        except:
            min_T = min(value1_T)
        try:
            min_V = min(value1_V.remove(0))
        except:
            min_V = min(value1_V)
    except:
        min_T = 0
        min_V = 0

    try:
        v2_T = _select_get_his_value_22(db_con, table, n1, st, ed, startT, endT,'6H', vmax=65,minV=0.1)  # 电池簇最高温度
        v2_V = _select_get_his_value_22(db_con, table, n3, st, ed, startT, endT,'6H', vmax=5,minV=0.1)  # 电池簇最高电压
        value2_T = v2_T['value']  # 最高温度
        value2_V = v2_V['value']  # 最高电压
        try:
            max_T = min(value2_T.remove(0))
        except:
            max_T = min(value2_T)
        try:
            max_V = min(value2_V.remove(0))
        except:
            max_V = min(value2_V)
    except:
        max_T = 0
        max_V = 0
    return max_T, max_V, min_T, min_V, value2_T, value2_V
def frozeDongMuDataDay_(day):
    # 冻结一天的数据（东睦）
    startT = ('%s 00:00:01' % day)
    endT = ('%s 23:59:59' % day)
    st = timeUtils.timeStrToTamp(startT)  # 起始时间绝对秒
    ed = timeUtils.timeStrToTamp(endT)  # 截止时间绝对秒
    dm_table = HisDM('r_cumulant')
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()

    if values_mong:
        values_1_s = {'datainfo': {}}
        values_1_e = {'datainfo': {}}
        values_1_s['datainfo'] = values_mong[0]['datainfo']  # 最小
        values_1_e['datainfo'] = values_mong[-1]['datainfo']  # 最大
        value_s = json.loads(values_1_s['datainfo'])['body']
        value_e = json.loads(values_1_e['datainfo'])['body']
        BDcap_cu_s = []  # 电池总放电量
        BCCap_cu_s = []  # 电池总充电量
        for r in range(7):
            for ii in value_s:
                if ii['device'][:4] == 'PCS%s' % (r + 1):
                    BDcap_cu_s.append(float(ii['BDcap']))  # 电池总放电量最小
                    BCCap_cu_s.append(float(ii['BCCap']))  # 电池总充电量最小
        BDcap_cu_e = []  # 电池总放电量
        BCCap_cu_e = []  # 电池总充电量
        for r in range(7):
            for ii in value_e:
                if ii['device'][:4] == 'PCS%s' % (r + 1):
                    BDcap_cu_e.append(float(ii['BDcap']))  # 电池总放电量最大
                    BCCap_cu_e.append(float(ii['BCCap']))  # 电池总充电量最大
        BDcap_cu = np.array(BDcap_cu_e) - np.array(BDcap_cu_s)  # 电池总放电量
        BCCap_cu = np.array(BCCap_cu_e) - np.array(BCCap_cu_s)  # 电池总充电量

        ratio = 0
        e = 1
        for f in BDcap_cu.tolist():  #
            if BCCap_cu.tolist()[e - 1] == 0:
                ratio = 0
            else:
                ratio = ('%.3f' % ((f / BCCap_cu.tolist()[e - 1]) * 100))
                if float(ratio) > 100:
                    ratio = 100.0
                elif float(ratio) < 0:
                    ratio = 0
            print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % e)
            user_session.merge(FReportBmsDongmu(pcs_name='%s#PCS' % e, cu_name='电池簇%s' % e,en_cu_name='Cluster %s' % e, chag=('%.3f' % BCCap_cu.tolist()[e - 1]), disg=('%.3f' % f), day=day, ratio=ratio, op_ts=timeUtils.getNewTimeStr(), cause=1))
            e += 1
    else:
        for f in range(7):
            print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % (f+1))
            user_session.merge(FReportBmsDongmu(pcs_name='%s#PCS' % (f+1), cu_name='电池簇%s' % (f+1),en_cu_name='Cluster %s' % (f+1), chag=0,disg=0, day=day, ratio=0, op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    dm_table = HisDM('r_measure')
    obj = []
    cu_name_t = model_config.get('peizhi', 'cu_StRan_4')  # name温度极差
    cu_name_v = model_config.get('peizhi', 'cu_SVRan_4')  # name电压极差
    cu_name_s = model_config.get('peizhi', 'cu_SOH_4')  # name
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
    for f in range(7):
        d1 = []
        d2 = []
        d3 = []
        if values_mong:
            for v in values_mong:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][:4] == 'BMS%s' % (f + 1):
                        value1 = float(b[cu_name_t])  #
                        if 0 < value1 <= 65:
                            d1.append(value1)
                        else:
                            d1.append(0)
                        value2 = float(b[cu_name_v])  #
                        if 0 < value2 <= 100:
                            d2.append(value2)
                        else:
                            d2.append(0)
                        value3 = float(b[cu_name_s])  #
                        if 0 < value3 <= 100:
                            d3.append(value3)
                        else:
                            d3.append(100)
        else:
            d1 = [0]
            d2 = [0]
            d3 = [0]
        print ('db:', 'dongmu', '--day:', day, '--name:', 'PCS-%s' % (f + 1))
        page = user_session.query(FReportBmsDongmu).filter(FReportBmsDongmu.pcs_name=='%s#PCS' % (f + 1),FReportBmsDongmu.day== day+' 00:00:00').first()
        page.max_t= max(d1)
        page.max_v= max(d2)
        page.min_soh= max(d3)
    cu_name = model_config.get('peizhi', 'PCS_PF_4')  # name功率因数
    dm_table = HisDM('r_measure')
    list_ = []
    values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
    for f in range(7):
        d = []
        if values_mong:
            for v in values_mong:
                body = json.loads(v['datainfo'])['body']
                for b in body:
                    if b['device'][:4] == 'PCS%s' % (f + 1):
                        value = float(b[cu_name])  #
                        if 0 < value <= 1:
                            d.append(value)
                        else:
                            d.append(0)
        else:
            d = [1,0]
        print ('db:', 'dongmu', '--day:', day, 'PCS--name:', 'PCS-%s' % (f + 1))
        user_session.merge(FReportPcs(pcs_name='PCS-%s' % (f + 1), max_p=max(d), min_p=min(d), day=day, station='dongmu',op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    user_session.close()
    dongmu_session.close()
    print ('SUCCESS')
def _select_get_his_value_22(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65, minV=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])
    l = timeUtils.getBeforeHouseStr()[:14] + "00:00"  # 获取上一小时时间 格式 换算整点值
    t1 = timeUtils.timeSeconds(l, endTime)  # 计算截止时间和前一小时时间的差值

    if t1 > 0:  # 截止时间大
        # 查询上一小时表里的数据
        try:
            HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
            values = db_con.query(HisTable_l.value, HisTable_l.dts_s).filter(HisTable_l.name == name,
                                                                             HisTable_l.dts_s.between(st, ed),
                                                                             HisTable_l.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])
    n = timeUtils.getNewTimeStr()[:14] + "00:00"  # 取整点值
    t2 = timeUtils.timeSeconds(n, endTime)  # 计算截止时间和当前小时的差值

    if t2 > 0:  # 截止时间大于当前时间
        # 查询当前小时表里的数据
        HisTable_n = HisACDMS('r_measure%s' % timeUtils.getNowHouse())  #
        try:
            values = db_con.query(HisTable_n.value, HisTable_n.dts_s).filter(HisTable_n.name == name,
                                                                             HisTable_n.dts_s.between(st, ed),
                                                                             HisTable_n.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_n.dts_s.asc()).all()  # 当前小时表数据
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

    tl = timeUtils.timeSeconds(l, startTime)  # 计算起始时间和前一小时时间的差值

    if tl > 0:  # 起始时间大
        try:
            value = db_con.query(HisTable_l.value).filter(HisTable_l.name == name, HisTable_l.dts_s <= st,
                                                          HisTable_l.value.between(minV, vmax)).order_by(
                HisTable_l.dts_s.desc()).first()  # 查询前一小时
        except:
            value = []
    else:
        nt = data['time'][0] if data['time'] else startTime
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name,
                                                      HisTable_m.dts_s <= timeUtils.timeStrToTamp(nt[:19]),
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月

    if not value and not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()

    return complete_data(data, jiange)
def _select_get_his_value_222(db_con, table, name, st, ed, startTime, endTime, vmax=65, minV=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(minV, vmax)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])

    l = timeUtils.getBeforeHouseStr()[:14] + "00:00"  # 获取上一小时时间 格式 换算整点值
    t1 = timeUtils.timeSeconds(l, endTime)  # 计算截止时间和前一小时时间的差值

    if t1 > 0:  # 截止时间大
        # 查询上一小时表里的数据
        HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
        try:
            values = db_con.query(HisTable_l.value, HisTable_l.dts_s).filter(HisTable_l.name == name,
                                                                             HisTable_l.dts_s.between(st, ed),
                                                                             HisTable_l.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])
    n = timeUtils.getNewTimeStr()[:14] + "00:00"  # 取整点值
    t2 = timeUtils.timeSeconds(n, endTime)  # 计算截止时间和当前小时的差值

    if t2 > 0:  # 截止时间大于当前时间
        # 查询当前小时表里的数据
        HisTable_n = HisACDMS('r_measure%s' % timeUtils.getNowHouse())  #
        try:
            values = db_con.query(HisTable_n.value, HisTable_n.dts_s).filter(HisTable_n.name == name,
                                                                             HisTable_n.dts_s.between(st, ed),
                                                                             HisTable_n.value.between(minV,
                                                                                                      vmax)).order_by(
                HisTable_n.dts_s.asc()).all()  # 当前小时表数据
        except:
            values = []
        for val in values:
            data['time'].append(timeUtils.ssTtimes(val[1]))
            data['value'].append(val[0])

    tl = timeUtils.timeSeconds(l, startTime)  # 计算起始时间和前一小时时间的差值

    if tl > 0:  # 起始时间大
        try:
            value = db_con.query(HisTable_l.value).filter(HisTable_l.name == name, HisTable_l.dts_s <= st,
                                                          HisTable_l.value.between(minV, vmax)).order_by(
                HisTable_l.dts_s.desc()).first()  # 查询前一小时
        except:
            value = []

    else:
        nt = data['time'][0] if data['time'] else startTime
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name,
                                                      HisTable_m.dts_s <= timeUtils.timeStrToTamp(nt[:19]),
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月

    if not value and not data['time']:  # 都没值
        value = db_con.query(HisTable_m.value).filter(HisTable_m.name == name, HisTable_m.dts_s <= st,
                                                      HisTable_m.value.between(minV, vmax)).order_by(
            HisTable_m.dts_s.desc()).first()  # 查询当前月
        # logging.info('name:%s  SELECT BEFORE'%name)
    # print '初始数据-------',data
    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        if value:
            data['value'].insert(0, value[0])
        else:
            data['value'].insert(0, data['value'][0])
    elif value:  # 时间为空
        data['time'].insert(0, startTime)
        data['value'].insert(0, value[0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])

    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()

    return data
def _select_get_his_value_dm(db_con, table, name, st, ed, max_=65, min_=0.1):  # 查询添加最小值
    '''获取历史数据'''
    db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                     HisTable_m.dts_s.between(st, ed),
                                                                     HisTable_m.value.between(min_, max_)).order_by(
        HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])
    return data
def frozeDayaByTime(startTime,endTime):
    days = timeUtils.dateToDataList(startTime,endTime)  # 计算间隔的天
    for day in days:
        calculation(day)


# def calculation(day='2023-11-04'):
def calculation(day=None):
    # days = timeUtils.dateToDataList('2024-04-22', '2024-04-23')  # 计算间隔的天
    # for d in days:
    #     for i in station:
    #         if i =='dongmu':
    #             frozeDongMuDataDay_(d[:10])
    #         else:
    #             frozeDataDay(d[:10],i)
    if not day:
        day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
        for i in station:
            if i =='dongmu':
                frozeDongMuDataDay_(day)
            else:
                frozeDataDay(day,i)
    else:
        for i in station:
            if i == 'dongmu':
                frozeDongMuDataDay_(day)
            else:
                frozeDataDay(day, i)

def RunClearFileAndData():
    scheduler = BlockingScheduler()
    # scheduler.add_job(calculation, 'interval', seconds=10)  # 10秒获取一次数据
    # scheduler.add_job(calculation, 'interval', seconds=60*2)  # 每 15分钟执行一次
    # scheduler.add_job(calculation, 'cron', hour=10,minute=1,misfire_grace_time=600)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.add_job(calculation, 'cron', hour=3,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.start()

if __name__ == '__main__':
    try:
        opts, args = getopt.getopt(sys.argv[1:],"h",["help","froze","start=","end="])
        cmd = None
        start = None
        end = None
        for opt,arg in opts:
            if opt=="--froze":
                cmd = "froze"
            elif opt=="--start":
                start = arg
            elif opt=="--end":
                end = arg
            else:
                print ('''%s 数据冻结工具
                选项
                    -h|--help 查看帮助
                    --froze 冻结数据
                    --start 起始时刻（含）。yyyy-mm-dd
                    --end 结束时刻（含）。yyyy-mm-dd

                ''' )% sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            RunClearFileAndData()
            # calculation()
        elif cmd=="froze":
            if not start:
                print ("请指定开始时刻")
                quit()
            if not end:
                print ("请指定结束时刻")
                quit()
            frozeDayaByTime(start,end)
            print ('SUCCESS')

    except Exception as e:
        print (e)