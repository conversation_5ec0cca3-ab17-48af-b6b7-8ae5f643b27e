package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 项目包创建DTO
 */
@Data
@ApiModel("项目包创建DTO")
public class ProjectPackCreateDTO {

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "项目包数据(JSON字符串)", required = true)
    @NotBlank(message = "项目包数据不能为空")
    private String data;

    @ApiModelProperty(value = "项目包名称", required = true)
    @NotBlank(message = "项目包名称不能为空")
    private String name;
}
