#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-05-14 11:59:13
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\zh_sub.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-05-22 15:15:21


import json
import logging
from Tools.Utils.num_utils import Translate_cls, user_email
from Tools.DB.redis_con import r_real
from Tools.Utils.send_mail import sendMail_
# from Tools.DecisionDB.ele_base import user_session
from Tools.DB.mysql_user import user_session
# 英文转中文
t_cls = Translate_cls(1)

def sub():
    """
    订阅
    :return:
    """
    # 获取英译中翻译数据订阅
    pub = r_real.pubsub()
    pub.subscribe('zh_translate_pub')
    # 监听
    msg_stream = pub.listen()
    for msg in msg_stream:
        if msg["type"] == "message":
            data = json.loads(msg['data'])
            id = data.get('id')
            table = data.get('table')
            info = data.get('update_data')
            sql = ''
            for k, v in info.items():
                sql += f"{k} = '{json.dumps(t_cls.str_chinese(v))}',"
            if sql:
                sql = sql[:-1]
                u_sql = f"UPDATE `dm_user`.`{table}` SET {sql} WHERE `id` = {id};"
                try:
                    user_session.execute(u_sql)
                    user_session.commit()
                    print('执行完成')
                except Exception as e:
                    logging.error(f'英译中翻译写入数据失败：翻译表：{table}；数据ID：{id}, 错误信息：{e}')
                    sendMail_(f"您的异步翻译异常，请关注 数据表：{table}；数据ID：{id}, 错误信息：{e}", "异步翻译异常消息通知：英译中", "山海系统", "XXX", user_email)
        elif msg["type"] == "subscribe":
            print(str(msg["channel"]), "订阅成功")

if __name__ == '__main__':
    sub()