#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-27 09:45:14
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\station.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-29 14:34:35


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class StationS(user_Base):
    u'电站运行状态'
    __tablename__ = "t_station_state"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(255), nullable=False, comment=u"具体名称,多个变量计算时以#号分割")
    descr = Column(VARCHAR(255), nullable=True,comment=u"描述")
    type_name = Column(VARCHAR(255), nullable=False,comment=u"数据类型,status,measure,discrete")
    station_name = Column(VARCHAR(255), nullable=False,comment=u"电站名称")
    info = Column(CHAR(1), nullable=False,comment=u"1是状态，2是温度")
    type = Column(CHAR(1), nullable=False, comment=u"1是运行状态，2是故障，3是报警")


    @classmethod
    # def init(cls):
    #     user_Base.metadata.create_all()
    #     now = timeUtils.getNewTimeStr()
    #     user_session.merge(Station(id=1,name='halun',descr='哈伦6MW/3MWh储能电站',op_ts=now,index=3));
    #     user_session.merge(Station(id=2,name='taicang',descr='太仓9MW/4.5MWh储能电站',op_ts=now,index=2));
    #     user_session.merge(Station(id=3,name='binhai',descr='滨海18MW/9MWh储能电站',op_ts=now,index=1));
    #     user_session.merge(Station(id=4,name='ygzhen',descr='永臻5MW/18MWh储能电站',op_ts=now,index=4));
    #
    #     user_session.commit()
    #     user_session.close()
        
    def __repr__(self):
        bean = "{'id':%s,'name':'%s','descr':%s,'station_name':'%s','info':'%s','type':'%s'}" % (self.id,self.name,self.descr,self.station_name,self.info,self.type)
        return bean.replace("None",'')
        
    def deleteStation(self,id):
        try:
            user_session.query(StationS).filter(StationS.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False