import datetime
import time
import json
import requests
import pandas as pd

from apis.user import models
from common.database_pools import dwd_tables, dwd_db_tool


# row_tittle = ["时间","最高单体电压", "最低单体电压", "最高单体温度","最低单体温度", ]
# for i in range(1,261):
#     row_tittle.append(f"单体电压 {i}")
# for i in range(1, 141):
#     row_tittle.append(f"单体温度 {i}")
# row_tittle = ["时间", "并网点功率", "输出总有功功率"]


def get_station_accu_data(station, start_time, end_time):
    """
    获取电站的某一天的累计充放电量
    """""
    table_name = dwd_tables['cumulant']['bms']
    charge_list = []
    discharge_list = []
    select_sql = (
        f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
        f" time DESC limit 1")
    temp_list = []
    units = station.unit_set.filter(is_delete=0).all()
    for unit in units:
        result = dwd_db_tool.select_one(select_sql, *(station.english_name, unit.bms, start_time, end_time))
        if result:
            temp_dict = {"device": unit.bms, "station": station.station_name}
            # time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
            if station.english_name == 'NBLS001':  # 单独处理
                discharge_list.append(result.get('CuDis'.lower(), 0))
                charge_list.append(result.get('CuCha'.lower(), 0))
                temp_dict["accu_charge"] = round(abs(result.get('CuCha'.lower(), 0)), 2)
                temp_dict["accu_discharge"] = round(abs(result.get('CuDis'.lower(), 0)), 2)
            else:
                discharge_list.append(result.get('PAE'.lower(), 0))
                charge_list.append(result.get('NAE'.lower(), 0))
                temp_dict["accu_charge"] = round(abs(result.get('NAE'.lower(), 0)), 2)
                temp_dict["accu_discharge"] = round(abs(result.get('PAE'.lower(), 0)), 2)
            temp_list.append(temp_dict)

    if len(charge_list) != 0 or len(discharge_list) != 0:
        temp_list.append({"accu_charge": round(abs(sum(charge_list)), 2), "accu_discharge": round(abs(sum(discharge_list)), 2), "station": station.station_name, "device": "汇总"})
    return temp_list


class GetHttpHistory:
    def __init__(self, start_time, end_time, energy_col_dic):
        self.start_time = start_time
        self.end_time = end_time
        self.energy_col_dic = energy_col_dic

    def get_energy(self, station_app, station_english_name, index):
        url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
        post_json = {
            "startTime": str(self.start_time),
            "endTime": str(self.end_time),
            "datatype": "cumulant",
            "app": station_app,
            "order": "DESC",
            "pageSize": 50000,
            "station": station_english_name,
            "startPage": 1,
        }

        response = requests.post(url=url, json=post_json)
        charge_list = []
        discharge_list = []
        if response.json()["datas"]:
            data = json.loads(response.json()["datas"]["list"][0]["dataInfo"])["body"]
           
            for i in range(1, index + 1, 2):
                obj = data[i]
                if 'PAE' in obj.keys():
                    self.energy_col_dic["正向有功电能/kWh"].append(abs(int(float(obj["PAE"]))))
                    charge_list.append(abs(int(float(obj["PAE"]))))
                else:
                    self.energy_col_dic["正向有功电能/kWh"].append(0)
                    charge_list.append(0)
                if "NAE" in obj.keys():
                    self.energy_col_dic["负向有功电能/kWh"].append(abs(int(float(obj["NAE"]))))
                    discharge_list.append(abs(int(float(obj["NAE"]))))
                else:
                    self.energy_col_dic["负向有功电能/kWh"].append(0)
                    discharge_list.append(0)
            if index > 1:
                self.energy_col_dic["正向有功电能/kWh"].append(sum(charge_list))
                self.energy_col_dic["负向有功电能/kWh"].append(sum(discharge_list))
            

    def alarm_fault(self, station_app, station_english_name, index):
        alarm_setting = {"0": "无告警", "1": "告警"}
        fault_setting = {"0": "无故障", "1": "故障"}
        url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
        post_json = {
            "startTime": str(self.start_time),
            "endTime": str(self.end_time),
            "datatype": "status",
            "app": station_app,
            "order": "ASC",
            "pageSize": 50000,
            "station": station_english_name,
            "startPage": 1,
        }

        response = requests.post(url=url, json=post_json)
        if index == 1:
            return_dic = {
                "时间": [],
                "BMS总告警": [],
                "BMS总故障": [],
                "PCS故障状态": [],
                "PCS警告状态": [],
            }
            for i in range(len(response.json()["datas"]["list"])):
                time = datetime.datetime.fromtimestamp(int(json.loads(response.json()["datas"]["list"][i]["dataInfo"])["utime"]))
                return_dic["时间"].append(time)
                data = json.loads(response.json()["datas"]["list"][i]["dataInfo"])["body"]
                BMS = data[1]
                return_dic["BMS总告警"].append(alarm_setting[BMS.get("GAlarm")])
                return_dic["BMS总故障"].append(
                    fault_setting[
                        BMS.get(
                            "GFault",
                        )
                    ]
                )
                PCS = data[0]
                return_dic["PCS故障状态"].append(
                    fault_setting[
                        PCS.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS警告状态"].append(
                    alarm_setting[
                        PCS.get(
                            "alarm",
                        )
                    ]
                )
            return return_dic
        if index == 3:
            return_dic = {
                "时间": [],
                "BMS1总告警": [],
                "BMS2总告警": [],
                "BMS1总故障": [],
                "BMS2总故障": [],
                "PCS1故障状态": [],
                "PCS2故障状态": [],
                "PCS1警告状态": [],
                "PCS2警告状态": [],
            }
            for i in range(len(response.json()["datas"]["list"])):
                time = datetime.datetime.fromtimestamp(int(json.loads(response.json()["datas"]["list"][i]["dataInfo"])["utime"]))
                return_dic["时间"].append(time)
                data = json.loads(response.json()["datas"]["list"][i]["dataInfo"])["body"]
                BMS1 = data[1]
                BMS2 = data[3]
                return_dic["BMS1总告警"].append(alarm_setting[BMS1.get("GAlarm")])
                return_dic["BMS2总告警"].append(alarm_setting[BMS2.get("GAlarm")])
                return_dic["BMS1总故障"].append(
                    fault_setting[
                        BMS1.get(
                            "GFault",
                        )
                    ]
                )
                return_dic["BMS2总故障"].append(
                    fault_setting[
                        BMS2.get(
                            "GFault",
                        )
                    ]
                )
                PCS1 = data[0]
                PCS2 = data[2]
                return_dic["PCS1故障状态"].append(
                    fault_setting[
                        PCS1.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS2故障状态"].append(
                    fault_setting[
                        PCS2.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS1警告状态"].append(
                    alarm_setting[
                        PCS1.get(
                            "alarm",
                        )
                    ]
                )
                return_dic["PCS2警告状态"].append(
                    alarm_setting[
                        PCS2.get(
                            "alarm",
                        )
                    ]
                )
            return return_dic
        if index == 5:
            return_dic = {
                "时间": [],
                "BMS1总告警": [],
                "BMS2总告警": [],
                "BMS3总告警": [],
                "BMS1总故障": [],
                "BMS2总故障": [],
                "BMS3总故障": [],
                "PCS1故障状态": [],
                "PCS2故障状态": [],
                "PCS3故障状态": [],
                "PCS1警告状态": [],
                "PCS2警告状态": [],
                "PCS3警告状态": [],
            }
            for i in range(len(response.json()["datas"]["list"])):
                time = datetime.datetime.fromtimestamp(int(json.loads(response.json()["datas"]["list"][i]["dataInfo"])["utime"]))
                return_dic["时间"].append(time)
                data = json.loads(response.json()["datas"]["list"][i]["dataInfo"])["body"]
                BMS1 = data[1]
                BMS2 = data[3]
                BMS3 = data[5]
                return_dic["BMS1总告警"].append(alarm_setting[BMS1.get("GAlarm")])
                return_dic["BMS2总告警"].append(alarm_setting[BMS2.get("GAlarm")])
                return_dic["BMS3总告警"].append(alarm_setting[BMS3.get("GAlarm")])
                return_dic["BMS1总故障"].append(
                    fault_setting[
                        BMS1.get(
                            "GFault",
                        )
                    ]
                )
                return_dic["BMS2总故障"].append(
                    fault_setting[
                        BMS2.get(
                            "GFault",
                        )
                    ]
                )
                return_dic["BMS3总故障"].append(
                    fault_setting[
                        BMS3.get(
                            "GFault",
                        )
                    ]
                )
                PCS1 = data[0]
                PCS2 = data[2]
                PCS3 = data[4]
                return_dic["PCS1故障状态"].append(
                    fault_setting[
                        PCS1.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS2故障状态"].append(
                    fault_setting[
                        PCS2.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS3故障状态"].append(
                    fault_setting[
                        PCS3.get(
                            "Fault",
                        )
                    ]
                )
                return_dic["PCS1警告状态"].append(
                    alarm_setting[
                        PCS1.get(
                            "alarm",
                        )
                    ]
                )
                return_dic["PCS2警告状态"].append(
                    alarm_setting[
                        PCS2.get(
                            "alarm",
                        )
                    ]
                )
                return_dic["PCS3警告状态"].append(
                    alarm_setting[
                        PCS3.get(
                            "alarm",
                        )
                    ]
                )
            return return_dic


if __name__ == "__main__":
    station_detail_list = (
        ("NBLS001", "TN001", 1, "宁波朗盛001"),
        ("NBLS002", "TN001", 1, "宁波朗盛002"),
        ("LJQC001", "TL001", 1, "中伏能源001"),
        ("GLTY201", "TC201", 5, "光隆天畅001"),
        ("QXYJ201", "YJ201", 5, "千能逸嘉201"),
        ("QNKX101", "KX101", 3, "千能康信101"),
        ("QNKX001", "KX001", 1, "千能康信001"),
    )
    input_time = input("请输入查询时间: (时间格式为:2023-07-01)")
    # toady = datetime.date.today()
    toady = datetime.datetime.strptime(input_time, "%Y-%m-%d")
    toady_tuple = toady.timetuple()
    today_time_stamp = int(time.mktime(toady_tuple))  # 今日凌晨
    yesterday = toady - datetime.timedelta(days=1)
    yesterday_tuple = yesterday.timetuple()
    yesterday_time_stamp = int(time.mktime(yesterday_tuple))  # 前一天凌晨
    energy_col_dic = {
        "  ": [
            "宁波朗盛001",
            "宁波朗盛002",
            "中伏能源001",
            "光隆天畅001",
            "光隆天畅001",
            "光隆天畅001",
            "光隆天畅001",
            "千能逸嘉201",
            "千能逸嘉201",
            "千能逸嘉201",
            "千能逸嘉201",
            "千能康信101",
            "千能康信101",
            "千能康信101",
            "千能康信001",
        ],
        "设备": [
            "BMS",
            "BMS",
            "BMS",
            "BMS1",
            "BMS2",
            "BMS3",
            "汇总",
            "BMS1",
            "BMS2",
            "BMS3",
            "汇总",
            "BMS1",
            "BMS2",
            "汇总",
            "BMS",
        ],
        "正向有功电能/kWh": [],
        "负向有功电能/kWh": [],
    }
    http = GetHttpHistory(yesterday_time_stamp, today_time_stamp)
    for station in station_detail_list:
        http.get_energy(station[1], station[0], station[2])
    writer = pd.ExcelWriter(
        f"{toady.strftime('%Y-%m-%d')}.xlsx",
    )
    pd.DataFrame(energy_col_dic).to_excel(writer, sheet_name="累计充放电量", index=False)

    # 故障
    for station in station_detail_list:
        data_dic = http.alarm_fault(station[1], station[0], station[2])
        pd.DataFrame(data_dic).to_excel(writer, sheet_name=f"{station[3]}", index=False)
    writer.close()
