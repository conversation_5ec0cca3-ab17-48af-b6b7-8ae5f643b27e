import json
from django.conf import settings
from paho.mqtt import client as mqtt_client
import paho.mqtt.publish as publish

broker = '47.92.121.83'  # mqtt代理服务器地址
port = 1883
keepalive = 60  # 与代理通信之间允许的最长时间段（以秒为单位）

user = "mqtt_user1"
pwd = "123456&"
topic = "req/database/realtime/SAMPLE1/TN002"
msg = {"time": "1684979456", "token": "L5yoVFYuLLXDhGcTwVkVng==", "device": "EMS",
       "body": [{"EAEn": "1", "type": "parameter"}]}

auth = {"username": "mqtt_user1", "password": "123456&"}


# def connect_mqtt():
#     '''连接mqtt代理服务器'''
#
#     def on_connect(client, userdata, flags, rc):
#         '''连接回调函数'''
#         # 响应状态码为0表示连接成功
#         if rc == 0:
#             print("Connected to MQTT OK!")
#         else:
#             print("Failed to connect, return code %d\n", rc)
#
#     # 连接mqtt代理服务器，并获取连接引用
#     client = mqtt_client.Client()
#     client.username_pw_set(username=user, password=pwd)
#     client.on_connect = on_connect
#     client.connect(broker, port, keepalive)
#     client.loop_start()
#     return client
#
#
# def publish(client, message: dict, topic: str):
#     '''发布消息'''
#     result = client.publish(topic, json.dumps(message))
#     status = result[0]
#     if status == 0:
#         print(f"Send `{message}` to topic `{topic}`")
#     else:
#         print(f"Failed to send message to topic {topic}")
#
#
# def run():
#     # topic =   # 消息主题
#     # '''运行发布者'''
#     mes = {"time": "1684979456", "token": "L5yoVFYuLLXDhGcTwVkVng==", "device": "EMS",
#            "body": [{"EAEn": "1", "type": "parameter"}]}
#     topic = "req/database/realtime/SAMPLE1/TN002"
#     client = connect_mqtt()
#     publish(client, topic=topic,
#             message=mes)


#
#
#
# def connect_mqtt():
#     '''连接mqtt代理服务器'''
#     def on_connect(client, userdata, flags, rc):
#         '''连接回调函数'''
#         # 响应状态码为0表示连接成功
#         if rc == 0:
#             print("Connected to MQTT OK!")
#         else:
#             print("Failed to connect, return code %d\n", rc)
#     # 连接mqtt代理服务器，并获取连接引用
#     client = mqtt_client.Client()
#     client.username_pw_set(username=user, password=pwd)
#     client.on_connect = on_connect
#     client.connect(broker, port, keepalive)
#     return client
#
# def publish(client):
#     '''发布消息'''
#     while True:
#         '''每隔4秒发布一次服务器信息'''
#         time.sleep(4)
#         msg = "msg"
#         result = publish.single(topic, msg)
#         status = result[0]
#         if status == 0:
#             print(f"Send `{msg}` to topic `{topic}`")
#         else:
#             print(f"Failed to send message to topic {topic}")
#
# def run():
#     '''运行发布者'''
#     client = connect_mqtt()
#     # 运行一个线程来自动调用loop()处理网络事件, 非阻塞
#     # client.loop_start()
#     # publish(client)
class Mqtt:
    def __init__(self, username, password, hostname, port=1883):
        self.hostname = hostname
        self.port = port
        self.auther = {"username": username, "password": password}

    def publish(self, topic: str, message: dict):
        try:
            publish.single(topic=topic, payload=json.dumps(message), hostname=self.hostname, port=self.port,
                           auth=self.auther)
            return 1
        except Exception as e:

            return e


# if __name__ == '__main__':
#     mqtt_cl = Mqtt(username=user, password=pwd, hostname=broker)
#     d = mqtt_cl.publish(topic=topic, message=msg)
#     print(d)
