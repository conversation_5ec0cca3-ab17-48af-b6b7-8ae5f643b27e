#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-29 09:04:43
#@FilePath     : \RHBESS_Service\Tools\DB\mongodb_con.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-06 12:44:24

import os,pymongo
from mongoengine import Document,StringField,IntField,FloatField,BooleanField,DateTimeField,ListField,DictField,ReferenceField,\
    SequenceField,connect
# from sqlalchemy import create_engine
# from sqlalchemy.orm import sessionmaker, relationship
# from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config

MONGODB_HOSTNAME = model_config.get('mysql', "MONGO_HOSTNAME")
MONGODB_PORT = int(model_config.get('mysql', "MONGO_PORT"))
MONGODB_DATABASE = model_config.get('mysql', "MONGO_DATABASE")
MONGODB_USERNAME = model_config.get('mysql', "MONGO_USERNAME")
MONGODB_PASSWORD = model_config.get('mysql', "MONGO_PASSWORD")

mongodb_client = pymongo.MongoClient( host=MONGODB_HOSTNAME,
                              port=MONGODB_PORT,
                              username=MONGODB_USERNAME,
                              password=MONGODB_PASSWORD,
                              connect=False
                             )

mongo_db = mongodb_client[MONGODB_DATABASE]
#  太仓现场为智锂使用
TAICANG_ZHILI_MONGO_HOSTNAME = model_config.get('mysql', "TAICANG_ZHILI_MONGO_HOSTNAME")
TAICANG_ZHILI_MONGO_PORT = int(model_config.get('mysql', "TAICANG_ZHILI_MONGO_PORT"))
TAICANG_ZHILI_MONGO_DATABASE = model_config.get('mysql', "TAICANG_ZHILI_MONGO_DATABASE")
TAICANG_ZHILI_MONGO_USERNAME = model_config.get('mysql', "TAICANG_ZHILI_MONGO_USERNAME")
TAICANG_ZHILI_MONGO_PASSWORD = model_config.get('mysql', "TAICANG_ZHILI_MONGO_PASSWORD")

zhili_mongodb_client = pymongo.MongoClient( host=TAICANG_ZHILI_MONGO_HOSTNAME,
                              port=TAICANG_ZHILI_MONGO_PORT,
                              username=TAICANG_ZHILI_MONGO_USERNAME,
                              password=TAICANG_ZHILI_MONGO_PASSWORD,
                              connect=False
                             )

zhili_mongo_db = zhili_mongodb_client[TAICANG_ZHILI_MONGO_DATABASE]
# 滨海项目
BINHAI_MONGODB_HOSTNAME = model_config.get('mysql', "BINHAI_MONGO_HOSTNAME")
BINHAI_MONGODB_PORT = int(model_config.get('mysql', "BINHAI_MONGO_PORT"))
BINHAI_MONGODB_DATABASE = model_config.get('mysql', "BINHAI_MONGO_DATABASE")
BINHAI_MONGODB_USERNAME = model_config.get('mysql', "BINHAI_MONGO_USERNAME")
BINHAI_MONGODB_PASSWORD = model_config.get('mysql', "BINHAI_MONGO_PASSWORD")

binhai_mongodb_client = pymongo.MongoClient( host=BINHAI_MONGODB_HOSTNAME,
                              port=BINHAI_MONGODB_PORT,
                              username=BINHAI_MONGODB_USERNAME,
                              password=BINHAI_MONGODB_PASSWORD,
                              connect=False
                             )

binhai_mongo_db = binhai_mongodb_client[BINHAI_MONGODB_DATABASE]

# 永臻项目
YGZHEN_MONGODB_HOSTNAME = model_config.get('mysql', "YGZHEN_MONGO_HOSTNAME")
YGZHEN_MONGODB_PORT = int(model_config.get('mysql', "YGZHEN_MONGO_PORT"))
YGZHEN_MONGODB_DATABASE = model_config.get('mysql', "YGZHEN_MONGO_DATABASE")
YGZHEN_MONGODB_USERNAME = model_config.get('mysql', "YGZHEN_MONGO_USERNAME")
YGZHEN_MONGODB_PASSWORD = model_config.get('mysql', "YGZHEN_MONGO_PASSWORD")

ygzhen_mongodb_client = pymongo.MongoClient( host=YGZHEN_MONGODB_HOSTNAME,
                              port=YGZHEN_MONGODB_PORT,
                              username=YGZHEN_MONGODB_USERNAME,
                              password=YGZHEN_MONGODB_PASSWORD,
                              connect=False
                             )

ygzhen_mongo_db = ygzhen_mongodb_client[YGZHEN_MONGODB_DATABASE]

# 滨海现场1项目
BINHAI1_ZHILI_MONGO_HOSTNAME = model_config.get('mysql', "BINHAI1_ZHILI_MONGO_HOSTNAME")
BINHAI1_ZHILI_MONGO_PORT = int(model_config.get('mysql', "BINHAI1_ZHILI_MONGO_PORT"))
BINHAI1_ZHILI_MONGO_DATABASE = model_config.get('mysql', "BINHAI1_ZHILI_MONGO_DATABASE")
BINHAI1_ZHILI_MONGO_USERNAME = model_config.get('mysql', "BINHAI1_ZHILI_MONGO_USERNAME")
BINHAI1_ZHILI_MONGO_PASSWORD = model_config.get('mysql', "BINHAI1_ZHILI_MONGO_PASSWORD")

binhai1_mongodb_client = pymongo.MongoClient( host=BINHAI1_ZHILI_MONGO_HOSTNAME,
                              port=BINHAI1_ZHILI_MONGO_PORT,
                              username=BINHAI1_ZHILI_MONGO_USERNAME,
                              password=BINHAI1_ZHILI_MONGO_PASSWORD,
                              connect=False
                             )
binhai1_mongo_db = binhai1_mongodb_client[BINHAI1_ZHILI_MONGO_DATABASE]

# 滨海现场2项目
BINHAI2_ZHILI_MONGO_HOSTNAME = model_config.get('mysql', "BINHAI2_ZHILI_MONGO_HOSTNAME")
BINHAI2_ZHILI_MONGO_PORT = int(model_config.get('mysql', "BINHAI2_ZHILI_MONGO_PORT"))
BINHAI2_ZHILI_MONGO_DATABASE = model_config.get('mysql', "BINHAI2_ZHILI_MONGO_DATABASE")
BINHAI2_ZHILI_MONGO_USERNAME = model_config.get('mysql', "BINHAI2_ZHILI_MONGO_USERNAME")
BINHAI2_ZHILI_MONGO_PASSWORD = model_config.get('mysql', "BINHAI2_ZHILI_MONGO_PASSWORD")

binhai2_mongodb_client = pymongo.MongoClient( host=BINHAI2_ZHILI_MONGO_HOSTNAME,
                              port=BINHAI2_ZHILI_MONGO_PORT,
                              username=BINHAI2_ZHILI_MONGO_USERNAME,
                              password=BINHAI2_ZHILI_MONGO_PASSWORD,
                              connect=False
                             )
binhai2_mongo_db = binhai2_mongodb_client[BINHAI2_ZHILI_MONGO_DATABASE]
# 东睦站
DONGMU_MONGODB_HOSTNAME = model_config.get('mysql', "DONGMU_MONGO_HOSTNAME")
DONGMU_MONGODB_PORT = int(model_config.get('mysql', "DONGMU_MONGO_PORT"))
DONGMU_MONGODB_DATABASE = model_config.get('mysql', "DONGMU_MONGO_DATABASE")
DONGMU_MONGODB_USERNAME = model_config.get('mysql', "DONGMU_MONGO_USERNAME")
DONGMU_MONGODB_PASSWORD = model_config.get('mysql', "DONGMU_MONGO_PASSWORD")

dongmu_mongodb_client = pymongo.MongoClient( host=DONGMU_MONGODB_HOSTNAME,
                              port=DONGMU_MONGODB_PORT,
                              username=DONGMU_MONGODB_USERNAME,
                              password=DONGMU_MONGODB_PASSWORD,
                              connect=False
                             )

dongmu_mongo_db = dongmu_mongodb_client[DONGMU_MONGODB_DATABASE]




