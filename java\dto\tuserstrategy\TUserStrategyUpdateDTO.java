package com.robestec.analysis.dto.tuserstrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户策略更新DTO
 */
@Data
@ApiModel("用户策略更新DTO")
public class TUserStrategyUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "策略名称", required = true)
    @NotBlank(message = "策略名称不能为空")
    private String name;

    @ApiModelProperty(value = "英文策略名称")
    private String enName;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "是否删除: 1-删除, 0-不删除")
    private Integer isDelete;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
