#!/usr/bin/env python
# coding=utf-8
#@Information: 永臻接入英臻计量表
#<AUTHOR> WYJ
#@Date         : 2022-11-07 11:01:27
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\Utils\alarm_save copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-07 11:01:27
import os
import requests
import json,time
from Tools.Utils.time_utils import timeUtils
from Tools.DB.ygzhen_his import ygzhen1_session
from Application.Models.His.r_ACDMS import HisACDMS_YG
from Tools.DB.redis_con import r_real
import random
from apscheduler.schedulers.blocking import BlockingScheduler
# print  timeUtils.timeDiff('2022-11-07 10:00:00','2022-12-10 12:09:34')
# print timeUtils.betweenDayNum('2022-11-07 10:00:00','2022-12-10 12:09:34')

token = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgrwm5KtLhu85JQ6Y9JyQJxYgquQ9knP3_GzSG-1-BfW37KHDD1H0MYGCOw1tDIFo6aa0KMGOKi6ro8Ho_M9428VE4PW5V39JJUJGx8Bm1Bb7B_GLKhy2oAYNP-tngsmDw7VFLGFNTjQheCUKz12Yzr4IB80YYlqYnPsYvwzk2rGlFasCscuW8-tSFGTzsN-ohDwyATb2M8DHlQqPaT69WPstSPuYIv1sp1l9Sw1KEEOhDscJ8bKBQSGExmB6NWrMalCsCgKbKXJ8G_1PTOuMA_ekwe5EkPCLXtiFW-LNYcOjQUx3oe-fThEIpI8rYE13VOw1laXi2Mp2ak4NykYxg"
def get_token():
    '''
    获取token接口
    返回的参数为字符型的字典格式
    '''
    global token
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/account/v1.0/token",
            params={
                # "appId": "****************",
                "appId": "****************",
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "User-Agent":"Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "countryCode": "86",
                "orgId": "140910",
                "mobile": "***********",
                "password": "af51618704e8ae60a85ab5d71434ded3fbe68081e1fa437ca9ade839260504b7",
                # "appSecret": "b05ae1826629374a229d2963775955cb"
                "appSecret": "3b5ba8763a12d0eae41e355b9a440e88"
            })
        )
        print('Response token HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response token HTTP Response Body: {content}'.format(
            content=response.content))
       
        token=json.loads(response.content)['access_token']
        
    except requests.exceptions.RequestException:
        print('HTTP Request failed')


def get_real_data():
    global token
    print ('time:',timeUtils.getNewTimeStr())
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/device/v1.0/currentData",
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s"%token,
                "User-Agent":"Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "deviceSn": "001565793841"
            })
        )
        # print ('旧token',token)
        # print('Response HTTP Response Body: {content}'.format(content=response.content))
        re = json.loads(response.content)
        code = re['code']
        if code == '2101017' or code == '2101019':  # token验证失败
            get_token()
            print (u'新token:',token)
            get_real_data()
        elif re['success']:  # 成功取得数据
            data_save(re)
    except requests.exceptions.RequestException:
        print('HTTP Request failed')


def data_save(data):
    ots = timeUtils.getNewTimeStr()
    m = ots[0:7].replace('-','')
    table = 'r_measure%s'%m
    base_name = 'tfStygzhen1.EMS.MET.'
    HisTable = HisACDMS_YG(table)
    # nowt = str(time.time()).split('.')
    # dts_s = int(nowt[0])
    # dts_ms = int(nowt[1])
    if data['success']:
        datalist = data['dataList']
        dts_s = data['collectionTime']
        for obj in datalist:
            k = obj['key']
            if k != 'SN1' or k != 'METERrate1' or k != 'METER_PTC_TYP1':  #过滤掉SN号，电表倍率，电表协议类型
                name = '%s%s'%(base_name,obj['key'])
                print ('name:',name,'----value:',obj['value'],'描述----',obj['name'])
                oobj = {'value':obj['value'],'desc':obj['name'],'unit':obj['unit'],'valueDesc':'','index':-1,'time':ots}
                r_real.hset("measure",name,str(oobj))
                histab = HisTable(name=name,value=obj['value'],dts_s=dts_s,dts_ms=44,ots=ots,cause=4)
                ygzhen1_session.merge(histab)
    ygzhen1_session.commit()
    ygzhen1_session.close()


def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(get_real_data, 'interval', seconds=900)  # 10分钟获取一次数据
    
    # scheduler.add_job(run_database, 'cron',  hour=1)  # 每天1点执行
    scheduler.start()

def get_his_data():
    '''历史数据'''
    # global token
    token = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hW3QU0uUA69NjtCDVGob6CAOSAIieDwraMvT8tUKYMBvkjesukZ7gHay7PxFDwHS6z_Xj1iZG2HEWawDhiNf3nlm9zoPDpA79ImEraN_d2FqPSy6tVm3coZaYwJlsCThukszWpPQn2bu2VIXVAJvqGjuNiFY3ebMxNZw5BFKSCUSTlQKLW7CilrLSQmP9aOGJDPMfkWjMLkMfsVM0-dNTPRKVPnRWD6Wi_hgkA8uhuCF4QL_oiBlcmGCMWeSKc6Yc25gHqRKd4k-4fAlsUKL7BEr6Fhn28VAQxnSPPs4lzdTtTgPBd_wUKxevJ6Zn3NH6TtghM7YaQC5S2HddW-xqA"
    print ('time:',timeUtils.getNewTimeStr())
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/device/v1.0/historical",
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s"%token,
                "User-Agent":"Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "deviceSn": "001565793841",
                "startTime":"1706616000",  # 十位的时间戳
                "endTime":"1706630400",
                "timeType":"5"
            })
        )
        # print ('旧token',token)
        # print('Response HTTP Response Body: {content}'.format(content=response.content))
        re = json.loads(response.content)
        code = re['code']
        if code == '2101017' or code == '2101019':  # token验证失败
            get_token()
            print (u'新token:',token)
            
        elif re['success']:  # 成功取得数据
            # print ('**************',re)
            his_data_save(re)
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def his_data_save(data):
    # ots = timeUtils.getNewTimeStr()
    # m = ots[0:7].replace('-','')
    k_arr = ['SN1','METERrate1','METER_PTC_TYP1','L_M_C_A_S_E','L_M_C_A_P_E','L_M_C_A_F_E','L_M_C_A_V_E','L_M_P_A_S_E','L_M_P_A_P_E','L_M_P_A_F_E','L_M_P_A_V_E','L_M_R_A_S_E','L_M_R_A_P_E','L_M_R_A_F_E','L_M_R_A_V_E']
    table = 'r_measure202402'
    base_name = 'tfStygzhen1.EMS.MET.'
    HisTable = HisACDMS_YG(table)
    if data['success']:
        allData = data['paramDataList']  # 所有数据
        
        for i in range(0,len(allData),3):  # 隔三个个取一次
        # for adata in allData:
            datalist = allData[i]['dataList']
            dts_s = allData[i]['collectTime']
            print ('time---------------',timeUtils.ssTtimes(dts_s))
            for obj in datalist:
                k = obj['key']
                print ('----------',obj)
                if k not in k_arr and 'value' in obj.keys():  #过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s'%(base_name,obj['key'])
                    print ('name:',name,'----value:',obj['value'],'描述----',obj['name'])
                    # oobj = {'value':obj['value'],'desc':obj['name'],'unit':obj['unit'],'valueDesc':'','index':-1,'time':timeUtils.ssTtimes(dts_s)}
                    # r_real.hset("measure",name,str(oobj))
                    histab = HisTable(name=name,value=obj['value'],dts_s=dts_s,dts_ms=random.randint(100, 999),ots=timeUtils.ssTtimes(dts_s),cause=4)
                    ygzhen1_session.merge(histab)
    ygzhen1_session.commit()
    ygzhen1_session.close()

def get_ramain_num():
    '''
    查看还剩余多少调用次数
    '''
    try:
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://api.solarmanpv.com/account/v1.0/balance'
        else:
            url = 'http://172.17.5.187:50000/yzsettle/'
        response = requests.post(
            url=url,
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s"%token,
                "User-Agent":"Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "appId": "****************",
            })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

if __name__ == '__main__':
    get_his_data()
    # get_real_data()
    # RunClearFileAndData()
    # get_token()
    
    # d = '{"code":null,"msg":null,"success":true,"requestId":"73b99c342f954845","deviceSn":"001537580774","deviceId":219807578,"deviceType":"METER","deviceState":1,"dataList":[{"key":"SN1","value":"001537580774","unit":null,"name":"SN号"},{"key":"METERrate1","value":"1","unit":null,"name":"电表倍率"},{"key":"METER_PTC_TYP1","value":"0","unit":null,"name":"电表协议类型"},{"key":"Pt1","value":"619.2","unit":"W","name":"有功总功率"},{"key":"Etdy_pos1","value":"4.06","unit":"kWh","name":"日正向总电能"},{"key":"Etdy_pos_sharp1","value":"0","unit":"kWh","name":"日正向尖电能"},{"key":"Etdy_pos_peak1","value":"0","unit":"kWh","name":"日正向峰电能"},{"key":"Etdy_pos_ord1","value":"1.63","unit":"kWh","name":"日正向平电能"},{"key":"Edty_pos_vly1","value":"2.44","unit":"kWh","name":"日正向谷电能"},{"key":"Etdy_neg1","value":"1.80","unit":"kWh","name":"日反向总电能"},{"key":"Edty_neg_sharp1","value":"0","unit":"kWh","name":"日反向尖电能"},{"key":"Edty_neg_peak1","value":"1.80","unit":"kWh","name":"日反向峰电能"},{"key":"Edty_neg_ord1","value":"0.01","unit":"kWh","name":"日反向平电能"},{"key":"Edty_neg_vly1","value":"0","unit":"kWh","name":"日反向谷电能"},{"key":"Et_pos1","value":"334.16","unit":"kWh","name":"总正向总电能"},{"key":"Et_pos_sharp1","value":"0.00","unit":"kWh","name":"总正向尖电能"},{"key":"Et_pos_peak1","value":"5.96","unit":"kWh","name":"总正向峰电能"},{"key":"Et_pos_ord1","value":"142.64","unit":"kWh","name":"总正向平电能"},{"key":"Et_pos_vly1","value":"185.56","unit":"kWh","name":"总正向谷电能"},{"key":"Et_neg1","value":"271.30","unit":"kWh","name":"总反向总电能"},{"key":"Et_neg_sharp1","value":"0.00","unit":"kWh","name":"总反向尖电能"},{"key":"Et_neg_peak1","value":"260.98","unit":"kWh","name":"总反向峰电能"},{"key":"Et_neg_ord1","value":"10.31","unit":"kWh","name":"总反向平电能"},{"key":"Et_neg_vly1","value":"0.00","unit":"kWh","name":"总反向谷电能"},{"key":"Et_cbn_a1","value":"605.47","unit":"kWh","name":"组合有功总电能"},{"key":"Et_cbn_a_sharp1","value":"0.00","unit":"kWh","name":"组合有功尖电能"},{"key":"Et_cbn_a_peak1","value":"266.94","unit":"kWh","name":"组合有功峰电能"},{"key":"Et_cbn_a_ord1","value":"152.95","unit":"kWh","name":"组合有功平电能"},{"key":"Et_cbn_a_vly1","value":"185.56","unit":"kWh","name":"组合有功谷电能"},{"key":"E_rat_cbn_sharp1","value":"0.00","unit":"kWh","name":"组合无功1尖电能"},{"key":"E_rat_cbn_peak1","value":"12.46","unit":"kWh","name":"组合无功1峰电能"},{"key":"E_rat_cbn_ord1","value":"13.16","unit":"kWh","name":"组合无功1平电能"},{"key":"E_rat_cbn_vly1","value":"15.55","unit":"kWh","name":"组合无功1谷电能"},{"key":"E_rat_cbn_sharp2","value":"0.00","unit":"kWh","name":"组合无功2尖电能"},{"key":"E_rat_cbn_peak2","value":"2.08","unit":"kWh","name":"组合无功2峰电能"},{"key":"E_rat_cbn_ord2","value":"5.00","unit":"kWh","name":"组合无功2平电能"},{"key":"E_rat_cbn_vly2","value":"4.70","unit":"kWh","name":"组合无功2谷电能"},{"key":"E_Ap_pos1","value":"161.76","unit":"kWh","name":"A相正向有功电能"},{"key":"E_Ap_neg1","value":"139.50","unit":"kWh","name":"A相反向有功电能"},{"key":"E_rat_Ap_cbn1","value":"109.33","unit":"kWh","name":"A相组合无功1电能"},{"key":"E_rat_Ap_cbn2","value":"76.52","unit":"kWh","name":"A相组合无功2电能"},{"key":"E_Bp_pos1","value":"0.00","unit":"kWh","name":"B相正向有功电能"},{"key":"E_Bp_neg1","value":"0.00","unit":"kWh","name":"B相反向有功电能"},{"key":"E_rat_Bp_cbn1","value":"0.00","unit":"kWh","name":"B相组合无功1电能"},{"key":"E_rat_Bp_cbn2","value":"0.00","unit":"kWh","name":"B相组合无功2电能"},{"key":"E_Cp_pos1","value":"172.88","unit":"kWh","name":"C相正向有功电能"},{"key":"E_Cp_neg1","value":"132.27","unit":"kWh","name":"C相反向有功电能"},{"key":"E_rat_Cp_cbn1","value":"84.97","unit":"kWh","name":"C相组合无功1电能"},{"key":"E_rat_Cp_cbn2","value":"88.37","unit":"kWh","name":"C相组合无功2电能"},{"key":"EPt_pos1","value":"41.18","unit":"KVarh","name":"总正向无功总电能"},{"key":"EQt_pos1","value":"11.78","unit":"KVarh","name":"总反向无功总电能"}]}'
    # dd = json.loads(d)
    # i = 0
    
    # data_save(dd)
       
   