# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/26 17:01
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : system_monitor.py
# @Software : PyCharm
import datetime
import json
import re
import time
import traceback
from decimal import Decimal

from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.user import models
from apis.user.models import PointType, PointMeasure, PointStatus
from apis.web2.project_dashboard import error_log
from common import common_response_code
from common.constant import EMPTY_STR_LIST
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from settings.meter_settings import METER_DIC
from settings.version_maps import Version_Dict
from tools.day_hours_used import select_latest_data_from_dwd_rhyc, pool, select_range_data_from_dwd_rhyc


class SystemMonitorProjectsView(APIView):
    """集中监控-系统监控: 项目列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        try:
            user = models.UserDetails.objects.get(id=request.user.get('user_id'))
            projects = user.project_set.filter(is_used=1).all()
            if projects.exists():
                details = [{"id": project.id, "name": project.name, "en_name": project.english_name} for project in projects]
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": details,
                        },
                    }
                )
            else:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {
                            "message": "no data",
                            "detail": "集中监控-系统监控: 当前用户无项目信息",
                        },
                    }
                )

        except Exception as e:
            error_log.error("集中监控-系统监控: 用户不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 用户不存在!",
                    },
                }
            )


class SystemMonitorStationsView(APIView):
    """集中监控-系统监控: 项目-站列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request, pk):
        try:
            project = models.Project.objects.get(id=pk)
            stations = project.stationdetails_set.filter(is_delete=0).all()
            if stations.exists():
                details = [{"id": station.id, "name": station.master_station.name + '--' + station.station_name,
                            "en_name": station.english_name} for station in stations]
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": details,
                        },
                    }
                )
            else:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {
                            "message": "no data",
                            "detail": "集中监控-系统监控: 当前项目无站信息",
                        },
                    }
                )

        except Exception as e:
            error_log.error("集中监控-系统监控: 项目不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 项目不存在!",
                    },
                }
            )


class SystemMonitorView(APIView):
    """集中监控-系统监控"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def count_charge_discharge(self, new_list_, charge, discharge):
        # new_list_ = sorted(datas, key=lambda x: x["time"])
        first_charge = new_list_[0][charge]
        first_discharge = new_list_[0][discharge]

        last_charge = new_list_[-1][charge]
        last_discharge = new_list_[-1][discharge]
        charge_count = abs(Decimal(last_charge) - Decimal(first_charge))
        discharge_count = abs(Decimal(last_discharge) - Decimal(first_discharge))
        return charge_count, discharge_count

    def get(self, request, pk):
        try:
            station = models.StationDetails.objects.get(id=pk)
        except Exception as e:
            error_log.error("集中监控-系统监控: 查询站出现异常：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 站不存在!",
                    },
                }
            )

        conn_ = pool.connection()
        cursor = conn_.cursor()
        detail = dict()
        detail["station"] = {"page_area": 0}

        station_english_name = station.english_name
        station_app = station.app
        meter_type = station.meter_type
        meter_dic = METER_DIC.get(meter_type)
        charge_key = meter_dic.get("charge")
        discharge_key = meter_dic.get("discharge")
        device = meter_dic.get("device")

        conn = get_redis_connection("3")

        # 查询站的信息：变压器容量、并网点功率、今日充电量、今日放电量等
        # result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'measure', 'EMS', 'TFM', 'PCC')

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        e_station = models.StationDetails.objects.filter(is_delete=0, master_station=station.master_station,
                                                         english_name=station.master_station.english_name).first()
        key1_ = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', e_station.english_name, 'EMS')

        measure_ems = conn.get(key1_)
        if measure_ems:
            measure_ems_dict = json.loads(json.loads(measure_ems.decode("utf-8")))
        else:
            measure_ems_dict = {}

        if measure_ems_dict:
            detail["station"]['tfm'] = {
                "value": measure_ems_dict.get('TFM') if measure_ems_dict.get('TFM') not in EMPTY_STR_LIST else station.transformer_capacity,
                "unit": 'kVA',
                "desc": '变压器容量'
            }

            detail["station"]['pcc'] = {
                "value": measure_ems_dict.get('PCC') if measure_ems_dict.get('PCC') not in EMPTY_STR_LIST else '--',
                "unit": 'kW',
                "desc": '并网点功率'
            }

            detail["station"]['day_charge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日充电量"
            }
            detail["station"]['day_discharge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日放电量"
            }
            detail['units'] = []
            detail["station"]['meter_position'] = {
                "value": station.meter_position if station.meter_position else '1',
                "enums": {
                    1: '电表前置',
                    2: '电表后置',
                },
                "desc": "电表位置"
            }
        else:
            detail["station"]['tfm'] = {
                "value": station.transformer_capacity,
                "unit": 'kVA',
                "desc": '变压器容量'
            }

            detail["station"]['pcc'] = {
                "value": '--',
                "unit": 'kW',
                "desc": '并网点功率'
            }

            detail["station"]['day_charge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日充电量"
            }
            detail["station"]['day_discharge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日放电量"
            }
            detail['units'] = []
            detail["station"]['meter_position'] = {
                "value": station.meter_position if station.meter_position else '1',
                "enums": {
                    1: '电表前置',
                    2: '电表后置',
                },
                "desc": "电表位置"
            }

        units = station.unit_set.filter(is_delete=0).all()

        now_time_ = int(time.time())
        now_time = datetime.datetime.fromtimestamp(now_time_).strftime("%Y-%m-%d %H:%M:%S")
        start_time = now_time.split(' ')[0] + ' 00:00:00'
        # end_time = start_time.replace('00:00:00', '23:59:59')
        start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_datatime = datetime.datetime.strptime(now_time, "%Y-%m-%d %H:%M:%S")

        is_online = 0             # 判断并网点是否是离线状态

        # 查询储能单元信息
        if units.exists():
            num = 1
            for unit in units:
                unit_dict = dict()
                unit_dict['id'] = unit.id
                unit_dict['page_area'] = num

                # 计算充放电量
                bms_ = getattr(unit, device)
                pcs = unit.pcs

                results = select_range_data_from_dwd_rhyc(station_english_name, 'cumulant', device, bms_, start_datatime,
                                                       end_datatime, charge_key, discharge_key)

                if results:
                    unit_charge, unit_discharge = self.count_charge_discharge(results, charge_key, discharge_key)
                    unit_dict['day_charge'] = {
                        "value": unit_charge,
                        "unit": "kWh",
                        "desc": "今日充电量"
                    }
                    unit_dict['day_discharge'] = {
                        "value": unit_discharge,
                        "unit": "kWh",
                        "desc": "今日放电量"
                    }
                    detail['station']['day_charge']['value'] += unit_charge
                    detail['station']['day_discharge']['value'] += unit_discharge

                    is_online += 1

                else:
                    unit_dict['day_charge'] = {
                        "value": '--',
                        "unit": "kWh",
                        "desc": "今日充电量"
                    }
                    unit_dict['day_discharge'] = {
                        "value": '--',
                        "unit": "kWh",
                        "desc": "今日放电量"
                    }

                # PCS运行状态
                # result_ = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'discrete', pcs, 'PRse')

                key6_ = "Business:TRANS_TIMING_{}_{}_{}".format('DISCRETE', station_english_name, pcs)
                discrete_pcs = conn.get(key6_)
                if discrete_pcs:
                    discrete_pcs_dict = json.loads(json.loads(discrete_pcs.decode("utf-8")))
                else:
                    discrete_pcs_dict = {}

                if discrete_pcs_dict:
                    unit_dict['pcs_prse'] = {
                        "value": discrete_pcs_dict.get('PRse') if discrete_pcs_dict.get('PRse') and discrete_pcs_dict.get('PRse') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }
                else:
                    unit_dict['pcs_prse'] = {
                        "value": '1',
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }

                # pcs 状态量
                # pcs_status_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'status', pcs, 'PCStu', 'alarm', 'Fault', 'DCse')
                key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, pcs)
                status_pcs = conn.get(key3)
                if status_pcs:
                    status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                else:
                    status_pcs_dict = {}

                if status_pcs_dict:
                    unit_dict['pcs_online'] = {
                        "value": "1",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{} PCS在线离线状态".format(pcs)
                    }

                    # 离线的不要显示充放电状态，默认待机: pcs离线的，充放电状态按待机
                    if unit_dict['pcs_online']['value'] == '0':
                        unit_dict['pcs_prse'] = {
                            "value": '1',
                            "enums": {
                                0: '停机',
                                1: '待机',
                                2: '放电运行',
                                3: '充电运行',
                                4: '零功率运行'
                            },
                            "desc": "{} PCS运行状态".format(pcs)
                        }

                    unit_dict['pcs_tu'] = {
                        "value": status_pcs_dict.get('PCStu') if status_pcs_dict.get('PCStu') and status_pcs_dict.get('PCStu') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "PCS开关机"
                    }
                    unit_dict['pcs_alarm'] = {
                        "value": status_pcs_dict.get('alarm') if status_pcs_dict.get('alarm') and status_pcs_dict.get('alarm') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{} PCS报警状态".format(pcs)
                    }
                    unit_dict['pcs_fault'] = {
                        "value": status_pcs_dict.get('Fault') if status_pcs_dict.get('Fault') and status_pcs_dict.get('Fault') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{} PCS故障状态".format(pcs)
                    }
                    unit_dict['pcs_dcse'] = {
                        "value": status_pcs_dict.get('DCse') if status_pcs_dict.get('DCse') and status_pcs_dict.get('DCse') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{} 直流接触器状态".format(pcs)
                    }
                else:
                    unit_dict['pcs_online'] = {
                        "value": "0",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{} PCS在线离线状态".format(pcs)
                    }
                    unit_dict['pcs_prse'] = {
                        "value": '1',
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }
                    unit_dict['pcs_tu'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "PCS开关机"
                    }
                    unit_dict['pcs_alarm'] = {
                        "value": '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{} PCS报警状态".format(pcs)
                    }
                    unit_dict['pcs_fault'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{} PCS故障状态".format(pcs)
                    }
                    unit_dict['pcs_dcse'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{} 直流接触器状态".format(pcs)
                    }

                # pcs 测量量
                # pcs_measure_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'measure', pcs, 'PUa', 'PUb', 'PUc', 'PUab', 'PUbc', 'PUca', 'Ia', 'Ib', 'Ic', 'P')
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, pcs)
                measure_pcs = conn.get(key2)
                if measure_pcs:
                    measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
                else:
                    measure_pcs_dict = {}

                if measure_pcs_dict:
                    unit_dict['pcs_ua'] = {
                        "value": measure_pcs_dict.get('PUa') if measure_pcs_dict.get('PUa') and measure_pcs_dict.get('PUa') not in EMPTY_STR_LIST else measure_pcs_dict.get('PUab'),
                        "unit": "V",
                        "desc": "{} A相电网相电压".format(pcs) if measure_pcs_dict.get('PUa') and measure_pcs_dict.get('PUa') not in EMPTY_STR_LIST else "{} AB相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_ub'] = {
                        "value": measure_pcs_dict.get('PUb') if measure_pcs_dict.get('PUb') and measure_pcs_dict.get('PUb') not in EMPTY_STR_LIST else measure_pcs_dict.get('PUbc'),
                        "unit": "V",
                        "desc": "{} B相电网相电压".format(pcs) if measure_pcs_dict.get('PUb') and measure_pcs_dict.get('PUb') not in EMPTY_STR_LIST else "{} BC相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_uc'] = {
                        "value": measure_pcs_dict.get('PUc') if measure_pcs_dict.get('PUc') and measure_pcs_dict.get('PUc') not in EMPTY_STR_LIST else measure_pcs_dict.get('PUca'),
                        "unit": "V",
                        "desc": "{} C相电网相电压".format(pcs) if measure_pcs_dict.get('PUc') and measure_pcs_dict.get('PUc') not in EMPTY_STR_LIST else "{} CA相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_ia'] = {
                        "value": measure_pcs_dict.get('Ia') if measure_pcs_dict.get('Ia') and measure_pcs_dict.get('Ia') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} A相电流".format(pcs)
                    }
                    unit_dict['pcs_ib'] = {
                        "value": measure_pcs_dict.get('Ib') if measure_pcs_dict.get('Ib') and measure_pcs_dict.get('Ib') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} B相电流".format(pcs)
                    }
                    unit_dict['pcs_ic'] = {
                        "value": measure_pcs_dict.get('Ic') if measure_pcs_dict.get('Ic') and measure_pcs_dict.get('Ic') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} C相电流".format(pcs)
                    }
                    unit_dict['pcs_p'] = {
                        "value": measure_pcs_dict.get('P') if measure_pcs_dict.get('P') and measure_pcs_dict.get('P') not in EMPTY_STR_LIST else '--',
                        "unit": "kW",
                        "desc": "{}输出总有功功率".format(pcs)
                    }

                    # 非标的“七章公园”运行状态特殊处理
                    if station_english_name == 'HZDC101':
                        if unit_dict['pcs_online']['value'] == '0':
                            unit_dict['pcs_prse'] = {
                                "value": '1',
                                "enums": {
                                    0: '停机',
                                    1: '待机',
                                    2: '放电运行',
                                    3: '充电运行',
                                    4: '零功率运行'
                                },
                                "desc": "{} PCS运行状态".format(pcs)
                            }
                        else:
                            if unit_dict['pcs_p']['value'] == '--':
                                unit_dict['pcs_prse'] = {
                                    "value": '1',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            elif float(unit_dict['pcs_p']['value']) > 0:
                                unit_dict['pcs_prse'] = {
                                    "value": '2',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            elif float(unit_dict['pcs_p']['value']) == 0:
                                unit_dict['pcs_prse'] = {
                                    "value": '1',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            else:
                                unit_dict['pcs_prse'] = {
                                    "value": '3',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                    # end
                else:
                    pcs_measure_result = select_latest_data_from_dwd_rhyc(station_english_name,
                                                                        'measure', 'pcs', pcs,
                                                                           'PUa', 'PUb', 'PUc', 'PUab',
                                                                        'PUbc', 'PUca', 'Ia', 'Ib', 'Ic', 'P')
                    if pcs_measure_result:
                        unit_dict['pcs_ua'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} A相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUa') and pcs_measure_result.get('PUa') not in EMPTY_STR_LIST else "{} AB相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_ub'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} B相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUb') and pcs_measure_result.get('PUb') not in EMPTY_STR_LIST else "{} BC相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_uc'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} C相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUc') and pcs_measure_result.get('PUc') not in EMPTY_STR_LIST else "{} CA相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_ia'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} A相电流".format(pcs)
                        }
                        unit_dict['pcs_ib'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} B相电流".format(pcs)
                        }
                        unit_dict['pcs_ic'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} C相电流".format(pcs)
                        }
                        unit_dict['pcs_p'] = {
                            "value": '--',
                            "unit": "kW",
                            "desc": "{}输出总有功功率".format(pcs)
                        }

                # bms 状态量
                # bms_status_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'status', bms_, 'GFault', 'HPCCse', 'GAlarm', 'ComFau')
                key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, bms_)
                status_bms = conn.get(key6)
                if status_bms:
                    status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                else:
                    status_bms_dict = {}
                if status_bms_dict:
                    unit_dict['bms_online'] = {
                        "value": "1",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{}在线离线状态".format(bms_)
                    }

                    unit_dict['bms_hpccse'] = {
                        "value": status_bms_dict.get('HPCCse') if status_bms_dict.get('HPCCse') and status_bms_dict.get('HPCCse') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{}高压闭合状态".format(bms_)
                    }
                    unit_dict['bms_alarm'] = {
                        "value": status_bms_dict.get('GAlarm') if status_bms_dict.get('GAlarm') and status_bms_dict.get('GAlarm') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{}总报警".format(bms_)
                    }
                    unit_dict['bms_fault'] = {
                        "value": status_bms_dict.get('GFault') if status_bms_dict.get('GFault') and status_bms_dict.get('GFault') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}总故障".format(bms_)
                    }
                    unit_dict['bms_comfau'] = {
                        "value": status_bms_dict.get('ComFau') if status_bms_dict.get('ComFau') and status_bms_dict.get('ComFau') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}压缩机故障".format(bms_)
                    }
                else:
                    unit_dict['bms_online'] = {
                        "value": "0",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{}在线离线状态".format(bms_)
                    }

                    unit_dict['bms_hpccse'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{}高压闭合状态".format(bms_)
                    }
                    unit_dict['bms_alarm'] = {
                        "value": '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{}总报警".format(bms_)
                    }
                    unit_dict['bms_fault'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}总故障".format(bms_)
                    }
                    unit_dict['bms_comfau'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}压缩机故障".format(bms_)
                    }

                # bms 测量量
                # bms_measure_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name,
                #                                                     'measure', bms_, 'U', 'I', 'SOC',
                #                                                     'MUMax', 'MUMin', 'MTMax', 'MTMin', 'ET', 'RWT',
                #                                                     'IPV', 'OPV')

                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, bms_)
                measure_bms = conn.get(key1)
                if measure_bms:
                    measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
                else:
                    measure_bms_dict = {}

                if measure_bms_dict:
                    unit_dict['bms_u'] = {
                        "value": measure_bms_dict.get('U') if measure_bms_dict.get('U') and measure_bms_dict.get('U') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}总电压".format(bms_)
                    }
                    unit_dict['bms_i'] = {
                        "value": measure_bms_dict.get('I') if measure_bms_dict.get('I') and measure_bms_dict.get('I') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{}总电流".format(bms_)
                    }
                    unit_dict['bms_soc'] = {
                        "value": measure_bms_dict.get('SOC') if measure_bms_dict.get('SOC') and measure_bms_dict.get('SOC') not in EMPTY_STR_LIST else '--',
                        "unit": "%",
                        "desc": "{} SOC".format(bms_)
                    }
                    unit_dict['bms_umax'] = {
                        "value": measure_bms_dict.get('MUMax') if measure_bms_dict.get('MUMax') and measure_bms_dict.get('MUMax') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}最高单体电压".format(bms_)
                    }
                    unit_dict['bms_umin'] = {
                        "value": measure_bms_dict.get('MUMin') if measure_bms_dict.get('MUMin') and measure_bms_dict.get('MUMin') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}最低单体电压".format(bms_)
                    }
                    unit_dict['bms_tmax'] = {
                        "value": measure_bms_dict.get('MTMax') if measure_bms_dict.get('MTMax') and measure_bms_dict.get('MTMax') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}最高单体温度".format(bms_)
                    }
                    unit_dict['bms_tmin'] = {
                        "value": measure_bms_dict.get('MTMin') if measure_bms_dict.get('MTMin') and measure_bms_dict.get('MTMin') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}最低单体温度".format(bms_)
                    }

                    unit_dict['bms_et'] = {
                        "value": measure_bms_dict.get('ET') if measure_bms_dict.get('ET') and measure_bms_dict.get('ET') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}出水温度".format(bms_)
                    }
                    unit_dict['bms_rwt'] = {
                        "value": measure_bms_dict.get('RWT') if measure_bms_dict.get('RWT') and measure_bms_dict.get('RWT') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}回水温度".format(bms_)
                    }
                    unit_dict['bms_ipv'] = {
                        "value": measure_bms_dict.get('IPV') if measure_bms_dict.get('IPV') and measure_bms_dict.get('IPV') not in EMPTY_STR_LIST else '--',
                        "unit": "bar",
                        "desc": "{}进水口压力值".format(bms_)
                    }
                    unit_dict['bms_opv'] = {
                        "value": measure_bms_dict.get('OPV') if measure_bms_dict.get('OPV') and measure_bms_dict.get('OPV') not in EMPTY_STR_LIST else '--',
                        "unit": "bar",
                        "desc": "{}出水口压力值".format(bms_)
                    }

                else:
                    unit_dict['bms_u'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}总电压".format(bms_)
                    }
                    unit_dict['bms_i'] = {
                        "value": '--',
                        "unit": "A",
                        "desc": "{}总电流".format(bms_)
                    }
                    unit_dict['bms_soc'] = {
                        "value": '--',
                        "unit": "%",
                        "desc": "{} SOC".format(bms_)
                    }
                    unit_dict['bms_umax'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}最高单体电压".format(bms_)
                    }
                    unit_dict['bms_umin'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}最低单体电压".format(bms_)
                    }
                    unit_dict['bms_tmax'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}最高单体温度".format(bms_)
                    }
                    unit_dict['bms_tmin'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}最低单体温度".format(bms_)
                    }

                    unit_dict['bms_et'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}出水温度".format(bms_)
                    }
                    unit_dict['bms_rwt'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}回水温度".format(bms_)
                    }
                    unit_dict['bms_ipv'] = {
                        "value": '--',
                        "unit": "bar",
                        "desc": "{}进水口压力值".format(bms_)
                    }
                    unit_dict['bms_opv'] = {
                        "value": '--',
                        "unit": "bar",
                        "desc": "{}出水口压力值".format(bms_)
                    }

                detail['units'].append(unit_dict)
                num += 1

        if not is_online:
            detail["station"]['day_charge'] = {
                "value": '--',
                "unit": "kWh",
                "desc": "今日充电量"
            }
            detail["station"]['day_discharge'] = {
                "value": '--',
                "unit": "kWh",
                "desc": "今日放电量"
            }

        try:
            cursor.close()
            conn.close()
        except:
            pass

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail,
                },
            }
        )


class VersionNumberView(APIView):
    """
    版本信息查看
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request, pk):
        try:
            project = models.Project.objects.get(id=pk, is_used=1)
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": f"项目:{pk}不存在!",
                    },
                }
            )

        return_dict = {
            "ems": [],
            "pcs": [],
            "bms": []
        }

        # stations = project.stationdetails_set.all()
        master_stations = project.materstation_set.filter(is_delete=0).all()
        if master_stations.exists():
            conn = get_redis_connection("3")
            for master_station in master_stations:
                slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                if slave_stations.exists():
                    for slave_station in slave_stations:
                        # if slave_station.slave == 0 or slave_station.slave == -1:

                        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
                        if slave_station.english_name == slave_station.master_station.english_name:
                            # 查询网关版本号
                            station_ems_list = []
                            # key1 = str(english_name + "_" + app + "_" + unit_["pcs"] + "_" + "EFPR")
                            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, 'EMS')
                            measure_ems = conn.get(key1)
                            if measure_ems:
                                measure_ems_str = measure_ems.decode("utf-8")
                                measure_ems_dict = json.loads(json.loads(measure_ems_str))
                                for k, v in Version_Dict['EMS'].items():
                                    temp_dict = {'name': v['name'],
                                                 'point': k,
                                                 'value': measure_ems_dict.get(k) if measure_ems_dict.get(k) and measure_ems_dict.get(k) not in EMPTY_STR_LIST else '--',
                                                 'note': v['note']}
                                    station_ems_list.append(temp_dict)
                            else:
                                for k, v in Version_Dict['EMS'].items():
                                    temp_dict = {'name': v['name'],
                                                 'point': k,
                                                 'value': '--',
                                                 'note': v['note']}
                                    station_ems_list.append(temp_dict)

                            station_ems_dict_ = {"statin": master_station.name, "version": station_ems_list}
                            return_dict['ems'].append(station_ems_dict_)

                        if ((slave_station.slave != 0 and slave_station.pack == -1) or
                                (slave_station.slave == -1 and slave_station.pack != 0)):
                            units = slave_station.unit_set.filter(is_delete=0).all()
                            if units.exists():
                                for unit in units:
                                    # 查询PCS版本
                                    unit_pcs_list = []
                                    key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE',
                                                                                   slave_station.english_name, unit.pcs)
                                    measure_pcs = conn.get(key2)
                                    if measure_pcs:
                                        measure_pcs_str = measure_pcs.decode("utf-8")
                                        measure_pcs_dict = json.loads(json.loads(measure_pcs_str))
                                        for k, v in Version_Dict['PCS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': measure_pcs_dict.get(k) if measure_pcs_dict.get(k) and measure_pcs_dict.get(k) not in EMPTY_STR_LIST
                                                         else '--',
                                                         'note': v['note']}
                                            unit_pcs_list.append(temp_dict)

                                    else:
                                        for k, v in Version_Dict['PCS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': '--',
                                                         'note': v['note']}
                                            unit_pcs_list.append(temp_dict)

                                    unit_pcs_dict_ = {"unit": master_station.name + ' ' + unit.unit_new_name,
                                                      "version": unit_pcs_list}
                                    return_dict['pcs'].append(unit_pcs_dict_)

                                    unit_bms_list = []
                                    key3 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE',
                                                                                   slave_station.english_name, unit.bms)
                                    measure_bms = conn.get(key3)
                                    if measure_bms:
                                        measure_bms_str = measure_bms.decode("utf-8")
                                        measure_bms_dict = json.loads(json.loads(measure_bms_str))
                                        for k, v in Version_Dict['BMS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': measure_bms_dict.get(k) if measure_bms_dict.get(k) and measure_bms_dict.get(k) not in EMPTY_STR_LIST
                                                         else '--',
                                                         'note': v['note']}
                                            unit_bms_list.append(temp_dict)

                                    else:
                                        for k, v in Version_Dict['BMS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': '--',
                                                         'note': v['note']}
                                            unit_bms_list.append(temp_dict)

                                    unit_bms_dict_ = {"unit": master_station.name + ' ' + unit.unit_new_name,
                                                      "version": unit_bms_list}
                                    return_dict['bms'].append(unit_bms_dict_)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                },
            }
        )


class PcsMonitorTitlesView(APIView):
    """PCS、电池簇监测: 标头列表"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        project_id = request.query_params['project_id']
        monitor_type = request.query_params['monitor_type']

        result = list()

        if not all([project_id, type]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "项目ID参数缺失"},
                }
            )
        # print(project_id, monitor_type)
        if not re.match(r'\d+', project_id):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误"},
                }
            )

        # 电站集合
        # station_instances = models.StationDetails.objects.filter(project_id=project_id).all()
        # for station_instance in station_instances:
        master_stations = models.MaterStation.objects.filter(project_id=project_id, is_delete=0).all()
        for master_station in master_stations:
            station_name = master_station.name

            units = models.Unit.objects.filter(station__master_station_id=master_station.id)
            for unit_ in units:
                unit_name = unit_.unit_new_name
                bms = unit_.bms
                pcs = unit_.pcs

                # 表头，示例："宁波朗盛002-储能单元1-PCS"
                if monitor_type == 'pcs':
                    title = station_name + '-' + unit_name + '-' + pcs
                else:
                    title = station_name + '-' + unit_name + '-' + bms.replace('BMS', '电池簇')
                result.append(title)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": result,
                },
            }
        )


class MonitorView(APIView):
    """PCS/电源监测：实时遥测数据"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        title = request.query_params['title']
        data_type = request.query_params['data_type']

        if not title:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "PCS/电源监测：参数缺失"},
                }
            )
        if not re.match(r'.+?-.+?-.+', title):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "PCS/电源监测：参数格式错误"},
                }
            )

        a = title.split('-')
        if len(a) == 3:
            master_station = a[0]
            unit_name = a[1]
            device = a[2].replace('电池簇', 'BMS') if '电池' in a[2] else a[2]
        elif len(a) == 4:       # 兼容“宁波朗盛001”的并网点名称改叫“朗盛-昱泓东区”；
            master_station = '-'.join(a[:2])
            unit_name = a[2]
            device = a[3].replace('电池簇', 'BMS') if '电池' in a[3] else a[3]

        try:
            # 电站集合
            # station_instance = models.StationDetails.objects.filter(station_name=station).first()
            master_station = models.MaterStation.objects.filter(name=master_station, is_delete=0).first()

            # station_name = master_station.station_name
            # station_english_name = master_station.english_name

            # slave_stations = master_station.stationdetails_set.exclude(slave=0, pack=0).all()

            units = models.Unit.objects.filter(is_delete=0, station__master_station_id=master_station.id, unit_new_name=unit_name)
            if units.count() > 1 or not units.exists():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "PCS/电源监测：查询储能单元信息异常"},
                    }
                )

            unit = units.first()
            conn = get_redis_connection("3")

            station_app = unit.station.app
            station_english_name = unit.station.english_name
            unit_type = unit.station.unit_number

            # 点表类型表
            type_instance = PointType.objects.filter(type=unit_type, device=device).first()
            if data_type == 'telemetry':
                # 查询点表测量量最新数据
                # measure_list = select_least_by_measure_m(station_app, station_english_name)
                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, device)
                measure_1 = conn.get(key1)
                if measure_1:
                    measure_1_dict = json.loads(json.loads(measure_1.decode("utf-8")))
                else:
                    measure_1_dict = {}

                # 点表：测量量指标
                measure_items = PointMeasure.objects.filter(point_type_id=type_instance.id)
                measure_dict = dict()
                for measure_item in measure_items:
                    measure_dict.update({measure_item.name: {"name": measure_item.description,
                                                             "unit": measure_item.unit,
                                                             "id": measure_item.id}}),

                realtime_telemetry_data = list()  # 实时遥测数据
                if measure_1_dict:
                    for k, v in measure_1_dict.items():
                        if k != 'device' and k in measure_dict.keys():
                            temp_dict = {
                                "id": measure_dict[k]['id'],
                                "key": k,
                                "name": measure_dict[k]['name'],
                                "unit": measure_dict[k]['unit'],
                                "value": v if v and v not in EMPTY_STR_LIST else '--'
                            }
                            realtime_telemetry_data.append(temp_dict)

                    for k1, v1 in measure_dict.items():
                        if k1 not in measure_1_dict.keys():
                            temp_dict = {
                                "id": measure_dict[k1]['id'],
                                "key": k1,
                                "name": measure_dict[k1]['name'],
                                "unit": measure_dict[k1]['unit'],
                                "value": '--'
                            }
                            realtime_telemetry_data.append(temp_dict)

                else:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "{}监测：获取遥测数据失败".format(device)}
                        }
                    )

                # 按点表顺序排序
                sorted_realtime_telemetry_data = sorted(realtime_telemetry_data, key=lambda x: x['id'])
                result = {
                    "title": title,
                    "data": sorted_realtime_telemetry_data
                }
            else:
                # 查询点表状态量最新数据
                # status_list = select_least_by_status_m(station_app, station_english_name)
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, device)
                status_1 = conn.get(key2)
                if status_1:
                    status_1_dict = json.loads(json.loads(status_1.decode("utf-8")))
                else:
                    status_1_dict = {}

                # 点表：状态量指标
                status_items = PointStatus.objects.filter(point_type_id=type_instance.id)
                status_dict = dict()
                for status_item in status_items:
                    status_dict.update({status_item.name: {"name": status_item.description,
                                                           "a_status_name": status_item.a_status_name,
                                                           "b_status_name": status_item.a_status_name,
                                                           "id": status_item.id}})

                realtime_remote_data = list()  # 实时遥信数据
                if status_1_dict:
                    for k1, v1 in status_1_dict.items():
                        if k1 != 'device' and k1 in status_dict.keys():
                            temp_dict_ = {
                                "id": status_dict[k1]['id'],
                                "key": k1,
                                "name": status_dict[k1]['name'],
                                "value": status_dict[k1]['a_status_name'] if v1 == "0"
                                else status_dict[k1]['b_status_name']
                            }
                            realtime_remote_data.append(temp_dict_)
                    for k2, v2 in status_dict.items():
                        if k2 not in status_1_dict.keys():
                            temp_dict_ = {
                                "id": status_dict[k2]['id'],
                                "key": k2,
                                "name": status_dict[k2]['name'],
                                "value": status_dict[k2]['a_status_name'] if v2 == "0"
                                else status_dict[k2]['b_status_name']
                            }
                            realtime_remote_data.append(temp_dict_)
                else:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "{}监测：获取遥信数据失败".format(device)},
                        }
                    )

                # 按点表顺序排序
                sorted_realtime_remote_data = sorted(realtime_remote_data, key=lambda x: x['id'])
                result = {"title": title, "data": sorted_realtime_remote_data}

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": result,
                    },
                }
            )

        except Exception as e:
            print(traceback.print_exc())
            error_log.error("{}监测：获取遥测数据出错：{}".format(device, e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "{}监测：获取遥测数据出错".format(device)},
                }
            )