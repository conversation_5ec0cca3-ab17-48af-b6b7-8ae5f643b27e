#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Tools\TimeTask\app_client.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:34:34

'''
实时数据中心app运行情况
'''
import sys
import os
import sys
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)
import logging
import paho.mqtt.client as mqtt
from Application.Cfg.dir_cfg import model_config
from Tools.DB.mysql_scada import  scada_session
from Application.Models.power.appmonitor_r import AppMonitorR
from Tools.Utils.time_utils import timeUtils

LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
logging.basicConfig(filename='../../log/brokermqtt.log', level=logging.DEBUG, format=LOG_FORMAT)



def on_connect(client, userdata, flags, rc):
    '''连接成功回调'''
    print('Connected with result code '+str(rc))
    # 订阅主题
    client.subscribe("+/report/notify/broadcast/activity")
    
def on_message(client, userdata, msg):
    '''消息接收回调'''
    topic = msg.topic  # 订阅主题
    data = msg.payload  # 返回值
    msg_t = 'topic:%s'%topic
    msg_p = 'payload:%s'%data
    logging.info(msg_t)
    logging.info(msg_p)
    topics = topic.split('/')
    name = topics[0]
    print (name,eval(data)['state'])
    scada_session.add(AppMonitorR(name=name,status=eval(data)['state'],create_time=timeUtils.getNewTimeStr()))
    scada_session.commit()
    scada_session.close()
   
   

ip = str(model_config.get('broker', "ip"))
port = int(model_config.get('broker', "port"))
keepalive = int(model_config.get('broker', "keepalive"))

client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message

client.connect(ip, port, keepalive)
client.loop_forever()
