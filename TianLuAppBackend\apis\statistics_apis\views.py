import datetime
import os.path
import time
import json
import random
import decimal
import traceback
import uuid
import re

import ast
from django.core.paginator import Paginator
from django.db import transaction
from rest_framework import filters
from rest_framework.generics import GenericAPIView

from apis.statistics_apis.db_link import time_range_by_measure_lc, master_station_time_range_by_measure_lc, \
    time_range_by_dwd
from apis.statistics_apis.filters import CustomIncomeFilter
from apis.statistics_apis.forms import UploadFileForm
from apis.statistics_apis.main import *
from apis.statistics_apis.models import UploadedFile

from apis.user.models import PointType, PointMeasure, PointStatus
from common.database_pools import dwd_db_tool, dwd_tables, ads_db_tool
from encryption.MD5 import MD5Tool
from tools.get_ac_http import ChargeDischargeType, classification, new_get_unit_t_report_data, new_get_station_t_report_data
import requests
from django_redis import get_redis_connection
import paho.mqtt.client as mqtt
from apis.user import models
from django.db.models import Sum, Q
from rest_framework.response import Response
from rest_framework.views import APIView
from common import common_response_code
from encryption.AES_symmetric_encryption import EncryptDate
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from serializers import monitor_serializers, income_serializers
from tools.aly_send_smscode import Sample
from django.conf import settings
from tools.get_ac_http import get_type, SocV3
from settings.meter_settings import METER_DIC
from tools.minio_tool import MinioTool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


class ElectricityWeekCountView(APIView):
    """历史 7 天冲放电量"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.PowerPlanHistorySerializer(data=request.data)
        if not ser.is_valid():
            error_log.error(f"历史 7 天冲放电量:字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        unit_english_name = ser.validated_data.get("unit_name")
        if unit_english_name:
            unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
            station_english_name = unit_instance.station.english_name

            select_sql = ("SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
                          " where station=%s and station_type=2 and unit_name=%s and day=%s")

            # 获取当前时间
            today = datetime.date.today()
            detail = []
            for i in range(1, 8):
                return_dic = {}
                # 使用timedelta计算前一天日期
                target_day = today - datetime.timedelta(days=i)

                # 查询小时冻结表
                # day_reports = models.FReport.objects.filter(day=target_day.strftime('%Y-%m-%d'), station=station_english_name, station_type=2, unit_name=unit_instance.bms).all()
                #
                # if day_reports.exists():
                #     return_dic["charge"] = round(sum([float(day_report.chag) for day_report in day_reports]), 2)
                #     return_dic["discharge"] = round(sum([float(day_report.disg) for day_report in day_reports]), 2)
                #     return_dic["month"] = target_day.strftime('%Y-%m-%d')
                #     detail.append(return_dic)

                # 查询 ads_report_chag_disg_1d
                result = ads_db_tool.select_one(select_sql, station_english_name, unit_instance.bms, target_day.strftime('%Y-%m-%d'))
                if result:
                    return_dic = {"charge": result['charge'], "discharge": result['discharge'],
                                  "month": target_day.strftime('%Y-%m-%d')}

                    detail.append(return_dic)

            detail.reverse()
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "unit_name": unit_instance.unit_name,
                        "detail": detail,
                    },
                }
            )
        else:
            station = ser.validated_data.get("station")
            try:
                master_station = models.MaterStation.objects.filter(english_name=station, is_delete=0).first()
            except Exception as e:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"查询{station}的信息失败！"},
                    }
                )
            # station_inst = models.StationDetails.objects.filter(english_name=station).first()
            detail = []

            select_sql = ("SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
                          " where station=%s and station_type=1 and day=%s")

            slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
            if slave_stations.exists():
                today = datetime.date.today()
                for i in range(1, 8):
                    # 使用timedelta计算前一天日期
                    target_day = today - datetime.timedelta(days=i)
                    return_dic = {'charge': 0, 'discharge': 0}
                    for station_inst in slave_stations:
                        if station_inst.slave != 0:
                            # return_dic = {'charge': 0, 'discharge': 0}

                            # 查询小时冻结表
                            # day_reports = models.FReport.objects.filter(day=target_day.strftime('%Y-%m-%d'),
                            #                                             station=station_inst.english_name, station_type=1).all()
                            #
                            # if day_reports.exists():
                            #     return_dic["charge"] += round(sum([float(day_report.chag) for day_report in day_reports]), 2)
                            #     return_dic["discharge"] += round(sum([float(day_report.disg) for day_report in day_reports]), 2)

                            # 查询 ads_report_chag_disg_1d
                            result = ads_db_tool.select_one(select_sql, station_inst.english_name,
                                                            target_day.strftime('%Y-%m-%d'))
                            if result:
                                return_dic["charge"] += round(float(result['charge']), 2)
                                return_dic["discharge"] += round(float(result['discharge']), 2)

                    return_dic["charge"] = round(return_dic["charge"], 2)
                    return_dic["discharge"] = round(return_dic["discharge"], 2)
                    return_dic["month"] = target_day.strftime('%Y-%m-%d')
                    detail.append(return_dic)

            detail.reverse()

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "station": ser.validated_data["station"],
                        "detail": detail,
                    },
                }
            )


class ElectricityDayCountView(APIView):
    """逐时冲放电量"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.ElectricityDayCountSerializer(data=request.data)

        if not ser.is_valid():
            error_log.error(f"逐时冲放电量:字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        types = ser.validated_data.get("type", None)
        date_time = ser.validated_data["time"]
        unit_english_name = ser.validated_data.get("unit_name")
        success_dic = {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        }
        if unit_english_name:

            select_sql = (
                "SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
                " where station=%s and station_type=2 and unit_name=%s and day=%s ORDER BY hour ASC")

            unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()

            detail_ins = models.PeakValley.objects.get(
                province=unit_instance.station.province,
                year_month=date_time.month,
                type=unit_instance.station.type,
                level=unit_instance.station.level,
            )

            # 查询小时冻结表
            # day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
            #                                             station=unit_instance.station.english_name, station_type=2,
            #                                             unit_name=unit_instance.bms).all()
            # if day_reports:
            #     detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
            #                "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for
            #               day_report in day_reports]
            #     for ind, i in enumerate(detail):
            #         i["type"] = getattr(detail_ins, f"pv{ind}")
            # else:
            #     detail = []

            # 查询 ads_report_chag_disg_data
            results = ads_db_tool.select_many(select_sql, unit_instance.station.english_name, unit_instance.bms,
                                                date_time.strftime('%Y-%m-%d'))
            if results:
                detail = [{"charge": float(day_report['charge']), "discharge": float(day_report['discharge']),
                           "soc": abs(float(day_report['soc'])), "time": f"{day_report['hour']}:00:00"} for
                          day_report in results]
                for ind, i in enumerate(detail):
                    i["type"] = getattr(detail_ins, f"pv{ind}")
            else:
                detail = []

            success_dic["data"]["unit"] = ser.validated_data["unit_name"]
            success_dic["data"]["detail"] = detail

        else:
            station = ser.validated_data.get("station")

            select_sql = (
                "SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
                " where station=%s and station_type=1 and day=%s ORDER BY hour ASC")

            try:
                master_station = models.MaterStation.objects.filter(english_name=station, is_delete=0).first()
            except Exception as e:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"查询{station}的信息失败！"},
                    }
                )

            slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
            detail = []
            if slave_stations.exists():
                temp_array = []
                for station_inst in slave_stations:
                    if station_inst.slave != 0:

                        detail_ins = models.PeakValley.objects.get(
                            province=station_inst.province,
                            year_month=date_time.month,
                            type=station_inst.type,
                            level=station_inst.level
                        )

                        # 查询小时冻结表
                        # day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
                        #                                             station=station_inst.english_name,
                        #                                             station_type=1).all()
                        # if day_reports:
                        #     temp_detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
                        #                     "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for
                        #                    day_report in day_reports]
                        #     for ind, i in enumerate(temp_detail):
                        #         i["type"] = getattr(detail_ins, f"pv{ind}")
                        #     temp_array.append(temp_detail)

                        # 查询 ads_report_chag_disg_data
                        results = ads_db_tool.select_many(select_sql, station_inst.english_name,
                                                          date_time.strftime('%Y-%m-%d'))
                        if results:
                            temp_detail = [
                                {"charge": float(day_report['charge']), "discharge": float(day_report['discharge']),
                                 "soc": abs(float(day_report['soc'])), "time": f"{day_report['hour']}:00:00"} for
                                day_report in results]
                            for ind, i in enumerate(temp_detail):
                                i["type"] = getattr(detail_ins, f"pv{ind}")

                            temp_array.append(temp_detail)

                if len(temp_array):
                    for ind, item in enumerate(temp_array):
                        if ind == 0:
                            detail = item
                        else:
                            for index, i in enumerate(item):
                                detail[index]['charge'] += i['charge']
                                detail[index]['discharge'] += i['discharge']
                                detail[index]['soc'] += round(i['soc'], 2)

            for j in detail:
                j['soc'] = round(j['soc'], 1)

            success_dic["data"]["station"] = ser.validated_data["station"]
            success_dic["data"]["detail"] = detail

        if types:
            success_dic["data"]["detail"] = get_type(types, success_dic["data"]["detail"])
        return Response(success_dic)


class ElectricityTypeCountView(APIView):
    """冲放电量统计"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.ElectricityTypeCountSerializer(data=request.data)

        if not ser.is_valid():
            error_log.error(f"逐时冲放电量:字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        types = ser.validated_data.get("type", None)
        date_time = ser.validated_data["time"]
        unit_english_name = ser.validated_data.get("unit")
        count_ins = ChargeDischargeType(date_time)
        success_dic = {
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        }
        if unit_english_name:
            unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()

            detail = new_get_unit_t_report_data(unit_instance, date_time)

            success_dic["data"]["unit"] = ser.validated_data["unit"]
            success_dic["data"]["detail"] = classification(detail) if detail else detail

        else:
            station = ser.validated_data.get("station")
            # station_inst = models.StationDetails.objects.filter(english_name=station).first()
            # detail = count_ins.get_stations(station_inst)
            try:
                master_station = models.MaterStation.objects.filter(english_name=station, is_delete=0).first()
            except Exception as e:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"查询{station}的信息失败！"},
                    }
                )

            detail = new_get_station_t_report_data(master_station, date_time)

            success_dic["data"]["station"] = ser.validated_data["station"]
            success_dic["data"]["detail"] = classification(detail) if detail else detail

        return Response(success_dic)


class DayPowerView(APIView):
    """历史功率"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.PowerPlanHistorySerializer(data=request.data)
        import time

        if not ser.is_valid():
            error_log.error("历史功率:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        unit_english_name = ser.validated_data.get("unit_name")
        if unit_english_name:
            today = ser.validated_data.get("time", None)
            today_str = today.strftime("%Y-%m-%d") if today else datetime.date.today().strftime("%Y-%m-%d")
            unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
            station_english_name = unit_instance.station.english_name
            station_app = unit_instance.station.app
            pcs = unit_instance.pcs

            # # 获取当前时间
            # if not today:
            #     now = str(int(time.time()))
            # today_str =
            #
            # url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
            # post_json = {
            #     "time": now,
            #     "datatype": "measure",
            #     "app": station_app,
            #     "station": station_english_name,
            #     "body": [{"device": pcs, "body": ["P", "Q"]}],
            # }  # 充电量  # 放电量
            # response = requests.post(url=url, json=post_json)
            # datas = response.json()["datas"]
            # if not datas:
            #     return_detail_list = []
            # else:
            #     return_detail_list = []
            #     new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
            #     for i in range(len(new_list_)):
            #         return_dic = {}
            #         try:
            #             return_dic["active_power"] = new_list_[i]["body"]["data"][0]["P"]  # 有功功率
            #         except:
            #             success_log.info("历史功率: active_power 数据不存在 active_power 默认为0")
            #             return_dic["active_power"] = "0"
            #         try:
            #             return_dic["reactive_power"] = new_list_[i]["body"]["data"][0]["Q"]  # 无功功率
            #         except:
            #             success_log.info("历史功率: reactive_power 数据不存在 reactive_power 默认为0")
            #             return_dic["reactive_power"] = "0"
            #
            #         time_stamp = time.localtime(int(new_list_[i]["body"]["time"]))
            #         return_dic["time"] = time.strftime("%Y-%m-%d %H:%M:%S", time_stamp)
            #
            #         return_detail_list.append(return_dic)

            select_sql = f'select p, q, time, station_name from {dwd_tables["measure"]["pcs"]} where station_name=%s and device=%s and time >= %s and time <= %s order by time ASC'

            results = dwd_db_tool.select_many(select_sql, *(station_english_name, pcs,
                                                           today_str + ' 00:00:00', today_str + ' 23:59:59'))
            return_detail_list = []
            if results:
                print(411, results)
                for i in results:
                    temp_dict = {
                        "time": i['time'].strftime("%Y-%m-%d %H:%M:%S"),
                        "active_power": float(i['p']),
                        "reactive_power": float(i['q'])
                    }
                    return_detail_list.append(temp_dict)

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "unit_name": unit_instance.unit_name,
                        "detail": return_detail_list[0:],
                    },
                }
            )
        else:
            today = ser.validated_data.get("time", None)
            today_str = today.strftime("%Y-%m-%d") if today else datetime.date.today().strftime("%Y-%m-%d")
            station = ser.validated_data.get("station")
            # station_inst = models.StationDetails.objects.filter(english_name=station).first()
            # units_inst = models.Unit.objects.filter(is_delete=0, station=station_inst).all()
            master_stations = models.MaterStation.objects.filter(english_name=station, is_delete=0)
            if master_stations.exists():
                master_station = master_stations.first()
            else:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"查询{station}的信息失败！"},
                    }
                )
            slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
            units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()

            return_detail_list = []

            for unit in units:
                station_english_name = unit.station.english_name
                station_app = unit.station.app
                pcs = unit.pcs
                # # 获取当前时间
                # if not today:
                #     now = str(int(time.time()))
                #
                # url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
                # post_json = {
                #     "time": now,
                #     "datatype": "measure",
                #     "app": station_app,
                #     "station": station_english_name,
                #     "body": [{"device": pcs, "body": ["P", "Q"]}],
                # }  # 充电量  # 放电量
                # response = requests.post(url=url, json=post_json)
                # datas = response.json()["datas"]
                # if not datas:
                #     return_detail_list = []
                # else:
                #     new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
                #     for i in range(len(new_list_)):
                #         try:
                #             P = new_list_[i]["body"]["data"][0]["P"]
                #         except:
                #             success_log.info("历史功率: active_power 数据不存在 active_power 默认为0")
                #             P = "0"
                #         try:
                #             Q = new_list_[i]["body"]["data"][0]["Q"]
                #         except:
                #             success_log.info("历史功率: reactive_power 数据不存在 reactive_power 默认为0")
                #             Q = "0"
                #         try:
                #             return_detail_list[i]["active_power"] += decimal.Decimal(P)
                #             return_detail_list[i]["reactive_power"] += decimal.Decimal(Q)
                #         except Exception as e:
                #             time_stamp = time.localtime(int(new_list_[i]["body"]["time"]))
                #             return_detail_list.append(
                #                 {
                #                     "active_power": decimal.Decimal(P),
                #                     "reactive_power": decimal.Decimal(Q),
                #                     "time": time.strftime("%Y-%m-%d %H:%M:%S", time_stamp),
                #                 }
                #             )

                select_sql = f'select p, q, time, station_name from {dwd_tables["measure"]["pcs"]} where station_name=%s and device=%s and time >= %s and time <= %s order by time ASC'

                results = dwd_db_tool.select_many(select_sql, *(station_english_name, pcs,
                                                               today_str + ' 00:00:00', today_str + ' 23:59:59'))
                if results:
                    for i in range(0, len(results)):
                        try:
                            return_detail_list[i]["active_power"] += float(results[i]['p'])
                            return_detail_list[i]["reactive_power"] += float(results[i]['q'])
                        except:
                            return_detail_list.append(
                                {
                                    "active_power": float(results[i]['p']),
                                    "reactive_power": float(results[i]['q']),
                                    "time": results[i]['time'].strftime("%Y-%m-%d %H:%M:%S"),
                                }
                            )

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "station": ser.validated_data["station"],
                        "detail": return_detail_list,
                    },
                }
            )


class LoadDayView(APIView):
    """负荷功率"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.LoadDaySerializer(data=request.data)

        if not ser.is_valid():
            error_log.error("负荷功率:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        station_english_name = ser.validated_data.get("station")
        today = ser.validated_data.get("time", None)
        today_str = today.strftime("%Y-%m-%d") if today else datetime.datetime.now().strftime("%Y-%m-%d")

        # now = str(int(time.mktime(today.timetuple()))) if today else str(int(time.time()))

        # station_instance = models.StationDetails.objects.filter(english_name=station_english_name).first()
        master_stations = models.MaterStation.objects.filter(english_name=station_english_name, is_delete=0)
        if master_stations.exists():
            master_station = master_stations.first()
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"查询{station_english_name}的信息失败！"},
                }
            )

        # slave_stations = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=0) | Q(slave=-1))

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        slave_stations = master_station.stationdetails_set.filter(is_delete=0).filter(english_name=master_station.enlish_name)

        if slave_stations.exists:
            slave_station = slave_stations.first()
            # url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
            # post_json = {
            #     "time": now,
            #     "datatype": "measure",
            #     "app": slave_station.app,
            #     "station": slave_station.english_name,
            #     "body": [
            #         {
            #             "device": "EMS",
            #             "body": [
            #                 "PCC",
            #             ],
            #         }
            #     ],
            # }
            # response = requests.post(url=url, json=post_json)
            # datas = response.json()["datas"]
            # if not datas:
            #     return_detail_list = []
            # elif "PCC" not in datas[0]["body"]["data"][0].keys():
            #     return_detail_list = []
            # else:
            #     return_detail_list = []
            #     new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
            #     for i in range(len(new_list_)):
            #         return_dic = {}
            #         try:
            #             return_dic["load"] = new_list_[i]["body"]["data"][0]["PCC"]  # 有功功率
            #         except:
            #             success_log.info("负荷功率: active_power 数据不存在 active_power 默认为0")
            #             return_dic["load"] = None
            #
            #         time_stamp = time.localtime(int(new_list_[i]["body"]["time"]))
            #         return_dic["time"] = time.strftime("%Y-%m-%d %H:%M:%S", time_stamp)
            #
            #         return_detail_list.append(return_dic)

            select_sql = f'select pcc, time, station_name from {dwd_tables["measure"]["ems"]} where station_name=%s and device=%s and time >= %s and time <= %s order by time ASC'

            results = dwd_db_tool.select_many(select_sql, *(slave_station.english_name, 'EMS',
                                                            today_str + ' 00:00:00', today_str + ' 23:59:59'))
            return_detail_list = []
            if results:
                for i in range(0, len(results)):
                    return_detail_list.append(
                        {
                            "load": float(results[i]['pcc']),
                            "time": results[i]['time'].strftime("%Y-%m-%d %H:%M:%S"),
                        }
                    )

        else:
            return_detail_list = []

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "station": station_english_name,
                    "detail": return_detail_list[0:],
                },
            }
        )


# class SocHistoryV2View(APIView):
#     """历史 SOC差值版本"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         import time
#
#         ser = monitor_serializers.PowerPlanHistorySerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("历史 SOC:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         today = ser.validated_data.get("time", None)
#         today_str = today.strftime("%Y-%m-%d") if today else datetime.datetime.now().strftime("%Y-%m-%d")
#
#         types = ser.validated_data.get("type", None)
#         unit_english_name = ser.validated_data.get("unit_name")
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             station_english_name = unit_instance.station.english_name
#
#             province_ins = unit_instance.station.province
#
#             station_app = unit_instance.station.app
#             bms = unit_instance.bms
#
#             detail_ins = models.PeakValley.objects.filter(province=province_ins, year_month=today.month).first()
#             # url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#             # post_json = {
#             #     "time": now,
#             #     "datatype": "measure",
#             #     "app": station_app,
#             #     "station": station_english_name,
#             #     "body": [
#             #         {
#             #             "device": bms,
#             #             "body": [
#             #                 "SOC",  # SOC
#             #             ],
#             #         }
#             #     ],
#             # }
#
#             # response = requests.post(url=url, json=post_json)
#             # datas = response.json()["datas"]
#
#             # if not datas:
#             #     return_detail_list = []
#             # else:
#             #     return_detail_list = []
#             #     new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#             #     signal = 0
#             #     for i in range(0, len(new_list_), 2):
#             #         hour = new_list_[i]["timeHour"]
#             #         history = GetHttpHistory(hour).v2_history_max_soc(str(int(hour) + 60 * 60), station_app,
#             #                                                           station_english_name)
#             #         return_dic = {}
#             #         return_dic["type"] = getattr(detail_ins, f"pv{signal}")
#             #         return_dic["peak"] = history
#             #         try:
#             #             return_dic["SOC"] = decimal.Decimal(new_list_[i]["body"]["data"][0]["SOC"])
#             #             return_dic["diff"] = int(new_list_[i + 1]["body"]["data"][0]["SOC"]) - int(
#             #                 new_list_[i]["body"]["data"][0]["SOC"]
#             #             )  # soc
#             #         except Exception as e:
#             #             success_log.info("历史 SOC: SOC 数据不存在 SOC 默认为0")
#             #             return_dic["SOC"] = "0"
#             #             return_dic["diff"] = "0"
#             #         time_stamp = time.localtime(int(new_list_[i]["body"]["time"]))
#             #
#             #         return_dic["time"] = time.strftime("%Y-%m-%d %H:%M:%S", time_stamp)
#             #         return_detail_list.append(return_dic)
#             #         signal += 1
#
#             select_sql = f'select soc, time, station_name from {dwd_tables["measure"]["bms_3"]} where station_name=%s and device=%s and time like %s order by time ASC'
#
#             results = dwd_db_tool.select_many(select_sql, *(station_english_name, bms,
#                                                             '%' + today_str + '%'))
#             return_detail_list = []
#             if results:
#                 for i in range(0, len(results)):
#                     signal = results[i]['time'].hour
#                     return_detail_list.append(
#                         {
#                             "SOC": float(results[i]['soc']),
#                             "time": results[i]['time'].strftime("%Y-%m-%d %H:%M:%S"),
#                             "diff": results[i + 1]["soc"] - results[i]["soc"],
#                             "peak": 0,
#                             "type": getattr(detail_ins, f"pv{signal}"),
#                         }
#                     )
#
#             if types:
#                 return_detail_list = get_type(types, return_detail_list)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": unit_instance.unit_name,
#                         "detail": return_detail_list,
#                     },
#                 }
#             )
#         else:
#             station = ser.validated_data.get("station")
#             # station_ins = models.StationDetails.objects.filter(english_name=station).first()
#
#             master_station = models.MaterStation.objects.filter(english_name=station, is_delete=0).first()
#             slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
#             return_detail_list = []
#             if slave_stations.exists():
#                 detail = []
#                 temp_array = []
#                 units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()
#                 if units.exists():
#
#                     today = ser.validated_data.get("time", None)
#                     if today:
#                         now = str(int(time.mktime(today.timetuple())))
#                         now_ = today
#
#                     else:
#                         # 获取当前时间
#                         now_ = datetime.datetime.now()
#                         now = str(int(time.time()))
#
#                     for unit in units:
#                         province_ins = unit.station.province
#                         detail_ins = models.PeakValley.objects.filter(province=province_ins,
#                                                                       year_month=now_.month).first()
#
#                         # station_english_name = unit.station.english_name
#                         # station_app = unit.station.app
#                         bms = unit.bms
#
#                         url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#                         post_json = {
#                             "time": now,
#                             "datatype": "measure",
#                             "app": unit.station.app,
#                             "station": unit.station.english_name,
#                             "body": [
#                                 {
#                                     "device": bms,
#                                     "body": [
#                                         "SOC",  # SOC
#                                     ],
#                                 }
#                             ],
#                         }
#                         response = requests.post(url=url, json=post_json)
#                         datas = response.json()["datas"]
#                         if not datas:
#                             return_detail_list = []
#                         else:
#                             new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#                             signal = 0
#                             for i in range(0, len(new_list_), 2):
#                                 hour = new_list_[i]["timeHour"]
#                                 history = GetHttpHistory(hour).v2_history_max_soc(str(int(hour) + 60 * 60), unit.station.app,
#                                                                                   unit.station.english_name)
#
#                                 try:
#                                     SOC = decimal.Decimal(new_list_[i]["body"]["data"][0]["SOC"])
#                                     diff = decimal.Decimal(new_list_[i + 1]["body"]["data"][0]["SOC"]) - decimal.Decimal(
#                                         new_list_[i]["body"]["data"][0]["SOC"]
#                                     )
#                                     # return_dic["SOC"] = new_list_[i]["body"]["data"][0]["SOC"]  # 有功功率
#                                 except:
#                                     success_log.info("历史 SOC: SOC 数据不存在 SOC 默认为0")
#                                     # return_dic["active_power"] = "0"
#                                     SOC = 0
#                                     diff = 0
#                                 try:
#                                     return_detail_list[i]["SOC"].append(SOC)
#                                     return_detail_list[i]["peak"].append(decimal.Decimal(history))
#                                     return_detail_list[i]["diff"].append(diff)
#                                 except Exception as e:
#                                     time_stamp = time.localtime(int(new_list_[i]["body"]["time"]))
#                                     return_detail_list.append(
#                                         {
#                                             "type": getattr(detail_ins, f"pv{signal}"),
#                                             "peak": [decimal.Decimal(history)],
#                                             "SOC": [
#                                                 SOC,
#                                             ],
#                                             "diff": [
#                                                 diff,
#                                             ],
#                                             "time": time.strftime("%Y-%m-%d %H:%M:%S", time_stamp),
#                                         }
#                                     )
#                                 signal += 1
#                     for i in range(len(return_detail_list)):
#                         # print('return_detail_list[i]["SOC"]', return_detail_list[i]["SOC"])
#                         # print('return_detail_list[i]["peak"]', return_detail_list[i]["peak"])
#                         # print('return_detail_list[i]["diff"]', return_detail_list[i]["diff"])
#
#                         return_detail_list[i]["SOC"] = decimal.Decimal(
#                             sum(return_detail_list[i]["SOC"]) / len(return_detail_list[i]["SOC"])
#                         ).quantize(decimal.Decimal("0.00"))
#                         return_detail_list[i]["peak"] = decimal.Decimal(
#                             sum(return_detail_list[i]["peak"]) / len(return_detail_list[i]["peak"])
#                         ).quantize(decimal.Decimal("0.00"))
#                         return_detail_list[i]["diff"] = decimal.Decimal(
#                             sum(return_detail_list[i]["diff"]) / len(return_detail_list[i]["diff"])
#                         ).quantize(decimal.Decimal("0.00"))
#                     if types:
#                         return_detail_list = get_type(types, return_detail_list)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "station": ser.validated_data["station"],
#                         "detail": return_detail_list,
#                     },
#                 }
#             )


# class SocHistoryView(APIView):
#     """历史 SOC"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.PowerPlanHistorySerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error(f"历史 SOC:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         date_time = ser.validated_data["time"]
#         unit_english_name = ser.validated_data.get("unit_name")
#         count_ins = SocV3(date_time)
#         success_dic = {
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#             },
#         }
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             detail = count_ins.get_unit(unit_instance)
#             success_dic["data"]["unit"] = ser.validated_data["unit_name"]
#             success_dic["data"]["detail"] = detail
#
#         else:
#             station = ser.validated_data.get("station")
#             # station_inst = models.StationDetails.objects.filter(english_name=station).first()
#             master_station = models.MaterStation.objects.filter(english_name=station, is_delete=0).first()
#             slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
#
#             detail = []
#             temp_array = []
#
#             for station in slave_stations:
#                 temp_detail = count_ins.get_stations(station)
#                 temp_array.append(temp_detail)
#
#             if len(temp_array):
#                 for ind, item in enumerate(temp_array):
#                     if not detail:
#                         detail = item
#                         for item_ in detail:
#                             item_['SOC'] = item_['SOC'] / len(temp_array)
#                     else:
#                         if item:
#                             for index, i in enumerate(item):
#                                 if index <= len(detail) - 1:
#                                     detail[index]['SOC'] += i['SOC']/len(temp_array)
#                                 else:
#                                     detail.append(i['SOC']/len(temp_array))
#
#             for i in detail:
#                 i['SOC'] = round(i['SOC'], 2)
#
#             success_dic["data"]["station"] = ser.validated_data["station"]
#             success_dic["data"]["detail"] = detail
#         return Response(success_dic)


class ReportCellView(APIView):
    """电芯报告"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        ser = monitor_serializers.StationSerializer(data=request.query_params)

        if not ser.is_valid():
            error_log.error(f"电芯报告:字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        # station_ins = models.StationDetails.objects.filter(english_name=ser.validated_data["station"]).first()
        master_stations = models.MaterStation.objects.filter(english_name=ser.validated_data["station"], is_delete=0)
        if master_stations.exists():
            master_station = master_stations.first()
            db = master_station.project.english_name
            from encryption.aes_cbc import AESUtil
            from django.conf import settings

            key_enc = settings.AES_REPORT_KEY_ENC
            iv_enc = settings.AES_REPORT_IV_ENC
            key_dec = settings.AES_REPORT_KEY_DEC
            iv_dec = settings.AES_REPORT_IV_DEC
            data = {"db": db, "workNet": ser.validated_data["station"]}
            res = AESUtil.encryt(json.dumps(data), key_enc, iv_enc)
            open_id = settings.MD5_OPENID
            md = str(res, encoding="utf-8") + str(open_id, encoding="utf-8")
            md_sale = MD5Tool.get_str_md5(md)
            header = {
                "Bean": str(md_sale),
            }
            try:
                result = requests.post(
                    "http://192.168.1.98:82/yc/HisData/getReportInfo", headers=header, timeout=10,
                    data={"data": str(res, encoding="utf-8")}
                )
                d_res = result.json()["data"]
                url = eval(AESUtil.decrypt(d_res, key_dec, iv_dec))[0]
            except:
                url = []

            success_dic = {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "ok",
                    "url": url,
                },
            }
            return Response(success_dic)
        else:
            success_dic = {
                "code": common_response_code.NO_DATA,
                "data": {
                    "message": "NO DATA",
                    "url": "",
                },
            }
            return Response(success_dic)


# class IncomeDetailsView(APIView):
#     """收益明细"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         query_dict = request.data
#         user_id = request.user["user_id"]
#         date_str = query_dict.get("date", None)
#         user_ins = models.UserDetails.objects.filter(id=user_id).first()
#         now = datetime.date.today()
#         if not date_str:
#             success_log.info("收益明细:无查询条件获取该年数据")
#             master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
#
#             months = now.month
#             detail_list = []
#             income_ins = models.StationIncome.objects.filter(master_station__in=master_stations)
#
#             for i in range(1, months + 1):
#                 return_dic = {
#                     "income_date__month": i,
#                     "income_date__year": now.year,
#                 }
#                 month_ins = income_ins.filter(income_date__month=i, income_date__year=now.year)
#
#                 day_ins = (
#                     month_ins.values("income_date")
#                     .annotate(
#                         demand_side_responses=Sum(
#                             "demand_side_response",
#                         ),
#                         peak_load_shiftings=Sum("peak_load_shifting"),
#                     )
#                     .values("income_date", "demand_side_responses", "peak_load_shiftings")
#                     .order_by("income_date")
#                 )
#                 return_dic["sum_month"] = 0
#                 return_dic["day"] = day_ins
#                 if day_ins:
#                     total_income_list = []
#                     for i in day_ins:
#                         total_income_list.append(i["peak_load_shiftings"])
#                         total_income_list.append(i["demand_side_responses"])
#                     return_dic["sum_month"] = sum(total_income_list)
#                     detail_list.append(return_dic)
#             detail_list.reverse()
#         else:
#             success_log.info("收益明细:条件查询获取数据")
#             new_query_dict = {}
#             detail_list = []
#             date = query_dict.get("date", None)
#             if date:
#                 if len(date) == 2:
#                     new_query_dict["income_date__month"] = date[1]
#                     new_query_dict["income_date__year"] = date[0]
#                 if len(date) == 1:
#                     new_query_dict["income_date__year"] = date[0]
#             income_type = query_dict.get("income_type", None)
#             # if income_type:
#             #     new_query_dict["income_type"] = income_type
#
#             master_station_english_name = query_dict.get("station_id__english_name")
#
#             # stations_ins = models.StationDetails.objects.filter(
#             #     userdetails=user_ins,
#             #     english_name__in=station_id__english_name,
#             # )
#
#             master_stations = models.MaterStation.objects.filter(userdetails=user_ins,
#                                                                  english_name__in=master_station_english_name, is_delete=0)
#
#             income_ins = (models.StationIncome.objects.filter(**new_query_dict).
#                           filter(master_station__in=master_stations))
#             if len(date) == 1:
#                 year = date[0]
#                 months = 12
#                 if year == now.year:
#                     months = now.month
#
#                 for i in range(1, months + 1):
#                     return_dic = {
#                         "income_date__month": i,
#                         "income_date__year": year,
#                     }
#                     month_ins = income_ins.filter(income_date__month=i, income_date__year=year)
#                     if not income_type:
#                         day_ins = (
#                             month_ins.values("income_date")
#                             .annotate(
#                                 demand_side_responses=Sum(
#                                     "demand_side_response",
#                                 ),
#                                 peak_load_shiftings=Sum("peak_load_shifting"),
#                             )
#                             .values(
#                                 "income_date",
#                                 "demand_side_responses",
#                                 "peak_load_shiftings",
#                             )
#                             .order_by("income_date")
#                         )
#                         return_dic["sum_month"] = 0
#                         return_dic["day"] = day_ins
#                         if day_ins:
#                             total_income_list = []
#                             for i in day_ins:
#                                 total_income_list.append(i["peak_load_shiftings"])
#                                 total_income_list.append(i["demand_side_responses"])
#                             return_dic["sum_month"] = sum(total_income_list)
#                             detail_list.append(return_dic)
#                     if income_type == 2:
#                         day_ins = (
#                             month_ins.values("income_date")
#                             .annotate(
#                                 demand_side_responses=Sum(
#                                     "demand_side_response",
#                                 )
#                             )
#                             .values("income_date", "demand_side_responses")
#                             .order_by("income_date")
#                         )
#                         return_dic["sum_month"] = 0
#                         return_dic["day"] = day_ins
#                         if day_ins:
#                             total_income_list = []
#                             for i in day_ins:
#                                 total_income_list.append(i["demand_side_responses"])
#                             return_dic["sum_month"] = sum(total_income_list)
#                             detail_list.append(return_dic)
#
#                     if income_type == 1:
#                         day_ins = (
#                             month_ins.values("income_date")
#                             .annotate(peak_load_shiftings=Sum("peak_load_shifting"))
#                             .values("income_date", "peak_load_shiftings")
#                             .order_by("income_date")
#                         )
#                         return_dic["sum_month"] = 0
#                         return_dic["day"] = day_ins
#                         if day_ins:
#                             total_income_list = []
#                             for i in day_ins:
#                                 total_income_list.append(i["peak_load_shiftings"])
#
#                             return_dic["sum_month"] = sum(total_income_list)
#                             detail_list.append(return_dic)
#                 detail_list.reverse()
#             if len(date) == 2:
#                 year = date[0]
#                 month = date[1]
#                 return_dic = {
#                     "income_date__month": month,
#                     "income_date__year": year,
#                 }
#                 month_ins = income_ins.filter(income_date__month=month, income_date__year=year)
#
#                 if not income_type:
#                     day_ins = (
#                         month_ins.values("income_date")
#                         .annotate(
#                             demand_side_responses=Sum(
#                                 "demand_side_response",
#                             ),
#                             peak_load_shiftings=Sum("peak_load_shifting"),
#                         )
#                         .values(
#                             "income_date",
#                             "demand_side_responses",
#                             "peak_load_shiftings",
#                         )
#                         .order_by("income_date")
#                     )
#                     return_dic["sum_month"] = 0
#                     return_dic["day"] = day_ins
#                     if day_ins:
#                         total_income_list = []
#                         for i in day_ins:
#                             total_income_list.append(i["peak_load_shiftings"])
#                             total_income_list.append(i["demand_side_responses"])
#                         return_dic["sum_month"] = sum(total_income_list)
#                         detail_list = return_dic
#                 if income_type == 1:
#                     day_ins = (
#                         month_ins.values("income_date")
#                         .annotate(peak_load_shiftings=Sum("peak_load_shifting"))
#                         .values("income_date", "peak_load_shiftings")
#                         .order_by("income_date")
#                     )
#                     return_dic["sum_month"] = 0
#                     return_dic["day"] = day_ins
#                     if day_ins:
#                         total_income_list = []
#                         for i in day_ins:
#                             total_income_list.append(i["peak_load_shiftings"])
#                         return_dic["sum_month"] = sum(total_income_list)
#                         detail_list = return_dic
#                 if income_type == 2:
#                     day_ins = (
#                         month_ins.values("income_date")
#                         .annotate(
#                             demand_side_responses=Sum(
#                                 "demand_side_response",
#                             )
#                         )
#                         .values("income_date", "demand_side_responses")
#                         .order_by("income_date")
#                     )
#                     return_dic["sum_month"] = 0
#                     return_dic["day"] = day_ins
#                     if day_ins:
#                         total_income_list = []
#                         for i in day_ins:
#                             total_income_list.append(i["demand_side_responses"])
#                         return_dic["sum_month"] = sum(total_income_list)
#                         detail_list = return_dic
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": detail_list},
#             }
#         )


# class IncomeAddView(APIView):
#     """添加收益明细"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = income_serializers.AddIncomesSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("添加收益明细:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         datas = ser.validated_data["data"]
#
#         for data in datas:
#             english_name = data["english_name"]
#
#             master_stations = models.MaterStation.objects.filter(english_name=english_name, is_delete=0)
#             if master_stations.exists():
#                 master_station = master_stations.first()
#             else:
#                 return Response(
#                                     {
#                                         "code": common_response_code.NO_DATA,
#                                         "data": {"message": "success", "detail": f"(主)电站{english_name}不存在"},
#                                     }
#                                 )
#
#             notes = data.get("notes")
#             date = data["date"]
#             income_type = data["income_type"]
#             day_income = data["day_income"]
#
#             income_ins_exist = models.StationIncome.objects.filter(income_date=date,
#                                                                    master_station=master_station).exists()
#             if not income_ins_exist:
#                 if income_type == 1:
#                     models.StationIncome.objects.create(
#                         master_station=master_station,
#                         income_date=date,
#                         peak_load_shifting=day_income,
#                         notes=notes,
#                         record=1,
#                     )
#                 if income_type == 2:
#                     models.StationIncome.objects.create(
#                         master_station=master_station,
#                         income_date=date,
#                         demand_side_response=day_income,
#                         notes=notes,
#                         record=1,
#                     )
#             if income_ins_exist:
#                 if income_type == 1:
#                     models.StationIncome.objects.filter(master_station=master_station, income_date=date).update(
#                         peak_load_shifting=day_income, record=1
#                     )
#                 if income_type == 2:
#                     models.StationIncome.objects.filter(master_station=master_station, income_date=date).update(
#                         demand_side_response=day_income, record=1
#                     )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": "收益添加成功"},
#             }
#         )


# class AutomaticControlSendSmsView(APIView):
#     """自动控制模式发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("自动控制模式发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("自动控制模式发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         """目前没有接入第三方发短信程序"""
#         conn = get_redis_connection("default")
#         conn.set("auto" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class UserStrategyApplyView(APIView):
#     """新: 自动控制策略：下发"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["station_id"] = json.loads(request.body.decode()).get("station_id", None)
#         self.serializer_context["uid"] = uuid.uuid4()
#
#     def post(self, request):
#         ser = UserStrategyApplySerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！",
#                              "details": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#
#         # 查询用户信息
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("自动控制模式下发:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "手机号与当前登录用户名不符！"
#                     },
#                 }
#             )
#
#         # 查询站信息
#         station_id = self.serializer_context["station_id"]
#         master_station = models.MaterStation.objects.filter(id=station_id, is_delete=0).first()
#         # station_instance = models.StationDetails.objects.filter(id=station_id).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#         if not station_instance:
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 站信息不存在！"
#                     },
#                 }
#             )
#         station_app = station_instance.app
#         station_english_name = station_instance.english_name
#
#         # 查询策略信息
#         try:
#             strategy_ins = UserStrategy.objects.get(id=ser.validated_data.get('strategy_id'))
#         except Exception as e:
#             error_log.error("自动控制策略下发: 策略信息不存在：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 策略信息不存在！"
#                     },
#                 }
#             )
#
#         # 查询策略-分类
#         category_instances = strategy_ins.userstrategycategory_set.all()
#         if not len(category_instances):
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 策略-分类为空！"
#                     },
#                 }
#             )
#
#         temp_list = list()
#         for category_instance in category_instances:
#             category_months = category_instance.month_set.all()
#             for category_month in category_months:
#                 for i in range(0, 24):
#                     temp_dict1 = dict()
#                     temp_dict2 = dict()
#                     temp_dict1['M' + str(category_month.month_number) + 'H' + str(i+1) + 'FC']\
#                         = str(ast.literal_eval(category_instance.charge_config)[i])
#                     temp_dict1['type'] = 'parameter'
#                     temp_dict2['M' + str(category_month.month_number) + 'H' + str(i+1) + 'PC']\
#                         = str(int(ast.literal_eval(category_instance.rl_list)['RL' + str(i+1)])/100)
#                     temp_dict2['type'] = 'parameter'
#                     temp_list.append(temp_dict1)
#                     temp_list.append(temp_dict2)
#         temp_list.append(
#             {
#                 "LoadFollowTC": str(category_instances[0].is_follow),
#                 "type": "parameter",
#             }
#         )
#
#         # 准备下发策略
#         topic = f"req/database/parameter/{station_english_name}/{station_app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#
#         token = aes.encrypt(station_english_name)
#
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": "EMS",
#             "body": temp_list
#         }
#
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#         try:
#             client.publish(topic, json_message)
#             print("下发策略内容：", json_message)           # todo 无法真实下发调试，待测试或者上线再打开
#         except Exception as e:
#             error_log.error('自动控制策略下发: 失败: {}'.format(e))
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 下发失败！"
#                     },
#                 }
#             )
#
#         success_log.info("云端计划下发成功===")
#         success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
#
#         # 写入下发历史策略
#         # plan_id = ser.validated_data.pop("plan_id")
#         # plan_ins = models.UserAutomation.objects.get(id=plan_id)
#         # strategy_id = ser.validated_data.get("strategy_id")
#
#         models.Record.objects.create(
#             user=user_instance.id,
#             topic=topic,
#             message=message,
#             station=station_instance,
#             strategy=strategy_ins,
#         )
#         try:
#             ser.save()
#         except Exception as e:
#             print(e)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "控制策略下发成功.",
#                 },
#             }
#         )


# class AutomaticControlView(APIView):
#     """自动控制模式下发--下发"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["strategy"] = json.loads(request.body.decode()).get("strategy", None)
#         self.serializer_context["uid"] = uuid.uuid4()
#
#     def post(self, request):
#         ser = income_serializers.SummerAutomaticControlSerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("自动控制模式下发:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         storage_name = self.serializer_context["strategy"]
#         summer = ser.validated_data["summer"]
#         none_summer = ser.validated_data["none_summer"]
#         app = models.StationDetails.objects.filter(is_delete=0, english_name=storage_name).values("app").first()
#         app = app.get("app")
#         topic = f"req/database/parameter/{storage_name}/{app}"
#
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#         token = aes.encrypt(storage_name)
#
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": "EMS",
#             "body": [
#                 {
#                     "STSMonth": str(ser.validated_data["summer_start"]),
#                     "type": "parameter",
#                 },  # 云端夏季起始月份
#                 {
#                     "STTSMonth": str(ser.validated_data["summer_end"]),
#                     "type": "parameter",
#                 },  # 云端夏季截止月份
#                 {
#                     "LoadFollowTC": str(ser.validated_data["follow"]),
#                     "type": "parameter",
#                 },  # 负荷跟随
#                 {"TC1": str(summer[0]), "type": "parameter"},
#                 {"TC2": str(summer[1]), "type": "parameter"},
#                 {"TC3": str(summer[2]), "type": "parameter"},
#                 {"TC4": str(summer[3]), "type": "parameter"},
#                 {"TC5": str(summer[4]), "type": "parameter"},
#                 {"TC6": str(summer[5]), "type": "parameter"},
#                 {"TC7": str(summer[6]), "type": "parameter"},
#                 {"TC8": str(summer[7]), "type": "parameter"},
#                 {"TC9": str(summer[8]), "type": "parameter"},
#                 {"TC10": str(summer[9]), "type": "parameter"},
#                 {"TC11": str(summer[10]), "type": "parameter"},
#                 {"TC12": str(summer[11]), "type": "parameter"},
#                 {"TC13": str(summer[12]), "type": "parameter"},
#                 {"TC14": str(summer[13]), "type": "parameter"},
#                 {"TC15": str(summer[14]), "type": "parameter"},
#                 {"TC16": str(summer[15]), "type": "parameter"},
#                 {"TC17": str(summer[16]), "type": "parameter"},
#                 {"TC18": str(summer[17]), "type": "parameter"},
#                 {"TC19": str(summer[18]), "type": "parameter"},
#                 {"TC20": str(summer[19]), "type": "parameter"},
#                 {"TC21": str(summer[20]), "type": "parameter"},
#                 {"TC22": str(summer[21]), "type": "parameter"},
#                 {"TC23": str(summer[22]), "type": "parameter"},
#                 {"TC24": str(summer[23]), "type": "parameter"},
#                 {"TCW1": str(none_summer[0]), "type": "parameter"},
#                 {"TCW2": str(none_summer[1]), "type": "parameter"},
#                 {"TCW3": str(none_summer[2]), "type": "parameter"},
#                 {"TCW4": str(none_summer[3]), "type": "parameter"},
#                 {"TCW5": str(none_summer[4]), "type": "parameter"},
#                 {"TCW6": str(none_summer[5]), "type": "parameter"},
#                 {"TCW7": str(none_summer[6]), "type": "parameter"},
#                 {"TCW8": str(none_summer[7]), "type": "parameter"},
#                 {"TCW9": str(none_summer[8]), "type": "parameter"},
#                 {"TCW10": str(none_summer[9]), "type": "parameter"},
#                 {"TCW11": str(none_summer[10]), "type": "parameter"},
#                 {"TCW12": str(none_summer[11]), "type": "parameter"},
#                 {"TCW13": str(none_summer[12]), "type": "parameter"},
#                 {"TCW14": str(none_summer[13]), "type": "parameter"},
#                 {"TCW15": str(none_summer[14]), "type": "parameter"},
#                 {"TCW16": str(none_summer[15]), "type": "parameter"},
#                 {"TCW17": str(none_summer[16]), "type": "parameter"},
#                 {"TCW18": str(none_summer[17]), "type": "parameter"},
#                 {"TCW19": str(none_summer[18]), "type": "parameter"},
#                 {"TCW20": str(none_summer[19]), "type": "parameter"},
#                 {"TCW21": str(none_summer[20]), "type": "parameter"},
#                 {"TCW22": str(none_summer[21]), "type": "parameter"},
#                 {"TCW23": str(none_summer[22]), "type": "parameter"},
#                 {"TCW24": str(none_summer[23]), "type": "parameter"},
#             ],
#         }
#         rl_list = ser.validated_data.get("rl_list", None)
#         if rl_list:
#             for rl in rl_list:
#                 for k, v in rl.items():
#                     rl[k] = str(int(v) / 100)
#                 message["body"].append(rl)
#                 rl["type"] = "parameter"
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#
#         client.publish(topic, json_message)
#         success_log.info("云端计划下发成功===")
#         success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
#         station_ins = models.StationDetails.objects.get(english_name=storage_name)
#         plan_id = ser.validated_data.pop("plan_id")
#         plan_ins = models.UserAutomation.objects.get(id=plan_id)
#         models.Record.objects.create(
#             user=request.user["user_id"],
#             topic=topic,
#             message=message,
#             station=station_ins,
#             plan=plan_ins,
#         )
#         ser.save()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "控制策略下发成功",
#                 },
#             }
#         )


# class AutomaticPlanView(APIView):
#     """用户自动模式配置添加--废弃"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request):
#         """
#         添加新用户策略
#         :param request:
#         :return:
#         """
#         ser = income_serializers.PostAutomaticPlanAddSerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动模式配置添加:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         plan = ser.save()
#         e = plan.id
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "策略添加成功",
#                     "plan_id": e,
#                 },
#             }
#         )
#
#     def get(self, request):
#         """
#         获取用户所具有的策略
#         :param request:
#         :return:
#         """
#         query = request.query_params.get("query", None)
#         station = request.query_params.get("station", None)
#
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.get(id=user_id)
#         details = models.UserAutomation.objects.filter(
#             user=user_ins,
#             delete=0
#         )
#
#         if query:
#             details = models.UserAutomation.objects.filter(
#                 user=user_ins,
#                 name__contains=query,
#                 delete=0
#             )
#         for detail in details:
#             if detail.summer:
#                 detail.summer = eval(detail.summer)
#             if detail.none_summer:
#                 detail.none_summer = eval(detail.none_summer)
#             if detail.rl_list:
#                 detail.rl_list = eval(detail.rl_list)
#         ser = income_serializers.PostAutomaticPlanAddSerializer(instance=details, many=True)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": ser.data},
#             }
#         )
#
#     def put(self, request):
#         ser = income_serializers.PutAutomaticPlanAddSerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动模式配置添加:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
#         au_instance = models.UserAutomation.objects.filter(name=ser.validated_data["name"], user=user_ins)
#         if not au_instance:
#             error_log.error(f"用户自动模式配置添加:策略名不正确")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "策略名不存在"},
#                 }
#             )
#         ser.update(instance=au_instance, validated_data=ser.validated_data)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "策略更新成功",
#                     "plan_id": au_instance[0].id,
#                 },
#             }
#         )


# class UserStrategyView(APIView):
#     """新.用户自动模式配置：添加/修改/查询列表"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request):
#         """
#         添加新用户策略
#         """""
#         # try:
#         ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动模式配置添加: 字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         strategy = ser.save()
#         e = strategy.id
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动模式配置: 添加成功",
#                     "strategy_id": e,
#                 },
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动模式配置: 添加报错：{}".format(e.args))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动模式配置：添加失败!'},
#         #         }
#         #     )
#
#     def get(self, request):
#         """
#         获取用户所具有的策略
#         """""
#         try:
#             query = request.query_params.get("query", None)
#             station = request.query_params.get("station", None)
#
#             user_id = request.user["user_id"]
#             user_ins = models.UserDetails.objects.get(id=user_id)
#             details = UserStrategy.objects.filter(
#                 user=user_ins,
#                 is_delete=0
#             )
#
#             if query:
#                 details = details.filter(
#                     name__contains=query
#                 )
#
#             for detail in details:
#                 months_count = detail.month_set.filter(is_valid=0).count()
#                 detail = detail.__dict__
#                 detail['status'] = 0
#                 if months_count == 12:
#                     detail['is_show'] = 1
#                 else:
#                     detail['is_show'] = 0
#             if station:
#                 record = models.Record.objects.filter(user=user_id, station=station).order_by('-id').first()
#                 if record:
#                     for detail in details:
#                         detail = detail.__dict__
#                         if record.strategy and detail.get('id') == record.strategy.id:
#                             detail['status'] = 1
#                         else:
#                             detail['status'] = 0
#
#             ser = ResponseUserStrategySerializer(instance=details, many=True)
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": ser.data},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动模式配置: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动模式配置：查询失败!'},
#                 }
#             )
#
#     def put(self, request, pk):
#         """
#         更新用户策略
#         """""
#         try:
#             ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
#             if not ser.is_valid():
#                 error_log.error(f"用户自动模式配置添加:字段校验失败{ser.errors}")
#
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": ser.errors},
#                     }
#                 )
#             user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
#             au_instance = UserStrategy.objects.filter(id=pk, user=user_ins)
#             if not au_instance:
#                 error_log.error(f"用户自动模式配置更新:策略不存在")
#
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": "用户自动模式配置更新:策略不存在."},
#                     }
#                 )
#             ser.update(instance=au_instance, validated_data=ser.validated_data)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "用户自动模式配置更新: 更新成功",
#                         "strategy_id": au_instance[0].id,
#                     },
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动模式配置: 更新报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动模式配置：更新失败!'},
#                 }
#             )


# class UserStrategyDeleteView(APIView):
#     """新.用户自动模式配置：删除"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         try:
#             obj = UserStrategy.objects.get(id=pk)
#             obj.is_delete = 1
#             obj.save()
#
#             # 删除策略的月份
#             month_instances = Month.objects.filter(strategy=obj)
#             for month_instance in month_instances:
#                 month_instance.delete()
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": '用户自动模式配置删除：删除成功'},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动模式配置删除: 删除报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '模式配置删除：删除失败!'},
#                 }
#             )


# class UserStrategyCheckMonthView(APIView):
#     """新.用户自动模式配置：校验策略的所有月份是否均配置"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def get(self, request, pk):
#         try:
#             strategy_ins = UserStrategy.objects.get(id=pk)
#             months = Month.objects.filter(strategy=strategy_ins).all()
#
#             valid_months = [month.month_number for month in months if month.is_valid]
#             no_valid_months = [month.month_number for month in months if not month.is_valid]
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": {"valid_months": valid_months, "no_valid_months": no_valid_months,
#                                    "count": len(valid_months)},
#                     },
#                 }
#             )
#
#         except Exception as e:
#             error_log.error("策略月份校验：校验报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '策略月份校验：校验失败!'},
#                 }
#             )


# class UserStrategyCategoryView(APIView):
#     """新.用户自动模式配置-分类：添加/修改/查询列表"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request):
#         """
#         添加新用户策略-分类
#         """""
#         # try:
#         ser = UserStrategyCategorySerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动控制策略-分类: 字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.get(id=user_id)
#         strategy = UserStrategy.objects.filter(id=ser.validated_data.get('strategy_id'), user=user_ins)
#         if not strategy:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-分类：控制策略不存在!'},
#                 }
#             )
#
#         category = ser.save()
#         e = category.id
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-分类：添加成功",
#                     "category_id": e,
#                 },
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-分类: 添加报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-分类：添加失败!'},
#         #         }
#         #     )
#
#     def get(self, request, strategy_id):
#         """
#         获取策略所具有的分类列表
#         """""
#         # try:
#         #     query = request.query_params.get("query", None)
#             # station = request.query_params.get("station", None)
#
#         try:
#             strategy_ins = UserStrategy.objects.get(id=strategy_id)
#         except Exception as e:
#             error_log.error("用户自动控制策略-分类: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-分类：控制策略不存在!'},
#                 }
#             )
#
#         category_instances = strategy_ins.userstrategycategory_set.filter(is_delete=0).all()
#
#         temp_detail = dict()
#         for detail in category_instances:
#             month_instances = detail.month_set.all()
#             months = [month.month_number for month in month_instances]
#             temp_detail[detail.id] = months
#
#             if detail.charge_config:
#                 detail.charge_config = eval(detail.charge_config)
#             if detail.rl_list:
#                 detail.rl_list = eval(detail.rl_list)
#
#         ser = UserStrategyCategorySerializer(instance=category_instances, many=True)
#
#         details = ser.data
#         for detail in details:
#             detail['months'] = temp_detail[detail['id']]
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": details},
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-分类: 查询报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-分类：查询失败!'},
#         #         }
#         #     )
#
#     def put(self, request, pk):
#         # try:
#         try:
#             category_instance = UserStrategyCategory.objects.get(id=pk)
#         except Exception as e:
#             error_log.error("用户自动控制策略-分类:资源不存在: {}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-分类: 资源不存在."},
#                 }
#             )
#         ser = UpdateUserStrategyCategorySerializer(category_instance, data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动控制策略-分类:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         ser.save()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-分类: 更新成功",
#                     "category_id": category_instance.id,
#                 },
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-分类: 更新报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-分类：更新失败!'},
#         #         }
#         #     )


# class UserStrategyCategoryUpdateMonthView(APIView):
#     """新.用户自动模式配置-分类：单独修改月份"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         """
#         添加新用户策略-分类
#         """""
#         try:
#             category_instance = UserStrategyCategory.objects.get(id=pk)
#         except Exception as e:
#             error_log.error("用户自动控制策略-分类:资源不存在: {}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-分类: 资源不存在."},
#                 }
#             )
#
#         months = request.data.get('months')
#         if not months:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-分类: 缺失参数：months."},
#                 }
#             )
#
#         strategy = UserStrategy.objects.get(id=category_instance.strategy_id)
#         all_valid_strategy_months = Month.objects.filter(strategy=strategy, is_valid=1).all()
#         old_cate_months = Month.objects.filter(user_Strategy_Category=category_instance).all()
#         old_cate_months_numbers = [valid_month.month_number for valid_month in old_cate_months]
#         all_valid_strategy_months_numbers = [valid_month.month_number for valid_month in all_valid_strategy_months]
#
#         for month in months:
#             if month not in all_valid_strategy_months_numbers and month not in old_cate_months_numbers:
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": "用户自动控制策略-分类：{}月份已被其他分类选择".format(month)},
#                     }
#                 )
#
#         # 保存月份
#         all_strategy_months = Month.objects.filter(strategy=strategy).all()
#         for month in all_strategy_months:
#             if month.month_number in months:
#                 month.is_valid = False
#                 month.user_Strategy_Category = category_instance
#                 month.save()
#         for month_ in old_cate_months:
#             if month_.month_number not in months:
#                 month_.is_valid = True
#                 month_.user_Strategy_Category = None
#                 month_.save()
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-分类: 月份更新成功",
#                     "category_id": category_instance.id,
#                 },
#             }
#         )


# class UserStrategyCategory2View(APIView):
#     """新.用户自动模式配置-分类：删除/查询详情"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         """
#         删除新用户策略-分类
#         """""
#         try:
#             obj = UserStrategyCategory.objects.get(id=pk)
#             obj.is_delete = 1
#             obj.save()
#             months = obj.month_set.all()
#             for month in months:
#                 month.is_valid = True
#                 month.save()
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": '用户自动控制策略-分类：删除成功'},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动控制策略-分类: 删除报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-分类：删除失败!'},
#                 }
#             )
#
#     def get(self, request, pk):
#         """
#         获取策略-分类详情
#         """""
#         try:
#             try:
#                 category_ins = UserStrategyCategory.objects.get(id=pk)
#             except Exception as e:
#                 error_log.error("用户自动控制策略-分类: 查询报错：{}".format(e))
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {"message": "fail", "detail": '用户自动控制策略-分类：不存在!'},
#                     }
#                 )
#
#             month_instances = category_ins.month_set.all()
#             months = [month.month_number for month in month_instances]
#
#             if category_ins.charge_config:
#                 category_ins.charge_config = eval(category_ins.charge_config)
#             if category_ins.rl_list:
#                 category_ins.rl_list = eval(category_ins.rl_list)
#
#             ser = UserStrategyCategorySerializer(instance=category_ins)
#             detail = ser.data
#             detail['months'] = months
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": detail},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动控制策略-分类: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-分类：查询详情失败!'},
#                 }
#             )


# class UserStrategySaveToOtherView(APIView):
#     """新.用户自动控制策略：另存为"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.conn = get_redis_connection("3")
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#
#         client = mtqq_station_strategy(station_id, month)
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = self.conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = self.conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     @transaction.atomic
#     def post(self, request, pk):
#         """
#         用户策略-另存为
#         """""
#         new_strategy_name = request.data.get('new_name')
#         type = int(request.data.get('type', 3))   # 1: 实时；2：默认，3：自定义
#         user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
#
#         strategy = UserStrategy.objects.filter(user=user_ins, name=new_strategy_name, is_delete=0)
#         if strategy.exists():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户控制策略另存为: 名称已存在"},
#                 }
#             )
#         try:
#             save_id = transaction.savepoint()
#             if type == 3:
#                 try:
#                     strategy_instance = UserStrategy.objects.get(id=pk)
#                 except Exception as e:
#                     error_log.error("用户控制策略另存为:策略不存在：{}".format(e))
#
#                     return Response(
#                         {
#                             "code": common_response_code.FIELD_ERROR,
#                             "data": {"message": "error", "detail": "用户控制策略另存为:策略不存在"},
#                         }
#                     )
#
#                 old_category_instances = strategy_instance.userstrategycategory_set.all()
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     Month.objects.create(strategy=new_strategy, month_number=i)
#
#                  # 创建新分类
#                 for old_category_instance in old_category_instances:
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name=old_category_instance.name,
#                                                                                 charge_config=old_category_instance.charge_config,
#                                                                                 is_follow=old_category_instance.is_follow,
#                                                                                 rl_list=old_category_instance.rl_list,
#                                                                                 pv0=old_category_instance.pv0,
#                                                                                 pv1=old_category_instance.pv1,
#                                                                                 pv2=old_category_instance.pv2,
#                                                                                 pv3=old_category_instance.pv3,
#                                                                                 pv4=old_category_instance.pv4,
#                                                                                 pv5=old_category_instance.pv5,
#                                                                                 pv6=old_category_instance.pv6,
#                                                                                 pv7=old_category_instance.pv7,
#                                                                                 pv8=old_category_instance.pv8,
#                                                                                 pv9=old_category_instance.pv9,
#                                                                                 pv10=old_category_instance.pv10,
#                                                                                 pv11=old_category_instance.pv11,
#                                                                                 pv12=old_category_instance.pv12,
#                                                                                 pv13=old_category_instance.pv13,
#                                                                                 pv14=old_category_instance.pv14,
#                                                                                 pv15=old_category_instance.pv15,
#                                                                                 pv16=old_category_instance.pv16,
#                                                                                 pv17=old_category_instance.pv17,
#                                                                                 pv18=old_category_instance.pv18,
#                                                                                 pv19=old_category_instance.pv19,
#                                                                                 pv20=old_category_instance.pv20,
#                                                                                 pv21=old_category_instance.pv21,
#                                                                                 pv22=old_category_instance.pv22,
#                                                                                 pv23=old_category_instance.pv23
#                                                                                 )
#                     new_category_instance.save()
#                     tem_months = old_category_instance.month_set.all()
#                     tem_months_numbers = [month.month_number for month in tem_months]
#                     for month in tem_months_numbers:
#                         month_ins = Month.objects.filter(strategy=new_strategy, month_number=month).first()
#                         month_ins.is_valid = False
#                         month_ins.user_Strategy_Category = new_category_instance
#                         month_ins.save()
#
#             elif type == 2:
#                 master_station = models.MaterStation.objects.filter(id=pk, is_delete=0).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#                 # station = models.StationDetails.objects.get(id=pk)
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#
#                 default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#                 default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#                 station_actic = models.StationActic.objects.filter(station_id=station.id).all()
#                 former_actic_ids = [i.former_actic_id for i in station_actic]
#                 former_actic = models.FormerActic.objects.filter(id__in=former_actic_ids).all()
#
#                 for info in former_actic:
#                     info = info.__dict__
#                     rl_list = dict()
#                     for key in default_month_rl_keys:
#                         rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(info.get(key, 0) / info.get('power') * 100, 2)
#                     charge_config = [int(info.get(key, 0)) for key in default_month_charge_keys]
#                     pv_list = self._get_pv_status(station.province, station.type, station.level, int(info.get('year_month').split('-')[-1]))
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name='月份{}'.format(info.get('year_month').split('-')[-1]),
#                                                                                 charge_config=charge_config,
#                                                                                 is_follow=1,
#                                                                                 rl_list=rl_list,
#                                                                                 pv0=pv_list[0],
#                                                                                 pv1=pv_list[1],
#                                                                                 pv2=pv_list[2],
#                                                                                 pv3=pv_list[3],
#                                                                                 pv4=pv_list[4],
#                                                                                 pv5=pv_list[5],
#                                                                                 pv6=pv_list[6],
#                                                                                 pv7=pv_list[7],
#                                                                                 pv8=pv_list[8],
#                                                                                 pv9=pv_list[9],
#                                                                                 pv10=pv_list[10],
#                                                                                 pv11=pv_list[11],
#                                                                                 pv12=pv_list[12],
#                                                                                 pv13=pv_list[13],
#                                                                                 pv14=pv_list[14],
#                                                                                 pv15=pv_list[15],
#                                                                                 pv16=pv_list[16],
#                                                                                 pv17=pv_list[17],
#                                                                                 pv18=pv_list[18],
#                                                                                 pv19=pv_list[19],
#                                                                                 pv20=pv_list[20],
#                                                                                 pv21=pv_list[21],
#                                                                                 pv22=pv_list[22],
#                                                                                 pv23=pv_list[23])
#                     new_category_instance.save()
#                     Month.objects.create(strategy=new_strategy, month_number=int(info.get('year_month').split('-')[-1]), is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#             elif type == 1:
#                 # station = models.StationDetails.objects.get(id=pk)
#                 master_station = models.MaterStation.objects.filter(id=pk, is_delete=0).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#
#                 conn = get_redis_connection("3")
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     datas = conn.get('{}-{}-mqtt'.format(station.english_name, i))
#                     data = {}
#                     current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#                     current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#                     rl_list = {'RL' + str(current_month_rl_keys.index(key) + 1): 0 for key in current_month_rl_keys}
#                     pv_list = self._get_pv_status(station.province, station.type, station.level, i)
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     if datas:
#                         datas = eval(datas)
#                         datas = datas.get('body')[0].get('body')
#                         for y in range(1, 25):
#                             data[f'RLH{y}F'] = datas.get(f'M{i}H{y}F', 0)
#                             data[f'RLH{y}P'] = datas.get(f'M{i}H{y}P', 0)
#                     else:
#                         data = self._get_data(station.id, station.english_name, i)
#                     charge_config = [int(data.get(key, 0)) for key in current_month_charge_keys]
#                     for key in current_month_rl_keys:
#                         rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(
#                             float(data.get(key, 0)) * 100, 2)
#
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name='月份{}'.format(i),
#                                                                                 charge_config=charge_config,
#                                                                                 is_follow=1,
#                                                                                 rl_list=rl_list,
#                                                                                 pv0=pv_list[0],
#                                                                                 pv1=pv_list[1],
#                                                                                 pv2=pv_list[2],
#                                                                                 pv3=pv_list[3],
#                                                                                 pv4=pv_list[4],
#                                                                                 pv5=pv_list[5],
#                                                                                 pv6=pv_list[6],
#                                                                                 pv7=pv_list[7],
#                                                                                 pv8=pv_list[8],
#                                                                                 pv9=pv_list[9],
#                                                                                 pv10=pv_list[10],
#                                                                                 pv11=pv_list[11],
#                                                                                 pv12=pv_list[12],
#                                                                                 pv13=pv_list[13],
#                                                                                 pv14=pv_list[14],
#                                                                                 pv15=pv_list[15],
#                                                                                 pv16=pv_list[16],
#                                                                                 pv17=pv_list[17],
#                                                                                 pv18=pv_list[18],
#                                                                                 pv19=pv_list[19],
#                                                                                 pv20=pv_list[20],
#                                                                                 pv21=pv_list[21],
#                                                                                 pv22=pv_list[22],
#                                                                                 pv23=pv_list[23]
#                                                                                 )
#                     new_category_instance.save()
#                     Month.objects.create(strategy=new_strategy, month_number=i,
#                                          is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "detail": "控制策略类型错误",
#                         },
#                     }
#                 )
#             transaction.savepoint_commit(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "用户自动控制策略: 另存为保存成功",
#                         "new_strategy_id": new_strategy.id
#                     },
#                 }
#             )
#         except Exception as e:
#             error_log.error("另存策略失败：{}".format(e))
#             transaction.savepoint_rollback(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {"message": "error", "detail": "另存失败"},
#                 }
#             )


# class AutomaticGetView(APIView):
#     """官方电价获取"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         query = request.query_params
#         ser = income_serializers.AutomaticGetSerializers(data=query)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         master_stations = models.MaterStation.objects.filter(english_name=ser.validated_data['station'], is_delete=0)
#         if master_stations.exists():
#             master_station = master_stations.first()
#             slave_station = master_station.stationdetails_set.filter(is_delete=0).first()
#             # province_ins = models.StationDetails.objects.filter(english_name=ser.validated_data["station"]).first().province
#             province_ins = slave_station.province
#
#             month = datetime.datetime.today().month
#             detail_ins = models.PeakValley.objects.filter(year_month=str(month), province=province_ins).first()
#             ser_res = income_serializers.AutomaticResSerializers(instance=detail_ins)
#             detail = ser_res.data
#         else:
#             detail = {}
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": detail},
#             }
#         )


# class AutomaticCurrentView(APIView):
#     """当前策略--废弃"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         query = request.query_params
#
#         ser = income_serializers.AutomaticGetSerializers(data=query)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station = ser.validated_data["station"]
#         station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
#         record = models.Record.objects.filter(station=station_ins).last()
#         if record:
#             plan_ins = record.plan
#             if plan_ins.summer:
#                 plan_ins.summer = eval(plan_ins.summer)
#             if plan_ins.none_summer:
#                 plan_ins.none_summer = eval(plan_ins.none_summer)
#             if plan_ins.rl_list:
#                 plan_ins.rl_list = eval(plan_ins.rl_list)
#             if int(plan_ins.user_id) != int(request.user["user_id"]):
#                 plan_ins.name = "当前策略"
#             else:
#                 plan_ins.name = f"当前策略:{plan_ins.name}"
#             ser = income_serializers.PutAutomaticPlanAddSerializer(instance=plan_ins)
#             data = ser.data
#         else:
#             data = {}
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": data},
#             }
#         )


# class CurrentStrategyView(APIView):
#     """新-获取当前策略"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         query = request.query_params
#
#         ser = CurrentStrategySerializers(data=query)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station = ser.validated_data["station"]
#         # station_ins = models.StationDetails.objects.filter(english_name=station).first()
#         master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#         record = models.Record.objects.filter(station=station_instance).last()
#         if record:
#             strategy_ins = record.strategy
#
#             if strategy_ins:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "success", "detail": {
#                             "strategy_id": strategy_ins.id,
#                             "strategy_name": strategy_ins.name
#                         }},
#                     }
#                 )
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "no strategy has apply!!!", "detail": "当前站无下发策略策略"},
#                     }
#                 )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "no record for strategy has apply!!!", "detail": "当前站无下发指令记录"},
#             }
#         )


# class AutomaticPlanDelView(APIView):
#     """用户自动模式配置添加 --废弃"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         automation_id = request.data['id']
#         obj = models.UserAutomation.objects.get(id=automation_id)
#         obj.delete = 1
#         obj.save()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": '删除成功'},
#             }
#         )


# class PowerActingView(APIView):
#     """当日有功功率、无功功率"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         from .db_link import time_range_by_dwd
#         ser = monitor_serializers.ElectricityTypeCountSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"当日有功功率、无功功率:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         unit_english_name = ser.validated_data.get("unit")
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             station_english_name = unit_instance.station.english_name
#
#             device_type = 'pcs'
#             device = getattr(unit_instance, device_type)
#             # 获取当前时间
#             time_str = ser.validated_data.get("time")
#             # 开始时间
#             start_time = str(time_str)
#             end_time = str(time_str).replace('00:00:00', '23:59:59')
#             start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#             end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#             result = time_range_by_dwd(station_english_name, 'measure', device_type, device,
#                                               start_datatime, end_datatime, 'P', 'Q')
#             if result == []:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": unit_instance.unit_name,
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "P": 0,
#                                     "Q": 0
#                                 }]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": unit_instance.unit_name,
#                         "detail": result,
#                     },
#                 }
#             )
#         else:
#             station = ser.validated_data.get("station")
#             time_str = ser.validated_data.get("time")
#             last_result = []
#             # station_inst = models.StationDetails.objects.filter(english_name=station).first()
#
#             master_stations = models.MaterStation.objects.filter(english_name=station, is_delete=0)
#             if master_stations.exists():
#
#                 master_station = master_stations.first()
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
#
#                 if slave_stations.exists():
#                     start_time = str(time_str)
#                     end_time = str(time_str).replace('00:00:00', '23:59:59')
#                     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#
#                     units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()
#                     if units.exists():
#                         for index, unit in enumerate(units):
#                             device_type = 'pcs'
#                             device = getattr(unit, device_type)
#                             results = time_range_by_dwd(unit.station.english_name, 'measure', device_type, device,
#                                                               start_datatime, end_datatime, 'P', 'Q')
#                             error_log.error(f'{results}')
#                             for idx, one in enumerate(results):
#                                 if index == 0:
#                                     last_result.append(one)
#                                 else:
#                                     if idx < len(last_result) and idx < len(results):
#                                         last_result[idx]['Q'] = round(last_result[idx]['Q'] + one['Q'], 2)
#                                         last_result[idx]['P'] = round(last_result[idx]['P'] + one['P'], 2)
#             if not last_result:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": ser.validated_data["station"],
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "P": 0,
#                                     "Q": 0
#                                 }]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "station": ser.validated_data["station"],
#                         "detail": last_result,
#                     },
#                 }
#             )


# class ThreePhaseCurrentView(APIView):
#     """当日三相电流"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.ElectricityTypeCountSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"当日三相电流:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         unit_english_name = ser.validated_data.get("unit")
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             station_english_name = unit_instance.station.english_name
#             station_app = unit_instance.station.app
#
#             # meter_type = unit_instance.station.meter_type
#             # meter_dic = METER_DIC.get(meter_type)
#             # device = meter_dic.get("device")
#             # 获取当前时间
#             time_str = ser.validated_data.get("time")
#             # 开始时间
#             start_time = str(time_str)
#             end_time = str(time_str).replace('00:00:00', '23:59:59')
#             start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#             end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#             # result = time_range_by_measure_bl(station_app, station_english_name, start_datatime, end_datatime, device)
#             device_type = 'pcs'
#             device = getattr(unit_instance, device_type)
#             result = time_range_by_dwd(station_english_name, 'measure', device_type, device,
#                                               start_datatime, end_datatime, 'Ia', 'Ib', 'Ic')
#             if result == []:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": unit_instance.unit_name,
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "Ia": 0,
#                                     "Ib": 0,
#                                     "Ic": 0
#                                 }]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": unit_instance.unit_name,
#                         "detail": result,
#                     },
#                 }
#             )
#         else:
#             station = ser.validated_data.get("station")
#             time_str = ser.validated_data.get("time")
#             last_result = []
#             # station_inst = models.StationDetails.objects.filter(english_name=station).first()
#             master_stations = models.MaterStation.objects.filter(english_name=station, is_delete=0)
#             if master_stations.exists():
#
#                 master_station = master_stations.first()
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
#
#                 if slave_stations.exists():
#                     start_time = str(time_str)
#                     end_time = str(time_str).replace('00:00:00', '23:59:59')
#                     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#
#                     units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()
#                     if units.exists():
#                         for index, unit in enumerate(units):
#                             device_type = 'pcs'
#                             device = getattr(unit, device_type)
#                             result = time_range_by_dwd(unit.station.english_name, 'measure', device_type, device,
#                                                        start_datatime, end_datatime, 'Ia', 'Ib', 'Ic')
#                             for idx, one in enumerate(result):
#                                 if index == 0:
#                                     last_result.append(one)
#                                 else:
#                                     if idx < len(last_result) and idx < len(result):
#                                         last_result[idx]['Ia'] = round(one['Ia'] + last_result[idx]['Ia'], 2)
#                                         last_result[idx]['Ib'] = round(one['Ib'] + last_result[idx]['Ib'], 2)
#                                         last_result[idx]['Ic'] = round(one['Ic'] + last_result[idx]['Ic'], 2)
#
#             if not last_result:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": ser.validated_data["station"],
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "Ia": 0,
#                                     "Ib": 0,
#                                     "Ic": 0
#                                 }]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "station": ser.validated_data["station"],
#                         "detail": last_result,
#                     },
#                 }
#             )


# class PhaseVoltageView(APIView):
#     """当日三相电压"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         from .db_link import time_range_by_measure_pu
#         ser = monitor_serializers.ElectricityTypeCountSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"当日三相电压:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         unit_english_name = ser.validated_data.get("unit")
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             station_english_name = unit_instance.station.english_name
#             # station_app = unit_instance.station.app
#
#             # meter_type = unit_instance.station.meter_type
#             # meter_dic = METER_DIC.get(meter_type)
#             device_type = 'pcs'
#             device = getattr(unit_instance, device_type)
#             # 获取当前时间
#             time_str = ser.validated_data.get("time")
#             # 开始时间
#             start_time = str(time_str)
#             end_time = str(time_str).replace('00:00:00', '23:59:59')
#             start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#             end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#             result = time_range_by_measure_pu(station_english_name, 'measure', device_type, device, start_datatime, end_datatime)
#             if result == []:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": unit_instance.unit_name,
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "PUa": 0,
#                                     "PUb": 0,
#                                     "PUc": 0
#                                 }
#                             ],
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": unit_instance.unit_name,
#                         "detail": result,
#                     },
#                 }
#             )
#         else:
#             station = ser.validated_data.get("station")
#             time_str = ser.validated_data.get("time")
#
#             last_result = []
#             # station_inst = models.StationDetails.objects.filter(english_name=station).first()
#             master_stations = models.MaterStation.objects.filter(english_name=station, is_delete=0)
#             if master_stations.exists():
#
#                 master_station = master_stations.first()
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
#
#                 if slave_stations.exists():
#                     start_time = str(time_str)
#                     end_time = str(time_str).replace('00:00:00', '23:59:59')
#                     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#
#                     units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()
#                     if units.exists():
#                         for index, unit in enumerate(units):
#                             # device = METER_DIC.get(unit.station.meter_type).get('device').upper()
#                             device_type = 'pcs'
#                             device = getattr(unit, device_type)
#                             result = time_range_by_measure_pu(unit.station.english_name, 'measure', device_type, device, start_datatime, end_datatime)
#
#                             for idx, one in enumerate(result):
#                                 if index == 0:
#                                     last_result.append(one)
#                                 else:
#                                     if idx < len(last_result) and idx < len(result):
#                                         last_result[idx]['PUa'] += one['PUa']
#                                         last_result[idx]['PUb'] += one['PUb']
#                                         last_result[idx]['PUc'] += one['PUc']
#
#                         for result in last_result:
#                             result['PUa'] = round(result['PUa'] / len(units), 2)
#                             result['PUb'] = round(result['PUb'] / len(units), 2)
#                             result['PUc'] = round(result['PUc'] / len(units), 2)
#
#             if not last_result:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": ser.validated_data["station"],
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "PUa": 0,
#                                     "PUb": 0,
#                                     "PUc": 0
#                                 }
#                             ],
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "station": ser.validated_data["station"],
#                         "detail": last_result,
#                     },
#                 }
#             )


# class EstimatedCapacityDischargingView(APIView):
#     """当日预估可充可放"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.ElectricityTypeCountSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"当日预估可充可放:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         unit_english_name = ser.validated_data.get("unit")
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#         if unit_english_name:
#             unit_instance = models.Unit.objects.filter(is_delete=0, english_name=unit_english_name).first()
#             station_english_name = unit_instance.station.english_name
#             station_app = unit_instance.station.app
#
#             meter_type = unit_instance.station.meter_type
#             meter_dic = METER_DIC.get(meter_type)
#             device = meter_dic.get("device")
#             # 获取当前时间
#             time_str = ser.validated_data.get("time")
#             # 开始时间
#             start_time = str(time_str)
#             end_time = str(time_str).replace('00:00:00', '23:59:59')
#             start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#             end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#             device_type = 'pcs'
#             device = getattr(unit_instance, device_type)
#             result = time_range_by_dwd(station_english_name, 'measure', device_type, device,
#                                               start_datatime, end_datatime, 'BPCE', 'BPDE')
#             if result == []:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": unit_instance.unit_name,
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "BPCE": 0,
#                                     "BPDE": 0
#                                 }
#                             ]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": unit_instance.unit_name,
#                         "detail": result,
#                     },
#                 }
#             )
#         else:
#             station = ser.validated_data.get("station")
#             time_str = ser.validated_data.get("time")
#             last_result = []
#             # station_inst = models.StationDetails.objects.filter(english_name=station).first()
#             master_stations = models.MaterStation.objects.filter(english_name=station, is_delete=0)
#             if master_stations.exists():
#
#                 master_station = master_stations.first()
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
#
#                 if slave_stations.exists():
#                     start_time = str(time_str)
#                     end_time = str(time_str).replace('00:00:00', '23:59:59')
#                     start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#
#                     units = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).all()
#                     if units.exists():
#                         for index, unit in enumerate(units):
#                             start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                             end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#                             device_type = 'pcs'
#                             device = getattr(unit, device_type)
#                             result = time_range_by_dwd(unit.station.english_name, 'measure', device_type, device,
#                                                        start_datatime, end_datatime, 'BPCE', 'BPDE')
#                             for idx, one in enumerate(result):
#                                 if one['BPCE'] is None:
#                                     one['BPCE'] = 0
#                                 if one['BPDE'] is None:
#                                     one['BPDE'] = 0
#
#                                 if index == 0:
#                                     last_result.append(one)
#                                 else:
#                                     if idx < len(last_result) and idx < len(result):
#                                         last_result[idx]['BPCE'] += one['BPCE']
#                                         last_result[idx]['BPDE'] += one['BPDE']
#
#             if not last_result:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "station": ser.validated_data["station"],
#                             "detail": [
#                                 {
#                                     "time": formatted_datetime,
#                                     "BPCE": 0,
#                                     "BPDE": 0
#                                 }
#                             ]
#                         },
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "station": ser.validated_data["station"],
#                         "detail": last_result,
#                     },
#                 }
#             )


# class RealTimeStationPolicyView(APIView):
#     """实时策略--废弃"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _request_data(self, app, station):
#         url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#         request_json = {
#             "app": app,
#             "station": station,
#             "body": [{"device": "EMS", "datatype": "measure", "totalcall": "1", "body": []}]
#         }
#         response = requests.post(url=url, json=request_json)
#         return_dic = response.json()
#         try:
#             datas = return_dic['body']
#         except:
#             datas = []
#         return datas
#
#     def _get_status(self, app, station):
#         url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#         request_json = {
#             "app": app,
#             "station": station,
#             "body": [{"device": "EMS", "datatype": "status", "totalcall": "0", "body": ["MCGSA", "MCGSP"]}]
#         }
#         response = requests.post(url=url, json=request_json)
#         return_dic = response.json()
#         error_log.error(return_dic)
#         try:
#             datas = return_dic['body'][0]['body']
#         except:
#             datas = None
#         return datas
#
#     def _get_pv_status(self, province, type_, level):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(datetime.datetime.now().month)  # 当前月
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         if not request.data.get('station'):
#             error_log.error(f"实时策略:字段校验不通过 => station字段未填写")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "station字段为必填项"},
#                 }
#             )
#
#         unit_english_name = request.data['station']
#         station_policy = []
#         station_instance = models.StationDetails.objects.filter(english_name=unit_english_name).first()
#         station_english_name = station_instance.english_name
#         station_app = station_instance.app
#         station_type = station_instance.type
#         station_level = station_instance.level
#         # 当前月电价的峰谷标识
#         pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level)
#
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         real_status = self._get_status(station_app, station_english_name)
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#         if not real_status:
#             error_log.error('查询状态时失败')
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error",
#                              "unit_name": station_instance.station_name,
#                              "detail": []
#                              },
#                 }
#             )
#         MCGSP = int(real_status.get('MCGSP'))
#         MCGSA = int(real_status.get('MCGSA'))
#         if MCGSA:  # 组态自动化运行模式
#             datas = self._request_data(station_app, station_english_name)
#             # error_log.error(datas)
#             if not datas or not datas[0].get('body'):
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": station_instance.station_name,
#                             "detail": [],
#                         },
#                     }
#                 )
#
#             # for i in datas:
#             #     policy = {}
#             #     if (current_month >= int(i['body']['STSMonth'])) and (current_month <= int(i['body']['STTSMonth'])):
#             #         policy['type'] = "夏季电价"
#             #         for k, v in i['body'].items():
#             #             MTC_key = re.match(r'MTC\d+', k)
#             #             RL_key = re.match(r'RL\d+', k)
#             #             if MTC_key:
#             #                 time_str = MTC_key.group().replace('MTC', '')
#             #                 if not policy.get(f'{time_str}时'):
#             #                     policy[f'{time_str}时'] = {}
#             #                 policy[f'{time_str}时']['TC'] = v
#             #             if RL_key:
#             #                 time_str = RL_key.group().replace('RL', '').replace('M', '')
#             #                 if not policy.get(f'{time_str}时'):
#             #                     policy[f'{time_str}时'] = {}
#             #                 policy[f'{time_str}时']['RL'] = v
#             #     else:
#             #         policy['type'] = "非夏季电价"
#             #         for k, v in i['body'].items():
#             #             MTCW_key = re.match(r'MTCW\d+', k)
#             #             RLW_key = re.match(r'RLW\d+', k)
#             #             if MTCW_key:
#             #                 time_str = MTCW_key.group().replace('MTCW', '')
#             #                 if not policy.get(f'{time_str}时'):
#             #                     policy[f'{time_str}时'] = {}
#             #                 policy[f'{time_str}时']['TC'] = v
#             #             if RLW_key:
#             #                 time_str = RLW_key.group().replace('RLW', '').replace('M', '')
#             #                 if not policy.get(f'{time_str}时'):
#             #                     policy[f'{time_str}时'] = {}
#             #                 policy[f'{time_str}时']['RL'] = v
#             #     policy['WLoadFollowTC'] = i['body']['WLoadFollowTC']
#             #     policy['STSMonth'] = i['body']['STSMonth']
#             #     policy['STTSMonth'] = i['body']['STTSMonth']
#             #     station_policy.append(policy)
#
#             for i in datas:
#                 policy = {}
#                 policy['id'] = 0
#                 policy['delete'] = 0
#                 policy['follow'] = i['body']['WLoadFollowTC']
#                 policy['create_time'] = formatted_datetime
#                 policy['name'] = station_instance.province.name + common_response_code.ConfLevType.TYPE[station_type] + \
#                                  common_response_code.ConfLevType.LEVEL[station_level] + '默认运行策略'
#                 policy['summer'] = []
#                 policy['none_summer'] = []
#                 policy['nosummer_end'] = int(i['body']['STSMonth']) - 1
#                 policy['nosummer_start'] = int(i['body']['STTSMonth']) + 1
#                 policy['summer_end'] = int(i['body']['STTSMonth'])
#                 policy['summer_start'] = int(i['body']['STSMonth'])
#                 policy['rl_list'] = []
#
#                 summer = [0] * 24  # 24个点都是0
#                 none_summer = [0] * 24
#                 for k, v in i['body'].items():
#                     MTC_key = re.match(r'MTC\d+', k)  # 夏标识
#                     RL_key = re.match(r'RL\d+', k)  # 夏限值
#                     MTCW_key = re.match(r'MTCW\d+', k)  # 非夏标识
#                     RLW_key = re.match(r'RLW\d+', k)  # 非夏限值
#
#                     if MTC_key:
#                         time_str = int(MTC_key.group().replace('MTC', ''))
#                         summer[time_str - 1] = int(float(v))
#                     if MTCW_key:
#                         time_str = int(MTCW_key.group().replace('MTCW', ''))
#                         none_summer[time_str - 1] = int(float(v))
#                     if RL_key:
#                         time_str = RL_key.group().replace('M', '')
#                         policy['rl_list'].append({time_str: int(float(v) * 100)})
#                         ind = int(time_str.replace('RL', '')) - 1
#                         policy[f"pv{str(ind)}"] = pv_list[ind]
#                     if RLW_key:
#                         time_str = RLW_key.group().replace('M', '')
#                         policy['rl_list'].append({time_str: int(float(v) * 100)})
#                         ind = int(time_str.replace('RLW', '')) - 1
#
#                         policy[f"pv{str(ind)}_no"] = pv_list[ind]
#
#                 policy['summer'] = summer
#                 policy['none_summer'] = none_summer
#                 station_policy.append(policy)
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": station_instance.station_name,
#                         "detail": station_policy,
#                     },
#                 }
#             )
#         elif MCGSP:  # 组态计划调度模式
#             datas = self._request_data(station_app, station_english_name)
#             if not datas or not datas[0].get('body'):
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "unit_name": station_instance.station_name,
#                             "detail": [],
#                         },
#                     }
#                 )
#             # for i in datas:
#             #     policy = {}
#             #     for k, v in i['body'].items():
#             #         TP_key = re.match(r'TP\d+', k)
#             #         if TP_key:
#             #             time_str = TP_key.group().replace('TP', '')
#             #             if not policy.get(f'{time_str}时'):
#             #                 policy[f'{time_str}时'] = {}
#             #             policy[f'{time_str}时']['TP'] = v
#             #             policy[f'{time_str}时']['TC'] = '----'
#             #             policy[f'{time_str}时']['RL'] = '----'
#             #
#             #     policy['WLoadFollowTC'] = i['body']['WLoadFollowTC']
#             #
#             #     station_policy.append(policy)
#             for i in datas:
#                 policy = {}
#                 policy['id'] = 0
#                 policy['delete'] = 0
#                 policy['follow'] = i['body']['WLoadFollowTC']
#                 policy['create_time'] = formatted_datetime
#                 policy['name'] = '实时策略'
#                 policy['summer'] = []
#                 policy['none_summer'] = []
#                 policy['nosummer_end'] = int(i['body']['STSMonth']) - 1
#                 policy['nosummer_start'] = int(i['body']['STTSMonth']) + 1
#                 policy['summer_end'] = int(i['body']['STTSMonth'])
#                 policy['summer_start'] = int(i['body']['STSMonth'])
#                 policy['rl_list'] = []
#
#                 for k, v in i['body'].items():
#                     RL_key = re.match(r'RL\d+', k)  # 夏限值
#                     tp_key = re.match(r'TP\d+', k)
#
#                     if RL_key:
#                         time_str = RL_key.group().replace('M', '')
#                         policy['rl_list'].append({time_str: '----'})
#                         policy[f"pv{str(int(time_str.replace('RL', '')) - 1)}"] = 0
#
#                     if tp_key:
#                         time_str = tp_key.group().replace('TP', '')
#                         policy['rl_list'].append({f'RLW{time_str}': int(float(v) * 100)})
#                         policy[f'pv{str(int(time_str) - 1)}_no'] = 0
#
#                 policy['summer'] = [0] * 24
#                 policy['none_summer'] = [0] * 24
#                 station_policy.append(policy)
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": station_instance.station_name,
#                         "detail": station_policy,
#                     },
#                 }
#             )
#         else:
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "unit_name": station_instance.station_name,
#                         "detail": 'MCGSP和MCGSA都为0无法处理！',
#                     },
#                 }
#             )


# class RealTimeStationStrategyView(APIView):
#     """新-实时策略"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     # def _request_data(self, app, station):
#     #     url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#     #     request_json = {
#     #         "app": app,
#     #         "station": station,
#     #         "body": [{"device": "EMS", "datatype": "measure", "totalcall": "1", "body": []}]
#     #     }
#     #     response = requests.post(url=url, json=request_json)
#     #     return_dic = response.json()
#     #     try:
#     #         datas = return_dic['body']
#     #     except:
#     #         datas = []
#     #     return datas
#
#     # def _get_status(self, app, station):
#     #     url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#     #     request_json = {
#     #         "app": app,
#     #         "station": station,
#     #         "body": [{"device": "EMS", "datatype": "status", "totalcall": "0", "body": ["MCGSA", "MCGSP"]}]
#     #     }
#     #     response = requests.post(url=url, json=request_json)
#     #     return_dic = response.json()
#     #     error_log.error(return_dic)
#     #     try:
#     #         datas = return_dic['body'][0]['body']
#     #     except:
#     #         datas = None
#     #     return datas
#
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         conn = get_redis_connection("3")
#         client = mtqq_station_strategy(station_id, month)
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data, conn
#
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(datetime.datetime.now().month) if not month else month
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         if not request.data.get('station'):
#             error_log.error(f"实时策略:字段校验不通过 => station字段未填写")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "station字段为必填项"},
#                 }
#             )
#
#         station_english_name = request.data['station']
#         # station_instance = models.StationDetails.objects.filter(english_name=station_english_name).first()
#         master_station = models.MaterStation.objects.filter(english_name=station_english_name, is_delete=0).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#
#         station_type = station_instance.type
#         station_level = station_instance.level
#         # 目标月电价的峰谷标识
#
#         month = request.data.get('month')
#         pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level, month)
#
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#
#         data, conn = self._get_data(station_instance.id, station_instance.english_name, month)
#         policy = {}
#         policy['id'] = 0
#         policy['is_delete'] = 0
#         policy['months'] = [month if month else datetime.datetime.now().month]
#         policy['create_time'] = formatted_datetime
#         policy['name'] = station_instance.province.name + common_response_code.ConfLevType.TYPE[station_type] + \
#                      common_response_code.ConfLevType.LEVEL[station_level] + '默认运行策略'
#
#         res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station_instance.english_name))
#         if res_fllow:
#             res_fllow = json.loads(eval(res_fllow))
#         else:
#             res_fllow = {}
#         policy['is_follow'] = res_fllow.get('WLoadFollowTC', 0)
#         charge_config = [int(data.get(key, 0)) for key in current_month_charge_keys]
#         rl_list = dict()
#         for key in current_month_rl_keys:
#             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(data.get(key, 0)) * 100, 2)
#
#         policy['rl_list'] = rl_list
#         policy['charge_config'] = charge_config
#
#         for nu in range(0, 24):
#             policy[f"pv{str(nu)}"] = pv_list[nu]
#         return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {
#                             "message": "success",
#                             "unit_name": station_instance.station_name,
#                             "detail": policy,
#                         },
#                     }
#                 )
#
#         # real_status = self._get_status(station_app, station_english_name)
#         # current_datetime = datetime.datetime.now()
#         #
#         # formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#         # if not real_status:
#         #     error_log.error('查询状态时失败')
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "error",
#         #                      "unit_name": station_instance.station_name,
#         #                      "detail": {}
#         #                      },
#         #         }
#         #     )
#         # MCGSP = int(real_status.get('MCGSP'))
#         # MCGSA = int(real_status.get('MCGSA'))
#
#         # current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         # current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#         # if MCGSA:  # 组态自动化运行模式
#         #     datas = self._request_data(station_app, station_english_name)
#         #     # error_log.error(datas)
#         #     if not datas or not datas[0].get('body'):
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.ERROR,
#         #                 "data": {
#         #                     "message": "error",
#         #                     "station_name": station_instance.station_name,
#         #                     "detail": {},
#         #                 },
#         #             }
#         #         )
#         #
#         #     for i in datas:
#         #         policy = {}
#         #         policy['id'] = 0
#         #         policy['is_delete'] = 0
#         #         policy['months'] = [month]
#         #         policy['is_follow'] = i['body']['WLoadFollowTC']
#         #         policy['create_time'] = formatted_datetime
#         #         policy['name'] = station_instance.province.name + common_response_code.ConfLevType.TYPE[station_type] + \
#         #                          common_response_code.ConfLevType.LEVEL[station_level] + '默认运行策略'
#         #
#         #         charge_config = [int(i['body'].get(key, 0)) for key in current_month_charge_keys]
#         #         rl_list = dict()
#         #         for key in current_month_rl_keys:
#         #             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = float(i['body'].get(key, 0))*100
#         #
#         #         policy['rl_list'] = rl_list
#         #         policy['charge_config'] = charge_config
#         #
#         #         for nu in range(0, 24):
#         #             policy[f"pv{str(nu)}"] = pv_list[nu]
#         #
#         #         station_strategy.append(policy)
#         #
#         #     if len(station_strategy):
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.SUCCESS,
#         #                 "data": {
#         #                     "message": "success",
#         #                     "unit_name": station_instance.station_name,
#         #                     "detail": station_strategy[0],
#         #                 },
#         #             }
#         #         )
#         #     else:
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.SUCCESS,
#         #                 "data": {
#         #                     "message": "no data",
#         #                     "unit_name": station_instance.station_name,
#         #                     "detail": {},
#         #                 },
#         #             }
#         #         )
#         # elif MCGSP:  # 组态计划调度模式
#         #     datas = self._request_data(station_app, station_english_name)
#         #     if not datas or not datas[0].get('body'):
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.ERROR,
#         #                 "data": {
#         #                     "message": "error",
#         #                     "unit_name": station_instance.station_name,
#         #                     "detail": [],
#         #                 },
#         #             }
#         #         )
#         #
#         #     for i in datas:
#         #         policy = {}
#         #         policy['id'] = 0
#         #         policy['is_delete'] = 0
#         #         policy['months'] = [month]
#         #         policy['is_follow'] = i['body']['WLoadFollowTC']
#         #         policy['create_time'] = formatted_datetime
#         #         policy['name'] = '实时策略'
#         #
#         #         charge_config = [int(i['body'].get(key, 0)) for key in current_month_charge_keys]
#         #         rl_list = dict()
#         #         for key in current_month_rl_keys:
#         #             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = float(i['body'].get(key, 0))*100
#         #
#         #         policy['rl_list'] = rl_list
#         #         policy['charge_config'] = charge_config
#         #
#         #         for nu in range(0, 24):
#         #             policy[f"pv{str(nu)}"] = pv_list[nu]
#         #
#         #         station_strategy.append(policy)
#         #
#         #     if len(station_strategy):
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.SUCCESS,
#         #                 "data": {
#         #                     "message": "success",
#         #                     "unit_name": station_instance.station_name,
#         #                     "detail": station_strategy[0],
#         #                 },
#         #             }
#         #         )
#         #     else:
#         #         return Response(
#         #             {
#         #                 "code": common_response_code.SUCCESS,
#         #                 "data": {
#         #                     "message": "no data",
#         #                     "unit_name": station_instance.station_name,
#         #                     "detail": {},
#         #                 },
#         #             }
#         #         )
#         # else:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.SUCCESS,
#         #             "data": {
#         #                 "message": "success",
#         #                 "unit_name": station_instance.station_name,
#         #                 "detail": 'MCGSP和MCGSA都为0无法处理！',
#         #             },
#         #         }
#         #     )


# class StationStrategyView(APIView):
#     """新-站自动控制策略：发布+订阅"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["station_id"] = json.loads(request.body.decode()).get("station_id", None)
#         self.serializer_context["uid"] = uuid.uuid4()
#
#     def post(self, request):
#         station_id = request.data.get('station_id')
#         month = request.data.get('month')
#
#         station_instance = models.StationDetails.objects.filter(id=station_id, is_delete=0).first()
#         if not station_instance:
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 站信息不存在！"
#                     },
#                 }
#             )
#         station_app = station_instance.app
#         station_english_name = station_instance.english_name
#
#         temp_list = list()
#         for i in range(0, 24):
#             temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'F')
#             temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'P')
#
#         import time
#         now_time = str(int(time.time()))
#
#         t10 = ['M10H1F', 'M10H2F', 'M10H3F', 'M10H4F', 'M10H5F', 'M10H6F', 'M10H7F', 'M10H8F',
#               'M10H9F', 'M10H10F', 'M10H11F', 'M10H12F', 'M10H13F', 'M10H14F', 'M10H15F', 'M10H16F', 'M10H17F', 'M10H18F',
#               'M10H19F', 'M10H20F', 'M10H21F', 'M10H22F', 'M10H23F', 'M10H24F',
#               'M10H1P', 'M10H2P', 'M10H3P', 'M10H4P', 'M10H5P', 'M10H6P', 'M10H7P', 'M10H8P',
#               'M10H9P', 'M10H10P', 'M10H11P', 'M10H12P', 'M10H13P', 'M10H14P', 'M10H15P', 'M10H16P', 'M10H17P', 'M10H18P',
#               'M10H19P', 'M10H20P', 'M10H21P', 'M10H22P', 'M10H23P', 'M10H24P']
#
#         t11 = ['M11H1F', 'M11H2F', 'M11H3F', 'M11H4F', 'M11H5F', 'M11H6F', 'M11H7F', 'M11H8F',
#               'M11H9F', 'M11H10F', 'M11H11F', 'M11H12F', 'M11H13F', 'M11H14F', 'M11H15F', 'M11H16F', 'M11H17F', 'M11H18F',
#               'M11H19F', 'M11H20F', 'M11H21F', 'M11H22F', 'M11H23F', 'M11H24F',
#               'M11H1P', 'M11H2P', 'M11H3P', 'M11H4P', 'M11H5P', 'M11H6P', 'M11H7P', 'M11H8P',
#               'M11H9P', 'M11H10P', 'M11H11P', 'M11H12P', 'M11H13P', 'M11H14P', 'M11H15P', 'M11H16P', 'M11H17P', 'M11H18P',
#               'M11H19P', 'M11H20P', 'M11H21P', 'M11H22P', 'M11H23P', 'M11H24P']
#
#         C11 = ['M11H1FC', 'M11H2FC', 'M11H3FC', 'M11H4FC', 'M11H5FC', 'M11H6FC', 'M11H7FC', 'M11H8FC',
#               'M11H9FC', 'M11H10FC', 'M11H11FC', 'M11H12FC', 'M11H13FC', 'M11H14FC', 'M11H15FC', 'M11H16FC', 'M11H17FC', 'M11H18FC',
#               'M11H19FC', 'M11H20FC', 'M11H21FC', 'M11H22FC', 'M11H23FC', 'M11H24FC',
#               'M11H1PC', 'M11H2PC', 'M11H3PC', 'M11H4PC', 'M11H5PC', 'M11H6PC', 'M11H7PC', 'M11H8PC',
#               'M11H9PC', 'M11H10PC', 'M11H11PC', 'M11H12PC', 'M11H13PC', 'M11H14PC', 'M11H15PC', 'M11H16PC', 'M11H17PC', 'M11H18PC',
#               'M11H19PC', 'M11H20PC', 'M11H21PC', 'M11H22PC', 'M11H23PC', 'M11H24PC']
#
#         t9 = ['M9H1F', 'M9H2F', 'M9H3F', 'M9H4F', 'M9H5F', 'M9H6F', 'M9H7F', 'M9H8F',
#               'M9H9F', 'M9H10F', 'M9H11F', 'M9H12F', 'M9H13F', 'M9H14F', 'M9H15F', 'M9H16F', 'M9H17F', 'M9H18F',
#               'M9H19F', 'M9H20F', 'M9H21F', 'M9H22F', 'M9H23F', 'M9H24F',
#               'M9H1P', 'M9H2P', 'M9H3P', 'M9H4P', 'M9H5P', 'M9H6P', 'M9H7P', 'M9H8P',
#               'M9H9P', 'M9H10P', 'M9H11P', 'M9H12P', 'M9H13P', 'M9H14P', 'M9H15P', 'M9H16P', 'M9H17P', 'M9H18P',
#               'M9H19P', 'M9H20P', 'M9H21P', 'M9H22P', 'M9H23P', 'M9H24P']
#
#         message = {
#             "time": now_time,
#             "body": [
#                 {
#                     "device": "EMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": t9
#                 }
#             ]
#         }
#
#         message1 = {
#             "time": now_time,
#             "body": [
#                 {
#                     "device": "EMS",
#                     "datatype": "parameter",
#                     "totalcall": "0",
#                     "body": C11
#                 }
#             ]
#         }
#
#         # 准备下发策略
#         # print(station_english_name, station_app)
#         req_topic = f"req/database/realtime/{station_english_name}/{station_app}"
#         res_topic = f"res/database/realtime/{station_english_name}/{station_app}"
#         # secret_key = settings.AES_KEY
#         # aes = EncryptDate(secret_key)
#
#         # token = aes.encrypt(station_english_name)
#
#         # message = {
#         #     "time": str(int(time.time())),
#         #     "token": token,
#         #     "device": "EMS",
#         #     "body": temp_list
#         # }
#
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#         json_message1 = json.dumps(message1)
#
#         # 发布
#         client.publish(req_topic, json_message)
#         client.publish(req_topic, json_message1)
#
#         # time.sleep(3)
#         # 获取订阅结果
#         client.subscribe(res_topic)
#
#         client.loop_forever()
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "发布订阅成功",
#                 },
#             }
#         )


# class ElectricityLoadCurveView(APIView):
#     """负载曲线"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.ElectricityLoadCurveSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error(f"负载曲线:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station_ = ser.validated_data.get("station")
#         time_str = ser.validated_data.get("time", None)
#         if not time_str:
#             time_str = str(datetime.date.today()) + ' 00:00:00'
#
#         result = []
#         master_stations = models.MaterStation.objects.filter(english_name=station_, is_delete=0)
#         if master_stations.exists():
#             start_time = str(time_str)
#             end_time = str(time_str).replace('00:00:00', '23:59:59')
#
#             master_station = master_stations.first()
#             slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
#
#             if slave_stations.exists():
#                 # temp_slave_station = slave_stations.first()
#
#                 # 标准站
#                 if slave_stations.count() == 1:
#                     station = slave_stations.first()
#
#                     # start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     # end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#                     meter_position = station.meter_position
#                     meter_position = 1 if meter_position is None else meter_position
#                     device = METER_DIC.get(station.meter_type).get('device')
#                     result = time_range_by_measure_lc(station, start_time, end_time, meter_position, device)
#                 # 主从站
#                 else:
#                     result = master_station_time_range_by_measure_lc(master_station, start_time, end_time)
#
#         if not result:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {
#                         "message": "error",
#                         "station": station_,
#                         "detail": [
#                         ]
#                     },
#                 }
#             )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "station": station_,
#                     "detail": result,
#                 },
#             }
#         )


class CustomIncomeView(GenericAPIView):
    """收益录入: 新增/详情"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    queryset = models.CustomIncome.objects.all()
    serializer_class = income_serializers.CustomIncomeSerializer
    # pagination_class = MyPaginationClass

    filter_backends = filters.SearchFilter
    search_fields = ('project', 'income_month', 'income_type')

    lookup_field = 'id'

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user["user_id"]
        user_name = models.UserDetails.objects.get(id=user_id).user_name
        # print(request.data)
        ser = self.get_serializer(data=request.data, context=self.serializer_context)

        if not ser.is_valid():
            error_log.error(f"录入收益:字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        download_url = ""
        file_name = ""
        try:
            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():
                #     uploaded_file = form.cleaned_data['file']
                #     uploaded_file_instance = UploadedFile(file=uploaded_file)
                #     uploaded_file_instance.file_name = uploaded_file.name
                #     uploaded_file_instance.Uuid = uuid.uuid4()
                #     uploaded_file_instance.save()
                #     download_url = uploaded_file_instance.get_download_url()  # 获取文件的下载链接
                #     file_name = uploaded_file.name
                obj = request.FILES.get('file')
                if obj:
                    file_name = obj.name  # 获取上传文件的字符串类型名称/
                    # suffix = re.findall(r'\..*', img_name)[0]
                    # file_name = new_name + suffix
                    path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

                    with open(path, mode='wb') as f:
                        for content in obj.chunks():  # 读取上传文件的内容
                            f.write(content)  # 存储图像文件

                    try:
                        minio_client = MinioTool()
                        minio_client.create_bucket('worker')
                        minio_client.upload_local_image(file_name, path, 'worker')
                        os.remove(path)
                        download_url = path
                    except Exception as e:
                        success_log.error(e)
                        error_log.error(traceback.print_exc())

        except Exception as e:
            error_log.error("录入收益:附件上传失败==>{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        ser.save(creator=user_name, attar_url=download_url, attar_name=file_name)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ser.data,
                },
            }
        )

    def get(self, request, id):

        lang = request.headers.get("lang", 'zh')

        try:
            income_ins = models.CustomIncome.objects.get(id=id)
        except Exception as e:
            error_log.error("录入收益：{} 条目不存在：{}".format(id, e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "录入收益：条目不存在: {}".format(e) if lang == 'zh' else
                             "Entry does not exist."},
                }
            )

        ser = income_serializers.CustomIncomeSerializer(income_ins)

        i = ser.data

        if lang == 'en':
            i['station'] = i['en_station']
            i['creator'] = i['en_creator']
            i['updator'] = i['en_updator']

        if i.get('attar_name'):
            try:
                minio_client = MinioTool()
                minio_client.create_bucket('worker')
                i['attar_url'] = minio_client.get_download_url('worker', i.get('attar_name'))
            except Exception as e:
                pass

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": i,
                },
            }
        )

    def put(self, request, id):
        lang = request.headers.get("lang", 'zh')

        user_id = request.user["user_id"]
        user_name = models.UserDetails.objects.get(id=user_id).user_name

        income_ins = models.CustomIncome.objects.get(id=id)

        ser = self.get_serializer(income_ins, data=request.data, context=self.serializer_context)

        if not ser.is_valid():
            error_log.error(f"录入收益:更新字段校验不通过 =>{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        download_url = ""
        file_name = ""
        try:
            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():
                obj = request.FILES.get('file')
                if obj:
                    file_name = obj.name  # 获取上传文件的字符串类型名称/
                    # suffix = re.findall(r'\..*', img_name)[0]
                    # file_name = new_name + suffix
                    path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

                    with open(path, mode='wb') as f:
                        for content in obj.chunks():  # 读取上传文件的内容
                            f.write(content)  # 存储图像文件

                    try:
                        minio_client = MinioTool()
                        minio_client.create_bucket('worker')
                        minio_client.upload_local_image(file_name, path, 'worker')
                        os.remove(path)
                        download_url = path
                    except Exception as e:
                        success_log.error(e)
                        error_log.error(traceback.print_exc())
        except Exception as e:
            error_log.error("录入收益:附件上传失败==>{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        ser.save(updator=user_name, attar_url=download_url, attar_name=file_name)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ser.data,
                },
            }
        )


class CustomIncomeDelView(GenericAPIView):
    """收益录入: 删除"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    queryset = models.CustomIncome.objects.all()
    serializer_class = income_serializers.CustomIncomeSerializer

    def post(self, request, id):
        lang = request.headers.get("lang", 'zh')
        income_ins = models.CustomIncome.objects.get(id=id)
        try:
            income_ins.is_delete = 1
            income_ins.save()
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "delete success",
                        "detail": id,
                    },
                }
            )
        except Exception as e:
            error_log.error("录入收入：收入信息删除失败：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "录入收入：收入信息删除失败" if lang == 'zh' else
                             "Failed to delete income information."},
                }
            )


class CustomIncomesView(GenericAPIView):
    """收益录入: 列表数据"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    queryset = models.CustomIncome.objects.all()
    serializer_class = income_serializers.CustomIncomeSerializer
    # pagination_class = MyPaginationClass

    # filter_backends = filters.SearchFilter
    search_fields = ('project', 'income_month', 'income_type')

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user = request.user['user_id']
        projects = models.Project.objects.filter(user=user, is_used=1).all()
        projects_names = [i.name for i in projects]
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 15))
        income_instances = models.CustomIncome.objects.filter(is_delete=0, project__in=projects_names).all()

        # 筛选
        income_filter = CustomIncomeFilter(queryset=income_instances, data=request.query_params)
        tem_income_instances = income_filter.qs

        # 分页
        paginator = Paginator(tem_income_instances, page_size)
        tem_income_instances_ = paginator.get_page(page)

        paginator_info = {
            "page": page,
            "page_size": page_size,
            "pages": paginator.num_pages,
            "total_count": paginator.count
        }

        ser = income_serializers.CustomIncomeSerializer(tem_income_instances_, many=True)

        for i in ser.data:
            if lang == 'en':
                # i['station'] = i['en_station']
                i['creator'] = i['en_creator']
                i['updator'] = i['en_updator']
                if i['station'] == '全部':
                    i['station'] = 'all'
            else:
                if i['station'] == 'All' or i['station'] == 'all':
                    i['station'] = '全部'

            if i.get('attar_name'):
                try:
                    minio_client = MinioTool()
                    minio_client.create_bucket('worker')
                    i['attar_url'] = minio_client.get_download_url('worker', i.get('attar_name'))
                except Exception as e:
                    pass

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ser.data,
                    "paginator_info": paginator_info
                },
            }
        )


# class FileUploadView(APIView):
#     """收益录入: 添加附件"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request, *args, **kwargs):
#         lang = request.headers.get("lang", 'zh')
#         # 如果请求中包含文件，则将其保存到服务器上
#         form = UploadFileForm(request.data, request.FILES)
#         if form.is_valid():
#             uploaded_file = form.cleaned_data['file']
#             uploaded_file_instance = UploadedFile(file=uploaded_file)
#             uploaded_file_instance.file_name = uploaded_file.name
#             uploaded_file_instance.Uuid = uuid.uuid4()
#             uploaded_file_instance.save()
#             download_url = uploaded_file_instance.get_download_url()  # 获取文件的下载链接
#             # return ({'file_path': file_path})
#             return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "success", "detail": {'file_path': download_url}},
#                     }
#                 )
#         else:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "录入收入：上传附件失败" if lang == 'zh' else 'Upload attachment failed.'},
#                 }
#             )


class FileDownloadView(APIView):
    """收益录入: 下载附件"""
    # authentication_classes = [
    #     JwtParamAuthentication,
    #     JWTHeaderAuthentication,
    #     DenyAuthentication,
    # ]  # jwt认证

    def get(self, request, file_id):
        lang = request.headers.get("lang", 'zh')
        uploaded_file = UploadedFile.objects.get(Uuid=file_id)
        file_path = os.path.join(settings.MEDIA_ROOT, str(uploaded_file.file))
        if os.path.exists(file_path):
            # print(file_path)
            with open(file_path, 'rb') as file:
                response = Response(file.read(), content_type='application/force-download')
                response['Content-Disposition'] = f'attachment; filename={os.path.basename(file_path)}'
                return response
        else:
            # return Response('File not found', status=404)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "File not found."},
                }
            )


class ListStationNamesView(APIView):
    """收益录入：电站下拉选择名称"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]
        projects = models.Project.objects.filter(user=user_id, is_used=1).all()
        temp_list = list()

        if projects:
            for project in projects:
                stations = project.materstation_set.filter(is_delete=0).all()
                if stations.exists():
                    for station in stations:
                        temp_list.append(station.name)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": {
                        "stations": temp_list
                    },
                },
            }
        )


class PcsMonitorTitlesView(APIView):
    """PCS、电池簇监测: 标头列表"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        project_id = request.query_params['project_id']
        monitor_type = request.query_params['monitor_type']

        result = list()

        if not all([project_id, type]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "项目ID参数缺失"},
                }
            )
        # print(project_id, monitor_type)
        if not re.match(r'\d+', project_id):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误"},
                }
            )

        # 电站集合
        # station_instances = models.StationDetails.objects.filter(project_id=project_id).all()
        # for station_instance in station_instances:
        master_stations = models.MaterStation.objects.filter(project_id=project_id, is_delete=0).all()
        for master_station in master_stations:
            station_name = master_station.name

            units = models.Unit.objects.filter(is_delete=0, station__master_station_id=master_station.id)
            for unit_ in units:
                unit_name = unit_.unit_new_name
                bms = unit_.bms
                pcs = unit_.pcs

                # 表头，示例："宁波朗盛002-储能单元1-PCS"
                if monitor_type == 'pcs':
                    title = station_name + '-' + unit_name + '-' + pcs
                else:
                    title = station_name + '-' + unit_name + '-' + bms.replace('BMS', '电池簇')
                result.append(title)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": result,
                },
            }
        )


class MonitorView(APIView):
    """PCS/电源监测：实时遥测数据"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        title = request.query_params['title']
        data_type = request.query_params['data_type']

        if not title:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "PCS/电源监测：参数缺失"},
                }
            )
        if not re.match(r'.+?-.+?-.+', title):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "PCS/电源监测：参数格式错误"},
                }
            )

        a = title.split('-')
        if len(a) == 3:
            master_station = a[0]
            unit_name = a[1]
            device = a[2].replace('电池簇', 'BMS') if '电池' in a[2] else a[2]
        elif len(a) == 4:       # 兼容“宁波朗盛001”的并网点名称改叫“朗盛-昱泓东区”；
            master_station = '-'.join(a[:2])
            unit_name = a[2]
            device = a[3].replace('电池簇', 'BMS') if '电池' in a[3] else a[3]

        try:
            # 电站集合
            # station_instance = models.StationDetails.objects.filter(station_name=station).first()
            master_station = models.MaterStation.objects.filter(name=master_station, is_delete=0).first()

            # station_name = master_station.station_name
            # station_english_name = master_station.english_name

            # slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0, pack=0).all()

            units = models.Unit.objects.filter(is_delete=0, station__master_station_id=master_station.id, unit_new_name=unit_name)
            if units.count() > 1 or not units.exists():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "PCS/电源监测：查询储能单元信息异常"},
                    }
                )

            unit = units.first()
            conn = get_redis_connection("3")

            station_app = unit.station.app
            station_english_name = unit.station.english_name
            unit_type = unit.station.unit_number

            # 点表类型表
            type_instance = PointType.objects.filter(type=unit_type, device=device).first()
            if data_type == 'telemetry':
                # 查询点表测量量最新数据
                # measure_list = select_least_by_measure_m(station_app, station_english_name)
                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, device)
                measure_1 = conn.get(key1)
                if measure_1:
                    measure_1_dict = json.loads(json.loads(measure_1.decode("utf-8")))
                else:
                    measure_1_dict = {}

                # 点表：测量量指标
                measure_items = PointMeasure.objects.filter(point_type_id=type_instance.id)
                measure_dict = dict()
                for measure_item in measure_items:
                    measure_dict.update({measure_item.name: {"name": measure_item.description,
                                                             "unit": measure_item.unit,
                                                             "id": measure_item.id}}),

                realtime_telemetry_data = list()  # 实时遥测数据
                if measure_1_dict:
                    for k, v in measure_1_dict.items():
                        if k != 'device' and k in measure_dict.keys():
                            temp_dict = {
                                "id": measure_dict[k]['id'],
                                "key": k,
                                "name": measure_dict[k]['name'],
                                "unit": measure_dict[k]['unit'],
                                "value": v
                            }
                            realtime_telemetry_data.append(temp_dict)

                    for k1, v1 in measure_dict.items():
                        if k1 not in measure_1_dict.keys():
                            temp_dict = {
                                "id": measure_dict[k1]['id'],
                                "key": k1,
                                "name": measure_dict[k1]['name'],
                                "unit": measure_dict[k1]['unit'],
                                "value": '--'
                            }
                            realtime_telemetry_data.append(temp_dict)

                else:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "{}监测：获取遥测数据失败".format(device)}
                        }
                    )

                # 按点表顺序排序
                sorted_realtime_telemetry_data = sorted(realtime_telemetry_data, key=lambda x: x['id'])
                result = {
                    "title": title,
                    "data": sorted_realtime_telemetry_data
                }
            else:
                # 查询点表状态量最新数据
                # status_list = select_least_by_status_m(station_app, station_english_name)
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, device)
                status_1 = conn.get(key2)
                if status_1:
                    status_1_dict = json.loads(json.loads(status_1.decode("utf-8")))
                else:
                    status_1_dict = {}

                # 点表：状态量指标
                status_items = PointStatus.objects.filter(point_type_id=type_instance.id)
                status_dict = dict()
                for status_item in status_items:
                    status_dict.update({status_item.name: {"name": status_item.description,
                                                           "a_status_name": status_item.a_status_name,
                                                           "b_status_name": status_item.a_status_name,
                                                           "id": status_item.id}})

                realtime_remote_data = list()  # 实时遥信数据
                if status_1_dict:
                    for k1, v1 in status_1_dict.items():
                        if k1 != 'device' and k1 in status_dict.keys():
                            temp_dict_ = {
                                "id": status_dict[k1]['id'],
                                "key": k1,
                                "name": status_dict[k1]['name'],
                                "value": status_dict[k1]['a_status_name'] if v1 == "0"
                                else status_dict[k1]['b_status_name']
                            }
                            realtime_remote_data.append(temp_dict_)
                    for k2, v2 in status_dict.items():
                        if k2 not in status_1_dict.keys():
                            temp_dict_ = {
                                "id": status_dict[k2]['id'],
                                "key": k2,
                                "name": status_dict[k2]['name'],
                                "value": status_dict[k2]['a_status_name'] if v2 == "0"
                                else status_dict[k2]['b_status_name']
                            }
                            realtime_remote_data.append(temp_dict_)
                else:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "{}监测：获取遥信数据失败".format(device)},
                        }
                    )

                # 按点表顺序排序
                sorted_realtime_remote_data = sorted(realtime_remote_data, key=lambda x: x['id'])
                result = {"title": title, "data": sorted_realtime_remote_data}

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": result,
                    },
                }
            )

        except Exception as e:
            error_log.error("{}监测：获取遥测数据出错：{}".format(device, e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "{}监测：获取遥测数据出错".format(device)},
                }
            )


# class OperatingDataMonitorView(APIView):
#     """运行数据监测"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.OperatingDataMonitorSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error(f"逐时冲放电量:字段校验不通过 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         project_name = ser.validated_data.get('project')
#         system_level = ser.validated_data.get("system_level")
#         target = ser.validated_data["target"]
#         start_day = ser.validated_data.get("start_time")
#         end_day = ser.validated_data.get("end_time")
#
#         # 项目层级为“项目”
#         if system_level == "1":
#             # 项目有功功率
#             if target == "1":
#                 success_dic = get_project_p(project_name, start_day, end_day)
#
#             # 项目SOC
#             elif target == "2":
#                 success_dic = get_project_soc(project_name, start_day, end_day)
#
#             # 项目逐时充放电量
#             elif target == "3":
#                 type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#                 success_dic = get_project_charge_discharge_by_hour(project_name, start_day, end_day, type_)
#
#             # 项目充放电量统计
#             elif target == "4":
#                 type_ = 'all'  # 尖/峰/平/谷
#                 success_dic = get_project_charge_discharge_count(project_name, start_day, end_day, type_)
#
#             # 项目逐日充放电量
#             elif target == "5":
#                 success_dic = get_project_charge_discharge_by_day(project_name, start_day, end_day)
#             else:
#                 pass
#
#         # 项目层级为“并网点”
#         elif system_level == "2":
#             # 并网点有功功率
#             if target == "1":
#                 success_dic = get_station_p(project_name, start_day, end_day)
#             # 并网点SOC
#             elif target == "2":
#                 success_dic = get_station_soc(project_name, start_day, end_day)
#
#             # 并网点逐时充放电量
#             elif target == "3":
#                 type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#                 tem_station_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001”
#                 success_dic = get_station_charge_discharge_by_hour(project_name, start_day, end_day, type_, tem_station_id)
#
#             # 并网点电量统计
#             elif target == "4":
#                 type_ = 'all'  # 尖/峰/平/谷
#                 tem_station_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001”
#                 success_dic = get_station_charge_discharge_count(project_name, start_day, end_day, type_,
#                                                                    tem_station_id)
#
#             # 并网点逐日充放电量
#             elif target == "5":
#                 type_ = ser.validated_data.get("key1", 'all')  # 尖/峰/平/谷
#                 tem_station_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001”
#                 success_dic = get_station_charge_discharge_by_day(project_name, start_day, end_day, type_,
#                                                                  tem_station_id)
#
#             # 并网点负荷数据
#             elif target == "6":
#                 success_dic = get_station_load_data(project_name, start_day, end_day)
#             else:
#                 pass
#         # 项目层级为“储能单元”
#         else:
#             # 储能单元有功功率
#             if target == "1":
#                 success_dic = get_unit_p(project_name, start_day, end_day)
#
#             # 储能单元SOC
#             elif target == "2":
#                 success_dic = get_unit_soc(project_name, start_day, end_day)
#
#             # 储能单元逐时充放电量
#             elif target == "3":
#                 type_ = ser.validated_data.get("key1", 'all')   # 尖/峰/平/谷
#                 tem_unit_id = ser.validated_data.get("key2", 0)     # 1:“宁波朗盛001-储能单元1”
#                 success_dic = get_unit_charge_discharge_by_hour(project_name, start_day, end_day, type_, tem_unit_id)
#
#             # 储能单元充放电量统计
#             elif target == "4":
#                 type_ = "all"   # 尖/峰/平/谷
#                 tem_unit_id = ser.validated_data.get("key2", 0)     # 1:“宁波朗盛001-储能单元1”
#
#                 success_dic = get_unit_charge_discharge_count(project_name, start_day, end_day, type_, tem_unit_id)
#
#             # 储能单元逐日充放电量
#             elif target == "5":
#                 type_ = ser.validated_data.get("key1", "all")  # 尖/峰/平/谷
#                 tem_unit_id = ser.validated_data.get("key2", 0)  # 1:“宁波朗盛001-储能单元1”
#
#                 success_dic = get_unit_charge_discharge_by_day(project_name, start_day, end_day, type_, tem_unit_id)
#
#             else:
#                 pass
#
#         return Response(success_dic)


# class DefaultStrategyView(APIView):
#     """
#     默认策略
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(month)
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         ser = DefaultStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         now_time = datetime.datetime.now()
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_name, is_delete=0).first()
#         station_info = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         month = request.data.get('month') if request.data.get('month') else now_time.month
#         # pv_list = self._get_pv_status(station_info[1], station_info[2], station_info[3], month)
#         pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#         default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#
#         station_id = station_info.id
#         former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
#         former_actic_ids = [i.former_actic_id for i in former_actic_ids]
#         former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
#         if not former_res:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
#                 }
#             )
#         policy = {}
#         policy['id'] = 0
#         policy['is_delete'] = 0
#         policy['months'] = [month if month else now_time.month]
#         policy['create_time'] = now_time.strftime("%Y-%m-%d %H:%M:%S")
#         policy['name'] = station_info.province.name + common_response_code.ConfLevType.TYPE[station_info.type] + \
#                          common_response_code.ConfLevType.LEVEL[station_info.level] + '默认运行策略'
#
#         former_res = former_res.__dict__
#         conn = get_redis_connection("3")
#         res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station_info.english_name))
#         if res_fllow:
#             res_fllow = json.loads(eval(res_fllow))
#         else:
#             res_fllow = {}
#         policy['is_follow'] = res_fllow.get('WLoadFollowTC', 0)
#         charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
#         rl_list = dict()
#         for key in default_month_rl_keys:
#             rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(former_res.get(key, 0) / former_res.get('power') * 100, 2)
#
#         policy['rl_list'] = rl_list
#         policy['charge_config'] = charge_config
#
#         for nu in range(0, 24):
#             policy[f"pv{str(nu)}"] = pv_list[nu]
#
#         return Response(
#                         {
#                             "code": common_response_code.SUCCESS,
#                             "data": {"message": "success", "detail": policy},
#                         }
#                     )


# class UserStrategyCustomizeView(APIView):
#     """自定义策略查询列表"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request, strategy_id):
#         """
#         获取策略所具有的分类列表
#         """""
#
#         try:
#             strategy_ins = UserStrategy.objects.get(id=strategy_id)
#         except Exception as e:
#             error_log.error("自定义控制策略: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '自定义控制策略：控制策略不存在!'},
#                 }
#             )
#
#         category_month_instances = strategy_ins.month_set.filter(is_valid=0).all()
#         temp_detail = []
#         for detail in category_month_instances:
#             info = UserStrategyCategory.objects.get(id=detail.user_Strategy_Category.id)
#             info = info.__dict__
#             info['month'] = detail.month_number
#             if info.get('charge_config'):
#                 info['charge_config'] = eval(info.get('charge_config'))
#             if info.get('rl_list'):
#                 info['rl_list'] = eval(info.get('rl_list'))
#             del info['_state']
#             temp_detail.append(info)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": temp_detail},
#             }
#         )


# class CompareStationStrategyView(APIView):
#     """
#     默认策略比较
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         client = mtqq_station_strategy(station_id, month)
#         conn = get_redis_connection("3")
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#
#     def post(self, request):
#         ser = DefaultStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station_english_name = request.data['station']
#         now_time = datetime.datetime.now()
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_english_name, is_delete=0).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         month = request.data.get('month') if request.data.get('month') else now_time.month
#
#         default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#         default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#
#
#         station_id = station_instance.id
#         former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
#         former_actic_ids = [i.former_actic_id for i in former_actic_ids]
#
#         month_list = [month] if month != '-1' else [str(i) for i in range(1, 13)]
#         for month in month_list:
#             pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level, month)
#             # 做处理为了对应1-24点的计时
#             pv_list.append(pv_list[0])
#             pv_list.pop(0)
#             # 默认策略
#             former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
#             if not former_res:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
#                     }
#                 )
#
#             former_res = former_res.__dict__
#             # 实时策略
#             current_data = self._get_data(station_instance.id, station_instance.english_name, month)
#
#             default_charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
#             current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]
#
#             default_rl_list = dict()
#             for key in default_month_rl_keys:
#                 default_rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(former_res.get(key, 0) / former_res.get('power') * 100, 2)
#
#             current_rl_list = dict()
#             for key in current_month_rl_keys:
#                 current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(current_data.get(key, 0)) * 100, 2)
#
#             res = []
#             rl_list = list(zip(current_rl_list.values(), default_rl_list.values()))
#             charge_config = list(zip(current_charge_config, default_charge_config))
#             for i in range(24):
#                 if charge_config[i][0] == 0 and charge_config[i][1] == 0:
#                     continue
#                 if len(set(rl_list[i])) > 1 or (len(set(charge_config[i])) > 1):
#                     data = {}
#                     data['hours'] = i + 1
#                     data['default_rl'] = rl_list[i][1]
#                     data['default_charge'] = charge_config[i][1]
#                     data['current_rl'] = rl_list[i][0]
#                     data['current_charge'] = charge_config[i][0]
#                     data['pv'] = pv_list[i]
#                     data['month'] = month
#                     res.append(data)
#
#             if res:
#                 return Response(
#                                 {
#                                     "code": common_response_code.SUCCESS,
#                                     "data": {"message": "success", "detail": res},
#                                 }
#                             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": []},
#             }
#         )


# class CustomizeStationStrategyView(APIView):
#     """
#     自定义策略比较
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         client = mtqq_station_strategy(station_id, month)
#         conn = get_redis_connection("3")
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#
#     def post(self, request):
#         ser = CustomizeStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station_english_name = request.data['station']
#         strategy_id = int(request.data['strategy_id'])
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_name, is_delete=0).first()
#         station_info = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         detail = UserStrategy.objects.get(id=strategy_id)
#         if not detail:
#             error_log.error(f"用户自动控策略:策略不存在")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略:策略不存在."},
#                 }
#             )
#
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#         month_list = [str(i) for i in range(1, 13)]
#         for month in month_list:
#             pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
#             # 做处理为了对应1-24点的计时
#             pv_list.append(pv_list[0])
#             pv_list.pop(0)
#             # 自定义策略
#             month_detail = detail.month_set.filter(is_valid=0, month_number=int(month)).first()
#             if not month_detail:
#                 error_log.error(f"用户自动控策略:策略配置不完整")
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": "用户自动控策略:策略配置不完整"},
#                     }
#                 )
#             strategy_category = UserStrategyCategory.objects.get(id=month_detail.user_Strategy_Category.id)
#             customize_change_config = eval(strategy_category.charge_config)
#             customize_rl_list = eval(strategy_category.rl_list)
#
#
#             # 实时策略
#             current_data = self._get_data(station_info.id, station_info.english_name, month)
#
#             current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]
#
#             current_rl_list = dict()
#             for key in current_month_rl_keys:
#                 current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(current_data.get(key, 0)) * 100, 2)
#
#             res = []
#             customize_list = [float(i) for i in customize_rl_list.values()]
#             rl_list = list(zip(current_rl_list.values(), customize_list))
#             charge_config = list(zip(current_charge_config, customize_change_config))
#             for i in range(24):
#                 if charge_config[i][0] == 0 and charge_config[i][1] == 0:
#                     continue
#                 if len(set(rl_list[i])) > 1 or len(set(charge_config[i])) > 1:
#                     data = {}
#                     data['hours'] = i + 1
#                     data['customize_rl'] = rl_list[i][1]
#                     data['customize_charge'] = charge_config[i][1]
#                     data['current_rl'] = rl_list[i][0]
#                     data['current_charge'] = charge_config[i][0]
#                     data['pv'] = pv_list[i]
#                     data['month'] = month
#                     res.append(data)
#
#             if res:
#                 return Response(
#                                 {
#                                     "code": common_response_code.SUCCESS,
#                                     "data": {"message": "success", "detail": res},
#                                 }
#                             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": []},
#             }
#         )