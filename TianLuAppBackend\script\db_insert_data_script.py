import django
import os
from tools.send_mail import sendMail_
from django.conf import settings
from django_redis import get_redis_connection
from script.AES_symmetric_encryption import EncryptDate

#
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TianLuAppBackend.settings')
django.setup()

from apis.user import models

import datetime
import json

import time

import requests
from django.db.models import Sum, F, Max, Subquery
from django.db.models.functions import ExtractMonth
from tools.mqtt_tools import client as mqtt_client

# from faker import Faker
from datetime import date, timedelta

# fk = Faker(locale="zh_CN")

c = {
    "app": "TN001",
    "station": "NBLS001",
    "body": [
        {
            "device": "BMS",
            "datatype": "measure",
            "totalcall": "0",
            "body": ["NBSC", "BQ"]
        },
        {
            "device": "PCS",
            "datatype": "measure",
            "totalcall": "0",
            "body": ["ChaD", "DisD", "P", "Q"]
        },
        {
            "device": "PCS",
            "datatype": "status",
            "totalcall": "0",
            "body": ["Fault", "alarm"]
        },
        {
            "device": "EMS",
            "datatype": "status",
            "totalcall": "0",
            "body": ["AEnC"]
        },
        {
            "device": "PCS",
            "datatype": "measure",
            "totalcall": "0",
            "body": ["PP1"]
        }
    ]
}


def query():
    """获取实时功率"""
    user_list = {"纪雨": 18834401742,
                 "孙增福": 13810897522,
                 "王艳杰": 13030337255,
                 "李智": 15101649651,
                 "何传鑫": 18611739782,
                 "夏耀杰": 13701673770,
                 "刘明": 18513185086, }
    user_ins_list = []
    for k, v in user_list.items():
        station_1 = models.StationDetails.objects.filter(is_delete=0, id=1).first()
        station_2 = models.StationDetails.objects.filter(is_delete=0, id=2).first()
        ins = models.UserDetails.objects.create(user_name=k, mobile=v, password="6159dfdb831c7a296f187929843c80e3")
        ins.stations.add(station_1)
        ins.stations.add(station_2)

    # user_ins = models.UserDetails.objects.filter(id=1).first()
    # station_1 = models.StationDetails.objects.filter(id=1).first()
    # station_2 = models.StationDetails.objects.filter(id=2).first()
    # models.StationIncome.objects.create(peak_load_shifting=1000, demand_side_response=1000, station_id=station_1,
    #                                     income_date="2023-05-04", record=1)

    # user_ins.stations.add(station_1)
    # user_ins.stations.add(station_2)
    # c = models.StationDetails.objects.filter(userdetails=1)
    #
    # models.StationDetails.objects.create(
    #     station_name="宁波绿盛001", english_name="NBLS001", rated_power=100
    #     , rated_power_unit="kW", rated_capacity=200, rated_capacity_unit="kWh", unit_number=1,
    #     address="浙江省宁波市海曙区高桥镇石塘村", userdetails=1
    # )
    # models.StationDetails.objects.create(
    #     station_name="宁波绿盛002", english_name="NBLS002", rated_power=100
    #     , rated_power_unit="kW", rated_capacity=200, rated_capacity_unit="kWh", unit_number=1,
    #     address="浙江省宁波市海曙区高桥镇石塘村", userdetails=1
    # )
    # models.Unit.objects.create(unit_name="PCS", english_name='PCS',rated_power=100,user_id=1,station_id=2,
    #      rated_power_unit="kW", rated_capacity=200, rated_capacity_unit="kWh", pcs_number=1,)
    # models.StationDetails.objects.create(
    #     station_name="宁波绿盛002", english_name="NBLS002", rated_power=100
    #     , rated_power_unit="kW", rated_capacity=200, rated_capacity_unit="kWh", unit_number=1,
    #     address="浙江省宁波市海曙区高桥镇石塘村",
    # )
    # result = models.StationIncome.objects.filter(station_id__userdetails__id=16)
    # print(result)
    # c = models.StationDetails.objects.filter(english_name="station_1", station_name=None)
    # print(c)
    # station_list =

    station_id__english_name_list = []
    # query_dict = {
    #     # "station_id__english_name": ["station_2", "station_1", "station_3"],
    #     # "income_date": "2023-02-13",
    #     "income_type": 1
    # }
    # today = datetime.datetime.now()
    # year = today.year
    # month = today.month
    # query = models.StationIncome.objects.filter(**query_dict)
    # # query = models.StationIncome.objects.filter(**query_dict,income_date__year=year,income_date__month=month,
    # #                                             station_id__english_name__in=station_id__english_name_list).all().values(
    # #         "income_date__month").annotate(sum_month=Sum("day_income"))
    # # query = models.StationIncome.objects.filter(**query_dict, income_date__year=year, income_date__month=month,
    # #                                             station_id__english_name__in=station_id__english_name_list).all().values(
    # #     "income_date").annotate(sum_month=Sum("day_income"))
    #
    # dict_ins = query.filter(income_date__year=year,
    #                         station_id__userdetails__id=16,
    #                         income_date__month=month,
    #                         income_date__lte=datetime.datetime.now()).values(
    #     "income_date__month").annotate(
    #     sum_month=Sum("day_income")).values("sum_month",
    #                                         "income_date__month", "income_date__year").all()
    # print(dict_ins)
    # today = datetime.datetime.now()
    # year = today.year
    # # 构造今年第一天日期
    # # first_day = datetime.datetime(year=today.year, month=1, day=1)
    # # result = models.StationIncome.objects.filter(income_date__year=year, station_id__userdetails__id=16).values(
    # #     "income_date__month").annotate(sum_day=Sum("day_income")).values("sum_day","income_date__month")
    # # instance = models.StationIncome.objects.filter(income_date__year=year,
    # #                                                station_id__userdetails__id=16).values(
    # #     "income_date__month").annotate(sum_month=Sum("day_income")).values_list("sum_month",
    # #                                                                        "income_date__month")
    # # print(instance)
    # date = "2023-03-13"
    # date_obj = datetime.datetime.strptime(date, '%Y-%m-%d')
    #
    # year = date_obj.date().year
    # month = date_obj.date().month
    # instance_ = models.StationIncome.objects.filter(income_date__year=year,
    #                                                 station_id__userdetails__id=16,
    #                                                 income_date__month=month,
    #                                                 income_date__lte=datetime.datetime.now()).values(
    #     "day_income").annotate(
    #     sum_month=Sum("day_income")).values("sum_month",
    #                                         "income_date__month").all()
    # dict_instance = dict(instance_[0])
    # day_list = models.StationIncome.objects.filter(income_date__year=year, income_date__month=month,
    #                                                station_id__userdetails__id=16,
    #                                                income_date__lte=datetime.datetime.now()).values_list(
    #     "day_income", "income_date").all()
    # dict_instance["xxx"] = day_list
    # print(dict_instance)
    # my_result = my_data.annotate(month=ExtractMonth('date')).values('month').annotate(total=Sum('amount')).order_by(
    #     'month')

    # # models.StationIncome.objects.filter(id__gt=12984).delete()
    # start_data = date(2020, 1, 1)
    # # stations = models.StationDetails.objects.filter(userdetails__id=16).all()
    # # print(stations)
    # # for station in stations:
    # for j in range(2003, 2017):
    #     for i in range(1, 1500):
    #         models.StationIncome.objects.create(day_income=1000,
    #                                             income_date=start_data + timedelta(days=i), station_id_id=j)
    # print("xxxx")
    # print(type(fk.date_time_between(start_date="-3y", end_date="-1y")))

    # 站信息插入
    # user_instance = models.UserDetails.objects.filter(mobile=18804480007).first()
    # for i in range(5, 10):
    #     station_obj = models.StationDetails.objects.create(
    #         station_name=f"天禄{i}号",
    #         address=fk.address(),
    #         english_name=f"station_{i}"
    #
    #     )
    #
    #     print(station_obj)
    # user_instance.stations.add(station_obj)

    # for i in range(1001, 2001):
    #     obj = models.StationDetails.objects.filter(id=i).first()
    #     models.StationIncome.objects.create(day_income=1000, station_id=obj,
    #                                         income_date=fk.date_time_between(start_date="-3y"))

    # # 测量量测试数据添加
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=1, type="FLOAT32", data_description="Uab",
    #     point_english="Uab", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=2, type="FLOAT32", data_description="Ubc",
    #     point_english="Ubc", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=3, type="FLOAT32", data_description="Uca",
    #     point_english="Uca", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=4, type="FLOAT32", data_description="Ua",
    #     point_english="Ua", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=5, type="FLOAT32", data_description="Ub",
    #     point_english="Ub", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=6, type="FLOAT32", data_description="Uc",
    #     point_english="Uc", unit="kV"
    # )
    # models.Measure.objects.create(
    #     unit_description="储能送入柜子", english_name="ESFC", total_id=7, type="FLOAT32", data_description="Ia",
    #     point_english="Ia", unit="kV"
    # )
    # models_list = []
    # for i in range(1, 8):
    #     models_list.append(
    #         models.Unit(unit_name=f"储能单元xx", english_name=f"unit_00{i}", unit_status=1, rated_power="300kW",
    #                     rated_capacity="600kWh",
    #                     user_id=16, station_id=2002))
    # models.Unit.objects.bulk_create(models_list)


def get_post():
    """获取实时数据"""
    c = [0.3461, 0.3461, 0.3461, 0.3461, 0.3461, 0.3461, 0.6654, 0.6654, 0.6654, 0.6654, 0.6654, 0.6654, 0.6654, 0.6654,
         0.6654, 0.6654, 0.9847, 0.9847, 0.9847, 0.9847, 0.9847, 0.9847, 0.6654, 0.3461]
    in_dic = {}
    for i in range(len(c)):
        in_dic[f"h{i}"] = c[i]
    m = models.PeakValley.objects.create(year_month="2023-05-01", province="山西", **in_dic)
    m.save()
    # import datetime
    #
    # # 获取当前时间
    # today = datetime.date.today()
    #
    # # 使用timedelta计算前一天日期
    # yesterday = today - datetime.timedelta(days=1)
    # # 构造前一天最后一秒的时间
    # last_second = datetime.datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59)
    # # 获取前一天最后一秒的时间戳
    # end_timestamp = str(int(last_second.timestamp()))
    #
    # star_timestamp = str(int(
    #     time.mktime(datetime.datetime(yesterday.year, yesterday.month, yesterday.day, 0, 0, 1).timetuple())))
    # print(end_timestamp, star_timestamp)
    # url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
    # post_json = {
    #     "startTime": star_timestamp,
    #     "endTime": end_timestamp,
    #     "datatype": "measure",
    #     "app": "TN001",
    #     "order": "DESC",
    #     "pageSize": 1,
    #     "station": "NBLS001",
    #     "startPage": 1}
    # response = requests.post(url=url, json=post_json)
    # print(response.json())
    # if not response.json()['datas']:
    #     e = 0
    # else:
    #     c = response.json().get("datas").get("list")[0]["dataInfo"]
    #     d = json.loads(c)
    #     e = d["body"][-1]["CCcap"]
    #
    # print(e)


def get_today_power():
    import datetime
    import time
    now = time.time()
    # 获取当前时间
    today = datetime.date.today()

    # 构造今天第一秒的时间

    star_timestamp = str(int(
        time.mktime(datetime.datetime(today.year, today.month, today.day, 0, 0, 1).timetuple())))

    url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
    post_json = {
        "startTime": star_timestamp,
        "endTime": str(int(now)),
        "datatype": "measure",
        "app": "TN001",
        "order": "DESC",
        "pageSize": 2,
        "station": "NBLS001",
        "startPage": 1}
    response = requests.post(url=url, json=post_json)
    print(response.json())
    if not response.json()['datas']:
        e = 0
    else:
        data_list = response.json().get("datas").get("list")
        print(data_list)
        print(len(data_list))
        for i in range(len(data_list)):
            json_str = data_list[i]
            time = json_str.get('time')
            json_dic = json.loads(json_str.get('dataInfo'))

            P = json_dic["body"][1].get("P", 0)
            Q = json_dic["body"][1].get("Q", 0)
            print("=" * 100)
            print(time, P, Q)


def time_work():
    # 获取当前日期
    current_date = datetime.date.today()

    price = ins.values("h0").first()['h0']
    print(price)
    # income_price = income * price
    print(price)

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    print(current_date.day)
    for station in stations_ins:
        income_ins = models.StationIncome.objects.filter(station_id=station, income_date__day=current_date.day + 1,
                                                         income_date__month=current_date.month,
                                                         income_date__year=current_date.year).first()
        print(income_ins)
        # if not income_ins:
        #     models.StationIncome.objects.create()
        # print(exist)


def income():
    import datetime
    detail_list = []
    user_ins = models.UserDetails.objects.filter(id=1).first()
    now = datetime.date.today()
    query_dict = {
        "station_id__english_name": ["NBLS001", "NBLS002"],
        "date": ["2023", "5"],
        "income_type": 1
    }
    new_query_dict = {}
    date = query_dict.get("date", None)
    if date:
        if len(date) == 2:
            new_query_dict["income_date__month"] = date[1]
            new_query_dict["income_date__year"] = date[0]
        if len(date) == 1:
            new_query_dict["income_date__year"] = date[0]
    income_type = query_dict.get("income_type", None)
    if income_type:
        new_query_dict["income_type"] = income_type

    station_id__english_name = query_dict.get("station_id__english_name", None)

    stations_ins = models.StationDetails.objects.filter(is_delete=0, userdetails=user_ins, english_name__in=station_id__english_name,
                                                        ).first()
    income_ins = models.StationIncome.objects.filter(**new_query_dict, station_id=stations_ins)
    if len(date) == 1:
        year = date[0]
        months = 12
        if year == now.year:
            months = now.month

        for i in range(1, months + 1):
            return_dic = {"income_date__month": i, "income_date__year": now.year, }
            month_ins = income_ins.filter(income_date__month=i, income_date__year=now.year).annotate(
                last_entry=Max('id')).values('last_entry')

            day_ins = models.StationIncome.objects.filter(id__in=month_ins).values("income_date").annotate(
                peak_load_shifting=Sum("peak_load_shifting"),
                demand_side_response=Sum("demand_side_response")).values("income_date",
                                                                         "demand_side_response",
                                                                         "peak_load_shifting")
            return_dic["sum_month"] = 0
            return_dic["day"] = day_ins
            if day_ins:
                total_income_list = []
                for i in day_ins:
                    total_income_list.append(i["peak_load_shifting"])
                    total_income_list.append(i["demand_side_response"])
                return_dic["sum_month"] = sum(total_income_list)
                detail_list.append(return_dic)
    if len(date) == 2:

        year = date[0]
        month = date[1]
        return_dic = {"income_date__month": month, "income_date__year": now.year, }
        month_ins = income_ins.filter(income_date__month=month, income_date__year=year).annotate(
            last_entry=Max('id')).values('last_entry')

        day_ins = models.StationIncome.objects.filter(id__in=month_ins).values("income_date").annotate(
            peak_load_shifting=Sum("peak_load_shifting"),
            demand_side_response=Sum("demand_side_response")).values("income_date",
                                                                     "demand_side_response",
                                                                     "peak_load_shifting")
        return_dic["sum_month"] = 0
        return_dic["day"] = day_ins
        if day_ins:
            total_income_list = []
            for i in day_ins:
                total_income_list.append(i["peak_load_shifting"])
                total_income_list.append(i["demand_side_response"])
            return_dic["sum_month"] = sum(total_income_list)
            detail_list = return_dic
    print(detail_list)


def task():
    time_now = datetime.datetime.now()
    time_5 = datetime.datetime.now() + datetime.timedelta(minutes=5)
    tasks = models.StationPlanHistory.objects.filter(start_time__range=[time_now, time_5, ], status=1).all()
    print("发现任务计划", tasks)
    print(tasks)
    if tasks:
        for task in tasks:
            storage_name = task.station.english_name
            app = task.station.app

            # topic = f"req/database/parameter/{storage_name}/{app}"
            # print(topic)
            topic = f"req/database/parameter/SAMPLE1/TN002"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(task.station.english_name)
            power = task.power
            follow = task.power_follow
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [{"PCS": str(power), "type": "parameter"}, {"LLF": str(follow), "type": "parameter"}, ]
            }
            json_message = json.dumps(message)
            print(f"下发参数为{json_message}", "=============================")
            mqtt_client.publish(topic, json_message)
            print("历史任务下发成功", "=============================")
            task.status = 3
            task.save()


def insert_history():
    models.StationPlanHistory.objects.create(station_id=1, status=1, power_follow=1,
                                             start_time="2023-05-12 09:27:54.000000",
                                             end_time="2023-05-12 09:27:54.000000")


def sum_income():
    now = datetime.date.today()
    user_ins = models.UserDetails.objects.filter(id=1).first()
    stations_ins = models.StationDetails.objects.filter(is_delete=0, userdetails=user_ins)
    income_ins = models.StationIncome.objects.filter(station_id__in=stations_ins)
    income = income_ins.values("income_date").annotate(demand_side_responses=Sum("demand_side_response", ),
                                                       peak_load_shiftings=Sum("peak_load_shifting")).values(
        "peak_load_shiftings", "demand_side_responses")
    print(income)
    # month_ins = income_ins.filter(income_date__month=i, income_date__year=now.year)


def insert_income(date):
    current_date = date
    # 生成24个小时的时间戳
    timestamps = []

    for i in range(24):
        # 构造具体的日期和时间
        dt = datetime.datetime.combine(current_date, datetime.time(hour=i))
        # 转换为时间戳（以秒为单位）
        timestamp = int(dt.timestamp())
        timestamps.append(timestamp)
    print("定时任务时间构造完成================")
    yesterday = current_date + datetime.timedelta(days=1)
    yesterday_time_stamp = datetime.datetime.combine(yesterday, datetime.time(hour=0))
    yesterday_time_stamp = int(yesterday_time_stamp.timestamp())
    timestamps.append(yesterday_time_stamp)
    ins = models.PeakValley.objects.filter(year_month__month=current_date.month,
                                           year_month__year=current_date.year)
    url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        exists = models.StationIncome.objects.filter(station_id=station, income_date__day=current_date.day,
                                                     income_date__month=current_date.month,
                                                     income_date__year=current_date.year, record=1)
        if not exists:
            total = 0
            for i in range(len(timestamps) - 1):

                post_json = {
                    "startTime": str(int(timestamps[i])),
                    "endTime": str(int(timestamps[i + 1])),
                    "datatype": "cumulant",
                    "app": station.app,
                    "order": "DESC",
                    "pageSize": 1,
                    "station": station.english_name,
                    "startPage": 1}
                _post_json = {
                    "startTime": str(int(timestamps[i])),
                    "endTime": str(int(timestamps[i + 1])),
                    "datatype": "cumulant",
                    "app": station.app,
                    "order": "ASC",
                    "pageSize": 1,
                    "station": station.english_name,
                    "startPage": 1}
                response = requests.post(url=url, json=post_json)
                _response = requests.post(url=url, json=_post_json)
                print("定时任务数据库查询完成================")
                if not response.json()['datas']:

                    CuCha = 0
                    CuDis = 0
                else:
                    data = response.json().get("datas").get("list")[0]["dataInfo"]
                    info = json.loads(data)
                    CuCha = info["body"][-1]["CuCha"]  # 累计充电量
                    CuDis = info["body"][-1]["CuDis"]  # 累放电量

                if not _response.json()['datas']:

                    _CuCha = 0
                    _CuDis = 0
                else:
                    data = _response.json().get("datas").get("list")[0]["dataInfo"]
                    info = json.loads(data)
                    _CuCha = info["body"][-1]["CuCha"]  # 累计充电量
                    print(_CuCha, CuCha, "累计冲电量")
                    _CuDis = info["body"][-1]["CuDis"]  # 累放电量
                    print(_CuDis, CuDis, "累计放电量")

                CuCha_ = int(float(CuCha)) - int(float(_CuCha))
                CuDis_ = int(float(CuDis)) - int(float(_CuDis))

                income = int(float(CuDis_)) - int(float(CuCha_))
                print("充电量差值:", CuCha_, "放电量差值:", CuDis_, "充电量差值-放电量差值", income)
                price = ins.values(f"h{i}").first()[f"h{i}"]
                one_incom = float(price) * income
                total += one_incom
            print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元")
            if total < 0:
                total = 0
                print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元 小于 0")
            income_ins = models.StationIncome.objects.filter(station_id=station, income_date__day=current_date.day,
                                                             income_date__month=current_date.month,
                                                             income_date__year=current_date.year, record=2)
            if not income_ins:
                models.StationIncome.objects.create(peak_load_shifting=total, station_id=station,
                                                    income_date=current_date, income_type=1, record=2)
            else:
                income_ins.update(peak_load_shifting=total, income_type=1, record=2)
            print("定时任务数据库修改完成================")
            print("定时任务执行完成=========================")
        else:
            print("已存在手动添加的收入 不进行自动添加=====================")


def new_result(time):
    url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
    new_url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
    json_data = {
        "time": str(time),
        "datatype": "measure",
        "app": "TN001",
        "station": "NBLS002",
        "body": [
            {
                "device": "BMS",
                "body": [
                    "PAE",
                    "NAE"
                ]
            }
        ]}
    new_json = post_json = {
        "startTime": str(1683777643),
        "endTime": str(1683781243),
        "datatype": "measure",
        "app": "TN001",
        "order": "DESC",
        "pageSize": 1,
        "station": "NBLS001",
        "startPage": 1}
    print(json.dumps(json_data))
    response = requests.post(url=url, json=json_data)
    response = requests.post(url=new_url, json=new_json)
    return_dic = response.json()
    json_ = return_dic['datas']['list'][0]['dataInfo']
    print(json_)

    # datas = return_dic["datas"]
    # datas.sort(key=lambda s: s['body']["time"])
    # first_NAE = datas[0]['body']["data"][0]["NAE"]
    # last_NAE = datas[-1]['body']["data"][0]["NAE"]
    # first_PAE = datas[0]['body']["data"][0]["PAE"]
    # last_PAE = datas[-1]['body']["data"][0]["PAE"]
    # print(first_NAE, last_NAE, first_PAE, last_PAE)
    # DisD = int(last_PAE) - int(first_PAE)  # 今日放电量
    # ChaD = int(first_NAE) - int(last_NAE)  # 今日充电量
    # print("今日放电量:", DisD, "今日充电量:", ChaD)


def time_insert():
    time = models.StationPlanHistory.objects.filter(id=1).first()
    start_time = time.start_time
    end_time = time.end_time
    print(start_time, end_time)
    print(type(start_time), type(end_time))
    print(start_time.hour)


def new_request():
    request = {
        "time": "1683971728",
        "datatype": "cumulant",
        "app": "TN001",
        "station": "NBLS002",
        "body": [
            {
                "device": "BMS",
                "body": [
                    "CuCha",
                    "CuDis"
                ]
            }
        ]}
    print(json.dumps(request))
    url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
    response = requests.post(url=url, json=request)
    datas = response.json()['datas']
    new_list = sorted(c, key=lambda x: x["body"]["time"])
    print(new_list)








# def sed_email():
#     to_addrs = ["<EMAIL>", "<EMAIL>", "<EMAIL>",
#                 "<EMAIL>"]
#     url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#     stations_obj = models.StationDetails.objects.filter(is_delete=0).all()
#
#     for station in stations_obj:
#
#         json_data = {
#             "app": station["stations__app"],
#             "station": station["stations__english_name"],
#             "body": [
#
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault"]  # pcs故障
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["GFault", "ComFau"]  # ComFau压缩机故障  GFault pcs三级故障
#                 }
#             ]
#         }
#         response = requests.post(url=url, json=json_data)
#         return_dic = response.json()
#         body = return_dic.get("body", None)
#         if not body:
#             return "http调用安昌数据库查询接口失败"
#
#         Fault = body[0]['body'].get("Fault", "-999")  # 故障状态
#         GFault = body[1]['body'].get("GFault", "-999")  # ComFau压缩机故障
#         ComFau = body[1]['body'].get("ComFau", "-999")  # GFault pcs三级故障
#         message = f"天禄小程序故障警告:站名{station.station_name} 发生故障"
#         if str(GFault) == "1":
#             message = f"天禄小程序故障警告:站名{station.station_name} 发生故障, 故障为:pcs三级故障"
#         if str(ComFau) == "1":
#             message = f"天禄小程序故障警告:站名{station.station_name} 发生故障, 故障为:压缩机故障"
#
#         Subject = '电站告警'
#         sender_show = 'rhbess'
#         recipient_show = 'xxx'
#         if str(Fault) == "1":
#             conn = get_redis_connection("default")
#             exist = conn.get(station.station_name)
#             if not exist:
#                 sendMail_(message, Subject, sender_show, recipient_show, to_addrs)
#
#                 conn.set(station.station_name, "1", ex=60 * 12 * 60)  # redis 数据格式 monitor_mobile
#             if exist:
#                 print("有故障发送过短信 不进行重复发送")
#     print("定时发送短信任务完成")


def number_insert():
    stations = models.StationDetails.objects.filter(is_delete=0).all()
    print(stations)
    insert_phone_list = [13761107235,
                         18602175729,
                         15838260981,
                         13681868903,
                         15711611632,
                         15101754561,
                         18500230510,
                         18612300676,
                         15732622193]
    insert_name = ["施婕",
                   "王又佳",
                   "郭良合",
                   "夏雨",
                   "韩杰",
                   "吴晗",
                   "陈稳",
                   "王秀东",
                   "禄朋园", ]
    for i in range(len(insert_phone_list)):
        models.UserDetails(
            user_name=insert_name[i], login_name=insert_name[i], mobile=insert_phone_list[i],password="6159dfdb831c7a296f187929843c80e3", gender=3,

                           )


if __name__ == '__main__':
    # query()
    # get_post()
    # get_today_power()
    # time_work()
    # income()
    # print(json.dumps(c))
    # task()
    # insert_history()
    # sum_income()
    # insert_income("2023-05-05")
    # new_result(1683866683)
    #     time_insert()
    # new_request()

    # sed_email()
    number_insert()