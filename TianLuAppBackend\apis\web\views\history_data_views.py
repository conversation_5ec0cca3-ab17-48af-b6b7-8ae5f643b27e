import json
import os

from django.core.paginator import <PERSON><PERSON><PERSON>, PageNotAnInteger, EmptyPage
from django.db.models import Q
from django_redis import get_redis_connection

from apis.statistics_apis.db_link import time_range_by_dwd_for_web, time_range_by_dwd_for_web_v2
from common.constant import EMPTY_STR_LIST
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication, )
import settings
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from apis.user import models
from common import common_response_code
from apis.app2.utils import request_restrict_ip
import pandas as pd
from dbutils.persistent_db import PersistentDB
import pymysql
import datetime
import logging
import requests

from tools.minio_tool import MinioTool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_origin_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_origin_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_origin_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_origin_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_origin_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


class GetProjectListViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''获取项目名称及状态'''

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            # if 1:
            user_id = request.user["user_id"]
            success_log.info("项目疾控卡片页面: 普通查询")
            projects_obj = models.UserDetails.objects.filter(id=user_id, is_used=1).values(
                "project",  # 项目id
                "project__name",  # 项目名
                "project__english_name",  # 项目英文名
            )

            search_key = request.query_params.get('search_key', None)
            if search_key:
                projects_obj = [p for p in projects_obj if search_key in p["project__name"]]

            for project in projects_obj:
                Fault_list = []
                offline_list = []
                alarm_list = []
                # stations = models.StationDetails.objects.filter(project=project["project"]).all()
                units = models.Unit.objects.filter(is_delete=0, station__master_station__project=project["project"]).all()
                for unit in units:
                    alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station_id=unit.station.id).exists()
                    alarm_list.append(alarm_exist)
                    json_data = {
                        "app": unit.station.app,
                        "station": unit.station.english_name,
                        "body": [
                            {
                                "device": unit.pcs,
                                "datatype": "status",
                                "totalcall": "0",
                                "body": [
                                    "Fault",
                                    "alarm",
                                ],
                            },
                            {
                                "device": unit.bms,
                                "datatype": "status",
                                "totalcall": "0",
                                "body": [
                                    "GFault",
                                    "GAlarm",
                                ],
                            },
                        ],
                    }
                    # response = requests.post(url=url, json=json_data)
                    # return_dic = response.json()
                    # body = return_dic.get("body", None)

                    conn = get_redis_connection("3")

                    key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name,
                                                                   unit.bms)
                    measure_bms = conn.get(key1)
                    if measure_bms:
                        measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
                    else:
                        measure_bms_dict = {}

                    key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name,
                                                                   unit.pcs)
                    measure_pcs = conn.get(key2)
                    if measure_pcs:
                        measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
                    else:
                        measure_pcs_dict = {}

                    if not measure_pcs_dict or not measure_bms_dict:
                        offline_list.append(1)  # 离线状态

                    else:
                        GFault = measure_bms_dict.get("GFault") if measure_bms_dict.get("GFault") and measure_bms_dict.get("GFault") not in EMPTY_STR_LIST else -2  # bms故障状态
                        Fault = measure_pcs_dict.get("Fault") if measure_pcs_dict.get("Fault") and measure_pcs_dict.get("Fault") not in EMPTY_STR_LIST else -2  # pcs故障状态
                        if Fault and int(Fault) == 1:
                            Fault_list.append(1)
                        if GFault and int(GFault) == 1:
                            Fault_list.append(1)

                project["project_status"] = 1  # 正常
                if True in alarm_list:
                    project["project_status"] = 2  # 告警
                if 1 in Fault_list:
                    project["project_status"] = 3  # 故障
                if 1 in offline_list:
                    project["project_status"] = 4  # 离线
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": projects_obj,
                    },
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else 'Query failed.'},
            })


class GetPointListViews(APIView):
    '''获取并网点'''

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            p_id = request.data['id']  # 项目id
            station_ins = models.StationDetails.objects.filter(project_id=p_id,
                                                               master_station__is_delete=0, is_delete=0).all().order_by('id')
            dev_num_list = []

            for sta in station_ins:
                detail = {}
                detail['name'] = sta.master_station.name + ':' + sta.station_name  # 并网点name
                # detail['id']=sta.id#并网点id
                detail['ty'] = sta.id  # 项目类型
                dev_num_list.append(detail)
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": dev_num_list},
            })
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else 'Query failed.'},
            })


class GetDevNumViews(APIView):
    '''获取设备编号'''

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            s_id = request.data['ty']  # 从站id
            p_id = request.data['id']  # 项目id

            station = models.StationDetails.objects.get(id=s_id)
            ty = station.pcs_number
            dev_num_list = []

            # 传统标准站
            if station.slave == -1 and station.pack == -1:
                # 非标的标准站
                if int(p_id) == 5:
                    results = models.PointType.objects.filter(Q(type=int(ty)) | Q(type=0), is_stand=0).order_by('id')
                else:
                    results = models.PointType.objects.filter(Q(type=int(ty)) | Q(type=0), is_stand=1).order_by('id')
            else:
                # 非标的主从站，其实这个判断用不上
                if int(p_id) == 5:
                    results = models.PointType.objects.filter(type=int(ty), is_stand=0).order_by('id')
                else:
                    # 下面是标准主从站，EMS 在slave = 0的从站，pcs和bms在slave ！= 0 的从站
                    if station.slave == 0:
                        results = models.PointType.objects.filter(device='EMS', is_stand=1).order_by('id')[:1]
                    else:
                        # 兼容 EMS级联主从站模式
                        print(219, station.english_name)
                        # if station.english_name == station.master_station.english_name:
                        #     results = models.PointType.objects.filter(type=int(ty), is_stand=1).order_by('id')
                        # else:
                        #     results = models.PointType.objects.filter(type=int(ty), is_stand=1).exclude(device='EMS').order_by('id')

                        results = models.PointType.objects.filter(type=int(ty), is_stand=1).order_by('id')

            for re in results:
                detail = {'device': re.device, 'type_id': re.id}
                dev_num_list.append(detail)
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": dev_num_list},
            })
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else 'Query failed.'},
            })


class GetDataItemViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''获取数据项'''

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        station_id = request.data.get("station_id")  # 并网点(从站)ID
        descr = request.data.get("descr", '')  # 搜索
        type_id = request.data.get("type_id", '')  # 设备类型id
        ty = request.data.get("ty", '')  # 数据类型
        page = int(request.data.get("page", 1))
        page_size = int(request.data.get("page_size", 1000000))
        try:
            station = models.StationDetails.objects.get(id=station_id)
        except models.StationDetails.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "error", "detail": "并网点信息不存在" if lang == 'zh' else 'The station does not exist.'},
            })
        ty = int(ty)
        if station.slave == 0:
            version = 2
        else:
            version = 3 if station.unit_set.filter(is_delete=0).first().v_number == 3 else 2
        dev_num_list = []
        try:
            if ty == 1:  # 测量量
                if descr:
                    results = (models.PointMeasure.objects.filter(Q(description__contains=descr) | Q(en_description__contains=descr),
                                                                 point_type=type_id).values("name",
                                                                                            "description",
                                                                                            "en_description").
                               distinct().order_by('id'))
                else:
                    results = (models.PointMeasure.objects.filter(point_type=int(type_id)).values("name",
                                                                                                 "description",
                                                                                                 "en_description")
                               .distinct().order_by('id'))
            elif ty == 2:  # 状态量
                if descr:
                    results = (models.PointStatus.objects.filter(Q(description__contains=descr) | Q(en_description__contains=descr),
                                                                point_type=type_id).values("name",
                                                                                           "description",
                                                                                           "en_description").distinct().
                               order_by('id'))
                else:
                    results = (models.PointStatus.objects.filter(point_type=type_id).values("name",
                                                                                            "description",
                                                                                            "en_description").distinct().
                               order_by('id'))

            elif ty == 3:  # 累积量
                if descr:
                    results = (models.PointCumulant.objects.filter(Q(description__contains=descr) |
                                                                   Q(en_description__contains=descr),
                                                                   point_type=type_id).values("name",
                                                                                             "description",
                                                                                             "en_description").
                               distinct().order_by('id'))
                else:
                    results = (models.PointCumulant.objects.filter(point_type=type_id).values("name", "description",
                                                                                              "en_description").
                               distinct().order_by('id'))
            elif ty == 4:  #离散量
                if descr:
                    results = (models.PointDiscrete.objects.filter(Q(description__contains=descr) |
                                                                   Q(en_description__contains=descr), point_type=type_id).
                               values("name", "description", "en_description").distinct().order_by('id'))
                else:
                    results = (models.PointDiscrete.objects.filter(point_type=type_id).values("name", "description",
                                                                                              "en_description").
                               distinct().order_by('id'))
            results = results.filter(version=version)
            # 创建 Paginator 对象
            paginator = Paginator(results, page_size)
            try:
                # 获取当前页的数据
                results_page = paginator.page(page)
            except PageNotAnInteger:
                # 如果页码不是整数，返回第一页数据
                results_page = paginator.page(1)
            except EmptyPage:
                # 如果页码超出范围，返回最后一页数据
                results_page = paginator.page(paginator.num_pages)

            for re in results_page:
                detail = {}
                detail['name'] = re['name']  # 英文名称
                detail['descr'] = re['description'] if lang == 'zh' else re['en_description']  # 描述
                dev_num_list.append(detail)
            # dev_num_list = sorted(set(dev_num_list))
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": dev_num_list, "pageinfo": {"pages": paginator.num_pages, "total": paginator.count, "page": page, "page_size": page_size}},
            })

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else "No data found."},
            })


def get_time_points(start_time, end_time, m=5):
    # m：时间间隔默认5分钟
    # 将字符串转换为datetime对象
    # start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S").replace(second=0)
    # end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S").replace(second=0)
    start_dt = start_time.replace(second=0)
    end_dt = end_time.replace(second=0)

    # 计算开始时间点和结束时间点最接近的5分钟点
    # start_dt = start_dt + datetime.timedelta(minutes=(5 - (start_dt.minute % 5)))
    # end_dt = end_dt - datetime.timedelta(minutes=end_dt.minute % 5)
    # 初始化时间点列表
    time_points = []
    start_dt -= datetime.timedelta(seconds=start_dt.second, microseconds=start_dt.microsecond)
    if m == 5:
        # 减去多余的秒数和微秒数，使开始时间点精确到分钟
        # 如果开始时间不是整5分钟，则向上取整到下一个5分钟
        if start_dt.minute % 5 != 0:
            start_dt += datetime.timedelta(minutes=(5 - start_dt.minute % 5))

        # 减去多余的秒数和微秒数，使结束时间点精确到分钟
        end_dt -= datetime.timedelta(seconds=end_dt.second, microseconds=end_dt.microsecond)
        # 如果结束时间不是整5分钟，则向下取整到上一个5分钟
        if end_dt.minute % 5 != 0:
            end_dt -= datetime.timedelta(minutes=end_dt.minute % 5)

        # 循环遍历从开始时间到结束时间，每隔5分钟添加一个时间点
        current_dt = start_dt
        while current_dt <= end_dt:
            time_points.append(current_dt.strftime("%Y-%m-%d %H:%M:%S"))
            current_dt += datetime.timedelta(minutes=5)
    else:
        current_dt = start_dt
        while current_dt <= end_dt:
            time_points.append(current_dt.strftime("%Y-%m-%d %H:%M:%S"))
            current_dt += datetime.timedelta(minutes=m)
    return time_points


class GetHistoryDataViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''查询历史数据'''
    @request_restrict_ip()
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            # if 1:
            data = request.data
            ty = data.get('ty')  # 1测量量2状态量
            compute = data.get('compute')  # 1无聚合2算数平均值3最大值4最小值5差值
            # time_=300#间隔秒数
            ty = int(ty)
            if ty == 1:  # 测量量
                table_name = 'measure'
            elif ty == 2:  # 状态量
                table_name = 'status'
            elif ty == 3:  # 累积量
                table_name = 'cumulant'
            elif ty == 4:  # 离散量
                table_name = 'discrete'
            execl_data = {"时间": []} if lang == 'zh' else {"Time": []}
            # project_id = data.get('project_id')#项目id
            station_name = data.get('station_name').split(':')[1]                     #项目id
            dev_num_list = json.loads(data.get('dev_num_list'))#设备列表
            # project_ins = models.Project.objects.get(id=project_id)

            # station_ins = models.StationDetails.objects.filter(project=project_ins)
            stations = models.StationDetails.objects.filter(station_name=station_name, is_delete=0)
            if stations.exists():
                station = stations.first()
            else:
                return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else "No data found."},
            })

            data_node_list = json.loads(data.get('data_node_list'))  # 名称列表
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            start_time = data.get('start_time')
            end_time = data.get('end_time')
            start_time_t = start_date + " " + start_time
            end_time_t = end_date + " " + end_time

            start_datatime = datetime.datetime.strptime(start_time_t, "%Y-%m-%d %H:%M:%S")
            end_datatime = datetime.datetime.strptime(end_time_t, "%Y-%m-%d %H:%M:%S")
            # 峰谷套利
            if station.project.application_scenario == 0:
                m = 5
            else:
                m = 1
            time_points = get_time_points(start_datatime, end_datatime, m=m)
            if lang == 'zh':
                execl_data["时间"] = time_points
            else:
                execl_data["Time"] = time_points

            # temp_list = []
            last_dict = {}
            last_dict[station.english_name] = {}
            # history_data = self.get_history_data(table_name, station.app, station.english_name, start_time_t,
            #                                      end_time_t)
            device_type = dev_num_list[0][:3].lower()

            # 测量量对应bms有3张表
            # if table_name == 'measure':
            #     device_type = 'bms_3' if device_type == 'bms' else device_type

            for device in dev_num_list:
                last_dict[station.english_name][device] = {}

                args = [i['name'] for i in data_node_list]

                if station.english_name == 'NBLS001':
                    for i in range(len(args)):
                        if args[i] == "PAE":
                            args[i] = "CuDis"
                        if args[i] == "NAE":
                            args[i] = "CuCha"

                history_data = time_range_by_dwd_for_web_v2(station.english_name, table_name, device_type, device, start_datatime, end_datatime, time_points, *args)
                if history_data:
                    # history_data = sorted(history_data, key=lambda x: x['time'])
                    for data_node in data_node_list:
                        k = device + ':' + data_node['descr']
                        if k not in last_dict[station.english_name][device].keys():
                            last_dict[station.english_name][device][k] = []
                        if k not in execl_data.keys():
                            execl_data[k] = []

                        for time_point in time_points:
                            time_data = history_data.get(time_point)

                            if time_data:

                                if station.english_name == 'NBLS001':
                                    if data_node['name'] == "PAE":
                                        data_node['name'] = "CuDis"
                                    if data_node['name'] == "NAE":
                                        data_node['name'] = "CuCha"

                                if time_data.get(data_node['name']) is not None:
                                    detail = {
                                        'time': time_point.replace(' ', 'T'),
                                        'value': time_data.get(data_node['name'])
                                    }
                                    last_dict[station.english_name][device][k].append(detail)
                                else:
                                    detail = {
                                        'time': time_point.replace(' ', 'T'),
                                        'value': '--'
                                    }
                                    last_dict[station.english_name][device][k].append(detail)
                                execl_data[k].append(detail['value'])
                            else:
                                detail = {
                                    'time': time_point.replace(' ', 'T'),
                                    'value': '--'
                                }
                                last_dict[station.english_name][device][k].append(detail)
                                execl_data[k].append('--')

            file_name = f"{station_name}{start_date}~{end_date}历史数据.xlsx" if lang == 'zh' else\
                f"{station_name}{start_date}~{end_date}History data.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            # 生成excel文件之前处理：防止数据异常导致excel文件生成失败
            # 找到最短的列表长度
            min_length = min(len(lst) for lst in execl_data.values())

            # 调整每个值的列表长度
            for key, value in execl_data.items():
                if len(value) > min_length:
                    # 截断多余的部分
                    value = value[:min_length]
                execl_data[key] = value

            # for k, v in execl_data.items():
            #     print(525, k, len(v))
            # print(526, temp_list)
            df = pd.DataFrame(execl_data)

            df.to_excel(path, index=False)

            # 上传至minio
            try:
                minio_client = MinioTool()
                minio_client.create_bucket('download')
                url = minio_client.upload_local_file(file_name, path, bucket_name='download')
                os.remove(path)

                last_dict['url'] = url
            except Exception as e:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": f"上传文件报错：{e}" if lang == 'zh' else "Upload file error."},
                })

            return Response(last_dict)
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": f"查询报错：{e}" if e else "Query error."},
            })

    def get_history_data(self, table_name, app_name, station_name, start_time, end_time):


        conn = pool.connection()
        cursor = conn.cursor()
        try:
            # 执行SQL查询
            sql = """SELECT time, get_json_string(data_info,"$.body") as body 
                        FROM device_notify_{}_record 
                        WHERE 1=1
                        and app_name='{}'
                        and station_name='{}'
                        and time BETWEEN '{}' AND '{}'
                        ORDER BY time ASC
                        """.format(
                table_name, app_name, station_name, start_time, end_time
            )
            error_log.error(sql)
            try:
                cursor.execute(sql)
            except Exception as e:
                error_log.error(e)
                return []

            # 获取查询结果
            result = cursor.fetchall()
            body_result = []
            # 处理查询结果
            if not result:
                return []

            for row in result:
                if row:
                    body_result.append(row)
            return body_result

        except Exception as e:
            error_log.error(e)
            return []
        finally:
            try:
                cursor.close()
                conn.close()
            except Exception as e:
                pass
