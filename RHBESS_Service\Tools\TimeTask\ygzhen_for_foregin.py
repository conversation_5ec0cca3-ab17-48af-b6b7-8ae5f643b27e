#!/usr/bin/env python
# coding=utf-8
# @Information: 永臻接入英臻计量表
# <AUTHOR> WYJ
# @Date         : 2022-11-07 11:01:27
# @FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\Utils\alarm_save copy.py
# @Email        : <EMAIL>
# @LastEditTime : 2022-11-07 11:01:27
import os
import requests
import json, time

from Tools.DB.guizhou_his import guizhou1_session
from Tools.DB.shgyu_his import shgyu_session
from Tools.DB.ygqn_his import ygqn7_session
from Tools.Utils.time_utils import timeUtils
from Tools.DB.ygzhen_his import ygzhen1_session
from Application.Models.His.r_ACDMS import HisACDMS_YG
from Tools.DB.redis_con import r_real
import pymysql
import random
from apscheduler.schedulers.blocking import BlockingScheduler

# print  timeUtils.timeDiff('2022-11-07 10:00:00','2022-12-10 12:09:34')
# print timeUtils.betweenDayNum('2022-11-07 10:00:00','2022-12-10 12:09:34')
station=['ygzhen','taicgxr','guizhou','ygqn','shgyu','datong']
# station = ['guizhou']
dingding_url = "https://oapi.dingtalk.com/robot/send?access_token=d434f55a7a679e7230940f588fecfd35610c8a945386ce5465443fc705f44f95"

# 永臻token
yz_token = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kZXPvKnr4ZwLbV9rnkO-wL05tSx6ctn-Xf1vHjEwkfz7hH5YIOKPYGCDw88EEmOJvbtBF3x0RheFG1fi8v1JV0Mun-fP2MijBpp-0zZW4Q3jIAv5zL5B-Km98Lt24amnWHG2z-NOU6gANavSU5_QtWwDg1ue1g3KOVwCHsAgTdHXuADbcionG60hg9xphzw9iB3qfQ8YfRYYlVu2w43s9wMqrvtrA8eK5EsY2mHLHgoQrcxXc_BaTAssbtIh2F-ypbJkP9tbEUOWwU0LWXsfyyqB1UB8dpAuSlnGg6U5RQCwfuTFu3A-Qmv3hAocVyUXGW6OGH4bI2lrqFbgiQDGDg"
# 贵州token
gz_token = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# 阳泉token
yq_token = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# 上虞token
sy_token = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# 大同token
dt_token = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# 太仓鑫融
xr_token = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


orgid_obj = {"ygzhen":"140910","guizhou":"258317","ygqn":"280136","shgyu":"280138","datong":"258317","taicgxr":"140910"}
deviceSn_obj = {"ygzhen":"************","guizhou":"**********-************","ygqn":"**********-************","shgyu":"**********-************","datong":"**********-************",
                "taicgxr":"**********-************"}


ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
if ret == 0:
    token_url = "https://api.solarmanpv.com/account/v1.0/token"  # 获取token地址
    data_url = "https://api.solarmanpv.com/device/v1.0/currentData"  # 获取数据的url
else:  # 代理地址
    token_url = "http://************:50000/token_url/"  # 获取token地址
    data_url = "http://************:50000/data_url/"  # 获取数据的url

# print (ret,token_url,data_url)


# 请求基准头
base_header = {
    "Content-Type": "application/json",
    "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
}
def get_token(db):
    '''
    获取token接口
    返回的参数为字符型的字典格式
    '''
    global yz_token
    global gz_token
    global yq_token
    global sy_token
    global dt_token
    global xr_token
    
    try:
        orgId = orgid_obj[db]
        if db == 'ygzhen' or db == 'taicgxr':
            response = requests.post(
                url=token_url,
                params={
                    # "appId": "****************",
                    "appId": "****************",
                    "language": "zh",
                },
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                },
                data=json.dumps({
                    "countryCode": "86",
                    "orgId": orgId,
                    "mobile": "13701673770",
                    "password": "af51618704e8ae60a85ab5d71434ded3fbe68081e1fa437ca9ade839260504b7",
                    # "appSecret": "b05ae1826629374a229d2963775955cb"
                    "appSecret": "3b5ba8763a12d0eae41e355b9a440e88"
                })
            )
            # print('Response token HTTP Status Code: {status_code}'.format(
            #     status_code=response.status_code))
            # print('Response token HTTP Response Body: {content}'.format(
            #     content=response.content))
            if db == 'ygzhen':
                yz_token = json.loads(response.content)['access_token']
            if db == 'taicgxr':
                xr_token = json.loads(response.content)['access_token']
            
        elif db == 'guizhou' or db == 'datong':  # 大同，贵州共用一个账号

            response = requests.post(
                url=token_url,
                params={
                    "appId": "***************",
                    "language": "zh",
                },
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                },
                data=json.dumps({
                    "countryCode": "86",
                    "orgId": orgId,  
                    "mobile": "13030337255",
                    "password": "7833f4450f91f9220a46d1903f8b2b664bef0d852132fbe3a94df14079d6e093",
                    "appSecret": "2ca4ae0664b14151bdbd3e4ee90e47b3"
                })
            )
            # print('Response token HTTP Status Code: {status_code}'.format(
            #     status_code=response.status_code))
            # print('Response token HTTP Response Body: {content}'.format(
            #     content=response.content))
            if db == 'guizhou':
                gz_token = json.loads(response.content)['access_token']
            if db == 'datong':
                dt_token = json.loads(response.content)['access_token']
                print ('dt_token:',dt_token)

        elif db == 'ygqn':
            response = requests.post(
                url=token_url,
                params={
                    "appId": "202309261641146",
                    "language": "zh",
                },
                headers=base_header,
                data=json.dumps({
                    "countryCode": "86",
                    "orgId": orgId,
                    "mobile": "15101649651",
                    "password": "daaabbd274585a444a572a631397577a856bceee973c7bb0d6bf67657ecab919",
                    "appSecret": "d2f1c0c41fe4ad035fcd9a09b3f54acb"
                })
            )
            print('Response token HTTP Status Code: {status_code}'.format(
                status_code=response.status_code))
            print('Response token HTTP Response Body: {content}'.format(
                content=response.content))
            yq_token = json.loads(response.content)['access_token']
            # print('yq_token:', yq_token)
        elif db == 'shgyu':
            response = requests.post(
                url=token_url,
                params={
                    "appId": "202309269214148",
                    "language": "zh",
                },
                headers=base_header,
                data=json.dumps({
                    "countryCode": "86",
                    "orgId": orgId,
                    "mobile": "19033520994",
                    "password": "486d2fff536844e75bfc770b53515da3ceb78ce8c144844a3cff3074893aecb4",
                    "appSecret": "1f05e3f7f91bcccfec2bc2f069fe3613"
                })
            )
            print('Response token HTTP Status Code: {status_code}'.format(
                status_code=response.status_code))
            print('Response token HTTP Response Body: {content}'.format(
                content=response.content))
            sy_token = json.loads(response.content)['access_token']
            # print('sy_token:', sy_token)
    except requests.exceptions.RequestException:
        print('HTTP Request failed')


def get_real_data():
    global yz_token
    global gz_token
    global yq_token
    global sy_token
    global dt_token
    global xr_token
    print('time:', timeUtils.getNewTimeStr())
    for db in station:
        deviceSn = deviceSn_obj[db]
        try:
            if db == 'ygzhen':
                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % yz_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn,
                        # "deviceId":227767993   # 永臻
                    })
                )
            if db == 'taicgxr' :
                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % xr_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn,
                        # "deviceId":240330224  #  太仓鑫荣
                    })
                )
            elif db == 'guizhou':  # 大同，贵州共用一个账号:

                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % gz_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn,
                        # "deviceId":240232834

                    })
                )
            elif db == 'datong':  # 大同，贵州共用一个账号:
    
                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % dt_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn,
                        # "deviceId":240232834

                    })
                )
            elif db == 'ygqn':
                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },

                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % yq_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn
                    })
                )
            elif db == 'shgyu':
                response = requests.post(
                    url=data_url,
                    params={
                        "language": "zh",
                    },

                    headers={
                        "Content-Type": "application/json",
                        "Authorization": "bearer %s" % sy_token,
                        "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
                    },
                    data=json.dumps({
                        "deviceSn": deviceSn
                    })
                )
            # print ('旧token',token)
            # print('Response HTTP Response Body: {content}'.format(content=response.content))
            re = json.loads(response.content)
            code = re['code']
            if code == '2101017' or code == '2101019':  # token验证失败
                get_token(db)
                # print(u'新token:', dt_token)
                get_real_data()
            elif re['success']:  # 成功取得数据
                # data_save(re, db)
                idc_data_save(re, db)  # 写入idc一份
                # print ('***********',db,'--------------',re)
        except requests.exceptions.RequestException:
            print('HTTP Request failed')


def data_save(data, db):
    ots = timeUtils.getNewTimeStr()
    m = ots[0:7].replace('-', '')
    table = 'r_measure%s' % m
    if db == 'ygzhen':
        base_name = 'tfStygzhen1.EMS.MET.'
    elif db == 'guizhou':
        base_name = 'guizhou.EMS.MET.'
    elif db == 'ygqn':
        base_name = 'ygqn.EMS.MET.'
    elif db == 'shgyu':
        base_name = 'shgyu.EMS.MET.'

    HisTable = HisACDMS_YG(table)
    # nowt = str(time.time()).split('.')
    # dts_s = int(nowt[0])
    # dts_ms = int(nowt[1])
    if data['success']:
        datalist = data['dataList']
        dts_s = data['collectionTime']
        now_secs = timeUtils.nowSecs()  # 当前绝对秒

        if abs(now_secs - dts_s) >= 2 * 3600:  # 2小时数据没更新
            if db == 'ygzhen':
                send_msg_dingding("永臻结算电表  WARNING!   \n数据缺失,当前时间：{}".format(timeUtils.ssTtimes(dts_s)))
            elif db == 'guizhou':
                send_msg_dingding("贵州结算电表  WARNING!   \n数据缺失,当前时间：{}".format(timeUtils.ssTtimes(dts_s)))
            elif db == 'ygqn':
                send_msg_dingding("阳泉结算电表  WARNING!   \n数据缺失,当前时间：{}".format(timeUtils.ssTtimes(dts_s)))
        if db == 'ygzhen':
            for obj in datalist:
                k = obj['key']
                if k != 'SN1' and k != 'METERrate1' and k != 'METER_PTC_TYP1':  # 过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s' % (base_name, obj['key'])
                    print('name:', name, '----value:', obj['value'], '描述----', obj['name'])
                    oobj = {'value': obj['value'], 'desc': obj['name'], 'unit': obj['unit'], 'valueDesc': '',
                            'index': -1, 'time': ots}
                    r_real.hset("measure", name, str(oobj))
                    histab = HisTable(name=name, value=obj['value'], dts_s=dts_s, dts_ms=random.randint(100, 999),
                                      ots=ots, cause=4)
                    ygzhen1_session.merge(histab)
            ygzhen1_session.commit()
            ygzhen1_session.close()
        elif db == 'guizhou':
            for obj in datalist:
                k = obj['key']
                if k != 'SN1' and k != 'METERrate1' and k != 'METER_PTC_TYP1':  # 过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s' % (base_name, obj['key'])
                    print('name:', name, '----value:', obj['value'], '描述----', obj['name'])
                    oobj = {'value': obj['value'], 'desc': obj['name'], 'unit': obj['unit'], 'valueDesc': '',
                            'index': -1,
                            'time': ots}
                    r_real.hset("measure", name, str(oobj))
                    histab = HisTable(name=name, value=obj['value'], dts_s=dts_s, dts_ms=random.randint(100, 999),
                                      ots=ots,
                                      cause=4)
                    guizhou1_session.merge(histab)
            guizhou1_session.commit()
            guizhou1_session.close()

        elif db == 'ygqn':
            for obj in datalist:
                k = obj['key']
                if k != 'SN1' and k != 'METERrate1' and k != 'METER_PTC_TYP1':  # 过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s' % (base_name, obj['key'])
                    print('name:', name, '----value:', obj['value'], '描述----', obj['name'])
                    oobj = {'value': obj['value'], 'desc': obj['name'], 'unit': obj['unit'], 'valueDesc': '',
                            'index': -1,
                            'time': ots}
                    r_real.hset("measure", name, str(oobj))
                    histab = HisTable(name=name, value=obj['value'], dts_s=dts_s, dts_ms=random.randint(100, 999),
                                      ots=ots,
                                      cause=4)
                    ygqn7_session.merge(histab)
            ygqn7_session.commit()
            ygqn7_session.close()

        elif db == 'shgyu':
            for obj in datalist:
                k = obj['key']
                if obj['key'] == 'Et_pos1':
                    obj['key'] = 'Et_neg1'
                elif obj['key'] == 'Et_neg1':
                    obj['key'] = 'Et_pos1'
                if k != 'SN1' and k != 'METERrate1' and k != 'METER_PTC_TYP1':  # 过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s' % (base_name, obj['key'])
                    print('name:', name, '----value:', obj['value'], '描述----', obj['name'])
                    oobj = {'value': obj['value'], 'desc': obj['name'], 'unit': obj['unit'], 'valueDesc': '',
                            'index': -1,
                            'time': ots}
                    r_real.hset("measure", name, str(oobj))
                    histab = HisTable(name=name, value=obj['value'], dts_s=dts_s, dts_ms=random.randint(100, 999),
                                      ots=ots,
                                      cause=4)
                    shgyu_session.merge(histab)
            shgyu_session.commit()
            shgyu_session.close()


def idc_data_save(data, db):
    try:
        if db == 'ygzhen':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='readonly',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='ods_tfStygzhen1',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'tfStygzhen1.EMS.MET.'
        elif db == 'guizhou':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='readonly',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='ods_his_tc_guizhou1',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'guizhou.EMS.MET.'
        elif db == 'ygqn':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='readonly',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='odsHisTcYgqn7Ac',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'ygqn.EMS.MET.'
        elif db == 'shgyu':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='readonly',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='odsHisTfShgyu',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'shgyu.EMS.MET.'
        elif db == 'taicgxr':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='handle',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='odsHisTcTaicgxr',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'taicgxr.EMS.MET.'
        elif db == 'datong':
            conn = pymysql.connect(
                host='************',  # 连接主机, 默认127.0.0.1
                user='handle',  # 用户名
                passwd='rhyc123',  # 密码
                port=9300,  # 端口，默认为3306
                db='ods_his_tc_datong1',  # 数据库名称
                charset='utf8'  # 字符编码
            )
            ots = timeUtils.getNewTimeStr()
            base_name = 'datong.EMS.MET.'
        
        if data['success']:
            datalist = data['dataList']
            dts_s = data['collectionTime']
            for obj in datalist:
                k = obj['key']
                if obj['key'] == 'Et_pos1':
                    obj['key'] = 'Et_neg1'
                elif obj['key'] == 'Et_neg1':
                    obj['key'] = 'Et_pos1'
                if k != 'SN1' and k != 'METERrate1' and k != 'METER_PTC_TYP1':  # 过滤掉SN号，电表倍率，电表协议类型
                    name = '%s%s' % (base_name, obj['key'])
                    print('IDC-------------name:', name, '----value:', obj['value'], '描述----', obj['name'])
                    sql = "insert into ods_r_measure1 (name,value,dts_s,dts_ms,ots,cause) values ('{}',{},'{}',{},'{}',{})".format(
                        name, obj['value'], timeUtils.ssTtimes(dts_s), 0, ots, 4)
                    cursor = conn.cursor()
                    cursor.execute("SET group_commit = async_mode;")
                    cursor.execute(sql)
                    cursor.close()
        conn.close()
    except requests.exceptions.RequestException:
        print('HTTP Request failed-------IDC insert is errar')


def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(get_real_data, 'interval', seconds=900)  # 10分钟获取一次数据

    # scheduler.add_job(run_database, 'cron',  hour=1)  # 每天1点执行
    scheduler.start()


def get_ramain_num_yz():
    '''
    查看还剩余多少调用次数
    '''
    try:
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://api.solarmanpv.com/account/v1.0/balance'
        else:
            url = 'http://************:50000/yzsettle/'
        response = requests.post(
            url=url,
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s" % yz_token,
                "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "appId": "****************",
            })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def get_ramain_num_gz():
    '''
    查看还剩余多少调用次数
    '''
    try:
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://api.solarmanpv.com/account/v1.0/balance'
        else:
            url = 'http://************:50000/yzsettle/'
        response = requests.post(
            url=url,
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s" % gz_token,
                "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                "appId": "***************",
            })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def get_orgid():
    '''
    获取orgid
    根据token获取
    '''
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/account/v1.0/info",
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s" % dt_token,
                "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            # data=json.dumps({
            #     "appId": "**********-************",
            # })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def get_station_list():
    '''
    获取用户下站列表
    根据token获取
    '''
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/station/v1.0/list",
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s" % gz_token,
                "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                # "appId": "***************",
                "page":1
                
            })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def get_station_device_list():
    '''
    获取站设备列表
    根据token获取
    '''
    try:
        response = requests.post(
            url="https://api.solarmanpv.com/station/v1.0/device",
            params={
                "language": "zh",
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": "bearer %s" % gz_token,
                "User-Agent": "Paw/3.3.1 (Macintosh; OS X/12.0.1) GCDHTTPRequest"
            },
            data=json.dumps({
                # "stationId":2861430  # 
                "stationId":50688798  #
                
            })
        )
        print('Response HTTP Status Code: {status_code}'.format(
            status_code=response.status_code))
        print('Response HTTP Response Body: {content}'.format(
            content=response.content))
    except requests.exceptions.RequestException:
        print('HTTP Request failed')



def send_msg_dingding(content):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    data = {
        "msgtype": "text",
        "text": {
            "content": content
        },
        # "mentioned_list":["永臻"],
        "at": {
            "isAtAll": False
        }
    }
    r = requests.post(dingding_url, headers=headers, data=json.dumps(data))
    print('dingding告警信息-------------:', r.json())

if __name__ == '__main__':
    # get_station_list()
    # get_station_device_list()
    # get_orgid()
    # get_real_data()
    RunClearFileAndData()
    # get_token("taicgxr")

    # d = '{"code":null,"msg":null,"success":true,"requestId":"73b99c342f954845","deviceSn":"001537580774","deviceId":219807578,"deviceType":"METER","deviceState":1,"dataList":[{"key":"SN1","value":"001537580774","unit":null,"name":"SN号"},{"key":"METERrate1","value":"1","unit":null,"name":"电表倍率"},{"key":"METER_PTC_TYP1","value":"0","unit":null,"name":"电表协议类型"},{"key":"Pt1","value":"619.2","unit":"W","name":"有功总功率"},{"key":"Etdy_pos1","value":"4.06","unit":"kWh","name":"日正向总电能"},{"key":"Etdy_pos_sharp1","value":"0","unit":"kWh","name":"日正向尖电能"},{"key":"Etdy_pos_peak1","value":"0","unit":"kWh","name":"日正向峰电能"},{"key":"Etdy_pos_ord1","value":"1.63","unit":"kWh","name":"日正向平电能"},{"key":"Edty_pos_vly1","value":"2.44","unit":"kWh","name":"日正向谷电能"},{"key":"Etdy_neg1","value":"1.80","unit":"kWh","name":"日反向总电能"},{"key":"Edty_neg_sharp1","value":"0","unit":"kWh","name":"日反向尖电能"},{"key":"Edty_neg_peak1","value":"1.80","unit":"kWh","name":"日反向峰电能"},{"key":"Edty_neg_ord1","value":"0.01","unit":"kWh","name":"日反向平电能"},{"key":"Edty_neg_vly1","value":"0","unit":"kWh","name":"日反向谷电能"},{"key":"Et_pos1","value":"334.16","unit":"kWh","name":"总正向总电能"},{"key":"Et_pos_sharp1","value":"0.00","unit":"kWh","name":"总正向尖电能"},{"key":"Et_pos_peak1","value":"5.96","unit":"kWh","name":"总正向峰电能"},{"key":"Et_pos_ord1","value":"142.64","unit":"kWh","name":"总正向平电能"},{"key":"Et_pos_vly1","value":"185.56","unit":"kWh","name":"总正向谷电能"},{"key":"Et_neg1","value":"271.30","unit":"kWh","name":"总反向总电能"},{"key":"Et_neg_sharp1","value":"0.00","unit":"kWh","name":"总反向尖电能"},{"key":"Et_neg_peak1","value":"260.98","unit":"kWh","name":"总反向峰电能"},{"key":"Et_neg_ord1","value":"10.31","unit":"kWh","name":"总反向平电能"},{"key":"Et_neg_vly1","value":"0.00","unit":"kWh","name":"总反向谷电能"},{"key":"Et_cbn_a1","value":"605.47","unit":"kWh","name":"组合有功总电能"},{"key":"Et_cbn_a_sharp1","value":"0.00","unit":"kWh","name":"组合有功尖电能"},{"key":"Et_cbn_a_peak1","value":"266.94","unit":"kWh","name":"组合有功峰电能"},{"key":"Et_cbn_a_ord1","value":"152.95","unit":"kWh","name":"组合有功平电能"},{"key":"Et_cbn_a_vly1","value":"185.56","unit":"kWh","name":"组合有功谷电能"},{"key":"E_rat_cbn_sharp1","value":"0.00","unit":"kWh","name":"组合无功1尖电能"},{"key":"E_rat_cbn_peak1","value":"12.46","unit":"kWh","name":"组合无功1峰电能"},{"key":"E_rat_cbn_ord1","value":"13.16","unit":"kWh","name":"组合无功1平电能"},{"key":"E_rat_cbn_vly1","value":"15.55","unit":"kWh","name":"组合无功1谷电能"},{"key":"E_rat_cbn_sharp2","value":"0.00","unit":"kWh","name":"组合无功2尖电能"},{"key":"E_rat_cbn_peak2","value":"2.08","unit":"kWh","name":"组合无功2峰电能"},{"key":"E_rat_cbn_ord2","value":"5.00","unit":"kWh","name":"组合无功2平电能"},{"key":"E_rat_cbn_vly2","value":"4.70","unit":"kWh","name":"组合无功2谷电能"},{"key":"E_Ap_pos1","value":"161.76","unit":"kWh","name":"A相正向有功电能"},{"key":"E_Ap_neg1","value":"139.50","unit":"kWh","name":"A相反向有功电能"},{"key":"E_rat_Ap_cbn1","value":"109.33","unit":"kWh","name":"A相组合无功1电能"},{"key":"E_rat_Ap_cbn2","value":"76.52","unit":"kWh","name":"A相组合无功2电能"},{"key":"E_Bp_pos1","value":"0.00","unit":"kWh","name":"B相正向有功电能"},{"key":"E_Bp_neg1","value":"0.00","unit":"kWh","name":"B相反向有功电能"},{"key":"E_rat_Bp_cbn1","value":"0.00","unit":"kWh","name":"B相组合无功1电能"},{"key":"E_rat_Bp_cbn2","value":"0.00","unit":"kWh","name":"B相组合无功2电能"},{"key":"E_Cp_pos1","value":"172.88","unit":"kWh","name":"C相正向有功电能"},{"key":"E_Cp_neg1","value":"132.27","unit":"kWh","name":"C相反向有功电能"},{"key":"E_rat_Cp_cbn1","value":"84.97","unit":"kWh","name":"C相组合无功1电能"},{"key":"E_rat_Cp_cbn2","value":"88.37","unit":"kWh","name":"C相组合无功2电能"},{"key":"EPt_pos1","value":"41.18","unit":"KVarh","name":"总正向无功总电能"},{"key":"EQt_pos1","value":"11.78","unit":"KVarh","name":"总反向无功总电能"}]}'
    # dd = json.loads(d)
    # i = 0

    # data_save(dd)
    # send_msg_dingding("永臻结算电表  WARNING!   \n数据缺失测试内容{}".format(timeUtils.getNewTimeStr()))

