
#!/usr/bin/env python
# coding=utf-8
#@Information:项目签约
#<AUTHOR> WYJ
#@Date         : 2023-08-30 10:09:58
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_dict_project_type copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-30 10:10:00



from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseDictProjectsign(user_Base):
    '项目签约'
    __tablename__ = "t_side_forecase_dict_project_sign"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"名称")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=True,comment=u"排序索引")

    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        return "{'id':%s,'name':'%s'}" % (self.id,self.name)
        
    