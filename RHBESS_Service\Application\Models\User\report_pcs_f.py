#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-10 14:28:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report_f.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-30 11:52:43

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class FReportPcs(user_Base):
    u'冻结PCS功率因数'
    __tablename__ = "f_report_pcs"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    pcs_name = Column(VARCHAR(50), nullable=False,comment=u"PCS名称")
    max_p = Column(VARCHAR(256), nullable=True,comment=u"功率因数最大值")
    min_p = Column(VARCHAR(256), nullable=True,comment=u"功率因数最小值")
    # min_soh = Column(VARCHAR(256), nullable=True,comment=u"SOH最小值")
    day = Column(DateTime, nullable=False,primary_key=True,comment=u"数据时间")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    cause = Column(CHAR(2), nullable=False,comment=u"1日数据2月数据,3当天")
    station = Column(VARCHAR(50), nullable=False,comment=u"电站名称")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'pcs_name':'%s','max_p':'%s','min_p':'%s','day':'%s','op_ts':'%s','cause':'%s','station':'%s'}" % (
            self.id,self.pcs_name,self.max_p,self.min_p,self.day,self.op_ts,self.cause,self.station)
        
   