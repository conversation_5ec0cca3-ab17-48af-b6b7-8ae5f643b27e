package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.projectpack.ProjectPackCreateDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackQueryDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackUpdateDTO;
import com.robestec.analysis.entity.ProjectPack;
import com.robestec.analysis.mapper.ProjectPackMapper;
import com.robestec.analysis.service.ProjectPackService;
import com.robestec.analysis.vo.ProjectPackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目包服务实现类
 */
@Slf4j
@Service
public class ProjectPackServiceImpl extends SuperServiceImpl<ProjectPackMapper, ProjectPack>
        implements ProjectPackService {

    @Override
    public PageResult<ProjectPackVO> queryProjectPack(ProjectPackQueryDTO queryDTO) {
        LambdaQueryWrapper<ProjectPack> wrapper = new LambdaQueryWrapper<ProjectPack>()
                .eq(queryDTO.getUserId() != null, ProjectPack::getUserId, queryDTO.getUserId())
                .like(StringUtils.hasText(queryDTO.getName()), ProjectPack::getName, queryDTO.getName())
                .eq(queryDTO.getIsUse() != null, ProjectPack::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), ProjectPack::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), ProjectPack::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(ProjectPack::getCreateTime);

        Page<ProjectPack> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<ProjectPack> result = this.page(page, wrapper);

        List<ProjectPackVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<ProjectPackVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProjectPack(ProjectPackCreateDTO createDTO) {
        // 检查同一用户下项目包名称是否重复
        long count = this.count(Wrappers.<ProjectPack>lambdaQuery()
                .eq(ProjectPack::getName, createDTO.getName())
                .eq(ProjectPack::getUserId, createDTO.getUserId()));
        if (count > 0) {
            throw new RuntimeException("项目包名称已存在");
        }

        ProjectPack entity = BeanUtil.copyProperties(createDTO, ProjectPack.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectPack(ProjectPackUpdateDTO updateDTO) {
        ProjectPack entity = BeanUtil.copyProperties(updateDTO, ProjectPack.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectPack(Long id) {
        this.removeById(id);
    }

    @Override
    public ProjectPackVO getProjectPack(Long id) {
        ProjectPack entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createProjectPackList(List<ProjectPackCreateDTO> createDTOList) {
        List<ProjectPack> entityList = BeanUtil.copyToList(createDTOList, ProjectPack.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<ProjectPackVO> getProjectPackByUserId(Long userId) {
        List<ProjectPack> entityList = this.list(Wrappers.<ProjectPack>lambdaQuery()
                .eq(ProjectPack::getUserId, userId)
                .orderByDesc(ProjectPack::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<ProjectPackVO> getProjectPackByName(String name) {
        List<ProjectPack> entityList = this.list(Wrappers.<ProjectPack>lambdaQuery()
                .like(ProjectPack::getName, name)
                .orderByDesc(ProjectPack::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByUserId(Long userId) {
        return this.count(Wrappers.<ProjectPack>lambdaQuery()
                .eq(ProjectPack::getUserId, userId));
    }

    /**
     * 转换为VO对象
     */
    private ProjectPackVO convertToVO(ProjectPack entity) {
        if (entity == null) {
            return null;
        }
        ProjectPackVO vo = BeanUtil.copyProperties(entity, ProjectPackVO.class);
        vo.setIsUseName(getIsUseName(entity.getIsUse()));
        return vo;
    }

    /**
     * 获取是否使用名称
     */
    private String getIsUseName(Integer isUse) {
        if (isUse == null) {
            return "";
        }
        return isUse == 1 ? "使用" : "不使用";
    }
}
