#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ
# @Date         : 2024-04-17 12:02:45
# @FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\pdf_shuiyin.py
# @Email        : <EMAIL>
# @LastEditTime : 2024-05-10 13:22:05

# -*- coding: utf-8 -*-
import reportlab
import time
from PyPDF2 import PdfFileWriter, PdfFileReader
from reportlab.pdfgen import canvas
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import cm

from Tools.Utils.time_utils import timeUtils


def create_watermark(content,
                     rotate=45,
                     N_space_between_content=5,
                     alpha=0.3,
                     fontsize=12):
    '''添加水印模板'''
    reportlab.pdfbase.pdfmetrics.registerFont(
        reportlab.pdfbase.ttfonts.TTFont('Arial', '/home/<USER>/RHBESS_Service/Application/Static/msyh.ttf'))
        # reportlab.pdfbase.ttfonts.TTFont('Arial', '../../Application/Static/msyh.ttf'))
    # c = canvas.Canvas('../../Application/Static/watermark.pdf')
    now_time = timeUtils.getNewTimeStr()
    path='/home/<USER>/RHBESS_Service/Application/Static/'+now_time+'.pdf'
    c = canvas.Canvas(path)
    c.setFont('Arial', fontsize)
    c.setFillAlpha(alpha)
    c.rotate(rotate)
    for i in range(5):
        c.drawCentredString(0 * cm, (15 - i * 10) * cm, (content + "       " * N_space_between_content) * 200)
    c.save()
    # pdf_watermark = PdfFileReader(open('../../Application/Static/watermark.pdf', 'rb'))
    pdf_watermark = PdfFileReader(open(path, 'rb'))
    return path


def add_watermark(pdf_file_in, pdf_file_out, pdf_file_mark='../../Application/Static/watermark.pdf'):
    '''
    pdf_file_in:输入文件路径和名称
    pdf_file_out：输出文件路径和名称
    pdf_file_mark：使用水印模板
    '''
    # print(pdf_file_out)
    pdf_output = PdfFileWriter()
    input_stream = open(pdf_file_in, 'rb')
    pdf_input = PdfFileReader(input_stream, strict=False)
    # 获取PDF文件的页数
    if pdf_input.getIsEncrypted():
        print("文件已被加密")
        PDF_Passwd = input("请输入PDF密码：")
        # 尝试用空密码解密
        try:
            pdf_input.decrypt(PDF_Passwd)
        except Exception:
            print(f"尝试用密码{PDF_Passwd}解密失败.")
            return False
    pageNum = pdf_input.getNumPages()
    # 读入水印pdf文件
    # print(pdf_file_mark,'****************')
    mark_stream = open(pdf_file_mark, mode='rb')
    pdf_watermark = PdfFileReader(mark_stream, strict=False)
    # 给每一页打水印
    for i in range(pageNum):
        page = pdf_input.getPage(i)
        page.mergePage(pdf_watermark.getPage(0))
        page.compressContentStreams()  # 压缩内容
        pdf_output.addPage(page)
    pdf_output.write(open(pdf_file_out, 'wb'))
    return pdf_file_out

# if __name__ == '__main__':
    # create_watermark("融和刘文娟")
#     # pdf_file_mark = 'watermark.pdf'  # 水印文件
#     pdf_file_out = '水印版2.pdf'  # 添加PDF水印后的文件
#     add_watermark("中能融和对接-分析接口说明_20240509112730.pdf", pdf_file_out)
#     # print("水印添加结束，页面将在15秒内关闭！")
#     # time.sleep(15)


