#!/usr/bin/env python
# coding=utf-8
#@Information:  初始化工单系统数据库
#<AUTHOR> WYJ
#@Date         : 2022-10-26 11:50:51
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Running\WorkOrder\init_work_order.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-26 11:52:08

from Tools.Utils.time_utils import timeUtils
from Tools.DB.mysql_user import user_Base,user_session



def pg_create():
    '''
    创建数据库数据库
    数据库必须有mysql供连接
    所有数据库必须在一个服务器上
    '''
    import os
    from sqlalchemy import create_engine
    from Application.Cfg.dir_cfg import model_config
    print ('开始创建数据库……')
    

    USER_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
    USER_PORT = model_config.get('mysql', "DB_PORT")
    USER_DATABASE = model_config.get('mysql', "DB_DATABASE")
    USER_USERNAME = model_config.get('mysql', "DB_USERNAME")
    USER_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

    userdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
        USER_USERNAME,
        USER_PASSWORD,
        USER_HOSTNAME,
        USER_PORT,
        USER_DATABASE
    )

    engine = create_engine(userdb_mysql_url,isolation_level="AUTOCOMMIT",
                       echo=(os.getenv('DEBUG_DB', 'null') != 'null'),
                       max_overflow=10, pool_size=100, pool_timeout=30, pool_recycle=-1)
    conn = engine.connect()
    # conn.execute("commit")
    db_cfg = ['DB_DATABASE','HIS_DATABASE','USER_DATABASE']
    for db in db_cfg:
        pg = model_config.get('mysql', db)
        sql = "SELECT SCHEMA_NAME  FROM information_schema.SCHEMATA  where SCHEMA_NAME='%s'"%pg
        f = conn.execute(sql).first()
        if f:
            print ('数据库 %s 已存在'%str(pg))
            continue
        conn.execute("create database %s"%(pg))
       
    conn.close()
    print ('数据库创建成功！')


if __name__ == '__main__':
    pg_create()
    print ('初始化所有表和用户')
    # 初始化所有工单
    from Application.Models.WorkOrder import *
    create_all()
    # 初始化用户
    print ('初始化完成')