#!/usr/bin/env python
# coding=utf-8
#@Information:预测函数
#<AUTHOR> WYJ
#@Date         : 2023-01-11 15:54:26
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseCalculateHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-11 15:57:03

import os
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import *
from Tools.Utils.scipopt_util_day import calrate,power_calculate,power_calculate2
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_area import ForecaseArea
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_price import ForecasePrice
from Tools.Utils.time_utils import timeUtils
import numpy as np
np.set_printoptions(suppress=True)  # 不用科学计数表示
import math
import random
import logging,json
from sqlalchemy import func
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
# eff_charge = 0.92  # 充电效率
# eff_discharge = 0.92  # 放电效率
# p_max = 1000  #交流侧最大功率（kW）
# Cycle = 0.5  #系统设计循环倍率X
# DOD = 0.95  #充放深度
# s_max = p_max/Cycle;  #直流侧最大容量（kWh）
# SS = s_max*DOD  # 直流侧有效容量
# # 一天的电价
# # price=[0.4276,0.4276,0.4276,0.4276,0.4276,0.4276,0.4276,0.9386,0.9386,0.9386,1.5556,1.5556,1.5556,1.5556,0.9386,0.9386,0.9386,1.5556,1.8321,1.5556,1.5556,0.9386,0.9386,0.4276]
# H0 = 3100  #变压器容量/最大需量 KVA
# base_price= 0.33  # 放电价差，0.3基数除放电效率
# w_day = np.ones((24,1))*0.5  # 一天的负荷

class SideForecaseCalculateHandleIntetface(BaseHandler):
    # @tornado.web.authenticated
    def get(self,kt):
        # self.refreshSession()
        try:
            if kt == 'BeginnerCalculatePower':  # 小白功率测算
                factory_arr = [0,1.4,1.2,1.0]
                #factory = self.get_argument('factory',1) # 1工坊2倍；2工厂1.8倍；3园区1.9倍
                factory = self.get_argument('factory',1) # 1小型；2中型；3大型
                load_peak = float(self.get_argument('load_peak',0.5)) # 尖峰负荷率
                load_valley = float(self.get_argument('load_flat',0.5)) # 平负荷率 和谷对调
                load_flat = float(self.get_argument('load_valley',0.5)) # 谷负荷率 和平对调
                H0 = float(self.get_argument('H0',0.1)) # 变压器容量
                rp1 = H0*load_peak
                rp2 = H0*(1-load_valley)
                rp = rp1 if rp1<rp2 else rp2  # 交流测变压器最大功率()
                s_max = rp*2  # 电池容量
                forecast_cost = factory_arr[factory]*s_max*100
                obj = {"gonglv":round(rp,1),"rongliang":round(s_max,1),"yugutouzi":round(forecast_cost,1)}
                return self.returnTypeSuc(obj)

            elif kt == 'BeginnerCalculate':  # 小白测算
                now_time = timeUtils.getNewTimeStr()  # 获取当前时间
                factory_arr = [0,1.4,1.2,1.0]
                province_id = self.get_argument('province_id',None) # 省份id
                ele_id = self.get_argument('ele_id',None) # 用电分类id
                vol_id = self.get_argument('vol_id',None) # 电压等级id
                p_max = float(self.get_argument('H0',0.1))*1000 # 变压器容量
                factory = int(self.get_argument('factory',1)) # 1工坊1.8倍；2工厂1.6倍；3园区1.7倍
                load_peak = float(self.get_argument('load_peak',50))/100 # 峰负荷率
                load_flat = float(self.get_argument('load_flat',50))/100 # 平负荷率
                load_valley = float(self.get_argument('load_valley',50))/100 # 谷负荷率
                load_jianfeng = float(self.get_argument('load_jianfeng',50))/100 # 尖峰负荷率
                Cycle = float(self.get_argument('Cycle',2))  # 系统设计循环倍率,对应储能时长
                share = float(self.get_argument('share',85))/100  # 分享比例
                ah_ele = int(self.get_argument('ah_ele',1))  # 容量电费  1按容收费 2 按需收费
                best_mode = int(self.get_argument('best_mode',0))  # 0为度电收益最佳的最大容量（投资回收最快）1为满足收益率要求下的最大容量（总收益最大化）
                if DEBUG:
                    logging.info('province_id:%s,ele_id:%s,vol_id:%s,factory:%s,load_peak:%s,load_flat:%s,load_valley:%s,p_max:%s,Cycle:%s,share:%s,load_jianfeng:%s,best_mode:%s'%(
                        province_id,ele_id,vol_id,factory,load_peak,load_flat,load_valley,p_max,Cycle,share,load_jianfeng,best_mode))
                if not province_id or not ele_id or not vol_id :
                    return self.customError('参数不完整')
                if load_jianfeng == 0 and load_peak == 0 and load_flat == 0 and load_valley == 0:
                    return self.customError('非法入参')
                
                filte = [ForecasePrice.is_use==1,ForecasePrice.part_id==1]
                filte.append(ForecasePrice.province_id==province_id)
                filte.append(ForecasePrice.ele_id==ele_id)
                filte.append(ForecasePrice.vol_id==vol_id)
                pages = user_session.query(ForecasePrice).filter(*filte).order_by(ForecasePrice.year_month.asc()).all()
                Y,price_12month = [],[]  # 12月电价峰谷标识,12月电价
                for p in pages:
                    pv = [p.pv0,p.pv1,p.pv2,p.pv3,p.pv4,p.pv5,p.pv6,p.pv7,p.pv8,p.pv9,p.pv10,p.pv11,p.pv12,p.pv13,p.pv14,p.pv15,p.pv16,p.pv17,p.pv18,p.pv19,p.pv20,p.pv21,p.pv22,p.pv23]
                    Y.append(pv)
                    price_12month.append([p.h0,p.h1,p.h2,p.h3,p.h4,p.h5,p.h6,p.h7,p.h8,p.h9,p.h10,p.h11,p.h12,p.h13,p.h14,p.h15,p.h16,p.h17,p.h18,p.h19,p.h20,p.h21,p.h22,p.h23])
                # print 'Y:',Y
                price_12month = np.array(price_12month).astype(np.float)  # 全年电价二维矩阵
                # rp,s_max = power_calculate(Y,load_jianfeng,load_peak,load_flat,load_valley,p_max,ah_ele,Cycle)

                # Cycle = 1.0/Cycle # 系统设计循环倍率
                eff_charge = 0.92 # 充电效率
                eff_discharge = 0.92 # 放电效率
                DOD = 0.95 # 充放深度
                # rp1 = p_max*load_peak
                # rp2 = p_max*(1-load_valley)
                # rp = rp1 if rp1<rp2 else rp2  # 交流测PCS最大功率
                # s_max = rp/Cycle  # 电池标定容量
                # print '------------',rp,s_max
                # print '__________',timeUtils.getNewTimeStr()
                rp,s_max,shichang = power_calculate2(Y,price_12month,load_jianfeng,load_peak,load_flat,load_valley,p_max/1000,ah_ele,Cycle,DOD,eff_charge,eff_discharge,best_mode+1,share)

                if ah_ele == 2:  # 按需
                    H1 = max([load_jianfeng,load_peak,load_flat,load_valley])*p_max
                else:
                    H1 = p_max
                
                SS = s_max*DOD  # 电池有效容量 标定容量乘放电深度
                H0 = p_max  # 变压器容量/最大需量 KVA 
                forecast_cost = round(factory_arr[factory]*rp*Cycle/10,2)

                # w_day = np.ones((24,1))*((load_peak+load_flat+load_valley)/3)

                
                # m_price 购电价差；save_cost 总节省电费；m_power 功率曲线；
                priceall,fuheall = [],[]  # 当月负荷曲线  一年内的电价,全年负荷
                if pages:
                    # print '************计算套利参数：',p_max,eff_charge,eff_discharge,w_day,H0,SS
                    taoli_arr,save_cost,m_price,m_power,data = self._taoli_fun(pages,rp,eff_charge,eff_discharge,load_peak,load_flat,load_valley,load_jianfeng,H0,SS,share,H1)
                    # print 'taoli_arr:::::::::::::',taoli_arr
                    
                    cycl_num = taoli_arr[2][0]  # 循环次数
                    
                    ## 电站矩阵
                    station_arr = self._station_fun(rp/1000,1.0/Cycle,95.0,92.0,92.0,8000.0,15,30.0,5.0,330)
                    # print '******',station_arr
                    # 静态投资矩阵； 补贴总数;每年补贴数
                    static_arr,subsidy_num,subsidy_one_year = self._static_fun(station_arr,taoli_arr,0,0,2,900,factory_arr[factory])
                    # print '--------------',static_arr
                    # 融资方案
                    financing_arr = self._financing_plan_fun(station_arr,static_arr)
                    # print '&*&*&*&*&*&*',financing_arr
                    # 设备维护费用
                    s = s_max/1000.0
                    if s>=30:
                        pa = 4
                    elif s<30 and s>=20:
                        pa = 3
                    elif s<20 and s>=10:
                        pa = 2
                    else:
                        pa=1
                    devop_arr = self._devope_fun(static_arr,factory_arr[factory],pa)
                    # print '$$$$$$$$$$$$$$',devop_arr
                    #  计算负荷曲线和原预估电费
                    n_m = int(now_time[5:7])
                    
                    for p in pages:
                        pv = [p.pv0,p.pv1,p.pv2,p.pv3,p.pv4,p.pv5,p.pv6,p.pv7,p.pv8,p.pv9,p.pv10,p.pv11,p.pv12,p.pv13,p.pv14,p.pv15,p.pv16,p.pv17,p.pv18,p.pv19,p.pv20,p.pv21,p.pv22,p.pv23]
                        fuhe_line = []
                        for i in range(24):
                            if pv[i]<=-1:  # 谷
                                fuhe_line.append(p_max*load_valley/1000)
                            elif pv[i]==1:  # 峰
                                fuhe_line.append(p_max*load_peak/1000)
                            elif pv[i]==2:  # 尖峰
                                fuhe_line.append(p_max*load_jianfeng/1000)
                            else:  # 平
                                fuhe_line.append(p_max*load_flat/1000)
                        fuheall.append(fuhe_line)
                        priceall.append([p.h0,p.h1,p.h2,p.h3,p.h4,p.h5,p.h6,p.h7,p.h8,p.h9,p.h10,p.h11,p.h12,p.h13,p.h14,p.h15,p.h16,p.h17,p.h18,p.h19,p.h20,p.h21,p.h22,p.h23])

                    # 经济测算
                    part_arr = self._part_fun(station_arr,static_arr,financing_arr,devop_arr,taoli_arr,0,subsidy_one_year)

                    chongfang,shouyi2 = [],[]
                    vv1,vv2,vv3,vv4,vv5,vv6=[],[],[],[],[],[]   # 计算合计使用
                    jiessheng_arr = []  # 节省电费
                    
                    for i in range(1,13):
                        d = taoli_arr[3][i]
                        chag = round(-taoli_arr[7][i]*1000/d,2)
                        disg = round(taoli_arr[8][i]*1000/d,2)
                        chag_cost = round(-taoli_arr[10][i]*10000/d*share,2)
                        disg_cost = round(taoli_arr[12][i]*10000/d*share,2)
                        cost = round((taoli_arr[10][i]+taoli_arr[12][i])*10000/d*share,2)
                        chongfang.append({"chag":chag,"disg":disg,"chag_cost":chag_cost,"disg_cost":disg_cost,"cost":cost})
                        # 首年分月预估
                        vv1.append(taoli_arr[14][i]*0.9041)
                        jiessheng_arr.append(taoli_arr[14][i]*0.9041)
                        vv2.append(taoli_arr[4][i]*0.9041)
                        vv3.append(taoli_arr[6][i]/10*0.9041)
                        shouyi2.append({"cost":round(taoli_arr[14][i]*0.9041*share,2),"num":round(taoli_arr[4][i]*0.9041,0),"dianliang":round(taoli_arr[6][i]/10*0.9041,2)})
                    shouyi2.append({"cost":round(np.sum(vv1)*share,2),"num":round(np.sum(vv2),0),"dianliang":round(np.sum(vv3),2)})
                    cycl_price =  round(np.sum(vv1)/np.sum(vv3)*share,2) if np.sum(vv3) else 0 #循环价差  乘分享比例
                    shouyi3,jishu,jishu2,jishu3=[],1,1,1
                    for i in range(1,11):  # 十年收益计算
                        vv4.append(round(np.sum(vv1)*jishu,2))
                        vv5.append(round(np.sum(vv2),0))
                        vv6.append(round(np.sum(vv3)*jishu,2))
                        shouyi3.append({"cost":round(np.sum(vv1)*jishu*share,2),"num":round(np.sum(vv2),0),"dianliang":round(np.sum(vv3)*jishu,2)})
                        jishu = jishu-(1-0.8)/9
                    shouyi3.append({"cost":round(np.sum(vv4)*share,2),"num":round(np.sum(vv5),0),"dianliang":round(np.sum(vv6),2)})
                    shengmingzhouqishouyi = []
                   
                    b_xunhuan = round(8000.0/np.sum(vv2),1) if np.sum(vv2) else '--'
                    for i in range(1,16):
                        shengmingzhouqishouyi.append(round(taoli_arr[14][0]*jishu3*share,2))
                        jishu3 = jishu3-(1-0.7)/14
                    priceall = np.array(priceall).astype(np.float)  # 全年电价二维矩阵
                    yugudianf = np.zeros((12,24))  # 12行24列矩阵
                    for m in range(12):  # 12个月
                        mm = m+1
                        if mm == 1 or mm == 3 or mm == 5 or mm == 7 or mm == 8 or mm == 10 or mm == 12 :
                            day = 31
                        elif mm == 4 or mm == 6 or mm == 9 or mm== 11:
                            day = 30
                        else:
                            day = 28
                        for d in range(24):  # 一天24个小时
                            yugudianf[m][d] = priceall[m][d]*fuheall[m][d]*day/10
                    yugudianf = np.array(yugudianf)
                    yugudianf = np.sum(yugudianf)
                   
                    jiesheng = np.sum(jiessheng_arr)
                    jieshenglv = jiesheng/yugudianf*100
                    huishou = '--'
                    if part_arr[125][0]>=3 and part_arr[125][0]<15:
                        huishou = round(part_arr[125][0],2)
                    elif part_arr[125][0]>=15:
                        huishou = 15
                    
                    obj= {"main":{"yugudianf":round(yugudianf,2),"jieshenglv":round(jieshenglv,2),"fenxiangshouyi":round(jiesheng*share,2),"jieshengdianfei":round(jiesheng,2),"rongliang":round(s_max/1000,2),\
                                  "yugutouzi":forecast_cost,"power":round(rp/1000,2)},"jiacha":{"goudian":m_price,"xunhuan":cycl_price,"xunhuancishu":cycl_num,"rijuntaoli":round(cycl_num*cycl_price,2)},
                    "chongfang":{"data":m_power,"shichangmsg":shichang},"dianjia":data,"sysinfo":{"power":round(rp/1000,2),"rongliang":round(s_max/1000,2),"dod":95,"xiaolv":station_arr[12][0]*100,"shouming":8000,"zhandi":round(rp/1000*70,2)},\
                        "chongfanginfo":chongfang,
                    "shouyi1":{"yugutouzi":forecast_cost,"nianxian":b_xunhuan,"huishou":huishou,
                    "irr": '--' if math.isnan(part_arr[113][0]) or part_arr[113][0]<=-0.2  else round(part_arr[113][0]*100,2),"shengmingzhouqi":round(np.sum(shengmingzhouqishouyi)*share,2)},
                    "shouyi2":shouyi2,"shouyi3":shouyi3,"fuheLine":fuheall[n_m-1]}
                else:
                    obj= {"main":{"yugudianf":"--","jieshenglv":"--","jieshengdianfei":'--',"rongliang":round(s_max/1000,2),"yugutouzi":forecast_cost,"power":rp/1000},"jiacha":{"goudian":'--',"xunhuan":'--',"xunhuancishu":'--',"rijuntaoli":'--'},
                    "chongfang":{"data":[]},"dianjia":[],"sysinfo":{"power":rp/1000,"rongliang":round(s_max/1000,2),"dod":95,"xiaolv":'--',"shouming":8000,"zhandi":rp/1000*17},"chongfanginfo":[],"shouyi1":{"yugutouzi":forecast_cost,"nianxian":15,"huishou":'--',"irr": '--',"shengmingzhouqi":'--'},
                    'shouyi2':[],'shouyi3':[],"fuheLine":fuheall[n_m-1]}
                
                return self.returnTypeSuc(obj)

            else:
                return self.pathError()
                
            
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

        

    # @tornado.web.authenticated
    # def post(self,kt):
        # self.refreshSession()
        # pass
        
    
    def _station_fun(self,p_max,Cycle,DOD,charge_efficient,disg_efficient,battery_num,battery_year,battery_atte,battery_salvage,sys_run):
        '''电站基本参数换算成矩阵
        p_max,额定功率  MW
        Cycle,系统设计循环倍率
        DOD,放电深度
        charge_efficient,充电效率
        disg_efficient,放电效率
        battery_num,电池寿命（次）
        battery_year,电池寿命（年）
        battery_atte,电池循环次数满用时的总衰减
        battery_salvage,电池残值率
        sys_run,系统年平均运行天数
        '''
       
        station_arr = np.zeros((17,1))  # 声明一个17行1列的基本矩阵
        # station_arr[0][0] = battery_name
        station_arr[1][0] = p_max
        station_arr[2][0] = Cycle
        station_arr[3][0] = p_max/Cycle  # 装机规模（电池标称容量）
        station_arr[5][0] = DOD/100  # 额定放电深度（DOD）
        station_arr[4][0] = station_arr[3][0]*station_arr[5][0]  # 额定容量
        station_arr[6][0] = battery_num
        station_arr[7][0] = battery_year
        station_arr[8][0] = battery_atte/100
        station_arr[9][0] = station_arr[3][0]*(1-battery_atte/100)  # 截止标称容量
        station_arr[10][0] = station_arr[9][0]*station_arr[5][0] # 末年实际容量
        station_arr[11][0] = battery_salvage/100 # 电池残值率
        station_arr[12][0] = charge_efficient*disg_efficient/10000  # 系统效率
        station_arr[13][0] = charge_efficient/100
        station_arr[14][0] = disg_efficient/100
        station_arr[15][0] = sys_run
        station_arr[16][0] = sys_run/365.0  #盈利折扣系数
        
        return station_arr

    def _static_fun(self,station_arr,taoli_arr,subsidy_flag,subsidy_eff,subsidy_age,subsidy_total,tt=0):
        '''项目静态总投资参数换算成矩阵
        station_arr:系统基本参数矩阵
        subsidy_flag,补贴标识
        subsidy_eff,补贴费用 元/wh
        subsidy_age,补贴年限
        subsidy_total，补贴总数 万元
        tt:静态总投资费用，c22
        '''
    
        battery_cost = float(self.get_argument('battery_cost',1.06)) # 电池单位价格
        dc_other_cost = float(self.get_argument('dc_other_cost',0.38)) # 直流侧其他设备
        pcs_cost = float(self.get_argument('pcs_cost',0.11)) # PCS单位价格
        exchange_other_cost = float(self.get_argument('exchange_other_cost',0.09)) # 交流侧其他设备
        build_cost = float(self.get_argument('build_cost',0.22)) # 建安施工款总额（税率6%）
        build_other_cost = float(self.get_argument('build_other_cost',0)) # 建设期其他费用（税率6%）

        if DEBUG:
            logging.info('静态总投资参数：battery_cost:%s,dc_other_cost:%s,pcs_cost:%s,exchange_other_cost:%s,build_cost:%s,build_other_cost:%s,subsidy_flag:%s,subsidy_eff:%s,subsidy_age:%s,subsidy_total:%s'%(
                battery_cost,dc_other_cost,pcs_cost,exchange_other_cost,build_cost,build_other_cost,subsidy_flag,subsidy_eff,subsidy_age,subsidy_total))
        static_arr = np.zeros((12,4))  # 声明一个12行4列的基本矩阵
        # 第一列  单位造价  元/wh
        static_arr[2][0] = pcs_cost+exchange_other_cost+battery_cost+dc_other_cost
        static_arr[3][0] = battery_cost+dc_other_cost
        static_arr[4][0] = battery_cost
        static_arr[5][0] = dc_other_cost
        static_arr[6][0] = pcs_cost+exchange_other_cost
        static_arr[7][0] = pcs_cost
        static_arr[8][0] = exchange_other_cost
        static_arr[9][0] = build_cost
        static_arr[10][0] = build_other_cost
        static_arr[11][0] = subsidy_eff  # 补贴
        # static_arr[1][0] = static_arr[2][0]+build_cost+build_other_cost
        if tt:
            static_arr[0][0] = tt
            static_arr[1][0] = tt
        else:
            static_arr[1][0] = static_arr[2][0]+build_cost+build_other_cost
            static_arr[0][0] = static_arr[1][0]-subsidy_eff
        # 第二列  总造价  万元
        for i in range(1,12):
            static_arr[i][1] = station_arr[3][0]*static_arr[i][0]*100   # 应该是*1000*1000/100000
        static_arr[0][1] = static_arr[1][1]-static_arr[11][1]
        # 第三列 增值税  万元
        for i in range(2,11):
            if i>8:
                static_arr[i][2] = static_arr[i][1]/1.06*0.06
            else:
                static_arr[i][2] = static_arr[i][1]/1.13*0.13
        static_arr[1][2] = static_arr[2][2]+static_arr[9][2]+static_arr[10][2]
        static_arr[0][2] = static_arr[1][2]
        # 第四列 固定资产  万元
        for i in range(11):
            static_arr[i][3] = static_arr[i][1] - static_arr[i][2]
        # # 计算补贴
        subsidy_num,t = 0,0  # 最终补贴数，万元;每年补贴数
        if subsidy_flag:  # 参与补贴
            t = taoli_arr[8][0]*subsidy_eff*100  # 第一年补贴
            tt = t*subsidy_age
            
            subsidy_num = tt if tt< subsidy_total else subsidy_total
        # print 'static_arr:',static_arr,'\n','subsidy_num:',subsidy_num
        # for i in range(12):
        #     print '########static_arr[%s]'%(i+22),static_arr[i]
        return static_arr,subsidy_num,t

    def _financing_plan_fun(self,station_arr,static_arr):
        '''融资方案参数换算成矩阵
        station_arr:系统基本参数矩阵
        static_arr:静态总投资矩阵
        '''
    
        own_capital = float(self.get_argument('own_capital',20))/100 # 自有资金（资本金）占比
        plan_capital_time = float(self.get_argument('plan_capital_time',10)) # 融资期限（含建设期）
        plan_capital_efficient = float(self.get_argument('plan_capital_efficient',13))/100 # 融资期限税率
        repayment_time = float(self.get_argument('repayment_time',1))  # 本金第一次还款时间
        build_efficient = float(self.get_argument('build_efficient',5))/100 # 建设期利率（不含税）
        build_year = float(self.get_argument('build_year',0.5)) # 建设期年限
        operator_efficient = float(self.get_argument('operator_efficient',5))/100 # 运营期利率
        operator_year = float(self.get_argument('operator_year',9.5)) # 运营时间
            
        if DEBUG:
            logging.info(u'融资方案参数：own_capital:%s,plan_capital_time:%s,plan_capital_efficient:%s,build_efficient:%s,operator_efficient:%s,operator_year:%s,repayment_time:%s'%(
                own_capital,plan_capital_time,plan_capital_efficient,build_efficient,operator_efficient,operator_year,repayment_time))
        # if operator_year+build_year>plan_capital_time:
        #     return self.customError("参数不合理")
        
        financing_arr = np.zeros((8,2))  # 声明一个8行2列的基本矩阵
        # 第一列 单位参数  元/wh；第二列 总数
        financing_arr[0][0] = own_capital
        financing_arr[0][1] = static_arr[0][1]*own_capital
        # 融资金额
        financing_arr[1][1] = static_arr[0][1] - financing_arr[0][1]
        financing_arr[1][0] = financing_arr[1][1]/station_arr[3][0]/100

        financing_arr[2][0] = plan_capital_time
        financing_arr[2][1] = plan_capital_efficient
        financing_arr[3][0] = repayment_time

        financing_arr[4][0] = build_efficient
        financing_arr[4][1] = build_year
        # 建设期利息
        financing_arr[5][1] = financing_arr[1][1]*build_efficient*build_year
        financing_arr[5][0] = financing_arr[5][1]/station_arr[3][0]/100
        # 项目动态总投资
        financing_arr[6][1] = financing_arr[5][1]+static_arr[0][1]
        financing_arr[6][0] = financing_arr[5][0]+static_arr[0][0]
        
        financing_arr[7][1] = operator_year
        financing_arr[7][0] = operator_efficient
        # print     '++++++++++++++financing_arr:',financing_arr    
        return financing_arr

    def _devope_fun(self,static_arr,factory,person_num=1):
        '''运维费用参数换算成矩阵
        station_arr:系统基本参数矩阵
        static_arr:静态总投资矩阵
        '''
        # person_num = float(self.get_argument('person_num',1)) # 人数
        # person_pay = float(self.get_argument('person_pay',6)) # 工资标准 万元/年
        other_cost = float(self.get_argument('other_cost',static_arr[0][1]*0.002)) # 其他费用 万元/年
        site_cost = float(self.get_argument('site_cost',0))  # 占用场地年租金 万元/年  
        
        if DEBUG:
            logging.info(u'运维费用参数：person_num:%s,other_cost:%s,site_cost:%s'%(
                person_num,other_cost,site_cost))
        e1 = 0.0005  # 机器损坏险
        e2 = 0.0005  # 财产一切险
        # e3 = 0.001   # 总保险费率 e1+e2
        e4 = 0.01    # 维护费率
        devop_arr = np.zeros((9,1))  # 声明一个9行1列的基本矩阵
        # 人工费（含工资、福利等）万元/年
        
        devop_arr[1][0] = round(static_arr[0][1]*0.02/factory,2)
        # 设备维护费用（含维修费、材料费等）万元/年
        devop_arr[2][0] = 0

        devop_arr[4][0] = static_arr[0][1]*e2
        devop_arr[5][0] = static_arr[0][1]*e1
        # 设备保险费
        devop_arr[3][0] = devop_arr[4][0]+devop_arr[5][0]

        devop_arr[6][0] = other_cost
        devop_arr[7][0] = site_cost
        devop_arr[8][0] = 0.01
        devop_arr[0][0] = devop_arr[1][0]+devop_arr[2][0]+devop_arr[3][0]+other_cost

        # print '&&&&&&&&&devop_arr:',devop_arr    
        return devop_arr

    def _part_fun(self,station_arr,static_arr,financing_arr,devop_arr,taoli_arr,subsidy_age,subsidy_one_year):
        '''
        经济测算模块转换成矩阵
        station_arr:基本参数矩阵
        static_arr:静态总投资,
        financing_arr：融资方案矩阵
        devop_arr：运维费用矩阵
        taoli_arr:套利矩阵
        subsidy_age,补贴年限
        subsidy_one_year，第一年补贴数
        '''
        
        year = int(station_arr[7][0])  # 使用年限，默认10
        part_arr = np.zeros((135,year+2))  # 声明一个62行12列的基本矩阵,默认10+2 第二列为建设期
        part_arr[0][1]=part_arr[0][2]=station_arr[4][0]
        part_arr[0][year+1]=station_arr[10][0]
        # 建设期
        part_arr[11][1]=part_arr[12][1]=part_arr[15][1]=part_arr[16][1]=part_arr[17][1]=part_arr[18][1]=part_arr[19][1]=part_arr[75][1]=part_arr[84][1]= 0
        part_arr[95][1]=part_arr[96][1]=part_arr[97][1]=0

        part_arr[85][0] = financing_arr[7][0]  # 融资成本
        part_arr[85][1] = financing_arr[5][1]
        part_arr[87][0] = financing_arr[2][1]  # 融资增值税（进项）
        part_arr[95][0] = static_arr[0][1]*(1-station_arr[11][0])  # 固定资产折旧
        part_arr[96][0] = static_arr[4][1]*(1-station_arr[11][0])  # 电池折旧
        part_arr[97][0] = part_arr[95][0]-part_arr[96][0]  # 其他折旧或摊销
        part_arr[87][1] = part_arr[85][1]*part_arr[87][0]
        part_arr[61][0]=part_arr[61][1] = static_arr[0][1] # 初始投资
        part_arr[98][1]=part_arr[95][1]  # 累计折旧
        part_arr[96][2]=part_arr[96][0]*(1-station_arr[11][0])/10
        part_arr[97][2]=part_arr[97][0]*(1-station_arr[11][0])/10
        part_arr[82][0]=10

        for i in range(2,year):  # 电池剩余容量
            part_arr[0][i+1] = part_arr[0][i]-(part_arr[0][2]-part_arr[0][year+1])/year
            
        for i in range(year+1):  # 残值，最后一年有
            part_arr[54][i]= 0  # 电池残值收入（含税）
            part_arr[55][i]= 0  # 其他设备残值收入（含税)
            part_arr[56][i]= 0  # 电池残值收入增值税金（销项）
            part_arr[57][i]= 0  # 其他设备收入增值税税金（销项）
        part_arr[54][year+1] = static_arr[4][3]*station_arr[11][0]
        part_arr[55][year+1] = (static_arr[2][3]-static_arr[4][3])*station_arr[11][0]
        part_arr[56][year+1] = part_arr[10][year+1]/1.13*0.13
        

    
        for i in range(2,year+2):
            part_arr[15][i] = taoli_arr[17][0]*part_arr[0][i]/part_arr[0][1]*station_arr[16][0]  # 套利收益（含税）万元
            part_arr[16][i] = taoli_arr[8][0]/10*part_arr[0][i]/part_arr[0][1]*station_arr[16][0]  # 套利放电电量 万Kwh或万度
            part_arr[17][i]=1  # 循环变化曲线
            part_arr[18][i] = part_arr[15][i]/part_arr[16][i]  # 首年套利价差（放电端）
            part_arr[19][i]=1  # 价差变化曲线
            part_arr[61][i] = 0  # 初始投资
            part_arr[5][i] = i-1  # 年限
            part_arr[11][i] = 1 if i-1<=subsidy_age else 0  # 补贴收益
            part_arr[12][i] = subsidy_one_year*part_arr[0][i]/part_arr[0][1]*station_arr[16][0]*part_arr[12][i]  # 补贴收标识
            part_arr[7][i] = part_arr[15][i]+part_arr[54][i]+part_arr[55][i]+part_arr[11][i]+part_arr[45][i]  # 现金总流入 
            part_arr[75][i] = devop_arr[0][0]*(1+devop_arr[8][0]*(part_arr[5][i]-1))+devop_arr[7][0]  # 运维费用(含土地或房屋租金)

        part_arr[57][year+1] = part_arr[11][year+1]/1.13*0.13
            
        for i in range(year+2):
            part_arr[20][i]= taoli_arr[16][0]  # 套利收益分成比例
            part_arr[64][i] = 0  # 更换电池比
            part_arr[60][i] = part_arr[64][i]  # 现金总流入
            
        for i in range(1,year+2):
            part_arr[9][i] = part_arr[15][i]*0.13  # 收入增值税金（销项）
            part_arr[76][i] = part_arr[75][i]*0.06  # 运维费用增值税税金（进项
            part_arr[82][i] = 0.1  # 增值税附加税金率
        

        part_arr[79][1]=static_arr[0][2]+part_arr[76][1]
        part_arr[80][1]=part_arr[9][1]+part_arr[56][1]+part_arr[57][1]
        part_arr[78][1]= 0 if part_arr[79][1]>part_arr[80][1] else part_arr[80][1]-part_arr[79][1]
        part_arr[81][1]=part_arr[78][1]*part_arr[82][1]
        for i in range(2,year+2):
            part_arr[79][i] = part_arr[79][i-1]+part_arr[76][i] # 累计进项（全投资）
            part_arr[80][i] = part_arr[80][i-1]+part_arr[9][i]+part_arr[56][i]+part_arr[57][i]  # 累计销项（全投资）
            part_arr[78][i] = 0 if part_arr[79][i]>part_arr[80][i] else part_arr[80][i]-part_arr[79][i]-part_arr[78][i-1]  # 实际应缴纳增值税金额（全投资）
            part_arr[81][i]=part_arr[78][i]*part_arr[82][i]  # 增值税附加金额（全投资）
            


        part_arr[7][1] = part_arr[15][1]+part_arr[54][1]+part_arr[55][1]
        # 融资每年还本金额
        if  part_arr[5][2]<=financing_arr[2][0]-1:
            if part_arr[5][2]>=financing_arr[3][0]:
                part_arr[84][2]=financing_arr[1][1]/(financing_arr[2][0]-financing_arr[3][0]+1)
            else:
                part_arr[84][2]=0
        else:
            part_arr[84][2]=0
        for i in range(3,year+2):
            if  part_arr[5][i]<=financing_arr[2][0]:
                if part_arr[5][i]>=financing_arr[3][0]:
                    part_arr[84][i]=financing_arr[1][1]/(financing_arr[2][0]-financing_arr[3][0]+1)
                else:
                    part_arr[84][i]=0
            else:
                part_arr[84][i]=0

            part_arr[96][i]=part_arr[96][i-1]  # 电池折旧
            part_arr[97][i]=part_arr[97][i-1]  # 其他折旧或摊销

    

        part_arr[7][0] = np.sum(part_arr[7][1:])
        part_arr[15][0] = np.sum(part_arr[15][1:])
        part_arr[16][0] = np.sum(part_arr[16][1:])
        # part_arr[6][0] = 0
        part_arr[18][0] = part_arr[15][0]/part_arr[16][0]
        part_arr[54][0] = np.sum(part_arr[54][1:])
        part_arr[55][0] = np.sum(part_arr[55][1:])
        part_arr[56][0] = np.sum(part_arr[56][1:])
        part_arr[57][0] = np.sum(part_arr[57][1:])
        part_arr[8] = part_arr[16]  # 系统放电电量  万Kwh或万度
        part_arr[9][0] = np.sum(part_arr[9][1:])
        part_arr[76][0] = np.sum(part_arr[76][1:])
        part_arr[79][0] = np.sum(part_arr[79][1:])
        part_arr[80][0] = np.sum(part_arr[80][1:])
        part_arr[78][0] = np.sum(part_arr[78][1:])
        part_arr[81][0] = np.sum(part_arr[81][1:])
        part_arr[84][0] = np.sum(part_arr[84][1:])
        part_arr[109] = part_arr[7]  # 全投资现金流入小计
        

        #  剩余融资本金金额
        part_arr[86][1]=part_arr[84][0]-part_arr[84][1]
        part_arr[99][1]=part_arr[7][1]-part_arr[9][1]
        part_arr[100][1]=part_arr[75][1]-part_arr[76][1]+part_arr[85][1]+part_arr[95][1]
        part_arr[101][1]=part_arr[75][1]-part_arr[76][1]
        part_arr[90][1]=part_arr[79][1]+part_arr[87][1]
        part_arr[91][1]= part_arr[90][1]
        part_arr[89][1]=0 if part_arr[90][1]>part_arr[91][1] else part_arr[91][1]-part_arr[90][1]
        part_arr[92][1]=part_arr[89][1]*part_arr[82][1]
        part_arr[103][1]=part_arr[99][1]-part_arr[100][1]-part_arr[81][1]-part_arr[92][1]
        part_arr[104][1]=part_arr[103][1]
        part_arr[105][1]=0 if part_arr[103][1]*0.25<0 else part_arr[104][1]*0.25-part_arr[103][0]
        part_arr[106][1]=part_arr[102][1]-part_arr[105][1]
        part_arr[110][1]=part_arr[61][1]+part_arr[75][1]+part_arr[78][1]+part_arr[81][1]
        part_arr[111][1]=part_arr[109][1]-part_arr[110][1]
        part_arr[112][1]=part_arr[111][1]-part_arr[105][1]
        part_arr[121][1]=part_arr[111][1]
        part_arr[122][1]=part_arr[112][1]

        part_arr[123][1]=0 if part_arr[121][1]*part_arr[121][0]>0 else 0 if part_arr[111][1]==0 else part_arr[5][0]+(part_arr[111][1]-part_arr[121][1])/part_arr[111][1]
        part_arr[124][1]=0 if part_arr[122][1]*part_arr[122][0]>0 else 0 if part_arr[112][1]==0 else part_arr[5][0]+(part_arr[112][1]-part_arr[122][1])/part_arr[112][1]

        part_arr[129][1]=part_arr[109][1]
        part_arr[130][1]=financing_arr[0][1]+ part_arr[84][1]+ part_arr[85][1]+ part_arr[87][1]+ part_arr[89][1]+ part_arr[92][1]- part_arr[78][1]
        part_arr[131][1]=part_arr[129][1]-part_arr[130][1]
        part_arr[132][1]=part_arr[131][1]-part_arr[105][1]
        for i in range(2,year+2):
            part_arr[86][i] = part_arr[86][i-1]-part_arr[84][i]  # 剩余融资本金金额
            part_arr[85][i] = part_arr[86][i-1]*part_arr[85][0]  # 融资成本（不含税）
            part_arr[87][i] = part_arr[85][i]*part_arr[87][0]  # 融资增值税（进项）
            part_arr[95][i] = part_arr[96][i]+part_arr[97][i]  #固定资产折旧
            part_arr[98][i] = part_arr[98][i-1]+part_arr[95][i]  #累计折旧
            part_arr[99][i] = part_arr[7][i]-part_arr[9][i]  # 业务收入
            part_arr[90][i]=part_arr[79][i]+part_arr[87][i]  # 累计进项（资本金）
            part_arr[91][i]=part_arr[80][i]  # 累计销项（资本金）

            part_arr[89][i]=0 if part_arr[90][i]>part_arr[91][i] else part_arr[91][i]-part_arr[90][i]-part_arr[89][i]  # 实际应缴纳增值税金额（资本金）
            part_arr[92][i]=part_arr[89][i]*part_arr[82][i]  # 增值税附加税金额（资本金）

            part_arr[100][i]=part_arr[75][i]-part_arr[76][i]+part_arr[85][i]+part_arr[95][i]  # 业务成本（运维费用+财务成本+固定资产折旧）
            part_arr[101][i]=part_arr[75][i]-part_arr[76][i]  # 其中：运维费用
            part_arr[103][i]=part_arr[99][i]-part_arr[100][i]-part_arr[81][i]-part_arr[92][i]  # 利润总额（业务收入-业务成本-增值税附加金额）
            part_arr[104][i]=part_arr[104][i-1]+part_arr[103][i]  # 留存利润
            if part_arr[103][i]*0.25<0:  # 所得税
                part_arr[105][i]=0
            else:
                if part_arr[104][i]<0:
                    part_arr[105][i]=0
                else:
                    if part_arr[103][i]>part_arr[104][i]:
                        part_arr[105][i]=part_arr[104][i]*0.25
                    else:
                        part_arr[105][i]=part_arr[103][i]*0.25

            part_arr[106][i]=part_arr[102][i]-part_arr[105][i]  # 净利润
            part_arr[110][i]=part_arr[62][i]+part_arr[75][i]+part_arr[78][i]+part_arr[81][i]  # 全投资现金流出小计
            part_arr[111][i]=part_arr[109][i]-part_arr[110][i]  # 全投资净现金流（所得税前，10年）
            part_arr[112][i]=part_arr[111][i]-part_arr[105][i]  # 全投资净现金流（扣除所得税后，10年）
            part_arr[121][i]=part_arr[121][i-1]+part_arr[111][i]  # 累计净现金流（所得税前）
            part_arr[122][i]=part_arr[122][i-1]+part_arr[112][i]  # 累计净现金流（所得税后）
            part_arr[123][i]=0 if part_arr[121][i]*part_arr[121][i-1]>0 else part_arr[5][i-1]+(part_arr[111][i]-part_arr[121][i])/part_arr[111][i]
            part_arr[124][i]=0 if part_arr[122][i]*part_arr[122][i-1]>0 else part_arr[5][i-1]+(part_arr[112][i]-part_arr[122][i])/part_arr[112][i]
            part_arr[129][i]=part_arr[109][i]  # 资本金现金流入小计 万元
            part_arr[130][i]=part_arr[84][i]+ part_arr[85][i]+ part_arr[87][i]+ part_arr[89][i]+ part_arr[92][i]+ part_arr[75][i]  # 资本金现金流出小计
            part_arr[131][i]=part_arr[129][i]-part_arr[130][i]  # 资本金净现金流（所得税前，10年）
            part_arr[132][i]=part_arr[131][i]-part_arr[105][i] # 资本金净现金流（扣除所得税后，10年）



        part_arr[100][0] = np.sum(part_arr[100][1:])
        part_arr[90][0] = np.sum(part_arr[90][1:])
        part_arr[91][0] = np.sum(part_arr[91][1:])
        part_arr[92][0] = np.sum(part_arr[92][1:])
        part_arr[105][0] = np.sum(part_arr[105][1:])
        part_arr[110][0] = np.sum(part_arr[110][1:])
        part_arr[111][0] = np.sum(part_arr[111][1:])
        part_arr[112][0] = np.sum(part_arr[112][1:])
        part_arr[102]=part_arr[85]  # 财务成本
        
        for p in [113,114,133,134]:
            ft = np.isnan(part_arr[p-2][1:])
            part_arr[p][0] = 0 if True in ft else np.irr(part_arr[p-2][1:])
           
        # part_arr[113][0] = np.irr(part_arr[111][1:])
        # part_arr[114][0] = np.irr(part_arr[112][1:])
        # part_arr[133][0] = np.irr(part_arr[131][1:])
        # part_arr[134][0] = np.irr(part_arr[132][1:])

        part_arr[125][0] = np.sum(part_arr[123][1:])+financing_arr[4][1]
        part_arr[126][0] = np.sum(part_arr[124][1:])+financing_arr[4][1]
    
    
        # for i in range(135):
        #     print '********part_arr[%s]'%(i+2),part_arr[i]

        return part_arr



    def _taoli_fun(self,pages,p_max,eff_charge,eff_discharge,load_peak,load_flat,load_valley,load_jianfeng,H0,SS,share,H1,battery_year=15):
        '''
        计算套利模块结构
        '''
        taoli_arr = np.zeros((18,13))  # 声明一个26行13列的矩阵，对应套利模块结构
        m_price,m_power = [],[]
        save_cost,data = [],[]  # 节省电费;电价信息
        
        for p in pages:
            w_day = np.ones((24,1))
            data.append(eval(str(p)))
            ind = pages.index(p)
            year_month = p.year_month
            ym = year_month.split('-')
            Mdays = month_days(ym[0],ym[1])  # 当前月天数
            pv = [p.pv0,p.pv1,p.pv2,p.pv3,p.pv4,p.pv5,p.pv6,p.pv7,p.pv8,p.pv9,p.pv10,p.pv11,p.pv12,p.pv13,p.pv14,p.pv15,p.pv16,p.pv17,p.pv18,p.pv19,p.pv20,p.pv21,p.pv22,p.pv23]
            for i in range(24):
                if pv[i]<=-1:  # 谷
                    w_day[i][0] = w_day[i][0]*load_valley
                elif pv[i]==1:  # 峰
                    w_day[i][0] = w_day[i][0]*load_peak
                elif pv[i]==2:  # 尖峰
                    w_day[i][0] = w_day[i][0]*load_jianfeng
                else:
                    w_day[i][0] = w_day[i][0]*load_flat
            
            price = [p.h0,p.h1,p.h2,p.h3,p.h4,p.h5,p.h6,p.h7,p.h8,p.h9,p.h10,p.h11,p.h12,p.h13,p.h14,p.h15,p.h16,p.h17,p.h18,p.h19,p.h20,p.h21,p.h22,p.h23]
           
            taoli_arr[16][0] = share  # 套利分享比例采用默认值
            
            p_chag,p_disg,chag_money,disg_money,cou,power=calrate(p_max,eff_charge,eff_discharge,price,w_day,H0,SS,H1)
            save_cost.append(cou)
            m_price.append(float(max(price))-float(min(price)))
            m_power.append(power)
            # 因为每月一个价格，所以当月的只计算一天的即可
            c_disg = p_disg*Mdays/1000
            c_chag = p_chag*Mdays/1000
            chag_moneys = chag_money*Mdays
            disg_moneys = disg_money*Mdays
            taoli_arr[3][ind+1] = Mdays  # 首年每月天数
            # taoli_arr[4][ind+1] = math.ceil(c_disg*1000/eff_discharge/SS)  # 首年循环次数 向上取整数
            if SS == 0:
                   taoli_arr[4][ind+1] = 0
            else: 
                taoli_arr[4][ind+1] = round(c_disg*1000/eff_discharge/SS,2) 
            taoli_arr[7][ind+1] = -round(c_chag,2)  # 首年实际充电电量
            taoli_arr[8][ind+1] = round(c_disg,2)  # 首年实际放电电量
            taoli_arr[6][ind+1] = round(c_disg/eff_discharge,2)  # 首年累计循环电量
            taoli_arr[10][ind+1] = -round(chag_moneys/10000,2)  # 首年充电电费
            taoli_arr[12][ind+1] = round(disg_moneys/10000,2)  # 首年放电电费
            taoli_arr[14][ind+1] = taoli_arr[12][ind+1]+taoli_arr[10][ind+1]  # 套利基准收益
            taoli_arr[16][ind+1] = round(taoli_arr[16][0]*taoli_arr[14][ind+1],2)  # 平均充电电价(含税) =E10/E7*
        taoli_arr[8][0] = np.sum(taoli_arr[8])  # 首年实际放电电量总数
        taoli_arr[7][0] = np.sum(taoli_arr[7])  # 首年实际充电电量总数
        taoli_arr[6][0] = np.sum(taoli_arr[6]) 
        taoli_arr[3][0] = np.sum(taoli_arr[3])
        taoli_arr[4][0] = np.sum(taoli_arr[4])
        taoli_arr[10][0] = np.sum(taoli_arr[10])
        taoli_arr[12][0] = np.sum(taoli_arr[12])
        taoli_arr[14][0] = np.sum(taoli_arr[14])
        taoli_arr[17][0] = round(taoli_arr[14][0]*(1+taoli_arr[15][0])*taoli_arr[16][0],2)
       
        for i in range(13):
            taoli_arr[0][i] = 1  # 每月/每日平均容量利用率
            taoli_arr[1][i] = taoli_arr[4][i]  # 约束松弛100%利用循环
            taoli_arr[5][i] = taoli_arr[4][i]*battery_year  # 十年累计循环次数
            taoli_arr[9][i] = round(taoli_arr[10][i]/taoli_arr[7][i]*10,2) if taoli_arr[7][i]!=0 else 0 # 平均充电电价(含税) =E10/E7*10
            taoli_arr[11][i] = round(taoli_arr[12][i]/taoli_arr[8][i]*10,2) if taoli_arr[8][i]!=0 else 0  # 平均放电电价(含税)
            taoli_arr[2][i] = round(taoli_arr[4][i]/taoli_arr[3][i],2) if taoli_arr[3][i]!=0 else 0  # 每月/每日平均循环次数=C4/C3
            taoli_arr[13][i] = taoli_arr[14][i]/taoli_arr[8][i]*10 if taoli_arr[8][i]!=0 else 0  # 套利基准收益
        # for i in range(18):
        #     print '********taoli_arr[%s]'%(i+3),taoli_arr[i]
        # 套利矩阵；一年节省费用；购电价差（最大减去最小）；功率；soc百分比;选中的月份;电价信息
        
        return taoli_arr,round(sum(save_cost)/10000,2),round(max(m_price),3),m_power,data