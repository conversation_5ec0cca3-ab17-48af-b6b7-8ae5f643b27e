#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Application\Models\User\organization_type.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-25 16:20:43


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, \
    VARCHAR


class OrganizationType(user_Base):
    u'组织关系类型表'
    __tablename__ = "c_organization_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    descr = Column(String(256), nullable=False, comment=u"描述")
    en_descr = Column(String(256), nullable=False, comment=u"描述-英文")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(OrganizationType(id=1,descr='私企'))
        user_session.commit()
        user_session.close()

    def __repr__(self):
        return "{'id':%s,'descr':'%s','en_descr':'%s'}" % (self.id,self.descr,self.en_descr)


    def deleteOrganizationType(self,id):
        from Application.Models.User.organization import Organization
        try:
            orgs = user_session.query(Organization).filter(Organization.type_id == id).all()  # 
            for org in orgs:
                org.deleteOrganization(org.id)  # 委托模块处理
            user_session.query(OrganizationType).filter(OrganizationType.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}