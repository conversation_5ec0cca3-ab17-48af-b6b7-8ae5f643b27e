package com.tianlu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.central.common.exception.BusinessException;
import com.central.common.model.Result;
import com.tianlu.entity.FaultAlarmAggr;
import com.tianlu.entity.MessageCenter;
import com.tianlu.mapper.FaultAlarmAggrMapper;
import com.tianlu.service.FaultAlarmAggrService;
import com.tianlu.service.MessageCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class FaultAlarmAggrServiceImpl extends ServiceImpl<FaultAlarmAggrMapper, FaultAlarmAggr> implements FaultAlarmAggrService {

    private final MessageCenterService messageCenterService;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 推送告警消息到消息中心
     * @param alarmId 告警ID
     * @param userId 当前用户ID
     * @return 推送结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> pushAlarmMessage(Long alarmId, Long userId) {
        try {
            // 获取告警信息
            FaultAlarmAggr aggrAlarm = this.getById(alarmId);
            if (aggrAlarm == null) {
                log.error("告警推送至消息中心:告警不存在");
                return Result.failed("告警不存在");
            }

            // 获取告警类型描述
            String typeDesc;
            String enTypeDesc;
            if (aggrAlarm.getType() == 1) {
                typeDesc = "故障";
                enTypeDesc = "Faulty";
            } else if (aggrAlarm.getType() == 4) {
                typeDesc = "离线";
                enTypeDesc = "OffLine";
            } else {
                typeDesc = "通讯异常";
                enTypeDesc = "Com. Error";
            }

            // 构建时间字符串
            String timeStr = aggrAlarm.getStatus() == 1 
                ? String.format("%s-%s", 
                    aggrAlarm.getStartTime().format(DATE_TIME_FORMATTER),
                    aggrAlarm.getEndTime().format(DATE_TIME_FORMATTER))
                : String.format("从%s", aggrAlarm.getStartTime().format(DATE_TIME_FORMATTER));

            String enTimeStr = aggrAlarm.getStatus() == 1
                ? String.format("%s-%s", 
                    aggrAlarm.getStartTime().format(DATE_TIME_FORMATTER),
                    aggrAlarm.getEndTime().format(DATE_TIME_FORMATTER))
                : String.format("from %s", aggrAlarm.getStartTime().format(DATE_TIME_FORMATTER));

            // 获取站点和项目信息
            // TODO: 需要注入站点服务和项目服务
            String projectName = "项目名称"; // 从站点服务获取
            String stationName = "站点名称"; // 从站点服务获取

            // 构建消息标题
            String title = String.format("%s项目%s出现%s%s: %s。", 
                projectName, timeStr, aggrAlarm.getNote(), typeDesc, aggrAlarm.getDetails());
            
            String enTitle = String.format("The project %s %s appear %s %s: %s.",
                projectName, enTimeStr, aggrAlarm.getNote(), enTypeDesc, aggrAlarm.getDetails());

            // 设置接收者列表
            Set<Long> receivers = new HashSet<>(Arrays.asList(69L, 76L, 88L, 120L, 182L, userId));

            // 为每个接收者创建消息
            for (Long receiver : receivers) {
                // 检查是否已经推送过
                boolean exists = messageCenterService.count(new LambdaQueryWrapper<MessageCenter>()
                    .eq(MessageCenter::getAlarmId, alarmId)
                    .eq(MessageCenter::getUserId, receiver)) > 0;

                if (exists) {
                    log.error("告警推送至消息中心: {}已推送过, 将再次推送", receiver);
                    continue;
                }

                // 创建新消息
                MessageCenter message = new MessageCenter();
                message.setTitle(title);
                message.setEnTitle(enTitle);
                message.setType(1); // 告警类型
                message.setIsRead(0);
                message.setIsVerify(0);
                message.setUserId(receiver);
                message.setRelatedId(String.valueOf(alarmId));
                message.setCreateTime(LocalDateTime.now());
                
                messageCenterService.save(message);
            }

            return Result.succeed("告警推送至消息中心: 推送成功");
        } catch (Exception e) {
            log.error("告警推送至消息中心失败", e);
            return Result.failed("告警推送失败");
        }
    }

    /**
     * 获取告警消息详情
     * @param messageId 消息ID
     * @return 告警消息详情
     */
    @Override
    public Result<Map<String, Object>> getAlarmMessageDetail(Long messageId) {
        try {
            MessageCenter message = messageCenterService.getById(messageId);
            if (message == null) {
                return Result.failed("消息中心:告警消息不存在");
            }

            if (message.getType() != 1) {
                return Result.failed("消息中心: 该消息非告警消息，无告警信息");
            }

            // 获取告警信息
            FaultAlarmAggr aggrAlarm = this.getById(Long.parseLong(message.getRelatedId()));
            if (aggrAlarm == null) {
                return Result.failed("消息中心: 该消息非告警消息，无告警信息");
            }

            // TODO: 需要注入站点服务和项目服务
            String projectName = "项目名称"; // 从站点服务获取
            String stationName = "站点名称"; // 从站点服务获取

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("title", String.format("%s项目%s告警信息", 
                projectName, aggrAlarm.getStartTime().format(DATE_FORMATTER)));
            result.put("description", message.getTitle());
            result.put("receive_message_time", message.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.put("related_project_id", 1L); // TODO: 从项目服务获取
            result.put("alarm_type", aggrAlarm.getType());
            result.put("alarm_status", aggrAlarm.getStatus());
            result.put("target_date", aggrAlarm.getStartTime().format(DATE_FORMATTER));

            // 获取相关告警详情
            List<Map<String, Object>> alarmDetails = new ArrayList<>();
            // TODO: 需要注入告警服务获取相关告警
            // 这里需要根据实际情况实现获取相关告警的逻辑

            result.put("alarm_details", alarmDetails);

            return Result.succeed(result);
        } catch (Exception e) {
            log.error("获取告警消息详情失败", e);
            return Result.failed("获取告警消息详情失败");
        }
    }
} 