#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\Running\ElePriceDectision\urls_decision.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-29 09:03:06


# -*- coding=utf-8 -*-
from tornado.routing import Rule, PathMatches
from tornado.web import url

from Application.EqAccount.ElePriceDecision.view import *
from Application.EqAccount.ElePriceDecision.user import UserManageIntetface, UserLoginHandler
from Application.EqAccount.ElePriceDecision.model_management import ModelManagement, MainBodyViews, ForecastViews
from Application.EqAccount.ElePriceDecision.view_v2 import GetRealPartMeteorologicalData, \
    RealElePriceRelation, ProvinceCityLadingCap, PowerFlagAndChagDisg, RealForecastPower, MarketInfoV2, \
    MarketInfoOptionsV2
from Application.EqAccount.ElePriceDecision.view_v21 import GetProvinces, RealForecastWeather, \
    ElectricityPriceMonitoring, RecentlyDisclosedInfo, ClearInfo, RealMarketInfo, \
    PrivateInfo, OtherInfo, ElectricityStations, ProvincesMenuOptions, ClearInfoNameList, \
    RealMarketInfoNameList, RecentlyDisclosedInfoNameList, DownloadTemplateData, GetCityPreMeteorologicalData, \
    GetCityRealMeteorologicalData
from Application.EqAccount.Foreign.DemoOpenAPIViews import PriceRealDataHandler, PriceHistoryDataHandler

routes = [
    # v1.0 版本接口
    # 省市信息接口
    url(r"/Ele/ProvincesData", GetCProvinces),
    # 气象地图数据接口
    url(r"/Ele/GetMeteorologicalData", GetMeteorologicalData),          # 2.1版本废弃
    # 气象地图城市数据接口
    url(r"/Ele/GetMeteorologicalCityData", GetMeteorologicalCityData),      # 2.1版本废弃
    # 气象表格数据接口
    url(r"/Ele/GetMeteorologicalDataForTable", GetMeteorologicalDataForTable),
    # 导出气象表格数据接口
    url(r"/Ele/ExportMeteorologicalDataForTable", ExportMeteorologicalDataForTable),
    # SOP流程图接口
    url(r"/Ele/SOP", GetSOP),
    # 折线图下拉选项接口
    url(r"/Ele/MarketInfoOptions", MarketInfoOptions),
    # 折线图数据接口
    url(r"/Ele/MarketInfo", MarketInfo),
    # 时序字典数据接口
    url(r"/Ele/SeriesDictData", SeriesDictData),
    # 时序交易数据接口
    url(r"/Ele/SeriesData", SeriesData),
    # 导出时序交易数据接口
    url(r"/Ele/ExportSeriesData", ExportSeriesData),
    # 非时序字典数据接口
    url(r"/Ele/NonSeriesDictData", NonSeriesDictData),
    # 非时序交易数据/导出非时序交易数据
    url(r"/Ele/NonSeriesData", NonSeriesData),

    # 权限管理接口
    # 用户管理
    url(r"/User/(\w+)", UserManageIntetface),
    # 登录、登出
    url(r"/UserLogin/(\w+)", UserLoginHandler),
    # 测算模型
    url(r"/Model/(\w+)", ModelManagement),
    # 主体管理
    url(r"/MainBody/(\w+)", MainBodyViews),
    # 电价预测
    url(r"/Forecast/(\w+)", ForecastViews),

    url(r"/RealData/SeriesData", PriceRealDataHandler),
    url(r"/HisData/SeriesData", PriceHistoryDataHandler),

    # v2.0
    # 查询指定区域的实时天气数据
    url(r"/Ele/GetRealPartMeteorologicalData", GetRealPartMeteorologicalData),
    # 量价散点图
    url(r"/Ele/RealElePriceRelation", RealElePriceRelation),
    # 指定省份各个市装机容量数据
    url(r"/Ele/ProvinceCityLadingCap", ProvinceCityLadingCap),
    # 查询电源结构和充放电量数据
    url(r"/Ele/PowerFlagAndChagDisg", PowerFlagAndChagDisg),
    # 获取实时及预测需求量数据
    url(r"/Ele/RealForecastPower", RealForecastPower),
    # 数据可视化看板: 下拉选项
    url(r"/Ele/MarketInfoOptionsV2", MarketInfoOptionsV2),
    # 数据可视化看板: 电价趋势/电源趋势/负荷趋势
    url(r"/Ele/MarketInfoV2", MarketInfoV2),

    # v2.1 版本接口
    # 省份下拉框
    url(r"/Ele/Provinces", GetProvinces),
    # 实时/预报气象数据接口
    url(r"/Ele/Weather", RealForecastWeather),

    # 实时气象图表数据接口
    url(r"/Ele/RealWeatherCharts", GetCityRealMeteorologicalData),
    # 预报气象图表数据接口
    url(r"/Ele/PreWeatherCharts", GetCityPreMeteorologicalData),

    # 电价监测
    url(r"/Ele/ElectricityStations", ElectricityStations),
    url(r"/Ele/ElectricityPriceMonitoring", ElectricityPriceMonitoring),

    # 获取菜单的二级标签
    url(r"/Ele/ProvincesMenuOptions", ProvincesMenuOptions),

    # 日前披露信息
    url(r"/Ele/RecentlyDisclosedInfo", RecentlyDisclosedInfo),
    # 日前披露信息: 点击数字时查看节点名称列表
    url(r"/Ele/RecentlyDisclosedInfoNameList", RecentlyDisclosedInfoNameList),

    # 出清信息
    url(r"/Ele/ClearInfor", ClearInfo),
    # 出清信息: 日前节点边际电价 & 实时节点边际电价: 点击数字时查看节点名称列表[749]
    url(r"/Ele/ClearInfoNameList", ClearInfoNameList),

    # 实时市场信息
    url(r"/Ele/RealMarketInfo", RealMarketInfo),
    # 实时市场信息: 点击数字时查看节点名称列表[749]
    url(r"/Ele/RealMarketInfoNameList", RealMarketInfoNameList),

    # 其他信息
    url(r"/Ele/OtherInfo", OtherInfo),

    # 私有信息 ===> 2.1 版本无数据 待完善
    url(r"/Ele/PrivateInfo", PrivateInfo),

    # 下载模板数据
    url(r"/Ele/DownloadTemplateData", DownloadTemplateData),

]
