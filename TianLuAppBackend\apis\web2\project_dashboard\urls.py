# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/26 9:26
# <AUTHOR> <PERSON><PERSON>ang
# @Project  : TianLuAppBackend
# @File     : urls.py
# @Software : PyCharm


from django.urls import path

from apis.web2.project_dashboard.views import login, organization_view, system_monitor, project_center_control, user_view, \
    map_dashboard, role_view

urlpatterns = [
    # 地图大屏
    path("user_password_login", login.UsernamePasswordLoginView.as_view()),  # web账号登录  en
    path("get_login_code", login.WebGetSMSCode.as_view()),  # 获取短信验证码       en
    path("map/auth_view", map_dashboard.AuthMapView.as_view()),  # web内部员工大屏地图  en
    # path("map/user_view", map_dashboard.UserMapView.as_view()),  # web 客户大屏地图   未使用
    path("income/view", map_dashboard.WebIncomeView.as_view()),  # web大屏收益  no need
    path("map/health", map_dashboard.MapHealthView.as_view()),  # web地图健康度  no need
    path("map/reach_yield_by_month", map_dashboard.MapReachYieldByMonthView.as_view()),  # web逐月达产率 no need
    path("map/reach_yield_top10", map_dashboard.MapProjReachYieldTopView.as_view()),  # 项目达产率排名 no need

    path("map/diff", map_dashboard.WebIncomeDiffView.as_view()),  # 项目达产率: 测试
    path("map/test", map_dashboard.TestView.as_view()),  # 功率计划下发: 测试
    path("map/sync", map_dashboard.SyncMasterStationsEnglishNameView.as_view()),  # 同步主站的英文名称为逻辑主站的英文名称
    path("map/test2", map_dashboard.TestIncomeView.as_view()),  # 项目达产率数据核对：测试
    path("map/test2excel", map_dashboard.TestIncome2excelView.as_view()),  # 项目达产率数据核对：测试

    # 集中监控---卡片、列表
    path("control/provinces", project_center_control.ProvincesView.as_view()),  # 项目下拉省份列表  en
    path("control/views", project_center_control.ControlCardsView.as_view()),  # 项目集控卡片页面   en
    # path("index/income/", control_views.IndexIncomeView.as_view()),  # 项目集控卡片收益

    # 集中监控-系统监控
    path("system/monitor/projects", system_monitor.SystemMonitorProjectsView.as_view()),     # 集中监控-系统监控：项目列表   no need
    path("system/monitor/stations/<int:pk>", system_monitor.SystemMonitorStationsView.as_view()),    # 集中监控-系统监控：项目-站列表     no need
    path("system/monitor/<int:pk>", system_monitor.SystemMonitorView.as_view()),     # 集中监控-系统监控        no need

    # 集中监控-查看版本号
    path("system/version/<int:pk>", system_monitor.VersionNumberView.as_view()),    # 集中监控-查看版本号        no need

    # 集中监控--PCS/电源监测
    path("station/monitor/titles", system_monitor.PcsMonitorTitlesView.as_view()),      # PCS/电源监测数据：标头集合       no need
    path("station/monitor/data", system_monitor.MonitorView.as_view()),     # PCS/电源监测数据：实时遥测数据/实时遥信数据      no need

    # 系统管理--角色管理
    # path("role/list", role_view.RoleViews.as_view()),  # 查询角色       no need
    # path("role/add", role_view.RoleAddViews.as_view()),  # 添加角色     no need
    # path("role/list/<str:id>", role_view.RoleInfoViews.as_view()),  # 展示与修改角色       no need
    # path("role/del", role_view.RoleDeViews.as_view()),  # 删除角色      no need
    # path("role/permissions/list", role_view.RolePermissionsViews.as_view()),  # 获取角色权限      no need
    # path("role/permissions/add", role_view.RolePermissionsAddViews.as_view()),  # 保存权限      no need

    # 系统管理--用户管理
    # path("user/add", user_view.WebUsernameAddView.as_view()),  # 用户添加
    # path("user/project", user_view.WebProjectView.as_view()),  # 项目展示
    # path("user/list/<str:id>", user_view.UserInfoViews.as_view()),   # 展示与修改用户
    # path("user/list", user_view.UserViews.as_view()),   # 查询用户
    # path("user/del", user_view.UserDeViews.as_view()),    # 删除用户
    # path("organization/list", organization_view.OrganizationViews.as_view()),  # 查询组织
    # path("organization/add", organization_view.OrganizationAddViews.as_view()),  # 添加组织
    # path("organization/list/<str:id>", organization_view.OrganizationInfoViews.as_view()),  # 展示与修改组织
    # path("organization/del", organization_view.OrganizationDeViews.as_view()),  # 删除
]