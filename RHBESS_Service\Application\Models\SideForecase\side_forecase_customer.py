#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\side_forecase_customer.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-29 14:38:23


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseCustomer(user_Base):
    u'用户侧潜在客户'
    __tablename__ = "t_side_forecase_customer"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"姓名")
    phone_no = Column(VARCHAR(13), nullable=False, comment=u"电话")
    company = Column(VARCHAR(256), nullable=False, comment=u"公司")
    address = Column(VARCHAR(256), nullable=True, comment=u"地址")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
   
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.commit()
        user_session.close()
        
    def __repr__(self):
        
        bean = "{'id':%s,'name':'%s','phone_no':'%s','company':'%s','address':'%s','op_ts':'%s'}" % (
            self.id,self.name,self.phone_no,self.company,self.address,self.op_ts)
        return bean.replace("None",'')
        
    