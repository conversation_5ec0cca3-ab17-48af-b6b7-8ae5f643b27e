#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-05-14 11:59:13
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\zh_sub.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-05-22 15:15:21


import json
import logging

from LocaleTool import settings
from LocaleTool.common import TranslateCls, redis_pool, db_tool
from tools.send_mail import sendMail_

# 英文转中文
t_cls = TranslateCls(1)


def en2zh_sub():
    """
    订阅
    :return:
    """
    # 获取英译中翻译数据订阅
    pub = redis_pool.pubsub()
    pub.subscribe('zh_translate_pub')
    # 监听
    msg_stream = pub.listen()
    for msg in msg_stream:
        if msg["type"] == "message":
            data = json.loads(msg['data'])
            id = data.get('id')
            table = data.get('table')
            info = data.get('update_data')
            sql = ''
            for k, v in info.items():
                sql += f"{k} = {json.dumps(t_cls.str_chinese(v)).encode().decode('unicode_escape')},"
            if sql:
                sql = sql[:-1]
                u_sql = f"UPDATE `{settings.DATABASES['default']['NAME']}`.`{table}` SET {sql} WHERE `id` = {id};"
                try:
                    db_tool.execute_sql(u_sql)
                    print('执行完成')
                except Exception as e:
                    logging.error(f'英译中翻译写入数据失败：翻译表：{table}；数据ID：{id}, 错误信息：{e}')
                    # sendMail_(f"您的异步翻译异常，请关注 数据表：{table}；数据ID：{id}, 错误信息：{e}", "异步翻译异常消息通知：英译中", "天禄系统", "XXX", user_email)
        elif msg["type"] == "subscribe":
            print(str(msg["channel"]), "订阅成功")


if __name__ == '__main__':
    en2zh_sub()