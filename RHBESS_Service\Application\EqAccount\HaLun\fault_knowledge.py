#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/11/6 下午3:49
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import os
import json
import re
import logging
import time

import tornado.web
from datetime import datetime, timedelta
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import user_session
from Tools.DB.mysql_scada import DEBUG
from Tools.Utils.time_utils import timeUtils
from sqlalchemy import func
from Tools.Utils.mimio_tool import upload_file
from Application.Models.User.fault_knowledge_t import FaultKnowledgeBase, FaultKnowledgePropose, FaultKnowledgeFiles, FaultKnowledgeDictionaries


class FaultKnowledgeIntetface(BaseHandler):
    """
    故障知识库
    """
    @tornado.web.authenticated
    def get(self, action):
        self.refreshSession()
        try:
            if action == 'GetFaultRecord':
                """
                获取故障知识库清单
                """
                start_time = self.get_argument('start_time', None)  # 收录开始日期
                end_time = self.get_argument('end_time', None)  # 收录结束日期
                fault_entry_name = self.get_argument('fault_entry_name', None)  # 条目名称
                fault_keyword = self.get_argument('fault_keyword', None)  # 关键字
                fault_type_id = self.get_argument('fault_type_id', None)  # 故障分类ID
                soft_hard_ware_id = self.get_argument('soft_hard_ware_id', None)  # 软件/硬件故障ID
                fault_level_id = self.get_argument('fault_level_id', None)  # 故障等级ID
                page = int(self.get_argument('page', '1'))  # 页码
                page_size = int(self.get_argument('page_size', '20'))  # 每页数
                fault_id = self.get_argument('fault_id', None)

                if DEBUG:
                    logging.info(
                        'start_time:%s, end_time:%s, fault_entry_name:%s, fault_keyword:%s, fault_type_id:%s, '
                        'soft_hard_ware_id:%s, fault_level_id:%s, page:%s, page_size:%s',
                        start_time, end_time, fault_entry_name, fault_keyword, fault_type_id,
                        soft_hard_ware_id, fault_level_id, page, page_size
                    )

                filters = [FaultKnowledgeBase.is_use == 1]

                if start_time and end_time:
                    if not judge_is_date(start_time) or not judge_is_date(end_time):
                        return self.parameterError(msg='收录时间格式错误', data=None)
                    start_time = datetime.strptime(start_time, '%Y-%m-%d').date()
                    end_time = datetime.strptime(end_time, '%Y-%m-%d').date()
                    filters.append(FaultKnowledgeBase.inclusion_date.between(start_time, end_time))
                else:
                    start_time = datetime.now().date() - timedelta(days=31)
                    end_time = datetime.now().date()

                if fault_id:
                    filters.append(FaultKnowledgeBase.id == int(fault_id))

                if fault_entry_name:
                    filters.append(FaultKnowledgeBase.fault_entry_name.like('%' + fault_entry_name + '%'))

                if fault_keyword:
                    filters.append(FaultKnowledgeBase.fault_keyword.like('%' + fault_keyword + '%'))

                if fault_type_id:
                    if not re.compile(r'^-?\d+$').match(fault_type_id):
                        return self.parameterError(msg='故障分类参数错误', data=None)
                    if not judge_is_exist(int(fault_type_id)):
                        return self.parameterError(msg='故障分类不存在', data=None)
                    filters.append(FaultKnowledgeBase.fault_type_id == int(fault_type_id))

                if soft_hard_ware_id:
                    if not re.compile(r'^-?\d+$').match(soft_hard_ware_id):
                        return self.parameterError(msg='软件/硬件故障参数错误', data=None)
                    if not judge_is_exist(int(soft_hard_ware_id)):
                        return self.parameterError(msg='软件/硬件故障不存在', data=None)
                    filters.append(FaultKnowledgeBase.soft_hard_ware_id == int(soft_hard_ware_id))

                if fault_level_id:
                    if not re.compile(r'^-?\d+$').match(fault_level_id):
                        return self.parameterError(msg='故障等级参数错误', data=None)
                    if not judge_is_exist(int(fault_level_id)):
                        return self.parameterError(msg='故障等级不存在', data=None)
                    filters.append(FaultKnowledgeBase.fault_level_id == int(fault_level_id))

                total = user_session.query(func.count(FaultKnowledgeBase.id)).filter(*filters).scalar()
                fault_records = user_session.query(FaultKnowledgeBase).filter(*filters).order_by(
                    FaultKnowledgeBase.id.desc()).limit(
                    page_size).offset((page - 1) * page_size).all()

                data = []
                if fault_records:
                    for record in fault_records:
                        data.append({
                            "id": record.id,
                            "fault_entry_name": record.fault_entry_name,
                            "discover_date": record.discover_date.strftime('%Y-%m-%d') if record.discover_date else "",
                            "inclusion_date": record.inclusion_date.strftime(
                                '%Y-%m-%d') if record.inclusion_date else "",
                            "create_user": record.create_user.name if record.create_user else "",
                            "fault_keyword": record.fault_keyword,
                            "fault_type_id": record.fault_type_id,
                            "fault_type_desc": record.fault_type.name if record.fault_type else "",
                            "soft_hard_ware_id": record.soft_hard_ware_id,
                            "soft_hard_ware_desc": record.soft_hard_ware.name if record.soft_hard_ware else "",
                            "fault_level_id": record.fault_level_id,
                            "fault_level_desc": record.fault_level.name if record.fault_level else "",
                            "analysis_causes": record.analysis_causes,
                            "analysis_causes_imgs": [{'file_name': file.file_name, 'file_url':file.file_url, 'id': file.id} for file in record.files if file.belong == 'causes'],
                            "background_summarize": record.background_summarize,
                            "device_type": record.device_type,
                            "manufacturer": record.manufacturer,
                            "device_model": record.device_model,
                            "problem_desc": record.problem_desc,
                            "problem_desc_imgs": [{'file_name': file.file_name, 'file_url':file.file_url, 'id': file.id} for file in record.files if file.belong == 'problem'],
                            "handle_measures": record.handle_measures,
                            "handle_measures_imgs": [{'file_name': file.file_name, 'file_url':file.file_url, 'id': file.id} for file in record.files if
                                                     file.belong == 'measures'],
                            "propose_list": [
                                {
                                    "user_name": propose.create_user.name if propose.create_user else "",
                                    "create_time": propose.create_time.strftime(
                                        '%Y-%m-%d %H:%M:%S') if propose.create_time else "",
                                    "propose_content": propose.propose_content
                                }
                                for propose in record.proposes
                            ]
                        })

                return self.pagingSuccess(data, total, page_size, page)
            elif action == 'GetSelectList':
                """
                获取故障分类/软硬件故障/故障等级列表
                """
                list_type = self.get_argument('list_type', None)  # 列表类型 1:故障分类，2:软件/硬件故障，3:故障等级
                filters = [FaultKnowledgeDictionaries.is_use==1]
                if list_type:
                    if not re.compile(r'^-?\d+$').match(list_type):
                        return self.customError("列表类型错误")
                    filters.append(FaultKnowledgeDictionaries.type == int(list_type))
                data = user_session.query(FaultKnowledgeDictionaries).filter(*filters).all()
                result = []
                if data:
                    temp_dict = {}
                    for item in data:
                        type_id = item.type
                        if type_id not in temp_dict:
                            temp_dict[type_id] = []
                        temp_dict[type_id].append({'id': item.id, 'name': item.name})
                    for type_id, items in temp_dict.items():
                        result.append({'type_id': type_id, 'list': items})
                return self.commonSuc(result)
            else:
                return self.new_pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            pass

    @tornado.web.authenticated
    def post(self, action):
        self.refreshSession()
        try:
        # if 1:
            session = self.getOrNewSession()
            user_id = session.user['id']
            # 附件单个大小限制和类型
            max_file_size = 50 * 1024 * 1024  # 50MB限制
            allow_type = ['jpg', 'jpeg', 'png', 'svg']
            if action == "AddFaultRecord":
                """
                新增故障知识库内容
                """
                fault_entry_name = self.get_argument('fault_entry_name', None)  # 条目名称
                fault_keyword = self.get_argument('fault_keyword', None)  # 关键字
                background_summarize = self.get_argument('background_summarize', '')  # 背景概述
                discover_date = self.get_argument('discover_date', None)  # 发现日期
                fault_type_id = self.get_argument('fault_type_id', None)  # 故障分类ID
                soft_hard_ware_id = self.get_argument('soft_hard_ware_id', None)  # 软件/硬件故障ID
                fault_level_id = self.get_argument('fault_level_id', None)  # 故障等级ID
                device_type = self.get_argument('device_type', None)  # 设备类型
                manufacturer = self.get_argument('manufacturer', '')  # 生产厂家
                device_model = self.get_argument('device_model', '')  # 设备型号
                problem_desc = self.get_argument('problem_desc', None)  # 问题描述
                analysis_causes = self.get_argument('analysis_causes', '')  # 原因分析
                handle_measures = self.get_argument('handle_measures', None)  # 处理措施
                problem_desc_imgs = self.get_argument('problem_desc_imgs', None)
                analysis_causes_imgs = self.get_argument('analysis_causes_imgs', None)
                handle_measures_imgs = self.get_argument('handle_measures_imgs', None)

                if DEBUG:
                    logging.info(
                        'fault_entry_name:%s, fault_keyword:%s, background_summarize:%s, discover_date:%s, fault_type_id:%s, '
                        'soft_hard_ware_id:%s, fault_level_id:%s, device_type:%s, manufacturer:%s, device_model:%s, '
                        'problem_desc:%s, analysis_causes:%s, '
                        'handle_measures:%s',
                        fault_entry_name, fault_keyword, background_summarize, discover_date, fault_type_id,
                        soft_hard_ware_id, fault_level_id, device_type, manufacturer, device_model,
                        problem_desc, analysis_causes, handle_measures
                    )

                # 参数校验
                if not fault_entry_name or not fault_keyword or not fault_type_id or not soft_hard_ware_id or not fault_level_id or not device_type or not problem_desc or not handle_measures:
                    return self.parameterError(msg='参数不完整', data=None)
                # if not problem_desc_imgs:
                #     return self.parameterError(msg='请上传问题描述的图片', data=None)
                # if not handle_measures_imgs:
                #     return self.parameterError(msg='请上传处理措施的图片', data=None)
                if problem_desc_imgs:
                    if not can_be_parsed_as_list(problem_desc_imgs):
                        return self.parameterError(msg='问题描述图片参数类型错误', data=None)
                    problem_desc_imgs = json.loads(problem_desc_imgs)
                if handle_measures_imgs:
                    if not can_be_parsed_as_list(handle_measures_imgs):
                        return self.parameterError(msg='处理措施图片参数类型错误', data=None)
                    handle_measures_imgs = json.loads(handle_measures_imgs)
                if analysis_causes_imgs:
                    if not can_be_parsed_as_list(analysis_causes_imgs):
                        return self.parameterError(msg='原因分析图片参数类型错误', data=None)
                    analysis_causes_imgs = json.loads(analysis_causes_imgs)

                # files = self.request.files
                # problem_desc_imgs = files.get('problem_desc_imgs')
                # analysis_causes_imgs = files.get('analysis_causes_imgs')
                # handle_measures_imgs = files.get('handle_measures_imgs')
                # if not problem_desc_imgs:
                #     return self.parameterError(msg='请上传问题描述的图片', data=None)
                # if not handle_measures_imgs:
                #     return self.parameterError(msg='请上传处理措施的图片', data=None)

                if len(fault_keyword) > 500 or len(problem_desc) > 500 or len(handle_measures) > 500 or len(background_summarize) > 500 or len(analysis_causes) > 500:
                    return self.parameterError(msg='请检查关键字、问题描述、处理措施、背景概述、原因分析参数长度是否超过500', data=None)

                if not re.compile(r'^-?\d+$').match(fault_type_id) or not re.compile(r'^-?\d+$').match(
                        soft_hard_ware_id) or not re.compile(r'^-?\d+$').match(fault_level_id):
                    return self.parameterError(msg='请检查故障分类、软件/硬件故障、故障等级参数类型', data=None)


                if not judge_is_exist(int(fault_type_id)):
                    return self.parameterError(msg='故障分类不存在', data=None)
                if not judge_is_exist(int(soft_hard_ware_id)):
                    return self.parameterError(msg='软件/硬件故障不存在', data=None)
                if not judge_is_exist(int(fault_level_id)):
                    return self.parameterError(msg='故障等级不存在', data=None)

                if discover_date:
                    try:
                        discover_date = datetime.strptime(discover_date, '%Y-%m-%d').date()
                    except ValueError:
                        return self.parameterError(msg='发现日期格式错误', data=None)
                    if discover_date>datetime.now().date():
                        return self.parameterError(msg='发现日期不能大于当前日期', data=None)

                # 问题描述图片验证
                # for i in problem_desc_imgs:
                #     data = i.get('body')
                #     file_size = len(data)
                #     if file_size > max_file_size:
                #         return self.parameterError(msg='问题描述上传附件超过50MB限制，请检查！', data=None)
                #     filename = i.get('filename')
                #     filename_data = os.path.splitext(filename)
                #     if filename_data[1][1:] not in allow_type:
                #         return self.parameterError(msg='问题描述上传图片格式错误，请检查！', data=None)

                # 处理措施图片验证
                # for i in handle_measures_imgs:
                #     data = i.get('body')
                #     file_size = len(data)
                #     if file_size > max_file_size:
                #         return self.parameterError(msg='处理措施上传附件超过50MB限制，请检查！', data=None)
                #     filename = i.get('filename')
                #     filename_data = os.path.splitext(filename)
                #     if filename_data[1][1:] not in allow_type:
                #         return self.parameterError(msg='处理措施上传图片格式错误，请检查！', data=None)

                # 原因分析图片验证
                # if analysis_causes_imgs:
                #     for i in analysis_causes_imgs:
                #         data = i.get('body')
                #         file_size = len(data)
                #         if file_size > max_file_size:
                #             return self.parameterError(msg='原因分析上传附件超过50MB限制，请检查！', data=None)
                #         filename = i.get('filename')
                #         filename_data = os.path.splitext(filename)
                #         if filename_data[1][1:] not in allow_type:
                #             return self.parameterError(msg='原因分析上传图片格式错误，请检查！', data=None)


                # 创建故障记录
                new_record = FaultKnowledgeBase(
                    fault_entry_name=fault_entry_name,
                    discover_date=discover_date if discover_date else None,
                    inclusion_date=datetime.now().date(),
                    fault_keyword=fault_keyword,
                    fault_type_id=int(fault_type_id),
                    soft_hard_ware_id=int(soft_hard_ware_id),
                    fault_level_id=int(fault_level_id),
                    device_type=device_type,
                    manufacturer=manufacturer,
                    device_model=device_model,
                    background_summarize=background_summarize,
                    problem_desc=problem_desc,
                    analysis_causes=analysis_causes,
                    handle_measures=handle_measures,
                    create_user_id=user_id,
                    create_time=datetime.now()
                )
                user_session.add(new_record)
                user_session.flush()

                # 添加附件
                # 问题描述图片
                if problem_desc_imgs:
                    save_status = save_file_data(problem_desc_imgs, new_record.id, 'problem')
                    if not save_status:
                        return self.commonError("问题描述图片上传超时", None, 408)


                # 处理措施图片
                if handle_measures_imgs:
                    handle_save_status = save_file_data(handle_measures_imgs, new_record.id, 'measures')
                    if not handle_save_status:
                        return self.commonError("处理措施图片上传超时", None, 408)

                # 原因分析图片
                if analysis_causes_imgs:
                    handle_save_status = save_file_data(analysis_causes_imgs, new_record.id, 'causes')
                    if not handle_save_status:
                        return self.commonError("原因分析图片上传超时", None, 408)

                user_session.commit()

                return self.commonSuc(data=[], info=None)
            elif action == "UpdateFaultRecord":
                """
                更新故障知识库
                """
                # 获取参数
                fault_id = self.get_argument('fault_id', None)  # 记录id
                fault_entry_name = self.get_argument('fault_entry_name', None)  # 条目名称
                fault_keyword = self.get_argument('fault_keyword', None)  # 关键字
                background_summarize = self.get_argument('background_summarize', '')  # 背景概述
                discover_date = self.get_argument('discover_date', None)  # 发现日期
                fault_type_id = self.get_argument('fault_type_id', None)  # 故障分类ID
                soft_hard_ware_id = self.get_argument('soft_hard_ware_id', None)  # 软件/硬件故障ID
                fault_level_id = self.get_argument('fault_level_id', None)  # 故障等级ID
                device_type = self.get_argument('device_type', None)  # 设备类型
                manufacturer = self.get_argument('manufacturer', '')  # 生产厂家
                device_model = self.get_argument('device_model', '')  # 设备型号
                problem_desc = self.get_argument('problem_desc', None)  # 问题描述
                delete_problem_desc_img_ids = self.get_argument('delete_problem_desc_img_ids', None)  # 删除的问题描述图片id的list
                analysis_causes = self.get_argument('analysis_causes', '')  # 原因分析
                delete_analysis_causes_img_ids = self.get_argument('delete_analysis_causes_img_ids',None)  # 删除的原因分析图片id的list
                handle_measures = self.get_argument('handle_measures', None)  # 处理措施
                delete_handle_measures_img_ids = self.get_argument('delete_handle_measures_img_ids',None)  # 删除的处理措施图片id的list
                add_problem_desc_imgs = self.get_argument('add_problem_desc_imgs', '[]')
                add_handle_measures_imgs = self.get_argument('add_handle_measures_imgs', '[]')
                add_analysis_causes_imgs = self.get_argument('add_analysis_causes_imgs', '[]')

                # 参数校验
                if not fault_id or not fault_entry_name or not fault_keyword or not fault_type_id or not soft_hard_ware_id or not fault_level_id or not device_type or not problem_desc or not handle_measures:
                    return self.parameterError(msg='必填内容不完整', data=None)

                if not re.compile(r'^-?\d+$').match(fault_id):
                    return self.parameterError(msg='请检查记录id参数类型', data=None)

                # 查询故障记录
                record = user_session.query(FaultKnowledgeBase).filter_by(id=fault_id).first()
                if not record:
                    return self.commonError('记录不存在', None, 404)

                if len(fault_keyword) > 500 or len(problem_desc) > 500 or len(handle_measures) > 500 or len(background_summarize) > 500 or len(analysis_causes) > 500:
                    return self.parameterError(msg='请检查关键字、问题描述、处理措施、背景概述、原因分析参数长度是否超过500', data=None)

                if not re.compile(r'^-?\d+$').match(fault_type_id) or not re.compile(r'^-?\d+$').match(
                        soft_hard_ware_id) or not re.compile(r'^-?\d+$').match(fault_level_id):
                    return self.parameterError(msg='请检查故障分类、软件/硬件故障、故障等级参数类型', data=None)

                if not judge_is_exist(int(fault_type_id)):
                    return self.parameterError(msg='故障分类不存在', data=None)
                if not judge_is_exist(int(soft_hard_ware_id)):
                    return self.parameterError(msg='软件/硬件故障不存在', data=None)
                if not judge_is_exist(int(fault_level_id)):
                    return self.parameterError(msg='故障等级不存在', data=None)

                if discover_date:
                    try:
                        discover_date = datetime.strptime(discover_date, '%Y-%m-%d').date()
                    except ValueError:
                        return self.parameterError(msg='发现日期格式错误', data=None)
                    if discover_date > datetime.now().date():
                        return self.parameterError(msg='发现日期不能大于当前日期', data=None)

                # 判断更新后的问题描述图片和处理措施图片是否为空
                # files = self.request.files
                # add_problem_desc_imgs = files.get('add_problem_desc_imgs')
                # add_handle_measures_imgs = files.get('add_handle_measures_imgs')
                # add_analysis_causes_imgs = files.get('add_analysis_causes_imgs')

                if can_be_parsed_as_list(add_problem_desc_imgs):
                    add_problem_desc_imgs = json.loads(add_problem_desc_imgs) if len(json.loads(add_problem_desc_imgs)) > 0 else None
                else:
                    add_problem_desc_imgs = None
                if can_be_parsed_as_list(add_handle_measures_imgs):
                    add_handle_measures_imgs = json.loads(add_handle_measures_imgs) if len(json.loads(add_handle_measures_imgs)) > 0 else None
                else:
                    add_handle_measures_imgs = None
                if can_be_parsed_as_list(add_analysis_causes_imgs):
                    add_analysis_causes_imgs = json.loads(add_analysis_causes_imgs) if len(json.loads(add_analysis_causes_imgs)) > 0 else None
                else:
                    add_analysis_causes_imgs = None

                now_problem_desc_imgs_ids = [item.id for item in record.files if item.belong == 'problem']
                now_handle_measures_imgs_ids = [item.id for item in record.files if item.belong == 'measures']
                if delete_problem_desc_img_ids:
                    if not can_be_parsed_as_list(delete_problem_desc_img_ids):
                        return self.parameterError(msg='删除的问题描述图片参数类型错误', data=None)
                    delete_problem_desc_img_ids = json.loads(delete_problem_desc_img_ids)
                    # if are_lists_equal(now_problem_desc_imgs_ids, delete_problem_desc_img_ids) and not add_problem_desc_imgs:
                    #     return self.parameterError(msg='更新后的问题描述相关图片不可为空', data=None)
                if delete_handle_measures_img_ids:
                    if not can_be_parsed_as_list(delete_handle_measures_img_ids):
                        return self.parameterError(msg='删除的处理措施图片参数类型错误', data=None)
                    delete_handle_measures_img_ids = json.loads(delete_handle_measures_img_ids)
                    # if are_lists_equal(now_handle_measures_imgs_ids, delete_handle_measures_img_ids) and not add_handle_measures_imgs:
                    #     return self.parameterError(msg='更新后的处理措施相关图片不可为空', data=None)
                if delete_analysis_causes_img_ids:
                    if not can_be_parsed_as_list(delete_analysis_causes_img_ids):
                        return self.parameterError(msg='删除的原因分析图片参数类型错误', data=None)
                    delete_analysis_causes_img_ids = json.loads(delete_analysis_causes_img_ids)

                # 问题描述图片验证
                # if add_problem_desc_imgs:
                #     for i in add_problem_desc_imgs:
                #         data = i.get('body')
                #         file_size = len(data)
                #         if file_size > max_file_size:
                #             return self.parameterError(msg='问题描述上传附件超过50MB限制，请检查！', data=None)
                #         filename = i.get('filename')
                #         filename_data = os.path.splitext(filename)
                #         if filename_data[1][1:] not in allow_type:
                #             return self.parameterError(msg='问题描述上传图片格式错误，请检查！', data=None)

                # 处理措施图片验证
                # if add_handle_measures_imgs:
                #     for i in add_handle_measures_imgs:
                #         data = i.get('body')
                #         file_size = len(data)
                #         if file_size > max_file_size:
                #             return self.parameterError(msg='处理措施上传附件超过50MB限制，请检查！', data=None)
                #         filename = i.get('filename')
                #         filename_data = os.path.splitext(filename)
                #         if filename_data[1][1:] not in allow_type:
                #             return self.parameterError(msg='处理措施上传图片格式错误，请检查！', data=None)

                # 原因分析图片验证
                # if add_analysis_causes_imgs:
                #     for i in add_analysis_causes_imgs:
                #         data = i.get('body')
                #         file_size = len(data)
                #         if file_size > max_file_size:
                #             return self.parameterError(msg='原因分析上传附件超过50MB限制，请检查！', data=None)
                #         filename = i.get('filename')
                #         filename_data = os.path.splitext(filename)
                #         if filename_data[1][1:] not in allow_type:
                #             return self.parameterError(msg='原因分析上传图片格式错误，请检查！', data=None)

                # 更新故障记录
                record.fault_entry_name = fault_entry_name
                record.discover_date = discover_date if discover_date else None
                record.inclusion_date = datetime.now().date()
                record.fault_keyword = fault_keyword
                record.fault_type_id = int(fault_type_id)
                record.soft_hard_ware_id = int(soft_hard_ware_id)
                record.fault_level_id = int(fault_level_id)
                record.device_type = device_type
                record.manufacturer = manufacturer
                record.device_model = device_model
                record.background_summarize = background_summarize
                record.problem_desc = problem_desc
                record.analysis_causes = analysis_causes
                record.handle_measures = handle_measures
                record.update_time = datetime.now()

                # 删除问题描述图片
                if delete_problem_desc_img_ids:
                    delete_file_data(delete_problem_desc_img_ids, fault_id, 'problem')

                # 添加问题描述图片
                if add_problem_desc_imgs is not None:
                    save_file_data(add_problem_desc_imgs, fault_id, 'problem')

                # 删除处理措施图片
                if delete_handle_measures_img_ids:
                    delete_file_data(delete_handle_measures_img_ids, fault_id, 'measures')

                # 添加处理措施图片
                if add_handle_measures_imgs is not None:
                    save_file_data(add_handle_measures_imgs, fault_id, 'measures')

                # 删除原因分析图片
                if delete_analysis_causes_img_ids:
                    delete_file_data(delete_analysis_causes_img_ids, fault_id, 'causes')

                # 添加原因分析图片
                if add_analysis_causes_imgs is not None:
                    save_file_data(add_analysis_causes_imgs, fault_id, 'causes')
                user_session.commit()
                return self.commonSuc(data='', info=None)
            elif action == "DeleteFaultRecord":
                """
                删除故障知识库
                """
                fault_id = self.get_argument("fault_id", None)
                if not fault_id:
                    return self.parameterError(msg='记录id不完整', data=None)
                if not re.compile(r'^-?\d+$').match(fault_id):
                    return self.parameterError(msg='记录id格式不正确', data=None)
                record = user_session.query(FaultKnowledgeBase).filter(FaultKnowledgeBase.id == fault_id).first()
                if not record:
                    return self.commonError('记录不存在', None, 404)
                record.is_use = 0
                user_session.commit()
                return self.commonSuc(data='', info=None)
            elif action == "AddFaultPropose":
                """
                添加故障知识库建议
                """
                # 获取入参
                fault_id = self.get_argument('fault_id', None)  # 记录id
                propose_content = self.get_argument('propose_content', None)

                # 参数校验
                if not fault_id or not propose_content:
                    return self.parameterError(msg='必填内容不完整', data=None)
                if not re.compile(r'^-?\d+$').match(fault_id):
                    return self.parameterError(msg='记录id格式不正确', data=None)

                if len(propose_content) > 500:
                    return self.parameterError(msg='请将建议内容控制在500字以内', data=None)

                # 检查故障记录是否存在
                fault_record = user_session.query(FaultKnowledgeBase).filter_by(id=fault_id).first()
                if not fault_record:
                    return self.commonError('记录不存在', None, 404)

                # 创建建议记录
                new_propose = FaultKnowledgePropose(
                    fault_id=int(fault_id),
                    propose_content=propose_content,
                    propose_user_id=user_id,
                    create_time=datetime.now()
                )
                user_session.add(new_propose)
                user_session.commit()

                return self.commonSuc(data='', info=None)
            elif action == "UploadImage":
                files = self.request.files
                imgs = files.get('img')
                if not imgs:
                    return self.parameterError(msg='请选择上传图片', data=None)
                if len(imgs) > 1:
                    return self.parameterError(msg='请上传单张图片', data=None)

                # 处理单个文件
                img = imgs[0]
                data = img.get('body')
                file_size = len(data)
                max_file_size = 50 * 1024 * 1024  # 50 MB
                allow_type = ['jpg', 'jpeg', 'png', 'gif']  # 允许的文件类型

                if file_size > max_file_size:
                    return self.parameterError(msg='上传图片不得超过50M！', data=None)

                filename = img.get('filename')
                filename_data = os.path.splitext(filename)

                if filename_data[1][1:] not in allow_type:
                    return self.parameterError(msg='上传图片格式错误，请检查！', data=None)

                filename = img.get('filename')
                filename_data = os.path.splitext(filename)
                new_filename = filename_data[0] + '--' + str(int(timeUtils.nowSecs())) + filename_data[1]
                url = upload_file(data, 'rhyc', new_filename)
                if not url:
                    return self.commonError("问题描述图片上传超时", None, 408)
                url = url.split('?', 1)[0]
                res = {
                    "file_url": url,
                    "file_name": filename
                }
                return self.commonSuc(data=res, info=None)
            else:
                return self.new_pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            pass


def judge_is_date(judge_time, time_format='%Y-%m-%d'):
    """
    判断日期格式是否正确
    :param time_format:
    :param judge_time:
    :return:
    """
    try:
        # 尝试将字符串转换为 datetime 对象
        datetime.strptime(str(judge_time).strip(), time_format)
        return True
    except ValueError as e:
        return False


def can_be_parsed_as_list(json_str):
    """
    判断变量是否可以转换成list
    :param json_str:
    :return:
    """
    try:
        result = json.loads(json_str)
        return isinstance(result, list)
    except json.JSONDecodeError:
        return False


def are_lists_equal(list1, list2):
    """
    判断list1是否在list2里
    """
    return set(list1).intersection(set(list2)) == set(list1)


def save_file_data(file_data, fault_id, belong):
    """
    保存附件图片
    """
    try:
        if len(file_data) == 0:
            return True
        for i in file_data:
            # data = i.get('body')
            # filename = i.get('filename')
            # filename_data = os.path.splitext(filename)
            # new_filename = filename_data[0] + '--' + str(int(timeUtils.nowSecs())) + filename_data[1]
            # url = upload_file(data, 'rhyc', new_filename)
            # if not url:
            #     return False
            url = i['file_url']
            filename = i['file_name']
            new_file = FaultKnowledgeFiles(
                fault_id=int(fault_id),
                belong=belong,
                file_url=url,
                file_name=filename,
                create_time=datetime.now()
            )
            user_session.add(new_file)
        return True
    except Exception as E:
        logging.error(E)
        return False



def delete_file_data(ids, fault_id, belong):
    """
    逻辑删除附件图片
    """
    if len(ids) > 0:
        user_session.query(FaultKnowledgeFiles).filter(
            FaultKnowledgeFiles.fault_id == int(fault_id),
            FaultKnowledgeFiles.belong == belong,
            FaultKnowledgeFiles.id.in_(ids)
        ).update({FaultKnowledgeFiles.is_use: 0}, synchronize_session=False)
    return True


def judge_is_exist(j_id):
    """
    判断字典信息是否存在
    """
    return user_session.query(FaultKnowledgeDictionaries.name).filter(FaultKnowledgeDictionaries.id == j_id).first()
