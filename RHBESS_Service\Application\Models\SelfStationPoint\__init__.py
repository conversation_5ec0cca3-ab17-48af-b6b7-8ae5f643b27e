#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 16:30:39
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\__init__.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-07 14:20:33



def create_all():
    u'初始化所有表'
    
    from Application.Models.SelfStationPoint.t_station import StationPT
    from Application.Models.SelfStationPoint.t_device import DevicePT
    from Application.Models.SelfStationPoint.t_measure import MeasurePT
    from Application.Models.SelfStationPoint.t_discrete import DiscretePT
    from Application.Models.SelfStationPoint.t_cumulant import CumulantPT
    from Application.Models.SelfStationPoint.t_status import StatusPT
    from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT



    StationPT.init()
    DevicePT.init()
    MeasurePT.init()
    DiscretePT.init()
    CumulantPT.init()
    StatusPT.init()
    StatusBitsPT.init()

