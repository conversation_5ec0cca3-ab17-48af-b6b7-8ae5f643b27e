#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-25 09:08:56
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\Utils\read_csv.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-02 14:28:50
import math,os
import sys
reload(sys)
sys.setdefaultencoding("utf-8")

import pandas as pd
import numpy as np

data,obj = [],{}
# df = pd.read_csv('/home/<USER>/csvfiles/1661702400Bank02_20220520.csv',header=0,usecols=[0,7,8,10],encoding='GBK')
# print df
# for c in df:
#     all = []
#     print df[c]
#     for s in df[c]:
#         all.append(s)
#     obj[c] = all
# return self.returnTypeSuc(obj)
## 此方法可过滤时间，即查询所有数据后再过滤
# file1=np.array(df)
# print '**********************'
# print file1
# for f in file1:
#     print f

# df = pd.read_csv("/home/<USER>/csvfiles/1661702400Bank02_20220520.csv",encoding='GBK')

# oo = os.listdir('/home/<USER>/csvfiles')
# print oo

'''利用pandas补全数据'''
import pandas as pd

df = pd.DataFrame({"date":["2022-01-01 12:01:11","2022-01-01 12:01:22", "2022-01-01 12:01:32", "2022-01-01 12:01:37"], "value":range(1,5)})

df["date"] = df["date"].astype("datetime64")  # 确保数据格式为日期

date_range = pd.date_range(start=df["date"].min(), end=df["date"].max(), freq="5S")  # freq="D"表示按天，可以按分钟，月，季度，年等

# print '************',df.set_index("date")
end = df.set_index("date").reindex(index=date_range,method='ffill')  # ffill 向前填充

print 'end--:',end




