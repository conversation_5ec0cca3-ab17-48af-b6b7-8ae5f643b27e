package com.robestec.analysis.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 远程控制策略配置类
 * 对应Python中的配置参数
 */
@Configuration
@ConfigurationProperties(prefix = "telecontrol")
public class TelecontrolConfig {

    /**
     * 文件上传配置
     */
    private FileUpload fileUpload = new FileUpload();

    /**
     * MinIO配置
     */
    private Minio minio = new Minio();

    /**
     * Redis配置
     */
    private Redis redis = new Redis();

    /**
     * 策略配置
     */
    private Strategy strategy = new Strategy();

    public FileUpload getFileUpload() {
        return fileUpload;
    }

    public void setFileUpload(FileUpload fileUpload) {
        this.fileUpload = fileUpload;
    }

    public Minio getMinio() {
        return minio;
    }

    public void setMinio(Minio minio) {
        this.minio = minio;
    }

    public Redis getRedis() {
        return redis;
    }

    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    public Strategy getStrategy() {
        return strategy;
    }

    public void setStrategy(Strategy strategy) {
        this.strategy = strategy;
    }

    /**
     * 文件上传配置
     */
    public static class FileUpload {
        /**
         * 上传路径
         */
        private String uploadPath = "/tmp/uploads";

        /**
         * 最大文件大小（MB）
         */
        private int maxFileSize = 10;

        /**
         * 允许的文件类型
         */
        private String[] allowedTypes = {"xlsx", "xls", "csv"};

        public String getUploadPath() {
            return uploadPath;
        }

        public void setUploadPath(String uploadPath) {
            this.uploadPath = uploadPath;
        }

        public int getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(int maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public String[] getAllowedTypes() {
            return allowedTypes;
        }

        public void setAllowedTypes(String[] allowedTypes) {
            this.allowedTypes = allowedTypes;
        }
    }

    /**
     * MinIO配置
     */
    public static class Minio {
        /**
         * 端点
         */
        private String endpoint = "http://localhost:9000";

        /**
         * 访问密钥
         */
        private String accessKey = "minioadmin";

        /**
         * 秘密密钥
         */
        private String secretKey = "minioadmin";

        /**
         * 存储桶名称
         */
        private String bucketName = "tianlu";

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public void setAccessKey(String accessKey) {
            this.accessKey = accessKey;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }

        public String getBucketName() {
            return bucketName;
        }

        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }

    /**
     * Redis配置
     */
    public static class Redis {
        /**
         * 键前缀
         */
        private String keyPrefix = "telecontrol_strategy_";

        /**
         * 过期时间（秒）
         */
        private int expireTime = 3600;

        public String getKeyPrefix() {
            return keyPrefix;
        }

        public void setKeyPrefix(String keyPrefix) {
            this.keyPrefix = keyPrefix;
        }

        public int getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(int expireTime) {
            this.expireTime = expireTime;
        }
    }

    /**
     * 策略配置
     */
    public static class Strategy {
        /**
         * 默认策略模板文件名
         */
        private String templateFileName = "策略模板.xlsx";

        /**
         * 策略执行超时时间（秒）
         */
        private int executionTimeout = 300;

        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;

        public String getTemplateFileName() {
            return templateFileName;
        }

        public void setTemplateFileName(String templateFileName) {
            this.templateFileName = templateFileName;
        }

        public int getExecutionTimeout() {
            return executionTimeout;
        }

        public void setExecutionTimeout(int executionTimeout) {
            this.executionTimeout = executionTimeout;
        }

        public int getMaxRetryCount() {
            return maxRetryCount;
        }

        public void setMaxRetryCount(int maxRetryCount) {
            this.maxRetryCount = maxRetryCount;
        }
    }
}
