import datetime
import json
import time
from django.conf import settings
from numpy import average
import pymysql
import logging
import numpy as np
import decimal
from settings.meter_settings import METER_DIC_IDC
# 创建任务函数
from apis.user import models
import pymysql
from dbutils.persistent_db import PersistentDB
import logging
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "TianLuAppBackend.settings")


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")

def electricity_h_Count():#每小时跑
    """逐时冲放电量"""
    now_time = datetime.datetime.now()#跑小时
    stations_ins = models.StationDetails.objects.exclude(id__in=[9, 10]).all()  # 查询总的站
    # stations_ins = models.StationDetails.objects.exclude(id__in=[8,9, 10,40,49]).all()  # 查询总的站
    # stations_ins = models.StationDetails.objects.filter(id__in=[ 4,5,14,16,26,22, 24,29,32,33,38,39,44,47,48,50,52,53,56,66]).all()  # 查询总的站
    # stations_ins = models.StationDetails.objects.filter(id=43).all()  # 查询总的站
    for station_insss in stations_ins:
        if station_insss:
            station_time_ = station_insss.create_time
            bb = now_time.strftime("%Y-%m-%d %H:%M:%S")
            b = getBeforeHouseStr(now_time)
            if station_time_ < now_time:
                detail = get_stations(station_insss,bb)
                soc_list = []
                soc_list_2 = []
                chag_list = []
                disg_list = []
                for d in detail:
                    chag__=float(d['charge'])
                    disg__=float(d['discharge'])
                    soc__=float(d['soc'])
                    soc__2 = float(d['soc'])

                    if chag__==0.0 and disg__==0.0:
                        soc__2 = 0.0
                    elif soc__==0.0:
                        chag__=0.0
                        disg__=0.0
                    elif soc__>55:
                        soc__2 = 0.0
                        chag__ = 0.0
                        disg__ = 0.0
                    try:
                        models.FReport.objects.create(day=d['time'][0:10], hour=d['time'][11:13],chag=str(chag__),
                                                      disg=str(disg__), soc=str(soc__), soc_2=str(soc__2), station=station_insss.english_name,
                                                      app=station_insss.app,station_type=2, unit_name=d['device'])
                    except Exception as e:
                        break
                    if chag__!=0.0:
                        chag_list.append(chag__)
                    else:
                        chag_list.append(0.0)
                    if disg__ != 0.0:
                        disg_list.append(disg__)
                    else:
                        disg_list.append(0.0)
                    if soc__ != 0.0 or soc__ != 0:
                        soc_list.append(soc__)
                    if soc__2 != 0.0:
                        soc_list_2.append(soc__2)
                if soc_list_2 == []:
                    soc_avg_2 = 0.0
                    chag_all = 0.0
                    disg_all = 0.0
                    if soc_list==[]:
                        soc_avg = 0.0
                    else:
                        soc_avg = float('%.2f' % (average(soc_list)))
                else:
                    chag_all = float('%.2f' % (sum(chag_list)))
                    disg_all = float('%.2f' % (sum(disg_list)))
                    soc_avg_2 = float('%.2f' % (average(soc_list_2)))
                    if soc_list == []:
                        soc_avg = 0.0
                    else:
                        soc_avg = float('%.2f' % (average(soc_list)))
                try:
                    models.FReport.objects.create(day=b[0:10], hour=b[11:13],
                                                    chag=str(chag_all),
                                                    disg=str(disg_all), soc=str(soc_avg),soc_2=str(soc_avg_2),
                                                    station=station_insss.english_name,
                                                    app=station_insss.app,
                                                    station_type=1, unit_name=0)
                except Exception as e:
                    if 'Duplicate entry' in e.args:
                        pass
                    else:
                        raise e
            else:
                pass
# def electricity_h_Count():#跑历史
#     """逐时冲放电量"""
#     # time_list=dateToDataList('2023-10-28','2023-10-28')
#     # time_list=dateToDataList('2023-10-10','2023-10-10')
#     time_list=['2024-01-04']
#     print (';;;;;;;;;;;;;;;;', time_list)
#     stations_ins = models.StationDetails.objects.exclude(id__in=[9, 10]).all()  # 查询总的站
#     # stations_ins = models.StationDetails.objects.exclude(id__in=[8,9, 10,40,49]).all()  # 查询总的站
#     # stations_ins = models.StationDetails.objects.filter(id__in=[24,47,49,50,]).all()  # 查询总的站
#     # stations_ins = models.StationDetails.objects.filter(id=43).all()  # 查询总的站
#     for tt in time_list:#跑历史
#         # list_h=[5,14]
#         for ttt in range(14, 15):
#                 if ttt > 9:
#                     q = str(ttt)
#                 else:
#                     q = '0' + str(ttt)
#                 now_time = tt + ' ' + q + ':00:30'
#                 print ('cccccccccccccc', now_time)
#                 time_ = time.strptime(now_time, "%Y-%m-%d %H:%M:%S")
#                 timeStamp = int(time.mktime(time_))
#                 tttttt = timeStamp - 3600
#                 b = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(tttttt)))
#                 for station_insss in stations_ins:
#                     print ('bbbbbbbbbbbbbbb', station_insss.english_name)
#                     if station_insss:
#                         print ('.............')
#                         station_time_ = station_insss.create_time
#                         station_time__ = station_time_.strftime("%Y-%m-%d %H:%M:%S")#跑历史
#                         if station_time__ < now_time:
#                             detail = get_stations(station_insss,now_time)#跑历史
#                             soc_list = []
#                             soc_list_2 = []
#                             chag_list = []
#                             disg_list = []
#                             print ('??????????????????????',detail)
#                             for d in detail:
#                                 chag__=float(d['charge'])
#                                 disg__=float(d['discharge'])
#                                 soc__=float(d['soc'])
#                                 soc__2 = float(d['soc'])
#
#                                 if chag__==0.0 and disg__==0.0:
#                                     soc__2 = 0.0
#                                 elif soc__==0.0:
#                                     chag__=0.0
#                                     disg__=0.0
#                                 elif soc__>55:
#                                     soc__2 = 0.0
#                                     chag__ = 0.0
#                                     disg__ = 0.0
#                                 try:
#                                     models.FReport.objects.create(day=d['time'][0:10], hour=d['time'][11:13],chag=str(chag__),
#                                                                   disg=str(disg__), soc=str(soc__), soc_2=str(soc__2), station=station_insss.english_name,
#                                                                   app=station_insss.app,station_type=2, unit_name=d['device'])
#                                 except Exception as e:
#                                     break
#                                 if chag__!=0.0:
#                                     chag_list.append(chag__)
#                                 else:
#                                     chag_list.append(0.0)
#                                 if disg__ != 0.0:
#                                     disg_list.append(disg__)
#                                 else:
#                                     disg_list.append(0.0)
#                                 if soc__ != 0.0 or soc__ != 0:
#                                     soc_list.append(soc__)
#                                 if soc__2 != 0.0:
#                                     soc_list_2.append(soc__2)
#                             if soc_list_2 == []:
#                                 soc_avg_2 = 0.0
#                                 chag_all = 0.0
#                                 disg_all = 0.0
#                                 if soc_list==[]:
#                                     soc_avg = 0.0
#                                 else:
#                                     soc_avg = float('%.2f' % (average(soc_list)))
#                             else:
#                                 chag_all = float('%.2f' % (sum(chag_list)))
#                                 disg_all = float('%.2f' % (sum(disg_list)))
#                                 soc_avg_2 = float('%.2f' % (average(soc_list_2)))
#                                 if soc_list == []:
#                                     soc_avg = 0.0
#                                 else:
#                                     soc_avg = float('%.2f' % (average(soc_list)))
#                             try:
#                                 models.FReport.objects.create(day=b[0:10], hour=b[11:13],
#                                                                 chag=str(chag_all),
#                                                                 disg=str(disg_all), soc=str(soc_avg),soc_2=str(soc_avg_2),
#                                                                 station=station_insss.english_name,
#                                                                 app=station_insss.app,
#                                                                 station_type=1, unit_name=0)
#                             except Exception as e:
#                                 if 'Duplicate entry' in e.args:
#                                     pass
#                                 else:
#                                     raise e
#                         else:
#                             pass

def get_request(unit,tt,ty):
    '''获取小时数据'''
    station_english_name = unit.station.english_name
    bms = unit.bms
    meter_type = unit.station.meter_type
    meter_dic = METER_DIC_IDC.get(meter_type)
    charge = meter_dic.get("charge")
    discharge = meter_dic.get("discharge")
    device = meter_dic.get("device")
    bms_ = getattr(unit, device)
    start_time=end_time=tt
    if ty==0:#整点
        time_ = datetime.datetime.strptime(tt, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        start_time_1 = getBeforeHouseStr(time_)  # 获取上一个小时
        start_time_2 = datetime.datetime.strptime(start_time_1, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        start_time_3 = getBeforeHouseStr(start_time_2)
        start_time = start_time_3[:14] + '59:30'  # 获取上上一个小时
        end_time = tt[:14] + '00:30'
    elif ty==1:#前一个小时
        time_ = datetime.datetime.strptime(tt, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        start_time_1 = getBeforeHouseStr(time_)  # 获取上一个小时
        start_time_2 = datetime.datetime.strptime(start_time_1, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        start_time_3 = getBeforeHouseStr(start_time_2)
        start_time = start_time_3[:14] + '59:30'  # 获取上上一个小时
        end_time = tt[:14] + '00:30'
    elif ty == 2:#后一个小时
        time_ = datetime.datetime.strptime(tt, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        start_time = tt[:14] + '59:30'
        end_time_1 = getHHouseStr(time_)  # 获取下一个小时
        send_time_2 = datetime.datetime.strptime(end_time_1, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
        end_time_3 = getHHouseStr(send_time_2)
        end_time = end_time_3[:14] + '00:30'  # 获取下下一个小时

    datas = get_history_data_f(((charge, discharge,),), 'dwd_cumulant_bms_data_storage', bms_, station_english_name,start_time, end_time)
    time.sleep(0.05)
    datas_ = get_history_data_f_soc(('soc',), 'dwd_measure_bms_data_storage_3', bms, station_english_name,start_time, end_time)

    return datas, datas_, charge, discharge

def get_stations(station,now_time):
    """获取冲放电量小时数据"""
    units_inst = models.Unit.objects.filter(is_delete=0, station=station).all()
    detail = []
    time_ = datetime.datetime.strptime(now_time, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
    formatted_time = getBeforeHouseStr(time_)  # 获取上一个小时
    for unit in units_inst:
        meter_type = unit.station.meter_type
        meter_dic = METER_DIC_IDC.get(meter_type)
        device = meter_dic.get("device")
        bms_ = getattr(unit, device)
        new_list_, new_list__, charge, discharge = get_request(unit,now_time,0)
        CuCha_list_0, CuDis_list_0 = [], []
        if new_list_ and new_list__:#整点有值
            tt_0 = new_list_[0]['time'].strftime("%Y-%m-%d %H:%M:%S")
            tt_1 = new_list_[-1]['time'].strftime("%Y-%m-%d %H:%M:%S")
            try:
                tt_t = new_list_[5]['time'].strftime("%Y-%m-%d %H:%M:%S")
            except:
                tt_t = new_list_[0]['time'].strftime("%Y-%m-%d %H:%M:%S")
            charge_0,discharge_0,zt_0 = jisunc_f(charge, discharge, new_list_,CuCha_list_0,CuDis_list_0)
            device_1 = new_list_[0]["device"]
            tt_t_t = datetime.datetime.strptime(tt_t, "%Y-%m-%d %H:%M:%S")  # 转换成时间格式
            tt_0_1=getBeforeHouseStr(tt_t_t)[:14]+'59:30'    #获取上个小时
            tt_0_2 =getHHouseStr(tt_t_t)[:14] +'00:30'        # 获取下个小时
            if tt_0_1<tt_0<(tt_t[:14] +'00:30') and (tt_t[:14] +'59:30')<tt_1<tt_0_2:#双端在整点
                zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
            elif tt_0_1<tt_0<(tt_t[:14] +'00:30') :#开始时间在整点
                charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                CuCha_list_2, CuDis_list_2 = [], []
                new_list_2, new_list__2, charge, discharge = get_request(unit, formatted_time, 2)  # 后一个小时
                charge_2, discharge_2, zt_2 = jisunc_f(charge, discharge, new_list_2, CuCha_list_2, CuDis_list_2)
                if new_list_2:
                    time_2 = new_list_2[0]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    if 00 <= int(time_2[14:16]) <= 15:
                        if zt_2 == 'JiZhi':
                            soc_0 = 0.0
                            if zt_0 == 'CuCha':  # 充电
                                soc_0 = jisuansoc(new_list__)
                            elif zt_0 == 'CuDis':  # 放电
                                soc_0 = jisuansoc(new_list__)
                            elif zt_0 == 'JiZhi':  # 静置
                                soc_0 = 0.0
                            charge_ = charge_0
                            discharge_ = discharge_0
                            soc_ = soc_0
                        else:
                            try:
                                dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                          'new_list_3': new_list__2[0], 'new_list_4': new_list__[0], 'charge': charge,
                                          'discharge': discharge}
                            except:
                                dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                          'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                          'discharge': discharge}

                            dict_b = {'new_list_1': new_list_[-1], 'new_list_2': new_list_[0],
                                      'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}
                            if zt_0 == 'CuCha':  # 充电
                                if zt_2 != 'CuCha':
                                    charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list__)
                                elif zt_2 == 'CuCha':
                                    charge_, discharge_, soc_ = jisuanf_h(dict_b)
                            elif zt_0 == 'CuDis':  # 放电
                                if zt_2 != 'CuDis':
                                    charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list__)
                                elif zt_2 == 'CuDis':
                                    charge_, discharge_, soc_ = jisuanf_h(dict_b)
                            elif zt_0 == 'JiZhi':  # 静置
                                charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                    else:
                        soc_0 = 0.0
                        if zt_0 == 'CuCha':  # 充电
                            soc_0 = jisuansoc(new_list__)
                        elif zt_0 == 'CuDis':  # 放电
                            soc_0 = jisuansoc(new_list__)
                        elif zt_0 == 'JiZhi':  # 静置
                            soc_0 = 0.0
                        charge_ = charge_0
                        discharge_ = discharge_0
                        soc_ = soc_0
                    if charge_ > 135:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                    elif discharge_ > 135:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                    else:
                        append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                else:
                    zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
            elif (tt_t[:14] +'59:30')<tt_1<tt_0_2:  # 结束时间在整点
                CuCha_list_1, CuDis_list_1 = [], []
                new_list_1, new_list__1, charge, discharge = get_request(unit, formatted_time, 1)  # 前一个小时
                charge_1, discharge_1, zt_1 = jisunc_f(charge, discharge, new_list_1, CuCha_list_1, CuDis_list_1)
                if new_list_1:
                    time_1=new_list_1[-1]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    if 45<=int(time_1[14:16])<=59:
                        if charge_0 == 0 and discharge_0 == 0:  # 静置状态
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        elif zt_1=='JiZhi':
                            soc_0 = 0.0
                            if zt_0 == 'CuCha':  # 充电
                                soc_0 = jisuansoc(new_list__)
                            elif zt_0 == 'CuDis':  # 放电
                                soc_0 = jisuansoc(new_list__)
                            elif zt_0 == 'JiZhi':  # 静置
                                soc_0 = 0.0
                            charge_ = charge_0
                            discharge_ = discharge_0
                            soc_ = soc_0
                        else:
                            charge_ = float('%.4f' % (abs(new_list_[-1][charge] - new_list_1[-1][charge])))
                            discharge_ = float('%.4f' % (abs(new_list_[-1][discharge] - new_list_1[-1][discharge])))
                            # soc_ = float('%.4f' % (abs(new_list__[-1]['soc'])- abs(new_list__1[-1]['soc'])))
                            try:
                                soc_ = float('%.4f' % (abs(new_list__[-1]['soc'])- abs(new_list__1[-1]['soc'])))
                            except:
                                soc_ = float('%.4f' % (jisuansoc(new_list__)))

                    else:
                        soc_0 = 0.0
                        if zt_0 == 'CuCha':  # 充电
                            soc_0 = jisuansoc(new_list__)
                        elif zt_0 == 'CuDis':  # 放电
                            soc_0 = jisuansoc(new_list__)
                        elif zt_0 == 'JiZhi':  # 静置
                            soc_0 = 0.0
                        charge_=charge_0
                        discharge_=discharge_0
                        soc_=soc_0
                    if charge_>135:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                    elif discharge_>135:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                    else:
                        append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                else:
                    zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
            else:
                CuCha_list_1,CuDis_list_1,CuCha_list_2,CuDis_list_2=[],[],[],[]
                new_list_1, new_list__1, charge, discharge = get_request(unit, formatted_time,1)  # 前一个小时
                charge_1, discharge_1,zt_1= jisunc_f(charge, discharge, new_list_1,CuCha_list_1,CuDis_list_1)
                new_list_2, new_list__2, charge, discharge = get_request(unit, formatted_time,2)  # 后一个小时
                charge_2, discharge_2, zt_2 = jisunc_f(charge, discharge, new_list_2, CuCha_list_2, CuDis_list_2)
                charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                if new_list_1 and new_list_2:
                    time_1 = new_list_1[-1]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    time_2 = new_list_2[0]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    if 45 <= int(time_1[14:16]) <= 59 and 00 <= int(time_2[14:16]) <= 15:
                        try:
                            dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_1[-1],
                                      'new_list_3': new_list__2[0], 'new_list_4': new_list__1[-1], 'charge': charge,
                                      'discharge': discharge}
                        except:
                            try:
                                dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_1[-1],
                                          'new_list_3': new_list__2[0], 'new_list_4': new_list__[0], 'charge': charge,
                                          'discharge': discharge}
                            except:
                                dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_1[-1],
                                          'new_list_3': new_list__[-1], 'new_list_4': new_list__1[-1], 'charge': charge,
                                          'discharge': discharge}
                        try:
                            dict_b = {'new_list_1': new_list_[-1], 'new_list_2': new_list_1[-1],
                                      'new_list_3': new_list__[-1], 'new_list_4': new_list__1[-1], 'charge': charge,
                                      'discharge': discharge}
                        except:
                            dict_b = {'new_list_1': new_list_[-1], 'new_list_2': new_list_1[-1],
                                      'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}
                        if zt_0 == 'CuCha':  # 充电
                            if zt_1 != 'CuCha' and zt_2 != 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_1 == 'CuCha' and zt_2 != 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_1 != 'CuCha' and zt_2 == 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b,new_list_)
                            elif zt_1 == 'CuCha' and zt_2 == 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b,new_list_)
                        elif zt_0 == 'CuDis':  # 放电
                            if zt_1 != 'CuDis' and zt_2 != 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_1 == 'CuDis' and zt_2 != 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_1 != 'CuDis' and zt_2 == 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b,new_list_)
                            elif zt_1 == 'CuDis' and zt_2 == 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b,new_list_)
                        elif zt_0 == 'JiZhi':  # 静置
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        if charge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        elif discharge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        else:
                            append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                    elif 45 <= int(time_1[14:16]) <= 59:
                        if charge_0 == 0 and discharge_0 == 0:  # 静置状态
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        else:
                            charge_ = float('%.4f' % (abs(new_list_[-1][charge] - new_list_1[-1][charge])))
                            discharge_ = float('%.4f' % (abs(new_list_[-1][discharge] - new_list_1[-1][discharge])))
                            try:
                                soc_ = float('%.4f' % (abs(new_list__[-1]['soc']) - abs(new_list__1[-1]['soc'])))
                            except:
                                soc_ = float('%.4f' % (jisuansoc(new_list__)))

                        if charge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        elif discharge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        else:
                            append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                    elif 00 <= int(time_2[14:16]) <= 15:
                        try:
                            dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                      'new_list_3': new_list__2[0], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}
                        except:
                            dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                      'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}

                        dict_b = {'new_list_1': new_list_[-1], 'new_list_2': new_list_[0],
                                  'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                  'discharge': discharge}
                        if zt_0 == 'CuCha':  # 充电
                            if zt_2 != 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list__)
                            elif zt_2 == 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b)
                        elif zt_0 == 'CuDis':  # 放电
                            if zt_2 != 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list__)
                            elif zt_2 == 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b)
                        elif zt_0 == 'JiZhi':  # 静置
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        if charge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        elif discharge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        else:
                            append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                    else:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                elif new_list_1:
                    time_1 = new_list_1[-1]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    if 45 <= int(time_1[14:16]) <= 59:
                        if charge_0 == 0 and discharge_0 == 0:  # 静置状态
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        else:
                            charge_ = float('%.4f' % (abs(new_list_[-1][charge] - new_list_1[-1][charge])))
                            discharge_ = float('%.4f' % (abs(new_list_[-1][discharge] - new_list_1[-1][discharge])))
                            try:
                                soc_ = float('%.4f' % (abs(new_list__[-1]['soc'])- abs(new_list__1[-1]['soc'])))
                            except:
                                soc_ = float('%.4f' % (jisuansoc(new_list__)))
                        if charge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        elif discharge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        else:
                            append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                    else:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                elif new_list_2:
                    time_2 = new_list_2[0]['time'].strftime("%Y-%m-%d %H:%M:%S")
                    if 00 <= int(time_2[14:16]) <= 15:
                        try:
                            dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                      'new_list_3': new_list__2[0], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}
                        except:
                            dict_a = {'new_list_1': new_list_2[0], 'new_list_2': new_list_[0],
                                      'new_list_3': new_list__[-1], 'new_list_4': new_list__[0], 'charge': charge,
                                      'discharge': discharge}

                        dict_b = {'new_list_1': new_list_[-1], 'new_list_2': new_list_[0],
                                  'new_list_3': new_list__[-1], 'new_list_4':  new_list__[0], 'charge': charge,
                                  'discharge': discharge}
                        if zt_0 == 'CuCha':  # 充电
                            if zt_2 != 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_2 == 'CuCha':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b)
                        elif zt_0 == 'CuDis':  # 放电
                            if zt_2 != 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_a,new_list_)
                            elif zt_2 == 'CuDis':
                                charge_, discharge_, soc_ = jisuanf_h(dict_b)
                        elif zt_0 == 'JiZhi':  # 静置
                            charge_, discharge_, soc_ = 0.0, 0.0, 0.0
                        if charge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        elif discharge_ > 135:
                            zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                        else:
                            append_list(charge_, detail, device_1, discharge_, formatted_time, soc_)
                    else:
                        zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
                else:#都不存在（取整点的值）
                    zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0)
        else:#整点没有值
            time_ = time.strptime(now_time, "%Y-%m-%d %H:%M:%S")
            timeStamp = int(time.mktime(time_))
            tt=timeStamp-3600
            b = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(tt)))
            append_list(0.0, detail, bms_, 0.0, b, 0.0)
    return detail


def zdzhi(charge_0, detail, device_1, discharge_0, formatted_time, new_list__, zt_0):
    '''保存整点值'''
    soc_0 = 0.0
    if zt_0 == 'CuCha':  # 充电
        soc_0 = jisuansoc(new_list__)
    elif zt_0 == 'CuDis':  # 放电
        soc_0 = jisuansoc(new_list__)
    elif zt_0 == 'JiZhi':  # 静置
        soc_0 = 0.0
    append_list(charge_0, detail, device_1, discharge_0, formatted_time, soc_0)


def append_list(charge_0, detail, device_1, discharge_0, formatted_time, soc_0):
    '''添加到列表中'''
    detail.append({"charge": charge_0,
                   "discharge": discharge_0,
                   "soc": soc_0,
                   "time": formatted_time,
                   "device": device_1})

def jisuansoc(new_list__):
    '''计算soc'''
    try:
        first_soc = float(abs(decimal.Decimal(new_list__[0]["soc"])))
    except Exception as e:
        success_log.info("soc: soc不存在数据 soc默认取 0")
        first_soc = 0.0
    try:
        last_soc = float(abs(decimal.Decimal(new_list__[-1]["soc"])))
    except Exception as e:
        success_log.info("soc: soc不存在数据 soc默认取 0")
        last_soc = 0.0
    # soc_0 = float('%.2f' %(abs(last_soc) - abs(first_soc)))
    soc_0 = float('%.2f' %(last_soc - first_soc))
    return soc_0

def jisunc_f(charge, discharge, new_list_,CuCha_list_0,CuDis_list_0):
    '''计算小时的充放电量'''
    zt='0'
    try:
        first_CuCha = float(abs(decimal.Decimal(new_list_[0][charge])))
    except Exception as e:
        success_log.info("逐时冲放电量: 该天first_CuCha不存在数据 first_CuCha默认取 0")
        first_CuCha = 0.0
    try:
        first_CuDis = float(abs(decimal.Decimal(new_list_[0][discharge])))
    except Exception as e:
        success_log.info("逐时冲放电量: 该天first_CuDis不存在数据 first_CuDis默认取 0")
        first_CuDis = 0.0
    try:
        last_CuCha = float(abs(decimal.Decimal(new_list_[-1][charge])))
    except Exception as e:
        success_log.info("逐时冲放电量: 该天last_CuCha不存在数据 first_CuCha默认取 0")
        last_CuCha = 0.0
    try:
        last_CuDis = float(abs(decimal.Decimal(new_list_[-1][discharge])))
    except Exception as e:
        success_log.info("逐时冲放电量: 该天last_CuDis不存在数据 first_CuDis默认取 0")
        last_CuDis = 0.0
    charge_ = float('%.2f' % (last_CuCha - first_CuCha))
    discharge_ = float('%.2f' % (last_CuDis- first_CuDis))
    if charge_>0 and discharge_==0:
        if charge_>135:
            charge_, discharge_, zt = cucha_wq(CuCha_list_0, charge, new_list_)
        else:
            zt='CuCha'
    elif charge_ > 0 and discharge_ > 0:#取大的
        if charge_>discharge_:
            if charge_ > 135:
                charge_, discharge_, zt = cucha_wq(CuCha_list_0, charge, new_list_)
            else:
                zt = 'CuCha'
                discharge_=0.0
        else:
            if discharge_ > 135:
                charge_, discharge_, zt = dischart_wq(CuDis_list_0, discharge, new_list_)
            else:
                zt = 'CuDis'
                charge_=0.0
    elif charge_ > 0 and discharge_ < 0:#取大的
        if charge_>135:
            charge_, discharge_, zt = cucha_wq(CuCha_list_0, charge,  new_list_)
        else:
            zt = 'CuCha'
            discharge_=0.0

    elif charge_ == 0 and discharge_ > 0:
        if discharge_ > 135:
            charge_, discharge_, zt = dischart_wq(CuDis_list_0,discharge, new_list_)
        else:
            zt = 'CuDis'

    elif charge_ == 0 and discharge_ < 0:
        CuDis_list = []
        for ll in new_list_:
            if ll[discharge]!=None:
                CuDis_list.append(ll[discharge])
        CuDis_data = np.diff(CuDis_list).tolist()  # 后一个值减去前一个值得差集合
        for dd in CuDis_data:
            if 0 < dd < 135:
                CuDis_list_0.append(dd)
        discharge_ = float(np.sum(CuDis_list_0))
        if discharge_>0:
            zt = 'CuDis'
            charge_ = 0.0
        else:
            zt = 'JiZhi'
            discharge_=0.0

    elif charge_ < 0 and discharge_ > 0:#取大的
        if discharge_ > 135:
            charge_, discharge_, zt = dischart_wq(CuDis_list_0,  discharge, new_list_)
        else:
            zt = 'CuDis'
            charge_=0.0

    elif charge_ < 0 and discharge_ < 0:#往前相减去了异常值
        CuCha_list, CuDis_list = [], []
        for ll in new_list_:
            if ll[charge]!=None:
                CuCha_list.append(ll[charge])
            if ll[discharge] != None:
                CuDis_list.append(ll[discharge])
        CuCha_data = np.diff(CuCha_list).tolist()  # 后一个值减去前一个值得差集合
        CuDis_data = np.diff(CuDis_list).tolist()  # 后一个值减去前一个值得差集合
        for cc in CuCha_data:
            if 0 < cc < 135:
                CuCha_list_0.append(cc)
        for dd in CuDis_data:
            if 0 < dd < 135:
                CuDis_list_0.append(dd)
        charge_, discharge_ = float(np.sum(CuCha_list_0)), float(np.sum(CuDis_list_0))
        if charge_>discharge_:
            zt = 'CuCha'
            discharge_=0.0
        else:
            zt = 'CuDis'
            charge_=0.0
    elif charge_ < 0 and discharge_ == 0:
        CuCha_list = []
        for ll in new_list_:
            CuCha_list.append(ll[charge])
        CuCha_data = np.diff(CuCha_list).tolist()  # 后一个值减去前一个值得差集合
        for cc in CuCha_data:
            if 0 < cc < 135:
                CuCha_list_0.append(cc)
        charge_ = float(np.sum(CuCha_list_0))
        if charge_ > 0:
            zt = 'CuCha'
            discharge_ = 0.0
        else:
            zt = 'JiZhi'
            charge_ = 0.0
    elif charge_ == 0 and discharge_ == 0:
        zt = 'JiZhi'
    return charge_,discharge_,zt


def dischart_wq(CuDis_list_0, discharge, new_list_):
    '''放电往期取值'''
    CuDis_list = []
    for ll in new_list_:
        if ll[discharge] != None:
            CuDis_list.append(ll[discharge])
    CuDis_data = np.diff(CuDis_list).tolist()  # 后一个值减去前一个值得差集合
    for dd in CuDis_data:
        if 0<dd < 135:
            CuDis_list_0.append(dd)
    discharge_ = float(np.sum(CuDis_list_0))
    if discharge_ > 0:
        zt = 'CuDis'
        charge_ = 0.0
    else:
        zt = 'JiZhi'
        charge_ = 0.0
    return charge_, discharge_, zt


def cucha_wq(CuCha_list_0, charge,  new_list_):
    '''充电往前取值'''
    CuCha_list = []
    for ll in new_list_:
        if ll[charge]!=None:
            CuCha_list.append(ll[charge])
    CuCha_data = np.diff(CuCha_list).tolist()  # 后一个值减去前一个值得差集合
    for cc in CuCha_data:
        if 0<cc < 135:
            CuCha_list_0.append(cc)
    charge_ = float(np.sum(CuCha_list_0))
    if charge_ > 0:
        zt = 'CuCha'
        discharge_ = 0.0
    else:
        zt = 'JiZhi'
        discharge_ = 0.0
    return charge_, discharge_, zt


def jisuanf_h(dict_,new_list__=None):
    '''计算返回的值'''
    charge_ = float('%.4f' % (abs(dict_['new_list_1'][dict_['charge']] - dict_['new_list_2'][dict_['charge']])))
    discharge_ = float('%.4f' % (abs(dict_['new_list_1'][dict_['discharge']] - dict_['new_list_2'][dict_['discharge']])))

    try:
        soc_ = float('%.4f' % (abs(dict_['new_list_3']['soc']) - abs(dict_['new_list_4']['soc'])))
    except:
        soc_ = float('%.4f' % (jisuansoc(new_list__)))

    return charge_,discharge_,soc_

def get_history_data_f(value_n, table_name, de, station_name, start_time, end_time):
    '''查询小时历史数据充放电量'''
    conn = pool.connection()
    cursor = conn.cursor()
    try:
        # 执行SQL查询
        sql = """SELECT {},{},time,device
                        FROM {} 
                        WHERE 1=1
                        and (type=1 or type is NULL)
                        and device='{}'
                        and station_name='{}'
                        and time BETWEEN '{}' AND '{}'
                        and {} IS NOT NULL
                        and {} IS NOT NULL
                        ORDER BY time ASC
                        """.format(
            value_n[0][0], value_n[0][1], table_name, de, station_name, start_time, end_time,value_n[0][0], value_n[0][1])
        error_log.error(sql)
        try:
            cursor.execute(sql)
        except Exception as e:
            error_log.error(e)
        # 获取查询结果
        result = cursor.fetchall()
        if not result:
            return []
        return result
    except Exception as e:
        error_log.error(e)
    finally:
        cursor.close()

def get_history_data_f_soc(value_n, table_name, de, station_name, start_time, end_time):
        '''查询小时历史soc变化数据'''
        conn = pool.connection()
        cursor = conn.cursor()
        try:
            # 执行SQL查询
            sql = """SELECT {},time,device
                            FROM {}
                            WHERE 1=1
                            and (type=1 or type is NULL)
                            and device='{}'                          
                            and station_name='{}'
                            and time BETWEEN '{}' AND '{}'
                            and {} IS NOT NULL
                            ORDER BY time ASC
                            """.format(
                value_n[0], table_name, de, station_name, start_time, end_time,value_n[0])
            error_log.error(sql)
            try:
                cursor.execute(sql)
            except Exception as e:
                error_log.error(e)
            # 获取查询结果
            result = cursor.fetchall()
            if not result:
                return []
            return result
        except Exception as e:
            error_log.error(e)
        finally:
            cursor.close()
def dateToDataList(start,end):
    # 计算时间段内的时间列表,包含首位
    datestart=datetime.datetime.strptime(start[0:10],'%Y-%m-%d')
    dateend=datetime.datetime.strptime(end[0:10],'%Y-%m-%d')
    data_list = list()
    while datestart<=dateend:
        data_list.append(datestart.strftime('%Y-%m-%d'))
        datestart+=datetime.timedelta(days=1)
    return data_list

def last_day_of_month(year,month):
    """
    获取某个月的最后一天
    """
    any_day = datetime.date(year, month, 1)
    next_month = any_day.replace(day=28) + datetime.timedelta(days=4)  # this will never fail
    return next_month - datetime.timedelta(days=next_month.day)

def getNewTimeStr():
    u'获取当前时间'
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

def timeStrToTamp(data):
    u'时间转时间戳'
    timeArray = time.strptime(data, "%Y-%m-%d %H:%M:%S")
    timeStamp = int(time.mktime(timeArray))
    return timeStamp

def getBeforeHouseStr(data):
    # 获取上一小时 时间格式
    # now_time=datetime.datetime.now()
    l= (data+datetime.timedelta(hours=-1)).strftime("%Y-%m-%d %H:%M:%S")
    return l
def getBeforeHouseStr2(data):
    # 获取上一小时 时间格式
    # now_time=datetime.datetime.now()
    l= (data+datetime.timedelta(hours=-2)).strftime("%Y-%m-%d %H:%M:%S")
    return l

def getHHouseStr(data):
    # 获取下一小时 时间格式
    # now_time=datetime.datetime.now()
    l= (data+datetime.timedelta(hours=+1)).strftime("%Y-%m-%d %H:%M:%S")
    return l

if __name__ == '__main__':
    electricity_h_Count()
