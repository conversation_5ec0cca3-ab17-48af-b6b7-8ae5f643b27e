import json
import os

from django.shortcuts import render
from rest_framework.response import Response
from rest_framework.views import APIView

from LocaleTool.translate_exist_tables import translate_exist_tables, api_translate_exist_tables
from TianLuAppBackend.settings import BASE_DIR
from apis.user import models
from common import common_response_code
from tools.gen_excel import create_excel
from LocaleTool.common import redis_pool


def index(request):
    qq_group_num = request.GET.get('num')
    return render(request, 'index.html', {"qq_group_num": qq_group_num})


class MeterUseTimeView(APIView):
    """
    【废弃】写入所有主站已有配置过使用结算电表的默认使用时间范围
    """""

    def get(self, request):
        master_stations = models.MaterStation.objects.filter(is_delete=0).all()

        for master_station in master_stations:
            if master_station.is_account == 1:
                models.MeterUseTime.objects.create(station=master_station, start_time='2024-05-01 00:00:00', user_id=75,
                                                   end_time='2099-12-31 23:59:59')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success"
                }
            }
        )


class StationMeterUseTimeView(APIView):
    """
    写入所有从站的是否使用结算电表配置和使用结算电表的默认使用时间范围: 只部署时执行一次
    """""

    def get(self, request):
        stations = models.StationDetails.objects.filter(is_delete=0).all()

        for station in stations:
            if station.master_station.is_account == 1:
                station.is_account = 1
                station.meter_number = station.master_station.meter_number
                station.save()

                m_u_t = models.MeterUseTime.objects.filter(station=station.master_station, is_use=1).first()
                if m_u_t:
                    start_time = m_u_t.start_time
                else:
                    start_time = '2024-05-01 00:00:00'

                models.StationMeterUseTime.objects.create(station=station, start_time=start_time, user_id=75)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success"
                }
            }
        )


class InitAllMasterStationModeView(APIView):
    """
    初始化所有主站的mode字段的值
    """""

    def get(self, request):
        master_stations = models.MaterStation.objects.filter(is_delete=0).all()

        for master_station in master_stations:

            stations = master_station.stationdetails_set.filter(is_delete=0).all()
            # 常规模式
            if len(stations) == 1:
                master_station.mode = 1

            else:
                ems_station = stations.filter(english_name=master_station.english_name).first()

                # 标准主从模式
                if ems_station.slave == 0:
                    master_station.mode = 2

                # 级联主从模式
                elif ems_station.slave == 1:
                    master_station.mode = 3

                # 逻辑主从模式
                elif ems_station.pack != -1:
                    master_station.mode = 4

                # 其他：模式为常规模式
                else:
                    master_station.mode = 1

            master_station.save()

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "message": "success"
            }
        )


class PublishTranslateView(APIView):

    def get(self, request):
        lang = request.headers.get('lang', 'zh')
        # 发布翻译数据
        pdr_data = {'id': 1,
                    'table': 't_demo',
                    'update_data': {'name': '测试'}}
        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))
        print('发布完成')
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "message": "发布成功"
            }
        )


class ExportUnitsView(APIView):

    def get(self, request):
        stations = models.StationDetails.objects.filter(is_delete=0).all()

        unit_list = []
        no = 1
        for station in stations:
            ini_num = station.ini_num.split(';') if station.ini_num else []
            units = models.Unit.objects.filter(is_delete=0, station=station).all()

            for i, unit in enumerate(units):
                try:
                    english_name = ini_num[i]
                except IndexError as e:
                    english_name = ''
                unit_dict = {
                    "no": no,
                    'name': unit.unit_new_name,
                    'english_name': english_name,
                    'versions': str(unit.v_number) + '.0' if unit.v_number else '',
                    'station': station.master_station.name,
                    'project': station.project.name
                }
                unit_list.append([unit_dict['no'], unit_dict['name'], unit_dict['english_name'], unit_dict['versions'], unit_dict['station'], unit_dict['project']])
                no += 1

        file_name = "tianlu_units.xlsx"
        file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)

        sheet1 = f'units'

        sheet1_headers = ['编号', '储能单元', '设备出厂编号', '设备版本号', '并网点', '项目']

        create_excel(file_path, sheet_name=sheet1, headers=sheet1_headers, data=unit_list)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
            },
        })


class TranslateView(APIView):

    def get(self, request):
        try:
            api_translate_exist_tables()
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                },
            })
        except Exception as e:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {
                    "message": "error",
                },
            })