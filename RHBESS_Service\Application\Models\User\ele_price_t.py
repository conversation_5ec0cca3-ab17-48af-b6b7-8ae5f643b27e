#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class ElePrice(user_Base):
    u'电价配置表'
    __tablename__ = "t_ele_price"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=True, comment=u"电价名称")
    descr = Column(VARCHAR(256), nullable=True,comment=u"电价类型")
    start_time = Column(VARCHAR(50), nullable=True,comment=u"开始时间")
    end_time = Column(VARCHAR(50), nullable=True,comment=u"截止时间")
    rate = Column(Float, nullable=True,comment=u"费率,单位元/kWh")
    month = Column(VARCHAR(50), nullable=True,comment=u"前端传输月份")
    months = Column(VARCHAR(50), nullable=True,comment=u"月份")
    station = Column(VARCHAR(50), nullable=True, comment=u"所属站")
    op_ts = Column(DateTime, nullable=True,comment=u"录入时间")
    is_use = Column(CHAR(2), nullable=True,server_default='1',comment=u"是否使用1是0否")
    years = Column(Integer, nullable=True, comment=u"年份")

    en_name = Column(VARCHAR(256), nullable=True, comment=u"电价名称")
    en_descr = Column(VARCHAR(256), nullable=True, comment=u"电价类型")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        bean = "{'id':%s,'name':'%s','descr':'%s','start_time':'%s','end_time':'%s','rate':%s,'month':'%s','months':'%s','station':'%s','op_ts':'%s','is_use':%s,'years':%s,'en_name':%s,'en_descr':%s}" % (
            self.id,self.name,self.descr,self.start_time,self.end_time,self.rate,self.month,self.months,self.station,self.op_ts,self.is_use,self.years,self.en_name,self.en_descr)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}