#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-06-16 18:17:00
#@FilePath     : \RHBESS_Service\Tools\Mqtt\meter_mqtt_client.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-05 13:56:01


'''
实时数据中心app运行情况
'''
import sys,json
import os,time
import logging
import paho.mqtt.client as mqtt
# from Application.Cfg.dir_cfg import model_config
from Application.Models.User.event import Event
# from Tools.DB.mongodb_con import  dongmu_mongodb_client,dongmu_mongo_db
from Application.Models.User.meter_data_test import MeterDataTest
from Application.Models.SelfStationPoint.t_device import DevicePT
from Application.Models.SelfStationPoint.t_status import StatusPT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT
from Application.Models.User.alarm_r import AlarmR
from Tools.DB.redis_con import r_real
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.time_utils import timeUtils
from Tools.Cfg.get_cnf import work_dir
path = os.path.join(work_dir, 'log/metermqtt.log')
# reload(sys)
# sys.setdefaultencoding("utf-8")
data_arr = []
json_data = {}
def on_connect(client, userdata, flags, rc):
    '''连接成功回调'''
    if rc == 0:  # 连接成功
        logging.info('start success')
        print('Connection Succeed!')
    else:
        print('Connected with result code '+str(rc))
    # 订阅主题
    client.subscribe("/usr/plcnet/test_device3836/edge/u",0)  # 桐乡中鑫富能变压器1  一个串口
    client.subscribe("/usr/plcnet/test_device3736/edge/u",0)  # 桐乡中鑫富能变压器2 两个串口
    client.subscribe("/usr/plcnet/test_device0714/edge/u",0)  # 宁波朗盛1  一个串口
    # client.subscribe("/usr/plcnet/test_device2980/edge/u",0)  # 浙江飞剑工贸配电室1  两个串口

    client.subscribe("/usr/plcnet/test_device2793/edge/u",0)  # 西郊甲变压器 A1057 一个串口
    client.subscribe("/usr/plcnet/test_device3465/edge/u",0)  # 西郊乙变压器 B1057 一个串口
    client.subscribe("/usr/plcnet/test_device5093/edge/u",0)  # 西郊一期1号网关0726 一个串口

    client.subscribe("/usr/plcnet/test_device5504/edge/u",0)  # 远航锦锂 1 号配电室 2#变压器

    client.subscribe("/usr/plcnet/test_device5783/edge/u",0)  # 3＃配电房  甲线
    client.subscribe("/usr/plcnet/test_device3316/edge/u",0)  # 3＃配电房  乙线
    client.subscribe("/usr/plcnet/test_device1661/edge/u",0)  # 测试设备1（XG-1661）AL
    
def on_message(client, userdata, msg):
    '''消息接收回调'''
    print ('start----',time.time())
    topic = msg.topic  # 订阅主题
    data = msg.payload  # 返回值
    msg_t = 'topic:%s'%topic
    msg_p = 'payload:%s'%data
    print (msg_t,'--------',msg_p)
    json_data = json.loads(data)
    datas = json_data['d']  # 上报的数据类型
    print ('json_data:',json_data)
    # print ('*******',topic[12:26])
    device = topic[12:27]  # 截取的设备
    ia,ib,ic,i2a,i2b,i2c = 0,0,0,0,0,0
    pa,pb,pc,p2a,p2b,p2c = 0,0,0,0,0,0
    ntime = 0
    for d in datas:
        if d['pid'] == 'Ia':
            ia = int(d['v'])
        elif d['pid'] == 'Ib':
            ib = int(d['v'])
        elif d['pid'] == 'Ic':
            ic = int(d['v'])
        elif d['pid'] == 'I2a':
            i2a = int(d['v'])
        elif d['pid'] == 'I2b':
            i2b = int(d['v'])
        elif d['pid'] == 'I2c':
            i2c = int(d['v'])
        ntime = int(d['s'])
    
    # if device == 'test_device2980':
    #     pa = ia/2000*90*10/1.732
    #     pb = ib/2000*90*10/1.732
    #     pc = ic/2000*90*10/1.732
    #     p2a = i2a/2000*95*10/1.732
    #     p2b = i2b/2000*95*10/1.732
    #     p2c = i2c/2000*95*10/1.732
    if device == 'test_device2793' :  # 变比
        pa = ia/2000*1000*10/1.732
        pb = ib/2000*1000*10/1.732
        pc = ic/2000*1000*10/1.732
    if device == 'test_device3465' :  # 变比
        pa = ia/2000*60*10/1.732
        pb = ib/2000*60*10/1.732
        pc = ic/2000*60*10/1.732

    if device == 'test_device3736' :
        pa = ia/2000*160*220
        pb = ib/2000*160*220
        pc = ic/2000*160*220
        p2a = i2a/2000*160*220
        p2b = i2b/2000*160*220
        p2c = i2c/2000*160*220
    if device == 'test_device0714' :  # 变比
        pa = ia/2000*120*0.38/1.732
        pb = ib/2000*120*0.38/1.732
        pc = ic/2000*120*0.38/1.732
    if device == 'test_device3836' :  # 变比20
        pa = ia/2000*240*220
        pb = ib/2000*240*220
        pc = ic/2000*240*220

    if device == 'test_device5504' :  # 变比20
        pa = 220*ia/2000*800
        pb = 220*ib/2000*800
        pc = 220*ic/2000*800

    if device == 'test_device5783':  # 变比20
        pa = 220*ia/2000*800
        pb = 220*ib/2000*800
        pc = 220*ic/2000*800
    if device == 'test_device3316':  # 变比20
        pa = 220*ia/2000*800
        pb = 220*ib/2000*800
        pc = 220*ic/2000*800
    

    # print (ia,ib,ic,i2a,i2b,i2c,ntime)

    meter = MeterDataTest(ntime=ntime,ia=ia,ib=ib,ic=ic,i2a=i2a,i2b=i2b,i2c=i2c,pa=pa,pb=pb,pc=pc,p2a=p2a,p2b=p2b,p2c=p2c,op_ts=timeUtils.getNewTimeStr(),device=device)
    user_session.add(meter)
    user_session.commit()
   
    # logging.info(msg_t)
    print ('end----',time.time())
    user_session.close()
  
    # logging.info(msg_p)
 

if __name__ == "__main__":
    
    ip = "************"
    port = 1883
    keepalive = 60
    username = "rhbess1"
    password = "rhbess123"

    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message

    client.username_pw_set(username, password=password)  # 用户名和密码
    client.connect(ip, port, keepalive)
    client.loop_forever()  # 守护连接状态
    
    
    
    
    

        



