#!/usr/bin/env python
# coding=utf-8
# @Information:

from Tools.Cfg.DB_his import get_dhis
import tornado.web
from Application.Models.His.r_ACDMS import HisACDMS, HisSDA
from Tools.DB.datong_his import sdatong1_session, sdatong3_session, sdatong1_engine, sdatong4_session, sdatong2_session, \
    sdatong2_engine, sdatong4_engine, sdatong3_engine
from Tools.DB.guizhou_his import sguizhou1_engine, sguizhou3_engine, sguizhou5_engine, sguizhou7_engine, sguizhou1_session, sguizhou3_session, \
    sguizhou5_session, sguizhou7_session, sguizhou8_session, sguizhou2_engine, sguizhou4_engine, sguizhou2_session, \
    sguizhou4_session, sguizhou6_session, sguizhou6_engine, sguizhou8_engine
from Tools.DB.mysql_scada import sessionmaker, scada_engine, scada_session, DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.PointTable.t_device import DevicePT
from Tools.DB.ygqn_his import sygqn7_session, sygqn8_session, sygqn7_engine, sygqn8_engine
from Tools.DB.shgyu_his import sshgyu_session, sshgyu_engine
from Tools.Utils.num_utils import *
import uuid
from Application.Models.SelfStationPoint.t_status import StatusPT
from Application.Models.SelfStationPoint.t_device import DevicePT as SELYDevicePT
from Tools.DB.mysql_scada import mqtt_session
from Application.Models.His.r_ACDMS_1 import HisACDMS_1
from Tools.DB.mysql_user import user_session
from Application.Models.User.point_type import PointType
from Application.Models.User.data_item import DataItemV2
from Application.Models.User.point_table import PointTable
from Tools.DB.houma_his import shoumaa1_session, shoumab1_session, shoumab2_session, \
    shoumaa2_session, shoumaa1_engine, shoumaa2_engine, shoumab2_engine, \
    shoumab1_engine
import sys
sys.setrecursionlimit(10000)

db_peiz={"houma":[[shoumaa1_session,shoumaa2_session,shoumab1_session,shoumab2_session],[shoumaa1_engine,shoumaa2_engine,shoumab1_engine,shoumab2_engine]],
          "datong":[[sdatong1_session,sdatong2_session,sdatong3_session,sdatong4_session],[sdatong1_engine,sdatong2_engine,sdatong3_engine,sdatong4_engine]],
         "guizhou":[[sguizhou1_session,sguizhou2_session,sguizhou3_session,sguizhou4_session,sguizhou5_session,sguizhou6_session,sguizhou7_session,sguizhou8_session],
                    [sguizhou1_engine,sguizhou2_engine,sguizhou3_engine,sguizhou4_engine,sguizhou5_engine,sguizhou6_engine,sguizhou7_engine,sguizhou8_engine]],
         "ygqn": [[sygqn7_session, sygqn8_session],[sygqn7_engine, sygqn8_engine]],
         "shgyu":[[sshgyu_session],[sshgyu_engine]]}

data_obj = {"status": "t_status", "measure": 't_measure',
            "cumulant": 't_cumulant', "discrete": 't_discrete',
            "parameter": "t_parameter", "remoteCtl": "t_remote_ctl",
            "device": "t_device"}

db_=get_dhis('equipment_tree')
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']

class EquipmentTreeIntetface(BaseHandler):
    '''
    获取设备层结构
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            if kt == 'AllDevice':
                data = self.getAllDevices()
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceByName':#变量名
                type_ = self.get_argument('type', None)  # 类型
                name = self.get_argument('name', None)  # 名称
                pageNum = int(self.get_argument('pageNum', 1))  # 页码
                pageSize = int(self.get_argument('pageSize', 20))  # 每页数据量
                db = self.get_argument('db', 'his')
                if DEBUG:
                    logging.info('type:%s,name:%s' % (type_, name))
                if not type_ or not name or type_ not in data_obj.keys():
                    if lang=='en':
                        return self.customError('Parameter invalid')
                    else:
                        return self.customError('参数非法')
                if db == 'dongmu':
                    data, result_c = self.getDeviceByName_1(type_, name, pageNum, pageSize)
                    if lang == 'en':
                        return self.returnTotalSuc(data, result_c,lang='en')
                    else:
                        return self.returnTotalSuc(data, result_c)
                elif db not in exclude_station:
                    data = []
                    if type_ == 'status':
                        data_type = 2
                    elif type_ == 'measure':
                        data_type =1
                    else:
                        data_type = 0
                    point_type = user_session.query(PointTable).filter(PointTable.name_la == name, PointTable.data_type == data_type, PointTable.station == db).first()
                    if point_type:
                        point_type_id = user_session.query(PointType).filter(PointType.en_equipment_name == point_type.device_name, PointType.station == db).first()
                        if point_type_id:
                            data_item = user_session.query(DataItemV2).filter(DataItemV2.point_type_id == point_type_id.id).all()
                            for i in data_item:
                                data.append(
                                    {
                                        'descr': f'{i.descr} {point_type.name}',
                                        'name': f'{i.name}.{name}',
                                        'nameT': True
                                    }
                                )

                    return self.returnTotalSuc(data, len(data), lang='en')
                else:
                    data, result_c = self.getDeviceByName(type_, name, pageNum, pageSize)
                    if lang == 'en':
                        return self.returnTotalSuc(data, result_c[0][0],lang='en')
                    else:
                        return self.returnTotalSuc(data, result_c[0][0])
            elif kt == 'GetTreeNames':
                db = self.get_argument('db', 'his')
                if DEBUG:
                    logging.info('db:%s' % db)
                if db not in db_.keys():
                    return self.customError("无效入参")
                if db == 'his':
                    name = None
                elif 'ygzhen' == db or 'zgtian' == db or 'houma' == db:  # 调峰站
                    name = 'tfSt' + db
                elif 'datong' == db :
                    name = 'tc_' + db
                elif 'baodian' == db:
                    name = 'tfStbodian'
                elif 'guizhou' == db or 'ygqn' == db or 'shgyu' == db:
                    name = db
                else:
                    if db not in exclude_station:
                        name = db
                    else:
                        name = 'tpSt' + db
                datas = []
                if db == "dongmu":
                    name = 'tfStdongmu'
                    if lang=='en':
                        obj = {'title': 'Peaking project power station Dongmu', 'value': 'Peaking project power station Dongmu', 'key': str(uuid.uuid1())}
                    else:
                        obj = {'title': '调峰项目电站东睦', 'value': '调峰项目电站东睦', 'key': str(uuid.uuid1())}
                    obj['children'] = getNames_1(name)
                    datas.append(obj)
                else:
                    data = self.getAllDevices(name)
                    for d in data:
                        obj = {'title': d['descr'], 'value': d['name'], 'key': str(uuid.uuid1())}
                        if 'houma' in d['name']:
                            len_name=len(d['name'].split('.'))
                            if len_name<4:
                                obj['children'] = getNames(d['name'])
                        elif db not in exclude_station:
                            table_res = user_session.query(PointTable).filter(PointTable.device_name == d['name'], PointTable.station == db).all()
                            children = []
                            for table in table_res:
                                t_obj = {'title': table.name, 'value': table.name_la, 'key': str(uuid.uuid1())}
                                children.append(t_obj)
                            obj['children'] = children

                        elif 'datong' in d['name']:
                            len_name=len(d['name'].split('.'))
                            if len_name<4:
                                obj['children'] = getNames(d['name'])
                        elif 'guizhou' in d['name'] :
                            len_name=len(d['name'].split('.'))
                            if len_name<4:
                                obj['children'] = getNames(d['name'])
                        elif 'ygqn' in d['name'] :
                            len_name=len(d['name'].split('.'))
                            if 'd' in d['name']:
                                if len_name<4:
                                    obj['children'] = getNames(d['name'])
                            elif len_name<3:
                                obj['children'] = getNames(d['name'])
                        else:
                            obj['children'] = getNames(d['name'])
                        datas.append(obj)
                if lang == 'en':
                    return self.returnTypeSuc_en(data=datas, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=datas, info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            scada_session.rollback()
            return self.requestError()
        finally:
            scada_session.close()


    @tornado.web.authenticated
    def post(self, kt):
        # self.refreshSession()
        pass
    @staticmethod
    def getDevicesByName(name):
        a = scada_session.query(DevicePT).filter(DevicePT.name == name).first()
        return {'title': a.descr, 'value': a.name, 'key': str(uuid.uuid1())}

    @staticmethod
    def getAllDevices(name=None):
        data = []
        if 'houma' in name:
            table_1 = 't_device'
            HisTable_1 = HisSDA(table_1)
            all=[]
            for d in range(0, 4):
                db_con = _return_db_con_sda('houma', d,0)  # 获取具体数据库链接
                data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys', HisTable_1.name.like(name + "%")).all()
                all.append(data_h[0])
                db_con.close()
        elif 'datong' in name:
            table_1 = 't_device'
            HisTable_1 = HisSDA(table_1)
            all=[]
            for d in range(0, 4):
                db_con = _return_db_con_sda('datong', d,0)  # 获取具体数据库链接
                data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys', HisTable_1.name.like(name + "%")).all()
                all.append(data_h[0])
                db_con.close()
        elif 'guizhou' in name:
            table_1 = 't_device'
            HisTable_1 = HisSDA(table_1)
            all=[]
            for d in range(0, 8):
                db_con = _return_db_con_sda('guizhou', d,0)  # 获取具体数据库链接
                data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys', HisTable_1.name.like(name + "%")).all()
                all.append(data_h[0])
                all.append(data_h[1])
                db_con.close()
        elif 'ygqn' in name:
            table_1 = 't_device'
            HisTable_1 = HisSDA(table_1)
            all=[]
            for d in range(0, 2):
                db_con = _return_db_con_sda('ygqn', d,0)  # 获取具体数据库链接
                if d==1:
                    data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys', HisTable_1.name.like(name + "%")).all()
                    all.append(data_h[0])
                    all.append(data_h[1])
                    db_con.close()
                else:
                    data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys',HisTable_1.name.like(name + "%")).all()
                    all.append(data_h[0])
                    db_con.close()
        elif 'shgyu' in name:
            table_1 = 't_device'
            HisTable_1 = HisSDA(table_1)
            all = []
            db_con = _return_db_con_sda('shgyu', 0, 0)  # 获取具体数据库链接
            data_h = db_con.query(HisTable_1).filter(HisTable_1.name != 'sys',HisTable_1.name.like(name + "%")).all()
            all.append(data_h[0])
            db_con.close()
        elif name not in exclude_station:
            all = user_session.query(PointType).filter(PointType.station == name).all()
        else:
            if name:
                all = scada_session.query(DevicePT).filter(DevicePT.name != 'sys', DevicePT.name.like(name + "%")).all()
            else:
                all = scada_session.query(DevicePT).filter(DevicePT.name != 'sys').all()
        if name not in exclude_station:
            for a in all:
                data.append({'name': a.en_equipment_name, 'descr': a.equipment_name, 'nameT': True})
        else:
            for a in all:
                data.append({"name": a.name, "descr": a.descr, "nameT": True})
        return data

    @staticmethod
    def getDeviceByName(type_, name, pageNum, pageSize):
        data = []
        if 'houma' in name:
            if name[9:11] == 'A1':
                scada_engine_h = _return_db_con_sda('houma', 0, 1)  # 获取具体数据库链接
            elif name[9:11] == 'A2':
                scada_engine_h = _return_db_con_sda('houma', 1, 1)  # 获取具体数据库链接
            elif name[9:11] == 'B1':
                scada_engine_h = _return_db_con_sda('houma', 2, 1)  # 获取具体数据库链接
            elif name[9:11] == 'B2':
                scada_engine_h = _return_db_con_sda('houma', 3, 1)  # 获取具体数据库链接
            conn = scada_engine_h.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()
            if 'houma' in name:
                s = len(name.split('.')) + 3
                if len(name.split('.')) == 4 and '.CK' not in name:
                    s = len(name.split('.')) + 1
            else:
                s = len(name.split('.')) + 1
        elif 'guizhou' in name:
            if name[7:9] == '1a' or name[7:9] == '1b':
                scada_engine_h = _return_db_con_sda('guizhou', 0, 1)  # 获取具体数据库链接
            elif name[7:9] == '2a' or name[7:9] == '2b':
                scada_engine_h = _return_db_con_sda('guizhou', 1, 1)  # 获取具体数据库链接
            elif name[7:9] == '3a' or name[7:9] == '3b':
                scada_engine_h = _return_db_con_sda('guizhou', 2, 1)  # 获取具体数据库链接
            elif name[7:9] == '4a' or name[7:9] == '4b':
                scada_engine_h = _return_db_con_sda('guizhou', 3, 1)  # 获取具体数据库链接
            elif name[7:9] == '5a' or name[7:9] == '5b':
                scada_engine_h = _return_db_con_sda('guizhou', 4, 1)  # 获取具体数据库链接
            elif name[7:9] == '6a' or name[7:9] == '6b':
                scada_engine_h = _return_db_con_sda('guizhou', 5, 1)  # 获取具体数据库链接
            elif name[7:9] == '7a' or name[7:9] == '7b':
                scada_engine_h = _return_db_con_sda('guizhou', 6, 1)  # 获取具体数据库链接
            elif name[7:9] == '8a' or name[7:9] == '8b':
                scada_engine_h = _return_db_con_sda('guizhou', 7, 1)  # 获取具体数据库链接
            conn = scada_engine_h.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()
            s = len(name.split('.')) + 1
        elif 'datong' in name:
            scada_engine_h = _return_db_con_sda('datong', (int(name[9]) - 1), 1)  # 获取具体数据库链接
            conn = scada_engine_h.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()

            if 'datong' in name:
                s = len(name.split('.')) + 3
                if len(name.split('.')) == 4 and '.CK' not in name:
                    if len(name.split('.')) == 4 and '.SS' not in name:
                        s = len(name.split('.')) + 1
            else:
                s = len(name.split('.')) + 1
        elif 'ygqn' in name:
            if 'ygqn.d' in name:
                scada_engine_h = _return_db_con_sda('ygqn', 1, 1)  # 获取具体数据库链接
            else:
                scada_engine_h = _return_db_con_sda('ygqn', 0, 1)  # 获取具体数据库链接
            conn = scada_engine_h.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()

            s = len(name.split('.')) + 1
        elif 'shgyu' in name:
            scada_engine_h = _return_db_con_sda('shgyu', 0, 1)  # 获取具体数据库链接
            conn = scada_engine_h.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()

            s = len(name.split('.')) + 1

        else:
            conn = scada_engine.raw_connection()  # 拿原生的连接
            cursor = conn.cursor()
            cursor_c = conn.cursor()
            s = len(name.split('.')) + 1
        sql_1 = "select name from {1} where name like '{2}%' group by substring_index(name,'.',{0})".format(s, data_obj[
            type_], name)
        sql_c = "select count(name) from (%s) as r" % (sql_1)
        sql = "select substring_index(name,'.',{0}) from {1} where name like '{2}%' group by substring_index(name,'.',{0}) limit {3}, {4} ".format(
            s, data_obj[type_], name, (pageNum - 1) * pageSize, pageSize)
        cursor.execute(sql)
        cursor_c.execute(sql_c)
        result = cursor.fetchall()
        result_c = cursor_c.fetchall()
        cursor.close()
        cursor_c.close()
        conn.close()

        for nam in result:
            n = nam[0]
            obj = {"name": n, "nameT": False}
            f = real_data(type_, n, 'db')
            if f['desc']:
                obj["descr"] = f['desc']
                obj["nameT"] = True
            data.append(obj)
        return data, result_c

    @staticmethod
    def getDeviceByName_1(type_, name, pageNum, pageSize):
        data = []
        total_ = 0
        if type_ == 'status':
            if name[11:] == 'ReCon':  # 遥控设备没有合并上报
                table = 't_' + type_
            else:
                table = 't_' + type_ + '_bits'
        else:
            table = 't_' + type_
        HisTable = HisACDMS_1(table)
        if name[11:] == 'ReMod':  # 如果传的name是遥调
            if type_ == 'discrete':
                device_ = mqtt_session.query(SELYDevicePT.id).filter(SELYDevicePT.name == 'ReMod').all()
            elif type_ == 'measure':
                device_ = mqtt_session.query(SELYDevicePT.id).filter(SELYDevicePT.name == name[11:]).all()
            else:
                data.append({"name": name, "nameT": False})
                return data, total_
        elif name[11:] == 'ESFC' or name[11:] == 'ESDC' or name[11:] == 'THSSA' or name[11:] == 'ReCon':  # 储能送入柜，储能送出柜，温度传感器及烟感报警  遥控
            if type_ == 'status' or type_ == 'measure':
                device_ = mqtt_session.query(SELYDevicePT.id).filter(SELYDevicePT.name == name[11:]).all()
            else:
                data.append({"name": name, "nameT": False})
                return data, total_
        elif name[11:] == 'EMeter' or name[11:] == 'PowQ':  # 电表，电能质量.
            if type_ == 'measure':
                device_ = mqtt_session.query(SELYDevicePT.id).filter(SELYDevicePT.name == name[11:]).all()
            else:
                data.append({"name": name, "nameT": False})
                return data, total_
        else:
            device_ = mqtt_session.query(SELYDevicePT.id).filter(SELYDevicePT.name == name[11:]).all()
        for i in device_:
            if type_ == 'status' and name[11:] != 'ReCon':  # ReCon 设备没有合并上报
                value_ = mqtt_session.query(StatusPT.id, StatusPT.name).filter(StatusPT.device_id == i[0]).all()
                for e in value_:
                    value = mqtt_session.query(HisTable.name, HisTable.descr).filter(HisTable.status_id == e[0]).all()
                    total_1 = len(value)
                    total_ = total_ + total_1
                    for v in value:
                        obj = {"name": name, "nameT": False}
                        obj["descr"] = v[1]
                        obj["name"] = name + '.' + e[1] + '.' + v[0]
                        obj["nameT"] = True
                        data.append(obj)
            else:
                value = mqtt_session.query(HisTable.name, HisTable.descr).filter(HisTable.device_id == i[0]).all()
                total_1 = len(value)
                total_ = total_ + total_1
                for v in value:
                    obj = {"name": name, "nameT": False}
                    obj["descr"] = v[1]
                    obj["name"] = name + '.' + v[0]
                    obj["nameT"] = True
                    data.append(obj)
        mqtt_session.close()
        return data, total_
def getNames(name):
    data = []
    if 'houma' in name:
        if name[9:11] == 'A1':
            scada_engine_h = _return_db_con_sda('houma', 0, 1)  # 获取具体数据库链接
        elif name[9:11] == 'A2':
            scada_engine_h = _return_db_con_sda('houma', 1, 1)  # 获取具体数据库链接
        elif name[9:11] == 'B1':
            scada_engine_h = _return_db_con_sda('houma', 2, 1)  # 获取具体数据库链接
        elif name[9:11] == 'B2':
            scada_engine_h = _return_db_con_sda('houma', 3, 1)  # 获取具体数据库链接
        conn = scada_engine_h.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        try:
            gg = getTreeByName('measure', name, cursor)
            for g in gg:
                obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
                if not g["nameT"]:
                    obj['children'] = getNames(g['name'])
                data.append(obj)
        except:
            pass
        cursor.close()
        conn.close()
    elif 'guizhou' in name:
        if name[7:9] == '1a' or name[7:9] == '1b':
            scada_engine_h = _return_db_con_sda('guizhou', 0, 1)  # 获取具体数据库链接
        elif name[7:9] == '2a' or name[7:9] == '2b':
            scada_engine_h = _return_db_con_sda('guizhou', 1, 1)  # 获取具体数据库链接
        elif name[7:9] == '3a' or name[7:9] == '3b':
            scada_engine_h = _return_db_con_sda('guizhou', 2, 1)  # 获取具体数据库链接
        elif name[7:9] == '4a' or name[7:9] == '4b':
            scada_engine_h = _return_db_con_sda('guizhou', 3, 1)  # 获取具体数据库链接
        elif name[7:9] == '5a' or name[7:9] == '5b':
            scada_engine_h = _return_db_con_sda('guizhou', 4, 1)  # 获取具体数据库链接
        elif name[7:9] == '6a' or name[7:9] == '6b':
            scada_engine_h = _return_db_con_sda('guizhou', 5, 1)  # 获取具体数据库链接
        elif name[7:9] == '7a' or name[7:9] == '7b':
            scada_engine_h = _return_db_con_sda('guizhou', 6, 1)  # 获取具体数据库链接
        elif name[7:9] == '8a' or name[7:9] == '8b':
            scada_engine_h = _return_db_con_sda('guizhou', 7, 1)  # 获取具体数据库链接
        conn = scada_engine_h.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        try:
            gg = getTreeByName('measure', name, cursor)
            for g in gg:
                obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
                if not g["nameT"]:
                    obj['children'] = getNames(g['name'])
                data.append(obj)
        except:
            pass
        cursor.close()
        conn.close()
    elif 'datong' in name:
        scada_engine_h = _return_db_con_sda('datong', (int(name[9])-1), 1)  # 获取具体数据库链接
        conn = scada_engine_h.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        try:
            gg = getTreeByName('measure', name, cursor)
            for g in gg:
                obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
                if not g["nameT"]:
                    obj['children'] = getNames(g['name'])
                data.append(obj)
        except:
            pass
        cursor.close()
        conn.close()
    elif 'ygqn' in name:
        d=0
        if 'ygqn.d' in name:
            d=1
        scada_engine_h = _return_db_con_sda('ygqn', d, 1)  # 获取具体数据库链接
        conn = scada_engine_h.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        try:
            gg = getTreeByName('measure', name, cursor)
            for g in gg:
                obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
                if not g["nameT"]:
                    obj['children'] = getNames(g['name'])
                data.append(obj)
        except:
            pass
        cursor.close()
        conn.close()
    elif 'shgyu' in name:
        scada_engine_h = _return_db_con_sda('shgyu', 0, 1)  # 获取具体数据库链接
        conn = scada_engine_h.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        try:
            gg = getTreeByName('measure', name, cursor)
            for g in gg:
                obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
                if not g["nameT"]:
                    obj['children'] = getNames(g['name'])
                data.append(obj)
        except:
            pass
        cursor.close()
        conn.close()
    else:
        conn = scada_engine.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        gg = getTreeByName('measure', name, cursor)
        cursor.close()
        conn.close()
        for g in gg:
            obj = {'title': g['name'], 'value': g['name'], 'key': str(uuid.uuid1())}
            if not g["nameT"]:
                obj['children'] = getNames(g['name'])
            data.append(obj)

    return data

def getNames_1(name):
    data = []
    all = mqtt_session.query(SELYDevicePT).filter(SELYDevicePT.name != 'sys').all()
    # all = mqtt_session.query(SELYDevicePT).all()

    for a in all:
        obj = {'title': 'tfStdongmu' + '.' + a.name, 'value': 'tfStdongmu' + '.' + a.name, 'key': str(uuid.uuid1())}
        obj['children'] = []
        data.append(obj)
    return data
def getTreeByName(type_, name, cursor):
    data = []
    if 'houma' in name:
        s = len(name.split('.')) + 3
        if len(name.split('.')) ==4 and '.CK' not in name:
            s = len(name.split('.')) + 1
    elif 'datong' in name:
        s = len(name.split('.')) + 3
        if len(name.split('.')) ==4 and '.CK' not in name:
            if len(name.split('.')) ==4 and '.SS' not in name:
                s = len(name.split('.')) + 1
    else:
        s = len(name.split('.')) + 1
    sql = "select substring_index(name,'.',{0}) from {1} where name like '{2}%' group by substring_index(name,'.',{0})".format(
        s, data_obj[type_], name)
    cursor.execute(sql)
    result = cursor.fetchall()
    for nam in result:
        n = nam[0]
        if 'EMS.Meter.' in n :
            pass
        else:
            obj = {"name": n, "nameT": False}
            f = real_data(type_, n, 'db')
            if f['desc']:
                return []
            data.append(obj)
    return data

def _return_db_con_sda(db, d,ty):
    '''返回sda数据库链接'''
    return db_peiz[db][ty][d]


