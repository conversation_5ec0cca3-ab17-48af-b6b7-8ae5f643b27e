from django.urls import path
from apis.project_manage import views

urlpatterns = [
    path("projects/list/", views.ProjectsViews.as_view()),  # 项目信息           en
    path("projects/download/", views.ProjectsDownloadViews.as_view()),  # 项目信息下载    en
    path("projects/del/", views.ProjectsDelViews.as_view()),  # 项目信息删除       en
    # path("projects/list/<str:id>/", views.ProjectsInfoViews.as_view()),  # 项目信息详情展示与修改           # 废弃
    # path("customization/", views.CustomizationView.as_view()),  # 省份
    path("projects/add/new/", views.ProjectsAddNewViews.as_view()),  # 项目信息添加       en
    path("projects/update/new/", views.ProjectsAddNewViews.as_view()),  # 项目信息编辑        en
    path("projects/detail/new/<int:id>", views.ProjectsAddNewViews.as_view()),  # 项目信息详情        en
    path("projects/station/delete/", views.ProjectsDelNewViews.as_view()),  # 项目站信息删除       en
    path("projects/region/list/", views.ProjectsRegionViews.as_view()),  # 省市区查询        en
    path("projects/mappings/", views.ProjectsMappingViews.as_view()),  # 企业行业字典     en
    path("projects/firm/list/", views.ProjectsFirmListViews.as_view()),  # 企业列表         en
    path("projects/upload/", views.ProjectsUploadViews.as_view()),  # 多个附件上传        no need
    path("user/info/", views.UserInfo.as_view()),  # 项目客户联系人        en

    # 需求侧响应
    path("demand_side_respond/list/", views.DemandSideRespondViews.as_view()),   # 需求侧响应查询列表        en
    path("demand_side_respond/update/", views.DemandSideRespondViews.as_view()),   # 需求侧响应编辑        en
    path("demand_side_respond/register/", views.DemandSideRegisterViews.as_view()),   # 需求侧响应注册/解除注册        en
    path("demand_side_respond/batch_entry/", views.DemandSideBatchEntryViews.as_view()),   # 用户编号批量录入       en
    path("demand_side_respond/download/", views.DemandSideDownloadViews.as_view()),   # 批量下载        en

]
