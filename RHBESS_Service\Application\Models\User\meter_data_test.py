#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-06-16 18:10:54
#@FilePath     : \RHBESS_Service\Application\Models\User\meter_data_test.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-28 17:56:39


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.event import Event
from Application.Models.User.user import User

class MeterDataTest(user_Base):
    u'计量表功率记录'
    __tablename__ = "t_meter_data_test"
    ntime = Column(Integer, nullable=False, primary_key=True,comment=u"采集时间")
    ia = Column(Integer, nullable=False, comment=u"A相电流")
    ib = Column(Integer, nullable=False, comment=u"A相电流")
    ic = Column(Integer, nullable=False, comment=u"A相电流")
    pa = Column(Integer, nullable=False, comment=u"A相功率")
    pb = Column(Integer, nullable=False, comment=u"A相功率")
    pc = Column(Integer, nullable=False, comment=u"A相功率")
    i2a = Column(Integer, nullable=True, comment=u"串口2A相电流")
    i2b = Column(Integer, nullable=True, comment=u"串口2A相电流")
    i2c = Column(Integer, nullable=True, comment=u"串口2A相电流")
    p2a = Column(Integer, nullable=True, comment=u"串口2A相功率")
    p2b = Column(Integer, nullable=True, comment=u"串口2A相功率")
    p2c = Column(Integer, nullable=True, comment=u"串口2A相功率")
    device = Column(VARCHAR(50), nullable=True,comment=u"值描述")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")

    en_device = Column(VARCHAR(50), nullable=True, comment=u"值描述")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'ntime':%s,'ia':%s,'ib':%s,'ic':%s,'pa':%s,'pb':%s,'pc':%s,'device':'%s','en_device':'%s','op_ts':'%s'}" % (self.ntime,
        self.ia,self.ib,self.ic,self.pa,self.pb,self.pc,self.device,self.en_device,self.op_ts)
        return bean.replace("None",'')

        
 