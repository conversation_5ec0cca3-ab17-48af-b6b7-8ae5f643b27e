from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import HttpResponse


class AuthMiddleware(MiddlewareMixin):
    """跨域及 options 请求处理"""
    def process_request(self, request):
        if request.method == "OPTIONS":
            return HttpResponse("")

    def process_response(self, request, response):
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "*"
        response["Access-Control-Allow-Headers"] = "*"
        return response
