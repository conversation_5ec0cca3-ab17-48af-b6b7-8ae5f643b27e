#!/usr/bin/env python
# coding=utf-8
#@Information:字典管理
#<AUTHOR> WYJ
#@Date         : 2023-08-31 10:49:59
#@FilePath     : \RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseDictHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 10:51:26

import tornado.web
from Application.Models.SideForecase.side_forecase_dict_policy_infos import ForecaseDicPolicy
from Application.Models.SideForecase.side_forecase_dict_policy_type import ForecaseDicPolicyType
from Application.Models.SideForecase.side_forecase_group import ForecaseGroup
from Application.Models.SideForecase.side_forecase_organization import ForecaseOrganization
from Application.Models.SideForecase.side_forecase_dict_policy_infos_user import ForecaseDicPolicyUser
from Application.Models.SideForecase.side_forecase_user import ForecaseUser
from Tools.Utils.mimio_tool import upload_file, MinioTool
from Tools.Utils.pdf_shuiyin import add_watermark, create_watermark
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import <PERSON><PERSON>andler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import *
from sqlalchemy import func,or_
from xhtml2pdf import pisa
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from xhtml2pdf.default import DEFAULT_FONT
import os

class SideForecasePolicyHandleIntetface(BaseHandler):

    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'GetPolicyRead':  # 阅读政策情报详情
                id = int(self.get_argument('id', None))#情报id
                logging.info('id:%s' % (id))
                data = []
                page_=user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use == '1', ForecaseDicPolicy.id==id).first()
                if not page_:
                    return self.customError("无效id")
                pa = eval(str(page_))
                data.append({'id': pa['id'], 'content': pa['content'],'user': pa['user'], 'create_time': pa['create_time'],'page_ty_name_1': pa['page_ty_name_1'],
                                             'page_ty_name_2': pa['page_ty_name_2'],'page_ty_name_3': pa['page_ty_name_3'], 'title': pa['title']})
                return self.returnTypeSuc(data)
            elif kt == 'GetPolicyTagD':  # 标签下拉框
                ty = self.get_argument('ty', None)  # 标题
                data = []
                filter=[ForecaseDicPolicyType.is_use == '1']
                if ty:
                    filter.append(ForecaseDicPolicyType.ty == ty)
                page_use = user_session.query(ForecaseDicPolicyType).filter(*filter).order_by(ForecaseDicPolicyType.id.asc()).all()
                for pag in page_use:
                    pa = eval(str(pag))
                    data.append(pa)
                return self.returnTypeSuc(data)
            elif kt == 'GetPolicyUsesD':  # 可见人下拉框
                e={'全部': []}
                page_or=user_session.query(ForecaseOrganization).filter(ForecaseOrganization.is_use == '1').all()
                for pag1 in page_or:
                    pa_1 = eval(str(pag1))
                    page_gr = user_session.query(ForecaseGroup).filter(ForecaseGroup.is_use == '1',ForecaseGroup.organization_id==pa_1['id']).all()
                    dd={'org':pa_1['descr'],'id':pa_1['id'],'gro':[]}
                    for pag2 in page_gr:
                        pa_2 = eval(str(pag2))
                        dd['gro'].append({'name':pa_2['name'],'id':pa_2['id']})
                    e['全部'].append(dd)
                return self.returnTypeSuc(e)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'AddPolicy':  # 新建政策情报
                title_name= self.get_argument('title_name', None)  # 标题
                dic_police_ids = self.get_argument('dic_police_ids', [])  # 标签id
                content = self.get_argument('content', None)  # 内容
                see_org = self.get_argument('see_org', '[]')  # 可见部门id
                see_gro = self.get_argument('see_id', '[]')  # 可见组织id
                rank_ids = self.get_argument('rank_ids', '[]')  # 可见职级id
                is_use = self.get_argument('is_use', '1')  # 是否使用1是0否默认1，2草稿箱
                org_gro = self.get_argument('org_gro', '[]')  # 可见部门和小组字段
                see_org = eval(str(see_org))
                see_gro = eval(str(see_gro))
                rank_ids = eval(str(rank_ids))
                dic_police_ids = eval(str(dic_police_ids))
                if not title_name:
                    return self.customError("标题为填写，请返回修改！")
                logging.info('title_name:%s,see_org:%s,see_gro:%s,is_use:%s,rank_id:%s' % (title_name, see_org, see_gro, is_use, rank_ids))
                session = self.getOrNewSession()
                user_id = session.user['id']
                user_name = session.user['name']
                see_users=[]
                page_ = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.title==title_name,or_(ForecaseDicPolicy.is_use=='1',ForecaseDicPolicy.is_use=='2')).first()
                if page_:
                    return self.customError("情报政策已存在,无法新增。请返回修改！")

                page_ty_name = user_session.query(ForecaseDicPolicyType.name,ForecaseDicPolicyType.id,ForecaseDicPolicyType.ty).filter(ForecaseDicPolicyType.is_use=='1',ForecaseDicPolicyType.id.in_(dic_police_ids)).order_by(ForecaseDicPolicyType.id.asc()).all()
                page_ty_name_1=[]#1）地区标签 2）情报类型标签 3）重点提示标签
                page_ty_name_2=[]
                page_ty_name_3=[]
                dic_police_ids_1=[]
                dic_police_ids_2=[]
                dic_police_ids_3=[]
                if page_ty_name:
                    for pp in page_ty_name:
                        if pp[2] ==1:
                            page_ty_name_1.append(pp[0])
                            dic_police_ids_1.append(pp[1])
                        elif pp[2] ==2:
                            page_ty_name_2.append(pp[0])
                            dic_police_ids_2.append(pp[1])
                        elif pp[2] == 3:
                            page_ty_name_3.append(pp[0])
                            dic_police_ids_3.append(pp[1])

                try:
                    if see_org!=[]:
                        see_users_ = user_session.query(ForecaseUser.id).filter(ForecaseUser.is_use == '1',ForecaseUser.organization_id.in_(see_org)).all()
                    if see_gro!=[]:
                        see_users_ = user_session.query(ForecaseUser.id).filter(ForecaseUser.is_use == '1',ForecaseUser.group_id.in_(see_gro)).all()
                    if see_users_:
                        for ss in see_users_:
                            see_users.append(ss[0])
                except:
                    pass
                dic_police_ids_1=str(dic_police_ids_1).replace("'", '"')
                dic_police_ids_2=str(dic_police_ids_2).replace("'", '"')
                dic_police_ids_3=str(dic_police_ids_3).replace("'", '"')
                page_ty_name_1=str(page_ty_name_1).replace("'", '"')
                page_ty_name_2=str(page_ty_name_2).replace("'", '"')
                page_ty_name_3=str(page_ty_name_3).replace("'", '"')
                see_users=str(see_users).replace("'", '"')
                if int(is_use)==2:
                    d = ForecaseDicPolicy(user_id=user_id,user=user_name, create_time=timeUtils.getNewTimeStr(), content=content,
                                          title=title_name, see_users=see_users, dic_police_ids_1=dic_police_ids_1,dic_police_ids_2=dic_police_ids_2,dic_police_ids_3=dic_police_ids_3,
                                          page_ty_name_1=page_ty_name_1,page_ty_name_2=page_ty_name_2,page_ty_name_3=page_ty_name_3, is_use='2', is_top=0,org_gro=org_gro,see_org=str(see_org),see_gro=str(see_gro))
                else:
                    d = ForecaseDicPolicy(user_id=user_id,user=user_name,create_time=timeUtils.getNewTimeStr(), content=content,title=title_name,see_users=see_users,
                    dic_police_ids_1 = dic_police_ids_1, dic_police_ids_2 = dic_police_ids_2, dic_police_ids_3 = dic_police_ids_3,page_ty_name_1 = page_ty_name_1, page_ty_name_2 = page_ty_name_2,
                                          page_ty_name_3 = page_ty_name_3,is_top=0,org_gro=org_gro,see_org=str(see_org),see_gro=str(see_gro))
                user_session.add(d)
                user_session.commit()
                intle_id = d.id
                add_list = []
                total = len(rank_ids) if len(rank_ids) > len(see_gro) else len(see_gro)
                for i in range(total):
                    group_id = see_gro[i] if i < len(see_gro) else None
                    rank_id = rank_ids[i] if i < len(rank_ids) else None
                    _d = ForecaseDicPolicyUser(intel_id=intle_id, group_id=group_id, rank_id=rank_id)
                    add_list.append(_d)
                user_session.add_all(add_list)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'UpdatePolicy':  # 编辑草稿箱
                id = self.get_argument('id', None)  #情报id
                title_name = self.get_argument('title_name', None)  # 标题
                dic_police_ids = self.get_argument('dic_police_ids', [])  # 标签id
                content = self.get_argument('content', None)  # 内容
                see_org = self.get_argument('see_org', '[]')  # 可见部门id
                see_gro = self.get_argument('see_id', '[]')  # 可见组织id
                rank_ids = self.get_argument('rank_ids', '[]')  # 可见职级id
                is_use = self.get_argument('is_use', '1')  # 是否使用1是0否默认1，2草稿箱id
                org_gro = self.get_argument('org_gro', '[]')  # 可见部门和小组字段
                dic_police_ids = eval(str(dic_police_ids))
                see_org = eval(str(see_org))
                see_gro = eval(str(see_gro))
                rank_ids = eval(str(rank_ids))
                logging.info('title_name:%s,dic_police_ids:%s' % (title_name, dic_police_ids))
                session = self.getOrNewSession()
                user_id = session.user['id']
                user_name = session.user['name']
                see_users_=[]
                page_1 = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.title==title_name,ForecaseDicPolicy.id!=id,or_(ForecaseDicPolicy.is_use=='1',ForecaseDicPolicy.is_use=='2')).first()
                if page_1:
                    return self.customError("情报政策已存在,请返回修改！")

                page_ = user_session.query(ForecaseDicPolicy).filter(or_(ForecaseDicPolicy.is_use=='1',ForecaseDicPolicy.is_use=='2'),ForecaseDicPolicy.id==id).first()
                if not page_:
                    return self.customError("无效id")

                page_ty_name = user_session.query(ForecaseDicPolicyType.name,ForecaseDicPolicyType.id,ForecaseDicPolicyType.ty).filter(ForecaseDicPolicyType.is_use == '1', ForecaseDicPolicyType.id.in_(dic_police_ids)).order_by(ForecaseDicPolicyType.id.asc()).all()
                page_ty_name_1 = []  # 1）地区标签 2）情报类型标签 3）重点提示标签
                page_ty_name_2 = []
                page_ty_name_3 = []
                dic_police_ids_1 = []
                dic_police_ids_2 = []
                dic_police_ids_3 = []
                if page_ty_name:
                    for pp in page_ty_name:
                        if pp[2] == 1:
                            page_ty_name_1.append(pp[0])
                            dic_police_ids_1.append(pp[1])
                        elif pp[2] == 2:
                            page_ty_name_2.append(pp[0])
                            dic_police_ids_2.append(pp[1])
                        elif pp[2] == 3:
                            page_ty_name_3.append(pp[0])
                            dic_police_ids_3.append(pp[1])
                try:
                    if see_org != []:
                        see_users = user_session.query(ForecaseUser.id).filter(ForecaseUser.is_use == '1',ForecaseUser.organization_id.in_(see_org)).all()
                    if see_gro != []:
                        see_users = user_session.query(ForecaseUser.id).filter(ForecaseUser.is_use == '1',ForecaseUser.group_id.in_(see_gro)).all()
                    if see_users:
                        for ss in see_users:
                            see_users_.append(ss[0])
                except:
                    pass
                dic_police_ids_1 = str(dic_police_ids_1).replace("'", '"')
                dic_police_ids_2 = str(dic_police_ids_2).replace("'", '"')
                dic_police_ids_3 = str(dic_police_ids_3).replace("'", '"')
                page_ty_name_1 = str(page_ty_name_1).replace("'", '"')
                page_ty_name_2 = str(page_ty_name_2).replace("'", '"')
                page_ty_name_3 = str(page_ty_name_3).replace("'", '"')
                see_users_ = str(see_users_).replace("'", '"')

                page_.create_time = timeUtils.getNewTimeStr()
                page_.content = content
                page_.title = title_name
                page_.see_users = see_users_
                page_.dic_police_ids_1 = dic_police_ids_1
                page_.dic_police_ids_2 = dic_police_ids_2
                page_.dic_police_ids_3 = dic_police_ids_3
                page_.page_ty_name_1 = page_ty_name_1
                page_.page_ty_name_2 = page_ty_name_2
                page_.page_ty_name_3 = page_ty_name_3
                page_.org_gro = org_gro
                page_.see_org = str(see_org)
                page_.see_gro = str(see_gro)
                if int(is_use)==1:
                    page_.is_use = '1'
                elif int(is_use)==2:
                    page_.is_use = '2'
                user_session.query(ForecaseDicPolicyUser).filter(ForecaseDicPolicyUser.intel_id==id).delete()
                user_session.commit()
                add_list = []

                total = len(rank_ids) if len(rank_ids) > len(see_gro) else len(see_gro)
                for i in range(total):
                    group_id = see_gro[i] if i < len(see_gro) else None
                    rank_id = rank_ids[i] if i < len(rank_ids) else None
                    _d = ForecaseDicPolicyUser(intel_id=id, group_id=group_id, rank_id=rank_id)
                    add_list.append(_d)
                user_session.add_all(add_list)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'AddPicPolicy':  # 上传图片
                file_name = self.get_argument('file_name', None)  # 文件名称
                files = self.request.files
                logging.info('file_name:%s' % (file_name))
                imgs = files.get('files')
                download_url=''
                if imgs:
                    data = imgs[0].get('body')
                    f=SideForecaseFilename(1)
                    filenames=f.filename(imgs[0])
                    download_url = upload_file(data, 'side', filenames).split('?')[0]
                    if not download_url:
                        logging.info("file_name:%s," % (filenames))
                        return self.customError("上传附件超过100MB限制，请检查！")
                return self.returnTypeSuc({'download_url':download_url})
            elif kt == 'AddPicFiles':  # 上传文件
                files = self.request.files
                imgs = files.get('files')
                filename_s = []
                download_url_s = []
                if imgs:
                    data = imgs[0].get('body')
                    size = len(data)
                    file_size = f'{round(size / 1024, 2)} KB' if size < 1048576 else f'{round(size / 1048576, 2)} MB'
                    f = SideForecaseFilename(1)
                    filename = f.filename(imgs[0])
                    download_url = upload_file(data, 'side', filename)
                    if not download_url:
                        logging.info("file_name:%s," % (filename))
                        return self.customError("上传附件超过100MB限制，请检查！")
                    download_url = download_url.split('?')[0]
                    filename_s.append(filename)
                    download_url_s.append(download_url)
                return self.returnTypeSuc({'download_url':download_url_s,'filenames':filename_s})
            elif kt == 'TopPolicy':  # 置顶情报
                id = self.get_argument('id', None)  #情报id
                page_ = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use == '1',ForecaseDicPolicy.id==id).first()
                if not page_:
                    return self.customError("无效id")
                page_.is_top=1
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'CancelTopPolicy':  # 取消置顶情报
                id = self.get_argument('id', None)  #情报id
                page_ = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use == '1',ForecaseDicPolicy.id==id).first()
                if not page_:
                    return self.customError("无效id")
                page_.is_top=0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'AddTag':  # 添加标签
                tag_name = self.get_argument('tag_name', None)  # 标签名称
                ty = self.get_argument('ty', None)  # 标签类型
                logging.info('tag_name:%s' % (tag_name))
                page_ = user_session.query(ForecaseDicPolicyType).filter(ForecaseDicPolicyType.ty == ty,ForecaseDicPolicyType.name == tag_name,ForecaseDicPolicyType.is_use == '1').first()
                if page_:
                    return self.customError("标签已存在,无法新增。请返回修改！")
                d = ForecaseDicPolicyType(name=tag_name,ty=ty)
                user_session.add(d)
                user_session.commit()
                data=[]
                page_use = user_session.query(ForecaseDicPolicyType).filter(ForecaseDicPolicyType.is_use=='1',ForecaseDicPolicyType.id==d.id).first()
                data.append(eval(str(page_use)))
                return self.returnTypeSuc(data)
            elif kt == 'DeleteTag':  # 删除标签
                id = self.get_argument('id', None)  # 标签id
                logging.info('id:%s' % (id))
                page_use = user_session.query(ForecaseDicPolicyType).filter(ForecaseDicPolicyType.id == id,ForecaseDicPolicyType.is_use == '1').first()
                if not page_use:
                    return self.customError("无效id")
                page_use.is_use=0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'DeletePolicy':  # 删除政策情报（草稿）
                id = self.get_argument('id', None)  # 情报id
                is_use = self.get_argument('is_use', '1')  # 默认1，2草稿箱
                logging.info('id:%s' % (id))
                page_use = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.id == id,ForecaseDicPolicy.is_use == str(is_use)).first()
                if not page_use:
                    return self.customError("无效id")
                page_use.is_use=0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'PolicyImport':  # 导出pdf
                data = self.get_argument('data', None)  # 标题
                logging.info('data:%s' % (data))
                data = data.replace("'", '"')
                data = eval(str(data))
                path = "/home/<USER>/RHBESS_Service/Application/Static/msyh.ttf"
                pdfmetrics.registerFont(TTFont('yh', path))
                DEFAULT_FONT['helvetica'] = 'yh'
                title = '标题：' + data['title']
                use = '创建人：' + data['user']
                page_ty_name_1 = '地区:' + str(data['page_ty_name_1'])[2:-2].replace("', '", " ")
                page_ty_name_2 = '情报类型:' + str(data['page_ty_name_2'])[2:-2].replace("', '", " ")
                page_ty_name_3 = '重点提示:' + str(data['page_ty_name_3'])[2:-2].replace("', '", " ")
                content_l = data['content'].split('span')
                content = '<span'
                for cc in content_l:
                    ind = content_l.index(cc)
                    if ind > 0:
                        if 'img src' in cc:
                            content += cc + "span"
                        elif 'href=' in cc:
                            content += cc + "span"
                        else:
                            if len(cc) > 100:
                                cc = ('(').join(cc.split('（'))
                                cc = (')').join(cc.split('）'))
                                cc = (',').join(cc.split('，'))
                                part1 = cc[20:95]
                                part2 = cc[95:]
                                if len(part2) > 75:
                                    cc = getCc(part2)
                                    cc = part1 + "<br>" + cc + "span"
                                else:
                                    cc = part1 + "<br>" + part2 + "span"
                                content += cc
                            else:
                                cc = ('11px;">').join(cc.split('16px;">'))
                                content += cc + "span"
                content = content[:-4]
                # 创建HTML模板
                html_template = """
                                                <!DOCTYPE html>
                                                <html>
                                                <head>
                                                    <meta charset="UTF-8">
                                                    <title>{title}</title>
                                                    <style>
                                                        p.lighter {{
                                                            color: #999; /* 设置颜色为浅灰色 */
                                                        }}
                                                        p.different {{
                                                            color: #ADD8E6; /* 设置颜色浅蓝色 */
                                                        }}
                                                    </style>
                                                </head>
                                                <body>
                                                    <h1>{title}</h1>
                                                    <p class="lighter">{use}</p>
                                                    <p class="different">{page_ty_name_1}</p>
                                                    <p class="different">{page_ty_name_2}</p>
                                                    <p class="different">{page_ty_name_3}</p>
                                                    {content}

                                                </body>
                                                </html>
                                                """
                # 使用字符串格式化将data中的值插入到模板中
                html = html_template.format(title=title, use=use, page_ty_name_1=page_ty_name_1,
                                            page_ty_name_2=page_ty_name_2, page_ty_name_3=page_ty_name_3,
                                            content=content)
                outputFilename = data['title'] + '.pdf'
                resultFile = open(outputFilename, "w+b")
                pisaStatus = pisa.CreatePDF(
                    html,
                    dest=resultFile)
                resultFile.close()
                putFilename = data['title'] + '-.pdf'
                session = self.getOrNewSession()
                user_name = session.user['name']
                tet = "融和元储 " + user_name
                pdf_watermark = create_watermark(tet)
                data_2 = add_watermark(outputFilename, putFilename, pdf_file_mark=pdf_watermark)
                logging.error(data_2)
                path = os.path.abspath(data_2)
                minioTool = MinioTool()
                download_url = minioTool.upload_local_file(outputFilename, path, 'side')
                if os.path.isfile(outputFilename):
                    os.remove(outputFilename)
                return self.returnTypeSuc({"download_url": download_url})

        except Exception as E:
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


def getCc(cc):
    part1 = cc[:75]
    part2 = cc[75:]
    if len(part2) > 75:
        cc = getCc(part2)
        cc = part1 + "<br>" + cc + "span"
    else:
        cc = part1 + "<br>" + part2 + "span"
    return cc