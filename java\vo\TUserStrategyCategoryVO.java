package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户策略分类VO
 */
@Data
@ApiModel("用户策略分类VO")
public class TUserStrategyCategoryVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("分类名称")
    private String name;

    @ApiModelProperty("策略ID")
    private Long strategyId;

    @ApiModelProperty("充电配置(JSON格式)")
    private String chargeConfig;

    @ApiModelProperty("是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty("是否跟随名称")
    private String isFollowName;

    @ApiModelProperty("RL列表(JSON格式)")
    private String rlList;

    @ApiModelProperty("解释JSON(JSON格式)")
    private String explainJson;

    @ApiModelProperty("PV列表(JSON格式)")
    private String pvList;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("是否使用名称")
    private String isUseName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
