#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ
# @Date         : 2022-04-29 09:26:06
# @FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\num_utils.py
# @Email        : <EMAIL>
# @LastEditTime : 2024-01-24 14:21:08


# -*- coding=utf-8 -*-
import json, re
import logging
import numpy as np
import pandas as pd
from Tools.DB.redis_con import r_real
# from translate import Translator
import requests
import random
from Tools.Utils.send_mail import sendMail_
from Tools.DataEnDe.MD5 import MD5Tool
import configparser
import os
from Application.Models.base_handler import BaseHandler
# 查询配置文件
from Tools.Utils.time_utils import timeUtils

model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

path = basepath + "/Application/Cfg/test.ini"
model_config.read(path, encoding='utf-8')

user_email_ = model_config.get('peizhi', 'user_email')
user_email = json.loads(user_email_)

appid = '20240109001935690'
appkey = '0ORDSzOBSpmyLpkXKbWk'
ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
if ret == 0:
    url = 'https://fanyi-api.baidu.com/api/trans/vip/translate'
else:
    url = 'http://************:50000/translation/'


# appid = '20240109001935547'
# appkey = '80Mn4bYV881ZUFwzQhI1'

# appid = '20240114001940932'
# appkey = 'QiLaEIM65UK1TyiEQO04'

# url = "http://api.fanyi.baidu.com/api/trans/vip/translate"

# def make_md5(s, encoding='utf-8'):
#     return md5(s.encode(encoding)).hexdigest()

def num_retain(num, N=2):
    '''
    @description: 保留N位有效小数,默认两位
    @param num 要保留的数
    @param N 要保留的有效位数
    @return {*}
    '''
    try:
        num = float(num)
        if isinstance(N, int):
            a = format(num, '.%df' % N)
            return a
        else:
            print ('parameter2 is not int')
            return None
    except:
        print ('parameter1 is not number')
        return None


def is_number(s):
    '''判断字符是否为数字'''
    try:
        f = float(s)
        if f != f or f == float('inf') or f == float('-inf'):
            return False
        return True
    except ValueError:
        return False


def month_days(year, month):
    '''
    根据年月，计算当月有多少天
    '''
    year = int(year)
    m = int(month)
    if m == 1 or m == 3 or m == 5 or m == 7 or m == 8 or m == 10 or m == 12:
        return 31
    elif m == 4 or m == 6 or m == 9 or m == 11:
        return 30
    elif year % 4 == 0:  # 闰年2月29天
        return 29
    else:
        return 28


def fill0(num, N=2):
    '''
    @description: 字符串补0
    @param num 需要补0的字符串
    @param N 补0后的总长度
    @return 返回补0后的字符串
    '''
    s = str(num).zfill(N)
    return s


def judge_phone(tel):
    import re
    """
    该函数判断一个字符串是否为手机号
    :param tel:
    :return:
    """
    tel = str(tel)
    ret = re.match(r"^1[3456789]\d{9}$", tel)
    if ret:
        return True
    else:
        return False


def judge_email(tel):
    import re
    """
    该函数判断一个字符串是否为邮箱
    :param tel:
    :return:
    """
    tel = str(tel)
    ret = re.fullmatch(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', tel)
    if ret:
        return True
    else:
        return False


def judge_ip(ipAddr):
    import re
    """
    该函数判断一个字符串是否为正确ip地址
    :param tel:
    :return:
    """
    compile_ip = re.compile(
        '^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$')
    if compile_ip.match(ipAddr):
        return True
    else:
        return False


def computeMD5(msg):
    import hashlib
    '''md5加密'''
    m = hashlib.md5()
    m.update(msg.encode(encoding='utf-8'))
    return m.hexdigest()


def make_targz(output_filename, source_dir):
    #  打包文件夹，输出打包文件，要打包的文件
    import os, tarfile
    with tarfile.open(output_filename, "w:gz") as tar:
        tar.add(source_dir, arcname=os.path.basename(source_dir))
    tar.close()


def is_json(myjson):  # 判断是否为json
    try:
        json_object = json.loads(str(myjson))
    except ValueError as e:
        return False
    return True


def list_sum_mm(data, M, bd=-500):
    '''返回数组求和后的总和，包含满码值'''
    datas, v = [], 0
    data = np.diff(data).tolist()  # 后一个值减去前一个值得差集合
    for da in data:
        if da < -1100:  # 异常数据跳变
            continue
        elif da < 0 and da < bd:  # 过滤波动,默认-500
            v = da + M
        elif da > 500:
            continue
        else:
            v = da
        datas.append(v)
    return float(np.sum(datas))


def list_soc_mm(data):
    '''计算放电和补点的soc变化总数'''
    data1, data2 = [], []  # 放电变化集合；充电变化集合
    data = np.diff(data).tolist()  # 后一个值减去前一个值得差集合
    for da in data:
        if da < 0:  # 过滤波动,默认-2
            data1.append(abs(da))
        else:
            data2.append(da)

    return float(np.sum(data1)), float(np.sum(data2))


def complete_data(data, ti='5S', timF=True):
    '''补全数据
    此方法在获取时间时还有优化空间
    data:{"time":["2022-01-01 12:01:11","2022-01-01 12:01:22", "2022-01-01 12:01:32", "2022-01-01 12:01:37"], "value":range(1,5)}
    ti: Y:年度；M:月；D:日；B:工作日；C：自定义工作日；W周；H：小时；T或min:分钟；S：秒；ms：毫秒
    timF:是否需要返回时间列表，boolean 默认是
    '''
    df = pd.DataFrame(data)
    df["time"] = df["time"].astype("datetime64")  # 确保数据格式为日期
    date_range = pd.date_range(start=df["time"].min(), end=df["time"].max(), freq=ti)  # freq="D"表示按天，可以按分钟，月，季度，年等
    end = df.set_index("time").reindex(index=date_range, method='ffill')  # ffill 向前填充
    # end = df.set_index("time").resample(ti).ffill() # ffill 向前填充(方法更新)
    v = end.value.to_list()  # 所有值

    # df['time'] = pd.to_datetime(df['time'])
    # df.set_index('time', inplace=True)
    # end = df.resample(ti).ffill().fillna(0)
    # v= end.value.to_list()
    # df = None  # 释放内存
    if timF:
        dat = []
        for k in end.to_dict()['value'].keys():
            dat.append(str(k))
        return {'time': dat, 'value': v}
    else:
        return v


def real_data(type_, name, db):
    '''计算实时值'''
    obj = {'value': 0.00, 'desc': '', 'unit': '', 'valueDesc': '', 'index': -1}
    if db == 'ram':  # 从scada开辟的内存取值
        import dm2016
        measure = dm2016.MeasureMgr()
        status = dm2016.StatusMgr()
        discrete = dm2016.DiscreteMgr()
        cumulant = dm2016.CumulantMgr()
        if type_ == 'measure':  # 测量量
            m = measure.getByName(name)
            if m:
                obj['value'] = m.rt()['value']
                obj['desc'] = m.desc()
                obj['index'] = m.index()
            else:
                logging.error('%s %s not in scada' % (type_, name))
        elif type_ == 'status':  # 状态量
            s = status.getByName(name)
            if s:
                v = s.rt()['value']
                obj['value'] = v
                obj['desc'] = s.desc()
                obj['valueDesc'] = s.valueDesc(v)
                obj['index'] = s.index()
            else:
                logging.error('%s %s not in scada' % (type_, name))
        elif type_ == 'discrete':  # 离散量
            d = discrete.getByName(name)
            if d:
                v = d.rt()['value']
                obj['value'] = v
                obj['desc'] = d.desc()
                obj['valueDesc'] = d.valueDesc(v)
                obj['index'] = d.index()
            else:
                logging.error('%s %s not in scada' % (type_, name))
        elif type_ == 'cumulant':  # 累计量
            c = cumulant.getByName(name)
            if c:
                obj['value'] = c.rt()['value']
                obj['desc'] = c.desc()
                obj['index'] = c.index()
            else:
                logging.error('%s %s not in scada' % (type_, name))
    elif db == 'db':  # 从redis中取值
        v = r_real.hget(type_, name)
        if v:
            obj = eval(v.decode())
            if not isinstance(obj, dict):
                obj = eval(obj)
    elif db == 'dbn':  # 没有点或该站无值占位使用
        obj = {'value': '--', 'desc': '', 'unit': '', 'valueDesc': '', 'index': -1}

    else:
        logging.error("参数错误 %s %s %s" % (type_, name, db))
    return obj


def password_is_valid(password):
    '''密码必须包括大小写字母，数字和特殊字符长度大于8'''
    pwd_regex = '^(?=\S{8,18}$)(?=.*?\d)(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[^A-Za-z\s0-9])'
    compiled_regex = re.compile(pwd_regex)

    if (re.search(compiled_regex, password)):
        return True
    else:
        return False


# # real_data('status','tpSthalun.PcsSt1.Lp1.DcContAllowRk3','db')
# real_data('status','tpSthalun.PcsSt1.Lp1.DcContAllowRk3','db')

def translate_text(text_, ty=None):
    '''
    text_: 需要翻译的文本
    user_email: 异常需要接收邮件的人员，列表[]
    ty: 1: 转中文 ; 2: 转英文to_Lang: 目标语言
    '''
    from_lang = 'zh' if ty == 2 else 'en'
    to_lang = 'en' if ty == 2 else 'zh'

    # translator = Translator(from_lang=from_lang, to_lang=to_lang)
    # translated_text_ = translator.translate(text_)
    # # if 'MYMEMORY WARNING' or 'HTTPS://' in translated_text_:
    # if 'MYMEMORY WARNING' in translated_text_:
    result = ''
    try:
        salt = random.randint(32768, 65536)  # 生产随机数
        try:
            float(text_)
            return text_
        except:
            if '“' in text_:
                text_ = text_.replace('“', '<')
            if '”' in text_:
                text_ = text_.replace('”', '>')
            sign = MD5Tool.get_str_md5(appid + text_ + str(salt) + appkey)  # 签名加密 固定顺序
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            payload = {'appid': appid, 'q': text_, 'from': from_lang, 'to': to_lang, 'salt': salt, 'sign': sign}
            r = requests.post(url, params=payload, headers=headers)
            result = r.json()
            print(result["trans_result"][0]['dst'], '************')
            # print(json.dumps(result, indent=4, ensure_ascii=False),'************')
            return result["trans_result"][0]['dst']
    except Exception as E:
        print (result)
        # sendMail_("您的实时翻译异常，请关注 %s" % result, "实时翻译异常消息通知", "山海系统", "XXX", user_email)


class Translate_cls(object):
    """
    翻译类
    """

    def __init__(self, ty):
        """
        :param ty: 1: 英文转中文 ; 2: 中文转英文
        """
        self.ty = ty

    def is_chinese(self, string):
        """
        判断是否存在中文或英文
        :param string:
        :return:
        """
        if self.ty == 2:
            for ch in string:
                if u'\u4e00' <= ch <= u'\u9fa5':
                    return True
        else:
            for ch in string:
                ch = ord(ch)
                if 65 <= ch <= 90 or (97 <= ch <= 122):
                    return True
        return False

    def list_chinese(self, ch_list):
        """
        处理列表类型
        :param ch_list:
        :return:
        """
        new_list = []
        for i in ch_list:
            if isinstance(i, str):
                if self.is_chinese(i):
                    if '{' in i or '[' in i:
                        i = eval(i)
                        if isinstance(i, dict):
                            i = self.dict_chinese(i)
                        if isinstance(i, list):
                            return self.list_chinese(i)
                    else:
                        new_list.append(translate_text(i, self.ty))
                else:
                    new_list.append(i)
            elif isinstance(i, dict):
                new_list.append(self.dict_chinese(i))
            elif isinstance(i, list):
                new_list.append(self.list_chinese(i))
            else:
                new_list.append(i)
        return new_list

    def dict_chinese(self, ch_dict):
        """
        处理字典类型
        :param ch_dict:
        :return:
        """
        for k, v in ch_dict.items():
            if isinstance(v, str):
                if self.is_chinese(v):
                    ch_dict[k] = self.str_chinese(v)
                else:
                    ch_dict[k] = v
            elif isinstance(v, dict):
                ch_dict[k] = self.dict_chinese(v)
            elif isinstance(v, list):
                ch_dict[k] = self.list_chinese(v)
        return ch_dict

    def str_chinese(self, string):
        """
        字符串类型
        :param string:
        :param ty: 1: 转中文 ; 2: 转英文
        :return:
        """
        if self.is_chinese(string):
            if '{' in string or '[' in string:
                string = eval(string)
                if isinstance(string, dict):
                    string = self.dict_chinese(string)
                elif isinstance(string, list):
                    string = self.list_chinese(string)
            else:
                string = translate_text(string, self.ty)
        if not isinstance(string, str):
            string = json.dumps(string)
        if '\\n' in string:
            string = string.replace('\\n', '/')
        if "'s" in string:
            string = string.replace("'s", " is")
        if "' s" in string:
            string = string.replace("' s", " is")
        if "'an" in string:
            string = string.replace("'an", " an")
        if "o'clock" in string:
            string = string.replace("o'clock", 'of the clock')
        return string


def get_conf_name(db):  # 电站值的配置
    if db == 'halun' or db == 'taicang' or db == 'binhai':  # PCS_SOC，PCS有功功率，功率因数，PCS交流电压，PCS交流电流，PCS直流电压，PCS直流电流,PCS温度，PCS日放电，PCS日充电,PCS SOH
        return 'soc_1', 'PCS_yougong_1', 'PCS_PF_1', 'PCS_jldy_1', 'PCS_jldl_1', 'PCS_zldy_1', 'PCS_zldl_1', 'PCS_T_1', 'PCS_Disg_1', 'PCS_Chag_1', 'PCS_SOH_1'
    elif db == 'ygzhen' or db == 'zgtian':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_2', 'PCS_yougong_2', 'PCS_PF_2', 'PCS_jldy_2', 'PCS_jldl_2', 'PCS_zldy_2', 'PCS_zldl_2', 'PCS_T_2', 'PCS_Disg_2', 'PCS_Chag_2'
    elif db == 'baodian':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度（保电）内部温度,PCS日放电，PCS日充电
        return 'soc_3', 'PCS_yougong_3', 'PCS_PF_3', 'PCS_jldy_3', 'PCS_jldl_3', 'PCS_zldy_3', 'PCS_zldl_3', 'PCS_T_3', 'PCS_Disg_3', 'PCS_Chag_3'
    elif db == 'dongmu':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_4', 'PCS_yougong_4', 'PCS_PF_4', 'PCS_jldy_4', 'PCS_jldl_4', 'PCS_zldy_4', 'PCS_zldl_4', 'PCS_T_4', 'PCS_Disg_4', 'PCS_Chag_4'
    elif db == 'houma':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_5', 'PCS_yougong_5', 'PCS_PF_5', 'PCS_jldy_5', 'PCS_jldl_5', 'PCS_zldy_5', 'PCS_zldl_5', 'PCS_T_5', 'PCS_Disg_5', 'PCS_Chag_5'
    elif db == 'datong':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_6', 'PCS_yougong_6', 'PCS_PF_6', 'PCS_jldy_6', 'PCS_jldl_6', 'PCS_zldy_6', 'PCS_zldl_6', 'PCS_T_6', 'PCS_Disg_6', 'PCS_Chag_6'
    elif db == 'guizhou':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_7', 'PCS_yougong_7', 'PCS_PF_7', 'PCS_jldy_7', 'PCS_jldl_7', 'PCS_zldy_7', 'PCS_zldl_7', 'PCS_T_7', 'PCS_Disg_7', 'PCS_Chag_7'
    elif db == 'ygqn.abc':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_8', 'PCS_yougong_8', 'PCS_PF_8', 'PCS_jldy_8', 'PCS_jldl_8', 'PCS_zldy_8', 'PCS_zldl_8', 'PCS_T_8', 'PCS_Disg_8', 'PCS_Chag_8'
    elif db == 'ygqn.d':  # PCS_SOC,PCS有功功率,功率因数,PCS交流电压,PCS交流电流,PCS直流电压,PCS直流电流,PCS温度,PCS日放电，PCS日充电
        return 'soc_9', 'PCS_yougong_9', 'PCS_PF_9', 'PCS_jldy_9', 'PCS_jldl_9', 'PCS_zldy_9', 'PCS_zldl_9', 'PCS_T_9', 'PCS_Disg_9', 'PCS_Chag_9'

class SideForecaseFilename(BaseHandler):
    def __init__(self, ty):
        self.ty = ty
    def filename(self,imgs):  # 电站值的配置
        filename = imgs.get('filename')
        if len(filename)>75:
            return self.customError("文件名称过长，请返回修改！")
        filenames = filename.split('.')[0] + '--' + str(timeUtils.nowSecs()) + '.' + filename.split('.')[-1]
        return filenames
# if __name__ == '__main__':
#     text_ = "hello wangyanjie"
#     print(baidu_translation(text_,["<EMAIL>"],'auto1',"zh2323" ))
