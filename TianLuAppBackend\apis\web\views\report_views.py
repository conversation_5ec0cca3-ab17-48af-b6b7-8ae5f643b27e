import calendar
import datetime
import json
import threading
import time
import traceback

import math
import os
import concurrent.futures

import openpyxl
from dateutil.relativedelta import relativedelta
from django_redis import get_redis_connection
from rest_framework.generics import GenericAPIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView

from TianLuAppBackend import settings
from apis.user import models
from apis.web.async_tasks import save_RunningReportsView_to_minio
from apis.web.models import RunningReport
from apis.web.serializers import SelectRunningReportsSerializer
from apscheduler_tasks.report_tasks import get_day_data_for_station
from common import common_response_code
from middlewares.authentications import JwtParamAuthentication, J<PERSON>THeaderAuthentication, DenyAuthentication
from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
from LocaleTool.common import redis_pool
from tools.minio_tool import MinioTool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def get_month_start_end(year, month):
    first_day = datetime.datetime(year, month, 1)
    if month == 12:
        next_month_start = datetime.datetime(year + 1, 1, 1)
    else:
        next_month_start = datetime.datetime(year, month + 1, 1)
    last_day = next_month_start - datetime.timedelta(days=1)
    return first_day, last_day


def get_last_day_of_month(year, month):
    _, last_day = calendar.monthrange(year, month)
    last_day_date = datetime.datetime(year, month, last_day)
    return last_day_date


class StandardPageNumberPagination(PageNumberPagination):
    page_size_query_param = 'page_size'
    max_page_size = 10


class RunningReportsListView(APIView):
    """运行报告: 列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_queryset(self):
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        master_stations = user.master_stations.filter(is_delete=0).all()
        station_names = [station.name for station in master_stations]
        reports = RunningReport.objects.filter(station_name__in=station_names).all()
        return reports

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        today = datetime.date.today()
        year_month = today.strftime('%Y-%m')
        page = int(request.data.get('page', 1))
        page_size = int(request.data.get('page_size', 1000000000))

        report_type = request.data.get('report_type', 3)
        project_ids = eval(request.data.get('project_ids')) if request.data.get('project_ids') else []
        start_time = request.data.get('start_time', year_month)
        end_time = request.data.get('end_time', year_month)
        # 校验参数

        # if project_id and not re.match(r'\d*', project_id):
        #     return Response(
        #             {
        #                 "code": common_response_code.FIELD_ERROR,
        #                 "data": {"message": "error", "detail": f"项目ID格式不正确"},
        #             }
        #         )

        try:
            datetime.datetime.strptime(start_time, '%Y-%m')
            datetime.datetime.strptime(end_time, '%Y-%m')
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"请检查时间参数格式" if lang == 'zh' else 'Time format error.'},
                }
            )

        if datetime.datetime.strptime(start_time, '%Y-%m') > datetime.datetime.strptime(end_time, '%Y-%m'):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"请检查时间参数范围" if lang == 'zh' else 'Time range error.'},
                }
            )

        # 计算月份集合
        temp_months = list()
        target_month = datetime.datetime.strptime(start_time, '%Y-%m')
        end_month = datetime.datetime.strptime(end_time, '%Y-%m')
        while target_month <= end_month:
            temp_months.append(target_month.strftime('%Y-%m'))
            target_month = target_month + relativedelta(months=1)

        reports = self.get_queryset().filter(report_type=report_type, datetime__in=temp_months).order_by('datetime')

        if len(project_ids):
            for project_id in project_ids:
                try:
                    project = models.Project.objects.get(id=project_id)
                except Exception as e:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": f"项目ID: {project_id}不存在" if lang == 'zh' else 'Project ID: {project_id} does not exist.'},
                        }
                    )

            # stations = models.StationDetails.objects.filter(project_id__in=project_ids)
            master_stations = models.MaterStation.objects.filter(project_id__in=project_ids, is_delete=0).order_by('id')
            if not master_stations.exists():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"项目无并网点数据" if lang == 'zh' else 'No data.'},
                    }
                )
        else:
            master_stations = models.MaterStation.objects.filter(userdetails=user, is_delete=0).order_by('id')

        # 单独起1个线程缓存需要下载的数据
        user_id = self.request.user['user_id']
        s = threading.Thread(target=save_RunningReportsView_to_minio, args=(user_id, start_time, end_time, project_ids, lang))
        s.start()

        total_reports = []
        for master_station in master_stations:
            reports_s = reports.filter(station_name=master_station.name).all().order_by('datetime')
            if reports_s.exists():
                total_reports.extend(reports_s)

            # station_names = [station.name for station in master_stations]
            # reports = reports.filter(station_name__in=station_names)

        total_pages = math.ceil(len(total_reports) / page_size)
        start_index = (page-1) * page_size
        end_index = page * page_size if page < total_pages else len(total_reports) + 1
        reports_ = total_reports[start_index: end_index]

        ser = SelectRunningReportsSerializer(reports_, many=True)

        if lang != 'zh':
            for item in ser.data:
                item['analyse'] = item['en_analyse']

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    # "detail": sorted(ser.data, key=lambda x: x['datetime'], reverse=True),
                    "detail": ser.data,
                    "paginator_info": {
                        "page": page,
                        "page_size": page_size,
                        "pages": total_pages,
                        "total_count": len(total_reports)
                    }
                }
            }
        )


class ExportRunningReportsView(APIView):
    """运行报告: 导出"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_queryset(self):
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        # stations = user.stations.all()
        master_stations = user.master_stations.filter(is_delete=0).all()
        station_names = [station.name for station in master_stations]
        reports = RunningReport.objects.filter(station_name__in=station_names).all()
        return reports

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        project_ids = eval(request.data.get('project_ids')) if request.data.get('project_ids') else []
        report_type = 3

        today = datetime.date.today()

        start_time = request.data.get('start_time') if request.data.get('start_time') else today.strftime('%Y-%m')
        end_time = request.data.get('end_time') if request.data.get('end_time') else today.strftime('%Y-%m')

        try:
            datetime.datetime.strptime(start_time, '%Y-%m')
            datetime.datetime.strptime(end_time, '%Y-%m')
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"请检查时间参数格式" if lang == 'zh' else 'Time format error.'},
                }
            )

        if datetime.datetime.strptime(start_time, '%Y-%m') > datetime.datetime.strptime(end_time, '%Y-%m'):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"请检查时间参数范围" if lang == 'zh' else 'Time range error.'},
                }
            )

        user_id = request.user["user_id"]

        en_key = 'tianlu_' + f"{start_time}-{end_time}_Tianlu_Operation_Monthly_Report_{user_id}"

        # 优先从 redis 中取缓存数据
        key = f"tianlu_{start_time}-{end_time}天禄运行月报_{user_id}" if lang == 'zh' else en_key

        redis_conn = get_redis_connection("default")
        # 先从redis获取数据
        try:
            export_url = redis_conn.get(key)

            if export_url:
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {"message": f"success", "detail": export_url},
                    }
                )

        except Exception as e:
            print(traceback.print_exc())
            pass

        # 计算月份集合
        temp_months = list()
        target_month = datetime.datetime.strptime(start_time, '%Y-%m')
        end_month = datetime.datetime.strptime(end_time, '%Y-%m')
        while target_month <= end_month:
            temp_months.append(target_month.strftime('%Y-%m'))
            target_month = target_month + relativedelta(months=1)

        # 计算所选月份得开始日和结束日
        temp_days = list()
        first_day = datetime.datetime.strptime(start_time, '%Y-%m')
        end_year, end_month = end_time.split('-')
        end_day = get_last_day_of_month(int(end_year), int(end_month)).date() if get_last_day_of_month(
            int(end_year), int(end_month)).date() <= today else today

        target_day = first_day.date()
        while target_day <= end_day:
            temp_days.append(target_day.strftime('%Y-%m-%d'))
            target_day += datetime.timedelta(days=1)

        reports = self.get_queryset().filter(report_type=report_type, datetime__in=temp_months)

        if project_ids:
            for project_id in project_ids:
                try:
                    project = models.Project.objects.get(id=project_id)
                except Exception as e:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": f"项目ID: {project_id}不存在{e}" if lang == 'zh' else 'Project ID: ' + str(project_id) + ' does not exist.'},
                        }
                    )

            # stations = models.StationDetails.objects.filter(project_id__in=project_ids)
            master_stations = models.MaterStation.objects.filter(project_id__in=project_ids, is_delete=0)
            station_names = [station.name for station in master_stations]
            if not master_stations.exists():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"项目无并网点数据" if lang == 'zh' else
                                 'No station data in the project.'},
                    }
                )
        else:
            master_stations = models.MaterStation.objects.filter(userdetails=user, is_delete=0).order_by('id')

        total_reports = []
        for master_station in master_stations:
            reports_s = reports.filter(station_name=master_station.name).all().order_by('datetime')
            if reports_s.exists():
                total_reports.extend(reports_s)

        workbook = openpyxl.Workbook()

        time_str = str(int(time.time()))
        if lang == 'zh':
            file_name = f"{start_time}~{end_time}天禄运行月报_{time_str}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            t_station_names = list(set([r.station_name for r in total_reports]))              # 之前是 "in reports", 现在改为 "in total_reports" 了
            # t_stations = models.StationDetails.objects.filter(station_name__in=t_station_names)
            t_master_stations = models.MaterStation.objects.filter(name__in=t_station_names, is_delete=0)
            t_stations = models.StationDetails.objects.filter(master_station__in=t_master_stations, is_delete=0)

            table1_titles = ["开始日期", "结束日期", "并网点名称", "充电量(kWh)", "放电量(kWh)", "充放电效率(%)",
                             "充放电次数",
                             "尖时充电量(kWh)", "峰时充电量(kWh)", "平时充电量(kWh)", "谷时充电量(kWh)", "深谷时充电量(kWh)",
                             "尖时放电量(kWh)", "峰时放电量(kWh)", "平时放电量(kWh)", "谷时放电量(kWh)", "深谷时放电量(kWh)",
                             "基准充电量(kWh)", "基准充电量完成率（%）", "基准放电量(kWh)", "基准放电量完成率(%)",
                             "累计充电量(kWh)",
                             "累计放电量（kWh）", "累计充放电量效率(%)", "收益(元)", "故障告警数量", "分析内容"]

            table2_titles = ["日期", "并网点名称", "充电量(kWh)", "放电量(kWh)", "充放电效率(%)",
                             "基准充电量(kWh)", "基准充电量完成率（%）", "基准放电量(kWh)", "基准放电量完成率(%)",
                             "尖时充电量(kWh)", "峰时充电量(kWh)", "平时充电量(kWh)", "谷时充电量(kWh)", "深谷时充电量(kWh)",
                             "尖时放电量(kWh)", "峰时放电量(kWh)", "平时放电量(kWh)", "谷时放电量(kWh)", "深谷时放电量(kWh)",
                             "收益(元)", "故障告警数量"]

            table3_titles = ["序号", "项目名称", "设备名称", "报文", "告警分类", "开始时间", "结束时间"]

            sheet1 = workbook.active
            sheet1.title = '运行数据'

        else:
            file_name = f"{start_time}~{end_time}_Tianlu_Operation_Monthly_Report_{user_id}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            t_station_names = list(set([r.station_name for r in total_reports]))
            # t_stations = models.StationDetails.objects.filter(station_name__in=t_station_names)
            t_master_stations = models.MaterStation.objects.filter(name__in=t_station_names, is_delete=0)
            t_stations = models.StationDetails.objects.filter(is_delete=0, master_station__in=t_master_stations)

            table1_titles = ["From", "To", "Installation", "Energy Charged(kWh)", "Energy Discharged(kWh)",
                             "Efficiency(%)",
                             "Energy Charge and Discharge Cycles",
                             "Peak-hour Energy Charged(kWh)", "Shoulder-hour Energy Charged(kWh)",
                             "Off-peak-hour Energy Charged(kWh)", "Valley-hour Energy Charged(kWh)",
                             "Deep Valley-hour Energy Charged(kWh)",
                             "Peak-hour Energy Discharged(kWh)", "Shoulder-hour Energy Discharged(kWh)",
                             "Off-peak-hour Energy Discharged(kWh)", "Valley-hour Energy Discharged(kWh)",
                             "Deep Valley-hour Energy Discharged(kWh)",
                             "Reference Energy Charged(kWh)", "Energy Charge Completion Rate(%)",
                             "Reference Energy Charged(kWh)", "Reference Energy Disharged Completion Rate(%)",
                             "Cumulative Energy Charged(kWh)",
                             "Cumulative Energy Discharged(kWh)", "General Efficiency(%)", "Profit(Yuan)",
                             "Number of Fault Alarms", "Analysis"]

            table2_titles = ["Date", "Installation", "Energy Charged(kWh)", "Energy Discharged(kWh)", "Efficiency(%)",
                             "Reference Energy Charged(kWh)", "Reference Energy Charge Completion Rate（%）",
                             "Reference Energy Charged(kWh)", "Reference Energy Disharged Completion Rate(%)",
                             "Peak-hour Energy Charged(kWh)", "Shoulder-hour Energy Charged(kWh)",
                             "Off-peak-hour Energy Charged(kWh)", "Valley-hour Energy Charged(kWh)",
                             "Deep Valley-hour Energy Charged(kWh)",
                             "Peak-hour Energy Discharged(kWh)", "Shoulder-hour Energy Discharged(kWh)",
                             "Off-peak-hour Energy Discharged(kWh)", "Valley-hour Energy Discharged(kWh)",
                             "Deep Valley-hour Energy Discharged(kWh)",
                             "Profit(Yuan)", "Number of Fault Alarms"]

            table3_titles = ["Number", "Project", "Device", "Message", "Alarm Types", "From", "To"]

            sheet1 = workbook.active
            sheet1.title = 'Operation Data'

        # sheet1 写入标头
        for col_num, header in enumerate(table1_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet1[f'{col_letter}1'] = header

        # 准备数据
        data_array = list()
        if total_reports:
            for t in total_reports:
                year_month = t.datetime.split('-')
                year_, month_ = int(year_month[0]), int(year_month[1])
                first_day_, last_day_ = get_month_start_end(year_, month_)

                li = [first_day_.strftime('%Y-%m-%d'), last_day_.strftime('%Y-%m-%d'), t.station_name, t.charge_cap,
                      t.discharge_cap, t.comp_rate, t.count,
                      t.spike_charge, t.peak_charge, t.flat_charge, t.valley_charge, t.dvalley_charge, t.spike_discharge, t.peak_discharge,
                      t.flat_discharge, t.valley_discharge, t.dvalley_discharge, t.theory_charge, t.theory_charge_comp_rate, t.theory_discharge,
                      t.theory_discharge_comp_rate, t.accu_charge, t.accu_discharge, t.effic, t.income, t.alarm+t.fault,
                      t.analyse
                      ]
                data_array.append(li)
            sorted(data_array, key=lambda x: x[0])

            # 写入工作表
            for row_num, row_data in enumerate(data_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet1[f'{col_letter}{row_num}'] = cell_value

        if lang == 'zh':
            sheet2 = workbook.create_sheet("逐日运行数据")
        else:
            sheet2 = workbook.create_sheet('Daily Operation Data')

        # sheet2 写入标头
        for col_num, header in enumerate(table2_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet2[f'{col_letter}1'] = header
        # 准备表2数据
        reports_days = RunningReport.objects.filter(report_type=1, station_name__in=t_station_names, datetime__in=temp_days).all()
        days_data_array = list()
        for master_station in master_stations:
            reports_day_s = reports_days.filter(station_name=master_station.name).all().order_by('datetime')
            if reports_day_s.exists():
                for t in reports_day_s:
                    li = [t.datetime, t.station_name, t.charge_cap, t.discharge_cap, t.comp_rate, t.theory_charge,
                        t.theory_charge_comp_rate,
                        t.theory_discharge, t.theory_discharge_comp_rate, t.spike_charge, t.peak_charge, t.flat_charge,
                        t.valley_charge, t.dvalley_charge, t.spike_discharge, t.peak_discharge,
                          t.flat_discharge, t.valley_discharge, t.dvalley_discharge, t.income, t.alarm+t.fault]
                    days_data_array.append(li)

        # sorted(days_data_array, key=lambda x: x[0])
        for row_num, row_data in enumerate(days_data_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet2[f'{col_letter}{row_num}'] = cell_value

        if lang == 'zh':
            sheet3 = workbook.create_sheet("故障告警记录")
        else:
            sheet3 = workbook.create_sheet('Fault Alarm Record')

        # sheet3 写入标头
        for col_num, header in enumerate(table3_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet3[f'{col_letter}1'] = header

        # start_time = first_day.replace(hour=0, minute=0, second=0)
        # print(end_day, type(end_day))
        # print(first_day, type(first_day))
        # end_time = end_day.replace(hour=23, minute=59, second=59)
        end_time_ = datetime.datetime.combine(end_day, datetime.datetime.max.time())

        stations_ids = [s.id for s in t_stations]
        filters = {
            "start_time__gte": first_day,
            "start_time__lte": end_time_,
            "station_id__in": stations_ids,
            "type__in": [1, 2]
        }

        alarm_array = list()
        alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
                                                                             "type", "start_time",
                                                                             "end_time", "details",
                                                                             "id",
                                                                             "note", "device",
                                                                             "station_id"
                                                                             ).order_by("-start_time").all()
        if alarm_instances.exists():
            for ind, ins in enumerate(alarm_instances):
                # ins["details"] = ins["device"] + ":" + ins["details"]
                ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')

                if lang == 'zh':
                    ins['type'] = "故障" if ins['type'] == 1 else '报警'
                else:
                    ins['type'] = "Fault" if ins['type'] == 1 else 'Alarm'

                if ins["end_time"]:
                    ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                else:
                    if lang == 'zh':
                        ins["end_time"] = "未恢复故障" if ins['type'] == 1 else "未恢复报警"
                    else:
                        ins["end_time"] = "Not recovered Fault" if ins['type'] == 1 else "Not recovered Alarm"
                try:
                    ins['station_name'] = models.StationDetails.objects.get(id=ins['station_id']).station_name
                except Exception as e:
                    pass
                li = [ind+1, ins['station_name'], ins['device'], ALARM_ZH_EN_MAP[ins['details'].strip()][lang], ins['type'], ins['start_time'], ins['end_time']]
                alarm_array.append(li)

            sorted(alarm_array, key=lambda x: x[5])
            for row_num, row_data in enumerate(alarm_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet3[f'{col_letter}{row_num}'] = cell_value

        workbook.save(path)

        # 上传至minio
        try:
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            url = minio_client.upload_local_file(file_name, path, bucket_name='download')
            os.remove(path)

            # 缓存下载链接到redis
            # key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
            redis_conn = get_redis_connection("default")
            redis_conn.set(key, url, 60 * 5)

        except Exception as e:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": f"上传文件报错：{e}" if lang == 'zh' else 'Upload file error.'},
            })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": url,
                },
            }
        )


class RunningReportDetailView(APIView):
    """运行报告: 详情"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_queryset(self):
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        master_stations = user.master_stations.filter(is_delete=0).all()
        station_names = [station.name for station in master_stations]
        reports = RunningReport.objects.filter(station_name__in=station_names).all()
        return reports

    def get(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        report = RunningReport.objects.get(id=pk)
        ser = SelectRunningReportsSerializer(report)

        # if not ser.is_valid():
        #     error_log.error(f"运行报告:字段校验不通过 =>{ser.errors}")
        #     return Response(
        #         {
        #             "code": common_response_code.FIELD_ERROR,
        #             "data": {"message": "error", "detail": ser.errors},
        #         }
        #     )
        # project_name = ser.validated_data.get('project')
        # system_level = ser.validated_data.get("system_level")
        # target = ser.validated_data["target"]
        # start_day = ser.validated_data.get("start_time")
        # end_day = ser.validated_data.get("end_time")

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ser.data,
                },
            }
        )

    def post(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        try:
            report = RunningReport.objects.get(id=pk)
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:ID 不存在'},
                }
            )

        note = request.data.get('note', None)
        report.analyse = note
        report.en_analyse = note
        report.save()

        # 异步翻译
        pdr_data = {'id': report.id,
                    'table': 't_running_report',
                    'update_data': {'analyse': note}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": '运行月报： 分析写入成功' if lang == 'zh' else
                    'Running monthly report: Analysis written successfully.',
                },
            }
        )


class RunningReportChargeDischargeListView(GenericAPIView):
    """运行报告: 逐时or逐日充放电量"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    # queryset = RunningReport.objects.all()
    # serializer_class = RunningReportSerializer
    pagination_class = StandardPageNumberPagination
    lookup_field = 'id'

    def get_queryset(self):
        user = models.UserDetails.objects.get(id=self.request.user['user_id'])
        master_stations = user.master_stations.filter(is_delete=0).all()
        station_names = [station.name for station in master_stations]
        reports = RunningReport.objects.filter(station_name__in=station_names).all()
        return reports

    def get(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        key = request.query_params.get('key', 'all')

        try:
            report = RunningReport.objects.get(id=pk)
        except Exception as e:
            error_log.error(f"运行报告:参数错误 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:参数错误' if lang == 'zh' else 'Parameter error.'},
                }
            )

        station_name = report.station_name
        # station = models.StationDetails.objects.get(station_name=station_name)
        master_station = models.MaterStation.objects.get(name=station_name, is_delete=0)

        charge_list = list()
        discharge_list = list()
        datetime_list = list()
        dis_charge_effic_list = list()

        soc_list = list()
        type_list = list()

        if report.report_type == 1:
            pass

        elif report.report_type == 2:
            pass
        else:
            year_month = report.datetime.split('-')
            year_, month_ = int(year_month[0]), int(year_month[1])
            first_day, last_day = get_month_start_end(year_, month_)
            date_month = first_day.strftime('%Y-%m')

            temp_list = []

            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                futures = list()
                target_day = first_day
                while target_day <= last_day:

                    future = executor.submit(get_day_data_for_station, master_station, target_day.date())
                    futures.append(future)

                    target_day = target_day + datetime.timedelta(days=1)

                for f in concurrent.futures.as_completed(futures):
                    res_ = f.result()
                    if res_:
                        temp_list.append(res_)

                temp_list = sorted(temp_list, key=lambda x: x['datetime'])

                for res in temp_list:
                    day_charge_cap = round(res['charge_cap'], 2)
                    day_discharge_cap = round(res['discharge_cap'], 2)
                    dis_charge_effic = res['effic']
                    charge_list.append(day_charge_cap)
                    discharge_list.append(day_discharge_cap)
                    dis_charge_effic_list.append(dis_charge_effic)
                    datetime_list.append(res['datetime'])

            detail = {
                "title": station_name + '-逐日充放电量' if lang == 'zh' else ' Daily Energy Charge and Discharge',
                "datetime": date_month,
                "charge_list": charge_list,
                "discharge_list": discharge_list,
                "dis_charge_effic_list": dis_charge_effic_list,
                "datetime_list": datetime_list

            }
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": detail,
                    },
                }
            )


class RunningReportChargeDischargeCountListView(GenericAPIView):
    """运行报告: 充放电量统计"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    # queryset = RunningReport.objects.all()
    # serializer_class = RunningReportSerializer
    # pagination_class = StandardPageNumberPagination
    # lookup_field = 'id'
    #
    # def get_queryset(self):
    #     user = models.UserDetails.objects.get(id=self.request.user['user_id'])
    #     stations = user.stations.all()
    #     station_names = [station.station_name for station in stations]
    #     reports = RunningReport.objects.filter(station_name__in=station_names).all()
    #     return reports

    def get(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        # key = request.query_params.get('key', 'all')

        try:
            report = RunningReport.objects.get(id=pk)
        except Exception as e:
            error_log.error(f"运行报告:参数错误 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:参数错误' if lang == 'zh' else 'Parameter error.'},
                }
            )

        station_name = report.station_name
        # station = models.StationDetails.objects.get(station_name=station_name)
        # master_station = models.MaterStation.objects.get(name=station_name)

        if report.report_type == 2:
            datetime_ = report.datetime + '~' + report.datetime_end
        else:
            datetime_ = report.datetime

        detail = {
            "title": station_name + '-充放电量统计' if lang == 'zh' else ' Energy Charge and Discharge Statistics',
            "datetime": datetime_,
            "unit": "kWh",
            "spike": {
                "charge": report.spike_charge,
                "discharge": report.spike_discharge
            },
            "peak": {
                "charge": report.peak_charge,
                "discharge": report.peak_discharge
            },
            "flat": {
                "charge": report.flat_charge,
                "discharge": report.flat_discharge
            },
            "valley": {
                "charge": report.valley_charge,
                "discharge": report.valley_discharge
            },
            "dvalley": {
                "charge": report.dvalley_charge,
                "discharge": report.dvalley_discharge
            }
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail,
                },
            }
        )


class RunningReportSOCbyDayView(GenericAPIView):
    """运行报告: 日报告 SOC: 废弃"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    # queryset = RunningReport.objects.all()
    # serializer_class = RunningReportSerializer
    # pagination_class = StandardPageNumberPagination
    # lookup_field = 'id'
    #
    # def get_queryset(self):
    #     user = models.UserDetails.objects.get(id=self.request.user['user_id'])
    #     stations = user.stations.all()
    #     station_names = [station.station_name for station in stations]
    #     reports = RunningReport.objects.filter(station_name__in=station_names).all()
    #     return reports

    def get(self, request, pk):
        try:
            report = RunningReport.objects.get(id=pk)
        except Exception as e:
            error_log.error(f"运行报告:参数错误 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:参数错误'},
                }
            )

        station_name = report.station_name
        station = models.StationDetails.objects.get(station_name=station_name)

        datetime_list = list()
        soc_list = list()

        if report.report_type != 1:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:只有日报可查看 SOC!'},
                }
            )

        date = report.datetime
        target_day = datetime.datetime.strptime(date, '%Y-%m-%d')
        res = get_day_data_for_station(station, target_day)
        if res:
            datetime_list = res['day_time_list']
            soc_list = res['day_soc_list']

        detail = {
            "title": station_name + '-SOC',
            "datetime": date,
            "soc_list": soc_list,
            "datetime_list": datetime_list
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail,
                },
            }
        )


class RunningReportFaultAlartView(GenericAPIView):
    """运行报告: 故障、告警记录"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        try:
            report = RunningReport.objects.get(id=pk)
        except Exception as e:
            error_log.error(f"运行报告:参数错误 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:参数错误' if lang == 'zh' else 'Parameter error.'},
                }
            )

        type_ = int(request.query_params.get('type', 0))    # 0: 故障告警， 1： 故障  2： 告警
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 1000000000))

        station_name = report.station_name
        # station = models.StationDetails.objects.get(station_name=station_name)
        # master_station = models.MaterStation.objects.get(name=station_name)
        # slave_stations = master_station.stationdetails_set.all()
        master_station = models.MaterStation.objects.get(name=station_name,is_delete=0)
        slave_stations = models.StationDetails.objects.filter(master_station=master_station.id,is_delete=0).all()
        slave_stations_id = [item.id for item in slave_stations]


        date_str = report.datetime
        report_type = report.report_type

        start = (page - 1) * page_size
        end = start + page_size

        if report_type == 1:
            start_time = date_str + ' 00:00:00'
            end_time = date_str + ' 23:59:59'

        elif report_type == 2:
            first_day = report.datetime
            last_day = report.datetime_end
            date_str = first_day + '~' + last_day
            first_day_ = datetime.datetime.strptime(first_day, '%Y-%m-%d')
            last_day_ = datetime.datetime.strptime(last_day, '%Y-%m-%d')

            start_time = first_day_.replace(hour=0, minute=0, second=0)
            end_time = last_day_.replace(hour=23, minute=59, second=59)

        else:
            year_month = report.datetime.split('-')
            year_, month_ = int(year_month[0]), int(year_month[1])
            first_day, last_day = get_month_start_end(year_, month_)
            start_time = first_day.replace(hour=0, minute=0, second=0)
            end_time = last_day.replace(hour=23, minute=59, second=59)

        filters = {
            # "station__in": slave_stations,
            "station_id__in": slave_stations_id,
            "start_time__gte": start_time,
            "start_time__lte": end_time
        }

        if type_ == 1:
            filters['type'] = 1
        elif type_ == 2:
            filters['type'] = 2
        elif type_ == 0:
            filters['type__in'] = [1, 2]
        total_fault_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
                                                                                         "type", "start_time",
                                                                                         "end_time", "details",
                                                                                         "id", "joint_primary_key",
                                                                                         "note", "device",
                                                                                         "station_id"
                                                                                         ).order_by("-start_time")

        if total_fault_alarm_instances:
            total_pages = math.ceil(len(total_fault_alarm_instances) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(total_fault_alarm_instances) + 1
            total_fault_alarm_instances_ = total_fault_alarm_instances[start_index: end_index]

            for ins in total_fault_alarm_instances_:
                station = models.StationDetails.objects.filter(id=ins['station_id']).first()
                ins["details"] = ins["device"] + ":" + ALARM_ZH_EN_MAP[ins["details"].strip()][lang]
                ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
                if ins["end_time"]:
                    ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                ins["note"] = ALARM_ZH_EN_MAP[ins['note'].strip().replace(' ', '')][lang]
                ins['id'] = ins['joint_primary_key']

                try:
                    ins['station_name'] = station.station_name if station else '--'
                except Exception as e:
                    pass

            detail = {
                "title": station_name + '-故障告警记录' if lang == 'zh' else ' Fault Alarm Record',
                "datetime": date_str,
                "alarms": sorted(total_fault_alarm_instances_, key=lambda x: x['start_time']),
            }

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": detail,
                        "paginator_info": {
                            "page": page,
                            "page_size": page_size,
                            "pages": total_pages,
                            "total_count": len(total_fault_alarm_instances)
                        }
                    },
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": [],
                        "paginator_info": {
                            "page": page,
                            "page_size": page_size,
                            "pages": 0,
                            "total_count": 0
                        }
                    },
                }
            )
