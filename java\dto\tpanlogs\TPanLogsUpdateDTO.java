package com.robestec.analysis.dto.tpanlogs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 下发日志记录更新DTO
 */
@Data
@ApiModel("下发日志记录更新DTO")
public class TPanLogsUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "电站名称", required = true)
    @NotBlank(message = "电站名称不能为空")
    private String station;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "类型名称", required = true)
    @NotBlank(message = "类型名称不能为空")
    private String typeName;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "状态: 1-成功, 2-失败", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
