#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:26:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\sysinfos_save.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-06-08 16:21:39

'''
获取linux自身信息保存至数据库中
'''
import sys,os
from Tools.Utils.num_utils import num_retain
from Tools.Utils.time_utils import timeUtils 
import commands
import psutil
from sysinfos_log import app_log
from Tools.DB.mysql_user import user_session
from Tools.DB.redis_con import r
from Application.Models.User.sys_info import SysInfo
from Application.Models.User.sys_info_r import SysInfosR
reload(sys)
sys.setdefaultencoding("utf-8")


basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)

from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()


all0,all1 = [],[]  # 不刷新和刷新
def getSysInfos():
    '''查询所有监控内容'''
    try:
        if not all0:
            infos = user_session.query(SysInfo).all()  # 所有系统监控配置
            for info in infos:
                if int(info.refresh) == 0:  # 不刷新的值
                    all0.append(info)
                else:
                    all1.append(info) 
                # 初次加载都执行
                name = info.name
                val = eval(info.cmd)
                r.hset('sysinfos',name,val)
                user_session.add(SysInfosR(name=name,value=val,op_ts=timeUtils.getNewTimeStr()))
            user_session.commit()
        return all0,all1

    except Exception as E:
        app_log.error(E)
        user_session.rollback()
     


def RunClearFileAndData():
    scheduler.add_job(calculation, 'interval', seconds=5*60)  # 5分钟执行一次
    # scheduler.add_job(fault, 'interval', seconds=60*15)  # 每 15分钟执行一次

    # scheduler.add_job(run_database, 'cron',  hour=1)  # 每天1点执行
    scheduler.start()


def calculation():
    '''
    只计算需要刷新的值
    '''
    try:
        tim = timeUtils.getNewTimeStr()
        for o in all1:
            name = o.name
            val = eval(o.cmd)
            user_session.merge(SysInfosR(name=name,value=val,op_ts=tim))
            r.hset('sysinfos',name,val)
        user_session.commit()
    except Exception as E:
        app_log.error(E)
        user_session.rollback()
  


if __name__ == '__main__':
    getSysInfos()
    RunClearFileAndData()
   
    



