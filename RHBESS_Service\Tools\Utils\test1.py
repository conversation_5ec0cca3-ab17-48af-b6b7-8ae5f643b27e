#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-08 14:48:01
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\test1.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-10 09:58:18

import requests
import random
import json
from Tools.DataEnDe.MD5 import MD5Tool

# appid = '20240109001935547' 
# appkey = '80Mn4bYV881ZUFwzQhI1'

# 公司账号
appid = '20240109001935690' 
appkey = '0ORDSzOBSpmyLpkXKbWk'

from_lang = 'auto'  # 原始语言自动
to_lang =  'en'  # 目标语言  zh:中文 ；en：英文
# url = "http://api.fanyi.baidu.com/api/trans/vip/translate"
url = "https://fanyi-api.baidu.com/api/trans/vip/translate"

# def make_md5(s, encoding='utf-8'):
#     return md5(s.encode(encoding)).hexdigest()


def baidu_api(query,from_lang,to_lang):
    result = ''
    try:
        salt = random.randint(32768, 65536)  # 生产随机数
        sign = MD5Tool.get_str_md5(appid + query + str(salt) + appkey)  # 签名加密 固定顺序

        # Build request
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = {'appid': appid, 'q': query, 'from': from_lang, 'to': to_lang, 'salt': salt, 'sign': sign}

        r = requests.post(url, params=payload, headers=headers)
        result = r.json()

        # Show response
        # print(json.dumps(result, indent=4, ensure_ascii=False),'************')
        return result["trans_result"][0]['dst']
    except Exception as E:
        print (result,'send email to ')
    



if __name__ == '__main__':
    text = "hello wangyanjie"
    print(baidu_api(text,from_lang, to_lang))
    
