import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

public class MapToEntityConverter {
    
    /**
     * 将Map转换为实体类对象
     * @param map 源数据Map
     * @param clazz 目标实体类Class
     * @param <T> 实体类泛型
     * @return 实体类对象
     */
    public static <T> T convert(Map<String, Object> map, Class<T> clazz) {
        if (map == null || clazz == null) {
            return null;
        }
        
        try {
            // 创建实体类实例
            T entity = clazz.newInstance();
            
            // 获取所有字段
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                String fieldName = field.getName();
                // 如果Map中包含该字段
                if (map.containsKey(fieldName)) {
                    Object value = map.get(fieldName);
                    if (value != null) {
                        // 获取setter方法名
                        String setterName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        // 获取setter方法
                        Method setter = clazz.getMethod(setterName, field.getType());
                        // 调用setter方法设置值
                        setter.invoke(entity, value);
                    }
                }
            }
            
            return entity;
        } catch (Exception e) {
            throw new RuntimeException("Convert map to entity failed", e);
        }
    }

    /**
     * 将实体类对象转换为Map
     * @param entity 实体类对象
     * @return Map对象
     */
    public static Map<String, Object> convertToMap(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            Map<String, Object> map = new java.util.HashMap<>();
            Class<?> clazz = entity.getClass();
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                String fieldName = field.getName();
                // 获取getter方法名
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                // 获取getter方法
                Method getter = clazz.getMethod(getterName);
                // 调用getter方法获取值
                Object value = getter.invoke(entity);
                map.put(fieldName, value);
            }
            
            return map;
        } catch (Exception e) {
            throw new RuntimeException("Convert entity to map failed", e);
        }
    }
} 