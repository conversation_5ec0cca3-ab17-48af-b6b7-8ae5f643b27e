#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:43:30
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\His\r_ACDMS.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-05-20 15:37:57

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_his import his_Base,his_session
from Tools.DB.mysql_his import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

from sqlalchemy.ext.declarative import declarative_base

def HisACDMS_1(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类
 
    class table_model(Model):
        __tablename__ = _BOOKNAME
 
        name = Column(String(256), nullable=False,primary_key=True,comment=u"名称")
        value = Column(Integer, nullable=False, comment=u"")
        dts_s = Column(DateTime, nullable=False,comment=u"时间")
        dts_ms = Column(Integer, nullable=False,primary_key=True,comment=u"毫秒")
        ots = Column(DateTime, nullable=False,comment=u"时间")
        cause = Column(Integer, nullable=False,comment=u"原因")
        descr = Column(String(256), nullable=False,comment=u"描述")
        device_id = Column(Integer, nullable=False,comment=u"设备id")
        unit = Column(String(256), nullable=False,comment=u"单位")
        status_id = Column(Integer, nullable=False,comment=u"所属状态量id")

    return table_model

# class HisACDMS(Base):
#     ''' A:Action,C:Cumulant,D:Discrete,M:measure,S:status '''
#     u'权限静态配置表'
#     # @declared_attr
#     # def __tablename__(cls):
#     #     return cls.__name__.lower()
#     __tablename__ = "r_measure202204"
#     __table_args__ = {"extend_existing": True}
#     # id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
#     name = Column(String(256), nullable=False,primary_key=True,comment=u"名称")
#     value = Column(Integer, nullable=False, comment=u"")
#     dts_s = Column(Integer, nullable=False,primary_key=True,comment=u"绝对秒")
#     dts_ms = Column(Integer, nullable=False,primary_key=True,comment=u"毫秒")
#     ots = Column(DateTime, nullable=False,comment=u"时间")
#     cause = Column(Integer, nullable=False,comment=u"原因")
   

#     @classmethod
#     def init(cls):
#         his_Base.metadata.create_all()
        

#     def __repr__(self):
#         return "{'name':'%s','value':'%s','dts_s':%s,'dts_ms':'%s','ots':'%s','cause':'%s'}" % (self.name,self.value,self.dts_s,self.dts_ms,self.ots,self.cause)

