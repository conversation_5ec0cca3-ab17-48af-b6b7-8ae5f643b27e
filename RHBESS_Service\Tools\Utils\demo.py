#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-04-11 18:30:48
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\demo.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-06-28 10:38:57



# encoding: utf-8
from xhtml2pdf import pisa
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from xhtml2pdf.default import DEFAULT_FONT

pdfmetrics.registerFont(TTFont('yh', '../../Application/Static/msyh.ttf'))
DEFAULT_FONT['helvetica'] = 'yh'
sourceHtml = """<html><body>
<p>To <br>

PDF 中文测试
danyuanceshi<br>
以下是中文

</p></body></html>"""
outputFilename = "test.pdf"


# Utility function
def convertHtmlToPdf(sourceHtml, outputFilename):
    # open output file for writing (truncated binary)
    resultFile = open(outputFilename, "w+b")
    # convert HTML to PDF
    pisaStatus = pisa.CreatePDF(
        sourceHtml,  # the HTML to convert
        dest=resultFile)  # file handle to recieve result
    # close output file
    resultFile.close()  # close output file
    # return True on success and False on errors
    return pisaStatus.err


# Main program
if __name__ == "__main__":
    import requests
    # pisa.showLogging()
    # convertHtmlToPdf(sourceHtml, outputFilename)
    # startT = '2023-12-25 12:23:34'
    # endT = '2024-03-16 10:05:04'
    # noT = '2024-01-16 10:05:04'
    # print (noT>endT ,noT>startT )
    # print (noT>startT and noT<endT)
    url = "http://47.92.125.179:10090/SAVE"
    enc_text="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"
    payload = {
            "data": enc_text,
            "flag": "新能源总实时出力"
        }
    headers = {"content-type": "application/json"}
    try:
        response = requests.post(url, json=payload, headers=headers)
        print(response.text,'-------------')
        if response.json()["Code"] == 200:
            pass
    except Exception as e:
        print(e)
        pass
