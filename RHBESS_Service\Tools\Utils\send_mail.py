#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-18 10:35:25
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\Utils\send_mail.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-30 11:43:32

from smtplib import SMTP_SSL
from email.mime.text import MIMEText

def sendMail_(message,Subject,sender_show,recipient_show,to_addrs,cc_show=''):
    '''
    使用个人163邮箱发送
    :param message: str 邮件内容 
    :param Subject: str 邮件主题描述
    :param sender_show: str 发件人显示，不起实际作用如："xxx"
    :param recipient_show: str 收件人显示，不起实际作用 多个收件人用','隔开如："xxx,xxxx"
    :param to_addrs: str 实际收件人
    :param cc_show: str 抄送人显示，不起实际作用，多个抄送人用','隔开如："xxx,xxxx"
    '''
    # 填写真实的发邮件服务器用户名、密码（个人）
    # user = '<EMAIL>'
    # password = 'VHBDAYGOICEZFMTJ'
    # 使用公司邮箱发送
    user = '<EMAIL>'
    password = 'ty9WdTLu8Nw6BzDa'


    # 邮件内容
    msg = MIMEText(message, 'plain', _charset="utf-8")
    # 邮件主题描述
    msg["Subject"] = Subject
    # 发件人显示，不起实际作用
    msg["from"] = sender_show
    # 收件人显示，不起实际作用
    msg["to"] = recipient_show
    # 抄送人显示，不起实际作用
    msg["Cc"] = cc_show
    # 个人账户发送
    # smtp = SMTP_SSL(host="smtp.163.com",port=465)
    # # 连接到服务器
    # smtp.connect("smtp.163.com",465)

    # 公司邮箱发送
    smtp = SMTP_SSL(host="smtphz.qiye.163.com",port=465)
    smtp.connect("smtphz.qiye.163.com",465)

    # 登录发邮件服务器
    smtp.login(user, password)
    # 实际发送、接收邮件配置
    smtp.sendmail(from_addr = user, to_addrs=to_addrs, msg=msg.as_string())
    smtp.quit()

# message = 'Python 测试邮件...'
# Subject = '电站告警'
# # 显示发送人
# sender_show = 'rhbess'
# # 显示收件人
# recipient_show = 'xxx'
# # 实际发给的收件人
# to_addrs = ['<EMAIL>','<EMAIL>']
# print 'start^^^………………'
# sendMail_(message,Subject,sender_show,recipient_show,to_addrs)
# print 'SUCCESS'

