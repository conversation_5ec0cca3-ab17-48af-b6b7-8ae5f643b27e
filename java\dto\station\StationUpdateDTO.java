package com.robestec.analysis.dto.station;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 电站更新DTO
 */
@Data
@ApiModel("电站更新DTO")
public class StationUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "英文名称", required = true)
    @NotBlank(message = "英文名称不能为空")
    private String name;

    @ApiModelProperty(value = "中文名称", required = true)
    @NotBlank(message = "中文名称不能为空")
    private String descr;

    @ApiModelProperty(value = "是否注册: 1-是, 0-否")
    private Integer register;

    @ApiModelProperty(value = "索引")
    private Integer index;

    @ApiModelProperty(value = "操作时间戳")
    private LocalDateTime opTs;
}
