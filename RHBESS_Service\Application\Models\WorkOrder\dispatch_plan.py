#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-02-07 09:37:38
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WorkOrder\dispatch_plan.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-10 16:08:13



from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.dispatch_type import DispatchType
from Application.Models.User.user import User
from Application.Models.User.organization import Organization
from Application.Models.User.station import Station

class DispatchPlan(user_Base):
    u'计划工单记录信息'
    __tablename__ = "t_dispatch_plan"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    dispatch_type = Column(Integer,ForeignKey("t_dispatch_type.id"), nullable=False,comment=u"模板类型id")
    start_time = Column(DateTime, nullable=False,comment=u"开始时间 YYYY-mm-dd")
    end_time = Column(DateTime, nullable=False,comment=u"结束时间 YYYY-mm-dd")
    examine_time = Column(String(10), nullable=False,comment=u"检查时间，HH:MM")
    plan_time = Column(String(10), nullable=False,comment=u"每次巡检持续时长，天为单位")
    create_user = Column(Integer, nullable=False, comment=u"创建者")
    content = Column(String(256), nullable=True, comment=u"设定内容")
    handle_user = Column(Integer, nullable=False, comment=u"执行人")
    copy_users = Column(String(256), nullable=True, comment=u"抄送人,[]")
    station = Column(String(256), nullable=False, comment=u"所属站")
    first_apply_user = Column(Integer, nullable=False, comment=u"准备工作第一个审核人")
    second_apply_user = Column(Integer, nullable=False, comment=u"准备工作第二个审核人")
    finall_apply_user = Column(Integer, nullable=False, comment=u"工作总结审核人")
    device = Column(String(256), nullable=False, comment=u"计划任务的设备名称")
    repet = Column(Integer, nullable=False, comment=u"是否重复，0不重复1按天重复2按周重复3按月重复4按年重复")
    repet_interval = Column(Integer, nullable=True, comment=u"间隔，整数")
    repet_week = Column(String(20), nullable=True, comment=u"间隔周使用，每逢周几执行")
    is_execute = Column(Integer, nullable=False,server_default='0',comment=u"是否已执行，1是0否")
    next_time = Column(DateTime, nullable=True,comment=u"下次执行时间 YYYY-mm-dd HH:MM")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    organization_id = Column(Integer, nullable=False,comment=u"所属组织id")

    en_content = Column(String(256), nullable=True, comment=u"设定内容")
    en_device = Column(String(256), nullable=True, comment=u"计划任务的设备名称")
    
    plan_type= relationship("DispatchType", backref="plan_type")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        CU = user_session.query(User).filter(User.id==self.create_user,User.unregister==1).first()
        HU = user_session.query(User).filter(User.id==self.handle_user,User.unregister==1).first()

        FU = user_session.query(User).filter(User.id==self.first_apply_user,User.unregister==1).first()
        SU = user_session.query(User).filter(User.id==self.second_apply_user,User.unregister==1).first()
        EU = user_session.query(User).filter(User.id==self.finall_apply_user,User.unregister==1).first()
        
        CUS = user_session.query(User).filter(User.id.in_(eval(str(self.copy_users))),User.unregister==1).all()
        sta = user_session.query(Station).filter(Station.name==self.station).first()
        station = sta.descr if sta else ''
       
        create_descr = CU.name if CU else ''
        handle_descr = HU.name if HU else ''
        first_apply_descr = FU.name if FU else ''
        second_apply_descr = SU.name if SU else ''
        finall_apply_descr = EU.name if EU else ''
        plan_descr = self.plan_type.descr if self.plan_type else ''

        en_create_descr = CU.en_name if CU else ''
        en_handle_descr = HU.en_name if HU else ''
        en_first_apply_descr = FU.en_name if FU else ''
        en_second_apply_descr = SU.en_name if SU else ''
        en_finall_apply_descr = EU.en_name if EU else ''
        en_station = sta.en_descr if sta else ''

        copy_descr = []
        en_copy_descr = []
        for u in CUS:
            copy_descr.append(u.name)
            en_copy_descr.append(u.en_name)
        repet_descr = ''
        en_repet_descr = ''
        if self.repet == 0:
            repet_descr = '不重复'
            en_repet_descr = 'Without repetition'
        elif self.repet == 1:
            repet_descr = '按天重复'
            en_repet_descr = 'Daily repetition'
        elif self.repet == 2:
            repet_descr = '按周重复'
            en_repet_descr = 'Cyclic repetition'
        elif self.repet == 3:
            repet_descr = '按月重复'
            en_repet_descr = 'Monthly repetition'
        elif self.repet == 4:
            repet_descr = '按年重复'
            en_repet_descr = 'Annual repetition'
       
        bean = "{'id':%s,'op_ts':'%s','dispatch_type':%s,'dispatch_type_descr':'%s','start_time':'%s','end_time':'%s','plan_time':'%s','examine_time':'%s','create_user':%s,\
            'create_descr':'%s','content':'%s','handle_user':%s,'handle_descr':'%s','copy_users':%s,'copy_descr':'%s','en_copy_descr':'%s','station':'%s','first_apply_user':%s,'first_apply_descr':'%s','second_apply_user':%s,\
            'second_apply_descr':'%s','finall_apply_user':%s,'finall_apply_descr':'%s','device':%s,'repet':%s,'repet_descr':'%s','repet_interval':%s,'repet_week':%s,'is_use':%s,'en_content':'%s','en_device':%s, \
               'en_create_descr':'%s','en_handle_descr':'%s','en_first_apply_descr':'%s','en_second_apply_descr':'%s','en_finall_apply_descr':'%s','en_repet_descr':'%s','en_station':'%s'}" % (self.id,self.op_ts,self.dispatch_type,plan_descr,self.start_time,self.end_time,self.plan_time,self.examine_time,self.create_user,
            create_descr,self.content,self.handle_user,handle_descr,self.copy_users,','.join(copy_descr),','.join(en_copy_descr),station,self.first_apply_user,first_apply_descr,self.second_apply_user,
            second_apply_descr,self.finall_apply_user,finall_apply_descr,self.device,self.repet,repet_descr,self.repet_interval,self.repet_week,self.is_use,self.en_content,self.en_device,
            en_create_descr,en_handle_descr,en_first_apply_descr,en_second_apply_descr,en_finall_apply_descr,en_repet_descr,en_station)
        
        return bean.replace("None",'')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}