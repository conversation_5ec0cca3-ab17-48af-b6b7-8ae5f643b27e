import datetime

from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, ForeignKey, Column, DateTime, String, CHAR


class Rank(user_Base):
    u'职级'
    __tablename__ = "t_side_rank"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    create_time = Column(DateTime, nullable=False, comment="创建时间", default=datetime.datetime.now())
    name = Column(String(32), nullable=True, comment=u"职级名称")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

