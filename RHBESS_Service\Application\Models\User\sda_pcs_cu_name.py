#!/usr/bin/env python
# coding=utf-8
# @Information:


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base, user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR, \
    Boolean
from Tools.Utils.time_utils import timeUtils


class SdaPcsCu(user_Base):
    u'电站pcs 簇中文名和英文名'
    __tablename__ = "t_pcs_cu_name"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    station_name = Column(VARCHAR(50), nullable=False, comment=u"电站名称")
    pcs_id = Column(VARCHAR(50), nullable=False, comment=u"PCS id")
    unit_id = Column(VARCHAR(50), nullable=False, comment=u"单元 id")
    cluster_id = Column(VARCHAR(50), nullable=False, comment=u"簇 id")
    cluster_cname = Column(VARCHAR(50), nullable=True, comment=u"簇 中文名")
    pcs_name = Column(VARCHAR(50), nullable=True, comment=u"pcs 名称")
    cluster_name = Column(VARCHAR(50), nullable=True, comment=u"簇 英文名")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'station_name':'%s','pcs_id':'%s','unit_id':'%s','cluster_id':'%s','cluster_cname':'%s','pcs_name':'%s','cluster_name':'%s'}" % (
            self.id, self.station_name, self.pcs_id, self.unit_id, self.cluster_id, self.cluster_cname, self.pcs_name,
            self.cluster_name,)

