import datetime
import decimal
import json
import concurrent.futures

import math
from django.db.models import Su<PERSON>, <PERSON>, Q
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.user import models
from common import common_response_code
from common.constant import EMPTY_STR_LIST
from common.database_pools import ads_db_tool
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from settings.meter_origin_values import OriginValuesDict
from settings.meter_settings import METER_DIC
from tools.count import project_count, \
    get_project_yesterday_charge_discharge_complete_percentage


class ProvincesView(APIView):
    """
    省份集合
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        search_key = request.GET.get("search_key")
        province_list = models.ElectricityProvince.objects.all()
        if search_key:
            province_list = province_list.filter(Q(name__icontains=search_key) | Q(en_name__icontains=search_key)).all()

        provinces = []

        for province in province_list:
            temp_dict = {
                "id": province.id,
                "name": province.name if lang == "zh" else province.en_name
            }
            provinces.append(temp_dict)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": provinces
                }
            }
        )


class ControlCardsView(APIView):
    """项目疾控卡片页面"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
    def _get_project_income(self, project_id, lang='zh'):
        conn = get_redis_connection("1") # redis链接
        detail_dic = {
                "yesterday_general_income": 0,
                "month_general_income": 0,
                "year_general_income": 0,
                "all_general_income": 0,
            }
        p_income_key = f'project_all_year_month_yesterday_income_{lang}_{project_id}'  # 项目收益key
        if conn.get(p_income_key):
            detail_dic =json.loads(conn.get(p_income_key))
        else:
            project_ins = models.Project.objects.get(id=project_id)
            # master_stations = models.MaterStation.objects.filter(project=project_ins, is_delete=0).all()
            stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project_ins,
                                                            master_station__is_delete=0).all()
            income_date = datetime.date.today()
            yesterday = income_date - datetime.timedelta(days=1)

            

            # 切换 ads_report_station_income_1d

            stations_names = [s.english_name for s in stations]
            # 昨日总收益
            sql_yesterday = """
                    select sum(day_income) as day_income from ads_report_station_income_1d
                    where day='%s' and station in ('%s');
                    """ % (yesterday, "','".join(stations_names))

            t_peak = ads_db_tool.select_one(sql_yesterday)
            total_yesterday = t_peak['day_income'] if t_peak['day_income'] else 0

            # 月总收益
            # first_day = income_date.replace(day=1)
            current_month_str = datetime.datetime.strftime(income_date, '%Y%m')
            sql_month = """
                    select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                    where date_type='year_month' and date_value='%s' and station in ('%s');
                    """ % (current_month_str, "','".join(stations_names))

            monthly_income = ads_db_tool.select_one(sql_month)
            total_month = monthly_income['total_income'] if monthly_income['total_income'] else '--'

            # 年收益
            # year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()
            current_year_str = income_date.year
            sql_year = """
                    select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                    where date_type='year' and date_value='%s' and station in ('%s');
                    """ % (current_year_str, "','".join(stations_names))
            year_income = ads_db_tool.select_one(sql_year)
            total_year = year_income['total_income'] if year_income['total_income'] else '--'

            # 累计收益
            # all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()
            sql_all = """
                    select sum(total_income) as total_income from ads_report_station_income_cw_cm_cy_tt
                    where date_type='total' and station in ('%s');
                    """ % ("','".join(stations_names))

            total_income = ads_db_tool.select_one(sql_all)
            total_all = total_income['total_income'] if total_income['total_income'] else '--'

            detail_dic["yesterday_general_income"] = total_yesterday
            detail_dic["year_general_income"] = total_year
            detail_dic["month_general_income"] = total_month
            detail_dic["all_general_income"] = total_all

            if lang == 'zh':
                if detail_dic["yesterday_general_income"] != '--' and detail_dic["yesterday_general_income"] >= 1000000:
                    detail_dic["yesterday_general_income"] = [str(round(detail_dic["yesterday_general_income"] / 10000, 2)), "万元"]
                else:
                    detail_dic["yesterday_general_income"] = [str(detail_dic["yesterday_general_income"]), "元"]
                if detail_dic["month_general_income"] != '--' and detail_dic["month_general_income"] >= 1000000:
                    detail_dic["month_general_income"] = [str(round(detail_dic["month_general_income"] / 10000, 2)), "万元"]
                else:
                    detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "元"]
                if detail_dic["year_general_income"] != '--' and detail_dic["year_general_income"] >= 1000000:
                    detail_dic["year_general_income"] = [str(round(detail_dic["year_general_income"] / 10000, 2)), "万元"]
                else:
                    detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "元"]

                if detail_dic["all_general_income"] != '--' and detail_dic["all_general_income"] >= 1000000:
                    detail_dic["all_general_income"] = [str(round(detail_dic["all_general_income"] / 10000, 2)), "万元"]
                else:
                    detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "元"]
            else:
                detail_dic["yesterday_general_income"] = [str(detail_dic["yesterday_general_income"]), "Yuan"]
                detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "Yuan"]
                detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "Yuan"]
                detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "Yuan"]
            conn.set(p_income_key, json.dumps(detail_dic), ex=60 * 60 *6)
        return detail_dic


    def _get_realtime_data(self, project_id, lang='zh'):
        projects = models.Project.objects.filter(id=project_id).values(
            "id",  # 项目id
            "name",  # 项目名
            "english_name",  # 项目英文名
            "rated_power",  # 额定功率
            "rated_power_unit",  # 额定功率单位
            "rated_capacity",  # 额定容量
            "rated_capacity_unit",  # 额定容量单位
        )

        project = projects[0]
        # 旧字段强制映射
        project["project"] = project['id']
        project["project__name"] = project['name']
        project["project__english_name"] = project['english_name']
        project["project__rated_power"] = project['rated_power']
        project["project__rated_power_unit"] = project['rated_power_unit']
        project["project__rated_capacity"] = project['rated_capacity']
        project["project__rated_capacity_unit"] = project['rated_capacity_unit']

        # url = "http://172.17.6.44:9001/api/point/getRealtimeData"

        # day_charge_list = []
        # day_discharge_list = []
        # Fault_list = []
        # offline_list = []
        active_power_list = []
        # reactive_power_list = []
        # cycles_nmuber_list = []
        soc_list = []
        soh_list = []
        BCHCap_list = []  # 累计充电量
        BDHcap_list = []  # 累积放电量
        # alarm_list = []  # 累积放电量

        # units_all = models.Unit.objects.filter(station__project_id=project["id"]).all()
        # stations = models.StationDetails.objects.filter(project=project["id"]).all()
        units_all = models.Unit.objects.filter(is_delete=0, station__master_station__project_id=project["id"]).all()
        stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project["id"]).exclude(slave=0).all()
        for station in stations:
            meter_ins = METER_DIC[station.meter_count]

            units = models.Unit.objects.filter(is_delete=0, station=station).all()
            num = 0
            # alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station_id=station.id).exists()
            # alarm_list.append(alarm_exist)
            for unit in units:

                # 取电表初始值
                if (station.english_name in OriginValuesDict.keys() and unit.english_name in
                        OriginValuesDict[station.english_name].keys()):

                    origin_value_dict = OriginValuesDict.get(station.english_name).get(unit.english_name)
                    charge_key = METER_DIC.get(station.meter_count).get('charge')
                    discharge_key = METER_DIC.get(station.meter_count).get('discharge')

                    origin_charge = origin_value_dict.get(station.meter_count).get(
                        charge_key)
                    origin_discharge = origin_value_dict.get(station.meter_count).get(
                        discharge_key)

                else:
                    origin_charge = 0
                    origin_discharge = 0

                if len(units) > 1:
                    num += 1
                else:
                    num = ""
                # json_data = {
                #     "app": station.app,
                #     "station": station.english_name,
                #     "body": [
                #         {
                #             "device": unit.bms,
                #             "datatype": "measure",
                #             "totalcall": "0",
                #             "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
                #         },
                #         {
                #             "device": unit.pcs,
                #             "datatype": "measure",
                #             "totalcall": "0",
                #             "body": ["ChaD", "DisD", "P", "Q"],
                #         },
                #         {
                #             "device": unit.pcs,
                #             "datatype": "status",
                #             "totalcall": "0",
                #             "body": [
                #                 "Fault",
                #                 "alarm",
                #             ],
                #         },
                #         {
                #             "device": "EMS",
                #             "datatype": "status",
                #             "totalcall": "0",
                #             "body": ["AEnC", "AEn", "EAEn"],
                #         },
                #         {
                #             "device": unit.pcs,
                #             "datatype": "measure",
                #             "totalcall": "0",
                #             "body": ["PP1"],
                #         },
                #         {
                #             "device": f'{meter_ins["device"].upper()}{num}',
                #             "datatype": "cumulant",
                #             "totalcall": "0",
                #             "body": [meter_ins["charge"], meter_ins["discharge"]],
                #         },
                #         {
                #             "device": unit.bms,
                #             "datatype": "status",
                #             "totalcall": "0",
                #             "body": [
                #                 "GFault",
                #                 "GAlarm",
                #             ],
                #         },
                #     ],
                # }

                redis_conn = get_redis_connection("3")

                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
                                                               unit.bms)
                measure_bms = redis_conn.get(key1)
                if measure_bms:
                    measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
                else:
                    measure_bms_dict = {}

                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
                                                               unit.pcs)
                measure_pcs = redis_conn.get(key2)
                if measure_pcs:
                    measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
                else:
                    measure_pcs_dict = {}

                # key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                #                                                unit.pcs)
                # status_pcs = redis_conn.get(key3)
                # if status_pcs:
                #     status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                # else:
                #     status_pcs_dict = {}

                # key4 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, 'EMS')
                # status_ems = redis_conn.get(key4)
                # if status_ems:
                #     status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
                # else:
                #     status_ems_dict = {}

                key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', station.english_name,
                                                               f'{meter_ins["device"].upper()}{num}')
                cumulant_ = redis_conn.get(key5)
                if cumulant_:
                    cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
                else:
                    cumulant_dict = {}

                # key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
                # status_bms = redis_conn.get(key6)
                # if status_bms:
                #     status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                # else:
                #     status_bms_dict = {}

                # if not status_pcs_dict:
                    # offline_list.append(1)  # 离线状态

                # else:
                    # GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
                    # Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
                    # if Fault and int(Fault) == 1:
                    #     Fault_list.append(1)
                    # if GFault and int(GFault) == 1:
                    #     Fault_list.append(1)

                    # if station.english_name == "NBLS001":  # 德创单独计算日充放电量
                    #     ChaD = measure_bms_dict.get("ChaED", '--')  # 今日充电量
                    #     DisD = measure_bms_dict.get("DisED", '--')  # 今日放电量
                    # else:
                    #     ChaD, DisD = project_day_charge_count(station, unit.bms)
                    #
                    #
                    # day_charge_list.append(decimal.Decimal(ChaD)) if ChaD != '--' else day_charge_list.append(ChaD)
                    # day_discharge_list.append(decimal.Decimal(DisD)) if DisD != '--' else day_discharge_list.append(
                    #     DisD)

                BCHCap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["charge"])))) - abs(origin_charge)) if cumulant_dict.get(meter_ins["charge"]) and cumulant_dict.get(meter_ins["charge"]) not in EMPTY_STR_LIST else '--'  # 累计充电量
                BDHcap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["discharge"])))) - abs(origin_discharge)) if cumulant_dict.get(meter_ins["discharge"]) and cumulant_dict.get(meter_ins["discharge"]) not in EMPTY_STR_LIST else '--'# 累计放电量
                if station.english_name == "NBLS002" and station.meter_count == 3:
                    BCHCap += 21007 if BCHCap != '--' else '--'
                    BDHcap += 19108 if BDHcap != '--' else '--'
                BCHCap_list.append(decimal.Decimal(BCHCap)) if BCHCap != '--' else BCHCap_list.append(BCHCap)
                BDHcap_list.append(decimal.Decimal(BDHcap)) if BDHcap != '--' else BDHcap_list.append(BDHcap)

                P = measure_pcs_dict.get("P") if measure_pcs_dict.get("P") and measure_pcs_dict.get("P") not in EMPTY_STR_LIST else '--'  # 实时功率

                active_power_list.append(float(P)) if P != '--' and P != '' and P != 'null' else active_power_list.append('--')
                # Q = measure_pcs_dict.get("Q") if measure_pcs_dict.get("Q") and measure_pcs_dict.get("Q") not in EMPTY_STR_LIST else '--'  # 无功功率

                # reactive_power_list.append(float(Q)) if Q != '--' and Q != '' and Q != 'null' else reactive_power_list.append('--')
                NBSC = measure_bms_dict.get("NBSC") if measure_bms_dict.get("NBSC") and measure_bms_dict.get("NBSC") not in EMPTY_STR_LIST else '--'  # 循环次数
                SOC = measure_bms_dict.get("SOC") if measure_bms_dict.get("SOC") and measure_bms_dict.get("SOC") not in EMPTY_STR_LIST else '--'  # SOC
                SOH = measure_bms_dict.get("SOH") if measure_bms_dict.get("SOH") and measure_bms_dict.get("SOH") not in EMPTY_STR_LIST else '--'  # SOH

                # cycles_nmuber_list.append(float(NBSC)) if NBSC != '--' and NBSC != '' and NBSC != 'null' else cycles_nmuber_list.append('--')
                soc_list.append(float(SOC)) if SOC != '--' and SOC != '' and SOC != 'null' else soc_list.append('--')
                soh_list.append(float(SOH)) if SOH != '--' and SOH != '' and SOH != 'null' else soh_list.append('--')


        project["project_status"] = models.StationStatus.objects.values(
            'station__master_station__project_id').filter(station__master_station__project_id=project["id"]).aggregate(
            Max('status')).get('status__max')

        # project["day_charge"], project["day_charge_unit"] = charge_discharge_conversion(day_charge_list)  # 日充电量
        # project["day_discharge"], project["day_discharge_unit"] = charge_discharge_conversion(
        #     day_discharge_list)  # 日放电量
        project["active_power"] = int(sum(active_power_list)) if '--' not in active_power_list else '--'    # 有功功率
        # project["reactive_power"] = int(sum(reactive_power_list)) if '--' not in reactive_power_list else '--'  # 无功功率
        if len(units_all) == 0:
            project["SOC"] = '--'
            project["cycles"] = '--'
            project["SOH"] = '--'
        else:
            project["SOC"] = int(sum(soc_list) / len(units_all)) if '--' not in soc_list else '--'  # soc
            # project["cycles"] = int(sum(cycles_nmuber_list) / len(units_all))     # 上报循环次数
            count = project_count(project, sum(BCHCap_list), sum(BDHcap_list)) if '--' not in BCHCap_list and '--' not in BCHCap_list else '--'   # 计算循环次数
            project["cycles"] = count  # 上报循环次数
            project["SOH"] = int(sum(soh_list) / len(units_all)) if '--' not in soh_list else '--'  # soh
        project["unit_count"] = units_all.count()

        # 项目收益接口合入本接口
        income = self._get_project_income(project_id, lang)
        project["income"] = income

        # 新增的“设备数量”
        project["device_count"] = models.MaterStation.objects.filter(project_id=project_id, is_delete=0).count()

        #  “今日充电量”指标变更为“昨日充电量完成率”、 “今日放电量”指标变更为“昨日放电量完成率”
        charge_complete_percentage, discharge_complete_percentage = get_project_yesterday_charge_discharge_complete_percentage(
            project_id)
        project["yest_charge_comp_per"] = charge_complete_percentage
        project["yest_discharge_comp_per"] = discharge_complete_percentage
        return project

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        
        user_id = request.user["user_id"]
        # user_id = 6
        project_include = request.query_params.get("project", None)
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 8))
        province = request.query_params.get("province", None)
        user = models.UserDetails.objects.get(id=user_id)
        project_instances = models.Project.objects.filter(user=user, is_used=1).all()
        if project_include:
            project_instances = project_instances.filter(name__contains=project_include)

        if province:
            project_instances = project_instances.filter(Q(province__name__contains=province) | Q(province__en_name__contains=province))

        # 手动分页
        total_pages = math.ceil(len(project_instances) / page_size)
        start_index = (page-1) * page_size
        end_index = page * page_size if page < total_pages else len(project_instances) + 1

        project_ids = [project_instance.id for project_instance in project_instances][start_index:end_index]
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = list()
            for project_id in project_ids:
                future = executor.submit(self._get_realtime_data, project_id, lang)
                futures.append(future)
            results = [f.result() for f in concurrent.futures.as_completed(futures)]
        results.sort(key=lambda x: x['project'])
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": results,
                    "paginator_info": {
                        "page": page,
                        "page_size": page_size,
                        "pages": total_pages,
                        "total_count": len(project_instances)
                    }
                }
            }
        )