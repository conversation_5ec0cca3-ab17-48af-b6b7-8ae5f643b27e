#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-31 08:37:38
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_base_file.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-20 09:06:11


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Tools.Utils.time_utils import timeUtils

class ForecaseBaseFile(user_Base):
    u'项目文件分类表--收资归档'
    __tablename__ = "t_side_forecase_base_file"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    module_name = Column(String(50), nullable=False, comment="模块分类名称")
    name = Column(String(50), nullable=False, comment="具体文件分类名称")
    type_name = Column(String(50), nullable=True, comment="分类名称；基本信息；开工资料等")
    user_type = Column(VARCHAR(25), nullable=False, comment="用户类型；用户侧、独立储能")
    is_file = Column(CHAR(1), nullable=False,server_default='0',comment="是否有文件，0否1是")
    op_ts = Column(DateTime, nullable=False, comment="时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment="是否使用1是0否")

    stag_flag = Column(CHAR(1), nullable=True,comment="阶段表示")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        now = timeUtils.getNewTimeStr()
        i = 1
        # 用户侧
        # user_session.merge(ForecaseBaseFile(id=i,name='用户侧项目档案',user_type='用户侧',type_name='经营资料',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,name='用户侧项目档案',user_type='用户侧',type_name='用电信息',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,name='用户侧项目档案',user_type='用户侧',type_name='厂区面积',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,name='用户侧项目档案',user_type='用户侧',type_name='电气电路',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,name='用户侧项目档案',user_type='用户侧',type_name='政策文件',op_ts=now));i = i+1
        # 独立储能
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='承载企业信息',user_type='用户侧',type_name='企业信息表',stag_flag=1,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='承载企业信息',user_type='用户侧',type_name='企业信用证明',stag_flag=1,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='承载企业信息',user_type='用户侧',type_name='营业执照',stag_flag=2,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='承载企业信息',user_type='用户侧',type_name='土地证明/厂区租赁合同',stag_flag=2,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='踏勘信息',user_type='用户侧',type_name='踏勘照片',stag_flag=2,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='踏勘信息',user_type='用户侧',type_name='厂区平面图',stag_flag=2,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='用电信息',user_type='用户侧',type_name='电费单-原文件',stag_flag=1,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='用电信息',user_type='用户侧',type_name='电表负荷曲线-原文件',stag_flag=1,op_ts=now));i = i+1
        user_session.merge(ForecaseBaseFile(id=i,module_name="项目收资信息",name='用电信息',user_type='用户侧',type_name='电气接线图',stag_flag=2,op_ts=now));i = i+1

        # user_session.merge(ForecaseBaseFile(id=i,module_name="项目整理信息",name='整理数据',user_type='用户侧',type_name='电费单整理表',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,module_name="项目整理信息",name='整理数据',user_type='用户侧',type_name='现场采集负荷曲线',op_ts=now));i = i+1
        # user_session.merge(ForecaseBaseFile(id=i,module_name="项目整理信息",name='整理数据',user_type='用户侧',type_name='电表负荷曲线整理表',op_ts=now));i = i+1

        user_session.merge(ForecaseBaseFile(id=i,module_name="合同电子版归档",name='合同名称',user_type='用户侧',type_name='EMC协议',stag_flag=1,op_ts=now))
        # user_session.merge(ForecaseBaseFile(id=i,module_name="合同电子版归档",name='合同名称',user_type='独立储能',type_name='招标资料',op_ts=now));i = i+1
        
        user_session.commit()
        user_session.close()

    def __repr__(self):
        return "{'id':%s,'name':'%s','type_name':'%s','is_file':'%s','op_ts':'%s'}" %(self.id,self.name,self.type_name,self.is_file,self.op_ts)

   