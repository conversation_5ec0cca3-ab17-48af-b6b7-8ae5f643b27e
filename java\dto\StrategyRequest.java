package com.robestec.analysis.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 策略相关请求DTO
 */
@Data
public class StrategyRequest {

    /**
     * 策略模板导入请求DTO
     */
    @Data
    public static class StrategyImportRequest {
        /**
         * 上传的文件
         */
        @NotNull(message = "文件不能为空")
        private MultipartFile files;

        /**
         * 语言
         */
        private String lang;
    }

    /**
     * 项目包添加请求DTO
     */
    @Data
    public static class ProjectPackAddRequest {
        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        /**
         * 项目包数据(JSON字符串)
         */
        @NotBlank(message = "项目包数据不能为空")
        private String data;

        /**
         * 项目包名称
         */
        @NotBlank(message = "项目包名称不能为空")
        private String name;
    }

    /**
     * 项目包列表请求DTO
     */
    @Data
    public static class ProjectPackListRequest {
        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        private Long userId;
    }

    /**
     * 计划历史查询请求DTO
     */
    @Data
    public static class PlanHistoryRequest {
        /**
         * 电站名称
         */
        private String station;

        /**
         * 状态: 1成功；2失败
         */
        private Integer status;

        /**
         * 类型名称
         */
        private String typeName;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 页大小
         */
        private Integer pageSize = 10;

        /**
         * 页码
         */
        private Integer pageNum = 1;
    }

    /**
     * 计划历史导出请求DTO
     */
    @Data
    public static class PlanHistoryExportRequest {
        /**
         * 电站名称
         */
        private String station;

        /**
         * 状态: 1成功；2失败
         */
        private Integer status;

        /**
         * 类型名称
         */
        private String typeName;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;
    }
}
