import time
import json
import uuid
import geocoder
import datetime

from rest_framework import serializers
from apis.user import models
from django.core.validators import RegexValidator
from django.db.models import Q

from LocaleTool.common import redis_pool

UNIT_VERSIONS_DICT = {
    '1.0': {
        'rated_power': 100,
        'rated_capacity': 232.96
    },
    '2.0': {
        'rated_power': 100,
        'rated_capacity': 232.96
    },
    '3.0': {
        'rated_power': 125,
        'rated_capacity': 250
    }
}

VERSIONS_DICT = {
    '1.0': 1,
    '2.0': 2,
    '3.0': 3
}

class UnitInfoSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="储能单元名称")
    english_name = serializers.CharField(required=False, max_length=128, label="设备出厂编号")
    versions = serializers.CharField(required=True, max_length=32, label='设备版本号')


class EquipmentInfoSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="并网点名称")
    english_name = serializers.CharField(required=True, max_length=32, label="并网点英文名称")
    int_num = serializers.CharField(required=True, max_length=128, label="物联网卡号")
    app = serializers.CharField(required=True, max_length=128, label="app编号")
    emq_user = serializers.CharField(required=True, max_length=32, label="EMQX用户名")
    emq_pwd = serializers.CharField(required=True, max_length=32, label="EMQX密码")
    emq_clid = serializers.CharField(required=True, max_length=128, label="EMQX客户端ID")
    model = serializers.IntegerField(required=False, label="设备型号 (1：100型，2：200型，3：300型，4：400型)")
    # mode = serializers.IntegerField(required=False, label='从站布置模式 1:标准模式；2：鑫渠模式')
    unit_info = UnitInfoSerializers(required=False, many=True, label="储能单元信息")
    is_account = serializers.BooleanField(required=False, label="是否使用结算表(1：使用，0：不使用)")
    meter_number = serializers.CharField(required=False, max_length=32, label="电表编号")
    account_start = serializers.DateTimeField(required=False, label="结算表开始时间")
    account_end = serializers.DateTimeField(required=False, label="结算表结束时间")

    def validate_english_name(self, value):
        """验证站名全局唯一"""
        lang = self.context.get('lang', 'zh')
        station = models.StationDetails.objects.filter(is_delete=0, english_name=value).first()
        if station:
            raise Exception("{}-从设备名称重复".format(value) if lang == 'zh' else 'Duplicate from device name.')
        return value

    def validate(self, attrs):
        """
        验证参数
        """""
        # name
        lang = self.context.get('lang', 'zh')
        if not attrs.get('name'):
            raise Exception("{}-名称不能为空".format(attrs.get('name')) if lang == 'zh' else 'The name cannot be empty.')
        if len(attrs.get('name')) > 32:
            raise Exception("{}-名称长度不能超过32".format(attrs.get('name')) if lang == 'zh' else 'The length of the name cannot exceed 32.')

        # 验证结算电表开始时间和结束时间
        if attrs.get('account_start') and attrs.get('account_end'):
            if attrs.get('account_start') > attrs.get('account_end'):
                raise Exception(f"结算结束时间: <{attrs.get('account_end')}> 必须大于开始时间:"
                                f" <{attrs.get('account_start')}>" if lang == 'zh' else
                                'The settlement end time must be greater than the start time.')
        return attrs


class StationInfoSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="并网点名称")
    english_name = serializers.CharField(required=True, max_length=32, label="并网点英文名称")
    meter_position = serializers.IntegerField(required=True, label='电表位置 (1, "电表前置"), (2, "电表后置")')
    transformer_capacity = serializers.CharField(required=True, max_length=32, label='变压器容量')
    mode = serializers.IntegerField(required=True, label='配置模式 (1, "常规模式"), (2, "标准主从模式"), (3, "级联主从模式"), (2, "逻辑主从模式")')
    up_vol = serializers.IntegerField(required=False, label='并网点变压器上限阈值')
    low_vol = serializers.IntegerField(required=False, label='并网点变压器下限阈值')
    equipment_info = EquipmentInfoSerializers(many=True, label='设备信息')

    def validate_english_name(self, value):
        """验证站名全局唯一"""
        lang = self.context.get('lang', 'zh')
        station = models.MaterStation.objects.filter(english_name=value, is_delete=0).first()
        if station:
            raise Exception("{}-并网点名称重复".format(value) if lang == 'zh' else 'Duplicate station name.')
        return value


class ProjectsAddNewSerializer(serializers.Serializer):
    """web 序列化器"""

    project_name = serializers.CharField(required=True, max_length=32, label="项目名称")
    project_english_name = serializers.CharField(required=True, max_length=32, label="项目英文名称")
    address = serializers.CharField(required=False, max_length=128, label="地址")
    province_id = serializers.IntegerField(required=True, label="省份ID")
    organization_id = serializers.IntegerField(required=False, label="组织ID")
    manager = serializers.CharField(required=True, max_length=32, label="客户经理")
    manager_phone = serializers.CharField(required=True, max_length=32, label="客户经理电话", validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    price_type = serializers.IntegerField(required=True, label="电价类型")
    _type = serializers.IntegerField(required=True, label="默认用电类型")
    level = serializers.IntegerField(required=True, label="默认用电等级")
    city = serializers.CharField(required=True, max_length=32, label="城市")
    application_scenario = serializers.IntegerField(required=True, min_value=0, max_value=1, label="应用场景：0：峰谷套利；1：台区治理")
    counties_id = serializers.IntegerField(required=False, allow_null=True, label="区县ID")
    compant_name = serializers.CharField(required=False, max_length=64, allow_null=True, label="企业名称")
    compant_code = serializers.CharField(required=False, max_length=32, allow_null=True, label="企业code")
    contact_person = serializers.CharField(required=False, max_length=32, allow_null=True, label="客户联系人")
    contact_person_phone = serializers.CharField(required=False, max_length=32, allow_null=True, label="客户联系人电话")
    industry_id = serializers.IntegerField(required=False, allow_null=True, label="企业行业ID")
    connect_time = serializers.DateField(required=True, label="并网日期")
    project_type = serializers.IntegerField(required=True, label="项目类型")
    gf_cap = serializers.FloatField(required=False, label="光伏规模")
    demand_cumput = serializers.IntegerField(required=True, label="需量计算方式")
    demand_cap = serializers.FloatField(required=False, label="需量")
    debug_user = serializers.CharField(required=False, max_length=128, label="调试人员")
    afftet_sale_user = serializers.CharField(required=False, max_length=128, label="售后负责人")
    images = serializers.JSONField(required=False, allow_null=True, label="附件url，多个以英文逗号间隔")
    station_info = StationInfoSerializers(many=True, label="并网点信息")

    def validate_project_english_name(self, value):
        """验证项目名全局唯一"""
        lang = self.context.get('lang', 'zh')
        print(126, lang)
        station = models.Project.objects.filter(is_used=1, english_name=value).first()
        if station:
            raise Exception(f"{value}-项目名称重复" if lang == 'zh' else 'Duplicate project name.')
        return value

    def validate(self, attrs):
        """
        验证主站和逻辑主站的英文名称需要保持一致
        """""
        lang = self.context.get('lang', 'zh')
        station_info = attrs.get('station_info')
        application_scenario = attrs.get('application_scenario')
        for m_station in station_info:
            m_station_en = m_station.get('english_name')
            first_station = m_station.get('equipment_info')[0]
            if m_station_en != first_station.get('english_name'):
                raise Exception(f"并网点:<{m_station_en}>和主设备:<{first_station.get('english_name')}>编号需要保持一致" if
                                lang == 'zh' else f"The equipment number of the main station:<{m_station_en}> and"
                                                  f" the main device:<{first_station.get('english_name')}>"
                                                  f" need to be consistent.")
            if application_scenario == 1:
                up_vol = m_station.get('up_vol')
                low_vol = m_station.get('low_vol')
                if up_vol and low_vol:
                    if up_vol <= low_vol:
                        raise Exception(f"并网点:<{m_station_en}>: 请注意上下阈值大小关系！" if
                                        lang == 'zh' else f"The equipment number of the main station:<{m_station_en}: Please pay attention to the relationship between the upper and lower threshold sizes!")





        return attrs

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')

        project = models.Project()
        project.name = validated_data.get('project_name')
        project.en_name = validated_data.get('project_name')
        project.english_name = validated_data.get('project_name')
        # 查询地址信息
        province = models.ElectricityProvince.objects.filter(id=validated_data.get('province_id')).first()
        province_name = province.name
        province_en_name = province.en_name
        # g = geocoder.google("1403 Washington Ave, New Orleans, LA 70130")
        # g = geocoder.arcgis(province_name)
        # c = g.latlng
        project.city = province_name
        project.en_city = province_en_name
        # project.longitude = c[1]
        # project.latitude = c[0]
        project.english_name = validated_data.get('project_english_name')
        project.address = validated_data.get('address') if validated_data.get('address') else '未填写'
        project.en_address = validated_data.get('address') if validated_data.get('address') else 'Blank'
        project.in_time = datetime.datetime.now()
        project.province_id = validated_data.get('province_id')
        project.type = validated_data.get('_type')
        project.application_scenario = validated_data.get('application_scenario')
        project.level = validated_data.get('level')
        project.manager = validated_data.get('manager')
        project.en_manager = validated_data.get('manager')
        project.contact_person = validated_data.get('contact_person')
        project.contact_person_phone = validated_data.get('contact_person_phone')
        project.manager_phone = validated_data.get('manager_phone')
        project.create_time = validated_data.get('connect_time')
        project.city = validated_data.get('city')
        project.en_city = validated_data.get('city')
        if validated_data.get('organization_id'):
            project.organization_id = validated_data.get('organization_id')
        if validated_data.get('counties_id'):
            project.counties_id = validated_data.get('counties_id')
        if validated_data.get('compant_code'):
            project.compant_code = validated_data.get('compant_code')
        if validated_data.get('compant_name'):
            project.compant_name = validated_data.get('compant_name')
            project.en_compant_name = validated_data.get('compant_name')
        if validated_data.get('industry_id'):
            project.industry_id = validated_data.get('industry_id')
        if validated_data.get('images'):
            project.images = json.dumps(validated_data.get('images'))
        else:
            project.images = None
        project.price_type = validated_data.get('price_type')
        project.project_type = validated_data.get('project_type')
        project.gf_cap = validated_data.get('gf_cap')
        project.demand_cumput = validated_data.get('demand_cumput')
        project.demand_cap = validated_data.get('demand_cap')
        project.debug_user = validated_data.get('debug_user')
        project.en_debug_user = validated_data.get('debug_user')
        project.afftet_sale_user = validated_data.get('afftet_sale_user')
        project.en_afftet_sale_user = validated_data.get('afftet_sale_user')
        project.save()

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'

        user_ins = models.UserDetails.objects.get(id=user)
        user_ins.project_set.add(project)
        user_ins.save()

        project_rated_power = 0  # 项目总功率
        project_rated_capacity = 0  # 项目总容量
        project_station_number = 0  # 单元总数
        # 新增主站
        for m in validated_data.get('station_info'):
            slave, pack = 0, 0
            master_station = models.MaterStation()
            master_station.name = m.get('name')
            master_station.en_name = m.get('name')
            master_station.english_name = m.get('english_name')
            if m.get('up_vol'):
                master_station.up_vol = m.get('up_vol')
            if m.get('low_vol'):
                master_station.low_vol = m.get('low_vol')
            # 舍弃该字段
            # if m.get('mode') == 1:  # 主从模式
            #     is_v3 = m.get('equipment_info')[1].get('unit_info')[0].get('versions')
            # else:
            #     is_v3 = m.get('equipment_info')[0].get('unit_info')[0].get('versions')
            # master_station.is_v3 = 1 if is_v3 == '3.0' else 0
            master_station.project_id = project.id
            master_station.mode = m.get('mode')
            master_station.save()

            # 异步翻译：主站
            pdr_data_master = {'id': master_station.id,
                         'table': 't_master_stations',
                         'update_data': {'name': m.get('name')}}

            redis_pool.publish(pub_name, json.dumps(pdr_data_master))

            side_register_account = models.SideRegisterAccount()
            side_register_account.province_id = validated_data.get('province_id')
            side_register_account.station = master_station
            side_register_account.station_code = master_station.english_name
            side_register_account.project = project
            if validated_data.get('compant_code'):
                side_register_account.compant_code = validated_data.get('compant_code')
            if validated_data.get('compant_name'):
                side_register_account.compant_name = validated_data.get('compant_name')
                side_register_account.en_compant_name = validated_data.get('compant_name')
            if validated_data.get('industry_id'):
                side_register_account.industry_id = validated_data.get('industry_id')
            side_register_account.price_type_id = 1  # 默认大工业
            side_register_account.level_name = validated_data.get('level')
            if validated_data.get('address'):
                side_register_account.address = validated_data.get('address')
                side_register_account.en_address = validated_data.get('address')
            if project.city:
                side_register_account.city_id = models.City.objects.get(Q(name=project.city) | Q(en_name=project.city), province=project.province).id
            side_register_account.counties_id = project.counties_id
            side_register_account.city_counties = project.city + models.Counties.objects.get(id=validated_data.get('counties_id')).name if validated_data.get('counties_id') else project.city
            side_register_account.en_city_counties = project.city + models.Counties.objects.get(id=validated_data.get('counties_id')).name if validated_data.get('counties_id') else project.city

            station_model_count = 0  # 主站设备数量

            # 新增从站
            for s in m.get('equipment_info'):
                rated_power = 0
                rated_capacity = 0
                station = models.StationDetails()
                station.station_name = s.get('name')
                station.en_station_name = s.get('name')
                station.db = project.english_name
                station.app = s.get('app')
                station.english_name = s.get('english_name')
                if s.get('model'):
                    station.meter = s.get('model')
                    station_model_count += s.get('model')
                    station.specifications = s.get('model')
                    station.pcs_number = s.get('model')
                    station.unit_number = s.get('model')
                    station.fire_fighting = s.get('model')
                    station.cabinet = s.get('model')
                    station.battery_cluster = s.get('model')
                else:
                    station.meter = 0
                    station.specifications = 0
                    station.pcs_number = 0
                    station.unit_number = 0
                    station.fire_fighting = 0
                    station.cabinet = 0
                    station.battery_cluster = 0
                station.address = project.address
                station.en_address = project.address
                station.emq_user = s.get('emq_user')
                station.emq_pwd = s.get('emq_pwd')
                station.emq_clid = s.get('emq_clid')
                station.int_num = s.get('int_num')
                station.meter_position = m.get('meter_position')
                station.transformer_capacity = m.get('transformer_capacity') if station.english_name == master_station.english_name else 0
                station.master_station_id = master_station.id
                station.project_id = project.id
                station.province_id = project.province_id
                station.level = project.level
                station.type = project.type
                station.meter_type = 2
                station.meter_count = 1

                # 常规模式
                if m.get('mode') == 1:
                    station.slave = -1
                    station.pack = -1

                # 标准主从模式
                elif m.get('mode') == 2:
                    station.slave = slave
                    station.pack = -1
                    slave += 1

                # 级联主从模式
                elif m.get('mode') == 3:
                    station.slave = slave + 1
                    station.pack = -1
                    slave += 1

                # 逻辑主从模式
                elif m.get('mode') == 4:
                    station.slave = -1
                    station.pack = pack
                    pack += 1

                # 其他：默认为标准模式
                else:
                    station.slave = -1
                    station.pack = -1

                if s.get('model'):
                    project_station_number += s.get('model')
                station.save()

                ini_num = []

                # 新增单元
                unit_number = 1
                if s.get('unit_info'):
                    for u in s.get('unit_info'):
                        rated_power += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                        rated_capacity += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                        ini_num.append(u.get('english_name'))
                        unit = models.Unit()
                        unit.unit_name = '储能单元' + str(unit_number)
                        unit.en_unit_name = 'Unit ' + str(unit_number)
                        unit.english_name = 'PCS' + str(uuid.uuid4())[:16]
                        unit.unit_new_name = u.get('name')
                        unit.en_unit_new_name = u.get('name')
                        unit.user_id = user
                        unit.station_id = station.id
                        unit.rated_power = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                        unit.rated_capacity = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                        unit.v_number = VERSIONS_DICT.get(u.get('versions'), 1)
                        if station.meter == 1:
                            unit.bms = "BMS"
                            unit.pcs = "PCS"
                        else:
                            unit.bms = f"BMS{unit_number}"
                            unit.pcs = f"PCS{unit_number}"
                        unit_number += 1
                        unit.save()

                        # 异步翻译: 单元
                        pdr_data_unit = {'id': unit.id,
                                         'table': 't_unit',
                                         'update_data': {'unit_new_name': u.get('name')}}

                        redis_pool.publish(pub_name, json.dumps(pdr_data_unit))

                station.ini_num = ';'.join(ini_num)
                station.rated_power = rated_power
                station.rated_capacity = round(rated_capacity, 2)

                if s.get('is_account'):
                    station.is_account = s.get('is_account')
                if s.get('meter_number'):
                    station.meter_number = s.get('meter_number')

                station.save()

                # 异步翻译: 从站
                pdr_data_station = {'id': station.id,
                                    'table': 't_stations',
                                    'update_data': {'station_name': s.get('name'), 'address': project.address}}

                redis_pool.publish(pub_name, json.dumps(pdr_data_station))

                # 保存结算电表使用时间范围
                if s.get('is_account'):
                    meter_use_time = models.StationMeterUseTime()
                    meter_use_time.start_time = s.get('account_start')
                    if s.get('account_end'):
                        meter_use_time.end_time = s.get('account_end')
                    meter_use_time.station = station
                    meter_use_time.user_id = user
                    meter_use_time.save()

                project_rated_power += station.rated_power
                project_rated_capacity += station.rated_capacity
            side_register_account.compact_cap = 110 * station_model_count
            side_register_account.run_cap = 110 * station_model_count
            side_register_account.save()

            # 异步翻译: 需求侧响应
            pdr_data_register = {'id': side_register_account.id,
                         'table': 't_side_register_account',
                         'update_data': {'city_counties': side_register_account.city_counties}}

            if validated_data.get('compant_name'):
                pdr_data_register['update_data']['compant_name'] = validated_data.get('compant_name')
            if validated_data.get('address'):
                pdr_data_register['update_data']['address'] = validated_data.get('address')

            redis_pool.publish(pub_name, json.dumps(pdr_data_register))

        project.rated_power = project_rated_power
        project.rated_capacity = round(project_rated_capacity, 2)
        project.station_number = project_station_number
        project.save()

        # 异步翻译: 项目
        pdr_data_project = {'id': project.id,
                    'table': 't_project',
                    'update_data': {'name': validated_data.get('project_name'),
                                    'city': validated_data.get('city'),
                                    'address': project.address}}
        if validated_data.get('compant_name'):
            pdr_data_project['update_data']['compant_name'] = validated_data.get('compant_name')
        if validated_data.get('debug_user'):
            pdr_data_project['update_data']['debug_user'] = validated_data.get('debug_user')
        if validated_data.get('afftet_sale_user'):
            pdr_data_project['update_data']['afftet_sale_user'] = validated_data.get('afftet_sale_user')


        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data_project))

        return 1


class UnitInfoUpdateSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="储能单元名称")
    english_name = serializers.CharField(required=False, max_length=128, label="设备出厂编号")
    versions = serializers.CharField(required=True, max_length=32, label='设备版本号')


class EquipmentInfoUpdateSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="并网点名称")
    english_name = serializers.CharField(required=True, max_length=32, label="并网点英文名称")
    int_num = serializers.CharField(required=True, max_length=128, label="物联网卡号")
    app = serializers.CharField(required=True, max_length=128, label="app编号")
    emq_user = serializers.CharField(required=True, max_length=32, label="EMQX用户名")
    emq_pwd = serializers.CharField(required=True, max_length=32, label="EMQX密码")
    emq_clid = serializers.CharField(required=True, max_length=128, label="EMQX客户端ID")
    model = serializers.IntegerField(required=False, label="设备型号 (1：100型，2：200型，3：300型，4：400型)")
    # mode = serializers.IntegerField(required=False, label='从站布置模式 1:标准模式；2：鑫渠模式')
    unit_info = UnitInfoSerializers(required=False, many=True, label="储能单元信息")
    is_account = serializers.BooleanField(required=False, label="是否使用结算表(1：使用，0：不使用)")
    meter_number = serializers.CharField(required=False, max_length=32, label="电表编号")
    account_start = serializers.DateTimeField(required=False, label="结算表开始时间")
    account_end = serializers.DateTimeField(required=False, label="结算表结束时间")
    id = serializers.IntegerField(required=False, label='从站ID')

    def validate(self, attrs):
        """验证站名全局唯一"""
        if attrs.get('id'):
            station = models.StationDetails.objects.filter(~Q(id=attrs.get('id')), english_name=attrs.get('english_name'), is_delete=0).first()
            if station:
                raise Exception("{}-从设备名称重复".format(attrs.get('english_name')))

        # 验证结算电表开始时间和结束时间
        if attrs.get('account_start') and attrs.get('account_end'):
            if attrs.get('account_start') > attrs.get('account_end'):
                raise Exception(
                    f"结算结束时间: <{attrs.get('account_end')}> 必须大于开始时间: <{attrs.get('account_start')}>")
        # else:
        #     station = models.StationDetails.objects.filter(english_name=attrs.get('english_name')).first()
        #     if station:
        #         raise Exception("{}-从设备名称重复".format(attrs.get('english_name')))
        return attrs


class StationInfoUpdateSerializers(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=32, label="并网点名称")
    english_name = serializers.CharField(required=True, max_length=32, label="并网点英文名称")
    meter_position = serializers.IntegerField(required=True, label='电表位置 (1, "电表前置"), (2, "电表后置")')
    transformer_capacity = serializers.CharField(required=True, max_length=32, label='变压器容量')
    mode = serializers.IntegerField(required=True, label='配置模式 (1, "主从模式"), (2, "常规模式")')
    up_vol = serializers.IntegerField(required=False, label='并网点变压器上限阈值')
    low_vol = serializers.IntegerField(required=False, label='并网点变压器下限阈值')
    equipment_info = EquipmentInfoUpdateSerializers(many=True, label='设备信息')
    id = serializers.IntegerField(required=False, label='主站ID')

    def validate(self, attrs):
        """验证站名全局唯一"""
        lang = self.context.get('lang', 'zh')
        if attrs.get('id'):
            station = models.MaterStation.objects.filter(~Q(id=attrs.get('id')), english_name=attrs.get('english_name'), is_delete=0).first()
            if station:
                raise Exception("{}-并网点名称重复".format(attrs.get('english_name')) if lang == 'zh' else 'Duplicate station name.')
        else:
            station = models.MaterStation.objects.filter(english_name=attrs.get('english_name'), is_delete=0).first()
            if station:
                raise Exception("{}-并网点名称重复".format(attrs.get('english_name'))  if lang == 'zh' else 'Duplicate station name.')
        return attrs


class ProjectsUpdateNewSerializer(serializers.Serializer):
    """web 序列化器"""

    project_name = serializers.CharField(required=True, max_length=32, label="项目名称")
    project_english_name = serializers.CharField(required=True, max_length=32, label="项目英文名称")
    address = serializers.CharField(required=False, max_length=128, label="地址")
    province_id = serializers.IntegerField(required=True, label="省份ID")
    organization_id = serializers.IntegerField(required=False, allow_null=True, label="组织ID")
    manager = serializers.CharField(required=True, max_length=32, label="客户经理")
    manager_phone = serializers.CharField(required=True, max_length=32, label="客户经理电话", validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    price_type = serializers.IntegerField(required=True, label="电价类型")
    _type = serializers.IntegerField(required=True, label="默认用电类型")
    level = serializers.IntegerField(required=True, label="默认用电等级")
    station_info = StationInfoUpdateSerializers(many=True, label="并网点信息")
    city = serializers.CharField(required=True, max_length=32, label="城市")
    application_scenario = serializers.IntegerField(required=True,  min_value=0, max_value=1, label="应用场景：0：峰谷套利；1：台区治理")
    counties_id = serializers.IntegerField(required=False, allow_null=True, label="区县ID")
    compant_name = serializers.CharField(required=False, max_length=64, allow_null=True, label="企业名称")
    compant_code = serializers.CharField(required=False, max_length=32, allow_null=True, label="企业code")
    contact_person = serializers.CharField(required=False, max_length=32, allow_null=True, label="客户联系人")
    contact_person_phone = serializers.CharField(required=False, max_length=32, allow_null=True, label="客户联系人电话")
    industry_id = serializers.IntegerField(required=False, allow_null=True, label="企业行业ID")
    connect_time = serializers.DateField(required=True, label="并网日期")
    images = serializers.JSONField(required=False, allow_null=True, label="附件url，多个以英文逗号间隔")
    id = serializers.IntegerField(required=True, label='项目ID')
    project_type = serializers.IntegerField(required=True, label="项目类型")
    gf_cap = serializers.FloatField(required=False, label="光伏规模")
    demand_cumput = serializers.IntegerField(required=True, label="需量计算方式")
    demand_cap = serializers.FloatField(required=False, label="需量")
    debug_user = serializers.CharField(required=False, max_length=128, label="调试人员")
    afftet_sale_user = serializers.CharField(required=False, max_length=128, label="售后负责人")

    def validate(self, attrs):
        """验证项目名全局唯一"""
        lang = self.context.get('lang', 'zh')
        if attrs.get('id'):
            station = models.Project.objects.filter(~Q(id=attrs.get('id')), english_name=attrs.get('project_english_name'), is_used=1).first()
            if station:
                raise Exception("{}-项目名称重复".format(attrs.get('project_english_name') if lang == 'zh' else
                                f"Duplicate project name: {attrs.get('project_english_name')}"))

        application_scenario = attrs.get('application_scenario')
        # 验证主站和逻辑主站的英文名称需要保持一致
        station_info = attrs.get('station_info')
        for m_station in station_info:
            m_station_en = m_station.get('english_name')
            first_station = m_station.get('equipment_info')[0]
            if m_station_en != first_station.get('english_name'):
                raise Exception(f"并网点:<{m_station_en}>和主设备:<{first_station.get('english_name')}>编号需要保持一致" if
                                lang == 'zh' else f"The Master station:<{m_station_en}> and the main"
                                                  f" device:<{first_station.get('english_name')}> need"
                                                  f" to be consistent.")
            if application_scenario == 1:
                up_vol = m_station.get('up_vol')
                low_vol = m_station.get('low_vol')
                if up_vol and low_vol:
                    if up_vol <= low_vol:
                        raise Exception(f"并网点:<{m_station_en}>: 请注意上下阈值大小关系！" if
                                        lang == 'zh' else f"The equipment number of the main station:<{m_station_en}: Please pay attention to the relationship between the upper and lower threshold sizes!")

        return attrs

    def update(self, project, validated_data):
        lang = self.context.get('lang', 'zh')
        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        user = self.context.get('user_id')
        project = models.Project.objects.get(id=validated_data.get('id'))
        project.name = validated_data.get('project_name')
        project.en_name = validated_data.get('project_name')
        # 查询地址信息
        province_name = models.ElectricityProvince.objects.filter(id=validated_data.get('province_id')).first().name
        # g = geocoder.google("1403 Washington Ave, New Orleans, LA 70130")
        # g = geocoder.arcgis(province_name)
        # c = g.latlng
        project.city = province_name
        # project.longitude = c[1]
        # project.latitude = c[0]
        project.english_name = validated_data.get('project_english_name')
        project.address = validated_data.get('address') if validated_data.get('address') else '未填写'
        project.en_address = validated_data.get('address') if validated_data.get('address') else 'Blank'
        project.user_id = user
        project.application_scenario = validated_data.get('application_scenario')
        project.province_id = validated_data.get('province_id')
        project.type = validated_data.get('_type')
        project.level = validated_data.get('level')
        project.manager = validated_data.get('manager')
        project.en_manager = validated_data.get('manager')
        project.contact_person = validated_data.get('contact_person')
        project.contact_person_phone = validated_data.get('contact_person_phone')
        project.manager_phone = validated_data.get('manager_phone')
        project.create_time = validated_data.get('connect_time')
        project.city = validated_data.get('city')
        project.en_city = validated_data.get('city')
        if validated_data.get('organization_id'):
            project.organization_id = validated_data.get('organization_id')
        if validated_data.get('counties_id'):
            project.counties_id = validated_data.get('counties_id')
        if validated_data.get('compant_code'):
            project.compant_code = validated_data.get('compant_code')
        if validated_data.get('compant_name'):
            project.compant_name = validated_data.get('compant_name')
            project.en_compant_name = validated_data.get('compant_name')
        if validated_data.get('industry_id'):
            project.industry_id = validated_data.get('industry_id')
        if validated_data.get('images'):
            project.images = json.dumps(validated_data.get('images'))
        project.price_type = validated_data.get('price_type')
        project.project_type = validated_data.get('project_type')
        project.gf_cap = validated_data.get('gf_cap')
        project.demand_cumput = validated_data.get('demand_cumput')
        project.demand_cap = validated_data.get('demand_cap')
        project.debug_user = validated_data.get('debug_user')
        project.en_debug_user = validated_data.get('debug_user')
        project.afftet_sale_user = validated_data.get('afftet_sale_user')
        project.en_afftet_sale_user = validated_data.get('afftet_sale_user')
        project.save()
        project_user_ids = [i.id for i in project.user.all()]
        user_data = [models.UserDetails.objects.get(id=i) for i in project_user_ids]

        # 异步翻译
        pdr_data_project = {'id': project.id,
                    'table': 't_project',
                    'update_data': {'name': project.name,
                                    'city': project.city,
                                    'address': project.address,
                                    'manager': project.manager}}
        if validated_data.get('compant_name'):
            pdr_data_project['update_data']['compant_name'] = validated_data.get('compant_name')

        if validated_data.get('debug_user'):
            pdr_data_project['update_data']['debug_user'] = validated_data.get('debug_user')
        if validated_data.get('afftet_sale_user'):
            pdr_data_project['update_data']['afftet_sale_user'] = validated_data.get('afftet_sale_user')
        redis_pool.publish(pub_name, json.dumps(pdr_data_project))

        project_rated_power = 0  # 项目总功率
        project_rated_capacity = 0  # 项目总容量
        project_station_number = 0  # 单元总数
        # 新增主站
        for m in validated_data.get('station_info'):
            slave, pack = 0, 0
            if m.get('id'):
                master_station = models.MaterStation.objects.get(id=m.get('id'), is_delete=0)

                # 主站的模式改变后将删除掉该主站下的所有子站及子站下的所有储能单元
                if m.get('mode') != master_station.mode:
                    has_units = models.Unit.objects.filter(is_delete=0, station__master_station_id=m.get('id')).all()
                    if has_units.exists():
                        for i in has_units:
                            i.is_delete = 1
                            i.save()
                    has_m_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                    if has_m_stations.exists():
                        for j in has_m_stations:
                            j.is_delete = 1
                            j.save()
                # if m.get('up_vol'):
                master_station.up_vol = m.get('up_vol')
                # if m.get('low_vol'):
                master_station.low_vol = m.get('low_vol')
                master_station.name = m.get('name')
                master_station.en_name = m.get('name')
                master_station.english_name = m.get('english_name')
                master_station.mode = m.get('mode')
                master_station.save()

                # 异步翻译：主站
                pdr_data_master = {'id': master_station.id,
                                   'table': 't_master_stations',
                                   'update_data': {'name': m.get('name')}}

                redis_pool.publish(pub_name, json.dumps(pdr_data_master))

                # station_mode = master_station.stationdetails_set.first()
                # if station_mode:
                #     if station_mode.slave == -1 and station_mode.pack == -1:
                #         if m.get('mode') != 2:
                #             models.Unit.objects.filter(is_delete=0, station__master_station_id=m.get('id')).delete()
                #             master_station.stationdetails_set.all().delete()
                #     else:
                #         if m.get('mode') != 1:

                for s in m.get('equipment_info'):
                    if s.get('id'):
                        station = models.StationDetails.objects.filter(is_delete=0, id=s.get('id')).first()
                        rated_power = 0
                        rated_capacity = 0
                        station.station_name = s.get('name')
                        station.db = project.english_name
                        station.app = s.get('app')
                        station.english_name = s.get('english_name')
                        if s.get('model'):
                            station.meter = s.get('model')
                            station.specifications = s.get('model')
                            station.pcs_number = s.get('model')
                            station.unit_number = s.get('model')
                            station.fire_fighting = s.get('model')
                            station.cabinet = s.get('model')
                            station.battery_cluster = s.get('model')
                        else:
                            station.meter = 0
                            station.specifications = 0
                            station.pcs_number = 0
                            station.unit_number = 0
                            station.fire_fighting = 0
                            station.cabinet = 0
                            station.battery_cluster = 0
                        station.address = project.address
                        station.en_address = project.address
                        station.emq_user = s.get('emq_user')
                        station.emq_pwd = s.get('emq_pwd')
                        station.emq_clid = s.get('emq_clid')
                        station.int_num = s.get('int_num')
                        station.meter_position = m.get('meter_position')
                        station.transformer_capacity = m.get('transformer_capacity') if station.english_name == master_station.english_name else 0
                        station.province_id = project.province_id
                        station.level = project.level
                        station.type = project.type
                        station.is_account = s.get('is_account', station.is_account)
                        station.meter_number = s.get('meter_number', station.meter_number)

                        # 常规模式
                        if m.get('mode') == 1:
                            station.slave = -1
                            station.pack = -1

                        # 标准主从模式
                        elif m.get('mode') == 2:
                            station.slave = slave
                            station.pack = -1
                            slave += 1

                        # 级联主从模式
                        elif m.get('mode') == 3:
                            station.slave = slave + 1
                            station.pack = -1
                            slave += 1

                        # 逻辑主从模式
                        elif m.get('mode') == 4:
                            station.slave = -1
                            station.pack = pack
                            pack += 1

                        # 其他：默认为标准模式
                        else:
                            station.slave = -1
                            station.pack = -1

                        station.save()

                        # 异步翻译: 从站
                        pdr_data_station = {'id': station.id,
                                            'table': 't_stations',
                                            'update_data': {'station_name': s.get('name'), 'address': project.address}}

                        redis_pool.publish(pub_name, json.dumps(pdr_data_station))

                        # 保存结算电表使用时间范围
                        if s.get('is_account'):
                            meter_use_time = models.StationMeterUseTime.objects.filter(station=station).first()
                            if meter_use_time:
                                meter_use_time.start_time = s.get('account_start')
                                meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                                meter_use_time.station = station
                                meter_use_time.user_id = user
                                meter_use_time.save()
                            else:
                                meter_use_time = models.StationMeterUseTime()
                                meter_use_time.start_time = s.get('account_start')
                                meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                                meter_use_time.station = station
                                meter_use_time.user_id = user
                                meter_use_time.save()

                        # 删除储能单元重新写入
                        if station.unit_set.filter(is_delete=0).exists():
                            for u in station.unit_set.filter(is_delete=0).all():
                                u.is_delete = 1
                                u.save()

                        ini_num = []

                        # 新增单元
                        unit_number = 1
                        if s.get('unit_info'):
                            for u in s.get('unit_info'):
                                rated_power += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                                rated_capacity += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')

                                ini_num.append(u.get('english_name'))
                                unit = models.Unit()
                                unit.unit_name = '储能单元' + str(unit_number)
                                unit.en_unit_name = 'Unit ' + str(unit_number)
                                unit.english_name = 'PCS' + str(uuid.uuid4())[:16]
                                unit.unit_new_name = u.get('name')
                                unit.en_unit_new_name = u.get('name')
                                unit.user_id = user
                                unit.station_id = station.id
                                unit.rated_power = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                                unit.rated_capacity = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                                unit.v_number = VERSIONS_DICT.get(u.get('versions'), 1)
                                if station.meter == 1:
                                    unit.bms = "BMS"
                                    unit.pcs = "PCS"
                                else:
                                    unit.bms = f"BMS{unit_number}"
                                    unit.pcs = f"PCS{unit_number}"
                                unit_number += 1
                                unit.save()

                                if u.get('name'):
                                    # 异步翻译: 单元
                                    pdr_data_unit = {'id': unit.id,
                                                     'table': 't_unit',
                                                     'update_data': {'unit_new_name': u.get('name')}}

                                    redis_pool.publish(pub_name, json.dumps(pdr_data_unit))

                        station.rated_power = rated_power
                        station.rated_capacity = round(rated_capacity, 2)
                        station.ini_num = ';'.join(ini_num)
                        station.save()
                        project_rated_power += station.rated_power
                        project_rated_capacity += round(station.rated_capacity, 2)
                        if s.get('model'):
                            project_station_number += s.get('model')
                    else:
                        rated_power = 0
                        rated_capacity = 0
                        station = models.StationDetails()
                        station.station_name = s.get('name')
                        station.en_station_name = s.get('name')
                        station.db = project.english_name
                        station.app = s.get('app')
                        station.english_name = s.get('english_name')
                        if s.get('model'):
                            station.meter = s.get('model')
                            station.specifications = s.get('model')
                            station.pcs_number = s.get('model')
                            station.unit_number = s.get('model')
                            station.fire_fighting = s.get('model')
                            station.cabinet = s.get('model')
                            station.battery_cluster = s.get('model')
                        else:
                            station.meter = 0
                            station.specifications = 0
                            station.pcs_number = 0
                            station.unit_number = 0
                            station.fire_fighting = 0
                            station.cabinet = 0
                            station.battery_cluster = 0
                        station.address = project.address
                        station.en_address = project.en_address
                        station.emq_user = s.get('emq_user')
                        station.emq_pwd = s.get('emq_pwd')
                        station.emq_clid = s.get('emq_clid')
                        station.int_num = s.get('int_num')
                        station.meter_position = m.get('meter_position')
                        station.transformer_capacity = m.get('transformer_capacity') if station.english_name == master_station.english_name else 0
                        station.master_station_id = master_station.id
                        station.project_id = project.id
                        station.province_id = project.province_id
                        station.level = project.level
                        station.type = project.type
                        station.meter_type = 2
                        station.meter_count = 1
                        station.is_account = s.get('is_account', station.is_account)
                        station.meter_number = s.get('meter_number', station.meter_number)

                        # 常规模式
                        if m.get('mode') == 1:
                            station.slave = -1
                            station.pack = -1

                        # 标准主从模式
                        elif m.get('mode') == 2:
                            station.slave = slave
                            station.pack = -1
                            slave += 1

                        # 级联主从模式
                        elif m.get('mode') == 3:
                            station.slave = slave + 1
                            station.pack = -1
                            slave += 1

                        # 逻辑主从模式
                        elif m.get('mode') == 4:
                            station.slave = -1
                            station.pack = pack
                            pack += 1

                        # 其他：默认为标准模式
                        else:
                            station.slave = -1
                            station.pack = -1

                        station.save()

                        # 保存结算电表使用时间范围
                        if s.get('is_account'):
                            meter_use_time = models.StationMeterUseTime.objects.filter(station=station).first()
                            if meter_use_time:
                                meter_use_time.start_time = s.get('account_start')
                                meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                                meter_use_time.station = station
                                meter_use_time.user_id = user
                                meter_use_time.save()
                            else:
                                meter_use_time = models.StationMeterUseTime()
                                meter_use_time.start_time = s.get('account_start')
                                meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                                meter_use_time.station = station
                                meter_use_time.user_id = user
                                meter_use_time.save()

                        ini_num = []

                        # 新增单元
                        unit_number = 1
                        for u in s.get('unit_info') if s.get('unit_info') else []:
                            rated_power += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                            rated_capacity += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                            ini_num.append(u.get('english_name'))
                            unit = models.Unit()
                            unit.unit_name = '储能单元' + str(unit_number)
                            unit.en_unit_name = 'Unit ' + str(unit_number)
                            unit.english_name = 'PCS' + str(uuid.uuid4())[:16]
                            unit.unit_new_name = u.get('name')
                            unit.en_unit_new_name = u.get('name')
                            unit.user_id = user
                            unit.station_id = station.id
                            unit.v_number = VERSIONS_DICT.get(u.get('versions'), 1)
                            unit.rated_power = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                            unit.rated_capacity = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                            if station.meter == 1:
                                unit.bms = "BMS"
                                unit.pcs = "PCS"
                            else:
                                unit.bms = f"BMS{unit_number}"
                                unit.pcs = f"PCS{unit_number}"
                            unit_number += 1
                            unit.save()

                            if u.get('name'):
                                # 异步翻译: 单元
                                pdr_data_unit = {'id': unit.id,
                                                 'table': 't_unit',
                                                 'update_data': {'unit_new_name': u.get('name')}}

                                redis_pool.publish(pub_name, json.dumps(pdr_data_unit))

                        station.rated_power = rated_power
                        station.rated_capacity = round(rated_capacity, 2)
                        station.ini_num = ';'.join(ini_num)
                        station.save()

                        # 异步翻译: 从站
                        pdr_data_station = {'id': station.id,
                                            'table': 't_stations',
                                            'update_data': {'station_name': s.get('name'), 'address': project.address}}

                        redis_pool.publish(pub_name, json.dumps(pdr_data_station))

                        project_rated_power += station.rated_power
                        project_rated_capacity += round(station.rated_capacity, 2)
                        if s.get('model'):
                            project_station_number += s.get('model')

            else:
                master_station = models.MaterStation()
                master_station.name = m.get('name')
                master_station.en_name = m.get('name')
                master_station.project_id = project.id
                master_station.english_name = m.get('english_name')
                master_station.mode = m.get('mode')
                master_station.save()

                # 异步翻译：主站
                pdr_data_master = {'id': master_station.id,
                                   'table': 't_master_stations',
                                   'update_data': {'name': m.get('name')}}

                redis_pool.publish(pub_name, json.dumps(pdr_data_master))

                for master_user in user_data:
                    master_station.userdetails_set.add(master_user)
                    master_station.save()

                for s in m.get('equipment_info'):
                    rated_power = 0
                    rated_capacity = 0
                    station = models.StationDetails()
                    station.station_name = s.get('name')
                    station.en_station_name = s.get('name')
                    station.db = project.english_name
                    station.app = s.get('app')
                    station.english_name = s.get('english_name')
                    if s.get('model'):
                        station.meter = s.get('model')
                        station.specifications = s.get('model')
                        station.pcs_number = s.get('model')
                        station.unit_number = s.get('model')
                        station.fire_fighting = s.get('model')
                        station.cabinet = s.get('model')
                        station.battery_cluster = s.get('model')
                    else:
                        station.meter = 0
                        station.specifications = 0
                        station.pcs_number = 0
                        station.unit_number = 0
                        station.fire_fighting = 0
                        station.cabinet = 0
                        station.battery_cluster = 0
                    station.address = project.address
                    station.en_address = project.address
                    station.emq_user = s.get('emq_user')
                    station.emq_pwd = s.get('emq_pwd')
                    station.emq_clid = s.get('emq_clid')
                    station.int_num = s.get('int_num')
                    station.meter_position = m.get('meter_position')
                    station.transformer_capacity = m.get('transformer_capacity') if station.english_name == master_station.english_name else 0
                    station.master_station_id = master_station.id
                    station.project_id = project.id
                    station.province_id = project.province_id
                    station.level = project.level
                    station.type = project.type
                    station.meter_type = 2
                    station.meter_count = 1
                    station.is_account = s.get('is_account', station.is_account)
                    station.meter_number = s.get('meter_number', station.meter_number)

                    # 常规模式
                    if m.get('mode') == 1:
                        station.slave = -1
                        station.pack = -1

                    # 标准主从模式
                    elif m.get('mode') == 2:
                        station.slave = slave
                        station.pack = -1
                        slave += 1

                    # 级联主从模式
                    elif m.get('mode') == 3:
                        station.slave = slave + 1
                        station.pack = -1
                        slave += 1

                    # 逻辑主从模式
                    elif m.get('mode') == 4:
                        station.slave = -1
                        station.pack = pack
                        pack += 1

                    # 其他：默认为标准模式
                    else:
                        station.slave = -1
                        station.pack = -1

                    station.save()

                    # 异步翻译: 从站
                    pdr_data_station = {'id': station.id,
                                        'table': 't_stations',
                                        'update_data': {'station_name': s.get('name'), 'address': project.address}}

                    redis_pool.publish(pub_name, json.dumps(pdr_data_station))

                    # 保存结算电表使用时间范围
                    if s.get('is_account'):
                        meter_use_time = models.StationMeterUseTime.objects.filter(station=station).first()
                        if meter_use_time:
                            meter_use_time.start_time = s.get('account_start')
                            meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                            meter_use_time.station = station
                            meter_use_time.user_id = user
                            meter_use_time.save()
                        else:
                            meter_use_time = models.StationMeterUseTime()
                            meter_use_time.start_time = s.get('account_start')
                            meter_use_time.end_time = s.get('account_end') if s.get('account_end') else None
                            meter_use_time.station = station
                            meter_use_time.user_id = user
                            meter_use_time.save()

                    ini_num = []

                    # 新增单元
                    unit_number = 1
                    if s.get('unit_info'):
                        for u in s.get('unit_info'):
                            rated_power += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                            rated_capacity += UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                            ini_num.append(u.get('english_name'))
                            unit = models.Unit()
                            unit.unit_name = '储能单元' + str(unit_number)
                            unit.en_unit_name = 'Unit' + str(unit_number)
                            unit.english_name = 'PCS' + str(uuid.uuid4())[:16]
                            unit.unit_new_name = u.get('name')
                            unit.user_id = user
                            unit.station_id = station.id
                            unit.rated_power = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_power')
                            unit.rated_capacity = UNIT_VERSIONS_DICT.get(u.get('versions')).get('rated_capacity')
                            unit.v_number = VERSIONS_DICT.get(u.get('versions'), 1)
                            if station.meter == 1:
                                unit.bms = "BMS"
                                unit.pcs = "PCS"
                            else:
                                unit.bms = f"BMS{unit_number}"
                                unit.pcs = f"PCS{unit_number}"
                            unit_number += 1
                            unit.save()

                            if u.get('name'):
                                # 异步翻译: 单元
                                pdr_data_unit = {'id': unit.id,
                                                 'table': 't_unit',
                                                 'update_data': {'unit_new_name': u.get('name')}}

                                redis_pool.publish(pub_name, json.dumps(pdr_data_unit))

                    station.rated_power = rated_power
                    station.rated_capacity = round(rated_capacity, 2)
                    station.ini_num = ';'.join(ini_num)
                    station.save()
                    project_rated_power += station.rated_power
                    project_rated_capacity += round(station.rated_capacity, 2)
                    if s.get('model'):
                        project_station_number += s.get('model')

        project.rated_power = project_rated_power
        project.rated_capacity = project_rated_capacity
        project.station_number = project_station_number
        project.save()

        # 异步翻译: 项目
        pdr_data_project = {'id': project.id,
                            'table': 't_project',
                            'update_data': {'name': validated_data.get('project_name'),
                                            'city': validated_data.get('city'),
                                            'address': project.address}}
        if validated_data.get('compant_name'):
            pdr_data_project['update_data']['compant_name'] = validated_data.get('compant_name')

        return 1