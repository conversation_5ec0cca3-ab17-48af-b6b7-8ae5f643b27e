#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:42:37
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_policy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-30 17:50:36



from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseDicPolicy(user_Base):
    u'政策情报中心记录表'
    __tablename__ = "t_side_forecase_dic_police_infos"

    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_id = Column(Integer, nullable=False, comment=u"操作人id")
    user = Column(String(256), nullable=False, comment=u"操作人名称")
    create_time = Column(DateTime, nullable=False, comment=u"日志记录时间")
    content = Column(Text, nullable=True, comment=u"内容")
    title = Column(String(256), nullable=True, comment=u"标题")
    is_top = Column(Integer, nullable=True, comment=u"是否置顶")
    see_users = Column(Text, nullable=False, comment=u"可见人id集合")
    dic_police_ids_1 = Column(Text, nullable=False, comment=u"标签类型外键")
    page_ty_name_1 = Column(Text, nullable=False, comment=u"政策箱包中心标签名称")
    dic_police_ids_2 = Column(Text, nullable=False, comment=u"标签类型外键")
    page_ty_name_2 = Column(Text, nullable=False, comment=u"政策箱包中心标签名称")
    dic_police_ids_3 = Column(Text, nullable=False, comment=u"标签类型外键")
    page_ty_name_3 = Column(Text, nullable=False, comment=u"政策箱包中心标签名称")

    org_gro = Column(Text, nullable=True, comment=u"可见部门和小组字段")
    see_org = Column(Text, nullable=True, comment=u"可见部门id")
    see_gro = Column(Text, nullable=True, comment=u"可见小组id")
    is_use = Column(CHAR(1), nullable=False, server_default='1', comment=u"是否使用1是0否默认1，2草稿箱")
    file_name = Column(Text, nullable=True, comment=u"文件名称")
    file_path = Column(Text, nullable=True, comment=u"文件路径")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'user_id':'%s','user':'%s','create_time':'%s','content':'%s','is_top':%s,'see_users':%s,'is_use':'%s','title':'%s','org_gro':'%s','file_name':'%s'," \
               "'file_path':'%s','dic_police_ids_1':'%s','dic_police_ids_2':'%s','dic_police_ids_3':'%s','page_ty_name_1':'%s','page_ty_name_2':'%s','page_ty_name_3':'%s'," \
               "'see_org':'%s','see_gro':'%s',}" \
               %(self.id,self.user_id,self.user,self.create_time,self.content,self.is_top,self.see_users,self.is_use,self.title,self.org_gro,self.file_name,self.file_path,
                 self.dic_police_ids_1,self.dic_police_ids_2,self.dic_police_ids_3,self.page_ty_name_1,self.page_ty_name_2,self.page_ty_name_3,self.see_org,self.see_gro)
