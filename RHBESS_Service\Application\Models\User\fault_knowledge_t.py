#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/11/6 下午3:51
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import (Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,
                                 Boolean, Text, SmallInteger, DATE)


class FaultKnowledgeBase(user_Base):
    """
    故障知识库记录表
    """
    __tablename__ = 't_fault_knowledge_base'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键')
    fault_entry_name = Column(String(255), nullable=False, default='', comment='条目名称')
    discover_date = Column(DATE, nullable=True, comment='发现日期')
    inclusion_date = Column(DATE, nullable=False, comment='收录/更新日期')
    fault_keyword = Column(String(600), nullable=False, comment='关键字')
    fault_type_id = Column(Integer, ForeignKey('t_fault_knowledge_dictionaries.id'), nullable=False,
                           comment='故障分类id')
    soft_hard_ware_id = Column(Integer, ForeignKey('t_fault_knowledge_dictionaries.id'), nullable=False,
                               comment='软件/硬件故障id')
    fault_level_id = Column(Integer, ForeignKey('t_fault_knowledge_dictionaries.id'), nullable=False,
                            comment='故障等级id')
    device_type = Column(String(100), nullable=False, comment='设备类型')
    manufacturer = Column(String(100), nullable=True, comment='生产厂家')
    device_model = Column(String(100), nullable=True, comment='设备型号')
    background_summarize = Column(String(600), nullable=True, comment='背景概述')
    problem_desc = Column(String(600), nullable=False, comment='问题描述')
    analysis_causes = Column(String(600), nullable=True, comment='原因分析')
    handle_measures = Column(String(600), nullable=False, comment='处理措施')
    create_user_id = Column(Integer, ForeignKey('t_user.id'), nullable=False, comment='录入人id')
    is_use = Column(SmallInteger, nullable=True, default=1, comment='是否使用，1是0否，默认1')
    create_time = Column(DateTime, nullable=False, comment='记录添加时间')

    # 定义关系
    create_user = relationship("User", backref="fault_knowledge_bases")
    fault_type = relationship("FaultKnowledgeDictionaries", foreign_keys=[fault_type_id], back_populates="fault_types")
    soft_hard_ware = relationship("FaultKnowledgeDictionaries", foreign_keys=[soft_hard_ware_id],
                                  back_populates="soft_hard_wares")
    fault_level = relationship("FaultKnowledgeDictionaries", foreign_keys=[fault_level_id],
                               back_populates="fault_levels")
    files = relationship("FaultKnowledgeFiles",
                         primaryjoin="and_(FaultKnowledgeFiles.fault_id==FaultKnowledgeBase.id, FaultKnowledgeFiles.is_use==1)",
                         back_populates="fault_knowledge_base")
    proposes = relationship("FaultKnowledgePropose", back_populates="fault_knowledge_base")


class FaultKnowledgeDictionaries(user_Base):
    """
    故障知识库字典表
    """
    __tablename__ = 't_fault_knowledge_dictionaries'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键')
    name = Column(String(100), nullable=False, comment='名称')
    type = Column(SmallInteger, nullable=False, comment='类型，1:故障分类，2:软件/硬件故障，3:故障等级')
    is_use = Column(SmallInteger, nullable=True, default=1, comment='是否使用，1是0否，默认1')
    create_time = Column(DateTime, nullable=False, comment='录入时间')

    # 定义关系
    fault_types = relationship("FaultKnowledgeBase", foreign_keys='FaultKnowledgeBase.fault_type_id', back_populates="fault_type")
    soft_hard_wares = relationship("FaultKnowledgeBase", foreign_keys='FaultKnowledgeBase.soft_hard_ware_id', back_populates="soft_hard_ware")
    fault_levels = relationship("FaultKnowledgeBase", foreign_keys='FaultKnowledgeBase.fault_level_id', back_populates="fault_level")


class FaultKnowledgeFiles(user_Base):
    """
    故障知识库附件记录表
    """
    __tablename__ = 't_fault_knowledge_files'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键')
    fault_id = Column(Integer, ForeignKey('t_fault_knowledge_base.id'), nullable=False, comment='故障知识库记录id')
    belong = Column(String(50), nullable=False, comment='附件所属，causes:原因分析，problem:问题描述，measures:处理措施')
    file_url = Column(String(255), nullable=False, comment='附件地址')
    file_name = Column(String(255), nullable=False, comment='附件名称')
    is_use = Column(SmallInteger, nullable=True, default=1, comment='是否使用，1是0否，默认1')
    create_time = Column(DateTime, nullable=False, comment='创建时间')

    # 定义关系
    fault_knowledge_base = relationship("FaultKnowledgeBase", back_populates="files")


class FaultKnowledgePropose(user_Base):
    """
    故障知识库内容建议表
    """
    __tablename__ = 't_fault_knowledge_propose'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键')
    fault_id = Column(Integer, ForeignKey('t_fault_knowledge_base.id'), nullable=False, comment='故障知识库记录id')
    propose_content = Column(String(600), nullable=False, comment='建议内容')
    propose_user_id = Column(Integer, ForeignKey('t_user.id'), nullable=False, comment='建议人id')
    is_use = Column(SmallInteger, nullable=True, default=1, comment='是否使用，1是0否，默认1')
    create_time = Column(DateTime, nullable=True, comment='创建时间')

    # 定义关系
    fault_knowledge_base = relationship("FaultKnowledgeBase", back_populates="proposes")
    create_user = relationship("User", backref="fault_knowledge_propose")
