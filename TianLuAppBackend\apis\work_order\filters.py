from django_filters import rest_framework as filters
from .models import WorkOrderModel


class WorkOderFilter(filters.FilterSet):
    status = filters.NumberFilter(field_name='status')
    work_type = filters.NumberFilter(field_name='work_type')
    todo = filters.NumberFilter(field_name='todo')
    see = filters.NumberFilter(field_name='see')

    class Meta:
        model = WorkOrderModel
        fields = ['status', 'work_type', 'todo', 'see']
