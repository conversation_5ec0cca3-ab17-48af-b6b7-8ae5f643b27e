import datetime
import decimal
import logging
import time,json
from decimal import Decimal

import requests

from apis.statistics_apis.models import StrategyApplyHistory
from apis.user import models
from common.database_pools import dwd_tables, dwd_db_tool, ads_db_tool
from settings.meter_origin_values import OriginValuesDict
from settings.meter_settings import METER_DIC
from django_redis import get_redis_connection


def get_price(station, current_date: datetime.date.today(), hour):
    """
    获取小时电价
    :param station: 站实例
    :param current_date: 日期
    :param hour: 小时
    :return:
    """
    hour_price = None
    current_month = current_date.month
    user_send = models.SummerAutomation.objects.filter(stations=station).last()  # 获取该站是否有过下发记录
    user_price = models.UnitPrice.objects.filter(stations=station, delete=0).last()  # 判断是否配置峰平谷电价
    # print("user_price有下发记录", user_price)
    # print("user_send有下发记录", user_send)
    # print("入参为:", station.station_name, current_date, hour)
    # print("user_send", user_send)
    # print("user_price", user_price)
    if user_price:
        if user_send:
            # print("有电价 有下发记录 采用下发的峰平谷标识进行计算")
            if user_price.start <= current_date < user_price.end:  # 判断时间是否在开始及截止月份中
                if user_price.summer_start <= current_month < user_price.summer_end:
                    price_dic = {
                        "price_3": float(user_price.summer_spike),
                        "price_2": float(user_price.summer_peak),
                        "price_1": float(user_price.summer_flat),
                        "price_0": float(user_price.summer_valley),
                    }
                    hour_number = getattr(user_send, f"pv{hour}")
                    hour_price = price_dic[f"price_{int(hour_number) + 1}"]
                else:
                    price_dic = {
                        "price_3": float(user_price.no_summer_spike),
                        "price_2": float(user_price.no_summer_peak),
                        "price_1": float(user_price.no_summer_flat),
                        "price_0": float(user_price.no_summer_valley),
                    }
                    hour_number = getattr(user_send, f"pv{hour}_no")
                    hour_price = price_dic[f"price_{int(hour_number) + 1}"]
                    # print("非夏季电价", hour_price)
        else:
            # print("有电价 无下发记录 采用官方峰平谷标识")
            province_ins = station.province
            peak_ins = models.PeakValley.objects.filter(year_month=current_date.month, province=province_ins, type=station.type, level=station.level)

            price_dic = {
                "price_3": float(user_price.summer_spike),
                "price_2": float(user_price.summer_peak),
                "price_1": float(user_price.summer_flat),
                "price_0": float(user_price.summer_valley),
            }
            hour_number = peak_ins.values()[0][f"pv{hour}"]
            hour_price = price_dic[f"price_{int(hour_number) + 1}"]
            # print(f"电价为{hour_price}")
    return hour_price


def new_get_price_v2(station, target_date: datetime.date.today(), peak_ins):
    """
    获取小时充电和放电电价
    :param station: 站实例
    :param target_date: 日期
    :param peak_ins: 电价数据
    :param hour: 小时
    :return:
    """

    # 判断是否存在自定义电价配置
    user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=target_date,
                                                 end__gte=target_date,
                                                 delete=0)
    if user_price.exists():
        user_price = user_price.last()

        # 暂不考虑下发的自动控制策略
        # province_ins = station.province
        # peak_ins = models.PeakValley.objects.filter(year_month=target_date.month, province=province_ins,
        #                                             type=station.type, level=station.level)

        price_dic = {
            "2_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
            "1_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
            "0_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
            "-1_chag_price": float(user_price.valley_chag_price) if user_price.valley_chag_price else '--',
            "-2_chag_price": float(user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
            "2_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
            "1_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
            "0_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
            "-1_disg_price": float(user_price.valley_disg_price) if user_price.valley_disg_price else '--',
            "-2_disg_price": float(user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
        }

        hour_number = peak_ins.pv
        hour_chag_price = price_dic[f"{str(hour_number)}_chag_price"]
        hour_disg_price = price_dic[f"{str(hour_number)}_disg_price"]
        # print(f"电价为{hour_price}")
    else:
        hour_chag_price = hour_disg_price = peak_ins.price

    return float(hour_chag_price), float(hour_disg_price)


def new_get_price(station, target_date: datetime.date.today(), hour):
    """
    获取小时电价
    :param station: 站实例
    :param target_date: 日期
    :param hour: 小时
    :return:
    """
    hour_price = None
    current_month = target_date.month

    # 获取该站是否有过下发记录
    # user_Strategy_history = StrategyApplyHistory.objects.filter(station=station)

    # 判断是否存在自定义电价配置
    user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=target_date, end__gte=target_date, delete=0)
    if user_price.exists():
        user_price = user_price.last()

        # # 下发过自动控制策略
        # if user_Strategy_history.exists():
        #     user_Strategy = user_Strategy_history.last()
        #
        #     print("有电价 有下发记录 采用下发的峰平谷标识进行计算")
        #
        #     # 判断时间是否在开始及截止月份中
        #     if user_price.start <= target_date.date() < user_price.end:
        #         if user_price.summer_start <= current_month < user_price.summer_end:
        #             price_dic = {
        #                 "price_3": float(user_price.summer_spike),
        #                 "price_2": float(user_price.summer_peak),
        #                 "price_1": float(user_price.summer_flat),
        #                 "price_0": float(user_price.summer_valley),
        #             }
        #             hour_number = getattr(user_send, f"pv{hour}")
        #             hour_price = price_dic[f"price_{int(hour_number) + 1}"]
        #         else:

                    # price_dic = {
                    #     "price_3": float(user_price.no_summer_spike),
                    #     "price_2": float(user_price.no_summer_peak),
                    #     "price_1": float(user_price.no_summer_flat),
                    #     "price_0": float(user_price.no_summer_valley),
                    # }
                    # hour_number = getattr(user_send, f"pv{hour}_no")
                    # hour_price = price_dic[f"price_{int(hour_number) + 1}"]
                    # # print("非夏季电价", hour_price)
        # else:

        # print("有电价 无下发记录 采用官方峰平谷标识")

        # 暂不考虑下发的自动控制策略
        province_ins = station.province
        peak_ins = models.PeakValley.objects.filter(year_month=target_date.month, province=province_ins, type=station.type, level=station.level)

        price_dic = {
            "price_3": float(user_price.spike_chag_price),
            "price_2": float(user_price.peak_chag_price),
            "price_1": float(user_price.flat_chag_price),
            "price_0": float(user_price.valley_chag_price),
        }
        hour_number = peak_ins.values()[0][f"pv{hour}"]         # 获取对应峰平谷标识
        hour_price = price_dic[f"price_{int(hour_number) + 1}"]
        # print(f"电价为{hour_price}")
    else:
        province_ins = station.province
        peak_ins = models.PeakValley.objects.filter(year_month=target_date.month, province=province_ins,
                                                    type=station.type, level=station.level).first()
        hour_price = getattr(peak_ins, "h" + str(hour))

    return float(hour_price)


def unit_count(unit, charge, discharge):
    """
    单元循环次数
    :param unit: 单元对象
    :param charge: 累积充电量
    :param discharge: 累积放电量
    :return:
    """
    unit_capacity = Decimal(unit["rated_capacity"])
    count = ((Decimal(charge) * Decimal(discharge)) ** Decimal(0.5)) / unit_capacity
    return_count = Decimal(count).quantize(Decimal("0"))
    return return_count


def project_count(project, charge, discharge):
    """
    计算整个项目的循环次数
    :param project: 项目
    :param charge: 充电量
    :param discharge: 放电量
    :return: 循环次数
    """
    project_capacity = Decimal(project["project__rated_capacity"])
    count = ((Decimal(charge) * Decimal(discharge)) ** Decimal(0.5)) / project_capacity
    return_count = Decimal(count).quantize(Decimal("0"))
    return return_count


def new_project_count(project, charge, discharge):
    """
    计算整个项目的循环次数
    :param project: 项目
    :param charge: 充电量
    :param discharge: 放电量
    :return: 循环次数
    """
    project_capacity = Decimal(project["rated_capacity"])
    count = ((Decimal(charge) * Decimal(discharge)) ** Decimal(0.5)) / project_capacity
    return_count = Decimal(count).quantize(Decimal("0"))
    return return_count


def station_count(station, charge, discharge):
    """
    站循环次数计算
    :param station: 站对象
    :param charge: 累积充电量
    :param discharge: 累积放电量
    :return:
    """
    station_capacity = Decimal(station["stations__rated_capacity"])
    count = ((Decimal(charge) * Decimal(discharge)) ** Decimal(0.5)) / station_capacity
    return_count = Decimal(count).quantize(Decimal("0"))
    return return_count


def new_station_count(station, charge, discharge):
    """
    站循环次数计算
    :param station: 站对象
    :param charge: 累积充电量
    :param discharge: 累积放电量
    :return:
    """
    station_capacity = Decimal(station.rated_capacity)
    count = ((Decimal(charge) * Decimal(discharge)) ** Decimal(0.5)) / station_capacity
    return_count = Decimal(count).quantize(Decimal("0"))
    return return_count


def project_day_charge_count(station, device):
    """
    项目日冲电量计算
    :param project:
    :return:
    """
    today_str = time.strftime("%Y-%m-%d", time.localtime())

    # 改查冻结表 ads_report_chag_disg_1d
    select_sql = ("SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
                  " where station=%s and station_type=2 and unit_name=%s and day=%s")

    # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
    # select_sql_2 = ("SELECT day, chag as charge, disg as discharge FROM ads_report_ems_chag_disg_1d"
    #               " where station=%s and day=%s")

    # 查询 ads_report_chag_disg_1d
    result = ads_db_tool.select_one(select_sql, station.english_name, device, today_str)

    if result:
        day_charge = result["charge"]
        day_discharge = result["discharge"]
    else:
        day_charge = 0
        day_discharge = 0

    return day_charge, day_discharge


def get_project_yesterday_charge_discharge_complete_percentage(project_id):
    conn = get_redis_connection("1") # redis链接 
    key = f'project_yesterday_charge_discharge_complete_percentage_{project_id}'
    if conn.get(key):
        v = json.loads(conn.get(key))
        return v[0],v[1]
    else:
        yesterday = datetime.date.today() - datetime.timedelta(days=1)
        yesterday_str = yesterday.strftime("%Y-%m-%d")

        stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project_id).exclude(slave=0).all()

        charge = 0
        discharge = 0
        theory_charge = 0
        theory_discharge = 0

        for station in stations:
            try:
                # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表====>改查新综合过结算表和计量表的新1d冻结表：表名未定
                select_sql = ("SELECT day, v_chag as charge, v_disg as discharge FROM ads_report_chag_disg_union_1d"
                            " where station=%s and station_type<=1 and day=%s")
                # 查询 ads_report_chag_disg_1d 获取昨日充放电量
                result = ads_db_tool.select_one(select_sql, station.english_name, yesterday_str)
                if result:
                    charge += float(result["charge"]) if result["charge"] else 0
                    discharge += float(result["discharge"]) if result['discharge'] else 0
                # 查询基准充放电量
                base_sql = ("SELECT day, base_chag as charge, base_disg as discharge FROM ads_report_station_income_1d"
                            " where station=%s and station_type<=1 and day=%s")
                base_res = ads_db_tool.select_one(base_sql, station.english_name, yesterday_str)
                if base_res:
                    theory_charge += float(base_res["charge"]) if base_res["charge"] else 0
                    theory_discharge += float(base_res["discharge"]) if base_res['discharge'] else 0
            except Exception as e:
                logging.error(e)

        # 计算充放电量完成率
        discharge_complete_percentage = round(discharge / theory_discharge * 100, 2) if theory_discharge else '--'
        charge_complete_percentage = round(charge / theory_charge * 100, 2) if theory_charge else '--'
        conn.set(key, json.dumps([charge_complete_percentage, discharge_complete_percentage]), ex=60 * 60 *6)
    return charge_complete_percentage, discharge_complete_percentage


# def day_charge_count(station, unit):
#     """
#     非标德创日充放电量计算(站)
#     :param station:站对象
#     :param unit:  单元对象
#     :return: 日冲放电量
#     """
#     url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#     today_timestamp = int(time.time())
#     request_json = {
#         "time": str(int(today_timestamp)),
#         "datatype": "cumulant",
#         "app": station["stations__app"],
#         "station": station["stations__english_name"],
#         "body": [
#             {
#                 "device": unit.bms,
#                 "body": ["PAE", "NAE"],
#             }
#         ],  # 充电量  # 放电量
#     }
#     response = requests.post(url=url, json=request_json)
#     datas = response.json()["datas"]
#     first_pae,first_nae,last_pae,last_nae = 0,0,0,0
#     if datas:
#         sorter_datas = sorted(datas, key=lambda x: x["body"]["time"])
#         if "PAE" in sorter_datas[0]["body"]["data"][0]:
#             first_pae = sorter_datas[0]["body"]["data"][0]["PAE"] if 'n' not in sorter_datas[0]["body"]["data"][0]["PAE"] else 0
#             first_nae = sorter_datas[0]["body"]["data"][0]["NAE"] if 'n' not in sorter_datas[0]["body"]["data"][0]["NAE"] else 0
#         if "PAE" in sorter_datas[-1]["body"]["data"][0]:
#             last_pae = sorter_datas[-1]["body"]["data"][0]["PAE"] if 'n' not in sorter_datas[-1]["body"]["data"][0]["PAE"] else 0
#             last_nae = sorter_datas[-1]["body"]["data"][0]["NAE"] if 'n' not in sorter_datas[-1]["body"]["data"][0]["NAE"] else 0
#
#     day_discharge = abs(decimal.Decimal(last_pae) - decimal.Decimal(first_pae))
#     day_charge = abs(decimal.Decimal(last_nae) - decimal.Decimal(first_nae))
#     return day_charge, day_discharge


# def day_charge_count_unit(station_instance, unit):
#     """
#     非标德创日充放电量计算(单元)
#     :param station:站对象
#     :param unit:  单元对象
#     :return: 日冲放电量
#     """
#     url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#     today_timestamp = int(time.time())
#     request_json = {
#         "time": str(int(today_timestamp)),
#         "datatype": "cumulant",
#         "app": station_instance.app,
#         "station": station_instance.english_name,
#         "body": [
#             {
#                 "device": unit["bms"],
#                 "body": ["PAE", "NAE"],
#             }
#         ],  # 充电量  # 放电量
#     }
#     response = requests.post(url=url, json=request_json)
#     datas = response.json()["datas"]
#     day_charge,day_discharge = 0,0
#     if datas:
#         sorter_datas = sorted(datas, key=lambda x: x["body"]["time"])
#         first_pae,first_nae,last_pae,last_nae = 0,0,0,0
#         if "PAE" in sorter_datas[0]["body"]["data"][0]:
#             first_pae = sorter_datas[0]["body"]["data"][0]["PAE"] if 'n' not in sorter_datas[0]["body"]["data"][0]["PAE"] else 0
#             first_nae = sorter_datas[0]["body"]["data"][0]["NAE"] if 'n' not in sorter_datas[0]["body"]["data"][0]["NAE"] else 0
#         if "PAE" in sorter_datas[-1]["body"]["data"][0]:
#             last_pae = sorter_datas[-1]["body"]["data"][0]["PAE"] if 'n' not in sorter_datas[-1]["body"]["data"][0]["PAE"] else 0
#             last_nae = sorter_datas[-1]["body"]["data"][0]["NAE"] if 'n' not in sorter_datas[-1]["body"]["data"][0]["NAE"] else 0
#         day_charge = abs(decimal.Decimal(last_nae) - decimal.Decimal(first_nae))
#         day_discharge = abs(decimal.Decimal(last_pae) - decimal.Decimal(first_pae))
#     return day_charge, day_discharge


def unit_convert(count, unit):
    """
    kW, kWh 转换 MW, MWh
    :param count: 数量
    :param unit: 单位
    :return: 转换后数量及单位
    """
    covert_dic = {"kW": "MW", "kWh": "MWh", "MWh": "GWh"}
    count = Decimal(count)
    if count >= 1000:
        count = Decimal(count / 1000).quantize(Decimal("0.00"))
        unit = covert_dic[unit]
    return count, unit


def mqtt_request_data(station_ins, datetime_):
    """
    动态获取 mqtt 请求参数
    :param station_ins:站实例
    :datetime_ 时间对象
    :return: requests 参数
    """
    timestamp = int(datetime_.timestamp())
    print("站名:", station_ins.station_name)
    meter_type = station_ins.meter_type
    meter_dic = METER_DIC.get(meter_type)
    charge = meter_dic.get("charge")
    discharge = meter_dic.get("discharge")
    device = meter_dic.get("device").upper()
    request_json = {
        "time": str(timestamp),
        "datatype": "cumulant",
        "app": station_ins.app,
        "station": station_ins.english_name,
        "body": [],
    }

    unit_count = models.Unit.objects.filter(is_delete=0, station=station_ins).count()

    if unit_count == 1:
        request_json["body"].append({"device": device, "body": [charge, discharge]})
    else:
        for i in range(unit_count):
            request_json["body"].append({"device": f"{device}{i + 1}", "body": [charge, discharge]})
    print("请求型号:", unit_count, "请求参数:", request_json)
    return request_json, charge, discharge


def mqtt_income(station_ins, datas, current_date, peak_ins, charge, discharge):
    """
    动态获取 mqtt 收益
    :param station_ins: 站实例
    :param datas: mqtt 查询数据
    :param current_date: 日期
    :param peak_ins: 收益实例
    :param charge: 日冲
    :param discharge: 日放
    :return: 站收益
    """
    meter_type = station_ins.meter_type
    meter_dic = METER_DIC.get(meter_type)
    charge = meter_dic.get("charge")
    discharge = meter_dic.get("discharge")

    units = station_ins.unit_set.filter(is_delete=0).all()
    # for unit in units:

    CuCha_list = []
    CuDis_list = []
    new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
    new_list = new_list_[::2]
    unit_count = models.Unit.objects.filter(is_delete=0, station=station_ins).count()
    new_list.append(new_list_[-1])
    for i in range(len(new_list) - 1):
        price = get_price(station_ins, current_date, i)
        # print("自定义电价为:", price)
        if not price:
            price = float(peak_ins.values()[0][f"h{i}"])

        for j in range(unit_count):
            later_d = new_list[i + 1]["body"]["data"][j]
            before_d = new_list[i]["body"]["data"][j]
            if discharge not in later_d.keys() or charge not in later_d.keys() or discharge not in before_d.keys() or charge not in before_d.keys() :
                print("数据异常later_d:", later_d, "----:", before_d)
                continue

            CuDis_list.append(
                abs(abs(float(later_d[discharge])) - abs(float(before_d[discharge]))) * price
            )

            CuCha_list.append(
                abs(abs(float(later_d[charge])) - abs(float(before_d[charge]))) * price
            )
    total = sum(CuDis_list) - sum(CuCha_list)
    print(f"定时任务收益计算完成================ {station_ins.english_name}总收益为:{total}元")
    return total


def get_station_price(station, target_date: datetime.date):
    """
    获取某天的尖峰平谷充放电价
    :param station: 站实例
    :param current_date: 日期
    :return:
    """""
    # 判断是否存在自定义电价配置
    user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=target_date, end__gte=target_date,
                                                 delete=0)
    if user_price.exists():
        user_price = user_price.last()

        # 暂不考虑下发的自动控制策略
        # province_ins = station.province
        # peak_ins = models.PeakValley.objects.filter(year_month=target_date.month, province=province_ins,
        #                                             type=station.type, level=station.level)

        price_dic = {
            "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else 0,
            "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else 0,
            "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else 0,
            "valley_chag_price": float(user_price.valley_chag_price) if user_price.valley_chag_price else 0,
            "dvalley_chag_price": float(user_price.dvalley_chag_price) if user_price.dvalley_chag_price else 0,
            "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else 0,
            "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else 0,
            "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else 0,
            "valley_disg_price": float(user_price.valley_disg_price) if user_price.valley_disg_price else 0,
            "dvalley_disg_price": float(user_price.dvalley_disg_price) if user_price.dvalley_disg_price else 0
        }

    else:
        price_dic = {
            "spike_chag_price": 0,
            "peak_chag_price": 0,
            "flat_chag_price": 0,
            "valley_chag_price": 0,
            "dvalley_chag_price": 0,
            "spike_disg_price": 0,
            "peak_disg_price": 0,
            "flat_disg_price": 0,
            "valley_disg_price": 0,
            "dvalley_disg_price": 0
        }

        day_prices = models.PeakValleyNew.objects.filter(province=station.province, type=station.type,
                                                        level=station.level, year_month=target_date.strftime("%Y-%m")
                                                         ).all()
        if day_prices.exists():
            # 尖
            spike_price = day_prices.filter(pv=2).first()
            price_dic['spike_chag_price'] = float(spike_price.price) if spike_price else 0
            price_dic['spike_disg_price'] = float(spike_price.price) if spike_price else 0

            # 峰
            peak_price = day_prices.filter(pv=1).first()
            price_dic['peak_chag_price'] = float(peak_price.price) if peak_price else 0
            price_dic['peak_disg_price'] = float(peak_price.price) if peak_price else 0

            # 平
            flat_price = day_prices.filter(pv=0).first()
            price_dic['flat_chag_price'] = float(flat_price.price) if flat_price else 0
            price_dic['flat_disg_price'] = float(flat_price.price) if flat_price else 0

            # 谷
            valley_price = day_prices.filter(pv=-1).first()
            price_dic['valley_chag_price'] = float(valley_price.price) if valley_price else 0
            price_dic['valley_disg_price'] = float(valley_price.price) if valley_price else 0

            # 深谷
            dvalley_price = day_prices.filter(pv=-2).first()
            price_dic['dvalley_chag_price'] = float(dvalley_price.price) if dvalley_price else 0
            price_dic['dvalley_disg_price'] = float(dvalley_price.price) if dvalley_price else 0

    return price_dic



def get_station_price_optimize(station, target_date: datetime.date, price_new_dict):
    """
    获取某天的尖峰平谷充放电价
    :param station: 站实例
    :param current_date: 日期
    :param price_new_dict: 当日全部电价
    :return:
    """""
    # 判断是否存在自定义电价配置
    user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=target_date, end__gte=target_date,
                                                 delete=0)
    if user_price.exists():
        user_price = user_price.last()

        # 暂不考虑下发的自动控制策略
        # province_ins = station.province
        # peak_ins = models.PeakValley.objects.filter(year_month=target_date.month, province=province_ins,
        #                                             type=station.type, level=station.level)

        price_dic = {
            "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else 0,
            "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else 0,
            "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else 0,
            "valley_chag_price": float(user_price.valley_chag_price) if user_price.valley_chag_price else 0,
            "dvalley_chag_price": float(user_price.dvalley_chag_price) if user_price.dvalley_chag_price else 0,
            "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else 0,
            "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else 0,
            "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else 0,
            "valley_disg_price": float(user_price.valley_disg_price) if user_price.valley_disg_price else 0,
            "dvalley_disg_price": float(user_price.dvalley_disg_price) if user_price.dvalley_disg_price else 0
        }

    else:
        price_dic = {
            "spike_chag_price": 0,
            "peak_chag_price": 0,
            "flat_chag_price": 0,
            "valley_chag_price": 0,
            "dvalley_chag_price": 0,
            "spike_disg_price": 0,
            "peak_disg_price": 0,
            "flat_disg_price": 0,
            "valley_disg_price": 0,
            "dvalley_disg_price": 0
        }

        day_prices = price_new_dict.get(f"{station.province_id}-{station.type}-{station.level}")
        if day_prices:
            # 尖
            spike_price = day_prices.get(2)
            price_dic['spike_chag_price'] = float(spike_price) if spike_price else 0
            price_dic['spike_disg_price'] = float(spike_price) if spike_price else 0

            # 峰
            peak_price = day_prices.get(1)
            price_dic['peak_chag_price'] = float(peak_price) if peak_price else 0
            price_dic['peak_disg_price'] = float(peak_price) if peak_price else 0

            # 平
            flat_price = day_prices.get(0)
            price_dic['flat_chag_price'] = float(flat_price) if flat_price else 0
            price_dic['flat_disg_price'] = float(flat_price) if flat_price else 0

            # 谷
            valley_price = day_prices.get(-1)
            price_dic['valley_chag_price'] = float(valley_price) if valley_price else 0
            price_dic['valley_disg_price'] = float(valley_price) if valley_price else 0

            # 深谷
            dvalley_price = day_prices.get(-2)
            price_dic['dvalley_chag_price'] = float(dvalley_price) if dvalley_price else 0
            price_dic['dvalley_disg_price'] = float(dvalley_price) if dvalley_price else 0

    return price_dic