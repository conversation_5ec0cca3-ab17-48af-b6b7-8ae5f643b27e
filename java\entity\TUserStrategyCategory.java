package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户策略分类表
 * 对应Python模型: TUserStrategyCategory
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_user_strategy_category")
public class TUserStrategyCategory extends SuperEntity {

    /**
     * 分类名称
     */
    @TableField("name")
    private String name;

    /**
     * 策略ID
     */
    @TableField("strategy_id")
    private Long strategyId;

    /**
     * 充电配置(JSON格式)
     */
    @TableField("charge_config")
    private String chargeConfig;

    /**
     * 是否跟随: 1-是, 0-否
     */
    @TableField("is_follow")
    private Integer isFollow;

    /**
     * RL列表(JSON格式)
     */
    @TableField("rl_list")
    private String rlList;

    /**
     * 解释JSON(JSON格式)
     */
    @TableField("explain_json")
    private String explainJson;

    /**
     * PV列表(JSON格式)
     */
    @TableField("pv_list")
    private String pvList;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
