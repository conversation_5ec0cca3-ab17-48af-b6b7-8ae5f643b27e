#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\redis_con.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-08-08 13:41:23

import redis
from Application.Cfg.dir_cfg import model_config
_pool = redis.ConnectionPool(host=model_config.get('redis', "HOSTNAME"),
                            port=model_config.get('redis', "PORT"),
                            db=model_config.get('redis', "DB"),
                            username=model_config.get('redis', "USERNAME"),
                            password=model_config.get('redis', "PASSWORD"),
                            decode_responses=False, encoding='UTF-8')
r = redis.Redis(connection_pool=_pool)

real_pool = redis.ConnectionPool(host=model_config.get('realredis', "HOSTNAME"),
                            port=model_config.get('realredis', "PORT"),
                            db=model_config.get('realredis', "DB"),
                            username=model_config.get('realredis', "USERNAME"),
                            password=model_config.get('realredis', "PASSWORD"),
                            decode_responses=False, encoding='UTF-8')
r_real = redis.Redis(connection_pool=real_pool)

matlab_pool = redis.ConnectionPool(host=model_config.get('realredismatlab', "HOSTNAME"),
                            port=model_config.get('realredismatlab', "PORT"),
                            db=model_config.get('realredismatlab', "DB"),
                            username=model_config.get('realredismatlab', "USERNAME"),
                            password=model_config.get('realredismatlab', "PASSWORD"),
                            decode_responses=False, encoding='UTF-8')
r_matlab = redis.Redis(connection_pool=matlab_pool)