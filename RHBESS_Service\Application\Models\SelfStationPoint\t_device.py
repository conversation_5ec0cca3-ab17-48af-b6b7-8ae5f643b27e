#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 16:32:13
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_device.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:21:59


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_station import StationPT

class DevicePT(mqtt_Base):
    ''' 设备说明表 '''
    __tablename__ = "t_device"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    station_id = Column(Integer, ForeignKey("t_station.id"),nullable=False, comment=u"所属站")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    

    station_device = relationship("StationPT",backref='station_device')

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s'}" % (self.id,self.name,self.descr)