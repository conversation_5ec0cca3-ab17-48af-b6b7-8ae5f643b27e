#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-18 09:59:17
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WebSocket\chathandler.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-18 17:41:58


#!/usr/bin/env python
# coding=utf-8
#@Information:websocket
#<AUTHOR> WYJ
#@Date         : 2022-11-09 09:00:56
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WorkOrder\workOrderHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-09 09:02:53

import datetime
import json
import logging
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import WebSocketBase<PERSON>and<PERSON>, BaseHandler
from Tools.DB.redis_con import r
from Application.Models.User.user import User
from Application.Models.WebSocket.user_message import UserMessage
from Application.Models.WebSocket.message import Message

def event_handler(msg):
    if msg.get('data'):
        # 推送UserMessage表的id
        user_msg_id, push_type = msg.get('data').split('+')
        try:
            user_message = user_session.query(UserMessage).filter(UserMessage.id == user_msg_id).first()
            if user_message:
                msg = user_session.query(Message).filter(Message.id == user_message.message_id).first()
                EchoWebSocket.push_user(msg, user_message.user_id, user_message.read, push_type)
        except Exception as E:
            logging.info('消息格式错误')


pub = r.pubsub()
pub.subscribe(**{'rhbess_ws': event_handler})
pub.run_in_thread(sleep_time=0.1, daemon=True)


class EchoWebSocket(WebSocketBaseHandler):
    user_road = {}

    def open(self):
        try:
            logging('websocket 连接已建立')
            _session = self.getSession()
            if not _session:
                self.close()
            user = _session.user
            user = user_session.query(User).filter(User.id == user['id']).first()
            if str(user['id']) in EchoWebSocket.user_road:
                EchoWebSocket.user_road[str(user['id'])].close()
            EchoWebSocket.user_road[str(user['id'])] = self

            user_message_ids = user_session.query(UserMessage.message_id, UserMessage.read).filter(UserMessage.user_id == user['id']).all()
            msg_dict = {i[0]: i[1] for i in user_message_ids}
            message_list = user_session.query(Message).filter(Message.id.in_([i[0] for i in user_message_ids])).order_by(-Message.id).all()
            message_data = {"type": 'messages', 'data': [{'type': msg.type, 'msg': msg.descr, 'id': msg.id, 'read': msg_dict.get(msg.id),
                                            'timestamp': msg.op_ts.strftime("%Y-%m-%d %H:%M:%S")} for msg in message_list]}
            self.write_message(message_data)
            
        except Exception as E:
            logging.error(E)
            user_session.rollback()
       

    def on_message(self, message):
        pass
        # 与客户端进行交互
        # push_user_message(1, '测试', 'info', datetime.datetime.now())
        

    @staticmethod
    def push_user(message, user_id, read=False, push_type='new_msg'):
        """为对应用户推送消息
        :message 消息对象
        :user_id 用户id
        :read 是否已读 默认未读
        :msg_type notification(公告)/new_msg(新消息）
        """
        user_id = str(user_id)
        if user_id in EchoWebSocket.user_road.keys():
            EchoWebSocket.user_road.get(user_id).write_message({'type': push_type, 'data': {'type': message.type,
                            'read': read, 'msg': message.descr, 'id': message.id, 'timestamp': message.op_ts.strftime(
                                                                 "%Y-%m-%d %H:%M:%S")}})

    # @tornado.web.authenticated
    def on_close(self):
        _session = self.getSession()
        if _session:
            user = _session.user
            if str(user['id']) in EchoWebSocket.user_road:
                del EchoWebSocket.user_road[str(user['id'])]

    def check_origin(self, origin):
        return True  # 允许WebSocket的跨域请求








