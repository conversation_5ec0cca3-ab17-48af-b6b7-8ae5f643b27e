#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-02-15 10:47:27
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\HaLun\fault.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-22 16:05:41
import json
import os
import logging
import tornado.web
from sqlalchemy import func
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.User.doc_t import Tdoc
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import Translate_cls
import uuid

file_title = ['文档编号','文档名称','文档格式','归属组织','描述','上传日期','上传人员']

class KnowledgeBaseIntetface(BaseHandler):
    ''' 知识库功能 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)
        try:
            data = []
            if kt == 'GetHelpDocumentList': # 所有帮助文档记录
                doc_number = self.get_argument('number',None)
                doc_name = self.get_argument('name',None)
                start_time = self.get_argument('start_time', None)
                end_time = self.get_argument('end_time', None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'
                filter = [Tdoc.is_use==1]
                if DEBUG:
                    logging.info('doc_number:%s,doc_name:%s,start_time:%s,end_time:%s,pageNum:%s,pageSize:%s'
                                 %(doc_number,doc_name,start_time,end_time,pageNum,pageSize))
                if lang == 'zh':
                    if doc_name:
                        filter.append(Tdoc.doc_name.like('%' + doc_name + '%'))
                else:
                    if doc_name:
                        filter.append(Tdoc.en_doc_name.like('%' + doc_name + '%'))

                if doc_number:
                    filter.append(Tdoc.doc_number.like('%' + doc_number + '%'))

                if start_time:
                    filter.append(Tdoc.date_upload >= start_time[:10])
                if end_time:
                    filter.append(Tdoc.date_upload <= end_time[:10])


                total = user_session.query(func.count(Tdoc.id)).filter(*filter).scalar()
                pages = user_session.query(Tdoc).filter(*filter).order_by(Tdoc.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if lang == 'en':
                    for pag in pages:
                        pag = pag.__dict__
                        pag['doc_name'] = pag.get('en_doc_name')
                        pag['owning_organization'] = pag.get('en_owning_organization')
                        pag['description'] = pag.get('en_description')
                        pag['uploader'] = pag.get('en_uploader')
                        pag['file_name'] = pag.get('en_file_name')
                        del pag['_sa_instance_state']
                        data.append(pag)
                else:
                    for pag in pages:
                        pag = pag.__dict__
                        del pag['_sa_instance_state']
                        data.append(pag)
                return self.returnTotalSuc(data,total)

            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)
        try:
            if kt == 'AddDoc': # 添加文档记录
                doc_number = self.get_argument('number', None)
                doc_name = self.get_argument('name', None)
                owning_organization = self.get_argument('organization', None)
                description = self.get_argument('description', None)
                if not lang:
                    lang = 'zh'
                if not doc_number or not doc_name or not owning_organization :
                    return self.customError('参数不完整') if lang == 'zh' else self.customError("Incomplete input parameters!")
                description = ';'.join(description.split()) if description else ''

                if DEBUG:
                    logging.info('doc_number:%s,doc_name:%s,owning_organization:%s,description:%s'%(doc_number,doc_name,owning_organization,description))

                p= self._document_exist(doc_number)
                if p:
                    logging.info("文档编号重复")
                    return self.customError('文档编号重复') if lang == 'zh' else self.customError("Document number is duplicated")

                files = self.request.files

                file_path = '/home/<USER>/' + timeUtils.getNewTimeStr()[:4]
                # file_path = '/home/<USER>/document/' + station
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)


                imgs = files.get('file')
                data = imgs[0].get('body')
                filename = imgs[0].get('filename')

                doc_format = str(os.path.splitext(filename)[1])  # 格式

                uploadfilename = str(uuid.uuid1()) + doc_format
                path = '%s/%s' % (file_path, uploadfilename)
                file = open(path, 'wb')
                file.write(data)
                file.close()

                session = self.getOrNewSession()
                uploader = session.user['name']
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                filename_res = t_cls.str_chinese(filename)
                doc_name_res = t_cls.str_chinese(doc_name)
                owning_organization_res = t_cls.str_chinese(owning_organization)
                description_res = t_cls.str_chinese(description)
                uploader_res = t_cls.str_chinese(uploader)
                if ty == 2:
                    en_file_name = filename_res
                    en_doc_name = doc_name_res
                    en_owning_organization = owning_organization_res
                    en_description = description_res
                    en_uploader = uploader_res
                else:
                    en_file_name = filename
                    en_doc_name = doc_name
                    en_owning_organization = owning_organization
                    en_description = description
                    en_uploader = uploader
                    filename = filename_res
                    doc_name = doc_name_res
                    owning_organization = owning_organization_res
                    description = description_res
                    uploader = uploader_res

                p = Tdoc(file_name=filename,doc_url=path,doc_number=doc_number,doc_name=doc_name,doc_format=doc_format,owning_organization=owning_organization,description=description,uploader=uploader,is_use='1',date_upload=timeUtils.getNewTimeStr()[:10],
                         en_file_name=en_file_name,en_doc_name=en_doc_name,en_uploader=en_uploader,en_description=en_description,en_owning_organization=en_owning_organization)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UpdateDoc': # 修改记录
                id = self.get_argument('id',None)
                doc_number = self.get_argument('number',None)
                doc_name = self.get_argument('name',None)
                owning_organization = self.get_argument('organization',None)
                description = self.get_argument('description',None)
                if not lang:
                    lang = 'zh'
                if not doc_number or not doc_name or not owning_organization :
                    return self.customError('参数不完整') if lang == 'zh' else self.customError("Incomplete parameters")

                description = ';'.join(description.split()) if description else ''
                if DEBUG:
                    logging.info('id:%s,doc_number:%s,doc_name:%s,owning_organization:%s,description:%s'%(id,
                                doc_number,doc_name,owning_organization,description))

                p = self._document_exist(doc_number, id)
                if p:
                    logging.info("重复数据项")
                    return self.customError('重复数据项') if lang == 'zh' else self.customError("Duplicate data item")

                page = user_session.query(Tdoc).filter(Tdoc.id==id).first()
                path_ = page.doc_url
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)

                if not path_:
                    files = self.request.files
                    # file_path = '/home/<USER>/' + station
                    file_path = '/home/<USER>/' + timeUtils.getNewTimeStr()[:4]

                    # file_path = '/home/<USER>/document/' + station
                    if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                        os.makedirs(file_path)
                    imgs = files.get('file')
                    data = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    path = '%s/%s' % (file_path, uploadfilename)
                    file = open(path, 'wb')
                    file.write(data)
                    file.close()
                    filename_res = t_cls.str_chinese(filename)
                    if ty == 2:
                        en_file_name = filename_res
                    else:
                        en_file_name = filename
                        filename = filename_res

                    page.file_name = filename
                    page.en_file_name = en_file_name
                    page.doc_format = doc_format
                    page.doc_url = path


                session = self.getOrNewSession()
                uploader = session.user['name']
                doc_name_res = t_cls.str_chinese(doc_name)
                owning_organization_res = t_cls.str_chinese(owning_organization)
                description_res = t_cls.str_chinese(description)
                uploader_res = t_cls.str_chinese(uploader)
                if ty == 2:
                    en_doc_name = doc_name_res
                    en_owning_organization = owning_organization_res
                    en_description = description_res
                    en_uploader = uploader_res
                else:
                    en_doc_name = doc_name
                    doc_name = doc_name_res
                    en_owning_organization = owning_organization
                    owning_organization = owning_organization_res
                    en_description = description
                    description = description_res
                    en_uploader = uploader
                    uploader = uploader_res

                page.doc_number = doc_number
                page.doc_name = doc_name
                page.en_doc_name = en_doc_name
                page.owning_organization = owning_organization
                page.en_owning_organization = en_owning_organization

                page.description = description
                page.en_description = en_description
                page.uploader = uploader
                page.en_uploader = en_uploader
                page.date_upload = timeUtils.getNewTimeStr()[:10]

                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')

            elif kt == 'DeleteDoc':  # 删除文档
                id = self.get_argument('id', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s' % (id))

                page = user_session.query(Tdoc).filter(Tdoc.id == id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                try:
                    os.remove(page.doc_url)
                except:
                    logging.error('未找到%s文件' % page.doc_url)
                page.doc_url = None
                page.doc_format = None
                page.file_name = None
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')

            elif kt == 'LogicDeleteDoc': # 逻辑删除文档
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s'%(id))

                page = user_session.query(Tdoc).filter(Tdoc.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
               
                page.is_use = 0
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()


    def _document_exist(self, doc_number,id = None):
        '''判断文档是否存在'''
        filter = [Tdoc.doc_number == doc_number, Tdoc.is_use == 1]
        if id:
            filter.append(Tdoc.id != id)
        page = user_session.query(Tdoc).filter(*filter).first()
        if page:
            return True
        else:
            return False