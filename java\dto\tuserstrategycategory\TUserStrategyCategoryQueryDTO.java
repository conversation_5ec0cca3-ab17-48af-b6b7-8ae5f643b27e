package com.robestec.analysis.dto.tuserstrategycategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户策略分类查询DTO
 */
@Data
@ApiModel("用户策略分类查询DTO")
public class TUserStrategyCategoryQueryDTO {

    @ApiModelProperty("分类名称")
    private String name;

    @ApiModelProperty("策略ID")
    private Long strategyId;

    @ApiModelProperty("是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
