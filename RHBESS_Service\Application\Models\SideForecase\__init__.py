#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:04:58
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\__init__.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 19:30:42



def create_all():
    u'初始化所有表'

    from Application.Models.SideForecase.side_forecase_dict_capacity import ForecaseDictCapacity
    from Application.Models.SideForecase.side_forecase_dict_device_type import ForecaseDictDeviceType
    from Application.Models.SideForecase.side_forecase_dict_dod import ForecaseDictDOD
    from Application.Models.SideForecase.side_forecase_dict_eff_chagdisg import ForecaseDictEffChagDisg
    from Application.Models.SideForecase.side_forecase_dict_firm_type import ForecaseDictFrimType
    from Application.Models.SideForecase.side_forecase_dict_indus_one import ForecaseDictIndesOne
    from Application.Models.SideForecase.side_forecase_dict_indus_two import ForecaseDictIndesTwo
    from Application.Models.SideForecase.side_forecase_dict_own_type import ForecaseDictOwnType
    from Application.Models.SideForecase.side_forecase_dict_project_sign import ForecaseDictProjectsign
    from Application.Models.SideForecase.side_forecase_dict_project_type import ForecaseDictProjectType
    from Application.Models.SideForecase.side_forecase_dict_runday_num import ForecaseDictRundayNum
    from Application.Models.SideForecase.side_forecase_dict_runday_one import ForecaseDictRundayOne
    from Application.Models.SideForecase.side_forecase_dict_runday_two import ForecaseDictRundayTwo
    from Application.Models.SideForecase.side_forecase_dict_share_emc import ForecaseDictShareEmc
   
    from Application.Models.SideForecase.side_forecase_price_files import ForecasePriceFiles
    from Application.Models.SideForecase.side_forecase_part import ForecasePart
    from Application.Models.SideForecase.side_forecase_area import ForecaseArea
    from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
    from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
    from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
    from Application.Models.SideForecase.side_forecase_price import ForecasePrice
    from Application.Models.SideForecase.side_forecase_market_price import ForecaseMarketPrice
    from Application.Models.SideForecase.side_forecase_customer import ForecaseCustomer
    from Application.Models.SideForecase.side_forecase_proele import ForecaseProele

    from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
    from Application.Models.SideForecase.side_forecase_policy import ForecasePolicy
    from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
    from Application.Models.SideForecase.side_forecase_files import ForecaseFiles
    from Application.Models.SideForecase.side_forecase_group import ForecaseGroup
    from Application.Models.SideForecase.side_forecase_role import ForecaseRole
    from Application.Models.SideForecase.side_forecase_project import ForecaseProject
    from Application.Models.SideForecase.side_forecase_organization import ForecaseOrganization
    from Application.Models.SideForecase.side_forecase_user import ForecaseUser
    from Application.Models.SideForecase.side_forecase_first_info import ForecaseFirstInfo
    from Application.Models.SideForecase.side_forecase_authority import ForecaseAuthority
    from Application.Models.SideForecase.side_forecase_fastmatch_model import ForecaseFastmatchModel
    from Application.Models.SideForecase.side_forecase_fastmatch_other import ForecaseFastmatchOther
    from Application.Models.SideForecase.side_forecase_useele_ele_info import ForecaseUseEleEleInfo
    from Application.Models.SideForecase.side_forecase_useele_ele_file import ForecaseUseEleEleFile
    from Application.Models.SideForecase.side_forecase_useele_load_offer import ForecaseUseEleEleLoadOffer
    from Application.Models.SideForecase.side_forecase_calculate import ForecaseCalcuate
    from Application.Models.SideForecase.side_forecase_dict_policy_infos import ForecaseDicPolicy
    from Application.Models.SideForecase.side_forecase_dict_policy_type import ForecaseDicPolicyType
    from Application.Models.SideForecase.side_forecase_dict_log import ForecaseDicLog
    from Application.Models.SideForecase.side_forecase_handle_logs import ForecaseHandleLogs


    ForecaseDictCapacity.init()
    ForecaseDictDeviceType.init()
    ForecaseDictDOD.init()
    ForecaseDictEffChagDisg.init()
    ForecaseDictFrimType.init()
    ForecaseDictIndesOne.init()
    ForecaseDictIndesTwo.init()
    ForecaseDictOwnType.init()
    ForecaseDictProjectsign.init()
    ForecaseDictProjectType.init()
    ForecaseDictRundayNum.init()
    ForecaseDictRundayOne.init()
    ForecaseDictRundayTwo.init()
    ForecaseDictShareEmc.init()
    ForecasePriceFiles.init()

    ForecasePart.init()
    ForecaseArea.init()
    ForecaseEle.init()
    ForecaseVol.init()
    ForecaseProvince.init()
    ForecasePrice.init()
    ForecaseMarketPrice.init()
    ForecaseCustomer.init()
    ForecaseProele.init()
    ForecasePolicyType.init()
    ForecasePolicy.init()
    ForecaseBaseFile.init()
    ForecaseFiles.init()
    ForecaseGroup.init()
    ForecaseRole.init()
    ForecaseOrganization.init()
    ForecaseUser.init()
    ForecaseProject.init()
    ForecaseAuthority.init()
    ForecaseFirstInfo.init()
    ForecaseFastmatchModel.init()
    ForecaseFastmatchOther.init()
    ForecaseUseEleEleInfo.init()
    ForecaseUseEleEleFile.init()
    ForecaseUseEleEleLoadOffer.init()
    ForecaseCalcuate.init()
    ForecaseDicPolicyType.init()
    ForecaseDicPolicy.init()
    ForecaseDicLog.init()
    ForecaseHandleLogs.init()




    

   




    


