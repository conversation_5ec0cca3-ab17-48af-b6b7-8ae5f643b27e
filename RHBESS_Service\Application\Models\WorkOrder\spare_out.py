#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-03-14 17:04:25
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WorkOrder\spare_out.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-16 15:27:17


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.WorkOrder.spare_info import SpareInfo

class SpareOut(user_Base):
    u'备件出库表'
    __tablename__ = "t_spare_out"
    id = Column(Integer, ForeignKey("t_spare_info.id"), primary_key=True,comment=u"主键")
    op_ts = Column(DateTime, nullable=False,primary_key=True,comment=u"时间")
    num = Column(Integer, nullable=False,comment=u"领取数量")
    out_source= Column(Text, nullable=False,comment=u"领取用途")
    en_out_source= Column(Text, nullable=False,comment=u"领取用途-英文")
    out_time= Column(DateTime, nullable=False,comment=u"领取时间")
    out_descr = Column(String(256), nullable=False, comment=u"领取登记人")
    en_out_descr = Column(String(256), nullable=False, comment=u"领取登记人-英文")
    remarks = Column(Text, nullable=True, comment=u"备注")
    en_remarks = Column(Text, nullable=True, comment=u"备注-英文")
    create_descr = Column(String(256), nullable=False,comment=u"创建者")
    en_create_descr = Column(String(256), nullable=False,comment=u"创建者-英文")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")

    spare_info_out= relationship("SpareInfo", backref="spare_info_out")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'id':%s,'num':%s,'source':'%s','time':'%s','descr':'%s','remark':'%s','op_ts':'%s','en_out_source':'%s','en_out_descr':'%s','en_remarks':'%s'}" % (self.id,self.num,self.out_source,self.out_time,self.out_descr,
                self.remarks,self.op_ts,self.en_out_source,self.en_out_descr,self.en_remarks)
        return bean.replace("None",'')
        
