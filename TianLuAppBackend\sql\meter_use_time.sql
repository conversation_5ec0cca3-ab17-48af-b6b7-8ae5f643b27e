CREATE TABLE `t_meter_use_time_station` (
  `id` int NOT NULL AUTO_INCREMENT,
  `start_time` datetime NOT NULL COMMENT '起始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户',
  `station_id` bigint DEFAULT NULL COMMENT '站',
  `is_use` tinyint DEFAULT '0' COMMENT '是否启用',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `t_meter_use_time_ibfk_4` (`station_id`),
  CONSTRAINT `t_meter_use_time_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `t_user` (`id`),
  CONSTRAINT `t_meter_use_time_ibfk_4` FOREIGN KEY (`station_id`) REFERENCES `t_stations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `t_meter_use_time_chk_5` CHECK ((`is_use` in (0,1)))
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='从站结算电表使用时间配置表'