class RET:
    OK                  = 200
    ERROR               = 400
    INVALIDTOKEN        = 401
    NOPERMISSION        = 403
    NODATA              = 404
    SESSIONERR          = 406
    PARAMERR            = 407
    TIMEOUT             = 408
    LOGINERR            = 409
    UNKOWNERR           = 410
    SERVERERR           = 411
    DATANOTEXIST        = 1002
    USERERR             = 1102


error_map = {
    RET.OK                    : u"成功",
    RET.ERROR                 : u"失败",
    RET.NODATA                : u"资源不存在",
    RET.SESSIONERR            : u"请求未授权/身份验证失败",
    RET.LOGINERR              : u"用户登录失败",
    RET.PARAMERR              : u"参数错误",
    RET.USERERR               : u"用户不存在",
    RET.SERVERERR             : u"请求异常",
    RET.UNKOWNERR             : u"请求错误，具体参考返回的msg",
    RET.INVALIDTOKEN          : u"用户未认证/用户未登录",
    RET.NOPERMISSION          : u"无权限",
    RET.TIMEOUT               : u"请求超时",
    RET.DATANOTEXIST          : u"数据不存在"
}