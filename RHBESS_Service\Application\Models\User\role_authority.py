#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\role_authority.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-27 13:45:21

from sqlalchemy import func
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, \
    VARCHAR,Text
from  Application.Models.User.role import Role
from Application.Models.User.authority import Authority
from Tools.Utils.time_utils import timeUtils


class RoleAuthority(user_Base):
    u'角色权限表'
    __tablename__ = "t_user_authority"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    role_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("c_user_role.id"),nullable=False, comment=u"用户id")
    authority_id = Column(Integer, ForeignKey("t_authority.id"),nullable=False, comment=u"权限id")
    op_ts = Column(DateTime, nullable=True, comment=u"时间")
   
    role_ = relationship("Role", backref="role_")
    authority_ = relationship("Authority", backref="authority_")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        i=1
        ids = user_session.query(Authority.id).all()
        for a in ids:
            user_session.merge(RoleAuthority(id=i,role_id=1,authority_id=a[0],op_ts=timeUtils.getNewTimeStr()))
            i=i+1
        user_session.commit()
        if user_session==None:
            user_session.close()

        
    def __repr__(self):

        return "{'id':%s,'role_id':'%s','role_descr':'%s','authority_id':'%s','authority_name':'%s','authority_path':'%s','authority_redirect':'%s',\
            'authority_component':'%s','op_ts':'%s','authority_hidden':'%s','authority_id':'%s','authority_title':'%s','station_id':'%s'}" % (
            self.id,self.role_id,self.role_.descr,self.authority_id,self.authority_.name,self.authority_.path,self.authority_.redirect,
            self.authority_.component,self.op_ts,self.authority_.hidden,self.authority_.id,self.authority_.title_,self.authority_.station_id)

  