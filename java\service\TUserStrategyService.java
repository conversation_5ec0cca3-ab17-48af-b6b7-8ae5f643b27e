package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyCreateDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyQueryDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyUpdateDTO;
import com.robestec.analysis.entity.TUserStrategy;
import com.robestec.analysis.vo.TUserStrategyVO;

import java.util.List;

/**
 * 用户策略服务接口
 */
public interface TUserStrategyService extends ISuperService<TUserStrategy> {

    /**
     * 分页查询用户策略
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TUserStrategyVO> queryTUserStrategy(TUserStrategyQueryDTO queryDTO);

    /**
     * 创建用户策略
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTUserStrategy(TUserStrategyCreateDTO createDTO);

    /**
     * 更新用户策略
     * @param updateDTO 更新参数
     */
    void updateTUserStrategy(TUserStrategyUpdateDTO updateDTO);

    /**
     * 删除用户策略
     * @param id 记录ID
     */
    void deleteTUserStrategy(Long id);

    /**
     * 获取用户策略详情
     * @param id 记录ID
     * @return 记录详情
     */
    TUserStrategyVO getTUserStrategy(Long id);

    /**
     * 批量创建用户策略
     * @param createDTOList 创建参数列表
     */
    void createTUserStrategyList(List<TUserStrategyCreateDTO> createDTOList);

    /**
     * 根据用户ID查询用户策略
     * @param userId 用户ID
     * @return 记录列表
     */
    List<TUserStrategyVO> getTUserStrategyByUserId(Long userId);

    /**
     * 根据策略名称查询用户策略
     * @param name 策略名称
     * @return 记录列表
     */
    List<TUserStrategyVO> getTUserStrategyByName(String name);

    /**
     * 统计用户的策略数量
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(Long userId);
}
