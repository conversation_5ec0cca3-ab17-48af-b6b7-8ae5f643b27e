# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/6/18 上午9:47
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : custom_price_views.py
# @Software : PyCharm
import datetime
import json
import uuid

import math
from django.db.models import F, Max, Q
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.user import models
from apis.web2 import error_log
from apis.web2.project_account.CustomPriceSerialzier import PeakValleySerializer, CustomizationAddSerializer, \
    CustomizationUpdateSerializer, CustomizationDetSerializer, EnPeakValleySerializer
from common import common_response_code
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from LocaleTool.common import redis_pool


class CustomizationMasterStationsView(APIView):
    """电站列表下拉选项"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)
        search_name = request.query_params.get("search_name", None)
        if search_name:
            master_stations = user_ins.master_stations.filter(Q(name__contains=search_name) | Q(english_name__contains=search_name), is_delete=0).all().order_by('id').values(
                "id", "name", "english_name", "en_name")
        else:
            master_stations = user_ins.master_stations.filter(is_delete=0).order_by('id').values("id", "name", "english_name", "en_name")
        # master_stations = user_ins.master_stations.all().order_by('id').values("id", "name", "english_name")
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "ok", "detail": master_stations}})


class CustomizationOptionsView(APIView):
    """代理购电价格查询下拉选项"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """代理购电价格查询下拉选项"""""
        lang = request.headers.get("lang", 'zh')
        provinces = models.ElectricityProvince.objects.all()
        result = {}
        for province in provinces:
            month = datetime.datetime.now().strftime("%Y-%m")
            provinces_ins = province.peakvalleynew_set.filter(year_month=month, day=1, moment='00:00').values("type", "level").annotate(
                province=F("province"))
            if lang == 'zh':
                ser = PeakValleySerializer(instance=provinces_ins, many=True, context={'lang': lang})
                result[province.name] = ser.data
            else:
                ser = EnPeakValleySerializer(instance=provinces_ins, many=True, context={'lang': lang})
                result[province.en_name] = ser.data
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "ok", "detail": result}})


class CustomizationDetailView(APIView):
    """代理购电电价查询"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        # ser = CustomizationDetailSerializer(data=request.data, context=self.serializer_context)
        # try:
        #     if not ser.is_valid():
        #         return Response(
        #             {
        #                 "code": common_response_code.FIELD_ERROR,
        #                 "data": {"message": "error", "detail": ser.errors},
        #             }
        #         )
        # except Exception as e:
        #     error_log.error(f"代理购电价查询:字段校验不通过 =>{e}")
        #     return Response(
        #         {
        #             "code": common_response_code.FIELD_ERROR,
        #             "data": {"message": "error", "detail": e.args[0]},
        #         }
        #     )
        query_dic = request.data
        now = datetime.datetime.now()
        id = query_dic.get('id')
        type_ = query_dic.get('type')
        level = query_dic.get('level')
        month = query_dic.get('year_month', now.month)

        try:
            month_str = now.replace(month=month).strftime('%Y-%m')
        except Exception as e:
            return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": 'error', "detail": "参数校验不通过，请检查月份参数" if lang == 'zh' else
                                 "Parameter validation failed, please check."},
                    }
                )

        if not all([id, type_, level, month]):
            return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": 'error', "detail": "参数校验不通过，请检查" if lang == 'zh' else
                                 "Parameter validation failed, please check."},
                    }
                )

        province_ins = models.ElectricityProvince.objects.filter(id=id).first()
        if not province_ins:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": 'error', "detail": "省份不存在" if lang == 'zh' else
                    "Province does not exist."},
                }
            )

        price_instances = models.PeakValleyNew.objects.filter(province=province_ins, type=type_, level=level,
                                                              year_month=month_str, day=1, moment__contains=':00').all()

        detail = {"price_spike": '--',  # 尖峰
                  "price_peak": '--',  # 峰
                  "price_flat": '--',  # 平
                  "price_valley": '--',  # 谷
                  "price_dvalley": '--'}

        if price_instances.exists():
            for i in price_instances:
                key = f'h{int(i.moment.split(":")[0])}'
                value = i.price
                detail[key] = value

                if i.pv == 2:
                    detail['price_spike'] = value
                elif i.pv == 1:
                    detail['price_peak'] = value
                elif i.pv == 0:
                    detail['price_flat'] = value
                elif i.pv == -1:
                    detail['price_valley'] = value
                else:
                    detail['price_dvalley'] = value

        return Response({"code": common_response_code.SUCCESS, "data": {"message": "ok", "detail": detail}})


class CustomizationsView(APIView):
    """单位电价: 添加、列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["lang"] = request.headers.get("lang", 'zh')
        # self.serializer_context["station"] = json.loads(request.body.decode()).get("station", None)

    def post(self, request):
        """添加"""""
        lang = request.headers.get("lang", 'zh')
        # uid = request.data['uid']
        start = request.data["start"]
        end = request.data["end"]
        name = request.data["name"]

        try:
            # if not uid:
            #     raise Exception("参数Uid缺失")
            #
            # if not models.UnitPrice.objects.filter(uid=uid, delete=0).exists():
            #     raise Exception("单位电价不存在")

            if not name:
                raise Exception("单位电价名称不能为空" if lang == 'zh' else "Unit Price Name can not be empty.")

            if not all([start, end]):
                raise Exception("开始时间和结束时间不能为空" if lang == 'zh' else "Start and end time can not be empty.")
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )

        ser = CustomizationAddSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "参数校验不通过，请检查" if lang == 'zh' else
                        "Parameter validation failed, please check.", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )
        stations_list = ser.validated_data.pop("stations_list")
        stations_names = [models.MaterStation.objects.get(id=station_id, is_delete=0).name for station_id in stations_list]
        stations_names_str = ','.join(stations_names)

        uu = str(uuid.uuid4())
        r = ''
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)

        # 关联并网点不为空时
        if stations_list:
            for station_id in stations_list:
                m_station = models.MaterStation.objects.get(id=station_id, is_delete=0)

                ins = models.UnitPrice.objects.create(station=m_station, user=user_ins, uid=uu,
                                                      stations_name=stations_names_str,
                                                      project=m_station.project, en_name=ser.validated_data['name'],
                                                      en_note=ser.validated_data['note'],
                                                      **ser.validated_data)
                r = ins.uid

                # 异步翻译
                pdr_data = {'id': ins.id,
                            'table': 't_unit_price',
                            'update_data': {'name': ser.validated_data['name']}}
                if ser.validated_data['note']:
                    pdr_data['update_data']['note'] = ser.validated_data['note']

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

        # 未选择关联的并网点则新建一条不关联的单位电价
        else:
            ins = models.UnitPrice.objects.create(user_id=user_id, uid=uu, **ser.validated_data)
            r = ins.uid
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "单位电价添加成功" if lang == 'zh' else "Custom Price add successfully.", "detail": r},
            }
        )

    def get(self, request):
        """列表"""""
        lang = request.headers.get("lang", 'zh')
        search_name = request.query_params.get("search_name", None)
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size")) if request.query_params.get("page_size") else None

        # user_id = request.user["user_id"]
        # user_ins = models.UserDetails.objects.get(id=user_id)

        # 第一步：获取每个 uid 对应的最新 create_time
        latest_time_subquery = models.UnitPrice.objects.filter(
            delete=0  # 确保只查找未删除的记录
        ).values('uid').annotate(latest_create_time=Max('create_time'))

        # 第二步：将最新的 create_time 与原表进行关联，获取对应的完整记录
        all_queryset = models.UnitPrice.objects.filter(
            uid__in=[item['uid'] for item in latest_time_subquery],
            create_time__in=[item['latest_create_time'] for item in latest_time_subquery]
        ).order_by('-create_time')

        # 如果需要进一步过滤（如search_name），可以在主查询中添加过滤条件
        if search_name:
            all_queryset = all_queryset.filter(Q(name__contains=search_name) | Q(en_name__contains=search_name))

        # if search_name:
        #     all_queryset = models.UnitPrice.objects.filter(name__contains=search_name, delete=0).all()
        # else:
        #     all_queryset = models.UnitPrice.objects.filter(delete=0).all()

        detail_dic = (
            all_queryset.values(
                "name",
                "en_name",
                "start",
                "end",
                "spike_chag_price",
                "peak_chag_price",
                "flat_chag_price",
                "valley_chag_price",
                "dvalley_chag_price",
                "spike_disg_price",
                "peak_disg_price",
                "flat_disg_price",
                "valley_disg_price",
                "dvalley_disg_price",
                "uid",
                "stations_name",
                "note",
                "en_note"
            )
            .distinct()
        )

        for item in detail_dic:
            unit_prices = models.UnitPrice.objects.filter(delete=0, uid=item["uid"]).all()
            if unit_prices.exists():
                stations_list = [i.station_id for i in unit_prices]
                item["stations_list"] = stations_list

            if lang == 'en':
                item['name'] = item['en_name']
                item['note'] = item['en_note']

        if page_size:
            # 手动分页
            total_pages = math.ceil(len(detail_dic) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(detail_dic) + 1
            detail_dic_ = detail_dic[start_index:end_index]

            return Response({"code": common_response_code.SUCCESS, "data": {"detail": detail_dic_, "message": "ok"},
                             "paginator_info": {
                            "page": page,
                            "page_size": page_size,
                            "pages": total_pages,
                            "total_count": len(detail_dic)}})
        else:
            return Response({"code": common_response_code.SUCCESS, "data": {"detail": detail_dic, "message": "ok"}})


class CustomizationView(APIView):
    """单位电价电价：详情、修改、删除"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["lang"] = request.headers.get("lang", 'zh')

    def get(self, request):
        """详情"""""
        lang = request.headers.get("lang", 'zh')
        uid = request.query_params.get('uid')
        try:
            unit_prices = models.UnitPrice.objects.filter(delete=0, uid=uid).all()
            if unit_prices.exists():
                stations_list = [i.station_id for i in unit_prices]

                unit_price = unit_prices.first()
                ser = CustomizationDetSerializer(instance=unit_price)
                return_dict = ser.data
                return_dict.update(stations_list=stations_list)

                if lang == 'en':
                    return_dict['name'] = return_dict['en_name']
                    return_dict['note'] = return_dict['en_note']

                return Response({"code": common_response_code.SUCCESS, "data": {"detail": return_dict, "message": "ok"}})

            else:
                return Response({"code": common_response_code.NO_DATA, "data": {"message": "uid查询不到数据" if lang == 'zh' else 'no data.'}})
        except Exception as e:
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "查询失败" if lang == 'zh'
                             else 'Query failed.', "detail": e.args[0]}})

    def put(self, request):
        """修改"""""
        lang = request.headers.get("lang", 'zh')
        user = request.user.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)

        uid = request.data['uid']
        start = request.data["start"]
        end = request.data["end"]
        name = request.data["name"]

        try:
            if not uid:
                raise Exception("参数Uid缺失" if lang == 'zh' else "Uid is missing.")

            if not models.UnitPrice.objects.filter(uid=uid, delete=0).exists():
                raise Exception("单位电价不存在" if lang == 'zh' else "Unit price does not exist.")

            if not name:
                raise Exception("单位电价名称不能为空" if lang == 'zh' else "Unit price name cannot be empty.")

            if not all([start, end]):
                raise Exception("开始时间和结束时间不能为空" if lang == 'zh' else "Start time and end time cannot be empty.")
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )

        ser = CustomizationUpdateSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "参数校验不通过，请检查" if lang == 'zh' else
                        "Parameter verification does not pass, please check.", "detail": ser.errors},
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )
        stations_list = ser.validated_data.pop("stations_list")
        stations_names = [models.MaterStation.objects.get(id=station_id, is_delete=0).name for station_id in stations_list]
        stations_names_str = ','.join(stations_names)

        uid = ser.validated_data.get("uid")
        has_unit_prices = models.UnitPrice.objects.filter(uid=uid, delete=0).all()

        # 针对已有的uid关联的配置处理：删除不在列表中的配置，更新在列表中的配置
        for has_unit_price in has_unit_prices:
            if has_unit_price.station_id not in stations_list:
                has_unit_price.delete = 1
                has_unit_price.save()
            else:
                has_unit_price.user = user_ins
                has_unit_price.name = ser.validated_data.get("name")
                has_unit_price.en_name = ser.validated_data.get("name")
                has_unit_price.start = ser.validated_data.get("start")
                has_unit_price.end = ser.validated_data.get("end")
                has_unit_price.spike_chag_price = ser.validated_data.get("spike_chag_price")
                has_unit_price.peak_chag_price = ser.validated_data.get("peak_chag_price")
                has_unit_price.flat_chag_price = ser.validated_data.get("flat_chag_price")
                has_unit_price.valley_chag_price = ser.validated_data.get("valley_chag_price")
                has_unit_price.dvalley_chag_price = ser.validated_data.get("dvalley_chag_price")
                has_unit_price.spike_disg_price = ser.validated_data.get("spike_disg_price")
                has_unit_price.peak_disg_price = ser.validated_data.get("peak_disg_price")
                has_unit_price.flat_disg_price = ser.validated_data.get("flat_disg_price")
                has_unit_price.valley_disg_price = ser.validated_data.get("valley_disg_price")
                has_unit_price.dvalley_disg_price = ser.validated_data.get("dvalley_disg_price")
                has_unit_price.note = ser.validated_data.get("note")
                has_unit_price.en_note = ser.validated_data.get("note")
                has_unit_price.stations_name = stations_names_str

                has_unit_price.save()

                # 异步翻译
                pdr_data = {'id': has_unit_price.id,
                            'table': 't_unit_price',
                            'update_data': {'name': ser.validated_data.get("name")}}
                if ser.validated_data.get("note"):
                    pdr_data['update_data']['note'] = ser.validated_data.get("note")

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

        # ser.validated_data.pop('stations_list')
        # 针对新增的uid关联的并网点处理：新增配置关联到uid
        if stations_list:
            for station_id in stations_list:
                station_unit_price = models.UnitPrice.objects.filter(uid=uid, station=station_id, delete=0).all()
                if station_unit_price:
                    continue
                m_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
                ins = models.UnitPrice.objects.create(station=m_station, user=user_ins,
                                                      stations_name=stations_names_str,
                                                      project=m_station.project,
                                                      en_name=ser.validated_data.get("name"),
                                                      en_note=ser.validated_data.get("note"),
                                                      **ser.validated_data)

                # 异步翻译
                pdr_data = {'id': ins.id,
                            'table': 't_unit_price',
                            'update_data': {'name': ser.validated_data.get("name")}}
                if ser.validated_data.get("note"):
                    pdr_data['update_data']['note'] = ser.validated_data.get("note")

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

        # 未选择关联的并网点则新建一条不关联的单位电价
        else:
            ins = models.UnitPrice.objects.create(user=user_ins,
                                                  stations_name=stations_names_str,
                                                  en_name=ser.validated_data.get("name"),
                                                  en_note=ser.validated_data.get("note"),
                                                  **ser.validated_data)

            # 异步翻译
            pdr_data = {'id': ins.id,
                        'table': 't_unit_price',
                        'update_data': {'name': ser.validated_data.get("name")}}
            if ser.validated_data.get("note"):
                pdr_data['update_data']['note'] = ser.validated_data.get("note")

            pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "单位电价修改成功" if lang == 'zh' else "Unit price modified successfully.", "detail": uid},
            }
        )

    def post(self, request):
        """删除"""""
        lang = request.headers.get("lang", 'zh')
        user_id = request.user["user_id"]
        uid = request.data.get("uid")
        if not uid:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": {"message": "uid为必传参数" if lang == 'zh' else "uid is required."}},
                }
            )

        price_ins = models.UnitPrice.objects.filter(uid=uid, delete=0).all()
        if not price_ins.exists():
            error_log.error("单位电价删除:uid 不存在或已删除")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": {"message": "uid 不存在或已删除" if lang == 'zh' else "uid does not exist or has been deleted."}},
                }
            )
        for p in price_ins.all():
            p.delete = 1
            p.delete_user_id = user_id
            p.save()

        return Response({"code": common_response_code.SUCCESS, "data": {"message": "单位电价删除成功" if lang == 'zh' else "Unit price deleted successfully.", "detail": uid}})
