package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 计划历史查询DTO
 */
@Data
@ApiModel("计划历史查询DTO")
public class PlanHistoryQueryDTO {

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("状态: 1成功；2失败")
    private Integer status;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
