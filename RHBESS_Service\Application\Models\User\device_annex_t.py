#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40

from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class DeviceAnnex(user_Base):
    u'备件库附件表'
    __tablename__ = "t_device_annex"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    device_id = Column(Integer, nullable=False, comment=u"设备库id")
    annex_url = Column(VARCHAR(255), nullable=True, comment=u"附件存放的绝对地址")
    file_name = Column(VARCHAR(255), nullable=True, comment=u"上传文件名")
    en_file_name = Column(VARCHAR(255), nullable=True, comment=u"上传文件名")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        return "{'id':%s,'device_id':'%s','annex_url':'%s','file_name':'%s','en_file_name':'%s'}" % (self.id,self.device_id,self.annex_url,self.file_name,self.en_file_name)
        
