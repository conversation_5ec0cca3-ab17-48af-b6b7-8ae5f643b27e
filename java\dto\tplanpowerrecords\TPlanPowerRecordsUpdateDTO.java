package com.robestec.analysis.dto.tplanpowerrecords;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 功率计划关联记录更新DTO
 */
@Data
@ApiModel("功率计划关联记录更新DTO")
public class TPlanPowerRecordsUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "计划历史记录ID", required = true)
    @NotNull(message = "计划历史记录ID不能为空")
    private Long planId;

    @ApiModelProperty(value = "功率下发记录ID", required = true)
    @NotNull(message = "功率下发记录ID不能为空")
    private Long powerId;

    @ApiModelProperty(value = "功率计划序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
