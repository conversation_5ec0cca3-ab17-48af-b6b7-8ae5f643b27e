# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/6/7 上午9:36
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : insert_former_base_data.py
# @Software : PyCharm


# 连接数据库
import datetime

import pymysql
from dbutils.persistent_db import PersistentDB

time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
pool = PersistentDB(pymysql, 10, **{
    "host": 'rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com',  # 数据库主机地址
    "user": 'tianlu_app',  # 数据库用户名
    "password": 'P@ssw0rd!',  # 数据库密码
    "database": 'db_tianluapp_fz',  # 数据库名称
    "port": 3306,
    "cursorclass": pymysql.cursors.DictCursor
})
conn = pool.connection()
cursor = conn.cursor()
index = 0
try:
# # 基准功率
    base_sql = """SELECT * FROM `t_former_base` ORDER BY id"""
    cursor.execute(base_sql)
    base_result = cursor.fetchall()
    for i, base in enumerate(base_result):
        for _i in range(24):
            for y in ['00', '15', '30', '45']:
                moment = str(_i) + ':' + y if _i >= 10 else '0' + str(_i) + ':' + y
                year_month = f"2024-0{base.get('year_month')}" if int(
                    base.get('year_month')) < 10 else f"2024-{base.get('year_month')}"
                mark = base.get(f'h{_i + 1}f')
                power_value = base.get(f'h{_i + 1}p')
                base_inser_sql = f"""INSERT INTO `t_former_base_new`(`create_time`, `year_month`, `day`, `moment`, `mark`, `power_value`, `power`) VALUES ('{time_now}', '{year_month}', 1, '{moment}', {mark}, {power_value}, {base.get('power')});"""
                cursor.execute(base_inser_sql)
                inster_id = cursor.lastrowid
                conn.commit()
                # if i % 12 == 0:

                station_base = f"""SELECT station_id FROM `t_station_base` WHERE former_base_id = {base.get('id')}"""
                cursor.execute(station_base)
                station_base_result = cursor.fetchall()
                if station_base_result:
                    for s in station_base_result:
                        s_base_inster_sql = f"""INSERT INTO `t_station_base_new`(`create_time`, `station_id`, `former_base_id`) VALUES ('{time_now}', {s.get('station_id')}, {inster_id});"""
                        cursor.execute(s_base_inster_sql)
                        conn.commit()
                        index += 1
                        if index == 1000:
                            print(
                                '-------------------------------------执行1000条-------------------------------------')
                            index = 0


except pymysql.MySQLError as e:
    print(f"Error: {e}")
    conn.rollback()
finally:
    # 关闭连接
    conn.close()
print('-----------完成-----------')




