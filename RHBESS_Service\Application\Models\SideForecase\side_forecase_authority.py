#!/usr/bin/env python
# coding=utf-8
#@Information:静态路由表
#<AUTHOR> WYJ
#@Date         : 2023-06-01 10:03:55
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_authority.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-01 10:10:02


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseAuthority(user_Base):
    u'静态路由表'
    __tablename__ = "t_side_forecase_authority"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    label = Column(String(256), nullable=False, comment=u"名称")
    index = Column(String(256), nullable=False,comment=u"连接")
    authority = Column(Boolean, nullable=False,comment=u"是否有权限")
    tags = Column(Text, nullable=True,comment=u"一个集合")
    icon = Column(String(256), nullable=True,comment=u"图标")
    parent_id = Column(String(256), nullable=True,comment=u"父节点id,用index")
    disabled = Column(Boolean, nullable=True, comment=u"是否禁用")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'label':'%s','index':'%s','authority':%s,'icon':'%s','parent_id':'%s','disabled':%s}" % (
            self.label,self.index,self.authority,self.icon,self.parent_id,self.disabled)

   