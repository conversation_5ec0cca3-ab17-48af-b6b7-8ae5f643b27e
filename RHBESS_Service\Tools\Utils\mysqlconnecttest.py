#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-20 15:51:49
#@FilePath     : \RHBESS_Service\Tools\Utils\mysqlconnecttest.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:36:54

#  连接mysql测试
# import pymysql
# conn = pymysql.connect(host='rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com',port=3306,user='rhbesssv1',passwd='Rhbesssv123',charset='utf8')

# cursor = conn.cursor()
# cursor.execute("show databases;")
# data = cursor.fetchall()
# for d in data:
#     print 'data:',d

# 连接mongodb测试
import pymongo
client = pymongo.MongoClient(host='dds-8vbe82888bceeda41.mongodb.zhangbei.rds.aliyuncs.com',port=3717,username='root',password='ROOT123#')
#client = pymongo.MongoClient(host='*********',port=27017)
mongo_db = client['test002']
dbs =  mongo_db.collection_names()
print (dbs)