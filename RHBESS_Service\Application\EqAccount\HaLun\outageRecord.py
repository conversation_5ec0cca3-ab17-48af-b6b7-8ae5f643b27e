#!/usr/bin/env python3
# encoding=utf-8
# Information: 停运记录
# @Time    : 2024/7/24 上午11:33
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import os
import json
import re
import logging
import tornado.web
from datetime import datetime, timedelta
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import user_session
from Tools.DB.mysql_scada import DEBUG
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.outage_record import (OutageRecord, DeviceChildUnit, OutageRecordDictionary,
                                                   OutageEffectPeriodRecord, OutageSpareUseRecord, OutageEffectRange)
from Application.Models.User.device_type_t import DeviceType
from Application.Models.User.device_unit_t import DeviceUnit
from Application.Models.User.project_info_t import ProjectInfo
from Application.Models.User.project_device_t import ProjectDevice
from Application.Models.User.device_library_t import DeviceLibrary
from Application.Models.WorkOrder.spare_info import SpareInfo
from Application.Models.User.device_no_t import DeviceNo
from Application.Models.User.manufacturer import Manufacturer
from itertools import product
import pymysql
from Application.Cfg.dir_cfg import model_config
from dbutils.persistent_db import PersistentDB
from Application.Models.User.station import Station
from Tools.Utils.mimio_tool import upload_file, MinioTool
from sqlalchemy import func, distinct, or_
import pandas as pd
import io
import uuid
import numpy as np
from decimal import Decimal
import string
import random

pools = PersistentDB(pymysql, 10, **{
    "host": model_config.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
    "user": model_config.get('mysql', "IDC_USERNAME"),  # 数据库用户名
    "password": model_config.get('mysql', "IDC_PASSWORD"),  # 数据库密码
    "database": model_config.get('mysql', "IDCS_DATABASE"),  # 数据库名称
    "port": int(model_config.get('mysql', "IDC_PORT")),
    "cursorclass": pymysql.cursors.DictCursor
})


class OutageRecordIntetface(BaseHandler):
    @tornado.web.authenticated
    def get(self, action):
        self.refreshSession()
        try:
            data = []
            if action == 'GetOutageRecord':
                """
                获取停运记录清单
                """
                project_id = self.get_argument('project_id', None)  # 项目/电站id
                device_type_id = self.get_argument('device_type_id', None)  # 设备类型id
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                occur_begin_time = self.get_argument('occur_begin_time', None)  # 发生开始时间
                occur_end_time = self.get_argument('occur_end_time', None)  # 发生结束时间
                outage_grade_id = self.get_argument('outage_grade_id', None)  # 停运等级id
                repair_schedule_id = self.get_argument('repair_schedule_id', None)  # 修复进度id
                page_num = int(self.get_argument('pageNum', '1'))
                page_size = int(self.get_argument('pageSize', '20'))

                if DEBUG:
                    logging.info(
                        'project_id:%s, device_type_id:%s, device_sn:%s, occur_begin_time:%s, occur_end_time:%s, '
                        'outage_grade_id:%s, repair_schedule_id:%s, page_num:%s, page_size:%s',
                        project_id, device_type_id, device_sn, occur_begin_time, occur_end_time,
                        outage_grade_id, repair_schedule_id, page_num, page_size
                    )
                if not project_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(project_id):
                    return self.customError("参数类型错误")
                filters = [OutageRecord.is_use == 1]
                if device_type_id:
                    if not re.compile(r'^-?\d+$').match(device_type_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.device_type_id == int(device_type_id))
                if project_id:
                    filters.append(OutageRecord.project_id == int(project_id))
                if device_sn:
                    filters.append(OutageRecord.device_sn.like('%' + device_sn + '%'))
                if outage_grade_id:
                    if not re.compile(r'^-?\d+$').match(outage_grade_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.outage_grade_id == int(outage_grade_id))
                if repair_schedule_id:
                    if not re.compile(r'^-?\d+$').match(repair_schedule_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.repair_schedule_id == int(repair_schedule_id))
                if occur_begin_time and occur_end_time:
                    occur_begin_time = occur_begin_time + ' 00:00:00'
                    occur_end_time = occur_end_time + ' 23:59:59'
                    filters.append(OutageRecord.occur_time.between(self.translate_date(occur_begin_time),
                                                                   self.translate_date(occur_end_time)))

                total = user_session.query(func.count(OutageRecord.id)).filter(*filters).scalar()
                out_record = user_session.query(OutageRecord).filter(*filters).order_by(OutageRecord.id.desc()).limit(
                    page_size).offset((page_num - 1) * page_size).all()
                if out_record:
                    # 按需加载 DeviceType, DeviceUnit, DeviceChildUnit 数据
                    device_type_ids = {item.device_type_id for item in out_record if item.device_type_id}
                    device_unit_ids = {item.device_unit_id for item in out_record if item.device_unit_id}
                    device_child_unit_ids = {item.device_child_unit_id for item in out_record if
                                             item.device_child_unit_id}

                    device_types = {dt.id: dt.name for dt in
                                    user_session.query(DeviceType).filter(DeviceType.id.in_(device_type_ids)).all()}
                    device_units = {du.id: du.name for du in
                                    user_session.query(DeviceUnit).filter(DeviceUnit.id.in_(device_unit_ids)).all()}
                    device_child_units = {dcu.id: dcu.name for dcu in user_session.query(DeviceChildUnit).filter(
                        DeviceChildUnit.id.in_(device_child_unit_ids)).all()}

                    for item in out_record:
                        effect_range_names = [er.effect_range for er in item.effect_ranges if er.is_use]
                        data.append({
                            "id": item.id,
                            "project_id": item.project_id,
                            "device_sn": item.device_sn,
                            "area": item.area,
                            "device_type_id": item.device_type_id,
                            "device_type_name": device_types.get(item.device_type_id, ""),
                            "device_unit_id": item.device_unit_id,
                            "device_unit_name": device_units.get(item.device_unit_id, ""),
                            "device_child_unit_id": item.device_child_unit_id,
                            "device_child_unit": device_child_units.get(item.device_child_unit_id, ""),
                            "effect_range": effect_range_names,
                            "effect_capacity": item.effect_capacity,
                            "outage_grade_id": item.outage_grade_id,
                            "outage_grade_name": item.outage_grade.name if item.outage_grade_id else "",
                            "repair_schedule_id": item.repair_schedule_id,
                            "repair_schedule_name": item.repair_schedule.name if item.repair_schedule_id else "",
                            "discover_time": item.discover_time.strftime(
                                '%Y-%m-%d %H:%M') if item.discover_time else "",
                            "occur_time": item.occur_time.strftime('%Y-%m-%d %H:%M') if item.occur_time else "",
                            "expect_repair_time": item.expect_repair_time.strftime(
                                '%Y-%m-%d %H:%M') if item.expect_repair_time else "",
                            "reality_repair_time": item.reality_repair_time.strftime(
                                '%Y-%m-%d %H:%M') if item.reality_repair_time else "",
                            "fault_phenomenon": item.fault_phenomenon,
                            "fault_reason": item.fault_reason,
                            "scene_solution": item.scene_solution,
                            "preventive_measure": item.preventive_measure,
                            "record_name": item.record_name,
                            "record_no": item.record_no,
                            "factory": item.factory,
                            "affect_time": item.affect_time,
                            "device_no": item.device_no
                        })
                return self.returnTotalSuc(data, total)
            elif action == 'GetDeviceType':
                """
                获取设备类型列表
                """
                type_name = self.get_argument('type_name', None)  # 根据类型名称筛选
                project_id = self.get_argument('project_id', None)
                area = self.get_argument('area', None)  # 根据分区名称筛选
                if not project_id:
                    return self.customError("参数错误")
                # 先获取当前项目电站对应的project_info的信息
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取project_info下对应的设备清单中的类型名称列表
                filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1]
                if area:
                    filters.append(ProjectDevice.area == area)
                if type_name:
                    model_device_type = user_session.query(DeviceType).filter(
                        DeviceType.name.like('%' + type_name + '%')).all()
                    if model_device_type:
                        device_type_ids = [i.id for i in model_device_type]
                        filters.append(ProjectDevice.type_id.in_(device_type_ids))
                    else:
                        return self.returnTypeSuc(data)
                device_type_list = user_session.query(ProjectDevice.type_id).filter(*filters).distinct().all()

                if device_type_list:
                    data = [
                        {
                            "id": i[0],
                            "name": user_session.query(DeviceType.name).filter(DeviceType.id == i[0]).scalar()
                        } for i in device_type_list
                    ]
                return self.returnTypeSuc(data)
            elif action == 'GetAreaList':
                """
                获取分区列表
                """
                area = self.get_argument('area', None)  # 根据分区名称筛选
                project_id = self.get_argument('project_id', None)
                if not project_id:
                    return self.customError("参数错误")
                # 先获取当前项目电站对应的project_info的信息
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取project_info下对应的设备清单中的类型名称列表
                filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1]
                if area:
                    filters.append(ProjectDevice.area.like('%' + area + '%'))
                device_area_list = user_session.query(ProjectDevice.area).filter(*filters).distinct().all()

                if device_area_list:
                    data = [i[0] for i in device_area_list]
                return self.returnTypeSuc(data)
            elif action == 'GetDeviceSnList':
                """
                根据已选分区和设备类型获取设备编号列表
                """
                device_sn = self.get_argument('device_sn', None)  # 根据设备编号筛选
                area = self.get_argument('area', None)  # 分区
                device_type_id = self.get_argument('device_type_id', None)  # 设备类型id

                project_id = self.get_argument('project_id', None)
                if not project_id or not area or not device_type_id:
                    return self.customError("参数错误")
                # 先获取当前项目电站对应的project_info的信息
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取project_info下对应的设备清单中的类型名称列表
                filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1, ProjectDevice.area == area,
                           ProjectDevice.type_id == device_type_id, ProjectDevice.sn.isnot(None)]
                if device_sn:
                    filters.append(ProjectDevice.sn.like('%' + device_sn + '%'))
                device_sn_list = user_session.query(ProjectDevice.sn).filter(*filters).distinct().all()

                if device_sn_list:
                    data = [i[0] for i in device_sn_list]
                return self.returnTypeSuc(data)
            elif action == 'GetEffectRangeList':
                """
                获取影响范围列表
                """
                effect_range = self.get_argument('effect_range', None)  # 根据名称筛选
                project_id = self.get_argument('project_id', None)
                if not project_id:
                    return self.customError("参数错误")
                # 先获取当前项目电站对应的project_info的信息
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取project_info下对应的设备清单中的类型名称列表
                filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.area.like('%电池堆%'),
                           ProjectDevice.is_use == 1]
                if effect_range:
                    filters.append(ProjectDevice.area.like('%' + effect_range + '%'))
                device_area_list = user_session.query(ProjectDevice.area).filter(*filters).distinct().all()

                if device_area_list:
                    data = [i[0] for i in device_area_list]
                return self.returnTypeSuc(data)
            elif action == 'GetDeviceUnitList':
                """
                获取故障设备部件列表
                """
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                project_id = self.get_argument('project_id', None)
                device_unit_name = self.get_argument('device_unit_name', None)  # 根据名称筛选
                if not project_id or not device_sn:
                    return self.customError("参数错误")
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取所选设备编号对应的设备型号id
                device_no_data = user_session.query(ProjectDevice.device_no_id).filter(
                    ProjectDevice.project_id == project_info.id, ProjectDevice.sn == device_sn,
                    ProjectDevice.is_use == 1).first()
                if not device_no_data:
                    return self.returnTypeSuc(data)
                device_no_id = device_no_data[0]
                # 获取设备型号对应的t_device_library中的id
                device_library_data = user_session.query(DeviceLibrary.id).filter(
                    DeviceLibrary.device_no_id == device_no_id).first()
                if not device_library_data:
                    return self.returnTypeSuc(data)
                device_library_id = device_library_data[0]
                filters = [DeviceUnit.is_use == 1, DeviceUnit.device_id == device_library_id]
                if device_unit_name:
                    filters.append(DeviceUnit.name.like('%' + device_unit_name + '%'))
                device_unit_list = user_session.query(DeviceUnit).filter(*filters).all()

                if device_unit_list:
                    data = [
                        {
                            "id": i.id,
                            "name": i.name
                        } for i in device_unit_list
                    ]
                return self.returnTypeSuc(data)
            elif action == 'GetGradeAndScheduleList':
                """
                获取停运等级和修复进度列表 type 1 停运等级 2 修复进度
                """
                category = self.get_argument('type', '1')
                if not re.compile(r'^-?\d+$').match(category):
                    return self.customError('参数类型传参错误')
                category = int(category)
                nodes = user_session.query(OutageRecordDictionary).filter(OutageRecordDictionary.category == category,
                                                                          OutageRecordDictionary.is_use == 1).all()
                type_list = []
                if nodes:
                    type_list = [{'id': item.id, 'name': item.name} for item in nodes]
                return self.returnTypeSuc(type_list)
            elif action == 'GetEffectPeriodList':
                """
                获取影响时间段列表
                """
                outage_record_id = self.get_argument('outage_record_id', None)  # 停运记录id
                page_num = int(self.get_argument('pageNum', '1'))
                page_size = int(self.get_argument('pageSize', '20'))
                if not outage_record_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(outage_record_id):
                    return self.customError('参数类型错误')

                total = user_session.query(func.count(OutageEffectPeriodRecord.id)).filter(
                    OutageEffectPeriodRecord.is_use == 1,
                    OutageEffectPeriodRecord.outage_id == int(outage_record_id)).scalar()
                run_type_data = user_session.query(OutageEffectPeriodRecord).filter(
                    OutageEffectPeriodRecord.is_use == 1,
                    OutageEffectPeriodRecord.outage_id == int(outage_record_id)).order_by(OutageEffectPeriodRecord.id.desc()).limit(
                    page_size).offset((page_num - 1) * page_size).all()
                if run_type_data:
                    data = [{'id': item.id, 'begin_time': item.begin_time.strftime('%Y-%m-%d %H:%M:%S'),
                             'end_time': item.end_time.strftime('%Y-%m-%d %H:%M:%S'), 'remark': item.remark}
                            for item in run_type_data]
                return self.returnTotalSuc(data, total)
            elif action == 'GetSpareUseList':
                """
                获取备件信息列表
                """
                outage_record_id = self.get_argument('outage_record_id', None)  # 停运记录id
                if not outage_record_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(outage_record_id):
                    return self.customError('参数类型错误')
                spare_list = user_session.query(OutageSpareUseRecord).filter(
                    OutageSpareUseRecord.outage_id == outage_record_id, OutageSpareUseRecord.is_use == 1).all()
                if spare_list:
                    data = [
                        {
                            "id": item.id,
                            "spare_name": item.spare_info.name,
                            "use_num": item.use_num,
                            "unit": item.unit,
                            "remark": item.remark
                        } for item in spare_list
                    ]
                return self.returnTypeSuc(data)
            elif action == 'GetTemplateDownloadUrl':
                """
                获取模版下载地址
                """
                nodes = user_session.query(OutageRecordDictionary).filter(OutageRecordDictionary.category == 3,
                                                                          OutageRecordDictionary.is_use == 1).first()
                return self.returnTypeSuc({'down_url': nodes.name})
            elif action == 'GetDownloadOutageUrl':
                """
                获取下载停运记录地址
                """
                project_id = self.get_argument('project_id', None)  # 项目/电站id
                device_type_id = self.get_argument('device_type_id', None)  # 设备类型id
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                occur_begin_time = self.get_argument('occur_begin_time', None)  # 发生开始时间
                occur_end_time = self.get_argument('occur_end_time', None)  # 发生结束时间
                outage_grade_id = self.get_argument('outage_grade_id', None)  # 停运等级id
                repair_schedule_id = self.get_argument('repair_schedule_id', None)  # 修复进度id

                if DEBUG:
                    logging.info(
                        'project_id:%s, device_type_id:%s, device_sn:%s, occur_begin_time:%s, occur_end_time:%s, '
                        'outage_grade_id:%s, repair_schedule_id:%s',
                        project_id, device_type_id, device_sn, occur_begin_time, occur_end_time,
                        outage_grade_id, repair_schedule_id
                    )
                if not occur_begin_time or not occur_end_time:
                    return self.customError("请选择日期范围，不超过90天")
                if self.is_over_by_days(occur_begin_time, occur_end_time, 90):
                    return self.customError("选择的日期范围不能超过90天")
                if not project_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(project_id):
                    return self.customError("参数类型错误")
                filters = []
                if device_type_id:
                    if not re.compile(r'^-?\d+$').match(device_type_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.device_type_id == int(device_type_id))
                if project_id:
                    filters.append(OutageRecord.project_id == int(project_id))
                if device_sn:
                    filters.append(OutageRecord.device_sn.like('%' + device_sn + '%'))
                if outage_grade_id:
                    if not re.compile(r'^-?\d+$').match(outage_grade_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.outage_grade_id == int(outage_grade_id))
                if repair_schedule_id:
                    if not re.compile(r'^-?\d+$').match(repair_schedule_id):
                        return self.customError("参数类型错误")
                    filters.append(OutageRecord.repair_schedule_id == int(repair_schedule_id))
                if occur_begin_time and occur_end_time:
                    occur_begin_time_new = occur_begin_time + ' 00:00:00'
                    occur_end_time_new = occur_end_time + ' 23:59:59'
                    filters.append(OutageRecord.occur_time.between(self.translate_date(occur_begin_time_new),
                                                                   self.translate_date(occur_end_time_new)))
                station_info = user_session.query(Station).filter(
                    Station.id == project_id).first()
                if not station_info:
                    return self.customError("项目不存在")
                station_descr = station_info.descr if station_info else ''
                data_list = [['项目名称', '项目类型', '故障编号', '故障所在分区', '故障影响范围', '停运容量（kWh）', '故障设备编号', '设备类型',
                              '故障设备部件', '故障设备子部件', '设备厂家', '设备型号', '停运等级', '修复进度', '发现时间', '发生时间', '预计修复时间', '实际修复时间',
                              '售后负责人', '故障现象', '故障原因及影响因素分析', '现场解决方案', '后续预防措施', '消耗备件']]
                out_record = user_session.query(OutageRecord).filter(*filters).order_by(OutageRecord.id.desc()).all()
                if out_record:
                    for item in out_record:
                        project_name = station_descr
                        area = item.area if item.area else ''
                        effect_range = user_session.query(
                            OutageEffectRange.effect_range).filter(
                            OutageEffectRange.outage_id == item.id, OutageEffectRange.is_use == 1).all()
                        effect_range_str = ''
                        if effect_range:
                            effect_range_list = [i[0] for i in effect_range]
                            effect_range_str = '、'.join(effect_range_list)
                        effect_capacity = item.effect_capacity if item.effect_capacity else ''
                        device_sn = item.device_sn if item.device_sn else ''
                        device_type_name = user_session.query(DeviceType.name).filter(
                            DeviceType.id == item.device_type_id).scalar() or ""
                        device_unit_name = user_session.query(DeviceUnit.name).filter(
                            DeviceUnit.id == item.device_unit_id).scalar() or ""
                        device_child_unit_name = user_session.query(DeviceChildUnit.name).filter(
                            DeviceChildUnit.id == item.device_child_unit_id).scalar() or ""
                        if device_unit_name == "":
                            device_child_unit_name = ""
                        discover_time = item.discover_time.strftime('%Y-%m-%d %H:%M:%S') if item.discover_time else ""
                        occur_time = item.occur_time.strftime('%Y-%m-%d %H:%M:%S') if item.occur_time else ""
                        expect_repair_time = item.expect_repair_time.strftime('%Y-%m-%d %H:%M:%S') if (
                            item.expect_repair_time) else ""
                        reality_repair_time = item.reality_repair_time.strftime(
                            '%Y-%m-%d %H:%M:%S') if item.reality_repair_time else ""
                        fault_phenomenon = item.fault_phenomenon
                        fault_reason = item.fault_reason
                        scene_solution = item.scene_solution
                        preventive_measure = item.preventive_measure
                        spare_list = user_session.query(OutageSpareUseRecord).filter(
                            OutageSpareUseRecord.outage_id == item.id, OutageSpareUseRecord.is_use == 1).all()
                        spare_str = ''
                        if spare_list:
                            spare_data = [
                                str(item.spare_info.name) + '-' + str(item.use_num) for item in spare_list
                            ]
                            spare_str = '、'.join(spare_data)
                        factory = item.factory if item.factory else ''
                        device_no = item.device_no if item.device_no else ''
                        outage_grade_name = item.outage_grade.name if item.outage_grade_id else ""
                        repair_schedule_name = item.repair_schedule.name if item.repair_schedule_id else ""
                        data_list.append([project_name, '自持项目', item.record_no, area, effect_range_str, effect_capacity, device_sn,
                                          device_type_name, device_unit_name, device_child_unit_name, factory, device_no, outage_grade_name, repair_schedule_name, discover_time,
                                          occur_time, expect_repair_time, reality_repair_time, item.record_name, fault_phenomenon,
                                          fault_reason, scene_solution, preventive_measure, spare_str])

                # 将数据转换为DataFrame
                df = pd.DataFrame(data_list[1:], columns=data_list[0])

                # 创建一个BytesIO对象，用于保存Excel文件的二进制数据
                excel_buffer = io.BytesIO()

                # 将DataFrame写入Excel文件
                df.to_excel(excel_buffer, index=False)

                # 将BytesIO对象的位置重置到开始，以便从头读取数据
                excel_buffer.seek(0)

                # 将二进制数据转换为字节
                binary_data = excel_buffer.read()

                # 定义存储桶名称和对象名称（即文件名）
                bucket_name = 'rhyc'

                object_name = (station_descr.replace("/", "-") + "故障记录" + str(timeUtils.getNewTimeStr()[0:10].replace('-', '')) + "-" +
                               str(uuid.uuid4()) + ".xlsx")

                # 调用upload_file方法上传Excel文件
                storage_url = upload_file(binary_data, bucket_name, object_name)
                storage_url = storage_url.split('?', 1)[0]

                return self.returnTypeSuc({'down_url': storage_url})
            elif action == 'GetAffectingCapacity':
                """
                获取影响停运容量
                """
                effect_range = self.get_argument('effect_range', '[]')
                project_id = self.get_argument('project_id', None)
                if not project_id or not effect_range:
                    return self.customError("参数错误")
                if not self.can_be_parsed_as_list(effect_range):
                    return self.customError("参数类型错误")
                data_list = json.loads(effect_range)
                if len(data_list) == 0:
                    return self.returnTypeSuc(data={'capacity': None}, info=None)

                # 先获取当前项目电站对应的project_info的信息
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取project_info下对应的设备清单中的类型名称列表
                sum_nonan = self.gte_effect_capacity(data_list, project_info)

                return self.returnTypeSuc({'capacity': sum_nonan})
            elif action == 'GetFactoryAndDeviceNo':
                """
                获取设备厂家和设备型号列表
                """
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                project_id = self.get_argument('project_id', None)
                if not project_id or not device_sn:
                    return self.customError("参数错误")
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                # 获取所选设备编号对应的设备型号id
                device_no_data = user_session.query(ProjectDevice.device_no_id, ProjectDevice.factory_id).filter(
                    ProjectDevice.project_id == project_info.id, ProjectDevice.sn == device_sn,
                    ProjectDevice.is_use == 1).first()
                if not device_no_data:
                    data = {
                        "factory": "",
                        "device_no": ""
                    }
                    return self.returnTypeSuc(data)
                device_no_id = device_no_data[0]
                factory_id = device_no_data[1]
                # 获取设备型号对应的t_device_library中的id
                # 厂家
                factory_data = user_session.query(Manufacturer).filter(Manufacturer.id == factory_id).first()
                factory_name = ''
                if factory_data:
                    factory_name = factory_data.descr
                # 设备型号
                device_no_data = user_session.query(DeviceNo).filter(
                    DeviceNo.id == device_no_id).first()
                device_no_name = ''
                if device_no_data:
                    device_no_name = device_no_data.name

                data = {
                    "factory": factory_name,
                    "device_no": device_no_name
                }
                return self.returnTypeSuc(data)

        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self, action):
        self.refreshSession()
        try:
        # if 1:
            session = self.getOrNewSession()
            user_id = session.user['id']
            user_name = session.user['name']
            user_en_name = session.user['en_name']
            if action == 'AddOutageRecord':
                """
                添加停运记录
                """
                project_id = self.get_argument('project_id', None)  # 项目id
                area = self.get_argument('area', None)  # 分区
                device_type_id = self.get_argument('device_type_id', None)  # 设备类型id
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                effect_range = self.get_argument('effect_range', None)  # 影响范围集合
                device_unit_id = self.get_argument('device_unit_id', None)  # 故障设备部件id
                device_child_unit = self.get_argument('device_child_unit', None)  # 故障设备子部件
                effect_capacity = self.get_argument('effect_capacity', None)  # 影响（停运）容量，单位kWh
                outage_grade_id = self.get_argument('outage_grade_id', None)  # 停运等级id
                repair_schedule_id = self.get_argument('repair_schedule_id', None)  # 修复进度id
                discover_time = self.get_argument('discover_time', None)  # 发现时间
                occur_time = self.get_argument('occur_time', None)  # 发生时间
                expect_repair_time = self.get_argument('expect_repair_time', None)  # 预计修复时间
                reality_repair_time = self.get_argument('reality_repair_time', None)  # 实际修复时间
                fault_phenomenon = self.get_argument('fault_phenomenon', None)  # 故障现象
                fault_reason = self.get_argument('fault_reason', None)  # 故障原因及影响因素分析
                scene_solution = self.get_argument('scene_solution', None)  # 现场查过解决方案
                preventive_measure = self.get_argument('preventive_measure', None)  # 后续预防措施
                factory = self.get_argument('factory', None)  # 设备厂家
                device_no = self.get_argument('device_no', None)  # 设备型号
                record_name = self.get_argument('record_name', None)  # 售后对接人

                if DEBUG:
                    logging.info(
                        'project_id:%s, area:%s, device_type_id:%s, device_sn:%s, effect_range:%s, '
                        'device_unit_id:%s, device_child_unit:%s, effect_capacity:%s, outage_grade_id:%s, '
                        'repair_schedule_id:%s, discover_time:%s, occur_time:%s, expect_repair_time:%s, '
                        'reality_repair_time:%s, fault_phenomenon:%s, fault_reason:%s, scene_solution:%s, '
                        'preventive_measure:%s',
                        project_id, area, device_type_id, device_sn, effect_range, device_unit_id, device_child_unit,
                        effect_capacity, outage_grade_id, repair_schedule_id, discover_time, occur_time,
                        expect_repair_time, reality_repair_time, fault_phenomenon, fault_reason, scene_solution,
                        preventive_measure)

                if (not project_id or not area or not device_type_id or not device_sn or not effect_range
                        or not effect_capacity or not outage_grade_id or not repair_schedule_id or not occur_time
                        or not expect_repair_time or not fault_phenomenon or not record_name):
                    return self.customError("参数错误")
                if (not re.compile(r'^-?\d+$').match(project_id) or not re.compile(r'^-?\d+$').match(
                        device_type_id) or
                        not re.compile(r'^-?\d+$').match(outage_grade_id) or not
                        re.compile(r'^-?\d+$').match(repair_schedule_id)):
                    return self.customError("参数类型错误")
                if device_unit_id:
                    if not re.compile(r'^-?\d+$').match(device_unit_id):
                        return self.customError("参数类型错误")
                    device_unit_id = int(device_unit_id)
                else:
                    device_unit_id = None

                if not re.compile(r'^-?\d+(\.\d+)?$').match(effect_capacity):
                    return self.customError("参数类型错误")

                if not self.can_be_parsed_as_list(effect_range):
                    return self.customError("参数类型错误")
                effect_range = json.loads(effect_range)
                if len(effect_range) == 0:
                    return self.customError("参数错误")
                # 获取项目name
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                if occur_time:
                    if not self.judge_is_date(occur_time, '%Y-%m-%d %H:%M'):
                        return self.customError("发生时间格式错误")
                if discover_time:
                    if not self.judge_is_date(discover_time, '%Y-%m-%d %H:%M'):
                        return self.customError("发现时间格式错误")
                if expect_repair_time:
                    if not self.judge_is_date(expect_repair_time, '%Y-%m-%d %H:%M'):
                        return self.customError("预计修复时间格式错误")
                if reality_repair_time:
                    if not self.judge_is_date(reality_repair_time, '%Y-%m-%d %H:%M'):
                        return self.customError("实际修复时间格式错误")
                if len(record_name) > 50:
                    return self.customError("售后对接人长度不能超过50")

                # 查询 t_outage_record 表中是否存在符合条件的记录
                record = user_session.query(OutageRecord).filter(
                        OutageRecord.area == area,
                        OutageRecord.device_type_id == device_type_id,
                        OutageRecord.device_sn == device_sn,
                        OutageRecord.outage_grade_id == outage_grade_id,
                        OutageRecord.occur_time == self.translate_date(occur_time + ":00")
                ).all()
                if record:
                    for item in record:
                        record_effect = user_session.query(OutageEffectRange.effect_range).filter(
                            OutageEffectRange.outage_id == item.id,
                            OutageEffectRange.is_use == 1
                        ).all()
                        record_effect_list = [i[0] for i in record_effect]
                        if set(record_effect_list) == set(effect_range):
                            return self.customError("该记录已存在")

                # 如果故障设备子部件存在则新增设备子部件
                device_child_unit_id = None
                if device_child_unit:
                    if not device_unit_id:
                        return self.customError("请选择故障设备部件")
                    # 先确认是否已有此设备子部件
                    device_data = user_session.query(DeviceChildUnit).filter(
                        DeviceChildUnit.name == device_child_unit, DeviceChildUnit.parent_id == device_unit_id).first()
                    if device_data:
                        device_child_unit_id = device_data.id
                    else:
                        add_child_unit = DeviceChildUnit(creat_time=timeUtils.getNewTimeStr(), name=device_child_unit,
                                                         parent_id=device_unit_id)
                        user_session.add(add_child_unit)
                        user_session.flush()
                        device_child_unit_id = add_child_unit.id
                if discover_time:
                    discover_time = self.translate_date(discover_time + ":00")
                else:
                    discover_time = None

                effect_capacity = Decimal(effect_capacity)
                # 计算影响容量
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    capacity_status = 2
                else:
                    # 获取project_info下对应的设备清单中的类型名称列表
                    sum_nonan = self.gte_effect_capacity(effect_range, project_info)
                    capacity_status = 1 if Decimal(sum_nonan) == effect_capacity else 2

                # 获取影响时间段
                if not reality_repair_time:
                    reality_repair_time = timeUtils.getNewTimeStr()
                effect_period = self.get_outage_effect_period(occur_time, reality_repair_time, station_name)

                # 获取影响时长
                total_seconds = 0
                if effect_period:
                    for item in product(effect_period):
                        start_time = item[0]['window_start']
                        end_time = item[0]['window_end']
                        total_seconds += (end_time - start_time).total_seconds()
                # 计算影响时长 单位分钟 四舍五入
                effect_duration = round(total_seconds / 60)
                # 获取故障编号
                record_no = self.get_record_no(occur_time, station_name, area)

                # 保存停运记录
                add_record = OutageRecord(creat_time=timeUtils.getNewTimeStr(), project_id=int(project_id),
                                          area=area, device_type_id=int(device_type_id), device_sn=device_sn,
                                          device_unit_id=device_unit_id, device_child_unit_id=device_child_unit_id,
                                          effect_capacity=float(effect_capacity),
                                          outage_grade_id=int(outage_grade_id),
                                          repair_schedule_id=int(repair_schedule_id), discover_time=discover_time,
                                          occur_time=self.translate_date(occur_time + ":00"),
                                          expect_repair_time=self.translate_date(expect_repair_time + ":00"),
                                          reality_repair_time=self.translate_date(reality_repair_time[0:16] + ":00") if reality_repair_time else None,
                                          fault_phenomenon=fault_phenomenon, fault_reason=fault_reason,
                                          factory=factory, record_name=record_name, device_no=device_no,
                                          affect_time=str(effect_duration), record_no=record_no,
                                          scene_solution=scene_solution, preventive_measure=preventive_measure,
                                          creat_user_id=user_id, creat_type=1, capacity_status=capacity_status)
                user_session.add(add_record)
                user_session.flush()
                outage_record_id = add_record.id
                # 保存影响范围
                data_to_insert = [
                    {
                        "creat_time": timeUtils.getNewTimeStr(),
                        "outage_id": outage_record_id,
                        "effect_range": item
                    }
                    for item in product(effect_range)
                ]
                # 执行批量插入
                user_session.bulk_insert_mappings(OutageEffectRange, data_to_insert)

                # 计算影响时间段并保存

                if effect_period:
                    period_data_to_insert = [
                        {
                            "creat_time": timeUtils.getNewTimeStr(),
                            "outage_id": outage_record_id,
                            "begin_time": item[0]['window_start'],
                            "end_time": item[0]['window_end'],
                            "effect_pw": item[0]['real_pws'],
                            "add_type": 1
                        }
                        for item in product(effect_period)
                    ]
                    user_session.bulk_insert_mappings(OutageEffectPeriodRecord, period_data_to_insert)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif action == 'AddOutageRecordByBatch':
                """
                批量添加停运记录
                """
                file_title = ['*故障所在分区', '*故障影响范围', '*停运容量（kWh）', '*故障设备编号', '*设备类型', '故障设备部件',
                              '故障设备子部件', '设备厂家', '设备型号', '*停运等级', '*修复进度', '发现时间', '*发生时间', '*预计修复时间', '实际修复时间', '*售后对接人', '*故障现象',
                              '故障原因及影响因素分析', '现场解决方案', '后续预防措施']
                project_id = self.get_argument('project_id', None)
                is_repetition = False
                if not project_id:
                    return self.customError("参数错误")
                    # 获取项目name
                station_name = self.get_station_name_by_id(project_id)
                if not station_name:
                    return self.customError("项目不存在")
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    return self.customError("项目不存在")
                file_path = '/home/<USER>/outagerecordfiles'
                files = self.request.files
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                files_obj = files.get('file')

                data = files_obj[0].get('body')
                filename = files_obj[0].get('filename')
                max_file_size = 40 * 1024 * 1024  # 40MB限制
                file_size = len(data)
                if file_size > max_file_size:
                    return self.customError("文件大小超过40M限制")
                if not filename.endswith(('.xls','.xlsx')):
                    return self.customError('上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障记录信息！')
                prefix, extension = filename.rsplit('.', 1)
                characters = string.ascii_letters + string.digits  # 包含字母和数字
                short_uuid = ''.join(random.choice(characters) for _ in range(8))
                new_filename = '%s.%s' % (prefix+short_uuid, extension)
                path = '%s/%s' % (file_path, new_filename)
                file = open(path, 'wb')
                file.write(data)
                file.close()
                file_data = pd.read_excel(path, header=None)
                file_data = file_data.dropna(how='all', thresh=1)
                values_arr = file_data.values  # 二维矩阵
                values_arr[pd.isna(values_arr)] = None
                for i in range(len(file_title)):
                    if file_title[i] != values_arr[0][i]:
                        logging.error("标准title:%s  文件title:%s" % (file_title[i], values_arr[1][i]))
                        return self.customError(
                            '上传表格表头错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障记录信息！')
                # 先验证所有数据是否符合要求
                for val in range(1, len(values_arr)):
                    values = values_arr[val]
                    area = values[0]  # 分区
                    device_sn = values[3]  # 设备编号
                    device_type = values[4]  # 设备类型
                    effect_range = values[1]  # 影响范围 多个用、隔开
                    device_unit = values[5]  # 故障设备部件
                    device_child_unit = values[6]  # 故障设备子部件
                    effect_capacity = values[2]  # 影响（停运）容量，单位kWh
                    discover_time = values[11]  # 发现时间
                    occur_time = values[12]  # 发生时间
                    expect_repair_time = values[13]  # 预计修复时间
                    reality_repair_time = values[14]  # 实际修复时间
                    record_name = values[15]  # 售后对接人
                    outage_grade = values[9]  # 停运等级
                    repair_schedule = values[10]  # 修复进度

                    if not device_type or not device_sn or not occur_time or not record_name:
                        return self.customError('表格数据错误!请填入必填信息')
                    if effect_capacity:
                        if not re.compile(r'^-?\d+(\.\d+)?$').match(str(effect_capacity)):
                            return self.customError("参数类型错误")
                    if (not self.judge_is_date(occur_time, '%Y-%m-%d %H:%M:%S') and
                            not self.judge_is_date(occur_time, '%Y-%m-%d %H:%M')):
                        return self.customError("发生时间类型错误")
                    if len(record_name) > 50:
                        return self.customError("售后对接人长度不正常")
                    # 获取当前时间
                    current_time = datetime.now()
                    # 判断是否晚于当前时间
                    if datetime.strptime(str(occur_time), '%Y-%m-%d %H:%M:%S') > current_time:
                        return self.customError("发生时间不能晚于当前时间")
                    occur_time = self.translate_date_format(occur_time)
                    if discover_time:
                        if (not self.judge_is_date(discover_time, '%Y-%m-%d %H:%M:%S')
                                and not self.judge_is_date(discover_time, '%Y-%m-%d %H:%M')):
                            return self.customError("发现时间类型错误")
                        if datetime.strptime(str(discover_time), '%Y-%m-%d %H:%M:%S') > current_time:
                            return self.customError("发现时间不能晚于当前时间")

                    if expect_repair_time:
                        if (not self.judge_is_date(expect_repair_time, '%Y-%m-%d %H:%M:%S')
                                and not self.judge_is_date(expect_repair_time, '%Y-%m-%d %H:%M')):
                            return self.customError("预计修复时间类型错误")
                    if reality_repair_time:
                        if (not self.judge_is_date(reality_repair_time, '%Y-%m-%d %H:%M:%S')
                                and not self.judge_is_date(reality_repair_time, '%Y-%m-%d %H:%M')):
                            return self.customError("实际修复时间类型错误")
                    if area:
                        filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1,
                                   ProjectDevice.area == area]
                        device_area = user_session.query(ProjectDevice.area).filter(*filters).first()
                        if not device_area:
                            return self.customError('分区错误！请重新填写故障所在分区！')
                    if device_type:
                        device_type_id = user_session.query(DeviceType.id).filter(
                            DeviceType.name == device_type).first()
                        if not device_type_id:
                            return self.customError('设备类型错误！请重新填写设备类型！')
                    if effect_range:
                        effect_range = effect_range.split('、')
                        for effect_item in effect_range:
                            filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.area.like('%电池堆%'),
                                       ProjectDevice.is_use == 1, ProjectDevice.area == effect_item]
                            res = user_session.query(ProjectDevice.area).filter(*filters).first()
                            if not res:
                                return self.customError('影响范围错误！请重新填写影响范围！')
                    device_unit_id = None
                    if device_unit:
                        if not device_sn:
                            return self.customError('所填的故障设备部件没有填所属的设备编号！请重新填写故障设备部件！')
                        # 获取所选设备编号对应的设备型号id
                        device_no_data = user_session.query(ProjectDevice.device_no_id).filter(
                            ProjectDevice.project_id == project_info.id, ProjectDevice.sn == device_sn,
                            ProjectDevice.is_use == 1).first()
                        if not device_no_data:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')
                        device_no_id = device_no_data[0]
                        # 获取设备型号对应的t_device_library中的id
                        device_library_data = user_session.query(DeviceLibrary.id).filter(
                            DeviceLibrary.device_no_id == device_no_id).first()
                        if not device_library_data:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')
                        device_library_id = device_library_data[0]
                        filters = [DeviceUnit.is_use == 1, DeviceUnit.device_id == device_library_id,
                                   DeviceUnit.name == device_unit]
                        device_unit_id = user_session.query(DeviceUnit.id).filter(*filters).first()
                        if not device_unit_id:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')

                    outage_data = user_session.query(OutageRecord).filter(
                        OutageRecord.device_sn == device_sn, OutageRecord.occur_time == occur_time,
                        OutageRecord.project_id == project_id).first()

                    if outage_data:
                        is_repetition = True
                        continue

                    if device_child_unit:
                        if not device_unit:
                            return self.customError("请填写故障设备子部件所属的故障设备部件")
                    # 影响范围
                    if effect_range:
                        effect_range_list = effect_range
                        if len(effect_range_list) == 0:
                            return self.customError('影响范围错误！请重新填写影响范围！')

                    # 停运等级
                    if outage_grade:
                        outage_grade_id = user_session.query(OutageRecordDictionary.id).filter(
                            OutageRecordDictionary.name == outage_grade, OutageRecordDictionary.category == 1).first()
                        if not outage_grade_id:
                            return self.customError('停运等级错误！请重新选择停运等级！')

                    # 修复进度
                    if repair_schedule:
                        repair_schedule_id = user_session.query(OutageRecordDictionary.id).filter(
                            OutageRecordDictionary.name == repair_schedule,
                            OutageRecordDictionary.category == 2).first()
                        if not repair_schedule_id:
                            return self.customError('修复进度错误！请重新选择停运等级！')

                for v in range(1, len(values_arr)):
                    values = values_arr[v]
                    area = values[0]  # 分区
                    device_sn = values[3]  # 设备编号
                    device_type = values[4]  # 设备类型
                    effect_range = values[1]  # 影响范围 多个用、隔开
                    device_unit = values[5]  # 故障设备部件
                    device_child_unit = values[6]  # 故障设备子部件
                    factory = values[7]  # 设备厂家
                    device_no = values[8]  # 设备型号
                    outage_grade = values[9]  # 停运等级
                    repair_schedule = values[10]  # 修复进度
                    effect_capacity = values[2]  # 影响（停运）容量，单位kWh
                    discover_time = values[11]  # 发现时间
                    occur_time = values[12]  # 发生时间
                    expect_repair_time = values[13]  # 预计修复时间
                    reality_repair_time = values[14]  # 实际修复时间
                    record_name = values[15]  # 售后对接人
                    fault_phenomenon = values[16]  # 故障现象
                    fault_reason = values[17]  # 故障原因及影响因素分析
                    scene_solution = values[18]  # 现场查过解决方案
                    preventive_measure = values[19]  # 后续预防措施

                    capacity_status = None
                    if effect_capacity:
                        capacity_status = 2
                    occur_time = self.translate_date_format(occur_time)
                    if discover_time:
                        discover_time = self.translate_date_format(discover_time)

                    if expect_repair_time:
                        expect_repair_time = self.translate_date_format(expect_repair_time)
                    if reality_repair_time:
                        reality_repair_time = self.translate_date_format(reality_repair_time)
                    # if area:
                    #     filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1,
                    #                ProjectDevice.area == area]
                    #     device_area = user_session.query(ProjectDevice.area).filter(*filters).first()
                    #     if not device_area:
                    #         return self.customError('分区错误！请重新填写故障所在分区！')
                    device_type_id = None
                    if device_type:
                        device_type_id = user_session.query(DeviceType.id).filter(
                            DeviceType.name == device_type).first()
                        if not device_type_id:
                            return self.customError('设备类型错误！请重新填写设备类型！')
                        device_type_id = device_type_id[0]
                    # if device_sn:
                    #     if not device_type_id or not area:
                    #         return self.customError('设备编号错误！请填写正确的的分区和设备类型！')
                    #     filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1,
                    #                ProjectDevice.area == area, ProjectDevice.sn == device_sn,
                    #                ProjectDevice.type_id == device_type_id, ProjectDevice.sn.isnot(None)]
                    #     device_sn_list = user_session.query(ProjectDevice.sn).filter(*filters).scalar()
                    #     if not device_sn_list:
                    #         return self.customError('设备编号错误！请重新填写设备编号！')
                    if effect_range:
                        effect_range = effect_range.split('、')
                        for effect_item in effect_range:
                            filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.area.like('%电池堆%'),
                                       ProjectDevice.is_use == 1, ProjectDevice.area == effect_item]
                            res = user_session.query(ProjectDevice.area).filter(*filters).first()
                            if not res:
                                return self.customError('影响范围错误！请重新填写影响范围！')
                    device_unit_id = None
                    if device_unit:
                        if not device_sn:
                            return self.customError('所填的故障设备部件没有填所属的设备编号！请重新填写故障设备部件！')
                        # 获取所选设备编号对应的设备型号id
                        device_no_data = user_session.query(ProjectDevice.device_no_id).filter(
                            ProjectDevice.project_id == project_info.id, ProjectDevice.sn == device_sn,
                            ProjectDevice.is_use == 1).first()
                        if not device_no_data:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')
                        device_no_id = device_no_data[0]
                        # 获取设备型号对应的t_device_library中的id
                        device_library_data = user_session.query(DeviceLibrary.id).filter(
                            DeviceLibrary.device_no_id == device_no_id).first()
                        if not device_library_data:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')
                        device_library_id = device_library_data[0]
                        filters = [DeviceUnit.is_use == 1, DeviceUnit.device_id == device_library_id,
                                   DeviceUnit.name == device_unit]
                        device_unit_id = user_session.query(DeviceUnit.id).filter(*filters).first()
                        if not device_unit_id:
                            return self.customError('故障设备部件错误！请重新填写故障设备部件！')
                        device_unit_id = device_unit_id[0]
                    device_child_unit_id = None

                    outage_data = user_session.query(OutageRecord).filter(
                        OutageRecord.device_sn == device_sn, OutageRecord.occur_time == occur_time,
                        OutageRecord.project_id == project_id).first()

                    if outage_data:
                        is_repetition = True
                        continue

                    if device_child_unit:
                        if not device_unit:
                            return self.customError("请填写故障设备子部件所属的故障设备部件")
                        # 先确认是否已有此设备子部件
                        device_data = user_session.query(DeviceChildUnit).filter(
                            DeviceChildUnit.name == device_child_unit,
                            DeviceChildUnit.parent_id == device_unit_id).first()
                        if device_data:
                            device_child_unit_id = device_data.id
                        else:
                            add_child_unit = DeviceChildUnit(creat_time=timeUtils.getNewTimeStr(),
                                                             name=device_child_unit,
                                                             parent_id=device_unit_id)
                            user_session.add(add_child_unit)
                            user_session.flush()
                            device_child_unit_id = add_child_unit.id

                    # 停运等级
                    outage_grade_id = None
                    if outage_grade:
                        outage_grade_id = user_session.query(OutageRecordDictionary.id).filter(
                            OutageRecordDictionary.name == outage_grade,
                            OutageRecordDictionary.category == 1).first()
                        if not outage_grade_id:
                            return self.customError('停运等级错误！请重新选择停运等级！')
                        outage_grade_id = outage_grade_id[0]

                    # 修复进度
                    repair_schedule_id = None
                    if repair_schedule:
                        repair_schedule_id = user_session.query(OutageRecordDictionary.id).filter(
                            OutageRecordDictionary.name == repair_schedule,
                            OutageRecordDictionary.category == 2).first()
                        if not repair_schedule_id:
                            return self.customError('修复进度错误！请重新选择停运等级！')
                        repair_schedule_id = repair_schedule_id[0]

                    # 获取故障编号
                    record_no = self.get_record_no(occur_time, station_name, area)
                    add_record = OutageRecord(creat_time=timeUtils.getNewTimeStr(), project_id=int(project_id),
                                              area=area, device_type_id=device_type_id, device_sn=device_sn,
                                              device_unit_id=device_unit_id, device_child_unit_id=device_child_unit_id,
                                              effect_capacity=Decimal(effect_capacity) if effect_capacity else None,
                                              discover_time=self.translate_date(str(discover_time) + ":00") if discover_time else None,
                                              occur_time=self.translate_date(str(occur_time) + ":00") if occur_time else None,
                                              expect_repair_time=self.translate_date(str(expect_repair_time) + ":00")
                                              if expect_repair_time else None,
                                              reality_repair_time=self.translate_date(
                                                  str(reality_repair_time) + ":00") if reality_repair_time else None,
                                              fault_phenomenon=fault_phenomenon, fault_reason=fault_reason,
                                              scene_solution=scene_solution, preventive_measure=preventive_measure,
                                              creat_user_id=user_id, creat_type=2, capacity_status=capacity_status,
                                              upload_file_url=path,
                                              factory=factory if factory else None,
                                              device_no=device_no if device_no else None,
                                              record_name=record_name, outage_grade_id=outage_grade_id, repair_schedule_id=repair_schedule_id,
                                              record_no=record_no, affect_time='0')
                    user_session.add(add_record)
                    user_session.flush()
                    outage_record_id = add_record.id
                    # 保存影响范围
                    if effect_range:
                        effect_range_list = effect_range
                        if len(effect_range_list) == 0:
                            user_session.rollback()
                            return self.customError('影响范围错误！请重新填写影响范围！')
                        data_to_insert = [
                            {
                                "creat_time": timeUtils.getNewTimeStr(),
                                "outage_id": outage_record_id,
                                "effect_range": item
                            }
                            for item in product(effect_range_list)
                        ]
                        # 执行批量插入
                        user_session.bulk_insert_mappings(OutageEffectRange, data_to_insert)

                    # 计算影响时间段并保存
                    if not reality_repair_time:
                        reality_repair_time = timeUtils.getNewTimeStr()[0:16]
                    effect_period = self.get_outage_effect_period(str(occur_time), str(reality_repair_time), station_name)
                    # if effect_period:
                    #     period_data_to_insert = [
                    #         {
                    #             "creat_time": timeUtils.getNewTimeStr(),
                    #             "outage_id": outage_record_id,
                    #             "begin_time": item[0]['window_start'],
                    #             "end_time": item[0]['window_end'],
                    #             "effect_pw": item[0]['real_pws'],
                    #             "add_type": 1
                    #         }
                    #         for item in product(effect_period)
                    #     ]
                    #     user_session.bulk_insert_mappings(OutageEffectPeriodRecord, period_data_to_insert)
                    total_effect_seconds = 0
                    if effect_period:
                        period_data_to_insert = []
                        for item in product(effect_period):
                            period_data_to_insert.append(
                                {
                                    "creat_time": timeUtils.getNewTimeStr(),
                                    "outage_id": outage_record_id,
                                    "begin_time": item[0]['window_start'],
                                    "end_time": item[0]['window_end'],
                                    "effect_pw": item[0]['real_pws'],
                                    "add_type": 1
                                }
                            )
                            total_effect_seconds += (item[0]['window_end'] - item[0]['window_start']).total_seconds()
                        user_session.bulk_insert_mappings(OutageEffectPeriodRecord, period_data_to_insert)
                    if total_effect_seconds != 0:
                        add_record.affect_time = round(total_effect_seconds / 60)
                user_session.commit()
                if is_repetition:
                    return self.returnTypeSuc(data='', info='发现重复录入的故障记录！无法录入重复的故障纪录')
                return self.returnTypeSuc(data='', info=None)
            elif action == 'EditOutageRecord':
                """
                编辑停运记录
                """
                record_id = self.get_argument('record_id', None)  # 停运记录id
                # project_id = self.get_argument('project_id', None)  # 项目id
                area = self.get_argument('area', None)  # 分区
                device_type_id = self.get_argument('device_type_id', None)  # 设备类型id
                device_sn = self.get_argument('device_sn', None)  # 设备编号
                effect_range = self.get_argument('effect_range', None)  # 影响范围集合
                device_unit_id = self.get_argument('device_unit_id', None)  # 故障设备部件id
                device_child_unit = self.get_argument('device_child_unit', None)  # 故障设备子部件
                effect_capacity = self.get_argument('effect_capacity', None)  # 影响（停运）容量，单位kWh
                outage_grade_id = self.get_argument('outage_grade_id', None)  # 停运等级id
                repair_schedule_id = self.get_argument('repair_schedule_id', None)  # 修复进度id
                discover_time = self.get_argument('discover_time', None)  # 发现时间
                occur_time = self.get_argument('occur_time', None)  # 发生时间
                expect_repair_time = self.get_argument('expect_repair_time', None)  # 预计修复时间
                reality_repair_time = self.get_argument('reality_repair_time', None)  # 实际修复时间
                fault_phenomenon = self.get_argument('fault_phenomenon', None)  # 故障现象
                fault_reason = self.get_argument('fault_reason', None)  # 故障原因及影响因素分析
                scene_solution = self.get_argument('scene_solution', None)  # 现场查过解决方案
                preventive_measure = self.get_argument('preventive_measure', None)  # 后续预防措施
                factory = self.get_argument('factory', None)  # 设备厂家
                device_no = self.get_argument('device_no', None)  # 设备型号
                record_name = self.get_argument('record_name', None)  # 售后对接人

                if DEBUG:
                    logging.info(
                        'area:%s, device_type_id:%s, device_sn:%s, effect_range:%s, '
                        'device_unit_id:%s, device_child_unit:%s, effect_capacity:%s, outage_grade_id:%s, '
                        'repair_schedule_id:%s, discover_time:%s, occur_time:%s, expect_repair_time:%s, '
                        'reality_repair_time:%s, fault_phenomenon:%s, fault_reason:%s, scene_solution:%s, '
                        'preventive_measure:%s',
                        area, device_type_id, device_sn, effect_range, device_unit_id, device_child_unit,
                        effect_capacity, outage_grade_id, repair_schedule_id, discover_time, occur_time,
                        expect_repair_time, reality_repair_time, fault_phenomenon, fault_reason, scene_solution,
                        preventive_measure)

                if (not area or not device_type_id or not device_sn or not effect_range
                        or not effect_capacity or not outage_grade_id or not repair_schedule_id or not occur_time
                        or not expect_repair_time or not fault_phenomenon or not record_id or not record_name):
                    return self.customError("参数不完整")
                if (not re.compile(r'^-?\d+$').match(device_type_id) or
                        not re.compile(r'^-?\d+$').match(outage_grade_id) or not
                        re.compile(r'^-?\d+$').match(repair_schedule_id) or not
                        re.compile(r'^-?\d+$').match(record_id)):
                    return self.customError("参数类型错误")
                if device_unit_id:
                    if not re.compile(r'^-?\d+$').match(device_unit_id):
                        return self.customError("参数类型错误")
                    device_unit_id = int(device_unit_id)
                else:
                    device_unit_id = None
                if not re.compile(r'^-?\d+(\.\d+)?$').match(effect_capacity):
                    return self.customError("参数类型错误")

                if not self.can_be_parsed_as_list(effect_range):
                    return self.customError("参数类型错误")
                effect_range = json.loads(effect_range)
                if len(effect_range) == 0:
                    return self.customError("参数错误")
                outage_record = user_session.query(OutageRecord).filter(OutageRecord.id == record_id).first()
                if not outage_record:
                    return self.customError("故障记录不存在")
                if discover_time:
                    if not self.judge_is_date(discover_time, '%Y-%m-%d %H:%M'):
                        return self.customError("发现时间格式错误")
                if occur_time:
                    if not self.judge_is_date(occur_time, '%Y-%m-%d %H:%M'):
                        return self.customError("发生时间格式错误")
                if expect_repair_time:
                    if not self.judge_is_date(expect_repair_time, '%Y-%m-%d %H:%M'):
                        return self.customError("预计修复时间格式错误")
                if reality_repair_time:
                    if not self.judge_is_date(reality_repair_time, '%Y-%m-%d %H:%M'):
                        return self.customError("实际修复时间格式错误")
                if len(record_name) > 50:
                    return self.customError("售后对接人长度不能超过50")

                # 如果故障设备子部件存在则新增设备子部件
                device_child_unit_id = outage_record.device_child_unit_id
                if device_child_unit:
                    if not device_unit_id:
                        return self.customError("请选择故障设备部件")
                    # 先确认是否已有此设备子部件
                    device_data = user_session.query(DeviceChildUnit).filter(
                        DeviceChildUnit.name == device_child_unit, DeviceChildUnit.parent_id == device_unit_id).first()
                    if device_data:
                        device_child_unit_id = device_data.id
                    else:
                        add_child_unit = DeviceChildUnit(creat_time=timeUtils.getNewTimeStr(),
                                                         name=device_child_unit,
                                                         parent_id=device_unit_id)
                        user_session.add(add_child_unit)
                        user_session.flush()
                        device_child_unit_id = add_child_unit.id
                if discover_time:
                    discover_time = self.translate_date(discover_time + ":00")
                    if discover_time != outage_record.discover_time:
                        outage_record.discover_time = discover_time
                else:
                    outage_record.discover_time = None
                if area != outage_record.area:
                    outage_record.area = area
                if int(device_type_id) != outage_record.device_type_id:
                    outage_record.device_type_id = int(device_type_id)
                if device_sn != outage_record.device_sn:
                    outage_record.device_sn = device_sn
                if device_unit_id != outage_record.device_unit_id:
                    outage_record.device_unit_id = device_unit_id
                if device_child_unit_id != outage_record.device_child_unit_id:
                    outage_record.device_child_unit_id = device_child_unit_id
                if not device_child_unit:
                    outage_record.device_child_unit_id = None
                if float(effect_capacity) != outage_record.effect_capacity:
                    outage_record.effect_capacity = float(effect_capacity)
                if int(outage_grade_id) != outage_record.outage_grade_id:
                    outage_record.outage_grade_id = int(outage_grade_id)
                if int(repair_schedule_id) != outage_record.repair_schedule_id:
                    outage_record.repair_schedule_id = int(repair_schedule_id)
                if self.translate_date(occur_time + ":00") != outage_record.occur_time:
                    outage_record.occur_time = self.translate_date(occur_time + ":00")
                if self.translate_date(expect_repair_time + ":00") != outage_record.expect_repair_time:
                    outage_record.expect_repair_time = self.translate_date(expect_repair_time + ":00")
                if fault_phenomenon != outage_record.fault_phenomenon:
                    outage_record.fault_phenomenon = fault_phenomenon
                if fault_reason != outage_record.fault_reason:
                    outage_record.fault_reason = fault_reason
                if scene_solution != outage_record.scene_solution:
                    outage_record.scene_solution = scene_solution
                if preventive_measure != outage_record.preventive_measure:
                    outage_record.preventive_measure = preventive_measure
                if factory != outage_record.factory:
                    outage_record.factory = factory
                if record_name != outage_record.record_name:
                    outage_record.record_name = record_name
                if device_no != outage_record.device_no:
                    outage_record.device_no = device_no
                if reality_repair_time:
                    outage_record.reality_repair_time = self.translate_date(reality_repair_time + ":00")
                else:
                    outage_record.reality_repair_time = None
                # 获取项目name
                station_name = self.get_station_name_by_id(outage_record.project_id)
                if not station_name:
                    return self.customError("项目不存在")
                effect_capacity = Decimal(effect_capacity)
                # 计算影响容量
                project_info = user_session.query(ProjectInfo).filter(ProjectInfo.name == station_name,
                                                                      ProjectInfo.is_use == 1).first()
                if not project_info:
                    capacity_status = 2
                else:
                    # 获取project_info下对应的设备清单中的类型名称列表
                    sum_nonan = self.gte_effect_capacity(effect_range, project_info)
                    capacity_status = 1 if Decimal(sum_nonan) == effect_capacity else 2
                outage_record.capacity_status = capacity_status

                # 先获取当前停运记录的影响范围
                effect_range_now = user_session.query(OutageEffectRange).filter(
                    OutageEffectRange.outage_id == record_id, OutageEffectRange.is_use == 1).all()
                effect_range_list = [item.effect_range for item in effect_range_now]
                if set(effect_range_list) != set(effect_range):
                    set1 = set(effect_range_list)
                    set2 = set(effect_range)
                    # 获取交集
                    intersection = list(set1.intersection(set2))
                    # 获取各自独有元素
                    unique_in_list1 = list(set1.difference(set2))  # 将这个集合的逻辑删除
                    unique_in_list2 = list(set2.difference(set1))  # 将这个集合的做增加操作

                    # 逻辑删除
                    if len(unique_in_list1) > 0:
                        user_session.query(OutageEffectRange).filter(
                            OutageEffectRange.outage_id == int(record_id),
                            OutageEffectRange.effect_range.in_(unique_in_list1)).update(
                            {OutageEffectRange.is_use: 0}, synchronize_session=False)

                    if len(unique_in_list2) > 0:
                    # 保存影响范围
                        for item in effect_range:
                            outage_effect_range = user_session.query(OutageEffectRange).filter(
                                OutageEffectRange.outage_id == record_id, OutageEffectRange.effect_range == item, OutageEffectRange.is_use == 1).first()
                            if not outage_effect_range:
                                add_outage_effect_range = OutageEffectRange(creat_time=timeUtils.getNewTimeStr(),
                                                                            effect_range=item, outage_id=int(record_id))
                                user_session.add(add_outage_effect_range)

                # 如果实际修复时间不为空且与原有实际修复时间不一致则需要重新计算影响运行时间段信息
                if not reality_repair_time and not outage_record.reality_repair_time:
                    reality_repair_time = timeUtils.getNewTimeStr()
                if ((reality_repair_time and self.translate_date(reality_repair_time[0:16] + ":00") !=
                     outage_record.reality_repair_time) or (self.translate_date(occur_time + ":00") !=
                                                            outage_record.reality_repair_time)):
                    if not reality_repair_time:
                        reality_repair_time = timeUtils.getNewTimeStr()
                    # 如果有变化先将之前的影响运行时段没有编辑过的数据逻辑删除
                    user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id),
                        OutageEffectPeriodRecord.add_type == 1, OutageEffectPeriodRecord.is_edit == 2).update(
                        {OutageEffectPeriodRecord.is_use: 0}, synchronize_session=False)
                    # 查询出已编辑过的影响运行时段数据
                    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id),
                        OutageEffectPeriodRecord.is_edit == 1, OutageEffectPeriodRecord.is_use == 1).all()
                    total_effect_seconds = 0
                    if effect_periods_by_outage_id:
                        for item in effect_periods_by_outage_id:
                            total_effect_seconds += (item.end_time - item.begin_time).total_seconds()

                    station_name = self.get_station_name_by_id(outage_record.project_id)
                    effect_period = self.get_outage_effect_period(occur_time, reality_repair_time, station_name)
                    if effect_period:
                        period_data_to_insert = []
                        for item in product(effect_period):
                            period_data_to_insert.append(
                                {
                                    "creat_time": timeUtils.getNewTimeStr(),
                                    "outage_id": int(record_id),
                                    "begin_time": item[0]['window_start'],
                                    "end_time": item[0]['window_end'],
                                    "effect_pw": item[0]['real_pws'],
                                    "add_type": 1
                                }
                            )
                            total_effect_seconds += (item[0]['window_end'] - item[0]['window_start']).total_seconds()
                        # period_data_to_insert = [
                        #     {
                        #         "creat_time": timeUtils.getNewTimeStr(),
                        #         "outage_id": int(record_id),
                        #         "begin_time": item[0]['window_start'],
                        #         "end_time": item[0]['window_end'],
                        #         "effect_pw": item[0]['real_pws'],
                        #         "add_type": 1
                        #     }
                        #     for item in product(effect_period)
                        # ]
                        user_session.bulk_insert_mappings(OutageEffectPeriodRecord, period_data_to_insert)
                    if total_effect_seconds != 0:
                            outage_record.affect_time = round(total_effect_seconds / 60)

                    # effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                    #     OutageEffectPeriodRecord.outage_id == record_id, OutageEffectPeriodRecord.is_use == 1).all()
                    # if effect_periods_by_outage_id:
                    #     b_combinations = {(item.begin_time, item.end_time) for item in effect_periods_by_outage_id}
                    #     # 找出计算出的值不在查询结果中的组合
                    #     diff = [item for item in effect_period if (item['window_start'], item['window_end']) not in
                    #             b_combinations]
                    #     if len(diff) > 0:
                    #         # 如果有不同的集合 则需要逐个去排查
                    #         period_data_to_insert = []
                    #         for item in diff:
                    #             period_data_to_insert.append({
                    #                 "creat_time": timeUtils.getNewTimeStr(),
                    #                 "outage_id": int(record_id),
                    #                 "begin_time": item['window_start'],
                    #                 "end_time": item['window_end'],
                    #                 "effect_pw": item['real_pws'],
                    #                 "add_type": 1
                    #             })
                    #         user_session.bulk_insert_mappings(OutageEffectPeriodRecord, period_data_to_insert)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif action == 'ManageEffectPeriod':
                """
                批量添加/编辑/删除影响运行时间段
                """
                record_id = self.get_argument('outage_record_id', None)  # 停运记录id
                data = self.get_argument('data', '[]')  # 所有数据集合
                if DEBUG:
                    logging.info('record_id:%s,data:%s' % (record_id, data))
                if not self.can_be_parsed_as_list(data):
                    return self.customError("参数类型错误")
                if not record_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(record_id):
                    return self.customError('参数不合规')
                data_list = json.loads(data)
                if len(data_list) == 0:
                    return self.returnTypeSuc(data='', info=None)
                record_info = user_session.query(OutageRecord).filter(
                    OutageRecord.id == int(record_id), OutageRecord.is_use == 1).first()
                if not record_info:
                    return self.customError("故障记录不存在")
                # 查询出所有已有停运记录的影响运行时间段
                not_ids = []
                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if not len(item['data']):
                        continue
                    if item['type'] == "delete":  # 删除
                        del_ids = [int(del_id['id']) for del_id in item['data']]
                        not_ids.extend(del_ids)
                    if item['type'] == "edit":
                        edit_ids = [int(edit_id['id']) for edit_id in item['data']]
                        not_ids.extend(edit_ids)
                if len(not_ids) > 0:
                    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id),
                        OutageEffectPeriodRecord.is_use == 1, OutageEffectPeriodRecord.id.not_in(not_ids)).all()
                else:
                    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id), OutageEffectPeriodRecord.is_use == 1).all()
                effect_periods_time_list = []
                if effect_periods_by_outage_id:
                    # 获取时间段列表
                    effect_periods_time_list = [{'begin_time': item.begin_time.strftime("%Y-%m-%d %H:%M:%S"),
                                                 'end_time': item.end_time.strftime("%Y-%m-%d %H:%M:%S")}
                                                for item in effect_periods_by_outage_id]
                sers = []
                del_ids = []
                # 检查参数中的时间是否存在冲突
                compare_list = []
                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if item['type'] == "edit":
                        if not len(item['data']):
                            continue
                        compare_edit_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                          for item_time in item['data']]
                        compare_list.extend(compare_edit_time_list)
                    elif item['type'] == "add":
                        if not len(item['data']):
                            continue
                        compare_add_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                         for item_time in item['data']]
                        compare_list.extend(compare_add_time_list)
                    else:
                        continue
                if self.has_overlapping_intervals(compare_list):
                    return self.customError("影响时间范围有冲突，请确认后重新录入！")

                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if not len(item['data']):
                        continue
                    if item['type'] == "delete":  # 删除
                        if not len(item['data']):
                            continue
                        for del_item in item['data']:
                            keys = del_item.keys()
                            if "id" not in keys:
                                return self.customError('参数不完整')
                            if not re.compile(r'^-?\d+$').match(del_item['id']):
                                return self.customError('参数不合规')
                            del_ids.append(int(del_item['id']))
                    elif item['type'] == "edit":  # 编辑
                        if not len(item['data']):
                            continue
                        edit_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                          for item_time in item['data']]
                        has_intersections = self.check_intersections(effect_periods_time_list, edit_time_list)
                        if has_intersections:
                            return self.customError("影响时间范围有冲突，请确认后重新录入！")
                        effect_periods_time_list.extend(edit_time_list)
                        for edit_item in item['data']:
                            update_dic = {}
                            edit_keys = edit_item.keys()
                            if "id" not in edit_keys:
                                return self.customError('参数不完整')
                            up_id = int(edit_item['id'])
                            if "begin_time" in edit_keys:
                                update_dic['begin_time'] = edit_item['begin_time']
                            if "end_time" in edit_keys:
                                update_dic['end_time'] = edit_item['end_time']
                            if "remark" in edit_keys:
                                update_dic['remark'] = edit_item['remark']
                            filters = [OutageEffectPeriodRecord.is_use == 1,
                                       OutageEffectPeriodRecord.outage_id == int(record_id),
                                       OutageEffectPeriodRecord.id == up_id]
                            user_session.query(OutageEffectPeriodRecord).filter(*filters).update(
                                {k: update_dic[k] for k in OutageEffectPeriodRecord.__table__.columns.keys()
                                 if k in update_dic}, synchronize_session=False)
                    elif item["type"] == 'add':  # 新增
                        add_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                         for item_time in item['data']]
                        has_intersections = self.check_intersections(effect_periods_time_list, add_time_list)
                        if has_intersections:
                            return self.customError("影响时间范围有冲突，请确认后重新录入！")
                        effect_periods_time_list.extend(add_time_list)
                        for add_item in item['data']:
                            keys = add_item.keys()
                            if "begin_time" not in keys or "end_time" not in keys:
                                return self.customError("参数不完整")
                            remark = add_item['remark'] if "remark" in keys else None
                            sers.append(OutageEffectPeriodRecord(begin_time=add_item['begin_time'],
                                                                 end_time=add_item['end_time'],
                                                                 outage_id=int(record_id),
                                                                 add_type=2, remark=remark,
                                                                 creat_time=timeUtils.getNewTimeStr())
                                        )

                    else:
                        continue
                    if len(sers):
                        user_session.add_all(sers)
                    if len(del_ids):
                        filter_del = [OutageEffectPeriodRecord.is_use == 1, OutageEffectPeriodRecord.id.in_(del_ids)]
                        user_session.query(OutageEffectPeriodRecord).filter(*filter_del).update(
                            {'is_use': 0}, synchronize_session=False)
                    user_session.commit()
                # 计算总的影响运行时间
                total_effect_seconds = 0
                time_format = '%Y-%m-%d %H:%M:%S'
                if effect_periods_time_list:
                    for item in effect_periods_time_list:
                        total_effect_seconds += (datetime.strptime(item['end_time'], time_format) - datetime.strptime(item['begin_time'], time_format)).total_seconds()
                record_info.affect_time = round(total_effect_seconds/60)
                user_session.commit()
                return self.returnTypeSuc(data='', info=None)
            elif action == 'ManageSpareUse':
                """
                批量添加/编辑/删除备件信息
                """
                data = self.get_argument('data', '[]')  # 所有数据集合
                outage_record_id = self.get_argument('outage_record_id', None)  # 停运记录id
                if DEBUG:
                    logging.info('record_id:%s,data:%s' % (outage_record_id, data))
                if not self.can_be_parsed_as_list(data):
                    return self.customError("参数类型错误")
                if not outage_record_id:
                    return self.customError("参数不完整")
                if not re.compile(r'^-?\d+$').match(outage_record_id):
                    return self.customError('参数不合规')
                data_list = json.loads(data)
                if len(data_list) == 0:
                    return self.returnTypeSuc(data='', info=None)
                record_info = user_session.query(OutageRecord).filter(
                    OutageRecord.id == int(outage_record_id), OutageRecord.is_use == 1).first()
                if not record_info:
                    return self.customError("故障记录不存在")
                station_name = self.get_station_name_by_id(record_info.project_id)
                if not station_name:
                    return self.customError("电站项目不存在")
                sers = []
                del_ids = []
                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if not len(item['data']):
                        continue
                    if item["type"] == 'add':  # 新增
                        """
                        需要先在spare_info表查询是否有，有的话直接获取id,没有则新增一条 厂家默认‘融合’
                        """
                        for add_item in item['data']:
                            keys = add_item.keys()
                            if "spare_name" not in keys or "use_num" not in keys or "unit" not in keys:
                                return self.customError("参数不完整")
                            spare_info_id = self.get_is_have_spare(add_item['spare_name'], station_name)
                            if spare_info_id:
                                spare_id = spare_info_id
                            else:
                                spare_add_id = self.add_spare_info(add_item['spare_name'], station_name, user_name,
                                                                   user_en_name, add_item['unit'])
                                if not spare_add_id:
                                    return self.customError("备件信息添加失败")
                                spare_id = spare_add_id
                            remark = add_item['remark'] if "remark" in keys else None
                            sers.append(OutageSpareUseRecord(
                                spare_id=spare_id, use_num=int(add_item['use_num']), unit=add_item['unit'],
                                outage_id=int(outage_record_id), remark=remark,creat_time=timeUtils.getNewTimeStr())
                            )
                    elif item['type'] == "delete":  # 删除
                        if not len(item['data']):
                            continue
                        for del_item in item['data']:
                            keys = del_item.keys()
                            if "id" not in keys:
                                return self.customError('参数不完整')
                            if not re.compile(r'^-?\d+$').match(del_item['id']):
                                return self.customError('参数不合规')
                            del_ids.append(int(del_item['id']))
                    elif item['type'] == "edit":  # 编辑
                        if not len(item['data']):
                            continue
                        for edit_item in item['data']:
                            update_dic = {}
                            edit_keys = edit_item.keys()
                            if "id" not in edit_keys:
                                return self.customError('参数不完整')
                            up_id = int(edit_item['id'])
                            if "spare_name" in edit_keys:
                                spare_add_id = self.get_is_have_spare(edit_item['spare_name'], station_name)
                                if spare_add_id:
                                    spare_id = spare_add_id
                                else:
                                    spare_add_id = self.add_spare_info(edit_item['spare_name'], station_name,
                                                                       user_name, user_en_name)
                                    if not spare_add_id:
                                        return self.customError("备件信息添加失败")
                                    spare_id = spare_add_id
                                update_dic['spare_id'] = spare_id
                            if "use_num" in edit_keys:
                                update_dic['use_num'] = edit_item['use_num']
                            if "unit" in edit_keys:
                                update_dic['unit'] = edit_item['unit']
                            if "remark" in edit_keys:
                                update_dic['remark'] = edit_item['remark']
                            filters = [OutageSpareUseRecord.id == int(edit_item['id']),
                                       OutageSpareUseRecord.outage_id == outage_record_id]
                            user_session.query(OutageSpareUseRecord).filter(*filters).update(
                                {k: update_dic[k] for k in OutageSpareUseRecord.__table__.columns.keys()
                                 if k in update_dic}, synchronize_session=False)
                    else:
                        continue
                if len(sers):
                    user_session.add_all(sers)
                if len(del_ids):
                    filter_del = [OutageSpareUseRecord.is_use == 1, OutageSpareUseRecord.id.in_(del_ids)]
                    user_session.query(OutageSpareUseRecord).filter(*filter_del).update(
                        {'is_use': 0}, synchronize_session=False)
                user_session.commit()

                return self.returnTypeSuc(data='', info=None)
            elif action == 'GainTotalAffectTime':
                """
                动态获取影响时长
                """
                record_id = self.get_argument('outage_record_id', None)  # 停运记录id
                data = self.get_argument('data', '[]')  # 所有数据集合
                if DEBUG:
                    logging.info('record_id:%s,data:%s' % (record_id, data))
                if not self.can_be_parsed_as_list(data):
                    return self.customError("参数类型错误")
                if not record_id:
                    return self.customError("参数错误")
                if not re.compile(r'^-?\d+$').match(record_id):
                    return self.customError('参数不合规')
                data_list = json.loads(data)

                record_info = user_session.query(OutageRecord).filter(
                    OutageRecord.id == int(record_id), OutageRecord.is_use == 1).first()
                if not record_info:
                    return self.customError("故障记录不存在")
                if len(data_list) == 0:
                    return self.returnTypeSuc({'affect_time': record_info.affect_time})
                # 查询出所有已有停运记录的影响运行时间段
                not_ids = []
                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if not len(item['data']):
                        continue
                    if item['type'] == "delete":  # 删除
                        del_ids = [int(del_id['id']) for del_id in item['data']]
                        not_ids.extend(del_ids)
                    if item['type'] == "edit":
                        edit_ids = [int(edit_id['id']) for edit_id in item['data']]
                        not_ids.extend(edit_ids)
                if len(not_ids) > 0:
                    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id),
                        OutageEffectPeriodRecord.is_use == 1, OutageEffectPeriodRecord.id.not_in(not_ids)).all()
                else:
                    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
                        OutageEffectPeriodRecord.outage_id == int(record_id), OutageEffectPeriodRecord.is_use == 1).all()
                effect_periods_time_list = []
                if effect_periods_by_outage_id:
                    # 获取时间段列表
                    effect_periods_time_list = [{'begin_time': item.begin_time.strftime("%Y-%m-%d %H:%M:%S"),
                                                 'end_time': item.end_time.strftime("%Y-%m-%d %H:%M:%S")}
                                                for item in effect_periods_by_outage_id]
                for item in data_list:
                    if "type" not in item:
                        return self.customError("参数不完整")
                    if not len(item['data']):
                        continue
                    if item['type'] == "delete":  # 删除
                        if not len(item['data']):
                            continue
                        for del_item in item['data']:
                            keys = del_item.keys()
                            if "id" not in keys:
                                return self.customError('参数不完整')
                            if not re.compile(r'^-?\d+$').match(del_item['id']):
                                return self.customError('参数不合规')
                    elif item['type'] == "edit":  # 编辑
                        if not len(item['data']):
                            continue
                        edit_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                          for item_time in item['data']]
                        has_intersections = self.check_intersections(effect_periods_time_list, edit_time_list)
                        if has_intersections:
                            return self.returnTypeSuc({'affect_time': '0', 'compare_status': False})
                            # return self.customError("影响时间范围有冲突，请确认后重新录入！")
                        effect_periods_time_list.extend(edit_time_list)
                    elif item["type"] == 'add':  # 新增
                        add_time_list = [{'begin_time': item_time['begin_time'], 'end_time': item_time['end_time']}
                                         for item_time in item['data']]
                        has_intersections = self.check_intersections(effect_periods_time_list, add_time_list)
                        if has_intersections:
                            return self.returnTypeSuc({'affect_time': '0', 'compare_status': False})
                            # return self.customError("影响时间范围有冲突，请确认后重新录入！")
                        effect_periods_time_list.extend(add_time_list)
                    else:
                        continue
                compare_status = True
                if self.has_overlapping_intervals(effect_periods_time_list):
                    compare_status = False
                    return self.returnTypeSuc({'affect_time': '0', 'compare_status': compare_status})

                # 计算总的影响运行时间
                total_effect_seconds = 0
                time_format = '%Y-%m-%d %H:%M:%S'
                if effect_periods_time_list:
                    for item in effect_periods_time_list:
                        total_effect_seconds += (datetime.strptime(item['end_time'], time_format) - datetime.strptime(item['begin_time'], time_format)).total_seconds()
                affect_time = round(total_effect_seconds/60)
                return self.returnTypeSuc({'affect_time': affect_time, 'compare_status': compare_status})
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @staticmethod
    def can_be_parsed_as_list(json_str):
        """
        判断变量是否可以转换成list
        :param json_str:
        :return:
        """
        try:
            result = json.loads(json_str)
            return isinstance(result, list)
        except json.JSONDecodeError:
            return False

    @staticmethod
    def translate_date(date_str):
        """
        将字符串转换为 datetime 对象
        :param date_str:
        :return:
        """
        formatted_date = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")

        return formatted_date

    @staticmethod
    def get_near_date(time_str, type_value):
        """
        :param time_str:
        :param type_value: 1往前 2往后
        :return:
        """
        dt = datetime.strptime(time_str[0:16], "%Y-%m-%d %H:%M")
        # 获取分钟
        minute = dt.minute
        if minute < 15:
            if type_value == 1:
                new_dt = dt.replace(minute=0, second=0, microsecond=0)
            else:
                if minute == 0:
                    new_dt = dt.replace(minute=0, second=0, microsecond=0)
                else:
                    new_dt = dt.replace(minute=14, second=59, microsecond=0)
        elif 15 <= minute < 30:
            if type_value == 1:
                new_dt = dt.replace(minute=15, second=0, microsecond=0)
            else:
                new_dt = dt.replace(minute=29, second=59, microsecond=0)
        elif 30 <= minute < 45:
            if type_value == 1:
                new_dt = dt.replace(minute=30, second=0, microsecond=0)
            else:
                new_dt = dt.replace(minute=44, second=59, microsecond=0)
        else:
            if type_value == 1:
                new_dt = dt.replace(minute=45, second=0, microsecond=0)
            else:
                new_dt = dt.replace(minute=59, second=59, microsecond=0)
        return new_dt

    @staticmethod
    def get_station_name_by_id(project_id):
        """
        通过数据库查询获取站点名称
        """
        station_info = user_session.query(Station).filter(
            Station.id == project_id).first()
        if not station_info:
            return False
        return station_info.name

    @staticmethod
    def get_outage_effect_period(begin_time, end_time, station_name):
        """
        通过数据库查询获取停运影响运行时段
        """
        conns = pools.connection()
        cursors = conns.cursor()
        try:
            use_begin_time = OutageRecordIntetface.get_near_date(begin_time, 1)
            use_end_time = OutageRecordIntetface.get_near_date(end_time, 2)
            sql = ("select window_start, window_end, ABS(sum(real_pw)) as real_pws from %s where"
                   " station_name='%s' and window_start>='%s' and window_end<='%s' GROUP BY window_start, window_end "
                   "HAVING ABS(SUM(real_pw)) > 20  ORDER BY window_start")
            sql = sql % ('dws_pcs_measure_win_15min', station_name, use_begin_time, use_end_time)
            # 执行SQL查询
            cursors.execute(sql)
            # 获取查询结果
            result = cursors.fetchall()
            res_num = len(result)
            if res_num > 0:
                s_window_start = result[0]['window_start']
                t_begin_time = OutageRecordIntetface.translate_date(begin_time[0:16]+':00')
                t_end_time = OutageRecordIntetface.translate_date(end_time[0:16]+':59')
                if s_window_start < t_begin_time:
                    result[0]['window_start'] = t_begin_time
                s_window_end = result[res_num - 1]['window_end']
                if s_window_end > t_end_time:
                    result[res_num - 1]['window_end'] = t_end_time
                data_list = OutageRecordIntetface.merge_records(result)

                return data_list
            return False
        except Exception as e:
            logging.error(e)
            raise
        finally:
            cursors.close()
            conns.close()

    @staticmethod
    def is_over_by_days(begin_time_str, end_time_str, days):
        # 解析日期字符串
        begin_time = datetime.strptime(begin_time_str, '%Y-%m-%d').date()
        end_time = datetime.strptime(end_time_str, '%Y-%m-%d').date()

        # 计算时间差
        delta = (end_time - begin_time).days
        if delta > int(days):
            return True
        return False

    @staticmethod
    def get_is_have_spare(spare_name, station_name):
        """
        查询备件信息是否已经存在
        :param spare_name:
        :param station_name:
        :return:
        """
        spare_info = user_session.query(SpareInfo).filter(
            SpareInfo.name == spare_name, SpareInfo.manufacturer == '融合', SpareInfo.station == station_name, SpareInfo.is_use == 1).first()
        if not spare_info:
            return False
        return spare_info.id

    @staticmethod
    def add_spare_info(spare_name, station_name, user_name, user_en_name, unit=None):
        """
        添加备件信息
        :param spare_name:
        :param station_name:
        :param unit:
        :return:
        """
        try:
            new_add = SpareInfo(name=spare_name, manufacturer='融合', create_descr=user_name, unit=unit,
                                station=station_name, op_ts=timeUtils.getNewTimeStr(), en_create_descr=user_en_name)
            user_session.add(new_add)
            user_session.commit()
            return new_add.id
        except Exception as e:
            logging.error(e)
            return False

    @staticmethod
    def gte_effect_capacity(data_list, project_info):
        """
        获取影响停运容量
        :param data_list:
        :param project_info:
        :return:
        """
        try:
            filters = [ProjectDevice.project_id == project_info.id, ProjectDevice.is_use == 1,
                       ProjectDevice.init_cap.isnot(None)]
            np_list = []
            # for item in data_list:
            #     print(item)
            filters.append(ProjectDevice.area.in_(data_list))
            capacity_data = user_session.query(ProjectDevice.init_cap).filter(*filters).all()
            for capacity in capacity_data:
                if capacity[0]:
                    capacity = Decimal(str(capacity[0]))
                else:
                    capacity = Decimal('0')
                np_list.append(capacity)
            # capacity_arr = np.array(np_list)
            sum_nonan = float(sum(np_list))
            return sum_nonan
        except Exception as e:
            logging.error(e)
            return 0.0

    @staticmethod
    def judge_is_date(judge_time, time_format='%Y/%m/%d %H:%M'):
        """
        判断日期格式是否正确
        :param time_format:
        :param judge_time:
        :return:
        """
        try:
            # 尝试将字符串转换为 datetime 对象
            datetime.strptime(str(judge_time).strip(), time_format)
            return True
        except ValueError as e:
            logging.error(e)
            return False

    @staticmethod
    def translate_date_format(date_time):
        """
        转换时间格式
        :param date_time:
        :return:
        """
        try:
            # 将字符串转换为 datetime 对象
            date_time = date_time.strftime("%Y-%m-%d %H:%M")
            return date_time
        except Exception as e:
            logging.error(e)
            return False

    @staticmethod
    def merge_records(records):
        """
        合并相差一秒的记录生成新的list
        """
        merged_records = []
        i = 0

        while i < len(records):
            # 当前记录
            current_record = records[i]

            # 初始化合并记录
            merged_record = {
                'window_start': current_record['window_start'],
                'window_end': current_record['window_end'],
                'real_pws': current_record['real_pws']
            }

            # 尝试合并后续记录
            j = i + 1
            while j < len(records):
                next_record = records[j]

                # 检查与下一个记录的时间间隔是否为1秒
                if (next_record['window_start'] - merged_record['window_end']) == timedelta(seconds=1):
                    # 继续合并
                    merged_record['window_end'] = next_record['window_end']
                    merged_record['real_pws'] += next_record['real_pws']
                    j += 1
                else:
                    break

            # 将合并后的记录添加到结果列表
            merged_records.append(merged_record)

            # 更新索引
            i = j

        return merged_records

    @staticmethod
    def string_to_datetime(time_str):
        """
        将时间字符串转换为 datetime 对象。

        :param time_str: 时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
        :return: datetime 对象
        """
        return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

    @staticmethod
    def has_intersection(time_period1, time_period2):
        """
        检查两个时间区间的交集是否存在。

        :param time_period1: 第一个时间区间，字典形式 {'begin_time': ..., 'end_time': ...}
        :param time_period2: 第二个时间区间，字典形式 {'begin_time': ..., 'end_time': ...}
        :return: 如果存在交集返回 True，否则返回 False
        """
        # 将时间字符串转换为 datetime 对象
        begin1 = OutageRecordIntetface.string_to_datetime(time_period1['begin_time'])
        end1 = OutageRecordIntetface.string_to_datetime(time_period1['end_time'])
        begin2 = OutageRecordIntetface.string_to_datetime(time_period2['begin_time'])
        end2 = OutageRecordIntetface.string_to_datetime(time_period2['end_time'])

        # 检查两个时间区间是否有交集
        if end1 >= begin2 and begin1 <= end2:
            return True
        return False

    @staticmethod
    def check_intersections(effect_periods_time_list, add_time_list):
        """
        检查两个列表中的时间区间是否有交集。

        :param effect_periods_time_list: 第一个列表，包含多个时间区间
        :param add_time_list: 第二个列表，包含多个时间区间
        :return: 如果存在交集返回 True，否则返回 False
        """
        for period1 in effect_periods_time_list:
            for period2 in add_time_list:
                if OutageRecordIntetface.has_intersection(period1, period2):
                    return True
        return False

    @staticmethod
    def get_record_no(occur_time, station_name, area):
        """
        获取记录编号
        :param occur_time: 发生日期 yymmdd
        :param station_name: 项目英文名
        :param area: 分区
        :return:
        """
        if area is not None:
            fault_unit = area.split('#')[0]  # 故障单元
        else:
            fault_unit = 'XXX'
        # 查询出当前故障单元已有几条记录 2024-05-07 16:16
        occur_start_time = occur_time[0:10] + ' 00:00:00'
        occur_end_time = occur_time[0:10] + ' 23:59:59'
        if fault_unit == 'XXX':
            record_count = user_session.query(func.count(OutageRecord.id)).filter(
                OutageRecord.occur_time.between(occur_start_time, occur_end_time),
                OutageRecord.is_use == 1,
                OutageRecord.area == None
            ).scalar()
        else:
            record_count = user_session.query(func.count(OutageRecord.id)).filter(
                OutageRecord.occur_time.between(occur_start_time, occur_end_time),
                OutageRecord.area.like('%' + fault_unit + '%'),
                OutageRecord.is_use == 1
            ).scalar()
        # 故障编号规则项目点号（取自 “项目信息管理”-“英文名称”）+发生日期（yymmdd）+发生故障单元（取自对应故障记录的“分区”信息“#”及之前的字符组合，如C11#、D01#，04#）+自然序号（比如同一个单元在同一天发生2次故障记录，则其自然序号分别为01、02）
        record_no = station_name + str(occur_time[0:10]).replace('-', '') + fault_unit + "#" + str(record_count + 1).zfill(2)
        return record_no

    @staticmethod
    def has_overlapping_intervals(intervals):
        """
        获取列表中的时间是否存在交集
        """
        parsed_intervals = [
            {
                'begin_time': datetime.strptime(interval['begin_time'], '%Y-%m-%d %H:%M:%S'),
                'end_time': datetime.strptime(interval['end_time'], '%Y-%m-%d %H:%M:%S')
            }
            for interval in intervals
        ]

        # 按 begin_time 排序
        parsed_intervals.sort(key=lambda x: x['begin_time'])

        # 检查是否有交集
        for i in range(1, len(parsed_intervals)):
            if parsed_intervals[i]['begin_time'] < parsed_intervals[i - 1]['end_time']:
                return True

        return False


