#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class EquipmentNumbe(user_Base):
    u'历史数据下载功能设备编号'
    __tablename__ = "t_equipment_numbe"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"设备编号")
    ty = Column(VARCHAR(100), nullable=False, comment=u"设备类型")
    station = Column(VARCHAR(50), nullable=False, comment=u"所属站")
    is_use = Column(CHAR(2), nullable=False,server_default='1',comment=u"是否使用1是0否")
    name_la = Column(VARCHAR(256), nullable=False, comment=u"设备编号点表")
    en_name = Column(VARCHAR(256), nullable=False, comment=u"设备编号")
    en_ty = Column(VARCHAR(100), nullable=False, comment=u"设备类型")
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        bean =  "{'id':%s,'name':'%s','ty':'%s','station':'%s','is_use':'%s','name_la':'%s','en_name':'%s','en_ty':'%s'}" % (
            self.id,self.name,self.ty,self.station,self.is_use,self.name_la,self.en_name,self.en_ty)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}