package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyCreateDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyQueryDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyUpdateDTO;
import com.robestec.analysis.entity.TUserStrategy;
import com.robestec.analysis.mapper.TUserStrategyMapper;
import com.robestec.analysis.service.TUserStrategyService;
import com.robestec.analysis.vo.TUserStrategyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户策略服务实现类
 */
@Slf4j
@Service
public class TUserStrategyServiceImpl extends SuperServiceImpl<TUserStrategyMapper, TUserStrategy>
        implements TUserStrategyService {

    @Override
    public PageResult<TUserStrategyVO> queryTUserStrategy(TUserStrategyQueryDTO queryDTO) {
        LambdaQueryWrapper<TUserStrategy> wrapper = new LambdaQueryWrapper<TUserStrategy>()
                .like(StringUtils.hasText(queryDTO.getName()), TUserStrategy::getName, queryDTO.getName())
                .like(StringUtils.hasText(queryDTO.getEnName()), TUserStrategy::getEnName, queryDTO.getEnName())
                .eq(queryDTO.getUserId() != null, TUserStrategy::getUserId, queryDTO.getUserId())
                .eq(queryDTO.getIsDelete() != null, TUserStrategy::getIsDelete, queryDTO.getIsDelete())
                .eq(queryDTO.getIsUse() != null, TUserStrategy::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TUserStrategy::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TUserStrategy::getCreateTime, queryDTO.getEndTime())
                .eq(TUserStrategy::getIsDelete, 0) // 只查询未删除的记录
                .orderByDesc(TUserStrategy::getCreateTime);

        Page<TUserStrategy> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TUserStrategy> result = this.page(page, wrapper);

        List<TUserStrategyVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TUserStrategyVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTUserStrategy(TUserStrategyCreateDTO createDTO) {
        TUserStrategy entity = BeanUtil.copyProperties(createDTO, TUserStrategy.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTUserStrategy(TUserStrategyUpdateDTO updateDTO) {
        TUserStrategy entity = BeanUtil.copyProperties(updateDTO, TUserStrategy.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTUserStrategy(Long id) {
        // 逻辑删除：设置is_delete=1
        this.update(Wrappers.<TUserStrategy>lambdaUpdate()
                .eq(TUserStrategy::getId, id)
                .set(TUserStrategy::getIsDelete, 1));
    }

    @Override
    public TUserStrategyVO getTUserStrategy(Long id) {
        TUserStrategy entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTUserStrategyList(List<TUserStrategyCreateDTO> createDTOList) {
        List<TUserStrategy> entityList = BeanUtil.copyToList(createDTOList, TUserStrategy.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TUserStrategyVO> getTUserStrategyByUserId(Long userId) {
        List<TUserStrategy> entityList = this.list(Wrappers.<TUserStrategy>lambdaQuery()
                .eq(TUserStrategy::getUserId, userId)
                .orderByDesc(TUserStrategy::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TUserStrategyVO> getTUserStrategyByName(String name) {
        List<TUserStrategy> entityList = this.list(Wrappers.<TUserStrategy>lambdaQuery()
                .like(TUserStrategy::getName, name)
                .orderByDesc(TUserStrategy::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByUserId(Long userId) {
        return this.count(Wrappers.<TUserStrategy>lambdaQuery()
                .eq(TUserStrategy::getUserId, userId));
    }

    /**
     * 转换为VO对象
     */
    private TUserStrategyVO convertToVO(TUserStrategy entity) {
        if (entity == null) {
            return null;
        }
        TUserStrategyVO vo = BeanUtil.copyProperties(entity, TUserStrategyVO.class);
        vo.setIsDeleteName(getIsDeleteName(entity.getIsDelete()));
        vo.setIsUseName(getIsUseName(entity.getIsUse()));
        return vo;
    }

    /**
     * 获取是否删除名称
     */
    private String getIsDeleteName(Integer isDelete) {
        if (isDelete == null) {
            return "";
        }
        return isDelete == 1 ? "已删除" : "未删除";
    }

    /**
     * 获取是否使用名称
     */
    private String getIsUseName(Integer isUse) {
        if (isUse == null) {
            return "";
        }
        return isUse == 1 ? "使用" : "不使用";
    }
}
