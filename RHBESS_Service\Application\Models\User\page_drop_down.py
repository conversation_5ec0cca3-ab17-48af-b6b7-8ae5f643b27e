#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-05 08:23:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\page.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-07-13 13:42:12

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base, user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR, \
    Boolean


class PageDrop(user_Base):
    u'页面配置表'
    __tablename__ = "t_page_drop_down"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"页面名称")
    ky = Column(Integer, nullable=False, comment=u"pag_id")
    p_id = Column(Integer, nullable=False, comment=u"父id")
    en_descr = Column(String(256), nullable=False, comment=u"英文页面名称")
    station = Column(VARCHAR(50), nullable=True, comment=u"所属站")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'descr':'%s','ky':'%s','p_id':'%s','en_descr':'%s','station':'%s'}" % (
            self.id, self.descr, self.ky, self.p_id, self.en_descr, self.station)

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}
        
