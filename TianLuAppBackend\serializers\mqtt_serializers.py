from django.core.validators import RegexValidator
from django_redis import get_redis_connection
from rest_framework import serializers
from rest_framework import exceptions
from apis.user import models

class AlarmSerializer(serializers.ModelSerializer):
    """告警定时上报序列化器"""
    class Meta:
        model = models.Alarm
        exclude = ["create_time"]


class UnAlarmSerializer(serializers.ModelSerializer):
    """告警定时上报序列化器"""
    class Meta:
        model = models.Unalarm
        exclude = ["create_time"]