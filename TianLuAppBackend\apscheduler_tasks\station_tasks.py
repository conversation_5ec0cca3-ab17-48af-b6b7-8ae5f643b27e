import json
import concurrent.futures
from django_redis import get_redis_connection
from django.db.models import Q
from apis.user import models
from common.constant import EMPTY_STR_LIST
from tools.count import unit_convert


def get_station_data(station, conn):

    rated_power_all = []
    rated_capacity_all = []
    station_dict = {}
    SOC = 0
    PCS = 0
    bms_count = 0
    discharge_status = 0  # 充放电状态
    # 查询主站下从站
    slave_data = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).all()
    # s_status = models.StationStatus.objects.filter(station=station.stationdetails_set.filter(Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0)).first()).first().status
    s_status_res = models.StationStatus.objects.filter(station=station.stationdetails_set.filter(is_delete=0,
                                                                                             english_name=station.english_name).first()).first()
    if s_status_res:
        s_status = s_status_res.status
    else:
        s_status = 1

    for slave in slave_data:
        # 计算总功率和容量
        rated_power_all.append(float(slave.rated_power))
        rated_capacity_all.append(float(slave.rated_capacity))
        status_res = models.StationStatus.objects.filter(station=slave).first()


        status = status_res.status if status_res else 1
        s_status = status if s_status < status and s_status != 4 else s_status
        units = models.Unit.objects.filter(is_delete=0, station__id=slave.id).all()  # 查询BMS, PCS 数据
        for unit in units:
            bms = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.bms))
            if not bms:
                bms = {}
            else:
                bms_count += 1
                bms = eval(eval(bms))
            pcs = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.pcs))
            if not pcs:
                pcs = {}
                discharge_status = -1  # -1：离线
            else:
                pcs = eval(eval(pcs))
            if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST and PCS != '--':
                PCS += float(pcs.get('P'))
            else:
                PCS = '--'
            if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST and SOC != '--':
                SOC += float(bms.get('SOC'))
            else:
                SOC = '--'
    # 查询地图打点净经纬度
    dodging = models.Project.objects.values('longitude', 'latitude').filter(is_used=1,
                                                                            id=station.project_id).first()
    if dodging:
        station_dict['latitude'] = dodging['latitude']  # 纬度
        station_dict['longitude'] = dodging['longitude']  # 经度
    station_dict['station_name'] = station.name
    if bms_count > 0 and SOC != '--':
        station_dict['SOC'] = round(SOC / bms_count, 2) if SOC / bms_count <= 100 else '--'
    else:
        station_dict['SOC'] = '--'
    station_dict['power'] = round(PCS, 2) if PCS != '--' else PCS
    # 不离线的情况下判断充放电状态 1：充电；0：静置；2：放电
    # 计算多个单元的边界值
    for unit in models.Unit.objects.filter(is_delete=0,
                                           station__in=station.stationdetails_set.filter(is_delete=0).all()):
        # 判断单元充放电状态 -1：离线；1：充电；0：静置；2：放电
        pcs = conn.get(
            'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.station.english_name,
                                                    unit.pcs))
        if not pcs:
            discharge_status = -1
            break
        else:
            pcs = eval(eval(pcs))
            p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
            if p != '--':
                if p > 1:
                    discharge_status = 2
                elif p < -1:
                    discharge_status = 1
                else:
                    discharge_status = 0 if discharge_status == 0 else discharge_status
            else:
                discharge_status = -1
                break

    rated_power = (unit_convert(sum(rated_power_all), 'kW'))
    rated_capacity = (unit_convert(sum(rated_capacity_all), 'kWh'))
    station_dict['discharge_status'] = discharge_status
    station_dict['english_name'] = station.english_name
    station_dict['id'] = station.id
    station_dict['rated_power'] = [float(rated_power[0]), rated_power[1]]
    station_dict['rated_capacity'] = [float(rated_capacity[0]), rated_capacity[1]]
    station_dict['address'] = station.stationdetails_set.first().address if station.stationdetails_set.first() else '--'
    station_dict['station_status'] = s_status
    station_dict['efficiency'] = station.efficiency

    return station_dict

def map_task():
    """缓存地图数据，5分钟"""
    conn = get_redis_connection('3')  # 连接redis
    station_data = models.MaterStation.objects.filter(is_delete=0).all()
    data = {}
    with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        futures = list()
        for station in station_data:
            futures.append(executor.submit(get_station_data, station, conn))
        for future in concurrent.futures.as_completed(futures):
            station_dict = future.result()
            data[station_dict['id']] = station_dict
    conn.set('app_map_station_data', json.dumps(data), ex=60 * 5)

    return 1