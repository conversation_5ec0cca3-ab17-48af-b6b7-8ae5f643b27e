from apis.user.models import Project


def select_project_subitems_by_name(project_name):
    # 查询 project 下所有的project, station, unit
    project_instance = Project.objects.filter(name=project_name, is_used=1).first()
    master_stations = project_instance.materstation_set.filter(is_delete=0)
    # stations = project_instance.stationdetails_set.all()

    total_units = dict()
    total_stations = dict()
    total_master_stations = dict()

    for master_station in master_stations:
        total_master_stations.update({
            master_station.id: master_station.name.replace(' ', '')
        })

        stations = master_station.stationdetails_set.filter(is_delete=0).all()
        for station in stations:
            total_stations.update({
                station.id: station.station_name.replace(' ', '')
            })

            unit_instances = station.unit_set.filter(is_delete=0).all()
            for unit_instance in unit_instances:
                total_units.update({unit_instance.id: master_station.name.replace(' ', '') + "-" +
                                                      unit_instance.unit_new_name.replace(
                                                          ' ', '')})
    return total_master_stations, total_stations, total_units


def accumulate_dic(return_dics):
    result = {}
    for dic in return_dics:
        for key, value in dic.items():
            if key in result:
                result[key]["total_charge"] += round(value["total_charge"], 2)
                result[key]["total_discharge"] += round(value["total_discharge"], 2)
                result[key]["time"] = value["time"]
            else:
                result[key] = {"total_charge": round(value["total_charge"], 2), "total_discharge": round(value["total_discharge"], 2),
                               "time": value["time"]}
    # 处理小于1的数据置零
    for k, v in result.items():
        if v.get('total_charge') < 1:
            v['total_charge'] = 0
        if v.get('total_discharge') < 1:
            v['total_discharge'] = 0

    return result


def abnormal_handle(data, detail):
    """
    data: serializers error信息
    detail: 异常信息列表
    """
    for k, v in data.items():
        if isinstance(v[0], dict):
            return abnormal_handle(v[0], detail)
        else:
            print(v[0])
            if '-' in v[0]:
                detail.append(v[0].split('-')[1])
            elif 'non_field_errors' == k:
                detail.append(str(v[0]))
            elif 'null' in v[0]:
                detail.append(k + '-' + v[0].replace('null', '空'))
            else:
                detail.append(f"{k}-{v[0]}")
    return detail

