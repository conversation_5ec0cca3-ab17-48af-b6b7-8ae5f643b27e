#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-09-21 18:32:32
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_useele_load_offer.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 19:32:57


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseUseEleEleLoadOffer(user_Base):
    u'用电信息表--客户提供负荷&勘探负荷'
    __tablename__ = "t_side_forecase_useele_load_offer"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    file_name = Column(String(256), nullable=True, comment=u"文件名称")
    file_path = Column(String(256), nullable=False, comment=u"文件路径加自定义命名")
    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    sta_ti = Column(String(256), nullable=False, comment=u"起始日期")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    type = Column(CHAR(1), nullable=False,server_default='1',comment=u"1用户提供2勘探负荷")
    capacity_config = Column(Text, nullable=True, comment=u"优化定容配置容量信息")
    project_ele_load_offer = relationship("ForecaseProject", backref="project_ele_load_offer")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean =  "{'id':%s,'file_name':'%s','file_path':'%s','project_id':'%s','op_ts':'%s','sta_ti':'%s','is_use':'%s','type':'%s','capacity_config':'%s'}" %(self.id,self.file_name,self.file_path
             ,self.project_id,self.op_ts,self.sta_ti,self.is_use,self.type,self.capacity_config)
        return bean.replace('None',"")

   