import redis
from Application.Cfg.decision_cfg import model_config
_pool = redis.ConnectionPool(host=model_config.get('redis', "HOSTNAME"),
                            port=model_config.get('redis', "PORT"),
                            db=model_config.get('redis', "DB"),
                            username=model_config.get('redis', "USERNAME"),
                            password=model_config.get('redis', "PASSWORD"),
                            decode_responses=False, encoding='UTF-8')
r = redis.Redis(connection_pool=_pool)

real_pool = redis.ConnectionPool(host=model_config.get('realredis', "HOSTNAME"),
                            port=model_config.get('realredis', "PORT"),
                            db=model_config.get('realredis', "DB"),
                            username=model_config.get('realredis', "USERNAME"),
                            password=model_config.get('realredis', "PASSWORD"),
                            decode_responses=False, encoding='UTF-8')
r_real = redis.Redis(connection_pool=real_pool)