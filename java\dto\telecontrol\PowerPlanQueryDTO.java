package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 功率计划查询DTO
 */
@Data
@ApiModel("功率计划查询DTO")
public class PowerPlanQueryDTO {

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("状态: 1已保存；2已下发；3执行中，4已完成，5下发失败 6已停止")
    private Integer status;

    @ApiModelProperty("计划类型: 1-自定义, 2-周期性, 3-节假日")
    private Integer planType;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("语言")
    private String lang;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
