package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 电站容量刷新DTO
 */
@Data
@ApiModel("电站容量刷新DTO")
public class StationRefreshDTO {

    @ApiModelProperty(value = "电站ID列表", required = true)
    @NotNull(message = "电站ID不能为空")
    private List<Long> stationIds;
}
