#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\side_forecase_ele.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-17 08:54:23


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseEle(user_Base):
    u'用户侧预算用电分类'
    __tablename__ = "t_side_forecase_ele"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"用电分类")
    en_name = Column(VARCHAR(256), nullable=True, comment=u"用电分类")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    provinces = Column(VARCHAR(256), nullable=True, comment=u"电压等级所属省份")
    index_ = Column(VARCHAR(5), nullable=True, comment=u"索引，英文")
   
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
    def __repr__(self):
        
        return "{'id':%s,'name':'%s','en_name':'%s','is_use':%s}" % (
            self.id,self.name,self.en_name,self.is_use)
        
    