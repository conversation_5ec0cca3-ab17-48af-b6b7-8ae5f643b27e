#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-19 14:28:20
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\chars.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-05-23 15:24:53

from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.user import User
from Application.Models.User.event_alarm_type import EventAlarmType

class Charts(user_Base):
    u'曲线基础配置表'
    __tablename__ = "t_charts"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"名称")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    index = Column(Integer, nullable=False,comment=u"索引自动生成")
    type_name = Column(VARCHAR(20), nullable=False,comment=u"数据类型；status,measure,cumulant,device,discrete")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    user_id = Column(Integer, ForeignKey("t_user.id"),nullable=True,comment=u"用户id，记录是谁配置的")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    
   
    charts_user = relationship("User", backref="charts_user")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        user_descr = self.charts_user.name if self.charts_user else ''
        return "{'id':%s,'descr':'%s','name':'%s','index':'%s','type_name':'%s','user_id':'%s','user_descr':'%s','op_ts':'%s'}" % (
            self.id,self.descr,self.name,self.index,self.type_name,self.user_id,user_descr,self.op_ts)
        
    def deleteCharts(self,id):
        try:
         
            user_session.query(Charts).filter(Charts.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False