import datetime
import concurrent.futures
import logging

import pymysql
import time

from dbutils.persistent_db import PersistentDB

from TianLuAppBackend import settings
from apis.user import models
from common.database_pools import dwd_tables, dwd_db_tool, ads_db_tool
from settings.meter_settings import METER_DIC_IDC
from decimal import Decimal


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_origin_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_origin_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_origin_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_origin_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_origin_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


def time_range_by_cumulant_charge(station_name, point_type, device_type, device, start_time, end_time, charge, discharge):
    select_sql = f'select {charge}, {discharge}, time, station_name from {dwd_tables[point_type][device_type]} where station_name=%s and device=%s and time between %s and %s order by time ASC'

    results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
    body_result = []
    if results:
        # print(34, charge, discharge, results)
        for row in results:
            # print(36, charge, discharge, row)
            if row[charge] or row[discharge]:
                body_result.append({"time": row['time'].strftime("%Y-%m-%d %H:%M:%S"), "charge": round(float(row[charge]), 2),
                                    "discharge": round(float(row[discharge]), 2)})

    return body_result


def select_latest_data_from_dwd_rhyc(station_name, point_type, device_type, device, *args):
    table_name = dwd_tables[point_type][device_type]

    # 执行SQL查询
    select_sql = f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and type = 1 ORDER BY time DESC limit 1"
    try:
        result = dwd_db_tool.select_one(select_sql, *(station_name, device))
        if result:
            time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
            temp_dict = {"time": time_}
            for i in args:
                temp_dict[i] = result.get(i.lower(), None)
        else:
            return None
    except Exception as e:
        error_log.error(e)
        return None


def select_range_data_from_dwd_rhyc(station_name, point_type, device_type, device, start_time, end_time, *args):
    table_name = dwd_tables[point_type][device_type]

    select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time ASC")
    try:
        temp_list = list()
        results = dwd_db_tool.select_many(select_sql, *(station_name, device, start_time, end_time))
        if results:
            for result in results:
                time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
                temp_dict = {"time": time_}
                for i in args:
                    temp_dict[i] = result.get(i.lower(), None)
                    temp_list.append(temp_dict)
        return temp_list
    except Exception as e:
        error_log.error(e)
        return None


def select_target_day_latest_data_from_dwd_rhyc(station_name, point_type, device_type, device, target_day, *args):
    start_time = datetime.datetime.combine(target_day, datetime.datetime.min.time())
    end_time = datetime.datetime.combine(target_day+datetime.timedelta(days=1), datetime.datetime.max.time()).replace(hour=0, minute=0, second=30,
                                                                                           microsecond=0)
    table_name = dwd_tables[point_type][device_type]
    # table_name_ = "dws_bms_charge_discharge_data"

    select_sql = (f"SELECT * FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
                  f" time DESC limit 1")
    try:
        result = dwd_db_tool.select_one(select_sql, *(station_name, device, start_time, end_time))
        if result:
            # print(93, result)
            time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
            temp_dict = {"time": time_}
            for i in args:
                temp_dict[i] = result.get(i.lower(), None)
            return temp_dict

            # if station_name == 'NBLS001':  # 单独处理
            #     temp_dict['PAE'] = result.get('CuCha'.lower(), None)
            #     temp_dict['NAE'] = result.get('CuDis'.lower(), None)
            # else:
            #     temp_dict['PAE'] = result.get('PAE'.lower(), None)
            #     temp_dict['NAE'] = result.get('NAE'.lower(), None)
            # return temp_dict
        else:
            return None
    except Exception as e:
        error_log.error(e)
        return None


def select_target_day_latest_data_from_ads_rhyc(station_name, device, target_day, *args):
    start_time = datetime.datetime.combine(target_day, datetime.datetime.min.time())
    end_time = datetime.datetime.combine(target_day+datetime.timedelta(days=1), datetime.datetime.max.time()).replace(hour=0, minute=0, second=30,
                                                                                           microsecond=0)
    table_name = "ads_cumulant_ems_product_5min"

    select_sql = (f"SELECT * FROM {table_name} WHERE station=%s and device=%s and time between %s and %s ORDER BY"
                  f" time DESC limit 1")
    try:
        result = ads_db_tool.select_one(select_sql, *(station_name, device, start_time, end_time))
        if result:
            time_ = result['time'].strftime("%Y-%m-%d %H:%M:%S")
            temp_dict = {"time": time_}
            for i in args:
                temp_dict[i] = result.get(i.lower(), None)
            return temp_dict

        else:
            return None
    except Exception as e:
        error_log.error(e)
        return None



class NewDayHourUsed:
    """日冲放电量"""""

    def __init__(self, user_ins):
        """

        :param user_ins: 用户对象
        """
        self.user_ins = user_ins

    @property
    def _stations(self):
        """
        用户所持有的站
        :return:
        exited-> 是否存在 True or False
        stations_ins -> 站 queryset 不存在为 []
        """
        stations_ins = self.user_ins.stations.all()
        exited = stations_ins.exists()
        return exited, stations_ins

    @property
    def _projects(self):
        """
        用户所持有的项目集
        :return:
        """
        projects_ins = models.Project.objects.filter(user=self.user_ins, is_used=1).all()
        stations_ins = (models.StationDetails.objects.filter(is_delete=0, master_station__project__in=projects_ins)
                        .exclude(slave=0).all())
        exited = stations_ins.exists()
        return exited, stations_ins

    def _unit_info(self, station_ins, func):
        """
        单元信息
        :param station_ins: 站实例
        :param func: 计算函数名
        :return:
        """
        now_time = int(time.time())
        now_time = datetime.datetime.fromtimestamp(now_time).strftime("%Y-%m-%d %H:%M:%S")
        start_time = now_time.split(' ')[0] + ' 00:00:00'
        end_time = start_time.replace('00:00:00', '23:59:59')
        start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

        station_english_name = station_ins.english_name
        # station_app = station_ins.app
        meter_type = station_ins.meter_type
        meter_dic = METER_DIC_IDC.get(meter_type)
        charge_ = meter_dic.get("charge")
        discharge_ = meter_dic.get("discharge")
        device = meter_dic.get("device")

        units_ins = models.Unit.objects.filter(is_delete=0, station=station_ins).all()
        charge_list = []
        discharge_list = []
        for unit in units_ins:
            bms_ = getattr(unit, device)
            result = time_range_by_cumulant_charge(station_english_name, 'cumulant', device, bms_, start_datatime,
                                               end_datatime, charge_, discharge_)
            if result:
                # error_log.error(f'{result}')
                charge, discharge = getattr(self, func)(result)
                charge_list.append(charge)
                discharge_list.append(discharge)

        return round(sum(charge_list), 2), round(sum(discharge_list), 2)

    @staticmethod
    def day_charge_discharge(datas):
        """
        计算日冲放电量
        :return:
        """
        new_list_ = sorted(datas, key=lambda x: x["time"])
        first_charge = new_list_[0]['charge']
        first_discharge = new_list_[0]['discharge']

        last_charge = new_list_[-1]['charge']
        last_discharge = new_list_[-1]['discharge']
        charge = abs(Decimal(last_charge) - Decimal(first_charge))
        discharge = abs(Decimal(last_discharge) - Decimal(first_discharge))
        return charge, discharge

    @property
    def hours_used(self):
        exist, stations_query = self._projects
        if exist:

            power = self.relate_power(stations_query)
            charge_list = []
            discharge_list = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                futures = list()
                for station in stations_query:
                    future = executor.submit(self._unit_info, station, "day_charge_discharge")
                    futures.append(future)
                for f in concurrent.futures.as_completed(futures):
                    charge, discharge = f.result()
                    charge_list.append(abs(charge))
                    discharge_list.append(abs(discharge))

            utilization_hours = (sum(charge_list) + sum(discharge_list)) / power
            return Decimal(utilization_hours).quantize(Decimal("0.000"))
        else:
            return 0

    @property
    def history_hours_used(self):
        """
        历史日均利用小时数
        :return: 历史日均利用小时数
        """
        exist, stations_query = self._projects
        projects_ins = models.Project.objects.filter(user=self.user_ins, is_used=1).all()
        days_list = []

        if exist:
            for project in projects_ins:
                days = self.projecgt_days(project)
                days_list.append(days)
            power = self.relate_power(stations_query)

            charge_list = []
            discharge_list = []

            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                futures = list()
                for station in stations_query:
                    future = executor.submit(self._unit_info, station, "history_charge_discharge")
                    futures.append(future)
                for f in concurrent.futures.as_completed(futures):
                    charge, discharge = f.result()
                    charge_list.append(abs(charge))
                    discharge_list.append(abs(discharge))

            utilization_hours = (round(sum(charge_list), 2) + round(sum(discharge_list), 2)) / power / (sum(days_list) + 1)
            return Decimal(utilization_hours).quantize(Decimal("0.0000"))
        else:
            return 0

    def station_days(self, station_ins):
        c_time = station_ins.create_time
        now = datetime.datetime.today()
        return (now - c_time).days

    def projecgt_days(self, project_ins):
        c_time = project_ins.in_time
        now = datetime.datetime.today()
        return (now - c_time).days

    def history_charge_discharge(self, datas):
        """
        历史总充放电
        :param datas:
        :param charge:
        :param discharge:
        :return:
        """
        new_list_ = sorted(datas, key=lambda x: x["time"])
        last_charge = new_list_[-1]['charge']
        last_discharge = new_list_[-1]['discharge']
        charge = round(abs(Decimal(last_charge)), 2)
        discharge = round(abs(Decimal(last_discharge)), 2)
        return charge, discharge

    def relate_power(self, station_queryset):
        """
        额定功率之和
        :param station_queryset: 站查询集
        :return: 额定功率总和
        """
        power_list = []
        for station in station_queryset:
            power_list.append(Decimal(station.rated_power))
        return sum(power_list)


# class DayHourUsed:
#     """日冲放电量"""
#
#     def __init__(self, user_ins):
#         """
#
#         :param user_ins: 用户对象
#         """
#         self.user_ins = user_ins
#         self.http_url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#         self.now = int(time.time())
#
#     @property
#     def _stations(self):
#         """
#         用户所持有的站
#         :return:
#         exited-> 是否存在 True or False
#         stations_ins -> 站 queryset 不存在为 []
#         """
#         stations_ins = self.user_ins.stations.all()
#         exited = stations_ins.exists()
#         return exited, stations_ins
#
#     @property
#     def _projects(self):
#         """
#         用户所持有的项目集
#         :return:
#         """
#         projects_ins = models.Project.objects.filter(user=self.user_ins).all()
#         stations_ins = models.StationDetails.objects.filter(project__in=projects_ins).all()
#         exited = stations_ins.exists()
#         return exited, stations_ins
#
#     def reqeust_json(self, station_ins, units_ins):
#         """
#         http 请求 json 构造
#         :return: json
#         """
#         station_english_name = station_ins.english_name
#         station_app = station_ins.app
#         meter_type = station_ins.meter_type
#         meter_dic = METER_DIC.get(meter_type)
#         charge = meter_dic.get("charge")
#         discharge = meter_dic.get("discharge")
#         device = meter_dic.get("device")
#         bms_ = getattr(units_ins, device)
#         request_json = {
#             "time": str(self.now),
#             "datatype": "cumulant",
#             "app": station_app,
#             "station": station_english_name,
#             "body": [{"device": bms_, "body": [charge, discharge]}],  # 充电量  # 放电量
#         }
#         return request_json, charge, discharge
#
#     def _unit_info(self, station_ins, func):
#         """
#         单元信息
#         :param station_ins: 站实例
#         :param func: 计算函数名
#         :return:
#         """
#         units_ins = models.Unit.objects.filter(is_delete=0, station=station_ins).all()
#         charge_list = []
#         discharge_list = []
#         for unit in units_ins:
#             request_json, charge_, discharge_ = self.reqeust_json(station_ins, unit)
#             response = requests.post(url=self.http_url, json=request_json)
#             datas = response.json()["datas"]
#             if datas:
#                 charge, discharge = getattr(self, func)(datas, charge_, discharge_)
#                 charge_list.append(charge)
#                 discharge_list.append(discharge)
#         return sum(charge_list), sum(discharge_list)
#
#     def day_charge_discharge(self, datas, charge, discharge):
#         """
#         计算日冲放电量
#         :return:
#         """
#         new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#         first_charge,first_discharge,last_charge,last_discharge = 0,0,0,0
#         if charge in new_list_[0]["body"]["data"][0]:
#             first_charge = new_list_[0]["body"]["data"][0][charge]
#             first_discharge = new_list_[0]["body"]["data"][0][discharge]
#         if charge in new_list_[-1]["body"]["data"][0]:
#             last_charge = new_list_[-1]["body"]["data"][0][charge]
#             last_discharge = new_list_[-1]["body"]["data"][0][discharge]
#         charge = abs(Decimal(last_charge) - Decimal(first_charge))
#         discharge = abs(Decimal(last_discharge) - Decimal(first_discharge))
#         return charge, discharge
#
#     @property
#     def hours_used(self):
#         exist, stations_query = self._projects
#         if exist:
#             power = self.relate_power(stations_query)
#             # print("power------------------------>", power)
#             charge_list = []
#             discharge_list = []
#             with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#                 futures = list()
#                 for station in stations_query:
#                     # charge, discharge = self._unit_info(station, "day_charge_discharge")
#                     future = executor.submit(self._unit_info, station, "day_charge_discharge")
#                     futures.append(future)
#                 for f in concurrent.futures.as_completed(futures):
#                     charge, discharge = f.result()
#                     charge_list.append(abs(charge))
#                     discharge_list.append(abs(discharge))
#             # print(charge_list, discharge_list)
#             # print(sum(charge_list) + sum(discharge_list), "sum------------>")
#             utilization_hours = (sum(charge_list) + sum(discharge_list)) / power
#             return Decimal(utilization_hours).quantize(Decimal("0.000"))
#         else:
#             return 0
#
#     @property
#     def history_hours_used(self):
#         """
#         历史日均利用小时数
#         :return: 历史日均利用小时数
#         """
#         exist, stations_query = self._projects
#         projects_ins = models.Project.objects.filter(user=self.user_ins).all()
#         days_list = []
#
#         if exist:
#             for project in projects_ins:
#                 days = self.projecgt_days(project)
#                 days_list.append(days)
#             power = self.relate_power(stations_query)
#
#             charge_list = []
#             discharge_list = []
#
#             with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#                 futures = list()
#                 for station in stations_query:
#                     # charge, discharge = self._unit_info(station, "day_charge_discharge")
#                     future = executor.submit(self._unit_info, station, "history_charge_discharge")
#                     futures.append(future)
#                 for f in concurrent.futures.as_completed(futures):
#                     charge, discharge = f.result()
#                     charge_list.append(abs(charge))
#                     discharge_list.append(abs(discharge))
#
#             # for station in stations_query:
#             #     charge, discharge = self._unit_info(station, "history_charge_discharge")
#             #     charge_list.append(charge)
#             #     discharge_list.append(discharge)
#
#             # print(sum(charge_list) + sum(discharge_list), "历史日均利用小时数历史--------->")
#             # print(power, "历史日均利用小时数历史 power--------->")
#             # print(sum(days_list), "历史日均利用小时数days-------->")
#             utilization_hours = ((sum(charge_list) + sum(discharge_list)) / power) / (sum(days_list) + 1)
#             return Decimal(utilization_hours).quantize(Decimal("0.0000"))
#         else:
#             return 0
#
#     def station_days(self, station_ins):
#         c_time = station_ins.create_time
#         now = datetime.datetime.today()
#         return (now - c_time).days
#
#     def projecgt_days(self, project_ins):
#         c_time = project_ins.in_time
#         now = datetime.datetime.today()
#         return (now - c_time).days
#
#     def history_charge_discharge(self, datas, charge, discharge):
#         """
#         历史总充放电
#         :param datas:
#         :param charge:
#         :param discharge:
#         :return:
#         """
#         new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#         last_charge,last_discharge= 0,0
#         if charge in new_list_[-1]["body"]["data"][0]:
#             last_charge = new_list_[-1]["body"]["data"][0][charge]
#             last_discharge = new_list_[-1]["body"]["data"][0][discharge]
#         charge = abs(Decimal(last_charge))
#         discharge = abs(Decimal(last_discharge))
#         return charge, discharge
#
#     def relate_power(self, station_queryset):
#         """
#         额定功率之和
#         :param station_queryset: 站查询集
#         :return: 额定功率总和
#         """
#         power_list = []
#         for station in station_queryset:
#             power_list.append(Decimal(station.rated_power))
#         return sum(power_list)
