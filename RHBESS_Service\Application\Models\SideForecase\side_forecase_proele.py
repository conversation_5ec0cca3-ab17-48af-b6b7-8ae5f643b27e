#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-19 11:14:26
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\side_forecase_proele.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-19 11:18:39

from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseProele(user_Base):
    u'省份用户类型电压等级关联表'
    __tablename__ = "t_side_forecase_proele"
    
    province_id = Column(Integer, nullable=False, primary_key=True,comment=u"省份id")
    ele_id = Column(Integer, nullable=False, primary_key=True,comment=u"用电类型id")
    vols = Column(VARCHAR(256), nullable=False, comment=u"电压等级主键，逗号隔开")
    beilv = Column(CHAR(1), nullable=False,server_default='1',comment=u"倍率")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
    def __repr__(self):
        
        return "{'province_id':%s,'ele_id':'%s','vols':%s}" % (self.province_id,self.ele_id,self.vols)
        
    