import datetime
import pymysql
from dbutils.persistent_db import PersistentDB

from TianLuAppBackend import settings

METER_DIC_IDC = {
    1: {"charge": "cucha", "discharge": "cudis", "device": "bms"},
    2: {"charge": "nae", "discharge": "pae", "device": "bms"},
    3: {"charge": "bclcap", "discharge": "bdlcap", "device": "pcs"},
}


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

conn = pool.connection()
cursor = conn.cursor()

db_session = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['default']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['default']['USER'],  # 数据库用户名
            "password": settings.DATABASES['default']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['default']['NAME'],  # 数据库名称
            "port": settings.DATABASES['default']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

conn1 = db_session.connection()
db_session = conn1.cursor()

def timing_chgdig_s_day():
    """冻结逐天充放电"""
    day = datetime.date.today() - datetime.timedelta(days=1)
    start_time = str(datetime.date.today() - datetime.timedelta(days=2)) + ' 23:59:30'
    end_time = str(datetime.date.today() - datetime.timedelta(days=1)) + ' 23:59:59'
    now_time = datetime.datetime.now()
    sql = "SELECT * FROM t_stations WHERE id not in (9, 10)"
    try:
        db_session.execute(sql)
    except Exception as e:
        print('异常：{}'.format(e))
    stations_ins = db_session.fetchall() # 查询总的站
    report_day_list = [] # 逐日充放数据
    for station in stations_ins:
        if station.create_time < now_time:
            station_chag = 0
            station_disg = 0
            sql = "SELECT bms, pcs FROM t_unit WHERE station_id = {}".format(station.id)
            try:
                db_session.execute(sql)
            except Exception as e:
                print('异常：{}'.format(e))
            unit = db_session.fetchall()   # 取单元的bms pcs
            if unit:
                meter_type = station.meter_type
                chag_n = METER_DIC_IDC[meter_type]['charge']
                disg_n = METER_DIC_IDC[meter_type]['discharge']
                for info in unit:
                    report_day = models.FReportDay()
                    if meter_type == 1 or meter_type == 2:
                        table_name = 'dwd_cumulant_bms_data_storage'
                        chag, disg = get_history_data_f_day(chag_n, disg_n, table_name, info['bms'],
                                                            station.english_name, start_time, end_time, day)

                        sql = "SELECT * FROM f_report " \
                              "WHERE station = {}" \
                              "and  unit_name= = {}" \
                              "and  day = {}" \
                              "and station_type = {}".format(station.english_name, info['bms'], str(day), '2')
                        try:
                            db_session.execute(sql)
                        except Exception as e:
                            print('异常：{}'.format(e))
                        soc_data = db_session.fetchall()

                        report_day.unit_name = info['bms']

                    elif station.meter_type == 3:
                        table_name = 'dwd_cumulant_pcs_data_storage'
                        chag, disg = get_history_data_f_day(chag_n, disg_n, table_name, info['pcs'],
                                                            station.english_name, start_time, end_time, day)


                        sql = "SELECT * FROM f_report " \
                              "WHERE station = {}" \
                              "and  unit_name= = {}" \
                              "and  day = {}" \
                              "and station_type = {}".format(station.english_name, info['pcs'], str(day), '2')
                        try:
                            db_session.execute(sql)
                        except Exception as e:
                            print('异常：{}'.format(e))
                        soc_data = db_session.fetchall()

                        report_day.unit_name = info['pcs']
                    report_day.day = str(day)
                    report_day.station = station.english_name
                    report_day.chag = chag
                    report_day.disg = disg
                    report_day.station_type = 2
                    chag_soc = 0
                    disg_soc = 0
                    for soc in soc_data:
                        v = float(soc.soc)
                        if v < 0:
                            disg_soc += v
                        else:
                            chag_soc += v
                    report_day.chag_soc = round(chag_soc, 2)
                    report_day.disg_soc = round(abs(disg_soc), 2)
                    report_day_list.append(report_day)
                    station_chag += chag
                    station_disg += disg

                sql = "SELECT * FROM f_report " \
                      "WHERE station = {}" \
                      "and  day = {}" \
                      "and station_type = {}".format(station.english_name,  str(day), '1')
                try:
                    db_session.execute(sql)
                except Exception as e:
                    print('异常：{}'.format(e))
                soc_info = db_session.fetchall()

                if soc_info:
                    soc_v_chag = 0
                    soc_v_disg = 0
                    for info in soc_info:
                        soc_v = float(info.soc)
                        if soc_v > 0:
                            soc_v_chag += soc_v
                        else:
                            soc_v_disg += soc_v
                    station_report_day = models.FReportDay()
                    station_report_day.day = str(day)
                    station_report_day.station = station.english_name
                    station_report_day.chag = station_chag
                    station_report_day.disg = station_disg
                    station_report_day.station_type = 1
                    station_report_day.chag_soc = round(soc_v_chag, 2)
                    station_report_day.disg_soc = round(abs(soc_v_disg), 2)
                    station_report_day.unit_name = 0
                    report_day_list.append(station_report_day)
    models.FReportDay.objects.bulk_create(report_day_list)

    return 1



def get_history_data_f_day(chag_n, disg_n, table_name, device, station_name, start_time, end_time, day):
    """
    查询天历史数据充放电量
    :param chag_n: 充电
    :param disg_n: 放电
    :param table_name: 表明
    :param device: bms/pcs
    :param station_name: 站名
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param day:
    :return:
    """

    # try:
        # 执行SQL查询
    sql = """SELECT {},{}
                FROM {} 
                WHERE 1=1
                and (type=1 or type is NULL)
                and {} is not NULL 
                and device='{}'
                and station_name='{}'
                and time BETWEEN '{}' AND '{}'
                ORDER BY time ASC
                """.format(
        chag_n, disg_n, table_name, chag_n, device, station_name, start_time, end_time
    )
    try:
        cursor.execute(sql)
    except Exception as e:
        print('异常：{}'.format(e))

    # 获取查询结果
    result = cursor.fetchall()
    # 处理查询结果
    chag, disg = 0.0, 0.0
    count = len(result)
    if count >= 1:
        # 执行SQL查询
        sql = """SELECT {},{}
                    FROM {} 
                    WHERE 1=1
                    and (type=1 or type is NULL)
                    and {} is not NULL 
                    and device='{}'
                    and station_name='{}'
                    and time BETWEEN '{}' AND '{}'
                    ORDER BY time DESC LIMIT 1
                    """.format(
            chag_n, disg_n, table_name, chag_n, device, station_name, str(day-datetime.timedelta(days=2)) + ' 23:59:59',
            start_time
        )

        try:
            cursor.execute(sql)
        except Exception as e:
            print('异常：{}'.format(e))

        result1 = cursor.fetchone()
        if result1:
            if count != 1:
                chag = abs(result[count - 1][chag_n] - result1[chag_n])
                disg = abs(result[count - 1][disg_n] - result1[disg_n])
            elif count == 1:
                chag = abs(result[0][chag_n] - result1[chag_n])
                disg = abs(result[0][disg_n] - result1[disg_n])
        else:
            if count == 1:
                chag = 0.0
                disg = 0.0
            else:
                chag = abs(result[count - 1][chag_n] - result[0][chag_n])
                disg = abs(result[count - 1][disg_n] - result[0][disg_n])


    return round(chag,2), round(disg,2)
    # except Exception as e:
    #     error_log.error(e)
    # finally:
    #     cursor.close()