#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-04 08:59:49
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\dispatch_step_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-21 10:37:06


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.dispatch_r import DispatchR
from Application.Models.User.user import User

class DispatchStepR(user_Base):
    u'工单步骤表'
    __tablename__ = "r_dispatch_step"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    dispatch_id = Column(Integer,ForeignKey("r_dispatch.id"), nullable=False,comment=u"模板id")
    status = Column(String(256), nullable=False, comment=u"工单状态1活动中2结束")
    handle_user = Column(Integer, nullable=True,comment=u"处理人")
    send_time = Column(DateTime, nullable=True,comment=u"发送时间")
    read_time = Column(DateTime, nullable=True,comment=u"阅读时间")
    handle_time = Column(DateTime, nullable=True,comment=u"处理时间")
    dispatch_step_id = Column(Integer, nullable=True,comment=u"对应具体步骤id，（第一步和最后一步不可更改）")
    content = Column(String(256), nullable=True, comment=u"工作内容")
    ticket_files = Column(String(256), nullable=True, comment=u"工作票文件保存，多个用#号分割")
    handle_files = Column(String(256), nullable=True, comment=u"操作票文件保存，多个用#号分割")
    detection_files = Column(String(256), nullable=True, comment=u"现场检测单文件保存，多个用#号分割")
    service_files = Column(String(256), nullable=True, comment=u"运维服务单文件保存，多个用#号分割")
    other_files = Column(String(256), nullable=True, comment=u"其他文件保存，多个用#号分割")
    other_content = Column(String(256), nullable=True, comment=u"其他文件保存时输入内容")

    log_files = Column(String(256), nullable=True, comment=u"日志文件保存，多个用#号分割")
    summary_files = Column(String(256), nullable=True, comment=u"工作总结文件保存，多个用#号分割")
   
    check_flag = Column(Integer, nullable=True,comment=u"审核状态，1通过，2驳回")
    check_content = Column(String(256), nullable=True, comment=u"审批填写的内容")
    check_time = Column(DateTime, nullable=True,comment=u"审核时间")
    stage = Column(Integer, nullable=True,comment=u"工单阶段")
    real_start_time = Column(DateTime, nullable=True,comment=u"实际开始时间")
    real_end_time = Column(DateTime, nullable=True,comment=u"实际结束时间")

    care_user = Column(Integer, nullable=True,comment=u"被转交人")
    care_time = Column(DateTime, nullable=True,comment=u"转交时间")
    care_content = Column(String(256), nullable=True,comment=u"转交填写内容")

    en_content = Column(String(256), nullable=True, comment=u"英文工作内容")
    # en_ticket_files = Column(String(256), nullable=True, comment=u"英文工作票文件保存，多个用#号分割")
    # en_detection_files = Column(String(256), nullable=True, comment=u"英文现场检测单文件保存，多个用#号分割")
    # en_service_files = Column(String(256), nullable=True, comment=u"英文运维服务单文件保存，多个用#号分割")
    # en_other_files = Column(String(256), nullable=True, comment=u"英文其他文件保存，多个用#号分割")
    # en_log_files = Column(String(256), nullable=True, comment=u"英文日志文件保存，多个用#号分割")
    # en_summary_files = Column(String(256), nullable=True, comment=u"英文工作总结文件保存，多个用#号分割")
    en_check_content = Column(String(256), nullable=True, comment=u"英文审批填写的内容")
    en_care_content = Column(String(256), nullable=True, comment=u"英文转交填写内容")
    en_other_content = Column(String(256), nullable=True, comment=u"其他文件保存时输入内容")

    dispatch_r= relationship("DispatchR", backref="dispatch_r")
    

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        real_user = self.care_user if self.care_user else self.handle_user
        HU = user_session.query(User).filter(User.id==real_user,User.unregister==1).first()
        handle_descr = HU.name if HU else ""
        dispatch_descr = self.dispatch_r.descr if self.dispatch_r else ''
        # a2 = self.content.split() if self.content else []
        # a4 = self.check_content.split() if self.check_content else []
        # a3 = self.other_content.split() if self.other_content else []  # ';'.join(a3)
        
        if str(self.status) == '1':
            status = '进行中'
            en_status = 'Under way'
        elif str(self.status) == '2':
            status = '已结束'
            en_status = 'Already ended'

        if str(self.check_flag) == '1':
            check_flag = '通过'
            en_check_flag = 'Pass'
        elif str(self.check_flag) == '2':
            check_flag = '驳回'
            en_check_flag = 'Turn down'
        else :
            check_flag = '待审核'
            en_check_flag = 'To be reviewed'

        # bean = "{'id':%s,'dispatch_id':%s,'dispatch_descr':'%s','status':'%s','content':'%s','handle_time':'%s','dispatch_step_id':%s,'check_time':'%s','handle_user':'%s','handle_descr':'%s',\
        # 'send_time':'%s','read_time':'%s','ticket_files':'%s','detection_files':'%s','check_flag':'%s','check_content':'%s','service_files':'%s','other_files':'%s','other_content':'%s',\
        #     'log_files':'%s','summary_files':'%s','stage':'%s','handle_files':'%s','realStartTime':'%s','realEndTime':'%s','care_user':'%s','care_time':'%s','care_content':'%s','en_content':'%s','en_ticket_files':'%s','en_detection_files':'%s'" \
        #        ",'en_service_files':'%s','en_other_files':'%s','en_log_files':'%s','en_summary_files':'%s','en_check_content':'%s','en_care_content':'%s'}" % (self.id,self.dispatch_id,dispatch_descr,status,self.content,self.handle_time,self.dispatch_step_id,self.check_time,real_user,handle_descr,self.send_time,self.read_time,
        #     self.ticket_files,self.detection_files,check_flag,self.check_content,self.service_files,self.other_files,self.other_content,self.log_files,self.summary_files,self.stage,self.handle_files,
        #     self.real_start_time,self.real_end_time,self.care_user,self.care_time,self.care_content,self.en_content,self.en_ticket_files,self.en_detection_files,self.en_service_files,self.en_other_files,self.en_log_files,self.en_summary_files,self.en_check_content,self.en_care_content)

        bean = "{'id':%s,'dispatch_id':%s,'dispatch_descr':'%s','status':'%s','en_status':'%s','content':'%s','handle_time':'%s','dispatch_step_id':%s,'check_time':'%s','handle_user':'%s','handle_descr':'%s',\
                'send_time':'%s','read_time':'%s','ticket_files':'%s','detection_files':'%s','check_flag':'%s','en_check_flag':'%s','check_content':'%s','service_files':'%s','other_files':'%s','other_content':'%s',\
                    'log_files':'%s','summary_files':'%s','stage':'%s','handle_files':'%s','realStartTime':'%s','realEndTime':'%s','care_user':'%s','care_time':'%s','care_content':'%s','en_content':'%s','en_other_content':'%s','en_check_content':'%s','en_care_content':'%s'}" % (
               self.id, self.dispatch_id, dispatch_descr, status,en_status, self.content, self.handle_time, self.dispatch_step_id,
               self.check_time, real_user, handle_descr, self.send_time, self.read_time,
               self.ticket_files, self.detection_files, check_flag,en_check_flag, self.check_content, self.service_files,
               self.other_files, self.other_content, self.log_files, self.summary_files, self.stage, self.handle_files,
               self.real_start_time, self.real_end_time, self.care_user, self.care_time, self.care_content,
               self.en_content, self.en_other_content, self.en_check_content,
               self.en_care_content)
        
        return bean.replace("None",'')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}