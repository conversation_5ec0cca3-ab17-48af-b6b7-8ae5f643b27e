import json
import re
from datetime import datetime, timedelta

from rest_framework import serializers
from django.core.validators import RegexValidator
from apis.user import models
from rest_framework import exceptions
from django_redis import get_redis_connection

from LocaleTool.common import redis_pool
from tools.time_sorted import time_sorted


class PowerPlanHistoryEditSerializer(serializers.Serializer):
    """历史记录修改"""

    station = serializers.CharField(required=True)
    unique = serializers.UUIDField(required=True)
    power = serializers.IntegerField(required=True)


class MonitorSendMobileMessageSerializer(serializers.Serializer):
    """控制策略短信发送"""

    mobile = serializers.CharField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')
        if not re.match("^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Invalid mobile.")
        return value


class ControlCheckSMSCodeSerializer(serializers.Serializer):
    """控制策略短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)
    # 控制策略
    control_strategy = serializers.IntegerField(required=True)

    # 单元id
    unit_id = serializers.IntegerField(required=False)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')
        if not re.match("^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Invalid mobile.")

        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else "Mobile does not exist.")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get("lang", 'zh')
        mobile = self.initial_data.get("mobile")
        print(65, value)
        monitor_mobile = "control" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", str(value)):
            raise Exception("验证码不正确" if lang == 'zh' else "Invalid code.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Invalid code.")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise Exception("验证码错误" if lang == 'zh' else "Invalid code.")
        return value


class PowerCheckSMSCodeSerializer(serializers.Serializer):
    """控制策略短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.CharField(required=True)
    # 功率
    power = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')
        if not re.match("^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Invalid mobile.")

        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else "Mobile does not exist.")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get("lang", 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "power" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", str(value)):
            raise Exception("验证码不正确" if lang == 'zh' else "Invalid code.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Invalid code.")
        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else "Invalid code.")
        return value


class BasePlanSerializer(serializers.Serializer):
    start_time = serializers.DateTimeField(required=True)
    end_time = serializers.DateTimeField(required=True)
    power = serializers.IntegerField(required=True)
    power_follow = serializers.CharField(required=True, max_length=16)


class PowerPlanCheckSMSCodeSerializer(serializers.Serializer):
    """控制策略短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # 功率
    power_plan = BasePlanSerializer(many=True)
    """
    数据格式为 {"plan": [
        {"start_time":"ctime", "end_time":"ctime", "power":123,power_follow:True},
        {"start_time":"ctime", "end_time":"ctime", "power":123,power_follow:True},
        {"start_time":"ctime", "end_time":"ctime", "power":123,power_follow:True},
        {"start_time":"ctime", "end_time":"ctime", "power":123,power_follow:True},
    ]}
    """
    # 站名
    strategy = serializers.CharField(required=True)

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "power_plan" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if cache_mobile_code.decode('utf-8') != value:
            raise exceptions.ValidationError("验证码错误")
        return value

    def validate_power_plan(self, value):
        """判断下发功率时间是否有重叠"""
        time_lists = []
        for plan in value:
            time_lists.append((plan["start_time"], plan["end_time"]))

        whether_repeat = time_sorted(time_lists)
        if not whether_repeat:
            raise exceptions.ValidationError("下发功率时间有重叠")
        return value


class StationNameSerializer(serializers.Serializer):
    strategy = serializers.CharField(required=True)


class StationSerializer(serializers.Serializer):
    station = serializers.CharField(required=True)


class PowerPlanHistorySerializer(serializers.Serializer):
    unit_name = serializers.CharField(required=False)
    station = serializers.CharField(required=False)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    type = serializers.CharField(required=False, max_length=32)

    def validate(self, attrs):
        """校验 station ,unit 不能同时为空"""
        station = attrs.get("station")
        unit_name = attrs.get("unit_name")
        if not station and not unit_name:
            raise serializers.ValidationError("station unit_name 不能同时为空")
        if station and unit_name:
            raise serializers.ValidationError("station unit_name 不能同时存在")
        return attrs


class LoadDaySerializer(serializers.Serializer):
    station = serializers.CharField(required=True)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)


class ElectricityDayCountSerializer(serializers.Serializer):
    unit_name = serializers.CharField(required=False)
    station = serializers.CharField(required=False, max_length=64)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    type = serializers.CharField(required=False, max_length=32)

    def validate(self, attrs):
        """校验 station ,unit 不能同时为空"""
        station = attrs.get("station")
        unit_name = attrs.get("unit_name")
        if not station and not unit_name:
            raise serializers.ValidationError("station unit_name 不能同时为空")
        if station and unit_name:
            raise serializers.ValidationError("station unit_name 不能同时存在")
        return attrs


class ElectricityTypeCountSerializer(serializers.Serializer):
    unit = serializers.CharField(required=False)
    station = serializers.CharField(required=False, max_length=64)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    type = serializers.CharField(required=False, max_length=32)

    def validate(self, attrs):
        """校验 station ,unit 不能同时为空"""
        station = attrs.get("station")
        unit_name = attrs.get("unit")
        if not station and not unit_name:
            raise serializers.ValidationError("station unit 不能同时为空")
        if station and unit_name:
            raise serializers.ValidationError("station unit 不能同时存在")
        return attrs


class UnitSwitchCheckSMSCodeSerializer(serializers.Serializer):
    """储能单元短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.CharField(required=True)
    # 开关
    switch = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)
    # 储能单元英文名
    unit = serializers.CharField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception('手机号格式不正确' if lang == 'zh' else 'Invalid mobile.')
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else 'Mobile does not exist.')
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get("lang", 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "unit_switch" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", str(value)):
            raise Exception('验证码格式不正确' if lang == 'zh' else 'Invalid code.')

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else 'Code expired or does not exist.')

        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else 'Code error.')
        return value


class UnitSwitchSwitchRunningTypeSerializer(serializers.Serializer):
    """储能单元切换运行类型"""""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # 运行状态
    running_type = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)
    # 储能单元英文名
    unit = serializers.CharField(required=True)

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "unit_switch_running_type" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if cache_mobile_code.decode('utf-8') != value:
            raise exceptions.ValidationError("验证码错误")
        return value


class UnitPowerCheckSMSCodeSerializer(serializers.Serializer):
    """储能单元功率短信验证并下发"""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # 功率
    power = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)

    # # 储能单元英文名
    unit = serializers.CharField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception('手机号格式不正确' if lang == 'zh' else 'Invalid mobile.')

        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else 'Mobile does not exist.')
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get("lang", 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "unit_power" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not re.match(r"^\d{6}$", str(value)):
            raise Exception('验证码格式不正确' if lang == 'zh' else 'Invalid code.')

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else 'Code expired or does not exist.')

        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else 'Code error.')
        return value


class VirtuallyCheckSMSCodeSerializer(serializers.Serializer):
    """控制策略短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.IntegerField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # 站名
    strategy = serializers.CharField(required=True)
    # 虚量参数
    virtually = serializers.IntegerField(required=True)

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "virtually" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise exceptions.ValidationError("验证码错误")
        return value


class ResetFaultSMSCodeCheckSerializer(serializers.Serializer):
    """故障复位下发"""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.IntegerField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # 站名
    strategy = serializers.CharField(required=True)
    # unit
    unit = serializers.CharField(required=True)

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "reset" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise exceptions.ValidationError("验证码错误")
        return value


class AlarmSendSMSSerializer(serializers.Serializer):
    station = serializers.CharField(required=True, max_length=64)
    ids = serializers.ListSerializer(required=True, child=serializers.IntegerField(), allow_empty=False)


class WebAlarmSendSMSSerializer(serializers.Serializer):
    project = serializers.CharField(required=True, max_length=64)
    # ids = serializers.ListSerializer(required=True, child=serializers.IntegerField(), allow_empty=False)
    # ids = serializers.CharField(required=True, max_length=256)               # 备注：web 前端json参数ids 由列表转换为字符串 ：{"station":"nblangsheng","ids":"[39005,39006]"}


class ElectricityLoadCurveSerializer(serializers.Serializer):
    station = serializers.CharField(required=True, max_length=64)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    # # meter_device = serializers.CharField(required=True)                      # 现场充放电计量设备：BMS,PCS
    # electrical_meter_location = serializers.CharField(required=True)      # 电表位置: pre：前置：post
    #
    # def validate(self, attrs):
    #     """校验 meter_device ,electrical_meter_location 的取值范围"""
    #     meter_device = attrs.get("meter_device")
    #     electrical_meter_location = attrs.get("electrical_meter_location")
    #     if meter_device not in ['BMS', 'PCS']:
    #         raise serializers.ValidationError("现场充放电计量设备参数有误")
    #     if electrical_meter_location not in ['pre', 'post']:
    #         raise serializers.ValidationError("电表位置参数有误")
    #     return attrs


class OperatingDataMonitorSerializer(serializers.Serializer):
    project = serializers.CharField(required=True, max_length=64)
    system_level = serializers.CharField(required=True, max_length=64)
    target = serializers.CharField(required=True, max_length=64)
    start_time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    end_time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    key1 = serializers.CharField(required=False, max_length=32)
    key2 = serializers.CharField(required=False, max_length=32)


class WebAlarmMonitorSerializer(serializers.Serializer):
    # station_id = serializers.CharField(required=True, max_length=8, validators=[RegexValidator(r"^\d+$",
    #                                                                                            message="站 ID 格式错误")])
    status = serializers.CharField(required=False, max_length=128)
    type = serializers.CharField(required=False, max_length=128)
    station_id = serializers.CharField(required=False, max_length=128)
    start_time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    end_time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    keyword = serializers.CharField(required=False, max_length=128)

    def validate(self, attrs):
        """校验取值范围"""
        lang = self.context.get("lang", "zh")
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")
        status = attrs.get('status')
        type_ = attrs.get('type')

        # if start_time:
        #     try:
        #         datetime.strptime(start_time, '%Y-%m-%d')
        #     except ValueError:
        #         raise serializers.ValidationError('Invalid start_time format. Use YYYY-MM-DDS.')
        #
        # if end_time:
        #     try:
        #         datetime.strptime(end_time, '%Y-%m-%dS')
        #     except ValueError:
        #         raise serializers.ValidationError('Invalid end_time format. Use YYYY-MM-DD.')

        if start_time and end_time:
            # start_time_ = str(start_time)
            # end_time_ = str(end_time).replace('00:00:00', '23:59:59')
            # start_datatime = datetime.strptime(start_time_, "%Y-%m-%d")
            # end_datatime = datetime.strptime(end_time_, "%Y-%m-%d")

            if start_time > end_time:
                raise Exception('结束时间不能早于开始时间.' if lang == 'zh' else 'End time cannot be earlier than start time.')
        if status:
            if int(status) not in [0, 1, 2, 3, 4]:
                raise Exception('告警状态参数错误.' if lang == 'zh' else 'Alarm status parameter error.')
        if type_:
            if int(type_) not in [0, 1, 2, 3, 4, 5, 6]:
                raise Exception('告警类型参数错误.' if lang == 'zh' else 'Alarm type parameter error.')

        return attrs


class AlarmFeedbackSerializer(serializers.Serializer):
    content = serializers.CharField(required=True, max_length=256)
    is_dispatch_worker = serializers.BooleanField(required=False)
    worker_order = serializers.CharField(required=False, max_length=64)
    question_type = serializers.CharField(required=True, max_length=64)
    expected_closing_date = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    real_closing_date = serializers.DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'], required=False)
    note = serializers.CharField(required=False, max_length=256)

    class Meta:
        model = models.AggrFaultAlarmFeedback
        # exclude = ['feedback_time', "id"]
        fields = '__all__'
        read_only_fields = ('id', 'feedback_time', 'feedback_user')

    def validate(self, attrs):
        lang = self.context.get("lang", "zh")
        expected_closing_date = attrs.get("expected_closing_date")
        if expected_closing_date:
            if expected_closing_date.date() <= datetime.now().date() - timedelta(days=1):
                raise Exception("预计闭环时间不能小于当前日期" if lang == 'zh' else 'The expected closed-loop time cannot be less than the current date.')

        return attrs

    def create(self, validated_data):
        lang = self.context.get("lang", "zh")
        ins = models.AggrFaultAlarmFeedback.objects.create(**validated_data, en_content=validated_data.get("content"),
                                                           en_question_type=validated_data.get("question_type"),
                                                           en_note=validated_data.get("note"),
                                                           feedback_user_id=self.context["user_id"],
                                                           feedback_time=datetime.now())

        # 异步翻译
        pdr_data = {'id': ins.id,
                    'table': 't_fault_alarm_aggr_feedback',
                    'update_data': {'content': validated_data.get("content"),
                                    'question_type': validated_data.get("question_type")}}
        if validated_data.get("note") and validated_data.get("note") != '':
            pdr_data['update_data']['note'] = validated_data.get("note")

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return ins

    def update(self, instance, validated_data):
        lang = self.context.get("lang", "zh")

        instance.content = validated_data.get("content", instance.content)
        instance.en_content = validated_data.get("content", instance.en_content)
        instance.feedback_time = datetime.now()
        instance.feedback_user_id = self.context["user_id"]
        instance.is_dispatch_worker = validated_data.get("is_dispatch_worker", instance.is_dispatch_worker)
        instance.worker_order = validated_data.get("worker_order", instance.worker_order)
        instance.question_type = validated_data.get("question_type", instance.question_type)
        instance.en_question_type = validated_data.get("question_type", instance.en_question_type)
        instance.note = validated_data.get("note", instance.note)
        instance.en_note = validated_data.get("note", instance.en_note)
        instance.feedback_content = validated_data.get("feedback_content", instance.feedback_content)
        instance.expected_closing_date = validated_data.get("expected_closing_date", instance.expected_closing_date)
        instance.save()

        # 异步翻译
        pdr_data = {'id': instance.id,
                    'table': 't_fault_alarm_aggr_feedback',
                    'update_data': {'content': validated_data.get("content"),
                                    'question_type': validated_data.get("question_type")}}
        if validated_data.get("note") and validated_data.get("note") != '':
            pdr_data['update_data']['note'] = validated_data.get("note")

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

class WebOcsMonitorSerializer(serializers.Serializer):
    
    english_name = serializers.ListField(required=False, max_length=128)
    start_time = serializers.DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'], required=False)
    end_time = serializers.DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'], required=False)
    keyword = serializers.CharField(required=False, max_length=128)
    detail = serializers.CharField(required=False, max_length=128)

    def validate(self, attrs):
        """校验取值范围"""
        lang = self.context.get("lang", "zh")
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")
        detail = attrs.get('detail')
        english_name = attrs.get('english_name')

        if start_time and end_time:
            if start_time > end_time:
                raise Exception('结束时间不能早于开始时间.' if lang == 'zh' else 'End time cannot be earlier than start time.')
            # s_day = datetime.strptime(start_time,'%Y-%m-%d %H:%M:%S')
            # e_day = datetime.strptime(end_time,'%Y-%m-%d %H:%M:%S')
            s = end_time-start_time
            if s.days>30:
                raise Exception('时间跨度请小于30天.' if lang == 'zh' else 'The time span is less than 30 days.')
        return attrs
    
class WebOcsInfosMonitorSerializer(serializers.Serializer):
    
    tiggr_devices = serializers.CharField(required=False)  # 触发设备集合、
    ocs_points = serializers.CharField(required=False)  # 录波点集合、
    ocs_devices = serializers.CharField(required=False)  # 录波设备集合、
    time_ = serializers.DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'], required=True)  #触发时间
    v_number = serializers.IntegerField(required=True)  #版本号
    english_name = serializers.CharField(required=True, max_length=128)  # 从站
    tiggr_point = serializers.CharField(required=True, max_length=128)  # 触发点
    station_name = serializers.CharField(required=True, max_length=128)  # 并网点名称

    def validate(self, attrs):
        """校验取值范围"""
        lang = self.context.get("lang", "zh")
        tiggr_devices = attrs.get("tiggr_devices")
        ocs_points = attrs.get("ocs_points")
        time_ = attrs.get("time_")
        v_number = attrs.get("v_number")
        english_name = attrs.get("english_name")
        tiggr_point = attrs.get("tiggr_point")
        station_name = attrs.get("station_name")
       
        if not time_ or not v_number or not station_name or not tiggr_point:
            raise Exception('入参缺失.' if lang == 'zh' else 'Inclusion deletion .')
        return attrs

class AlarmFeedbackDetailSerializer(serializers.Serializer):

    class Meta:
        model = models.AggrFaultAlarmFeedback
        # exclude = ['feedback_time', "id"]
        fields = '__all__'
