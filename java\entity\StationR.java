package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电站关系表
 * 对应Python模型: StationR
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_station_relation")
public class StationR extends SuperEntity {

    /**
     * 电站名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 电功率
     */
    @TableField("electric_power")
    private Double electricPower;

    /**
     * 运行状态
     */
    @TableField("running_state")
    private String runningState;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 储能
     */
    @TableField("energy_storage")
    private String energyStorage;

    /**
     * 有功功率名称(JSON格式)
     */
    @TableField("active_power_name")
    private String activePowerName;

    /**
     * 充电容量名称
     */
    @TableField("chag_capacity_name")
    private String chagCapacityName;

    /**
     * 放电容量名称
     */
    @TableField("disg_capacity_name")
    private String disgCapacityName;

    /**
     * 电池簇
     */
    @TableField("battery_cluster")
    private String batteryCluster;

    /**
     * 放电容量
     */
    @TableField("DisgCapy")
    private String disgCapy;

    /**
     * 充电容量
     */
    @TableField("ChagCapy")
    private String chagCapy;

    /**
     * SOC
     */
    @TableField("SOC")
    private String soc;

    /**
     * 监控
     */
    @TableField("monitor")
    private String monitor;

}
