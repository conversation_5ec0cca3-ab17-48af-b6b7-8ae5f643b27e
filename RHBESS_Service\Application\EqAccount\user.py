# -*- coding:utf-8 -*-
import json
import logging
import tornado.web
from sqlalchemy import func
from sqlalchemy import or_, and_
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.organization import Organization
from Application.Models.User.user import User
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import judge_phone,computeMD5,password_is_valid, Translate_cls


class UserIntetface(BaseHandler):
    '''
    @description: 用户管理
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        if kt == 'GetAll':  # 获取所有用户
            try:
                if not lang:
                    lang = 'zh'
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%id)
                if not id:
                    session = self.getOrNewSession()
                    id = session.user['organization_id']
                org = user_session.query(Organization).filter(Organization.id==id).first()
                if not org:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                all = []
                get_end_node = org.get_lower_node()
                total = user_session.query(func.count(User.id)).filter(User.organization_id.in_(get_end_node)).scalar()
                stations = user_session.query(User).filter(User.organization_id.in_(get_end_node)).order_by(User.id.desc()).all()
                for station in stations:
                    all.append(eval(str(station)))  
                user_session.close()  
                return self.returnTotalSuc(all,total)
            except Exception as E:
                logging.info(E)
                user_session.rollback()
                return self.requestError()
                    
        elif kt == 'GetAllUser':  # 获取指定组织下所有用户
            try:
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,pageSize:%s"%(descr,pageNum,pageSize))
                if not id:
                    session = self.getOrNewSession()
                    id = session.user['organization_id']
                org = user_session.query(Organization).filter(Organization.id==id).first()
                if not org:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                get_end_node = org.get_lower_node()
                if descr :
                    total = user_session.query(func.count(User.id)).filter(or_(User.name.like('%' + descr + '%'),User.phone_no.like('%' + descr + '%')),
                    and_(User.organization_id.in_(get_end_node))).scalar()
                    users = user_session.query(User).filter(or_(User.name.like('%' + descr + '%'),User.phone_no.like('%' + descr + '%')),
                     and_(User.organization_id.in_(get_end_node))).order_by(User.id.desc()
                    ).limit(pageSize).offset((pageNum-1)*pageSize).all()
                else:
                    total = user_session.query(func.count(User.id)).filter(User.organization_id.in_(get_end_node)).scalar()
                    users = user_session.query(User).filter(User.organization_id.in_(get_end_node)).order_by(User.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                all = []
                for u in users:
                    u = eval(str(u))
                    if lang == 'en':
                        replaced_data = User.replace_en_fields(u, "")
                        u.update(replaced_data)
                    all.append(u)
                user_session.close()
                return self.returnTotalSuc(all,total)

            except Exception as E:
                logging.info(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
        else:
            return self.pathError()
    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        if kt == 'Delete':  # 删除用户
            try:
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(User).filter(User.id==id).first()
                if not delete_apex:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                delete_apex.deleteUser(id)
                # user_session.query(User).filter(User.id == id).delete()
                user_session.commit()
                user_session.close()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
         
        elif kt == 'Add':  # 添加用户
            try:
                name = self.get_argument('descr',None)
                sex = self.get_argument('sex',0)
                phone_no = self.get_argument('phone_no',None)
                account = self.get_argument('account',None)
                email = self.get_argument('email',None)
                passwd = self.get_argument('passwd','RHYc@2023')
                organization_id = self.get_argument('organization_id',None)
                user_role_id = self.get_argument('user_role_id',None)
                station_id = self.get_argument('station_id','1,2,3,4,5')
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("name:%s,sex:%s,phone_no:%s,account:%s,passwd:%s,organization_id:%s,user_role_id:%s,station_id:%s,email:%s"%(name,sex,phone_no,account,
                    passwd,organization_id,user_role_id,station_id,email))
                if not password_is_valid(passwd):
                    return self.customError("密码无效，必须包括大小写字母，数字和特殊字符长度大于8") if lang == 'zh' else self.customError("The password is invalid and must include uppercase and lowercase letters, numbers, and special characters with a length greater than 8")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效") if lang == 'zh' else self.customError("Invalid phone number")
                if not name :
                    return self.customError("姓名为空") if lang == 'zh' else self.customError("Name is empty")
                if not account :
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Login name is empty")
                if not organization_id :
                    return self.customError("组织为空") if lang == 'zh' else self.customError("Organization is empty")
                if not user_role_id:
                    return self.customError("角色为空") if lang == 'zh' else self.customError("The role is empty")
                if len(name) > 25:
                    return self.customError("姓名长度不能超过25") if lang == 'zh' else self.customError("The name length cannot exceed 25")
                if len(account) >25:
                    return self.customError("名称长度不能超过25") if lang == 'zh' else self.customError("The login name length cannot exceed 25")

                elif user_session.query(User).filter(User.account == account).first():
                    return self.customError("用户名已存在") if lang == 'zh' else self.customError("The username already exists")
                elif user_session.query(User).filter(User.phone_no == phone_no).first():
                    return self.customError("手机号已存在") if lang == 'zh' else self.customError("Phone number already exists")

                # for ch in account:
                #     if u'\u4e00' <= ch <= u'\u9fa5':
                #         return self.customError("登录名不允许存在中文") if lang == 'zh' else self.customError("Login name does not allow Chinese characters")
                passwd = computeMD5(passwd)
                wx_connect = computeMD5(phone_no)+phone_no[-4:]
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                t_res = t_cls.str_chinese(name)
                if ty == 2:
                    en_name = t_res
                else:
                    en_name = name
                    name = t_res
                user = User(name=name,sex=sex,account=account,passwd=passwd,organization_id=organization_id,user_role_id=user_role_id,en_name=en_name,
                    phone_no=phone_no,op_ts=timeUtils.getNewTimeStr(),unregister=1,wx_connect=wx_connect,station_id=station_id,email=email)
                user_session.add(user)
                user_session.commit()
                user_session.close()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
            
        elif kt == 'Modify':  # 修改用户信息
            try:
                id = int(self.get_argument('id',None))
                name = self.get_argument('descr',None)
                sex = self.get_argument('sex',0)
                phone_no = self.get_argument('phone_no',None)
                account = self.get_argument('account',None)
                email = self.get_argument('email',None)
                organization_id = self.get_argument('organization_id',None)
                user_role_id = self.get_argument('user_role_id',None)
                unregister = self.get_argument('unregister',None)
                passwd = self.get_argument('passwd',None)
                station_id = self.get_argument('station_id',[])
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s,name:%s,sex:%s,phone_no:%s,account:%s,organization_id:%s,user_role_id:%s,unregister:%s,passwd:%s,station_id:%s,email:%s"%(id,
                    name,sex,phone_no,account,organization_id,user_role_id,unregister,passwd,station_id,email))
                to_update = user_session.query(User).filter(User.id==id).first()
               
                if not to_update:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效") if lang == 'zh' else self.customError("Invalid phone number")
                if not name :
                    return self.customError("姓名为空") if lang == 'zh' else self.customError("Name is empty")
                if not account :
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Login name is empty")
                if len(name) > 25:
                    return self.customError("姓名长度不能超过25") if lang == 'zh' else self.customError("The name length cannot exceed 25")
                if len(account) >25:
                    return self.customError("名称长度不能超过25") if lang == 'zh' else self.customError("The login name length cannot exceed 25")
                # for ch in account:
                #     if u'\u4e00' <= ch <= u'\u9fa5':
                #         return self.customError("登录名不允许存在中文") if lang == 'zh' else self.customError("Login name does not allow Chinese characters")
                if not organization_id :
                    return self.customError("组织为空") if lang == 'zh' else self.customError("Organization is empty")
                if not user_role_id:
                    return self.customError("角色为空") if lang == 'zh' else self.customError("The role is empty")
                if account != to_update.account and user_session.query(User).filter(User.account == account).first():
                    return self.customError("登录名重复") if lang == 'zh' else self.customError("Login name is duplicate")
                if phone_no != to_update.phone_no and user_session.query(User).filter(User.phone_no == phone_no).first():
                    return self.customError("手机号已存在") if lang == 'zh' else self.customError("Phone number already exists")

                if phone_no != to_update.phone_no:
                    to_update.wx_connect = computeMD5(phone_no)+phone_no[-4:]
                if passwd:
                    if not password_is_valid(passwd):
                        return self.customError("密码无效，必须包括大小写字母，数字和特殊字符长度大于8") if lang == 'zh' else self.customError("The password is invalid and must include uppercase and lowercase letters, numbers, and special characters with a length greater than 8")
                    passwd = computeMD5(passwd)  # md5加密
                    if passwd == to_update.passwd:  # 修改后和原密码一样
                        return self.customError("新旧密码一致") if lang == 'zh' else self.customError("Consistent old and new passwords")
                    to_update.passwd = passwd
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                t_res = t_cls.str_chinese(name)
                if ty == 2:
                    en_name = t_res
                else:
                    en_name = name
                    name = t_res
                to_update.name = name
                to_update.en_name = en_name
                to_update.sex = sex
                to_update.phone_no = phone_no
                to_update.account = account
                if email:
                    to_update.email = email
                to_update.organization_id = organization_id
                to_update.user_role_id = user_role_id
                to_update.unregister = unregister
                if station_id and id != 1:
                    to_update.station_id=station_id
                user_session.commit()
                user_session.close()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
            
        elif kt == 'UpdatePwd':  # 修改用户密码
            try:
                id = self.get_argument('id',None)
                passwd = self.get_argument('passwd',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s,passwd:%s"%(id,passwd))
                to_update = user_session.query(User).filter(User.id==id).first()
                if not to_update or not passwd:
                    return self.customError("id无效或密码为空") if lang == 'zh' else self.customError("Invalid ID or empty password")
                if not password_is_valid(passwd):
                    return self.customError("密码无效，必须包括大小写字母，数字和特殊字符长度大于8") if lang == 'zh' else self.customError("The password is invalid and must include uppercase and lowercase letters, numbers, and special characters with a length greater than 8")
               
                # if passwd == to_update.passwd:  # 原密码没修改
                #     return self.customError("新旧密码一致")
                passwd = computeMD5(passwd)  # md5加密
                if passwd == to_update.passwd:  # 修改后和原密码一样
                    return self.customError("新旧密码一致") if lang == 'zh' else self.customError("Consistent old and new passwords")

                to_update.passwd = passwd
                user_session.commit()
                user_session.close()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
        elif kt == 'InitPwd':  # 初始化密码
            try:
                if not lang:
                    lang = 'zh'
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                to_update = user_session.query(User).filter(User.id==id).first()
                if not to_update :
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                to_update.passwd = computeMD5('RHYc@2023')
                
                user_session.commit()
                user_session.close()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
                # return self.requestError()
        else:
            return self.pathError()
       
                

   
    