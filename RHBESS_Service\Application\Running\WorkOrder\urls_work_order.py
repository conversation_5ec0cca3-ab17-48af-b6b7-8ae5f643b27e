#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_Service\Application\Running\WorkOrder\urls_work_order.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-21 08:32:21


# -*- coding=utf-8 -*-
from tornado.routing import Rule, PathMatches
from tornado.web import url

from Application.EqAccount.WorkOrder.workOrderManager import WorkOrderManagerIntetface
from Application.EqAccount.WorkOrder.workOrderHandle import WorkOrderHandleIntetface
from Application.EqAccount.WorkOrder.spareManager import SpareManagerIntetface
from Application.EqAccount.WorkOrder.sopManager import SOPManagerIntetface
from Application.EqAccount.WorkOrder.saleWorkerOrderHanddle import SaleWorkerOrderHanddleIntetface


routes = [
    # 工单管理
    url(r"/WorkOrderManager/(\w+)", WorkOrderManagerIntetface),
    # 工单处理
    url(r"/WorkOrderHandle/(\w+)", WorkOrderHandleIntetface),
    # 备件管理
    url(r"/SpareManager/(\w+)", SpareManagerIntetface),
    # SOP管理
    url(r"/SOPManager/(\w+)", SOPManagerIntetface),
    # 售后工单处理
    url(r"/SaleWorkerOrderHandle/(\w+)", SaleWorkerOrderHanddleIntetface),
      

]
