#!/usr/bin/env python
# -*- coding: UTF-8 -*-
u'''
login服务响应
'''

import logging
import math, pickle
from Application.Models.base_handler import BaseHandler
from Application.Models.User.user import User
from Application.Models.User.role_authority import RoleAuthority
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session, DEBUG
import json, base64
from Tools.DB.redis_con import r
from Tools.Utils.num_utils import judge_phone, computeMD5
from Application.Models.User.event_r import EventR
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.station import Station
from sqlalchemy import or_
from Application.EqAccount.encryption.jwt_encryption import create_token


class UserLoginHandler(BaseHandler):
    u'用于用户登录'

    def get(self, kt):

        if kt == 'userNull':  # 用户未登录
            return self.userError()
        else:
            return self.pathError()

    def post(self, kt):
        lang = self.get_argument('lang', None)  # 英文
        # if kt == 'Login':  # 登录
        #     try:
        #         # if 1:
        #         Session = self.getOrNewSession()
        #         user = Session.user
        #         if lang == 'en':
        #             if user:
        #                 logging.info("user is in session")
        #                 return self.userExist("User logged in", user)
        #             account = self.get_argument("account", None)  # 登录名
        #             passwd = self.get_argument("passwd", None)  # 密码
        #             if DEBUG:
        #                 logging.info('account:%s,passwd:%s' % (account, passwd))
        #             if not account or not passwd:
        #                 return self.customError("The user name or password is empty")
        #             if account == 'zulin':  # 租赁的账号密码包含固定字符，需要将固定字符去掉
        #                 passwd = passwd.replace('rhyc', '')
        #             passwd = computeMD5(passwd)
        #             user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
        #                                                    User.unregister == 1).first()
        #             if not user:
        #                 Session.remove()
        #                 return self.customError("The user name or password is incorrect")
        #             else:
        #                 retry = r.get(str(user.id) + self.request.remote_ip)
        #                 if retry and int(retry) > 3:
        #                     Session.remove()
        #                     disable = r.ttl(str(user.id) + self.request.remote_ip)
        #                     disable_minute = 0
        #                     if disable:
        #                         disable_minute = int(math.ceil(disable / 60.0))
        #                     return self.customError("Account locked please" + str(disable_minute) + "minute try again")
        #                 if user.passwd != passwd:
        #                     r.incr(str(user.id) + self.request.remote_ip, )
        #                     r.expire(str(user.id) + self.request.remote_ip, 600)
        #                     retry = r.get(str(user.id) + self.request.remote_ip)
        #                     Session.remove()
        #                     if retry and int(retry) == 3:
        #                         return self.customError("One more chance to lock out your account for 10 minutes")
        #                     elif retry and int(retry) >= 3:
        #                         return self.customError("Account locked")
        #                     return self.customError("The user name or password is incorrect")
        #         else:
        #             if user:
        #                 logging.info("user is in session")
        #                 return self.userExist("用户已登录", user)
        #             account = self.get_argument("account", None)  # 登录名
        #             passwd = self.get_argument("passwd", None)  # 密码
        #             if DEBUG:
        #                 logging.info('account:%s,passwd:%s' % (account, passwd))
        #             if not account or not passwd:
        #                 return self.customError("用户名或密码为空")
        #             if account == 'zulin':  # 租赁的账号密码包含固定字符，需要将固定字符去掉
        #                 passwd = passwd.replace('rhyc', '')
        #             passwd = computeMD5(passwd)
        #             user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
        #                                                    User.unregister == 1).first()
        #             if not user:
        #                 Session.remove()
        #                 return self.customError("用户名或密码错误")
        #             else:
        #                 retry = r.get(str(user.id) + self.request.remote_ip)
        #                 if retry and int(retry) > 3:
        #                     Session.remove()
        #                     disable = r.ttl(str(user.id) + self.request.remote_ip)
        #                     disable_minute = 0
        #                     if disable:
        #                         disable_minute = int(math.ceil(disable / 60.0))
        #                     return self.customError("账户已锁定请" + str(disable_minute) + "分钟再试")
        #                 if user.passwd != passwd:
        #                     r.incr(str(user.id) + self.request.remote_ip, )
        #                     r.expire(str(user.id) + self.request.remote_ip, 600)
        #                     retry = r.get(str(user.id) + self.request.remote_ip)
        #                     Session.remove()
        #                     if retry and int(retry) == 3:
        #                         return self.customError("还有一次机会账户被锁定10分钟")
        #                     elif retry and int(retry) >= 3:
        #                         return self.customError("账户已锁定")
        #                     return self.customError("用户名或密码错误")
        #         r.delete(str(user.id) + self.request.remote_ip)
        #         u = eval(str(user))
        #         if user.userRole.station_id:
        #             u['role_station_id'] = eval(user.userRole.station_id)
        #         user = u
        #         station_id = user['role_station_id']
        #         stations = user_session.query(Station).filter(Station.index.in_(list(station_id))).all()
        #         station_names = []
        #         for s in stations:
        #             station_names.append(s.name)
        #         user['station_name'] = station_names
        #         Session.user = user
        #         self.updateSession(Session)
        #         v = '用户：%s 登录 ip: %s' % (user['account'], self.request.remote_ip)
        #         event = EventR(value=1, value_descr='登录', event_id=1, op_ts=timeUtils.getNewTimeStr(),
        #                        ts=timeUtils.getNewTimeStr(), opt1=v)
        #         user_session.add(event)
        #         user_session.commit()
        #         # 将session归还连接池
        #         user_session.close()
        #         return self.returnTypeSuc(user)
        #     except Exception as E:
        #         logging.error(E)
        #         user_session.rollback()
        #         if lang == 'en':
        #             return self.requestError('en')
        #         else:
        #             return self.requestError()
        if kt == 'Login':  # 登录
            try:
                # if 1:
                # Session = self.getOrNewSession()
                # user = Session.user
                if lang == 'en':
                    # if user:
                    #     logging.info("user is in session")
                    #     return self.userExist("User logged in", user)
                    account = self.get_argument("account", None)  # 登录名
                    passwd = self.get_argument("passwd", None)  # 密码
                    if DEBUG:
                        logging.info('account:%s,passwd:%s' % (account, passwd))
                    if not account or not passwd:
                        return self.customError("The user name or password is empty")
                    if account == 'zulin':  # 租赁的账号密码包含固定字符，需要将固定字符去掉
                        passwd = passwd.replace('rhyc', '')
                    passwd = computeMD5(passwd)
                    user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
                                                           User.unregister == 1).first()
                    if not user:
                        # Session.remove()
                        return self.customError("The user name or password is incorrect")
                    else:
                        retry = r.get(str(user.id) + self.request.remote_ip)
                        if retry and int(retry) > 3:
                            # Session.remove()
                            disable = r.ttl(str(user.id) + self.request.remote_ip)
                            disable_minute = 0
                            if disable:
                                disable_minute = int(math.ceil(disable / 60.0))
                            return self.customError("Account locked please" + str(disable_minute) + "minute try again")
                        if user.passwd != passwd:
                            r.incr(str(user.id) + self.request.remote_ip, )
                            r.expire(str(user.id) + self.request.remote_ip, 600)
                            retry = r.get(str(user.id) + self.request.remote_ip)
                            # Session.remove()
                            if retry and int(retry) == 3:
                                return self.customError("One more chance to lock out your account for 10 minutes")
                            elif retry and int(retry) >= 3:
                                return self.customError("Account locked")
                            return self.customError("The user name or password is incorrect")
                else:
                    # if user:
                    #     logging.info("user is in session")
                    #     return self.userExist("用户已登录", user)
                    account = self.get_argument("account", None)  # 登录名
                    passwd = self.get_argument("passwd", None)  # 密码
                    if DEBUG:
                        logging.info('account:%s,passwd:%s' % (account, passwd))
                    if not account or not passwd:
                        return self.customError("用户名或密码为空")
                    if account == 'zulin':  # 租赁的账号密码包含固定字符，需要将固定字符去掉
                        passwd = passwd.replace('rhyc', '')
                    passwd = computeMD5(passwd)
                    user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
                                                           User.unregister == 1).first()
                    if not user:
                        # Session.remove()
                        return self.customError("用户名或密码错误")
                    else:
                        retry = r.get(str(user.id) + self.request.remote_ip)
                        if retry and int(retry) > 3:
                            # Session.remove()
                            disable = r.ttl(str(user.id) + self.request.remote_ip)
                            disable_minute = 0
                            if disable:
                                disable_minute = int(math.ceil(disable / 60.0))
                            return self.customError("账户已锁定请" + str(disable_minute) + "分钟再试")
                        if user.passwd != passwd:
                            r.incr(str(user.id) + self.request.remote_ip, )
                            r.expire(str(user.id) + self.request.remote_ip, 600)
                            retry = r.get(str(user.id) + self.request.remote_ip)
                            # Session.remove()
                            if retry and int(retry) == 3:
                                return self.customError("还有一次机会账户被锁定10分钟")
                            elif retry and int(retry) >= 3:
                                return self.customError("账户已锁定")
                            return self.customError("用户名或密码错误")
                r.delete(str(user.id) + self.request.remote_ip)
                u = eval(str(user))
                if user.userRole.station_id:
                    u['role_station_id'] = eval(user.userRole.station_id)
                user = u
                station_id = user['role_station_id']
                stations = user_session.query(Station).filter(Station.index.in_(list(station_id))).all()
                station_names = []
                for s in stations:
                    station_names.append(s.name)
                user['station_name'] = station_names
                success_token = create_token({"user_id": user.get('id')})
                if isinstance(success_token, bytes):
                    success_token = base64.b64encode(success_token).decode('utf-8')
                Session = self.getOrNewSession(success_token)
                Session.user = user
                self.updateSession(Session, success_token)
                v = '用户：%s 登录 ip: %s' % (user['account'], self.request.remote_ip)
                event = EventR(value=1, value_descr='登录', event_id=1, op_ts=timeUtils.getNewTimeStr(),
                               ts=timeUtils.getNewTimeStr(), opt1=v)
                user_session.add(event)
                user_session.commit()
                user['success_token'] = success_token
                # 将session归还连接池
                user_session.close()
                return self.returnTypeSuc(user)
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()

        elif kt == 'LogOut':  # 退出
            try:
                session = self.getOrNewSession()
                if session.user:
                    v = '用户：%s 退出 ip: %s' % (session.user['account'], self.request.remote_ip)
                    event = EventR(value=0, value_descr='退出', event_id=1, op_ts=timeUtils.getNewTimeStr(),
                                   ts=timeUtils.getNewTimeStr(), opt1=v)
                    user_session.add(event)
                    user_session.commit()
                    user_session.close()
                    session.user = None
                    self.updateSession(session)
                return self.returnTypeSuc('')

            except Exception as E:
                logging.error(E)
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
        elif kt == 'GetUserByPhone':  # 通过手机号获取用户
            try:
                session = self.getOrNewSession()
                phone_no = self.get_argument('phone_no', None)
                wx_connect = self.get_argument('wx_connect', None)
                if not phone_no or not wx_connect:
                    return self.dataError()
                if DEBUG:
                    logging.info("phone_no:%s,wx_connect:%s" % (phone_no, wx_connect))
                user = user_session.query(User).filter(User.phone_no == phone_no, User.wx_connect == wx_connect).first()

                if user:
                    session.user = eval(str(user))
                    self.updateSession(session)
                    msg = eval(str(user))
                else:
                    msg = ''
                user_session.close()
                return self.returnTypeSuc(msg)
            except Exception as E:
                logging.error(E)
                user_session.rollback()
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
        elif kt == 'GetOnLineUser':  # 在线用户
            try:
                data, i = [], 0
                all = r.keys()
                for a in all:
                    if len(a) == 32:
                        user = pickle.loads(r.get(a)).user
                        if user:
                            data.append(user)
                            i += 1

                return self.returnTotalSuc(data, i)
            except Exception as E:
                logging.error(E)
                if lang == 'en':
                    return self.requestError('en')
                else:
                    return self.requestError()
        else:
            return self.pathError()



