#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ
# @Date         : 2022-04-29 16:18:14
# @FilePath     : \RHBESS_Service\Application\EqAccount\HaLun\main_frame.py
# @Email        : <EMAIL>
# @LastEditTime : 2023-04-27 20:18:05

from Application.Models.User.event import Event
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.bit import Bit
import numpy, math
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import or_, asc
from Tools.DB.mysql_user import user_session, DEBUG
from Application.Models.base_handler import BaseHandler
from Tools.Utils.num_utils import *
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.page_data import PageData
from Tools.DB.mysql_scada import mqtt_session
from Application.Models.SelfStationPoint.t_status import StatusPT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT
from Application.Cfg.dir_cfg import model_config
from Application.Static.value_desc import discrete_data_obj, status_data_obj
from natsort import natsorted 

# 结构 {"page":{"area1":[区域一数据集],"area2":[区域2数据集]}}
datas_obj = {'his': {}, 'halun': {}, 'taicang': {}, 'binhai': {}, 'ygzhen': {}, 'baodian': {}, 'dongmu': {},
             'zgtian': {}, 'houma': {}, 'datong': {}, 'guizhou': {}, 'ygqn': {}, 'shgyu': {}}
pageData_first = {'his': 1, 'halun': 1, 'taicang': 10, 'binhai': 23, 'ygzhen': 40, 'baodian': 67, 'dongmu': 80,
                  'zgtian': 106,'houma': {991}, 'datong': 211, 'guizhou': {993}, 'ygqn': {994}, 'shgyu': {995}}  # 首页监视
pageData_pcs = {'his': 4, 'halun': 4, 'taicang': 13, 'binhai': 26, 'ygzhen': 50, 'baodian': 74, 'dongmu': 83,
                'zgtian': 124, 'datong': 228}  # pcs监视
pageData_sys = {'his': 2, 'halun': 2, 'taicang': 11, 'binhai': 24, 'ygzhen': 41, 'baodian': 68, 'dongmu': 81,
                'zgtian': 107, 'datong': 211}  # 系统监控对应页面id
heart_obj = {}  # 保留心跳上次的值
bit_obj = {}  # 所有bit位配置项
station=['binhai','halun','taicang','zgtian','ygzhen','baodian','dongmu','datong']
# 具体计算效率去除基础值（即错误值）
station_base_value = {"tfStbodian2.Pcs.DisgCapyTotl": 239395, "tfStbodian2.Pcs.ChagCapyTotl": 266699,
                      "tfStbodian3.Pcs.DisgCapyTotl": 446647, "tfStbodian3.Pcs.ChagCapyTotl": 321598,
                      "tfStbodian4.Pcs.DisgCapyTotl": 506974, "tfStbodian4.Pcs.ChagCapyTotl": 451724,
                      "tfStbodian5.Pcs.DisgCapyTotl": 364836, "tfStbodian5.Pcs.ChagCapyTotl": 349776,
                      "tfStzgtian1.EMS.PCS1.Lp1.AcDisgCapyTotl": 2358, "tfStzgtian1.EMS.PCS1.Lp1.AcChagCapyTotl": 1623,
                      "tfStzgtian1.EMS.PCS1.Lp2.AcDisgCapyTotl": 2620, "tfStzgtian1.EMS.PCS1.Lp2.AcChagCapyTotl": 1628,
                      "tfStzgtian1.EMS.PCS1.Lp3.AcDisgCapyTotl": 2518, "tfStzgtian1.EMS.PCS1.Lp3.AcChagCapyTotl": 1615,
                      "tfStzgtian1.EMS.PCS1.Lp4.AcDisgCapyTotl": 2585, "tfStzgtian1.EMS.PCS1.Lp4.AcChagCapyTotl": 1634,
                      "tfStzgtian2.EMS.PCS2.Lp1.AcDisgCapyTotl": 2366, "tfStzgtian2.EMS.PCS2.Lp1.AcChagCapyTotl": 1589,
                      "tfStzgtian2.EMS.PCS2.Lp2.AcDisgCapyTotl": 1924, "tfStzgtian2.EMS.PCS2.Lp2.AcChagCapyTotl": 1452,
                      "tfStzgtian2.EMS.PCS2.Lp3.AcDisgCapyTotl": 2453, "tfStzgtian2.EMS.PCS2.Lp3.AcChagCapyTotl": 1669,
                      "tfStzgtian2.EMS.PCS2.Lp4.AcDisgCapyTotl": 2486, "tfStzgtian2.EMS.PCS2.Lp4.AcChagCapyTotl": 1524,
                      "tfStzgtian3.EMS.PCS3.Lp1.AcDisgCapyTotl": 2056, "tfStzgtian3.EMS.PCS3.Lp1.AcChagCapyTotl": 1592,
                      "tfStzgtian3.EMS.PCS3.Lp2.AcDisgCapyTotl": 5359, "tfStzgtian3.EMS.PCS3.Lp2.AcChagCapyTotl": 5970,
                      "tfStzgtian3.EMS.PCS3.Lp3.AcDisgCapyTotl": 0, "tfStzgtian3.EMS.PCS3.Lp3.AcChagCapyTotl": 1538,
                      "tfStzgtian3.EMS.PCS3.Lp4.AcDisgCapyTotl": 2211, "tfStzgtian3.EMS.PCS3.Lp4.AcChagCapyTotl": 1584,
                      "tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl": 2094, "tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyTotl": 2323,
                      "tfStzgtian4.EMS.PCS4.Lp2.AcDisgCapyTotl": 2161, "tfStzgtian4.EMS.PCS4.Lp2.AcChagCapyTotl": 1792,
                      "tfStzgtian4.EMS.PCS4.Lp3.AcDisgCapyTotl": 2379, "tfStzgtian4.EMS.PCS4.Lp3.AcChagCapyTotl": 1562,
                      "tfStzgtian4.EMS.PCS4.Lp4.AcDisgCapyTotl": 2515, "tfStzgtian4.EMS.PCS4.Lp4.AcChagCapyTotl": 1899}
heartContinue = int(model_config.get('broker', "heart_continue"))
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']

class MainFrameCenterIntetface(BaseHandler):
    '''
    首页中间 + 左侧上两部分
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
        # if 1:
            db = self.get_argument('db', 'his')
            key_ = self.get_argument('key', '')
            lang = self.get_argument('lang', None)  # 英文网址
            logging.info('db:%s' % db)
            if db in exclude_station:
                if db == 'dongmu':
                    F, data = _getDataMain_1(db, lang)
                elif db not in station:
                    F, data = _getDataMain_2(db, lang,key_)
                else:
                    F, data = _getDataMain(db, lang)
            else:
                F, data = _getProgramsDataNormal(db, lang, key_)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


class SystemMonitorIntetface(BaseHandler):
    '''
    系统监控页
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
        # if 1:
            db = self.get_argument('db', 'his')
            key_ = self.get_argument('key', '')
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            lang = self.get_argument('lang', None)  # 英文网址
            logging.info('db:%s,statusF:%s' % (db, statusF))
            if db in exclude_station:
                if db == 'dongmu':
                    F, data = _getDataByPageId_1(pageData_sys[db], statusF, db, lang)
                elif db == 'houma' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                    F, data = _getDataByPageId(key_, statusF, db, lang)
                else:
                    F, data = _getDataByPageId(pageData_sys[db], statusF, db, lang)
            else:
                F, data = _getDataByPageIdNormal(key_, statusF, db, lang)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


class BatMonitorIntetface(BaseHandler):
    '''
    电池监控界面3
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            # if 1:
            page = self.get_argument('page', 5)  # 具体电池仓
            key_ = self.get_argument('key', '')
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            db = self.get_argument('db', 'his')
            lang = self.get_argument('lang', None)  # 英文网址
            logging.info('page:%s,statusF:%s,db:%s' % (page, statusF, db))
            if not page:
                if lang == 'en':
                    return self.customError("The entry is incomplete")
                else:
                    return self.customError("入参不完整")
            if not bit_obj:
                bits = user_session.query(Bit).all()
                for bit in bits:
                    bit_obj[bit.id] = bit.name
            if db in exclude_station:
                if db == 'dongmu':
                    id_1 = 0
                    if 92 <= int(page) <= 98:
                        id_1 = int(page) - 81  # 东睦状态量id
                    if int(page) == 91:
                        F, data = _getDataByPageId_3(page, statusF, db, id_1, lang)
                    else:
                        F, data = _getDataByPageId_2(page, statusF, db, id_1, lang)
                elif db == 'houma' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                    F, data = _getDataByPageId(key_, statusF, db, lang)
                else:
                    F, data = _getDataByPageId(page, statusF, db, lang)
            else:
                F, data = _getDataByPageIdNormal(key_, statusF, db, lang)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            mqtt_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
            mqtt_session.close()


class PcsMonitorIntetface(BaseHandler):
    '''
    PCS监控界面
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            # if 1:
            db = self.get_argument('db', 'his')
            key_ = self.get_argument('key', '')
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            lang = self.get_argument('lang', None)  # 英文网址
            if not bit_obj:
                bits = user_session.query(Bit).all()
                for bit in bits:
                    bit_obj[bit.id] = bit.name
            logging.info('db:%s,statusF:%s' % (db, statusF))
            if db in exclude_station:
                if db == 'dongmu':
                    F, data = _getDataByPageId_3(pageData_pcs[db], statusF, db, lang)
                elif db == 'houma' or db == 'guizhou' or db=='ygqn' or db == 'shgyu':
                    F, data = _getDataByPageId(key_, statusF, db, lang)
                else:
                    F, data = _getDataByPageId(pageData_pcs[db], statusF, db, lang)
            else:
                F, data = _getDataByPageIdNormal(key_, statusF, db, lang)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            mqtt_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
            mqtt_session.close()

class StatusBitIntetface(BaseHandler):
    '''
    状态量具体每位的值
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            # if 1:
            id = self.get_argument('id', None)  # 具体电池仓
            db = self.get_argument('db', 'his')
            lang = self.get_argument('lang', None)  # 英文
            if DEBUG:
                logging.info('id:%s' % id)
            if not id:
                if lang == 'en':
                    return self.customError("Invalid parameter")
                else:
                    return self.customError("无效参数")
            if db == 'dongmu':
                # 状态量实时值
                v2 = real_data('status', 'dongmu', 'db')
                if v2:
                    e2 = v2['body']
                status_data = mqtt_session.query(StatusPT).filter(StatusPT.id == id).first()
                status_bits_data = mqtt_session.query(StatusBitsPT).filter(StatusBitsPT.status_id == id).group_by(
                    StatusBitsPT.bits).all()
                data = []
                for i in e2:
                    if status_data.descr[:4] == i['device']:
                        f = -1
                        bin_ST = bin(int(i[status_data.name]))[2:].zfill(16)[::-1]  # 状态量里状态字的ST值
                        for b in status_bits_data:
                            f += 1
                            ob = {'name': b.name}
                            ob['descr'] = b.descr
                            ob['value'] = int(bin_ST[f]) + 1
                            ob['bit'] = 'bit%s' % (b.bits)
                            if bin_ST[f] == '0':
                                if lang == 'en':
                                    ob['valueDescr'] = b.en_desc_off
                                else:
                                    ob['valueDescr'] = b.desc_off
                            if bin_ST[f] == '1':
                                if lang == 'en':
                                    ob['valueDescr'] = b.en_desc_on
                                else:
                                    ob['valueDescr'] = b.desc_on
                            data.append(ob)
            else:
                data = []
                pageData = user_session.query(PageData).filter(PageData.id == id).first()
                if pageData:
                    if pageData.bit_pageData:
                        bit_names = pageData.bit_pageData.name.split('#')
                        name = pageData.name.split('#')[0].split('.')[:-1]
                        p_name = '.'.join(name)
                        for n in bit_names:
                            name = '%s.%s' % (p_name, n)
                            ob = {'name': name}
                            sbean = real_data('status', name, pageData.mode)
                            ob['descr'] = sbean['desc']
                            ob['value'] = sbean['value']
                            ob['valueDescr'] = _value_desc_fanyi(lang, sbean['valueDesc'], status_data_obj)
                            ob['bit'] = 'bit%s' % (bit_names.index(n))
                            data.append(ob)
                    elif pageData.method == 'selfbit':
                        names = pageData.name.split("#")
                        for name in names:
                            ob = {'name': name}
                            sbean = real_data('status', name, pageData.mode)
                            ob['descr'] = sbean['desc']
                            ob['value'] = sbean['value']
                            ob['valueDescr'] = _value_desc_fanyi(lang, sbean['valueDesc'], status_data_obj)
                            ob['bit'] = 'bit%s' % (names.index(name))
                            data.append(ob)
            return self.returnTypeSuc(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


def _getDataMain(db, lang):
    '''
    处理首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.name.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]
    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0], 'bams_gz': [1, '电池堆故障', 0, '无故障'], 'bams_bj': [1, '电池堆报警', 0, '无报警'],
                   'pcs_gz': [1, 'pcs故障', 0, '无故障'], 'pcs_bj': [1, 'pcs报警', 0, '无报警'],
                   'SysMdChag': [0, '充电状态', 0, '未充电'], 'SysMdDisg': [0, '放电状态', 0, '未放电']}
            page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                if type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    v = float(m['value']) if m['value'] != '--' else '--'
                    now = timeUtils.nowSecs()  # 当前秒
                    if v != '--':
                        if v > 0:
                            n_v = 1
                        else:
                            n_v = 2
                    else:
                        n_v = '--'
                    if data_name.name in heart_obj.keys():
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] >= heartContinue:
                            obj[return_key] = [n_v, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [n_v, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [num_retain(m['value'], 1) + unit, data_name.descr, 0, '']

                elif type_name == 'measure' and method == '+':  # 测量量相加
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m1['value'] == '--' or m2['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']
                    else:
                        m = m1['value'] + m2['value'] / 1000
                        obj[return_key] = [num_retain(m, 1) + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'pow':  # 测量量求开3次方
                    obj[return_key] = [num_retain(_getMeasurePowByNames(data_name.name, data_name.mode), 1) + unit,
                                       data_name.descr, 0, '']

                elif type_name == 'measure' and method == '/':  # 测量量计算效率
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m1['value'] == '--' or m2['value'] == '--':
                        obj[return_key] = ['--', data_name.descr, 0, '']
                    else:
                        if m2['value'] != 0:
                            if names[0] in station_base_value.keys():
                                v1 = m1['value'] - station_base_value[names[0]]
                                v2 = m2['value'] - station_base_value[names[1]]
                                effi = v1 / v2 if v1 / v2 < 1 else 1
                            else:
                                effi = m1['value'] / m2['value'] if m1['value'] / m2['value'] < 1 else 1
                            obj[return_key] = [num_retain(effi, 2), data_name.descr, 0, '']
                        else:
                            obj[return_key] = ['0.00', data_name.descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    v = d['value']  # 当前值
                    obj[return_key] = [v, data_name.descr, 0,
                                       _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                    if 'tfStygzhen' in data_name.name or 'tfStzgtian' in data_name.name:  # 永臻/中天、离散量判断充放电状态  [电池簇维护|放电|充电]
                        if v == 1:
                            obj['SysMdChag'] = [1, '充电状态', 0, '充电']
                        elif v == 2:
                            obj['SysMdDisg'] = [1, '放电状态', 0, '放电']

                elif type_name == 'status' and not method:  # 状态量实时值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0,
                                       _value_desc_fanyi(lang, s['valueDesc'], status_data_obj)]

                if 'tfStbodian' in data_name.name:
                    if return_key == 'pcs_bj' or return_key == 'pcs_gz':  # 保电  tfStbodian1.Pcs.SysRunSt [停机|待机|故障|充电|放电|充电降额|放电降额]
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 2:  # 只判断故障
                            obj['pcs_gz'] = [2, 'pcs故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            obj['pcs_bj'] = [2, 'pcs报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                        elif d['value'] == 3:
                            obj['SysMdChag'] = [1, '充电状态', 0, _value_desc_fanyi(lang, '充电', discrete_data_obj)]
                        elif d['value'] == 4:
                            obj['SysMdDisg'] = [1, '放电状态', 0, _value_desc_fanyi(lang, '放电', discrete_data_obj)]
                        else:
                            obj['pcs_gz'] = [1, 'pcs故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['pcs_bj'] = [1, 'pcs报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]

                    elif return_key == 'bams_bj' or return_key == 'bams_gz':  # 保电
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 3:  # 报警
                            obj['bams_gz'] = [1, '电池堆故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['bams_bj'] = [2, '电池堆报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                        elif d['value'] == 4:  # 故障
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            obj['bams_bj'] = [1, '电池堆报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]
                        else:
                            obj['bams_gz'] = [1, '电池堆故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['bams_bj'] = [1, '电池堆报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]

                if data_name.name.endswith('Sys_SOC'):  # 以soc结尾计算告警和故障

                    for n in bams_gz.split('#'):  # 电池故障
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_gz2.split('#'):  # 电池故障2
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_bj.split('#'):  # 电池报警
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_bj'] = [2, '电池堆报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                            break

                    name = data_name.name.replace('Sys_SOC', 'InvtOallStat')  # PCS故障 告警
                    d = real_data('discrete', name, data_name.mode)
                    val = d['value']
                    if val == 2:
                        obj['pcs_gz'] = [2, 'pcs故障', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue
                    elif val == 1:
                        obj['pcs_bj'] = [2, 'pcs报警', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue

            data.append(obj)

    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    page_data_all = user_session.query(PageData).filter(PageData.page_id == pageData_first[db], PageData.is_use == 1,
                                                        PageData.return_key == 'onlineall').all()

    page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db], PageData.is_use == 1,
                                                           PageData.return_key == 'online').all()
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()
    to = len(page_data_status)
    # arar = user_session.query(Bit.name).filter(Bit.id==3).first()
    left1['total'] = to
    left1['page_area'] = bj_page_data_status[0].page_area
    i, y = 0, 0
    for stat in page_data_status:
        ind = page_data_status.index(stat)
        if real_data('status', page_data_all[ind].name, page_data_all[ind].mode)['value'] == 2:
            sname = stat.name
            ss = real_data('status', sname, stat.mode)
            val = ss['value']
            if val == 2:  # 在线
                y = y + 1;

            if '#' in bj_page_data_status[ind].name:  # 永臻
                na = bj_page_data_status[ind].name.split('#')[0].replace('Sw4OnleMd1', 'Sw2Alarm')
                s = real_data('status', na, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            elif 'baodian' == db:  # 广州保电
                name = bj_page_data_status[ind].name.replace('_Pcs.Online', "Pcs.SysRunSt")
                s = real_data('discrete', name, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            else:
                for n in bams_bj.split('#'):
                    name = bj_page_data_status[ind].name.replace('SysMdOffline', n)
                    s = real_data('status', name, stat.mode)
                    val = s['value']
                    if val == 2:
                        i = i + 1;
                        break
    left1['online'] = y
    left1['outline'] = to - y
    left1['bj'] = i
    left1['lv'] = num_retain(float(y) / to * 100)
    data.append(left1)
    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db,
                                      Event.type == 2,
        or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).group_by(Event.type_id).order_by(
        Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, Event.station == db,
                                               Event.type == 2,
                                               or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).order_by(
        AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " " + _value_desc_fanyi(lang, '报警', status_data_obj)
        else:
            point = point + " " + _value_desc_fanyi(lang, '已恢复', status_data_obj)
        d.append({'ts': alarm['ts'], 'alarm_descr': _value_desc_fanyi(lang, alarm['alarm_descr'], status_data_obj),
                  'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data

def _getDataMain_2(db, lang,key_):
    '''
    处理首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == key_,PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.name.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]
    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0], 'bams_gz': [1, '电池堆故障', 0, '无故障'], 'bams_bj': [1, '电池堆报警', 0, '无报警'],
                   'pcs_gz': [1, 'pcs故障', 0, '无故障'], 'pcs_bj': [1, 'pcs报警', 0, '无报警'],
                   'SysMdChag': [0, '充电状态', 0, '未充电'], 'SysMdDisg': [0, '放电状态', 0, '未放电']}
            page_data = user_session.query(PageData).filter(PageData.page_id == key_,
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                if type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    v = float(m['value']) if m['value'] != '--' else '--'
                    if v != '--':
                        if v>0:
                            n_v = 1
                        else:
                            n_v = 2
                    else:
                        n_v = '--'
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] >= heartContinue:
                            obj[return_key] = [n_v, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [n_v, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] != '--':
                        obj[return_key] = [num_retain(m['value'], 1) + unit, data_name.descr, 0, '']
                    else:
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']

                elif type_name == 'measure' and method == '+':  # 测量量相加
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m1['value'] == '--' or m2['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']
                    else:
                        m = m1['value'] + m2['value'] / 1000
                        obj[return_key] = [num_retain(m, 1) + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'pow':  # 测量量求开3次方
                    obj[return_key] = [num_retain(_getMeasurePowByNames(data_name.name, data_name.mode), 1) + unit,
                                       data_name.descr, 0, '']

                elif type_name == 'measure' and method == '/':  # 测量量计算效率
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m1['value'] == '--' or m2['value'] == '--':
                        obj[return_key] = ['--', data_name.descr, 0, '']
                    else:
                        if m2['value'] != 0:
                            if names[0] in station_base_value.keys():
                                v1 = m1['value'] - station_base_value[names[0]]
                                v2 = m2['value'] - station_base_value[names[1]]
                                effi = v1 / v2 if v1 / v2 < 1 else 1
                            else:
                                effi = m1['value'] / m2['value'] if m1['value'] / m2['value'] < 1 else 1
                            obj[return_key] = [num_retain(effi, 2), data_name.descr, 0, '']
                        else:
                            obj[return_key] = ['0.00', data_name.descr, 0, '']

                elif type_name == 'measure' and method == '——':
                    obj[return_key] = ['——', data_name.descr, 0, '']


                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    v = d['value']  # 当前值
                    obj[return_key] = [v, data_name.descr, 0,
                                       _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                    if 'tfStygzhen' in data_name.name or 'tfStzgtian' in data_name.name or 'tc_datong' in data_name.name:  # 永臻/中天、离散量判断充放电状态  [电池簇维护|放电|充电]
                        if v == 1:
                            obj['SysMdChag'] = [1, '充电状态', 0, '充电']
                        elif v == 2:
                            obj['SysMdDisg'] = [1, '放电状态', 0, '放电']
                    elif 'guizhou' in data_name.name:
                        if v == 4:
                            obj['SysMdChag'] = [1, '充电状态', 0, '充电']
                        elif v == 5:
                            obj['SysMdDisg'] = [1, '放电状态', 0, '放电']

                elif type_name == 'status' and method == 'online':
                    d = real_data('discrete', data_name.name, data_name.mode)
                    v = d['value']  # 当前值
                    if v == 2:
                        obj['SysMdOffline'] = [1, '在线', 0, '在线']
                    else:
                        obj['SysMdIdle'] = [1, '空闲', 0, '空闲']

                elif type_name == 'status' and not method:  # 状态量实时值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0,
                                       _value_desc_fanyi(lang, s['valueDesc'], status_data_obj)]

                if 'tfStbodian' in data_name.name:
                    if return_key == 'pcs_bj' or return_key == 'pcs_gz':  # 保电  tfStbodian1.Pcs.SysRunSt [停机|待机|故障|充电|放电|充电降额|放电降额]
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 2:  # 只判断故障
                            obj['pcs_gz'] = [2, 'pcs故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            obj['pcs_bj'] = [2, 'pcs报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                        elif d['value'] == 3:
                            obj['SysMdChag'] = [1, '充电状态', 0, _value_desc_fanyi(lang, '充电', discrete_data_obj)]
                        elif d['value'] == 4:
                            obj['SysMdDisg'] = [1, '放电状态', 0, _value_desc_fanyi(lang, '放电', discrete_data_obj)]
                        else:
                            obj['pcs_gz'] = [1, 'pcs故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['pcs_bj'] = [1, 'pcs报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]

                    elif return_key == 'bams_bj' or return_key == 'bams_gz':  # 保电
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 3:  # 报警
                            obj['bams_gz'] = [1, '电池堆故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['bams_bj'] = [2, '电池堆报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                        elif d['value'] == 4:  # 故障
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            obj['bams_bj'] = [1, '电池堆报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]
                        else:
                            obj['bams_gz'] = [1, '电池堆故障', 0, _value_desc_fanyi(lang, '无故障', discrete_data_obj)]
                            obj['bams_bj'] = [1, '电池堆报警', 0, _value_desc_fanyi(lang, '无报警', discrete_data_obj)]

                if data_name.name.endswith('Sys_SOC'):  # 以soc结尾计算告警和故障

                    for n in bams_gz.split('#'):  # 电池故障
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_gz2.split('#'):  # 电池故障2
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_bj.split('#'):  # 电池报警
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_bj'] = [2, '电池堆报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                            break

                    name = data_name.name.replace('Sys_SOC', 'InvtOallStat')  # PCS故障 告警
                    d = real_data('discrete', name, data_name.mode)
                    val = d['value']
                    if val == 2:
                        obj['pcs_gz'] = [2, 'pcs故障', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue
                    elif val == 1:
                        obj['pcs_bj'] = [2, 'pcs报警', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue

            data.append(obj)
    data = natsorted(data, key=lambda x: x['page_area'], reverse=False)
    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    page_data_all = user_session.query(PageData).filter(PageData.page_id == key_, PageData.is_use == 1,
                                                        PageData.return_key == 'onlineall').all()

    page_data_status = user_session.query(PageData).filter(PageData.page_id == key_, PageData.is_use == 1,
                                                           PageData.return_key == 'online').all()
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == key_,
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()
    to = len(page_data_status)
    # arar = user_session.query(Bit.name).filter(Bit.id==3).first()
    left1['total'] = to
    left1['page_area'] = bj_page_data_status[0].page_area
    i, y = 0, 0
    for stat in page_data_status:
        ind = page_data_status.index(stat)
        if real_data('status', page_data_all[ind].name, page_data_all[ind].mode)['value'] == 2:
            sname = stat.name
            ss = real_data('status', sname, stat.mode)
            val = ss['value']
            if val == 2:  # 在线
                y = y + 1;

            if '#' in bj_page_data_status[ind].name:
                na = bj_page_data_status[ind].name.split('#')[0].replace('Sw4OnleMd1', 'Sw2Alarm')
                s = real_data('status', na, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            elif 'baodian' == db:  # 广州保电
                name = bj_page_data_status[ind].name.replace('_Pcs.Online', "Pcs.SysRunSt")
                s = real_data('discrete', name, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            else:
                for n in bams_bj.split('#'):
                    name = bj_page_data_status[ind].name.replace('SysMdOffline', n)
                    s = real_data('status', name, stat.mode)
                    val = s['value']
                    if val == 2:
                        i = i + 1;
                        break
    left1['online'] = y
    left1['outline'] = to - y
    left1['bj'] = i
    left1['lv'] = num_retain(float(y) / to * 100)
    data.append(left1)
    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db,
                                      Event.type == 2,
        or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).group_by(Event.type_id).order_by(
        Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, Event.station == db,
                                               Event.type == 2,
                                               or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).order_by(
        AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " " + _value_desc_fanyi(lang, '报警', status_data_obj)
        else:
            point = point + " " + _value_desc_fanyi(lang, '已恢复', status_data_obj)
        d.append({'ts': alarm['ts'], 'alarm_descr': _value_desc_fanyi(lang, alarm['alarm_descr'], status_data_obj),
                  'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data


def _getDataMain_1(db, lang):
    '''
    处理东睦首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.page_area.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']

    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']

    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']
    nnn = 0
    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0], 'bms_gz': [1, '电池堆故障', 0, '无故障'], 'bms_bj': [1, '电池堆报警', 0, '无报警'],
                   'pcs_gz': [1, 'pcs故障', 0, '无故障'], 'pcs_bj': [1, 'pcs报警', 0, '无报警']}
            page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                for i in e1:

                    if type_name == 'measure' and method == '/':  # 测量量计算效率,视在功率
                        names = data_name.name.split('#')
                        m1 = names[0]
                        m2 = names[1]
                        if data_name.device == i['device']:
                            if float(i[m2]) != 0 and m1 == 'dcapd':
                                effi = float(i[m1]) / float(i[m2]) if float(i[m1]) / float(i[m2]) < 1 else 1
                                obj[return_key] = [num_retain(effi, 3), data_name.descr, 0, '']
                            elif float(i[m2]) != 0:
                                effi = math.sqrt(float(i[m1]) * float(i[m1]) + float(i[m2]) * float(i[m2]))
                                obj[return_key] = [num_retain(effi, 3), data_name.descr, 0, '']
                            else:
                                obj[return_key] = ['0.000', data_name.descr, 0, '']
                    elif type_name == 'measure' and not method:  # 测量量具体值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                for i in e2:
                    if type_name == 'status' and not method:  # 状态量实时值
                        # if i['device'] == 'ReCon':
                        #     obj[return_key] = [2, data_name.descr, 0, '']
                        if data_name.device == i['device']:
                            if data_name.device[:3] == 'PCS':
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()

                                bin_aaa = bit_list_n[13]
                                if bin_aaa == '0':
                                    obj['PCSM'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                                elif bin_aaa == '1':
                                    now_time = timeUtils.getNewTimeStr()
                                    now_time_ = timeUtils.timeStrToTamp(now_time)
                                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                    if time_ss > 125:
                                        obj['PCSM'] = [1, '交流断路器位置', 0, '']
                                    if time_ss <= 125:
                                        obj['PCSM'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                                bit_n = '{:016b}'.format(int(i['ST4']))  # 转成2进制，高位补零
                                bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                bit_list.reverse()

                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()

                                if bit_list[6] == '1':  # pcs
                                    obj['Chargstate'] = [int(bit_list[6]) + 1, '充电状态', 0,
                                                         _value_desc_fanyi(lang, '合状态', status_data_obj)]  # pcs状态

                                elif bit_list[6] == '0':  # pcs
                                    obj['Chargstate'] = [int(bit_list[6]) + 1, '充电状态', 0,
                                                         _value_desc_fanyi(lang, '未充电', status_data_obj)]  # pcs状态

                                if bit_list[7] == '1':  # pcs
                                    obj['dischstate'] = [int(bit_list[7]) + 1, '放电状态', 0,
                                                         _value_desc_fanyi(lang, '合状态', status_data_obj)]  # pcs状态

                                elif bit_list[7] == '0':  # pcs
                                    obj['dischstate'] = [int(bit_list[7]) + 1, '放电状态', 0,
                                                         _value_desc_fanyi(lang, '未放电', status_data_obj)]  # pcs状态

                                if bit_list_n[14] == '1':  # pcs告警.故障（取同一个值）
                                    obj['pcs_gz'] = [int(bit_list_n[14]) + 1, 'pcs报警', 0,
                                                     _value_desc_fanyi(lang, '报警', status_data_obj)]  # pcs状态
                                    obj['pcs_bj'] = [int(bit_list_n[14]) + 1, 'pcs故障', 0,
                                                     _value_desc_fanyi(lang, '故障', status_data_obj)]  # pcs状态
                                elif bit_list_n[14] == '0':  # pcs告警.故障（取同一个值）
                                    obj['pcs_gz'] = [int(bit_list_n[14]) + 1, 'pcs报警', 0,
                                                     _value_desc_fanyi(lang, '无报警', status_data_obj)]  # pcs状态
                                    obj['pcs_bj'] = [int(bit_list_n[14]) + 1, 'pcs故障', 0,
                                                     _value_desc_fanyi(lang, '无故障', status_data_obj)]  # pcs状态
                            if data_name.device[:3] == 'BMS':
                                bit_c = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                                bit_list_c.reverse()
                                # bin_ccc = bin(int(i['ST1']))[2:].zfill(16)[::-1]
                                if bit_list_c[4] == '1':  # bms告警.故障
                                    obj['bms_gz'] = [int(bit_list_c[4]) + 1, '电池堆报警', 0,
                                                     _value_desc_fanyi(lang, '报警', status_data_obj)]

                                elif bit_list_c[4] == '0':  # bms告警.故障
                                    obj['bms_gz'] = [int(bit_list_c[4]) + 1, '电池堆报警', 0,
                                                     _value_desc_fanyi(lang, '无报警', status_data_obj)]

                                if bit_list_c[3] == '1':  # bms告警.故障

                                    obj['bms_bj'] = [int(bit_list_c[3]) + 1, '电池堆故障', 0,
                                                     _value_desc_fanyi(lang, '故障', status_data_obj)]
                                elif bit_list_c[3] == '0':  # bms告警.故障

                                    obj['bms_bj'] = [int(bit_list_c[3]) + 1, '电池堆故障', 0,
                                                     _value_desc_fanyi(lang, '无故障', status_data_obj)]
                for i in e3:
                    if type_name == 'cumulant' and method == '/':  # 累积量计算效率
                        names = data_name.name.split('#')
                        m1 = names[0]
                        m2 = names[1]
                        if i['device'] == data_name.device:
                            if float(i[m2]) != 0 and m1 == 'BDcap':
                                effi = float(i[m1]) / float(i[m2]) if float(i[m1]) / float(i[m2]) < 1 else 1
                                obj['efficiency'] = [num_retain(effi, 3), data_name.descr, 0, '']
                            else:
                                obj['efficiency'] = ['0.00', data_name.descr, 0, '']

            data.append(obj)

    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()

    left1['page_area'] = bj_page_data_status[0].page_area
    s, y, z = 0, 0, 0  # 告警，在线，不在线
    for h in bj_page_data_status:
        if h.type_name == 'status' and not h.method:  # 状态量实时值
            for i in e2:
                if h.device != 'ReCon' and h.device == i['device']:
                    bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                    bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                    bit_list.reverse()
                    # bin_aaa = bin(int(i['ST1']))[2:].zfill(16)[::-1]
                    bin_aaa = bit_list[13]
                    bin_bbb = bit_list[14]

                    if bin_aaa == '0':
                        # obj['accibrepos'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                        z += 1
                    elif bin_aaa == '1':
                        now_time = timeUtils.getNewTimeStr()
                        now_time_ = timeUtils.timeStrToTamp(now_time)
                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                        if time_ss > 125:
                            # obj['accibrepos'] = [1, '交流断路器位置', 0, '']
                            z += 1
                        if time_ss <= 125:
                            # obj['accibrepos'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                            y += 1
                    if bin_bbb == '1':
                        s = s + 1
            # if h.device == 'ReCon':
            #     for i in e2:
            #         if h.device == i['device']:
            #                 y = y + 1
            # else:
            #     for i in e2:
            #         if h.device == i['device']:
            #             # if bin(int(i['ST1']))[::-1].zfill(18)[14] == '1':# 告警
            #             aaa = bin(int(i['ST1']))[2:].zfill(16)[::-1][14]
            #             if aaa == '1':# 告警
            #                 s = s + 1

    left1['total'] = y + z
    left1['online'] = y
    left1['outline'] = z
    left1['bj'] = s
    left1['lv'] = num_retain(float(y) / float(y + z) * 100)
    data.append(left1)

    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db, Event.type == 2,
        or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).group_by(Event.type_id).order_by(
        Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, Event.station == db,
                                               Event.type == 2,
                                               or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).order_by(
        AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " " + _value_desc_fanyi(lang, d['报警'], status_data_obj)
        else:
            point = point + " " + _value_desc_fanyi(lang, d['已恢复'], status_data_obj)
        d.append({'ts': alarm['ts'], 'alarm_descr': _value_desc_fanyi(lang, alarm['alarm_descr'], status_data_obj),
                  'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data


def _getDataByPageId(id, statusF, db, lang=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    page_method_1 = user_session.query(PageData).filter(PageData.page_id == id,PageData.station == db,PageData.is_use == 1,or_(PageData.return_key == 'status1',PageData.return_key == 'status2')).order_by(PageData.page_area).all()
    # 单元状态
    obj_unit_list = []
    if page_method_1:
        for area in page_method_1:
            obj_unit = {'page_area': area.page_area}
            if db == 'baodian':
                s = real_data('status', area.name, area.mode)  # 状态量的单个值
                if s['value'] == 1 or s['value'] == 0:
                    if lang == 'en':
                        obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                    else:
                        obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                if area.return_key not in obj_unit.keys():
                    page_method_3 = user_session.query(PageData).filter(PageData.page_area == area.page_area,
                                                                        PageData.return_key == area.return_key,
                                                                        PageData.method == '1', PageData.station == db,
                                                                        PageData.device == 'online',
                                                                        PageData.is_use == 1).order_by(
                        PageData.page_area).first()
                    s1 = real_data('status', page_method_3.name, page_method_3.mode)  # 状态量的单个值
                    if s1['value'] == 1 or s1['value'] == 0:
                        if lang == 'en':
                            obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                        else:
                            obj_unit[page_method_3.return_key] = [2, '是否离线', 0, '离线']
                if area.return_key not in obj_unit.keys():
                    if lang == 'en':
                        obj_unit[area.return_key] = [1, 'Whether it works properly', 0, 'Normal operation']
                    else:
                        obj_unit[area.return_key] = [1, '是否正常运行', 0, '正常运行']
            if db == 'datong':
                s = real_data('discrete', area.name, area.mode)  # 离散量的单个值
                if s['value'] == 2:
                    if lang == 'en':
                        obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                    else:
                        obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                if area.return_key not in obj_unit.keys():
                    page_method_2 = user_session.query(PageData).filter(PageData.page_area == area.page_area,
                                                                        PageData.return_key == area.return_key,
                                                                        PageData.method == '2', PageData.station == db,
                                                                        PageData.is_use == 1).order_by(
                        PageData.page_area).first()
                    if page_method_2:
                        s4 = real_data('status', page_method_2.name, page_method_2.mode)  # 状态量的单个值
                        if s4['value'] == 1:
                            if lang == 'en':
                                obj_unit[area.return_key] = [3, 'Test whether the AC is disconnected', 0,
                                                             'Ac test disconnect']
                            else:
                                obj_unit[page_method_2.return_key] = [3, '交流测是否断开', 0, '交流测断开']
                if area.return_key not in obj_unit.keys():
                    if lang == 'en':
                        obj_unit[area.return_key] = [1, 'Whether it works properly', 0, 'Normal operation']
                    else:
                        obj_unit[area.return_key] = [1, '是否正常运行', 0, '正常运行']
            else:
                s2 = real_data('status', area.name, area.mode)  # 状态量的单个值
                if db == 'houma':
                    if s2['value'] != 2:
                        if lang == 'en':
                            obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                        else:
                            obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                else:
                    if s2['value'] == 1 or s2['value'] == 0:
                        if lang == 'en':
                            obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                        else:
                            obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                if ('status1' not in obj_unit.keys()) or ('status2' not in obj_unit.keys()):
                    if area.device == 'onlineall' and area.method == '1':
                        s3 = real_data('status', area.name, area.mode)  # 状态量的单个值
                        if s3['value'] == 1 or s3['value'] == 0:
                            if lang == 'en':
                                obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                            else:
                                obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                    if area.device == 'online' and area.method == '1':
                        s3 = real_data('status', area.name, area.mode)  # 状态量的单个值
                        if s3['value'] == 1 or s3['value'] == 0:
                            if lang == 'en':
                                obj_unit[area.return_key] = [2, 'Offline or not', 0, 'Offline']
                            else:
                                obj_unit[area.return_key] = [2, '是否离线', 0, '离线']
                if ('status1' not in obj_unit.keys()) or ('status2' not in obj_unit.keys()):
                    if area.method == '2':
                        s4 = real_data('status', area.name, area.mode)  # 状态量的单个值
                        if s4['value'] == 1 or s4['value'] == 0:
                            if lang == 'en':
                                obj_unit[area.return_key] = [3, 'Test whether the AC is disconnected', 0,
                                                             'Ac test disconnect']
                            else:
                                obj_unit[area.return_key] = [3, '交流测是否断开', 0, '交流测断开']
                if 'status1' not in obj_unit.keys():
                    if lang == 'en':
                        obj_unit[area.return_key] = [1, 'Whether it works properly', 0, 'Normal operation']
                    else:
                        obj_unit[area.return_key] = [1, '是否正常运行', 0, '正常运行']
                if 'status2' not in obj_unit.keys():
                    if lang == 'en':
                        obj_unit[area.return_key] = [1, 'Whether it works properly', 0, 'Normal operation']
                    else:
                        obj_unit[area.return_key] = [1, '是否正常运行', 0, '正常运行']

            obj_unit_list.append(obj_unit)
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]
    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).order_by(asc(PageData.id)).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area
    for area in page_area_obj.keys():
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            if lang == 'en':
                if type_name == 'measure' and method == '+':  # 测量量的和
                    v = _getMeasureSumByNames(data_name.name, data_name.mode)
                    if data_name.station == 'ygzhen' and v != '--':
                        v = str(round(float(v) / 1000, 3))
                    obj[return_key] = [v + unit, data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == 'avg':  # 测量量的平均值
                    obj[return_key] = [_getMeasureAvgByNames(data_name.name, data_name.mode) + unit, data_name.en_descr,
                                       0, '']
                elif type_name == 'measure' and method == 'sqrt':  # 两数平方和开平方
                    obj[return_key] = [_getMeasureSqrtByNames(data_name.name, data_name.mode) + unit,
                                       data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == '-':  # 测量量的差值
                    obj[return_key] = [_getMeasureRangeByNames(data_name.name, data_name.mode) + unit,
                                       data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == '/':  # 测量量两个数商
                    obj[return_key] = [_getMeasureSByNames(data_name.name, data_name.mode) + unit, data_name.en_descr,
                                       0, '']
                elif type_name == 'status' and not method:  # 状态量的单个值
                    s = real_data('status', data_name.name, data_name.mode)
                    if lang == 'en':
                        if '空调' not in data_name.descr:
                            valueDesc_s = _value_desc_fanyi(lang, s['valueDesc'], status_data_obj)
                            obj[return_key] = [s['value'], data_name.en_descr, 0, valueDesc_s]
                        else:
                            obj[return_key] = [s['value'], data_name.en_descr, 0, s['valueDesc']]
                    else:
                        obj[return_key] = [s['value'], data_name.en_descr, 0, s['valueDesc']]


                elif type_name == 'status' and method == 'or' and statusF == 0:  # 组合状态量直接返回告警状态
                    obj[return_key] = [_getStatusOrByNames(data_name.name, data_name.mode), data_name.en_descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 4:  # 组合状态量直接值
                    obj[return_key] = [_getStatusValueOrByNames(data_name.name, data_name.mode), data_name.en_descr, 0,
                                       '']
                elif type_name == 'status' and method == 'or' and statusF == 1:  # 组合状态量返回所有当前值
                    obj[return_key] = [_getStatusByNames(data_name.name, data_name.mode), data_name.en_descr, 0, '']

                elif type_name == 'status' and statusF == 3:  # 组合状态量返回二进制数
                    bit_val = 0
                    names = data_name.name.split('#')  # 数据名称集合
                    if len(names) != 16:
                        return False, 'status %s the number of names does not match' % data_name.en_descr
                    if method == 'selfbit':  # 自身就是按0-15位排列
                        for nam in names:
                            ind = names.index(nam)
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    else:
                        if method != 'bit' or not data_name.bit_id:
                            return False, 'status %s misconfiguration' % data_name.en_descr
                        # bit_names = data_name.bit_pageData.name.split('#')  # 获取当前配置的二进制name集合
                        bit_names = bit_obj[data_name.bit_id].split('#')

                        for nam in names:
                            n = nam.split('.')[-1]  # 取点分割后的最后一个名称
                            if n not in bit_names:
                                return False, 'status %s the name configuration does not match' % data_name.en_descr
                            ind = bit_names.index(n)  # 获取名称在二进制中的索引
                            # val = 0
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    obj[return_key] = [bit_val, data_name.en_descr, data_name.id, '']
                elif type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        v = '--'
                    else:
                        v = float(m['value'])
                    now = timeUtils.nowSecs()  # 当前秒
                    if v == '--':
                        v_status = '--'
                    else:
                        v_status = 1 if v > 0 else 2
                    if data_name.name in heart_obj.keys():
                        if lang == 'en':
                            data_name.descr = data_name.en_descr
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] > heartContinue:
                            obj[return_key] = [v_status, data_name.en_descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.en_descr, 0, '']
                    else:
                        obj[return_key] = [v_status, data_name.en_descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.en_descr, 0, '']
                    else:
                        if return_key == 'soc':
                            if m['value'] > 101:
                                m['value'] = 0
                        obj[return_key] = [num_retain(m['value'], 3) + unit, data_name.en_descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    if db == 'houma':
                        d['value'] = d['value'] + 1
                    if lang == 'en':
                        data_name.descr = data_name.en_descr
                    obj[return_key] = [d['value'], data_name.en_descr, 0,
                                       _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
            else:
                if type_name == 'measure' and method == '+':  # 测量量的和
                    v = _getMeasureSumByNames(data_name.name, data_name.mode)
                    if data_name.station == 'ygzhen' and v != '--':
                        v = str(round(float(v) / 1000, 3))
                    obj[return_key] = [v + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'avg':  # 测量量的平均值
                    obj[return_key] = [_getMeasureAvgByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                       '']
                elif type_name == 'measure' and method == 'sqrt':  # 两数平方和开平方
                    obj[return_key] = [_getMeasureSqrtByNames(data_name.name, data_name.mode) + unit, data_name.descr,
                                       0, '']
                elif type_name == 'measure' and method == '-':  # 测量量的差值
                    obj[return_key] = [_getMeasureRangeByNames(data_name.name, data_name.mode) + unit, data_name.descr,
                                       0, '']
                elif type_name == 'measure' and method == '/':  # 测量量两个数商
                    obj[return_key] = [_getMeasureSByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                       '']
                elif type_name == 'status' and not method:  # 状态量的单个值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0, s['valueDesc']]

                elif type_name == 'status' and method == 'or' and statusF == 0:  # 组合状态量直接返回告警状态
                    obj[return_key] = [_getStatusOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 4:  # 组合状态量直接值
                    obj[return_key] = [_getStatusValueOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 1:  # 组合状态量返回所有当前值
                    obj[return_key] = [_getStatusByNames(data_name.name, data_name.mode), data_name.descr, 0, '']

                elif type_name == 'status' and statusF == 3:  # 组合状态量返回二进制数
                    bit_val = 0
                    names = data_name.name.split('#')  # 数据名称集合
                    if len(names) != 16:
                        return False, 'status %s 名称个数不匹配' % data_name.descr
                    if method == 'selfbit':  # 自身就是按0-15位排列
                        for nam in names:
                            ind = names.index(nam)
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    else:
                        if method != 'bit' or not data_name.bit_id:
                            return False, 'status %s 配置错误' % data_name.descr
                        # bit_names = data_name.bit_pageData.name.split('#')  # 获取当前配置的二进制name集合
                        bit_names = bit_obj[data_name.bit_id].split('#')
                        for nam in names:
                            n = nam.split('.')[-1]  # 取点分割后的最后一个名称
                            if n not in bit_names:
                                return False, 'status %s 名称配置不匹配' % data_name.descr
                            ind = bit_names.index(n)  # 获取名称在二进制中的索引
                            # val = 0
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    obj[return_key] = [bit_val, data_name.descr, data_name.id, '']
                elif type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        v = '--'
                        v_status = '--'
                    else:
                        v = float(m['value'])
                        v_status = 1 if v > 0 else 2
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        data_name.descr = data_name.descr
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] > heartContinue:
                            obj[return_key] = [v_status, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [v_status, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']
                    else:
                        if return_key == 'soc':
                            if m['value'] > 101:
                                m['value'] = 0
                        obj[return_key] = [num_retain(m['value'], 3) + unit, data_name.descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    if db == 'houma':
                        d['value'] = d['value'] + 1
                    data_name.descr = data_name.descr
                    obj[return_key] = [d['value'], data_name.descr, 0, d['valueDesc']]

        data.append(obj)
    for o in data:
        for oo in obj_unit_list:
            if o['page_area'] == oo['page_area']:
                o.update(oo)
    return True, data


def _getDataByPageId_1(id, statusF, db, lang=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']
    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']
    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']
    # page_method_1 = user_session.query(PageData).filter(PageData.method=='1', PageData.station == db,PageData.is_use == 1).order_by(PageData.page_area).all()
    page_method_2 = user_session.query(PageData).filter(PageData.method == '2', PageData.station == db,
                                                        PageData.is_use == 1).order_by(PageData.page_area).all()
    # 单元状态
    # print (page_method)
    obj_unit_list = []
    if page_method_2:
        for area_2 in page_method_2:
            obj_unit = {'page_area': area_2.page_area}
            for i in e2:
                if area_2.device == i['device']:
                    now_time = timeUtils.getNewTimeStr()
                    now_time_ = timeUtils.timeStrToTamp(now_time)
                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                    status_data_PCS = mqtt_session.query(StatusPT.id).filter(StatusPT.descr.like(i['device'] + '%'),
                                                                             StatusPT.name == 'ST1').first()
                    status_bits_data = mqtt_session.query(StatusBitsPT).filter(
                        StatusBitsPT.status_id == status_data_PCS[0]).group_by(StatusBitsPT.bits).all()
                    bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                    bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                    bit_list.reverse()
                    for b in status_bits_data:
                        if b.name == 'accibrepos':
                            if int(bit_list[13]) == 0:
                                if lang == 'en':
                                    obj_unit[area_2.return_key] = [2, 'Offline or not', 0, 'Offline']
                                else:
                                    obj_unit[area_2.return_key] = [2, '是否离线', 0, '离线']
                            elif int(bit_list[13]) == 1:
                                if time_ss > 125:
                                    if lang == 'en':
                                        obj_unit[area_2.return_key] = [2, '是否离线', 0, 'Offline']
                                    else:
                                        obj_unit[area_2.return_key] = [2, '是否离线', 0, '离线']
            if area_2.return_key not in obj_unit.keys():
                if lang == 'en':
                    obj_unit[area_2.return_key] = [1, 'Whether it works properly', 0, 'Normal operation']
                else:
                    obj_unit[area_2.return_key] = [1, '是否正常运行', 0, '正常运行']
            obj_unit_list.append(obj_unit)
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]
    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area

    for area in page_area_obj.keys():
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            if lang == 'en':
                for i in e1:
                    if type_name == 'measure' and not method:  # 测量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.en_descr, 0, '']
                for i in e2:
                    if type_name == 'status' and statusF == 0:  # 组合状态量返回二进制数
                        if data_name.device == i['device']:
                            if i['device'] == 'ReCon':
                                obj[return_key] = [int(i[data_name.name]) + 1, data_name.en_descr, 0, '']
                            else:
                                status_data_PCS = mqtt_session.query(StatusPT.id).filter(
                                    StatusPT.descr.like(i['device'] + '%'), StatusPT.name == 'ST1').first()
                                status_bits_data = mqtt_session.query(StatusBitsPT).filter(
                                    StatusBitsPT.status_id == status_data_PCS[0]).group_by(StatusBitsPT.bits).all()
                                bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                bit_list.reverse()
                                for b in status_bits_data:
                                    if b.name == 'runnsta':
                                        if int(bit_list[6]) == 0:
                                            obj['runnsta'] = [int(bit_list[6]) + 1, 'Running state', 0,
                                                              _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        elif int(bit_list[6]) == 1:
                                            obj['runnsta'] = [int(bit_list[6]) + 1, 'Running state', 0,
                                                              _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'Shutdsta':
                                        if int(bit_list[7]) == 0:
                                            obj['Shutdsta'] = [int(bit_list[7]) + 1, 'Shutdown state', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[7]) == 1:
                                            obj['Shutdsta'] = [int(bit_list[7]) + 1, 'Shutdown state', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'standsta':
                                        if int(bit_list[8]) == 0:
                                            obj['standsta'] = [int(bit_list[8]) + 1, 'Standby mode', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[8]) == 1:
                                            obj['standsta'] = [int(bit_list[8]) + 1, 'Standby mode', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'readystate':
                                        if int(bit_list[9]) == 0:
                                            obj['readystate'] = [int(bit_list[9]) + 1, 'Ready state', 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[9]) == 1:
                                            obj['readystate'] = [int(bit_list[9]) + 1, 'Ready state', 0,
                                                                 _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'emeshutd':
                                        if int(bit_list[10]) == 0:
                                            obj['emeshutd'] = [int(bit_list[10]) + 1, 'Emergency shutdown', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[10]) == 1:
                                            obj['emeshutd'] = [int(bit_list[10]) + 1, 'Emergency shutdown', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'dccibrepos':
                                        if int(bit_list[11]) == 0:
                                            obj['dccibrepos'] = [int(bit_list[11]) + 1, 'Dc circuit breaker position',
                                                                 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[11]) == 1:
                                            obj['dccibrepos'] = [int(bit_list[11]) + 1, 'Dc circuit breaker position',
                                                                 0, _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'accibrepos':
                                        now_time = timeUtils.getNewTimeStr()
                                        now_time_ = timeUtils.timeStrToTamp(now_time)
                                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                        if int(bit_list[13]) == 0:
                                            obj['accibrepos'] = [int(bit_list[13]) + 1, 'Ac circuit breaker position',
                                                                 0, '']
                                        elif int(bit_list[13]) == 1:
                                            if time_ss > 125:
                                                obj['accibrepos'] = [1, 'Ac circuit breaker position', 0, '']
                                            if time_ss <= 125:
                                                obj['accibrepos'] = [int(bit_list[13]) + 1,
                                                                     'Ac circuit breaker position', 0, '']
                                    if b.name == 'TotalAlarm':
                                        if int(bit_list[14]) == 0:
                                            obj['TotalAlarm'] = [int(bit_list[14]) + 1, 'Alarm total', 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        elif int(bit_list[14]) == 1:
                                            obj['TotalAlarm'] = [int(bit_list[14]) + 1, 'Alarm total', 0,
                                                                 _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                for i in e3:
                    if type_name == 'cumulant' and not method:  # 累积量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.en_descr, 0, '']
            else:
                for i in e1:
                    if type_name == 'measure' and not method:  # 测量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                for i in e2:
                    if type_name == 'status' and statusF == 0:  # 组合状态量返回二进制数
                        if data_name.device == i['device']:
                            if i['device'] == 'ReCon':
                                obj[return_key] = [int(i[data_name.name]) + 1, data_name.descr, 0, '']
                            else:
                                status_data_PCS = mqtt_session.query(StatusPT.id).filter(
                                    StatusPT.descr.like(i['device'] + '%'), StatusPT.name == 'ST1').first()
                                status_bits_data = mqtt_session.query(StatusBitsPT).filter(
                                    StatusBitsPT.status_id == status_data_PCS[0]).group_by(StatusBitsPT.bits).all()
                                bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                bit_list.reverse()
                                for b in status_bits_data:
                                    if b.name == 'runnsta':
                                        if int(bit_list[6]) == 0:
                                            obj['runnsta'] = [int(bit_list[6]) + 1, '运行状态', 0,
                                                              _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        elif int(bit_list[6]) == 1:
                                            obj['runnsta'] = [int(bit_list[6]) + 1, '运行状态', 0,
                                                              _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'Shutdsta':
                                        if int(bit_list[7]) == 0:
                                            obj['Shutdsta'] = [int(bit_list[7]) + 1, '停机状态', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[7]) == 1:
                                            obj['Shutdsta'] = [int(bit_list[7]) + 1, '停机状态', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'standsta':
                                        if int(bit_list[8]) == 0:
                                            obj['standsta'] = [int(bit_list[8]) + 1, '待机状态', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[8]) == 1:
                                            obj['standsta'] = [int(bit_list[8]) + 1, '待机状态', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'readystate':
                                        if int(bit_list[9]) == 0:
                                            obj['readystate'] = [int(bit_list[9]) + 1, '就绪状态', 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[9]) == 1:
                                            obj['readystate'] = [int(bit_list[9]) + 1, '就绪状态', 0,
                                                                 _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'emeshutd':
                                        if int(bit_list[10]) == 0:
                                            obj['emeshutd'] = [int(bit_list[10]) + 1, '紧急停机', 0,
                                                               _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[10]) == 1:
                                            obj['emeshutd'] = [int(bit_list[10]) + 1, '紧急停机', 0,
                                                               _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'dccibrepos':
                                        if int(bit_list[11]) == 0:
                                            obj['dccibrepos'] = [int(bit_list[11]) + 1, '直流断路器位置', 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        if int(bit_list[11]) == 1:
                                            obj['dccibrepos'] = [int(bit_list[11]) + 1, '直流断路器位置', 0,
                                                                 _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                                    if b.name == 'accibrepos':
                                        now_time = timeUtils.getNewTimeStr()
                                        now_time_ = timeUtils.timeStrToTamp(now_time)
                                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                        if int(bit_list[13]) == 0:
                                            obj['accibrepos'] = [int(bit_list[13]) + 1, '交流断路器位置', 0, '']
                                        elif int(bit_list[13]) == 1:
                                            if time_ss > 125:
                                                obj['accibrepos'] = [1, '交流断路器位置', 0, '']
                                            if time_ss <= 125:
                                                obj['accibrepos'] = [int(bit_list[13]) + 1, '交流断路器位置', 0, '']
                                    if b.name == 'TotalAlarm':
                                        if int(bit_list[14]) == 0:
                                            obj['TotalAlarm'] = [int(bit_list[14]) + 1, '告警总', 0,
                                                                 _value_desc_fanyi(lang, b.desc_off, status_data_obj)]
                                        elif int(bit_list[14]) == 1:
                                            obj['TotalAlarm'] = [int(bit_list[14]) + 1, '告警总', 0,
                                                                 _value_desc_fanyi(lang, b.desc_on, status_data_obj)]
                for i in e3:
                    if type_name == 'cumulant' and not method:  # 累积量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
        data.append(obj)
    for o in data:
        for oo in obj_unit_list:
            if o['page_area'] == oo['page_area']:
                o.update(oo)
    return True, data


def _getDataByPageId_2(id, statusF, db, id_1=None, lang=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]

    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']
    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']
    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']

    for area in page_area_obj.keys():
        if area == 'total':
            if area[:3] == 'PCS':
                id_1 = int(area[-1]) + 3
            if id != '91':
                status_data = mqtt_session.query(StatusPT).filter(StatusPT.device_id == id_1).all()
            obj = {'page_area': area}
            for data_name in page_area_obj[area]:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method
                if lang == 'en':
                    for i in e1:
                        if type_name == 'measure' and not method:  # 测量的值
                            if data_name.device == i['device']:
                                if i[data_name.name]:
                                    obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']

                    if id != '91':
                        for i in e3:
                            if type_name == 'cumulant' and not method:  # 累积量的值
                                if data_name.device == i['device']:
                                    if i[data_name.name]:
                                        obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0,
                                                           '']

                    for i in e2:
                        if id != '91':
                            for c in status_data:
                                if c.descr[:4] == i['device']:
                                    # return_key = c.descr[:4] + '_' + c.name
                                    obj[c.name] = [i[c.name], c.descr, c.id, '']
                            if id != '83':
                                if data_name.device == i['device']:
                                    if int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 0:
                                        obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常',
                                                             0, _value_desc_fanyi(lang, '异常', status_data_obj)]
                                    elif int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 1:
                                        obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常',
                                                             0, _value_desc_fanyi(lang, '正常', status_data_obj)]


                        else:
                            if data_name.device[:3] == 'PCS':
                                if data_name.device[:4] == i['device']:

                                    bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                    bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                    bit_list_n.reverse()

                                    bin_aaa = bit_list_n[13]
                                    if bin_aaa == '0':
                                        obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                                    elif bin_aaa == '1':
                                        now_time = timeUtils.getNewTimeStr()
                                        now_time_ = timeUtils.timeStrToTamp(now_time)
                                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                        if time_ss > 125:
                                            obj[return_key] = [1, '交流断路器位置', 0, '']
                                        if time_ss <= 125:
                                            obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                            elif data_name.device[:3] == 'BMS':
                                if data_name.device[:4] == i['device']:
                                    bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                    bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                    bit_list_n.reverse()

                                    bin_aaa = bit_list_n[10]
                                    obj[return_key] = [int(bin_aaa) + 1, '开入电源正常', 0, '']
                else:
                    for i in e1:
                        if type_name == 'measure' and not method:  # 测量的值
                            if data_name.device == i['device']:
                                if i[data_name.name]:
                                    obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                    if id != '91':
                        for i in e3:
                            if type_name == 'cumulant' and not method:  # 累积量的值
                                if data_name.device == i['device']:
                                    if i[data_name.name]:
                                        obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0,
                                                           '']
                    for i in e2:
                        if id != '91':
                            for c in status_data:
                                if c.descr[:4] == i['device']:
                                    # return_key = c.descr[:4] + '_' + c.name
                                    obj[c.name] = [i[c.name], c.descr, c.id, '']
                            if id != '83':
                                if data_name.device == i['device']:
                                    if int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 0:
                                        obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常',
                                                             0, '异常']
                                    elif int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 1:
                                        obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常',
                                                             0, '正常']
                        else:
                            if data_name.device[:3] == 'PCS':
                                if data_name.device[:4] == i['device']:
                                    bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                    bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                    bit_list_n.reverse()
                                    bin_aaa = bit_list_n[13]
                                    if bin_aaa == '0':
                                        obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                                    elif bin_aaa == '1':
                                        now_time = timeUtils.getNewTimeStr()
                                        now_time_ = timeUtils.timeStrToTamp(now_time)
                                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                        if time_ss > 125:
                                            obj[return_key] = [1, '交流断路器位置', 0, '']
                                        if time_ss <= 125:
                                            obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                            elif data_name.device[:3] == 'BMS':
                                if data_name.device[:4] == i['device']:
                                    bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                    bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                    bit_list_n.reverse()
                                    bin_aaa = bit_list_n[10]
                                    obj[return_key] = [int(bin_aaa) + 1, '开入电源正常', 0, '']

            data.append(obj)

    return True, data


def _getDataByPageId_3(id, statusF, db, id_1=None, lang=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]

    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']
    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']
    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']

    for area in page_area_obj.keys():
        if area[:3] == 'PCS':
            id_1 = int(area[-1]) + 3

        if id != '91':
            status_data = mqtt_session.query(StatusPT).filter(StatusPT.device_id == id_1).all()
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            if lang == 'en':
                for i in e1:
                    if type_name == 'measure' and not method:  # 测量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.en_descr, 0, '']
                if id != '91':
                    for i in e3:
                        if type_name == 'cumulant' and not method:  # 累积量的值
                            if data_name.device == i['device']:
                                if i[data_name.name]:
                                    obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.en_descr, 0,
                                                       '']
                for i in e2:
                    if id != '91':
                        for c in status_data:
                            if c.descr[:4] == i['device']:
                                # return_key = c.descr[:4] + '_' + c.name
                                obj[c.name] = [i[c.name], c.descr, c.id, '']
                        if id != '83':
                            if data_name.device == i['device']:
                                if int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 0:
                                    obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1,
                                                         'The power supply is normal', 0,
                                                         _value_desc_fanyi(lang, '异常', status_data_obj)]
                                elif int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 1:
                                    obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1,
                                                         'The power supply is normal', 0,
                                                         _value_desc_fanyi(lang, '正常', status_data_obj)]
                    else:
                        if data_name.device[:3] == 'PCS':
                            if data_name.device[:4] == i['device']:

                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()

                                bin_aaa = bit_list_n[13]
                                if bin_aaa == '0':
                                    obj[return_key] = [int(bin_aaa) + 1, 'Ac circuit breaker position', 0, '']

                                elif bin_aaa == '1':
                                    now_time = timeUtils.getNewTimeStr()
                                    now_time_ = timeUtils.timeStrToTamp(now_time)
                                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                    if time_ss > 125:
                                        obj[return_key] = [1, 'Ac circuit breaker position', 0, '']
                                    if time_ss <= 125:
                                        obj[return_key] = [int(bin_aaa) + 1, 'Ac circuit breaker position', 0, '']

                        elif data_name.device[:3] == 'BMS':
                            if data_name.device[:4] == i['device']:
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()
                                bin_aaa = bit_list_n[10]
                                obj[return_key] = [int(bin_aaa) + 1, 'The power supply is normal', 0, '']
            else:
                for i in e1:
                    if type_name == 'measure' and not method:  # 测量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                if id != '91':
                    for i in e3:
                        if type_name == 'cumulant' and not method:  # 累积量的值
                            if data_name.device == i['device']:
                                if i[data_name.name]:
                                    obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                for i in e2:
                    if id != '91':
                        for c in status_data:
                            if c.descr[:4] == i['device']:
                                # return_key = c.descr[:4] + '_' + c.name
                                obj[c.name] = [i[c.name], c.descr, c.id, '']
                        if id != '83':
                            if data_name.device == i['device']:
                                if int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 0:
                                    obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常', 0,
                                                         '异常']
                                elif int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 1:
                                    obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常', 0,
                                                         '正常']
                    else:
                        if data_name.device[:3] == 'PCS':
                            if data_name.device[:4] == i['device']:
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()
                                bin_aaa = bit_list_n[13]
                                if bin_aaa == '0':
                                    obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                                elif bin_aaa == '1':
                                    now_time = timeUtils.getNewTimeStr()
                                    now_time_ = timeUtils.timeStrToTamp(now_time)
                                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                    if time_ss > 125:
                                        obj[return_key] = [1, '交流断路器位置', 0, '']
                                    if time_ss <= 125:
                                        obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                        elif data_name.device[:3] == 'BMS':
                            if data_name.device[:4] == i['device']:
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()

                                bin_aaa = bit_list_n[10]
                                obj[return_key] = [int(bin_aaa) + 1, '开入电源正常', 0, '']

        data.append(obj)

    return True, data


def _getMeasureSumByNames(names, mode):
    ''' 返回测量量的和 '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        if mea['value'] == '--':
            return '--'
        arr.append(mea['value'])
    return num_retain(sum(arr), 3)


def _getMeasureSByNames(names, mode):
    ''' 返回两个测量量的商 '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        if mea['value'] == '--':
            return '--'
        arr.append(mea['value'])

    if arr[1] == 0:
        return '0.000'
    return num_retain(arr[0] / arr[1] * 100, 3)


def _getMeasureAvgByNames(names, mode, ro=2):
    '''
    返回测量量的平均值
    name:名称集合
    ro：保留小数位，默认2

    '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        if mea['value'] == '--':
            return '--'
        arr.append(mea['value'])

    return num_retain(numpy.mean(arr), 3)


def _getMeasureSqrtByNames(names, mode, ro=2):
    name = names.split('#')
    al = 0
    for n in name:
        mea = real_data('measure', n, mode)
        if mea['value'] == '--':
            return '--'
        al = al + mea['value'] ** 2

    return num_retain(math.sqrt(al), 3)


def _getMeasurePowByNames(names, mode, ro=2):
    '''
    求一个数的开3次方
    '''
    name = names.split('#')
    al = 0
    for n in name:
        mea = real_data('measure', n, mode)
        al = al + mea['value'] ** 2
    return num_retain(al ** 0.5, 3)


def _getMeasureRangeByNames(names, mode, ro=2):
    '''
    返回测量量的差值
    name:名称集合
    ro：保留小数位，默认2

    '''
    name = names.split('#')
    v1 = real_data('measure', name[0], mode)['value']
    v2 = real_data('measure', name[1], mode)['value']
    if v1=='--' or v2=='--':
        return '--'

    return num_retain(v1 - v2, 3)


def _getStatusOrByNames(names, mode):
    '''
    返回状态量是否告警
    name:名称集合
    mode:从哪取值
    '''
    name = names.split('#')
    for n in name:
        sta = real_data('status', n, mode)
        val = int(sta['value'])
        if val == 2:
            return '告警'

    return '正常'


def _getStatusValueOrByNames(names, mode):
    name = names.split('#')
    for n in name:
        sta = real_data('status', n, mode)
        val = int(sta['value'])

    return val


def _getStatusByNames(names, mode):
    '''
    返回状态量具体值
    '''
    object = {}
    name = names.split("#")
    for n in name:
        sta = real_data('status', n, mode)
        object[n] = sta['value']

    return object


def _replace(old_string, char, index):
    '''
    替换指定位置内容
    old_string：旧字符
    char：替换值
    index：替换位置
    '''
    new_string = '%s%s%s' % (old_string[:index], char, old_string[index + 1:])
    return new_string


def _value_desc_fanyi(lang, descr, dtype):
    '''
    值描述翻译
    lang:目标语言
    descr:描述
    dtype:原始数据集
    '''
    try:
        if lang == 'en':
            desc = dtype[descr][lang]
        else:
            desc = descr
    except Exception as e:
        desc = translate_text(descr, 2)

    return desc

def extract_number(s):
    return int(''.join(filter(str.isdigit, s)))


def _getDataByPageIdNormal(id, statusF, db, lang=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    page_method_1 = user_session.query(PageData).filter(PageData.page_id == id,PageData.station == db,PageData.is_use == 1,PageData.type_name == 'status', PageData.method == None).order_by(PageData.page_area).all()
    # 单元状态
    obj_unit_list = []
    if page_method_1:
        for area in page_method_1:
            obj_unit = {'page_area': area.page_area}
            s2 = real_data('status', area.name, area.mode)  # 状态量的单个值
            if lang == 'en':
                obj_unit[area.return_key] = [int(s2['value']), 'Whether it works properly', 0, 'Normal operation']
            else:
                obj_unit[area.return_key] = [int(s2['value']), s2['desc'].split()[-1] if s2['desc'] else area.descr, 0, s2['valueDesc']]

            obj_unit_list.append(obj_unit)
    # 判断页面是否再自定义的内存中
    # global datas_obj
    #  当前页的所有区域分类
    page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                               PageData.station == db).group_by(
        PageData.page_area).order_by(asc(PageData.id)).all()
    p_area = {}  # 定义区域集
    for area in page_areas:
        page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                        PageData.is_use == 1, PageData.station == db).all()
        p_area[area[0]] = page_data
    # datas_obj[db][id] = p_area
    page_area_obj = p_area
    # print(page_area_obj)
    # exit()
    for area in page_area_obj.keys():
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            if lang == 'en':
                if type_name == 'measure' and method == '+':  # 测量量的和
                    v = _getMeasureSumByNames(data_name.name, data_name.mode)
                    if data_name.station == 'ygzhen' and v != '--':
                        v = str(round(float(v) / 1000, 3))
                    obj[return_key] = [v + unit, data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == 'avg':  # 测量量的平均值
                    obj[return_key] = [_getMeasureAvgByNames(data_name.name, data_name.mode) + unit, data_name.en_descr,
                                       0, '']
                elif type_name == 'measure' and method == 'sqrt':  # 两数平方和开平方
                    obj[return_key] = [_getMeasureSqrtByNames(data_name.name, data_name.mode) + unit,
                                       data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == '-':  # 测量量的差值
                    obj[return_key] = [_getMeasureRangeByNames(data_name.name, data_name.mode) + unit,
                                       data_name.en_descr, 0, '']
                elif type_name == 'measure' and method == '/':  # 测量量两个数商
                    obj[return_key] = [_getMeasureSByNames(data_name.name, data_name.mode) + unit, data_name.en_descr,
                                       0, '']
                elif type_name == 'status' and not method:  # 状态量的单个值
                    s = real_data('status', data_name.name, data_name.mode)
                    if lang == 'en':
                        if '空调' not in data_name.descr:
                            valueDesc_s = _value_desc_fanyi(lang, s['valueDesc'], status_data_obj)
                            obj[return_key] = [s['value'], data_name.en_descr, 0, valueDesc_s]
                        else:
                            obj[return_key] = [s['value'], data_name.en_descr, 0, s['valueDesc']]
                    else:
                        obj[return_key] = [s['value'], data_name.en_descr, 0, s['valueDesc']]


                elif type_name == 'status' and method == 'or' and statusF == 0:  # 组合状态量直接返回告警状态
                    obj[return_key] = [_getStatusOrByNames(data_name.name, data_name.mode), data_name.en_descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 4:  # 组合状态量直接值
                    obj[return_key] = [_getStatusValueOrByNames(data_name.name, data_name.mode), data_name.en_descr, 0,
                                       '']
                elif type_name == 'status' and method == 'or' and statusF == 1:  # 组合状态量返回所有当前值
                    obj[return_key] = [_getStatusByNames(data_name.name, data_name.mode), data_name.en_descr, 0, '']

                elif type_name == 'status' and statusF == 3:  # 组合状态量返回二进制数
                    bit_val = 0
                    names = data_name.name.split('#')  # 数据名称集合
                    if len(names) != 16:
                        return False, 'status %s the number of names does not match' % data_name.en_descr
                    if method == 'selfbit':  # 自身就是按0-15位排列
                        for nam in names:
                            ind = names.index(nam)
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    else:
                        if method != 'bit' or not data_name.bit_id:
                            return False, 'status %s misconfiguration' % data_name.en_descr
                        # bit_names = data_name.bit_pageData.name.split('#')  # 获取当前配置的二进制name集合
                        bit_names = bit_obj[data_name.bit_id].split('#')

                        for nam in names:
                            n = nam.split('.')[-1]  # 取点分割后的最后一个名称
                            if n not in bit_names:
                                return False, 'status %s the name configuration does not match' % data_name.en_descr
                            ind = bit_names.index(n)  # 获取名称在二进制中的索引
                            # val = 0
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    obj[return_key] = [bit_val, data_name.en_descr, data_name.id, '']
                elif type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        v = '--'
                        v_status = '--'
                    else:
                        v = float(m['value'])
                        v_status = 1 if v > 0 else 2
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        if lang == 'en':
                            data_name.descr = data_name.en_descr
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] > heartContinue:
                            obj[return_key] = [v_status, data_name.en_descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.en_descr, 0, '']
                    else:
                        obj[return_key] = [v_status, data_name.en_descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.en_descr, 0, '']
                    else:
                        if return_key == 'soc':
                            if m['value'] > 101:
                                m['value'] = 0
                        obj[return_key] = [num_retain(m['value'], 3) + unit, data_name.en_descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    if db == 'houma':
                        d['value'] = d['value'] + 1
                    if lang == 'en':
                        data_name.descr = data_name.en_descr
                    obj[return_key] = [d['value'], data_name.en_descr, 0,
                                       _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
            else:
                if type_name == 'measure' and method == '+':  # 测量量的和
                    v = _getMeasureSumByNames(data_name.name, data_name.mode)
                    obj[return_key] = [v + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'avg':  # 测量量的平均值
                    obj[return_key] = [_getMeasureAvgByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                       '']
                elif type_name == 'measure' and method == 'sqrt':  # 两数平方和开平方
                    obj[return_key] = [_getMeasureSqrtByNames(data_name.name, data_name.mode) + unit, data_name.descr,
                                       0, '']
                elif type_name == 'measure' and method == '-':  # 测量量的差值
                    obj[return_key] = [_getMeasureRangeByNames(data_name.name, data_name.mode) + unit, data_name.descr,
                                       0, '']
                elif type_name == 'measure' and method == '/':  # 测量量两个数商
                    obj[return_key] = [_getMeasureSByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                       '']
                elif type_name == 'status' and not method:  # 状态量的单个值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0, s['valueDesc']]

                elif type_name == 'status' and method == 'or' and statusF == 0:  # 组合状态量直接返回告警状态
                    obj[return_key] = [_getStatusOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 4:  # 组合状态量直接值
                    obj[return_key] = [_getStatusValueOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
                elif type_name == 'status' and method == 'or' and statusF == 1:  # 组合状态量返回所有当前值
                    obj[return_key] = [_getStatusByNames(data_name.name, data_name.mode), data_name.descr, 0, '']

                elif type_name == 'status' and statusF == 3:  # 组合状态量返回二进制数
                    bit_val = 0
                    names = data_name.name.split('#')  # 数据名称集合
                    if len(names) != 16:
                        return False, 'status %s 名称个数不匹配' % data_name.descr
                    if method == 'selfbit':  # 自身就是按0-15位排列
                        for nam in names:
                            ind = names.index(nam)
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    else:
                        if method != 'bit' or not data_name.bit_id:
                            return False, 'status %s 配置错误' % data_name.descr
                        # bit_names = data_name.bit_pageData.name.split('#')  # 获取当前配置的二进制name集合
                        bit_names = bit_obj[data_name.bit_id].split('#')
                        for nam in names:
                            n = nam.split('.')[-1]  # 取点分割后的最后一个名称
                            if n not in bit_names:
                                return False, 'status %s 名称配置不匹配' % data_name.descr
                            ind = bit_names.index(n)  # 获取名称在二进制中的索引
                            # val = 0
                            s = real_data('status', nam, data_name.mode)
                            val = s['value']
                            if val > 1:
                                bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数
                    obj[return_key] = [bit_val, data_name.descr, data_name.id, '']
                elif type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        v = '--'
                        v_status = '--'
                    else:
                        v = float(m['value'])
                        v_status = 1 if v > 0 else 2
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        data_name.descr = data_name.descr
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] > heartContinue:
                            obj[return_key] = [v_status, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [v_status, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    if m['value'] == '--':
                        obj[return_key] = ['--' + unit, data_name.descr, 0, '']
                    else:
                        if return_key == 'soc':
                            if m['value'] > 101:
                                m['value'] = 0
                        obj[return_key] = [num_retain(m['value'], 3) + unit, data_name.descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    if db == 'houma':
                        d['value'] = d['value'] + 1
                    data_name.descr = data_name.descr
                    obj[return_key] = [d['value'], data_name.descr, 0, d['valueDesc']]

        data.append(obj)
    for o in data:
        for oo in obj_unit_list:
            if o['page_area'] == oo['page_area']:
                o.update(oo)
    return True, data


def _getProgramsDataNormal(db, lang,key_):
    '''
    处理小程序首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == key_,PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.name.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]
    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0]}
            page_data = user_session.query(PageData).filter(PageData.page_id == key_,
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                if type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    v = float(m['value'])
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] >= heartContinue:
                            obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    obj[return_key] = [num_retain(m['value'], 1) + unit, data_name.descr, 0, '']

                elif type_name == 'measure' and method == '+':  # 测量量相加
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    m = m1['value'] + m2['value'] / 1000
                    obj[return_key] = [num_retain(m, 1) + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'pow':  # 测量量求开3次方
                    obj[return_key] = [num_retain(_getMeasurePowByNames(data_name.name, data_name.mode), 1) + unit,
                                       data_name.descr, 0, '']

                elif type_name == 'measure' and method == '/':  # 测量量计算效率
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m2['value'] != 0:
                        if names[0] in station_base_value.keys():
                            v1 = m1['value'] - station_base_value[names[0]]
                            v2 = m2['value'] - station_base_value[names[1]]
                            effi = v1 / v2 if v1 / v2 < 1 else 1
                        else:
                            effi = m1['value'] / m2['value'] if m1['value'] / m2['value'] < 1 else 1
                        obj[return_key] = [num_retain(effi, 2), data_name.descr, 0, '']
                    else:
                        obj[return_key] = ['0.00', data_name.descr, 0, '']

                elif type_name == 'measure' and method == '——':
                    obj[return_key] = ['——', data_name.descr, 0, '']

                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    v = d['value']  # 当前值
                    obj[return_key] = [v, data_name.descr, 0,
                                       _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                    if 'tfStygzhen' in data_name.name or 'tfStzgtian' in data_name.name or 'tc_datong' in data_name.name:  # 永臻/中天、离散量判断充放电状态  [电池簇维护|放电|充电]
                        if v == 1:
                            obj['SysMdChag'] = [1, '充电状态', 0, '充电']
                        elif v == 2:
                            obj['SysMdDisg'] = [1, '放电状态', 0, '放电']
                    elif 'guizhou' in data_name.name:
                        if v == 4:
                            obj['SysMdChag'] = [1, '充电状态', 0, '充电']
                        elif v == 5:
                            obj['SysMdDisg'] = [1, '放电状态', 0, '放电']

                elif type_name == 'status' and method == 'online':
                    d = real_data('discrete', data_name.name, data_name.mode)
                    v = d['value']  # 当前值
                    if v == 2:
                        obj['SysMdOffline'] = [1, '在线', 0, '在线']
                    else:
                        obj['SysMdIdle'] = [1, '空闲', 0, '空闲']

                elif type_name == 'status' and not method:  # 状态量实时值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0,
                                       _value_desc_fanyi(lang, s['valueDesc'], status_data_obj)]

                if data_name.name.endswith('Sys_SOC'):  # 以soc结尾计算告警和故障

                    for n in bams_gz.split('#'):  # 电池故障
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_gz2.split('#'):  # 电池故障2
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, _value_desc_fanyi(lang, '故障', discrete_data_obj)]
                            break
                    for n in bams_bj.split('#'):  # 电池报警
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_bj'] = [2, '电池堆报警', 0, _value_desc_fanyi(lang, '报警', discrete_data_obj)]
                            break

                    name = data_name.name.replace('Sys_SOC', 'InvtOallStat')  # PCS故障 告警
                    d = real_data('discrete', name, data_name.mode)
                    val = d['value']
                    if val == 2:
                        obj['pcs_gz'] = [2, 'pcs故障', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue
                    elif val == 1:
                        obj['pcs_bj'] = [2, 'pcs报警', 0, _value_desc_fanyi(lang, d['valueDesc'], discrete_data_obj)]
                        # continue

            data.append(obj)
    data = natsorted(data, key=lambda x: x['page_area'], reverse=False)
    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    page_data_all = user_session.query(PageData).filter(PageData.page_id == key_, PageData.is_use == 1,
                                                        PageData.return_key == 'onlineall').all()

    page_data_status = user_session.query(PageData).filter(PageData.page_id == key_, PageData.is_use == 1,
                                                           PageData.return_key == 'online').all()
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == key_,
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()
    to = len(page_data_status)
    # arar = user_session.query(Bit.name).filter(Bit.id==3).first()
    left1['total'] = to
    left1['page_area'] = bj_page_data_status[0].page_area
    i, y = 0, 0
    for stat in page_data_status:
        ind = page_data_status.index(stat)
        if real_data('status', page_data_all[ind].name, page_data_all[ind].mode)['value'] == 2:
            sname = stat.name
            ss = real_data('status', sname, stat.mode)
            val = ss['value']
            if val == 2:  # 在线
                y = y + 1;

            if '#' in bj_page_data_status[ind].name:
                na = bj_page_data_status[ind].name.split('#')[0].replace('Sw4OnleMd1', 'Sw2Alarm')
                s = real_data('status', na, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            elif 'baodian' == db:  # 广州保电
                name = bj_page_data_status[ind].name.replace('_Pcs.Online', "Pcs.SysRunSt")
                s = real_data('discrete', name, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            else:
                for n in bams_bj.split('#'):
                    name = bj_page_data_status[ind].name.replace('SysMdOffline', n)
                    s = real_data('status', name, stat.mode)
                    val = s['value']
                    if val == 2:
                        i = i + 1;
                        break
    left1['online'] = y
    left1['outline'] = to - y
    left1['bj'] = i
    left1['lv'] = num_retain(float(y) / to * 100)
    data.append(left1)
    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db,
                                      Event.type == 2,
        or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).group_by(Event.type_id).order_by(
        Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, Event.station == db,
                                               Event.type == 2,
                                               or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).order_by(
        AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " " + _value_desc_fanyi(lang, '报警', status_data_obj)
        else:
            point = point + " " + _value_desc_fanyi(lang, '已恢复', status_data_obj)
        d.append({'ts': alarm['ts'], 'alarm_descr': _value_desc_fanyi(lang, alarm['alarm_descr'], status_data_obj),
                  'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data
