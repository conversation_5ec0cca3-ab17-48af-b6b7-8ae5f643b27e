#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-02-21 14:14:58
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\TianLuAppBackend\tools\minio_tool.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-04-09 09:30:20


from minio import Minio
import io
from datetime import timedelta

# 文件服务器配置
MINIO_CONF = {
        # 'endpoint': '192.168.1.101:9006',   # 内网
        # 'endpoint': '120.132.33.184:9006',  # 公网
        # 'endpoint': 'https://minio.robestec.cn/minio/',
        'endpoint': 'minio.robestec.cn',
        'access_key': 'transmit',
        'secret_key': 'RHbess9ol.',
        'secure': True
    }


class MinioTool:
    def __init__(self):
        # 使用endpoint、access key和secret key来初始化minioClient对象。
        self.minioClient = Minio(**MINIO_CONF)

    def create_bucket(self, bucket_name='rhyc'):
        # 调用make_bucket来创建一个存储桶。
        if self.minioClient.bucket_exists(bucket_name):
            print('Bucket：', bucket_name, '已存在！')
            return
        self.minioClient.make_bucket(bucket_name, location="us-east-1")
        print('Bucket：', bucket_name, '创建成功！')
        return bucket_name

    def upload_local_file(self, file_name, file_path, bucket_name='rhyc'):
        self.minioClient.fput_object(bucket_name, file_name, file_path)
        print('文件上传成功！')
        download_url = self.minioClient.presigned_get_object(bucket_name, file_name)
        return download_url

    def upload_local_image(self, file_name, file_path, bucket_name='rhyc'):
        suffix = file_name.split('.')[-1]
        self.minioClient.fput_object(bucket_name, file_name, file_path, content_type=f'image/{suffix}')
        print('图片上传成功！')
        download_url = self.minioClient.presigned_get_object(bucket_name, file_name)
        return download_url

    def upload_object(self, file, bucket_name='rhyc'):
        self.minioClient.put_object(bucket_name, file.name, file, file.size)
        print('文件上传成功！')
        download_url = self.minioClient.presigned_get_object(bucket_name, file.name)
        return download_url

    def get_download_url(self, bucket_name, object_name):
        download_url = self.minioClient.presigned_get_object(bucket_name, object_name)
        return download_url

    def upload_file(self, binary_data, bucket_name, object_name):
        """
        上传二进制数据到MinIO存储桶
        :param binary_data: 二进制数据
        :param bucket_name: 存储桶名称
        :param object_name: 文件名
        :return: 存储地址
        """
        # 确保存储桶存在
        if not self.minioClient.bucket_exists(bucket_name):
            self.minioClient.make_bucket(bucket_name)

        # 上传二进制数据
        binary_stream = io.BytesIO(binary_data)
        self.minioClient.put_object(bucket_name, object_name, binary_stream, len(binary_data))

        # 返回存储地址
        storage_url = self.minioClient.presigned_get_object(bucket_name, object_name, expires=timedelta(days=7))

        return storage_url


# minioClient = MinioTool()

if __name__ == '__main__':
    minioClient = MinioTool()
    minioClient.create_bucket('test01')
    # url = minioClient.upload_local_file('11.png', './avatar.png')
    # print('文件地址为【文件在浏览器打开会直接下载，放到index.html 中使用img引入查看】：\n', url)
