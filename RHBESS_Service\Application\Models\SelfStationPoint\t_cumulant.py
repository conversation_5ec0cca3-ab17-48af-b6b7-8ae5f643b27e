#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 16:50:22
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_cumulant.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:22:07

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_device import DevicePT

class CumulantPT(mqtt_Base):
    ''' 累积量说明表 '''
    __tablename__ = "t_cumulant"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    device_id = Column(Integer, ForeignKey("t_device.id"),nullable=False, comment=u"所属设备")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    delta = Column(Float, nullable=True,comment=u"变化限值")
    coef = Column(Float, nullable=True,comment=u"系数")
    unit = Column(String(10), nullable=True,comment=u"单位")
    max_value = Column(Float, nullable=True,comment=u"满码值")
    store_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否存盘（0否1是，默认1）")
    rep_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否上报（0否1是，默认1）")
    

    device_cumulant = relationship("DevicePT",backref='device_cumulant')

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','delta':'%s','coef':'%s','max':'%s','store_flag':'%s','rep_flag':'%s','unit':'%s'}" % (
            self.id,self.name,self.descr,self.delta,self.coef,self.max_value,self.store_flag,self.rep_flag,self.unit)