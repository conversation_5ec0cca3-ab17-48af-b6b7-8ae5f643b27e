import datetime
import decimal
import random

from django.db.models import Min
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.app2 import error_log, success_log
from apis.app2.my import serializer
from apis.user import models
from common import common_response_code
from common.constant import FEEDBACK_EMAIL_RECEIVERS
from encryption import jwt_encryption
from tools.aly_send_smscode import Sample
from tools.count import unit_convert

from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from tools.send_mail import sendMail_


# Create your views here.

class GetSMSCode(APIView):
    """
    登录页面发送短信接口
    """""

    def get(self, request):
        ser = serializer.SendMobileMessageSerializer(data=request.query_params)
        if not ser.is_valid():
            error_log.error("短信登录字段传参校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("验证码登录:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )

        conn = get_redis_connection("default")
        conn.set(ser.validated_data["mobile"], random_sms_code, 60)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功",
                },
            }
        )


class SMSCodeLoginView(APIView):
    """手机号 + 短信验证码登录"""""

    def post(self, request):
        ser = serializer.SMSCodeLoginSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error(f"传参格式不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        tem_code = conn.get(ser.validated_data['mobile'])
        if not tem_code:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "验证码已过期",
                    },
                }
            )

        conn.delete(ser.validated_data["mobile"])  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],is_used=1).first()
        if not user_instance:
            error_log.error("手机号验证码登录:用户名或密码错误")
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "用户名或密码错误",
                    },
                }
            )
        if ser.validated_data['code'] == tem_code.decode('utf-8'):
            user_roles = list(user_instance.roles.all()) if user_instance else []
            permissions = list()

            # 使用列表推导式来简化角色和权限的处理
            temp_roles = [{"id": role.id, "role": role.role_name} for role in user_roles if role.type == 0]
            permissions.extend({"title": p.title, "url": p.url} for role in user_roles if role.type == 0 for p in
                               role.permissions.all() if p.ty == 2)

            new_permissions = []
            temp_list = []
            for p in permissions:
                if p['title'] not in temp_list:
                    temp_list.append(p['title'])
                    new_permissions.append(p)

            # 使用any/all进一步简化数据库查询
            master_stations = models.MaterStation.objects.filter(
                userdetails=user_instance, is_delete=0
            ).values(
                "id", "name", "english_name"
            )

            for master_station in master_stations:
                station = models.StationDetails.objects.filter(is_delete=0, master_station=master_station['id']).first()
                master_station['address'] = station.address
                master_station['province'] = station.province.name

            # 使用get来安全地访问validated_data
            mobile = ser.validated_data.get("mobile")
            login_name = ser.validated_data.get("login_name")
            success_token = jwt_encryption.create_token({"user_id": user_instance.id})

            success_message = (
                f"手机号:{mobile}登录成功" if mobile else f"用户:{login_name}登录成功"
            )

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": success_message,
                        "roles": temp_roles,
                        "permissions": new_permissions,
                        "success_token": success_token,
                        "station_detail": master_stations,
                    },
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "验证码错误",
                    },
                }
            )


class PasswordLoginView(APIView):
    """手机号/用户名 + 密码登录"""""

    def post(self, request):
        ser = serializer.PasswordLoginSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("短信密码:字段校验不通过")
            error_code = common_response_code.FIELD_ERROR
            error_message = {
                "message": "error",
                "detail": ser.errors,
            }
            return Response({"code": error_code, "data": error_message})

        # 缓存查询结果避免重复查询
        user_instance = models.UserDetails.objects.filter(**ser.data).first()
        user_roles = list(user_instance.roles.all()) if user_instance else []
        permissions = list()

        # 使用列表推导式来简化角色和权限的处理
        temp_roles = [{"id": role.id, "role": role.role_name} for role in user_roles if role.type == 0]
        permissions.extend({"title": p.title, "url": p.url} for role in user_roles if role.type == 0
                           for p in role.permissions.all() if p.ty == 2)
        new_permissions = []
        temp_list = []
        for p in permissions:
            if p['title'] not in temp_list:
                temp_list.append(p['title'])
                new_permissions.append(p)

        # 使用any/all进一步简化数据库查询
        master_stations = models.MaterStation.objects.filter(
            userdetails=user_instance, is_delete=0
        ).values(
            "id", "name", "english_name"
        )

        for master_station in master_stations:
            station = models.StationDetails.objects.filter(is_delete=0, master_station=master_station['id']).first()
            master_station['address'] = station.address
            master_station['province'] = station.province.name

        # 使用get来安全地访问validated_data
        mobile = ser.validated_data.get("mobile")
        login_name = ser.validated_data.get("login_name")
        success_token = jwt_encryption.create_token({"user_id": user_instance.id})

        success_message = (
            f"手机号:{mobile}登录成功" if mobile else f"用户:{login_name}登录成功"
        )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": success_message,
                    "roles": temp_roles,
                    "permissions": new_permissions,
                    "success_token": success_token,
                    "station_detail": master_stations,
                },
            }
        )


class ChangePasswordByMobilePasswordView(APIView):
    """修改密码"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = serializer.ChangePasswordByMobilePasswordSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("修改密码:数据格式校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        user_instance = models.UserDetails.objects.filter(id=request.user["user_id"]).first()

        user_instance.password = ser.validated_data["new_password"]
        user_instance.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "密码修改成功",
                },
            }
        )


class UserInfoView(APIView):
    """
    用户详情
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_instance = models.UserDetails.objects.filter(id=request.user["user_id"]).first()
        ser = serializer.UserInfoSerializer(instance=user_instance, many=False)
        detail = ser.data
        detail['avt_url'] = "https://www.robestec.cn/imgapi3/home/<USER>/media/picture/avatar.png"
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail,
                },
            }
        )


class FeedbackView(APIView):
    """小程序： 用户反馈"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def post(self, request):
        try:
            user_id = request.user['user_id']
            user = models.UserDetails.objects.get(id=user_id)
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户反馈：用户不存在"},
                }
            )

        feedback_content = request.data.get('content')
        if not feedback_content:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户反馈：反馈内容为空"},
                }
            )

        models.Feedback.objects.create(
            user_id=request.user['user_id'], content=feedback_content
        )

        tissue = user.tissue if user.tissue else ''
        Subject = f'{user.user_name}{datetime.datetime.today().strftime("%Y-%m-%d")}的天禄使用反馈'
        message = (f"{tissue}用户{user.user_name}在{datetime.datetime.now().strftime('%Y-%m-%d %H:%M')} "
                   f"针对天禄系列产品反馈以下内容：\n\n{feedback_content}")

        sender_show = "白泽添禄反馈邮件"
        recipient_show = "白泽添禄反馈邮件"

        try:
            sendMail_(message, Subject, sender_show, recipient_show, FEEDBACK_EMAIL_RECEIVERS)
        except Exception as e:
            error_log.error(f"邮件: {Subject} ===>发送失败")

        success_log.debug(f"邮件: {Subject} ===>发送成功")
        return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": "用户反馈：成功"},
                }
            )