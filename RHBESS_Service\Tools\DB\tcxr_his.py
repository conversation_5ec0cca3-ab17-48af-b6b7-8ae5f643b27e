from sqlalchemy import create_engine
from Application.Cfg.dir_cfg import model_config
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base



TCXR_USERNAMES = model_config.get('mysql', "TCXR_USERNAMES")
TCXR_PASSWORDS = model_config.get('mysql', "TCXR_PASSWORDS")
TCXR_HOSTNAMES = model_config.get('mysql', "TCXR_HOSTNAMES")
TCXR_PORTS = model_config.get('mysql', "TCXR_PORTS")
TCXR_DATABASE = model_config.get('mysql', "TCXR_DATABASE")
TCXR_DATABASE1 = model_config.get('mysql', "TCXR_DATABASE1")
TCXR_DATABASE2 = model_config.get('mysql', "TCXR_DATABASE2")
TCXR_DATABASE3 = model_config.get('mysql', "TCXR_DATABASE3")
TCXR_DATABASE4 = model_config.get('mysql', "TCXR_DATABASE4")

TCXR_HISTORY_USERNAMES = model_config.get('mysql', "TCXR_HISTORY_USERNAMES")
TCXR_HISTORY_PASSWORDS = model_config.get('mysql', "TCXR_HISTORY_PASSWORDS")
TCXR_HISTORY_HOSTNAMES = model_config.get('mysql', "TCXR_HISTORY_HOSTNAMES")
TCXR_HISTORY_PORTS = model_config.get('mysql', "TCXR_HISTORY_PORTS")
TCXR_HISTORY_DATABASE = model_config.get('mysql', "TCXR_HISTORY_DATABASE")
TCXR_HISTORY_DATABASE1 = model_config.get('mysql', "TCXR_HISTORY_DATABASE1")
TCXR_HISTORY_DATABASE2 = model_config.get('mysql', "TCXR_HISTORY_DATABASE2")
TCXR_HISTORY_DATABASE3 = model_config.get('mysql', "TCXR_HISTORY_DATABASE3")
TCXR_HISTORY_DATABASE4 = model_config.get('mysql', "TCXR_HISTORY_DATABASE4")



tcxr_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_USERNAMES,
    TCXR_PASSWORDS,
    TCXR_HOSTNAMES,
    TCXR_PORTS,
    TCXR_DATABASE
)
tcxr_engine = create_engine(tcxr_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr_session = scoped_session(sessionmaker(tcxr_engine,autoflush=True))
tcxr_Base = declarative_base(tcxr_engine)
tcxr_session = _tcxr_session()



tcxr1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_USERNAMES,
    TCXR_PASSWORDS,
    TCXR_HOSTNAMES,
    TCXR_PORTS,
    TCXR_DATABASE1
)
tcxr1_engine = create_engine(tcxr1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr1_session = scoped_session(sessionmaker(tcxr1_engine,autoflush=True))
tcxr1_Base = declarative_base(tcxr1_engine)
tcxr1_session = _tcxr1_session()



tcxr2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_USERNAMES,
    TCXR_PASSWORDS,
    TCXR_HOSTNAMES,
    TCXR_PORTS,
    TCXR_DATABASE2
)
tcxr2_engine = create_engine(tcxr2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr2_session = scoped_session(sessionmaker(tcxr2_engine,autoflush=True))
tcxr2_Base = declarative_base(tcxr2_engine)
tcxr2_session = _tcxr2_session()


tcxr3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_USERNAMES,
    TCXR_PASSWORDS,
    TCXR_HOSTNAMES,
    TCXR_PORTS,
    TCXR_DATABASE3
)
tcxr3_engine = create_engine(tcxr3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr3_session = scoped_session(sessionmaker(tcxr3_engine,autoflush=True))
tcxr3_Base = declarative_base(tcxr3_engine)
tcxr3_session = _tcxr3_session()


tcxr4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_USERNAMES,
    TCXR_PASSWORDS,
    TCXR_HOSTNAMES,
    TCXR_PORTS,
    TCXR_DATABASE4
)
tcxr4_engine = create_engine(tcxr4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr4_session = scoped_session(sessionmaker(tcxr4_engine,autoflush=True))
tcxr4_Base = declarative_base(tcxr4_engine)
tcxr4_session = _tcxr4_session()



tcxr_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_HISTORY_USERNAMES,
    TCXR_HISTORY_PASSWORDS,
    TCXR_HISTORY_HOSTNAMES,
    TCXR_HISTORY_PORTS,
    TCXR_HISTORY_DATABASE
)
tcxr_history_engine = create_engine(tcxr_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr_history_session = scoped_session(sessionmaker(tcxr_history_engine,autoflush=True))
tcxr_history_Base = declarative_base(tcxr_history_engine)
tcxr_history_session = _tcxr_history_session()



tcxr1_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_HISTORY_USERNAMES,
    TCXR_HISTORY_PASSWORDS,
    TCXR_HISTORY_HOSTNAMES,
    TCXR_HISTORY_PORTS,
    TCXR_HISTORY_DATABASE1
)
tcxr1_history_engine = create_engine(tcxr1_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr1_history_session = scoped_session(sessionmaker(tcxr1_history_engine,autoflush=True))
tcxr1_history_Base = declarative_base(tcxr1_history_engine)
tcxr1_history_session = _tcxr1_history_session()



tcxr2_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_HISTORY_USERNAMES,
    TCXR_HISTORY_PASSWORDS,
    TCXR_HISTORY_HOSTNAMES,
    TCXR_HISTORY_PORTS,
    TCXR_HISTORY_DATABASE2
)
tcxr2_history_engine = create_engine(tcxr2_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr2_history_session = scoped_session(sessionmaker(tcxr2_history_engine,autoflush=True))
tcxr2_history_Base = declarative_base(tcxr2_history_engine)
tcxr2_history_session = _tcxr2_history_session()


tcxr3_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_HISTORY_USERNAMES,
    TCXR_HISTORY_PASSWORDS,
    TCXR_HISTORY_HOSTNAMES,
    TCXR_HISTORY_PORTS,
    TCXR_HISTORY_DATABASE3
)
tcxr3_history_engine = create_engine(tcxr3_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr3_history_session = scoped_session(sessionmaker(tcxr3_history_engine,autoflush=True))
tcxr3_history_Base = declarative_base(tcxr3_history_engine)
tcxr3_history_session = _tcxr3_history_session()


tcxr4_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCXR_HISTORY_USERNAMES,
    TCXR_HISTORY_PASSWORDS,
    TCXR_HISTORY_HOSTNAMES,
    TCXR_HISTORY_PORTS,
    TCXR_HISTORY_DATABASE4
)
tcxr4_history_engine = create_engine(tcxr4_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tcxr4_history_session = scoped_session(sessionmaker(tcxr4_history_engine,autoflush=True))
tcxr4_history_Base = declarative_base(tcxr4_history_engine)
tcxr4_history_session = _tcxr4_history_session()