#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class Report(user_Base):
    u'报表基础配置表'
    __tablename__ = "t_report"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"名称")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    start_time = Column(VARCHAR(50), nullable=False,comment=u"开始时间")
    end_time = Column(VARCHAR(50), nullable=False,comment=u"截止时间")
    rate = Column(Float, nullable=False,comment=u"费率")
    months = Column(VARCHAR(256), nullable=False,comment=u"月份，多个用,隔开")
    years = Column(Integer, nullable=False,comment=u"年份")
    station = Column(VARCHAR(50), nullable=False,comment=u"所属站")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    is_use = Column(CHAR(2), nullable=False,comment=u"是否使用1是0否")
    
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        now = timeUtils.getNewTimeStr()
        user_session.merge(Report(id=1,name='jf1',descr='尖峰1',start_time='11:00:00',end_time='13:00:00',rate=1.29,years=2022,months='7,8.9',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=2,name='jf2',descr='尖峰2',start_time='16:00:00',end_time='17:00:00',rate=1.29,years=2022,months='7,8.9',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=3,name='fd1',descr='峰段1',start_time='10:00:00',end_time='11:00:00',rate=1.00,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=4,name='fd2',descr='峰段2',start_time='13:00:00',end_time='15:00:00',rate=1.00,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=5,name='fd3',descr='峰段3',start_time='18:00:00',end_time='21:00:00',rate=1.29,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=6,name='pd1',descr='平段1',start_time='07:00:00',end_time='10:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=7,name='pd2',descr='平段2',start_time='15:00:00',end_time='16:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=8,name='pd3',descr='平段3',start_time='17:00:00',end_time='18:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=9,name='pd4',descr='平段4',start_time='21:00:00',end_time='23:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=10,name='gd1',descr='谷段1',start_time='00:00:00',end_time='07:00:00',rate=0.35,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        user_session.merge(Report(id=11,name='gd2',descr='谷段2',start_time='23:00:00',end_time='23:59:59',rate=0.35,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=1))
        
        user_session.merge(Report(id=12,name='fd1',descr='峰段1',start_time='10:00:00',end_time='15:00:00',rate=1.00,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=13,name='fd2',descr='峰段2',start_time='18:00:00',end_time='21:00:00',rate=1.00,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=14,name='pd1',descr='平段1',start_time='07:00:00',end_time='10:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=15,name='pd2',descr='平段2',start_time='15:00:00',end_time='18:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=16,name='pd3',descr='平段3',start_time='21:00:00',end_time='23:00:00',rate=0.86,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=17,name='gd1',descr='谷段1',start_time='00:00:00',end_time='07:00:00',rate=0.35,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.merge(Report(id=18,name='gd2',descr='谷段2',start_time='23:00:00',end_time='23:59:59',rate=0.35,years=2022,months='1,2,3,4,5,6,7,8,9,10,11,12',station='halun',op_ts=now,is_use=0))
        user_session.commit()
        user_session.close()

    def __repr__(self):
        
        return "{'id':%s,'name':'%s','descr':'%s','start_time':'%s','end_time':'%s','rate':%s,'months':'%s','op_ts':'%s','is_use':%s,'years':%s}" % (
            self.id,self.name,self.descr,self.start_time,self.end_time,self.rate,self.months,self.op_ts,self.is_use,self.years)
        
    def deleteReport(self,id):
        try:
            user_session.query(Report).filter(Report.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False