#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-24 11:02:39
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_fastmatch_other.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-14 10:32:20


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
class ForecaseFastmatchOther(user_Base):
    u'其他结果速查表'
    __tablename__ = "t_side_forecase_fastmatch_other"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    province_id = Column(Integer, ForeignKey("t_side_forecase_province.id"),nullable=False, comment=u"省份id")
    ele_id = Column(Integer, ForeignKey("t_side_forecase_ele.id"),nullable=False, comment=u"用电分类")
    vol_id = Column(Integer, ForeignKey("t_side_forecase_vol.id"),nullable=False, comment=u"电压等级")
    share_emc = Column(VARCHAR(25), nullable=True, comment=u"EMC比例")
    capacity = Column(VARCHAR(25), nullable=True, comment=u"容量")
    # hope_invest = Column(VARCHAR(25), nullable=True, comment=u"期望总投资")

    cost_count = Column(VARCHAR(255), nullable=False, comment=u"总投造价列表[1.5，1.55，1.6，1.65，1.7，1.75，1.8,1.85,1.9]")
    back_before = Column(VARCHAR(255), nullable=False, comment=u"回本期限 税前 列表[]")
    back_after = Column(VARCHAR(255), nullable=False, comment=u"回本期限 税后 列表[]")
    top_invest = Column(VARCHAR(25), nullable=True, comment=u"总投资上限")
    base_invest = Column(VARCHAR(25), nullable=True, comment=u"总投资基准")
    top_other_invest = Column(VARCHAR(25), nullable=True, comment=u"其他费用基准价格")
    base_other_invest = Column(VARCHAR(25), nullable=True, comment=u"其他费用上限价格")
    master_invest = Column(VARCHAR(25), nullable=True, comment=u"项目主体建设成本")
    base_year = Column(VARCHAR(25), nullable=True, comment=u"基准回本年限")
    top_base_year = Column(VARCHAR(25), nullable=True, comment=u"基准回本年限上线")

    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    province_forecase_fastmatch_other = relationship("ForecaseProvince", backref="province_forecase_fastmatch_other")
    ele_fastmatch_other = relationship("ForecaseEle",backref='ele_fastmatch_other',foreign_keys=[ele_id])
    vol_fastmatch_other = relationship("ForecaseVol",backref='vol_fastmatch_other',foreign_keys=[vol_id])
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'top_invest':'%s','base_invest':'%s','top_other_invest':'%s','base_other_invest':'%s','master_invest':'%s','base_year':'%s','top_base_year':'%s'}" %(
            self.top_invest,self.base_invest,self.top_other_invest,self.base_other_invest,self.master_invest,self.base_year,self.top_base_year)

   