package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsCreateDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsQueryDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsUpdateDTO;
import com.robestec.analysis.entity.TPlanPowerRecords;
import com.robestec.analysis.mapper.TPlanPowerRecordsMapper;
import com.robestec.analysis.service.TPlanPowerRecordsService;
import com.robestec.analysis.vo.TPlanPowerRecordsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率计划关联记录服务实现类
 */
@Slf4j
@Service
public class TPlanPowerRecordsServiceImpl extends SuperServiceImpl<TPlanPowerRecordsMapper, TPlanPowerRecords>
        implements TPlanPowerRecordsService {

    @Override
    public PageResult<TPlanPowerRecordsVO> queryTPlanPowerRecords(TPlanPowerRecordsQueryDTO queryDTO) {
        LambdaQueryWrapper<TPlanPowerRecords> wrapper = new LambdaQueryWrapper<TPlanPowerRecords>()
                .eq(queryDTO.getPlanId() != null, TPlanPowerRecords::getPlanId, queryDTO.getPlanId())
                .eq(queryDTO.getPowerId() != null, TPlanPowerRecords::getPowerId, queryDTO.getPowerId())
                .eq(queryDTO.getSerialNumber() != null, TPlanPowerRecords::getSerialNumber, queryDTO.getSerialNumber())
                .eq(queryDTO.getIsUse() != null, TPlanPowerRecords::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TPlanPowerRecords::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TPlanPowerRecords::getCreateTime, queryDTO.getEndTime())
                .orderByAsc(TPlanPowerRecords::getSerialNumber)
                .orderByDesc(TPlanPowerRecords::getCreateTime);

        Page<TPlanPowerRecords> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TPlanPowerRecords> result = this.page(page, wrapper);

        List<TPlanPowerRecordsVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPlanPowerRecordsVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTPlanPowerRecords(TPlanPowerRecordsCreateDTO createDTO) {
        // 检查计划ID和功率ID的关联是否已存在
        long count = this.count(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPlanId, createDTO.getPlanId())
                .eq(TPlanPowerRecords::getPowerId, createDTO.getPowerId()));
        if (count > 0) {
            throw new RuntimeException("该计划和功率记录的关联已存在");
        }

        TPlanPowerRecords entity = BeanUtil.copyProperties(createDTO, TPlanPowerRecords.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTPlanPowerRecords(TPlanPowerRecordsUpdateDTO updateDTO) {
        TPlanPowerRecords entity = BeanUtil.copyProperties(updateDTO, TPlanPowerRecords.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTPlanPowerRecords(Long id) {
        this.removeById(id);
    }

    @Override
    public TPlanPowerRecordsVO getTPlanPowerRecords(Long id) {
        TPlanPowerRecords entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTPlanPowerRecordsList(List<TPlanPowerRecordsCreateDTO> createDTOList) {
        List<TPlanPowerRecords> entityList = BeanUtil.copyToList(createDTOList, TPlanPowerRecords.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TPlanPowerRecordsVO> getTPlanPowerRecordsByPlanId(Long planId) {
        List<TPlanPowerRecords> entityList = this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPlanId, planId)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPlanPowerRecordsVO> getTPlanPowerRecordsByPowerId(Long powerId) {
        List<TPlanPowerRecords> entityList = this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPowerId, powerId)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TPlanPowerRecordsVO> getTPlanPowerRecordsBySerialNumber(Integer serialNumber) {
        List<TPlanPowerRecords> entityList = this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getSerialNumber, serialNumber)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByPlanId(Long planId) {
        return this.count(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPlanId, planId));
    }

    @Override
    public Long countByPowerId(Long powerId) {
        return this.count(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPowerId, powerId));
    }

    /**
     * 转换为VO对象
     */
    private TPlanPowerRecordsVO convertToVO(TPlanPowerRecords entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, TPlanPowerRecordsVO.class);
    }

    // ==================== 从Mapper迁移的方法 ====================

    /**
     * 根据功率ID查询计划关联记录
     */
    public List<TPlanPowerRecords> selectByPowerId(Long powerId) {
        return this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPowerId, powerId)
                .eq(TPlanPowerRecords::getIsUse, 1)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
    }

    /**
     * 根据计划ID查询功率关联记录
     */
    public List<TPlanPowerRecords> selectByPlanId(Long planId) {
        return this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPlanId, planId)
                .eq(TPlanPowerRecords::getIsUse, 1)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
    }

    /**
     * 根据功率ID和计划ID查询关联记录
     */
    public TPlanPowerRecords selectByPowerIdAndPlanId(Long powerId, Long planId) {
        return this.getOne(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPowerId, powerId)
                .eq(TPlanPowerRecords::getPlanId, planId)
                .eq(TPlanPowerRecords::getIsUse, 1));
    }

    /**
     * 根据功率ID列表软删除记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean softDeleteByPowerIds(List<Long> powerIds) {
        if (powerIds == null || powerIds.isEmpty()) {
            return false;
        }
        return this.update(Wrappers.<TPlanPowerRecords>lambdaUpdate()
                .in(TPlanPowerRecords::getPowerId, powerIds)
                .set(TPlanPowerRecords::getIsUse, 0));
    }

    /**
     * 根据计划ID列表软删除记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean softDeleteByPlanIds(List<Long> planIds) {
        if (planIds == null || planIds.isEmpty()) {
            return false;
        }
        return this.update(Wrappers.<TPlanPowerRecords>lambdaUpdate()
                .in(TPlanPowerRecords::getPlanId, planIds)
                .set(TPlanPowerRecords::getIsUse, 0));
    }

    /**
     * 根据序号查询记录
     */
    public List<TPlanPowerRecords> selectBySerialNumber(Integer serialNumber) {
        return this.list(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getSerialNumber, serialNumber)
                .eq(TPlanPowerRecords::getIsUse, 1)
                .orderByDesc(TPlanPowerRecords::getCreateTime));
    }

    /**
     * 统计功率ID的关联记录数量
     */
    public int countRecordsByPowerId(Long powerId) {
        return Math.toIntExact(this.count(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPowerId, powerId)
                .eq(TPlanPowerRecords::getIsUse, 1)));
    }

    /**
     * 统计计划ID的关联记录数量
     */
    public int countRecordsByPlanId(Long planId) {
        return Math.toIntExact(this.count(Wrappers.<TPlanPowerRecords>lambdaQuery()
                .eq(TPlanPowerRecords::getPlanId, planId)
                .eq(TPlanPowerRecords::getIsUse, 1)));
    }
}
