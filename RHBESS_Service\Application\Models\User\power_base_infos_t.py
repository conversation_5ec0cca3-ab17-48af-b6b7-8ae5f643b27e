#!/usr/bin/env python
# coding=utf-8
# @Information:

from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Column, DateTime, Float, Text, ForeignKey, VARCHAR


class PowerBaseInfos(user_Base):
    u'基准功率详情表'
    __tablename__ = "t_power_base_infos"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    base_id = Column(Integer, ForeignKey("t_power_base.id"), nullable=False, comment=u"基准功率基础表主键")
    start_time = Column(VARCHAR(50), nullable=False, comment=u"开始时间HH:MM")
    end_time = Column(VARCHAR(50), nullable=False, comment=u"结束时间HH:MM")
    cd_flag = Column(Integer, nullable=False, comment=u"充放标识 - 1充0静置1放电")
    pv_flag = Column(Integer, nullable=False, comment=u" 峰谷标识 - 2深谷 - 1谷0平1峰2尖峰")
    power = Column(Float, nullable=False, comment=u"功率值")
    remark = Column(Text, nullable=True, comment=u"备注")
    en_remark = Column(Text, nullable=True, comment=u"备注")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'start_time':'%s','end_time':'%s','base_id':'%s','cd_flag':'%s','pv_flag':'%s','power':'%s'," \
               "'remark':'%s','en_remark':'%s'}" % (
               self.id, self.start_time, self.end_time, self.base_id, self.cd_flag, self.pv_flag, self.power,
               self.remark, self.en_remark)
        return bean.replace("None", '')




