package com.robestec.analysis.dto.tuserstrategycategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户策略分类更新DTO
 */
@Data
@ApiModel("用户策略分类更新DTO")
public class TUserStrategyCategoryUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "策略ID", required = true)
    @NotNull(message = "策略ID不能为空")
    private Long strategyId;

    @ApiModelProperty(value = "充电配置(JSON格式)")
    private String chargeConfig;

    @ApiModelProperty(value = "是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty(value = "RL列表(JSON格式)")
    private String rlList;

    @ApiModelProperty(value = "解释JSON(JSON格式)")
    private String explainJson;

    @ApiModelProperty(value = "PV列表(JSON格式)")
    private String pvList;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
