#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-01 15:45:57
#@FilePath     : \RHBESS_Service\Application\Models\User\custom_report_t.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-02 12:02:50
import json
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from sqlalchemy.databases import mysql


class ReportCustom(user_Base):
    u'自定义报表输入记录'
    __tablename__ = "t_report_custom"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    title = Column(Text, nullable=False, comment=u"表头")
    table_list = Column(mysql.MEDIUMTEXT, nullable=True,comment=u"表内容")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    flag = Column(Integer,nullable=True,comment=u"1周报2月报3年报")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
    type = Column(CHAR(1), nullable=True,comment=u"储能类型1火储2工商3集中式")
    sys_availty = Column(VARCHAR(10), nullable=True,comment=u"可用率")
    agc = Column(VARCHAR(200), nullable=True,comment=u"AGC投入时间统计")

    year = Column(VARCHAR(5), nullable=True,comment=u"年")
    month = Column(VARCHAR(10), nullable=True,comment=u"月")
    day = Column(VARCHAR(10), nullable=True,comment=u"运行天数")
    week = Column(VARCHAR(10), nullable=True,comment=u"第几周")
    user = Column(VARCHAR(100), nullable=True,comment=u"执勤人")
    is_use = Column(CHAR(2), nullable=False, comment=u"是否使用1是0否")

    en_user = Column(VARCHAR(100), nullable=True, comment=u"英文执勤人")
    en_title = Column(Text, nullable=False, comment=u"表头")
    en_table_list = Column(mysql.MEDIUMTEXT, nullable=True, comment=u"表内容")


       
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'title':'%s','table_list':'%s','op_ts':'%s','flag':'%s','station':'%s','type':'%s','sys_availty':'%s','agc':'%s','year':'%s','month':'%s'," \
               "'day':%s,'week':'%s','user':'%s','is_use':'%s','en_user':'%s','en_title':'%s','en_table_list':'%s'}" % (self.id, self.title, self.table_list, self.op_ts, self.flag, self.station, self.type,
                   self.sys_availty, self.agc, self.year, self.month, self.day, self.week, self.user, self.is_use,
                   self.en_user, self.en_title, self.en_table_list)
        return bean.replace("None", '')


    # def replace_en_fields(data, new_key_prefix):
    #     return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}
        

