package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.robestec.analysis.entity.TPanLogs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 下发日志记录Mapper
 * 对应Python中的TPanLogs相关数据库操作
 */
@Mapper
public interface TPanLogsMapper extends BaseMapper<TPanLogs> {

    /**
     * 分页查询下发记录列表
     * 对应Python中GetPlanHis方法的查询逻辑
     */
    @Select("<script>" +
            "SELECT * FROM t_pan_logs " +
            "WHERE is_use = 1 " +
            "<if test='station != null and station != \"\"'>" +
            "  AND station LIKE CONCAT('%', #{station}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "  AND status = #{status} " +
            "</if>" +
            "<if test='typeNames != null and typeNames.size() > 0'>" +
            "  AND type_name IN " +
            "  <foreach collection='typeNames' item='typeName' open='(' separator=',' close=')'>" +
            "    #{typeName}" +
            "  </foreach>" +
            "</if>" +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND create_time <= #{endTime} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<TPanLogs> selectPlanHistoryList(
            Page<TPanLogs> page,
            @Param("station") String station,
            @Param("status") Integer status,
            @Param("typeNames") List<String> typeNames,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 查询所有下发记录（用于导出）
     * 对应Python中planHisExport方法的查询逻辑
     */
    @Select("<script>" +
            "SELECT * FROM t_pan_logs " +
            "WHERE is_use = 1 " +
            "<if test='station != null and station != \"\"'>" +
            "  AND station LIKE CONCAT('%', #{station}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "  AND status = #{status} " +
            "</if>" +
            "<if test='typeNames != null and typeNames.size() > 0'>" +
            "  AND type_name IN " +
            "  <foreach collection='typeNames' item='typeName' open='(' separator=',' close=')'>" +
            "    #{typeName}" +
            "  </foreach>" +
            "</if>" +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND create_time <= #{endTime} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<TPanLogs> selectAllPlanHistory(
            @Param("station") String station,
            @Param("status") Integer status,
            @Param("typeNames") List<String> typeNames,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 根据用户ID查询下发记录
     */
    @Select("SELECT * FROM t_pan_logs " +
            "WHERE user_id = #{userId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPanLogs> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据电站名称查询下发记录
     */
    @Select("SELECT * FROM t_pan_logs " +
            "WHERE station = #{station} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPanLogs> selectByStation(@Param("station") String station);

    /**
     * 根据类型名称查询下发记录
     */
    @Select("SELECT * FROM t_pan_logs " +
            "WHERE type_name = #{typeName} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPanLogs> selectByTypeName(@Param("typeName") String typeName);

    /**
     * 统计下发记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_pan_logs " +
            "WHERE is_use = 1 " +
            "<if test='status != null'>" +
            "  AND status = #{status} " +
            "</if>" +
            "<if test='userId != null'>" +
            "  AND user_id = #{userId} " +
            "</if>" +
            "</script>")
    int countLogs(@Param("status") Integer status, @Param("userId") Long userId);

    /**
     * 根据时间范围统计下发记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_pan_logs " +
            "WHERE is_use = 1 " +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND create_time <= #{endTime} " +
            "</if>" +
            "</script>")
    int countByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
