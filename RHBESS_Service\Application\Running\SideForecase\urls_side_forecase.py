# -*- coding=utf-8 -*-
#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_Service\Application\Running\SideForecase\urls_side_forecase.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-13 16:11:55



from tornado.routing import Rule, PathMatches
from tornado.web import url

from Application.EqAccount.SideForecase.sideForecasePolicyHandle import SideForecasePolicyHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseUserHandle import SideForecaseUserHandleIntetface,SideForecaseGroupUserHandle,SideForecaseUserLoginHandler
from Application.EqAccount.SideForecase.sideForecaseMarketHandle import SideForecaseMarketHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseCalculateHandle import SideForecaseCalculateHandleIntetface

from Application.EqAccount.SideForecase.sideForecaseCounterHandle import SideForecaseCounterHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseRecordHandle import SideForecaseRecordHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseProjectHandle import SideForecaseProjectHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseDictHandle import SideForecaseDictHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseDictHandle import SideForecaseDictHandleIntetface,SideForecaseFastMatchHandleIntetface

routes = [
    # 用户交易价格查询
    url(r"/SideForecaseUser/(\w+)", SideForecaseUserHandleIntetface),
    # 现货交易价格
    url(r"/SideForecaseMarket/(\w+)", SideForecaseMarketHandleIntetface),
    # 预测
    url(r"/SideForecaseCalculate/(\w+)", SideForecaseCalculateHandleIntetface),

    # 登录
    url(r"/SideForecaseLogin/(\w+)", SideForecaseUserLoginHandler),
    # 角色&权限&用户管理
    url(r"/SideForecaseGroupUser/(\w+)", SideForecaseGroupUserHandle),

    # 计算器
    url(r"/SideForecaseCounter/(\w+)", SideForecaseCounterHandleIntetface),
    # 档案资料
    url(r"/SideForecaseRecord/(\w+)", SideForecaseRecordHandleIntetface),
    # 项目管理
    url(r"/SideForecaseProject/(\w+)", SideForecaseProjectHandleIntetface),
    # 字典类管理
    url(r"/SideForecaseDict/(\w+)", SideForecaseDictHandleIntetface),
    # 速查管理
    url(r"/SideForecaseFastMatch/(\w+)", SideForecaseFastMatchHandleIntetface),
    # 政策情报
    url(r"/SideForecasePolicy/(\w+)", SideForecasePolicyHandleIntetface),
]
