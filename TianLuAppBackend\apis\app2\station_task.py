import logging
import os

from tools.count import new_get_price_v2, get_station_price, get_station_price_optimize

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "TianLuAppBackend.settings")
import datetime
import pymysql
from django.db.models import Q, Max
from django.conf import settings
from django_redis import get_redis_connection
from django.db import connections,close_old_connections
from apis.user import models
from apis.user.models import StationBaseIncome, StationBase, FormerBase, StationDetails, StationStatus, StationBaseNew, FormerBaseNew, PeakValleyNew
from settings.meter_settings import METER_DIC_IDC
from dbutils.persistent_db import PersistentDB
from settings.alarm_settings import alarm_detail as alarm_detail_v2
from settings.alarm_settings_v3 import alarm_detail as alarm_detail_v3

# 连接数据库
pool = PersistentDB(pymysql, 10, **{
    "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
    "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
    "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
    "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
    "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
    "cursorclass": pymysql.cursors.DictCursor
})

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def station_satatus_task():
    station_obj = models.StationDetails.objects.filter(master_station__is_delete=0, is_delete=0).values(
        "id",
        "app",
        "english_name",
        "station_name",
    )

    # conn = get_redis_connection('3')  # 连接redis

    data_create_list = []
    data_update_list = []
    close_old_connections()  # 关闭旧链接
    for station in station_obj:
        # 查询是否有离线事件
        Offline_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=4, status=0, station_id=station["id"]).exists()
        if Offline_exist:
            station["station_status"] = 4
        else:
            # 查询是否有告警事件，告警和警告为正常
            alarm_exist = models.FaultAlarm.objects.using('alarm_module').values('station_id').filter(~Q(type=3),~Q(type=2),status=0,station_id=station["id"]).annotate(max_type=Max('type'))
            if alarm_exist:
                type_ = alarm_exist[0].get('max_type')
                if type_ == 1:  #故障
                    station["station_status"] = 3
                elif type_ == 0:
                    station["station_status"] = 1
                else:
                    station["station_status"] = type_
            else:
                station["station_status"] = 1


        station_status_res = StationStatus.objects.filter(station__id=station["id"]).first()
        if station_status_res:
            station_status_res.status = station["station_status"]
            station_status_res.create_time = datetime.datetime.now()
            data_update_list.append(station_status_res)
        else:
            station_status = StationStatus()
            station_status.station_id = station["id"]
            station_status.status = station["station_status"]
            data_create_list.append(station_status)

    if data_create_list:
        StationStatus.objects.bulk_create(data_create_list)
    if data_update_list:
        StationStatus.objects.bulk_update(data_update_list, ['status', 'create_time'])

    return 1


def station_base_income_day(s_time=None, e_time=None):
    """
    写入基准充放电量和收益表
    :return:
    """
    if not s_time:
        day_time = datetime.date.today()
        r_count = 1
    else:
        day_time = datetime.datetime.strptime(s_time, '%Y-%m-%d').date()
        end_time = datetime.datetime.strptime(e_time, '%Y-%m-%d').date()
        r_count = (end_time - day_time).days
    data = StationDetails.objects.filter(is_delete=0).all()  # 查询所有站信息
    # 查询当月所有电价
    day_price_res = models.PeakValleyNew.objects.filter(year_month=day_time.strftime("%Y-%m")).order_by('moment').all()
    price_new_dict = {}
    for price in day_price_res:
        k = f"{price.province_id}-{price.type}-{price.level}"  # 定义唯一key
        # moment = (datetime.datetime.strptime(price.moment, '%H:%M') + datetime.timedelta(hours=1)).strftime('%H:%M')
        moment = price.moment
        if price_new_dict.get(k):
            price_new_dict[k][moment] = price
        else:
            price_new_dict[k] = {
                moment: price
            }
    for station in data:
        # 查询该站下理论充放字典
        station_base = StationBaseNew.objects.filter(station_id=station.id).all()
        name = station.english_name  # 站英文标识
        former_ids = [base.former_base_id for base in station_base]
        income_list = []
        if former_ids:
            if r_count != 1:
                for d in range(r_count):
                    day_time1 = day_time + datetime.timedelta(days=d)
                    month = f"{day_time1.year}-{day_time1.month}" if day_time1.month >= 10 else f"{day_time1.year}-0{day_time1.month}"
                    former_data = FormerBaseNew.objects.filter(id__in=former_ids, year_month=month).all()
                    former_dict = {i.moment: i for i in former_data}
                    chag = 0  # 日充电
                    disg = 0  # 日放电
                    income = 0  # 日收益
                    # 考虑存在单位电价的情况
                    user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=day_time1,
                                                                 end__gte=day_time1,
                                                                 delete=0)

                    if user_price.exists():
                        user_price = user_price.last()
                        price_dic = {
                            "2_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                            "1_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                            "0_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                            "-1_chag_price": float(
                                user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                            "-2_chag_price": float(
                                user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                            "2_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                            "1_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                            "0_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                            "-1_disg_price": float(
                                user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                            "-2_disg_price": float(
                                user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
                        }
                    else:
                        price_dic = None

                    price_dict = price_new_dict.get(f"{station.province_id}-{station.type}-{station.level}")
                    if former_dict and price_dict:
                        for k, y in price_dict.items():  # k: 时刻; y：电价信息
                            former = former_dict.get(k)
                            if price_dic:  # 单位电价
                                chag_price, disg_price = price_dic[f"{str(y.pv)}_chag_price"], price_dic[f"{str(y.pv)}_disg_price"]
                            else:  # 电价
                                chag_price = disg_price = y.price

                            power_value = former.power_value / 4
                            if former.mark == -1:  # 充电
                                chag += power_value
                                income -= power_value * float(chag_price)
                            elif former.mark == 1:  # 放电
                                disg += power_value
                                income += power_value * float(disg_price)
                            else:
                                continue
                    station_base_income = StationBaseIncome()
                    station_base_income.station_name = name
                    station_base_income.day = str(day_time1)
                    station_base_income.day_income = round(income, 3)
                    station_base_income.day_chag = round(chag, 3)
                    station_base_income.day_disg = round(disg, 3)
                    income_list.append(station_base_income)
            else:
                chag = 0  # 日充电
                disg = 0  # 日放电
                income = 0  # 日收益
                month = f"{day_time.year}-{day_time.month}" if day_time.month >= 10 else f"{day_time.year}-0{day_time.month}"
                former_data = FormerBaseNew.objects.filter(id__in=former_ids, year_month=month).order_by('moment').all()
                former_dict = {i.moment: i for i in former_data}  # 一天的策略信息
                price_dict = price_new_dict.get(f"{station.province_id}-{station.type}-{station.level}")  # 一天的电价信息

                # 考虑存在单位电价的情况
                user_price = models.UnitPrice.objects.filter(station=station.master_station, start__lte=day_time,
                                                             end__gte=day_time,
                                                             delete=0)

                if user_price.exists():
                    user_price = user_price.last()
                    price_dic = {
                        "2_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                        "1_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                        "0_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                        "-1_chag_price": float(
                            user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                        "-2_chag_price": float(
                            user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                        "2_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                        "1_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                        "0_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                        "-1_disg_price": float(
                            user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                        "-2_disg_price": float(
                            user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
                    }
                else:
                    price_dic = None
                if former_dict and price_dict:
                    for k, y in price_dict.items():  # k: 时刻; y：电价信息
                        former = former_dict.get(k)
                        if price_dic:  # 单位电价
                            chag_price, disg_price = price_dic[f"{str(y.pv)}_chag_price"], price_dic[
                                f"{str(y.pv)}_disg_price"]
                        else:  # 电价
                            chag_price = disg_price = y.price

                        power_value = former.power_value / 4
                        if former.mark == -1:  # 充电
                            chag += power_value
                            income -= power_value * float(chag_price)
                        elif former.mark == 1:  # 放电
                            disg += power_value
                            income += power_value * float(disg_price)
                        else:
                            continue
                station_base_income = StationBaseIncome()
                station_base_income.station_name = name
                station_base_income.day = str(day_time)
                station_base_income.day_income = round(income, 3)
                station_base_income.day_chag = round(chag, 3)
                station_base_income.day_disg = round(disg, 3)
                income_list.append(station_base_income)

        StationBaseIncome.objects.bulk_create(income_list)

    return 1


# def timing_chgdig_s_day(s_time=None, e_time=None):
#     """冻结逐天充放电"""
#     conn = pool.connection()
#     cursor = conn.cursor()
#     # 处理开始时间, 计算循环天数
#     if not s_time:
#         start_time_originate = (datetime.datetime.now() - datetime.timedelta(days=2)).replace(hour=23, minute=59,
#                                                                                               second=30)
#         r_count = 1
#     else:
#         start_time_originate = datetime.datetime.strptime(s_time, '%Y-%m-%d')
#         end_time = datetime.datetime.strptime(e_time, '%Y-%m-%d')
#         r_count = (end_time - start_time_originate).days
#         start_time_originate = start_time_originate.replace(hour=23, minute=59, second=30) - datetime.timedelta(days=1)
#     now_time = datetime.datetime.now()
#     stations_ins = models.StationDetails.objects.exclude(id__in=[9, 10]).all()  # 查询总的站
#     # stations_ins = models.StationDetails.objects.exclude(id__in=[9, 10]).filter(english_name='GLTY201').all()  # 查询总的站
#     report_day_list = []  # 逐日充放数据
#     for station in stations_ins:
#         logging.warning('执行-{}-中'.format(station.english_name))
#         if station.create_time < now_time:
#             unit = models.Unit.objects.values("bms", "pcs").filter(station_id=station.id).all()  # 取单元的bms pcs
#             if unit:
#                 meter_type = station.meter_type
#                 chag_n = METER_DIC_IDC[meter_type]['charge']
#                 disg_n = METER_DIC_IDC[meter_type]['discharge']
#                 for r in range(r_count):
#                     station_chag = 0
#                     station_disg = 0
#                     start_time = start_time_originate + datetime.timedelta(days=r)
#                     end_time = start_time_originate.replace(hour=00, minute=00, second=30) + datetime.timedelta(
#                         days=r + 2)
#                     day = start_time + datetime.timedelta(days=1)
#                     day_ = day.strftime('%Y-%m-%d')
#                     for info in unit:
#                         report_day = models.FReportDay()
#                         if meter_type == 1 or meter_type == 2:
#                             table_name = 'dwd_cumulant_bms_data_storage'
#                             chag, disg = get_history_data_f_day(chag_n, disg_n, table_name, info['bms'],
#                                                                 station.english_name, start_time, end_time, cursor)
#                             soc_data = models.FReport.objects.filter(station=station.english_name,
#                                                                      unit_name=info['bms'],
#                                                                      day=day_, station_type='2').all()

#                             report_day.unit_name = info['bms']

#                         elif station.meter_type == 3:
#                             table_name = 'dwd_cumulant_pcs_data_storage'
#                             chag, disg = get_history_data_f_day(chag_n, disg_n, table_name, info['pcs'],
#                                                                 station.english_name, start_time, end_time, cursor)
#                             soc_data = models.FReport.objects.filter(station=station.english_name,
#                                                                      unit_name=info['pcs'],
#                                                                      day=day_, station_type='2').all()
#                             report_day.unit_name = info['pcs']
#                         chag = 0 if chag == 0.0 else float(chag)
#                         disg = 0 if disg == 0.0 else float(disg)
#                         chag_soc = 0
#                         disg_soc = 0
#                         chag_hours = 0
#                         disg_hours = 0
#                         for soc in soc_data:
#                             if chag > 500:
#                                 chag_hours += float(soc.chag)
#                             if disg > 500:
#                                 disg_hours += float(soc.disg)
#                             v = float(soc.soc)
#                             if v < 0:
#                                 disg_soc += v
#                             else:
#                                 chag_soc += v
#                         report_day.day = day_
#                         report_day.station = station.english_name
#                         report_day.app = station.app
#                         report_day.chag = round(chag_hours, 2) if chag > 500 else chag
#                         report_day.disg = round(disg_hours, 2) if disg > 500 else disg
#                         report_day.station_type = 2
#                         report_day.chag_soc = round(chag_soc, 2)
#                         report_day.disg_soc = round(abs(disg_soc), 2)
#                         report_day_list.append(report_day)
#                         station_chag += round(chag_hours, 2) if chag > 500 else chag
#                         station_disg += round(disg_hours, 2) if disg > 500 else disg
#                     soc_info = models.FReport.objects.filter(station=station.english_name, day=day_,
#                                                              station_type='1').all()
#                     soc_v_chag = 0
#                     soc_v_disg = 0
#                     if soc_info:
#                         for info in soc_info:
#                             soc_v = float(info.soc)
#                             if soc_v > 0:
#                                 soc_v_chag += soc_v
#                             else:
#                                 soc_v_disg += soc_v
#                     station_report_day = models.FReportDay()
#                     station_report_day.day = day_
#                     station_report_day.station = station.english_name
#                     station_report_day.app = station.app
#                     station_report_day.chag = round(station_chag, 2)
#                     station_report_day.disg = round(station_disg, 2)
#                     station_report_day.station_type = 1
#                     station_report_day.chag_soc = round(soc_v_chag, 2)
#                     station_report_day.disg_soc = round(abs(soc_v_disg), 2)
#                     station_report_day.unit_name = 0
#                     report_day_list.append(station_report_day)

#                     if len(report_day_list) >= 1000:
#                         models.FReportDay.objects.bulk_create(report_day_list)
#                         report_day_list = []
#                         logging.warning('写入1000条数据！')

#     if len(report_day_list) > 0:
#         models.FReportDay.objects.bulk_create(report_day_list)

#     return 1


def get_history_data_f_day(chag_n, disg_n, table_name, device, station_name, start_time, end_time, cursor):
    """
    查询天历史数据充放电量
    :param chag_n: 充电
    :param disg_n: 放电
    :param table_name: 表明
    :param device: bms/pcs
    :param station_name: 站名
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param cursor: 数据库连接
    :return:
    """
    try:
        # 执行SQL查询
        sql = """SELECT {},{},time
                    FROM {} 
                    WHERE 1=1
                    and (type=1 or type is NULL)
                    and {} is not NULL 
                    and device='{}'
                    and station_name='{}'
                    and time BETWEEN '{}' AND '{}'
                    ORDER BY time ASC
                    """.format(
            chag_n, disg_n, table_name, chag_n, device, station_name, start_time, end_time
        )
        try:
            cursor.execute(sql)
        except Exception as e:
            error_log.error(e)

        # 获取查询结果
        result = cursor.fetchall()
        # 处理查询结果
        chag, disg = 0, 0
        count = len(result)
        if count <= 2:
            chag = 0.0
            disg = 0.0
        else:
            # 判断60秒区间内没有值的话，往前计算两个小时的最后一条数据作为计算数据
            if not start_time <= result[0]['time'] <= start_time + datetime.timedelta(seconds=60):
                # 执行SQL查询
                sql = """SELECT {},{}
                            FROM {} 
                            WHERE 1=1
                            and (type=1 or type is NULL)
                            and {} is not NULL 
                            and device='{}'
                            and station_name='{}'
                            and time BETWEEN '{}' AND '{}'
                            ORDER BY time DESC LIMIT 1
                            """.format(
                    chag_n, disg_n, table_name, chag_n, device, station_name, start_time - datetime.timedelta(hours=2),
                    start_time
                )

                try:
                    cursor.execute(sql)
                except Exception as e:
                    error_log.error(e)

                result1 = cursor.fetchone()
                if result1:
                    chag = abs(result[count - 1][chag_n] - result1[chag_n])
                    disg = abs(result[count - 1][disg_n] - result1[disg_n])
            else:
                chag = abs(result[count - 1][chag_n] - result[0][chag_n])
                disg = abs(result[count - 1][disg_n] - result[0][disg_n])

        return round(chag, 2), round(disg, 2)
    except Exception as e:
        error_log.error(e)


def station_fault_alarm():
    """
    新增并网点匹配故障信息
    :return:
    """
    now_time = datetime.datetime.now()
    start_time = now_time - datetime.timedelta(minutes=50)
    station_res = models.StationDetails.objects.filter(create_time__gte=start_time, is_delete=0).all()
    conn = get_redis_connection('3')  # 连接redis
    for station in station_res:
        alarm_detail = alarm_detail_v3 if station.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2
        data = []
        units = station.unit_set.filter(is_delete=0).all()
        ems = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', station.english_name, "EMS"))
        if ems:
            ems = eval(eval(ems))
            for k, v in ems.items():
                if k in alarm_detail:
                    if int(v) == 1:
                        detail = alarm_detail.get(k)
                        alarm = models.FaultAlarm()
                        alarm.station_id = station.id
                        alarm.point = k
                        alarm.status = 0
                        alarm.type = detail.get('type')
                        alarm.details = detail.get('detail')
                        alarm.note = '未恢复'
                        alarm.device = 'EMS'
                        alarm.device_another_name = 'EMS'
                        alarm.start_time = now_time.strftime('%Y-%m-%d %H:%M:%S')
                        alarm.joint_primary_key = f"{station.id}_EMS_{detail.get('type')}_{k}_{now_time.strftime('%Y-%m-%d %H:%M:%S')}"
                        data.append(alarm)

        for unit in units:
            bms = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', station.english_name, unit.bms))
            pcs = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', station.english_name, unit.pcs))
            if bms:
                bms = eval(eval(bms))
                for k, v in bms.items():
                    if k in alarm_detail:
                        if int(v) == 1:
                            detail = alarm_detail.get(k)
                            alarm = models.FaultAlarm()
                            alarm.station_id = station.id
                            alarm.point = k
                            alarm.status = 0
                            alarm.type = detail.get('type')
                            alarm.details = detail.get('detail')
                            alarm.note = '未恢复'
                            alarm.start_time = now_time.strftime('%Y-%m-%d %H:%M:%S')
                            alarm.joint_primary_key = f"{station.id}_{unit.bms}_{detail.get('type')}_{k}_{now_time.strftime('%Y-%m-%d %H:%M:%S')}"
                            alarm.device = unit.bms
                            alarm.device_another_name = unit.bms[:3] + unit.unit_new_name[
                                -1] if unit.unit_new_name else unit.bms
                            data.append(alarm)
            if pcs:
                pcs = eval(eval(pcs))
                for k, v in pcs.items():
                    if k in alarm_detail:
                        if int(v) == 1:
                            detail = alarm_detail.get(k)
                            alarm = models.FaultAlarm()
                            alarm.station_id = station.id
                            alarm.point = k
                            alarm.status = 0
                            alarm.type = detail.get('type')
                            alarm.details = detail.get('detail')
                            alarm.note = '未恢复'
                            alarm.start_time = now_time.strftime('%Y-%m-%d %H:%M:%S')
                            alarm.joint_primary_key = f"{station.id}_{unit.pcs}_{detail.get('type')}_{k}_{now_time.strftime('%Y-%m-%d %H:%M:%S')}"
                            alarm.device = unit.pcs
                            alarm.device_another_name = unit.pcs[:3] + unit.unit_new_name[
                                -1] if unit.unit_new_name else unit.pcs
                            data.append(alarm)
        try:
            models.FaultAlarm.objects.using('alarm_module').bulk_create(data)
        except Exception as e:
            logging.error(f'fault_alarm--{now_time}：{station.english_name} 执行失败！,{e}')
        logging.warning(f'fault_alarm--{now_time}：{station.english_name} 执行完成！')

    return 1


def station_efficiency():
    """
    主站充放电效率
    :return:
    """
    stations = models.MaterStation.objects.filter(is_delete=0).all()
    pool = PersistentDB(pymysql, 10, **{
        "host": settings.DATABASES['doris_ads_rhyc']['HOST'],  # 数据库主机地址
        "user": settings.DATABASES['doris_ads_rhyc']['USER'],  # 数据库用户名
        "password": settings.DATABASES['doris_ads_rhyc']['PASSWORD'],  # 数据库密码
        "database": settings.DATABASES['doris_ads_rhyc']['NAME'],  # 数据库名称
        "port": settings.DATABASES['doris_ads_rhyc']['PORT'],
        "cursorclass": pymysql.cursors.DictCursor
    })
    conn = pool.connection()
    ads_cursor = conn.cursor()
    # with connections['doris_ads_rhyc'].cursor() as ads_cursor:
    for station in stations:
        station_slaves = station.stationdetails_set.filter(is_delete=0).all()
        station_list = [i.english_name for i in station_slaves]
        if len(station_list) == 1:
            station_list.append(-1)  # 防止in查询错误

        # if station.is_account:
        #     discharg_sql = """
        #              SELECT
        #                  sum( chag ) as disg,
        #                  sum( disg ) as chag,
        #                  avg( chag_soc ),
        #                  avg( disg_soc )
        #              FROM
        #                  ads_report_ems_chag_disg_cw_cm_cy
        #              WHERE
        #                  date_type = 'year'
        #              AND
        #                  station = '{}'
        #                  """.format(station.english_name)
        # else:
        #     # 切换查询IDC库
        #     discharg_sql = """
        #                 SELECT
        #                     sum( chag ),
        #                     sum( disg ),
        #                     avg( chag_soc ),
        #                     avg( disg_soc )
        #                 FROM
        #                     ads_report_chag_disg_cy
        #                 WHERE
        #                     station in {}
        #                     AND station_type = 1""".format(tuple(station_list))

        discharg_sql = """
                            SELECT
                                sum( v_chag ),
                                sum( v_disg ),
                                avg( chag_soc ),
                                avg( disg_soc )
                            FROM
                                ads_report_chag_disg_union_cw_cm_cq_cy
                            WHERE
                                date_type='year'
                                AND station_type <= 1"""

        if len(station_list) == 1:
            discharg_sql += f' AND station = "{station_list[0]}"'
        else:
            discharg_sql += f' AND station in {tuple(station_list)}'

        # 获取查询结果
        try:
            ads_cursor.execute(discharg_sql)
            res = ads_cursor.fetchone()
            res = [i for i in res.values()]
        except Exception as e:
            error_log.error(discharg_sql)
            error_log.error(f'计算并网点-{station.english_name}: 充放电效率失败: {e}')
        if res and 0 not in res and None not in res:
            eff = res[1] / res[3] / (res[0] / res[2]) * 100
            if eff > 100:
                station.efficiency = '--'
            elif eff < 85:
                station.efficiency = 85
            else:
                station.efficiency = round(eff, 2)
        else:
            station.efficiency = '--'
        station.update_time = datetime.datetime.now()
        station.save()
    conn.close()
    return 1


def station_statistics_income(start_time=None, end_time=None):
    """
    计算每日尖峰平谷收益
    :param start_time: 开始时间
    :param end_time: 结束时间（不包含本日）
    :return:
    """
    # 处理开始结束时间
    if not start_time:
        # start_time = (datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0)
        now = datetime.datetime.now()
        if now.hour == 0:
            start_time = datetime.datetime.now().replace(hour=0, minute=0, second=0) - datetime.timedelta(days=1)
        else:
            start_time = datetime.datetime.now().replace(hour=0, minute=0, second=0)
        r_count = 1
    else:
        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        r_count = (end_time - start_time).days + 1

    stations = models.MaterStation.objects.filter(is_delete=0).all()
    # 连接IDC
    pool = PersistentDB(pymysql, 10, **{
        "host": settings.DATABASES['doris_ads_rhyc']['HOST'],  # 数据库主机地址
        "user": settings.DATABASES['doris_ads_rhyc']['USER'],  # 数据库用户名
        "password": settings.DATABASES['doris_ads_rhyc']['PASSWORD'],  # 数据库密码
        "database": settings.DATABASES['doris_ads_rhyc']['NAME'],  # 数据库名称
        "port": settings.DATABASES['doris_ads_rhyc']['PORT'],
        "cursorclass": pymysql.cursors.DictCursor
    })
    conn = pool.connection()
    cursor = conn.cursor()
    for r in range(r_count):
        start_time_r = start_time + datetime.timedelta(days=r)

        # 手动执行是需要对其实际收益开始计算的日期
        # income_res = models.StationIncome.objects.filter(income_date=start_time_r).all()
        # station_ids = [i.master_station_id for i in income_res]
        sql = """SELECT day, pointed_chag, peak_chag, flat_chag, valley_chag,
                                      pointed_disg, peak_disg, flat_disg, valley_disg, station, dvalley_chag, dvalley_disg
                                      FROM ads_report_chag_disg_union_1d
                                      WHERE
                                      station_type <= 1
                                      AND day = '{}'""".format(start_time_r)

        sql += " ORDER BY day"
        cursor.execute(sql)
        result = cursor.fetchall()
        chag_disg_dict = {}
        for d in result:
            d = [i for i in d.values()]
            chag_disg_dict[d[9]] = [d[c] for c in range(12)]

        # 查询当月所有电价
        day_price_res = models.PeakValleyNew.objects.filter(year_month=start_time_r.strftime("%Y-%m")).all()
        price_new_dict = {}
        for price in day_price_res:
            k = f"{price.province_id}-{price.type}-{price.level}"  # 定义唯一key
            pv_k = price.pv  # 小时峰谷标识
            if price_new_dict.get(k):
                if price_new_dict.get(k).get(pv_k):
                    continue
                else:
                    price_new_dict[k][pv_k] = price.price
            else:
                price_new_dict[k] = {
                    pv_k: price.price
                }


        for station in stations:
            # 改查新综合过结算表和计量表的新1d冻结表：表名未定
            slave_station = station.stationdetails_set.filter(is_delete=0).all()
            for s_info in slave_station:

                price_dict = get_station_price_optimize(s_info, start_time_r.date(), price_new_dict)
                station_info = chag_disg_dict.get(s_info.english_name)
                if station_info:
                    peakvalley_income = models.PeakValleyIncome.objects.filter(day=start_time_r,
                                                                               station=s_info).first()
                    peakvalley_income = peakvalley_income if peakvalley_income else models.PeakValleyIncome()
                    day = start_time_r.strftime('%Y-%m-%d')
                    k = int(day.split('-')[1])
                    peakvalley_income.day = start_time_r
                    peakvalley_income.pointed_chag = round((station_info[1]), 2) if station_info[1] else 0

                    peakvalley_income.pointed_chag_income = round(
                        price_dict['spike_chag_price'] * float(station_info[1]), 2) if price_dict[
                                                                                           'spike_chag_price'] != '--' and station_info[1] else 0
                    peakvalley_income.peak_chag = round((station_info[2]), 2) if station_info[2] else 0
                    peakvalley_income.peak_chag_income = round(
                        price_dict['peak_chag_price'] * float(station_info[2]), 2) if price_dict[
                                                                                          'peak_chag_price'] != '--' and station_info[2] else 0
                    peakvalley_income.flat_chag = round((station_info[3]), 2) if station_info[3] else 0
                    peakvalley_income.flat_chag_income = round(
                        price_dict['flat_chag_price'] * float(station_info[3]),
                        2) if price_dict['flat_chag_price'] != '--' and station_info[3] else 0
                    peakvalley_income.valley_chag = round((station_info[4]), 2) if station_info[4] else 0
                    peakvalley_income.valley_chag_income = round(
                        price_dict['valley_chag_price'] * float(station_info[4]), 2) if price_dict[
                                                                                            'valley_chag_price'] != '--' and station_info[4] else 0
                    peakvalley_income.dvalley_chag = round((station_info[10]), 2) if station_info[10] else 0
                    peakvalley_income.dvalley_chag_income = round(
                        price_dict['dvalley_chag_price'] * float(station_info[10]), 2) if price_dict[
                                                                                            'dvalley_chag_price'] != '--' and station_info[10] else 0

                    peakvalley_income.pointed_disg = round((station_info[5]), 2) if station_info[5] else 0
                    peakvalley_income.pointed_disg_income = round(
                        price_dict['spike_disg_price'] * float(station_info[5]), 2) if price_dict[
                                                                                           'spike_disg_price'] != '--' and station_info[5] else 0
                    peakvalley_income.peak_disg = round((station_info[6]), 2) if station_info[6] else 0
                    peakvalley_income.peak_disg_income = round(
                        price_dict['peak_disg_price'] * float(station_info[6]), 2) if price_dict[
                                                                                          'peak_disg_price'] != '--' and station_info[6] else 0
                    peakvalley_income.flat_disg = round((station_info[7]), 2) if station_info[7] else 0
                    peakvalley_income.flat_disg_income = round(
                        price_dict['flat_disg_price'] * float(station_info[7]),
                        2) if price_dict['flat_disg_price'] != '--' and station_info[7] else 0
                    peakvalley_income.valley_disg = round((station_info[8]), 2) if station_info[8] else 0
                    peakvalley_income.valley_disg_income = round(
                        price_dict['valley_disg_price'] * float(station_info[8]), 2) if price_dict[
                                                                                            'valley_disg_price'] != '--' and station_info[8] else 0
                    peakvalley_income.dvalley_disg = round((station_info[11]), 2) if station_info[11] else 0
                    peakvalley_income.dvalley_disg_income = round(
                        price_dict['dvalley_disg_price'] * float(station_info[11]), 2) if price_dict[
                                                                                            'dvalley_disg_price'] != '--' and station_info[11] else 0
                    peakvalley_income.master_station = station
                    peakvalley_income.station = s_info
                    peakvalley_income.is_account = 0
                    peakvalley_income.save()

    cursor.close()
    return 1



def station_statistics_income_old(start_time=None, end_time=None):
    """
    计算每日尖峰平谷收益
    :param start_time: 开始时间
    :param end_time: 结束时间（不包含本日）
    :return:
    """
    # 处理开始结束时间
    if not start_time:
        # start_time = (datetime.datetime.now() - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0)
        start_time = datetime.datetime.now().replace(hour=0, minute=0, second=0)
        r_count = 1
    else:
        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        r_count = (end_time - start_time).days + 1

    stations = models.MaterStation.objects.filter(is_delete=0).all()
    # 连接IDC
    with connections['doris_ads_rhyc'].cursor() as ads_cursor:
        for r in range(r_count):
            start_time_r = start_time + datetime.timedelta(days=r)
            for station in stations:
                # 查询电价
                # price_info = station.stationdetails_set.first()
                # prices = models.PeakValley.objects.filter(province=price_info.province, type=price_info.type, level=price_info.level).order_by('-id')[:12]
                # price_data = {}  # 电价字典
                # for i in prices:
                #     i = i.__dict__
                #     price_data[i.get('year_month')] = {}
                #     for y in range(24):
                #         price_data[i.get('year_month')][i.get(f'pv{y}')] = float(i.get(f'h{y}'))

                # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
                # meter_use_time = models.MeterUseTime.objects.filter(station=station.master_station, is_use=1).first()
                # is_use_account = (station.master_station.is_account and meter_use_time and
                #                   meter_use_time.start_time <= start_time_r <= meter_use_time.end_time)
                #
                # if is_use_account:  # 结算电表
                #     # 处理主从模式
                #     # slave_station = station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0),
                #     #                                                   Q(pack=-1) | Q(pack=0)).first()
                #     slave_station = station.stationdetails_set.filter(english_name=station.english_name).first()
                #     if slave_station:
                #         sql = """SELECT day, pointed_chag, peak_chag, flat_chag, valley_chag,
                #                                               pointed_disg, peak_disg, flat_disg, valley_disg
                #                                               FROM ads_report_ems_chag_disg_1d
                #                                               WHERE
                #                                               day
                #                                               ='{}'
                #                                               """.format(start_time_r)
                #
                #         sql += f"AND station = '{slave_station.english_name}'"
                #         sql += f" ORDER BY day"
                #         try:
                #             # 获取查询结果
                #             ads_cursor.execute(sql)
                #             result = ads_cursor.fetchall()
                #         except Exception as e:
                #             error_log.error(e)
                #
                #         for i in result:
                #             day = start_time_r.strftime('%Y-%m-%d')
                #             k = int(day.split('-')[1])
                #             peakvalley_income = models.PeakValleyIncome.objects.filter(day=start_time_r,
                #                                                                        station=slave_station).first()
                #             peakvalley_income = peakvalley_income if peakvalley_income else models.PeakValleyIncome()
                #             peakvalley_income.day = start_time_r
                #             peakvalley_income.pointed_chag = round((i[1]), 2)
                #             peakvalley_income.pointed_chag_income = round(price_data.get(k).get(2) * float(i[1]), 2) if price_data.get(k).get(2) else 0
                #             peakvalley_income.peak_chag = round((i[2]), 2)
                #             peakvalley_income.peak_chag_income = round(price_data.get(k).get(1) * float(i[2]), 2) if price_data.get(k).get(1) else 0
                #             peakvalley_income.flat_chag = round((i[3]), 2)
                #             peakvalley_income.flat_chag_income = round(price_data.get(k).get(0) * float(i[3]),
                #                                                        2) if price_data.get(k).get(0) else 0
                #             peakvalley_income.valley_chag = round((i[4]), 2)
                #             peakvalley_income.valley_chag_income = round(price_data.get(k).get(-1) * float(i[4]), 2) if price_data.get(k).get(-1) else 0
                #             peakvalley_income.pointed_disg = round((i[5]), 2)
                #             peakvalley_income.pointed_disg_income = round(price_data.get(k).get(2) * float(i[5]), 2) if price_data.get(k).get(2) else 0
                #             peakvalley_income.peak_disg = round((i[6]), 2)
                #             peakvalley_income.peak_disg_income = round(price_data.get(k).get(1) * float(i[6]), 2) if price_data.get(k).get(1) else 0
                #             peakvalley_income.flat_disg = round((i[7]), 2)
                #             peakvalley_income.flat_disg_income = round(price_data.get(k).get(0) * float(i[7]),
                #                                                        2) if price_data.get(k).get(0) else 0
                #             peakvalley_income.valley_disg = round((i[8]), 2)
                #             peakvalley_income.valley_disg_income = round(price_data.get(k).get(-1) * float(i[8]), 2) if price_data.get(k).get(-1) else 0
                #             peakvalley_income.master_station = station
                #             peakvalley_income.station = s_info
                #             peakvalley_income.is_account = 1
                #
                #             peakvalley_income.save()
                # else:  # 计量电表
                # 处理主从模式
                # slave_station = station.stationdetails_set.filter(~Q(slave=0), ~Q(pack=0)).all()

                # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                slave_station = station.stationdetails_set.filter(is_delete=0).all()
                for s_info in slave_station:

                    price_dict = get_station_price(s_info, start_time_r.date())

                    sql = """SELECT day, pointed_chag, peak_chag, flat_chag, valley_chag,
                              pointed_disg, peak_disg, flat_disg, valley_disg
                              FROM ads_report_chag_disg_union_1d
                              WHERE
                              station_type <= 1
                              AND day = '{}'""".format(start_time_r)

                    sql += "AND station = '{}'".format(s_info.english_name)
                    sql += " ORDER BY day"

                    try:
                        # 获取查询结果
                        ads_cursor.execute(sql)
                        result = ads_cursor.fetchall()
                    except Exception as e:
                        error_log.error(e)

                    if not result:
                        continue
                    for i in result:
                        peakvalley_income = models.PeakValleyIncome.objects.filter(day=start_time_r,
                                                                                   station=s_info).first()
                        peakvalley_income = peakvalley_income if peakvalley_income else models.PeakValleyIncome()
                        day = start_time_r.strftime('%Y-%m-%d')
                        k = int(day.split('-')[1])
                        peakvalley_income.day = start_time_r
                        peakvalley_income.pointed_chag = round((i[1]), 2)

                        peakvalley_income.pointed_chag_income = round(price_dict['spike_chag_price'] * float(i[1]), 2) if price_dict['spike_chag_price'] != '--' else 0
                        peakvalley_income.peak_chag = round((i[2]), 2)
                        peakvalley_income.peak_chag_income = round(price_dict['peak_chag_price'] * float(i[2]), 2) if price_dict['peak_chag_price'] != '--' else 0
                        peakvalley_income.flat_chag = round((i[3]), 2)
                        peakvalley_income.flat_chag_income = round(price_dict['flat_chag_price'] * float(i[3]),
                                                                   2) if price_dict['flat_chag_price'] != '--' else 0
                        peakvalley_income.valley_chag = round((i[4]), 2)
                        peakvalley_income.valley_chag_income = round(price_dict['valley_chag_price'] * float(i[4]), 2) if price_dict['valley_chag_price'] != '--' else 0
                        peakvalley_income.pointed_disg = round((i[5]), 2)
                        peakvalley_income.pointed_disg_income = round(price_dict['spike_disg_price'] * float(i[5]), 2) if price_dict['spike_disg_price'] != '--' else 0
                        peakvalley_income.peak_disg = round((i[6]), 2)
                        peakvalley_income.peak_disg_income = round(price_dict['peak_disg_price'] * float(i[6]), 2)  if price_dict['peak_disg_price'] != '--' else 0
                        peakvalley_income.flat_disg = round((i[7]), 2)
                        peakvalley_income.flat_disg_income = round(price_dict['flat_disg_price'] * float(i[7]),
                                                                   2) if price_dict['flat_disg_price'] != '--' else 0
                        peakvalley_income.valley_disg = round((i[8]), 2)
                        peakvalley_income.valley_disg_income = round(price_dict['valley_disg_price'] * float(i[8]), 2) if price_dict['valley_disg_price'] != '--' else 0
                        peakvalley_income.master_station = station
                        peakvalley_income.station = s_info
                        peakvalley_income.is_account = 0
                        peakvalley_income.save()
    return 1


def is_device_online(device_json):
    non_null_count = 0
    for key, value in device_json.items():
        if value is not None and value != '' and value != '--':
            non_null_count += 1

    return non_null_count > 4



def station_fault_alarm_polling():
    """
    每五分钟扫描故障时长超过五分钟的数据，对比redis故障测点，进行手动恢复
    :return:
    """
    close_old_connections() # 关闭旧链接
    now_time = datetime.datetime.now()
    polling_time = now_time - datetime.timedelta(minutes=5)  # 五分钟节点
    fault_alarm_data = models.FaultAlarm.objects.using('alarm_module').filter(type=1, status=0, start_time__lte=polling_time).all()
    conn = get_redis_connection('3')  # 连接redis
    for alarm in fault_alarm_data:
        station = models.StationDetails.objects.filter(id=alarm.station_id).first()
        if station:
            alarm_status = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', station.english_name, alarm.device))
            if alarm_status:
                alarm_status = eval(eval(alarm_status))
                if alarm_status.get(alarm.point) is not None:
                    if int(alarm_status.get(alarm.point)) == 0:  # 正常
                        alarm.end_time = now_time
                        alarm.status = 1
                        alarm.note = '已 恢 复'
                        alarm.save()
    # 离线、通讯异常数据
    offline_data = models.FaultAlarm.objects.using('alarm_module').filter(type__gte=4, status=0, start_time__lte=polling_time).all()
    conn_def = get_redis_connection('default')  # 连接redis
    now = datetime.datetime.now()
    today_end = now.replace(hour=23, minute=59, second=59, microsecond=0)
    expire_time = (today_end - now).total_seconds()
    for alarm in offline_data:
        station = models.StationDetails.objects.filter(id=alarm.station_id).first()
        if station:
            alarm_status = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', station.english_name, alarm.device))
            if alarm_status:
                alarm_status = eval(eval(alarm_status))
                is_online = is_device_online(alarm_status)
                online_status = 1 if is_online else 0
            else:
                online_status = 0
            if online_status == 1:  # 恢复正常
                alarm.end_time = now_time
                alarm.status = 1
                alarm.note = '已 恢 复'
                alarm.save()
                if alarm.type == 4:  # 离线
                    key = f"{station.english_name}_{alarm.device}_Online"
                    key_aggr = f"{station.english_name}_{alarm.device}_Online_aggr"
                else:
                    key = f"{station.english_name}_{alarm.device}_Cmlost"
                    key_aggr = f"{station.english_name}_{alarm.device}_Cmlost_aggr"
                if conn_def.get(key):
                    conn_def.delete(key)
                exist_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').filter(joint_primary_key=alarm.joint_primary_key).first()
                if exist_aggr_alarm:
                    related_alarm_list = models.FaultAlarm.objects.using('alarm_module').filter(
                        joint_primary_key=exist_aggr_alarm.joint_primary_key).all()
                    duration = 0
                    for alarm in related_alarm_list:
                        if alarm.status == 1:
                            duration += (alarm.end_time - alarm.start_time).total_seconds()
                        else:
                            duration += (datetime.datetime.now() - alarm.start_time).total_seconds()
                    exist_aggr_alarm.status = 1
                    exist_aggr_alarm.end_time = now
                    exist_aggr_alarm.note = '已 恢 复'
                    exist_aggr_alarm.duration = round(duration / 3600, 2)
                    exist_aggr_alarm.save()
                    # 给聚合告警的 key 设置过期时间
                    if conn.get(key_aggr):
                        if exist_aggr_alarm.start_time.date() == now.date():
                            conn.expire(key_aggr, int(expire_time))
                        else:
                            conn.delete(key_aggr)
    return 1
