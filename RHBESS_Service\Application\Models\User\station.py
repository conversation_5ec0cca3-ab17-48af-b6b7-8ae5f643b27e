#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-27 09:45:14
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\station.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-27 17:01:09

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class Station(user_Base):
    u'子站配置表'
    __tablename__ = "t_station"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(50), nullable=False, comment=u"名称")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    address = Column(VARCHAR(256), nullable=True,comment=u"地址")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    index = Column(Integer, nullable=True,comment=u"排序索引")
    other_infos = Column(VARCHAR(256), nullable=True,comment=u"其他信息集合")
    station_type = Column(CHAR(1), nullable=True,comment=u"1调频，2调峰，3其他")
    project_info = Column(VARCHAR(1024), nullable=True,comment=u"项目介绍")
    basic_info = Column(VARCHAR(1024), nullable=True,comment=u"基本信息")
    longitude = Column(VARCHAR(255), nullable=True,comment=u"经度")
    dimension = Column(VARCHAR(255), nullable=True,comment=u"纬度")
    scene_picture = Column(VARCHAR(255), nullable=True,comment=u"现场图片")
    volume = Column(Integer, nullable=True, comment=u"电站容量")
    start_ts = Column(DateTime, nullable=False, comment=u"电站开始时间：YYYY-mm-dd")

    en_address = Column(VARCHAR(256), nullable=True, comment=u"英文地址")
    en_descr = Column(VARCHAR(256), nullable=False, comment=u"英文描述")
    en_other_infos = Column(VARCHAR(256), nullable=True, comment=u"其他信息集合")
    en_project_info = Column(VARCHAR(1024), nullable=True, comment=u"项目介绍")
    en_basic_info = Column(VARCHAR(1024), nullable=True, comment=u"基本信息")


    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        now = timeUtils.getNewTimeStr()
        user_session.merge(Station(id=1,name='halun',descr='哈伦6MW/3MWh储能电站',op_ts=now,index=3));
        user_session.merge(Station(id=2,name='taicang',descr='太仓9MW/4.5MWh储能电站',op_ts=now,index=2));
        user_session.merge(Station(id=3,name='binhai',descr='滨海18MW/9MWh储能电站',op_ts=now,index=1));
        user_session.merge(Station(id=4,name='ygzhen',descr='永臻5MW/18MWh储能电站',op_ts=now,index=4));
       
        user_session.commit()
        user_session.close()

    def __repr__(self):
        # bean = "{'id':%s,'descr':'%s','name':'%s','address':'%s','op_ts':'%s','index':'%s','station_type':%s,'project_info':'%s','longitude':'%s','dimension':'%s'," \
        #        "'scene_picture':'%s','volume':'%s','start_ts':'%s','en_descr':'%s','en_address':'%s','en_other_infos':'%s','en_project_info':'%s','en_basic_info':'%s'}" % (
        # self.id, self.descr, self.name, self.address,self.op_ts, self.index, self.station_type, self.project_info, self.longitude, self.dimension,
        # self.scene_picture, self.volume, self.start_ts, self.en_descr, self.en_address, self.en_other_infos, self.en_project_info, self.en_basic_info)

        bean = "{'id':%s,'descr':'%s','name':'%s','address':'%s','op_ts':'%s','index':'%s','station_type':'%s','project_info':'%s','longitude':'%s','dimension':'%s'," \
               "'scene_picture':'%s','volume':'%s','start_ts':'%s','en_descr':'%s','en_address':'%s','en_project_info':'%s'}" % (
                   self.id, self.descr, self.name, self.address, self.op_ts, self.index, self.station_type,
                   self.project_info, self.longitude, self.dimension,
                   self.scene_picture, self.volume, self.start_ts, self.en_descr, self.en_address,
                   self.en_project_info)

        return bean.replace("None", '')
        
    def deleteStation(self,id):
        try:
            user_session.query(Station).filter(Station.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}