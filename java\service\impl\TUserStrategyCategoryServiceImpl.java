package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryCreateDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryQueryDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryUpdateDTO;
import com.robestec.analysis.entity.TUserStrategyCategory;
import com.robestec.analysis.mapper.TUserStrategyCategoryMapper;
import com.robestec.analysis.service.TUserStrategyCategoryService;
import com.robestec.analysis.vo.TUserStrategyCategoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户策略分类服务实现类
 */
@Slf4j
@Service
public class TUserStrategyCategoryServiceImpl extends SuperServiceImpl<TUserStrategyCategoryMapper, TUserStrategyCategory>
        implements TUserStrategyCategoryService {

    @Override
    public PageResult<TUserStrategyCategoryVO> queryTUserStrategyCategory(TUserStrategyCategoryQueryDTO queryDTO) {
        LambdaQueryWrapper<TUserStrategyCategory> wrapper = new LambdaQueryWrapper<TUserStrategyCategory>()
                .like(StringUtils.hasText(queryDTO.getName()), TUserStrategyCategory::getName, queryDTO.getName())
                .eq(queryDTO.getStrategyId() != null, TUserStrategyCategory::getStrategyId, queryDTO.getStrategyId())
                .eq(queryDTO.getIsFollow() != null, TUserStrategyCategory::getIsFollow, queryDTO.getIsFollow())
                .eq(queryDTO.getIsUse() != null, TUserStrategyCategory::getIsUse, queryDTO.getIsUse())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TUserStrategyCategory::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TUserStrategyCategory::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(TUserStrategyCategory::getCreateTime);

        Page<TUserStrategyCategory> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TUserStrategyCategory> result = this.page(page, wrapper);

        List<TUserStrategyCategoryVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TUserStrategyCategoryVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTUserStrategyCategory(TUserStrategyCategoryCreateDTO createDTO) {
        // 检查同一策略下分类名称是否重复
        long count = this.count(Wrappers.<TUserStrategyCategory>lambdaQuery()
                .eq(TUserStrategyCategory::getName, createDTO.getName())
                .eq(TUserStrategyCategory::getStrategyId, createDTO.getStrategyId()));
        if (count > 0) {
            throw new RuntimeException("该策略下分类名称已存在");
        }

        TUserStrategyCategory entity = BeanUtil.copyProperties(createDTO, TUserStrategyCategory.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTUserStrategyCategory(TUserStrategyCategoryUpdateDTO updateDTO) {
        TUserStrategyCategory entity = BeanUtil.copyProperties(updateDTO, TUserStrategyCategory.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTUserStrategyCategory(Long id) {
        this.removeById(id);
    }

    @Override
    public TUserStrategyCategoryVO getTUserStrategyCategory(Long id) {
        TUserStrategyCategory entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTUserStrategyCategoryList(List<TUserStrategyCategoryCreateDTO> createDTOList) {
        List<TUserStrategyCategory> entityList = BeanUtil.copyToList(createDTOList, TUserStrategyCategory.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TUserStrategyCategoryVO> getTUserStrategyCategoryByStrategyId(Long strategyId) {
        List<TUserStrategyCategory> entityList = this.list(Wrappers.<TUserStrategyCategory>lambdaQuery()
                .eq(TUserStrategyCategory::getStrategyId, strategyId)
                .orderByDesc(TUserStrategyCategory::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<TUserStrategyCategoryVO> getTUserStrategyCategoryByName(String name) {
        List<TUserStrategyCategory> entityList = this.list(Wrappers.<TUserStrategyCategory>lambdaQuery()
                .like(TUserStrategyCategory::getName, name)
                .orderByDesc(TUserStrategyCategory::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByStrategyId(Long strategyId) {
        return this.count(Wrappers.<TUserStrategyCategory>lambdaQuery()
                .eq(TUserStrategyCategory::getStrategyId, strategyId));
    }

    /**
     * 转换为VO对象
     */
    private TUserStrategyCategoryVO convertToVO(TUserStrategyCategory entity) {
        if (entity == null) {
            return null;
        }
        TUserStrategyCategoryVO vo = BeanUtil.copyProperties(entity, TUserStrategyCategoryVO.class);
        vo.setIsFollowName(getIsFollowName(entity.getIsFollow()));
        vo.setIsUseName(getIsUseName(entity.getIsUse()));
        return vo;
    }

    /**
     * 获取是否跟随名称
     */
    private String getIsFollowName(Integer isFollow) {
        if (isFollow == null) {
            return "";
        }
        return isFollow == 1 ? "是" : "否";
    }

    /**
     * 获取是否使用名称
     */
    private String getIsUseName(Integer isUse) {
        if (isUse == null) {
            return "";
        }
        return isUse == 1 ? "使用" : "不使用";
    }
}
