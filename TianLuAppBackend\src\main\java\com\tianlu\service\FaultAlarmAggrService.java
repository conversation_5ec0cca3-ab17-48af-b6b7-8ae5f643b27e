package com.tianlu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.central.common.model.Result;
import com.tianlu.entity.FaultAlarmAggr;

import java.util.Map;

public interface FaultAlarmAggrService extends IService<FaultAlarmAggr> {
    
    /**
     * 推送告警消息到消息中心
     * @param alarmId 告警ID
     * @param userId 当前用户ID
     * @return 推送结果
     */
    Result<String> pushAlarmMessage(Long alarmId, Long userId);

    /**
     * 获取告警消息详情
     * @param messageId 消息ID
     * @return 告警消息详情
     */
    Result<Map<String, Object>> getAlarmMessageDetail(Long messageId);
} 