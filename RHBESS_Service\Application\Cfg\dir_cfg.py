#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \em_pjt_rh\RHBESS_empower\Application\Cfg\dir_cfg.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-27 10:28:19


# coding=utf-8
import configparser
import os

model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.abspath(__file__))
# sd2021path = os.path.dirname(basepath)
path = basepath + "/module.ini"
model_config.read(path,encoding='utf-8')
followpath = os.path.abspath(os.path.join(os.getcwd(),"../../.."))