#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_Service\Tools\TimeTask\timing_task.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-03 09:33:25

import sys,os,getopt
import json
import logging
logging.basicConfig()
reload(sys)
sys.setdefaultencoding("utf-8")
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)
from Tools.DB.mysql_user import user_session
from Application.Models.User.event import Event
from Application.Models.User.event_r import EventR
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.first_page_data import FirstPageDatas
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import real_data
from apscheduler.schedulers.blocking import BlockingScheduler

from timedtask_log import app_log

import dm2016
eventMonitor = dm2016.EventMonitor()
measureMonitor = dm2016.MeasureMonitor()
cumulantMonitor = dm2016.CumulantMonitor()
measure = dm2016.MeasureMgr()
status = dm2016.StatusMgr()
cumulant = dm2016.CumulantMgr()
discrete = dm2016.DiscreteMgr()
v_type = {'status':dm2016.Status,'discrete':dm2016.Discrete}
event_type_obj = {1:'device',2:'status',3:'discrete',4:'action',5:'measure',6:'cumulant'}
alarm_obj,event_obj = {},{}
datas_dianbiao = {'halun_tpSthalun':["tpSthalun.PcsSt1.Lp1.ChagCapy","tpSthalun.PcsSt1.Lp2.ChagCapy","tpSthalun.PcsSt2.Lp1.ChagCapy","tpSthalun.PcsSt2.Lp2.ChagCapy",
"tpSthalun.PcsSt1.Lp1.DisgCapy","tpSthalun.PcsSt1.Lp2.DisgCapy","tpSthalun.PcsSt2.Lp1.DisgCapy","tpSthalun.PcsSt2.Lp2.DisgCapy",
"tpSthalun.PcsSt1.Lp1.RealPwOtInvt","tpSthalun.PcsSt1.Lp2.RealPwOtInvt","tpSthalun.PcsSt2.Lp1.RealPwOtInvt","tpSthalun.PcsSt2.Lp2.RealPwOtInvt",
"tpSthalun.PcsSt1.Lp1.ReactPwOtInvt","tpSthalun.PcsSt1.Lp2.ReactPwOtInvt","tpSthalun.PcsSt2.Lp1.ReactPwOtInvt","tpSthalun.PcsSt2.Lp2.ReactPwOtInvt",
"tpSthalun.PcsSt1.Lp1.AcVoltInvt","tpSthalun.PcsSt1.Lp2.AcVoltInvt","tpSthalun.PcsSt2.Lp1.AcVoltInvt","tpSthalun.PcsSt2.Lp2.AcVoltInvt",
"tpSthalun.PcsSt1.Lp1.AcCurrInvt","tpSthalun.PcsSt1.Lp2.AcCurrInvt","tpSthalun.PcsSt2.Lp1.AcCurrInvt","tpSthalun.PcsSt2.Lp2.AcCurrInvt"],

"taicang_tpSttaicang":["tpSttaicang.PcsSt1.Lp1.ChagCapy","tpSttaicang.PcsSt1.Lp2.ChagCapy","tpSttaicang.PcsSt2.Lp1.ChagCapy","tpSttaicang.PcsSt2.Lp2.ChagCapy",
"tpSttaicang.PcsSt3.Lp1.ChagCapy","tpSttaicang.PcsSt3.Lp2.ChagCapy","tpSttaicang.PcsSt4.Lp1.ChagCapy","tpSttaicang.PcsSt4.Lp2.ChagCapy",
"tpSttaicang.PcsSt1.Lp1.DisgCapy","tpSttaicang.PcsSt1.Lp2.DisgCapy","tpSttaicang.PcsSt2.Lp1.DisgCapy","tpSttaicang.PcsSt2.Lp2.DisgCapy",
"tpSttaicang.PcsSt3.Lp1.DisgCapy","tpSttaicang.PcsSt3.Lp2.DisgCapy","tpSttaicang.PcsSt4.Lp1.DisgCapy","tpSttaicang.PcsSt4.Lp2.DisgCapy",
"tpSttaicang.PcsSt1.Lp1.RealPwOtInvt","tpSttaicang.PcsSt1.Lp2.RealPwOtInvt","tpSttaicang.PcsSt2.Lp1.RealPwOtInvt","tpSttaicang.PcsSt2.Lp2.RealPwOtInvt",
"tpSttaicang.PcsSt3.Lp1.RealPwOtInvt","tpSttaicang.PcsSt3.Lp2.RealPwOtInvt","tpSttaicang.PcsSt4.Lp1.RealPwOtInvt","tpSttaicang.PcsSt4.Lp2.RealPwOtInvt",
"tpSttaicang.PcsSt1.Lp1.ReactPwOtInvt","tpSttaicang.PcsSt1.Lp2.ReactPwOtInvt","tpSttaicang.PcsSt2.Lp1.ReactPwOtInvt","tpSttaicang.PcsSt2.Lp2.ReactPwOtInvt",
"tpSttaicang.PcsSt3.Lp1.ReactPwOtInvt","tpSttaicang.PcsSt3.Lp2.ReactPwOtInvt","tpSttaicang.PcsSt4.Lp1.ReactPwOtInvt","tpSttaicang.PcsSt4.Lp2.ReactPwOtInvt",
"tpSttaicang.PcsSt1.Lp1.AcCurrInvt","tpSttaicang.PcsSt1.Lp2.AcCurrInvt","tpSttaicang.PcsSt2.Lp1.AcCurrInvt","tpSttaicang.PcsSt2.Lp2.AcCurrInvt",
"tpSttaicang.PcsSt3.Lp1.AcCurrInvt","tpSttaicang.PcsSt3.Lp2.AcCurrInvt","tpSttaicang.PcsSt4.Lp1.AcCurrInvt","tpSttaicang.PcsSt4.Lp2.AcCurrInvt",
"tpSttaicang.PcsSt1.Lp1.AcVoltInvt","tpSttaicang.PcsSt1.Lp2.AcVoltInvt","tpSttaicang.PcsSt2.Lp1.AcVoltInvt","tpSttaicang.PcsSt2.Lp2.AcVoltInvt",
"tpSttaicang.PcsSt3.Lp1.AcVoltInvt","tpSttaicang.PcsSt3.Lp2.AcVoltInvt","tpSttaicang.PcsSt4.Lp1.AcVoltInvt","tpSttaicang.PcsSt4.Lp2.AcVoltInvt",],

"binhai_tpStbinhai1":["tpStbinhai1.PcsSt1.Lp1.ChagCapy","tpStbinhai1.PcsSt1.Lp2.ChagCapy","tpStbinhai1.PcsSt2.Lp1.ChagCapy","tpStbinhai1.PcsSt2.Lp2.ChagCapy",
"tpStbinhai1.PcsSt3.Lp1.ChagCapy","tpStbinhai1.PcsSt3.Lp2.ChagCapy","tpStbinhai1.PcsSt1.Lp1.DisgCapy","tpStbinhai1.PcsSt1.Lp2.DisgCapy",
"tpStbinhai1.PcsSt2.Lp1.DisgCapy","tpStbinhai1.PcsSt2.Lp2.DisgCapy","tpStbinhai1.PcsSt3.Lp1.DisgCapy","tpStbinhai1.PcsSt3.Lp2.DisgCapy",
"tpStbinhai1.PcsSt1.Lp1.RealPwOtInvt","tpStbinhai1.PcsSt1.Lp2.RealPwOtInvt","tpStbinhai1.PcsSt2.Lp1.RealPwOtInvt","tpStbinhai1.PcsSt2.Lp2.RealPwOtInvt",
"tpStbinhai1.PcsSt3.Lp1.RealPwOtInvt","tpStbinhai1.PcsSt3.Lp2.RealPwOtInvt","tpStbinhai1.PcsSt1.Lp1.ReactPwOtInvt","tpStbinhai1.PcsSt1.Lp2.ReactPwOtInvt",
"tpStbinhai1.PcsSt2.Lp1.ReactPwOtInvt","tpStbinhai1.PcsSt2.Lp2.ReactPwOtInvt","tpStbinhai1.PcsSt3.Lp1.ReactPwOtInvt","tpStbinhai1.PcsSt3.Lp2.ReactPwOtInvt",
"tpStbinhai1.PcsSt1.Lp1.DcVoltInvt","tpStbinhai1.PcsSt1.Lp2.DcVoltInvt",
"tpStbinhai1.PcsSt2.Lp1.DcVoltInvt","tpStbinhai1.PcsSt2.Lp2.DcVoltInvt","tpStbinhai1.PcsSt3.Lp1.DcVoltInvt","tpStbinhai1.PcsSt3.Lp2.DcVoltInvt",
"tpStbinhai1.PcsSt1.Lp1.AcVoltInvt","tpStbinhai1.PcsSt1.Lp2.AcVoltInvt",
"tpStbinhai1.PcsSt2.Lp1.AcVoltInvt","tpStbinhai1.PcsSt2.Lp2.AcVoltInvt","tpStbinhai1.PcsSt3.Lp1.AcVoltInvt","tpStbinhai1.PcsSt3.Lp2.AcVoltInvt",],

"binhai_tpStbinhai2":["tpStbinhai2.PcsSt4.Lp1.ChagCapy","tpStbinhai2.PcsSt4.Lp2.ChagCapy","tpStbinhai2.PcsSt5.Lp1.ChagCapy","tpStbinhai2.PcsSt5.Lp2.ChagCapy","tpStbinhai2.PcsSt6.Lp1.ChagCapy",
"tpStbinhai2.PcsSt6.Lp2.ChagCapy","tpStbinhai2.PcsSt4.Lp1.DisgCapy","tpStbinhai2.PcsSt4.Lp2.DisgCapy","tpStbinhai2.PcsSt5.Lp1.DisgCapy",
"tpStbinhai2.PcsSt5.Lp2.DisgCapy","tpStbinhai2.PcsSt6.Lp1.DisgCapy","tpStbinhai2.PcsSt6.Lp2.DisgCapy","tpStbinhai2.PcsSt4.Lp1.RealPwOtInvt",
"tpStbinhai2.PcsSt4.Lp2.RealPwOtInvt","tpStbinhai2.PcsSt5.Lp1.RealPwOtInvt","tpStbinhai2.PcsSt5.Lp2.RealPwOtInvt","tpStbinhai2.PcsSt6.Lp1.RealPwOtInvt",
"tpStbinhai2.PcsSt6.Lp2.RealPwOtInvt","tpStbinhai2.PcsSt4.Lp1.ReactPwOtInvt","tpStbinhai2.PcsSt4.Lp2.ReactPwOtInvt",
"tpStbinhai2.PcsSt5.Lp1.ReactPwOtInvt","tpStbinhai2.PcsSt5.Lp2.ReactPwOtInvt","tpStbinhai2.PcsSt6.Lp1.ReactPwOtInvt","tpStbinhai2.PcsSt6.Lp2.ReactPwOtInvt",
"tpStbinhai2.PcsSt4.Lp1.AcVoltInvt","tpStbinhai2.PcsSt4.Lp2.AcVoltInvt",
"tpStbinhai2.PcsSt5.Lp1.AcVoltInvt","tpStbinhai2.PcsSt5.Lp2.AcVoltInvt","tpStbinhai2.PcsSt6.Lp1.AcVoltInvt","tpStbinhai2.PcsSt6.Lp2.AcVoltInvt",
"tpStbinhai2.PcsSt4.Lp1.DcVoltInvt","tpStbinhai2.PcsSt4.Lp2.DcVoltInvt",
"tpStbinhai2.PcsSt5.Lp1.DcVoltInvt","tpStbinhai2.PcsSt5.Lp2.DcVoltInvt","tpStbinhai2.PcsSt6.Lp1.DcVoltInvt","tpStbinhai2.PcsSt6.Lp2.DcVoltInvt",],

"ygzhen_tfStygzhen1":["tfStygzhen1.EMS.PCS1.Lp1.AcChagCapyDaly","tfStygzhen1.EMS.PCS1.Lp2.AcChagCapyDaly","tfStygzhen1.EMS.PCS1.Lp3.AcChagCapyDaly","tfStygzhen1.EMS.PCS1.Lp4.AcChagCapyDaly",
"tfStygzhen1.EMS.PCS1.Lp1.AcDisgCapyDaly","tfStygzhen1.EMS.PCS1.Lp2.AcDisgCapyDaly","tfStygzhen1.EMS.PCS1.Lp3.AcDisgCapyDaly","tfStygzhen1.EMS.PCS1.Lp4.AcDisgCapyDaly",
"tfStygzhen1.EMS.PCS1.Lp1.U_Volt","tfStygzhen1.EMS.PCS1.Lp2.U_Volt","tfStygzhen1.EMS.PCS1.Lp3.U_Volt","tfStygzhen1.EMS.PCS1.Lp4.U_Volt",
"tfStygzhen1.EMS.PCS1.Lp1.U_Curr","tfStygzhen1.EMS.PCS1.Lp2.U_Curr","tfStygzhen1.EMS.PCS1.Lp3.U_Curr","tfStygzhen1.EMS.PCS1.Lp4.U_Curr",],

"ygzhen_tfStygzhen2":["tfStygzhen2.EMS.PCS2.Lp1.AcChagCapyDaly","tfStygzhen2.EMS.PCS2.Lp2.AcChagCapyDaly","tfStygzhen2.EMS.PCS2.Lp3.AcChagCapyDaly","tfStygzhen2.EMS.PCS2.Lp4.AcChagCapyDaly",
"tfStygzhen2.EMS.PCS2.Lp1.AcDisgCapyDaly","tfStygzhen2.EMS.PCS2.Lp2.AcDisgCapyDaly","tfStygzhen2.EMS.PCS2.Lp3.AcDisgCapyDaly","tfStygzhen2.EMS.PCS2.Lp4.AcDisgCapyDaly",
"tfStygzhen2.EMS.PCS2.Lp1.U_Curr","tfStygzhen2.EMS.PCS2.Lp2.U_Curr","tfStygzhen2.EMS.PCS2.Lp3.U_Curr","tfStygzhen2.EMS.PCS2.Lp4.U_Curr",
"tfStygzhen2.EMS.PCS2.Lp1.U_Volt","tfStygzhen2.EMS.PCS2.Lp2.U_Volt","tfStygzhen2.EMS.PCS2.Lp3.U_Volt","tfStygzhen2.EMS.PCS2.Lp4.U_Volt"],
"baodian_tfStbodian1":["tfStbodian1.Pcs.RealPw","tfStbodian1.Pcs.ReactPw","tfStbodian1.Pcs.M1ChgCapyAlw","tfStbodian1.Pcs.M1DsgCapyAlw"],
"baodian_tfStbodian2":["tfStbodian2.Pcs.RealPw","tfStbodian2.Pcs.ReactPw","tfStbodian2.Pcs.M1ChgCapyAlw","tfStbodian2.Pcs.M1DsgCapyAlw"],
"baodian_tfStbodian3":["tfStbodian3.Pcs.RealPw","tfStbodian3.Pcs.ReactPw","tfStbodian3.Pcs.M1ChgCapyAlw","tfStbodian3.Pcs.M1DsgCapyAlw"],
"baodian_tfStbodian4":["tfStbodian4.Pcs.RealPw","tfStbodian4.Pcs.ReactPw","tfStbodian4.Pcs.M1ChgCapyAlw","tfStbodian4.Pcs.M1DsgCapyAlw"],
"baodian_tfStbodian5":["tfStbodian5.Pcs.RealPw","tfStbodian5.Pcs.ReactPw","tfStbodian5.Pcs.M1ChgCapyAlw","tfStbodian5.Pcs.M1DsgCapyAlw"],
}

def getEventAlarmConf(station=None,name=None):
    '''查询事件和告警配置'''
    for v in event_type_obj.values():
        if station:
            alarms = user_session.query(Event).filter(Event.type==2,Event.index!=-1,Event.type_name==v,Event.station==station,Event.name.like(name+'%')).all()  # 所有告警配置
            events = user_session.query(Event).filter(Event.type==1,Event.index!=-1,Event.type_name==v,Event.id!=1,Event.station==station,Event.name.like(name+'%')).all()  # 所有事件配置
        else:
            alarms = user_session.query(Event).filter(Event.type==2,Event.index!=-1,Event.type_name==v).all()  # 所有告警配置
            events = user_session.query(Event).filter(Event.type==1,Event.index!=-1,Event.type_name==v,Event.id!=1).all()  # 所有事件配置
        obj,obj1 = {},{}
        for alarm in alarms:
            obj[alarm.index]=eval(str(alarm))
        alarm_obj[v] = obj
        
        for event in events:
            obj1[event.index]=eval(str(event))
        event_obj[v] = obj1
        print ('-----',v,len(alarms))
        print ('******',v,len(events))

def froze_data(station_):
    try:
        n = timeUtils.getNewTimeStr()
        del_data = timeUtils.getAgoTime(3)
        app_log.info('station:%s'%station_)
        for name in datas_dianbiao[station_]:
            v = measure.getByName(name).rt()['value']
            a = FirstPageDatas(name=name,value=v,op_ts=n,cause=1)
            user_session.add(a)
            
        # 删除多余数据 
        user_session.query(FirstPageDatas).filter(FirstPageDatas.op_ts<=del_data).delete()
        user_session.commit()
    except Exception as E:
        app_log.error(E)
        user_session.rollback()
    


def RunClearFileAndData(station_):
    scheduler = BlockingScheduler()
    scheduler.add_job(calculation, 'interval', seconds=10)  # 10秒获取一次数据
    # scheduler.add_job(froze_data, 'interval', minutes=30,kwargs={'station_':station_})  # 每 15分钟执行一次 minutes

    # scheduler.add_job(run_database, 'cron',  hour=1)  # 每天1点执行
    scheduler.start()

def calculation():
    event = eventMonitor.tryGet()
    mea = measureMonitor.tryGet()  # 测量量
    cum = cumulantMonitor.tryGet()  # 累积量
    try:
        now_t = timeUtils.getNewTimeStr()
        app_log.info(now_t)
        while event:
            app_log.info('event:%s'%event)
            if 'over' in event.keys():  # 溢出处理
                eventMonitor.reset()
                break
            saveHandleAlarmEvent(event,now_t,event_type_obj[event['type']])
            event = eventMonitor.tryGet()
            
        while mea:
            if 'over' in mea.keys():
                measureMonitor.reset()
                break
            saveHandleAlarmEvent(mea,now_t,'measure')
            mea = measureMonitor.tryGet()
            
        while cum:
            if 'over' in cum.keys():
                cumulantMonitor.reset()
                break
            saveHandleAlarmEvent(cum,now_t,'cumulant')
            cum = cumulantMonitor.tryGet()
            

    except Exception as E:
        app_log.error(E)
        user_session.rollback()

def saveHandleAlarmEvent(even,now_t,type_):
    '''
    处理保存具体的告警或事件
    event:具体事件
    '''
    index = even['index']
    filter = {'value':even['value'],'op_ts':now_t,'ts':even['time']}
   
    if index in alarm_obj[type_].keys():  # 告警
        obj = alarm_obj[type_][index]
        filter['event_id'] = obj['id']
        filter['station'] = obj['station']
        bean = real_data(type_,obj['name'],'ram')
        # if not bean['desc']:
        #     bean = real_data(type_,obj['name'],'db')
        if type_ == 'status' or type_ == 'discrete':
            value_descr=v_type[type_](index).valueDesc(even['value'])
        else:
            value_descr = bean['valueDesc']
        filter['value_descr']= value_descr.replace('告警',"报警").replace('警告',"报警")
        saveAlarmEvent(AlarmR,obj,filter)
        app_log.info('station:%s'%obj['station'])
        delExceed(obj['station'])
    if index in event_obj[type_].keys():  # 事件
        obj = event_obj[type_][index]
        filter['event_id'] = obj['id']
        filter['station'] = obj['station']
        bean = real_data(type_,obj['name'],'ram')
        # if not bean['desc']:
        #     bean = real_data(type_,obj['name'],'db')
        if type_ == 'status' or type_ == 'discrete':
            filter['value_descr']=v_type[type_](index).valueDesc(even['value'])
        else:
            filter['value_descr'] = bean['valueDesc']
        saveAlarmEvent(EventR,obj,filter)
        delExceed(obj['station'])
    user_session.commit()

def saveAlarmEvent(bean,obj,filter):
    '''
    保存具体的告警或事件值
    '''
    filter = optHandle(obj,filter)
    a = bean(**filter)
    user_session.add(a)

def optHandle(obj,filter):
    '''
    处理告警和事件的关联点
    '''   
    for i in range(1,11):
        fil = 'opt%s'%i
        type_name = 'type_name%s'%i
        opt_name = obj[fil]
        type_ = obj[type_name]
        if opt_name and opt_name != 'None':
            rt = None
            if type_ == 'measure':
                rt = measure.getByName(opt_name)
            if type_ == 'status':
                rt = status.getByName(opt_name)
            if type_ == 'cumulant':
                rt = cumulant.getByName(opt_name)
            if type_ == 'discrete':
                rt = discrete.getByName(opt_name)

            if rt:
                filter[fil] = rt.rt()['value']
            else:
                app_log.error(' %s  %sis not in system'%(opt_name,type_))
    return filter

def delExceed(station):
    '''
    删除超过一定数量的数据
    '''
    try:
        # 删除多余的事件记录
        child_event = user_session.query(EventR.id.label('id')).filter(EventR.station==station).order_by(EventR.id.desc()).limit(100).offset(500000).subquery()
        
        user_session.query(EventR).filter(EventR.id==child_event.c.id).delete()
        # 删除多余的告警记录
        child_alarm = user_session.query(AlarmR.id.label('id')).filter(AlarmR.station==station).order_by(AlarmR.id.desc()).limit(100).offset(500000).subquery()
        user_session.query(AlarmR).filter(AlarmR.id==child_alarm.c.id).delete()
        
        # user_session.commit()
    except Exception as E:
        app_log.error(E)
        user_session.rollback()
        
if __name__ == '__main__':
    try:
        opts, args = getopt.getopt(sys.argv[1:],"h",["help","sta","station=","name="])
        cmd = None
        name = None
        for opt,arg in opts:
            if opt=="--sta":
                cmd = "sta"
            elif opt=="--station":
                station = arg
            elif opt=="--name":
                name = arg
            else:
                print ('''%s 告警或事件存储工具
                选项
                    -h|--help 查看帮助
                    --sta 指定具体电站数据
                    --station  具体电站，halun,binhai,taicang,ygzhen,baodian,zgtian,houma,datong,guizhou,ygqn,shgyu
                    --name 具体电站名称（和点表一致），tpSthalun,tpStbinhai1,tpStbinhai2,tpSttaicang,tfStygzhen1,tfStygzhen2,
                    tfStbodian1,tfStbodian2,tfStbodian3,tfStbodian4,tfStbodian5,tfStzgtian1,tfStzgtian2,tfStzgtian3,tfStzgtian4,
                    tfSthoumaA1,tfSthoumaA2,tfSthoumaB1,tfSthoumaB2,tc_datong1,tc_datong2,tc_datong3,tc_datong4,guizhou1,guizhou2,guizhou3,guizhou4,
                    guizhou5,guizhou6,guizhou7,guizhou8,ygqn.abc,ygqn.d,shgyu
                                      
                ''' )% sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            getEventAlarmConf()
        elif cmd=="sta":
            if not station:
                print ("请指定具体电站，station值")
                quit()
            if not name:
                print ("请指定电站名称,name值")
                quit()
            getEventAlarmConf(station,name)
            print ('SUCCESS')
        RunClearFileAndData(station+"_"+name)


    except Exception as e:
        print (e)
  
    

