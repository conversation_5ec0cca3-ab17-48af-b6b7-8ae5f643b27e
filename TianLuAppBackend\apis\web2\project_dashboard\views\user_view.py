import datetime
import decimal
import json

from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,)

from django.db import transaction
from django.db.models import Min
from django.db.models import  Q
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.user import models
from apis.user.models import Role, StationDetails
from common import common_response_code
from encryption import jwt_encryption
from serializers import user_serializers
from tools.count import unit_convert
from django.core.paginator import Paginator

# Create your views here.
from tools.get_ac_http import error_log

from apis.web.filters import UserDetailsFilter


class WebUsernamePasswordLoginView(APIView):
    """web账号密码登录"""
    def post(self, request):
        ser = user_serializers.WebUsernamePasswordLoginSerializer(data=request.data)
        ty = request.data.get("ty")
        ty=int(ty)
        if not ser.is_valid():
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        user_instance = ser.validated_data["username"]
        user_type = user_instance.type
        roles = user_instance.roles.values("permissions__title", "permissions__url").distinct()
        role_list = []
        project_ins = models.Project.objects.filter(is_used=1, user=user_instance).all()
        stations_ins = models.StationDetails.objects.filter(project__user=user_instance, project__in=project_ins).all()
        master_stations = models.MaterStation.objects.filter(project__user=user_instance, project__in=project_ins,
                                                             is_delete=0).all()
        power_count, power_unit = unit_convert(sum([decimal.Decimal(station_ins.rated_power) for station_ins in stations_ins]), "kW")
        capacity_count, capacity_unit = unit_convert(
            sum([decimal.Decimal(station_ins.rated_capacity) for station_ins in stations_ins]), "kWh"
        )
        max_time = models.Project.objects.filter(is_used=1, user=user_instance).aggregate(Min('in_time'))
        run_times = datetime.datetime.now() - max_time["in_time__min"]
        run_days = run_times.days
        stations_count_dic = {
            "power_count": power_count,
            "run_days": run_days,
            "power_unit": power_unit,
            "capacity_count": capacity_count,
            "capacity_unit": capacity_unit,
            "1-1": stations_ins.filter(rated_power=100).count(),
            "1-2": stations_ins.filter(rated_power=200).count(),
            "1-3": stations_ins.filter(rated_power=300).count(),
            "1-4": stations_ins.filter(rated_power=400).count(),
            "count": master_stations.count(),
            "pcs_num": sum([decimal.Decimal(station_ins.battery_cluster) for station_ins in stations_ins]),
        }

        for role in roles:
            role_list.append(
                {
                    "tittle": role["permissions__title"],
                    "url": role["permissions__url"],
                }
            )
        user_ins = user_instance.roles
        user_dic = {
            'role': user_ins,
        }
        related_role_name = list(user_dic['role'].all())
        role_name = []
        for j in related_role_name:
            role_name_d = {}
            role_name_d['id'] = j.id
            role_name_d['role_name'] = j.role_name
            role_name.append(role_name_d)
        role_name_2 = []
        for rr in role_name:
            name_ = models.Role.objects.filter(id=rr['id']).first()
            if name_.type==ty:
                role_name_2.append(rr)
        success_token = jwt_encryption.create_token({"user_id": user_instance.id})
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"用户({user_instance.user_name})登录成功",
                    "success_token": success_token,
                    "user_type": user_type,
                    "roles": role_name_2,
                    "stations": stations_count_dic,
                },
            }
        )

class WebProjectView(APIView):
    '''项目展示'''

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    @transaction.atomic
    def get(self, request, *args, **kwargs):
        try:
            project_ins = models.Project.objects.filter(is_used=1).values("name", "id").order_by('id')
            project_list=[]
            for p in project_ins:
                project_dic = {
                    'name': p['name'],
                    'id': p['id']
                }
                project_list.append(project_dic)
            return Response(project_list)
        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到！"},
            })


class WebUsernameAddView(APIView):
    '''添加用户'''

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    @transaction.atomic
    def post(self, request):
        try:
        # if 1:
            data = request.data
            user_name = request.data.get("user_name")
            login_name = request.data.get("login_name")
            role_id = json.loads(data.get('role_id'))  #角色列表
            role_id_y = json.loads(data.get('role_id_y'))  #移动端角色列表
            mobile = request.data.get("mobile")
            email = request.data.get("email", '')
            gender = request.data.get("gender",3)#(1, "男"), (2, "女"), (3, "未认证")
            project_id = json.loads(data.get('project_id'))  # 项目id列表
            tissue = request.data.get("tissue")#组织
            tissue_id = request.data.get("tissue_id")#组织
            remark = request.data.get("remark")#备注
            type_ = request.data.get("type")#(0, "内部人员"), (1, "客户"), (2, "开发人员"))
            type_=int(type_)
            if gender=='':
                gender=3
            import re
            def is_phone_number(s):
                return re.match(r'^\d{11}$', s) is not None

            if is_phone_number(mobile)==False:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "请输入正确的手机号格式！"},
                })
            if models.UserDetails.objects.filter(mobile=mobile).first():
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "手机号已存在！"},
                })

            user_name_ = models.UserDetails.objects.filter(user_name=user_name,mobile=mobile,tissue=tissue).first()
            if user_name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户已存在！"},
                })

            try:
                user_ins = models.UserDetails.objects.create(
                    login_name=login_name,
                    user_name=user_name,
                    mobile=mobile,
                    email=email,
                    gender=gender,
                    tissue=tissue,
                    tissue_id=tissue_id,
                    password='6159dfdb831c7a296f187929843c80e3',
                    remark=remark if remark else '',
                    type=type_
                )

                role_id_list=role_id+role_id_y
                for r in role_id_list:
                    user_ins.roles.add(r)

                for p in project_id:
                    try:
                        project = models.Project.objects.get(id=p)
                    except Exception as e:
                        return Response({
                            "code": common_response_code.ERROR,
                            "data": {"message": "error", "detail": "项目ID 不存在！"},
                        })
                    project.user.add(user_ins)

                    master_stations = models.MaterStation.objects.filter(project=project, is_delete=0).all()
                    for master_station in master_stations:
                        user_ins.master_stations.add(master_station)

                        stations = models.StationDetails.objects.filter(is_delete=0, master_station=master_station).all()
                        for s in stations:
                            user_ins.stations.add(s)

            except Exception as e:
                error_log.error(e)
                raise e

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "添加成功！"},
            })
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "添加失败！"},
            })


class UserInfoViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def get(self, request, *args, **kwargs):
        try:
            p_id = self.kwargs['id']    #用户id
            try:
                user_ins = models.UserDetails.objects.get(id=p_id, is_used=1)
            except:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "未查询到"},
                })

            user_dic = {
                'login_name': user_ins.login_name,
                'user_name': user_ins.user_name,
                'mobile': user_ins.mobile,
                'email': user_ins.email,
                'gender': user_ins.gender,
                'project': user_ins.project_set.filter(is_used=1).values('id', 'name'),
                'tissue': user_ins.tissue,
                'tissue_id': user_ins.tissue_id,
                'remark': user_ins.remark,
                'type': user_ins.type
            }

            if user_dic['project'].exists():
                for item in user_dic['project']:
                    item['project_name'] = item['name']

            user_role = {
                'role': user_ins.roles
            }

            related_role_name = list(user_role['role'].all())
            role_id_ = []
            role_id_y_ = []

            for j in related_role_name:
                if j.type == 0:
                    role_id_y_.append(j.id)
                elif j.type == 1:
                    role_id_.append(j.id)

            user_dic['role_id'] = role_id_
            user_dic['role_id_y'] = role_id_y_

            return Response(user_dic)

        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！"},
            })

    @transaction.atomic
    def put(self, request, *args, **kwargs):
        try:
            data = request.data
            p_id = data['id']
            user_name = request.data.get("user_name")
            login_name = request.data.get("login_name")
            role_id = json.loads(data.get('role_id'))  # 角色列表
            role_id_y = json.loads(data.get('role_id_y'))  #移动端角色列表
            mobile = request.data.get("mobile")
            email = request.data.get("email", '')
            gender = request.data.get("gender",3)  # (1, "男"), (2, "女"), (3, "未认证")
            project_ids = json.loads(data.get('project_id'))  # 项目id列表
            tissue = request.data.get("tissue")  # 组织
            tissue_id = request.data.get("tissue_id")  # 组织id
            remark = request.data.get("remark")  # 备注
            type_ = request.data.get("type")  # (0, "内部人员"), (1, "客户"), (2, "开发人员"))
            type_=int(type_)
            if gender=='':
                gender=3

            name_ = models.UserDetails.objects.filter(user_name=user_name,mobile=mobile,tissue=tissue, is_used=1).exclude(id=p_id).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户已存在！"},
                })
            try:
                # 修改用户表
                user_ins = models.UserDetails.objects.get(id=p_id)
                user_ins.login_name = login_name
                user_ins.user_name = user_name
                user_ins.mobile = mobile
                user_ins.email = email
                user_ins.gender = gender
                user_ins.tissue = tissue
                user_ins.tissue_id = tissue_id
                user_ins.type = type_
                user_ins.remark = remark if remark else ''

                roles_list_id=role_id+role_id_y
                roles = Role.objects.filter(id__in=roles_list_id)
                user_ins.roles.set(roles)
                stations_list=[]

                for p in project_ids:
                    try:
                        project = models.Project.objects.get(id=p)
                    except Exception as e:
                        return Response({
                            "code": common_response_code.ERROR,
                            "data": {"message": "error", "detail": "项目ID 不存在！"},
                        })
                master_stations = models.MaterStation.objects.filter(project_id__in=project_ids, is_delete=0).all()
                user_ins.master_stations.set(master_stations)
                # if master_stations.exists():
                #         for master_station in master_stations:
                slave_stations = models.StationDetails.objects.filter(is_delete=0, project_id__in=project_ids).all()
                user_ins.stations.set(slave_stations)
                        # user_ins.master_stations.set(master_stations)
                user_ins.project_set.set(project_ids)
                user_ins.save()
            except Exception as e:
                error_log.error(e)
                raise e

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "修改成功！"},
            })
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "修改失败！"},
            })

def tem_user_instances(args):
    pass


class UserViews(APIView):
    '''查询用户'''
    #
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        try:
            user_name = request.query_params.get("user_name", None)
            tissue = request.query_params.get("tissue", None)
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 15))


            # user_ins = models.UserDetails.objects.filter(Q(user_name__contains=user_name)&Q(tissue__contains=tissue)&Q(category__contains=category), is_used=1).order_by('id')

            if user_name and not tissue:
                user_ins = models.UserDetails.objects.filter(Q(user_name__contains=user_name), is_used=1).order_by('id')
            elif tissue and not user_name:
                user_ins = models.UserDetails.objects.filter(Q(tissue__contains=tissue), is_used=1).order_by('id')
            elif tissue and user_name:
                user_ins = models.UserDetails.objects.filter(Q(user_name__contains=user_name,tissue__contains=tissue), is_used=1).order_by('id')
            else:
                user_ins = models.UserDetails.objects.filter(is_used=1).order_by('id')
            users_ins = user_ins.values("id","user_name","login_name","mobile","gender","tissue","tissue_id","email")

            #筛选
            if user_name and not tissue:
                user_filter = UserDetailsFilter()
                tem_user_instances = user_filter.filter_by_username(users_ins, request.query_params.get('user_name'))
            elif tissue and not user_name:
                user_filter = UserDetailsFilter()
                tem_user_instances = user_filter.filter_by_tissue(users_ins, request.query_params.get('tissue'))
            elif tissue and user_name:
                user_filter = UserDetailsFilter()
                tem_user_instances = user_filter.filter_by_(users_ins, request.query_params.get('user_name'),request.query_params.get('tissue'))
            else:
                user_filter = UserDetailsFilter(queryset=users_ins, data=request.query_params)
                tem_user_instances = user_filter.qs

            # 分页
            paginator_info = dict()
            if page and page_size:
                paginator = Paginator(tem_user_instances, page_size)
                tem_user_instances_ = paginator.get_page(page)

                paginator_info = {
                    "page": page,
                    "page_size": page_size,
                    "pages": paginator.num_pages,
                    "total_count": paginator.count
                }
            else:
                tem_user_instances_ = tem_user_instances
            new_user_ins=[]
            for t in tem_user_instances_:
                new_user_ins.append(t)

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": new_user_ins, "paginator_info": paginator_info},
                }
            )

        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class UserDeViews(APIView):#删除
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic()
    def post(self, request):
        p_id = request.data.get('id')
        if p_id:
            transaction.atomic()
            try:
                obj = models.UserDetails.objects.get(id=p_id)

                obj.is_used = 2
                obj.save()

                # 删除用户关联的 project 和 station
                related_projects = obj.project_set.filter(is_used=1).all()
                for project in related_projects:
                    project.user.remove(obj)

                related_master_stations = obj.master_stations.filter(is_delete=0).all()
                for master_station in related_master_stations:
                    master_station.userdetails_set.remove(obj)

                related_stations = obj.stations.all()
                for station in related_stations:
                    station.userdetails_set.remove(obj)
            except Exception as e:
                error_log.error(e)
                raise

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "删除成功！"},
            })

        else:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "id为必填项"},
            })


