#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:42:37
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_policy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-30 17:50:36



from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecasePolicy(user_Base):
    u'政策信息表'
    __tablename__ = "t_side_forecase_policy"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    policy_flag = Column(CHAR(1), nullable=False, comment=u"1政策 0内参")
    title_ = Column(String(256), nullable=False, comment=u"标题")
    author = Column(String(50), nullable=True, comment=u"作者")
    content = Column(Text, nullable=True, comment=u"内容")
    status = Column(CHAR(1), nullable=False, server_default='0',comment=u"状态0待发表，1已发表")
    policy_type_id = Column(Integer, ForeignKey("t_side_forecase_policy_type.id"),nullable=False, comment=u"政策类型表id")

    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    forecase_policy_type = relationship("ForecasePolicyType", backref="forecase_policy_type")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'title_':'%s','author':'%s'}" %(self.id,self.title_,self.author)

   