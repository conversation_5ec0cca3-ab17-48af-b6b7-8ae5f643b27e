# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/25 下午4:46
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Project  : TranslateDeom
# @File     : settings.py
# @Software : PyCharm
from TianLuAppBackend import settings

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        # "PORT": 3306,

        # 生产数据库
        # "HOST": "rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com",  # ip
        # 'NAME': 'db_tianluapp_prd_v1.1',  # 数据库名字
        # "USER": "tianlu_app",
        # "PASSWORD": "P@ssw0rd!",

        # 测试数据库
        # "HOST": "rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com",  # ip
        # 'NAME': 'db_tianluapp',  # 数据库名字
        # "USER": "tianlu_app",
        # "PASSWORD": "P@ssw0rd!",

        # 仿真数据库
        # "HOST": "rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com",  # ip
        # 'NAME': 'db_tianluapp_fz1',  # 数据库名字
        # "USER": "tianlu_app",
        # "PASSWORD": "P@ssw0rd!",

        # 仿真数据库--ssh 隧道链接
        # "HOST": "127.0.0.1",  # ip
        # 'NAME': 'db_tianluapp_fz1',  # 数据库名字
        # "USER": "tianlu_app",
        # "PORT": 3386,
        # "PASSWORD": "P@ssw0rd!",

        # 新仿真数据库
        # "HOST": "*************",
        # 'NAME': 'db_tianluapp_fz',
        # # 'NAME': 'dm_shanhai_fz',
        # "USER": "rsync_tianlu",
        # "PASSWORD": "rhycROOT123#",
        # "PORT": 3306,

        # 引用天禄的settings.py文件
        "HOST": settings.DATABASES['default']['HOST'],
        'NAME': settings.DATABASES['default']['NAME'],
        "USER": settings.DATABASES['default']['USER'],
        "PASSWORD": settings.DATABASES['default']['PASSWORD'],
        "PORT": settings.DATABASES['default']['PORT'],

        # 新仿真数据库---ssh 隧道链接
        # "HOST": "127.0.0.1",
        # 'NAME': 'db_tianluapp_fz',
        # "USER": "rsync_tianlu",
        # "PORT": 3387,
        # "PASSWORD": "rhycROOT123#",

        # 本地开发数据库
        # 'HOST': '127.0.0.1',  # ip
        # 'NAME': 'fany',  # 数据库名字
        # 'USER': 'root',
        # "PASSWORD": "123456",
    }
}

# 本地开发数据库
# REDIS_CONF = {
#     "HOST": "127.0.0.1",
#     "PORT": 6379,
#     "DB": 0,
#     "PASSWORD": "",
#     "decode_responses": True
# }

# 线上 redis 数据库
REDIS_CONF = {
    "HOST": "***********",
    "PORT": 6379,
    "DB": 2,
    "PASSWORD": "A45lEdj&f335@3s5h*8g",
    "decode_responses": True
}