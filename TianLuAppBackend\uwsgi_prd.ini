[uwsgi]
# 井号是注释，文件以[uwsgi]开头
# Http通信方式的IP地址:端口号
http=0.0.0.0:19190
# 项目当前工作目录，注意从根目录“/”开始
chdir=/home/<USER>/TianLuAppBackend
# 项目中wsgi.py文件的目录，相对于当前工作目录(就是上一个目录下的同名文件夹)
wsgi-file=TianLuAppBackend/wsgi.py
# 进程个数，这个和下面的threads根据自己需要改变
process=5
# 每个进程的线程个数
threads=6
# 一下两个文件都将在运行后自动创建在uwsgi.ini同路径下
# 服务的pid记录文件，可以在uwsgi.ini所在路径下使用uwsgi --stop uwsgi.pid来终止运行
pidfile=uwsgi.pid
# 服务的目录文件位置(代表是否后台启动以及启动的日志输出在哪里)
daemonize=uwsgi.log
# 开启主进程管理模式
master=true
# 有需要可以看我参照的文章自行配置静态目录
workers=6
# 结束后是否清理文件
vacuum = true