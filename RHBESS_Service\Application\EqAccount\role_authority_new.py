#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-17 09:35:35
#@FilePath     : \RHBESS_Service\Application\EqAccount\role_authority_new.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-22 11:47:11


# -*- coding:utf8 -*-
import json
import logging
from Application.Models.User.role import Role
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.station import Station
from Application.Models.User.authority_new import AuthorityNew 
from Application.Models.User.role_authority import RoleAuthority
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import judge_phone,computeMD5
import tornado.web
from sqlalchemy import func,or_


class RoleAuthorityNewIntetface(BaseHandler):
    '''
    @description: 用户权限管理新需求
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        try:
            if kt == 'GetUesrAuthority':  # 获取指定
                id = self.get_argument('id',None)  # 角色id
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                all,datas = [],{}
                roleInfo = user_session.query(Role).filter(Role.id==id).first()
                if not roleInfo:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")

                Rauthority = eval(roleInfo.authority)
                station = eval(roleInfo.station_id)
                stationInfos = []
                for s in station:
                    stat = user_session.query(Station.descr.label('descr'),Station.en_descr.label('en_descr')).filter(Station.index==s).first()
                    if not stat:
                        continue
                    if lang == 'en':
                        stationInfos.append({'var': True, 'label': stat.en_descr})
                    else:
                        stationInfos.append({'var':True,'label':stat.descr})

                if Rauthority:  # 有权限
                    if lang == 'en':
                        Rauthority = eval(roleInfo.en_authority.replace('false', 'False').replace('true', 'True'))
                    else:
                        Rauthority = Rauthority
                    datas = {"authority":Rauthority,"select_key":eval(roleInfo.select_key),'station_info':stationInfos,'station_key':station}
                else:
                    aus = user_session.query(AuthorityNew).filter(or_(AuthorityNew.parent_id==None,AuthorityNew.parent_id=='')).order_by(AuthorityNew.sort_order).all()
                    for u in aus:
                        authority = True if u.authority == 1 else False
                        if lang == 'en':
                            label = u.en_label
                            tags = eval(u.en_tags)
                        else:
                            label = u.label
                            tags = eval(u.tags)
                        ob = {'label':label,'tags':tags,'index':u.index,'authority':authority}
                        if u.icon:
                            ob['icon'] = u.icon
                        if authority:
                            ob['station'] = stationInfos
                        ob['disabled'] = True if u.disabled == 1 else False
                        if self.rolechile(u,stationInfos):
                            ob['children'] = self.rolechile(u,stationInfos,lang)
                        all.append(ob)
                    datas = {"authority":all,"select_key":eval(roleInfo.select_key),'station_info':stationInfos,'station_key':station}
                if lang == 'en':
                    return self.returnTypeSuc_en(data=datas, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=datas, info=None, lang=None)

            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

    # # @tornado.web.authenticated
    # def post(self, kt):
    #     # self.refreshSession()  # 刷新session
    #     try:
    #         pass
            
    #     except Exception as E:
    #         logging.error(E)
    #         user_session.rollback()
    #         return self.requestError()
    #     finally:
    #         user_session.close()
       

    def rolechile(self, Bean,station,lang=None):
        # 递归构建树状权限
        all = []
        auts = user_session.query(AuthorityNew).filter(AuthorityNew.parent_id==Bean.index).all()

        for u in auts:
            authority = True if u.authority == 1 else False
            if lang == 'en':
                label = u.en_label
                tags = eval(u.en_tags)
            else:
                label = u.label
                tags = eval(u.tags)
            ob = {'label': label, 'tags': tags, 'index': u.index, 'authority': authority}
            if u.icon:
                ob['icon'] = u.icon
            if authority:
                ob['station'] = station
            ob['disabled'] = True if u.disabled == 1 else False
            if self.rolechile(u,station):
                ob['children'] = self.rolechile(u,station)
            all.append(ob)
        return all
    
