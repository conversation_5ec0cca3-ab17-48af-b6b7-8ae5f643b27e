#!/usr/bin/env python
# coding=utf-8
#@Information:基准功率
import time
import datetime

import tornado.web,ast

from Application.Models.User.power_base_infos_t import PowerBaseInfos
from Application.Models.User.power_base_t import PowerBase
from Application.Models.base_handler import BaseHandler
from Tools.Utils.time_utils import timeUtils
from Tools.DB.mysql_user import  user_session
from Application.HistoryData.his_data_alluse import DEBUG
from Application.Models.User.custom_report_t import ReportCustom
from Tools.Utils.num_utils import *
from Application.Models.User.station import Station
from sqlalchemy import func,or_,and_
import ast

class ReferencePowerInterface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            if kt == 'GetPower':
                startTime = self.get_argument('startTime', '')  # 开始时间
                # endTime = self.get_argument('endTime', '')  # 开始时间
                user = self.get_argument('user', None)  # 用户
                descr = self.get_argument('descr', None)  # 描述
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter, data = [PowerBase.is_use == '1'], []
                if descr:
                    if lang == 'en':
                        filter.append(PowerBase.en_descr.contains(descr))
                    else:
                        filter.append(PowerBase.descr.contains(descr))
                if startTime:
                    filter.append(PowerBase.start_time >= startTime)
                if user:
                    if lang == 'en':
                        filter.append(PowerBase.en_create_user_name.contains(user))
                    else:
                        filter.append(PowerBase.create_user_name.contains(user))
                if DEBUG:
                    logging.info('startTime:%s,user:%s,descr:%s,pageNum:%s,pageSize:%s' % (startTime, user, descr, pageNum, pageSize))
                total = user_session.query(func.count(PowerBase.id)).filter(*filter).scalar()
                all = user_session.query(PowerBase).filter(*filter).order_by(PowerBase.start_time.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                session = self.getOrNewSession()
                user_id = session.user['id']
                for org in all:
                    list1 = []
                    org=eval(str(org))
                    org['is_del'] = 1 if org['create_user'] == user_id else 0 # 是否支持修改删除记录；1：支持；0：禁止
                    power1 = user_session.query(PowerBaseInfos).filter(PowerBaseInfos.base_id == org['id']).all()
                    if lang == 'en':
                        org['descr']=org['en_descr']
                        org['create_user_name']=org['en_create_user_name']
                        if power1:
                            for p in power1:
                                p = eval(str(p))
                                if lang == 'en':
                                    p['remark'] = p['en_remark']
                                    list1.append(p)
                        org['list1']=list1
                        data.append(org)
                    else:
                        org=eval(str(org))
                        if power1:
                            for p in power1:
                                p = eval(str(p))
                                list1.append(p)
                        org['list1'] = list1
                        data.append(org)
                self.returnTotalSuccess(data, total)
        except Exception as E:
            user_session.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        station = self.get_argument('db', 'his')
        try:
        # if 1:
            if kt == 'AddPower':  # 保存基准功率
                startTime = self.get_argument('startTime',  '')  # 开始时间
                endTime = self.get_argument('endTime', '--')# 结束时间
                descr = self.get_argument('descr', None)  # 描述
                list1 = self.get_argument('list1', [])  # 基准功率详情
                if DEBUG:
                    logging.info('list1:%s' % (list1))
                if station and startTime!='' and list1!=[]:
                    # 覆盖
                    filter1 = [PowerBase.station == station, PowerBase.is_use == '1', or_(PowerBase.start_time>=startTime)]
                    # 修改数据结束时间
                    filter3 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time < startTime]
                else:
                    if lang=='en':
                        return self.customError("Required fields are not filled in!")
                    else:
                        return self.customError("必填项未填写！")
                session = self.getOrNewSession()
                create_user_id = session.user['id']
                create_user = session.user['name']
                a1 = user_session.query(PowerBase).filter(*filter1).first()
                if a1:
                    if lang == 'en':
                        return self.customErrorId(
                            "The base power already exists. Check whether the base power is overwritten!", a1.id)
                    else:
                        return self.customErrorId("基准功率已存在,是否覆盖！", a1.id)

                a3 = user_session.query(PowerBase).filter(*filter3).order_by(PowerBase.start_time.desc()).first()
                if a3:
                    endTime_3 = timeUtils.getAgoTimeS(startTime, 1)
                    a3.end_time = endTime_3
                    a3.update_user = create_user_id
                    a3.update_time = timeUtils.getNewTimeStr()
                self.add_(create_user, create_user_id, descr, endTime, lang, list1, startTime, station)

                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdatePower':  # 修改基准功率(另存为接口）
                id = self.get_argument('id', None)
                startTime = self.get_argument('startTime', '')  # 开始时间
                endTime = self.get_argument('endTime', '--')  # 结束时间
                descr = self.get_argument('descr', None)  # 描述
                list1 = self.get_argument('list1', [])  # 基准功率详情
                if DEBUG:
                    logging.info('list1:%s' % (list1))
                if station and startTime!='' and id:
                    filter = [PowerBase.id == int(id), PowerBase.is_use == '1']
                    if list1:
                        # 覆盖
                        filter1 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time>=startTime, PowerBase.id != int(id)]
                        # 修改数据结束时间
                        filter3 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time < startTime, PowerBase.id != int(id)]
                    else:
                        # 覆盖
                        filter1 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time>=startTime]
                        # 修改数据结束时间
                        filter3 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time < startTime]
                else:
                    if lang=='en':
                        return self.customError("Required fields are not filled in!")
                    else:
                        return self.customError("必填项未填写！")
                a = user_session.query(PowerBase).filter(*filter).first()
                if not a:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError('无效id')
                session = self.getOrNewSession()
                update_user_id = session.user['id']
                update_user = session.user['name']

                if not user_session.query(PowerBase).filter(PowerBase.create_user==update_user_id, PowerBase.id==int(id)).first():
                    if list1:
                        if lang == 'en':
                            return self.customError("Only supports creating user modifications! ")
                        else:
                            return self.customError('仅支持创建用户修改！')
                a1 = user_session.query(PowerBase).filter(*filter1).first()
                if a1:
                    if lang == 'en':
                        return self.customErrorId("The base power already exists. Check whether the base power is overwritten!", a1.id)
                    else:
                        return self.customErrorId("基准功率已存在,是否覆盖！", a1.id)
                a3 = user_session.query(PowerBase).filter(*filter3).order_by(PowerBase.start_time.desc()).first()
                if a3:
                    endTime_3 = timeUtils.getAgoTimeS(startTime, 1)
                    a3.end_time=endTime_3
                    a3.update_user=update_user_id
                    a3.update_time=timeUtils.getNewTimeStr()
                    user_session.commit()

                # 保存
                if list1:
                    self.update_(descr, endTime, id, list1, startTime, station, update_user, update_user_id, lang)
                else:
                    power3 = user_session.query(PowerBase).filter(
                        PowerBase.id == id).first()  # 根据id查询基准功率复制
                    power3_Infos = user_session.query(PowerBaseInfos).filter(
                        PowerBaseInfos.base_id == id).all()  #
                    # 新增一条记录
                    power_base_0 = PowerBase(start_time=startTime, end_time=endTime,
                                             create_user=update_user_id,
                                             station=station, update_user=update_user_id,
                                             chag_times=power3.chag_times, disg_times=power3.disg_times,
                                             static_times=power3.static_times,
                                             create_user_name=update_user, en_create_user_name=update_user,
                                             op_ts=timeUtils.getNewTimeStr(), is_use='1', en_descr=descr,
                                             descr=descr)
                    user_session.add(power_base_0)
                    user_session.commit()
                    for r in power3_Infos:
                        power = PowerBaseInfos(base_id=power_base_0.id, start_time=r.start_time,
                                               end_time=r.end_time, cd_flag=r.cd_flag, pv_flag=r.pv_flag,
                                               power=r.power, remark=r.remark, en_remark=r.remark)
                        user_session.add(power)
                    user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'CoverPower':  # 覆盖
                id = self.get_argument('id', None)
                startTime = self.get_argument('startTime', '')  # 开始时间
                endTime = self.get_argument('endTime', '--')  # 结束时间
                descr = self.get_argument('descr', None)  # 描述
                list1 = self.get_argument('list1', [])  # 基准功率详情

                if DEBUG:
                    logging.info('list1:%s' % (list1))


                if id:
                    user_session.query(PowerBase).filter(PowerBase.start_time >= startTime, PowerBase.station == station, PowerBase.id!=int(id), PowerBase.is_use==1).update({'is_use': 0})
                    filter3 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.id!=int(id), PowerBase.start_time < startTime]
                else:
                    filter3 = [PowerBase.station == station, PowerBase.is_use == '1', PowerBase.start_time < startTime]
                    user_session.query(PowerBase).filter(PowerBase.start_time>=startTime, PowerBase.station == station, PowerBase.is_use==1).update({'is_use': 0})

                session = self.getOrNewSession()
                create_user_id = session.user['id']
                create_user = session.user['name']



                a3 = user_session.query(PowerBase).filter(*filter3).order_by(PowerBase.start_time.desc()).first()
                if a3:
                    endTime_3 = timeUtils.getAgoTimeS(startTime, 1)
                    a3.end_time=endTime_3
                    a3.update_user=create_user_id
                    a3.update_time=timeUtils.getNewTimeStr()
                    user_session.commit()
                if id and list1:
                    self.update_(descr, endTime, int(id), list1, startTime, station, create_user, create_user_id, lang)
                elif id and not list1:
                    power3 = user_session.query(PowerBase).filter(
                        PowerBase.id == int(id)).first()  # 根据id查询基准功率复制
                    power3_Infos = user_session.query(PowerBaseInfos).filter(
                        PowerBaseInfos.base_id == int(id)).all()  #
                    # 新增一条记录
                    power_base_0 = PowerBase(start_time=startTime, end_time=endTime,
                                             create_user=create_user_id,
                                             station=station, update_user=create_user_id,
                                             chag_times=power3.chag_times, disg_times=power3.disg_times,
                                             static_times=power3.static_times,
                                             create_user_name=create_user, en_create_user_name=create_user,
                                             op_ts=timeUtils.getNewTimeStr(), is_use='1', en_descr=descr,
                                             descr=descr)
                    user_session.add(power_base_0)
                    user_session.commit()
                    for r in power3_Infos:
                        power = PowerBaseInfos(base_id=power_base_0.id, start_time=r.start_time,
                                               end_time=r.end_time, cd_flag=r.cd_flag, pv_flag=r.pv_flag,
                                               power=r.power, remark=r.remark, en_remark=r.remark)
                        user_session.add(power)
                    power3.is_use = 0
                    user_session.commit()
                else:
                    self.add_(create_user, create_user_id, descr, endTime, lang, list1, startTime, station)

                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'DeletePower':  # 删除
                id = self.get_argument('id',None)
                page = user_session.query(PowerBase).filter(PowerBase.id==id).first()
                if not lang:
                    lang = 'zh'
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    def update_(self, descr, endTime, id, list1, startTime, station, update_user, update_user_id, lang):
        cd_time = []  # 充电时段
        di_time = []  # 放电时段
        jz_time = []  # 静置时段
        list_data = eval(list1)
        power1 = user_session.query(PowerBaseInfos).filter(PowerBaseInfos.base_id == id).all()
        for r in list_data:
            if r['cd_flag'] == '-1':
                cd_time.append(r['start_time'] + '~' + r['end_time'])
            elif r['cd_flag'] == '0':
                jz_time.append(r['start_time'] + '~' + r['end_time'])
            elif r['cd_flag'] == '1':
                di_time.append(r['start_time'] + '~' + r['end_time'])
            for rr in power1:
                if rr.start_time == r['start_time'] and rr.end_time == r['end_time']:
                    rr.cd_flag = r['cd_flag']
                    rr.pv_flag = r['pv_flag']
                    rr.power = r['power']
                    rr.remark = r['remark']
                    rr.en_remark = r['remark']
        cd_time = (';'.join(str(cd_time).split(',')))[1:-1] if cd_time != [] else '--'
        di_time = (';'.join(str(di_time).split(',')))[1:-1] if di_time != [] else '--'
        jz_time = (';'.join(str(jz_time).split(',')))[1:-1] if jz_time != [] else '--'
        cd_time = ''.join(str(cd_time).split("'"))
        di_time = ''.join(str(di_time).split("'"))
        jz_time = ''.join(str(jz_time).split("'"))

        if lang == 'en':
            translate_instance = Translate_cls(ty=1)
            zh_descr = translate_instance.str_chinese(descr)
            en_descr = descr
        else:
            translate_instance = Translate_cls(ty=2)
            en_descr = translate_instance.str_chinese(descr)
            zh_descr = descr

        power2 = user_session.query(PowerBase).filter(PowerBase.id == id).first()
        power2.start_time = startTime
        power2.end_time = endTime
        power2.chag_times = cd_time
        power2.disg_times = di_time
        power2.static_times = jz_time
        power2.station = station
        power2.update_user = update_user_id
        power2.update_time = timeUtils.getNewTimeStr()
        power2.en_descr = en_descr
        power2.descr = zh_descr
        user_session.commit()

    def add_(self, create_user, create_user_id, descr, endTime, lang, list1, startTime, station):
        if lang == 'en':
            translate_instance = Translate_cls(ty=1)
            zh_descr = translate_instance.str_chinese(descr)
            en_descr = descr
        else:
            translate_instance = Translate_cls(ty=2)
            en_descr = translate_instance.str_chinese(descr)
            zh_descr = descr
        power_base = PowerBase(start_time=startTime, end_time=endTime, create_user=create_user_id,
                               station=station, update_user=create_user_id,
                               chag_times='--', disg_times='--', static_times='--',
                               create_user_name=create_user, en_create_user_name=create_user,
                               op_ts=timeUtils.getNewTimeStr(), is_use='1', en_descr=en_descr, descr=zh_descr)
        user_session.add(power_base)
        user_session.commit()
        # 保存
        if list1 != []:
            list_data = eval(list1)
            cd_time = []  # 充电时段
            di_time = []  # 放电时段
            jz_time = []  # 静置时段
            for r in list_data:
                if r['cd_flag'] == '-1':
                    cd_time.append(r['start_time'] + '~' + r['end_time'])
                elif r['cd_flag'] == '0':
                    jz_time.append(r['start_time'] + '~' + r['end_time'])
                elif r['cd_flag'] == '1':
                    di_time.append(r['start_time'] + '~' + r['end_time'])
                if lang == 'en':
                    translate_instance = Translate_cls(ty=1)
                    zh_remark = translate_instance.str_chinese(r['remark'])
                    en_remark = r['remark']
                else:
                    translate_instance = Translate_cls(ty=2)
                    en_remark = translate_instance.str_chinese(r['remark'])
                    zh_remark = r['remark']
                power = PowerBaseInfos(base_id=power_base.id, start_time=r['start_time'],
                                       end_time=r['end_time'], cd_flag=r['cd_flag'], pv_flag=r['pv_flag'],
                                       power=r['power'], remark=zh_remark, en_remark=en_remark)
                user_session.add(power)
            cd_time = (';'.join(str(cd_time).split(',')))[1:-1] if cd_time != [] else '--'
            di_time = (';'.join(str(di_time).split(',')))[1:-1] if di_time != [] else '--'
            jz_time = (';'.join(str(jz_time).split(',')))[1:-1] if jz_time != [] else '--'
            cd_time = ''.join(str(cd_time).split("'"))
            di_time = ''.join(str(di_time).split("'"))
            jz_time = ''.join(str(jz_time).split("'"))
            power_base.chag_times = cd_time
            power_base.disg_times = di_time
            power_base.static_times = jz_time
            user_session.commit()



