package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 策略导入VO
 */
@Data
@ApiModel("策略导入VO")
public class StrategyImportVO {

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("策略数据列表")
    private List<StrategyDataItemVO> data;

    @ApiModelProperty("月份列表")
    private List<String> monthList;

    /**
     * 策略数据项VO
     */
    @Data
    @ApiModel("策略数据项VO")
    public static class StrategyDataItemVO {

        @ApiModelProperty("开始时间")
        private String startTime;

        @ApiModelProperty("结束时间")
        private String endTime;

        @ApiModelProperty("PV值")
        private Object pv;

        @ApiModelProperty("充电配置")
        private Object chargeConfig;

        @ApiModelProperty("RL值")
        private String rl;
    }
}
