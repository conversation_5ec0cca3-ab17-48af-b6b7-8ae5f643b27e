import os
import json
import ast
import datetime
import time
import traceback
import uuid
import random
import concurrent.futures
import paho.mqtt.client as mqtt

from django.db.models import Q, Max, F
from django.conf import settings
from django.db import transaction
from openpyxl import Workbook, load_workbook

from common.constant import EMPTY_STR_LIST
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)

from rest_framework.views import APIView
from rest_framework.response import Response
from common import common_response_code
from apis.user import models
from tools.aly_send_smscode import Sample
from LocaleTool.common import redis_pool, TranslateCls
from tools.minio_tool import MinioTool
from apis.statistics_apis.models import UserStrategy, Month, StrategyApplyHistory, UserStrategyCategoryNew, UserStrategyHours
from apis.web.serializers import get_redis_connection, MonitorSendMobileMessageSerializer, UserStrategyApplySerializer, \
    UserStrategyApplyDefaultSerializer, UserStrategyCompareSerializer, UserStrategyCategorySerializerNew, UpdateUserStrategyCategorySerializerNew, UserStrategyApplyRealtimeSerializer
from encryption.AES_symmetric_encryption import EncryptDate
from apis.app2.utils import paging
from apis.statistics_apis.main import mtqq_station_strategy


success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


class UserStrategyCategoryView(APIView):
    """新.用户自动模式配置-分类：添加/修改/查询列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    @transaction.atomic
    def post(self, request):
        """
        添加用户策略
        """""
        lang = request.headers.get("lang", 'zh')
        ser = UserStrategyCategorySerializerNew(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"用户自动控制策略-分类: 字段校验失败{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("用户自动控制策略-分类:字段校验失败:{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        save_id = transaction.savepoint()
        try:
            category = ser.save()
            e = category.id
            transaction.savepoint_commit(save_id)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "用户自动控制策略：添加成功" if lang == 'zh' else "Add success.",
                        "category_id": e,
                    },
                }
            )
        except Exception as e:
            error_log.error("新建策略失败：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "新建策略失败" if lang == 'zh' else "Add failed."},
                }
            )

    @transaction.atomic
    def put(self, request, pk):
        """
        编辑用户策略
        """""
        lang = request.headers.get("lang", 'zh')
        try:
            category_instance = UserStrategy.objects.get(id=pk)
        except Exception as e:
            error_log.error("用户自动控制策略:资源不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else
                    "Resource does not exist."},
                }
            )
        strategy = UserStrategy.objects.filter(~Q(id=pk), Q(name=request.data['name']) | Q(en_name=request.data['name']),
                                               user_id=request.user['user_id'],
                                               is_delete=0).first()
        if strategy:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略: 策略名称重复" if lang == 'zh' else
                             "The strategy name is repeated."},
                }
            )
        ser = UpdateUserStrategyCategorySerializerNew(category_instance, data=request.data,
                                                   context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"用户自动控制策略:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("用户自动控制策略:字段校验失败:{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        save_id = transaction.savepoint()
        try:
            ser.save()
            transaction.savepoint_commit(save_id)
        except Exception as e:
            error_log.error("编辑策略失败：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "编辑策略失败" if lang == 'zh' else "Update failed."},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "用户自动控制策略: 更新成功" if lang == 'zh' else 'Update success.',
                    "category_id": category_instance.id,
                },
            }
        )

    def get(self, request):
        """
        用户策略列表
        """""
        lang = request.headers.get("lang", 'zh')
        user_id = request.user['user_id']
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        project_id = request.query_params.get('project_id')
        station_id = request.query_params.get('station_id')
        search_key = request.query_params.get('search_key', None)
        data = []
        if models.UserDetails.objects.filter(id=user_id, roles=8):
            custom_strategy = UserStrategy.objects.filter(is_delete=0)
        else:
            custom_strategy = UserStrategy.objects.filter(user_id=user_id, is_delete=0)
        strategy_name = ''
        if start_time and end_time:
            custom_strategy = custom_strategy.filter(create_time__gte=start_time, create_time__lte=end_time)
            stations = None
        else:
            if project_id:
                stations = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id, project_id=project_id,
                                                                id=station_id).all()
                # 查询当前下发的策略
                records = StrategyApplyHistory.objects.filter(user=user_id, station_id=station_id).first()
                if records:
                    strategy_name = records.name
            else:
                if station_id:
                    stations = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id, id=station_id).all()
                else:
                    stations = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id).all()

        # 用户自定义策略
        records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
        records_list = [i.get('id')for i in records]
        for custom in custom_strategy.order_by('-create_time').all():
            # 查询策略下发过的站
            count = StrategyApplyHistory.objects.filter(id__in=records_list, name=custom.name).count()

            is_show = 0
            months = custom.month_set.filter(is_valid=1).count()
            if months == 0:
                is_show = 1
            custom_dict = {
                'id': custom.id,
                'name': custom.name if lang == 'zh' else custom.en_name,
                'user_name': custom.user.user_name if lang == 'zh' else custom.user.en_user_name,
                'create_time': custom.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'type': 1,  # 自定义策略,
                'total': count,
                'is_show': is_show,
                'count': custom.userstrategycategorynew_set.count()
            }
            data.append(custom_dict)

        # 默认策略
        if stations:
            set_list = []
            for station in stations:
                set_v = [station.province.name, station.type, station.level]
                if set_v in set_list:
                    continue

                name = station.province.name + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                            common_response_code.ConfLevType.LEVEL[lang][station.level] + '默认运行策略'
                # 查询策略下发过的站
                count = StrategyApplyHistory.objects.filter(id__in=records_list, name=name, user=user_id).count()
                default_dict = {
                    'id': station.id,  # 默认策略站ID
                    'name': name if lang == 'zh' else station.province.en_name + ' ' + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                           common_response_code.ConfLevType.LEVEL[lang][station.level] + ' ' + 'Default Running Strategy',
                    'user_name': '--',
                    'create_time': '--',
                    'type': 0,  # 默认策略
                    'total': count,
                    'count': 12,
                    'province_id': station.province.id,
                    "is_show": 1
                }

                data.append(default_dict)
                set_list.append(set_v)
        if strategy_name:
            for i in data:
                if i.get('name') == strategy_name:
                    data.remove(i)

        if search_key:
            data = [i for i in data if i.get('name') and search_key in i.get('name')]
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )

    def delete(self, request, pk):
        """
        用户策略删除
        """""
        lang = request.headers.get("lang", 'zh')
        try:
            strategy = UserStrategy.objects.get(id=pk)
        except Exception as e:
            error_log.error("用户自动控制策略:资源不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else 'The resource does not exist.'},
                }
            )
        user_id = request.user['user_id']
        # 查询策略下发过的站
        records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
        records_list = [i.get('id')for i in records]
        records_count = StrategyApplyHistory.objects.filter(id__in=records_list, name=strategy.name, user=user_id).count()
        if records_count > 0:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {
                        "message": "error",
                        "detail": "该策略存在关联并网点，不可删除！" if lang == 'zh' else 'This strategy has associated branch points and cannot be deleted.'
                    },
                }
            )
        strategy.is_delete = 1
        strategy.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "用户自动控制策略: 删除成功" if lang == 'zh' else 'User automatic control strategy: successful deletion.'
                },
            }
        )


class UserStrategyCategoryDispositionView(APIView):
    """新.用户自动模式配置-分类：添加/修改/查询列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def get(self, request):
        """
        用户策略列表
        """""
        lang = request.headers.get("lang", 'zh')
        user_id = request.user['user_id']
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        project_id = request.query_params.get('project_id')
        station_id = request.query_params.get('station_id')
        search_key = request.query_params.get('search_key', None)
        data = []
        if models.UserDetails.objects.filter(id=user_id, roles=8):
            custom_strategy = UserStrategy.objects.filter(is_delete=0)
        else:
            custom_strategy = UserStrategy.objects.filter(user_id=user_id, is_delete=0)

        if start_time and end_time:
            custom_strategy = custom_strategy.filter(create_time__gte=start_time, create_time__lte=end_time)
            stations = None
        else:
            if project_id:
                stations = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id, project_id=project_id,
                                                                id=station_id).all()
            else:
                if station_id:
                    stations = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id, id=station_id).all()
                else:
                    stations = models.StationDetails.objects.filter(project__user__id=user_id).all()

        # 用户自定义策略
        for custom in custom_strategy.order_by('-create_time').all():
            custom_dict = {
                'id': str(custom.id),
                'name': custom.name if lang == 'zh' else custom.en_name,
                'type': 1  # 自定义策略,
            }
            data.append(custom_dict)

        # 默认策略
        if stations:
            set_list = []
            for station in stations:
                set_v = [station.province.name, station.type, station.level]
                if set_v in set_list:
                    continue
                if lang == 'zh':
                    name = station.province.name + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                                common_response_code.ConfLevType.LEVEL[lang][station.level] + '默认运行策略'
                    default_dict = {
                        'id': station.english_name,  # 默认策略站ID
                        'name': name,
                        'type': 0  # 默认策略

                    }
                else:
                    name = station.province.en_name + ' ' + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                           common_response_code.ConfLevType.LEVEL[lang][station.level] + ' ' + 'Default Running Strategy'
                    default_dict = {
                        'id': station.english_name,  # 默认策略站ID
                        'name': name,
                        'type': 0  # 默认策略

                    }
                data.append(default_dict)
                set_list.append(set_v)

        if search_key:
            data = [i for i in data if i.get('name') and search_key in i.get('name')]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )

    def delete(self, request, pk):
        """
        用户策略删除
        """""
        lang = request.headers.get("lang", 'zh')
        try:
            strategy = UserStrategy.objects.get(id=pk)
        except Exception as e:
            error_log.error("用户自动控制策略:资源不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else "User Strategy: Resource does not exist."},
                }
            )
        user_id = request.user['user_id']
        # 查询策略下发过的站
        records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
        records_list = [i.get('id')for i in records]
        records_count = StrategyApplyHistory.objects.filter(id__in=records_list, name=strategy.name, user=user_id).count()
        if records_count > 0:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {
                        "message": "error",
                        "detail": "该策略存在关联并网点，不可删除！" if lang == 'zh' else "The strategy has been associated with the station and cannot be deleted!"
                    },
                }
            )
        strategy.is_delete = 1
        strategy.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "用户自动控制策略: 删除成功" if lang == 'zh' else "User Strategy: Delete Success.",
                },
            }
        )


class UserStrategyEchoView(APIView):
    """用户自动模式配置-编辑回显"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def _get_pv_status_new(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).order_by('moment').all()
        if station_instance:
            if settings.FORMATEF:
                for i in station_instance:
                    pv.append(i.pv)
            else:
                for i in station_instance[::4]:
                    pv.append(i.pv)
            return pv
        else:
            return pv


    def _get_pv_status(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = [0] * 24
        n_month = int(month)
        station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).values(
            "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
            "pv14", "pv15", "pv16", "pv17",
            "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
        if station_instance:
            for i in range(24):
                pv[i] = station_instance['pv%s' % i]
            return pv
        else:
            return pv

    def get(self, request, pk):
        lang = request.headers.get("lang", 'zh')
        try:
            strategy = UserStrategy.objects.get(id=pk)
        except Exception as e:
            error_log.error("用户自动控制策略:资源不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else "User Strategy: Resource does not exist."},
                }
            )

        data = {
            'name': strategy.name if lang == 'zh' else strategy.en_name,  # 策略名称
            'category': {}
        }
        # 查询策略分类
        category_res = UserStrategyHours.objects.filter(strategy=strategy).first()
        if not category_res:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": data,
                }
            )
        data['category'] = json.loads(category_res.data)

        if lang == 'en':
            t_cls = TranslateCls(2)
            for i in data['category']:
                i['name'] = t_cls.str_chinese(i['name'])

        elif lang == 'zh':
            t_cls = TranslateCls(1)
            for i in data['category']:
                i['name'] = t_cls.str_chinese(i['name'])

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )

    def post(self, request):
        """详情"""
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')
        month = request.data.get('month')
        station_id = request.data.get('station_id')
        t = request.data.get('t', 1)  # 1:用户自定义；0：默认策略
        sign = request.data.get('sign')  # 标识小程序或者web :  1:小程序
        user_id = request.user['user_id']
        hours = request.data.get('time')  # 查询具体小时：all标识全部，多个以英文逗号隔开
        if not month:
            month = datetime.datetime.now().month
        if not hours or hours == 'all':
            hours = [i for i in range(24)]
        else:
            hours = hours.split(',')
            hours = [int(i)-1 for i in hours]
            hours.sort()
        # 自定义策略
        if int(t) == 1:
            if not id:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else "User Strategy: Resource does not exist."},
                    }
                )
            strategy_month = Month.objects.filter(month_number=month, strategy_id=id).first()
            if not strategy_month or not strategy_month.user_Strategy_Category_id:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在或配置不完善." if lang == 'zh' else
                        "User Strategy: Resource does not exist or configuration is incomplete."},
                    }
                )
            strategy_info = UserStrategyCategoryNew.objects.get(id=strategy_month.user_Strategy_Category_id)
            if settings.FORMATEF:  # 15分钟
                # 查询策略下发过的站
                records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
                records_list = [i.get('id') for i in records]
                record_stations = StrategyApplyHistory.objects.filter(id__in=records_list, name=strategy_month.strategy.name, user=user_id).all()
                rl_list = eval(strategy_info.rl_list)
                charge_config = eval(strategy_info.charge_config)
                explain_json = eval(strategy_info.explain_json) if strategy_info.explain_json else None
                pv_list = eval(strategy_info.pv_list)
                data = {
                    'name': strategy_info.strategy.name if lang == 'zh' else strategy_info.en_name,
                    'month': month,
                    'is_follow': strategy_info.is_follow,
                    'rl_list': [],
                    'charge_config': [],
                    'explain_json': [],
                    'remark': strategy_info.remark if lang == 'zh' else strategy_info.en_remark,
                    'pv_list': [],
                    'station_list': [i.station.station_name for i in record_stations]
                }
                for i in hours:
                    # s：起始位置  l: 结束位置
                    s, l = i * 4, (i+1) * 4
                    data['rl_list'].extend(rl_list[s:l])
                    data['charge_config'].extend(charge_config[s:l])
                    data['pv_list'].extend(pv_list[s:l])
                    if explain_json:
                        data['explain_json'].extend(explain_json[s:l])


            else:  # 半个小时
                # 查询策略下发过的站
                records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
                records_list = [i.get('id') for i in records]
                record_stations = StrategyApplyHistory.objects.filter(id__in=records_list,
                                                                      name=strategy_month.strategy.name,
                                                                      user=user_id).all()

                data = {
                    'name': strategy_info.strategy.name if lang == 'zh' else strategy_info.en_name,
                    'month': month,
                    'is_follow': strategy_info.is_follow,
                    'rl_list': eval(strategy_info.rl_list)[::4],
                    'charge_config': eval(strategy_info.charge_config)[::4],
                    'explain_json': eval(strategy_info.explain_json)[::4] if strategy_info.explain_json and strategy_info.explain_json != '{}' else [],
                    'remark': strategy_info.remark if lang == 'zh' else strategy_info.en_remark,
                    'pv_list': eval(strategy_info.pv_list)[::4],
                    'station_list': [i.station.station_name for i in record_stations]
                }
        else:
            try:
                if sign:
                    master_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
                    station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
                else:
                    station = models.StationDetails.objects.get(id=station_id)
                station_id = station.id
            except Exception as e:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "fail", "detail": '查询站不存在' if lang == 'zh' else 'Query station does not exist.'},
                    }
                )
            pv_list = self._get_pv_status_new(station.province, station.type, station.level, month)
            former_actic_ids = models.StationActicNew.objects.filter(station_id=station_id).all()
            former_actic_ids = [i.former_actic_id for i in former_actic_ids]
            year_month = f"{datetime.datetime.now().year}-{month}" if int(month) >= 10 else f"{datetime.datetime.now().year}-0{month}"
            former_res = models.FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
            if not former_res:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理" if lang == 'zh' else
                        "There is no policy information under the current query time,"
                        " please contact the administrator to handle.", "detail": {}},
                    }
                )
            conn = get_redis_connection("3")
            res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station.english_name))
            if res_fllow:
                res_fllow = json.loads(eval(res_fllow))
            else:
                res_fllow = {}


            rl_list = []
            charge_config = []
            name = station.province.name + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                         common_response_code.ConfLevType.LEVEL[lang][station.level] + '默认运行策略'

            if settings.FORMATEF:
                for info in former_res:
                    rl_list.append(round(info.power_value / info.power * 100, 2))
                    charge_config.append(info.mark)
            else:
                for info in former_res[::4]:
                    rl_list.append(round(info.power_value / info.power * 100, 2))
                    charge_config.append(info.mark)

            # 查询策略下发过的站
            records = StrategyApplyHistory.objects.values('station_id').annotate(id=Max('id'))
            records_list = [i.get('id') for i in records]
            record_stations = StrategyApplyHistory.objects.filter(id__in=records_list, name=name, user=user_id).all()

            data = {
                'id': 0,  # 默认策略ID写死为0
                'name': name if lang == 'zh' else station.province.en_name + ' ' + common_response_code.ConfLevType.TYPE[lang][station.type] + \
                        common_response_code.ConfLevType.LEVEL[lang][station.level] + ' ' + 'Default Running Strategy',
                'month': month,
                'is_follow': res_fllow.get('WLoadFollowTC') if res_fllow.get('WLoadFollowTC') and res_fllow.get('WLoadFollowTC') not in EMPTY_STR_LIST else 0,
                'rl_list': [],
                'charge_config': [],
                'explain_json': [],
                'remark': '',
                'pv_list': [],
                'station_list': [i.station.station_name for i in record_stations]
            }
            if settings.FORMATEF:
                for i in hours:
                    # s：起始位置  l: 结束位置
                    s, l = i * 4, (i + 1) * 4
                    data['rl_list'].extend(rl_list[s:l])
                    data['charge_config'].extend(charge_config[s:l])
                    data['pv_list'].extend(pv_list[s:l])
            else:
                data['rl_list'] = rl_list
                data['charge_config'] = charge_config
                data['pv_list'] = pv_list
        data['formatef'] = 1 if settings.FORMATEF else 0  # 1：15分钟；0：一小时
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )


class StationListView(APIView):
    """
    用户策略关联并网点列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user["user_id"]
        id = request.query_params.get('id')
        project_id = request.query_params.get('project_id')
        province_id = request.query_params.get('province_id')
        search_key = request.query_params.get('search_key', None)

        # if not id:
        #     return Response(
        #         {
        #             "code": common_response_code.FIELD_ERROR,
        #             "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在"},
        #         }
        #     )
        # 用户所属站
        # station = models.StationDetails.objects.filter(Q(slave=-1) | Q(slave=0), project__user__id=user_id)

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station = models.StationDetails.objects.filter(is_delete=0, project__user__id=user_id, english_name=F('master_station__english_name'))

        if project_id:
            station = station.filter(project_id=project_id)

        if province_id:
            station = station.filter(province_id=province_id)

        if search_key:
            station = station.filter(Q(station_name__icontains=search_key) | Q(en_station_name__icontains=search_key))

        station = station.all()
        if id:
            # 用户下发记录
            try:
                strategy = UserStrategy.objects.get(id=id)
            except Exception as e:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动控制策略: 资源不存在." if lang == 'zh' else
                        'User Auto Control Strategy: Resource does not exist.'},
                    }
                )
            records = StrategyApplyHistory.objects.values('station_id').filter(station__in=station, name=strategy.name).annotate(id=Max('id'))
            records_list = [i.get('id') for i in records]
            # records = StrategyApplyHistory.objects.filter(name=strategy.name, user=user_id, station__in=station).all()
            # use_station_ids = [i.station.id for i in records]
            data = []
            for info in station:
                # 查询策略下发过的站
                records_issue = StrategyApplyHistory.objects.filter(id__in=records_list, station_id=info.id, name=strategy.name, user=user_id).first()
                data.append(
                    {
                        'name': info.station_name,
                        'id': info.id,
                        'status': 1 if records_issue else 0  # 判断该策略是否关联过并网点
                    }
                )
        else:
            data = []
            for info in station:
                data.append(
                    {
                        'name': info.master_station.name,
                        'id': info.id
                    }
                )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )


class StrategySendSmsCodeView(APIView):
    """策略下发发送短信"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["uid"] = uuid.uuid4()
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = MonitorSendMobileMessageSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error("策略下发发送短信:参数校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("策略下发发送短信:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("策略下发发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else
                    'Failed to send SMS verification code.'},
                }
            )
        conn = get_redis_connection("default")
        conn.set("stategy" + str(ser.validated_data["mobile"]), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功" if lang == 'zh' else
                    'Phone number:' + ser.validated_data.get('mobile') + 'SMS sent successfully.',
                },
            }
        )


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


class UserStrategyApplyView(APIView):
    """新: 自动控制策略：自定义策略下发"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["uid"] = uuid.uuid4()
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = UserStrategyApplySerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！" if lang == 'zh' else
                                 'Policy issuance: Field validation failed, please check whether the parameters are correct!',
                                 "details": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"自动控制模式下发:字段校验失败{e.args[0]}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        sign = request.data.get('sign')  # 标识小程序或者web :  1:小程序
        # 查询站信息
        station_ids = request.data.get('station_ids')
        if sign:
            master_station = models.MaterStation.objects.get(id=station_ids[0], is_delete=0)
            station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).all()
        else:
            station_instance = models.StationDetails.objects.filter(is_delete=0, id__in=station_ids).all()

        if not station_instance:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 站信息不存在！" if lang == 'zh' else
                        'Auto control policy issuance: Station information does not exist!'
                    },
                }
            )

        # 查询策略信息
        try:
            strategy_ins = UserStrategy.objects.get(id=ser.validated_data.get('strategy_id'))
        except Exception as e:
            error_log.error("自动控制策略下发: 策略信息不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 策略信息不存在！" if lang == 'zh' else
                        'Auto control policy issuance: Strategy information does not exist!'
                    },
                }
            )
        # 查询策略-分类
        category_instances = strategy_ins.userstrategycategorynew_set.all()
        if not len(category_instances):
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 策略-分类为空！" if lang == 'zh' else
                        'Auto control policy issuance: The strategy-category is empty!'
                    },
                }
            )

        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        client.loop_start()
        temp_list = list()

        # 下发时段处理
        issue_hour_list = [i for i in range(1, 24)]
        issue_hour_list.append(0)
        for category_instance in category_instances:
            category_months = category_instance.month_set.all()
            for category_month in category_months:
                # for i in range(48):
                #     temp_dict1 = dict()
                #     temp_dict2 = dict()
                #     temp_dict1['M' + str(category_month.month_number) + 'H' + str(i + 1) + 'FC'] \
                #         = str(ast.literal_eval(category_instance.charge_config)[i*2])
                #     temp_dict1['type'] = 'parameter'
                #     temp_dict2['M' + str(category_month.month_number) + 'H' + str(i + 1) + 'PC'] \
                #         = str(round(int(ast.literal_eval(category_instance.rl_list)[i*2]) / 100, 2))
                #     temp_dict2['type'] = 'parameter'
                #     temp_list.append(temp_dict1)
                #     temp_list.append(temp_dict2)
                for _i, i in enumerate(issue_hour_list):  # 下发24小时
                    hour = i * 4 if i != 0 else i
                    half_hour = i * 4 + 2 if i != 0 else i + 2
                    temp_list.append({
                        f"M{category_month.month_number}H{_i + 1}FC": str(ast.literal_eval(category_instance.charge_config)[hour]),
                        'type': 'parameter'
                    })
                    temp_list.append({
                        f"M{category_month.month_number}H{_i + 1}PC": str(round(int(ast.literal_eval(category_instance.rl_list)[hour]) / 100, 2)),
                        'type': 'parameter'
                    })
                    temp_list.append({
                        f"M{category_month.month_number}H{_i + 25}FC": str(ast.literal_eval(category_instance.charge_config)[half_hour]),
                        'type': 'parameter'
                    })
                    temp_list.append({
                        f"M{category_month.month_number}H{_i + 25}PC": str(round(int(ast.literal_eval(category_instance.rl_list)[half_hour]) / 100, 2)),
                        'type': 'parameter'
                    })
        temp_list.append(
            {
                "LoadFollowTC": str(category_instances[0].is_follow),
                "type": "parameter",
            }
        )
        for station in station_instance:
            print(station.station_name)
            station_app = station.app
            station_english_name = station.english_name

            # 准备下发策略
            topic = f"req/database/parameter/{station_english_name}/{station_app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)

            token = aes.encrypt(station_english_name)

            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": temp_list
            }

            json_message = json.dumps(message)
            try:
                client.publish(topic, json_message)
                print("下发策略内容：", json_message)
            except Exception as e:
                error_log.error('自动控制策略下发: 失败: {}'.format(e))
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": "自动控制策略下发: 下发失败！" if lang == 'zh' else
                            'Auto control policy issuance: Failed!'
                        },
                    }
                )

            success_log.info("云端计划下发成功===")
            success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
            time.sleep(0.2)  # 增加0.2秒延迟，同一时间下发网关机会覆盖下发策略

        try:
            # 写入下发历史记录
            ser.save()
        except Exception as e:
            error_log(f'自动控制策略下发: 日志写入失败 {e}')
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 日志写入失败！" if lang == 'zh' else
                        'Auto control policy issuance: Log write failed!',
                    },
                }
            )
        client.loop_stop()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略下发成功." if lang == 'zh' else 'Control policy issuance succeeded.',
                },
            }
        )


class UserStrategyDefaultApplyView(APIView):
    """默认策略下发"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["uid"] = uuid.uuid4()
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = UserStrategyApplyDefaultSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！" if lang == 'zh' else
                                 'Policy issuance: parameter verification failed, please check whether the parameter is correct!',
                                 },
                    }
                )
        except Exception as e:
            error_log.error(f"自动控制模式下发:字段校验失败{e}")
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": e.args[0],
                    },
                }
            )
        conn = get_redis_connection("default")
        conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        sign = request.data.get('sign')  # 标识小程序或者web :  1:小程序
        # 查询站信息
        station_id = request.data.get('station_id')
        station_ids = request.data.get('station_ids')
        if station_id not in station_ids:
            station_ids.append(station_id)
        if sign:
            master_station = models.MaterStation.objects.get(id=station_ids[0], is_delete=0)
            station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).all()
            station_id = station_instance[0].id
        else:
            station_instance = models.StationDetails.objects.filter(is_delete=0, id__in=station_ids).all()
        if not station_instance:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 站信息不存在！" if lang == 'zh' else
                        'Auto control policy issuance: Station information does not exist!',
                    },
                }
            )
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        client.loop_start()
        # default_temp_list = list()
        # default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
        # default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
        # former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
        # former_actic_ids = [i.former_actic_id for i in former_actic_ids]
        # for m in range(1, 13):
        #     former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=m).first()
        #     former_res = former_res.__dict__
        #     charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
        #     rl_list = dict()
        #     for key in default_month_rl_keys:
        #         rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(
        #             former_res.get(key, 0) / former_res.get('power') * 100, 2)
        #     for i in range(0, 24):
        #         temp_dict1 = dict()
        #         temp_dict2 = dict()
        #         temp_dict1['M' + str(m) + 'H' + str(i + 1) + 'FC'] \
        #             = str(charge_config[i])
        #         temp_dict1['type'] = 'parameter'
        #         temp_dict2['M' + str(m) + 'H' + str(i + 1) + 'PC'] \
        #             = str(int(rl_list['RL' + str(i + 1)]) / 100)
        #         temp_dict2['type'] = 'parameter'
        #         default_temp_list.append(temp_dict1)
        #         default_temp_list.append(temp_dict2)

        former_actic_ids = models.StationActicNew.objects.filter(station_id=station_id).all()
        former_actic_ids = [i.former_actic_id for i in former_actic_ids]
        default_temp_list = list()

        # 下发时段处理
        issue_hour_list = [i for i in range(1, 24)]
        issue_hour_list.append(0)
        for m in range(1, 13):
            year_month = f'{datetime.datetime.now().year}-{m}' if m >= 10 else f'{datetime.datetime.now().year}-0{m}'
            former_res = models.FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
            if former_res.count() != 96:
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": "自动控制策略下发: 策略配置不完整！" if lang == 'zh' else
                            'Auto control policy issuance: Strategy configuration is incomplete!',
                        },
                    }
                )
            former_res = [i for i in former_res[::2]]
            for _i, i in enumerate(issue_hour_list):  # 下发24小时
                hour = i*2 if i != 0 else i
                half_hour = i*2+1 if i != 0 else i+1
                default_temp_list.append({
                    f"M{m}H{_i+1}FC": str(former_res[hour].mark),
                    'type': 'parameter'
                })
                default_temp_list.append({
                    f"M{m}H{_i+1}PC": str(round(former_res[hour].power_value / former_res[hour].power, 2)),
                    'type': 'parameter'
                })
                default_temp_list.append({
                    f"M{m}H{_i+25}FC": str(former_res[half_hour].mark),
                    'type': 'parameter'
                })
                default_temp_list.append({
                    f"M{m}H{_i+25}PC": str(round(former_res[half_hour].power_value / former_res[half_hour].power, 2)),
                    'type': 'parameter'
                })

            # for i, former in enumerate(former_res[::2]):
            #     temp_dict1 = dict()
            #     temp_dict2 = dict()
            #     temp_dict1['M' + str(m) + 'H' + str(i + 1) + 'FC'] \
            #         = str(former.mark)
            #     temp_dict1['type'] = 'parameter'
            #     temp_dict2['M' + str(m) + 'H' + str(i + 1) + 'PC'] \
            #         = str(round(former.power_value / former.power, 2))
            #     temp_dict2['type'] = 'parameter'
            #     default_temp_list.append(temp_dict1)
            #     default_temp_list.append(temp_dict2)

        for station in station_instance:
            station_app = station.app
            station_english_name = station.english_name

            conn = get_redis_connection("3")
            res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station.english_name))
            if res_fllow:
                res_fllow = json.loads(eval(res_fllow))
            else:
                res_fllow = {}
            is_follow = res_fllow.get('WLoadFollowTC') if res_fllow.get('WLoadFollowTC') and res_fllow.get('WLoadFollowTC') not in EMPTY_STR_LIST else 0

            temp_list = []
            temp_list.extend(default_temp_list)
            temp_list.append(
                {
                    "LoadFollowTC": str(is_follow),
                    "type": "parameter",
                }
            )

            # 准备下发策略
            topic = f"req/database/parameter/{station_english_name}/{station_app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)

            token = aes.encrypt(station_english_name)

            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": temp_list
            }


            json_message = json.dumps(message)
            try:
                client.publish(topic, json_message)
                print("下发策略内容：", json_message)
            except Exception as e:
                error_log.error('自动控制策略下发: 失败: {}'.format(e))
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": "自动控制策略下发: 下发失败！" if lang == 'zh' else 'Auto control policy issuance: Issuance failed!',
                        },
                    }
                )

            success_log.info("云端计划下发成功===")
            success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
            time.sleep(0.2)  # 增加0.2秒延迟，同一时间下发网关机会覆盖下发策略
            try:
                ser.save()
            except Exception as e:
                error_log(f'自动控制策略下发: 日志写入失败 {e}')
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": "自动控制策略下发: 日志写入失败！" if lang == 'zh' else 'Auto control policy issuance: Log write failed!',
                        },
                    }
                )
        client.loop_stop()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略下发成功." if lang == 'zh' else 'Control policy issuance successful.',
                },
            }
        )


class UserStrategyRealtimeApplyView(APIView):
    """实时策略下发"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["uid"] = uuid.uuid4()
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = UserStrategyApplyRealtimeSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！" if lang == 'zh' else
                                 'Policy issuance: parameter verification failed, please check whether the parameter is correct!',
                                 },
                    }
                )
        except Exception as e:
            error_log.error(f"自动控制模式下发:字段校验失败{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": e.args[0],
                    },
                }
            )
        conn = get_redis_connection("default")
        conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码

        # 查询站信息
        station_id = request.data.get('station_id')

        try:
            master_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
            station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 站信息不存在！" if lang == 'zh' else
                        'Auto control policy issuance: Station information does not exist!',
                    },
                }
            )

        if not station:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 站信息不存在！" if lang == 'zh' else
                        'Auto control policy issuance: Station information does not exist!',
                    },
                }
            )
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        client.loop_start()

        default_temp_list = list()

        # 下发时段处理
        issue_hour_list = [i for i in range(1, 24)]
        issue_hour_list.append(0)

        m = request.data.get('month')
        rl_list = request.data.get('rl_list')
        charge_config = request.data.get('charge_config')
        for _i, i in enumerate(issue_hour_list):  # 下发24小时
            hour = i * 4 if i != 0 else i
            half_hour = i * 4 + 2 if i != 0 else i + 2
            default_temp_list.append({
                f"M{m}H{_i + 1}FC": str(charge_config[hour]),
                'type': 'parameter'
            })
            default_temp_list.append({
                f"M{m}H{_i + 1}PC": str(round(rl_list[hour] / 100, 2)),
                'type': 'parameter'
            })
            default_temp_list.append({
                f"M{m}H{_i + 25}FC": str(charge_config[half_hour]),
                'type': 'parameter'
            })
            default_temp_list.append({
                f"M{m}H{_i + 25}PC": str(round(rl_list[half_hour] / 100, 2)),
                'type': 'parameter'
            })


        station_app = station.app
        station_english_name = station.english_name

        conn = get_redis_connection("3")
        res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station.english_name))
        if res_fllow:
            res_fllow = json.loads(eval(res_fllow))
        else:
            res_fllow = {}
        is_follow = res_fllow.get('WLoadFollowTC') if res_fllow.get('WLoadFollowTC') and res_fllow.get('WLoadFollowTC') not in EMPTY_STR_LIST else 0

        temp_list = []
        temp_list.extend(default_temp_list)
        temp_list.append(
            {
                "LoadFollowTC": str(is_follow),
                "type": "parameter",
            }
        )

        # 准备下发策略
        topic = f"req/database/parameter/{station_english_name}/{station_app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        token = aes.encrypt(station_english_name)

        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": "EMS",
            "body": temp_list
        }


        json_message = json.dumps(message)
        try:
            client.publish(topic, json_message)
            print("下发策略内容：", json_message)
        except Exception as e:
            error_log.error('自动控制策略下发: 失败: {}'.format(e))
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 下发失败！" if lang == 'zh' else 'Auto control policy issuance: Issuance failed!',
                    },
                }
            )

        success_log.info("云端计划下发成功===")
        success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
        try:
            ser.save()
        except Exception as e:
            error_log(f'自动控制策略下发: 日志写入失败 {e}')
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": "自动控制策略下发: 日志写入失败！" if lang == 'zh' else 'Auto control policy issuance: Log write failed!',
                    },
                }
            )
        client.loop_stop()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略下发成功." if lang == 'zh' else 'Control policy issuance successful.',
                },
            }
        )

class UserProjectListView(APIView):
    """
    用户项目列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        project_res = models.Project.objects.filter(is_used=1, user__id=user_id).all()
        data = []
        for p in project_res:
            data.append({
                'name': p.name,
                'id': p.id
            })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": data,
            }
        )


class UserStrategyApplyHistoryView(APIView):
    """
    策略下发历史
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 10))
        project_id = request.query_params.get("project_id")  # 项目ID
        station_id = request.query_params.get('station_id')  # 并网点ID
        start_time = request.query_params.get('start_time')  # 下发开始时间
        end_time = request.query_params.get('end_time')  # 下发结束时间
        result = request.query_params.get('result')  # 下发结果
        user_id = request.user['user_id']
        if result and result != '1':
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": [],
                }
            )

        record_res = StrategyApplyHistory.objects.filter(station__master_station__project__user__id=user_id)
        if project_id:
            record_res = record_res.filter(station__project__id=project_id)
        if station_id:
            record_res = record_res.filter(station_id=station_id)
        if start_time and end_time:
            record_res = record_res.filter(create_time__gte=start_time, create_time__lte=end_time)

        record_res = record_res.order_by('-create_time').all()
        page_res = paging(page, size, record_res)  # 分页器
        data = []
        res = page_res.get('data')
        for record in res:
            front_record = StrategyApplyHistory.objects.filter(station=record.station,
                                                               id__lt=record.id).order_by('-id')[:1]
            if lang == 'zh':
                data.append({
                    'name': record.name,
                    'front_name': front_record[0].name if front_record else '就地策略',
                    'id': record.id,
                    'station_name': record.station.station_name,
                    'time': record.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'project_name': record.station.project.name,
                    'result': 1,
                    'user_name': models.UserDetails.objects.get(id=record.user).user_name
                })
            else:
                data.append({
                    'name': record.en_name,
                    'front_name': front_record[0].en_name if front_record else 'On site strategy',
                    'id': record.id,
                    'station_name': record.station.station_name,
                    'time': record.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'project_name': record.station.project.name,
                    'result': 1,
                    'user_name': models.UserDetails.objects.get(id=record.user).en_user_name
                })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        project_id = request.data.get("project_id")  # 项目ID
        station_id = request.data.get('station_id')  # 并网点ID
        start_time = request.data.get('start_time')  # 下发开始时间
        end_time = request.data.get('end_time')  # 下发结束时间
        result = request.data.get('result')  # 下发结果
        user_id = request.user['user_id']
        if result and result != '1':
            res = None
        else:
            record_res = StrategyApplyHistory.objects.filter(station__master_station__project__user__id=user_id)
            if project_id:
                record_res = record_res.filter(station__project__id=project_id)
            if station_id:
                record_res = record_res.filter(station_id=station_id)
            if start_time and end_time:
                record_res = record_res.filter(create_time__gte=start_time, create_time__lte=end_time)
            res = record_res.order_by('-create_time').all()
        if not res:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": [],
                }
            )
        wb = Workbook()
        wb.encoding = 'utf-8'  # 定义编码格式
        st = wb.active  # 获取第一个工作表（sheet1）
        if lang == 'zh':
            title = ['序号', '项目名称', '并网点名称', '修改前策略', '下发策略', '下发时间', '下发结果', '下发账户用户名']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            i = 1
            for record in res:
                max_row = st.max_row + 1  # 获取到工作表的最大行数并加1
                front_record = StrategyApplyHistory.objects.filter(station=record.station,
                                                                   id__lt=record.id).order_by('-id')[:1]
                st.cell(row=max_row, column=1).value = i
                st.cell(row=max_row, column=2).value = record.station.project.name
                st.cell(row=max_row, column=3).value = record.station.station_name
                st.cell(row=max_row, column=4).value = front_record[0].name if front_record else '就地策略'
                st.cell(row=max_row, column=5).value = record.name
                st.cell(row=max_row, column=6).value = record.create_time.strftime('%Y-%m-%d %H:%M:%S')
                st.cell(row=max_row, column=7).value = '成功'
                st.cell(row=max_row, column=8).value = models.UserDetails.objects.get(id=record.user).user_name
                i += 1
            # # 准备写入到IO中
            # output = BytesIO()
            # wb.save(output)  # 将Excel文件内容保存到IO中
            # output.seek(0)  # 重新定位到开始
            # # 设置HttpResponse的类型
            # responses = HttpResponse(output.getvalue(),
            #                          content_type='application/vnd.ms-excel')


            e_time = res[0].create_time.strftime('%Y-%m-%d')
            s_time = res[len(res) - 1].create_time.strftime('%Y-%m-%d')
            file_name = f'{s_time}~{e_time}策略历史下发日志.xlsx'

        else:
            title = ['Number', 'Project', 'Installation', 'Pre modification strategy', 'Issuance strategy',
                     'Issue time', 'Issue results', 'Issue account username']
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
            i = 1
            for record in res:
                max_row = st.max_row + 1  # 获取到工作表的最大行数并加1
                front_record = StrategyApplyHistory.objects.filter(station=record.station,
                                                                   id__lt=record.id).order_by('-id')[:1]
                st.cell(row=max_row, column=1).value = i
                st.cell(row=max_row, column=2).value = record.station.project.name
                st.cell(row=max_row, column=3).value = record.station.station_name
                st.cell(row=max_row, column=4).value = front_record[0].en_name if front_record else 'On site strategy'
                st.cell(row=max_row, column=5).value = record.en_name
                st.cell(row=max_row, column=6).value = record.create_time.strftime('%Y-%m-%d %H:%M:%S')
                st.cell(row=max_row, column=7).value = 'Success'
                st.cell(row=max_row, column=8).value = models.UserDetails.objects.get(id=record.user).en_user_name
                i += 1
            # # 准备写入到IO中
            # output = BytesIO()
            # wb.save(output)  # 将Excel文件内容保存到IO中
            # output.seek(0)  # 重新定位到开始
            # # 设置HttpResponse的类型
            # responses = HttpResponse(output.getvalue(),
            #                          content_type='application/vnd.ms-excel')

            e_time = res[0].create_time.strftime('%Y-%m-%d')
            s_time = res[len(res) - 1].create_time.strftime('%Y-%m-%d')
            file_name = f'{s_time}~{e_time} Strategy History Issuance Log.xlsx'

        # file_name = urlquote(file_name)
        # responses['Content-Disposition'] = f'attachment; filename={file_name}'

        file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
        wb.save(file_path)
        # 上传Minio
        minio_client = MinioTool()
        file_path = minio_client.upload_local_file(file_name, file_path, 'static')
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )


class UserStrategySaveToOtherView(APIView):
    """用户自动控制策略：另存为"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def _get_pv_status_new(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).order_by('moment').all()
        if station_instance:
            if settings.FORMATEF:
                for i in station_instance:
                    pv.append(i.pv)
            else:
                for i in station_instance[::4]:
                    pv.append(i.pv)
            return pv
        else:
            return pv

    def _get_pv_status(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = [0] * 24
        station_instance = models.PeakValley.objects.filter(year_month=month, province=province, type=type_,
                                                            level=level).values(
            "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
            "pv14", "pv15", "pv16", "pv17",
            "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
        if station_instance:
            for i in range(24):
                pv[i] = station_instance['pv%s' % i]
            return pv
        else:
            return pv

    def _handle_strategy_hours(self, charge_config, rl_list, pv_list, month, lang='zh'):
        """相同时间内的阈值、充放电标识、峰谷标识 数据合并"""
        compare_list = zip(charge_config, rl_list, pv_list)
        last_info = None
        _i = 0
        t = '00:00'
        data = []
        for i, v in enumerate(compare_list):
            if i != 0:
                if v == last_info:
                    _i += 1
                else:
                    end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
                    start_time = datetime.datetime.strptime(t, '%H:%M')
                    end_time = end_time(start_time).strftime('%H:%M')

                    d = {
                        'start_time': t,
                        'end_time': end_time,
                        'rl': last_info[1],
                        'charge_config': last_info[0],
                        'pv': last_info[2],
                        'explain': '',
                    }
                    data.append(d)
                    t = end_time
                    _i = 1
                    last_info = v
            else:
                _i += 1
                last_info = v

        end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
        start_time = datetime.datetime.strptime(t, '%H:%M')
        end_time = (end_time(start_time) - datetime.timedelta(minutes=1)).strftime('%H:%M')
        d = {
            'start_time': t,
            'end_time': end_time,
            'rl': last_info[1],
            'charge_config': last_info[0],
            'pv': last_info[2],
            'explain': '',
        }
        data.append(d)

        res = {
            'name': f'月份{month}' if lang == 'zh' else f'Month {month}',
            'months': [month],
            'is_follow': 1,
            'data': data

        }
        return res

    @transaction.atomic
    def post(self, request, pk):
        """
        用户策略-另存为
        """""
        lang = request.headers.get("lang", 'zh')
        new_strategy_name = request.data.get('new_name')
        type = int(request.data.get('type', 3))  # 1: 实时；2：默认，3：自定义
        # type = 1   #  web只另存当前策略
        user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
        sign = request.data.get('sign')  # 标识小程序或者web :  1:小程序
        strategy = UserStrategy.objects.filter(user=user_ins, name=new_strategy_name, is_delete=0)
        if strategy.exists():
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户控制策略另存为: 名称已存在" if lang == 'zh' else
                             "The name already exists."},
                }
            )
        try:
            save_id = transaction.savepoint()
            if type == 3:
                try:
                    strategy_instance = UserStrategy.objects.get(id=pk)
                except Exception as e:
                    error_log.error("用户控制策略另存为:策略不存在：{}".format(e))

                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "用户控制策略另存为:策略不存在" if lang == 'zh' else "The strategy does not exist."},
                        }
                    )

                old_category_instances = strategy_instance.userstrategycategorynew_set.all()
                # 创建新策略
                new_strategy = UserStrategy.objects.create(name=new_strategy_name, en_name=new_strategy_name, user=user_ins)

                # 异步翻译
                pdr_data = {'id': new_strategy.id,
                            'table': 't_user_strategy',
                            'update_data': {'name': new_strategy_name}}

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

                new_strategy.save()
                old_strategy_hours = UserStrategyHours.objects.get(strategy=strategy_instance)
                UserStrategyHours.objects.create(strategy=new_strategy, data=old_strategy_hours.data)

                for i in range(1, 13):
                    Month.objects.create(strategy=new_strategy, month_number=i)

                # 创建新分类
                for old_category_instance in old_category_instances:
                    new_category_instance = UserStrategyCategoryNew.objects.create(strategy=new_strategy,
                                                                                name=old_category_instance.name,
                                                                                en_name=old_category_instance.en_name,
                                                                                charge_config=old_category_instance.charge_config,
                                                                                is_follow=old_category_instance.is_follow,
                                                                                rl_list=old_category_instance.rl_list,
                                                                                pv_list=old_category_instance.pv_list,
                                                                                explain_json=old_category_instance.explain_json,
                                                                                remark=old_category_instance.remark
                                                                                )

                    tem_months = old_category_instance.month_set.all()
                    tem_months_numbers = [month.month_number for month in tem_months]
                    for month in tem_months_numbers:
                        month_ins = Month.objects.filter(strategy=new_strategy, month_number=month).first()
                        month_ins.is_valid = False
                        month_ins.user_Strategy_Category = new_category_instance
                        month_ins.save()

            elif type == 2:
                if sign:
                    master_station = models.MaterStation.objects.get(id=pk, is_delete=0)
                    station = master_station.stationdetails_set.filter(is_delete=0,
                        english_name=master_station.english_name).first()
                else:
                    station = models.StationDetails.objects.filter(is_delete=0, id=pk).first()

                # 创建新策略
                new_strategy = UserStrategy.objects.create(name=new_strategy_name, en_name=new_strategy_name, user=user_ins)
                new_strategy.save()

                # 异步翻译
                pdr_data = {'id': new_strategy.id,
                            'table': 't_user_strategy',
                            'update_data': {'name': new_strategy_name}}

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

                station_actic = models.StationActicNew.objects.filter(station_id=station.id).all()
                former_actic_ids = [i.former_actic_id for i in station_actic]
                category_list = []  # 小时策略表
                for i in range(1, 13):
                    year_month = f"{datetime.datetime.now().year}-{i}" if i >= 10 else f"{datetime.datetime.now().year}-0{i}"
                    former_actic = models.FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
                    rl_list = []
                    charge_config = []
                    pv_list = self._get_pv_status_new(station.province, station.type, station.level, i)

                    for info in former_actic:
                        rl_list.append(int(info.power_value / info.power * 100))
                        charge_config.append(info.mark)

                    new_category_instance = UserStrategyCategoryNew.objects.create(strategy=new_strategy,
                                                                                name='月份{}'.format(i),
                                                                                en_name=f'Month {i}',
                                                                                charge_config=json.dumps(charge_config),
                                                                                is_follow=1,
                                                                                rl_list=json.dumps(rl_list),
                                                                                pv_list=json.dumps(pv_list))
                    new_category_instance.save()
                    Month.objects.create(strategy=new_strategy, month_number=i,
                                             is_valid=False, user_Strategy_Category=new_category_instance).save()
                    _category = self._handle_strategy_hours(charge_config, rl_list, pv_list, i, lang)
                    category_list.append(_category)
                UserStrategyHours.objects.create(strategy=new_strategy, data=json.dumps(category_list)).save()

            elif type == 1:
                if sign:
                    master_station = models.MaterStation.objects.get(id=pk, is_delete=0)
                    station = master_station.stationdetails_set.filter(is_delete=0,
                        english_name=master_station.english_name).first()
                else:
                    station = models.StationDetails.objects.filter(is_delete=0, id=pk).first()

                conn = get_redis_connection("3")

                # 创建新策略
                new_strategy = UserStrategy.objects.create(name=new_strategy_name, en_name=new_strategy_name,
                                                           user=user_ins)
                new_strategy.save()

                # 异步翻译
                pdr_data = {'id': new_strategy.id,
                            'table': 't_user_strategy',
                            'update_data': {'name': new_strategy_name}}

                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

                new_strategy.save()
                category_list = []  # 小时策略表
                for i in range(1, 13):
                    datas = conn.get('{}-{}-mqtt'.format(station.english_name, i))
                    charge_config = []
                    rl_list = []
                    pv_list = self._get_pv_status_new(station.province, station.type, station.level, i)
                    if datas:
                        datas = eval(datas)
                        datas = datas.get('body')[0].get('body')
                        count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
                        for y in [24, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]:
                            _hour = y + 24 if count == 2 else y
                            rl_list.extend([
                                int(float(datas.get(f'M{i}H{y}P', 0)) * 100),
                                int(float(datas.get(f'M{i}H{y}P', 0)) * 100),
                                int(float(datas.get(f'M{i}H{_hour}P', 0)) * 100),
                                int(float(datas.get(f'M{i}H{_hour}P', 0)) * 100),
                            ])
                            charge_config.extend([
                                int(datas.get(f'M{i}H{y}F', 0)),
                                int(datas.get(f'M{i}H{y}F', 0)),
                                int(datas.get(f'M{i}H{_hour}F', 0)),
                                int(datas.get(f'M{i}H{_hour}F', 0)),
                            ])


                    new_category_instance = UserStrategyCategoryNew.objects.create(strategy=new_strategy,
                                                                                name='月份{}'.format(i),
                                                                                en_name=f'Month {i}',
                                                                                charge_config=json.dumps(charge_config),
                                                                                is_follow=1,
                                                                                rl_list=json.dumps(rl_list),
                                                                                pv_list=json.dumps(pv_list)
                                                                                )
                    new_category_instance.save()
                    Month.objects.create(strategy=new_strategy, month_number=i,
                                         is_valid=False, user_Strategy_Category=new_category_instance).save()

                    _category = self._handle_strategy_hours(charge_config, rl_list, pv_list, i, lang)
                    category_list.append(_category)
                UserStrategyHours.objects.create(strategy=new_strategy, data=json.dumps(category_list)).save()

            else:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {
                            "message": "error",
                            "detail": "控制策略类型错误" if lang == 'zh' else "Control strategy type error.",
                        },
                    }
                )
            transaction.savepoint_commit(save_id)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "用户自动控制策略: 另存为保存成功" if lang == 'zh' else "User automatic control strategy: Another save succeeded.",
                        "new_strategy_id": new_strategy.id
                    },
                }
            )
        except Exception as e:
            error_log.error("另存策略失败：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "另存失败" if lang == 'zh' else "Failed to save."},
                }
            )


class CompareStationStrategyView(APIView):
    """
    默认策略比较
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def _get_pv_status_new(self, province, type_, level, month, hour):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        moment_list = []
        hour = f'0{hour}' if int(hour) < 10 else str(hour)
        for i in range(4):
            m = str(15*i) if i != 0 else '00'
            moment_list.append(f'{hour}:{m}')
        station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level, moment__in=moment_list).order_by('moment').all()

        if station_instance:
            if settings.FORMATEF:
                for i in station_instance:
                    pv.append(i.pv)
            else:
                for i in station_instance[::4]:
                    pv.append(i.pv)
            return pv
        else:
            return pv

    def _get_data(self, station_id, station, month, hours):
        """
        获取当前站的指定月份的自动策略控制信息
        :return:
        """
        client = mtqq_station_strategy(station_id, month)
        conn = get_redis_connection("3")
        # if month:
        i = 0
        n = 2 if int(month) < 10 else 3
        charge_config = {}
        rl_dict = {}
        while i < 5:
            time.sleep(0.1)
            datas = conn.get('{}-{}-mqtt'.format(station, month))
            if datas:
                datas = eval(datas)
                datas = datas.get('body')[0].get('body')
                if not datas:
                    break
                key = list(datas.keys())[0]
                if key[:n] == f'M{month}':
                    if settings.FORMATEF:  # 策略时间颗粒度 15分钟或者一小时
                        count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
                        for hour in hours:
                            current_hour = hour
                            hour = 24 if hour == 0 else hour
                            if count == 2:
                                _hour = 48 if hour == 0 else hour + 24
                            else:
                                _hour = 24 if hour == 0 else hour
                            charge_config[current_hour] = [
                                float(datas.get(f'M{month}H{hour}F', 0)),
                                float(datas.get(f'M{month}H{hour}F', 0)),
                                float(datas.get(f'M{month}H{_hour}F', 0)),
                                float(datas.get(f'M{month}H{_hour}F', 0)),
                            ]
                            rl_dict[current_hour] = [
                                float(datas.get(f'M{month}H{hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{_hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{_hour}P', 0)) * 100
                            ]
                    else:
                        for hour in hours:
                            charge_config_lsit = []
                            rl_list = []
                            charge_config_lsit.append(float(datas.get(f'M{month}H{hour+1}F', 0)))
                            rl_list.append(float(datas.get(f'M{month}H{hour+1}P', 0)) * 100)
                            charge_config[hour] = charge_config_lsit
                            rl_dict[hour] = rl_list
                    i = 10
            else:
                i += 1
        # else:
        #     data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
        #     if data:
        #         data = json.loads(eval(data))
        #     else:
        #         data = {}
        client.disconnect()
        return charge_config, rl_dict

    def default_tactics(self, station_instance,month, hours, former_actic_ids, lang='zh'):

        # 实时策略
        current_charge_config, current_rl_list = self._get_data(station_instance.id,
                                                                station_instance.english_name, month, hours)
        # 默认策略
        year_month = f'{datetime.datetime.now().year}-{month}' if month >= 10 else f'{datetime.datetime.now().year}-0{month}'
        former_res = models.FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
        if not former_res:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理" if lang == 'zh' else
                    'No strategy information is available for the current query time.'
                    ' Please contact the administrator to handle it', "detail": {}},
                }
            )
        default_rl_list = [round(i.power_value / i.power * 100, 2) for i in former_res]
        default_charge_config = [i.mark for i in former_res]
        day_res = []

        n_month = f'{datetime.datetime.now().year}-{month}' if int(
            month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        price_dict = {}
        if settings.FORMATEF:
            station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=station_instance.province, type=station_instance.type,
                                                               level=station_instance.level).order_by('moment').all()

        else:
            station_instance = models.PeakValleyNew.objects.filter(year_month=n_month,
                                                                   province=station_instance.province,
                                                                   type=station_instance.type,
                                                                   level=station_instance.level).order_by('moment').all()[::4]
        for price in station_instance:
            moment = int(price.moment[:2])
            if price_dict.get(moment):
                price_dict[moment].append(price.pv)
            else:
                price_dict[moment] = [price.pv]
        for i in hours:
            if settings.FORMATEF:
                first, last = i * 4, i * 4 + 4
            else:
                first, last = i * 4, i * 4 + 1
            data = {
                'hours': i,
                'rl': default_rl_list[first: last],
                'charge': default_charge_config[first: last],
                'pv': price_dict.get(i),
                'current_rl': current_rl_list[i] if current_rl_list else [],
                'current_charge': current_charge_config[i] if current_charge_config else [],
                'current_pv': price_dict.get(i),
            }
            data['status'] = data['charge'] == data['current_charge']
            day_res.append(data)
        return day_res, month

    def customize_tactics(self, station_info, detail, month, hours, lang='zh'):
        # 实时策略
        current_charge_config, current_rl_list = self._get_data(station_info.id, station_info.english_name,
                                                                month, hours)
        # 做处理为了对应1-24点的计时
        # pv_list.append(pv_list[0])
        # pv_list.pop(0)
        # 自定义策略
        month_detail = detail.month_set.filter(month_number=month).first()
        if not month_detail or not month_detail.user_Strategy_Category:
            error_log.error(f"用户自动控策略:策略配置不完整")
            raise Exception(f"用户自动控策略:策略配置不完整" if lang == 'zh' else 'User automatic control strategy: incomplete policy configuration.')
        strategy_category = UserStrategyCategoryNew.objects.get(id=month_detail.user_Strategy_Category.id)
        customize_change_config = eval(strategy_category.charge_config)
        customize_rl_list = eval(strategy_category.rl_list)
        customize_pv_list = eval(strategy_category.pv_list)

        day_res = []


        n_month = f'{datetime.datetime.now().year}-{month}' if int(
            month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        price_dict = {}
        if settings.FORMATEF:
            station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=station_info.province, type=station_info.type,
                                                               level=station_info.level).order_by('moment').all()

        else:
            station_instance = models.PeakValleyNew.objects.filter(year_month=n_month,
                                                                   province=station_info.province,
                                                                   type=station_info.type,
                                                                   level=station_info.level).order_by('moment').all()[::4]
        for price in station_instance:
            moment = int(price.moment[:2])
            if price_dict.get(moment):
                price_dict[moment].append(price.pv)
            else:
                price_dict[moment] = [price.pv]

        for i in hours:
            if settings.FORMATEF:
                first, last = i * 4, i * 4 + 4
            else:
                first, last = i * 4, i * 4 + 1
            data = {
                'hours': i,
                'rl': customize_rl_list[first: last],
                'charge': customize_change_config[first: last],
                'pv': customize_pv_list[first: last],
                'current_rl': current_rl_list[i] if current_rl_list else [],
                'current_charge': current_charge_config[i] if current_charge_config else [],
                'current_pv': price_dict.get(i),

            }
            data['status'] = [data['charge'], data['pv']] == [data['current_charge'], data['current_pv']]
            day_res.append(data)
        return day_res, month

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = UserStrategyCompareSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"策略对比:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"策略对比失败:{traceback.print_exc()}")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": e.args[0]}
                }
            )

        try:
            station_id = request.data['station_id']
            if request.data.get('sign'):
                master_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
                station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
            else:
                station_instance = models.StationDetails.objects.get(id=station_id)
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '查询并网点资源不存在！' if lang == 'zh' else
                             'The station resource does not exist!'},
                }
            )

        station_id = station_instance.id
        former_actic_ids = models.StationActicNew.objects.filter(station_id=station_id).all()
        former_actic_ids = [i.former_actic_id for i in former_actic_ids]
        res = []
        conn = get_redis_connection("3")
        res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station_instance.english_name))
        if res_fllow:
            res_fllow = json.loads(eval(res_fllow))
        else:
            res_fllow = {}
        current_is_follow = res_fllow.get('WLoadFollowTC') if res_fllow.get('WLoadFollowTC') and res_fllow.get('WLoadFollowTC') not in EMPTY_STR_LIST else 0
        m = datetime.datetime.now().month
        hours_str = request.data.get('hours').replace('24', '0')
        hours = hours_str.split(',') if request.data.get('hours') != 'all' else [i for i in range(0, 24)]
        hours = [int(h) for h in hours]
        hours.sort()
        # 创建线程池
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=12)
        # 自定义策略对比
        if int(request.data.get('t')) == 1:
            is_follow = Month.objects.filter(strategy_id=request.data['strategy_id'], month_number=m).first()
            if not is_follow:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": '用户自动控制策略: 资源不存在或配置不完善.' if lang == 'zh'
                        else 'User automatic control strategy: resource does not exist or configuration is incomplete.'},
                    }
                )
            is_follow = is_follow.user_Strategy_Category.is_follow if is_follow.user_Strategy_Category else '--'
            station_info = models.StationDetails.objects.get(id=request.data.get('station_id'))
            detail = UserStrategy.objects.get(id=request.data['strategy_id'])
            # is_follow = detail.userstrategycategory_set.first().is_follow
            task_list = []
            try:
                for i in range(1, 13):
                    task_list.append(executor.submit(self.customize_tactics, station_info, detail, i, hours, lang))
                concurrent.futures.wait(task_list)
                for task_res in task_list:
                    task_res = task_res.result()
                    res.append(
                        {
                            'month': task_res[1],
                            'data': task_res[0],
                            'name': detail.name if lang == 'zh' else detail.en_name
                        }
                    )
            except Exception as e:
                error_log.error(f"策略对比:自定义策略对比失败{traceback.print_exc()}")
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "error", "detail": e.args[0]}
                    }
                )

        else:
            is_follow = current_is_follow

            if lang == 'zh':
                name = station_instance.province.name + common_response_code.ConfLevType.TYPE[lang][station_instance.type] + \
                                common_response_code.ConfLevType.LEVEL[lang][station_instance.level] + '默认运行策略',
            else:
                name = station_instance.province.en_name + ' ' + common_response_code.ConfLevType.TYPE[lang][station_instance.type] + ' ' + \
                                common_response_code.ConfLevType.LEVEL[lang][station_instance.level] + ' default running strategy'

            task_list = []
            for i in range(1, 13):
                task_list.append(executor.submit(self.default_tactics, station_instance, i, hours, former_actic_ids, lang))
            concurrent.futures.wait(task_list)
            for task_res in task_list:
                if isinstance(task_res.result(), tuple):
                    task_res = task_res.result()
                    res.append(
                        {
                            'month': task_res[1],
                            'data': task_res[0],
                            'name': name
                        }
                    )
        # 关闭线程池
        executor.shutdown()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": {
                    'current_is_folllow': current_is_follow,
                    'is_follow': is_follow,
                    'data': res
                }},
            }
        )


class UserStrategyTemplateView(APIView):
    """
    导入策略模板
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        minioClient = MinioTool()
        url = minioClient.get_download_url('tianlu', '策略模板.xlsx')
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": url},
            }
        )


class UserStrategyImportView(APIView):
    """
    解析策略模板
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        files = request.FILES.get('files')
        if files:
            try:
                wb = load_workbook(files)
                data = []
                for i in wb:
                    res = []
                    st = wb[i.title]
                    for _i in range(7, st.max_row + 1):
                        res.append({
                            'start_time': st.cell(_i, 1).value,
                            'end_time': st.cell(_i, 2).value,
                            'pv': st.cell(_i, 3).value,
                            'charge_config': st.cell(_i, 4).value,
                            'rl': st.cell(_i, 1).value,
                        })
                    data.append(
                        {
                            'name': i.title,
                            'data': res,
                            'month_list': st['B5'].value.split(','),
                        }
                    )
            except Exception as e:
                error_log.error(f'模板错误信息：{e}')
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {"message": "error", "detail": '模板错误，请检查！' if lang == 'zh' else 'Template error, please check!'}
                    }
                )
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data}
                }
            )

        else:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "success", "detail": '未上传附件' if lang == 'zh' else 'No attachment uploaded.'}
                }
            )


