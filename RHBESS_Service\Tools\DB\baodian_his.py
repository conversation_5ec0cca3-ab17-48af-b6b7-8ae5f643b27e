#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\baodian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


BAODIAN1_HOSTNAME = model_config.get('mysql', "BAODIAN1_HOSTNAME")
BAODIAN1_PORT = model_config.get('mysql', "BAODIAN1_PORT")
BAODIAN1_DATABASE = model_config.get('mysql', "BAODIAN1_DATABASE")
BAODIAN1_USERNAME = model_config.get('mysql', "BAODIAN1_USERNAME")
BAODIAN1_PASSWORD = model_config.get('mysql', "BAODIAN1_PASSWORD")

BAODIAN2_HOSTNAME = model_config.get('mysql', "BAODIAN2_HOSTNAME")
BAODIAN2_PORT = model_config.get('mysql', "BAODIAN2_PORT")
BAODIAN2_DATABASE = model_config.get('mysql', "BAODIAN2_DATABASE")
BAODIAN2_USERNAME = model_config.get('mysql', "BAODIAN2_USERNAME")
BAODIAN2_PASSWORD = model_config.get('mysql', "BAODIAN2_PASSWORD")

BAODIAN3_HOSTNAME = model_config.get('mysql', "BAODIAN3_HOSTNAME")
BAODIAN3_PORT = model_config.get('mysql', "BAODIAN3_PORT")
BAODIAN3_DATABASE = model_config.get('mysql', "BAODIAN3_DATABASE")
BAODIAN3_USERNAME = model_config.get('mysql', "BAODIAN3_USERNAME")
BAODIAN3_PASSWORD = model_config.get('mysql', "BAODIAN3_PASSWORD")

BAODIAN4_HOSTNAME = model_config.get('mysql', "BAODIAN4_HOSTNAME")
BAODIAN4_PORT = model_config.get('mysql', "BAODIAN4_PORT")
BAODIAN4_DATABASE = model_config.get('mysql', "BAODIAN4_DATABASE")
BAODIAN4_USERNAME = model_config.get('mysql', "BAODIAN4_USERNAME")
BAODIAN4_PASSWORD = model_config.get('mysql', "BAODIAN4_PASSWORD")

BAODIAN5_HOSTNAME = model_config.get('mysql', "BAODIAN5_HOSTNAME")
BAODIAN5_PORT = model_config.get('mysql', "BAODIAN5_PORT")
BAODIAN5_DATABASE = model_config.get('mysql', "BAODIAN5_DATABASE")
BAODIAN5_USERNAME = model_config.get('mysql', "BAODIAN5_USERNAME")
BAODIAN5_PASSWORD = model_config.get('mysql', "BAODIAN5_PASSWORD")


hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BAODIAN1_USERNAME,
    BAODIAN1_PASSWORD,
    BAODIAN1_HOSTNAME,
    BAODIAN1_PORT,
    BAODIAN1_DATABASE
)
baodian1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_baodian1_session = scoped_session(sessionmaker(baodian1_engine,autoflush=True))
baodian1_Base = declarative_base(baodian1_engine)
baodian1_session = _baodian1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BAODIAN2_USERNAME,
    BAODIAN2_PASSWORD,
    BAODIAN2_HOSTNAME,
    BAODIAN2_PORT,
    BAODIAN2_DATABASE
)
baodian2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_baodian2_session = scoped_session(sessionmaker(baodian2_engine,autoflush=True))
baodian2_Base = declarative_base(baodian2_engine)
baodian2_session = _baodian2_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BAODIAN3_USERNAME,
    BAODIAN3_PASSWORD,
    BAODIAN3_HOSTNAME,
    BAODIAN3_PORT,
    BAODIAN3_DATABASE
)
baodian3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_baodian3_session = scoped_session(sessionmaker(baodian3_engine,autoflush=True))
baodian3_Base = declarative_base(baodian3_engine)
baodian3_session = _baodian3_session()

hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BAODIAN4_USERNAME,
    BAODIAN4_PASSWORD,
    BAODIAN4_HOSTNAME,
    BAODIAN4_PORT,
    BAODIAN4_DATABASE
)
baodian4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_BAODIAN4_session = scoped_session(sessionmaker(baodian4_engine,autoflush=True))
baodian4_Base = declarative_base(baodian4_engine)
baodian4_session = _BAODIAN4_session()


hisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BAODIAN5_USERNAME,
    BAODIAN5_PASSWORD,
    BAODIAN5_HOSTNAME,
    BAODIAN5_PORT,
    BAODIAN5_DATABASE
)
baodian5_engine = create_engine(hisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_BAODIAN5_session = scoped_session(sessionmaker(baodian5_engine,autoflush=True))
baodian5_Base = declarative_base(baodian5_engine)
baodian5_session = _BAODIAN5_session()

SBAODIAN1_HOSTNAME = model_config.get('mysql', "SBAODIAN1_HOSTNAME")
SBAODIAN1_PORT = model_config.get('mysql', "SBAODIAN1_PORT")
SBAODIAN1_DATABASE = model_config.get('mysql', "SBAODIAN1_DATABASE")
SBAODIAN1_USERNAME = model_config.get('mysql', "SBAODIAN1_USERNAME")
SBAODIAN1_PASSWORD = model_config.get('mysql', "SBAODIAN1_PASSWORD")

SBAODIAN2_HOSTNAME = model_config.get('mysql', "SBAODIAN2_HOSTNAME")
SBAODIAN2_PORT = model_config.get('mysql', "SBAODIAN2_PORT")
SBAODIAN2_DATABASE = model_config.get('mysql', "SBAODIAN2_DATABASE")
SBAODIAN2_USERNAME = model_config.get('mysql', "SBAODIAN2_USERNAME")
SBAODIAN2_PASSWORD = model_config.get('mysql', "SBAODIAN2_PASSWORD")

SBAODIAN3_HOSTNAME = model_config.get('mysql', "SBAODIAN3_HOSTNAME")
SBAODIAN3_PORT = model_config.get('mysql', "SBAODIAN3_PORT")
SBAODIAN3_DATABASE = model_config.get('mysql', "SBAODIAN3_DATABASE")
SBAODIAN3_USERNAME = model_config.get('mysql', "SBAODIAN3_USERNAME")
SBAODIAN3_PASSWORD = model_config.get('mysql', "SBAODIAN3_PASSWORD")

SBAODIAN4_HOSTNAME = model_config.get('mysql', "SBAODIAN4_HOSTNAME")
SBAODIAN4_PORT = model_config.get('mysql', "SBAODIAN4_PORT")
SBAODIAN4_DATABASE = model_config.get('mysql', "SBAODIAN4_DATABASE")
SBAODIAN4_USERNAME = model_config.get('mysql', "SBAODIAN4_USERNAME")
SBAODIAN4_PASSWORD = model_config.get('mysql', "SBAODIAN4_PASSWORD")

SBAODIAN5_HOSTNAME = model_config.get('mysql', "SBAODIAN5_HOSTNAME")
SBAODIAN5_PORT = model_config.get('mysql', "SBAODIAN5_PORT")
SBAODIAN5_DATABASE = model_config.get('mysql', "SBAODIAN5_DATABASE")
SBAODIAN5_USERNAME = model_config.get('mysql', "SBAODIAN5_USERNAME")
SBAODIAN5_PASSWORD = model_config.get('mysql', "SBAODIAN5_PASSWORD")


shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBAODIAN1_USERNAME,
    SBAODIAN1_PASSWORD,
    SBAODIAN1_HOSTNAME,
    SBAODIAN1_PORT,
    SBAODIAN1_DATABASE
)
sbaodian1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sbaodian1_session = scoped_session(sessionmaker(sbaodian1_engine,autoflush=True))
sbaodian1_Base = declarative_base(sbaodian1_engine)
sbaodian1_session = _sbaodian1_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBAODIAN2_USERNAME,
    SBAODIAN2_PASSWORD,
    SBAODIAN2_HOSTNAME,
    SBAODIAN2_PORT,
    SBAODIAN2_DATABASE
)
sbaodian2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sbaodian2_session = scoped_session(sessionmaker(sbaodian2_engine,autoflush=True))
sbaodian2_Base = declarative_base(sbaodian2_engine)
sbaodian2_session = _sbaodian2_session()

shisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBAODIAN3_USERNAME,
    SBAODIAN3_PASSWORD,
    SBAODIAN3_HOSTNAME,
    SBAODIAN3_PORT,
    SBAODIAN3_DATABASE
)
sbaodian3_engine = create_engine(shisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sbaodian3_session = scoped_session(sessionmaker(sbaodian3_engine,autoflush=True))
sbaodian3_Base = declarative_base(sbaodian3_engine)
sbaodian3_session = _sbaodian3_session()

shisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBAODIAN4_USERNAME,
    SBAODIAN4_PASSWORD,
    SBAODIAN4_HOSTNAME,
    SBAODIAN4_PORT,
    SBAODIAN4_DATABASE
)
sbaodian4_engine = create_engine(shisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sbaodian3_session = scoped_session(sessionmaker(sbaodian4_engine,autoflush=True))
_sBAODIAN4_session = sessionmaker(sbaodian4_engine,autoflush=True)
sbaodian4_Base = declarative_base(sbaodian4_engine)
sbaodian4_session = _sBAODIAN4_session()


shisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBAODIAN5_USERNAME,
    SBAODIAN5_PASSWORD,
    SBAODIAN5_HOSTNAME,
    SBAODIAN5_PORT,
    SBAODIAN5_DATABASE
)
sbaodian5_engine = create_engine(shisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sBAODIAN5_session = scoped_session(sessionmaker(sbaodian5_engine,autoflush=True))
sbaodian5_Base = declarative_base(sbaodian5_engine)
sbaodian5_session = _sBAODIAN5_session()
