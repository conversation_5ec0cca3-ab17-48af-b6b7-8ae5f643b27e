package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.TUserStrategyCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户策略分类Mapper
 * 对应Python中的TUserStrategyCategory相关数据库操作
 */
@Mapper
public interface TUserStrategyCategoryMapper extends BaseMapper<TUserStrategyCategory> {

    /**
     * 根据策略ID查询分类列表
     */
    @Select("SELECT * FROM t_user_strategy_category " +
            "WHERE strategy_id = #{strategyId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TUserStrategyCategory> selectByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略ID和分类名称查询分类
     */
    @Select("SELECT * FROM t_user_strategy_category " +
            "WHERE strategy_id = #{strategyId} AND name = #{name} AND is_use = 1")
    TUserStrategyCategory selectByStrategyIdAndName(@Param("strategyId") Long strategyId, @Param("name") String name);

    /**
     * 根据分类名称模糊查询
     */
    @Select("SELECT * FROM t_user_strategy_category " +
            "WHERE name LIKE CONCAT('%', #{name}, '%') AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TUserStrategyCategory> selectByNameLike(@Param("name") String name);

    /**
     * 统计策略分类数量
     */
    @Select("SELECT COUNT(*) FROM t_user_strategy_category " +
            "WHERE strategy_id = #{strategyId} AND is_use = 1")
    int countByStrategyId(@Param("strategyId") Long strategyId);
}
