from sqlalchemy import create_engine
from Application.Cfg.dir_cfg import model_config
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base

SIKLY_USERNAMES = model_config.get('mysql', "SIKLY_USERNAMES")
SIKLY_PASSWORDS = model_config.get('mysql', "SIKLY_PASSWORDS")
SIKLY_HOSTNAMES = model_config.get('mysql', "SIKLY_HOSTNAMES")
SIKLY_PORTS = model_config.get('mysql', "SIKLY_PORTS")
SIKLY_DATABASE = model_config.get('mysql', "SIKLY_DATABASE")
SIKLY_DATABASEG = model_config.get('mysql', "SIKLY_DATABASEG")


SIKLY_HISTORY_USERNAMES = model_config.get('mysql', "SIKLY_HISTORY_USERNAMES")
SIKLY_HISTORY_PASSWORDS = model_config.get('mysql', "SIKLY_HISTORY_PASSWORDS")
SIKLY_HISTORY_HOSTNAMES = model_config.get('mysql', "SIKLY_HISTORY_HOSTNAMES")
SIKLY_HISTORY_PORTS = model_config.get('mysql', "SIKLY_HISTORY_PORTS")
SIKLY_HISTORY_DATABASE = model_config.get('mysql', "SIKLY_HISTORY_DATABASE")
SIKLY_HISTORY_DATABASE1 = model_config.get('mysql', "SIKLY_HISTORY_DATABASE1")


sikly_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SIKLY_USERNAMES,
    SIKLY_PASSWORDS,
    SIKLY_HOSTNAMES,
    SIKLY_PORTS,
    SIKLY_DATABASE
)
sikly_engine = create_engine(sikly_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sikly_session = scoped_session(sessionmaker(sikly_engine,autoflush=True))
sikly_Base = declarative_base(sikly_engine)
sikly_session = _sikly_session()


sikly_mysql_url_g='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SIKLY_USERNAMES,
    SIKLY_PASSWORDS,
    SIKLY_HOSTNAMES,
    SIKLY_PORTS,
    SIKLY_DATABASEG
)
sikly_engine_g = create_engine(sikly_mysql_url_g,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sikly_session_g = scoped_session(sessionmaker(sikly_engine_g,autoflush=True))
sikly_Base_g = declarative_base(sikly_engine_g)
sikly_session_g = _sikly_session_g()


sikly_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SIKLY_HISTORY_USERNAMES,
    SIKLY_HISTORY_PASSWORDS,
    SIKLY_HISTORY_HOSTNAMES,
    SIKLY_HISTORY_PORTS,
    SIKLY_HISTORY_DATABASE
)
sikly_history_engine = create_engine(sikly_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sikly_history_session = scoped_session(sessionmaker(sikly_history_engine,autoflush=True))
sikly_history_Base = declarative_base(sikly_history_engine)
sikly_history_session = _sikly_history_session()


sikly1_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SIKLY_HISTORY_USERNAMES,
    SIKLY_HISTORY_PASSWORDS,
    SIKLY_HISTORY_HOSTNAMES,
    SIKLY_HISTORY_PORTS,
    SIKLY_HISTORY_DATABASE1
)
sikly1_history_engine = create_engine(sikly1_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sikly1_history_session = scoped_session(sessionmaker(sikly1_history_engine,autoflush=True))
sikly1_history_Base = declarative_base(sikly1_history_engine)
sikly1_history_session = _sikly1_history_session()