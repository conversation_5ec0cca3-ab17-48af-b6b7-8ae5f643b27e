#!/usr/bin/env python
# coding=utf-8
#@Information:运行天数
#<AUTHOR> WYJ
#@Date         : 2023-08-30 10:18:16
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_dict_runday_one copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-30 10:18:18
from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseDictRundayNum(user_Base):
    '运行天数'
    __tablename__ = "t_side_forecase_dict_runday_num"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"所属行业")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=True,comment=u"排序索引")

    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        return "{'id':%s,'name':'%s'}" % (self.id,self.name)
        
    