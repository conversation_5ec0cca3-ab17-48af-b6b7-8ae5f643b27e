package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 功率计划创建DTO
 */
@Data
@ApiModel("功率计划创建DTO")
public class PowerPlanCreateDTO {

    @ApiModelProperty(value = "计划类型: 1-自定义, 2-周期性, 3-节假日", required = true)
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    @ApiModelProperty(value = "功率列表(JSON字符串)", required = true)
    @NotBlank(message = "功率列表不能为空")
    private String powerList;

    @ApiModelProperty(value = "电站列表(JSON字符串)", required = true)
    @NotBlank(message = "电站列表不能为空")
    private String stationList;

    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "账号不能为空")
    private String account;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "计划名称", required = true)
    @NotBlank(message = "计划名称不能为空")
    private String planName;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "语言")
    private String lang;

    @ApiModelProperty(value = "联系人电话")
    private String mobile;
}
