#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-03-06 16:16:29
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\Models\session_ele.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-03-06 16:16:48


#-*- coding: UTF-8 -*-
u'''
会话管理
'''
import pickle

from tornado.log import app_log
import uuid
from Tools.Utils.time_utils import timeUtils
from Tools.DB.redis_decision import r
from Application.EqAccount.encryption.jwt_encryption import parse_payload
import copy
# 会话列表

# _Sessions = r.hgetall("Session")



class Session:
    u'会话'
    def __init__(self, sid, remote_ip, now=None):
        # app_log.debug(u'创建新的会话')
        self.sid = sid
        self.user = None
        self.remote_ip = remote_ip
        self.d = {}
    # 更新key 过期时间默认24小时
    def update(self, overdue=86400):
        if r.exists(self.sid):
            r.expire(self.sid, overdue)

    def ifTimeout(self,):
       if r.exists(self.sid):
           return True
       return False
        

    def login(self,user):
        self.user = user

    def logout(self):
        self.user = None

    def remove(self):
        r.delete(self.sid)
        # del _Sessions[self.sid]

    def set(self,item,value):
        self.d[item] = value
    
    def get(self,item):
        if item in self.d:
            return self.d[item]
        return None

def newSession(Authorization):
    u'分配新会话'

    # r.hlen("hash1")
    # if len(_Sessions)>=1024:
    # if r.hlen("Session") >= 1024:
    #     app_log.warn(u'会话数达到1024')
    #     return None
    s = Session(Authorization, Authorization)
    r.set(Authorization, pickle.dumps(s), ex=7200)
    return s

def getSession(sid):
    u'获取会话'
    # if sid in _Sessions:
    if r.exists(sid):
        s = pickle.loads(r.get(sid))
        # 2.token校验
        status, info_or_error = parse_payload(sid)
        # 3.校验失败，继续往后走
        if not status:
            return
        # if s.ifTimeout
        # if s.ifTimeout():
        #     app_log.debug(u'查询的会话%s超时' % sid)
        #     s.remove()
        #     return None
        return s
    else:
        app_log.debug( u'查询的会话%s不存在' % sid )
        return None

def updateSession(sid,session):
    """
    更新redis里面的对象
    """
    r.set(sid, pickle.dumps(session), ex=7200)


def removeSession(sid):
    u'删除指定会话'
    # del _Sessions[sid]
    r.delete(sid)


# def removeInvaliables(secs=timeUtils.SecsDay):
#     u'清除无效会话'
#     now = timeUtils.nowSecs()
#     app_log.debug( u'检测会话是否有效%d' % now )
#     for k in r.hgetall("Session"):
#         # s = _Sessions[k]
#         s = pickle.loads(r.hget('Session', k))
#         if s.ifTimeout(now,secs):
#             app_log.debug( u"会话%s无效，清除" % k )
#             # del _Sessions[s.sid]
#             r.hdel("Session", s.sid)
