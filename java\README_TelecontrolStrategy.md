# 远程控制策略Java实现文档

## 概述

本项目将Python中的`telecontrol_strategy.py`文件完整转换为Java实现，严格按照原Python逻辑实现所有功能，包括数据库操作、业务逻辑和API接口。

## 🎯 **转换完成的方法列表**

| Python方法 | Java实现 | 功能描述 | 状态 |
|-----------|---------|---------|------|
| StrategyTemplate | downloadStrategyTemplate | 策略模板下载 | ✅ |
| StrategyImport | importStrategy | 策略模板解析导入 | ✅ |
| PowerPlanStations | getPowerPlanStations | 获取电站容量信息 | ✅ |
| PowerPlanStationsRefresh | refreshPowerPlanStations | 刷新电站容量信息 | ✅ |
| PowerPlanList | getPowerPlanList | 功率计划下发列表 | ✅ |
| PowerPlanAdd | addPowerPlan | 新增计划功率 | ✅ |
| PowerPlanDetail | getPowerPlanDetail | 功率计划详情 | ✅ |
| PowerPlanUpdate | updatePowerPlan | 修改计划功率 | ✅ |
| powerPlanStop | stopPowerPlan | 停止计划功率 | ✅ |
| powerPlanDelete | deletePowerPlan | 删除功率计划 | ✅ |
| GetPlanHis | getPlanHistory | 查询下发记录列表 | ✅ |
| planHisExport | exportPlanHistory | 导出下发记录 | ✅ |
| GetIssuanceType | getIssuanceType | 查询下发类型 | ✅ |
| ProjectPackAdd | addProjectPack | 另存项目包 | ✅ |
| ProjectPackList | getProjectPackList | 加载项目包列表 | ✅ |

## 📁 **项目结构**

```
java/
├── entity/                     # 实体类
│   ├── TPowerDeliverRecords.java      # 功率计划下发记录
│   ├── TPlanPowerRecords.java         # 功率计划关联记录
│   ├── TPlanHistory.java              # 计划历史记录
│   ├── TPanLogs.java                  # 下发日志记录
│   ├── ProjectPack.java               # 项目包
│   ├── TUserStrategy.java             # 用户策略
│   ├── TUserStrategyCategory.java     # 用户策略分类
│   ├── Station.java                   # 电站信息
│   └── StationR.java                  # 电站关系
├── mapper/                     # 数据访问层
│   ├── TPowerDeliverRecordsMapper.java
│   ├── TPlanHistoryMapper.java
│   ├── TPanLogsMapper.java
│   ├── ProjectPackMapper.java
│   ├── StationMapper.java
│   ├── TUserStrategyMapper.java
│   ├── TUserStrategyCategoryMapper.java
│   └── TPlanPowerRecordsMapper.java
├── service/                    # 服务层
│   ├── TelecontrolStrategyService.java      # 服务接口
│   └── impl/
│       └── TelecontrolStrategyServiceImpl.java  # 服务实现
├── controller/                 # 控制器层
│   └── TelecontrolStrategyController.java
├── dto/                        # 数据传输对象
│   ├── PowerPlanRequest.java           # 功率计划请求DTO
│   ├── StrategyRequest.java            # 策略请求DTO
│   └── TelecontrolResponse.java        # 响应DTO
├── constant/                   # 常量类
│   └── TelecontrolConstants.java
├── config/                     # 配置类
│   └── TelecontrolConfig.java
├── exception/                  # 异常处理
│   ├── TelecontrolException.java
│   └── GlobalExceptionHandler.java
├── util/                       # 工具类
│   ├── ExcelUtil.java
│   └── FileUtil.java
└── test/                       # 测试类
    └── TelecontrolStrategyServiceTest.java
```

## 🗄️ **数据库模型映射**

### Python模型 → Java实体类

| Python模型 | Java实体类 | 表名 | 主要字段 |
|------------|-----------|------|---------|
| TPowerDeliverRecords | TPowerDeliverRecords | t_power_deliver_records | id, name, power_list, station_list, user_id, plan_type |
| TPlanPowerRecords | TPlanPowerRecords | t_plan_power_records | id, plan_id, power_id, serial_number |
| TPlanHistory | TPlanHistory | t_plan_history | id, name, status, power, plan_type, start_time, end_time |
| TPanLogs | TPanLogs | t_pan_logs | id, station, type_name, content, status, user_name |
| ProjectPack | ProjectPack | t_project_pack | id, user_id, name, data |
| TUserStrategy | TUserStrategy | t_user_strategy | id, name, user_id, is_delete |
| TUserStrategyCategory | TUserStrategyCategory | t_user_strategy_category | id, name, strategy_id, charge_config |
| Station | Station | t_station | id, name, descr, register |
| StationR | StationR | t_station_relation | id, station_name, electric_power, province |

## 🔧 **核心功能实现**

### 1. 策略模板管理

**下载策略模板 (StrategyTemplate)**
```java
@GetMapping("/strategy-template")
public void downloadStrategyTemplate(HttpServletResponse response)
```

**导入策略模板 (StrategyImport)**
```java
@PostMapping("/strategy-import")
public TelecontrolResponse.CommonResult<StrategyImportResponse> importStrategy(
    @RequestParam("files") MultipartFile file, 
    @RequestParam("lang") String lang)
```

### 2. 电站容量管理

**获取电站容量信息 (PowerPlanStations)**
```java
@GetMapping("/power-plan-stations")
public TelecontrolResponse.CommonResult<List<StationCapacityResponse>> getPowerPlanStations()
```

**刷新电站容量信息 (PowerPlanStationsRefresh)**
```java
@PostMapping("/power-plan-stations-refresh")
public TelecontrolResponse.CommonResult<List<StationCapacityResponse>> refreshPowerPlanStations(
    @RequestBody List<Long> stationIds)
```

### 3. 功率计划管理

**功率计划CRUD操作**
```java
// 新增
@PostMapping("/power-plan-add")
public TelecontrolResponse.CommonResult<String> addPowerPlan(@RequestBody PowerPlanRequest request)

// 查询列表
@PostMapping("/power-plan-list")
public TelecontrolResponse.CommonResult<PageResult<PowerPlanListResponse>> getPowerPlanList(@RequestBody PowerPlanRequest request)

// 查询详情
@GetMapping("/power-plan-detail/{id}")
public TelecontrolResponse.CommonResult<PowerPlanDetailResponse> getPowerPlanDetail(@PathVariable Long id)

// 更新
@PutMapping("/power-plan-update")
public TelecontrolResponse.CommonResult<String> updatePowerPlan(@RequestBody PowerPlanRequest request)

// 停止
@PostMapping("/power-plan-stop/{id}")
public TelecontrolResponse.CommonResult<String> stopPowerPlan(@PathVariable Long id, @RequestParam String lang)

// 删除
@DeleteMapping("/power-plan-delete/{id}")
public TelecontrolResponse.CommonResult<String> deletePowerPlan(@PathVariable Long id, @RequestParam String account, @RequestParam String password)
```

### 4. 历史记录管理

**查询下发记录 (GetPlanHis)**
```java
@PostMapping("/plan-history")
public TelecontrolResponse.CommonResult<PageResult<PlanHistoryResponse>> getPlanHistory(@RequestBody PlanHistoryRequest request)
```

**导出下发记录 (planHisExport)**
```java
@PostMapping("/plan-history-export")
public void exportPlanHistory(@RequestBody PlanHistoryExportRequest request, HttpServletResponse response)
```

### 5. 项目包管理

**另存项目包 (ProjectPackAdd)**
```java
@PostMapping("/project-pack-add")
public TelecontrolResponse.CommonResult<String> addProjectPack(@RequestBody ProjectPackAddRequest request)
```

**加载项目包列表 (ProjectPackList)**
```java
@GetMapping("/project-pack-list/{userId}")
public TelecontrolResponse.CommonResult<List<ProjectPackResponse>> getProjectPackList(@PathVariable Long userId)
```

## 🚀 **关键特性**

### 1. 严格按照Python逻辑实现
- **数据库查询逻辑**：完全复制Python中的SQL查询逻辑
- **业务流程**：保持与Python相同的业务处理流程
- **数据结构**：维持相同的数据输入输出格式
- **异常处理**：对应Python中的异常处理机制

### 2. 使用现代Java技术栈
- **Spring Boot**：提供自动配置和依赖注入
- **MyBatis-Plus**：简化数据库操作
- **Lombok**：减少样板代码
- **Validation**：参数验证
- **Swagger**：API文档生成

### 3. 完整的分层架构
- **Controller层**：处理HTTP请求和响应
- **Service层**：实现业务逻辑
- **Mapper层**：数据访问操作
- **Entity层**：数据库实体映射

### 4. 全面的异常处理
- **自定义异常类**：TelecontrolException及其子类
- **全局异常处理器**：统一处理各种异常
- **参数验证**：自动验证请求参数

## 📊 **数据流转示例**

### 新增功率计划流程
```
1. Controller接收请求 → PowerPlanRequest
2. Service验证业务逻辑 → 检查重复性、权限等
3. Mapper执行数据库操作 → 插入TPowerDeliverRecords
4. 返回统一响应格式 → CommonResult<String>
```

### 查询功率计划列表流程
```
1. Controller接收查询参数 → PowerPlanRequest
2. Service构建查询条件 → 分页、过滤条件
3. Mapper执行复杂查询 → 关联查询多表
4. 转换响应格式 → PageResult<PowerPlanListResponse>
```

## 🧪 **测试验证**

提供了完整的测试类`TelecontrolStrategyServiceTest.java`，包含：

- **单元测试**：每个方法的独立测试
- **集成测试**：完整业务流程测试
- **数据验证**：输入输出数据格式验证
- **异常测试**：异常情况处理验证

## 🔧 **配置说明**

### application.yml配置示例
```yaml
telecontrol:
  file-upload:
    upload-path: /tmp/uploads
    max-file-size: 10
    allowed-types: [xlsx, xls, csv]
  minio:
    endpoint: http://localhost:9000
    access-key: minioadmin
    secret-key: minioadmin
    bucket-name: tianlu
  redis:
    key-prefix: telecontrol_strategy_
    expire-time: 3600
  strategy:
    template-file-name: 策略模板.xlsx
    execution-timeout: 300
    max-retry-count: 3
```

## 📝 **使用说明**

1. **引入依赖**：确保项目包含Spring Boot、MyBatis-Plus等依赖
2. **配置数据库**：创建对应的数据库表结构
3. **配置参数**：在application.yml中配置相关参数
4. **启动服务**：运行Spring Boot应用
5. **测试接口**：使用Swagger UI或Postman测试API接口

## 🎯 **总结**

本Java实现完全按照Python原始逻辑进行转换，确保：

- ✅ **功能完整性**：所有15个方法全部实现
- ✅ **逻辑一致性**：严格按照Python逻辑实现
- ✅ **数据一致性**：保持相同的数据结构和格式
- ✅ **性能优化**：使用Java生态的最佳实践
- ✅ **可维护性**：清晰的分层架构和代码组织
- ✅ **可扩展性**：支持后续功能扩展和优化

这个Java实现可以直接替换Python版本，提供相同的功能和API接口。
