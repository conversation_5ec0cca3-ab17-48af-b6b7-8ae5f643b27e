#!/usr/bin/env python
# coding=utf-8

# from Application.Models.His.r_ACDMS import HisSD<PERSON>
# from Application.Models.User.page_data_20 import PageData20
# from Tools.DB.mysql_user import user_session, user_session_20
# from Application.Models.User.page_data import PageData
# from Tools.Utils.num_utils import translate_text
# from Tools.Utils.time_utils import timeUtils
# from Tools.DB.guizhou_his import sguizhou1_session, sguizhou2_session, sguizhou3_session, sguizhou4_session,sguizhou5_session,sguizhou6_session,sguizhou7_session,sguizhou8_session
#
# #系统监控页面配置
# db_=[sguizhou1_session, sguizhou2_session, sguizhou3_session, sguizhou4_session,sguizhou5_session,sguizhou6_session,sguizhou7_session,sguizhou8_session]
# # db_=[sguizhou1_session]
#
# sys_name=[{"name":"VolAB","return_key":"U_Volt"},{"name":"VolBC","return_key":"V_Volt"},{"name":"VolCA","return_key":"W_Volt"},{"name":"CurA","return_key":"U_Curr"},{"name":"CurB","return_key":"V_Curr"},
#           {"name":"CurC","return_key":"W_Curr"},{"name":"PwFtr1","return_key":"U_Pf"},{"name":"ActPw","return_key":"U_RealPw"},{"name":"RctPw","return_key":"U_ReactPw"},{"name":"Freq","return_key":"Freq"},
#           {"name":"CellVol","return_key":"DcVolt"},{"name":"CellCur","return_key":"DcCurr"},{"name":"CellPw","return_key":"DcPw"},{"name":"IgbtMaxT","return_key":"ModuleTem"},{"name":"DayChgEle","return_key":"AcChagCapyDaly"},
#           {"name":"DayDigEle","return_key":"AcDisgCapyDaly"},{"name":"TotlChgEle","return_key":"DcChagCapyTotl"},{"name":"TotlDigEle","return_key":"DcDisgCapyTotl"},{"name":"ChdRunSt","return_key":"Sw1OnOff"},
#           {"name":"W11TolFlt","return_key":"W11TolFlt"},{"name":"W11TolWn","return_key":"W11TolWn"},{"name":"W9ChgOpn","return_key":"W9ChgOpn"},{"name":"W11AcOpnS","return_key":"W11AcOpnS"},{"name":"Volt","return_key":"SysVolt"},
#           {"name":"Cur","return_key":"SysCurr"},{"name":"Soc","return_key":"SysSOC"},{"name":"Soh","return_key":"SysSOH"},{"name":"SigMxVol","return_key":"SysCellVmax"},{"name":"SigMiVol","return_key":"SysCellVmin"},{"name":"SigAvgVol","return_key":"SysCellVminRn"},
#           {"name":"SigMxTem","return_key":"SysCellVminPn"},{"name":"SigMiTem","return_key":"SysCellTmax"},{"name":"SigAvgTem","return_key":"SysCellTmaxRn"},{"name":"W2BmsTlEr","return_key":"W2BmsTlEr"},{"name":"W3CellVlWn","return_key":"W3CellVlWn"}]
#
# # sys_name=[{"name":"VolAB","return_key":"U_Volt"},{"name":"TotlChgEle","return_key":"DcChagCapyTotl"},{"name":"TotlDigEle","return_key":"DcDisgCapyTotl"}]
#
# sys_page_id=[{"page_id":230,"id":1,"list":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]},
#              {"page_id":231,"id":2,"list":[25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48]},
#              {"page_id":232,"id":3,"list":[49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72]},
#              {"page_id":233,"id":4,"list":[73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96]},
#              {"page_id":234,"id":5,"list":[97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120]}]
#
# pcs_page_id=[{"page_id":355,"id":1,"list":[1,2,3,4]},
#              {"page_id":356,"id":2,"list":[5,6,7,8]},
#              {"page_id":357,"id":2,"list":[9,10,11,12]},
#              {"page_id":358,"id":2,"list":[13,14,15,16]},
#              {"page_id":359,"id":2,"list":[17,18,19,20]},
#              {"page_id":360,"id":2,"list":[21,22,23,24]},
#              {"page_id":361,"id":2,"list":[25,26,27,28]},
#              {"page_id":362,"id":2,"list":[29,30,31,32]},
#              {"page_id":363,"id":2,"list":[33,34,35,36]},
#              {"page_id":364,"id":2,"list":[37,38,39,40]},
#              {"page_id":365,"id":2,"list":[41,42,43,44]},
#              {"page_id":366,"id":2,"list":[45,46,47,48]},
#              {"page_id":367,"id":2,"list":[49,50,51,52]},
#              {"page_id":368,"id":2,"list":[53,54,55,56]},
#              {"page_id":369,"id":2,"list":[57,58,59,60]},
#              {"page_id":370,"id":2,"list":[61,62,63,64]},
#              {"page_id":371,"id":2,"list":[65,66,67,68]},
#              {"page_id":372,"id":3,"list":[69,70,71,72]},
#              {"page_id":373,"id":4,"list":[73,74,75,76]},
#              {"page_id":374,"id":4,"list":[77,78,79,80]},
#              {"page_id":375,"id":4,"list":[81,82,83,84]},
#              {"page_id":376,"id":4,"list":[85,86,87,88]},
#              {"page_id":377,"id":4,"list":[89,90,91,92]},
#              {"page_id":378,"id":4,"list":[93,94,95,96]},
#              {"page_id":379,"id":4,"list":[97,98,99,100]},
#              {"page_id":380,"id":4,"list":[101,102,103,104]},
#              {"page_id":381,"id":4,"list":[105,106,107,108]},
#              {"page_id":382,"id":4,"list":[109,110,111,112]},
#              {"page_id":383,"id":4,"list":[113,114,115,116]},
#              {"page_id":384,"id":5,"list":[117,118,119,120]}]
#
# fire_page_id=[{"page_id":385,"id":1,"list":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]},
#              {"page_id":386,"id":2,"list":[25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48]},
#              {"page_id":387,"id":3,"list":[49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72]},
#              {"page_id":388,"id":4,"list":[73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96]},
#              {"page_id":389,"id":5,"list":[97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120]}]
#
# air_page_id=[{"page_id":390,"id":1,"list":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]},
#              {"page_id":391,"id":2,"list":[25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48]},
#              {"page_id":392,"id":3,"list":[49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72]},
#              {"page_id":393,"id":4,"list":[73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96]},
#              {"page_id":394,"id":5,"list":[97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120]}]
#
# #系统通信
# sys_com_page_id=[{"page_id":395,"id":1,"list":[1,2,3,4,5,6,7,8]},
#              {"page_id":396,"id":2,"list":[9,10,11,12,13,14,15,16]},
#              {"page_id":397,"id":3,"list":[17,18,19,20,21,22,23,24]},
#              {"page_id":398,"id":4,"list":[25,26,27,28,29,30,31,32]},
#              {"page_id":399,"id":4,"list":[33,34,35,36,37,38,39,40]},
#              {"page_id":400,"id":4,"list":[41,42,43,44,45,46,47,48]},
#              {"page_id":401,"id":4,"list":[49,50,51,52,53,54,55,56]},
#              {"page_id":402,"id":4,"list":[57,58,59,60,61,62,63,64]},
#              {"page_id":403,"id":4,"list":[65,66,67,68,69,70,71,72]},
#              {"page_id":404,"id":4,"list":[73,74,75,76,77,78,79,80]},
#              {"page_id":405,"id":4,"list":[81,82,83,84,85,85,86,87,88]},
#              {"page_id":406,"id":4,"list":[89,90,91,92,93,94,95,96]},
#              {"page_id":407,"id":4,"list":[97,98,99,100,101,102,103,104]},
#              {"page_id":408,"id":4,"list":[105,106,107,108,109,110,111,112]},
#              {"page_id":409,"id":4,"list":[113,114,115,116,117,118,119,120]}]
#
# # PCS_name=[{"name":"VolAB","return_key":"U_Volt"},{"name":"VolBC","return_key":"V_Volt"},{"name":"VolCA","return_key":"W_Volt"},{"name":"CurA","return_key":"U_Curr"},{"name":"CurB","return_key":"V_Curr"},{"name":"CurC","return_key":"W_Curr"},{"name":"PwFtr1","return_key":"U_Pf"},
# #           {"name":"ActPw","return_key":"U_RealPw"},{"name":"RctPw","return_key":"U_ReactPw"},{"name":"Freq","return_key":"Freq"},{"name":"CellVol","return_key":"DcVolt"},{"name":"CellCur","return_key":"DcCurr"},{"name":"CellPw","return_key":"DcPw"},
# #           {"name":"IgbtMaxT","return_key":"ModuleTem"},{"name":"CtlTem","return_key":"AmbientTem"},{"name":"CtlHum","return_key":"CtlHum"},{"name":"FreeCap","return_key":"MaxOperCapy"},{"name":"DayChgEle","return_key":"AcChagCapyDaly"},{"name":"DayDigEle","return_key":"AcDisgCapyDaly"},{"name":"TotlChgEle","return_key":"DcChagCapyTotl"},{"name":"TotlDigEle","return_key":"DcDisgCapyTotl"},{"name":"ChdRunSt","return_key":"Sw1OnOff"},{"name":"W11TolFlt","return_key":"W11TolFlt"},{"name":"W11TolWn","return_key":"W11TolWn"}]
#
# PCS_name=[{"name":"VolAB","return_key":"U_Volt"},{"name":"VolBC","return_key":"V_Volt"},{"name":"VolCA","return_key":"W_Volt"},{"name":"CurA","return_key":"U_Curr"},{"name":"CurB","return_key":"V_Curr"},{"name":"CurC","return_key":"W_Curr"},{"name":"PwFtr1","return_key":"U_Pf"},
#           {"name":"ActPw","return_key":"U_RealPw"},{"name":"RctPw","return_key":"U_ReactPw"},{"name":"Freq","return_key":"Freq"},{"name":"CellVol","return_key":"DcVolt"},{"name":"CellCur","return_key":"DcCurr"},{"name":"CellPw","return_key":"DcPw"},
#           {"name":"IgbtMaxT","return_key":"ModuleTem"},{"name":"CtlTem","return_key":"AmbientTem"},{"name":"CtlHum","return_key":"CtlHum"},{"name":"FreeCap","return_key":"MaxOperCapy"},{"name":"DayChgEle","return_key":"AcChagCapyDaly"},
#           {"name":"DayDigEle","return_key":"AcDisgCapyDaly"},{"name":"TotlChgEle","return_key":"DcChagCapyTotl"},{"name":"TotlDigEle","return_key":"DcDisgCapyTotl"},{"name":"ChdRunSt","return_key":"Sw1OnOff"},{"name":"W11TolFlt","return_key":"W11TolFlt"},{"name":"W11TolWn","return_key":"W11TolWn"}]
#
# BMS_name=[{"name":"Volt","return_key":"SysVolt"},{"name":"Cur","return_key":"SysCurr"},{"name":"Soc","return_key":"SysSOC"},{"name":"Soh","return_key":"SysSOH"},{"name":"SigMxVol","return_key":"SysCellVmax"},
#           {"name":"SigMiVol","return_key":"SysCellVmin"},{"name":"SigAvgVol","return_key":"SysCellVminRn"},{"name":"SigMxTem","return_key":"SysCellVminPn"},{"name":"SigMiTem","return_key":"SysCellTmax"},{"name":"SigAvgTem","return_key":"SysCellTmaxRn"},{"name":"ChgCap","return_key":"SysCellTmaxPn"},{"name":"DigCap","return_key":"SysCellTmin"},
#           {"name":"MxChgPw","return_key":"SysChagCurLim"},{"name":"MxDigPw","return_key":"SysDisgCurLim"},{"name":"MxChgCur","return_key":"SysChagPwLim"},{"name":"MxDigCur","return_key":"SysDisgPwLim"},{"name":"RoomTem1","return_key":"RoomTem1"},{"name":"CurHum1","return_key":"CurHum1"},{"name":"GasTem1","return_key":"GasTem1"},
#          {"name":"AcVol1","return_key":"AcVol1"},{"name":"AcCur1","return_key":"AcCur1"},{"name":"DcVol1","return_key":"DcVol1"},{"name":"CoilTem1","return_key":"SysRnInso"},{"name":"CoorTem1","return_key":"AyRunStat"},{"name":"OutWtTem","return_key":"OutWtTem"},{"name":"IptWtTem","return_key":"IptWtTem"},{"name":"OutPres1","return_key":"OutPres1"},{"name":"IptPres1","return_key":"IptPres1"},{"name":"GasDensity1","return_key":"GasDensity1"},{"name":"GasDensity2","return_key":"GasDensity2"},{"name":"CmuSt","return_key":"CmuSt"},{"name":"AirSt","return_key":"AirSt"},
#          {"name":"CrWpSt","return_key":"rWpSt"},{"name":"CrPsSt1","return_key":"CrPsSt1"},{"name":"CrHotSt","return_key":"CrHotSt"},{"name":"W2BmsTlEr","return_key":"W2BmsTlEr"},{"name":"W2FbtDisg","return_key":"W2FbtDisg"},{"name":"W2FbtChag","return_key":"W2FbtChag"},{"name":"W3ChgOfWn","return_key":"W3ChgOfWn"},{"name":"W3DigOfWn","return_key":"W3DigOfWn"},{"name":"W70Xf1Er","return_key":"W70Xf1Er"},{"name":"W70Xf2Er","return_key":"W70Xf2Er"},{"name":"W70XfWn","return_key":"W70XfWn"},{"name":"W3SocLw1","return_key":"W3SocLw1"},{"name":"W3SocLw2","return_key":"W3SocLw2"},{"name":"W3CellVlWn","return_key":"W3CellVlWn"}]
# #系统通信
# sys_com_name=[{"name":"W1Swh","return_key":"AC1InrTmp"}]
#
# #消防
# fire_name=[{"name":"GasDensity1","return_key":"GasDensity1"},{"name":"GasDensity2","return_key":"GasDensity2"},{"name":"W70Xf1Er","return_key":"W70Xf1Er"},{"name":"W70Xf2Er","return_key":"W70Xf2Er"},{"name":"W70XfWn","return_key":"W70XfWn"}]
# #空调
# air_name=[{"name":"CoilTem1","return_key":"CoilTem1"},{"name":"RoomTem1","return_key":"RoomTem1"},{"name":"CurHum1","return_key":"CurHum1"},{"name":"GasTem1","return_key":"GasTem1"},{"name":"W28HiWn","return_key":"W28HiWn"},{"name":"W28LoWn","return_key":"W28LoWn"},
#           {"name":"W28InrErWn","return_key":"W28InrErWn"},{"name":"W28OsdErWn","return_key":"W28OsdErWn"},{"name":"W28PrsErWn","return_key":"W28PrsErWn"}]
#
#
#
# #温度和电压最大和最小
# BMS_T_V_name=['SigMxVol','SigMiVol','SigMxTem','SigMiTem']
#
# table_1 = 't_measure'
# table_2 = 't_status'
# table_3 = 't_discrete'
# HisTable_1 = HisSDA(table_1)
# HisTable_2 = HisSDA(table_2)
# HisTable_3 = HisSDA(table_3)
#
# def unm_f(m):
#     '''获取page_area'''
#     snum=0
#     aa = int((m.name).split('.')[4][2:])
#     bb = int((m.name).split('.')[2][6:])
#     for i in range(1,63):
#         n=i-1
#         if bb == i and aa == 1:
#             snum = n+ i
#         elif bb == i and aa == 2:
#             snum = n+i+1
#     return snum
#
# def page_area_f_zg(m,snum):
#     page_area=''
#     if 'PCS' in m.name:
#         page_area = 'Pcs' + snum
#     elif 'BMS' in m.name :
#         page_area = 'Bms' + snum
#     return page_area
#
# def PCSgzztjh(s,page_area,pcs_gzzt_dict,page_id_):
#     '''PCS故障状态集合'''
#     try:
#         nn = int(s.name.split('.')[-1][1:3])
#     except:
#         nn = int(s.name.split('.')[-1][1:2])
#     key_ = 'xtzt%s' % (str(nn))
#     print ('wwwwwwwwwwwwwwwwwww',s.name,key_)
#     if page_area not in pcs_gzzt_dict.keys():
#         pcs_gzzt_dict[page_area] = {}
#         if key_ not in pcs_gzzt_dict[page_area].keys():
#             pcs_gzzt_dict[page_area]['descr'] = s.descr
#             pcs_gzzt_dict[page_area]['page_id'] = page_id_
#             pcs_gzzt_dict[page_area][key_] = []
#             pcs_gzzt_dict[page_area][key_].append(s.name)
#         else:
#             pcs_gzzt_dict[page_area][key_].append(s.name)
#     else:
#         if key_ not in pcs_gzzt_dict[page_area].keys():
#             pcs_gzzt_dict[page_area][key_] = []
#             pcs_gzzt_dict[page_area][key_].append(s.name)
#         else:
#             pcs_gzzt_dict[page_area][key_].append(s.name)
# def BMSgzztjh(s,page_area,bms_gzzt_dict):
#     '''BMS故障状态集合'''
#     try:
#         nn = int(s.name.split('.')[-1][1:3])
#     except:
#         nn = int(s.name.split('.')[-1][1:2])
#     key_ = 'xtzt%s' % (str(nn))
#     if page_area not in bms_gzzt_dict.keys():
#         bms_gzzt_dict[page_area] = {}
#         if key_ not in bms_gzzt_dict[page_area].keys():
#             bms_gzzt_dict[page_area][key_] = []
#             bms_gzzt_dict[page_area][key_].append(s.name)
#         else:
#             bms_gzzt_dict[page_area][key_].append(s.name)
#     else:
#         if key_ not in bms_gzzt_dict[page_area].keys():
#             bms_gzzt_dict[page_area][key_] = []
#             bms_gzzt_dict[page_area][key_].append(s.name)
#         else:
#             bms_gzzt_dict[page_area][key_].append(s.name)
#
# # #系统监测
# # for b in range(0,8):
# #     db_con = db_[b]  # 获取具体数据库链接
# #     measure = db_con.query(HisTable_1.name, HisTable_1.descr).order_by(HisTable_1.id.asc()).all()
# #     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
# #     discrete = db_con.query(HisTable_3.name, HisTable_3.descr).order_by(HisTable_3.id.asc()).all()
# #     print ('PPPPPPPPPPPP')
# #     for l in sys_name:
# #         for m in measure:
# #             if (m.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(m)
# #                 page_area = 'Pcs' + str(snum)
# #                 print ('OOOOOOOOOOO',snum)
# #                 for page_id in sys_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr=translate_text(m.descr, 2)
# #                         p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='measure', page_id=page_id['page_id'], is_use=1,
# #                                      op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #         for s in status:
# #             if (s.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(s)
# #                 page_area = 'Pcs' + str(snum)
# #                 for page_id in sys_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr = translate_text(s.descr, 2)
# #                         p = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='status', page_id=page_id['page_id'], is_use=1,
# #                                          op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #         for d in discrete:
# #             if (d.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(d)
# #                 page_area = 'Pcs' + str(snum)
# #                 for page_id in sys_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr = translate_text(d.descr, 2)
# #                         p = PageData20(name=d.name, descr=d.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='discrete', page_id=page_id['page_id'], is_use=1,
# #                                          op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #         print (333333333333333333)
#
# #PCS监测
# for b in range(0,8):
#     db_con = db_[b]  # 获取具体数据库链接
#     # measure = db_con.query(HisTable_1.name, HisTable_1.descr).order_by(HisTable_1.id.asc()).all()
#     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
#     # discrete = db_con.query(HisTable_3.name, HisTable_3.descr).order_by(HisTable_3.id.asc()).all()
#     #故障状态集合
#     pcs_gzzt_dict = {}
#     print (111111111111111111111)
#     # for l in PCS_name:
#     #     for m in measure:
#     #         if (m.name).split('.')[-1] == l['name']:
#     #             snum = unm_f(m)
#     #             page_area = 'Pcs' + str(snum)
#     #             for page_id in pcs_page_id:
#     #                 if snum in page_id['list']:
#     #                     en_descr = translate_text(m.descr, 2)
#     #                     p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='measure', page_id=page_id['page_id'], is_use=1,
#     #                                  op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
#     #                     user_session_20.add(p)
#     #     print (3333333333333333333333333)
#     #     for s in status:
#     #         if (s.name).split('.')[-1] == l['name']:
#     #             snum = unm_f(s)
#     #             page_area = 'Pcs' + str(snum)
#     #             for page_id in pcs_page_id:
#     #                 if snum in page_id['list']:
#     #                     en_descr = translate_text(s.descr, 2)
#     #                     p = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='status', page_id=page_id['page_id'], is_use=1,
#     #                                      op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
#     #                     user_session_20.add(p)
#     #     print (4444444444444444444)
#     #     for d in discrete:
#     #         if (d.name).split('.')[-1] == l['name']:
#     #             snum = unm_f(d)
#     #             page_area = 'Pcs' + str(snum)
#     #             for page_id in pcs_page_id:
#     #                 if snum in page_id['list']:
#     #                     en_descr = translate_text(d.descr, 2)
#     #                     p = PageData20(name=d.name, descr=d.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='discrete', page_id=page_id['page_id'], is_use=1,
#     #                                      op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
#     #                     user_session_20.add(p)
#     #     print (555555555555555555555)
#     for s in status:  # 故障状态字
#         if 'PCS' in s.name:
#             snum = str(unm_f(s))
#             page_id_=0
#             for page_id in pcs_page_id:
#                 if int(snum) in page_id['list']:
#                     page_id_=page_id['page_id']
#             page_area = 'Pcs' + str(snum)
#             PCSgzztjh(s, page_area,pcs_gzzt_dict,page_id_)
#     if pcs_gzzt_dict != {}:
#         '''PCS状态字'''
#         for pp in pcs_gzzt_dict.keys():
#             chu = pcs_gzzt_dict[pp]['descr'].split(' ')[2][-1]
#             try:
#                 unt = int(pcs_gzzt_dict[pp]['descr'].split(' ')[0][-2:])
#             except:
#                 unt = int(pcs_gzzt_dict[pp]['descr'].split(' ')[0][-1])
#             page_id=pcs_gzzt_dict[pp]['page_id']
#             for ppp in pcs_gzzt_dict[pp].keys():
#                 if ppp=='descr' or ppp=='page_id':
#                     pass
#                 else:
#                     name = ('#'.join(str(pcs_gzzt_dict[pp][ppp]).split("', '")))[2:-2]
#                     return_key = ppp
#                     try:
#                         nnn=int(return_key[-2:])
#                         print ('cccccccccccccccccc',return_key,nnn)
#                     except:
#                         nnn = int(return_key[-1])
#                         print ('bbbbbbbbbbbbbbb', return_key, nnn)
#                     print ('33333333333333333',return_key,nnn)
#                     if 'xtzt' in return_key:
#                         descr = 'Pcs站'+str(unt) +'-'+chu+'#储能'+' 状态字' + str(nnn)
#                         en_descr = 'Pcs station'+str(unt) +'-'+chu+'#stored energy'+' status word' + str(nnn)
#                     elif 'xtbj' in return_key:
#                         descr = 'Pcs站' + str(unt) + '-' + chu + '#储能' + ' 故障字' + str(nnn)
#                         en_descr = 'Pcs station' + str(unt) + '-' + chu + '#stored energy' + ' fault word' + str(nnn)
#                     # if nnn == 2:
#                     #     continue
#                     if nnn == 12:
#                         pass
#                     else:
#                         pss = PageData20(name=name, descr=descr, en_descr=en_descr, page_area=pp, return_key=return_key,type_name='status',
#                                          page_id=page_id, is_use=1, op_ts=timeUtils.getNewTimeStr(), station='guizhou',mode='db',
#                                          method='selfbit')
#                         user_session_20.add(pss)
#                         user_session_20.commit()
#     print (6666666666666666666666)
# # BMS监测
# # for b in range(0, 8):
# #     db_con = db_[b]  # 获取具体数据库链接
# #     measure = db_con.query(HisTable_1.name, HisTable_1.descr).order_by(HisTable_1.id.asc()).all()
# #     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
# #     discrete = db_con.query(HisTable_3.name, HisTable_3.descr).order_by(HisTable_3.id.asc()).all()
# #     bms_gzzt_dict = {}# 故障状态集合
# #     vv = {}#电压最大和最小
# #     tt = {}#温度最大和最小
# #     print (111111111111111111111)
# #     for l in BMS_name:
# #         for m in measure:
# #             name_en = (m.name).split('.')[-1]
# #             if name_en == l['name']:
# #                 # if 'EmuActPw' in m.name:
# #                 #     print ('8****************************************************')
# #                 #     snum_1,snum_2 = snum = unm_f(m)
# #                 #     page_area = 'BMS' + str(snum)
# #                 #     en_descr = translate_text(m.descr, 2)
# #                 #     page_id = snum + 234
# #                 #     if name_en == l['name']:
# #                 #         p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area,
# #                 #                        return_key=l['return_key'], type_name='measure', page_id=page_id, is_use=1,
# #                 #                        op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                 #         user_session_20.add(p)
# #                 snum = unm_f(m)
# #                 page_area = 'BMS' + str(snum)
# #                 en_descr = translate_text(m.descr, 2)
# #                 page_id = snum + 234


                # p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='measure', page_id=page_id, is_use=1,
                #              op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
                # user_session_20.add(p)
                # user_session_20.commit()
# #         print (33333333333333333)
# #         for s in status:
# #             if (s.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(s)
# #                 page_area = 'BMS' + str(snum)
# #                 page_id = snum + 234
# #                 en_descr = translate_text(s.descr, 2)
# #                 p = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='status', page_id=page_id, is_use=1,
# #                                  op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                 user_session_20.add(p)
# #                 user_session_20.commit()
# #         print (4444444444444444)
# #         for d in discrete:
# #             if (d.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(d)
# #                 page_area = 'BMS' + str(snum)
# #                 page_id = snum + 234
# #                 en_descr = translate_text(d.descr, 2)
# #                 p = PageData20(name=d.name, descr=d.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='discrete', page_id=page_id, is_use=1,
# #                                  op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                 user_session_20.add(p)
# #                 user_session_20.commit()
# #         print (666666666666666666)
# #
# #     # 电压极差
# #     print ('***************', vv)
# #     for k,v in vv.items():
# #         try:
# #             chu = v[0].split('.')[4][-1]
# #         except:
# #             continue
# #         page_area = 'BMS' + str(k)
# #         try:
# #             unt = int(v[0].split('.')[2][-2])
# #         except:
# #             unt = int(v[0].split('.')[2][-1])
# #         descr = 'BMS 堆' + str(unt) + '-' + chu + '#储能' + ' 堆单体电压极差'
# #         en_descr = 'BMS pile' + str(unt) + '-' + chu + '#stored energy' + ' reactor cell voltage range'
# #         pp = PageData20(name=v[0] + '#' + v[1], descr=descr, en_descr=en_descr, page_area=page_area,
# #                        return_key='SysCellVrange', type_name='measure', page_id=v[2], is_use=1,
# #                        op_ts=timeUtils.getNewTimeStr(), method='-', station='guizhou', mode='db')
# #         user_session_20.add(pp)
# #         user_session_20.commit()
# #     print (77777777777777777777)
# #     # 温度极差
# #     for k,v in tt.items():
# #         try:
# #             chu = v[0].split('.')[4][-1]
# #         except:
# #             continue
# #         page_area = 'BMS' + str(k)
# #         try:
# #             unt = int(v[0].split('.')[2][-2])
# #         except:
# #             unt = int(v[0].split('.')[2][-1])
# #         descr = 'BMS 堆' + str(unt) + '-' + chu + '#储能' + ' 堆单体温度极差'
# #         en_descr = 'BMS pile' + str(unt) + '-' + chu + '#stored energy' + ' reactor cell temperature range'
# #         p = PageData20(name=v[0] + '#' + v[1], descr=descr, en_descr=en_descr, page_area=page_area,
# #                        return_key='SysCellTrange', type_name='measure', page_id=v[2], is_use=1,
# #                        op_ts=timeUtils.getNewTimeStr(), method='-',station='guizhou', mode='db')
# #         user_session_20.add(p)
# #         user_session_20.commit()
# #
# #     print (88888888888888888888888)
#
# #消防
# # for b in range(0,8):
# #     db_con = db_[b]  # 获取具体数据库链接
# #     measure = db_con.query(HisTable_1.name, HisTable_1.descr).order_by(HisTable_1.id.asc()).all()
# #     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
# #     print ('PPPPPPPPPPPP')
# #     for l in fire_name:
# #         for m in measure:
# #             if (m.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(m)
# #                 page_area = 'BMS' + str(snum)
# #                 print ('OOOOOOOOOOO',snum)
# #                 for page_id in fire_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr=translate_text(m.descr, 2)
# #                         p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='measure', page_id=page_id['page_id'], is_use=1,
# #                                      op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #                         user_session_20.commit()
# #         for s in status:
# #             if (s.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(s)
# #                 page_area = 'BMS' + str(snum)
# #                 for page_id in fire_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr = translate_text(s.descr, 2)
# #                         p = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='status', page_id=page_id['page_id'], is_use=1,
# #                                          op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #                         user_session_20.commit()
# #         print (333333333333333333)
#
# # #空调
# # for b in range(0,8):
# #     db_con = db_[b]  # 获取具体数据库链接
# #     measure = db_con.query(HisTable_1.name, HisTable_1.descr).order_by(HisTable_1.id.asc()).all()
# #     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
# #     print ('PPPPPPPPPPPP')
# #     for l in air_name:
# #         for m in measure:
# #             if (m.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(m)
# #                 page_area = 'BMS' + str(snum)
# #                 print ('OOOOOOOOOOO',snum)
# #                 for page_id in air_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr=translate_text(m.descr, 2)
# #                         p = PageData20(name=m.name, descr=m.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='measure', page_id=page_id['page_id'], is_use=1,
# #                                      op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #                         user_session_20.commit()
# #         for s in status:
# #             if (s.name).split('.')[-1] == l['name']:
# #                 snum = unm_f(s)
# #                 page_area = 'BMS' + str(snum)
# #                 for page_id in air_page_id:
# #                     if snum in page_id['list']:
# #                         en_descr = translate_text(s.descr, 2)
# #                         p = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area, return_key=l['return_key'],type_name='status', page_id=page_id['page_id'], is_use=1,
# #                                          op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         user_session_20.add(p)
# #                         user_session_20.commit()
# #         print (333333333333333333)
#
# #系统通信
# # for b in range(0,8):
# #     db_con = db_[b]  # 获取具体数据库链接
# #     status = db_con.query(HisTable_2.name, HisTable_2.descr).order_by(HisTable_2.id.asc()).all()
# #     print ('PPPPPPPPPPPP')
# #     for l in sys_com_name:
# #         for s in status:
# #             if (s.name).split('.')[-1] == l['name']:
# #                 if 'W1Swh' in s.name:
# #                     print ('8****************************************************')
# #                     snum1 = 0
# #                     snum2 = 0
# #                     bb = int((s.name).split('.')[2][6:])
# #                     for i in range(1, 63):
# #                         n = i - 1
# #                         if bb == i :
# #                             snum1 = n + i
# #                             snum2 = n + i + 1
# #                     page_area_1 = 'BMS' + str(snum1)
# #                     page_area_2 = 'BMS' + str(snum2)
# #                     en_descr = translate_text(s.descr, 2)
# #
# #                     for page_id in sys_com_page_id:
# #                         print ('wwwwwwwwwwwwwww', page_id)
# #                         # if snum1 in page_id['list']:
# #                         #     print ('vvvvvvvvvvvvvvvvvvvvvvvvvvv')
# #                         #     p1 = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area_1,
# #                         #                    return_key=l['return_key'], type_name='status', page_id=page_id['page_id'], is_use=1,
# #                         #                    op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                         #     user_session_20.add(p1)
# #                         #     user_session_20.commit()
# #                         if snum2 in page_id['list']:
# #                             print ('mmmmmmmmmmmmmmmmmmmmm')
# #                             p2 = PageData20(name=s.name, descr=s.descr, en_descr=en_descr, page_area=page_area_2,
# #                                            return_key=l['return_key'], type_name='status', page_id=page_id['page_id'], is_use=1,
# #                                            op_ts=timeUtils.getNewTimeStr(), station='guizhou', mode='db')
# #                             user_session_20.add(p2)
# #                             user_session_20.commit()
# #
# #
# #         print (333333333333333333)
# #之前代码
#     # for s in status:#故障状态字
#     #     if 'BMS' in s.name:
#     #         snum = str(unm_f(s))
#     #         page_area = page_area_f_zg(s,snum)
#     #         BMSgzztjh(s,page_area,bms_gzzt_dict)
#     #
#     # if bms_gzzt_dict!={}:
#     #     '''BMS状态字'''
#     #     for pp in bms_gzzt_dict.keys():
#     #         for ppp in bms_gzzt_dict[pp].keys():
#     #             name=('#'.join(str(bms_gzzt_dict[pp][ppp]).split("', '")))[2:-2]
#     #             return_key=ppp
#     #             if 'xtzt' in return_key:
#     #                 descr=pp+' 状态字'+return_key[-1]
#     #             elif 'xtbj' in return_key:
#     #                 descr = pp + ' 故障字' + return_key[-1]
#     #             pss = PageData(name=name, descr=descr,page_area=pp, return_key=return_key,type_name='status',page_id=228, is_use=1, op_ts=timeUtils.getNewTimeStr(), station='datong', mode='db', method='selfbit')
#     #             user_session.add(pss)
#
#
#     # if ss_dict_1!={}:
#     #     pss = PageData(name=('#'.join(str(list(ss_dict_1.values())[0]).split("', '")))[2:-2], descr='空调故障', page_area=('#'.join(str(list(ss_dict_1.keys())[0]).split("', '"))), return_key='Cokg1', type_name='status',
#     #                  page_id=211, is_use=1, op_ts=timeUtils.getNewTimeStr(), station='datong', mode='db',method='or')
#     #     user_session.add(pss)
#
#
#     # for l in PCS_list_:  # PCS
#     # for l in BMS_list_:  # 电池
#     # for l in sys_tx_list:  # 系统通信
#     # for s in status:#故障状态字
#     #     # if 'PCS' in s.name:
#     #     #     snum = str(unm_f(s))
#     #     #     page_area = page_area_f_zg(s,snum)
#     #     #     PCSgzztjh(s, page_area,pcs_gzzt_dict)
#     #     if 'BMS' in s.name:
#     #         snum = str(unm_f(s))
#     #         page_area = page_area_f_zg(s,snum)
#     #         BMSgzztjh(s,page_area,bms_gzzt_dict)
#
#
#
#     # for l in list_name_sys_kongtiao:  #'''系统监控空调的测量温度集合'''
#     #     for m in measure:
#     #         if (m.name).split('.')[-1] == l['name']:
#     #             snum=str(unm_f(m))
#     #             if '.SS.' in m.name and 'SS.TemS' in m.name:
#     #                 page_area = page_area_f(m)
#     #                 return_key = l['return_key']
#     #                 p = PageData(name=m.name, descr=m.descr, page_area=page_area, return_key=return_key,
#     #                              type_name='measure', page_id=211, is_use=1, op_ts=timeUtils.getNewTimeStr(),
#     #                              station='datong', mode='db')
#     #                 user_session.add(p)

# # user_session_20.commit()
# print (****************)
# user_session_20.close()
# #启动方式和冻结表一样

def _name_join(self, db, pcsSt, lp, bank, na):
    '''电池名称拼接'''
    if bank:  # 电池簇的名称
        if db == 'binhai' or db == 'taicang':
            return 'G%s.H%s.R%s%s' % (pcsSt, lp, bank, na)
        elif db == 'ygzhen':
            dui = divmod(bank, 2)
            a = self._ygzhen_join(pcsSt, lp)
            return '%s.S%s.R%s%s' % (a, sum(dui), bank, na)
        elif db == 'zgtian':
            a, b = self._zgtian_join(pcsSt, lp, bank)
            return '%s%s' % (a, na)

    else:  # 电池堆
        if db == 'binhai' or db == 'taicang':
            return 'G%s.H%s%s' % (pcsSt, lp, na)
        elif db == 'ygzhen':
            a = self._ygzhen_join(pcsSt, lp)
            return '%s%s' % (a, na)
        elif db == 'zgtian':  # 中天
            a, b = self._zgtian_join(pcsSt, lp, bank)
            return '%s%s' % (a, na)

def _ygzhen_join(self, pcsSt, lp):
    '''永臻拼接'''
    nam = 'G1.H1'
    if pcsSt == 1:
        if lp == 1:
            nam = 'G1.H1'
        elif lp == 2:
            nam = 'G1.H2'
        elif lp == 3:
            nam = 'G2.H1'
        elif lp == 4:
            nam = 'G2.H2'
    elif pcsSt == 2:
        if lp == 1:
            nam = 'G3.H1'
        elif lp == 2:
            nam = 'G3.H2'
        elif lp == 3:
            nam = 'G4.H1'
        elif lp == 4:
            nam = 'G4.H2'
    return nam
def _zgtian_join(self, pcsSt, lp, bank):
    '''中天的'''
    dui = ''
    if bank:  # 拼接电池簇
        dui = divmod(bank, 3)
        if dui[1] == 2:
            dui = '.S%s.R%s' % (dui[0] + dui[1] - 1, bank)
        else:
            dui = '.S%s.R%s' % (sum(dui), bank)

    if pcsSt == 1:
        if lp < 3:
            nam = 'G1.H%s%s' % (lp, dui)
        else:
            nam = 'G2.H%s%s' % (lp - 2, dui)
    elif pcsSt == 2:
        if lp < 3:
            nam = 'G3.H%s%s' % (lp, dui)
        else:
            nam = 'G4.H%s%s' % (lp - 2, dui)
    elif pcsSt == 3:
        if lp < 3:
            nam = 'G5.H%s%s' % (lp, dui)
        else:
            nam = 'G6.H%s%s' % (lp - 2, dui)
    elif pcsSt == 4:
        if lp < 3:
            nam = 'G7.H%s%s' % (lp, dui)
        else:
            nam = 'G8.H%s%s' % (lp - 2, dui)

    return nam, nam[:2]

