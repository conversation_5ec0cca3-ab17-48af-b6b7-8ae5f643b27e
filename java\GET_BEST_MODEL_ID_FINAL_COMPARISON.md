# getBestModelId方法 - Python到Java完整转换对比

## 问题分析

您指出的问题完全正确：之前的Java实现没有正确处理Python中的WITH CTE语句和完整的SQL执行逻辑。

## Python原始代码分析

```python
def get_best_model_id(target_id, mstation_id, start_time, end_time):
    """
    获取预测准确率最高的模型
    """
    # 查询所有符合条件的model_id
    model_ids = models.DictModel.objects.filter(is_active=1, is_use=1).values_list('id', flat=True)

    in_clause = ', '.join(map(str, model_ids))
    # 先看时间段内是否有准确率数据
    sql_rate = """SELECT count(id)
                            FROM dwd_dict_model_rate
                            WHERE target_id = {}
                              AND mstation_id = {}
                              AND forecast_time BETWEEN '{}' AND '{}'
                              AND is_use = 1
                              AND model_id IN ({})""".format(target_id, mstation_id, start_time, end_time, in_clause)

    queryset = None

    with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
        try:
            dwd_cursor.execute(sql_rate)
            rate_res = dwd_cursor.fetchone()
            if rate_res[0]:
                sql = """WITH filtered_data AS (
                                SELECT *
                                FROM dwd_dict_model_rate
                                WHERE target_id = {}
                                  AND mstation_id = {}
                                  AND forecast_time BETWEEN '{}' AND '{}'
                                  AND is_use = 1
                                  AND model_id IN ({})
                            )
                            SELECT model_id, AVG(rate) AS avg_rate
                            FROM filtered_data
                            GROUP BY model_id
                            ORDER BY avg_rate DESC
                            LIMIT 1;""".format(target_id, mstation_id, start_time, end_time, in_clause)
            else:
                sql = """SELECT model_id, rate AS avg_rate
                                FROM dwd_dict_model_rate
                                WHERE target_id = {}
                                  AND mstation_id = {}
                                  AND is_use = 1
                                  AND model_id IN ({})
                                ORDER BY forecast_time DESC, rate DESC
                                LIMIT 1;""".format(target_id, mstation_id, in_clause)
            # 获取查询结果
            dwd_cursor.execute(sql)
            queryset = dwd_cursor.fetchone()
        except Exception as e:
            error_log.error(e)
            return None, target_id

    # 获取结果
    if queryset:
        best_model_id = queryset[0]
    else:
        best_model_id = None
    keys_list = list(FORECASTING_MODEL_ID_MAPPING.keys())
    if str(best_model_id) in keys_list:
        return int(FORECASTING_MODEL_ID_MAPPING[str(best_model_id)]), 1
    # 因为目前王玲霞三个模型还没有接入推荐策略 所以所有推荐策略都是小丽模型的 后期接入后放开这里：
    # return None
    return best_model_id, 20
```

## 修正后的Java实现

### 1. 服务层实现 (PowerLoadForecastingServiceImpl.java)

```java
public Long[] getBestModelId(Long targetId, Long mstationId, String startTime, String endTime) {
    try {
        // 查询所有符合条件的model_id - 对应Python中的models.DictModel.objects.filter(is_active=1, is_use=1).values_list('id', flat=True)
        List<Long> modelIds = getActiveModelIds();
        if (modelIds.isEmpty()) {
            return new Long[]{null, targetId};
        }

        // 构建IN条件 - 对应Python中的in_clause = ', '.join(map(str, model_ids))
        String inClause = modelIds.stream()
            .map(String::valueOf)
            .collect(Collectors.joining(", "));

        // 先看时间段内是否有准确率数据 - 对应Python中的sql_rate查询和dwd_cursor.execute(sql_rate)
        Map<String, Object> rateResult = powerLoadForecastingMapper.getRateCount(
            targetId, mstationId, startTime, endTime, inClause);
        
        // 获取准确率数据计数 - 对应Python中的rate_res = dwd_cursor.fetchone()
        Long rateCount = 0L;
        if (rateResult != null && rateResult.get("count_result") != null) {
            rateCount = ((Number) rateResult.get("count_result")).longValue();
        }

        Map<String, Object> queryset = null;
        // 根据是否有准确率数据选择不同的SQL - 对应Python中的if rate_res[0]逻辑
        if (rateCount > 0) {
            // 有准确率数据，使用WITH CTE查询 - 完全对应Python中的第一个SQL
            queryset = powerLoadForecastingMapper.getBestModelWithCTE(
                targetId, mstationId, startTime, endTime, inClause);
        } else {
            // 没有准确率数据，查询最新的准确率记录 - 完全对应Python中的第二个SQL
            queryset = powerLoadForecastingMapper.getLatestModelRate(
                targetId, mstationId, inClause);
        }

        // 获取结果 - 对应Python中的if queryset: best_model_id = queryset[0] else: best_model_id = None
        Long bestModelId = null;
        if (queryset != null && queryset.get("model_id") != null) {
            bestModelId = ((Number) queryset.get("model_id")).longValue();
        }

        // 检查FORECASTING_MODEL_ID_MAPPING映射 - 对应Python中的keys_list = list(FORECASTING_MODEL_ID_MAPPING.keys())
        Map<String, Integer> forecastingMapping = getForecastingModelIdMapping();
        Set<String> keysList = forecastingMapping.keySet();
        
        // 对应Python中的if str(best_model_id) in keys_list逻辑
        if (bestModelId != null && keysList.contains(String.valueOf(bestModelId))) {
            // 找到映射，返回映射后的模型ID和target_id=1 - 对应Python中的return int(FORECASTING_MODEL_ID_MAPPING[str(best_model_id)]), 1
            Integer mappedModelId = forecastingMapping.get(String.valueOf(bestModelId));
            return new Long[]{mappedModelId.longValue(), 1L};
        }

        // 因为目前王玲霞三个模型还没有接入推荐策略 所以所有推荐策略都是小丽模型的 后期接入后放开这里：
        // return new Long[]{null, targetId}; - 对应Python中注释的return None
        // 对应Python中的return best_model_id, 20
        return new Long[]{bestModelId, 20L};

    } catch (Exception e) {
        log.error("获取最佳模型ID失败", e);
        // 对应Python中的except Exception as e: error_log.error(e) return None, target_id
        return new Long[]{null, targetId};
    }
}
```

### 2. Mapper层实现 (PowerLoadForecastingMapper.java)

```java
/**
 * 执行准确率计数查询
 * 对应Python中的sql_rate查询
 */
@Select("SELECT count(id) as count_result " +
        "FROM dwd_dict_model_rate " +
        "WHERE target_id = #{targetId} " +
        "AND mstation_id = #{mstationId} " +
        "AND forecast_time BETWEEN #{startTime} AND #{endTime} " +
        "AND is_use = 1 " +
        "AND model_id IN (${inClause})")
Map<String, Object> getRateCount(@Param("targetId") Long targetId,
                                @Param("mstationId") Long mstationId,
                                @Param("startTime") String startTime,
                                @Param("endTime") String endTime,
                                @Param("inClause") String inClause);

/**
 * 执行WITH CTE查询获取最佳模型
 * 对应Python中有准确率数据时的SQL查询
 */
@Select("WITH filtered_data AS (" +
        "    SELECT * " +
        "    FROM dwd_dict_model_rate " +
        "    WHERE target_id = #{targetId} " +
        "      AND mstation_id = #{mstationId} " +
        "      AND forecast_time BETWEEN #{startTime} AND #{endTime} " +
        "      AND is_use = 1 " +
        "      AND model_id IN (${inClause})" +
        ") " +
        "SELECT model_id, AVG(rate) AS avg_rate " +
        "FROM filtered_data " +
        "GROUP BY model_id " +
        "ORDER BY avg_rate DESC " +
        "LIMIT 1")
Map<String, Object> getBestModelWithCTE(@Param("targetId") Long targetId,
                                       @Param("mstationId") Long mstationId,
                                       @Param("startTime") String startTime,
                                       @Param("endTime") String endTime,
                                       @Param("inClause") String inClause);

/**
 * 获取最新的准确率记录
 * 对应Python中没有准确率数据时的SQL查询
 */
@Select("SELECT model_id, rate AS avg_rate " +
        "FROM dwd_dict_model_rate " +
        "WHERE target_id = #{targetId} " +
        "  AND mstation_id = #{mstationId} " +
        "  AND is_use = 1 " +
        "  AND model_id IN (${inClause}) " +
        "ORDER BY forecast_time DESC, rate DESC " +
        "LIMIT 1")
Map<String, Object> getLatestModelRate(@Param("targetId") Long targetId,
                                      @Param("mstationId") Long mstationId,
                                      @Param("inClause") String inClause);
```

## 关键修正点

### ✅ 1. WITH CTE语句完整保留
- **Python**: 使用完整的WITH CTE语法
- **Java**: 在@Select注解中完整保留WITH CTE语法

### ✅ 2. 数据库连接和游标处理
- **Python**: `with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:`
- **Java**: 通过MyBatis的@Select注解自动处理连接和结果映射

### ✅ 3. SQL执行顺序完全一致
- **Python**: 先执行sql_rate，再根据结果选择执行不同的sql
- **Java**: 先调用getRateCount，再根据结果选择调用不同的方法

### ✅ 4. 异常处理逻辑一致
- **Python**: `except Exception as e: error_log.error(e) return None, target_id`
- **Java**: `catch (Exception e) { log.error(...) return new Long[]{null, targetId}; }`

### ✅ 5. 业务注释完全保留
- 保留了所有Python中的重要业务注释
- 特别是关于"王玲霞三个模型"的特殊处理说明

## 验证清单

| 验证项 | Python | Java | 状态 |
|--------|--------|------|------|
| 模型ID查询 | `models.DictModel.objects.filter(...)` | `getActiveModelIds()` | ✅ |
| IN条件构建 | `', '.join(map(str, model_ids))` | `Collectors.joining(", ")` | ✅ |
| 准确率计数 | `dwd_cursor.execute(sql_rate)` | `getRateCount(...)` | ✅ |
| WITH CTE查询 | 完整WITH语法 | 完整WITH语法 | ✅ |
| ORDER BY查询 | 完整ORDER BY语法 | 完整ORDER BY语法 | ✅ |
| 结果提取 | `queryset[0]` | `queryset.get("model_id")` | ✅ |
| 映射检查 | `str(best_model_id) in keys_list` | `keysList.contains(String.valueOf(...))` | ✅ |
| 返回值格式 | `return best_model_id, 20` | `return new Long[]{bestModelId, 20L}` | ✅ |

现在的Java实现真正做到了与Python代码的**完全一致性**，包括WITH CTE语句的完整保留和所有业务逻辑的精确转换。
