#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-07-24 11:13:53
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\TianLuAppBackend\encryption\aes_cbc.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-08 13:39:15


#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ
# @Date         : 2022-09-08 17:28:09
# @FilePath     : \RHBESS_Service\Tools\DataEnDe\aes_cbc.py
# @Email        : <EMAIL>
# @LastEditTime : 2023-07-21 11:01:22


from Crypto.Cipher import AES
import os, json
from Crypto import Random
import base64
import hashlib
from encryption.MD5 import MD5Tool
import requests

"""
aes加密算法
padding : PKCS7
"""


class AESUtil:
    __BLOCK_SIZE_16 = BLOCK_SIZE_16 = AES.block_size

    @staticmethod
    def encryt(str1, key, iv):
        cipher = AES.new(key, AES.MODE_CBC, iv)
        x = AESUtil.__BLOCK_SIZE_16 - (len(str1) % AESUtil.__BLOCK_SIZE_16)
        if x != 0:
            str1 = str1 + chr(x) * x
        str1 = str1.encode()
        msg = cipher.encrypt(str1)
        # msg = base64.urlsafe_b64encode(msg).replace('=', '')
        msg = base64.b64encode(msg)
        return msg

    @staticmethod
    def decrypt(enStr, key, iv):
        cipher = AES.new(key, AES.MODE_CBC, iv)
        # enStr += (len(enStr) % 4)*"="
        # decryptByts = base64.urlsafe_b64decode(enStr)
        decryptByts = base64.b64decode(enStr)
        msg = cipher.decrypt(decryptByts)
        paddingLen = msg[len(msg) - 1]
        # print ('paddingLen:',paddingLen)
        st = str(msg[0:-paddingLen], encoding="utf-8")
        return st


if __name__ == "__main__":
    key_zj = b"RHBESS1101022022"
    iv_zj = b"1101020090002022"
    key = b"fastfuncn1234567"
    iv = b"fastfuncn1234567"
    openId_zj = b'yuanchu1'
    openId = b'RHBESS01'

    # value,time = [],[]
    # for i in range(20):
    #     value.append(i)
    #     time.append(i)
    data = {"db": "nblangsheng", "workNet": "NBLS001"}
    # data = {"workNet": "NBLS001", "type_": "measure","startTime":"2023-06-25 14:00:00","endTime":"2023-06-25 14:10:00"}
    res = AESUtil.encryt(json.dumps(data), key_zj, iv_zj)  # 加密
    # res = b'AflGkiBtd7ibX8D9T89tIoUk68pC8MOTEBfjcojgDOzIjAcpaY1iB8X6otBk6974'
    # # # # res = b'7LeTqp80qxWqXRzZoMMSMSm6/sLkXFKeybyjMotjUKK73AGO1h0nYMgQIfehIl1ENiLKx44RAOb7xy2/8K6kOXm9KQGMogVbPC5iMsbSlSLkI6O+lzHprsEnTbSRyRlsVlDCBweqm8zcYjCNdMrizy9SjUzr5FbBgpzTeEhn0K0='
    md = str(res, encoding="utf-8") + str(openId, encoding="utf-8")
    md_sale = MD5Tool.get_str_md5(md)


    header = {
        "Bean": str(md_sale),
    }
    result = requests.post("https://rframe.robestec.cn/yc/HisData/getReportInfo", headers=header, data={"data": str(res, encoding="utf-8")})

    d_res = result.json()["data"]
#     desc = {
# 	"msg": "请求成功",
# 	"code": 200,
# 	"data": "+8kT8bSim91zg6+ZhGwFL+kfzDh2G4K2W4VCVxfkV7pGvhbfw1XI1AqmEPJoX47+Q8a2ed9IfYl4T6t10BKZ3b/76qYKiYm+NA1jOLMIc6L2FgRueByN3k4nnkXcdxhbY/vM16b+PnYdprpPE++s/csVUsxWpIHU5gi3Li3qAqa+5R1T1KC0oKFiSJ2QyGmU3exS8FPA2Y187snFNW2EYh+lkk5Ic+uI88lLA/zNTP1szsmDvuRplDgdLP38uD++RZYPwM+j0ZHX9fAoWvQ1KGIwA5red9nxXK85vQkL5rW3OyfUsuE/YAvqGxmnANHCV1AkZf42J/rlEOUirqJS4PXH73qWDSCoSbx867ptGEV71aLOsrV41pZiPddrOLUSHal9Lc2l55AWm6FxvQDBGcHRkj4QKv3SxoM9Q7oLBj011yo61Fg33MgCcDNmI3rD/ydTXfWRJgSQg9Z8UD36SnhWXrtZehOrUtolHB5uRVVq2VpX3/1UqUSyJI2Xo7B3avvL5N4IhcQ27s1ghZMNWRL6jNNt7bv3CXCZFjOY0bLvYKTEoUnKfrz14sOSHJEVj8PiPJprEA640kgUyYl4SCsci32RvwDfZ2bDKmLzNUwmydF7V2940MGDNLS4zQqT17CncDITr3c7CsDBtfCQg0XouvfaZjad0DJXgq5GKF6IzUdbm+K9tSXR+Bobs34oci25G/rW+N/BJ1zXzs1pWtCLt194vjKXf7c3ajsw7ybZh4TMofUcqnKEt7BeeltU8MwES6UAT5HIvfEr8ZQT9siskuy++2s1pfCC7YBva1WmhFJbdoHq71+N/xGWWtBpiXA2HwGrbGi8RM/roe1navcogdKptjlh+YpZl61/xI8k2Dazi60s21LtGZiNHIww8o/q4b7zFl0z3pYdyGJg88N77wgOvsSgw2oCh8O9eWWBNEI/hrP90gJcTez9gFqkc1VggJTQ+8PKEpZV9QzgBRh916iufJzvJ5yLOv+oznu7fE+R/loa26b09JGpAfsN1Aay508adBgPL9N5bwKWwn1wvuGxYS0CUCma9obnyBr84aAe4Z4Cdc1wB5j+W2aCnG28Nou0ongYl5/m5Z7Qx5QYa6ySumqVFNuurbIUfUB/XINPg4VS+EM4IujJ1EcGfgY7qCCveg33fzBRNhbxmBUqtVh7SzYkls+S4Wg0fbfylXqHgkuBX1n9hgMj2U0C6S/n4BJ8UTKGK/lqJY+3mhYFDfRB1Nuz4PO+xPsGEhZiQFL+XL0RkvXgdsgNc8jEKk2MRqY8txr39BpLIDS+tA/SEpZqitP4u+I1SKEvNueMO238EzDi1KT7uoRDDyzLeAiZW9i073+axbPw0w03RApCxjfPzMjBh5uwrXAsnQLO4lM6NhDE6RTE9b3qWBgyYjYRUFLwzzuWqGQ3K4QVKe2Xj7RNVIQsD/PRVhn3UWhNL3casOAPIzUNix6tfkLF4lsk36DzE9EIKftcEdlDV4jwivJ1ufJHp83CbQgd1+B4Pns9577Z4CcPduV0tntFYdT7JM9IEHZw8iCPp6+5sK5vx4lijrU1xLUgnHx/ruIED7kKMnmIoGAy+uykUw95XJ+e5ZSJsjqRdVnFHNvNFXNy3Bb9KSAxDIzgGWsmROtd+v+kQMBMtbx3ONr6Z9XkM8tUFLMtOKNj7kD0ALhyrCBTzrFJj6pF9X3SwZxRSYBUie83hjGYPdbWYLr+MGwjIXDGA57C6Sjv7JddQXiZsi9BNRUbQVV3k6YGzttYAtTsXPDw+XEsUaNBgCJjCVs3Y6Stxwbof0ADRLekhtQyUBXFjs6MfHsAQBiNWRIBhGushSUa5NQUSiTk1sw3jiuA842lhVw/IbsAHHsQ9+v+hinoXZAzx/Y/Egq7Qc51FDZ3nkq6MCgbop40oxVx0TofzPlWD1a4VWCoTnEVba0UNqjQI25pU46DdAhvOQQ8xLGruPKXbbONoBjDp+fcxQsu8ve9FtZ68Zruq8UNoE/y6w7z01Onfd4F77C+2CXLdJMiKzarTCOBrh0gZyim88sMs5vuv/5ffaBFuBpFwtZJc5pgfckghb/sC+9KKdUcUa6LoClrhh5khDnitqV+/sY9EfSJV5th0/XKoVgEs4CPM3wf9jd5/W5TYHbnSDyd4FAL/g8/PHIx++BK5gCUZtYKZ41ZRrvZvHiN5rlYHYUtjRUvYblqVxMlzX5gom7N85DMG/t/ulRZJpJKSfHMbxQTyuKg2mGHhEIHW3EZJcYXjbz81pDcWS2TqGLF7Cck0jJofm+WZK8VsoedOUw+vnsVroxGdf42wdsqto6cFDpYHKgJxLbSsQj0WDs5z2veV5lma60S4zchaYqics7HsfutvCBi8KW5rDK3eRVNi/xNOSBfP/JaXvZbOn186vpkMiaMXr3h4idVl5FpFx6s+aLrxOeAtvz4lmGwufgJtQbOf8OdCVdHtcMjwd1HDMcHLfqI/mC4LjS7cl/Q07ECwNQxihNhEF6rLc/u0TaD2jLgs2q99AuHa2qVDk3pz/nvdpORZqPwRcm4PG3HFTjHjlI6XPJZbPqTUJzLWeL2QKBGwaUCnpI467yR8jwJ/krP0Rtqj5QRTIXb5FoSh6NCoVY0eeKNbU6AITuseJmM582UuGJ3RR/P6pHvoIeMduUHLcWnJBLBbjtDQMFRNap0NiOVJA4y8tClHeEINInzUe1Jss9N3aoo+Meg1OSdfmWZvpcwxbAzjzEVv9bJ1xde5JAUMlG9LBZ4ool3RmReOQxsIE+TFf/P6/vqe01cKLsieGd6YbeJuu2JYKrBzGJyJgAA0K3i7VJM8pi5wWeax+EYB00O7YlrSBt2+j/XXkhpSvZzQHZ5c4HG82GHi6k8T9dLvhqag5etPRMVnfEB95lxoHXRB8rQUUXsBKdEBLk1OXfrvPcOWWXrOCfR/0PBILqDjn8bm3qB483MewSUTxxrV/yxli7Iw9XtufxEPEOjOBx4XdxqbCipMAuhprZK1vPmkvl5A0L6aJe8aDklPRAiV8I1m4rG9sUMCUyZTiJM4PQ2+XivQBv9Svt3T9xaR7gAxBrdFbQtdiNnr13lSg7d7ghG52Z6AILo+ZediFm+dXuwEi/IFLRn/1ow8Su6ph+GRiW8z+qFzKV+ZYo4Jwiekm270Fjj/ctbqMaRWssIo8dIX0evIXd9nsMKTO/tNC1q+rSNH+Olu40WvIRMv80F/7ZeKltUyxAzBESrpk3NZaJQI+zcYG+Ta8rrZuCFPzGx9KMpRJiHbyxyHSSFV/kAIy1W/ZdMWdPa1WkJOFGWuXl/ZwehxMOxrhZgnANLezdPfS8jJhLizU/QAVppRtnrznhtLbhlmz3RFkG5FGIDkJPZF/J2cDIuBJrKhOj39qXmCbtWCLwSlkBmK2yyXpj5mkS+OcadouPDcvuEr0DH4SX78rU8biVwxTLEpWSaYnpg72EeKib2PthsNYik24mymw8hbON4LOAo1C+VQgLGHFUiXRH46uFxF9XFCj5PCCJJ4roBYPqZTwF3KBLN+nn7k7KFYxN6XgmQAUalm82S7mEPBO0a2H+LtrRE66XwjqeB5lDFoa7AID1ofRNmmhFIMn6EZ54fnT51xBk0Yu8VociT/xPCT+oj1eK6XIIMV9qwXzlA7cp1rTVXUXB7myGwCvaut+3BCnFqJ3iX5Bp6iux9B5oRhwNww7s2fUCYRSx8axDJvp3DNYTrOwbL0SqZ9+Naq7jrmDrmnXjf9+sFTZkXq82J1oBhQ59kwvleoHeH8PmX6LxM8QRlYvH6CVP5aeCDY4RM2Sv3TwOFns+bhH0xwn6LlVsf/ZdZy21qymCe18IKmHNyzn9Huf39m1IQoFD12lVKA+yI5yM3rhSJk6jjaAAJ5yevpZSuztDwsX8zdOk9y48OpQpF9sLLpoE67FRz1xA7h3ehNPVoWMFImThsJzL6RUpnN4xZxroNzZmn/jHarMdrtXToarcUhlS2eMdh6TkgQfOWHc356pSDtUWie5OnrBqGqKaGUMvYSJazUcY54DrGkLrkoO/6v+0E2zeXCrKZCKHeZRdSGaSzHQHfJuX8zsXEywmEhJbTQL1Fl1+9XBX9Gm2jsAJ36wq0/4gHYuB3ScCbpOvu7ScJXltF93XREhMcWubuaIiYqIxeWywG6ZOf+0aUHQeeZ9UB4U4wyGH3vl0G/es0Ura4+P8dRl29rGeEFtcJwrxeXX0MvYssNNL64WDg72XwaDSoab1AZPCcQTnzdyGl75NfocXGPcHhIotmzlBiYXTXQLUSpagVPNtw3+DzdJ8F4boMydD8Ad2WBWuNTNJu+vBzF/cQsyhiQYabPpqp9+wAaOVrDP8G8wrSmEWgPIJCO6lvYl+FHctbGSgEXWNOB1Ci4U7Q/RfXvopKLtQMbMDl/SJp2k/2jw10gPJ59NTU1qbCvqppo0EIRicSjRofOyEupKQULPUyvkb94xfjhZEpJsu/jNQ76/E0ZzrWyyugN1KE3ahnMZ7pkicBT/q/nyZ9wsygpYVnecShHt+hNQ0wlXtMA9JCdl/ErCN+QiyQRJQkDCZnGTBZ4xWyVuUK5j5b8ESGx7ysnewINj2wY/PLuIs/5UmAwO9B65vyGGN1/qNu7OCg2PtWAL2jX3XOSl9uUl9YWZhpp0dbWKLkhv+z+9f7qBGiRGJ2eIlG6AS2HP6fgXyB1SZHNmWMyPi6qKfTg8HVr7QN83TmxLUqMSX3QBjWs+dzyih8OAKWYFMQ42qGX8/OPS+x9V8c/DwIMjE1nu7EsMrQzMqiIp886BEt9B8QZ3VqHqvoVu4qQczSq1JAjkuPbFlSUw8A3frOTE5+g1IbrGQoZJmtiq1qTj24ueFFoQAfiXI70UIz81rzRy0tZU5vB3Ct11PfaPaTKJV0cujg38Wmd3OfxXMYf6bO3gxKxuVo4S75it9bh47sWhx3DzDFmzlwKYia6ywxyjt7Ivqn6uLBNm9j2gPjiorqsLORLbLNHedAr0KcEAsaSKDBDcadjZS708HYWYWCqVLoHfDNpN8CxowGPrBIUJ/7a0ELcgviWlNahRwwt6ZTbDe5tMbBrSmXGPLJ5LSc7ZC1E11qm9koESWELNhtzZNQWngSZ9eDrRQL7Otd91LZ6QLJcWAKMBWVYmcNN+fKklzuC2HMVnTGvXcAZ2cBzQNYkB7tR+1bAz66r8rODjK28ziV6Cp59AetYG/Q04ew7lmAs4qPdYgjJHD2g/Wvi/2kFqdTIukgegFZ/HcuvCXxjHQtfWwd0ZvsmnDCiFDdG4Gbyp67AJy0yirmzqjIDubeQApp9Kn/m6S3JnZryc1Ypr3+WHoHwZlxRGRO34HxXQuLYtrkNPzHgRm1JNnMoLfujn+y6Ttt/ET4dLr1Hi3HitgwqXNjZ2u/0U2L4mNVoKoohxEMjZaNxjEN/UZowqx0ZacWpIMZSQZyiNqjCDmfwHqU0cbwnCUfJnzg8Tw4eY16lpSjhZolndHGJijAYvcQKvmxKLegf7za/NG/PS0iQu28A/ANG+rYuyURk3bmfYsaApgXtDHvlUV2btg8IxoX8uj2qmXR92C2kh4rSuC0VtTLsxPHI14lQC3AVpq7FuhhcSUQPGmNS+WNeSWUwEdwvB6HxE7OIMVzEIO0n18CvFYlXF8nKfpi7fszzV++2jWj1UqKthyj7ZewZzT19hJO8fzRItO3Klx88HZEiGIkQ1yiT4JkGzp0qy4nMTYMsJ/AcxtmdTrHSaw5358PWRtIm7HA/Am9+thZgqzUUA6sYmXXhPyzBHilYJs4pn+me4QJfcTNFQO1mn3KPGt76V8xmuYRlnRqmn6vUXr93EYsXLTVkHQFlrFPo/TAbCjBhhV9DhJo/GTbluVjn+pBpLvLsfg+Wl6jNlt8UvKUBEyF+DK+mVo1CZwYUlPYzQd0DZlnYmBvV05lox+1a9J7RYNF5B7tHPyq80zJA5QrsETgPjNx7r3absu7VmXJpx3lQRM7mBp888TVx1Tr4GXr+E+6boOm70+8QBExnGK9yh0QB2X8tNKALuwFAOMjxg8zKwjDZQDead3eUHw/pmmLX3pog8UoQ3SeCAyvXjgAie8123ylT56a85GfoVN9sjxrrNxor3wcIXetTpcOEgRJGozTARvU61vosXnkPCDbD1tGM8cL0aQC0AWXqeOj5sAW1aYdwU3v4AuHrb+mkWf/tv7vA1uoSjEtWvoZMrXXB2CDr5sAZzl8L/gl9gTjn+1PtUeCE73rIT7Lsq9UKpRL9w6/lhiEpczkSWJj8cJUEx3kynJBQHm1BpA5qmn8tSwR3fLd3sq5TJz38N7uLeN+NuIwNjQWf+wz0+UtD4SSt83lPBIIsQMvES67wlkBmernIobQ7mIDjvaQcdtfMWmrNsAn64O57HMxoH4Z15yjd55YwyGhC6mSThOVYcPKGdKe5WH5Ww502KxEeFTxjoSuRVI4iVBO4LcFYr++VM6m4zIdPa5lsI29aQw/xgevRSQWT925LmKBCaBGInivLjapsR4PP7dRDq3M0Ynjwgt/Sxh/A+0EcmphGUlChnxc92cZoQUHSjSTSTs/PC/RR1xr5AzkIYyUTxGxnZsBfzu3BJ0RkJtqh/mMsmeORlo2EsJoBeM0XB4fTBctPAmJNDfK4ERteJ3KGtuVbMDKNDiYDx281P6B6LITaTj28vjY97R7qSsnpAEs0xgevCeCM5OLP7H0xLIlvlnkcQs4M21nd4+tb2vNIIVyV5y3mkJ/0eAeisa65ZRmJgGvpi0U8Qot/HaZr7qjJWoyvaPbltnDqoG7IM5uO+9jw/qwIxd/CjOgp+sP/+CfNnP6nlMDQClZGqMD75FW3VoNiXQexiKal7tyKCEhf3OLWhfmyqI1RikWkVM0eRTcxT6Q/7VT8bmUVqlxcCYgnPyW4ZnVqR0634R/1K4ADvhbMbErKUiYm2rN1Nh5QayxjH32vNf6n2Hxw+6A3VPaGqN4vJ1DVRCwxOzy23A+0Y7Bus9ds2Xk+sM3nH/VnE93kEcZkS3jv6a3uysvN0Q2QHGlKKlBUIht8/oZVuvP1cctRIsYbq7hMvWe9RSsfocFydYBCReTas0suygAyqZsIxVZKvsESASX6SYHF6IJ0NszPGYj4QFmg+WbCAUpiZbruZeN9V+vnl2guJDbaweoa7xkw5cBaaEXl0dawaejP5aoOjR7WE/PrQ6+v3Glb/a33Dq8JoHyMSFHRv13MdXKqRoSFLUorl+5y+l+6NEFR755ONf4nWBL4XLK0/36bPLMDF7OSq7SqFLjlUA/gplKm1geg85fIcU9YUVpQXW+O9r63PN101f0RKronyi39168Eq/moDGJeUmAXnZLci1YnbxwxmwzED+KqUaKyMLcSRgUva0G5kpoKv6Dje3agmRMxz9kC7J3F9OCZMpDKP931ICYvDBdbrrQPMLyHu4fejnlDeWevqkwNd0Bnkbvl89wST88d+KNG5xAm4WhAFJiOBMK8UUWKVRhHJm0HWlYvr13caVn0xTo56x4havkN3NcQnUA1fFE6K+DEkKUCID2aFBoD8cHR1StaorRmqCI1ctEjCmcxac0kfbKCFUg8F2jq2++FvE5vymGh6MQxjDqdP43W6nfaO4poUpPHvgj3fB1yoVcDkxl7R6FntIxGdWi2YJbgzQCn5jEVAaFOt9Frf7/R9MPy9hZKP1N8OdQniPtFB3GlKO3tsdKP6qN0VmJW+JEN5aXyYPz3o2XiZ1iq+LGTepvGVB/JBC3i7A/fiUJgZ1nmyUlhvtTLngz92Vj0Zzk40m0xNh2JMHt5ONxlN2YdWhUgN28W7u10VuWfrPiQtYFWMR9Syw9Veaz7/1Oe/AkFt/z97Y72YS73JfeDhbdgU41t1RCsMHBrIJ2ZjCCxiI2YX3VCODfdAU/vwonAZqD4MN3pNGqdKAOrYmJXHCFfmfYjT8AuR8PfCkN4s2qNIN021qu+enrwo4Ke7XKlQZ2XRGNt5Vjqt027M91e1FKnIwS/yKaVkBKwgiVUk/PISxozWYPV0KZ/Gc6rBu6oRkQ+lm9qpOjkugdOPI8wvWhkCwEpzuiZjZVZZdn3uGGCvjgwuAXjMtwPbMHTGBw7LDaxRtE70jHTTu5LVv+qBHeQyqL1pGdWJMXChCh8om2a2hB688EqlezhFTWLYKTNCOhSAt/wmzq7hThYee3lANu0n2hgQdMU0bEkGhZSncsKz4Qt6cD8R2CBlU+17fLOQr3q32HNaMrbqJLJDnoNMiTxpy8rE/EQU5GGhIM4/hhMJob2ND+Pz7HpMh2kY0+3HIJYiwmq9Z9RMqcF9druizzprgWbCbKMilDR01t1oDYyA3A320reGNwkdDtLA/cHNDG2AhF1VSz2I3g1PAX8QcqMtrVWDXgsiSsaJlCLJdq0aO3yhPomduitYD6MUAYHDXWBeNCWVIx1iSYMiVJZP0PSBRz0CrNVCB52o+88PMZyeF4aZx4ILmwJ6QzKH68XdonWbBLeQn2mY7YGE1YJv22ib4ppzBPWZwY/MAUxppBS1jqBJghCBFbZAXF3+C6dRKMYWRE0TehfUVZrGRsieEEy3/90BNEdeu9ZYmNYL9OXx7LCHNp7ngbVfMQxG9HVqkIBw41jCcruStq3IFrndWsdDaU76X0WB9RNA465+3DJK6RbOsjswUikUZ1DdgUg1tEd5LmL5BNEgchxdGRrMWGZA9qIrzVTW0KYT/J6lqt7i9V5JvZHKqRpJq2BlopfDW0AZ7vJbU1AobbwE8X6LWebUjHf858Dnmb8wz1gkpSP72g9fzCIAUdDfe7vuDm2nmmF8Er6ZDOojsrvv3qDH0ouhh8Sur4cVc362j/vm/QOAD1T7WzVvLBhb+JO7kF3Df/j8Z/JmTbiNNh/i8eQC7/eKCsXqgLt4xXpK9g4G/UDriYv1hAGaCVUPMc49D6nNJc0g6ZW6M66/30iAQIrJT247Ec0BdZ9YpA9zrlVg=="
# }
#     data = AESUtil.decrypt(desc['data'], key_zj, iv_zj)
#     print ('data:',data)

# print self.request.headers.get('Bean')  # 接收消息头信息
