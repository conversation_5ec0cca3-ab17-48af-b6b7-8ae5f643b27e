package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsCreateDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsQueryDTO;
import com.robestec.analysis.dto.tplanpowerrecords.TPlanPowerRecordsUpdateDTO;
import com.robestec.analysis.entity.TPlanPowerRecords;
import com.robestec.analysis.vo.TPlanPowerRecordsVO;

import java.util.List;

/**
 * 功率计划关联记录服务接口
 */
public interface TPlanPowerRecordsService extends ISuperService<TPlanPowerRecords> {

    /**
     * 分页查询功率计划关联记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TPlanPowerRecordsVO> queryTPlanPowerRecords(TPlanPowerRecordsQueryDTO queryDTO);

    /**
     * 创建功率计划关联记录
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTPlanPowerRecords(TPlanPowerRecordsCreateDTO createDTO);

    /**
     * 更新功率计划关联记录
     * @param updateDTO 更新参数
     */
    void updateTPlanPowerRecords(TPlanPowerRecordsUpdateDTO updateDTO);

    /**
     * 删除功率计划关联记录
     * @param id 记录ID
     */
    void deleteTPlanPowerRecords(Long id);

    /**
     * 获取功率计划关联记录详情
     * @param id 记录ID
     * @return 记录详情
     */
    TPlanPowerRecordsVO getTPlanPowerRecords(Long id);

    /**
     * 批量创建功率计划关联记录
     * @param createDTOList 创建参数列表
     */
    void createTPlanPowerRecordsList(List<TPlanPowerRecordsCreateDTO> createDTOList);

    /**
     * 根据计划ID查询功率计划关联记录
     * @param planId 计划ID
     * @return 记录列表
     */
    List<TPlanPowerRecordsVO> getTPlanPowerRecordsByPlanId(Long planId);

    /**
     * 根据功率ID查询功率计划关联记录
     * @param powerId 功率ID
     * @return 记录列表
     */
    List<TPlanPowerRecordsVO> getTPlanPowerRecordsByPowerId(Long powerId);

    /**
     * 根据序号查询功率计划关联记录
     * @param serialNumber 序号
     * @return 记录列表
     */
    List<TPlanPowerRecordsVO> getTPlanPowerRecordsBySerialNumber(Integer serialNumber);

    /**
     * 统计计划ID的关联记录数量
     * @param planId 计划ID
     * @return 记录数量
     */
    Long countByPlanId(Long planId);

    /**
     * 统计功率ID的关联记录数量
     * @param powerId 功率ID
     * @return 记录数量
     */
    Long countByPowerId(Long powerId);
}
