#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-05 08:26:35
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\page_data.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-08-05 14:50:30

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base, user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR, \
    Boolean, Text
class PageData20(user_Base):
    u'页面数据配置表'
    __tablename__ = "t_page_data_20"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    name = Column(Text, nullable=False, comment=u"具体名称,多个变量计算时以#号分割")
    descr = Column(String(256), nullable=True, comment=u"描述")
    page_area = Column(String(256), nullable=False, comment=u"页面区域key")
    return_key = Column(String(256), nullable=False, comment=u"返回值得key")
    type_name = Column(String(50), nullable=False, comment=u"数据类型,status,measure,cumulant")
    page_id = Column(Integer, nullable=False, comment=u"所属页面")
    method = Column(String(20), nullable=True, comment=u"计算方法，+,-,*,/,sum,avg,or,bit")
    is_use = Column(Integer, nullable=False, comment=u"是否启用，1是0否，默认1", server_default='1')
    bit_id = Column(Integer, nullable=False, comment=u"所属bit配置")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    unit = Column(String(10), nullable=True, comment=u"单位")
    station = Column(VARCHAR(50), nullable=True, comment=u"所属站")
    mode = Column(VARCHAR(10), nullable=True, comment=u"查询方式，暂定ram和db")
    device = Column(VARCHAR(50), nullable=True, comment=u"设备名称")

    en_descr = Column(String(256), nullable=True, comment=u"描述")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'name':'%s','descr':'%s','page_area':':%s','return_key':'%s','type_name':'%s','page_id':'%s','method':'%s','bit_id':'%s','op_ts':'%s','unit':'%s','station':'%s','mode':'%s','en_descr':'%s'}" % (
            self.id, self.name, self.descr, self.page_area, self.return_key, self.type_name, self.page_id, self.method,
            self.bit_id, self.op_ts, self.unit, self.station, self.mode, self.en_descr)

