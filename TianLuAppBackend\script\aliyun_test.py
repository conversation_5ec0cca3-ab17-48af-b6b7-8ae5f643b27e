import requests

url = "http://172.17.6.44:9001/api/point/getRealtimeData"
json_data = json_data = {
                "app": "TN001",
                "station": "NBLS001",
                "body": [
                    {
                        "device": "BMS",
                        "datatype": "measure",
                        "totalcall": "0",
                        "body": ["RTCap"]
                    },
                    {
                        "device": "PCS",
                        "datatype": "measure",
                        "totalcall": "0",
                        "body": ["RP", "ChaD", "DisD"]
                    },
                    {
                        "device": "PCS",
                        "datatype": "status",
                        "totalcall": "0",
                        "body": ["Fault", "alarm"]
                    },
                    {
                        "device": "EMS",
                        "datatype": "status",
                        "totalcall": "0",
                        "body": ["AEnC"]
                    }
                ]
            }
response = requests.post(url=url, json=json_data)
print(response.json())

