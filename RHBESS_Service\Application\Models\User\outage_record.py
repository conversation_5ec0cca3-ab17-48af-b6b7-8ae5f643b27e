#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/7/25 下午2:10
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me


from Tools.DB.mysql_user import user_Base, user_session
from Tools.DB.mysql_user import (Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,
                                 Boolean, Text, SmallInteger)
from Application.Models.WorkOrder.spare_info import SpareInfo  # 导入 SpareInfo


class OutageRecord(user_Base):
    """
    停运记录表
    """
    __tablename__ = 't_outage_record'

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey('t_station.id'), nullable=False, comment='所属项目id')
    area = Column(String(100), comment='分区')
    device_type_id = Column(Integer, nullable=False, comment='设备类型id')
    device_sn = Column(String(255), nullable=False, comment='设备编号')
    device_unit_id = Column(Integer, comment='故障设备部件id')
    device_child_unit_id = Column(Integer, comment='故障设备子部件id')
    effect_capacity = Column(Float, comment='影响（停运）容量，单位kWh')
    outage_grade_id = Column(Integer, ForeignKey('c_outage_record_dictionary.id'), comment='停运等级id')
    repair_schedule_id = Column(Integer, ForeignKey('c_outage_record_dictionary.id'), comment='修复进度id')
    discover_time = Column(DateTime, comment='发现时间')
    occur_time = Column(DateTime, nullable=False, comment='发生时间')
    expect_repair_time = Column(DateTime, comment='预计修复时间')
    reality_repair_time = Column(DateTime, comment='实际修复时间')
    fault_phenomenon = Column(Text, comment='故障现象')
    fault_reason = Column(Text, comment='故障原因及影响因素分析')
    scene_solution = Column(Text, comment='现场解决方案')
    preventive_measure = Column(Text, comment='后续预防措施')
    creat_time = Column(DateTime, comment='录入时间')
    creat_type = Column(Integer, default=1, comment='添加方式1:单个添加 2:批量添加，默认1')
    creat_user_id = Column(Integer, comment='录入人')
    upload_file_url = Column(String(300), comment='批量上传文件地址')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    capacity_status = Column(Integer, default=1, comment='影响容量状态 1系统计算 2人为添加')
    factory = Column(String(100), comment='设备厂家')
    device_no = Column(String(100), comment='设备型号')
    record_no = Column(String(100), nullable=False, comment='故障编号')
    record_name = Column(String(50), nullable=False, comment='售后对接人')
    affect_time = Column(String(100), nullable=False, comment='影响时长')

    # 定义关系
    station_info = relationship("Station", backref="records")
    repair_schedule = relationship("OutageRecordDictionary", foreign_keys=[repair_schedule_id],
                                   back_populates="repair_schedules")
    outage_grade = relationship("OutageRecordDictionary", foreign_keys=[outage_grade_id],
                                back_populates="outage_grades")
    effect_periods = relationship("OutageEffectPeriodRecord", back_populates="outage_record1")
    effect_ranges = relationship("OutageEffectRange", back_populates="outage_record2")
    spare_use_records = relationship("OutageSpareUseRecord", back_populates="outage_record3")

    def __repr__(self):
        return f"<OutageRecord(id={self.id}, device_sn='{self.device_sn}', occur_time='{self.occur_time}')>"


class DeviceChildUnit(user_Base):
    """
    设备子部件信息表
    """
    __tablename__ = 't_device_child_unit'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment='设备部件名称')
    parent_id = Column(Integer, ForeignKey('t_device_unit.id'), nullable=False, comment='父级id')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    creat_time = Column(DateTime, comment='创建时间')

    # 定义关系
    parent = relationship("DeviceUnit", foreign_keys=[parent_id])

    def __repr__(self):
        return f"<DeviceChildUnit(id={self.id}, name='{self.name}', parent_id={self.parent_id})>"


class OutageRecordDictionary(user_Base):
    """
    停运记录字典表
    """
    __tablename__ = 'c_outage_record_dictionary'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment='字典值或者描述')
    category = Column(SmallInteger, nullable=False, comment='字典类型1停运等级2修复进度3模版地址')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    creat_time = Column(DateTime, comment='创建时间')

    repair_schedules = relationship("OutageRecord", back_populates="repair_schedule",
                                    foreign_keys="[OutageRecord.repair_schedule_id]")
    outage_grades = relationship("OutageRecord", back_populates="outage_grade",
                                 foreign_keys="[OutageRecord.outage_grade_id]")

    def __repr__(self):
        return f"<OutageRecordDictionary(id={self.id}, name='{self.name}', category={self.category})>"


class OutageEffectPeriodRecord(user_Base):
    """
    影响运行时段记录表
    """
    __tablename__ = 'c_outage_effect_period_record'

    id = Column(Integer, primary_key=True, autoincrement=True)
    begin_time = Column(DateTime, nullable=False, comment='开始时间')
    end_time = Column(DateTime, nullable=False, comment='结束时间')
    remark = Column(String(255), comment='备注')
    outage_id = Column(Integer, ForeignKey('t_outage_record.id'), nullable=False, comment='停运记录id')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    creat_time = Column(DateTime, comment='创建时间')
    effect_pw = Column(Float, comment='时间段内的储能系统功率绝对值')
    add_type = Column(SmallInteger, nullable=False, comment='1自动计算 2手动添加')
    is_edit = Column(SmallInteger, default=2, comment='1编辑过 2未编辑')

    # 定义关系
    outage_record1 = relationship("OutageRecord", back_populates="effect_periods")

    def __repr__(self):
        return f"<OutageEffectPeriodRecord(id={self.id}, begin_time='{self.begin_time}', end_time='{self.end_time}', outage_id={self.outage_id})>"


class OutageSpareUseRecord(user_Base):
    """
    备件记录表
    """
    __tablename__ = 'c_outage_spare_use_record'

    id = Column(Integer, primary_key=True, autoincrement=True)
    spare_id = Column(Integer, ForeignKey('t_spare_info.id'), nullable=False, comment='备件信息id')
    use_num = Column(Integer, nullable=False, comment='领用数量')
    unit = Column(String(50), nullable=False, comment='单位')
    outage_id = Column(Integer, ForeignKey('t_outage_record.id'), nullable=False, comment='停运记录id')
    remark = Column(String(255), comment='备注信息')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    creat_time = Column(DateTime, comment='创建时间')

    # 定义关系
    outage_record3 = relationship("OutageRecord", back_populates="spare_use_records")
    spare_info = relationship("SpareInfo", backref="usages")

    def __repr__(self):
        return f"<OutageSpareUseRecord(id={self.id}, spare_id={self.spare_id}, outage_id={self.outage_id})>"


class OutageEffectRange(user_Base):
    """
    停运记录影响范围关联表
    """
    __tablename__ = 'c_outage_effect_range'

    id = Column(Integer, primary_key=True, autoincrement=True)
    outage_id = Column(Integer, ForeignKey('t_outage_record.id'), nullable=False, comment='停运记录id')
    effect_range = Column(String(255), nullable=False, comment='影响范围')
    is_use = Column(SmallInteger, default=1, comment='是否使用1是0否 默认1')
    creat_time = Column(DateTime, comment='创建时间')

    # 定义关系
    outage_record2 = relationship("OutageRecord", back_populates="effect_ranges")

    def __repr__(self):
        return f"<OutageEffectRange(id={self.id}, outage_id={self.outage_id}, effect_range='{self.effect_range}')>"
