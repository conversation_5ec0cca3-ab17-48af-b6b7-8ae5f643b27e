#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-09 09:00:56
#@FilePath     : \RHBESS_Service\Application\EqAccount\WorkOrder\workOrderHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-07-19 11:29:36



import os
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.WorkOrder.dispatch_type import DispatchType
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Application.Models.WorkOrder.dispatch_step_t import DispatchStep
from Application.Models.WorkOrder.dispatch_r import DispatchR
from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
from Application.Models.User.user import User
from Tools.Utils.send_mail import sendMail_
import logging,json
from sqlalchemy import func
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.EqAccount.WorkOrder.workOrderManager import self_station
import ast
file_path = '/home/<USER>/workorderfiles'
class WorkOrderHandleIntetface(BaseHandler):

    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
            if kt == 'DownloadFiles':  # 文件下载
                filepath = self.get_argument('file_path',None)  # 所属站
                if DEBUG:
                    logging.info('filepath:%s'%(filepath))
                if not os.path.exists(filepath):
                    return self.customError("文件不存在")
                filename = filepath.split('/')[-1]
                self.set_header ('Content-Type', 'application/octet-stream')
                self.set_header ('Content-Disposition', 'attachment; filename=' + filename)
                with open(os.path.join('',filepath), 'rb') as f:
                    data = f.read()
                    f.close()
                self.write(data)
                self.finish()
       
        except Exception as E:
            # user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            pass
            # user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            db = self.get_argument('db','his')  # 所属站
            if kt == 'HandleMeStep':  # 处理我的当前工单
                id = self.get_argument('id',None)  # 步骤id
                content = self.get_argument('content',None) 
                other_content = self.get_argument('other_content',None) 

                realStartTime = self.get_argument('realStartTime',None)   # 实际开始时间
                realEndTime = self.get_argument('realEndTime',None)   # 实际结束时间
                year = timeUtils.getNewTimeStr()[:4]
                file_pathf = '%s/%s'%(file_path,year)
                
                if DEBUG:
                    logging.info('id:%s,content:%s,other_content:%s,realStartTime:%s,realEndTime:%s'%(id,content,other_content,realStartTime,realEndTime))
                if not id:
                    return self.customError("无效id")
                session = self.getOrNewSession()
                uid = session.user['id']
                # user_session.query(DispatchStepR).filter(DispatchStepR.id == id).update({"handle_time":timeUtils.getNewTimeStr(),"imgs":f1,"files":f2,"handle_user":uid,"content":content})  # 
                DR = user_session.query(DispatchStepR).filter(DispatchStepR.id == id).first()
                if str(DR.check_flag) == '2':
                    return  self.customError("当前步骤已驳回，不可操作")
                if str(DR.status) == '2':
                    return  self.customError("当前步骤已结束，不可重复操作")
                
                dsp = user_session.query(DispatchStep).filter(DispatchStep.id == DR.dispatch_step_id).first()
                # if (dsp.is_team==0 and str(dsp.handle_users) != str(uid)) or (dsp.is_team==1 and uid not in eval(str(dsp.handle_users))):
                #     return  self.customError("权限不足")
                # 新增转交人，修改逻辑
                if DR.care_user and str(DR.care_user) != str(uid):
                    return  self.customError("代处理权限不足")
                elif not DR.care_user and str(dsp.handle_users) != str(uid):
                    return  self.customError("权限不足")
                    
                filess = self.request.files
                if not os.path.exists(file_pathf):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_pathf)

                F,all = self._getHandleFile(filess,file_pathf,other_content)  # 文件上传处理
                if not F:
                    return self.customError("入参无效")
                
                DR.content = ';'.join(content.split())
                DR.ticket_files = '#'.join(all[0])
                DR.detection_files = '#'.join(all[1])
                DR.service_files = '#'.join(all[2])
                DR.other_files = '#'.join(all[3])
                DR.log_files = '#'.join(all[4])
                DR.summary_files = '#'.join(all[5])
                DR.handle_files = '#'.join(all[6])
                DR.other_content = '' if not other_content else ';'.join(other_content.split())
                DR.handle_time = timeUtils.getNewTimeStr()
                DR.handle_user = uid
                DR.real_start_time = realStartTime
                DR.real_end_time = realEndTime
                DR.status=2  # 结束当前步骤
                user_session.commit()
                
                # if dsp.check_user:  # 需要审核的步骤，包含最后一步或中间步骤
                #     user = user_session.query(User).filter(User.id==dsp.check_user,User.unregister==1).first()
                #     sendMail_("您有新的待审核任务，请及时登录系统处理","审核消息通知","RHBESS","XXX",[user.email])
                #     return self.returnTypeSuc("")
                if dsp.next_step:  # 有下一步工单
                    em = []
                    NDR = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_step_id == dsp.next_step,DispatchStepR.check_flag!=2).first()
                    dspe = user_session.query(DispatchStep).filter(DispatchStep.id == dsp.next_step).first() # 下一步
                    
                    if not NDR:
                        NDR = DispatchStepR(dispatch_id=DR.dispatch_id,status=1,send_time=timeUtils.getNewTimeStr(),dispatch_step_id=dsp.next_step,stage=dspe.stage)
                        user_session.add(NDR)
                    user_session.commit()
                   
                    # 下一步，要么审核，要么处理
                    if dspe.check_user:  # 需要审核的步骤，包含最后一步或中间步骤
                        user = user_session.query(User).filter(User.id==dspe.check_user,User.unregister==1).first()
                        sendMail_("您有新的待审核任务，请及时登录系统处理","审核消息通知","RHBESS","XXX",[user.email])
                        return self.returnTypeSuc("")
                    elif dspe.is_team == 0:
                        u = user_session.query(User).filter(User.id==dspe.handle_users,User.unregister==1).first()
                        if u:
                            em.append(u.email)
                    elif dspe.is_team == 1:
                        users = user_session.query(User).filter(User.id.in_(eval(str(dspe.handle_users))),User.unregister==1).all()
                        for u in users:
                            if u.email:
                                em.append(u.email)
                    user_session.query(DispatchR).filter(DispatchR.id == NDR.dispatch_id).update({"status":NDR.id})  # 更新工单id
                    user_session.commit()
                    logging.info('send users is %s'%em)
                    sendMail_("您有新的工单需要处理，请及时登录系统处理","工单消息通知","RHBESS","XXX",em)
                    return self.returnTypeSuc("")
                else:  # 最后一步且不需要审核
                    # user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_id == DR.dispatch_id).update({"status":2})
                    user_session.query(DispatchR).filter(DispatchR.id == DR.dispatch_id).update({"status":'end','finish_time':timeUtils.getNewTimeStr()})  # 更新工单id
                    user_session.commit()

                return self.returnTypeSuc("")
            elif kt == 'HandleMeCheckStep':  # 处理我的审批工单
                id = self.get_argument('id',None)  # 步骤id
                content = self.get_argument('content',None) 
                check_flag = self.get_argument('check_flag',None) # 审核状态
                if DEBUG:
                    logging.info('id:%s,content:%s,check_flag:%s'%(id,content,check_flag))
                if not id or not content or not check_flag:
                    return self.customError("入参不完整！")
                session = self.getOrNewSession()
                uid = session.user['id']
                DR = user_session.query(DispatchStepR).filter(DispatchStepR.id == id).first()  # 当前工单步骤
                if str(DR.check_flag) == '2':
                    return  self.customError("当前步骤已驳回，不可操作")
                if DR.check_content:  # 已有审核内容
                    return  self.customError("当前步骤已审核过，不可重复操作")
                dsp = user_session.query(DispatchStep).filter(DispatchStep.id == DR.dispatch_step_id).first()
                if str(dsp.check_user) != str(uid):
                    return  self.customError("权限不足")
                nt = timeUtils.getNewTimeStr()  # 当前时间
                DR.check_flag = check_flag
                DR.check_content = ';'.join(content.split())
                DR.check_time = nt
                DR.handle_time = nt
                DR.status = 2
                user_session.commit()
                if str(check_flag) == '1':  # 通过
                    drs = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_id == DR.dispatch_id,DispatchStepR.stage==DR.stage).all()
                    for ds in drs:
                        if not ds.check_flag:
                            ds.check_flag = 1
                    if dsp.next_step:  # 有下一步工单
                        self._getCheckNext(dsp,DR,nt)
                        return self.returnTypeSuc("")
                    else:  # 最后一步且不需要审核
                        work_flag = user_session.query(DispatchR).filter(DispatchR.working_no == DR.dispatch_r.working_no,DispatchR.working_flag==2).first()
                        if str(DR.dispatch_r.working_flag) == '1' and not work_flag:  # 工号申请，且没有工单  # and not DR.dispatch_r.update_time
                            first_apply_user = self.get_argument('first_apply_user',None) # 准备工作第一个审核人
                            second_apply_user = self.get_argument('second_apply_user',None) # 准备工作第二个审核人
                            handle_user = self.get_argument('handle_user',None) # 实际处理人
                            finall_apply_user = self.get_argument('finall_apply_user',None) # 工作总结审核人
                            # 正式环境
                            if DR.dispatch_r.station=='ygzhen' or DR.dispatch_r.station=='zgtian':
                                first_apply_user = 126
                                second_apply_user = 126
                                finall_apply_user = 126
                            elif DR.dispatch_r.station in self_station:  # 自持电站武霄龙审核
                                first_apply_user = 113
                                second_apply_user = 113
                                finall_apply_user = 113
                            else:  # 其他电站吴晗审核
                                first_apply_user = 6
                                second_apply_user = 6
                                finall_apply_user = 6
                            # 测试环境都选李智
                            # first_apply_user = 9
                            # second_apply_user = 9
                            # finall_apply_user = 9

                            self._addWorkerWorking(first_apply_user,second_apply_user,handle_user,finall_apply_user,DR.dispatch_r.copy_users,dsp.model_step.dispatch_type,DR.dispatch_r.station,
                                                   DR.dispatch_r.working_no,DR.dispatch_r.start_time,DR.dispatch_r.plan_time)
                        logging.info('working create success')
                        user_session.query(DispatchR).filter(DispatchR.id == DR.dispatch_id).update({"status":'end','finish_time':nt})  # 更新工单id
                        user_session.commit()
                        # 给抄送人发送邮件
                        self._sendCopyUserMail(DR.dispatch_r.copy_users)
                    
                elif str(check_flag) == '2':  # 驳回
                    rdispatchR = user_session.query(DispatchR).filter(DispatchR.id == DR.dispatch_id).first()  # 获取工单
                    # 该工单已执行步骤进行退回操作
                    dsr = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_id == rdispatchR.id,DispatchStepR.stage==DR.stage).all()
                    for ds in dsr:
                        ds.check_flag = 2
                        if not ds.status:
                            ds.status = 2
                        if not ds.check_time:
                            ds.check_time = nt
                    user_session.commit()
                    if str(rdispatchR.working_flag) == '1': # 工号服务申请直接关闭重新申请
                        rdispatchR.status = 'off'  # 关闭
                        rdispatchR.finish_time = nt 
                    else:  # 具体工单
                        rdispatchR.finish_time=None
                        # 获取当前阶段的第一步
                        firststep =  user_session.query(DispatchStep).filter(DispatchStep.model_id == dsp.model_id,DispatchStep.stage==dsp.stage).order_by(DispatchStep.id.asc()).first()
                        self._getCheckFirst(firststep,DR,nt,rdispatchR)
                user_session.commit() 
                return self.returnTypeSuc("")
            elif kt == 'CareMeCheckStep':  # 转交当前需要我处理的工单
                id = self.get_argument('id',None)  # 步骤id
                care_user = self.get_argument('care_user',None) # 转交人id
                care_content = self.get_argument('care_content',None) # 转交时信息填写
                if DEBUG:
                    logging.info('id:%s,care_user:%s,care_content:%s'%(id,care_user,care_content))
                if not id or not care_user:
                    return self.customError("入参不完整！")
                session = self.getOrNewSession()
                uid = session.user['id']
                if str(uid) == str(care_user):
                    return  self.customError("转交人不能是自己")
                DR = user_session.query(DispatchStepR).filter(DispatchStepR.id == id).first()  # 当前工单步骤
                dsp = user_session.query(DispatchStep).filter(DispatchStep.id == DR.dispatch_step_id).first()
                if str(dsp.handle_users) != str(uid):
                    return  self.customError("转交权限不足")
                nt = timeUtils.getNewTimeStr()  # 当前时间
                DR.care_user = care_user  # 转交人
                DR.care_time = nt  #转交时间
                DR.care_content = care_content  #转交信息

                dsp.care_user = care_user  # 转交人
                dsp.care_time = nt  #转交时间
                dsp.care_content = care_content  #转交信息
                user_session.commit()

                u = user_session.query(User).filter(User.id==care_user,User.unregister==1).first()
                if u:
                    sendMail_("您有新的转交工单需要处理，请及时登录系统处理","工单消息通知","RHBESS","XXX",[u.email])
                return self.returnTypeSuc("")
            else:
                return self.pathError()

            
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
        
    def _getCheckFirst(self,dsp,DR,nt,rdispatchR):
        '''审核工单退回后创建第一步'''
        em = []
        NNDR = DispatchStepR(dispatch_id=DR.dispatch_id,status=1,send_time=nt,dispatch_step_id=dsp.id,stage=dsp.stage)
        user_session.add(NNDR)
        user_session.commit()
        # rdispatchR.status =NNDR.id
        # user_session.commit()
        
        if dsp.check_user:  # 需要审核的步骤，包含最后一步或中间步骤
            u = user_session.query(User).filter(User.id==dsp.check_user,User.unregister==1).first()
            if u:
                em.append(u.email)
            sendMail_("您有新的待审核任务，请及时登录系统处理","审核消息通知","RHBESS","XXX",em)
            return self.returnTypeSuc("")
        elif dsp.is_team == 0:
            u = user_session.query(User).filter(User.id==dsp.handle_users,User.unregister==1).first()
            if u:
                em.append(u.email)
        elif dsp.is_team == 1:
            users = user_session.query(User).filter(User.id.in_(eval(str(dsp.handle_users))),User.unregister==1).all()
            for u in users:
                if u.email:
                    em.append(u.email)
        
        logging.info('send users is %s'%em)
        sendMail_("您有新的工单需要处理，请及时登录系统处理","工单消息通知","RHBESS","XXX",em)

    def _getCheckNext(self,dsp,DR,nt):
        '''审核工单的下一步'''
        em = []
        NDR = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_step_id == dsp.next_step,DispatchStepR.status==1).first()
        dspe = user_session.query(DispatchStep).filter(DispatchStep.id == dsp.next_step).first()
        
        if not NDR:
            NNDR = DispatchStepR(dispatch_id=DR.dispatch_id,status=1,send_time=nt,dispatch_step_id=dsp.next_step,stage=dspe.stage)
            
            user_session.add(NNDR)
            # user_session.commit()
            # user_session.query(DispatchR).filter(DispatchR.id == NNDR.dispatch_id).update({"status":NNDR.id})  # 更新工单id
        user_session.commit()
        
        if dspe.check_user:  # 需要审核的步骤，包含最后一步或中间步骤
            u = user_session.query(User).filter(User.id==dspe.check_user,User.unregister==1).first()
            if u:
                em.append(u.email)
            # sendMail_("您有新的待审核任务，请及时登录系统处理","审核消息通知","RHBESS","XXX",em)
            # return self.returnTypeSuc("")
        elif dspe.is_team == 0:
            u = user_session.query(User).filter(User.id==dspe.handle_users,User.unregister==1).first()
            if u:
                em.append(u.email)
        elif dspe.is_team == 1:
            users = user_session.query(User).filter(User.id.in_(eval(str(dspe.handle_users))),User.unregister==1).all()
            for u in users:
                if u.email:
                    em.append(u.email)
        logging.info('send users is %s'%em)
        sendMail_("您有新的工单需要处理，请及时登录系统处理","工单消息通知","RHBESS","XXX",em)


    def _getHandleFile(self,filess,file_pathf,other_content):
        '''
        处理上传文件
        filess:接受文件的整内容
        file_pathf：文件路径，不含文件名
        other_content：选择其他时的输入内容
        '''
        ticket_files = filess.get('ticket_files')  # 工作票上传
        detection_files = filess.get('detection_files')  # 现场检测单上传
        service_files = filess.get('service_files')  # 服务报告上传
        other_files = filess.get('other_files')  # 其他文件上传
        log_files = filess.get('log_files')  # 工作日志上传
        summary_files = filess.get('summary_files')  # 工作总结上传
        handle_files = filess.get('handle_files')  # 操作票上传

        # ticket_files_arr,detection_files_arr,service_files_arr,other_files_arr,log_files_arr,summary_files_arr = [],[],[],[],[],[]
        files = [ticket_files,detection_files,service_files,other_files,log_files,summary_files,handle_files]
        all = [[],[],[],[],[],[],[]]
        if other_files and not other_content:  # 选择其他文件时，必须输入内容
            return False,[]
        
        for file in files:
            ind = files.index(file)
            if file:
                for fi in file:
                    data = fi.get('body')
                    filename = fi.get('filename')
                    path = '%s/%s' % (file_pathf,filename)
                    file1 = open(path, 'wb')
                    file1.write(data)
                    file1.close()
                    all[ind].append(path)

        return True, all

    def _addWorkerWorking(self,first_apply_user,second_apply_user,handle_user,finall_apply_user,copy_users,dispatch_type,db,working_no,start_time,plan_time):
        '''
        自动添加工单模板步骤
        first_apply_user,  # 准备工作第一个审核人
        second_apply_user,  # 准备工作第二个审核人
        handle_user,  # 实际操作人
        finall_apply_user,  # 工作总结审核人
        copy_users,  # 抄送人
        dispatch_type,  # 工单类型
        db  # 所属站
        working_no,  # 工号  
        start_time,  # 预计开始时间
        plan_time  # 预计结束时间
        '''
        session = self.getOrNewSession()
        oid = session.user['organization_id']
        uid = session.user['id']
        model = DispatchModel(descr=u'通用电站工作流程',op_ts=timeUtils.getNewTimeStr(),create_user=uid,organization_id=oid,dispatch_type=dispatch_type,working_flag=2)
        user_session.add(model)
        user_session.commit()
        dstep1 = DispatchStep(model_id=model.id,descr=u'准备工作内容',content=u'请填写或上传前期准备工作相关资料',is_team=0 ,handle_users=handle_user,stage=1)  #准备工作内容
        user_session.add(dstep1)
        user_session.commit()
        dstep2 = DispatchStep(model_id=model.id,descr=u'准备工作初步审核',content=u'请仔细核对现场准备工作是否符合进场操作规范，或注意事项',is_team= 0,check_user=first_apply_user,pre_step=dstep1.id,stage=1)  # 准备工作审核第一步
        user_session.add(dstep2)
        user_session.commit()
        dstep3 = DispatchStep(model_id=model.id,descr=u'准备工作最终审核',content=u'核对是否符合进场操作规范',is_team= 0,check_user=second_apply_user,pre_step=dstep2.id,stage=1)  # 准备工作最终审核
        user_session.add(dstep3)
        user_session.commit()
        dstep4 = DispatchStep(model_id=model.id,descr=u'此次工作汇总',content=u'请填写或上传工作汇总相关资料',is_team= 0,handle_users=handle_user,pre_step=dstep3.id,stage=2)  # 工作汇总内容提交
        user_session.add(dstep4)
        user_session.commit()
        dstep5 = DispatchStep(model_id=model.id,descr=u'工作总结审核',content=u'请对此次工作总结进行审核',is_team=0,check_user=finall_apply_user,pre_step=dstep4.id,stage=2)  # 工作汇总审核
        user_session.add(dstep5)
        user_session.commit()
        dstep1.next_step=dstep2.id
        dstep2.next_step=dstep3.id
        dstep3.next_step=dstep4.id
        dstep4.next_step=dstep5.id
        
        user_session.commit()
        rd = DispatchR(descr=u'通用电站工作流程记录',op_ts=timeUtils.getNewTimeStr(),model_id=model.id,start_time=start_time,plan_time=plan_time,create_user=uid,content=u'通用电站流程记录内容',station=db,
        working_no=working_no,working_flag=2,status=dstep1.id,copy_users=copy_users)
        user_session.add(rd)
        user_session.commit()
        rdr = DispatchStepR(dispatch_id=rd.id,status=1,send_time=timeUtils.getNewTimeStr(),dispatch_step_id=dstep1.id,stage=1)
        user_session.add(rdr)
        user_session.commit()
        rd.status=rdr.id
        user_session.commit()
        emails = []
        HUS = user_session.query(User).filter(User.id==handle_user,User.unregister==1).first()
        if HUS and HUS.email:
            emails.append(HUS.email)
            logging.info('send users is %s'%emails)
            sendMail_("您有新的待处理工单，请及时登录系统处理","工单处理消息通知","RHBESS","XXX",emails)
        
        # return self.returnTypeSuc("")

    def _sendCopyUserMail(self, copy_user):
        emails = []
        con_s, copy_user_list = self.is_convertible_to_list(copy_user)
        if not con_s:
            return False
        if len(copy_user_list) == 0:
            return False
        for copy_user in copy_user_list:
            HUS = user_session.query(User).filter(User.id == copy_user, User.unregister == 1).first()
            if HUS and HUS.email:
                emails.append(HUS.email)
        if len(emails)>0:
            logging.info('send users is %s' % emails)
            sendMail_("抄送您的工单已完成，请知悉", "工单抄送消息通知", "RHBESS", "XXX", emails)

    def is_convertible_to_list(self, str_list):
        try:
            result = ast.literal_eval(str_list)
            if isinstance(result, list):
                return True, result
            else:
                return False, None
        except (ValueError, SyntaxError):
            return False, None
