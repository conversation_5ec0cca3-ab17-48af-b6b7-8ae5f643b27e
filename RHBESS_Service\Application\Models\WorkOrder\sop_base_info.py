#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-06-20 08:59:26
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\sop_base_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-28 17:09:17

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.station import Station

class SopBaseInfo(user_Base):
    u'SOP基本表'
    __tablename__ = "t_sop_base_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False, comment=u"SOP名称")
    en_name = Column(String(256), nullable=False, comment=u"SOP名称-英文")
    sn = Column(String(256), nullable=False, comment=u"编号")
    en_sn = Column(String(256), nullable=False, comment=u"编号-英文")
    version = Column(String(256), nullable=False, comment=u"版本号")
    author = Column(String(256), nullable=False, comment=u"作者")
    en_author = Column(String(256), nullable=False, comment=u"作者-英文")
    organization_name = Column(String(256), nullable=False,comment=u"组织")
    en_organization_name = Column(String(256), nullable=False,comment=u"组织-英文")
    pub_time = Column(DateTime, nullable=False,comment=u"发行时间")
    device = Column(String(256), nullable=False,comment=u"目标设备")
    en_device = Column(String(256), nullable=False,comment=u"目标设备-英文")
    check_user = Column(String(256), nullable=True,comment=u"审批人")
    en_check_user = Column(String(256), nullable=True,comment=u"审批人-英文")
    job_length = Column(String(10), nullable=True,comment=u"作业时长")
    remarks = Column(Text, nullable=True, comment=u"备注")
    en_remarks = Column(Text, nullable=True, comment=u"备注-英文")

    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")
    station = Column(String(256), nullable=True, comment=u"所属站")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        # sta = user_session.query(Station).filter(Station.name==self.station).first()
        # if sta:
        #     station = sta.descr
        # else:
        #     station = ''
        bean = "{'id':%s,'name':'%s','op_ts':'%s','sn':'%s','version':'%s','author':'%s','organization_name':'%s','pub_time':'%s','device':'%s','check_user':'%s','job_length':'%s'," \
               "'en_name':'%s','en_sn':'%s','en_author':'%s','en_organization_name':'%s','en_device':'%s','en_check_user':'%s','en_remarks':'%s',}" % (
            self.id,self.name,self.op_ts,self.sn,self.version,self.author,self.organization_name,self.pub_time,self.device,self.check_user,self.job_length,
        self.en_name,self.en_sn,self.en_author,self.en_organization_name,self.en_device,self.en_check_user,self.en_remarks)
        return bean.replace("None",'')
        
