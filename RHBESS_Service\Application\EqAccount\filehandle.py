#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-29 14:44:15
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\filehandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-01 16:54:14


import json,os
import logging
import datetime,math
import pandas as pd
from Application.Models.base_handler import BaseHandler
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import *
import gzip

all_title = ['时间','堆电压','堆电流','堆SOC','堆SOH','堆单体Vmax簇号','堆单体Vmax序号','堆单体Vmax','堆单体Vmin簇号','堆单体Vmin序号','堆单体Vmin','堆单体Tmax簇号','堆单体Tmax序号','堆单体Tmax','堆单体Tmin簇号',
'堆单体Tmin序号','堆单体Tmin','堆可充电量','堆可放电量','堆累计充电电量','堆累计放电电量','簇总电压','簇预充电压','簇电流','绝缘电阻+','绝缘电阻-','簇SOC','簇SOH','簇平均电压','簇最高电压序号','簇最高电压','簇最低电压序号',
'簇最低电压','簇平均温度','簇最高温度序号','簇最高温度','簇最低温度序号','簇最低温度','簇累计充电电量','簇累计放电电量','V1', 'V2', 'V3', 'V4', 'V5', 'V6', 'V7', 'V8', 'V9', 'V10', 'V11', 'V12', 'V13', 'V14', 
'V15', 'V16', 'V17', 'V18', 'V19', 'V20', 'V21', 'V22', 'V23', 'V24', 'V25', 'V26', 'V27', 'V28', 'V29', 'V30', 'V31', 'V32', 'V33', 'V34', 'V35', 'V36', 'V37', 'V38', 'V39', 'V40', 'V41', 'V42', 'V43', 
'V44', 'V45', 'V46', 'V47', 'V48', 'V49', 'V50', 'V51', 'V52', 'V53', 'V54', 'V55', 'V56', 'V57', 'V58', 'V59', 'V60', 'V61', 'V62', 'V63', 'V64', 'V65', 'V66', 'V67', 'V68', 'V69', 'V70', 'V71', 'V72', 
'V73', 'V74', 'V75', 'V76', 'V77', 'V78', 'V79', 'V80', 'V81', 'V82', 'V83', 'V84', 'V85', 'V86', 'V87', 'V88', 'V89', 'V90', 'V91', 'V92', 'V93', 'V94', 'V95', 'V96', 'V97', 'V98', 'V99', 'V100', 'V101', 
'V102', 'V103', 'V104', 'V105', 'V106', 'V107', 'V108', 'V109', 'V110', 'V111', 'V112', 'V113', 'V114', 'V115', 'V116', 'V117', 'V118', 'V119', 'V120', 'V121', 'V122', 'V123', 'V124', 'V125', 'V126', 
'V127', 'V128', 'V129', 'V130', 'V131', 'V132', 'V133', 'V134', 'V135', 'V136', 'V137', 'V138', 'V139', 'V140', 'V141', 'V142', 'V143', 'V144', 'V145', 'V146', 'V147', 'V148', 'V149', 'V150', 'V151', 
'V152', 'V153', 'V154', 'V155', 'V156', 'V157', 'V158', 'V159', 'V160', 'V161', 'V162', 'V163', 'V164', 'V165', 'V166', 'V167', 'V168', 'V169', 'V170', 'V171', 'V172', 'V173', 'V174', 'V175', 'V176', 
'V177', 'V178', 'V179', 'V180', 'V181', 'V182', 'V183', 'V184', 'V185', 'V186', 'V187', 'V188', 'V189', 'V190', 'V191', 'V192', 'V193', 'V194', 'V195', 'V196', 'V197', 'V198', 'V199', 'V200', 'V201', 
'V202', 'V203', 'V204', 'V205', 'V206', 'V207', 'V208', 'V209', 'V210', 'V211', 'V212', 'V213', 'V214', 'V215', 'V216', 'V217', 'V218', 'V219', 'V220', 'V221', 'V222', 'V223', 'V224', 'V225', 'V226', 
'V227', 'V228', 'V229', 'V230', 'V231', 'V232', 'V233', 'V234', 'V235', 'V236', 'V237', 'V238', 'V239', 'V240', 'V241', 'V242', 'V243', 'V244', 'V245', 'V246', 'V247', 'V248', 'V249', 'V250', 'V251', 
'V252', 'V253', 'V254', 'V255', 'V256', 'V257', 'V258', 'V259', 'V260', 'V261', 'V262', 'V263', 'V264', 'V265', 'V266', 'V267', 'V268', 'V269', 'V270', 'V271', 'V272', 'V273', 'V274', 'V275', 'V276', 
'V277', 'V278', 'V279', 'V280', 'V281', 'V282', 'V283', 'V284', 'V285', 'V286', 'V287', 'V288', 'V289', 'V290', 'V291', 'V292', 'V293', 'V294', 'V295', 'V296', 'V297', 'V298', 'V299', 'V300', 'V301', 
'V302', 'V303', 'V304', 'V305', 'V306', 'V307', 'V308', 'V309', 'V310', 'V311', 'V312', 'V313', 'V314', 'V315', 'V316', 'V317', 'V318', 'V319', 'V320', 'V321', 'V322', 'V323', 'V324', 'V325', 'V326', 
'V327', 'V328', 'V329', 'V330', 'V331', 'V332', 'V333', 'V334', 'V335', 'V336', 'V337', 'V338', 'V339', 'V340', 'V341', 'V342', 'V343', 'V344', 'V345', 'V346', 'V347', 'V348', 'V349', 'V350', 'V351', 
'V352', 'V353', 'V354', 'V355', 'V356', 'V357', 'V358', 'V359', 'V360', 'V361', 'V362', 'V363', 'V364', 'V365', 'V366', 'V367', 'V368', 'V369', 'V370', 'V371', 'V372', 'V373', 'V374', 'V375', 'V376', 
'V377', 'V378', 'V379', 'V380', 'V381', 'V382', 'V383', 'V384', 'V385', 'V386', 'V387', 'V388', 'V389', 'V390', 'V391', 'V392', 'V393', 'V394', 'V395', 'V396', 'V397', 'V398', 'V399', 'V400', 'T1', 
'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12', 'T13', 'T14', 'T15', 'T16', 'T17', 'T18', 'T19', 'T20', 'T21', 'T22', 'T23', 'T24', 'T25', 'T26', 'T27', 'T28', 'T29', 'T30', 'T31', 
'T32', 'T33', 'T34', 'T35', 'T36', 'T37', 'T38', 'T39', 'T40', 'T41', 'T42', 'T43', 'T44', 'T45', 'T46', 'T47', 'T48', 'T49', 'T50', 'T51', 'T52', 'T53', 'T54', 'T55', 'T56', 'T57', 'T58', 'T59', 'T60', 
'T61', 'T62', 'T63', 'T64', 'T65', 'T66', 'T67', 'T68', 'T69', 'T70', 'T71', 'T72', 'T73', 'T74', 'T75', 'T76', 'T77', 'T78', 'T79', 'T80', 'T81', 'T82', 'T83', 'T84', 'T85', 'T86', 'T87', 'T88', 'T89', 
'T90', 'T91', 'T92', 'T93', 'T94', 'T95', 'T96', 'T97', 'T98', 'T99', 'T100', 'T101', 'T102', 'T103', 'T104', 'T105', 'T106', 'T107', 'T108', 'T109', 'T110', 'T111', 'T112', 'T113', 'T114', 'T115', 'T116', 
'T117', 'T118', 'T119', 'T120', 'T121', 'T122', 'T123', 'T124', 'T125', 'T126', 'T127', 'T128', 'T129', 'T130', 'T131', 'T132', 'T133', 'T134', 'T135', 'T136', 'T137', 'T138', 'T139', 'T140', 'T141', 
'T142', 'T143', 'T144', 'T145', 'T146', 'T147', 'T148', 'T149', 'T150', 'T151', 'T152', 'T153', 'T154', 'T155', 'T156', 'T157', 'T158', 'T159', 'T160', 'T161', 'T162', 'T163', 'T164', 'T165', 'T166', 
'T167', 'T168', 'T169', 'T170', 'T171', 'T172', 'T173', 'T174', 'T175', 'T176', 'T177', 'T178', 'T179', 'T180', 'T181', 'T182', 'T183', 'T184', 'T185', 'T186', 'T187', 'T188', 'T189', 'T190', 'T191', 
'T192', 'T193', 'T194', 'T195', 'T196', 'T197', 'T198', 'T199', 'T200', 'T201', 'T202', 'T203', 'T204', 'T205', 'T206', 'T207', 'T208', 'T209', 'T210', 'T211', 'T212', 'T213', 'T214', 'T215', 'T216', 
'T217', 'T218', 'T219', 'T220', 'T221', 'T222', 'T223', 'T224', 'T225', 'T226', 'T227', 'T228', 'T229', 'T230', 'T231', 'T232', 'T233', 'T234', 'T235', 'T236', 'T237', 'T238', 'T239', 'T240', 'T241', 
'T242', 'T243', 'T244', 'T245', 'T246', 'T247', 'T248', 'T249', 'T250', 'T251', 'T252', 'T253', 'T254', 'T255', 'T256', 'T257', 'T258', 'T259', 'T260', 'T261', 'T262', 'T263', 'T264', 'T265', 'T266', 
'T267', 'T268', 'T269', 'T270', 'T271', 'T272', 'T273', 'T274', 'T275', 'T276', 'T277', 'T278', 'T279', 'T280', 'T281', 'T282', 'T283', 'T284', 'T285', 'T286', 'T287', 'T288', 'T289', 'T290', 'T291', 
'T292', 'T293', 'T294', 'T295', 'T296', 'T297', 'T298', 'T299', 'T300', 'T301', 'T302', 'T303', 'T304', 'T305', 'T306', 'T307', 'T308', 'T309', 'T310', 'T311', 'T312', 'T313', 'T314', 'T315', 'T316', 
'T317', 'T318', 'T319', 'T320', 'T321', 'T322', 'T323', 'T324', 'T325', 'T326', 'T327', 'T328', 'T329', 'T330', 'T331', 'T332', 'T333', 'T334', 'T335', 'T336', 'T337', 'T338', 'T339', 'T340', 'T341', 
'T342', 'T343', 'T344', 'T345', 'T346', 'T347', 'T348', 'T349', 'T350', 'T351', 'T352', 'T353', 'T354', 'T355', 'T356', 'T357', 'T358', 'T359', 'T360', 'T361', 'T362', 'T363', 'T364', 'T365', 'T366', 
'T367', 'T368', 'T369', 'T370', 'T371', 'T372', 'T373', 'T374', 'T375', 'T376', 'T377', 'T378', 'T379', 'T380', 'T381', 'T382', 'T383', 'T384', 'T385', 'T386', 'T387', 'T388', 'T389', 'T390', 'T391',
 'T392', 'T393', 'T394', 'T395', 'T396', 'T397', 'T398', 'T399', 'T400', 'SOC1', 'SOC2', 'SOC3', 'SOC4', 'SOC5', 'SOC6', 'SOC7', 'SOC8', 'SOC9', 'SOC10', 'SOC11', 'SOC12', 'SOC13', 'SOC14', 'SOC15', 
 'SOC16', 'SOC17', 'SOC18', 'SOC19', 'SOC20', 'SOC21', 'SOC22', 'SOC23', 'SOC24', 'SOC25', 'SOC26', 'SOC27', 'SOC28', 'SOC29', 'SOC30', 'SOC31', 'SOC32', 'SOC33', 'SOC34', 'SOC35', 'SOC36', 'SOC37', 
 'SOC38', 'SOC39', 'SOC40', 'SOC41', 'SOC42', 'SOC43', 'SOC44', 'SOC45', 'SOC46', 'SOC47', 'SOC48', 'SOC49', 'SOC50', 'SOC51', 'SOC52', 'SOC53', 'SOC54', 'SOC55', 'SOC56', 'SOC57', 'SOC58', 'SOC59', 
 'SOC60', 'SOC61', 'SOC62', 'SOC63', 'SOC64', 'SOC65', 'SOC66', 'SOC67', 'SOC68', 'SOC69', 'SOC70', 'SOC71', 'SOC72', 'SOC73', 'SOC74', 'SOC75', 'SOC76', 'SOC77', 'SOC78', 'SOC79', 'SOC80', 'SOC81', 
 'SOC82', 'SOC83', 'SOC84', 'SOC85', 'SOC86', 'SOC87', 'SOC88', 'SOC89', 'SOC90', 'SOC91', 'SOC92', 'SOC93', 'SOC94', 'SOC95', 'SOC96', 'SOC97', 'SOC98', 'SOC99', 'SOC100', 'SOC101', 'SOC102', 'SOC103', 
 'SOC104', 'SOC105', 'SOC106', 'SOC107', 'SOC108', 'SOC109', 'SOC110', 'SOC111', 'SOC112', 'SOC113', 'SOC114', 'SOC115', 'SOC116', 'SOC117', 'SOC118', 'SOC119', 'SOC120', 'SOC121', 'SOC122', 'SOC123', 
 'SOC124', 'SOC125', 'SOC126', 'SOC127', 'SOC128', 'SOC129', 'SOC130', 'SOC131', 'SOC132', 'SOC133', 'SOC134', 'SOC135', 'SOC136', 'SOC137', 'SOC138', 'SOC139', 'SOC140', 'SOC141', 'SOC142', 'SOC143', 
 'SOC144', 'SOC145', 'SOC146', 'SOC147', 'SOC148', 'SOC149', 'SOC150', 'SOC151', 'SOC152', 'SOC153', 'SOC154', 'SOC155', 'SOC156', 'SOC157', 'SOC158', 'SOC159', 'SOC160', 'SOC161', 'SOC162', 'SOC163', 
 'SOC164', 'SOC165', 'SOC166', 'SOC167', 'SOC168', 'SOC169', 'SOC170', 'SOC171', 'SOC172', 'SOC173', 'SOC174', 'SOC175', 'SOC176', 'SOC177', 'SOC178', 'SOC179', 'SOC180', 'SOC181', 'SOC182', 'SOC183', 
 'SOC184', 'SOC185', 'SOC186', 'SOC187', 'SOC188', 'SOC189', 'SOC190', 'SOC191', 'SOC192', 'SOC193', 'SOC194', 'SOC195', 'SOC196', 'SOC197', 'SOC198', 'SOC199', 'SOC200', 'SOC201', 'SOC202', 'SOC203', 
 'SOC204', 'SOC205', 'SOC206', 'SOC207', 'SOC208', 'SOC209', 'SOC210', 'SOC211', 'SOC212', 'SOC213', 'SOC214', 'SOC215', 'SOC216', 'SOC217', 'SOC218', 'SOC219', 'SOC220', 'SOC221', 'SOC222', 'SOC223', 
 'SOC224', 'SOC225', 'SOC226', 'SOC227', 'SOC228', 'SOC229', 'SOC230', 'SOC231', 'SOC232', 'SOC233', 'SOC234', 'SOC235', 'SOC236', 'SOC237', 'SOC238', 'SOC239', 'SOC240', 'SOC241', 'SOC242', 'SOC243', 
 'SOC244', 'SOC245', 'SOC246', 'SOC247', 'SOC248', 'SOC249', 'SOC250', 'SOC251', 'SOC252', 'SOC253', 'SOC254', 'SOC255', 'SOC256', 'SOC257', 'SOC258', 'SOC259', 'SOC260', 'SOC261', 'SOC262', 'SOC263', 
 'SOC264', 'SOC265', 'SOC266', 'SOC267', 'SOC268', 'SOC269', 'SOC270', 'SOC271', 'SOC272', 'SOC273', 'SOC274', 'SOC275', 'SOC276', 'SOC277', 'SOC278', 'SOC279', 'SOC280', 'SOC281', 'SOC282', 'SOC283', 
 'SOC284', 'SOC285', 'SOC286', 'SOC287', 'SOC288', 'SOC289', 'SOC290', 'SOC291', 'SOC292', 'SOC293', 'SOC294', 'SOC295', 'SOC296', 'SOC297', 'SOC298', 'SOC299', 'SOC300', 'SOC301', 'SOC302', 'SOC303', 
 'SOC304', 'SOC305', 'SOC306', 'SOC307', 'SOC308', 'SOC309', 'SOC310', 'SOC311', 'SOC312', 'SOC313', 'SOC314', 'SOC315', 'SOC316', 'SOC317', 'SOC318', 'SOC319', 'SOC320', 'SOC321', 'SOC322', 'SOC323', 
 'SOC324', 'SOC325', 'SOC326', 'SOC327', 'SOC328', 'SOC329', 'SOC330', 'SOC331', 'SOC332', 'SOC333', 'SOC334', 'SOC335', 'SOC336', 'SOC337', 'SOC338', 'SOC339', 'SOC340', 'SOC341', 'SOC342', 'SOC343', 
 'SOC344', 'SOC345', 'SOC346', 'SOC347', 'SOC348', 'SOC349', 'SOC350', 'SOC351', 'SOC352', 'SOC353', 'SOC354', 'SOC355', 'SOC356', 'SOC357', 'SOC358', 'SOC359', 'SOC360', 'SOC361', 'SOC362', 'SOC363', 
 'SOC364', 'SOC365', 'SOC366', 'SOC367', 'SOC368', 'SOC369', 'SOC370', 'SOC371', 'SOC372', 'SOC373', 'SOC374', 'SOC375', 'SOC376', 'SOC377', 'SOC378', 'SOC379', 'SOC380', 'SOC381', 'SOC382', 'SOC383', 
 'SOC384', 'SOC385', 'SOC386', 'SOC387', 'SOC388', 'SOC389', 'SOC390', 'SOC391', 'SOC392', 'SOC393', 'SOC394', 'SOC395', 'SOC396', 'SOC397', 'SOC398', 'SOC399', 'SOC400', 'SOH1', 'SOH2', 'SOH3', 'SOH4', 
 'SOH5', 'SOH6', 'SOH7', 'SOH8', 'SOH9', 'SOH10', 'SOH11', 'SOH12', 'SOH13', 'SOH14', 'SOH15', 'SOH16', 'SOH17', 'SOH18', 'SOH19', 'SOH20', 'SOH21', 'SOH22', 'SOH23', 'SOH24', 'SOH25', 'SOH26', 'SOH27', 
 'SOH28', 'SOH29', 'SOH30', 'SOH31', 'SOH32', 'SOH33', 'SOH34', 'SOH35', 'SOH36', 'SOH37', 'SOH38', 'SOH39', 'SOH40', 'SOH41', 'SOH42', 'SOH43', 'SOH44', 'SOH45', 'SOH46', 'SOH47', 'SOH48', 'SOH49', 
 'SOH50', 'SOH51', 'SOH52', 'SOH53', 'SOH54', 'SOH55', 'SOH56', 'SOH57', 'SOH58', 'SOH59', 'SOH60', 'SOH61', 'SOH62', 'SOH63', 'SOH64', 'SOH65', 'SOH66', 'SOH67', 'SOH68', 'SOH69', 'SOH70', 'SOH71', 
 'SOH72', 'SOH73', 'SOH74', 'SOH75', 'SOH76', 'SOH77', 'SOH78', 'SOH79', 'SOH80', 'SOH81', 'SOH82', 'SOH83', 'SOH84', 'SOH85', 'SOH86', 'SOH87', 'SOH88', 'SOH89', 'SOH90', 'SOH91', 'SOH92', 'SOH93', 
 'SOH94', 'SOH95', 'SOH96', 'SOH97', 'SOH98', 'SOH99', 'SOH100', 'SOH101', 'SOH102', 'SOH103', 'SOH104', 'SOH105', 'SOH106', 'SOH107', 'SOH108', 'SOH109', 'SOH110', 'SOH111', 'SOH112', 'SOH113', 
 'SOH114', 'SOH115', 'SOH116', 'SOH117', 'SOH118', 'SOH119', 'SOH120', 'SOH121', 'SOH122', 'SOH123', 'SOH124', 'SOH125', 'SOH126', 'SOH127', 'SOH128', 'SOH129', 'SOH130', 'SOH131', 'SOH132', 'SOH133', 
 'SOH134', 'SOH135', 'SOH136', 'SOH137', 'SOH138', 'SOH139', 'SOH140', 'SOH141', 'SOH142', 'SOH143', 'SOH144', 'SOH145', 'SOH146', 'SOH147', 'SOH148', 'SOH149', 'SOH150', 'SOH151', 'SOH152', 'SOH153', 
 'SOH154', 'SOH155', 'SOH156', 'SOH157', 'SOH158', 'SOH159', 'SOH160', 'SOH161', 'SOH162', 'SOH163', 'SOH164', 'SOH165', 'SOH166', 'SOH167', 'SOH168', 'SOH169', 'SOH170', 'SOH171', 'SOH172', 'SOH173', 
 'SOH174', 'SOH175', 'SOH176', 'SOH177', 'SOH178', 'SOH179', 'SOH180', 'SOH181', 'SOH182', 'SOH183', 'SOH184', 'SOH185', 'SOH186', 'SOH187', 'SOH188', 'SOH189', 'SOH190', 'SOH191', 'SOH192', 'SOH193', 
 'SOH194', 'SOH195', 'SOH196', 'SOH197', 'SOH198', 'SOH199', 'SOH200', 'SOH201', 'SOH202', 'SOH203', 'SOH204', 'SOH205', 'SOH206', 'SOH207', 'SOH208', 'SOH209', 'SOH210', 'SOH211', 'SOH212', 'SOH213', 
 'SOH214', 'SOH215', 'SOH216', 'SOH217', 'SOH218', 'SOH219', 'SOH220', 'SOH221', 'SOH222', 'SOH223', 'SOH224', 'SOH225', 'SOH226', 'SOH227', 'SOH228', 'SOH229', 'SOH230', 'SOH231', 'SOH232', 'SOH233', 
 'SOH234', 'SOH235', 'SOH236', 'SOH237', 'SOH238', 'SOH239', 'SOH240', 'SOH241', 'SOH242', 'SOH243', 'SOH244', 'SOH245', 'SOH246', 'SOH247', 'SOH248', 'SOH249', 'SOH250', 'SOH251', 'SOH252', 'SOH253', 
 'SOH254', 'SOH255', 'SOH256', 'SOH257', 'SOH258', 'SOH259', 'SOH260', 'SOH261', 'SOH262', 'SOH263', 'SOH264', 'SOH265', 'SOH266', 'SOH267', 'SOH268', 'SOH269', 'SOH270', 'SOH271', 'SOH272', 'SOH273', 
 'SOH274', 'SOH275', 'SOH276', 'SOH277', 'SOH278', 'SOH279', 'SOH280', 'SOH281', 'SOH282', 'SOH283', 'SOH284', 'SOH285', 'SOH286', 'SOH287', 'SOH288', 'SOH289', 'SOH290', 'SOH291', 'SOH292', 'SOH293', 
 'SOH294', 'SOH295', 'SOH296', 'SOH297', 'SOH298', 'SOH299', 'SOH300', 'SOH301', 'SOH302', 'SOH303', 'SOH304', 'SOH305', 'SOH306', 'SOH307', 'SOH308', 'SOH309', 'SOH310', 'SOH311', 'SOH312', 'SOH313', 
 'SOH314', 'SOH315', 'SOH316', 'SOH317', 'SOH318', 'SOH319', 'SOH320', 'SOH321', 'SOH322', 'SOH323', 'SOH324', 'SOH325', 'SOH326', 'SOH327', 'SOH328', 'SOH329', 'SOH330', 'SOH331', 'SOH332', 'SOH333', 
 'SOH334', 'SOH335', 'SOH336', 'SOH337', 'SOH338', 'SOH339', 'SOH340', 'SOH341', 'SOH342', 'SOH343', 'SOH344', 'SOH345', 'SOH346', 'SOH347', 'SOH348', 'SOH349', 'SOH350', 'SOH351', 'SOH352', 'SOH353', 
 'SOH354', 'SOH355', 'SOH356', 'SOH357', 'SOH358', 'SOH359', 'SOH360', 'SOH361', 'SOH362', 'SOH363', 'SOH364', 'SOH365', 'SOH366', 'SOH367', 'SOH368', 'SOH369', 'SOH370', 'SOH371', 'SOH372', 'SOH373', 
 'SOH374', 'SOH375', 'SOH376', 'SOH377', 'SOH378', 'SOH379', 'SOH380', 'SOH381', 'SOH382', 'SOH383', 'SOH384', 'SOH385', 'SOH386', 'SOH387', 'SOH388', 'SOH389', 'SOH390', 'SOH391', 'SOH392', 'SOH393', 
 'SOH394', 'SOH395', 'SOH396', 'SOH397', 'SOH398', 'SOH399', 'SOH400']

file_path = '/home/<USER>/csvfiles'
class FileHandleIntetface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        try:
            if kt == 'ReadCSVFile':  # 适用于单个文件，且文件中的时间是从每天的凌晨零分开始统计
                fileName = self.get_argument('file_name',None)  # 文件名
                titles = self.get_argument('titles',[])  # 标题集合
                startTime = self.get_argument('start_time',None)  # 起始时间，只有时分秒
                endTime = self.get_argument('end_time',None)  # 截止时间，只有时分秒
                if not fileName or not titles or not startTime or not endTime:
                    return self.customError('请上携带完整参数')
                if not os.path.exists(file_path+'/'+fileName):
                    return self.customError('文件不存在')
                titles = eval(str(titles))
                # 时分秒转秒
                startTime = timeUtils.HMSTSec(startTime)
                endTime = timeUtils.HMSTSec(endTime)
                # 计算开始和截止行，切片使用
                start = math.floor(startTime/5)
                end = math.floor(endTime/5)
                usecols = [0]
                for t in titles:
                    usecols.append(all_title.index(t))
                obj = self._readcsv(fileName,usecols,start,end)
                return self.returnTypeSuc(obj)
            elif kt == 'GetFileList':  # 查询所有列表
                filelist = get_file_list(file_path)
                return self.returnTypeSuc(filelist)
            else:
                return self.pathError()
        except ValueError as E:
            return self.customError(str(E).encode('utf8'))
        except Exception as E:
            logging.info(E)
            return self.requestError()
    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        try:
            if kt == 'UploadCSVFile':
                self._receivecsv()
                return self.returnTypeSuc('')
            elif kt == 'UploadCSVAndReadFile':  # 上传并获取文件
                path,filename = self._receivecsv()  # 获取文件全路径和文件名
                titles = self.get_argument('titles',[])  # 标题集合
                startTime = self.get_argument('start_time',None)  # 起始时间，只有时分秒
                endTime = self.get_argument('end_time',None)  # 截止时间，只有时分秒
                if not titles or not startTime or not endTime:
                    return self.customError('请上携带完整参数')
                if not os.path.exists(path):
                    return self.customError('文件不存在')
                titles = eval(str(titles))
                # 时分秒转秒
                startTime = timeUtils.HMSTSec(startTime)
                endTime = timeUtils.HMSTSec(endTime)
                # 计算开始和截止行，切片使用
                start = math.floor(startTime/5)
                end = math.floor(endTime/5)
                usecols = [0]
                for t in titles:
                    usecols.append(all_title.index(t))
                obj = self._readcsv(filename,usecols,start,end)
                return self.returnTypeSuc(obj)
            else:
                return self.pathError()
        except Exception as E:
            logging.info(E)
            return self.requestError()

    def _readcsv(self,fileName,usecols,start,end):
        '''读取csv'''
        df = pd.read_csv('%s/%s'%(file_path,fileName),sep=',',header=0,usecols=usecols,encoding='gbk')
        cond = df.loc[start:end]
        obj = {}
        for c in cond:
            all = []
            for s in cond[c]:
                s = str(s)
                if ':' in s:
                    all.append(s[11:])
                else:
                    all.append(num_retain(s,3))
            obj[c] = all
        return obj

    def _receivecsv(self):
        '''接收上传的csv文件'''
        files = self.request.files
        if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
            os.makedirs(file_path)
        imgs = files.get('file')
        data = imgs[0].get('body')
        filename = imgs[0].get('filename')
        if not filename.endswith('.gz'):
            return self.customError('请上传gz格式的压缩文件')
        # now_time = timeUtils.todaySecs()
        path = '%s/%s' % (file_path,filename)
        file = open(path, 'wb')
        file.write(data)
        file.close()
        # os.system('cd %s && unzip %s '%(path_b,path.split('/')[-1]))
        # # 删除指定压缩文件
        # os.system('rm -f %s'%(path))
        un_gz(path)
        return path.replace(".gz", ""),filename.replace(".gz", "")

def un_gz(file_name):
    """ungz zip file"""
    f_name = file_name.replace(".gz", "")
    #获取文件的名称，去掉
    g_file = gzip.GzipFile(file_name)
    #创建gzip对象
    open(f_name, "wb+").write(g_file.read())
    #gzip对象用read()打开后，写入open()建立的文件里。
    g_file.close() #关闭gzip对象
    os.system('rm -f %s'%(file_name))
    # 超过五个文件就删除
    l = get_file_list(file_path) # 所有文件列表
    if len(l)>5:
        os.system('rm -f %s/%s'%(file_path,l[0]))

def get_file_list(file_path):
    dir_list = os.listdir(file_path)
    if not dir_list:
        return
    else:
        # 注意，这里使用lambda表达式，将文件按照最后修改时间顺序升序排列
        # os.path.getmtime() 函数是获取文件最后修改时间
        # os.path.getctime() 函数是获取文件最后创建时间
        dir_list = sorted(dir_list,key=lambda x: os.path.getmtime(os.path.join(file_path, x)))
        # print(dir_list)
        return dir_list