#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-09-21 16:30:08
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_useele_ele_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 20:02:26


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseUseEleEleInfo(user_Base):
    u'用电信息表--电力情况'
    __tablename__ = "t_side_forecase_useele_ele_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    power_plan_f = Column(CHAR(1), nullable=True, comment=u"是否有自备电厂",server_default='0')
    power_cap = Column(String(256), nullable=True, comment=u"已有电厂容量")
    plan_cap = Column(String(256), nullable=True, comment=u"计划电厂容量")
    cpv_f = Column(CHAR(1), nullable=True, comment=u"是否有光伏接入",server_default='0')
    cpv_cap = Column(String(256), nullable=True, comment=u"已有光伏容量")
    plan_cpv_cap = Column(String(256), nullable=True, comment=u"计划光伏容量")
    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    project_ele_info = relationship("ForecaseProject", backref="project_ele_info")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean =  "{'id':%s,'power_plan_f':%s,'power_cap':'%s','plan_cap':'%s','cpv_f':%s,'cpv_cap':'%s','plan_cpv_cap':'%s','op_ts':'%s'}" %(
            self.id,self.power_plan_f,self.power_cap,self.plan_cap,self.cpv_f,self.cpv_cap,self.plan_cpv_cap,self.op_ts)
        return bean.replace('None',"")

   