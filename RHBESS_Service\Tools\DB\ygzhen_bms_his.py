# -*- coding: utf-8 -*-
#!/usr/bin/env python
# coding=utf-8
#@Information:永臻电芯数据
#<AUTHOR> WYJ
#@Date         : 2023-02-22 14:30:24
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\ygzhen_bms_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-22 17:47:24


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")


YGZHEN_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
YGZHEN_PORT = model_config.get('mysql', "HIS_PORT")
YGZHEN_DATABASE1 = model_config.get('mysql', "YGZHEN_DATABASE1")
YGZHEN_DATABASE2 = model_config.get('mysql', "YGZHEN_DATABASE2")
YGZHEN_USERNAME = model_config.get('mysql', "HIS_USERNAME")
YGZHEN_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")

hisdb1='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGZHEN_USERNAME,
    YGZHEN_PASSWORD,
    YGZHEN_HOSTNAME,
    YGZHEN_PORT,
    YGZHEN_DATABASE1
)
ygzhen_engine1 = create_engine(hisdb1,echo=False,max_overflow=5, pool_size=30, pool_timeout=6, pool_pre_ping=True,pool_recycle=1800)
_ygzhen_session1 = scoped_session(sessionmaker(ygzhen_engine1,autoflush=True))
ygzhen_Base1 = declarative_base(ygzhen_engine1)
ygzhen_session1 = _ygzhen_session1()

hisdb2='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGZHEN_USERNAME,
    YGZHEN_PASSWORD,
    YGZHEN_HOSTNAME,
    YGZHEN_PORT,
    YGZHEN_DATABASE2
)
ygzhen_engine2 = create_engine(hisdb2,echo=False,max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygzhen_session2 = scoped_session(sessionmaker(ygzhen_engine2,autoflush=True))
ygzhen_Base2 = declarative_base(ygzhen_engine2)
ygzhen_session2 = _ygzhen_session2()


