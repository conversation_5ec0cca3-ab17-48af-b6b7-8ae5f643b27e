# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/22 下午4:58
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : views.py
# @Software : PyCharm
import datetime
import functools
import re
import traceback
import concurrent.futures

from django.forms import model_to_dict
from rest_framework.response import Response
from rest_framework.views import APIView

from TianLuAppBackend.settings import SUCCESS_LOG
from apis.app2 import error_log
from apis.user.models import Project, MaterStation, MessageCenter, StationDetails, \
    UserDetails, Unit, BatteryAnalysisRecord
from apis.web2.battery_analysis import serializers
from common import common_response_code
from common.database_pools import ads_db_tool, dwd_tables, dwd_db_tool
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from settings.anslysis_zh_en_mapping import ANALYSIS_ZH_EN_MAP, SEARCH_KEYS
from settings.types_dict import BATTERY_ANALYSIS_TYPES_DICT
from tools.hour_setting import create_time_mapping_2, create_time_mapping_3

logger = SUCCESS_LOG


class AnalysisListView(APIView):
    """分析列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def deal_item_data(self, stations, ins, lang='zh'):
        en_name = ins['station_name']
        station_ins = stations.filter(english_name=en_name).first()
        ins['station_name'] = station_ins.master_station.name
        ins['en_name'] = en_name

        unit_ins = station_ins.unit_set.filter(bms=ins['unit'], is_delete=0).first()
        ins['unit_name'] = unit_ins.unit_new_name.replace(' ', '') if unit_ins else ''

        ins['battery_nums'] = ','.join(eval(ins['battery_nums']))
        ins['battery_name'] = ','.join(eval(ins['battery_name'])) if ins['battery_name'] else ''

        if ins['case_type'] == '电池温度分析':
            ins['date_time'] = f"{ins['start_time'].strftime('%Y-%m-%d %H:%M:%S')}" + '~' + (
                f"{ins['end_time'].strftime('%Y-%m-%d %H:%M:%S')}" if ins['end_time'] else '')

            ins['battery_count'] = '--'

        record_id = ins['record_id']
        res = BatteryAnalysisRecord.objects.filter(related_id=int(record_id)).order_by('-create_time').first()

        ins['is_assessed'] = True if res else False

        if lang == 'zh':
            unit_ins = station_ins.unit_set.filter(bms=ins['unit']).first()
            ins['unit_name'] = unit_ins.unit_new_name.replace(' ', '')

        else:
            unit_ins = station_ins.unit_set.filter(bms=ins['unit']).first()
            ins['unit_name'] = unit_ins.en_unit_new_name.replace(' ', '')

            ins['level'] = ANALYSIS_ZH_EN_MAP[ins['level'].strip()][lang]
            ins['case_type'] = ANALYSIS_ZH_EN_MAP[ins['case_type'].strip()][lang]
            ins['case_content'] = ANALYSIS_ZH_EN_MAP[ins['case_content'].strip().replace('\n', '')][lang]
            ins['hand_info'] = ANALYSIS_ZH_EN_MAP[ins['hand_info'].strip().replace('\n', '')][lang]

        return ins

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get('lang', 'zh')

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user = UserDetails.objects.get(id=request.user.get('user_id'))
        ser = serializers.AnalysisSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"运行分析:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("运行分析：字段校验不通过：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": e.args[0]}})

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=7)

        case_content = ser.validated_data.get('case_content')
        case_type = ser.validated_data.get('case_type')
        battery_num = ser.validated_data.get('battery_num')
        level = ser.validated_data.get('level')
        start_time = ser.validated_data.get('start_time', None)
        end_time = ser.validated_data.get('end_time', None)
        station_id = ser.validated_data.get('station_id')
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 1000000000))

        offset = (int(page) - 1) * int(page_size)
        limit = page_size

        try:
            if station_id:
                stations_id_list = station_id.split(',')
                master_stations = MaterStation.objects.filter(id__in=stations_id_list, project__user=user, is_delete=0).all()
            else:
                projects = Project.objects.filter(is_used=1, user=user).all()
                # master_stations = MaterStation.objects.filter(project__in=projects).all()
                master_stations = MaterStation.objects.filter(project__in=projects, is_delete=0).all()
        except Exception as e:
            error_log.error("运行分析：并网点不存在：{}".format(e))
            print(traceback.print_exc())
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error",
                                                                    "detail": f"并网点: <{station_id}>不存在" if lang == 'zh' else 'The station does not exist.'}})

        stations = StationDetails.objects.filter(is_delete=0, master_station__in=master_stations).all()
        stations_en_names = [i.english_name for i in stations]

        # args = [stations_en_names]

        a_sql = f"select count(1) as count from ads_record_bat_anal_1d where is_delete=0 and station_name in %s"

        sql = f"""
            SELECT
                record_id, date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count, start_time, end_time 
            FROM
                `ads_record_bat_anal_1d`
            WHERE
                is_delete = 0 AND
                station_name IN %s
        """

        if start_time and end_time:
            start_time_ = start_time.strftime('%Y-%m-%d')
            end_time_ = end_time.strftime('%Y-%m-%d')
            sql += f" AND date_time BETWEEN '{start_time_}' AND '{end_time_}'"
            a_sql += f" AND date_time BETWEEN '{start_time_}' AND '{end_time_}'"

        if case_content:
            if lang == 'zh':
                sql += f" AND case_content like '%%{case_content}%%'"
                a_sql += f" AND case_content like '%%{case_content}%%'"
            else:
                keyword = case_content
                for k,v in ANALYSIS_ZH_EN_MAP.items():
                    if case_content in v[lang]:
                        keyword = k
                        break
                sql += f" AND case_content like '%%{keyword}%%'"
                a_sql += f" AND case_content like '%%{keyword}%%'"

        if case_type:
            sql += f" AND case_type = '{SEARCH_KEYS[case_type]}'"
            a_sql += f" AND case_type = '{SEARCH_KEYS[case_type]}'"

        if level:
            sql += f" AND level = '{SEARCH_KEYS[level]}'"
            a_sql += f" AND level = '{SEARCH_KEYS[level]}'"

        if battery_num:
            # sql += f" AND battery_nums LIKE '%%{battery_num}%%'"
            sql += f" AND array_count(x->x like '%%{battery_num}%%', battery_nums) >0"
            a_sql += f" AND array_count(x->x like '%%{battery_num}%%', battery_nums) >0"

        sql += f" ORDER BY start_time DESC LIMIT {offset}, {limit}"

        names = tuple(stations_en_names)
        total_analysis_instances = ads_db_tool.select_many(sql, names)

        total_count = ads_db_tool.select_one(a_sql, names)['count']
        total_pages = int(total_count / page_size) + 1 if total_count % page_size else int(total_count / page_size)

        temp_list = []
        if len(total_analysis_instances):
            with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
                futures = list()
                for item in total_analysis_instances:
                    future = executor.submit(self.deal_item_data, stations, item, lang)
                    futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    ins = future.result()
                    temp_list.append(ins)

        # 分页
        # paginator = Paginator(list(temp_list), page_size)
        # tem_analysis_instances = paginator.get_page(page).object_list

        paginator_info = {
            "page": page,
            "page_size": page_size,
            "pages": total_pages,
            "total_count": total_count
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": sorted(list(temp_list), key=lambda x: x['start_time'], reverse=True),
                    "paginator_info": paginator_info,
                    # "stations_options": stations_options
                },
            }
        )


class AnalysisTypesView(APIView):
    """
    获取分析的报文类型
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": BATTERY_ANALYSIS_TYPES_DICT[lang].values()
            },
        })


class AnalysisMessageView(APIView):
    """
    运行分析：推送至消息中心
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        record_id = request.data.get("record_id")

        sql = """SELECT
            date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count, start_time, end_time
        FROM
            `ads_record_bat_anal_1d`
        WHERE
            record_id = %s AND
            is_delete = 0 limit 1"""

        res = ads_db_tool.select_one(sql, int(record_id))
        if not res:
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail":
                            "分析记录不存在" if lang == 'zh' else 'Analysis record does not exist.'}})

        receivers = [69, 76, 88, 120, 182, request.user['user_id']]

        if res['case_type'] == '电池电压分析':

            battery_count = res['battery_count']

            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            analysis_title = (f"{station.master_station.name}项目{res['date_time'].strftime('%m月%d日')}{battery_count}"
                              f"个电池出现{res['case_content']}报文，建议{res['hand_info']}。")
            en_analysis_title = (f"The project at {station.master_station.name} encountered a message on {res['date_time'].strftime('%m/%d')}"
                                 f" indicating that {battery_count} {'batteries are' if battery_count > 1 else 'battery is'} experiencing "
                                 + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + ".")

            for receiver in list(set(receivers)):
                # 首先查询是否推送过，已推送则跳过====>不跳过
                # if MessageCenter.objects.filter(related_id=int(record_id), user_id=receiver).exists():
                #     error_log.error(f"电池电压分析推送至消息中心: {receiver}已推送过, 不再推送")
                #     continue

                ins = MessageCenter.objects.create(
                    title=analysis_title,
                    en_title=en_analysis_title,
                    type=3,
                    is_read=0,
                    is_verify=0,
                    user_id=receiver,
                    related_id=int(record_id)
                )

        elif res['case_type'] == '电池温度分析':
            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            unit = Unit.objects.filter(is_delete=0, station=station, bms=res['unit']).first()

            # 有结束时间：构成为——“并网点名称”+项目+“储能单元”+在+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
            if res['end_time']:
                analysis_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}在{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}-{res['end_time'].strftime('%Y/%m/%d/ %H:%M:%S')}出现{res['case_content']}报文，建议{res['hand_info']}。"
                en_analysis_title = f"The project at {station.master_station.name} encountered a message on {res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}-{res['end_time'].strftime('%Y/%m/%d/ %H:%M:%S')} indicating that " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + f". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + "."

            # 没有结束时间：构成为——“并网点名称”+项目+“储能单元”+从+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
            else:
                analysis_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}从{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}开始出现{res['case_content']}报文，建议{res['hand_info']}。"
                en_analysis_title = f"The project at {station.master_station.name} encountered a message on {res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')} indicating that " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + f". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + "."

            for receiver in list(set(receivers)):
                # 首先查询是否推送过，已推送则跳过===> 不跳过了
                # if MessageCenter.objects.filter(related_id=int(record_id), user_id=receiver).exists():
                #     error_log.error(f"电池温度分析推送至消息中心: {receiver}已推送过, 不再推送")
                #     continue

                ins = MessageCenter.objects.create(
                    title=analysis_title,
                    en_title=en_analysis_title,
                    type=4,
                    is_read=0,
                    is_verify=0,
                    user_id=receiver,
                    related_id=int(record_id)
                )

        elif res['case_type'] == '电池过放风险':

            battery_count = res['battery_count']

            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            analysis_title = (f"{station.master_station.name}项目{res['date_time'].strftime('%m月%d日')}{battery_count}"
                              f"个电池出现{res['case_content']}报文，建议{res['hand_info']}。")
            en_analysis_title = (f"The project at {station.master_station.name} encountered a message on {res['date_time'].strftime('%m/%d')}"
                                 f" indicating that {battery_count} {'batteries are' if battery_count > 1 else 'battery is'} experiencing "
                                 + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + ".")

            for receiver in list(set(receivers)):
                # 首先查询是否推送过，已推送则跳过====>不跳过
                # if MessageCenter.objects.filter(related_id=int(record_id), user_id=receiver).exists():
                #     error_log.error(f"电池电压分析推送至消息中心: {receiver}已推送过, 不再推送")
                #     continue

                ins = MessageCenter.objects.create(
                    title=analysis_title,
                    en_title=en_analysis_title,
                    type=5,
                    is_read=0,
                    is_verify=0,
                    user_id=receiver,
                    related_id=int(record_id)
                )

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": f'电池电压/温度分析/电池过放推送至消息中心: 推送成功.' if lang == 'zh' else
                'Battery voltage/temperature/Over release analysis push to message center: Push successfully.'
                }
            }
        )

    def get(self, request):
        """
        查看消息：运行分析详情
        """""
        lang = request.headers.get("lang", 'zh')
        message_id = request.query_params.get("message_id")

        try:
            message = MessageCenter.objects.get(id=message_id)
        except Exception as e:
            error_log.error("消息中心:运行分析消息不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail":
                 "消息中心:电池电压分析消息不存在" if lang == 'zh' else 'Message center:Battery voltage analysis message does not exist.'}})

        if message.type == 3:
            sql = """SELECT
                        date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count
                    FROM
                        `ads_record_bat_anal_1d`
                    WHERE
                        record_id = %s AND
                        is_delete = 0 limit 1"""

            res = ads_db_tool.select_one(sql, message.related_id)
            if not res:
                return Response({"code": common_response_code.FIELD_ERROR,
                                 "data": {"message": "error", "detail": "分析记录不存在" if lang == 'zh' else 'Analysis record does not exist.'}})

            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            unit = station.unit_set.filter(bms=res['unit'], is_delete=0).first()
            battery_count = res['battery_count']
            battery_names = eval(res['battery_name'])

            if lang == 'zh':
                title = f"{station.master_station.name}项目{res['date_time'].strftime('%m月%d日')}{battery_count}个电池出现{res['case_content']}报文，建议{res['hand_info']}。"
            else:
                title = f"The project at {station.master_station.name} encountered a message on {res['date_time'].strftime('%m/%d')} indicating that {battery_count} {'batteries are' if battery_count > 1 else 'battery is'} experiencing " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + "."
            description = message.title if lang == 'zh' else message.en_title
            receive_message_time = message.create_time.strftime("%Y-%m-%d %H:%M:%S")

            return_dict = {
                "title": title,
                "station": station.english_name,
                "description": description,
                "receive_message_time": receive_message_time,
                "table": {
                            "station": station.master_station.name,
                            "unit": unit.unit_new_name if lang == 'zh' else unit.en_unit_new_name,
                            "date_time": res['date_time'],
                            "case_type": ANALYSIS_ZH_EN_MAP[res['case_type']][lang],
                            "case_content": ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')][lang],
                            "battery_nums": res['battery_nums'],
                            "battery_count": res['battery_count'],
                            "level": ANALYSIS_ZH_EN_MAP[res['level']][lang],
                            "hand_info": ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', "")][lang]
                }
            }

            chart_dict = get_battery_data_2(station, unit, battery_names, res['date_time'], res['case_type'])

            return_dict['chart'] = chart_dict

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict
                },
            })

        elif message.type == 4:
            sql = """SELECT
                            date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count, start_time, end_time
                        FROM
                            `ads_record_bat_anal_1d`
                        WHERE
                            record_id = %s AND
                            is_delete = 0 limit 1"""

            res = ads_db_tool.select_one(sql, message.related_id)
            if not res:
                return Response({"code": common_response_code.FIELD_ERROR,
                                 "data": {"message": "error", "detail": "分析记录不存在" if lang == 'zh' else 'Analysis record does not exist.'}})

            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            unit = station.unit_set.filter(bms=res['unit'], is_delete=0).first()
            battery_count = res['battery_count']
            battery_names = eval(res['battery_name']) if res['battery_name'] else []

            if lang == 'zh':
                # 有结束时间：构成为——“并网点名称”+项目+“储能单元”+在+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
                if res['end_time']:
                    analysis_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}在{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}-{res['end_time'].strftime('%Y/%m/%d/ %H:%M:%S')}出现{res['case_content']}报文，建议{res['hand_info']}。"
                    chart_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}在{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}-{res['end_time'].strftime('%Y/%m/%d/ %H:%M:%S')} \n 最高单体温度----TMS故障码/故障等级折线图"
                # 没有结束时间：构成为——“并网点名称”+项目+“储能单元”+从+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
                else:
                    analysis_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}从{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}开始出现{res['case_content']}报文，建议{res['hand_info']}。"
                    chart_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}从{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')} \n 最高单体温度----TMS故障码/故障等级折线图"
                description = message.title

            else:
                # 有结束时间：构成为——“并网点名称”+项目+“储能单元”+在+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
                if res['end_time']:
                    analysis_title = f"The project at {station.master_station.name} encountered a message on {res['start_time'].strftime('%m/%d')}-{res['end_time'].strftime('%m/%d')} indicating that " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', "")]['en'] + ". "
                    chart_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}在{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')}-{res['end_time'].strftime('%Y/%m/%d/ %H:%M:%S')} \n 最高单体温度----TMS故障码/故障等级折线图"
                # 没有结束时间：构成为——“并网点名称”+项目+“储能单元”+从+“日期（提示时间范围）”+出现+“推送的电池温度分析记录的报文内容”+报文+建议+“建议内容”
                else:
                    analysis_title = f"The project at {station.master_station.name} encountered a message on {res['start_time'].strftime('%m/%d')} indicating that " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', "")]['en'] + ". "
                    chart_title = f"{station.master_station.name}项目{unit.unit_new_name.replace(' ', '')}从{res['start_time'].strftime('%Y/%m/%d/ %H:%M:%S')} \n 最高单体温度----TMS故障码/故障等级折线图"
                description = message.en_title

            receive_message_time = message.create_time.strftime("%Y-%m-%d %H:%M:%S")

            return_dict = {
                "title": analysis_title,
                "station": station.english_name,
                "description": description,
                "receive_message_time": receive_message_time,
                "table": {
                    "station": station.master_station.name,
                    "unit": unit.unit_new_name if lang == 'zh' else unit.en_unit_new_name,
                    "date_time": f"{res['start_time'].strftime('%Y/%m/%d %H:%M:%S')}" + '~' + f"{res['end_time'].strftime('%Y/%m/%d %H:%M:%S')}" if res['end_time'] else '',
                    "case_type": ANALYSIS_ZH_EN_MAP[res['case_type']][lang],
                    "case_content": ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')][lang],
                    "battery_nums": res['battery_nums'],
                    "battery_count": '--',
                    "level": ANALYSIS_ZH_EN_MAP[res['level']][lang],
                    "hand_info": ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')][lang]
                }
            }

            date_time = res['date_time']
            start_time = res['start_time']

            # 提示日期范围（如果没有结束时间显示开始时间至当前）、提示时间内的最高单体温度和对应储能单元的TMS故障码和故障等级
            end_time = res['end_time'] if res['end_time'] else datetime.datetime.now()

            chart_dict = get_battery_tem_data(station, unit, date_time, start_time, end_time)

            return_dict['chart'] = chart_dict
            return_dict['chart']['title'] = chart_title

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict
                },
            })

        elif message.type == 5:
            sql = """SELECT
                        date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count
                    FROM
                        `ads_record_bat_anal_1d`
                    WHERE
                        record_id = %s AND
                        is_delete = 0 limit 1"""

            res = ads_db_tool.select_one(sql, message.related_id)
            if not res:
                return Response({"code": common_response_code.FIELD_ERROR,
                                 "data": {"message": "error", "detail": "分析记录不存在" if lang == 'zh' else 'Analysis record does not exist.'}})

            station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
            unit = station.unit_set.filter(bms=res['unit'], is_delete=0).first()
            battery_count = res['battery_count']
            battery_names = eval(res['battery_name'])

            if lang == 'zh':
                title = f"{station.master_station.name}项目{res['date_time'].strftime('%m月%d日')}{battery_count}个电池出现{res['case_content']}报文，建议{res['hand_info']}。"
            else:
                title = f"The project at {station.master_station.name} encountered a message on {res['date_time'].strftime('%m/%d')} indicating that {battery_count} {'batteries are' if battery_count > 1 else 'battery is'} experiencing " + ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')]['en'] + ". It is recommended to " + ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', '')]['en'] + "."
            description = message.title if lang == 'zh' else message.en_title
            receive_message_time = message.create_time.strftime("%Y-%m-%d %H:%M:%S")

            return_dict = {
                "title": title,
                "station": station.english_name,
                "description": description,
                "receive_message_time": receive_message_time,
                "table": {
                            "station": station.master_station.name,
                            "unit": unit.unit_new_name if lang == 'zh' else unit.en_unit_new_name,
                            "date_time": res['date_time'],
                            "case_type": ANALYSIS_ZH_EN_MAP[res['case_type']][lang],
                            "case_content": ANALYSIS_ZH_EN_MAP[res['case_content'].replace('\n', '')][lang],
                            "battery_nums": res['battery_nums'],
                            "battery_count": res['battery_count'],
                            "level": ANALYSIS_ZH_EN_MAP[res['level']][lang],
                            "hand_info": ANALYSIS_ZH_EN_MAP[res['hand_info'].replace('\n', "")][lang]
                }
            }

            chart_dict = get_battery_data_2(station, unit, battery_names, res['date_time'], res['case_type'])

            return_dict['chart'] = chart_dict

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict
                },
            })

        else:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "消息中心:该消息非电池分析消息，无电池分析信息" if lang == 'zh' else
                         'Message center: This message is not a battery analysis message,'
                         ' no battery analysis information.'}
            })


@functools.lru_cache(maxsize=None)
def select_battery_v_data(station_name, device, battery_names, date_time):
    """
    查询各个电池的单体电压
    """""
    table_name1 = dwd_tables['measure']['bms_1']
    select_sql_1 = f"""SELECT time, {battery_names}
                        FROM {table_name1} where station_name=%s and device=%s and time >= %s and time <= %s and MOD(MINUTE(time), 5) = 0  
                        ORDER BY time ASC;"""

    results_2 = dwd_db_tool.select_many(select_sql_1, *(station_name, device, f'{date_time} 00:00:00', f'{date_time} 23:59:59'))

    res = {}

    if results_2:
        for result_2 in results_2:
            if result_2['time']:
                if result_2['time'].minute % 5 != 0:
                    time_ = datetime.datetime.fromtimestamp(
                        result_2['time'].replace(second=0, microsecond=0).timestamp() - result_2[
                            'time'].timestamp() % 300).strftime("%H:%M")
                else:
                    time_ = result_2['time'].replace(second=0, microsecond=0).strftime("%H:%M")

                res[time_] = result_2

    return res


@functools.lru_cache(maxsize=None)
def get_unit_soc_data(station_name, device, date_time):
    """
    查询储能单元soc
    """""
    table_name = dwd_tables['measure']['bms_3']
    select_sql_2 = f"""SELECT time, soc, aveu
                        FROM {table_name} where station_name=%s and device=%s and time >= %s and time <= %s and MOD(MINUTE(time), 5) = 0 
                        ORDER BY time ASC;"""

    results_1 = dwd_db_tool.select_many(select_sql_2, *(station_name, device, f'{date_time} 00:00:00', f'{date_time} 23:59:59'))
    res = {}

    if results_1:
        for result_1 in results_1:
            if result_1['time']:
                if result_1['time'].minute % 5 != 0:
                    time_ = datetime.datetime.fromtimestamp(
                        result_1['time'].replace(second=0, microsecond=0).timestamp() - result_1[
                            'time'].timestamp() % 300).strftime("%H:%M")
                else:
                    time_ = result_1['time'].replace(second=0, microsecond=0).strftime("%H:%M")

                res[time_] = result_1

    return res


@functools.lru_cache(maxsize=None)
def get_unit_vol_min_data(station_name, device, battery_names, date_time):
    """
    查询储能单元最低电压
    """""
    table_name = dwd_tables['measure']['bms_3']
    select_sql = f"""SELECT time, mumin, muminn
                        FROM {table_name} where station_name=%s and device=%s and time >= %s and time <= %s and MOD(MINUTE(time), 5) = 0 
                        ORDER BY time ASC;"""

    results = dwd_db_tool.select_many(select_sql, *(station_name, device, f'{date_time} 00:00:00', f'{date_time} 23:59:59'))
    res = {}

    if results:
        for result_2 in results:
            if result_2['time']:
                if result_2['time'].minute % 5 != 0:
                    time_ = datetime.datetime.fromtimestamp(
                        result_2['time'].replace(second=0, microsecond=0).timestamp() - result_2[
                            'time'].timestamp() % 300).strftime("%H:%M")
                else:
                    time_ = result_2['time'].replace(second=0, microsecond=0).strftime("%H:%M")

                res[time_] = result_2


    return res

def get_battery_data_2(station, unit, battery_names, date_time, case_type):
    """
    查看指定电池分析的指定一天的SOC和单体电压，和平均电压或最低电压
    """""
    station_name = station.english_name
    device = unit.bms
    return_dict = {}

    moments = create_time_mapping_2(m=5)

    try:
        if case_type == '电池过放风险':
            # 使用多线程和缓存优化查询速度
            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                future = executor.submit(get_unit_vol_min_data, station_name, device, ','.join(battery_names),
                                           date_time)
                ress1 = future.result()
            for k, v in moments.items():
                if k in ress1.keys():
                    moments[k]['vol'] = ress1[k]['mumin']
                    moments[k]['number'] = ress1[k]['muminn']
                    # for b in battery_names:
                        # v.update({b: ress1[k].get(b)})

                else:
                    moments[k]['vol'] = '--'
                    moments[k]['number'] = '--'
            return_dict['vol'] = [i.get('vol') for i in moments.values()]
            return_dict['number'] = [i.get('number') for i in moments.values()]
            return_dict['datetime'] = moments.keys()
        else:
            # 使用多线程和缓存优化查询速度
            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                future_1 = executor.submit(select_battery_v_data, station_name, device, ','.join(battery_names), date_time)
                future_2 = executor.submit(get_unit_soc_data, station_name, device, date_time)

                ress1 = future_1.result()
                ress2 = future_2.result()

            for k, v in moments.items():
                if k in ress1.keys():
                    for b in battery_names:
                        v.update({b: ress1[k].get(b)})
                else:
                    v['soc'] = '--'
                    v['vol'] = '--'

                if k in ress2.keys():
                    v.update(soc=ress2[k].get('soc'), vol=ress2[k].get('aveu'))

            # 处理缺失时刻数据：补齐‘--’
            for moment_dict in moments.values():
                if not 'soc' in moment_dict:
                    moment_dict['soc'] = '--'
                if not 'vol' in moment_dict:
                    moment_dict['vol'] = '--'
                for battery_name in battery_names:
                    if not battery_name in moment_dict:
                        moment_dict[battery_name] = '--'

            # 组织响应数据格式
            return_dict['soc'] = [i.get('soc') for i in moments.values()]
            return_dict['vol'] = [i.get('vol') for i in moments.values()]
            return_dict['datetime'] = moments.keys()
            return_dict['cell_vol'] = {}

            for battery_name in battery_names:
                return_dict['cell_vol'][battery_name] = [i.get(battery_name) for i in moments.values()]

    except Exception as e:
        error_log.error(e)
        print(traceback.print_exc())
        raise e

    return_dict['date'] = date_time
    return return_dict


class BatteryDataView(APIView):
    """
    电池电压分析：查看指定电池分析的指定一天的SOC和单体电压，和平均电压
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        # station_name = request.query_params.get('station_name')  # 并网点英文名称
        # date_time = request.query_params.get('date_time')  # 查询日期
        # unit_name = request.query_params.get('unit_name')   # 单元名称
        # battery_names = request.query_params.get('battery_name')     # 单体电池名称集合

        record_id = request.query_params.get('record_id')
        if not record_id:
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "record_id为空" if
                 lang == 'zh' else 'Record_id is empty.'}})

        sql = """SELECT
                    date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums, battery_name, battery_count
                FROM
                    `ads_record_bat_anal_1d`
                WHERE
                    record_id = %s AND
                    is_delete = 0 limit 1"""

        res = ads_db_tool.select_one(sql, record_id)
        if not res:
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "分析记录不存在" if
                 lang == 'zh' else 'Analysis record does not exist.'}})

        station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
        unit = Unit.objects.filter(is_delete=0, station=station, bms=res['unit']).first()

        battery_names = eval(res['battery_name'])
        date_time = res['date_time']

        return_dict = get_battery_data_2(station, unit, battery_names, date_time, res['case_type'])

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                }
            }
        )


@functools.lru_cache(maxsize=None)
def select_battery_tem_data(station_name, device, start_time, end_time):
    """
    指定一天的最高单体温度
    """""
    table_name1 = dwd_tables['measure']['bms_3']
    select_sql_1 = f"""SELECT time, mtmax
                        FROM {table_name1} where station_name=%s and device=%s and time between %s and %s
                        ORDER BY time ASC;"""

    results_2 = dwd_db_tool.select_many(select_sql_1, *(station_name, device, start_time, end_time))

    res = {}

    if results_2:
        for result_2 in results_2:
            if result_2['time']:
                if result_2['time'].minute % 5 != 0:
                    time_ = datetime.datetime.fromtimestamp(
                        result_2['time'].replace(second=0, microsecond=0).timestamp() - result_2[
                            'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M")
                else:
                    time_ = result_2['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M")

                res[time_] = result_2

    return res


@functools.lru_cache(maxsize=None)
def get_code_status_data(station_name, device, start_time, end_time):
    """
    指定一天的故障码，故障等级
    """""
    table_name = dwd_tables['discrete']['bms']
    select_sql_2 = f"""SELECT time, tmsfc, tmsfcl
                        FROM {table_name} where station_name=%s and device=%s and time between %s and %s
                        ORDER BY time ASC;"""

    results_1 = dwd_db_tool.select_many(select_sql_2, *(station_name, device, start_time, end_time))
    res = {}

    if results_1:
        for result_1 in results_1:
            if result_1['time']:
                if result_1['time'].minute % 5 != 0:
                    time_ = datetime.datetime.fromtimestamp(
                        result_1['time'].replace(second=0, microsecond=0).timestamp() - result_1[
                            'time'].timestamp() % 300).strftime("%Y-%m-%d %H:%M")
                else:
                    time_ = result_1['time'].replace(second=0, microsecond=0).strftime("%Y-%m-%d %H:%M")

                res[time_] = result_1

    return res


def get_battery_tem_data(station, unit, date_time, start_time, end_time, lang='zh'):
    """
    查看指定电池分析的指定一天的最高单体温度，故障码，故障等级
    """""
    station_name = station.english_name
    device = unit.bms
    return_dict = {}

    moments = create_time_mapping_3(start_time, end_time, m=5)

    try:
        # 使用多线程和缓存优化查询速度
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            future_1 = executor.submit(select_battery_tem_data, station_name, device, start_time, end_time)
            future_2 = executor.submit(get_code_status_data, station_name, device, start_time, end_time)

            ress1 = future_1.result()
            ress2 = future_2.result()

        for k, v in moments.items():
            if k in ress1.keys():
                v.update(mtmax=ress1[k].get('mtmax'), tmsfc='--', tmsfcl='--')

            if k in ress2.keys():
                v.update(tmsfc=ress2[k].get('tmsfc'), tmsfcl=ress2[k].get('tmsfcl'))

        # 处理缺失时刻数据：补齐‘--’
        for moment_dict in moments.values():
            if not 'mtmax' in moment_dict:
                moment_dict['mtmax'] = '--'
            if not 'tmsfc' in moment_dict:
                moment_dict['tmsfc'] = '--'
            if not 'tmsfcl' in moment_dict:
                moment_dict['tmsfcl'] = '--'

        if lang == 'zh':
            # 组织响应数据格式
            return_dict['最高单体温度'] = [i.get('mtmax') for i in moments.values()]
            return_dict['故障码'] = [i.get('tmsfc') for i in moments.values()]
            return_dict['故障等级'] = [i.get('tmsfcl') for i in moments.values()]
            return_dict['datetime'] = moments.keys()
        else:
            # 组织响应数据格式
            return_dict['最高单体温度'] = [i.get('mtmax') for i in moments.values()]
            return_dict['故障码'] = [i.get('tmsfc') for i in moments.values()]
            return_dict['故障等级'] = [i.get('tmsfcl') for i in moments.values()]
            return_dict['datetime'] = moments.keys()

    except Exception as e:
        error_log.error(e)
        print(traceback.print_exc())
        raise e

    return_dict['date'] = date_time
    return return_dict


class BatteryTemDataView(APIView):
    """
    电池温度分析：查看指定电池分析的指定一天的最高单体温度，故障码，故障等级
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        # station_name = request.query_params.get('station_name')  # 并网点英文名称
        # date_time = request.query_params.get('date_time')  # 查询日期
        # unit_name = request.query_params.get('unit_name')   # 单元名称
        # battery_names = request.query_params.get('battery_name')     # 单体电池名称集合

        record_id = request.query_params.get('record_id')
        if not record_id:
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "record_id为空" if
                 lang == 'zh' else "The record_id is null."}})

        sql = """SELECT
                    date_time, station_name, unit, unit_name, level, case_type, case_content, hand_info, battery_nums,
                     battery_name, battery_count, start_time, end_time
                FROM
                    `ads_record_bat_anal_1d`
                WHERE
                    record_id = %s AND
                    is_delete = 0 limit 1"""

        res = ads_db_tool.select_one(sql, record_id)
        if not res:
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "分析记录不存在"
                 if lang == 'zh' else "The record does not exist."}})

        station = StationDetails.objects.filter(is_delete=0, english_name=res['station_name']).first()
        unit = Unit.objects.filter(is_delete=0, station=station, bms=res['unit']).first()

        date_time = res['date_time']
        start_time = res['start_time']

        # 提示日期范围（如果没有结束时间显示开始时间至当前）、提示时间内的最高单体温度和对应储能单元的TMS故障码和故障等级
        end_time = res['end_time'] if res['end_time'] else datetime.datetime.now()

        return_dict = get_battery_tem_data(station, unit, date_time, start_time, end_time)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                }
            }
        )


def get_effe_data(station, unit, start_time, end_time):
    """
    取选择时间段内的最新一次充电电量和充电的起始截止时间。以及选择当条记录的最近一次充电记录
    【更正】：效果评估时查询操作前后最近的SOC变化值大于等于60%的一次充电量，且计算电量提升比例的公式中需要除以响应的soc变化值
    """""
    # start_day = datetime.datetime.strptime(start_time - datetime.timedelta(days=1), '%Y-%m-%d')
    # end_day = datetime.datetime.strptime(end_time, '%Y-%m-%d')
    one_day_before = start_time - datetime.timedelta(days=1)
    one_day_after = end_time + datetime.timedelta(days=1)

    # 查询dwd_measure_bms_data_storage_3表根据电流>0 查询对应时间范围前后的充电的开始和结束时间，查询出来的是满足条件的所有的充电时间段
    sql = """
            select station_name,device,slave,pack,type,min(time) as start_time,max(time) as end_time, max(soc) as max_soc, min(soc) as min_soc
            from(
                -- 去重，time化5分钟整点
                select five_min_time as time,station_name ,device ,slave ,pack ,`type` ,i,soc
                    ,unix_timestamp(five_min_time)-row_number () over(partition by station_name,device order by time)*300 as rk_group
                from(
                    select *
                        ,row_number () over(partition by station_name,device,five_min_time order by time desc) as rk
                    from(
                        select time,station_name ,device ,slave ,pack ,`type` ,i,soc
                            ,from_unixtime(unix_timestamp(time) -unix_timestamp(time) % 300) as five_min_time
                        from dwd_measure_bms_data_storage_3
                        where 1=1
                        and i > 0 
                        and station_name='{}'
                        and device='{}'
                        and time BETWEEN '{}' and '{}'
                    )t0
                )t1
                where rk=1
            )t3
            group by rk_group,station_name,device,slave,pack,type
            having count(i)>1 -- i<0连续条件，连续行数至少为2
        """

    # order by end_time desc -- 仅保留最后一条
    # limit 1'

    # sql1 = "select max(time) as b_end_time, min(time) as b_start_time from dwd_measure_bms_data_storage_3 WHERE i < 0 and station_name=%s and device=%s and time like %s"
    # sql2 = "select max(time) as a_end_time, min(time) as a_start_time from dwd_measure_bms_data_storage_3 WHERE i < 0 and station_name=%s and device=%s and time like %s"
    sql3 = "SELECT pae, nae FROM `dwd_cumulant_bms_data_storage` WHERE `station_name` = %s AND `device` = %s AND `time` BETWEEN %s AND %s order by time asc"

    # 查询操作前的充电电量和充电的开始和结束时间
    res1_ = dwd_db_tool.select_single(sql.format(station.english_name, unit.bms, one_day_before.strftime('%Y-%m-%d %H:%M:%S'), start_time.strftime('%Y-%m-%d %H:%M:%S')) + ' order by end_time desc')
    # 查询操作后的充电电量和充电的开始和结束时间
    res2_ = dwd_db_tool.select_single(sql.format(station.english_name, unit.bms, end_time.strftime('%Y-%m-%d %H:%M:%S'), one_day_after.strftime('%Y-%m-%d %H:%M:%S')) + ' order by end_time asc')

    return_dict = {
        "before_chag": '--',
        "before_s_time": '--',
        "before_e_time": '--',
        "after_chag": '--',
        "after_s_time": '--',
        "after_e_time": '--',
    }

    # 继续找到SOC变化值大于等于60%的一次充电量
    if res1_:
        res1 = None
        for r in res1_:
            if r['max_soc'] - r['min_soc'] >= 60:
                res1 = r
                break
        if res1:
            b_start_time = res1.get('start_time')
            b_end_time = res1.get('end_time')

            # 根据查询到的时间段，查询dwd_cumulant_bms_data_storage的BMS充电历史记录值，并首尾相减计算充电电量
            res3 = dwd_db_tool.select_many(sql3, *(station.english_name, unit.bms, b_start_time, b_end_time))
            if res3:
                first_chag = res3[0]['nae'] if res3[0]['nae'] else '--'
                last_chag = res3[-1]['nae'] if res3[-1]['nae'] else '--'
                before_chag = abs(round((float(last_chag) - float(first_chag)), 2)) if first_chag != '--' and last_chag != '--' else '--'

                return_dict['before_chag'] = before_chag
                return_dict["before_s_time"] = b_start_time[:16]
                return_dict["before_e_time"] = b_end_time[:16]
                return_dict['before_diff_soc'] = float(res1.get('max_soc')) - float(res1.get('min_soc'))
                return_dict['before_min_soc'] = float(res1.get('min_soc'))
                return_dict['before_max_soc'] = float(res1.get('max_soc'))

    # 继续找到SOC变化值大于等于60%的一次充电量
    if res2_:
        res2 = None
        for r in res2_:
            if r['max_soc'] - r['min_soc'] >= 60:
                res2 = r
                break
        if res2:
            a_start_time = res2.get('start_time')
            a_end_time = res2.get('end_time')

            # 根据查询到的时间段，查询dwd_cumulant_bms_data_storage的的BMS充电历史记录值，并首尾相减计算充电电量
            res4 = dwd_db_tool.select_many(sql3, *(station.english_name, unit.bms, a_start_time, a_end_time))
            if res4:
                first_chag = res4[0]['nae'] if res4[0]['nae'] else '--'
                last_chag = res4[-1]['nae'] if res4[-1]['nae'] else '--'
                after_chag = abs(round((float(last_chag) - float(first_chag)), 2)) if first_chag != '--' and last_chag != '--' else '--'

                return_dict['after_chag'] = after_chag
                return_dict["after_s_time"] = a_start_time[:16]
                return_dict["after_e_time"] = a_end_time[:16]
                return_dict['after_max_soc'] = float(res2.get('max_soc'))
                return_dict['after_min_soc'] = float(res2.get('min_soc'))
                return_dict['after_diff_soc'] = float(res2.get('max_soc')) - float(res2.get('min_soc'))

    # 计算提升效率：（操作后最近一次的充电电量/操作后最近一次的充电电量变化值 - 操作前最近一次的充电电量/操作前最近一次的充电电量变化值） / 最近一次的操作前充电电量/操作前最近一次的充电电量变化值 * 100
    if not return_dict['before_chag'] == '--' and not return_dict['after_chag'] == '--' and not return_dict['before_chag'] == 0:
        effe_rate = round((return_dict['after_chag'] / return_dict['after_diff_soc'] - return_dict['before_chag'] / return_dict['before_diff_soc']) / (return_dict['before_chag'] / return_dict['before_diff_soc']) * 100, 2)
    else:
        effe_rate = '--'
    return_dict['effe_rate'] = effe_rate

    return return_dict


class AnalysisAssessView(APIView):
    """
    电池电压分析：查看电池电压分析的指定数据
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        station_name = request.query_params.get('station_name')  # 并网点英文名称
        start_time = request.query_params.get('start_time')  # 查询日时间
        end_time = request.query_params.get('end_time')
        unit_name = request.query_params.get('unit_name')   # 单元名称

        if not all([station_name, start_time, end_time, unit_name]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '必填项参数缺失！' if lang == 'zh' else 'Missing required parameters！',
                    }
                }
            )

        station = StationDetails.objects.filter(is_delete=0, english_name=station_name).first()
        if not station:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '并网点不存在！' if lang == 'zh' else 'The station does not exist！',
                    }
                }
            )

        unit = Unit.objects.filter(is_delete=0, station=station, bms=unit_name).first()
        if not unit:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '单元不存在！' if lang == 'zh' else 'The unit does not exist！',
                    }
                }
            )

        try:
            start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            if start_time > end_time:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {
                            "message": "error",
                            "detail": '开始时间不能大于结束时间！' if lang == 'zh' else
                            'The start time cannot be greater than the end time！',
                        }
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '时间格式错误！' if lang == 'zh' else 'Time format error！',
                    }
                }
            )

        return_dict = get_effe_data(station, unit, start_time, end_time)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                }
            }
        )


class SaveAnalysisAssessView(APIView):
    """
    电池电压分析：保存评估数据
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        station_name = request.data.get('station_name')  # 并网点英文名称
        date_time = request.data.get('date_time')       # 查询日期
        start_time = request.data.get('start_time')  # 查询日时间
        end_time = request.data.get('end_time')
        unit_name = request.data.get('unit_name')   # 单元名称
        note = request.data.get('note')
        before_s_time = request.data.get('before_s_time')
        before_e_time = request.data.get('before_e_time')
        before_chag = request.data.get('before_chag')
        after_s_time = request.data.get('after_s_time')
        after_e_time = request.data.get('after_e_time')
        after_chag = request.data.get('after_chag')
        effe_rate = request.data.get('effe_rate')
        record_id = request.data.get('record_id')

        # todo 参数校验
        if not all([record_id, station_name, start_time, end_time, unit_name, date_time, effe_rate, before_s_time,
                    before_e_time, after_e_time, after_s_time, after_chag, before_chag, note]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '必填项参数缺失！' if lang == 'zh' else 'Missing required parameters！',
                    }
                }
            )

        station = StationDetails.objects.filter(is_delete=0, english_name=station_name).first()
        if not station:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '并网点不存在！' if lang == 'zh' else 'The station does not exist！',
                    }
                }
            )

        unit = Unit.objects.filter(is_delete=0, station=station, bms=unit_name).first()
        if not unit:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '单元不存在！' if lang == 'zh' else 'The unit does not exist！',
                    }
                }
            )

        # 校验date_time
        if not re.match(r'^[0-9]{4}-[0-9]{2}-[0-9]{2}$', date_time):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": 'date_time格式不正确！' if lang == 'zh' else 'date_time format is incorrect！',
                    }
                }
            )

        try:
            res = BatteryAnalysisRecord.objects.create(
                related_id=str(record_id),
                record_user_id=user_id,
                station_name=station_name,
                unit_name=unit_name,
                date_time=date_time,
                start_time=start_time,
                end_time=end_time,
                before_s_time=before_s_time,
                before_e_time=before_e_time,
                before_chag=before_chag,
                after_s_time=after_s_time,
                after_e_time=after_e_time,
                after_chag=after_chag,
                effe_rate=effe_rate,
                note=note
            )
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": res.id,
                    }
                }
            )
        except Exception as e:
            logger.error(e)
            print(traceback.print_exc())
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '保存失败！' if lang == 'zh' else 'Failed to save！',
                    }
                }
            )


class GetAnalysisAssessView(APIView):
    """
    电池电压分析：查看评估数据
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        record_id = request.query_params.get('record_id')

        if not record_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '必填项record_id参数缺失！' if lang == 'zh' else 'Missing required parameter record_id.',
                    }
                }
            )

        try:
            res = BatteryAnalysisRecord.objects.filter(related_id=int(record_id)).order_by('-create_time').first()
            if res:
                res = model_to_dict(res)
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": res,
                        }
                    }
                )
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '分析记录无评估数据！' if lang == 'zh' else 'No assessment data for analysis record.',
                    }
                }
            )
        except Exception as e:
            logger.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                    }
                }
            )