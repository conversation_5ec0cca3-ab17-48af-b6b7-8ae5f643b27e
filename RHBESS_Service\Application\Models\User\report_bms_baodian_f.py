#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-10 14:28:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report_f.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-30 11:52:43

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class FReportBmsBaodian(user_Base):
    u'冻结保电充放电量，温度，电压，SOH数据'
    __tablename__ = "f_report_bms_baodian"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    pcs_name = Column(VARCHAR(50), nullable=False,comment=u"PCS名称")
    cu_name = Column(VARCHAR(50), nullable=False,comment=u"BMS名称")
    chag = Column(VARCHAR(256), nullable=True,comment=u"充电量")
    disg = Column(VARCHAR(256), nullable=True,comment=u"放电量")
    ratio = Column(Float, nullable=False,comment=u"效率")
    max_t = Column(VARCHAR(50), nullable=True, comment=u"最高温度极差")
    max_v = Column(VARCHAR(50), nullable=True, comment=u"最高电压极差")
    min_soh = Column(VARCHAR(50), nullable=True, comment=u"SOH最小值")
    day = Column(DateTime, nullable=False,primary_key=True,comment=u"数据时间")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    cause = Column(CHAR(2), nullable=False,comment=u"1日数据2月数据")
    en_cu_name = Column(VARCHAR(50), nullable=False, comment=u"BMS名称")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'pcs_name':'%s','cu_name':'%s','chag':'%s','disg':'%s','ratio':'%s','day':'%s','max_t':'%s','max_v':'%s','min_soh':'%s','op_ts':'%s','cause':'%s','en_cu_name':'%s'}" % (
            self.id,self.pcs_name,self.cu_name,self.chag,self.disg,self.ratio,self.day,self.max_t,self.max_v,self.min_soh,self.op_ts,self.cause,self.en_cu_name)


    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}