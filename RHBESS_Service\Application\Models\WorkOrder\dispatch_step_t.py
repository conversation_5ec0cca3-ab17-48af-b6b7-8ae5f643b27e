#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-04 08:59:49
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\dispatch_step_t.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-19 09:32:59


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Application.Models.User.user import User

class DispatchStep(user_Base):
    u'工单模板步骤表'
    __tablename__ = "t_dispatch_step"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    model_id = Column(Integer,ForeignKey("t_dispatch_model.id"), nullable=False,comment=u"模板id")
    descr = Column(String(256), nullable=True, comment=u"当前步骤名称")
    check_user = Column(Integer, nullable=True,comment=u"当前步骤审核人，没有为空，必须是一个人")
    content = Column(String(256), nullable=True,comment=u"工作内容")
    rcontent = Column(String(256), nullable=True,comment=u"回复内容")
    pre_step = Column(Integer, nullable=True,comment=u"上一步，空为第一步")
    next_step = Column(Integer, nullable=True,comment=u"下一步,空为最后一步")
    is_team = Column(Integer, nullable=False,server_default='0',comment=u"是否需要协作，0否，1是")
    handle_users = Column(String(256), nullable=True, comment=u"当前步骤处理人，[]")
    imgs = Column(String(256), nullable=True, comment=u"图片，多个用#号分割")
    files = Column(String(256), nullable=True, comment=u"文件，多个用#号分割")
    stage = Column(Integer, nullable=True,comment=u"工单阶段")

    care_user = Column(Integer, nullable=True,comment=u"被转交人")
    care_time = Column(DateTime, nullable=True,comment=u"转交时间")
    care_content = Column(String(256), nullable=True,comment=u"转交填写内容")

    en_descr = Column(String(256), nullable=True, comment=u"当前步骤名称")
    en_content = Column(String(256), nullable=True, comment=u"工作内容")
    en_care_content = Column(String(256), nullable=True, comment=u"转交填写内容")
    
    model_step= relationship("DispatchModel", backref="model_step")
    

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        CU = user_session.query(User).filter(User.id==self.check_user,User.unregister==1).first()
        if self.is_team == 1:
            HUS = user_session.query(User).filter(User.id.in_(eval(str(self.handle_users))),User.unregister==1).all()
        else:
            HUS = user_session.query(User).filter(User.id==self.handle_users,User.unregister==1).all()
        check_descr = CU.name if CU else ''
        en_check_descr = CU.en_name if CU else ''
        model_descr = self.model_step.descr if self.model_step else ''
        en_model_descr = self.model_step.en_descr if self.model_step else ''
        a1 = self.descr.split() if self.descr else []
        a2 = self.content.split() if self.content else []
        a3 = self.rcontent.split() if self.rcontent else []
        handle_descr = []
        en_handle_descr = []
        for u in HUS:
            handle_descr.append(u.name)
            en_handle_descr.append(u.en_name)
        # user_session.close()
        bean = "{'id':%s,'descr':'%s','model_id':%s,'model_descr':'%s','en_model_descr':'%s','check_user':'%s','check_descr':'%s','en_check_descr':'%s','content':'%s','rcontent':'%s',\
            'pre_step':%s,'next_step':%s,'is_team':%s,'handle_users':'%s','en_handle_descr':'%s','handle_descr':'%s','imgs':'%s','files':'%s','stage':%s,'care_user':'%s','care_time':'%s','care_content':'%s','en_descr':'%s','en_content':'%s''en_care_content':'%s'}" % (
            self.id,';'.join(a1),self.model_id,model_descr,en_model_descr,self.check_user,check_descr,en_check_descr,';'.join(a2),';'.join(a3),
            self.pre_step,self.next_step,self.is_team,self.handle_users,','.join(handle_descr),','.join(en_handle_descr),self.imgs,self.files,self.stage,self.care_user,self.care_time,self.care_content,self.en_descr,self.en_content,self.en_care_content)
        
        return bean.replace("None",'')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}