import datetime
import json
from Application.Models.ElePriceDescision.models import ModelFile, TModel
from Application.Models.ElePriceDescision.models import CDictSery, RSeriesDataShanxi
from Tools.DecisionDB.ele_base import user_session
from apscheduler.schedulers.blocking import BlockingScheduler
def compute_accuracy_rate():
    """计算时刻准确率"""
    now_time = datetime.datetime.now()
    query_time = now_time - datetime.timedelta(hours=25)
    files = user_session.query(ModelFile).filter(ModelFile.type == 1,
                                                 ModelFile.create_time >= query_time, ModelFile.is_use == 1).all()

    # 查询每个电价文件
    for _file in files:
        dict_serie_id = user_session.query(CDictSery).filter(CDictSery.name == '现货出清电价信息',
                                                             CDictSery.province_id == _file.province_id,
                                                             CDictSery.is_use == "1").first().id
        if dict_serie_id == 13:
            res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == _file.op_ts,
                                                               RSeriesDataShanxi.series_id == dict_serie_id,
                                                               RSeriesDataShanxi.name == "总加",
                                                               RSeriesDataShanxi.is_use == "1").order_by(
                RSeriesDataShanxi.op_ts.desc()).all()

        else:
            res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == _file.op_ts,
                                                               RSeriesDataShanxi.series_id == dict_serie_id,
                                                               RSeriesDataShanxi.is_use == "1").order_by(
                RSeriesDataShanxi.op_ts.desc()).all()
        # 查询文件上传日的出清电价信息
        serie_dict = {}
        for serie in res:
            serie_dict[serie.moment] = serie.value1
        # 解析附件
        if _file.price_data:
            file_res = json.loads(_file.price_data)
            price_data = []

            for v in file_res:
                if serie_dict.get(v.get('time')) != None:
                    if float(serie_dict.get(v.get('time'))) < 100 and float(v.get('value')) < 100:
                        accuracy_rate = 1
                    elif float(serie_dict.get(v.get('time'))) > 500 and float(v.get('value')) > 500:
                        accuracy_rate = 1
                    else:
                        if serie_dict.get(v.get('time')) != 0:
                            rate = abs(float(v.get('value')) / float(serie_dict.get(v.get('time'))) - 1)
                            if rate > 1 or rate < 0:
                                rate = 1
                            accuracy_rate = round(1 - rate, 2)
                        else:
                            accuracy_rate = 0

                    price_data.append(
                        {
                            'time': v.get('time'),
                            'value': round(float(v.get('value')), 2),
                            'accuracy_rate': accuracy_rate if accuracy_rate <= 1 else 1
                        }
                        )
            _file.price_data = json.dumps(price_data)
    user_session.commit()
    # 查询模型，更新模型准确率
    models_res = user_session.query(TModel).filter(TModel.is_use == 1).all()
    for models in models_res:
        accuracy_rate_res = user_session.query(ModelFile).filter(ModelFile.model_id == models.id,
                                                                 ModelFile.province_id == models.province.id,
                                                                 ModelFile.type == 1,
                                                                 ModelFile.is_use == 1).all()  # 查询时刻准确率
        accuracy_rate_list = []  # 模型准确率
        for rate in accuracy_rate_res:
            if rate.price_data:
                info = json.loads(rate.price_data)
                rate_data = [r.get('accuracy_rate') for r in info if r.get('accuracy_rate') != None]
                accuracy_rate_list.extend(rate_data)
        models.accuracy_rate = round(sum(accuracy_rate_list) / len(accuracy_rate_list), 2) * 100 if accuracy_rate_list else 0
        models.update_time = datetime.datetime.now()
    user_session.commit()
    return 1

def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(compute_accuracy_rate, 'cron', hour=17, minute=33,
                  id="compute_accuracy_rate_task", max_instances=10, replace_existing=True)  # 10分钟获取一次数据
    scheduler.start()

if __name__ == '__main__':
    RunClearFileAndData()