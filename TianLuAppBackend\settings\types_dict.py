# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/9/10 下午5:15
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : types_dict.py
# @Software : PyCharm


BATTERY_ANALYSIS_TYPES_DICT = {
    "zh": {
            1: "电池电压分析",
            2: "电池温度分析",
            3: "电池过放风险"
        },
    "en": {
            1: "Battery Voltage Analysis",
            2: "Battery Temperature Analysis",
            3: "Risk of Battery Over Discharge"
        }
}

# 负荷分析下所有身份
LOAD_REGIONS = {
    'provinces': {
        'zh': [
                {'name': '浙江省', 'id': 12},
                {'name': '江苏省', 'id': 11}
        ],
        'en': [
                {'name': 'Zhejiang Province', 'id': 12},
                {'name': 'Jiangsu Province', 'id': 11}
        ]
    }
}

# 算法API请求白名单
IP_WHITE_LIST = [
    '**********/12'
]

# 负荷预测模型id映射
FORECASTING_MODEL_ID_MAPPING = {
    '11': '12'
}

locale_keys = {
    "zh": "en_translate_pub",
    "en": "zh_translate_pub"
}

# 项目类型
SCENE_FLAG = {
    0: {
        "zh": "峰谷套利",
        "en": "Peak Valley Arbitrage"
    },
    1: {
        "zh": "台区治理",
        "en": "Taiwan District Governance"
    }
}


# 项目类型
PROJECT_TYPE = {
    0: {
        'zh': 'EMC-自投',
        'en': 'EMC Self Investment'
    },
    1: {
        'zh': '经租类',
        'en': 'Rental Category'
    },
    2: {
        'zh': '销售类',
        'en': 'Sales Category'
    }
}

# 需量计算方式
DEMAND_CUMPUT = {
    0: {
        'zh': '按需量计费',
        'en': 'Pay On Demand'
    },
    1: {
        'zh': '按容量计费',
        'en': 'Charged By Capacity'
    }
}