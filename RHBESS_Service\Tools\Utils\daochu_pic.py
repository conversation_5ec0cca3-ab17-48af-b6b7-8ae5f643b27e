#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-03-30 17:23:03
#@FilePath     : \RHBESS_Service\Tools\Utils\daochu_pic.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-12-07 14:52:51

# from pyecharts import Bar,Funnel

# bar = Bar("我的第一个图表", "这里是副标题")
# bar.add("服装", ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"], [5, 20, 36, 10, 75, 90])
# bar.render(path='bar.png')    # 生成本地 HTML 文件


import json,time
from Tools.DB.mysql_his import dongmu_session,_dm_his_engine,HIS_DATABASE_
from Application.Models.His.r_ACDMS import HisDM
from Tools.Utils.time_utils import timeUtils
import xlsxwriter as xw
workbook = xw.Workbook('aaaddee.xls')  # 创建工作簿

def xw_toExcel(data,shell):  # xlsxwriter库储存数据到excel
    print (11111111111111)
    worksheet1 = workbook.add_worksheet(shell)  # 创建子表
    worksheet1.activate()  # 激活表
    title = ['时间','BMS1充电','BMS1放电','BMS2充电','BMS2放电','BMS3充电','BMS3放电','BMS4充电','BMS4放电','BMS5充电','BMS5放电','BMS6充电','BMS6放电','BMS7充电','BMS7放电']  # 设置表头
    worksheet1.write_row('A1', title)  # 从A1单元格开始写入表头
    i = 2  # 从第二行开始写入数据
    for j in range(len(data[0])):
        insertData = []
        for a in range(len(data)):
            insertData.append(data[a][j])
        row = 'A' + str(i)
        worksheet1.write_row(row, insertData)
        i += 1
        print ('------------',time.time(),'*****',j)

def daochu():
    dm_table = HisDM('r_cumulant')
    startT = "2023-06-15"
    # endT = "2023-10-18"
    endT = "2023-12-05"

    # startT_ = timeUtils.timeStrToTamp(startT)
    # endT_ = timeUtils.timeStrToTamp(endT)
    days = timeUtils.dateToDataList(startT,endT)
    type_obj = []
    all = [[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]]
    for day in days:
        startT_ = timeUtils.timeStrToTamp(day+' 00:00:00')
        endT_ = timeUtils.timeStrToTamp(day+" 23:59:59")+1
        chag_arr,disg_arr,chag_arr_e,disg_arr_e = [],[],[],[]
        values_mong = dongmu_session.query(dm_table.datainfo,dm_table.utime).filter(dm_table.time.between(startT_,endT_)).order_by(dm_table.time.asc()).all()
        
        # 所有原始数据
        for value_mong in values_mong:
            data_info = json.loads(value_mong[0])['body']
            timeUtils.ssTtimes(value_mong[1])
            for v in data_info: #首值
                for i in range(1,8):
                    if "PCS%s"%i == v['device']:
                        chag_arr.append(v["BCCap"])  # 充电量
                        disg_arr.append(v["BDcap"])  # 放电量
            all[0].append(timeUtils.ssTtimes(value_mong[1]))
            all[1].append(chag_arr[0])
            all[2].append(disg_arr[0])
            for i in range(1,7):
                all[i*2+1].append(chag_arr[i])
                all[i*2+2].append(disg_arr[i])


        # if values_mong:
        #     start_data_info = json.loads(values_mong[0][0])['body']
        #     end_data_info = json.loads(values_mong[-1][0])['body']
            
        #     for v in start_data_info: #首值
        #         if "PCS" in v['device']:
        #             chag = v["BCCap"]  # 充电量
        #             disg = v["BDcap"]  # 放电量
        #             chag_arr.append(chag)
        #             disg_arr.append(disg)
        #     for v in end_data_info: # 末值
        #         if "PCS" in v['device']:
        #             chag_e = v["BCCap"]  # 充电量
        #             disg_e = v["BDcap"]  # 放电量
        #             chag_arr_e.append(chag_e)
        #             disg_arr_e.append(disg_e)
        #     all[0].append(day)
        #     all[1].append(float(chag_arr_e[0]) - float(chag_arr[0]))
        #     all[2].append(float(disg_arr_e[0]) - float(disg_arr[0]))
        #     for i in range(1,7):
        #         all[i*2+1].append(float(chag_arr_e[i]) - float(chag_arr[i]))
        #         all[i*2+2].append(float(disg_arr_e[i]) - float(disg_arr[i]))

        #     type_obj.append({"day": day, "chag":chag_arr,"disg":disg_arr,"chag_e":chag_arr_e,"disg_e":disg_arr_e,"start":startT_,"end":endT_})

    dongmu_session.close()
    xw_toExcel(all,'title1')  # 

daochu()
workbook.close()  # 关闭表
