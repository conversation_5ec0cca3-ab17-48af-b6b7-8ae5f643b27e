#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-23 17:14:08
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\python3_read_python2.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-23 14:43:52



# import sys
# reload(sys)
# sys.setdefaultencoding("utf-8")
import os,time
import json
import pandas as pd
from Tools.DB.redis_con import r_real

"""
从excel读取数据，写入另一个excel
padding : PKCS7
"""

if __name__ == "__main__":
    # data = r_real.hget("measure","tpStbinhai1.PcsSt3.Lp2.TmaxInvt")
    # # da = {"name":"张三","age":32}
    # # r_real.hset("measure","test",json.dumps(da))
    # # data = r_real.hget("measure","test")
    
   
    # data2 = json.loads(data.decode())
    # print ('******',type(data2),data2)
    json_data = {
                                "app": slave_station.app,
                                "station": slave_station.english_name,
                                "body": [
                                    {
                                        "device": unit.bms,
                                        "datatype": "measure",
                                        "totalcall": "0",
                                        "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
                                    },
                                    {
                                        "device": unit.pcs,
                                        "datatype": "measure",
                                        "totalcall": "0",
                                        "body": ["ChaD", "DisD", "P", "Q"],
                                    },
                                    {
                                        "device": unit.pcs,
                                        "datatype": "status",
                                        "totalcall": "0",
                                        "body": [
                                            "Fault",
                                            "alarm",
                                        ],
                                    },
                                    {
                                        "device": "EMS",
                                        "datatype": "status",
                                        "totalcall": "0",
                                        "body": ["AEnC", "AEn", "EAEn"],
                                    },
                                    {
                                        "device": unit.pcs,
                                        "datatype": "measure",
                                        "totalcall": "0",
                                        "body": ["PP1"],
                                    },
                                    {
                                        "device": f'{slave_station_meter_ins["device"].upper()}{slave_station_num}',
                                        "datatype": "cumulant",
                                        "totalcall": "0",
                                        "body": [slave_station_meter_ins["charge"],
                                                 slave_station_meter_ins["discharge"]],
                                    },
                                    {
                                        "device": unit.bms,
                                        "datatype": "status",
                                        "totalcall": "0",
                                        "body": [
                                            "GFault",
                                            "GAlarm",
                                        ],
                                    },
                                    {
                                        "device": unit.pcs,
                                        "datatype": "measure",
                                        "totalcall": "0",
                                        "body": ["BPCE", "BPDE"],
                                    }
                                ],
                            }
    
    
                   
