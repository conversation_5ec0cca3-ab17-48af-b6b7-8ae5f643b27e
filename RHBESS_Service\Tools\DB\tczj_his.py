from sqlalchemy import create_engine
from Application.Cfg.dir_cfg import model_config
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base

TCZJ_USERNAMES = model_config.get('mysql', "TCZJ_USERNAMES")
TCZJ_PASSWORDS = model_config.get('mysql', "TCZJ_PASSWORDS")
TCZJ_HOSTNAMES = model_config.get('mysql', "TCZJ_HOSTNAMES")
TCZJ_PORTS = model_config.get('mysql', "TCZJ_PORTS")
TCZJ_DATABASE = model_config.get('mysql', "TCZJ_DATABASE")
TCZJ_DATABASEG = model_config.get('mysql', "TCZJ_DATABASEG")


TCZJ_HISTORY_USERNAMES = model_config.get('mysql', "TCZJ_HISTORY_USERNAMES")
TCZJ_HISTORY_PASSWORDS = model_config.get('mysql', "TCZJ_HISTORY_PASSWORDS")
TCZJ_HISTORY_HOSTNAMES = model_config.get('mysql', "TCZJ_HISTORY_HOSTNAMES")
TCZJ_HISTORY_PORTS = model_config.get('mysql', "TCZJ_HISTORY_PORTS")
TCZJ_HISTORY_DATABASE = model_config.get('mysql', "TCZJ_HISTORY_DATABASE")
TCZJ_HISTORY_DATABASE1 = model_config.get('mysql', "TCZJ_HISTORY_DATABASE1")


tczj_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCZJ_USERNAMES,
    TCZJ_PASSWORDS,
    TCZJ_HOSTNAMES,
    TCZJ_PORTS,
    TCZJ_DATABASE
)
tczj_engine = create_engine(tczj_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tczj_session = scoped_session(sessionmaker(tczj_engine,autoflush=True))
tczj_Base = declarative_base(tczj_engine)
tczj_session = _tczj_session()

tczj_mysql_url_g='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCZJ_USERNAMES,
    TCZJ_PASSWORDS,
    TCZJ_HOSTNAMES,
    TCZJ_PORTS,
    TCZJ_DATABASEG
)
tczj_engine_g = create_engine(tczj_mysql_url_g,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tczj_session_g = scoped_session(sessionmaker(tczj_engine_g,autoflush=True))
tczj_Base_g = declarative_base(tczj_engine_g)
tczj_session_g = _tczj_session_g()


tczj_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCZJ_HISTORY_USERNAMES,
    TCZJ_HISTORY_PASSWORDS,
    TCZJ_HISTORY_HOSTNAMES,
    TCZJ_HISTORY_PORTS,
    TCZJ_HISTORY_DATABASE
)
tczj_history_engine = create_engine(tczj_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tczj_history_session = scoped_session(sessionmaker(tczj_history_engine,autoflush=True))
tczj_history_Base = declarative_base(tczj_history_engine)
tczj_history_session = _tczj_history_session()


tczj1_history_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TCZJ_HISTORY_USERNAMES,
    TCZJ_HISTORY_PASSWORDS,
    TCZJ_HISTORY_HOSTNAMES,
    TCZJ_HISTORY_PORTS,
    TCZJ_HISTORY_DATABASE1
)
tczj1_history_engine = create_engine(tczj1_history_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_tczj1_history_session = scoped_session(sessionmaker(tczj1_history_engine,autoflush=True))
tczj1_history_Base = declarative_base(tczj1_history_engine)
tczj1_history_session = _tczj1_history_session()