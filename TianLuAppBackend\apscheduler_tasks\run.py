#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-06-13 13:09:34
#@FilePath     : \后端d:\git_mulu\emot_pjt_rh\TianLuAppBackend\apscheduler_tasks\run.py
#@Email        : <EMAIL>
#@LastEditTime : 2025-05-07 13:30:15


import logging
import socket
import traceback
from datetime import datetime
# from apscheduler.schedulers.blocking import BlockingScheduler
import os

from TianLuAppBackend import settings
from apis.monitor.tasks import timing_income_v4, timing_income_v4_for_yesterday
from apis.project_manage.tasks import check_ems_slave_station_meter_config
from apis.web2.running_analysis.tasks import all_stations_analysis, all_stations_analysis_day
from apscheduler_tasks.report_tasks import *
from apscheduler_tasks.message_verify_tasks import message_verify_task
from apscheduler_tasks.send_message_or_email_tasks import CheckFault
from apscheduler_tasks.tasks_chack_online_status import check_all_stations
from apscheduler_tasks.mqtt_tasks import mtqq_station_strategy_task
from apscheduler_tasks.project_tasks import project_latitude_and_longitude_task,project_all_year_month_yesterday_income_task
from apscheduler_tasks.station_tasks import map_task
# from apscheduler_tasks.tasks import timing_chgdig_s
from apscheduler_tasks.tasks_h import electricity_h_Count
from apis.monitor.tasks import plan_history
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "TianLuAppBackend.settings")


# 这里是创建django_apscheduler 任务的固定代码
from apscheduler.schedulers.background import BackgroundScheduler
from django_apscheduler.jobstores import DjangoJobStore, register_job
from apis.app2.station_task import station_satatus_task, station_base_income_day, station_fault_alarm, station_efficiency, \
    station_fault_alarm_polling, station_statistics_income
from apscheduler_tasks.strategy_tasks import strategy_generate
logger = logging.getLogger('exception')

# 1.实例化调度器
APS_SCHEDULER_CONFIG = {
    'apscheduler.executors.default': {
        'class': 'apscheduler.executors.pool:ThreadPoolExecutor',
        'max_workers': '40'
    },
    'apscheduler.job_defaults.misfire_grace_time': None,
    'apscheduler.job_defaults.coalesce': 'true'
}
scheduler = BackgroundScheduler(APS_SCHEDULER_CONFIG)
# 2.调度器使用DjangoJobStore()
scheduler.add_jobstore(DjangoJobStore(), "default")


def tick():
    print('Tick! The time is: %s' % datetime.now())


# def task_timing_demo():
#     logger.info('====================定时任务：<task_timing_demo> 开始执行===========================')
#     electricity_h_Count()


def task_get_all_stations_pre_day_report():
    logger.info('====================定时任务：<task_get_all_stations_pre_day_report> 开始执行===========================')
    try:
        get_all_stations_pre_day_report()
    except Exception as e:
        logger.error(e)


def task_get_all_stations_his_day_report():
    logger.info('====================定时任务：<task_get_all_stations_his_day_report> 开始执行===========================')
    # get_all_stations_his_day_report()
    try:
        get_all_stations_his_day_report()
    except Exception as e:
        logger.error(e)


def task_get_all_stations_pre_week_report():
    logger.info('====================定时任务：<task_get_all_stations_pre_week_report> 开始执行===========================')
    try:
        get_all_stations_pre_week_report()
    except Exception as e:
        logger.error(e)


def task_get_all_stations_his_week_report():
    logger.info('====================定时任务：<task_get_all_stations_his_week_report> 开始执行===========================')
    # get_all_stations_his_week_report()
    try:
        get_all_stations_his_week_report()
    except Exception as e:
        logger.error(e)


def task_get_all_stations_pre_month_report():
    logger.info('====================定时任务：<task_get_all_stations_pre_month_report> 开始执行===========================')
    try:
        get_all_stations_pre_month_report()
    except Exception as e:
        logger.error(e)


def task_get_all_stations_his_month_report():
    logger.info('====================定时任务：<task_get_all_stations_his_month_report> 开始执行===========================')
    # get_all_stations_his_month_report()
    try:
        get_all_stations_his_month_report()
    except Exception as e:
        logger.error(e)


def task_check_fault_alarm():
    logger.info(
        '==========================定时任务：<task_check_fault_alarm> 开始执行=================================')
    f = CheckFault()
    now = datetime.datetime.now()
    five_minutes_ago = now - datetime.timedelta(minutes=5)
    f.run(five_minutes_ago)


def task_check_all_stations_oneline_status():
    logger.info(
        '====================定时任务：<task_check_all_stations_oneline_status> 开始执行===========================')
    try:
        check_all_stations()
    except Exception as e:
        print(traceback.print_exc())
        logger.error(e)


# def task_timing_income_v4_for_today():
#     logger.info(
#         '====================定时任务：<task_timing_income_v4_for_today> 开始执行===========================')
#     try:
#         timing_income_v4()
#     except Exception as e:
#         logger.error(e)


# def task_timing_income_v4_for_yesterday():
#     logger.info(
#         '====================定时任务：<task_timing_income_v2_for_yesterday> 开始执行===========================')
#     try:
#         timing_income_v4_for_yesterday()
#     except Exception as e:
#         logger.error(e)


def task_running_analysis_task():
    logger.info(
        '====================定时任务：<task_running_analysis_task> 开始执行===========================')
    try:
        all_stations_analysis()
    except Exception as e:
        print(traceback.print_exc())
        logger.error(e)

def task_running_analysis_task_day():
    logger.info(
        '====================定时任务：<task_running_analysis_task_day--反向送电一天任务> 开始执行===========================')
    try:
        all_stations_analysis_day()
    except Exception as e:
        print(traceback.print_exc())
        logger.error(e)


def task_check_ems_slave_station_meter_config():
    logger.info(
        '====================定时任务：<task_check_ems_slave_station_meter_config> 开始执行===========================')
    try:
        check_ems_slave_station_meter_config()
    except Exception as e:
        print(traceback.print_exc())
        logger.error(e)



# def station_base_income_day_task():
#     """
#     写入基准充放电量和收益表
#     :return:
#     :return:
#     """
#     logger.info(
#         '==========================定时任务：<station_base_income_task_day> 开始执行=================================')
#     try:
#         station_base_income_day()
#     except Exception as e:
#         logger.error(e)
#     return 1

# # 每天凌晨2点跑一天的收益信息  IDC计算
# scheduler.add_job(station_base_income_day_task, 'cron', hour=2, minute=10,
#                   id="station_base_income_task_day", name='station_base_income_task_day', max_instances=10, replace_existing=True)

def plan_history_run():
    """
    功率计划下发任务
    :return:
    :return:
    """
    logger.info(
        '==========================定时任务：<plan_history_task> 开始执行=================================')
    try:
        plan_history()
    except Exception as e:
        logger.error(e)
    return 1

# 每5分钟跑一次功率计划下发
scheduler.add_job(plan_history_run, 'cron', minute='*/5', id="plan_history_task", name='plan_history_task', max_instances=10, replace_existing=True)

def station_fault_alarm_polling_task():
    """
    故障巡检任务
    :return:
    :return:
    """
    logger.info(
        '==========================定时任务：<station_fault_alarm_polling_task_minute> 开始执行=================================')
    try:
        station_fault_alarm_polling()
    except Exception as e:
        logger.error(e)
    return 1

# 每六分钟跑一次故障巡检任务
scheduler.add_job(station_fault_alarm_polling_task, 'cron', minute='*/6', id="station_fault_alarm_polling_task_minute", name='station_fault_alarm_polling_task_minute', max_instances=10, replace_existing=True)


def station_fault_alarm_task():
    """
    写入新增并网点匹配故障信息
    :return:
    :return:
    """
    logger.info(
        '==========================定时任务：<station_fault_alarm_task_minute> 开始执行=================================')
    try:
        station_fault_alarm()
    except Exception as e:
        logger.error(e)
    return 1

# 每五十分钟执行一次
scheduler.add_job(station_fault_alarm_task, 'cron', minute='*/50', id="station_fault_alarm_task_minute", name='station_fault_alarm_task_minute', max_instances=10, replace_existing=True)

def station_efficiency_task():
    """
    写入主站充放电效率
    :return:
    :return:
    """
    logger.info(
        '==========================定时任务：<station_efficiency_task_hour> 开始执行=================================')
    try:
        station_efficiency()
    except Exception as e:
        logger.error(e)
    return 1

# 每三十分钟执行一次
scheduler.add_job(station_efficiency_task, 'cron', minute='*/30', id="station_efficiency_task_hour", name='station_efficiency_task_hour', max_instances=10, replace_existing=True)


# def timing_chgdig_s_day_task():
#     """
#     逐天收益冻结数据
#     :return:
#     """
#     logger.info(
#         '==========================定时任务：<timing_chgdig_s_day_task_day> 开始执行=================================')
#     try:
#         timing_chgdig_s_day()
#     except Exception as e:
#         logger.error(e)
#     return 1

# # 每天凌晨0点45分跑前一天的充电量信息
# scheduler.add_job(timing_chgdig_s_day_task, 'cron', hour=0, minute=45, id="timing_chgdig_s_day_task_day", name='timing_chgdig_s_day_task_day', max_instances=10, replace_existing=True)



# def station_statistics_income_task():
#     """
#     计算每日尖峰平谷收益
#     :return:
#     """
#     logger.info(
#         '==========================定时任务：<station_statistics_income_task_hour> 开始执行=================================')
#     try:
#         station_statistics_income()
#     except Exception as e:
#         logger.error(e)
#     return 1

# # 每三个小时执行一次尖峰平谷收益信息 从idc直接取
# scheduler.add_job(station_statistics_income_task, 'cron', hour='*/3', minute=15, id="station_statistics_income_task_hour", name='station_statistics_income_task_hour', max_instances=10, replace_existing=True)




def update_station_satatus_task():
    """
    写入站的状态信息
    :return:
    """
    logger.info(
        '==========================定时任务：<station_satatus_task_minute> 开始执行=================================')
    try:
        station_satatus_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每3分钟跑一次所有站的状态信息
scheduler.add_job(update_station_satatus_task, 'cron', minute='*/3', id="station_satatus_task_minute", name='station_satatus_task_minute', max_instances=10, replace_existing=True)


def message_verify():
    """
    策略抄送数据超期处理
    :return:
    """
    logger.info(
        '==========================定时任务：<message_verify_task> 开始执行=================================')
    try:
        message_verify_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每两小时查询一次超过48小时未处理的策略抄送信息
scheduler.add_job(message_verify, 'cron', hour='*/2', minute=5, id="message_verify_task", name='message_verify_task', max_instances=10, replace_existing=True)


def mtqq_station_strategy():
    """
    更新全部电站的当月实时策略信息
    :return:
    """
    logger.info(
        '==========================定时任务：<mtqq_station_strategy_task> 开始执行=================================')
    try:
        mtqq_station_strategy_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每天执行一次，更新全部电站的当月实时策略信息
scheduler.add_job(mtqq_station_strategy, 'cron', hour=1, minute=45, id="mtqq_station_strategy_task", name='mtqq_station_strategy_task', max_instances=10, replace_existing=True)


def latitude_and_longitud():
    """
    更新新增项目的经纬度
    :return:
    """
    logger.info(
        '==========================定时任务：<project_latitude_and_longitude_task> 开始执行=================================')
    try:
        project_latitude_and_longitude_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每5分钟执行一次，更新新增项目的经纬度
scheduler.add_job(latitude_and_longitud, 'cron', minute='*/5', id="project_latitude_and_longitude_task", name='project_latitude_and_longitude_task', max_instances=10, replace_existing=True)


def project_all_year_month_yesterday_income():
    """
    缓存项目累计、年、月、昨日收益和昨日充放完成率
    :return:
    """
    logger.info(
        '==========================定时任务：<project_all_year_month_yesterday_income_task> 开始执行=================================')
    try:
        project_all_year_month_yesterday_income_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每5小时56分钟执行一次，更新项目累计、年、月、昨日收益和昨日充放完成率。有效时间是6小时
scheduler.add_job(project_all_year_month_yesterday_income, 'cron', hour='*/5',minute=56, id="project_all_year_month_yesterday_income_task", name='project_all_year_month_yesterday_income_task', max_instances=10, replace_existing=True)



def map():
    """
    更新小程序地图缓存
    :return:
    """
    logger.info(
        '==========================定时任务：<map_task> 开始执行=================================')
    try:
        map_task()
    except Exception as e:
        logger.error(e)
    return 1

# 每4分钟执行一次，更新地图缓存
scheduler.add_job(map, 'cron', minute='*/4', id="map_task", name='map_task', max_instances=10, replace_existing=True)



def strategy_generate_task():
    """
    默认、基准、电价策略同步至下一年
    :return:
    """
    logger.info(
        '==========================定时任务：<strategy_generate_task> 开始执行=================================')
    try:
        strategy_generate()
    except Exception as e:
        logger.error(e)
    return 1

# 每年12月30日同步
scheduler.add_job(strategy_generate_task, 'cron', month=12, day=30, id="strategy_generate_task", name='strategy_generate_task', max_instances=10, replace_existing=True)





# 添加任务示例
# scheduler.add_job(task_timing_demo, 'interval', seconds=60 * 2, id="task_timing_demo", replace_existing=True)
# scheduler.add_job(tick, 'cron', hour=16, minute=39, id="task_cron", max_instances=10, replace_existing=True)
# 初始化充放电效率数据
# scheduler.add_job(station_efficiency, 'date', id='disposable_task', run_date=datetime.datetime.now())


# TODO 每小时第30分钟执行获取冻结表数据
# scheduler.add_job(task_timing_demo, 'cron', minute=30, id="task_timing_demo", max_instances=10,
#                   replace_existing=True)

# TODO 每天02：30：00获取前一天的运行日报
scheduler.add_job(task_get_all_stations_pre_day_report, 'cron', hour=1, minute=30,
                  id="task_get_all_stations_pre_day_report", name='task_get_all_stations_pre_day_report', max_instances=10, replace_existing=True)

# 手动启动获取前一日的运行日报，，只运行一次
# scheduler.add_job(task_get_all_stations_pre_day_report, id="task_get_all_stations_pre_day_report_1",
#                   max_instances=10, replace_existing=True)


# 启动时获取一次本年所有的历史日报，只运行一次
# scheduler.add_job(task_get_all_stations_his_day_report, id="task_get_all_stations_his_day_report",
#                   max_instances=10, replace_existing=True)

# TODO 每周一03：30：00获取前一周的运行周报
scheduler.add_job(task_get_all_stations_pre_week_report, 'cron', day_of_week=0, hour=2, minute=30,
                  id="task_get_all_stations_pre_week_report", name='task_get_all_stations_pre_week_report', max_instances=10, replace_existing=True)

# 手动启动获取前一周的运行周报，，只运行一次
# scheduler.add_job(task_get_all_stations_pre_week_report,
#                   id="task_get_all_stations_pre_week_report_1", max_instances=10, replace_existing=True)

# 启动时获取一次本年所有周的历史周报，只运行一次
# scheduler.add_job(task_get_all_stations_his_week_report,
#                   id="task_get_all_stations_his_week_report", max_instances=10, replace_existing=True)


# TODO 每月1日03：00：00获取前一个月的运行月报
scheduler.add_job(task_get_all_stations_pre_month_report, 'cron', day=1, hour=3, minute=30,
                  id="task_get_all_stations_pre_month_report", name='task_get_all_stations_pre_month_report', max_instances=10, replace_existing=True)

# scheduler.add_job(task_get_all_stations_pre_month_report,
#                   id="task_get_all_stations_pre_month_report_1", max_instances=10, replace_existing=True)

# 启动时获取一次本年所有月份的历史月报，只运行一次
# scheduler.add_job(task_get_all_stations_his_month_report,
#                   id="task_get_all_stations_his_month_report", max_instances=10, replace_existing=True)


# TODO 每5分钟运行一次检查故障告警，发送短信和邮件
scheduler.add_job(task_check_fault_alarm, 'interval', minutes=5, id="task_check_fault_alarm", name='task_check_fault_alarm', max_instances=10,
                  replace_existing=True)

# TODO 运行一次检查故障告警，发送短信和邮件
scheduler.add_job(task_check_fault_alarm, id="task_check_fault_alarm_test", name='task_check_fault_alarm_test', max_instances=10,
                  replace_existing=True)

# TODO 运行一次检查所有电站的设备运行状态
scheduler.add_job(task_check_all_stations_oneline_status,
                  id="task_check_all_stations_oneline_status_one", name='task_check_all_stations_oneline_status_one', max_instances=1,
                  replace_existing=True)

# TODO 每5分钟运行一次检查所有电站的设备运行状态
scheduler.add_job(task_check_all_stations_oneline_status, 'interval', minutes=5,
                  id="task_check_all_stations_oneline_status", name='task_check_all_stations_oneline_status', max_instances=10,
                  replace_existing=True)


# TODO 每1小时计算一次当天的收益数据  IDC计算
# scheduler.add_job(task_timing_income_v4_for_today, 'interval', minutes=45,
#                   id="task_timing_income_v4_for_today", name='task_timing_income_v4_for_today', max_instances=10, replace_existing=True)

# scheduler.add_job(task_timing_income_v4_for_today,
#                   id="task_timing_income_v4_for_today_1", max_instances=10, replace_existing=True)


# TODO 每天00：50：00计算前一天的收益数据  IDC计算
# scheduler.add_job(task_timing_income_v4_for_yesterday, 'cron', hour=1, minute=10,
#                   id="task_timing_income_v4_for_yesterday", name='task_timing_income_v4_for_yesterday', max_instances=10, replace_existing=True)

# 启动时计算前一天的收益数据，只运行一次
# scheduler.add_job(task_timing_income_v4_for_yesterday,
#                   id="task_timing_income_v4_for_yesterday_for_once", max_instances=1, replace_existing=True)


# TODO 每6分钟运行一次检查所有电站的运行分析状态
scheduler.add_job(task_running_analysis_task, 'interval', minutes=6,
                  id="task_running_analysis_task", name='task_running_analysis_task', max_instances=10,
                  replace_existing=True)

# TODO 每天运行一次统计所有电站得反向电数据
scheduler.add_job(task_running_analysis_task_day, 'cron', hour=0, minute=10,
                  id="task_running_analysis_task_day", name='task_running_analysis_task_day', max_instances=10,
                  replace_existing=True)

# 启动时只运行一次
scheduler.add_job(task_running_analysis_task,
                  id="task_running_analysis_task_1", name='task_running_analysis_task_1', max_instances=10,
                  replace_existing=True)

# TODO 每15分钟运行一次检查所有的标准主从并网点且使用结算电表的主站的使用电表配置是否缺失
scheduler.add_job(task_check_ems_slave_station_meter_config, 'interval', minutes=15,
                  id="task_check_ems_slave_station_meter_config", name='task_check_ems_slave_station_meter_config', max_instances=10, replace_existing=True)
scheduler.add_job(task_check_ems_slave_station_meter_config,
                  id="task_check_ems_slave_station_meter_config_1", name='task_check_ems_slave_station_meter_config_1', max_instances=10, replace_existing=True)

# try:
#     scheduler.start()
# except (KeyboardInterrupt, SystemExit):
#     print('====>定时任务启动失败!!!<====')
#     pass


try:
    # 利用一个占用端口来检测是否已经启动， 如已占用则说明已启动
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.bind(("127.0.0.1", 44444))
except socket.error:
    print("!!!scheduler started, DO NOTHING")
else:
    try:
        if settings.SYNC_TASK_TOOGLE:
            print('scheduler running !!!')
            scheduler.start()
        else:
            print('No need scheduler')
    except Exception as e:
        # 一般是没生成表，就启动当前程序就会报错
        print(e)


# if __name__ == '__main__':
#     try:
#         scheduler.start()
#     except (KeyboardInterrupt, SystemExit):
#         print('====>定时任务启动失败!!!<====')
#         pass

