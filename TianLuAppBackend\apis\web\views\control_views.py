import concurrent.futures
import datetime
import json
import traceback

import math
import random
import time

from decimal import Decimal

from django.db import transaction
from django.db.models import Sum, <PERSON>, Q, Min
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings

from apis.user import models
from apis.web.models import ProjectPack, PlanDeliverRecords, PowerDeliverRecords
from apis.web.serializers import MonitorSendMobileMessageSerializer, PowerIssudSerializer, PowerIssudDeleteSerializer
from common import common_response_code
from common.constant import EMPTY_STR_LIST
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from settings.meter_settings import METER_DIC
from settings.version_maps import Version_Dict
from tools.aly_send_smscode import Sample
from tools.day_hours_used import pool, \
    select_range_data_from_dwd_rhyc, select_latest_data_from_dwd_rhyc
from LocaleTool.common import redis_pool
from apis.app2.utils import paging, permission_required
from apis.web.nodeconfig import PLANSTATUS
from settings.types_dict import SCENE_FLAG
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# Create your views here.


# class ControlCardsView(APIView):
#     """项目疾控卡片页面"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def _get_realtime_data(self, project_id):
#         projects = models.Project.objects.filter(id=project_id).values(
#             "id",  # 项目id
#             "name",  # 项目名
#             "english_name",  # 项目英文名
#             "rated_power",  # 额定功率
#             "rated_power_unit",  # 额定功率单位
#             "rated_capacity",  # 额定容量
#             "rated_capacity_unit",  # 额定容量单位
#         )
#
#         project = projects[0]
#         # 旧字段强制映射
#         project["project"] = project['id']
#         project["project__name"] = project['name']
#         project["project__english_name"] = project['english_name']
#         project["project__rated_power"] = project['rated_power']
#         project["project__rated_power_unit"] = project['rated_power_unit']
#         project["project__rated_capacity"] = project['rated_capacity']
#         project["project__rated_capacity_unit"] = project['rated_capacity_unit']
#
#         # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#
#         day_charge_list = []
#         day_discharge_list = []
#         # Fault_list = []
#         # offline_list = []
#         active_power_list = []
#         reactive_power_list = []
#         cycles_nmuber_list = []
#         soc_list = []
#         soh_list = []
#         BCHCap_list = []  # 累计充电量
#         BDHcap_list = []  # 累积放电量
#         # alarm_list = []  # 累积放电量
#         day = datetime.datetime.now().strftime('%Y-%m-%d')
#         # units_all = models.Unit.objects.filter(is_delete=0, station__project_id=project["id"]).all()
#         # stations = models.StationDetails.objects.filter(project=project["id"]).all()
#         units_all = models.Unit.objects.filter(is_delete=0, station__master_station__project_id=project["id"]).all()
#         stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project["id"]).exclude(slave=0).all()
#         for station in stations:
#             meter_ins = METER_DIC[station.meter_count]
#
#             units = models.Unit.objects.filter(is_delete=0, station=station).all()
#             num = 0
#             # alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station=station).exists()
#             # alarm_list.append(alarm_exist)
#
#             # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
#             meter_use_time = models.StationMeterUseTime.objects.filter(station=station, is_use=1).first()
#             if meter_use_time:
#                 if meter_use_time.end_time:
#                     is_use_account = (station.is_account and
#                                       meter_use_time.start_time <= datetime.datetime.now() <= meter_use_time.end_time)
#                 else:
#                     is_use_account = (station.is_account and
#                                       meter_use_time.start_time <= datetime.datetime.now())
#             else:
#                 is_use_account = False
#
#             if is_use_account == 1:
#                 # 查询结算表
#                 sql = """SELECT
#                                     chag, disg
#                                      FROM ads_report_ems_chag_disg_1d
#                                      WHERE
#                                         station = '{}'
#                                         AND
#                                         day = '{}'
#                                          """.format(station.english_name, day)
#                 with connections['doris_ads_rhyc'].cursor() as ads_cursor:
#                     try:
#                         # 获取查询结果
#                         ads_cursor.execute(sql)
#                         res = ads_cursor.fetchall()
#                     except Exception as e:
#                         error_log.error(e)
#                         return Response(
#                             {
#                                 "code": common_response_code.SUMMARY_CODE,
#                                 "data": {
#                                     "message": "error",
#                                     "detail": '查询失败！',
#                                 }
#                             }
#                         )
#                 for i in res:
#                     day_charge_list.append(i[0])
#                     day_discharge_list.append(i[1])
#
#             for unit in units:
#
#                 # 取电表初始值
#                 if (station.english_name in OriginValuesDict.keys() and unit.english_name in
#                         OriginValuesDict[station.english_name].keys()):
#
#                     origin_value_dict = OriginValuesDict.get(station.english_name).get(unit.english_name)
#                     charge_key = METER_DIC.get(station.meter_count).get('charge')
#                     discharge_key = METER_DIC.get(station.meter_count).get('discharge')
#
#                     origin_charge = origin_value_dict.get(station.meter_count).get(
#                         charge_key)
#                     origin_discharge = origin_value_dict.get(station.meter_count).get(
#                         discharge_key)
#
#                 else:
#                     origin_charge = 0
#                     origin_discharge = 0
#
#                 if len(units) > 1:
#                     num += 1
#                 else:
#                     num = ""
#                 json_data = {
#                     "app": station.app,
#                     "station": station.english_name,
#                     "body": [
#                         {
#                             "device": unit.bms,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["ChaD", "DisD", "P", "Q"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": [
#                                 "Fault",
#                                 "alarm",
#                             ],
#                         },
#                         {
#                             "device": "EMS",
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": ["AEnC", "AEn", "EAEn"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["PP1"],
#                         },
#                         {
#                             "device": f'{meter_ins["device"].upper()}{num}',
#                             "datatype": "cumulant",
#                             "totalcall": "0",
#                             "body": [meter_ins["charge"], meter_ins["discharge"]],
#                         },
#                         {
#                             "device": unit.bms,
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": [
#                                 "GFault",
#                                 "GAlarm",
#                             ],
#                         },
#                     ],
#                 }
#
#                 redis_conn = get_redis_connection("3")
#
#                 key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
#                                                                unit.bms)
#                 measure_bms = redis_conn.get(key1)
#                 if measure_bms:
#                     measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
#                 else:
#                     measure_bms_dict = {}
#
#                 key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
#                                                                unit.pcs)
#                 measure_pcs = redis_conn.get(key2)
#                 if measure_pcs:
#                     measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
#                 else:
#                     measure_pcs_dict = {}
#
#                 key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
#                                                                unit.pcs)
#                 status_pcs = redis_conn.get(key3)
#                 if status_pcs:
#                     status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
#                 else:
#                     status_pcs_dict = {}
#
#                 # key4 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, 'EMS')
#                 # status_ems = redis_conn.get(key4)
#                 # if status_ems:
#                 #     status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
#                 # else:
#                 #     status_ems_dict = {}
#
#                 key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', station.english_name,
#                                                                f'{meter_ins["device"].upper()}{num}')
#                 cumulant_ = redis_conn.get(key5)
#                 if cumulant_:
#                     cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
#                 else:
#                     cumulant_dict = {}
#
#                 # key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
#                 # status_bms = redis_conn.get(key6)
#                 # if status_bms:
#                 #     status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
#                 # else:
#                 #     status_bms_dict = {}
#
#                 # if not status_pcs_dict:
#                 #     offline_list.append(1)  # 离线状态
#
#                 # else:
#                     # GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
#                     # Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
#                     # if Fault and int(Fault) == 1:
#                     #     Fault_list.append(1)
#                     # if GFault and int(GFault) == 1:
#                     #     Fault_list.append(1)
#                 if not is_use_account:
#                     if station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                         ChaD = measure_bms_dict.get("ChaED", '--')  # 今日充电量
#                         DisD = measure_bms_dict.get("DisED", '--')  # 今日放电量
#                     else:
#                         ChaD, DisD = project_day_charge_count(station, unit.bms)
#                     day_charge_list.append(decimal.Decimal(ChaD)) if ChaD != '--' else day_charge_list.append(ChaD)
#                     day_discharge_list.append(decimal.Decimal(DisD)) if DisD != '--' else day_discharge_list.append(
#                         DisD)
#
#                 BCHCap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["charge"])))) - abs(origin_charge)) if cumulant_dict.get(meter_ins["charge"]) and cumulant_dict.get(meter_ins["charge"]) not in EMPTY_STR_LIST else '--'  # 累计充电量
#                 BDHcap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["discharge"])))) - abs(origin_discharge)) if cumulant_dict.get(meter_ins["discharge"]) and cumulant_dict.get(meter_ins["discharge"]) not in EMPTY_STR_LIST else '--'# 累计放电量
#                 if station.english_name == "NBLS002" and station.meter_count == 3:
#                     BCHCap += 21007 if BCHCap != '--' else '--'
#                     BDHcap += 19108 if BDHcap != '--' else '--'
#                 BCHCap_list.append(decimal.Decimal(BCHCap)) if BCHCap != '--' else BCHCap_list.append(BCHCap)
#                 BDHcap_list.append(decimal.Decimal(BDHcap)) if BDHcap != '--' else BDHcap_list.append(BDHcap)
#
#                 P = measure_pcs_dict.get("P", '--')  # 实时功率
#
#                 active_power_list.append(float(P)) if P not in EMPTY_STR_LIST  else active_power_list.append(P)
#                 Q = measure_pcs_dict.get("Q", '--')  # 无功功率
#
#                 reactive_power_list.append(float(Q)) if Q not in EMPTY_STR_LIST else reactive_power_list.append(Q)
#                 NBSC = measure_bms_dict.get("NBSC", '--')  # 循环次数
#                 SOC = measure_bms_dict.get("SOC", '--')  # SOC
#                 SOH = measure_bms_dict.get("SOH", '--')  # SOH
#
#                 cycles_nmuber_list.append(float(NBSC)) if NBSC not in EMPTY_STR_LIST else cycles_nmuber_list.append(NBSC)
#                 soc_list.append(float(SOC)) if SOC not in EMPTY_STR_LIST else soc_list.append(SOC)
#                 soh_list.append(float(SOH)) if SOH not in EMPTY_STR_LIST else soh_list.append(SOH)
#
#         # count = new_project_count(project, sum(BCHCap_list), sum(BDHcap_list))
#         # project["project_status"] = 1  # 正常
#         # if True in alarm_list:
#         #     project["project_status"] = 2  # 告警
#         # if 1 in Fault_list:
#         #     project["project_status"] = 3  # 故障
#         # if 1 in offline_list:
#         #     project["project_status"] = 4  # 离线
#
#         project["project_status"] = models.StationStatus.objects.values(
#             'station__master_station__project_id').filter(station__master_station__project_id=project["id"]).aggregate(
#             Max('status')).get('status__max')
#
#         project["day_charge"], project["day_charge_unit"] = charge_discharge_conversion(day_charge_list)  # 日充电量
#         project["day_discharge"], project["day_discharge_unit"] = charge_discharge_conversion(
#             day_discharge_list)  # 日放电量
#         project["active_power"] = int(sum(active_power_list)) if '--' not in active_power_list else '--'    # 有功功率
#         project["reactive_power"] = int(sum(reactive_power_list)) if '--' not in reactive_power_list else '--'  # 无功功率
#         if len(units_all) == 0:
#             project["SOC"] = '--'
#             project["cycles"] = '--'
#         else:
#             project["SOC"] = int(sum(soc_list) / len(units_all)) if '--' not in soc_list else '--'  # soc
#             # project["cycles"] = int(sum(cycles_nmuber_list) / len(units_all))     # 上报循环次数
#             count = project_count(project, sum(BCHCap_list), sum(BDHcap_list)) if '--' not in BCHCap_list and '--' not in BCHCap_list else '--'   # 计算循环次数
#             project["cycles"] = count  # 上报循环次数
#         project["SOH"] = int(sum(soh_list) / len(units_all)) if '--' not in soh_list else '--'  # soh
#         project["unit_count"] = units_all.count()
#         return project
#
#     def get(self, request):
#         user_id = request.user["user_id"]
#         project_include = request.query_params.get("project", None)
#         page = int(request.query_params.get('page', 1))
#         page_size = int(request.query_params.get('page_size', 8))
#
#         user = models.UserDetails.objects.get(id=user_id)
#         project_instances = models.Project.objects.filter(user=user, is_used=1).all()
#         if project_include:
#             project_instances = project_instances.filter(name__contains=project_include)
#
#         # 手动分页
#         total_pages = math.ceil(len(project_instances) / page_size)
#         start_index = (page-1) * page_size
#         end_index = page * page_size if page < total_pages else len(project_instances) + 1
#
#         project_ids = [project_instance.id for project_instance in project_instances][start_index:end_index]
#
#         with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#             futures = list()
#             for project_id in project_ids:
#                 future = executor.submit(self._get_realtime_data, project_id)
#                 futures.append(future)
#             results = [f.result() for f in concurrent.futures.as_completed(futures)]
#
#         results.sort(key=lambda x: x['project'])
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": results,
#                     "paginator_info": {
#                         "page": page,
#                         "page_size": page_size,
#                         "pages": total_pages,
#                         "total_count": len(project_instances)
#                     }
#                 }
#             }
#         )


class ControlCardsListView(APIView):
    """项目疾控卡片页面：列表"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def _get_realtime_data(self, project_id):
        conn = pool.connection()
        cursor = conn.cursor()

        projects = models.Project.objects.filter(id=project_id).values(
            "id",  # 项目id
            "name",  # 项目名
            "english_name",  # 项目英文名
            "rated_capacity",  # 额定容量
        )
        project = projects[0]
        # 旧字段强制映射
        project["project"] = project['id']
        project["project__name"] = project['name']
        project["project__english_name"] = project['english_name']
        project["project__rated_capacity"] = project['rated_capacity']

        # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
        Fault_list = []
        offline_list = []
        # alarm_list = []

        stations = models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).all()
        for station in stations:
            # meter_ins = METER_DIC[station.meter_count]

            units = models.Unit.objects.filter(is_delete=0, station=station)
            num = 0
            # alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station=station).exists()
            # alarm_list.append(alarm_exist)
            for unit in units:
                app = station.app
                station_name = station.english_name

                if len(units) > 1:
                    num += 1
                else:
                    num = ""

                # try:
                #     result_1 = select_realtime_data_from_rhyc(cursor, app, station_name, 'status', unit.pcs, "Fault",
                #                                               "alarm", )
                #     result_2 = select_realtime_data_from_rhyc(cursor, app, station_name, 'status', unit.bms, "GFault",
                #                                           "GAlarm", )
                # except Exception as e:
                #     error_log.error("select realtime data from rhyc：error: {}".format(e))
                #     try:
                #         cursor.close()
                #         conn.close()
                #     except:
                #         pass
                #     return Response(
                #         {
                #             "code": common_response_code.ERROR,
                #             "data": {
                #                 "message": "fail",
                #                 "detail": "安昌数据库查询失败",
                #             },
                #         }
                #     )

                redis_conn = get_redis_connection("3")

                key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                               unit.pcs)
                status_pcs = redis_conn.get(key3)
                if status_pcs:
                    status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                else:
                    status_pcs_dict = {}

                key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
                status_bms = redis_conn.get(key6)
                if status_bms:
                    status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                else:
                    status_bms_dict = {}

                if not status_pcs_dict or not status_bms_dict:
                    offline_list.append(1)  # 离线状态
                else:
                    GFault = status_bms_dict.get("GFault") or -2  # bms故障状态
                    Fault = status_pcs_dict.get("Fault") or -2  # pcs故障状态
                    if Fault and int(Fault) == 1:
                        Fault_list.append(1)
                    if GFault and int(GFault) == 1:
                        Fault_list.append(1)

            # project["project_status"] = 1  # 正常
            # if True in alarm_list:
            #     project["project_status"] = 2  # 告警
            # if 1 in Fault_list:
            #     project["project_status"] = 3  # 故障
            # if 1 in offline_list:
            #     project["project_status"] = 4  # 离线
            project["project_status"] = models.StationStatus.objects.values(
                'station__master_station__project_id').filter(station__master_station__project_id=project["id"]).aggregate(Max('status')).get('status__max')
        try:
            cursor.close()
            conn.close()
        except:
            pass

        return project

    def _get_realtime_datas(self, project_ids, lang):
        all_projects_info = models.Project.objects.filter(id__in=project_ids,is_used=1).values(
            "id",  # 项目id
            "name",  # 项目名
            "english_name",  # 项目英文名
            "rated_capacity",  # 额定容量
            "application_scenario"  # 项目类型
        )
        all_projects_dict = {}
        for item in all_projects_info:
            item["project"] = item['id']
            item["project__name"] = item['name']
            item["project__english_name"] = item['english_name']
            item["project__rated_capacity"] = item['rated_capacity']
            item["application_scenario"] = SCENE_FLAG.get(item['application_scenario'])[lang]
            all_projects_dict[item['id']] = item

        # stations = models.StationDetails.objects.filter(master_station__project_id__in=all_projects_dict.keys(),is_delete=0).all()
        project = []
        for key, data in all_projects_dict.items():
            data["project_status"] = models.StationStatus.objects.values(
                'station__master_station__project_id').filter(station__master_station__project_id=key).aggregate(Max('status')).get('status__max')
            project.append(data)
        return project

    def get(self, request):
        lang = request.headers.get('lang', 'zh')
        user_id = request.user["user_id"]

        user = models.UserDetails.objects.get(id=user_id)
        project_instances = models.Project.objects.filter(user=user, is_used=1).all()
        # project_ids = [project_instance.id for project_instance in project_instances]

        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 8))

        search_key = request.query_params.get('search_key', None)
        if search_key:
            project_instances = project_instances.filter(name__contains=search_key)

        total_pages = math.ceil(len(project_instances) / page_size)
        start_index = (page-1) * page_size
        end_index = page * page_size if page < total_pages else len(project_instances) + 1

        project_ids = [project_instance.id for project_instance in project_instances][start_index:end_index]
        results = self._get_realtime_datas(project_ids, lang)
        # with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        #     futures = list()
        #     for project_id in project_ids:
        #         future = executor.submit(self._get_realtime_data, project_id)
        #         futures.append(future)
        #     results = [f.result() for f in concurrent.futures.as_completed(futures)]

        results.sort(key=lambda x: x['project'])
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": results,
                    "paginator_info": {
                        "page": page,
                        "page_size": page_size,
                        "pages": total_pages,
                        "total_count": len(project_instances)
                    }
                }
            }
        )

class ProjectListView(APIView):
    """项目列表"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]

        user = models.UserDetails.objects.get(id=user_id)
        project_instances = models.Project.objects.filter(user=user, is_used=1).all().values(
            "id",  # 项目id
            "name",  # 项目名
            "english_name",  # 项目英文名
        )

        search_key = request.query_params.get('search_key', None)
        if search_key:
            project_instances = project_instances.filter(name__contains=search_key)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": project_instances,
                    }
                }
            )

# class ControlCardsInfoView(APIView):
#     """项目疾控卡片页面：详情"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request, pk):
#
#         try:
#             tem_project = models.Project.objects.get(id=pk)
#         except Exception as e:
#             error_log.error("项目疾控卡片页面:项目不存在:{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {"message": "error", "detail": "项目详情：项目不存在"},
#                 }
#             )
#         # print('tem_project:', tem_project)
#         project = dict()
#         project['project'] = tem_project.id
#         project['project__name'] = tem_project.name
#         project['project__english_name'] = tem_project.english_name
#         project['project__rated_power'] = tem_project.rated_power
#         project['project__rated_power_unit'] = tem_project.rated_power_unit
#         project['project__rated_capacity'] = tem_project.rated_capacity
#         project['project__rated_capacity_unit'] = tem_project.rated_capacity_unit
#
#         # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#         day = datetime.datetime.now().strftime('%Y-%m-%d')
#         # project = projects_obj:
#         day_charge_list = []
#         day_discharge_list = []
#         Fault_list = []
#         offline_list = []
#         active_power_list = []
#         reactive_power_list = []
#         cycles_nmuber_list = []
#         soc_list = []
#         soh_list = []
#         BCHCap_list = []  # 累计充电量
#         BDHcap_list = []  # 累积放电量
#         # alarm_list = []  # 累积放电量
#         # print(project["project__name"])
#         units_all = models.Unit.objects.filter(is_delete=0, station__master_station__project_id=project["project"]).all()
#         # print(len(units_all))
#         stations = models.StationDetails.objects.filter(is_delete=0, master_station__project=project["project"]).all()
#         for station in stations:
#             meter_ins = METER_DIC[station.meter_count]
#
#             units = models.Unit.objects.filter(is_delete=0, station=station).all()
#             num = 0
#             # alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station=station).exists()
#             # alarm_list.append(alarm_exist)
#
#             # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
#             meter_use_time = models.StationMeterUseTime.objects.filter(station=station, is_use=1).first()
#             if meter_use_time:
#                 if meter_use_time.end_time:
#                     is_use_account = (station.is_account and
#                                       meter_use_time.start_time <= datetime.datetime.now() <= meter_use_time.end_time)
#                 else:
#                     is_use_account = (station.is_account and
#                                       meter_use_time.start_time <= datetime.datetime.now())
#             else:
#                 is_use_account = False
#
#             if is_use_account:
#                 # 查询结算表
#                 sql = """SELECT
#                                 chag, disg
#                                  FROM ads_report_ems_chag_disg_1d
#                                  WHERE
#                                     station = '{}'
#                                     AND
#                                     day = '{}'
#                                      """.format(station.english_name, day)
#                 with connections['doris_ads_rhyc'].cursor() as ads_cursor:
#                     try:
#                         # 获取查询结果
#                         ads_cursor.execute(sql)
#                         res = ads_cursor.fetchall()
#                     except Exception as e:
#                         error_log.error(e)
#                         return Response(
#                             {
#                                 "code": common_response_code.SUMMARY_CODE,
#                                 "data": {
#                                     "message": "error",
#                                     "detail": '查询失败！',
#                                 }
#                             }
#                         )
#                 for i in res:
#                     day_charge_list.append(i[0])
#                     day_discharge_list.append(i[1])
#             for unit in units:
#
#                 # 取电表初始值
#                 if (station.english_name in OriginValuesDict.keys() and unit.english_name in
#                         OriginValuesDict[station.english_name].keys()):
#
#                     origin_value_dict = OriginValuesDict.get(station.english_name).get(unit.english_name)
#                     charge_key = METER_DIC.get(station.meter_count).get('charge')
#                     discharge_key = METER_DIC.get(station.meter_count).get('discharge')
#                     origin_charge = origin_value_dict.get(station.meter_count).get(
#                         charge_key)
#                     origin_discharge = origin_value_dict.get(station.meter_count).get(
#                         discharge_key)
#
#                 else:
#                     origin_charge = 0
#                     origin_discharge = 0
#
#                 if len(units) > 1:
#                     num += 1
#                 else:
#                     num = ""
#                 json_data = {
#                     "app": station.app,
#                     "station": station.english_name,
#                     "body": [
#                         {
#                             "device": unit.bms,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["ChaD", "DisD", "P", "Q"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": [
#                                 "Fault",
#                                 "alarm",
#                             ],
#                         },
#                         {
#                             "device": "EMS",
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": ["AEnC", "AEn", "EAEn"],
#                         },
#                         {
#                             "device": unit.pcs,
#                             "datatype": "measure",
#                             "totalcall": "0",
#                             "body": ["PP1"],
#                         },
#                         {
#                             "device": f'{meter_ins["device"].upper()}{num}',
#                             "datatype": "cumulant",
#                             "totalcall": "0",
#                             "body": [meter_ins["charge"], meter_ins["discharge"]],
#                         },
#                         {
#                             "device": unit.bms,
#                             "datatype": "status",
#                             "totalcall": "0",
#                             "body": [
#                                 "GFault",
#                                 "GAlarm",
#                             ],
#                         },
#                     ],
#                 }
#                 # response = requests.post(url=url, json=json_data)
#                 # return_dic = response.json()
#                 # body = return_dic.get("body", None)
#                 # if not body:
#                 #     error_log.error("http调用安昌数据库查询接口失败")
#                 #     return Response(
#                 #         {
#                 #             "code": common_response_code.ERROR,
#                 #             "data": {
#                 #                 "message": "fail",
#                 #                 "detail": "http调用安昌数据库查询接口失败",
#                 #             },
#                 #         }
#                 #     )
#
#                 redis_conn = get_redis_connection("3")
#
#                 key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
#                                                                unit.bms)
#                 measure_bms = redis_conn.get(key1)
#                 if measure_bms:
#                     measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
#                 else:
#                     measure_bms_dict = {}
#
#                 key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name,
#                                                                unit.pcs)
#                 measure_pcs = redis_conn.get(key2)
#                 if measure_pcs:
#                     measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
#                 else:
#                     measure_pcs_dict = {}
#
#                 key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
#                                                                unit.pcs)
#                 status_pcs = redis_conn.get(key3)
#                 if status_pcs:
#                     status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
#                 else:
#                     status_pcs_dict = {}
#
#                 key4 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, 'EMS')
#                 status_ems = redis_conn.get(key4)
#                 if status_ems:
#                     status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
#                 else:
#                     status_ems_dict = {}
#
#                 key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', station.english_name,
#                                                                f'{meter_ins["device"].upper()}{num}')
#                 cumulant_ = redis_conn.get(key5)
#                 if cumulant_:
#                     cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
#                 else:
#                     cumulant_dict = {}
#
#                 key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
#                 status_bms = redis_conn.get(key6)
#                 if status_bms:
#                     status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
#                 else:
#                     status_bms_dict = {}
#
#                 if not status_pcs_dict or not status_bms_dict:
#                     offline_list.append(1)  # 离线状态
#
#                 else:
#                     GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
#                     Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
#                     if Fault and int(Fault) == 1:
#                         Fault_list.append(1)
#                     if GFault and int(GFault) == 1:
#                         Fault_list.append(1)
#                     BCHCap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["charge"])))) - abs(
#                         origin_charge)) if cumulant_dict.get(meter_ins["charge"]) and cumulant_dict.get(meter_ins["charge"]) not in EMPTY_STR_LIST else '--'  # 累计充电量
#                     BDHcap = (abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["discharge"])))) - abs(
#                         origin_discharge)) if cumulant_dict.get(meter_ins["discharge"]) and cumulant_dict.get(meter_ins["discharge"]) not in EMPTY_STR_LIST else '--'  # 累计放电量
#                     if station.english_name == "NBLS002" and station.meter_count == 3:
#                         BCHCap += 21007 if BCHCap != '--' else '--'
#                         BDHcap += 19108 if BDHcap != '--' else '--'
#                     BCHCap_list.append(decimal.Decimal(BCHCap)) if BCHCap != '--' else BCHCap_list.append(BCHCap)
#                     BDHcap_list.append(decimal.Decimal(BDHcap)) if BDHcap != '--' else BDHcap_list.append(BDHcap)
#
#                     P = measure_pcs_dict.get("P", '--')  # 实时功率
#
#                     active_power_list.append(float(P)) if P not in EMPTY_STR_LIST else active_power_list.append(P)
#                     Q = measure_pcs_dict.get("Q", '--')  # 无功功率
#
#                     reactive_power_list.append(float(Q)) if Q not in EMPTY_STR_LIST else reactive_power_list.append(Q)
#                     NBSC = measure_bms_dict.get("NBSC", '--')  # 循环次数
#                     SOC = measure_bms_dict.get("SOC", '--')  # SOC
#                     SOH = measure_bms_dict.get("SOH", '--')  # SOH
#
#                     cycles_nmuber_list.append(float(NBSC)) if NBSC not in EMPTY_STR_LIST else cycles_nmuber_list.append(NBSC)
#                     soc_list.append(float(SOC)) if SOC not in EMPTY_STR_LIST else soc_list.append(SOC)
#                     soh_list.append(float(SOH)) if SOH not in EMPTY_STR_LIST else soh_list.append(SOH)
#                 if not is_use_account:
#
#                     if station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                         ChaD = measure_bms_dict.get("ChaED", '--')  # 今日充电量
#                         DisD = measure_bms_dict.get("DisED", '--')  # 今日放电量
#                     else:
#                         ChaD, DisD = project_day_charge_count(station, unit.bms)
#
#                     day_charge_list.append(decimal.Decimal(ChaD)) if ChaD not in EMPTY_STR_LIST else day_charge_list.append(ChaD)
#                     day_discharge_list.append(decimal.Decimal(DisD)) if DisD not in EMPTY_STR_LIST else day_discharge_list.append(
#                         DisD)
#
#
#         # count = new_project_count(project, sum(BCHCap_list), sum(BDHcap_list))
#         # project["project_status"] = 1  # 正常
#         # if True in alarm_list:
#         #     project["project_status"] = 2  # 告警
#         # if 1 in Fault_list:
#         #     project["project_status"] = 3  # 故障
#         # if 1 in offline_list:
#         #     project["project_status"] = 4  # 离线
#         project["project_status"] = models.StationStatus.objects.values('station__master_station__project_id')\
#             .filter(station__master_station__project_id=project["project"]).aggregate(Max('status')).get('status__max')
#         project["day_charge"], project["day_charge_unit"] = charge_discharge_conversion(day_charge_list)  # 日充电量
#         project["day_discharge"], project["day_discharge_unit"] = charge_discharge_conversion(
#             day_discharge_list)  # 日放电量
#         project["active_power"] = int(sum(active_power_list)) if '--' not in active_power_list else '--'  # 有功功率
#         project["reactive_power"] = int(
#             sum(reactive_power_list)) if '--' not in reactive_power_list else '--'  # 无功功率
#         if len(units_all) == 0:
#             project["SOC"] = '--'
#             project["cycles"] = '--'
#         else:
#             project["SOC"] = int(sum(soc_list) / len(units_all)) if '--' not in soc_list else '--'  # soc
#             # project["cycles"] = int(sum(cycles_nmuber_list) / len(units_all))     # 上报循环次数
#             count = project_count(project, sum(BCHCap_list),
#                                   sum(BDHcap_list)) if '--' not in BCHCap_list and '--' not in BCHCap_list else '--'  # 计算循环次数
#             project["cycles_nmuber"] = project["cycles"] = count  # 上报循环次数
#         project["SOH"] = int(sum(soh_list) / len(units_all)) if '--' not in soh_list else '--'  # soh
#         project["unit_count"] = units_all.count()
#         # project["cycles_nmuber"] = project["count"] = count  # 计算循环次数
#         # project["unit_count"] = units_all.count()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": project,
#                 },
#             }
#         )


class IndexIncomeView(APIView):
    """web 卡片收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        pk = request.query_params.get("pk", None)
        if not pk:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "缺少必传参数 " if lang == 'zh' else 'Missing required parameters.'},
                }
            )
        project_ins = models.Project.objects.get(id=pk)
        master_stations = models.MaterStation.objects.filter(project=project_ins, is_delete=0).all()
        income_date = datetime.date.today()
        detail_dic = {
            "today_general_income": 0,
            "month_general_income": 0,
            "year_general_income": 0,
            "all_general_income": 0,
        }  # 今日总收益  # 本月总收益  # 本年总收益  # 历史总收益
        today_general_income = []
        year_general_income = []
        month_general_income = []
        all_general_income = []
        for station in master_stations:
            incomes_ins = models.StationIncome.objects.filter(master_station=station)

            # 日总收益
            day_incomes = incomes_ins.filter(income_date=income_date).order_by("id").all()
            # t_peak_income = 0
            # t_demand_income = 0

            if day_incomes.exists():
                for d_income in day_incomes:
                    t_peak_income = d_income.peak_load_shifting if hasattr(d_income, "peak_load_shifting") else 0
                    t_demand_income = d_income.demand_side_response if hasattr(d_income, "demand_side_response") else 0

                    t_income = t_peak_income + t_demand_income
                    today_general_income.append(t_income)

            # t_incomes = t_peak_income + t_demand_income
            # today_general_income.append(t_incomes)
            # 月收益
            first_day = income_date.replace(day=1)

            # subquery = (
            #     incomes_ins.filter(income_date__range=(first_day, income_date))
            #     .values("income_date")
            #     .annotate(last_entry=Max("id"))
            #     .values("last_entry")
            # )

            monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum("peak_load_shifting"), total_demand=Sum("demand_side_response")
            )
            if not monthly_income["total_peak"]:
                monthly_income["total_peak"] = 0
            if not monthly_income["total_demand"]:
                monthly_income["total_demand"] = 0
            total_month = monthly_income.get("total_peak", 0) + monthly_income.get("total_demand", 0)
            month_general_income.append(total_month)
            # 年收益
            year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()

            # year_subquery = (
            #     incomes_ins.filter(income_date__range=(year_start, income_date))
            #     .values("income_date")
            #     .annotate(last_entry=Max("id"))
            #     .values("last_entry")
            # )

            year_income = incomes_ins.filter(income_date__range=(year_start, income_date)).aggregate(
                total_peak=Sum("peak_load_shifting"), total_demand=Sum("demand_side_response")
            )
            if not year_income["total_peak"]:
                year_income["total_peak"] = 0
            if not year_income["total_demand"]:
                year_income["total_demand"] = 0
            total_year = year_income["total_peak"] + year_income["total_demand"]
            year_general_income.append(total_year)
            # 所有收益
            all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()

            # all_subquery = (
            #     incomes_ins.filter(income_date__range=(all_start, income_date))
            #     .values("income_date")
            #     .annotate(last_entry=Max("id"))
            #     .values("last_entry")
            # )

            all_income = incomes_ins.filter(income_date__range=(all_start, income_date)).aggregate(
                total_peak=Sum("peak_load_shifting"), total_demand=Sum("demand_side_response")
            )
            if not all_income["total_peak"]:
                all_income["total_peak"] = 0
            if not all_income["total_demand"]:
                all_income["total_demand"] = 0
            total_all = all_income["total_peak"] + all_income["total_demand"]
            all_general_income.append(total_all)

        detail_dic["today_general_income"] = sum(today_general_income)
        detail_dic["year_general_income"] = sum(year_general_income)
        detail_dic["month_general_income"] = sum(month_general_income)
        detail_dic["all_general_income"] = sum(all_general_income)

        if lang == 'zh':
            if detail_dic["today_general_income"] >= 10000:
                detail_dic["today_general_income"] = [str(round(detail_dic["today_general_income"] / 10000, 2)), "万元"]
            else:
                detail_dic["today_general_income"] = [str(detail_dic["today_general_income"]), "元"]
            if detail_dic["month_general_income"] >= 10000:
                detail_dic["month_general_income"] = [str(round(detail_dic["month_general_income"] / 10000, 2)), "万元"]
            else:
                detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "元"]
            if detail_dic["year_general_income"] >= 10000:
                detail_dic["year_general_income"] = [str(round(detail_dic["year_general_income"] / 10000, 2)), "万元"]
            else:
                detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "元"]

            if detail_dic["all_general_income"] >= 10000:
                detail_dic["all_general_income"] = [str(round(detail_dic["all_general_income"] / 10000, 2)), "万元"]
            else:
                detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "元"]

        else:
            detail_dic["today_general_income"] = [str(detail_dic["today_general_income"]), "Yuan"]
            detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "Yuan"]
            detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "Yuan"]
            detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "Yuan"]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail_dic,
                },
            }
        )


# class IndexStationsView(APIView):
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request, pk):
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.filter(id=user_id).first()
#         # stations_ins = models.StationDetails.objects.filter(master_station__project_id=pk,
#         #                                                     master_station__project__user=user_ins).all()
#         temp_array = []
#         # .values(
#         #     "id", "station_name", "app", "unit__unit_name", "meter_count", "english_name"
#         # ))
#
#         # master_stations = models.MaterStation.objects.filter(project_id=pk, project__user=user_ins).all()
#         units = models.Unit.objects.filter(is_delete=0, station__master_station__project_id=pk, station__master_station__is_delete=0).all()
#         if not units.exists():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "project 不存在"},
#                 }
#             )
#
#         unit_list = []
#         # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#         for unit in units:
#             unit_dict = {
#                 "id": unit.id,
#                 "station_name": unit.station.master_station.name,
#                 "app": unit.station.app,
#                 "unit__unit_name": unit.unit_new_name,
#                 "meter_count": unit.station.meter_count,
#                 "english_name": unit.english_name
#             }
#
#             meter_ins = METER_DIC[unit.station.meter_count]
#             Fault_list = []
#             offline_list = []
#             num = 0
#             alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0, station_id=unit.station.id).exists()
#             json_data = {
#                 "app": unit.station.app,
#                 "station": unit.station.english_name,
#                 "body": [
#                     {
#                         "device": unit.pcs,
#                         "datatype": "status",
#                         "totalcall": "0",
#                         "body": [
#                             "Fault",
#                             "alarm",
#                         ],
#                     },
#                     {
#                         "device": unit.bms,
#                         "datatype": "status",
#                         "totalcall": "0",
#                         "body": [
#                             "GFault",
#                             "GAlarm",
#                         ],
#                     },
#                 ],
#             }
#
#             conn = get_redis_connection("3")
#
#             key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name, unit.pcs)
#             status_pcs = conn.get(key3)
#             if status_pcs:
#                 status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
#             else:
#                 status_pcs_dict = {}
#
#             key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name, unit.bms)
#             status_bms = conn.get(key6)
#             if status_bms:
#                 status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
#             else:
#                 status_bms_dict = {}
#
#             if not status_pcs_dict or not status_bms_dict:
#                 offline_list.append(1)  # 离线状态
#
#             else:
#                 GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
#                 Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
#
#                 if Fault and int(Fault) == 1:
#                     Fault_list.append(1)
#                 if GFault and int(GFault) == 1:
#                     Fault_list.append(1)
#             unit_dict["status"] = 1
#             if alarm_exist:
#                 # print(alarm_exist)
#                 unit_dict["status"] = 2
#             if 1 in Fault_list:
#                 unit_dict["status"] = 3
#             if 1 in offline_list:
#                 unit_dict["status"] = 4
#             if models.StationStatus.objects.get(station_id=unit.station_id).status == 4:
#                 unit_dict["status"] = 4
#             else:
#                 if not status_bms or not status_pcs:
#                     unit_dict["status"] = 5
#                 else:
#                     if alarm_exist:
#                         # print(alarm_exist)
#                         unit_dict["status"] = 2
#                     if 1 in Fault_list:
#                         unit_dict["status"] = 3
#                     if 1 in offline_list:
#                         unit_dict["status"] = 4
#
#             unit_list.append(unit_dict)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": unit_list,
#                 },
#             }
#         )


# class WebAlarmDetailView(APIView):
#     """告警详情"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request, pk):
#         user_id = request.user["user_id"]
#         project_ins = models.Project.objects.filter(id=pk, user=user_id).first()
#         if not project_ins:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "project 不存在"},
#                 }
#             )
#
#         stations_id = [s.id for s in project_ins.stationdetails_set.filter(is_delete=0).all()]
#         ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=stations_id, status=0)
#         alarm_ins = (
#             ins.filter(type__lt=3)
#             .values("status", "type", "start_time", "details", "id", "note", "station_id", "device")
#             .order_by("-start_time")
#         )
#         for alarm in alarm_ins:
#             station = models.StationDetails.objects.filter(id=alarm['station_id']).first()
#             station_name = station.station_name if station else '--'
#             alarm["details"] = station_name + ":" + alarm["device"] + alarm["details"]
#
#         # 统计 type = 0的告警数量
#         alarm_count = ins.filter(type=0).count()
#         # 统计 type = 1的故障数量
#         fault_count = ins.filter(type=1).count()
#         # 统计 type = 2的报警数量
#         warning_count = ins.filter(type=2).count()
#
#         # 构造 alarm_ins 返回数据
#         count = {"alarm_count": alarm_count, "fault_count": fault_count, "warning_count": warning_count}
#
#         return Response(
#             {"code": common_response_code.SUCCESS,
#              "data": {"message": "success", "detail": {"count": count, "alarms": alarm_ins}}}
#         )


# class Old_WebElectricityDayCountView(APIView):
#     """web逐时冲放电量"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request, pk):
#         user_id = request.user["user_id"]
#         # 获取 post 请求 types 参数
#         types = request.data.get("types", None)
#         # 获取 post 请求 time 参数
#         time_ = request.data.get("time", None)
#         project_ins = models.Project.objects.filter(id=pk, user=user_id).first()
#         if not project_ins:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "project 不存在"},
#                 }
#             )
#         # 如果时间为空 获取当天日期
#         if not time_:
#             time_ = datetime.datetime.now()  # 当天日期
#         else:
#             time_ = datetime.datetime.strptime(time_, '%Y-%m-%d')
#         # detail, a, b = ChargeDischargeCount(time_).get_projects(project_ins)
#         detail = get_project_sum_t_report_data(project_ins, time_)
#
#         for j in detail:
#             j['charge'] = round(j['charge'], 1)
#             j['discharge'] = round(j['discharge'], 1)
#             j['soc'] = round(j['soc'], 1)
#
#         success_dic = {
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#             },
#         }
#         success_dic["data"]["project"] = project_ins.name
#         success_dic["data"]["detail"] = detail
#         # if types:
#         #     success_dic["data"]["detail"] = get_type(types, success_dic["data"]["detail"])
#         return Response(success_dic)


# class WebElectricityDayCountView(APIView):
#     """web逐时冲放电量"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request, pk):
#         user_id = request.user["user_id"]
#         # 获取 post 请求 types 参数
#         types = request.data.get("types", None)
#         # 获取 post 请求 time 参数
#         time_ = request.data.get("time", None)
#         project_ins = models.Project.objects.filter(id=pk, user=user_id).first()
#         if not project_ins:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "project 不存在"},
#                 }
#             )
#         # 如果时间为空 获取当天日期
#         if not time_:
#             time_ = datetime.datetime.now()  # 当天日期
#         else:
#             time_ = datetime.datetime.strptime(time_, '%Y-%m-%d')
#         # detail, a, b = ChargeDischargeCount(time_).get_projects(project_ins)
#         detail = get_project_sum_t_data_from_ads_rhyc(project_ins, time_)
#
#         for j in detail:
#             j['charge'] = round(j['charge'], 1)
#             j['discharge'] = round(j['discharge'], 1)
#             j['soc'] = round(j['soc'], 1)
#
#         success_dic = {
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#             },
#         }
#         success_dic["data"]["project"] = project_ins.name
#         success_dic["data"]["detail"] = detail
#         # if types:
#         #     success_dic["data"]["detail"] = get_type(types, success_dic["data"]["detail"])
#         return Response(success_dic)


# class Old_WebElectricityWeekCountView(APIView):
#     """web历史 7 天冲放电量"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = WebPowerPlanHistorySerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"web历史 7 天冲放电量 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         project_name = ser.validated_data.get("project")
#         detail = []
#         project_ins = models.Project.objects.filter(is_used=1, english_name=project_name).first()
#         stations = models.StationDetails.objects.filter(is_delete=0, project=project_ins).all()
#         temp_array = list()
#         for station in stations:
#             station_detail = []
#             for i in range(1, 8):
#                 return_dic = {'charge': 0, 'discharge': 0}
#                 today = datetime.date.today()
#
#                 # 使用timedelta计算前一天日期
#                 target_day = today - datetime.timedelta(days=i)
#
#                 day_reports = models.FReport.objects.filter(day=target_day.strftime('%Y-%m-%d'),
#                                                             station=station.english_name, station_type=1).all()
#
#                 if day_reports.exists():
#                     return_dic["charge"] = round(sum([float(day_report.chag) for day_report in day_reports]), 2)
#                     return_dic["discharge"] = round(sum([float(day_report.disg) for day_report in day_reports]), 2)
#                     return_dic["day"] = target_day.strftime('%Y-%m-%d')
#                     station_detail.append(return_dic)
#
#             station_detail.reverse()
#             temp_array.append(station_detail)
#
#         if len(temp_array):
#             for ind, item in enumerate(temp_array):
#                 if ind == 0:
#                     detail = item
#                 else:
#                     for index, i in enumerate(item):
#                         detail[index]['charge'] += i['charge']
#                         detail[index]['discharge'] += i['discharge']
#
#             for j in detail:
#                 j['completion_rate'] = round(float(
#                                 j["discharge"] / j["charge"]) * 100) if j["charge"] else 100
#                 j['completion_rate'] = round(j['completion_rate'], 1)
#                 j['charge'] = round(j['charge'], 1)
#                 j['discharge'] = round(j['discharge'], 1)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "project": project_ins.name,
#                     "detail": detail,
#                 },
#             }
#         )


# class WebElectricityWeekCountView(APIView):
#     """web历史 7 天冲放电量"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = WebPowerPlanHistorySerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"web历史 7 天冲放电量 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         project_name = ser.validated_data.get("project")
#         detail = []
#         project_ins = models.Project.objects.filter(is_used=1, english_name=project_name).first()
#         master_station = models.MaterStation.objects.filter(project=project_ins, is_delete=0).all()
#
#         select_sql = ("SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
#                       " where station=%s and station_type=1 and day=%s")
#         temp_array = list()
#         for m_station in master_station:
#             stations = m_station.stationdetails_set.filter(is_delete=0).all()
#             for station in stations:
#                 station_detail = []
#                 if m_station.is_account != 1:
#                     for i in range(1, 8):
#                         return_dic = {'charge': 0, 'discharge': 0}
#                         today = datetime.date.today()
#
#                         # 使用timedelta计算前一天日期
#                         target_day = today - datetime.timedelta(days=i)
#
#                         result = ads_db_tool.select_one(select_sql, station.english_name,
#                                                         target_day.strftime('%Y-%m-%d'))
#                         if result:
#                             return_dic = {"charge": result['charge'], "discharge": result['discharge'],
#                                           "day": target_day.strftime('%Y-%m-%d')}
#
#                             station_detail.append(return_dic)
#
#                     station_detail.reverse()
#                     temp_array.append(station_detail)
#                 else:
#                     for i in range(1, 8):
#                         return_dic = {'charge': 0, 'discharge': 0}
#                         today = datetime.date.today()
#
#                         # 使用timedelta计算前一天日期
#                         target_day = today - datetime.timedelta(days=i)
#                         sql = """SELECT
#                              day,  chag, disg
#                              FROM ads_report_ems_chag_disg_1d
#                              WHERE
#                                 station = '{}'
#                                 AND
#                                 day = '{}'
#                                  """.format(station.english_name, target_day.strftime('%Y-%m-%d'))
#                         with connections['doris_ads_rhyc'].cursor() as ads_cursor:
#                             try:
#                                 # 获取查询结果
#                                 ads_cursor.execute(sql)
#                                 res = ads_cursor.fetchone()
#                             except Exception as e:
#                                 error_log.error(e)
#                                 return Response(
#                                     {
#                                         "code": common_response_code.SUMMARY_CODE,
#                                         "data": {
#                                             "message": "error",
#                                             "detail": '查询失败！',
#                                         }
#                                     }
#                                 )
#                         if res:
#                             return_dic = {"charge": res[1], "discharge": res[2],
#                                           "day": res[0]}
#
#                             station_detail.append(return_dic)
#
#                     station_detail.reverse()
#                     temp_array.append(station_detail)
#
#         # stations = models.StationDetails.objects.filter(project=project_ins).all()
#         #
#         # select_sql = ("SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
#         #               " where station=%s and station_type=1 and day=%s")
#         #
#         # temp_array = list()
#         # for station in stations:
#         #     station_detail = []
#         #     for i in range(1, 8):
#         #         return_dic = {'charge': 0, 'discharge': 0}
#         #         today = datetime.date.today()
#         #
#         #         # 使用timedelta计算前一天日期
#         #         target_day = today - datetime.timedelta(days=i)
#         #
#         #         # day_reports = models.FReport.objects.filter(day=target_day.strftime('%Y-%m-%d'),
#         #         #                                             station=station.english_name, station_type=1).all()
#         #         #
#         #         # if day_reports.exists():
#         #         #     return_dic["charge"] = round(sum([float(day_report.chag) for day_report in day_reports]), 2)
#         #         #     return_dic["discharge"] = round(sum([float(day_report.disg) for day_report in day_reports]), 2)
#         #         #     return_dic["day"] = target_day.strftime('%Y-%m-%d')
#         #         #     station_detail.append(return_dic)
#         #
#         #         # print(1233, select_sql, station.english_name, target_day.strftime('%Y-%m-%d'))
#         #         result = ads_db_tool.select_one(select_sql, station.english_name, target_day.strftime('%Y-%m-%d'))
#         #         if result:
#         #             return_dic = {"charge": result['charge'], "discharge": result['discharge'],
#         #                           "day": target_day.strftime('%Y-%m-%d')}
#         #
#         #             station_detail.append(return_dic)
#         #
#         #     station_detail.reverse()
#         #     temp_array.append(station_detail)
#
#         if len(temp_array):
#             # for ind, item in enumerate(temp_array):
#             #     if ind == 0:
#             #         detail = item
#             #     else:
#             #         for index, i in enumerate(item):
#             #             detail[index]['charge'] += i['charge']
#             #             detail[index]['discharge'] += i['discharge']
#
#             detail = get_common_results_from_ads_report_chag_disg_1d(temp_array)
#             detail = sorted(detail, key=lambda x: x["day"])
#
#             for j in detail:
#                 j['completion_rate'] = round(float(
#                                 j["discharge"] / j["charge"]) * 100) if j["charge"] else 100
#                 j['completion_rate'] = round(j['completion_rate'], 1)
#                 j['charge'] = round(j['charge'], 1)
#                 j['discharge'] = round(j['discharge'], 1)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "project": project_ins.name,
#                     "detail": detail,
#                 },
#             }
#         )


# class SocHistoryView(APIView):
#     """web soc 历史"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = WebSocSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error(f"web soc 历史 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         # date_time = ser.validated_data["time"]
#         # 获取当前事件对象
#         date_time = datetime.datetime.now()
#         project = ser.validated_data.get("project")
#         count_ins = SocV3(date_time)
#         success_dic = {
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#                 "units": [],
#             },
#         }
#         project_ins = models.Project.objects.filter(english_name=project, is_used=1).first()
#         # 获取项目下的所有单元
#         units_ins = models.Unit.objects.filter(is_delete=0, station__master_station__project=project_ins).all()
#         for unit in units_ins:
#             detail = count_ins.get_unit(unit)
#             unit_dic = {"unit": f"{unit.station.master_station.name}:{unit.unit_new_name}", "detail": detail}
#             success_dic["data"]["units"].append(unit_dic)
#
#         return Response(success_dic)


# class DayPowerView(APIView):
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = WebPowerSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error("历史功率:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         project = ser.validated_data.get("project")
#         project_ins = models.Project.objects.filter(english_name=project, is_used=1).first()
#         # today = ser.validated_data.get("time", None)
#         # if today:
#             # now = str(int(time.mktime(today.timetuple())))
#         units_ins = models.Unit.objects.filter(is_delete=0, station__master_station__project=project_ins).all()
#         return_dic = {
#             "code": common_response_code.SUCCESS,
#             "data": {
#                 "message": "success",
#                 "details": [],
#             },
#         }
#
#         # 获取当前时间
#         time_str = ser.validated_data.get("time") or datetime.datetime.now().replace(hour=0, minute=0, second=0)
#         # 开始时间
#         start_time = time_str
#         # end_time = str(start_time).replace('00:00:00', '23:59:59')
#         end_time = time_str.replace(hour=23, minute=59, second=59)
#         # start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#         # end_datatime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
#
#         for unit in units_ins:
#             station_english_name = unit.station.english_name
#             device_type = 'pcs'
#             device = getattr(unit, device_type)
#
#             results = time_range_by_dwd_for_web(station_english_name, 'measure', device_type, device,
#                                         start_time, end_time, 'P', 'Q')
#
#             if results:
#                 unit_dic = {"unit": f"{unit.station.master_station.name}:{unit.unit_new_name}", "detail": []}
#                 for result in results:
#                     unit_dic["detail"].append({"active_power": result['P'], "reactive_power": result['Q'], "time":
#                         result['time']})
#                 return_dic["data"]["details"].append(unit_dic)
#
#         return Response(return_dic)


# class IndexRunDaysView(APIView):
#     """首页运行天数"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request, pk):
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.filter(id=user_id).first()
#         project_ins = models.Project.objects.filter(id=pk, user=user_ins, is_used=1).first()
#         if not project_ins:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "项目不存在"},
#                 }
#             )
#         # 获取当前时间
#         now = datetime.datetime.now()
#         # 获取项目接入时间
#         register_time = project_ins.in_time
#         # 获取项目运行天数
#         run_days = (now - register_time).days
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": {"days": run_days}},
#             }
#         )


# class RealTimeBatteryChargingDischargingView(APIView):
#     """实时可充放电量"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = WebSocSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error(f"web soc 历史 =>{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         # date_time = ser.validated_data["time"]
#         # 获取当前事件对象
#         # date_time = datetime.datetime.now()
#         project = ser.validated_data.get("project")
#         # count_ins = SocV3(date_time)
#
#         project_ins = models.Project.objects.filter(english_name=project, is_used=1).first()
#         # 获取项目下的所有单元
#         units_ins = models.Unit.objects.filter(is_delete=0, station__master_station__project=project_ins).all()
#         # print(units_ins)
#         detail = []
#         for unit in units_ins:
#             station_english_name = unit.station.english_name
#             station_app = unit.station.app
#             bms = unit.bms
#             pcs = unit.pcs
#             # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
#             # request_json = {
#             #
#             #     "app": station_app,
#             #     "station": station_english_name,
#             #     "body": [
#             #         {
#             #             "device": bms,
#             #             "datatype": "measure",
#             #             "body": ['SOC', 'MUMax', 'MUMin', 'MTMax', 'MTMin']
#             #         },
#             #         {
#             #             "device": pcs,
#             #             "datatype": "measure",
#             #             "body": ['P']
#             #         },
#             #     ]
#             # }
#             # response = requests.post(url=url, json=request_json)
#             # datas = response.json()["body"]
#
#             conn = get_redis_connection("3")
#
#             key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, bms)
#             measure_bms = conn.get(key1)
#             if measure_bms:
#                 measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
#             else:
#                 measure_bms_dict = {}
#
#             key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, pcs)
#             measure_pcs = conn.get(key2)
#             if measure_pcs:
#                 measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
#             else:
#                 measure_pcs_dict = {}
#
#             result = {"unit": f"{unit.station.master_station.name}:{unit.unit_new_name}"}
#
#             if measure_bms_dict:
#
#                 result['SOC'] = round(float(measure_bms_dict.get('SOC')), 2) if (measure_bms_dict.get('SOC') and
#                                                                                  measure_bms_dict.get('SOC') not in
#                                                                                  EMPTY_STR_LIST) else 0
#                 result['MUMax'] = round(float(measure_bms_dict.get('MUMax')), 2) if measure_bms_dict.get('MUMax') and measure_bms_dict.get('MUMax') not in EMPTY_STR_LIST else 0.0
#                 result['MTMax'] = round(float(measure_bms_dict.get('MTMax')), 2) if measure_bms_dict.get('MTMax') and measure_bms_dict.get('MTMax') not in EMPTY_STR_LIST else 0.0
#                 result['MUMin'] = round(float(measure_bms_dict.get('MUMin')), 2) if measure_bms_dict.get('MUMin') and measure_bms_dict.get('MUMin') not in EMPTY_STR_LIST else 0.0
#                 result['MTMin'] = round(float(measure_bms_dict.get('MTMin')), 2) if measure_bms_dict.get('MTMin') and measure_bms_dict.get('MTMin') not in EMPTY_STR_LIST else 0.0
#             else:
#                 result['SOC'] = 0
#                 result['MUMax'] = 0.0
#                 result['MTMax'] = 0.0
#                 result['MUMin'] = 0.0
#                 result['MTMin'] = 0.0
#
#             if measure_pcs_dict:
#                 if measure_pcs_dict['P'] and measure_pcs_dict['P'] not in EMPTY_STR_LIST:
#                     if float(measure_pcs_dict['P']) > 0:
#                         result['status'] = -1
#                     elif float(measure_pcs_dict['P']) < 0:
#                         result['status'] = 1
#                     else:
#                         result['status'] = 0
#                 else:
#                     result['status'] = 0
#             else:
#                 result['status'] = 0
#             if result['SOC'] == 100:
#                 result['status'] = 2
#
#             detail.append(result)
#
#         detail = sorted(detail, key=lambda x: x["unit"])
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": detail,
#                 },
#             }
#         )


class SystemMonitorProjectsView(APIView):
    """集中监控-系统监控: 项目列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        try:
            user = models.UserDetails.objects.get(id=request.user.get('user_id'))
            projects = user.project_set.filter(is_used=1).all()
            if projects.exists():
                details = [{"id": project.id, "name": project.name, "en_name": project.english_name} for project in projects]
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": details,
                        },
                    }
                )
            else:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {
                            "message": "no data",
                            "detail": "集中监控-系统监控: 当前用户无项目信息",
                        },
                    }
                )

        except Exception as e:
            error_log.error("集中监控-系统监控: 用户不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 用户不存在!",
                    },
                }
            )


class SystemMonitorStationsView(APIView):
    """集中监控-系统监控: 项目-站列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request, pk):
        try:
            project = models.Project.objects.get(id=pk)
            stations = project.stationdetails_set.filter(is_delete=0).all()
            if stations.exists():
                details = [{"id": station.id, "name": station.master_station.name + '--' + station.station_name,
                            "en_name": station.english_name} for station in stations]
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": details,
                        },
                    }
                )
            else:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {
                            "message": "no data",
                            "detail": "集中监控-系统监控: 当前项目无站信息",
                        },
                    }
                )

        except Exception as e:
            error_log.error("集中监控-系统监控: 项目不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 项目不存在!",
                    },
                }
            )


class SystemMonitorView(APIView):
    """集中监控-系统监控"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def count_charge_discharge(self, new_list_, charge, discharge):
        # new_list_ = sorted(datas, key=lambda x: x["time"])
        first_charge = new_list_[0][charge]
        first_discharge = new_list_[0][discharge]

        last_charge = new_list_[-1][charge]
        last_discharge = new_list_[-1][discharge]
        charge_count = abs(Decimal(last_charge) - Decimal(first_charge))
        discharge_count = abs(Decimal(last_discharge) - Decimal(first_discharge))
        return charge_count, discharge_count

    def get(self, request, pk):
        try:
            station = models.StationDetails.objects.get(id=pk)
        except Exception as e:
            error_log.error("集中监控-系统监控: 查询站出现异常：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {
                        "message": "error",
                        "detail": "集中监控-系统监控: 站不存在!",
                    },
                }
            )

        conn_ = pool.connection()
        cursor = conn_.cursor()
        detail = dict()
        detail["station"] = {"page_area": 0}

        station_english_name = station.english_name
        station_app = station.app
        meter_type = station.meter_type
        meter_dic = METER_DIC.get(meter_type)
        charge_key = meter_dic.get("charge")
        discharge_key = meter_dic.get("discharge")
        device = meter_dic.get("device")

        conn = get_redis_connection("3")

        # 查询站的信息：变压器容量、并网点功率、今日充电量、今日放电量等
        # result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'measure', 'EMS', 'TFM', 'PCC')

        key1_ = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, 'EMS')
        measure_ems = conn.get(key1_)
        if measure_ems:
            measure_ems_dict = json.loads(json.loads(measure_ems.decode("utf-8")))
        else:
            measure_ems_dict = {}

        if measure_ems_dict:
            detail["station"]['tfm'] = {
                "value": measure_ems_dict.get('TFM') if measure_ems_dict.get('TFM') not in EMPTY_STR_LIST else station.transformer_capacity,
                "unit": 'kVA',
                "desc": '变压器容量'
            }

            detail["station"]['pcc'] = {
                "value": measure_ems_dict.get('PCC') if measure_ems_dict.get('PCC') not in EMPTY_STR_LIST else '--',
                "unit": 'kW',
                "desc": '并网点功率'
            }

            detail["station"]['day_charge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日充电量"
            }
            detail["station"]['day_discharge'] = {
                "value": 0,
                "unit": "kWh",
                "desc": "今日放电量"
            }
            detail['units'] = []
            detail["station"]['meter_position'] = {
                "value": station.meter_position if station.meter_position else '1',
                "enums": {
                    1: '电表前置',
                    2: '电表后置',
                },
                "desc": "电表位置"
            }
        else:
            detail["station"]['tfm'] = {
                "value": station.transformer_capacity,
                "unit": 'kVA',
                "desc": '变压器容量'
            }

            detail["station"]['pcc'] = {
                "value": '--',
                "unit": 'kW',
                "desc": '并网点功率'
            }

            if station.slave > -1:
                detail["station"]['day_charge'] = {
                    "value": 0,
                    "unit": "kWh",
                    "desc": "今日充电量"
                }
                detail["station"]['day_discharge'] = {
                    "value": 0,
                    "unit": "kWh",
                    "desc": "今日放电量"
                }
            else:
                detail["station"]['day_charge'] = {
                    "value": '--',
                    "unit": "kWh",
                    "desc": "今日充电量"
                }
                detail["station"]['day_discharge'] = {
                    "value": '--',
                    "unit": "kWh",
                    "desc": "今日放电量"
                }
            detail['units'] = []
            detail["station"]['meter_position'] = {
                "value": station.meter_position if station.meter_position else '1',
                "enums": {
                    1: '电表前置',
                    2: '电表后置',
                },
                "desc": "电表位置"
            }

        units = station.unit_set.filter(is_delete=0).all()

        now_time_ = int(time.time())
        now_time = datetime.datetime.fromtimestamp(now_time_).strftime("%Y-%m-%d %H:%M:%S")
        start_time = now_time.split(' ')[0] + ' 00:00:00'
        # end_time = start_time.replace('00:00:00', '23:59:59')
        start_datatime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_datatime = datetime.datetime.strptime(now_time, "%Y-%m-%d %H:%M:%S")

        is_online = 0             # 判断并网点是否是离线状态

        # 查询储能单元信息
        if units.exists():
            num = 1
            for unit in units:
                unit_dict = dict()
                unit_dict['id'] = unit.id
                unit_dict['page_area'] = num

                # 计算充放电量
                bms_ = getattr(unit, device)
                pcs = unit.pcs

                results = select_range_data_from_dwd_rhyc(station_english_name, 'cumulant', device, bms_, start_datatime,
                                                       end_datatime, charge_key, discharge_key)

                if results:
                    unit_charge, unit_discharge = self.count_charge_discharge(results, charge_key, discharge_key)
                    unit_dict['day_charge'] = {
                        "value": unit_charge,
                        "unit": "kWh",
                        "desc": "今日充电量"
                    }
                    unit_dict['day_discharge'] = {
                        "value": unit_discharge,
                        "unit": "kWh",
                        "desc": "今日放电量"
                    }
                    if detail['station']['day_charge']['value'] != '--':             # 兼容EMS级联主从的从站查询的EMS数据为‘--’，储能单元的day_discharge和day_charge不为‘--’
                        detail['station']['day_charge']['value'] += unit_charge
                    if detail['station']['day_discharge']['value'] != '--':
                        detail['station']['day_discharge']['value'] += unit_discharge

                    is_online += 1

                else:
                    unit_dict['day_charge'] = {
                        "value": '--',
                        "unit": "kWh",
                        "desc": "今日充电量"
                    }
                    unit_dict['day_discharge'] = {
                        "value": '--',
                        "unit": "kWh",
                        "desc": "今日放电量"
                    }

                # PCS运行状态
                # result_ = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'discrete', pcs, 'PRse')

                key6_ = "Business:TRANS_TIMING_{}_{}_{}".format('DISCRETE', station_english_name, pcs)
                discrete_pcs = conn.get(key6_)
                if discrete_pcs:
                    discrete_pcs_dict = json.loads(json.loads(discrete_pcs.decode("utf-8")))
                else:
                    discrete_pcs_dict = {}
                # pcs 测量量
                # pcs_measure_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'measure', pcs, 'PUa', 'PUb', 'PUc', 'PUab', 'PUbc', 'PUca', 'Ia', 'Ib', 'Ic', 'P')
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, pcs)
                measure_pcs = conn.get(key2)

                if measure_pcs:
                    measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
                else:
                    measure_pcs_dict = {}
                if measure_pcs_dict:
                    value = '1'
                    if measure_pcs_dict.get('P') not in EMPTY_STR_LIST:
                        p = float(measure_pcs_dict.get('P'))
                        if p >= 1:
                            value = '2'
                        elif p < 0:
                            value = '3'
                        else:
                            value = '1'
                    unit_dict['pcs_prse'] = {
                        "value": value,
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }
                else:
                    unit_dict['pcs_prse'] = {
                        "value": '1',
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }

                # pcs 状态量
                # pcs_status_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'status', pcs, 'PCStu', 'alarm', 'Fault', 'DCse')
                key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, pcs)
                status_pcs = conn.get(key3)
                if status_pcs:
                    status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                else:
                    status_pcs_dict = {}

                if status_pcs_dict:
                    unit_dict['pcs_online'] = {
                        "value": "1",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{} PCS在线离线状态".format(pcs)
                    }

                    # 离线的不要显示充放电状态，默认待机: pcs离线的，充放电状态按待机
                    if unit_dict['pcs_online']['value'] == '0':
                        unit_dict['pcs_prse'] = {
                            "value": '1',
                            "enums": {
                                0: '停机',
                                1: '待机',
                                2: '放电运行',
                                3: '充电运行',
                                4: '零功率运行'
                            },
                            "desc": "{} PCS运行状态".format(pcs)
                        }

                    unit_dict['pcs_tu'] = {
                        "value": status_pcs_dict.get('PCStu') if status_pcs_dict.get('PCStu') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "PCS开关机"
                    }
                    unit_dict['pcs_alarm'] = {
                        "value": status_pcs_dict.get('alarm') if status_pcs_dict.get('alarm') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{} PCS报警状态".format(pcs)
                    }
                    unit_dict['pcs_fault'] = {
                        "value": status_pcs_dict.get('Fault') if status_pcs_dict.get('Fault') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{} PCS故障状态".format(pcs)
                    }
                    unit_dict['pcs_dcse'] = {
                        "value": status_pcs_dict.get('DCse') if status_pcs_dict.get('DCse') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{} 直流接触器状态".format(pcs)
                    }
                else:
                    unit_dict['pcs_online'] = {
                        "value": "0",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{} PCS在线离线状态".format(pcs)
                    }
                    unit_dict['pcs_prse'] = {
                        "value": '1',
                        "enums": {
                            0: '停机',
                            1: '待机',
                            2: '放电运行',
                            3: '充电运行',
                            4: '零功率运行'
                        },
                        "desc": "{} PCS运行状态".format(pcs)
                    }
                    unit_dict['pcs_tu'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "PCS开关机"
                    }
                    unit_dict['pcs_alarm'] = {
                        "value": '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{} PCS报警状态".format(pcs)
                    }
                    unit_dict['pcs_fault'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{} PCS故障状态".format(pcs)
                    }
                    unit_dict['pcs_dcse'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{} 直流接触器状态".format(pcs)
                    }



                if measure_pcs_dict:
                    unit_dict['pcs_ua'] = {
                        "value": measure_pcs_dict.get('PUa') or measure_pcs_dict.get('PUab'),
                        "unit": "V",
                        "desc": "{} A相电网相电压".format(pcs) if measure_pcs_dict.get('PUa') else "{} AB相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_ub'] = {
                        "value": measure_pcs_dict.get('PUb') or measure_pcs_dict.get('PUbc'),
                        "unit": "V",
                        "desc": "{} B相电网相电压".format(pcs) if measure_pcs_dict.get('PUb') else "{} BC相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_uc'] = {
                        "value": measure_pcs_dict.get('PUc') or measure_pcs_dict.get('PUca'),
                        "unit": "V",
                        "desc": "{} C相电网相电压".format(pcs) if measure_pcs_dict.get('PUc') else "{} CA相电网相电压".format(pcs)
                    }
                    unit_dict['pcs_ia'] = {
                        "value": measure_pcs_dict.get('Ia') if measure_pcs_dict.get('Ia') and measure_pcs_dict.get('Ia') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} A相电流".format(pcs)
                    }
                    unit_dict['pcs_ib'] = {
                        "value": measure_pcs_dict.get('Ib') if measure_pcs_dict.get('Ib') and measure_pcs_dict.get('Ib') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} B相电流".format(pcs)
                    }
                    unit_dict['pcs_ic'] = {
                        "value": measure_pcs_dict.get('Ic') if measure_pcs_dict.get('Ic') and measure_pcs_dict.get('Ic') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{} C相电流".format(pcs)
                    }
                    unit_dict['pcs_p'] = {
                        "value": measure_pcs_dict.get('P') if measure_pcs_dict.get('P') and measure_pcs_dict.get('P') not in EMPTY_STR_LIST else '--',
                        "unit": "kW",
                        "desc": "{}输出总有功功率".format(pcs)
                    }

                    # 非标的“七章公园”运行状态特殊处理
                    if station_english_name == 'HZDC101':
                        if unit_dict['pcs_online']['value'] == '0':
                            unit_dict['pcs_prse'] = {
                                "value": '1',
                                "enums": {
                                    0: '停机',
                                    1: '待机',
                                    2: '放电运行',
                                    3: '充电运行',
                                    4: '零功率运行'
                                },
                                "desc": "{} PCS运行状态".format(pcs)
                            }
                        else:
                            if unit_dict['pcs_p']['value'] == '--':
                                unit_dict['pcs_prse'] = {
                                    "value": '1',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            elif float(unit_dict['pcs_p']['value']) > 0:
                                unit_dict['pcs_prse'] = {
                                    "value": '2',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            elif float(unit_dict['pcs_p']['value']) == 0:
                                unit_dict['pcs_prse'] = {
                                    "value": '1',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                            else:
                                unit_dict['pcs_prse'] = {
                                    "value": '3',
                                    "enums": {
                                        0: '停机',
                                        1: '待机',
                                        2: '放电运行',
                                        3: '充电运行',
                                        4: '零功率运行'
                                    },
                                    "desc": "{} PCS运行状态".format(pcs)
                                }
                    # end
                else:
                    pcs_measure_result = select_latest_data_from_dwd_rhyc(station_english_name,
                                                                        'measure', 'pcs', pcs,
                                                                           'PUa', 'PUb', 'PUc', 'PUab',
                                                                        'PUbc', 'PUca', 'Ia', 'Ib', 'Ic', 'P')
                    if pcs_measure_result:
                        unit_dict['pcs_ua'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} A相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUa') else "{} AB相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_ub'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} B相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUb') else "{} BC相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_uc'] = {
                            "value": '--',
                            "unit": "V",
                            "desc": "{} C相电网相电压".format(pcs) if pcs_measure_result.get(
                                'PUc') else "{} CA相电网相电压".format(pcs)
                        }
                        unit_dict['pcs_ia'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} A相电流".format(pcs)
                        }
                        unit_dict['pcs_ib'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} B相电流".format(pcs)
                        }
                        unit_dict['pcs_ic'] = {
                            "value": '--',
                            "unit": "A",
                            "desc": "{} C相电流".format(pcs)
                        }
                        unit_dict['pcs_p'] = {
                            "value": '--',
                            "unit": "kW",
                            "desc": "{}输出总有功功率".format(pcs)
                        }

                # bms 状态量
                # bms_status_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name, 'status', bms_, 'GFault', 'HPCCse', 'GAlarm', 'ComFau')
                key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station_english_name, bms_)
                status_bms = conn.get(key6)
                if status_bms:
                    status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                else:
                    status_bms_dict = {}
                if status_bms_dict:
                    unit_dict['bms_online'] = {
                        "value": "1",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{}在线离线状态".format(bms_)
                    }

                    unit_dict['bms_hpccse'] = {
                        "value": status_bms_dict.get('HPCCse') if status_bms_dict.get('HPCCse') and status_bms_dict.get('HPCCse') not in EMPTY_STR_LIST else '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{}高压闭合状态".format(bms_)
                    }
                    unit_dict['bms_alarm'] = {
                        "value": status_bms_dict.get('GAlarm') if status_bms_dict.get('GAlarm') and status_bms_dict.get('GAlarm') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{}总报警".format(bms_)
                    }
                    unit_dict['bms_fault'] = {
                        "value": status_bms_dict.get('GFault') if status_bms_dict.get('GFault') and status_bms_dict.get('GFault') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}总故障".format(bms_)
                    }
                    unit_dict['bms_comfau'] = {
                        "value": status_bms_dict.get('ComFau') if status_bms_dict.get('ComFau') and status_bms_dict.get('ComFau') not in EMPTY_STR_LIST else '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}压缩机故障".format(bms_)
                    }
                else:
                    unit_dict['bms_online'] = {
                        "value": "0",
                        "enums": {
                            0: '离线',
                            1: '在线',
                        },
                        "desc": "{}在线离线状态".format(bms_)
                    }

                    unit_dict['bms_hpccse'] = {
                        "value": '1',
                        "enums": {
                            0: '断开',
                            1: '闭合'
                        },
                        "desc": "{}高压闭合状态".format(bms_)
                    }
                    unit_dict['bms_alarm'] = {
                        "value": '0',
                        "enums": {
                            0: '无报警',
                            1: '报警'
                        },
                        "desc": "{}总报警".format(bms_)
                    }
                    unit_dict['bms_fault'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}总故障".format(bms_)
                    }
                    unit_dict['bms_comfau'] = {
                        "value": '0',
                        "enums": {
                            0: '无故障',
                            1: '故障'
                        },
                        "desc": "{}压缩机故障".format(bms_)
                    }

                # bms 测量量
                # bms_measure_result = select_realtime_data_from_rhyc(cursor, station_app, station_english_name,
                #                                                     'measure', bms_, 'U', 'I', 'SOC',
                #                                                     'MUMax', 'MUMin', 'MTMax', 'MTMin', 'ET', 'RWT',
                #                                                     'IPV', 'OPV')

                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_english_name, bms_)
                measure_bms = conn.get(key1)
                if measure_bms:
                    measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
                else:
                    measure_bms_dict = {}

                if measure_bms_dict:
                    unit_dict['bms_u'] = {
                        "value": measure_bms_dict.get('U') if measure_bms_dict.get('U') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}总电压".format(bms_)
                    }
                    unit_dict['bms_i'] = {
                        "value": measure_bms_dict.get('I') if measure_bms_dict.get('I') not in EMPTY_STR_LIST else '--',
                        "unit": "A",
                        "desc": "{}总电流".format(bms_)
                    }
                    unit_dict['bms_soc'] = {
                        "value": measure_bms_dict.get('SOC') if measure_bms_dict.get('SOC') not in EMPTY_STR_LIST else '--',
                        "unit": "%",
                        "desc": "{} SOC".format(bms_)
                    }
                    unit_dict['bms_umax'] = {
                        "value": measure_bms_dict.get('MUMax') if measure_bms_dict.get('MUMax') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}最高单体电压".format(bms_)
                    }
                    unit_dict['bms_umin'] = {
                        "value": measure_bms_dict.get('MUMin') if measure_bms_dict.get('MUMin') not in EMPTY_STR_LIST else '--',
                        "unit": "V",
                        "desc": "{}最低单体电压".format(bms_)
                    }
                    unit_dict['bms_tmax'] = {
                        "value": measure_bms_dict.get('MTMax') if measure_bms_dict.get('MTMax') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}最高单体温度".format(bms_)
                    }
                    unit_dict['bms_tmin'] = {
                        "value": measure_bms_dict.get('MTMin') if measure_bms_dict.get('MTMin') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}最低单体温度".format(bms_)
                    }

                    unit_dict['bms_et'] = {
                        "value": measure_bms_dict.get('ET') if measure_bms_dict.get('ET') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}出水温度".format(bms_)
                    }
                    unit_dict['bms_rwt'] = {
                        "value": measure_bms_dict.get('RWT') if measure_bms_dict.get('RWT') not in EMPTY_STR_LIST else '--',
                        "unit": "℃",
                        "desc": "{}回水温度".format(bms_)
                    }
                    unit_dict['bms_ipv'] = {
                        "value": measure_bms_dict.get('IPV') if measure_bms_dict.get('IPV') not in EMPTY_STR_LIST else '--',
                        "unit": "bar",
                        "desc": "{}进水口压力值".format(bms_)
                    }
                    unit_dict['bms_opv'] = {
                        "value": measure_bms_dict.get('OPV') if measure_bms_dict.get('OPV') not in EMPTY_STR_LIST else '--',
                        "unit": "bar",
                        "desc": "{}出水口压力值".format(bms_)
                    }

                else:
                    unit_dict['bms_u'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}总电压".format(bms_)
                    }
                    unit_dict['bms_i'] = {
                        "value": '--',
                        "unit": "A",
                        "desc": "{}总电流".format(bms_)
                    }
                    unit_dict['bms_soc'] = {
                        "value": '--',
                        "unit": "%",
                        "desc": "{} SOC".format(bms_)
                    }
                    unit_dict['bms_umax'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}最高单体电压".format(bms_)
                    }
                    unit_dict['bms_umin'] = {
                        "value": '--',
                        "unit": "V",
                        "desc": "{}最低单体电压".format(bms_)
                    }
                    unit_dict['bms_tmax'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}最高单体温度".format(bms_)
                    }
                    unit_dict['bms_tmin'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}最低单体温度".format(bms_)
                    }

                    unit_dict['bms_et'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}出水温度".format(bms_)
                    }
                    unit_dict['bms_rwt'] = {
                        "value": '--',
                        "unit": "℃",
                        "desc": "{}回水温度".format(bms_)
                    }
                    unit_dict['bms_ipv'] = {
                        "value": '--',
                        "unit": "bar",
                        "desc": "{}进水口压力值".format(bms_)
                    }
                    unit_dict['bms_opv'] = {
                        "value": '--',
                        "unit": "bar",
                        "desc": "{}出水口压力值".format(bms_)
                    }

                detail['units'].append(unit_dict)
                num += 1


        if not is_online:
            detail["station"]['day_charge'] = {
                "value": '--',
                "unit": "kWh",
                "desc": "今日充电量"
            }
            detail["station"]['day_discharge'] = {
                "value": '--',
                "unit": "kWh",
                "desc": "今日放电量"
            }

        try:
            cursor.close()
            conn.close()
        except:
            pass

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail,
                },
            }
        )


class VersionNumberView(APIView):
    """
    版本信息查看
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request, pk):
        try:
            project = models.Project.objects.get(id=pk, is_used=1)
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": f"项目:{pk}不存在!",
                    },
                }
            )

        return_dict = {
            "ems": [],
            "pcs": [],
            "bms": []
        }

        # stations = project.stationdetails_set.all()
        master_stations = project.materstation_set.filter(is_delete=0).all()
        if master_stations.exists():
            conn = get_redis_connection("3")
            for master_station in master_stations:
                slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                if slave_stations.exists():
                    for slave_station in slave_stations:
                        if slave_station.slave == 0 or slave_station.slave == -1:
                            # 查询网关版本号
                            station_ems_list = []
                            # key1 = str(english_name + "_" + app + "_" + unit_["pcs"] + "_" + "EFPR")
                            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, 'EMS')
                            measure_ems = conn.get(key1)
                            if measure_ems:
                                measure_ems_str = measure_ems.decode("utf-8")
                                measure_ems_dict = json.loads(json.loads(measure_ems_str))
                                for k, v in Version_Dict['EMS'].items():
                                    temp_dict = {'name': v['name'],
                                                 'point': k,
                                                 'value': measure_ems_dict.get(k) if measure_ems_dict.get(k) not in EMPTY_STR_LIST else '--',
                                                 'note': v['note']}
                                    station_ems_list.append(temp_dict)
                            else:
                                for k, v in Version_Dict['EMS'].items():
                                    temp_dict = {'name': v['name'],
                                                 'point': k,
                                                 'value': '--',
                                                 'note': v['note']}
                                    station_ems_list.append(temp_dict)

                            station_ems_dict_ = {"statin": master_station.name, "version": station_ems_list}
                            return_dict['ems'].append(station_ems_dict_)

                        if slave_station.slave != 0:
                            units = slave_station.unit_set.filter(is_delete=0).all()
                            if units.exists():
                                for unit in units:
                                    # 查询PCS版本
                                    unit_pcs_list = []
                                    key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE',
                                                                                   slave_station.english_name, unit.pcs)
                                    measure_pcs = conn.get(key2)
                                    if measure_pcs:
                                        measure_pcs_str = measure_pcs.decode("utf-8")
                                        measure_pcs_dict = json.loads(json.loads(measure_pcs_str))
                                        for k, v in Version_Dict['PCS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': measure_pcs_dict.get(k) if measure_pcs_dict.get(k) not in EMPTY_STR_LIST
                                                         else '--',
                                                         'note': v['note']}
                                            unit_pcs_list.append(temp_dict)

                                    else:
                                        for k, v in Version_Dict['PCS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': '--',
                                                         'note': v['note']}
                                            unit_pcs_list.append(temp_dict)

                                    unit_pcs_dict_ = {"unit": master_station.name + ' ' + unit.unit_new_name,
                                                      "version": unit_pcs_list}
                                    return_dict['pcs'].append(unit_pcs_dict_)

                                    unit_bms_list = []
                                    key3 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE',
                                                                                   slave_station.english_name, unit.bms)
                                    measure_bms = conn.get(key3)
                                    if measure_bms:
                                        measure_bms_str = measure_bms.decode("utf-8")
                                        measure_bms_dict = json.loads(json.loads(measure_bms_str))
                                        for k, v in Version_Dict['BMS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': measure_bms_dict.get(k) if measure_bms_dict.get(k) not in EMPTY_STR_LIST
                                                         else '--',
                                                         'note': v['note']}
                                            unit_bms_list.append(temp_dict)

                                    else:
                                        for k, v in Version_Dict['BMS'].items():
                                            temp_dict = {'name': v['name'],
                                                         'point': k,
                                                         'value': '--',
                                                         'note': v['note']}
                                            unit_bms_list.append(temp_dict)

                                    unit_bms_dict_ = {"unit": master_station.name + ' ' + unit.unit_new_name,
                                                      "version": unit_bms_list}
                                    return_dict['bms'].append(unit_bms_dict_)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                },
            }
        )


class ProjectStationView(APIView):
    """
    项目并网点列表
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')

        projects = models.Project.objects.filter(is_used=1, user__id=user_id).values(
            'id',
            'name'
        )

        data = []
        # Redis
        conn = get_redis_connection("3")
        for project in projects:
            p_data = {
                'id': project['id'],
                'name': project['name']
            }
            # 查询主站
            stations = models.MaterStation.objects.filter(project_id=project['id'], is_delete=0).all()
            station_list = []
            for station in stations:
                station_dict = {
                    'id': station.id,
                    'name': station.name,
                    'rated_power': 0,
                    'rated_capacity': 0,
                    'realtime_power': 0
                }
                slaves = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0)).values(
                    'rated_power',
                    'rated_capacity',
                    'english_name',
                    'id'
                )
                for s_info in slaves:
                    station_dict['rated_power'] += float(s_info['rated_power'])
                    station_dict['rated_capacity'] += float(s_info['rated_capacity'])
                    units = models.Unit.objects.filter(is_delete=0, station_id=s_info['id']).values('pcs')
                    for unit in units:
                        pcs = conn.get(
                            'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', s_info.get('english_name'),
                                                                    unit.get('pcs')))
                        if not pcs:
                            p = 0
                        else:
                            pcs = eval(eval(pcs))
                            p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else 0
                        station_dict['realtime_power'] += p
                station_list.append(station_dict)
            p_data['station'] = station_list
            data.append(p_data)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
            }
        )


class ProjectsPackrView(APIView):
    """
    项目包
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    @permission_required('JKGLtab6')
    def post(self, request):
        """
        另存项目包
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        data = request.data.get('data')
        name = request.data.get('name')
        if not data or not name:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "项目包信息为空！" if lang == 'zh' else
                    'Project package information is empty.'},
                }
            )
        for info in data:
            if not info.get('id') or not info.get('station'):
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {"message": "error", "detail": "项目包另存失败，内容不完整！" if lang == 'zh' else
                        'The project package fails to save, the content is incomplete.'},
                    }
                )
        pack = ProjectPack.objects.filter(user_id=user_id, name=name).count()
        if pack > 0:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "项目包名称已存在，请修改后重新上传！" if lang == 'zh' else
                    'The project package name already exists, please modify and upload again.'},
                }
            )

        ins = ProjectPack.objects.create(user_id=user_id, data=json.dumps(data), name=name)

        # 异步翻译
        pdr_data = {'id': ins.id,
                    'table': 't_project_pack',
                    'update_data': {'name': name}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": [],
                },
            }
        )

    @permission_required('JKGLtab6')
    def get(self, request):
        """
        加载项目包
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        pack_res = ProjectPack.objects.filter(user_id=user_id).all()
        data = []
        for pack in pack_res:
            p_dict = {
                'name': pack.name if lang == 'zh' else pack.en_name,
                'data': json.loads(pack.data)
            }
            data.append(p_dict)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
            }
        )


class SendSmscodeView(APIView):
    """
    短信下发
    """
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        context = {'user_id': request.user.get('user_id'), "lang": request.headers.get("lang", 'zh')}
        ser = MonitorSendMobileMessageSerializer(data=request.data, context=context)
        try:
            if not ser.is_valid():
                error_log.error("短信发送:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("短信发送:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("故障复位下发发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else
                    'The SMS verification code failed to be issued.'},
                }
            )
        
        conn = get_redis_connection("default")
        conn.set("reset" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功" if lang == 'zh' else
                    f'The phone number:{ser.validated_data.get("mobile")} sent the SMS successfully.',
                },
            }
        )


class PowerIssudView(APIView):
    """
    功率计划下发
    """
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    @permission_required('JKGLtab6')
    @transaction.atomic
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        context = {'user_id': request.user.get('user_id'), 'lang': lang}
        save_id = transaction.savepoint()
        ser = PowerIssudSerializer(data=request.data, context=context)
        try:
            if not ser.is_valid():
                error_log.error("功率计划下发:字段校验不通过:{}".format(ser.errors))
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("功率计划下发:字段校验不通过:{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        count = PowerDeliverRecords.objects.filter(user_id=context.get('user_id'), name=ser.validated_data['name']).count()
       
        if count > 0:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "任务名称重复，添加失败！" if lang == 'zh' else 'Task name is repeated, add failed!'},
                }
            )
        try:
            obj = ser.save()
            transaction.savepoint_commit(save_id)
        except Exception as e:
            error_log.error("新增功率计划下发失败：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "新增失败" if lang == 'zh' else 'Add failed.'},
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"新增成功: {obj.id}" if lang == 'zh' else f'Add successfully: {obj.id}.',
                }
            }
        )

    @permission_required('JKGLtab6')
    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 10))
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        # 查询符合条件的中间表数据
        record_ids = [i.get('id') for i in
                      PlanDeliverRecords.objects.filter(power__user_id=user_id).values('power_id').annotate(
                          id=Min('id'))]
        power_record = PlanDeliverRecords.objects.filter(power__user_id=user_id, id__in=record_ids)
        if name:
            power_record = power_record.filter(power__name__contains=name)
        if status:
            power_record = power_record.filter(plan__status=status)
        if start_time and end_time:
            power_record = power_record.filter(plan__start_time__gte=start_time, plan__end_time__lte=end_time)
        # 排序
        power_record = power_record.order_by('-id')
        page_res = paging(page, size, power_record)  # 分页器
        res = page_res.get('data')
        data = []
        for i in res:
            p_dict = {
                'id': i.power.id,
                'name': i.power.name if lang == 'zh' else i.power.en_name,
                'status': i.plan.status,
                'start_time': i.plan.start_time,
                'end_time': i.plan.end_time,
                'plan_count': len(json.loads(i.power.power_list)),
                'station_count': len(json.loads(i.power.station_list))
            }
            data.append(p_dict)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )

    @permission_required('JKGLtab6')
    @transaction.atomic
    def put(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        context = {'user_id': user_id, 'lang': lang}
        ser = PowerIssudSerializer(data=request.data, context=context)
        try:
            if not ser.is_valid():
                error_log.error("功率计划下发修改:字段校验不通过:{}".format(ser.errors))
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors}
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        res = PowerDeliverRecords.objects.filter(id=request.data.get('id'), user_id=user_id).first()
        if not res:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": "功率下发计划任务ID为空或数据不存在" if lang == 'zh' else 'Power plan task ID is empty or does not exist.'},
                }
            )
        count = PowerDeliverRecords.objects.filter(user_id=context.get('user_id'), name=ser.validated_data['name'])\
            .exclude(id=request.data.get('id')).count()
        if count > 0:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "任务名称重复，更新失败！" if lang == 'zh' else 'Task name is repeated, update failed!'},
                }
            )

        save_id = transaction.savepoint()
        try:
            ser.update(instance=res, validated_data=request.data)
            transaction.savepoint_commit(save_id)
        except Exception as e:
            print(traceback.print_exc())
            error_log.error(f'功率下发计划任务更新失败：{e}')
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "更新失败，请检查参数" if lang == 'zh' else 'Update failed, please check the parameters.'},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"更新成功：{request.data.get('id')}" if lang == 'zh' else 'Update successfully: {request.data.get("id")}',
                }
            }
        )

    @permission_required('JKGLtab6')
    def delete(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        context = {'user_id': user_id, 'lang': lang}
        ser = PowerIssudDeleteSerializer(data=request.query_params, context=context)

        try:
            if not ser.is_valid():
                error_log.error("删除功率计划下发:字段校验不通过：{}".format(ser.errors))
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("删除功率计划下发:字段校验不通过：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        try:
            res = PowerDeliverRecords.objects.get(id=ser.validated_data['id'], user_id=user_id)
        except Exception as e:
            error_log.error('删除功率下发计划任务失败：{}'.format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '功率下发计划任务ID为空或数据不存在' if lang == 'zh' else
                    'Power plan task ID is empty or does not exist.'},
                }
            )

        records = PlanDeliverRecords.objects.filter(power_id=ser.validated_data['id'])
        plan_ids = [i.plan_id for i in records]
        records.delete()
        models.StationPlanHistory.objects.filter(id__in=plan_ids).delete()
        res.delete()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": [],
                }
            }
        )


class PowerIssudDetailView(APIView):
    """
    功率计划下发-详情
    """
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    @permission_required('JKGLtab6')
    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user_id = request.user.get('user_id')
        p_id = request.query_params.get('id')
        try:
            res = PowerDeliverRecords.objects.get(id=p_id, user_id=user_id)
        except Exception as e:
            error_log.error('查询功率下发计划任务失败：{}'.format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '功率下发计划任务ID为空或数据不存在' if lang == 'zh' else 'Power plan task ID is empty or does not exist.'},
                }
            )
        data = {}
        power_list = json.loads(res.power_list)
        for info in power_list:
            info['status'] = PlanDeliverRecords.objects.filter(power_id=p_id, serial_number=info.get('serial_number')).values('plan__status').annotate(id=Min('plan_id'))[0].get('plan__status')
        data['name'] = res.name if lang == 'zh' else res.en_name
        data['mobile'] = res.mobile
        data['power_list'] = power_list
        data['station_list'] = json.loads(res.station_list)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class FlushedView(APIView):
    """
    功率计划下发-并网点刷新
    """
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def post(self, request):
        user_id = request.user.get('user_id')
        station = request.data.get('station', [])
        data = []
        for info in station:
            project_id = info.get('project_id')
            station_id = info.get('station_id')

            stations = models.MaterStation.objects.filter(project_id=project_id, is_delete=0, id=station_id,
                                                          project__user__id=user_id).first()

            # Redis
            conn = get_redis_connection("3")

            station_dict = {
                'project_name': stations.project.name,
                'project_id': stations.project.id,
                'station_name': stations.name,
                'station_id': stations.id,
                'rated_power': 0,
                'rated_capacity': 0,
                'realtime_power': 0
            }
            slaves = stations.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0)).values(
                'rated_power',
                'rated_capacity',
                'english_name',
                'id'
            )
            for s_info in slaves:
                station_dict['rated_power'] += float(s_info['rated_power'])
                station_dict['rated_capacity'] += float(s_info['rated_capacity'])
                units = models.Unit.objects.filter(is_delete=0, station_id=s_info['id']).values('pcs')
                for unit in units:
                    pcs = conn.get(
                        'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', s_info.get('english_name'),
                                                                unit.get('pcs')))
                    if not pcs:
                        p = 0
                    else:
                        pcs = eval(eval(pcs))
                        p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else 0
                    station_dict['realtime_power'] += p
            station_dict['realtime_power'] = round(station_dict.get('realtime_power'), 2)
            data.append(station_dict)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
            }
        )


class IssuedMappingView(APIView):
    """
    功率计划下发状态字典
    """
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": PLANSTATUS[lang],
                },
            }
        )

