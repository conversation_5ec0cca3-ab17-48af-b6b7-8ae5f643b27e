#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 09:03:44
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\alarm_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-01 14:32:03


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.event import Event
from Application.Models.User.user import User

class AlarmR(user_Base):
    u'告警记录表'
    __tablename__ = "r_alarm"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    value = Column(Float, nullable=False, comment=u"数值")
    value_descr = Column(VARCHAR(50), nullable=True,comment=u"值描述")
    event_id = Column(Integer,ForeignKey("t_event.id"), nullable=False,comment=u"事件配置表")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    ts = Column(DateTime, nullable=False,comment=u"数据时间")
    user_id = Column(Integer, ForeignKey("t_user.id"),nullable=True,comment=u"用户id，记录是谁处理的")
    user_ts = Column(DateTime, nullable=True,comment=u"用户处理时间id")
    remark = Column(VARCHAR(256), nullable=True, comment=u"处理意见")
    opt1 = Column(VARCHAR(50), nullable=True,comment=u"关联点1")
    opt2 = Column(VARCHAR(50), nullable=True,comment=u"关联点2")
    opt3 = Column(VARCHAR(50), nullable=True,comment=u"关联点3")
    opt4 = Column(VARCHAR(50), nullable=True,comment=u"关联点4")
    opt5 = Column(VARCHAR(50), nullable=True,comment=u"关联点5")
    opt6 = Column(VARCHAR(50), nullable=True,comment=u"关联点6")
    opt7 = Column(VARCHAR(50), nullable=True,comment=u"关联点7")
    opt8 = Column(VARCHAR(50), nullable=True,comment=u"关联点8")
    opt9 = Column(VARCHAR(50), nullable=True,comment=u"关联点9")
    opt10 = Column(VARCHAR(50), nullable=True,comment=u"关联点10")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
    send = Column(Integer, nullable=True,comment=u"是否发送通知(0：否， 1：是)")
    status = Column(Integer, nullable=True,comment=u"是否确认(0：否， 1：是)")
   
    alarm_event = relationship("Event", backref="alarm_event")
    alarm_user = relationship("User", backref="alarm_user")

    en_value_descr = Column(VARCHAR(50), nullable=True, comment=u"值描述")
    en_remark = Column(VARCHAR(256), nullable=True, comment=u"处理意见")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        user_descr = self.alarm_user.name if self.alarm_user else ''
        en_user_descr = self.alarm_user.en_name if self.alarm_user else ''
        a1 = self.remark.split('\n') if self.remark else []
        en_a1 = self.en_remark.split('\n') if self.en_remark else []
        alarm_descr = self.alarm_event.event_type.descr if self.alarm_event else ''
        en_alarm_descr = self.alarm_event.event_type.en_descr if self.alarm_event else ''
        point = self.alarm_event.point if  self.alarm_event else ''
        descr = self.alarm_event.descr if  self.alarm_event else ''
        en_descr = self.alarm_event.en_descr if self.alarm_event else ''
        bean = "{'id':%s,'value':%s,'event_id':%s,'alarm_descr':'%s','en_alarm_descr':'%s','ts':'%s','user_id':'%s','user_descr':'%s','en_user_descr':'%s','user_ts':'%s','remark':'%s','en_remark':'%s','op_ts':'%s','opt1':'%s','opt2':'%s','opt3':'%s',\
        'opt4':'%s','opt5':'%s','opt6':'%s','opt7':'%s','opt8':'%s','opt9':'%s','opt10':'%s','point':'%s','descr':'%s','en_descr':'%s','value_descr':'%s','send':'%s','status':'%s','en_value_descr':'%s'}" % (self.id,
        self.value,self.event_id,alarm_descr,en_alarm_descr,self.ts,self.user_id,user_descr,en_user_descr,self.user_ts,''.join(a1),''.join(en_a1),self.op_ts,self.opt1,self.opt2,self.opt3,self.opt4,
        self.opt5,self.opt6,self.opt7,self.opt8,self.opt9,self.opt10,point,descr,en_descr,self.value_descr,self.send,self.status,self.en_value_descr)
        return bean.replace("None",'')
        
    def deleteAlarm(self,id):
        try:
            user_session.query(AlarmR).filter(AlarmR.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}