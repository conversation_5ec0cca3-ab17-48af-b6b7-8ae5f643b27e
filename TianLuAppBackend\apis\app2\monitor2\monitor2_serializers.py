import re

from rest_framework import serializers
from django.core.validators import RegexValidator
from apis.user import models
from rest_framework import exceptions
from django_redis import get_redis_connection


class VirtuallyCheckSMSCodeSerializer(serializers.Serializer):
    """控制策略短信验证"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.IntegerField(required=True)
    # 站名
    strategy = serializers.CharField(required=True)
    # # 虚量参数
    # virtually = serializers.IntegerField(required=True)
    # # 变压器容量
    # capacity = serializers.IntegerField(required=True)
    # # 变压器有功比例
    # proportion = serializers.FloatField(required=True)
    # # 防逆流阈值
    # threshold = serializers.FloatField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get("lang", 'zh')

        if not re.match("^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Invalid mobile.")

        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get("lang", 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "virtually" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match("^[0-9]{6}$", str(value)):
            raise Exception("验证码格式不正确" if lang == 'zh' else "Invalid code.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Invalid code.")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise Exception("验证码错误" if lang == 'zh' else "Invalid code.")
        return value

    # def validated_proportion(self, value):
    #     proportion = self.initial_data.get('proportion')
    #     if not 0 <= proportion <= 1:
    #         raise exceptions.ValidationError("变压器有功比例超过限制范围：0-1")
    #     return value