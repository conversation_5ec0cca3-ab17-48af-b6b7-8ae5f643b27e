package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 计划历史记录VO
 */
@Data
@ApiModel("计划历史记录VO")
public class TPlanHistoryVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("状态: 1-已保存, 2-已下发, 3-执行中, 4-已完成, 5-下发失败, 6-已停止")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("功率值")
    private Double power;

    @ApiModelProperty("计划类型: 1-自定义, 2-周期性, 3-节假日")
    private Integer planType;

    @ApiModelProperty("计划类型名称")
    private String planTypeName;

    @ApiModelProperty("计划开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("计划结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty("是否跟随名称")
    private String isFollowName;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("描述")
    private String descr;

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
