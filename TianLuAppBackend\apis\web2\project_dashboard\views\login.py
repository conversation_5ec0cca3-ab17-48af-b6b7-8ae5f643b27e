# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/26 9:35
# <AUTHOR> <PERSON><PERSON>ang
# @Project  : TianLuAppBackend
# @File     : login.py
# @Software : PyCharm
import datetime
import decimal
import random
import re

from django.db.models import Min, Q, Sum
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.user import models
from apis.web2 import error_log
from apis.web2.project_dashboard import c_serializers
from apis.web2.project_dashboard.c_serializers import WebSendMobileMessageSerializer
from common import common_response_code
from encryption import jwt_encryption
from tools.aly_send_smscode import Sample
from tools.count import unit_convert


class UsernamePasswordLoginView(APIView):
    """web2.0： 用户名/手机号 + 密码 ==> 登录"""""
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = c_serializers.UsernamePasswordLoginSerializer(data=request.data, context={"lang": lang})
        ty = int(request.data.get("ty") or "3")     # 登录类型
        code = request.data.get("code")
        try:
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": e.args[0],
                    },
                }
            )

        username = ser.validated_data["username"]
        # if re.match(r"^1[3-9]\d{9}$", username) and username in ['13810897522', '13030337255', '18600739775']:

        #     if not code:
        #         return Response(
        #             {
        #                 "code": common_response_code.SUMMARY_CODE,
        #                 "data": {
        #                     "message": "验证码缺失",
        #                 },
        #             }
        #         )

        #     # 3个手机号增加验证验证码
        #     conn = get_redis_connection("default")
        #     tem_code = conn.get('tianlu_' + ser.validated_data['username'])
        #     if not tem_code:
        #         return Response(
        #             {
        #                 "code": common_response_code.SUMMARY_CODE,
        #                 "data": {
        #                     "message": "验证码已过期",
        #                 },
        #             }
        #         )

        #     conn.delete('tianlu_' + ser.validated_data['username'])  # 删除 redis 中的短信验证码

        #     if not str(code) == tem_code.decode('utf-8'):
        #         return Response(
        #             {
        #                 "code": common_response_code.SUMMARY_CODE,
        #                 "data": {
        #                     "message": "验证码错误",
        #                 },
        #             }
        #         )

        user_instance = models.UserDetails.objects.get(Q(login_name=username) | Q(mobile=username))
        user_type = user_instance.type
        roles = user_instance.roles.values("permissions__title", "permissions__en_title", "permissions__url").distinct()
        role_list = []

        project_ins = models.Project.objects.filter(user=user_instance, is_used=1).all()
        stations_ins = models.StationDetails.objects.filter(project__user=user_instance, project__in=project_ins, is_delete=0).all()

        master_stations = models.MaterStation.objects.filter(project__user=user_instance, project__in=project_ins,
                                                             is_delete=0).all()
        power_count, power_unit = unit_convert(decimal.Decimal(stations_ins.aggregate(Sum('rated_power')).get('rated_power__sum')), "kW")
        capacity_count, capacity_unit = unit_convert(
            decimal.Decimal(stations_ins.aggregate(Sum('rated_capacity')).get('rated_capacity__sum')), "kWh"
        )

        max_time = models.Project.objects.filter(is_used=1, user=user_instance).aggregate(Min('in_time'))
        run_times = datetime.datetime.now() - max_time["in_time__min"]
        run_days = run_times.days

        stations_count_dic = {
            "power_count": power_count,
            "run_days": run_days,
            "power_unit": power_unit,
            "capacity_count": capacity_count,
            "capacity_unit": capacity_unit,
            "1-1": stations_ins.filter(unit_number=1).count(),
            "1-2": stations_ins.filter(unit_number=2).count(),
            "1-3": stations_ins.filter(unit_number=3).count(),
            "1-4": stations_ins.filter(unit_number=4).count(),
            "count": master_stations.count(),
            "pcs_num": sum([decimal.Decimal(station_ins.battery_cluster) for station_ins in stations_ins]),
        }

        for role in roles:
            role_list.append(
                {
                    "tittle": role["permissions__title"] if lang == 'zh' else role["permissions__en_title"],
                    "url": role["permissions__url"],
                }
            )

        user_ins = user_instance.roles
        user_dic = {
            'role': user_ins,
        }
        related_role_name = list(user_dic['role'].all())
        role_name = []
        for j in related_role_name:
            role_name_d = {}
            role_name_d['id'] = j.id
            role_name_d['role_name'] = j.role_name if lang == 'zh' else j.en_role_name
            role_name.append(role_name_d)

        role_name_2 = []
        for rr in role_name:
            name_ = models.Role.objects.filter(id=rr['id']).first()
            if name_.type == ty:
                role_name_2.append(rr)

        success_token = jwt_encryption.create_token({"user_id": user_instance.id})

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"用户({user_instance.user_name})登录成功" if lang == 'zh' else f"{user_instance.en_user_name} Login Success.",
                    "success_token": success_token,
                    "user_type": user_type,
                    "roles": role_name_2,
                    "stations": stations_count_dic,
                },
            }
        )


class WebGetSMSCode(APIView):
    """
    登录页面发送短信接口
    """""

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = WebSendMobileMessageSerializer(data=request.query_params, context={"lang": lang})

        try:
            if not ser.is_valid():
                error_log.error("短信登录字段传参校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": e.args[0]
                    }
                }
            )
        random_sms_code = random.randint(100000, 999999)

        code = Sample.main(ser.validated_data["mobile"], random_sms_code, lang)
        if code != 200:
            error_log.error("验证码登录:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else
                    'Failed to send SMS verification code.'},
                }
            )

        conn = get_redis_connection("default")
        conn.set('tianlu_' + ser.validated_data["mobile"], random_sms_code, 60)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功" if lang == 'zh' else
                    "Send SMS successfully.",
                },
            }
        )