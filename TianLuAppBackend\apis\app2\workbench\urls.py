from django.urls import path
from apis.app2.workbench import views

urlpatterns = [
    # 充放电量明细
    path("charge-discharge/details", views.ChargeAndDischargeDetailsView.as_view()),  # 充放电量明细

    # 收益明细
    path("income/details", views.IncomeDetailsView.as_view()),  # 收益明细
    path("incom/add/", views.IncomeAddView.as_view()),  # 月份及收益添加

    # 度电收益明细
    path("pre-income/details", views.PreIncomeDetailsView.as_view()),  # 度电收益明细

    # 单位电价配置
    path("customization/", views.CustomizationView.as_view()),  # 单位电价化配置
    path("customization/detail/", views.CustomizationDetailView.as_view()),  # 当月时刻电价查询
    path("customization/add/", views.CustomizationAddView.as_view()),  # 单位电价添加         en
    path("customization/history/", views.CustomizationHistoryView.as_view()),  # 单位电价列表
    path("customization/delete/", views.CustomizationDeleteView.as_view()),  # 单位电价删除
    path("customization/update/", views.CustomizationUpdateDetailView.as_view()),  # 单位电价更新     en
    path("customization/info/", views.CustomizationUpdateDetailView.as_view()),  # 单位电价详情

    # 自动控制策略: 废弃
    # path("user_strategy/add", views.UserStrategyView.as_view()),  # 用户自动控制策略：新增
    # path("user_strategy/update/<int:pk>", views.UserStrategyView.as_view()),  # 用户自动控制策略：修改
    path("user_strategy/list", views.UserStrategyView.as_view()),  # 用户自动控制策略：列表
    # path("user_strategy/delete/<int:pk>", views.UserStrategyDeleteView.as_view()),  # 用户自动控制策略：删除
    # path("user_strategy/check_month/<int:pk>", views.UserStrategyCheckMonthView.as_view()),  # 用户自动控制策略：月份校验
    #
    # path("user_strategy/category/add", views.UserStrategyCategoryView.as_view()),  # 用户自动控制策略-季节：新增
    # path("user_strategy/category/update/<int:pk>", views.UserStrategyCategoryView.as_view()),  # 用户自动控制策略-季节：修改
    # path("user_strategy/category/update_month/<int:pk>", views.UserStrategyCategoryUpdateMonthView.as_view()),
    # # 用户自动控制策略-季节：修改月份
    # path("user_strategy/category/list/<int:strategy_id>", views.UserStrategyCategoryView.as_view()),  # 用户自动控制策略-季节：列表
    # path("user_strategy/category/detail/<int:pk>", views.UserStrategyCategory2View.as_view()),  # 用户自动控制策略-季节：详情
    # path("user_strategy/category/delete/<int:pk>", views.UserStrategyCategory2View.as_view()),  # 用户自动控制策略-季节：删除
    #
    # path("user_strategy/current", views.CurrentStrategyView.as_view()),  # 用户自动控制策略：当前策略
    # path("user_strategy/default", views.DefaultStrategyView.as_view()),  # 用户自动控制策略：默认策略
    # path("user_strategy/realtime", views.RealTimeStationStrategyView.as_view()),  # 用户自动控制策略：实时策略
    # path("user_strategy/compare", views.CompareStationStrategyView.as_view()),  # 用户自动控制策略：默认策略比较
    # path("user_strategy/compare/customize", views.CustomizeStationStrategyView.as_view()),  # 用户自动控制策略：自定义策略比较
    # path("user_strategy/save/<int:pk>", views.UserStrategySaveToOtherView.as_view()),  # 用户自动控制策略：另存为
    # path("user_strategy/sendsms/", views.AutomaticControlSendSmsView.as_view()),  # 自动控制模式发送短信
    # path("user_strategy/apply", views.UserStrategyApplyView.as_view()),  # 用户自动控制策略：下发
    # path("user_strategy/customize/list/<int:strategy_id>", views.UserStrategyCustomizeView.as_view()),
    # # 用户自动控制策略-自定义策略列表
    #
    # path("user_strategy/station_strategy/month", views.StationStrategyView.as_view()),  # 用户自动控制策略：某月的策略, mtqq


]