package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryCreateDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryQueryDTO;
import com.robestec.analysis.dto.tplanhistory.TPlanHistoryUpdateDTO;
import com.robestec.analysis.entity.TPlanHistory;
import com.robestec.analysis.vo.TPlanHistoryVO;

import java.util.List;

/**
 * 计划历史记录服务接口
 */
public interface TPlanHistoryService extends ISuperService<TPlanHistory> {

    /**
     * 分页查询计划历史记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TPlanHistoryVO> queryTPlanHistory(TPlanHistoryQueryDTO queryDTO);

    /**
     * 创建计划历史记录
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTPlanHistory(TPlanHistoryCreateDTO createDTO);

    /**
     * 更新计划历史记录
     * @param updateDTO 更新参数
     */
    void updateTPlanHistory(TPlanHistoryUpdateDTO updateDTO);

    /**
     * 删除计划历史记录
     * @param id 记录ID
     */
    void deleteTPlanHistory(Long id);

    /**
     * 获取计划历史记录详情
     * @param id 记录ID
     * @return 记录详情
     */
    TPlanHistoryVO getTPlanHistory(Long id);

    /**
     * 批量创建计划历史记录
     * @param createDTOList 创建参数列表
     */
    void createTPlanHistoryList(List<TPlanHistoryCreateDTO> createDTOList);

    /**
     * 根据用户ID查询计划历史记录
     * @param userId 用户ID
     * @return 记录列表
     */
    List<TPlanHistoryVO> getTPlanHistoryByUserId(Long userId);

    /**
     * 根据状态查询计划历史记录
     * @param status 状态
     * @return 记录列表
     */
    List<TPlanHistoryVO> getTPlanHistoryByStatus(Integer status);

    /**
     * 根据电站名称查询计划历史记录
     * @param station 电站名称
     * @return 记录列表
     */
    List<TPlanHistoryVO> getTPlanHistoryByStation(String station);

    /**
     * 统计用户的计划历史记录数量
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(Long userId);

    /**
     * 统计指定状态的计划历史记录数量
     * @param status 状态
     * @return 记录数量
     */
    Long countByStatus(Integer status);
}
