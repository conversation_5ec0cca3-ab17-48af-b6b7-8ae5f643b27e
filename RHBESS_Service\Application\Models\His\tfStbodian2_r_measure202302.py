#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 09:03:44
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\alarm_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-03 08:43:42


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,TIMESTAMP


class Measure202302R(user_Base):
    u'测量表'
    __tablename__ = "r_measure202302"
    name = Column(VARCHAR(64), nullable=False, primary_key=True, comment=u"")
    value = Column(Float, nullable=False, comment=u"")
    dts_s = Column(Integer, nullable=False, primary_key=True,comment=u"数据时标")
    dts_ms = Column(Integer, nullable=False, primary_key=True,comment=u"")
    ots = Column(TIMESTAMP, nullable=False,comment=u"操作时标")
    cause = Column(Integer, nullable=False,comment=u"记录原因")


    
    # @classmethod
    # def init(cls):
    #     user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'name':'%s','value':%s,'dts_s':%s,'dts_ms':%s,'ots':'%s','cause':%s}" % (self.name, self.value,self.dts_s, self.dts_ms, self.ots, self.cause)
        return bean.replace("None",'')
        
    def deleteMeasure202302R(self,name):
        try:
            user_session.query(Measure202302R).filter(Measure202302R.name == name).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False