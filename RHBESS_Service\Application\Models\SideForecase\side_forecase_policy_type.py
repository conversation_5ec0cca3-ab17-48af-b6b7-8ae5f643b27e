#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:40:11
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_policy_type.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-30 17:40:27


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecasePolicyType(user_Base):
    u'政策类型表'
    __tablename__ = "t_side_forecase_policy_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(String(256), nullable=False, comment=u"描述")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'name':'%s'}" %(self.id,self.name)

   