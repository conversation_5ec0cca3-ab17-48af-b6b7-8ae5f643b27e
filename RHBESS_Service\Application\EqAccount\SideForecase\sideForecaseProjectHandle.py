#!/usr/bin/env python
# coding=utf-8
#@Information:项目管理
#<AUTHOR> WYJ
#@Date         : 2023-05-31 10:52:52
#@FilePath     : \RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseProjectHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-07 17:18:35



import os,uuid

import ast
import time
from datetime import datetime
import os

import numpy as np
import xlrd
# from django.db.models import Model
from sqlalchemy.orm import load_only
from pyscipopt import Model
from Tools.Utils.scipopt_util_day import calrate_new
from Tools.Utils.optimum_proposal_utils import *
import pandas as pd
import tornado.web
import pandas as pd
from io import BytesIO
import json
import xlrd
from openpyxl import Workbook
from openpyxl.styles import Alignment
from pathlib import Path
from Application.Models.SideForecase.side_forecase_dict_log import ForecaseDicLog
from Application.Models.SideForecase.side_forecase_handle_logs import ForecaseHandleLogs
from Application.Models.SideForecase.side_forecase_user import ForecaseUser
from Application.Models.User.user import User
from Tools.Utils.mimio_tool import  upload_file
from Tools.Utils.scipopt_util_day import calrate_new
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_area import ForecaseArea
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_price import ForecasePrice
from Application.Models.SideForecase.side_forecase_customer import ForecaseCustomer
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Application.Models.SideForecase.side_forecase_files import ForecaseFiles
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_useele_ele_info import ForecaseUseEleEleInfo
from Application.Models.SideForecase.side_forecase_useele_ele_file import ForecaseUseEleEleFile
from Application.Models.SideForecase.side_forecase_useele_load_offer import ForecaseUseEleEleLoadOffer
from Application.Models.SideForecase.side_forecase_calculate import ForecaseCalcuate
import logging,json
from sqlalchemy import func,distinct
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import *
from Application.EqAccount.HaLun.project_info import ProvinceCity
from Application.EqAccount.SideForecase.sideForecaseCalculateHandle import SideForecaseCalculateHandleIntetface
from Application.EqAccount.SideForecase.sideForecaseProjectService import OptimumProposal, Calculate
from Application.EqAccount.SideForecase.sideForecaseDict import side_forecase_default_dict, side_forecase_dict, \
    side_forecase_optimized_dict
from Tools.Utils.mimio_tool import MinioTool
from Application.Models.SideForecase.side_reference_standard import ReferenceStandard
from Application.Models.SideForecase.side_argument_calcuate import ArgumentCalcuate

# SideForecaseCalculateHandleIntetface._financing_plan_fun(self,1,2)

dangan_yh = ["项目收资信息","合同电子版归档"]  # 项目收资第一版大类划分


class SideForecaseProjectHandleIntetface(BaseHandler):

    @tornado.web.authenticated
    @tornado.gen.coroutine
    def get(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'GetProvinceList':  # 查询省份
                data = ProvinceCity.ProvinceList()
                return self.returnTypeSuc(data)
            elif kt == 'GetCityList':  # 查询城市
                id = self.get_argument('id', None)  # 省id
                data = ProvinceCity.CityList(id)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectModelFiles':  # 查询项目流程图和收资模板
                type_ = self.get_argument('type_', 1)  # 省id
                d = {'1':'/home/<USER>/side/用户侧SOP流程.docx','2':'/home/<USER>/side/用户侧模板包.zip','3':'/home/<USER>/side/用户侧用电情况模板包.zip'}
                if type_ not in d.keys():
                    return self.customError("非法入参")
                return self.returnTypeSuc(d[type_])
            elif kt == 'GetProjectTS':  # 查询当前用户是否有暂存项目
                session = self.getOrNewSession()
                user_id = session.user['id']
                udp = user_session.query(ForecaseProject).filter(ForecaseProject.is_use==2,ForecaseProject.user_id==user_id).all()
                data = []
                for ud in udp:
                    u = eval(str(ud))
                    data.append(u)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectList':  # 查询项目
                data = []
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                name = self.get_argument('name', None)
                cls = self.get_argument('cls', None)
                type = self.get_argument('type', None)
                # _user_ids = self.get_argument('user_ids', None)   # 项目经理ID
                user_name = self.get_argument('user_name', None)   # 项目经理
                priority_ids = self.get_argument('priority_ids', None)   # 重要程度ID
                province_name = self.get_argument('province_name', None)   # 省份名称
                priority_sort = self.get_argument('priority_sort', None)   # 重要程度排序：0：取消排序；1：正序；2：倒序
                # f = user_session.query(ForecaseProject.city_name,ForecaseProject.expect_device_num,ForecaseProject.expect_emc_share,ForecaseProject.expect_emc_share_type,ForecaseProject.expect_emc_time,ForecaseProject.id,ForecaseProject.op_ts,ForecaseProject.other_status,ForecaseProject.own_year,
                #                        ForecaseProject.owner_label,ForecaseProject.owner_name,ForecaseProject.owner_short,ForecaseProject.project_level,ForecaseProject.province_name,ForecaseProject.sign_status,ForecaseProject.sn,ForecaseProject.update_time,ForecaseProject.user_id,
                #                        ForecaseProject.user_name).filter(ForecaseProject.is_use==1)
                f = user_session.query(ForecaseProject.id, ForecaseProject.city_name, ForecaseProject.expect_device_num, ForecaseProject.expect_emc_share,
                                       ForecaseProject.expect_emc_share_type, ForecaseProject.expect_emc_time, ForecaseProject.is_use, ForecaseProject.op_ts,
                                       ForecaseProject.other_status, ForecaseProject.own_year, ForecaseProject.owner_label, ForecaseProject.owner_name, ForecaseProject.owner_short,
                                       ForecaseProject.project_level, ForecaseProject.province_name, ForecaseProject.sign_status, ForecaseProject.sn,
                                       ForecaseProject.update_time, ForecaseProject.user_id, ForecaseProject.user_name).filter(ForecaseProject.is_use == 1)
                if name:
                    f = f.filter(ForecaseProject.owner_short.like('%{}%'.format(name)))
                if cls:
                    f = f.filter(ForecaseProject.owner_label==cls)
                if type:
                    if type != '1':
                        return self.returnTotalSuc(data,0)
                session = self.getOrNewSession()
                user_id = session.user['id']
                user_role_id = session.user['user_role_id']

                if user_role_id=='3':
                    f = f.filter(ForecaseProject.user_id == user_id)
                if user_name:
                    # _user_ids = _user_ids.split(',')
                    f = f.filter(ForecaseProject.user_name==user_name)
                if priority_ids:
                    priority_ids = priority_ids.split(',')
                    f = f.filter(ForecaseProject.project_level.in_(priority_ids))
                if province_name:
                    province_name = province_name.split(',')
                    f = f.filter(ForecaseProject.province_name.in_(province_name))
                total = f.count()
                pages = f.order_by(ForecaseProject.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                if priority_sort:
                    if priority_sort == '1':
                        pages = f.order_by(ForecaseProject.project_level.asc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                    elif priority_sort == '2':
                        pages = f.order_by(ForecaseProject.project_level.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()

                #1查看，2新建，3编辑，4删除
                for pag in pages:
                    # e = eval(str(pag))
                    e = {
                        "city_name": pag[1],
                        "expect_device_num": pag[2],
                        "expect_emc_share": pag[3],
                        "expect_emc_share_type": pag[4],
                        "expect_emc_time": pag[5],
                        "id": pag[0],
                        "is_use": pag[6],
                        "op_ts": pag[7].strftime("%Y-%m-%d %H:%M:%S") if pag[7] else pag[7],
                        "other_status": pag[8],
                        "own_year": pag[9],
                        "owner_label": pag[10],
                        "owner_name": pag[11],
                        "owner_short": pag[12],
                        "project_level": pag[13],
                        "province_name": pag[14],
                        "sign_status": pag[15],
                        "sn": pag[16],
                        "update_time": pag[17].strftime("%Y-%m-%d %H:%M:%S") if pag[17] else pag[17],
                        "user_id": pag[18],
                        "user_name": pag[19]
                    }
                    # del e['optimized_constant_1']
                    # del e['optimized_constant_2']
                    e['owner_short'] = e['owner_short'].split('-')[1]
                    e['redact']=[]
                    if user_role_id=='1':
                        e['redact'] = ['1','2','3','4']
                    else:
                        if user_role_id == '4':
                            e['redact'] = ['1']
                        else:
                            e['redact'].append('2')
                            if user_role_id == '3':
                                if e['user_id']==user_id:
                                    e['redact'].append('1')
                                    e['redact'].append('3')
                                    e['redact'].append('4')
                            else:
                                e['redact'].append('1')
                                if user_role_id == '2':
                                    if e['user_id'] == user_id:
                                        e['redact'].append('3')
                                        e['redact'].append('4')
                    data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetProjectInfosById':  # 根据具体项目id茶轩详情
                # session = self.getOrNewSession()
                # user_id = session.user['id']
                id = self.get_argument('id',None)
                ud = user_session.query(ForecaseProject.id, ForecaseProject.city_name, ForecaseProject.expect_device_num, ForecaseProject.expect_emc_share,
                                       ForecaseProject.expect_emc_share_type, ForecaseProject.expect_emc_time, ForecaseProject.is_use, ForecaseProject.op_ts,
                                       ForecaseProject.other_status, ForecaseProject.own_year, ForecaseProject.owner_label, ForecaseProject.owner_name, ForecaseProject.owner_short,
                                       ForecaseProject.project_level, ForecaseProject.province_name, ForecaseProject.sign_status, ForecaseProject.sn,
                                       ForecaseProject.update_time, ForecaseProject.user_id, ForecaseProject.user_name, ForecaseProject.owner_introduce,
                                        ForecaseProject.project_level_explain, ForecaseProject.sign_status_explain, ForecaseProject.special_content, ForecaseProject.sop,
                                        ForecaseProject.run_type, ForecaseProject.owner_type, ForecaseProject.indus, ForecaseProject.own_status, ForecaseProject.run_day,
                                        ForecaseProject.device_type, ForecaseProject.area_flag).filter(ForecaseProject.id==id).first()


                u = {
                    "owner_introduce": ud[20],
                    "project_level_explain": ud[21],
                    "sign_status_explain": ud[22],
                    "special_content": ud[23],
                    "sop": json.loads(ud[24]) if ud[24] else ud[24],
                    "run_type": json.loads(ud[25]) if ud[25] else ud[25],
                    "owner_type": json.loads(ud[26]) if ud[26] else ud[26],
                    "indus": json.loads(ud[27]) if ud[27] else ud[27],
                    "own_status": ud[28],
                    "run_day": ud[29],
                    "device_type": ud[30],
                    "area_flag": ud[31],
                    "city_name": ud[1],
                    "expect_device_num": ud[2],
                    "expect_emc_share": ud[3],
                    "expect_emc_share_type": ud[4],
                    "expect_emc_time": ud[5],
                    "id": ud[0],
                    "is_use": ud[6],
                    "op_ts": ud[7].strftime("%Y-%m-%d %H:%M:%S") if ud[7] else ud[7],
                    "other_status": ud[8],
                    "own_year": ud[9],
                    "owner_label": ud[10],
                    "owner_name": ud[11],
                    "owner_short": ud[12],
                    "project_level": ud[13],
                    "province_name": ud[14],
                    "sign_status": ud[15],
                    "sn": ud[16],
                    "update_time": ud[17].strftime("%Y-%m-%d %H:%M:%S") if ud[17] else ud[17],
                    "user_id": ud[18],
                    "user_name": ud[19]
                }
                # u['optimized_constant'] = ud.optimized_constant #优化定容
                return self.returnTypeSuc(u)
            elif kt == 'GetProjectBaseFiles':  # 查询资料归档第一步文件
                stag_flag = self.get_argument('stag_flag', 1)  # 项目id
                obj = {}
                for yh in dangan_yh:
                    peizhi = user_session.query(distinct(ForecaseBaseFile.name.label('name'))).filter(ForecaseBaseFile.module_name==yh,ForecaseBaseFile.is_use==1,ForecaseBaseFile.stag_flag==stag_flag).all()
                    obj[yh] = []
                    for pz in peizhi:
                        o = {}
                        pfile = user_session.query(ForecaseBaseFile).filter(ForecaseBaseFile.module_name==yh,ForecaseBaseFile.name==pz[0],ForecaseBaseFile.is_use==1,ForecaseBaseFile.stag_flag==stag_flag).all()
                        if pfile:
                            o[pz[0]] = eval(str(pfile))
                        obj[yh].append(o)

                return self.returnTypeSuc(obj)
            elif kt == 'GetProjectFiles':  # 查询具体文档
                pid = self.get_argument('id', None)  # 项目id
                type_ = self.get_argument('base_file', 1)  # 基础文件类型
                data = []
                if DEBUG:
                    logging.info('id:%s,base_file:%s'%(pid,type_))
                if not pid or not type_:
                    return self.customError('非法入参')
                peizhi = user_session.query(ForecaseFiles).filter(ForecaseFiles.project_id==pid,ForecaseFiles.base_file_id==type_,ForecaseFiles.is_use==1).all()
                if peizhi:
                    for pz in peizhi:
                        p = eval(str(pz))
                        p['remarks'] = pz.remarks
                        data.append(p)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectUseEleInfo':  # 查询电力情况用电信息
                id = self.get_argument('id',None) # 项目id
                data = []
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                total = user_session.query(func.count(ForecaseUseEleEleInfo.id)).filter(ForecaseUseEleEleInfo.is_use==1,ForecaseUseEleEleInfo.project_id==id).scalar()
                pages = user_session.query(ForecaseUseEleEleInfo).filter(ForecaseUseEleEleInfo.is_use==1,ForecaseUseEleEleInfo.project_id==id).order_by(ForecaseUseEleEleInfo.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    e = eval(str(pag))
                    data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetProjectUseEleFile':  # 查询电力情况用电信息
                data = []
                id = self.get_argument('id',None) # 项目id
                pages = user_session.query(ForecaseUseEleEleFile).filter(ForecaseUseEleEleFile.is_use==1,ForecaseUseEleEleFile.project_id==id).order_by(ForecaseUseEleEleFile.id.desc()).all()
                for pag in pages:
                    e = pag.__dict__
                    e['op_ts'] = e.get('op_ts').strftime("%Y-%m-%d %H:%M:%S")
                    del e['_sa_instance_state']
                    # da = self._getexceldata(pag.file_path,2,13,range(1,8))
                    e['data1'] = self._getexceldata(pag.file_path,2,13,range(1,8))  # 用电量及电费数据统计
                    e['data2'] = self._getexceldata(pag.file_path,16,4,range(2))  # 饼图
                    e['data3'] = self._getexceldata(pag.file_path,2,13,range(8,17))  # 用电电费
                    e['data4'] = self._getexceldata(pag.file_path,2,13,range(17,24))  # 度电电费
                    e['data5'] = self._getexceldata(pag.file_path,2,13,range(24,28))  # 容量信息
                    data.append(e)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectUseEleLoadOffe':  # 查看电信息负荷/勘探负荷数据数据
                data = []
                id = self.get_argument('id',None) # 项目id
                type_ = self.get_argument('type_',1) # 类型id
                pages = user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.is_use==1,ForecaseUseEleEleLoadOffer.project_id==id,
                                                                              ForecaseUseEleEleLoadOffer.type==type_).order_by(ForecaseUseEleEleLoadOffer.id.desc()).all()
                for pag in pages:
                    e = eval(str(pag))
                    data.append(e)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectUseEleLoadOffeDataById':  # 根据id查看电信息负荷数据
                id = self.get_argument('id',None) # 主键id
                startTime = self.get_argument('startTime',None) # 起始时间
                endTime = self.get_argument('endTime',None) # 截止时间
                time_list = timeUtils.dateToDataList(startTime,endTime)  # 时间列表
                pag = user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.is_use==1,ForecaseUseEleEleLoadOffer.id==id).first()
                e = {}
                if pag:
                    e = eval(str(pag))
                    try:
                        df = pd.read_excel(pag.file_path,sheet_name="Input")
                    except Exception as e:
                        return self.customError('上传负荷文件有误，请检查后重新上传！')
                    for tim in time_list:
                        dd = df.loc[df['日期']==tim,["时","负荷"]]
                        e[tim] = (np.around(np.array(dd.values), 2)).tolist()
                return self.returnTypeSuc(e)

            elif kt == 'OptimumProposalDetail':
                """
                优化定容
                """
                # 项目ID
                id = self.get_argument('id')
                power = self.get_argument('power','')
                capacity = self.get_argument('capacity','')
                name = self.get_argument('name','')#产品名称
                is_first = self.get_argument('is_first','')# 0：优化定容；1：重新定容
                price_charge_lim = float(self.get_argument('price_charge_lim', '0.2'))   # 阈值（默认 0.2，限值 0-10）
                DOD = float(self.get_argument('DOD', '0.95'))   #  DOD（默认 0.95，限值 0-1）
                eff_charge = float(self.get_argument('eff_charge', '0.92'))    # 放电效率（默认 0.92，限值 0-1）
                eff_discharge = float(self.get_argument('eff_discharge', '0.92'))    # 充电效率（默认 0.92，限值 0-1）
                # calculation_time = self.get_argument('calculation_time')
                if id:
                    id = int(id)
                else:
                    return self.write({"code": 401, "msg": "参数错误，项目ID不允许为空", "data": []})
                type_ = self.get_argument('type', '1')    # 1：客户提供负荷；2：踏勘采集负荷
                session = self.getOrNewSession()
                user_id = session.user['id']
                # 查询设备型号
                # device_type, expect_emc_share = user_session.query(ForecaseProject.device_type, ForecaseProject.expect_emc_share).filter(ForecaseProject.id==id).first()
                pag_ = user_session.query(ForecaseProject).filter(ForecaseProject.id==id).first()

                # 查询项目下的所有客户提供负或者踏勘采集负荷附件
                pages = user_session.query(ForecaseUseEleEleLoadOffer.file_path, ForecaseUseEleEleLoadOffer.capacity_config, ForecaseUseEleEleLoadOffer.id).\
                    filter(ForecaseUseEleEleLoadOffer.is_use == 1,ForecaseUseEleEleLoadOffer.project_id == id, ForecaseUseEleEleLoadOffer.type == type_).all()

                optimum_eoposal = OptimumProposal()

                # 电费单
                electricity_bill = []
                res = {}
                total = 0
                if not pages:
                    return self.returnTypeSuc(res)
                try:
                    if int(type_) == 1:
                        echo = user_session.query(ForecaseProject.optimized_constant_1, ).filter(
                            ForecaseProject.is_use == 1, ForecaseProject.id == id).first()
                    elif int(type_) == 2:
                        echo = user_session.query(ForecaseProject.optimized_constant_2, ).filter(
                            ForecaseProject.is_use == 1, ForecaseProject.id == id).first()
                    if echo:
                        echo = eval(str(echo[0]))
                    else:
                        echo = {}
                    Result3_all = []
                    for page in pages:
                        capacity_config = {}
                        equipment_sum = 1
                        if page[1]:
                            capacity_config = json.loads(page[1])
                            equipment_sum = capacity_config.get('equipment_sum')
                        # 优化计算
                        data = optimum_eoposal.optimum_proposal_calculate_new(file_path=page[0], equipment_sum=equipment_sum,power=power,capacity=capacity,id=id,user_id=user_id,type_=type_, price_charge_lim=price_charge_lim,
                                                                              DOD=DOD,eff_charge=eff_charge,eff_discharge=eff_discharge)
                        if data == None:
                            return self.customError('计算超时！')
                        data["id"] = page[2]
                        data["capacity_config"] = capacity_config
                        if is_first == '0':
                            data["device_type"] = str(pag_.device_type)
                            data["user_name"] = session.user['name']
                        else:
                            echo_info = echo['electricity_bill'][total]
                            data["device_type"] = echo_info.get('device_type')
                            data["equipment_sum"] = echo_info.get('equipment_sum')
                            data["price"] = echo_info.get('price')
                            data["recommend_capacity"] = echo_info.get('recommend_capacity')
                            data["recommend_power"] = echo_info.get('recommend_power')
                            data["user_name"] = echo_info.get('user_name') if echo_info.get('user_name') else session.user['name']

                        electricity_bill.append(data)
                        total += 1

                    recommend_power = 0
                    recommend_capacity = 0
                    d1 = 0
                    d2 = 0
                    # 计算项目汇总：推荐功率、推荐容量、综合利用天数

                    for i in electricity_bill:
                        Result3_all.append(i.get('Result3'))
                        if i.get('capacity_config'):
                            recommend_power += i.get('capacity_config').get('recommend_power')
                            recommend_capacity += i.get('capacity_config').get('recommend_capacity')
                        else:
                            recommend_power += i['recommend_power']
                            recommend_capacity += i['recommend_capacity']
                        simulated = i.get('simulated')
                        d1 += simulated * i.get('capacity_total')
                        d2 += i.get('capacity_total')

                    # 计算Result3 经评计算使用
                    Result3 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    for i in range(len(Result3_all)):
                        Result3[3] += Result3_all[i][3]
                        Result3[1] += (Result3_all[i][1] / Result3_all[i][0] * 365) * Result3_all[i][3] if Result3_all[i][0] else 0
                        Result3[2] += Result3_all[i][2]
                        Result3[4] = Result3_all[i][4]
                        Result3[5] = Result3_all[i][5]
                        Result3[6] = Result3_all[i][6]
                        Result3[7] += Result3_all[i][3] * Result3_all[i][7]
                        Result3[8] += Result3_all[i][3] * Result3_all[i][8]
                        Result3[9] += Result3_all[i][3] * Result3_all[i][9]
                        Result3[10] += Result3_all[i][3] * Result3_all[i][10]
                        Result3[11] += Result3_all[i][3] * Result3_all[i][11]
                        Result3[12] += Result3_all[i][12] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[13] += Result3_all[i][13] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[14] += Result3_all[i][14] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[15] += Result3_all[i][15] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[16] += Result3_all[i][16] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[17] += Result3_all[i][17] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[18] += Result3_all[i][18] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[19] += Result3_all[i][19] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[20] += Result3_all[i][20] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[21] += Result3_all[i][21] / Result3_all[i][1] if Result3_all[i][1] else 0
                        Result3[22] += Result3_all[i][22] / Result3_all[i][1] * Result3_all[i][3] if Result3_all[i][1] else 0
                        Result3[23] += Result3_all[i][23] / Result3_all[i][1] * Result3_all[i][3] if Result3_all[i][1] else 0

                    Result3[1] = Result3[1] / Result3[3] if Result3[3] else 0
                    Result3[7] = Result3[7] / Result3[3] if Result3[3] else 0
                    Result3[8] = Result3[8] / Result3[3] if Result3[3] else 0
                    Result3[9] = Result3[9] / Result3[3] if Result3[3] else 0
                    Result3[10] = Result3[10] / Result3[3] if Result3[3] else 0
                    Result3[11] = Result3[11] / Result3[3] if Result3[3] else 0
                    Result3[12] = Result3[12] * Result3[1]
                    Result3[13] = Result3[13] * Result3[1]
                    Result3[14] = Result3[14] * Result3[1]
                    Result3[15] = Result3[15] * Result3[1]
                    Result3[16] = Result3[16] * Result3[1]
                    Result3[17] = Result3[17] * Result3[1]
                    Result3[18] = Result3[18] * Result3[1]
                    Result3[19] = Result3[19] * Result3[1]
                    Result3[20] = Result3[20] * Result3[1]
                    Result3[21] = Result3[21] * Result3[1]
                    Result3[22] = Result3[22] / Result3[3] * Result3[1] if Result3[3] else 0
                    Result3[23] = Result3[23] / Result3[3] * Result3[1] if Result3[3] else 0

                    # 表格信息
                    res['electricity_bill'] = electricity_bill
                    # 推荐功率
                    res['recommend_power'] = recommend_power
                    # 推荐容量
                    res['recommend_capacity'] = recommend_capacity
                    # 电表数
                    res['electricity_total'] = total
                    # 综合利用天数
                    res['comprehensive_utilization_day'] = d1 / d2
                    res['Result3'] = Result3

                    res['power'] = power
                    res['capacity'] = capacity
                    res['name'] = name
                    res = eval(str(res).replace('nan', "\"\""))
                    res = eval(str(res).replace('-inf', "\"\""))

                except Exception as e:
                    logging.error(e)
                    return self.customError('计算失败！请检查上传文件')
                ntime=self.logs_(3,id)
                res['ntime'] = ntime
                if int(type_)==1:
                    pag_.optimized_constant_1=str(res).replace("'",'"')
                elif int(type_)==2:
                    pag_.optimized_constant_2 = str(res).replace("'", '"')
                user_session.commit()
                return self.returnTypeSuc(res)

            elif kt == 'OptimizeDict':
                """
                优化定容下拉框字典
                """
                res = side_forecase_optimized_dict
                return self.returnTypeSuc(res)

            elif kt == 'defaultValueDict':
                """
                经评计算页面默认值
                """
                res = side_forecase_default_dict
                return self.returnTypeSuc(res)

            elif kt == 'mapping_dict':
                """
                经评计算下拉框字典
                """
                res = side_forecase_dict
                return self.returnTypeSuc(res)

            elif kt == 'calculateDetail':
                """
                经评计算回显接口
                """
                id = self.get_query_argument('id')
                if not id:
                    return self.write({'code': 400, 'msg': '项目ID不允许为空', 'data':[]})
                else:
                    data = user_session.query(ForecaseCalcuate).filter(ForecaseCalcuate.p_id == int(id)).first()
                    res = {}
                    if data:
                        res['input_json'] = ast.literal_eval(data.input_json)
                        res['output_json'] = ast.literal_eval(data.output_json)
                return self.returnTypeSuc(res)
            elif kt == 'GetOptimumProposalDetailById':  # 优化定容回显接口
                id = self.get_argument('id', None)  # 项目id
                type_ = self.get_argument('type', '1')  # 1：客户提供负荷；2：踏勘采集负荷

                if int(type_)==1:
                    pag = user_session.query(ForecaseProject.optimized_constant_1,).filter(ForecaseProject.is_use == 1,ForecaseProject.id == id).first()
                elif int(type_)==2:
                    pag = user_session.query(ForecaseProject.optimized_constant_2,).filter(ForecaseProject.is_use == 1,ForecaseProject.id == id).first()
                e = {}
                if pag:
                    e = eval(str(pag[0]))
                else:
                    return self.customError("无效id")
                return self.returnTypeSuc(e)
            elif kt == 'GetLogsList':  # 查询日志
                id = self.get_argument('id', None)  # 项目id
                # session = self.getOrNewSession()
                # user_id = session.user['id']
                # ntime = timeUtils.getNewTimeStr()
                pag = user_session.query(ForecaseHandleLogs).filter(ForecaseHandleLogs.is_use==1,ForecaseHandleLogs.project_id==id).all()
                if not pag:
                    return self.customError("无操作记录")
                user_name = user_session.query(ForecaseUser.name,ForecaseUser.id).filter(ForecaseUser.is_use==1).all()
                data=[]
                obj = {}
                for p in pag:
                    p = eval(str(p))
                    for u in user_name:
                        u = eval(str(u))
                        if int(p['user_id'])==int(u[1]):
                            if u[0] not in obj.keys():
                                obj[u[0]] =[]
                                obj[u[0]].append(p)
                            else:
                                obj[u[0]].append(p)
                return self.returnTypeSuc(obj)

            elif kt == 'GetChartDict':#获取折线图下拉框
                """
                优化定容获取折线图
                """
                id = self.get_argument('id')
                pag = user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.is_use == 1,
                                                                            ForecaseUseEleEleLoadOffer.id == id).first()
                e = {}
                ti_di=[]
                pw_di=[]
                if pag:
                    e = eval(str(pag))
                    try:
                        df = pd.read_excel(pag.file_path, sheet_name="Input")
                        ti_list = df.loc[:, '日期']
                        for ti in ti_list:
                            ti = str(ti)[:10]
                            if ti not in ti_di:
                                ti_di.append(ti)
                        pw_list = df.loc[:, '容量/需量']
                        for pw in pw_list:
                            if pw not in pw_di:
                                pw_di.append(pw)
                    except Exception as e:
                        return self.customError('上传负荷文件有误，请检查后重新上传！')
                e['day_']=ti_di
                e['capacity']=pw_di
                return self.returnTypeSuc(e)

            elif kt == 'GetMappings':
                """
                获取项目经理、重要程度下拉框
                """
                data = {
                    'user_info': [],
                    'priority': [
                        {
                            'id': 1,
                            'name': '一星'
                        },
                        {
                            'id': 2,
                            'name': '二星'
                        },
                        {
                            'id': 3,
                            'name': '三星'
                        },
                        {
                            'id': 4,
                            'name': '四星'
                        },
                        {
                            'id': 5,
                            'name': '五星'
                        }

                    ]
                }
                user_res = user_session.query(ForecaseUser).filter(ForecaseUser.is_use == '1').all()
                for user in user_res:
                    data['user_info'].append({
                        'id': user.id,
                        'name': user.name
                    })
                return self.returnTypeSuc(data)

            elif kt == 'GetTemplates':
                """收资要求及模版/电费情况附件下载"""
                _type = self.get_argument('type')   # 1：收资要求及模版；2：电费情况
                if not _type:
                    return self.customError('参数错误！')
                minioClient = MinioTool()
                data = {}
                _type = '2'  # 跟产品确认过，收资要求模板统一使用电费情况模板
                if _type == '1':
                    for i in ['负荷曲线模板.xlsx', '横版数据处理模板-1小时读数用电量版.xlsx', '横版数据处理模板-15分钟用电量版.xlsx', '项目收资及电费单整理模板.xlsx', '材料清单自查表.xlsx', '补充实控人信息表.xlsx']:
                        url = minioClient.get_download_url('side', f'template/{i}')
                        data[i] = url
                else:
                    for i in ['横版数据处理模板-1小时读数用电量版.xlsx', '横版数据处理模板-15分钟用电量版.xlsx', '时序模拟-模板.xlsx', '项目收资及电费单整理模板.xlsx', '材料清单自查表.xlsx', '补充实控人信息表.xlsx']:
                        url = minioClient.get_download_url('side', f'template2/{i}')
                        data[i] = url
                return self.returnTypeSuc(data)

            elif kt == 'GetTemplatesPhase':
                """收资归档阶段一键下载功能"""
                stag_flag = self.get_argument('stag_flag')   # 1：第一阶段；2：第二阶段
                project_id = self.get_argument('project_id')   # 项目ID
                base_name = self.get_argument('base_name')   # 栏目名称
                if not stag_flag or not project_id or not base_name:
                    return self.customError('参数错误！')
                # 查询各阶段下附件类型ID
                files_type = user_session.query(ForecaseBaseFile).filter(
                    ForecaseBaseFile.module_name.in_(dangan_yh), ForecaseBaseFile.is_use == 1,
                    ForecaseBaseFile.stag_flag == stag_flag, ForecaseBaseFile.name == base_name).all()
                file_type_ids = [i.id for i in files_type]

                # 封装该阶段下所有附件信息
                file_res = user_session.query(ForecaseFiles).filter(ForecaseFiles.project_id == int(project_id),
                                                         ForecaseFiles.base_file_id.in_(file_type_ids),
                                                         ForecaseFiles.is_use == 1).all()
                data = []
                for file in file_res:
                    data.append(
                        {
                            'file_name': file.file_name,
                            'file_path': file.file_path
                        }
                    )
                return self.returnTypeSuc(data)

            elif kt == 'GetReferenceStandard':
                """定容参考标准查询"""
                res = user_session.query(ReferenceStandard).all()
                data = []
                for i in res:
                    data.append(
                        {
                            "name": i.name,
                            "count": float(i.count),
                            "id": i.id
                        }
                    )
                return self.returnTypeSuc(data)

            elif kt == 'DeleteReferenceStandard':
                """定容参考删除"""
                _id = self.get_argument('id')
                if not _id:
                    return self.customError('参数错误！')
                session = self.getOrNewSession()
                user_role_id = session.user['user_role_id']
                if int(user_role_id) != 1:  # 超级管理员
                    return self.customError('非超级管理员用户不可操作！')
                user_session.query(ReferenceStandard).filter(ReferenceStandard.id == int(_id)).delete()
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'GetArgument':
                """经评计算-历史数据列表"""
                project_id = self.get_argument('project_id')
                if not project_id:
                    return self.customError('参数错误！')
                res = user_session.query(ArgumentCalcuate).filter(ArgumentCalcuate.project_id == int(project_id)).all()
                data = []
                for i in res:
                    data.append(
                        {
                            "id": i.id,
                            "create_time": i.create_time.strftime("%Y-%m-%d"),
                            "user_name": i.user.name,
                            "data": json.loads(i.data)
                        }
                    )

                return self.returnTypeSuc(data)

            elif kt == 'GetChart':  # 获取折线图
                """
                优化定容获取折线图
                """
                # 项目ID
                id = self.get_argument('id')
                tida = self.get_argument('tida', [])  # 日期
                capacity = self.get_argument('capacity', None)  # 容量
                tida = eval(str(tida))
                tida = timeUtils.dateToDataList(tida[0], tida[1])
                capacity = int(capacity)
                obj = []
                if id:
                    id = int(id)
                else:
                    return self.write({"code": 401, "msg": "参数错误，项目ID不允许为空", "data": []})
                type_ = self.get_argument('type', '1')  # 1：客户提供负荷；2：踏勘采集负荷
                session = self.getOrNewSession()
                user_id = session.user['id']
                page = user_session.query(ForecaseUseEleEleLoadOffer.file_path,ForecaseUseEleEleLoadOffer.capacity_config,
                                           ForecaseUseEleEleLoadOffer.id).filter(ForecaseUseEleEleLoadOffer.is_use == 1,
                                                                                 ForecaseUseEleEleLoadOffer.id == id).first()
                if not page:
                    return self.customError("无效id")

                optimum_eoposal = OptimumProposal()
                res = {}
                if not page:
                    return self.returnTypeSuc(res)
                try:
                    power_out_all = []
                    if page:
                        equipment_sum = 1
                        if page[1]:
                            capacity_config = json.loads(page[1])
                            equipment_sum = capacity_config.get('equipment_sum')
                        # 优化计算
                        data = optimum_eoposal.optimum_proposal_calculate_new(file_path=page[0],
                                                                              equipment_sum=equipment_sum,
                                                                              capacity=capacity, id=id, user_id=user_id,
                                                                              type_=type_)
                        power_out_all_1 = data['power_out_all']
                        df = pd.read_excel(page[0], sheet_name="Input")
                        for ti in tida:
                            ee = {}
                            ee[ti] = []
                            if capacity != None:
                                dd = df.loc[df['日期'] == ti, ["负荷", "容量/需量", "变压器容量"]]
                                row_indices = df[df['日期'] == ti].index
                                load = df.loc[df['日期'] == ti, ["负荷"]]  # 负荷
                                load = load.values.tolist()
                                load_value = [round(num[0], 2) for num in load]
                                load = np.array(load)
                                dd = dd.values.tolist()
                                dd = [[round(x, 2) for x in sublist] for sublist in dd]
                                for p in power_out_all_1:
                                    inx_2 = power_out_all_1.index(p)
                                    for ii in row_indices:
                                        if inx_2==(ii-1):
                                            power_out_all.append(p)
                                power_out = [round(power_out_all[0], 2) for power_out_all in load]  # 储能充放功率模拟
                                result = (np.around([x - y for x, y in zip(load_value, power_out)], 2)).tolist()  # 负荷叠加曲线
                                for i in range(24):
                                    dd[i].append(power_out[i])
                                    dd[i].append(result[i])
                                ee[ti] = dd
                            obj.append(ee)
                except Exception as e:
                    logging.error(e)
                return self.returnTypeSuc(obj)

        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    @tornado.gen.coroutine
    def post(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt=='AddProject':  # 添加项目
                sop = self.get_argument('sop',None) # sop
                province_name = self.get_argument('province_name',None) # 省份名称
                city_name = self.get_argument('city_name',None) # 城市省份名称
                owner_type = self.get_argument('owner_type',None) # 企业性质，中文
                run_type = self.get_argument('run_type',None) # 经营类二级下拉
                indus = self.get_argument('indus',None) # 所属行业二级下拉

                owner_name = self.get_argument('owner_name',None) # 企业名称
                owner_short = self.get_argument('owner_short',None) # 企业简称
                own_status = self.get_argument('own_status',None) # 产权情况下拉
                # sn = self.get_argument('sn',None) # 项目编号  自动生成
                other_status = self.get_argument('other_status',None) # 其他情况
                own_year = self.get_argument('own_year',None) # 产权剩余年限
                run_day = self.get_argument('run_day',None) # 生产经营天数，列表[1-12月加一个总数]
                owner_introduce = self.get_argument('owner_introduce',None) # 企业介绍
                project_level = self.get_argument('project_level',None) # 项目紧急程度，分数
                project_level_explain = self.get_argument('project_level_explain',None) # 项目紧急程度说明
                sign_status = self.get_argument('sign_status',None) # 签约情况，下拉选择
                sign_status_explain = self.get_argument('sign_status_explain',None) # 签约信息说明
                # 商务资料
                owner_label = self.get_argument('owner_label',None) # 项目类别 下拉
                device_type = self.get_argument('device_type',None) # 设备型号 下拉
                expect_device_num = self.get_argument('expect_device_num',None) # 预期设备数量
                expect_emc_time = self.get_argument('expect_emc_time',None) # 预期EMC合同期限
                expect_emc_share_type = self.get_argument('expect_emc_share_type',None) # 预期EMC分享模式
                expect_emc_share = self.get_argument('expect_emc_share',None) # 预期EMC分享比例
                special_content = self.get_argument('special_content',None) # 产品及商务特殊需求

                is_use = self.get_argument('is_use',1)  # 是否使用1是0否2暂存
                session = self.getOrNewSession()
                user_name = session.user['name']
                user_id = session.user['id']
                t = int(time.time() * 1000)
                sn = 'RHTL-{}'.format(t)  # 项目编号  自动生成
                if DEBUG:
                    logging.info("province_name:%s,city_name:%s,owner_type:%s,run_type:%s,indus:%s,owner_name:%s,owner_short:%s,own_status:%s,sn:%s,other_status:%s,own_year:%s,run_day:%s,\
                                owner_introduce：%s, project_level:%s,project_level_explain:%s,sign_status:%s,sign_status_explain:%s,owner_label:%s,device_type:%s,expect_device_num:%s,expect_emc_time:%s,\
                                 expect_emc_share_type:%s,expect_emc_share：%s,special_content:%s,is_use:%s"%(
                        province_name,city_name,owner_type,run_type,indus,owner_name,owner_short,own_status,sn,other_status,own_year,run_day,owner_introduce,project_level,project_level_explain,
                        sign_status,sign_status_explain,owner_label,device_type,expect_device_num,expect_emc_time,expect_emc_share_type,expect_emc_share,special_content,is_use))
                if not province_name or not city_name or not owner_type or not run_type or not owner_name or not owner_short or not own_status or not own_year or not owner_label or not project_level\
                        or not sign_status:
                    return self.customError('参数不完成')

                ntime = timeUtils.getNewTimeStr()
                p = ForecaseProject(sop=sop,sn=sn,user_name=user_name,user_id=user_id,province_name=province_name,city_name=city_name,owner_type=owner_type,run_type=run_type,
                    indus=indus,owner_name=owner_name,owner_short=owner_short,own_status=own_status,other_status=other_status,own_year=own_year,run_day=run_day,owner_introduce=owner_introduce,
                    project_level=project_level,project_level_explain=project_level_explain,sign_status=sign_status,sign_status_explain=sign_status_explain,owner_label=owner_label,device_type=device_type,
                    expect_device_num=expect_device_num,expect_emc_time=expect_emc_time,expect_emc_share_type=expect_emc_share_type,expect_emc_share=expect_emc_share,special_content=special_content,
                    is_use=is_use,op_ts=ntime,update_time=ntime)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc({"id":p.id})
            elif kt=='UpdateProject':  # 更新项目信息
                id = self.get_argument('id',None) # 姓名
                sop = self.get_argument('sop',None) # sop
                province_name = self.get_argument('province_name',None) # 省份名称
                city_name = self.get_argument('city_name',None) # 城市省份名称
                owner_type = self.get_argument('owner_type',None) # 企业性质，中文
                run_type = self.get_argument('run_type',None) # 经营类二级下拉
                indus = self.get_argument('indus',None) # 所属行业二级下拉

                owner_name = self.get_argument('owner_name',None) # 企业名称
                owner_short = self.get_argument('owner_short',None) # 企业简称
                own_status = self.get_argument('own_status',None) # 产权情况下拉
                sn = self.get_argument('sn',None) # 项目编号  自动生成
                other_status = self.get_argument('other_status',None) # 其他情况
                own_year = self.get_argument('own_year',None) # 产权剩余年限
                run_day = self.get_argument('run_day',None) # 生产经营天数，列表[1-12月加一个总数]
                owner_introduce = self.get_argument('owner_introduce',None) # 企业介绍
                project_level = self.get_argument('project_level',None) # 项目紧急程度，分数
                project_level_explain = self.get_argument('project_level_explain',None) # 项目紧急程度说明
                sign_status = self.get_argument('sign_status',None) # 签约情况，下拉选择
                sign_status_explain = self.get_argument('sign_status_explain',None) # 签约信息说明
                # 商务资料
                owner_label = self.get_argument('owner_label',None) # 项目类别 下拉
                device_type = self.get_argument('device_type',None) # 设备型号 下拉
                expect_device_num = self.get_argument('expect_device_num',None) # 预期设备数量
                expect_emc_time = self.get_argument('expect_emc_time',None) # 预期EMC合同期限
                expect_emc_share_type = self.get_argument('expect_emc_share_type',None) # 预期EMC分享模式
                expect_emc_share = self.get_argument('expect_emc_share',None) # 预期EMC分享比例
                special_content = self.get_argument('special_content',None) # 产品及商务特殊需求

                is_use = self.get_argument('is_use',1)  # 是否使用1是0否2暂存

                if DEBUG:
                    logging.info("id:%s,province_name:%s,city_name:%s,owner_type:%s,run_type:%s,indus:%s,owner_name:%s,owner_short:%s,own_status:%s,sn:%s,other_status:%s,own_year:%s,run_day:%s,\
                                owner_introduce：%s, project_level:%s,project_level_explain:%s,sign_status:%s,sign_status_explain:%s,owner_label:%s,device_type:%s,expect_device_num:%s,expect_emc_time:%s,\
                                 expect_emc_share_type:%s,expect_emc_share：%s,special_content:%s,is_use:%s"%(
                        id,province_name,city_name,owner_type,run_type,indus,owner_name,owner_short,own_status,sn,other_status,own_year,run_day,owner_introduce,project_level,project_level_explain,
                        sign_status,sign_status_explain,owner_label,device_type,expect_device_num,expect_emc_time,expect_emc_share_type,expect_emc_share,special_content,is_use))

                page = user_session.query(ForecaseProject).filter(ForecaseProject.id==id).first()
                if not page:
                    return self.customError('无效id')
                page.sop = sop
                page.province_name = province_name
                page.city_name = city_name
                page.owner_type = owner_type
                page.run_type = run_type
                page.indus = indus
                page.owner_name = owner_name
                page.owner_short = owner_short
                page.own_status = own_status
                page.other_status = other_status
                page.own_year = own_year
                page.run_day = run_day
                page.owner_introduce = owner_introduce
                page.project_level = project_level
                page.project_level_explain = project_level_explain
                page.sign_status = sign_status
                page.sign_status_explain = sign_status_explain
                page.owner_label = owner_label
                page.device_type = device_type
                page.expect_device_num = expect_device_num
                page.expect_emc_time = expect_emc_time
                page.expect_emc_share_type = expect_emc_share_type
                page.expect_emc_share = expect_emc_share
                page.special_content = special_content
                page.is_use = is_use
                page.update_time = timeUtils.getNewTimeStr()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='DeleteProject':  # 删除项目
                id = self.get_argument('id',None) # id
                page = user_session.query(ForecaseProject).filter(ForecaseProject.id==id,ForecaseProject.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='DeleteProjectFiles':  # 删除项目文件
                id = self.get_argument('id',None) # 具体文件id
                id_pro = self.get_argument('id_pro',None) # 项目id
                page = user_session.query(ForecaseFiles).filter(ForecaseFiles.id==id,ForecaseFiles.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                if page.file_path:
                    # os.remove(page.file_path)
                    page.file_path = ''
                page.is_use = 0
                ntime=self.logs_(2,id_pro,page.file_name)
                user_session.commit()
                return self.returnTypeSuc({"uptime":ntime})
            elif kt=='UpdateProjectFile':  # 更新项目文件
                id = self.get_argument('id',None) # id
                remarks = self.get_argument('remarks',None) # 备注
                file_type = self.get_argument('file_type',None) # 文件类型
                if DEBUG:
                    logging.info('id:%s,file_type:%s,remarks:%s'%(id,file_type,remarks))
                page = user_session.query(ForecaseFiles).filter(ForecaseFiles.id==id,ForecaseFiles.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                files = self.request.files
                imgs = files.get('files')
                if imgs:
                    data = imgs[0].get('body')
                    f = SideForecaseFilename(1)
                    filenames = f.filename(imgs[0])
                    download_url = upload_file(data, 'side', filenames)
                    if not download_url:
                        return self.customError("上传附件超过100MB限制，请检查！")
                    size = len(data)
                    file_size = f'{round(size/1024, 2)} KB' if size < 1048576 else f'{round(size/1048576, 2)} MB'
                    page.file_name = filenames
                    page.file_size = file_size
                    page.file_path = download_url.split('?')[0]

                    page.op_ts = timeUtils.getNewTimeStr()
                    page.remarks = remarks
                    page.file_type = file_type
                    ntime=self.logs_(1, page.project_id ,filenames)
                    user_session.commit()
                    return self.returnTypeSuc({"uptime":ntime})
            elif kt=='AddProjectFile':  # 添加项目文件
                id = self.get_argument('id',None) # 项目id
                base_file = self.get_argument('base_file',None) # 基础文件id
                file_type = self.get_argument('file_type',None) # 文件类型
                remarks = self.get_argument('remarks',None) # 备注
                if DEBUG:
                    logging.info('id:%s,file_type:%s,base_fils_id:%s,remarks:%s'%(id,file_type,base_file,remarks))
                if not id or not base_file:
                    return self.customError("入参不完整")
                files = self.request.files
                imgs = files.get('files')
                e={}
                if imgs:
                    data = imgs[0].get('body')
                    size = len(data)
                    file_size = f'{round(size / 1024, 2)} KB' if size < 1048576 else f'{round(size / 1048576, 2)} MB'
                    f = SideForecaseFilename(1)
                    filename = f.filename(imgs[0])
                    download_url = upload_file(data, 'side', filename)
                    if not download_url:
                        logging.info("id:%s,file_name:%s," % (id, filename))
                        return self.customError("上传附件超过100MB限制，请检查！")

                    download_url = download_url.split('?')[0]

                    user_session.query(ForecaseBaseFile).filter(ForecaseBaseFile.id == base_file).update({"is_file":1})
                    d = ForecaseFiles(project_id=id,base_file_id=base_file,file_name = filename,file_type=file_type,file_path=download_url,remarks=remarks,op_ts=timeUtils.getNewTimeStr(), file_size=file_size)
                    user_session.add(d)
                    ntime=self.logs_(1,id,filename)
                    user_session.commit()
                    e={"id":d.id,"uptime":ntime}
                return self.returnTypeSuc(e)
            elif kt=='AddProjectBaseFiles':  # 添加项目基本文件信息分类
                # user_type = Column(VARCHAR(25), nullable=False, comment="用户类型；用户侧、独立储能")
                stag_flag = self.get_argument('stag_flag',1) # 阶段表示  默认1
                module_name = self.get_argument('module_name',None) # 模块分类名称
                name = self.get_argument('name',None) # 具体文件分类名称
                type_name = self.get_argument('type_name',None) # 分类名称
                if DEBUG:
                    logging.info('stag_flag:%s,module_name:%s,name:%s,type_name:%s'%(stag_flag,module_name,name,type_name))
                if not module_name or not name or not type_name:
                    return self.customError("入参不完整")
                d = ForecaseBaseFile(stag_flag=stag_flag,module_name=module_name,name = name,type_name=type_name,user_type='用户侧',op_ts=timeUtils.getNewTimeStr())
                user_session.add(d)
                user_session.commit()
                return self.returnTypeSuc({"id":d.id})
            elif kt=='UpdateProjectBaseFiles':  # 添加项目基本文件信息分类
                id = self.get_argument('id',1) # 主键
                stag_flag = self.get_argument('stag_flag',1) # 阶段表示  默认1
                module_name = self.get_argument('module_name',None) # 模块分类名称
                name = self.get_argument('name',None) # 具体文件分类名称
                type_name = self.get_argument('type_name',None) # 分类名称
                if DEBUG:
                    logging.info('id:%s,stag_flag:%s,module_name:%s,name:%s,type_name:%s'%(id,stag_flag,module_name,name,type_name))
                page = user_session.query(ForecaseBaseFile).filter(ForecaseBaseFile.id==id,ForecaseBaseFile.is_use==1).first()
                if not page:
                    return self.customError('无效id')

                page.stag_flag = stag_flag
                page.module_name = module_name
                page.name = name
                page.type_name = type_name

                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='DeleteProjectBaseFiles':  # 删除项目文件
                id = self.get_argument('id',None) # 具体文件id
                id_pro = self.get_argument('id_pro', None)  # 项目id
                page = user_session.query(ForecaseBaseFile).filter(ForecaseBaseFile.id==id,ForecaseBaseFile.is_use==1).first()
                ntime = self.logs_(2, id_pro, page.type_name)
                if not page:
                    return self.customError('无效id')
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc({"uptime":ntime})
            elif kt=='AddProjectUseEleInfo':  # 添加用电信息电力情况信息
                id = self.get_argument('id',None) # 项目id
                power_plan_f = self.get_argument('power_plan_f',None)  # 是否有自备电厂
                power_cap = self.get_argument('power_cap',None)  # 已有电厂容量
                plan_cap = self.get_argument('plan_cap',None)  # 计划电厂容量
                cpv_f = self.get_argument('cpv_f',None)  # 是否有光伏接入
                cpv_cap = self.get_argument('cpv_cap',None)  # 已有光伏容量
                plan_cpv_cap = self.get_argument('plan_cpv_cap',None)  # 计划光伏容量
                if DEBUG:
                    logging.info("id:%s,power_plan_f:%s,power_cap:%s,plan_cap:%s,cpv_f:%s,cpv_cap:%s,plan_cpv_cap:%s"%(id,power_plan_f,power_cap,plan_cap,cpv_f,cpv_cap,plan_cpv_cap))

                d = ForecaseUseEleEleInfo(power_plan_f=power_plan_f,power_cap=power_cap,plan_cap = plan_cap,cpv_f=cpv_f,cpv_cap=cpv_cap,plan_cpv_cap=plan_cpv_cap,
                                          project_id=id,op_ts=timeUtils.getNewTimeStr())
                user_session.add(d)
                user_session.commit()
                return self.returnTypeSuc({"id":d.id})
            elif kt=='UpdateProjectUseEleInfo':  # 修改用电信息电力情况信息
                id = self.get_argument('id',None) # 主键id
                power_plan_f = self.get_argument('power_plan_f',None)  # 是否有自备电厂
                power_cap = self.get_argument('power_cap',None)  # 已有电厂容量
                plan_cap = self.get_argument('plan_cap',None)  # 计划电厂容量
                cpv_f = self.get_argument('cpv_f',None)  # 是否有光伏接入
                cpv_cap = self.get_argument('cpv_cap',None)  # 已有光伏容量
                plan_cpv_cap = self.get_argument('plan_cpv_cap',None)  # 计划光伏容量
                if DEBUG:
                    logging.info("id:%s,power_plan_f:%s,power_cap:%s,plan_cap:%s,cpv_f:%s,cpv_cap:%s,plan_cpv_cap:%s"%(id,power_plan_f,power_cap,plan_cap,cpv_f,cpv_cap,plan_cpv_cap))

                page = user_session.query(ForecaseUseEleEleInfo).filter(ForecaseUseEleEleInfo.id==id,ForecaseUseEleEleInfo.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                page.power_plan_f = power_plan_f
                page.power_cap = power_cap
                page.plan_cap = plan_cap
                page.cpv_f = cpv_f
                page.cpv_cap = cpv_cap
                page.plan_cpv_cap = plan_cpv_cap
                page.op_ts = timeUtils.getNewTimeStr()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='DeleteProjectUseEleInfo':  # 删除用电信息电力情况信息
                id = self.get_argument('id',None) # 项目id
                if DEBUG:
                    logging.info("id:%s"%(id))

                page = user_session.query(ForecaseUseEleEleInfo).filter(ForecaseUseEleEleInfo.id==id,ForecaseUseEleEleInfo.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='AddProjectUseEleFile':  # 添加用电信息电费情况信息
                id = self.get_argument('id',None) # 项目id
                file_name = self.get_argument('file_name',None) # 文件名称
                if file_name.split('.')[-1] != 'xlsx' and file_name.split('.')[-1] != 'xls':
                    return self.customError('上传文件格式错误！')
                files = self.request.files
                imgs = files.get('files')
                e={}
                if imgs:
                    data = imgs[0].get('body')
                    f = SideForecaseFilename(1)
                    filename = f.filename(imgs[0])
                    download_url = upload_file(data, 'side',filename)
                    if not download_url:
                        logging.info("id:%s,file_name:%s," % (id, file_name))
                        return self.customError("上传附件超过100MB限制，请检查！")
                    download_url=download_url.split('?')[0]
                    d = ForecaseUseEleEleFile(project_id=id,file_name=file_name,file_path=download_url,op_ts=timeUtils.getNewTimeStr())
                    user_session.add(d)
                    ntime=self.logs_(1,id,filename)
                    user_session.commit()
                    e = {"id":d.id, "file_name":d.file_name,"uptime":ntime}
                return self.returnTypeSuc(e)

            elif kt=='AddProjectUseEleFileForm': # 添加用电信息电费情况信息表单信息
                id = self.get_argument('id', None)  # 电费信息表ID
                ele_id = self.get_argument('ele_id', None)  # 用电分类id
                ele_name = self.get_argument('ele_name', None)  # 用电分类名称
                vol_id = self.get_argument('vol_id', None)  # 电压等级id
                vol_name = self.get_argument('vol_name', None)  # 电压等级名称
                sell_ele = self.get_argument('sell_ele', None)  # 购售电信息
                demand_f = self.get_argument('demand_f', None)  # 1按荣收费2按需收费
                d = user_session.query(ForecaseUseEleEleFile).get(id)
                path = d.file_path
                d.ele_id = ele_id
                d.ele_name = ele_name
                d.vol_name = vol_name
                d.vol_id = vol_id
                d.sell_ele = sell_ele
                d.demand_f = demand_f
                ntime = self.logs_(1, id,d.file_name)
                user_session.commit()
                e = {"id": d.id, "file_name": d.file_name, "uptime": ntime}
                e['data1'] = self._getexceldata(path,2,13,range(1,8))  # 用电量及电费数据统计
                e['data2'] = self._getexceldata(path,16,4,range(2))  # 饼图
                e['data3'] = self._getexceldata(path,2,13,range(8,17))  # 用电电费
                e['data4'] = self._getexceldata(path,2,13,range(17,24))  # 度电电费
                e['data5'] = self._getexceldata(path,2,13,range(24,28))  # 容量信息
                return self.returnTypeSuc(e)

            elif kt=='DeleteProjectUseEleFile':  # 删除用电信息电费情况信息
                id = self.get_argument('id',None) # 主键id
                if DEBUG:
                    logging.info("id:%s"%(id))

                page = user_session.query(ForecaseUseEleEleFile).filter(ForecaseUseEleEleFile.id==id,ForecaseUseEleEleFile.is_use==1).first()
                if not page:
                    return self.customError('无效id')
                if page.file_path:
                    # os.remove(page.file_path)
                    page.file_path = ''
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt=='AddProjectUseEleLoadOffe':  # 添加用电信息负荷数据/勘探负荷
                id = self.get_argument('id',None) # 项目id
                file_name = self.get_argument('file_name',None) # 文件名称
                if file_name.split('.')[-1] != 'xlsx' and file_name.split('.')[-1] != 'xls':
                    return self.customError('上传文件格式错误！')
                type_ = self.get_argument('type_',1) # 类型id
                files = self.request.files
                imgs = files.get('files')
                e={}
                if imgs:
                    try:
                        data = imgs[0].get('body')
                        f = SideForecaseFilename(1)
                        filename = f.filename(imgs[0])
                        download_url = upload_file(data, 'side', filename)
                        if not download_url:
                            return self.customError("上传附件超过100MB限制，请检查！")
                        df = pd.read_excel(download_url, sheet_name="Input")
                        value = df.loc[0, '起始日期']
                        if not download_url:
                            logging.info("id:%s,file_name:%s," % (id, filename))
                        download_url = download_url.split('?')[0]
                    except:
                        return self.customError('校验失败！请检查参数')
                    d = ForecaseUseEleEleLoadOffer(file_name=filename, file_path=download_url,project_id=id,op_ts=timeUtils.getNewTimeStr(),is_use='1',type=type_,sta_ti=str(value))
                    user_session.add(d)
                    user_session.commit()
                    ntime=self.logs_(1,id,filename)
                    user_session.commit()
                    e={"id":d.id,"uptime":ntime,"sta_ti":str(value)}
                return self.returnTypeSuc(e)
            elif kt=='DeleteProjectUseEleLoadOffe':  # 删除电信息负荷数据
                id = self.get_argument('id',None) # 主键id
                id_pro = self.get_argument('id_pro', None)  # 项目id
                if DEBUG:
                    logging.info("id:%s"%(id))

                page = user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.id==id,ForecaseUseEleEleLoadOffer.is_use==1).first()
                ntime = self.logs_(2, page.project_id, page.file_name)
                if not page:
                    return self.customError('无效id')
                if page.file_path:
                    # os.remove(page.file_path)
                    page.file_path = ''
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc({'uptime':ntime})

            elif kt == 'AddCapacityConfig':
                "优化定容-配置容量"
                id = self.get_argument('id', None)  # 电表单ID
                id_pro = self.get_argument('id_pro', None)  # 项目id
                device_type = self.get_argument('device_type', None)  # 设备类型
                equipment_sum = self.get_argument('equipment_sum', None)  # 设备台数
                price = self.get_argument('price', None)  # 预估主设备价格
                recommend_power = self.get_argument('recommend_power', None)  # 推荐功率
                recommend_capacity = self.get_argument('recommend_capacity', None)  # 推荐容量
                capacity_number = self.get_argument('capacity_number', None)  # 容量增减
                reason = self.get_argument('reason', None)  # 修改原因
                session = self.getOrNewSession()
                user_name = session.user['name']
                data = {}
                count = int(equipment_sum) + int(capacity_number)
                data['device_type'] = device_type
                data['equipment_sum'] = count

                data['price'] = eval(price) / int(equipment_sum) * count
                data['recommend_power'] = eval(recommend_power) / int(equipment_sum) * count
                data['recommend_capacity'] = eval(recommend_capacity) / int(equipment_sum) * count
                data['user_name'] = user_name
                data['reason'] = reason
                # 更新踏勘采集负荷文件 优化定容-配置容量信息
                user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.id == int(id))\
                    .update({'capacity_config': json.dumps(data)})
                ntime=self.logs_(4, user_session.query(ForecaseUseEleEleLoadOffer).filter(ForecaseUseEleEleLoadOffer.id == int(id)).first().project_id)
                data['uptime'] = ntime
                user_session.commit()
                return self.returnTypeSuc(data)

            elif kt == 'calculate':
                """
                经评计算
                """
                try:
                    data = {
                        'project_life': self.get_argument('project_life'),
                        'project_name': self.get_argument('project_name'),
                        'project_address': self.get_argument('project_address'),
                        'province': self.get_argument('province'),
                        'product_name': self.get_argument('product_name'),
                        'product_voltage': self.get_argument('product_voltage'),
                        'product_MW': float(self.get_argument('product_MW')),
                        'product_MWh': float(self.get_argument('product_MWh')),
                        'MV_all': float(self.get_argument('MV_all')),
                        'MWh_all': float(self.get_argument('MWh_all')),
                        'product_total': int(self.get_argument('product_total')),
                        'charge_eff': float(self.get_argument('charge_eff')),
                        'discharge_eff': float(self.get_argument('discharge_eff')),
                        'equipment_czl': float(self.get_argument('equipment_czl')),
                        'frequency': int(self.get_argument('frequency')),
                        'lifetime': int(self.get_argument('lifetime')),
                        'DOD': float(self.get_argument('DOD')),
                        'EOL': float(self.get_argument('EOL')),
                        'battery_czl': float(self.get_argument('battery_czl')),
                        'project_cost_all': float(self.get_argument('project_cost_all')),
                        'product_cost': float(self.get_argument('product_cost')),
                        'engineering_price': float(self.get_argument('engineering_price')),
                        'cost_of_labor': float(self.get_argument('cost_of_labor')),
                        'material_cost': float(self.get_argument('material_cost')),
                        'machinery_cost': float(self.get_argument('machinery_cost')),
                        'other_charges': float(self.get_argument('other_charges')),
                        'consulting_fee': float(self.get_argument('consulting_fee')),
                        'ops_cost_of_labor': float(self.get_argument('ops_cost_of_labor')),
                        'product_maintenance_cost': float(self.get_argument('product_maintenance_cost')),
                        'premium': float(self.get_argument('premium')),
                        'AAGR': float(self.get_argument('AAGR')),
                        'capital_ratio': float(self.get_argument('capital_ratio')),
                        'construction_ratio': float(self.get_argument('construction_ratio')),
                        'construction_cycle': float(self.get_argument('construction_cycle')),
                        'construction_deductible_VAT': float(self.get_argument('construction_deductible_VAT')),
                        'construction_financial_cost': float(self.get_argument('construction_financial_cost')),
                        'construction_mode_of_repayment': self.get_argument('construction_mode_of_repayment'),
                        'operation_ratio': float(self.get_argument('operation_ratio')),
                        'operation_cycle': int(self.get_argument('operation_cycle')),
                        'operation_deductible_VAT': float(self.get_argument('operation_deductible_VAT')),
                        'operation_financial_cost': float(self.get_argument('operation_financial_cost')),
                        'operation_mode_of_repayment': self.get_argument('operation_mode_of_repayment'),
                        'use_day_frequency': float(self.get_argument('use_day_frequency')),
                        'use_day_year': float(self.get_argument('use_day_year')),
                        'use_ratio': float(self.get_argument('use_ratio')),
                        'EMC_share': self.get_argument('EMC_share'),
                        'third_party': float(self.get_argument('third_party')),
                        'bearer': float(self.get_argument('bearer')),
                        'change_ratio': float(self.get_argument('change_ratio')),
                        'first_stage': ast.literal_eval(self.get_argument('first_stage')),
                        'second_stage': ast.literal_eval(self.get_argument('second_stage')),
                        'policy_subsidy': ast.literal_eval(self.get_argument('policy_subsidy')),
                        'Result3': ast.literal_eval(self.get_argument('Result3')),
                        'id': int(self.get_argument('id'))
                    }
                    if data.get('EMC_share') == '1.固定分享':
                        data['first_stage'][2] = data['third_party']
                    calculate = Calculate(data)
                    res = calculate.calculate()
                except Exception as e:
                    logging.error(data)
                    logging.error(e)
                    return self.customError('计算失败！请检查参数')
                return self.returnTypeSuc(res)

            elif kt == 'calculateSave':
                """
                经评计算保存接口
                """
                id = self.get_argument('id', None)
                input_json = self.get_argument('input_json')
                output_json = self.get_argument('output_json')
                calculate = user_session.query(ForecaseCalcuate).filter(ForecaseCalcuate.p_id == int(id)).first()
                if calculate:
                    user_session.query(ForecaseCalcuate).filter(ForecaseCalcuate.p_id == int(id)) \
                        .update({'input_json': input_json, 'output_json': output_json})
                else:
                    res = ForecaseCalcuate(input_json=input_json, output_json=output_json, p_id=id)
                    user_session.add(res)
                ntime=self.logs_(5,id)
                user_session.commit()
                return self.returnTypeSuc({'uptime':ntime})

            elif kt == 'calculateImport':
                """
                经评计算导出功能
                """
                type = self.get_argument('type', '0') # 0：计算结果  1：敏感性表格 2：经评计算第一个表格
                data = ast.literal_eval(self.get_argument('data','{}'))  # 数据
                t = int(time.time() * 1000)
                wb = Workbook()
                s = wb.active
                if type == '0':
                    for x, line in enumerate(data):
                        for i, k in enumerate(line):
                            s.cell(row=x + 1, column=i + 1).value = k
                elif type == '1':
                    s.cell(row=1, column=1).value = data[0][0]
                    s.cell(row=1, column=3).value = data[0][1]
                    s.cell(row=2, column=1).value = data[1][0]
                    s.cell(row=3, column=1).value = data[2][0]

                    for i in range(9):
                        s.cell(row=i + 3, column=2).value = data[i + 2][1]
                        s.cell(row=2, column=i + 3).value = data[1][i + 1]

                    for l, line in enumerate(data[2:]):
                        for i, k in enumerate(line[2:]):
                            s.cell(row=l + 3, column=i + 3).value = k

                    s.merge_cells('C1:K1')
                    s.merge_cells('A1:B1')
                    s.merge_cells('A2:B2')
                    s.merge_cells('A3:A11')
                    s['C1'].alignment = Alignment(horizontal='center', vertical='center')
                    s['A1'].alignment = Alignment(horizontal='center', vertical='center')
                    s['A2'].alignment = Alignment(horizontal='center', vertical='center')
                    s['A3'].alignment = Alignment(horizontal='center', vertical='center')
                else:
                    for x, line in enumerate(data):
                        for i, k in enumerate(line):
                            s.cell(row=x + 1, column=i + 1).value = k

                output = BytesIO()
                wb.save(output)
                self.set_header('Content-Type', 'application/x-xls')
                self.set_header('Content-Disposition', 'attachment; filename={}.xlsx'.format(t))

                return self.write(output.getvalue())

            elif kt == 'GetChart':  # 获取折线图
                """
                优化定容获取折线图
                """
                # 项目ID
                id = self.get_argument('id')
                tida = self.get_argument('tida', [])  # 日期
                capacity = self.get_argument('capacity', None)  # 容量
                power_result = self.get_argument('power_result', None)  # power_out_all
                if not power_result:
                    return self.write({"code": 410, "msg": "请先进行优化计算！", "data": []})
                power_result = eval(str(power_result))
                tida = eval(str(tida))
                tida = timeUtils.dateToDataList(tida[0], tida[1])
                capacity = int(capacity)
                obj = []
                if id:
                    id = int(id)
                else:
                    return self.write({"code": 401, "msg": "参数错误，项目ID不允许为空", "data": []})
                type_ = self.get_argument('type', '1')  # 1：客户提供负荷；2：踏勘采集负荷

                session = self.getOrNewSession()
                user_id = session.user['id']
                page = user_session.query(ForecaseUseEleEleLoadOffer.file_path,
                                          ForecaseUseEleEleLoadOffer.capacity_config,
                                          ForecaseUseEleEleLoadOffer.id).filter(ForecaseUseEleEleLoadOffer.is_use == 1,
                                                                                ForecaseUseEleEleLoadOffer.id == id).first()
                df = pd.read_excel(page[0], sheet_name="Input")  # 获取负荷文件
                for ti in tida:  # 循环查询时间范围内的天数：ti: 天 yyyy-mm-dd
                    ee = {}
                    ee[ti] = {}

                    power = []
                    power_curve = []
                    data = df.loc[df['日期'] == ti, ["负荷", "容量/需量", "变压器容量"]]
                    row_indices = df[df['日期'] == ti].index
                    load = df.loc[df['日期'] == ti, ["负荷"]]  # 负荷
                    load = load.values.tolist()
                    load_value = [round(num[0], 2) for num in load]
                    load = np.array(load)

                    data = data.values.tolist()
                    data = [[round(x, 2) for x in sublist] for sublist in data]
                    _i = int(row_indices[0] / 24)
                    ee[ti]['data'] = data
                    for power_out_all in power_result:  # power_out_all：循环对应设备台数
                        power.append(power_out_all[_i])
                        power_curve.append([y - x for x, y in zip(power_out_all[_i], load_value)])

                    ee[ti]['power'] = power
                    ee[ti]['power_curve'] = power_curve

                    obj.append(ee)


                return self.returnTypeSuc(obj)







                # if not page:
                #     return self.customError("无效id")
                #
                # optimum_eoposal = OptimumProposal()
                # res = {}
                # if not page:
                #     return self.returnTypeSuc(res)
                # try:
                #     power_out_all = []
                #     if page:
                #         equipment_sum = 1
                #         if page[1]:
                #             capacity_config = json.loads(page[1])
                #             equipment_sum = capacity_config.get('equipment_sum')
                #         # 优化计算
                #         data = optimum_eoposal.optimum_proposal_calculate_new(file_path=page[0],
                #                                                               equipment_sum=equipment_sum,
                #                                                               capacity=capacity, id=id, user_id=user_id,
                #                                                               type_=type_)
                #         power_out_all_1 = data['power_out_all']
                #         df = pd.read_excel(page[0], sheet_name="Input")
                #         for ti in tida:
                #             ee = {}
                #             ee[ti] = []
                #             if capacity != None:
                #                 dd = df.loc[df['日期'] == ti, ["负荷", "容量/需量", "变压器容量"]]
                #                 row_indices = df[df['日期'] == ti].index
                #                 load = df.loc[df['日期'] == ti, ["负荷"]]  # 负荷
                #                 load = load.values.tolist()
                #                 load_value = [round(num[0], 2) for num in load]
                #                 load = np.array(load)
                #                 dd = dd.values.tolist()
                #                 dd = [[round(x, 2) for x in sublist] for sublist in dd]
                #                 for p in power_out_all_1:
                #                     inx_2 = power_out_all_1.index(p)
                #                     for ii in row_indices:
                #                         if inx_2 == (ii - 1):
                #                             power_out_all.append(p)
                #                 power_out = [round(power_out_all[0], 2) for power_out_all in load]  # 储能充放功率模拟
                #                 result = (
                #                     np.around([x + y for x, y in zip(load_value, power_out)], 2)).tolist()  # 负荷叠加曲线
                #                 for i in range(24):
                #                     dd[i].append(power_out[i])
                #                     dd[i].append(result[i])
                #                 ee[ti] = dd
                #             obj.append(ee)
                # except Exception as e:
                #     logging.error(e)
                # return self.returnTypeSuc(obj)

            elif kt == 'AddReferenceStandard':
                """定容参考标准新增"""
                name = self.get_argument('name')  # 省份名称
                count = self.get_argument('count')  # 日均循环次数
                if not name or not count:
                    return self.customError('参数不完整！请检查参数')
                session = self.getOrNewSession()
                user_role_id = session.user['user_role_id']
                if int(user_role_id) != 1:  # 超级管理员
                    return self.customError('非超级管理员用户不可操作！')
                if user_session.query(ReferenceStandard).filter(ReferenceStandard.name == name).first():
                    return self.customError('省份信息已存在，禁止重复添加！')

                res = ReferenceStandard(name=name, count=float(count))
                user_session.add(res)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'EditReferenceStandard':
                """定容参考标准编辑"""
                _id = self.get_argument('id')
                name = self.get_argument('name')
                count = self.get_argument('count')
                if not all([_id, name, count]):
                    return self.customError('参数错误！')
                session = self.getOrNewSession()
                user_role_id = session.user['user_role_id']
                if int(user_role_id) != 1:  # 超级管理员
                    return self.customError('非超级管理员用户不可操作！')
                user_session.query(ReferenceStandard).filter(ReferenceStandard.id == _id).update({'name': name, 'count':count})
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'SaveArgument':
                """经评计算-历史数据保存"""
                body = eval(self.request.body.decode('utf-8'))
                _id = body.get('id', None)   # 数据ID（覆盖的话需要）
                is_verify = body.get('is_verify')   # 1：确认覆盖
                project_id = body.get('project_id')   # 项目ID
                data = body.get('data')  # 经评计算参数（一个json）
                if not all([data, project_id]):
                    return self.customError('参数错误！')

                session = self.getOrNewSession()
                user_id = session.user['id']
                count = user_session.query(func.count(ArgumentCalcuate.id)).filter(ArgumentCalcuate.project_id == int(project_id)).scalar()
                if not _id:
                    if count >= 5:
                        return self.customError('参数保存个数已达上限，请选择覆盖操作!')
                    else:
                        info = ArgumentCalcuate(project_id=int(project_id), data=json.dumps(data), user_id=user_id)
                        user_session.add(info)
                        user_session.commit()
                else:
                    if is_verify == '1':
                        user_session.query(ArgumentCalcuate).filter(ArgumentCalcuate.id == int(_id)).update({'user_id': user_id, 'data': json.dumps(data), 'create_time':datetime.now()})
                        user_session.commit()
                    else:
                        return self.customError('已经有历史参数，是否覆盖保存记录！')
                return self.returnTypeSuc('')

            elif kt == 'GetArgumentUpload':
                """经评计算-历史数据参数导出"""
                _id = self.get_argument('id')
                if not _id:
                    return self.customError('参数错误！')
                res = user_session.query(ArgumentCalcuate).filter(ArgumentCalcuate.id == int(_id)).first()
                data = json.loads(res.data)
                # 导出excel
                wb = Workbook()
                st = wb.active
                st.cell(row=1, column=1).value = '参数名称'
                st.cell(row=1, column=2).value = '值'
                for k, v in data.get('input_json').items():
                    max_row = st.max_row + 1
                    st.cell(row=max_row, column=1).value = k
                    st.cell(row=max_row, column=2).value = str(v)
                file_name = f'{res.project.owner_short}-经评测算参数.xlsx'
                BASE_DIR = Path(__file__).resolve().parent
                path = os.path.join(BASE_DIR, file_name)
                wb.save(path)
                minioClient = MinioTool()
                file_path = minioClient.upload_local_file(file_name, path, 'side')
                d = {
                    "file_name": file_name,
                    "file_path": file_path
                }
                return self.returnTypeSuc(d)

            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    def logs_(self,id,pro_id,filename=None):
        '''保存操作日志'''
        session = self.getOrNewSession()
        user_id = session.user['id']
        ntime = timeUtils.getNewTimeStr()
        F = ForecaseHandleLogs(user_id=user_id, create_time=ntime, dic_log_id=id, project_id=pro_id,is_use='1',filename=filename)
        pag = user_session.query(ForecaseDicLog).filter(ForecaseDicLog.id == F.dic_log_id).first()
        F.descr=pag.name
        user_session.add(F)
        return ntime

    def _getfile(self,pid,list_,list_name,n):
        '''根据项目id和类型id获取数据
        pid:项目id
        list_：文件分类集合
        list_name：文件分类名称集合
        n:文件分类个数
        '''
        da = []
        for i in range(n):
            d = []
            bean = user_session.query(ForecaseFiles).filter(ForecaseFiles.project_id==pid,ForecaseFiles.base_file_id==list_[i],ForecaseFiles.is_use==1).all()
            for pag in bean:
                e = eval(str(pag))
                e['remarks'] = pag.remarks if pag.remarks else ''
                d.append(e)
            da.append({list_name[i]:d})
        return da

  
    def _getexceldata(self,path,s_row,e_row,cols,sheet_name=None):
        '''
        通过上传的excel获取负荷曲线
        s_row：跳过多少行
        e_row：往后读取多少行
        cols：读取的列
        '''
        try:
            if sheet_name:
                df = pd.read_excel(path,skiprows=s_row,nrows=e_row,usecols=cols,sheet_name=sheet_name)  # 跳过前29行，往后读取24行,读取二到13列
            else:
                df = pd.read_excel(path,skiprows=s_row,nrows=e_row,usecols=cols)
            df = df.round(2).fillna('--')  # Nan值填充
            values_arr = df.values  # 二维矩阵
        except:
            return self.customError('校验失败！请检查参数')
        return values_arr.tolist()
