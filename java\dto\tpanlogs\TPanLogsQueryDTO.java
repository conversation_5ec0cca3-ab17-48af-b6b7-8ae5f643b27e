package com.robestec.analysis.dto.tpanlogs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 下发日志记录查询DTO
 */
@Data
@ApiModel("下发日志记录查询DTO")
public class TPanLogsQueryDTO {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("状态: 1-成功, 2-失败")
    private Integer status;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
