package com.robestec.analysis.dto.tplanpowerrecords;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 功率计划关联记录查询DTO
 */
@Data
@ApiModel("功率计划关联记录查询DTO")
public class TPlanPowerRecordsQueryDTO {

    @ApiModelProperty("计划历史记录ID")
    private Long planId;

    @ApiModelProperty("功率下发记录ID")
    private Long powerId;

    @ApiModelProperty("功率计划序号")
    private Integer serialNumber;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
