#!/usr/bin/env python
# coding=utf-8
#@Information:实时值保存至redis中
#<AUTHOR> WYJ
#@Date         : 2022-08-09 11:00:57
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\realdata_save_redis.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-08-09 11:01:29


'''
获取scada中实时值保存至redis中
'''
import sys
import redis
import dm2016
import time,json
eventMonitor = dm2016.EventMonitor()
measureMonitor = dm2016.MeasureMonitor()
cumulantMonitor = dm2016.CumulantMonitor()
# real_pool = redis.ConnectionPool(host='***********',port=6379,db=1,username='',password='A45lEdj&f335@3s5h*8g',decode_responses=False, encoding='UTF-8')
real_pool = redis.ConnectionPool(host='*************',port=6379,db=1,username='',password='rhyc@redis1',decode_responses=False, encoding='UTF-8')
r_real = redis.Redis(connection_pool=real_pool)
reload(sys)
sys.setdefaultencoding("utf-8")
status_obj,measure_obj,discrete_obj,cumulant_obj = {},{},{},{}

s_c = dm2016.StatusMgr()
m_c = dm2016.MeasureMgr()
d_c = dm2016.DiscreteMgr()
c_c = dm2016.CumulantMgr()
def getSysInfos():
    '''查询所有监控内容'''
    # app_log.info('start %s'%(time.time()))
    obj = {'value':0.00,'desc':'','unit':'','valueDesc':'','index':-1,'time':time.ctime()}
    
    s_obj = {}  # 状态量数据
    d_obj = {}  # 离散量数据
    m_obj = {}  # 测量量数据
    c_obj = {}  # 累积量数据
    for s in range(s_c.size()):  # 所有状态量
        status = dm2016.Status(s)
        v = status.rt()['value']
        if VFlag:
            obj['value'] = v
            obj['desc'] = status.desc()
            obj['index'] = s
            obj['valueDesc'] = status.valueDesc(v)
            try:
                # r_real.hset('status',status.name(),json.dumps(obj))
                s_obj[status.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                s_obj[status.name()] = json.dumps(str(obj).decode('utf-8'))
                # r_real.hset('status',status.name(),json.dumps(str(obj).decode('utf-8')))
            status_obj[s] = v
        elif status_obj[s] != v:
            obj['value'] = v
            obj['desc'] = status.desc()
            obj['index'] = s
            obj['valueDesc'] = status.valueDesc(v)
            try:
                s_obj[status.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                s_obj[status.name()] = json.dumps(str(obj).decode('utf-8'))
            status_obj[s] = v
    # print ('status-count :',len(s_obj.keys()),time.time())  # 本次更新个数     
    if len(s_obj.keys())>0:  
        r_real.hmset('status',s_obj)
    # print ('redis-status-count:',r_real.hlen('status'))
    for d in range(d_c.size()):  # 所有离散量
        discrete = dm2016.Discrete(d)
        v = discrete.rt()['value']
        if VFlag:
            obj['value'] = v
            obj['desc'] = discrete.desc()
            obj['index'] = d
            obj['valueDesc'] = discrete.valueDesc(v)
            
            try:
                d_obj[discrete.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                d_obj[discrete.name()] = json.dumps(str(obj).decode('utf-8'))
            discrete_obj[d] = v
        elif discrete_obj[d] != v:
            obj['value'] = v
            obj['desc'] = discrete.desc()
            obj['index'] = d
            obj['valueDesc'] = discrete.valueDesc(v)
            try:
                d_obj[discrete.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                d_obj[discrete.name()] = json.dumps(str(obj).decode('utf-8'))
            discrete_obj[d] = v
    # print ('discrete-count :',len(d_obj.keys()),time.time())  # 本次更新个数  
    if len(d_obj.keys())>0:     
        r_real.hmset('discrete',d_obj)
    # print ('redis-discrete-count:',r_real.hlen('discrete'))
    for m in range(m_c.size()):  # 所有测量量
        measure = dm2016.Measure(m)
        v = measure.rt()['value']
        if VFlag:
            obj['value'] = v
            obj['desc'] = measure.desc()
            obj['index'] = m
            obj['unit'] = measure.unit()
            try:
                m_obj[measure.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                m_obj[measure.name()] = json.dumps(str(obj).decode('utf-8'))
            measure_obj[m] = measure.rt()['value']
        elif measure_obj[m] !=  v:
            obj['value'] = v
            obj['desc'] = measure.desc()
            obj['index'] = m
            obj['unit'] = measure.unit()
            try:
                m_obj[measure.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                m_obj[measure.name()] = json.dumps(str(obj).decode('utf-8'))
            measure_obj[m] = measure.rt()['value']
    # print ('z总数 :',len(m_obj.keys()),time.time())  # 本次更新个数
    if len(m_obj.keys())>0:
        r_real.hmset('measure',m_obj)
    # print ('redis-measure-count:',r_real.hlen('measure'))  # 获取指定的key下所有元素
    for c in range(c_c.size()):  # 所有累计量
        cumulant = dm2016.Cumulant(c)
        v = cumulant.rt()['value']
        if VFlag:
            obj['value'] = v
            obj['desc'] = cumulant.desc()
            obj['index'] = c
            c_obj[cumulant.name()] = json.dumps(obj)
            try:
                c_obj[cumulant.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                c_obj[cumulant.name()] = json.dumps(str(obj).decode('utf-8'))
            cumulant_obj[c] = cumulant.rt()['value']
        elif cumulant_obj[c] != v:
            obj['value'] = v
            obj['desc'] = cumulant.desc()
            obj['index'] = c
            c_obj[cumulant.name()] = json.dumps(obj)
            try:
                c_obj[cumulant.name()] = json.dumps(obj)
            except UnicodeDecodeError as E:
                c_obj[cumulant.name()] = json.dumps(str(obj).decode('utf-8'))
            cumulant_obj[c] = cumulant.rt()['value']
    # print ('cumulant-count :',len(c_obj.keys()),time.time())  # 本次更新个数  
    if len(c_obj.keys())>0:
        r_real.hmset('cumulant',c_obj)
    # print ('redis-cumulant-count:',r_real.hlen('cumulant'))
        
    # print ('333333333333',time.time())
# def calculation():
#     '''
#     只计算需要刷新的值
#     '''
#     try:
#         while 1:
#             obj = {'value':0.00,'desc':'','unit':'','valueDesc':'','index':-1,'time':''}
#             event = eventMonitor.tryGet()
#             # app_log.info('timing runing is %s'%(time.time()))
#             while event:  # 所有事件
#                 index= event['index']
#                 value = event['value']
#                 obj['index'] = index
#                 obj['value'] = value
#                 obj['time'] = event['time']
#                 type_ = event['type']
#                 if type_ == 2:  # 状态量
#                     status = dm2016.Status(index)
#                     if status_obj[index] != value:  # 值不相等
#                         obj['desc'] = status.desc()
#                         obj['valueDesc'] = status.valueDesc(value)
#                         r_real.hset('status',status.name(),str(obj))
#                         status_obj[index] = value  # 重新赋值
#                 elif type_ == 3:  #离散量
#                     discrete = dm2016.Discrete(index)
#                     if discrete_obj[index] != value:  # 两次离散量不同
#                         obj['desc'] = discrete.desc()
#                         obj['valueDesc'] = discrete.valueDesc(value)
#                         r_real.hset('discrete',discrete.name(),str(obj))
#                         discrete_obj[index] = value  # 重新赋值
#                 # elif type_ == 5:  # 测量量
#                 #     measure = dm2016.Measure(index)
#                 #     if measure_obj[index] != value:  # 两次测量值不相等
#                 #         obj['desc'] = measure.desc()
#                 #         obj['unit'] = measure.unit()
#                 #         r_real.hset('measure',measure.name(),str(obj))
#                 #         measure_obj[index] = value  # 重新赋值
#                 # elif type_ == 6:  # 累计量
#                 #     cumulant = dm2016.Cumulant(index)
#                 #     if cumulant_obj[index] != value:
#                 #         obj['desc'] = cumulant.desc()
#                 #         r_real.hset('cumulant',cumulant.name(),str(obj))
#                 #         cumulant_obj[index] = value  #
#                 # app_log.info('event:%s'%event)
#                 event = eventMonitor.tryGet()
#             # 测量量的变化
#             measureEvent = measureMonitor.tryGet()
#             while measureEvent:  # 获取测量量的变化值
#                 index = measureEvent['index']
#                 value = round(measureEvent['value'],3)
#                 obj['index'] = index
#                 obj['value'] = value
#                 obj['time'] = measureEvent['time']
#                 measure = dm2016.Measure(index)
#                 if measure_obj[index] != value:  # 两次测量值不相等
#                     obj['desc'] = measure.desc()
#                     obj['unit'] = measure.unit()
#                     r_real.hset('measure',measure.name(),str(obj))
#                     measure_obj[index] = value  # 重新赋值
#                 measureEvent = measureMonitor.tryGet()
#             #  累积量的变化
#             cumulantEvent = cumulantMonitor.tryGet()
#             while cumulantEvent:  # 获取累计量的变化值
#                 index = cumulantEvent['index']
#                 value = round(cumulantEvent['value'],3)
#                 obj['index'] = index
#                 obj['value'] = value
#                 obj['time'] = cumulantEvent['time']
#                 cumulant = dm2016.Cumulant(index)
#                 if cumulant_obj[index] != value:  # 两次测量值不相等
#                     obj['desc'] = cumulant.desc()
#                     r_real.hset('cumulant',cumulant.name(),str(obj))
#                     cumulant_obj[index] = value  # 重新赋值
#                 cumulantEvent = cumulantMonitor.tryGet()
#             time.sleep(6)

#     except Exception as E:
#         # app_log.error(E)
#         print (E)
  

if __name__ == '__main__':
    VFlag = True  # 第一次加载全量写入
    while 1:
        getSysInfos()
        time.sleep(7)
        VFlag = False
    # calculation()
    # print (r_real.hmset('test',{'k1':'v1', 'k2': 'v2'}))
   
    



