#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ报表冻结
# @Date         : 2022-08-04 08:37:09
# @FilePath     : \RHBESS_Service\Tools\TimeTask\froze_report.py
# @Email        : <EMAIL>
# @LastEditTime : 2023-04-26 14:24:21

from sqlalchemy import func
from Tools.Cfg.DB_his import ygqn7,ygqn8,ygqn8_bms,ygqn7_bms_a,ygqn7_bms_b1,ygqn7_bms_b2,ygqn7_bms_c
import sys, os, getopt
from Tools.DB.guizhou_his import guizhou1_engine, guizhou2_engine, guizhou3_engine, guizhou4_engine, guizhou5_engine, \
    guizhou7_engine, guizhou6_engine, guizhou8_engine, guizhou1_session, guizhou2_session, guizhou3_session, \
    guizhou4_session, guizhou5_session, guizhou6_session, guizhou7_session, guizhou8_session, GUIZHOU1_DATABASE, \
    GUIZHOU2_DATABASE, GUIZHOU3_DATABASE, GUIZHOU4_DATABASE, GUIZHOU5_DATABASE, GUIZHOU6_DATABASE, GUIZHOU7_DATABASE, \
    GUIZHOU8_DATABASE
from Tools.DB.houma_his import houmaa1_engine, HOUMAA1_DATABASE, houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, \
    houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houmab2_engine, houmab2_session, HOUMAB2_DATABASE, \
    houmaa1_session
from Tools.DB.ygqn_his import ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn8_session, YGQN8_DATABASE, ygqn8_engine
from Tools.DB.zgtian_his import zgtian1_engine, zgtian1_session,ZGTIAN1_DATABASE, zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, \
    zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE,mzgtian_engine,mzgtian_session,MZGTIAN_DATABASE
from Tools.Utils.num_utils import *
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session
from Tools.DB.halun_his import halun_session, halun_engine, HALUN_DATABASE
from Tools.DB.taicang_his import taicang_session, taicang_engine, TAICANG_DATABASE
from Tools.DB.binhai1_his import binhai1_session, binhai1_engine, BINHAI1_DATABASE
from Tools.DB.binhai2_his import binhai2_session, binhai2_engine, BINHAI2_DATABASE
from Tools.DB.ygzhen_his import ygzhen1_engine, ygzhen2_engine, ygzhen1_session, ygzhen2_session, YGZHEN1_DATABASE, \
    YGZHEN2_DATABASE
from Tools.DB.datong_his import datong1_engine, datong1_session, datong2_engine, datong2_session, datong3_engine, \
    datong3_session, datong4_engine, datong4_session, DATONG1_DATABASE, DATONG2_DATABASE, DATONG3_DATABASE, \
    DATONG4_DATABASE
from Tools.DB.baodian_his import baodian1_engine, baodian1_session, BAODIAN1_DATABASE, baodian2_engine, \
    baodian2_session, BAODIAN2_DATABASE, \
    baodian3_engine, baodian3_session, BAODIAN3_DATABASE, baodian4_engine, baodian4_session, BAODIAN4_DATABASE, \
    baodian5_engine, baodian5_session, BAODIAN5_DATABASE
from Application.Models.User.report_f import FReport
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.report import Report
from apscheduler.schedulers.blocking import BlockingScheduler
import logging
import pymysql
from dbutils.persistent_db import PersistentDB
logging.basicConfig()

pool_ = PersistentDB(pymysql, 10, **{
    "host": '*********',  # 数据库主机地址
    "user": 'root',  # 数据库用户名
    "password": 'rhyc@doris1',  # 数据库密码
    "database": 'dwd_rhyc_ES_station',  # 数据库名称
    "port": 9030,
    "cursorclass": pymysql.cursors.DictCursor
})

pool_zt = PersistentDB(pymysql, 10, **{
    "host": '*********',  # 数据库主机地址
    "user": 'root',  # 数据库用户名
    "password": 'rhyc@doris1',  # 数据库密码
    "database": 'ods_tfStzgtian1meter',  # 数据库名称
    "port": 9030,
    "cursorclass": pymysql.cursors.DictCursor
})

pool_yz = PersistentDB(pymysql, 10, **{
    "host": '*********',  # 数据库主机地址
    "user": 'root',  # 数据库用户名
    "password": 'rhyc@doris1',  # 数据库密码
    "database": 'ods_tfStygzhen1',  # 数据库名称
    "port": 9030,
    "cursorclass": pymysql.cursors.DictCursor
})
error_log = logging.getLogger("error")

#用到的表t_report，f_report

# 所有需要冻结的变量
disg = 'KWH_DisgCapyTotl'  # 放电
chag = 'KWH_ChagCapyTotl'  # 补电
soc = 'Sys_SOC'  # SOC
# 哈伦
halun = ["tpSthalun.PcsSt1.Lp1.", "tpSthalun.PcsSt1.Lp2.", "tpSthalun.PcsSt2.Lp1.", "tpSthalun.PcsSt2.Lp2."]
# 太仓
taicang = ["tpSttaicang.PcsSt1.Lp1.", "tpSttaicang.PcsSt1.Lp2.", "tpSttaicang.PcsSt2.Lp1.", "tpSttaicang.PcsSt2.Lp2.",
           "tpSttaicang.PcsSt3.Lp1.", "tpSttaicang.PcsSt3.Lp2.", "tpSttaicang.PcsSt4.Lp1.", "tpSttaicang.PcsSt4.Lp2."]
# 滨海1
binhai1 = ["tpStbinhai1.PcsSt1.Lp1.", "tpStbinhai1.PcsSt1.Lp2.", "tpStbinhai1.PcsSt2.Lp1.", "tpStbinhai1.PcsSt2.Lp2.",
           "tpStbinhai1.PcsSt3.Lp1.", "tpStbinhai1.PcsSt3.Lp2."]
# 滨海2
binhai2 = ["tpStbinhai2.PcsSt4.Lp1.", "tpStbinhai2.PcsSt4.Lp2.", "tpStbinhai2.PcsSt5.Lp1.", "tpStbinhai2.PcsSt5.Lp2.",
           "tpStbinhai2.PcsSt6.Lp1.", "tpStbinhai2.PcsSt6.Lp2."]
# pcs永臻1
ygzhen1 = ["tfStygzhen1.EMS.PCS1.Lp1.", "tfStygzhen1.EMS.PCS1.Lp2.", "tfStygzhen1.EMS.PCS1.Lp3.",
           "tfStygzhen1.EMS.PCS1.Lp4."]
# pcs永臻2
ygzhen2 = ["tfStygzhen2.EMS.PCS2.Lp1.", "tfStygzhen2.EMS.PCS2.Lp2.", "tfStygzhen2.EMS.PCS2.Lp3.",
           "tfStygzhen2.EMS.PCS2.Lp4."]
# bms永臻1
ygzhen1_bms = ["tfStygzhen1.EMS.BMS1.Ay1.", "tfStygzhen1.EMS.BMS1.Ay2.", "tfStygzhen1.EMS.BMS1.Ay3.",
               "tfStygzhen1.EMS.BMS1.Ay4."]
# bms永臻2
ygzhen2_bms = ["tfStygzhen2.EMS.BMS2.Ay1.", "tfStygzhen2.EMS.BMS2.Ay2.", "tfStygzhen2.EMS.BMS2.Ay3.",
               "tfStygzhen2.EMS.BMS2.Ay4."]
# pcs中天1
zgtian1 = ["tfStzgtian1.EMS.PCS1.Lp1.", "tfStzgtian1.EMS.PCS1.Lp2.", "tfStzgtian1.EMS.PCS1.Lp3.",
           "tfStzgtian1.EMS.PCS1.Lp4."]
# pcs中天2
zgtian2 = ["tfStzgtian2.EMS.PCS2.Lp1.", "tfStzgtian2.EMS.PCS2.Lp2.", "tfStzgtian2.EMS.PCS2.Lp3.",
           "tfStzgtian2.EMS.PCS2.Lp4."]
# pcs中天3
zgtian3 = ["tfStzgtian3.EMS.PCS3.Lp1.", "tfStzgtian3.EMS.PCS3.Lp2.", "tfStzgtian3.EMS.PCS3.Lp3.",
           "tfStzgtian3.EMS.PCS3.Lp4."]
# pcs中天4
zgtian4 = ["tfStzgtian4.EMS.PCS4.Lp1.", "tfStzgtian4.EMS.PCS4.Lp2.", "tfStzgtian4.EMS.PCS4.Lp3.",
           "tfStzgtian4.EMS.PCS4.Lp4."]
# bms中天1
zgtian1_bms = ["tfStzgtian1.EMS.BMS1.Ay1.", "tfStzgtian1.EMS.BMS1.Ay2.", "tfStzgtian1.EMS.BMS1.Ay3.",
               "tfStzgtian1.EMS.BMS3.Ay4."]
# bms中天2
zgtian2_bms = ["tfStzgtian2.EMS.BMS2.Ay1.", "tfStzgtian2.EMS.BMS2.Ay2.", "tfStzgtian2.EMS.BMS2.Ay3.",
               "tfStygzhen2.EMS.BMS2.Ay4."]
# bms中天3
zgtian3_bms = ["tfStzgtian3.EMS.BMS3.Ay1.", "tfStzgtian3.EMS.BMS3.Ay2.", "tfStzgtian3.EMS.BMS3.Ay3.",
               "tfStzgtian3.EMS.BMS3.Ay4."]
# bms中天4
zgtian4_bms = ["tfStzgtian4.EMS.BMS4.Ay1.", "tfStzgtian4.EMS.BMS4.Ay2.", "tfStzgtian4.EMS.BMS4.Ay3.",
               "tfStzgtian4.EMS.BMS4.Ay4."]
# 保电1pcs  tfStbodian1.Pcs.ChagCapyTotl 充，tfStbodian1.Pcs.DisgCapyTotl 放
baodian1_pcs = ["tfStbodian1.Pc"]
# 保电2pcs
baodian2_pcs = ["tfStbodian2.Pc"]
# 保电3pcs
baodian3_pcs = ["tfStbodian3.Pc"]
# 保电4pcs
baodian4_pcs = ["tfStbodian4.Pc"]
# 保电5pcs
baodian5_pcs = ["tfStbodian5.Pc"]

# pcs侯马
houma1 = ["tfSthoumaA1.EMS.A502.PCS1.Lp1.","tfSthoumaA1.EMS.A502.PCS1.Lp2.","tfSthoumaA1.EMS.A502.PCS2.Lp1.","tfSthoumaA1.EMS.A502.PCS2.Lp2.","tfSthoumaA1.EMS.A502.PCS3.Lp1.","tfSthoumaA1.EMS.A502.PCS3.Lp2.","tfSthoumaA1.EMS.A502.PCS4.Lp1.","tfSthoumaA1.EMS.A502.PCS4.Lp2.","tfSthoumaA1.EMS.A502.PCS5.Lp1.","tfSthoumaA1.EMS.A502.PCS5.Lp2.","tfSthoumaA1.EMS.A502.PCS6.Lp1.","tfSthoumaA1.EMS.A502.PCS6.Lp2.","tfSthoumaA1.EMS.A503.PCS7.Lp1.","tfSthoumaA1.EMS.A503.PCS7.Lp2."]
houma2 =["tfSthoumaA2.EMS.A503.PCS8.Lp1.","tfSthoumaA2.EMS.A503.PCS8.Lp2.","tfSthoumaA2.EMS.A503.PCS9.Lp1.","tfSthoumaA2.EMS.A503.PCS9.Lp2.","tfSthoumaA2.EMS.A503.PCS10.Lp1.","tfSthoumaA2.EMS.A503.PCS10.Lp2.","tfSthoumaA2.EMS.A503.PCS11.Lp1.","tfSthoumaA2.EMS.A503.PCS11.Lp2.","tfSthoumaA2.EMS.A503.PCS12.Lp1.","tfSthoumaA2.EMS.A503.PCS12.Lp2.","tfSthoumaA2.EMS.A504.PCS13.Lp1.","tfSthoumaA2.EMS.A504.PCS13.Lp2.","tfSthoumaA2.EMS.A504.PCS14.Lp1.","tfSthoumaA2.EMS.A504.PCS14.Lp2."]
houma3 =["tfSthoumaB1.EMS.B504.PCS1.Lp1.","tfSthoumaB1.EMS.B504.PCS1.Lp2.","tfSthoumaB1.EMS.B504.PCS2.Lp1.","tfSthoumaB1.EMS.B504.PCS2.Lp2.","tfSthoumaB1.EMS.B504.PCS3.Lp1.","tfSthoumaB1.EMS.B504.PCS3.Lp2.","tfSthoumaB1.EMS.B504.PCS4.Lp1.","tfSthoumaB1.EMS.B504.PCS4.Lp2.","tfSthoumaB1.EMS.B505.PCS5.Lp1.","tfSthoumaB1.EMS.B505.PCS5.Lp2.","tfSthoumaB1.EMS.B505.PCS6.Lp1.","tfSthoumaB1.EMS.B505.PCS6.Lp2.","tfSthoumaB1.EMS.B505.PCS7.Lp1.","tfSthoumaB1.EMS.B505.PCS7.Lp2.","tfSthoumaB1.EMS.B505.PCS8.Lp1.","tfSthoumaB1.EMS.B505.PCS8.Lp2."]
houma4 =["tfSthoumaB2.EMS.B505.PCS9.Lp1.","tfSthoumaB2.EMS.B505.PCS9.Lp2.","tfSthoumaB2.EMS.B505.PCS10.Lp1.","tfSthoumaB2.EMS.B505.PCS10.Lp2.","tfSthoumaB2.EMS.B506.PCS11.Lp1.","tfSthoumaB2.EMS.B506.PCS11.Lp2.","tfSthoumaB2.EMS.B506.PCS12.Lp1.","tfSthoumaB2.EMS.B506.PCS12.Lp2.","tfSthoumaB2.EMS.B506.PCS13.Lp1.","tfSthoumaB2.EMS.B506.PCS13.Lp2.","tfSthoumaB2.EMS.B506.PCS14.Lp1.","tfSthoumaB2.EMS.B506.PCS14.Lp2.","tfSthoumaB2.EMS.B506.PCS15.Lp1.","tfSthoumaB2.EMS.B506.PCS15.Lp2.","tfSthoumaB2.EMS.B506.PCS16.Lp1.","tfSthoumaB2.EMS.B506.PCS16.Lp2."]

# bms侯马
houma1_bms = ["tfSthoumaA1.EMS.A502.BMS1.Ay1.","tfSthoumaA1.EMS.A502.BMS1.Ay2.","tfSthoumaA1.EMS.A502.BMS2.Ay1.","tfSthoumaA1.EMS.A502.BMS2.Ay2.","tfSthoumaA1.EMS.A502.BMS3.Ay1.","tfSthoumaA1.EMS.A502.BMS3.Ay2.","tfSthoumaA1.EMS.A502.BMS4.Ay1.","tfSthoumaA1.EMS.A502.BMS4.Ay2.","tfSthoumaA1.EMS.A502.BMS5.Ay1.","tfSthoumaA1.EMS.A502.BMS5.Ay2.","tfSthoumaA1.EMS.A502.BMS6.Ay1.","tfSthoumaA1.EMS.A502.BMS6.Ay2.","tfSthoumaA1.EMS.A503.BMS7.Ay1.","tfSthoumaA1.EMS.A503.BMS7.Ay2."]
houma2_bms =["tfSthoumaA2.EMS.A503.BMS8.Ay1.","tfSthoumaA2.EMS.A503.BMS8.Ay2.","tfSthoumaA2.EMS.A503.BMS9.Ay1.","tfSthoumaA2.EMS.A503.BMS9.Ay2.","tfSthoumaA2.EMS.A503.BMS10.Ay1.","tfSthoumaA2.EMS.A503.BMS10.Ay2.","tfSthoumaA2.EMS.A503.BMS11.Ay1.","tfSthoumaA2.EMS.A503.BMS11.Ay2.","tfSthoumaA2.EMS.A503.BMS12.Ay1.","tfSthoumaA2.EMS.A503.BMS12.Ay2.","tfSthoumaA2.EMS.A504.BMS13.Ay1.","tfSthoumaA2.EMS.A504.BMS13.Ay2.","tfSthoumaA2.EMS.A504.BMS14.Ay1.","tfSthoumaA2.EMS.A504.BMS14.Ay2."]
houma3_bms =["tfSthoumaB1.EMS.B504.BMS1.Ay1.","tfSthoumaB1.EMS.B504.BMS1.Ay2.","tfSthoumaB1.EMS.B504.BMS2.Ay1.","tfSthoumaB1.EMS.B504.BMS2.Ay2.","tfSthoumaB1.EMS.B504.BMS3.Ay1.","tfSthoumaB1.EMS.B504.BMS3.Ay2.","tfSthoumaB1.EMS.B504.BMS4.Ay1.","tfSthoumaB1.EMS.B504.BMS4.Ay2.","tfSthoumaB1.EMS.B505.BMS5.Ay1.","tfSthoumaB1.EMS.B505.BMS5.Ay2.","tfSthoumaB1.EMS.B505.BMS6.Ay1.","tfSthoumaB1.EMS.B505.BMS6.Ay2.","tfSthoumaB1.EMS.B505.BMS7.Ay1.","tfSthoumaB1.EMS.B505.BMS7.Ay2.","tfSthoumaB1.EMS.B505.BMS8.Ay1.","tfSthoumaB1.EMS.B505.BMS8.Ay2."]
houma4_bms =["tfSthoumaB2.EMS.B505.BMS9.Ay1.","tfSthoumaB2.EMS.B505.BMS9.Ay2.","tfSthoumaB2.EMS.B505.BMS10.Ay1.","tfSthoumaB2.EMS.B505.BMS10.Ay2.","tfSthoumaB2.EMS.B506.BMS11.Ay1.","tfSthoumaB2.EMS.B506.BMS11.Ay2.","tfSthoumaB2.EMS.B506.BMS12.Ay1.","tfSthoumaB2.EMS.B506.BMS12.Ay2.","tfSthoumaB2.EMS.B506.BMS13.Ay1.","tfSthoumaB2.EMS.B506.BMS13.Ay2.","tfSthoumaB2.EMS.B506.BMS14.Ay1.","tfSthoumaB2.EMS.B506.BMS14.Ay2.","tfSthoumaB2.EMS.B506.BMS15.Ay1.","tfSthoumaB2.EMS.B506.BMS15.Ay2.","tfSthoumaB2.EMS.B506.BMS16.Ay1.","tfSthoumaB2.EMS.B506.BMS16.Ay2."]

# pcs大同
datong1 = ["tc_datong1.EMS.Energy1.PCS.Lp1.","tc_datong1.EMS.Energy1.PCS.Lp2.","tc_datong1.EMS.Energy2.PCS.Lp1.","tc_datong1.EMS.Energy2.PCS.Lp2."]
datong2 =["tc_datong2.EMS.Energy3.PCS.Lp1.","tc_datong2.EMS.Energy3.PCS.Lp2.","tc_datong2.EMS.Energy4.PCS.Lp1.","tc_datong2.EMS.Energy4.PCS.Lp2."]
datong3 =["tc_datong3.EMS.Energy5.PCS.Lp1.","tc_datong3.EMS.Energy5.PCS.Lp2.","tc_datong3.EMS.Energy6.PCS.Lp1.","tc_datong3.EMS.Energy6.PCS.Lp2."]
datong4 =["tc_datong4.EMS.Energy7.PCS.Lp1.","tc_datong4.EMS.Energy7.PCS.Lp2.","tc_datong4.EMS.Energy8.PCS.Lp1.","tc_datong4.EMS.Energy8.PCS.Lp2."]

# bms大同
datong1_bms= ["tc_datong1.EMS.Energy1.BMS.Ay1.","tc_datong1.EMS.Energy1.BMS.Ay2.","tc_datong1.EMS.Energy2.BMS.Ay1.","tc_datong1.EMS.Energy2.BMS.Ay2."]
datong2_bms=["tc_datong2.EMS.Energy3.BMS.Ay1.","tc_datong2.EMS.Energy3.BMS.Ay2.","tc_datong2.EMS.Energy4.BMS.Ay1.","tc_datong2.EMS.Energy4.BMS.Ay2."]
datong3_bms=["tc_datong3.EMS.Energy5.BMS.Ay1.","tc_datong3.EMS.Energy5.BMS.Ay2.","tc_datong3.EMS.Energy6.BMS.Ay1.","tc_datong3.EMS.Energy6.BMS.Ay2."]
datong4_bms=["tc_datong4.EMS.Energy7.BMS.Ay1.","tc_datong4.EMS.Energy7.BMS.Ay2.","tc_datong4.EMS.Energy8.BMS.Ay1.","tc_datong4.EMS.Energy8.BMS.Ay2."]

# pcs贵州
guizhou1 =["guizhou1a.EMS.Energy1.PCS.Lp1.","guizhou1a.EMS.Energy1.PCS.Lp2.","guizhou1a.EMS.Energy2.PCS.Lp1.","guizhou1a.EMS.Energy2.PCS.Lp2.","guizhou1a.EMS.Energy3.PCS.Lp1.","guizhou1a.EMS.Energy3.PCS.Lp2.","guizhou1a.EMS.Energy4.PCS.Lp1.","guizhou1a.EMS.Energy4.PCS.Lp2.","guizhou1b.EMS.Energy5.PCS.Lp1.","guizhou1b.EMS.Energy5.PCS.Lp2.","guizhou1b.EMS.Energy6.PCS.Lp1.","guizhou1b.EMS.Energy6.PCS.Lp2.","guizhou1b.EMS.Energy7.PCS.Lp1.","guizhou1b.EMS.Energy7.PCS.Lp2.","guizhou1b.EMS.Energy8.PCS.Lp1.","guizhou1b.EMS.Energy8.PCS.Lp2."]
guizhou2 =["guizhou2a.EMS.Energy10.PCS.Lp1.","guizhou2a.EMS.Energy10.PCS.Lp2.","guizhou2a.EMS.Energy11.PCS.Lp1.","guizhou2a.EMS.Energy11.PCS.Lp2.","guizhou2a.EMS.Energy12.PCS.Lp1.","guizhou2a.EMS.Energy12.PCS.Lp2.","guizhou2a.EMS.Energy9.PCS.Lp1.","guizhou2a.EMS.Energy9.PCS.Lp2.","guizhou2b.EMS.Energy13.PCS.Lp1.","guizhou2b.EMS.Energy13.PCS.Lp2.","guizhou2b.EMS.Energy14.PCS.Lp1.","guizhou2b.EMS.Energy14.PCS.Lp2.","guizhou2b.EMS.Energy15.PCS.Lp1.","guizhou2b.EMS.Energy15.PCS.Lp2.","guizhou2b.EMS.Energy16.PCS.Lp1.","guizhou2b.EMS.Energy16.PCS.Lp2."]
guizhou3 =["guizhou3a.EMS.Energy17.PCS.Lp1.","guizhou3a.EMS.Energy17.PCS.Lp2.","guizhou3a.EMS.Energy18.PCS.Lp1.","guizhou3a.EMS.Energy18.PCS.Lp2.","guizhou3a.EMS.Energy19.PCS.Lp1.","guizhou3a.EMS.Energy19.PCS.Lp2.","guizhou3a.EMS.Energy20.PCS.Lp1.","guizhou3a.EMS.Energy20.PCS.Lp2.","guizhou3b.EMS.Energy21.PCS.Lp1.","guizhou3b.EMS.Energy21.PCS.Lp2.","guizhou3b.EMS.Energy22.PCS.Lp1.","guizhou3b.EMS.Energy22.PCS.Lp2.","guizhou3b.EMS.Energy23.PCS.Lp1.","guizhou3b.EMS.Energy23.PCS.Lp2.","guizhou3b.EMS.Energy24.PCS.Lp1.","guizhou3b.EMS.Energy24.PCS.Lp2."]
guizhou4 =["guizhou4a.EMS.Energy25.PCS.Lp1.","guizhou4a.EMS.Energy25.PCS.Lp2.","guizhou4a.EMS.Energy26.PCS.Lp1.","guizhou4a.EMS.Energy26.PCS.Lp2.","guizhou4a.EMS.Energy27.PCS.Lp1.","guizhou4a.EMS.Energy27.PCS.Lp2.","guizhou4a.EMS.Energy28.PCS.Lp1.","guizhou4a.EMS.Energy28.PCS.Lp2.","guizhou4b.EMS.Energy29.PCS.Lp1.","guizhou4b.EMS.Energy29.PCS.Lp2.","guizhou4b.EMS.Energy30.PCS.Lp1.","guizhou4b.EMS.Energy30.PCS.Lp2.","guizhou4b.EMS.Energy31.PCS.Lp1.","guizhou4b.EMS.Energy31.PCS.Lp2.","guizhou4b.EMS.Energy32.PCS.Lp1.","guizhou4b.EMS.Energy32.PCS.Lp2."]
guizhou5 =["guizhou5a.EMS.Energy33.PCS.Lp1.","guizhou5a.EMS.Energy33.PCS.Lp2.","guizhou5a.EMS.Energy34.PCS.Lp1.","guizhou5a.EMS.Energy34.PCS.Lp2.","guizhou5a.EMS.Energy35.PCS.Lp1.","guizhou5a.EMS.Energy35.PCS.Lp2.","guizhou5a.EMS.Energy36.PCS.Lp1.","guizhou5a.EMS.Energy36.PCS.Lp2.","guizhou5b.EMS.Energy37.PCS.Lp1.","guizhou5b.EMS.Energy37.PCS.Lp2.","guizhou5b.EMS.Energy38.PCS.Lp1.","guizhou5b.EMS.Energy38.PCS.Lp2.","guizhou5b.EMS.Energy39.PCS.Lp1.","guizhou5b.EMS.Energy39.PCS.Lp2.","guizhou5b.EMS.Energy40.PCS.Lp1.","guizhou5b.EMS.Energy40.PCS.Lp2."]
guizhou6 =["guizhou6a.EMS.Energy41.PCS.Lp1.","guizhou6a.EMS.Energy41.PCS.Lp2.","guizhou6a.EMS.Energy42.PCS.Lp1.","guizhou6a.EMS.Energy42.PCS.Lp2.","guizhou6a.EMS.Energy43.PCS.Lp1.","guizhou6a.EMS.Energy43.PCS.Lp2.","guizhou6a.EMS.Energy44.PCS.Lp1.","guizhou6a.EMS.Energy44.PCS.Lp2.","guizhou6b.EMS.Energy45.PCS.Lp1.","guizhou6b.EMS.Energy45.PCS.Lp2.","guizhou6b.EMS.Energy46.PCS.Lp1.","guizhou6b.EMS.Energy46.PCS.Lp2.","guizhou6b.EMS.Energy47.PCS.Lp1.","guizhou6b.EMS.Energy47.PCS.Lp2.","guizhou6b.EMS.Energy48.PCS.Lp1.","guizhou6b.EMS.Energy48.PCS.Lp2."]
guizhou7 =["guizhou7a.EMS.Energy49.PCS.Lp1.","guizhou7a.EMS.Energy49.PCS.Lp2.","guizhou7a.EMS.Energy50.PCS.Lp1.","guizhou7a.EMS.Energy50.PCS.Lp2.","guizhou7a.EMS.Energy51.PCS.Lp1.","guizhou7a.EMS.Energy51.PCS.Lp2.","guizhou7b.EMS.Energy52.PCS.Lp1.","guizhou7b.EMS.Energy52.PCS.Lp2.","guizhou7b.EMS.Energy53.PCS.Lp1.","guizhou7b.EMS.Energy53.PCS.Lp2.","guizhou7b.EMS.Energy54.PCS.Lp1.","guizhou7b.EMS.Energy54.PCS.Lp2."]
guizhou8 =["guizhou8a.EMS.Energy55.PCS.Lp1.","guizhou8a.EMS.Energy55.PCS.Lp2.","guizhou8a.EMS.Energy56.PCS.Lp1.","guizhou8a.EMS.Energy56.PCS.Lp2.","guizhou8a.EMS.Energy57.PCS.Lp1.","guizhou8a.EMS.Energy57.PCS.Lp2.","guizhou8b.EMS.Energy58.PCS.Lp1.","guizhou8b.EMS.Energy58.PCS.Lp2.","guizhou8b.EMS.Energy59.PCS.Lp1.","guizhou8b.EMS.Energy59.PCS.Lp2.","guizhou8b.EMS.Energy60.PCS.Lp1.","guizhou8b.EMS.Energy60.PCS.Lp2."]
# bms贵州
guizhou1_bms =["guizhou1a.EMS.Energy1.BMS.Ay1.","guizhou1a.EMS.Energy1.BMS.Ay2.","guizhou1a.EMS.Energy2.BMS.Ay1.","guizhou1a.EMS.Energy2.BMS.Ay2.","guizhou1a.EMS.Energy3.BMS.Ay1.","guizhou1a.EMS.Energy3.BMS.Ay2.","guizhou1a.EMS.Energy4.BMS.Ay1.","guizhou1a.EMS.Energy4.BMS.Ay2.","guizhou1b.EMS.Energy5.BMS.Ay1.","guizhou1b.EMS.Energy5.BMS.Ay2.","guizhou1b.EMS.Energy6.BMS.Ay1.","guizhou1b.EMS.Energy6.BMS.Ay2.","guizhou1b.EMS.Energy7.BMS.Ay1.","guizhou1b.EMS.Energy7.BMS.Ay2.","guizhou1b.EMS.Energy8.BMS.Ay1.","guizhou1b.EMS.Energy8.BMS.Ay2."]
guizhou2_bms =["guizhou2a.EMS.Energy10.BMS.Ay1.","guizhou2a.EMS.Energy10.BMS.Ay2.","guizhou2a.EMS.Energy11.BMS.Ay1.","guizhou2a.EMS.Energy11.BMS.Ay2.","guizhou2a.EMS.Energy12.BMS.Ay1.","guizhou2a.EMS.Energy12.BMS.Ay2.","guizhou2a.EMS.Energy9.BMS.Ay1.","guizhou2a.EMS.Energy9.BMS.Ay2.","guizhou2b.EMS.Energy13.BMS.Ay1.","guizhou2b.EMS.Energy13.BMS.Ay2.","guizhou2b.EMS.Energy14.BMS.Ay1.","guizhou2b.EMS.Energy14.BMS.Ay2.","guizhou2b.EMS.Energy15.BMS.Ay1.","guizhou2b.EMS.Energy15.BMS.Ay2.","guizhou2b.EMS.Energy16.BMS.Ay1.","guizhou2b.EMS.Energy16.BMS.Ay2."]
guizhou3_bms =["guizhou3a.EMS.Energy17.BMS.Ay1.","guizhou3a.EMS.Energy17.BMS.Ay2.","guizhou3a.EMS.Energy18.BMS.Ay1.","guizhou3a.EMS.Energy18.BMS.Ay2.","guizhou3a.EMS.Energy19.BMS.Ay1.","guizhou3a.EMS.Energy19.BMS.Ay2.","guizhou3a.EMS.Energy20.BMS.Ay1.","guizhou3a.EMS.Energy20.BMS.Ay2.","guizhou3b.EMS.Energy21.BMS.Ay1.","guizhou3b.EMS.Energy21.BMS.Ay2.","guizhou3b.EMS.Energy22.BMS.Ay1.","guizhou3b.EMS.Energy22.BMS.Ay2.","guizhou3b.EMS.Energy23.BMS.Ay1.","guizhou3b.EMS.Energy23.BMS.Ay2.","guizhou3b.EMS.Energy24.BMS.Ay1.","guizhou3b.EMS.Energy24.BMS.Ay2."]
guizhou4_bms =["guizhou4a.EMS.Energy25.BMS.Ay1.","guizhou4a.EMS.Energy25.BMS.Ay2.","guizhou4a.EMS.Energy26.BMS.Ay1.","guizhou4a.EMS.Energy26.BMS.Ay2.","guizhou4a.EMS.Energy27.BMS.Ay1.","guizhou4a.EMS.Energy27.BMS.Ay2.","guizhou4a.EMS.Energy28.BMS.Ay1.","guizhou4a.EMS.Energy28.BMS.Ay2.","guizhou4b.EMS.Energy29.BMS.Ay1.","guizhou4b.EMS.Energy29.BMS.Ay2.","guizhou4b.EMS.Energy30.BMS.Ay1.","guizhou4b.EMS.Energy30.BMS.Ay2.","guizhou4b.EMS.Energy31.BMS.Ay1.","guizhou4b.EMS.Energy31.BMS.Ay2.","guizhou4b.EMS.Energy32.BMS.Ay1.","guizhou4b.EMS.Energy32.BMS.Ay2."]
guizhou5_bms =["guizhou5a.EMS.Energy33.BMS.Ay1.","guizhou5a.EMS.Energy33.BMS.Ay2.","guizhou5a.EMS.Energy34.BMS.Ay1.","guizhou5a.EMS.Energy34.BMS.Ay2.","guizhou5a.EMS.Energy35.BMS.Ay1.","guizhou5a.EMS.Energy35.BMS.Ay2.","guizhou5a.EMS.Energy36.BMS.Ay1.","guizhou5a.EMS.Energy36.BMS.Ay2.","guizhou5b.EMS.Energy37.BMS.Ay1.","guizhou5b.EMS.Energy37.BMS.Ay2.","guizhou5b.EMS.Energy38.BMS.Ay1.","guizhou5b.EMS.Energy38.BMS.Ay2.","guizhou5b.EMS.Energy39.BMS.Ay1.","guizhou5b.EMS.Energy39.BMS.Ay2.","guizhou5b.EMS.Energy40.BMS.Ay1.","guizhou5b.EMS.Energy40.BMS.Ay2."]
guizhou6_bms =["guizhou6a.EMS.Energy41.BMS.Ay1.","guizhou6a.EMS.Energy41.BMS.Ay2.","guizhou6a.EMS.Energy42.BMS.Ay1.","guizhou6a.EMS.Energy42.BMS.Ay2.","guizhou6a.EMS.Energy43.BMS.Ay1.","guizhou6a.EMS.Energy43.BMS.Ay2.","guizhou6a.EMS.Energy44.BMS.Ay1.","guizhou6a.EMS.Energy44.BMS.Ay2.","guizhou6b.EMS.Energy45.BMS.Ay1.","guizhou6b.EMS.Energy45.BMS.Ay2.","guizhou6b.EMS.Energy46.BMS.Ay1.","guizhou6b.EMS.Energy46.BMS.Ay2.","guizhou6b.EMS.Energy47.BMS.Ay1.","guizhou6b.EMS.Energy47.BMS.Ay2.","guizhou6b.EMS.Energy48.BMS.Ay1.","guizhou6b.EMS.Energy48.BMS.Ay2."]
guizhou7_bms =["guizhou7a.EMS.Energy49.BMS.Ay1.","guizhou7a.EMS.Energy49.BMS.Ay2.","guizhou7a.EMS.Energy50.BMS.Ay1.","guizhou7a.EMS.Energy50.BMS.Ay2.","guizhou7a.EMS.Energy51.BMS.Ay1.","guizhou7a.EMS.Energy51.BMS.Ay2.","guizhou7b.EMS.Energy52.BMS.Ay1.","guizhou7b.EMS.Energy52.BMS.Ay2.","guizhou7b.EMS.Energy53.BMS.Ay1.","guizhou7b.EMS.Energy53.BMS.Ay2.","guizhou7b.EMS.Energy54.BMS.Ay1.","guizhou7b.EMS.Energy54.BMS.Ay2."]
guizhou8_bms =["guizhou8a.EMS.Energy55.BMS.Ay1.","guizhou8a.EMS.Energy55.BMS.Ay2.","guizhou8a.EMS.Energy56.BMS.Ay1.","guizhou8a.EMS.Energy56.BMS.Ay2.","guizhou8a.EMS.Energy57.BMS.Ay1.","guizhou8a.EMS.Energy57.BMS.Ay2.","guizhou8b.EMS.Energy58.BMS.Ay1.","guizhou8b.EMS.Energy58.BMS.Ay2.","guizhou8b.EMS.Energy59.BMS.Ay1.","guizhou8b.EMS.Energy59.BMS.Ay2.","guizhou8b.EMS.Energy60.BMS.Ay1.","guizhou8b.EMS.Energy60.BMS.Ay2."]

# 所有查询数据的配置结合
all_datas = [[halun_engine, halun_session, HALUN_DATABASE, halun, 'halun', 1000],
             [taicang_engine, taicang_session, TAICANG_DATABASE, taicang, 'taicang', 1000],
             [binhai1_engine, binhai1_session, BINHAI1_DATABASE, binhai1, 'binhai', 1000],
             [binhai2_engine, binhai2_session, BINHAI2_DATABASE, binhai2, 'binhai', 1000]]
all_tf_data = [[ygzhen1_engine, ygzhen1_session, YGZHEN1_DATABASE, ygzhen1, 'ygzhen', 389700],
               [ygzhen2_engine, ygzhen2_session, YGZHEN2_DATABASE, ygzhen2, 'ygzhen', 389700],
               [zgtian1_engine, zgtian1_session, ZGTIAN1_DATABASE, zgtian1, 'zgtian', 389700],
               [zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, zgtian2, 'zgtian', 389700],
               [zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian3, 'zgtian', 389700],
               [zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE, zgtian4, 'zgtian', 389700],
               [houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE, houma1, 'houma', 38970000],
               [houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, houma2, 'houma', 38970000],
               [houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houma3, 'houma', 38970000],
               [houmab2_engine, houmab2_session, HOUMAB2_DATABASE, houma4, 'houma', 38970000]]

# 保电
all_baodian_data = [[baodian1_engine, baodian1_session, BAODIAN1_DATABASE, baodian1_pcs, 'baodian', 389700],
                    [baodian2_engine, baodian2_session, BAODIAN2_DATABASE, baodian2_pcs, 'baodian', 389700],
                    [baodian3_engine, baodian3_session, BAODIAN3_DATABASE, baodian3_pcs, 'baodian', 389700],
                    [baodian4_engine, baodian4_session, BAODIAN4_DATABASE, baodian4_pcs, 'baodian', 389700],
                    [baodian5_engine, baodian5_session, BAODIAN5_DATABASE, baodian5_pcs, 'baodian', 389700]]
all_bms_datas = [[ygzhen1_engine, ygzhen1_session, YGZHEN1_DATABASE, ygzhen1_bms, 'ygzhen', 389700],
                 [ygzhen2_engine, ygzhen2_session, YGZHEN2_DATABASE, ygzhen2_bms, 'ygzhen', 389700],
                 [zgtian1_engine, zgtian1_session, ZGTIAN1_DATABASE, zgtian1_bms, 'zgtian', 389700],
                 [zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, zgtian2_bms, 'zgtian', 389700],
                 [zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian3_bms, 'zgtian', 389700],
                 [zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE, zgtian4_bms, 'zgtian', 389700],
                 [houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE, houma1_bms, 'houma', 38970000],
                 [houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, houma2_bms, 'houma', 38970000],
                 [houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houma3_bms, 'houma', 38970000],
                 [houmab2_engine, houmab2_session, HOUMAB2_DATABASE, houma4_bms, 'houma', 38970000]]
#大同PCS
datong_data = [[datong1_engine, datong1_session, DATONG1_DATABASE, datong1, 'datong', 3897000],
               [datong2_engine, datong2_session, DATONG2_DATABASE, datong2, 'datong', 3897000],
               [datong3_engine, datong3_session, DATONG3_DATABASE, datong3, 'datong', 3897000],
               [datong4_engine, datong4_session, DATONG4_DATABASE, datong4, 'datong', 3897000]]
#大同BMS
datng_bms_datas = [[datong1_engine, datong1_session, DATONG1_DATABASE, datong1_bms, 'datong', 3897000],
                   [datong2_engine, datong2_session, DATONG2_DATABASE, datong2_bms, 'datong', 3897000],
                   [datong3_engine, datong3_session, DATONG3_DATABASE, datong3_bms, 'datong', 3897000],
                   [datong4_engine, datong4_session, DATONG4_DATABASE, datong4_bms, 'datong', 3897000]]
#贵州PCS
guizhou_data = [[guizhou1_engine, guizhou1_session, GUIZHOU1_DATABASE, guizhou1, 'guizhou', 3897000],
               [guizhou2_engine, guizhou2_session, GUIZHOU2_DATABASE, guizhou2, 'guizhou', 3897000],
               [guizhou3_engine, guizhou3_session, GUIZHOU3_DATABASE, guizhou3, 'guizhou', 3897000],
               [guizhou4_engine, guizhou4_session, GUIZHOU4_DATABASE, guizhou4, 'guizhou', 3897000],
               [guizhou5_engine, guizhou5_session, GUIZHOU5_DATABASE, guizhou5, 'guizhou', 3897000],
               [guizhou6_engine, guizhou6_session, GUIZHOU6_DATABASE, guizhou6, 'guizhou', 3897000],
               [guizhou7_engine, guizhou7_session, GUIZHOU7_DATABASE, guizhou7, 'guizhou', 3897000],
               [guizhou8_engine, guizhou8_session, GUIZHOU8_DATABASE, guizhou8, 'guizhou', 3897000]]
#贵州BMS
guizhou_bms_datas = [[guizhou1_engine, guizhou1_session, GUIZHOU1_DATABASE, guizhou1_bms, 'guizhou', 3897000],
               [guizhou2_engine, guizhou2_session, GUIZHOU2_DATABASE, guizhou2_bms, 'guizhou', 3897000],
               [guizhou3_engine, guizhou3_session, GUIZHOU3_DATABASE, guizhou3_bms, 'guizhou', 3897000],
               [guizhou4_engine, guizhou4_session, GUIZHOU4_DATABASE, guizhou4_bms, 'guizhou', 3897000],
               [guizhou5_engine, guizhou5_session, GUIZHOU5_DATABASE, guizhou5_bms, 'guizhou', 3897000],
               [guizhou6_engine, guizhou6_session, GUIZHOU6_DATABASE, guizhou6_bms, 'guizhou', 3897000],
               [guizhou7_engine, guizhou7_session, GUIZHOU7_DATABASE, guizhou7_bms, 'guizhou', 3897000],
               [guizhou8_engine, guizhou8_session, GUIZHOU8_DATABASE, guizhou8_bms, 'guizhou', 3897000]]

# pcs阳泉
ygqn7=ygqn7
ygqn8=ygqn8

# bms阳泉
ygqn7_bms_a=ygqn7_bms_a
ygqn7_bms_b1=ygqn7_bms_b1
ygqn7_bms_b2=ygqn7_bms_b2
ygqn7_bms_c=ygqn7_bms_c
ygqn8_bms=ygqn8_bms
#阳泉PCS
ygqn_data_7 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7, 'ygqn', 3897000]]
ygqn_data_8 = [[ygqn8_engine, ygqn8_session, YGQN8_DATABASE, ygqn8, 'ygqn', 3897000]]
#阳泉BMS
ygqn_bms_datas_a = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_a, 'ygqn', 3897000]]
ygqn_bms_datas_b1 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_b1, 'ygqn', 3897000]]
ygqn_bms_datas_b2 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_b2, 'ygqn', 3897000]]
ygqn_bms_datas_c = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_c, 'ygqn', 3897000]]
ygqn_bms_datas_8 = [[ygqn8_engine, ygqn8_session, YGQN8_DATABASE, ygqn8_bms, 'ygqn', 3897000]]

# all_datas = [[his_engine,his_session,HIS_DATABASE,halun]]
shiduan = ['尖峰', '峰段', '平段', '谷段']

def frozeDataDay(day, db_con, table, names, disg, chag, soc, F, db, maxV):
    # 冻结一天的数据
    soc_startT = ('%s 00:00:01' % day)
    soc_endT =('%s 23:59:59' % day)
    year = int(day[:4])
    month = int(day[5:7])
    for name in names:
        chag_num, disg_num, data3 = 0, 0, []  # 放电数据集，补电数据集，soc数据集
        chag_jf, chag_fd, chag_pd, chag_gd = [], [], [], []  # 充电四个阶段数据集
        disg_jf, disg_fd, disg_pd, disg_gd = [], [], [], []  # 放电四个阶段数据集

        if F == 'TF':  # 调峰站
            sname = '%s%s' % (name.replace('PCS', 'BMS').replace('Lp', 'Ay'), soc)  # SOC
        else:
            sname = '%s%s' % (name, soc)  # SOC
        values = data_sql_(sname,table, soc_startT, soc_endT)
        if values:
            for value in values:
                data3.append(value['value'])
        for dl in shiduan:
            reports = user_session.query(Report).filter(Report.station == db, Report.is_use == 1, Report.descr == dl,
                                                        Report.years == year,func.find_in_set(int(month), Report.months)).order_by(Report.start_time.asc()).all()
            if dl == '尖峰':
                c = chag_jf
                d = disg_jf
            elif dl == '峰段':
                c = chag_fd
                d = disg_fd
            elif dl == '平段':
                c = chag_pd
                d = disg_pd
            elif dl == '谷段':
                c = chag_gd
                d = disg_gd

            for re in reports:
                data1, data2 = [], []
                startT = ('%s %s' % (day, re.start_time))
                endT = ('%s %s' % (day, re.end_time))
                cname = '%s%s' % (name, chag)  # 补电
                values = data_sql_( cname, table, startT, endT)
                for value in values:
                    if db == 'houma':
                        if value['value'] < 65535:
                            data1.append(value['value'])
                    else:
                        data1.append(value['value'])
                dname = '%s%s' % (name, disg)  # 放电
                values = data_sql_( dname, table, startT, endT)
                for value in values:
                    if db == 'houma':
                        if value['value'] < 65535:
                            data2.append(value['value'])
                    else:
                        data2.append(value['value'])
                if db =='houma':
                    if data1!=[]:
                        c.append(max(data1)-min(data1))
                    if data2 != []:
                        d.append(max(data2)-min(data2))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
                else:
                    c.append(list_sum_mm(data1, maxV))
                    d.append(list_sum_mm(data2, maxV))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
        fd, bd = list_soc_mm(data3)  # 放电soc，补电soc

        if db == 'houma':
            if chag_num == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num
        else:
            if chag_num == 0 or fd == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num / fd * bd
        if lv > 1:
            lv = 1
        if lv < 0:
            lv = 0
        print ('table:', table, '--day:', day, '--name:', name,chag_jf,disg_jf,chag_fd,disg_fd,chag_pd,disg_pd,chag_gd,disg_gd,lv)

        user_session.merge(FReport(name=name, jf_chag=str(chag_jf), jf_disg=str(disg_jf), fd_chag=str(chag_fd), fd_disg=str(disg_fd),
                    pd_chag=str(chag_pd), pd_disg=str(disg_pd),
                    gd_chag=str(chag_gd), gd_disg=str(disg_gd), day=day, ratio=float(lv),
                    op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    user_session.close()
    db_con.close()

def data_sql_(sname,table, soc_startT, soc_endT):
    """查询数据"""""
    conn = pool_.connection()
    cursor = conn.cursor()
    try:
        # 执行SQL查询
        sql = """SELECT value
                    FROM {}
                    where measure_id = '{}'
                    and dts_s BETWEEN '{}' AND '{}'
                    order by dts_s asc
                    """.format(table,sname, soc_startT, soc_endT)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()

def frozeMeterDataDay(day, table, db, db_con, chag_l, disg_l, sname):
    '''
    冻结关口表数据
    day:日期
    table:数据源表
    db_con:数据源连接session
    chag:总充电量
    disg:总放电量
    sname:需要存储的变量名，一般为充放电量共有的前缀名
    '''
    chag_dl, disg_dl, lv = 0, 0, 0
    n_time = ('%s %s' % (day, '23:59:59'))
    b_time = ('%s %s' % (day, '00:00:00'))
    print('METER-----table:', table, '--start:', b_time, '--end:', n_time,'--sname:',sname)

    chag = chag_l
    disg = disg_l

    c1 = data_sql_dz_i(chag, table, b_time)
    if not c1:
        c1 = data_sql_dz_s( chag, table, b_time)
    c2 = data_sql_dz_i(chag, table, n_time)
    d1 = data_sql_dz_i(disg, table, b_time)
    if not d1:
        d1 = data_sql_dz_s(disg, table, b_time)
    d2 = data_sql_dz_i(disg, table, n_time)

    print('c1=', c1, '--c2=', c2, '--d1=', d1, '--d2=', d2)

    if db=='zgtian':
        if c2 and c1:
            chag_dl = round((c2[0]['value'] - c1[0]['value']), 2)
        if d1 and d2:
            disg_dl = round((d2[0]['value'] - d1[0]['value']), 2)
    else:
        if c2 and c1:
            chag_dl = round((c2[0]['value'] - c1[0]['value']) * 8000, 2)
        if d1 and d2:
            disg_dl = round((d2[0]['value'] - d1[0]['value']) * 8000, 2)
    if chag_dl:
        lv = disg_dl / chag_dl
    if chag_dl < 0:
        chag_dl = 0
        lv = 0
    if disg_dl < 0:
        disg_dl = 0
        lv = 0
    if lv > 1:
        lv = 1
    user_session.merge(FReport(name=sname, jf_chag=chag_dl, jf_disg=disg_dl, fd_chag=0, fd_disg=0, pd_chag=0, pd_disg=0,
                               gd_chag=0, gd_disg=0, day=day, ratio=float(lv), op_ts=timeUtils.getNewTimeStr(),
                               cause=1))
    user_session.commit()
    user_session.close()
    db_con.close()

def data_sql_dz_i(sname,table,b_time ):
    """查询数据"""""
    if 'ygzhen' in sname:
        conn = pool_yz.connection()
        cursor = conn.cursor()
    else:
        conn = pool_zt.connection()
        cursor = conn.cursor()
    try:
        # 执行SQL查询
        sql = """SELECT value
                    FROM {}
                    where name = '{}'
                    and dts_s <= '{}'
                    order by dts_s desc
                    """.format(table, sname,b_time)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()

def data_sql_dz_s(sname,table,b_time):
    """查询数据"""""
    if 'ygzhen' in sname:
        conn = pool_yz.connection()
        cursor = conn.cursor()
    else:
        conn = pool_zt.connection()
        cursor = conn.cursor()
    try:
        # 执行SQL查询
        sql = """SELECT value
                    FROM {}
                    where name = '{}'
                    and dts_s >= '{}'
                    order by dts_s asc
                    """.format(table, sname, b_time)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()

def frozeDayaByTime(startTime, endTime):
    days = timeUtils.dateToDataList(startTime, endTime)  # 计算间隔的天
    for day in days:
        calculation(day)

# list_3=timeUtils.dateToDataList('2023-11-05','2023-11-14')
# # def calculation(day=None):
# for day in list_3:
def calculation(day=None):
    if not day:
        day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
    # for all_data in all_datas:
    #     table = 'dwd_' +all_data[-2]+'_pcs_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], disg, chag, soc, 'TP', all_data[4], all_data[5])
    # # 调峰电站
    # for all_data in all_tf_data:
    #     table = 'dwd_' +all_data[-2]+'_pcs_measure'
    #     if all_data[4]=='houma':
    #         frozeDataDay(day, all_data[1], table, all_data[3], 'ACTotlDisg', 'ACTotlChag', 'BmsBSOC', 'TF',
    #                      all_data[4], all_data[5])
    #     else:
    #         frozeDataDay(day, all_data[1], table, all_data[3], 'AcDisgCapyTotl', 'AcChagCapyTotl', 'SysSOC', 'TF',
    #                      all_data[4], all_data[5])
    # # 永臻电站根据bms计算
    # for all_data in all_bms_datas:
    #     table = 'dwd_' +all_data[-2]+'_bc_measure'
    #     if all_data[4] == 'houma':
    #         frozeDataDay(day, all_data[1], table, all_data[3], 'SysAcuDisgCapy', 'SysAcuChagCapy', 'SysSOC', 'TF',
    #                      all_data[4], all_data[5])
    #     else:
    #         frozeDataDay(day, all_data[1], table, all_data[3], 'SysAcuDisgCapy', 'SysAcuChagCapy', 'SysSOC', 'TP',
    #                      all_data[4], all_data[5])
    #
    # # 保电
    # for all_data in all_baodian_data:
    #     table = 'dwd_' +all_data[-2]+'_pcs_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], 's.DisgCapyTotl', 's.ChagCapyTotl', 's.M1BatSoc', 'TP',
    #                  all_data[4], all_data[5])
    #
    #
    # # 大同PCS
    # for all_data in datong_data:
    #     table = 'dwd_' +all_data[-2]+'_pcs_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], 'TolDisgEle', 'TolChagEle', 'SysSoc', 'TF',
    #                  all_data[4], all_data[5])
    #
    # # 大同BMS
    # for all_data in datng_bms_datas:
    #     table = 'dwd_' + all_data[-2] + '_bc_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], 'TolDisgCpt', 'TolChagCpt', 'SysSOC',
    #                  'TF',
    #                  all_data[4], all_data[5])
    #
    # # 贵州PCS
    # for all_data in guizhou_data:
    #     table = 'dwd_' +all_data[-2]+'_pcs_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], 'TotlDigEle', 'TotlChgEle', 'Soc', 'TF',
    #                  all_data[4], all_data[5])
    #
    # #贵州BMS
    # for all_data in datng_bms_datas:
    #     table = 'dwd_' + all_data[-2] + '_bc_measure'
    #     frozeDataDay(day, all_data[1], table, all_data[3], 'TolDisgCpt', 'TolChagCpt', 'SysSOC',
    #                  'TF',all_data[4], all_data[5])

    # 阳泉PCS abc
    for all_data in ygqn_data_7:
        table = 'dwd_' + all_data[-2] + '_pcs_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'TlAcDg', 'TlAcCg', 'Soc', 'TF',
                     all_data[4], all_data[5])

    # 阳泉PCS d
    for all_data in ygqn_data_8:
        table = 'dwd_' + all_data[-2] + '_pcs_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'ElTlDg', 'ElTlCg', 'BmsSoc', 'TF',
                     all_data[4], all_data[5])

    # 阳泉BMS a
    for all_data in ygqn7_bms_a:
        table = 'dwd_' + all_data[-2] + '_bc_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'DgElTl', 'CgElTl', 'Soc',
                     'TF', all_data[4], all_data[5])

    # 阳泉BMS b1
    for all_data in ygqn7_bms_b1:
        table = 'dwd_' + all_data[-2] + '_bc_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'TlDig', 'TlChg', 'Soc',
                     'TF', all_data[4], all_data[5])
    # 阳泉BMS b2
    for all_data in ygqn7_bms_b2:
        table = 'dwd_' + all_data[-2] + '_bc_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'TlDgEl', 'TlCgEl', 'Soc',
                     'TF', all_data[4], all_data[5])
    # 阳泉BMS c
    for all_data in ygqn7_bms_c:
        table = 'dwd_' + all_data[-2] + '_bc_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'DgElTl', 'CgElTl', 'Soc',
                     'TF', all_data[4], all_data[5])
    # 阳泉BMS d
    for all_data in ygqn_bms_datas_8:
        table = 'dwd_' + all_data[-2] + '_bc_measure'
        frozeDataDay(day, all_data[1], table, all_data[3], 'CmlDisgCapy', 'CmlChagCapy', 'RkSoc',
                     'TF', all_data[4], all_data[5])

def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(calculation, 'cron', hour=1,minute=5,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.start()

if __name__ == '__main__':

    # print _tableIsExist(halun_engine,HALUN_DATABASE,'r_measure202207')
    try:
    # if 1:
        opts, args = getopt.getopt(sys.argv[1:], "h", ["help", "froze", "start=", "end="])
        cmd = None
        start = None
        end = None
        for opt, arg in opts:
            if opt == "--froze":
                cmd = "froze"
            elif opt == "--start":
                start = arg
            elif opt == "--end":
                end = arg
            else:
                print ('''%s 数据冻结工具
                选项
                    -h|--help 查看帮助
                    --froze 冻结数据
                    --start 起始时刻（含）。yyyy-mm-dd 
                    --end 结束时刻（含）。yyyy-mm-dd 

                ''') % sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            RunClearFileAndData()
            # calculation()
        elif cmd == "froze":
            if not start:
                print ("请指定开始时刻")
                quit()
            if not end:
                print ("请指定结束时刻")
                quit()
            frozeDayaByTime(start, end)
            print ('SUCCESS')

    except Exception as e:
        print (e)