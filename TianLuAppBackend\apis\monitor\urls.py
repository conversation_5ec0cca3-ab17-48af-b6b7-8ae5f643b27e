from django.urls import path
from . import views

urlpatterns = [
    # path('list/', views.ProjectListView.as_view()),  # 项目清单                                            # done
    # path('control_send_smscode/', views.ControlSendSmsCodeView.as_view()),  # 就地控制发送短信
    # path('control_check_smscode/', views.ControlSMSCodeCheckView.as_view()),  # 就地控制下发                   # do
    # path('power_send_smscode/', views.PowerSendSmsCodeView.as_view()),  # 功率下发发送短信
    # path('power_check_smscode/', views.PowerSMSCodeCheckView.as_view()),  # 功率下发                           # do
    # path('power_plan_send_smscode/', views.PowerPlanSendSmsCodeView.as_view()),  # 计划下发发送短信
    # path('power_plan_check_smscode/', views.PowerPlanSMSCodeCheckView.as_view()),  # 功率计划下发               # do
    # path('power_plan_history/', views.PowerPlanHistoryView.as_view()),  # 功率计划下发历史                       # do
    # path('power_plan_history/edit/', views.PowerPlanHistoryEditView.as_view()),  # 功率计划修改                 # do
    # path('unit_view/', views.UintView.as_view()),  # 单元详情                                              # done      1
    # path('unit_switch/', views.UnitSwitchSMSCodeCheckView.as_view()),  # 单元控制下发                            # do
    # path('unit_switch_send_smscode/', views.UnitSwitchSendSmsCodeView.as_view()),  # 单元开关控制下发短信
    # path('unit_power/', views.UnitPowerSMSCodeCheckView.as_view()),  # 单元功率下发                              # do
    # path('unit_power_send_smscode/', views.UnitPowerSendSmsCodeView.as_view()),  # 单元功率下发发送短信
    # path('station/details/', views.StationDetailsView.as_view()),  # 站详情                                # done
    # path('virtually_send_smscode/', views.VirtuallySendSmsCodeView.as_view()),  # 需量下发发送短信
    # path('virtually_check_smscode/', views.VirtuallySMSCodeCheckView.as_view()),  # 需量下发               # do
    # path('reset_fault/sendsms/', views.ResetFaultSendSmsCodeView.as_view()),  # 故障复位发送短信
    # path('reset_fault/', views.ResetFaultSMSCodeCheckView.as_view()),  # 故障复位下发                       # do
    # path('station/list/', views.StationListView.as_view()),  # 电站列表                                    # done    1
    # path('station/unit_income/', views.UnitCapacityIncome.as_view()),  # 单位容量收益                       # done
    # path('list/new/', views.ProjectListNewView.as_view()),  # 项目清单                                     # done     1
    #
    # path('get_unit_running_type_smscode', views.UnitSwitchRunningTypeSendSmsCodeView.as_view()),  # 单元切换运行状态下发短信
    # path('switch_unit_running_type', views.UnitSwitchRunningTypeView.as_view()),  # 单元换运行状态
    path('token', views.Ht.as_view()),
]
