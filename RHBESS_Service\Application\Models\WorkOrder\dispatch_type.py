#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-10-24 16:07:57
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WorkOrder\dispatch_type.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-01 10:15:57


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class DispatchType(user_Base):
    u'工单类型表'
    __tablename__ = "t_dispatch_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"类型名称")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")
    en_descr = Column(String(256), nullable=False, comment=u"类型名称")
   

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':%s,'descr':'%s','op_ts':'%s','isUse':%s,'en_descr':'%s'}" % (self.id,self.descr,self.op_ts,self.is_use,self.en_descr)

        
    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}