#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


HOUMA_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
HOUMA_PORT = model_config.get('mysql', "HIS_PORT")
HOUMA_DATABASE1 =  "tfst_houma_a1_g"
HOUMA_DATABASE2 =  "tfst_houma_a2_g"
HOUMA_DATABASE3 =  "tfst_houma_b1_g"
HOUMA_DATABASE4 =  "tfst_houma_b2_g"
HOUMA_USERNAME = model_config.get('mysql', "HIS_USERNAME")
HOUMA_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")



hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMA_USERNAME,
    HOUMA_PASSWORD,
    HOUMA_HOSTNAME,
    HOUMA_PORT,
    HOUMA_DATABASE1
)
houma_a1_g_bms_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_houma_a1_g_bms_session = sessionmaker(houma_a1_g_bms_engine,autoflush=True)
houma_a1_g_bms_Base = declarative_base(houma_a1_g_bms_engine)
houma_a1_g_bms_session = _houma_a1_g_bms_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMA_USERNAME,
    HOUMA_PASSWORD,
    HOUMA_HOSTNAME,
    HOUMA_PORT,
    HOUMA_DATABASE2
)
houma_a2_g_bms_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houma_a2_g_bms_session = sessionmaker(houma_a2_g_bms_engine,autoflush=True)
houma_a2_g_bms_Base = declarative_base(houma_a2_g_bms_engine)
houma_a2_g_bms_session = _houma_a2_g_bms_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
   HOUMA_USERNAME,
   HOUMA_PASSWORD,
   HOUMA_HOSTNAME,
   HOUMA_PORT,
   HOUMA_DATABASE3
)
houma_b1_g_bms_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houma_b1_g_bms_session = sessionmaker(houma_b1_g_bms_engine,autoflush=True)
houma_b1_g_bms_Base = declarative_base(houma_b1_g_bms_engine)
houma_b1_g_bms_session = _houma_b1_g_bms_session()

hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMA_USERNAME,
    HOUMA_PASSWORD,
    HOUMA_HOSTNAME,
    HOUMA_PORT,
    HOUMA_DATABASE4
)
houma_b2_g_bms_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houma_b2_g_bms_session = sessionmaker(houma_b2_g_bms_engine,autoflush=True)
houma_b2_g_bms_Base = declarative_base(houma_b2_g_bms_engine)
houma_b2_g_bms_session = _houma_b2_g_bms_session()


