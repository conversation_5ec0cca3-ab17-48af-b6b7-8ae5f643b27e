package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.stationr.StationRCreateDTO;
import com.robestec.analysis.dto.stationr.StationRQueryDTO;
import com.robestec.analysis.dto.stationr.StationRUpdateDTO;
import com.robestec.analysis.service.StationRService;
import com.robestec.analysis.vo.StationRVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电站关系管理API
 */
@RestController
@RequestMapping("/station-relation")
@RequiredArgsConstructor
@Api(tags = "电站关系管理API")
public class StationRController {

    private final StationRService stationRService;

    @GetMapping
    @ApiOperation("分页查询电站关系")
    public PageResult<StationRVO> queryStationR(@Validated StationRQueryDTO queryDTO) {
        return stationRService.queryStationR(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增电站关系")
    public Result<Long> createStationR(@Validated @RequestBody StationRCreateDTO createDTO) {
        return Result.succeed(stationRService.createStationR(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增电站关系")
    public Result createStationRList(@Validated @RequestBody List<StationRCreateDTO> createDTOList) {
        stationRService.createStationRList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改电站关系")
    public Result updateStationR(@PathVariable Long id, @Validated @RequestBody StationRUpdateDTO updateDTO) {
        updateDTO.setId(id);
        stationRService.updateStationR(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除电站关系")
    public Result deleteStationR(@PathVariable Long id) {
        stationRService.deleteStationR(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取电站关系详情")
    public Result<StationRVO> getStationR(@PathVariable Long id) {
        return Result.succeed(stationRService.getStationR(id));
    }

    @GetMapping("/station-name/{stationName}")
    @ApiOperation("根据电站名称查询电站关系")
    public Result<List<StationRVO>> getStationRByStationName(@PathVariable String stationName) {
        return Result.succeed(stationRService.getStationRByStationName(stationName));
    }

    @GetMapping("/province/{province}")
    @ApiOperation("根据省份查询电站关系")
    public Result<List<StationRVO>> getStationRByProvince(@PathVariable String province) {
        return Result.succeed(stationRService.getStationRByProvince(province));
    }

    @GetMapping("/running-state/{runningState}")
    @ApiOperation("根据运行状态查询电站关系")
    public Result<List<StationRVO>> getStationRByRunningState(@PathVariable String runningState) {
        return Result.succeed(stationRService.getStationRByRunningState(runningState));
    }

    @GetMapping("/count/province/{province}")
    @ApiOperation("统计省份的电站关系数量")
    public Result<Long> countByProvince(@PathVariable String province) {
        return Result.succeed(stationRService.countByProvince(province));
    }
}
