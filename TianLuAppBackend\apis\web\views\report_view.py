# coding=utf-8
import datetime
import json
import threading
import traceback
from operator import itemgetter

from django_redis import get_redis_connection
from numpy import average
import os
import openpyxl
import math
import concurrent.futures

from rest_framework.response import Response
from rest_framework.views import APIView
from dbutils.persistent_db import PersistentDB
import pymysql
import logging

from apis.web.async_tasks import upload_day_report_for_download, upload_week_report_for_download
from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
from tools.get_ac_http import ChargeDischargeCount
from datetime import  timedelta

from apis.user import models
from apis.web.models import RunningReport, AnalyzeDict

from common import common_response_code
from django.conf import settings
from middlewares.authentications import J<PERSON>THeader<PERSON>uthentication, DenyAuthentication, JwtParamAuthentication
from LocaleTool.common import redis_pool
from tools.minio_tool import MinioTool
from apis.app2.utils import paging
from openpyxl.styles import colors, PatternFill
from common.constant import EMPTY_STR_LIST
from settings.types_dict import PROJECT_TYPE
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")

star_level_dict = {
    1: '一星',
    2: '二星',
    3: '三星',
    4: '四星',
    5: '五星',
}
en_star_level_dict = {
    1: 'One star',
    2: 'Two stars',
    3: 'Three stars',
    4: 'Four stars',
    5: 'Five stars',
}


class ReportAnalyze(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''分析类型'''
    def get(self, request):
        """分析类型列表"""
        name = request.query_params.get("name")
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("size", 20))
        lang = request.headers.get("lang", 'zh')
        if name:
            res = AnalyzeDict.objects.filter(is_delete=0, name__contains=name).all()
        else:
            res = AnalyzeDict.objects.filter(is_delete=0).all()

        page_res = paging(page, page_size, res)  # 分页器
        data = page_res.get('data')
        data = {i.id: i.name for i in data} if lang == 'zh' else {i.id: i.en_name for i in data}
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
                "page_size": page_size
            }
        )

    def post(self, request):
        """新增分析类型"""
        lang = request.headers.get("lang", 'zh')
        name = request.data.get("name")
        if not AnalyzeDict.objects.filter(name=name).first():
            res = AnalyzeDict.objects.create(name=name, en_name=name)
            # 异步翻译
            pdr_data = {'id': res.id,
                        'table': 't_analyze_dict',
                        'update_data': {'name': name}}

            pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            redis_pool.publish(pub_name, json.dumps(pdr_data))
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": '',
                    },
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"分析类型已存在，请勿重复添加！" if lang == 'zh' else "The analysis type already exists. Do not add it again！"},
                }
            )

class ReportDayCarryOut(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''手动执行日报'''
    def get(self, request):
        from apscheduler_tasks.report_tasks import get_day_report
        # from apscheduler_tasks.r2 import get_day_report
        _time = request.query_params.get("time")  # 开始时间
        master_name = request.query_params.get("master_name")  # 主站英文名称
        if master_name:
            master_station = models.MaterStation.objects.filter(is_delete=0, english_name=master_name).first()
        else:
            master_station = models.MaterStation.objects.filter(is_delete=0).all()
        if master_station:
            try:
                if master_name:
                    get_day_report(master_station, datetime.datetime.strptime(_time, '%Y-%m-%d'))
                else:
                    for master in master_station:
                        get_day_report(master, datetime.datetime.strptime(_time, '%Y-%m-%d'))
                        print('-'*50, master.name,'-执行完成')
                print('='*100, '所有并网点执行完成')
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": '',
                        },
                    }
                )
            except Exception as e:
                print(traceback.print_exc())
                error_log.error(f"{master_station.name} 生成运行报告数据失败:{e}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"生成运行报告数据失败！"},
                    }
                )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"主站不存在！"},
                }
            )

class ReportWeekCarryOut(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''手动执行周报'''
    def get(self, request):
        from apscheduler_tasks.report_tasks import get_week_report
        _time = request.query_params.get("time")  # 开始时间  yyyy-mm-dd
        master_name = request.query_params.get("master_name")  # 主站英文名称
        if master_name:
            master_station = models.MaterStation.objects.filter(is_delete=0, english_name=master_name).first()
        else:
            master_station = models.MaterStation.objects.filter(is_delete=0).all()
        if master_station:
            try:
                t = datetime.datetime.strptime(_time, '%Y-%m-%d')
                today = t
                year_ = today.year
                pre_week = today.strftime('%W')
                if master_name:
                    get_week_report(master_station, year_, pre_week)
                else:
                    for master in master_station:
                        get_week_report(master, year_, pre_week)
                        print('-'*50, master.name,'-周报执行完成')
                print('='*100, '所有并网点执行完成')
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": '',
                        },
                    }
                )
            except Exception as e:
                print(traceback.print_exc())
                error_log.error(f"{master_station.name} 生成运行报告数据失败:{e}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"生成运行报告数据失败！"},
                    }
                )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"主站不存在！"},
                }
            )

class ReportMonthCarryOut(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''手动执行月报'''
    def get(self, request):
        from apscheduler_tasks.report_tasks import get_month_report
        _time = request.query_params.get("time")  # 开始时间  yyyy-mm-dd
        master_name = request.query_params.get("master_name")  # 主站英文名称
        if master_name:
            master_station = models.MaterStation.objects.filter(is_delete=0, english_name=master_name).first()
        else:
            master_station = models.MaterStation.objects.filter(is_delete=0).all()
        if master_station:
            try:
                t = datetime.datetime.strptime(_time, '%Y-%m-%d')
                today = t
                year_ = today.year
                month = today.month
                if master_name:
                    get_month_report(master_station, year_, month)
                else:
                    for master in master_station:
                        get_month_report(master, year_, month)
                        print('-'*50, master.name,'-月报执行完成')
                print('='*100, '所有并网点执行完成')
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": '',
                        },
                    }
                )
            except Exception as e:
                print(traceback.print_exc())
                error_log.error(f"{master_station.name} 生成运行报告数据失败:{e}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f"生成运行报告数据失败！"},
                    }
                )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"主站不存在！"},
                }
            )


class ReportDayViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''检索运行日报'''

    num = 1

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        project = request.data.get("project")  # 项目id
        s_time = request.data.get("s_time")  # 开始时间
        e_time = request.data.get("e_time")  # 结束时间
        page = int(request.data.get("page", 1))  #
        page_size = int(request.data.get("page_size", 100))  #
        is_download = int(request.data.get("is_download", 0))

        project_ = json.loads(project)
        if len(project_):
            for project_id in project_:
                try:
                    project = models.Project.objects.get(id=int(project_id))
                except Exception as e:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": f"项目ID: {project_id}不存在" if lang == 'zh' else
                            f"Project ID: {project_id} does not exist."},
                        }
                    )

            master_stations = models.MaterStation.objects.filter(project_id__in=project_, is_delete=0)
        else:
            user = models.UserDetails.objects.get(id=self.request.user['user_id'])
            master_stations = user.master_stations.filter(is_delete=0).all()

        if is_download != 1:
            return self.day_report(e_time, master_stations, page, page_size, s_time, lang)
        else:
            return self.day_report_for_download(e_time, master_stations, s_time, lang)

    def get_master_stations_day_report_data(self, master_stations, time_list, lang='zh'):
        master_stations_names = [master_station.name for master_station in master_stations]
        master_stations_name_id_map = {master_station.name: master_station.id for master_station in master_stations}
        last_list = []

        running_reports = RunningReport.objects.filter(
            station_name__in=master_stations_names,
            datetime__in=time_list, report_type=1).order_by('id').all()

        if running_reports.exists():
            for s in running_reports:
                # s_time_ = s.datetime + " 00:00:00"
                # e_time_ = s.datetime + " 23:59:59"
                tt = s.datetime[:4] + '年' + s.datetime[5:7] + '月' + s.datetime[8:] + '日' if lang == 'zh' else s.datetime[:4] + '-' + s.datetime[5:7] + '-' + s.datetime[8:]
                last_dict = {}
                last_dict['time'] = tt
                last_dict['datetime'] = s.datetime
                last_dict['station_name'] = s.station_name
                last_dict['name'] = s.station_name
                master = models.MaterStation.objects.get(name=s.station_name)
                last_dict['project_type'] = PROJECT_TYPE.get(master.project.project_type)[lang] if master.project.project_type != None else ''
                last_dict['station_id'] = master_stations_name_id_map[s.station_name]
                last_dict['id'] = s.id  #
                last_dict['sid'] = s.id  #
                last_dict['daily_chag_amount'] = s.charge_cap  # 充电量
                last_dict['daily_disg_amount'] = s.discharge_cap  # 放电量
                last_dict['charge_disg_ratio'] = s.comp_rate

                last_dict['theoretical_daily_chag_amount'] = s.theory_charge  # 逐日基准充电量
                last_dict['theory_chag_ratio'] = s.theory_charge_comp_rate  # 基准充电量完成率
                last_dict['theoretical_daily_disgamount'] = s.theory_discharge  # 逐日基准放电量
                last_dict['theory_disg_ratio'] = s.theory_discharge_comp_rate  # 基准放电量完成率

                last_dict['initial_soc'] = s.soc_init  # 逐日SOC初始值
                last_dict['final_soc'] = s.soc_final  # 逐日SoC终值
                last_dict['maximum_soc'] = s.soc_max  # 逐日SOC最大值
                last_dict['minimum_soc'] = s.soc_min  # 逐日SOC最小值

                last_dict['cumulative_chag_amount'] = s.accu_charge  # 累计充电量
                last_dict['cumulative_disg_amount'] = s.accu_discharge  # 累计放电量
                last_dict['cumulative_charge_disg_efficiency'] = s.effic

                last_dict['daily_income'] = s.income  # 当日收益

                # # 故障告警
                # ins = models.FaultAlarm.objects.filter(station__master_station=master_station,
                #                                        start_time__gte=s_time_,
                #                                        end_time__lte=e_time_)
                #
                # # 统计 type = 0的告警数量
                # alarm_count = ins.filter(type=0).count()
                # # 统计 type = 1的故障数量
                # fault_count = ins.filter(type=1).count()
                # # 统计 type = 2的报警数量
                # warning_count = ins.filter(type=2).count()
                # alarm_count_ = alarm_count + fault_count + warning_count
                # alarm_count_2 = alarm_count + warning_count

                last_dict['fault_alarm'] = {"count": s.alarm + s.fault, "warning_count": s.alarm,
                                            "fault_count": s.fault}

                last_dict['analyse'] = s.analyse if lang == 'zh' else s.en_analyse # 备注
                analyze_id_list = s.analyze_ids.split(',') if s.analyze_ids else []

                last_dict['analyze_list'] = [int(i) for i in analyze_id_list] # 分析类型

                last_dict['theory_income_day'] = s.theory_income_day  # 日基准收益
                last_dict['reach_the_standard'] = s.reach_the_standard  # 日收益是否达标
                last_dict['income_day_reach_yield'] = s.income_day_reach_yield  # 日收益达成率（%）
                last_dict['theory_income_month'] = s.theory_income_month  # 月基准收益
                last_dict['income_month'] = s.income_month  # 月收益
                last_dict['theory_income_year'] = s.theory_income_year  # 年基准收益
                last_dict['income_year'] = s.income_year  # 年收益
                last_dict['theory_income_all'] = s.theory_income_all  # 累计基准收益
                last_dict['income_all'] = s.income_all  # 累计收益
                last_dict['income_all_reach_yield'] = s.income_all_reach_yield  # 累计收益达成率（%）
                last_dict['star_level'] = star_level_dict.get(s.star_level) if lang == 'zh' else en_star_level_dict.get(s.star_level)  # 星级：1-5星
                last_dict['reach_the_standard'] = s.reach_the_standard  # 收益是否达标


                last_dict['spike_charge'] = s.spike_charge  # 尖峰充电量
                last_dict['spike_discharge'] = s.spike_discharge  # 尖峰放电量
                last_dict['peak_charge'] = s.peak_charge  # 峰时充电量
                last_dict['peak_discharge'] = s.peak_discharge  # 峰时放电量

                last_dict['flat_charge'] = s.flat_charge  # 平时充电量
                last_dict['flat_discharge'] = s.flat_discharge  # 平时放电量

                last_dict['valley_charge'] = s.valley_charge  # 谷时充电量
                last_dict['valley_discharge'] = s.valley_discharge  # 谷时放电量

                last_dict['dvalley_charge'] = s.dvalley_charge  # 深谷时充电量
                last_dict['dvalley_discharge'] = s.dvalley_discharge  # 深谷时放电量

                last_list.append(last_dict)
        return last_list

    def day_report(self, e_time, master_stations, page, page_size, s_time, lang='zh'):

        # 单独起1个线程缓存需要下载的数据
        user_id = self.request.user['user_id']
        # s = threading.Thread(target=upload_day_report_for_download, args=(master_stations, e_time, s_time, user_id, lang))
        # s.start()

        time_list = self.dateToDataList(s_time, e_time)
        last_list = self.get_master_stations_day_report_data(master_stations, time_list, lang)

        # # 使用多线程优化处理速度
        # with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        #     futures = list()
        #     for master_station in master_stations:
        #         for t in time_list:
        #             future = executor.submit(self.get_master_station_day_report_data, master_station, t, lang)
        #             futures.append(future)
        #
        #     for future in concurrent.futures.as_completed(futures):
        #         last_list_ = future.result()
        #         if last_list_:
        #             last_list += last_list_

        last_list = sorted(last_list, key=itemgetter('name', 'datetime'))

        total_pages = math.ceil(len(last_list) / int(page_size))
        start_index = (int(page) - 1) * int(page_size)
        end_index = int(page) * int(page_size) if int(page) < total_pages else len(last_list) + 1
        last_list_ = last_list[start_index: end_index]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": last_list_, 'total': len(last_list)},
            }
        )

    def get_master_station_day_report_data_for_download(self, master_stations, time_list, lang='zh'):
        master_stations_names = [master_station.name for master_station in master_stations]
        master_stations_name_id_map = {master_station.name: master_station.id for master_station in master_stations}

        execl_data_2_list = []
        execl_data_list = []

        running_reports = RunningReport.objects.filter(
            station_name__in=master_stations_names,
            datetime__in=time_list, report_type=1).order_by('id').all()
        if running_reports.exists():
            for s in running_reports:
                s_time_ = s.datetime + " 00:00:00"
                e_time_ = s.datetime + " 23:59:59"
                tt = s.datetime[:4] + '年' + s.datetime[5:7] + '月' + s.datetime[8:] + '日' if lang == 'zh' else s.datetime[:4] + '-' + s.datetime[5:7] + '-' + s.datetime[8:]
                execl_data = {}
                execl_data['日期'] = tt
                master = models.MaterStation.objects.get(name=s.station_name)
                execl_data['项目类型'] = PROJECT_TYPE.get(master.project.project_type)[lang] if master.project.project_type != None else ''
                execl_data['并网点名称'] = s.station_name
                execl_data['充电量(kWh)'] = s.charge_cap
                execl_data['放电量(kWh)'] = s.discharge_cap


                stations = models.StationDetails.objects.filter(is_delete=0, master_station__name=s.station_name).all()
                stations_ids = [s.id for s in stations] if stations.exists() else []
                # 故障告警
                ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=stations_ids,
                                                       start_time__gte=s_time_,
                                                       end_time__lte=e_time_)
                alarm_ins = (
                    ins.filter(type__in=[1, 2]).values("status", "type", "start_time", "end_time", "details", "id",
                                                  "note",
                                                  "station_id",
                                                  "device")
                    .order_by("-start_time")
                )
                for alarm in alarm_ins:
                    station = models.StationDetails.objects.filter(id=alarm['station_id']).first()

                    execl_data_2 = {}
                    execl_data_2['序号'] = self.num
                    self.num += 1
                    execl_data_2['日期'] = tt
                    execl_data_2['并网点名称'] = station.station_name if station else '--'
                    execl_data_2['设备名称'] = alarm["device"]
                    execl_data_2['报文'] = ALARM_ZH_EN_MAP[alarm["details"].strip()][lang]
                    if alarm["type"] == 0:
                        execl_data_2['告警分类'] = '告警' if lang == 'zh' else 'Alarm'
                    elif alarm["type"] == 1:
                        execl_data_2['告警分类'] = '故障' if lang == 'zh' else 'Fault'
                    elif alarm["type"] == 2:
                        execl_data_2['告警分类'] = '报警' if lang == 'zh' else 'Warning'
                    alarm["start_time"] = alarm["start_time"].strftime('%Y-%m-%d %H:%M:%S')
                    alarm["end_time"] = alarm["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                    execl_data_2['开始时间'] = alarm["start_time"]
                    execl_data_2['结束时间'] = alarm["end_time"]
                    execl_data_2_list.append(execl_data_2)
                    alarm["details"] = (station.station_name if station else '--') + ":" + alarm["device"] + ALARM_ZH_EN_MAP[alarm["details"].strip()][lang]

                execl_data['基准充电量(kWh)'] = s.theory_charge
                execl_data['日充电量达成率(%)'] = s.theory_charge_comp_rate
                execl_data['基准放电量(kWh)'] = s.theory_discharge
                execl_data['日放电量达成率(%)'] = s.theory_discharge_comp_rate
                execl_data['逐日充放电效率(%)'] = s.comp_rate
                # execl_data['SOC初始值（%）'] = s.soc_init
                # execl_data['SOC终值（%）'] = s.soc_final
                # execl_data['SOC最大值（%）'] = s.soc_max
                # execl_data['SOC最小值（%）'] = s.soc_min

                execl_data['尖时充电量(kWh)'] = s.spike_charge
                execl_data['尖时放电量(kWh)'] = s.spike_discharge

                execl_data['峰时充电量(kWh)'] = s.peak_charge
                execl_data['峰时放电量(kWh)'] = s.peak_discharge

                execl_data['平时充电量(kWh)'] = s.flat_charge
                execl_data['平时放电量(kWh)'] = s.flat_discharge

                execl_data['谷时充电量(kWh)'] = s.valley_charge
                execl_data['谷时放电量(kWh)'] = s.valley_discharge

                execl_data['深谷时充电量(kWh)'] = s.dvalley_charge
                execl_data['深谷时放电量(kWh)'] = s.dvalley_discharge

                execl_data['累计充电量（kWh）'] = s.accu_charge
                execl_data['累计放电量（kWh）'] = s.accu_discharge

                execl_data['累计充放电效率（%）'] = s.effic
                execl_data['收益（元）'] = s.income
                execl_data['日基准收益（元）'] = s.theory_income_day
                execl_data['日收益达成率（%）'] = s.income_day_reach_yield
                execl_data['月收益（元）'] = s.income_month
                execl_data['月基准收益（元）'] = s.theory_income_month
                execl_data['年累计收益（元）'] = s.income_year
                execl_data['年累计基准收益（元）'] = s.theory_income_year
                execl_data['累计收益（元）'] = s.income_all
                execl_data['累计基准收益（元）'] = s.theory_income_all
                execl_data['累计收益达成率（%）'] = s.income_all_reach_yield
                execl_data['星级'] = star_level_dict.get(s.star_level) if lang == 'zh' else en_star_level_dict.get(
                    s.star_level)
                execl_data['故障数量'] = s.fault + s.alarm
                if s.reach_the_standard not in EMPTY_STR_LIST:
                    if lang == 'zh':
                        reach_the_standard = '是' if s.reach_the_standard == 1 else '否'
                    else:
                        reach_the_standard = 'YES' if s.reach_the_standard == 1 else 'NO'
                else:
                    reach_the_standard = '--'
                execl_data['日收益是否达标'] = reach_the_standard
                analyze_res = AnalyzeDict.objects.filter(is_delete=0).all()
                analyze_res = {str(i.id): i.name for i in analyze_res} if lang == 'zh' else {str(i.id): i.en_name for i in analyze_res}
                analyze_id_list = s.analyze_ids.split(',') if s.analyze_ids else []
                execl_data['分析'] = ','.join([analyze_res.get(i) for i in analyze_id_list])

                execl_data_list.append(execl_data)

        return execl_data_list, execl_data_2_list

    def day_report_for_download(self, e_time, master_stations, s_time, lang='zh'):
        user_id = self.request.user.get('user_id')

        if lang == 'zh':

            # 优先从 redis 中取缓存数据
            # key = f"tianlu_{start_time}-{end_time}天禄运行月报_{user_id}"
            key = f"tianlu_{s_time}-{e_time}天禄运行日报_{user_id}"

            redis_conn = get_redis_connection("default")
            # 先从redis获取数据
            try:
                export_url = redis_conn.get(key)

                if export_url:
                    return Response(
                        {
                            "code": common_response_code.SUCCESS,
                            "data": {"message": f"success", "detail": export_url},
                        }
                    )

            except Exception as e:
                print(traceback.print_exc())
                pass


            execl_data_11 = {"日期": "日期", "项目类型": "项目类型", "并网点名称": "并网点名称", "充电量(kWh)": "充电量(kWh)", "放电量(kWh)": "放电量(kWh)",
                             "基准充电量(kWh)": "基准充电量(kWh)",  "日充电量达成率(%)": "日充电量达成率(%)",
                             "基准放电量(kWh)": "基准放电量(kWh)", "日放电量达成率(%)": "日放电量达成率(%)",
                             "逐日充放电效率(%)": "逐日充放电效率(%)",
                             "尖时充电量(kWh)": "尖时充电量(kWh)", "尖时放电量(kWh)": "尖时放电量(kWh)", "峰时充电量(kWh)": "峰时充电量(kWh)", "峰时放电量(kWh)": "峰时放电量(kWh)",
                             "平时充电量(kWh)": "平时充电量(kWh)", "平时放电量(kWh)": "平时放电量(kWh)", "谷时充电量(kWh)": "谷时充电量(kWh)", "谷时放电量(kWh)": "谷时放电量(kWh)",
                             "深谷时充电量(kWh)": "深谷时充电量(kWh)", "深谷时放电量(kWh)": "深谷时放电量(kWh)",
                             "累计充电量（kWh）": "累计充电量（kWh）", "累计放电量（kWh）": "累计放电量（kWh）", "累计充放电效率（%）": "累计充放电效率（%）",
                             "收益（元）": "收益（元）", "日基准收益（元）": "日基准收益（元）", "日收益达成率（%）": "日收益达成率（%）", "月收益（元）": "月收益（元）",
                             "月基准收益（元）": "月基准收益（元）", "年累计收益（元）": "年累计收益（元）", "年累计基准收益（元）": "年累计基准收益（元）",
                             "累计收益（元）": "累计收益（元）", "累计基准收益（元）": "累计基准收益（元）", "累计收益达成率（%）": "累计收益达成率（%）",
                             "星级": "星级",
                             "故障数量": "故障数量", "日收益是否达标": "日收益是否达标",  "分析": "分析"}
            execl_data_22 = {"序号": "序号", "日期": "日期", "并网点名称": "并网点名称", "设备名称": "设备名称", "报文": "报文", "告警分类": "告警分类",
                             "开始时间": "开始时间", "结束时间": "结束时间"}
            time_list = self.dateToDataList(s_time, e_time)
            execl_data_list = []
            execl_data_2_list = []
            last_execl_data_list = []
            last_execl_data_2_list = []
            last_execl_data_list.append(execl_data_11)
            last_execl_data_2_list.append(execl_data_22)

            # 使用多线程优化处理速度
            # with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            #     futures = list()
            #     for master_station in master_stations:
            #         for t in time_list:
            #             future = executor.submit(self.get_master_station_day_report_data_for_download, master_station, t, lang)
            #             futures.append(future)
            #
            #     for future in concurrent.futures.as_completed(futures):
            #         execl_data_list_, execl_data_2_list_ = future.result()
            #         if execl_data_list_:
            #             execl_data_list += execl_data_list_
            #         if execl_data_2_list_:
            #             execl_data_2_list += execl_data_2_list_

            execl_data_list_, execl_data_2_list_ = self.get_master_station_day_report_data_for_download(master_stations, time_list, lang)
            execl_data_list += execl_data_list_
            execl_data_2_list += execl_data_2_list_

            execl_data_list = sorted(execl_data_list, key=itemgetter('并网点名称', '日期'))
            execl_data_2_list = sorted(execl_data_2_list, key=itemgetter('并网点名称', '日期'), reverse=True)

            last_execl_data_list = last_execl_data_list + execl_data_list
            last_execl_data_2_list = last_execl_data_2_list + execl_data_2_list

            file_name = f"{s_time}~{e_time}天禄运行日报_{user_id}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            workbook = openpyxl.Workbook()

            data = {
                "运行日报": last_execl_data_list,
                "故障告警记录": last_execl_data_2_list,
            }
            # 遍历字典，将数据写入不同的sheet
            for sheet_name, sheet_data in data.items():
                sheet = workbook.create_sheet(title=sheet_name)
                for index, row_data in enumerate(sheet_data, start=1):
                    if sheet_name == '运行日报' and index > 1:
                        if row_data.get('日充电量达成率(%)') not in EMPTY_STR_LIST and row_data.get('日充电量达成率(%)') >= 150:
                            yellow_fill = PatternFill(start_color='d8f7d7', fill_type='solid')
                            for i in sheet[index]:
                                i.fill = yellow_fill
                        if row_data.get('日放电量达成率(%)') not in EMPTY_STR_LIST and row_data.get('日放电量达成率(%)') >= 150:
                            yellow_fill = PatternFill(start_color='d8f7d7', fill_type='solid')
                            for i in sheet[index]:
                                i.fill = yellow_fill
                    for col_num, cell_value in enumerate(row_data.values(), start=1):
                        cell = sheet.cell(row=index, column=col_num)
                        cell.value = cell_value
            # 删除sheet页
            del workbook['Sheet']
            # 保存工作簿到文件夹
            workbook.save(path)


            # 上传至minio
            try:
                minio_client = MinioTool()
                minio_client.create_bucket('download')
                url = minio_client.upload_local_file(file_name, path, bucket_name='download')
                os.remove(path)

                # 缓存下载链接到redis
                # key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
                redis_conn = get_redis_connection("default")
                redis_conn.set(key, url, 60 * 5)

            except Exception as e:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": f"上传文件报错：{e}"},
                })

        else:
            # 优先从 redis 中取缓存数据
            # key = f"tianlu_{start_time}-{end_time}天禄运行月报_{user_id}"
            key = f"tianlu_{s_time}-{e_time}_operation_daily_{user_id}"

            redis_conn = get_redis_connection("default")
            # 先从redis获取数据
            try:
                export_url = redis_conn.get(key)

                if export_url:
                    return Response(
                        {
                            "code": common_response_code.SUCCESS,
                            "data": {"message": f"success", "detail": export_url},
                        }
                    )

            except Exception as e:
                print(traceback.print_exc())
                pass


            execl_data_11 = {"日期": "Date", "项目类型": "Item Type",
                             "并网点名称": "Branh Name", "充电量(kWh)": "Energy Charged(kWh)",
                             "放电量(kWh)": "Energy Discharged(kWh)",
                             "基准充电量(kWh)": "Reference Energy Charged(kWh)",
                             "日充电量达成率(%)": "Daily Charging Rate Achieved(%)",
                             "基准放电量(kWh)": "Reference Energy Disharged(kWh)",
                             "日放电量达成率(%)": "Daily Discharge Rate Achieved(%)",
                             "逐日充放电效率(%)": "Daily Charging And Discharging Efficiency(%)",
                             # "SOC初始值（%）": "SOC Initial Value（%）",
                             # "SOC终值（%）": "SOC Final Value（%）", "SOC最大值（%）": "SOC Maximum Value（%）",
                             # "SOC最小值（%）": "SOC Minimum Value（%）",
                             "尖时充电量(kWh)": "Peak-hour Energy Charged(kWh)",
                             "尖时放电量(kWh)": "Peak-hour Energy Discharged(kWh)",
                             "峰时充电量(kWh)": "Shoulder-hour Energy Charged(kWh)",
                             "峰时放电量(kWh)": "Shoulder-hour Energy Discharged(kWh)",
                             "平时充电量(kWh)": "Off-peak-hour Energy Charged(kWh)",
                             "平时放电量(kWh)": "Off-peak-hour Energy Discharged(kWh)",
                             "谷时充电量(kWh)": "Valley-hour Energy Charged(kWh)",
                             "谷时放电量(kWh)": "Valley-hour Energy Discharged(kWh)",
                             "深谷时充电量(kWh)": "Deep Valley-hour Energy Charged(kWh)",
                             "深谷时放电量(kWh)": "Deep Valley-hour Energy Discharged(kWh)",
                             "累计充电量（kWh）": "Cumulative Energy Charged（kWh）",
                             "累计放电量（kWh）": "Cumulative Energy Discharged（kWh）",
                             "累计充放电效率（%）": "Cumulative Efficiency（%）",
                             "收益（元）": "Profit（Yuan）",
                             "日基准收益（元）": "Daily Benchmark Profit（Yuan）",
                             "日收益达成率（%）": "Daily Revenue Achievement Rate（%）",
                             "月收益（元）": "Monthly Profit（Yuan）",
                             "月基准收益（元）": "Monthly Benchmark Profit（Yuan）",
                             "年累计收益（元）": "Annual Cumulative Profit（Yuan）",
                             "年累计基准收益（元）": "Annual Cumulative Benchmark Profit（Yuan）",
                             "累计收益（元）": "Cumulative Profit（Yuan）",
                             "累计基准收益（元）": "Cumulative Benchmark Profit（Yuan）",
                             "累计收益达成率（%）": "Cumulative Profit Achievement Rate（%）",
                             "星级": "Star Level",
                             "故障数量": "Number of Failures",
                             "日收益是否达标": "Daily Earnings Are Up To Standard",
                             "分析": "Analysis"}

            execl_data_22 = {"序号": "Number", "日期": "Date", "并网点名称": "Branh Name", "设备名称": "Device",
                             "报文": "Message", "告警分类": "Alarm Types",
                             "开始时间": "From", "结束时间": "To"}
            time_list = self.dateToDataList(s_time, e_time)
            execl_data_list = []
            execl_data_2_list = []
            last_execl_data_list = []
            last_execl_data_2_list = []
            last_execl_data_list.append(execl_data_11)
            last_execl_data_2_list.append(execl_data_22)

            # 使用多线程优化处理速度
            # with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            #     futures = list()
            #     for master_station in master_stations:
            #         for t in time_list:
            #             future = executor.submit(self.get_master_station_day_report_data_for_download, master_station, t, lang)
            #             futures.append(future)
            #
            #     for future in concurrent.futures.as_completed(futures):
            #         execl_data_list_, execl_data_2_list_ = future.result()
            #         if execl_data_list_:
            #             execl_data_list += execl_data_list_
            #         if execl_data_2_list_:
            #             execl_data_2_list += execl_data_2_list_

            execl_data_list_, execl_data_2_list_ = self.get_master_station_day_report_data_for_download(master_stations,
                                                                                                        time_list, lang)

            if execl_data_list_:
                execl_data_list += execl_data_list_
            if execl_data_2_list_:
                execl_data_2_list += execl_data_2_list_

            execl_data_list = sorted(execl_data_list, key=itemgetter('并网点名称', '日期'))
            execl_data_2_list = sorted(execl_data_2_list, key=itemgetter('并网点名称', '日期'), reverse=True)

            last_execl_data_list = last_execl_data_list + execl_data_list
            last_execl_data_2_list = last_execl_data_2_list + execl_data_2_list

            file_name = f"{s_time}~{e_time}_Tianlu_Operation_Daily_{user_id}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            workbook = openpyxl.Workbook()

            data = {
                "Operation Daily": last_execl_data_list,
                "Fault Alarm Record": last_execl_data_2_list,
            }
            # 遍历字典，将数据写入不同的sheet
            for sheet_name, sheet_data in data.items():
                sheet = workbook.create_sheet(title=sheet_name)
                for index, row_data in enumerate(sheet_data, start=1):
                    if sheet_name == 'Operation Daily' and index > 1:
                        if row_data.get('日充电量达成率(%)') not in EMPTY_STR_LIST and row_data.get('日充电量达成率(%)') >= 150:
                            yellow_fill = PatternFill(start_color='d8f7d7', fill_type='solid')
                            for i in sheet[index]:
                                i.fill = yellow_fill
                        if row_data.get('日放电量达成率(%)') not in EMPTY_STR_LIST and row_data.get('日放电量达成率(%)') >= 150:
                            yellow_fill = PatternFill(start_color='d8f7d7', fill_type='solid')
                            for i in sheet[index]:
                                i.fill = yellow_fill
                    for col_num, cell_value in enumerate(row_data.values(), start=1):
                        cell = sheet.cell(row=index, column=col_num)
                        cell.value = cell_value
            # 删除sheet页
            del workbook['Sheet']
            # 保存工作簿到文件夹
            workbook.save(path)

            # 上传至minio
            try:
                minio_client = MinioTool()
                minio_client.create_bucket('download')
                url = minio_client.upload_local_file(file_name, path, bucket_name='download')
                os.remove(path)

                # 缓存下载链接到redis
                # key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
                redis_conn = get_redis_connection("default")
                redis_conn.set(key, url, 60 * 5)

            except Exception as e:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": f"Upload file error：{e}"},
                })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": url},
            }
        )

    def dateToDataList(self, start, end):
        # 计算时间段内的时间列表,包含首位
        datestart = datetime.datetime.strptime(start[0:10], '%Y-%m-%d')
        dateend = datetime.datetime.strptime(end[0:10], '%Y-%m-%d')
        data_list = list()
        while datestart <= dateend:
            data_list.append(datestart.strftime('%Y-%m-%d'))
            datestart += datetime.timedelta(days=1)
        return data_list


class ReportDayUpdateViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''保存日报分析'''

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.data.get("id")  # 报表id
        analyse = request.data.get("analyse", None)  # 分析
        _type = request.data.get("type") # 日报：1
        analyze_ids = request.data.get("analyze_ids") # 分析类型ID多个用英文隔开; 只有日报需要
        run_Report = RunningReport.objects.filter(id=id).first()
        if run_Report:
            if _type and _type == '1':
                analyse = analyse
                RunningReport.objects.filter(id=id).update(analyse=analyse, en_analyse=analyse, analyze_ids=analyze_ids)
            else:
                analyse = ';'.join(analyse.split()) if analyse else ''
                RunningReport.objects.filter(id=id).update(analyse=analyse, en_analyse=analyse)



        #     run_Report.analyse = analyse
        #     run_Report.en_analyse = analyse
        #
        # run_Report.save()

        # 异步翻译
        pdr_data = {'id': run_Report.id,
                    'table': 't_running_report',
                    'update_data': {'analyse': analyse}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {"message": "success", "detail": "修改成功！" if lang == 'zh' else 'Modify successfully!'},
        })


class ReportDayCgDgViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''日报充放电小时数据展示'''

    def post(self, request):
        station_id = request.data.get("station_id")  # 项目id
        t = request.data.get("t")  # 日期
        tt = t[:4] + '-' + t[5:7] + '-' + t[8:10]
        last_dict = {}
        time_ = datetime.datetime.strptime(tt + ' 00:00:00', "%Y-%m-%d %H:%M:%S")  # parse the date string
        # station_insss = models.StationDetails.objects.filter(project_id=project).order_by('id')
        master_stations = models.MaterStation.objects.filter(id=station_id, is_delete=0)
        # 站充放电量（逐时冲放电量统计成峰平谷 (-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")）
        if master_stations.exists():
            detail_1 = ChargeDischargeCount(time_).new_get_stations(master_stations.first(), tt)
            if detail_1:
                last_dict['peak_chag_amount'] = detail_1  # 逐时冲放电量统计成峰平谷 (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {"message": "success", "detail": last_dict},
        })


class ReportSOCViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''日报soc原始数据展示'''

    def post(self, request):
        station_id = request.data.get("station_id")  # 项目id
        t = request.data.get("t")  # 日期
        tt = t[:4] + '-' + t[5:7] + '-' + t[8:10]
        start_time = datetime.datetime.strptime(tt + ' 00:00:00', "%Y-%m-%d %H:%M:%S")  # parse the date string
        end_time = datetime.datetime.strptime(tt + ' 23:59:59', "%Y-%m-%d %H:%M:%S")  # parse the date string
        master_stations = models.MaterStation.objects.filter(id=station_id, is_delete=0)
        units_inst = models.Unit.objects.filter(is_delete=0, station__master_station=master_stations.first()).all()

        dict_11 = {}
        list_22 = []
        for unit in units_inst:
            detail = get_history_data_f_soc(('soc',), 'dwd_measure_bms_data_storage_3', unit.bms, unit.station.english_name,start_time, end_time)
            if detail != []:
                for d in detail:
                    time_=d['time'].strftime("%Y-%m-%d %H:%M")
                    if time_ not in dict_11.keys():
                        dict_11[time_]=[]
                        dict_11[time_].append(d['soc'])
                    else:
                        dict_11[time_].append(d['soc'])
        for dd in dict_11.keys():
            dict_22={}
            if dict_11[dd] == []:
                soc_avg = 0.0
            else:
                soc_avg = ('%.2f' % (average(dict_11[dd])))
            dict_22['soc']=soc_avg
            dict_22['time']=dd
            list_22.append(dict_22)
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {"message": "success", "detail": list_22},
        })


def get_history_data_f_soc(value_n, table_name, de, station_name, start_time, end_time):
        '''查询小时历史soc变化数据'''
        conn = pool.connection()
        cursor = conn.cursor()
        try:
            # 执行SQL查询：====> 兼容新增台区数据上报间隔为1分钟粒度: MOD(MINUTE(time), 5) = 0
            sql = """SELECT {},time,device
                            FROM {}
                            WHERE 1=1
                            and (type=1 or type is NULL)
                            and device='{}'                          
                            and station_name='{}'
                            and time BETWEEN '{}' AND '{}'
                            and {} IS NOT NULL
                            and MOD(MINUTE(time), 5) = 0        
                            ORDER BY time ASC
                            """.format(
                value_n[0], table_name, de, station_name, start_time, end_time, value_n[0])
            error_log.error(sql)
            try:
                cursor.execute(sql)
            except Exception as e:
                error_log.error(e)
            # 获取查询结果
            result = cursor.fetchall()
            if not result:
                return []
            return result
        except Exception as e:
            error_log.error(e)
        finally:
            cursor.close()


class ReportWeekViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''检索运行周报'''

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        project = request.data.get("project")  # 项目id
        s_time = request.data.get("s_time")  # 开始时间
        e_time = request.data.get("e_time")  # 结束时间
        page = int(request.data.get("page", 1))
        page_size = int(request.data.get("page_size", 100))
        is_download = int(request.data.get("is_download", 0))

        smonday, ssunday=get_week_start_end(s_time)
        emonday, esunday=get_week_start_end(e_time)

        project_ = json.loads(project)
        if len(project_):
            for project_id in project_:
                try:
                    project = models.Project.objects.get(id=int(project_id))
                except Exception as e:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": f"项目ID: {project_id}不存在" if lang == 'zh' else f"Project ID: {project_id} does not exist."},
                        }
                    )

            # stations = models.StationDetails.objects.filter(project_id__in=project_ids)
            master_stations = models.MaterStation.objects.filter(project_id__in=project_, is_delete=0)
        else:
            user = models.UserDetails.objects.get(id=self.request.user['user_id'])
            master_stations = user.master_stations.filter(is_delete=0).all()

        if is_download:
            return self.week_report_for_download(e_time, esunday, master_stations, s_time, smonday, lang)
        else:
            return self.week_report(e_time, esunday, master_stations, page, page_size, s_time, smonday, lang)

    def deal_one_report(self, master_station, smonday, esunday, lang='zh'):

        temp_list = []

        running_reports = RunningReport.objects.filter(station_name=master_station.name, report_type=2,
                                                       datetime__gte=smonday, datetime_end__lte=esunday).order_by(
            'datetime')
        if running_reports:
            for s in running_reports:
                last_dict = {}

                last_dict['s_time_'] = s.datetime
                last_dict['e_time_'] = s.datetime_end
                last_dict['name'] = master_station.name
                last_dict['station_id'] = master_station.id

                last_dict['id'] = s.id  #
                last_dict['daily_chag_amount'] = s.charge_cap  # 充电量
                last_dict['daily_disg_amount'] = s.discharge_cap  # 放电量
                last_dict['charge_disg_ratio'] = s.comp_rate

                last_dict['count'] = s.count  # 充放电次数

                last_dict['theoretical_daily_chag_amount'] = s.theory_charge  # 逐日基准充电量
                last_dict['theory_chag_ratio'] = s.theory_charge_comp_rate  # 基准充电量完成率
                last_dict['theoretical_daily_disgamount'] = s.theory_discharge  # 逐日基准放电量
                last_dict['theory_disg_ratio'] = s.theory_discharge_comp_rate  # 基准放电量完成率

                last_dict['cumulative_chag_amount'] = s.accu_charge  # 累计充电量
                last_dict['cumulative_disg_amount'] = s.accu_discharge  # 累计放电量
                last_dict['cumulative_charge_disg_efficiency'] = s.effic

                last_dict['daily_income'] = s.income  # 当日收益

                last_dict['fault_alarm'] = {"count": s.alarm + s.fault, "warning_count": s.alarm,
                                            "fault_count": s.fault}

                last_dict['analyse'] = s.analyse if lang == 'zh' else s.en_analyse  # 分析

                last_dict['spike_charge'] = s.spike_charge  # 尖峰充电量
                last_dict['spike_discharge'] = s.spike_discharge  # 尖峰放电量
                last_dict['peak_charge'] = s.peak_charge  # 峰时充电量
                last_dict['peak_discharge'] = s.peak_discharge  # 峰时放电量

                last_dict['flat_charge'] = s.flat_charge  # 平时充电量
                last_dict['flat_discharge'] = s.flat_discharge  # 平时放电量

                last_dict['valley_charge'] = s.valley_charge  # 谷时充电量
                last_dict['valley_discharge'] = s.valley_discharge  # 谷时放电量

                last_dict['dvalley_charge'] = s.dvalley_charge  # 深谷时充电量
                last_dict['dvalley_discharge'] = s.dvalley_discharge  # 深谷时放电量

                if last_dict != {}:
                    temp_list.append(last_dict)

        return temp_list

    def week_report(self, e_time, esunday, master_stations, page, page_size, s_time, smonday, lang='zh'):

        # 单独起1个线程缓存需要下载的数据
        user_id = self.request.user['user_id']
        s = threading.Thread(target=upload_week_report_for_download, args=(e_time, esunday, master_stations, s_time,
                                                                           smonday, user_id, lang))
        s.start()

        last_list = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            futures = list()
            for master_station in master_stations:
                future = executor.submit(self.deal_one_report, master_station, smonday, esunday, lang)
                futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                data_list_ = future.result()
                if data_list_:
                    last_list += data_list_

        total_pages = math.ceil(len(last_list) / page_size)
        start_index = (page - 1) * page_size
        end_index = page * page_size if page < total_pages else len(last_list) + 1
        last_list_ = last_list[start_index: end_index]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": last_list_, 'total': len(last_list)},
            }
        )

    def deal_one_report_for_download(self, master_station, smonday, esunday, lang='zh'):
        execl_data_list = []
        execl_data_2_list = []
        execl_data_3_list = []

        running_reports = RunningReport.objects.filter(station_name=master_station.name, report_type=2,
                                                       datetime__gte=smonday, datetime_end__lte=esunday).order_by(
            'datetime')
        if running_reports:
            for s in running_reports:
                execl_data = {}
                execl_data['开始日期'] = s.datetime
                execl_data['结束日期'] = s.datetime_end
                execl_data['项目名称'] = master_station.name

                execl_data['充电量(kWh)'] = s.charge_cap
                execl_data['放电量(kWh)'] = s.discharge_cap
                execl_data['充放电效率(%)'] = s.comp_rate
                execl_data['充放电次数'] = s.count

                stations_ids = [s.id for s in master_station.stationdetails_set.filter(is_delete=0).all()]
                # 故障告警
                ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=stations_ids,
                                                       start_time__gte=s.datetime, end_time__lte=s.datetime_end)
                alarm_ins = (
                    ins.filter(type__in=[1, 2]).values("status", "type", "start_time", "end_time", "details",
                                                  "id",
                                                  "note", "station_id", "device")
                    .order_by("-start_time"))
                for alarm in alarm_ins:
                    station = models.StationDetails.objects.filter(id=alarm['station_id']).first()
                    execl_data_2 = {}
                    execl_data_2['序号'] = alarm['id']
                    execl_data_2['项目名称'] = station.station_name if station else '--'
                    execl_data_2['设备名称'] = alarm["device"]
                    execl_data_2['报文'] = ALARM_ZH_EN_MAP[alarm['details'].strip()][lang]
                    if alarm["type"] == 0:
                        execl_data_2['告警分类'] = '告警' if lang == 'zh' else 'Alarm'
                    elif alarm["type"] == 1:
                        execl_data_2['告警分类'] = '故障' if lang == 'zh' else 'Fault'
                    elif alarm["type"] == 2:
                        execl_data_2['告警分类'] = '报警' if lang == 'zh' else 'Warning'
                    alarm["start_time"] = alarm["start_time"].strftime('%Y-%m-%d %H:%M:%S')
                    alarm["end_time"] = alarm["end_time"].strftime('%Y-%m-%d %H:%M:%S')
                    execl_data_2['开始时间'] = alarm["start_time"]
                    execl_data_2['结束时间'] = alarm["end_time"]

                    execl_data_2_list.append(execl_data_2)
                    alarm["details"] = (station.station_name if station else '--') + ":" + alarm["device"] + ' ' + ALARM_ZH_EN_MAP[alarm["details"].strip()][lang]

                # 统计 type = 0的告警数量
                # alarm_count = ins.filter(type=2).count()
                # # 统计 type = 1的故障数量
                # fault_count = ins.filter(type=1).count()
                # # 统计 type = 2的报警数量
                # # warning_count = ins.filter(type=2).count()
                # # alarm_count_ = alarm_count + fault_count

                execl_data['尖时充电量（kWh）'] = s.spike_charge
                execl_data['峰时充电量（kWh）'] = s.peak_charge
                execl_data['平时充电量（kWh）'] = s.flat_charge
                execl_data['谷时充电量（kWh）'] = s.valley_charge
                execl_data['深谷时充电量（kWh）'] = s.dvalley_charge
                execl_data['尖时放电量（kWh）'] = s.spike_discharge
                execl_data['峰时放电量（kWh）'] = s.peak_discharge
                execl_data['平时放电量（kWh）'] = s.flat_discharge
                execl_data['谷时放电量（kWh）'] = s.valley_discharge
                execl_data['深谷时放电量（kWh）'] = s.dvalley_discharge

                execl_data['基准充电量(kWh)'] = s.theory_charge
                execl_data['基准充电量完成率(%)'] = s.theory_charge_comp_rate
                execl_data['基准放电量(kWh)'] = s.theory_discharge
                execl_data['基准放电量完成率(%)'] = s.theory_discharge_comp_rate
                # execl_data['基准充电量完成率(%)'] = last_dict['cumulative_charge_disg_efficiency']
                execl_data['累计充电量（kWh）'] = s.accu_charge
                execl_data['累计放电量（kWh）'] = s.accu_discharge
                execl_data['累计充放电效率（%）'] = s.effic
                execl_data['收益（元）'] = s.income
                execl_data['故障告警数量'] = s.fault + s.alarm  # 故障告警数量
                execl_data['分析'] = s.analyse if lang == 'zh' else s.en_analyse

                execl_data_list.append(execl_data)

                time_list = dateToDataList(s.datetime, s.datetime_end)

                for ll in time_list:
                    execl_data_3 = {}
                    day_dict = {}
                    execl_data_3['日期'] = ll
                    execl_data_3['并网点名称'] = master_station.name
                    day_dict['time'] = ll
                    day_dict['daily_chag_amount'] = 0  # 充电量
                    day_dict['daily_disg_amount'] = 0  # 放电量
                    day_dict['charge_disg_ratio'] = 0
                    day_dict['spike_charge'] = 0  # 尖峰充电量
                    day_dict['spike_discharge'] = 0  # 尖峰放电量
                    day_dict['peak_charge'] = 0  # 峰时充电量
                    day_dict['peak_discharge'] = 0  # 峰时放电量
                    day_dict['flat_charge'] = 0  # 平时充电量
                    day_dict['flat_discharge'] = 0  # 平时放电量
                    day_dict['valley_charge'] = 0  # 谷时充电量
                    day_dict['valley_discharge'] = 0  # 谷时放电量
                    day_dict['dvalley_charge'] = 0  # 深谷时充电量
                    day_dict['dvalley_discharge'] = 0  # 深谷时放电量
                    station_insss = RunningReport.objects.filter(station_name=master_station.name, datetime=ll,
                                                                 report_type=1).order_by('id')
                    if station_insss:
                        for s in station_insss:
                            # day_dict['id'] = s.id  #
                            day_dict['daily_chag_amount'] = s.charge_cap  # 充电量
                            day_dict['daily_disg_amount'] = s.discharge_cap  # 放电量
                            day_dict['charge_disg_ratio'] = 0
                            if day_dict['daily_chag_amount']:
                                if day_dict['daily_chag_amount'] == 0:
                                    day_dict['charge_disg_ratio'] = 0
                                else:
                                    day_dict['charge_disg_ratio'] = float('%.2f' % (
                                            (day_dict['daily_disg_amount'] / day_dict[
                                                'daily_chag_amount']) * 100))

                            execl_data_3['充电量(kWh)'] = day_dict['daily_chag_amount']
                            execl_data_3['放电量(kWh)'] = day_dict['daily_disg_amount']
                            execl_data_3['充放电量完成率(%)'] = day_dict['charge_disg_ratio']

                            day_dict['spike_charge'] = s.spike_charge  # 尖峰充电量
                            day_dict['spike_discharge'] = s.spike_discharge  # 尖峰放电量
                            day_dict['peak_charge'] = s.peak_charge  # 峰时充电量
                            day_dict['peak_discharge'] = s.peak_discharge  # 峰时放电量
                            day_dict['flat_charge'] = s.flat_charge  # 平时充电量
                            day_dict['flat_discharge'] = s.flat_discharge  # 平时放电量
                            day_dict['valley_charge'] = s.valley_charge  # 谷时充电量
                            day_dict['valley_discharge'] = s.valley_discharge  # 谷时放电量

                            day_dict['dvalley_charge'] = s.dvalley_charge  # 深谷时充电量
                            day_dict['dvalley_discharge'] = s.dvalley_discharge  # 深谷时放电量

                            execl_data_3['基准充电量(kWh)'] = s.theory_charge  # 逐日基准充电量
                            execl_data_3['基准充电量完成率(%)'] = s.theory_charge_comp_rate  # 基准充电量完成率
                            execl_data_3['基准放电量(kWh)'] = s.theory_discharge  # 逐日基准放电量
                            execl_data_3['基准放电量完成率(%)'] = s.theory_discharge_comp_rate  # 基准放电量完成率

                            execl_data_3['尖时充电量（kWh）'] = day_dict['spike_charge']
                            execl_data_3['峰时充电量（kWh）'] = day_dict['peak_charge']
                            execl_data_3['平时充电量（kWh）'] = day_dict['flat_charge']
                            execl_data_3['谷时充电量（kWh）'] = day_dict['valley_charge']
                            execl_data_3['深谷时充电量（kWh）'] = day_dict['dvalley_charge']
                            execl_data_3['尖时放电量（kWh）'] = day_dict['spike_discharge']
                            execl_data_3['峰时放电量（kWh）'] = day_dict['peak_discharge']
                            execl_data_3['平时放电量（kWh）'] = day_dict['flat_discharge']
                            execl_data_3['谷时放电量（kWh）'] = day_dict['valley_discharge']
                            execl_data_3['深谷时放电量（kWh）'] = day_dict['dvalley_discharge']

                            execl_data_3['收益（元）'] = s.income  # 当日收益
                            execl_data_3['故障告警数量'] = s.fault + s.alarm

                            execl_data_3_list.append(execl_data_3)

        return execl_data_list, execl_data_2_list, execl_data_3_list

    def week_report_for_download(self, e_time, esunday, master_stations, s_time, smonday, lang='zh'):

        user_id = self.request.user.get("user_id")

        if lang == 'zh':

            # 优先从 redis 中取缓存数据
            # key = f"tianlu_{start_time}-{end_time}天禄运行月报_{user_id}"
            key = f"tianlu_{s_time}-{e_time}天禄运行周报报_{user_id}"

            redis_conn = get_redis_connection("default")
            # 先从redis获取数据
            try:
                export_url = redis_conn.get(key)

                if export_url:
                    return Response(
                        {
                            "code": common_response_code.SUCCESS,
                            "data": {"message": f"success1", "detail": export_url},
                        }
                    )

            except Exception as e:
                print(traceback.print_exc())
                pass

            execl_data_111 = {"开始日期": "开始日期", "结束日期": "结束日期", "项目名称": "项目名称", "充电量(kWh)": "充电量(kWh)",
                              "放电量(kWh)": "放电量(kWh)", "充放电效率(%)": "充放电效率(%)", "充放电次数": "充放电次数", "尖时充电量（kWh）": "尖时充电量（kWh）",
                              "峰时充电量（kWh）": "峰时充电量（kWh）", "平时充电量（kWh）": "平时充电量（kWh）", "谷时充电量（kWh）": "谷时充电量（kWh）",
                              "深谷时充电量（kWh）": "深谷时充电量（kWh）",
                              "尖时放电量（kWh）": "尖时放电量（kWh）", "峰时放电量（kWh）": "峰时放电量（kWh）", "平时放电量（kWh）": "平时放电量（kWh）",
                              "谷时放电量（kWh）": "谷时放电量（kWh）", "深谷时放电量（kWh）": "深谷时放电量（kWh）",
                              "基准充电量(kWh)": "基准充电量(kWh)", "基准充电量完成率(%)": "基准充电量完成率(%)",
                              "基准放电量(kWh)": "基准放电量(kWh)", "基准放电量完成率(%)": "基准放电量完成率(%)",
                              "累计充电量（kWh）": "累计充电量（kWh）", "累计放电量（kWh）": "累计放电量（kWh）", "累计充放电效率（%）": "累计充放电效率（%）",
                              "收益（元）": "收益（元）", "故障告警数量": "故障告警数量", "分析内容": "分析内容"}
            execl_data_333 = {"日期": "日期", "并网点名称": "并网点名称", "充电量(kWh)": "充电量(kWh)", "放电量(kWh)": "放电量(kWh)",
                              "充放电效率(%)": "充放电效率(%)", "基准充电量(kWh)": "基准充电量(kWh)", "基准充电量完成率(%)": "基准充电量完成率(%)",
                              "基准放电量(kWh)": "基准放电量(kWh)", "基准放电量完成率(%)": "基准放电量完成率(%)",
                              "尖时充电量（kWh）": "尖时充电量（kWh）", "峰时充电量（kWh）": "峰时充电量（kWh）", "平时充电量（kWh）": "平时充电量（kWh）",
                              "谷时充电量（kWh）": "谷时充电量（kWh）", "深谷时充电量（kWh）": "深谷时充电量（kWh）","尖时放电量（kWh）": "尖时放电量（kWh）",
                              "峰时放电量（kWh）": "峰时放电量（kWh）",
                              "平时放电量（kWh）": "平时放电量（kWh）", "谷时放电量（kWh）": "谷时放电量（kWh）", "深谷时放电量（kWh）": "深谷时放电量（kWh）",
                              "收益（元）": "收益（元）", "故障告警数量": "故障告警数量"}
            execl_data_222 = {"序号": "序号", "并网点名称": "并网点名称", "设备名称": "设备名称", "报文": "报文", "告警分类": "告警分类", "开始时间": "开始时间",
                              "结束时间": "结束时间"}
            execl_data_list = []
            execl_data_2_list = []
            execl_data_3_list = []
            execl_data_list.append(execl_data_111)
            execl_data_2_list.append(execl_data_222)
            execl_data_3_list.append(execl_data_333)

            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                futures = list()
                for master_station in master_stations:
                    future = executor.submit(self.deal_one_report_for_download, master_station, smonday, esunday, lang)
                    futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    execl_data_list_, execl_data_2_list_, execl_data_3_list_ = future.result()
                    if execl_data_list_:
                        execl_data_list += execl_data_list_
                    if execl_data_2_list_:
                        execl_data_2_list += execl_data_2_list_
                    if execl_data_3_list_:
                        execl_data_3_list += execl_data_3_list_

            user_id = self.request.user.get('user_id')
            file_name = f"{s_time}~{e_time}天禄运行周报_{user_id}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            workbook = openpyxl.Workbook()
            data = {
                "运行数据": execl_data_list,
                "逐日运行数据": execl_data_3_list,
                "故障告警记录": execl_data_2_list,
            }
            # 遍历字典，将数据写入不同的sheet
            for sheet_name, sheet_data in data.items():
                sheet = workbook.create_sheet(title=sheet_name)
                for index, row_data in enumerate(sheet_data, start=1):
                    for col_num, cell_value in enumerate(row_data.values(), start=1):
                        cell = sheet.cell(row=index, column=col_num)
                        cell.value = cell_value
            # 删除sheet页
            del workbook['Sheet']
            # 保存工作簿到文件夹
            workbook.save(path)

        else:
            # 优先从 redis 中取缓存数据
            # key = f"tianlu_{start_time}-{end_time}天禄运行月报_{user_id}"
            key = f"tianlu_{s_time}-{e_time}_Operation_Weekly_Report_{user_id}"

            redis_conn = get_redis_connection("default")
            # 先从redis获取数据
            try:
                export_url = redis_conn.get(key)

                if export_url:
                    return Response(
                        {
                            "code": common_response_code.SUCCESS,
                            "data": {"message": f"success1", "detail": export_url},
                        }
                    )

            except Exception as e:
                print(traceback.print_exc())
                pass

            execl_data_111 = {"开始日期": "From", "结束日期": "To", "项目名称": "Installation",
                              "充电量(kWh)": "Energy Charged(kWh)",
                              "放电量(kWh)": "Energy Discharged(kWh)", "充放电效率(%)": "Efficiency(%)",
                              "充放电次数": "Energy Charge and Discharge Cycles",
                              "尖时充电量（kWh）": "Peak-hour Energy Charged(kWh)",
                              "峰时充电量（kWh）": "Shoulder-hour Energy Charged(kWh)",
                              "平时充电量（kWh）": "Off-peak-hour Energy Charged(kWh)",
                              "谷时充电量（kWh）": "Valley-hour Energy Charged(kWh)",
                              "深谷时充电量（kWh）": "Deep Valley-hour Energy Charged(kWh)",
                              "尖时放电量（kWh）": "Peak-hour Energy Discharged(kWh)",
                              "峰时放电量（kWh）": "Shoulder-hour Energy Discharged(kWh)",
                              "平时放电量（kWh）": "Off-peak-hour Energy Discharged(kWh)",
                              "谷时放电量（kWh）": "Valley-hour Energy Discharged(kWh)",
                              "深谷时放电量（kWh）": "Deep Valley-hour Energy Discharged(kWh)",
                              "基准充电量(kWh)": "Reference Energy Charged(kWh)",
                              "基准充电量完成率(%)": "Reference Energy Charged Completion Rate(%)",
                              "基准放电量(kWh)": "Reference Energy Disharged(kWh)",
                              "基准放电量完成率(%)": "Reference Energy Disharged Completion Rate(%)",
                              "累计充电量（kWh）": "Cumulative Energy Charged(kWh)",
                              "累计放电量（kWh）": "Cumulative Energy Discharged(kWh)",
                              "累计充放电效率（%）": "Cumulative Energy Charged and Discharged Efficiency(%)",
                              "收益（元）": "Profit(Yuan)",
                              "故障告警数量": "Number of Fault Alarms",
                              "分析内容": "Analysis"}
            execl_data_333 = {"日期": "Date", "并网点名称": "Installation", "充电量(kWh)": "Energy Charged(kWh)",
                              "放电量(kWh)": "Energy Discharged(kWh)",
                              "充放电效率(%)": "Efficiency(%)", "基准充电量(kWh)": "Reference Energy Charged(kWh)",
                              "基准充电量完成率(%)": "Reference Energy Charged Completion Rate(%)",
                              "基准放电量(kWh)": "Reference Energy Disharged(kWh)",
                              "基准放电量完成率(%)": "Reference Energy Disharged Completion Rate(%)",
                              "尖时充电量（kWh）": "Peak-hour Energy Charged(kWh)",
                              "峰时充电量（kWh）": "Shoulder-hour Energy Charged(kWh)",
                              "平时充电量（kWh）": "Off-peak-hour Energy Charged(kWh)",
                              "谷时充电量（kWh）": "Valley-hour Energy Charged(kWh)",
                              "深谷时充电量（kWh）": "Deep Valley-hour Energy Charged(kWh)",
                              "尖时放电量（kWh）": "Peak-hour Energy Discharged(kWh)",
                              "峰时放电量（kWh）": "Shoulder-hour Energy Discharged(kWh)",
                              "平时放电量（kWh）": "Off-peak-hour Energy Discharged(kWh)",
                              "谷时放电量（kWh）": "Valley-hour Energy Discharged(kWh)",
                              "深谷时放电量（kWh）": "Deep Valley-hour Energy Discharged(kWh)",
                              "收益（元）": "Profit(Yuan)",
                              "故障告警数量": "Number of Fault Alarms"}
            execl_data_222 = {"序号": "Number", "日期": "Date", "项目名称": "Installation", "设备名称": "Device",
                              "报文": "Message", "告警分类": "Alarm Types", "开始时间": "From", "结束时间": "To"}
            execl_data_list = []
            execl_data_2_list = []
            execl_data_3_list = []
            execl_data_list.append(execl_data_111)
            execl_data_2_list.append(execl_data_222)
            execl_data_3_list.append(execl_data_333)

            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                futures = list()
                for master_station in master_stations:
                    future = executor.submit(self.deal_one_report_for_download, master_station, smonday, esunday, lang)
                    futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    execl_data_list_, execl_data_2_list_, execl_data_3_list_ = future.result()
                    if execl_data_list_:
                        execl_data_list += execl_data_list_
                    if execl_data_2_list_:
                        execl_data_2_list += execl_data_2_list_
                    if execl_data_3_list_:
                        execl_data_3_list += execl_data_3_list_

            user_id = self.request.user.get('user_id')
            file_name = f"{s_time}~{e_time}_Tianlu_Operation_Weekly_Report_{user_id}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            workbook = openpyxl.Workbook()
            data = {
                "Operational Data": execl_data_list,
                "Daily Operational Data": execl_data_3_list,
                "Fault Alarm Record": execl_data_2_list,
            }
            # 遍历字典，将数据写入不同的sheet
            for sheet_name, sheet_data in data.items():
                sheet = workbook.create_sheet(title=sheet_name)
                for index, row_data in enumerate(sheet_data, start=1):
                    for col_num, cell_value in enumerate(row_data.values(), start=1):
                        cell = sheet.cell(row=index, column=col_num)
                        cell.value = cell_value
            # 删除sheet页
            del workbook['Sheet']
            # 保存工作簿到文件夹
            workbook.save(path)

        # 上传至minio
        try:
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            url = minio_client.upload_local_file(file_name, path, bucket_name='download')
            os.remove(path)

        except Exception as e:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": f"上传文件报错：{e}" if lang == 'zh' else 'Upload file error.'},
            })

        # 缓存下载链接到redis
        # key = 'tianlu_' + file_name.split('.')[0].replace('~', '-')
        redis_conn = get_redis_connection("default")
        redis_conn.set(key, url, 60 * 5)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": url},
            }
        )


class ReportWeekDayViews(APIView):
    """
    运行周报--逐日充放电量
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        report_id = request.query_params.get('report_id')

        try:
            report = RunningReport.objects.filter(id=int(report_id)).first()
        except Exception as e:
            error_log.error(f"运行报告:参数错误 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行报告:参数<report_id>错误' if lang == 'zh' else 'Running report:Parameter <report_id> error.'},
                }
            )

        if report and report.report_type == 2:

            detail = []
            time_list = dateToDataList(report.datetime, report.datetime_end)

            for ll in time_list:
                day_dict = {'time': ll, 'daily_chag_amount': 0, 'daily_disg_amount': 0, 'charge_disg_ratio': 0,
                            'spike_charge': 0, 'spike_discharge': 0, 'peak_charge': 0, 'peak_discharge': 0,
                            'flat_charge': 0, 'flat_discharge': 0, 'valley_charge': 0, 'valley_discharge': 0,
                            'dvalley_charge': 0, 'dvalley_discharge': 0}
                day_reports = RunningReport.objects.filter(station_name=report.station_name, datetime=ll,
                                                             report_type=1).order_by('datetime')
                if day_reports:
                    for s in day_reports:
                        # day_dict['id'] = s.id  #
                        day_dict['daily_chag_amount'] = s.charge_cap  # 充电量
                        day_dict['daily_disg_amount'] = s.discharge_cap  # 放电量
                        day_dict['charge_disg_ratio'] = 0
                        if day_dict['daily_chag_amount']:
                            if day_dict['daily_chag_amount'] == 0:
                                day_dict['charge_disg_ratio'] = 0
                            else:
                                day_dict['charge_disg_ratio'] = float('%.2f' % (
                                        (day_dict['daily_disg_amount'] / day_dict[
                                            'daily_chag_amount']) * 100))

                        day_dict['spike_charge'] = s.spike_charge  # 尖峰充电量
                        day_dict['spike_discharge'] = s.spike_discharge  # 尖峰放电量
                        day_dict['peak_charge'] = s.peak_charge  # 峰时充电量
                        day_dict['peak_discharge'] = s.peak_discharge  # 峰时放电量
                        day_dict['flat_charge'] = s.flat_charge  # 平时充电量
                        day_dict['flat_discharge'] = s.flat_discharge  # 平时放电量
                        day_dict['valley_charge'] = s.valley_charge  # 谷时充电量
                        day_dict['valley_discharge'] = s.valley_discharge  # 谷时放电量

                        day_dict['dvalley_charge'] = s.dvalley_charge  # 深谷时充电量
                        day_dict['dvalley_discharge'] = s.dvalley_discharge  # 深谷时放电量
                    detail.append(day_dict)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": detail},
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '运行周报:参数<report_id>错误或者不是正确的周报ID' if lang == 'zh'
                    else 'Running report:Parameter <report_id> error or not the correct week report ID.'},
                }
            )


def dateToDataList(start, end):
    # 计算时间段内的时间列表,包含首位
    datestart = datetime.datetime.strptime(start[0:10], '%Y-%m-%d')
    dateend = datetime.datetime.strptime(end[0:10], '%Y-%m-%d')
    data_list = list()
    while datestart <= dateend:
        data_list.append(datestart.strftime('%Y-%m-%d'))
        datestart += datetime.timedelta(days=1)
    return data_list


def get_week_start_end(date_str):
    date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    weekday = date_obj.weekday()
    start_of_week = date_obj - timedelta(days=weekday)
    end_of_week = start_of_week + timedelta(days=6)
    return start_of_week.strftime('%Y-%m-%d'), end_of_week.strftime('%Y-%m-%d')
































