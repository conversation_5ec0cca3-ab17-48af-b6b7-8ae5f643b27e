import datetime
import json
import logging

from django_redis import get_redis_connection

from apis.user import models
from apis.user.models import UserDetails
from tools.aly_send_smscode import Sample
from tools.send_mail import sendMail_

logger = logging.getLogger('fault')
redis_conn = get_redis_connection("default")

class CheckFault(object):

    def __int__(self):
        pass

    @staticmethod
    def get_email_receivers(station):
        receivers = UserDetails.objects.filter(is_send_email=1, stations=station, email__isnull=False).all()
        if receivers.exists():
            return [receiver.email for receiver in receivers]
        else:
            return []

    @staticmethod
    def get_message_receivers(station):
        receivers = UserDetails.objects.filter(is_send_message=1, stations=station, mobile__isnull=False).all()
        if receivers.exists():
            return [receiver.mobile for receiver in receivers]
        else:
            return []

    def t_check_alarm(self, five_minutes_ago):
        alarms = models.FaultAlarm.objects.using('alarm_module').filter(type__in=[2, 0], status=0, start_time__gt=five_minutes_ago,
                                                  end_time__isnull=True).all()
        if alarms.exists():
            # receiver_emails = self.get_email_receivers()
            # if not receiver_emails:
            #     logger.error('未配置故障告警邮箱收件人邮箱！！！')
            #     return
            for alarm in alarms:
                station = models.StationDetails.objects.filter(id=alarm.station_id).first()
                if station and models.FaultAlarm.setting_dic[alarm.point].get("is_send_email"):
                    receiver_emails = self.get_email_receivers(station)
                    if not receiver_emails:
                        logger.error('未配置故障告警邮箱收件人邮箱！！！')
                        continue
                    has_send_email_tag = redis_conn.get(str(alarm.joint_primary_key) + 'send_email')
                    if not has_send_email_tag:
                        type_ = '警告' if alarm.type == 0 else '报警'
                        message = (f"{type_}时间: {alarm.start_time}\n{type_}设备: {alarm.device_another_name}\n{type_}点位: "
                                   f"{alarm.point},\n{type_}详情: {alarm.details},\n当前状态：{alarm.note}\n")
                        Subject = f'{datetime.datetime.today().strftime("%Y-%m-%d")}来自{station.station_name}的报修消息'
                        sender_show = "白泽添禄报警邮件"
                        recipient_show = "白泽添禄报警邮件"
                        try:
                            sendMail_(message, Subject, sender_show, recipient_show, receiver_emails)
                            logger.debug(f"邮件: {Subject} ===>发送成功")
                            redis_conn.set(str(alarm.joint_primary_key) + 'send_email', "1", 300)
                        except Exception as e:
                            logger.error(f"邮件: {Subject} ===>发送失败")


    def t_check_fault(self, five_minutes_ago):
        # now = datetime.datetime.now()
        # five_minutes_ago = now - datetime.timedelta(minutes=5)
        faults = models.FaultAlarm.objects.using('alarm_module').filter(type=1, status=0, start_time__gt=five_minutes_ago,
                                                  end_time__isnull=True).all()
        if faults.exists():

            for fault in faults:
                station = models.StationDetails.objects.filter(id=fault.station_id).first()
                if station:
                    receiver_mobiles = self.get_message_receivers(station)
                    receiver_emails = self.get_email_receivers(station)
                    if receiver_mobiles and models.FaultAlarm.setting_dic[fault.point].get("is_send_message"):
                        has_send_message_tag = redis_conn.get(str(fault.joint_primary_key) + 'send_message')
                        if not has_send_message_tag:
                            message_dict = {
                                "station": station.station_name, "time": fault.start_time.strftime('%Y-%m-%d %H:%M:%S'), "point": fault.point,
                                "detail": fault.device_another_name + ' ' + fault.details, "status": fault.note
                            }
                            for phone in receiver_mobiles:
                                try:
                                    code = Sample.main(phone, 00, msg=json.dumps(message_dict))
                                    if code != 200:
                                        logger.error("故障告知短信下发失败")
                                    else:
                                        logger.debug("故障告知短信下发成功")
                                except Exception as e:
                                    logger.error(f"故障告知短信下发失败: {e}")
                            redis_conn.set(str(fault.joint_primary_key) + 'send_message', "1", 300)
                    else:
                        logger.info('不需要发送短信或者未配置故障告警短信收件人手机号！！！')

                    if receiver_emails and models.FaultAlarm.setting_dic[fault.point].get("is_send_email"):
                        has_send_email_tag = redis_conn.get(str(fault.joint_primary_key) + 'send_email')
                        if not has_send_email_tag:
                            type_ = '故障'
                            message = (f"{type_}时间: {fault.start_time}\n{type_}设备: {fault.device_another_name}\n{type_}点位: "
                                       f"{fault.point},\n{type_}详情: {fault.details},\n当前状态：{fault.note}\n")
                            Subject = f'{datetime.datetime.today().strftime("%Y-%m-%d")}来自{station.station_name}的报修消息'
                            sender_show = "白泽添禄报警邮件"
                            recipient_show = "白泽添禄报警邮件"
                            try:
                                sendMail_(message, Subject, sender_show, recipient_show, receiver_emails)
                                logger.debug(f"邮件: {Subject} ===>发送成功")
                                redis_conn.set(str(fault.joint_primary_key) + 'send_email', "1", 300)
                            except Exception as e:
                                logger.error(f"邮件: {e} ===>发送失败!!!!!")
                                logger.error(f"邮件: {Subject} ===>发送失败")

                    else:
                        logger.error('未配置故障告警邮箱收件人邮箱！！！')

    def run(self, five_minutes_ago):
        self.t_check_alarm(five_minutes_ago)
        self.t_check_fault(five_minutes_ago)
