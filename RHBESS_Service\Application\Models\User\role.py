#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Application\Models\User\role.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-17 09:47:17


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class Role(user_Base):
    u'角色表'
    __tablename__ = "c_user_role"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    descr = Column(String(256), nullable=False, comment=u"描述")
    station_id = Column(Text, nullable=True, comment=u"所属站")
    authority = Column(Text, nullable=True, comment=u"所拥有的权限")
    select_key = Column(Text, nullable=True, comment=u"所拥选中的key")
    en_descr = Column(String(256), nullable=False, comment=u"描述-英文")
    en_authority = Column(String(256), nullable=False, comment=u"所拥有的权限-英文")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(Role(id=1,descr='超级管理员'))
        user_session.commit()
        user_session.close()

    def __repr__(self):
        return "{'id':%s,'descr':'%s','en_descr':'%s'}" %(self.id,self.descr,self.en_descr)

   
    def delete_role(self,id):
        from Application.Models.User.user import User
        from Application.Models.User.role_authority import RoleAuthority
        try:
            users = user_session.query(User).filter(User.user_role_id== id).all()
            for user in users:
                user.deleteUser(user.id)
            user_session.query(RoleAuthority).filter(RoleAuthority.role_id== id).delete()
            user_session.query(Role).filter(Role.id== id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}