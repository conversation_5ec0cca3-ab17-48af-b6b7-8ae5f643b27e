import openpyxl
from openpyxl.styles import Alignment, Font, Side, Border


def generate_excel(return_dic, excel_file, lang='zh'):

    # 创建一个工作簿
    workbook = openpyxl.Workbook()

    # 选择活动工作表
    sheet = workbook.active
    sheet.title = '削峰填谷收益' if lang == 'zh' else 'Peak Shaving Profit'

    # 合并A1到B2的单元格
    sheet.merge_cells('A1:J1')

    # 合并A1到B2的单元格
    sheet.merge_cells('B2:F2')
    sheet.merge_cells('H2:J2')

    sheet.merge_cells('B3:F3')
    sheet.merge_cells('H3:J3')

    sheet.merge_cells('B4:F4')
    sheet.merge_cells('H4:J4')

    # 在A1单元格中写入固定数据
    if lang == 'zh':
        sheet['A1'] = '天禄智控运营管理系统'
        sheet['A2'] = '项目名称'
        sheet['A3'] = '项目地址'
        sheet['A4'] = '电站编号'
        sheet['G2'] = '结算月份'
        sheet['G3'] = '结算开始时间'
        sheet['G4'] = '结算结束时间'

        sheet['A5'] = '并网点名称'
        sheet['B5'] = '电表编号'
        sheet['C5'] = '计量类型'
        sheet['D5'] = '峰谷标识'
        sheet['E5'] = '电表倍率'
        sheet['F5'] = '上次抄表底码'
        sheet['G5'] = '本次抄表底码'
        sheet['H5'] = '结算电量 (kWh)'
        sheet['I5'] = '结算电价 (元/kWh)'
        sheet['J5'] = '结算金额 (元)'
    else:
        sheet['A1'] = 'C&I ESS Operation Online Portal'
        sheet['A2'] = 'Project'
        sheet['A3'] = 'Address'
        sheet['A4'] = 'Project ID'
        sheet['G2'] = 'Month'
        sheet['G3'] = 'From'
        sheet['G4'] = 'To'

        sheet['A5'] = 'Installation'
        sheet['B5'] = 'Meter ID'
        sheet['C5'] = 'Metering Type'
        sheet['D5'] = 'Identification'
        sheet['E5'] = 'Multiplier'
        sheet['F5'] = 'Last Reading'
        sheet['G5'] = 'Current Reading'
        sheet['H5'] = 'Settlement Energy(kWh)'
        sheet['I5'] = 'Settlement Price(￥/kWh)'
        sheet['J5'] = 'Settlement Amount(￥)'

    # 写入数据
    sheet['B2'] = return_dic['project']
    sheet['B3'] = return_dic['address']
    sheet['B4'] = return_dic['station_number']
    sheet['H2'] = return_dic['account_month']
    sheet['H3'] = return_dic['account_start']
    sheet['H4'] = return_dic['account_end']

    n = 0
    m = 10
    for index, station in enumerate(sorted(return_dic['stations_info'], key=lambda x: x['station_name'])):
        n = index
        for row in range(6 + n + n * m, 6 + m + 1 + n * m):
            sheet.cell(row=row, column=1)
            sheet.cell(row=row, column=2)
            sheet.cell(row=row, column=3)
            sheet.cell(row=row, column=4)
            sheet.cell(row=row, column=5)
            sheet.cell(row=row, column=6)
            sheet.cell(row=row, column=7)
            sheet.cell(row=row, column=8)
            sheet.cell(row=row, column=9)
            sheet.cell(row=row, column=10)

        # 并网点名称
        sheet[f'A{6 + n + n * m}'] = station['station_name']
        # sheet.merge_cells(f'A{6 + n * m}:A{6 + m + n * m}')

        # 电表编号
        sheet[f'B{6 + n + n * m}'] = station['station_number']
        # sheet.merge_cells(f'B{6 + n * m}:B{6 + m + n * m}')

        # 计量类型
        sheet[f'C{6 + n + n * m}'] = '反向有功（充电）' if lang == 'zh' else 'Charging'
        # sheet.merge_cells(f'C{6 + n * m}:C{6 + 3 + n * m}')

        sheet[f'C{6 + n + 5 + n * m}'] = '正向有功（放电）' if lang == 'zh' else 'Discharging'
        # sheet.merge_cells(f'C{6 + 4 + n * m}:C{6 + 7 + n * m}')

        # 电表倍率
        sheet[f'E{6 + n + n * m}'] = station['rate']
        # sheet.merge_cells(f'E{6 + n * m}:E{6 + 7 + n * m}')

        # 结算电价
        sheet[f'I{6 + n + n * m}'] = station['account_data']['price_url']
        # sheet.merge_cells(f'I{6 + n * m}:I{6 + 7 + n * m}')

        # 削峰填谷总收益
        sheet[f'C{6 + n + m + n * m}'] = '削峰填谷收益' if lang == 'zh' else 'Peak Shaving Profit'
        # sheet.merge_cells(f'C{6 + m + n * m}:E{6 + m + n * m}')

        sheet[f'F{6 + n + m + n * m}'] = station['account_data']['total_income']
        # sheet.merge_cells(f'G{6 + m + n * m}:I{6 + m + n * m}')

        sheet[f'J{6 + n + m + n * m}'] = '元' if lang == 'zh' else 'Yuan'

        # 写入数据
        if lang == 'zh':
            sheet[f'D{6 + n + n * m}'] = '尖'
            sheet[f'D{6 + n + 1 + n * m}'] = '峰'
            sheet[f'D{6 + n + 2 + n * m}'] = '平'
            sheet[f'D{6 + n + 3 + n * m}'] = '谷'
            sheet[f'D{6 + n + 4 + n * m}'] = '深谷'
            sheet[f'D{6 + n + 5 + n * m}'] = '尖'
            sheet[f'D{6 + n + 6 + n * m}'] = '峰'
            sheet[f'D{6 + n + 7 + n * m}'] = '平'
            sheet[f'D{6 + n + 8 + n * m}'] = '谷'
            sheet[f'D{6 + n + 9 + n * m}'] = '深谷'
        else:
            sheet[f'D{6 + n + n * m}'] = 'Rush-hour'
            sheet[f'D{6 + n + 1 + n * m}'] = 'Peak-hour'
            sheet[f'D{6 + n + 2 + n * m}'] = 'Flat-hour'
            sheet[f'D{6 + n + 3 + n * m}'] = 'Valley-hour'
            sheet[f'D{6 + n + 4 + n * m}'] = 'Dvalley-hour'
            sheet[f'D{6 + n + 5 + n * m}'] = 'Rush-hour'
            sheet[f'D{6 + n + 6 + n * m}'] = 'Peak-hour'
            sheet[f'D{6 + n + 7 + n * m}'] = 'Flat-hour'
            sheet[f'D{6 + n + 8 + n * m}'] = 'Valley-hour'
            sheet[f'D{6 + n + 9 + n * m}'] = 'Dvalley-hour'

        sheet[f'F{6 + n + n * m}'] = station['account_data']['charge']['spike']['first']
        sheet[f'F{6 + n + 1 + n * m}'] = station['account_data']['charge']['peak']['first']
        sheet[f'F{6 + n + 2 + n * m}'] = station['account_data']['charge']['flat']['first']
        sheet[f'F{6 + n + 3 + n * m}'] = station['account_data']['charge']['valley']['first']
        sheet[f'F{6 + n + 4 + n * m}'] = station['account_data']['charge']['dvalley']['first']
        sheet[f'F{6 + n + 5 + n * m}'] = station['account_data']['discharge']['spike']['first']
        sheet[f'F{6 + n + 6 + n * m}'] = station['account_data']['discharge']['peak']['first']
        sheet[f'F{6 + n + 7 + n * m}'] = station['account_data']['discharge']['flat']['first']
        sheet[f'F{6 + n + 8 + n * m}'] = station['account_data']['discharge']['valley']['first']
        sheet[f'F{6 + n + 9 + n * m}'] = station['account_data']['discharge']['dvalley']['first']

        sheet[f'G{6 + n + n * m}'] = station['account_data']['charge']['spike']['last']
        sheet[f'G{6 + n + 1 + n * m}'] = station['account_data']['charge']['peak']['last']
        sheet[f'G{6 + n + 2 + n * m}'] = station['account_data']['charge']['flat']['last']
        sheet[f'G{6 + n + 3 + n * m}'] = station['account_data']['charge']['valley']['last']
        sheet[f'G{6 + n + 4 + n * m}'] = station['account_data']['charge']['dvalley']['last']
        sheet[f'G{6 + n + 5 + n * m}'] = station['account_data']['discharge']['spike']['last']
        sheet[f'G{6 + n + 6 + n * m}'] = station['account_data']['discharge']['peak']['last']
        sheet[f'G{6 + n + 7 + n * m}'] = station['account_data']['discharge']['flat']['last']
        sheet[f'G{6 + n + 8 + n * m}'] = station['account_data']['discharge']['valley']['last']
        sheet[f'G{6 + n + 9 + n * m}'] = station['account_data']['discharge']['dvalley']['last']

        sheet[f'H{6 + n + n * m}'] = station['account_data']['charge']['spike']['count']
        sheet[f'H{6 + n + 1 + n * m}'] = station['account_data']['charge']['peak']['count']
        sheet[f'H{6 + n + 2 + n * m}'] = station['account_data']['charge']['flat']['count']
        sheet[f'H{6 + n + 3 + n * m}'] = station['account_data']['charge']['valley']['count']
        sheet[f'H{6 + n + 4 + n * m}'] = station['account_data']['charge']['dvalley']['count']
        sheet[f'H{6 + n + 5 + n * m}'] = station['account_data']['discharge']['spike']['count']
        sheet[f'H{6 + n + 6 + n * m}'] = station['account_data']['discharge']['peak']['count']
        sheet[f'H{6 + n + 7 + n * m}'] = station['account_data']['discharge']['flat']['count']
        sheet[f'H{6 + n + 8 + n * m}'] = station['account_data']['discharge']['valley']['count']
        sheet[f'H{6 + n + 9 + n * m}'] = station['account_data']['discharge']['dvalley']['count']

        sheet[f'J{6 + n + n * m}'] = station['account_data']['charge']['spike']['income']
        sheet[f'J{6 + n + 1 + n * m}'] = station['account_data']['charge']['peak']['income']
        sheet[f'J{6 + n + 2 + n * m}'] = station['account_data']['charge']['flat']['income']
        sheet[f'J{6 + n + 3 + n * m}'] = station['account_data']['charge']['valley']['income']
        sheet[f'J{6 + n + 4 + n * m}'] = station['account_data']['charge']['dvalley']['income']
        sheet[f'J{6 + n + 5 + n * m}'] = station['account_data']['discharge']['spike']['income']
        sheet[f'J{6 + n + 6 + n * m}'] = station['account_data']['discharge']['peak']['income']
        sheet[f'J{6 + n + 7 + n * m}'] = station['account_data']['discharge']['flat']['income']
        sheet[f'J{6 + n + 8 + n * m}'] = station['account_data']['discharge']['valley']['income']
        sheet[f'J{6 + n + 9 + n * m}'] = station['account_data']['discharge']['dvalley']['income']

        # 合并单元格
        sheet.merge_cells(f'A{6 + n + n * m}:A{6 + n + m + n * m}')
        sheet.merge_cells(f'B{6 + n + n * m}:B{6 + n + m + n * m}')
        sheet.merge_cells(f'C{6 + n + n * m}:C{6 + n + 4 + n * m}')
        sheet.merge_cells(f'C{6 + n + 5 + n * m}:C{6 + n + 9 + n * m}')
        sheet.merge_cells(f'E{6 + n + n * m}:E{6 + n + 9 + n * m}')
        sheet.merge_cells(f'I{6 + n + n * m}:I{6 + n + 9 + n * m}')
        sheet.merge_cells(f'C{6 + n + m + n * m}:E{6 + n + m + n * m}')
        sheet.merge_cells(f'F{6 + n + m + n * m}:I{6 + n + m + n * m}')

    # 储能电站削峰填谷总收益
    sheet[f'A{6 + n + m + n * m + 1}'] = '储能电站削峰填谷总收益' if lang == 'zh' else 'Amount for Peak Shaving'
    sheet[f'F{6 + n + m + n * m + 1}'] = return_dic['project_total_income']
    sheet[f'J{6 + n + m + n * m + 1}'] = '元' if lang == 'zh' else 'Yuan'
    sheet.merge_cells(f'A{6 + n + m + n * m + 1}:E{6 + n + m + n * m + 1}')
    sheet.merge_cells(f'F{6 + n + m + n * m + 1}:I{6 + n + m + n * m + 1}')

    sheet[f'A{6 + n + m + n * m + 2}'] = return_dic['note']
    sheet.merge_cells(f'a{6 + n + m + n * m + 2}:J{6 + n + m + n * m + 6}')

    # 设置A1单元格的对齐方式为自动换行
    # sheet['B3'].alignment = Alignment(wrap_text=True)

    # 设置所有单元格居中对齐及自动换行
    for row in sheet.iter_rows(max_row=6 + n + m + n * m + 2 - 1):
        for cell in row:
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            cell.font = Font(size=11)

    sheet[f'A{6 + n + m + n * m + 2}'].alignment = Alignment(horizontal="left", vertical="top", wrap_text=True)

    # 设置边框线
    border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    for row in sheet.iter_rows():
        for cell in row:
            cell.border = border

    for station_info in return_dic['stations_info']:
        if lang == 'zh':
            sheet_s = workbook.create_sheet(f"{station_info['station_name']}-每日充放电量-详情")
            # 在A1单元格中写入固定数据
            sheet_s['A1'] = '并网点名称'
            sheet_s['B1'] = '日期'
            sheet_s['C1'] = '反向有功电量（kWh）'
            sheet_s['I1'] = '正向有功电量（kWh）'

            sheet_s['C2'] = '尖'
            sheet_s['D2'] = '峰'
            sheet_s['E2'] = '平'
            sheet_s['F2'] = '谷'
            sheet_s['G2'] = '深谷'
            sheet_s['H2'] = '总'

            sheet_s['I2'] = '尖'
            sheet_s['J2'] = '峰'
            sheet_s['K2'] = '平'
            sheet_s['L2'] = '谷'
            sheet_s['M2'] = '深谷'
            sheet_s['N2'] = '总'
        else:
            sheet_s = workbook.create_sheet(f"{station_info['station_name']}-Daily Ballance")
            # 在A1单元格中写入固定数据
            sheet_s['A1'] = 'Installation'
            sheet_s['B1'] = 'Date'
            sheet_s['C1'] = 'Charging（kWh）'
            sheet_s['I1'] = 'Discharging（kWh）'

            sheet_s['C2'] = 'Rush-hour'
            sheet_s['D2'] = 'Peak-hour'
            sheet_s['E2'] = 'Flat-hour'
            sheet_s['F2'] = 'Valley-hour'
            sheet_s['G2'] = 'Dvalley-hour'
            sheet_s['H2'] = 'Total'

            sheet_s['I2'] = 'Rush-hour'
            sheet_s['J2'] = 'Peak-hour'
            sheet_s['K2'] = 'Flat-hour'
            sheet_s['L2'] = 'Valley-hour'
            sheet_s['M2'] = 'Dvalley-hour'
            sheet_s['N2'] = 'Total'
        # 合并单元格
        sheet_s.merge_cells('A1:A2')
        sheet_s.merge_cells('B1:B2')
        sheet_s.merge_cells('C1:H1')
        sheet_s.merge_cells('I1:N1')

        # sheet.merge_cells('B4:F4')
        # sheet.merge_cells('H4:J4')

        # # sheet_s 写入标头
        # for col_num, header in enumerate(table2_titles, 1):
        #     col_letter = openpyxl.utils.get_column_letter(col_num)
        #     sheet2[f'{col_letter}1'] = header

        # 准备表数据
        days_data_array = list()
        for t in station_info['days_data']:
            li = [station_info['station_name'], t['date'], t['day_spike_charge'], t['day_peak_charge'], t['day_flat_charge'],
                  t['day_valley_charge'], t['day_dvalley_charge'],
                  t['day_total_charge'],
                  t['day_spike_discharge'], t['day_peak_discharge'], t['day_flat_discharge'], t['day_valley_discharge'],
                  t['day_dvalley_discharge'], t['day_total_discharge']]
            days_data_array.append(li)

        # sorted(days_data_array, key=lambda x: x[0])
        for row_num, row_data in enumerate(days_data_array, 3):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet_s[f'{col_letter}{row_num}'] = cell_value

        # 设置所有单元格居中对齐及自动换行
        for row in sheet_s.iter_rows():
            for cell in row:
                cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
                cell.font = Font(size=11)

        # 设置边框线
        border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                        bottom=Side(style='thin'))
        for row in sheet_s.iter_rows():
            for cell in row:
                cell.border = border

    # 每日购电价格
    if len(return_dic['price_infos']):
        for index, price_info in enumerate(return_dic['price_infos']):
            if price_info:
                if lang == 'zh':
                    sheet_s = workbook.create_sheet("每日电价详情-" + price_info[0]['title'])
                    # 在A1单元格中写入固定数据
                    sheet_s['A1'] = '日期'
                    sheet_s['B1'] = '单位电价'
                    sheet_s['C1'] = '尖峰充电价格（元/kWh）'
                    sheet_s['D1'] = '尖峰放电价格（元/kWh）'
                    sheet_s['E1'] = '峰时充电价格（元/kWh）'
                    sheet_s['F1'] = '峰时放电价格（元/kWh）'
                    sheet_s['G1'] = '平时充电价格（元/kWh）'
                    sheet_s['H1'] = '平时放电价格（元/kWh）'
                    sheet_s['I1'] = '谷时充电价格（元/kWh）'
                    sheet_s['J1'] = '谷时放电价格（元/kWh）'
                    sheet_s['K1'] = '深谷时充电价格（元/kWh）'
                    sheet_s['L1'] = '深谷时放电价格（元/kWh）'
                else:
                    sheet_s = workbook.create_sheet("Daily Price-" + price_info[0]['title'])
                    # 在A1单元格中写入固定数据
                    sheet_s['A1'] = 'Date'
                    sheet_s['B1'] = 'Settlement Price'
                    sheet_s['C1'] = 'Rush-hour Charging Price(￥/kWh)'
                    sheet_s['D1'] = 'Rush-hour Discharging Price(￥/kWh)'
                    sheet_s['E1'] = 'Peak-hour Charging Price(￥/kWh)'
                    sheet_s['F1'] = 'Peak-hour Discharging Price(￥/kWh)'
                    sheet_s['G1'] = 'Flat-hour Charging Price(￥/kWh)'
                    sheet_s['H1'] = 'Flat-hour Discharging Price(￥/kWh)'
                    sheet_s['I1'] = 'Valley-hour Charging Price(￥/kWh)'
                    sheet_s['J1'] = 'Valley-hour Discharging Price(￥/kWh)'
                    sheet_s['K1'] = 'Dvalley-hour Charging Price(￥/kWh)'
                    sheet_s['L1'] = 'Dvalley-hou Discharging Price(￥/kWh)'

                # 合并单元格
                # sheet_s.merge_cells('A1:A2')
                # sheet_s.merge_cells('B1:B2')
                # sheet_s.merge_cells('C1:G1')
                # sheet_s.merge_cells('H1:L1')

                # sheet.merge_cells('B4:F4')
                # sheet.merge_cells('H4:J4')

                # # sheet_s 写入标头
                # for col_num, header in enumerate(table2_titles, 1):
                #     col_letter = openpyxl.utils.get_column_letter(col_num)
                #     sheet2[f'{col_letter}1'] = header

                # 准备表数据
                days_data_array = list()
                for t in price_info:
                    li = [t['date'],
                          t['title'],
                          t['spike']['charge_price'],
                          t['spike']['discharge_price'],
                          t['peak']['charge_price'],
                          t['peak']['discharge_price'],
                          t['flat']['charge_price'],
                          t['flat']['discharge_price'],
                          t['valley']['charge_price'],
                          t['valley']['discharge_price'],
                          t['dvalley']['discharge_price'],
                          t['dvalley']['discharge_price'],
                          ]
                    days_data_array.append(li)

                # sorted(days_data_array, key=lambda x: x[0])
                for row_num, row_data in enumerate(days_data_array, 2):
                    for col_num, cell_value in enumerate(row_data, 1):
                        col_letter = openpyxl.utils.get_column_letter(col_num)
                        sheet_s[f'{col_letter}{row_num}'] = cell_value

                # 设置所有单元格居中对齐及自动换行
                for row in sheet_s.iter_rows():
                    for cell in row:
                        cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
                        cell.font = Font(size=11)

                # 设置边框线
                border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                                bottom=Side(style='thin'))
                for row in sheet_s.iter_rows():
                    for cell in row:
                        cell.border = border

    # 设置纸张方向为横向
    # sheet_s.page_setup.orientation = sheet_s.ORIENTATION_LANDSCAPE

    # 保存工作簿到文件
    workbook.save(excel_file)




