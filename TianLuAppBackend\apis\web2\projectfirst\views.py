import os
import re
import datetime
import traceback

import requests
import json
import random
import time
import pymysql
import calendar

from math import sqrt
from openpyxl import Workbook
from django.conf import settings
from django.db import connections
from django.db.models import Sum, Q, Max
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from LocaleTool.common import TranslateCls
from common.constant import EMPTY_STR_LIST
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
import paho.mqtt.client as mqtt

from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
from tools.hour_setting import create_time_mapping, create_one_minute_list
from tools.aly_send_smscode import Sample
from serializers import monitor_serializers
from settings.point_dict import STATUS_DICT, DISCRETE_DICT
from common import common_response_code
from apis.user import models
from encryption.AES_symmetric_encryption import EncryptDate
from apis.app2.monitor2.monitor2_serializers import VirtuallyCheckSMSCodeSerializer
from tools.minio_tool import MinioTool
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


class Weather(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        项目所在地天气信息
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 项目ID
        try:
            project = models.Project.objects.values('province__name', 'city').get(id=id)
        except models.Project.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "error", "detail": "项目不存在" if lang == 'zh' else 'project not exists!'}
            })

        city = project.get('city')

        if city:
            name = city[:-1]
        else:
            province = project.get('province__name')
            name = province[:-1]
        ret = os.system("timeout 0.3 ping -c 1 weathernew.pae.baidu.com")
        if ret == 0:
            url = 'https://weathernew.pae.baidu.com/weathernew/pc?query={}天气&srcid=4982&forecast=long_day_forecast'.format(
                name)
        else:
            url = 'http://172.17.5.187:50000/weath?query={}天气&srcid=4982&forecast=long_day_forecast'.format(
                name)

        res = requests.get(url).content.decode('utf-8')
        data = re.findall(r'long_day_forecast.*15_day_forecast', res)
        t_data = {}
        if data:
            data = eval(re.findall(r'\{.*\}', data[0])[0]).get('info')
            now_hour = datetime.datetime.now().hour  # 判断夜间白天

            for i in range(3):  # 查询三天
                t = data[i]
                t_data[t.get('date')] = {
                    'temperature': t.get('temperature_night') + '~' + t.get('temperature_day'),
                    'weather': t.get('weather_day') if 7 <= now_hour <= 19 else t.get('weather_night'),
                    'wind': t.get('wind_direction_day') + t.get('wind_power_day') if 7 <= now_hour <= 19 else t.get(
                        'wind_direction_night') + t.get('wind_power_night'),
                }
        else:
            for i in range(3):  # 查询三天
                date = (datetime.datetime.now() + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
                t_data[date] = {
                    'temperature': '--',
                    'weather': '--',
                    'wind': '--'
                }

        # if lang == 'en':
        #     # 中文转英文
        #     t_cls = TranslateCls(2)
        #     for v in t_data.values():
        #         v['weather'] = t_cls.str_chinese(v['weather']) if v['weather'] != '--' else v['weather']
        #         v['wind'] = t_cls.str_chinese(v['wind']) if v['wind'] != '--' else v['wind']

        # temp_data = {
		# 	"2024-11-25": {
		# 		"temperature": "9~20",
		# 		"weather": "light rain",
		# 		"wind": "Northwest wind level 3"
		# 	},
		# 	"2024-11-26": {
		# 		"temperature": "5~14",
		# 		"weather": "Sunny",
		# 		"wind": "Northwest wind level 4"
		# 	},
		# 	"2024-11-27": {
		# 		"temperature": "2~14",
		# 		"weather": "Sunny",
		# 		"wind": "Northwest wind level 4"
		# 	}
		# }
        # todo 临时调试数据

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": t_data,
            },
        })


class MessageView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        项目信息
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 项目ID
        if not id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'field error'}
            })

        try:
            project = models.Project.objects.get(id=id)
        except models.Project.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "error", "detail": "项目不存在" if lang == 'zh' else 'project not exists!'}
            })

        master_stations = project.materstation_set.filter(is_delete=0).all()
        data = {
            'name': project.name if lang == 'zh' else project.en_name,
            'address': project.address if lang == 'zh' else project.en_address,  # 地址
            'run_day': (datetime.datetime.now() - project.in_time).days,  # 项目运行天数
            'station_count': master_stations.count(),  # 并网点数量
            'rated_power': 0,  # 额定功率
            'rated_capacity': 0,  # 额定容量
            'unit_count': 0,  # 储能单元数量
            'off_line_unit_count': 0,  # 离线储能单元数量
            'fault_unit_count': 0,  # 故障储能单元数量
            'ammeter_count': master_stations.filter(is_account=1).count(),  # 结算电表数量
            'battery_count': 0,  # 电池数量
        }

        conn = get_redis_connection("3")

        for master in master_stations:
            stations = master.stationdetails_set.filter(is_delete=0).all()
            for station in stations:
                data['rated_power'] += float(station.rated_power)
                data['rated_capacity'] += float(station.rated_capacity)
                units = station.unit_set.filter(is_delete=0)
                data['unit_count'] += units.count()
                for unit in units.all():
                    pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.pcs))
                    bms = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms))
                    if pcs:
                        pcs_dict = json.loads(json.loads(pcs.decode("utf-8")))
                    else:
                        pcs_dict = {}
                    if bms:
                        bms_dict = json.loads(json.loads(bms.decode("utf-8")))
                    else:
                        bms_dict = {}

                    if not bms_dict or not pcs_dict:
                        data['off_line_unit_count'] += 1
                    else:
                        GFault = bms_dict.get("GFault") if (bms_dict.get("GFault") and bms_dict.get("GFault")
                                                            not in EMPTY_STR_LIST) else -2  # bms故障状态
                        Fault = pcs_dict.get("Fault") if (pcs_dict.get("Fault") and pcs_dict.get("Fault")
                                                          not in EMPTY_STR_LIST) else -2  # pcs故障状态
                        if Fault and int(Fault) == 1 or GFault and int(GFault) == 1:
                            data['fault_unit_count'] += 1

        # 最新逻辑项目离线，单元直接离线
        project_status = (models.StationStatus.objects.filter(station__master_station__project_id=id).
                          aggregate(Max('status')).get('status__max'))
        if project_status == 4:
            data['off_line_unit_count'] = data['unit_count']

        data['rated_power'] = round(data['rated_power'], 2)
        data['rated_capacity'] = round(data['rated_capacity'], 2)
        data['battery_count'] = round(data.get('unit_count') * 260, 2)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })


class StationListView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        项目并网点列表
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 项目ID
        if not id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'parameters error.'}
            })

        try:
            project = models.Project.objects.get(id=id)
        except models.Project.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "error", "detail": "项目不存在" if lang == 'zh' else 'The project does not exist.'}
            })

        master_stations = project.materstation_set.values('id', 'name', 'english_name').filter(is_delete=0).all()

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": master_stations,
            },
        })


class StationMessageView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        项目并网点信息
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 并网点ID
        if not id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'field error'}
            })

        try:
            station = models.MaterStation.objects.get(id=id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "error", "detail": "并网点不存在" if lang == 'zh' else 'station does not exist'}
            })

        today = datetime.date.today()
        yesterday = today - datetime.timedelta(days=1)
        english_names = [i.english_name for i in station.stationdetails_set.filter(is_delete=0).all()]
        if len(english_names) == 1:
            english_names.append(-1)  # 防止转tuple出现逗号的情况
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:

            # 改查新综合过结算表和计量表的新1d冻结表: 表名未定
            # 今日充放电量
            day_sql = """
                        SELECT
                            sum( v_disg ) as disg,
                            sum( v_chag ) as chag,
                            avg( chag_soc ),
                            avg( disg_soc )
                        FROM
                            ads_report_chag_disg_union_1d
                        WHERE
                            station in {}
                            AND day = '{}'
                            AND station_type <= 1
                            """.format(tuple(english_names), today)

            # 累计充放电量
            all_sql = """
                        SELECT
                            sum( v_disg ) as disg,
                            sum( v_chag ) as chag,
                            avg( chag_soc ),
                            avg( disg_soc )
                        FROM
                            ads_report_chag_disg_union_1d
                        WHERE
                            station in {}
                            AND station_type <=1""".format(tuple(english_names))

            # 查询收益信息
            income_sql = f"""SELECT 
                                    DAY,
                                    sum(day_income)
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                    DAY = '{yesterday}' AND
	                                station in {tuple(english_names)}  GROUP BY `day` ORDER BY `day`"""
            # 获取查询结果
            income_all_sql = f"""SELECT
                                        incomes.month, ele.v_disg, incomes.day_income
                                    FROM
                                        (  select 
                                           SUBSTRING( day, 1, 7 ) as month,
                                            sum(day_income) as day_income
                                        FROM
                                            ads_report_station_income_1d 
                                        WHERE
                                             station in {tuple(english_names)}
                                        GROUP BY
                                        month 
                                        ORDER BY
                                        month 
                                        ) AS incomes
                                        LEFT JOIN (
                                        SELECT  SUBSTRING( day, 1, 7 ) as month,
                                            sum(v_disg) as v_disg
                                        FROM
                                            ads_report_chag_disg_union_1d 
                                        WHERE
                                             station in {tuple(english_names)} AND
                                             station_type <= 1 
                                        GROUP BY
                                        month 
                                        ORDER BY
                                    month 
                                        ) AS ele ON ele.month = incomes.month  ORDER BY incomes.month DESC"""
            ads_cursor.execute(all_sql)
            charge_discharge_all = ads_cursor.fetchall()
            ads_cursor.execute(day_sql)
            charge_discharge_day = ads_cursor.fetchall()

            ads_cursor.execute(income_sql)
            income_res = ads_cursor.fetchone()  # 获取昨日收益
            ads_cursor.execute(income_all_sql)
            income_all_res = ads_cursor.fetchall()  # 获取当月、累计收益

            income, all_income = 0, 0  # 昨日收益、累计收益
            electricity_income_month, electricity_income_all = 0, 0  # 当月度电收益、累计度电收益
            if income_res:
                if income_res[1] not in EMPTY_STR_LIST:
                    income = income_res[1]
            year_month = today.strftime('%Y-%m')
            all_income, disg_all = 0, 0
            if income_all_res:
                for i in income_all_res:
                    month_income = float(i[2]) if i[2] not in EMPTY_STR_LIST else '--'
                    disg = float(i[1]) if i[1] not in EMPTY_STR_LIST else '--'
                    if i[0] == year_month:
                        electricity_income_month = round(month_income / disg, 2) if disg != '--' and disg != 0 and month_income != '--' else '--'
                    all_income += month_income if month_income != '--' else 0
                    disg_all += disg if disg != '--' else 0

            electricity_income_all = round(all_income / disg_all, 2) if disg_all != 0 else '--'
            # 计算充放电效率
            if 0 in charge_discharge_day[0] or None in charge_discharge_day[0]:
                discharge_efficiency_day = '--'
            else:
                discharge_efficiency_day = ((charge_discharge_day[0][0] / charge_discharge_day[0][3]) / \
                                                  (charge_discharge_day[0][1] / charge_discharge_day[0][2])) * 100
                if discharge_efficiency_day < 85:
                    discharge_efficiency_day = 85
                elif discharge_efficiency_day > 100:
                    discharge_efficiency_day = '--'
                else:
                    discharge_efficiency_day = round(discharge_efficiency_day, 2)
            disg_day = charge_discharge_day[0][0]
            chag_day = charge_discharge_day[0][1]

            disg_all = charge_discharge_all[0][0]
            chag_all = charge_discharge_all[0][1]



        # 实时监测
        conn = get_redis_connection("3")
        dump_energy = 0  # 剩余电量
        soc_list = []  # SOC
        discharge_status = 0  # 充放电状态 1：充电；0：静置；2：放电；-1离线
        power = 0    # 储能单元有功功率的和
        total_q = 0  #　储能单元无功功率的和
        p_load = 0  # 负荷功率


        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).first()

        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems:
            ems = eval(eval(ems))
        else:
            ems = {}
        tfm = float(ems.get('TFM')) if ems.get('TFM') not in EMPTY_STR_LIST else float(station_ems.transformer_capacity)
        # tprt = float(ems.get('TPRT')) if ems.get('TPRT') not in EMPTY_STR_LIST else '--'
        # transformer_safety_capacity = tfm if '--' == tprt or tfm == '--' else tfm * tprt  # 变压器安全容量
        pcc = float(ems.get('PCC')) if ems.get('PCC') not in EMPTY_STR_LIST else '--'  # 负荷表采集的有功功率
        EQ = float(ems.get('EQ')) if ems.get('EQ') not in EMPTY_STR_LIST else '--'    # 负荷表采集的无功功率
        ehz = float(ems.get('EHZ')) if ems.get('EHZ') not in EMPTY_STR_LIST else '--'  # 频率
        # eq = float(ems.get('EQ')) if ems.get('EQ') not in EMPTY_STR_LIST else '--'  # 总无功功率
        cos = float(ems.get('PCOS')) if ems.get('PCOS') not in EMPTY_STR_LIST else '--'  # 功率因数(4位小数)
        # 电表位置：1：前置；2：后置
        meter_position = station_ems.meter_position
        for slave_station in station.stationdetails_set.filter(is_delete=0).all():
            for unit in slave_station.unit_set.filter(is_delete=0).all():
                bms = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, unit.bms))
                if bms:
                    bms = eval(eval(bms))
                else:
                    bms = {}

                if bms.get('SE') not in EMPTY_STR_LIST and dump_energy != '--':
                    dump_energy += float(bms.get('SE'))
                else:
                    dump_energy = '--'

                if bms.get('SOC') not in EMPTY_STR_LIST:
                    soc_list.append(float(bms.get('SOC')))
                else:
                    soc_list.append('--')

                pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, unit.pcs))
                if pcs:
                    pcs = eval(eval(pcs))
                    p = float(pcs.get('P')) if pcs.get('P') not in EMPTY_STR_LIST else '--'
                    if p != '--':
                        if p > 1:
                            discharge_status = 2
                        elif p < -1:
                            discharge_status = 1
                        else:
                            discharge_status = 0 if discharge_status == 0 else discharge_status
                    else:
                        discharge_status = -1
                        break

                else:
                    pcs = {}
                    discharge_status = -1

                if pcs.get('P') not in EMPTY_STR_LIST and power != '--':
                    power += float(pcs.get('P'))
                else:
                    power = '--'

                if pcs.get('Q') not in EMPTY_STR_LIST and total_q != '--':
                    total_q += float(pcs.get('Q'))
                else:
                    total_q = '--'

        # 电网测功率/总有功功率
        if pcc != '--' and power != '--':
            gridSide_power = round(pcc - power, 2) if meter_position == 2 else round(pcc, 2)  # 电网侧功率
            p_load = round(pcc + power, 2) if meter_position == 1 else round(pcc, 2)
        else:
            gridSide_power = '--'

        # 总无功功率
        if EQ != '--' and total_q != '--':
            eq_ = round(EQ - total_q, 2) if meter_position == 2 else round(EQ, 2)
        else:
            eq_ = '--'

        # 电表后置，功率因数=有功/视在功率（保留4位，不可能大于1），视在功率=开平方（有功平方+无功平方）
        if meter_position == 2:
            if gridSide_power != '--' and eq_ != '--':
                # 视在功率
                p_appr = round(sqrt(gridSide_power ** 2 + eq_ ** 2), 4)
                cos = round(gridSide_power / p_appr, 4)
            else:
                cos = 1.0

        # 用电设备是否从电网取电：1：取电；0：不取电
        if ems:
            power_on_condition = 1
        else:
            power_on_condition = 0
        if station.stationdetails_set.filter(is_delete=0).first().meter_position == 1:  # 电表前置
            if pcc != '--' and pcc <= 0:
                power_on_condition = 0
        else:
            if pcc != '--' and power != '--':
                if round(pcc, 2) == round(power, 2):
                    power_on_condition = 0
            else:
                power_on_condition = 0

        # 如果总有功功率小于0或为'--'，则取power_on_condition 取0
        if gridSide_power == '--' or gridSide_power <= 0:
            power_on_condition = 0

        data = {
            'income': round(income, 2),  # 昨日收益
            'all_income': ([round(all_income / 10000, 2), '万元'] if all_income >= 10000 else [round(all_income, 2), '元']) if lang == 'zh' else [round(all_income, 2), 'Yuan'],# 累计收益
            'disg_day': round(disg_day, 2) if disg_day != '--' and disg_day is not None else disg_day,  # 昨日放电量
            'chag_day': round(chag_day, 2) if chag_day != '--' and chag_day is not None else chag_day,  # 昨日充电量
            'disg_all': round(disg_all / 1000, 2) if disg_all != '--' and disg_all else disg_day,  # 累计放电量（MWh)
            'chag_all': round(chag_all / 1000, 2) if chag_all != '--' and chag_all else disg_day,  # 累计充电量（MWh)
            'electricity_income_month': round(electricity_income_month, 2) if electricity_income_month != '--' else '--',  # 当月度电收益
            'electricity_income_all': round(electricity_income_all, 2) if electricity_income_all != '--' else '--',  # 累计度电收益
            'discharge_efficiency_day': discharge_efficiency_day,  # 今日充放电效率
            'discharge_efficiency_all': station.efficiency,  # 累计充放电效率
            'dump_energy': round(dump_energy, 2) if dump_energy != '--' and dump_energy != None else '--',  # 剩余电量
            'soc': round(sum(soc_list) / len(soc_list), 2) if soc_list and '--' not in soc_list else '--',  # SOC
            'discharge_status': discharge_status,  # 充放电状态 1：充电；0：静置；2：放电; -1:离线
            'power': round(power, 2) if power != '--' else '--',  # 储能功率
            'transformer_safety_capacity': round(tfm, 2) if tfm != '--' else '--',  # 变压器安全容量===>变压器容量
            'gridSide_power': gridSide_power,  # 电网测功率
            # 'pcc': pcc,  # 负荷功率
            'total_p': gridSide_power,  # 总有功功率
            'total_q': eq_,  # 总无功功率
            'total_ehz': ehz,  # 频率
            'total_cos': cos,  # 功率因数(4位小数)
            'total_load': '--',  # 负载率
            'p_load': p_load,   # 负荷功率
            'power_on_condition': power_on_condition,  # 用电设备是否从电网取电：1：取电；0：不取电
        }
        if gridSide_power != '--' and (tfm != '--' and tfm != 0):
            data['total_load'] = round(gridSide_power / tfm * 100, 2)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })


class StationUnitListView(APIView):
    """
    储能单元清单
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        station_id = request.query_params.get('id')
        request_id = request.user["user_id"]
        if not station_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )

        try:
            staiton = models.MaterStation.objects.get(id=station_id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission, please check the parameters!',
                    }
                }
            )

        # Redis
        conn = get_redis_connection('3')

        data = []

        # 查询该主站下所有主站的储能单元；slave=0不参与计算
        units_res = models.Unit.objects.filter(is_delete=0, station__in=staiton.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0)).all()).all()
        for unit in units_res:
            discharge_status = 0  # 默认静置
            bms = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, unit.bms))
            if bms:
                bms = eval(eval(bms))
            else:
                bms = {}
            # 剩余电量
            if bms.get('SE') not in EMPTY_STR_LIST:
                dump_energy = float(bms.get('SE'))
            else:
                dump_energy = '--'
            # 额定电量
            if bms.get('RE') not in EMPTY_STR_LIST:
                kwh = float(bms.get('RE'))
            else:
                kwh = '--'
            # 计算电量百分比
            if kwh != '--' and dump_energy != '--':
                kwh_percent = round(dump_energy / kwh, 2) if dump_energy != 0 else 0
            else:
                kwh_percent = '--'
            # SOC
            if bms.get('SOC') not in EMPTY_STR_LIST:
                soc = float(bms.get('SOC'))
            else:
                soc = '--'

            pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, unit.pcs))
            if pcs:
                pcs = eval(eval(pcs))
            else:
                pcs = {}
                discharge_status = -1
            # 实时功率
            if pcs.get('P') not in EMPTY_STR_LIST:
                p = float(pcs.get('P'))
            else:
                p = '--'
                discharge_status = -1
            # 不离线的情况下判断充放电状态 1：充电；0：静置；2：放电
            # 储能单元的边界值
            if discharge_status != -1 and p != '--':
                if p >= 1:
                    discharge_status = 2
                elif p <= -1:
                    discharge_status = 1
                else:
                    discharge_status = 0

            data.append({
                'id': unit.id,
                'english_name': unit.english_name,
                'name': unit.unit_new_name if lang == 'zh' else unit.en_unit_new_name,
                'soc': soc,
                'power': p,
                'discharge_status': discharge_status,
                'dump_energy': dump_energy,
                'kwh_percent': kwh_percent,
                'pcs_name': unit.pcs
            })
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })



class StationGridSidePowerView(APIView):
    """
    电网侧电表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        station_id = request.query_params.get('id')
        request_id = request.user["user_id"]
        if not station_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )

        try:
            station = models.MaterStation.objects.get(id=station_id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission, please check the parameters!',
                    }
                }
            )

        # Redis
        conn = get_redis_connection('3')

        data = {
            'voltage': {},  # 电压
            'electricity': {},  # 电流
            'active_power': {},  # 有功功率
            'active_power_all': 0,  # 总有功功率
            'reactive_power': {},  # 无功功率
            'reactive_power_all': {},  # 总无功功率
            'frequency': 0,  # 频率
            'pe': {},  # 功率因数
            'pe_all': 0,  # 总功率因数
            'positive_active_energy': 0,  # 正向有功电能
            'reverse_active_energy': 0  # 反向有功电能
        }

        # 查询主站
        # station_ems = station.stationdetails_set.filter(
        #     Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0)).first()

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).first()

        # 测量量
        ems_measure = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems_measure:
            ems_measure = eval(eval(ems_measure))
        else:
            ems_measure = {}
        data['voltage']['ab'] = ems_measure.get('EUAB') if ems_measure.get('EUAB') not in EMPTY_STR_LIST else '--'
        data['voltage']['bc'] = ems_measure.get('EUBC') if ems_measure.get('EUBC') not in EMPTY_STR_LIST else '--'
        data['voltage']['ca'] = ems_measure.get('EUCA') if ems_measure.get('EUCA') not in EMPTY_STR_LIST else '--'
        data['electricity']['a'] = ems_measure.get('EIA') if ems_measure.get('EIA') not in EMPTY_STR_LIST else '--'
        data['electricity']['b'] = ems_measure.get('EIB') if ems_measure.get('EIB') not in EMPTY_STR_LIST else '--'
        data['electricity']['c'] = ems_measure.get('EIC') if ems_measure.get('EIC') not in EMPTY_STR_LIST else '--'
        data['active_power']['a'] = ems_measure.get('EPA') if ems_measure.get('EPA') not in EMPTY_STR_LIST else '--'
        data['active_power']['b'] = ems_measure.get('EPB') if ems_measure.get('EPB') not in EMPTY_STR_LIST else '--'
        data['active_power']['c'] = ems_measure.get('EPC') if ems_measure.get('EPC') not in EMPTY_STR_LIST else '--'
        data['active_power_all'] = ems_measure.get('PCC') if ems_measure.get('PCC') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['a'] = ems_measure.get('EQA') if ems_measure.get('EQA') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['b'] = ems_measure.get('EQB') if ems_measure.get('EQB') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['c'] = ems_measure.get('EQC') if ems_measure.get('EQC') not in EMPTY_STR_LIST else '--'
        data['reactive_power_all'] = ems_measure.get('EQ') if ems_measure.get('EQ') not in EMPTY_STR_LIST else '--'
        data['frequency'] = ems_measure.get('EHZ') if ems_measure.get('EHZ') not in EMPTY_STR_LIST else '--'
        data['pe']['a'] = ems_measure.get('COSA') if ems_measure.get('COSA') not in EMPTY_STR_LIST else '--'
        data['pe']['b'] = ems_measure.get('COSB') if ems_measure.get('COSB') not in EMPTY_STR_LIST else '--'
        data['pe']['c'] = ems_measure.get('COSC') if ems_measure.get('COSC') not in EMPTY_STR_LIST else '--'
        data['pe_all'] = ems_measure.get('PCOS') if ems_measure.get('PCOS') not in EMPTY_STR_LIST else '--'

        # 累积量
        ems_cumulant = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', station_ems.english_name, 'EMS'))
        if ems_cumulant:
            ems_cumulant = eval(eval(ems_cumulant))
        else:
            ems_cumulant = {}

        data['positive_active_energy'] = ems_cumulant.get('EPAE') if ems_measure.get('EPAE') not in EMPTY_STR_LIST else '--'
        data['reverse_active_energy'] = ems_cumulant.get('ENAE') if ems_measure.get('ENAE') not in EMPTY_STR_LIST else '--'

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })

class StationUnitDetailView(APIView):
    """
    储能单元详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        unit_id = request.query_params.get('id')
        if not unit_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '参数缺失！' if lang == 'zh' else 'Missing parameters.',
                    }
                }
            )

        try:
            unit = models.Unit.objects.get(id=unit_id)
        except models.Unit.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '储能单元不存在，请检查参数！' if lang == 'zh' else 'The storage unit does not exist, please check the parameters.',
                    }
                }
            )
        data = {}
        conn = get_redis_connection("3")
        # 查看主站及非主从站的就地控制状态
        conn_1 = get_redis_connection("default")
        pcs_d = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('DISCRETE', unit.station.english_name, unit.pcs))
        if pcs_d:
            pcs_d = eval(eval(pcs_d))
            data['status'] = DISCRETE_DICT.get('PRse')[lang].get(int(pcs_d.get('PRse'))) if pcs_d.get(
                'PRse') and pcs_d.get('PRse') not in EMPTY_STR_LIST else '--'
        else:
            data['status'] = '--'  # 运行状态

        pcs_s = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name, unit.pcs))
        redis_key = str(unit.station.english_name + "_" + unit.station.app + "_" + unit.pcs + "_" + "EFPR")
        redis_EFPR = conn_1.get(redis_key)
        if pcs_s:
            pcs_s = eval(eval(pcs_s))
            data['desc'] = STATUS_DICT.get('DCse')[lang].get(int(pcs_s.get('DCse'))) if pcs_s.get(
                'DCse') and pcs_s.get('DCse') not in EMPTY_STR_LIST else '-- '
            if redis_EFPR:
                data['pcs_status'] = int(redis_EFPR.decode("utf-8")) if redis_EFPR else 0  # 0开启；1关闭 PCS开关
            else:
                data['pcs_status'] = int(pcs_s.get('PCStu')) if pcs_s.get('PCStu') and pcs_s.get('PCStu') not in EMPTY_STR_LIST else 0
        else:
            data['desc'] = '--'  # 直流接触器状态
            if redis_EFPR:
                data['pcs_status'] = int(redis_EFPR.decode("utf-8")) if redis_EFPR else 0  # 0开启；1关闭 PCS开关
            else:
                data['pcs_status'] = 0
        data['pcs_status'] = 1 if int(data['pcs_status']) == 0 else 0  # 反转
        pcs_m = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, unit.pcs))

        if pcs_m:
            pcs_m = eval(eval(pcs_m))
            data['iitg'] = float(pcs_m.get('IITG')) if pcs_m.get('IITG') and pcs_m.get('IITG') not in EMPTY_STR_LIST else '--'
            data['pat'] = float(pcs_m.get('PAT')) if pcs_m.get('PAT') and pcs_m.get('PAT') not in EMPTY_STR_LIST else '--'
            data['a'] = float(pcs_m.get('IGBTAT')) if pcs_m.get('IGBTAT') and pcs_m.get('IGBTAT') not in EMPTY_STR_LIST else '--'
            data['b'] = float(pcs_m.get('TGBTBT')) if pcs_m.get('TGBTBT') and pcs_m.get('TGBTBT') not in EMPTY_STR_LIST else '--'
            data['c'] = float(pcs_m.get('TGBTCT')) if pcs_m.get('TGBTCT') and pcs_m.get('TGBTCT') not in EMPTY_STR_LIST else '--'
            data['p'] = float(pcs_m.get('P')) if pcs_m.get('P') and pcs_m.get('P') not in EMPTY_STR_LIST else '--'
            data['q'] = float(pcs_m.get('Q')) if pcs_m.get('Q') and pcs_m.get('Q') not in EMPTY_STR_LIST else '--'
            data['otap'] = float(pcs_m.get('OTAP')) if pcs_m.get('OTAP') and pcs_m.get('OTAP') not in EMPTY_STR_LIST else '--'
            data['cos'] = float(pcs_m.get('COS')) if pcs_m.get('COS') and pcs_m.get('COS') not in EMPTY_STR_LIST else '--'
            data['ab'] = float(pcs_m.get('PUab')) if pcs_m.get('PUab') and pcs_m.get('PUab') not in EMPTY_STR_LIST else '--'
            data['bc'] = float(pcs_m.get('PUbc')) if pcs_m.get('PUbc') and pcs_m.get('PUbc') not in EMPTY_STR_LIST else '--'
            data['ca'] = float(pcs_m.get('PUca')) if pcs_m.get('PUca') and pcs_m.get('PUca') not in EMPTY_STR_LIST else '--'
            data['ia'] = float(pcs_m.get('Ia')) if pcs_m.get('Ia') and pcs_m.get('Ia') not in EMPTY_STR_LIST else '--'
            data['ib'] = float(pcs_m.get('Ib')) if pcs_m.get('Ib') and pcs_m.get('Ib') not in EMPTY_STR_LIST else '--'
            data['ic'] = float(pcs_m.get('Ic')) if pcs_m.get('Ic') and pcs_m.get('Ic') not in EMPTY_STR_LIST else '--'
            data['pf'] = float(pcs_m.get('PF')) if pcs_m.get('PF') and pcs_m.get('PF') not in EMPTY_STR_LIST else '--'
            dcp = float(pcs_m.get('DCP')) if pcs_m.get('DCP') and pcs_m.get('DCP') not in EMPTY_STR_LIST else '--'
            data['dcp'] = dcp
            if dcp != 0 and dcp != '--' and data['p'] != '--':
                transfer_efficiency = data['p'] / dcp
            else:
                transfer_efficiency = '--'
            if transfer_efficiency != '--' and data['p'] != '--' and dcp != '--' and data['p'] != 0:
                transfer_efficiency = dcp / data['p'] if dcp / data['p'] < transfer_efficiency else transfer_efficiency
            else:
                transfer_efficiency = '--'
            if transfer_efficiency != '--' and transfer_efficiency > 1:
                data['transfer_efficiency'] = 1
            data['transfer_efficiency'] = round(transfer_efficiency*100,
                                                2) if transfer_efficiency != '--' else transfer_efficiency  # 实时转换效率
            # 充放电状态 -1：离线；1：充电；0：静置；2：放电
            # 计算多个单元的边界值
            if data['p'] != '--':
                if data['p'] >= 1:
                    discharge_status = 2
                elif data['p'] <= -1:
                    discharge_status = 1
                else:
                    discharge_status = 0
            else:
                discharge_status = -1
            data['discharge_status'] = discharge_status
        else:
            data['iitg'] = '--'  # 对地绝缘阻抗
            data['pat'] = '--'  # 机柜温度
            data['dcp'] = '--'  # 直流侧功率
            data['a'] = '--'  # A 相 IGBT 温度
            data['b'] = '--'  # B 相 IGBT 温度
            data['c'] = '--'  # C 相 IGBT 温度
            data['p'] = '--'  # 有功功率
            data['q'] = '--'  # 无功功率
            data['otap'] = '--'  # 视在功率
            data['cos'] = '--'  # 功率因数
            data['ab'] = '--'  # AB 相电网线电压
            data['bc'] = '--'  # BC 相电网线电压
            data['ca'] = '--'  # CA 相电网线电压
            data['ia'] = '--'  # A 相电流
            data['ib'] = '--'  # B 相电流
            data['ic'] = '--'  # C 相电流
            data['pf'] = '--'  # 频率
            data['transfer_efficiency'] = '--'  # 实时转换效率
            data['discharge_status'] = -1  # 充放电状态 -1：离线；1：充电；0：静置；2：放电

        bms_m = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.station.english_name, unit.bms))
        if bms_m:
            bms_m = eval(eval(bms_m))
            data['bu'] = float(bms_m.get('U')) if bms_m.get('U') and bms_m.get('U') not in EMPTY_STR_LIST else '--'
            data['opv'] = float(bms_m.get('OPV')) if bms_m.get('OPV') and bms_m.get('OPV') not in EMPTY_STR_LIST else '--'
            data['ipv'] = float(bms_m.get('IPV')) if bms_m.get('IPV') and bms_m.get('IPV') not in EMPTY_STR_LIST else '--'
            data['dci'] = float(bms_m.get('I')) if bms_m.get('I') and bms_m.get('I') not in EMPTY_STR_LIST else '--'
            data['bat'] = float(bms_m.get('BAT')) if bms_m.get('BAT') and bms_m.get('BAT') not in EMPTY_STR_LIST else '--'
        else:
            data['bu'] = '--'   # 直流电压
            data['opv'] = '--'  # 液冷机出水压力
            data['ipv'] = '--'  # 液冷机回水压力
            data['dci'] = '--'  # 直流电流
            data['bat'] = '--'  # 环境温度
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })


class AlarmDetailView(APIView):
    """小程序端告警详情"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        params = request.query_params
        if not params:
            today = datetime.date.today()
            query_ins = (
                models.FaultAlarm.objects.using('alarm_module').filter(start_time__date=today)
                    .values("status", "type", "start_time", "end_time", "details", "id", "note", "device")
                    .order_by("-start_time")
            )
        else:
            new_dic = {}
            pcs = params.get("device", None)
            if pcs:
                unit_ins = models.Unit.objects.filter(is_delete=0, english_name=pcs).first()
                pcs_ = unit_ins.pcs
                bms_ = unit_ins.bms
                ins = models.FaultAlarm.objects.using('alarm_module').filter(Q(device=pcs_) | Q(device=bms_))
            else:
                ins = models.FaultAlarm.objects
            for k in params.keys():
                if k == "device":
                    pass
                elif k == "station__id":
                    master_station_id = params.get("station__id")
                    master_stations = models.MaterStation.objects.filter(id=master_station_id, is_delete=0)
                    if master_stations.exists():
                        master_station = master_stations.first()
                        slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                        ids = [s.id for s in slave_stations]
                        new_dic['station_id__in'] = ids
                else:
                    new_dic[k] = params[k]
            if new_dic.get('token'):
                del new_dic['token']
            query_ins = (
                ins.filter(**new_dic)
                    .values("status", "type", "start_time", "end_time", "details", "id", "note", "device",
                            "device_another_name")
                    .order_by("-start_time")
            )
        warn = []
        fault = []
        event = []
        if query_ins.exists():
            end = 100 if len(query_ins) > 100 else len(query_ins)
            for query in query_ins[:end]:
                try:
                    query['note'] = ALARM_ZH_EN_MAP.get(query["note"])[lang]
                    query["details"] = query["device_another_name"] + ":" + ALARM_ZH_EN_MAP.get(query["details"])[lang] if query["device_another_name"] \
                        else query["device"] + ":" + ALARM_ZH_EN_MAP.get(query["details"])[lang]
                    if query['type'] == 1:
                        fault.append(query)
                    elif query['type'] == 3:
                        event.append(query)
                    else:
                        warn.append(query)
                except Exception as e:
                    pass
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success",
                                                                        # "detail": query_ins,
                                                                        "warn": warn,
                                                                        "fault": fault,
                                                                        "event": event,
                                                                        }})


class ResetFaultSendSmsCodeView(APIView):
    """故障复位下发发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mobile = request.data.get("mobile")
        if not mobile:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        # print(989, random_sms_code)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("故障复位下发发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else "SMS code send failed"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("reset" + str(mobile), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"Phone number:{mobile} SMS sent successfully",
                },
            }
        )


class ResetFaultSMSCodeCheckView(APIView):
    """故障复位下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.ResetFaultSMSCodeCheckSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error(f"故障复位下发:参数校验不通过{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("reset" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("故障复位下发:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else "Phone number does not match current login username",
                    },
                }
            )

        try:
            unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"储能单元不存在！" if lang == 'zh' else f"Storage unit does not exist!",
                    },
                }
            )
        topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        pcs = unit.pcs
        token = aes.encrypt(unit.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{"REFau": "1", "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"故障复位下发:下发指令{json_message}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "故障复位下发指令成功" if lang == 'zh' else "Fault reset down instruction successful.",
                },
            }
        )


class UnitPowerSMSCodeCheckView(APIView):
    """pcs功率下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.UnitPowerCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("pcs功率下发:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"pcs功率下发:{e}")
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": e.args[0],
                    },
                }
            )

        conn = get_redis_connection("default")
        conn.delete("unit_power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("pcs功率下发:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else "Phone number does not match current login username",
                    },
                }
            )

        power = ser.validated_data["power"]

        unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        pcs = unit.pcs
        topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(unit.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{"APS": str(power * 10), "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"pcs功率下发:下发参数{json_message}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略下发成功" if lang == 'zh' else "Control strategy down successful.",
                },
            }
        )


class UnitPowerSendSmsCodeView(APIView):
    """pcs功率发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mobile = request.data.get("mobile")
        if not mobile:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("pcs功率发送短信:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else "Failed to send SMS verification code."},
                }
            )
        conn = get_redis_connection("default")
        conn.set("unit_power" + str(mobile), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"Phone number:{mobile}Send SMS successfully.",
                },
            }
        )


class UnitSwitchSendSmsCodeView(APIView):
    """PCS开关发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mobile = request.data.get("mobile")
        if not mobile:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("PCS开关发送短信:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else "Failed to send SMS verification code."},
                }
            )
        conn = get_redis_connection("default")
        conn.set(
            "unit_switch" + str(mobile),
            random_sms_code,
            ex=60 * 5,
        )  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"Phone number:{mobile}Send SMS successfully.",
                },
            }
        )


class UnitSwitchSMSCodeCheckView(APIView):
    """PCS开关下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.UnitSwitchCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("PCS开关下发指令:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("PCS开关下发指令:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        conn = get_redis_connection("default")
        conn.delete("unit_switch" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("PCS开关下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else "Mobile number does not match current login username",
                    },
                }
            )
        switch_dic = {
            1: "CDSta",  # 启动
            0: "CDSto",  # 停止
        }
        switch = ser.validated_data["switch"]
        switch = switch_dic.get(int(switch))
        try:
            unit_ins = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        except models.Unit.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "储能单元不存在" if lang == 'zh' else 'The energy storage unit does not exist.'},
                }
            )
        topic = f"req/database/parameter/{unit_ins.station.english_name}/{unit_ins.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        pcs = unit_ins.pcs
        token = aes.encrypt(unit_ins.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{switch: "1", "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"PCS开关下发指令:下发指令{json_message}")
        conn = get_redis_connection("default")
        redis_key = str(unit_ins.station.english_name + "_" + unit_ins.station.app + "_" + pcs + "_" + "EFPR")
        conn.set(redis_key, str(ser.validated_data["switch"]), ex=150)
        success_log.info("PCS开关下发指令:写入缓存")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        success_log.info("PCS开关下发指令:最新数据拉取成功")
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "PCS开关下发指令成功" if lang == 'zh' else "PCS switch command sent successfully.",
                },
            }
        )


class VirtuallySendSmsCodeView(APIView):
    """需量下发发送短信"""

    # authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mobile = request.data.get("mobile")
        if not mobile:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("需量下发发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else 'Failed to send SMS verification code.'},
                }
            )
        conn = get_redis_connection("default")
        conn.set("virtually" + str(mobile), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"Mobile number:{mobile} SMS sent successfully",
                },
            }
        )


class VirtuallyCheckParamView(APIView):
    """需量下发回显"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '参数不完整' if lang == 'zh' else 'Incomplete parameters'},
                }
            )
        try:
            master_station = models.MaterStation.objects.get(id=id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": '查询失败，数据不存在' if lang == 'zh' else 'Query failed, data does not exist.'},
                }
            )
        # slave_station = master_station.stationdetails_set.filter(
        #     Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0)).first()

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        conn = get_redis_connection("3")
        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems:
            ems = eval(eval(ems))
        else:
            ems = {}
        MFixDemandRef = ems.get('MFixDemandRef') if ems.get('MFixDemandRef') and ems.get('MFixDemandRef') not in EMPTY_STR_LIST else 0
        TFM = ems.get('TFM') if ems.get('TFM') and ems.get('TFM') not in EMPTY_STR_LIST else 0
        TPRT = ems.get('TPRT') if ems.get('TPRT') and ems.get('TPRT') not in EMPTY_STR_LIST else 0
        Pccth = ems.get('Pccth') if ems.get('Pccth') and ems.get('Pccth') not in EMPTY_STR_LIST else 0
        data = {
            'MFixDemandRef': MFixDemandRef,  # 需量功率
            'TFM': TFM,  # 变压器容量
            'TPRT': TPRT,  # 变压器有功比例
            'Pccth': Pccth  # 防逆流阈值
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class VirtuallySMSCodeCheckView(APIView):
    """需量下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = VirtuallyCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error(f"需量下发:参数校验不通过{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"需量下发:参数校验不通过{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        conn = get_redis_connection("default")
        conn.delete("virtually" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else "Mobile number does not match current login username.",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        # app = models.StationDetails.objects.filter(english_name=storage_name).values("app").first()
        # app = app.get("app")

        master_stations = models.MaterStation.objects.filter(english_name=master_station_name, is_delete=0).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!" if lang == 'zh' else f"Master station {master_station_name} does not exist!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(slave_station.english_name)
        body = []
        if request.data.get('virtually'):
            body.append({'FixDemandRef': str(request.data.get('virtually')), "type": "parameter"})
        if request.data.get('capacity'):
            body.append({'TFMC': str(request.data.get('capacity')), "type": "parameter"})
        if request.data.get('proportion'):
            if not 0 <= float(request.data.get('proportion')) <= 1:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {
                            "message": "error",
                            "detail": "变压器有功比例超过限制范围：0-1" if lang == 'zh' else "Transformer active ratio exceeds the limit range: 0-1",
                        },
                    }
                )
            body.append({'TPRTC': str(request.data.get('proportion')), "type": "parameter"})
        if request.data.get('threshold'):
            body.append({'PccthC': str(request.data.get('threshold')), "type": "parameter"})
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": "EMS",
            "body": body,
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )

        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"需量下发:下发参数为{json.dumps(message)}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "操作成功！" if lang == 'zh' else "Operation succeeded!",
                },
            }
        )


class PowerSendSmsCodeView(APIView):
    """全站功率发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mobile = request.data.get("mobile")
        if not mobile:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error",
                             "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("全站功率发送短信:短信发送失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else "SMS verification code issuance failed."},
                }
            )
        """目前没有接入第三方发短信程序"""
        conn = get_redis_connection("default")
        conn.set("power" + str(mobile), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"Phone number:{mobile} SMS sent successfully.",
                },
            }
        )


class PowerSMSCodeCheckView(APIView):
    """全站功率下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.PowerCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("全站功率下发指令:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("全站功率下发指令:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        conn = get_redis_connection("default")
        conn.delete("power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("全站功率下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else "The mobile phone number does not match the current login username.",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        power = ser.validated_data["power"]
        # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
        # app = app.get("app")

        master_stations = models.MaterStation.objects.filter(english_name=master_station_name, is_delete=0).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!" if lang == 'zh' else f"The master station {master_station_name} does not exist!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(slave_station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": "EMS",
            "body": [{"EFPR": str(power), "type": "parameter"}],
        }
        success_log.info(f"全站功率下发指令:下发参数为{json.dumps(message)}")
        json_message = json.dumps(message)
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        client.publish(topic, json_message)
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "功率指标下发成功" if lang == 'zh' else "Power indicator issued successfully.",
                },
            }
        )


class StationoverviewView(APIView):
    """
    生产概览
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        types = request.data.get('type', '0')  # 0：电网侧功率； 1：SOC；2：有功功率；3：逐日充放电量；4：逐日收益
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：字段校验失败！' if lang == 'zh' else 'Data board: Field verification failed!',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'Data board: The station does not exist or the current user does not have permission. Please check the parameters!',
                    }
                }
            )
        # 连接清洗库
        connection = self.connection
        cursor = connection.cursor()
        if types == '0' or types == '2':
            # slave_station = staiton.stationdetails_set.filter(Q(slave=-1)|Q(slave=0), Q(pack=-1)|Q(pack=0)).all()
            # 兼容标准站 & 标准主从站 & EMS级联主从站模式
            slave_station = staiton.stationdetails_set.filter(is_delete=0, english_name=staiton.english_name).all()
        else:
            slave_station = staiton.stationdetails_set.filter(is_delete=0).all()
        # slave_station = staiton.stationdetails_set.filter(~Q(slave=0)).all()
        bms_unit_list = []
        pcs_unit_list = []
        station_list = []
        staiton_frontloaded_list = []  # 电表前置列表
        staiton_frontloaded_pcs_list = []  # 电表前置pcs列表
        # 处理主从模式
        for s_info in slave_station:
            if s_info.meter_position == 1:
                staiton_frontloaded_list.append(s_info.english_name)
                units = s_info.unit_set.filter(is_delete=0).values('pcs')
                for unit in units:
                    staiton_frontloaded_pcs_list.append(unit.get('pcs'))
            station_list.append(s_info.english_name)
            # 处理所有单元
            units = s_info.unit_set.filter(is_delete=0).values('bms', 'pcs')
            for unit in units:
                bms_unit_list.append(unit.get('bms'))
                pcs_unit_list.append(unit.get('pcs'))

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'

        # 时间节点，防止某时段数据为空不返回
        # SOC
        if types == '1':
            sql = """SELECT  avg(soc) as soc, DATE_FORMAT(time,'%H:%i') as new_time
                              FROM dwd_measure_bms_data_storage_3
                              WHERE 
                              time 
                              BETWEEN '{}'
                              AND '{}'
                              AND MOD(MINUTE(time), 5) = 0 """.format(s_time, e_time)
            if len(station_list) == 1:
                sql += "AND station_name = '{}'".format(station_list[0])
            else:
                sql += "AND station_name in {}".format(tuple(station_list))
            if len(bms_unit_list) == 1:
                sql += "AND device = '{}'".format(bms_unit_list[0])
            else:
                sql += 'AND device in {}'.format(tuple(bms_unit_list))
            sql += " GROUP BY new_time ORDER BY new_time"
            try:
                # 获取查询结果
                cursor.execute(sql)
                result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )
            data = create_time_mapping()
            for i in result:
                t = i.get('new_time')
                soc = i.get('soc')
                if data.get(t):
                    data[t] = soc

        # 电网侧功率
        elif types == '0':
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                DATE_FORMAT(time,'%H:%i') as new_time, avg(p_gird)
                            FROM
                                ads_report_loading_data
                            WHERE
                                time
                                BETWEEN '{}'
                                AND '{}'
                                AND state_pcc = 0
                                """.format(s_time, e_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                p_sql += " GROUP BY new_time ORDER BY new_time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                            }
                        }
                    )
            dwd_sql = """SELECT
                                mfixdemandref,
                                tfm,
                                tprt,
                                device,
                                DATE_FORMAT(time,'%H:%i') as time
                        FROM dwd_measure_ems_data_storage
                        WHERE
                            time
                            BETWEEN '{}'
                            AND '{}'
                            AND device = 'EMS'
                            AND MOD(MINUTE(time), 5) = 0
                             """.format(s_time, e_time)

            if len(station_list) == 1:
                dwd_sql += "AND station_name = '{}'".format(station_list[0])
            else:
                dwd_sql += "AND station_name in {}".format(tuple(station_list))

            try:
                # 获取查询结果
                cursor.execute(dwd_sql)
                dwd_result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )

            data = {
                'required_power': create_time_mapping(),  # 需量功率
                'transformer_safety_capacity': create_time_mapping(),  # 变压器安全容量
                'grid_side_power': create_time_mapping(),  # 电网侧功率
            }

            dwd_res = {i.get('time'): [i.get('mfixdemandref'), i.get('tfm'), i.get('tprt')] for i in dwd_result}
            for k, v in dwd_res.items():
                if v[0] is not None:
                    if data['required_power'].get(k):
                        data['required_power'][k] = v[0]
                if v[2] is not None:
                    if data['transformer_safety_capacity'].get(k):
                        data['transformer_safety_capacity'][k] = v[2] * v[1]
            for i in p_result:
                t = i[0]
                if i[1] is not None:
                    if data['grid_side_power'].get(t):
                        data['grid_side_power'][t] = i[1]

        # 有功功率
        elif types == '2':
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                DATE_FORMAT(time,'%H:%i') as new_time, sum(p_load) as p_load, sum(p) as p
                            FROM
                                ads_report_loading_data
                            WHERE
                                time
                                BETWEEN '{}'
                                AND '{}'
                                AND state_pcc = 0
                                """.format(s_time, e_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                p_sql += " GROUP BY new_time ORDER BY new_time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                            }
                        }
                    )
            data = {
                'pcc': create_time_mapping(),  # 负荷功率
                'p': create_time_mapping(),  # 储能功率
                'aim_power': create_time_mapping()  # 目标功率
            }

            master_station = models.MaterStation.objects.filter(id=id, is_delete=0).first()
            power = master_station.stationdetails_set.filter(is_delete=0).aggregate(p_sum=Sum('rated_power'))
            power = power.get('p_sum')
            tactics_sql = f"""
                        SELECT
                               time, station_name, device, slave, pack, type, ots, rlh1p, rlh2p, rlh3p, rlh4p, rlh5p, rlh6p, rlh7p, rlh8p, rlh9p, 
                            rlh10p, rlh11p, rlh12p, rlh13p, rlh14p, rlh15p, rlh16p, rlh17p, rlh18p, rlh19p, rlh20p, rlh21p, rlh22p, rlh23p, rlh24p,
                             rlh1f, rlh2f, rlh3f, rlh4f, rlh5f, rlh6f, rlh7f, rlh8f, rlh9f, rlh10f, rlh11f, rlh12f, rlh13f, rlh14f, rlh15f, rlh16f, 
                             rlh17f, rlh18f, rlh19f, rlh20f, rlh21f, rlh22f, rlh23f, rlh24f, rlh25p, rlh26p, rlh27p, rlh28p, rlh29p, rlh30p, rlh31p, 
                             rlh32p, rlh33p, rlh34p, rlh35p, rlh36p, rlh37p, rlh38p, rlh39p, rlh40p, rlh41p, rlh42p, rlh43p, rlh44p, rlh45p, rlh46p,
                              rlh47p, rlh48p, rlh25f, rlh26f, rlh27f, rlh28f, rlh29f, rlh30f, rlh31f, rlh32f, rlh33f, rlh34f, rlh35f, rlh36f, rlh37f,
                               rlh38f, rlh39f, rlh40f, rlh41f, rlh42f, rlh43f, rlh44f, rlh45f, rlh46f, rlh47f, rlh48f 
                        FROM
                            `dwd_measure_ems_data_storage_tscale` 
                        WHERE
                            station_name = '{master_station.english_name}' 
                            AND time BETWEEN '{inquire_time}' 
                            AND '{inquire_time + ' 23:59:59'}' 
                            AND device='EMS'
                            AND MOD(MINUTE(time), 5) = 0
                        ORDER BY
                            time DESC 
                            LIMIT 1
                        """
            try:
                # 获取查询结果
                cursor.execute(tactics_sql)
                tactics_result = cursor.fetchone()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                        }
                    }
                )
            for i in p_result:
                t = i[0]
                if i[1] is not None:
                    if data['pcc'].get(t):
                        data['pcc'][t] = i[1]
                    # k1：小时；k2：分钟;
                    k1 = int(t[:2])
                    k1 = 24 if k1 == 0 else k1
                    k2 = int(t[3:5])
                    k = k1 if k2 < 30 else k1 + 24  # 策略修改为半小时一个点位，通过判断分钟是否小于等于半小时，来计算取对应的点位
                    if tactics_result:
                        if k > 24 and tactics_result.get(f'rlh{k}p') is None:
                            k = k-24
                        if tactics_result.get(f'rlh{k}p') is not None and tactics_result.get(f'rlh{k}f') is not None:
                            if data['aim_power'].get(t):
                                data['aim_power'][t] = float(tactics_result.get(f'rlh{k}p')) * float(tactics_result.get(f'rlh{k}f')) * power
                        else:
                            if data['aim_power'].get(t):
                                data['aim_power'][t] = '--'
                    else:
                        if data['aim_power'].get(t):
                            data['aim_power'][t] = '--'
                if i[2] is not None:
                    if data['p'].get(t):
                        data['p'][t] = i[2]

        # 逐日充放电量
        elif types == '3':
            s_time = datetime.datetime.strftime(
                datetime.datetime.strptime(inquire_time, '%Y-%m-%d') - datetime.timedelta(days=30), '%Y-%m-%d')
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                discharg_sql = """SELECT
                                day, sum(v_chag), sum(v_disg), avg(chag_soc), avg(disg_soc)
                            FROM
                                ads_report_chag_disg_union_1d
                            WHERE
                                day
                                BETWEEN '{}'
                                AND '{}'
                                AND station_type <= 1 """.format(s_time, inquire_time)
                if len(station_list) == 1:
                    discharg_sql += "AND station = '{}'".format(station_list[0])
                else:
                    discharg_sql += "AND station in {}".format(tuple(station_list))
                discharg_sql += " GROUP BY day ORDER BY day"

                # 获取查询结果
                try:
                    ads_cursor.execute(discharg_sql)
                    res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                            }
                        }
                    )

                data = {
                    'chag': {},
                    'disg': {},
                    'charge_discharge_efficiency': {}
                }
                for i in res:
                    t = datetime.datetime.strftime(i[0], '%Y-%m-%d')
                    data['chag'][t] = i[1] if i[1] else '--'
                    data['disg'][t] = i[2] if i[2] else '--'
                    if i[3] and i[4] and i[1]:
                        eff = i[2] / i[4] / (i[1] / i[3]) * 100
                        if eff > 100:
                            data['charge_discharge_efficiency'][t] = '--'
                        else:
                            if eff < 85:
                                eff = 85
                            data['charge_discharge_efficiency'][t] = round(eff, 2)
                    else:
                        data['charge_discharge_efficiency'][t] = '--'

        # 逐日收益
        elif types == '4':
            if len(station_list) == 1:
                station_list.append(-1)  # 防止查询异常
            s_time = datetime.datetime.strftime(
                datetime.datetime.strptime(inquire_time, '%Y-%m-%d') - datetime.timedelta(days=30), '%Y-%m-%d')
            income_sql = f"""SELECT 
                                DAY,
                                sum(day_income)
                            FROM
                                ads_report_station_income_1d 
                            WHERE
                                 station in {tuple(station_list)}
                                 AND DAY >= '{s_time}' 
                            GROUP BY `day`
                            ORDER BY
                                `day`"""
            data = {}
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                try:
                    ads_cursor.execute(income_sql)
                    income_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error("逐日收益查询失败：",e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'QUERY FAILURE！',
                            }
                        }
                    )

            for income in income_res:
                t = datetime.datetime.strftime(income[0], '%Y-%m-%d')
                data[t] = round(income[1], 2) if income[1] not in EMPTY_STR_LIST else 0



        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": 'types参数值超过限制' if lang == 'zh' else 'The value of the types parameter exceeds the limit!',
                    }
                }
            )

        cursor.close()
        connection.close()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class ControlSendSmsCodeView(APIView):
    """就地控制发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')

        mobile = request.data.get("mobile")
        if not mobile:
                return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "手机号不能为空" if lang == 'zh' else "Mobile number cannot be empty."},
                }
            )

        if not re.match("^1[3-9]\d{9}$", mobile):
                return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect."},
                }
            )

        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(mobile, random_sms_code)
        if code != 200:
            error_log.error("就地控制发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败" if lang == 'zh' else "SMS verification code failed"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("control" + str(mobile), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{mobile}发送短信成功成功" if lang == 'zh' else f"SMS verification code has been sent to {mobile}",
                },
            }
        )


class ControlListView(APIView):
    """就地控制列表"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.StationSerializer(data=request.query_params, context={'lang': lang})
        try:
            if not ser.is_valid():
                error_log.error("就地控制列表:参数校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": e.args[0]},
                }
            )
        stations = models.StationDetails.objects.filter(is_delete=0, master_station__english_name=ser.validated_data['station']).all()

        data = {
            'is_master': 1 if stations.count() > 1 else 0,
            'slave_control_strategys': []
        }
        conn = get_redis_connection("3")
        conn_ = get_redis_connection("default")

        master_station = models.MaterStation.objects.get(english_name=ser.validated_data['station'], is_delete=0)
        m_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
        # 查看主站及非主从站的就地控制状态
        redis_key_1 = str(
            m_station.english_name + "-" + m_station.app + "-" + "AEn")
        redis_AEn = conn_.get(redis_key_1)
        if redis_AEn:
            success_log.info("项目清单:当期数据为缓存数据")
            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
        else:
            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', m_station.english_name, 'EMS')
            measure_ems = conn.get(key1)
            if measure_ems:
                measure_ems_str = measure_ems.decode("utf-8")
                measure_ems_dict = json.loads(json.loads(measure_ems_str))

                if measure_ems_dict.get('AEn') and measure_ems_dict.get('AEn') not in EMPTY_STR_LIST:
                    AEn = int(measure_ems_dict.get('AEn'))
                else:
                    AEn = '--'
            else:
                AEn = '--'

        data['stations__control_strategy'] = int(AEn) if AEn != '--' else AEn
        for station in stations:

            if station.slave != -1 or station.pack != -1:
                units = station.unit_set.filter(is_delete=0).all()
                for unit in units:
                    key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                                   unit.pcs)
                    status_pcs = conn.get(key3)
                    if status_pcs:
                        status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                    else:
                        status_pcs_dict = {}

                    if not status_pcs_dict:
                        redis_key_ = str(
                            station.english_name + "-" + station.app + "-" + "AEn")
                        redis_AEn = conn_.get(redis_key_)
                        if redis_AEn not in EMPTY_STR_LIST:
                            success_log.info("项目清单:当期数据为缓存数据")
                            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
                        else:
                            AEn = '--'
                    else:
                        redis_key_ = str(
                            station.english_name + "-" + station.app + "-" + "AEn")
                        redis_AEn = conn_.get(redis_key_)
                        if redis_AEn not in EMPTY_STR_LIST:
                            success_log.info("项目清单:当期数据为缓存数据")
                            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
                        else:
                            key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                                           'EMS')
                            measure_ems_1 = conn.get(key2)
                            if measure_ems_1:
                                measure_ems_str_1 = measure_ems_1.decode("utf-8")
                                measure_ems_dict_1 = json.loads(json.loads(measure_ems_str_1))

                                AEn = measure_ems_dict_1.get('AEn') \
                                    if measure_ems_dict_1.get('AEn') not in EMPTY_STR_LIST else '--'
                            else:
                                AEn = '--'

                    if lang == 'zh':
                        data['slave_control_strategys'].append({"title": unit.unit_new_name + '-就地控制（从）', "id": unit.id,
                                                          "control_strategy": AEn})
                    else:
                            data['slave_control_strategys'].append({"title": unit.en_unit_new_name + '-Local control (slave)',
                                                          "id": unit.id, "control_strategy": AEn})

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
            }
        )


class ControlSMSCodeCheckView(APIView):
    """就地控制下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.ControlCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("就地控制下发指令:参数校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("就地控制下发指令:参数校验不通过")
            print(2343, traceback.print_exc())
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": e.args[0]},
                }
            )

        conn = get_redis_connection("default")
        conn.delete("control" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符" if lang == 'zh' else 'The mobile phone number does not match the current login username.',
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        control_strategy = ser.validated_data["control_strategy"]
        unit_id = ser.validated_data.get('unit_id', None)

        # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
        # app = app.get("app")
        master_stations = models.MaterStation.objects.filter(english_name=master_station_name, is_delete=0).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!" if lang == 'zh' else f'The master station {master_station_name} is not exist!',
                    },
                }
            )
        master_station = master_stations.first()

        if not unit_id:
            # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
            # 兼容标准站 & 标准主从站 & EMS级联主从站模式
            slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

            topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(slave_station.english_name)
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    # {"ENAu": str(control_strategy), "type": "parameter"},
                    {"EAEn": str(control_strategy), "type": "parameter"}
                ],
            }
            client = mqtt.Client()
            client.on_connect = on_connect
            client.on_message = on_message
            client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
            client.connect(
                host=settings.MQTT_SERVER,
                port=settings.MQTT_PORT,
                keepalive=settings.MQTT_KEEPALIVE,
            )

            json_message = json.dumps(message)
            client.publish(topic, json_message)
            success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
            conn = get_redis_connection("default")
            redis_key = str(slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
            success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
            conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)

        else:
            units = models.Unit.objects.filter(is_delete=0, id=unit_id, station__master_station__english_name=master_station_name)
            if not units.exists():
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": f"单元{unit_id}不存在!" if lang == 'zh' else f'Unit {unit_id} does not exist!',
                        },
                    }
                )
            unit = units.first()
            topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(unit.station.english_name)
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    # {"ENAu": str(control_strategy), "type": "parameter"},
                    {"EAEn": str(control_strategy), "type": "parameter"}
                ],
            }
            client = mqtt.Client()
            client.on_connect = on_connect
            client.on_message = on_message
            client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
            client.connect(
                host=settings.MQTT_SERVER,
                port=settings.MQTT_PORT,
                keepalive=settings.MQTT_KEEPALIVE,
            )

            json_message = json.dumps(message)
            client.publish(topic, json_message)
            success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
            conn = get_redis_connection("default")
            redis_key = str(unit.station.english_name + "-" + unit.station.app + "-" + "AEn")
            success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
            conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)

        success_log.info("就地控制下发指令:最新数据拉取成功")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "下发成功！" if lang == 'zh' else "Successfully issued!",
                },
            }
        )


class ProjectAreaViews(APIView):
    """
    台区项目信息详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        _id = request.query_params.get('id')
        try:
            project = models.Project.objects.get(id=_id, application_scenario=1, is_used=1)
        except Exception as e:
            error_log.error("查询项目不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "查询项目信息不存在", "detail": ""},
                }
            )
        master_stations = project.materstation_set.filter(is_delete=0).all()
        conn = get_redis_connection("3")
        conn_ = get_redis_connection("default")
        data = {
            'id': project.id,
            'name': project.name,    # 项目名称
            'address': project.address,    # 项目地址
            'run_day': (datetime.datetime.now() - project.in_time).days,    # 运行天数
            'rated_power': project.rated_power,    # 额度功率
            'rated_capacity': project.rated_capacity,    # 额度容量
            'unit_count': 0,    # 储能单元数量
            'off_line_unit_count': 0,    # 离线单元数量
            'fault_unit_count': 0,    # 故障单元数量
            'ammeter_count': master_stations.filter(is_account=1).count(),    # 结算电表数量
            'battery_count': 0,    # 电池数量
            'load_switch': '--',    # 就地控制开关
            'station_id': project.materstation_set.first().id  # 并网点ID
        }
        # 查看主站及非主从站的就地控制状态

        for master in master_stations:
            # 台区项目只有一个并网点
            station_app = master.stationdetails_set.filter(english_name=master.english_name).first()
            redis_key_1 = str(
                master.english_name + "-" + station_app.app + "-" + "AEn")
            redis_AEn = conn_.get(redis_key_1)
            if redis_AEn:
                success_log.info("项目清单:当期数据为缓存数据")
                AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
            else:
                key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', master.english_name, 'EMS')
                measure_ems = conn.get(key1)
                if measure_ems:
                    measure_ems_str = measure_ems.decode("utf-8")
                    measure_ems_dict = json.loads(json.loads(measure_ems_str))

                    if measure_ems_dict.get('AEn') and measure_ems_dict.get('AEn') not in EMPTY_STR_LIST:
                        AEn = int(measure_ems_dict.get('AEn'))
                    else:
                        AEn = '--'
                else:
                    AEn = '--'
            stations = master.stationdetails_set.filter(is_delete=0).all()
            for station in stations:
                units = station.unit_set.filter(is_delete=0)
                data['unit_count'] += units.count()
                for unit in units.all():
                    pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.pcs))
                    bms = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms))
                    if pcs:
                        pcs_dict = json.loads(json.loads(pcs.decode("utf-8")))
                    else:
                        pcs_dict = {}
                    if bms:
                        bms_dict = json.loads(json.loads(bms.decode("utf-8")))
                    else:
                        bms_dict = {}

                    if not bms_dict or not pcs_dict:
                        data['off_line_unit_count'] += 1
                    else:
                        GFault = float(bms_dict.get("GFault")) if (bms_dict.get("GFault") and bms_dict.get("GFault")
                                                            not in EMPTY_STR_LIST) else -2  # bms故障状态
                        Fault = float(pcs_dict.get("Fault")) if (pcs_dict.get("Fault") and pcs_dict.get("Fault")
                                                          not in EMPTY_STR_LIST) else -2  # pcs故障状态
                        if Fault and int(Fault) == 1 or GFault and int(GFault) == 1:
                            data['fault_unit_count'] += 1
        # 最新逻辑项目离线，单元直接离线
        project_status = (models.StationStatus.objects.filter(station__master_station__project_id=_id).
                          aggregate(Max('status')).get('status__max'))
        if project_status == 4:
            data['off_line_unit_count'] = data['unit_count']


        data['rated_power'] = round(float(data['rated_power']), 2)
        data['rated_capacity'] = round(float(data['rated_capacity']), 2)
        data['battery_count'] = round(float(data.get('unit_count')) * 260, 2)
        data['load_switch'] = AEn
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class StationAreaViews(APIView):
    """
    台区项目并网点信息详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        项目并网点信息
        :param request:
        :return:
        """
        request_id = request.user["user_id"]
        _id = request.query_params.get('id')  # 并网点ID
        if not _id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "参数错误", "detail": ""}
            })

        try:
            station = models.MaterStation.objects.get(id=_id, is_delete=0, userdetails__id=request_id)
        except models.MaterStation.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "该站不存在或当前用户没有权限，请检查参数！", "detail": ""}
            })

        today = datetime.date.today()
        english_names = [i.english_name for i in station.stationdetails_set.filter(is_delete=0).all()]
        if len(english_names) == 1:
            english_names.append(-1)  # 防止转tuple出现逗号的情况
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:

            # 今日充放电量
            day_sql = """
                        SELECT
                            sum( v_disg ) as disg,
                            sum( v_chag ) as chag,
                            avg( chag_soc ),
                            avg( disg_soc )
                        FROM
                            ads_report_chag_disg_union_1d
                        WHERE
                            station in {}
                            AND day = '{}'
                            AND station_type <=1""".format(tuple(english_names), today)

            # 累计充放电量
            all_sql = """
                        SELECT
                            sum( v_disg ) as disg,
                            sum( v_chag ) as chag,
                            avg( chag_soc ),
                            avg( disg_soc )
                        FROM
                            ads_report_chag_disg_union_1d
                        WHERE
                            station in {}
                            AND station_type <=1""".format(tuple(english_names))
            # 当月充放电量
            year_month = today.strftime('%Y-%m')
            start_time = datetime.date(int(year_month.split('-')[0]), int(year_month.split('-')[1]), 1).strftime(
                '%Y-%m-%d 00:00:00')
            end_time = datetime.date(int(year_month.split('-')[0]), int(year_month.split('-')[1]),
                                     calendar.monthrange(int(year_month.split('-')[0]),
                                                         int(year_month.split('-')[1]))[1]).strftime(
                '%Y-%m-%d 23:59:59')
            month_sql = """
                        SELECT
                            sum( v_disg ) as disg
                        FROM
                            ads_report_chag_disg_union_1d
                        WHERE
                            station in {}
                            AND station_type <=1 AND day >= '{}' AND day <= '{}'""".format(tuple(english_names), start_time, end_time)
            # 获取查询结果

            ads_cursor.execute(all_sql)
            charge_discharge_all = ads_cursor.fetchall()
            ads_cursor.execute(day_sql)
            charge_discharge_day = ads_cursor.fetchall()
            ads_cursor.execute(month_sql)

        # 计算充放电效率
        if 0 in charge_discharge_day[0] or None in charge_discharge_day[0]:
            discharge_efficiency_day = '--'
        else:
            discharge_efficiency_day = ((charge_discharge_day[0][0] / charge_discharge_day[0][3]) / \
                                              (charge_discharge_day[0][1] / charge_discharge_day[0][2])) * 100
            if discharge_efficiency_day < 85:
                discharge_efficiency_day = 85
            elif discharge_efficiency_day > 100:
                discharge_efficiency_day = '--'
            else:
                discharge_efficiency_day = round(discharge_efficiency_day, 2)
        disg_day = charge_discharge_day[0][0]
        chag_day = charge_discharge_day[0][1]

        disg_all = charge_discharge_all[0][0]
        chag_all = charge_discharge_all[0][1]

        # 实时监测
        conn = get_redis_connection("3")
        dump_energy = 0  # 剩余电量
        soc_list = []  # SOC
        discharge_status = 0  # 充放电状态 1：充电；0：静置；2：放电；-1离线
        power = 0    # 储能单元有功功率的和
        total_q = 0  #　储能单元无功功率的和
        p_load = 0  # 负荷功率

        # 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).first()

        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems:
            ems = eval(eval(ems))
        else:
            ems = {}
        tfm = round(float(ems.get('TFM')), 2) if ems.get('TFM') and ems.get('TFM') not in EMPTY_STR_LIST else float(station_ems.transformer_capacity)  # 变压器容量
        # tprt = round(float(ems.get('TPRT')), 2) if ems.get('TPRT') and ems.get('TPRT') not in EMPTY_STR_LIST else '--'
        # transformer_safety_capacity = tfm if '--' == tprt else tfm * tprt  # 变压器安全容量
        pcc = round(float(ems.get('PCC')), 2) if ems.get('PCC') and ems.get('PCC') not in EMPTY_STR_LIST else '--'  # 负荷表采集的无功功率
        EQ = round(float(ems.get('EQ')), 2) if ems.get('EQ') and ems.get(
            'EQ') not in EMPTY_STR_LIST else '--'  # 负荷表采集的无功功率

        # eq = round(float(ems.get('EQ')), 2) if ems.get('EQ') and ems.get('EQ') not in EMPTY_STR_LIST else '--'  # 总无功功率
        ehz = round(float(ems.get('EHZ')), 2) if ems.get('EHZ') and ems.get('EHZ') not in EMPTY_STR_LIST else '--'  # 频率
        cos = round(float(ems.get('PCOS')), 4) if ems.get('PCOS') and ems.get('PCOS') not in EMPTY_STR_LIST else '--'  # 功率因数(4位小数)

        for slave_station in station.stationdetails_set.filter(is_delete=0).all():
            for unit in slave_station.unit_set.filter(is_delete=0).all():
                bms = conn.get(
                    "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, unit.bms))
                if bms:
                    bms = eval(eval(bms))
                else:
                    bms = {}

                if bms.get('SE') and bms.get('SE') not in EMPTY_STR_LIST and dump_energy != '--':
                    dump_energy += float(bms.get('SE'))
                else:
                    dump_energy = '--'

                if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST:
                    soc_list.append(float(bms.get('SOC')))
                else:
                    soc_list.append('--')


                pcs = conn.get(
                    "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name, unit.pcs))
                if pcs:
                    pcs = eval(eval(pcs))
                else:
                    pcs = {}
                    discharge_status = -1

                if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST and power != '--':
                    power += float(pcs.get('P'))
                else:
                    power = '--'

                if pcs.get('Q') not in EMPTY_STR_LIST and total_q != '--':
                    total_q += float(pcs.get('Q'))
                else:
                    total_q = '--'

        # 计算多个单元的边界值
        for unit in models.Unit.objects.filter(is_delete=0,
                                               station__in=station.stationdetails_set.filter(is_delete=0).all()):
            # 判断单元充放电状态 -1：离线；1：充电；0：静置；2：放电
            pcs = conn.get(
                'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.station.english_name,
                                                        unit.pcs))
            if not pcs:
                discharge_status = -1
                break
            else:
                pcs = eval(eval(pcs))
                p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
                if p != '--':
                    if p > 1:
                        discharge_status = 2
                    elif p < -1:
                        discharge_status = 1
                    else:
                        discharge_status = 0 if discharge_status == 0 else discharge_status
                else:
                    discharge_status = -1
                    break

        # 电网测功率/总有功功率
        if pcc != '--' and power != '--':
            gridSide_power = round(pcc - power, 2)  # 电网侧功率
            p_load = round(pcc + power, 2)
        else:
            gridSide_power = '--'

        # 总无功功率
        if EQ != '--' and total_q != '--':
            eq_ = round(EQ - total_q, 2)
        else:
            eq_ = '--'

        # 用电设备是否从电网取电：1：取电；0：不取电
        if ems:
            power_on_condition = 1
        else:
            power_on_condition = 0
        if station.stationdetails_set.filter(is_delete=0).first().meter_position == 1:  # 电表前置
            if pcc != '--' and pcc <= 0:
                power_on_condition = 0
        else:
            if pcc != '--' and power != '--':
                if round(pcc, 2) == round(power, 2):
                    power_on_condition = 0
            else:
                power_on_condition = 0

        # 如果总有功功率小于0或为'--'，则取power_on_condition 取0
        if gridSide_power != '--' and gridSide_power <= 0:
            power_on_condition = 0

        data = {
            'disg_day': round(disg_day, 2) if disg_day != '--' and disg_day not in EMPTY_STR_LIST else disg_day,  # 昨日放电量
            'chag_day': round(chag_day, 2) if chag_day != '--' and chag_day not in EMPTY_STR_LIST else chag_day,  # 昨日充电量
            'disg_all': round(disg_all / 1000, 2) if disg_all != '--' and disg_all else disg_day,  # 累计放电量（MWh)
            'chag_all': round(chag_all / 1000, 2) if chag_all != '--' and chag_all else disg_day,  # 累计充电量（MWh)
            'discharge_efficiency_day': discharge_efficiency_day,  # 今日充放电效率
            'discharge_efficiency_all': station.efficiency,  # 累计充放电效率
            'dump_energy': round(dump_energy, 2) if dump_energy != '--' and dump_energy != None else '--',  # 剩余电量
            'soc': round(sum(soc_list) / len(soc_list), 2) if soc_list and '--' not in soc_list else '--',  # SOC
            'discharge_status': discharge_status,  # 充放电状态 1：充电；0：静置；2：放电; -1:离线
            'power': round(power, 2) if power != '--' and power != 0 else '--',  # 储能功率
            'transformer_safety_capacity': round(tfm, 2) if tfm != '--' else '--',  # 变压器安全容量 ===> 变压器容量
            # 'pcc': pcc,  # 负载功率
            'p_load': '--',  # 负荷功率
            'total_p': gridSide_power,  # 总有功功率
            'total_q': eq_,  # 总无功功率
            'total_ehz': ehz,  # 频率
            'total_cos': cos,  # 功率因数(4位小数)
            'total_load': '--',  # 负载率
            'power_on_condition': power_on_condition  # 用电设备是否从电网取电：1：取电；0：不取电
        }
        if gridSide_power != '--' and (tfm != '--' and tfm != 0):
            data['total_load'] = round(gridSide_power / tfm * 100, 2)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )

class StationAreaGridSidePower(APIView):
    """
    采集点电表详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        request_id = request.user["user_id"]
        _id = request.query_params.get('id')  # 并网点ID
        if not _id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "参数错误", "detail": ""}
            })

        try:
            station = models.MaterStation.objects.get(id=_id, is_delete=0, userdetails__id=request_id)
        except models.MaterStation.DoesNotExist as e:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {"message": "该站不存在或当前用户没有权限，请检查参数！", "detail": ""}
            })
        # Redis
        conn = get_redis_connection('3')

        data = {
            'voltage': {},  # 电压
            'electricity': {},  # 电流
            'active_power': {},  # 有功功率
            'active_power_all': 0,  # 总有功功率
            'reactive_power': {},  # 无功功率
            'reactive_power_all': 0,  # 总无功功率
            'frequency': 0,  # 频率
            'pe': {},  # 功率因数
            'pe_all': 0,  # 总功率因数
            'total_p': 0,  # 负载功率
            'total_load': 0  # 负荷率
        }

# 兼容标准站 & 标准主从站 & EMS级联主从站模式
        station_ems = station.stationdetails_set.filter(is_delete=0, english_name=station.english_name).first()

        # 测量量
        ems_measure = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems.english_name, 'EMS'))
        if ems_measure:
            ems_measure = eval(eval(ems_measure))
        else:
            ems_measure = {}
        data['voltage']['ab'] = float(ems_measure.get('EUAB')) if ems_measure.get('EUAB') not in EMPTY_STR_LIST else '--'
        data['voltage']['bc'] = float(ems_measure.get('EUBC')) if ems_measure.get('EUBC') not in EMPTY_STR_LIST else '--'
        data['voltage']['ca'] = float(ems_measure.get('EUCA')) if ems_measure.get('EUCA') not in EMPTY_STR_LIST else '--'
        data['electricity']['a'] = float(ems_measure.get('EIA')) if ems_measure.get('EIA') not in EMPTY_STR_LIST else '--'
        data['electricity']['b'] = float(ems_measure.get('EIB')) if ems_measure.get('EIB') not in EMPTY_STR_LIST else '--'
        data['electricity']['c'] = float(ems_measure.get('EIC')) if ems_measure.get('EIC') not in EMPTY_STR_LIST else '--'
        data['active_power']['a'] = float(ems_measure.get('EPA')) if ems_measure.get('EPA') not in EMPTY_STR_LIST else '--'
        data['active_power']['b'] = float(ems_measure.get('EPB')) if ems_measure.get('EPB') not in EMPTY_STR_LIST else '--'
        data['active_power']['c'] = float(ems_measure.get('EPC')) if ems_measure.get('EPC') not in EMPTY_STR_LIST else '--'
        data['active_power_all'] = float(ems_measure.get('PCC')) if ems_measure.get('PCC') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['a'] = float(ems_measure.get('EQA')) if ems_measure.get('EQA') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['b'] = float(ems_measure.get('EQB')) if ems_measure.get('EQB') not in EMPTY_STR_LIST else '--'
        data['reactive_power']['c'] = float(ems_measure.get('EQC')) if ems_measure.get('EQC') not in EMPTY_STR_LIST else '--'
        data['reactive_power_all'] = float(ems_measure.get('EQ')) if ems_measure.get('EQ') not in EMPTY_STR_LIST else '--'
        data['frequency'] = float(ems_measure.get('EHZ')) if ems_measure.get('EHZ') not in EMPTY_STR_LIST else '--'
        data['pe']['a'] = float(ems_measure.get('COSA')) if ems_measure.get('COSA') not in EMPTY_STR_LIST else '--'
        data['pe']['b'] = float(ems_measure.get('COSB')) if ems_measure.get('COSB') not in EMPTY_STR_LIST else '--'
        data['pe']['c'] = float(ems_measure.get('COSC')) if ems_measure.get('COSC') not in EMPTY_STR_LIST else '--'
        data['pe_all'] = float(ems_measure.get('PCOS')) if ems_measure.get('PCOS') not in EMPTY_STR_LIST else '--'
        tfm = float(ems_measure.get('TFM')) if ems_measure.get('TFM') and ems_measure.get('TFM') not in EMPTY_STR_LIST else '--'  # 变压器容量
        pcc = float(ems_measure.get('PCC')) if ems_measure.get('PCC') and ems_measure.get('PCC') not in EMPTY_STR_LIST else '--'  # 负荷功率
        data['total_p'] = pcc
        if data['active_power_all'] != '--' and (tfm != '--' and tfm != 0):
            data['total_load'] = round(data['active_power_all'] / tfm * 100, 2)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": data,
            },
        })

class StationAreaOverview(APIView):
    """
    监测数据折线图信息
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):

        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )

        station_list = [-1]
        ems_name = staiton.english_name
        slave_station = staiton.stationdetails_set.all()
        for s_info in slave_station:
            station_list.append(s_info.english_name)
        times = create_one_minute_list()
        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        load_sql = """SELECT  avg(pcc) as pcc, avg(eq) as eq, DATE_FORMAT(time,'%H:%i') as time, avg(eua) as a, avg(eub) as b, avg(euc) as c
                                 FROM dwd_measure_load_data_storage
                                 WHERE 
                                 time 
                                 BETWEEN '{}'
                                 AND '{}'
                                  """.format(s_time, e_time)
        load_sql += " AND station_name in {}".format(tuple(station_list))
        load_sql += " GROUP BY time ORDER BY time"

        # vol_sql = """SELECT  pccvolt_sys, DATE_FORMAT(time,'%H:%i') as time
        #                          FROM dwd_measure_ems_data_storage
        #                          WHERE
        #                          time
        #                          BETWEEN '{}'
        #                          AND '{}'
        #                          AND station_name = '{}' ORDER BY time""".format(s_time, e_time, ems_name)

        bms_sql = """SELECT  avg(soc) as soc, avg(bp) as bp, avg(bq) as bq, DATE_FORMAT(time,'%H:%i') as time
                                 FROM dwd_measure_bms_data_storage_3
                                 WHERE 
                                 time 
                                 BETWEEN '{}'
                                 AND '{}'
                                  """.format(s_time, e_time)
        bms_sql += " AND station_name in {}".format(tuple(station_list))
        bms_sql += " GROUP BY time ORDER BY time"
        try:
            # 获取查询结果
            self.cursor.execute(load_sql)
            load_res = self.cursor.fetchall()
            # self.cursor.execute(vol_sql)
            # vol_res = self.cursor.fetchall()
            self.cursor.execute(bms_sql)
            bms_res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()

        data = {
            'time': times,
            'p': {
                'total_p': [],  # 总有功
                'total_q': [],  # 总无功
                # 'total_loat': []  # 负荷功率
            },
            'vol': {
                'ab': [],
                'bc': [],
                'ca': []
            },  # 相电压
            'bms': {
                'total_p': [],  # 储能有功
                'total_q': [],  # 储能无功
                'total_soc': []  # soc
            },
            'up_vol': staiton.up_vol if staiton.up_vol else '--',
            'low_vol': staiton.low_vol if staiton.low_vol else '--'
        }
        vol_dict = {i['time']: {'a': i['a'], 'b': i['b'], 'c': i['c']} for i in load_res}
        load_dict = {i['time']: {'pcc': i['pcc'], 'eq': i['eq']} for i in load_res}
        bms_dict = {i['time']: {'bp': i['bp'], 'bq': i['bq'], 'soc': i['soc']} for i in bms_res}
        for v in times:
            load = load_dict.get(v)
            vol = vol_dict.get(v)
            bms = bms_dict.get(v)
            if load:
                data['p']['total_p'].append(load['pcc'] if load['pcc'] not in EMPTY_STR_LIST else '--')
                data['p']['total_q'].append(load['eq'] if load['eq'] not in EMPTY_STR_LIST else '--')
                # data['p']['total_loat'].append(load['pcc'] if load['pcc'] not in EMPTY_STR_LIST else '--')
            else:
                data['p']['total_p'].append('--')
                data['p']['total_q'].append('--')
                # data['p']['total_loat'].append('--')

            if bms:
                data['bms']['total_p'].append(bms['bp'] if bms['bp'] not in EMPTY_STR_LIST else '--')
                data['bms']['total_q'].append(bms['bq'] if bms['bq'] not in EMPTY_STR_LIST else '--')
                data['bms']['total_soc'].append(bms['soc'] if bms['soc'] not in EMPTY_STR_LIST else '--')
            else:
                data['bms']['total_p'].append('--')
                data['bms']['total_q'].append('--')
                data['bms']['total_soc'].append('--')
            if vol:
                data['vol']['ab'].append(vol['a'] if vol['a'] not in EMPTY_STR_LIST else '--')
                data['vol']['bc'].append(vol['b'] if vol['b'] not in EMPTY_STR_LIST else '--')
                data['vol']['ca'].append(vol['c'] if vol['c'] not in EMPTY_STR_LIST else '--')
            else:
                data['vol']['ab'].append('--')
                data['vol']['bc'].append('--')
                data['vol']['ca'].append('--')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )



class StationAreaChagDisg(APIView):
    """
    逐日充放电量信息
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )
        # 从站
        station_list = [-1]
        slave_station = staiton.stationdetails_set.all()
        for s_info in slave_station:
            station_list.append(s_info.english_name)

        s_time = datetime.datetime.strftime(
            datetime.datetime.strptime(inquire_time, '%Y-%m-%d') - datetime.timedelta(days=30), '%Y-%m-%d')
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            discharg_sql = """SELECT
                                        day, sum(v_chag), sum(v_disg), avg(chag_soc), avg(disg_soc)
                                    FROM
                                        ads_report_chag_disg_union_1d
                                    WHERE
                                        day
                                        BETWEEN '{}'
                                        AND '{}'
                                        AND station_type <= 1 """.format(s_time, inquire_time)
            if len(station_list) == 1:
                discharg_sql += "AND station = '{}'".format(station_list[0])
            else:
                discharg_sql += "AND station in {}".format(tuple(station_list))
            discharg_sql += " GROUP BY day ORDER BY day"

            # 获取查询结果
            try:
                ads_cursor.execute(discharg_sql)
                res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )

            data = {
                'time': [],
                'chag': [],
                'disg': [],
                'rate': []
            }
            for i in res:
                t = datetime.datetime.strftime(i[0], '%Y-%m-%d')
                data['time'].append(t)
                data['chag'].append(i[1] if i[1] not in EMPTY_STR_LIST else '--')
                data['disg'].append(i[2] if i[2] not in EMPTY_STR_LIST else '--')
                if i[3] and i[4] and i[1]:
                    eff = i[2] / i[4] / (i[1] / i[3]) * 100
                    if eff > 100:
                        charge_discharge_efficiency = '--'
                    else:
                        if eff < 85:
                            eff = 85
                        charge_discharge_efficiency = round(eff, 2)
                else:
                    charge_discharge_efficiency = '--'
                data['rate'].append(charge_discharge_efficiency)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class StationAreaOverviewLoad(APIView):
    """
    生产概览下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()


    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed.',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission, please check the parameters!',
                    }
                }
            )

        station_list = [-1]
        ems_name = staiton.english_name
        slave_station = staiton.stationdetails_set.all()
        for s_info in slave_station:
            station_list.append(s_info.english_name)
        times = create_one_minute_list()
        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        load_sql = """SELECT  avg(pcc) as pcc, avg(eq) as eq, DATE_FORMAT(time,'%H:%i') as time, avg(eua) as a, avg(eub) as b, avg(euc) as c
                                 FROM dwd_measure_load_data_storage
                                 WHERE 
                                 time 
                                 BETWEEN '{}'
                                 AND '{}'
                                  """.format(s_time, e_time)
        load_sql += " AND station_name in {}".format(tuple(station_list))
        load_sql += " GROUP BY time ORDER BY time"

        # vol_sql = """SELECT  pccvolt_sys, DATE_FORMAT(time,'%H:%i') as time
        #                          FROM dwd_measure_ems_data_storage
        #                          WHERE
        #                          time
        #                          BETWEEN '{}'
        #                          AND '{}'
        #                          AND station_name = '{}' ORDER BY time""".format(s_time, e_time, ems_name)

        bms_sql = """SELECT  avg(soc) as soc, avg(bp) as bp, avg(bq) as bq, DATE_FORMAT(time,'%H:%i') as time
                                 FROM dwd_measure_bms_data_storage_3
                                 WHERE 
                                 time 
                                 BETWEEN '{}'
                                 AND '{}'
                                  """.format(s_time, e_time)
        bms_sql += " AND station_name in {}".format(tuple(station_list))
        bms_sql += " GROUP BY time ORDER BY time"
        try:
            # 获取查询结果
            self.cursor.execute(load_sql)
            load_res = self.cursor.fetchall()
            # self.cursor.execute(vol_sql)
            # vol_res = self.cursor.fetchall()
            self.cursor.execute(bms_sql)
            bms_res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()
        data = []
        vol_dict = {i['time']: {'a': i['a'], 'b': i['b'], 'c': i['c']} for i in load_res}
        load_dict = {i['time']: {'pcc': i['pcc'], 'eq': i['eq']} for i in load_res}
        bms_dict = {i['time']: {'bp': i['bp'], 'bq': i['bq'], 'soc': i['soc']} for i in bms_res}
        for v in times:
            load = load_dict.get(v)
            vol = vol_dict.get(v)
            bms = bms_dict.get(v)
            if load:
                pcc = load['pcc'] if load['pcc'] not in EMPTY_STR_LIST else '--'
                eq = load['eq'] if load['eq'] not in EMPTY_STR_LIST else '--'
            else:
                pcc, eq = '--', '--'

            if bms:
                bp = bms['bp'] if bms['bp'] not in EMPTY_STR_LIST else '--'
                bq = bms['bq'] if bms['bq'] not in EMPTY_STR_LIST else '--'
                b_pcc = bms['soc'] if bms['soc'] not in EMPTY_STR_LIST else '--'
            else:
                bp, bq, b_pcc = '--', '--', '--'

            if vol:
                a = vol['a'] if vol['a'] not in EMPTY_STR_LIST else '--'
                b = vol['b'] if vol['b'] not in EMPTY_STR_LIST else '--'
                c = vol['c'] if vol['c'] not in EMPTY_STR_LIST else '--'
            else:
                a, b, c = '--', '--', '--'
            data.append([v, pcc, eq, a, b, c, staiton.low_vol if staiton.low_vol else '--', staiton.up_vol if staiton.up_vol else '--', b_pcc, bp, bq])


        # 逐日充放电量
        s_time = datetime.datetime.strftime(
            datetime.datetime.strptime(inquire_time, '%Y-%m-%d') - datetime.timedelta(days=30), '%Y-%m-%d')
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            discharg_sql = """SELECT
                                        day, sum(v_chag), sum(v_disg), avg(chag_soc), avg(disg_soc)
                                    FROM
                                        ads_report_chag_disg_union_1d
                                    WHERE
                                        day
                                        BETWEEN '{}'
                                        AND '{}'
                                        AND station_type <= 1 """.format(s_time, inquire_time)
            if len(station_list) == 1:
                discharg_sql += "AND station = '{}'".format(station_list[0])
            else:
                discharg_sql += "AND station in {}".format(tuple(station_list))
            discharg_sql += " GROUP BY day ORDER BY day"

            # 获取查询结果
            try:
                ads_cursor.execute(discharg_sql)
                res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                        }
                    }
                )

            ele_data = []
            for i in res:
                t = datetime.datetime.strftime(i[0], '%Y-%m-%d')
                if i[3] and i[4] and i[1]:
                    eff = i[2] / i[4] / (i[1] / i[3]) * 100
                    if eff > 100:
                        charge_discharge_efficiency = '--'
                    else:
                        if eff < 85:
                            eff = 85
                        charge_discharge_efficiency = round(eff, 2)
                else:
                    charge_discharge_efficiency = '--'
                ele_data.append([t, i[1] if i[1] not in EMPTY_STR_LIST else '--', i[2] if i[2] not in EMPTY_STR_LIST else '--', charge_discharge_efficiency])

        # 下载数据
        wb = Workbook()
        wb.encoding = 'utf-8'  # 定义编码格式
        st = wb.active  # 获取第一个工作表（sheet1）
        title = ['时刻', '变压器总有功功率（kW）', '变压器总无功功率（kVar）', 'A相电压（V）', 'B相电压（V）', 'C相电压（V）', '电压下阈值（V）', '电压上阈值（V）', '储能SOC（%）', '储能有功功率（kW）', '储能无功功率（kVar）']
        en_title = ['Time', 'Transformer Total Active Power（kW）', 'Transformer Total Reactive Power（kVar）', 'A-phase voltage（V）', 'B-phase voltage（V）', 'C-phase voltage（V）',
                    'Voltage low threshold（V）', 'Voltage upper threshold（V）', 'Energy Storage SOC（%）', 'Energy Storage Active Power（kW）', 'Energy Storage Reactive Power（kVar）']
        if lang == 'zh':
            for i in range(1, len(title) + 1):
                st.cell(row=1, column=i).value = title[i - 1]
        else:
            for i in range(1, len(en_title) + 1):
                st.cell(row=1, column=i).value = en_title[i - 1]

        for info in data:
            max_row = st.max_row + 1
            for _i, v in enumerate(info):
                st.cell(row=max_row, column=_i + 1).value = v

        sheet2 = wb.create_sheet(title='Sheet2')
        title2 = ['日期', '充电量（kWh）', '放电量（kWh）', '充放电效率（%）']
        en_title2 = ['Date', 'Charging Quantity（kWh）', 'Discharging Quantity（kWh）', 'Charge and Discharge Efficiency（%）']

        if lang == 'zh':
            for i in range(1, len(title2) + 1):
                sheet2.cell(row=1, column=i).value = title2[i - 1]
        else:
            for i in range(1, len(en_title2) + 1):
                sheet2.cell(row=1, column=i).value = en_title2[i - 1]
        for info in ele_data:
            max_row = sheet2.max_row + 1
            for _i, v in enumerate(info):
                sheet2.cell(row=max_row, column=_i + 1).value = v

        file_name = f"{staiton.project.name}-{inquire_time}-生产概览数据--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{staiton.project.name}-{inquire_time}-Production Overview Data--{int(time.time() * 1000)}.xlsx"

        file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
        wb.save(file_path)
        # 上传Minio
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        file_path = minio_client.upload_local_file(file_name, file_path, 'download')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )



class TrabAreaOverview(APIView):
    """
    变压器监测数据折线图
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%H:%i') as new_time, eua, eub, euc, 
                           eia, eib, eic, epa, epb, epc, eqa, eqb, eqc
                               FROM dwd_measure_load_data_storage
                               WHERE 
                               time 
                               BETWEEN '{}'
                               AND '{}' 
                            """.format(s_time, e_time)


        sql += "AND station_name = '{}'".format(staiton.english_name)
        sql += ' ORDER BY new_time'


        try:
            # 获取查询结果
            self.cursor.execute(sql)
            res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()

        # 补充坐标
        times = create_one_minute_list()
        data = {
            'time': times,
            'p': {
                'sa': [],  # A相有功
                'sb': [],  # B相有功
                'sc': [],  # C相有功
            },
            'q': {
                'sa': [],  # A相无功
                'sb': [],  # B相无功
                'sc': [],  # C相无功
            },
            'vol': {
                'ab': [],  # A相电压
                'bc': [],  # B相电压
                'ca': [],  # C相电压
            },
            'cur': {
                'a': [],  # A相电流
                'b': [],  # B相电流
                'c': [],  # C相电流
            },
            'up_vol': staiton.up_vol if staiton.up_vol else '--',
            'low_vol': staiton.low_vol if staiton.low_vol else '--'
        }
        res_dict = {}
        for i in res:
            res_dict[i['new_time']] = {
                'euab': i['eua'] if i['eua'] not in EMPTY_STR_LIST else '--',
                'eubc': i['eub'] if i['eub'] not in EMPTY_STR_LIST else '--',
                'euca': i['euc'] if i['euc'] not in EMPTY_STR_LIST else '--',
                'eia': i['eia'] if i['eia'] not in EMPTY_STR_LIST else '--',
                'eib': i['eib'] if i['eib'] not in EMPTY_STR_LIST else '--',
                'eic': i['eic'] if i['eic'] not in EMPTY_STR_LIST else '--',
                'epa': i['epa'] if i['epa'] not in EMPTY_STR_LIST else '--',
                'epb': i['epb'] if i['epb'] not in EMPTY_STR_LIST else '--',
                'epc': i['epc'] if i['epc'] not in EMPTY_STR_LIST else '--',
                'eqa': i['eqa'] if i['eqa'] not in EMPTY_STR_LIST else '--',
                'eqb': i['eqb'] if i['eqb'] not in EMPTY_STR_LIST else '--',
                'eqc': i['eqc'] if i['eqc'] not in EMPTY_STR_LIST else '--',
            }

        for v in times:
            info = res_dict.get(v)
            if info:
                data['p']['sa'].append(info.get('epa'))
                data['p']['sb'].append(info.get('epb'))
                data['p']['sc'].append(info.get('epc'))
                data['q']['sa'].append(info.get('eqa'))
                data['q']['sb'].append(info.get('eqb'))
                data['q']['sc'].append(info.get('eqc'))
                data['vol']['ab'].append(info.get('euab'))
                data['vol']['bc'].append(info.get('eubc'))
                data['vol']['ca'].append(info.get('euca'))
                data['cur']['a'].append(info.get('eia'))
                data['cur']['b'].append(info.get('eib'))
                data['cur']['c'].append(info.get('eic'))
            else:
                data['p']['sa'].append('--')
                data['p']['sb'].append('--')
                data['p']['sc'].append('--')
                data['q']['sa'].append('--')
                data['q']['sb'].append('--')
                data['q']['sc'].append('--')
                data['vol']['ab'].append('--')
                data['vol']['bc'].append('--')
                data['vol']['ca'].append('--')
                data['cur']['a'].append('--')
                data['cur']['b'].append('--')
                data['cur']['c'].append('--')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class TrabRealAreaOverview(APIView):
    """
    变压器实时数据监测信息
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        id = request.data.get('id')  # 主站ID
        request_id = request.user["user_id"]
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )

        conn = get_redis_connection("3")
        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', staiton.english_name, 'EMS'))
        data = {
            'transformer_safety_capacity': '--',  # 变压器容量
            'psa': '--',   # A相有功
            'psb': '--',   # B相有功
            'psc': '--',   # C相有功
            'qsa': '--',   # A相无功
            'qsb': '--',   # B相无功
            'qsc': '--',   # C相无功
            'vol_ab': '--',   # A相电压
            'vol_bc': '--',   # B相电压
            'vol_ca': '--',   # C相电压
            'cur_a': '--',   # A相电流
            'cur_b': '--',   # B相电流
            'cur_c': '--',   # C相电流
            'total_ehz': '--',   # 频率
            'total_cos': '--',   # 功率因数（4位小数）
        }

        if ems:
            ems = eval(eval(ems))
            data['transformer_safety_capacity'] = round(float(ems.get('TFM')), 2) if ems.get('TFM') not in EMPTY_STR_LIST else '--'
            data['psa'] = round(float(ems.get('EPA')), 2) if ems.get('EPA') not in EMPTY_STR_LIST else '--'
            data['psb'] = round(float(ems.get('EPB')), 2) if ems.get('EPB') not in EMPTY_STR_LIST else '--'
            data['psc'] = round(float(ems.get('EPC')), 2) if ems.get('EPC') not in EMPTY_STR_LIST else '--'
            data['qsa'] = round(float(ems.get('EQA')), 2) if ems.get('EQA') not in EMPTY_STR_LIST else '--'
            data['qsb'] = round(float(ems.get('EQB')), 2) if ems.get('EQB') not in EMPTY_STR_LIST else '--'
            data['qsc'] = round(float(ems.get('EQC')), 2) if ems.get('EQC') not in EMPTY_STR_LIST else '--'
            data['vol_ab'] = round(float(ems.get('EUA')), 2) if ems.get('EUA') not in EMPTY_STR_LIST else '--'
            data['vol_bc'] = round(float(ems.get('EUB')), 2) if ems.get('EUB') not in EMPTY_STR_LIST else '--'
            data['vol_ca'] = round(float(ems.get('EUC')), 2) if ems.get('EUC') not in EMPTY_STR_LIST else '--'
            data['cur_a'] = round(float(ems.get('EIA')), 2) if ems.get('EIA') not in EMPTY_STR_LIST else '--'
            data['cur_b'] = round(float(ems.get('EIB')), 2) if ems.get('EIB') not in EMPTY_STR_LIST else '--'
            data['cur_c'] = round(float(ems.get('EIC')), 2) if ems.get('EIC') not in EMPTY_STR_LIST else '--'
            data['total_ehz'] = round(float(ems.get('EHZ')), 2) if ems.get('EHZ') not in EMPTY_STR_LIST else '--'
            data['total_cos'] = round(float(ems.get('PCOS')), 4) if ems.get('PCOS') not in EMPTY_STR_LIST else '--'

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class TrabAreaOverviewLoad(APIView):
    """
    变压器监测数据折线图数据下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        id = request.data.get('id')  # 主站ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        request_id = request.user["user_id"]
        lang = request.headers.get("lang", 'zh')
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.get(id=id, userdetails__id=request_id, is_delete=0)
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else 'The station does not exist or the current user does not have permission, please check the parameters!',
                    }
                }
            )

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time,'%H:%i') as new_time, eua, eub, euc, 
                               eia, eib, eic, epa, epb, epc, eqa, eqb, eqc
                                   FROM dwd_measure_load_data_storage
                                   WHERE 
                                   time 
                                   BETWEEN '{}'
                                   AND '{}' 
                                """.format(s_time, e_time)

        sql += "AND station_name = '{}'".format(staiton.english_name)
        sql += ' ORDER BY new_time'

        load_sql = """SELECT  DATE_FORMAT(time,'%H:%i') as new_time, pcos, ehz
                                   FROM dwd_measure_load_data_storage
                                   WHERE 
                                   time 
                                   BETWEEN '{}'
                                   AND '{}' 
                                """.format(s_time, e_time)
        load_sql += "AND station_name = '{}'".format(staiton.english_name)
        load_sql += ' ORDER BY new_time'
        try:
            # 获取查询结果
            self.cursor.execute(sql)
            res = self.cursor.fetchall()
            self.cursor.execute(load_sql)
            load_res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()
        # 补充坐标
        times = create_one_minute_list()
        res_dict = {}
        for i in res:
            res_dict[i['new_time']] = {
                'euab': i['eua'] if i['eua'] not in EMPTY_STR_LIST else '--',
                'eubc': i['eub'] if i['eub'] not in EMPTY_STR_LIST else '--',
                'euca': i['euc'] if i['euc'] not in EMPTY_STR_LIST else '--',
                'eia': i['eia'] if i['eia'] not in EMPTY_STR_LIST else '--',
                'eib': i['eib'] if i['eib'] not in EMPTY_STR_LIST else '--',
                'eic': i['eic'] if i['eic'] not in EMPTY_STR_LIST else '--',
                'epa': i['epa'] if i['epa'] not in EMPTY_STR_LIST else '--',
                'epb': i['epb'] if i['epb'] not in EMPTY_STR_LIST else '--',
                'epc': i['epc'] if i['epc'] not in EMPTY_STR_LIST else '--',
                'eqa': i['eqa'] if i['eqa'] not in EMPTY_STR_LIST else '--',
                'eqb': i['eqb'] if i['eqb'] not in EMPTY_STR_LIST else '--',
                'eqc': i['eqc'] if i['eqc'] not in EMPTY_STR_LIST else '--',
            }

        load_res_dict = {}
        for i in load_res:
            load_res_dict[i['new_time']] = {
                'pcos': i['pcos'] if i['pcos'] not in EMPTY_STR_LIST else '--',  # 功率因数
                'ehz': i['ehz'] if i['ehz'] not in EMPTY_STR_LIST else '--'  #  频率
            }
        # 下载数据
        data = []
        for v in times:
            info = res_dict.get(v)
            load = load_res_dict.get(v)
            d = []
            if info:
                d.extend([inquire_time, v, info.get('epa'), info.get('epb'), info.get('epc'), info.get('eqa'),
                             info.get('eqb'),
                             info.get('eqc'), info.get('euab'), info.get('eubc'), info.get('euca'),
                          staiton.low_vol, staiton.up_vol,
                          info.get('eia'),
                             info.get('eib'),
                             info.get('eic')])
            else:
                d.extend([inquire_time, v, '--', '--', '--', '--', '--', '--', '--', '--', '--', staiton.low_vol if staiton.low_vol else '--', staiton.up_vol if staiton.up_vol else '--', '--', '--', '--'])
            if load:
                d.extend([load.get('ehz'), load.get('pcos')])
            else:
                d.extend(['--', '--'])

            data.append(d)

        # 下载数据
        wb = Workbook()
        wb.encoding = 'utf-8'  # 定义编码格式
        st = wb.active  # 获取第一个工作表（sheet1）
        title = ['日期', '时刻', '变压器A相有功功率（kW）', '变压器B相有功功率（kW）', '变压器C相有功功率（kW）', '变压器A相无功功率（kVar）', '变压器B相无功功率（kVar）',
                 '变压器C相无功功率（kVar）', '变压器A相电压（V）', '变压器B相电压（V）', '变压器C相电压（V）',
                 '电压下阈值（V）', '电压上阈值（V）', '变压器A相电流（A）', '变压器B相电流（A）', '变压器C相电流（A）', '频率（Hz）',
                 '功率因数']
        en_title = ['Date', 'Time', 'Phase A Active Power (kW)', 'Phase B Active Power (kW)',
                      'Phase C Active Power (kW)', 'Phase A Reactive Power (kVar)',
                      'Phase B Reactive Power (kVar)', 'Phase C Reactive Power (kVar)',
                      'A-phase voltage (V)', 'B-phase voltage (V)', 'C-phase voltage (V)',
                      'Voltage low threshold（V）', 'Voltage upper threshold（V）', 'Phase A Current (A)',
                      'Phase B Current (A)', 'Phase C Current (A)', 'Frequency (Hz)', 'Power Factor'
                   ]

        if lang == 'en':
            title = en_title
        for i in range(1, len(title) + 1):
            st.cell(row=1, column=i).value = title[i - 1]

        for info in data:
            max_row = st.max_row + 1
            for _i, v in enumerate(info):
                st.cell(row=max_row, column=_i + 1).value = v

        file_name = f"{staiton.project.name}-{inquire_time}-变压器监测数据--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{staiton.project.name}-{inquire_time}-Transformer Monitoring Data{int(time.time() * 1000)}.xlsx"
        file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
        wb.save(file_path)
        # 上传Minio
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        file_path = minio_client.upload_local_file(file_name, file_path, 'download')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )


class PCSAreaOverview(APIView):
    """
    PCS监测数据折线图
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证


    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        id = request.data.get('id')  # 储能单元ID
        inquire_time = request.data.get('inquire_time')  # 查询时间
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            unit = models.Unit.objects.get(id=id, is_delete=0)
        except models.Unit.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该单元不存在，请检查参数！',
                    }
                }
            )

        station_name = unit.station.english_name
        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time, '%H:%i') as new_time, pua, pub, puc, 
                            ia, ib, ic, pa, pb, pc, qa, qb, qc
                               FROM dwd_measure_pcs_data_storage
                               WHERE 
                               time 
                               BETWEEN '{}'
                               AND '{}' 
                            """.format(s_time, e_time)


        sql += "AND station_name = '{}' AND  device = '{}'".format(station_name, unit.pcs)
        sql += ' ORDER BY new_time'


        try:
            # 获取查询结果
            self.cursor.execute(sql)
            res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()

        # 补充坐标
        times = create_one_minute_list()
        data = {
            'time': times,
            'p': {
                'sa': [],  # A相有功
                'sb': [],  # B相有功
                'sc': [],  # C相有功
            },
            'q': {
                'sa': [],  # A相无功
                'sb': [],  # B相无功
                'sc': [],  # C相无功
            },
            'vol': {
                'ab': [],  # AB线电压
                'bc': [],  # BC线电压
                'ca': [],  # CA线电压
            },
            'cur': {
                'a': [],  # A相电流
                'b': [],  # B相电流
                'c': [],  # C相电流
            },
        }
        res_dict = {}
        for i in res:
            res_dict[i['new_time']] = {
                'puab': i['pua'] if i['pua'] not in EMPTY_STR_LIST else '--',
                'pubc': i['pub'] if i['pub'] not in EMPTY_STR_LIST else '--',
                'puca': i['puc'] if i['puc'] not in EMPTY_STR_LIST else '--',
                'ia': i['ia'] if i['ia'] not in EMPTY_STR_LIST else '--',
                'ib': i['ib'] if i['ib'] not in EMPTY_STR_LIST else '--',
                'ic': i['ic'] if i['ic'] not in EMPTY_STR_LIST else '--',
                'pa': i['pa'] if i['pa'] not in EMPTY_STR_LIST else '--',
                'pb': i['pb'] if i['pb'] not in EMPTY_STR_LIST else '--',
                'pc': i['pc'] if i['pc'] not in EMPTY_STR_LIST else '--',
                'qa': i['qa'] if i['qa'] not in EMPTY_STR_LIST else '--',
                'qb': i['qb'] if i['qb'] not in EMPTY_STR_LIST else '--',
                'qc': i['qc'] if i['qc'] not in EMPTY_STR_LIST else '--',
            }

        for v in times:
            info = res_dict.get(v)
            if info:
                data['p']['sa'].append(info.get('pa'))
                data['p']['sb'].append(info.get('pb'))
                data['p']['sc'].append(info.get('pc'))
                data['q']['sa'].append(info.get('qa'))
                data['q']['sb'].append(info.get('qb'))
                data['q']['sc'].append(info.get('qc'))
                data['vol']['ab'].append(info.get('puab'))
                data['vol']['bc'].append(info.get('pubc'))
                data['vol']['ca'].append(info.get('puca'))
                data['cur']['a'].append(info.get('ia'))
                data['cur']['b'].append(info.get('ib'))
                data['cur']['c'].append(info.get('ic'))
            else:
                data['p']['sa'].append('--')
                data['p']['sb'].append('--')
                data['p']['sc'].append('--')
                data['q']['sa'].append('--')
                data['q']['sb'].append('--')
                data['q']['sc'].append('--')
                data['vol']['ab'].append('--')
                data['vol']['bc'].append('--')
                data['vol']['ca'].append('--')
                data['cur']['a'].append('--')
                data['cur']['b'].append('--')
                data['cur']['c'].append('--')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class PCSRealAreaOverview(APIView):
    """
        变压器实时数据监测信息
        """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        id = request.data.get('id')  # 单元ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！',
                    }
                }
            )
        try:
            unit = models.Unit.objects.get(id=id, is_delete=0)
        except models.Unit.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该单元不存在，请检查参数！',
                    }
                }
            )


        station = unit.station

        conn = get_redis_connection("3")
        pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name, unit.pcs))
        data = {
            'rated_power': '--',  # 额定功率
            'rated_capacity': '--',  # 额定容量
            'psa': '--',  # A相有功
            'psb': '--',  # B相有功
            'psc': '--',  # C相有功
            'qsa': '--',  # A相无功
            'qsb': '--',  # B相无功
            'qsc': '--',  # C相无功
            'vol_ab': '--',  # A相电压
            'vol_bc': '--',  # B相电压
            'vol_ca': '--',  # C相电压
            'cur_a': '--',  # A相电流
            'cur_b': '--',  # B相电流
            'cur_c': '--',  # C相电流
            'total_pf': '--',  # 频率
            'total_cos': '--',  # 功率因数（4位小数）
        }

        data['rated_power'] = round(float(station.rated_power), 2) if station.rated_power not in EMPTY_STR_LIST else '--'
        data['rated_capacity'] = round(float(station.rated_capacity), 2) if station.rated_capacity not in EMPTY_STR_LIST else '--'
        if pcs:
            pcs = eval(eval(pcs))
            data['psa'] = round(float(pcs.get('PA')), 2) if pcs.get('PA') not in EMPTY_STR_LIST else '--'
            data['psb'] = round(float(pcs.get('PB')), 2) if pcs.get('PB') not in EMPTY_STR_LIST else '--'
            data['psc'] = round(float(pcs.get('PC')), 2) if pcs.get('PC') not in EMPTY_STR_LIST else '--'
            data['qsa'] = round(float(pcs.get('QA')), 2) if pcs.get('QA') not in EMPTY_STR_LIST else '--'
            data['qsb'] = round(float(pcs.get('QB')), 2) if pcs.get('QB') not in EMPTY_STR_LIST else '--'
            data['qsc'] = round(float(pcs.get('QC')), 2) if pcs.get('QC') not in EMPTY_STR_LIST else '--'
            data['vol_ab'] = round(float(pcs.get('PUa')), 2) if pcs.get('PUa') not in EMPTY_STR_LIST else '--'
            data['vol_bc'] = round(float(pcs.get('PUb')), 2) if pcs.get('PUb') not in EMPTY_STR_LIST else '--'
            data['vol_ca'] = round(float(pcs.get('PUc')), 2) if pcs.get('PUc') not in EMPTY_STR_LIST else '--'
            data['cur_a'] = round(float(pcs.get('Ia')), 2) if pcs.get('Ia') not in EMPTY_STR_LIST else '--'
            data['cur_b'] = round(float(pcs.get('Ib')), 2) if pcs.get('Ib') not in EMPTY_STR_LIST else '--'
            data['cur_c'] = round(float(pcs.get('Ic')), 2) if pcs.get('Ic') not in EMPTY_STR_LIST else '--'
            data['total_pf'] = round(float(pcs.get('PF')), 2) if pcs.get('PF') not in EMPTY_STR_LIST else '--'
            data['total_cos'] = round(float(pcs.get('COS')), 4) if pcs.get('COS') not in EMPTY_STR_LIST else '--'

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


class PCSRealAreaOverviewLoad(APIView):
    """
    PCS监测数据下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )
        self.connection = self.connection
        self.cursor = self.connection.cursor()

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')  # 储能单元ID
        inquire_time = request.data.get('inquire_time')  # 查询日期
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '字段校验失败！' if lang == 'zh' else 'Field verification failed!',
                    }
                }
            )
        try:
            unit = models.Unit.objects.get(id=id, is_delete=0)
        except models.Unit.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该单元不存在，请检查参数！' if lang == 'zh' else 'The unit does not exist, please check the parameter!',
                    }
                }
            )

        station_name = unit.station.english_name
        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'
        sql = """SELECT  DATE_FORMAT(time, '%H:%i') as new_time, pua, pub, puc, 
                                ia, ib, ic, pa, pb, pc, qa, qb, qc, pf, cos
                                   FROM dwd_measure_pcs_data_storage
                                   WHERE 
                                   time 
                                   BETWEEN '{}'
                                   AND '{}' 
                                """.format(s_time, e_time)

        sql += "AND station_name = '{}' AND  device = '{}'".format(station_name, unit.pcs)
        sql += ' ORDER BY new_time'

        try:
            # 获取查询结果
            self.cursor.execute(sql)
            res = self.cursor.fetchall()
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                    }
                }
            )
        finally:
            self.cursor.close()
            self.connection.close()
        times = create_one_minute_list()
        res_dict = {}
        for i in res:
            res_dict[i['new_time']] = {
                'puab': i['pua'] if i['pua'] not in EMPTY_STR_LIST else '--',
                'pubc': i['pub'] if i['pub'] not in EMPTY_STR_LIST else '--',
                'puca': i['puc'] if i['puc'] not in EMPTY_STR_LIST else '--',
                'ia': i['ia'] if i['ia'] not in EMPTY_STR_LIST else '--',
                'ib': i['ib'] if i['ib'] not in EMPTY_STR_LIST else '--',
                'ic': i['ic'] if i['ic'] not in EMPTY_STR_LIST else '--',
                'pa': i['pa'] if i['pa'] not in EMPTY_STR_LIST else '--',
                'pb': i['pb'] if i['pb'] not in EMPTY_STR_LIST else '--',
                'pc': i['pc'] if i['pc'] not in EMPTY_STR_LIST else '--',
                'qa': i['qa'] if i['qa'] not in EMPTY_STR_LIST else '--',
                'qb': i['qb'] if i['qb'] not in EMPTY_STR_LIST else '--',
                'qc': i['qc'] if i['qc'] not in EMPTY_STR_LIST else '--',
                'pf': i['pf'] if i['pf'] not in EMPTY_STR_LIST else '--',
                'cos': i['cos'] if i['cos'] not in EMPTY_STR_LIST else '--',
            }
        data = []
        for v in times:
            info = res_dict.get(v)
            if info:
                data.append([inquire_time, v, info.get('pa'), info.get('pb'), info.get('pc'), info.get('qa'),
                             info.get('qb'), info.get('qc'), info.get('puab'), info.get('pubc'), info.get('puca'),
                             info.get('ia'), info.get('ib'), info.get('ic'), info.get('pf'), info.get('cos')])
            else:
                data.append([inquire_time, v, '--', '--', '--', '--', '--', '--', '--', '--', '--', '--', '--', '--', '--', '--'])

        # 下载数据
        wb = Workbook()
        wb.encoding = 'utf-8'  # 定义编码格式
        st = wb.active  # 获取第一个工作表（sheet1）
        title = ['日期', '时刻', 'PCS A相有功功率（kW）', 'PCS B相有功功率（kW）', 'PCS C相有功功率（kW）', 'PCS A相无功功率（kVar）', 'PCS B相无功功率（kVar）',
                     'PCS C相无功功率（kVar）', 'PCS A相电压（V）', 'PCS B相电压（V）', 'PCS C相电压（V）', 'PCS A相电流（A）', 'PCS B相电流（A）', 'PCS C相电流（A）',
                     '频率（Hz）', '功率因数']

        en_title = ['Date', 'Time', 'Phase A Active Power (kW)', 'Phase B Active Power (kW)',
                      'Phase C Active Power (kW)', 'Phase A Reactive Power (kVar)',
                      'Phase B Reactive Power (kVar)', 'Phase C Reactive Power (kVar)',
                      'A-phase voltage (V)', 'B-phase voltage (V)', 'C-phase voltage (V)', 'Phase A Current (A)',
                      'Phase B Current (A)', 'Phase C Current (C)', 'Frequency (Hz)', 'Power Factor'
                   ]

        if lang == 'en':
            title = en_title
        for i in range(1, len(title) + 1):
            st.cell(row=1, column=i).value = title[i - 1]

        for info in data:
            max_row = st.max_row + 1
            for _i, v in enumerate(info):
                st.cell(row=max_row, column=_i + 1).value = v

        file_name = f"{unit.station.project.name}-{inquire_time}-PCS监测数据--{int(time.time() * 1000)}.xlsx" if lang == 'zh' else f"{unit.station.project.name}-{inquire_time}-PCS Monitoring Data--{int(time.time() * 1000)}.xlsx"
        file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
        wb.save(file_path)
        # 上传Minio
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        file_path = minio_client.upload_local_file(file_name, file_path, 'download')

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": file_path}
            }
        )