#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


ZGTIAN1_HOSTNAME = model_config.get('mysql', "ZGTIAN1_HOSTNAME")
ZGTIAN1_PORT = model_config.get('mysql', "ZGTIAN1_PORT")
ZGTIAN1_DATABASE = model_config.get('mysql', "ZGTIAN1_DATABASE")
ZGTIAN1_USERNAME = model_config.get('mysql', "ZGTIAN1_USERNAME")
ZGTIAN1_PASSWORD = model_config.get('mysql', "ZGTIAN1_PASSWORD")

ZGTIAN2_HOSTNAME = model_config.get('mysql', "ZGTIAN2_HOSTNAME")
ZGTIAN2_PORT = model_config.get('mysql', "ZGTIAN2_PORT")
ZGTIAN2_DATABASE = model_config.get('mysql', "ZGTIAN2_DATABASE")
ZGTIAN2_USERNAME = model_config.get('mysql', "ZGTIAN2_USERNAME")
ZGTIAN2_PASSWORD = model_config.get('mysql', "ZGTIAN2_PASSWORD")

ZGTIAN3_HOSTNAME = model_config.get('mysql', "ZGTIAN3_HOSTNAME")
ZGTIAN3_PORT = model_config.get('mysql', "ZGTIAN3_PORT")
ZGTIAN3_DATABASE = model_config.get('mysql', "ZGTIAN3_DATABASE")
ZGTIAN3_USERNAME = model_config.get('mysql', "ZGTIAN3_USERNAME")
ZGTIAN3_PASSWORD = model_config.get('mysql', "ZGTIAN3_PASSWORD")

ZGTIAN4_HOSTNAME = model_config.get('mysql', "ZGTIAN4_HOSTNAME")
ZGTIAN4_PORT = model_config.get('mysql', "ZGTIAN4_PORT")
ZGTIAN4_DATABASE = model_config.get('mysql', "ZGTIAN4_DATABASE")
ZGTIAN4_USERNAME = model_config.get('mysql', "ZGTIAN4_USERNAME")
ZGTIAN4_PASSWORD = model_config.get('mysql', "ZGTIAN4_PASSWORD")




hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN1_USERNAME,
    ZGTIAN1_PASSWORD,
    ZGTIAN1_HOSTNAME,
    ZGTIAN1_PORT,
    ZGTIAN1_DATABASE
)
zgtian1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_zgtian1_session = scoped_session(sessionmaker(zgtian1_engine,autoflush=True))
zgtian1_Base = declarative_base(zgtian1_engine)
zgtian1_session = _zgtian1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN2_USERNAME,
    ZGTIAN2_PASSWORD,
    ZGTIAN2_HOSTNAME,
    ZGTIAN2_PORT,
    ZGTIAN2_DATABASE
)
zgtian2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_zgtian2_session = scoped_session(sessionmaker(zgtian2_engine,autoflush=True))
zgtian2_Base = declarative_base(zgtian2_engine)
zgtian2_session = _zgtian2_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN3_USERNAME,
    ZGTIAN3_PASSWORD,
    ZGTIAN3_HOSTNAME,
    ZGTIAN3_PORT,
    ZGTIAN3_DATABASE
)
zgtian3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_zgtian3_session = scoped_session(sessionmaker(zgtian3_engine,autoflush=True))
zgtian3_Base = declarative_base(zgtian3_engine)
zgtian3_session = _zgtian3_session()

hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ZGTIAN4_USERNAME,
    ZGTIAN4_PASSWORD,
    ZGTIAN4_HOSTNAME,
    ZGTIAN4_PORT,
    ZGTIAN4_DATABASE
)
zgtian4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_ZGTIAN4_session = scoped_session(sessionmaker(zgtian4_engine,autoflush=True))
zgtian4_Base = declarative_base(zgtian4_engine)
zgtian4_session = _ZGTIAN4_session()


SZGTIAN1_HOSTNAME = model_config.get('mysql', "SZGTIAN1_HOSTNAME")
SZGTIAN1_PORT = model_config.get('mysql', "SZGTIAN1_PORT")
SZGTIAN1_DATABASE = model_config.get('mysql', "SZGTIAN1_DATABASE")
SZGTIAN1_USERNAME = model_config.get('mysql', "SZGTIAN1_USERNAME")
SZGTIAN1_PASSWORD = model_config.get('mysql', "SZGTIAN1_PASSWORD")

SZGTIAN2_HOSTNAME = model_config.get('mysql', "SZGTIAN2_HOSTNAME")
SZGTIAN2_PORT = model_config.get('mysql', "SZGTIAN2_PORT")
SZGTIAN2_DATABASE = model_config.get('mysql', "SZGTIAN2_DATABASE")
SZGTIAN2_USERNAME = model_config.get('mysql', "SZGTIAN2_USERNAME")
SZGTIAN2_PASSWORD = model_config.get('mysql', "SZGTIAN2_PASSWORD")

SZGTIAN3_HOSTNAME = model_config.get('mysql', "SZGTIAN3_HOSTNAME")
SZGTIAN3_PORT = model_config.get('mysql', "SZGTIAN3_PORT")
SZGTIAN3_DATABASE = model_config.get('mysql', "SZGTIAN3_DATABASE")
SZGTIAN3_USERNAME = model_config.get('mysql', "SZGTIAN3_USERNAME")
SZGTIAN3_PASSWORD = model_config.get('mysql', "SZGTIAN3_PASSWORD")

SZGTIAN4_HOSTNAME = model_config.get('mysql', "SZGTIAN4_HOSTNAME")
SZGTIAN4_PORT = model_config.get('mysql', "SZGTIAN4_PORT")
SZGTIAN4_DATABASE = model_config.get('mysql', "SZGTIAN4_DATABASE")
SZGTIAN4_USERNAME = model_config.get('mysql', "SZGTIAN4_USERNAME")
SZGTIAN4_PASSWORD = model_config.get('mysql', "SZGTIAN4_PASSWORD")




shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SZGTIAN1_USERNAME,
    SZGTIAN1_PASSWORD,
    SZGTIAN1_HOSTNAME,
    SZGTIAN1_PORT,
    SZGTIAN1_DATABASE
)
szgtian1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_szgtian1_session = scoped_session(sessionmaker(szgtian1_engine,autoflush=True))
szgtian1_Base = declarative_base(szgtian1_engine)
szgtian1_session = _szgtian1_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SZGTIAN2_USERNAME,
    SZGTIAN2_PASSWORD,
    SZGTIAN2_HOSTNAME,
    SZGTIAN2_PORT,
    SZGTIAN2_DATABASE
)
szgtian2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_szgtian2_session = scoped_session(sessionmaker(szgtian2_engine,autoflush=True))
szgtian2_Base = declarative_base(szgtian2_engine)
szgtian2_session = _szgtian2_session()

shisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SZGTIAN3_USERNAME,
    SZGTIAN3_PASSWORD,
    SZGTIAN3_HOSTNAME,
    SZGTIAN3_PORT,
    SZGTIAN3_DATABASE
)
szgtian3_engine = create_engine(shisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_szgtian3_session = scoped_session(sessionmaker(szgtian3_engine,autoflush=True))
szgtian3_Base = declarative_base(szgtian3_engine)
szgtian3_session = _szgtian3_session()

shisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SZGTIAN4_USERNAME,
    SZGTIAN4_PASSWORD,
    SZGTIAN4_HOSTNAME,
    SZGTIAN4_PORT,
    SZGTIAN4_DATABASE
)
szgtian4_engine = create_engine(shisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sZGTIAN4_session = scoped_session(sessionmaker(szgtian4_engine,autoflush=True))
szgtian4_Base = declarative_base(szgtian4_engine)
szgtian4_session = _sZGTIAN4_session()


MZGTIAN_HOSTNAME = model_config.get('mysql', "MZGTIAN_HOSTNAME")
MZGTIAN_PORT = model_config.get('mysql', "MZGTIAN_PORT")
MZGTIAN_DATABASE = model_config.get('mysql', "MZGTIAN_DATABASE")
MZGTIAN_USERNAME = model_config.get('mysql', "MZGTIAN_USERNAME")
MZGTIAN_PASSWORD = model_config.get('mysql', "MZGTIAN_PASSWORD")




mhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    MZGTIAN_USERNAME,
    MZGTIAN_PASSWORD,
    MZGTIAN_HOSTNAME,
    MZGTIAN_PORT,
    MZGTIAN_DATABASE
)
mzgtian_engine = create_engine(mhisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_mzgtian_session = scoped_session(sessionmaker(mzgtian_engine,autoflush=True))
mzgtian_Base = declarative_base(mzgtian_engine)
mzgtian_session = _mzgtian_session()


SMZGTIAN_HOSTNAME = model_config.get('mysql', "SMZGTIAN_HOSTNAME")
SMZGTIAN_PORT = model_config.get('mysql', "SMZGTIAN_PORT")
SMZGTIAN_DATABASE = model_config.get('mysql', "SMZGTIAN_DATABASE")
SMZGTIAN_USERNAME = model_config.get('mysql', "SMZGTIAN_USERNAME")
SMZGTIAN_PASSWORD = model_config.get('mysql', "SMZGTIAN_PASSWORD")




smhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SMZGTIAN_USERNAME,
    SMZGTIAN_PASSWORD,
    SMZGTIAN_HOSTNAME,
    SMZGTIAN_PORT,
    SMZGTIAN_DATABASE
)
smzgtian_engine = create_engine(smhisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_smzgtian_session = scoped_session(sessionmaker(smzgtian_engine,autoflush=True))
smzgtian_Base = declarative_base(smzgtian_engine)
smzgtian_session = _smzgtian_session()

