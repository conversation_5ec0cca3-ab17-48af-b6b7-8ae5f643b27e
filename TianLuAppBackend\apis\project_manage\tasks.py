# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/10 上午11:10
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : tasks.py
# @Software : PyCharm
from apis.user.models import MaterStation, StationMeterUseTime


def check_ems_slave_station_meter_config():
    """
    背景：针对“标准主从模式”的并网点，在项目管理中只有“从设备”的使用结算电表配置，却无000主设备的是否使用结算电表配置信息，
        导致使用结算电表的标准主从并网点获取并网点的累计充放电量时，无法获取到（ads_cumulant_ems_product_5min表只有主设备数据，没有从设备数据）。
    功能：检查所有的标准主从并网点且使用结算电表的主站的使用电表配置是否缺失：包含 t_stations 表的 is_account 字段 和 t_meter_use_time_station 表的相关配置
    """""
    # 查询所有的标准主从并网点
    all_master_stations_use_ems_meter = MaterStation.objects.filter(is_delete=0, mode=2).all()

    # 检查存在使用结算电表的并网点
    for master_station in all_master_stations_use_ems_meter:
        print(22, master_station.name)

        stations = master_station.stationdetails_set.filter(is_delete=0).all()
        use_meter_station = stations.filter(is_account=1).first()

        # 只要存在一个使用结算电表的子站，就补充主设备(000)的电表使用时间配置
        if use_meter_station:
            use_meter_station_config = StationMeterUseTime.objects.filter(station=use_meter_station, is_use=1).first()

            if use_meter_station_config:

                ems_slave_station = stations.filter(slave=0, is_account=0).first()
                if ems_slave_station:
                    # t_stations 表的 is_account 字段
                    ems_slave_station.is_account = 1
                    ems_slave_station.save()

                    meter_use_time = StationMeterUseTime.objects.filter(station=ems_slave_station, is_use=1).all()
                    if not meter_use_time.exists():
                        StationMeterUseTime.objects.create(station=ems_slave_station,
                                                    user=use_meter_station_config.user,
                                                    start_time=use_meter_station_config.start_time,
                                                    end_time=use_meter_station_config.end_time)

                        print(f"{master_station.name} 已补充000主设备的电表使用时间配置")
        # else:
        #     print(f"{master_station.name} 不存在使用结算电表的子站，无需补充000主设备的电表使用时间配置")
