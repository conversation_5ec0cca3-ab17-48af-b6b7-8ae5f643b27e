#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\organization.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-28 17:43:36


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR
from Application.Models.User.organization_type import OrganizationType
import pandas as pd
from Tools.Utils.time_utils import timeUtils

class Equipment(user_Base):
    u'项目信息设备表'
    __tablename__ = "t_equipment"
    id = Column(VARCHAR(100), nullable=False, primary_key=True)
    name = Column(VARCHAR(100), nullable=False, comment=u"类名称")
    parent_id = Column(VARCHAR(100), nullable=True, comment=u"父节点id")

    # pcs_num = Column(VARCHAR(50), nullable=False, comment=u"PCS编号")
    pcs_name = Column(VARCHAR(50), nullable=True, comment=u"PCS名称")
    pcs_ty_num = Column(VARCHAR(50), nullable=True, comment=u"PCS型号")
    pcs_s_name = Column(VARCHAR(50), nullable=True, comment=u"PCS备用名称")
    act_acc_ene = Column(VARCHAR(50), nullable=True, comment=u"实际接入能量")
    # bat_stack_num = Column(VARCHAR(50), nullable=False, comment=u"电池堆编号")
    # bat_clu_num = Column(VARCHAR(50), nullable=False, comment=u"电池堆簇编号")
    cool_mode = Column(VARCHAR(50), nullable=True, comment=u"冷却方式")
    cell_type = Column(VARCHAR(50), nullable=True, comment=u"电芯类型")
    cell_num = Column(VARCHAR(50), nullable=True, comment=u"电芯数量")
    # cell_ty_num = Column(VARCHAR(50), nullable=False, comment=u"电芯型号")
    cell_s_ene = Column(VARCHAR(50), nullable=True, comment=u"电芯单体能量")
    total_ene = Column(VARCHAR(50), nullable=True, comment=u"合计能量")
    cell_s_na = Column(VARCHAR(50), nullable=True, comment=u"电芯备用名称")
    bms_num = Column(VARCHAR(50), nullable=True, comment=u"BMS编号")
    is_use = Column(CHAR(2), nullable=False, server_default='1',comment=u"是否使用1是0否")
    station = Column(VARCHAR(50), nullable=False, comment=u"所属站")
    descr = Column(VARCHAR(256), nullable=False, comment=u"描述")
    equ_ty = Column(CHAR(2), nullable=False, comment=u"设备类型 1储能单元，2PCS，3电池堆，4电池簇，5电芯")

    en_name = Column(VARCHAR(100), nullable=False, comment=u"类名称")
    en_pcs_s_name = Column(VARCHAR(50), nullable=True, comment=u"PCS备用名称")
    en_cool_mode = Column(VARCHAR(50), nullable=True, comment=u"冷却方式")
    en_cell_type = Column(VARCHAR(50), nullable=True, comment=u"电芯类型")
    en_cell_s_na = Column(VARCHAR(50), nullable=True, comment=u"电芯备用名称")
    en_descr = Column(VARCHAR(256), nullable=False, comment=u"描述")
    en_pcs_name = Column(VARCHAR(256), nullable=False, comment=u"PCS名称-英文")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean= "{'id':'%s','name':'%s','parent_id':'%s','pcs_name':'%s','pcs_ty_num':%s,'pcs_s_name':'%s','act_acc_ene':'%s','cool_mode':'%s','cell_type':%s,'cell_num':%s,'cell_s_na':'%s','total_ene':%s,'cell_s_na':%s," \
              "'bms_num':'%s','is_use':%s,'station':%s,'descr':'%s','equ_ty':'%s','en_name':'%s','en_pcs_s_name':'%s','en_cool_mode':'%s','en_cell_type':'%s','en_cell_s_na':'%s','en_descr':'%s','en_pcs_name':'%s'}" % (
            self.id, self.name ,self.parent_id, self.pcs_name, self.pcs_ty_num, self.pcs_s_name, self.act_acc_ene,self.cool_mode, self.cell_type, self.cell_num, self.cell_s_na, self.total_ene, self.cell_s_na, self.bms_num,
            self.is_use, self.station, self.descr,self.equ_ty,self.en_name,self.en_pcs_s_name,self.en_cool_mode,self.en_cell_type,self.en_cell_s_na,self.en_descr,self.en_pcs_name)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}

