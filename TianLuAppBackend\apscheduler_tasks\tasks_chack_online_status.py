import datetime
import json
import logging
import concurrent.futures
import traceback

from django.db.models import Q
from django_redis import get_redis_connection

from apis.user import models
from apis.web2 import success_log


def update_station_online_status_v2(station, device, status=0, point='Online', EMS_status=None):
    """
    将设备online状态信息写入数据库
    """""
    conn = get_redis_connection("default")
    note = '已修复' if status == 1 else '未修复'
    detail = f'{device} 设备在线状态'
    data_dic = {
        'type': 4,
        'station_id': station.id,
        'point': point,
        'device': device,
        'status': status,
        'details': detail,
        'note': note
    }

    # device_another_name
    if device != 'EMS':
        units = station.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
        if units.exists():
            unit = units.first()
            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
            device_another_name = device[:3] + unit_num
        else:
            device_another_name = device
    else:
        device_another_name = device
    if EMS_status == 1 and device != 'EMS':
        type_ = 5  # 通信异常
        point = 'Cmlost'
        data_dic['type'] = 5
        data_dic['point'] = 'Cmlost'
        data_dic['details'] = f'{device} 设备通信状态'
    else:
        type_ = 4

    now = datetime.datetime.now()

    # 写入 redis
    key_str_cmlost = f"{station.english_name}_{device}_Cmlost"
    key_str_online = f"{station.english_name}_{device}_Online"
    key_str_cmlost_aggr = f"{station.english_name}_{device}_Cmlost_aggr"
    key_str_online_aggr = f"{station.english_name}_{device}_Online_aggr"
    joint_primary_key = f"{station.id}_{device}_{type_}_{point}_{now.strftime('%Y-%m-%d %H:%M:%S')}"
    # 设备离线或者通讯异常时
    if status == 0:
        # redis 中不存在该 key，则分别写入 doris 和 redis
        # 通讯故障：通讯故障+离线
        if type_ == 5:
            # 再判断 redis 中是否已存在该聚合告警 key，有则说明已存在聚合告警，没有则写入
            if not conn.exists(key_str_cmlost) and not conn.exists(key_str_online):
                if not conn.exists(key_str_cmlost_aggr) and not conn.exists(key_str_online_aggr):
                    # 先创建聚合告警
                    models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                        **data_dic,
                        start_time=now,
                        device_another_name=device_another_name,
                        joint_primary_key=joint_primary_key
                    )
                    # new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(station_id=station.id,
                    #                                                                             device=device,
                    #                                                                             point='Cmlost',
                    #                                                                             status=0).first()


                    conn.set(key_str_cmlost_aggr, joint_primary_key)

                    # 再创建告警，并关联至该聚合告警
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        joint_primary_key=joint_primary_key,
                        aggr_alarm_id=joint_primary_key,
                        **data_dic
                    )

                    # new_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    #     station_id=station.id,
                    #     device=device,
                    #     point='Cmlost',
                    #     joint_primary_key=new_aggr_alarm.joint_primary_key,
                    #     status=0).first()

                    conn.set(key_str_cmlost, joint_primary_key)

                # 已存在聚合告警，则关联到聚合告警: 先将聚合告警改为未恢复状态，并将redis中的聚合告警的key的过期时间设置为-1
                else:
                    # 先将聚合告警改为"未恢复"状态
                    new_aggr_alarm_id = conn.get(key_str_cmlost_aggr) or conn.get(key_str_online_aggr)
                    if isinstance(new_aggr_alarm_id, bytes):
                        new_aggr_alarm_id = new_aggr_alarm_id.decode()
                    # 创建告警记录，并关联于该聚合告警
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        aggr_alarm_id=new_aggr_alarm_id,
                        joint_primary_key=joint_primary_key,
                        **data_dic
                    )

                    success_log.info(
                        '告警上报内容：station--{},body--{},utime--{}'.format(station, data_dic, now))

                    # new_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    #     station_id=station.id,
                    #     device=device,
                    #     point='Cmlost',
                    #     joint_primary_key=new_aggr_alarm_id,
                    #     status=0).first()

                    conn.set(key_str_cmlost, joint_primary_key)

                    # 修改聚合告警状态: 不管其为“离线”还是“通讯故障”，均更新为“通讯故障”
                    aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=new_aggr_alarm_id).first()
                    aggr_alarm.status = 0
                    aggr_alarm.end_time = None
                    aggr_alarm.note = data_dic['note']
                    aggr_alarm.point = data_dic['point']
                    aggr_alarm.type = data_dic['type']
                    aggr_alarm.details = data_dic['details']
                    aggr_alarm.save()

                    # 去除过期时间： 用expire 命令 设置为-1 未生效==> 直接重新设置key
                    if conn.exists(key_str_cmlost_aggr):
                        conn.set(key_str_cmlost_aggr, new_aggr_alarm_id)
                    else:
                        conn.set(key_str_online_aggr, new_aggr_alarm_id)

            # 已存在改设备的离线或者通讯故障告警，则更新其状态：不管其之前是离线或者通讯故障告警，都改为“通讯故障”
            online_alarm_id = conn.get(key_str_online)
            if online_alarm_id:
                if isinstance(online_alarm_id, bytes):
                    online_alarm_id = online_alarm_id.decode()
                if conn.get(key_str_cmlost):  # 如果存在通讯异常数据则恢复离线
                    today_end = now.replace(hour=23, minute=59, second=59, microsecond=0)
                    expire_time = (today_end - now).total_seconds()
                    alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=online_alarm_id).first()
                    if alarm:
                        alarm.status = 1
                        alarm.note = data_dic['note']
                        alarm.end_time = now
                        alarm.save()
                        try:
                            conn.delete(key_str_online)
                        except Exception as e:
                            print(e, traceback.print_exc())
                        # 查询 聚合告警：一般都是存在的，直接更新；redis中的 key 则增加一个过期时间，过了当晚 过期删除
                        aggr_alarm_id = alarm.aggr_alarm_id
                        exist_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(
                            joint_primary_key=aggr_alarm_id).first()
                        if exist_aggr_alarm:
                            # 计算 告警时长
                            exist_aggr_alarm.duration = round(
                                (datetime.datetime.now() - exist_aggr_alarm.start_time).total_seconds() / 3600, 2)
                            related_alarm_list = models.FaultAlarm.objects.using('alarm_module_write').filter(
                                aggr_alarm_id=aggr_alarm_id).all()
                            duration = 0
                            for alarm in related_alarm_list:
                                if alarm.status == 1:
                                    duration += (alarm.end_time - alarm.start_time).total_seconds()
                                else:
                                    duration += (datetime.datetime.now() - alarm.start_time).total_seconds()

                            exist_aggr_alarm.status = 1
                            exist_aggr_alarm.end_time = now
                            exist_aggr_alarm.note = data_dic['note']
                            exist_aggr_alarm.duration = round(duration / 3600, 2)
                            exist_aggr_alarm.save()

                            # 给聚合告警的 key 设置过期时间
                            if conn.exists(key_str_online_aggr):
                                if exist_aggr_alarm.start_time.date() == now.date():
                                    conn.expire(key_str_online_aggr, int(expire_time))
                                else:
                                    conn.delete(key_str_online_aggr)

                else:  # 离线置为通讯异常
                    alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=online_alarm_id).first()
                    if alarm:
                        alarm.type = type_
                        alarm.point = point
                        alarm.detail = data_dic['details']
                        alarm.note = data_dic['note']
                        alarm.status = status
                        alarm.save()
                        aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=alarm.aggr_alarm_id).first()
                        if aggr_alarm:
                            aggr_alarm.type = type_
                            aggr_alarm.point = point
                            aggr_alarm.detail = data_dic['details']
                            aggr_alarm.note = data_dic['note']
                            aggr_alarm.status = status
                            aggr_alarm.save()

        # 离线
        else:
            # redis中未存在该离线告警
            if not conn.exists(key_str_online):
                # 判断 redis 中是否已存在该聚合告警 key，有则说明已存在聚合告警，没有则写入
                if not conn.exists(key_str_online_aggr):
                    # 先创建聚合告警
                    models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                        **data_dic,
                        start_time=now.strftime('%Y-%m-%d %H:%M:%S'),
                        device_another_name=device_another_name,
                        joint_primary_key=joint_primary_key
                    )
                    # new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(station_id=station.id,
                    #                                                                             device=device,
                    #                                                                             point='Online',
                    #                                                                             status=0).first()

                    # if new_aggr_alarm:
                    conn.set(key_str_online_aggr, joint_primary_key)

                    # 再创建告警，并关联至该聚合告警
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        joint_primary_key=joint_primary_key,
                        aggr_alarm_id=joint_primary_key,
                        **data_dic
                    )

                    # new_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    #     station_id=station.id,
                    #     device=device,
                    #     point='Online',
                    #     joint_primary_key=new_aggr_alarm.joint_primary_key,
                    #     status=0).first()

                    conn.set(key_str_online, joint_primary_key)

                else:
                    # 已存在聚合告警，则关联到聚合告警: 先将聚合告警改为未恢复状态，并将redis中的聚合告警的key的过期时间设置为-1
                    new_aggr_alarm_id = conn.get(key_str_online_aggr)
                    if isinstance(new_aggr_alarm_id, bytes):
                        new_aggr_alarm_id = new_aggr_alarm_id.decode()
                    # 创建告警记录，并关联于该聚合告警
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        aggr_alarm_id=new_aggr_alarm_id,
                        joint_primary_key=joint_primary_key,
                        **data_dic
                    )

                    success_log.info(
                        '告警上报内容：station--{},body--{},utime--{}'.format(station, data_dic, now))

                    # new_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    #     station_id=station.id,
                    #     device=device,
                    #     point='Online',
                    #     joint_primary_key=new_aggr_alarm_id,
                    #     status=0).first()

                    conn.set(key_str_online, joint_primary_key)

                    # 将聚合告警改为"未恢复"状态
                    aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=new_aggr_alarm_id).first()
                    if aggr_alarm:
                        aggr_alarm.status = 0
                        aggr_alarm.end_time = None
                        aggr_alarm.note = data_dic['note']
                        aggr_alarm.point = data_dic['point']
                        aggr_alarm.type = data_dic['type']
                        aggr_alarm.details = data_dic['details']
                        aggr_alarm.save()

                        # 去除过期时间： 用expire 命令 设置为-1 未生效==> 直接重新设置key
                        if conn.exists(key_str_online_aggr):
                            conn.set(key_str_online_aggr, new_aggr_alarm_id)

    # 设备（恢复）在线
    else:
        today_end = now.replace(hour=23, minute=59, second=59, microsecond=0)
        expire_time = (today_end - now).total_seconds()

        # 通讯故障+离线
        # redis 中存在该 key，则恢复并删除 redis key
        if conn.get(key_str_cmlost):
            alarm_id = conn.get(key_str_cmlost)
            if isinstance(alarm_id, bytes):
                alarm_id = alarm_id.decode()
            alarm_ins = models.FaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=alarm_id).first()
            if alarm_ins:
                alarm_ins.status = 1
                alarm_ins.note = data_dic['note']
                alarm_ins.end_time = now
                alarm_ins.save()

                try:
                    conn.delete(key_str_cmlost)
                except Exception as e:
                    print(e, traceback.print_exc())

            # 查询 聚合告警：一般都是存在的，直接更新；redis中的 key 则增加一个过期时间，过了当晚 过期删除
            aggr_alarm_id = alarm_ins.aggr_alarm_id
            exist_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=aggr_alarm_id).first()

            if exist_aggr_alarm:
                # 计算 告警时长
                exist_aggr_alarm.duration = round(
                    (datetime.datetime.now() - exist_aggr_alarm.start_time).total_seconds() / 3600, 2)
                related_alarm_list = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    aggr_alarm_id=exist_aggr_alarm.joint_primary_key).all()
                duration = 0
                for alarm in related_alarm_list:
                    if alarm.status == 1:
                        duration += (alarm.end_time - alarm.start_time).total_seconds()
                    else:
                        duration += (datetime.datetime.now() - alarm.start_time).total_seconds()

                exist_aggr_alarm.status = 1
                exist_aggr_alarm.end_time = now
                exist_aggr_alarm.note = data_dic['note']
                exist_aggr_alarm.duration = round(duration / 3600, 2)
                exist_aggr_alarm.save()

                # 给聚合告警的 key 设置过期时间
                if conn.exists(key_str_cmlost_aggr):
                    if exist_aggr_alarm.start_time.date() == now.date():
                        conn.expire(key_str_cmlost_aggr, int(expire_time))
                    else:
                        conn.delete(key_str_cmlost_aggr)

        if conn.get(key_str_online):
            alarm_id = conn.get(key_str_online)
            if isinstance(alarm_id, bytes):
                alarm_id = alarm_id.decode()
            alarm_ins = models.FaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=alarm_id).first()
            aggr_alarm_id = alarm_ins.aggr_alarm_id
            if alarm_ins:
                alarm_ins.status = 1
                alarm_ins.note = data_dic['note']
                alarm_ins.end_time = now
                alarm_ins.save()

                try:
                    conn.delete(key_str_online)
                except Exception as e:
                    print(e, traceback.print_exc())

            # 查询 聚合告警：一般都是存在的，直接更新；redis中的 key 则增加一个过期时间，过了当晚 过期删除
            exist_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(joint_primary_key=aggr_alarm_id).first()

            if exist_aggr_alarm:
                # 计算 告警时长
                exist_aggr_alarm.duration = round(
                    (datetime.datetime.now() - exist_aggr_alarm.start_time).total_seconds() / 3600, 2)
                related_alarm_list = models.FaultAlarm.objects.using('alarm_module_write').filter(
                    aggr_alarm_id=exist_aggr_alarm.joint_primary_key).all()
                duration = 0
                for alarm in related_alarm_list:
                    if alarm.status == 1:
                        duration += (alarm.end_time - alarm.start_time).total_seconds()
                    else:
                        duration += (datetime.datetime.now() - alarm.start_time).total_seconds()

                exist_aggr_alarm.status = 1
                exist_aggr_alarm.end_time = now
                exist_aggr_alarm.note = data_dic['note']
                exist_aggr_alarm.duration = round(duration / 3600, 2)
                exist_aggr_alarm.save()

                # 给聚合告警的 key 设置过期时间
                if conn.exists(key_str_online_aggr):
                    if exist_aggr_alarm.start_time.date() == now.date():
                        conn.expire(key_str_online_aggr, int(expire_time))
                    else:
                        conn.delete(key_str_online_aggr)



def update_station_online_status(station, device, status=0, point='Online', EMS_status=None):
    """
    将设备online状态信息写入数据库
    """""
    conn = get_redis_connection("default")
    note = '已修复' if status == 1 else '未修复'
    detail = f'{device} 设备在线状态'
    data_dic = {
        'type': 4,
        'station_id': station.id,
        'point': point,
        'device': device,
        'status': status,
        'details': detail,
        'note': note
    }

    # device_another_name
    if device != 'EMS':
        units = station.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
        if units.exists():
            unit = units.first()
            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
            device_another_name = device[:3] + unit_num
        else:
            device_another_name = device
    else:
        device_another_name = device
    if EMS_status == 1 and device != 'EMS':
        type_ = 5  # 通信异常
        point = 'Cmlost'
        data_dic['type'] = 5
        data_dic['point'] = 'Cmlost'
        data_dic['details'] = f'{device} 设备通信状态'
    else:
        type_ = 4

    now = datetime.datetime.now()
    month_str = now.strftime('%Y-%m-%d')
    start_time = month_str + ' 00:00:00'
    end_time = month_str + ' 23:59:59'
    # 判断是否存在未恢复在线的告警记录
    if type_ == 5:
        # 聚合告警：首先查询当天是否存在相同的聚合告警信息：有则关联；没有则查询当天以前是否有未恢复的告警：有则关联，没有则新创建
        today_aggr_alarms = (models.AggrFaultAlarm.objects.using('alarm_module_write').filter(station_id=station.id, device=device, type__gte=4,
                                                                  point__in=['Cmlost', 'Online'],
                                                                  start_time__gte=start_time, start_time__lte=end_time).all())

        before_exist_alarms = (models.AggrFaultAlarm.objects.using('alarm_module_write').filter(status=0, station_id=station.id, device=device, type__gte=4,
                                                                  point__in=['Cmlost', 'Online'], start_time__lt=now)
                               .all())

        alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(type__gte=4, point__in=['Cmlost', 'Online'], station_id=station.id, device=device,
                                                 status=0).order_by('-start_time')
    else:
        today_aggr_alarms = (models.AggrFaultAlarm.objects.using('alarm_module_write').filter(station_id=station.id, device=device, point=point, type=type_,
                                                                  start_time__gte=start_time, start_time__lte=end_time).all())

        before_exist_alarms = (
            models.AggrFaultAlarm.objects.using('alarm_module_write').filter(status=0, station_id=station.id, device=device, type=type_,
                                                 point=point, start_time__lt=now)
            .all())

        alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(type=type_, point=point, station_id=station.id, device=device,
                                                 status=0).order_by('-start_time')
    # 设备离线或者通讯异常时
    if status == 0:
        # 不存在该记录，则新增，start_time 为当前时间
        if not alarm.exists():
            print(f'!!!!!!!!!!!!!!!!{station.station_name}的设备{device}已离线')

            # 当天不存在相同的聚合告警信息
            if not today_aggr_alarms.exists():

                # 当天以前存在未恢复的告警，则创建告警并关联至该聚合告警
                if before_exist_alarms.exists():
                    before_alarm = before_exist_alarms.last()
                    # 更新故障时长
                    before_alarm.duration = round((datetime.datetime.now() - before_alarm.start_time).seconds
                                                  / 3600, 2)
                    before_alarm.save()

                    # 创建告警，并关联到之前的未恢复聚合告警
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        aggr_alarm=before_alarm,
                        **data_dic
                    )

                # 当天以前也不存在未恢复的告警，则先创建聚合告警，再创建聚合告警，并关联至该聚合告警
                else:
                    new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                        **data_dic,
                        start_time=now,
                        device_another_name=device_another_name,
                    )
                    models.FaultAlarm.objects.using('alarm_module_write').create(
                        device_another_name=device_another_name,
                        start_time=datetime.datetime.now(),
                        aggr_alarm=new_aggr_alarm,
                        **data_dic
                    )
            # 当天存在相同的聚合告警信息，则创建告警并关联至该聚合告警
            else:
                today_aggr_alarm = today_aggr_alarms.last()

                # 更新聚合告警故障时长及状态等相关字段
                today_aggr_alarm.duration = round((datetime.datetime.now() - today_aggr_alarm.start_time).seconds
                                                  / 3600, 2)
                today_aggr_alarm.note = data_dic['note']
                today_aggr_alarm.type = type_
                today_aggr_alarm.point = data_dic['point']
                today_aggr_alarm.details = data_dic['details']
                today_aggr_alarm.end_time = None
                today_aggr_alarm.status = 0
                today_aggr_alarm.save()

                # 创建告警并关联至该聚合告警
                models.FaultAlarm.objects.using('alarm_module_write').create(
                    device_another_name=device_another_name,
                    start_time=datetime.datetime.now(),
                    aggr_alarm=today_aggr_alarm,
                    **data_dic
                )

        # 已存在未恢复的离线告警记录，则跳过====>不再跳过，而是更新一下status、type和note等，兼容设备由“离线”转为“通信异常”（例如：EMS已恢复，但PCS或BMS未恢复时）
        else:
            alarm_ = alarm.first()
            alarm_.type = type_
            alarm_.status = status
            alarm_.note = note
            alarm_.details = data_dic['details']
            alarm_.point = data_dic['point']
            alarm_.save()

            # 如存在关联的聚合告警，则更新聚合告警的故障时长及状态等相关字段
            if alarm_.aggr_alarm:
                aggr_alarm = alarm_.aggr_alarm
                aggr_alarm.duration = round((datetime.datetime.now() - aggr_alarm.start_time).seconds / 3600, 2)
                aggr_alarm.note = note
                aggr_alarm.type = type_
                aggr_alarm.point = data_dic['point']
                aggr_alarm.details = data_dic['details']
                aggr_alarm.end_time = None
                aggr_alarm.status = 0
                aggr_alarm.save()

            # 未存在关联的聚合告警，则新增聚合告警，并关联至该聚合告警
            else:
                new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                    **data_dic,
                    start_time=now,
                    device_another_name=device_another_name,
                )
                alarm_.aggr_alarm = new_aggr_alarm
                alarm_.save()

    # 设备（恢复）在线
    else:
        # 不存在未恢复的记录，则跳过
        if not alarm.exists():
            pass

        # 已存在记录,则由’离线‘变为’在线‘
        else:
            print(f'------------{station.station_name}的设备{device}已恢复在线')
            last_alarm = alarm.first()

            # 由’离线‘变为’在线‘时，更新end_time
            # last_alarm.update(end_time=datetime.datetime.now(), **data_dic)
            last_alarm.end_time = datetime.datetime.now()
            last_alarm.status = 1
            last_alarm.note = note
            last_alarm.save()

            # 如存在关联的聚合告警，则更新聚合告警的故障时长及状态等相关字段
            if last_alarm.aggr_alarm:
                aggr_alarm = last_alarm.aggr_alarm
                aggr_alarm.duration = round((datetime.datetime.now() - aggr_alarm.start_time).seconds / 3600, 2)
                aggr_alarm.note = note
                aggr_alarm.type = type_
                aggr_alarm.point = data_dic['point']
                aggr_alarm.details = data_dic['details']
                aggr_alarm.end_time = now
                aggr_alarm.status = 1
                aggr_alarm.save()

            # 未存在关联的聚合告警，则新增聚合告警，并关联至该聚合告警
            else:
                new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                    **data_dic,
                    start_time=last_alarm.start_time,
                    end_time=now,
                    device_another_name=device_another_name,
                )
                last_alarm.aggr_alarm = new_aggr_alarm
                last_alarm.save()


def is_device_online(device_json):
    non_null_count = 0
    for key, value in device_json.items():
        if value is not None and value != '' and value != '--':
            non_null_count += 1

    return non_null_count > 4


def check_status_online_status(m_station):

    ems_station = m_station.stationdetails_set.filter(is_delete=0, english_name=m_station.english_name).first()
    conn_ = get_redis_connection("3")
    # EMS
    key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', ems_station.english_name, 'EMS')

    try:
        status_ems = conn_.get(key1)
        # print(22, results)

        if status_ems:
            status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
            is_online = is_device_online(status_ems_dict)

            # if not is_online:
            #     print(235, ems_station.english_name, 'EMS', ':', *(status_ems_dict.keys()))

            online_status = 1 if is_online else 0
        else:
            online_status = 0

        EMS_status = online_status

        # 入库
        update_station_online_status_v2(ems_station, 'EMS', online_status)
    except Exception as e:
        logging.error(e)

    s_stations = m_station.stationdetails_set.filter(is_delete=0).all()
    for station in s_stations:
        if station.slave != 0:
            # BMS 和 PCS
            units = station.unit_set.filter(is_delete=0).all()
            for unit in units:
                # BMS
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
                try:
                    status_bms = conn_.get(key2)
                    # print(86, results)

                    if status_bms:
                        status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                        is_online = is_device_online(status_bms_dict)

                        # if not is_online:
                        #     print(260, station.english_name, unit.bms, ':', *(status_bms_dict.keys()))

                        online_status = 1 if is_online else 0
                    else:
                        online_status = 0

                    # 入库
                    update_station_online_status_v2(station, unit.bms, online_status, EMS_status=EMS_status)

                except Exception as e:
                    logging.error(e)

                # PCS
                key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.pcs)

                try:
                    status_pcs = conn_.get(key3)
                    # print(105, results)
                    if status_pcs:
                        status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                        is_online = is_device_online(status_pcs_dict)

                        # if not is_online:
                        #     print(288, station.english_name, unit.pcs, ':', *(status_pcs_dict.keys()))

                        online_status = 1 if is_online else 0
                    else:
                        online_status = 0

                    # 入库
                    update_station_online_status_v2(station, unit.pcs, online_status, EMS_status=EMS_status)

                except Exception as e:
                    logging.error(e)


def check_all_stations():

    m_stations = models.MaterStation.objects.filter(is_delete=0).all()

    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        for m_station in m_stations:
            executor.submit(check_status_online_status, m_station)
            print(133, f"正在检查{m_station.name}的设备在线状态。。。。。。")


# check_all_stations()


# if __name__ == '__main__':
#     # check_all_stations()
#     loop = asyncio.get_event_loop()
#
#     # 将协程包装成任务并添加到事件循环中
#     task = loop.create_task(check_all_stations())
#
#     # 运行事件循环直到所有任务完成
#     loop.run_until_complete(task)
#
#     # 关闭事件循环
#     loop.close()
