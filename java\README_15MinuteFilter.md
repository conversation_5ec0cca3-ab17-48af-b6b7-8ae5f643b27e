# 15分钟时间间隔过滤功能使用指南

## 功能概述

15分钟过滤功能可以从SQL查询结果中只保留从00:00:00开始每15分钟的数据点，即：
- 00:00:00, 00:15:00, 00:30:00, 00:45:00
- 01:00:00, 01:15:00, 01:30:00, 01:45:00
- 以此类推...

这个功能主要用于：
1. **减少数据量**：大幅降低前端需要处理的数据点数量
2. **提高性能**：减少网络传输和前端渲染时间
3. **优化显示**：在长时间跨度的图表中提供更清晰的数据展示

## 核心类和方法

### 1. SqlResultTransformer

#### 基础转换方法（支持15分钟过滤）

```java
// 时间序列格式转换
Map<String, Map<String, Object>> transformToTimeSeriesFormat(List<Map<String, Object>> sqlResults, boolean filter15Minutes)

// 前端友好格式转换
Map<String, Object> transformToFrontendFormat(List<Map<String, Object>> sqlResults, boolean filter15Minutes)

// ECharts图表格式转换
Map<String, Object> transformToEChartsFormat(List<Map<String, Object>> sqlResults, List<String> selectedFields, boolean filter15Minutes)
```

#### 专用过滤方法

```java
// 直接过滤SQL结果
List<Map<String, Object>> filterBy15Minutes(List<Map<String, Object>> sqlResults)

// 检查单个时间点是否符合15分钟规则
private boolean isValid15MinuteTime(Object timeValue)
```

### 2. TimeSeriesDataService

#### 设备监控数据获取

```java
// 获取设备监控数据（支持15分钟过滤）
Map<String, Object> getDeviceMonitoringData(List<Map<String, Object>> sqlResults, boolean filter15Minutes)

// 获取图表数据（支持15分钟过滤）
Map<String, Object> getChartData(List<Map<String, Object>> sqlResults, String chartType, List<String> selectedFields, boolean filter15Minutes)
```

## 使用示例

### 1. 基础使用

```java
@Autowired
private SqlResultTransformer sqlResultTransformer;

// 从数据库查询原始数据
List<Map<String, Object>> sqlResults = queryFromDatabase();

// 方式1：直接过滤数据
List<Map<String, Object>> filteredData = sqlResultTransformer.filterBy15Minutes(sqlResults);

// 方式2：转换时启用过滤
Map<String, Map<String, Object>> timeSeriesData = 
    sqlResultTransformer.transformToTimeSeriesFormat(sqlResults, true);
```

### 2. Service层使用

```java
@Autowired
private TimeSeriesDataService timeSeriesDataService;

// 获取设备监控数据（启用15分钟过滤）
Map<String, Object> monitoringData = 
    timeSeriesDataService.getDeviceMonitoringData(sqlResults, true);

// 获取图表数据（启用15分钟过滤）
List<String> fields = Arrays.asList("vol", "cur", "soc");
Map<String, Object> chartData = 
    timeSeriesDataService.getChartData(sqlResults, "line", fields, true);
```

### 3. Controller层使用

```java
@GetMapping("/device-data")
public Map<String, Object> getDeviceData(
        @RequestParam String stationId,
        @RequestParam String deviceId,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
        @RequestParam(defaultValue = "false") boolean filter15Minutes) {
    
    List<Map<String, Object>> sqlResults = queryDeviceData(stationId, deviceId, startTime, endTime);
    Map<String, Object> result = timeSeriesDataService.getDeviceMonitoringData(sqlResults, filter15Minutes);
    
    return Map.of("success", true, "data", result);
}
```

## 智能过滤策略

### 自动决策逻辑

系统可以根据以下条件自动决定是否启用15分钟过滤：

```java
private boolean shouldUseFilter(List<Map<String, Object>> sqlResults, LocalDateTime startTime, LocalDateTime endTime) {
    // 数据量超过500条时使用过滤
    if (sqlResults.size() > 500) {
        return true;
    }
    
    // 时间跨度超过24小时时使用过滤
    long hours = java.time.Duration.between(startTime, endTime).toHours();
    if (hours > 24) {
        return true;
    }
    
    return false;
}
```

### 智能API接口

```java
@GetMapping("/smart-data")
public Map<String, Object> getSmartData(
        @RequestParam String stationId,
        @RequestParam String deviceId,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
    
    List<Map<String, Object>> sqlResults = queryDeviceData(stationId, deviceId, startTime, endTime);
    
    // 智能决策是否使用过滤
    boolean useFilter = shouldUseFilter(sqlResults, startTime, endTime);
    
    Map<String, Object> result = timeSeriesDataService.getDeviceMonitoringData(sqlResults, useFilter);
    
    // 添加决策信息
    result.put("smartDecision", Map.of(
        "autoFilterEnabled", useFilter,
        "reason", getFilterReason(sqlResults, startTime, endTime)
    ));
    
    return Map.of("success", true, "data", result);
}
```

## 过滤效果统计

### 数据量对比

启用15分钟过滤后，可以获得详细的统计信息：

```java
Map<String, Object> result = timeSeriesDataService.getDeviceMonitoringData(sqlResults, true);

// 过滤统计信息
Map<String, Object> filterStats = (Map<String, Object>) result.get("filterStats");
System.out.println("原始数据量: " + filterStats.get("originalCount"));
System.out.println("过滤后数据量: " + filterStats.get("filteredCount"));
System.out.println("数据减少比例: " + filterStats.get("filterRatio"));
```

### 性能提升

典型的性能提升效果：

| 时间跨度 | 原始数据点 | 过滤后数据点 | 减少比例 | 性能提升 |
|---------|-----------|-------------|---------|---------|
| 1小时   | 12点      | 4点         | 66.7%   | 中等    |
| 6小时   | 72点      | 24点        | 66.7%   | 显著    |
| 24小时  | 288点     | 96点        | 66.7%   | 显著    |
| 7天     | 2016点    | 672点       | 66.7%   | 极大    |

## 时间格式支持

过滤功能支持多种时间格式：

```java
// LocalDateTime
LocalDateTime.of(2025, 6, 1, 0, 15, 0)  // ✓ 符合规则

// java.sql.Timestamp
Timestamp.valueOf("2025-06-01 00:30:00")  // ✓ 符合规则

// java.util.Date
new Date()  // 根据具体时间判断

// 字符串格式
"2025-06-01 00:45:00"  // ✓ 符合规则
```

## 注意事项

### 1. 时间精度要求

- 秒数必须为0
- 分钟数必须是15的倍数（0, 15, 30, 45）
- 小时和日期不限制

### 2. 数据完整性

- 过滤不会修改原始数据，只是选择性保留
- 被过滤掉的数据点不会丢失，只是不参与展示
- 可以随时切换是否启用过滤

### 3. 业务场景建议

**适合使用15分钟过滤的场景：**
- 长时间跨度的趋势分析
- 大数据量的图表展示
- 移动端或性能敏感的应用
- 概览性的监控面板

**不适合使用15分钟过滤的场景：**
- 需要精确到分钟的分析
- 短时间内的异常检测
- 实时监控告警
- 详细的故障诊断

## API接口示例

### 1. 基础数据获取

```bash
# 不启用过滤
GET /api/monitoring/device-data?stationId=station1&deviceId=BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59&filter15Minutes=false

# 启用过滤
GET /api/monitoring/device-data?stationId=station1&deviceId=BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59&filter15Minutes=true
```

### 2. 智能数据获取

```bash
# 系统自动决定是否过滤
GET /api/monitoring/smart-data?stationId=station1&deviceId=BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-07 23:59:59
```

### 3. 过滤统计信息

```bash
# 获取过滤效果统计
GET /api/monitoring/filter-stats?stationId=station1&deviceId=BMS1&startTime=2025-06-01 00:00:00&endTime=2025-06-01 23:59:59
```

## 测试用例

项目包含完整的测试用例，位于 `java/test/SqlResultTransformerTest.java`：

- `test15MinuteFilter()` - 测试15分钟过滤功能
- `testTransformWithFilter()` - 测试带过滤的转换方法
- `create15MinuteTestData()` - 创建测试数据

运行测试：

```bash
mvn test -Dtest=SqlResultTransformerTest#test15MinuteFilter
```

## 总结

15分钟过滤功能是一个强大的数据优化工具，可以在保持数据趋势完整性的同时大幅提升系统性能。通过合理使用这个功能，可以为用户提供更流畅的数据查看体验。
