#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-12 09:27:05


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class Province(user_Base):
    u'省份表'
    __tablename__ = "c_province"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    province = Column(VARCHAR(256), nullable=False, comment=u"省/直辖市/自治区")
    # area_id = Column(Integer, ForeignKey("t_side_forecase_area.id"), nullable=False, comment=u"所属区域")
    area_id = Column(Integer, nullable=False, comment=u"所属区域")
    en_province = Column(VARCHAR(256), nullable=False, comment=u"省/直辖市/自治区")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
                
    def __repr__(self):
        
        return "{'id':'%s','province':'%s','area_id':'%s','en_province':'%s'}" % (
            self.id,self.province,self.area_id,self.en_province)
        
    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}