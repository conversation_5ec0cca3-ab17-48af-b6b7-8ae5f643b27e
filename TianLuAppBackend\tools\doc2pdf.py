# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/4/10 9:43
# <AUTHOR> <PERSON><PERSON>ang
# @Project  : TianLuAppBackend
# @File     : doc2pdf.py
# @Software : PyCharm


import os
import comtypes.client
from docxtpl import DocxTemplate

def word_create():
    # doc = DocxTemplate('./input1.docx')
    # doc.render(data)
    # doc.save('./nwe_word.docx')
    word = comtypes.client.CreateObject('Word.Application')
    word.Visiable = 0
    path = os.getcwd()
    word_path = os.path.join(path, 'output.docx')
    pdf_path = os.path.join(path, 'output.pdf')
    new_pdf = word.Documents.Open(word_path)
    new_pdf.SaveAs(pdf_path, 17)
    new_pdf.Close()
    return 1


if __name__ == '__main__':
    word_create()
