# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/22 下午4:58
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Project  : TianLuAppBackend
# @File     : urls.py
# @Software : PyCharm

from django.urls import path

from apis.web2.running_analysis import views

urlpatterns = [
    path('analysis_list', views.AnalysisListView.as_view()),  # 分析列表        en
    path('analysis_download', views.AnalysisDownloadView.as_view()),  # 运行分析：下载     en
    path('analysis_alarms/<int:id>', views.AnalysisAlarmDetailListView.as_view()),  # 查看故障      en
    path('analysis_running_data', views.StationRunningDataviewView.as_view()),      # 运行分析：查看运行数据       en
    path('analysis_feedback/<int:id>', views.AnalysisFeedbackView.as_view()),  # 运行分析反馈：查看、新增&修改        en
    path('analysis_message/<int:id>', views.AnalysisMessageView.as_view()),  # 运行分析推送至消息中心 & 消息详情       en
]
