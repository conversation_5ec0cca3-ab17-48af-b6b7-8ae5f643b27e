package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 计划历史导出DTO
 */
@Data
@ApiModel("计划历史导出DTO")
public class PlanHistoryExportDTO {

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("状态: 1成功；2失败")
    private Integer status;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
