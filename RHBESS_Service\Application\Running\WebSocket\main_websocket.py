#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-18 10:19:43
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Running\WebSocket\main_websocket.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-18 15:06:27

import sys
import os

cmd = 'export PYTHONPATH=`dirname $(dirname $( dirname $(pwd)))`'
os.system(cmd)

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from Tools.Cfg.get_cnf import work_dir
from Application.Cfg.dir_cfg import model_config
import tornado.httpserver
import tornado.log
import logging
from tornado.options import options
from Application.Running.WebSocket.urls_websocket import routes

options.log_file_max_size = 20*1024*1024
options.log_file_num_backups = 10
options.log_file_prefix = os.path.join(work_dir, 'log/websocket.log')

class Application(tornado.web.Application):
    def __init__(self):
        handlers = routes
        self.settings = dict(
            cookie_secret="cou1q2TlSk61dN6L1wOv1aW/8SCochHpqZsADCn8Olk=",
            template_path=os.path.join(os.path.abspath(__file__), 'templates'),  # 设置模板路径
            static_path=os.path.join(os.path.abspath(__file__), 'static'),  # 设置静态资源引用路路径
            static_url_prefix='/static/',  # 设置html中静态文件的引用路径，默认为/static/
            debug=False,
            # 如果设置为一个数字，所有websockets将每n秒ping一次。这可以帮助通过某些关闭空闲连接的代理服务器保持连接处于活动状态，
            # 并且它可以检测websocket是否在没有正确关闭的情况下失败。
            websocket_ping_interval=28,
            # 如果设置了ping间隔，并且服务器在这么多秒内没有收到'pong'，它将关闭websocket。默认值为ping间隔的三倍，最少为30秒。
            # 如果未设置ping间隔，则忽略。
            websocket_ping_timeout=30,
            login_url="/UserLogin/userNull"  # 没登录就跳转到登录界面
        )
        super(Application, self).__init__(handlers, **self.settings)


# 格式化日志输出格式
# 默认是这种的：[I 160807 09:27:17 web:1971] 200 GET / (::1) 7.00ms
# 格式化成这种的：[2016-08-07 09:38:01 执行文件名:执行函数名:执行行数 日志等级] 内容消息
class LogFormatter(tornado.log.LogFormatter):
    def __init__(self):
        super(LogFormatter, self).__init__(
            fmt='%(color)s[%(asctime)s %(filename)s:%(funcName)s:%(lineno)d %(levelname)s]%(end_color)s %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

def main():
    tornado.options.define("port", default="19097", help="run on the port", type=int)  # 设置全局变量port
    tornado.options.parse_command_line()  # 启动应用前面的设置项目
    [i.setFormatter(LogFormatter()) for i in logging.getLogger().handlers]
    http_server = tornado.httpserver.HTTPServer(Application())
    http_server.bind(tornado.options.options.port)  # 在这里应用之前的全局变量port
    http_server.start(num_processes=2)
    # http_server.listen(tornado.options.options.port)
    tornado.ioloop.IOLoop.current().start()  # 启动监听


if __name__ == '__main__':
    logging.info("启动成功")
    main()
