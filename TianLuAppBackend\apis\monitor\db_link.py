import pymysql.cursors
import json
import logging

from TianLuAppBackend import settings

# 连接数据库
connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_origin_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_origin_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_origin_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_origin_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_origin_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
)

# error_log = logging.getLogger("error")
# def time_range_by_cumulant(app_name, station_name, start_time, end_time, device):
#     try:
#         with connection.cursor() as cursor:
#             # 执行SQL查询
#             sql = """SELECT data_info FROM device_notify_cumulant_record
#                         WHERE 1=1
#                         and app_name='{}'
#                         and station_name='{}'
#                         and type=1
#                         and time BETWEEN '{}' AND '{}'
#                         """.format(
#                 app_name, station_name, start_time, end_time
#             )
#             try:
#                 cursor.execute(sql)
#             except Exception as e:
#                 error_log.error(e)
#                 return []
#
#             # 获取查询结果
#             result = cursor.fetchall()
#
#             body_result = []
#             # 处理查询结果
#             if not result:
#                 return []
#             for row in result:
#                 if row:
#                     data_info = json.loads(row['data_info'])
#                     body_list = data_info['body']
#                     for body in body_list:
#                         # print(body)
#                         try:
#                             if body['device'] == device:
#                                 body_result.append({"time": data_info["time"], "PAE": body["PAE"], "NAE": body["NAE"]})
#                                 continue
#                         except:
#                             pass
#                     # print(data_info)  # 打印每一行的数据，以字典形式表示
#             return body_result
#     finally:
#         cursor.close()


# if __name__ == '__main__':
#     app_name = 'TN001'
#     station_name = 'NBLS001'
#     start_time = '2023-01-01'
#     end_time = '2023-08-24'
#     device = 'BMS'
#     time_range_by_cumulant(app_name, station_name, start_time, end_time,device)
