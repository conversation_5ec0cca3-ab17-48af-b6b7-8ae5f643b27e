import datetime
import json
import traceback
import uuid
import time
import os
import re
import geocoder
from django.conf import settings
from django.db import transaction, connections
from django.db.models import Sum, F, Q
from rest_framework.response import Response
from rest_framework.views import APIView
from openpyxl import Workbook

from TianLuAppBackend.settings import BASE_DIR
from apis.project_manage.filters import CustomProjectsFilter
from apis.project_manage.tools import remove_empty_string_keys
from apis.user import models
from apis.app2.utils import paging
from common import common_response_code
from LocaleTool.common import redis_pool
from middlewares.authentications import JWTHeaderAuthentication, DenyAuthentication, JwtParamAuthentication
from serializers import user_serializers
from serializers.project_manage import manage_serializers
from tools.gen_excel import create_excel, post2minio
from tools.minio_tool import MinioTool
from common.utils import abnormal_handle
from settings.types_dict import SCENE_FLAG, PROJECT_TYPE, DEMAND_CUMPUT

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# class CustomizationView(APIView):
#     """削峰填谷客制化配置"""
#
#     def get(self, request):
#         lang = request.headers.get("lang", 'zh')
#         all_projects = models.Project.objects.filter(is_used=1).all()
#         provinces = models.ElectricityProvince.objects.filter(project__in=all_projects).all()
#         result = {}
#         for province in provinces:
#             month = datetime.datetime.now().month
#             provinces_ins = province.peakvalley_set.filter(year_month=month).values("type", "level").annotate(
#                 province=F("province"))
#             ser = user_serializers.PeakValleySerializer(instance=provinces_ins, many=True)
#             result[province.name] = ser.data
#         return Response({"code": common_response_code.SUCCESS, "data": result})


# class CustomizationDetailView(APIView):
#     """当月电价时刻查询"""
#
#     def post(self, request):
#         ser = user_serializers.CustomizationDetailSerializer(data=request.data)
#         month = datetime.datetime.now().month
#         if not ser.is_valid():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         query_dic = request.data
#         province_ins = models.ElectricityProvince.objects.filter(id=ser.validated_data["id"]).first()
#         ser.validated_data.pop("id")
#         query_dic.pop("id")
#         price_ins = models.PeakValley.objects.filter(year_month=month, province=province_ins, **query_dic).first()
#         ins_ser = user_serializers.CustomizationDetailSerializer(instance=price_ins)
#         return Response({"code": common_response_code.SUCCESS, "data": ins_ser.data})


class ProjectsViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    filter_class = CustomProjectsFilter

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            user_id = request.user['user_id']
            page = int(request.query_params.get('pageNum', 1))
            page_size = int(request.query_params.get('pageSize', 20))
            name = request.query_params.get('name', None)
            province = request.query_params.get('province', None)
            level = request.query_params.get('level', None)
            type_ = request.query_params.get('type', None)

            # user = models.UserDetails.objects.filter(id=user_id, project__is_used=1).first()
            related_projects = models.Project.objects.filter(~Q(is_used=2), user__id=user_id)
            # query = Q()
            if name:
                related_projects = related_projects.filter(Q(name__icontains=name) | Q(en_name__icontains=name))
                # query &= Q(name__icontains=name)
            if province:
                related_projects = related_projects.filter(province_id=int(province))
            if level:
                related_projects = related_projects.filter(level=int(level))
            if type_:
                related_projects = related_projects.filter(type=int(type_))
            # related_projects = models.Project.objects.filter(~Q(is_used=2), query, user__id=user_id).all()
            related_projects = related_projects.values("id", "name", "province__name", "province__en_name", "project_type").order_by('-create_time')
            # 分页器
            page_res = paging(page, page_size, related_projects)
            new_projects_ins = page_res.get('data')
            data = []

            for project in new_projects_ins:

                project["station_count"] = models.MaterStation.objects.filter(project_id=project["id"],
                                                                              is_delete=0).count()
                project["unit_count"] = models.Unit.objects.filter(is_delete=0,
                    station__master_station__project_id=project["id"]).count()
                project["power"] = \
                    models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).exclude(
                        slave=0).aggregate(power=Sum("rated_power"))[
                        "power"]
                project["capacity"] = \
                    models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).exclude(
                        slave=0).aggregate(
                        capacity=Sum("rated_capacity"))[
                        "capacity"
                    ]
                station = models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).first()

                project['type_num'] = station.type if station else '--'

                if lang == 'zh':
                    project["electricity_standard"] = "代理购电"
                    project["address"] = station.address if station else '--'
                    project["electricity_type"] = models.PeakValleyNew.TYPE_CHOICE[station.type - 1][1] if station else '--'
                    project["electricity_level"] = models.PeakValleyNew.LEVEL_CHOICE[station.level - 1][
                        1] if station else '--'
                else:
                    project['province__name'] = project['province__en_name']
                    project["electricity_standard"] = "Official electricity price"
                    project["address"] = station.en_address if station else '--'
                    project["electricity_type"] = models.PeakValleyNew.EN_TYPE_CHOICE[station.type - 1][1] if station else '--'
                    project["electricity_level"] = models.PeakValleyNew.EN_LEVEL_CHOICE[station.level - 1][
                        1] if station else '--'

                project['level_num'] = station.level if station else '--'
                project["project_type"] = PROJECT_TYPE.get(project['project_type'])[lang] if project.get('project_type') != None else '--'
                # project["electricity_type"] = models.PeakValley.TYPE_CHOICE[project['type'] - 1][1]
                # project["electricity_level"] = models.PeakValley.LEVEL_CHOICE[project['level'] - 1][1]

                user_price = models.UnitPrice.objects.filter(project_id=project["id"], delete=0).last()  # 判断是否配置峰平谷电价
                if user_price:
                    project["electricity_standard"] = "自定义电价" if lang == 'zh' else "Customized electricity price"
                    project["electricity_type"] = "-----"
                data.append(project)

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data},
                    "total": page_res.get('total'),
                    "totalpage": page_res.get('totalpage'),
                    "page": page_res.get('page'),
                    "page_size": page_size
                }
            )
        #
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！" if lang == 'zh' else "Query failed!"},
            })


class ProjectsDownloadViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    filter_class = CustomProjectsFilter

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            user_id = request.user['user_id']
            # page = int(request.query_params.get('pageNum', 1))
            # page_size = int(request.query_params.get('pageSize', 20))
            name = request.query_params.get('name', None)
            province = request.query_params.get('province', None)
            level = request.query_params.get('level', None)
            type_ = request.query_params.get('type', None)

            related_projects = models.Project.objects.filter(~Q(is_used=2), user__id=user_id)
            if name:
                related_projects = related_projects.filter(Q(name__icontains=name) | Q(en_name__icontains=name))
            if province:
                related_projects = related_projects.filter(province_id=int(province))
            if level:
                related_projects = related_projects.filter(level=int(level))
            if type_:
                related_projects = related_projects.filter(type=int(type_))
            # related_projects = models.Project.objects.filter(~Q(is_used=2), query, user__id=user_id).all()
            related_projects = related_projects.values("id", "name", "en_name", "province__name", "province__en_name", "project_type").order_by('-create_time')
            # 分页器
            # page_res = paging(page, page_size, related_projects)
            # new_projects_ins = page_res.get('data')
            data = []
            # 筛选
            # project_filter = self.filter_class(queryset=related_projects, data=request.query_params)
            # tem_project_instances = project_filter.qs
            # print(tem_project_instances)

            # 分页
            # paginator_info = dict()
            # if page and page_size:
            #     paginator = Paginator(related_projects, page_size)
            #     tem_project_instances_ = paginator.get_page(page)
            #
            #     paginator_info = {
            #         "page": page,
            #         "page_size": page_size,
            #         "pages": paginator.num_pages,
            #         "total_count": paginator.count
            #     }
            # else:
            # tem_project_instances_ = related_projects
            # new_projects_ins = new_projects_ins.values("id", "name", "province__name")
            for project in related_projects:
                # try:
                # project["station_count"] = models.StationDetails.objects.filter(project_id=project["id"]).count()
                project["station_count"] = models.MaterStation.objects.filter(project_id=project["id"],
                                                                              is_delete=0).count()
                project["unit_count"] = models.Unit.objects.filter(is_delete=0,
                    station__master_station__project_id=project["id"]).count()
                project["power"] = \
                    models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).exclude(
                        slave=0).aggregate(power=Sum("rated_power"))[
                        "power"]
                project["capacity"] = \
                    models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).exclude(
                        slave=0).aggregate(
                        capacity=Sum("rated_capacity"))[
                        "capacity"
                    ]
                station = models.StationDetails.objects.filter(is_delete=0, master_station__project_id=project["id"]).first()
                project['type_num'] = station.type if station else '--'
                project["project_type"] = PROJECT_TYPE.get(project['project_type'])[lang] if project.get('project_type') != None else '--'
                if lang == 'zh':
                    project["electricity_standard"] = "代理购电"
                    project["address"] = station.address if station else '--'
                    project["electricity_type"] = models.PeakValleyNew.TYPE_CHOICE[station.type - 1][1] if station else '--'
                    project["electricity_level"] = models.PeakValleyNew.LEVEL_CHOICE[station.level - 1][
                        1] if station else '--'
                else:
                    project['province__name'] = project['province__en_name']
                    project["electricity_standard"] = "Official electricity price"
                    project["address"] = station.en_address if station else '--'
                    project["electricity_type"] = models.PeakValleyNew.EN_TYPE_CHOICE[station.type - 1][1] if station else '--'
                    project["electricity_level"] = models.PeakValleyNew.EN_LEVEL_CHOICE[station.level - 1][
                        1] if station else '--'

                user_price = models.UnitPrice.objects.filter(project_id=project["id"], delete=0).last()  # 判断是否配置峰平谷电价
                if user_price:
                    project["electricity_standard"] = "自定义电价" if lang == 'zh' else "Customized electricity price"
                    project["electricity_type"] = "-----"
                data.append(project)

            temp_list = []
            if len(data):
                for i in data:
                    temp_list.append([i['name'], i['project_type'], i['station_count'], i['unit_count'], i['power'], i['capacity'],
                                      i['address'], i['province__name'], i['electricity_type'], i['electricity_level']])

            # 生成excel
            if lang == 'zh':
                file_name = f"项目信息记录{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
                sheet1 = f'项目信息记录'
                sheet1_headers = ['项目名称', '项目类型', '并网点数量', '储能单元数量', '额定功率（kW）', '额定容量（kWh）', '地址', '省份', '用电类型',
                                  '电压等级']
            else:
                file_name = f"Project information record {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
                sheet1 = f'Project information record'
                sheet1_headers = ['Project name', 'Project Type', 'Installations', 'Units',
                                  'Nominal Power (kW)', 'Nominal capacity (kWh)', 'address', 'province', 'Electricity type',
                                  'voltage level']

            file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)

            create_excel(file_path, sheet_name=sheet1, headers=sheet1_headers, data=temp_list)

            download_url = post2minio(file_path, object_name=file_name)

            os.remove(file_path)

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "url": download_url
                },
                })
        except Exception as e:
            # transaction.rollback()
            error_log.error(traceback.print_exc())
            # print(traceback.print_exc())
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "下载失败！" if lang == 'zh' else 'Download failed!'},
            })


class ProjectsAddViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        user_id = request.user['user_id']
        user = models.UserDetails.objects.get(id=user_id)

        try:
            data = request.data
            project_name = request.data.get("name")
            project_english_name = request.data.get("english_name")
            address = request.data.get("address")
            province = request.data.get("province_id")
            organization = request.data.get("organization_id")
            manager = request.data.get("manager")
            manager_phone = request.data.get("manager_phone")
            price_type = request.data.get("price_type", 1)
            _type = request.data.get("type", 1)
            level = request.data.get("level", None)
            self_price = request.data.get("self_price", None)
            stations = json.loads(data.get('stations', []))  # 权限id列表
            # transformer_capacity = request.data.get("transformer_capacity", None)
            # meter_position = request.data.get("meter_position", 1)

            province_name = models.ElectricityProvince.objects.filter(id=province).first().name
            g = geocoder.google("1403 Washington Ave, New Orleans, LA 70130")
            g = geocoder.arcgis(province_name)
            c = g.latlng
            project_ins = models.Project.objects.create(
                name=project_name,
                english_name=project_english_name,
                address=address if address else '未填写',
                province_id=province,
                organization_id=organization,
                manager=manager,
                manager_phone=manager_phone,
                longitude=c[1],
                latitude=c[0],
                city=province_name,
                in_time=datetime.datetime.now(),
                price_type=price_type,
                type=_type,
                station_number=len(stations),
                rated_power=100 * len(stations),
                rated_capacity=float(232.96 * len(stations)),
                level=level
            )
            # project_ins.save()
            user.project_set.add(project_ins)
            user.save()

            num = 1
            if self_price:
                self_price = json.loads(self_price)
                for p in self_price:
                    name = p["title"]
                    start_time = p["start_time"]
                    end_time = p["end_time"]
                    summer_start = p["summer_start"]
                    summer_end = p["summer_end"]
                    nosummer_start = p["nosummer_start"]
                    nosummer_end = p["nosummer_end"]
                    summer_spike = p["summer_spike"]
                    summer_peak = p["summer_peak"]
                    summer_flat = p["summer_flat"]
                    summer_valley = p["summer_valley"]
                    no_summer_spike = p["no_summer_spike"]
                    no_summer_peak = p["no_summer_peak"]
                    no_summer_flat = p["no_summer_flat"]
                    no_summer_valley = p["no_summer_valley"]
                    exist = models.UnitPrice.objects.filter(name=name, delete=0, project=project_ins).exists()
                    if exist:
                        # transaction.rollback()
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": "error", "detail": f"电价名称{name}已存在"},
                            }
                        )
                    if end_time <= start_time:
                        # transaction.rollback()
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": "error", "detail": "结束时间不能小于开始时间"},
                            }
                        )
                    models.UnitPrice.objects.create(
                        project=project_ins,
                        uid=uuid.uuid4(),
                        name=name,
                        start=start_time,
                        end=end_time,
                        summer_start=summer_start,
                        summer_end=summer_end,
                        nosummer_start=nosummer_start,
                        nosummer_end=nosummer_end,
                        summer_spike=summer_spike,
                        summer_peak=summer_peak,
                        summer_flat=summer_flat,
                        summer_valley=summer_valley,
                        no_summer_spike=no_summer_spike,
                        no_summer_peak=no_summer_peak,
                        no_summer_flat=no_summer_flat,
                        no_summer_valley=no_summer_valley,
                    )
            else:
                project_ins.level = level
                project_ins.save()

            for station in stations:
                master_station = models.MaterStation.objects.create(
                    project=project_ins,
                    name=station['station_name'],
                    english_name=station["english_name"],
                )
                user.master_stations.add(master_station)

                station_ins = models.StationDetails.objects.create(
                    project=project_ins,
                    db=project_english_name,
                    app=station["app"],
                    station_name=station["station_name"],
                    english_name=station["english_name"],
                    meter=station["meter"],
                    specifications=station["meter"],
                    rated_power=int(station["meter"]) * 100,
                    rated_capacity=str(int(station["meter"]) * 232.96),
                    pcs_number=station["meter"],
                    unit_number=station["meter"],
                    fire_fighting=station["meter"],
                    cabinet=station["meter"],
                    battery_cluster=station["meter"],
                    address=address if address else '未填写',
                    emq_user=station["emq_user"],
                    emq_pwd=station["emq_pwd"],
                    emq_clid=station["emq_clid"],
                    ini_num=station["ini_num"],
                    int_num=station["int_num"],
                    meter_position=station['meter_position'],
                    transformer_capacity=str(station['transformer_capacity']),
                    type=_type,
                    level=level,
                    province_id=province,
                    meter_type=2,
                    meter_count=1,
                    master_station=master_station
                )
                user.stations.add(station_ins)
                for i in range(station["meter"]):
                    if station["meter"] == 1:
                        BMS = f"BMS"
                        PCS = f"PCS"
                    else:
                        BMS = f"BMS{i + 1}"
                        PCS = f"PCS{i + 1}"
                    # count = models.Unit.objects.all().count()
                    u = str(uuid.uuid4())[:16]
                    models.Unit.objects.create(
                        unit_name=f"储能单元 {i + 1}",
                        unit_new_name=f"储能单元 {num}",
                        english_name=f"PCS{u}",
                        station=station_ins,
                        bms=BMS,
                        pcs=PCS,
                        user=user
                    )
                    num += 1
                # transaction.commit()
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "添加成功！"},
            })
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "添加失败！"},
            })


class ProjectsDelViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        p_id = request.data.get('id')
        if p_id:
            # 获取要修改的对象
            obj = models.Project.objects.get(id=p_id)
            # 删除关联的逻辑主站
            master_stations = obj.materstation_set.filter(is_delete=0)
            if master_stations.exists():
                for master_station in master_stations:
                    # 删除关联的标准（从）站
                    slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                    if slave_stations.exists():
                        for slave_station in slave_stations:
                            # 删除关联的储能单元
                            units = slave_station.unit_set.filter(is_delete=0).all()
                            if units.exists():
                                for unit in units:
                                    unit.is_delete = 1
                                    unit.save()

                            # 删除关联的告警信息
                            # models.FaultAlarm.objects.filter(station=slave_station).delete()

                            slave_station.is_delete = 1
                            slave_station.save()

                    master_station.is_delete = 1
                    master_station.save()

            # 修改字段的值
            obj.is_used = 2
            # 保存修改后的对象
            obj.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "删除成功！" if lang == 'zh' else 'Delete success.'},
            })

        else:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "id为必填项" if lang == 'zh' else 'ID is required.'},
            })


class ProjectsInfoViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, *args, **kwargs):
        p_id = self.kwargs['id']
        try:
            project_ins = models.Project.objects.get(id=p_id)
        except:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到"},
            })
        project_dic = {
            "id": project_ins.id,
            "name": project_ins.name,
            "english_name": project_ins.english_name,
            "address": project_ins.address,
            "province_id": project_ins.province_id,
            "organization_id": project_ins.organization_id,
            "manager": project_ins.manager,
            "manager_phone": project_ins.manager_phone,
            "type": project_ins.type,
            "price_type": project_ins.price_type,
            "level": project_ins.level,
            # "transformer_capacity": project_ins.transformer_capacity,
            # "meter_position": project_ins.meter_position
        }
        master_stations = models.MaterStation.objects.filter(project=project_ins, is_delete=0).values(
            "id", "name", "english_name"
        )
        project_dic['master_stations'] = master_stations
        station_ins = models.StationDetails.objects.filter(project=project_ins, is_delete=0).values(
            "id",
            "app",
            "station_name",
            "english_name",
            "meter",
            "emq_user",
            "emq_pwd",
            "emq_clid",
            "ini_num",
            "int_num",
            "transformer_capacity",
            "meter_position",
            "master_station_id"
        )
        project_dic['stations'] = station_ins
        self_price = models.UnitPrice.objects.filter(project=project_ins).values(
            "id",
            "name",
            "start",
            "end",
            "summer_start",
            "summer_end",
            "nosummer_start",
            "nosummer_end",
            "summer_spike",
            "summer_peak",
            "summer_flat",
            "summer_valley",
            "no_summer_spike",
            "no_summer_peak",
            "no_summer_flat",
            "no_summer_valley",
        )
        project_dic['self_price'] = self_price
        return Response(project_dic)

    def put(self, request, *args, **kwargs):
        data = request.data
        p_id = data['id']
        project_name = request.data.get("name")
        project_english_name = request.data.get("english_name")
        address = request.data.get("address")
        province = request.data.get("province_id")
        organization = request.data.get("organization_id")
        manager = request.data.get("manager")
        manager_phone = request.data.get("manager_phone")
        price_type = request.data.get("price_type", 1)
        _type = request.data.get("type", 1)
        level = request.data.get("level", None)
        # transformer_capacity = request.data.get("transformer_capacity", None)
        # meter_position = request.data.get("meter_position", 1)
        self_price = request.data.get("self_price", None)
        stations = request.data.get("stations", None)
        # 修改project表
        project_ins = models.Project.objects.get(id=p_id)
        project_ins.name = project_name
        project_ins.english_name = project_english_name
        project_ins.address = address if address else '未填写'
        project_ins.province_id = province
        if organization:
            project_ins.organization_id = organization
        project_ins.manager = manager
        project_ins.manager_phone = manager_phone
        # project_ins.transformer_capacity = str(transformer_capacity)
        # project_ins.meter_position = meter_position
        project_ins.price_type = price_type
        project_ins.type = _type
        project_ins.level = level
        project_ins.save()
        # 修改电价
        if self_price:
            price_ids_by_pid = models.UnitPrice.objects.filter(project=project_ins).values("id")
            price_ids_by_pid = [i['id'] for i in price_ids_by_pid]
            self_price = json.loads(self_price)
            for p in self_price:
                p_id = p.get('id')
                if p_id:
                    price_ids_by_pid.remove(p_id)
                    name = p["title"]
                    start_time = p["start_time"]
                    end_time = p["end_time"]
                    summer_start = p["summer_start"]
                    summer_end = p["summer_end"]
                    nosummer_start = p["nosummer_start"]
                    nosummer_end = p["nosummer_end"]
                    summer_spike = p["summer_spike"]
                    summer_peak = p["summer_peak"]
                    summer_flat = p["summer_flat"]
                    summer_valley = p["summer_valley"]
                    no_summer_spike = p["no_summer_spike"]
                    no_summer_peak = p["no_summer_peak"]
                    no_summer_flat = p["no_summer_flat"]
                    no_summer_valley = p["no_summer_valley"]

                    price_ins = models.UnitPrice.objects.get(id=p_id)
                    price_ins.name = name
                    price_ins.start = start_time
                    price_ins.end = end_time
                    price_ins.summer_start = summer_start
                    price_ins.summer_end = summer_end
                    price_ins.nosummer_start = nosummer_start
                    price_ins.nosummer_end = nosummer_end
                    price_ins.summer_spike = summer_spike
                    price_ins.summer_peak = summer_peak
                    price_ins.summer_flat = summer_flat
                    price_ins.summer_valley = summer_valley
                    price_ins.no_summer_spike = no_summer_spike
                    price_ins.no_summer_peak = no_summer_peak
                    price_ins.no_summer_flat = no_summer_flat
                    price_ins.no_summer_valley = no_summer_valley
                    price_ins.save()
                else:
                    name = p["title"]
                    start_time = p["start_time"]
                    end_time = p["end_time"]
                    summer_start = p["summer_start"]
                    summer_end = p["summer_end"]
                    nosummer_start = p["nosummer_start"]
                    nosummer_end = p["nosummer_end"]
                    summer_spike = p["summer_spike"]
                    summer_peak = p["summer_peak"]
                    summer_flat = p["summer_flat"]
                    summer_valley = p["summer_valley"]
                    no_summer_spike = p["no_summer_spike"]
                    no_summer_peak = p["no_summer_peak"]
                    no_summer_flat = p["no_summer_flat"]
                    no_summer_valley = p["no_summer_valley"]
                    exist = models.UnitPrice.objects.filter(name=name, delete=0, project=project_ins).exists()
                    if exist:
                        # transaction.rollback()
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": "error", "detail": f"电价名称{name}已存在"},
                            }
                        )
                    if end_time <= start_time:
                        # transaction.rollback()
                        return Response(
                            {
                                "code": common_response_code.FIELD_ERROR,
                                "data": {"message": "error", "detail": "结束时间不能小于开始时间"},
                            }
                        )
                    models.UnitPrice.objects.create(
                        project=project_ins,
                        uid=uuid.uuid4(),
                        name=name,
                        start=start_time,
                        end=end_time,
                        summer_start=summer_start,
                        summer_end=summer_end,
                        nosummer_start=nosummer_start,
                        nosummer_end=nosummer_end,
                        summer_spike=summer_spike,
                        summer_peak=summer_peak,
                        summer_flat=summer_flat,
                        summer_valley=summer_valley,
                        no_summer_spike=no_summer_spike,
                        no_summer_peak=no_summer_peak,
                        no_summer_flat=no_summer_flat,
                        no_summer_valley=no_summer_valley,
                    )
            error_log.error(price_ids_by_pid)
            for p in price_ids_by_pid:
                error_log.error(p)
                models.UnitPrice.objects.filter(id=p).delete()

        # 修改站点
        if stations:
            station_ids_by_pid = models.StationDetails.objects.filter(project=project_ins, is_delete=0).values("id")
            station_ids_by_pid = [i['id'] for i in station_ids_by_pid]
            stations = json.loads(stations)
            for station in stations:
                s_id = station.get('id')
                if s_id:
                    error_log.error(s_id)
                    station_ins = models.StationDetails.objects.get(id=s_id)
                    station_ins.db = project_english_name
                    station_ins.app = station["app"]
                    station_ins.station_name = station["station_name"]
                    station_ins.english_name = station["english_name"]
                    station_ins.meter = station["meter"]
                    station_ins.specifications = station["meter"]
                    station_ins.rated_power = station["meter"] * 100
                    station_ins.rated_capacity = str(station["meter"] * 232.96)
                    station_ins.pcs_number = station["meter"]
                    station_ins.unit_number = station["meter"]
                    station_ins.fire_fighting = station["meter"]
                    station_ins.cabinet = station["meter"]
                    station_ins.battery_cluster = station["meter"]
                    station_ins.address = address if address else '未填写'
                    station_ins.emq_user = station["emq_user"]
                    station_ins.emq_pwd = station["emq_pwd"]
                    station_ins.emq_clid = station["emq_clid"]
                    station_ins.ini_num = station["ini_num"]
                    station_ins.int_num = station["int_num"]
                    station_ins.meter_position = station['meter_position']
                    station_ins.transformer_capacity = str(station['transformer_capacity'])
                    station_ins.type = _type
                    station_ins.level = level
                    station_ins.province_id = province
                    station_ins.save()
                    station_ids_by_pid.remove(s_id)
                else:
                    master_station = models.MaterStation.objects.create(
                        project=project_ins,
                        name=station['station_name'],
                        english_name=station["english_name"],
                    )
                    user = models.UserDetails.objects.get(id=request.user['user_id'])
                    user.master_stations.add(master_station)

                    station_ins = models.StationDetails.objects.create(
                        project=project_ins,
                        db=project_english_name,
                        app=station["app"],
                        station_name=station["station_name"],
                        english_name=station["english_name"],
                        meter=station["meter"],
                        specifications=station["meter"],
                        rated_power=station["meter"] * 100,
                        rated_capacity=str(station["meter"] * 232.96),
                        pcs_number=station["meter"],
                        unit_number=station["meter"],
                        fire_fighting=station["meter"],
                        cabinet=station["meter"],
                        battery_cluster=station["meter"],
                        address=address if address else '未填写',
                        emq_user=station["emq_user"],
                        emq_pwd=station["emq_pwd"],
                        emq_clid=station["emq_clid"],
                        ini_num=station["ini_num"],
                        int_num=station["int_num"],
                        meter_position=station['meter_position'],
                        transformer_capacity=str(station['transformer_capacity']),
                        type=_type,
                        level=level,
                        province_id=province,
                        meter_type=2,
                        meter_count=1,
                        master_station=master_station
                    )
                    for i in range(station["meter"]):
                        if station["meter"] == 1:
                            BMS = f"BMS"
                            PCS = f"PCS"
                        else:
                            BMS = f"BMS{i + 1}"
                            PCS = f"PCS{i + 1}"
                        count = models.Unit.objects.filter(is_delete=0).all().count()
                        models.Unit.objects.create(
                            unit_name=f"储能单元 {i + 1}",
                            english_name=f"PCS{count + 1}",
                            station=station_ins,
                            bms=BMS,
                            pcs=PCS,
                        )

            for s_id in station_ids_by_pid:
                units = models.Unit.objects.filter(is_delete=0, station=s_id).all()
                if units.exists():
                    for u in units:
                        u.is_delete = 1
                        u.save()

                stations = models.StationDetails.objects.filter(id=s_id).all()
                if stations.exists():
                    for s in stations:
                        s.is_delete = 1
                        s.save()
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {"message": "success", "detail": "修改成功！"},
        })


class ProjectsAddNewViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}
        self.serializer_context['user_id'] = request.user["user_id"]

    @transaction.atomic
    def post(self, request):
        """
        新增项目
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        # context = {"user_id": request.user["user_id"]}
        ser = manage_serializers.ProjectsAddNewSerializer(data=remove_empty_string_keys(request.data),
                                                          context=self.serializer_context)

        try:
            if not ser.is_valid():
                error_log.error("新增项目参数校验失败：{}".format(ser.errors))
                detail = []

                res = abnormal_handle(ser.errors, detail)
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f'参数校验失败: {res}' if lang == 'zh' else
                                 'Parameter verification failed.'},
                    }
                )
        except Exception as e:

            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args},
                }
            )
        save_id = transaction.savepoint()
        try:
            ser.save()
            transaction.savepoint_commit(save_id)
        except Exception as e:
            error_log.error(f'请求参数：{request.data}')
            error_log.error("新增项目失败：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "新增失败" if lang == 'zh' else f'Add failed: {e}'},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "新增成功！" if lang == 'zh' else 'Add success.'},
            }
        )

    @transaction.atomic
    def put(self, request):
        """
        编辑项目
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        # context = {"user_id": request.user["user_id"]}
        ser = manage_serializers.ProjectsUpdateNewSerializer(data=remove_empty_string_keys(request.data), context=self.serializer_context)

        try:
            if not ser.is_valid():
                error_log.error("编辑项目参数校验失败：{}".format(ser.errors))
                detail = []
                res = abnormal_handle(ser.errors, detail)
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": f'参数校验失败: {res}' if lang == 'zh' else
                                 'Parameter verification failed.'},
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        save_id = transaction.savepoint()
        try:
            project = models.Project.objects.get(id=ser.validated_data['id'])
            ser.update(project=project, validated_data=ser.validated_data)
            transaction.savepoint_commit(save_id)
        except Exception as e:
            error_log.error(f'请求参数：{request.data}')
            error_log.error("编辑项目失败：{}".format(e))
            try:
                transaction.savepoint_rollback(save_id)
            except Exception as e:
                error_log.error(f'回滚失败：{e}')
                print(837, traceback.print_exc())
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": f"编辑失败: {e}" if lang == 'zh' else 'Update failed.'},
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "编辑成功！" if lang == 'zh' else 'Update success.'},
            }
        )

    def get(self, request, id):
        """
        项目详情
        :param request:
        :param id: 项目ID
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        try:
            project = models.Project.objects.get(id=id)
        except Exception as e:
            error_log.error("查询项目不存在：{}".format(e))
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": "查询项目ID不存在" if lang == 'zh' else
                             'Query project ID does not exist.'},
                }
            )
        data = {
            'id': project.id,
            'project_name': project.name if lang == 'zh' else project.en_name,
            'project_english_name': project.english_name,
            'address': project.address if lang == 'zh' else project.en_address,
            'organization_id': project.organization_id,
            'manager': project.manager if lang == 'zh' else project.en_manager,
            'manager_phone': project.manager_phone,
            'province_id': project.province_id,
            'application_scenario': SCENE_FLAG.get(project.application_scenario)[lang],
            '_type': project.type,
            'level': project.level,
            'price_type': project.price_type,
            'city': project.city if lang == 'zh' else project.en_city,
            'counties': project.counties.name if project.counties else '',
            'connect_time': project.create_time,
            'compant_name': project.compant_name if lang == 'zh' else project.en_compant_name,
            'compant_code': project.compant_code,
            'contact_person': project.contact_person,
            'contact_person_phone': project.contact_person_phone,
            'industry': project.industry.id if project.industry else '',
            'images': json.loads(project.images) if project.images else '',
            'station_info': [],
            'project_type': project.project_type,
            'gf_cap': project.gf_cap,
            'demand_cumput': project.demand_cumput,
            'demand_cap': project.demand_cap,
            'debug_user': project.debug_user if lang == 'zh' else project.en_debug_user,
            'afftet_sale_user': project.afftet_sale_user if lang == 'zh' else project.en_afftet_sale_user,
        }
        # 查询项目站信息
        master_stations = models.MaterStation.objects.filter(project=project, is_delete=0).all()
        for master in master_stations:
            slave_stations = models.StationDetails.objects.filter(master_station=master, is_delete=0).all()
            station_list = []
            for station in slave_stations:
                ini_num = station.ini_num.split(';') if station.ini_num else []
                units = models.Unit.objects.filter(is_delete=0, station=station).all()
                unit_list = []
                for i, unit in enumerate(units):
                    try:
                        english_name = ini_num[i]
                    except IndexError as e:
                        english_name = ''
                    unit_dict = {
                        'name': unit.unit_new_name if lang == 'zh' else unit.en_unit_new_name,
                        'english_name': english_name,
                        'versions': str(unit.v_number) + '.0' if unit.v_number else ''
                    }
                    unit_list.append(unit_dict)

                # 查询结算电表使用时间范围
                meter_user_time = models.StationMeterUseTime.objects.filter(station=station).first()

                station_dict = {
                    'id': station.id,
                    'name': station.station_name if lang == 'zh' else station.en_station_name,
                    'english_name': station.english_name,
                    'int_num': station.int_num,
                    'app': station.app,
                    'emq_user': station.emq_user,
                    'emq_pwd': station.emq_pwd,
                    'emq_clid': station.emq_clid,
                    # 'model': len(units),
                    'is_account': 1 if station.is_account else 0,
                    # 'meter_number': station.meter_number if station.meter_number else '',
                    # 'account_start': meter_user_time.start_time.strftime('%Y-%m-%d %H:%M:%S') if meter_user_time else '',
                    # 'account_end': meter_user_time.end_time.strftime('%Y-%m-%d %H:%M:%S') if meter_user_time and meter_user_time.end_time else '',
                    'unit_info': unit_list
                }

                if len(units):
                    station_dict['model'] = len(units)

                if station.is_account:
                    station_dict['account_start'] = meter_user_time.start_time.strftime('%Y-%m-%d %H:%M:%S')
                    if meter_user_time.end_time:
                        station_dict['account_end'] = meter_user_time.end_time.strftime('%Y-%m-%d %H:%M:%S')
                    if station.meter_number:
                        station_dict['meter_number'] = station.meter_number

                station_list.append(station_dict)
            master_station_dict = {
                'id': master.id,
                'name': master.name if lang == 'zh' else master.en_name,
                'english_name': master.english_name,
                'meter_position': master.stationdetails_set.filter(is_delete=0).first().meter_position,
                'transformer_capacity': master.stationdetails_set.filter(is_delete=0).first().transformer_capacity,
                # 配置模式 (1, "常规模式"), (2, "标准主从模式") (2, "级联主从模式")，(4, "逻辑主从模式")
                'mode': master.mode,
                'equipment_info': station_list,
                'up_vol': master.up_vol,
                'low_vol': master.low_vol,
            }
            data['station_info'].append(master_station_dict)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
            }
        )


class ProjectsDelNewViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        """
        删除主从站信息
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        # 1：主站；2：从站
        t = request.data.get('type')
        id = request.data.get('id')
        project_id = request.data.get('project_id')
        if not t or not id or not project_id:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": "参数不完整" if lang == 'zh' else 'Parameter is incomplete.'},
                }
            )
        save_id = transaction.savepoint()
        try:
            project = models.Project.objects.get(id=project_id)
            unit_count = 0
            if t == '1':
                master = project.materstation_set.filter(id=id, is_delete=0).first()
                slave_stations = master.stationdetails_set.filter(is_delete=0).all()
                for i in slave_stations:
                    units = i.unit_set.filter(is_delete=0).all()
                    unit_count += units.count()
                    for u in units:
                        u.is_delete = 1
                        u.save()
                if slave_stations.exists():
                    for s in slave_stations:
                        s.is_delete = 1
                        s.save()
                master.is_delete = 1
                master.save()
            else:
                slave_stations = project.stationdetails_set.filter(id=id, is_delete=0).first()
                units = slave_stations.unit_set.filter(is_delete=0).all()
                unit_count += units.count()
                for u in units:
                    u.is_delete = 1
                    u.save()
                for s in slave_stations:
                    s.is_delete = 1
                    s.save()
            project.rated_power = float(project.rated_power) - unit_count * 100
            project.rated_capacity = round(float(project.rated_capacity) - unit_count * 232.96, 2)
            project.station_number = int(project.station_number) - unit_count
            project.save()
            transaction.savepoint_commit(save_id)
        except Exception as e:
            error_log.error(request.data)
            error_log.error("查询并网点信息不存在：{}".format(e))
            transaction.savepoint_rollback(save_id)
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": "并网点ID不存在" if lang == 'zh' else 'The station ID does not exist.'},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": f"项目：{project.name}的并网点信息删除成功" if lang == 'zh' else
                         'The station information of the project was deleted successfully'},
            }
        )


class ProjectsRegionViews(APIView):
    """
    省市区查询
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        res = []
        id = request.query_params.get('id')
        if id:
            province = models.ElectricityProvince.objects.filter(id=id).all()
        else:
            province = models.ElectricityProvince.objects.all()
        for p in province:
            data = {}
            data['id'] = p.id
            data['name'] = p.name if lang == 'zh' else p.en_name
            data['code'] = p.code
            data['childs'] = []
            for c in p.city_set.filter(is_use=1).all():
                citys = {}
                citys['id'] = c.id
                citys['name'] = c.name if lang == 'zh' else c.en_name
                citys['code'] = c.code
                citys['childs'] = []
                for co in c.counties_set.filter(is_use=1).all():
                    counties = {}
                    counties['id'] = co.id
                    counties['name'] = co.name if lang == 'zh' else co.en_name
                    counties['code'] = co.code
                    citys['childs'].append(counties)
                data['childs'].append(citys)
            res.append(data)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": res},
            }
        )


class ProjectsMappingViews(APIView):
    """
    企业、企业行业字典
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')
        if id:
            industry = models.Industry.objects.filter(province_id=id, is_use=1).all()
            price_type = models.PriceType.objects.filter(province_id=id, is_use=1).all()
        else:
            industry = models.Industry.objects.filter(is_use=1).all()
            price_type = models.PriceType.objects.filter(is_use=1).all()
        data = {
            'industry': [],  # 行业
            'price_type': []  # 电价类别
        }

        for i in industry:
            i_dict = {
                'id': i.id,
                'name': i.name if lang == 'zh' else i.en_name,
                'code': i.code
            }
            data['industry'].append(i_dict)

        for p in price_type:
            p_dict = {
                'id': p.id,
                'name': p.name if lang == 'zh' else p.en_name,
                'code': p.code
            }
            data['price_type'].append(p_dict)

        data['scene_flag'] = SCENE_FLAG  # 应用场景
        data['project_type'] = PROJECT_TYPE  # 项目类型
        data['demand_cumput'] = DEMAND_CUMPUT  # 计费方式
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
            }
        )


class ProjectsFirmListViews(APIView):
    """
    企业列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        res = models.SideRegisterAccount.objects.filter(is_register=0).values('compant_name', 'en_compant_name').distinct()
        data = []
        for i in res:
            if lang == 'zh':
                if i.get('compant_name'):
                    data.append(i.get('compant_name'))
            else:
                if i.get('en_compant_name'):
                    data.append(i.get('en_compant_name'))
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
            }
        )


class ProjectsUploadViews(APIView):
    """
    多个附件上传接口
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        files = request.FILES.getlist('files')
        # 上传Minio
        minio_client = MinioTool()
        url_list = {}  # 附件上传地址
        for file in files:
            url = minio_client.upload_object(file, bucket_name='tianlu')
            url_list[file.name] = url

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": url_list},
            }
        )


class DemandSideRespondViews(APIView):
    """
    需求侧响应查询列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        province_id = request.query_params.get('province_id', 11)  # 默认江苏省
        station_name = request.query_params.get('station_name')  # 并网点名称
        compant_name = request.query_params.get(('compant_name'))  # 公司名称
        region = request.query_params.get('region')  # 地区
        industry_id = request.query_params.get('industry_id')  # 行业ID
        is_register = request.query_params.get('is_register') # 是否注册 1：注册
        page = request.query_params.get('page', 1)
        size = request.query_params.get('size', 10)

        side_register_account = models.SideRegisterAccount.objects.filter(province_id=province_id, is_use=1)
        if station_name:
            side_register_account = side_register_account.filter(Q(station__name__contains=station_name) | Q(station__en_name__contains=station_name))
        if compant_name:
            side_register_account = side_register_account.filter(Q(compant_name__contains=compant_name) | Q(en_compant_name__contains=compant_name))
        if region:
            side_register_account = side_register_account.filter(Q(city_counties__contains=region) | Q(en_city_counties__contains=region))
        if industry_id:
            side_register_account = side_register_account.filter(industry_id=industry_id)
        if is_register:
            side_register_account = side_register_account.filter(is_register=is_register)
        side_register_account = side_register_account.order_by("create_time").all()

        # 分页器
        page_res = paging(page, size, side_register_account)
        side_register_res = page_res.get('data')
        data = []
        for i in side_register_res:
            if lang == 'zh':
                data.append({
                    'id': i.id,
                    'project_name': i.project.name,
                    'station_name': i.station.name,
                    'station_code': i.station_code,
                    'compant_name': i.compant_name,
                    'compant_code': i.compant_code,
                    'price_type': i.price_type.name,
                    'city_counties': i.city_counties,
                    'industry_name': i.industry.name if i.industry else '',
                    'industry_id': i.industry.id if i.industry else None,
                    'compact_cap': i.compact_cap,
                    'level_name': i.level_name,
                    'run_cap': i.run_cap,
                    'address': i.address,
                    'user_code': i.user_code,
                    'connect_time': i.connect_time.strftime("%Y-%m-%d %H:%M:%S") if i.connect_time else '',
                    'is_register': i.is_register,
                    'city_id': i.city_id,
                    'counties_id': i.counties_id
                })
            else:
                data.append({
                    'id': i.id,
                    'project_name': i.project.name,
                    'station_name': i.station.name,
                    'station_code': i.station_code,
                    'compant_name': i.en_compant_name,
                    'compant_code': i.compant_code,
                    'price_type': i.price_type.en_name,
                    'city_counties': i.en_city_counties,
                    'industry_name': i.industry.en_name if i.industry else '',
                    'industry_id': i.industry.id if i.industry else None,
                    'compact_cap': i.compact_cap,
                    'level_name': i.level_name,
                    'run_cap': i.run_cap,
                    'address': i.en_address,
                    'user_code': i.user_code,
                    'connect_time': i.connect_time.strftime("%Y-%m-%d %H:%M:%S") if i.connect_time else '',
                    'is_register': i.is_register,
                    'city_id': i.city_id,
                    'counties_id': i.counties_id
                })
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )

    def post(self, request):
        """编辑"""
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')
        compant_name = request.data.get('compant_name')
        compant_code = request.data.get('compant_code')
        price_type = request.data.get('price_type')
        city_id = request.data.get('city_id')
        counties_id = request.data.get('counties_id')
        industry_id = request.data.get('industry_id')
        compact_cap = request.data.get('compact_cap')
        level_name = request.data.get('level_name')
        run_cap = request.data.get('run_cap')
        address = request.data.get('address')
        user_code = request.data.get('user_code')
        station_code = request.data.get('station_code')
        if not id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "ID不允许为空" if lang == 'zh' else 'ID is not allowed to be empty.'}
            })
        if not all([compant_name, compant_code, price_type, city_id, counties_id, industry_id, compact_cap, level_name,
                    run_cap, address, station_code]):
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数输入不完整，请检查参数" if not lang == 'zh' else
                         'Incomplete parameter input, please check the parameter.'}
            })
        if user_code:
            if len(user_code) > 20:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数校验失败，用户编号限制长度20" if lang == 'zh' else
                             'Parameter verification failed, user number limit length 20.'}
                })
            pattern = re.compile(r'^[a-zA-Z0-9]+$')
            if not pattern.match(user_code):
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数校验失败，用户编号只支持数字和字母" if lang == 'zh' else
                             'Parameter verification failed, user number only supports numbers and letters.'}
                })
        try:
            side_register_account = models.SideRegisterAccount.objects.get(id=id)
        except models.SideRegisterAccount.DoesNotExist as e:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "ID不存在" if lang == 'zh' else 'ID does not exist.'}
            })
        side_register_account.compant_code = compant_code
        side_register_account.compant_name = compant_name
        side_register_account.price_type_id = price_type
        side_register_account.city_id = city_id
        if counties_id:
            side_register_account.counties_id = counties_id
        side_register_account.city_counties = side_register_account.city.name + side_register_account.counties.name
        side_register_account.industry_id = industry_id
        side_register_account.compact_cap = compact_cap
        side_register_account.level_name = level_name
        side_register_account.run_cap = run_cap
        side_register_account.address = address
        side_register_account.user_code = user_code
        side_register_account.station_code = station_code
        side_register_account.save()

        # 异步翻译
        pdr_data = {'id': side_register_account.id,
                    'table': 't_side_register_account',
                    'update_data': {'compant_name': compant_name, 'city_counties': side_register_account.city_counties,
                                    'address': address}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": '编辑成功！' if lang == 'zh' else 'Update successfully!'},
            }
        )


class DemandSideRegisterViews(APIView):
    """
    需求侧响应注册/解除注册
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        types = int(request.data.get('types', 1)) # 1：注册；0：解除
        ids = request.data.get('ids')  # 数据ID
        ids = ids.split(',')
        if not ids:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "ID不允许为空" if lang == 'zh' else 'ID is not allowed to be empty.'}
            })

        if len(ids) == 1:
            try:
                side_register_account_res = models.SideRegisterAccount.objects.get(id=int(ids[0]))
            except models.SideRegisterAccount.DoesNotExist as e:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "ID不存在" if lang == 'zh' else 'ID does not exist.'}
                })
            if not side_register_account_res.user_code:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "请录入“用户编号”后再完成注册！" if lang == 'zh' else
                             'Please enter "User Number" before completing the registration!'}
                })
            if types == 1:
                side_register_account_res.is_register = 1
                side_register_account_res.connect_time = datetime.datetime.now()
                side_register_account_res.save()
                with connections['virtual_power_plant'].cursor() as ads_cursor:
                    max_tid_sql = """SELECT
                                            max( tid ) 
                                        FROM
                                            t_stations"""
                    try:
                        # 获取查询结果
                        ads_cursor.execute(max_tid_sql)
                        max_tid_res = ads_cursor.fetchall()
                    except Exception as e:
                        error_log.error(e)
                        return Response(
                            {
                                "code": common_response_code.SUMMARY_CODE,
                                "data": {
                                    "message": "error",
                                    "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                                }
                            }
                        )

                    max_tid = int(max_tid_res[0][0]) + 1
                    is_master = models.StationDetails.objects.filter(Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0), master_station_id=side_register_account_res.station_id, is_delete=0).first()
                    if is_master:
                        if is_master.slave == 0 or is_master.pack == 0:
                            p_station = is_master.english_name[:-4]
                            is_slave = 0
                        else:
                            p_station = is_master.english_name
                            is_slave = -1
                        inster_sql = f"""INSERT INTO `virtual_power_plant`.`t_stations` ( `station`, `is_slave`, `is_pack`, `is_delete`, `p_station`, `ots`, `cons_id`, `tid`, `meter_position` )
                                        VALUES
                                            ( '{is_master.english_name}', {is_slave}, -1, 0, '{p_station}', '{side_register_account_res.connect_time.strftime("%Y-%m-%d")}', '{side_register_account_res.user_code}', {max_tid}, 1);"""
                        try:
                            # 获取查询结果
                            ads_cursor.execute(inster_sql)
                        except Exception as e:
                            error_log.error(e)
                            return Response(
                                {
                                    "code": common_response_code.SUMMARY_CODE,
                                    "data": {
                                        "message": "error",
                                        "detail": '执行失败！' if lang == 'zh' else 'Execution failed!',
                                    }
                                }
                            )
                    else:
                        return Response({
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {"message": "error", "detail": "未查询到关联主站！" if lang == 'zh' else 'Not find the master station!'}
                        })
            else:
                if (datetime.datetime.now() - side_register_account_res.connect_time).days < 8:
                    return Response({
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "该并网点接入未满8天，无法解除！" if lang == 'zh' else
                                 'The and net station has not been connected for more than 8 days,'
                                 ' and cannot be released!'}
                    })
                side_register_account_res.is_register = 0
                side_register_account_res.connect_time = None
                side_register_account_res.save()
                with connections['virtual_power_plant'].cursor() as ads_cursor:
                    is_master = models.StationDetails.objects.filter(Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) |
                                                                     Q(slave=-1, pack=0),
                                                                     master_station_id=side_register_account_res.station_id, is_delete=0).first()

                    update_sql = f"""UPDATE `virtual_power_plant`.`t_stations` 
                                        SET `is_delete` = 1 
                                        WHERE
                                            `station` = '{is_master.english_name}' AND `is_delete` = 0;"""
                    try:
                        # 获取查询结果
                        ads_cursor.execute(update_sql)
                    except Exception as e:
                        error_log.error(e)
                        return Response(
                            {
                                "code": common_response_code.SUMMARY_CODE,
                                "data": {
                                    "message": "error",
                                    "detail": '执行失败！' if lang == 'zh' else 'Execution failed!',
                                }
                            }
                        )
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {"message": "success", "detail": '解除成功！' if lang == 'zh' else 'Release successful!'}
                    }
                )
        else:
            side_register_account_res = models.SideRegisterAccount.objects.filter(id__in=ids).filter(
                ~Q(user_code=None)).all()
            update_list = []
            for i in side_register_account_res:
                i.is_register = 1
                i.connect_time = datetime.datetime.now()
                update_list.append(i)
            models.SideRegisterAccount.objects.bulk_update(update_list, ['is_register', 'connect_time'])
            with connections['virtual_power_plant'].cursor() as ads_cursor:
                max_tid_sql = """SELECT
                                        max( tid ) 
                                    FROM
                                        t_stations"""
                try:
                    # 获取查询结果
                    ads_cursor.execute(max_tid_sql)
                    max_tid_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed!',
                            }
                        }
                    )
                max_tid = int(max_tid_res[0][0])
                for _id in ids:
                    side = models.SideRegisterAccount.objects.get(id=_id)
                    if side.connect_time:
                        max_tid += 1
                        is_master = models.StationDetails.objects.filter(
                            Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0),
                            master_station_id=side.station_id, is_delete=0).first()
                        if is_master.slave == 0 or is_master.pack == 0:
                            p_station = is_master.english_name[:-4]
                            is_slave = 0
                        else:
                            p_station = is_master.english_name
                            is_slave = -1
                        inster_sql = f"""INSERT INTO `virtual_power_plant`.`t_stations` ( `station`, `is_slave`, `is_pack`, `is_delete`, `p_station`, `ots`, `cons_id`, `tid`, `meter_position` )
                                        VALUES
                                            ( '{is_master.english_name}', {is_slave}, -1, 0, '{p_station}', '{side.connect_time.strftime("%Y-%m-%d")}', {side.user_code}, {max_tid}, 1);"""
                        try:
                            # 获取查询结果
                            ads_cursor.execute(inster_sql)
                        except Exception as e:
                            error_log.error(e)
                            return Response(
                                {
                                    "code": common_response_code.SUMMARY_CODE,
                                    "data": {
                                        "message": "error",
                                        "detail": '执行失败！' if lang == 'zh' else 'Execution failed!',
                                    }
                                }
                            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": '注册成功！' if lang == 'zh' else 'Registration successful!'}
            }
        )


class DemandSideBatchEntryViews(APIView):
    """
    用户编号批量录入
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        name = request.data.get('name')  # 企业
        user_code = request.data.get('user_code')
        if not name or not user_code:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数校验失败，请检查参数！" if lang == 'zh' else
                         'Parameter verification failed, please check the parameters!'}
            })

        if len(user_code) > 20:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数校验失败，用户编号限制长度20" if lang == 'zh' else
                         'Parameter verification failed, the user number is limited to 20 characters.'}
            })
        pattern = re.compile(r'^[a-zA-Z0-9]+$')
        if not pattern.match(user_code):
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数校验失败，用户编号只支持数字和字母" if lang == 'zh' else
                         'Parameter verification failed. User ID only supports numbers and letters.'}
            })

        side = models.SideRegisterAccount.objects.filter(compant_name=name, is_register=0).update(user_code=user_code)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": '批量录入成功！' if lang == 'zh' else 'Batch entry successful!'}
            }
        )


class DemandSideDownloadViews(APIView):
    """
    批量下载
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ids = request.data.get('ids')  # 数据ID
        ids = ids.split(',')
        if not ids:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "ID不允许为空" if lang == 'zh' else 'ID is not allowed to be empty.'}
            })
        file_path_list = []
        file_path2_list = []
        side_res = models.SideRegisterAccount.objects.filter(id__in=ids).order_by('project_id').all()
        # 一个项目单独下载监测点信息和企业信息附件
        _side_res = {}
        for i in side_res:
            if _side_res.get(i.project.name):
                _side_res[i.project.name].append(i)
            else:
                _side_res[i.project.name] = [i]

        for k, v in _side_res.items():
            row_dict = {}  # 相同企业名称聚合
            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            title = ['CONS_ID', 'TID', 'P_TID', 'SEEPNAME(监测点名称)']
            for t in range(1, len(title) + 1):
                st.cell(row=1, column=t).value = title[t - 1]
            wb2 = Workbook()
            wb2.encoding = 'utf-8'  # 定义编码格式
            st2 = wb2.active  # 获取第一个工作表（sheet1）
            title2 = ['CONS_NAME（企业名称）', 'CONS_NO（企业户号）', 'PTYPE（电价类别编码）', 'STRATIVE_ID（地区编码）',
                      'TRADE_CODE（行业类型编码）', 'CONTRACT_CAP（合同容量，KVA）',
                      'VOLT_COOE（电压等级：KV）', 'RUN_CAP（运行容量，KVA）', 'ELEC_ADDR（用电地址）', '登录账号', '登录密码']
            for t in range(1, len(title2) + 1):
                st2.cell(row=1, column=t).value = title2[t - 1]

            for i in v:
                max_row = st.max_row + 1
                st.cell(row=max_row,
                        column=2).value = '1' + f"00{max_row - 1}" if max_row < 10 else '1' + f'0{max_row - 1}'
                st.cell(row=max_row, column=3).value = -1
                st.cell(row=max_row, column=4).value = i.station_code
                if row_dict.get(i.compant_name):
                    _max_row = row_dict.get(i.compant_name)
                    st2.cell(row=_max_row, column=6).value += i.compact_cap
                    st2.cell(row=_max_row, column=8).value += i.run_cap
                else:
                    _max_row = st2.max_row + 1
                    row_dict[i.compant_name] = _max_row
                    st2.cell(row=_max_row, column=1).value = i.compant_name if lang == 'zh' else i.en_compant_name
                    st2.cell(row=_max_row, column=2).value = i.compant_code
                    st2.cell(row=_max_row, column=3).value = i.price_type.code
                    st2.cell(row=_max_row, column=4).value = i.counties.code if i.counties else i.city.code
                    st2.cell(row=_max_row, column=5).value = i.industry.code if i.industry else ''
                    st2.cell(row=_max_row, column=6).value = i.compact_cap
                    st2.cell(row=_max_row, column=7).value = i.level_name
                    st2.cell(row=_max_row, column=8).value = i.run_cap
                    st2.cell(row=_max_row, column=9).value = i.address if lang == 'zh' else i.en_address

            if lang == 'zh':
                file_name = f"{i.project.name}-监测点信息--{int(time.time() * 1000)}.xls"
                file_name2 = f"{i.project.name}-企业注册信息--{int(time.time() * 1000)}.xls"
            else:
                file_name = f"{i.project.en_name}-Monitoring Point Information--{int(time.time() * 1000)}.xls"
                file_name2 = f"{i.project.en_name}-Enterprise Registration Information--{int(time.time() * 1000)}.xls"

            file_path = os.path.join(settings.STATICFILES_DIRS[0], file_name)
            file_path2 = os.path.join(settings.STATICFILES_DIRS[0], file_name2)
            wb.save(file_path)
            wb2.save(file_path2)
            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')
            file_path = minio_client.upload_local_file(file_name, file_path, 'download')
            file_path2 = minio_client.upload_local_file(file_name2, file_path2, 'download')
            file_path_list.append(file_path)
            file_path2_list.append(file_path2)
        data = {'监测点信息': file_path_list, '企业注册信息': file_path2_list}
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )

class UserInfo(APIView):
    """
    项目客户联系人
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        _id = request.query_params.get('organization_id')
        user_res = models.UserDetails.objects.filter(tissue_id=_id, is_used=1).all()
        data = []
        for user in user_res:
            data.append({
                'id': user.id,
                'name': user.user_name if lang == 'zh' else user.en_user_name,
                'mobile': user.mobile
            })
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )