#! bin/bash
a=`ps -fe|grep realdata_save_redis |grep -v grep|grep -v "/bin/bash -c"|wc -l`

if [ $a -eq 0 ] 
then
python /home/<USER>/dm2016/bin/RHBESS_Service/Tools/TimeTask/realdata_save_redis.py &
else
echo "runing"
fi

b=`ps -fe|grep timing_task |grep -v grep|wc -l`
if [ $b -eq 0 ] 
then
nohup python /home/<USER>/dm2016/bin/RHBESS_Service/Tools/TimeTask/timing_task.py --sta --station baodian --name tfStbodian5 >/dev/null 2>&1 &
nohup python /home/<USER>/dm2016/bin/RHBESS_Service/Tools/TimeTask/timing_task.py --sta --station houma --name tfSthoumaB2 >/dev/null 2>&1 &
else
echo "runing"
fi
