#!/usr/bin/env python
# coding=utf-8
# @Information:  报表
import ast
import math
from datetime import datetime, timedelta
from sqlalchemy import func
import tornado.web
from Application.HistoryData.his_bams import _select_get_des_value, _return_db_con
from Application.Models.User.availability_t import Availability
from Application.Models.User.event import Event
from Application.Models.User.operation_report_t import ReportOperation
from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
from Application.Models.User.station import Station
from Application.Models.User.station_relation import StationR
from Application.Models.base_handler import BaseHandler
from Application.Models.His.r_ACDMS import HisACDMS, HisDM
from Application.Static.value_desc import CoFa_obj
from Tools.Cfg.DB_his import get_dhis
from Tools.DB.mysql_his import dongmu_session
from Tools.Utils.time_utils import timeUtils
from Tools.DB.mysql_user import user_session
from Tools.Utils.num_utils import *
from Application.HistoryData.his_data_alluse import db_, <PERSON><PERSON><PERSON><PERSON>, shou<PERSON>, ch<PERSON><PERSON>, _tableIsExist
from Application.Models.User.report import Report
from Application.Models.User.report_f import FReport
from Application.Models.User.alarm_r import AlarmR
import pymysql
from Application.Cfg.dir_cfg import model_config
from dbutils.persistent_db import PersistentDB
from typing import List, Dict, Any

# 连接数据库
pool = PersistentDB(pymysql, 10,**{
            "host": model_config.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })
dongmu_num = 7
# 查询配置文件
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
dis_cha_name = ast.literal_eval(model_config.get('peizhi', 'dis_cha_name'))  # 充放电量名称
db_his, baodian_his = get_dhis('report')

# 关口表名称配置
meter_name = [{"ygzhen": "tfStygzhen1.EMS.MET."}, {"zgtian": "tfStzgtian1.EMS.MET."}, {"guizhou": "guizhou.EMS.MET."}, {"ygqn": "ygqn.EMS.MET."},{"shgyu": "shgyu.EMS.MET."}]
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']

class ReportFormInterface(BaseHandler):
    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        db_con = self.get_argument('db', 'his')
        db = db_con
        logging.warn('report post---db_con:%s' % db_con)
        if db_con not in db_.keys():
            return self.customError("DB 参数错误")
        if db in exclude_station:
            db_conn = db_[db_con]
            db_con = db_[db_con][1]
        else:
            db_conn = db_[db_con]
            db_con = db_[db_con][0]
        # 定义表头
        keys = ['type_', 'name', 'descr', 'unit', 'max', 'min', 'cost']
        columns = ['分类', '名称', '描述', '单位', '最大值', '最小值', '费用']
        en_columns = ['Sort', 'Name', 'Description', 'Unit', 'Maximum value', 'Minimum value', 'Expense']
        try:
        # if 1:
            if kt == 'ReportDay':  # 搜索运行日报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime())[:10]  # 默认7天前时间
                # 输入日期
                if DEBUG:
                    logging.info('endTime:%s,startTime:%s' % (endTime, startTime))
                Meter = []
                if db in exclude_station:
                    if db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                        disgCapy = dis_cha_name[db]['disgCapy'][0]
                        if db == 'ygzhen' or db == 'zgtian' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                            Meter = dis_cha_name[db]['Meter']
                    else:
                        disgCapy = dis_cha_name[db]['disgCapy']
                else:
                    disgCapy = ''
                    pass
                days = timeUtils.dateToDataList(startTime, endTime)
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                if timeUtils.timeStrToTamp('%s %s' % (endTime, '00:00:00')) > timeUtils.timeStrToTamp(
                        '%s %s' % (now, '00:00:00')):
                    endTime = now
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                power = user_session.query(StationR.electric_power).filter(
                    StationR.station_name == db).first()  # 获取电站额定功率
                data = {}
                tal = []
                PCS = []
                chag_end, disg_end = 0, 0
                # 原逻辑
                # for date in days[::-1]:
                #     self.disg_chag_arr(PCS, date, days, db, Meter, disgCapy, power, tal, volume, 3, lang)
                #     indx = days.index(date)
                #     if indx == len(days) - 1:  # 取截至到最后时间的累计充放电
                #         chag_end, disg_end = self.tal_disg_chag(db, disgCapy, endTime, now)
                # self.disg_chag_tatio(chag_end, disg_end, tal)

                if db in exclude_station and db == "dongmu":
                    for date in days[::-1]:
                        self.disg_chag_arr(PCS, date, days, db, Meter, disgCapy, power, tal, volume, 3, lang)
                        indx = days.index(date)
                        if indx == len(days) - 1:  # 取截至到最后时间的累计充放电
                            chag_end, disg_end = self.tal_disg_chag(db, disgCapy, endTime, now)
                else:
                    self._disg_chag_arr(PCS, startTime, endTime, days, db, Meter, disgCapy, power, tal, volume, 3, lang)
                    chag_end, disg_end = self._tal_disg_chag(db, disgCapy, endTime, now)
                self.disg_chag_tatio(chag_end, disg_end, tal)
                data['tal'] = tal
                data['PCS'] = PCS
                total = len(tal)
                return self.returnTotalSuc(data, total)
            elif kt == 'ReportPwOtInvt':  # 运行日报有功功率
                end_Time = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                # 输入日期
                if DEBUG:
                    logging.info('endTime:%s' % (end_Time))
                startTime = end_Time + ' 00:00:00'
                endTime = end_Time + ' 23:59:59'
                a = timeUtils.getBetweenMonth(startTime, endTime)
                tables = 'r_measure' + pd.Series(a)
                st = timeUtils.timeStrToTamp(startTime)  # 起始时间绝对秒
                ed = timeUtils.timeStrToTamp(endTime)  # 截止时间绝对秒
                obj = {}
                if db == 'dongmu':
                    self.data_day_dongmu(ed, obj, st, 'PCS_yougong_4', '15T', 1, obj_sys='系统', vmax=100000)
                else:
                    self.data_day(db,end_Time, obj)
                # 获取当天的起始时间，即00:00:00
                if isinstance(end_Time,str):
                    end_Time = datetime.strptime(end_Time,"%Y-%m-%d")
                start_time = end_Time.replace(hour=0, minute=0, second=0, microsecond=0)
                time_list = []
                # 循环生成当天每15分钟的时间
                while start_time.day == end_Time.day:
                    time_list.append(start_time)
                    start_time += timedelta(minutes=15)

                # 打印当天每15分钟的时刻表
                for time in time_list:
                    _time = time.strftime('%d %H:%M:%S')
                    if _time not in obj.get("time"):
                        obj.get("time").append(_time)
                        obj.get("value").append("--")
                return self.returnTypeSuc(obj)
            elif kt == 'ReportAlarm':  # 查看告警记录
                end_Time = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                # 输入日期
                if DEBUG:
                    logging.info('end_Time:%s' % (end_Time))
                startTime = end_Time + ' 00:00:00'
                endTime = end_Time + ' 23:59:59'
                filter = [AlarmR.station == db]
                if startTime:
                    filter.append(AlarmR.ts >= startTime)
                if endTime:
                    filter.append(AlarmR.ts <= endTime)
                a1 = [AlarmR.event_id == Event.id, Event.type == 2]
                filter = filter + a1
                stations = user_session.query(AlarmR).filter(*filter).order_by(AlarmR.ts.desc()).order_by(
                    AlarmR.event_id.asc()).all()
                all = {}
                three_alarm = 0
                two_alarm = 0
                first_alarm = 0
                three_alarm_y = 0
                two_alarm_y = 0
                first_alarm_y = 0
                if stations:
                    for ts in stations:
                        st = eval(str(ts))
                        if st['alarm_descr'] == '三级告警':
                            three_alarm += 1
                            if st['status'] == '1':
                                three_alarm_y += 1
                        if st['alarm_descr'] == '二级告警':
                            two_alarm += 1
                            if st['status'] == '1':
                                two_alarm_y += 1
                        if st['alarm_descr'] == '一级告警':
                            first_alarm += 1
                            if st['status'] == '1':
                                first_alarm_y += 1
                all['three_alarm'] = three_alarm
                all['two_alarm'] = two_alarm
                all['first_alarm'] = first_alarm
                all['three_alarm_y'] = three_alarm_y
                all['two_alarm_y'] = two_alarm_y
                all['first_alarm_y'] = first_alarm_y
                return self.returnTypeSuc(all)
            elif kt == 'ReportCdDs':  # 查看电池簇充放电一致性
                end_Time = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                # 输入日期
                if DEBUG:
                    logging.info('endTime:%s' % (end_Time))
                startTime = end_Time + ' 00:00:00'
                endTime = end_Time + ' 23:59:59'
                tal = []
                if db != 'dongmu':
                    name_f_list = model_config.get('peizhi', 'device_arr')
                    list1 = eval(name_f_list)[db][1:]
                    name_f_list1 = model_config.get('peizhi', 'piece_arr')
                    list2 = eval(name_f_list1)[db][1:]
                    name_f_list2 = model_config.get('peizhi', 'device_obj')
                    list3 = eval(name_f_list2)
                    b = []
                    for k in list2:
                        b.append(k['value'])
                    a = []
                    for i in list1:
                        a.append(i['value'])
                obj = []
                if db == 'dongmu':
                    chag_disg = user_session.query(FReportBmsDongmu.pcs_name, FReportBmsDongmu.cu_name,
                                                   FReportBmsDongmu.chag, FReportBmsDongmu.disg).filter(
                        FReportBmsDongmu.day.between(startTime, endTime)).order_by(FReportBmsDongmu.id.asc()).all()
                    self.cu_chag_disg(chag_disg, obj,db)
                else:
                    conn = pool.connection()
                    cursor = conn.cursor()

                    sql = """SELECT pcs_name,{},{},{}
                                            FROM {}
                                            where day ='{}'
                                            and station_name ='{}'                                    
                                            order by day asc
                                            """.format('cluster_cname','chag', 'disg', 'dws_bc_measure_1d', end_Time, db)
                    try:
                        cursor.execute(sql)
                    except Exception as e:
                        logging.error(e)
                        return []
                    # 获取查询结果
                    result = cursor.fetchall()
                    self.cu_chag_disg(result, obj, db)

                    cursor.close()
                    conn.close()

                for o in obj:
                    obj_ = {}
                    chag_arr = o['chag_arr']
                    disg_arr = o['disg_arr']
                    ratio = o['ratio']
                    disg_arr_ = [i for i in disg_arr if i]
                    chag_arr_ = [i for i in chag_arr if i]
                    ratio_ = [i for i in ratio if i]
                    disg_cons = float('%.2f' % (100 - ((np.std(disg_arr_) / np.average(disg_arr_)) * 100)))  # 放电一致性
                    chag_cons = float('%.2f' % (100 - ((np.std(chag_arr_) / np.average(chag_arr_)) * 100)))  # 充电一致性
                    ratio_cons = float('%.2f' % (100 - ((np.std(ratio_) / np.average(ratio_)) * 100)))  # 完成率一致性
                    obj_['cu_num'] = o['cu_num']
                    obj_['pcs_name'] = o['pcs_name']
                    if math.isnan(float(disg_cons)) is True:
                        obj_['disg_cons'] = 0
                    else:
                        obj_['disg_cons'] = float('%.2f' % (disg_cons))
                    if math.isnan(float(chag_cons)) is True:
                        obj_['chag_cons'] = 0
                    else:
                        obj_['chag_cons'] = float('%.2f' % (chag_cons))
                    if math.isnan(float(ratio_cons)) is True:
                        obj_['ratio_cons'] = 0
                    else:
                        if float(ratio_cons) > 100:
                            obj_['ratio_cons'] = 100
                        else:
                            obj_['ratio_cons'] = float('%.2f' % (ratio_cons))
                    tal.append(obj_)
                return self.returnTypeSuc(tal)
            elif kt == 'ReportWeek':  # 运行周报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime())[:10]  # 默认7天前时间
                # 输入日期
                if DEBUG:
                    logging.info('endTime:%s,startTime:%s' % (endTime, startTime))
                Meter = []
                if db in exclude_station:
                    if db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                        disgCapy = dis_cha_name[db]['disgCapy'][0]
                        if db == 'ygzhen' or db == 'zgtian' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                            Meter = dis_cha_name[db]['Meter']
                    else:
                        disgCapy = dis_cha_name[db]['disgCapy']
                else:
                    disgCapy = ''
                days = timeUtils.dateToDataList(startTime, endTime)
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                if timeUtils.timeStrToTamp('%s %s' % (endTime, '00:00:00')) > timeUtils.timeStrToTamp(
                        '%s %s' % (now, '00:00:00')):
                    endTime = now
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                power = user_session.query(StationR.electric_power).filter(
                    StationR.station_name == db).first()  # 获取电站额定功率
                weekly_dates = timeUtils.get_week_dates(datetime.strptime(startTime, '%Y-%m-%d'),
                                                        datetime.strptime(endTime, '%Y-%m-%d'))
                data = {}
                tal = []
                PCS = []
                chag_end, disg_end = 0, 0
                # 原逻辑
                # for date in weekly_dates:
                #     self.disg_chag_arr(PCS, date, days, db, Meter, disgCapy, power, tal, volume, 1, lang)
                #     indx = weekly_dates.index(date)
                #     len(weekly_dates)
                #     if indx == len(weekly_dates) - 1:  # 取截至到最后时间的累计充放电
                #         chag_end, disg_end = self.tal_disg_chag(db, disgCapy, endTime, now)
                # self.disg_chag_tatio(chag_end, disg_end, tal)

                self.week_disg_chag_arr(PCS, startTime, endTime, weekly_dates, db, Meter, disgCapy, power, tal, volume,
                                        1, lang)
                # chag_end, disg_end = self.tal_disg_chag(db, disgCapy, endTime, now)
                chag_end, disg_end = self._tal_disg_chag(db, disgCapy, endTime, now)
                self.disg_chag_tatio(chag_end, disg_end, tal)
                data['tal'] = tal
                data['PCS'] = PCS
                total = len(tal)
                return self.returnTotalSuc(data, total)
            elif kt == 'ReportMonth':  # 搜索运行月报
                endTime = self.get_argument('endTime', '')
                startTime = self.get_argument('startTime', '')
                # 输入日期
                if DEBUG:
                    logging.info('endTime:%s,startTime:%s' % (endTime, startTime))
                end_Time = '年'.join((endTime).split('-')) + '月'
                star_tTime = '年'.join((startTime).split('-')) + '月'
                data = {}
                tal = []
                PCS = []
                report = user_session.query(ReportOperation).filter(ReportOperation.station == db,
                                                                    ReportOperation.month.between(startTime, endTime),
                                                                    ReportOperation.is_use == 1).all()  # 获取月报详情
                total = user_session.query(func.count(ReportOperation.id)).filter(
                    ReportOperation.month.between(startTime, endTime), ReportOperation.station == db,
                                                                       ReportOperation.is_use == 1).scalar()

                if report:
                    for r in report:
                        obj = {}
                        obj['month'] = r.month
                        obj['disg_arr'] = r.disg
                        obj['chag_arr'] = r.chag
                        obj['ratio'] = r.ratio
                        obj['sys_availty'] = r.sys_availty
                        obj['disg_cons'] = r.disg_cons
                        obj['chag_cons'] = r.chag_cons
                        obj['ratio_cons'] = r.ratio_cons
                        obj['ratio_tal'] = r.ratio_tal
                        obj['day_utilize_hour'] = r.day_utilize_hour
                        obj['id'] = r.id
                        tal.append(obj)
                        if lang == 'en':
                            PCS.append(eval(str(r.en_pcs)))
                        else:
                            PCS.append(eval(str(r.pcs)))
                        data['tal'] = tal
                        data['PCS'] = PCS
                return self.returnTotalSuc(data, total)
            elif kt == 'LoadReportMonth':  # 加载运行月报
                monTime = self.get_argument('month', '')
                # 输入日期
                if DEBUG:
                    logging.info('monTime:%s' % (monTime))
                Meter = []
                if db in exclude_station:
                    if db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                        disgCapy = dis_cha_name[db]['disgCapy'][0]
                        if db == 'ygzhen' or db == 'zgtian' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                            Meter = dis_cha_name[db]['Meter']
                    else:
                        disgCapy = dis_cha_name[db]['disgCapy']
                else:
                    disgCapy = ''
                endtime = timeUtils.last_day_of_month(int(monTime[:4]), int(monTime[5:]))
                days = timeUtils.dateToDataList(monTime + '-01', str(endtime))
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容
                power = user_session.query(StationR.electric_power).filter(
                    StationR.station_name == db).first()  # 获取电站额定功率
                data = {}
                tal = []
                PCS = []
                self.disg_chag_arr(PCS, monTime, days, db, Meter, disgCapy, power, tal, volume, 2, lang)
                if db in exclude_station:
                    chag_end, disg_end = self.tal_disg_chag(db, disgCapy, endtime, now)
                    self.disg_chag_tatio(chag_end, disg_end, tal)
                data['tal'] = tal
                data['PCS'] = PCS
                total = len(tal)
                return self.returnTotalSuc(data, total)
            elif kt == 'AddReportMonth':  # 保存运行月报
                month = self.get_argument('month', '')  # 月份
                chag = self.get_argument('chag_arr', '')  # 充电量
                disg = self.get_argument('disg_arr', '')  # 放电量
                ratio = self.get_argument('ratio', '')  # 充放电量完成率
                ratio_tal = self.get_argument('ratio_tal', '')  # 累计充放电效率
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                day_utilize_hour = self.get_argument('day_utilize_hour', '')  # 日均利用小时数
                chag_cons = self.get_argument('chag_cons', '')  # 充电一致性
                disg_cons = self.get_argument('disg_cons', '')  # 放电一致性
                ratio_cons = self.get_argument('ratio_cons', '')  # 完成率一致性
                PCS = self.get_argument('PCS', [])  # PCS
                a = user_session.query(ReportOperation).filter(ReportOperation.month == month,
                                                               ReportOperation.station == db,
                                                               ReportOperation.is_use == '1').first()
                zh_PCS, en_PCS = '', ''
                if lang == 'en':
                    translate_instance = Translate_cls(ty=1)
                    zh_PCS = translate_instance.str_chinese(PCS)
                else:
                    en_PCS_ = ' Station'.join(PCS.split('站'))
                    en_PCS = ' Area'.join(en_PCS_.split('区'))
                if lang == 'en':
                    if a:
                        return self.customError(
                            "The monthly report already exists and cannot be added. Please return to the modified report!")
                    list_1 = [chag, disg, ratio, ratio_tal, sys_availty, day_utilize_hour, chag_cons, disg_cons,
                              ratio_cons]
                    for t in list_1:
                        if t != '':
                            try:
                                int_str = float(t)
                            except:
                                return self.customError("Please enter the data format!")
                    rp = ReportOperation(month=month, chag=chag, op_ts=timeUtils.getNewTimeStr(), disg=disg,
                                         ratio=ratio, ratio_tal=ratio_tal, sys_availty=sys_availty,
                                         day_utilize_hour=day_utilize_hour, chag_cons=chag_cons, disg_cons=disg_cons,
                                         ratio_cons=ratio_cons, station=db, pcs=zh_PCS, en_pcs=PCS, is_use='1')

                else:
                    if a:
                        return self.customError("月报已存在,无法新增。请返回修改报表！")
                    list_1 = [chag, disg, ratio, ratio_tal, sys_availty, day_utilize_hour, chag_cons, disg_cons,
                              ratio_cons]
                    for t in list_1:
                        if t != '':
                            try:
                                int_str = float(t)
                            except:
                                return self.customError("请输入数据格式！")
                    rp = ReportOperation(month=month, chag=chag, op_ts=timeUtils.getNewTimeStr(), disg=disg,
                                         ratio=ratio,
                                         ratio_tal=ratio_tal, sys_availty=sys_availty,
                                         day_utilize_hour=day_utilize_hour, chag_cons=chag_cons,
                                         disg_cons=disg_cons, ratio_cons=ratio_cons, station=db, pcs=PCS, en_pcs=en_PCS,
                                         is_use='1')
                user_session.add(rp)
                user_session.commit()
                return self.returnTypeSuc("")
            elif kt == 'UpdateReportMonth':  # 编辑运行月报
                id = self.get_argument('id', None)  #
                month = self.get_argument('month', '')  # 月份
                chag = self.get_argument('chag_arr', '')  # 充电量
                disg = self.get_argument('disg_arr', '')  # 放电量
                ratio = self.get_argument('ratio', '')  # 充放电量完成率
                ratio_tal = self.get_argument('ratio_tal', '')  # 累计充放电效率
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                day_utilize_hour = self.get_argument('day_utilize_hour', '')  # 日均利用小时数
                chag_cons = self.get_argument('chag_cons', '')  # 充电一致性
                disg_cons = self.get_argument('disg_cons', '')  # 放电一致性
                ratio_cons = self.get_argument('ratio_cons', '')  # 完成率一致性
                PCS = self.get_argument('PCS', [])  # PCS
                o = user_session.query(ReportOperation).get(id)
                if not o:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError('无效id')
                page = user_session.query(ReportOperation).filter(ReportOperation.month == month,
                                                                  ReportOperation.station == db,
                                                                  ReportOperation.is_use == 1,
                                                                  ReportOperation.id != id).first()
                if page:
                    if lang == 'en':
                        return self.customError(
                            "The monthly report already exists and cannot be added. Please return to the modified report!")
                    else:
                        return self.customError("月报已存在,无法新增。请返回修改报表！")
                list_1 = [chag, disg, ratio, ratio_tal, sys_availty, day_utilize_hour, chag_cons, disg_cons, ratio_cons]
                for t in list_1:
                    if t != '':
                        try:
                            int_str = float(t)
                        except:
                            if lang == 'en':
                                return self.customError("Please enter the data format!")
                            else:
                                return self.customError("请输入数据格式！")
                o.month = month
                o.chag = chag
                o.disg = disg
                o.ratio = ratio
                o.ratio_tal = ratio_tal
                o.sys_availty = sys_availty
                o.day_utilize_hour = day_utilize_hour
                o.chag_cons = chag_cons
                o.disg_cons = disg_cons
                o.ratio_cons = ratio_cons
                if lang == 'en':
                    translate_instance = Translate_cls(ty=1)
                    zh_PCS = translate_instance.str_chinese(PCS)
                    o.PCS = zh_PCS
                    o.en_PCS = PCS
                else:
                    # translate_instance = Translate_cls(ty=2)
                    # en_PCS = translate_instance.str_chinese(PCS)
                    en_PCS_ = ' Station'.join(PCS.split('站'))
                    en_PCS = ' Area'.join(en_PCS_.split('区'))
                    o.PCS = PCS
                    o.en_PCS = en_PCS
                user_session.commit()
                return self.returnTypeSuc("")
            elif kt == 'ReportMonthDelete':  # 删除月报
                id = self.get_argument('id', None)  # id
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(ReportOperation).filter(ReportOperation.id == id).first()
                if not page:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = '0'
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'AnnualReport':  # 年报
                nowTime = self.get_argument('nowTime', timeUtils.getNewTimeStr())[:4]  # 只取年份
                if DEBUG:
                    logging.info('nowTime:%s' % (nowTime))
                type_ = self.get_argument('type_', 'PCS')  # 充电电量
                if db in exclude_station:
                    if type_ == 'PCS':
                        if db == 'ygzhen' or db == 'zgtian' or db == 'houma' or db == 'datong' or db == 'guizhou' or db == 'ygqn' or db == 'shgyu':
                            disgCapy = dis_cha_name[db]['disgCapy'][0]
                            chagCapy = dis_cha_name[db]['chagCapy'][0]
                        else:
                            disgCapy = dis_cha_name[db]['disgCapy']
                            chagCapy = dis_cha_name[db]['chagCapy']
                    elif type_ == 'BMS':
                        disgCapy = dis_cha_name[db]['disgCapy'][1]
                        chagCapy = dis_cha_name[db]['chagCapy'][1]
                    if DEBUG:
                        logging.info('nowTime:%s,disgCapy:%s,chagCapy:%s' % (nowTime, disgCapy, chagCapy))
                else:
                    disgCapy = []
                    chagCapy = []
                en_yue = {1: 'Jan.', 2: 'February', 4: 'April', 3: 'March', 5: 'May', 6: 'June', 7: 'July', 8: 'August',
                          9: 'September', 10: 'Oct.', 11: 'November', 12: 'December'}
                for a in range(1, 13):  # 获取月份
                    month = '%s-%s' % (nowTime, fill0(a))
                    if lang == 'en':
                        da = '%s' % (en_yue[a])
                    else:
                        da = '%s月' % (a)
                    keys.append(month)
                    if lang == 'en':
                        en_columns.append(da)
                    else:
                        columns.append(da)
                total = user_session.query(func.count(AlarmR.id)).filter(
                    AlarmR.ts.between('%s-01-01 00:00:00' % nowTime, '%s-12-31 23:59:59' % nowTime),
                    AlarmR.station == db, AlarmR.value == 2).scalar()
                # if db in exclude_station:
                #     if db == 'dongmu':
                #         disg_arr, chag_arr, ratio, arr = self._getreportMonthData_1(disgCapy, chagCapy, nowTime, db, lang)
                #     else:
                #         disg_arr, chag_arr, ratio, arr = self._getreportMonthData(disgCapy, chagCapy, nowTime, db, type_,lang)
                #
                #     disg_con = '{} {}'.format(float('%.2f' % (np.sum(disg_arr[0]))), 'kWh')  # 放电量总数
                #     disg_cost = '{} {}'.format(num_retain(np.sum(disg_arr[1])), '¥')  # 放电费用
                #     chag_con = '{} {}'.format(float('%.2f' % (np.sum(chag_arr[0]))), 'kWh')  # 充电总数
                #     chag_cost = '{} {}'.format(num_retain(np.sum(chag_arr[1])), '¥')  # 放电费用
                #     earning = '{} {}'.format(num_retain(np.sum(disg_arr[1]) - np.sum(chag_arr[1])), '¥')
                #
                #     if float(np.sum(chag_arr[0])) == 0:
                #         ratio_al = 0
                #     else:
                #         ratio_al = ('%.3f' % ((float(np.sum(disg_arr[0])) / float(np.sum(chag_arr[0])))))
                #         if float(ratio_al) > 1:
                #             ratio_al = 1
                # else:
                #     arr, chag_con, disg_con, ratio_al = self._getreportMonthDataNew(nowTime, db, type_, lang)
                #     disg_cost = "0.00 ¥"
                #     chag_cost = "0.00 ¥"
                #     earning = "0.00 ¥"
                # 改为统一查doris
                if db == 'dongmu':
                    disg_arr, chag_arr, ratio, arr = self._getreportMonthData_1(disgCapy, chagCapy, nowTime, db, lang)
                    disg_con = '{} {}'.format(float('%.2f' % (np.sum(disg_arr[0]))), 'kWh')  # 放电量总数
                    disg_cost = '{} {}'.format(num_retain(np.sum(disg_arr[1])), '¥')  # 放电费用
                    chag_con = '{} {}'.format(float('%.2f' % (np.sum(chag_arr[0]))), 'kWh')  # 充电总数
                    chag_cost = '{} {}'.format(num_retain(np.sum(chag_arr[1])), '¥')  # 放电费用
                    earning = '{} {}'.format(num_retain(np.sum(disg_arr[1]) - np.sum(chag_arr[1])), '¥')

                    if float(np.sum(chag_arr[0])) == 0:
                        ratio_al = 0
                    else:
                        ratio_al = ('%.3f' % ((float(np.sum(disg_arr[0])) / float(np.sum(chag_arr[0])))))
                        if float(ratio_al) > 1:
                            ratio_al = 1
                else:
                    arr, chag_con, disg_con, ratio_al = self._getreportMonthDataNew(nowTime, db, type_, lang)
                    disg_cost = "0.00 ¥"
                    chag_cost = "0.00 ¥"
                    earning = "0.00 ¥"
                if lang == 'en':
                    return self.returnTypeSuc(
                        {'keys': keys, 'columns': en_columns, 'data': arr, 'disg_con': disg_con, 'disg_cost': disg_cost,
                         'chag_con': chag_con, 'chag_cost': chag_cost, 'earning': earning, 'ratio': ratio_al,
                         'alarm': total})
                else:
                    return self.returnTypeSuc(
                        {'keys': keys, 'columns': columns, 'data': arr, 'disg_con': disg_con,
                         'disg_cost': disg_cost, 'chag_con': chag_con, 'chag_cost': chag_cost,
                         'earning': earning, 'ratio': ratio_al, 'alarm': total})

            elif kt == 'MeterAnnualReport':  # 年报
                nowTime = self.get_argument('nowTime', timeUtils.getNewTimeStr())[:4]  # 只取年份
                if DEBUG:
                    logging.info('nowTime:%s,db:%s' % (nowTime, db))
                columns = ["月份", "充电量", "放电量", "效率"]
                en_columns = ["Month", "Charging Capacity", "Discharging Capacity", "Efficiency"]
                # keys = ['day', 'chag', 'disg', 'ratio']
                # if db in exclude_station:
                #     arr, cd, fd, pxl, total = self._getMeterData(nowTime, nowTime, db, 1, lang)
                #     disg_con = '{} {}'.format(float('%.2f' % (np.sum(fd) / 1000)), 'MWH')  # 放电量总数
                #     chag_con = '{} {}'.format(float('%.2f' % (np.sum(cd) / 1000)), 'MWH')  # 充电总数
                # else:
                #     disg_con, chag_con, arr, pxl, total = self._getMeterDataNew(nowTime, db)
                # 改成统一查doris
                disg_con, chag_con, arr, pxl, total = self._getMeterDataNew(nowTime, db)
                if lang == 'en':
                    columns=en_columns
                return self.returnTypeSuc({'keys': keys,'columns': columns,'data': arr, 'disg_con': disg_con, 'chag_con': chag_con, 'ratio': pxl, 'alarm': total})

            elif kt == 'DailyPaper':  # 日报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime())[:10]  # 默认7天前时间
                type_ = self.get_argument('type_', 'PCS')  # 充电电量
                if DEBUG:
                    logging.info('endTime:%s,startTime:%s' % (endTime, startTime))
                if type_ == 'PCS':
                    if db == 'ygzhen' or db == 'zgtian' or db == 'ygqn':
                        disgCapy = dis_cha_name[db]['disgCapy'][0]
                        chagCapy = dis_cha_name[db]['chagCapy'][0]
                    else:
                        disgCapy = dis_cha_name[db]['disgCapy']
                        chagCapy = dis_cha_name[db]['chagCapy']
                elif type_ == 'BMS':
                    disgCapy = dis_cha_name[db]['disgCapy'][1]
                    chagCapy = dis_cha_name[db]['chagCapy'][1]
                days = timeUtils.dateToDataList(startTime, endTime)
                if len(days) > 15:
                    return self.customError('最多选择15天')
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                if timeUtils.timeStrToTamp('%s %s' % (endTime, '00:00:00')) > timeUtils.timeStrToTamp(
                        '%s %s' % (now, '00:00:00')):
                    endTime = now
                days = timeUtils.dateToDataList(startTime, endTime)

                for day in days:
                    da = day[5:].replace('-', '月') + '日'
                    keys.append(day[5:])
                    columns.append(da)
                if db == 'dongmu':
                    disg_arr, chag_arr, ratio, arr = self._getreportData_1(db_con, disgCapy, chagCapy, startTime,
                                                                           endTime, now, db)
                else:
                    disg_arr, chag_arr, ratio, arr = self._getreportData(db_con, disgCapy, chagCapy, startTime, endTime,
                                                                         now, db)
                disg_con = '{} {}'.format(np.sum(disg_arr[0]), 'kWh')  # 放电量总数
                disg_cost = '{} {}'.format(num_retain(np.sum(disg_arr[1])), '¥')  # 放电费用
                chag_con = '{} {}'.format(np.sum(chag_arr[0]), 'kWh')  # 充电总数
                chag_cost = '{} {}'.format(num_retain(np.sum(chag_arr[1])), '¥')  # 放电费用
                earning = '{} {}'.format(num_retain(np.sum(disg_arr[1]) - np.sum(chag_arr[1])), '¥')
                total = user_session.query(func.count(AlarmR.id)).filter(AlarmR.ts.between(startTime, endTime),
                                                                         AlarmR.station == db,
                                                                         AlarmR.value == 2).scalar()
                return self.returnTypeSuc(
                    {'keys': keys, 'columns': columns, 'data': arr, 'disg_con': disg_con, 'disg_cost': disg_cost,
                     'chag_con': chag_con, 'chag_cost': chag_cost, 'earning': earning,
                     'ratio': num_retain(np.mean(ratio), 4), 'alarm': total})
            elif kt == 'MonthlyMagazine':  # 月报
                nowTime = self.get_argument('nowTime', timeUtils.getNewTimeStr())[:7]  # 当前时间
                type_ = self.get_argument('type_', 'PCS')  # 充电电量
                if type_ == 'PCS':
                    if db == 'ygzhen' or db == 'zgtian':
                        disgCapy = dis_cha_name[db]['disgCapy'][0]
                        chagCapy = dis_cha_name[db]['chagCapy'][0]
                    else:
                        disgCapy = dis_cha_name[db]['disgCapy']
                        chagCapy = dis_cha_name[db]['chagCapy']
                elif type_ == 'BMS':
                    disgCapy = dis_cha_name[db]['disgCapy'][1]
                    chagCapy = dis_cha_name[db]['chagCapy'][1]
                am = ['r_measure' + nowTime.replace('-', '')]  # 表名
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                days = timeUtils.getAllDaysByMonth(nowTime)
                if now in days:
                    days = timeUtils.dateToDataList(nowTime + '-01', now)
                if int(nowTime[:4]) > int(now[:4]) and int(nowTime[5:7]) > int(now[5:7]):
                    return self.customError("时间不合理，请重新选择!")
                for day in days:
                    da = day[8:10] + '日'
                    keys.append(day[5:])
                    columns.append(da)
                if db == 'dongmu':
                    disg_arr, chag_arr, ratio, arr = self._getreportData_1(db_con, disgCapy, chagCapy, nowTime + '-01',
                                                                           days[-1], now, db)
                else:
                    disg_arr, chag_arr, ratio, arr = self._getreportData(db_con, disgCapy, chagCapy, nowTime + '-01',
                                                                         days[-1], now, db)
                disg_con = '{} {}'.format(np.sum(disg_arr[0]), 'kWh')  # 放电量总数
                disg_cost = '{} {}'.format(num_retain(np.sum(disg_arr[1])), '¥')  # 放电费用
                chag_con = '{} {}'.format(np.sum(chag_arr[0]), 'kWh')  # 充电总数
                chag_cost = '{} {}'.format(num_retain(np.sum(chag_arr[1])), '¥')  # 放电费用
                earning = '{} {}'.format(num_retain(np.sum(disg_arr[1]) - np.sum(chag_arr[1])), '¥')
                total = user_session.query(func.count(AlarmR.id)).filter(AlarmR.ts.between(days[0], days[-1]),
                                                                         AlarmR.station == db,
                                                                         AlarmR.value == 2).scalar()
                return self.returnTypeSuc(
                    {'keys': keys, 'columns': columns, 'data': arr, 'disg_con': disg_con, 'disg_cost': disg_cost,
                     'chag_con': chag_con, 'chag_cost': chag_cost, 'earning': earning,
                     'ratio': num_retain(np.mean(ratio), 4), 'alarm': total})
            elif kt == 'ReportDay':  # 分段统计日报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime(0))[:10]  # 默认7天前时间
                disgCapy = self.get_argument('disgCapy', [])  # 放电电量
                chagCapy = self.get_argument('chagCapy', [])  # 充电电量
                if DEBUG:
                    logging.info(
                        'startTime:%s,endTime:%s,disgCapy:%s,chagCapy:%s' % (startTime, endTime, disgCapy, chagCapy))
                if not disgCapy or not chagCapy:
                    return self.customError('参数不完整')
                tables = timeUtils.getBetweenMonth(startTime, endTime)
                am = 'r_measure' + pd.Series(tables)  # 为每一项加上前缀
                for a in am:
                    if not _tableIsExist(db_conn, a):
                        return self.customError("时间不合理，请重新选择")
                disgCapy = eval(disgCapy)
                chagCapy = eval(chagCapy)
                # 定义表头
                keys = ['type_', 'name', 'descr', 'unit']
                columns = ['分类', '名称', '描述', '单位']
                data, disg_con, disg_cost, chag_con, chag_cost = [], [], [], [], []
                reports = user_session.query(Report).filter(Report.is_use == 1, Report.station == db).order_by(
                    Report.rate.desc()).all()
                for report in reports:
                    keys.append(report.name)
                    columns.append(report.name)
                for disg in disgCapy:  # 放电
                    mm = real_data('measure', disg, 'db')
                    if not mm['desc']:
                        mm = real_data('measure', disg, 'ram')
                        logging.info('scada is not %s' % (disg))
                    obj = {"type_": "放电量", "name": disg, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}
                    for report in reports:
                        startT = timeUtils.timeStrToTamp('%s %s' % (startTime, report.start_time))
                        endT = timeUtils.timeStrToTamp('%s %s' % (startTime, report.end_time))
                        v = self._getValByNameOfDay(db_con, am.tolist(), disg, startT, endT)
                        disg_con.append(v)
                        cost = v * report.rate
                        disg_cost.append(cost)
                        obj[report.name] = num_retain(cost, 4)
                    data.append(obj)
                for chag in chagCapy:  # 补电
                    mm = real_data('measure', disg, 'db')
                    if not mm['desc']:
                        mm = real_data('measure', disg, 'ram')
                        logging.info('scada is not %s' % (disg))
                    obj = {"type_": "充电量", "name": chag, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}
                    for report in reports:
                        startT = timeUtils.timeStrToTamp('%s %s' % (startTime, report.start_time))
                        endT = timeUtils.timeStrToTamp('%s %s' % (startTime, report.end_time))
                        v = self._getValByNameOfDay(db_con, am.tolist(), chag, startT, endT)
                        chag_con.append(v)
                        cost = v * report.rate
                        chag_cost.append(cost)
                        obj[report.name] = num_retain(cost, 4)
                    data.append(obj)
                return self.returnTypeSuc({'keys': keys, 'columns': columns, 'data': data,
                                           'disg_con': num_retain(np.sum(disg_con), 4) + ' kWh',
                                           'disg_cost': num_retain(np.sum(disg_cost), 4) + ' ¥',
                                           'chag_con': num_retain(np.sum(chag_con), 4) + ' kWh',
                                           'chag_cost': num_retain(np.sum(chag_cost), 4) + ' ¥', })
            elif kt == 'ReportCharts':
                endTime = self.get_argument('endTime', timeUtils.getAgoTime(1))[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime(7))[:10]  # 默认7天前时间
                disgCapy = self.get_argument('disgCapy', [])  # 放电电量
                chagCapy = self.get_argument('chagCapy', [])  # 充电电量
                if DEBUG:
                    logging.info(
                        'endTime:%s,startTime:%s,disgCapy:%s,chagCapy:%s' % (endTime, startTime, disgCapy, chagCapy))
                if db == 'dongmu':
                    # 放电量
                    disgCapy = ['dongmu.PCS1.dcapd', 'dongmu.PCS2.dcapd', 'dongmu.PCS3.dcapd', 'dongmu.PCS4.dcapd',
                                'dongmu.PCS5.dcapd', 'dongmu.PCS6.dcapd', 'dongmu.PCS7.dcapd']
                    # 充电电量
                    chagCapy = ['dongmu.PCS1.ccapd', 'dongmu.PCS2.ccapd', 'dongmu.PCS3.ccapd', 'dongmu.PCS4.ccapd',
                                'dongmu.PCS5.ccapd', 'dongmu.PCS6.ccapd', 'dongmu.PCS7.ccapd']
                elif db == 'zgtian':
                    # 放电量
                    disgCapy = ['tfStzgtian1.EMS.PCS1.Lp1.AcDisgCapyTotl', 'tfStzgtian1.EMS.PCS1.Lp2.AcDisgCapyTotl',
                                'tfStzgtian1.EMS.PCS1.Lp3.AcDisgCapyTotl', 'tfStzgtian1.EMS.PCS1.Lp4.AcDisgCapyTotl',
                                'tfStzgtian2.EMS.PCS2.Lp1.AcDisgCapyTotl', 'tfStzgtian2.EMS.PCS2.Lp2.AcDisgCapyTotl',
                                'tfStzgtian2.EMS.PCS2.Lp3.AcDisgCapyTotl', 'tfStzgtian2.EMS.PCS2.Lp4.AcDisgCapyTotl',
                                'tfStzgtian3.EMS.PCS3.Lp1.AcDisgCapyTotl', 'tfStzgtian3.EMS.PCS3.Lp2.AcDisgCapyTotl',
                                'tfStzgtian3.EMS.PCS3.Lp3.AcDisgCapyTotl', 'tfStzgtian3.EMS.PCS3.Lp4.AcDisgCapyTotl',
                                'tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl', 'tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl',
                                'tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl', 'tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl']
                    # 充电电量
                    chagCapy = ['tfStzgtian1.EMS.PCS1.Lp1.AcChagCapyTotl', 'tfStzgtian1.EMS.PCS1.Lp2.AcChagCapyTotl',
                                'tfStzgtian1.EMS.PCS1.Lp3.AcChagCapyTotl', 'tfStzgtian1.EMS.PCS1.Lp4.AcChagCapyTotl',
                                'tfStzgtian2.EMS.PCS2.Lp1.AcChagCapyTotl', 'tfStzgtian2.EMS.PCS2.Lp2.AcChagCapyTotl',
                                'tfStzgtian2.EMS.PCS2.Lp3.AcChagCapyTotl', 'tfStzgtian2.EMS.PCS2.Lp4.AcChagCapyTotl',
                                'tfStzgtian3.EMS.PCS3.Lp1.AcChagCapyTotl', 'tfStzgtian3.EMS.PCS3.Lp2.AcChagCapyTotl',
                                'tfStzgtian3.EMS.PCS3.Lp3.AcChagCapyTotl', 'tfStzgtian3.EMS.PCS3.Lp4.AcChagCapyTotl',
                                'tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyTotl', 'tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyTotl',
                                'tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyTotl', 'tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyTotl']
                else:
                    disgCapy = eval(disgCapy)
                    chagCapy = eval(chagCapy)
                if endTime >= timeUtils.getNewTimeStr()[:10]:
                    endTime = timeUtils.getAgoTime(1)[:10]
                year = endTime[:4]
                month = endTime[5:7]
                # table = 'r_measure%s%s'%(year,month)
                days = timeUtils.dateToDataList(startTime, endTime)
                jfpg = {u'尖峰': ['00:00:00', '00:00:00', 0], u'峰段': ['00:00:00', '00:00:00', 0],
                        u'平段': ['00:00:00', '00:00:00', 0],
                        u'谷段': ['00:00:00', '00:00:00', 0], }  # 尖峰平谷四个阶段基础数据，存三个值：起始时间，截止时间，费率
                report = user_session.query(Report).filter(Report.is_use == 1, Report.station == db,
                                                           func.find_in_set(int(month), Report.months),
                                                           Report.years == year).group_by(Report.descr).order_by(
                    Report.rate.asc()).all()
                for r in report:
                    jfpg[r.descr][0] = r.start_time
                    jfpg[r.descr][1] = r.end_time
                    jfpg[r.descr][2] = r.rate
                totalobj = {}  # 总收益储能单元
                branchobj = {}  # 各个阶段收益无单位
                # np.zeros(3, dtype=np.int32)
                a1, a2, a3, a4 = np.zeros(len(days), dtype=np.int32), np.zeros(len(days), dtype=np.int32), np.zeros(
                    len(days), dtype=np.int32), np.zeros(len(days), dtype=np.int32)
                # a1,a2,a3,a4 = np.zeros(3, dtype=np.int32),np.zeros(3, dtype=np.int32),np.zeros(3, dtype=np.int32),np.zeros(3, dtype=np.int32)
                for ind in range(len(disgCapy)):
                    un = ind + 1
                    if db == 'ygzhen' or db == 'baodian':
                        r_name = disgCapy[ind][:-14]
                    elif db == 'houma':
                        r_name = disgCapy[ind][:-10]
                    elif db == 'dongmu':
                        r_name = disgCapy[ind][:11]
                    else:
                        r_name = disgCapy[ind][:-16]
                    freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                                 FReport.day.between(days[0], days[-1]),
                                                                 FReport.cause == 1).order_by(FReport.day.asc()).all()
                    # fd,cd,fd_sy,cd_sy = 0,0,0,0
                    jf_sy, fd_sy, pd_sy, gd_sy = [], [], [], []
                    if freport:
                        for re in freport:
                            # 放电数据
                            jf_disg = eval(re.jf_disg)
                            fd_disg = eval(re.fd_disg)
                            pd_disg = eval(re.pd_disg)
                            gd_disg = eval(re.gd_disg)
                            # 充电
                            jf_chag = eval(re.jf_chag)
                            fd_chag = eval(re.fd_chag)
                            pd_chag = eval(re.pd_chag)
                            gd_chag = eval(re.gd_chag)
                            # if jf_disg or jf_chag:
                            sy1 = (np.sum(jf_disg) - np.sum(jf_chag)) * jfpg[u'尖峰'][2]
                            jf_sy.append(round(sy1, 3))
                            # if fd_disg or fd_chag:
                            sy1 = (np.sum(fd_disg) - np.sum(fd_chag)) * jfpg[u'峰段'][2]
                            fd_sy.append(round(sy1, 3))
                            # if pd_disg or pd_chag:
                            sy1 = (np.sum(pd_disg) - np.sum(pd_chag)) * jfpg[u'平段'][2]
                            pd_sy.append(round(sy1, 3))
                            # if gd_disg or gd_chag:
                            sy1 = (np.sum(gd_disg) - np.sum(gd_chag)) * jfpg[u'谷段'][2]
                            gd_sy.append(round(sy1, 3))
                    else:
                        jf_sy, fd_sy, pd_sy, gd_sy = [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0,
                                                                                                    0], [0, 0, 0, 0, 0,
                                                                                                         0, 0]
                    if jf_sy:
                        branchobj['单元%s尖峰' % un] = jf_sy
                    branchobj['单元%s峰段' % un] = fd_sy
                    branchobj['单元%s平段' % un] = pd_sy
                    branchobj['单元%s谷段' % un] = gd_sy
                    # branchunitobj['单元%s峰段'%un] = (pd.Series(fd_sy).astype(str)+' ¥').tolist()
                for key in branchobj.keys():
                    a = np.array(branchobj[key])
                    if u'尖峰' in key:
                        a1 = a1 + a
                    elif u'峰段' in key:
                        a2 = a2 + a
                    elif u'平段' in key:
                        a3 = a3 + a
                    elif u'谷段' in key:
                        a4 = a4 + a
                to = a2 + a3 + a4
                if a1.all():
                    totalobj['尖峰'] = a1.round(3).tolist()
                    to = to + a1
                totalobj['峰段'] = a2.round(3).tolist()
                totalobj['平段'] = a3.round(3).tolist()
                totalobj['谷段'] = a4.round(3).tolist()
                totalobj['Total'] = to.round(3).tolist()
                return self.returnTypeSuc({'total': totalobj, 'branch': branchobj, 'time': days})
            elif kt == 'WeekReport':  # 周报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime())[:10]  # 默认7天前时间
                disgCapy = self.get_argument('disgCapy', [])  # 放电电量
                chagCapy = self.get_argument('chagCapy', [])  # 充电电量
                if DEBUG:
                    logging.info(
                        'endTime:%s,startTime:%s,disgCapy:%s,chagCapy:%s' % (endTime, startTime, disgCapy, chagCapy))
                if not disgCapy or not chagCapy:
                    return self.customError('参数不完整')
                disgCapy = eval(disgCapy)
                chagCapy = eval(chagCapy)
                days = timeUtils.dateToDataList(startTime, endTime)
                if db == 'ygzhen' or db == 'baodian':
                    disg_arr = [i[:-14] for i in disgCapy]
                else:
                    disg_arr = [i[:-16] for i in disgCapy]
                disg, chag, eff = [], [], []
                for day in days:
                    fd, cd, rateno = 0, 0, []
                    freport = user_session.query(FReport).filter(FReport.name.in_(disg_arr), FReport.day == day,
                                                                 FReport.cause == 1).all()
                    for re in freport:
                        # 放电数据
                        jf_disg = eval(re.jf_disg)
                        fd_disg = eval(re.fd_disg)
                        pd_disg = eval(re.pd_disg)
                        gd_disg = eval(re.gd_disg)
                        # 充电
                        jf_chag = eval(re.jf_chag)
                        fd_chag = eval(re.fd_chag)
                        pd_chag = eval(re.pd_chag)
                        gd_chag = eval(re.gd_chag)
                        fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                        cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                        if re.ratio:
                            rateno.append(re.ratio)
                    disg.append('%.2f' % (fd / 1000))
                    chag.append('%.2f' % (cd / 1000))
                    if rateno:
                        eff.append('{} %'.format(round(np.mean(rateno), 4) * 100))
                    else:
                        eff.append('0 %')
                return self.returnTypeSuc({'table': {'disg': disg, 'chag': chag, 'eff': eff}})
            elif kt == 'MeterDailyPaper':  # 关口表日报
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())[:10]  # 当前时间
                startTime = self.get_argument('startTime', timeUtils.getAgoTime())[:10]  # 默认7天前时间
                if DEBUG:
                    logging.info('endTime:%s,startTime:%s,db:%s' % (endTime, startTime, db))
                days = timeUtils.dateToDataList(startTime, endTime)
                if len(days) > 15:
                    return self.customError('最多选择15天')
                arr, cd, fd, pxl, total = self._getMeterData(startTime, endTime, db, 0)
                disg_con = '{} {}'.format(np.sum(fd) / 1000, 'MWH')  # 放电量总数
                chag_con = '{} {}'.format(np.sum(cd) / 1000, 'MWH')  # 充电总数
                # total = user_session.query(func.count(AlarmR.id)).filter(AlarmR.ts.between(startTime,endTime),AlarmR.station==db,AlarmR.value==2).scalar()
                return self.returnTypeSuc(
                    {'data': arr, 'disg_con': disg_con, 'chag_con': chag_con, 'ratio': pxl, 'alarm': total})
            elif kt == 'MeterMonthlyMagazine':  # 关口表月报
                nowTime = self.get_argument('nowTime', timeUtils.getNewTimeStr())[:7]  # 当前时间
                if DEBUG:
                    logging.info('nowTime:%s,db:%s' % (nowTime, db))
                am = ['r_measure' + nowTime.replace('-', '')]  # 表名
                if not _tableIsExist(db_conn, am[0]):
                    return self.customError("时间不合理，请重新选择")
                days = timeUtils.getAllDaysByMonth(nowTime)
                arr, cd, fd, pxl, total = self._getMeterData(days[0], days[-1], db, 0)
                disg_con = '{} {}'.format(np.sum(fd) / 1000, 'MWH')  # 放电量总数
                chag_con = '{} {}'.format(np.sum(cd) / 1000, 'MWH')  # 充电总数
                return self.returnTypeSuc(
                    {'data': arr, 'disg_con': disg_con, 'chag_con': chag_con, 'ratio': pxl, 'alarm': total})
            else:
                return self.pathError()

        except Exception as E:
            if db != 'dongmu':
                for db_c in db_con:
                    db_c.rollback()
            user_session.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            # if db != 'dongmu':
            #     for db_c in db_con:
            #         db_c.rollback()
            user_session.close()

    def pcs_name(self, db, lang, obj, r_name):
        if lang == 'en':
            if db == 'baodian':
                obj['pcs_name'] = 'Pcs standing' + str(r_name[-4])
            elif db == 'dongmu':
                obj['pcs_name'] = 'Pcs standing' + str(r_name[7:])
            elif db == 'datong':
                obj['pcs_name'] = 'Pcs standing' + str(r_name[21]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'houma':
                try:
                    nat = int(r_name[-7:-5])
                    obj['pcs_name'] = 'Pcs standing' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = 'Pcs standing' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'guizhou':
                try:
                    nat = int(r_name[-11:-9])
                    obj['pcs_name'] = 'Pcs standing' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = 'Pcs standing' + str(r_name[-10]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'ygqn':
                nat_1 = r_name.split('.')[-5]
                try:
                    nat = int(r_name[-7:-6])
                    obj['pcs_name'] = nat_1 + ' Pcs standing' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = nat_1 + ' Pcs standing' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'
            else:
                obj['pcs_name'] = 'Pcs standing' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'
        else:
            if db == 'baodian':
                obj['pcs_name'] = 'Pcs站' + str(r_name[-4])
            elif db == 'dongmu':
                obj['pcs_name'] = 'Pcs站' + str(r_name[7:])
            elif db == 'datong':
                obj['pcs_name'] = 'Pcs站' + str(r_name[21]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'houma':
                try:
                    nat = int(r_name[-7:-5])
                    obj['pcs_name'] = 'Pcs站' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = 'Pcs站' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'guizhou':
                try:
                    nat = int(r_name[-11:-9])
                    obj['pcs_name'] = 'Pcs站' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = 'Pcs站' + str(r_name[-10]) + ' ' + str(r_name[-2]) + '#'
            elif db == 'ygqn':
                nat_1 = r_name.split('.')[-5]
                try:
                    nat = int(r_name[-7:-6])
                    obj['pcs_name'] = nat_1 + '区 Pcs站' + str(nat) + ' ' + str(r_name[-2]) + '#'
                except:
                    obj['pcs_name'] = nat_1 + '区 Pcs站' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'
            else:
                obj['pcs_name'] = 'Pcs站' + str(r_name[-6]) + ' ' + str(r_name[-2]) + '#'

    def _r_name_(self, db, disgCapy, ind, type_):
        if db == 'baodian':
            r_name = ('.'.join(disgCapy[ind].split('.')[0:-1]))[:-1]
        else:
            r_name = ('.'.join(disgCapy[ind].split('.')[0:-1])) + '.'
        return r_name

    def generate_dates(self, start_date_str, end_date_str, _time, week_dict):
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        date_list = []
        current_date = start_date
        while current_date <= end_date:
            week_dict[current_date.strftime('%Y-%m-%d')] = _time
            # date_list.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        # return date_list

    def week_disg_chag_arr(self, PCS, sta, end, days, db, Meter, disgCapy, power, tal, volume, ty, lang=None):
        '''获取时间范围内充放电量'''
        sta = sta
        end = end
        week_dict = {}
        for item in days:
            start = item.get("start")
            end = item.get("end")
            _time = str(start) + '~' + str(end)
            self.generate_dates(start, end, _time, week_dict)
        # week_of_month = timeUtils.get_week_number(end)
        # obj['week_of_month'] = week_of_month
        # obj['time'] = str(sta) + '~' + str(end)
        # obj['month'] = end[:4] + '-' + end[5:7]
        conn = pool.connection()
        cursor = conn.cursor()
        rrr_dict = {}
        rrr_d = user_session.query(Availability).filter(Availability.date >= sta, Availability.date <= end,
                                                        Availability.station == db).all()  # 获取停运容量
        for item in rrr_d:
            key = week_dict.get(item.date[:10])
            if key in rrr_dict.keys():
                rrr_dict[key].append(item)
            else:
                rrr_dict[key] = [item]
        import time
        t1 = time.time()
        # 获取各PCS站点的充放电数据以及充放电效率并排名
        contact_name = 'PCS Station. ' if lang == 'en' else 'Pcs站. '
        sql_pcs = "select day, chag ,disg ,ratio, CONCAT('{}', SUBSTRING_INDEX(es_unit_name, '#', 1), '#') AS pcs_name from {} where day BETWEEN '{}' and '{}' and station_name='{}' order by pcs_unit_rk asc". \
            format(contact_name, 'dws_pcs_measure_1d', sta, end, db)
        cursor.execute(sql_pcs)
        result_pcs = cursor.fetchall()
        t2 = time.time()
        pcs_dict = {}
        for item in result_pcs:
            day = item.get("day").strftime('%Y-%m-%d')
            del item["day"]
            key = week_dict.get(day)
            if key not in pcs_dict.keys():
                pcs_dict[key] = [item]
            else:
                pcs_dict[key].append(item)
        t3 = time.time()
        print(t3 - t2, t2 - t1)
        # 获取充放电量以及充放电效率
        sql = "select day, sum(chag) as chag, sum(disg) as disg, avg(ratio) as ratio from {} where day BETWEEN '{}' and '{}' and station_name='{}' group by day order by day". \
            format('dws_st_measure_1d', sta, end, db)
        cursor.execute(sql)
        results = cursor.fetchall()
        result_dict = {}
        t4 = time.time()
        for result in results:
            month = result["day"].strftime('%Y-%m-%d')
            key = week_dict.get(month)
            if key in result_dict:
                result_dict[key]["sum_disg_arr"] += float('%.2f' % (result['disg']))
                result_dict[key]["sum_chag_arr"] += float('%.2f' % (result['chag']))
                result_dict[key]["sum_ratio"] += float('%.2f' % (result['ratio']))
            else:
                result_dict[key] = {}
                result_dict[key]["sum_disg_arr"] = float('%.2f' % (result['disg']))
                result_dict[key]["sum_chag_arr"] = float('%.2f' % (result['chag']))
                result_dict[key]["sum_ratio"] = float('%.2f' % (result['ratio']))
                # if result['chag']:
            #     sum_disg_arr += float('%.2f' % (result['disg']))
            #     sum_chag_arr += float('%.2f' % (result['chag']))
            #     sum_ratio += float('%.2f' % (result['ratio']))
            # if result['chag']:
            #     obj['disg_arr'] = sum_disg_arr = float('%.2f' % (result['disg']))
            #     obj['chag_arr'] = sum_chag_arr = float('%.2f' % (result['chag']))
            #     obj['ratio_tal'] = sum_ratio = float('%.2f' % (result['ratio']))
            #     obj['ratio'] = float('%.2f' % ((sum_disg_arr / sum_chag_arr) * 100)) if sum_chag_arr != 0 else 0
            #     if obj['ratio'] > 100:
            #         obj['ratio'] = 100
            # else:
            #     obj['disg_arr'] = sum_disg_arr = 0
            #     obj['chag_arr'] = sum_chag_arr = 0
            #     obj['ratio_tal'] = 0
            #     obj['ratio'] = 0
        t5 = time.time()

        for key, value in result_dict.items():
            obj = {}
            sta = key[:10]
            end = key[11:]
            week_of_month = timeUtils.get_week_number(end)
            obj['week_of_month'] = week_of_month
            obj['time'] = key
            obj['month'] = end[:7]
            obj['disg_arr'] = value.get("sum_disg_arr")
            obj['chag_arr'] = value.get("sum_chag_arr")
            obj['ratio_tal'] = value.get('sum_ratio')
            obj['ratio'] = float('%.2f' % ((value.get("sum_disg_arr") / value.get("sum_chag_arr")) * 100)) if value.get(
                "sum_disg_arr") != 0 else 0
            if obj['ratio'] > 100:
                obj['ratio'] = 100
            result_pcs_list = pcs_dict.get(key)
            chag_arr = [item['chag'] for item in result_pcs_list]
            disg_arr = [item['disg'] for item in result_pcs_list]
            ratio = [item['ratio'] for item in result_pcs_list]
            if result_pcs_list:
                result_pcs_list = self.dense_rank(result_pcs_list)
            else:
                result_pcs_list = []
            Pcs = result_pcs_list
            if ty == 1 or ty == 3:
                PCS.append(Pcs)
            else:
                PCS.extend(Pcs)
            now = timeUtils.getNewTimeStr()[:10]  # 当前时间
            if ty == 2:
                st_time = str(sta) + ' 00:00:00'
                if now <= str(end):
                    en_time = str(now) + ' 23:59:59'
                else:
                    en_time = str(end) + ' 23:59:59'
            else:
                st_time = str(sta) + ' 00:00:00'
                en_time = str(end) + ' 23:59:59'
            time_ = timeUtils.timeSeconds(st_time, en_time) / 60  # 时间分钟数
            rrr = rrr_dict.get(key)
            sys_availty = self.sys_availty(rrr, time_, volume)
            obj['sys_availty'] = sys_availty
            if ty == 2:
                if now > str(end):
                    dayss = int(str(
                        datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                                :2]) + 1
                else:
                    dayss = int(str(
                        datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                                :2])
            else:
                dayss = int(
                    str(datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                    :2]) + 1

            if db in exclude_station:
                if ty == 3:
                    week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / (power[0] * 1000)), 2)  # 日利用小时数
                else:
                    week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / ((power[0] * 1000)) / dayss),
                                                  2)  # 日均利用小时数
            else:
                if ty == 3:
                    week_day_utilize_hour = round(((value.get("sum_disg_arr") + value.get("sum_chag_arr")) / (power[0] * 1000)), 2)  # 日利用小时数
                else:
                    week_day_utilize_hour = round(((value.get("sum_disg_arr") + value.get("sum_chag_arr")) / ((power[0] * 1000)) / dayss),
                                                  2)  # 日均利用小时数
            # if db in exclude_station:
            disg_arr_ = [i for i in disg_arr if i]
            chag_arr_ = [i for i in chag_arr if i]
            ratio_ = [i for i in ratio if i]

            disg_cons = float('%.2f' % (100 - ((np.std(disg_arr_) / np.average(disg_arr_)) * 100))) if np.average(
                disg_arr_) != 0 else float('%.2f' % (100))  # 放电一致性
            chag_cons = float('%.2f' % (100 - ((np.std(chag_arr_) / np.average(chag_arr_)) * 100))) if np.average(
                chag_arr_) != 0 else float('%.2f' % (100))  # 充电一致性
            ratio_cons = float('%.2f' % (100 - ((np.std(ratio_) / np.average(ratio_)) * 100))) if np.average(
                ratio_) != 0 else float('%.2f' % (100))  # 完成率一致性
            if math.isnan(float(disg_cons)) is True:
                obj['disg_cons'] = 0
            else:
                obj['disg_cons'] = '%.2f' % (disg_cons)
            if math.isnan(float(chag_cons)) is True:
                obj['chag_cons'] = 0
            else:
                obj['chag_cons'] = '%.2f' % (chag_cons)
            if math.isnan(float(ratio_cons)) is True:
                obj['ratio_cons'] = 0
            else:
                if float(ratio_cons) > 100:
                    obj['ratio_cons'] = 100
                else:
                    obj['ratio_cons'] = '%.2f' % (ratio_cons)
            if ty == 1:
                obj['week_day_utilize_hour'] = week_day_utilize_hour
            else:
                obj['day_utilize_hour'] = week_day_utilize_hour
            tal.append(obj)
        t6 = time.time()
        print(t6 - t5, t5 - t4, t4 - t3)
        cursor.close()
        conn.close()

    def _disg_chag_arr(self, PCS, sta, end, days, db, Meter, disgCapy, power, tal, volume, ty, lang=None):
        '''获取时间范围内充放电量'''
        sta = sta
        end = end
        conn = pool.connection()
        cursor = conn.cursor()
        rrr = []
        rrr_dict = {}
        if ty == 3:
            rrr_d = user_session.query(Availability).filter(Availability.date >= sta, Availability.date <= end,
                                                            Availability.station == db).all()  # 获取停运容量
            for item in rrr_d:
                if item.date[:10] in rrr_dict.keys():
                    rrr_dict[item.date[:10]].append(item)
                else:
                    rrr_dict[item.date[:10]] = [item]
            # for item in rrr_d:
            #     if ty == 3:
            #         if item.date[:10] in rrr_dict.keys():
            #             rrr_dict[item.date[:10]].append(item)
            #         else:
            #             rrr_dict[item.date[:10]] = [item]
            #     elif ty == 1:
            #         key = week_dict.get(item.date[:10])
            #         if key in rrr_dict.keys():
            #             rrr_dict[key].append(item)
            #         else:
            #             rrr_dict[key] = [item]
        else:
            for day in days:
                rrr_d = user_session.query(Availability).filter(Availability.date == day,
                                                                Availability.station == db).all()  # 获取停运容量
                if rrr_d:
                    rrr.append(rrr_d[0])
        # 获取各PCS站点的充放电数据以及充放电效率并排名
        contact_name = 'PCS Station. ' if lang == 'en' else 'Pcs站. '
        sql_pcs = "select day, chag ,disg ,ratio, CONCAT('{}', SUBSTRING_INDEX(es_unit_name, '#', 1), '#') AS pcs_name from {} where day BETWEEN '{}' and '{}' and station_name='{}' order by pcs_unit_rk asc". \
            format(contact_name, 'dws_pcs_measure_1d', sta, end, db)
        cursor.execute(sql_pcs)
        result_pcs = cursor.fetchall()
        pcs_dict = {}
        for item in result_pcs:
            day = item.get("day").strftime('%Y-%m-%d')
            del item["day"]
            if day not in pcs_dict.keys():
                pcs_dict[day] = [item]
            else:
                pcs_dict[day].append(item)
        # 获取充放电量以及充放电效率
        sql = "select day, sum(chag) as chag, sum(disg) as disg, avg(ratio) as ratio from {} where day BETWEEN '{}' and '{}' and station_name='{}' group by day order by day desc ". \
            format('dws_st_measure_1d', sta, end, db)
        cursor.execute(sql)
        results = cursor.fetchall()
        for result in results:
            obj = {}
            month = result["day"].strftime('%Y-%m-%d')
            rrr = rrr_dict.get(month)
            obj["month"] = month
            if result['chag']:
                obj['disg_arr'] = sum_disg_arr = float('%.2f' % (result['disg']))
                obj['chag_arr'] = sum_chag_arr = float('%.2f' % (result['chag']))
                obj['ratio_tal'] = sum_ratio = float('%.2f' % (result['ratio']))
                obj['ratio'] = float('%.2f' % ((sum_disg_arr / sum_chag_arr) * 100)) if sum_chag_arr != 0 else 0
                if obj['ratio'] > 100:
                    obj['ratio'] = 100
            else:
                obj['disg_arr'] = sum_disg_arr = 0
                obj['chag_arr'] = sum_chag_arr = 0
                obj['ratio_tal'] = 0
                obj['ratio'] = 0

            result_pcs_list = pcs_dict.get(month)
            chag_arr = [item['chag'] for item in result_pcs_list]
            disg_arr = [item['disg'] for item in result_pcs_list]
            ratio = [item['ratio'] for item in result_pcs_list]
            if result_pcs_list:
                result_pcs_list = self.dense_rank(result_pcs_list)
            else:
                result_pcs_list = []
            Pcs = result_pcs_list
            if ty == 1 or ty == 3:
                PCS.append(Pcs)
            else:
                PCS.extend(Pcs)
            now = timeUtils.getNewTimeStr()[:10]  # 当前时间
            if ty == 2:
                st_time = str(sta) + ' 00:00:00'
                if now <= str(end):
                    en_time = str(now) + ' 23:59:59'
                else:
                    en_time = str(end) + ' 23:59:59'
            else:
                st_time = str(sta) + ' 00:00:00'
                en_time = str(end) + ' 23:59:59'
            time_ = timeUtils.timeSeconds(st_time, en_time) / 60  # 时间分钟数
            sys_availty = self.sys_availty(rrr, time_, volume)
            obj['sys_availty'] = sys_availty
            if ty == 2:
                if now > str(end):
                    dayss = int(str(
                        datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                                :2]) + 1
                else:
                    dayss = int(str(
                        datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                                :2])
            else:
                dayss = int(
                    str(datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time,
                                                                                            "%Y-%m-%d %H:%M:%S"))[
                    :2]) + 1

            if db in exclude_station:
                if ty == 3:
                    week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / (power[0] * 1000)), 2)  # 日利用小时数
                else:
                    week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / ((power[0] * 1000)) / dayss),
                                                  2)  # 日均利用小时数
            else:
                if ty == 3:
                    week_day_utilize_hour = round(((sum_disg_arr + sum_chag_arr) / (power[0] * 1000)), 2)  # 日利用小时数
                else:
                    week_day_utilize_hour = round(((sum_disg_arr + sum_chag_arr) / ((power[0] * 1000)) / dayss),
                                                  2)  # 日均利用小时数
            # if db in exclude_station:
            disg_arr_ = [i for i in disg_arr if i]
            chag_arr_ = [i for i in chag_arr if i]
            ratio_ = [i for i in ratio if i]

            disg_cons = float('%.2f' % (100 - ((np.std(disg_arr_) / np.average(disg_arr_)) * 100))) if np.average(
                disg_arr_) != 0 else float('%.2f' % (100))  # 放电一致性
            chag_cons = float('%.2f' % (100 - ((np.std(chag_arr_) / np.average(chag_arr_)) * 100))) if np.average(
                chag_arr_) != 0 else float('%.2f' % (100))  # 充电一致性
            ratio_cons = float('%.2f' % (100 - ((np.std(ratio_) / np.average(ratio_)) * 100))) if np.average(
                ratio_) != 0 else float('%.2f' % (100))  # 完成率一致性
            if math.isnan(float(disg_cons)) is True:
                obj['disg_cons'] = 0
            else:
                obj['disg_cons'] = '%.2f' % (disg_cons)
            if math.isnan(float(chag_cons)) is True:
                obj['chag_cons'] = 0
            else:
                obj['chag_cons'] = '%.2f' % (chag_cons)
            if math.isnan(float(ratio_cons)) is True:
                obj['ratio_cons'] = 0
            else:
                if float(ratio_cons) > 100:
                    obj['ratio_cons'] = 100
                else:
                    obj['ratio_cons'] = '%.2f' % (ratio_cons)
            if ty == 1:
                obj['week_day_utilize_hour'] = week_day_utilize_hour
            else:
                obj['day_utilize_hour'] = week_day_utilize_hour
            tal.append(obj)
        cursor.close()
        conn.close()

    def disg_chag_arr(self, PCS, m, days, db, Meter, disgCapy, power, tal, volume, ty, lang=None):
        '''获取时间范围内充放电量'''
        obj = {}
        if ty == 3:
            sta = m
            end = m
            obj['month'] = m[:4] + '-' + m[5:7] + '-' + m[8:]  # 日期
        elif ty == 2:
            sta = m + '-01'
            end = timeUtils.last_day_of_month(int(m[:4]), int(m[5:]))
            obj['month'] = m[:4] + '-' + m[5:]
        elif ty == 1:
            sta = m['start']
            end = m['end']
            week_of_month = timeUtils.get_week_number(end)
            obj['week_of_month'] = week_of_month
            obj['time'] = str(sta) + '~' + str(end)
            obj['month'] = end[:4] + '-' + end[5:7]
        if db in exclude_station and db == "dongmu":
            disg_arr, chag_arr, ratio, Pcs = self._getreportW(disgCapy, sta, end, db, lang)

            if db == 'ygzhen' or db == 'zgtian' or db == 'guizhou' or db == 'ygqn':
                disg, chag, eff = self.meter_data(Meter, sta, end)
                obj['disg_arr'] = float('%.2f' % (sum(disg)))
                obj['chag_arr'] = float('%.2f' % (sum(chag)))
                if obj['chag_arr'] != 0:
                    obj['ratio'] = float('%.2f' % ((float(sum(disg)) / float(sum(chag))) * 100))
                    if obj['ratio'] > 100:
                        obj['ratio'] = 100
                else:
                    obj['ratio'] = 0
            else:
                obj['disg_arr'] = float('%.2f' % (sum(disg_arr)))
                obj['chag_arr'] = float('%.2f' % (sum(chag_arr)))
                if obj['chag_arr'] != 0:
                    obj['ratio'] = float('%.2f' % ((float(sum(disg_arr)) / float(sum(chag_arr))) * 100))
                    if obj['ratio'] > 100:
                        obj['ratio'] = 100
                else:
                    obj['ratio'] = 0
        else:
            conn = pool.connection()
            cursor = conn.cursor()
            # 获取充放电量以及充放电效率
            sql = "select sum(chag) as chag, sum(disg) as disg, avg(ratio) as ratio from {} where day BETWEEN '{}' and '{}' and station_name='{}'". \
                format('dws_st_measure_1d', sta, end, db)
            cursor.execute(sql)
            result = cursor.fetchone()
            if result:
                obj['disg_arr'] = sum_disg_arr = float('%.2f' % (result['disg']))
                obj['chag_arr'] = sum_chag_arr = float('%.2f' % (result['chag']))
                obj['ratio_tal'] = sum_ratio = float('%.2f' % (result['ratio']))
                obj['ratio'] = float('%.2f' % ((sum_disg_arr / sum_chag_arr) * 100)) if sum_chag_arr != 0 else 0
                if obj['ratio'] > 100:
                    obj['ratio'] = 100
            else:
                obj['disg_arr'] = sum_disg_arr = 0
                obj['chag_arr'] = sum_chag_arr = 0
                obj['ratio_tal'] = 0
                obj['ratio'] = 0
            # 获取各PCS站点的充放电数据以及充放电效率并排名
            contact_name = 'PCS Station. ' if lang == 'en' else 'Pcs站. '
            sql_pcs = "select chag,disg,ratio, CONCAT('{}', SUBSTRING_INDEX(es_unit_name, '#', 1), '#') AS pcs_name from {} where day BETWEEN '{}' and '{}' and station_name='{}' order by pcs_unit_rk asc ". \
                format(contact_name, 'dws_pcs_measure_1d', sta, end, db)
            cursor.execute(sql_pcs)
            result_pcs = cursor.fetchall()
            chag_arr = [item['chag'] for item in result_pcs]
            disg_arr = [item['disg'] for item in result_pcs]
            ratio = [item['ratio'] for item in result_pcs]
            if result_pcs:
                result_pcs = self.dense_rank(result_pcs)
            else:
                result_pcs = []
            Pcs = result_pcs
            cursor.close()
            conn.close()
        if ty == 1 or ty == 3:
            PCS.append(Pcs)
        else:
            PCS.extend(Pcs)
        rrr = []
        if ty == 3:
            rrr_d = user_session.query(Availability).filter(Availability.date.like(m[:10] + '%'),
                                                            Availability.station == db).all()  # 获取停运容量
            if rrr_d:
                rrr.append(rrr_d[0])
        else:
            for day in days:
                rrr_d = user_session.query(Availability).filter(Availability.date == day,
                                                                Availability.station == db).all()  # 获取停运容量
                if rrr_d:
                    rrr.append(rrr_d[0])

        now = timeUtils.getNewTimeStr()[:10]  # 当前时间
        if ty == 2:
            st_time = str(sta) + ' 00:00:00'
            if now <= str(end):
                en_time = str(now) + ' 23:59:59'
            else:
                en_time = str(end) + ' 23:59:59'
        else:
            st_time = str(sta) + ' 00:00:00'
            en_time = str(end) + ' 23:59:59'
        time_ = timeUtils.timeSeconds(st_time, en_time) / 60  # 时间分钟数
        sys_availty = self.sys_availty(rrr, time_, volume)
        obj['sys_availty'] = sys_availty
        if ty == 2:
            if now > str(end):
                dayss = int(str(
                    datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time, "%Y-%m-%d %H:%M:%S"))[
                            :2]) + 1
            else:
                dayss = int(str(
                    datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time, "%Y-%m-%d %H:%M:%S"))[
                            :2])
        else:
            dayss = int(
                str(datetime.strptime(en_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(st_time, "%Y-%m-%d %H:%M:%S"))[
                :2]) + 1

        if db in exclude_station:
            if ty == 3:
                week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / (power[0] * 1000)), 2)  # 日利用小时数
            else:
                week_day_utilize_hour = round(((sum(disg_arr) + sum(chag_arr)) / ((power[0] * 1000)) / dayss),
                                              2)  # 日均利用小时数
        else:
            if ty == 3:
                week_day_utilize_hour = round(((sum_disg_arr + sum_chag_arr) / (power[0] * 1000)), 2)  # 日利用小时数
            else:
                week_day_utilize_hour = round(((sum_disg_arr + sum_chag_arr) / ((power[0] * 1000)) / dayss),
                                              2)  # 日均利用小时数
        # if db in exclude_station:
        disg_arr_ = [i for i in disg_arr if i]
        chag_arr_ = [i for i in chag_arr if i]
        ratio_ = [i for i in ratio if i]

        disg_cons = float('%.2f' % (100 - ((np.std(disg_arr_) / np.average(disg_arr_)) * 100))) if np.average(
            disg_arr_) != 0 else float('%.2f' % (100))  # 放电一致性
        chag_cons = float('%.2f' % (100 - ((np.std(chag_arr_) / np.average(chag_arr_)) * 100))) if np.average(
            chag_arr_) != 0 else float('%.2f' % (100))  # 充电一致性
        ratio_cons = float('%.2f' % (100 - ((np.std(ratio_) / np.average(ratio_)) * 100))) if np.average(
            ratio_) != 0 else float('%.2f' % (100))  # 完成率一致性
        if math.isnan(float(disg_cons)) is True:
            obj['disg_cons'] = 0
        else:
            obj['disg_cons'] = '%.2f' % (disg_cons)
        if math.isnan(float(chag_cons)) is True:
            obj['chag_cons'] = 0
        else:
            obj['chag_cons'] = '%.2f' % (chag_cons)
        if math.isnan(float(ratio_cons)) is True:
            obj['ratio_cons'] = 0
        else:
            if float(ratio_cons) > 100:
                obj['ratio_cons'] = 100
            else:
                obj['ratio_cons'] = '%.2f' % (ratio_cons)
        if ty == 1:
            obj['week_day_utilize_hour'] = week_day_utilize_hour
        else:
            obj['day_utilize_hour'] = week_day_utilize_hour
        tal.append(obj)

    def _tal_disg_chag(self, db, disgCapy, endTime, now):
        '''获取最后时间的累计充放电量'''
        disg, chag, eff = [], [], []
        freport_3 = user_session.query(FReport.jf_chag, FReport.jf_disg, ).filter(FReport.name == db,
                                                                                  FReport.cause == 3).first()  # 截至当前时间的累计充放电
        r_names = []
        for ind in range(len(disgCapy)):
            r_name = self._r_name_(db, disgCapy, ind, 'PCS')
            r_names.append(r_name)

        freport = user_session.query(FReport).filter(FReport.name.in_(r_names), FReport.day.between(endTime, now),
                                                     FReport.cause == 1).group_by(FReport.name).all()
        report_dict = {}
        if freport:
            for re in freport:
                if re.name not in report_dict:
                    report_dict[re.name] = {}
                    report_dict[re.name]["fd"] = 0
                    report_dict[re.name]["cd"] = 0
                # 放电数据
                jf_disg = eval(re.jf_disg)
                fd_disg = eval(re.fd_disg)
                pd_disg = eval(re.pd_disg)
                gd_disg = eval(re.gd_disg)
                # 充电
                jf_chag = eval(re.jf_chag)
                fd_chag = eval(re.fd_chag)
                pd_chag = eval(re.pd_chag)
                gd_chag = eval(re.gd_chag)
                report_dict[re.name]["fd"] += np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                report_dict[re.name]["cd"] += np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
        if report_dict:
            for key, item in report_dict.items():
                disg.append(item["fd"])
                chag.append(item["cd"])
        chag_end, disg_end = 0, 0
        if freport_3:
            disg_end = float('%.2f' % (float(freport_3[1]) - float(sum(disg))))
            chag_end = float('%.2f' % (float(freport_3[0]) - float(sum(chag))))
        return chag_end, disg_end

    def tal_disg_chag(self, db, disgCapy, endTime, now):
        '''获取最后时间的累计充放电量'''
        disg, chag, eff = [], [], []
        freport_3 = user_session.query(FReport.jf_chag, FReport.jf_disg, ).filter(FReport.name == db,
                                                                                  FReport.cause == 3).first()  # 截至当前时间的累计充放电
        for ind in range(len(disgCapy)):

            r_name = self._r_name_(db, disgCapy, ind, 'PCS')
            fd, cd = 0, 0
            freport = user_session.query(FReport).filter(FReport.name == r_name, FReport.day.between(endTime, now),
                                                         FReport.cause == 1).all()
            if freport:
                for re in freport:
                    # 放电数据
                    jf_disg = eval(re.jf_disg)
                    fd_disg = eval(re.fd_disg)
                    pd_disg = eval(re.pd_disg)
                    gd_disg = eval(re.gd_disg)
                    # 充电
                    jf_chag = eval(re.jf_chag)
                    fd_chag = eval(re.fd_chag)
                    pd_chag = eval(re.pd_chag)
                    gd_chag = eval(re.gd_chag)
                    fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                    cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电

                disg.append(fd)
                chag.append(cd)
        chag_end, disg_end = 0, 0
        if freport_3:
            disg_end = float('%.2f' % (float(freport_3[1]) - float(sum(disg))))
            chag_end = float('%.2f' % (float(freport_3[0]) - float(sum(chag))))
        return chag_end, disg_end

    def ReportMonthWeek(self, days, db, disgCapy, endTime, mon_list, now, power, volume, ty):
        data = {}
        tal = []
        PCS = []
        disg_end = 0  # 最后日期的累计放电
        chag_end = 0  # 最后日期的累计充电
        if ty == 1:
            for m in mon_list:
                obj = {}
                sta = m + '-01'
                end = timeUtils.last_day_of_month(m[:5], m[5:])
                if ty == 1:
                    week_of_month = timeUtils.get_week_number(m['end'])
                    obj['week_of_month'] = week_of_month
                    obj['time'] = str(sta) + '~' + str(end)
                disg_arr, chag_arr, ratio, Pcs = self._getreportW(disgCapy, sta, end, db)
                PCS.append(Pcs)
                obj['month'] = m[:5] + '年' + m[5:] + '月'
                obj['disg_arr'] = ('%.2f' % (sum(disg_arr)))
                obj['chag_arr'] = ('%.2f' % (sum(chag_arr)))
                if obj['chag_arr'] != 0:
                    obj['ratio'] = ('%.2f' % ((float(sum(disg_arr)) / float(sum(chag_arr))) * 100))
                else:
                    obj['ratio'] = 0
                rrr = []
                for day in days:
                    rrr_d = user_session.query(Availability).filter(Availability.date.like(day[:10] + '%'),
                                                                    Availability.station == db).all()  # 获取停运容量
                    if rrr_d:
                        rrr.append(rrr_d[0])
                st_time = sta + ' 00:00:00'
                en_time = end + ' 23:59:59'
                time_ = timeUtils.timeSeconds(st_time, en_time) / 60  # 周时间
                sys_availty = self.sys_availty(rrr, time_, volume)
                obj['sys_availty'] = sys_availty
                days = (en_time - st_time) + 1
                week_day_utilize_hour = (
                        '%.2f' % (((sum(disg_arr) + sum(chag_arr)) / (power[0] * 1000)) / days))  # 日均利用小时数
                disg_arr_ = [i for i in disg_arr if i]
                chag_arr_ = [i for i in chag_arr if i]
                ratio_ = [i for i in ratio if i]
                disg_cons = ('%.2f' % (100 - ((np.std(disg_arr_) / np.average(disg_arr_)) * 100)))  # 放电一致性
                chag_cons = ('%.2f' % (100 - ((np.std(chag_arr_) / np.average(chag_arr_)) * 100)))  # 充电一致性
                ratio_cons = ('%.2f' % (100 - ((np.std(ratio_) / np.average(ratio_)) * 100)))  # 完成率一致性
                if math.isnan(float(disg_cons)) is True:
                    obj['disg_cons'] = 0
                else:
                    obj['disg_cons'] = disg_cons

                if math.isnan(float(chag_cons)) is True:
                    obj['chag_cons'] = 0
                else:
                    obj['chag_cons'] = chag_cons

                if math.isnan(float(ratio_cons)) is True:
                    obj['ratio_cons'] = 0
                else:
                    if float(ratio_cons) > 100:
                        obj['ratio_cons'] = 100
                    else:
                        obj['ratio_cons'] = ratio_cons
                obj['week_day_utilize_hour'] = week_day_utilize_hour
                tal.append(obj)
                indx = mon_list.index(m)
                len(mon_list)
                if indx == len(mon_list) - 1:  # 取截至到最后时间的累计充放电
                    disg, chag, eff = [], [], []
                    freport_3 = user_session.query(FReport.jf_chag, FReport.jf_disg, ).filter(FReport.name == db,
                                                                                              FReport.cause == 3).first()  # 截至当前时间的累计充放电
                    for ind in range(len(disgCapy)):
                        r_name = self._r_name_(db, disgCapy, ind, 'PCS')
                        fd, cd = 0, 0
                        freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                                     FReport.day.between(endTime, now),
                                                                     FReport.cause == 1).all()
                        if freport:
                            for re in freport:
                                # 放电数据
                                jf_disg = eval(re.jf_disg)
                                fd_disg = eval(re.fd_disg)
                                pd_disg = eval(re.pd_disg)
                                gd_disg = eval(re.gd_disg)
                                # 充电
                                jf_chag = eval(re.jf_chag)
                                fd_chag = eval(re.fd_chag)
                                pd_chag = eval(re.pd_chag)
                                gd_chag = eval(re.gd_chag)
                                fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                                cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                            disg.append(fd)
                            chag.append(cd)
                    disg_end = float(freport_3[1]) - float(sum(disg))
                    chag_end = float(freport_3[0]) - float(sum(chag))
            for d in tal[::-1]:  # 取累计充放电效率
                indx = tal.index(d)
                if int(indx) == len(tal) - 1:
                    d['ratio_tal'] = ('%.2f' % ((disg_end / chag_end) * 100))
                else:
                    disg_tal = float('%.2f' % (disg_end - float(d['disg_arr'])))
                    chag_tal = float('%.2f' % (chag_end - float(d['chag_arr'])))
                    d['ratio_tal'] = ('%.2f' % ((disg_tal / chag_tal) * 100))
                    disg_end = float('%.2f' % (disg_tal))
                    chag_end = float('%.2f' % (chag_tal))
            data['tal'] = tal
            data['PCS'] = PCS
            total = len(tal)
        return data, total

    def _getreportMonthData(self, disgCapy, chagCapy, year, db, type_, lang=None):

        disg_arr, chag_arr, ratio = [[], []], [[], []], []
        arr1, arr2, arr3 = [], [], []
        ll = 0
        for ind in range(len(disgCapy)):
            xl = ''
            r_name = self._r_name_(db, disgCapy, ind, type_)
            # 计算放电量
            m1 = real_data('measure', disgCapy[ind], 'db')
            if not m1['desc']:
                m1 = real_data('measure', disgCapy[ind], 'ram')
            if lang == 'en':
                obj1 = {"type_": "Discharge capacity", "name": disgCapy[ind],
                        'descr': _value_desc_fanyi(lang, m1['desc'], CoFa_obj), 'unit': self._getUnit(m1['unit'])}
            else:
                obj1 = {"type_": "放电量", "name": disgCapy[ind], 'descr': m1['desc'], 'unit': self._getUnit(m1['unit'])}
            # 计算充电量
            m2 = real_data('measure', chagCapy[ind], 'db')
            if not m2['desc']:
                m2 = real_data('measure', chagCapy[ind], 'ram')
            if lang == 'en':
                obj2 = {"type_": "Charge capacity", "name": chagCapy[ind],
                        'descr': _value_desc_fanyi(lang, m2['desc'], CoFa_obj), 'unit': self._getUnit(m2['unit'])}
            else:
                obj2 = {"type_": "充电量", "name": chagCapy[ind], 'descr': m2['desc'], 'unit': self._getUnit(m2['unit'])}
            # 计算效率
            if 'PCS' in disgCapy[ind] or 'Pcs' in disgCapy[ind]:
                xl = 'PCS'
            elif 'BMS' in disgCapy[ind]:
                xl = 'BMS'
            if lang == 'en':
                obj3 = {"type_": "Efficiency", "name": '', 'descr': '% sefficiency %s' % (xl, ind + 1), 'unit': ''}
            else:
                obj3 = {"type_": "效率", "name": '', 'descr': '%s效率 %s' % (xl, ind + 1), 'unit': ''}
            v1, v11, v2, v22, v3, v33 = [], [], [], [], [], []  # 放电量数据;放电费用；充电量数据；充电费用；效率;去掉0的效率
            for a in range(1, 13):  # 获取月份
                month = '%s-%s' % (year, fill0(a))
                jfpg = {u'尖峰': ['00:00:00', '00:00:00', 0], u'峰段': ['00:00:00', '00:00:00', 0],
                        u'平段': ['00:00:00', '00:00:00', 0],
                        u'谷段': ['00:00:00', '00:00:00', 0], }  # 尖峰平谷四个阶段基础数据，存三个值：起始时间，截止时间，费率
                report = user_session.query(Report).filter(Report.is_use == 1, Report.station == db,
                                                           func.find_in_set(month, Report.months),
                                                           Report.years == year).group_by(Report.descr).order_by(
                    Report.rate.asc()).all()
                for r in report:
                    jfpg[r.descr][0] = r.start_time
                    jfpg[r.descr][1] = r.end_time
                    jfpg[r.descr][2] = r.rate
                days = timeUtils.getAllDaysByMonth(month)
                freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                             FReport.day.between(days[0], days[-1]),
                                                             FReport.cause == 1).order_by(FReport.day.asc()).all()
                fd, cd, fd_sy, cd_sy, rate, rateno = 0, 0, 0, 0, [], []
                if ll == 0:
                    ll = len(freport)
                if freport:
                    for re in freport:
                        # 放电数据
                        jf_disg = eval(re.jf_disg)
                        fd_disg = eval(re.fd_disg)
                        pd_disg = eval(re.pd_disg)
                        gd_disg = eval(re.gd_disg)
                        # 充电
                        jf_chag = eval(re.jf_chag)
                        fd_chag = eval(re.fd_chag)
                        pd_chag = eval(re.pd_chag)
                        gd_chag = eval(re.gd_chag)
                        fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                        cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                        fd_sy = fd_sy + np.sum(jf_disg) * jfpg[u'尖峰'][2] + np.sum(fd_disg) * jfpg[u'峰段'][2] + np.sum(
                            pd_disg) * jfpg[u'平段'][2] + np.sum(gd_disg) * jfpg[u'谷段'][2]
                        cd_sy = cd_sy + np.sum(jf_chag) * jfpg[u'尖峰'][2] + np.sum(fd_chag) * jfpg[u'峰段'][2] + np.sum(
                            pd_chag) * jfpg[u'平段'][2] + np.sum(gd_chag) * jfpg[u'谷段'][2]
                        if re.ratio:
                            rateno.append(re.ratio)
                            rate.append(re.ratio)
                        else:
                            rateno.append(0)
                            rate.append(0)
                    v1.append(fd)  # 放电数据
                    v11.append(fd_sy)  # 放电费用
                    v2.append(cd)  # 充电数据
                    v22.append(cd_sy)  # 充电费用
                    v3.append(num_retain(np.mean(rate) if rate else 0, 4))  # 效率数据
                    if rateno:
                        v33.append(np.mean(rateno))  # 效率数据
                    else:
                        v33.append(0)
                    obj1[month] = round(fd, 3)
                    obj2[month] = round(cd, 3)
                    obj3[month] = num_retain((np.mean(rate) if rate else 0), 4)
                else:
                    fd, fd_sy, cd, cd_sy = 0.0, 0.0, 0.0, 0.0
                    v1.append(fd)  # 放电数据
                    v11.append(fd_sy)  # 放电费用
                    v2.append(cd)  # 充电数据
                    v22.append(cd_sy)  # 充电费用

            obj1['min'] = num_retain((min(v1) if v1 else 0), 1)
            obj1['max'] = num_retain((max(v1) if v1 else 0), 1)
            obj1['cost'] = num_retain((np.sum(v11) if v11 else 0), 2)
            obj2['min'] = num_retain((min(v2) if v2 else 0), 1)
            obj2['max'] = num_retain((max(v2) if v2 else 0), 1)
            obj2['cost'] = num_retain((np.sum(v22) if v22 else 0), 2)
            obj3['min'] = num_retain((min(v3) if v3 else 0), 1)
            obj3['max'] = num_retain((max(v3) if v3 else 0), 1)
            disg_arr[0].append(v1)
            disg_arr[1].append(v11)
            chag_arr[0].append(v2)
            chag_arr[1].append(v22)
            ratio.append(np.mean(v33))
            arr1.append(obj1)
            arr2.append(obj2)
            arr3.append(obj3)
        return disg_arr, chag_arr, ratio, arr1 + arr2 + arr3

    def _getreportMonthDataNew(self, year, db, type_, lang=None):
        chag_data_list=[]  # 充电电量
        disg_data_list=[]  # 放电电量
        ratio_data_list=[]  # 充放电效率
        # 查询idc数据库
        result = []
        if type_ == "PCS":
            table_name = 'dws_pcs_measure_cw_cm_cy'
            order_field_name = 'pcs_unit_rk'
        else:
            table_name = 'dws_bc_measure_cw_cm_cy'
            order_field_name = 'unit_cluster_rk'
        conn = pool.connection()
        cursor = conn.cursor()

        sql = """SELECT * 
                FROM
                    {} 
                WHERE
                    station_name = '{}' 
                    AND date_type = '{}' 
                    AND date_value LIKE '%{}%' 
                ORDER BY
                    start_date ASC,
                    {} ASC;""".format(table_name, db,
                                               'year_month', str(year), order_field_name)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
        result = cursor.fetchall()
        if result:
            chag_data_list = self._MerageData(result, 'chag', '充电量',type_, lang)
            disg_data_list = self._MerageData(result, 'disg', '放电量',type_, lang)
            ratio_data_list = self._MerageData(result, 'ratio', '效率',type_, lang)
        # 获取累计值和平均值
        sql_max_min_avg = """SELECT sum(chag) as sum_chag, sum(disg) as sum_disg, avg(ratio) as avg_ratio 
                            FROM
                                {} 
                            WHERE
                                station_name = '{}' 
                                AND date_type = '{}' 
                                AND date_value LIKE '%{}%';""".format(table_name, db,
                                                                      'year_month', str(year))
        try:
            cursor.execute(sql_max_min_avg)
        except Exception as e:
            logging.error(e)
        result_max_min_avg = cursor.fetchone()
        sum_chag = result_max_min_avg['sum_chag'] if result_max_min_avg['sum_chag'] else '0' + ' kWh'
        sum_disg = result_max_min_avg['sum_disg'] if result_max_min_avg['sum_disg'] else '0' + ' kWh'
        avg_ratio = num_retain((result_max_min_avg['avg_ratio'] if result_max_min_avg['avg_ratio'] else 0) / 100, 4)
        return chag_data_list + disg_data_list + ratio_data_list, sum_chag, sum_disg, avg_ratio

    def _MerageData(self, data, value_key, name, type_, lang):
        """合并数据"""
        # 创建一个空字典来存储结果
        result = {}
        en_data = {
            "放电量": "Discharge capacity",
            "充电量": "Charge capacity",
            "效率": "Efficiency"
        }
        en_name = {
            "chag": "ChgElTl",
            "disg": "DigElTl",
        }

        # 遍历数据
        for item in data:
            pcs_name = item['pcs_name']
            date_value = item['date_value'][0:4] + '-' + item['date_value'][4:6]
            if '效率' in name:
                value = num_retain(item[value_key] / 100, 4)
                max_min_value = num_retain(item[value_key] / 100, 1)
            else:
                value = item[value_key]
                max_min_value = item[value_key]

            # 如果 pcs_name 已经存在于结果字典中，则更新其值
            if pcs_name in result:
                # 更新 date_value 和 value
                if date_value in result[pcs_name]:
                    # 更新最大值和最小值
                    result[pcs_name]['max'] = max(result[pcs_name]['max'], max_min_value)
                    result[pcs_name]['min'] = min(result[pcs_name]['min'], max_min_value)
                else:
                    # 添加新的 date_value 和 value
                    result[pcs_name][date_value] = value
                    result[pcs_name]['max'] = max(result[pcs_name]['max'], max_min_value)
                    result[pcs_name]['min'] = min(result[pcs_name]['min'], max_min_value)
            else:
                # 否则，创建一个新的字典并将 pcs_name 作为键
                if lang == 'en' and str(name) in en_data.keys():
                    t_name = en_data[str(name)]
                    descr = (str(item['pcs_unit_cname']) + " " + str(t_name)).replace("单元", "unit")
                else:
                    t_name = str(name)
                    descr = str(item['pcs_unit_cname']) + " " + str(t_name)
                if type_ != "PCS":
                    descr = descr.replace('PCS', 'BMS')
                if "效率" not in name:
                    pcs_name_list = pcs_name.split('#')
                    #taicgxr.EMS.EN3.PCS2.ChgElTl
                    #taicgxr.EMS.EN1.BMS1.ChgElTl
                    new_name = item['station_name'] + ".EMS.EN" + pcs_name_list[0] + "." + pcs_name_list[1].replace('-','') + "." + en_name[value_key]
                    if type_ != "PCS":
                        new_name = new_name.replace('PCS','BMS')
                else:
                    new_name = ""

                result[pcs_name] = {
                    'max': max_min_value,
                    'min': max_min_value,
                    'cost': 0,
                    date_value: value,
                    'name': new_name,
                    'descr': descr,
                    'type_': t_name,
                    'unit': 'kWh'
                }

        # 将结果转换为所需的格式
        final_result = []
        for pcs_name, values in result.items():
            merged_dict = {'pcs_name': pcs_name}
            merged_dict.update(values)
            final_result.append(merged_dict)

        return final_result

    def _getreportMonthData_1(self, disgCapy, chagCapy, year, db, lang=None):
        disg_arr, chag_arr, ratio = [[], []], [[], []], []
        arr1, arr2, arr3 = [], [], []
        for ind in range(len(disgCapy)):
            xl = ''
            r_name = self._r_name_(db, disgCapy, ind,'PCS')
            # 计算放电量
            if lang == 'en':
                obj1 = {"type_": "Discharge capacity", "name": disgCapy[ind],
                        'descr': disgCapy[ind][:-6] + 'Total discharge capacity KWH', 'unit': 'kWh'}
            else:
                obj1 = {"type_": "放电量", "name": disgCapy[ind], 'descr': disgCapy[ind][:-6] + '放电容量总KWH', 'unit': 'kWh'}
            # 计算充电量
            if lang == 'en':
                obj2 = {"type_": "Charge capacity", "name": chagCapy[ind],
                        'descr': disgCapy[ind][:-6] + 'Total charging capacity KWH', 'unit': 'kWh'}
            else:
                obj2 = {"type_": "充电量", "name": chagCapy[ind], 'descr': disgCapy[ind][:-6] + '充电容量总KWH', 'unit': 'kWh'}
            # 计算效率
            if 'PCS' in disgCapy[ind] or 'Pcs' in disgCapy[ind]:
                xl = 'PCS'
            elif 'BMS' in disgCapy[ind]:
                xl = 'BMS'
            if lang == 'en':
                obj3 = {"type_": "Efficiency", "name": '', 'descr': '% efficiency %s' % (xl, ind + 1), 'unit': ''}
            else:
                obj3 = {"type_": "效率", "name": '', 'descr': '%s效率 %s' % (xl, ind + 1), 'unit': ''}
            v1, v11, v2, v22, v3, v33 = [], [], [], [], [], []  # 放电量数据;放电费用；充电量数据；充电费用；效率;去掉0的效率
            for a in range(1, 13):  # 获取月份
                month = '%s-%s' % (year, fill0(a))
                jfpg = {u'尖峰': ['00:00:00', '00:00:00', 0], u'峰段': ['00:00:00', '00:00:00', 0],
                        u'平段': ['00:00:00', '00:00:00', 0],
                        u'谷段': ['00:00:00', '00:00:00', 0], }  # 尖峰平谷四个阶段基础数据，存三个值：起始时间，截止时间，费率
                report = user_session.query(Report).filter(Report.is_use == 1, Report.station == db,
                                                           func.find_in_set(month, Report.months),
                                                           Report.years == year).group_by(Report.descr).order_by(
                    Report.rate.asc()).all()
                for r in report:
                    jfpg[r.descr][0] = r.start_time
                    jfpg[r.descr][1] = r.end_time
                    jfpg[r.descr][2] = r.rate
                days = timeUtils.getAllDaysByMonth(month)
                freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                             FReport.day.between(days[0], days[-1]),
                                                             FReport.cause == 1).order_by(FReport.day.asc()).all()
                fd, cd, fd_sy, cd_sy, rate, rateno = 0, 0, 0, 0, [], []
                for re in freport:
                    # 放电数据
                    jf_disg = eval(re.jf_disg)
                    fd_disg = eval(re.fd_disg)
                    pd_disg = eval(re.pd_disg)
                    gd_disg = eval(re.gd_disg)
                    # 充电
                    jf_chag = eval(re.jf_chag)
                    fd_chag = eval(re.fd_chag)
                    pd_chag = eval(re.pd_chag)
                    gd_chag = eval(re.gd_chag)
                    fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                    cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                    fd_sy = fd_sy + np.sum(jf_disg) * jfpg[u'尖峰'][2] + np.sum(fd_disg) * jfpg[u'峰段'][2] + np.sum(
                        pd_disg) * jfpg[u'平段'][2] + np.sum(gd_disg) * jfpg[u'谷段'][2]
                    cd_sy = cd_sy + np.sum(jf_chag) * jfpg[u'尖峰'][2] + np.sum(fd_chag) * jfpg[u'峰段'][2] + np.sum(
                        pd_chag) * jfpg[u'平段'][2] + np.sum(gd_chag) * jfpg[u'谷段'][2]
                    rate.append(re.ratio)
                    # if re.ratio:
                    #     rateno.append(re.ratio)
                    rateno.append(round(re.ratio if re.ratio else 0, 4))

                v1.append(fd)  # 放电数据
                v11.append(fd_sy)  # 放电费用
                v2.append(cd)  # 充电数据
                v22.append(cd_sy)  # 充电费用
                # v3.append(np.mean(rate))  # 效率数据
                v3.append(num_retain(np.mean(rate) if rateno else 0, 3))  # 效率数据
                if rateno:
                    v33.append(np.mean(rateno))  # 效率数据
                obj1[month] = round(fd, 2)
                obj2[month] = round(cd, 2)
                obj3[month] = num_retain(np.mean(rate) if rate else 0, 4)
            obj1['min'] = num_retain(min(v1) if v1 else 0, 2)
            obj1['max'] = num_retain(max(v1) if v1 else 0, 2)
            obj1['cost'] = num_retain(np.sum(v11) if v11 else 0, 2)
            obj2['min'] = num_retain(min(v2) if v2 else 0, 2)
            obj2['max'] = num_retain(max(v2) if v2 else 0, 2)
            obj2['cost'] = num_retain(np.sum(v22) if v22 else 0, 2)
            obj3['min'] = num_retain(min(v3) if v3 else 0, 4)
            obj3['max'] = num_retain(max(v3) if v3 else 0, 4)
            disg_arr[0].append(v1)
            disg_arr[1].append(v11)
            chag_arr[0].append(v2)
            chag_arr[1].append(v22)
            ratio.append(np.mean(v33))
            arr1.append(obj1)
            arr2.append(obj2)
            arr3.append(obj3)
        return disg_arr, chag_arr, ratio, arr1 + arr2 + arr3

    def _getreportData(self, db_con, disgCapy, chagCapy, startTime, endTime, now, db):
        disg_arr, chag_arr, ratio = [[], []], [[], []], []
        arr1, arr2, arr3 = [], [], []
        ll = 0
        year = endTime[:4]
        month = int(endTime[5:7])
        jfpg = {u'尖峰': ['00:00:00', '00:00:00', 0], u'峰段': ['00:00:00', '00:00:00', 0],
                u'平段': ['00:00:00', '00:00:00', 0],
                u'谷段': ['00:00:00', '00:00:00', 0], }  # 尖峰平谷四个阶段基础数据，存三个值：起始时间，截止时间，费率
        report = user_session.query(Report).filter(Report.is_use == 1, Report.station == db,
                                                   func.find_in_set(month, Report.months),
                                                   Report.years == year).group_by(Report.descr).order_by(
            Report.rate.asc()).all()
        for r in report:
            jfpg[r.descr][0] = r.start_time
            jfpg[r.descr][1] = r.end_time
            jfpg[r.descr][2] = r.rate
        for ind in range(len(disgCapy)):
            xl = ''
            r_name = self._r_name_(db, disgCapy, ind, 'PCS')
            # 计算放电量
            m1 = real_data('measure', disgCapy[ind], 'db')
            if not m1['desc']:
                m1 = real_data('measure', disgCapy[ind], 'ram')
            obj1 = {"type_": "放电量", "name": disgCapy[ind], 'descr': m1['desc'], 'unit': self._getUnit(m1['unit'])}
            # 计算充电量
            m2 = real_data('measure', chagCapy[ind], 'db')
            if not m2['desc']:
                m2 = real_data('measure', chagCapy[ind], 'ram')
            obj2 = {"type_": "充电量", "name": chagCapy[ind], 'descr': m2['desc'], 'unit': self._getUnit(m2['unit'])}
            # 计算效率
            if 'PCS' in disgCapy[ind] or 'Pcs' in disgCapy[ind]:
                xl = 'PCS'
            elif 'BMS' in disgCapy[ind]:
                xl = 'BMS'
            obj3 = {"type_": "效率", "name": '', 'descr': '%s效率 %s' % (xl, ind + 1), 'unit': ''}
            v1, v11, v2, v22, v3, v33 = [], [], [], [], [], []  # 放电量数据;放电费用；充电量数据；充电费用；效率；去掉0的费率
            freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                         FReport.day.between(startTime, endTime),
                                                         FReport.cause == 1).order_by(FReport.day.asc()).all()
            if ll == 0:
                ll = len(freport)
            if freport:
                for re in freport:
                    d = str(re.day)[5:10]
                    # 放电数据
                    jf_disg = eval(re.jf_disg)
                    fd_disg = eval(re.fd_disg)
                    pd_disg = eval(re.pd_disg)
                    gd_disg = eval(re.gd_disg)
                    # 充电
                    jf_chag = eval(re.jf_chag)
                    fd_chag = eval(re.fd_chag)
                    pd_chag = eval(re.pd_chag)
                    gd_chag = eval(re.gd_chag)
                    fd = np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                    cd = np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                    fd_sy = np.sum(jf_disg) * jfpg[u'尖峰'][2] + np.sum(fd_disg) * jfpg[u'峰段'][2] + np.sum(pd_disg) * \
                            jfpg[u'平段'][2] + np.sum(gd_disg) * jfpg[u'谷段'][2]
                    cd_sy = np.sum(jf_chag) * jfpg[u'尖峰'][2] + np.sum(fd_chag) * jfpg[u'峰段'][2] + np.sum(pd_chag) * \
                            jfpg[u'平段'][2] + np.sum(gd_chag) * jfpg[u'谷段'][2]
                    v1.append(fd)  # 放电数据
                    v11.append(fd_sy)  # 放电费用
                    v2.append(cd)  # 充电数据
                    v22.append(cd_sy)  # 充电费用
                    v3.append(re.ratio)  # 效率数据
                    if re.ratio:
                        v33.append(re.ratio)
                    obj1[d] = round(fd, 3)
                    obj2[d] = round(cd, 3)
                    obj3[d] = num_retain(re.ratio, 4)
            else:
                for l in range(ll):
                    fd, fd_sy, cd, cd_sy = 0.0, 0.0, 0.0, 0.0
                    v1.append(fd)  # 放电数据
                    v11.append(fd_sy)  # 放电费用
                    v2.append(cd)  # 充电数据
                    v22.append(cd_sy)  # 充电费用
            obj1['min'] = min(v1) if v1 else 0
            obj1['max'] = max(v1) if v1 else 0
            obj1['cost'] = num_retain(np.sum(v11))
            obj2['min'] = min(v2) if v2 else 0
            obj2['max'] = max(v2) if v2 else 0
            obj2['cost'] = num_retain(np.sum(v22))
            obj3['min'] = num_retain(min(v3) if v3 else 0, 4)
            obj3['max'] = num_retain(max(v3) if v3 else 0, 4)
            disg_arr[0].append(v1)
            disg_arr[1].append(v11)
            chag_arr[0].append(v2)
            chag_arr[1].append(v22)
            if v33:
                ratio.append(np.mean(v33))
            arr1.append(obj1)
            arr2.append(obj2)
            arr3.append(obj3)
        return disg_arr, chag_arr, ratio, arr1 + arr2 + arr3

    def _getreportW(self, disgCapy, startTime, endTime, db, lang=None):
        disg, chag, eff, rrr = [], [], [], []
        data_re = []
        fd_list, cd_list, rateno_list = [], [], []
        for ind in range(len(disgCapy)):
            r_name = self._r_name_(db, disgCapy, ind,'PCS')
            fd, cd, rateno = 0, 0, []
            obj = {}
            freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                         FReport.day.between(startTime, endTime),
                                                         FReport.cause == 1).order_by(FReport.day).all()
            for re in freport:
                # 放电数据
                jf_disg = eval(re.jf_disg)
                fd_disg = eval(re.fd_disg)
                pd_disg = eval(re.pd_disg)
                gd_disg = eval(re.gd_disg)
                # 充电
                jf_chag = eval(re.jf_chag)
                fd_chag = eval(re.fd_chag)
                pd_chag = eval(re.pd_chag)
                gd_chag = eval(re.gd_chag)
                fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
            disg.append(float('%.2f'%(fd)))
            chag.append(float('%.2f'%(cd)))
            self.pcs_name(db, lang, obj, r_name)
            obj['chag'] = float('%.2f' % (cd))
            obj['disg'] = float('%.2f' % (fd))
            if cd != 0:
                obj['ratio'] = float('%.2f' % ((fd / cd) * 100))
                if obj['ratio'] > 100:
                    obj['ratio'] = 100
                eff.append(obj['ratio'])
            else:
                obj['ratio'] = 0
                eff.append(obj['ratio'])
            fd_list.append(float('%.2f' % fd))
            cd_list.append(float('%.2f' % cd))
            if cd != 0:
                rateno_list.append(float('%.2f' % ((fd / cd) * 100)))
            else:
                rateno_list.append(0)
            data_re.append(obj)
        # 获取排名
        for f in fd_list:
            indx = fd_list.index(f)
            for v in sorted(fd_list)[::-1]:
                ind = sorted(fd_list)[::-1].index(v)
                if f == v:
                    data_re[indx]['fd_rank'] = ind + 1
                    break
        for c in cd_list:
            indx = cd_list.index(c)
            for v in sorted(cd_list)[::-1]:
                ind = sorted(cd_list)[::-1].index(v)
                if c == v:
                    data_re[indx]['cd_rank'] = ind + 1
                    break
        for r in rateno_list:
            indx = rateno_list.index(r)
            for v in sorted(rateno_list)[::-1]:
                ind = sorted(rateno_list)[::-1].index(v)
                if r == v:
                    data_re[indx]['ra_rank'] = ind + 1
                    break
        return disg, chag, eff, data_re

    def disg_chag_tatio(self, chag_end, disg_end, tal):
        for d in tal[::-1]:  # 取累计充放电效率
            disg_tal = 0
            chag_tal = 0
            indx = tal.index(d)
            if int(indx) == len(tal) - 1:
                if chag_end == 0.0:
                    d['ratio_tal'] = 0
                else:
                    d['ratio_tal'] = float('%.2f' % ((disg_end / chag_end) * 100))
                if d['ratio_tal'] > 100:
                    d['ratio_tal'] = 100
            else:
                disg_tal = disg_end - float(d['disg_arr'])
                chag_tal = chag_end - float(d['chag_arr'])
                if chag_tal == 0.0:
                    d['ratio_tal'] = 0
                else:
                    d['ratio_tal'] = float('%.2f' % ((disg_tal / chag_tal) * 100))
                if d['ratio_tal'] > 100:
                    d['ratio_tal'] = 100
            disg_end =float('%.2f'%(disg_tal))
            chag_end = float('%.2f'%(chag_tal))

    def meter_data(self, Meter, startTime, endTime):
        '''电表充放电量'''
        disg, chag, eff = [], [], []
        mfreport = user_session.query(FReport).filter(FReport.name == Meter[0], FReport.day.between(startTime, endTime),
                                                      FReport.cause == 1).order_by(FReport.day).all()
        if mfreport:
            for m in mfreport:
                try:
                    disg.append(float(m.jf_disg))
                except:
                    disg.append(int(m.jf_disg))
                try:
                    chag_m = float(m.jf_chag)
                except:
                    chag_m = int(m.jf_chag)
                if chag_m != 0:
                    chag.append(float(m.jf_chag))
                    mratio = float('%.2f' % ((float(m.jf_disg) / float(m.jf_chag)) * 100))
                    eff.append(mratio)
                else:
                    eff.append(0)
        return disg, chag, eff

    def _getMeterData(self, startTime, endTime, db, ty=0, lang=None):
        '''关口报表数据
        ty=0,计算具体每天数据
        ty=1,按月计算
        '''
        cd, fd, xl, arr, pxl = [], [], [], [], 0
        for meter in meter_name:
            if db in meter:  # 有关口表的电站
                if ty:  # 按月计算,一次计算一个月的数据 startTime=endTime
                    for a in range(1, 13):  # 获取月份
                        disg, chag, ratio = [], [], []
                        month = '%s-%s' % (startTime, fill0(a))
                        days = timeUtils.getAllDaysByMonth(month)
                        freport = user_session.query(FReport).filter(FReport.name == meter[db],
                                                                     FReport.day.between(days[0], days[-1]),
                                                                     FReport.cause == 1).order_by(
                            FReport.day.asc()).all()
                        for rp in freport:
                            chag.append(float(rp.jf_chag))
                            disg.append(float(rp.jf_disg))
                            if rp.ratio:
                                ratio.append(rp.ratio)
                                xl.append(rp.ratio)
                        n1 = np.sum(chag)
                        n2 = np.sum(disg)
                        arr.append({'day': month, 'chag': n1, 'disg': n2,
                                    'ratio': num_retain(np.mean(ratio), 4) if ratio else 0})
                        cd.append(float('%.2f'%(n1)))
                        fd.append(float('%.2f'%(n2)))
                    total = user_session.query(func.count(AlarmR.id)).filter(
                        AlarmR.ts.between('%s-01-01' % startTime, '%s-12-31' % startTime), AlarmR.station == db,
                        AlarmR.value == 2).scalar()
                else:
                    freport = user_session.query(FReport).filter(FReport.name == meter[db],
                                                                 FReport.day.between(startTime, endTime),
                                                                 FReport.cause == 1).order_by(FReport.day.asc()).all()
                    for rp in freport:
                        arr.append({'day': str(rp.day)[0:10], 'chag': rp.jf_chag, 'disg': rp.jf_disg,
                                    'ratio': round(rp.ratio, 4)})
                        cd.append(float('%.2f'%(rp.jf_chag)))
                        fd.append(float('%.2f'%(rp.jf_disg)))
                        if rp.ratio:
                            xl.append(rp.ratio)
                    total = user_session.query(func.count(AlarmR.id)).filter(AlarmR.ts.between(startTime, endTime),
                                                                             AlarmR.station == db,
                                                                             AlarmR.value == 2).scalar()
            else:
                if ty:
                    total = user_session.query(func.count(AlarmR.id)).filter(
                        AlarmR.ts.between('%s-01-01' % startTime, '%s-12-31' % startTime), AlarmR.station == db,
                        AlarmR.value == 2).scalar()
                else:
                    total = user_session.query(func.count(AlarmR.id)).filter(AlarmR.ts.between(startTime, endTime),
                                                                             AlarmR.station == db,
                                                                             AlarmR.value == 2).scalar()
        if xl:
            pxl = num_retain(np.mean(xl), 4)
        return arr, cd, fd, pxl, total

    def _getMeterDataNew(self, year, db):
        """
        关口年报数据
        """

        conn = pool.connection()
        cursor = conn.cursor()
        sql = """SELECT * 
                FROM
                    {} 
                WHERE
                    station_name = '{}' 
                    AND date_type = '{}' 
                    AND date_value LIKE '%{}%' 
                ORDER BY
                    date_value ASC;""".format('dws_st_measure_cw_cm_cy', db,
                                      'year_month', str(year))
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
        data_l: List[Dict[str, Any]] = [] # 月份数据列表
        result = cursor.fetchall()
        for month_d in range(1, 13):  # 获取月份
            month = '%s%s' % (year, fill0(month_d))
            data_val = self._find_index_by_date_value(result, month)
            if data_val == False:
                month_data = {
                    'chag': 0,
                    'day': '%s-%s' % (year, fill0(month_d)),
                    'disg': 0,
                    'ratio': 0
                }
                data_l.append(month_data)
            else:
                month_data = {
                    'chag': data_val['chag'],
                    'day': '%s-%s' % (year, fill0(month_d)),
                    'disg': data_val['disg'],
                    'ratio': num_retain(data_val['ratio'] / 100, 4) if data_val['ratio'] else 0
                }
                data_l.append(month_data)

        total = user_session.query(func.count(AlarmR.id)).filter(
            AlarmR.ts.between('%s-01-01' % year, '%s-12-31' % year), AlarmR.station == db, AlarmR.value == 2).scalar()

        # 获取累计值和平均值
        sql_max_min_avg = """SELECT sum(chag) as sum_chag, sum(disg) as sum_disg, avg(ratio) as avg_ratio
                                    FROM
                                        {}
                                    WHERE
                                        station_name = '{}'
                                        AND date_type = '{}'
                                        AND date_value LIKE '%{}%';""".format('dws_st_measure_cw_cm_cy', db,
                                                                              'year_month', str(year))
        try:
            cursor.execute(sql_max_min_avg)
        except Exception as e:
            logging.error(e)
        result_max_min_avg = cursor.fetchone()
        sum_chag = result_max_min_avg['sum_chag'] if result_max_min_avg['sum_chag'] else 0
        chag_con = '{} {}'.format(float('%.2f' % (sum_chag / 1000)), 'MWH')  # 充电量总数
        sum_disg = result_max_min_avg['sum_disg'] if result_max_min_avg['sum_disg'] else 0
        disg_con = '{} {}'.format(float('%.2f' % (sum_disg / 1000)), 'MWH')  # 放电量总数
        avg_ratio = num_retain((result_max_min_avg['avg_ratio'] if result_max_min_avg['avg_ratio'] else 0)/100, 4)

        return disg_con, chag_con, data_l, avg_ratio, total

    def _getreportData_1(self, db_con, disgCapy, chagCapy, startTime, endTime, now, db):
        disg_arr, chag_arr, ratio = [[], []], [[], []], []
        arr1, arr2, arr3 = [], [], []
        year = endTime[:4]
        month = int(endTime[5:7])
        jfpg = {u'尖峰': ['00:00:00', '00:00:00', 0], u'峰段': ['00:00:00', '00:00:00', 0],
                u'平段': ['00:00:00', '00:00:00', 0],
                u'谷段': ['00:00:00', '00:00:00', 0], }  # 尖峰平谷四个阶段基础数据，存三个值：起始时间，截止时间，费率
        report = user_session.query(Report).filter(Report.is_use == 1, Report.station == db,
                                                   func.find_in_set(month, Report.months),
                                                   Report.years == year).group_by(Report.descr).order_by(
            Report.rate.asc()).all()
        for r in report:
            jfpg[r.descr][0] = r.start_time
            jfpg[r.descr][1] = r.end_time
            jfpg[r.descr][2] = r.rate
        for ind in range(len(disgCapy)):
            xl = ''
            r_name = disgCapy[ind][:-6]
            # 计算放电量
            obj1 = {"type_": "放电量", "name": disgCapy[ind], 'descr': disgCapy[ind][:-6] + '放电容量总KWH', 'unit': 'kWh'}
            # 计算充电量
            obj2 = {"type_": "充电量", "name": chagCapy[ind], 'descr': disgCapy[ind][:-6] + '充电容量总KWH', 'unit': 'kWh'}
            # 计算效率
            if 'PCS' in disgCapy[ind] or 'Pcs' in disgCapy[ind]:
                xl = 'PCS'
            elif 'BMS' in disgCapy[ind]:
                xl = 'BMS'
            obj3 = {"type_": "效率", "name": '', 'descr': '%s效率 %s' % (xl, ind + 1), 'unit': ''}
            v1, v11, v2, v22, v3, v33 = [], [], [], [], [], []  # 放电量数据;放电费用；充电量数据；充电费用；效率；去掉0的费率
            freport = user_session.query(FReport).filter(FReport.name == r_name,
                                                         FReport.day.between(startTime, endTime),
                                                         FReport.cause == 1).order_by(FReport.day.asc()).all()
            for re in freport:
                d = str(re.day)[5:10]
                # 放电数据
                jf_disg = eval(re.jf_disg)
                fd_disg = eval(re.fd_disg)
                pd_disg = eval(re.pd_disg)
                gd_disg = eval(re.gd_disg)
                # 充电
                jf_chag = eval(re.jf_chag)
                fd_chag = eval(re.fd_chag)
                pd_chag = eval(re.pd_chag)
                gd_chag = eval(re.gd_chag)
                fd = np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                cd = np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                fd_sy = np.sum(jf_disg) * jfpg[u'尖峰'][2] + np.sum(fd_disg) * jfpg[u'峰段'][2] + np.sum(pd_disg) * \
                        jfpg[u'平段'][2] + np.sum(gd_disg) * jfpg[u'谷段'][2]
                cd_sy = np.sum(jf_chag) * jfpg[u'尖峰'][2] + np.sum(fd_chag) * jfpg[u'峰段'][2] + np.sum(pd_chag) * \
                        jfpg[u'平段'][2] + np.sum(gd_chag) * jfpg[u'谷段'][2]
                v1.append(fd)  # 放电数据
                v11.append(fd_sy)  # 放电费用
                v2.append(cd)  # 充电数据
                v22.append(cd_sy)  # 充电费用
                v3.append(re.ratio)  # 效率数据
                if re.ratio:
                    v33.append(re.ratio)
                obj1[d] = round(fd, 3)
                obj2[d] = round(cd, 3)
                obj3[d] = num_retain(re.ratio, 4)
            obj1['min'] = min(v1) if v1 else 0
            obj1['max'] = max(v1) if v1 else 0
            obj1['cost'] = num_retain(np.sum(v11))
            obj2['min'] = min(v2) if v2 else 0
            obj2['max'] = max(v2) if v2 else 0
            obj2['cost'] = num_retain(np.sum(v22))
            obj3['min'] = num_retain(min(v3) if v3 else 0, 4)
            obj3['max'] = num_retain(max(v3) if v3 else 0, 4)
            disg_arr[0].append(v1)
            disg_arr[1].append(v11)
            chag_arr[0].append(v2)
            chag_arr[1].append(v22)
            if v33:
                ratio.append(np.mean(v33))
            arr1.append(obj1)
            arr2.append(obj2)
            arr3.append(obj3)
        return disg_arr, chag_arr, ratio, arr1 + arr2 + arr3

    def _find_index_by_date_value(self, data, date_value):
        for index, item in enumerate(data):
            if item['date_value'] == date_value:
                return data[index]
        return False

    def _getMonthDisgCap(self, db_conn, disgCapy, nowTime, disg_arr, arr):
        '''按月计算放电量
        db_conn:数据库连接数组
        disgCapy：放电量名称集合
        nowTime：时间，YYYY-mm
        disg_arr:存放查询数据的三维数组
        arr：返回的数据集
        '''
        for dc in disgCapy:  # 放电量
            mm = real_data('measure', dc, 'db')
            if not mm['desc']:
                mm = real_data('measure', dc, 'ram')
                logging.info('scada is not %s' % (dc))
            obj, at = {"type_": "放电量", "name": dc, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}, []
            for a in range(1, 13):  # 获取月份
                month = '%s-%s' % (nowTime, fill0(a))
                am = 'r_measure' + month.replace('-', '')
                days = timeUtils.getAllDaysByMonth(month)  # 每月具体天数
                v = self._getValByNameMonth(db_conn, am, days, dc)
                at.append(v)
                obj[month] = v
            if len(at) > 0:
                obj['max'] = max(at)
                obj['min'] = min(at)
            else:
                obj['max'] = 0
                obj['min'] = 0
            disg_arr[0].append(at)
            disg_arr[1].append(sum(at) * shouyi)
            arr.append(obj)
        return arr, disg_arr

    def _getMonthChagCap(self, db_conn, chagCapy, nowTime, chag_arr, arr):
        '''按月计算充电量'''
        for cg in chagCapy:  # 充电量
            mm = real_data('measure', cg, 'db')
            if not mm['desc']:
                mm = real_data('measure', cg, 'ram')
                logging.info('scada is not %s' % (cg))
            obj, at = {"type_": "充电量", "name": cg, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}, []
            for a in range(1, 13):  # 获取月份
                month = '%s-%s' % (nowTime, fill0(a))
                am = 'r_measure' + month.replace('-', '')
                days = timeUtils.getAllDaysByMonth(month)  # 每月具体天数
                v = self._getValByNameMonth(db_conn, am, days, cg)
                at.append(v)
                obj[month] = v
            if len(at) > 0:
                obj['max'] = max(at)
                obj['min'] = min(at)
            else:
                obj['max'] = 0
                obj['min'] = 0
            chag_arr[0].append(at)
            chag_arr[1].append(sum(at) * chengben)
            arr.append(obj)
        return arr, chag_arr

    def _getSocMonthRatip(self, db_conn, chagCapy, disg_arr, nowTime, arr, chag_arr, ratio):
        '''计算效率数据'''
        for i in range(len(chagCapy)):
            name = chagCapy[i].replace('KWH_ChagCapyTotl', 'Sys_Soc')
            val = []
            obj = {"type_": "效率", "name": '', 'descr': '效率 %s' % (i + 1), 'unit': ''}
            for a in range(1, 13):
                month = '%s-%s' % (nowTime, fill0(a))
                am = 'r_measure' + month.replace('-', '')
                days = timeUtils.getAllDaysByMonth(month)  # 每月具体天数
                sum1, sum2 = 0, 0
                if _tableIsExist(db_conn, am):
                    for tim in days:
                        startT = timeUtils.timeStrToTamp('%s 00:00:01' % tim)
                        endT = timeUtils.timeStrToTamp('%s 23:59:59' % tim)
                        sum3, sum4 = self._getValByNameOfDay(db_conn[1], [am], name, startT, endT, 1)
                        sum1 = sum1 + sum3
                        sum2 = sum2 + sum4
                if chag_arr[0][i][a - 1] == 0.0 or disg_arr[0][i][a - 1] == 0.0 or sum1 == 0 or sum2 == 0:
                    v = 0
                else:
                    v = disg_arr[0][i][a - 1] / chag_arr[0][i][a - 1] / sum1 * sum2
                    obj[month] = num_retain(v, 4)
                val.append(v)
            if len(val) > 0:
                obj['max'] = num_retain(max(val), 4)
                obj['min'] = num_retain(min(val), 4)
            else:
                obj['max'] = '0.0000'
                obj['min'] = '0.0000'
            obj['cost'] = num_retain(disg_arr[1][i] - chag_arr[1][i])
            ratio.append(val)
            arr.append(obj)
        return arr, ratio

    def _getMonthRatip(self, chagCapy, disg_arr, nowTime, arr, chag_arr, ratio):
        '''计算效率数据'''
        for i in range(len(chagCapy)):
            val = []
            obj = {"type_": "效率", "name": '', 'descr': '效率 %s' % (i + 1), 'unit': ''}
            for a in range(0, 12):
                if chag_arr[0][i][a] == 0 or disg_arr[0][i][a] == 0:
                    v = 0
                else:
                    v = float(disg_arr[0][i][a]) / chag_arr[0][i][a]
                month = '%s-%s' % (nowTime, fill0(a + 1))
                obj[month] = num_retain(v)
                val.append(v)
            if len(val) > 0:
                obj['max'] = num_retain(max(val), 4)
                # obj['min'] = num_retain(min(filter(lambda x: x > 0, val)),4)
                obj['min'] = num_retain(min(val), 4)
            else:
                obj['max'] = '0.0000'
                obj['min'] = '0.0000'
            obj['cost'] = num_retain(disg_arr[1][i] - chag_arr[1][i])
            ratio.append(val)
            arr.append(obj)
        return arr, ratio

    def _getDisgCap(self, disgCapy, db_con, am, days, arr, disg_arr):
        '''查询放电量数据
        disgCapy：放电数据集
        db_con：数据库session
        am:所有表数组
        days：天数数组
        arr：返回的数据集
        disg_arr：暂时存放放点数据的三维数组
        '''
        for dc in disgCapy:  # 放电量
            mm = real_data('measure', dc, 'db')
            if not mm['desc']:
                mm = real_data('measure', dc, 'ram')
                logging.info('scada is not %s' % (dc))
            obj = {"type_": "放电量", "name": dc, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}
            v, obj = self._getDataValue(db_con, am, dc, days, obj)
            obj['cost'] = num_retain(sum(v) * shouyi)
            arr.append(obj)
            #  存放放电数据
            disg_arr[0].append(v)
            disg_arr[1].append(sum(v) * shouyi)
        return arr, disg_arr

    def _getChagCap(self, chagCapy, db_con, am, days, arr, chag_arr):
        '''查询充电量数据'''
        for cg in chagCapy:  # 充电量
            mm = real_data('measure', cg, 'db')
            if not mm['desc']:
                mm = real_data('measure', cg, 'ram')
                logging.info('scada is not %s' % (cg))
            obj = {"type_": "充电量", "name": cg, 'descr': mm['desc'], 'unit': self._getUnit(mm['unit'])}
            v, obj = self._getDataValue(db_con, am, cg, days, obj)
            obj['cost'] = num_retain(sum(v) * chengben)
            arr.append(obj)
            #  存放充电数据
            chag_arr[0].append(v)
            chag_arr[1].append(sum(v) * chengben)
        return arr, chag_arr

    def _getRatip(self, chagCapy, disg_arr, days, arr, chag_arr, ratio):
        '''计算效率数据'''
        for i in range(len(chagCapy)):
            val = []
            obj = {"type_": "效率", "name": '', 'descr': '效率 %s' % (i + 1), 'unit': ''}
            for tim in days:
                ind = days.index(tim)
                if chag_arr[0][i][ind] == 0.0 or disg_arr[0][i][ind] == 0.0:
                    v = 0
                else:
                    v = float(disg_arr[0][i][ind]) / chag_arr[0][i][ind]

                obj[tim[5:]] = num_retain(v)
                val.append(v)
            if len(val) > 0:
                obj['max'] = num_retain(max(val), 4)
                obj['min'] = num_retain(min(val), 4)
            else:
                obj['max'] = '0.0000'
                obj['min'] = '0.0000'
            obj['cost'] = num_retain(disg_arr[1][i] - chag_arr[1][i])
            ratio.append(val)
            arr.append(obj)
        return arr, ratio

    def _getSocRatip(self, db_con, chagCapy, disg_arr, days, arr, chag_arr, ratio, am):
        '''计算效率数据'''
        for i in range(len(chagCapy)):
            name = chagCapy[i].replace('KWH_ChagCapyTotl', 'Sys_Soc')
            val = []
            obj = {"type_": "效率", "name": '', 'descr': '效率 %s' % (i + 1), 'unit': ''}
            for tim in days:
                ind = days.index(tim)
                startT = timeUtils.timeStrToTamp('%s 00:00:01' % tim)
                endT = timeUtils.timeStrToTamp('%s 23:59:59' % tim)
                sum1, sum2 = self._getValByNameOfDay(db_con, am, name, startT, endT, 1)

                if chag_arr[0][i][ind] == 0.0 or disg_arr[0][i][ind] == 0.0 or sum1 == 0 or sum2 == 0:
                    v = 0
                else:
                    v = disg_arr[0][i][ind] / chag_arr[0][i][ind] / sum1 * sum2
                    obj[tim[5:]] = num_retain(v, 4)
                val.append(v)
            if len(val) > 0:
                obj['max'] = num_retain(max(val), 4)
                obj['min'] = num_retain(min(val), 4)
            else:
                obj['max'] = '0.0000'
                obj['min'] = '0.0000'
            obj['cost'] = num_retain(disg_arr[1][i] - chag_arr[1][i])
            ratio.append(val)
            arr.append(obj)
        return arr, ratio

    def _getDataValue(self, db_con, am, name, days, obj):
        v = []
        for tim in days:
            startT = timeUtils.timeStrToTamp('%s 00:00:01' % tim)
            endT = timeUtils.timeStrToTamp('%s 23:59:59' % tim)
            val = self._getValByNameOfDay(db_con, am, name, startT, endT)
            v.append(val)
            obj[tim[5:]] = val
        if len(v) > 0:
            obj['max'] = max(v)
            obj['min'] = min(v)
        else:
            obj['max'] = 0
            obj['min'] = 0
        return v, obj

    def _getValByNameOfDay(self, db_con, hisTables, name, startT, endT, soc=0):
        '''获取某一个名字某一天的值'''
        data = []
        for table in hisTables:
            HisTable = HisACDMS(table)
            for db_c in db_con:
                values = db_c.query(HisTable.value).filter(HisTable.name == name,
                                                           HisTable.dts_s.between(startT, endT)).order_by(
                    HisTable.dts_s.asc()).all()
                for value in values:
                    data.append(value[0])
        if soc:  # 如果是计算soc的
            return list_soc_mm(data)
        else:
            return list_sum_mm(data, 1000)

    def _getValByNameMonth(self, db_conn, tablename, days, name):
        '''计算某个名字一月内的数据'''
        # 查询表明是否存在
        month_v = 0
        if _tableIsExist(db_conn, tablename):
            for tim in days:
                startT = timeUtils.timeStrToTamp('%s 00:00:01' % tim)
                endT = timeUtils.timeStrToTamp('%s 23:59:59' % tim)
                v = self._getValByNameOfDay(db_conn[1], [tablename], name, startT, endT)
                month_v = month_v + np.sum(v)
        return month_v

    def _getUnit(self, unit):
        if unit == 'KWH':
            unit = 'kWh'

        return unit

    def sys_availty(self, rrr, time_, volume):
        '''计算系统可用率'''
        if rrr:
            sum_1 = 0  # 累加值
            for l in rrr:
                if l.type_ == '1':
                    for i in eval(l.table_list):
                        star_time = i['star_time']  # 开始时间（年月日时分）
                        end_time = i['end_time']  # 结束时间（年月日时分）
                        outage_volume = i['outage_volume']  # 停运容量
                        time = timeUtils.timeSeconds((star_time + ':00'), (end_time + ':59'))
                        sum_1 = sum_1 + (float(outage_volume) * (time / 60))
            sum_ = (volume[0] * 1000) * time_
            sys_availty = 100 - ((sum_1 / sum_) * 100)  # 系统可用率

        else:
            sys_availty = 100
        sys_availty = str('%.2f' % sys_availty)

        return sys_availty

    def data_day_dongmu(self, ed=None, obj=None, st=None, name=None, t=None, z=None, obj_sys=None, vmax=None):
        '''系统，PCS东睦日数据'''
        dataP1, data2 = [], {}  #
        obj['time'] = []
        obj['value'] = []
        PCS_name = model_config.get('peizhi', name)  # name
        dm_table = HisDM('r_measure')
        values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(st, ed)).order_by(
            dm_table.time.asc()).all()
        timeall_, alldata, time_ = [], [], []  # 时间，所有数据
        if values:
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            obj_sys_list = []
            for e in range(dongmu_num):
                PCS_ = 'PCS%s' % (e + 1)
                d2 = []  # 有功
                for ii in alldata:
                    # index = alldata.index(ii)
                    if ii['device'] == PCS_:
                        value = float(ii[PCS_name])  #
                        if value < 0:
                            d2.append(0)
                        elif value > 1:
                            d2.append(1)
                        else:
                            d2.append(value)
                data12 = {}
                data12['time'] = timeall_
                data12['value'] = d2

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                if data12["time"]:
                    data22 = complete_data(data12, t)
                    list_time = []
                    if obj['time'] == []:  # 时间只加一次
                        if data22:
                            for i in data22['time']:
                                list_time.append(i[8:16])
                    obj['time'] = list_time
                for d in data22['value']:
                    try:
                        if math.isnan(d) is True:
                            indx = data22['value'].index(d)
                            data22['value'][indx] = 0
                    except:
                        break

                if obj_sys_list != []:
                    obj_sys_list = np.array(obj_sys_list).astype(float) + np.array(data22['value']).astype(float)
                else:
                    obj_sys_list = data22['value']

            if obj_sys_list != []:
                obj['value'] = np.round(obj_sys_list, 3).tolist()
        dongmu_session.close()

    def data_day(self, db, endTime, obj):
        '''系统，PCS其他电站日数据(调了小数表)(当日)'''
        obj['time'] = []
        obj['value'] = []
        conn = pool.connection()
        cursor = conn.cursor()

        sql = """SELECT window_start,{}
                FROM {}
                where day ='{}'
                and station_name ='{}'                                    
                order by window_start asc
                """.format('real_pw', 'dws_st_measure_win_15min', endTime, db)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        if result:
            for i in result:
                obj['time'].append(i['window_start'].strftime("%d %H:%M:%S"))
                obj['value'].append(i['real_pw'])


    def cu_chag_disg(self, chag_disg, obj,db):
        '''簇充放电量'''
        if chag_disg:
            dict_chag_disg = {}
            for s in chag_disg:
                if db !='dongmu':
                    if s['pcs_name'] not in dict_chag_disg.keys():
                        dict_chag_disg[s['pcs_name']] = {}
                        if s['cluster_cname'] not in dict_chag_disg[s['pcs_name']].keys():
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']] = {'chag': [], 'disg': []}
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['chag'].append(float(s['chag']))
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['disg'].append(float(s['disg']))
                    else:
                        if s['cluster_cname'] not in dict_chag_disg[s['pcs_name']].keys():
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']] = {'chag': [], 'disg': []}
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['chag'].append(float(s['chag']))
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['disg'].append(float(s['disg']))
                        else:
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['chag'].append(float(s['chag']))
                            dict_chag_disg[s['pcs_name']][s['cluster_cname']]['disg'].append(float(s['disg']))
                else:
                    if s[0] not in dict_chag_disg.keys():
                        dict_chag_disg[s[0]] = {}
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                    else:
                        if s[1] not in dict_chag_disg[s[0]].keys():
                            dict_chag_disg[s[0]][s[1]] = {'chag': [], 'disg': []}
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
                        else:
                            dict_chag_disg[s[0]][s[1]]['chag'].append(float(s[2]))
                            dict_chag_disg[s[0]][s[1]]['disg'].append(float(s[3]))
            for d in dict_chag_disg.keys():
                cu = []
                chag_arr = []
                disg_arr = []
                ratio = []
                cu_num = 0
                for ss in dict_chag_disg[d].keys():
                    cu_num += 1
                    chag_ = float('%.2f' % sum(dict_chag_disg[d][ss]['chag']))
                    disg_ = float('%.2f' % sum(dict_chag_disg[d][ss]['disg']))
                    if chag_ == 0:
                        ratio_ = 0
                    else:
                        ratio_ = float('%.3f' % ((disg_ / chag_) * 100))
                        if ratio_ > 100:
                            ratio_ = 100
                    cu.append({'cu_name': ss, 'chag': chag_, 'disg': disg_, 'ratio': ratio_, 'cu_num': cu_num})
                    chag_arr.append(chag_)
                    disg_arr.append(disg_)
                    ratio.append(ratio_)
                # list3 = sorted(cu, key=lambda x: x.get("chag"), reverse=True)
                obj.append(
                    {'pcs_name': d, 'chag_arr': chag_arr, 'disg_arr': disg_arr, 'ratio': ratio, 'cu_num': cu_num, })

    def cu_other_chag_disg(self, a, b, data, db, ed, list3, max, min, obj, st, tables):
        '''簇其他电站充放电量'''
        obj = []
        data = {}
        for table in tables.to_list():
            z = 1
            for d in a:
                pcs_name = 'PCS-%s' % (z)
                if pcs_name not in data:
                    data[pcs_name] = {}
                z += 1
                aa = 1
                for d1 in b:
                    cu_name = '电池簇%s' % (aa)
                    if cu_name not in data[pcs_name]:
                        data[pcs_name][cu_name] = {}
                    aa += 1
                    if db == 'baodian':
                        db_con = _return_db_con(db, d)  # 生成数据库连接
                    else:
                        db_con = _return_db_con(db, "%s%s" % (d, d1))  # 生成数据库连接
                    m = '%s%s%s' % (d, d1, list3[0])  # 累计充电电量的name
                    n = '%s%s%s' % (d, d1, list3[1])  # 累计放电电量的name
                    try:
                        v1 = _select_get_des_value(db_con, table, m, maxV=1000000, minV=0.1)  # 总充电量
                        value1 = v1['value']
                    except:
                        value1 = None
                    if value1 == None:
                        max_value = 0
                        min_value = 0
                    elif value1 != []:
                        max_value = max(value1)
                        min_value = min(value1)
                    else:
                        max_value = 0
                        min_value = 0

                    if 'chag' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['chag'] = []
                        data[pcs_name][cu_name]['chag'].append(max_value)
                        data[pcs_name][cu_name]['chag'].append(min_value)
                    else:
                        data[pcs_name][cu_name]['chag'].append(max_value)
                        data[pcs_name][cu_name]['chag'].append(min_value)
                    try:
                        v2 = _select_get_des_value(db_con, table, n, maxV=1000000, minV=0.1)  # 总放电量
                        value2 = v2['value']
                    except:
                        value2 = None
                    if value2 == None:
                        max_value = 0
                        min_value = 0
                    elif value2 != []:
                        max_value = max(value2)
                        min_value = min(value2)
                        # f_value = max_value - min_value
                        # data[pcs_name][cu_name].append({'disg': f_value})
                    else:
                        max_value = 0
                        min_value = 0
                    if 'disg' not in data[pcs_name][cu_name]:
                        data[pcs_name][cu_name]['disg'] = []
                        data[pcs_name][cu_name]['disg'].append('%.2f'%(max_value))
                        data[pcs_name][cu_name]['disg'].append('%.2f'%(min_value))
                    else:
                        data[pcs_name][cu_name]['disg'].append('%.2f'%(max_value))
                        data[pcs_name][cu_name]['disg'].append('%.2f'%(min_value))
                    db_con.close()
        return data, obj

    def _getDatas(self, bean, endTime, filter):
        all, total = [], 0
        stations = user_session.query(bean).filter(*filter).order_by(bean.ts.desc()).order_by(
            bean.event_id.asc()).all()
        if stations:
            for st in stations:
                all.append(eval(str(st)))
        if not endTime:
            endTime = timeUtils.getNewTimeStr()
        le = len(stations)
        if le > 0:
            all[-1]['sc'] = timeUtils.timeDiff(all[-1]['ts'], endTime)
            for i in range(le - 1):
                now_data = all[i]
                next_data = all[i + 1]
                if now_data['event_id'] == next_data['event_id']:
                    all[i]['sc'] = timeUtils.timeDiff(next_data['ts'], now_data['ts'])
                else:
                    all[i]['sc'] = timeUtils.timeDiff(next_data['ts'], endTime)
            all = sorted(all, key=lambda x: x['ts'], reverse=True)  # 倒序排
        return all, total

    def dense_rank(self, result_pcs):
        # 对结果进行排名
        # 提取 chag, disg, ratio 的值
        chag_values = [item['chag'] for item in result_pcs]
        disg_values = [item['disg'] for item in result_pcs]
        ratio_values = [item['ratio'] for item in result_pcs]

        # 对 chag, disg, ratio 进行降序排序
        sorted_chag = sorted(chag_values, reverse=True)
        sorted_disg = sorted(disg_values, reverse=True)
        sorted_ratio = sorted(ratio_values, reverse=True)

        # 为每个字典添加排名
        for i, item in enumerate(result_pcs):
            # 获取 chag 的排名
            cd_rank = sorted_chag.index(item['chag']) + 1
            item['cd_rank'] = cd_rank

            # 获取 disg 的排名
            fd_rank = sorted_disg.index(item['disg']) + 1
            item['fd_rank'] = fd_rank

            # 获取 ratio 的排名
            ra_rank = sorted_ratio.index(item['ratio']) + 1
            item['ra_rank'] = ra_rank

        return result_pcs


def _value_desc_fanyi(lang, descr, dtype):
    '''
    值描述翻译
    lang:目标语言
    descr:描述
    dtype:原始数据集
    '''
    desc = ''
    try:
        if lang == 'en':
            desc = dtype[descr][lang]
    except Exception as e:
        desc = translate_text(descr, 2)

    return desc


def _return_db_con_(db, psc_font_name):
    '''返回数据库链接（PCS）'''
    if db == 'baodian':
        return baodian_his[psc_font_name.split('.')[0]]
    else:
        if db == 'taicang' or db == 'halun':
            return db_his[db][0]
        elif db == 'guizhou':
            d = int(psc_font_name.split('.')[0][-2])
            return db_his[db][d - 1]
        elif db == 'ygqn':
            if 'ygqn.d' in psc_font_name:
                return db_his[db][1]
            else:
                return db_his[db][0]
        elif db == 'houma':
            d = psc_font_name.split('.')[0][-2:]
            if d == 'A1':
                return db_his[db][0]
            elif d == 'A2':
                return db_his[db][1]
            elif d == 'B1':
                return db_his[db][2]
            elif d == 'B2':
                return db_his[db][3]
        else:
            d = int(psc_font_name.split('.')[0][-1])
            return db_his[db][d - 1]
