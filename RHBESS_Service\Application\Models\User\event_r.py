#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:49:46
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-29 14:36:35


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.event import Event

class EventR(user_Base):
    u'事件记录表'
    __tablename__ = "r_event"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    value_descr = Column(VARCHAR(50), nullable=True,comment=u"值描述")
    value = Column(Float, nullable=False, comment=u"数值")
    event_id = Column(Integer,ForeignKey("t_event.id"), nullable=False,comment=u"事件配置表")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    ts = Column(DateTime, nullable=False,comment=u"数据时间")
    opt1 = Column(VARCHAR(50), nullable=True,comment=u"关联点1")
    opt2 = Column(VARCHAR(50), nullable=True,comment=u"关联点2")
    opt3 = Column(VARCHAR(50), nullable=True,comment=u"关联点3")
    opt4 = Column(VARCHAR(50), nullable=True,comment=u"关联点4")
    opt5 = Column(VARCHAR(50), nullable=True,comment=u"关联点5")
    opt6 = Column(VARCHAR(50), nullable=True,comment=u"关联点6")
    opt7 = Column(VARCHAR(50), nullable=True,comment=u"关联点7")
    opt8 = Column(VARCHAR(50), nullable=True,comment=u"关联点8")
    opt9 = Column(VARCHAR(50), nullable=True,comment=u"关联点9")
    opt10 = Column(VARCHAR(50), nullable=True,comment=u"关联点10")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
   
    event_r = relationship("Event", backref="event_r")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        event_descr = self.event_r.event_type.descr if self.event_r else ''
        point = self.event_r.point if  self.event_r else ''
        descr = self.event_r.descr if  self.event_r else ''
        bean = "{'id':%s,'value':'%s','event_id':'%s','event_descr':'%s','ts':'%s','op_ts':'%s','opt1':'%s','opt2':'%s','opt3':'%s','opt4':'%s','opt5':'%s','opt6':'%s','opt7':'%s',\
            'opt8':'%s','opt9':'%s','opt10':'%s','point':'%s','descr':'%s','value_descr':'%s'}" % (self.id,self.value,self.event_id,event_descr,self.ts,self.op_ts,self.opt1,self.opt2,
            self.opt3,self.opt4,self.opt5,self.opt6,self.opt7,self.opt8,self.opt9,self.opt10,point,descr,self.value_descr)
        return bean.replace("None",'')
    def deleteEventR(self,id):
        try:
            user_session.query(EventR).filter(EventR.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False