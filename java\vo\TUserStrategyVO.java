package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户策略VO
 */
@Data
@ApiModel("用户策略VO")
public class TUserStrategyVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("英文策略名称")
    private String enName;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("是否删除: 1-删除, 0-不删除")
    private Integer isDelete;

    @ApiModelProperty("是否删除名称")
    private String isDeleteName;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("是否使用名称")
    private String isUseName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
