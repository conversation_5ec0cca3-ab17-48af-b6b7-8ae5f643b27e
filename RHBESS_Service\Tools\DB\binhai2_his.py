#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-05 10:28:52
#@FilePath     : \RHBESS_Service\Tools\DB\binhai2_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:09:01

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


BINHAI2_HOSTNAME = model_config.get('mysql', "BINHAI2_HOSTNAME")
BINHAI2_PORT = model_config.get('mysql', "BINHAI2_PORT")
BINHAI2_DATABASE = model_config.get('mysql', "BINHAI2_DATABASE")
BINHAI2_USERNAME = model_config.get('mysql', "BINHAI2_USERNAME")
BINHAI2_PASSWORD = model_config.get('mysql', "BINHAI2_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BINHAI2_USERNAME,
    BINHAI2_PASSWORD,
    BINHAI2_HOSTNAME,
    BINHAI2_PORT,
    BINHAI2_DATABASE
)
binhai2_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_binhai2_session = scoped_session(sessionmaker(binhai2_engine,autoflush=True))

binhai2_Base = declarative_base(binhai2_engine)
binhai2_session = _binhai2_session()


SBINHAI2_HOSTNAME = model_config.get('mysql', "SBINHAI2_HOSTNAME")
SBINHAI2_PORT = model_config.get('mysql', "SBINHAI2_PORT")
SBINHAI2_DATABASE = model_config.get('mysql', "SBINHAI2_DATABASE")
SBINHAI2_USERNAME = model_config.get('mysql', "SBINHAI2_USERNAME")
SBINHAI2_PASSWORD = model_config.get('mysql', "SBINHAI2_PASSWORD")

shisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBINHAI2_USERNAME,
    SBINHAI2_PASSWORD,
    SBINHAI2_HOSTNAME,
    SBINHAI2_PORT,
    SBINHAI2_DATABASE
)
sbinhai2_engine = create_engine(shisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_sbinhai2_session = scoped_session(sessionmaker(sbinhai2_engine,autoflush=True))

sbinhai2_Base = declarative_base(sbinhai2_engine)
sbinhai2_session = _sbinhai2_session()




