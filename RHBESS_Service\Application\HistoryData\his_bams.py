#!/usr/bin/env python
# coding=utf-8
# @Information:  bams历史数据
import ast
import json

import tornado.web
from Application.Models.base_handler import BaseHandler
from Tools.Cfg.DB_his import get_dhis
from Tools.DB.houma_bms_his import houma_a1_g_bms_session, houma_b2_g_bms_session, houma_a2_g_bms_session, \
    houma_b1_g_bms_session
from Tools.DB.zgtian_bms_his import zgtian1_bms_session, zgtian2_bms_session, zgtian3_bms_session, zgtian4_bms_session
from Tools.Utils.time_utils import timeUtils
from Application.Models.His.r_ACDMS import HisACDMS, HisDM
from Tools.DB.binhai_bms_his import binhai_session1, binhai_session2
from Tools.DB.taicang_bms_his import taicang_session1
from Tools.DB.ygzhen_bms_his import ygzhen_session1, ygzhen_session2
from Tools.Utils.num_utils import *
from Tools.DB.mysql_his import dongmu_session
from Application.HistoryData.station_unit_cluster import station_unit_cluster_dict
from Application.Models.User.data_item import DataItemV2
from Tools.DB.mysql_user import user_session
import numpy as np
import pandas as pd
import time,datetime
from sqlalchemy import func
import pymysql
from dbutils.persistent_db import PersistentDB
from Application.Cfg.dir_cfg import model_config as model_config_base

model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
device_arr = ast.literal_eval(model_config.get('peizhi', 'device_arr'))  # 所有设备筛选
piece_arr = ast.literal_eval(model_config.get('peizhi', 'piece_arr'))  # 所有电池簇
device_arr_en = ast.literal_eval(model_config.get('peizhi', 'device_arr_en'))  # 所有设备筛选
piece_arr_en = ast.literal_eval(model_config.get('peizhi', 'piece_arr_en'))  # 所有电池簇

bmsdb, baodian_siss = get_dhis('his_bams')
db_ = get_dhis('his_data_query')
ygqn={"1":["G1","G2"],"2":["G3","G4"],"3":["G5","G6"],"4":["G7","G8"],"5":["G9","G10"],"6":["G11"]}
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']
# 仪表盘，实时值
real_char_tv = "Soc"  # 堆储能单元soc
real_char_tv_bd = ".H.M.Soc"  # 保电堆储能单元soc
real_char_rv = ["SgVmax", "SgVmin", "SgTmax", "SgTmin"]  # 簇信息
real_char_rv_bd = ["HestCelVol", "LestCelVol", "HestCelTmp", "LestCelTmp"]  # 保电簇信息
real_char_rv_gz = ["MxSigVol", "MiSigVol", "MxSigTem", "MiSigTem"]  # 贵州簇信息

# 具体展示参数
paras_obj = {'paras_0': ["SgVmax", "SgTmax", "SgVmin", "SgTmin"],
             'paras_1': ["SgVmax", "SgTmax"],
             'paras_2': ["SgVmin", "SgTmin"],
             'paras_3': ["SgVavg", "SgTavg"]}  # 极差，最大值；最小值;平均值

paras_obj_bd = {'paras_0': ["MaxCelVDif", "MaxCelTDif"],
                'paras_1': ["HestCelVol", "HestCelTmp"],
                'paras_2': ["LestCelVol", "LestCelTmp"],
                'paras_3': ["AvegCelVol", "AvegCelTmp"]}  # 极差，最大值；最小值；平均值

paras_obj_gz = {'paras_0': ["MxSigVol", "MiSigVol", "MxSigTem", "MiSigTem"],
                'paras_1': ["MxSigVol", "MxSigTem"],
                'paras_2': ["MiSigVol", "MiSigTem"],
                'paras_3': ["AvgSigVol", "AvgSigTem"]}  # 极差，最大值；最小值；平均值


pool = PersistentDB(pymysql, 10,**{
            "host": model_config_base.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config_base.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config_base.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config_base.get('mysql', "IDCD_DATABASE"),  # 数据库名称
            "port":  int(model_config_base.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })


class HisBamsInterface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        try:
            db = self.get_argument('db', None)
            logging.info('db:%s' % (db))
            if db not in device_arr.keys():
                lang = self.get_argument('lang', None)  # 英文网址
                if lang == 'en':
                    return self.customError("Please specify power station！")
                else:
                    return self.customError("请指定电站！")
            if kt == 'AllDevices':  # 所有堆
                lang = self.get_argument('lang', None)  # 英文网址
                if lang == 'en':
                    return self.returnTypeSuc(device_arr_en[db])
                else:
                    return self.returnTypeSuc(device_arr[db])
            elif kt == 'BaseField':  # 所有簇
                lang = self.get_argument('lang', None)  # 英文网址
                if lang == 'en':
                    return self.returnTypeSuc(piece_arr_en[db])
                else:
                    return self.returnTypeSuc(piece_arr[db])
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            return self.requestError()

    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        try:
        # if 1:
            if kt == 'BamsDatas':
                db = self.get_argument('db', None)
                bams = self.get_argument('dui', [])  # 电池堆
                bank = self.get_argument('cu', [])  # 电池簇
                bamsName = self.get_argument('duiName', [])  # 电池堆名称
                bankName = self.get_argument('cuName', [])  # 电池簇名称
                paras = int(self.get_argument('paras', 0))  # 展示参数  0极差1最大2最小3平均值
                chartType = int(self.get_argument('chartType', 0))  # 图表形式  0折线1热力图
                time_ = self.get_argument('time', [])  # 时间数组
                lang = self.get_argument('lang', None)  # 英文网址
                logging.info('bams:%s,bank:%s,paras:%s,chartType:%s,time_:%s,db:%s,bamsName:%s,bankName:%s' % (
                bams, bank, paras, chartType, time_, db, bamsName, bankName))
                if db not in device_arr.keys():
                    if lang == 'en':
                        return self.customError("Please specify power station！")
                    else:
                        return self.customError("请指定电站！")
                bams = eval(bams)
                bank = eval(bank)
                if len(bams) > 5 :
                    if lang == 'en':
                        return self.customError('Select up to 5 units, please re-select!')
                    else:
                        return self.customError('最多选择5个单元，请重新选择！')
                if len(bank) > 10:
                    if lang == 'en':
                        return self.customError('Select up to 10 cluster, please re-select!')
                    else:
                        return self.customError('最多选择10个簇，请重新选择！')
                time_ = eval(time_)
                bamsName = eval(bamsName)
                bankName = eval(bankName)
                if not bams or not bank or not bankName or not bamsName:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if db != 'dongmu':
                    if ('all' in bams and len(bams) > 1) or ('all' in bank and len(bank) > 1):
                        if lang == 'en':
                            return self.customError("Illegal entry")
                        else:
                            return self.customError('非法入参')
                select_history = False
                if db == 'ygqn':
                    _bamsName = ','.join(bamsName)
                    if 'D' in _bamsName and ('A' in _bamsName or 'B' in _bamsName or 'C' in _bamsName):
                        if lang == 'en':
                            return self.customError("Illegal entry， Separate query in Zone D")
                        else:
                            return self.customError('非法入参, D区单独查询')
                    else:
                        if 'D' in _bamsName:
                            select_history = True
                startTime = time_[0]
                endTime = time_[1]
                timelen = timeUtils.timeSeconds(startTime, endTime)
                coun = timelen // 50  # 秒
                jiange = ''
                if coun > 60:
                    coun = coun // 60  # 分钟
                    if coun > 60:
                        coun = coun // 60  # 小时
                        jiange = '%sH' % coun
                    else:
                        jiange = '%sT' % coun
                else:
                    jiange = '%sS' % coun
                if timelen > 259200:  # 72小时
                    if lang == 'en':
                        return self.customError('Time span less than three days!')
                    else:
                        return self.customError('时间跨度请小于三天！')
                elif timelen < 0:
                    if lang == 'en':
                        return self.customError('The time span is unreasonable!')
                    else:
                        return self.customError('时间跨度不合理！')
                if startTime <= '2023-02-01 00:00:00':
                    if lang == 'en':
                        return self.customError('Too early, no data!')
                    else:
                        return self.customError('时间太早，无数据！')
                if 'all' in bams:
                    if lang == 'en':
                        return self.customError('Select up to 5, please re-select!')
                    else:
                        return self.customError('最多选择5个，请重新选择！')
                if 'all' in bank:
                    if lang == 'en':
                        return self.customError('Select up to 10, please re-select!')
                    else:
                        return self.customError('最多选择10个，请重新选择！')
                if not station_unit_cluster_dict.get(db):
                    if lang == 'en':
                        return self.customError('Configure the cluster information and try again!')
                    else:
                        return self.customError('请电站簇配置信息后重试！')
                if db == "dongmu":
                    real_data = self._real_data_1(db, bamsName, bankName)  # 调用获取实时数据，json格式
                else:
                    if select_history:
                        real_data = self._real_data_yq_d(db, bams, bank)  # 调用获取实时数据，json格式
                    else:
                        real_data = self._real_data_dwd(db, bamsName, bankName, station_unit_cluster_dict.get(db))
                    # real_data = self._real_data(db, bams, bank)  # 调用获取实时数据，json格式
                st = startTime
                ed = endTime

                if db == 'baodian':
                    ch_arr = paras_obj_bd['paras_%s' % paras]
                    real_arr = real_char_tv_bd
                    his_datas = self._his_data_dwd(db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr,
                                               paras, real_arr, jiange, station_unit_cluster_dict.get(db))  # 调用获取历史数据
                elif db == 'dongmu':
                    ch_arr = paras_obj_bd['paras_%s' % paras]
                    real_arr = real_char_tv_bd
                    his_datas = self._his_data_1(db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr,
                                                 paras, real_arr, jiange)  # 调用获取历史数据
                elif db == 'guizhou':
                    ch_arr = real_char_tv
                    real_arr = real_char_rv_gz
                    his_datas = self._his_data_dwd(db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr,
                                               paras, real_arr, jiange, station_unit_cluster_dict.get(db))  # 调用获取历史数据
                else:
                    ch_arr = paras_obj['paras_%s' % paras]
                    real_arr = real_char_tv
                    if select_history:
                        his_datas = self._his_data_dwd_yq_d(db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr,
                                               paras, real_arr, '5T')  # 调用获取实时数据，json格式
                    else:
                        his_datas = self._his_data_dwd(db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr,
                                               paras, real_arr, jiange, station_unit_cluster_dict.get(db))  # 调用获取历史数据
                return self.returnTypeSuc({'real_data': real_data, "his_data": his_datas})
            elif kt == 'BamsSpeTimeData':  # 电芯指定时刻数据
                time_ = self.get_argument('time', '')  # 时间数组
                db = self.get_argument('db', None)
                name = self.get_argument('name', '')  # 储能单元1-簇2
                logging.info('time:%s,db:%s,name:%s' % (time_, db, name))
                v_names, t_names = [], []
                dev_name = name.split('-')
                lang = self.get_argument('lang', None)  # 英文网址
                if lang == 'en':
                    for o in device_arr[db]:
                        if o['name'] == dev_name[0]:
                            bms = o['value']
                    for o in piece_arr[db]:
                        if o['name'] == dev_name[1]:
                            bank = o['value']
                else:
                    for o in device_arr[db]:
                        if o['name'] == dev_name[0]:
                            bms = o['value']
                    for o in piece_arr[db]:
                        if o['name'] == dev_name[1]:
                            bank = o['value']
                db_con = _return_db_con(db, bms)  # 数据库连接
                if db == 'baodian':  # 保电项目
                    for p in range(1, 27):  # 保电每簇26个包
                        for i in range(1, 17):  # 每个包16个单体
                            v_names.append('%s.R%s.P%s.CelVol%s' % (bms, bank[-2], p, i))
                        for j in range(1, 9):
                            t_names.append('%s.R%s.P%s.CelTmp%s' % (bms, bank[-2], p, j))
                elif db == 'binhai' or db == 'taicang':  # 滨海 太仓项目
                    for i in range(1, 277):  # 太仓、滨海276
                        n = fill0(i, 3)  # 补0
                        v_names.append('%s%sSgV%s' % (bms, bank, n))
                    for j in range(1, 139):
                        m = fill0(j, 3)  # 补0
                        t_names.append('%s%sSgT%s' % (bms, bank, m))
                elif db == 'ygzhen' or db == 'zgtian':
                    for i in range(1, 241):  # 永臻240
                        n = fill0(i, 3)  # 补0
                        v_names.append('%s%sSgV%s' % (bms, bank, n))
                    for j in range(1, 121):
                        m = fill0(j, 3)  # 补0
                        t_names.append('%s%sSgT%s' % (bms, bank, m))
                elif db == 'houma':
                    for i in range(1, 401):  # 侯马400
                        n = fill0(i, 3)  # 补0
                        v_names.append('%s%sSgV%s' % (bms, bank, n))
                    for j in range(1, 401):
                        m = fill0(j, 3)  # 补0
                        t_names.append('%s%sSgT%s' % (bms, bank, m))
                now_time = timeUtils.getNewTimeStr()[:14] + '00:00'  # 取整点
                l_hour = timeUtils.getBeforeHouseStr()[:14] + '00:00'  # 上一小时时间格式
                t1 = 'r_measure%s' % (now_time[:13].replace('-', '').replace(' ', ''))
                t2 = 'r_measure%s' % (l_hour[:13].replace('-', '').replace(' ', ''))
                t3 = 'r_measure%s' % (time_[:7].replace('-', ''))
                if time_ >= now_time:
                    table = t1
                elif time_ >= l_hour:
                    table = t2
                else:
                    table = t3
                st = timeUtils.timeStrToTamp(time_)  # 转时间戳
                logging.info('name pinjie table is %s SUCCESS  time is %s' % (table, timeUtils.getNewTimeStr()))
                HisTable = HisACDMS(table)
                HisTable3 = HisACDMS(t3)
                data = [{}]
                for v_n in v_names:
                    ind = v_names.index(v_n)
                    obj = {'title': fill0(ind + 1, 3)}
                    value = db_con.query(HisTable.value.label('value')).filter(HisTable.name == v_n,
                                                                               HisTable.value.between(0.1, 65),
                                                                               HisTable.dts_s <= st).order_by(
                        HisTable.dts_s.desc()).first()  # 查询当前时间表
                    if not value:
                        value = db_con.query(HisTable3.value.label('value')).filter(HisTable3.name == v_n,
                                                                                    HisTable3.value.between(0.1, 65),
                                                                                    HisTable3.dts_s <= st).order_by(
                            HisTable3.dts_s.desc()).first()
                    if value:
                        obj['V'] = value[0]
                    else:
                        obj['V'] = '--'
                    data.append(obj)
                logging.info('V select SUCCESS  time is %s' % timeUtils.getNewTimeStr())
                for t_n in t_names:
                    ind = t_names.index(t_n) + 1
                    o1 = data[ind * 2 - 1]  # 温度是两个电芯共享一个
                    o2 = data[ind * 2]
                    value = db_con.query(HisTable.value.label('value')).filter(HisTable.name == t_n,
                                                                               HisTable.value.between(0.1, 65),
                                                                               HisTable.dts_s <= st).order_by(
                        HisTable.dts_s.desc()).first()  # 查询当前时间表
                    if not value:
                        value = db_con.query(HisTable3.value.label('value')).filter(HisTable3.name == t_n,
                                                                                    HisTable3.value.between(0.1, 65),
                                                                                    HisTable3.dts_s <= st).order_by(
                            HisTable3.dts_s.desc()).first()
                    if value:
                        o1['T'] = value[0]
                        o2['T'] = value[0]
                    else:
                        o1['T'] = '--'
                        o2['T'] = '--'
                logging.info('T select SUCCESS  time is %s' % timeUtils.getNewTimeStr())
                db_con.close()
                del data[0]
                return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            taicang_session1.close()
            binhai_session1.close()
            binhai_session2.close()
            ygzhen_session1.close()
            ygzhen_session2.close()
            zgtian1_bms_session.close()
            zgtian2_bms_session.close()
            zgtian3_bms_session.close()
            zgtian4_bms_session.close()
            houma_a1_g_bms_session.close()
            houma_a2_g_bms_session.close()
            houma_b1_g_bms_session.close()
            houma_b2_g_bms_session.close()
            for b in baodian_siss.values():
                b.close()
            logging.error(E)
            return self.requestError()


    def _his_data_dwd_yq_d(self, db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr, paras, real_arr,
                  jiange):
        '''获取历史数据'''

        table = 'ods_r_measure1'

        timeall, soc_data, V_data, T_data = [], {"data": [], 'name': []}, {"data": [], 'name': []}, {"data": [],
                                                                                                     'name': []}

        if '簇1' not in bankName:
            return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}
        db_con = db_[db][1][1]  # 获取阳泉D区数据库连接
        HisTable_m = HisACDMS(table)
        names = ['RkSoc', 'SgMxVl', 'SgMiVl', 'SgVlAvg', 'SgMxTm', 'SgMiTm', 'SgTmAvg']
        try:
            for n in bams:
                name_list = []
                for y in names:
                    name_list.append(n + y)
                d_i = bams.index(n)  # 堆下标
                values = db_con.query(HisTable_m.name, HisTable_m.value, HisTable_m.dts_s).filter(
                    HisTable_m.name.in_(name_list),
                    HisTable_m.dts_s >= st, HisTable_m.dts_s <= ed,
                    HisTable_m.value < 101).order_by(
                    HisTable_m.dts_s.asc()).all()
                data = {'RkSoc': {'time': [], 'value': []}, 'SgMxVl': {'time': [], 'value': []}, 'SgMiVl': {'time': [], 'value': []}, 'SgMxTm': {'time': [], 'value': []}, 'SgMiTm': {'time': [], 'value': []},
                        'SgVlAvg': {'time': [], 'value': []},'SgTmAvg': {'time': [], 'value': []}}

                for val in values:
                    k = val[0].split('.')[-1]
                    if isinstance(val[2], datetime.datetime):
                        data[k]['time'].append(val[2].strftime("%Y-%m-%d %H:%M:%S"))
                    else:
                        data[k]['time'].append(val[2])
                    data[k]['value'].append(val[1])
                for k, d in data.items():
                    if d['time']:  # 有值
                        d['time'].insert(0, startTime)
                        d['value'].insert(0, d['value'][0])
                        d['time'].append(endTime)
                        d['value'].append(d['value'][-1])
                        df = pd.DataFrame(d)
                        df = df.drop_duplicates(subset=["time"], keep="first")
                        # 转换回字典
                        d["time"] = df["time"].tolist()
                        d["value"] = df["value"].tolist()
                        data[k] = complete_data(d, jiange)
                    else:
                        pass

                timeall = data['RkSoc']['time']
                if timeall:
                    T_data['name'].append(bamsName[d_i] + '-簇1')
                    V_data['name'].append(bamsName[d_i] + '-簇1')

                soc_data['data'].append(data['RkSoc']['value']) if data['RkSoc']['value'] else soc_data['data'].append('--' for i in range(len(timeall)))
                soc_data['name'].append(bamsName[d_i])
                # for i in range(len(timeall)):
                if paras == 0:  # 极差
                    max_v, min_v = len(data['SgMxVl']['value']), len(data['SgMiVl']['value'])
                    max_t, min_t = len(data['SgMxTm']['value']), len(data['SgMxTm']['value'])
                    V_data['data'].append([round(data['SgMxVl']['value'][i] - data['SgMiVl']['value'][i], 2) if i < max_v and i < min_v else '--' for i in range(len(timeall))])
                    T_data['data'].append([round(data['SgMxTm']['value'][i] - data['SgMiTm']['value'][i], 2) if i < max_t and i < min_t else '--' for i in range(len(timeall))])
                elif paras == 1:  # 最大值
                    V_data['data'].append(data['SgMxVl']['value']) if data['SgMxVl']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                    T_data['data'].append(data['SgMxTm']['value']) if data['SgMxTm']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                elif paras == 2:  # 最小值
                    V_data['data'].append(data['SgMiVl']['value']) if data['SgMiVl']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                    T_data['data'].append(data['SgMiTm']['value']) if data['SgMiTm']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                elif paras == 3:  # 平均值
                    V_data['data'].append(data['SgVlAvg']['value']) if data['SgVlAvg']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                    T_data['data'].append(data['SgTmAvg']['value']) if data['SgTmAvg']['value'] else V_data['data'].append('--' for i in range(len(timeall)))
                else:
                    pass
        except Exception as e:
            logging.error(e)
            db_con.close()
            return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}
        finally:
            db_con.close()


        return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}


    def _his_data_dwd(self, db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr, paras, real_arr,
                  jiange, station_dict):
        '''获取历史数据'''
        timeall, soc_data, V_data, T_data = [], {"data": [], 'name': []}, {"data": [], 'name': []}, {"data": [], 'name': []}

        unit_list = [-1]  # 防止查询异常
        cluster_list = [-1]
        if db == 'taicgxr':  # 单独处理：有两台bms
            for d in bamsName:
                d = int(d.split('储能单元')[1])
                unit_list.append(d)
                unit_list.append(d + 1)
                for b in bankName:
                    b = int(b.split('簇')[1])
                    if station_dict.get(d):
                        if len(station_dict.get(d)) > b-1:
                            cluster_list.append(station_dict.get(d)[b-1])
                        else:
                            cluster_list.append('--')
                        if len(station_dict.get(d+1)) > b - 1:
                            cluster_list.append(station_dict.get(d+1)[b - 1])
                        else:
                            cluster_list.append('--')
        else:
            for d in bamsName:
                if db != 'ygqn':
                    d = int(d.split('储能单元')[1])
                else:
                    # 阳泉单独处理分区
                    bams_list = d.split('储能单元')
                    if bams_list[0] == 'A区':
                        d = int(bams_list[1])
                    elif bams_list[0] == 'B区':
                        d = int(bams_list[1]) + 40
                    else:
                        d = int(bams_list[1]) + 80
                unit_list.append(d)
                for b in bankName:
                    b = int(b.split('簇')[1])
                    if station_dict.get(d):
                        if len(station_dict.get(d)) > b - 1:
                            cluster_list.append(station_dict.get(d)[b - 1])
                        else:
                            cluster_list.append('--')

        soc_sql = f"""SELECT
                            soc,
                            start_time,
                            unit_rk
                        FROM
                            `dwd_rhyc_ES_station`.`dwd_unit_measure_5min` 
                        WHERE
                            `station_name` = '{db}' 
                            AND start_time BETWEEN '{st}' 
                            AND '{ed}' 
                            AND unit_rk IN {tuple(unit_list)}
                            AND soc < 101 
                        ORDER BY
                            start_time ASC"""

        conn = pool.connection()
        cursor = conn.cursor()

        paras_sql = f"""SELECT
                                max_vol, min_vol, max_temp, min_temp, avg_vol, avg_temp,
                                start_time,
                                unit_cluster_rk, unit_rk
                            FROM
                                `dwd_rhyc_ES_station`.`dwd_bc_measure_5min` 
                            WHERE
                                `station_name` = '{db}' 
                                 AND start_time BETWEEN '{st}' 
                                 AND '{ed}' 
                                AND unit_cluster_rk IN {tuple(cluster_list)}
                            ORDER BY
                                 start_time ASC"""

        try:
            cursor.execute(soc_sql)
            soc_result = cursor.fetchall()
            cursor.execute(paras_sql)
            paras_result = cursor.fetchall()
            data = {}
            paras_data = {}
            if db != 'ygqn':
                for val in soc_result:
                    k = f"储能单元{val['unit_rk']}"
                    if data.get(k):
                        data[k]['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
                        data[k]['value'].append(val['soc'])
                    else:
                        data[k] = {
                            'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                            'value': [val['soc']]
                    }

                for val in paras_result:
                    cluster = station_dict.get(val['unit_rk'])
                    if cluster:
                        index = np.where(np.array(cluster) == val['unit_cluster_rk'])[0]
                        if len(index) > 0:
                            k = f"储能单元{val['unit_rk']}-簇{index[0] + 1}"
                            if paras_data.get(k):
                                if paras_data.get(k):
                                    paras_data[k]['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
                                    paras_data[k]['value'].append({'max_vol': val['max_vol'], 'min_vol': val['min_vol'], 'max_temp': val['max_temp'],
                                                   'min_temp': val['min_temp'], 'avg_vol': val['avg_vol'], 'avg_temp': val['avg_temp']})
                                else:
                                    paras_data[k] = {
                                        'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                                        'value': [{'max_vol': val['max_vol'], 'min_vol': val['min_vol'], 'max_temp': val['max_temp'],
                                                   'min_temp': val['min_temp'], 'avg_vol': val['avg_vol'], 'avg_temp': val['avg_temp']}]
                                    }
                            else:
                                paras_data[k] = {
                                    'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                                    'value': [{'max_vol': val['max_vol'], 'min_vol': val['min_vol'], 'max_temp': val['max_temp'],
                                                   'min_temp': val['min_temp'], 'avg_vol': val['avg_vol'], 'avg_temp': val['avg_temp']}]
                                }
            else:
                for val in soc_result:
                    if val['unit_rk'] < 41:
                        k = f"A区储能单元{val['unit_rk']}"
                    elif 40 < val['unit_rk'] < 81:
                        k = f"B区储能单元{val['unit_rk']-40}"
                    else:
                        k = f"C区储能单元{val['unit_rk']-80}"
                    if data.get(k):
                        data[k]['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
                        data[k]['value'].append(val['soc'])
                    else:
                        data[k] = {
                            'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                            'value': [val['soc']]
                        }

                for val in paras_result:
                    cluster = station_dict.get(val['unit_rk'])
                    if cluster:
                        index = np.where(np.array(cluster) == val['unit_cluster_rk'])[0]
                        if len(index) > 0:
                            if val['unit_rk'] < 41:
                                k = f"A区储能单元{val['unit_rk']}-簇{index[0] + 1}"
                            elif 40 < val['unit_rk'] < 81:
                                k = f"B区储能单元{val['unit_rk'] - 40}-簇{index[0] + 1}"
                            else:
                                k = f"C区储能单元{val['unit_rk'] - 80}-簇{index[0] + 1}"
                            # k = f"储能单元{val['unit_rk']}-簇{index[0] + 1}"
                            if paras_data.get(k):
                                if paras_data.get(k):
                                    paras_data[k]['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
                                    paras_data[k]['value'].append({'max_vol': val['max_vol'], 'min_vol': val['min_vol'],
                                                                   'max_temp': val['max_temp'],
                                                                   'min_temp': val['min_temp'],
                                                                   'avg_vol': val['avg_vol'],
                                                                   'avg_temp': val['avg_temp']})
                                else:
                                    paras_data[k] = {
                                        'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                                        'value': [{'max_vol': val['max_vol'], 'min_vol': val['min_vol'],
                                                   'max_temp': val['max_temp'],
                                                   'min_temp': val['min_temp'], 'avg_vol': val['avg_vol'],
                                                   'avg_temp': val['avg_temp']}]
                                    }
                            else:
                                paras_data[k] = {
                                    'time': [val['start_time'].strftime("%Y-%m-%d %H:%M:%S")],
                                    'value': [{'max_vol': val['max_vol'], 'min_vol': val['min_vol'],
                                               'max_temp': val['max_temp'],
                                               'min_temp': val['min_temp'], 'avg_vol': val['avg_vol'],
                                               'avg_temp': val['avg_temp']}]
                                }
            exclude_keys = ['null', 'None', None, '--']
            res = {}
            if paras == 0:  # 极差
                for k, v in paras_data.items():  # 簇, data
                    res[k] = {'T': [], 'V': []}
                    for info in v['value']:
                        if info.get('max_temp') not in exclude_keys and info.get('min_temp') not in exclude_keys:
                            res[k]['T'].append(round(info.get('max_temp') - info.get('min_temp'), 2))
                        else:
                            res[k]['T'].append('--')
                        if info.get('max_vol') not in exclude_keys and info.get('min_vol') not in exclude_keys:
                            res[k]['V'].append(round(info.get('max_vol') - info.get('min_vol'), 2))
                        else:
                            res[k]['V'].append('--')

            elif paras == 1:  # 最大值
                for k, v in paras_data.items():  # 簇, data
                    res[k] = {'T': [], 'V': []}
                    for info in v['value']:
                        if info.get('max_temp') not in exclude_keys:
                            res[k]['T'].append(round(info.get('max_temp'), 2))
                        else:
                            res[k]['T'].append('--')
                        if info.get('max_vol') not in exclude_keys:
                            res[k]['V'].append(round(info.get('max_vol'), 2))
                        else:
                            res[k]['V'].append('--')
            elif paras == 2:  # 最小值
                for k, v in paras_data.items():  # 簇, data
                    res[k] = {'T': [], 'V': []}
                    for info in v['value']:
                        if info.get('min_temp') not in exclude_keys:
                            res[k]['T'].append(round(info.get('min_temp'), 2))
                        else:
                            res[k]['T'].append('--')
                        if info.get('min_vol') not in exclude_keys:
                            res[k]['V'].append(round(info.get('min_vol'), 2))
                        else:
                            res[k]['V'].append('--')
            elif paras == 3:  # 平均值
                for k, v in paras_data.items():  # 簇, data
                    res[k] = {'T': [], 'V': []}
                    for info in v['value']:
                        if info.get('avg_vol') not in exclude_keys:
                            res[k]['T'].append(round(info.get('avg_vol'), 2))
                        else:
                            res[k]['T'].append('--')
                        if info.get('avg_temp') not in exclude_keys:
                            res[k]['V'].append(round(info.get('avg_temp'), 2))
                        else:
                            res[k]['V'].append('--')
            else:
                return []
            if db == 'taicgxr':
                for name in bamsName:
                    for i in range(2):  # 太仓鑫融单独处理
                        d = int(name.split('储能单元')[1])
                        d = f"储能单元{d+i}"
                        if data.get(d):
                            _data = data.get(d)
                            timeall = _data['time']
                            soc_data['name'].append(name+f'-BMS{i+1}')
                            soc_data['data'].append(_data['value'])
                        for bank in bankName:
                            b = int(bank.split('簇')[1])
                            k = f'{d}-簇{b}'
                            res_k = f'{name}-BMS{i+1}-簇{b}'
                            if res.get(k):
                                V_data['name'].append(res_k)
                                V_data['data'].append(res[k]['V'])
                                T_data['name'].append(res_k)
                                T_data['data'].append(res[k]['T'])
            else:
                for name in bamsName:
                    d = name
                    if data.get(d):
                        _data = data.get(d)
                        timeall = _data['time']
                        soc_data['name'].append(name)
                        soc_data['data'].append(_data['value'])
                    for bank in bankName:
                        b = int(bank.split('簇')[1])
                        k = f'{d}-簇{b}'
                        if res.get(k):
                            V_data['name'].append(k)
                            V_data['data'].append(res[k]['V'])
                            T_data['name'].append(k)
                            T_data['data'].append(res[k]['T'])
        except Exception as e:
            logging.error(e)
            return []
        finally:
            cursor.close()
            conn.close()

        return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}

    def _his_data(self, db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr, paras, real_arr,
                  jiange):
        '''获取历史数据'''
        table = 'ods_r_measure1'
        timeall, soc_data, V_data, T_data = [], {"data": [], 'name': []}, {"data": [], 'name': []}, {"data": [],
                                                                                                     'name': []}
        for d in bams:
            d_i = bams.index(d)  # 堆下标
            if db not in exclude_station:  # tcxr单独处理
                db_con = _return_db_con(db, d)
                for bms in range(1, 3):  # 两个储能单元
                    soc_name = f'{d}BMS{bms}.{real_arr}'
                    value = _select_get_his_value(db_con, table, soc_name, st, ed, startTime, endTime, jiange,
                                                  101)  # 电池堆的soc值
                    soc_data['name'].append(bamsName[d_i] + f'-BMS{bms}')
                    soc_data['data'].append(value['value'])
            else:
                if db == 'guizhou':
                    soc_name = '%s%s' % (d, ch_arr)

                else:
                    soc_name = '%s%s' % (d, real_arr)
                db_con = _return_db_con(db, d)  # 获取具体数据库链接
                value = _select_get_his_value(db_con, table, soc_name, st, ed, startTime, endTime, jiange,
                                              101)  # 电池堆的soc值
                soc_data['name'].append(bamsName[d_i])
                soc_data['data'].append(value['value'])
            if db == 'baodian' or (db != 'baodian' and paras != 0):  # 保电项目 或者不是保电不是极差
                for i in range(len(ch_arr)):  # 变量名
                    for c in bank:  # 具体簇
                        c_i = bank.index(c)  # 簇下标
                        n1 = '%s%s%s' % (d, c, ch_arr[i])
                        v = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, jiange)
                        if i == 0:  # 电压
                            V_data['name'].append('%s-%s' % (bamsName[d_i], bankName[c_i]))
                            V_data['data'].append(v['value'])
                        else:
                            T_data['name'].append('%s-%s' % (bamsName[d_i], bankName[c_i]))
                            T_data['data'].append(v['value'])
                        if not timeall and v['time']:
                            timeall = v['time']
            else:  # 极差值
                for c in bank:  # 具体簇
                    c_i = bank.index(c)  # 簇下标
                    if db not in exclude_station:  # tcxr单独处理
                        for bms in range(1, 3):  # 两个储能单元
                            n1 = f'{d}BMS{bms}.{c}{ch_arr[0]}'  # 最大电压
                            n2 = f'{d}BMS{bms}.{c}{ch_arr[1]}'  # 最大温度
                            n3 = f'{d}BMS{bms}.{c}{ch_arr[2]}'  # 最小电压
                            n4 = f'{d}BMS{bms}.{c}{ch_arr[3]}'  # 最小温度
                            v1 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, jiange)
                            v2 = _select_get_his_value(db_con, table, n2, st, ed, startTime, endTime, jiange)
                            v3 = _select_get_his_value(db_con, table, n3, st, ed, startTime, endTime, jiange)
                            v4 = _select_get_his_value(db_con, table, n4, st, ed, startTime, endTime, jiange)
                            if len(v1['value']) == len(v3['value']):
                                v_v = np.round(np.absolute(np.array(v1['value']) - np.array(v3['value'])), 3)
                            else:
                                v_v = np.array([])
                            if len(v2['value']) == len(v4['value']):
                                v_t = np.absolute(np.array(v2['value']) - np.array(v4['value']))
                            else:
                                v_t = np.array([])
                            V_data['name'].append('%s-%s-%s' % (bamsName[d_i], f'BMS{bms}', bankName[c_i]))
                            V_data['data'].append(v_v.tolist())
                            T_data['name'].append('%s-%s-%s' % (bamsName[d_i], f'BMS{bms}', bankName[c_i]))
                            T_data['data'].append(v_t.tolist())
                            if not timeall and v1['time']:
                                timeall = v1['time']
                    else:
                        if db == 'guizhou':
                            n1 = '%s%s%s' % (d, real_arr[0], c)  # 最大电压
                            n2 = '%s%s%s' % (d, real_arr[1], c)  # 最大温度
                            n3 = '%s%s%s' % (d, real_arr[2], c)  # 最小电压
                            n4 = '%s%s%s' % (d, real_arr[3], c)  # 最小温度
                        else:
                            n1 = '%s%s%s' % (d, c, ch_arr[0])  # 最大电压
                            n2 = '%s%s%s' % (d, c, ch_arr[1])  # 最大温度
                            n3 = '%s%s%s' % (d, c, ch_arr[2])  # 最小电压
                            n4 = '%s%s%s' % (d, c, ch_arr[3])  # 最小温度
                        v1 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, jiange)
                        v2 = _select_get_his_value(db_con, table, n2, st, ed, startTime, endTime, jiange)
                        v3 = _select_get_his_value(db_con, table, n3, st, ed, startTime, endTime, jiange)
                        v4 = _select_get_his_value(db_con, table, n4, st, ed, startTime, endTime, jiange)
                        if len(v1['value']) == len(v3['value']):
                            v_v = np.round(np.absolute(np.array(v1['value']) - np.array(v3['value'])), 3)
                        else:
                            v_v = np.array([])
                        if len(v2['value']) == len(v4['value']):
                            v_t = np.absolute(np.array(v2['value']) - np.array(v4['value']))
                        else:
                            v_t = np.array([])
                        V_data['name'].append('%s-%s' % (bamsName[d_i], bankName[c_i]))
                        V_data['data'].append(v_v.tolist())
                        T_data['name'].append('%s-%s' % (bamsName[d_i], bankName[c_i]))
                        T_data['data'].append(v_t.tolist())
                        if not timeall and v1['time']:
                            timeall = v1['time']
        # logging.info('soc_data:%s----V_data:%s------T_data:%s'%(soc_data,V_data,T_data))
        # socmax,socmin,Vmax,Vmin,Tmax,Tmin = 0,0,0,0,0,0
        # if soc_data['data'][0]:
        #     socmax = np.max(soc_data['data'])
        #     socmin = np.min(soc_data['data'])
        # if V_data['data'][0]:
        #     Vmax = np.max(V_data['data'])
        #     Vmin = np.min(V_data['data'])
        # if T_data['data'][0]:
        #     Tmax = np.max(T_data['data'])
        #     Tmin = np.min(T_data['data'])
        return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}

    def _his_data_1(self, db, bams, bank, bamsName, bankName, st, ed, startTime, endTime, ch_arr, paras, real_arr,
                    jiange):
        '''获取历史数据（东睦）'''
        a = timeUtils.getBetweenMonth(startTime, endTime)  # 计算时间范围内的所有年月
        # t = timeUtils.getNowHouse()[:6]
        dm_table = HisDM('ods_r_measure')
        timeall, soc_data, V_data, T_data = [], {"data": [], 'name': []}, {"data": [], 'name': []}, {"data": [],
                                                                                                     'name': []}
        data1, data2, data3 = {"time": [], "value": []}, {"time": [], "value": []}, {"time": [], "value": []}
        values = None
        values = dongmu_session.query(dm_table.datainfo.label('datainfo'), dm_table.time.label('time')).filter(
            dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
        for d in bamsName:
            bms_index = int(d[-1])
            timeall, soc_data_ = [], []

            for i in values:
                value = json.loads(i['datainfo'])['body']
                timeArray = time.localtime(i['time'])  # 秒数
                otherStyleTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                timeall.append(otherStyleTime)  # BMS电池SOC
                for ii in value:
                    if ii['device'][:4] == 'PCS%s' % bms_index:
                        soc_data_.append(float(ii['BtSOC']))  # BMS电池SOC

            data1['value'] = soc_data_
            data1['time'] = timeall
            data2['time'] = timeall
            data3['time'] = timeall
            data4 = complete_data(data1, jiange)
            timeall = data4['time']
            if data4['value']:
                soc_data["data"].append(data4['value'])
            soc_data['name'].append(d)
        for c in bankName:  # 具体簇
            ban_index = int(c[-1])
            # c_i = bank.index(c)  # 簇下标
            # values = dongmu_session.query(dm_table.datainfo.label('datainfo'),dm_table.time.label('time')).filter(dm_table.time.between(st,ed)).order_by(dm_table.time.asc()).all()
            timeall, V_data_, T_data_ = [], [], []
            for i in values:
                value = json.loads(i['datainfo'])['body']
                timeArray = time.localtime(i['time'])  # 秒数
                otherStyleTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                # timeall.append(otherStyleTime)  # BMS电池SOC
                for ii in value:
                    if ii['device'][:4] == 'BMS%s' % ban_index:
                        if paras == 0:  # paras展示参数0极差1最大2最小3平均值
                            V_data_.append(float(ii['SVRan']))  # 单体电压极差
                            T_data_.append(float(ii['StRan']))  # 单体温度极差
                        elif paras == 1:  # paras展示参数0极差1最大2最小3平均值
                            V_data_.append(float(ii['SMaxV']))  # 单体最高电压
                            T_data_.append(float(ii['Smaxt']))  # 单体最高温度

                        elif paras == 2:  # paras展示参数0极差1最大2最小3平均值
                            V_data_.append(float(ii['SMinV']))  # 单体最低电压
                            T_data_.append(float(ii['sMint']))  # 单体最低温度

                        elif paras == 3:  # paras展示参数0极差1最大2最小3平均值
                            V_data_.append(float(ii['AVOSV']))  # 单体电压均值
                            T_data_.append(float(ii['AVOSt']))  # 单体温度均值

            V_data['name'].append(c)
            T_data['name'].append(c)
            data2['value'] = V_data_
            data3['value'] = T_data_

            data5 = complete_data(data2, jiange)
            data6 = complete_data(data3, jiange)
            if data5['value']:
                V_data["data"].append(data5['value'])
            if data6['value']:
                T_data["data"].append(data6['value'])
                # soc_data['data'].append(value['value'])

        dongmu_session.close()
        return {'time': timeall, "soc": soc_data, "T": T_data, "V": V_data}

    def _real_data_dwd(self, db, bamsName, bankName, station_dict):
        '''获取仪表盘实时数据'''
        # t = timeUtils.getNowHouse()
        unit_list = [-1]  # 防止查询异常
        cluster_list = [-1]
        if db == 'taicgxr':  # 单独处理：有两台BMS
            for d in bamsName:
                d = int(d.split('储能单元')[1])
                unit_list.append(d)
                unit_list.append(d + 1)
                for b in bankName:
                    b = int(b.split('簇')[1])
                    cluster_list.append(b * d)
                    cluster_list.append(b * (d+1))
        else:
            for d in bamsName:
                if db != 'ygqn':
                    d = int(d.split('储能单元')[1])
                else:
                    # 阳泉单独处理分区
                    bams_list = d.split('储能单元')
                    if bams_list[0] == 'A区':
                        d = int(bams_list[1])
                    elif bams_list[0] == 'B区':
                        d = int(bams_list[1]) + 40
                    else:
                        d = int(bams_list[1]) + 80
                unit_list.append(d)
                for b in bankName:
                    b = int(b.split('簇')[1])
                    if station_dict.get(d):
                        if len(station_dict.get(d)) > b - 1:
                            cluster_list.append(station_dict.get(d)[b - 1])
                        else:
                            cluster_list.append('--')

        s_time = (datetime.datetime.now() - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')
        soc_sql = f"""SELECT
                    unit_rk,
                    GROUP_CONCAT( cast( soc AS VARCHAR ) ORDER BY start_time DESC, soc DESC) AS socs 
                FROM
                    `dwd_rhyc_ES_station`.`dwd_unit_measure_5min` 
                WHERE
                    `station_name` = '{db}' 
                    AND `start_time` >= '{s_time}'
                    AND unit_rk IN {tuple(unit_list)}
                    AND soc BETWEEN 0.1 
                    AND 101 
                GROUP BY
                    unit_rk;"""

        conn = pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(soc_sql)
            soc_result = cursor.fetchall()
            soc = []
            b_arr = {
                'max_vol': '--',
                'min_vol': '--',
                'max_temp': '--',
                'min_temp': '--'
            }
            for i in soc_result:
                if i.get('socs'):
                    socs = [float(soc_v) for soc_v in i['socs'].split(',')]
                    soc.append(sum(socs) / len(socs))
            for _i, k in enumerate(['max_vol', 'min_vol', 'max_temp', 'min_temp']):
                b_arr_sql = f"""SELECT
                                    unit_rk,
                                    GROUP_CONCAT( cast( {k} AS VARCHAR ) ORDER BY start_time DESC, {k} DESC) AS key_name 
                                FROM
                                    `dwd_rhyc_ES_station`.`dwd_bc_measure_5min` 
                                WHERE
                                    `station_name` = '{db}' 
                                    AND `start_time` >= '{s_time}'
                                    AND unit_cluster_rk IN {tuple(cluster_list)}
                                    AND {k} BETWEEN 0.1 
                                    AND 65 
                                GROUP BY
                                    unit_rk;"""
                cursor.execute(b_arr_sql)
                b_arr_result = cursor.fetchall()
                for i in b_arr_result:
                    if i.get('key_name'):
                        if k[:3] == 'max':
                            b_arr[k] = max([float(y) for y in i['key_name'].split(',')])
                        else:
                            b_arr[k] = min([float(y) for y in i['key_name'].split(',')])
            soc = [float(i) for i in soc]
            return {'soc': round(np.mean(soc), 2) if soc else '--', 'Vmax': b_arr['max_vol'],
                    'Vmin': b_arr['min_vol'],
                    'Tmax': b_arr['max_temp'], 'Tmin': b_arr['min_temp']}

        except Exception as e:
            logging.error(e)
            return []
        finally:
            cursor.close()
            conn.close()





    def _real_data(self, db, bams, bank):
        '''获取仪表盘实时数据'''
        t = timeUtils.getNowHouse()
        table = 'ods_r_measure1' if db != 'dongmu' else 'r_measure'
        soc, b_arr = [], [[], [], [], []]
        if db == 'baodian':  # 保电项目特殊处理
            soc_l_n = real_char_tv_bd  # soc后缀名
            real_arr = real_char_rv_bd
        elif db == 'guizhou':  # 贵州
            soc_l_n = real_char_tv
            real_arr = real_char_rv_gz
        else:
            soc_l_n = real_char_tv
            real_arr = real_char_rv
        for d in bams:
            soc_name = '%s%s' % (d, soc_l_n)
            db_con = _return_db_con(db, d)  # 获取具体数据库链接
            if db == 'taicgxr':  # 两台BMS处理
                soc_list = []
                for bms in range(1,3):
                    soc_name = f'{d}BMS{bms}.{soc_l_n}'
                    value = _select_get_des_value(db_con, table, soc_name, 101)
                    if value != '--':
                        soc_list.append(value)
                if soc_list:
                    soc.append(sum(soc_list) / len(soc_list))
            else:
                value = _select_get_des_value(db_con, table, soc_name, 101)
                if value != '--':
                    soc.append(value)
            for i in range(len(real_arr)):  # 变量名
                for c in bank:  # 具体簇
                    if db == 'taicgxr':  # 两台BMS处理
                        totality = []
                        for bms in range(1,3):
                            n1 = f'{d}BMS{bms}.{c}{real_arr[i]}'
                            v = _select_get_des_value(db_con, table, n1)
                            if v != '--':
                                totality.append(v)
                        if totality:
                            b_arr[i].append(sum(totality) / 2)
                    else:
                        if db == 'guizhou':
                            n1 = '%s%s%s' % (d, real_arr[i], c)
                        else:
                            n1 = '%s%s%s' % (d, c, real_arr[i])
                        v = _select_get_des_value(db_con, table, n1)
                        if v != '--':
                            b_arr[i].append(v)

        return {'soc': round(np.mean(soc), 2) if soc else '--', 'Vmax': np.max(b_arr[0]) if b_arr[0] else '--',
                'Vmin': np.min(b_arr[1]) if b_arr[1] else '--',
                'Tmax': np.max(b_arr[2]) if b_arr[2] else '--', 'Tmin': np.min(b_arr[3]) if b_arr[3] else '--'}


    def _real_data_yq_d(self, db, bams, bank):

        db_con_ms = db_[db][1][1]  # 获取阳泉D区数据库连接
        table_m = 'ods_r_measure1'

        HisTable_m = HisACDMS(table_m)
        now_time = datetime.datetime.now()
        st = now_time - datetime.timedelta(minutes=30)  # 查询半小时内
        # 查询单体温度、单体电压、soc实时值（最新5条）
        # soc 最高电压 最低电压 最高温度 最低温度
        names = ['RkSoc', 'SgMxVl', 'SgMiVl', 'SgMxTm', 'SgMiTm']
        name_list = []
        for n in bams:
            for y in names:
                name_list.append(n+y)

        soc, b_arr = [], [[], [], [], []]
        try:
            values = db_con_ms.query(HisTable_m.name, HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name.in_(name_list),
                                                                             HisTable_m.dts_s >= st,
                                                                             HisTable_m.value.between(0.01, 100)).order_by(
                HisTable_m.dts_s.desc()).limit(len(bams)*5).all()
        except Exception as e:
            logging.error(e)
            db_con_ms.close()
            return {}
        finally:
            db_con_ms.close()
        if values:
            for v in values:
                if 'RkSoc' in v[0]:  # soc
                    soc.append(v[1])
                elif 'SgMxVl' in v[0]:  # 最高电压
                    b_arr[0].append(v[1])
                elif 'SgMiVl' in v[0]:  # 最低电压
                    b_arr[1].append(v[1])
                elif 'SgMxTm' in v[0]:  # 最高温度
                    b_arr[2].append(v[1])
                elif 'SgMiTm' in v[0]:  # 最低温度
                    b_arr[3].append(v[1])
                else:
                    pass


            return {'soc': round(np.mean(soc), 2) if soc else '--', 'Vmax': np.max(b_arr[0]) if b_arr[0] else '--',
                    'Vmin': np.min(b_arr[1]) if b_arr[1] else '--',
                    'Tmax': np.max(b_arr[2]) if b_arr[2] else '--', 'Tmin': np.min(b_arr[3]) if b_arr[3] else '--'}
        else:
            return {}



    def _real_data_1(self, db, bams, bank):
        '''获取仪表盘实时数据（东睦）'''
        # t = timeUtils.getNowHouse()
        # table = 'r_measure%s' % t
        soc, b_arr = [], [[], [], [], []]

        v1 = real_data('measure', db, 'db')
        if v1:
            e1 = v1['body']
        for d in bams:
            for i in e1:
                if i['device'][:4] == 'PCS%s' % d[-1]:
                    soc.append(float(i['BtSOC']))  # BMS电池SOC
            for c in bank:  # 具体簇
                for i in e1:
                    if i['device'][:4] == 'BMS%s' % d[-1]:
                        b_arr[0].append(float(i['SMaxV']))  # 单体最高电压
                        b_arr[1].append(float(i['SMinV']))  # 单体最低电压
                        b_arr[2].append(float(i['Smaxt']))  # 单体最高温度
                        b_arr[3].append(float(i['sMint']))  # 单体最低温度
        return {'soc': round(np.mean(soc), 2) if soc else '--', 'Vmax': np.max(b_arr[0]) if b_arr[0] else '--',
                'Vmin': np.min(b_arr[1]) if b_arr[1] else '--',
                'Tmax': np.max(b_arr[2]) if b_arr[2] else '--', 'Tmin': np.min(b_arr[3]) if b_arr[3] else '--'}


def _select_get_des_value(db_con, table, name, maxV=65, minV=0.1):
    '''查询表获取最新值'''
    # db_con.commit()
    HisTable = HisACDMS(table)
    try:
        value = db_con.query(HisTable.value.label('value')).filter(HisTable.name == name,
                                                                   HisTable.value.between(minV, maxV)).order_by(
            HisTable.dts_s.desc()).first()  # 查询当前时间表
    except:
        return '--'
    if value:
        return value[0]
    else:
        return '--'


def _select_get_des_value_new(db_con, table, names, maxV=99999999, minV=0.1):
    '''查询表获取最新值'''
    # 获取当前时间
    now = datetime.datetime.now()
    # 计算七天前的时间
    seven_days_ago = now - datetime.timedelta(days=1)
    HisTable = HisACDMS(table)
    try:
        # 子查询：获取每个name在7天内的最大时间戳
        subquery = (
            db_con.query(
                HisTable.name,
                func.max(HisTable.dts_s).label('max_dts_s')
            )
            .filter(
                HisTable.name.in_(names),
                HisTable.dts_s >= seven_days_ago
            )
            .group_by(HisTable.name)
            .subquery()
        )
        # 主查询：获取每个name在7天内的最新记录
        query = (
            db_con.query(
                HisTable.name,
                HisTable.value.label('value')
            )
            .join(subquery, (HisTable.name == subquery.c.name) & (HisTable.dts_s == subquery.c.max_dts_s))
        )

        # 初始化结果字典
        result = {name: '--' for name in names}

        # 更新结果字典
        for row in query.all():
            result[row.name] = row.value
        return result
    except:
        result = {name: '--' for name in names}
        return result



def _select_get_his_value(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=65):
    '''获取历史数据'''
    # db_con.commit()
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    try:
        values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                         HisTable_m.dts_s.between(st, ed),
                                                                         HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.asc()).all()  # 查询当前月
        for val in values:
            if isinstance(val[1], datetime.datetime):
                data['time'].append(val[1].strftime("%Y-%m-%d %H:%M:%S"))
            else:
                data['time'].append(val[1])
            data['value'].append(val[0])
    except:
        values = []

    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        data['value'].insert(0, data['value'][0])
    else:
        return data

    data['time'].append(endTime)
    data['value'].append(data['value'][-1])
    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()
    return complete_data(data, jiange)


def _return_db_con(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_siss[d]
    elif db == 'guizhou':
        d = int(d.split('.')[0][-2])
        return bmsdb[db][d - 1]
    elif db == 'ygqn':
        d= d.split('.')[0]
        for k,v in ygqn.items():
            if d in v:
                d=int(k)
        return bmsdb[db][d-1]
    elif db == 'taicgxr':
        d = int(d.split('.')[0][1])
        d = d//2 if d%2 == 0 else d//2 + 1
        return bmsdb[db][0][d]
    else:
        d = int(d[1:2]) if db != 'dongmu' else 0
        if db == 'binhai':  # 滨海电站
            if d < 4:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'taicang' or db == 'dongmu':
            return bmsdb[db][0]
        elif db == 'ygzhen':
            if d < 3:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'zgtian' or db == 'datong' or db == 'shgyu':
            if d < 3:
                return bmsdb[db][0]
            elif d < 5:
                return bmsdb[db][1]
            elif d < 7:
                return bmsdb[db][2]
            else:
                return bmsdb[db][3]
        elif db == 'houma':
            if d < 3:
                return bmsdb[db][0]
            elif d < 5:
                return bmsdb[db][1]
            elif d < 7:
                return bmsdb[db][2]
            else:
                return bmsdb[db][3]


def map_numbers(num):
    if num % 2 == 0:
        return (num - 1) // 2 + 1
    else:
        return num // 2 + 1


def _select_get_his_value_new(select_fileds, db, table, st, ed, startTime, endTime, jiange, type, field_arr, rk_num, cell_v=[], cell_t=[], vmax=65):
    '''获取历史数据'''
    # db_con.commit()
    data = {'time': [], 'value': []}
    if select_fileds:
        select_fileds = select_fileds + ",start_time"
    result = {'tm': [], 'value': []}
    try:
        values = data_sql_(select_fileds, table, st, ed, db, type, rk_num)
        if values:
            result = {key if key != 'start_time' else 'tm': [] for key in values[0].keys()}
            # 处理数据
            for entry in values:
                for key, value in entry.items():
                    if key == 'start_time':
                        result['tm'].append(value.strftime('%Y-%m-%d %H:%M:%S'))
                    else:
                        result[key].append(value)
    except:
        values = []
    if len(result['tm'])>0:
        res_data = {}
        tm_data = result['tm'].copy()
        if not tm_data:
            return data
        new_field_dict = {v: k for k, v in field_arr.items()}
        keys_list = list(result.keys())
        for field_item in keys_list:
            if field_item != 'tm' and field_item != '' and field_item != "cell_vol_list" and field_item != "cell_temp_list":
                data['time'] = tm_data.copy()
                data['value'] = result[field_item].copy()
                if startTime not in data['time']:
                    data['time'].insert(0, startTime)
                    data['value'].insert(0, data['value'][0])
                if endTime not in data['time']:
                    data['time'].append(endTime)
                    data['value'].append(data['value'][-1])
                df = pd.DataFrame(data)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data["time"] = df["time"].tolist()
                data["value"] = df["value"].tolist()
                res = complete_data(data, jiange)
                field_key_name = new_field_dict[str(field_item)]
                res_data[field_key_name] = res['value']
                res_data['tm'] = res['time']
            elif field_item == 'cell_vol_list':
                v_data={}
                new_cell_vol_val = []
                for cell_vol_val in result[field_item].copy():
                    new_cell_vol_val.append(json.loads(cell_vol_val))

                for i, key in enumerate(cell_v):
                    if i < len(new_cell_vol_val[0]):
                        v_data[key] = [row[i] for row in new_cell_vol_val]
                    else:
                        v_data[key] = []
                for item_v in v_data.keys():
                    data['time'] = tm_data.copy()
                    data['value'] = v_data[item_v]
                    if startTime not in data['time']:
                        data['time'].insert(0, startTime)
                        data['value'].insert(0, data['value'][0])
                    if endTime not in data['time']:
                        data['time'].append(endTime)
                        data['value'].append(data['value'][-1])
                    df = pd.DataFrame(data)
                    df = df.drop_duplicates(subset=["time"], keep="first")
                    # 转换回字典
                    data["time"] = df["time"].tolist()
                    data["value"] = df["value"].tolist()
                    res = complete_data(data, jiange)
                    res_data[item_v] = res['value']
            elif field_item == 'cell_temp_list':
                t_data = {}
                new_cell_temp_val = []
                for cell_temp_val in result[field_item]:
                    new_cell_temp_val.append(json.loads(cell_temp_val))

                for i, key in enumerate(cell_t):
                    if i < len(new_cell_temp_val[0]):
                        t_data[key] = [row[i] for row in new_cell_temp_val]
                    else:
                        t_data[key] = []
                for item_t in t_data.keys():
                    data['time'] = tm_data.copy()
                    data['value'] = t_data[item_t]
                    if startTime not in data['time']:
                        data['time'].insert(0, startTime)
                        data['value'].insert(0, data['value'][0])
                    if endTime not in data['time']:
                        data['time'].append(endTime)
                        data['value'].append(data['value'][-1])
                    df = pd.DataFrame(data)
                    df = df.drop_duplicates(subset=["time"], keep="first")
                    # 转换回字典
                    data["time"] = df["time"].tolist()
                    data["value"] = df["value"].tolist()
                    res = complete_data(data, jiange)
                    res_data[item_t] = res['value']
        return res_data

    else:
        return result

def data_sql_(fields,table, start_time, end_time, db, type, rk_num):
    """查询数据"""""
    conn = pool.connection()
    cursor = conn.cursor()
    try:
        if type == "pile":
            where_str = " and unit_rk = {}".format(rk_num)
        else:
            where_str = " and unit_cluster_rk = {}".format(rk_num)
        # 执行SQL查询
        sql = """SELECT {}
                    FROM {}
                    where  start_time BETWEEN '{}' AND '{}' 
                    and station_name = '{}' {}
                    order by start_time asc
                    """.format(fields,table, start_time, end_time, db, where_str)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()

def _get_the_new_value(db_con, table, names, maxV=99999999, minV=0.1):
    '''查询表获取最新值'''
    # 获取当前时间
    now = datetime.datetime.now()
    # 计算七天前的时间
    seven_days_ago = now - datetime.timedelta(days=7)
    HisTable = HisACDMS(table)
    try:
        # 子查询：获取每个name在7天内的最大时间戳
        subquery = (
            db_con.query(
                HisTable.name,
                func.max(HisTable.dts_s).label('max_dts_s')
            )
            .filter(
                HisTable.name.in_(names),
                HisTable.dts_s >= seven_days_ago
            )
            .group_by(HisTable.name)
            .subquery()
        )
        # 主查询：获取每个name在7天内的最新记录
        query = (
            db_con.query(
                HisTable.name,
                HisTable.value.label('value')
            )
            .join(subquery, (HisTable.name == subquery.c.name) & (HisTable.dts_s == subquery.c.max_dts_s))
        )

        # 初始化结果字典
        result = {name: '--' for name in names}

        # 更新结果字典
        for row in query.all():
            result[row.name] = row.value
        return result
    except:
        result = {name: '--' for name in names}
        return result


def get_the_new_data(select_fileds,table, db, type, rk_num, field_arr, cell_v=[], cell_t=[],):
    # 获取当前时间
    current_time = datetime.datetime.now()
    # 计算一个小时前的时间
    one_hour_ago = current_time - datetime.timedelta(hours=1)
    one_hour_ago_str = one_hour_ago.strftime('%Y-%m-%d %H:%M:%S')
    try:
        result = data_the_new_sql_(select_fileds, table, db, type, rk_num, one_hour_ago_str)
    except:
        result = {}

    if result:
        res_data = {}
        new_field_dict = {v: k for k, v in field_arr.items()}
        keys_list = list(result.keys())
        for field_item in keys_list:
            if field_item != 'tm' and field_item != '' and field_item != "cell_vol_list" and field_item != "cell_temp_list":
                # result[new_field_dict[field_item]] = res['value']
                field_key_name = new_field_dict[str(field_item)]
                res_data[field_key_name] = result[field_item] if result[field_item] is not None else '--'
            elif field_item == 'cell_vol_list':
                new_cell_vol_val = json.loads(result[field_item])
                for i, key in enumerate(cell_v):
                    if i < len(new_cell_vol_val):
                        res_data[key] = new_cell_vol_val[i] if new_cell_vol_val[i] is not None else '--'
                    else:
                        res_data[key] = '--'
            elif field_item == 'cell_temp_list':
                new_cell_temp_val = json.loads(result[field_item])
                for i, key in enumerate(cell_t):
                    if i < len(new_cell_temp_val):
                        res_data[key] = new_cell_temp_val[i] if new_cell_temp_val[i] is not None else '--'
                    else:
                        res_data[key] = '--'
        return res_data
    else:
        return None

def data_the_new_sql_(fields,table, db, type, rk_num, one_hour_ago_str):
    """查询数据"""""
    conn = pool.connection()
    cursor = conn.cursor()
    try:
        if type == "pile":
            where_str = " and unit_rk = {}".format(rk_num)
        else:
            where_str = " and unit_cluster_rk = {}".format(rk_num)
        # 执行SQL查询
        sql = """SELECT {}
                    FROM {}
                    where station_name = '{}' {} 
                    and start_time>= '{}'
                    order by start_time desc limit 0,1
                    """.format(fields,table, db, where_str, one_hour_ago_str)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchone()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()


# for i in range(1, 17):
#     print(f"{i} -> {map_numbers(i)}")