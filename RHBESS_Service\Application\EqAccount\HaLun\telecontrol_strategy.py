import ast
import concurrent
import itertools
import json
import time
import traceback
from io import BytesIO
from pathlib import Path

import tornado.web
from openpyxl.reader.excel import load_workbook

from Application.EqAccount.encryption.AES_symmetric_encryption import EncryptDate
from Application.Models import session
from Application.Models.User.station import Station
from Application.Models.User.station_relation import StationR
from Application.Models.User.telecontrol_strategy_model import *
from Application.Models.User.user import User
from Tools.DB.redis_con import r as reids_r
from Tools.TimeTask import get_middle_task
from Tools.DB.redis_con import r_real
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import user_engine, user_session, DEBUG, tinalu_session
from Application.Models.base_handler import BaseHandler
from Tools.Utils.mimio_tool import MinioTool
from Tools.Utils.num_utils import *
from Tools.Utils.time_utils import timeUtils
from sqlalchemy import func, or_, not_
import pandas as pd
import datetime
import pymysql
from dbutils.persistent_db import PersistentDB
from openpyxl import Workbook
import paho.mqtt.client as mqtt

EMPTY_STR_LIST = ['None', '', '--', 'null', None]

model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
gz_name = ast.literal_eval(model_config.get('peizhi', 'gz_name') ) #故障名称
list_datong = json.loads(model_config.get('peizhi', 'list_datong'))
from Application.Cfg.dir_cfg import model_config as model_config_base
pool = PersistentDB(pymysql, 10,**{
            "host": model_config_base.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config_base.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config_base.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config_base.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config_base.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })

ISSUANCE_TYPE = {
    "1": "整站控制模式",
    "2": "自动最大充电功率",
    "3": "自动最大放电功率",
    "4": "手动最大充放电功率",
    "5": "全站功率",
    "6": "需量目标功率",
    "7": "变压器容量",
    "8": "变压器安全系数",
    "9": "防逆流阈值",
    "10": "PCS控制模式",
    "11": "PCS开关",
    "12": "PCS功率",
    "13": "故障复位",
    "14": "电操状态",
    "15": "故障复归",
    "16": "分系统使能",
    "17": "功率计划下发",
    "18": "运行策略修改",
    "19": "策略下发",
}

station_type_level = {
    "sikly":{
        "province_id": 10,
        "type": 4,
        "level": 3
    },
    "tczj":{
        "province_id": 11,
        "type": 6,
        "level": 4
    }
}

t

class UserVerify(BaseHandler):
    """用户权限校验"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        session = self.getOrNewSession()
        create_user = session.user['id']
        obj = dict(username=create_user)
        return self.returnTypeSuc(obj)

    def post(self):
        account = self.get_argument("account", None)  # 登录名
        passwd = self.get_argument("passwd", None)  # 密码
        passwd = computeMD5(passwd)  # md5加密

        if DEBUG:
            logging.info('account:%s,passwd:%s' % (account, passwd))
        if not account or not passwd:
            return self.customError("用户名或密码为空")


        user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
                                               User.unregister == 1).first()
        if not user:
            # Session.remove()
            return self.customError("用户名或密码错误")
        else:
            if user.passwd != passwd:
                return self.customError("用户名或密码错误")
            else:
                return self.returnTypeSuc({})

def validate_user_passwd(account, passwd):

    user = user_session.query(User).filter(or_(User.account == account, User.phone_no == account),
                                           User.unregister == 1).first()
    passwd = computeMD5(passwd)  # md5加密
    if not user:
        return False, "用户信息错误"
    else:
        if user.passwd != passwd:
            return False, "用户名或密码错误"
        else:
            return True, ""

class pcsRealDatas(BaseHandler):
    """PCS列表 返回该站下所有pcs，及实时功率和开关状态"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        station= self.get_argument('station', '') #站英文名
        lang = self.get_argument('lang', None)  # 英文
        datas = (user_session.query(TControlData).filter(TControlData.is_use==1,TControlData.station==station,
                                                         TControlData.device=="PCS").order_by(desc(TPlanHistory.create_time)))
        detail = []
        for item in datas:
            name = item.descr
            _item = {'pcaName': name}
            r_data = r_real.get(name)
            if r_data:
                data = json.loads(r_data)
                _item['realPower'] = data.get("realPower")
            else:
                _item['realPower'] = "--"
            detail.append(_item)

        return self.returnTypeSuc(detail)


class sendToDevice(BaseHandler):
    """指令下发"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        station = self.get_argument('station', '') #站英文名
        ems = self.get_argument('ems', None) # ems下发勾选的内容和值
        pcs = self.get_argument('pcs', None) # Pcs下发勾选的内容和值
        cmodel = self.get_argument('cmodel', None) # 控制模式勾选的内容和值
        subSystem = self.get_argument('subSystem', None) # 分系统使用勾选内容和值
        detail = []


        return self.returnTypeSuc(detail)

class dispositionList(BaseHandler):
    """配置下拉框列表"""
    @tornado.web.authenticated
    def get(self):
        start_time =  self.get_argument('start_time', None)
        end_time = self.get_argument('end_time', None)
        station_id = self.get_argument('station_id', None)
        search_key = self.get_argument('search_key', None)
        name = self.get_argument('name', None)
        session = self.getOrNewSession()
        user_id = str(session.user['id'])  # 当前用户id
        data = []
        try:
            # if user_id in []:
            #     custom_strategy = user_session.query(TUserStrategy).filter(TUserStrategy.is_use==1)
            # else:
            #     custom_strategy = user_session.query(TUserStrategy).filter(TUserStrategy.user_id==user_id, TUserStrategy.is_use==1)
            custom_strategy = user_session.query(TUserStrategy).filter(TUserStrategy.is_use == 1)
            if name:
                custom_strategy = custom_strategy.filter(TUserStrategy.name.like(f"%{name}%"))

            if start_time:
                start_time = f"{start_time} 00:00:00"
                custom_strategy = custom_strategy.filter(TUserStrategy.create_time >start_time)
                stations = None

            if end_time:
                end_time = f"{end_time} 23:59:00"
                custom_strategy = custom_strategy.filter(TUserStrategy.create_time <end_time)

            # 用户自定义策略
            for custom in custom_strategy.order_by(desc(TUserStrategy.create_time)).all():
                custom_dict = {
                    'id': str(custom.id),
                    'name': custom.name,
                    'type': 1  # 自定义策略,
                }
                data.append(custom_dict)
            if search_key:
                data = [i for i in data if i.get('name') and search_key in i.get('name')]
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
        return self.returnTypeSuc(data)


class strategyCompare(BaseHandler):
    """自动控制模式配置 策略对比"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        session = self.getOrNewSession()
        station_names= session.user.get('station_name', '')
        if not station_names:
            self.redirect("/UserLogin/Login")
        user_id = str(session.user['id'])  # 当前用户id
        return self.returnTypeSuc([])

    # def _get_data(self, station_id, station, month, hours):
    #     """
    #     获取当前站的指定月份的自动策略控制信息
    #     :return:
    #     """
    #     client = mtqq_station_strategy(station_id, month)
    #     # conn = get_redis_connection("3")
    #     # if month:
    #     i = 0
    #     n = 2 if int(month) < 10 else 3
    #     charge_config = {}
    #     rl_dict = {}
    #     while i < 5:
    #         time.sleep(0.1)
    #         datas = r_real.get('{}-{}-mqtt'.format(station, month))
    #         if datas:
    #             datas = eval(datas)
    #             datas = datas.get('body')[0].get('body')
    #             if not datas:
    #                 break
    #             key = list(datas.keys())[0]
    #             if key[:n] == f'M{month}':
    #                 # if settings.FORMATEF:  # 策略时间颗粒度 15分钟或者一小时
    #                 count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
    #                 for hour in hours:
    #                     current_hour = hour
    #                     hour = 24 if hour == 0 else hour
    #                     if count == 2:
    #                         _hour = 48 if hour == 0 else hour + 24
    #                     else:
    #                         _hour = 24 if hour == 0 else hour
    #                     charge_config[current_hour] = [
    #                         float(datas.get(f'M{month}H{hour}F', 0)),
    #                         float(datas.get(f'M{month}H{hour}F', 0)),
    #                         float(datas.get(f'M{month}H{_hour}F', 0)),
    #                         float(datas.get(f'M{month}H{_hour}F', 0)),
    #                     ]
    #                     rl_dict[current_hour] = [
    #                         float(datas.get(f'M{month}H{hour}P', 0)) * 100,
    #                         float(datas.get(f'M{month}H{hour}P', 0)) * 100,
    #                         float(datas.get(f'M{month}H{_hour}P', 0)) * 100,
    #                         float(datas.get(f'M{month}H{_hour}P', 0)) * 100
    #                     ]
    #                 i = 10
    #         else:
    #             i += 1
    #     client.disconnect()
    #     return charge_config, rl_dict

    def _get_redis_data(self, station_id, station, month, hours):
        charge_config = {}
        rl_config = {}
        for hour in hours:
            current_hour = hour
            _hour = 48 if hour == 0 else hour + 24
            if hour == 0:
                hour = 24
            _charge_value = []
            _rl_value = []
            for item in [hour, _hour]:
                charge_name = '{}.EMS.MC.{}'.format(station, f'M{month}H{item}F')
                rl_name = '{}.EMS.MC.{}'.format(station, f'M{month}H{item}P')
                charge_source_data = real_data("measure",charge_name,"db")
                charge_rl_data = real_data("measure", rl_name, "db")
                if isinstance(charge_source_data, str):
                    charge_data = json.loads(charge_source_data)
                else:
                    charge_data = charge_source_data
                if isinstance(charge_rl_data, str):
                    rl_data = json.loads(charge_rl_data)
                else:
                    rl_data = charge_rl_data
                charge_value = charge_data.get("value")
                rl_value = rl_data.get("value")
                # _charge_value.append(int(charge_value))
                # _rl_value.append(round(int(rl_value) * 100, 2))
                # int(format(charge_value, '.0f'))
                _charge_value.append(int(charge_value))
                _rl_value.append(int(format(rl_value * 100, '.0f')))

            charge_config[str(current_hour)] = _charge_value
            rl_config[str(current_hour)] = _rl_value
        return charge_config, rl_config

    def customize_tactics(self, station_info, detail, month, hours):
        # 实时策略
        current_charge_config, current_rl_list = self._get_redis_data(station_info.id, station_info.name,
                                                                month, hours)
        # 自定义策略
        month_detail = user_session.query(TUserStrategyCategoryMonth).filter(TUserStrategyCategoryMonth.strategy_id == detail.id,
                                                                             TUserStrategyCategoryMonth.month_number == month).first()
        if not month_detail or not month_detail.user_strategy_category_id:
            logging.error(f"用户自动控策略:策略配置不完整")
            raise Exception(f"用户自动控策略:策略配置不完整")
        strategy_category = user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.id==
                                                                             month_detail.user_strategy_category_id).first()
        customize_change_config = eval(strategy_category.charge_config)
        customize_rl_list = eval(strategy_category.rl_list)
        customize_pv_list = eval(strategy_category.pv_list)

        day_res = []


        n_month = f'{datetime.datetime.now().year}-{month}' if int(
            month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        price_dict = {}
        type_level = station_type_level.get(station_info.name)
        if type_level:
            station_instance = tinalu_session.query(PeakValleyNew).filter_by(year_month=n_month,
                                                                   province_id=type_level.get("province_id"),
                                                                   type=type_level.get("type"),
                                                                   level=type_level.get("level")).order_by('moment').all()
            for price in station_instance:
                moment = int(price.moment[:2])
                if price_dict.get(moment):
                    price_dict[moment].append(price.pv)
                else:
                    price_dict[moment] = [price.pv]
        for i in hours:
            # first, last = i * 4, i * 4 + 4
            first, last = i * 2, i * 2 + 2
            # 策略下发是半个小时的，展示要展示15分钟
            rl = [element for element in customize_rl_list[first: last] for _ in range(2)]
            charge = [element for element in customize_change_config[first: last] for _ in range(2)]
            if len(customize_pv_list) == 96:
                first, last = i * 4, i * 4 + 4
                pv = customize_pv_list[first: last]
            else:
                pv = [element for element in customize_pv_list[first: last] for _ in range(2)]
            data = {
                'hours': i,
                'rl': rl,
                'charge': charge,
                'pv': pv if pv else ["--","--","--","--"],
                'current_rl': [element for element in current_rl_list[str(i)] for _ in range(2)] if current_rl_list else ["--","--","--","--"],
                'current_charge': [element for element in current_charge_config[str(i)] for _ in range(2)] if current_charge_config else ["--","--","--","--"],
                'current_pv': price_dict.get(i) if price_dict else ["--","--","--","--"],
                # 'current_rl': ["--","--","--","--"],
                # 'current_charge': ["--","--","--","--"],
                # 'current_pv': ["--","--","--","--"],

            }
            data['status'] = [data['charge'], data['pv']] == [data['current_charge'], data['current_pv']]
            # data['status'] = False
            day_res.append(data)
        return day_res, month

    def post(self):
        station_id = self.get_argument('station_id', None)
        strategy_id = self.get_argument('strategy_id', None)
        hours = self.get_argument('hours', None)

        if not all([station_id]):
            return self.customError("必填参数缺失")
        station_info = user_session.query(Station).filter(Station.name == station_id).first()
        if not station_info:
            return self.customError("查询关联项目不存在！")
        try:
            res = []
            m = datetime.datetime.now().month
            hours_str = hours.replace('24', '0')
            hours = hours_str.split(',') if hours != 'all' else [i for i in range(0, 24)]
            hours = [int(h) for h in hours]
            hours.sort()
            # 创建线程池
            # executor = concurrent.futures.ThreadPoolExecutor(max_workers=12)
            if not strategy_id:
                detail = {
                    'current_is_folllow': "--",
                    'is_follow': "--",
                    'data': {
                        'month': "--",
                        'data': "--",
                        'name': "--"
                    }
                }
                return self.returnTypeSuc(detail)
            # 自定义策略对比
            is_month = user_session.query(TUserStrategyCategoryMonth).filter(TUserStrategyCategoryMonth.strategy_id == strategy_id,
                                                                  TUserStrategyCategoryMonth.month_number == m).first()
            if not is_month:
                return self.customError("用户自动控制策略: 资源不存在或配置不完善")
            category_obj = user_session.query(TUserStrategyCategory).filter_by(id=is_month.user_strategy_category_id).first()
            is_follow = category_obj.is_follow if category_obj.is_follow is not None else '--'
            detail = user_session.query(TUserStrategy).filter(TUserStrategy.id==strategy_id).first()
            task_list = []
            # try:
            for i in range(1, 13):
                # task_list.append(executor.submit(self.customize_tactics, station_info, detail , i, hours))
                task_list.append(self.customize_tactics(station_info, detail, i, hours))
            # concurrent.futures.wait(task_list)
            for task_res in task_list:
                # task_res = task_res.result()
                res.append(
                    {
                        'month': task_res[1],
                        'data': task_res[0],
                        'name': detail.name
                    }
                )
            # except Exception as e:
            #     logging.error(f"策略对比:自定义策略对比失败{traceback.print_exc()}")
            #     return self.customError( e.args[0])
            # 关闭线程池
            # executor.shutdown()
            is_folllow_name = '{}.EMS.MC.{}'.format(station_info.name, "Scd")
            is_folllow_data = real_data("status", is_folllow_name, "db")
            if isinstance(is_folllow_data, str):
                is_folllow_data = json.loads(is_folllow_data)
            else:
                is_folllow_data = is_folllow_data
            current_is_folllow = int(is_folllow_data.get("value")) - 1
            if current_is_folllow == -1:
                current_is_folllow = 0
            detail={
                'current_is_folllow': current_is_folllow,
                'is_follow': is_follow,
                'data': res
            }

            return self.returnTypeSuc(detail)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
            tinalu_session.close()

class strategySave(BaseHandler):
    """自动控制模式配置 策略另存为"""

    def _get_pv_status_new(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        # station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
        #                                                     level=level).order_by('moment').all()
        # price_dict = {}
        station_instance = tinalu_session.query(PeakValleyNew).filter_by(year_month=n_month,
                                                               province_id=province,
                                                               type=type_,
                                                               level=level).order_by('moment').all()
        if station_instance:
            for i in station_instance[::2]:
                pv.append(i.pv)
            return pv
        else:
            return pv

    def _handle_strategy_hours(self, charge_config, rl_list, pv_list, month, lang='zh'):
        """相同时间内的阈值、充放电标识、峰谷标识 数据合并"""
        if not pv_list:
            pv_list =  ['-'] * len(charge_config)
        compare_list = zip(charge_config, rl_list, pv_list)
        last_info = None
        _i = 0
        t = '00:00'
        data = []
        for i, v in enumerate(compare_list):
            if i != 0:
                if v == last_info:
                    _i += 1
                else:
                    end_time = lambda x: x + datetime.timedelta(minutes=30 * _i)
                    start_time = datetime.datetime.strptime(t, '%H:%M')
                    end_time = end_time(start_time).strftime('%H:%M')

                    d = {
                        'start_time': t,
                        'end_time': end_time,
                        'rl': last_info[1],
                        'charge_config': last_info[0],
                        'pv': last_info[2],
                        'explain': '',
                    }
                    data.append(d)
                    t = end_time
                    _i = 1
                    last_info = v
            else:
                _i += 1
                last_info = v

        end_time = lambda x: x + datetime.timedelta(minutes=30 * _i)
        start_time = datetime.datetime.strptime(t, '%H:%M')
        end_time = (end_time(start_time) - datetime.timedelta(minutes=1)).strftime('%H:%M')
        d = {
            'start_time': t,
            'end_time': end_time,
            'rl': last_info[1],
            'charge_config': last_info[0],
            'pv': last_info[2],
            'explain': '',
        }
        data.append(d)

        res = {
            'name': f'月份{month}' if lang == 'zh' else f'Month {month}',
            'months': [month],
            'is_follow': 1,
            'data': data

        }
        return res

    def _get_redis_data(self, station_id, station, month, hours):
        charge_config = {}
        rl_config = {}
        explain_config = {}
        for hour in hours:
            current_hour = hour
            _hour = 48 if hour == 0 else hour + 24
            if hour == 0:
                hour = 24
            _charge_value = []
            _rl_value = []
            explain_json = []
            for item in [hour, _hour]:
                charge_name = '{}.EMS.MC.{}'.format(station, f'M{month}H{item}F')
                rl_name = '{}.EMS.MC.{}'.format(station, f'M{month}H{item}P')
                charge_source_data = real_data("measure",charge_name,"db")
                charge_rl_data = real_data("measure", rl_name, "db")
                if isinstance(charge_source_data, str):
                    charge_data = json.loads(charge_source_data)
                else:
                    charge_data = charge_source_data
                if isinstance(charge_rl_data, str):
                    rl_data = json.loads(charge_rl_data)
                else:
                    rl_data = charge_rl_data
                charge_value = charge_data.get("value")
                rl_value = rl_data.get("value")
                explain_value = charge_data.get('desc')

                _charge_value.append(int(format(charge_value, '.0f')))
                _rl_value.append(int(format(rl_value * 100, '.0f')))
                explain_json.append(explain_value)

            charge_config[str(current_hour)] = _charge_value
            rl_config[str(current_hour)] = _rl_value
            explain_config[str(current_hour)] = explain_value
        return charge_config, rl_config, explain_config

    # def customize_tactics(self, station_info, detail, month, hours):
    #     # 实时策略
    #     current_charge_config, current_rl_config, explain_config = self._get_redis_data(station_info.id, station_info.name,
    #                                                             month, hours)
    #     current_charge_list = list(itertools.chain(*current_charge_config.values()))
    #     rl_charge_list = list(itertools.chain(*current_rl_config.values()))
    #     explain_list = list(itertools.chain(*explain_config.values()))
    #
    #     return current_charge_list, rl_charge_list, explain_list

    @tornado.web.authenticated
    def post(self,pk):
        new_strategy_name = self.get_argument('new_name', None) #策略名称
        session = self.getOrNewSession()
        user_id = session.user['id']
        user_name = session.user['account']
        try:
            if not new_strategy_name:
                return self.customError("必填字段缺失")
            strategy = user_session.query(TUserStrategy).filter_by(user_id=user_id, name=new_strategy_name, is_use=1).first()
            if strategy:
                return self.customError("用户控制策略另存为: 名称已存在")

            station_instance = user_session.query(Station).filter_by(name=pk).first()
            if not station_instance:
                return self.customError("保存策略站不存在")
            # 创建新策略
            new_strategy = TUserStrategy(name=new_strategy_name, user_id=user_id, user_name= user_name,category_num=1)
            user_session.add(new_strategy)
            user_session.commit()
            hours = [int(i) for i in range(0, 24)]
            hours.sort()
            is_folllow_name = '{}.EMS.MC.{}'.format(station_instance.name, "Scd")
            is_folllow_data = real_data("status", is_folllow_name, "db")
            if isinstance(is_folllow_data, str):
                is_folllow_data = json.loads(is_folllow_data)
            else:
                is_folllow_data = is_folllow_data
            current_is_folllow = int(is_folllow_data.get("value")) - 1
            if current_is_folllow == -1:
                current_is_folllow = 0
            # is_follow = False
            type_level = station_type_level.get(station_instance.name)
            # if type_level:
            #     pv = self._get_pv_status_new(type_level.get("province_id"),type_level.get("type"),type_level.get("level"),1)
            # else:
            #     pv = ["-"] * 48
            category_list = []  # 小时策略表
            for i in range(1, 13):
                current_charge_config, current_rl_config, explain_config = self._get_redis_data(station_instance.id, station_instance.name,
                                                                              i, hours)
                # charge_config.extend(current_charge_config.get(str(i)))
                # rl_list.extend(current_rl_list.get(str(i)))
                # explain_json.extend(explain_config.get(str(i)))
                charge_config = list(itertools.chain(*current_charge_config.values()))
                rl_list = list(itertools.chain(*current_rl_config.values()))
                # explain_json = list(explain_config.values())
                if type_level:
                    pv = self._get_pv_status_new(type_level.get("province_id"), type_level.get("type"),
                                                 type_level.get("level"), i)
                else:
                    pv = []
                new_category_instance = TUserStrategyCategory(
                    strategy_id=new_strategy.id,
                    name="月份{}".format(i),
                    charge_config=str(charge_config),
                    is_follow=current_is_folllow,
                    rl_list=str(rl_list),
                    pv_list=str(pv),
                    explain_json=str([]),
                )
                user_session.add(new_category_instance)
                user_session.commit()
                month = TUserStrategyCategoryMonth(month_number=i,strategy_id=new_strategy.id,
                                                   user_strategy_category_id=new_category_instance.id)
                user_session.add(month)
                user_session.commit()
                _category = self._handle_strategy_hours(charge_config, rl_list, pv, i)
                category_list.append(_category)
            source = TUserStrategySource(strategy_id=new_strategy.id, data=json.dumps(category_list))
            user_session.add(source)
            user_session.commit()
            # user_session.query(TUserStrategyCategoryMonth).filter(TUserStrategyCategoryMonth.strategy_id == new_strategy.id).update(
            #     {"user_strategy_category_id": new_category_instance.id}
            # )
            # user_session.commit()

            return self.write({"code": 200, "msg": "用户自动控制策略保存成功", "data": []})
        except Exception as e:
            logging.error(traceback.format_exc())
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
            tinalu_session.close()

# class strategySave(BaseHandler):
#     """自动控制模式配置 策略另存为"""
#     @tornado.web.authenticated
#     def post(self,pk):
#         new_strategy_name = self.get_argument('new_name', None) #策略名称
#         session = self.getOrNewSession()
#         user_id = session.user['id']
#         user_name = session.user['account']
#         if not new_strategy_name:
#             return self.customError("必填字段缺失")
#         strategy = user_session.query(TUserStrategy).filter_by(user_id=user_id, name=new_strategy_name, is_use=1).first()
#         if strategy:
#             return self.customError("用户控制策略另存为: 名称已存在")
#
#         strategy_instance = user_session.query(TUserStrategy).filter_by(id=pk).first()
#         if not strategy_instance:
#             return self.customError("另存为策略不存在")
#         old_category_instances = user_session.query(TUserStrategyCategory).filter_by(strategy_id=strategy_instance.id).all()
#         old_category_ids = [item.id for item in old_category_instances]
#         category_num = len(old_category_instances)
#         # 创建新策略
#         new_strategy = TUserStrategy(name=new_strategy_name, user_id=user_id, user_name= user_name,category_num=category_num)
#         user_session.add(new_strategy)
#         user_session.commit()
#         source_ = user_session.query(TUserStrategySource).filter_by(strategy_id=strategy_instance.id).first()
#         _data = json.loads(source_.data)
#         _data[0]['name'] = new_strategy_name
#         source_obj = TUserStrategySource(strategy_id=new_strategy.id,data=json.dumps(_data))
#         user_session.add(source_obj)
#         user_session.commit()
#         # old_strategy_hours = UserStrategyHours.objects.get(strategy=strategy_instance)
#         # UserStrategyHours.objects.create(strategy=new_strategy, data=old_strategy_hours.data)\
#         for i in range(1, 13):
#             # Month.objects.create(strategy=new_strategy, month_number=i)
#             month = TUserStrategyCategoryMonth(month_number=i,strategy_id=new_strategy.id)
#             user_session.add(month)
#         user_session.commit()
#         # 创建新分类
#         tem_months_objs = user_session.query(TUserStrategyCategoryMonth).filter(
#             TUserStrategyCategoryMonth.user_strategy_category_id.in_(old_category_ids)).all()
#         tem_months_numbers_dict = {}
#         for item in tem_months_objs:
#             if item.user_strategy_category_id in tem_months_numbers_dict.keys():
#                 tem_months_numbers_dict[item.user_strategy_category_id].append(item.month_number)
#             else:
#                 tem_months_numbers_dict[item.user_strategy_category_id] = [item.month_number]
#         for old_category_instance in old_category_instances:
#             # new_category_instance = UserStrategyCategoryNew.objects.create(strategy=new_strategy,
#             #                                                                name=old_category_instance.name,
#             #                                                                en_name=old_category_instance.en_name,
#             #                                                                charge_config=old_category_instance.charge_config,
#             #                                                                is_follow=old_category_instance.is_follow,
#             #                                                                rl_list=old_category_instance.rl_list,
#             #                                                                pv_list=old_category_instance.pv_list,
#             #                                                                explain_json=old_category_instance.explain_json,
#             #                                                                remark=old_category_instance.remark
#             #                                                                )
#             new_category_instance = TUserStrategyCategory(
#                 strategy_id=new_strategy.id,
#                 name=old_category_instance.name,
#                 charge_config=old_category_instance.charge_config,
#                 is_follow=old_category_instance.is_follow,
#                 rl_list=old_category_instance.rl_list,
#                 pv_list=old_category_instance.pv_list,
#                 explain_json=old_category_instance.explain_json,
#             )
#             user_session.add(new_category_instance)
#             user_session.commit()
#             # tem_months = old_category_instance.month_set.all()
#             # tem_months_numbers = [month.month_number for month in tem_months]
#             tem_months_numbers = tem_months_numbers_dict.get(old_category_instance.id)
#             for month in tem_months_numbers:
#                 # month_ins = Month.objects.filter(strategy=new_strategy, month_number=month).first()
#                 month_ins = user_session.query(TUserStrategyCategoryMonth).filter_by(strategy_id=new_strategy.id,
#                                                                                      month_number=month)
#                 # month_ins.is_valid = False
#                 # month_ins.user_Strategy_Category = new_category_instance
#                 # month_ins.save()
#                 month_ins.update({"user_strategy_category_id":new_category_instance.id,"is_valid": False})
#                 user_session.commit()
#         return self.write({"code": 200, "msg": "用户自动控制策略保存成功", "data": []})


class strategyList(BaseHandler):
    """自动控制模式管理 获取策略列表"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        name = self.get_argument('name', None) #策略名称
        userName = self.get_argument('userName', None) # 用户名
        startTime = self.get_argument('startTime', None) # 开始时间
        endTime = self.get_argument('endTime', None) # 截止时间
        page_size = int(self.get_argument('page_size',10))
        page_num = int(self.get_argument('page_num',1))
        try:
            session = self.getOrNewSession()
            user_id = session.user['id']
            username = session.user['account']
            # 分页参数
            offset = (page_num - 1) * page_size

            # 执行分页查询 #, TUserStrategy.user_id==user_id
            plans = (user_session.query(TUserStrategy).filter(TUserStrategy.is_use==1).
                     order_by(desc(TUserStrategy.create_time)))
            subquery = user_session.query(
                TUserStrategyHistory.station_id,
                func.max(TUserStrategyHistory.id).label('max_id')
            ).group_by(TUserStrategyHistory.station_id).subquery()
            history_obj = (user_session.query(TUserStrategyHistory.strategy_id,
                                              func.count(TUserStrategyHistory.station_id.distinct()).label("total")).
            join(subquery, (TUserStrategyHistory.id == subquery.c.max_id)).
             filter(TUserStrategyHistory.is_use == 1).group_by(TUserStrategyHistory.strategy_id).all())
            category = (user_session.query(TUserStrategyCategory.strategy_id, func.count(TUserStrategyCategory.id)).filter(TUserStrategyCategory.is_use==1).
                                           group_by(TUserStrategyCategory.strategy_id).all())
            strategy_dict = {key: value for key, value in history_obj}
            category_dict = {key: value for key, value in category}
            if name:
                plans = plans.filter(TUserStrategy.name.like(f'%{name}%'))
            if userName:
                plans = plans.filter(TUserStrategy.user_name.like(f'%{userName}%'))
            if startTime:
                startTime = f"{startTime} 00:00:00"
                plans = plans.filter(TUserStrategy.create_time >= startTime)
            if endTime:
                endTime = f"{endTime} 23:59:00"
                plans = plans.filter(TUserStrategy.create_time <= endTime)
            total = plans.count()
            plans = plans.offset(offset).limit(page_size).all()
            detail = []
            is_show = 0
            is_valid_objs = user_session.query(TUserStrategyCategoryMonth).filter_by(is_valid=1).all()
            is_valid_id = [item.strategy_id for item in is_valid_objs]
            for plan in plans:
                stationNum = strategy_dict.get(plan.id)
                planType = category_dict.get(plan.id)
                total_station = strategy_dict.get(plan.id) if strategy_dict.get(plan.id) else 0
                if plan.id in is_valid_id:
                    is_show = 1
                if not stationNum:
                    stationNum = 0
                _item = dict(
                    id=plan.id,
                    name=plan.name,
                    userName=plan.user_name,
                    categoryNum=plan.category_num,
                    # planType=planType,
                    type= 1,
                    is_show=is_show,
                    time=plan.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                    stationNum=stationNum,
                    total=total_station,
                )
                detail.append(_item)

            return self.returnTotalSuc(detail, total)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class PowerLimit(BaseHandler):
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        detail = {"power_limit": 300}
        return self.returnTypeSuc(detail)

class strategyAdd(BaseHandler):
    """自动控制模式管理 新增自定义策略
    var = [{"name": "季节01",
        "months": [
            1,
            2,
            3,
            4,
            9,
            8,
            7,
            6,
            5
        ],
        "is_follow": 1,
        "data": [
            {
                "start_time": "00:00",
                "end_time": "22:30",
                "rl": 100,
                "charge_config": 0,
                "pv": 2,
                "explain": "11111"
            },
            {
                "start_time": "22:30",
                "end_time": "23:59",
                "rl": 100,
                "charge_config": -1,
                "pv": 0,
                "explain": "222"
            }
        ]
        },
       {
           "name": "季节02",
           "months": [
               10,
               11,
               12
           ],
           "is_follow": 1,
           "data": [
               {
                   "start_time": "00:00",
                   "end_time": "23:59",
                   "rl": 100,
                   "charge_config": 1,
                   "pv": 1,
                   "explain": "2222"
               }
           ]
       }
       ]"""
    @tornado.web.authenticated
    def post(self):
        name = self.get_argument('name',None)
        category_list = self.get_argument('category_list',None)
        # data = self.request.body
        # json_data = json.loads(data)
        # name = json_data['name']
        # category_list = json_data['category_list']
        try:
            session = self.getOrNewSession()
            detail = {}
            # if not all([name, category_list]):
            #     return self.customError("必填字段缺失")
            if isinstance(category_list, str):
                category_list = ast.literal_eval(category_list)
            if not all([name,category_list]):
                return self.customError("必填字段缺失")
            create_user = session.user['id']
            username = session.user['account']
            strategy = user_session.query(TUserStrategy).filter(TUserStrategy.name==name, TUserStrategy.user_id==create_user,
                                                   TUserStrategy.is_use==1).first()
            if strategy:
                return self.customError("策略名称重复！")
            month_count = 0
            category_num = len(category_list)
            for item in category_list:
                for _ in item.get('months'):
                    month_count += 1
                day_seconds = 0
                for data in item.get('data'):
                    if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                        day_seconds = 86340
                        break
                    start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                    end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                    day_seconds += (end_time - start_time).total_seconds()
                if day_seconds != 86340:
                    return self.customError("时间校验失败；时段不足一天！")
            if month_count < 12:
                return self.customError("月份校验失败；不满12月！")
            elif month_count > 12:
                return self.customError("月份校验失败；超过12月！")
            strategy = TUserStrategy(name=name, user_id=create_user, user_name=username,category_num=category_num)
            user_session.add(strategy)
            user_session.commit()
            strategy_id = strategy.id
            source_obj = TUserStrategySource(strategy_id=strategy.id,data=json.dumps(category_list))
            user_session.add(source_obj)
            user_session.commit()
            for item in category_list:
                charge_config = []
                rl_list = []
                explain_json = []
                pv_list = []
                for data in item['data']:
                    if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                        # count = 96  # 15分钟一次
                        count = 48  # 30分钟一次
                    else:
                        start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                        end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                        count = int((end_time-start_time).total_seconds() / 1800)
                        if data.get('end_time') == '23:59':
                            count += 1
                    for i in range(count):  # 15分钟写入一次
                        charge_config.append(data.get('charge_config'))
                        rl_list.append(data.get('rl'))
                        pv_list.append(data.get('pv'))
                        explain_json.append(data.get('explain')) if data.get('explain') else ''

                category = TUserStrategyCategory(name=item.get('name'),
                                                 strategy_id=strategy_id,
                                                 charge_config=str(charge_config),
                                                 is_follow=item.get('is_follow'),
                                                 rl_list=str(rl_list),
                                                 explain_json=str(explain_json),
                                                 pv_list=str(pv_list))
                user_session.add(category)
                user_session.commit()
                # 保存月份
                for month in item.get('months'):
                    # category = TUserStrategyCategory(
                    #     name=item.get('name'),
                    #     strategy_id=strategy_id,
                    #     month_number=month,
                    #     charge_config=str(charge_config),
                    #     is_follow=item.get('is_follow'),
                    #     rl_list=str(rl_list),
                    #     explain_json=str(explain_json),
                    #     pv_list=str(pv_list))
                    month = TUserStrategyCategoryMonth(strategy_id=strategy_id, month_number=month, user_strategy_category_id=category.id)
                    user_session.add(month)
                user_session.commit()
            return self.returnTypeSuc(detail)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class strategyInfosById(BaseHandler):
    """自动控制模式管理 查看自定义策略"""
    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        _id = self.get_argument('id', None)
        month = self.get_argument('month', None)
        hours = self.get_argument('time', None)
        session = self.getOrNewSession()
        user_id = session.user['id']
        username = session.user['account']
        try:
            if not _id:
                return self.customError("必填字段缺失")
            if not str(_id).isdigit():
                return self.customError("id类型错误")
            if not month:
                month = datetime.datetime.now().month
            if not hours or hours == 'all':
                hours = [i for i in range(24)]
            else:
                hours = hours.split(',')
                hours = [int(i)-1 for i in hours]
                hours.sort()
            strategy = user_session.query(TUserStrategy).filter_by(id=_id, is_use=1).first()
            if not strategy:
                return self.customError("用户自动控制策略: 资源不存在")
            month_obj = user_session.query(TUserStrategyCategoryMonth).filter_by(month_number=month,strategy_id=_id).first()
            if not month_obj or not month_obj.user_strategy_category_id:
                return self.customError("用户自动控制策略: 资源不存在或配置不完善.")

            strategy_info = user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.is_use==1,
                                                                    TUserStrategyCategory.id==month_obj.user_strategy_category_id).first()
            # , func.max(TUserStrategyHistory.id).label('id')
            subquery = user_session.query(
                TUserStrategyHistory.station_id,
                func.max(TUserStrategyHistory.id).label('max_id')
            ).group_by(TUserStrategyHistory.station_id).subquery()
            records = (user_session.query(TUserStrategyHistory.station_id).join(subquery,(TUserStrategyHistory.id == subquery.c.max_id))
                       .filter(TUserStrategyHistory.strategy_id==_id,TUserStrategyHistory.is_use==1).group_by(TUserStrategyHistory.station_id).all())
            records_list = [i.station_id for i in records]
            record_stations = user_session.query(Station).filter(Station.id.in_(records_list)).all()
            rl_list = eval(strategy_info.rl_list)
            charge_config = eval(strategy_info.charge_config)
            explain_json = eval(strategy_info.explain_json) if strategy_info.explain_json else None
            pv_list = eval(strategy_info.pv_list)
            data = {
                'name': strategy.name,
                'month': month,
                'is_follow': strategy_info.is_follow,
                'rl_list': [],
                'charge_config': [],
                'explain_json': [],
                # 'remark': strategy_info.explain_json,
                'pv_list': [],
                'station_list': [i.descr for i in record_stations]
            }
            for i in hours:
                # s：起始位置  l: 结束位置
                # s, l = i * 4, (i+1) * 4
                s, l = i * 2, (i + 1) * 2
                _rl_list = [element for element in rl_list[s:l] for _ in range(2)]
                _charge_config = [element for element in charge_config[s:l] for _ in range(2)]
                if len(pv_list) == 96:
                    first, last = i * 4, i * 4 + 4
                    _pv_list = pv_list[first: last]
                else:
                    _pv_list = [element for element in pv_list[s:l] for _ in range(2)]
                data['rl_list'].extend(_rl_list)
                data['charge_config'].extend(_charge_config)
                data['pv_list'].extend(_pv_list)
                if explain_json:
                    _explain_json = [element for element in explain_json[s:l] for _ in range(2)]
                    data['explain_json'].extend(_explain_json)
            # data = {}
            # if strategy_info:
            #     explain_json = json.loads(strategy_info.explain_json)
            #     detail = dict(
            #         chargeConfig=strategy_info.charge_config,
            #         isFollow=strategy_info.is_follow,
            #         rlList=strategy_info.rl_list,
            #         pvList=strategy_info.pv_list,
            #         explainJson=explain_json
            #     )
            return self.returnTypeSuc(data)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class strategyUpdate(BaseHandler):
    """
    修改自定义策略
    """
    @tornado.web.authenticated
    def get(self, _id):
        # _id = self.get_argument('id', None)
        if not _id:
            return self.customError("必填字段缺失")
        if not str(_id).isdigit():
            return self.customError("id类型错误")
        try:
            strategy = user_session.query(TUserStrategy).filter_by(id=_id, is_use=1).first()
            if not strategy:
                return self.customError("用户自动控制策略:资源不存在")
            data = {
                'name': strategy.name,  # 策略名称
                'category': {}
            }
            # 查询策略分类
            category_res = user_session.query(TUserStrategySource).filter_by(strategy_id=strategy.id).first()
            if not category_res:
                return self.returnTypeSuc(data)
            data['category'] = json.loads(category_res.data)

            return self.returnTypeSuc(data)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


    @tornado.web.authenticated
    def post(self, _id):
        session = self.getOrNewSession()
        detail = {}
        name = self.get_argument('name',None)
        category_list = self.get_argument('category_list',None)
        if not all([name, category_list]):
            return self.customError("必填字段缺失")
        if isinstance(category_list, str):
            category_list = ast.literal_eval(category_list)
        # data = self.request.body
        # json_data = json.loads(data)
        # name = json_data['name']
        # category_list = json_data['category_list']
        create_user = session.user['id']
        username = session.user['account']
        try:
            strategy = user_session.query(TUserStrategy).filter(TUserStrategy.id==_id,TUserStrategy.is_use==1).first()
            if not strategy:
                return  self.customError("更新的数据不存在！！")
            is_strategy = user_session.query(TUserStrategy).filter(not_(TUserStrategy.id == _id), TUserStrategy.name==name,
                                                                   TUserStrategy.is_use==1, TUserStrategy.user_id==create_user).first()
            if is_strategy:
                return self.customError("用户自动控制策略: 策略名称重复")
            if name != strategy.name:
                descr = "将策略由'{}'修改为'{}'".format(strategy.name,name)
                _category = user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.strategy_id == _id).first()
                project_name = "未关联项目"
                if _category:
                    _station = user_session.query(Station).filter(Station.id==_category.id).first()
                    if _station:
                        project_name = _station.descr
                log = TPanLogs(project_name=project_name, station=name, user_id=create_user,
                               user_name=username, type_name='18', content=descr)
                user_session.add(log)
                user_session.commit()
            month_count = 0
            strategy_id = strategy.id
            for item in category_list:
                for _ in item.get('months'):
                    month_count += 1
                day_seconds = 0
                for data in item.get('data'):
                    if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                        day_seconds = 86340
                        break
                    start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                    end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                    day_seconds += (end_time - start_time).total_seconds()
                if day_seconds != 86340:  # 00:00-23:59 的秒数
                    return self.customError("时间校验失败；时段不足一天！")

            if month_count < 12:
                return self.customError("月份校验失败；不满12月！")
            elif month_count > 12:
                return self.customError("月份校验失败；超过12月！")
            # 先删除再写入

            category_month = user_session.query(TUserStrategyCategoryMonth).filter(TUserStrategyCategoryMonth.strategy_id==_id)
            if category_month:
                category_month.delete()
            category = user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.strategy_id==_id)
            if category:
                category.delete()
            user_session.query(TUserStrategySource).filter_by(strategy_id=strategy.id).update({'data':json.dumps(category_list)})
            user_session.query(TUserStrategy).filter(TUserStrategy.id == _id).update({"name":name,"category_num": len(category_list)})
            user_session.commit()
            for item in category_list:
                charge_config = []
                rl_list = []
                explain_json = []
                pv_list = []
                for data in item['data']:
                    if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                        # count = 96  # 15分钟一次
                        count = 48
                    else:
                        start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                        end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                        count = int((end_time-start_time).total_seconds() / 1800)
                        if data.get('end_time') == '23:59':
                            count += 1
                    for i in range(count):  # 15分钟写入一次
                        charge_config.append(data.get('charge_config'))
                        rl_list.append(data.get('rl'))
                        pv_list.append(data.get('pv'))
                        explain_json.append(data.get('explain')) if data.get('explain') else ''

                category = TUserStrategyCategory(name=item.get('name'),
                                                 strategy_id=strategy_id,
                                                 charge_config=str(charge_config),
                                                 is_follow=item.get('is_follow'),
                                                 rl_list=str(rl_list),
                                                 explain_json=str(explain_json),
                                                 pv_list=str(pv_list))
                user_session.add(category)
                user_session.commit()
                # 保存月份
                for month in item.get('months'):
                    # category = TUserStrategyCategory(
                    #     name=item.get('name'),
                    #     strategy_id=strategy_id,
                    #     month_number=month,
                    #     charge_config=str(charge_config),
                    #     is_follow=item.get('is_follow'),
                    #     rl_list=str(rl_list),
                    #     explain_json=str(explain_json),
                    #     pv_list=str(pv_list))
                    month = TUserStrategyCategoryMonth(strategy_id=strategy_id, month_number=month, user_strategy_category_id=category.id)
                    user_session.add(month)
                user_session.commit()
            return self.returnTypeSuc(detail)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class strategyDel(BaseHandler):
    """删除自定义策略"""
    @tornado.web.authenticated
    def post(self,):
        id = self.get_argument('id',None)
        if not id:
            return self.customError("必填字段缺失")
        try:
            # records_count = user_session.query(TUserStrategyHistory.strategy_id==id).filter().count()
            # if records_count > 0:
            #     return self.customError("该策略存在关联并网点，不可删除！")
            strategy = user_session.query(TUserStrategy).filter(TUserStrategy.id==id, TUserStrategy.is_use==1).first()
            if not strategy:
                return self.customError("删除资源不存在")
            user_session.query(TUserStrategy).filter(TUserStrategy.id==id).update({'is_use':0})
            user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.strategy_id==id).update({'is_use':0})
            user_session.commit()
            return self.returnTypeSuc({})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


class strategySendToDevice(BaseHandler):
    """下发自定义策略，策略配置时公用次接口"""
    @tornado.web.authenticated
    def post(self):
        _id = self.get_argument('id',None)
        station_ids = self.get_argument('station_ids',None)
        username = self.get_argument('username',None)
        password = self.get_argument('password',None)
        if not all([_id, station_ids,username, password]):
            return self.customError("必填字段缺失")
        if not str(_id).isdigit():
            return self.customError("id类型错误")
        if isinstance(station_ids, str):
            station_ids = ast.literal_eval(station_ids)
        try:
            session = self.getOrNewSession()
            # create_user = session.user['id']
            account = session.user['account']
            passwd = computeMD5(password)  # md5加密
            user = user_session.query(User).filter(or_(User.account == username, User.phone_no == username),
                                                   User.unregister == 1).first()
            if user.passwd != passwd:
                return self.customError("用户名或密码错误")
            if username != account:
                return self.customError("下发账号与登录账号不符")
            stations = user_session.query(Station).filter(Station.name.in_(station_ids)).all()
            if not stations:
                return self.customError("自动控制策略下发: 站信息不存在！")
            strategy_ins = user_session.query(TUserStrategy).filter(TUserStrategy.id == _id, TUserStrategy.is_use==1).first()
            if not strategy_ins:
                return self.customError("自动控制策略下发: 策略信息不存在!")
            # 查询分类
            category_instances = user_session.query(TUserStrategyCategory).filter(TUserStrategyCategory.strategy_id == _id,
                                                                                   TUserStrategyCategory.is_use==1).all()
            if not len(category_instances):
                return self.customError("自动控制策略下发: 策略-分类为空！")
            month_category = {}
            for item in category_instances:
                if item.id not in month_category.keys():
                    month_category[item.id] = item
            category_ids = [item.id for item in category_instances]
            # client = mqtt.Client()
            # client.on_connect = on_connect
            # client.on_message = on_message
            # client.username_pw_set(model_config_base.get('mqtt', "MQTT_USER"), model_config_base.get('mqtt', "MQTT_PASSWORD"))
            # client.connect(
            #     host=model_config_base.get('mqtt', "MQTT_SERVER"),
            #     port=model_config_base.get('mqtt', "MQTT_PORT"),
            #     keepalive=model_config_base.get('mqtt', "MQTT_KEEPALIVE"),
            # )
            # client.loop_start()

            # 下发时段处理
            issue_hour_list = [i for i in range(1, 24)]
            issue_hour_list.append(0)
            category_month_objs = user_session.query(TUserStrategyCategoryMonth).filter(TUserStrategyCategoryMonth.
                                                                                        user_strategy_category_id.in_(
                category_ids)).all()
            category_month_dict = {item.month_number:item.user_strategy_category_id for item in category_month_objs}

            # for item in category_month_objs:
            #     if item.month_number not in category_month_dict.keys():
            #         category_month_dict[item.month_number] = [item.user_strategy_category_id]
            #     else:
            #         category_month_dict[item.user_strategy_category_id].append(item.month_number)
            # category_month = {item.user_strategy_category_id: item for item in category_month_objs}
            # print(category_month_dict)
            strategy_data = user_session.query(TStrategyData).filter(TStrategyData.is_use==1).all()
            # strategy_data_dict = {item.month:item for item in strategy_data}
            temp_dict = {}
            for category_month in strategy_data:
                # _item = {}
                _month = category_month.month

                _category_id = category_month_dict[_month]
                month_category_obj = month_category.get(_category_id)
                temp_dict[_month] = {"is_follow":month_category_obj.is_follow,"data":[]}
                rl_list = eval(month_category_obj.rl_list)
                # charge_config = eval(month_category_obj.charge_config)
                charge_config_list = eval(month_category_obj.charge_config)
                # 使用切片将第一个元素移到最后
                rl_list_new = rl_list[2:] + rl_list[:2]
                charge_config_list_new = charge_config_list[2:] + charge_config_list[:2]
                value_p = eval(category_month.value_p)
                value_f = eval(category_month.value_f)
                temp_dict[_month]["data"].append(value_p[0].format(*rl_list_new[::2]))
                temp_dict[_month]["data"].append(value_p[1].format(*rl_list_new[1::2]))
                temp_dict[_month]["data"].append(value_f[0].format(*charge_config_list_new[::2]))
                temp_dict[_month]["data"].append(value_f[1].format(*charge_config_list_new[1::2]))
                # temp_list.append(_item)
            # print(temp_list)
            for station in stations:
                # print(station.name,station.descr)
                name = station.name
                station_app = station.descr
                descr = "下发了策略：{}".format(strategy_ins.name)
                log = TPanLogs(project_name=station.descr, station=name,user_id=user.id,
                               user_name=user.account, type_name='19', content=descr)
                user_session.add(log)
                user_session.commit()
                # station_english_name = station.english_name

                # 准备下发策略
                # topic = f"req/database/parameter/{station_english_name}/{station_app}"
                # secret_key = model_config_base.get('aes_key', "AES_KEY")
                # aes = EncryptDate(secret_key)
                #
                # token = aes.encrypt(name)

                message = {
                    "time": str(int(time.time())),
                    # "token": token,
                    "device": "EMS",
                    "is_send_device": "0",
                    "body": temp_dict,
                    'station': name,

                }
                json_message = json.dumps(message)
                issuance = TUserStrategyIssuanceSource(station=name,strategy_id=strategy_ins.id,data=json_message)
                user_session.add(issuance)
                user_session.commit()
                key = f"telecontrol_strategy_{name}"
                r_real.set(key,json_message)
                # try:
                #     client.publish(topic, json_message)
                #     print("下发策略内容：", json_message)
                # except Exception as e:
                #     logging.error('自动控制策略下发: 失败: {}'.format(e))
                #     return self.customError("自动控制策略下发: 下发失败！")
                # time.sleep(0.2)  # 增加0.2秒延迟，同一时间下发网关机会覆盖下发策略
                # print(message)
            try:
                for station in stations:
                    history = TUserStrategyHistory(name=strategy_ins.name,user_id=user.id,user_name=user.account,station_id=station.id,
                                         strategy_id=strategy_ins.id)
                    user_session.add(history)
                    user_session.commit()
                    for category in category_instances:
                        category_history = TUserStrategyCategoryHistory(
                            name=category.name,
                            station_id=station.id,
                            charge_config=category.charge_config,
                            is_follow=category.is_follow,
                            rl_list=category.rl_list,
                            pv_list=category.pv_list,
                            explain_json=category.explain_json,
                            user_strategy_category_id=history.id
                        )
                        user_session.add(category_history)
                        user_session.commit()
                        for month in category_month_dict.keys():
                            month_history = TUserStrategyCategoryMonthHistory(month_number=month, strategy_history_id=history.id,
                                                              user_strategy_category_history_id=category_history.id)
                            user_session.add(month_history)
                        user_session.commit()
            except Exception as e:
                logging.error(f'自动控制策略下发: 日志写入失败 {e}')
                user_session.rollback()
                return self.customError("自动控制策略下发: 日志写入失败！")
            finally:
                user_session.close()
            # client.loop_stop()
            return self.write({"code": 200, "msg": "自动控制策略下发成功", "data": message})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

    def get(self):
        key = self.get_argument('key', None)
        if not key:
            key = f"telecontrol_strategy_sikly"
        data = r_real.get(key)
        return self.write({"code": 200, "msg": "查询成功", "data": json.loads(data)})


class StrategyTemplate(BaseHandler):
    """功率计划电站容量信息获取"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        minioClient = MinioTool()
        url = minioClient.get_download_url('tianlu', '策略模板.xlsx')
        return self.write({"code": 200, "msg": "策略模板下载成功", "data": url})

class StrategyImport(BaseHandler):
    """功率计划电站容量信息获取"""
    @tornado.web.authenticated
    def post(self):
        self.refreshSession()
        files = self.request.files.get('files')
        if files:
            try:
                excel_file = BytesIO(files[0].body)
                wb = load_workbook(excel_file)
                data = []
                for i in wb:
                    res = []
                    st = wb[i.title]
                    for _i in range(7, st.max_row + 1):
                        res.append({
                            'start_time': st.cell(_i, 1).value.strftime('%H:%M:%S'),
                            'end_time': st.cell(_i, 2).value.strftime('%H:%M:%S'),
                            'pv': st.cell(_i, 3).value,
                            'charge_config': st.cell(_i, 4).value,
                            'rl': st.cell(_i, 1).value.strftime('%H:%M:%S'),
                        })
                    data.append(
                        {
                            'name': i.title,
                            'data': res,
                            'month_list': st['B5'].value.split(','),
                        }
                    )
            except Exception as e:
                print(f'模板错误信息：{e}')
                logging.error(f'模板错误信息：{e}')
                return self.customError("模板错误，请检查！")
            return self.returnTypeSuc(data)
        else:
            return self.customError("未上传附件")


class PowerPlanStations(BaseHandler):
    """功率计划电站容量信息获取"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        try:
            stations = user_session.query(Station).filter().all()
            t_station_relation = user_session.query(StationR).filter().all()
            relation = {}
            for item in t_station_relation:
                real_power = 0
                active_power_name = item.active_power_name
                if active_power_name:
                    active_power_name = ast.literal_eval(active_power_name)
                    for key in active_power_name:
                        value = real_data("measure",key,"db")
                        if value:
                            real_power += round(value.get("value"), 2)
                relation[item.station_name] = dict(
                    electric_power=item.electric_power,
                    real_power= round(real_power / 1000,2),
                )
            detail = []
            """
            id: 1
            project_id: 97
            project_name: "安胜科技"
            rated_capacity: 232.96
            rated_power: 100
            realtime_power: 0
            station_id: 103
            station_name: "安胜科技001"
            """
            for station in stations:
                _relation = relation.get(station.name)
                if _relation:
                    rated_power = _relation.get("electric_power"),
                    realtime_power=_relation.get("real_power")
                else:
                    rated_power = "--"
                    realtime_power = "--"
                if isinstance(rated_power, tuple):
                    rated_power = rated_power[0]
                _item = dict(
                    station_id=station.id,
                    station_name=station.name,
                    descr=station.descr,
                    rated_capacity=station.volume,
                    rated_power=rated_power,
                    realtime_power=realtime_power
                )
                detail.append(_item)
            return self.returnTypeSuc(detail)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class PowerPlanStationsRefresh(BaseHandler):
    """功率计划电站容量信息刷新"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        station_id = self.get_argument('station_id', None)
        if not station_id:
            return self.customError("请选择电站")
        if isinstance(station_id, str):
            station_id = eval(station_id)
        try:
            stations = user_session.query(Station).filter(Station.id.in_(station_id)).all()
            names = [item.name for item in stations]
            t_station_relations = user_session.query(StationR).filter(StationR.station_name.in_(names)).all()
            relation = {}
            real_power = 0
            data = []
            for t_station_relation in t_station_relations:
                active_power_name = t_station_relation.active_power_name
                if active_power_name:
                    active_power_name = ast.literal_eval(active_power_name)
                    for key in active_power_name:
                        value = real_data("measure", key, "db")
                        if value:
                            real_power += round(value.get("value"), 2)
                relation[t_station_relation.station_name] = dict(
                    electric_power=t_station_relation.electric_power,
                    real_power=round(real_power / 1000,2),
                )
                # _relation = relation.get(t_station_relation.station_name)
                # if _relation:
                #     rated_power = _relation.get("electric_power"),
                #     realtime_power=_relation.get("real_power")
                # else:
                #     rated_power = "--"
                #     realtime_power = "--"
                # if isinstance(rated_power, tuple):
                #     rated_power = rated_power[0]
            for station in stations:
                _relation = relation.get(station.name)
                if _relation:
                    rated_power = _relation.get("electric_power"),
                    realtime_power=_relation.get("real_power")
                else:
                    rated_power = "--"
                    realtime_power = "--"
                if isinstance(rated_power, tuple):
                    rated_power = rated_power[0]
                detail = dict(
                    station_id=station.id,
                    station_name=station.name,
                    descr=station.descr,
                    rated_capacity=station.volume,
                    rated_power=round(rated_power,2) if rated_power != "--" else rated_power,
                    realtime_power=round(realtime_power,2) if realtime_power != "--" else realtime_power
                )
                data.append(detail)
            return self.returnTypeSuc(data)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class PowerPlanList(BaseHandler):
    """功率计划下发列表"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        name = self.get_argument('name', None)
        status = self.get_argument('status', None) # 1已保存；2已下发；3执行中，4已完成，5下发失败 6 已停止
        planType = self.get_argument('planType', None)
        startTime = self.get_argument('startTime', None)
        endTime = self.get_argument('endTime', None)
        lang = self.get_argument('lang', None)  # 英文
        page_size = int(self.get_argument('page_size',10))
        page_num = int(self.get_argument('page_num',1))

        # 分页参数
        offset = (page_num - 1) * page_size
        session = self.getOrNewSession()
        user_id = session.user['id']
        # 子查询获取每个power_id的最大id
        try:
            subq = (
                user_session.query(
                    TPlanPowerRecords.power_id,
                    func.max(TPlanPowerRecords.id).label('max_id')
                )
                .join(TPowerDeliverRecords, TPlanPowerRecords.power_id == TPowerDeliverRecords.id)
                .filter(TPowerDeliverRecords.user_id == user_id)
                .group_by(TPlanPowerRecords.power_id)
                .subquery()
            )

            # 主查询
            query = (
                user_session.query(TPlanPowerRecords)
                .join(subq, TPlanPowerRecords.id == subq.c.max_id)
                .join(TPowerDeliverRecords, TPlanPowerRecords.power)
                .join(TPlanHistory, TPlanPowerRecords.plan)
            ).filter(TPowerDeliverRecords.is_use==1, TPlanHistory.is_use==1)
            if name:
                query = query.filter(TPowerDeliverRecords.name.contains(name))
            if status:
                query = query.filter(TPlanHistory.status == status)
            if planType:
                query = query.filter(TPowerDeliverRecords.plan_type==planType)
            if startTime:
                query = query.filter(TPlanHistory.start_time >= startTime)
            if endTime:
                query = query.filter(TPlanHistory.start_time <= endTime)
            # 应用过滤条件
            # if start_time and end_time:
            #     query = query.filter(
            #         and_(
            #             Plan.start_time >= start_time,
            #             Plan.end_time <= end_time
            #         )
            #     )

            # 排序
            query = query.order_by(TPlanPowerRecords.power_id.desc())

            # 分页
            total = query.count()
            res = query.offset(offset).limit(page_size).all()

            # 构建响应数据
            detail = []
            for record in res:
                power = record.power
                plan = record.plan
                power_list = power.power_list
                descr = ""
                if isinstance(power_list, str):
                    power_list = ast.literal_eval(power_list)
                if int(power.plan_type) == 1:
                    descr = "{}-{}".format(plan.start_time.strftime("%Y-%m-%d %H:%M:%S"), plan.end_time.strftime("%Y-%m-%d %H:%M:%S"))
                if int(power.plan_type) == 2:
                    cron_data = power_list[0].get("cron_data")
                    start_time = power_list[0].get("start_time")
                    end_time = power_list[0].get("end_time")
                    descr = "{}({}-{})".format(cron_data, start_time, end_time)
                if int(power.plan_type) == 3:
                    cron_data = power_list[0].get("cron_data")
                    start_time = power_list[0].get("start_time")
                    end_time = power_list[0].get("end_time")
                    descr = "{}({}-{})".format(cron_data, start_time, end_time)
                p_dict = {
                    'id': power.id,
                    'name': power.name,
                    'status': plan.status,
                    "planType": power.plan_type,
                    "is_start": power.is_start,
                    'start_time': plan.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": plan.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "adjusting_time": descr,
                    "power": plan.power,
                    'planNum': len(json.loads(power.power_list)),
                    'stationNum': len(json.loads(power.station_list))
                }
                detail.append(p_dict)
                # detail.append(dict(
                #     id=plan.TPowerDeliverRecords.id,
                #     name=plan.TPowerDeliverRecords.name,
                #     planNum=plan_num,
                #     stationNum=station_num,
                #     planType=plan.TPowerDeliverRecords.plan_type,
                #     status=plan.TPlanHistory.status,
                #     is_start=plan.TPowerDeliverRecords.is_start,
                #     time=_time_list,
                # ))
                # data.append(p_dict)
            # # 执行分页查询
            # plans = (user_session.query(func.distinct(TPowerDeliverRecords.id),TPowerDeliverRecords,TPlanHistory).filter(
            #             TPlanPowerRecords.power_id==TPowerDeliverRecords.id,TPlanPowerRecords.plan_id==TPlanHistory.id,
            #             TPowerDeliverRecords.user_id==user_id
            #              ,TPowerDeliverRecords.is_use==1).order_by(desc(TPowerDeliverRecords.create_time))).distinct(TPowerDeliverRecords.id)
            #
            # if status:
            #     plans = plans.filter(TPlanHistory.status==status)
            # if planType:
            #     plans = plans.filter(TPowerDeliverRecords.plan_type==planType)
            # if startTime:
            #     plans = plans.filter(TPowerDeliverRecords.create_time >= startTime)
            # if endTime:
            #     plans = plans.filter(TPowerDeliverRecords.create_time <= endTime)
            # total = plans.count()
            # plans = plans.offset(offset).limit(page_size).all()
            # detail = []
            # for plan in plans:
            #     _time_list = []
            #     power_str = plan.TPowerDeliverRecords.power_list
            #     power_list = json.loads(power_str)
            #     for item in power_list:
            #         start_time = item.get("start_time")
            #         end_time = item.get("end_time")
            #         _time = "{}-{}".format(start_time, end_time)
            #         _time_list.append(_time)
            #     plan_num = len(power_list)
            #     station_str = plan.TPowerDeliverRecords.station_list
            #     station_list = json.loads(station_str)
            #     if isinstance(station_list,str):
            #         station_list = ast.literal_eval(station_list)
            #     station_num = len(station_list)
            #
            #     detail.append(dict(
            #         id=plan.TPowerDeliverRecords.id,
            #         name=plan.TPowerDeliverRecords.name,
            #         planNum=plan_num,
            #         stationNum=station_num,
            #         planType=plan.TPowerDeliverRecords.plan_type,
            #         status=plan.TPlanHistory.status,
            #         is_start=plan.TPowerDeliverRecords.is_start,
            #         time=_time_list,
            #     ))
            user_session.close()
            return self.returnTotalSuc(detail, total)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

def cron_tab_date(cron_tab):
    """根据cron_tab计算第一次执行日期"""
    # 定义要获取的节假日名称
    holiday_names = ["元旦", "春节", "清明节", "劳动节", "端午节", "国庆节", "中秋节"]

    # 获取当前年份
    current_year = datetime.datetime.now().year
    # # 创建一个中国节假日对象
    # china_holidays = holidays.China(years=current_year)
    # print(china_holidays)

    # 遍历节假日名称列表，获取每个节假日的第一天日期
    if cron_tab in holiday_names:
        # 获取当年的所有节假日
        _date = None
        obj = user_session.query(THolidayTable).filter_by(year=current_year,holiday_name=cron_tab).first()
        if obj:
            _date = obj.start_day
        return _date
    else:
        weekday_mapping = {
            '星期一': 0, '星期二': 1, '星期三': 2, '星期四': 3, '星期五': 4, '星期六': 5, '星期日': 6
        }
        # 获取当前日期
        today = datetime.datetime.today()
        # 获取当前日期是星期几（0 代表星期一，6 代表星期日）
        current_weekday = today.weekday()
        # 获取目标星期对应的数字
        target_weekday = weekday_mapping[cron_tab]
        if current_weekday >= target_weekday:
            target_week = "next"
        else:
            target_week = 'this'

        if target_week == 'this':
            # 计算当前周内目标星期与今天的天数差
            days_ahead = (target_weekday - current_weekday) % 7
            # 获取当前周内目标星期的日期
            target_date = today + datetime.timedelta(days=days_ahead)
        else:
            # 计算下一周内目标星期与今天的天数差
            days_ahead = (7 + target_weekday - current_weekday) % 7
            # 获取下一周内目标星期的日期
            target_date = today + datetime.timedelta(days=days_ahead)

        return target_date.date()

def data_check_power(powerList, planType, stationList):
    """验证功率下发数据"""
    power_dict = {}  # 排序用
    for power in powerList:
        cron_data = None
        if isinstance(power, str):
            power = ast.literal_eval(power)
        if int(planType) == 1:
            start_time = power.get("start_time")
            end_time = power.get("end_time")
        elif int(planType) == 2:
            cron_data = power.get("cron_data")
            start_time = power.get("start_time")
            end_time = power.get("end_time")
        else:
            cron_data = power.get("cron_data")
            start_time = power.get("start_time")
            end_time = power.get("end_time")
        option = power.get("option")
        limit = power.get("limit")
        is_follow = power.get("is_follow")
        if not all([start_time, end_time]):
            # return self.customError("执行时间不能空")
            return False, "执行时间不能空"
        if int(planType) == 1:
            start_time = datetime.datetime.strptime(power.get('start_time'), "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.strptime(power.get('end_time'), "%Y-%m-%d %H:%M:%S")
            difference = (end_time - start_time).total_seconds()
            now_time = datetime.datetime.now()
            now_difference = (start_time - now_time).total_seconds()
            if now_difference < 0:
                # return self.customError('时间范围错误：开始时间不能小于当前时间！')
                return False, "时间范围错误：开始时间不能小于当前时间！"
            if difference <= 0:
                # return self.customError('时间范围错误：结束时间不能小于等于开始时间！')
                return False, "时间范围错误：结束时间不能小于等于开始时间！"
            if power_dict.get(start_time):
                # return self.customError('任务时间重合，新增失败！')
                return False, "任务时间重合，新增失败！"
            else:
                power_dict[start_time] = power
        else:
            if int(planType) == 2:
                execution_date = cron_tab_date(cron_data)
                start_time = "{} {}".format(execution_date, start_time)
                end_time = "{} {}".format(execution_date, end_time)
            if int(planType) == 3:
                execution_date = cron_tab_date(cron_data)
                start_time = "{} {}".format(execution_date, start_time)
                end_time = "{} {}".format(execution_date, end_time)
            if cron_data is None:
                # return self.customError("周期性、节假日的周期时间不能空")
                return False, "周期性、节假日的周期时间不能空"
        repeat = False
        if isinstance(start_time,str):
            start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        if isinstance(end_time,str):
            end_time = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        for station in stationList:
            station_name = station.get("station_name")
            plans = user_session.query(TPlanHistory).filter(TPlanHistory.station==station_name).all()
            for plan in plans:
                _start_time = plan.start_time
                _end_time = plan.end_time
                if _start_time <= start_time <= _end_time:
                    repeat = True
                if _start_time <= end_time <= _end_time:
                    repeat = True
        # if repeat:
        #     return False, "与已有任务时间重合，请修改后下发"
        if limit is None:
            # return self.customError("限值不能为空")
            return False, "限值不能为空"
        if not option and option != 0:
            # return self.customError("充放电选项不能为空!")
            return False, "充放电选项不能为空"
        if is_follow is None:
            # return self.customError("负荷跟随不能为空!")
            return False, "负荷跟随不能为空"
    # 根据开始时间排序
    if int(planType) == 1:
        power_dict = sorted(power_dict.items(), key=lambda x: x[0])  # 根据key排序
        s_time_list = []
        e_time_list = []
        for i, v in enumerate(power_dict):
            power = v[1]
            s_time = datetime.datetime.strptime(power.get('start_time'), '%Y-%m-%d %H:%M:%S')
            e_time = datetime.datetime.strptime(power.get('end_time'), '%Y-%m-%d %H:%M:%S')
            s_time_list.append(s_time)
            e_time_list.append(e_time)
            power['serial_number'] = i + 1
        # 判断时间是否重合
        for i in range(len(s_time_list) - 1):
            differ_seconds = (s_time_list[i + 1] - e_time_list[i]).total_seconds()
            if differ_seconds < 0:
                # return self.customError('任务时间重合，新增失败!')
                return False, "任务时间重合，新增失败!"
    return True,""

class PowerPlanAdd(BaseHandler):
    """新增计划功率"""
    @tornado.web.authenticated
    def post(self,):
        self.refreshSession()
        planType = self.get_argument('planType',None) # 1 自定义 2 周期性 3 节假日
        powerList = self.get_argument('powerList',None)
        stationList = self.get_argument('stationList',None)
        account = self.get_argument('account',None)
        password = self.get_argument('password',None)
        plan_name = self.get_argument('planName',None)

        if not all([planType, powerList,stationList]):
            return self.customError("必填字段缺失")
        if not all([account,password]):
            return self.customError("请输入账号密码！")
        try:
            session = self.getOrNewSession()
            current_user = session.user
            validate, msg = validate_user_passwd(account, password)
            if not validate:
                return self.customError(msg)
            if current_user.get("account") != account:
                return self.customError("下发账号与登录账号不符")
            if isinstance(powerList, str):
                powerList = ast.literal_eval(powerList)
            if isinstance(stationList, str):
                stationList = ast.literal_eval(stationList)
            if len(stationList) == 0:
                return self.customError("电站信息不能为空！")
            is_check, msg = data_check_power(powerList,planType, stationList)
            if not is_check:
                return self.customError(msg)
            power_str = json.dumps(powerList)
            station_str = json.dumps(stationList)
            is_flag = user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.name==plan_name,
                        TPowerDeliverRecords.user_id==current_user.get("id"), TPowerDeliverRecords.is_use==1).first()
            if is_flag:
                return self.customError("计划功率名称重复")

            record = TPowerDeliverRecords(name=plan_name,power_list=power_str,station_list=station_str,user_id=current_user.get("id")
                                          ,user_name=current_user.get("account"),plan_type=int(planType))
            user_session.add(record)
            user_session.commit()
            record_id = record.id
            his_ids = []

            try:
                for station in stationList:
                    station_name = station.get("station_name")
                    station_id = station.get("station_id")
                    for power in powerList:
                        cron_data = power.get("cron_data")
                        start_time = power.get("start_time")
                        end_time = power.get("end_time")
                        serial_number = power.get("serial_number")
                        descr = ""
                        if int(planType) == 1:
                            descr = "下发了功率任务：自定义任务{}-{}".format(start_time,end_time)
                        if int(planType) == 2:
                            execution_date = cron_tab_date(cron_data)
                            start_time = "{} {}".format(execution_date,start_time)
                            end_time = "{} {}".format(execution_date, end_time)
                            descr = "下发了功率任务：{}{}-{}".format(cron_data,start_time,end_time)
                        if int(planType) == 3:
                            execution_date = cron_tab_date(cron_data)
                            start_time = "{} {}".format(execution_date,start_time)
                            end_time = "{} {}".format(execution_date, end_time)
                            descr = "下发了功率任务：{}{}-{}".format(cron_data, start_time, end_time)
                        if isinstance(power, str):
                            power = ast.literal_eval(power)
                        # descr = "下发了功率任务：{}".format(plan_name)
                        print(power,station,333)
                        _power = round(float(power.get('option')) * power.get('limit') * float(station.get('rated_power')) * 1000, 2)
                        his = TPlanHistory(name=plan_name,status=1,power=_power,plan_type=int(planType),start_time=start_time,end_time=end_time,
                                           is_follow=power.get("is_follow"),user_id=current_user.get("id"),
                                           user_name=current_user.get("account"),type_name="17",descr=descr,station=station_name)
                        user_session.add(his)
                        user_session.commit()
                        log = TPanLogs(project_name=station.get("descr"), station=station.get("station_name"),
                        user_id=current_user.get("id"),user_name=current_user.get("account"),type_name='17', content=descr)
                        user_session.add(log)
                        user_session.commit()
                        his_ids.append(his)
                        relation = TPlanPowerRecords(plan_id=his.id,power_id=record.id,serial_number=serial_number)
                        user_session.add(relation)
                        user_session.commit()

                # # 提交会话
                # user_session.commit()
            except Exception as e:
                logging.error(e)
                user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id==record_id).update({TPowerDeliverRecords.is_use: 0})
                user_session.query(TPlanHistory).filter(TPlanHistory.id.in_(his_ids)).update({TPlanHistory.is_use: 0})
            finally:
                user_session.commit()

            return self.returnTypeSuc({})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class PowerPlanDetail(BaseHandler):
    """功率计划下发指令详情"""

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        id = self.get_argument('id', None)
        if not id:
            return self.customError("id值不能为空")
        try:
            record = user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id==id, TPowerDeliverRecords.is_use==1).first()

            if not record:
                return self.customError("查询数据不存在")

            records_objs = user_session.query(TPlanPowerRecords).filter(TPlanPowerRecords.power_id == record.id).all()
            serial_number_id = {}
            for item in records_objs:
                if item.serial_number in serial_number_id.keys():
                    serial_number_id[item.serial_number].append(item.plan_id)
                else:
                    serial_number_id[item.serial_number] = [item.plan_id]
            power_list = record.power_list
            if isinstance(power_list, str):
                power_list = ast.literal_eval(power_list)
            station_list = record.station_list
            if isinstance(station_list, str):
                station_list = ast.literal_eval(station_list)
            for power in power_list:
                plan_id = serial_number_id.get(power.get("serial_number"))
                print(plan_id, serial_number_id)
                status_obj = (user_session.query(TPlanHistory).filter(TPlanHistory.id.in_(plan_id),TPlanHistory.is_use==1).
                              group_by(TPlanHistory.status).order_by(func.max(TPlanHistory.id)).first())
                power["status"] = status_obj.status
            detail = dict(name=record.name,power_list=power_list,station_list=station_list,user_name=record.user_name,
                          plan_type=record.plan_type)

            return self.returnTypeSuc(detail)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class PowerPlanUpdate(BaseHandler):
    """修改计划功率内容"""
    @tornado.web.authenticated
    def post(self):
        self.refreshSession()
        planType = self.get_argument('planType',None) # 1 自定义 2 周期性 3 节假日
        powerList = self.get_argument('powerList',None)
        stationList = self.get_argument('stationList',None)
        account = self.get_argument('account',None)
        password = self.get_argument('password',None)
        plan_name = self.get_argument('planName',None)
        id = self.get_argument('id',None)

        if not all([planType, powerList,stationList]):
            return self.customError("必填字段缺失")
        if not all([account,password]):
            return self.customError("请输入账号密码！")
        if not id:
            return self.customError("id值不能为空")
        try:
            session = self.getOrNewSession()
            current_user = session.user
            validate, msg = validate_user_passwd(account, password)
            if not validate:
                return self.customError(msg)
            if current_user.get("account") != account:
                return self.customError("下发账号与登录账号不符")
            if isinstance(powerList, str):
                powerList = ast.literal_eval(powerList)
            if isinstance(stationList, str):
                stationList = ast.literal_eval(stationList)
            if len(stationList) == 0:
                return self.customError("电站信息不能为空！")
            is_check, msg = data_check_power(powerList,planType, stationList)
            if not is_check:
                return self.customError(msg)
            power_str = json.dumps(powerList)
            station_str = json.dumps(stationList)
            record = user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id == id).first()
            if not record:
                return self.customError("修改数据不存在，请核实后下发！")

            # record = TPowerDeliverRecords(name=plan_name,power_list=power_str,station_list=station_str,user_id=current_user.get("id")
            #                               ,user_name=current_user.get("account"),plan_type=int(planType))
            user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id == id).update(
                {TPowerDeliverRecords.name: plan_name,TPowerDeliverRecords.power_list:power_str,TPowerDeliverRecords.station_list:station_str,
                 TPowerDeliverRecords.user_id:current_user.get("id"),TPowerDeliverRecords.user_name:current_user.get("account"),
                 TPowerDeliverRecords.plan_type:int(planType)})

            # user_session.add(record)
            user_session.commit()
            relation = user_session.query(TPlanPowerRecords).filter(TPlanPowerRecords.power_id==id).all()
            _his_ids = [item.plan_id for item in relation]
            user_session.query(TPlanHistory).filter(TPlanHistory.id.in_(_his_ids)).update({TPlanHistory.is_use: 0})
            his_ids = []
            try:
                for station in stationList:
                    station_name = station.get("station_name")
                    station_id = station.get("station_id")
                    for power in powerList:
                        cron_data = power.get("cron_data")
                        start_time = power.get("start_time")
                        end_time = power.get("end_time")
                        serial_number = power.get("serial_number")
                        if int(planType) == 2:
                            execution_date = cron_tab_date(cron_data)
                            start_time = "{} {}".format(execution_date,start_time)
                            end_time = "{} {}".format(execution_date, end_time)
                        if int(planType) == 3:
                            execution_date = cron_tab_date(cron_data)
                            start_time = "{} {}".format(execution_date,start_time)
                            end_time = "{} {}".format(execution_date, end_time)
                        if isinstance(power, str):
                            power = ast.literal_eval(power)
                        descr = "下发了功率任务：{}".format(plan_name)
                        print(power,station,333)
                        _power = round(float(power.get('option')) * power.get('limit') * float(station.get('rated_power')), 2)
                        his = TPlanHistory(name=plan_name,status=1,power=_power,plan_type=int(planType),start_time=start_time,end_time=end_time,
                                           is_follow=power.get("is_follow"),user_id=current_user.get("id"),
                                           user_name=current_user.get("account"),descr=descr,station=station_name)
                        user_session.add(his)
                        user_session.commit()
                        his_ids.append(his.id)
                        relation = TPlanPowerRecords(plan_id=his.id, power_id=record.id,serial_number=serial_number)
                        user_session.add(relation)
                        user_session.commit()

                # # 提交会话
                # user_session.commit()
            except Exception as e:
                logging.error(e)
                user_session.rollback()
                return self.requestError()
            finally:
                user_session.close()

            return self.returnTypeSuc({})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class powerPlanStop(BaseHandler):
    """停止计划功率内容"""

    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        id = self.get_argument('id', None)
        lang = self.get_argument('lang', None)  # 英文

        if not id:
            return self.customError("id值不能为空")
        try:
            session = self.getOrNewSession()
            user_id = session.user['id']
            record = user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id==id,TPowerDeliverRecords.user_id
                                                                     ==user_id).first()
            if not record:
                return self.customError("查询数据不存在")
            record.is_start = 0
            user_session.commit()
            relation = user_session.query(TPlanPowerRecords).filter(TPlanPowerRecords.power_id==record.id).all()
            relation_ids =  [item.plan_id for item in relation]
            user_session.query(TPlanHistory).filter(TPlanHistory.id.in_(relation_ids)).update({TPlanHistory.status: 6,
                                                                    TPlanHistory.descr: "停止功率任务：{}".format(record.name)})
            # 提交会话
            user_session.commit()
            return self.returnTypeSuc("")
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class powerPlanDelete(BaseHandler):
    """功率计划下发删除"""

    @tornado.web.authenticated
    def get(self, ):
        self.refreshSession()
        id = self.get_argument('id', None)
        account = self.get_argument('account',None)
        password = self.get_argument('password',None)
        try:
            session = self.getOrNewSession()
            current_user = session.user
            if current_user.get("account") != account:
                return self.customError("下发账号与登录账号不符")
            validate, msg = validate_user_passwd(account, password)
            if not validate:
                return self.customError(msg)
            if not id:
                return self.customError("id值不能为空")
            session = self.getOrNewSession()
            user_id = session.user['id']  #,TPowerDeliverRecords.user_id == user_id
            record = user_session.query(TPowerDeliverRecords).filter(TPowerDeliverRecords.id==id).first()
            if not record:
                return self.customError("查询数据不存在")
            record.is_use = 0
            user_session.commit()
            relation = user_session.query(TPlanPowerRecords).filter(TPlanPowerRecords.power_id==record.id).all()
            relation_ids =  [item.plan_id for item in relation]
            user_session.query(TPlanHistory).filter(TPlanHistory.id.in_(relation_ids)).update({TPlanHistory.is_use: 0})
            # 提交会话
            user_session.commit()
            return self.returnTypeSuc("")
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class GetPlanHis(BaseHandler):
    """查询下发记录列表"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        station = self.get_argument('station', None) # 项目名
        status = self.get_argument('status', None) # 下发结果 1成功；2失败
        typeName = self.get_argument('typeName', None) # 下发类型 下发分类名称
        startTime = self.get_argument('startTime', None)
        endTime = self.get_argument('endTime', None)
        page_size = int(self.get_argument('page_size',10))
        page_num = int(self.get_argument('page_num',1))

        # 分页参数
        offset = (page_num - 1) * page_size
        try:
            # 执行分页查询
            plans = user_session.query(TPanLogs).filter(TPanLogs.is_use==1).order_by(desc(TPanLogs.create_time))
            if station:
                plans = plans.filter(TPanLogs.station.contains(station))
            if status:
                plans = plans.filter(TPanLogs.status==status)
            if typeName:
                typeName = typeName.split(',')
                plans = plans.filter(TPanLogs.type_name.in_(typeName))
            if startTime:
                plans = plans.filter(TPanLogs.create_time >= startTime)
            if endTime:
                plans = plans.filter(TPanLogs.create_time <= endTime)
            # 计算总数
            total = plans.count()
            plans = plans.offset(offset).limit(page_size).all()
            detail = []
            for plan in plans:
                # datetime.strftime(plan.start_time,"%Y-%m-%d %H:%M:%S")
                _time = plan.create_time.strftime("%Y-%m-%d %H:%M:%S")
                detail.append(dict(
                    id=plan.id,
                    station=plan.project_name,
                    typeName=ISSUANCE_TYPE.get(plan.type_name),
                    descr=plan.content,
                    status=plan.status,
                    time=_time,
                    userName=plan.user_name,
                ))
            return self.returnTotalSuc(detail,total)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class planHisExport(BaseHandler):
    """导出查询下发记录"""
    @tornado.web.authenticated
    def post(self):
        self.refreshSession()
        station = self.get_argument('station', None) # 电站名
        status = self.get_argument('status', None) # 下发结果 1成功2失败
        typeName = self.get_argument('typeName', None) # 下发类型 下发分类名称
        startTime = self.get_argument('startTime', None)
        endTime = self.get_argument('endTime', None)
        try:
            plans = user_session.query(TPanLogs).filter(TPanLogs.is_use==1).order_by(desc(TPanLogs.create_time))
            if station:
                plans = plans.filter(TPanLogs.station.contains(station))
            if status:
                plans = plans.filter(TPanLogs.status==status)
            if typeName:
                typeName = typeName.split(',')
                plans = plans.filter(TPanLogs.type_name.in_(typeName))
            if startTime:
                plans = plans.filter(TPanLogs.create_time >= startTime)
            if endTime:
                plans = plans.filter(TPanLogs.create_time <= endTime)
            plans = plans.all()
            detail = []

            wb = Workbook()
            wb.encoding = 'utf-8'  # 定义编码格式
            st = wb.active  # 获取第一个工作表（sheet1）
            st.title = '下发记录'
            headers = ['项目名称', '下发类型', '下发指令内容', '下发状态','下发时间', '下发用户']
            st.append(headers)
            status_str = {"1": "成功", "2": "失败"}
            for plan in plans:
                # datetime.strftime(plan.start_time,"%Y-%m-%d %H:%M:%S")
                _time = plan.create_time.strftime("%Y-%m-%d %H:%M:%S")
                status = status_str.get(str(plan.status))
                typeName = ISSUANCE_TYPE.get(plan.type_name)
                # detail.append(dict(
                #     station=plan.project_name,
                #     typeName=ISSUANCE_TYPE.get(plan.type_name),
                #     descr=plan.descr,
                #     status=status,
                #     time=_time,
                #     userName=plan.user_name,
                # ))
                st.append([plan.project_name,typeName,plan.content,status,_time,plan.user_name])
            if startTime and endTime:
                file_name = f"{startTime}-{endTime}下发指令日志.xlsx"
            else:
                file_name = f"{datetime.datetime.now().strftime('%Y-%m-%d')}下发指令日志.xlsx"
            BASE_DIR = Path(__file__).resolve().parent
            path = os.path.join(BASE_DIR, file_name)
            # 上传Minio
            minio_client = MinioTool()
            wb.save(path)
            file_path = minio_client.upload_local_file(file_name, path)
            return self.returnTypeSuc({'file_name': file_name, 'file_path': file_path})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

class GetIssuanceType(BaseHandler):
    """查询下发类型"""
    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        return self.returnTypeSuc(ISSUANCE_TYPE)


class ProjectPackAdd(BaseHandler):
    """另存项目包"""

    @tornado.web.authenticated
    def post(self,):
        self.refreshSession()
        user_id = self.get_argument('user_id', None)
        data = self.get_argument('data', None)
        name = self.get_argument('name', None)
        if not all([name, data]):
            return self.customError("项目包信息为空!")
        try:
            if isinstance(data, str):
                data = eval(data)
            for info in data:
                station = info.get('station')
                if not station:
                    return self.customError("项目包另存失败，内容不完整！!")

            ins = ProjectPack(user_id=int(user_id), data=json.dumps(data), name=name)
            user_session.add(ins)
            user_session.commit()
            return self.write({"code": 200, "msg": "项目包保存成功", "data": []})
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


class ProjectPackList(BaseHandler):
    """加载另存项目包列表"""

    @tornado.web.authenticated
    def get(self,):
        self.refreshSession()
        user_id = self.get_argument('user_id', None) #
        try:
            packs = user_session.query(ProjectPack).filter(ProjectPack.user_id == user_id).all()
            data = []
            for pack in packs:
                p_dict = {
                    'name': pack.name,
                    'data': json.loads(pack.data)
                }
                data.append(p_dict)
            return self.returnTypeSuc(data)
        except Exception as e:
            logging.error(e)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
