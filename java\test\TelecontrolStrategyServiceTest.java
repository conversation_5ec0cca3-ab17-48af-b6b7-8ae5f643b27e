package com.robestec.analysis.test;

import com.alibaba.fastjson.JSON;
import com.robestec.analysis.dto.PowerPlanRequest;
import com.robestec.analysis.dto.StrategyRequest;
import com.robestec.analysis.dto.TelecontrolResponse;
import com.robestec.analysis.service.TelecontrolStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * 远程控制策略服务测试类
 * 验证Java实现与Python逻辑的一致性
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class TelecontrolStrategyServiceTest {

    @Autowired
    private TelecontrolStrategyService telecontrolStrategyService;

    /**
     * 测试获取电站容量信息
     * 对应Python中的PowerPlanStations方法
     */
    @Test
    public void testGetPowerPlanStations() {
        log.info("=== 测试获取电站容量信息 ===");
        
        try {
            List<TelecontrolResponse.StationCapacityResponse> result = 
                telecontrolStrategyService.getPowerPlanStations();
            
            log.info("获取电站容量信息成功，数量: {}", result.size());
            result.forEach(station -> {
                log.info("电站信息: ID={}, 名称={}, 描述={}, 电功率={}", 
                    station.getId(), station.getName(), station.getDescr(), station.getElectricPower());
            });
            
        } catch (Exception e) {
            log.error("获取电站容量信息失败", e);
        }
    }

    /**
     * 测试刷新电站容量信息
     * 对应Python中的PowerPlanStationsRefresh方法
     */
    @Test
    public void testRefreshPowerPlanStations() {
        log.info("=== 测试刷新电站容量信息 ===");
        
        try {
            List<Long> stationIds = Arrays.asList(1L, 2L, 3L);
            List<TelecontrolResponse.StationCapacityResponse> result = 
                telecontrolStrategyService.refreshPowerPlanStations(stationIds);
            
            log.info("刷新电站容量信息成功，数量: {}", result.size());
            result.forEach(station -> {
                log.info("刷新后电站信息: ID={}, 名称={}, 电功率={}", 
                    station.getId(), station.getName(), station.getElectricPower());
            });
            
        } catch (Exception e) {
            log.error("刷新电站容量信息失败", e);
        }
    }

    /**
     * 测试新增功率计划
     * 对应Python中的PowerPlanAdd方法
     */
    @Test
    public void testAddPowerPlan() {
        log.info("=== 测试新增功率计划 ===");
        
        try {
            PowerPlanRequest request = new PowerPlanRequest();
            request.setPlanType(1); // 自定义
            request.setPowerList("[{\"time\":\"00:00\",\"power\":100},{\"time\":\"12:00\",\"power\":200}]");
            request.setStationList("[{\"id\":1,\"name\":\"测试电站1\"},{\"id\":2,\"name\":\"测试电站2\"}]");
            request.setAccount("testuser");
            request.setPassword("testpass");
            request.setPlanName("测试功率计划_" + System.currentTimeMillis());
            request.setUserId(1L);
            request.setLang("zh");
            
            TelecontrolResponse.CommonResult<String> result = 
                telecontrolStrategyService.addPowerPlan(request);
            
            log.info("新增功率计划结果: {}", JSON.toJSONString(result));
            
        } catch (Exception e) {
            log.error("新增功率计划失败", e);
        }
    }

    /**
     * 测试功率计划列表查询
     * 对应Python中的PowerPlanList方法
     */
    @Test
    public void testGetPowerPlanList() {
        log.info("=== 测试功率计划列表查询 ===");
        
        try {
            PowerPlanRequest request = new PowerPlanRequest();
            request.setPlanName("测试");
            request.setStatus(1); // 已保存
            request.setPlanType(1); // 自定义
            request.setPageNum(1);
            request.setPageSize(10);
            request.setLang("zh");
            
            var result = telecontrolStrategyService.getPowerPlanList(request);
            
            log.info("查询功率计划列表成功，总数: {}, 当前页数据: {}", 
                result.getTotal(), result.getRecords().size());
            
            result.getRecords().forEach(plan -> {
                log.info("功率计划: ID={}, 名称={}, 类型={}, 状态={}", 
                    plan.getId(), plan.getName(), plan.getPlanTypeName(), plan.getStatusName());
            });
            
        } catch (Exception e) {
            log.error("查询功率计划列表失败", e);
        }
    }

    /**
     * 测试策略模板导入
     * 对应Python中的StrategyImport方法
     */
    @Test
    public void testImportStrategy() {
        log.info("=== 测试策略模板导入 ===");
        
        try {
            // 创建模拟Excel文件
            String excelContent = "开始时间,结束时间,PV,充电配置,RL\n00:00,12:00,100,80,50\n12:00,23:59,200,90,60";
            MockMultipartFile file = new MockMultipartFile(
                "file", "strategy_template.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                excelContent.getBytes()
            );
            
            TelecontrolResponse.StrategyImportResponse result = 
                telecontrolStrategyService.importStrategy(file, "zh");
            
            log.info("策略模板导入成功: 名称={}, 数据条数={}, 月份列表={}", 
                result.getName(), result.getData().size(), result.getMonthList());
            
            result.getData().forEach(item -> {
                log.info("策略数据: 开始时间={}, 结束时间={}, PV={}, 充电配置={}, RL={}", 
                    item.getStartTime(), item.getEndTime(), item.getPv(), 
                    item.getChargeConfig(), item.getRl());
            });
            
        } catch (Exception e) {
            log.error("策略模板导入失败", e);
        }
    }

    /**
     * 测试查询下发记录列表
     * 对应Python中的GetPlanHis方法
     */
    @Test
    public void testGetPlanHistory() {
        log.info("=== 测试查询下发记录列表 ===");
        
        try {
            StrategyRequest.PlanHistoryRequest request = new StrategyRequest.PlanHistoryRequest();
            request.setStation("测试电站");
            request.setStatus(1); // 成功
            request.setTypeName("功率计划下发");
            request.setPageNum(1);
            request.setPageSize(10);
            
            var result = telecontrolStrategyService.getPlanHistory(request);
            
            log.info("查询下发记录列表成功，总数: {}, 当前页数据: {}", 
                result.getTotal(), result.getRecords().size());
            
            result.getRecords().forEach(record -> {
                log.info("下发记录: ID={}, 电站={}, 类型={}, 状态={}, 时间={}", 
                    record.getId(), record.getStation(), record.getTypeName(), 
                    record.getStatusName(), record.getTime());
            });
            
        } catch (Exception e) {
            log.error("查询下发记录列表失败", e);
        }
    }

    /**
     * 测试查询下发类型
     * 对应Python中的GetIssuanceType方法
     */
    @Test
    public void testGetIssuanceType() {
        log.info("=== 测试查询下发类型 ===");
        
        try {
            TelecontrolResponse.IssuanceTypeResponse result = 
                telecontrolStrategyService.getIssuanceType();
            
            log.info("查询下发类型成功，类型数量: {}", result.getIssuanceTypes().size());
            result.getIssuanceTypes().forEach((key, value) -> {
                log.info("下发类型: {}={}", key, value);
            });
            
        } catch (Exception e) {
            log.error("查询下发类型失败", e);
        }
    }

    /**
     * 测试另存项目包
     * 对应Python中的ProjectPackAdd方法
     */
    @Test
    public void testAddProjectPack() {
        log.info("=== 测试另存项目包 ===");
        
        try {
            StrategyRequest.ProjectPackAddRequest request = new StrategyRequest.ProjectPackAddRequest();
            request.setUserId(1L);
            request.setName("测试项目包_" + System.currentTimeMillis());
            request.setData("{\"strategies\":[{\"name\":\"策略1\",\"config\":{\"pv\":100,\"charge\":80}}]}");
            
            TelecontrolResponse.CommonResult<String> result = 
                telecontrolStrategyService.addProjectPack(request);
            
            log.info("另存项目包结果: {}", JSON.toJSONString(result));
            
        } catch (Exception e) {
            log.error("另存项目包失败", e);
        }
    }

    /**
     * 测试加载项目包列表
     * 对应Python中的ProjectPackList方法
     */
    @Test
    public void testGetProjectPackList() {
        log.info("=== 测试加载项目包列表 ===");
        
        try {
            Long userId = 1L;
            List<TelecontrolResponse.ProjectPackResponse> result = 
                telecontrolStrategyService.getProjectPackList(userId);
            
            log.info("加载项目包列表成功，数量: {}", result.size());
            result.forEach(pack -> {
                log.info("项目包: 名称={}, 数据={}", pack.getName(), pack.getData());
            });
            
        } catch (Exception e) {
            log.error("加载项目包列表失败", e);
        }
    }

    /**
     * 综合测试：模拟完整的功率计划管理流程
     */
    @Test
    public void testCompleteWorkflow() {
        log.info("=== 综合测试：完整功率计划管理流程 ===");
        
        try {
            // 1. 获取电站容量信息
            log.info("步骤1: 获取电站容量信息");
            List<TelecontrolResponse.StationCapacityResponse> stations = 
                telecontrolStrategyService.getPowerPlanStations();
            log.info("获取到 {} 个电站", stations.size());
            
            // 2. 新增功率计划
            log.info("步骤2: 新增功率计划");
            PowerPlanRequest addRequest = new PowerPlanRequest();
            addRequest.setPlanType(1);
            addRequest.setPowerList("[{\"time\":\"00:00\",\"power\":100}]");
            addRequest.setStationList("[{\"id\":1,\"name\":\"测试电站\"}]");
            addRequest.setAccount("testuser");
            addRequest.setPassword("testpass");
            addRequest.setPlanName("综合测试计划_" + System.currentTimeMillis());
            addRequest.setUserId(1L);
            
            var addResult = telecontrolStrategyService.addPowerPlan(addRequest);
            log.info("新增功率计划结果: {}", addResult.getMsg());
            
            // 3. 查询功率计划列表
            log.info("步骤3: 查询功率计划列表");
            PowerPlanRequest listRequest = new PowerPlanRequest();
            listRequest.setPageNum(1);
            listRequest.setPageSize(5);
            
            var listResult = telecontrolStrategyService.getPowerPlanList(listRequest);
            log.info("查询到 {} 条功率计划记录", listResult.getTotal());
            
            // 4. 查询下发类型
            log.info("步骤4: 查询下发类型");
            var typeResult = telecontrolStrategyService.getIssuanceType();
            log.info("查询到 {} 种下发类型", typeResult.getIssuanceTypes().size());
            
            log.info("=== 综合测试完成 ===");
            
        } catch (Exception e) {
            log.error("综合测试失败", e);
        }
    }
}
