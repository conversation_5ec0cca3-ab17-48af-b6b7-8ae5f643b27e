#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-06-20 09:11:27
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\sop_label_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-21 11:06:36

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.WorkOrder.sop_base_info import SopBaseInfo

class SopLabelInfo(user_Base):
    u'SOP标签表'
    __tablename__ = "t_sop_label_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    label_name = Column(String(256), nullable=False, comment=u"标签名称")
    en_label_name = Column(String(256), nullable=False, comment=u"标签名称-英文")
    step_name = Column(String(256), nullable=False, comment=u"步骤名称")
    en_step_name = Column(String(256), nullable=False, comment=u"步骤名称-英文")
    datainfo = Column(Text, nullable=True, comment=u"信息")
    en_datainfo = Column(Text, nullable=True, comment=u"信息-英文")
    lb_file = Column(String(256), nullable=True, comment=u"文件传输解析时的名称")
    base_info_id = Column(Integer,ForeignKey("t_sop_base_info.id"), nullable=False,comment=u"SOP基本信息表id")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")

    sop_base_model= relationship("SopBaseInfo", backref="sop_base_model")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
       
        bean = "{'id':%s,'name':'%s','file':'%s','en_label_name':'%s','en_step_name':'%s','en_datainfo':'%s'," \
               "}" % (self.id,self.step_name,self.lb_file,self.en_label_name,self.en_step_name,self.en_datainfo)
        return bean.replace("None",'')
        
