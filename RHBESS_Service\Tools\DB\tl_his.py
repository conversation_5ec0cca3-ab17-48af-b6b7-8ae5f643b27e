#!/usr/bin/env python
# coding=utf-8
#@Information: 添禄系列数据库
#<AUTHOR> WYJ
#@Date         : 2023-07-06 08:35:29
#@FilePath     : \RHBESS_Service\Tools\DB\halun_his copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-07-06 08:35:30


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


TL_HOSTNAME = model_config.get('mysql', "TL_HOSTNAME")
TL_PORT = model_config.get('mysql', "TL_PORT")
TL_DATABASE = model_config.get('mysql', "TL_DATABASE")
TL_USERNAME = model_config.get('mysql', "TL_USERNAME")
TL_PASSWORD = model_config.get('mysql', "TL_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TL_USERNAME,
    TL_PASSWORD,
    TL_HOSTNAME,
    TL_PORT,
    TL_DATABASE
)
tl_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_tl_session = scoped_session(sessionmaker(tl_engine,autoflush=True))

tl_Base = declarative_base(tl_engine)
tl_session = _tl_session()




