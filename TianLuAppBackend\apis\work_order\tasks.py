import datetime
import traceback

from tools.send_mail import sendMail_
from TianLuAppBackend import settings
from .email_grep import YS, ZS, CS, XX_CS, CSR

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# 发送邮件功能
# @app.task
def send_email(order_num, status, station_name=None):
    try:
        # time.sleep(10)
        if status == '预审':
            to_addr = YS
            sender_show = '天禄工单有待审核任务'
            recipient_show = '天禄工单有待审核任务'
            message = f"天禄小程序工单  :工单编号  {order_num}  请尽快审核   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        elif status == '初审':
            to_addr = CSR
            sender_show = '天禄工单有待审核任务'
            recipient_show = '天禄工单有待审核任务'
            message = f"天禄小程序工单  :工单编号  {order_num}  请尽快审核   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        elif status == '终审':
            to_addr = ZS
            sender_show = '天禄工单有待审核任务'
            recipient_show = '天禄工单有待审核任务'
            message = f"天禄小程序工单  :工单编号  {order_num}  请尽快审核   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        elif status == '下线抄送':
            to_addr = XX_CS
            sender_show = '天禄工单有需要执行并网点下线操作的任务'
            recipient_show = '天禄工单有需要执行并网点下线操作的任务'
            message = f"天禄小程序工单  :工单编号  {order_num}  并网点：{station_name} 需要执行并网点下线操作，请尽快查看   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        else:
            to_addr = CS
            sender_show = '天禄工单任务完成'
            recipient_show = '天禄工单任务完成'
            message = f"天禄小程序工单  :工单编号  {order_num}  工单已执行完成，请查看。   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        subject = '天禄小程序-工单'
        # message = f"天禄小程序工单  :工单编号  {order_num}  请尽快审核   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}"
        sendMail_(message, subject, sender_show, recipient_show, to_addr)
        success_log.log(3, '工单邮件发送成功')
    except Exception as e:
        print(traceback.print_exc())
        error_log.log(3, e)


if __name__ == '__main__':
    send_email('123', '预审')
