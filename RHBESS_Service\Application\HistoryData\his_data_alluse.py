#!/usr/bin/env python
# coding=utf-8
# @Information:

'''
通用历史数据获取接口
'''
import math
from sqlalchemy import func, or_, update, bindparam
from Application.Models.base_handler import <PERSON><PERSON>andler
from Tools.Cfg.DB_his import get_dhis
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session, DEBUG
from Tools.DB.mysql_user import get_user_session
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.event import Event
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.event_r import EventR
from Application.Models.User.page_data import PageData
from Application.Models.User.report_f import FReport
from Application.Models.His.r_ACDMS import HisACDMS, HisDM
from Tools.Utils.num_utils import *
from Application.EqAccount.HaLun.main_frame import pageData_first
from Application.Models.User.station import Station
from Tools.DB.mysql_scada import mqtt_session
from Application.Models.His.r_ACDMS_1 import HisACDMS_1
from sqlalchemy import func, or_, and_
from Application.Models.SelfStationPoint.t_device import DevicePT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT
from Application.Models.SelfStationPoint.t_status import StatusPT
import tornado.web
from Tools.DB.mysql_his import dongmu_session, _dm_his_engine, HIS_DATABASE_
import time
import pymysql
from Application.Cfg.dir_cfg import model_config
from dbutils.persistent_db import PersistentDB
# 连接数据库
pool = PersistentDB(pymysql, 10,**{
            "host": model_config.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })
chengben = float(model_config.get('broker', "chengben"))
shouyi = float(model_config.get('broker', "shouyi"))

db_ = get_dhis('his_data_query')
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
pcs_ = json.loads(model_config.get('peizhi', 'pcs'))  #电站名称（PCS）

# 东睦电站个数
dongmu_num = 7
alarmType_arr = ['', '故障', '报警', '无故障', '无报警']

# dianya = {'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6","dongmu.PCS7"]}
#
# dianliu = {'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6","dongmu.PCS7"]}
#
# yougong = {'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6","dongmu.PCS7"]}
#
# wugong = {'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6","dongmu.PCS7"]}

pcs_table_name= {'1':'dws_pcs_measure_win_15min','2':'dws_pcs_measure_cw_cm_cy','3':'dws_pcs_measure_cw_cm_cy','4':'dws_pcs_measure_cw_cm_cy','5':'dws_pcs_measure_win_1h_2h_6h'}
value_name= {'yougong': 'real_pw', 'wugong': 'pf'}
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']

class HisDataAllUseInterface(BaseHandler):

    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        db_con = self.get_argument('db', 'his')
        time_ = int(self.get_argument('time', 8))
        logging.warn('get---db_con:%s,time:%s' % (db_con, time_))
        lang = self.get_argument('lang', None)  # 英文
        db_conn = db_[db_con]
        try:
        # if 1:
            if kt == 'MainFrameRight':
                F, data = self._getDataMainTwoApplet(db_con)#小程序接口
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'MainFrameTFRight':
                F, data = self._getDataMainTFTwo(db_conn, db_con, time_)
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'MainFrameLeft':
                F, data = self._getDataMainThr(db_conn, db_con)
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'RealAlarmDatas':
                all, total = self._getDatas(AlarmR, timeUtils.getNewTimeStr(), 1, 100,
                                            [AlarmR.station == db_con, AlarmR.status == None])
                return self.returnTypeSuc(all)
            elif kt == 'RealEventDatas':
                all, total = self._getDatas(EventR, timeUtils.getNewTimeStr(), 1, 100,
                                            [or_(EventR.station == None, EventR.station == db_con)])
                return self.returnTypeSuc(all)
            else:
                return self.pathError()
        except Exception as E:
            for dd in db_conn[1]:
                dd.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            for dd in db_conn[1]:
                dd.close()
            user_session.close()

    @staticmethod
    def _getDataMainTwoApplet( db):
        '''
        充放电量，有功无功
        '''
        now_time = timeUtils.getAgoTime(1)
        old_time = timeUtils.getAgoTime(7)
        two_time_lists = timeUtils.dateToDataList(old_time, now_time)
        months = timeUtils.getBetweenMonth(old_time, now_time)
        selectTable = 'r_measure%s' % (months[-1])

        obj = {'page_area': 'right'}
        data1, data2, names, names_1 = [], [], [], []  # 放电和充电数据,放电收益
        m=0

        for day in two_time_lists:
            freport = user_session.query(FReport).filter(FReport.name.in_(pcs_), FReport.name.like('%' + db + '%'),FReport.day == day + ' 00:00:00', FReport.cause == 1).all()
            fl, disg, chag = 0, 0, 0
            if freport:
                for f in freport:
                    disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(eval(f.gd_disg))
                    chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                data1.append(round(disg, 2))
                data2.append(round(chag, 2))
            else:
                data1.append(0)
                data2.append(0)

        obj['disgCapy'] = {"data": data1, "time": two_time_lists}
        obj['chagCapy'] = {"data": data2, "time": two_time_lists}
        if db =='dongmu':
            #  中间部分数据
            page_data = user_session.query(PageData.name).filter(PageData.page_id == pageData_first[db],PageData.page_area == 'right',PageData.is_use == 1).first()
            tables = [selectTable]
            for nam in page_data[0].split('#'):
                names_1.append(nam[:-6])

            timeall, dataV, dataA, dataP1, dataP2, data2 = [], [], [], [], [], {}  # 总时间;可充电量,可放电量,有功；无功
            total = len(dianya[db])  # 总的单元个数

            e_start = timeUtils.nowSecs()
            n = e_start - 86400
            dm_table = HisDM('r_measure')
            values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(n, e_start)).order_by(dm_table.time.asc()).all()
            timeall_, alldata = [], []  # 时间，所有数据
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            for e in range(dongmu_num):
                d2, d3, d4, d5 = [], [], [], []  # 有功，无功,可充电量,可放电量
                for ii in alldata:
                    PCS_ = 'PCS%s' % (e + 1)
                    if ii['device'] == PCS_:
                        actPo = ii['actPo']  # 有功
                        reapo = ii['reapo']  # 无功
                        d2.append(actPo)
                        d3.append(reapo)
                    BMS_ = 'BMS%s' % (e + 1)
                    if ii['device'] == BMS_:
                        ChCap = ii['ChCap']  # 可充电量
                        DiCap = ii['DiCap']  # 可放电量
                        d4.append(ChCap)
                        d5.append(DiCap)
                # 取相同的间隔时间(采用补数的方法)
                data12, data13, data14, data15 = {}, {}, {}, {}

                data12['time'] = timeall_
                data12['value'] = d2
                data13['time'] = timeall_
                data13['value'] = d3
                data14['time'] = timeall_
                data14['value'] = d4
                data15['time'] = timeall_
                data15['value'] = d5

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                df = pd.DataFrame(data13)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data13["time"] = df["time"].tolist()
                data13["value"] = df["value"].tolist()

                df = pd.DataFrame(data14)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data14["time"] = df["time"].tolist()
                data14["value"] = df["value"].tolist()

                df = pd.DataFrame(data15)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data15["time"] = df["time"].tolist()
                data15["value"] = df["value"].tolist()
                data22 = complete_data(data12, '15T') if data12['time'] else {}
                data23 = complete_data(data13, '15T') if data13['time'] else {}
                data24 = complete_data(data14, '15T') if data14['time'] else {}
                data25 = complete_data(data15, '15T') if data15['time'] else {}
                list_time = []
                if data22:
                    for i in data22['time']:
                        list_time.append(i[8:16])
                    data22['value'][0] = 0
                if data23:
                    data23['value'][0] = 0
                if data24:
                    data24['value'][0] = 0
                if data25:
                    data25['value'][0] = 0
                data2['time'] = list_time
                dataP1.append(data22['value']) if data22 else []
                dataP2.append(data23['value']) if data23 else []
                dataV.append(data24['value']) if data24 else []
                dataA.append(data25['value']) if data25 else []
            data2['dataP1'] = dataP1
            data2['dataP2'] = dataP2
            data2['dataV'] = dataV
            data2['dataA'] = dataA
            data2['total'] = total
            obj["data2"] = data2
        else:
            for p in pcs_:
                if db == 'baodian':
                    db = 'bodian'
                if db in p:
                    m += 1
            # day = timeUtils.getAgoTime(1)[:10]
            timeall,dataP1, dataP2, data2 = [], [[], []], [[],[]], {}  # 总时间;电压;电流；有功；无功
            conn = pool.connection()
            cursor = conn.cursor()
            day='2024-07-22'
            sql = "select {}, {},pcs_name,DATE_FORMAT(FLOOR(window_start / {}) * {}, '%d %H:%i') as window_start from {} where \
                                            window_start>='{}' and window_start<='{}' and station_name='{}' group by real_pw, pf,pcs_name, window_start".\
                format(value_name['yougong'], value_name['wugong'],16,16,pcs_table_name['1'],day+' 00:00:00',day+' 23:50:00',db)
            cursor.execute(sql)
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            dic_pcs={}
            for r in result:
                if r['window_start']!=None:
                    timeall.append(r['window_start'])
                    if r['pcs_name'] not in dic_pcs.keys():
                        dic_pcs[r['pcs_name']]={'yougong':[],'wugong':[]}
                        dic_pcs[r['pcs_name']]['yougong'].append(r[value_name['yougong']])
                        dic_pcs[r['pcs_name']]['wugong'].append(r[value_name['wugong']])
                    else:
                        dic_pcs[r['pcs_name']]['yougong'].append(r[value_name['yougong']])
                        dic_pcs[r['pcs_name']]['wugong'].append(r[value_name['wugong']])
            timeall = list(set(timeall))
            timeall.sort()
            data2['time'] = timeall
            data2['total'] = m# 总的单元个数
            data2['dataP1']=[]
            data2['dataP2']=[]
            for k,v in dic_pcs.items():
                data2['dataP1'].append(v['yougong'])
                data2['dataP2'].append(v['wugong'])
            obj["data2"] = data2
        return True, obj



dianya = {'halun': ["tpSthalun.PcsSt1.Lp1.ChagCapy", "tpSthalun.PcsSt1.Lp2.ChagCapy", "tpSthalun.PcsSt2.Lp1.ChagCapy",
                "tpSthalun.PcsSt2.Lp2.ChagCapy"],
      "taicang": ["tpSttaicang.PcsSt1.Lp1.ChagCapy", "tpSttaicang.PcsSt1.Lp2.ChagCapy",
                  "tpSttaicang.PcsSt2.Lp1.ChagCapy", "tpSttaicang.PcsSt2.Lp2.ChagCapy",
                  "tpSttaicang.PcsSt3.Lp1.ChagCapy", "tpSttaicang.PcsSt3.Lp2.ChagCapy",
                  "tpSttaicang.PcsSt4.Lp1.ChagCapy", "tpSttaicang.PcsSt4.Lp2.ChagCapy", ],
      "binhai": ["tpStbinhai1.PcsSt1.Lp1.ChagCapy", "tpStbinhai1.PcsSt1.Lp2.ChagCapy",
                 "tpStbinhai1.PcsSt2.Lp1.ChagCapy", "tpStbinhai1.PcsSt2.Lp2.ChagCapy",
                 "tpStbinhai1.PcsSt3.Lp1.ChagCapy", "tpStbinhai1.PcsSt3.Lp2.ChagCapy",
                 "tpStbinhai2.PcsSt4.Lp1.ChagCapy", "tpStbinhai2.PcsSt4.Lp2.ChagCapy",
                 "tpStbinhai2.PcsSt5.Lp1.ChagCapy", "tpStbinhai2.PcsSt5.Lp2.ChagCapy",
                 "tpStbinhai2.PcsSt6.Lp1.ChagCapy", "tpStbinhai2.PcsSt6.Lp2.ChagCapy", ],
      "ygzhen": ["tfStygzhen1.EMS.PCS1.Lp1.AcChagCapyDaly", "tfStygzhen1.EMS.PCS1.Lp2.AcChagCapyDaly",
                 "tfStygzhen1.EMS.PCS1.Lp3.AcChagCapyDaly", "tfStygzhen1.EMS.PCS1.Lp4.AcChagCapyDaly",
                 "tfStygzhen2.EMS.PCS2.Lp1.AcChagCapyDaly", "tfStygzhen2.EMS.PCS2.Lp2.AcChagCapyDaly",
                 "tfStygzhen2.EMS.PCS2.Lp3.AcChagCapyDaly", "tfStygzhen2.EMS.PCS2.Lp4.AcChagCapyDaly"],
      "baodian": ["tfStbodian1.Pcs.M1ChgCapyAlw", "tfStbodian2.Pcs.M1ChgCapyAlw", "tfStbodian3.Pcs.M1ChgCapyAlw",
                  "tfStbodian4.Pcs.M1ChgCapyAlw", "tfStbodian5.Pcs.M1ChgCapyAlw"],
      'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6",
                 "dongmu.PCS7"],
      "zgtian": ["tfStzgtian1.EMS.PCS1.Lp1.AcChagCapyDaly", "tfStzgtian1.EMS.PCS1.Lp2.AcChagCapyDaly",
                 "tfStzgtian1.EMS.PCS1.Lp3.AcChagCapyDaly", "tfStzgtian1.EMS.PCS1.Lp4.AcChagCapyDaly",
                 "tfStzgtian2.EMS.PCS2.Lp1.AcChagCapyDaly", "tfStzgtian2.EMS.PCS2.Lp2.AcChagCapyDaly",
                 "tfStzgtian2.EMS.PCS2.Lp3.AcChagCapyDaly",
                 "tfStzgtian2.EMS.PCS2.Lp4.AcChagCapyDaly", "tfStzgtian3.EMS.PCS3.Lp1.AcChagCapyDaly",
                 "tfStzgtian3.EMS.PCS3.Lp2.AcChagCapyDaly", "tfStzgtian3.EMS.PCS3.Lp3.AcChagCapyDaly",
                 "tfStzgtian3.EMS.PCS3.Lp4.AcChagCapyDaly",
                 "tfStzgtian4.EMS.PCS4.Lp1.AcChagCapyDaly", "tfStzgtian4.EMS.PCS4.Lp2.AcChagCapyDaly",
                 "tfStzgtian4.EMS.PCS4.Lp3.AcChagCapyDaly", "tfStzgtian4.EMS.PCS4.Lp4.AcChagCapyDaly"],
      "houma":["tfSthoumaA1.EMS.A502.PCS1.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS1.Lp2.ACTotlChag","tfSthoumaA1.EMS.A502.PCS2.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS2.Lp2.ACTotlChag","tfSthoumaA1.EMS.A502.PCS3.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS3.Lp2.ACTotlChag","tfSthoumaA1.EMS.A502.PCS4.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS4.Lp2.ACTotlChag","tfSthoumaA1.EMS.A502.PCS5.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS5.Lp2.ACTotlChag","tfSthoumaA1.EMS.A502.PCS6.Lp1.ACTotlChag","tfSthoumaA1.EMS.A502.PCS6.Lp2.ACTotlChag","tfSthoumaA1.EMS.A503.PCS7.Lp1.ACTotlChag","tfSthoumaA1.EMS.A503.PCS7.Lp2.ACTotlChag","tfSthoumaA2.EMS.A503.PCS8.Lp1.ACTotlChag","tfSthoumaA2.EMS.A503.PCS8.Lp2.ACTotlChag","tfSthoumaA2.EMS.A503.PCS9.Lp1.ACTotlChag","tfSthoumaA2.EMS.A503.PCS9.Lp2.ACTotlChag","tfSthoumaA2.EMS.A503.PCS10.Lp1.ACTotlChag","tfSthoumaA2.EMS.A503.PCS10.Lp2.ACTotlChag","tfSthoumaA2.EMS.A503.PCS11.Lp1.ACTotlChag","tfSthoumaA2.EMS.A503.PCS11.Lp2.ACTotlChag","tfSthoumaA2.EMS.A503.PCS12.Lp1.ACTotlChag","tfSthoumaA2.EMS.A503.PCS12.Lp2.ACTotlChag","tfSthoumaA2.EMS.A504.PCS13.Lp1.ACTotlChag","tfSthoumaA2.EMS.A504.PCS13.Lp2.ACTotlChag","tfSthoumaA2.EMS.A504.PCS14.Lp1.ACTotlChag","tfSthoumaA2.EMS.A504.PCS14.Lp2.ACTotlChag","tfSthoumaB1.EMS.B504.PCS1.Lp1.ACTotlChag","tfSthoumaB1.EMS.B504.PCS1.Lp2.ACTotlChag","tfSthoumaB1.EMS.B504.PCS2.Lp1.ACTotlChag","tfSthoumaB1.EMS.B504.PCS2.Lp2.ACTotlChag","tfSthoumaB1.EMS.B504.PCS3.Lp1.ACTotlChag","tfSthoumaB1.EMS.B504.PCS3.Lp2.ACTotlChag","tfSthoumaB1.EMS.B504.PCS4.Lp1.ACTotlChag","tfSthoumaB1.EMS.B504.PCS4.Lp2.ACTotlChag","tfSthoumaB1.EMS.B505.PCS5.Lp1.ACTotlChag","tfSthoumaB1.EMS.B505.PCS5.Lp2.ACTotlChag","tfSthoumaB1.EMS.B505.PCS6.Lp1.ACTotlChag","tfSthoumaB1.EMS.B505.PCS6.Lp2.ACTotlChag","tfSthoumaB1.EMS.B505.PCS7.Lp1.ACTotlChag","tfSthoumaB1.EMS.B505.PCS7.Lp2.ACTotlChag","tfSthoumaB1.EMS.B505.PCS8.Lp1.ACTotlChag","tfSthoumaB1.EMS.B505.PCS8.Lp2.ACTotlChag","tfSthoumaB2.EMS.B505.PCS9.Lp1.ACTotlChag","tfSthoumaB2.EMS.B505.PCS9.Lp2.ACTotlChag","tfSthoumaB2.EMS.B505.PCS10.Lp1.ACTotlChag","tfSthoumaB2.EMS.B505.PCS10.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS11.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS11.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS12.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS12.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS13.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS13.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS14.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS14.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS15.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS15.Lp2.ACTotlChag","tfSthoumaB2.EMS.B506.PCS16.Lp1.ACTotlChag","tfSthoumaB2.EMS.B506.PCS16.Lp2.ACTotlChag"],
      "datong":["tc_datong1.EMS.Energy1.PCS.Lp1.TolChagEle","tc_datong1.EMS.Energy1.PCS.Lp2.TolChagEle","tc_datong1.EMS.Energy2.PCS.Lp1.TolChagEle","tc_datong1.EMS.Energy2.PCS.Lp2.TolChagEle","tc_datong2.EMS.Energy3.PCS.Lp1.TolChagEle","tc_datong2.EMS.Energy3.PCS.Lp2.TolChagEle","tc_datong2.EMS.Energy4.PCS.Lp1.TolChagEle","tc_datong2.EMS.Energy4.PCS.Lp2.TolChagEle","tc_datong3.EMS.Energy5.PCS.Lp1.TolChagEle","tc_datong3.EMS.Energy5.PCS.Lp2.TolChagEle","tc_datong3.EMS.Energy6.PCS.Lp1.TolChagEle","tc_datong3.EMS.Energy6.PCS.Lp2.TolChagEle","tc_datong4.EMS.Energy7.PCS.Lp1.TolChagEle","tc_datong4.EMS.Energy7.PCS.Lp2.TolChagEle","tc_datong4.EMS.Energy8.PCS.Lp1.TolChagEle","tc_datong4.EMS.Energy8.PCS.Lp2.TolChagEle"],
      "guizhou":["guizhou1a.Energy1.Pcs.Lp1.DayChgEle","guizhou1a.Energy1.Pcs.Lp2.DayChgEle","guizhou1a.Energy2.Pcs.Lp1.DayChgEle","guizhou1a.Energy2.Pcs.Lp2.DayChgEle","guizhou1a.Energy3.Pcs.Lp1.DayChgEle","guizhou1a.Energy3.Pcs.Lp2.DayChgEle","guizhou1b.Energy4.Pcs.Lp1.DayChgEle","guizhou1b.Energy4.Pcs.Lp2.DayChgEle","guizhou1b.Energy5.Pcs.Lp1.DayChgEle","guizhou1b.Energy5.Pcs.Lp2.DayChgEle","guizhou1b.Energy6.Pcs.Lp1.DayChgEle","guizhou1b.Energy6.Pcs.Lp2.DayChgEle","guizhou1c.Energy7.Pcs.Lp1.DayChgEle","guizhou1c.Energy7.Pcs.Lp2.DayChgEle","guizhou1c.Energy8.Pcs.Lp1.DayChgEle","guizhou1c.Energy8.Pcs.Lp2.DayChgEle","guizhou2a.Energy10.Pcs.Lp1.DayChgEle","guizhou2a.Energy10.Pcs.Lp2.DayChgEle","guizhou2a.Energy11.Pcs.Lp1.DayChgEle","guizhou2a.Energy11.Pcs.Lp2.DayChgEle","guizhou2b.Energy12.Pcs.Lp1.DayChgEle","guizhou2b.Energy12.Pcs.Lp2.DayChgEle","guizhou2a.Energy9.Pcs.Lp1.DayChgEle","guizhou2a.Energy9.Pcs.Lp2.DayChgEle","guizhou2b.Energy13.Pcs.Lp1.DayChgEle","guizhou2b.Energy13.Pcs.Lp2.DayChgEle","guizhou2b.Energy14.Pcs.Lp1.DayChgEle","guizhou2b.Energy14.Pcs.Lp2.DayChgEle","guizhou2c.Energy15.Pcs.Lp1.DayChgEle","guizhou2c.Energy15.Pcs.Lp2.DayChgEle","guizhou2c.Energy16.Pcs.Lp1.DayChgEle","guizhou2c.Energy16.Pcs.Lp2.DayChgEle","guizhou3a.Energy17.Pcs.Lp1.DayChgEle","guizhou3a.Energy17.Pcs.Lp2.DayChgEle","guizhou3a.Energy18.Pcs.Lp1.DayChgEle","guizhou3a.Energy18.Pcs.Lp2.DayChgEle","guizhou3a.Energy19.Pcs.Lp1.DayChgEle","guizhou3a.Energy19.Pcs.Lp2.DayChgEle","guizhou3b.Energy20.Pcs.Lp1.DayChgEle","guizhou3b.Energy20.Pcs.Lp2.DayChgEle","guizhou3b.Energy21.Pcs.Lp1.DayChgEle","guizhou3b.Energy21.Pcs.Lp2.DayChgEle","guizhou3b.Energy22.Pcs.Lp1.DayChgEle","guizhou3b.Energy22.Pcs.Lp2.DayChgEle","guizhou3c.Energy23.Pcs.Lp1.DayChgEle","guizhou3c.Energy23.Pcs.Lp2.DayChgEle","guizhou3c.Energy24.Pcs.Lp1.DayChgEle","guizhou3c.Energy24.Pcs.Lp2.DayChgEle","guizhou4a.Energy25.Pcs.Lp1.DayChgEle","guizhou4a.Energy25.Pcs.Lp2.DayChgEle","guizhou4a.Energy26.Pcs.Lp1.DayChgEle","guizhou4a.Energy26.Pcs.Lp2.DayChgEle","guizhou4a.Energy27.Pcs.Lp1.DayChgEle","guizhou4a.Energy27.Pcs.Lp2.DayChgEle","guizhou4b.Energy28.Pcs.Lp1.DayChgEle","guizhou4b.Energy28.Pcs.Lp2.DayChgEle","guizhou4b.Energy29.Pcs.Lp1.DayChgEle","guizhou4b.Energy29.Pcs.Lp2.DayChgEle","guizhou4b.Energy30.Pcs.Lp1.DayChgEle","guizhou4b.Energy30.Pcs.Lp2.DayChgEle","guizhou4c.Energy31.Pcs.Lp1.DayChgEle","guizhou4c.Energy31.Pcs.Lp2.DayChgEle","guizhou4c.Energy32.Pcs.Lp1.DayChgEle","guizhou4c.Energy32.Pcs.Lp2.DayChgEle","guizhou5a.Energy33.Pcs.Lp1.DayChgEle","guizhou5a.Energy33.Pcs.Lp2.DayChgEle","guizhou5a.Energy34.Pcs.Lp1.DayChgEle","guizhou5a.Energy34.Pcs.Lp2.DayChgEle","guizhou5a.Energy35.Pcs.Lp1.DayChgEle","guizhou5a.Energy35.Pcs.Lp2.DayChgEle","guizhou5b.Energy36.Pcs.Lp1.DayChgEle","guizhou5b.Energy36.Pcs.Lp2.DayChgEle","guizhou5b.Energy37.Pcs.Lp1.DayChgEle","guizhou5b.Energy37.Pcs.Lp2.DayChgEle","guizhou5b.Energy38.Pcs.Lp1.DayChgEle","guizhou5b.Energy38.Pcs.Lp2.DayChgEle","guizhou5c.Energy39.Pcs.Lp1.DayChgEle","guizhou5c.Energy39.Pcs.Lp2.DayChgEle","guizhou5c.Energy40.Pcs.Lp1.DayChgEle","guizhou5c.Energy40.Pcs.Lp2.DayChgEle","guizhou6a.Energy41.Pcs.Lp1.DayChgEle","guizhou6a.Energy41.Pcs.Lp2.DayChgEle","guizhou6a.Energy42.Pcs.Lp1.DayChgEle","guizhou6a.Energy42.Pcs.Lp2.DayChgEle","guizhou6a.Energy43.Pcs.Lp1.DayChgEle","guizhou6a.Energy43.Pcs.Lp2.DayChgEle","guizhou6b.Energy44.Pcs.Lp1.DayChgEle","guizhou6b.Energy44.Pcs.Lp2.DayChgEle","guizhou6b.Energy45.Pcs.Lp1.DayChgEle","guizhou6b.Energy45.Pcs.Lp2.DayChgEle","guizhou6b.Energy46.Pcs.Lp1.DayChgEle","guizhou6b.Energy46.Pcs.Lp2.DayChgEle","guizhou6c.Energy47.Pcs.Lp1.DayChgEle","guizhou6c.Energy47.Pcs.Lp2.DayChgEle","guizhou6c.Energy48.Pcs.Lp1.DayChgEle","guizhou6c.Energy48.Pcs.Lp2.DayChgEle","guizhou7a.Energy49.Pcs.Lp1.DayChgEle","guizhou7a.Energy49.Pcs.Lp2.DayChgEle","guizhou7a.Energy50.Pcs.Lp1.DayChgEle","guizhou7a.Energy50.Pcs.Lp2.DayChgEle","guizhou7a.Energy51.Pcs.Lp1.DayChgEle","guizhou7a.Energy51.Pcs.Lp2.DayChgEle","guizhou7b.Energy52.Pcs.Lp1.DayChgEle","guizhou7b.Energy52.Pcs.Lp2.DayChgEle","guizhou7b.Energy53.Pcs.Lp1.DayChgEle","guizhou7b.Energy53.Pcs.Lp2.DayChgEle","guizhou7b.Energy54.Pcs.Lp1.DayChgEle","guizhou7b.Energy54.Pcs.Lp2.DayChgEle","guizhou7c.Energy55.Pcs.Lp1.DayChgEle","guizhou7c.Energy55.Pcs.Lp2.DayChgEle","guizhou7c.Energy56.Pcs.Lp1.DayChgEle","guizhou7c.Energy56.Pcs.Lp2.DayChgEle","guizhou8a.Energy57.Pcs.Lp1.DayChgEle","guizhou8a.Energy57.Pcs.Lp2.DayChgEle","guizhou8a.Energy58.Pcs.Lp1.DayChgEle","guizhou8a.Energy58.Pcs.Lp2.DayChgEle","guizhou8b.Energy59.Pcs.Lp1.DayChgEle","guizhou8b.Energy59.Pcs.Lp2.DayChgEle","guizhou8b.Energy60.Pcs.Lp1.DayChgEle","guizhou8b.Energy60.Pcs.Lp2.DayChgEle"],
      "ygqn":["ygqn.abc.EMS.A.PCS.Energy1.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy1.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy2.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy2.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy3.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy3.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy4.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy4.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy5.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy5.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy6.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy6.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy7.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy7.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy8.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy8.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy9.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy9.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy10.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy10.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy11.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy11.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy12.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy12.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy13.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy13.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy14.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy14.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy15.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy15.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy16.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy16.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy17.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy17.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy18.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy18.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy19.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy19.PCS2.DayCg","ygqn.abc.EMS.A.PCS.Energy20.PCS1.DayCg","ygqn.abc.EMS.A.PCS.Energy20.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy1.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy1.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy2.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy2.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy3.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy3.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy4.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy4.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy5.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy5.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy6.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy6.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy7.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy7.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy8.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy8.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy9.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy9.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy10.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy10.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy11.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy11.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy12.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy12.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy13.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy13.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy14.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy14.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy15.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy15.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy16.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy16.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy17.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy17.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy18.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy18.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy19.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy19.PCS2.DayCg","ygqn.abc.EMS.B.PCS.Energy20.PCS1.DayCg","ygqn.abc.EMS.B.PCS.Energy20.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy1.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy1.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy2.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy2.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy3.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy3.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy4.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy4.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy5.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy5.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy6.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy6.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy7.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy7.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy8.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy8.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy9.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy9.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy10.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy10.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy11.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy11.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy12.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy12.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy13.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy13.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy14.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy14.PCS2.DayCg","ygqn.abc.EMS.C.PCS.Energy15.PCS1.DayCg","ygqn.abc.EMS.C.PCS.Energy15.PCS2.DayCg","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS1.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS2.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS3.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS4.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS5.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS6.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS7.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS8.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS9.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS10.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS11.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS12.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS13.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS14.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS15.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS16.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS17.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS18.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS19.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS20.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS1.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS2.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS3.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS4.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS5.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS6.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS7.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS8.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS9.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS10.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS11.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS12.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS13.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS14.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS15.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS16.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS17.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS18.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS19.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS20.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS1.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS2.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS3.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS4.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS5.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS6.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS7.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS8.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS9.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS10.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS11.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS12.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS13.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS14.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS15.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS16.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS17.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS18.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS19.CgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS20.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS1.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS2.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS3.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS4.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS5.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS6.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS7.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS8.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS9.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS10.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS11.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS12.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS13.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS14.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS15.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS16.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS17.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS18.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS19.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS20.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS1.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS2.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS3.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS4.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS5.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS6.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS7.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS8.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS9.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS10.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS11.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS12.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS13.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS14.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS15.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS16.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS17.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS18.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS19.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS20.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS1.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS2.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS3.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS4.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS5.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS6.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS7.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS8.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS9.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS10.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS11.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS12.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS13.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS14.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS15.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS16.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS17.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS18.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS19.CgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS20.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS1.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS2.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS3.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS4.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS5.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS6.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS7.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS8.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS9.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS10.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS11.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS12.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS13.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS14.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS15.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS16.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS17.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS18.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS19.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS20.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS1.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS2.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS3.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS4.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS5.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS6.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS7.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS8.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS9.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS10.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS11.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS12.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS13.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS14.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS15.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS16.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS17.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS18.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS19.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS20.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS1.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS2.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS3.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS4.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS5.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS6.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS7.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS8.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS9.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS10.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS11.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS12.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS13.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS14.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS15.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS16.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS17.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS18.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS19.CgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS20.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS1.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS2.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS3.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS4.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS5.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS6.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS7.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS8.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS9.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS10.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS11.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS12.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS13.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS14.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS15.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS16.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS17.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS18.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS19.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS20.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS1.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS2.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS3.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS4.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS5.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS6.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS7.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS8.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS9.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS10.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS11.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS12.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS13.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS14.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS15.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS16.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS17.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS18.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS19.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS20.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS1.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS2.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS3.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS4.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS5.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS6.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS7.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS8.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS9.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS10.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS11.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS12.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS13.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS14.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS15.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS16.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS17.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS18.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS19.CgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS20.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS1.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS2.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS3.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS4.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS5.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS6.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS7.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS8.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS9.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS10.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS11.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS12.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS13.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS14.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS15.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS16.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS17.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS18.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS19.CgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS20.CgEleDay"],
      "shgyu":["shgyu.EMS.PCS.A.Energy1.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy1.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy2.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy2.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy3.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy3.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy4.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy4.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy5.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy5.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy6.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy6.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy7.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy7.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy8.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy8.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy9.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy9.PCS2.AcDyCg","shgyu.EMS.PCS.A.Energy10.PCS1.AcDyCg","shgyu.EMS.PCS.A.Energy10.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy11.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy11.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy12.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy12.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy13.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy13.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy14.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy14.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy15.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy15.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy16.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy16.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy17.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy17.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy18.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy18.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy19.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy19.PCS2.AcDyCg","shgyu.EMS.PCS.B.Energy20.PCS1.AcDyCg","shgyu.EMS.PCS.B.Energy20.PCS2.AcDyCg"]}

dianliu = {'halun': ["tpSthalun.PcsSt1.Lp1.DisgCapy", "tpSthalun.PcsSt1.Lp2.DisgCapy", "tpSthalun.PcsSt2.Lp1.DisgCapy",
                 "tpSthalun.PcsSt2.Lp2.DisgCapy"],
       "taicang": ["tpSttaicang.PcsSt1.Lp1.DisgCapy", "tpSttaicang.PcsSt1.Lp2.DisgCapy",
                   "tpSttaicang.PcsSt2.Lp1.DisgCapy", "tpSttaicang.PcsSt2.Lp2.DisgCapy",
                   "tpSttaicang.PcsSt3.Lp1.DisgCapy", "tpSttaicang.PcsSt3.Lp2.DisgCapy",
                   "tpSttaicang.PcsSt4.Lp1.DisgCapy", "tpSttaicang.PcsSt4.Lp2.DisgCapy", ],
       "binhai": ["tpStbinhai1.PcsSt1.Lp1.DisgCapy", "tpStbinhai1.PcsSt1.Lp2.DisgCapy",
                  "tpStbinhai1.PcsSt2.Lp1.DisgCapy", "tpStbinhai1.PcsSt2.Lp2.DisgCapy",
                  "tpStbinhai1.PcsSt3.Lp1.DisgCapy", "tpStbinhai1.PcsSt3.Lp2.DisgCapy",
                  "tpStbinhai2.PcsSt4.Lp1.DisgCapy", "tpStbinhai2.PcsSt4.Lp2.DisgCapy",
                  "tpStbinhai2.PcsSt5.Lp1.DisgCapy", "tpStbinhai2.PcsSt5.Lp2.DisgCapy",
                  "tpStbinhai2.PcsSt6.Lp1.DisgCapy", "tpStbinhai2.PcsSt6.Lp2.DisgCapy", ],
       "ygzhen": ["tfStygzhen1.EMS.PCS1.Lp1.AcDisgCapyDaly", "tfStygzhen1.EMS.PCS1.Lp2.AcDisgCapyDaly",
                  "tfStygzhen1.EMS.PCS1.Lp3.AcDisgCapyDaly", "tfStygzhen1.EMS.PCS1.Lp4.AcDisgCapyDaly",
                  "tfStygzhen2.EMS.PCS2.Lp1.AcDisgCapyDaly", "tfStygzhen2.EMS.PCS2.Lp2.AcDisgCapyDaly",
                  "tfStygzhen2.EMS.PCS2.Lp3.AcDisgCapyDaly", "tfStygzhen2.EMS.PCS2.Lp4.AcDisgCapyDaly"],
       "baodian": ["tfStbodian1.Pcs.M1DsgCapyAlw", "tfStbodian2.Pcs.M1DsgCapyAlw", "tfStbodian3.Pcs.M1DsgCapyAlw",
                   "tfStbodian4.Pcs.M1DsgCapyAlw", "tfStbodian5.Pcs.M1DsgCapyAlw"],
       'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6",
                 "dongmu.PCS7"],
       "zgtian": ["tfStzgtian1.EMS.PCS1.Lp1.AcDisgCapyTotl", "tfStzgtian1.EMS.PCS1.Lp2.AcDisgCapyTotl",
                  "tfStzgtian1.EMS.PCS1.Lp3.AcDisgCapyTotl", "tfStzgtian1.EMS.PCS1.Lp4.AcDisgCapyTotl",
                  "tfStzgtian2.EMS.PCS2.Lp1.AcDisgCapyTotl", "tfStzgtian2.EMS.PCS2.Lp2.AcDisgCapyTotl",
                  "tfStzgtian2.EMS.PCS2.Lp3.AcDisgCapyTotl", "tfStzgtian2.EMS.PCS2.Lp4.AcDisgCapyTotl",
                  "tfStzgtian3.EMS.PCS3.Lp1.AcDisgCapyTotl",
                  "tfStzgtian3.EMS.PCS3.Lp2.AcDisgCapyTotl", "tfStzgtian3.EMS.PCS3.Lp3.AcDisgCapyTotl",
                  "tfStzgtian3.EMS.PCS3.Lp4.AcDisgCapyTotl", "tfStzgtian4.EMS.PCS4.Lp1.AcDisgCapyTotl",
                  "tfStzgtian4.EMS.PCS4.Lp2.AcDisgCapyTotl",
                  "tfStzgtian4.EMS.PCS4.Lp3.AcDisgCapyTotl", "tfStzgtian4.EMS.PCS4.Lp4.AcDisgCapyTotl"],
       "houma":["tfSthoumaA1.EMS.A502.PCS1.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS1.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS2.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS2.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS3.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS3.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS4.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS4.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS5.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS5.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS6.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A502.PCS6.Lp2.ACTotlDisg","tfSthoumaA1.EMS.A503.PCS7.Lp1.ACTotlDisg","tfSthoumaA1.EMS.A503.PCS7.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS8.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS8.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS9.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS9.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS10.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS10.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS11.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS11.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS12.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A503.PCS12.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A504.PCS13.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A504.PCS13.Lp2.ACTotlDisg","tfSthoumaA2.EMS.A504.PCS14.Lp1.ACTotlDisg","tfSthoumaA2.EMS.A504.PCS14.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS1.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS1.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS2.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS2.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS3.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS3.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS4.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B504.PCS4.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS5.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS5.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS6.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS6.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS7.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS7.Lp2.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS8.Lp1.ACTotlDisg","tfSthoumaB1.EMS.B505.PCS8.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B505.PCS9.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B505.PCS9.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B505.PCS10.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B505.PCS10.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS11.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS11.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS12.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS12.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS13.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS13.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS14.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS14.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS15.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS15.Lp2.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS16.Lp1.ACTotlDisg","tfSthoumaB2.EMS.B506.PCS16.Lp2.ACTotlDisg"],
       "datong":["tc_datong1.EMS.Energy1.PCS.Lp1.TolDisgEle","tc_datong1.EMS.Energy1.PCS.Lp2.TolDisgEle","tc_datong1.EMS.Energy2.PCS.Lp1.TolDisgEle","tc_datong1.EMS.Energy2.PCS.Lp2.TolDisgEle","tc_datong2.EMS.Energy3.PCS.Lp1.TolDisgEle","tc_datong2.EMS.Energy3.PCS.Lp2.TolDisgEle","tc_datong2.EMS.Energy4.PCS.Lp1.TolDisgEle","tc_datong2.EMS.Energy4.PCS.Lp2.TolDisgEle","tc_datong3.EMS.Energy5.PCS.Lp1.TolDisgEle","tc_datong3.EMS.Energy5.PCS.Lp2.TolDisgEle","tc_datong3.EMS.Energy6.PCS.Lp1.TolDisgEle","tc_datong3.EMS.Energy6.PCS.Lp2.TolDisgEle","tc_datong4.EMS.Energy7.PCS.Lp1.TolDisgEle","tc_datong4.EMS.Energy7.PCS.Lp2.TolDisgEle","tc_datong4.EMS.Energy8.PCS.Lp1.TolDisgEle","tc_datong4.EMS.Energy8.PCS.Lp2.TolDisgEle"],
       "guizhou":["guizhou1a.Energy1.Pcs.Lp1.DayDigEle","guizhou1a.Energy1.Pcs.Lp2.DayDigEle","guizhou1a.Energy2.Pcs.Lp1.DayDigEle","guizhou1a.Energy2.Pcs.Lp2.DayDigEle","guizhou1a.Energy3.Pcs.Lp1.DayDigEle","guizhou1a.Energy3.Pcs.Lp2.DayDigEle","guizhou1b.Energy4.Pcs.Lp1.DayDigEle","guizhou1b.Energy4.Pcs.Lp2.DayDigEle","guizhou1b.Energy5.Pcs.Lp1.DayDigEle","guizhou1b.Energy5.Pcs.Lp2.DayDigEle","guizhou1b.Energy6.Pcs.Lp1.DayDigEle","guizhou1b.Energy6.Pcs.Lp2.DayDigEle","guizhou1c.Energy7.Pcs.Lp1.DayDigEle","guizhou1c.Energy7.Pcs.Lp2.DayDigEle","guizhou1c.Energy8.Pcs.Lp1.DayDigEle","guizhou1c.Energy8.Pcs.Lp2.DayDigEle","guizhou2a.Energy10.Pcs.Lp1.DayDigEle","guizhou2a.Energy10.Pcs.Lp2.DayDigEle","guizhou2a.Energy11.Pcs.Lp1.DayDigEle","guizhou2a.Energy11.Pcs.Lp2.DayDigEle","guizhou2b.Energy12.Pcs.Lp1.DayDigEle","guizhou2b.Energy12.Pcs.Lp2.DayDigEle","guizhou2a.Energy9.Pcs.Lp1.DayDigEle","guizhou2a.Energy9.Pcs.Lp2.DayDigEle","guizhou2b.Energy13.Pcs.Lp1.DayDigEle","guizhou2b.Energy13.Pcs.Lp2.DayDigEle","guizhou2b.Energy14.Pcs.Lp1.DayDigEle","guizhou2b.Energy14.Pcs.Lp2.DayDigEle","guizhou2c.Energy15.Pcs.Lp1.DayDigEle","guizhou2c.Energy15.Pcs.Lp2.DayDigEle","guizhou2c.Energy16.Pcs.Lp1.DayDigEle","guizhou2c.Energy16.Pcs.Lp2.DayDigEle","guizhou3a.Energy17.Pcs.Lp1.DayDigEle","guizhou3a.Energy17.Pcs.Lp2.DayDigEle","guizhou3a.Energy18.Pcs.Lp1.DayDigEle","guizhou3a.Energy18.Pcs.Lp2.DayDigEle","guizhou3a.Energy19.Pcs.Lp1.DayDigEle","guizhou3a.Energy19.Pcs.Lp2.DayDigEle","guizhou3b.Energy20.Pcs.Lp1.DayDigEle","guizhou3b.Energy20.Pcs.Lp2.DayDigEle","guizhou3b.Energy21.Pcs.Lp1.DayDigEle","guizhou3b.Energy21.Pcs.Lp2.DayDigEle","guizhou3b.Energy22.Pcs.Lp1.DayDigEle","guizhou3b.Energy22.Pcs.Lp2.DayDigEle","guizhou3c.Energy23.Pcs.Lp1.DayDigEle","guizhou3c.Energy23.Pcs.Lp2.DayDigEle","guizhou3c.Energy24.Pcs.Lp1.DayDigEle","guizhou3c.Energy24.Pcs.Lp2.DayDigEle","guizhou4a.Energy25.Pcs.Lp1.DayDigEle","guizhou4a.Energy25.Pcs.Lp2.DayDigEle","guizhou4a.Energy26.Pcs.Lp1.DayDigEle","guizhou4a.Energy26.Pcs.Lp2.DayDigEle","guizhou4a.Energy27.Pcs.Lp1.DayDigEle","guizhou4a.Energy27.Pcs.Lp2.DayDigEle","guizhou4b.Energy28.Pcs.Lp1.DayDigEle","guizhou4b.Energy28.Pcs.Lp2.DayDigEle","guizhou4b.Energy29.Pcs.Lp1.DayDigEle","guizhou4b.Energy29.Pcs.Lp2.DayDigEle","guizhou4b.Energy30.Pcs.Lp1.DayDigEle","guizhou4b.Energy30.Pcs.Lp2.DayDigEle","guizhou4c.Energy31.Pcs.Lp1.DayDigEle","guizhou4c.Energy31.Pcs.Lp2.DayDigEle","guizhou4c.Energy32.Pcs.Lp1.DayDigEle","guizhou4c.Energy32.Pcs.Lp2.DayDigEle","guizhou5a.Energy33.Pcs.Lp1.DayDigEle","guizhou5a.Energy33.Pcs.Lp2.DayDigEle","guizhou5a.Energy34.Pcs.Lp1.DayDigEle","guizhou5a.Energy34.Pcs.Lp2.DayDigEle","guizhou5a.Energy35.Pcs.Lp1.DayDigEle","guizhou5a.Energy35.Pcs.Lp2.DayDigEle","guizhou5b.Energy36.Pcs.Lp1.DayDigEle","guizhou5b.Energy36.Pcs.Lp2.DayDigEle","guizhou5b.Energy37.Pcs.Lp1.DayDigEle","guizhou5b.Energy37.Pcs.Lp2.DayDigEle","guizhou5b.Energy38.Pcs.Lp1.DayDigEle","guizhou5b.Energy38.Pcs.Lp2.DayDigEle","guizhou5c.Energy39.Pcs.Lp1.DayDigEle","guizhou5c.Energy39.Pcs.Lp2.DayDigEle","guizhou5c.Energy40.Pcs.Lp1.DayDigEle","guizhou5c.Energy40.Pcs.Lp2.DayDigEle","guizhou6a.Energy41.Pcs.Lp1.DayDigEle","guizhou6a.Energy41.Pcs.Lp2.DayDigEle","guizhou6a.Energy42.Pcs.Lp1.DayDigEle","guizhou6a.Energy42.Pcs.Lp2.DayDigEle","guizhou6a.Energy43.Pcs.Lp1.DayDigEle","guizhou6a.Energy43.Pcs.Lp2.DayDigEle","guizhou6b.Energy44.Pcs.Lp1.DayDigEle","guizhou6b.Energy44.Pcs.Lp2.DayDigEle","guizhou6b.Energy45.Pcs.Lp1.DayDigEle","guizhou6b.Energy45.Pcs.Lp2.DayDigEle","guizhou6b.Energy46.Pcs.Lp1.DayDigEle","guizhou6b.Energy46.Pcs.Lp2.DayDigEle","guizhou6c.Energy47.Pcs.Lp1.DayDigEle","guizhou6c.Energy47.Pcs.Lp2.DayDigEle","guizhou6c.Energy48.Pcs.Lp1.DayDigEle","guizhou6c.Energy48.Pcs.Lp2.DayDigEle","guizhou7a.Energy49.Pcs.Lp1.DayDigEle","guizhou7a.Energy49.Pcs.Lp2.DayDigEle","guizhou7a.Energy50.Pcs.Lp1.DayDigEle","guizhou7a.Energy50.Pcs.Lp2.DayDigEle","guizhou7a.Energy51.Pcs.Lp1.DayDigEle","guizhou7a.Energy51.Pcs.Lp2.DayDigEle","guizhou7b.Energy52.Pcs.Lp1.DayDigEle","guizhou7b.Energy52.Pcs.Lp2.DayDigEle","guizhou7b.Energy53.Pcs.Lp1.DayDigEle","guizhou7b.Energy53.Pcs.Lp2.DayDigEle","guizhou7b.Energy54.Pcs.Lp1.DayDigEle","guizhou7b.Energy54.Pcs.Lp2.DayDigEle","guizhou7c.Energy55.Pcs.Lp1.DayDigEle","guizhou7c.Energy55.Pcs.Lp2.DayDigEle","guizhou7c.Energy56.Pcs.Lp1.DayDigEle","guizhou7c.Energy56.Pcs.Lp2.DayDigEle","guizhou8a.Energy57.Pcs.Lp1.DayDigEle","guizhou8a.Energy57.Pcs.Lp2.DayDigEle","guizhou8a.Energy58.Pcs.Lp1.DayDigEle","guizhou8a.Energy58.Pcs.Lp2.DayDigEle","guizhou8b.Energy59.Pcs.Lp1.DayDigEle","guizhou8b.Energy59.Pcs.Lp2.DayDigEle","guizhou8b.Energy60.Pcs.Lp1.DayDigEle","guizhou8b.Energy60.Pcs.Lp2.DayDigEle"],
       "ygqn":["ygqn.abc.EMS.A.PCS.Energy1.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy1.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy2.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy2.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy3.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy3.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy4.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy4.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy5.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy5.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy6.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy6.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy7.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy7.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy8.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy8.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy9.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy9.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy10.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy10.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy11.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy11.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy12.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy12.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy13.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy13.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy14.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy14.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy15.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy15.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy16.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy16.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy17.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy17.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy18.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy18.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy19.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy19.PCS2.DayDg","ygqn.abc.EMS.A.PCS.Energy20.PCS1.DayDg","ygqn.abc.EMS.A.PCS.Energy20.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy1.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy1.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy2.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy2.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy3.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy3.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy4.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy4.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy5.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy5.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy6.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy6.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy7.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy7.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy8.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy8.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy9.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy9.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy10.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy10.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy11.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy11.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy12.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy12.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy13.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy13.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy14.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy14.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy15.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy15.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy16.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy16.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy17.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy17.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy18.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy18.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy19.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy19.PCS2.DayDg","ygqn.abc.EMS.B.PCS.Energy20.PCS1.DayDg","ygqn.abc.EMS.B.PCS.Energy20.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy1.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy1.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy2.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy2.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy3.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy3.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy4.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy4.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy5.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy5.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy6.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy6.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy7.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy7.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy8.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy8.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy9.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy9.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy10.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy10.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy11.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy11.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy12.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy12.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy13.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy13.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy14.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy14.PCS2.DayDg","ygqn.abc.EMS.C.PCS.Energy15.PCS1.DayDg","ygqn.abc.EMS.C.PCS.Energy15.PCS2.DayDg","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS1.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS2.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS3.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS4.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS5.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS6.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS7.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS8.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS9.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS10.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS11.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS12.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS13.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS14.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS15.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS16.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS17.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS18.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS19.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS20.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS1.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS2.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS3.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS4.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS5.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS6.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS7.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS8.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS9.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS10.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS11.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS12.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS13.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS14.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS15.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS16.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS17.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS18.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS19.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS20.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS1.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS2.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS3.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS4.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS5.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS6.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS7.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS8.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS9.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS10.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS11.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS12.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS13.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS14.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS15.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS16.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS17.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS18.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS19.DgEleDay","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS20.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS1.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS2.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS3.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS4.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS5.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS6.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS7.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS8.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS9.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS10.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS11.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS12.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS13.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS14.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS15.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS16.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS17.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS18.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS19.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS20.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS1.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS2.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS3.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS4.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS5.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS6.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS7.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS8.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS9.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS10.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS11.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS12.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS13.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS14.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS15.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS16.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS17.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS18.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS19.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS20.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS1.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS2.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS3.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS4.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS5.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS6.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS7.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS8.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS9.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS10.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS11.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS12.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS13.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS14.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS15.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS16.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS17.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS18.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS19.DgEleDay","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS20.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS1.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS2.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS3.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS4.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS5.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS6.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS7.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS8.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS9.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS10.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS11.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS12.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS13.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS14.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS15.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS16.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS17.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS18.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS19.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS20.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS1.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS2.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS3.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS4.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS5.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS6.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS7.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS8.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS9.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS10.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS11.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS12.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS13.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS14.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS15.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS16.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS17.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS18.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS19.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS20.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS1.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS2.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS3.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS4.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS5.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS6.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS7.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS8.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS9.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS10.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS11.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS12.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS13.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS14.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS15.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS16.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS17.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS18.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS19.DgEleDay","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS20.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS1.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS2.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS3.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS4.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS5.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS6.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS7.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS8.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS9.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS10.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS11.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS12.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS13.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS14.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS15.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS16.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS17.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS18.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS19.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS20.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS1.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS2.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS3.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS4.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS5.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS6.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS7.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS8.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS9.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS10.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS11.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS12.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS13.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS14.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS15.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS16.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS17.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS18.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS19.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS20.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS1.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS2.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS3.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS4.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS5.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS6.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS7.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS8.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS9.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS10.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS11.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS12.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS13.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS14.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS15.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS16.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS17.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS18.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS19.DgEleDay","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS20.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS1.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS2.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS3.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS4.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS5.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS6.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS7.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS8.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS9.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS10.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS11.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS12.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS13.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS14.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS15.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS16.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS17.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS18.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS19.DgEleDay","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS20.DgEleDay"],
       "shgyu":["shgyu.EMS.PCS.A.Energy1.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy1.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy2.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy2.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy3.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy3.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy4.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy4.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy5.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy5.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy6.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy6.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy7.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy7.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy8.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy8.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy9.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy9.PCS2.AcDyDg","shgyu.EMS.PCS.A.Energy10.PCS1.AcDyDg","shgyu.EMS.PCS.A.Energy10.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy11.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy11.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy12.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy12.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy13.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy13.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy14.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy14.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy15.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy15.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy16.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy16.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy17.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy17.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy18.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy18.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy19.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy19.PCS2.AcDyDg","shgyu.EMS.PCS.B.Energy20.PCS1.AcDyDg","shgyu.EMS.PCS.B.Energy20.PCS2.AcDyDg"]}

yougong = {'halun': ["tpSthalun.PcsSt1.Lp1.RealPwOtInvt", "tpSthalun.PcsSt1.Lp2.RealPwOtInvt",
                 "tpSthalun.PcsSt2.Lp1.RealPwOtInvt", "tpSthalun.PcsSt2.Lp2.RealPwOtInvt"],
       "taicang": ["tpSttaicang.PcsSt1.Lp1.RealPwOtInvt", "tpSttaicang.PcsSt1.Lp2.RealPwOtInvt",
                   "tpSttaicang.PcsSt2.Lp1.RealPwOtInvt", "tpSttaicang.PcsSt2.Lp2.RealPwOtInvt",
                   "tpSttaicang.PcsSt3.Lp1.RealPwOtInvt", "tpSttaicang.PcsSt3.Lp2.RealPwOtInvt",
                   "tpSttaicang.PcsSt4.Lp1.RealPwOtInvt", "tpSttaicang.PcsSt4.Lp2.RealPwOtInvt", ],
       "binhai": ["tpStbinhai1.PcsSt1.Lp1.RealPwOtInvt", "tpStbinhai1.PcsSt1.Lp2.RealPwOtInvt",
                  "tpStbinhai1.PcsSt2.Lp1.RealPwOtInvt", "tpStbinhai1.PcsSt2.Lp2.RealPwOtInvt",
                  "tpStbinhai1.PcsSt3.Lp1.RealPwOtInvt", "tpStbinhai1.PcsSt3.Lp2.RealPwOtInvt",
                  "tpStbinhai2.PcsSt4.Lp1.RealPwOtInvt", "tpStbinhai2.PcsSt4.Lp2.RealPwOtInvt",
                  "tpStbinhai2.PcsSt5.Lp1.RealPwOtInvt", "tpStbinhai2.PcsSt5.Lp2.RealPwOtInvt",
                  "tpStbinhai2.PcsSt6.Lp1.RealPwOtInvt", "tpStbinhai2.PcsSt6.Lp2.RealPwOtInvt" ],
       "ygzhen": [],
       "baodian": ["tfStbodian1.Pcs.RealPw", "tfStbodian2.Pcs.RealPw", "tfStbodian3.Pcs.RealPw",
                   "tfStbodian4.Pcs.RealPw", "tfStbodian5.Pcs.RealPw"], 'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6",
                 "dongmu.PCS7"],
       "zgtian": ["tfStzgtian1.EMS.PCS1.Lp1.Totl_RealPw", "tfStzgtian1.EMS.PCS1.Lp2.Totl_RealPw",
                  "tfStzgtian1.EMS.PCS1.Lp3.Totl_RealPw", "tfStzgtian1.EMS.PCS1.Lp4.Totl_RealPw",
                  "tfStzgtian2.EMS.PCS2.Lp1.Totl_RealPw", "tfStzgtian2.EMS.PCS2.Lp2.Totl_RealPw",
                  "tfStzgtian2.EMS.PCS2.Lp3.Totl_RealPw", "tfStzgtian2.EMS.PCS2.Lp4.Totl_RealPw",
                  "tfStzgtian3.EMS.PCS3.Lp1.Totl_RealPw", "tfStzgtian3.EMS.PCS3.Lp2.Totl_RealPw",
                  "tfStzgtian3.EMS.PCS3.Lp3.Totl_RealPw", "tfStzgtian3.EMS.PCS3.Lp4.Totl_RealPw",
                  "tfStzgtian4.EMS.PCS4.Lp1.Totl_RealPw", "tfStzgtian4.EMS.PCS4.Lp2.Totl_RealPw",
                  "tfStzgtian4.EMS.PCS4.Lp3.Totl_RealPw", "tfStzgtian4.EMS.PCS4.Lp4.Totl_RealPw"],
       "houma":["tfSthoumaA1.EMS.A502.PCS1.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS1.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS2.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS2.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS3.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS3.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS4.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS4.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS5.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS5.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS6.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A502.PCS6.Lp2.AC_TotlActPw","tfSthoumaA1.EMS.A503.PCS7.Lp1.AC_TotlActPw","tfSthoumaA1.EMS.A503.PCS7.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS8.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS8.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS9.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS9.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS10.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS10.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS11.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS11.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS12.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A503.PCS12.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A504.PCS13.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A504.PCS13.Lp2.AC_TotlActPw","tfSthoumaA2.EMS.A504.PCS14.Lp1.AC_TotlActPw","tfSthoumaA2.EMS.A504.PCS14.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS1.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS1.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS2.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS2.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS3.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS3.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS4.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B504.PCS4.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS5.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS5.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS6.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS6.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS7.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS7.Lp2.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS8.Lp1.AC_TotlActPw","tfSthoumaB1.EMS.B505.PCS8.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B505.PCS9.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B505.PCS9.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B505.PCS10.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B505.PCS10.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS11.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS11.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS12.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS12.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS13.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS13.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS14.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS14.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS15.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS15.Lp2.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS16.Lp1.AC_TotlActPw","tfSthoumaB2.EMS.B506.PCS16.Lp2.AC_TotlActPw"],
       "datong":["tc_datong1.EMS.Energy1.PCS.Lp1.AcTolAp","tc_datong1.EMS.Energy1.PCS.Lp2.AcTolAp","tc_datong1.EMS.Energy2.PCS.Lp1.AcTolAp","tc_datong1.EMS.Energy2.PCS.Lp2.AcTolAp","tc_datong2.EMS.Energy3.PCS.Lp1.AcTolAp","tc_datong2.EMS.Energy3.PCS.Lp2.AcTolAp","tc_datong2.EMS.Energy4.PCS.Lp1.AcTolAp","tc_datong2.EMS.Energy4.PCS.Lp2.AcTolAp","tc_datong3.EMS.Energy5.PCS.Lp1.AcTolAp","tc_datong3.EMS.Energy5.PCS.Lp2.AcTolAp","tc_datong3.EMS.Energy6.PCS.Lp1.AcTolAp","tc_datong3.EMS.Energy6.PCS.Lp2.AcTolAp","tc_datong4.EMS.Energy7.PCS.Lp1.AcTolAp","tc_datong4.EMS.Energy7.PCS.Lp2.AcTolAp","tc_datong4.EMS.Energy8.PCS.Lp1.AcTolAp","tc_datong4.EMS.Energy8.PCS.Lp2.AcTolAp"],
        "guizhou":["guizhou1a.Energy1.Pcs.Lp1.ActPw","guizhou1a.Energy1.Pcs.Lp2.ActPw","guizhou1a.Energy2.Pcs.Lp1.ActPw","guizhou1a.Energy2.Pcs.Lp2.ActPw","guizhou1a.Energy3.Pcs.Lp1.ActPw","guizhou1a.Energy3.Pcs.Lp2.ActPw","guizhou1b.Energy4.Pcs.Lp1.ActPw","guizhou1b.Energy4.Pcs.Lp2.ActPw","guizhou1b.Energy5.Pcs.Lp1.ActPw","guizhou1b.Energy5.Pcs.Lp2.ActPw","guizhou1b.Energy6.Pcs.Lp1.ActPw","guizhou1b.Energy6.Pcs.Lp2.ActPw","guizhou1c.Energy7.Pcs.Lp1.ActPw","guizhou1c.Energy7.Pcs.Lp2.ActPw","guizhou1c.Energy8.Pcs.Lp1.ActPw","guizhou1c.Energy8.Pcs.Lp2.ActPw","guizhou2a.Energy10.Pcs.Lp1.ActPw","guizhou2a.Energy10.Pcs.Lp2.ActPw","guizhou2a.Energy11.Pcs.Lp1.ActPw","guizhou2a.Energy11.Pcs.Lp2.ActPw","guizhou2b.Energy12.Pcs.Lp1.ActPw","guizhou2b.Energy12.Pcs.Lp2.ActPw","guizhou2a.Energy9.Pcs.Lp1.ActPw","guizhou2a.Energy9.Pcs.Lp2.ActPw","guizhou2b.Energy13.Pcs.Lp1.ActPw","guizhou2b.Energy13.Pcs.Lp2.ActPw","guizhou2b.Energy14.Pcs.Lp1.ActPw","guizhou2b.Energy14.Pcs.Lp2.ActPw","guizhou2c.Energy15.Pcs.Lp1.ActPw","guizhou2c.Energy15.Pcs.Lp2.ActPw","guizhou2c.Energy16.Pcs.Lp1.ActPw","guizhou2c.Energy16.Pcs.Lp2.ActPw","guizhou3a.Energy17.Pcs.Lp1.ActPw","guizhou3a.Energy17.Pcs.Lp2.ActPw","guizhou3a.Energy18.Pcs.Lp1.ActPw","guizhou3a.Energy18.Pcs.Lp2.ActPw","guizhou3a.Energy19.Pcs.Lp1.ActPw","guizhou3a.Energy19.Pcs.Lp2.ActPw","guizhou3b.Energy20.Pcs.Lp1.ActPw","guizhou3b.Energy20.Pcs.Lp2.ActPw","guizhou3b.Energy21.Pcs.Lp1.ActPw","guizhou3b.Energy21.Pcs.Lp2.ActPw","guizhou3b.Energy22.Pcs.Lp1.ActPw","guizhou3b.Energy22.Pcs.Lp2.ActPw","guizhou3c.Energy23.Pcs.Lp1.ActPw","guizhou3c.Energy23.Pcs.Lp2.ActPw","guizhou3c.Energy24.Pcs.Lp1.ActPw","guizhou3c.Energy24.Pcs.Lp2.ActPw","guizhou4a.Energy25.Pcs.Lp1.ActPw","guizhou4a.Energy25.Pcs.Lp2.ActPw","guizhou4a.Energy26.Pcs.Lp1.ActPw","guizhou4a.Energy26.Pcs.Lp2.ActPw","guizhou4a.Energy27.Pcs.Lp1.ActPw","guizhou4a.Energy27.Pcs.Lp2.ActPw","guizhou4b.Energy28.Pcs.Lp1.ActPw","guizhou4b.Energy28.Pcs.Lp2.ActPw","guizhou4b.Energy29.Pcs.Lp1.ActPw","guizhou4b.Energy29.Pcs.Lp2.ActPw","guizhou4b.Energy30.Pcs.Lp1.ActPw","guizhou4b.Energy30.Pcs.Lp2.ActPw","guizhou4c.Energy31.Pcs.Lp1.ActPw","guizhou4c.Energy31.Pcs.Lp2.ActPw","guizhou4c.Energy32.Pcs.Lp1.ActPw","guizhou4c.Energy32.Pcs.Lp2.ActPw","guizhou5a.Energy33.Pcs.Lp1.ActPw","guizhou5a.Energy33.Pcs.Lp2.ActPw","guizhou5a.Energy34.Pcs.Lp1.ActPw","guizhou5a.Energy34.Pcs.Lp2.ActPw","guizhou5a.Energy35.Pcs.Lp1.ActPw","guizhou5a.Energy35.Pcs.Lp2.ActPw","guizhou5b.Energy36.Pcs.Lp1.ActPw","guizhou5b.Energy36.Pcs.Lp2.ActPw","guizhou5b.Energy37.Pcs.Lp1.ActPw","guizhou5b.Energy37.Pcs.Lp2.ActPw","guizhou5b.Energy38.Pcs.Lp1.ActPw","guizhou5b.Energy38.Pcs.Lp2.ActPw","guizhou5c.Energy39.Pcs.Lp1.ActPw","guizhou5c.Energy39.Pcs.Lp2.ActPw","guizhou5c.Energy40.Pcs.Lp1.ActPw","guizhou5c.Energy40.Pcs.Lp2.ActPw","guizhou6a.Energy41.Pcs.Lp1.ActPw","guizhou6a.Energy41.Pcs.Lp2.ActPw","guizhou6a.Energy42.Pcs.Lp1.ActPw","guizhou6a.Energy42.Pcs.Lp2.ActPw","guizhou6a.Energy43.Pcs.Lp1.ActPw","guizhou6a.Energy43.Pcs.Lp2.ActPw","guizhou6b.Energy44.Pcs.Lp1.ActPw","guizhou6b.Energy44.Pcs.Lp2.ActPw","guizhou6b.Energy45.Pcs.Lp1.ActPw","guizhou6b.Energy45.Pcs.Lp2.ActPw","guizhou6b.Energy46.Pcs.Lp1.ActPw","guizhou6b.Energy46.Pcs.Lp2.ActPw","guizhou6c.Energy47.Pcs.Lp1.ActPw","guizhou6c.Energy47.Pcs.Lp2.ActPw","guizhou6c.Energy48.Pcs.Lp1.ActPw","guizhou6c.Energy48.Pcs.Lp2.ActPw","guizhou7a.Energy49.Pcs.Lp1.ActPw","guizhou7a.Energy49.Pcs.Lp2.ActPw","guizhou7a.Energy50.Pcs.Lp1.ActPw","guizhou7a.Energy50.Pcs.Lp2.ActPw","guizhou7a.Energy51.Pcs.Lp1.ActPw","guizhou7a.Energy51.Pcs.Lp2.ActPw","guizhou7b.Energy52.Pcs.Lp1.ActPw","guizhou7b.Energy52.Pcs.Lp2.ActPw","guizhou7b.Energy53.Pcs.Lp1.ActPw","guizhou7b.Energy53.Pcs.Lp2.ActPw","guizhou7b.Energy54.Pcs.Lp1.ActPw","guizhou7b.Energy54.Pcs.Lp2.ActPw","guizhou7c.Energy55.Pcs.Lp1.ActPw","guizhou7c.Energy55.Pcs.Lp2.ActPw","guizhou7c.Energy56.Pcs.Lp1.ActPw","guizhou7c.Energy56.Pcs.Lp2.ActPw","guizhou8a.Energy57.Pcs.Lp1.ActPw","guizhou8a.Energy57.Pcs.Lp2.ActPw","guizhou8a.Energy58.Pcs.Lp1.ActPw","guizhou8a.Energy58.Pcs.Lp2.ActPw","guizhou8b.Energy59.Pcs.Lp1.ActPw","guizhou8b.Energy59.Pcs.Lp2.ActPw","guizhou8b.Energy60.Pcs.Lp1.ActPw","guizhou8b.Energy60.Pcs.Lp2.ActPw"],
       "ygqn":["ygqn.abc.EMS.A.PCS.Energy1.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy1.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy2.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy2.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy3.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy3.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy4.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy4.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy5.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy5.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy6.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy6.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy7.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy7.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy8.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy8.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy9.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy9.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy10.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy10.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy11.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy11.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy12.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy12.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy13.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy13.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy14.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy14.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy15.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy15.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy16.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy16.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy17.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy17.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy18.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy18.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy19.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy19.PCS2.TlRctPw","ygqn.abc.EMS.A.PCS.Energy20.PCS1.TlRctPw","ygqn.abc.EMS.A.PCS.Energy20.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy1.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy1.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy2.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy2.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy3.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy3.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy4.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy4.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy5.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy5.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy6.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy6.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy7.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy7.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy8.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy8.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy9.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy9.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy10.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy10.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy11.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy11.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy12.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy12.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy13.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy13.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy14.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy14.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy15.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy15.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy16.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy16.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy17.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy17.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy18.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy18.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy19.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy19.PCS2.TlRctPw","ygqn.abc.EMS.B.PCS.Energy20.PCS1.TlRctPw","ygqn.abc.EMS.B.PCS.Energy20.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy1.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy1.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy2.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy2.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy3.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy3.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy4.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy4.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy5.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy5.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy6.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy6.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy7.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy7.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy8.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy8.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy9.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy9.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy10.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy10.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy11.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy11.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy12.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy12.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy13.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy13.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy14.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy14.PCS2.TlRctPw","ygqn.abc.EMS.C.PCS.Energy15.PCS1.TlRctPw","ygqn.abc.EMS.C.PCS.Energy15.PCS2.TlRctPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS1.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS2.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS3.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS4.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS5.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS6.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS7.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS8.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS9.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS10.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS11.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS12.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS13.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS14.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS15.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS16.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS17.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS18.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS19.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS20.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS1.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS2.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS3.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS4.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS5.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS6.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS7.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS8.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS9.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS10.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS11.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS12.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS13.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS14.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS15.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS16.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS17.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS18.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS19.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS20.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS1.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS2.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS3.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS4.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS5.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS6.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS7.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS8.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS9.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS10.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS11.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS12.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS13.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS14.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS15.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS16.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS17.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS18.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS19.ActPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS20.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS1.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS2.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS3.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS4.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS5.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS6.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS7.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS8.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS9.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS10.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS11.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS12.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS13.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS14.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS15.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS16.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS17.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS18.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS19.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS20.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS1.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS2.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS3.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS4.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS5.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS6.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS7.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS8.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS9.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS10.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS11.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS12.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS13.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS14.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS15.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS16.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS17.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS18.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS19.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS20.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS1.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS2.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS3.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS4.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS5.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS6.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS7.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS8.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS9.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS10.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS11.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS12.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS13.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS14.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS15.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS16.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS17.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS18.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS19.ActPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS20.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS1.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS2.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS3.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS4.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS5.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS6.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS7.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS8.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS9.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS10.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS11.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS12.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS13.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS14.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS15.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS16.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS17.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS18.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS19.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS20.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS1.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS2.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS3.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS4.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS5.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS6.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS7.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS8.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS9.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS10.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS11.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS12.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS13.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS14.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS15.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS16.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS17.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS18.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS19.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS20.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS1.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS2.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS3.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS4.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS5.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS6.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS7.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS8.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS9.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS10.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS11.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS12.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS13.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS14.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS15.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS16.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS17.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS18.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS19.ActPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS20.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS1.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS2.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS3.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS4.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS5.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS6.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS7.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS8.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS9.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS10.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS11.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS12.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS13.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS14.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS15.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS16.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS17.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS18.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS19.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS20.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS1.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS2.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS3.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS4.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS5.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS6.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS7.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS8.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS9.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS10.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS11.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS12.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS13.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS14.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS15.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS16.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS17.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS18.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS19.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS20.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS1.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS2.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS3.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS4.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS5.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS6.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS7.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS8.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS9.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS10.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS11.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS12.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS13.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS14.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS15.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS16.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS17.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS18.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS19.ActPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS20.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS1.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS2.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS3.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS4.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS5.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS6.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS7.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS8.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS9.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS10.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS11.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS12.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS13.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS14.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS15.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS16.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS17.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS18.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS19.ActPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS20.ActPw"],
       "shgyu":["shgyu.EMS.PCS.A.Energy1.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy1.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy2.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy2.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy3.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy3.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy4.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy4.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy5.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy5.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy6.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy6.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy7.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy7.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy8.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy8.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy9.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy9.PCS2.AcTlAp","shgyu.EMS.PCS.A.Energy10.PCS1.AcTlAp","shgyu.EMS.PCS.A.Energy10.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy11.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy11.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy12.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy12.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy13.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy13.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy14.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy14.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy15.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy15.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy16.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy16.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy17.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy17.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy18.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy18.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy19.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy19.PCS2.AcTlAp","shgyu.EMS.PCS.B.Energy20.PCS1.AcTlAp","shgyu.EMS.PCS.B.Energy20.PCS2.AcTlAp"]}

wugong = {'halun': ["tpSthalun.PcsSt1.Lp1.ReactPwOtInvt", "tpSthalun.PcsSt1.Lp2.ReactPwOtInvt",
                "tpSthalun.PcsSt2.Lp1.ReactPwOtInvt", "tpSthalun.PcsSt2.Lp2.ReactPwOtInvt"],
      "taicang": ["tpSttaicang.PcsSt1.Lp1.ReactPwOtInvt", "tpSttaicang.PcsSt1.Lp2.ReactPwOtInvt",
                  "tpSttaicang.PcsSt2.Lp1.ReactPwOtInvt", "tpSttaicang.PcsSt2.Lp2.ReactPwOtInvt",
                  "tpSttaicang.PcsSt3.Lp1.ReactPwOtInvt", "tpSttaicang.PcsSt3.Lp2.ReactPwOtInvt",
                  "tpSttaicang.PcsSt4.Lp1.ReactPwOtInvt", "tpSttaicang.PcsSt4.Lp2.ReactPwOtInvt", ],
      "binhai": ["tpStbinhai1.PcsSt1.Lp1.ReactPwOtInvt", "tpStbinhai1.PcsSt1.Lp2.ReactPwOtInvt",
                 "tpStbinhai1.PcsSt2.Lp1.ReactPwOtInvt", "tpStbinhai1.PcsSt2.Lp2.ReactPwOtInvt",
                 "tpStbinhai1.PcsSt3.Lp1.ReactPwOtInvt", "tpStbinhai1.PcsSt3.Lp2.ReactPwOtInvt",
                 "tpStbinhai2.PcsSt4.Lp1.ReactPwOtInvt", "tpStbinhai2.PcsSt4.Lp2.ReactPwOtInvt",
                 "tpStbinhai2.PcsSt5.Lp1.ReactPwOtInvt", "tpStbinhai2.PcsSt5.Lp2.ReactPwOtInvt",
                 "tpStbinhai2.PcsSt6.Lp1.ReactPwOtInvt", "tpStbinhai2.PcsSt6.Lp2.ReactPwOtInvt"],
      "ygzhen":[],
      "baodian": ["tfStbodian1.Pcs.ReactPw", "tfStbodian2.Pcs.ReactPw", "tfStbodian3.Pcs.ReactPw",
                  "tfStbodian4.Pcs.ReactPw", "tfStbodian5.Pcs.ReactPw"],
      'dongmu': ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6",
                 "dongmu.PCS7"], "zgtian": ["tfStzgtian1.EMS.PCS1.Lp1.Totl_ReactPw", "tfStzgtian1.EMS.PCS1.Lp2.Totl_ReactPw",
                               "tfStzgtian1.EMS.PCS1.Lp3.Totl_ReactPw", "tfStzgtian1.EMS.PCS1.Lp4.Totl_ReactPw",
                               "tfStzgtian2.EMS.PCS2.Lp1.Totl_ReactPw",
                               "tfStzgtian2.EMS.PCS2.Lp2.Totl_ReactPw", "tfStzgtian2.EMS.PCS2.Lp3.Totl_ReactPw",
                               "tfStzgtian2.EMS.PCS2.Lp4.Totl_ReactPw", "tfStzgtian3.EMS.PCS3.Lp1.Totl_ReactPw",
                               "tfStzgtian3.EMS.PCS3.Lp2.Totl_ReactPw",
                               "tfStzgtian3.EMS.PCS3.Lp3.Totl_ReactPw", "tfStzgtian3.EMS.PCS3.Lp4.Totl_ReactPw",
                               "tfStzgtian4.EMS.PCS4.Lp1.Totl_ReactPw", "tfStzgtian4.EMS.PCS4.Lp2.Totl_ReactPw",
                               "tfStzgtian4.EMS.PCS4.Lp3.Totl_ReactPw", "tfStzgtian4.EMS.PCS4.Lp4.Totl_ReactPw"],
      'houma':["tfSthoumaA1.EMS.A502.PCS1.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS1.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS2.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS2.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS3.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS3.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS4.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS4.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS5.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS5.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS6.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A502.PCS6.Lp2.AC_TotlReactPw","tfSthoumaA1.EMS.A503.PCS7.Lp1.AC_TotlReactPw","tfSthoumaA1.EMS.A503.PCS7.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS8.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS8.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS9.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS9.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS10.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS10.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS11.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS11.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS12.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A503.PCS12.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A504.PCS13.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A504.PCS13.Lp2.AC_TotlReactPw","tfSthoumaA2.EMS.A504.PCS14.Lp1.AC_TotlReactPw","tfSthoumaA2.EMS.A504.PCS14.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS1.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS1.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS2.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS2.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS3.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS3.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS4.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B504.PCS4.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS5.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS5.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS6.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS6.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS7.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS7.Lp2.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS8.Lp1.AC_TotlReactPw","tfSthoumaB1.EMS.B505.PCS8.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B505.PCS9.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B505.PCS9.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B505.PCS10.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B505.PCS10.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS11.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS11.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS12.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS12.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS13.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS13.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS14.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS14.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS15.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS15.Lp2.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS16.Lp1.AC_TotlReactPw","tfSthoumaB2.EMS.B506.PCS16.Lp2.AC_TotlReactPw"],
      "datong":["tc_datong1.EMS.Energy1.PCS.Lp1.AcTolRtvPw","tc_datong1.EMS.Energy1.PCS.Lp2.AcTolRtvPw","tc_datong1.EMS.Energy2.PCS.Lp1.AcTolRtvPw","tc_datong1.EMS.Energy2.PCS.Lp2.AcTolRtvPw","tc_datong2.EMS.Energy3.PCS.Lp1.AcTolRtvPw","tc_datong2.EMS.Energy3.PCS.Lp2.AcTolRtvPw","tc_datong2.EMS.Energy4.PCS.Lp1.AcTolRtvPw","tc_datong2.EMS.Energy4.PCS.Lp2.AcTolRtvPw","tc_datong3.EMS.Energy5.PCS.Lp1.AcTolRtvPw","tc_datong3.EMS.Energy5.PCS.Lp2.AcTolRtvPw","tc_datong3.EMS.Energy6.PCS.Lp1.AcTolRtvPw","tc_datong3.EMS.Energy6.PCS.Lp2.AcTolRtvPw","tc_datong4.EMS.Energy7.PCS.Lp1.AcTolRtvPw","tc_datong4.EMS.Energy7.PCS.Lp2.AcTolRtvPw","tc_datong4.EMS.Energy8.PCS.Lp1.AcTolRtvPw","tc_datong4.EMS.Energy8.PCS.Lp2.AcTolRtvPw"],
      "guizhou":["guizhou1a.Energy1.Pcs.Lp1.RctPw","guizhou1a.Energy1.Pcs.Lp2.RctPw","guizhou1a.Energy2.Pcs.Lp1.RctPw","guizhou1a.Energy2.Pcs.Lp2.RctPw","guizhou1a.Energy3.Pcs.Lp1.RctPw","guizhou1a.Energy3.Pcs.Lp2.RctPw","guizhou1b.Energy4.Pcs.Lp1.RctPw","guizhou1b.Energy4.Pcs.Lp2.RctPw","guizhou1b.Energy5.Pcs.Lp1.RctPw","guizhou1b.Energy5.Pcs.Lp2.RctPw","guizhou1b.Energy6.Pcs.Lp1.RctPw","guizhou1b.Energy6.Pcs.Lp2.RctPw","guizhou1c.Energy7.Pcs.Lp1.RctPw","guizhou1c.Energy7.Pcs.Lp2.RctPw","guizhou1c.Energy8.Pcs.Lp1.RctPw","guizhou1c.Energy8.Pcs.Lp2.RctPw","guizhou2a.Energy10.Pcs.Lp1.RctPw","guizhou2a.Energy10.Pcs.Lp2.RctPw","guizhou2a.Energy11.Pcs.Lp1.RctPw","guizhou2a.Energy11.Pcs.Lp2.RctPw","guizhou2b.Energy12.Pcs.Lp1.RctPw","guizhou2b.Energy12.Pcs.Lp2.RctPw","guizhou2a.Energy9.Pcs.Lp1.RctPw","guizhou2a.Energy9.Pcs.Lp2.RctPw","guizhou2b.Energy13.Pcs.Lp1.RctPw","guizhou2b.Energy13.Pcs.Lp2.RctPw","guizhou2b.Energy14.Pcs.Lp1.RctPw","guizhou2b.Energy14.Pcs.Lp2.RctPw","guizhou2c.Energy15.Pcs.Lp1.RctPw","guizhou2c.Energy15.Pcs.Lp2.RctPw","guizhou2c.Energy16.Pcs.Lp1.RctPw","guizhou2c.Energy16.Pcs.Lp2.RctPw","guizhou3a.Energy17.Pcs.Lp1.RctPw","guizhou3a.Energy17.Pcs.Lp2.RctPw","guizhou3a.Energy18.Pcs.Lp1.RctPw","guizhou3a.Energy18.Pcs.Lp2.RctPw","guizhou3a.Energy19.Pcs.Lp1.RctPw","guizhou3a.Energy19.Pcs.Lp2.RctPw","guizhou3b.Energy20.Pcs.Lp1.RctPw","guizhou3b.Energy20.Pcs.Lp2.RctPw","guizhou3b.Energy21.Pcs.Lp1.RctPw","guizhou3b.Energy21.Pcs.Lp2.RctPw","guizhou3b.Energy22.Pcs.Lp1.RctPw","guizhou3b.Energy22.Pcs.Lp2.RctPw","guizhou3c.Energy23.Pcs.Lp1.RctPw","guizhou3c.Energy23.Pcs.Lp2.RctPw","guizhou3c.Energy24.Pcs.Lp1.RctPw","guizhou3c.Energy24.Pcs.Lp2.RctPw","guizhou4a.Energy25.Pcs.Lp1.RctPw","guizhou4a.Energy25.Pcs.Lp2.RctPw","guizhou4a.Energy26.Pcs.Lp1.RctPw","guizhou4a.Energy26.Pcs.Lp2.RctPw","guizhou4a.Energy27.Pcs.Lp1.RctPw","guizhou4a.Energy27.Pcs.Lp2.RctPw","guizhou4b.Energy28.Pcs.Lp1.RctPw","guizhou4b.Energy28.Pcs.Lp2.RctPw","guizhou4b.Energy29.Pcs.Lp1.RctPw","guizhou4b.Energy29.Pcs.Lp2.RctPw","guizhou4b.Energy30.Pcs.Lp1.RctPw","guizhou4b.Energy30.Pcs.Lp2.RctPw","guizhou4c.Energy31.Pcs.Lp1.RctPw","guizhou4c.Energy31.Pcs.Lp2.RctPw","guizhou4c.Energy32.Pcs.Lp1.RctPw","guizhou4c.Energy32.Pcs.Lp2.RctPw","guizhou5a.Energy33.Pcs.Lp1.RctPw","guizhou5a.Energy33.Pcs.Lp2.RctPw","guizhou5a.Energy34.Pcs.Lp1.RctPw","guizhou5a.Energy34.Pcs.Lp2.RctPw","guizhou5a.Energy35.Pcs.Lp1.RctPw","guizhou5a.Energy35.Pcs.Lp2.RctPw","guizhou5b.Energy36.Pcs.Lp1.RctPw","guizhou5b.Energy36.Pcs.Lp2.RctPw","guizhou5b.Energy37.Pcs.Lp1.RctPw","guizhou5b.Energy37.Pcs.Lp2.RctPw","guizhou5b.Energy38.Pcs.Lp1.RctPw","guizhou5b.Energy38.Pcs.Lp2.RctPw","guizhou5c.Energy39.Pcs.Lp1.RctPw","guizhou5c.Energy39.Pcs.Lp2.RctPw","guizhou5c.Energy40.Pcs.Lp1.RctPw","guizhou5c.Energy40.Pcs.Lp2.RctPw","guizhou6a.Energy41.Pcs.Lp1.RctPw","guizhou6a.Energy41.Pcs.Lp2.RctPw","guizhou6a.Energy42.Pcs.Lp1.RctPw","guizhou6a.Energy42.Pcs.Lp2.RctPw","guizhou6a.Energy43.Pcs.Lp1.RctPw","guizhou6a.Energy43.Pcs.Lp2.RctPw","guizhou6b.Energy44.Pcs.Lp1.RctPw","guizhou6b.Energy44.Pcs.Lp2.RctPw","guizhou6b.Energy45.Pcs.Lp1.RctPw","guizhou6b.Energy45.Pcs.Lp2.RctPw","guizhou6b.Energy46.Pcs.Lp1.RctPw","guizhou6b.Energy46.Pcs.Lp2.RctPw","guizhou6c.Energy47.Pcs.Lp1.RctPw","guizhou6c.Energy47.Pcs.Lp2.RctPw","guizhou6c.Energy48.Pcs.Lp1.RctPw","guizhou6c.Energy48.Pcs.Lp2.RctPw","guizhou7a.Energy49.Pcs.Lp1.RctPw","guizhou7a.Energy49.Pcs.Lp2.RctPw","guizhou7a.Energy50.Pcs.Lp1.RctPw","guizhou7a.Energy50.Pcs.Lp2.RctPw","guizhou7a.Energy51.Pcs.Lp1.RctPw","guizhou7a.Energy51.Pcs.Lp2.RctPw","guizhou7b.Energy52.Pcs.Lp1.RctPw","guizhou7b.Energy52.Pcs.Lp2.RctPw","guizhou7b.Energy53.Pcs.Lp1.RctPw","guizhou7b.Energy53.Pcs.Lp2.RctPw","guizhou7b.Energy54.Pcs.Lp1.RctPw","guizhou7b.Energy54.Pcs.Lp2.RctPw","guizhou7c.Energy55.Pcs.Lp1.RctPw","guizhou7c.Energy55.Pcs.Lp2.RctPw","guizhou7c.Energy56.Pcs.Lp1.RctPw","guizhou7c.Energy56.Pcs.Lp2.RctPw","guizhou8a.Energy57.Pcs.Lp1.RctPw","guizhou8a.Energy57.Pcs.Lp2.RctPw","guizhou8a.Energy58.Pcs.Lp1.RctPw","guizhou8a.Energy58.Pcs.Lp2.RctPw","guizhou8b.Energy59.Pcs.Lp1.RctPw","guizhou8b.Energy59.Pcs.Lp2.RctPw","guizhou8b.Energy60.Pcs.Lp1.RctPw","guizhou8b.Energy60.Pcs.Lp2.RctPw"],
      "ygqn":["ygqn.abc.EMS.A.PCS.Energy1.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy1.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy2.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy2.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy3.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy3.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy4.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy4.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy5.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy5.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy6.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy6.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy7.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy7.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy8.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy8.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy9.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy9.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy10.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy10.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy11.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy11.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy12.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy12.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy13.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy13.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy14.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy14.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy15.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy15.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy16.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy16.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy17.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy17.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy18.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy18.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy19.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy19.PCS2.TlActPw","ygqn.abc.EMS.A.PCS.Energy20.PCS1.TlActPw","ygqn.abc.EMS.A.PCS.Energy20.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy1.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy1.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy2.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy2.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy3.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy3.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy4.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy4.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy5.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy5.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy6.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy6.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy7.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy7.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy8.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy8.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy9.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy9.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy10.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy10.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy11.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy11.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy12.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy12.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy13.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy13.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy14.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy14.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy15.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy15.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy16.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy16.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy17.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy17.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy18.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy18.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy19.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy19.PCS2.TlActPw","ygqn.abc.EMS.B.PCS.Energy20.PCS1.TlActPw","ygqn.abc.EMS.B.PCS.Energy20.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy1.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy1.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy2.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy2.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy3.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy3.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy4.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy4.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy5.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy5.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy6.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy6.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy7.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy7.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy8.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy8.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy9.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy9.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy10.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy10.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy11.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy11.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy12.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy12.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy13.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy13.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy14.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy14.PCS2.TlActPw","ygqn.abc.EMS.C.PCS.Energy15.PCS1.TlActPw","ygqn.abc.EMS.C.PCS.Energy15.PCS2.TlActPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS1.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS2.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS3.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS4.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS5.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS6.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS7.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS8.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS9.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS10.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS11.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS12.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS13.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS14.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS15.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS16.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS17.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS18.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS19.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy1.PCS20.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS1.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS2.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS3.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS4.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS5.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS6.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS7.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS8.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS9.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS10.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS11.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS12.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS13.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS14.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS15.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS16.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS17.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS18.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS19.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy2.PCS20.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS1.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS2.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS3.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS4.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS5.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS6.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS7.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS8.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS9.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS10.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS11.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS12.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS13.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS14.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS15.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS16.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS17.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS18.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS19.RatPw","ygqn.d.pcs.EMS.D1.PCS.Energy3.PCS20.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS1.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS2.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS3.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS4.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS5.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS6.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS7.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS8.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS9.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS10.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS11.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS12.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS13.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS14.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS15.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS16.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS17.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS18.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS19.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy4.PCS20.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS1.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS2.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS3.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS4.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS5.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS6.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS7.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS8.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS9.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS10.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS11.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS12.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS13.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS14.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS15.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS16.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS17.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS18.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS19.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy5.PCS20.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS1.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS2.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS3.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS4.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS5.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS6.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS7.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS8.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS9.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS10.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS11.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS12.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS13.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS14.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS15.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS16.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS17.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS18.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS19.RatPw","ygqn.d.pcs.EMS.D2.PCS.Energy6.PCS20.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS1.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS2.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS3.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS4.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS5.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS6.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS7.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS8.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS9.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS10.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS11.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS12.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS13.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS14.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS15.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS16.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS17.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS18.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS19.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy7.PCS20.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS1.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS2.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS3.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS4.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS5.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS6.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS7.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS8.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS9.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS10.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS11.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS12.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS13.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS14.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS15.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS16.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS17.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS18.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS19.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy8.PCS20.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS1.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS2.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS3.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS4.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS5.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS6.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS7.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS8.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS9.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS10.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS11.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS12.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS13.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS14.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS15.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS16.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS17.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS18.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS19.RatPw","ygqn.d.pcs.EMS.D3.PCS.Energy9.PCS20.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS1.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS2.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS3.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS4.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS5.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS6.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS7.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS8.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS9.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS10.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS11.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS12.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS13.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS14.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS15.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS16.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS17.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS18.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS19.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy10.PCS20.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS1.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS2.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS3.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS4.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS5.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS6.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS7.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS8.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS9.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS10.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS11.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS12.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS13.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS14.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS15.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS16.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS17.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS18.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS19.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy11.PCS20.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS1.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS2.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS3.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS4.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS5.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS6.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS7.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS8.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS9.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS10.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS11.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS12.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS13.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS14.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS15.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS16.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS17.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS18.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS19.RatPw","ygqn.d.pcs.EMS.D4.PCS.Energy12.PCS20.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS1.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS2.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS3.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS4.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS5.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS6.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS7.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS8.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS9.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS10.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS11.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS12.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS13.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS14.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS15.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS16.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS17.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS18.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS19.RatPw","ygqn.d.pcs.EMS.D5.PCS.Energy13.PCS20.RatPw"],
      "shgyu":["shgyu.EMS.PCS.A.Energy1.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy1.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy2.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy2.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy3.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy3.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy4.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy4.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy5.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy5.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy6.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy6.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy7.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy7.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy8.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy8.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy9.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy9.PCS2.AcTlRp","shgyu.EMS.PCS.A.Energy10.PCS1.AcTlRp","shgyu.EMS.PCS.A.Energy10.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy11.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy11.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy12.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy12.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy13.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy13.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy14.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy14.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy15.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy15.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy16.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy16.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy17.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy17.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy18.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy18.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy19.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy19.PCS2.AcTlRp","shgyu.EMS.PCS.B.Energy20.PCS1.AcTlRp","shgyu.EMS.PCS.B.Energy20.PCS2.AcTlRp"]}


class HisDataAllUseInterface_(BaseHandler):

    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        db_con = self.get_argument('db', 'his')
        time_ = int(self.get_argument('time', 8))
        logging.warn('get---db_con:%s,time:%s' % (db_con, time_))
        lang = self.get_argument('lang', None)  # 英文
        if db_con in exclude_station:
            if db_con not in db_.keys() or db_con not in dianya.keys() or db_con not in dianliu.keys() or db_con not in yougong.keys() or db_con not in wugong.keys():
                return self.customError("DB 参数错误")
        db_conn = db_[db_con]
        try:
        # if 1:
            if kt == 'MainFrameRight':
                F, data = self._getDataMainTwo(db_conn, db_con, time_)
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'MainFrameTFRight':
                F, data = self._getDataMainTFTwo(db_conn, db_con, time_)
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'MainFrameLeft':
                F, data = self._getDataMainThr(db_conn, db_con)
                if F:
                    return self.returnTypeSuc(data)
                else:
                    return self.customError(data)
            elif kt == 'RealAlarmDatas':
                all, total = self._getDatas(AlarmR, timeUtils.getNewTimeStr(), 1, 100,
                                            [AlarmR.station == db_con, AlarmR.status == None])
                return self.returnTypeSuc(all)
            elif kt == 'RealEventDatas':
                all, total = self._getDatas(EventR, timeUtils.getNewTimeStr(), 1, 100,
                                            [or_(EventR.station == None, EventR.station == db_con)])
                return self.returnTypeSuc(all)
            else:
                return self.pathError()
        except Exception as E:
            if db_con in exclude_station:
                for dd in db_conn[1]:
                    dd.rollback()
            else:
                for dd in db_conn[0]:
                    dd.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            if db_con in exclude_station:
                for dd in db_conn[1]:
                    dd.close()
            else:
                for dd in db_conn[0]:
                    dd.close()
            user_session.close()

    @staticmethod
    def _getDataMainTwo(db_con, db, time_):
        '''
        充放电量，有功无功
        '''

        now_time = timeUtils.getAgoTime(1)
        old_time = timeUtils.getAgoTime(7)
        two_time_lists = timeUtils.dateToDataList(old_time, now_time)
        obj = {'page_area': 'right'}
        data1, data2, names, names_1 = [], [], [], [] # 放电和充电数据,放电收益
        m = 0
        if db != 'dongmu':
            conn = pool.connection()
            cursor = conn.cursor()
            for day in two_time_lists:
                sql = "select chag,disg from {} where day='{}' and station_name='{}' ". \
                    format('dws_st_measure_1d', day, db)
                cursor.execute(sql)
                result = cursor.fetchone()
                if result:
                    data1.append(result['disg'])
                    data2.append(result['chag'])
                else:
                    data1.append(0)
                    data2.append(0)
            obj['disgCapy'] = {"data": data1, "time": two_time_lists}
            obj['chagCapy'] = {"data": data2, "time": two_time_lists}
            cursor.close()
            conn.close()

        if db == 'dongmu':
            for day in two_time_lists:
                freport = user_session.query(FReport).filter(FReport.name.in_(pcs_), FReport.name.like('%' + db + '%'),
                                                             FReport.day == day + ' 00:00:00', FReport.cause == 1).all()
                fl, disg, chag = 0, 0, 0
                if freport:
                    for f in freport:
                        disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                            eval(f.pd_disg)) + np.sum(eval(f.gd_disg))
                        chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                            eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                    data1.append(round(disg, 2))
                    data2.append(round(chag, 2))
                else:
                    data1.append(0)
                    data2.append(0)

            obj['disgCapy'] = {"data": data1, "time": two_time_lists}
            obj['chagCapy'] = {"data": data2, "time": two_time_lists}

            #  中间部分数据
            page_data = user_session.query(PageData.name).filter(PageData.page_id == pageData_first[db],
                                                                 PageData.page_area == 'right',
                                                                 PageData.is_use == 1).first()
            for nam in page_data[0].split('#'):
                names_1.append(nam[:-6])

            timeall, dataV, dataA, dataP1, dataP2, data2 = [], [], [], [], [], {}  # 总时间;可充电量,可放电量,有功；无功
            total = len(dianya[db])  # 总的单元个数

            e_start = timeUtils.nowSecs()
            n = e_start - 86400
            start_sec, end_sec = timeUtils.get_ysetday_se()
            dm_table = HisDM('r_measure')
            values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(start_sec, end_sec)).order_by(
                dm_table.time.asc()).all()
            timeall_, alldata = [], []  # 时间，所有数据
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)
            for e in range(dongmu_num):
                d2, d3, d4, d5 = [], [], [], []  # 有功，无功,可充电量,可放电量
                for ii in alldata:
                    PCS_ = 'PCS%s' % (e + 1)
                    if ii['device'] == PCS_:
                        actPo = ii['actPo']  # 有功
                        reapo = ii['reapo']  # 无功
                        d2.append(actPo)
                        d3.append(reapo)
                    BMS_ = 'BMS%s' % (e + 1)
                    if ii['device'] == BMS_:
                        ChCap = ii['ChCap']  # 可充电量
                        DiCap = ii['DiCap']  # 可放电量
                        d4.append(ChCap)
                        d5.append(DiCap)
                # 取相同的间隔时间(采用补数的方法)
                data12, data13, data14, data15 = {}, {}, {}, {}

                data12['time'] = timeall_
                data12['value'] = d2
                data13['time'] = timeall_
                data13['value'] = d3
                data14['time'] = timeall_
                data14['value'] = d4
                data15['time'] = timeall_
                data15['value'] = d5

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                df = pd.DataFrame(data13)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data13["time"] = df["time"].tolist()
                data13["value"] = df["value"].tolist()

                df = pd.DataFrame(data14)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data14["time"] = df["time"].tolist()
                data14["value"] = df["value"].tolist()

                df = pd.DataFrame(data15)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data15["time"] = df["time"].tolist()
                data15["value"] = df["value"].tolist()
                data22 = complete_data(data12, '15T') if data12['time'] else {}
                data23 = complete_data(data13, '15T') if data13['time'] else {}
                data24 = complete_data(data14, '15T') if data14['time'] else {}
                data25 = complete_data(data15, '15T') if data15['time'] else {}
                list_time = []
                if data22:
                    for i in data22['time']:
                        list_time.append(i[8:16])
                    data22['value'][0] = 0
                if data23:
                    data23['value'][0] = 0
                if data24:
                    data24['value'][0] = 0
                if data25:
                    data25['value'][0] = 0
                data2['time'] = list_time
                dataP1.append(data22['value']) if data22 else []
                dataP2.append(data23['value']) if data23 else []
                dataV.append(data24['value']) if data24 else []
                dataA.append(data25['value']) if data25 else []
            data2['dataP1'] = dataP1
            data2['dataP2'] = dataP2
            data2['dataV'] = dataV
            data2['dataA'] = dataA
            data2['total'] = total
            obj["data2"] = data2
        else:
            for p in pcs_:
                if db in p:
                    m += 1
            # day = timeUtils.getAgoTime(1)[:10]
            timeall, dataP1, dataP2, data2 = [], [[], []], [[], []], {}  # 总时间;电压;电流；有功；无功
            conn = pool.connection()
            cursor = conn.cursor()
            # day = '2024-07-22'
            day = timeUtils.getAgoTime(1)[:10]
            sql = "select {}, {},pcs_name,DATE_FORMAT(FLOOR(window_start / {}) * {}, '%d %H:%i') as window_start from {} where \
                                            window_start>='{}' and window_start<='{}' and station_name='{}' group by real_pw, pf,pcs_name, window_start". \
                format(value_name['yougong'], value_name['wugong'], 16, 16, pcs_table_name['1'], day + ' 00:00:00',
                       day + ' 23:50:00', db)
            cursor.execute(sql)
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            dic_pcs = {}
            for r in result:
                if r['window_start'] != None:
                    timeall.append(r['window_start'])
                    if r['pcs_name'] not in dic_pcs.keys():
                        dic_pcs[r['pcs_name']] = {'yougong': [], 'wugong': []}
                        dic_pcs[r['pcs_name']]['yougong'].append(r[value_name['yougong']])
                        dic_pcs[r['pcs_name']]['wugong'].append(r[value_name['wugong']])
                    else:
                        dic_pcs[r['pcs_name']]['yougong'].append(r[value_name['yougong']])
                        dic_pcs[r['pcs_name']]['wugong'].append(r[value_name['wugong']])
            timeall = list(set(timeall))
            timeall.sort()
            data2['time'] = timeall
            data2['total'] = m  # 总的单元个数
            data2['dataP1'] = []
            data2['dataP2'] = []
            for k, v in dic_pcs.items():
                data2['dataP1'].append(v['yougong'])
                data2['dataP2'].append(v['wugong'])
            obj["data2"] = data2
        return True, obj

    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        db_con = self.get_argument('db', 'his')
        logging.warn('post---db_con:%s' % db_con)
        if db_con not in db_.keys():
            if lang == 'en':
                return self.customError("DB parameter error")
            else:
                return self.customError("DB 参数错误")
        db_conn = db_[db_con]
        try:
        # if 1:
            if kt == 'AlarmDatas':  # 获取告警数
                startTime = self.get_argument('startTime', '')  # 开始时间
                endTime = self.get_argument('endTime', '')  # 截止时间
                descr = self.get_argument('descr', '')  # 文本描述
                alarmType = int(self.get_argument('alarmType', 0))  # 告警类别  1故障2报警3无故障4无报警
                handleF = int(self.get_argument('handleF', 0))  # 处理状态 1未处理2已处理
                alarmDescr = int(self.get_argument('alarmDescr', 0))  # 告警描述 1一级2二级3三级
                if DEBUG:
                    logging.info('startTime:%s,endTime:%s,descr:%s,alarmType:%s,handleF:%s,alarmDescr:%s' % (
                    startTime, endTime, descr, alarmType, handleF, alarmDescr))
                filter = [AlarmR.station == db_con]
                if startTime:
                    filter.append(AlarmR.ts >= startTime)
                if endTime:
                    filter.append(AlarmR.ts <= endTime)
                if descr:
                    if lang == 'en':
                        a1 = [Event.en_descr.like('%' + descr + '%'), AlarmR.event_id == Event.id, Event.type == 2]
                    else:
                        a1 = [Event.descr.like('%' + descr + '%'), AlarmR.event_id == Event.id, Event.type == 2]
                    filter = filter + a1
                if alarmType_arr[alarmType]:
                    filter.append(AlarmR.value_descr == alarmType_arr[alarmType])
                if handleF:
                    if handleF == 1:  # 未处理
                        filter.append(AlarmR.status == None)
                    else:
                        filter.append(AlarmR.status == 1)
                if alarmDescr:
                    a2 = [AlarmR.event_id == Event.id, Event.type_id == alarmDescr + 2]
                    filter = filter + a2
                if lang == 'en':
                    all, total = self._getEventOrAlarmDatas(AlarmR, endTime, filter, lang=lang)
                else:
                    all, total = self._getEventOrAlarmDatas(AlarmR, endTime, filter, 0)
                return self.returnTotalSuc(all, total)
            elif kt == 'EventDatas':  # 获取事件数据
                startTime = self.get_argument('startTime', '')  # 开始时间
                endTime = self.get_argument('endTime', '')  # 截止时间
                filter = []
                if startTime:
                    filter.append(EventR.ts >= startTime)
                if endTime:
                    filter.append(EventR.ts <= endTime)
                if lang == 'en':
                    all, total = self._getEventOrAlarmDatas(AlarmR, endTime, filter, lang=lang)
                else:
                    all, total = self._getEventOrAlarmDatas(EventR, endTime, filter, 0)
                return self.returnTotalSuc(all, total)
            elif kt == 'ArarmHandle':  # 告警事件处理
                id = self.get_argument('id', None)  # 具体告警id
                remark = self.get_argument('remark', None)  # 处理意见
                alarm = user_session.query(AlarmR).filter(AlarmR.id == id).first()
                if not alarm:
                    if lang:
                        return self.customError('Invalid id')
                    else:
                        return self.customError('无效id')
                session = self.getOrNewSession()
                user = session.user
                alarm.user_id = user['id']
                alarm.user_ts = timeUtils.getNewTimeStr()
                if remark != None:
                    if lang == 'en':
                        zh_remark = translate_text(remark, 1)
                        alarm.remark = zh_remark
                        alarm.en_remark = remark
                    else:
                        en_remark = translate_text(remark, 2)
                        alarm.remark = remark
                        alarm.en_remark = en_remark
                alarm.status = 1
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AlarmBatchVerify':  # 告警批量确认  只适用于一二级告警
                startTime = self.get_argument('startTime', '')  # 开始时间
                endTime = self.get_argument('endTime', '')  # 截止时间
                descr = self.get_argument('descr', '')  # 文本描述
                alarmType = int(self.get_argument('alarmType', 0))  # 告警类别  1故障2报警3无故障4无报警
                handleF = int(self.get_argument('handleF', 0))  # 处理状态 1未处理2已处理
                alarmDescr = int(self.get_argument('alarmDescr', 0))  # 告警描述 1一级2二级3三级
                if DEBUG:
                    logging.info('startTime:%s,endTime:%s,descr:%s,alarmType:%s,handleF:%s,alarmDescr:%s' % (
                    startTime, endTime, descr, alarmType, handleF, alarmDescr))
                if alarmDescr == 3 or not alarmDescr:
                    return self.customError('非法入参')
                filter = [AlarmR.station == db_con]
                if startTime:
                    filter.append(AlarmR.ts >= startTime)
                if endTime:
                    filter.append(AlarmR.ts <= endTime)
                if descr:
                    a1 = [Event.descr.like('%' + descr + '%'), AlarmR.event_id == Event.id, Event.type == 2]
                    filter = filter + a1
                if alarmType_arr[alarmType]:
                    filter.append(AlarmR.value_descr == alarmType_arr[alarmType])
                if handleF:
                    if handleF == 1:  # 未处理
                        filter.append(AlarmR.status == None)
                    else:
                        filter.append(AlarmR.status == 1)
                if alarmDescr:
                    a2 = [AlarmR.event_id == Event.id, Event.type_id == alarmDescr + 2]
                    filter = filter + a2
                session = self.getOrNewSession()
                user = session.user
                user_session.query(AlarmR).filter(*filter).update({'user_id': user['id']}, synchronize_session=False)
                user_session.commit()
                user_session.query(AlarmR).filter(*filter).update(
                    {'status': 1, 'user_id': user['id'], 'user_ts': timeUtils.getNewTimeStr()},
                    synchronize_session=False)
                user_session.commit()
                user_session.query(AlarmR).filter(*filter).update({'user_ts': timeUtils.getNewTimeStr()},
                                                                  synchronize_session=False)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ChartsHisData':  # 用户的监控历史值
                statusF = int(self.get_argument('statusF', 1))  # 状态量
                measureF = int(self.get_argument('measureF', 1))  # 测量量
                discreteF = int(self.get_argument('discreteF', 1))  # 离散量
                cumulantF = int(self.get_argument('cumulantF', 1))  # 累计量
                area = self.get_argument('area', None)  # 指定区域 不指定则全部
                part = self.get_argument('part', None)  # 指定区域下某个模块 不指定则全部
                startTime = self.get_argument('startTime', timeUtils.getBeforeDay())
                endTime = self.get_argument('endTime', timeUtils.getNewTimeStr())
                if DEBUG:
                    logging.info(
                        'statusF:%s,measureF:%s,discreteF:%s,cumulantF:%s,startTime:%s,endTime:%s,area:%s,part:%s' % (
                        statusF,
                        measureF, discreteF, cumulantF, startTime, endTime, area, part))

                session = self.getOrNewSession()
                user = session.user
                if db_con == 'his':
                    charts = eval(user['charts'])
                else:
                    s = '%s_charts' % db_con
                    charts = eval(user[s])
                # if db_con == 'taicang':
                #     charts = eval(user['taicang_charts'])
                # elif db_con == 'halun':
                #     charts = eval(user['halun_charts'])
                # elif db_con == 'binhai':
                #     charts = eval(user['binhai_charts'])
                # else:
                #     charts = eval(user['charts'])

                # 计算需要查询的表时间
                a = timeUtils.getBetweenMonth(startTime, endTime)
                if db_conn[2] == 'dm_his':  # 只适用于边缘平台，统计当前和小时和上一小时的曲线
                    a.append(timeUtils.getBeforeHouse())
                    a.append(timeUtils.getNowHouse())

                type_arr, type_table = [], {}
                if statusF:
                    al = 'r_status' + pd.Series(a)  # 为每一项加上前缀
                    type_arr.append('status')
                    type_table['status'] = al.tolist()
                if measureF:
                    am = 'r_measure' + pd.Series(a)  # 为每一项加上前缀
                    type_arr.append('measure')
                    type_table['measure'] = am.tolist()
                if discreteF:
                    ad = 'r_discrete' + pd.Series(a)  # 为每一项加上前缀
                    type_arr.append('discrete')
                    type_table['discrete'] = ad.tolist()
                if cumulantF:
                    ac = 'r_cumulant' + pd.Series(a)  # 为每一项加上前缀
                    type_arr.append('cumulant')
                    type_table['cumulant'] = ac.tolist()
                if db_con == 'dongmu':
                    time_all, obj = self._getHisData_1(type_table, type_arr, charts, startTime, endTime, db_conn, area,
                                                       part)
                    data = {'time': time_all, 'value': obj}
                    return self.returnTypeSuc(data)
                else:
                    time_all, obj = self._getHisData(type_table, type_arr, charts, startTime, endTime, db_conn, area,
                                                     part)
                data = self._handleTimeAndValue(time_all, obj)
                return self.returnTypeSuc(data)

            else:
                self.pathError()
        except Exception as E:
            if db_con not in exclude_station:
                for dd in db_conn[0]:
                    dd.rollback()
            else:
                for dd in db_conn[1]:
                    dd.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()
            if db_con not in exclude_station:
                for dd in db_conn[0]:
                    dd.rollback()
            else:
                for dd in db_conn[1]:
                    dd.rollback()

    def _getHisData(self, hisTables, type_arr, charts, startTime, endTime, db_con, area, part):
        '''
        查询历史数据
        hisTable：实体集合，正常情况下包含一个月表，一个前一小时表，一个当前小时表
        type_arr:数据类型
        charts：监控的所有内容
        startT：开始时间
        endT：截止时间
        obj：查询后统一返回
        all：存放所时间
        area：区域
        part：区域下模块
        '''
        obj, all = {}, []
        if area in charts.keys():
            part_obj = {}
            if part in charts[area].keys():
                part_obj[part] = self._getHisDataByPart(charts, area, hisTables, db_con, type_arr, startTime, endTime,
                                                        part, all)
            elif not part:
                for parts in charts[area].keys():
                    part_obj[parts] = self._getHisDataByPart(charts, area, hisTables, db_con, type_arr, startTime,
                                                             endTime, parts, all)
            obj[area] = part_obj
        elif not area:
            for areas in charts.keys():  # 所有区域
                part_obj = {}
                if part in charts[areas].keys():
                    part_obj[part] = self._getHisDataByPart(charts, areas, hisTables, db_con, type_arr, startTime,
                                                            endTime, part, all)
                elif not part:
                    for parts in charts[areas].keys():
                        part_obj[parts] = self._getHisDataByPart(charts, areas, hisTables, db_con, type_arr, startTime,
                                                                 endTime, parts, all)
                obj[areas] = part_obj

        return all, obj

    def _getHisData_1(self, hisTables, type_arr, charts, startTime, endTime, db_con, area, part):
        '''
        查询历史数据
        hisTable：实体集合，正常情况下包含一个月表，一个前一小时表，一个当前小时表
        type_arr:数据类型
        charts：监控的所有内容
        startT：开始时间
        endT：截止时间
        obj：查询后统一返回
        all：存放所时间
        area：区域
        part：区域下模块
        '''
        obj, all = {}, []
        if area in charts.keys():
            part_obj = {}
            if part in charts[area].keys():
                all, part_obj[part] = self._yuanshengsql_1(charts, area, hisTables, db_con, type_arr, startTime,
                                                           endTime, part, all)
            elif not part:
                for parts in charts[area].keys():
                    all, part_obj[parts] = self._yuanshengsql_1(charts, area, hisTables, db_con, type_arr, startTime,
                                                                endTime, parts, all)
            obj[area] = part_obj
        elif not area:
            for areas in charts.keys():  # 所有区域
                part_obj = {}
                if part in charts[areas].keys():
                    all, part_obj[part] = self._yuanshengsql_1(charts, areas, hisTables, db_con, type_arr, startTime,
                                                               endTime, part, all)
                elif not part:
                    for parts in charts[areas].keys():
                        all, part_obj[parts] = self._yuanshengsql_1(charts, areas, hisTables, db_con, type_arr,
                                                                    startTime, endTime, parts, all)
                obj[areas] = part_obj
        # print all
        return all, obj

    def _yuanshengsql_1(self, charts, area, hisTables, db_con, type_arr, startT, endT, part, all):
        '''查询东睦历史数据'''

        startT_ = timeUtils.timeStrToTamp(startT)
        endT_ = timeUtils.timeStrToTamp(endT)
        type_obj = []
        timeall_ = []
        # for con in db_con[0]:
        #     conn = con.raw_connection()
        #     cursor = conn.cursor()
        tb = endT.replace('-', '')[:6]
        for type_ in type_arr:
            if type_ in charts[area][part].keys():
                # time_ym = startT_[]
                names = eval(charts[area][part][type_])
                for name in names:
                    arr = [[], []]
                    n = name.split('.')
                    dm_table = HisDM('r_' + type_)
                    # mycol = dongmu_mongo_db['r_'+type_+tb]
                    # values_mong = mycol.find({'time': {'$gte': startT_, '$lte': endT_}})
                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                        dm_table.time.between(startT_, endT_)).order_by(dm_table.time.asc()).all()
                    values_1 = {'datainfo': {}}
                    HisTable = HisACDMS_1('t_' + type_)
                    if type_ == 'discrete':
                        device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == 'ReMod').all()
                    else:
                        device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == n[1]).all()
                    for i in device_:
                        if type_ == 'status':
                            timeall_1 = []
                            value_aa = []  # 查询出来的值
                            value_ = mqtt_session.query(StatusPT.id, StatusPT.name, StatusPT.descr).filter(
                                StatusPT.device_id == i[0]).all()
                            for e in value_:
                                if e[1] == n[2]:  # 后缀名一样，即一个点
                                    for value_mon in values_mong:
                                        data_info = json.loads(value_mon[0])
                                        value_mong = data_info['body']
                                        time_1 = data_info['utime']
                                        timeArray = time.localtime(time_1)  # 秒数
                                        otherStyleTime = time.strftime('%Y-%m-%d %H:%M:%S', timeArray)
                                        timeall_1.append(otherStyleTime)
                                        for v in value_mong:
                                            if v['device'] == n[1]:
                                                if n[2] in v.keys():
                                                    value_obj = v[n[2]]  # 历史值
                                                    value_aa.append(value_obj)
                                    type_obj.append(
                                        {'name': name, 'descr': e[2], 'type': type_, 'unit': '', 'value': value_aa})
                                else:
                                    value = mqtt_session.query(StatusBitsPT.name, StatusBitsPT.descr,
                                                               StatusBitsPT.bits).filter(
                                        StatusBitsPT.status_id == e[0]).all()
                                    for v in value:
                                        if v[0] == n[2]:
                                            for value_mon in values_mong:
                                                values_1['datainfo'] = value_mon[0]
                                                value_mong = json.loads(values_1['datainfo'])['body']
                                                time_1 = json.loads(values_1['datainfo'])['utime']
                                                timeArray = time.localtime(time_1)  # 秒数
                                                otherStyleTime = time.strftime('%Y-%m-%d %H:%M:%S', timeArray)
                                                timeall_1.append(otherStyleTime)

                                                for ii in value_mong:
                                                    if ii['device'] == n[2]:
                                                        if n[2] in ii.keys():
                                                            bit_n = '{:016b}'.format(int(ii[n[2]]))  # 转成2进制，高位补零
                                                            bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                                            value_obj = bit_list[v[2]]
                                                            if value_obj:
                                                                value_aa.append(value_obj)
                                        type_obj.append(
                                            {'name': name, 'descr': v[1], 'type': type_, '': '', 'value': value_aa})
                            if not timeall_:
                                timeall_ = timeall_1
                                # break
                        else:
                            timeall_2 = []
                            # 根据设备表里查询的id，查询名称，描述，单位
                            if type_ == 'discrete':
                                value = mqtt_session.query(HisTable.name, HisTable.descr).filter(
                                    HisTable.device_id == i[0]).all()
                            else:
                                value = mqtt_session.query(HisTable.name, HisTable.descr, HisTable.unit).filter(
                                    HisTable.device_id == i[0]).all()
                            value_bb = []  # 查询出来的值
                            for v in value:  # 循环mysql查询出来的名称，描述，单位
                                if v[0] == n[2]:
                                    for value_mon in values_mong:  # 循环mongdb里查询出来的结果
                                        values_1['datainfo'] = value_mon['datainfo']
                                        value_mong = json.loads(values_1['datainfo'])['body']
                                        time_1 = json.loads(values_1['datainfo'])['utime']
                                        timeArray = time.localtime(time_1)  # 秒数
                                        otherStyleTime = time.strftime('%Y-%m-%d %H:%M:%S', timeArray)
                                        timeall_2.append(otherStyleTime)

                                        for ii in value_mong:
                                            if ii['device'] == n[1]:
                                                if n[2] in ii.keys():
                                                    value_obj = ii[n[2]]  # 历史值
                                                    value_bb.append(value_obj)
                                    if type_ == 'discrete':
                                        type_obj.append(
                                            {'name': name, 'descr': v[1], 'type': type_, 'unit': '', 'value': value_bb})
                                    else:
                                        type_obj.append({'name': name, 'descr': v[1], 'type': type_, 'unit': v[2],
                                                         'value': value_bb})
                            if not timeall_:
                                timeall_ = timeall_2
                                # break
            mqtt_session.close()
        # print timeall_
        return timeall_, type_obj

    def _getHisDataByPart(self, charts, area, hisTables, db_con, type_arr, startTime, endTime, part, all):
        '''获取指定区域下执行模块的历史数据'''
        startT = timeUtils.timeStrToTamp(startTime)
        endT = timeUtils.timeStrToTamp(endTime)
        time_c = endT - startT
        if time_c < 14400:  # 4小时
            return self._min4hour(charts, area, hisTables, db_con[1], type_arr, startT, endT, part, all)
        elif time_c < 86400:
            return self._onedaydata(charts, area, hisTables, db_con[1], type_arr, startT, endT, part, all)
        else:
            time_l = timeUtils.dateToDataList(startTime, endTime)
            return self._yuanshengsql(charts, area, hisTables, db_con, type_arr, startT, endT, part, all,
                                      45 * len(time_l))

    def _min4hour(self, charts, area, hisTables, db_con, type_arr, startT, endT, part, all):
        '''获取小于4小时的数据，查询毫秒'''
        type_obj = []
        for type_ in type_arr:
            if type_ in charts[area][part].keys():
                names = eval(charts[area][part][type_])
                for name in names:
                    arr = [[], []]
                    bean = real_data(type_, name, 'db')
                    if not bean['desc']:
                        continue
                        # bean = real_data(type_,name,'ram')
                    for hisTable in hisTables[type_]:
                        HisTable = HisACDMS(hisTable)
                        for db_c in db_con:
                            values = db_c.query(HisTable.value.label('value'),
                                                func.from_unixtime((HisTable.dts_s), "%Y-%m-%d %H:%i:%s").label(
                                                    'dts_s'), HisTable.dts_ms.label('dts_ms')
                                                ).filter(HisTable.name == name,
                                                         HisTable.dts_s.between(startT, endT)).order_by(
                                HisTable.dts_s.asc()).all()
                            for val in values:
                                s = '{}.{}'.format(val.dts_s[11:], val.dts_ms)
                                all.append(s)
                                arr[0].append(s)
                                arr[1].append(val.value)
                    type_obj.append(
                        {'name': name, 'descr': bean['desc'], 'value': arr, 'unit': bean['unit'], 'type': type_})
        return type_obj

    def _onedaydata(self, charts, area, hisTables, db_con, type_arr, startT, endT, part, all):
        '''获取超4小时的数据'''
        type_obj = []
        for type_ in type_arr:
            if type_ in charts[area][part].keys():
                names = eval(charts[area][part][type_])
                for name in names:
                    arr = [[], []]
                    bean = real_data(type_, name, 'db')
                    if not bean['desc']:
                        continue
                        # bean = real_data(type_,name,'ram')
                    for hisTable in hisTables[type_]:
                        HisTable = HisACDMS(hisTable)
                        for db_c in db_con:
                            values = db_c.query(HisTable.value.label('value'),
                                                func.from_unixtime((HisTable.dts_s), "%Y-%m-%d %H:%i:%s").label('dts_s')
                                                ).filter(HisTable.name == name,
                                                         HisTable.dts_s.between(startT, endT)).order_by(
                                HisTable.dts_s.asc()).all()
                            for val in values:
                                all.append(val.dts_s[11:])
                                arr[0].append(val.dts_s[11:])
                                arr[1].append(val.value)
                    type_obj.append(
                        {'name': name, 'descr': bean['desc'], 'value': arr, 'unit': bean['unit'], 'type': type_})

        return type_obj

    def _yuanshengsql(self, charts, area, hisTables, db_con, type_arr, startT, endT, part, all, secs):
        '''超过1天的暂定45秒获取一个数据，采用原生连接'''
        type_obj = []
        for con in db_con[0]:
            conn = con.raw_connection()
            cursor = conn.cursor()
            for type_ in type_arr:
                if type_ in charts[area][part].keys():
                    names = eval(charts[area][part][type_])
                    for name in names:
                        arr = [[], []]
                        bean = real_data(type_, name, 'db')
                        if not bean['desc']:
                            continue
                            # bean = real_data(type_,name,'ram')
                        for hisTable in hisTables[type_]:
                            if _tableIsExist(db_con, hisTable):
                                sql = "select value,DATE_FORMAT(FLOOR(DATE_FORMAT(FROM_UNIXTIME(dts_s), '%Y%m%d%H%i%s')/{0})*{0},'%Y-%m-%d %H:%i:%s') as time from {1} where \
                                dts_s>='{2}' and dts_s<='{3}' and name='{4}' group by time".format(secs, hisTable,
                                                                                                   startT, endT, name)
                                cursor.execute(sql)
                                values = cursor.fetchall()
                                for val in values:
                                    if val[1]:
                                        all.append(val[1])
                                        arr[0].append(val[1])
                                        arr[1].append(val[0])
                        if arr[0]:  # 有值集合
                            type_obj.append({'name': name, 'descr': bean['desc'], 'value': arr, 'unit': bean['unit'],
                                             'type': type_})

            cursor.close()
            conn.close()
        return type_obj

    def _handleTimeAndValue(self, time_all, obj):
        # 对所有时间去重排序
        time_all = list(set(time_all))
        time_all.sort()
        len_ = len(time_all)
        for a in obj.keys():  # 所有区域
            for p in obj[a].keys():
                for obje in obj[a][p]:
                    timValue = obje['value']
                    times = timValue[0]
                    value = timValue[1]
                    for ind in range(len_):
                        try:
                            i = times.index(time_all[ind])
                        except:
                            if ind == 0:
                                value.insert(0, 0)
                            else:
                                value.insert(ind, value[ind - 1])
                    # for tim_all in time_all:
                    #     ind = time_all.index(tim_all)
                    #     if tim_all not in times:  # 当前数据里没有总时间，即缺少此刻数据点
                    #         if ind == 0:
                    #             value.insert(0,0)
                    #         else:
                    #             value.insert(ind,value[ind-1])
                    obje['value'] = value

        return {'time': time_all, 'value': obj}

    def _getEventOrAlarmDatas(self, bean, endTime, filter, lang=None):
        '''
        获取告警和事件数据
        f:1为告警，0为事件
        '''
        pageNum = int(self.get_argument('pageNum', 1))
        pageSize = int(self.get_argument('pageSize', 20))

        return self._getDatas(bean, endTime, pageNum, pageSize, filter, lang)

    def _getDatas(self, bean, endTime, pageNum, pageSize, filter, lang=None):
        user_session_ = get_user_session()
        try:
            all, total = [], 0
            total = user_session_.query(func.count(bean.id)).filter(*filter).scalar()
            stations = user_session_.query(bean).filter(*filter).order_by(bean.ts.desc()).order_by(
                bean.event_id.asc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
            for st in stations:
                js_on = eval(str(st))
                if lang == 'en':
                    replaced_data = bean.replace_en_fields(js_on, "")
                    js_on.update(replaced_data)
                    js_on['value_descr'] = js_on['en_value_descr']
                    js_on['descr'] = js_on['en_descr']
                    js_on['alarm_descr'] = js_on['en_alarm_descr']
                    js_on['user_descr'] = js_on['en_user_descr']
                    js_on['user_descr'] = js_on['en_user_descr']
                all.append(js_on)
            if not endTime:
                endTime = timeUtils.getNewTimeStr()
            le = len(stations)
            if le > 0:
                all[-1]['sc'] = timeUtils.timeDiff(all[-1]['ts'], endTime)
                for i in range(le - 1):
                    now_data = all[i]
                    next_data = all[i + 1]
                    if now_data['event_id'] == next_data['event_id']:
                        all[i]['sc'] = timeUtils.timeDiff(next_data['ts'], now_data['ts'])
                    else:
                        all[i]['sc'] = timeUtils.timeDiff(next_data['ts'], endTime)
                all = sorted(all, key=lambda x: x['ts'], reverse=True)  # 倒序排
            return all, total
        except Exception as e:
            logging.error(e)
            self.requestError()
        finally:
            user_session_.close()

    @staticmethod
    def _getDataMainThr(db_con, db):
        '''
        处理首页最左下角数据
        安全运行时间，减少碳排放量
        '''
        station_day = user_session.query(Station.start_ts).filter(Station.name == db).first()

        nowStr = timeUtils.getNewTimeStr()
        st_day = str(station_day[0])
        dd = timeUtils.betweenDayNum(st_day, nowStr)

        res = user_session.query(func.date_format(EventR.op_ts, '%Y-%m-%d').label('date'),func.count('*').label('cnt')).filter(
            EventR.event_id == Event.id, Event.type_id == 3, EventR.station == db).group_by('date').all()
        dd = dd - len(res)
        time_line = timeUtils.getBetweenMonth(st_day, nowStr)

        # thr_time_list = timeUtils.dateToDataList(tt,nowStr)
        # 查询放电量名称集合
        page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                        PageData.type_name == 'measure', PageData.method == 'line',
                                                        PageData.is_use == 1,
                                                        PageData.return_key == 'disgCapy').all()
        val = 0
        if time_line:
            if db == 'dongmu':
                m = nowStr[:7].replace('-', '')
                nowStr_Stamp = timeUtils.timeStrToTamp(nowStr)
                # Table = 'r_cumulant%s' % (m)

                BMS_list = ['BMS1', 'BMS2', 'BMS3', 'BMS4', 'BMS5', 'BMS6', 'BMS7']
                # mycol = dongmu_mongo_db[Table]
                # values = mycol.find({'time': {'$gte': nowStr_Stamp}})
                # values = mycol.find({'time': {'$gte': 1681454693}})
                dm_table = HisDM('r_cumulant')
                values = dongmu_session.query(dm_table.datainfo).order_by(dm_table.time.asc()).all()
                values_1 = {'datainfo': {}}
                for i in values:
                    values_1['datainfo'] = i[0]
                    value = json.loads(values_1['datainfo'])['body']

                    for ii in value:
                        if ii['device'][:3] == 'BMS':
                            val = val + float(ii['CuDis'])  # 累计放电量
            else:
                page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                                PageData.type_name == 'measure',
                                                                PageData.method == 'line', PageData.is_use == 1,
                                                                PageData.return_key == 'disgCapy').all()
                rtable = '%s%s' % ('r_measure', time_line[-1])
                HisTable = HisACDMS(rtable)
                for data_name in page_data:
                    names = data_name.name
                    for name in names.split("#"):
                        for db_c in db_con[1]:
                            values = db_c.query(HisTable.value).filter(HisTable.name == name).order_by(
                                HisTable.dts_s.desc()).first()
                            if values:
                                val = val + values[0]
        va = val * 0.997
        if va < 10000:
            carbon = va
            carbon_unit = '吨'
        else:
            carbon = num_retain(va / 10000)
            carbon_unit = '万吨'

        return True, {"days": dd, 'carbon': carbon, 'carbon_unit': carbon_unit, 'days_unit': '天'}

    @staticmethod
    def _getDataMainTFTwo(db_con, db, time_):
        '''
        处理首页右侧三块数据,调峰使用
        充放电量，收益（计算收益依据return_key的值为disgCapy）
        '''

        #  中间部分数据
        now_time = timeUtils.getAgoTime(1)
        old_time = timeUtils.getAgoTime(7)
        two_time_lists = timeUtils.dateToDataList(old_time, now_time)
        months = timeUtils.getBetweenMonth(old_time, now_time)
        selectTable = 'r_measure%s' % (months[-1])
        hourstable = 'r_measure%s' % timeUtils.getNewTimeStr().replace('-', '').replace(' ', '')[:10]

        obj = {'page_area': 'right'}
        data1, data2, names, names_1 = [], [], [], []  # 放电和充电数据,放电收益
        page_data = user_session.query(PageData.name).filter(PageData.page_id == pageData_first[db],
                                                             PageData.page_area == 'right',
                                                             PageData.is_use == 1).first()
        if db == 'dongmu':
            tables = [selectTable]
            for nam in page_data[0].split('#'):
                names_1.append(nam[:-6])
        else:
            tables = ['ods_r_measure1']
            if page_data:
                for nam in page_data[0].split('#'):
                    names_1.append(nam[:-14])
        for day in two_time_lists:
            freport = user_session.query(FReport).filter(FReport.name.in_(names_1), FReport.day == day + ' 00:00:00',
                                                         FReport.cause == 1).all()
            fl, disg, chag = 0, 0, 0
            if freport:
                for f in freport:
                    # fl = fl+(np.sum(eval(f.jf_disg))-np.sum(eval(f.jf_chag)))*jf+(np.sum(eval(f.fd_disg))-np.sum(eval(f.fd_chag)))*fd+(np.sum(eval(f.pd_disg))-np.sum(eval(f.pd_chag)))*pdd+(np.sum(eval(f.gd_disg))-np.sum(eval(f.gd_chag)))*gd
                    disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(
                        eval(f.gd_disg))
                    chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(
                        eval(f.gd_chag))
                data1.append(disg)
                data2.append(chag)
                # data3.append(round(fl,2))

            else:
                data1.append(0)
                data2.append(0)
                # data3.append(0)

        obj['disgCapy'] = {"data": data1, "time": two_time_lists}
        obj['chagCapy'] = {"data": data2, "time": two_time_lists}

        timeall, dataV, dataA, dataP1, dataP2, data2 = [], [], [], [], [], {}  # 总时间;可充电量,可放电量,有功；无功
        total = len(dianya[db])  # 总的单元个数

        e_start = timeUtils.nowSecs()
        n = e_start - 86400
        data_start_time = timeUtils.ssTtimes(n)[8:16]
        if db == 'dongmu':
            dm_table = HisDM('r_measure')
            # mycol = dongmu_mongo_db[selectTable]
            # values = mycol.find({'utime': {'$gte': n, '$lte': e_start}})
            values = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(n, e_start)).order_by(
                dm_table.time.asc()).all()
            timeall_, alldata = [], []  # 时间，所有数据
            for i in values:
                time_1 = json.loads(i['datainfo'])['utime']
                timeall_.append(timeUtils.ssTtimes(time_1))
                value = json.loads(i['datainfo'])['body']
                for v in value:
                    alldata.append(v)

            for e in range(dongmu_num):
                d2, d3, d4, d5 = [], [], [], []  # 有功，无功,可充电量,可放电量
                # values = mycol.find({'time': {'$gte': 1681712841, '$lte': 1681713776}})
                # values_1 = {'datainfo': {}}
                for ii in alldata:
                    # print '*************',ii,type(ii)
                    PCS_ = 'PCS%s' % (e + 1)
                    if ii['device'] == PCS_:
                        actPo = ii['actPo']  # 有功
                        reapo = ii['reapo']  # 无功
                        d2.append(actPo)
                        d3.append(reapo)
                    BMS_ = 'BMS%s' % (e + 1)
                    if ii['device'] == BMS_:
                        ChCap = ii['ChCap']  # 可充电量
                        DiCap = ii['DiCap']  # 可放电量
                        d4.append(ChCap)
                        d5.append(DiCap)
                # 取相同的间隔时间(采用补数的方法)
                data12, data13, data14, data15 = {}, {}, {}, {}

                data12['time'] = timeall_
                data12['value'] = d2
                data13['time'] = timeall_
                data13['value'] = d3
                data14['time'] = timeall_
                data14['value'] = d4
                data15['time'] = timeall_
                data15['value'] = d5

                df = pd.DataFrame(data12)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data12["time"] = df["time"].tolist()
                data12["value"] = df["value"].tolist()

                df = pd.DataFrame(data13)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data13["time"] = df["time"].tolist()
                data13["value"] = df["value"].tolist()

                df = pd.DataFrame(data14)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data14["time"] = df["time"].tolist()
                data14["value"] = df["value"].tolist()

                df = pd.DataFrame(data15)
                df = df.drop_duplicates(subset=["time"], keep="first")
                # 转换回字典
                data15["time"] = df["time"].tolist()
                data15["value"] = df["value"].tolist()
                data22 = complete_data(data12, '15T') if data12['time'] else {}
                data23 = complete_data(data13, '15T') if data13['time'] else {}
                data24 = complete_data(data14, '15T') if data14['time'] else {}
                data25 = complete_data(data15, '15T') if data15['time'] else {}

                list_time = []
                if data22:
                    for i in data22['time']:
                        list_time.append(i[8:16])
                    data22['value'][0] = 0
                if data23:
                    data23['value'][0] = 0
                if data24:
                    data24['value'][0] = 0
                if data25:
                    data25['value'][0] = 0
                data2['time'] = list_time
                dataP1.append(data22['value']) if data22 else []
                dataP2.append(data23['value']) if data23 else []
                dataV.append(data24['value']) if data24 else []
                dataA.append(data25['value']) if data25 else []

            data2['dataP1'] = dataP1
            data2['dataP2'] = dataP2
            data2['dataV'] = dataV
            data2['dataA'] = dataA
            data2['total'] = total
            obj["data2"] = data2
        elif db == 'ygzhen':
            #  计算功率
            all, arr, pw_line = [], [[[], []], [[], []], [[], []], [[], []], [[], []], [[], []], [[], []], [[], []],
                                     [[], []], [[], []], [[], []], [[], []], [[], []], [[], []], [[], []]], {}
            names = ['tfStygzhen1.EMS.SWT.GdCncbRealPw', 'tfStygzhen1.EMS.SWT.GdCncbReactPw',
                     'tfStygzhen1.EMS.SWT.PcsIncbRealPw', 'tfStygzhen1.EMS.SWT.PcsIncbReactPw']

            cursor = db_con[1][0]
            cursor2 = db_con[1][1]

            for name in names:
                ind = names.index(name)
                for tab in tables:
                    # sql = "select value,DATE_FORMAT(FLOOR(DATE_FORMAT(FROM_UNIXTIME(dts_s), '%Y%m%d%H%i%s')/{0})*{0},'%Y-%m-%d %H:%i:%s') as time from {1} where \
                    # dts_s>='{2}' and dts_s<='{3}' and name='{4}' group by time".format(time_,tab,n,e_start,name)
                    sql = "SELECT ROUND(avg(value),2),DATE_FORMAT(ots,'%Y-%m-%d %H:%i')  FROM {1} WHERE DATE_FORMAT(ots,'%i')%{0} = 0   AND dts_s>='{2}' and \
                                                dts_s<='{3}' and name ='{4}' GROUP BY DATE_FORMAT(ots,'%Y-%m-%d %H:%i')".format(
                        time_, tab, timeUtils.ssTtimes(n), timeUtils.ssTtimes(e_start), name)

                    values = cursor.execute(sql).fetchall()
                    for val in values:
                        if val[1]:
                            all.append(val[1][8:])
                            arr[ind][0].append(val[1][8:])
                            try:
                                arr[ind][1].append(val[0][0])
                            except:
                                arr[ind][1].append(val[0])

            all = list(set(all))
            all.sort()
            pw_line['times'] = all
            for i in range(len(names)):
                for ind in range(len(all)):
                    times = arr[i][0]
                    value = arr[i][1]
                    try:
                        y = times.index(all[ind])
                    except:
                        if ind == 0:
                            value.insert(0, 0)
                        else:
                            value.insert(ind, value[ind - 1])
                key = names[i].split('.')[-1]
                pw_line[key] = arr[i][1]
            wg = pw_line['GdCncbReactPw']  # 所有无功数据
            yg = pw_line['GdCncbRealPw']  # 所有有功数据
            a = []
            for i in range(len(wg)):
                v = num_retain(math.sqrt((wg[i] ** 2 + yg[i] ** 2)), 1)
                a.append(v)
            pw_line['GdCncbReactPw'] = a
            obj['PW'] = pw_line
            # 计算电压和电流——————————————————————————————————————————————————————
            timeall, dataV, dataA, data2 = [], [[], []], [[], []], {}  # 总时间;电压;电流；
            total = len(dianya[db])  # 总的单元个数
            for name in dianya[db]:  # 电压
                d1, d2 = [], []
                cur = cursor2 if 'tfStygzhen2' in name else cursor
                for tab in tables:
                    HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)

                sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                    time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)
                # cur.execute(sql)
                value = cur.execute(sql).fetchall()
                # value = cur.fetchone()
                if value:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    try:
                        d2.insert(0, value[0][0])
                    except:
                        d2.insert(0, value[0])
                dataV[0].append(d1)
                dataV[1].append(d2)

            for name in dianliu[db]:  # 电流
                d1, d2 = [], []
                cur = cursor2 if 'tfStygzhen2' in name else cursor
                for tab in tables:
                    HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)

                sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                    time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)
                value = cur.execute(sql).fetchall()
                if value:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    try:
                        d2.insert(0, value[0][0])
                    except:
                        d2.insert(0, value[0])
                dataA[0].append(d1)
                dataA[1].append(d2)
            timeall = list(set(timeall))
            timeall.sort()
            data2['time'] = timeall
            data2['total'] = total
            for i in range(total):
                for ind in range(len(timeall)):
                    time1 = dataV[0][i]
                    value1 = dataV[1][i]
                    time2 = dataA[0][i]
                    value2 = dataA[1][i]
                    # print 'valu1:,',len(value1),'--value2:',len(value2)
                    try:
                        y1 = time1.index(timeall[ind])
                        y2 = time2.index(timeall[ind])
                    except:
                        if ind == 0:
                            value1.insert(0, 0)
                            value2.insert(0, 0)
                        else:
                            value1.insert(ind, value1[ind - 1])
                            value2.insert(ind, value2[ind - 1])
            data2['dataV'] = list(dataV[1])
            data2['dataA'] = list(dataA[1])
            obj["data2"] = data2

        else:
            #  统计功率和电压电流
            e_start = timeUtils.nowSecs()
            n = e_start - 86400
            data_start_time = timeUtils.ssTtimes(n)[8:16]
            nows = timeUtils.getNewTimeStr()
            selectTable = 'r_measure%s' % (nows.replace('-', '')[:6])
            hourstable = 'r_measure%s' % nows.replace('-', '').replace(' ', '')[:10]
            tables = ['ods_r_measure1']
            timeall, dataV, dataA, dataP1, dataP2, data2 = [], [[], []], [[], []], [[], []], [[],
                                                                                              []], {}  # 总时间;电压;电流；有功；无功
            total = len(dianya[db])  # 总的单元个数

            cursor = db_con[1][0]
            cursor2 = db_con[1][1]
            cursor3 = db_con[1][2]
            cursor4 = db_con[1][3]

            cure = None
            if db == 'baodian':
                cursor5 = db_con[1][4]
                cure = {"tfStbodian1": cursor, "tfStbodian2": cursor2, "tfStbodian3": cursor3, "tfStbodian4": cursor4,
                        "tfStbodian5": cursor5, }
            elif db == 'zgtian':
                cure = {"tfStzgtian1": cursor, "tfStzgtian2": cursor2, "tfStzgtian3": cursor3, "tfStzgtian4": cursor4}

            for name in dianya[db]:  # 电压
                d1, d2 = [], []
                if cure:
                    cur = cure[name.split('.')[0]]
                    for tab in tables:
                        # sql = "select value,DATE_FORMAT(FLOOR(DATE_FORMAT(FROM_UNIXTIME(dts_s), '%Y%m%d%H%i%s')/{0})*{0},'%Y-%m-%d %H:%i:%s') as time from {1} where \
                        # dts_s>='{2}' and dts_s<='{3}' and name='{4}' group by time".format(time_,tab,n,e_start,name)
                        HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)

                    sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                        time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)

                    value = cur.execute(sql).fetchall()
                    if value:
                        timeall.insert(0, data_start_time)
                        d1.insert(0, data_start_time)
                        try:
                            d2.insert(0, value[0][0])
                        except:
                            d2.insert(0, value[0])
                else:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    d2.insert(0, 0)

                dataV[0].append(d1)
                dataV[1].append(d2)
            for name in dianliu[db]:  # 电流
                d1, d2 = [], []
                if cure:
                    cur = cure[name.split('.')[0]]
                    for tab in tables:
                        HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)

                    sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                        time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)
                    value = cur.execute(sql).fetchall()
                    if value:
                        timeall.insert(0, data_start_time)
                        d1.insert(0, data_start_time)
                        try:
                            d2.insert(0, value[0][0])
                        except:
                            d2.insert(0, value[0])
                else:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    d2.insert(0, 0)
                dataA[0].append(d1)
                dataA[1].append(d2)
            for name in wugong[db]:  # 无功
                d1, d2 = [], []
                if cure:
                    cur = cure[name.split('.')[0]]
                    for tab in tables:
                        HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)
    
                    sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                        time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)
                    value = cur.execute(sql).fetchall()
                    if value:
                        timeall.insert(0, data_start_time)
                        d1.insert(0, data_start_time)
                        try:
                            d2.insert(0, value[0][0])
                        except:
                            d2.insert(0, value[0])
                else:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    d2.insert(0, 0)
                dataP2[0].append(d1)
                dataP2[1].append(d2)
            for name in yougong[db]:  # 有功
                d1, d2 = [], []
                if cure:
                    cur = cure[name.split('.')[0]]
                    for tab in tables:
                        HisDataAllUseInterface_.his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall)

                    sql = "SELECT value  FROM {1} WHERE dts_s>='{2}' and dts_s<='{3}' and name ='{4}' order by dts_s desc limit 1".format(
                        time_, tables[0], timeUtils.ssTtimes(n - 86400), timeUtils.ssTtimes(n), name)
                    value = cur.execute(sql).fetchall()
                    if value:
                        timeall.insert(0, data_start_time)
                        d1.insert(0, data_start_time)
                        try:
                            d2.insert(0, value[0][0])
                        except:
                            d2.insert(0, value[0])
                else:
                    timeall.insert(0, data_start_time)
                    d1.insert(0, data_start_time)
                    d2.insert(0, 0)
                dataP1[0].append(d1)
                dataP1[1].append(d2)
            timeall = list(set(timeall))
            timeall.sort()
            data2['time'] = timeall
            data2['total'] = total
            for i in range(total):
                for ind in range(len(timeall)):
                    time1 = dataV[0][i]
                    value1 = dataV[1][i]
                    time2 = dataA[0][i]
                    value2 = dataA[1][i]
                    time3 = dataP1[0][i]
                    value3 = dataP1[1][i]
                    time4 = dataP2[0][i]
                    value4 = dataP2[1][i]

                    try:
                        y1 = time1.index(timeall[ind])
                    except:
                        value1.insert(0, 0) if ind == 0 else value1.insert(ind, value1[ind - 1])
                    try:
                        y2 = time2.index(timeall[ind])
                    except:
                        value2.insert(0, 0) if ind == 0 else value2.insert(ind, value2[ind - 1])
                    try:
                        y3 = time3.index(timeall[ind])
                    except:
                        value3.insert(0, 0) if ind == 0 else value3.insert(ind, value3[ind - 1])
                    try:
                        y4 = time4.index(timeall[ind])
                    except:
                        value4.insert(0, 0) if ind == 0 else value4.insert(ind, value4[ind - 1])
            data2['dataV'] = list(dataV[1])
            data2['dataA'] = list(dataA[1])
            data2['dataP1'] = list(dataP1[1])
            data2['dataP2'] = list(dataP2[1])
            obj["data2"] = data2

        return True, obj

    @staticmethod
    def his_shi(cur, d1, d2, e_start, n, name, tab, time_, timeall):
        try:
            sql = "SELECT value,DATE_FORMAT(ots,'%Y-%m-%d %H:%i')  FROM {1} WHERE DATE_FORMAT(ots,'%i')%{0} = 0   AND dts_s>='{2}' and \
                                dts_s<='{3}' and name ='{4}' GROUP BY  DATE_FORMAT(ots,'%Y-%m-%d %H:%i')".format(time_, tab,timeUtils.ssTtimes(n), timeUtils.ssTtimes(e_start), name)
            values = cur.execute(sql).fetchall()

        except:
            values = []
        if values != []:
            for val in values:
                if val[1]:
                    timeall.append(val[1][8:])
                    d1.append(val[1][8:])
                    d2.append(val[0])

def _getSumDataByNames(names, time_list, db_con, month):
    '''根据名称获取指定天数的数据集合'''
    datas = []
    for tim in time_list:
        startT = timeUtils.timeStrToTamp('%s 00:00:01' % tim)
        endT = timeUtils.timeStrToTamp('%s 23:59:59' % tim)
        v = 0
        for mon in month:
            table = 'r_measure%s' % (mon)
            HisTable = HisACDMS(table)
            v = v + _getDisgCapy(HisTable, names, startT, endT, db_con)
        datas.append(num_retain(v))

    return datas

def _getDisgCapy(Table, names, startT, endT, db_con):
    '''根据名称获取用电量
    名称，列表
    startT:开始时间，绝对秒
    endT:截止时间，绝对秒
    '''
    data = []
    try:
        for name in names:
            for db_c in db_con:
                values = db_c.query(Table.value).filter(Table.name == name, Table.dts_s.between(startT, endT)).order_by(
                    Table.dts_s.asc()).all()
                for value in values:
                    data.append(value[0])
    except Exception as E:
        logging.error(E)

    return list_sum_mm(data, 1000)

def _tableIsExist(db_conn, tablename):
    '''判断表是否存在'''
    f = False
    for i in range(len(db_conn[2])):
        schema = db_conn[2][i]
        conn = db_conn[0][i].raw_connection()
        cursor = conn.cursor()
        sql = "select table_name from information_schema.TABLES where table_schema='{}' and table_name= '{}' order by table_name limit 1".format(
            schema, tablename)

        cursor.execute(sql)
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        if result:
            f = True
            break
    return f

