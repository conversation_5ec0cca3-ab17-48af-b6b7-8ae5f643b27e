#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-27 10:26:17
#@FilePath     : \RHBESS_Service\Tools\DB\mysql_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:10:51

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
DEBUG = model_config.get('broker', "log_debug")
# his库连接 mysql


HIS_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
HIS_PORT = model_config.get('mysql', "HIS_PORT")
HIS_DATABASE = model_config.get('mysql', "HIS_DATABASE")
HIS_USERNAME = model_config.get('mysql', "HIS_USERNAME")
HIS_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HIS_USERNAME,
    HIS_PASSWORD,
    HIS_HOSTNAME,
    HIS_PORT,
    HIS_DATABASE
)
his_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=20, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_his_session = scoped_session(sessionmaker(his_engine))

his_Base = declarative_base(his_engine)
his_session = _his_session()

# 东睦电站
DB_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
DB_PORT = model_config.get('mysql', "DB_PORT")
DB_USERNAME = model_config.get('mysql', "DB_USERNAME")
DB_PASSWORD = model_config.get('mysql', "DB_PASSWORD")
HIS_DATABASE_ = 'tfstdongmu'
dongmu_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DB_USERNAME,
    DB_PASSWORD,
    DB_HOSTNAME,
    DB_PORT,
    HIS_DATABASE_
)
_dm_his_engine = create_engine(dongmu_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=20, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

dm_his_session = scoped_session(sessionmaker(_dm_his_engine))

dongmu_Base = declarative_base(_dm_his_engine)
dongmu_session = dm_his_session()




