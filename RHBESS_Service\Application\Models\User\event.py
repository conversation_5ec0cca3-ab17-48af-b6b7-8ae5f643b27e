#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-01-29 14:41:08


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.user import User
from Application.Models.User.event_alarm_type import EventAlarmType
from Tools.Utils.time_utils import timeUtils

class Event(user_Base):
    u'事件/告警配置表'
    __tablename__ = "t_event"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"名称")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    en_descr = Column(VARCHAR(256), nullable=False,comment=u"描述-英文")
    point = Column(VARCHAR(256), nullable=True,comment=u"故障点位")
    index = Column(Integer, nullable=False,comment=u"索引，自动生成")
    type = Column(CHAR(1), nullable=False,comment=u"1事件，2告警")
    type_name = Column(VARCHAR(20), nullable=False,comment=u"数据类型；status,measure,cumulant,device,discrete")
    class_id = Column(Integer, ForeignKey("c_even_alarm_type.id"),nullable=False,comment=u"类别，c_even_alarm_type中parent_id为空")
    type_id = Column(Integer, ForeignKey("c_even_alarm_type.id"),nullable=False,comment=u"类型，c_even_alarm_type中parent_id不为空")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    user_id = Column(Integer, ForeignKey("t_user.id"),nullable=True,comment=u"用户id，记录是谁配置的")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    opt1 = Column(VARCHAR(50), nullable=True,comment=u"关联点1")
    opt2 = Column(VARCHAR(50), nullable=True,comment=u"关联点2")
    opt3 = Column(VARCHAR(50), nullable=True,comment=u"关联点3")
    opt4 = Column(VARCHAR(50), nullable=True,comment=u"关联点4")
    opt5 = Column(VARCHAR(50), nullable=True,comment=u"关联点5")
    opt6 = Column(VARCHAR(50), nullable=True,comment=u"关联点6")
    opt7 = Column(VARCHAR(50), nullable=True,comment=u"关联点7")
    opt8 = Column(VARCHAR(50), nullable=True,comment=u"关联点8")
    opt9 = Column(VARCHAR(50), nullable=True,comment=u"关联点9")
    opt10 = Column(VARCHAR(50), nullable=True,comment=u"关联点10")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
   
    event_class = relationship('EventAlarmType',backref='event_class', foreign_keys=[class_id])
    event_type = relationship('EventAlarmType',backref='even_type', foreign_keys=[type_id])
    event_user = relationship("User", backref="event_user", foreign_keys=[user_id])

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        now = timeUtils.getNewTimeStr()
        user_session.merge(Event(id=1,name='用户登录',descr='登录状态',index=0,op_ts=now,is_use=1,type=1,type_name='status',class_id=2,type_id=6));
       
        user_session.commit()
        user_session.close()
        
    def __repr__(self):
        user_descr = self.event_user.name if self.event_user else ''
        en_user_descr = self.event_user.en_name if self.event_user else ''
        op1 = self.opt1.split('#');op2 = self.opt2.split('#');op3 = self.opt3.split('#');op4 = self.opt4.split('#');
        op5 = self.opt5.split('#');op6 = self.opt6.split('#');op7 = self.opt7.split('#');
        op8 = self.opt8.split('#');op9 = self.opt9.split('#');op10 = self.opt10.split('#');
        bean = "{'id':%s,'descr':'%s','name':'%s','index':'%s','type_':'%s','type_name':'%s','class_id':'%s','class_descr':'%s','type_id':'%s','type_descr':'%s','isUse':%s,'user_id':'%s',\
            'user_descr':'%s','op_ts':'%s','opt1':'%s','opt2':'%s','opt3':'%s','opt4':'%s','opt5':'%s','opt6':'%s','opt7':'%s','opt8':'%s','opt9':'%s','opt10':'%s','type_name1':'%s',\
            'type_name2':'%s','type_name3':'%s','type_name4':'%s','type_name5':'%s','type_name6':'%s','type_name7':'%s','type_name8':'%s','type_name9':'%s','type_name10':'%s','station':'%s', \
            'en_descr':'%s','en_type_descr':'%s','en_class_descr':'%s','en_user_descr':'%s'}" % (
            self.id,self.descr,self.name,self.index,self.type,self.type_name,self.class_id,self.event_class.descr,self.type_id,self.event_type.descr,self.is_use,self.user_id,user_descr,self.op_ts,
            op1[0],op2[0],op3[0],op4[0],op5[0],op6[0],op7[0],op8[0],op9[0],op10[0],op1[1],op2[1],op3[1],op4[1],op5[1],op6[1],op7[1],op8[1],op9[1],op10[1],self.station,self.en_descr,
            self.event_type.en_descr, self.event_class.en_descr,en_user_descr)
        return bean.replace("None",'')
    def deleteEvent(self,id):
        from Application.Models.User.event_r import EventR
        from Application.Models.User.alarm_r import AlarmR
        try:
            s1 = user_session.query(EventR).filter(EventR.event_id == id).all()  # 
            for s in s1:
                s.deleteEventR(s.id)  # 委托处理
            s2 = user_session.query(AlarmR).filter(AlarmR.event_id == id).all()  # 
            for s in s2:
                s.deleteAlarm(s.id)  # 委托处理
          
            user_session.query(Event).filter(Event.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False