package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 电站VO
 */
@Data
@ApiModel("电站VO")
public class StationVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("英文名称")
    private String name;

    @ApiModelProperty("中文名称")
    private String descr;

    @ApiModelProperty("是否注册: 1-是, 0-否")
    private Integer register;

    @ApiModelProperty("是否注册名称")
    private String registerName;

    @ApiModelProperty("索引")
    private Integer index;

    @ApiModelProperty("操作时间戳")
    private LocalDateTime opTs;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
