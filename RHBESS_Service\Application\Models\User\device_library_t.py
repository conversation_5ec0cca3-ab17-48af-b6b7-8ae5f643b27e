#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class DeviceLibrary(user_Base):
    u'备件库表'
    __tablename__ = "t_device_library"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    device_mo = Column(VARCHAR(256), nullable=False, comment=u"设备型号")
    device_ty = Column(VARCHAR(50), nullable=False,comment=u"设备类型")
    ra_power = Column(VARCHAR(50), nullable=True, comment=u"额定功率,单位kW")
    ra_volume = Column(VARCHAR(50), nullable=True, comment=u"额定容量,单位Ah")
    ra_energy = Column(VARCHAR(50), nullable=True, comment=u"额定能量,单位kWh")
    vender = Column(VARCHAR(50), nullable=True, comment=u"厂家")
    remarks = Column(VARCHAR(256), nullable=True, comment=u"基础信息备注")
    is_use = Column(CHAR(2), nullable=True, server_default='1', comment=u"是否使用1是0否")

    en_device_mo = Column(VARCHAR(256), nullable=False, comment=u"设备型号")
    en_device_ty = Column(VARCHAR(50), nullable=False, comment=u"设备类型")
    en_vender = Column(VARCHAR(50), nullable=True, comment=u"厂家")
    en_remarks = Column(VARCHAR(256), nullable=True, comment=u"基础信息备注")
    device_no_id = Column(Integer, nullable=False, comment=u"设备型号id")
    device_type_id = Column(Integer, nullable=False, comment=u"设备类型id")
    factory_id = Column(Integer, nullable=True, comment=u"厂家id")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        # bean = "{'id':%s,'device_mo':'%s','device_ty':'%s','ra_power':'%s','ra_volume':'%s','ra_energy':'%s','vender':'%s','remarks':'%s','is_use':%s,'en_device_mo':'%s','en_device_ty':'%s','en_vender':'%s','en_remarks':'%s','device_no_id':'%s','device_type_id':'%s','factory_id':'%s'}" % (
        #     self.id,self.device_mo,self.device_ty,self.ra_power,self.ra_volume,self.ra_energy,self.vender,self.remarks,self.is_use,self.en_device_mo,self.en_device_ty,self.en_vender,self.en_remarks,self.device_no_id,self.device_type_id,self.factory_id)
        bean = "{'id':%s,'ra_power':'%s','ra_volume':'%s','ra_energy':'%s','remarks':'%s','is_use':%s,'en_device_mo':'%s','en_device_ty':'%s','en_vender':'%s','en_remarks':'%s','device_no_id':'%s','device_type_id':'%s','factory_id':'%s'}" % (
            self.id, self.ra_power, self.ra_volume, self.ra_energy,
            self.remarks, self.is_use, self.en_device_mo, self.en_device_ty, self.en_vender, self.en_remarks,
            self.device_no_id, self.device_type_id, self.factory_id)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}
