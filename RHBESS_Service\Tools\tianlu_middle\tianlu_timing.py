#!/usr/bin/env python
# coding=utf-8
#@Information:天禄定时任务中间表程序
#<AUTHOR> WYJ
#@Date         : 2023-04-20 13:53:56
#@FilePath     : \RHBESS_Service\Tools\tianlu_middle\tianlu_timing.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-25 14:06:19

import time,datetime
import sys, os, getopt
import numpy as np
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from apscheduler.schedulers.blocking import BlockingScheduler
import logging
logging.basicConfig()
from doris_hisdata_bean import HisACDMS_DORIS
# 所有并网点名称
stations = ['NBLS001','NBLS002','LJQC001','GLTY201','QXYJ201','QNKX101','QNKX001','HZDC101','NBLS001','NBLS002','RHTBA001','RHTBA002','QNGS201','QNGS202','ZXFN301','JSGQ001','JSBD001',
'JSXQ001','JSJY001','JSJY002','JSJY101','JSJY102','QNKM301','NRTYN101','DCLR101','DCLR102']
def getNewTimeStr():
    '获取当前时间'
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) 

def getNewDayStartStr():
    '获取当前天的起始时间'
    return time.strftime("%Y-%m-%d 00:00:00", time.localtime())
def timeStrToTamp(data):
    '时间转时间戳'
    timeArray = time.strptime(data, "%Y-%m-%d %H:%M:%S")
    timeStamp = int(time.mktime(timeArray))
    return timeStamp
def todaySecs():
    '获取当天起始绝对秒'
    tstr = getNewDayStartStr()
    return timeStrToTamp(tstr)
def ssTtimes(ss):
    b=time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(int(ss)));
    return b;
def dateToDataList(start,end):
    # 计算时间段内的时间列表,包含首位
    datestart=datetime.datetime.strptime(start[0:10],'%Y-%m-%d')
    dateend=datetime.datetime.strptime(end[0:10],'%Y-%m-%d')
    data_list = list()
    while datestart<=dateend:
        data_list.append(datestart.strftime('%Y-%m-%d')) 
        datestart+=datetime.timedelta(days=1)
    return data_list




def get_mysql_conn():
    '''获取mysql库连接'''
    mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
        'root','rhyc123','192.168.1.25',3306,'test')
    mysql_engine = create_engine(mysql_url,echo=False,max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=18000)
    _mysql_session = sessionmaker(mysql_engine,autoflush=True)
    mysql_session = _mysql_session()
    return mysql_session,mysql_engine
def get_doris_conn():
    '''获取doris连接'''
    doris_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
        'root','rhyc123','192.168.1.25',9030,'rhyc')
    doris_engine = create_engine(doris_url,echo=False,max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=18000)
    _doris_session = sessionmaker(doris_engine,autoflush=True)
    doris_session = _doris_session()
    return doris_session

def frooze_disg_chag_data(station,cumulant_data,cursor):
    '''冻结关口表充放电量数据'''
    hour_arr= []
    save_time = str(cumulant_data[0][0])[:10]
    print ('station---%s day %s chag and disg start save'%(station,save_time))
    for d in cumulant_data:
        time_ = str(d[0])  # 获取时间
        hour_ = time_[11:13]  # 整点值
        hour_arr.append(int(hour_))
    hour_np = np.array(hour_arr)
    start_ind,end_ind = 0,-1
    data_chag,data_disg = np.zeros(24),np.zeros(24)
    
    for i in range(24):  # 24点值
        arr_n = hour_np[hour_np==i]  # 当前值
        end_ind += len(arr_n)
        start_data = eval(str(cumulant_data[start_ind][1]))['body']
        end_data = eval(str(cumulant_data[end_ind][1]))['body']
        
        # print (ind,'----',i,str(cumulant_data[ind][0]),str(cumulant_data[end_ind][0]))
        chag_0,disg_0,chag_1,disg_1 = 0,0,0,0
        for sd in start_data:  # 累积量
            if "BMS" in sd['device']:
                disg_0 += float(sd["PAE"] if "PAE" in sd.keys() else 0)
                chag_0 += float(sd["NAE"] if "NAE" in sd.keys() else 0)
        for ed in end_data:  # 累积量
            if "BMS" in ed['device']:
                disg_1 += float(ed["PAE"] if "PAE" in ed.keys() else 0)
                chag_1 += float(ed["NAE"] if "NAE" in ed.keys() else 0)
        # print ('-------------',i,'chag_0:',chag_0,'disg_0:',disg_0,'chag_1:',chag_1,'disg_1:',disg_1)
        if i == 0:
            start_ind += len(arr_n)-1
        else:
            start_ind += len(arr_n)
        chag_change = chag_1-chag_0
        disg_change = disg_1-disg_0
        if chag_change != 0:
            data_chag[i] = chag_change
        if disg_change != 0:
            data_disg[i]= disg_change
        
    # print ('data_chag:',data_chag,len(data_chag),np.sum(data_chag))
    # print ('data_disg:',data_disg,len(data_disg),np.sum(data_disg))
    sql = "replace into f_data_chag_disg values ( '%s','%s','%s','%s','%s','%s','%s')"%(station,save_time,getNewTimeStr(),np.sum(data_chag),np.sum(data_disg),data_chag.tolist(),data_disg.tolist())
    cursor.execute(sql)
    print ('station---%s day chag and disg  save success'%station)


def frooze_data_five_minute(startTime=None,endTime=None):
    '''冻结五分钟的原始数据'''
    if not startTime:  # 没传时间，默认冻结前一天数据
        endTime = getNewDayStartStr()
        startTime = ssTtimes(todaySecs()-86400)  # 
    time_list = dateToDataList(startTime,endTime)
    conn = get_mysql_conn()[1].raw_connection()
    cursor = conn.cursor()
    CumulantTable = HisACDMS_DORIS('device_notify_cumulant_record')  # 累积量
    MeasureTable = HisACDMS_DORIS('device_notify_measure_record')  # 测量量
    DiscreteTable = HisACDMS_DORIS('device_notify_discrete_record')  # 离散量
    for ti in time_list:
        stTime = ' %s 00:00:00'%ti
        edTime = ' %s 23:59:59'%ti
        for station in stations:
            cumulant_data = get_doris_conn().query(CumulantTable.time,CumulantTable.data_info).filter(CumulantTable.time.between(stTime,edTime),CumulantTable.station_name==station).order_by(CumulantTable.time.asc()).all()
            measure_data = get_doris_conn().query(MeasureTable.time,MeasureTable.data_info).filter(MeasureTable.time.between(stTime,edTime),MeasureTable.station_name==station).order_by(MeasureTable.time.asc()).all()
            discrete_data = get_doris_conn().query(DiscreteTable.time,DiscreteTable.data_info).filter(DiscreteTable.time.between(stTime,edTime),DiscreteTable.station_name==station).order_by(DiscreteTable.time.asc()).all()
            if cumulant_data:
                frooze_disg_chag_data(station,cumulant_data,cursor)
            print ('station--is %s start save'%(station))
        
            for i in range(len(cumulant_data)):  # 三个数据长度一致，不一致时mqtt通信数据上报有问题
                sql = "replace into f_data_storage values ('%s',"%(station)
                d_data = eval(str(discrete_data[i][1]))  # 离散量数据
                c_data = eval(str(cumulant_data[i][1]))  # 累计量数据
                m_data = eval(str(measure_data[i][1]))  # 测量量数据
                time_ = discrete_data[i][0]
                sql += "'%s','%s',"%(time_,getNewTimeStr())
                # 拼接累积量
                pae,nae,cucha,cudis,bclcap,bdlcap = [],[],[],[],[],[]
                c_body_data = c_data['body']
                for cbd in c_body_data:  # 累积量
                    cbd_keys = cbd.keys()
                    if "BMS" in cbd['device']:
                        pae.append(float(cbd["PAE"]) if "PAE" in cbd_keys else None)  # 兼容中间改点表问题，约是7.8-9号
                        nae.append(float(cbd["NAE"]) if "NAE" in cbd_keys else None)  # 兼容中间改点表问题，约是7.8-9号
                        cucha.append(float(cbd["CuCha"]) if "CuCha" in cbd_keys else None)
                        cudis.append(float(cbd["CuDis"]) if "CuDis" in cbd_keys else None)
                    if "PCS" in cbd['device']:
                        bclcap.append(float(cbd["BCLCap"]) if "BCLCap" in cbd_keys else None)
                        bdlcap.append(float(cbd["BDLcap"]) if "BDLcap" in cbd_keys else None)
                sql += "'%s','%s','%s','%s',"%(pae,nae,cucha,cudis)
                # 拼接测量量
                mt_max,mt_min,mu_max,mu_min,et,rwt,bat,ipv,opv,soc,p,q,igbtat,igbtbt,igbtct,ua,ub,uc,ia,ib,ic,pat,pcc = [],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]
                m_body_data = m_data['body']
                for mbd in m_body_data:  # 测量量
                    mbd_keys = mbd.keys()
                    if "BMS" in mbd['device']:
                        mt_max.append(float(mbd["MTMax"]) if "MTMax" in mbd_keys else None)
                        mt_min.append(float(mbd["MTMin"]) if "MTMin" in mbd_keys else None)
                        mu_max.append(float(mbd["MUMax"]) if "MUMax" in mbd_keys else None)
                        mu_min.append(float(mbd["MUMin"]) if "MUMin" in mbd_keys else None)
                        et.append(float(mbd["ET"]) if "ET" in mbd_keys else None)
                        rwt.append(float(mbd["RWT"]) if "RWT" in mbd_keys else None)
                        bat.append(float(mbd["BAT"]) if "BAT" in mbd_keys else None)
                        ipv.append(float(mbd["IPV"]) if "IPV" in mbd_keys else None)
                        opv.append(float(mbd["OPV"]) if "OPV" in mbd_keys else None)
                        soc.append(float(mbd["SOC"]) if "SOC" in mbd_keys else None)
            
                    if "PCS" in mbd['device']:
                        p.append(float(mbd["P"]) if "P" in mbd_keys else None)
                        q.append(float(mbd["Q"]) if "Q" in mbd.keys() else None)
                        igbtat.append(float(mbd["IGBTAT"]) if "IGBTAT" in mbd_keys else None)
                        igbtbt.append(float(mbd["TGBTBT"]) if "TGBTBT" in mbd_keys else None)
                        igbtct.append(float(mbd["TGBTCT"]) if "TGBTCT" in mbd_keys else None)
                        ua.append(float(mbd["PUa"]) if "PUa" in mbd_keys else None)
                        ub.append(float(mbd["PUb"]) if "PUb" in mbd_keys else None)
                        uc.append(float(mbd["PUc"]) if "PUc" in mbd_keys else None)
                        ia.append(float(mbd["Ia"]) if "Ia" in mbd_keys else None)
                        ib.append(float(mbd["Ib"]) if "Ib" in mbd_keys else None)
                        ic.append(float(mbd["Ic"]) if "Ic" in mbd_keys else None)
                        pat.append(float(mbd["PAT"]) if "PAT" in mbd_keys else None)
                    if "EMS" == mbd['device']:
                        pcc.append(float(mbd["PCC"]) if "PCC" in mbd_keys else None)
                # 拼接测量量
                ccse = []
                d_body_data = d_data['body']
                for dbd in d_body_data:  # 离散量
                    if "BMS" in dbd['device']:
                        ccse.append(int(dbd["CCse"]) if "CCse" in dbd.keys() else None)
                sql += "'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',1)"%(
                    mt_max,mt_min,mu_max,mu_min,et,rwt,bat,ipv,opv,soc,ccse,bclcap,bdlcap,p,q,igbtat,igbtbt,igbtct,ua,ub,uc,ia,ib,ic,pat,pcc)
                
                cursor.execute(sql)
                print ('station--is %s  time is %s save success'%(station,time_))
    
    
    conn.commit()       
    cursor.close()
    conn.close()
    get_doris_conn().close()
    


def RunClearFileAndData():
    scheduler = BlockingScheduler()
    # scheduler.add_job(frooze_data_five_minute, 'interval', seconds=5)  # 10秒获取一次数据
    # scheduler.add_job(fault, 'interval', seconds=60*15)  # 每 15分钟执行一次
    scheduler.add_job(frooze_data_five_minute, 'cron', hour=1,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    
    scheduler.start()
if __name__ == '__main__':
    try:
        opts, args = getopt.getopt(sys.argv[1:], "h", ["help", "frooze", "start=", "end="])
        cmd = None
        start = None
        end = None
        for opt, arg in opts:
            if opt == "--frooze":
                cmd = "frooze"
            elif opt == "--start":
                start = arg
            elif opt == "--end":
                end = arg
            else:
                print ('''%s 数据冻结工具
                选项
                    -h|--help 查看帮助
                    --frooze 冻结所有网关数据
                    --start 起始时刻（含）。yyyy-mm-dd
                    --end 结束时刻（含）。yyyy-mm-dd

                ''') % sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            RunClearFileAndData()
        elif cmd == "frooze":
            if not start:
                print ("请指定开始时刻")
                quit()
            if not end:
                print ("请指定结束时刻")
                quit()
            frooze_data_five_minute(start, end)
            print ('SUCCESS')

    except Exception as e:
        print (e)
        
# conn = get_doris_conn()[1].raw_connection()
# cursor = conn.cursor()
# sql = "select data_info from device_notify_cumulant_record"
# cursor.execute(sql)
# value = cursor.fetchone()
# # values = cursor.fetchall()
# print (value)
# cursor.close()
# conn.close()