import decimal


def charge_discharge_conversion(data_list):
    """
    冲放电量单位换算
    :return:
    """
    if '--' not in data_list:
        sum_data = sum(data_list)
        if sum_data < 1000000:
            return round(sum_data, 2), 'kWh'
        elif 1000000 <= sum_data < 100000000:
            return decimal.Decimal(sum_data / 1000).quantize(decimal.Decimal('0.00')), 'MWh'
        elif 100000000 <= sum_data:
            return decimal.Decimal(sum_data / 1000000).quantize(decimal.Decimal('0.00')), 'GWh'
    else:
        return '--', '--'


def charge_discharge_conversion_sum(sum_data):
    """
    冲放电量单位换算
    :return:
    """
    if sum_data < 1000000:
        return round(sum_data, 2), 'kWh'
    elif 1000000 <= sum_data < 100000000:
        return decimal.Decimal(sum_data / 1000).quantize(decimal.Decimal('0.00')), 'MWh'
    elif 100000000 <= sum_data:
        return decimal.Decimal(sum_data / 1000000).quantize(decimal.Decimal('0.00')), 'GWh'
    
def to_tuple(input_list):
    '''列表转字典'''
    if len(input_list) == 1:
        return "('%s')"%input_list[0]  # 明确创建单元素元组
    return tuple(input_list)
