#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:19:10
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_discrete_desc.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 14:21:16



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class DiscreteDescPT(scada_Base):
    ''' 离散量说明表 '''
    __tablename__ = "t_discrete_desc"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    no = Column(Integer, nullable=False,comment=u"")
    descr = Column(String(256), nullable=False,comment=u"名称")
    comment = Column(Integer, nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','no':%s,'descr':'%s','comment':'%s'}" % (
            self.id,self.no,self.descr,self.comment)