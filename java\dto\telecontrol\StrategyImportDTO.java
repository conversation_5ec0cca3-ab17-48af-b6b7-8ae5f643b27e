package com.robestec.analysis.dto.telecontrol;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 策略导入DTO
 */
@Data
@ApiModel("策略导入DTO")
public class StrategyImportDTO {

    @ApiModelProperty(value = "上传的文件", required = true)
    @NotNull(message = "文件不能为空")
    private MultipartFile files;

    @ApiModelProperty(value = "语言")
    private String lang;
}
