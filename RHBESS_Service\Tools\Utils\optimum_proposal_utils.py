
import numpy as np


def m_min(array):
    n = array.shape
    if len(n) > 1:
        min_a = []
        for row in array.T:
            min_a.append(min(row))
        min_a = [i for i in min_a]
        return np.array(min_a)
    else:
        return min(array)


def m_max(array):
    n = array.shape
    if len(n) > 1:
        min_a = []
        for row in array.T:
            min_a.append(max(row))
        min_a = [i for i in min_a]
        return np.array(min_a)
    else:
        return max(array)


def division(array1, array2):
    if array2.shape != array1.shape:
        if len(array2.shape) == 2:
            array1 = np.array([array1 for i in range(len(array2))])
        else:
            array2 = np.array([array2 for i in range(len(array1))])

    res = [a / b for a, b in zip(array1, array2)]
    return res


def times(array1, array2):
    if array2.shape != array1.shape:
        if len(array2.shape) == 2:
            array1 = np.array([array1 for i in range(len(array2))])
        else:
            array2 = np.array([array2 for i in range(len(array1))])

    res = [a * b for a, b in zip(array1, array2)]
    return np.array(res)



def zip_list(month, day, data, alist):
    for i in zip(month, day):
        alist[int(i[0]) - 1, int(i[1]) - 1] = data
    return alist


def find_equal(array, number):
    index = 0
    res_list = []
    if not isinstance(array,np.ndarray):
        array = np.array(array)
    if len(array.shape) > 1:
        for i in array.T:
            for y in i:
                if y == number:
                    res_list.append(index)
                index += 1
    else:
        for i in array.T:
            if i == number:
                res_list.append(index)
            index += 1
    return res_list

def find_greater(array, number):
    index = 0
    res_list = []
    if not isinstance(array,np.ndarray):
        array = np.array(array)
    if len(array.shape) > 1:
        for i in array.T:
            for y in i:
                if y > number:
                    res_list.append(index)
                index += 1
    else:
        for i in array.T:
            if i > number:
                res_list.append(index)
            index += 1
    return res_list


def find_less(array, number):
    index = 0
    res_list = []
    if not isinstance(array,np.ndarray):
        array = np.array(array)
    if len(array.shape) > 1:
        for i in array.T:
            for y in i:
                if y < number:
                    res_list.append(index)
                index += 1
    else:
        for i in array.T:
            if i < number:
                res_list.append(index)
            index += 1
    return res_list



def find_less_two(array, number):
    res1 = []
    res2 = []
    if not isinstance(array,np.ndarray):
        array = np.array(array)
    if len(array.shape) > 1:
        for i, row in enumerate(array.T):
            for index, y in enumerate(row):
                if y < number:
                    res1.append(index)
                    res2.append(i)
                index += 1
    else:
        for i, n in enumerate(array.T):
            if n < number:
                res1.append(i)
                res2.append(0)

    return res1, res2

def find_greater_two(array, number):
    res1 = []
    res2 = []
    if not isinstance(array,np.ndarray):
        array = np.array(array)
    if len(array.shape) > 1:
        for i, row in enumerate(array.T):
            for index, y in enumerate(row):
                if y > number:
                    res1.append(index)
                    res2.append(i)
                index += 1
    else:
        for i, n in enumerate(array.T):
            if n > number:
                res1.append(i)
                res2.append(0)

    return res1, res2



def power_out_month_func(power_out_month, Yy):
    index = 0
    res = []
    for row in power_out_month.T:
        for i in row:
            if Yy:
                if index == Yy[0]:
                    Yy.pop(0)

                    res.append(i)
            index += 1
    return res

def moneycheck0_func(moneycheck0, Yy):
    index = 0
    res = []
    for row in moneycheck0.T:
        for i in row:
            if Yy:
                if index == Yy[0]:
                    Yy.pop(0)

                    res.append(i)
            index += 1
    return np.array(res)