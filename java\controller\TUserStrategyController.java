package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyCreateDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyQueryDTO;
import com.robestec.analysis.dto.tuserstrategy.TUserStrategyUpdateDTO;
import com.robestec.analysis.service.TUserStrategyService;
import com.robestec.analysis.vo.TUserStrategyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户策略管理API
 */
@RestController
@RequestMapping("/user-strategy")
@RequiredArgsConstructor
@Api(tags = "用户策略管理API")
public class TUserStrategyController {

    private final TUserStrategyService tUserStrategyService;

    @GetMapping
    @ApiOperation("分页查询用户策略")
    public PageResult<TUserStrategyVO> queryTUserStrategy(@Validated TUserStrategyQueryDTO queryDTO) {
        return tUserStrategyService.queryTUserStrategy(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增用户策略")
    public Result<Long> createTUserStrategy(@Validated @RequestBody TUserStrategyCreateDTO createDTO) {
        return Result.succeed(tUserStrategyService.createTUserStrategy(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增用户策略")
    public Result createTUserStrategyList(@Validated @RequestBody List<TUserStrategyCreateDTO> createDTOList) {
        tUserStrategyService.createTUserStrategyList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改用户策略")
    public Result updateTUserStrategy(@PathVariable Long id, @Validated @RequestBody TUserStrategyUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tUserStrategyService.updateTUserStrategy(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除用户策略")
    public Result deleteTUserStrategy(@PathVariable Long id) {
        tUserStrategyService.deleteTUserStrategy(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取用户策略详情")
    public Result<TUserStrategyVO> getTUserStrategy(@PathVariable Long id) {
        return Result.succeed(tUserStrategyService.getTUserStrategy(id));
    }

    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询用户策略")
    public Result<List<TUserStrategyVO>> getTUserStrategyByUserId(@PathVariable Long userId) {
        return Result.succeed(tUserStrategyService.getTUserStrategyByUserId(userId));
    }

    @GetMapping("/name/{name}")
    @ApiOperation("根据策略名称查询用户策略")
    public Result<List<TUserStrategyVO>> getTUserStrategyByName(@PathVariable String name) {
        return Result.succeed(tUserStrategyService.getTUserStrategyByName(name));
    }

    @GetMapping("/count/user/{userId}")
    @ApiOperation("统计用户的策略数量")
    public Result<Long> countByUserId(@PathVariable Long userId) {
        return Result.succeed(tUserStrategyService.countByUserId(userId));
    }
}
