#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-26 15:43:39
#@FilePath     : \RHBESS_Service\Tools\DB\huaiyin_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:10:40

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 淮阴项目历史库连接 mysql


HUAIYIN_HOSTNAME = model_config.get('mysql', "HUAIYIN_HOSTNAME")
HUAIYIN_PORT = model_config.get('mysql', "HUAIYIN_PORT")
HUAIYIN_DATABASE = model_config.get('mysql', "HUAIYIN_DATABASE")
HUAIYIN_USERNAME = model_config.get('mysql', "HUAIYIN_USERNAME")
HUAIYIN_PASSWORD = model_config.get('mysql', "HUAIYIN_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HUAIYIN_USERNAME,
    HUAIYIN_PASSWORD,
    HUAIYIN_HOSTNAME,
    HUAIYIN_PORT,
    HUAIYIN_DATABASE
)
huaiyin_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=8, pool_pre_ping=True,pool_recycle=1800)


_huaiyin_session = scoped_session(sessionmaker(huaiyin_engine))

huaiyin_Base = declarative_base(huaiyin_engine)
huaiyin_session = _huaiyin_session()




