package com.robestec.dailyproduce.telecontrol.service.impl;

import com.robestec.dailyproduce.telecontrol.service.TelecontrolStrategyService;
import com.robestec.dailyproduce.telecontrol.vo.ProjectStationVO;
import com.robestec.dailyproduce.telecontrol.vo.StationInfoVO;
import com.robestec.dailyproduce.telecontrol.vo.ProjectUnitVO;
import com.robestec.dailyproduce.telecontrol.vo.ProjectMasterStationVO;
import com.robestec.dailyproduce.telecontrol.vo.Project;
import com.robestec.dailyproduce.telecontrol.vo.ProjectService;
import com.robestec.dailyproduce.telecontrol.vo.ProjectMasterStationService;
import com.robestec.dailyproduce.telecontrol.vo.DynamicRedisRepository;
import com.robestec.dailyproduce.telecontrol.vo.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;
import java.math.RoundingMode;

@Service
public class TelecontrolStrategyServiceImpl implements TelecontrolStrategyService {

    private static final Logger log = LoggerFactory.getLogger(TelecontrolStrategyServiceImpl.class);

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectMasterStationService projectMasterStationService;

    @Autowired
    private DynamicRedisRepository dynamicRedisRepository;

    @Override
    public List<ProjectStationVO> getProjectStations(Long userId) {
        log.info("获取项目并网点列表, userId={}", userId);
        
        if (userId == null) {
            return Collections.emptyList();
        }

        // 获取用户关联的项目列表
        List<Project> projects = projectService.findProjectsByUserId(userId);
        if (CollectionUtil.isEmpty(projects)) {
            return Collections.emptyList();
        }

        List<ProjectStationVO> result = new ArrayList<>();
        
        for (Project project : projects) {
            ProjectStationVO projectVO = new ProjectStationVO();
            projectVO.setId(project.getId());
            projectVO.setName(project.getName());
            
            // 获取项目下的主站列表
            List<ProjectMasterStationVO> masterStations = projectMasterStationService.getMasterStationInfoVOList(project.getId());
            if (CollectionUtil.isEmpty(masterStations)) {
                result.add(projectVO);
                continue;
            }

            List<StationInfoVO> stationList = new ArrayList<>();
            
            for (ProjectMasterStationVO masterStation : masterStations) {
                if (CollectionUtil.isEmpty(masterStation.getProjectStationList())) {
                    continue;
                }

                for (ProjectStationVO station : masterStation.getProjectStationList()) {
                    StationInfoVO stationVO = new StationInfoVO();
                    stationVO.setId(station.getId());
                    stationVO.setName(station.getName());
                    stationVO.setRatedPower(BigDecimal.ZERO);
                    stationVO.setRatedCapacity(BigDecimal.ZERO);
                    stationVO.setRealtimePower(BigDecimal.ZERO);

                    // 获取站点下的所有单元
                    List<ProjectUnitVO> units = station.getProjectUnitList();
                    if (CollectionUtil.isEmpty(units)) {
                        stationList.add(stationVO);
                        continue;
                    }

                    for (ProjectUnitVO unit : units) {
                        // 累加额定功率和容量
                        if (unit.getRatedPower() != null) {
                            stationVO.setRatedPower(stationVO.getRatedPower().add(unit.getRatedPower()));
                        }
                        if (unit.getRatedCapacity() != null) {
                            stationVO.setRatedCapacity(stationVO.getRatedCapacity().add(unit.getRatedCapacity()));
                        }

                        // 从Redis获取实时功率数据
                        String key = String.format("Business:TRANS_TIMING_%s_%s_%s", 
                            "MEASURE", station.getEnglishName(), unit.getPcs());
                        Object value = dynamicRedisRepository.get(3, key);
                        
                        if (value != null) {
                            Map<String, Object> measureData = JsonUtil.toMap(value);
                            if (measureData != null && measureData.containsKey("P")) {
                                Object pValue = measureData.get("P");
                                if (pValue != null && !pValue.toString().equals("--")) {
                                    try {
                                        BigDecimal power = new BigDecimal(pValue.toString());
                                        stationVO.setRealtimePower(stationVO.getRealtimePower().add(power));
                                    } catch (NumberFormatException e) {
                                        log.error("解析实时功率数据失败: {}", e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                    stationList.add(stationVO);
                }
            }
            
            projectVO.setStations(stationList);
            result.add(projectVO);
        }

        return result;
    }

    @Override
    public List<StationCapacityVO> getPowerPlanStations() {
        log.info("获取电站容量信息开始");
        try {
            // 1. 获取所有电站信息
            List<Station> stations = stationService.list();
            if (CollectionUtil.isEmpty(stations)) {
                log.info("未找到电站信息");
                return Collections.emptyList();
            }

            // 2. 获取电站关系信息
            List<StationR> stationRelations = stationRService.list();
            Map<String, StationR> relationMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(stationRelations)) {
                relationMap = stationRelations.stream()
                    .collect(Collectors.toMap(StationR::getStationName, Function.identity()));
            }

            // 3. 组装返回数据
            List<StationCapacityVO> result = new ArrayList<>();
            for (Station station : stations) {
                StationCapacityVO stationVO = new StationCapacityVO();
                stationVO.setId(station.getId());
                stationVO.setName(station.getName());
                stationVO.setRatedPower(BigDecimal.ZERO);
                stationVO.setRatedCapacity(BigDecimal.ZERO);
                stationVO.setRealtimePower(BigDecimal.ZERO);

                // 获取电站关系信息
                StationR relation = relationMap.get(station.getName());
                if (relation != null) {
                    // 设置额定功率
                    if (relation.getElectricPower() != null) {
                        stationVO.setRatedPower(relation.getElectricPower());
                    }

                    // 计算实时功率
                    BigDecimal realPower = BigDecimal.ZERO;
                    String activePowerName = relation.getActivePowerName();
                    if (StringUtils.hasText(activePowerName)) {
                        try {
                            // 解析JSON格式的activePowerName
                            List<String> powerNames = JsonUtil.toList(activePowerName, String.class);
                            for (String powerName : powerNames) {
                                // 从Redis获取实时功率数据
                                String key = String.format("Business:TRANS_TIMING_%s_%s_%s", 
                                    "MEASURE", station.getEnglishName(), powerName);
                                Object value = dynamicRedisRepository.get(3, key);
                                
                                if (value != null) {
                                    Map<String, Object> measureData = JsonUtil.toMap(value);
                                    if (measureData != null && measureData.containsKey("value")) {
                                        Object powerValue = measureData.get("value");
                                        if (powerValue != null) {
                                            try {
                                                BigDecimal power = new BigDecimal(powerValue.toString());
                                                realPower = realPower.add(power);
                                            } catch (NumberFormatException e) {
                                                log.error("解析实时功率数据失败: {}", e.getMessage());
                                            }
                                        }
                                    }
                                }
                            }
                            // 转换为MW单位
                            stationVO.setRealtimePower(realPower.divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP));
                        } catch (Exception e) {
                            log.error("解析activePowerName失败: {}", e.getMessage());
                        }
                    }
                }

                result.add(stationVO);
            }

            log.info("获取电站容量信息完成，共{}个电站", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取电站容量信息失败", e);
            throw new TelecontrolException.BusinessException("获取电站容量信息失败: " + e.getMessage());
        }
    }
} 