from Tools.DB.mysql_user import user_Base

from Tools.DB.mysql_user import Integer, Column
class ForecaseDicPolicyUser(user_Base):
    u'政策情报中心记录-可见用户关联表'
    __tablename__ = "t_side_forecase_dic_police_infos_user"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    intel_id = Column(Integer, nullable=False, comment=u"政策情报ID")
    group_id = Column(Integer, nullable=True, comment='小组ID')
    rank_id = Column(Integer, nullable=True, comment='职级ID')


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()