#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ
# @Date         : 2022-04-29 16:18:14
# @FilePath     : \RHBESS_Service\Application\EqAccount\HaLun\main_frame.py
# @Email        : <EMAIL>
# @LastEditTime : 2023-04-27 20:18:05

from Application.Models.User.event import Event
from Application.Models.User.event_r import EventR
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.bit import Bit
import numpy, math
import logging
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session, DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.PointTable.t_device import DevicePT
from Tools.Utils.num_utils import *
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.page_data import PageData
from Application.Cfg.dir_cfg import model_config
from Tools.DB.mysql_scada import mqtt_session
from Application.Models.PointTable.t_device import DevicePT
from Application.Models.SelfStationPoint.t_device import DevicePT
from Application.Models.SelfStationPoint.t_status import StatusPT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT
from Application.Cfg.dir_cfg import model_config

# 结构 {"page":{"area1":[区域一数据集],"area2":[区域2数据集]}}
datas_obj = {'his': {}, 'halun': {}, 'taicang': {}, 'binhai': {}, 'ygzhen': {}, 'baodian': {}, 'dongmu': {},'zgtian': {}}
pageData_first = {'his': 1, 'halun': 1, 'taicang': 10, 'binhai': 23, 'ygzhen': 40, 'baodian': 67, 'dongmu': 80,'zgtian': 106}  # 首页监视
pageData_pcs = {'his': 4, 'halun': 4, 'taicang': 13, 'binhai': 26, 'ygzhen': 50, 'baodian': 74, 'dongmu': 83,'zgtian': 124}  # pcs监视
pageData_sys = {'his': 2, 'halun': 2, 'taicang': 11, 'binhai': 24, 'ygzhen': 41, 'baodian': 68, 'dongmu': 81,'zgtian': 107}  # 系统监控对应页面id
heart_obj = {}  # 保留心跳上次的值
bit_obj = {}  # 所有bit位配置项

heartContinue = int(model_config.get('broker', "heart_continue"))


class MainFrameCenterIntetface(BaseHandler):
    '''
    首页中间 + 左侧上两部分
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            # if 1:
            db = self.get_argument('db', 'his')
            logging.info('db:%s' % db)
            if db == 'dongmu':
                F, data = _getDataMain_1(db)
            else:
                F, data = _getDataMain(db)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

        # user_session.rollback()
        #
        # user_session.close()


class SystemMonitorIntetface(BaseHandler):
    '''
    系统监控页
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            db = self.get_argument('db', 'his')
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            logging.info('db:%s,statusF:%s' % (db, statusF))
            if db == 'dongmu':
                F, data = _getDataByPageId_1(pageData_sys[db], statusF, db)
            else:
                F, data = _getDataByPageId(pageData_sys[db], statusF, db)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


class BatMonitorIntetface(BaseHandler):
    '''
    电池监控界面
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
        # if 1:
            page = self.get_argument('page', 5)  # 具体电池仓
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            db = self.get_argument('db', 'his')
            logging.info('page:%s,statusF:%s,db:%s' % (page, statusF, db))
            if not page:
                return self.customError("入参不完整")

            if not bit_obj:
                bits = user_session.query(Bit).all()
                for bit in bits:
                    bit_obj[bit.id] = bit.name
            if db == 'dongmu':
                id_1 = 0
                if 92 <= int(page) <= 98:
                    id_1 = int(page) - 81  # 东睦状态量id

                F, data = _getDataByPageId_2(page, statusF, db, id_1)
            else:
                F, data = _getDataByPageId(page, statusF, db)

            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            mqtt_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
            mqtt_session.close()
        # user_session.rollback()
        # user_session.close()


class PcsMonitorIntetface(BaseHandler):
    '''
    PCS监控界面
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            db = self.get_argument('db', 'his')
            statusF = int(self.get_argument('statusF', 0))  # 状态量取值，0返回告警，1返回具体类型的值
            if not bit_obj:
                bits = user_session.query(Bit).all()
                for bit in bits:
                    bit_obj[bit.id] = bit.name
            logging.info('db:%s,statusF:%s' % (db, statusF))
            if db == 'dongmu':
                F, data = _getDataByPageId_2(pageData_pcs[db], statusF, db)
            else:
                F, data = _getDataByPageId(pageData_pcs[db], statusF, db)
            if F:
                return self.returnTypeSuc(data)
            else:
                return self.customError(data)
        except Exception as E:
            user_session.rollback()
            mqtt_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
            mqtt_session.close()


class StatusBitIntetface(BaseHandler):
    '''
    状态量具体每位的值
    '''

    @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        try:
            id = self.get_argument('id', None)  # 具体电池仓
            db = self.get_argument('db', 'his')
            if DEBUG:
                logging.info('id:%s' % id)
            if not id:
                return self.customError("无效参数")

            if db == 'dongmu':
                # 状态量实时值
                v2 = real_data('status', 'dongmu', 'db')
                if v2:
                    e2 = v2['body']
                status_data = mqtt_session.query(StatusPT).filter(StatusPT.id == id).first()
                status_bits_data = mqtt_session.query(StatusBitsPT).filter(StatusBitsPT.status_id == id).group_by(
                    StatusBitsPT.bits).all()
                data = []

                for i in e2:
                    if status_data.descr[:4] == i['device']:
                        f = -1
                        # bin_ST = bin(int(i[status_data.name]))[::-1].zfill(len(status_bits_data)+2) # 状态量里状态字的ST值
                        bin_ST = bin(int(i[status_data.name]))[2:].zfill(16)[::-1]  # 状态量里状态字的ST值
                        # bit_c = '{:016b}'.format(int(i[status_data.name]))  # 转成2进制，高位补零
                        # bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                        # bit_list_c.reverse()
                        for b in status_bits_data:
                            f += 1
                            ob = {'name': b.name}
                            ob['descr'] = b.descr
                            ob['value'] = int(bin_ST[f]) + 1
                            ob['bit'] = 'bit%s' % (b.bits)
                            if bin_ST[f] == '0':
                                ob['valueDescr'] = b.desc_off
                            if bin_ST[f] == '1':
                                ob['valueDescr'] = b.desc_on
                            data.append(ob)

            else:
                data = []
                pageData = user_session.query(PageData).filter(PageData.id == id).first()
                if pageData:
                    if pageData.bit_pageData:
                        bit_names = pageData.bit_pageData.name.split('#')
                        name = pageData.name.split('#')[0].split('.')[:-1]
                        p_name = '.'.join(name)
                        for n in bit_names:
                            name = '%s.%s' % (p_name, n)
                            ob = {'name': name}
                            sbean = real_data('status', name, pageData.mode)
                            ob['descr'] = sbean['desc']
                            ob['value'] = sbean['value']
                            ob['valueDescr'] = sbean['valueDesc']
                            ob['bit'] = 'bit%s' % (bit_names.index(n))
                            data.append(ob)
                    elif pageData.method == 'selfbit':
                        names = pageData.name.split("#")
                        for name in names:
                            ob = {'name': name}
                            sbean = real_data('status', name, pageData.mode)
                            ob['descr'] = sbean['desc']
                            ob['value'] = sbean['value']
                            ob['valueDescr'] = sbean['valueDesc']
                            ob['bit'] = 'bit%s' % (names.index(name))
                            data.append(ob)
            return self.returnTypeSuc(data)
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


def _getDataMain(db):
    '''
    处理首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.name.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]

    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0], 'bams_gz': [1, '电池堆故障', 0, '无故障'], 'bams_bj': [1, '电池堆报警', 0, '无报警'],
                   'pcs_gz': [1, 'pcs故障', 0, '无故障'], 'pcs_bj': [1, 'pcs报警', 0, '无报警']}
            page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                if type_name == 'measure' and method == 'heart':  # 测量量心跳
                    m = real_data('measure', data_name.name, data_name.mode)
                    v = float(m['value'])
                    now = timeUtils.nowSecs()  # 当前秒
                    if data_name.name in heart_obj.keys():
                        h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                        if now - h_list[1] >= heartContinue:
                            obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                            heart_obj[data_name.name][0] = v
                            heart_obj[data_name.name][1] = now
                        else:
                            obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                    else:
                        obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                        heart_obj[data_name.name] = [v, now]

                elif type_name == 'measure' and not method:  # 测量量具体值
                    m = real_data('measure', data_name.name, data_name.mode)
                    obj[return_key] = [num_retain(m['value'], 1) + unit, data_name.descr, 0, '']

                elif type_name == 'measure' and method == '+':  # 测量量相加
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    m = m1['value'] + m2['value'] / 1000
                    obj[return_key] = [num_retain(m, 1) + unit, data_name.descr, 0, '']
                elif type_name == 'measure' and method == 'pow':  # 测量量求开3次方
                    obj[return_key] = [num_retain(_getMeasurePowByNames(data_name.name, data_name.mode), 1) + unit,
                                       data_name.descr, 0, '']

                elif type_name == 'measure' and method == '/':  # 测量量计算效率
                    names = data_name.name.split('#')
                    m1 = real_data('measure', names[0], data_name.mode)
                    m2 = real_data('measure', names[1], data_name.mode)
                    if m2['value'] != 0:
                        effi = m1['value'] / m2['value'] if m1['value'] / m2['value'] < 1 else 1
                        obj[return_key] = [num_retain(effi, 2), data_name.descr, 0, '']
                    else:
                        obj[return_key] = ['0.00', data_name.descr, 0, '']
                elif type_name == 'discrete' and not method:  # 离散量的单个值
                    d = real_data('discrete', data_name.name, data_name.mode)
                    obj[return_key] = [d['value'], data_name.descr, 0, d['valueDesc']]

                elif type_name == 'status' and not method:  # 状态量实时值
                    s = real_data('status', data_name.name, data_name.mode)
                    obj[return_key] = [s['value'], data_name.descr, 0, s['valueDesc']]

                if 'tfStbodian' in data_name.name:
                    if return_key == 'pcs_bj' or return_key == 'pcs_gz':  # 保电
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 2:  # 只判断故障
                            obj['pcs_gz'] = [2, 'pcs故障', 0, d['valueDesc']]
                            obj['pcs_bj'] = [2, 'pcs报警', 0, d['valueDesc']]
                        else:
                            obj['pcs_gz'] = [1, 'pcs故障', 0, '无故障']
                            obj['pcs_bj'] = [1, 'pcs报警', 0, '无报警']
                    elif return_key == 'bams_bj' or return_key == 'bams_gz':  # 保电
                        d = real_data('discrete', data_name.name, data_name.mode)
                        if d['value'] == 3:  # 报警
                            obj['bams_gz'] = [1, 'bams故障', 0, d['valueDesc']]
                            obj['bams_bj'] = [2, 'bams报警', 0, d['valueDesc']]
                        elif d['value'] == 4:  # 故障
                            obj['bams_gz'] = [2, 'bams故障', 0, d['valueDesc']]
                            obj['bams_bj'] = [1, 'bams报警', 0, d['valueDesc']]
                        else:
                            obj['bams_gz'] = [1, 'bams故障', 0, '无故障']
                            obj['bams_bj'] = [1, 'bams报警', 0, '无报警']

                if data_name.name.endswith('Sys_SOC'):  # 以soc结尾计算告警和故障

                    for n in bams_gz.split('#'):  # 电池故障
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, '故障']
                            break
                    for n in bams_gz2.split('#'):  # 电池故障2
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_gz'] = [2, '电池堆故障', 0, '故障']
                            break
                    for n in bams_bj.split('#'):  # 电池报警
                        name = data_name.name.replace('Sys_SOC', n)
                        s = real_data('status', name, data_name.mode)
                        if s['value'] == 2:
                            obj['bams_bj'] = [2, '电池堆报警', 0, '报警']
                            break

                    name = data_name.name.replace('Sys_SOC', 'InvtOallStat')  # PCS故障 告警
                    d = real_data('discrete', name, data_name.mode)
                    val = d['value']
                    if val == 2:
                        obj['pcs_gz'] = [2, 'pcs故障', 0, d['valueDesc']]
                        # continue
                    elif val == 1:
                        obj['pcs_bj'] = [2, 'pcs报警', 0, d['valueDesc']]
                        # continue

            data.append(obj)

    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    page_data_all = user_session.query(PageData).filter(PageData.page_id == pageData_first[db], PageData.is_use == 1,
                                                        PageData.return_key == 'onlineall').all()

    page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db], PageData.is_use == 1,
                                                           PageData.return_key == 'online').all()
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()
    # print 'page_data_all:',page_data_all,len(page_data_all),'\n'
    # print 'page_data_status',page_data_status,len(page_data_status),'\n'
    # print 'bj_page_data_status',bj_page_data_status,len(bj_page_data_status)
    to = len(page_data_status)
    # arar = user_session.query(Bit.name).filter(Bit.id==3).first()
    left1['total'] = to
    left1['page_area'] = bj_page_data_status[0].page_area
    i, y = 0, 0
    for stat in page_data_status:
        ind = page_data_status.index(stat)
        if real_data('status', page_data_all[ind].name, page_data_all[ind].mode)['value'] == 2:
            sname = stat.name
            ss = real_data('status', sname, stat.mode)
            val = ss['value']
            if val == 2:  # 在线
                y = y + 1;

            if '#' in bj_page_data_status[ind].name:  # 永臻
                na = bj_page_data_status[ind].name.split('#')[0].replace('Sw4OnleMd1', 'Sw2Alarm')
                s = real_data('status', na, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            elif 'baodian' == db:  # 广州保电
                name = bj_page_data_status[ind].name.replace('_Pcs.Online', "Pcs.SysRunSt")
                s = real_data('discrete', name, stat.mode)
                val = s['value']
                if val == 2:
                    i = i + 1;
            else:
                for n in bams_bj.split('#'):
                    name = bj_page_data_status[ind].name.replace('SysMdOffline', n)
                    s = real_data('status', name, stat.mode)
                    val = s['value']
                    if val == 2:
                        i = i + 1;
                        break
    left1['online'] = y
    left1['outline'] = to - y
    left1['bj'] = i
    left1['lv'] = num_retain(float(y) / to * 100)
    data.append(left1)
    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db,
        AlarmR.status == None).group_by(Event.type_id).order_by(Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, AlarmR.station == db,
                                               AlarmR.status == None).order_by(AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " 报警"
        else:
            point = point + " 已恢复"
        d.append({'ts': alarm['ts'], 'alarm_descr': alarm['alarm_descr'], 'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data


def _getDataMain_1(db):
    '''
    处理东睦首页数据
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    #  中间部分数据
    page_area = user_session.query(PageData.page_area).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.method != 'line').group_by(
        PageData.page_area).order_by(PageData.page_area.asc()).all()

    if not bit_obj:
        bits = user_session.query(Bit).all()
        for bit in bits:
            bit_obj[bit.id] = bit.name

    bams_gz = bit_obj[1]
    bams_gz2 = bit_obj[2]
    bams_bj = bit_obj[3]

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']

    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']

    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']
    nnn = 0
    for area in page_area:
        if 'center' in area[0]:
            obj = {'page_area': area[0], 'bms_gz': [1, '电池堆故障', 0, '无故障'], 'bms_bj': [1, '电池堆报警', 0, '无报警'],
                   'pcs_gz': [1, 'pcs故障', 0, '无故障'], 'pcs_bj': [1, 'pcs报警', 0, '无报警']}
            page_data = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                            PageData.page_area == area[0], PageData.is_use == 1).all()

            for data_name in page_data:
                unit = data_name.unit if data_name.unit else ''
                return_key = data_name.return_key
                type_name = data_name.type_name
                method = data_name.method

                for i in e1:

                    if type_name == 'measure' and method == '/':  # 测量量计算效率,视在功率
                        names = data_name.name.split('#')
                        m1 = names[0]
                        m2 = names[1]
                        if data_name.device == i['device']:
                            if float(i[m2]) != 0 and m1 == 'dcapd':
                                effi = float(i[m1]) / float(i[m2]) if float(i[m1]) / float(i[m2]) < 1 else 1
                                obj[return_key] = [num_retain(effi, 3), data_name.descr, 0, '']
                            elif float(i[m2]) != 0:
                                effi = float(i[m1]) / float(i[m2])
                                obj[return_key] = [num_retain(effi, 3), data_name.descr, 0, '']
                            else:
                                obj[return_key] = ['0.00', data_name.descr, 0, '']
                    elif type_name == 'measure' and not method:  # 测量量具体值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']
                for i in e2:
                    if type_name == 'status' and not method:  # 状态量实时值
                        # if i['device'] == 'ReCon':
                        #     obj[return_key] = [2, data_name.descr, 0, '']
                        if data_name.device == i['device']:
                            if data_name.device[:3] == 'PCS':
                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()

                                bin_aaa = bit_list_n[13]
                                if bin_aaa == '0':
                                    obj['PCSM'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                                elif bin_aaa == '1':
                                    now_time = timeUtils.getNewTimeStr()
                                    now_time_ = timeUtils.timeStrToTamp(now_time)
                                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                    if time_ss > 125:
                                        obj['PCSM'] = [1, '交流断路器位置', 0, '']
                                    if time_ss <= 125:
                                        obj['PCSM'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                                bit_n = '{:016b}'.format(int(i['ST4']))  # 转成2进制，高位补零
                                bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                bit_list.reverse()

                                bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                bit_list_n.reverse()
                                # print i['ST1'],data_name.device

                                if bit_list[6] == '1':  # pcs
                                    obj['Chargstate'] = [int(bit_list[6]) + 1, '充电状态', 0, '合']  # pcs状态

                                elif bit_list[6] == '0':  # pcs
                                    obj['Chargstate'] = [int(bit_list[6]) + 1, '充电状态', 0, '分']  # pcs状态

                                if bit_list[7] == '1':  # pcs
                                    obj['dischstate'] = [int(bit_list[7]) + 1, '放电状态', 0, '合']  # pcs状态

                                elif bit_list[7] == '0':  # pcs
                                    obj['dischstate'] = [int(bit_list[7]) + 1, '放电状态', 0, '分']  # pcs状态

                                if bit_list_n[14] == '1':  # pcs告警.故障（取同一个值）
                                    obj['pcs_gz'] = [int(bit_list_n[14]) + 1, 'pcs报警', 0, '报警']  # pcs状态
                                    obj['pcs_bj'] = [int(bit_list_n[14]) + 1, 'pcs故障', 0, '故障']  # pcs状态
                                elif bit_list_n[14] == '0':  # pcs告警.故障（取同一个值）
                                    obj['pcs_gz'] = [int(bit_list_n[14]) + 1, 'pcs报警', 0, '无报警']  # pcs状态
                                    obj['pcs_bj'] = [int(bit_list_n[14]) + 1, 'pcs故障', 0, '无故障']  # pcs状态
                            if data_name.device[:3] == 'BMS':
                                bit_c = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                                bit_list_c.reverse()
                                # bin_ccc = bin(int(i['ST1']))[2:].zfill(16)[::-1]
                                if bit_list_c[4] == '1':  # bms告警.故障
                                    obj['bms_gz'] = [int(bit_list_c[4]) + 1, 'pcs报警', 0, '报警']

                                elif bit_list_c[4] == '0':  # bms告警.故障
                                    obj['bms_gz'] = [int(bit_list_c[4]) + 1, 'pcs报警', 0, '无报警']

                                if bit_list_c[3] == '1':  # bms告警.故障

                                    obj['bms_bj'] = [int(bit_list_c[3]) + 1, 'pcs故障', 0, '故障']
                                elif bit_list_c[3] == '0':  # bms告警.故障

                                    obj['bms_bj'] = [int(bit_list_c[3]) + 1, 'pcs故障', 0, '无故障']
                for i in e3:
                    if type_name == 'cumulant' and method == '/':  # 累积量计算效率
                        names = data_name.name.split('#')
                        m1 = names[0]
                        m2 = names[1]
                        if i['device'] == data_name.device:
                            if float(i[m2]) != 0 and m1 == 'BDcap':
                                effi = float(i[m1]) / float(i[m2]) if float(i[m1]) / float(i[m2]) < 1 else 1
                                obj['efficiency'] = [num_retain(effi, 3), data_name.descr, 0, '']
                            else:
                                obj['efficiency'] = ['0.00', data_name.descr, 0, '']

            data.append(obj)

    #  左侧第一部分,数据库只配置在线状态名称即可
    left1 = {}
    bj_page_data_status = user_session.query(PageData).filter(PageData.page_id == pageData_first[db],
                                                              PageData.is_use == 1, PageData.page_area == 'left1').all()

    left1['page_area'] = bj_page_data_status[0].page_area
    s, y, z = 0, 0, 0  # 告警，在线，不在线
    for h in bj_page_data_status:
        if h.type_name == 'status' and not h.method:  # 状态量实时值
            for i in e2:
                if h.device != 'ReCon' and h.device == i['device']:
                    bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                    bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                    bit_list.reverse()
                    # bin_aaa = bin(int(i['ST1']))[2:].zfill(16)[::-1]
                    bin_aaa = bit_list[13]
                    bin_bbb = bit_list[14]

                    if bin_aaa == '0':
                        # obj['accibrepos'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                        z += 1
                    elif bin_aaa == '1':
                        now_time = timeUtils.getNewTimeStr()
                        now_time_ = timeUtils.timeStrToTamp(now_time)
                        time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                        if time_ss > 125:
                            # obj['accibrepos'] = [1, '交流断路器位置', 0, '']
                            z += 1
                        if time_ss <= 125:
                            # obj['accibrepos'] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']
                            y += 1
                    if bin_bbb == '1':
                        s = s + 1
            # if h.device == 'ReCon':
            #     for i in e2:
            #         if h.device == i['device']:
            #                 y = y + 1
            # else:
            #     for i in e2:
            #         if h.device == i['device']:
            #             # if bin(int(i['ST1']))[::-1].zfill(18)[14] == '1':# 告警
            #             aaa = bin(int(i['ST1']))[2:].zfill(16)[::-1][14]
            #             if aaa == '1':# 告警
            #                 s = s + 1

    left1['total'] = y + z
    left1['online'] = y
    left1['outline'] = z
    left1['bj'] = s
    left1['lv'] = num_retain(float(y) / float(y + z) * 100)
    data.append(left1)

    #  左侧第二部分
    left2, d = {'page_area': 'left2', 'one': 0, 'two': 0, 'thr': 0}, []
    # 一级告警，故障
    t_alarm = user_session.query(Event.type_id.label('type_id'), func.count(1).label('count')).filter(
        Event.type_id.in_([3, 4, 5]), AlarmR.event_id == Event.id, Event.station == db,
                                      AlarmR.status == None).group_by(Event.type_id).order_by(Event.type_id.asc()).all()

    for al in t_alarm:
        if al.type_id == 3:
            left2['one'] = al.count
        if al.type_id == 4:
            left2['two'] = al.count
        if al.type_id == 5:
            left2['thr'] = al.count
    alarms = user_session.query(AlarmR).filter(AlarmR.event_id == Event.id, Event.type_id == 5, AlarmR.station == db,
                                               AlarmR.status == None).order_by(AlarmR.ts.desc()).limit(20).offset(
        0).all()
    for alarm in alarms:
        alarm = eval(str(alarm))
        if alarm['point'] != 'None' and alarm['point'] != '':
            point = alarm['point']
        else:
            point = alarm['descr']

        if int(alarm['value']) == 2:
            point = point + " 报警"
        else:
            point = point + " 已恢复"
        d.append({'ts': alarm['ts'], 'alarm_descr': alarm['alarm_descr'], 'point': point})
    left2['data'] = d
    data.append(left2)
    return True, data


def _getDataByPageId(id, statusF, db):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]

    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area
    # print 'page_area_obj:',page_area_obj
    # page_area = user_session.query(PageData.page_area).filter(PageData.page_id==id,PageData.is_use==1).group_by(PageData.page_area).all()
    # for area in page_area:
    #     obj = {'page_area':area[0]}
    #     page_data = user_session.query(PageData).filter(PageData.page_id==id,PageData.page_area==area[0],PageData.is_use==1).all()

    for area in page_area_obj.keys():
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            if type_name == 'measure' and method == '+':  # 测量量的和
                v = _getMeasureSumByNames(data_name.name, data_name.mode)
                if data_name.station == 'ygzhen':
                    v = str(round(float(v) / 1000, 3))
                obj[return_key] = [v + unit, data_name.descr, 0, '']
            elif type_name == 'measure' and method == 'avg':  # 测量量的平均值
                obj[return_key] = [_getMeasureAvgByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0, '']
            elif type_name == 'measure' and method == 'sqrt':  # 两数平方和开平方
                obj[return_key] = [_getMeasureSqrtByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                   '']
            elif type_name == 'measure' and method == '-':  # 测量量的差值
                obj[return_key] = [_getMeasureRangeByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0,
                                   '']
            elif type_name == 'measure' and method == '/':  # 测量量两个数商
                obj[return_key] = [_getMeasureSByNames(data_name.name, data_name.mode) + unit, data_name.descr, 0, '']
            elif type_name == 'status' and not method:  # 状态量的单个值
                s = real_data('status', data_name.name, data_name.mode)
                obj[return_key] = [s['value'], data_name.descr, 0, s['valueDesc']]

            elif type_name == 'status' and method == 'or' and statusF == 0:  # 组合状态量直接返回告警状态
                obj[return_key] = [_getStatusOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
            elif type_name == 'status' and method == 'or' and statusF == 4:  # 组合状态量直接值
                obj[return_key] = [_getStatusValueOrByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
            elif type_name == 'status' and method == 'or' and statusF == 1:  # 组合状态量返回所有当前值
                obj[return_key] = [_getStatusByNames(data_name.name, data_name.mode), data_name.descr, 0, '']
            elif type_name == 'status' and statusF == 3:  # 组合状态量返回二进制数
                bit_val = 0
                names = data_name.name.split('#')  # 数据名称集合
                if len(names) != 16:
                    return False, 'status %s 名称个数不匹配' % data_name.descr
                if method == 'selfbit':  # 自身就是按0-15位排列
                    for nam in names:
                        ind = names.index(nam)
                        s = real_data('status', nam, data_name.mode)
                        val = s['value']
                        if val > 1:
                            bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数

                else:
                    if method != 'bit' or not data_name.bit_id:
                        return False, 'status %s 配置错误' % data_name.descr
                    # bit_names = data_name.bit_pageData.name.split('#')  # 获取当前配置的二进制name集合
                    bit_names = bit_obj[data_name.bit_id].split('#')

                    for nam in names:
                        n = nam.split('.')[-1]  # 取点分割后的最后一个名称
                        if n not in bit_names:
                            return False, 'status %s 名称配置不匹配' % data_name.descr
                        ind = bit_names.index(n)  # 获取名称在二进制中的索引
                        # val = 0
                        s = real_data('status', nam, data_name.mode)
                        val = s['value']
                        if val > 1:
                            bit_val = bit_val + 2 ** ind  # 有状态直接求改位置的十进制数

                obj[return_key] = [bit_val, data_name.descr, data_name.id, '']
            elif type_name == 'measure' and method == 'heart':  # 测量量心跳
                m = real_data('measure', data_name.name, data_name.mode)
                v = float(m['value'])
                now = timeUtils.nowSecs()  # 当前秒
                if data_name.name in heart_obj.keys():
                    h_list = heart_obj[data_name.name]  # 心跳值，包含值和值的时间
                    if now - h_list[1] > heartContinue:
                        obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                        heart_obj[data_name.name][0] = v
                        heart_obj[data_name.name][1] = now
                    else:
                        obj[return_key] = [1 if h_list[0] > 0 else 2, data_name.descr, 0, '']
                else:
                    obj[return_key] = [1 if v > 0 else 2, data_name.descr, 0, '']
                    heart_obj[data_name.name] = [v, now]

            elif type_name == 'measure' and not method:  # 测量量具体值
                m = real_data('measure', data_name.name, data_name.mode)
                obj[return_key] = [num_retain(m['value'], 3) + unit, data_name.descr, 0, '']
                # m = measure.getByName(data_name.name)
                # if m:
                #     obj[return_key] = [num_retain(m.rt()['value'],3)+unit,data_name.descr,0]
                # else:
                #     logging.error('measure descr %s is not in DB'%(data_name.descr))
                #     return False,'measure %s is not in DB'%data_name.descr
            elif type_name == 'discrete' and not method:  # 离散量的单个值
                d = real_data('discrete', data_name.name, data_name.mode)
                obj[return_key] = [d['value'], data_name.descr, 0, d['valueDesc']]

        data.append(obj)

    return True, data


def _getDataByPageId_1(id, statusF, db):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]

    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']
    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']
    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']

    for area in page_area_obj.keys():
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            for i in e1:
                if type_name == 'measure' and not method:  # 测量的值
                    if data_name.device == i['device']:
                        if i[data_name.name]:
                            obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']

            for i in e2:
                if type_name == 'status' and statusF == 0:  # 组合状态量返回二进制数
                    if data_name.device == i['device']:
                        if i['device'] == 'ReCon':
                            obj[return_key] = [int(i[data_name.name]) + 1, data_name.descr, 0, '']
                        else:
                            status_data_PCS = mqtt_session.query(StatusPT.id).filter(
                                StatusPT.descr.like(i['device'] + '%'), StatusPT.name == 'ST1').first()
                            status_bits_data = mqtt_session.query(StatusBitsPT).filter(
                                StatusBitsPT.status_id == status_data_PCS[0]).group_by(StatusBitsPT.bits).all()
                            # bin_s = bin(int(i['ST1']))[2:].zfill(16)[::-1]  # 状态量二进制

                            bit_n = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                            bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                            bit_list.reverse()

                            for b in status_bits_data:
                                if b.name == 'runnsta':
                                    if int(bit_list[6]) == 0:
                                        obj['runnsta'] = [int(bit_list[6]) + 1, '运行状态', 0, b.desc_off]
                                    elif int(bit_list[6]) == 1:
                                        obj['runnsta'] = [int(bit_list[6]) + 1, '运行状态', 0, b.desc_on]
                                if b.name == 'Shutdsta':
                                    if int(bit_list[7]) == 0:
                                        obj['Shutdsta'] = [int(bit_list[7]) + 1, '停机状态', 0, b.desc_off]
                                    if int(bit_list[7]) == 1:
                                        obj['Shutdsta'] = [int(bit_list[7]) + 1, '停机状态', 0, b.desc_on]
                                if b.name == 'standsta':
                                    if int(bit_list[8]) == 0:
                                        obj['standsta'] = [int(bit_list[8]) + 1, '待机状态', 0, b.desc_off]
                                    if int(bit_list[8]) == 1:
                                        obj['standsta'] = [int(bit_list[8]) + 1, '待机状态', 0, b.desc_on]

                                if b.name == 'readystate':
                                    if int(bit_list[9]) == 0:
                                        obj['readystate'] = [int(bit_list[9]) + 1, '就绪状态', 0, b.desc_off]
                                    if int(bit_list[9]) == 1:
                                        obj['readystate'] = [int(bit_list[9]) + 1, '就绪状态', 0, b.desc_on]
                                if b.name == 'emeshutd':
                                    if int(bit_list[10]) == 0:
                                        obj['emeshutd'] = [int(bit_list[10]) + 1, '紧急停机', 0, b.desc_off]
                                    if int(bit_list[10]) == 1:
                                        obj['emeshutd'] = [int(bit_list[10]) + 1, '紧急停机', 0, b.desc_on]

                                if b.name == 'dccibrepos':
                                    if int(bit_list[11]) == 0:
                                        obj['dccibrepos'] = [int(bit_list[11]) + 1, '直流断路器位置', 0, b.desc_off]
                                    if int(bit_list[11]) == 1:
                                        obj['dccibrepos'] = [int(bit_list[11]) + 1, '直流断路器位置', 0, b.desc_on]

                                if b.name == 'accibrepos':
                                    now_time = timeUtils.getNewTimeStr()
                                    now_time_ = timeUtils.timeStrToTamp(now_time)
                                    time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                    if int(bit_list[13]) == 0:
                                        obj['accibrepos'] = [int(bit_list[13]) + 1, '交流断路器位置', 0, '']
                                    elif int(bit_list[13]) == 1:
                                        if time_ss > 125:
                                            obj['accibrepos'] = [1, '交流断路器位置', 0, '']
                                        if time_ss <= 125:
                                            obj['accibrepos'] = [int(bit_list[13]) + 1, '交流断路器位置', 0, '']

                                if b.name == 'TotalAlarm':
                                    if int(bit_list[14]) == 0:
                                        obj['TotalAlarm'] = [int(bit_list[14]) + 1, '告警总', 0, b.desc_off]
                                    elif int(bit_list[14]) == 1:
                                        obj['TotalAlarm'] = [int(bit_list[14]) + 1, '告警总', 0, b.desc_on]

            for i in e3:
                if type_name == 'cumulant' and not method:  # 累积量的值
                    if data_name.device == i['device']:
                        if i[data_name.name]:
                            obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']

        data.append(obj)

    return True, data


def _getDataByPageId_2(id, statusF, db, id_1=None):
    '''
    根据页面id获取数据
    id:页面id
    statusF:状态量取值，0返回告警，1返回具体类型的值
    '''
    data = []
    # 判断页面是否再自定义的内存中
    # global datas_obj
    if id in datas_obj[db].keys():
        page_area_obj = datas_obj[db][id]

    else:
        #  当前页的所有区域分类
        page_areas = user_session.query(PageData.page_area).filter(PageData.page_id == id,
                                                                   PageData.station == db).group_by(
            PageData.page_area).all()
        p_area = {}  # 定义区域集
        for area in page_areas:
            page_data = user_session.query(PageData).filter(PageData.page_id == id, PageData.page_area == area[0],
                                                            PageData.is_use == 1, PageData.station == db).all()
            p_area[area[0]] = page_data
        datas_obj[db][id] = p_area
        page_area_obj = p_area

    # 测量量实时值
    v1 = real_data('measure', 'dongmu', 'db')
    if v1:
        e1 = v1['body']
    # 状态量实时值
    v2 = real_data('status', 'dongmu', 'db')
    if v2:
        time_real = v2['utime']
        e2 = v2['body']
    # 累积量实时值
    v3 = real_data('cumulant', 'dongmu', 'db')
    if v3:
        e3 = v3['body']

    for area in page_area_obj.keys():
        if area[:3] == 'PCS':
            id_1 = int(area[-1])+3
        # if area == 'PCS1':
        #     id_1 = 4
        # if area == 'PCS2':
        #     id_1 = 5
        # if area == 'PCS3':
        #     id_1 = 6
        # if area == 'PCS4':
        #     id_1 = 7
        # if area == 'PCS5':
        #     id_1 = 8
        # if area == 'PCS6':
        #     id_1 = 9
        # if area == 'PCS7':
        #     id_1 = 10
        if id != '91':
            status_data = mqtt_session.query(StatusPT).filter(StatusPT.device_id == id_1).all()
        obj = {'page_area': area}
        for data_name in page_area_obj[area]:
            unit = data_name.unit if data_name.unit else ''
            return_key = data_name.return_key
            type_name = data_name.type_name
            method = data_name.method
            for i in e1:
                if type_name == 'measure' and not method:  # 测量的值
                    if data_name.device == i['device']:
                        if i[data_name.name]:
                            obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']

            if id != '91':
                for i in e3:
                    if type_name == 'cumulant' and not method:  # 累积量的值
                        if data_name.device == i['device']:
                            if i[data_name.name]:
                                obj[return_key] = [num_retain(i[data_name.name], 3) + unit, data_name.descr, 0, '']

            for i in e2:
                if id != '91':
                    for c in status_data:
                        if c.descr[:4] == i['device']:
                            # return_key = c.descr[:4] + '_' + c.name
                            obj[c.name] = [i[c.name], c.descr, c.id, '']
                    if id != '83':
                        if data_name.device == i['device']:
                            if int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 0:
                                obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常', 0, '异常']
                            elif int(bin(int(i['ST1']))[::-1].zfill(18)[10]) == 1:
                                obj['inposupnor'] = [int(bin(int(i['ST1']))[::-1].zfill(18)[10]) + 1, '开入电源正常', 0, '正常']


                else:
                    if data_name.device[:3] == 'PCS':
                        if data_name.device[:4] == i['device']:

                            bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                            bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                            bit_list_n.reverse()

                            bin_aaa = bit_list_n[13]
                            if bin_aaa == '0':
                                obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                            elif bin_aaa == '1':
                                now_time = timeUtils.getNewTimeStr()
                                now_time_ = timeUtils.timeStrToTamp(now_time)
                                time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
                                if time_ss > 125:
                                    obj[return_key] = [1, '交流断路器位置', 0, '']
                                if time_ss <= 125:
                                    obj[return_key] = [int(bin_aaa) + 1, '交流断路器位置', 0, '']

                    elif data_name.device[:3] == 'BMS':
                        if data_name.device[:4] == i['device']:
                            bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                            bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                            bit_list_n.reverse()

                            bin_aaa = bit_list_n[10]
                            obj[return_key] = [int(bin_aaa) + 1, '开入电源正常', 0, '']

        data.append(obj)

    return True, data


def _getMeasureSumByNames(names, mode):
    ''' 返回测量量的和 '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        arr.append(mea['value'])
    return num_retain(sum(arr), 3)


def _getMeasureSByNames(names, mode):
    ''' 返回两个测量量的商 '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        arr.append(mea['value'])

    if arr[1] == 0:
        return '0.000'
    return num_retain(arr[0] / arr[1] * 100, 3)


def _getMeasureAvgByNames(names, mode, ro=2):
    '''
    返回测量量的平均值
    name:名称集合
    ro：保留小数位，默认2

    '''
    arr = []
    name = names.split('#')
    for n in name:
        mea = real_data('measure', n, mode)
        arr.append(mea['value'])

    return num_retain(numpy.mean(arr), 3)


def _getMeasureSqrtByNames(names, mode, ro=2):
    name = names.split('#')
    al = 0
    for n in name:
        mea = real_data('measure', n, mode)
        al = al + mea['value'] ** 2

    return num_retain(math.sqrt(al), 3)


def _getMeasurePowByNames(names, mode, ro=2):
    '''
    求一个数的开3次方
    '''
    name = names.split('#')
    al = 0
    for n in name:
        mea = real_data('measure', n, mode)
        al = al + mea['value'] ** 2
    return num_retain(al ** 0.5, 3)


def _getMeasureRangeByNames(names, mode, ro=2):
    '''
    返回测量量的差值
    name:名称集合
    ro：保留小数位，默认2

    '''
    name = names.split('#')
    v1 = real_data('measure', name[0], mode)['value']
    v2 = real_data('measure', name[1], mode)['value']

    return num_retain(v1 - v2, 3)


def _getStatusOrByNames(names, mode):
    '''
    返回状态量是否告警
    name:名称集合
    mode:从哪取值
    '''
    name = names.split('#')
    for n in name:
        sta = real_data('status', n, mode)
        val = int(sta['value'])
        if val == 2:
            return '告警'

    return '正常'


def _getStatusValueOrByNames(names, mode):
    name = names.split('#')
    for n in name:
        sta = real_data('status', n, mode)
        val = int(sta['value'])

    return val


def _getStatusByNames(names, mode):
    '''
    返回状态量具体值
    '''
    object = {}
    name = names.split("#")
    for n in name:
        sta = real_data('status', n, mode)
        object[n] = sta['value']

    return object


def _replace(old_string, char, index):
    '''
    替换指定位置内容
    old_string：旧字符
    char：替换值
    index：替换位置
    '''
    new_string = '%s%s%s' % (old_string[:index], char, old_string[index + 1:])
    return new_string
