#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-30 09:31:44
#@FilePath     : \RHBESS_Service\Tools\DB\taicang_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:11:47


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 太仓项目历史库连接 mysql


TAICANG_HOSTNAME = model_config.get('mysql', "TAICANG_HOSTNAME")
TAICANG_PORT = model_config.get('mysql', "TAICANG_PORT")
TAICANG_DATABASE = model_config.get('mysql', "TAICANG_DATABASE")
TAICANG_USERNAME = model_config.get('mysql', "TAICANG_USERNAME")
TAICANG_PASSWORD = model_config.get('mysql', "TAICANG_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TAICANG_USERNAME,
    TAICANG_PASSWORD,
    TAICANG_HOSTNAME,
    TAICANG_PORT,
    TAICANG_DATABASE
)
taicang_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_taicang_session = scoped_session(sessionmaker(taicang_engine,autoflush=True))

taicang_Base = declarative_base(taicang_engine)
taicang_session = _taicang_session()


STAICANG_HOSTNAME = model_config.get('mysql', "STAICANG_HOSTNAME")
STAICANG_PORT = model_config.get('mysql', "STAICANG_PORT")
STAICANG_DATABASE = model_config.get('mysql', "STAICANG_DATABASE")
STAICANG_USERNAME = model_config.get('mysql', "STAICANG_USERNAME")
STAICANG_PASSWORD = model_config.get('mysql', "STAICANG_PASSWORD")

shisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    STAICANG_USERNAME,
    STAICANG_PASSWORD,
    STAICANG_HOSTNAME,
    STAICANG_PORT,
    STAICANG_DATABASE
)
staicang_engine = create_engine(shisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_staicang_session = scoped_session(sessionmaker(staicang_engine,autoflush=True))

staicang_Base = declarative_base(staicang_engine)
staicang_session = _staicang_session()




