import datetime
import json
import re
import traceback
import uuid

from django.db.models import Q
from django_redis import get_redis_connection
from rest_framework import serializers

from apis.statistics_apis.models import UserStrategy, Month, StrategyApplyHistory, StrategyApplyHistoryCategoryNew,\
    StrategyApplyHistoryMonth, UserStrategyCategoryNew, UserStrategyHours
from apis.user.models import UserDetails, StationPlanHistory, StationDetails, FormerActicNew, StationActicNew, PeakValleyNew, MaterStation, MessageCenter
from apis.web.models import RunningReport, PowerDeliverRecords, PlanDeliverRecords
from common import common_response_code
from common.constant import EMPTY_STR_LIST

from LocaleTool.common import redis_pool



charge_dict = {
    0: '静置',
    1: '放电',
    -1: '充电'
}

en_charge_dict = {
    0: 'standing',
    1: 'electric discharge',
    -1: 'charging'
}


class SelectRunningReportsSerializer(serializers.ModelSerializer):
    """运行报告序列化器"""""

    # report_type = serializers.IntegerField(required=True)
    # project_id = serializers.IntegerField(required=False, write_only=True)
    # start_time = serializers.CharField(required=False, write_only=True)
    # end_time = serializers.CharField(required=False, write_only=True)

    class Meta:
        model = RunningReport
        fields = '__all__'


class MonitorSendMobileMessageSerializer(serializers.Serializer):
    """短信发送"""

    mobile = serializers.CharField(required=True)

    def validate_mobile(self, value):
        lang = self.context.get('lang', 'zh')

        user_id = self.context.get('user_id')
        # print(34, user_id)
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确！" if lang == 'zh' else "Mobile number format is incorrect!")

        # print(user_ins.mobile)
        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号获取验证码！" if lang == 'zh' else
                                             "Please use the current account mobile to get the verification code!")
        return value


class PowerIssudSerializer(serializers.Serializer):
    """功率计划下发"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.IntegerField(required=True)
    name = serializers.CharField(required=True, label='任务名称')
    power_list = serializers.JSONField(required=True, label='功率计划列表')
    station_list = serializers.JSONField(required=True, label='并网点列表')

    # id = serializers.IntegerField(required=False, label='任务ID')

    def validate_mobile(self, value):
        lang = self.context.get('lang', 'zh')
        user_id = self.context.get('user_id')
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确！" if lang == 'zh' else "Mobile number format is incorrect!")

        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号验证！" if lang == 'zh' else "Please use the current account mobile to verify!")
        exists = UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else "Mobile number does not exist.")

        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get('lang', 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "reset" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", str(value)):
            raise Exception("验证码格式不正确！" if lang == 'zh' else "Verification code format is incorrect!")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "The verification code has expired or does not exist.")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise Exception("验证码错误" if lang == 'zh' else "The verification code is wrong.")
        conn.delete("reset" + str(mobile))  # 删除 redis 中的短信验证码
        return value

    def validate_power_list(self, value):
        lang = self.context.get('lang', 'zh')
        power_dict = {}  # 排序用
        for power in value:

            option = power.get('option')
            limit = power.get('limit')
            if not option and option != 0:
                raise Exception("充放电选项不能为空！" if lang == 'zh' else
                                                 "Charge and discharge options cannot be empty!")
            if limit is None:
                raise Exception("限值不能为空！" if lang == 'zh' else "Limit value cannot be empty!")
            if option not in [-1, 0, 1]:
                raise Exception("充放电选项参数错误：取值范围：-1、0、1" if lang == 'zh' else "Charge and discharge option parameter error: Range: -1, 0, 1.")
            if limit < 0 or limit > 1:
                raise Exception("限值参数错误：取值范围：0~1" if lang == 'zh' else "Limit value parameter error: Range: 0~1.")

            start_time = datetime.datetime.strptime(power.get('start_time'), "%Y-%m-%d %H:%M:%S")
            now_time = datetime.datetime.now()
            now_difference = (start_time - now_time).total_seconds()
            if now_difference < 0:
                raise Exception("时间范围错误：开始时间不能小于当前时间！" if lang == 'zh' else
                                                 "Time range error: The start time cannot be less than the current time!")

            end_time = datetime.datetime.strptime(power.get('end_time'), "%Y-%m-%d %H:%M:%S")
            difference = (end_time - start_time).total_seconds()
            if difference <= 0:
                raise Exception("时间范围错误：结束时间不能小于等于开始时间！" if lang == 'zh' else "Time range error: The end time cannot be less than or equal to the start time!")
            if power_dict.get(start_time):
                raise Exception('任务时间重合，新增失败！' if lang == 'zh' else 'Task time overlap, addition failed!')
            else:
                power_dict[start_time] = power
        new_power_list = []  # 排序后的数据集
        power_dict = sorted(power_dict.items(), key=lambda x: x[0])  # 根据key排序
        s_time_list = []
        e_time_list = []
        # 根据开始时间排序
        for i, v in enumerate(power_dict):
            power = v[1]
            s_time = datetime.datetime.strptime(power.get('start_time'), '%Y-%m-%d %H:%M:%S')
            e_time = datetime.datetime.strptime(power.get('end_time'), '%Y-%m-%d %H:%M:%S')
            s_time_list.append(s_time)
            e_time_list.append(e_time)
            power['serial_number'] = i + 1
            new_power_list.append(power)
        # 判断时间是否重合和断档
        for i in range(len(s_time_list) - 1):
            differ_seconds = (s_time_list[i + 1] - e_time_list[i]).total_seconds()
            if differ_seconds < 0:
                raise Exception('任务时间重合，新增失败！' if lang == 'zh' else 'Task time overlap, addition failed!')
            # if differ_seconds > 2:
            #     raise Exception('任务时间断档，新增失败！')

        return new_power_list

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        power_list = validated_data.get('power_list')
        station_list = validated_data.get('station_list')
        validated_data['user_id'] = self.context.get('user_id')
        validated_data['power_list'] = json.dumps(power_list)
        validated_data['station_list'] = json.dumps(station_list)
        del validated_data['code']
        obj = PowerDeliverRecords.objects.create(**validated_data, en_name=validated_data.get('name', ''))
        for power in power_list:
            serial_number = power.get('serial_number')
            for station in station_list:
                plan = StationPlanHistory()
                # plan.power = power.get('power')
                plan.power = round(float(power.get('option')) * power.get('limit') * float(station.get('rated_power')), 2)
                plan.start_time = power.get('start_time')
                plan.end_time = power.get('end_time')
                plan.power_follow = power.get('power_follow')
                plan.user_id = self.context.get('user_id')
                plan.station_id = station.get('station_id')
                plan.unique = uuid.uuid4()
                plan.save()
                plan_id = plan.id
                PlanDeliverRecords.objects.create(plan_id=plan_id, power_id=obj.id, serial_number=serial_number)

        # 异步翻译
        pdr_data = {'id': obj.id,
                    'table': 't_power_deliver_records',
                    'update_data': {'name': validated_data.get('name')}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return obj

    def update(self, instance, validated_data):
        lang = self.context.get('lang', 'zh')
        id = validated_data.get('id')
        power_list = validated_data.get('power_list')
        station_list = validated_data.get('station_list')
        validated_data['user_id'] = self.context.get('user_id')
        validated_data['power_list'] = json.dumps(power_list)
        validated_data['station_list'] = json.dumps(station_list)
        del validated_data['code']
        # res = instance.update(**validated_data)
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.en_name = validated_data.get('name', instance.en_name)
        instance.save()
        records = PlanDeliverRecords.objects.filter(power_id=id)
        plan_ids = [i.plan_id for i in records]
        records.delete()
        StationPlanHistory.objects.filter(id__in=plan_ids).delete()
        for power in power_list:
            serial_number = power.get('serial_number')
            for station in station_list:
                plan = StationPlanHistory()
                # plan.power = power.get('power')
                plan.power = round(float(power.get('option')) * power.get('limit') * float(station.get('rated_power')),2)
                plan.start_time = power.get('start_time')
                plan.end_time = power.get('end_time')
                plan.power_follow = power.get('power_follow')
                plan.user_id = self.context.get('user_id')
                plan.station_id = station.get('station_id')
                plan.unique = uuid.uuid4()
                plan.save()
                plan_id = plan.id
                PlanDeliverRecords.objects.create(plan_id=plan_id, power_id=id, serial_number=serial_number)

        # 异步翻译
        pdr_data = {'id': instance.id,
                    'table': 't_power_deliver_records',
                    'update_data': {'name': validated_data.get('name')}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        return instance


class PowerIssudDeleteSerializer(serializers.Serializer):
    """功率计划下发删除"""

    # 手机号校验
    mobile = serializers.CharField(required=True)
    # 验证码校验
    code = serializers.IntegerField(required=True)
    id = serializers.IntegerField(required=True, label='任务ID')

    def validate_mobile(self, value):

        lang = self.context.get('lang', 'zh')
        user_id = self.context.get('user_id')
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确！" if lang == 'zh' else "Mobile number format is incorrect!")

        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号验证！" if lang == 'zh' else "Please use the current account mobile number to verify!")
        exists = UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise Exception("手机号不存在" if lang == 'zh' else "Mobile number does not exist.")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get('lang', 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "reset" + str(mobile)
        conn = get_redis_connection("default")

        if not re.match(r"^\d{6}$", str(value)):
            raise Exception("验证码格式不正确！" if lang == 'zh' else "Verification code format is incorrect!")

        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Verification code expired or does not exist.")
        if str(cache_mobile_code.decode('utf-8')) != str(value):
            raise Exception("验证码错误" if lang == 'zh' else "Verification code error.")
        conn.delete("reset" + str(mobile))  # 删除 redis 中的短信验证码
        return value


class CategorySerializer(serializers.Serializer):
    """策略类别"""

    name = serializers.CharField(required=True, max_length=32, label='策略分类名称')
    charge_config = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='24小时充放电量配置')
    rl_list = serializers.DictField(required=True, label='阈值')
    months = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='月份')
    explain_json = serializers.DictField(required=True, label='说明')
    pv_dict = serializers.DictField(required=True, label='峰谷标识')
    is_follow = serializers.BooleanField(required=False, default=1, label='负荷跟随，默认1开启')
    remark = serializers.CharField(required=False, label='备注')


class CategoryDataSerializerNew(serializers.Serializer):
    """时段数据"""

    start_time = serializers.CharField(required=True, max_length=32, label='开始时间')
    end_time = serializers.CharField(required=True, max_length=32, label='结束时间')
    rl = serializers.IntegerField(required=True, label='阈值')
    charge_config = serializers.IntegerField(required=True, label='24小时充放电量配置')
    pv = serializers.IntegerField(required=True, label='峰谷标识')
    explain = serializers.CharField(required=False, allow_null=True, allow_blank=True, label='备注')

class CategorySerializerNew(serializers.Serializer):
    """策略类别"""

    name = serializers.CharField(required=True, max_length=32, label='策略分类名称')
    months = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='月份')
    is_follow = serializers.BooleanField(required=False, default=1, label='负荷跟随，默认1开启')
    data = CategoryDataSerializerNew(many=True, label="时段信息")

# class UserStrategyCategorySerializer(serializers.Serializer):
#     """新建用户策略"""
#
#     name = serializers.CharField(required=True, max_length=32, label='策略名称')
#     category_list = CategorySerializer(many=True, label="分类信息")
#
#     def validate(self, attrs):
#
#         strategy = UserStrategy.objects.filter(name=attrs['name'], user_id=self.context.get('user_id'), is_delete=0).first()
#         if strategy:
#             raise Exception("策略名称重复！")
#         month_count = 0
#         for category in attrs['category_list']:
#             for month in category.get('months'):
#                 month_count += 1
#
#         if month_count < 12:
#             raise Exception("月份校验失败；不满12月！")
#         elif month_count > 12:
#             raise Exception("月份校验失败；超过12月！")
#         return attrs
#
#     def create(self, validated_data):
#         user = self.context.get('user_id')
#         strategy = UserStrategy.objects.create(name=validated_data['name'], user_id=user)
#         for category in validated_data['category_list']:
#             category_obj = UserStrategyCategory.objects.create(strategy=strategy, name=category.get('name'),
#                                                                charge_config=str(category.get('charge_config')),
#                                                                is_follow=category.get('is_follow'),
#                                                                rl_list=str(category.get('rl_list')),
#                                                                explain_json=str(category.get('explain_json')), **category.get('pv_dict'),
#                                                                remark=category.get('remark'))
#             # 保存月份
#             for month in category.get('months'):
#                 Month.objects.create(strategy=strategy, month_number=month, is_valid=False, user_Strategy_Category=category_obj)
#
#
#         return strategy


class UserStrategyCategorySerializerNew(serializers.Serializer):
    """新建用户策略--15分钟"""

    name = serializers.CharField(required=True, max_length=32, label='策略名称')
    category_list = CategorySerializerNew(many=True, label="分类信息")

    def validate(self, attrs):
        lang = self.context.get('lang', 'zh')
        strategy = UserStrategy.objects.filter(name=attrs['name'], user_id=self.context.get('user_id'),
                                               is_delete=0).first()
        if strategy:
            raise Exception("策略名称重复！" if lang == 'zh' else "Policy name repeat!")
        month_count = 0
        for category in attrs['category_list']:
            for month in category.get('months'):
                month_count += 1
            day_seconds = 0
            for data in category.get('data'):
                if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                    day_seconds = 86340
                    break
                start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                day_seconds += (end_time - start_time).total_seconds()
            if day_seconds != 86340:
                raise Exception("时间校验失败；时段不足一天！" if lang == 'zh' else
                                                 "Time check failed; Less than one day!")
        if month_count < 12:
            raise Exception("月份校验失败；不满12月！" if lang == 'zh' else
                                             "Month check failed; Less than 12 months!")
        elif month_count > 12:
            raise Exception("月份校验失败；超过12月！" if lang == 'zh' else
                                             "Month check failed; More than 12 months!")
        return attrs

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        strategy = UserStrategy.objects.create(name=validated_data['name'], en_name=validated_data['name'], user_id=user)

        # 异步翻译
        pdr_data = {'id': strategy.id,
                    'table': 't_user_strategy',
                    'update_data': {'name': validated_data['name']}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        hours_obj = UserStrategyHours.objects.create(strategy=strategy, data=json.dumps(validated_data['category_list']))
        for category in validated_data['category_list']:
            charge_config = []
            rl_list = []
            explain_json = []
            pv_list = []
            for data in category['data']:
                if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                    count = 96  # 15分钟一次
                else:
                    start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                    end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                    count = int((end_time-start_time).total_seconds() / 900)
                    if data.get('end_time') == '23:59':
                        count += 1
                for i in range(count):  # 15分钟写入一次
                    charge_config.append(data.get('charge_config'))
                    rl_list.append(data.get('rl'))
                    pv_list.append(data.get('pv'))
                    explain_json.append(data.get('explain')) if data.get('explain') else ''

            category_obj = UserStrategyCategoryNew.objects.create(strategy=strategy, name=category.get('name'),
                                                                  en_name=category.get('name'),
                                                               charge_config=str(charge_config),
                                                               is_follow=category.get('is_follow'),
                                                               rl_list=str(rl_list),
                                                               explain_json=str(explain_json),
                                                               pv_list=str(pv_list),
                                                               remark=category.get('remark'))

            pdr_data2 = {'id': category_obj.id,
                        'table': 't_user_strategy_category_new',
                        'update_data': {'name': category.get('name')}}

            redis_pool.publish(pub_name, json.dumps(pdr_data2))

            # 保存月份
            for month in category.get('months'):
                Month.objects.create(strategy=strategy, month_number=month, is_valid=False,
                                     user_Strategy_Category=category_obj)

        return strategy


class UpdateUserStrategyCategorySerializerNew(serializers.Serializer):
    """编辑用户策略"""

    name = serializers.CharField(required=True, max_length=32, label='策略名称')
    category_list = CategorySerializerNew(many=True, label="分类信息")

    def validate(self, attrs):
        lang = self.context.get('lang', 'zh')
        month_count = 0
        for category in attrs['category_list']:
            for month in category.get('months'):
                month_count += 1

        if month_count < 12:
            raise Exception("月份校验失败；不满12月！" if lang == 'zh' else "Month check failed; Less than 12 months!")
        elif month_count > 12:
            raise Exception("月份校验失败；超过12月！" if lang == 'zh' else "Month check failed; More than 12 months!")

        for category in attrs['category_list']:
            for month in category.get('months'):
                month_count += 1
            day_seconds = 0
            for data in category.get('data'):
                if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                    day_seconds = 86340
                    break
                start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                day_seconds += (end_time - start_time).total_seconds()
            if day_seconds != 86340:  # 00:00-23:59 的秒数
                raise Exception("时间校验失败；时段不足一天！" if lang == 'zh' else "Time check failed; Less than one day!")

        return attrs

    def update(self, strategy, validated_data):
        lang = self.context.get('lang', 'zh')
        # 先删除再写入
        Month.objects.filter(strategy=strategy).delete()
        UserStrategyCategoryNew.objects.filter(strategy=strategy).delete()
        UserStrategyHours.objects.filter(strategy=strategy).delete()

        strategy.name = validated_data['name']
        strategy.en_name = validated_data['name']
        strategy.save()

        # 异步翻译
        pdr_data = {'id': strategy.id,
                    'table': 't_user_strategy',
                    'update_data': {'name': validated_data['name']}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        hours_obj = UserStrategyHours.objects.create(strategy=strategy,
                                                     data=json.dumps(validated_data['category_list']))
        for category in validated_data['category_list']:
            charge_config = []
            rl_list = []
            explain_json = []
            pv_list = []
            for data in category['data']:
                if data.get('start_time') == '00:00' and data.get('end_time') == '23:59':
                    count = 96  # 15分钟一次
                else:
                    start_time = datetime.datetime.strptime(data.get('start_time'), "%H:%M")
                    end_time = datetime.datetime.strptime(data.get('end_time'), "%H:%M")
                    count = int((end_time - start_time).total_seconds() / 900)
                    if data.get('end_time') == '23:59':
                        count += 1
                for i in range(count):  # 15分钟写入一次
                    charge_config.append(data.get('charge_config'))
                    rl_list.append(data.get('rl'))
                    pv_list.append(data.get('pv'))
                    explain_json.append(data.get('explain')) if data.get('explain') else ''

            category_obj = UserStrategyCategoryNew.objects.create(strategy=strategy, name=category.get('name'),
                                                                  en_name=category.get('name'),
                                                                  charge_config=str(charge_config),
                                                                  is_follow=category.get('is_follow'),
                                                                  rl_list=str(rl_list),
                                                                  explain_json=str(explain_json),
                                                                  pv_list=str(pv_list),
                                                                  remark=category.get('remark'))

            # 异步翻译
            pdr_data = {'id': category_obj.id,
                        'table': 't_user_strategy_category_new',
                        'update_data': {'name': category.get('name')}}

            pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            redis_pool.publish(pub_name, json.dumps(pdr_data))

            # 保存月份
            for month in category.get('months'):
                Month.objects.create(strategy=strategy, month_number=month, user_Strategy_Category=category_obj, is_valid=False)

        return strategy


# class UpdateUserStrategyCategorySerializer(serializers.Serializer):
#     """编辑用户策略"""
#
#     name = serializers.CharField(required=True, max_length=32, label='策略名称')
#     category_list = CategorySerializer(many=True, label="分类信息")
#
#     def validate(self, attrs):
#         month_count = 0
#         for category in attrs['category_list']:
#             for month in category.get('months'):
#                 month_count += 1
#
#         if month_count < 12:
#             raise Exception("月份校验失败；不满12月！")
#         elif month_count > 12:
#             raise Exception("月份校验失败；超过12月！")
#         return attrs
#
#     def update(self, strategy, validated_data):
#         # 先删除再写入
#         Month.objects.filter(strategy=strategy).delete()
#         UserStrategyCategory.objects.filter(strategy=strategy).delete()
#
#         strategy.name = validated_data['name']
#
#         for category in validated_data['category_list']:
#             category_obj = UserStrategyCategory.objects.create(strategy=strategy, name=category.get('name'),
#                                                                charge_config=str(category.get('charge_config')),
#                                                                is_follow=category.get('is_follow'),
#                                                                rl_list=str(category.get('rl_list')),
#                                                                explain_json=str(category.get('explain_json')), **category.get('pv_dict'),
#                                                                    remark=category.get('remark'))
#             # 保存月份
#             for month in category.get('months'):
#                 Month.objects.create(strategy=strategy, month_number=month, user_Strategy_Category=category_obj, is_valid=False)
#
#         strategy.save()
#         return strategy


class UserStrategyApplySerializer(serializers.ModelSerializer):
    """自动控制策略：下发"""""
    # 手机号校验
    mobile = serializers.CharField(required=True,
                                   write_only=True)
    # 验证码校验
    code = serializers.CharField(required=True,
                                 write_only=True)

    strategy_id = serializers.IntegerField(required=True, write_only=True)
    station_ids = serializers.ListSerializer(child=serializers.IntegerField(), required=True)
    sign = serializers.IntegerField(required=False, write_only=False, label='标识小程序或者web：1：小程序')
    is_sendacopy = serializers.IntegerField(required=False, allow_null=True, label="是否抄送")

    class Meta:
        model = StrategyApplyHistory
        exclude = ['station', "user"]

    def validate_mobile(self, value):
        lang = self.context.get('lang', 'zh')
        user_id = self.context.get('user_id')
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect.")

        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号获取验证码！" if lang == 'zh' else
                                             "Please use the current account mobile number"
                                             " to obtain the verification code!")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get('lang', 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "stategy" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", value):
            raise Exception("验证码格式不正确" if lang == 'zh' else "Verification code format is incorrect.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Verification code expired or does not exist.")
        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else "Verification code error.")
        return value

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        station_ids = validated_data.get('station_ids')
        uid = self.context.get('uid')
        if not station_ids:
            raise Exception("站 ID 不能为空" if lang == 'zh' else "Station ID cannot be empty.")

        try:
            sign = validated_data.get('sign')
            if sign:
                master_station = MaterStation.objects.get(id=station_ids[0], is_delete=0)
                station_ins = master_station.stationdetails_set.filter(is_delete=0,
                    english_name=master_station.english_name).all()
            else:
                station_ins = StationDetails.objects.filter(is_delete=0, id__in=station_ids).all()
        except Exception as e:
            raise Exception("站点不存在" if lang == 'zh' else "Station does not exist.")

        validated_data.pop("mobile")
        validated_data.pop("code")

        old_strategy_ins = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        for station in station_ins:
            strategy_obj = StrategyApplyHistory.objects.create(user=user, name=old_strategy_ins.name,
                                                               en_name=old_strategy_ins.en_name, station=station,
                                                                uid=uid)

            old_category_objs = old_strategy_ins.userstrategycategorynew_set.all()
            for old_category_obj in old_category_objs:
                category_obj = StrategyApplyHistoryCategoryNew.objects.create(name=old_category_obj.name,
                                                                              en_name=old_category_obj.en_name,
                                                                              strategy=strategy_obj,
                                                                              charge_config=old_category_obj.charge_config,
                                                                              is_follow=old_category_obj.is_follow,
                                                                              rl_list=old_category_obj.rl_list,
                                                                              pv_list=old_category_obj.pv_list)
                old_category_months = old_category_obj.month_set.all()
                for old_category_month in old_category_months:
                    StrategyApplyHistoryMonth.objects.create(month_number=old_category_month.month_number,
                                                             strategy=strategy_obj, user_Strategy_Category=category_obj,
                                                             is_valid=False)
            if validated_data.get('is_sendacopy'):  # 确认抄送
                project = station.project
                title = f'{station.project.name}项目策略进行了调整，请您确认！'
                en_title = f'{station.project.name} project policy has been adjusted, please confirm!'
                MessageCenter.objects.create(title=title,  en_title=en_title, user_id=project.contact_person, issue_user=user,
                                             station=station, strategy_id=validated_data.get('strategy_id'), type=0)
        return 1


class UserStrategyApplyDefaultSerializer(serializers.ModelSerializer):
    """自动控制策略：默认策略下发"""""
    # 手机号校验
    mobile = serializers.CharField(required=True, write_only=True)
    # 验证码校验
    code = serializers.CharField(required=True, write_only=True)

    station_id = serializers.IntegerField(required=True, write_only=True, label='下发并网点')
    station_ids = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='关联并网点')
    sign = serializers.IntegerField(required=False, write_only=False, label='标识小程序或者web：1：小程序')
    is_sendacopy = serializers.IntegerField(required=False,allow_null=True, label="是否抄送")

    class Meta:
        model = StrategyApplyHistory
        exclude = ['station', "user"]

    def validate_mobile(self, value):
        lang = self.context.get('lang', 'zh')
        user_id = self.context.get('user_id')
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect.")

        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号获取验证码！" if lang == 'zh' else
                                             "Please use the mobile number of the current account to obtain the verification code!")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get('lang', 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "stategy" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", value):
            raise Exception("验证码格式不正确" if lang == 'zh' else "Verification code format is incorrect.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Verification code expired or does not exist.")
        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else "Verification code error.")
        return value

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        station_id = validated_data.get('station_id')
        station_ids = validated_data.get('station_ids')
        uid = self.context.get('uid')
        if not station_ids:
            raise Exception("站 ID 不能为空" if lang == 'zh' else "Station ID cannot be empty.")

        try:
            if station_id not in station_ids:
                station_ids.append(station_id)
            sign = validated_data.get('sign')
            if sign:
                master_station = MaterStation.objects.get(id=station_ids[0], is_delete=0)
                station_ins = master_station.stationdetails_set.filter(is_delete=0,
                    english_name=master_station.english_name).all()
            else:
                station_ins = StationDetails.objects.filter(is_delete=0, id__in=station_ids).all()
        except Exception as e:
            raise Exception("站点不存在" if lang == 'zh' else "Station does not exist.")

        validated_data.pop("mobile")
        validated_data.pop("code")

        for station in station_ins:

            name = station.province.name + common_response_code.ConfLevType.TYPE['zh'][station.type] + \
                             common_response_code.ConfLevType.LEVEL[lang][station.level] + '默认运行策略'
            en_name = station.province.name + ' ' + common_response_code.ConfLevType.TYPE['en'][station.type] + ' ' + \
                             common_response_code.ConfLevType.LEVEL[lang][station.level] + ' default run strategy'
            strategy_obj = StrategyApplyHistory.objects.create(user=user, name=name, en_name=en_name, station=station,
                                                         uid=uid)

            # 异步翻译
            # pdr_data = {'id': strategy_obj.id,
            #             'table': 't_user_strategy_apply_history',
            #             'update_data': {'name': name}}
            #
            # pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            # redis_pool.publish(pub_name, json.dumps(pdr_data))

            conn = get_redis_connection("3")
            res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station.english_name))
            if res_fllow:
                res_fllow = json.loads(eval(res_fllow))
            else:
                res_fllow = {}
            is_follow = res_fllow.get('WLoadFollowTC') if (res_fllow.get('WLoadFollowTC') and
                                                           res_fllow.get('WLoadFollowTC') not in
                                                           EMPTY_STR_LIST) else 0

            former_actic_ids = StationActicNew.objects.filter(station_id=station_id).all()
            former_actic_ids = [i.former_actic_id for i in former_actic_ids]
            for i in range(1, 13):
                year_month = f'{datetime.datetime.now().year}-{i}' if i >= 10 else f'{datetime.datetime.now().year}-0{i}'
                former_res = FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
                charge_config = [f.mark for f in former_res]
                rl_list = [round(f.power_value / f.power * 100, 2) for f in former_res]

                station_instance = PeakValleyNew.objects.filter(year_month=year_month, province=station.province, type=station.type, level=station.level).all()
                pv = [p.pv for p in station_instance]
                if not pv:
                    pv = [0 for _i in range(96)]
                # 做处理为了对应1-24点的计时
                pv.append(pv[0])
                pv.pop(0)
                category_obj = StrategyApplyHistoryCategoryNew.objects.create(name=f'月份{i}', en_name=f'Month {i}',
                                                                           strategy=strategy_obj,
                                                                           charge_config=json.dumps(charge_config),
                                                                           is_follow=is_follow,
                                                                           rl_list=json.dumps(rl_list),
                                                                           pv_list=json.dumps(pv)
                                                                           )

                StrategyApplyHistoryMonth.objects.create(month_number=i,
                                                         strategy=strategy_obj, user_Strategy_Category=category_obj,
                                                         is_valid=False)
            if validated_data.get('is_sendacopy'):  # 确认抄送
                project = station.project
                title = f'{station.project.name}项目策略进行了调整，请您确认！'
                en_title = f'The strategy of the project {station.project.name} has been adjusted, please confirm!'
                MessageCenter.objects.create(title=title, en_title=en_title, user_id=project.contact_person, issue_user=user, station=station, type=0)

        return strategy_obj



class UserStrategyApplyRealtimeSerializer(serializers.ModelSerializer):
    """自动控制策略：实时策略下发"""""
    # 手机号校验
    mobile = serializers.CharField(required=True, write_only=True)
    # 验证码校验
    code = serializers.CharField(required=True, write_only=True)
    month = serializers.IntegerField(required=True, write_only=True, label='下发月份')
    station_id = serializers.IntegerField(required=True, write_only=True, label='下发并网点')
    rl_list = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='限值list，96位')
    charge_config = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='充放电标识list，96位')
    pv_list = serializers.ListSerializer(required=True, child=serializers.IntegerField(), label='峰谷标识list，96位')
    update_data = serializers.ListSerializer(required=False, child=serializers.DictField(), allow_null=True, label="更新数据")
    is_sendacopy = serializers.IntegerField(required=False,allow_null=True, label="是否抄送")

    class Meta:
        model = StrategyApplyHistory
        exclude = ['station', "user"]

    def validate_mobile(self, value):
        lang = self.context.get('lang', 'zh')
        user_id = self.context.get('user_id')
        user_ins = UserDetails.objects.get(id=user_id)

        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == 'zh' else "Mobile number format is incorrect.")

        if user_ins.mobile != value:
            raise Exception("请使用当前账户的手机号获取验证码！" if lang == 'zh' else
                                             "Please use the mobile number of the current account to obtain the verification code!")
        return value

    def validate_code(self, value):
        """验证码"""
        lang = self.context.get('lang', 'zh')
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "stategy" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)

        if not re.match(r"^\d{6}$", value):
            raise Exception("验证码格式不正确" if lang == 'zh' else "Verification code format is incorrect.")

        if not cache_mobile_code:
            raise Exception("验证码过期或不存在" if lang == 'zh' else "Verification code expired or does not exist.")
        if cache_mobile_code.decode('utf-8') != value:
            raise Exception("验证码错误" if lang == 'zh' else "Verification code error.")
        return value

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        station_id = validated_data.get('station_id')
        uid = self.context.get('uid')
        if not station_id:
            raise Exception("站 ID 不能为空" if lang == 'zh' else "Station ID cannot be empty.")

        try:
            master_station = MaterStation.objects.get(id=station_id, is_delete=0)
            station = master_station.stationdetails_set.filter(is_delete=0,
                                                                        english_name=master_station.english_name).first()
        except Exception as e:
            raise Exception("站点不存在" if lang == 'zh' else "Station does not exist.")

        validated_data.pop("mobile")
        validated_data.pop("code")

        update_data = validated_data.get('update_data')
        update_list = []
        en_update_list = []
        for i in update_data:
            update_list.append(f"{i['time']}-{charge_dict.get(i['charge'])}-{i['rl']}%")
            en_update_list.append(f"{i['time']}-{en_charge_dict.get(i['charge'])}-{i['rl']}%")


        name = '临时策略' + f"（{','.join(update_list)}）"
        en_name = 'Temporarily Strategy' + f"（{','.join(en_update_list)}）"
        strategy_obj = StrategyApplyHistory.objects.create(user=user, name=name, en_name=en_name, station=station,
                                                     uid=uid)


        conn = get_redis_connection("3")
        res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station.english_name))
        if res_fllow:
            res_fllow = json.loads(eval(res_fllow))
        else:
            res_fllow = {}
        is_follow = res_fllow.get('WLoadFollowTC') if (res_fllow.get('WLoadFollowTC') and
                                                       res_fllow.get('WLoadFollowTC') not in
                                                       EMPTY_STR_LIST) else 0

        month = validated_data.get('month')
        charge_config = validated_data.get('charge_config')
        rl_list = validated_data.get('rl_list')
        pv = validated_data.get('pv_list')
        category_obj = StrategyApplyHistoryCategoryNew.objects.create(name=f'月份{month}', en_name=f'Month {month}',
                                                                   strategy=strategy_obj,
                                                                   charge_config=json.dumps(charge_config),
                                                                   is_follow=is_follow,
                                                                   rl_list=json.dumps(rl_list),
                                                                   pv_list=json.dumps(pv)
                                                                       )

        StrategyApplyHistoryMonth.objects.create(month_number=month,
                                                 strategy=strategy_obj, user_Strategy_Category=category_obj,
                                                 is_valid=False)
        if validated_data.get('is_sendacopy'):  # 确认抄送
            project = station.project
            title = f'{station.project.name}项目策略进行了调整，请您确认！'
            en_title = f'The strategy of the project {station.project.name} has been adjusted, please confirm!'
            MessageCenter.objects.create(title=title, en_title=en_title, user_id=project.contact_person, issue_user=user, station=station, type=0)

        return strategy_obj

class UserStrategyCompareSerializer(serializers.Serializer):
    """策略比较"""
    station_id = serializers.CharField(required=True, write_only=True)
    strategy_id = serializers.CharField(required=False, write_only=True)
    t = serializers.IntegerField(required=True, write_only=True, label='策略类型：1：自定义，0：默认')
    sign = serializers.IntegerField(required=False, write_only=False, label='标识小程序或者web：1：小程序')
    hours = serializers.CharField(required=True, write_only=True, max_length=64, label='对比时间；多个以英文逗号隔开')


    def validate_station_id(self, value):
        lang = self.context.get('lang', 'zh')
        master_stations = StationDetails.objects.filter(is_delete=0, english_name=value)
        if not master_stations.exists():
            return Exception("站名不存在" if lang == 'zh' else "Station name does not exist.")
        return value