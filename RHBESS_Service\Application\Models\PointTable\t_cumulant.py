#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:01:20
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_cumulant.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 14:11:31



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class CumulantPT(scada_Base):
    ''' 累积量配置表 '''
    __tablename__ = "t_cumulant"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    device = Column(Integer, nullable=False, comment=u"所属设备")
    no = Column(Integer, nullable=False,comment=u"编号")
    descr = Column(String(256), nullable=False,comment=u"名称")
    max = Column(Integer, nullable=False,comment=u"最大值")
    delta = Column(Integer, nullable=False,comment=u"系数")
    coef = Column(Float, nullable=False,comment=u"变化值")
    store_flag = Column(Integer, nullable=True,comment=u"")
    gen_timed_flag = Column(Integer, nullable=True,comment=u"")
    rpt_timed_flag = Column(Integer, nullable=True,comment=u"")
    comment = Column(Integer, nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','device':'%s','no':%s,'descr':'%s','max':'%s','delta':'%s','coef':'%s','store_flag':'%s','gen_timed_flag':'%s','rpt_timed_flag':'%s','comment':'%s'}" % (
            self.id,self.name,self.device,self.no,self.descr,self.max,self.delta,self.coef,self.store_flag,self.gen_timed_flag,self.rpt_timed_flag,self.comment)