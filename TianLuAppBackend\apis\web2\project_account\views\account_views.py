# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/4/1 14:08
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : views.py
# @Software : PyCharm
import datetime
import json
import logging
import concurrent.futures

import os
import threading
import time
import traceback
from copy import deepcopy

from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from TianLuAppBackend import settings
from apis.statistics_apis.db_link import time_range_by_dwd_for_web
from apis.user.models import Project, PeakValleyNew, UnitPrice, StationMeterUseTime
from apis.web2.project_account.async_tasks import save_project_account_detail_to_redis
from apis.web2.project_account.gen_pdf import gen_pdf
from common import common_response_code
from common.database_pools import ads_db_tool
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from tools.count import get_station_price
from apis.web2.project_account.gen_excel import generate_excel
from tools.minio_tool import MinioTool


def convert_to_time_ranges(lst):
        """
        将类似[0, 1,2,3, 7,8,9,18,19,20, 22,23]的数据转换成[0:00~3:00，7:00~9:00，18:00~23:00]
        """""
        time_ranges = []
        start_time = None
        for i in range(len(lst)):
            if i == 0 or lst[i] != lst[i - 1] + 1:
                if start_time is not None:
                    time_ranges.append(f"{start_time:02d}:00~{lst[i - 1]:02d}:00")
                start_time = lst[i]
        if start_time is not None:
            time_ranges.append(f"{start_time:02d}:00~{lst[-1]:02d}:00")
        return time_ranges


def convert_to_time_ranges_v2(lst):
    """
    将类似[0, 1,2,3, 7,8,9,18,19,20, 22,23]的数据转换成[0:00~3:00，7:00~9:00，18:00~23:00]
    """""
    if len(lst) == 0:
        return []
    # 将时间点转换为datetime对象
    time_format = "%H:%M"
    time_objects = [datetime.datetime.strptime(t, time_format) for t in lst]

    # 初始化时间段列表
    time_ranges = []

    # 起始时间点
    start_time = time_objects[0]
    prev_time = time_objects[0]

    for current_time in time_objects[1:]:
        if current_time - prev_time != datetime.timedelta(minutes=15):
            end_time = prev_time
            time_ranges.append(f"{start_time.strftime(time_format)}~{end_time.strftime(time_format)}")
            start_time = current_time
        prev_time = current_time

    # 添加最后一个时间段
    time_ranges.append(f"{start_time.strftime(time_format)}~{prev_time.strftime(time_format)}")
    return time_ranges


class ProjectAccountView(APIView):
    """项目结算管理--查询: 结算"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_station_account_data(self, station, start_time, end_time, income_dict, lang='zh'):
        """
        结算单
        :param project_id:
        :param start_day:
        :param end_day:
        :return:
        """
        if station.master_station.mode != 1:
            args = ['STPAP', 'SPATC', 'SFAPC', 'SPAPL', 'SPAVC', 'STAPIR', 'SRATC', 'SRAPC', 'SRAPL', 'SRAVC', 'SPT', 'SCT']
        else:
            args = ['TPAP', 'PATC', 'FAPC', 'PAPL', 'PAVC', 'TAPIR', 'RATC', 'RAPC', 'RAPL', 'RAVC', 'PT', 'CT']

        # if station.master_station.mode == 1:
        #     select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
        #                   " where station=%s and device_type=0 and day between %s and %s order by day")
        # else:
        #     select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
        #                   " where station=%s and device_type=1 and day between %s and %s order by day")

        if station.master_station.mode == 1:
            select_sql = ("SELECT sum(pointed_chag) as pointed_chag, sum(pointed_disg) as pointed_disg, sum(peak_chag) as peak_chag,"
                          "sum(peak_disg) as peak_disg, sum(flat_chag) as flat_chag, sum(flat_disg) as flat_disg, sum(valley_chag) as valley_chag,"
                          "sum(valley_disg) as valley_disg FROM ads_report_ems_chag_disg_1d"
                          " where station=%s and device_type=0 and day between %s and %s")
        else:
            select_sql = ("SELECT sum(pointed_chag) as pointed_chag, sum(pointed_disg) as pointed_disg, sum(peak_chag) as peak_chag,"
                          "sum(peak_disg) as peak_disg, sum(flat_chag) as flat_chag, sum(flat_disg) as flat_disg, sum(valley_chag) as valley_chag,"
                          "sum(valley_disg) as valley_disg FROM ads_report_ems_chag_disg_1d"
                          " where station=%s and device_type=1 and day between %s and %s")

        # select_sql = ("SELECT * FROM ads_report_chag_disg_union_1d"
        #               " where station=%s and station_type=0 and day=%s")

        station_info = {
            'station_name': station.station_name,
            'station_number': station.meter_number if station.meter_number else '--',
            'rate': '--',     # 实时获取
            'is_account': False
        }

        # 只时间在设置的使用范围内使用结算表
        meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
        if meter_use_time:
            if meter_use_time.end_time:
                is_use_account = (station.is_account and
                                  meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
            else:
                is_use_account = (station.is_account and
                                  meter_use_time.start_time <= start_time)
        else:
            is_use_account = False

        # 外接结算电表
        if is_use_account:

            station_info['is_account'] = True

            # 判断是否存在自定义电价配置
            user_price = UnitPrice.objects.filter(station=station.master_station, start__gte=start_time.date(),
                                                  end__lte=end_time.date(), delete=0)

            # 优先使用自定义电价
            if user_price.exists():
                user_price = user_price.last()

                title = user_price.name

            # 使用代理购电价格
            else:
                # 拼接标题
                if lang == 'zh':
                    province = station.province.name
                    title = province + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + \
                            dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
                else:
                    province = station.province.en_name
                    title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                             f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

            # 查询抄表底码
            data = time_range_by_dwd_for_web(station.english_name, 'cumulant', 'ems', 'EMS', start_time,
                                             end_time, *args)

            if data:
                sorted_data = sorted(data, key=lambda x: x['time'])
                first_data = sorted_data[0]
                last_data = sorted_data[-1]

                if station.master_station.mode != 1:
                    first_SPATC = first_data['SPATC']
                    last_SPATC = last_data['SPATC']

                    first_SFAPC = first_data['SFAPC']
                    last_SFAPC = last_data['SFAPC']

                    first_SPAPL = first_data['SPAPL']
                    last_SPAPL = last_data['SPAPL']

                    first_SPAVC = first_data['SPAVC']
                    last_SPAVC = last_data['SPAVC']

                    first_SRATC = first_data['SRATC']
                    last_SRATC = last_data['SRATC']

                    first_SRAPC = first_data['SRAPC']
                    last_SRAPC = last_data['SRAPC']

                    first_SRAPL = first_data['SRAPL']
                    last_SRAPL = last_data['SRAPL']

                    first_SRAVC = first_data['SRAVC']
                    last_SRAVC = last_data['SRAVC']

                    first_SPT = first_data['SPT'] if first_data['SPT'] else '--'
                    first_SCT = first_data['SCT'] if first_data['SCT'] else '--'

                    last_SPT = last_data['SPT'] if last_data['SPT'] else '--'
                    last_SCT = last_data['SCT'] if last_data['SCT'] else '--'
                else:
                    first_SPATC = first_data['PATC']
                    last_SPATC = last_data['PATC']

                    first_SFAPC = first_data['FAPC']
                    last_SFAPC = last_data['FAPC']

                    first_SPAPL = first_data['PAPL']
                    last_SPAPL = last_data['PAPL']

                    first_SPAVC = first_data['PAVC']
                    last_SPAVC = last_data['PAVC']

                    first_SRATC = first_data['RATC']
                    last_SRATC = last_data['RATC']

                    first_SRAPC = first_data['RAPC']
                    last_SRAPC = last_data['RAPC']

                    first_SRAPL = first_data['RAPL']
                    last_SRAPL = last_data['RAPL']

                    first_SRAVC = first_data['RAVC']
                    last_SRAVC = last_data['RAVC']

                    first_SPT = first_data['PT'] if first_data['PT'] else '--'
                    first_SCT = first_data['CT'] if first_data['CT'] else '--'

                    last_SPT = last_data['PT'] if last_data['PT'] else '--'
                    last_SCT = last_data['CT'] if last_data['CT'] else '--'

                spike_charge_income = 0
                spike_discharge_income = 0
                spike_charge = 0
                spike_discharge = 0

                peak_charge_income = 0
                peak_discharge_income = 0
                peak_charge = 0
                peak_discharge = 0

                flat_charge_income = 0
                flat_discharge_income = 0
                flat_charge = 0
                flat_discharge = 0

                valley_charge_income = 0
                dvalley_charge_income = 0
                valley_discharge_income = 0
                dvalley_discharge_income = 0
                valley_charge = 0
                valley_discharge = 0

                total_income = 0

                # 查询充放电量
                detail = ads_db_tool.select_one(select_sql, station.english_name,
                                                  start_time.strftime('%Y-%m-%d'), end_time.strftime('%Y-%m-%d'))

                if detail:
                    spike_charge = detail['pointed_chag'] if detail['pointed_chag'] != None else '--'
                    spike_discharge = detail['pointed_disg'] if detail['pointed_disg'] != None else '--'
                    peak_charge = detail['peak_chag'] if detail['peak_chag'] != None else '--'
                    peak_discharge = detail['peak_disg'] if detail['peak_disg'] != None else '--'
                    flat_charge = detail['flat_chag'] if detail['flat_chag'] != None else '--'
                    flat_discharge = detail['flat_disg'] if detail['flat_disg'] != None else '--'
                    valley_charge = detail['valley_chag'] if detail['valley_chag'] != None else '--'
                    valley_discharge = detail['valley_disg'] if detail['valley_disg'] != None else '--'
                    # dvalley_charge = detail['dvalley_chag'] if detail['dvalley_chag'] != None else '--'
                    # dvalley_discharge = detail['dvalley_disg'] if detail['dvalley_disg'] != None else '--'

                # 查询充电、放电收益
                if income_dict:
                    station_dict = income_dict.get(station.english_name)
                    if station_dict:
                        spike_charge_income = - station_dict['pointed_chag_income']
                        spike_discharge_income = station_dict['pointed_disg_income']
                        peak_charge_income = - station_dict['peak_chag_income']
                        peak_discharge_income = station_dict['peak_disg_income']
                        flat_charge_income = - station_dict['flat_chag_income']
                        flat_discharge_income = station_dict['flat_disg_income']
                        valley_charge_income = - station_dict['valley_chag_income']
                        dvalley_charge_income = - station_dict['dvalley_chag_income']
                        valley_discharge_income = station_dict['valley_disg_income']
                        dvalley_discharge_income = station_dict['dvalley_disg_income']

                        total_income = (spike_discharge_income + peak_discharge_income + flat_discharge_income +
                                        valley_discharge_income + dvalley_discharge_income + (spike_charge_income + peak_charge_income +
                                                                   flat_charge_income + valley_charge_income + dvalley_charge_income))

            else:
                first_SPATC = '--'
                last_SPATC = '--'

                first_SFAPC = '--'
                last_SFAPC = '--'

                first_SPAPL = '--'
                last_SPAPL = '--'

                first_SPAVC = '--'
                last_SPAVC = '--'

                first_SRATC = '--'
                last_SRATC = '--'

                first_SRAPC = '--'
                last_SRAPC = '--'

                first_SRAPL = '--'
                last_SRAPL = '--'

                first_SRAVC = '--'
                last_SRAVC = '--'

                first_SPT = '--'
                first_SCT = '--'

                last_SPT = '--'
                last_SCT = '--'

                spike_charge_income = '--'
                spike_discharge_income = '--'
                spike_charge = '--'
                spike_discharge = '--'

                peak_charge_income = '--'
                peak_discharge_income = '--'
                peak_charge = '--'
                peak_discharge = '--'

                flat_charge_income = '--'
                flat_discharge_income = '--'
                flat_charge = '--'
                flat_discharge = '--'

                valley_charge_income = '--'
                dvalley_charge_income = '--'
                valley_discharge_income = '--'
                dvalley_discharge_income = '--'
                valley_charge = '--'
                valley_discharge = '--'
                # dvalley_charge = '--'
                # dvalley_discharge = '--'

                total_income = '--'

            if not '--' in [first_SPT, first_SCT, last_SPT, last_SCT]:
                station_info['rate'] = round((float(first_SPT) * float(first_SCT) + float(last_SPT) * float(last_SCT)) / 2, 2)
            elif '--' in [first_SPT, first_SCT] and not '--' in [last_SPT, last_SCT]:
                station_info['rate'] = round(float(last_SPT) * float(last_SCT), 2)
            elif '--' in [last_SPT, last_SCT] and not '--' in [first_SPT, first_SCT]:
                station_info['rate'] = round(float(first_SPT) * float(first_SCT), 2)
            else:
                station_info['rate'] = '--'

            station_info['charge'] = {
                'spike': {
                    'first': first_SRATC,
                    'last': last_SRATC,
                    'count': round(spike_charge, 2) if spike_charge != '--' else spike_charge,
                    'income': round(spike_charge_income, 2) if spike_charge_income != '--' else '--',
                },
                'peak': {
                    'first': first_SRAPC,
                    'last': last_SRAPC,
                    'count': round(peak_charge, 2) if peak_charge != '--' else peak_charge,
                    'income': round(peak_charge_income, 2) if peak_charge_income != '--' else '--'
                },
                'flat': {
                    'first': first_SRAPL,
                    'last': last_SRAPL,
                    'count': round(flat_charge, 2) if flat_charge != '--' else flat_charge,
                    'income': round(flat_charge_income, 2) if flat_charge_income != '--' else '--'
                },
                'valley': {
                    'first': first_SRAVC,
                    'last': last_SRAVC,
                    'count': round(valley_charge, 2) if valley_charge != '--' else valley_charge,
                    'income': round(valley_charge_income, 2) if valley_charge_income != '--' else '--'
                },
                'dvalley': {
                    'first': '--',
                    'last': '--',
                    'count': '--',
                    'income': round(dvalley_charge_income, 2) if dvalley_charge_income != '--' else '--'
                }
            }
            station_info['discharge'] = {
                'spike': {
                    'first': first_SPATC,
                    'last': last_SPATC,
                    'count': round(spike_discharge, 2) if spike_discharge != '--' else spike_discharge,
                    'income': round(spike_discharge_income, 2) if spike_discharge_income != '--' else '--'
                },
                'peak': {
                    'first': first_SFAPC,
                    'last': last_SFAPC,
                    'count': round(peak_discharge, 2) if peak_discharge != '--' else peak_discharge,
                    'income': round(peak_discharge_income, 2) if peak_discharge_income != '--' else '--'
                },
                'flat': {
                    'first': first_SPAPL,
                    'last': last_SPAPL,
                    'count': round(flat_discharge, 2) if flat_discharge != '--' else flat_discharge,
                    'income': round(flat_discharge_income, 2) if flat_discharge_income != '--' else '--'
                },
                'valley': {
                    'first': first_SPAVC,
                    'last': last_SPAVC,
                    'count': round(valley_discharge, 2) if valley_discharge != '--' else valley_discharge,
                    'income': round(valley_discharge_income, 2) if valley_discharge_income != '--' else '--'
                },
                'devalley': {
                    'first': '--',
                    'last': '--',
                    'count': '--',
                    'income': round(dvalley_discharge_income, 2) if dvalley_discharge_income != '--' else '--'
                }
            }
            station_info['total_income'] = round(total_income, 2) if total_income != '--' else '--'
            station_info['total_income_'] = round(total_income, 4) if total_income != '--' else '--'
            station_info['price_url'] = title
        return station_info

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        # user_id = request.user["user_id"]
        query_params = request.query_params
        project_id = query_params.get('project_id')
        start_day = query_params.get("start_day")
        end_day = query_params.get("end_day")

        if not all([project_id, start_day, end_day]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id/start_day/end_day 缺失!!!" if lang == 'zh' else "Missing parameters: project_id/start_day/end_day"},
                }
            )

        # 校验项目id
        user_id = request.user["user_id"]
        projects = Project.objects.filter(user=user_id, is_used=1).all()
        if int(project_id) not in [project.id for project in projects]:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 错误!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # 校验参数start_day和end_day
        try:
            start_day = start_day + " 00:00:00"
            end_day = end_day + " 23:59:59"
            start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：start_day/end_day 错误!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        if start_day > end_day:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：开始日期不能大于结束日期!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        return_dic = {}

        # 查询数据
        try:
            project = Project.objects.get(id=int(project_id), is_used=1)
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 查询报错!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # 单独起1个线程缓存需要下载的数据
        s = threading.Thread(target=save_project_account_detail_to_redis, args=(project_id, start_day, end_day, user_id, lang))
        s.start()

        # 结算电表迁移至各个从站，其中标准主从只显示从站，不显示000主站
        stations = project.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()

        # 查询地址
        address = ''
        if stations.exists():
            address = stations.first().address if lang == 'zh' else stations.first().en_address

        return_dic['address'] = address
        return_dic['project'] = project.name
        return_dic['account_month'] = end_time.strftime("%Y年%m月") if lang == 'zh' else end_time.strftime("%Y-%m")
        return_dic['account_start'] = start_day
        return_dic['account_end'] = end_day
        return_dic['station_number'] = '/'
        return_dic['stations_info'] = []

        temp_dict = {
            'STPAP': '当前正向有功总电量',
            'SPATC': '当前正向有功尖电量',
            'SFAPC': '当前正向有功峰电量',
            'SPAPL': '当前正向有功平电量',
            'SPAVC': '当前正向有功谷电量',
            'STAPIR': '当前反向有功总电量',
            'SRATC': '当前反向有功尖电量',
            'SRAPC': '当前反向有功峰电量',
            'SRAPL': '当前反向有功平电量',
            'SRAVC': '当前反向有功谷电量'
        }

        # 查询所有从站结算收益集合，避免在从站循环中多次处理查询数据库，优化接口响应速度
        stations_names = [i.english_name for i in stations]
        # sql = """select station, sum(pointed_chag_income) as pointed_chag_income, sum(pointed_disg_income) as pointed_disg_income, sum(peak_chag_income) as peak_chag_income,
        #                   sum(peak_disg_income) as peak_disg_income, sum(flat_chag_income) as flat_chag_income, sum(flat_disg_income) as flat_disg_income, sum(valley_chag_income) as valley_chag_income,
        #                   sum(valley_disg_income) as valley_disg_income,sum(dvalley_chag_income) as dvalley_chag_income,
        #                   sum(dvalley_disg_income) as dvalley_disg_income from ads_report_station_income_1d where station in %s and day between %s and %s group by station"""
        #
        # results = ads_db_tool.select_many(sql, stations_names, start_time.date(), end_time.date())
        # results_dict = {}
        # if results:
        #     for i in results:
        #         results_dict[i['station']] = i
        # 查询结算收益
        sql = """SELECT
                                station,
                                sum( pointed_chag_income ) AS pointed_chag_income,
                                sum( peak_chag_income ) AS peak_chag_income,
                                sum( flat_chag_income ) AS flat_chag_income,
                                sum( valley_chag_income ) AS valley_chag_income,
                                sum( dvalley_chag_income ) AS dvalley_chag_income,
                                sum( pointed_disg_income ) AS pointed_disg_income,
                                sum( peak_disg_income ) AS peak_disg_income,
                                sum( flat_disg_income ) AS flat_disg_income,
                                sum( valley_disg_income ) AS valley_disg_income,
                                sum( dvalley_disg_income ) AS dvalley_disg_income,
                                sum( peak_load_shifting ) AS peak_load_shifting 
                            FROM
                                ads_report_ems_station_income_1d 
                            WHERE
                                station IN %s
                                AND DAY >= %s AND DAY <= %s 
                            GROUP BY
                                station """
        results = ads_db_tool.select_many(sql, stations_names, start_time.date(), end_time.date())
        results_dict = {i['station']: i for i in results} if results else {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            futures = list()
            for station in stations:
                future = executor.submit(self.get_station_account_data, station, start_time, end_time, results_dict, lang)
                futures.append(future)

            # station_info = self.get_m_station_data(master_station, start_time, end_time)

            return_dic['stations_info'] = [f.result() for f in concurrent.futures.as_completed(futures) if f.result()['is_account'] is True]

        return_dic['stations_info'] = sorted(return_dic['stations_info'], key=lambda x: x['station_name'])

        # return_dic['is_account'] = True if len(return_dic['stations_info']) else False
        # return_dic['is_account'] = True if any([i['is_account'] for i in return_dic['stations_info']]) else False
        return_dic['is_account'] = True if len(return_dic['stations_info']) else False

        if return_dic['is_account']:
            # 储能电站削峰填谷总收益
            return_dic['project_total_income'] = round(sum([i['total_income_'] for i in return_dic['stations_info'] if i['is_account'] and i['total_income_'] != '--']), 2)
            return_dic['note'] = "说明:\n1. 各峰谷标志的充放电量来自于并网点结算电表数据。\n2. 结算电量 = 电表倍率*（本次报表底码 - 上次抄表底码）。\n3. 结算金额 = ∑(储能系统在各峰谷时段并网点结算电表充放电电量 ×对应时段单位电价)。\n4. 削峰填谷收益 = 正向有功（放电）结算金额-反向有功（充电）结算金额。" if lang == 'zh' \
                else ("Instruction:\n"
                      "1. Energy data is measured from settlement meters.\n"
                      "2. Settlement Energy = Multiplier×(Current Meter Reading - Last Meter Reading).\n"
                      "3. Settlement Amount = ∑（Price by each hour × Energy by each hour）.\n"
                      "4. Peak Shaving Profit = Amount by discharging -Amount by charging.")
            # return_dic['note'] = """
            # 说明:
            # 1. 各峰谷标志的充放电量来自于并网点结算电表数据。
            # 2. 结算电量 = 电表倍率*（本次报表底码 - 上次抄表底码）。
            # 3. 结算金额 = ∑(储能系统在各峰谷时段并网点结算电表充放电电量 ×对应时段单位电价)。
            # 4. 削峰填谷收益 = 反向有功（放电）结算金额-正向有功（充电）结算金额。
            # """

        else:
            return_dic['message'] = f"{project.name}并未安装外接结算电表，无法计算结算电量计收益结算数据！" if lang == 'zh' else "The project has no settlement meter, and the settlement data cannot be calculated!"

        return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": f"success", "detail": return_dic},
                }
            )


class ProjectAccountDetailView(APIView):
        """项目结算管理--查询: 充放电明细"""""

        authentication_classes = [
            JwtParamAuthentication,
            JWTHeaderAuthentication,
            DenyAuthentication,
        ]  # jwt认证

        def get_station_details_data(self, station, start_time, end_time, lang='zh'):
            """
            结算电量明细
            :param project_id:
            :param start_day:
            :param end_day:
            :return:
            """
            if station.master_station.mode == 1:
                args = ['TPAP', 'PATC', 'FAPC', 'PAPL', 'PAVC', 'TAPIR', 'RATC', 'RAPC', 'RAPL', 'RAVC',
                        'PT', 'CT']
            else:
                args = ['STPAP', 'SPATC', 'SFAPC', 'SPAPL', 'SPAVC', 'STAPIR', 'SRATC', 'SRAPC', 'SRAPL', 'SRAVC', 'SPT', 'SCT']

            if station.master_station.mode == 1:
                select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                              " where station=%s and device_type=0 and day=%s")
            else:
                select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                              " where station=%s and device_type=1 and day=%s")

            # select_sql = ("SELECT * FROM ads_report_chag_disg_union_1d"
            #               " where station=%s and station_type=0 and day=%s")

            station_info = {
                'station_name': station.station_name,
                'station_number': station.meter_number if station.meter_number else '--',
                'rate': '--',           # todo 实时获取
                'is_account': False
            }

            # 只时间在设置的使用范围内使用结算表
            meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
            if meter_use_time:
                if meter_use_time.end_time:
                    is_use_account = (station.is_account and meter_use_time and
                                      meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
                else:
                    is_use_account = (station.is_account and meter_use_time and
                                      meter_use_time.start_time <= start_time)
            else:
                is_use_account = False

            # 外接结算电表
            if is_use_account:

                station_info['is_account'] = True

                days_data = []

                # 遍历每一天计算当日收益
                target_day = start_time
                end_day_ = end_time

                while target_day <= end_day_:

                    day_spike_charge_values = []
                    day_spike_discharge_values = []

                    day_peak_charge_values = []
                    day_peak_discharge_values = []

                    day_flat_charge_values = []
                    day_flat_discharge_values = []

                    day_valley_charge_values = []
                    day_valley_discharge_values = []

                    day_dvalley_charge_values = []
                    day_dvalley_discharge_values = []

                    detail = ads_db_tool.select_one(select_sql, station.english_name,
                                                    target_day.strftime('%Y-%m-%d'))

                    # price_dict = get_station_price(station, target_day)

                    if detail:
                        day_spike_charge_values.append(float(detail['pointed_chag']))
                        day_spike_discharge_values.append(float(detail['pointed_disg']))

                        day_peak_charge_values.append(float(detail['peak_chag']))
                        day_peak_discharge_values.append(float(detail['peak_disg']))

                        day_flat_charge_values.append(float(detail['flat_chag']))
                        day_flat_discharge_values.append(float(detail['flat_disg']))

                        day_valley_charge_values.append(float(detail['valley_chag']))
                        day_valley_discharge_values.append(float(detail['valley_disg']))

                        # day_dvalley_charge_values.append(float(detail['dvalley_chag']))
                        # day_dvalley_discharge_values.append(float(detail['dvalley_disg']))

                        spike_charge = -round(sum(day_spike_charge_values), 2)
                        spike_discharge = round(sum(day_spike_discharge_values), 2)

                        peak_charge = -round(sum(day_peak_charge_values), 2)
                        peak_discharge = round(sum(day_peak_discharge_values), 2)

                        flat_charge = -round(sum(day_flat_charge_values), 2)
                        flat_discharge = round(sum(day_flat_discharge_values), 2)

                        valley_charge = -round(sum(day_valley_charge_values), 2)
                        valley_discharge = round(sum(day_valley_discharge_values), 2)

                        # dvalley_charge = -round(sum(day_dvalley_charge_values), 2)
                        # dvalley_discharge = round(sum(day_dvalley_discharge_values), 2)

                        total_charge = round(spike_charge + peak_charge + flat_charge + valley_charge, 1)
                        # total_charge = round(spike_charge + peak_charge + flat_charge + valley_charge + dvalley_charge, 1)
                        total_discharge = round(spike_discharge + peak_discharge + flat_discharge + valley_discharge, 1)
                        # total_discharge = round(spike_discharge + peak_discharge + flat_discharge + valley_discharge + dvalley_discharge, 1)

                        temp_dict = {
                            "date": target_day.strftime('%Y-%m-%d'),
                            "spike_charge": spike_charge,
                            "spike_discharge": spike_discharge,
                            "peak_charge": peak_charge,
                            "peak_discharge": peak_discharge,
                            "flat_charge": flat_charge,
                            "flat_discharge": flat_discharge,
                            "valley_charge": valley_charge,
                            "valley_discharge": valley_discharge,
                            # "dvalley_charge": dvalley_charge,
                            # "dvalley_discharge": dvalley_discharge,
                            "dvalley_charge": 0,
                            "dvalley_discharge": 0,
                            "total_charge": total_charge,
                            "total_discharge": total_discharge
                        }

                        days_data.append(temp_dict)

                    target_day += datetime.timedelta(days=1)

                station_info['detail'] = days_data

            return station_info

        def get(self, request):
            lang = request.headers.get("lang", 'zh')
            # user_id = request.user["user_id"]
            query_params = request.query_params
            project_id = query_params.get('project_id')
            start_day = query_params.get("start_day")
            end_day = query_params.get("end_day")

            if not all([project_id, start_day, end_day]):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：project_id/start_day/end_day 缺失!!!" if lang == 'zh' else "Parameters: project_id/start_day/end_day are missing."},
                    }
                )

            # 校验项目id
            user_id = request.user["user_id"]
            projects = Project.objects.filter(user=user_id, is_used=1).all()
            if int(project_id) not in [project.id for project in projects]:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：project_id 错误!!!" if lang == 'zh' else "Parameters: project_id is wrong."},
                    }
                )

            # 校验参数start_day和end_day
            try:
                start_day = start_day + " 00:00:00"
                end_day = end_day + " 23:59:59"
                start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
                end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logging.error(e)
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：start_day/end_day 错误!!!" if lang == 'zh' else "Parameters: start_day/end_day is wrong."},
                    }
                )

            if start_day > end_day:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：开始日期不能大于结束日期!!!" if lang == 'zh' else 'Parameter: The start date cannot be greater than the end date.'},
                    }
                )

            return_dic = {}

            # 查询数据
            try:
                project = Project.objects.get(id=int(project_id), is_used=1)
            except Exception as e:
                logging.error(e)
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：project_id 查询报错!!!" if lang == 'zh' else "Parameter: project_id query error."},
                    }
                )

            # master_stations = project.materstation_set.filter(is_delete=0).all()

            # 结算电表迁移至各个从站，其中标准主从只显示从站，不显示000主站
            stations = project.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()

            # 查询地址
            address = ''
            if stations.exists():
                address = stations.first().address if lang == 'zh' else stations.first().en_address

            return_dic['address'] = address
            return_dic['project'] = project.name
            return_dic['account_month'] = end_time.strftime("%Y年%m月") if lang == 'zh' else  end_time.strftime("%Y-%m")
            return_dic['account_start'] = start_day
            return_dic['account_end'] = end_day
            return_dic['station_number'] = '/'
            return_dic['stations_info'] = []

            temp_dict = {
                'STPAP': '当前正向有功总电量',
                'SPATC': '当前正向有功尖电量',
                'SFAPC': '当前正向有功峰电量',
                'SPAPL': '当前正向有功平电量',
                'SPAVC': '当前正向有功谷电量',
                'STAPIR': '当前反向有功总电量',
                'SRATC': '当前反向有功尖电量',
                'SRAPC': '当前反向有功峰电量',
                'SRAPL': '当前反向有功平电量',
                'SRAVC': '当前反向有功谷电量'
            }
            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                futures = list()
                for station in stations:
                    future = executor.submit(self.get_station_details_data, station, start_time, end_time)
                    futures.append(future)

                # station_info = self.get_m_station_data(master_station, start_time, end_time)

                return_dic['stations_info'] = [f.result() for f in concurrent.futures.as_completed(futures) if f.result()['is_account']]

            return_dic['stations_info'] = sorted(return_dic['stations_info'], key=lambda x: x['station_name'])

            return_dic['is_account'] = True if len(return_dic['stations_info']) else False

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": f"success", "detail": return_dic},
                }
            )


class ProjectAccountPriceView(APIView):
    """项目结算管理--查询: 电价明细"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_project_ele_price_range_data(self, station, start_time, end_time, lang='zh'):
        """
        """

        station_info = {}

        # 只时间在设置的使用范围内使用结算表
        meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
        if meter_use_time:
            if meter_use_time.end_time:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
            else:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time)
        else:
            is_use_account = False

        # 外接结算电表
        if is_use_account:

            station_info['is_account'] = True

            days_data = []

            # 电价需要判断是使用代理电价还是使用自定义小时电价
            # 判断是否存在自定义电价配置
            user_price = UnitPrice.objects.filter(station=station.master_station, start__gte=start_time.date(),
                                                  end__lte=end_time.date(), delete=0)
            if user_price.exists():
                user_price = user_price.last()

                station_info['title'] = user_price.name if lang == 'zh' else user_price.en_name

                # 遍历每一天计算当日收益
                target_day = start_time
                end_day_ = end_time

                while target_day <= end_day_:
                    hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}
                    price_dic = {
                        "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                        "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                        "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                        "valley_chag_price": float(
                            user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                        "dvalley_chag_price": float(
                            user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                        "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                        "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                        "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                        "valley_disg_price": float(
                            user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                        "dvalley_disg_price": float(
                            user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
                    }

                    temp_dict = {
                        "date": target_day.strftime('%Y-%m-%d'),

                        "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                                  "discharge_price": price_dic.get('spike_disg_price') or '--',
                                  "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                        "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                                 "discharge_price": price_dic.get('peak_disg_price') or '--',
                                 "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                        "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                                 "discharge_price": price_dic.get('flat_disg_price') or '--',
                                 "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                        "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                                   "discharge_price": price_dic.get('valley_disg_price') or '--',
                                   "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                        "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                        "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                        "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
                    }

                    days_data.append(temp_dict)

                    target_day += datetime.timedelta(days=1)

            else:
                # 拼接标题
                if lang == 'zh':
                    province = station.province.name
                    station_info['title'] = province + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + dict(PeakValleyNew.TYPE_CHOICE)[
                        station.type] + "代理购电价格"
                else:
                    province = station.province.en_name
                    title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                             f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

                price_configs = PeakValleyNew.objects.filter(province=station.province, type=station.type,
                                                             level=station.level)

                # 遍历每一天计算当日收益
                target_day = start_time
                end_day_ = end_time

                while target_day <= end_day_:

                    day_prices = price_configs.filter(year_month=target_day.strftime("%Y-%m")).all()

                    price_dic = {
                        "spike_chag_price": '--',
                        "peak_chag_price": '--',
                        "flat_chag_price": '--',
                        "valley_chag_price": '--',
                        "dvalley_chag_price": '--',
                        "spike_disg_price": '--',
                        "peak_disg_price": '--',
                        "flat_disg_price": '--',
                        "valley_disg_price": '--',
                        "dvalley_disg_price": '--'
                    }

                    hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}

                    if day_prices.exists():

                        # 尖
                        spike_prices = day_prices.filter(pv=2).all()
                        price_dic['spike_chag_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                        price_dic['spike_disg_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                        if spike_prices.exists():
                            hours_map[2] = [i.moment for i in spike_prices]

                        # 峰
                        peak_prices = day_prices.filter(pv=1).all()
                        price_dic['peak_chag_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                        price_dic['peak_disg_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                        if peak_prices.exists():
                            hours_map[1] = [i.moment for i in peak_prices]

                        # 平
                        flat_prices = day_prices.filter(pv=0).all()
                        price_dic['flat_chag_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                        price_dic['flat_disg_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                        if flat_prices.exists():
                            hours_map[0] = [i.moment for i in flat_prices]

                        # 谷
                        valley_prices = day_prices.filter(pv=-1).all()
                        price_dic['valley_chag_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                        price_dic['valley_disg_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                        if valley_prices.exists():
                            hours_map[-1] = [i.moment for i in valley_prices]

                        # 深谷
                        dvalley_prices = day_prices.filter(pv=-2).all()
                        price_dic['dvalley_chag_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                        price_dic['dvalley_disg_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                        if dvalley_prices.exists():
                            hours_map[-2] = [i.moment for i in dvalley_prices]

                        for type_, hour_list in hours_map.items():
                            hours_map[type_] = convert_to_time_ranges_v2(hour_list)

                    temp_dict = {
                        "date": target_day.strftime('%Y-%m-%d'),

                        "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                                "discharge_price": price_dic.get('spike_disg_price') or '--',
                                  "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                        "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                                 "discharge_price": price_dic.get('peak_disg_price') or '--',
                                 "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                        "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                                 "discharge_price": price_dic.get('flat_disg_price') or '--',
                                 "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                        "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                                   "discharge_price": price_dic.get('valley_disg_price') or '--',
                                   "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                        "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                        "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                        "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
                    }

                    days_data.append(temp_dict)

                    target_day += datetime.timedelta(days=1)

            station_info['array'] = days_data

        return station_info

    def get_project_ele_price_for_month_data(self, station, start_time, end_time, lang='zh'):
        """

        """

        station_info = {
            # 'station_name': master_station.station_name,
        }

        # start_time, end_time = get_month_start_end(year, month)

        price_configs = PeakValleyNew.objects.filter(province=station.province, type=station.type,
                                                     level=station.level)
        day_prices = price_configs.filter(year_month=start_time.strftime('%Y-%m')).all()

        # 只时间在设置的使用范围内使用结算表
        meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
        if meter_use_time:

            if meter_use_time.end_time:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
            else:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time)
        else:
            is_use_account = False

        # 外接结算电表
        if is_use_account:

            station_info['is_account'] = True

            # 电价需要判断是使用代理电价还是使用自定义小时电价
            # 判断是否存在自定义电价配置
            user_price = UnitPrice.objects.filter(station=station.master_station, start__gte=start_time.date(),
                                                  end__lte=end_time.date(), delete=0)
            if user_price.exists():
                user_price = user_price.last()

                station_info['title'] = user_price.name if lang == 'zh' else user_price.en_name

                # 查询尖峰平谷时间段
                hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}
                if day_prices.exists():

                    # 尖
                    spike_prices = day_prices.filter(pv=2).all()
                    if spike_prices.exists():
                        hours_map[2] = [i.moment for i in spike_prices]

                    # 峰
                    peak_prices = day_prices.filter(pv=1).all()
                    if peak_prices.exists():
                        hours_map[1] = [i.moment for i in peak_prices]

                    # 平
                    flat_prices = day_prices.filter(pv=0).all()
                    if flat_prices.exists():
                        hours_map[0] = [i.moment for i in flat_prices]

                    # 谷
                    valley_prices = day_prices.filter(pv=-1).all()
                    if valley_prices.exists():
                        hours_map[-1] = [i.moment for i in valley_prices]

                    # 深谷
                    dvalley_prices = day_prices.filter(pv=-2).all()
                    if dvalley_prices.exists():
                        hours_map[-2] = [i.moment for i in dvalley_prices]

                    for type_, hour_list in hours_map.items():
                        hours_map[type_] = convert_to_time_ranges_v2(hour_list)

                price_dic = {
                    "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                    "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                    "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                    "valley_chag_price": float(
                        user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                    "dvalley_chag_price": float(
                        user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                    "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                    "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                    "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                    "valley_disg_price": float(
                        user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                    "dvalley_disg_price": float(
                        user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
                }

                temp_dict = {
                    "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                              "discharge_price": price_dic.get('spike_disg_price') or '--',
                              "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                    "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                             "discharge_price": price_dic.get('peak_disg_price') or '--',
                             "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                    "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                             "discharge_price": price_dic.get('flat_disg_price') or '--',
                             "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                    "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                               "discharge_price": price_dic.get('valley_disg_price') or '--',
                               "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                    "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                    "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                    "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
                }

                station_info['info'] = temp_dict

            else:
                # price_configs = PeakValleyNew.objects.filter(province=station.province, type=station.type,
                #                                           level=station.level)
                #
                # day_prices = price_configs.filter(year_month=start_time.strftime('%Y-%m')).all()

                price_dic = {
                    "spike_chag_price": '--',
                    "peak_chag_price": '--',
                    "flat_chag_price": '--',
                    "valley_chag_price": '--',
                    "dvalley_chag_price": '--',
                    "spike_disg_price": '--',
                    "peak_disg_price": '--',
                    "flat_disg_price": '--',
                    "valley_disg_price": '--',
                    "dvalley_disg_price": '--'
                }

                hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}

                if day_prices.exists():

                    # 尖
                    spike_prices = day_prices.filter(pv=2).all()
                    price_dic['spike_chag_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                    price_dic['spike_disg_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                    if spike_prices.exists():
                        hours_map[2] = [i.moment for i in spike_prices]

                    # 峰
                    peak_prices = day_prices.filter(pv=1).all()
                    price_dic['peak_chag_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                    price_dic['peak_disg_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                    if peak_prices.exists():
                        hours_map[1] = [i.moment for i in peak_prices]

                    # 平
                    flat_prices = day_prices.filter(pv=0).all()
                    price_dic['flat_chag_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                    price_dic['flat_disg_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                    if flat_prices.exists():
                        hours_map[0] = [i.moment for i in flat_prices]

                    # 谷
                    valley_prices = day_prices.filter(pv=-1).all()
                    price_dic['valley_chag_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                    price_dic['valley_disg_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                    if valley_prices.exists():
                        hours_map[-1] = [i.moment for i in valley_prices]

                    # 深谷
                    dvalley_prices = day_prices.filter(pv=-2).all()
                    price_dic['dvalley_chag_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                    price_dic['dvalley_disg_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                    if dvalley_prices.exists():
                        hours_map[-2] = [i.moment for i in dvalley_prices]

                    for type_, hour_list in hours_map.items():
                        hours_map[type_] = convert_to_time_ranges_v2(hour_list)

                temp_dict = {

                    "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                              "discharge_price": price_dic.get('spike_disg_price') or '--',
                              "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                    "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                             "discharge_price": price_dic.get('peak_disg_price') or '--',
                             "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                    "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                             "discharge_price": price_dic.get('flat_disg_price') or '--',
                             "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                    "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                               "discharge_price": price_dic.get('valley_disg_price') or '--',
                               "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                    "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                    "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                    "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
                }

                station_info['info'] = temp_dict

                # 拼接标题
                if lang == 'zh':
                    province = station.province.name
                    station_info['title'] = province + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
                else:
                    province = station.province.en_name
                    title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                             f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

        return station_info

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        # user_id = request.user["user_id"]
        query_params = request.query_params
        project_id = query_params.get('project_id')
        start_day = query_params.get("start_day")
        end_day = query_params.get("end_day")

        if not all([project_id, start_day, end_day]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id/start_day/end_day 缺失!!!" if lang == 'zh' else "参数：project_id/start_day/end_day is missing!"},
                }
            )

        # 校验项目id
        user_id = request.user["user_id"]
        projects = Project.objects.filter(user=user_id, is_used=1).all()
        if int(project_id) not in [project.id for project in projects]:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 错误!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # 校验参数start_day和end_day
        try:
            start_day = start_day + " 00:00:00"
            end_day = end_day + " 23:59:59"
            start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：start_day/end_day 错误!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        if start_day > end_day:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：开始日期不能大于结束日期!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        return_dic = {}

        # 查询数据
        try:
            project = Project.objects.get(id=int(project_id), is_used=1)
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 查询报错!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # master_stations = project.materstation_set.filter(is_delete=0).all()
        # 结算电表迁移至各个从站，其中标准主从只显示从站，不显示000主站
        stations = project.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = list()
            for station in stations:
                future = executor.submit(self.get_project_ele_price_range_data, station, start_time, end_time, lang)
                futures.append(future)

            temp_list = [f.result() for f in concurrent.futures.as_completed(futures)]

        # return_dic['is_account'] = True if any([i['is_account'] for i in temp_list]) else False

        return_dic = temp_list[0]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": f"success", "detail": return_dic},
            }
        )

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        query_params = request.query_params
        project_id = query_params.get('project_id')
        start_day = query_params.get("start_day")
        end_day = query_params.get("end_day")
        month = query_params.get("month")
        station = query_params.get("master_station")

        if not all([project_id, start_day, end_day]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id/start_day/end_day 缺失!!!" if lang == 'zh' else "Parameter: project_id/start_day/end_day is missing!"},
                }
            )

        # 校验项目id
        user_id = request.user["user_id"]
        projects = Project.objects.filter(user=user_id, is_used=1).all()
        if int(project_id) not in [project.id for project in projects]:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 错误!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # 校验参数start_day和end_day
        try:
            start_day = start_day + " 00:00:00"
            end_day = end_day + " 23:59:59"
            start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：start_day/end_day 错误!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        if start_day > end_day:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：开始日期不能大于结束日期!!!" if lang == 'zh' else "Parameter: start_day/end_day Error."},
                }
            )

        # 校验month
        if month:
            if int(month) not in range(1, 13):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": f"参数：month 错误!!!!!!" if lang == 'zh' else "Parameter: month Error."},
                    })

        # 查询数据
        try:
            project = Project.objects.get(id=int(project_id), is_used=1)
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 查询报错!!!" if lang == 'zh' else "Parameter: project_id Error."},
                }
            )

        # master_station = project.materstation_set.filter(is_delete=0, name=master_station).first()
        station = project.stationdetails_set.filter(is_delete=0, station_name=station).first()

        try:
            month_ = int(month)
            # if start_time.month != end_time.month:
            #     start_time = start_time.replace(month=month_)
            #     end_time = end_time.replace(month=month_ + 1)
            # else:
            start_time = start_time.replace(month=month_, day=1)
            end_time = (start_time + datetime.timedelta(days=31)).replace(day=1) - datetime.timedelta(days=1)
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：month 错误!!!" if lang == 'zh' else "Parameter: month Error."},
                })

        return_dict = self.get_project_ele_price_for_month_data(station, start_time, end_time, lang)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": f"success", "detail": return_dict},
            }
        )


class ExportProjectAccountExcelView(APIView):
    """项目结算管理--导出excel"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_station_day_data(self, station, target_day, select_sql, user_price, lang='zh'):
        hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}

        if user_price:
            # 优先使用自定义电价
            title = user_price.name if lang == 'zh' else user_price.en_name

            price_dic = {
                "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
                "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
                "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
                "valley_chag_price": float(
                    user_price.valley_chag_price) if user_price.valley_chag_price else '--',
                "dvalley_chag_price": float(
                    user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
                "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
                "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
                "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
                "valley_disg_price": float(
                    user_price.valley_disg_price) if user_price.valley_disg_price else '--',
                "dvalley_disg_price": float(
                    user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
            }

            temp_dict_ = {
                "date": target_day.strftime('%Y-%m-%d'),
                "title": title,

                "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                          "discharge_price": price_dic.get('spike_disg_price') or '--',
                          "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                         "discharge_price": price_dic.get('peak_disg_price') or '--',
                         "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                         "discharge_price": price_dic.get('flat_disg_price') or '--',
                         "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                           "discharge_price": price_dic.get('valley_disg_price') or '--',
                           "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
            }

        else:
            # 拼接标题
            if lang == 'zh':
                title = station.province.name + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + \
                        dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
            else:
                province = station.province.en_name
                title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                         f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

            price_configs = PeakValleyNew.objects.filter(province=station.province, type=station.type,
                                                         level=station.level)

            day_prices = price_configs.filter(year_month=target_day.strftime("%Y-%m")).all()

            price_dic = {
                "spike_chag_price": '--',
                "peak_chag_price": '--',
                "flat_chag_price": '--',
                "valley_chag_price": '--',
                "dvalley_chag_price": '--',
                "spike_disg_price": '--',
                "peak_disg_price": '--',
                "flat_disg_price": '--',
                "valley_disg_price": '--',
                "dvalley_disg_price": '--'
            }

            if day_prices.exists():

                # 尖
                spike_prices = day_prices.filter(pv=2).all()
                price_dic[
                    'spike_chag_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                price_dic[
                    'spike_disg_price'] = spike_prices.first().price if spike_prices.exists() else '--'
                if spike_prices.exists():
                    hours_map[2] = [i.moment for i in spike_prices]

                # 峰
                peak_prices = day_prices.filter(pv=1).all()
                price_dic['peak_chag_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                price_dic['peak_disg_price'] = peak_prices.first().price if peak_prices.exists() else '--'
                if peak_prices.exists():
                    hours_map[1] = [i.moment for i in peak_prices]

                # 平
                flat_prices = day_prices.filter(pv=0).all()
                price_dic['flat_chag_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                price_dic['flat_disg_price'] = flat_prices.first().price if flat_prices.exists() else '--'
                if flat_prices.exists():
                    hours_map[0] = [i.moment for i in flat_prices]

                # 谷
                valley_prices = day_prices.filter(pv=-1).all()
                price_dic[
                    'valley_chag_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                price_dic[
                    'valley_disg_price'] = valley_prices.first().price if valley_prices.exists() else '--'
                if valley_prices.exists():
                    hours_map[-1] = [i.moment for i in valley_prices]

                # 深谷
                dvalley_prices = day_prices.filter(pv=-2).all()
                price_dic[
                    'dvalley_chag_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                price_dic[
                    'dvalley_disg_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
                if dvalley_prices.exists():
                    hours_map[-2] = [i.moment for i in dvalley_prices]

                for type_, hour_list in hours_map.items():
                    hours_map[type_] = convert_to_time_ranges_v2(hour_list)

            temp_dict_ = {
                "date": target_day.strftime('%Y-%m-%d'),
                "title": title,

                "spike": {"charge_price": price_dic.get('spike_chag_price') or '--',
                          "discharge_price": price_dic.get('spike_disg_price') or '--',
                          "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
                "peak": {"charge_price": price_dic.get('peak_chag_price') or '--',
                         "discharge_price": price_dic.get('peak_disg_price') or '--',
                         "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
                "flat": {"charge_price": price_dic.get('flat_chag_price') or '--',
                         "discharge_price": price_dic.get('flat_disg_price') or '--',
                         "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
                "valley": {"charge_price": price_dic.get('valley_chag_price') or '--',
                           "discharge_price": price_dic.get('valley_disg_price') or '--',
                           "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
                "dvalley": {"charge_price": price_dic.get('dvalley_chag_price') or '--',
                                "discharge_price": price_dic.get('dvalley_disg_price') or '--',
                                "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
            }

        # price_details.append(temp_dict_)

        # 查询每日电表数据
        day_spike_charge_values = []
        day_spike_discharge_values = []

        day_peak_charge_values = []
        day_peak_discharge_values = []

        day_flat_charge_values = []
        day_flat_discharge_values = []

        day_valley_charge_values = []
        day_valley_discharge_values = []

        day_dvalley_charge_values = []
        day_dvalley_discharge_values = []

        detail = ads_db_tool.select_one(select_sql, station.english_name,
                                        target_day.strftime('%Y-%m-%d'))

        # price_dict = get_station_price(station, target_day)

        if detail:
            day_spike_charge_values.append(float(detail['pointed_chag']))
            day_spike_discharge_values.append(float(detail['pointed_disg']))

            day_peak_charge_values.append(float(detail['peak_chag']))
            day_peak_discharge_values.append(float(detail['peak_disg']))

            day_flat_charge_values.append(float(detail['flat_chag']))
            day_flat_discharge_values.append(float(detail['flat_disg']))

            day_valley_charge_values.append(float(detail['valley_chag']))
            day_valley_discharge_values.append(float(detail['valley_disg']))
            # 暂时没有
            # day_dvalley_charge_values.append(float(detail['dvalley_chag']))
            # day_dvalley_discharge_values.append(float(detail['dvalley_disg']))

            # 收益改查： ads_report_ems_station_income_1d
            income_sql = ("SELECT * FROM ads_report_ems_station_income_1d"
                          " where station=%s and day=%s")
            income = ads_db_tool.select_one(income_sql, station.english_name, target_day.strftime('%Y-%m-%d'))
            if income:
                d_dict = {'spike_charge_income': -float(income['pointed_chag_income']) if income['pointed_chag_income'] is not None else '--',
                          'spike_discharge_income': float(income['pointed_disg_income']) if income['pointed_disg_income'] is not None else '--',
                          'spike_charge': sum(day_spike_charge_values), 'spike_discharge': sum(day_spike_discharge_values),
                          'peak_charge_income': -float(income['peak_chag_income']) if income['peak_chag_income'] is not None else '--',
                          'peak_discharge_income': float(income['peak_disg_income']) if income['peak_disg_income'] is not None else '--',
                          'peak_charge': sum(day_peak_charge_values), 'peak_discharge': sum(day_peak_discharge_values),
                          'flat_charge_income': -float(income['flat_chag_income']) if income['flat_chag_income'] is not None else '--',
                          'flat_discharge_income': float(income['flat_disg_income']) if income['flat_disg_income'] is not None else '--',
                          'flat_charge': sum(day_flat_charge_values), 'flat_discharge': sum(day_flat_discharge_values),
                          'valley_charge_income': -float(income['valley_chag_income']) if income['valley_chag_income'] is not None else '--',
                          'valley_discharge_income': float(income['valley_disg_income']) if income['valley_disg_income'] is not None else '--',
                          'valley_charge': sum(day_valley_charge_values),
                          'valley_discharge': sum(day_valley_discharge_values),

                          'dvalley_charge_income': -float(income['dvalley_chag_income']) if income[
                                                                                              'dvalley_chag_income'] is not None else '--',
                          'dvalley_discharge_income': float(income['dvalley_disg_income']) if income[
                                                                                                'dvalley_disg_income'] is not None else '--',
                          'dvalley_charge': sum(day_dvalley_charge_values),
                          'dvalley_discharge': sum(day_dvalley_discharge_values),

                          }
            else:
                d_dict = {'spike_charge_income': '--', 'spike_discharge_income': '--',
                          'spike_charge': sum(day_spike_charge_values),
                          'spike_discharge': sum(day_spike_discharge_values),

                          'peak_charge_income': '--', 'peak_discharge_income': '--',
                          'peak_charge': sum(day_peak_charge_values),
                          'peak_discharge': sum(day_peak_discharge_values),

                          'flat_charge_income': '--', 'flat_discharge_income': '--',
                          'flat_charge': sum(day_flat_charge_values),
                          'flat_discharge': sum(day_flat_discharge_values),

                          'valley_charge_income': '--', 'valley_discharge_income': '--',
                          'valley_charge': sum(day_valley_charge_values),
                          'valley_discharge': sum(day_valley_discharge_values),
                          'dvalley_charge_income': '--', 'dvalley_discharge_income': '--',
                          'dvalley_charge': sum(day_dvalley_charge_values),
                          'dvalley_discharge': sum(day_dvalley_discharge_values),
                          }

            # 每天的尖峰平谷充放电量统计
            day_spike_charge = -round(sum(day_spike_charge_values), 2)
            day_spike_discharge = round(sum(day_spike_discharge_values), 2)

            day_peak_charge = -round(sum(day_peak_charge_values), 2)
            day_peak_discharge = round(sum(day_peak_discharge_values), 2)

            day_flat_charge = -round(sum(day_flat_charge_values), 2)
            day_flat_discharge = round(sum(day_flat_discharge_values), 2)

            day_valley_charge = -round(sum(day_valley_charge_values), 2)
            day_valley_discharge = round(sum(day_valley_discharge_values), 2)

            # day_dvalley_charge = -round(sum(day_dvalley_charge_values), 2)
            # day_dvalley_discharge = round(sum(day_dvalley_discharge_values), 2)

            day_total_charge = round(day_spike_charge + day_peak_charge + day_flat_charge + day_valley_charge, 2)
            day_total_discharge = round(
                day_spike_discharge + day_peak_discharge + day_flat_discharge + day_valley_discharge, 2)

            temp_dict = {
                "date": target_day.strftime('%Y-%m-%d'),
                "day_spike_charge": day_spike_charge,
                "day_spike_discharge": day_spike_discharge,
                "day_peak_charge": day_peak_charge,
                "day_peak_discharge": day_peak_discharge,
                "day_flat_charge": day_flat_charge,
                "day_flat_discharge": day_flat_discharge,
                "day_valley_charge": day_valley_charge,
                "day_valley_discharge": day_valley_discharge,
                # "day_dvalley_charge": day_dvalley_charge,
                # "day_dvalley_discharge": day_dvalley_discharge,
                "day_dvalley_charge": 0,
                "day_dvalley_discharge": 0,
                "day_total_charge": day_total_charge,
                "day_total_discharge": day_total_discharge
            }

            # days_data.append(temp_dict)

            return temp_dict_, temp_dict, d_dict
        return None

    def get_station_all_data(self, station, start_time, end_time, lang='zh'):
        """
        结算单
        :param project_id:
        :param start_day:
        :param end_day:
        :return:
        """
        if station.master_station.mode == 1:
            args = ['TPAP', 'PATC', 'FAPC', 'PAPL', 'PAVC', 'TAPIR', 'RATC', 'RAPC', 'RAPL', 'RAVC', 'PT',
                    'CT']
        else:
            args = ['STPAP', 'SPATC', 'SFAPC', 'SPAPL', 'SPAVC', 'STAPIR', 'SRATC', 'SRAPC', 'SRAPL', 'SRAVC', 'SPT', 'SCT']

        if station.master_station.mode == 1:
            select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                          " where station=%s and device_type=0 and day=%s")
        else:
            select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                          " where station=%s and device_type=1 and day=%s")

        # select_sql = ("SELECT * FROM ads_report_chag_disg_union_1d"
        #               " where station=%s and station_type=0 and day=%s")

        station_info = {
            'station_name': station.station_name,
            'station_number': station.meter_number if station.meter_number else '--',
            'rate': '--',           # TODO 实时获取
            'is_account': False
            # 'account_data': {},
            # 'days_data': [],
            # 'price_details': []
        }

        # 只时间在设置的使用范围内使用结算表
        meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
        if meter_use_time:
            if meter_use_time.end_time:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
            else:
                is_use_account = (station.is_account and meter_use_time and
                                  meter_use_time.start_time <= start_time)
        else:
            is_use_account = False

        # 外接结算电表
        if is_use_account:

            station_info['is_account'] = True

            # 判断是否存在自定义电价配置
            user_price = UnitPrice.objects.filter(station=station.master_station, start__gte=start_time.date(),
                                                  end__lte=end_time.date(), delete=0)

            # 优先使用自定义电价
            if user_price.exists():
                user_price = user_price.last()

                title = user_price.name if lang == 'zh' else user_price.en_name

            # 使用代理购电价格
            else:
                if lang == 'zh':
                    # 拼接标题
                    title = station.province.name + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + \
                                            dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
                else:
                    province = station.province.en_name
                    title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                             f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

            # 时间范围内的结算数据
            account_data = {}

            # 每日的结算电量数据
            days_data = []

            # 每日的结算电价数据
            price_details = []

            # 查询抄表底码
            data = time_range_by_dwd_for_web(station.english_name, 'cumulant', 'ems', 'EMS', start_time,
                                             end_time, *args)

            if data:
                sorted_data = sorted(data, key=lambda x: x['time'])
                first_data = sorted_data[0]
                last_data = sorted_data[-1]
                if station.master_station.mode != 1:
                    first_SPATC = first_data['SPATC']
                    last_SPATC = last_data['SPATC']

                    first_SFAPC = first_data['SFAPC']
                    last_SFAPC = last_data['SFAPC']

                    first_SPAPL = first_data['SPAPL']
                    last_SPAPL = last_data['SPAPL']

                    first_SPAVC = first_data['SPAVC']
                    last_SPAVC = last_data['SPAVC']

                    first_SRATC = first_data['SRATC']
                    last_SRATC = last_data['SRATC']

                    first_SRAPC = first_data['SRAPC']
                    last_SRAPC = last_data['SRAPC']

                    first_SRAPL = first_data['SRAPL']
                    last_SRAPL = last_data['SRAPL']

                    first_SRAVC = first_data['SRAVC']
                    last_SRAVC = last_data['SRAVC']

                    first_SPT = first_data['SPT'] if first_data['SPT'] else '--'
                    first_SCT = first_data['SCT'] if first_data['SCT'] else '--'

                    last_SPT = last_data['SPT'] if last_data['SPT'] else '--'
                    last_SCT = last_data['SCT'] if last_data['SCT'] else '--'

                else:
                    first_SPATC = first_data['PATC']
                    last_SPATC = last_data['PATC']

                    first_SFAPC = first_data['FAPC']
                    last_SFAPC = last_data['FAPC']

                    first_SPAPL = first_data['PAPL']
                    last_SPAPL = last_data['PAPL']

                    first_SPAVC = first_data['PAVC']
                    last_SPAVC = last_data['PAVC']

                    first_SRATC = first_data['RATC']
                    last_SRATC = last_data['RATC']

                    first_SRAPC = first_data['RAPC']
                    last_SRAPC = last_data['RAPC']

                    first_SRAPL = first_data['RAPL']
                    last_SRAPL = last_data['RAPL']

                    first_SRAVC = first_data['RAVC']
                    last_SRAVC = last_data['RAVC']

                    first_SPT = first_data['PT'] if first_data['PT'] else '--'
                    first_SCT = first_data['CT'] if first_data['CT'] else '--'

                    last_SPT = last_data['PT'] if last_data['PT'] else '--'
                    last_SCT = last_data['CT'] if last_data['CT'] else '--'


                spike_charge_income = 0
                spike_discharge_income = 0
                spike_charge = 0
                spike_discharge = 0

                peak_charge_income = 0
                peak_discharge_income = 0
                peak_charge = 0
                peak_discharge = 0

                flat_charge_income = 0
                flat_discharge_income = 0
                flat_charge = 0
                flat_discharge = 0

                valley_charge_income = 0
                dvalley_charge_income = 0
                valley_discharge_income = 0
                dvalley_discharge_income = 0
                valley_charge = 0
                dvalley_charge = 0
                valley_discharge = 0
                dvalley_discharge = 0

                # 遍历每一天计算当日收益
                target_day = start_time
                end_day_ = end_time

                with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                    futures = list()

                    while target_day <= end_day_:
                        future = executor.submit(self.get_station_day_data, station, target_day, select_sql, user_price, lang)
                        futures.append(future)
                        target_day = target_day + datetime.timedelta(days=1)

                    for f in concurrent.futures.as_completed(futures):
                        if f.result():
                            temp_dict_, temp_dict, d_dict = f.result()
                            price_details.append(temp_dict_)
                            days_data.append(temp_dict)

                            spike_charge_income += d_dict['spike_charge_income']
                            spike_discharge_income += d_dict['spike_discharge_income']
                            spike_charge += d_dict['spike_charge']
                            spike_discharge += d_dict['spike_discharge']

                            peak_charge_income += d_dict['peak_charge_income']
                            peak_discharge_income += d_dict['peak_discharge_income']
                            peak_charge += d_dict['peak_charge']
                            peak_discharge += d_dict['peak_discharge']

                            flat_charge_income += d_dict['flat_charge_income']
                            flat_discharge_income += d_dict['flat_discharge_income']
                            flat_charge += d_dict['flat_charge']
                            flat_discharge += d_dict['flat_discharge']

                            valley_charge_income += d_dict['valley_charge_income']
                            valley_discharge_income += d_dict['valley_discharge_income']
                            valley_charge += d_dict['valley_charge']
                            valley_discharge += d_dict['valley_discharge']
                            dvalley_charge_income += d_dict['dvalley_charge_income']
                            dvalley_discharge_income += d_dict['dvalley_discharge_income']
                            dvalley_charge += d_dict['dvalley_charge']
                            dvalley_discharge += d_dict['dvalley_discharge']

                price_details = sorted(price_details, key=lambda x: x['date'])
                days_data = sorted(days_data, key=lambda x: x['date'])

                total_income = (round(spike_charge_income, 4) + round(peak_charge_income, 4) + round(flat_charge_income,
                                                                                                     4) +
                                round(valley_charge_income, 4) + round(dvalley_charge_income, 4) + round(spike_discharge_income, 4) + round(
                            peak_discharge_income, 4) +
                                round(flat_discharge_income, 4) + round(valley_discharge_income, 4) + round(dvalley_discharge_income, 4))

            # 无底表数据时，显示--
            else:
                first_SPATC = '--'
                last_SPATC = '--'

                first_SFAPC = '--'
                last_SFAPC = '--'

                first_SPAPL = '--'
                last_SPAPL = '--'

                first_SPAVC = '--'
                last_SPAVC = '--'

                first_SRATC = '--'
                last_SRATC = '--'

                first_SRAPC = '--'
                last_SRAPC = '--'

                first_SRAPL = '--'
                last_SRAPL = '--'

                first_SRAVC = '--'
                last_SRAVC = '--'

                first_SPT = '--'
                first_SCT = '--'

                last_SPT = '--'
                last_SCT = '--'

                spike_charge_income = '--'
                spike_discharge_income = '--'
                spike_charge = '--'
                spike_discharge = '--'

                peak_charge_income = '--'
                peak_discharge_income = '--'
                peak_charge = '--'
                peak_discharge = '--'

                flat_charge_income = '--'
                flat_discharge_income = '--'
                flat_charge = '--'
                flat_discharge = '--'

                valley_charge_income = '--'
                dvalley_charge_income = '--'
                valley_discharge_income = '--'
                dvalley_discharge_income = '--'
                valley_charge = '--'
                dvalley_charge = '--'
                valley_discharge = '--'
                dvalley_discharge = '--'

                total_income = '--'

            if not '--' in [first_SPT, first_SCT, last_SPT, last_SCT]:
                station_info['rate'] = round((float(first_SPT) * float(first_SCT) + float(last_SPT) * float(last_SCT)) / 2, 2)
            elif '--' in [first_SPT, first_SCT] and not '--' in [last_SPT, last_SCT]:
                station_info['rate'] = round(float(last_SPT) * float(last_SCT), 2)
            elif '--' in [last_SPT, last_SCT] and not '--' in [first_SPT, first_SCT]:
                station_info['rate'] = round(float(first_SPT) * float(first_SCT), 2)
            else:
                station_info['rate'] = '--'

            account_data['charge'] = {
                'spike': {
                    'first': first_SRATC,
                    'last': last_SRATC,
                    'count': round(spike_charge, 2) if spike_charge != '--' else '--',
                    'income': round(spike_charge_income, 2) if spike_charge_income != '--' else '--',
                },
                'peak': {
                    'first': first_SRAPC,
                    'last': last_SRAPC,
                    'count': round(peak_charge, 2) if peak_charge != '--' else '--',
                    'income': round(peak_charge_income, 2) if spike_charge_income != '--' else '--'
                },
                'flat': {
                    'first': first_SRAPL,
                    'last': last_SRAPL,
                    'count': round(flat_charge, 2) if flat_charge != '--' else '--',
                    'income': round(flat_charge_income, 2) if spike_charge_income != '--' else '--'
                },
                'valley': {
                    'first': first_SRAVC,
                    'last': last_SRAVC,
                    'count': round(valley_charge, 2) if valley_charge != '--' else '--',
                    'income': round(valley_charge_income, 2) if spike_charge_income != '--' else '--'
                },
                'dvalley': {
                    'first': '--',
                    'last': '--',
                    'count': round(dvalley_charge, 2) if dvalley_charge != '--' else '--',
                    'income': round(dvalley_charge_income, 2) if spike_charge_income != '--' else '--'
                }
            }
            account_data['discharge'] = {
                'spike': {
                    'first': first_SPATC,
                    'last': last_SPATC,
                    'count': round(spike_discharge, 2) if spike_discharge != '--' else '--',
                    'income': round(spike_discharge_income, 2) if spike_charge_income != '--' else '--'
                },
                'peak': {
                    'first': first_SFAPC,
                    'last': last_SFAPC,
                    'count': round(peak_discharge, 2) if peak_discharge != '--' else '--',
                    'income': round(peak_discharge_income, 2) if spike_charge_income != '--' else '--'
                },
                'flat': {
                    'first': first_SPAPL,
                    'last': last_SPAPL,
                    'count': round(flat_discharge, 2) if flat_discharge != '--' else '--',
                    'income': round(flat_discharge_income, 2) if spike_charge_income != '--' else '--'
                },
                'valley': {
                    'first': first_SPAVC,
                    'last': last_SPAVC,
                    'count': round(valley_discharge, 2) if valley_discharge != '--' else '--',
                    'income': round(valley_discharge_income, 2) if spike_charge_income != '--' else '--'
                },
                'dvalley': {
                    'first': '--',
                    'last': '--',
                    'count': round(dvalley_discharge, 2) if dvalley_discharge != '--' else '--',
                    'income': round(dvalley_discharge_income, 2) if spike_charge_income != '--' else '--'
                }
            }
            account_data['total_income'] = round(total_income, 2) if spike_charge_income != '--' else '--'
            account_data['total_income_'] = round(total_income, 4) if spike_charge_income != '--' else '--'
            account_data['price_url'] = title

            station_info['account_data'] = account_data
            station_info['days_data'] = days_data
            station_info['price_details'] = price_details

        return station_info

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        query_params = request.query_params
        project_id = query_params.get('project_id')
        start_day = query_params.get("start_day")
        end_day = query_params.get("end_day")
        type_ = query_params.get("type", default="excel")

        if not all([project_id, start_day, end_day]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id/start_day/end_day 缺失!!!" if lang == 'zh' else "project_id/start_day/end_day field is missing!!!"},
                }
            )

        # 校验项目id
        user_id = request.user["user_id"]
        projects = Project.objects.filter(user=user_id, is_used=1).all()
        if int(project_id) not in [project.id for project in projects]:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 错误!!!" if lang == 'zh' else "project_id field is error!"},
                }
            )

        # 校验参数start_day和end_day
        try:
            start_day = start_day + " 00:00:00"
            end_day = end_day + " 23:59:59"
            start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：start_day/end_day 错误!!!" if lang == 'zh' else "start_day/end_day field is error!"},
                }
            )

        if start_day > end_day:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：开始日期不能大于结束日期!!!" if lang == 'zh' else "start_day/end_day field is error!"},
                }
            )

        if type_.lower() not in ['excel', 'pdf']:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：type 错误!!!" if lang == 'zh' else 'Parameter: type is incorrect.'},
                })

        user_id = request.user["user_id"]

        #　优先从 redis 中取缓存数据
        redis_conn = get_redis_connection("default")
        # 先从redis获取数据
        try:
            if type_.lower() == 'excel':
                key = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime(
                    "%Y%m%d") + '_' + end_time.strftime(
                    "%Y%m%d") + '_excel'

            else:
                key = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime(
                    "%Y%m%d") + '_' + end_time.strftime(
                    "%Y%m%d") + '_pdf'

            export_url = redis_conn.get(key)

            if export_url:
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {"message": f"success", "detail": export_url},
                    }
                )

        except Exception as e:
            print(traceback.print_exc())
            pass

        # 查询数据
        try:
            project = Project.objects.get(id=int(project_id), is_used=1)
        except Exception as e:
            logging.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": f"参数：project_id 查询报错!!!" if lang == 'zh' else 'Parameter: project_id An error is reported for the query.'},
                }
            )

        # 结算电表迁移至各个从站，其中标准主从只显示从站，不显示000主站
        stations = project.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()

        # 查询地址
        address = ''
        if stations.exists():
            address = stations.first().address if lang == 'zh' else stations.first().en_address

        return_dic = {}

        return_dic['address'] = address
        return_dic['project'] = project.name
        return_dic['account_month'] = end_time.strftime("%Y年%m月") if lang == 'zh' else end_time.strftime("%Y-%m")
        return_dic['account_start'] = start_day
        return_dic['account_end'] = end_day
        return_dic['station_number'] = '/'
        return_dic['stations_info'] = dict(account_info=[], detaiils_info=[], price_info={})

        temp_dict = {
            'STPAP': '当前正向有功总电量',
            'SPATC': '当前正向有功尖电量',
            'SFAPC': '当前正向有功峰电量',
            'SPAPL': '当前正向有功平电量',
            'SPAVC': '当前正向有功谷电量',
            'STAPIR': '当前反向有功总电量',
            'SRATC': '当前反向有功尖电量',
            'SRAPC': '当前反向有功峰电量',
            'SRAPL': '当前反向有功平电量',
            'SRAVC': '当前反向有功谷电量'
        }

        # 查询结算数据
        # with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
        #     futures = list()
        #     for station in stations:
        #         future = executor.submit(self.get_station_all_data, station, start_time, end_time)
        #         futures.append(future)
        #
        #     temp_list = [f.result() for f in concurrent.futures.as_completed(futures) if f.result()['is_account']]

        temp_list = [self.get_station_all_data(station, start_time, end_time, lang) for station in stations if
                     self.get_station_all_data(station, start_time, end_time, lang)['is_account']]

        return_dic['is_account'] = True if len(temp_list) else False

        if return_dic['is_account']:
            # 储能电站削峰填谷总收益
            return_dic['project_total_income'] = round(sum([i['account_data']['total_income_'] for i in temp_list if i['account_data']['total_income_'] != '--']), 2)

            # 电价信息只保留1份，避免重复，同一个项目的所有站点，电价信息一致====> 考虑到多个电站时，有的使用代理电价，有的使用自定义小时电价，所以相同时保留一份，不同时均保留
            # return_dic['price_infos'] = [item['price_details'] for item in temp_list if item['price_details'] not in return_dic['price_infos']]

            return_dic['price_infos'] = []
            temp_list_ = []
            for item in temp_list:
                if item['account_data']['price_url'] not in temp_list_ and item['price_details']:
                    return_dic['price_infos'].append(deepcopy(item['price_details']))
                    temp_list_.append(item['account_data']['price_url'])

                item.pop('price_details')

            return_dic['stations_info'] = sorted(temp_list, key=lambda x: x['station_name'])

            # return_dic['title'] = return_dic['stations_info'][0]['account_data']['price_url']

            if lang == 'zh':
                return_dic['note'] = ("说明:\n"
                                      "1. 各峰谷标志的充放电量来自于并网点结算电表数据。\n"
                                      "2. 结算电量 = 电表倍率*（本次报表底码 - 上次抄表底码）。\n"
                                      "3. 结算金额 = ∑(储能系统在各峰谷时段并网点结算电表充放电电量 ×对应时段单位电价)。\n"
                                      "4. 削峰填谷收益 = 正向有功（放电）结算金额-反向有功（充电）结算金额。")
            else:
                return_dic['note'] = ("Instruction:\n"
                                      "1. Energy data is measured from settlement meters.\n"
                                      "2. Settlement Energy = Multiplier×(Current Meter Reading - Last Meter Reading).\n"
                                      "3. Settlement Amount = ∑（Price by each hour × Energy by each hour）.\n"
                                      "4. Peak Shaving Profit = Amount by discharging -Amount by charging.")

            # return_dic['note'] = "削峰填谷收益"

            # 生成excel
            excel_path = os.path.join(settings.BASE_DIR, 'static/excel')
            if lang == 'zh':
                excel_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.xlsx"
                pdf_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.pdf"
                doc_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.doc"
            else:
                excel_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.xlsx"
                pdf_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.pdf"
                doc_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.doc"
            if not os.path.exists(excel_path):
                os.mkdir(excel_path)
            excel_file = os.path.join(excel_path, excel_file_name)
            pdf_file = os.path.join(excel_path, pdf_file_name)

            # 上传Minio
            minio_client = MinioTool()
            minio_client.create_bucket('download')

            if type_.lower() == 'excel':
                generate_excel(return_dic, excel_file, lang)
                export_url = minio_client.upload_local_file(excel_file_name, excel_file, 'download')
                os.remove(excel_file)
                # export_url = f"http://47.92.91.184:19190/static/excel/{excel_file_name}"

                key_excel = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime(
                    "%Y%m%d") + '_' + end_time.strftime("%Y%m%d") + '_excel'
                redis_conn.set(key_excel, export_url, 60 * 5)

            else:
                gen_pdf(return_dic, excel_path, doc_file_name, pdf_file_name, lang)
                export_url = minio_client.upload_local_file(pdf_file_name, pdf_file, 'download')
                os.remove(pdf_file)

                key_pdf = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime(
                    "%Y%m%d") + '_' + end_time.strftime("%Y%m%d") + '_pdf'
                redis_conn.set(key_pdf, export_url, 60 * 5)

                # export_url = f"http://47.92.91.184:19190/static/excel/{pdf_file_name}"

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": f"success", "detail": export_url},
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": f"no data", "detail": f"{project.name}并未安装外接结算电表，无法计算并导出结算电量计收益结算数据！" if lang == 'zh' else "The project has no external settlement meter, and the settlement energy profit cannot be calculated and exported!"},
                }
            )