#!/usr/bin/env python
# coding=utf-8
#@Information: SOP管理
#<AUTHOR> WYJ
#@Date         : 2023-06-21 08:26:23
#@FilePath     : \RHBESS_Service\Application\EqAccount\WorkOrder\spareManager copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-21 08:26:25

import os,uuid
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.WorkOrder.sop_base_info import SopBaseInfo
from Application.Models.WorkOrder.sop_label_info import SopLabelInfo
from Application.Models.WorkOrder.sop_file_info import SopFileInfo
import logging,json
from sqlalchemy import func,distinct
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import Translate_cls
import pandas as pd


class SOPManagerIntetface(BaseHandler):
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
            if kt == 'GetSOPBaseInfos':  # SOP查询
                name = self.get_argument('name',None)  # sop名
                sn = self.get_argument('sn',None) #设备编号
                author = self.get_argument('author',None)   # 作者
                organization_name = self.get_argument('organization_name',None)   # 组织
                device = self.get_argument('device',None)   # 目标设备
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('name:%s,sn:%s,author:%s,organization_name:%s,pageNum:%s,pageSize:%s,device:%s'%(name,sn,author,organization_name,pageNum,pageSize,device))
                filter,data = [SopBaseInfo.is_use==1],[]
                if lang == 'zh':
                    if name:
                        filter.append(SopBaseInfo.name.like(('%' + name + '%')))
                    if sn:
                        filter.append(SopBaseInfo.sn.like(('%' + sn + '%')))
                    if author:
                        filter.append(SopBaseInfo.author.like(('%' + author + '%')))
                    if organization_name:
                        filter.append(SopBaseInfo.organization_name.like(('%' + organization_name + '%')))
                    if device:
                        filter.append(SopBaseInfo.device.like(('%' + device + '%')))
                else:
                    if name:
                        filter.append(SopBaseInfo.en_name.like(('%' + name + '%')))
                    if sn:
                        filter.append(SopBaseInfo.en_sn.like(('%' + sn + '%')))
                    if author:
                        filter.append(SopBaseInfo.en_author.like(('%' + author + '%')))
                    if organization_name:
                        filter.append(SopBaseInfo.en_organization_name.like(('%' + organization_name + '%')))
                    if device:
                        filter.append(SopBaseInfo.en_device.like(('%' + device + '%')))
                total = user_session.query(func.count(SopBaseInfo.id)).filter(*filter).scalar()
                pages = user_session.query(SopBaseInfo).filter(*filter).order_by(SopBaseInfo.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                if lang == 'en':
                    for pag in pages:
                        p = eval(str(pag))
                        p['remarks'] = pag.en_remarks
                        p['name'] = pag.en_name
                        p['sn'] = pag.en_sn
                        p['author'] = pag.en_author
                        p['organization_name'] = pag.en_organization_name
                        p['device'] = pag.en_device
                        p['check_user'] = pag.en_check_user
                        data.append(p)
                else:
                    for pag in pages:
                        p = eval(str(pag))
                        p['remarks']=pag.remarks
                        data.append(p)
                return self.returnTotalSuccess(data,total)
            if kt == 'GetSOPBaseInfoById':  # 根据id查询
                datas = []
                id = self.get_argument('id',None)  # 信息id
                pages = user_session.query(SopBaseInfo).filter(SopBaseInfo.is_use==1,SopBaseInfo.id==id).first()
                if not pages:
                    return self.customError('无效id')
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'

                p = eval(str(pages))

                if lang == 'en':
                    p['remarks'] = pages.en_remarks
                    p['name'] = pages.en_name
                    p['sn'] = pages.en_sn
                    p['author'] = pages.en_author
                    p['organization_name'] = pages.en_organization_name
                    p['device'] = pages.en_device
                    p['check_user'] = pages.en_check_user
                    datas.append(p)
                    lb_names = user_session.query(distinct(SopLabelInfo.label_name), SopLabelInfo.en_label_name).filter(SopLabelInfo.is_use == 1,
                                                                                            SopLabelInfo.base_info_id == id).all()
                    for lb_name in lb_names:
                        lb_info = user_session.query(SopLabelInfo).filter(SopLabelInfo.is_use == 1,
                                                                          SopLabelInfo.label_name == lb_name[0],
                                                                          SopLabelInfo.base_info_id == id).all()
                        d = []
                        for lb in lb_info:
                            if lb.en_datainfo:
                                lb.en_datainfo = lb.en_datainfo.replace('\'','\"')
                            if lb.datainfo:
                                lb.datainfo = lb.datainfo.replace('\'','\"')
                            l = eval(str(lb))
                            l['step'] = eval(lb.en_datainfo)
                            l['name'] = l['en_step_name']
                            lb_fil = user_session.query(SopFileInfo).filter(SopFileInfo.is_use == 1,
                                                                            SopFileInfo.label_info_id == lb.id).all()
                            fil = []
                            for fi in lb_fil:
                                fil.append(eval(str(fi)))
                            l['file'] = fil
                            d.append(l)
                        datas.append({"name": lb_name[1], "data": d})
                else:
                    p['remarks']=pages.remarks
                    datas.append(p)
                    lb_names = user_session.query(distinct(SopLabelInfo.label_name)).filter(SopLabelInfo.is_use==1,SopLabelInfo.base_info_id==id).all()
                    for lb_name in lb_names:
                        lb_info = user_session.query(SopLabelInfo).filter(SopLabelInfo.is_use==1,SopLabelInfo.label_name==lb_name[0],SopLabelInfo.base_info_id==id).all()
                        d = []
                        for lb in lb_info:
                            if lb.en_datainfo:
                                lb.en_datainfo = lb.en_datainfo.replace('\'','\"')
                            if lb.datainfo:
                                lb.datainfo = lb.datainfo.replace('\'','\"')
                            l = eval(str(lb))
                            l['step']=eval(lb.datainfo)
                            lb_fil = user_session.query(SopFileInfo).filter(SopFileInfo.is_use==1,SopFileInfo.label_info_id==lb.id).all()
                            fil = []
                            for fi in lb_fil:
                                fil.append(eval(str(fi)))
                            l['file']=fil
                            d.append(l)
                        datas.append({"name":lb_name[0],"data":d})

                return self.returnTypeSuc({"data":datas})
        except Exception as E:
            user_session.close()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'AddSOPInfos':  # 添加备件
                station = self.get_argument('db',None)
                data = json.loads(self.get_argument('data',[{}]))  # 所有数据集合
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('data:%s,'%(data))

                files = self.request.files  # 所有文件信息集合
                # logging.info('Add----files is -----%s'%files)
                files_infos_list = files['files'] if files else []
                baseinfos = data[0]  # 第一个元素为基本信息
                keys = baseinfos.keys()
                if 'name' not in keys or not baseinfos['name'] or 'sn' not in keys or not baseinfos['sn'] or 'version' not in keys or not baseinfos['version'] or \
                'author' not in keys or not baseinfos['author'] or 'organization_name' not in keys or not baseinfos['organization_name'] or \
                'pub_time' not in keys or not baseinfos['pub_time'] or 'device' not in keys or not baseinfos['device']:
                    return self.customError('参数不完整') if lang == 'zh' else self.customError("Incomplete parameters")
                page = user_session.query(SopBaseInfo).filter(SopBaseInfo.name==baseinfos['name'],SopBaseInfo.sn==baseinfos['sn'],SopBaseInfo.station==station,SopBaseInfo.is_use==1).first()
                if page:  # 重复
                    logging.info("add data is in db")
                    return self.customError('数据已存在') if lang == 'zh' else self.customError("Data already exists")

                check_user = baseinfos['check_user'] if 'check_user' in keys else ''  # 审核人员
                job_length = baseinfos['job_length'] if 'job_length' in keys else ''  # 作业时长
                remarks = baseinfos['remarks'] if 'remarks' in keys else ''  # 备注
                nt_time = timeUtils.getNewTimeStr()

                name = baseinfos['name']
                sn = baseinfos['sn']
                author = baseinfos['author']
                organization_name = baseinfos['organization_name']
                device = baseinfos['device']


                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                name_res = t_cls.str_chinese(name)
                sn_res = t_cls.str_chinese(sn)
                author_res = t_cls.str_chinese(author)
                organization_name_res = t_cls.str_chinese(organization_name)
                device_res = t_cls.str_chinese(device)
                check_user_res = t_cls.str_chinese(check_user)
                remarks_res = t_cls.str_chinese(remarks)

                if ty == 2:
                    en_name = name_res
                    en_sn = sn_res
                    en_author = author_res
                    en_organization_name = organization_name_res
                    en_device = device_res
                    en_check_user = check_user_res
                    en_remarks = remarks_res
                else:
                    en_name = name
                    name = name_res
                    en_sn = sn
                    sn = sn_res
                    en_author = author
                    author = author_res
                    en_organization_name = organization_name
                    organization_name = organization_name_res
                    en_device = device
                    device = device_res
                    en_check_user = check_user
                    check_user = check_user_res
                    en_remarks = remarks
                    remarks = remarks_res

                baseinfo = SopBaseInfo(name=name,sn=sn,version=baseinfos['version'],author=author,pub_time=baseinfos['pub_time'],
                                       organization_name=organization_name,device=device,check_user=check_user,job_length=job_length,
                                       remarks=remarks,station=station,op_ts=nt_time,en_remarks=en_remarks,en_author=en_author,en_sn=en_sn,
                                       en_name=en_name,en_organization_name=en_organization_name,en_check_user=en_check_user,en_device=en_device)
                user_session.add(baseinfo)
                user_session.commit()
                for i in range(1,len(data)):
                    obj = data[i]  # 对应标签内容
                    lb_name = obj['name']
                    lb_data = obj['data']

                    lb_name_res = t_cls.str_chinese(lb_name)
                    if ty == 2:
                        en_lb_name = lb_name_res
                    else:
                        en_lb_name= lb_name
                        lb_name = lb_name_res

                    for lab in lb_data:
                        step_name = lab['name']
                        datainfo = json.dumps(lab['step'])
                        step_name_res = t_cls.str_chinese(step_name)
                        datainfo_res = t_cls.str_chinese(str(eval(datainfo)))

                        if ty == 2:
                            en_step_name = step_name_res
                            en_datainfo = datainfo_res
                        else:
                            en_step_name = step_name
                            step_name = step_name_res
                            en_datainfo = datainfo
                            datainfo = datainfo_res

                        file_info_arr = lab['file']  # [{"uid":1688020576182,"key":0},{"uid":1688020579380,"key":1}]
                        lb= SopLabelInfo(label_name=lb_name,en_label_name=en_lb_name,step_name=step_name,en_step_name=en_step_name,datainfo=datainfo,en_datainfo=en_datainfo,op_ts=nt_time,base_info_id=baseinfo.id)
                        user_session.add(lb)
                        user_session.commit()
                        self._savefile(file_info_arr,files_infos_list,lb.id,timeUtils.getNewTimeStr())
                       

                return self.returnTypeSuc('')
            elif kt == 'UpdateSOPInfos':  # 修改SOP信息
                station = self.get_argument('db',None)
                data = json.loads(self.get_argument('data',[{}]))  # 所有数据集合
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('data:%s,'%(data))
                files = self.request.files  # 所有文件信息集合
                files_infos_list = files['files'] if files else []
                # files_infos_list = files['files']
                baseinfos = data[0]  # 第一个元素为基本信息

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)

                page = user_session.query(SopBaseInfo).filter(SopBaseInfo.id==baseinfos['id']).first()
                if not page:  #
                    return self.customError('无效id') if lang == 'zh' else self.customError("Invalid ID")
                # 获取当前label_info的id
                label_info = user_session.query(SopLabelInfo).filter(SopLabelInfo.base_info_id==baseinfos['id'], SopLabelInfo.is_use==1).all()
                now_label_info_ids = [i.id for i in label_info] if label_info else []
                up_label_info_ids = []  # 更新数据中的label_id
                if baseinfos['name']:
                    name = baseinfos['name']
                    name_res = t_cls.str_chinese(name)
                    if ty == 2:
                        page.name = name
                        page.en_name = name_res
                    else:
                        page.name = name_res
                        page.en_name = name
                if baseinfos['sn']:
                    sn = baseinfos['sn']
                    sn_res = t_cls.str_chinese(sn)
                    if ty == 2:
                        page.sn = sn
                        page.en_sn = sn_res
                    else:
                        page.sn = sn_res
                        page.en_sn = sn
                if baseinfos['version']:
                    page.version=baseinfos['version']
                if baseinfos['author']:
                    author = baseinfos['author']
                    author_res = t_cls.str_chinese(author)
                    if ty == 2:
                        page.author = author
                        page.en_author = author_res
                    else:
                        page.author = author_res
                        page.en_author = author
                if baseinfos['pub_time']:
                    page.pub_time=baseinfos['pub_time']
                if baseinfos['organization_name']:
                    organization_name = baseinfos['organization_name']
                    organization_name_res = t_cls.str_chinese(organization_name)
                    if ty == 2:
                        page.organization_name = organization_name
                        page.en_organization_name = organization_name_res
                    else:
                        page.organization_name = organization_name_res
                        page.en_organization_name = organization_name
                if baseinfos['device']:
                    device = baseinfos['device']
                    device_res = t_cls.str_chinese(device)
                    if ty == 2:
                        page.device = device
                        page.en_device = device_res
                    else:
                        page.device = device_res
                        page.en_device = device
                if baseinfos['check_user']:
                    check_user = baseinfos['check_user']
                    check_user_res = t_cls.str_chinese(check_user)
                    if ty == 2:
                        page.check_user = check_user
                        page.en_check_user = check_user_res
                    else:
                        page.check_user = check_user_res
                        page.en_check_user = check_user
                if baseinfos['job_length']:
                    page.job_length=baseinfos['job_length']
                if baseinfos['remarks']:
                    remarks = baseinfos['remarks']
                    remarks_res = t_cls.str_chinese(remarks)
                    if ty == 2:
                        page.remarks = remarks
                        page.en_remarks = remarks_res
                    else:
                        page.remarks = remarks_res
                        page.en_remarks = remarks
                user_session.commit()


                for i in range(1,len(data)):
                    obj = data[i]  # 对应标签内容
                    lb_name = obj['name']
                    if lang == 'en':
                        lab_infos = user_session.query(SopLabelInfo.id).filter(SopLabelInfo.en_label_name == lb_name,
                                                                               SopLabelInfo.is_use == 1,
                                                                               SopLabelInfo.base_info_id == baseinfos[
                                                                                   'id']).all()
                    else:
                        lab_infos = user_session.query(SopLabelInfo.id).filter(SopLabelInfo.label_name==lb_name,SopLabelInfo.is_use==1,SopLabelInfo.base_info_id==baseinfos['id']).all()
                    lb_name_res = t_cls.str_chinese(lb_name)
                    if ty == 2:
                        en_lb_name = lb_name_res
                    else:
                        en_lb_name = lb_name
                        lb_name = lb_name_res
                    lb_data = obj['data']
                    lab_ids = []
                    for lab_i in lab_infos:  # 当前SOP当前tab的所有id集合
                        lab_ids.append(lab_i[0])
                    for lab in lb_data:
                        if "id" in lab.keys():
                            up_label_info_ids.append(lab['id'])
                        file_info_arr = lab['file']
                        lab_id = lab['id'] if 'id' in lab.keys() else None

                        name = lab['name']
                        datainfo = json.dumps(lab['step'])
                        name_res = t_cls.str_chinese(name)

                        if ty == 2:
                            en_name = name_res
                            en_datainfo = t_cls.str_chinese(str(eval(datainfo)))
                        else:
                            datainfo_res = t_cls.str_chinese(datainfo)
                            en_name = name
                            name = name_res
                            en_datainfo = datainfo
                            datainfo = datainfo_res

                        if lab_id:  # 修改
                            if int(lab_id) in lab_ids:
                                lab_ids.remove(int(lab_id))   # 删除元素
                            lab_info = user_session.query(SopLabelInfo).filter(SopLabelInfo.id==lab_id).first()
                            lab_info.name = name
                            lab_info.en_step_name = en_name
                            lab_info.datainfo = datainfo
                            lab_info.en_datainfo = en_datainfo
                            lab_info.label_name = lb_name
                            lab_info.en_label_name = en_lb_name
                            # 修改对应的文件信息
                            sop_file_id_arr = [] # 文件id
                            sop_file_infos = user_session.query(SopFileInfo.id).filter(SopFileInfo.label_info_id==lab_id,SopFileInfo.is_use==1).all()  # 获取lab下所有文件信息
                            for sop_file_id in sop_file_infos:
                                sop_file_id_arr.append(sop_file_id[0])
                            for file_info_ in file_info_arr:
                                uid = str(file_info_['uid'])
                                if uid in sop_file_id_arr:
                                    sop_file_id_arr.remove(uid)
                                if 'name' not in file_info_.keys():  # 添加
                                    self._savefile([file_info_],files_infos_list,lab_info.id,timeUtils.getNewTimeStr())
                                user_session.query(SopFileInfo).filter(SopFileInfo.id.in_(sop_file_id_arr)).update({"is_use":'0'})  # 删除已删除数据
                            user_session.commit()
                        else:  # 新增
                            lb= SopLabelInfo(label_name=lb_name,en_label_name=en_lb_name,step_name=name,en_step_name=en_name,datainfo=datainfo,en_datainfo=en_datainfo, op_ts=timeUtils.getNewTimeStr(),base_info_id=page.id)
                            user_session.add(lb)
                            user_session.commit()
                            self._savefile(file_info_arr,files_infos_list,lb.id,timeUtils.getNewTimeStr())

                    user_session.query(SopLabelInfo).filter(SopLabelInfo.id.in_(lab_ids)).update({"is_use":'0'})  # 删除已删除数据
                    user_session.commit()

                set1 = set(up_label_info_ids)
                set2 = set(now_label_info_ids)

                # 计算差集
                difference_list = list(set2 - set1)
                if len(difference_list)>0:
                    user_session.query(SopLabelInfo).filter(SopLabelInfo.id.in_(difference_list)).update(
                        {"is_use": '0'})  # 删除已删除数据
                    user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'DeleteSOPInfos':  # 删除
                id = self.get_argument('id',None)
                page = user_session.query(SopBaseInfo).filter(SopBaseInfo.id==id).first()
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            # elif kt == 'AddSOPInfosTest':  # 添加备件测试接口
            #     data = self.get_argument('data','')  # 所有数据集合
            #     files = self.get_argument('files','')  # 所有数据集合
            #     file__ = self.request.files  # 所有文件信息集合
            #     logging.info('AddSOPInfosTest==============data:%s,files:%s'%(data,files))
            #     logging.info('request.files---------------%s'%file__)
            #     body_args = self.get_body_arguments("files")
            #     logging.info('body_args---------------%s'%body_args)


            else:
                return self.pathError()

            
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
        # user_session.close()
       
    def _savefile(self,file_info_arr,files,lb_id,nt_time):
        '''
        添加文件信息
        '''
        # file_info_arr [{"uid":1688020576182,"key":0},{"uid":1688020579380,"key":1}]
        file_path = '/home/<USER>/workorderfiles' + timeUtils.getNewTimeStr()[:4]
        if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
            os.makedirs(file_path)
        for fi in file_info_arr:
            imgs = files[fi['key']]
            data = imgs['body']
            filename = imgs['filename']
            doc_format = str(os.path.splitext(filename)[1])  # 格式
            uploadfilename = str(uuid.uuid1()) + doc_format
            path = '%s/%s' % (file_path, uploadfilename)
            file = open(path, 'wb')
            file.write(data)
            file.close()
            pp = SopFileInfo(id=fi['uid'],file_name=filename,file_url=path,label_info_id=lb_id,op_ts=nt_time)
            user_session.add(pp)
            user_session.commit()