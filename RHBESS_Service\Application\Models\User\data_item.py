from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Column, ForeignKey, DateTime, VARCHAR
from sqlalchemy.orm import relationship
from Application.Models.User.point_type import PointType
class DataItemV2(user_Base):
    u'点表结构'
    __tablename__ = "t_data_item_v2"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    name = Column(VARCHAR(128), nullable=False, comment=u"点表结构名称")
    station = Column(VARCHAR(64), nullable=False, comment=u"并网点名称")
    descr = Column(VARCHAR(256), nullable=False, comment=u"点表结构描述")
    en_descr = Column(VARCHAR(256), nullable=False, comment=u"点表结构描述-英文")
    point_type_id = Column(Integer, ForeignKey("t_point_type.id"),nullable=True,comment=u"数据类型、设备类型ID")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    point_type = relationship('PointType')
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
