from django.urls import path
from apis.wb import views

urlpatterns = [
    path('index/', views.index),
    path('meter/use_time', views.MeterUseTimeView.as_view()),

    # 写入所有从站的是否使用结算电表配置和使用结算电表的默认使用时间范围
    path('meter/init_config', views.StationMeterUseTimeView.as_view()),

    # 初始化所有主站的mode字段的值
    path('master_station/init_mode', views.InitAllMasterStationModeView.as_view()),

    # 测试用：测试异步翻译
    path('publish/demo', views.PublishTranslateView.as_view()),

    # 导出所有储能单元的配置
    path('ExportUnitsView', views.ExportUnitsView.as_view()),

    # 手动翻译接口
    path('translate/', views.TranslateView.as_view()),
]
