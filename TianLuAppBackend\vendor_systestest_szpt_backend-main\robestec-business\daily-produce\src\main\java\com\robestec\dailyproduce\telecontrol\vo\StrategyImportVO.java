package com.robestec.dailyproduce.telecontrol.vo;

import lombok.Data;
import java.util.List;

/**
 * 策略模板导入VO
 */
@Data
public class StrategyImportVO {
    private String name;
    private List<StrategyDataItem> data;
    private List<String> monthList;

    @Data
    public static class StrategyDataItem {
        private String startTime;
        private String endTime;
        private Object pv;
        private Object chargeConfig;
        private String rl;
    }
} 