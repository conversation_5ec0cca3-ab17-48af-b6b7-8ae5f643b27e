package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.stationr.StationRCreateDTO;
import com.robestec.analysis.dto.stationr.StationRQueryDTO;
import com.robestec.analysis.dto.stationr.StationRUpdateDTO;
import com.robestec.analysis.entity.StationR;
import com.robestec.analysis.mapper.StationRMapper;
import com.robestec.analysis.service.StationRService;
import com.robestec.analysis.vo.StationRVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 电站关系服务实现类
 */
@Slf4j
@Service
public class StationRServiceImpl extends SuperServiceImpl<StationRMapper, StationR>
        implements StationRService {

    @Override
    public PageResult<StationRVO> queryStationR(StationRQueryDTO queryDTO) {
        LambdaQueryWrapper<StationR> wrapper = new LambdaQueryWrapper<StationR>()
                .like(StringUtils.hasText(queryDTO.getStationName()), StationR::getStationName, queryDTO.getStationName())
                .like(StringUtils.hasText(queryDTO.getRunningState()), StationR::getRunningState, queryDTO.getRunningState())
                .like(StringUtils.hasText(queryDTO.getProvince()), StationR::getProvince, queryDTO.getProvince())
                .like(StringUtils.hasText(queryDTO.getEnergyStorage()), StationR::getEnergyStorage, queryDTO.getEnergyStorage())
                .like(StringUtils.hasText(queryDTO.getBatteryCluster()), StationR::getBatteryCluster, queryDTO.getBatteryCluster())
                .like(StringUtils.hasText(queryDTO.getMonitor()), StationR::getMonitor, queryDTO.getMonitor())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), StationR::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), StationR::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(StationR::getElectricPower) // 按电功率降序排列
                .orderByDesc(StationR::getCreateTime);

        Page<StationR> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<StationR> result = this.page(page, wrapper);

        List<StationRVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<StationRVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStationR(StationRCreateDTO createDTO) {
        StationR entity = BeanUtil.copyProperties(createDTO, StationR.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStationR(StationRUpdateDTO updateDTO) {
        StationR entity = BeanUtil.copyProperties(updateDTO, StationR.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStationR(Long id) {
        this.removeById(id);
    }

    @Override
    public StationRVO getStationR(Long id) {
        StationR entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createStationRList(List<StationRCreateDTO> createDTOList) {
        List<StationR> entityList = BeanUtil.copyToList(createDTOList, StationR.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<StationRVO> getStationRByStationName(String stationName) {
        List<StationR> entityList = this.list(Wrappers.<StationR>lambdaQuery()
                .like(StationR::getStationName, stationName)
                .orderByDesc(StationR::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<StationRVO> getStationRByProvince(String province) {
        List<StationR> entityList = this.list(Wrappers.<StationR>lambdaQuery()
                .eq(StationR::getProvince, province)
                .orderByDesc(StationR::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<StationRVO> getStationRByRunningState(String runningState) {
        List<StationR> entityList = this.list(Wrappers.<StationR>lambdaQuery()
                .eq(StationR::getRunningState, runningState)
                .orderByDesc(StationR::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByProvince(String province) {
        return this.count(Wrappers.<StationR>lambdaQuery()
                .eq(StationR::getProvince, province));
    }

    /**
     * 转换为VO对象
     */
    private StationRVO convertToVO(StationR entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, StationRVO.class);
    }
}
