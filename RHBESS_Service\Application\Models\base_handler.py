# -*- coding:utf8 -*-
import sys,os

from sqlalchemy.sql.expression import update

# reload(sys)
# sys.setdefaultencoding("utf-8")


from Application.Cfg.dir_cfg import model_config
from Application.Models import session
from tornado.websocket import <PERSON><PERSON>ocket<PERSON>and<PERSON>
from tornado.web import Request<PERSON><PERSON>ler
from Tools.Error import ErrorInfo

class BaseHandler(RequestHandler):
    # 解决跨域问题
    def set_default_headers(self):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Headers', 'x-requested-with')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE')
    #
    # def on_finish(self):
    #     # 执行在其他方法之后，如果有资源需要关闭，在这里执行
    #     self.finish()

    def returnTypeSuc(self, data ,info=None):
        '''
        @description: 返回成功的值
        @param data 返回数据集
        @param info 返回备用数据
        @return {*}
        '''
        if info == None:
            self.write({"code": 200, "msg": "success", "data": data})
        else:
            self.write({"code": 200, "msg": "success", "data": data,"info":info})

    def returnTypeSuc_en(self, data=None, info=None,lang=None):
        '''
        @description: 返回成功的值
        @param data 返回数据集
        @param info 返回备用数据
        @return {*}
        '''
        if lang=='en':
            if info == None:
                self.write({"code": 200, "msg": "Request successful", "data": data})
            else:
                self.write({"code": 200, "msg": "Request successful", "data": data,"info":info})
        else:
            if info == None:
                self.write({"code": 200, "msg": "请求成功", "data": data})
            else:
                self.write({"code": 200, "msg": "请求成功", "data": data,"info":info})

    def returnTotalSuc(self,data,total,lang=None):
        '''返回分页数据'''
        if lang=='en':
            self.write({"code": 200, "msg": "Request successful", "data": data,"total":total})
        else:
            self.write({"code": 200, "msg": "success", "data": data, "total": total})
    def returnTotalSuccess(self,data,total,lang=None):
        '''返回分页数据'''
        if lang == 'en':
            self.write({"code": 200, "msg": "Request successful", "data": {"data":data,"total":total}})
        else:
            self.write({"code": 200, "msg": "success", "data": {"data": data, "total": total}})
    # 自定义错误
    def customError(self,error):
        self.write({"code": 410, "msg": error})

    # 自定义错误(带id)
    def customErrorId(self,error,id):
        self.write({"code": 408, "msg": error,'id':id})

    def userExist(self,msg,data):
        self.write({"code": 409, "msg": msg,"data":data})

    def pathError(self):
        self.write(ErrorInfo.errLJ)
    def userError(self):
        self.write(ErrorInfo.errRZ)
    def roleError(self):
        self.write(ErrorInfo.errQX)
    def dataError(self):
        self.write(ErrorInfo.errZY)
    def tokenError(self):
        self.write(ErrorInfo.errDL)
       
    def timeError(self):
        self.write(ErrorInfo.errCS)

    def requestError(self,lang=None):
        if lang=='en':
            self.write({"code": 411, "msg": "Request exception"})
        else:
            self.write({"code": 411, "msg": "请求异常"})

    def scada_commit(self,scada_session):
        u'提交修改。配合删除修改操作使用。'
        try:
            scada_session.commit()
        except Exception as e:
            scada_session.rollback()
            print (e)

    def his_commit(self,his_Session):
        u'提交修改。配合删除修改操作使用。'
        try:
            his_Session.commit()
        except Exception as e:
            his_Session.rollback()
            print (e)

   
    #  以下是验证用户登录
    def get_current_user(self):
        u'获取用户登录信息'
        s = self.getSession()
        if s:
            return s.user
        return None

    # def getSession(self):
    #     u'获取会话'
    #     sid = self.get_secure_cookie("sid")
    #     return session.getSession(sid) if sid else None

    def getSession(self):
        u'获取会话'
        Authorization = self.request.headers.get('Authorization')
        return session.getSession(Authorization) if Authorization else None

    # def getOrNewSession(self, success_token=None):
    #     # u'获取会话或创建会话'
    #     # sid = self.get_secure_cookie("sid")
    #     # s = session.getSession(sid) if sid else None
    #     # return s if s else session.newSession(self.request.remote_ip)

    def getOrNewSession(self, success_token=None):
        u'获取会话或创建会话'
        # sid = self.get_secure_cookie("sid")
        Authorization = success_token if success_token else self.request.headers.get('Authorization')
        s = session.getSession(Authorization)
        return s if s else session.newSession(Authorization)

    def updateSession(self, session_=None, Authorization=None):
        u'更新本会话'
        if session_==None:
            session_ = self.getSession()
        if session_:
            session_.update()
            if not Authorization:
                Authorization = self.request.headers.get('Authorization')
            session.updateSession(Authorization, session_)
            # self.set_secure_cookie("sid", session_.sid)
       
    def refreshSession(self):
        # 刷新session
        sess = self.getSession()
        if sess:
            sess.update()


    def commonError(self, msg = None, data=None, code=None):
        if data is None:
            data = []
        ErrorInfo.new_errParameter['data'] = data
        if msg:
            ErrorInfo.new_errParameter['message'] = msg
        if code:
            ErrorInfo.new_errParameter['code'] = code
        self.write(ErrorInfo.new_errParameter)

    def new_pathError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errLJ['data'] = data
        self.write(ErrorInfo.new_errLJ)

    def new_userError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errRZ['data'] = data
        self.write(ErrorInfo.new_errRZ)

    def new_roleError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errQX['data'] = data
        self.write(ErrorInfo.new_errQX)

    def new_dataError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errZY['data'] = data
        self.write(ErrorInfo.new_errZY)

    def new_tokenError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errDL['data'] = data
        self.write(ErrorInfo.new_errDL)

    def new_timeError(self, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errCS['data'] = data
        self.write(ErrorInfo.new_errCS)

    def parameterError(self, msg=None, data=None):
        if data is None:
            data = []
        ErrorInfo.new_errParameter['data'] = data
        if msg:
            ErrorInfo.new_errParameter['message'] = msg
        self.write(ErrorInfo.new_errParameter)

    def commonSuc(self, data ,info=None):
        '''
        @description: 返回成功的值
        @param data 返回数据集
        @param info 返回备用数据
        @return {*}
        '''
        ErrorInfo.successIF['data'] = data
        if info != None:
            ErrorInfo.successIF['message'] = info
        self.write(ErrorInfo.successIF)
    def pagingSuccess(self,data,total,page_size,page_num):
        '''返回分页数据'''
        ErrorInfo.successIF['data'] = data
        ErrorInfo.successIF['total'] = total
        ErrorInfo.successIF['page_size'] = page_size
        ErrorInfo.successIF['page_num'] = page_num
        self.write(ErrorInfo.successIF)



class WebSocketBaseHandler(WebSocketHandler):
    """
    websocket类
    """
    # #  以下是验证用户登录
    # def get_current_user(self):
    #     u'获取用户登录信息'
    #     s = self.getSession()
    #     if s:
    #         return s.user
    #     return None

    def getSession(self):
        u'获取会话'
        sid = self.get_secure_cookie("sid")
        return session.getSession(sid) if sid else None
