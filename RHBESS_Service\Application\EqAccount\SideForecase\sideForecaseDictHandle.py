#!/usr/bin/env python
# coding=utf-8
#@Information:字典管理
#<AUTHOR> WYJ
#@Date         : 2023-08-31 10:49:59
#@FilePath     : \RHBESS_Service\Application\EqAccount\SideForecase\sideForecaseDictHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 10:51:26


import os,uuid

import numpy
import tornado.web
from Application.Models.SideForecase.side_forecase_dict_policy_infos import ForecaseDicPolicy
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_dict_capacity import ForecaseDictCapacity
from Application.Models.SideForecase.side_forecase_dict_device_type import ForecaseDictDeviceType
from Application.Models.SideForecase.side_forecase_dict_dod import ForecaseDictDOD
from Application.Models.SideForecase.side_forecase_dict_eff_chagdisg import ForecaseDictEffChagDisg
from Application.Models.SideForecase.side_forecase_dict_firm_type import ForecaseDictFrimType
from Application.Models.SideForecase.side_forecase_dict_indus_one import ForecaseDictIndesOne
from Application.Models.SideForecase.side_forecase_dict_indus_two import ForecaseDictIndesTwo
from Application.Models.SideForecase.side_forecase_dict_own_type import ForecaseDictOwnType
from Application.Models.SideForecase.side_forecase_dict_project_sign import ForecaseDictProjectsign
from Application.Models.SideForecase.side_forecase_dict_project_type import ForecaseDictProjectType
from Application.Models.SideForecase.side_forecase_dict_runday_num import ForecaseDictRundayNum
from Application.Models.SideForecase.side_forecase_dict_runday_one import ForecaseDictRundayOne
from Application.Models.SideForecase.side_forecase_dict_runday_two import ForecaseDictRundayTwo
from Application.Models.SideForecase.side_forecase_dict_share_emc import ForecaseDictShareEmc
from Application.Models.SideForecase.side_forecase_price_files import ForecasePriceFiles
from Application.Models.SideForecase.side_forecase_fastmatch_model import ForecaseFastmatchModel
from Application.Models.SideForecase.side_forecase_fastmatch_other import ForecaseFastmatchOther
from Application.Models.SideForecase.side_forecase_first_info import ForecaseFirstInfo
from Application.Models.SideForecase.side_forecase_dict_policy_infos_user import ForecaseDicPolicyUser
from Application.Models.SideForecase.side_forecase_user import ForecaseUser
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import *
from sqlalchemy import func,or_
class SideForecaseDictHandleIntetface(BaseHandler):

    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'GetFrimTypes':  # 查询企业性质
                data = []
                pages = user_session.query(ForecaseDictFrimType).filter(ForecaseDictFrimType.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetRundayOne':  # 查询经营类型第一步
                data = []
                pages = user_session.query(ForecaseDictRundayOne).filter(ForecaseDictRundayOne.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetRundayTwo':  # 查询经营类型第二步
                data = []
                id = self.get_argument('id',None) # 经营类型第一步id
                if not id:
                    return self.customError("入参不完成")
                pages = user_session.query(ForecaseDictRundayTwo).filter(ForecaseDictRundayTwo.is_use==1,ForecaseDictRundayTwo.parent_id==id).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetIndusOne':  # 查询所属行业第一步
                data = []
                pages = user_session.query(ForecaseDictIndesOne).filter(ForecaseDictIndesOne.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetIndusTwo':  # 查询所属行业第二步
                data = []
                id = self.get_argument('id',None) # 所属行业第一步id
                if not id:
                    return self.customError("入参不完成")
                pages = user_session.query(ForecaseDictIndesTwo).filter(ForecaseDictIndesTwo.is_use==1,ForecaseDictIndesTwo.parent_id==id).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetOwnType':  # 产权情况
                data = []
                pages = user_session.query(ForecaseDictOwnType).filter(ForecaseDictOwnType.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectType':  # 项目类别
                data = []
                pages = user_session.query(ForecaseDictProjectType).filter(ForecaseDictProjectType.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectsign':  # 项目签约情况
                data = []
                pages = user_session.query(ForecaseDictProjectsign).filter(ForecaseDictProjectsign.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceType':  # 设备类别
                data = []
                pages = user_session.query(ForecaseDictDeviceType).filter(ForecaseDictDeviceType.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetShareEmc':  # 分享模式
                data = []
                type_ = int(self.get_argument('type',1))
                pages = user_session.query(ForecaseDictShareEmc).filter(ForecaseDictShareEmc.is_use==1,ForecaseDictShareEmc.type_==type_).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetRundayNum':  # 运行天数
                data = []
                pages = user_session.query(ForecaseDictRundayNum).filter(ForecaseDictRundayNum.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetDOD':  # 充放深度
                data = []
                pages = user_session.query(ForecaseDictDOD).filter(ForecaseDictDOD.is_use==1).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetEffChagDisg':  # 充放效率
                data = []
                type_ = int(self.get_argument('type',1))
                pages = user_session.query(ForecaseDictEffChagDisg).filter(ForecaseDictEffChagDisg.is_use==1,ForecaseDictEffChagDisg.type_==type_).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetCapacity':  # 容量
                data = []
                type_ = int(self.get_argument('type',1))
                pages = user_session.query(ForecaseDictCapacity).filter(ForecaseDictCapacity.is_use==1,ForecaseDictCapacity.type_==type_).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetPriceFiles':  # 代理购电价格源文件
                data = []
                province_name = self.get_argument('province_name',None)  # 省份名称
                month_ = self.get_argument('month_',None)  # 月份
                if not province_name or not month_:
                    return self.customError('入参不完整')

                page = user_session.query(ForecasePriceFiles.file_path).filter(ForecasePriceFiles.is_use==1,ForecasePriceFiles.province_name==province_name,ForecasePriceFiles.month==month_
                                            ).order_by(ForecasePriceFiles.id.desc()).first()
                if page:
                    return self.returnTypeSuc(page[0])
                else:
                    return self.returnTypeSuc('')
            elif kt == 'GetFirstMapInfos':  # 查询首页地图数据
                data = []
                # province_name = self.get_argument('province_name',None)  # 省份名称
                user_type = self.get_argument('user_type',None)  # 用户类型；用户侧、独立储能
                # logging.info('province_name:%s,user_type:%s'%(province_name,user_type))
                if not user_type:
                    return self.customError('入参不完整')
                data = []
                filter = [ForecaseFirstInfo.is_use==1,ForecaseFirstInfo.type==1]
                # if province_name:
                #     filter.append(ForecaseFirstInfo.province_name==province_name)
                if user_type:
                    filter.append(ForecaseFirstInfo.user_type==user_type)
                page = user_session.query(ForecaseFirstInfo).filter(*filter).order_by(ForecaseFirstInfo.op_ts.desc()).first()
                if page:
                    pa = eval(str(page))
                    pa['content1'] = json.loads(page.content1)
                    if page.content2:
                        pa['content2'] = json.loads(page.content2)
                    data.append(pa)
                return self.returnTypeSuc(data)
            elif kt == 'GetFirstMapList':  # 查询首页地图时政消息
                data = []
                province_name = self.get_argument('province_name',None)  # 省份名称
                user_type = self.get_argument('user_type',None)  # 用户类型；用户侧、独立储能
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                logging.info('province_name:%s,user_type:%s,pageNum:%s,pageSize:%s'%(province_name,user_type,pageNum,pageSize))
                if not province_name or not user_type:
                    return self.customError('入参不完整')
                data = []
                filter = [ForecaseFirstInfo.is_use==1,ForecaseFirstInfo.type==2]
                if province_name:
                    filter.append(ForecaseFirstInfo.province_name==province_name)
                if user_type:
                    filter.append(ForecaseFirstInfo.user_type==user_type)

                total = user_session.query(func.count(ForecaseFirstInfo.id)).filter(*filter).scalar()
                page = user_session.query(ForecaseFirstInfo).filter(*filter).order_by(ForecaseFirstInfo.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in page:
                    pa = eval(str(pag))
                    pa['title'] = pag.content1
                    pa['content'] = pag.content2
                    data.append(pa)
                return self.returnTotalSuc(data,total)

            elif kt == 'GetFirstMapPolicy':  # 查询首页地图政策情报(政策情报，政策草稿)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                name = self.get_argument('name', None)  # 名称搜索框
                tag_id_1 = self.get_argument('tag_id_1', '[]')  # 标签搜索框（政策草稿不填）
                tag_id_2 = self.get_argument('tag_id_2', '[]')  # 标签搜索框（政策草稿不填）
                tag_id_3 = self.get_argument('tag_id_3', '[]')  # 标签搜索框（政策草稿不填）
                ty_p = self.get_argument('ty_p', None)  # 政策和草稿填1
                is_use = self.get_argument('is_use', 1)  # 是否使用1是0否默认1，2草稿箱
                sort_ = self.get_argument('sort', 0)  # 是否使用0不排序，1正序，2倒序
                logging.info('pageNum:%s,pageSize:%s,name:%s,tag_name:%s,ty_p:%s,is_use:%s' % (pageNum, pageSize, name, tag_id_1,ty_p,is_use))
                session = self.getOrNewSession()
                user_id = session.user['id']
                user_role_id = session.user['user_role_id']
                if int(user_role_id) == 1: # 超级管理员默认查询全部
                    filter = [ForecaseDicPolicy.is_use == str(is_use)]
                else:
                    user = user_session.query(ForecaseUser).filter(ForecaseUser.id == int(user_id)).first()
                    intel_res = user_session.query(ForecaseDicPolicyUser).filter(or_(
                        ForecaseDicPolicyUser.group_id == user.group_id, ForecaseDicPolicyUser.rank_id == user.rank_id)).all()
                    intel_ids = [i.intel_id for i in intel_res]  # 政策情报ID
                    filter = [ForecaseDicPolicy.is_use == str(is_use), ForecaseDicPolicy.id.in_(intel_ids)]
                if name:
                    filter.append(or_(ForecaseDicPolicy.title.like('%'+name +'%'),ForecaseDicPolicy.user.like('%'+name +'%')))
                if tag_id_1!='[]':
                    try:
                        tag_id_1 = str(sorted(eval(str(tag_id_1))))
                        filter.append(ForecaseDicPolicy.dic_police_ids_1.like('%'+tag_id_1[1:-1] +'%'))
                    except:
                        pass
                if tag_id_2!='[]':
                    try:
                        tag_id_2 = str(sorted(eval(str(tag_id_2))))
                        filter.append(ForecaseDicPolicy.dic_police_ids_2.like('%'+tag_id_2[1:-1] +'%'))
                    except:
                        pass
                if tag_id_3!='[]':
                    try:
                        tag_id_3 = str(sorted(eval(str(tag_id_3))))
                        filter.append(ForecaseDicPolicy.dic_police_ids_3.like('%'+tag_id_3[1:-1] +'%'))
                    except:
                        pass
                if sort_ == '1':
                    query = user_session.query(ForecaseDicPolicy).filter(*filter).order_by(ForecaseDicPolicy.is_top.desc(), ForecaseDicPolicy.title.asc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                elif sort_ == '2':
                    query = user_session.query(ForecaseDicPolicy).filter(*filter).order_by(ForecaseDicPolicy.is_top.desc(), ForecaseDicPolicy.title.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                else:
                    query = user_session.query(ForecaseDicPolicy).filter(*filter).order_by(
                        ForecaseDicPolicy.is_top.desc(), ForecaseDicPolicy.id.desc()).limit(pageSize).offset(
                        (pageNum - 1) * pageSize).all()
                total =user_session.query(func.count(ForecaseDicPolicy.id)).filter(*filter).scalar()
                data=[]
                rank_ids = [i.id for i in query]
                rank_res = user_session.query(ForecaseDicPolicyUser).filter(ForecaseDicPolicyUser.intel_id.in_(rank_ids)).all()

                rank_dict = {}
                for i in rank_res:
                    if rank_dict.get(i.intel_id):
                        if i.rank_id:
                            rank_dict[i.intel_id].append(i.rank_id)
                    else:
                        if i.rank_id:
                            rank_dict[i.intel_id] = [i.rank_id]

                if ty_p and int(ty_p) == 1:
                    e = {}#可操作步骤
                    e['redact'] = []
                    if int(is_use) == 1:
                        if user_role_id == '1':  # 超级管理员
                            e['redact'] = [1, 2, 3, 4, 5, 6, 7]  # 1查看，2新建，3编辑，4删除，5阅读，6置顶,7情报草稿箱
                        else:
                            if user_role_id == '4':  # 浏览账号
                                e['redact'] = [1]
                            else:# 普通用户,客户
                                e['redact'] = [1, 2, 5]  # 1查看，2新建，3编辑，4删除，5阅读，6置顶,7情报草稿箱
                    elif int(is_use) == 2:  # 草稿箱
                        if user_role_id == '1':  # 超级管理员
                            e['redact'] = [1, 2, 3, 4, 5, 6]  # 1查看,2编辑，3删除
                    for row in query:
                        row = eval(str(row))
                        if user_role_id == '2' or user_role_id == '3':  # 普通用户,客户
                            if int(row['user_id']) == int(user_id):
                                if int(is_use) == 1:
                                    e['redact'].append(3)
                                    e['redact'].append(4)
                                    e['redact'].append(7)
                                elif int(is_use) == 2:  # 草稿箱
                                    e['redact'] = [1]
                        # rank = user_session.query(ForecaseDicPolicyUser).filter(ForecaseDicPolicyUser.intel_id == row['id']).all()
                        # rank = [i.rank_id for i in rank if i.rank_id]
                        data.append({'id': row['id'], 'content': row['content'], 'create_time': row['create_time'],
                             'title': row['title'], 'user': row['user'], 'is_use': row['is_use'],
                             'is_top': row['is_top'],'see_users': row['see_users'], 'org_gro': row['org_gro'],
                             'redact': e['redact'], 'file_name': row['file_name'], 'file_path': row['file_path'],
                             'page_ty_name_1': row['page_ty_name_1'],'page_ty_name_2': row['page_ty_name_2'], 'page_ty_name_3': row['page_ty_name_3'],
                             'dic_police_ids_1': row['dic_police_ids_1'], 'dic_police_ids_2': row['dic_police_ids_2'],
                             'dic_police_ids_3': row['dic_police_ids_3'], "rank": rank_dict.get(row['id'])})

                else:
                    for row in query:
                        row = eval(str(row))
                        # rank = user_session.query(ForecaseDicPolicyUser).filter(ForecaseDicPolicyUser.intel_id == row['id']).all()
                        # rank = [i.rank_id for i in rank if i.rank_id]
                        data.append({'id': row['id'], 'create_time': row['create_time'],
                                     'page_ty_name_1': row['page_ty_name_1'], 'page_ty_name_2': row['page_ty_name_2'],
                                     'page_ty_name_3': row['page_ty_name_3'], 'title': row['title'],
                                     'is_top': row['is_top'], 'see_users': row['see_users'], "rank": rank_dict.get(row['id'])})
                # if sort_=='1':
                #     data = sorted(data, key=lambda x: (x.get('is_top'), x.get('title')), reverse=False)
                # elif sort_ == '2':
                #     data = sorted(data, key=lambda x: (x.get('is_top'), x.get('title')), reverse=True)
                return self.returnTotalSuc(data,total)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'AddPriceFiles':  # 添加电价文件
                province_name = self.get_argument('province_name',None)  # 省份
                year_ = self.get_argument('year_',None)  # 年
                month_ = self.get_argument('month_',None)  # 月
                if not province_name or not year_ or not month_:
                    return self.customError('参数不完整')
                
                files = self.request.files
                file_path = '/home/<USER>/side/price_files'
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('files')
                img_path,filename = '',''
                if imgs:
                    data = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    img_path = '%s/%s' % (file_path,filename)
                    file = open(img_path, 'wb')
                    file.write(data)
                    file.close()
                pa = user_session.query(ForecasePriceFiles).filter(ForecasePriceFiles.is_use==1,ForecasePriceFiles.province_name==province_name,ForecasePriceFiles.month==month_,
                                                ForecasePriceFiles.year==year_).first()
                if pa :
                    os.remove(pa.file_path)
                    pa.file_name = filename
                    pa.file_path = img_path
                    user_session.commit()
                    d = {"id":pa.id}
                else:
                    user = ForecasePriceFiles(province_name=province_name,year=year_,month=month_,file_name=filename,file_path=img_path)
                    user_session.merge(user)
                    user_session.commit()
                    d={"id":user.id}
                return self.returnTypeSuc(d)
            elif kt == 'AddFirstMapInfo':  # 添加首页信息
                province_name = self.get_argument('province_name',None)  # 省份
                user_type = self.get_argument('user_type',None)  # 年
                content1 = self.get_argument('content1',None)  # 月
                content2 = self.get_argument('content2',None)  # 月
                if not province_name or not user_type :
                    return self.customError('参数不完整')
                d = ForecaseFirstInfo(user_type=user_type,province_name=province_name,content1 = content1,content2=content2,op_ts=timeUtils.getNewTimeStr(),type=1)
                user_session.add(d)
                user_session.commit()
                
                return self.returnTypeSuc({"id":d.id})
            elif kt == 'AddFirstMapList':  # 添加首页时政小心
                province_name = self.get_argument('province_name',None)  # 省份
                user_type = self.get_argument('user_type',None)  # 年
                content1 = self.get_argument('title',None)  # 月
                content2 = self.get_argument('content',None)  # 月
                if not province_name or not user_type :
                    return self.customError('参数不完整')
                d = ForecaseFirstInfo(user_type=user_type,province_name=province_name,content1 = content1,content2=content2,op_ts=timeUtils.getNewTimeStr(),type=2)
                user_session.add(d)
                user_session.commit()
                
                return self.returnTypeSuc({"id":d.id})
        except Exception as E:
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
        
    
class SideForecaseFastMatchHandleIntetface(BaseHandler):
    '''速查表'''
    
    # @tornado.web.authenticated
    # def get(self,kt):
    #     self.refreshSession()
    #     try:
    #         pass
            

    #     except Exception as E:
    #         user_session.rollback()
    #         logging.error(E)
    #         return self.requestError()
    #     finally:
    #         user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'FastmatchOther':  # 速查，其他
                province_id = self.get_argument('province_id',None)  # 省份id
                vol_id = self.get_argument('vol_id',None)  # 电压等级
                ele_id = self.get_argument('ele_id',None)  # 用电类型
                expect_emc_share = self.get_argument('expect_emc_share',None)  # 分享比例
                cap = self.get_argument('cap',None)  # 容量范围
                if DEBUG:
                    logging.info("province_id:%s,vol_id:%s,ele_id:%s,expect_emc_share:%s,cap:%s"%(province_id,vol_id,ele_id,expect_emc_share,cap))
                if not province_id or not vol_id or not ele_id or not expect_emc_share or not cap:
                    return self.customError('参数不完整')
                
                pa = user_session.query(ForecaseFastmatchOther).filter(ForecaseFastmatchOther.is_use==1,ForecaseFastmatchOther.province_id==province_id,ForecaseFastmatchOther.vol_id==vol_id,
                                        ForecaseFastmatchOther.ele_id==ele_id,ForecaseFastmatchOther.share_emc==expect_emc_share,ForecaseFastmatchOther.capacity==cap).first()
                if pa :
                    p = eval(str(pa))
                    p['cost_count'] = json.loads(pa.cost_count)
                    p['back_before'] = json.loads(pa.back_before)
                    p['back_after'] = json.loads(pa.back_after)

                    return self.returnTypeSuc(p)
                else:
                    return self.customError("不建议自投该项目")
            elif kt == 'FastmatchModel':  # 速查，仿真
                province_id = self.get_argument('province_id',None)  # 省份id
                vol_id = self.get_argument('vol_id',None)  # 电压等级
                ele_id = self.get_argument('ele_id',None)  # 用电类型
                run_day = self.get_argument('run_day',None)  # 运行天数
                dod = self.get_argument('dod',None)  # 放电深度
                power = self.get_argument('power',None)  #"功率"
                capacity = self.get_argument('cap',None) # "容量"
                eff_chag = self.get_argument('eff_chag',None) #"充电效率"
                eff_disg = self.get_argument('eff_disg',None) # "放电效率"
                project_year = int(self.get_argument('project_year',1)) # 项目年限
                share1_val = float(self.get_argument('share1_val',1)) # 第一阶段分享比例
                share1_start = self.get_argument('share1_start',None)  # 第一阶段开始年限
                share1_end = self.get_argument('share1_end',None)  # 第一阶段截止年限
                share2_val = float(self.get_argument('share2_val',1)) # 第二阶段分享比例
                share2_start = self.get_argument('share2_start',None)  # 第二阶段起始年限
                share2_end = self.get_argument('share2_end',None)  # 第二阶段截止年限

                if DEBUG:
                    logging.info("province_id:%s,vol_id:%s,ele_id:%s,run_day:%s,dod:%s,power:%s,capacity:%s,eff_chag:%s,eff_disg:%s,project_year:%s,share1_start:%s,\
                                 share1_end:%s,share2_start:%s,share2_end:%s,hare1_val:%s,share2_val:%s"%(
                        province_id,vol_id,ele_id,run_day,dod,power,capacity,eff_chag,eff_disg,project_year,share1_start,share1_end,share2_start,share2_end,share1_val,share2_val))
                if not province_id or not vol_id or not ele_id or not run_day or not dod or not power or not capacity or not eff_chag or not eff_disg or not project_year or not share1_start \
                    or not share1_end or not share2_start or not share2_end:
                    return self.customError('参数不完整')
                if project_year>20:
                    return self.customError('入参不合理1')
                share1_start = int(share1_start)
                share1_end = int(share1_end)
                share2_start = int(share2_start)
                share2_end = int(share2_end)
                if share2_start<share1_end :
                    return self.customError('入参不合理')

                # 电池衰减
                battery_ = [0.0183,0.0366,0.0549,0.0733,0.0916,0.1099,0.1282,0.1465,0.1648,0.1831,0.0183,0.0366,0.0549,0.0733,0.0916,0.1099,0.1282,0.1465,0.1648,0.1831]
                share_ = []
                
                # 第一阶段
                for a in range(share1_end):
                    share_.append(share1_val/100)
                # 第二阶段
                for b in range(share1_end+1,share2_end+1):
                    share_.append(share2_val/100)
               
                pa = user_session.query(ForecaseFastmatchModel).filter(ForecaseFastmatchOther.is_use==1,ForecaseFastmatchModel.province_id==province_id,ForecaseFastmatchModel.vol_id==vol_id,
                                        ForecaseFastmatchModel.ele_id==ele_id,ForecaseFastmatchModel.run_day==run_day,ForecaseFastmatchModel.capacity==capacity,ForecaseFastmatchModel.eff_chag==eff_chag,
                                        ForecaseFastmatchModel.eff_disg==eff_disg).first()
                if pa :
                    d = []
                    p = eval(str(pa))
                    p['weig_price'] = json.loads(pa.weig_price)
                    p['year_chag_disg'] = json.loads(pa.year_chag_disg)
                    for i in range(project_year):
                        dd = []
                        dd.append(round(float(pa.year_first_disg)*(1-battery_[i]),2))  # 年放电量
                        v1 = float(pa.shounian_taoli)*(1-battery_[i])  # 每年套利
                        dd.append(round(v1,2))  # 每年套利
                        dd.append(round(v1*(1-share_[i]),2))  # 用能方收益
                        dd.append(round(v1*share_[i],2))  # 投资方收益
                        d.append(dd)
                    p['d_c'] = d
                    d = numpy.array(d).T
                    t_kWh = sum(d[0])  # 总计-年放电量
                    t_arbitrage = sum(d[1])  # 总计-年总套利EMC分享前
                    t_energy_user = sum(d[2])  # 总计-用能方收益
                    t_investors = sum(d[3])  # 总计-投资方收益
                    p['t_kWh'] = round(t_kWh, 2)
                    p['t_arbitrage'] = round(t_arbitrage, 2)
                    p['t_energy_user'] = round(t_energy_user, 2)
                    p['t_investors'] = round(t_investors, 2)
                    return self.returnTypeSuc(p)
                else:
                    return self.customError("不建议自投该项目")
        except Exception as E:
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()