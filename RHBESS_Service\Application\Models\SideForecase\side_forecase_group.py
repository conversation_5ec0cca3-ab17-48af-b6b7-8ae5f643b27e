#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:29:20
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_group.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-01 14:23:59


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.User.organization import Organization
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseGroup(user_Base):
    u'小组表'
    __tablename__ = "t_side_forecase_group"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(String(256), nullable=False, comment=u"描述")
    organization_id = Column(Integer, ForeignKey("t_side_forecase_organization.id"),nullable=False, comment=u"组织id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    organization_group = relationship("ForecaseOrganization", backref="organization_group")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'name':'%s','organization_id':'%s','organization_descr':'%s'}" %(self.id,self.name,self.organization_id,self.organization_group.descr)

   