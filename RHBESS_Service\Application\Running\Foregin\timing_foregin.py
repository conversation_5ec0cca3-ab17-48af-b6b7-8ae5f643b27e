#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-03-30 15:16:02
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\Running\Foregin\timing_foregin.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-07-01 10:36:35
import os,time
from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()

def RunClearFileAndData():
    send_= scheduler.add_job(restart_fun, 'cron', hour='*/6' )  # 每天6小时执行
    
    scheduler.start()

def restart_fun():
    os.system('pkill -f main_foregin.py')
    time.sleep(2)
    os.system('nohup python main_foregin.py >/dev/null 2>&1 &')

      
if __name__ == '__main__':
    RunClearFileAndData()
    




