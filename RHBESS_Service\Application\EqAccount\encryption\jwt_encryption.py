import base64

import jwt
import datetime
from jwt import exceptions
from Tools.Cfg.jwt_secret_key import SECRET_KEY


def create_token(payload, timeout=48):
    """
    :param payload:  例如：{'user_id':1,'username':'wupeiqi'}用户信息
    :param timeout: token的过期时间，默认两天
    :return:
    """
    headers = {
        'typ': 'jwt',
        'alg': 'HS256'
    }
    payload['exp'] = datetime.datetime.utcnow() + datetime.timedelta(hours=timeout)
    result = jwt.encode(payload=payload, key=SECRET_KEY.encode('utf-8'), algorithm="HS256", headers=headers)
    return result


def parse_payload(token):
    """
    对token进行和发行校验并获取payload
    :param token:
    :return:
    """
    try:
        try:
            token = base64.b64decode(token)
        except Exception as e:
            pass
        finally:
            token = token
        verified_payload = jwt.decode(token, SECRET_KEY.encode('utf-8'), algorithms=["HS256"])
        return True, verified_payload
    except exceptions.ExpiredSignatureError:
        error = 'token已失效'
    except jwt.DecodeError:
        error = 'token认证失败'
    except jwt.InvalidTokenError:
        error = '非法的token'
    return False, error
