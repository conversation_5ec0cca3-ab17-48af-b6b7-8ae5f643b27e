package com.robestec.analysis.dto.tpowerdeliverrecords;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 功率计划下发记录更新DTO
 */
@Data
@ApiModel("功率计划下发记录更新DTO")
public class TPowerDeliverRecordsUpdateDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @ApiModelProperty(value = "任务名称", required = true)
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @ApiModelProperty(value = "英文任务名称")
    private String enName;

    @ApiModelProperty(value = "联系人电话")
    private String mobile;

    @ApiModelProperty(value = "功率计划列表(JSON格式)", required = true)
    @NotBlank(message = "功率计划列表不能为空")
    private String powerList;

    @ApiModelProperty(value = "关联并网点列表(JSON格式)", required = true)
    @NotBlank(message = "关联并网点列表不能为空")
    private String stationList;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "计划类型: 1-自定义, 2-周期性, 3-节假日", required = true)
    @NotNull(message = "计划类型不能为空")
    private Integer planType;
}
