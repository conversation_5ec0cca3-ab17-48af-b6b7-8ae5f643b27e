#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:43:30
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\Models\His\r_ACDMS.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-11-25 16:34:42

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_his import his_Base,his_session
from Tools.DB.mysql_his import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text

from sqlalchemy.ext.declarative import declarative_base

def HisACDMS(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类
    class table_model(Model):
        __tablename__ = _BOOKNAME
 
        name = Column(String(256), nullable=False,primary_key=True,comment=u"名称")
        value = Column(Integer, nullable=False, comment=u"")
        dts_s = Column(DateTime, nullable=False,comment=u"时间")
        dts_ms = Column(Integer, nullable=False,primary_key=True,comment=u"毫秒")
        ots = Column(DateTime, nullable=False,comment=u"时间")
        cause = Column(Integer, nullable=False,comment=u"原因")
        descr = Column(String(250), nullable=False,comment=u"描述")
        device_id = Column(Integer, nullable=False,comment=u"所属设备")
        station = Column(String(250), nullable=False,comment=u"电站英文名称")
        id = Column(Integer, nullable=False,comment=u"id")
        status_id = Column(Integer, nullable=False,comment=u"status_id")
        bits = Column(Integer, nullable=False,comment=u"bits")

    return table_model


def HisACDMS_S(_BOOKNAME):#配置数据库基类
    Model = declarative_base()  # 生成一个SQLORM基类

    class table_model(Model):
        __tablename__ = _BOOKNAME

        id = Column(Integer, nullable=False, comment=u"id")
        name = Column(String(256), nullable=False, primary_key=True, comment=u"名称")
        descr = Column(String(250), nullable=False, comment=u"描述")

    return table_model


def HisACDMS_S_D(_BOOKNAME):#东睦配置数据库基类
    Model = declarative_base()  # 生成一个SQLORM基类

    class table_model(Model):
        __tablename__ = _BOOKNAME

        name = Column(String(256), nullable=False, primary_key=True, comment=u"名称")
        descr = Column(String(250), nullable=False, comment=u"描述")
        device_id = Column(Integer, nullable=False, comment=u"所属设备")
        id = Column(Integer, nullable=False, comment=u"id")
        status_id = Column(Integer, nullable=False, comment=u"status_id")
        bits = Column(Integer, nullable=False, comment=u"bits")

    return table_model

def HisACDMS_YG(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类
 
    class table_model(Model):
        __tablename__ = _BOOKNAME
 
        name = Column(String(256), nullable=False,primary_key=True,comment=u"名称")
        value = Column(Integer, nullable=False, comment=u"")
        dts_s = Column(DateTime, nullable=False,comment=u"时间")
        dts_ms = Column(Integer, nullable=False,primary_key=True,comment=u"毫秒")
        ots = Column(DateTime, nullable=False,comment=u"时间")
        cause = Column(Integer, nullable=False,comment=u"原因")

    return table_model


def HisSDA(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类
    class table_model(Model):
        __tablename__ = _BOOKNAME

        name = Column(String(256), nullable=False, primary_key=True, comment=u"名称")
        no = Column(Integer, nullable=False, comment=u"")
        descr = Column(String(250), nullable=False, comment=u"描述")
        id = Column(Integer, nullable=False, comment=u"id")


    return table_model

def HisTCXR(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类-太仓鑫融
    class table_model(Model):
        __tablename__ = _BOOKNAME

        name = Column(String(256), nullable=False, primary_key=True, comment=u"名称")
        store_flag = Column(Integer, nullable=False, comment=u"")
        descr = Column(String(250), nullable=False, comment=u"描述")
        id = Column(Integer, nullable=False, comment=u"id")


    return table_model

def HisDM(_BOOKNAME):
    # 东睦
    Model_dm = declarative_base()  # 生成一个SQLORM基类
 
    class table_model_dm(Model_dm):
        __tablename__ = _BOOKNAME
 
        ntime = Column(Integer, nullable=False,primary_key=True,comment=u"入库时间")
        time = Column(Integer, nullable=False, comment=u"采集时间")
        utime = Column(Integer, nullable=False,comment=u"打包时间")
        datainfo = Column(Text, nullable=False,comment=u"数据")
        
 
    return table_model_dm

def HisTL(_BOOKNAME):
    # 天禄
    Model_tl = declarative_base()  # 生成一个SQLORM基类  工商业储能天禄系列
 
    class table_model_tl(Model_tl):
        __tablename__ = _BOOKNAME
        app_name = Column(String(50), nullable=False,primary_key=True,comment=u"app名")
        station_name = Column(String(50), nullable=False,comment=u"站点code")
        type = Column(String(50), nullable=False,comment=u"上报类型（0=变化上报/1=定时上报）")
        time = Column(DateTime, nullable=False, comment=u"采集时间")
        utime = Column(DateTime, nullable=False,comment=u"打包时间")
        data_info = Column(Text, nullable=False,comment=u"数据")
        slave = Column(Integer, nullable=False,comment=u"")
        pack = Column(Integer, nullable=False,comment=u"")
        ts = Column(DateTime, nullable=False,comment=u"入库时间")
 
    return table_model_tl