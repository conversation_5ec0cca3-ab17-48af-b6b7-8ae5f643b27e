#!/usr/bin/env python
# coding=utf-8
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")

GUIZHOU_HOSTNAME = model_config.get('mysql', "GUIZHOU_HOSTNAME")
GUIZHOU_PORT = model_config.get('mysql', "GUIZHOU_PORT")
BGUIZHOU1_DATABASE = model_config.get('mysql', "GUIZHOU1_DATABASE")
BGUIZHOU2_DATABASE = model_config.get('mysql', "GUIZHOU2_DATABASE")
BGUIZHOU3_DATABASE = model_config.get('mysql', "GUIZHOU3_DATABASE")
BGUIZHOU4_DATABASE = model_config.get('mysql', "GUIZHOU4_DATABASE")
BGUIZHOU5_DATABASE = model_config.get('mysql', "GUIZHOU5_DATABASE")
BGUIZHOU6_DATABASE = model_config.get('mysql', "GUIZHOU6_DATABASE")
BGUIZHOU7_DATABASE = model_config.get('mysql', "GUIZHOU7_DATABASE")
BGUIZHOU8_DATABASE = model_config.get('mysql', "GUIZHOU8_DATABASE")
GUIZHOU_USERNAME = model_config.get('mysql', "GUIZHOU_USERNAME")
GUIZHOU_PASSWORD = model_config.get('mysql', "GUIZHOU_PASSWORD")



bhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU1_DATABASE
)
bguizhou1_engine = create_engine(bhisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou1_session = scoped_session(sessionmaker(bguizhou1_engine,autoflush=True))
bguizhou1_Base = declarative_base(bguizhou1_engine)
bguizhou1_session = _bguizhou1_session()


bhisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU2_DATABASE
)
bguizhou2_engine = create_engine(bhisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou2_session = scoped_session(sessionmaker(bguizhou2_engine,autoflush=True))
bguizhou2_Base = declarative_base(bguizhou2_engine)
bguizhou2_session = _bguizhou2_session()



bhisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU3_DATABASE
)
bguizhou3_engine = create_engine(bhisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou3_session = scoped_session(sessionmaker(bguizhou3_engine,autoflush=True))
bguizhou3_Base = declarative_base(bguizhou3_engine)
bguizhou3_session = _bguizhou3_session()


bhisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU4_DATABASE
)
bguizhou4_engine = create_engine(bhisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou4_session = scoped_session(sessionmaker(bguizhou4_engine,autoflush=True))
bguizhou4_Base = declarative_base(bguizhou4_engine)
bguizhou4_session = _bguizhou4_session()


bhisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU5_DATABASE
)
bguizhou5_engine = create_engine(bhisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou5_session = scoped_session(sessionmaker(bguizhou5_engine,autoflush=True))
bguizhou5_Base = declarative_base(bguizhou5_engine)
bguizhou5_session = _bguizhou5_session()

bhisdb6_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU6_DATABASE
)
bguizhou6_engine = create_engine(bhisdb6_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou6_session = scoped_session(sessionmaker(bguizhou6_engine,autoflush=True))
bguizhou6_Base = declarative_base(bguizhou6_engine)
bguizhou6_session = _bguizhou6_session()

bhisdb7_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU7_DATABASE
)
bguizhou7_engine = create_engine(bhisdb7_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou7_session = scoped_session(sessionmaker(bguizhou7_engine,autoflush=True))
bguizhou7_Base = declarative_base(bguizhou7_engine)
bguizhou7_session = _bguizhou7_session()

bhisdb8_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    BGUIZHOU8_DATABASE
)
bguizhou8_engine = create_engine(bhisdb8_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_bguizhou8_session = scoped_session(sessionmaker(bguizhou8_engine,autoflush=True))
bguizhou8_Base = declarative_base(bguizhou8_engine)
bguizhou8_session = _bguizhou8_session()


