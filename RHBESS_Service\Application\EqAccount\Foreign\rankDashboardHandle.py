#!/usr/bin/env python
# coding=utf-8
#@Information: 租赁大屏数据
#<AUTHOR> WYJ
#@Date         : 2023-07-26 14:55:38
#@FilePath     : \RHBESS_Service\Application\EqAccount\Foreign\zhilRealHandle copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-07-26 14:55:40

import json,os
import logging
from Application.Models.base_handler import BaseHandler
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import *
from Tools.DataEnDe.aes_cbc import AESUtil
from Tools.DataEnDe.MD5 import MD5Tool
from sqlalchemy import func,or_,and_



zulin_openId = 'RHZL_WSM1'  # 租赁企业标识
zulin_key = b"RHZLRHEZ12345678"
zulin_iv = b"RHZL12345678RHEZ"
# 自身的加密密匙和向量
openId = 'RHBESS01'
key = b"RHBESS1101022022"  # 西城区地区编码
iv = b"****************"  #展览路街道

class RankDashboardHandleIntetface(BaseHandler):
    def get(self, kt):
        try:
            f,data = self._verifyToken()
            if not f:
                return data
            if kt == 'StationInfosForRent':  # 对租赁提供的信息
                body = AESUtil.decrypt(data,zulin_key,zulin_iv)
                
                name = json.loads(body).get("name",'')
                if name != 'zulin':
                    return self.customError("name 错误")
                return self.returnTypeSuc('')
                # return self.returnTypeSuc(returndata)
            else:
                return self.pathError()
        except Exception as E:
            logging.info(E)
            return self.customError("非法入参")
        

    
    
    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean',None)
        logging.info('Bean:%s'%Bean)
        data = self.get_argument('data',None)
        logging.info('密文---data-------:%s'%data)
        md5 = MD5Tool.get_str_md5(data+zulin_openId)
        if not Bean or md5 != Bean:  # 身份验证失败
            return False ,self.tokenError()

        logging.info('明文*****data*************:%s'%data)
        return True ,data
    

