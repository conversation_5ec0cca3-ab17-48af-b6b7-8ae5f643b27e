#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\organization.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-28 17:43:36


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR
from Application.Models.User.organization_type import OrganizationType
import pandas as pd
from Tools.Utils.time_utils import timeUtils

class DeviceDelTable(user_Base):
    u'设备删除记录表'
    __tablename__ = "t_device_del_table"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(VARCHAR(250), nullable=False, comment=u"表名")
    del_id = Column(VARCHAR(50), nullable=True, comment=u"删除id")
    del_user_id = Column(VARCHAR(50), nullable=True, comment=u"删除人")
    del_time = Column(DateTime, nullable=True, comment=u"删除时间")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'name':'%s','del_id':'%s','del_user':'%s','del_time':%s'}" % (self.id, self.name ,self.del_id, self.del_user, self.del_time)


