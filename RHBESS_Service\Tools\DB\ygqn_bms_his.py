#!/usr/bin/env python
# coding=utf-8
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")

YGQN_HOSTNAME = model_config.get('mysql', "YGQN_HOSTNAME")
YGQN_PORT = model_config.get('mysql', "YGQN_PORT")

YGQN1_DATABASE = model_config.get('mysql', "YGQN1_DATABASE")
YGQN2_DATABASE = model_config.get('mysql', "YGQN2_DATABASE")
YGQN3_DATABASE = model_config.get('mysql', "YGQN3_DATABASE")
YGQN4_DATABASE = model_config.get('mysql', "YGQN4_DATABASE")
YGQN5_DATABASE = model_config.get('mysql', "YGQN5_DATABASE")
YGQN6_DATABASE = model_config.get('mysql', "YGQN6_DATABASE")

YGQN_USERNAME = model_config.get('mysql', "YGQN_USERNAME")
YGQN_PASSWORD = model_config.get('mysql', "YGQN_PASSWORD")


hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN1_DATABASE
)
ygqn1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn1_session = scoped_session(sessionmaker(ygqn1_engine,autoflush=True))
ygqn1_Base = declarative_base(ygqn1_engine)
bygqn1_session = _ygqn1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN2_DATABASE
)
ygqn2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn2_session = scoped_session(sessionmaker(ygqn2_engine,autoflush=True))
ygqn2_Base = declarative_base(ygqn2_engine)
bygqn2_session = _ygqn2_session()



hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN3_DATABASE
)
ygqn3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn3_session = scoped_session(sessionmaker(ygqn3_engine,autoflush=True))
ygqn3_Base = declarative_base(ygqn3_engine)
bygqn3_session = _ygqn3_session()


hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN4_DATABASE
)
ygqn4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn4_session = scoped_session(sessionmaker(ygqn4_engine,autoflush=True))
ygqn4_Base = declarative_base(ygqn4_engine)
bygqn4_session = _ygqn4_session()



hisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN5_DATABASE
)
ygqn5_engine = create_engine(hisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn5_session = scoped_session(sessionmaker(ygqn5_engine,autoflush=True))
ygqn5_Base = declarative_base(ygqn5_engine)
bygqn5_session = _ygqn5_session()


hisdb6_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN6_DATABASE
)
ygqn6_engine = create_engine(hisdb6_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn6_session = scoped_session(sessionmaker(ygqn6_engine,autoflush=True))
ygqn6_Base = declarative_base(ygqn6_engine)
bygqn6_session = _ygqn6_session()








