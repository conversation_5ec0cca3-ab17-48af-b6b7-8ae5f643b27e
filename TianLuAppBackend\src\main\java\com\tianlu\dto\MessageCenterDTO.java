package com.tianlu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@ApiModel("消息中心DTO")
public class MessageCenterDTO {
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("策略标题")
    private String title;

    @ApiModelProperty("消息类型：0：策略; 1：告警；2:运行分析；3：电池电压分析；4：电池温度分析, 5:电池过放风险")
    private Integer type;

    @ApiModelProperty("是否已读：1：已读；0：未读")
    private Integer isRead;

    @ApiModelProperty("是否处理：1：已处理；0：未处理；2：不展示(策略确认结果消息)")
    private Integer isHandle;

    @ApiModelProperty("确认状态：1：已确认/已反馈；0：未确认/为反馈")
    private Integer isVerify;

    @ApiModelProperty("附件url，多个以引文逗号隔开")
    private String files;

    @ApiModelProperty("策略反馈意见")
    private String opinion;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("告警ID")
    private Long alarmId;

    @ApiModelProperty("策略ID")
    private Long strategyId;

    @ApiModelProperty("站点ID")
    private Long stationId;

    @ApiModelProperty("通用关联id")
    private String relatedId;

    @ApiModelProperty("下发用户")
    private Long issueUser;

    @ApiModelProperty("策略标题(英文)")
    private String enTitle;

    @ApiModelProperty("策略反馈意见(英文)")
    private String enOpinion;
} 