# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/10 下午4:59
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : demo.py
# @Software : PyCharm
import concurrent.futures
import datetime
import json
import os
import time
import traceback
from copy import deepcopy

from django_redis import get_redis_connection

from TianLuAppBackend import settings
from apis.statistics_apis.db_link import time_range_by_dwd_for_web
from apis.user.models import PeakValleyNew, StationMeterUseTime, UnitPrice, Project
from apis.web2.project_account.gen_excel import generate_excel
from apis.web2.project_account.gen_pdf import gen_pdf
from common.database_pools import ads_db_tool
from tools.minio_tool import MinioTool


def convert_to_time_ranges_v3(lst):
    """
    将类似[0, 1,2,3, 7,8,9,18,19,20, 22,23]的数据转换成[0:00~3:00，7:00~9:00，18:00~23:00]
    """""
    if len(lst) == 0:
        return []
    # 将时间点转换为datetime对象
    time_format = "%H:%M"
    time_objects = [datetime.datetime.strptime(t, time_format) for t in lst]

    # 初始化时间段列表
    time_ranges = []

    # 起始时间点
    start_time = time_objects[0]
    prev_time = time_objects[0]

    for current_time in time_objects[1:]:
        if current_time - prev_time != datetime.timedelta(minutes=15):
            end_time = prev_time
            time_ranges.append(f"{start_time.strftime(time_format)}~{end_time.strftime(time_format)}")
            start_time = current_time
        prev_time = current_time

    # 添加最后一个时间段
    time_ranges.append(f"{start_time.strftime(time_format)}~{prev_time.strftime(time_format)}")
    return time_ranges

def get_station_day_data(station, target_day, select_sql, user_price, lang='zh'):
    hours_map = {2: [], 1: [], 0: [], -1: [], -2: []}

    if user_price:
        # 优先使用自定义电价
        title = user_price.name if lang == 'zh' else user_price.en_name

        price_dic = {
            "spike_chag_price": float(user_price.spike_chag_price) if user_price.spike_chag_price else '--',
            "peak_chag_price": float(user_price.peak_chag_price) if user_price.peak_chag_price else '--',
            "flat_chag_price": float(user_price.flat_chag_price) if user_price.flat_chag_price else '--',
            "valley_chag_price": float(
                user_price.valley_chag_price) if user_price.valley_chag_price else '--',
            "dvalley_chag_price": float(
                user_price.dvalley_chag_price) if user_price.dvalley_chag_price else '--',
            "spike_disg_price": float(user_price.spike_disg_price) if user_price.spike_disg_price else '--',
            "peak_disg_price": float(user_price.peak_disg_price) if user_price.peak_disg_price else '--',
            "flat_disg_price": float(user_price.flat_disg_price) if user_price.flat_disg_price else '--',
            "valley_disg_price": float(
                user_price.valley_disg_price) if user_price.valley_disg_price else '--',
            "dvalley_disg_price": float(
                user_price.dvalley_disg_price) if user_price.dvalley_disg_price else '--'
        }

        temp_dict_ = {
            "date": target_day.strftime('%Y-%m-%d'),
            "title": title,

            "spike": {"charge_price": float(price_dic.get('spike_chag_price')) or '--',
                      "discharge_price": float(price_dic.get('spike_disg_price')) or '--',
                      "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
            "peak": {"charge_price": float(price_dic.get('peak_chag_price')) or '--',
                     "discharge_price": float(price_dic.get('peak_disg_price')) or '--',
                     "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
            "flat": {"charge_price": float(price_dic.get('flat_chag_price')) or '--',
                     "discharge_price": float(price_dic.get('flat_disg_price')) or '--',
                     "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
            "valley": {"charge_price": float(price_dic.get('valley_chag_price')) or '--',
                       "discharge_price": float(price_dic.get('valley_disg_price')) or '--',
                       "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
            "dvalley": {"charge_price": float(price_dic.get('dvalley_chag_price')) or '--',
                            "discharge_price": float(price_dic.get('dvalley_disg_price')) or '--',
                            "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
        }

    else:
        # 拼接标题
        if lang == 'zh':
            title = station.province.name + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + \
                    dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
        else:
            province = station.province.en_name
            title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                     f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

        price_configs = PeakValleyNew.objects.filter(province=station.province, type=station.type,
                                                     level=station.level)

        day_prices = price_configs.filter(year_month=target_day.strftime("%Y-%m")).all()

        price_dic = {
            "spike_chag_price": '--',
            "peak_chag_price": '--',
            "flat_chag_price": '--',
            "valley_chag_price": '--',
            "dvalley_chag_price": '--',
            "spike_disg_price": '--',
            "peak_disg_price": '--',
            "flat_disg_price": '--',
            "valley_disg_price": '--',
            "dvalley_disg_price": '--'
        }

        if day_prices.exists():

            # 尖
            spike_prices = day_prices.filter(pv=2).all()
            price_dic[
                'spike_chag_price'] = spike_prices.first().price if spike_prices.exists() else '--'
            price_dic[
                'spike_disg_price'] = spike_prices.first().price if spike_prices.exists() else '--'
            if spike_prices.exists():
                hours_map[2] = [i.moment for i in spike_prices]

            # 峰
            peak_prices = day_prices.filter(pv=1).all()
            price_dic['peak_chag_price'] = peak_prices.first().price if peak_prices.exists() else '--'
            price_dic['peak_disg_price'] = peak_prices.first().price if peak_prices.exists() else '--'
            if peak_prices.exists():
                hours_map[1] = [i.moment for i in peak_prices]

            # 平
            flat_prices = day_prices.filter(pv=0).all()
            price_dic['flat_chag_price'] = flat_prices.first().price if flat_prices.exists() else '--'
            price_dic['flat_disg_price'] = flat_prices.first().price if flat_prices.exists() else '--'
            if flat_prices.exists():
                hours_map[0] = [i.moment for i in flat_prices]

            # 谷
            valley_prices = day_prices.filter(pv=-1).all()
            price_dic[
                'valley_chag_price'] = valley_prices.first().price if valley_prices.exists() else '--'
            price_dic[
                'valley_disg_price'] = valley_prices.first().price if valley_prices.exists() else '--'
            if valley_prices.exists():
                hours_map[-1] = [i.moment for i in valley_prices]

            # 深谷
            dvalley_prices = day_prices.filter(pv=-2).all()
            price_dic[
                'dvalley_chag_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
            price_dic[
                'dvalley_disg_price'] = dvalley_prices.first().price if dvalley_prices.exists() else '--'
            if dvalley_prices.exists():
                hours_map[-2] = [i.moment for i in dvalley_prices]

            for type_, hour_list in hours_map.items():
                hours_map[type_] = convert_to_time_ranges_v3(hour_list)

        temp_dict_ = {
            "date": target_day.strftime('%Y-%m-%d'),
            "title": title,

            "spike": {"charge_price": float(price_dic.get('spike_chag_price')) if price_dic.get('spike_chag_price') != '--' else '--',
                      "discharge_price": float(price_dic.get('spike_disg_price')) if price_dic.get('spike_disg_price') != '--' else '--',
                      "times": ','.join(hours_map.get(2)) if hours_map.get(2) else '--'},
            "peak": {"charge_price": float(price_dic.get('peak_chag_price')) if price_dic.get('peak_chag_price') != '--' else '--',
                     "discharge_price": float(price_dic.get('peak_disg_price')) if price_dic.get('peak_disg_price') != '--' else '--',
                     "times": ','.join(hours_map.get(1)) if hours_map.get(1) else '--'},
            "flat": {"charge_price": float(price_dic.get('flat_chag_price')) if price_dic.get('flat_chag_price') != '--' else '--',
                     "discharge_price": float(price_dic.get('flat_disg_price')) if price_dic.get('flat_disg_price') != '--' else '--',
                     "times": ','.join(hours_map.get(0)) if hours_map.get(0) else '--'},
            "valley": {"charge_price": float(price_dic.get('valley_chag_price')) if price_dic.get('valley_chag_price') != '--' else '--',
                       "discharge_price": float(price_dic.get('valley_disg_price')) if price_dic.get('valley_disg_price') != '--' else '--',
                       "times": ','.join(hours_map.get(-1)) if hours_map.get(-1) else '--'},
            "dvalley": {"charge_price": float(price_dic.get('dvalley_chag_price')) if price_dic.get('dvalley_chag_price') != '--' else '--',
                            "discharge_price": float(price_dic.get('dvalley_disg_price')) if price_dic.get('dvalley_disg_price') != '--' else '--',
                            "times": ','.join(hours_map.get(-2)) if hours_map.get(-2) else '--'},
        }

    # price_details.append(temp_dict_)

    # 查询每日电表数据
    day_spike_charge_values = []
    day_spike_discharge_values = []

    day_peak_charge_values = []
    day_peak_discharge_values = []

    day_flat_charge_values = []
    day_flat_discharge_values = []

    day_valley_charge_values = []
    day_valley_discharge_values = []

    detail = ads_db_tool.select_one(select_sql, station.english_name,
                                    target_day.strftime('%Y-%m-%d'))

    # price_dict = get_station_price(station, target_day)

    if detail:
        day_spike_charge_values.append(float(detail['pointed_chag']))
        day_spike_discharge_values.append(float(detail['pointed_disg']))

        day_peak_charge_values.append(float(detail['peak_chag']))
        day_peak_discharge_values.append(float(detail['peak_disg']))

        day_flat_charge_values.append(float(detail['flat_chag']))
        day_flat_discharge_values.append(float(detail['flat_disg']))

        day_valley_charge_values.append(float(detail['valley_chag']))
        day_valley_discharge_values.append(float(detail['valley_disg']))

        # 收益改查： ads_report_ems_station_income_1d
        income_sql = ("SELECT * FROM ads_report_ems_station_income_1d "
                      " where station=%s and day=%s")
        income = ads_db_tool.select_one(income_sql, station.english_name, target_day.strftime('%Y-%m-%d'))
        if income:
            d_dict = {'spike_charge_income': float(income['pointed_chag_income']),
                      'spike_discharge_income': float(income['pointed_disg_income']),
                      'spike_charge': sum(day_spike_charge_values), 'spike_discharge': sum(day_spike_discharge_values),
                      'peak_charge_income': float(income['peak_chag_income']),
                      'peak_discharge_income': float(income['peak_disg_income']),
                      'peak_charge': sum(day_peak_charge_values), 'peak_discharge': sum(day_peak_discharge_values),
                      'flat_charge_income': float(income['flat_chag_income']),
                      'flat_discharge_income': float(income['flat_disg_income']),
                      'flat_charge': sum(day_flat_charge_values), 'flat_discharge': sum(day_flat_discharge_values),
                      'valley_charge_income': float(income['valley_chag_income']),
                      'valley_discharge_income': float(income['valley_disg_income']),
                      'valley_charge': sum(day_valley_charge_values),
                      'valley_discharge': sum(day_valley_discharge_values),
                      }
        else:
            d_dict = {'spike_charge_income': 0, 'spike_discharge_income': 0,
                      'spike_charge': sum(day_spike_charge_values),
                      'spike_discharge': sum(day_spike_discharge_values),

                      'peak_charge_income': 0, 'peak_discharge_income': 0,
                      'peak_charge': sum(day_peak_charge_values),
                      'peak_discharge': sum(day_peak_discharge_values),

                      'flat_charge_income': 0, 'flat_discharge_income': 0,
                      'flat_charge': sum(day_flat_charge_values),
                      'flat_discharge': sum(day_flat_discharge_values),

                      'valley_charge_income': 0, 'valley_discharge_income': 0,
                      'valley_charge': sum(day_valley_charge_values),
                      'valley_discharge': sum(day_valley_discharge_values),
                      }

        # 每天的尖峰平谷充放电量统计
        day_spike_charge = -round(sum(day_spike_charge_values), 2)
        day_spike_discharge = round(sum(day_spike_discharge_values), 2)

        day_peak_charge = -round(sum(day_peak_charge_values), 2)
        day_peak_discharge = round(sum(day_peak_discharge_values), 2)

        day_flat_charge = -round(sum(day_flat_charge_values), 2)
        day_flat_discharge = round(sum(day_flat_discharge_values), 2)

        day_valley_charge = -round(sum(day_valley_charge_values), 2)
        day_valley_discharge = round(sum(day_valley_discharge_values), 2)

        day_total_charge = round(day_spike_charge + day_peak_charge + day_flat_charge + day_valley_charge, 2)
        day_total_discharge = round(
            day_spike_discharge + day_peak_discharge + day_flat_discharge + day_valley_discharge, 2)

        temp_dict = {
            "date": target_day.strftime('%Y-%m-%d'),
            "day_spike_charge": day_spike_charge,
            "day_spike_discharge": day_spike_discharge,
            "day_peak_charge": day_peak_charge,
            "day_peak_discharge": day_peak_discharge,
            "day_flat_charge": day_flat_charge,
            "day_flat_discharge": day_flat_discharge,
            "day_valley_charge": day_valley_charge,
            "day_valley_discharge": day_valley_discharge,
            "day_total_charge": day_total_charge,
            "day_total_discharge": day_total_discharge
        }

        # days_data.append(temp_dict)

        return temp_dict_, temp_dict, d_dict
    return None


def get_station_all_data(station, start_time, end_time, lang='zh'):
    """
    结算单
    :param project_id:
    :param start_day:
    :param end_day:
    :return:
    """
    if station.master_station.mode == 1:
        args = ['TPAP', 'PATC', 'FAPC', 'PAPL', 'PAVC', 'TAPIR', 'RATC', 'RAPC', 'RAPL', 'RAVC', 'PT',
                'CT']
    else:
        args = ['STPAP', 'SPATC', 'SFAPC', 'SPAPL', 'SPAVC', 'STAPIR', 'SRATC', 'SRAPC', 'SRAPL', 'SRAVC', 'SPT', 'SCT']

    if station.master_station.mode == 1:
        select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                      " where station=%s and device_type=0 and day=%s")
    else:
        select_sql = ("SELECT * FROM ads_report_ems_chag_disg_1d"
                      " where station=%s and device_type=1 and day=%s")

    # select_sql = ("SELECT * FROM ads_report_chag_disg_union_1d"
    #               " where station=%s and station_type=0 and day=%s")

    station_info = {
        'station_name': station.station_name,
        'station_number': station.meter_number if station.meter_number else '--',
        'rate': '--',  # TODO 实时获取
        'is_account': False
        # 'account_data': {},
        # 'days_data': [],
        # 'price_details': []
    }

    # 只时间在设置的使用范围内使用结算表
    meter_use_time = StationMeterUseTime.objects.filter(station=station, is_use=1).first()
    if meter_use_time:
        if meter_use_time.end_time:
            is_use_account = (station.is_account and meter_use_time and
                              meter_use_time.start_time <= start_time and end_time <= meter_use_time.end_time)
        else:
            is_use_account = (station.is_account and meter_use_time and
                              meter_use_time.start_time <= start_time)
    else:
        is_use_account = False

    # 外接结算电表
    if is_use_account:

        station_info['is_account'] = True

        # 判断是否存在自定义电价配置
        user_price = UnitPrice.objects.filter(station=station.master_station, start__gte=start_time.date(),
                                              end__lte=end_time.date(), delete=0)

        # 优先使用自定义电价
        if user_price.exists():
            user_price = user_price.last()

            title = user_price.name if lang == 'zh' else user_price.en_name

        # 使用代理购电价格
        else:
            if lang == 'zh':
                # 拼接标题
                title = station.province.name + dict(PeakValleyNew.LEVEL_CHOICE)[station.level] + \
                        dict(PeakValleyNew.TYPE_CHOICE)[station.type] + "代理购电价格"
            else:
                province = station.province.en_name
                title = (f"{dict(PeakValleyNew.EN_TYPE_CHOICE)[station.type]}, "
                         f"{dict(PeakValleyNew.EN_LEVEL_CHOICE)[station.level]}, {province}")

        # 时间范围内的结算数据
        account_data = {}

        # 每日的结算电量数据
        days_data = []

        # 每日的结算电价数据
        price_details = []

        # 查询抄表底码
        data = time_range_by_dwd_for_web(station.english_name, 'cumulant', 'ems', 'EMS', start_time,
                                         end_time, *args)

        if data:
            sorted_data = sorted(data, key=lambda x: x['time'])
            first_data = sorted_data[0]
            last_data = sorted_data[-1]
            if station.master_station.mode != 1:
                first_SPATC = first_data['SPATC']
                last_SPATC = last_data['SPATC']

                first_SFAPC = first_data['SFAPC']
                last_SFAPC = last_data['SFAPC']

                first_SPAPL = first_data['SPAPL']
                last_SPAPL = last_data['SPAPL']

                first_SPAVC = first_data['SPAVC']
                last_SPAVC = last_data['SPAVC']

                first_SRATC = first_data['SRATC']
                last_SRATC = last_data['SRATC']

                first_SRAPC = first_data['SRAPC']
                last_SRAPC = last_data['SRAPC']

                first_SRAPL = first_data['SRAPL']
                last_SRAPL = last_data['SRAPL']

                first_SRAVC = first_data['SRAVC']
                last_SRAVC = last_data['SRAVC']

                first_SPT = first_data['SPT'] if first_data['SPT'] else '--'
                first_SCT = first_data['SCT'] if first_data['SCT'] else '--'

                last_SPT = last_data['SPT'] if last_data['SPT'] else '--'
                last_SCT = last_data['SCT'] if last_data['SCT'] else '--'

            else:
                first_SPATC = first_data['PATC']
                last_SPATC = last_data['PATC']

                first_SFAPC = first_data['FAPC']
                last_SFAPC = last_data['FAPC']

                first_SPAPL = first_data['PAPL']
                last_SPAPL = last_data['PAPL']

                first_SPAVC = first_data['PAVC']
                last_SPAVC = last_data['PAVC']

                first_SRATC = first_data['RATC']
                last_SRATC = last_data['RATC']

                first_SRAPC = first_data['RAPC']
                last_SRAPC = last_data['RAPC']

                first_SRAPL = first_data['RAPL']
                last_SRAPL = last_data['RAPL']

                first_SRAVC = first_data['RAVC']
                last_SRAVC = last_data['RAVC']

                first_SPT = first_data['PT'] if first_data['PT'] else '--'
                first_SCT = first_data['CT'] if first_data['CT'] else '--'

                last_SPT = last_data['PT'] if last_data['PT'] else '--'
                last_SCT = last_data['CT'] if last_data['CT'] else '--'

            spike_charge_income = 0
            spike_discharge_income = 0
            spike_charge = 0
            spike_discharge = 0

            peak_charge_income = 0
            peak_discharge_income = 0
            peak_charge = 0
            peak_discharge = 0

            flat_charge_income = 0
            flat_discharge_income = 0
            flat_charge = 0
            flat_discharge = 0

            valley_charge_income = 0
            valley_discharge_income = 0
            valley_charge = 0
            valley_discharge = 0

            # 遍历每一天计算当日收益
            target_day = start_time
            end_day_ = end_time

            with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
                futures = list()

                while target_day <= end_day_:
                    future = executor.submit(get_station_day_data, station, target_day, select_sql, user_price, lang)
                    futures.append(future)
                    target_day = target_day + datetime.timedelta(days=1)

                for f in concurrent.futures.as_completed(futures):
                    if f.result():
                        temp_dict_, temp_dict, d_dict = f.result()
                        price_details.append(temp_dict_)
                        days_data.append(temp_dict)

                        spike_charge_income -= d_dict['spike_charge_income']
                        spike_discharge_income += d_dict['spike_discharge_income']
                        spike_charge += d_dict['spike_charge']
                        spike_discharge += d_dict['spike_discharge']

                        peak_charge_income -= d_dict['peak_charge_income']
                        peak_discharge_income += d_dict['peak_discharge_income']
                        peak_charge += d_dict['peak_charge']
                        peak_discharge += d_dict['peak_discharge']

                        flat_charge_income -= d_dict['flat_charge_income']
                        flat_discharge_income += d_dict['flat_discharge_income']
                        flat_charge += d_dict['flat_charge']
                        flat_discharge += d_dict['flat_discharge']

                        valley_charge_income -= d_dict['valley_charge_income']
                        valley_discharge_income += d_dict['valley_discharge_income']
                        valley_charge += d_dict['valley_charge']
                        valley_discharge += d_dict['valley_discharge']

            price_details = sorted(price_details, key=lambda x: x['date'])
            days_data = sorted(days_data, key=lambda x: x['date'])

            total_income = (round(spike_charge_income, 4) + round(peak_charge_income, 4) + round(flat_charge_income,
                                                                                                 4) +
                            round(valley_charge_income, 4) + round(spike_discharge_income, 4) + round(
                        peak_discharge_income, 4) +
                            round(flat_discharge_income, 4) + round(valley_discharge_income, 4))

        # 无底表数据时，显示--
        else:
            first_SPATC = '--'
            last_SPATC = '--'

            first_SFAPC = '--'
            last_SFAPC = '--'

            first_SPAPL = '--'
            last_SPAPL = '--'

            first_SPAVC = '--'
            last_SPAVC = '--'

            first_SRATC = '--'
            last_SRATC = '--'

            first_SRAPC = '--'
            last_SRAPC = '--'

            first_SRAPL = '--'
            last_SRAPL = '--'

            first_SRAVC = '--'
            last_SRAVC = '--'

            first_SPT = '--'
            first_SCT = '--'

            last_SPT = '--'
            last_SCT = '--'

            spike_charge_income = '--'
            spike_discharge_income = '--'
            spike_charge = '--'
            spike_discharge = '--'

            peak_charge_income = '--'
            peak_discharge_income = '--'
            peak_charge = '--'
            peak_discharge = '--'

            flat_charge_income = '--'
            flat_discharge_income = '--'
            flat_charge = '--'
            flat_discharge = '--'

            valley_charge_income = '--'
            valley_discharge_income = '--'
            valley_charge = '--'
            valley_discharge = '--'

            total_income = '--'

        if not '--' in [first_SPT, first_SCT, last_SPT, last_SCT]:
            station_info['rate'] = round((float(first_SPT) * float(first_SCT) + float(last_SPT) * float(last_SCT)) / 2,
                                         2)
        elif '--' in [first_SPT, first_SCT] and not '--' in [last_SPT, last_SCT]:
            station_info['rate'] = round(float(last_SPT) * float(last_SCT), 2)
        elif '--' in [last_SPT, last_SCT] and not '--' in [first_SPT, first_SCT]:
            station_info['rate'] = round(float(first_SPT) * float(first_SCT), 2)
        else:
            station_info['rate'] = '--'

        account_data['charge'] = {
            'spike': {
                'first': float(first_SRATC) if first_SRATC != '--' else '--',
                'last': float(last_SRATC) if last_SRATC != '--' else '--',
                'count': round(spike_charge, 2) if spike_charge != '--' else '--',
                'income': round(spike_charge_income, 2) if spike_charge_income != '--' else '--',
            },
            'peak': {
                'first': float(first_SRAPC) if first_SRAPC != '--' else '--',
                'last': float(last_SRAPC) if last_SRAPC != '--' else '--',
                'count': round(peak_charge, 2) if peak_charge != '--' else '--',
                'income': round(peak_charge_income, 2) if spike_charge_income != '--' else '--'
            },
            'flat': {
                'first': float(first_SRAPL) if first_SRAPL != '--' else '--',
                'last': float(last_SRAPL) if last_SRAPL != '--' else '--',
                'count': round(flat_charge, 2) if flat_charge != '--' else '--',
                'income': round(flat_charge_income, 2) if spike_charge_income != '--' else '--'
            },
            'valley': {
                'first': float(first_SRAVC) if first_SPAVC != '--' else '--',
                'last': float(last_SRAVC) if last_SRAVC != '--' else '--',
                'count': round(valley_charge, 2) if valley_charge != '--' else '--',
                'income': round(valley_charge_income, 2) if spike_charge_income != '--' else '--'
            }
        }
        account_data['discharge'] = {
            'spike': {
                'first': float(first_SPATC) if first_SPATC != '--' else '--',
                'last': float(last_SPATC) if last_SPATC != '--' else '--',
                'count': round(spike_discharge, 2) if spike_discharge != '--' else '--',
                'income': round(spike_discharge_income, 2) if spike_charge_income != '--' else '--'
            },
            'peak': {
                'first': float(first_SFAPC) if first_SFAPC != '--' else '--',
                'last': float(last_SFAPC) if last_SFAPC != '--' else '--',
                'count': round(peak_discharge, 2) if peak_discharge != '--' else '--',
                'income': round(peak_discharge_income, 2) if spike_charge_income != '--' else '--'
            },
            'flat': {
                'first': float(first_SPAPL) if first_SPAPL != '--' else '--',
                'last': float(last_SPAPL) if last_SPAPL != '--' else '--',
                'count': round(flat_discharge, 2) if flat_discharge != '--' else '--',
                'income': round(flat_discharge_income, 2) if spike_charge_income != '--' else '--'
            },
            'valley': {
                'first': float(first_SPAVC) if first_SPAVC != '--' else '--',
                'last': float(last_SPAVC) if last_SPAVC != '--' else '--',
                'count': round(valley_discharge, 2) if valley_discharge != '--' else '--',
                'income': round(valley_discharge_income, 2) if spike_charge_income != '--' else '--'
            }
        }
        account_data['total_income'] = round(total_income, 2) if spike_charge_income != '--' else '--'
        account_data['total_income_'] = round(total_income, 4) if spike_charge_income != '--' else '--'
        account_data['price_url'] = title

        station_info['account_data'] = account_data
        station_info['days_data'] = days_data
        station_info['price_details'] = price_details

    return station_info


def save_project_account_detail_to_redis(project_id, start_day, end_day, user_id, lang='zh'):
    try:
        start_time = datetime.datetime.strptime(start_day, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.datetime.strptime(end_day, "%Y-%m-%d %H:%M:%S")
    except Exception as e:
        print(traceback.print_exc())
        return

    # 查询数据
    try:
        project = Project.objects.get(id=int(project_id), is_used=1)
    except Exception as e:
        return

    # 结算电表迁移至各个从站，其中标准主从只显示从站，不显示000主站
    stations = project.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()

    # 查询地址
    address = ''
    if stations.exists():
        address = stations.first().address if lang == 'zh' else stations.first().en_address

    return_dic = {}

    return_dic['address'] = address
    return_dic['project'] = project.name
    return_dic['account_month'] = end_time.strftime("%Y年%m月") if lang == 'zh' else end_time.strftime("%Y-%m")
    return_dic['account_start'] = start_day
    return_dic['account_end'] = end_day
    return_dic['station_number'] = '/'
    return_dic['stations_info'] = dict(account_info=[], detaiils_info=[], price_info={})

    temp_dict = {
        'STPAP': '当前正向有功总电量',
        'SPATC': '当前正向有功尖电量',
        'SFAPC': '当前正向有功峰电量',
        'SPAPL': '当前正向有功平电量',
        'SPAVC': '当前正向有功谷电量',
        'STAPIR': '当前反向有功总电量',
        'SRATC': '当前反向有功尖电量',
        'SRAPC': '当前反向有功峰电量',
        'SRAPL': '当前反向有功平电量',
        'SRAVC': '当前反向有功谷电量'
    }

    temp_list = [get_station_all_data(station, start_time, end_time, lang) for station in stations if
                 get_station_all_data(station, start_time, end_time, lang)['is_account']]

    return_dic['is_account'] = True if len(temp_list) else False

    if return_dic['is_account']:
        # 储能电站削峰填谷总收益
        return_dic['project_total_income'] = round(
            sum([i['account_data']['total_income_'] for i in temp_list if i['account_data']['total_income_'] != '--']),
            2)

        # 电价信息只保留1份，避免重复，同一个项目的所有站点，电价信息一致====> 考虑到多个电站时，有的使用代理电价，有的使用自定义小时电价，所以相同时保留一份，不同时均保留
        return_dic['price_infos'] = []
        temp_list_ = []
        for item in temp_list:
            if item['account_data']['price_url'] not in temp_list_ and item['price_details']:
                return_dic['price_infos'].append(deepcopy(item['price_details']))
                temp_list_.append(item['account_data']['price_url'])

            item.pop('price_details')

        return_dic['stations_info'] = sorted(temp_list, key=lambda x: x['station_name'])

        if lang == 'zh':
            return_dic['note'] = ("说明:\n"
                                  "1. 各峰谷标志的充放电量来自于并网点结算电表数据。\n"
                                  "2. 结算电量 = 电表倍率*（本次报表底码 - 上次抄表底码）。\n"
                                  "3. 结算金额 = ∑(储能系统在各峰谷时段并网点结算电表充放电电量 ×对应时段单位电价)。\n"
                                  "4. 削峰填谷收益 = 正向有功（放电）结算金额-反向有功（充电）结算金额。")
        else:
            return_dic['note'] = ("Instruction:\n"
                                  "1. Energy data is measured from settlement meters.\n"
                                  "2. Settlement Energy = Multiplier×(Current Meter Reading - Last Meter Reading).\n"
                                  "3. Settlement Amount = ∑（Price by each hour × Energy by each hour）.\n"
                                  "4. Peak Shaving Profit = Amount by discharging -Amount by charging.")

        excel_path = os.path.join(settings.BASE_DIR, 'static/excel')
        if lang == 'zh':
            excel_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.xlsx"
            pdf_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.pdf"
            doc_file_name = f"天禄智控-{return_dic['account_month']}{return_dic['project']}结算单{int(time.time())}.doc"
        else:
            excel_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.xlsx"
            pdf_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.pdf"
            doc_file_name = f"C&I ESS Operation-{return_dic['account_month']} {return_dic['project']} Ballance Sheet-{int(time.time())}.doc"
        if not os.path.exists(excel_path):
            os.mkdir(excel_path)
        excel_file = os.path.join(excel_path, excel_file_name)
        pdf_file = os.path.join(excel_path, pdf_file_name)

        # 上传Minio
        minio_client = MinioTool()
        minio_client.create_bucket('download')
        redis_conn = get_redis_connection("default")

        # 生成 excel 并上传到 Minio ，缓存下载链接到redis
        generate_excel(return_dic, excel_file, lang)
        excel_export_url = minio_client.upload_local_file(excel_file_name, excel_file, 'download')
        key_excel = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime(
            "%Y%m%d") + '_' + end_time.strftime("%Y%m%d") + '_excel'
        redis_conn.set(key_excel, excel_export_url, 60 * 5)

        # 生成 pdf 并上传到 Minio ，缓存下载链接到redis
        gen_pdf(return_dic, excel_path, doc_file_name, pdf_file_name)
        pdf_export_url = minio_client.upload_local_file(pdf_file_name, pdf_file, 'download')
        key_pdf = 'tianlu_' + str(user_id) + project_id + '_' + start_time.strftime("%Y%m%d") + '_' + end_time.strftime("%Y%m%d") + '_pdf'
        redis_conn.set(key_pdf, pdf_export_url, 60 * 5)