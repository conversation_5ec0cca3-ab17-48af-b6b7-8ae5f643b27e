#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-10 08:38:14
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\HaLun\custom.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-06 11:25:42
import json
import uuid
from socket import AI_PASSIVE
from Application.Models.User.authority import Authority
from Application.Models.User.device_annex_t import DeviceAnnex
from Application.Models.User.device_del_table_t import DeviceDelTable
from Application.Models.User.device_library_t import DeviceLibrary
from Application.Models.User.equipmen_t import Equipment
from Application.Models.User.organization import Organization
from Application.Models.User.remote_control import RemoteControl
from Application.Models.User.remote_letter_t import RemoteLetter
from Application.Models.User.remote_modula import RemoteModula
from Application.Models.User.remote_tele_t import RemoteTele
from Application.Models.User.project_device_t import ProjectDevice
from Application.Models.User.device_type_t import DeviceType
from Application.Models.User.device_no_t import DeviceNo
from Application.Models.User.device_unit_t import DeviceUnit
from Application.Models.User.manufacturer import Manufacturer

from Application.Models.User.user import User
import numpy
import re
import logging
import tornado.web
import datetime
from datetime import datetime
from sqlalchemy import func, or_, and_, JSON
from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
from Tools.DB.mysql_scada import DEBUG
from Tools.DB.mysql_user import user_session
from Application.HistoryData.his_bams import *
from Application.Models.User.ele_price_t import ElePrice
from Application.Models.User.project_info_t import ProjectInfo
from Application.Models.User.province_c import Province
from Application.Models.User.city_c import City
import os
import zipfile
import uuid
from Tools.Utils.mimio_tool import upload_file, MinioTool
import pandas as pd
import io

class ProjectIntetface(BaseHandler):
    ''' 项目信息配置功能汇总 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            data = []
            if kt == 'GetVoltClass':#电压等级
                d = [{'id':'380V',"name":'380V'},{'id':'10kV',"name":'10kV'},{'id':'35kV',"name":'35kV'},{'id':'110kV',"name":'110kV'},{'id':'220kV',"name":'220kV'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetCommMode':#通讯方式
                if lang=='en':
                    d = [{'id': 1, "name": 'Direct data acquisition'}, {'id': 2, "name": 'Data forwarding'}]
                else:
                    d = [{'id':1,"name":'数据直采'},{'id':2,"name":'数据转发'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetProjectList': # 所有配置页面
                descr = self.get_argument('descr',None) #项目名称
                energy_storage = self.get_argument('energy_storage',None) #项目类型
                status = self.get_argument('status',None) #项目状态
                head_main = self.get_argument('head_main',None) #负责人
                own_name = self.get_argument('own_name',None) #业主
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                filter = [ProjectInfo.is_use == '1']
                if DEBUG:
                    logging.info('descr:%s,energy_storage:%s,status:%s,head_main:%s,own_name:%s,pageNum:%s,pageSize:%s'%(descr,energy_storage,status,head_main,own_name,pageNum,pageSize))
                if descr:
                    if lang=='en':
                        filter.append(ProjectInfo.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(ProjectInfo.descr.like('%' + descr + '%'))
                if energy_storage:
                    filter.append(ProjectInfo.energy_storage==energy_storage)
                if head_main:
                    if lang=='en':
                        filter.append(ProjectInfo.en_head_main == head_main)
                    else:
                        filter.append(ProjectInfo.head_main == head_main)
                if own_name:
                    if lang=='en':
                        filter.append(ProjectInfo.en_own_name.like('%' + own_name + '%'))
                    else:
                        filter.append(ProjectInfo.own_name.like('%' + own_name + '%'))

                total_all = user_session.query(func.count(ProjectInfo.id)).filter(*filter).scalar()
                pages = user_session.query(ProjectInfo.id,ProjectInfo.descr,ProjectInfo.energy_storage,ProjectInfo.electric_power,ProjectInfo.volume,ProjectInfo.start_ts,ProjectInfo.head_main,ProjectInfo.own_name,ProjectInfo.data_acc_date,ProjectInfo.name
                                           ,ProjectInfo.en_descr,ProjectInfo.en_head_main,ProjectInfo.en_own_name).filter(*filter).order_by(ProjectInfo.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                now_time = timeUtils.getNewDayStartStr()
                total = 0
                for pag in pages:
                    obj = {}
                    if pag[8] <= now_time:
                        pag = list(eval(str(pag)))
                        if lang=='en':
                            obj['state'] = 'Connected'
                        else:
                            obj['state'] = '已接入'
                    else:
                        if lang=='en':
                            obj['state'] = 'connecting'
                        else:
                            obj['state'] = '接入中'
                    obj['id'] = pag[0]

                    obj['energy_storage'] = pag[2]
                    obj['electric_power'] = pag[3]
                    obj['volume'] = pag[4]
                    obj['start_ts'] = pag[5]
                    obj['data_acc_date'] = pag[8]
                    obj['name'] = pag[9]
                    if lang=='en':
                        obj['descr'] = pag[10]
                        obj['head_main'] = pag[11]
                        obj['own_name'] = pag[12]
                    else:
                        obj['descr'] = pag[1]
                        obj['head_main'] = pag[6]
                        obj['own_name'] = pag[7]
                    if status:
                        if  status == obj['state']:
                            total+=1
                            data.append(obj)
                    else:
                        data.append(obj)
                return self.returnTotalSuc(data,total if total else total_all)
            elif kt == 'GetProjectInfoList': # 获取项目详情
                id = self.get_argument('id', None)  # 项目id
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id ==id,DeviceLibrary.is_use == 1).first()
                page_ = user_session.query(ElePrice).filter(ElePrice.station == page.name,ElePrice.is_use == 1,ElePrice.years==datestart).order_by(ElePrice.id.asc()).all()
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                page1=eval(str(page))
                if lang == 'en':
                    replaced_data = ProjectInfo.replace_en_fields(page1, "")
                    page1.update(replaced_data)
                list1 = []
                aaa = {}
                bbb = {}
                if page_:
                    for p in page_:
                        if p.name in aaa.keys():
                            if lang =='en':
                                aaa[p.en_name].append({"start_time": p.start_time, "end_time": p.end_time, "descr": p.en_descr,"rate": p.rate})
                                bbb[p.en_name] = p.month
                            else:
                                aaa[p.name].append({"start_time":p.start_time,"end_time":p.end_time,"descr": p.descr,"rate": p.rate})
                                bbb[p.name] = p.month
                        else:
                            if lang =='en':
                                aaa[p.en_name] = []
                                aaa[p.en_name].append({"start_time": p.start_time, "end_time": p.end_time, "descr": p.en_descr,"rate": p.rate})
                                bbb[p.en_name] = p.month
                            else:
                                aaa[p.name] = []
                                aaa[p.name].append({"start_time": p.start_time, "end_time": p.end_time, "descr": p.descr, "rate": p.rate})
                                bbb[p.name] = p.month

                if aaa:
                    for key in aaa.keys():
                        obj = {'name': '', 'months': '', 'time_': []}
                        obj['name'] = key
                        list_ = eval(bbb[key])
                        obj['months'] = list_
                        obj['time_'] = aaa[key]
                        list1.append(obj)
                page1['list1'] = list1
                return self.returnTypeSuc(page1)
            elif kt == 'GetProvinceList': # 所有省份
                data = ProvinceCity.ProvinceList(lang)
                return self.returnTypeSuc(data)
            elif kt == 'GetOrganization': # 所有业主
                pages = user_session.query(User.id,User.name,User.en_name).filter(User.organization_id== 31).order_by(User.id.desc()).all()
                if pages:
                    for pag in pages:
                        obj = {}
                        obj['id'] = pag[0]
                        if lang=='en':
                            obj['name'] = pag[2]
                        else:
                            obj['name'] = pag[1]
                        data.append(obj)
                return self.returnTypeSuc(data)
            elif kt == 'GetCityList': # 所有省份下的市
                id = self.get_argument('id', None)  # 省id
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                data = ProvinceCity.CityList(id,lang)
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceLibraryList': # 所有设备库
                device_mo = self.get_argument('device_mo',None)#设备型号
                device_ty = self.get_argument('device_ty',None)#设备类型
                vender = self.get_argument('vender', None)#厂家
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                filter = [DeviceLibrary.is_use==1]
                if DEBUG:
                    logging.info('device_mo:%s,device_ty:%s,vender:%s,pageNum:%s,pageSize:%s'
                                 %(device_mo,device_ty,vender,pageNum,pageSize))
                if device_mo:
                    if lang =='en':
                        filter.append(DeviceLibrary.en_device_mo.like('%' + device_mo + '%'))
                    else:
                        # 查询设备型号满足的设备型号id
                        model_device_no = user_session.query(DeviceNo).filter(
                            DeviceNo.name.like('%' + device_mo + '%')).all()
                        if model_device_no:
                            device_no_ids = [i.id for i in model_device_no]
                            filter.append(DeviceLibrary.device_no_id.in_(device_no_ids))
                        else:
                            return self.returnTotalSuc(data, 0)
                            # filter.append(DeviceLibrary.device_mo.like('%' + device_mo + '%'))
                if device_ty:
                    if lang == 'en':
                        filter.append(DeviceLibrary.en_device_ty == device_ty)
                    else:
                        # 查询设备类型
                        device_type_data = user_session.query(DeviceType).filter(DeviceType.name == device_ty).first()
                        if device_type_data:
                            filter.append(DeviceLibrary.device_type_id == device_type_data.id)
                        else:
                            return self.returnTotalSuc(data,0)
                        # filter.append(DeviceLibrary.device_ty==device_ty)
                if vender:
                    if lang == 'en':
                        filter.append(DeviceLibrary.en_vender.like('%' + vender + '%'))
                    else:
                        # 查询厂家满足的id
                        model_factory = user_session.query(Manufacturer).filter(
                            Manufacturer.descr.like('%' + vender + '%')).all()
                        if model_factory:
                            factory_ids = [i.id for i in model_factory]
                            filter.append(DeviceLibrary.factory_id.in_(factory_ids))
                        else:
                            return self.returnTotalSuc(data, 0)
                        # filter.append(DeviceLibrary.vender.like('%' + vender + '%'))

                total = user_session.query(func.count(DeviceLibrary.id)).filter(*filter).scalar()
                pages = user_session.query(DeviceLibrary).filter(*filter).order_by(DeviceLibrary.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if pages:
                    for pag in pages:
                        obj = eval(str(pag))
                        if lang == 'en':
                            replaced_data = DeviceLibrary.replace_en_fields(obj, "")
                            obj.update(replaced_data)
                        if obj['device_no_id']:
                            device_no_data = user_session.query(DeviceNo).filter(
                                DeviceNo.id == obj['device_no_id']).first()
                            if device_no_data:
                                obj['device_mo'] = device_no_data.name
                        if obj['device_type_id']:
                            device_type_data = user_session.query(DeviceType).filter(
                                DeviceType.id == obj['device_type_id']).first()
                            if device_type_data:
                                obj['device_ty'] = device_type_data.name
                        if obj['factory_id']:
                            device_factory_data = user_session.query(Manufacturer).filter(
                                Manufacturer.id == obj['factory_id']).first()
                            if device_factory_data:
                                obj['vender'] = device_factory_data.descr
                        page_ = user_session.query(DeviceAnnex.id,DeviceAnnex.file_name,DeviceAnnex.annex_url,DeviceAnnex.en_file_name).filter(DeviceAnnex.device_id == pag.id).all()
                        if page_:
                            obj['Annex_id'] = []
                            obj['file_name'] = []
                            obj['file_url'] = []
                            for pag_ in page_:
                                obj['Annex_id'].append(str(pag_[0]))
                                if lang=='en':
                                    obj['file_name'].append(pag_[3])
                                else:
                                    obj['file_name'].append(pag_[1])
                                obj['file_url'].append(pag_[2])
                        data.append(obj)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetDownloadDeviceAnnex':#下载设备附件
                id = self.get_argument('id', [])  # 设备附件id
                if not id or id == '[]':
                    return self.customError('参数不完整')
                id = json.loads(id)
                if lang=='en':
                    dsp = user_session.query(DeviceAnnex.annex_url, DeviceAnnex.en_file_name).filter(DeviceAnnex.id.in_(id)).all()
                else:
                    dsp = user_session.query(DeviceAnnex.annex_url, DeviceAnnex.file_name).filter(DeviceAnnex.id.in_(id)).all()
                filestr = dsp[0][0]
                files = filestr.split('#')
                file_dir = '/home/<USER>/deviceDownload/' + timeUtils.getNewTimeStr()[:4]
                # file_dir = '/home/<USER>/deviceDownload/'+ timeUtils.getNewTimeStr()[:4]
                if not os.path.exists(file_dir):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_dir)

                # 新生成的压缩文件.zip的存放路径
                compressPathName = file_dir + '/'+time.strftime("%Y%m%d%H%M%S", time.localtime())+ ' DeviceData.zip'  # （1）创建zip压缩对像
                zip_file = zipfile.ZipFile(compressPathName, 'w', zipfile.ZIP_DEFLATED)

                for i in dsp:
                    file_path = os.path.join(file_dir, i[0])  # 会返回压缩包内所有文件名的列表。
                    zip_file.write(file_path, i[1])  # （2）将文件写入zip压缩文件——正常压缩，不出现多层目录#
                    # zip_file.write(file_path)  # （2）将文件写入zip压缩文件——直接压缩，出现多层目录
                zip_file.close()  # （3）关闭zip对象

                data.append(compressPathName)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectDeviceDatas':  # 获取项目下的设备信息
                data = {}
                id = self.get_argument('id', None)  # 项目id
                name = self.get_argument('name', None)  # 储能单元
                area = self.get_argument('area', None)  # 分区
                sn = self.get_argument('sn', None)  # 设备编号
                type_name = self.get_argument('type_name', None)  # 类型名称
                factory_name = self.get_argument('factory_name', None)  # 厂家
                device_no_name = self.get_argument('device_no_name', None)  # 设备型号
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 10))
                filter = [ProjectDevice.is_use == 1, ProjectDevice.project_id == id]

                pro_data = user_session.query(ProjectInfo).filter(ProjectInfo.id == id).first() # 项目信息
                if not pro_data:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                data['name'] = pro_data.name
                data['descr'] = pro_data.descr
                data['project_id'] = id
                if name:
                    filter.append(ProjectDevice.name.like('%' + name + '%'))
                if area:
                    filter.append(ProjectDevice.area.like('%' + area + '%'))
                if sn:
                    filter.append(ProjectDevice.sn.like('%' + sn + '%'))
                if type_name:
                    model_device_type = user_session.query(DeviceType).filter(
                        DeviceType.name.like('%' + type_name + '%')).all()
                    if model_device_type:
                        device_type_ids = [i.id for i in model_device_type]
                        filter.append(ProjectDevice.type_id.in_(device_type_ids))
                    else:
                        data['device_list'] = []
                        return self.returnTotalSuc(data, 0)
                if factory_name:
                    model_manufacturer = user_session.query(Manufacturer).filter(
                        Manufacturer.descr.like('%' + factory_name + '%')).all()
                    if model_manufacturer:
                        manufacturer_ids = [i.id for i in model_manufacturer]
                        filter.append(ProjectDevice.factory_id.in_(manufacturer_ids))
                    else:
                        data['device_list'] = []
                        return self.returnTotalSuc(data, 0)
                if device_no_name:
                    model_device_no = user_session.query(DeviceNo).filter(
                        DeviceNo.name.like('%' + device_no_name + '%')).all()
                    if model_device_no:
                        device_no_ids = [i.id for i in model_device_no]
                        filter.append(ProjectDevice.device_no_id.in_(device_no_ids))
                    else:
                        data['device_list'] = []
                        return self.returnTotalSuc(data, 0)
                total = user_session.query(func.count(ProjectDevice.id)).filter(*filter).scalar()
                device_list = []
                pages = user_session.query(ProjectDevice).filter(*filter).order_by(ProjectDevice.id.desc()).limit(
                    pageSize).offset((pageNum - 1) * pageSize).all()
                for pag in pages:
                    obj = eval(str(pag))
                    # 类型名称
                    device_type_data = user_session.query(DeviceType).filter(DeviceType.id == obj['type_id']).first()
                    obj['type_name'] = ''
                    if device_type_data:
                        obj['type_name'] = device_type_data.name
                    # 厂家
                    factory_data = user_session.query(Manufacturer).filter(Manufacturer.id == obj['factory_id']).first()
                    obj['factory_name'] = ''
                    if factory_data:
                        obj['factory_name'] = factory_data.descr
                    # 设备型号
                    device_no_data = user_session.query(DeviceNo).filter(
                        DeviceNo.id == obj['device_no_id']).first()
                    obj['device_no_name'] = ''
                    if device_no_data:
                        obj['device_no_name'] = device_no_data.name

                    keys_to_remove = ['op_ts', 'is_use', 'project_id']
                    # 'type_id', 'factory_id', 'device_no_id', 'remark'
                    for key in keys_to_remove:
                        if key in obj:
                            del obj[key]
                    device_list.append(obj)
                data['device_list'] = device_list
                return self.returnTotalSuc(data, total)
            elif kt == 'DownloadDeviceTemplate':  # 下载模版
                id = self.get_argument('id', None)  # 项目id
                filter = [ProjectDevice.is_use == 1, ProjectDevice.project_id == id]

                device_list = [['储能单元', '分区', '设备编号', '类型名称', '厂家', '设备型号', '设备出厂编号', '设备额定能量（kWh）']]
                pages = user_session.query(ProjectDevice).filter(*filter).order_by(ProjectDevice.id.desc()).all()
                for pag in pages:
                    obj = eval(str(pag))
                    # 类型名称
                    device_type_data = user_session.query(DeviceType).filter(DeviceType.id == obj['type_id']).first()
                    obj['type_name'] = ''
                    if device_type_data:
                        obj['type_name'] = device_type_data.name
                    # 厂家
                    factory_data = user_session.query(Manufacturer).filter(Manufacturer.id == obj['factory_id']).first()
                    obj['factory_name'] = ''
                    if factory_data:
                        obj['factory_name'] = factory_data.descr
                    # 设备型号
                    device_no_data = user_session.query(DeviceNo).filter(
                        DeviceNo.id == obj['device_no_id']).first()
                    obj['device_no_name'] = ''
                    if device_no_data:
                        obj['device_no_name'] = device_no_data.name

                    keys_to_remove = ['op_ts', 'is_use', 'project_id', 'id', 'type_id', 'factory_id', 'device_no_id',
                                      'remark']
                    # 'type_id', 'factory_id', 'device_no_id', 'remark'
                    for key in keys_to_remove:
                        if key in obj:
                            del obj[key]
                    device_list.append([obj['name'], obj['area'], obj['sn'], obj['type_name'], obj['factory_name'],
                                        obj['device_no_name'], obj['init_no'], obj['init_cap']])
                if pages:
                    # 将数据转换为DataFrame
                    df = pd.DataFrame(device_list[1:], columns=device_list[0])

                    # 创建一个BytesIO对象，用于保存Excel文件的二进制数据
                    excel_buffer = io.BytesIO()

                    # 将DataFrame写入Excel文件
                    df.to_excel(excel_buffer, index=False)

                    # 将BytesIO对象的位置重置到开始，以便从头读取数据
                    excel_buffer.seek(0)

                    # 将二进制数据转换为字节
                    binary_data = excel_buffer.read()

                    # 定义存储桶名称和对象名称（即文件名）
                    bucket_name = 'rhyc'
                    pro_data = user_session.query(ProjectInfo).filter(ProjectInfo.id == id).first()
                    object_name = pro_data.descr + "设备信息" + str(uuid.uuid4()) + ".xlsx"

                    # 调用upload_file方法上传Excel文件
                    storage_url = upload_file(binary_data, bucket_name, object_name)
                    storage_url = storage_url.split('?', 1)[0]
                else:
                    storage_url = 'https://minio.robestec.cn/rhyc/%E8%AE%BE%E5%A4%87%E4%BF%A1%E6%81%AF%E6%A8%A1%E7%89%88.xlsx'
                return self.returnTypeSuc({'down_url': storage_url})

            elif kt == 'GetProjectDeviceList': # 获取项目下的设备详情
                id = self.get_argument('id', None)  # 项目id
                list1 = []
                obj = {}
                # obj1 = {'PCS': []}
                page1 = user_session.query(Equipment).filter(Equipment.parent_id ==id,Equipment.is_use =='1').all()#查询单元
                if page1:
                    for i in page1:
                        obj1 = {'PCS':[]}
                        if 'project_id' not in obj:
                            obj['project_id'] = id
                            obj['name'] = i.station
                            if lang=='en':
                                obj['descr'] = i.en_descr
                            else:
                                obj['descr'] = i.descr
                            data.append(obj)
                        if lang =='en':
                            obj1['unit'] = i.en_name
                        else:
                            obj1['unit'] = i.name
                        obj1['unit_id'] = i.id
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id,Equipment.is_use =='1').all()#查询PCS
                        if page2:
                            for ii in page2:
                                obj2 = {'dui':[]}
                                obj2['pcs_num'] = ii.name
                                obj2['pcs_num_id'] = ii.id
                                obj2['pcs_ty_num'] = ii.pcs_ty_num
                                if lang=='en':
                                    obj2['pcs_s_name'] = ii.en_pcs_s_name
                                    obj2['pcs_name'] = ii.en_pcs_name
                                else:
                                    obj2['pcs_name'] = ii.pcs_name
                                    obj2['pcs_s_name'] = ii.pcs_s_name
                                obj2['act_acc_ene'] = ii.act_acc_ene
                                obj1['PCS'].append(obj2)
                                page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id,Equipment.is_use =='1').all()#查询堆
                                if page3:
                                    for iii in page3:
                                        page4 = user_session.query(Equipment).filter(Equipment.parent_id == iii.id,Equipment.is_use =='1').all()#查询簇
                                        if page4:
                                            for iiii in page4:
                                                obj3 = {'cu': []}
                                                obj3['dui_num'] = iii.name
                                                obj3['dui_num_id'] = iii.id
                                                obj2['dui'].append(obj3)
                                                obj4 = {'cell': []}
                                                obj4['cu_num'] = iiii.name
                                                obj4['cu_num_id'] = iiii.id
                                                if lang=='en':
                                                    obj4['cool_mode'] = iiii.en_cool_mode
                                                    obj4['cell_type'] = iiii.en_cell_type
                                                else:
                                                    obj4['cool_mode'] = iiii.cool_mode
                                                    obj4['cell_type'] = iiii.cell_type
                                                obj4['cell_num'] = iiii.cell_num
                                                obj3['cu'].append(obj4)
                                                page4 = user_session.query(Equipment).filter(Equipment.parent_id == iiii.id,Equipment.is_use =='1').all()  # 查询电芯
                                                if page4:
                                                    for p in page4:
                                                        obj5 = {}
                                                        obj5['cell_ty_num'] = p.name
                                                        obj5['cell_ty_num_id'] = p.id
                                                        obj5['cell_s_ene'] = p.cell_s_ene
                                                        obj5['total_ene'] = p.total_ene
                                                        if lang=='en':
                                                            obj5['cell_s_na'] = p.en_cell_s_na
                                                        else:
                                                            obj5['cell_s_na'] = p.cell_s_na
                                                        obj5['bms_num'] = p.bms_num
                                                        obj4['cell'].append(obj5)
                        list1.append(obj1)
                        obj['list1']=list1
                return self.returnTypeSuc(data)
            elif kt == 'GetPCSList': # 所有PCS型号
                pages = user_session.query(DeviceLibrary.device_mo,DeviceLibrary.id,DeviceLibrary.en_device_mo).filter(DeviceLibrary.device_ty=='PCS',DeviceLibrary.is_use == 1).order_by(DeviceLibrary.id.desc()).all()
                for pag in pages:
                    obj = {}
                    if lang=='en':
                        obj['name'] = pag[2]
                    else:
                        obj['name'] = pag[0]
                    obj['id'] = pag[1]
                    data.append(obj)
                return self.returnTypeSuc(data)
            elif kt == 'GetCellList': # 所有电芯型号
                pages = user_session.query(DeviceLibrary.device_mo,DeviceLibrary.id,DeviceLibrary.en_device_mo).filter(DeviceLibrary.device_ty=='电芯',DeviceLibrary.is_use == 1).order_by(DeviceLibrary.id.desc()).all()
                for pag in pages:
                    obj = {}
                    if lang == 'en':
                        obj['name'] = pag[2]
                    else:
                        obj['name'] = pag[0]
                    obj['id'] = pag[1]
                    data.append(obj)
                return self.returnTypeSuc(data)

            elif kt == 'GetMeaList': # 所有测点表
                id = self.get_argument('id',None) #单元id
                name = self.get_argument('name',None) #设备名称
                num = self.get_argument('num',None) #编号
                mea_point_n = self.get_argument('mea_point_n',None) #测点名称
                type_ = self.get_argument('type_','1') #类型,1遥测2遥信3遥调4遥控
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not id or not type_:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                filter1 = [RemoteTele.is_use == 1,RemoteTele.t_eq_id == id]
                filter2 = [RemoteLetter.is_use == 1,RemoteLetter.t_eq_id == id]
                filter3 = [RemoteModula.is_use == 1,RemoteModula.t_eq_id == id]
                filter4 = [RemoteControl.is_use == 1,RemoteControl.t_eq_id == id]

                if DEBUG:
                    logging.info('name:%s,num:%s,mea_point_n:%s,type_:%s'%(name,num,mea_point_n,type_))
                if name:
                    if lang =='en':
                        filter1.append(RemoteTele.en_name.like('%' + name + '%'))
                        filter2.append(RemoteLetter.en_name.like('%' + name + '%'))
                        filter3.append(RemoteModula.en_name.like('%' + name + '%'))
                        filter4.append(RemoteControl.en_name.like('%' + name + '%'))
                    else:
                        filter1.append(RemoteTele.name.like('%' + name + '%'))
                        filter2.append(RemoteLetter.name.like('%' + name + '%'))
                        filter3.append(RemoteModula.name.like('%' + name + '%'))
                        filter4.append(RemoteControl.name.like('%' + name + '%'))
                if num:
                   filter1.append(RemoteTele.num.like('%' + num + '%'))
                   filter2.append(RemoteLetter.num.like('%' + num + '%'))
                   filter3.append(RemoteModula.num.like('%' + num + '%'))
                   filter4.append(RemoteControl.num.like('%' + num + '%'))
                if mea_point_n:
                    if lang == 'en':
                        filter1.append(RemoteTele.en_mea_point_n.like('%' + mea_point_n + '%'))
                        filter2.append(RemoteLetter.en_mea_point_n.like('%' + mea_point_n + '%'))
                        filter3.append(RemoteModula.en_mea_point_n.like('%' + mea_point_n + '%'))
                        filter4.append(RemoteControl.en_mea_point_n.like('%' + mea_point_n + '%'))
                    else:
                        filter1.append(RemoteTele.mea_point_n.like('%' + mea_point_n + '%'))
                        filter2.append(RemoteLetter.mea_point_n.like('%' + mea_point_n + '%'))
                        filter3.append(RemoteModula.mea_point_n.like('%' + mea_point_n + '%'))
                        filter4.append(RemoteControl.mea_point_n.like('%' + mea_point_n + '%'))
                total = 0
                if type_ == '1':
                    total = user_session.query(func.count(RemoteTele.id)).filter(*filter1).scalar()
                    pages = user_session.query(RemoteTele.id,RemoteTele.name,RemoteTele.num,RemoteTele.mea_point_n,RemoteTele.type_,RemoteTele.remarks,RemoteTele.en_name,RemoteTele.en_mea_point_n,RemoteTele.en_remarks).filter(*filter1).order_by(RemoteTele.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages,lang)
                elif type_ == '2':
                    total = user_session.query(func.count(RemoteLetter.id)).filter(*filter2).scalar()
                    pages = user_session.query(RemoteLetter.id,RemoteLetter.name,RemoteLetter.num,RemoteLetter.mea_point_n,RemoteLetter.type_,RemoteLetter.remarks,RemoteLetter.en_name,RemoteLetter.en_mea_point_n,RemoteLetter.en_remarks).filter(*filter2).order_by(RemoteLetter.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages,lang)
                elif type_ == '3':
                    total = user_session.query(func.count(RemoteModula.id)).filter(*filter3).scalar()
                    pages = user_session.query(RemoteModula.id,RemoteModula.name,RemoteModula.num,RemoteModula.mea_point_n,RemoteModula.type_,RemoteModula.remarks,RemoteModula.en_name,RemoteModula.en_mea_point_n,RemoteModula.en_remarks).filter(*filter3).order_by(RemoteModula.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages,lang)
                elif type_ == '4':
                    total = user_session.query(func.count(RemoteControl.id)).filter(*filter4).scalar()
                    pages = user_session.query(RemoteControl.id,RemoteControl.name,RemoteControl.num,RemoteControl.mea_point_n,RemoteControl.type_,RemoteControl.remarks,RemoteControl.en_name,RemoteControl.en_mea_point_n,RemoteControl.en_remarks).filter(*filter4).order_by(RemoteControl.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages,lang)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetUnitList': # 项目单元下设备名称
                id = self.get_argument('id', None)  # 单元id
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                pages1 = user_session.query(Equipment.name,Equipment.id,Equipment.name).filter(Equipment.parent_id==id).order_by(Equipment.id.desc()).all()
                if pages1:
                    for pag1 in pages1:
                        obj = {}
                        if lang=='en':
                            obj['name'] = pag1[2]
                        else:
                            obj['name'] = pag1[0]
                        obj['id'] = pag1[1]
                        data.append(obj)
                        pages2 = user_session.query(Equipment.name, Equipment.id,Equipment.name).filter(Equipment.parent_id == pag1.id).order_by(Equipment.id.desc()).all()
                        if pages2:
                            for pag2 in pages2:
                                obj2 = {}
                                if lang == 'en':
                                    obj2['name'] = pag2[2]
                                else:
                                    obj2['name'] = pag2[0]
                                obj2['id'] = pag2[1]
                                data.append(obj2)
                                pages3 = user_session.query(Equipment.name, Equipment.id,Equipment.name).filter(Equipment.parent_id == pag2.id).order_by(Equipment.id.desc()).all()
                                if pages3:
                                    for pag3 in pages3:
                                        obj3 = {}
                                        if lang == 'en':
                                            obj3['name'] = pag3[2]
                                        else:
                                            obj3['name'] = pag3[0]
                                        obj3['id'] = pag3[1]
                                        data.append(obj3)
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceTypeList': # 设备类型列表
                data = []
                name = self.get_argument('name', None)
                filter = [DeviceType.is_use==1]
                if name:
                    filter.append(DeviceType.name.like('%' + name + '%'))

                res = user_session.query(DeviceType).filter(*filter).order_by(DeviceType.id.desc()).all()
                if res:
                    for pag in res:
                        obj = eval(str(pag))
                        data.append(obj)
                return self.returnTypeSuc(data)
            elif kt == 'GetFactoryList': # 厂家列表
                data = []
                name = self.get_argument('name', None)
                if name:
                    filter = [Manufacturer.descr.like('%' + name + '%')]
                    res = user_session.query(Manufacturer).filter(*filter).order_by(Manufacturer.id.desc()).all()
                else:
                    res = user_session.query(Manufacturer).order_by(Manufacturer.id.desc()).all()
                if res:
                    for pag in res:
                        obj = eval(str(pag))
                        data.append(obj)
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceNoList': # 设备型号列表
                data = []
                name = self.get_argument('name', None)
                factory_id = self.get_argument('factory_id', None)
                type_id = self.get_argument('type_id', None)
                filter = [DeviceNo.is_use == 1]
                if name:
                    filter.append(DeviceNo.name.like('%' + name + '%'))
                if factory_id:
                    if not re.compile(r'^-?\d+$').match(factory_id):
                        return self.customError('参数不合规')
                    filter.append(DeviceNo.factory_id == int(factory_id))
                if type_id:
                    if not re.compile(r'^-?\d+$').match(type_id):
                        return self.customError('参数不合规')
                    filter.append(DeviceNo.type_id == int(type_id))
                res = user_session.query(DeviceNo).filter(*filter).order_by(DeviceNo.id.desc()).all()
                if res:
                    for pag in res:
                        obj = eval(str(pag))
                        data.append(obj)
                return self.returnTypeSuc(data)

            elif kt == 'GetDeviceUnitList': # 设备部件列表
                data = []
                # name = self.get_argument('name', None)
                device_id = self.get_argument('device_id', None)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                if not device_id:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if not re.compile(r'^-?\d+$').match(device_id):
                    return self.customError('参数不合规')
                filter = [DeviceUnit.is_use==1, DeviceUnit.device_id==device_id]
                # if name:
                #     filter.append(DeviceUnit.name.like('%' + name + '%'))
                total = user_session.query(func.count(DeviceUnit.id)).filter(*filter).scalar()
                res = user_session.query(DeviceUnit).filter(*filter).order_by(DeviceUnit.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                if res:
                    for pag in res:
                        obj = eval(str(pag))
                        data.append(obj)
                return self.returnTotalSuc(data,total)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

        # user_session.rollback()
        # user_session.close()

    def MeaReturns(self, data, pages,lang=None):
        #返回测点表
        for pag in pages:
            obj = {}
            obj['id'] = pag[0]
            obj['num'] = pag[2]
            obj['type_'] = pag[4]
            if lang=='en':
                obj['name'] = pag[6]
                obj['mea_point_n'] = pag[7]
                obj['remarks'] = pag[8]
            else:
                obj['name'] = pag[1]
                obj['mea_point_n'] = pag[3]
                obj['remarks'] = pag[5]
            data.append(obj)

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        try:
        # if 1:
            if kt == 'ProjectAdd':  # 添加电站项目
                descr = self.get_argument('descr',None)# "项目名称"
                com_name = self.get_argument('com_name',None)# "项目公司名称"
                name = self.get_argument('name',None)# "英文名称"
                short_name = self.get_argument('short_name',None)# "项目简称"
                energy_storage = self.get_argument('energy_storage',None)# "储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能"
                electric_power = self.get_argument('electric_power',None)# "额定功率"
                volume = self.get_argument('volume',None)# "电站容量"
                start_ts = self.get_argument('start_ts',None)# "投运日期：YYYY-mm-dd"
                volt_class = self.get_argument('volt_class',None)# "电压等级"
                country = self.get_argument('country',None)# "国家"
                province = self.get_argument('province',None)# "电站所在省份"
                city = self.get_argument('city',None)# "市"
                address = self.get_argument('address',None)# "地址"
                remarks = self.get_argument('remarks',None)# "基础信息备注"
                own_name = self.get_argument('own_name',None)# "业主名称"
                con_per = self.get_argument('con_per',None)# "运维合同期限"
                tar_inc_y = self.get_argument('tar_inc_y',None)# "年度运营目标收益"
                ser_pers = self.get_argument('ser_pers',None)# "服务人员"
                phone = self.get_argument('phone',None)# "联系电话"
                met_read_c = self.get_argument('met_read_c',None)# "抄表周期"
                met_read_d = self.get_argument('met_read_d',None)# "抄表日"
                y_fm_re = self.get_argument('y_fm_re',None)# "年度一次调频收益"
                y_agc_re = self.get_argument('y_agc_re',None)# "年度AGC收益"
                y_spot_re = self.get_argument('y_spot_re',None)# "年度现货收益"
                y_f_e_s_re = self.get_argument('y_f_e_s_re',None)# "年度火储能辅助服务收益"
                y_p_c_g_f_re = self.get_argument('y_p_c_g_f_re',None)# "年度削峰填谷收益"
                y_d_r_re = self.get_argument('y_d_r_re',None)# "年度需求侧响应收益"
                y_oth_re = self.get_argument('y_oth_re',None)# "年度其他收益"
                tar_ava_y = self.get_argument('tar_ava_y',None)# "目标年度系统可用率"
                tar_li_ch_ef = self.get_argument('tar_li_ch_ef',None)# "目标集电线路充电效率"
                tar_li_di_ef = self.get_argument('tar_li_di_ef',None)# "目标集电线路放电效率"
                tar_vo_ch_ef = self.get_argument('tar_vo_ch_ef',None)# "目标升压站充电效率"
                tar_vo_di_ef = self.get_argument('tar_vo_di_ef',None)# "目标升压站放电效率"
                tar_pcs_ch_ef = self.get_argument('tar_pcs_ch_ef',None)# "目标PCS充电效率"
                tar_pcs_di_ef = self.get_argument('tar_pcs_di_ef',None)# "目标PCS放电效率"
                tar_dc_ch_ef = self.get_argument('tar_dc_ch_ef',None)# "目标直流侧充电效率"
                tar_dc_di_ef = self.get_argument('tar_dc_di_ef',None)# "目标直流侧放电效率"
                head_main = self.get_argument('head_main',None)# "运维负责人"
                dev_ops = self.get_argument('dev_ops',None)# "运维人员"
                other_dev_ops = self.get_argument('other_dev_ops',None)# "其他运维人员"
                data_acc_date = self.get_argument('data_acc_date',None)# "数据接入日期"
                data_int = self.get_argument('data_int',None)# "数据间隔"
                commu_mode = self.get_argument('commu_mode',None)# "通讯方式"
                iot_card_num = self.get_argument('iot_card_num',None)# "物联卡编号"
                list1 = self.get_argument('list1', [])  # "电价list"
                remarks = ';'.join(remarks.split()) if remarks else ''
                other_dev_ops = ';'.join(other_dev_ops.split()) if other_dev_ops else ''
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                if DEBUG:
                    logging.info('descr:%s,name:%s,short_name:%s,energy_storage:%s,electric_power:%s,volume:%s,start_ts:%s,province:%s,city:%s,address:%s,own_name:%s,con_per:%s,tar_inc_y:%s,head_main:%s,data_acc_date:%s'
                                 %(descr,name,short_name,energy_storage,electric_power,volume,start_ts,province,city,address,own_name,con_per,tar_inc_y,head_main,data_acc_date))
                if not descr or not name or not short_name or not energy_storage or not electric_power or not volume or not start_ts or not province or not city  or not own_name or not con_per or not tar_inc_y or not head_main or not data_acc_date :
                    if lang=='en':
                        return self.customError("Please bring full parameters")
                    else:
                        return self.customError("请携带完整参数")
                if lang == 'en':
                    page = user_session.query(ProjectInfo).filter(or_(ProjectInfo.en_descr==descr,ProjectInfo.name==name),ProjectInfo.is_use == '1').first()
                    if page:
                        return self.customError("The project name is repeated, please re-enter!")
                else:
                    page = user_session.query(ProjectInfo).filter(or_(ProjectInfo.descr == descr, ProjectInfo.name == name), ProjectInfo.is_use == '1').first()
                    if page:
                        return self.customError("项目名称重复，请重新录入！")
                if list1:
                    list1 = json.loads(list1)
                    list_2 = [1,2,3,4,5,6,7,8,9,10,11,12]
                    for l in list1:
                        name_ = l['name']
                        month = l['months']
                        if lang=='en':
                            page_ = user_session.query(ElePrice).filter(ElePrice.en_name == name_,
                                                                        ElePrice.station == name,
                                                                        ElePrice.is_use == '1',
                                                                        ElePrice.years == datestart).first()
                            if page_:
                                return self.customError("Data already exists 2")
                        else:
                            page_ = user_session.query(ElePrice).filter(ElePrice.name == name_, ElePrice.station == name,
                                                                        ElePrice.is_use == '1',
                                                                        ElePrice.years == datestart).first()
                            if page_:
                                return self.customError("数据已存在2")
                        a = int(month[0])
                        b = int(month[1])
                        months =0
                        datestart_ = 0
                        if a > b:#跨年
                            datestart_ = int(datestart) +1
                            months = ",".join(str(e) for e in list_2[a-1:]) #当年的时间
                            months_ = ",".join(str(e) for e in list_2[:b]) #第二年的时间
                        elif a == b:
                            months = a
                        else:
                            months = ",".join(str(e) for e in list_2[a-1:b])
                        if l['time_']:
                            for t in l['time_']:
                                start_time = t['start_time']
                                end_time = t['end_time']
                                rate = t['rate']
                                if lang=='en':
                                    zh_descr=translate_text(t['descr'], 1)
                                    zh_name=translate_text(name_, 1)
                                    p_ = ElePrice(name=zh_name, descr=zh_descr, start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months, month=str(month), years=datestart,
                                                  station=name, op_ts=now_time, en_name=name_, en_descr=t['descr'])
                                else:
                                    en_descr = translate_text(t['descr'], 2)
                                    en_name = translate_text(name_, 2)
                                    p_ = ElePrice(name=name_, descr=t['descr'], start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months, month=str(month), years=datestart,
                                                  station=name, op_ts=now_time, en_name=en_name, en_descr=en_descr)
                                user_session.add(p_)
                                if datestart_:
                                    if lang == 'en':
                                        zh_descr = translate_text(t['descr'], 1)
                                        zh_name = translate_text(name_, 1)
                                        p_1 = ElePrice(name=zh_name, descr=zh_descr, start_time=start_time, end_time=end_time,
                                                      rate=rate, months=months_, month=str(month), years=datestart_,station=name, op_ts=now_time,en_name=name_, en_descr=t['descr'])
                                        user_session.add(p_1)
                                    else:
                                        en_descr = translate_text(t['descr'], 2)
                                        en_name = translate_text(name_, 2)
                                        p_1 = ElePrice(name=name_, descr=descr, start_time=start_time,
                                                       end_time=end_time,
                                                       rate=rate, months=months_, month=str(month), years=datestart_,
                                                       station=name, op_ts=now_time, en_name=en_name, en_descr=en_descr)
                                        user_session.add(p_1)
                if lang=='en':
                    zh_descr = translate_text(descr, 1)
                    zh_com_name = translate_text(com_name, 1)
                    zh_short_name = translate_text(short_name, 1)
                    zh_country = translate_text(country, 1)
                    zh_province = translate_text(province, 1)
                    zh_city = translate_text(city, 1)
                    zh_address = translate_text(address, 1)
                    zh_remarks = translate_text(remarks, 1)
                    zh_own_name = translate_text(own_name, 1)
                    zh_ser_pers = translate_text(ser_pers, 1)
                    zh_head_main = translate_text(head_main, 1)
                    zh_dev_ops = translate_text(dev_ops, 1)
                    zh_other_dev_ops = translate_text(other_dev_ops, 1)
                    p = ProjectInfo(descr=descr, com_name=com_name, name=name, short_name=short_name,
                                    energy_storage=energy_storage, electric_power=electric_power, volume=volume,
                                    start_ts=start_ts, volt_class=volt_class, country=country, province=province,
                                    city=city, address=address,
                                    remarks=remarks, own_name=own_name, con_per=con_per, tar_inc_y=tar_inc_y,
                                    ser_pers=ser_pers, phone=phone,
                                    met_read_c=met_read_c, met_read_d=met_read_d, y_fm_re=y_fm_re, y_agc_re=y_agc_re,
                                    y_spot_re=y_spot_re, y_f_e_s_re=y_f_e_s_re,
                                    y_p_c_g_f_re=y_p_c_g_f_re, y_d_r_re=y_d_r_re, y_oth_re=y_oth_re,
                                    tar_ava_y=tar_ava_y, tar_li_ch_ef=tar_li_ch_ef, tar_li_di_ef=tar_li_di_ef,
                                    tar_vo_ch_ef=tar_vo_ch_ef, tar_vo_di_ef=tar_vo_di_ef, tar_pcs_ch_ef=tar_pcs_ch_ef,
                                    tar_pcs_di_ef=tar_pcs_di_ef, tar_dc_ch_ef=tar_dc_ch_ef, tar_dc_di_ef=tar_dc_di_ef,
                                    head_main=head_main, dev_ops=dev_ops, other_dev_ops=other_dev_ops,
                                    data_acc_date=data_acc_date,
                                    data_int=data_int, commu_mode=commu_mode, iot_card_num=iot_card_num,
                                    en_descr=zh_descr, en_com_name=zh_com_name, en_short_name=zh_short_name,
                                    en_country=zh_country,en_province=zh_province, en_city=zh_city, en_address=zh_address,
                                    en_remarks=zh_remarks, en_own_name=zh_own_name, en_ser_pers=zh_ser_pers, en_head_main=zh_head_main, en_dev_ops=zh_dev_ops, en_other_dev_ops=zh_other_dev_ops)
                else:
                    en_descr = translate_text(descr, 2)
                    en_com_name = translate_text(com_name, 2)
                    en_short_name = translate_text(short_name, 2)
                    en_country = translate_text(country, 2)
                    en_province = translate_text(province, 2)
                    en_city = translate_text(city, 2)
                    en_address = translate_text(address, 2)
                    en_remarks = translate_text(remarks, 2)
                    en_own_name = translate_text(own_name, 2)
                    en_ser_pers = translate_text(ser_pers, 2)
                    en_head_main = translate_text(head_main, 2)
                    en_dev_ops = translate_text(dev_ops, 2)
                    en_other_dev_ops = translate_text(other_dev_ops, 2)
                    p = ProjectInfo(descr=descr, com_name=com_name, name=name, short_name=short_name,
                                    energy_storage=energy_storage, electric_power=electric_power, volume=volume,
                                    start_ts=start_ts, volt_class=volt_class, country=country, province=province,
                                    city=city, address=address,
                                    remarks=remarks, own_name=own_name, con_per=con_per, tar_inc_y=tar_inc_y,
                                    ser_pers=ser_pers, phone=phone,
                                    met_read_c=met_read_c, met_read_d=met_read_d, y_fm_re=y_fm_re, y_agc_re=y_agc_re,
                                    y_spot_re=y_spot_re, y_f_e_s_re=y_f_e_s_re,
                                    y_p_c_g_f_re=y_p_c_g_f_re, y_d_r_re=y_d_r_re, y_oth_re=y_oth_re,
                                    tar_ava_y=tar_ava_y, tar_li_ch_ef=tar_li_ch_ef, tar_li_di_ef=tar_li_di_ef,
                                    tar_vo_ch_ef=tar_vo_ch_ef, tar_vo_di_ef=tar_vo_di_ef, tar_pcs_ch_ef=tar_pcs_ch_ef,
                                    tar_pcs_di_ef=tar_pcs_di_ef, tar_dc_ch_ef=tar_dc_ch_ef, tar_dc_di_ef=tar_dc_di_ef,
                                    head_main=head_main, dev_ops=dev_ops, other_dev_ops=other_dev_ops,
                                    data_acc_date=data_acc_date,
                                    data_int=data_int, commu_mode=commu_mode, iot_card_num=iot_card_num,
                                    en_descr=en_descr, en_com_name=en_com_name, en_short_name=en_short_name,
                                    en_country=en_country, en_province=en_province, en_city=en_city,
                                    en_address=en_address,
                                    en_remarks=en_remarks, en_own_name=en_own_name, en_ser_pers=en_ser_pers,
                                    en_head_main=en_head_main, en_dev_ops=en_dev_ops, en_other_dev_ops=en_other_dev_ops)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'ProjectUpdate':  # 修改页面
                id = self.get_argument('id',None)#项目表id
                descr = self.get_argument('descr', None)  # "项目名称"
                com_name = self.get_argument('com_name', None)  # "项目公司名称"
                name = self.get_argument('name', None)  # "英文名称"
                short_name = self.get_argument('short_name', None)  # "项目简称"
                energy_storage = self.get_argument('energy_storage', None)  # "储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能"
                electric_power = self.get_argument('electric_power', None)  # "额定功率"
                volume = self.get_argument('volume', None)  # "电站容量"
                start_ts = self.get_argument('start_ts', None)  # "投运日期：YYYY-mm-dd"
                volt_class = self.get_argument('volt_class', None)  # "电压等级"
                country = self.get_argument('country', None)  # "国家"
                province = self.get_argument('province', None)  # "电站所在省份"
                city = self.get_argument('city', None)  # "市"
                address = self.get_argument('address', None)  # "地址"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                own_name = self.get_argument('own_name', None)  # "业主名称"
                con_per = self.get_argument('con_per', None)  # "运维合同期限"
                tar_inc_y = self.get_argument('tar_inc_y', None)  # "年度运营目标收益"
                ser_pers = self.get_argument('ser_pers', None)  # "服务人员"
                phone = self.get_argument('phone', None)  # "联系电话"
                met_read_c = self.get_argument('met_read_c', None)  # "抄表周期"
                met_read_d = self.get_argument('met_read_d', None)  # "抄表日"
                y_fm_re = self.get_argument('y_fm_re', None)  # "年度一次调频收益"
                y_agc_re = self.get_argument('y_agc_re', None)  # "年度AGC收益"
                y_spot_re = self.get_argument('y_spot_re', None)  # "年度现货收益"
                y_f_e_s_re = self.get_argument('y_f_e_s_re', None)  # "年度火储能辅助服务收益"
                y_p_c_g_f_re = self.get_argument('y_p_c_g_f_re', None)  # "年度削峰填谷收益"
                y_d_r_re = self.get_argument('y_d_r_re', None)  # "年度需求侧响应收益"
                y_oth_re = self.get_argument('y_oth_re', None)  # "年度其他收益"
                tar_ava_y = self.get_argument('tar_ava_y', None)  # "目标年度系统可用率"
                tar_li_ch_ef = self.get_argument('tar_li_ch_ef', None)  # "目标集电线路充电效率"
                tar_li_di_ef = self.get_argument('tar_li_di_ef', None)  # "目标集电线路放电效率"
                tar_vo_ch_ef = self.get_argument('tar_vo_ch_ef', None)  # "目标升压站充电效率"
                tar_vo_di_ef = self.get_argument('tar_vo_di_ef', None)  # "目标升压站放电效率"
                tar_pcs_ch_ef = self.get_argument('tar_pcs_ch_ef', None)  # "目标PCS充电效率"
                tar_pcs_di_ef = self.get_argument('tar_pcs_di_ef', None)  # "目标PCS放电效率"
                tar_dc_ch_ef = self.get_argument('tar_dc_ch_ef', None)  # "目标直流侧充电效率"
                tar_dc_di_ef = self.get_argument('tar_dc_di_ef', None)  # "目标直流侧放电效率"
                head_main = self.get_argument('head_main', None)  # "运维负责人"
                dev_ops = self.get_argument('dev_ops', None)  # "运维人员"
                other_dev_ops = self.get_argument('other_dev_ops', None)  # "其他运维人员"
                data_acc_date = self.get_argument('data_acc_date', None)  # "数据接入日期"
                data_int = self.get_argument('data_int', None)  # "数据间隔"
                commu_mode = self.get_argument('commu_mode', None)  # "通讯方式"
                iot_card_num = self.get_argument('iot_card_num', None)  # "物联卡编号"
                remarks = ';'.join(remarks.split()) if remarks else ''
                other_dev_ops = ';'.join(other_dev_ops.split()) if other_dev_ops else ''

                list1 = self.get_argument('list1', [])  # "电价list"
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                if DEBUG:
                    logging.info(
                        'descr:%s,name:%s,short_name:%s,energy_storage:%s,electric_power:%s,volume:%s,start_ts:%s,province:%s,city:%s,address:%s,own_name:%s,con_per:%s,tar_inc_y:%s,head_main:%s,data_acc_date:%s'
                        % (descr, name, short_name, energy_storage, electric_power, volume, start_ts, province, city,
                           address, own_name, con_per, tar_inc_y, head_main, data_acc_date))
                if not descr or not name or not short_name or not energy_storage or not electric_power or not volume or not start_ts or not province or not city or not own_name or not con_per or not tar_inc_y or not head_main or not data_acc_date:
                    if lang=='en':
                        return self.customError("Please bring full parameters")
                    else:
                        return self.customError("请携带完整参数")
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                if lang=='en':
                    pa = user_session.query(ProjectInfo).filter(ProjectInfo.en_descr==descr,ProjectInfo.name==name,ProjectInfo.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("Data already exists 1")
                else:
                    pa = user_session.query(ProjectInfo).filter(ProjectInfo.descr == descr, ProjectInfo.name == name,
                                                                ProjectInfo.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在1")
                user_session.query(ElePrice).filter(ElePrice.station == page.name).delete()
                if list1:
                    list1 = json.loads(list1)
                    list_2 = [1,2,3,4,5,6,7,8,9,10,11,12]
                    for l in list1:
                        name_ = l['name']
                        month = l['months']
                        if lang == 'en':
                            page_ = user_session.query(ElePrice).filter(ElePrice.en_name == name_,
                                                                        ElePrice.station == name,
                                                                        ElePrice.is_use == '1',
                                                                        ElePrice.years == datestart).first()
                            if page_:
                                return self.customError("Data already exists 2")
                        else:
                            page_ = user_session.query(ElePrice).filter(ElePrice.name == name_,
                                                                        ElePrice.station == name,
                                                                        ElePrice.is_use == '1',
                                                                        ElePrice.years == datestart).first()
                            if page_:
                                return self.customError("数据已存在2")
                        a = int(month[0])
                        b = int(month[1])
                        months =0
                        datestart_ = 0
                        if a > b:#跨年
                            datestart_ = int(datestart) +1
                            months = ",".join(str(e) for e in list_2[a-1:]) #当年的时间
                            months_ = ",".join(str(e) for e in list_2[:b]) #第二年的时间
                        elif a == b:
                            months = a
                        else:
                            months = ",".join(str(e) for e in list_2[a-1:b])
                        if l['time_']:
                            for t in l['time_']:
                                start_time = t['start_time']
                                end_time = t['end_time']
                                descr = t['descr']
                                rate = t['rate']
                                if lang=='en':
                                    zh_descr=translate_text(t['descr'], 1)
                                    zh_name=translate_text(name_, 1)
                                    p_ = ElePrice(name=zh_name, descr=zh_descr, start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months, month=str(month), years=datestart,
                                                  station=name, op_ts=now_time, en_name=name_, en_descr=t['descr'])
                                else:
                                    en_descr = translate_text(t['descr'], 2)
                                    en_name = translate_text(name_, 2)
                                    p_ = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months, month=str(month), years=datestart,
                                                  station=name, op_ts=now_time, en_name=en_name, en_descr=en_descr)
                                user_session.add(p_)
                                # p_ = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,rate=rate, months=months,month=str(month),years=datestart, station=name, op_ts=now_time)
                                user_session.add(p_)
                                if datestart_:
                                    if lang == 'en':
                                        zh_descr = translate_text(t['descr'], 1)
                                        zh_name = translate_text(name_, 1)
                                        p_1 = ElePrice(name=zh_name, descr=zh_descr, start_time=start_time, end_time=end_time,
                                                      rate=rate, months=months_, month=str(month), years=datestart_,station=name, op_ts=now_time,en_name=name_, en_descr=t['descr'])
                                        user_session.add(p_1)
                                    else:
                                        en_descr = translate_text(t['descr'], 2)
                                        en_name = translate_text(name_, 2)
                                        p_1 = ElePrice(name=name_, descr=descr, start_time=start_time,
                                                       end_time=end_time,
                                                       rate=rate, months=months_, month=str(month), years=datestart_,
                                                       station=name, op_ts=now_time, en_name=en_name, en_descr=en_descr)
                                        user_session.add(p_1)

                if name :
                    page.name = name
                if energy_storage :
                    page.energy_storage = energy_storage
                if electric_power :
                    page.electric_power = electric_power
                if volume :
                    page.volume = volume
                if start_ts:
                    page.start_ts = start_ts
                # if volt_class :
                page.volt_class = volt_class
                if con_per:
                    page.con_per = con_per
                if tar_inc_y :
                    page.tar_inc_y = tar_inc_y
                if phone :
                    page.phone = phone
                # if met_read_c:
                page.met_read_c = met_read_c
                # if met_read_d :
                page.met_read_d = met_read_d
                # if y_fm_re :
                page.y_fm_re = y_fm_re
                # if y_agc_re :
                page.y_agc_re = y_agc_re
                # if y_spot_re :
                page.y_spot_re = y_spot_re
                # if y_f_e_s_re:
                page.y_f_e_s_re = y_f_e_s_re
                if y_p_c_g_f_re :
                    page.y_p_c_g_f_re = y_p_c_g_f_re
                if y_d_r_re :
                    page.y_d_r_re = y_d_r_re
                if y_oth_re :
                    page.y_oth_re = y_oth_re
                # if tar_ava_y:
                page.tar_ava_y = tar_ava_y
                # if tar_li_ch_ef :
                page.tar_li_ch_ef = tar_li_ch_ef
                # if tar_li_di_ef :
                page.tar_li_di_ef = tar_li_di_ef
                # if tar_vo_ch_ef :
                page.tar_vo_ch_ef = tar_vo_ch_ef
                # if tar_vo_di_ef :
                page.tar_vo_di_ef = tar_vo_di_ef
                # if tar_pcs_ch_ef:
                page.tar_pcs_ch_ef = tar_pcs_ch_ef
                # if tar_pcs_di_ef :
                page.tar_pcs_di_ef = tar_pcs_di_ef
                # if tar_dc_ch_ef :
                page.tar_dc_ch_ef = tar_dc_ch_ef
                # if tar_dc_di_ef :
                page.tar_dc_di_ef = tar_dc_di_ef
                # if data_acc_date :
                page.data_acc_date = data_acc_date
                if data_int :
                    page.data_int = data_int
                else:
                    page.data_int = None
                # if commu_mode :
                page.commu_mode = commu_mode
                # if iot_card_num :
                page.iot_card_num = iot_card_num

                if descr:
                    if lang == 'en':
                        page.descr = translate_text(descr, 1)
                        page.en_descr = descr
                    else:
                        page.descr = descr
                        page.en_descr = translate_text(descr, 2)
                if com_name:
                    if lang == 'en':
                        page.com_name = translate_text(com_name, 1)
                        page.en_com_name = com_name
                    else:
                        page.com_name = com_name
                        page.en_com_name = translate_text(com_name, 2)
                else:
                    page.com_name = com_name
                    page.en_com_name = com_name
                if short_name:
                    if lang == 'en':
                        page.short_name = translate_text(short_name, 1)
                        page.en_short_name = short_name
                    else:
                        page.short_name = short_name
                        page.en_short_name = translate_text(short_name, 2)
                if country :
                    if lang == 'en':
                        page.country = translate_text(country, 1)
                        page.en_country = country
                    else:
                        page.country = country
                        page.en_country = translate_text(country, 2)
                else:
                    page.country = country
                    page.en_country = country
                if province :
                    if lang == 'en':
                        page.province = translate_text(province, 1)
                        page.en_province = province
                    else:
                        page.province = province
                        page.en_province = translate_text(province, 2)
                if city :
                    if lang == 'en':
                        page.city = translate_text(city, 1)
                        page.en_city = city
                    else:
                        page.city = city
                        page.en_city = translate_text(city, 2)
                if address :
                    if lang == 'en':
                        page.address = translate_text(address, 1)
                        page.en_address = address
                    else:
                        page.address = address
                        page.en_address = translate_text(address, 2)
                else:
                    page.address = address
                    page.en_address = address
                if remarks :
                    if lang == 'en':
                        page.remarks = translate_text(remarks, 1)
                        page.en_remarks = remarks
                    else:
                        page.remarks = remarks
                        page.en_remarks = translate_text(remarks, 2)
                else:
                    page.remarks = remarks
                    page.en_remarks = remarks
                if own_name :
                    if lang == 'en':
                        page.own_name = translate_text(own_name, 1)
                        page.en_own_name = own_name
                    else:
                        page.own_name = own_name
                        page.en_own_name = translate_text(own_name, 2)
                if ser_pers :
                    if lang == 'en':
                        page.ser_pers = translate_text(ser_pers, 1)
                        page.en_ser_pers = ser_pers
                    else:
                        page.ser_pers = ser_pers
                        page.en_ser_pers =  translate_text(ser_pers, 2)
                else:
                    page.ser_pers = ser_pers
                    page.en_ser_pers = ser_pers
                if head_main :
                    if lang == 'en':
                        page.head_main = translate_text(head_main, 1)
                        page.en_head_main = head_main
                    else:
                        page.head_main = head_main
                        page.en_head_main = translate_text(head_main, 2)
                if dev_ops :
                    if lang == 'en':
                        page.dev_ops = translate_text(dev_ops, 1)
                        page.en_dev_ops = dev_ops
                    else:
                        page.dev_ops = dev_ops
                        page.en_dev_ops = translate_text(dev_ops, 2)
                else:
                    page.dev_ops = dev_ops
                    page.en_dev_ops = dev_ops
                if other_dev_ops :
                    if lang == 'en':
                        page.other_dev_ops = translate_text(other_dev_ops, 1)
                        page.en_other_dev_ops = other_dev_ops
                    else:
                        page.other_dev_ops = other_dev_ops
                        page.en_other_dev_ops = translate_text(other_dev_ops, 2)
                else:
                    page.other_dev_ops = other_dev_ops
                    page.en_other_dev_ops = other_dev_ops
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'ProjectDelete':  # 删除报表配置
                id = self.get_argument('id',None)#项目表id
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                p = user_session.query(ElePrice).filter(ElePrice.station == page.name).all()
                if p:
                    for i in p:
                        i.is_use = '0'
                page.is_use = '0'
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'AddDevice': # 添加设备
                device_mo = self.get_argument('device_mo', None) #"设备型号"
                device_ty = self.get_argument('device_ty', None) #"设备类型"
                ra_power = self.get_argument('ra_power', None) #"额定功率,单位kW"
                ra_volume =self.get_argument('ra_volume', None) #"额定容量,单位Ah"
                ra_energy = self.get_argument('ra_energy', None) #"额定能量,单位Wh"
                vender = self.get_argument('vender', None) #"厂家"
                remarks = self.get_argument('remarks', None) #"基础信息备注"
                if not device_mo or not device_ty :
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''
                if DEBUG:
                    logging.info('device_mo:%s,device_ty:%s,ra_power:%s,ra_volume:%s'%(device_mo,device_ty,ra_power,ra_volume))

                if lang == 'en':
                    page = user_session.query(DeviceLibrary).filter(DeviceLibrary.en_device_mo == device_mo,DeviceLibrary.en_device_ty == device_ty,DeviceLibrary.is_use == '1').first()
                    if page:
                        return self.customError("Data already exists")
                else:
                    page = user_session.query(DeviceLibrary).filter(DeviceLibrary.device_mo == device_mo,DeviceLibrary.device_ty == device_ty,DeviceLibrary.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")
                #######     新增逻辑begin       ##########
                # 查出设备类型id
                device_type_data = user_session.query(DeviceType).filter(DeviceType.name == device_ty).first()
                device_type_id = device_type_data.id
                # 查询厂家id
                device_factory_id = None
                if vender:
                    device_factory_data = user_session.query(Manufacturer).filter(Manufacturer.descr == vender).first()
                    if device_factory_data:
                        device_factory_id = device_factory_data.id
                    else:
                        # 如果没查询到厂家则新增厂家
                        dev_factiry_p = Manufacturer(descr=vender, op_ts=timeUtils.getNewTimeStr())
                        user_session.add(dev_factiry_p)
                        user_session.flush()
                        device_factory_id = dev_factiry_p.id
                # 添加设备型号表
                device_no_data = user_session.query(DeviceNo).filter(DeviceNo.name == device_mo).first()
                if device_no_data:
                    s_device_no_id = device_no_data.id
                    # s_device_type_id = device_no_data.type_id
                    # s_device_factory_id = device_no_data.factory_id
                    # if s_device_type_id != device_type_id:
                    #     return self.customError("设备型号与设备类型存在冲突")
                    # if s_device_factory_id and device_factory_id and s_device_factory_id != device_factory_id:
                    #     return self.customError("设备型号与厂家存在冲突")
                    # if device_factory_id and not s_device_factory_id:
                    #     user_session.query(DeviceNo).filter(DeviceNo.id == s_device_no_id).update({'factory_id': device_factory_id},
                    #                                                           synchronize_session=False)
                else:
                    # 未找到则先添加设备型号表
                    dev_type_n = DeviceNo(name=device_mo,type_id=device_type_id, factory_id=device_factory_id, op_ts=timeUtils.getNewTimeStr())
                    user_session.add(dev_type_n)
                    user_session.flush()
                    s_device_no_id = dev_type_n.id
                #######     新增逻辑end 下方更新表数据也增加了三个id值       ##########
                if lang=='en':
                    zh_device_mo = translate_text(device_mo, 1)
                    zh_device_ty = translate_text(device_ty, 1)
                    zh_vender = translate_text(vender, 1)
                    zh_remarks = translate_text(remarks, 1)
                    p = DeviceLibrary(ra_power=ra_power,
                                      ra_volume=ra_volume,
                                      ra_energy=ra_energy, remarks=zh_remarks, en_device_mo=device_mo,
                                      en_device_ty=device_ty, en_vender=vender, en_remarks=remarks,
                                      device_no_id=s_device_no_id, device_type_id=device_type_id, factory_id=device_factory_id)
                    # p = DeviceLibrary(device_mo=zh_device_mo, device_ty=zh_device_ty, ra_power=ra_power, ra_volume=ra_volume,
                    #                   ra_energy=ra_energy, vender=zh_vender, remarks=zh_remarks,en_device_mo=device_mo,en_device_ty=device_ty,en_vender=vender,en_remarks=remarks)
                else:
                    en_device_mo = translate_text(device_mo, 2)
                    en_device_ty = translate_text(device_ty, 2)
                    en_vender = translate_text(vender, 2)
                    en_remarks = translate_text(remarks, 2)
                    p = DeviceLibrary(device_mo=device_mo, device_ty=device_ty, ra_power=ra_power, ra_volume=ra_volume,
                                      ra_energy=ra_energy, vender=vender, remarks=remarks, en_device_mo=en_device_mo,
                                      en_device_ty=en_device_ty, en_vender=en_vender, en_remarks=en_remarks,
                                      device_no_id=s_device_no_id, device_type_id=device_type_id, factory_id=device_factory_id)
                    # p = DeviceLibrary(device_mo=device_mo, device_ty=device_ty, ra_power=ra_power, ra_volume=ra_volume,
                    #                   ra_energy=ra_energy, vender=vender, remarks=remarks,en_device_mo=en_device_mo,en_device_ty=en_device_ty,en_vender=en_vender,en_remarks=en_remarks)
                user_session.add(p)
                user_session.commit()
                if lang=='en':
                    page_id = user_session.query(DeviceLibrary.id).filter(DeviceLibrary.en_device_mo == device_mo,
                                                                          DeviceLibrary.en_device_ty == device_ty).first()
                else:
                    page_id = user_session.query(DeviceLibrary.id).filter(DeviceLibrary.device_mo == device_mo,DeviceLibrary.device_ty == device_ty).first()

                files = self.request.files
                # file_path = '/home/<USER>/device/' + timeUtils.getNewTimeStr()[:4]
                # # file_path = '/home/<USER>/device/' + timeUtils.getNewTimeStr()[:4]
                # if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                #     os.makedirs(file_path)
                imgs = files.get('files')
                if imgs:
                    for i in imgs:
                        data = i.get('body')
                        filename = i.get('filename')
                        # doc_format = str(os.path.splitext(filename)[1])  # 格式
                        # uploadfilename = str(uuid.uuid1()) + doc_format
                        # path = '%s/%s' % (file_path, uploadfilename)
                        # file = open(path, 'wb')
                        # file.write(data)
                        # file.close()
                        url = upload_file(data, 'rhyc', filename)
                        url = url.split('?', 1)[0]
                        pp = DeviceAnnex(device_id=page_id[0],annex_url=url,file_name=filename)
                        user_session.add(pp)
                        user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UpdateDevice': # 修改设备
                id = self.get_argument('id', None)  # 设备id
                device_mo = self.get_argument('device_mo', None) #"设备型号"
                device_ty = self.get_argument('device_ty', None) #"设备类型"
                ra_power = self.get_argument('ra_power', None) #"额定功率,单位kW"
                ra_volume =self.get_argument('ra_volume', None) #"额定容量,单位Ah"
                ra_energy = self.get_argument('ra_energy', None) #"额定能量,单位kWh"
                vender = self.get_argument('vender', None) #"厂家"
                remarks = self.get_argument('remarks', None) #"基础信息备注"
                Annex_id = self.get_argument('Annex_id', None) #"基础信息备注"
                if not device_mo or not device_ty or not id or not Annex_id:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''

                if DEBUG:
                    logging.info('device_mo:%s,device_ty:%s,ra_power:%s,ra_volume:%s,Annex_id:%s'%(device_mo,device_ty,ra_power,ra_volume,Annex_id))

                page = user_session.query(DeviceLibrary).filter(DeviceLibrary.id == id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                if lang == 'en':
                    pa = user_session.query(DeviceLibrary).filter(DeviceLibrary.en_device_mo == device_mo,DeviceLibrary.en_device_ty == device_ty,DeviceLibrary.is_use == '1').first()
                    if pa:
                        return self.customError("Data already exists")
                else:
                    pa = user_session.query(DeviceLibrary).filter(DeviceLibrary.device_mo == device_mo,DeviceLibrary.device_ty == device_ty,DeviceLibrary.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在")

                if device_mo :
                    if lang == 'en':
                        page.device_mo = translate_text(device_mo, 1)
                        page.en_device_mo = device_mo
                    else:
                        page.device_mo = device_mo
                        page.en_device_mo = translate_text(device_mo, 2)
                if device_ty:
                    if lang == 'en':
                        page.device_ty = translate_text(device_ty, 1)
                        page.en_device_ty = device_ty
                    else:
                        page.device_ty = device_ty
                        page.en_device_ty = translate_text(device_ty, 2)
                if vender :
                    if lang == 'en':
                        page.vender = translate_text(vender, 1)
                        page.en_vender = vender
                    else:
                        page.vender = vender
                        page.en_vender = translate_text(vender, 2)
                if remarks :
                    if lang == 'en':
                        page.remarks = translate_text(remarks, 1)
                        page.en_remarks = remarks
                    else:
                        page.remarks = remarks
                        page.en_remarks = translate_text(remarks, 2)

                if ra_power :
                    page.ra_power = ra_power
                if ra_volume :
                    page.ra_volume = ra_volume
                if ra_energy :
                    page.ra_energy = ra_energy
                if vender :
                    page.vender = vender
                if remarks :
                    page.remarks = remarks

                #######     新增逻辑begin       ##########
                # 查出设备类型id
                device_type_data = user_session.query(DeviceType).filter(DeviceType.name == device_ty).first()
                device_type_id = device_type_data.id
                # 查询厂家id
                device_factory_id = None
                if vender:
                    if page.factory_id:
                        fac_data = user_session.query(Manufacturer).filter(Manufacturer.id == page.factory_id).first()
                        if fac_data:
                            fac_data.descr = vender
                    else:
                        device_factory_data = user_session.query(Manufacturer).filter(
                            Manufacturer.descr == vender).first()
                        if device_factory_data:
                            device_factory_id = device_factory_data.id
                        else:
                            # 如果没查询到厂家则新增厂家
                            dev_factiry_p = Manufacturer(descr=vender, op_ts=timeUtils.getNewTimeStr())
                            user_session.add(dev_factiry_p)
                            user_session.flush()
                            device_factory_id = dev_factiry_p.id
                # 编辑设备型号表
                s_device_no_id = None
                if page.device_no_id:
                    device_no_data = user_session.query(DeviceNo).filter(DeviceNo.id == page.device_no_id).first()
                    if device_no_data:
                        device_no_data.name = device_mo
                    else:
                        device_n_data = user_session.query(DeviceNo).filter(DeviceNo.name == device_mo).first()
                        if device_n_data:
                            s_device_no_id = device_n_data.id
                    # s_device_type_id = device_no_data.type_id
                    # s_device_factory_id = device_no_data.factory_id
                    # if s_device_type_id != device_type_id:
                    #     return self.customError("设备型号与设备类型存在冲突")
                    # if s_device_factory_id and device_factory_id and s_device_factory_id != device_factory_id:
                    #     return self.customError("设备型号与厂家存在冲突")
                    # if device_factory_id and not s_device_factory_id:
                    #     user_session.query(DeviceNo).filter(DeviceNo.id == s_device_no_id).update(
                    #         {'factory_id': device_factory_id},
                    #         synchronize_session=False)
                    # else:
                    #     # 未找到则先添加设备型号表
                    #     dev_type_n = DeviceNo(name=device_mo, type_id=device_type_id, factory_id=device_factory_id,
                    #                           op_ts=timeUtils.getNewTimeStr())
                    #     user_session.add(dev_type_n)
                    #     user_session.flush()
                    #     s_device_no_id = dev_type_n.id
                if s_device_no_id:
                    page.device_no_id = s_device_no_id
                if device_type_id:
                    page.device_type_id = device_type_id
                if device_factory_id:
                    page.factory_id = device_factory_id
                #######     新增逻辑end       ##########
                user_session.commit()
                Annex_id = json.loads(Annex_id)
                DeviceAnnexs = user_session.query(DeviceAnnex.id).filter(DeviceAnnex.device_id == id).all()
                for d in DeviceAnnexs:#库里的附件id 是否在传来的列表里，不在的话，就删除
                    if str(d[0]) not in Annex_id:
                        user_session.query(DeviceAnnex).filter(DeviceAnnex.id == d[0]).delete()
                        user_session.commit()

                # page_id = user_session.query(DeviceLibrary.id).filter(DeviceAnnex.device_id == id).first()

                files = self.request.files
                # file_path = '/home/<USER>/device/' + timeUtils.getNewTimeStr()[:4]
                # # file_path = '/home/<USER>/device/' + timeUtils.getNewTimeStr()[:4]
                # if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                #     os.makedirs(file_path)
                minio_client = MinioTool()
                # url_list = {}  # 附件上传地址
                # for file in files:
                #     url = minio_client.upload_object(file, bucket_name='tianlu')
                #     url_list[file.name] = url
                imgs = files.get('files')
                if imgs:
                    for i in imgs:
                        # data = i.get('body')
                        filename = i.get('filename')
                        # doc_format = str(os.path.splitext(filename)[1])  # 格式
                        # uploadfilename = str(uuid.uuid1()) + doc_format
                        # path = '%s/%s' % (file_path, uploadfilename)
                        # file = open(path, 'wb')
                        # file.write(data)
                        # file.close()
                        data = i.get('body')
                        url = upload_file(data, 'devicefile', filename)
                        pp = DeviceAnnex(device_id=id, annex_url=url, file_name=filename)
                        user_session.add(pp)
                        user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')

            elif kt == 'LogicDeleteDoc':  # 逻辑删除设备库
                id = self.get_argument('id', None)#设备id
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(DeviceLibrary).filter(DeviceLibrary.id == id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = 0
                if page.device_no_id:
                    user_session.query(DeviceNo).filter(DeviceNo.id == page.device_no_id).update(
                        {'is_use': 0},
                        synchronize_session=False)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'ProjectDeviceAdd': # 添加项目设备
                id = self.get_argument('id', None) #"项目id"
                descr = self.get_argument('descr', None) #"项目名称"
                name = self.get_argument('name', None) #"电站英文名"
                unit = self.get_argument('unit', None)  # "单元名称"
                list1 = self.get_argument('list1', []) #"单元信息"
                if not id or not descr or not name or not unit:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,list1:%s,unit:%s'%(id,descr,name,list1,unit))
                if lang == 'en':
                    page = user_session.query(Equipment).filter(Equipment.en_descr == descr, Equipment.station == name,Equipment.en_name == unit,Equipment.is_use == '1').first()
                    if page:
                        return self.customError("Data already exists")
                else:
                    page = user_session.query(Equipment).filter(Equipment.descr == descr, Equipment.station == name,Equipment.name == unit,Equipment.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")

                PCS = {}
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            if ii['pcs_num'] in PCS:
                                if lang=='en':
                                    return self.customError("Data already exists 2")
                                else:
                                    return self.customError("数据已存在2")
                            else:
                                PCS = {ii['pcs_num']: ""}
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui.keys():
                                    ii['dui'][indx_1]['cu'].append(iii['cu'][0])#取相同堆名称里的簇放一起
                                else:
                                    dui = {iii['dui_num']: ""}
                                    indx_1= ii['dui'].index(iii)
                            for iii in ii['dui']:
                                cu = {}
                                for iiii in iii['cu']:
                                    if iiii['cu_num'] in cu.keys():
                                        if lang == 'en':
                                            return self.customError("Data already exists 3")
                                        else:
                                            return self.customError("数据已存在3")
                                    else:
                                        cu = {iiii['cu_num']: ""}
                if lang=='en':
                    zh_name = translate_text(unit, 1)
                    zh_descr = translate_text(descr, 1)
                    p1 = Equipment(id=uuid.uuid1(), name=zh_name, parent_id=id, station=name, descr=zh_descr,equ_ty=1,en_name=unit, en_descr=descr)  # 保存储能单元
                else:
                    en_name = translate_text(unit, 2)
                    en_descr = translate_text(descr, 2)
                    p1 = Equipment(id=uuid.uuid1(), name=unit, parent_id=id, station=name, descr=descr, equ_ty=1,en_name=en_name, en_descr=en_descr)  # 保存储能单元
                user_session.add(p1)
                user_session.commit()

                if list1:
                    # list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            # 保存PCS
                            if lang=='en':
                                zh_descr = translate_text(descr, 1)
                                zh_pcs_s_name = translate_text(descr, 1)
                                zh_pcs_name = translate_text(ii['pcs_name'], 1)
                                p2 = Equipment(id=uuid.uuid1(), name=ii['pcs_num'], parent_id=p1.id, station=name,
                                               descr=zh_descr, equ_ty=2, pcs_name=zh_pcs_name,
                                               pcs_ty_num=ii['pcs_ty_num'], pcs_s_name=zh_pcs_s_name,
                                               act_acc_ene=ii['act_acc_ene'], en_name=ii['pcs_num'],en_descr=descr, en_pcs_s_name=ii['pcs_s_name'],
                                               en_pcs_name=ii['pcs_name'])
                            else:
                                en_descr = translate_text(descr, 2)
                                en_pcs_s_name = translate_text(descr, 2)
                                en_pcs_name = translate_text(ii['pcs_name'], 2)
                                p2 = Equipment(id=uuid.uuid1(),name=ii['pcs_num'], parent_id=p1.id, station=name, descr=descr,equ_ty=2, pcs_name=ii['pcs_name'], pcs_ty_num=ii['pcs_ty_num'],pcs_s_name=ii['pcs_s_name'],
                                               act_acc_ene=ii['act_acc_ene'], en_name=ii['pcs_num'],en_descr=en_descr, en_pcs_s_name=en_pcs_s_name, en_pcs_name=en_pcs_name)
                            user_session.add(p2)
                            user_session.commit()
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui:
                                    pass
                                else:
                                    dui = {iii['dui_num']: ""}
                                    # 保存堆
                                    if lang == 'en':
                                        zh_descr = translate_text(descr, 1)
                                        p3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=p2.id, station=name, descr=zh_descr,equ_ty=3,en_name=iii['dui_num'],en_descr=descr)
                                    else:
                                        en_descr = translate_text(descr, 2)
                                        p3 = Equipment(id=uuid.uuid1(), name=iii['dui_num'], parent_id=p2.id,
                                                       station=name, descr=descr, equ_ty=3, en_name=iii['dui_num'],en_descr=en_descr)
                                    user_session.add(p3)
                                    user_session.commit()
                                    cu = {}
                                    for iiii in iii['cu']:
                                        if iiii['cu_num'] in cu:
                                            pass
                                        else:
                                            cu = {iiii['cu_num']: ""}
                                            #保存簇
                                            if lang == 'en':
                                                zh_descr = translate_text(descr, 1)
                                                p4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=p3.id, station=name, descr=zh_descr,equ_ty=4, cool_mode=iiii['cool_mode'],cell_type=iiii['cell_type'],
                                                               cell_num=iiii['cell_num'], en_name=iiii['cu_num'],en_descr=descr)
                                            else:
                                                en_descr = translate_text(descr, 2)
                                                p4 = Equipment(id=uuid.uuid1(), name=iiii['cu_num'], parent_id=p3.id,
                                                               station=name, descr=descr, equ_ty=4,
                                                               cool_mode=iiii['cool_mode'], cell_type=iiii['cell_type'],
                                                               cell_num=iiii['cell_num'], en_name=iiii['cu_num'],en_descr=en_descr)
                                            user_session.add(p4)
                                            user_session.commit()
                                            for iiiii in iiii['cell']:
                                                # 保存电芯
                                                if lang == 'en':
                                                    zh_descr = translate_text(descr, 1)
                                                    p5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=p4.id, station=name,descr=zh_descr, equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],total_ene=iiiii['total_ene'],
                                                                   cell_s_na=iiiii['cell_s_na'],bms_num=iiiii['bms_num'], en_name=iiiii['cell_ty_num'],en_descr=descr)
                                                else:
                                                    en_descr = translate_text(descr, 2)
                                                    p5 = Equipment(id=uuid.uuid1(), name=iiiii['cell_ty_num'],
                                                                   parent_id=p4.id, station=name, descr=descr, equ_ty=5,
                                                                   cell_s_ene=iiiii['cell_s_ene'],
                                                                   total_ene=iiiii['total_ene'],
                                                                   cell_s_na=iiiii['cell_s_na'],
                                                                   bms_num=iiiii['bms_num'], en_name=iiiii['cell_ty_num'],en_descr=en_descr)
                                                user_session.add(p5)
                                                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'ProjectDeviceUpdate': # 修改项目设备
                id = self.get_argument('id', None) #"项目id"
                descr = self.get_argument('descr', None) #"项目名称"
                name = self.get_argument('name', None) #"电站英文名"
                unit = self.get_argument('unit', None) #"单元名称"
                unit_id = self.get_argument('unit_id', None) #"单元id"
                list1 = self.get_argument('list1', []) #"单元信息"
                if not id or not descr or not name or not unit or not unit_id:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,unit:%s,unit_id:%s,list1:%s'%(id,descr,name,unit,unit_id,list1))
                page = user_session.query(Equipment).filter(Equipment.id == unit_id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                if lang == 'en':
                    pa = user_session.query(Equipment).filter(Equipment.en_descr == descr, Equipment.station == name,Equipment.en_name == unit,Equipment.is_use == '1').first()
                    if pa and pa.id != unit_id:
                        return self.customError("Data already exists")
                else:
                    pa = user_session.query(Equipment).filter(Equipment.descr == descr, Equipment.station == name,Equipment.name == unit,Equipment.is_use == '1').first()
                    if pa and pa.id != unit_id:
                        return self.customError("数据已存在")

                PCS = {}
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            if ii['pcs_num'] in PCS:
                                if lang=='en':
                                    return self.customError("Data already exists 2")
                                else:
                                    return self.customError("数据已存在2")
                            else:
                                PCS = {ii['pcs_num']: ""}
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui.keys():
                                    ii['dui'][indx_1]['cu'].append(iii['cu'][0])  # 取相同堆名称里的簇放一起
                                else:
                                    dui = {iii['dui_num']: ""}
                                    indx_1 = ii['dui'].index(iii)
                            for iii in ii['dui']:
                                cu = {}
                                for iiii in iii['cu']:
                                    if iiii['cu_num'] in cu.keys():
                                        if lang == 'en':
                                            return self.customError("Data already exists 3")
                                        else:
                                            return self.customError("数据已存在3")
                                    else:
                                        cu = {iiii['cu_num']: ""}

                if list1:
                    # list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            print('=' * 100)
                            if 'pcs_num_id' in ii:
                                page1 = user_session.query(Equipment).filter(Equipment.id == ii['pcs_num_id']).first()
                                if not page1:
                                    if lang == 'en':
                                        return self.customError("Invalid id_1")
                                    else:
                                        return self.customError("无效id_1")
                                page1.name = ii['pcs_num']
                                page1.en_name = ii['pcs_num']
                                page1.pcs_ty_num = ii['pcs_ty_num']
                                if lang=='en':
                                    zh_pcs_s_name = translate_text(ii['pcs_s_name'], 1)
                                    zh_pcs_name = translate_text(ii['pcs_name'], 1)
                                    page1.pcs_name = zh_pcs_name
                                    page1.en_pcs_name = ii['pcs_name']
                                    page1.pcs_s_name = zh_pcs_s_name
                                    page1.en_pcs_s_name = ii['pcs_s_name']
                                else:
                                    en_pcs_name = translate_text(ii['pcs_name'], 2)
                                    page1.pcs_name = ii['pcs_name']
                                    page1.en_pcs_name = en_pcs_name
                                    en_pcs_s_name = translate_text(ii['pcs_s_name'], 2)
                                    page1.pcs_s_name = ii['pcs_s_name']
                                    page1.en_pcs_s_name = en_pcs_s_name
                                page1.pcs_s_name = ii['pcs_s_name']
                                page1.act_acc_ene = ii['act_acc_ene']
                                dui = {}
                                for iii in ii['dui']:
                                    if iii['dui_num'] in dui.keys():
                                        pass
                                    else:
                                        dui = {iii['dui_num']: ""}
                                        if 'dui_num_id' in iii:
                                            page2 = user_session.query(Equipment).filter(Equipment.id == iii['dui_num_id']).first()
                                            if not page2:
                                                if lang == 'en':
                                                    return self.customError("Invalid id_2")
                                                else:
                                                    return self.customError("无效id_2")
                                            page2.name = iii['dui_num']
                                            page2.en_name = iii['dui_num']
                                            cu = {}
                                            for iiii in iii['cu']:
                                                if iiii['cu_num'] in cu.keys():
                                                    pass
                                                else:
                                                    cu = {iiii['cu_num']: ""}
                                                    if 'cu_num_id' in iiii:
                                                        page3 = user_session.query(Equipment).filter(Equipment.id == iiii['cu_num_id']).first()
                                                        if not page3:
                                                            if lang == 'en':
                                                                return self.customError("Invalid id_3")
                                                            else:
                                                                return self.customError("无效id_3")
                                                        page3.name = iiii['cu_num']
                                                        page3.en_name = iiii['cu_num']
                                                        if lang=='en':
                                                            zh_cool_mode = translate_text(iiii['cool_mode'], 1)
                                                            zh_cell_type = translate_text(iiii['cell_type'], 1)
                                                            page3.cool_mode = zh_cool_mode
                                                            page3.cell_type = zh_cell_type
                                                            page3.en_cool_mode = iiii['cool_mode']
                                                            page3.en_cell_type = iiii['cell_type']
                                                        else:
                                                            en_cool_mode = translate_text(iiii['cool_mode'], 2)
                                                            en_cell_type = translate_text(iiii['cell_type'], 2)
                                                            page3.cool_mode = iiii['cool_mode']
                                                            page3.cell_type = iiii['cell_type']
                                                            page3.en_cool_mode = en_cool_mode
                                                            page3.en_cell_type = en_cell_type
                                                        page3.cell_num = iiii['cell_num']
                                                        for iiiii in iiii['cell']:
                                                            if 'cell_ty_num_id' in iiiii:
                                                                page4 = user_session.query(Equipment).filter(Equipment.id == iiiii['cell_ty_num_id']).first()
                                                                if not page4:
                                                                    if lang == 'en':
                                                                        return self.customError("Invalid id_4")
                                                                    else:
                                                                        return self.customError("无效id_4")
                                                                if lang == 'en':
                                                                    zh_cell_s_na = translate_text(iiiii['cell_s_na'], 1)
                                                                    page4.cell_s_na = zh_cell_s_na
                                                                    page4.name = iiiii['cell_ty_num']
                                                                    page4.en_cell_s_na = iiiii['cell_s_na']
                                                                    page4.en_name = iiiii['cell_ty_num']
                                                                else:
                                                                    en_cell_s_na = translate_text(iiiii['cell_s_na'], 2)
                                                                    page4.cell_s_na = iiiii['cell_s_na']
                                                                    page4.name = iiiii['cell_ty_num']
                                                                    page4.en_cell_s_na = en_cell_s_na
                                                                    page4.en_name = iiiii['cell_ty_num']
                                                                page4.cell_s_ene = iiiii['cell_s_ene']
                                                                page4.total_ene = iiiii['total_ene']
                                                                page4.bms_num = iiiii['bms_num']
                                                            else:
                                                                # 保存电芯
                                                                if lang=='en':
                                                                    zh_descr = translate_text(descr, 1)
                                                                    zh_cell_s_na = translate_text(iiiii['cell_s_na'], 1)
                                                                    pppp5 = Equipment(name=iiiii['cell_ty_num'],
                                                                                      parent_id=iiii['cu_num_id'],
                                                                                      station=name, descr=zh_descr,
                                                                                      equ_ty=5,
                                                                                      cell_s_ene=iiiii['cell_s_ene'],
                                                                                      total_ene=iiiii['total_ene'],
                                                                                      cell_s_na=zh_cell_s_na,
                                                                                      bms_num=iiiii['bms_num'],en_name=iiiii['cell_ty_num'],en_descr=descr,en_cell_s_na=iiiii['cell_s_na'])
                                                                else:
                                                                    en_descr = translate_text(descr, 2)
                                                                    en_cell_s_na = translate_text(iiiii['cell_s_na'], 2)
                                                                    pppp5 = Equipment(name=iiiii['cell_ty_num'],
                                                                                     parent_id=iiii['cu_num_id'],
                                                                                     station=name, descr=descr,
                                                                                     equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],
                                                                                     total_ene=iiiii['total_ene'],
                                                                                     cell_s_na=iiiii['cell_s_na'],
                                                                                     bms_num=iiiii['bms_num'],en_name=iiiii['cell_ty_num'],en_descr=en_descr,en_cell_s_na=en_cell_s_na)
                                                                user_session.add(pppp5)
                                                                user_session.commit()
                                                    else:
                                                        if lang == 'en':
                                                            zh_descr = translate_text(descr, 1)
                                                            zh_cell_type = translate_text(iiii['cell_type'], 1)
                                                            ppp4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=iii['dui_num_id'],
                                                                            station=name, descr=zh_descr, equ_ty=4,
                                                                            cool_mode=iiii['cool_mode'],
                                                                            cell_type=zh_cell_type,
                                                                            cell_num=iiii['cell_num'],en_name=iiii['cu_num'],en_descr=descr,en_cell_type=iiii['cell_type'])
                                                        else:
                                                            en_descr = translate_text(descr,2)
                                                            en_cell_type = translate_text(iiii['cell_type'], 2)
                                                            ppp4 = Equipment(id=uuid.uuid1(), name=iiii['cu_num'],
                                                                             parent_id=iii['dui_num_id'],
                                                                             station=name, descr=descr, equ_ty=4,
                                                                             cool_mode=iiii['cool_mode'],
                                                                             cell_type=iiii['cell_type'],
                                                                             cell_num=iiii['cell_num'],
                                                                             en_name=iiii['cu_num'],
                                                                             en_descr=en_descr, en_cell_type=en_cell_type)
                                                        user_session.add(ppp4)
                                                        user_session.commit()
                                                        for iiiii in iiii['cell']:
                                                            # 保存电芯
                                                            if lang == 'en':
                                                                zh_descr = translate_text(descr, 1)
                                                                zh_cell_s_na = translate_text(iiii['cell_type'], 1)
                                                                ppp5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=ppp4.id,
                                                                                station=name, descr=zh_descr,
                                                                                equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],
                                                                                total_ene=iiiii['total_ene'],
                                                                                cell_s_na=zh_cell_s_na,
                                                                                bms_num=iiiii['bms_num'],en_name=iiiii['cell_ty_num'], en_descr=descr,en_cell_s_na=iiiii['cell_s_na'])
                                                            else:
                                                                en_descr = translate_text(descr, 2)
                                                                en_cell_s_na = translate_text(iiii['cell_type'], 2)
                                                                ppp5 = Equipment(id=uuid.uuid1(),
                                                                                 name=iiiii['cell_ty_num'],
                                                                                 parent_id=ppp4.id,
                                                                                 station=name, descr=descr,
                                                                                 equ_ty=5,
                                                                                 cell_s_ene=iiiii['cell_s_ene'],
                                                                                 total_ene=iiiii['total_ene'],
                                                                                 cell_s_na=iiiii['cell_s_na'],
                                                                                 bms_num=iiiii['bms_num'],
                                                                                 en_name=iiiii['cell_ty_num'],
                                                                                 en_descr=en_descr,
                                                                                 en_cell_s_na=en_cell_s_na)
                                                            user_session.add(ppp5)
                                                            user_session.commit()
                                        else:
                                            if lang == 'en':
                                                zh_descr = translate_text(descr, 1)
                                                pp3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=ii['pcs_num_id'],station=name, descr=zh_descr, equ_ty=3,en_name=iii['dui_num'], en_descr=descr)
                                            else:
                                                en_descr = translate_text(descr, 2)
                                                pp3 = Equipment(id=uuid.uuid1(), name=iii['dui_num'],
                                                                parent_id=ii['pcs_num_id'], station=name,
                                                                descr=descr, equ_ty=3, en_name=iii['dui_num'],
                                                                en_descr=en_descr)
                                            user_session.add(pp3)
                                            user_session.commit()
                                            for iiii in iii['cu']:
                                                # 保存簇
                                                if lang == 'en':
                                                    zh_descr = translate_text(descr, 1)
                                                    zh_cell_type = translate_text(iiii['cell_type'], 1)
                                                    pp4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=pp3.id, station=name,descr=zh_descr, equ_ty=4, cool_mode=iiii['cool_mode'],cell_type=zh_cell_type,cell_num=iiii['cell_num'],
                                                                    en_name=iiii['cu_num'],en_descr=descr,en_cell_type=iiii['cell_type'])
                                                else:
                                                    en_descr = translate_text(descr, 2)
                                                    en_cell_type = translate_text(iiii['cell_type'], 2)
                                                    pp4 = Equipment(id=uuid.uuid1(), name=iiii['cu_num'],
                                                                    parent_id=pp3.id, station=name, descr=descr,
                                                                    equ_ty=4, cool_mode=iiii['cool_mode'],
                                                                    cell_type=iiii['cell_type'], cell_num=iiii['cell_num'],
                                                                    en_name=iiii['cu_num'], en_descr=en_descr,
                                                                    en_cell_type=en_cell_type)
                                                user_session.add(pp4)
                                                user_session.commit()
                                                for iiiii in iiii['cell']:
                                                    # 保存电芯
                                                    if lang == 'en':
                                                        zh_descr = translate_text(descr, 1)
                                                        zh_cell_s_na = translate_text(iiiii['cell_s_na'], 1)
                                                        pp5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=pp4.id, station=name,descr=zh_descr, equ_ty=5,cell_s_ene=iiiii['cell_s_ene'], total_ene=iiiii['total_ene'],
                                                                   cell_s_na=zh_cell_s_na, bms_num=iiiii['bms_num'],en_name=iiiii['cell_ty_num'],en_descr=descr,en_cell_s_na=iiiii['cell_s_na'])
                                                    else:
                                                        en_descr = translate_text(descr, 2)
                                                        en_cell_s_na = translate_text(iiiii['cell_s_na'], 2)
                                                        pp5 = Equipment(id=uuid.uuid1(), name=iiiii['cell_ty_num'],
                                                                        parent_id=pp4.id, station=name, descr=descr,
                                                                        equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],
                                                                        total_ene=iiiii['total_ene'],
                                                                        cell_s_na=iiiii['cell_s_na'],
                                                                        bms_num=iiiii['bms_num'],
                                                                        en_name=iiiii['cell_ty_num'], en_descr=en_descr,
                                                                        en_cell_s_na=en_cell_s_na)
                                                    user_session.add(pp5)
                                                    user_session.commit()
                            else:
                                if lang == 'en':
                                    zh_descr = translate_text(descr, 1)
                                    zh_pcs_s_name = translate_text(ii['pcs_s_name'], 1)
                                    zh_pcs_name = translate_text(ii['pcs_name'], 1)
                                    p2 = Equipment(id=uuid.uuid1(),name=ii['pcs_num'], parent_id=unit_id, station=name, descr=zh_descr, equ_ty=2,en_pcs_name=ii['pcs_name'], pcs_ty_num=ii['pcs_ty_num'],pcs_s_name=zh_pcs_s_name,
                                               act_acc_ene=ii['act_acc_ene'],en_name=ii['pcs_num'], en_descr=descr,en_pcs_s_name=ii['pcs_s_name'], pcs_name=zh_pcs_name)
                                else:
                                    en_descr = translate_text(descr, 2)
                                    en_pcs_s_name = translate_text(ii['pcs_s_name'], 2)
                                    en_pcs_name = translate_text(ii['pcs_name'], 2)
                                    p2 = Equipment(id=uuid.uuid1(), name=ii['pcs_num'], parent_id=unit_id, station=name,
                                                   descr=descr, equ_ty=2, pcs_name=ii['pcs_name'],
                                                   pcs_ty_num=ii['pcs_ty_num'], pcs_s_name=ii['pcs_s_name'],
                                                   act_acc_ene=ii['act_acc_ene'], en_name=ii['pcs_num'], en_descr=en_descr,
                                                   en_pcs_s_name=en_pcs_s_name, en_pcs_name=en_pcs_name)
                                user_session.add(p2)
                                user_session.commit()
                                for iii in ii['dui']:
                                    if lang == 'en':
                                        zh_descr = translate_text(descr, 1)
                                        p3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=p2.id, station=name, descr=zh_descr,equ_ty=3,en_name=iii['dui_num'], en_descr=descr)
                                    else:
                                        en_descr = translate_text(descr, 2)
                                        p3 = Equipment(id=uuid.uuid1(), name=iii['dui_num'], parent_id=p2.id,
                                                       station=name, descr=descr, equ_ty=3, en_name=iii['dui_num'],
                                                       en_descr=en_descr)
                                    user_session.add(p3)
                                    user_session.commit()
                                    for iiii in iii['cu']:
                                        if lang == 'en':
                                            zh_descr = translate_text(descr, 1)
                                            zh_cool_mode = translate_text(iiii['cool_mode'], 1)
                                            zh_cell_type = translate_text(iiii['cell_type'], 1)
                                            p4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=p3.id, station=name, descr=zh_descr,equ_ty=4, cool_mode=zh_cool_mode,cell_type=zh_cell_type,
                                                           cell_num=iiii['cell_num'],en_name=iiii['cu_num'], en_descr=descr, en_cool_mode=iiii['cool_mode'],en_cell_type=iiii['cell_type'])
                                        else:
                                            en_descr = translate_text(descr, 2)
                                            en_cool_mode = translate_text(iiii['cool_mode'], 2)
                                            en_cell_type = translate_text(iiii['cell_type'], 2)
                                            p4 = Equipment(id=uuid.uuid1(), name=iiii['cu_num'], parent_id=p3.id,
                                                           station=name, descr=descr, equ_ty=4,
                                                           cool_mode=iiii['cool_mode'], cell_type=iiii['cell_type'],
                                                           cell_num=iiii['cell_num'], en_name=iiii['cu_num'],
                                                           en_descr=en_descr, en_cool_mode=en_cool_mode,
                                                           en_cell_type=en_cell_type)
                                        user_session.add(p4)
                                        user_session.commit()
                                        for iiiii in iiii['cell']:
                                            if lang == 'en':
                                                zh_descr = translate_text(descr, 1)
                                                zh_cell_s_ene = translate_text(iiiii['cell_s_ene'], 1)
                                                zh_cell_s_na = translate_text(iiiii['cell_s_na'], 1)
                                                p5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=p4.id, station=name,descr=zh_descr, equ_ty=5, cell_s_ene=zh_cell_s_ene,total_ene=iiiii['total_ene'],
                                                               cell_s_na=zh_cell_s_na,bms_num=iiiii['bms_num'],en_name=iiiii['cell_ty_num'],en_descr=descr, en_cell_s_ene=iiiii['cell_s_ene'],en_cell_s_na=iiiii['cell_s_na'])
                                            else:
                                                en_descr = translate_text(descr, 2)
                                                en_cell_s_ene = translate_text(iiiii['cell_s_ene'], 2)
                                                en_cell_s_na = translate_text(iiiii['cell_s_na'], 2)
                                                p5 = Equipment(id=uuid.uuid1(), name=iiiii['cell_ty_num'],
                                                               parent_id=p4.id, station=name, descr=descr, equ_ty=5,
                                                               cell_s_ene=iiiii['cell_s_ene'],
                                                               total_ene=iiiii['total_ene'],
                                                               cell_s_na=iiiii['cell_s_na'], bms_num=iiiii['bms_num'],
                                                               en_name=iiiii['cell_ty_num'], en_descr=en_descr,
                                                               en_cell_s_ene=en_cell_s_ene,
                                                               en_cell_s_na=en_cell_s_na)
                                            user_session.add(p5)
                                            user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeleteUnit':  # 逻辑删除储能单元
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page0 = user_session.query(Equipment).filter(Equipment.id == id).first()  # 单元
                if not page0:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page0.is_use = 0  # 单元

                page = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#PCS
                if page:
                    for p in page:
                        p.is_use = 0  # PCS
                        page1 = user_session.query(Equipment).filter(Equipment.parent_id == p.id).all()#堆
                        if page1:
                            for i in page1:
                                i.is_use = 0
                                page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 簇
                                if page2:
                                    for ii in page2:
                                        ii.is_use = 0
                                        page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id).all()  # 电芯
                                        if page3:
                                            for iii in page3:
                                                iii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeletePCS':  # 逻辑删除PCS
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(Equipment).filter(Equipment.id == id).first()#PCS
                if not page:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = 0  # PCS
                page1 = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#堆
                if page1:
                    for i in page1:
                        i.is_use = 0
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 簇
                        if page2:
                            for ii in page2:
                                ii.is_use = 0
                                page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id).all()  # 电芯
                                if page3:
                                    for iii in page3:
                                        iii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeleteDui':  # 逻辑删除堆
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(Equipment).filter(Equipment.id == id).first()#堆
                if not page:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = 0
                page1 = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#簇
                if page1:
                    for i in page1:
                        i.is_use = 0
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 电芯
                        if page2:
                            for ii in page2:
                                ii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AddMea':  # 添加测点
                name = self.get_argument('name', None)  # "设备名称"
                num = self.get_argument('num', None)  # "编号"
                descr = self.get_argument('descr', None)  # "描述"
                id = self.get_argument('id', None)  # "设备表单元id"
                mea_point_n = self.get_argument('mea_point_n', None)  # "测点名称"
                type_ = self.get_argument('type_', None)  # "类型"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                station = self.get_argument('station', None)  # "所属站"
                if not name or not id or not type_ or not station:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''
                if DEBUG:
                    logging.info('name:%s,num:%s,t_eq_id:%s,mea_point_n:%s,type_:%s,remarks:%s,station:%s' % (
                    name, num, id, mea_point_n, type_, remarks, station))
                if type_ == '1':
                    if lang=='en':
                        page = user_session.query(RemoteTele).filter(RemoteTele.en_mea_point_n == mea_point_n,
                                                                     RemoteTele.num == num,
                                                                     RemoteTele.is_use == '1').first()
                        if page:
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            p = RemoteTele(name=zh_name, num=num, t_eq_id=id,mea_point_n=zh_mea_point_n, type_=type_, remarks=zh_remarks,station=station,en_name=name,en_mea_point_n=mea_point_n, en_remarks=remarks)
                    else:
                        page = user_session.query(RemoteTele).filter(RemoteTele.mea_point_n == mea_point_n,RemoteTele.num == num,RemoteTele.is_use == '1').first()
                        if page:
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            p = RemoteTele(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station,en_name=en_name,en_mea_point_n=en_mea_point_n, en_remarks=en_remarks)
                elif type_ == '2':
                    if lang=='en':
                        page = user_session.query(RemoteLetter).filter(RemoteLetter.en_mea_point_n == mea_point_n,
                                                                     RemoteLetter.num == num,
                                                                     RemoteLetter.is_use == '1').first()
                        if page:
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            p = RemoteLetter(name=zh_name, num=num, t_eq_id=id,mea_point_n=zh_mea_point_n, type_=type_, remarks=zh_remarks,station=station,en_name=name,en_mea_point_n=mea_point_n, en_remarks=remarks)
                    else:
                        page = user_session.query(RemoteLetter).filter(RemoteLetter.mea_point_n == mea_point_n,RemoteLetter.num == num,RemoteLetter.is_use == '1').first()
                        if page:
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            p = RemoteLetter(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station,en_name=en_name,en_mea_point_n=en_mea_point_n, en_remarks=en_remarks)

                        # p = RemoteLetter(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)
                elif type_ == '3':
                    if lang == 'en':
                        page = user_session.query(RemoteModula).filter(RemoteModula.en_mea_point_n == mea_point_n,
                                                                     RemoteModula.num == num,
                                                                     RemoteModula.is_use == '1').first()
                        if page:
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            p = RemoteModula(name=zh_name, num=num, t_eq_id=id, mea_point_n=zh_mea_point_n, type_=type_,
                                           remarks=zh_remarks, station=station, en_name=name,
                                           en_mea_point_n=mea_point_n, en_remarks=remarks)
                    else:
                        page = user_session.query(RemoteModula).filter(RemoteModula.mea_point_n == mea_point_n,
                                                                       RemoteModula.num == num,
                                                                       RemoteModula.is_use == '1').first()
                        if page:
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            p = RemoteModula(name=name, num=num, t_eq_id=id, mea_point_n=mea_point_n, type_=type_,
                                           remarks=remarks, station=station, en_name=en_name,
                                           en_mea_point_n=en_mea_point_n, en_remarks=en_remarks)
                    # else:
                    #     p = RemoteModula(name=name, num=num,t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)
                elif type_ == '4':
                    if lang == 'en':
                        page = user_session.query(RemoteTele).filter(RemoteTele.en_mea_point_n == mea_point_n,
                                                                     RemoteTele.num == num,
                                                                     RemoteTele.is_use == '1').first()
                        if page:
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            p = RemoteTele(name=zh_name, num=num, t_eq_id=id, mea_point_n=zh_mea_point_n, type_=type_,
                                           remarks=zh_remarks, station=station, en_name=name,
                                           en_mea_point_n=mea_point_n, en_remarks=remarks)
                    else:
                        page = user_session.query(RemoteControl).filter(RemoteControl.mea_point_n == mea_point_n,
                                                                       RemoteControl.num == num,
                                                                       RemoteControl.is_use == '1').first()
                        if page:
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            p = RemoteControl(name=name, num=num, t_eq_id=id, mea_point_n=mea_point_n, type_=type_,
                                           remarks=remarks, station=station, en_name=en_name,
                                           en_mea_point_n=en_mea_point_n, en_remarks=en_remarks)
                    # else:
                    #     p = RemoteControl(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)

                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UpdateMea':  # 修改测点
                id = self.get_argument('id', None)  # "测点id
                name = self.get_argument('name', None)  # "设备名称"
                num = self.get_argument('num', None)  # "编号"
                t_eq_id = self.get_argument('t_eq_id', None)  # "设备表id"
                mea_point_n = self.get_argument('mea_point_n', None)  # "测点名称"
                type_ = self.get_argument('type_', None)  # "类型"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                station = self.get_argument('station', None)  # "所属站"
                if not name  or not type_ or not station or not id :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''

                if DEBUG:
                    logging.info('id:%s,name:%s,num:%s,t_eq_id:%s,mea_point_n:%s,type_:%s,remarks:%s,station:%s' % (
                    id,name, num,t_eq_id, mea_point_n, type_, remarks, station))
                if type_ == '1':
                    page = user_session.query(RemoteTele).filter(RemoteTele.id == id).first()
                    if not page:
                        if lang == 'en':
                            return self.customError("Invalid id")
                        else:
                            return self.customError("无效id")
                    if lang=='en':
                        pa = user_session.query(RemoteTele).filter(RemoteTele.mea_point_n == mea_point_n,
                                                                   RemoteTele.num == num,
                                                                   RemoteTele.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            self.UpdateMea(zh_mea_point_n, zh_name, num, page, zh_remarks, station, t_eq_id, type_,name,mea_point_n,remarks)
                    else:
                        pa = user_session.query(RemoteTele).filter(RemoteTele.mea_point_n == mea_point_n,RemoteTele.num == num,RemoteTele.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_,en_name,en_mea_point_n,en_remarks)


                elif type_ == '2':
                    page = user_session.query(RemoteLetter).filter(RemoteLetter.id == id).first()
                    if lang == 'en':
                        pa = user_session.query(RemoteLetter).filter(RemoteLetter.mea_point_n == mea_point_n,
                                                                   RemoteLetter.num == num,
                                                                   RemoteLetter.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            self.UpdateMea(zh_mea_point_n, zh_name, num, page, zh_remarks, station, t_eq_id, type_,
                                           name, mea_point_n, remarks)
                    else:
                        pa = user_session.query(RemoteLetter).filter(RemoteLetter.mea_point_n == mea_point_n,
                                                                   RemoteLetter.num == num,
                                                                   RemoteLetter.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_, en_name,
                                           en_mea_point_n, en_remarks)

                    # self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)

                elif type_ == '3':
                    page = user_session.query(RemoteModula).filter(RemoteModula.id == id).first()
                    if lang == 'en':
                        pa = user_session.query(RemoteModula).filter(RemoteModula.mea_point_n == mea_point_n,
                                                                   RemoteModula.num == num,
                                                                   RemoteModula.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            self.UpdateMea(zh_mea_point_n, zh_name, num, page, zh_remarks, station, t_eq_id, type_,
                                           name, mea_point_n, remarks)
                    else:
                        pa = user_session.query(RemoteModula).filter(RemoteModula.mea_point_n == mea_point_n,
                                                                   RemoteModula.num == num,
                                                                   RemoteModula.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_, en_name,
                                           en_mea_point_n, en_remarks)
                    # self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)

                elif type_ == '4':
                    page = user_session.query(RemoteControl).filter(RemoteControl.id == id).first()
                    if lang == 'en':
                        pa = user_session.query(RemoteControl).filter(RemoteControl.mea_point_n == mea_point_n,
                                                                   RemoteControl.num == num,
                                                                   RemoteControl.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("Data already exists")
                        else:
                            zh_name = translate_text(name, 1)
                            zh_mea_point_n = translate_text(mea_point_n, 1)
                            zh_remarks = translate_text(remarks, 1)
                            self.UpdateMea(zh_mea_point_n, zh_name, num, page, zh_remarks, station, t_eq_id, type_,
                                           name, mea_point_n, remarks)
                    else:
                        pa = user_session.query(RemoteControl).filter(RemoteControl.mea_point_n == mea_point_n,
                                                                   RemoteControl.num == num,
                                                                   RemoteControl.is_use == '1').first()
                        if pa and pa.id != int(id):
                            return self.customError("数据已存在")
                        else:
                            en_name = translate_text(name, 2)
                            en_mea_point_n = translate_text(mea_point_n, 2)
                            en_remarks = translate_text(remarks, 2)
                            self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_, en_name,
                                           en_mea_point_n, en_remarks)
                    # self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeleteMea':  # 逻辑删除测点表
                id = self.get_argument('id', None)
                type_ = self.get_argument('type_', None)  # "类型"
                now_time = timeUtils.getNewTimeStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id or not type_ :
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,type_:%s' % (id,type_))

                if type_ == '1':
                    page = user_session.query(RemoteTele).filter(RemoteTele.id == id).first()
                    if not page:
                        if lang == 'en':
                            return self.customError("Invalid id")
                        else:
                            return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_tele', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '2':
                    page = user_session.query(RemoteLetter).filter(RemoteLetter.id == id).first()
                    if not page:
                        if lang == 'en':
                            return self.customError("Invalid id")
                        else:
                            return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_letter', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '3':
                    page = user_session.query(RemoteModula).filter(RemoteModula.id == id).first()
                    if not page:
                        if lang == 'en':
                            return self.customError("Invalid id")
                        else:
                            return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_modula', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '4':
                    page = user_session.query(RemoteControl).filter(RemoteControl.id == id).first()
                    if not page:
                        if lang == 'en':
                            return self.customError("Invalid id")
                        else:
                            return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_control', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'SingleAddDevice':  # 单个添加设备信息
                name = self.get_argument('name', None)  # "储能单元"
                area = self.get_argument('area', None)  # "分区"
                sn = self.get_argument('sn', None)  # "设备编号"
                type_id = self.get_argument('type_id', None)
                factory_id = self.get_argument('factory_id', None)  # "厂家"
                device_no_id = self.get_argument('device_no_id', None)  # "设备型号"
                project_id = self.get_argument('project_id', None)  # "项目ID"
                init_cap = self.get_argument('init_cap', None) # "设备额定容量单位为kWh"
                if type_id:
                    try:
                        type_id = int(type_id)
                    except ValueError:
                        return self.customError('参数类型名称类型错误')
                if factory_id:
                    try:
                        factory_id = int(factory_id)  # "厂家"
                    except ValueError:
                        return self.customError('参数厂家类型错误')
                if device_no_id:
                    try:
                        device_no_id = int(device_no_id)  # "设备型号"
                    except ValueError:
                        return self.customError('参数设备型号类型错误')
                if project_id:
                    try:
                        project_id = int(project_id)  # "项目ID"
                    except ValueError:
                        return self.customError('参数项目ID类型错误')
                if init_cap:
                    try:
                        init_cap = float(init_cap)  # "设备额定容量单位为kWh"
                    except ValueError:
                        return self.customError('参数设备额定容量类型错误')
                else:
                    init_cap = None

                init_no = self.get_argument('init_no', None)  # "设备出场编号"
                remark = self.get_argument('remark', None)  # "备注"
                if not name or not area or not type_id or not project_id:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remark = ';'.join(remark.split()) if remark else ''

                page = user_session.query(ProjectDevice).filter(ProjectDevice.name == name,
                                                                ProjectDevice.project_id == project_id,
                                                                ProjectDevice.area == area,
                                                                ProjectDevice.type_id == type_id,
                                                                ProjectDevice.sn == sn,
                                                                ProjectDevice.is_use == 1).first()
                if page:
                    if lang == 'en':
                        return self.customError("Data already exists")
                    else:
                        return self.customError("数据已存在")
                p = ProjectDevice(name=name, area=area, sn=sn, type_id=type_id, factory_id=factory_id,
                                  device_no_id=device_no_id, init_cap=init_cap, init_no=init_no,
                                  remark=remark, project_id=project_id, op_ts=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UploadDevicesFile': # 批量添加设备信息
                file_path = '/home/<USER>/workorderfiles'
                file_title = ['储能单元', '分区', '设备编号', '类型名称', '厂家', '设备型号', '设备出厂编号', '设备额定能量（kWh）']
                try:
                    project_id = int(self.get_argument('project_id', None))  # "项目ID"
                except ValueError:
                    return self.customError('参数项目ID类型错误')
                lang = self.get_argument('lang', None)
                files = self.request.files
                if not lang:
                    lang = 'zh'
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('file')

                data = imgs[0].get('body')
                filename = imgs[0].get('filename')
                if not filename.endswith(('.xls','.xlsx')):
                    return self.customError('上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入设备信息！') if lang == 'zh'\
                        else self.customError("Upload table file format error! Please reselect the file, or use the "
                                              "”Template Download“ button to download a standard format spreadsheet "
                                              "and enter device information in this template!")
                path = '%s/%s' % (file_path,filename)
                file = open(path, 'wb')
                file.write(data)
                file.close()
                df = pd.read_excel(path, header=None)
                # print 'df-------',df
                df = df.dropna(how='all', thresh=1)
                values_arr = df.values  # 二维矩阵
                values_arr[pd.isna(values_arr)] = None
                # 校验表头
                for i in range(len(file_title)):
                    if file_title[i] != values_arr[0][i]:
                        logging.error("标准title:%s  文件title:%s" % (file_title[i], values_arr[1][i]))
                        return self.customError(
                            '上传表格表头错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入设备信息！') if lang == 'zh' \
                            else self.customError(
                            "Upload table file format error! Please reselect the file, or use the ”Template "
                            "Download“ button to download a standard format spreadsheet and enter device "
                            "information in this template!")

                for v in range(1,len(values_arr)):  # 获得某一行的数据
                    # print 'vvv',v,values_arr[v][1]
                    value = values_arr[v]
                    name = value[0]
                    area = value[1]
                    type_name = value[3]
                    if not type_name or not area or not name:
                        user_session.rollback()
                        return self.customError(
                            '上传表格内容错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入设备信息！')
                for v in range(1, len(values_arr)):  # 获得某一行的数据
                    # print 'vvv',v,values_arr[v][1]
                    values = values_arr[v]
                    name = values[0]
                    area = values[1]
                    sn = values[2]
                    type_name = values[3]
                    factory_name = values[4]
                    device_no_name = values[5]
                    init_no = values[6]
                    init_cap = values[7]
                    if self.can_convert_to_float(init_cap):
                        init_cap = float(init_cap)
                    else:
                        init_cap = None
                    #类型id
                    device_type_data = user_session.query(DeviceType).filter(DeviceType.name == type_name).first()
                    if device_type_data:
                        type_id = device_type_data.id
                    else:
                        # 未找到则先添加设备类型表
                        dev_type_p = DeviceType(name=type_name, op_ts=timeUtils.getNewTimeStr())
                        user_session.add(dev_type_p)
                        user_session.flush()
                        type_id = dev_type_p.id
                    factory_id = None
                    if factory_name:
                        factory_data = user_session.query(Manufacturer).filter(Manufacturer.descr == factory_name).first()
                        if factory_data:
                            factory_id = factory_data.id
                        else:
                            factory_p = Manufacturer(descr=factory_name, op_ts=timeUtils.getNewTimeStr())
                            user_session.add(factory_p)
                            user_session.flush()
                            factory_id = factory_p.id
                    device_no_id = None
                    if device_no_name:
                        device_no_data = user_session.query(DeviceNo).filter(DeviceNo.name == device_no_name).first()
                        if device_no_data:
                            device_no_id = device_no_data.id
                        else:
                            device_no_p = DeviceNo(name=device_no_name, type_id=type_id, op_ts=timeUtils.getNewTimeStr())
                            user_session.add(device_no_p)
                            user_session.flush()
                            device_no_id = device_no_p.id

                    pro_data = user_session.query(ProjectDevice).filter(ProjectDevice.name == name,
                                                                  ProjectDevice.area == area,
                                                                  ProjectDevice.sn == sn,
                                                                  ProjectDevice.is_use == 1,
                                                                  ProjectDevice.init_no == init_no,
                                                                  ProjectDevice.project_id == project_id).first()

                    if pro_data:  # 已保存
                        pro_data.type_id = type_id
                        pro_data.factory_id = factory_id
                        pro_data.device_no_id = device_no_id
                    else:
                        p = ProjectDevice(name=name,area=area,sn=sn,type_id=type_id,factory_id=factory_id,
                                          device_no_id=device_no_id,init_no=init_no,init_cap=init_cap,
                                          project_id=project_id,op_ts=timeUtils.getNewTimeStr())
                        user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)
            elif kt == 'UpdateDeviceData': # 编辑设备信息
                id = self.get_argument('id', None)  # 设备信息id
                name = self.get_argument('name', None)  # "储能单元"
                area = self.get_argument('area', None)  # "分区"
                sn = self.get_argument('sn', None)  # "设备编号"
                type_id = self.get_argument('type_id', None)
                factory_id = self.get_argument('factory_id', None)  # "厂家"
                device_no_id = self.get_argument('device_no_id', None)  # "设备型号"
                project_id = self.get_argument('project_id', None)  # "项目ID"
                init_cap = self.get_argument('init_cap', None)  # "设备额定容量单位为kWh"
                if type_id:
                    try:
                        type_id = int(type_id)
                    except ValueError:
                        return self.customError('参数类型名称类型错误')
                if factory_id:
                    try:
                        factory_id = int(factory_id)  # "厂家"
                    except ValueError:
                        return self.customError('参数厂家类型错误')
                if device_no_id:
                    try:
                        device_no_id = int(device_no_id)  # "设备型号"
                    except ValueError:
                        return self.customError('参数设备型号类型错误')
                if project_id:
                    try:
                        project_id = int(project_id)  # "项目ID"
                    except ValueError:
                        return self.customError('参数项目id类型错误')
                if init_cap:
                    try:
                        init_cap = float(init_cap)  # "设备额定容量单位为kWh"
                    except ValueError:
                        return self.customError('参数设备额定容量类型错误')
                else:
                    init_cap = None

                init_no = self.get_argument('init_no', None)  # "设备出场编号"
                remark = self.get_argument('remark', None)  # "备注"

                if not name or not area or not type_id or not project_id or not id:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                remark = ';'.join(remark.split()) if remark else ''

                pro_dev_data = user_session.query(ProjectDevice).filter(ProjectDevice.id == id).first()
                if not pro_dev_data:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")

                if name:
                    pro_dev_data.name = name
                if area:
                    pro_dev_data.area = area
                if sn:
                    pro_dev_data.sn = sn
                if type_id:
                    pro_dev_data.type_id = type_id
                if factory_id:
                    pro_dev_data.factory_id = factory_id
                if device_no_id:
                    pro_dev_data.device_no_id = device_no_id
                if init_cap:
                    pro_dev_data.init_cap = init_cap
                if init_no:
                    pro_dev_data.init_no = init_no
                if remark:
                    pro_dev_data.remark = remark

                user_session.commit()

                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'DeleteDeviceInfos':  # 删除设备信息（批量/单个）
                ids = self.get_argument('ids',None)
                if DEBUG:
                    logging.info('ids:%s'%(ids))
                if not ids:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                ids_to_update = [int(num) for num in ids.split("#")]
                filter = [ProjectDevice.is_use == 1, ProjectDevice.id.in_(ids_to_update)]
                user_session.query(ProjectDevice).filter(*filter).update({'is_use': 0}, synchronize_session=False)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'ManageDeviceTypes':  # 添加/删除设备类型
                data = json.loads(self.get_argument('data',[{}]))  # 所有数据集合
                if DEBUG:
                    logging.info('data:%s'%(data))
                if not isinstance(data, list):
                    self.customError('参数不合规')

                lang = self.get_argument('lang', 'zh')
                sers = []
                del_ids = []
                for item in data:
                    if item['type'] == "add": # 新增
                        if not len(item['data']):
                            continue
                        for add_item in item['data']:
                            keys = add_item.keys()
                            print(keys)
                            if "name" not in keys:
                                return self.customError('参数不完整') if lang == 'zh' else self.customError(
                                    "Incomplete parameters")
                            device_type_info = user_session.query(DeviceType).filter(DeviceType.name == add_item['name'],
                                                                            DeviceType.is_use == 1).first()
                            if device_type_info:
                                continue
                            sers.append(DeviceType(name=add_item['name'], op_ts=timeUtils.getNewTimeStr()))
                    elif item['type'] == "delete": # 删除
                        if not len(item['data']):
                            continue
                        for del_item in item['data']:
                            keys = del_item.keys()
                            if "id" not in keys:
                                return self.customError('参数不完整') if lang == 'zh' else self.customError(
                                    "Incomplete parameters")
                            if not re.compile(r'^-?\d+$').match(del_item['id']):
                                return self.customError('参数不合规')

                            # 先查询设备信息是否绑定了
                            project_device_info = user_session.query(ProjectDevice).filter(
                                ProjectDevice.type_id == int(del_item['id']),
                                ProjectDevice.is_use == 1).first()
                            if project_device_info:
                                return self.customError('该设备类型关联了设备，请解除关联后再删除！')
                            device_info = user_session.query(DeviceLibrary).filter(
                                DeviceLibrary.device_type_id == int(del_item['id']),
                                DeviceLibrary.is_use == 1).first()
                            if device_info:
                                return self.customError('该设备类型关联了设备库，请解除关联后再删除！')
                            # 看被删除的是否在不可删列表
                            no_del = ["电芯", "PCS"]
                            device_type_info = user_session.query(DeviceType).filter(DeviceType.id == int(del_item['id']),
                                                                                     DeviceType.is_use == 1).first()
                            if not device_type_info:
                                return self.customError('该设备类型不存在！')
                            if device_type_info.name in no_del:
                                return self.customError('该设备类型不可以删除！')
                            del_ids.append(int(del_item['id']))
                    else:
                        continue
                if len(sers):
                    user_session.add_all(sers)
                if len(del_ids):
                    filter = [DeviceType.is_use == 1, DeviceType.id.in_(del_ids)]
                    user_session.query(DeviceType).filter(*filter).update({'is_use': 0},
                                                                          synchronize_session=False)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'DeleteDeviceType':  # 单个删除设备类型
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                if not id:
                    if lang == 'en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError('参数不完整')
                if not re.compile(r'^-?\d+$').match(id):
                    return self.customError('参数不合规')
                # 先查询设备信息是否绑定了
                project_device_info = user_session.query(ProjectDevice).filter(ProjectDevice.type_id == id,
                                                                         ProjectDevice.is_use == 1).first()
                if project_device_info:
                    return self.customError('该设备类型关联了设备，请解除关联后再删除！')
                # 看被删除的是否在不可删列表
                no_del = ["电芯", "PCS"]
                device_type_info = user_session.query(DeviceType).filter(DeviceType.id == id,
                                                                         DeviceType.is_use == 1).first()
                if not device_type_info:
                    return self.customError('该设备类型不存在！')
                if device_type_info.name in no_del:
                    return self.customError('该设备类型不可以删除！')

                filter = [DeviceType.is_use == 1, DeviceType.id == int(id)]
                user_session.query(DeviceType).filter(*filter).update({'is_use': 0}, synchronize_session=False)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'ManageDeviceUnit':  # 添加/删除/编辑设备部件
                data = json.loads(self.get_argument('data', [{}]))  # 所有数据集合
                if DEBUG:
                    logging.info('data:%s' % (data))
                if not isinstance(data, dict):
                   return  self.customError('参数不合规')
                lang = self.get_argument('lang', 'zh')
                sers = []
                del_ids = []
                keys = data.keys()
                if "device_id" not in keys or "data" not in keys:
                    return self.customError('参数不完整') if lang == 'zh' else self.customError(
                        "Incomplete parameters")
                device_id = data['device_id']
                for item in data["data"]:
                    d_keys = item.keys()
                    if "type" not in d_keys :
                        return self.customError('参数不完整') if lang == 'zh' else self.customError(
                            "Incomplete parameters")

                    if item['type'] == "add":  # 新增
                        if not len(item['data']):
                            continue
                        for add_item in item['data']:
                            keys = add_item.keys()
                            if "name" not in keys:
                                return self.customError('参数不完整') if lang == 'zh' else self.customError(
                                    "Incomplete parameters")
                            name = add_item['name']
                            sn = add_item['sn'] if "sn" in keys else None
                            remark = add_item['remark'] if "remark" in keys else None
                            filter = [DeviceUnit.is_use == 1, DeviceUnit.name == name, DeviceUnit.device_id == device_id]
                            if sn:
                                filter.append(DeviceUnit.sn == sn)
                            if remark:
                                filter.append(DeviceUnit.remark == remark)
                            device_type_info = user_session.query(DeviceUnit).filter(*filter).first()
                            if device_type_info:
                                continue
                            sers.append(DeviceUnit(name=add_item['name'],device_id=device_id, sn=sn, remark=remark, op_ts=timeUtils.getNewTimeStr()))
                    elif item['type'] == "delete":  # 删除
                        if not len(item['data']):
                            continue
                        for del_item in item['data']:
                            keys = del_item.keys()
                            if "id" not in keys:
                                return self.customError('参数不完整') if lang == 'zh' else self.customError(
                                    "Incomplete parameters")
                            if not re.compile(r'^-?\d+$').match(del_item['id']):
                                return self.customError('参数不合规')
                            del_ids.append(int(del_item['id']))
                    elif item['type'] == "edit":  # 编辑
                        if not len(item['data']):
                            continue
                        for edit_item in item['data']:
                            update_dic = {}
                            edit_keys = edit_item.keys()
                            if "id" not in edit_keys:
                                return self.customError('参数不完整') if lang == 'zh' else self.customError(
                                    "Incomplete parameters")
                            id = int(edit_item['id'])
                            if "name" in edit_keys:
                                name = edit_item['name']
                                if name:
                                    update_dic['name'] = name
                            if "sn" in  edit_keys:
                                update_dic['sn'] = edit_item['sn']
                            if "remark" in  edit_keys:
                                update_dic['remark'] = edit_item['remark']
                            filter = [DeviceUnit.is_use == 1, DeviceUnit.id == id]
                            user_session.query(DeviceUnit).filter(*filter).update({k: update_dic[k] for k in DeviceUnit.__table__.columns.keys() if k in update_dic},
                                                                              synchronize_session=False)
                    else:
                        continue
                if len(sers):
                    user_session.add_all(sers)
                if len(del_ids):
                    filter = [DeviceUnit.is_use == 1, DeviceUnit.id.in_(del_ids)]
                    user_session.query(DeviceUnit).filter(*filter).update({'is_use': 0},
                                                                          synchronize_session=False)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

        # user_session.rollback()
        # user_session.close()

    def UpdateMea(self, mea_point_n, name, num, page, remarks, station, t_eq_id, type_,en_name,en_mea_point_n,en_remarks):
        #修改测点表
        if name:
            page.name = name
            page.en_name = en_name
        if num:
            page.num = num
        if t_eq_id:
            page.t_eq_id = t_eq_id
        if mea_point_n:
            page.mea_point_n = mea_point_n
            page.en_mea_point_n = en_mea_point_n
        # if mea_point_n:
        #     page.mea_point_n = mea_point_n
        if type_:
            page.type_ = type_
        if remarks:
            page.remarks = remarks
            page.en_remarks = en_remarks
        if station:
            page.station = station
        user_session.commit()

    def can_convert_to_float(self, s):
        try:
            if s == '' or s is None:
                return False
            float(s)  # 尝试将字符串转换为浮点数
            return True  # 如果成功，返回True
        except ValueError:
            return False  # 如果失败（即抛出ValueError），返回False


class ProvinceCity(BaseHandler):
    #获取省市数据
    @classmethod
    def ProvinceList(cls,lang=None):# 所有省份
        data = []
        pages = user_session.query(Province.province, Province.id,Province.en_province).order_by(Province.id.desc()).all()
        for pag in pages:
            obj = {}
            if lang=='en':
                obj['name'] = pag[2]
            else:
                obj['name'] = pag[0]
            obj['id'] = pag[1]
            data.append(obj)
        return data

    @classmethod
    def CityList(cls,id,lang=None):# 所有省份下的市
        data = []
        pages = user_session.query(City.city,City.id,City.en_city).filter(City.pro_id == id).order_by(City.id.desc()).all()
        for pag in pages:
            obj = {}
            if lang=='en':
                obj['name'] = pag[2]
            else:
                obj['name'] = pag[0]
            obj['id'] = pag[1]
            data.append(obj)
        return data