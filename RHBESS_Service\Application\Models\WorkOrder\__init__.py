#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-10-24 16:04:53
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\__init__.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-20 17:32:09



def create_all():
    u'初始化所有表'
    from Application.Models.WorkOrder.dispatch_type import DispatchType
    from Application.Models.WorkOrder.dispatch_model import DispatchModel
    from Application.Models.WorkOrder.dispatch_step_t import DispatchStep
    from Application.Models.WorkOrder.dispatch_r import DispatchR
    from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
    from Application.Models.WorkOrder.dispatch_plan import DispatchPlan
    from Application.Models.WorkOrder.spare_info import SpareInfo
    from Application.Models.WorkOrder.spare_in import SpareIn
    from Application.Models.WorkOrder.spare_out import SpareOut
    from Application.Models.WorkOrder.spare_disk import SpareDisk
    from Application.Models.WorkOrder.sop_base_info import SopBaseInfo
    from Application.Models.WorkOrder.sop_label_info import SopLabelInfo
    from Application.Models.WorkOrder.sop_file_info import SopFileInfo

    

    DispatchType.init()
    DispatchModel.init()
    DispatchStep.init()
    DispatchR.init()
    DispatchStepR.init()
    DispatchPlan.init()
    SpareInfo.init()
    SpareIn.init()
    SpareOut.init()
    SpareDisk.init()
    SopBaseInfo.init()
    SopLabelInfo.init()
    SopFileInfo.init()


