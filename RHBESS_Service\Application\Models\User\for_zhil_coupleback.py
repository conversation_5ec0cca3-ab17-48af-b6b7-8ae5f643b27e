#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-10-19 10:35:14
#@FilePath     : \RHBESS_Service\Application\Models\User\for_zhil_coupleback.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-07-26 17:13:37



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text


class ForCouplebackR(user_Base):
    u'智锂预测结果记录表'
    __tablename__ = "r_for_zhil_coupleback"
    # id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    pcs = Column(VARCHAR(50), nullable=False, comment=u"PCS")
    fault_no = Column(VARCHAR(256), nullable=False, primary_key=True,comment=u"故障编号，返回时使用")
    fault_info = Column(VARCHAR(256), nullable=False, comment=u"预警原因")
    fault_duration = Column(Integer, nullable=False, comment=u"预警时长/秒")
    fault_location = Column(VARCHAR(256), nullable=False, comment=u"预警位置")
    advice = Column(Text, nullable=False, comment=u"处理建议")
    start_time = Column(DateTime, nullable=False,comment=u"预警开始时间")
    end_time = Column(VARCHAR(20), nullable=True,comment=u"预警结束时间，没结束时间写0000-00-00 00:00:00")
    status = Column(Integer, nullable=True,comment=u"状态(0,预警中，1已恢复)")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
    alarm_type = Column(VARCHAR(5), nullable=True,comment=u"告警等级1,2,3")


    ignore = Column(Integer, nullable=True,comment=u"是否抑制(1：否， 2：是)")
    send = Column(Integer, nullable=True,comment=u"是否发送通知(1：否， 2：是)")
    handle_f = Column(Integer, nullable=True,comment=u"是否处理(1：否， 2：是)")
    feed_back = Column(Integer, nullable=True,comment=u"是否反馈(1：否， 2：是)")
    back_info= Column(Text, nullable=True,comment=u"人工反馈意见")
    hand_info= Column(Text, nullable=True,comment=u"人工干预处理意见")
    read = Column(Integer, nullable=True,comment=u"人工标识（1:已处理，2：误报）")
    # ignore_handle= Column(Integer, nullable=True,comment=u"抑制标识(1:否，2:是),人工处理时标识和抑制必须二选一，有标识后抑制可选可不选")
    user_descr = Column(VARCHAR(50), nullable=True,comment=u"处理人名称")
    hand_ts = Column(DateTime, nullable=False,comment=u"处理时间")
    fault_code = Column(VARCHAR(50), nullable=True,comment=u"故障编码")
    fault_title = Column(VARCHAR(250), nullable=True,comment=u"故障名称")

   
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'id':'%s','pcs':'%s','fault_no':'%s','fault_duration':'%s','advice':'%s','start_time':'%s','end_time':'%s','status':'%s','ignore':'%s',\
        'op_ts':'%s','station':'%s','send':%s,'handle_f':'%s','feed_back':'%s','alarm_type':'%s','back_info':'%s','hand_info':'%s','read':'%s','hand_ts':'%s','fault_code':'%s','fault_title':'%s'}" % (self.fault_no,self.pcs,
        self.fault_no,self.fault_duration,self.advice,self.start_time,self.end_time,self.status,
        self.ignore,self.op_ts,self.station,self.send,self.handle_f,self.feed_back,self.alarm_type,self.back_info,self.hand_info,self.read,self.hand_ts,self.fault_code,self.fault_title)
        return bean.replace("None",'')
        
