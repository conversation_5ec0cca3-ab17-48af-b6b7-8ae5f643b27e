#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-01 15:45:57
#@FilePath     : \RHBESS_Service\Application\Models\User\custom_report_t.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-02 12:02:50
import json
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from sqlalchemy.databases import mysql


class ReportOperation(user_Base):
    u'运行报表输入记录'
    __tablename__ = "t_report_operation"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    month = Column(VARCHAR(50), nullable=False,comment=u"月份")
    chag = Column(VARCHAR(250), nullable=True,comment=u"充电量")
    disg = Column(VARCHAR(250), nullable=True,comment=u"放电量")
    ratio = Column(VARCHAR(50), nullable=True,comment=u"充放电量完成率")
    ratio_tal = Column(VARCHAR(50), nullable=True,comment=u"累计充放电效率")
    sys_availty = Column(VARCHAR(50), nullable=True,comment=u"系统可用率")
    day_utilize_hour = Column(VARCHAR(50), nullable=True,comment=u"日均利用小时数")
    chag_cons = Column(VARCHAR(50), nullable=True,comment=u"充电一致性")
    disg_cons = Column(VARCHAR(50), nullable=True,comment=u"放电一致性")
    ratio_cons = Column(VARCHAR(50), nullable=True,comment=u"完成率一致性")
    station = Column(VARCHAR(50), nullable=False,comment=u"电站英文名称")
    pcs = Column(mysql.MEDIUMTEXT, nullable=True, comment=u"pcs内容详情")
    op_ts = Column(DateTime, nullable=False, comment=u"录入时间")
    is_use = Column(CHAR(2), nullable=False, comment=u"是否使用1是0否")

    en_pcs = Column(mysql.MEDIUMTEXT, nullable=False, comment=u"pcs内容详情")
       
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'month':'%s','chag':'%s','disg':'%s','ratio':'%s','ratio_tal':'%s','sys_availty':'%s','day_utilize_hour':'%s','chag_cons':'%s','disg_cons':'%s','ratio_cons':'%s'," \
               "'station':%s,'pcs':'%s','op_ts':'%s','is_use':'%s','is_use':'%s'}" % (
            self.id,self.month,self.chag,self.disg,self.ratio,self.ratio_tal,self.sys_availty,self.day_utilize_hour,self.chag_cons,self.disg_cons,self.ratio_cons,self.station,self.pcs,self.op_ts,self.is_use,self.is_use)
        return bean.replace("None",'')
        

