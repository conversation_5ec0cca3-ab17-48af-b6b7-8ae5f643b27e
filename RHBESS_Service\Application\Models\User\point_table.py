from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Column, SmallInteger, VARCHAR


class PointTable(user_Base):
    u'点表'
    __tablename__ = "t_point_table"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    name = Column(VARCHAR(128), nullable=False, comment=u"点名称")
    en_name = Column(VARCHAR(128), nullable=False, comment=u"点名称-英文")
    name_la = Column(VARCHAR(128), nullable=False, comment=u"点名")
    device_name = Column(VARCHAR(128), nullable=False, comment=u"设备类型")
    station = Column(VARCHAR(32), nullable=False, comment=u"电站")
    data_type = Column(SmallInteger(), nullable=False, comment=u"数据类型：1：测量量、2：状态量")
    station_type = Column(SmallInteger(), nullable=False, comment=u"数据类型：1：独立储能、2：用户侧、3：火储")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
