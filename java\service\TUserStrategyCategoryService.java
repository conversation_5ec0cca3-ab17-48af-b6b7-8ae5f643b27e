package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryCreateDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryQueryDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryUpdateDTO;
import com.robestec.analysis.entity.TUserStrategyCategory;
import com.robestec.analysis.vo.TUserStrategyCategoryVO;

import java.util.List;

/**
 * 用户策略分类服务接口
 */
public interface TUserStrategyCategoryService extends ISuperService<TUserStrategyCategory> {

    /**
     * 分页查询用户策略分类
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TUserStrategyCategoryVO> queryTUserStrategyCategory(TUserStrategyCategoryQueryDTO queryDTO);

    /**
     * 创建用户策略分类
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTUserStrategyCategory(TUserStrategyCategoryCreateDTO createDTO);

    /**
     * 更新用户策略分类
     * @param updateDTO 更新参数
     */
    void updateTUserStrategyCategory(TUserStrategyCategoryUpdateDTO updateDTO);

    /**
     * 删除用户策略分类
     * @param id 记录ID
     */
    void deleteTUserStrategyCategory(Long id);

    /**
     * 获取用户策略分类详情
     * @param id 记录ID
     * @return 记录详情
     */
    TUserStrategyCategoryVO getTUserStrategyCategory(Long id);

    /**
     * 批量创建用户策略分类
     * @param createDTOList 创建参数列表
     */
    void createTUserStrategyCategoryList(List<TUserStrategyCategoryCreateDTO> createDTOList);

    /**
     * 根据策略ID查询用户策略分类
     * @param strategyId 策略ID
     * @return 记录列表
     */
    List<TUserStrategyCategoryVO> getTUserStrategyCategoryByStrategyId(Long strategyId);

    /**
     * 根据分类名称查询用户策略分类
     * @param name 分类名称
     * @return 记录列表
     */
    List<TUserStrategyCategoryVO> getTUserStrategyCategoryByName(String name);

    /**
     * 统计策略ID的分类数量
     * @param strategyId 策略ID
     * @return 记录数量
     */
    Long countByStrategyId(Long strategyId);
}
