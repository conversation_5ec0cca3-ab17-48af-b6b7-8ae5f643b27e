#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \HY_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\user_message.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-28 13:57:32


from Application.Models.User.user import User
from Application.Models.User.message import Message
from Tools.DB.mysql_user import scada_Base
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class UserMessage(scada_Base):
    u'用户消息表'
    __tablename__ = "t_user_message"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_id = Column(Integer, ForeignKey("t_user.id"), nullable=False, comment=u"用户id")
    message_id = Column(Integer, ForeignKey("t_message.id"), nullable=False, comment=u"消息id")
    read = Column(Boolean, nullable=False, comment=u"阅读状态", default=False)
    read_ts = Column(DateTime, nullable=True, comment=u"阅读时间")

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()