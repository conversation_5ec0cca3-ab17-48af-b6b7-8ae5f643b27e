from django.urls import path
from apis.user import views

urlpatterns = [
    # path("register/", views.RegisterView.as_view()),  # 注册,
    # path("send_sms_code/", views.SendSMSCode.as_view()),  # 发送短信
    # path("sms_code_login/", views.SMSCodeLoginView.as_view()),  # 短信验证码登录
    # path("sms_password_login/", views.SMSPasswordLoginView.as_view()),  # 短信密码登录
    # path("username_password_login/", views.UsernamePasswordLoginView.as_view()),  # 用户名密码登录
    # path("mobile_change_password/", views.ChangePasswordByMobilePasswordView.as_view()),  # 短信修改密码
    # path("user_info/", views.UserInfoView.as_view()),  # 用户详情
    # path("retrieve_password/", views.RetrievePasswordView.as_view()),  # 短信找回密码
    # path("do_income/", views.TTView.as_view()),  # 手动计算收益                   # TODO 暂未适配

    # path("customization/", views.CustomizationView.as_view()),  # 削峰填谷客制化配置
    # path("customization/detail/", views.CustomizationDetailView.as_view()),  # 当月时刻电价查询
    # path("customization/add/", views.CustomizationAddView.as_view()),  # 削峰填谷客制化添加
    # path("customization/get/", views.CustomizationGetView.as_view()),  # 削峰填谷客制获取夏季起始月份
    # path("customization/history/", views.CustomizationHistoryView.as_view()),  # 削峰填谷客制获取历史记录
    # path("customization/delete/", views.CustomizationDeleteView.as_view()),  # 削峰填谷客制删除
    # path("excel/download/", views.ExcelDownloadView.as_view()),  # 每日统计数据excel 下载                 1

    path("feedback/", views.FeedbackView.as_view()),  # 用户反馈
    # path("do_income_v2/", views.NewTTView.as_view()),  # 手动计算收益v2
    # path("do_income_v3/", views.NewTTViewV2.as_view()),  # 手动计算收益v3
    # path("do_income_v4/", views.NewTTViewV3.as_view()),  # 手动计算收益v4
    path("do_income_v5/", views.NewTTViewV4.as_view()),  # 手动计算收益v4

    path("InitTModelUnitRelation/", views.InitTModelUnitRelationView.as_view()),  # 初始化电池温度分析--模型和设备关联表
]
