package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电站容量VO
 */
@Data
@ApiModel("电站容量VO")
public class StationCapacityVO {

    @ApiModelProperty("电站ID")
    private Long id;

    @ApiModelProperty("电站名称")
    private String name;

    @ApiModelProperty("电站描述")
    private String descr;

    @ApiModelProperty("电功率")
    private Double electricPower;

    @ApiModelProperty("实际功率")
    private Double realPower;

    @ApiModelProperty("运行状态")
    private String runningState;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("储能")
    private String energyStorage;
}
