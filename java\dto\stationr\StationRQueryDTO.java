package com.robestec.analysis.dto.stationr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电站关系查询DTO
 */
@Data
@ApiModel("电站关系查询DTO")
public class StationRQueryDTO {

    @ApiModelProperty("电站名称")
    private String stationName;

    @ApiModelProperty("运行状态")
    private String runningState;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("储能")
    private String energyStorage;

    @ApiModelProperty("电池簇")
    private String batteryCluster;

    @ApiModelProperty("监控")
    private String monitor;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
