package com.robestec.analysis.exception;

import com.robestec.analysis.dto.TelecontrolResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理远程控制策略相关的异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理远程控制策略异常
     */
    @ExceptionHandler(TelecontrolException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public TelecontrolResponse.CommonResult<String> handleTelecontrolException(TelecontrolException e) {
        log.error("远程控制策略异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理文件处理异常
     */
    @ExceptionHandler(TelecontrolException.FileProcessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleFileProcessException(TelecontrolException.FileProcessException e) {
        log.error("文件处理异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理数据验证异常
     */
    @ExceptionHandler(TelecontrolException.ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleValidationException(TelecontrolException.ValidationException e) {
        log.error("数据验证异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理业务逻辑异常
     */
    @ExceptionHandler(TelecontrolException.BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleBusinessException(TelecontrolException.BusinessException e) {
        log.error("业务逻辑异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理数据库操作异常
     */
    @ExceptionHandler(TelecontrolException.DatabaseException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public TelecontrolResponse.CommonResult<String> handleDatabaseException(TelecontrolException.DatabaseException e) {
        log.error("数据库操作异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(TelecontrolException.PermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public TelecontrolResponse.CommonResult<String> handlePermissionException(TelecontrolException.PermissionException e) {
        log.error("权限异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数验证异常: {}", message);
        return TelecontrolResponse.CommonResult.error(400, "参数验证失败: " + message);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleBindException(BindException e) {
        String message = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("绑定异常: {}", message);
        return TelecontrolResponse.CommonResult.error(400, "参数绑定失败: " + message);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.error("约束违反异常: {}", message);
        return TelecontrolResponse.CommonResult.error(400, "约束验证失败: " + message);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public TelecontrolResponse.CommonResult<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件上传大小超限: {}", e.getMessage());
        return TelecontrolResponse.CommonResult.error(400, "文件大小超过限制");
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public TelecontrolResponse.CommonResult<String> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(500, "系统内部错误: " + e.getMessage());
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public TelecontrolResponse.CommonResult<String> handleException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        return TelecontrolResponse.CommonResult.error(500, "系统异常，请联系管理员");
    }
}
