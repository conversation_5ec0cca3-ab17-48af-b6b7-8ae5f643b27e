# -*- coding:utf-8 -*-
import math
import time
import datetime 
import calendar
from dateutil.relativedelta import relativedelta

class timeUtils(object):
    # 绝对秒相关
    SecsMin = 60
    SecsHour = 3600
    SecsDay = 3600 * 24

    u'时间工具类'
    @classmethod
    def getNewDayStartStr(cls):
        u'获取当前天的起始时间'
        return time.strftime("%Y-%m-%d 00:00:00", time.localtime())
    @classmethod
    def getNewTimeStr(cls):
        u'获取当前时间'
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) 
    @classmethod
    def timeStrToTamp(cls,data):
        u'时间转时间戳'
        timeArray = time.strptime(data, "%Y-%m-%d %H:%M:%S")
        timeStamp = int(time.mktime(timeArray))
        return timeStamp

    @classmethod
    def tampToTimeStr(cls, timestamp):
        u'时间戳转时间'
        dt_object = datetime.datetime.fromtimestamp(timestamp)
        time_string = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        return time_string
    @classmethod
    def nowSecs(cls):
        u'获取当前绝对秒'
        return int(time.time())
    @classmethod
    def todaySecs(cls):
        u'获取当天起始绝对秒'
        tstr = cls.getNewDayStartStr()
        return cls.timeStrToTamp(tstr)
    
    @classmethod
    def ssTtimes(cls,ss):
        b=time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(int(ss)));
        return b;

    @classmethod
    def betweenDayNum(cls,start,end):
        start = start[:10].split('-')
        end = end[:10].split('-')
        d1 = datetime.datetime(int(start[0]),int(start[1]),int(start[2]))
        d2 = datetime.datetime(int(end[0]),int(end[1]),int(end[2]))
        return (d2-d1).days;

    @classmethod
    def getAgoTime(cls,days=7):
        u'获取N天前时间,默认一周'
        return (datetime.date.today() - datetime.timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    def getAgoTimeS(cls, startdate,days=7):
        u'获取N天前时间,默认一周'
        return (datetime.datetime.strptime(startdate[0:10],'%Y-%m-%d') - datetime.timedelta(days=days)).strftime("%Y-%m-%d")

    @classmethod
    def getAgoMonthD(cls, startdate,months=6):
        u'获取传入时间N月前年月,默认6个月'
        return (datetime.datetime.strptime(startdate[0:7],'%Y-%m') - relativedelta(months=months)).strftime("%Y-%m")

    @classmethod
    def getAgoMonth(cls, months=6):
        u'获取N月前年月,默认6个月'
        return (datetime.date.today() - relativedelta(months=months)).strftime("%Y-%m")

    @classmethod
    def getPreviousMonthDay(cls, startdate):
        u'获取指定日期前一个月第一天和最后一天'
        this_month_start = datetime.datetime.strptime(startdate[0:8]+'01','%Y-%m-%d')
        last_month_end = this_month_start - datetime.timedelta(days=1)
        last_month_start = datetime.datetime(last_month_end.year, last_month_end.month, 1)

        last_month_start_str = last_month_start.strftime('%Y-%m-%d')
        last_month_end_str = last_month_end.strftime('%Y-%m-%d')

        return last_month_start_str,last_month_end_str



    @classmethod
    def dateToDataList(cls,start,end):
        # 计算时间段内的时间列表,包含首位
        datestart=datetime.datetime.strptime(start[0:10],'%Y-%m-%d')
        dateend=datetime.datetime.strptime(end[0:10],'%Y-%m-%d')
        data_list = list()
        while datestart<=dateend:
            data_list.append(datestart.strftime('%Y-%m-%d')) 
            datestart+=datetime.timedelta(days=1)
        return data_list

    @classmethod
    def getBetweenMonth(cls,begin_date,end_date):
        # 返回一段时间内的所有年月
        date_list = []
        begin_date = datetime.datetime.strptime(begin_date[0:7], "%Y-%m")
        end_date = datetime.datetime.strptime(end_date[0:7], "%Y-%m")
        while begin_date <= end_date:
            date_list.append(begin_date.strftime("%Y%m"))
            begin_date = cls.add_months(begin_date,1)
        return date_list
    @classmethod
    def getBetweenTi(cls,begin_date,end_date):
        # 返回一段时间内的所有时间
        date_list = []
        begin_date = datetime.datetime.strptime(begin_date, "%Y-%m-%d %H:%M:%S")
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
        while begin_date <= end_date:
            date_list.append(begin_date.strftime("%Y-%m-%d %H:%M:%S"))
            begin_date = cls.add_months(begin_date,1)
        return date_list

    @classmethod
    def getBetweenMonthYm(cls, begin_date, end_date):
        # 返回一段时间内的所有年月
        date_list = []
        begin_date = datetime.datetime.strptime(begin_date[0:7], "%Y-%m")
        end_date = datetime.datetime.strptime(end_date[0:7], "%Y-%m")
        
        while begin_date <= end_date:
            date_list.append(begin_date.strftime("%Y-%m"))
            begin_date = cls.add_months(begin_date, 1)
        return date_list



    @classmethod
    def get_week_of_month(query_date):
        """
        获取某个日期是当月的第几周
        """
        query_date = datetime.strptime(query_date[:10]+" 00:00:00","%Y-%m-%d %H:%M:%S")
        end = int(query_date.strftime("%W"))
        begin = int(datetime(query_date.year, query_date.month, 1).strftime("%W"))
        return end - begin + 1




    @classmethod
    def last_day_of_month(cls,year,month):
        """
        获取某个月的最后一天
        """
        any_day = datetime.date(year, month, 1)
        next_month = any_day.replace(day=28) + datetime.timedelta(days=4)  # this will never fail
        return next_month - datetime.timedelta(days=next_month.day)

        
    
    @classmethod
    def add_months(cls,dt,months):
        month = dt.month - 1 + months
        year = int(dt.year + month / 12)
        month = month % 12 + 1
        day = min(dt.day, calendar.monthrange(year, month)[1])
        return dt.replace(year=year, month=month, day=day)

    @classmethod
    def getNowHouse(cls):
        # 获取当前小时
        return datetime.datetime.now().strftime("%Y%m%d%H")

    @classmethod
    def getBeforeHouse(cls):
        # 获取上一小时
        now_time=datetime.datetime.now()
        l= (now_time+datetime.timedelta(hours=-1)).strftime("%Y%m%d%H")
        return l
    @classmethod
    def getBeforeHouseStr(cls):
        # 获取上一小时 时间格式
        now_time=datetime.datetime.now()
        l= (now_time+datetime.timedelta(hours=-1)).strftime("%Y-%m-%d %H:%M:%S")
        return l
    @classmethod
    def getBeforeDay(cls):
        # 获取一天前时间
        now_time=datetime.datetime.now()
        l= (now_time+datetime.timedelta(days=-1)).strftime("%Y-%m-%d %H:%M:%S")
        return l

    @classmethod
    def timeDiff(cls,begin_date,end_date):
        # 计算两个时间相差的时分秒
        begin_date = datetime.datetime.strptime(begin_date[0:19], "%Y-%m-%d %H:%M:%S")
        end_date = datetime.datetime.strptime(end_date[0:19], "%Y-%m-%d %H:%M:%S")
        result = end_date - begin_date
        seconds = result.total_seconds()  # 获取秒数
        m,s = divmod(seconds,60)
        h,m = divmod(m,60)
        # 不采用此方法是因为超过24小时，格式就成了：1 day, 18:07:53
        # print datetime.timedelta(seconds=seconds)
       
        return "%02d:%02d:%02d" % (h, m, s)

    @classmethod
    def timeSeconds(cls,begin_date,end_date):
        # 计算两个时间相差的秒
        begin_date = datetime.datetime.strptime(begin_date[0:19], "%Y-%m-%d %H:%M:%S")
        end_date = datetime.datetime.strptime(end_date[0:19], "%Y-%m-%d %H:%M:%S")
        result = end_date - begin_date
        seconds = result.total_seconds()  # 获取秒数
              
        return int(seconds)
    
    @classmethod
    def getAllDaysByMonth(cls,str):
        # 根据传入的时间获取月份的每一天
        year = int(str[:4])
        month = int(str[5:7])
        last_day = calendar.monthrange(year,month)[1]## 最后一天
        start = datetime.date(year,month,1)
        end = datetime.date(year,month,last_day)
        start = start.strftime('%Y-%m-%d')
        end = end.strftime('%Y-%m-%d')
        days = cls.dateToDataList(start,end)
        return days

    @classmethod
    def HMSTSec(cls,st):
        # 将时分秒转换成秒
        var = ("hours","minutes","seconds")
        time2sec = lambda x:int(datetime.timedelta(**{k:int(v) for k,v in zip(var,x.strip().split(":"))}).total_seconds())
        a = time2sec(st)
        return int(a)
    @classmethod
    def returnRealTime(cls,endtime):
        u'返回时间，大于当前就返回当前时间，否则返回传入时间'
        nt = cls.getNewDayStartStr()[:10]
        if endtime <= nt:
            return endtime
        else:
            return  nt

    @classmethod
    def get_week_dates(cls,start_date, end_date):
        '''根据开始结束时间，算出每周的时间列表'''
        current_date = start_date
        week_dates = []

        while current_date <= end_date:
            # 获取当前日期是月份的第几周
            week_number = current_date.strftime('%W')
            week_number = int(week_number) if week_number.isdigit() else 0

            # 添加每周的开始时间和结束时间
            week_dates.append({
                'start': current_date.strftime('%Y-%m-%d'),
                'end': (current_date + datetime.timedelta(days=6)).strftime('%Y-%m-%d'),
                'week': week_number,
                'month': current_date.strftime('%m'),
                'year': current_date.strftime('%Y')
            })
            current_date += datetime.timedelta(days=7)


        return week_dates

    @classmethod
    def get_ysetday_se(cls):
        # 获取当前日期和时间
        now = datetime.datetime.now()

        # 计算昨天的日期
        yesterday = now - datetime.timedelta(days=1)

        # 设置昨天的起始时间为 00:00:00
        yesterday_start = datetime.datetime(yesterday.year, yesterday.month, yesterday.day)

        # 设置昨天的结束时间为 23:59:59
        yesterday_end = datetime.datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59)

        # 将时间转换为秒数
        yesterday_start_seconds = int(yesterday_start.timestamp())
        yesterday_end_seconds = int(yesterday_end.timestamp())
        return yesterday_start_seconds, yesterday_end_seconds


    @classmethod
    def get_week_number(cls,date_str):
        '''获取时间是时间月份里的第几周'''
        # 将输入的日期字符串转换为datetime对象
        date = datetime.datetime.strptime(date_str, "%Y-%m-%d")

        # 获取该日期是月份的第一天还是第二天等等
        day_of_month = date.day
        first_day_of_month = datetime.datetime(date.year, date.month, 1)

        # 计算从月份的第一天到当前日期的天数，然后除以7，向下取整得到周数
        week_number = math.ceil((day_of_month - first_day_of_month.day + 1) /7)

        return week_number

        # if __name__ == '__main__':
#     tu = timeUtils()
#     yy = tu.returnRealTime('2022-06-18')
#     print ('YYYYYYYY',yy)

   