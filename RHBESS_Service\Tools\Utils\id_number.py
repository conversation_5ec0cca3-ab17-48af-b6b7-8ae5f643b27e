#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-25 17:15:00
#@FilePath     : \RHBESS_Service\Tools\Utils\id_number.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:36:38

import commands
import logging
import uuid
import time
import psutil
from Tools.Utils.num_utils import num_retain
from Tools.Utils.time_utils import timeUtils 

# 获取mac地址
node = uuid.getnode()
mac = uuid.UUID(int = node).hex[-12:]


def disk_percent():
    '''统计磁盘使用率'''
    import re
    import subprocess
    # 磁盘信息
    disk = subprocess.Popen('df -h',shell=True,stdout=subprocess.PIPE)
    info = disk.stdout.read()
    list1 = re.findall("(\d{1,10}\%)",info)
    list1 = eval(str(list1).replace('%',''))  # 去掉%号
    list2 = re.findall("(/{0,1}\w{0,10}/\w{0,10})\n",info)
    list3 = zip(list2,list1)
    return str(list3).replace("'",'"')

# CPU逻辑个数
psutil.cpu_count()
# CPU物理个数
psutil.cpu_count(logical=False)
# cpu使用率
psutil.cpu_percent(1)
# cpu型号
commands.getoutput('cat /proc/cpuinfo | grep name | cut -f2 -d: | uniq -c')
# 物理内存
num_retain(psutil.virtual_memory().total / (1024.0 * 1024.0 * 1024.0))
# 剩余物理内存
num_retain(psutil.virtual_memory().free / (1024.0 * 1024.0 * 1024.0))
# 物理内存使用率
num_retain((psutil.virtual_memory().total - psutil.virtual_memory().free) / float(psutil.virtual_memory().total)*100)
# 开机时间
timeUtils.ssTtimes(psutil.boot_time())

# 连接用户列表
','.join([u.name for u in psutil.users()])



'''
psutil.disk_usage("/").total      根目录磁盘总量

psutil.disk_usage("/").free      根目录磁盘剩余量

psutil.disk_usage("/").percent      根目录磁盘使用率

'''

def disk_percent2():
    '''统计磁盘各个位置'''
    import re
    import subprocess
    # 磁盘信息
    all = []
    disk = subprocess.Popen('df -h',shell=True,stdout=subprocess.PIPE)
    info = disk.stdout.read()
    for i in info.split('\n'):
        ii = i.split(' ')[-1]
        if ii and '/' in ii:
            all.append(ii)
    # list2 = re.findall("(/{0,1}\w{0,10}/\w{0,10})\n",info)
    list2 = re.findall("(/{0,1}\w{0,10}/\w{0,10})\n",info)
    print ('all:',all)
    return list2

# print disk_percent2()

