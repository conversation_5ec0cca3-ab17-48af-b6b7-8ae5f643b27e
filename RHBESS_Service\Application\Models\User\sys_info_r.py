#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-06-08 14:55:25
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\sys_info_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-06-08 15:58:42

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class SysInfosR(user_Base):
    u'监控记录记录表'
    __tablename__ = "r_sys_info"
    name = Column(String(128), nullable=False,primary_key=True,comment=u"名称")
    value = Column(String(200), nullable=False, comment=u"数值")
    op_ts = Column(DateTime, nullable=False,primary_key=True,comment=u"时间")
   
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'name':'%s','value':'%s','op_ts':'%s'}" % (self.name,self.value,self.op_ts)
        
