import ast
import django.core.paginator
from django.core.paginator import Paginator
from functools import wraps
from rest_framework.response import Response
from apis.user import models
from django_redis import get_redis_connection

def paging(p, s, data):
    """
    分页器
    :param p: 页数
    :param s: 条数
    :param data: 数据集
    :return:
    """
    mes = {}
    page = Paginator(data, s)
    try:
        data = page.page(p)
    except django.core.paginator.EmptyPage as e:
        data = []
    totalpage = page.num_pages
    total = page.count
    mes['data'] = data  # 数据
    mes['total'] = total  # 总条数
    mes['totalpage'] = totalpage  # 总页数
    mes['page'] = p
    return mes


def permission_required(app_url):
    """
    登录用户权限校验
    :param app_url:
    :return:
    """

    def decorated_function(f):
        @wraps(f)
        def __decorated_function(*args, **kwargs):
            if not app_url:
                return Response(
                    {
                        "code": -4,
                        "data": {
                            "message": "success",
                            "detail": '当前用户没有该APP权限',
                        },
                    }
                )
            user_id = args[1].user.get('user_id')
            roles_res = models.Role.objects.filter(userdetails__id=user_id, is_used=1,
                                                   permissions__url=app_url).first()  # 获取角色权限id
            if roles_res:
                return f(*args, **kwargs)
            else:
                return Response(
                    {
                        "code": -4,
                        "data": {
                            "message": "success",
                            "detail": '当前用户没有该APP权限',
                        },
                    }
                )

        return __decorated_function

    return decorated_function


def request_restrict_ip():
    """
    限制每个IP限制每秒访问两次
    :param app_url:
    :return:
        """
    def decorated_function(f):
        @wraps(f)
        def __decorated_function(*args, **kwargs):
            x_forwarded_for = args[1].META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = args[1].META.get('REMOTE_ADDR')

            redis_conn = get_redis_connection()
            key = f'restrict_ip_{ip}'
            ip_count = redis_conn.get(key)
            if not ip_count:
                redis_conn.incr(key)
                redis_conn.expire(key, 1)
            else:
                redis_conn.incr(key)
                if int(ip_count) >= 2:
                    return Response(
                            {
                                "code": -4,
                                "data": {
                                    "message": "访问过于频繁！"
                                },
                            }
                        )
            return f(*args, **kwargs)
        return __decorated_function

    return decorated_function