#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:55:47
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_status.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 15:00:21



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class StatusPT(scada_Base):
    ''' 状态配置表 '''
    __tablename__ = "t_status"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    device = Column(Integer, nullable=False, comment=u"所属设备")
    no = Column(Integer, nullable=False,comment=u"编号")
    descr = Column(String(256), nullable=False,comment=u"名称")
    level = Column(Integer, nullable=False,comment=u"")
    desc_invoff = Column(Integer, nullable=True,comment=u"")
    desc_off = Column(Integer, nullable=True,comment=u"")
    desc_on = Column(Integer, nullable=True,comment=u"")
    desc_invon = Column(Integer, nullable=True,comment=u"")
    store_flag = Column(Integer, nullable=True,comment=u"")
    gen_timed_flag = Column(Integer, nullable=True,comment=u"")
    rpt_timed_flag = Column(Integer, nullable=True,comment=u"")
    comment = Column(String(256), nullable=True,comment=u"")

   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','device':'%s','no':%s,'descr':'%s','level':'%s','desc_invoff':'%s','desc_off':'%s','desc_on':'%s','desc_invon':'%s','store_flag':'%s','gen_timed_flag':'%s','rpt_timed_flag':'%s','comment':'%s'}" % (
            self.id,self.name,self.device,self.no,self.descr,self.level,self.desc_invoff,self.desc_off,self.desc_on,self.desc_invon,self.store_flag,self.gen_timed_flag,self.rpt_timed_flag,self.comment)