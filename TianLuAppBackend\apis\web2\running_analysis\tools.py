# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/25 上午10:17
# <AUTHOR> Li<PERSON><PERSON>
# @Project  : TianLuAppBackend
# @File     : tools.py
# @Software : PyCharm
from datetime import datetime

def format_duration(s):
    """
    转换时间：比如s＜60，就显示x秒，60<=s<3600就显示x.x分钟，3600<s<3600*24就显示x.x小时，s>=3600*24就显示x天x小时
    """""

    if s < 60:
        return f"{s:.1f}秒"
    elif s < 3600:
        minutes = s / 60
        return f"{minutes:.1f}分钟"
    elif s < 3600 * 24:
        hours = s / 3600
        return f"{hours:.1f}小时"
    else:
        days = s // (3600 * 24)
        remaining_seconds = s % (3600 * 24)
        hours = remaining_seconds / 3600
        return f"{days}天{hours:.1f}小时"


def en_format_duration(s):
    """
    转换时间：比如s＜60，就显示x秒，60<=s<3600就显示x.x分钟，3600<s<3600*24就显示x.x小时，s>=3600*24就显示x天x小时
    """""

    if s < 60:
        return f"{s:.1f}s"
    elif s < 3600:
        minutes = s / 60
        return f"{minutes:.1f} minutes"
    elif s < 3600 * 24:
        hours = s / 3600
        return f"{hours:.1f} hours"
    else:
        days = s // (3600 * 24)
        remaining_seconds = s % (3600 * 24)
        hours = remaining_seconds / 3600
        return f"{days}days {hours:.1f} hours"


def get_time_period(t: datetime):
    """
    每个小时分成00、15、30、14 这4个时间段，判断当前时间属于哪个时间段
    """""
    minutes = t.minute
    hour = t.hour

    if minutes < 15:
        return f"{hour}:00"
    elif minutes < 30:
        return f"{hour}:15"
    elif minutes < 45:
        return f"{hour}:30"
    else:
        return f"{hour}:45"


if __name__ == '__main__':

    # 示例
    # print(format_duration(59))      # 输出：59秒
    # print(format_duration(3599))    # 输出：59.8分钟
    # print(format_duration(3600))    # 输出：1.0小时
    # print(format_duration(86400))   # 输出：1天0.0小时
    # print(format_duration(90061))   # 输出：1天1.0小时

    t = datetime.strptime("2024-03-08 09:55:22", "%Y-%m-%d %H:%M:%S")
    current_period = get_time_period(t)
    print("当前时间属于", current_period, "时间段")
