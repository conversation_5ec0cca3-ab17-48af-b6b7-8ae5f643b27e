import json
import os
import re
import traceback
import uuid
from django.db.models import Q
from rest_framework.response import Response
from rest_framework.views import APIView
from apis.user import models
from common import common_response_code
from django.conf import settings
from django.db import transaction
from middlewares.authentications import <PERSON><PERSON><PERSON><PERSON>erAuthentication, DenyAuthentication, JwtParamAuthentication
from apis.statistics_apis.forms import UploadFileForm
from tools.minio_tool import MinioTool

from LocaleTool.common import redis_pool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG

class OrganizationViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''查询组织'''
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        name = request.data.get("name", '')#组织名称

        if name:
            organization_ins = models.Organization.objects.filter(Q(name__contains=name) | Q(en_name__contains=name),
                                                                  is_used=1).order_by('id')
        else:
            organization_ins = models.Organization.objects.filter(is_used=1).order_by('id')
        organizations_ins = organization_ins.values("id", "name", "en_name")

        if lang == 'en':
            for i in organizations_ins:
                i['name'] = i['en_name']

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": organizations_ins},
            }
        )


class OrganizationAddViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''添加组织'''
    @transaction.atomic
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        try:
            name = request.data.get("name")
            remark = request.data.get("remark", '')
            # 如果请求中包含文件，则将其保存到服务器上
            name_ = models.Organization.objects.filter(name=name).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "组织已存在！" if lang == 'zh' else 'Organization already exists!'},
                })

            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():
                # uploaded_file = form.cleaned_data['file']
                uploaded_file_instance = models.Organization()

                img_obj = request.FILES.get('file')
                new_name = name
                if img_obj and new_name:
                    img_name = img_obj.name  # 获取上传文件的字符串类型名称/
                    suffix = re.findall(r'\..*', img_name)[0]
                    file_name = new_name + suffix
                    path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

                    with open(path, mode='wb') as f:
                        for content in img_obj.chunks():  # 读取上传文件的内容
                            f.write(content)  # 存储图像文件

                    try:
                        minio_client = MinioTool()
                        minio_client.create_bucket('worker')
                        minio_client.upload_local_image(file_name, path, 'worker')
                        os.remove(path)
                    except Exception as e:
                        success_log.error(e)
                        error_log.error(traceback.print_exc())

                    uploaded_file_instance.file_name = file_name
                else:
                    uploaded_file_instance.file_name = '融和元储logo.png20241112135751-1.png'

                uploaded_file_instance.uuid = uuid.uuid4()
                uploaded_file_instance.name = name
                uploaded_file_instance.en_name = name
                uploaded_file_instance.remark = remark if remark else ''
                uploaded_file_instance.en_remark = remark if remark else ''
                uploaded_file_instance.save()
                # download_url = uploaded_file_instance.get_download_url()  # 获取文件的下载链接
                # uploaded_file_instance.url = download_url
                uploaded_file_instance.save()
            else:
                organization_ins = models.Organization.objects.create(
                    uuid='07240d1e-5c90-4770-af33-175d51768891',
                    name=name,
                    en_name=name,
                    remark=remark if remark else '',
                    en_remark=remark if remark else '',
                    file_name='融和元储logo.png20241112135751-1.png',
                )
                organization_ins.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "添加成功!" if lang == 'zh' else 'Add successfully!'},
            })
        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "添加失败！" if lang == 'zh' else 'Add failed!'},
            })


class OrganizationDeViews(APIView):     # 删除
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        p_id = request.data.get('id')
        if p_id:
            obj = models.Organization.objects.get(id=p_id)
            obj.is_used = 2
            obj.save()
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "删除成功！" if lang == 'zh' else 'Delete successfully!'},
            })
        else:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "id为必填项" if lang == 'zh' else 'The id is required.'},
            })


class OrganizationInfoViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    ''' 展示与修改组织'''
    def get(self, request, *args, **kwargs):
        lang = request.headers.get("lang", 'zh')
        p_id = self.kwargs['id']    # id
        try:
            organization_ins = models.Organization.objects.get(id=p_id,is_used=1)
        except:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到" if lang == 'zh' else 'Not found.'},
            })

        url = ''
        try:
            url = MinioTool().get_download_url('worker', organization_ins.file_name)
        except Exception as e:
            print(e)
            error_log.error(traceback.print_exc())

        organization_dic = {
            'name': organization_ins.name if lang == 'zh' else organization_ins.en_name,
            'remark': organization_ins.remark if lang == 'zh' else organization_ins.en_remark,
            'file_name': organization_ins.file_name,
            'url': url
        }

        return Response(organization_dic)

    def put(self, request, *args, **kwargs):
        lang = request.headers.get("lang", 'zh')
        try:
            data = request.data
            p_id = data['id']
            name = request.data.get("name")
            remark = request.data.get("remark", '')

            name_ = models.Organization.objects.filter(Q(name=name) | Q(en_name=name)).exclude(id=p_id).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "组织已存在！" if lang == 'zh' else 'Organization already exists!'},
                })
            # 修改组织
            organization_ins = models.Organization.objects.get(id=p_id)
            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():

                img_obj = request.FILES.get('file')
                new_name = name
                if img_obj and new_name:
                    img_name = img_obj.name  # 获取上传文件的字符串类型名称/
                    suffix = re.findall(r'\..*', img_name)[0]
                    file_name = new_name + suffix
                    path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

                    with open(path, mode='wb') as f:
                        for content in img_obj.chunks():  # 读取上传文件的内容
                            f.write(content)  # 存储图像文件

                    try:
                        minio_client = MinioTool()
                        minio_client.create_bucket('worker')
                        minio_client.upload_local_image(file_name, path, 'worker')
                        os.remove(path)
                    except Exception as e:
                        success_log.error(e)
                        error_log.error(traceback.print_exc())

                    organization_ins.file_name = file_name
                else:
                    organization_ins.file_name = '融和元储logo.png20241112135751-1.png'

                uploaded_file = form.cleaned_data['file']
                organization_ins.file = uploaded_file
                # organization_ins.file_name = uploaded_file.name
                organization_ins.uuid = uuid.uuid4()
                organization_ins.name = name
                organization_ins.en_name = name
                organization_ins.remark = remark if remark else ''
                organization_ins.en_remark = remark if remark else ''
                organization_ins.save()
                download_url = organization_ins.get_download_url()  # 获取文件的下载链接
                # organization_ins.url = download_url
                organization_ins.save()

            else:
                organization_ins.uuid = '07240d1e-5c90-4770-af33-175d51768891'
                organization_ins.name = name
                organization_ins.remark = remark if remark else ''
                organization_ins.en_name = name
                organization_ins.en_remark = remark if remark else ''
                organization_ins.file_name = '融和元储logo.png20241112135751-1.png'
                organization_ins.save()

            # 异步翻译
            pdr_data = {'id': organization_ins.id,
                        'table': 't_organization',
                        'update_data': {'name': name}}
            if remark != '':
                pdr_data['update_data']['remark'] = remark

            pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            redis_pool.publish(pub_name, json.dumps(pdr_data))

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "修改成功！" if lang == 'zh' else 'Modify successfully!'},
            })

        except Exception as e:
            print(286, traceback.print_exc())
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "修改失败！" if lang == 'zh' else 'Modify failed!'},
            })


