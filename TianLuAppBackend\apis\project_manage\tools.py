# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/29 下午5:08
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : tools.py
# @Software : PyCharm


def remove_empty_string_keys(json_obj):
    if isinstance(json_obj, dict):
        new_obj = {}
        for key, value in json_obj.items():
            if value != '':
                new_value = remove_empty_string_keys(value)
                if new_value is not None:
                    new_obj[key] = new_value
        return new_obj if new_obj else None
    elif isinstance(json_obj, list):
        new_list = []
        for item in json_obj:
            new_item = remove_empty_string_keys(item)
            if new_item is not None:
                new_list.append(new_item)
        return new_list if new_list else None
    else:
        return json_obj


if __name__ == '__main__':

    # 示例 JSON 对象
    json_obj = {
        "name": "张三",
        "age": 25,
        "address": {
            "city": "",
            "street": "南京路",
            "zipcode": ""
        },
        "hobbies": ["reading", "", "swimming"]
    }

    # 调用函数并打印结果
    new_json_obj = remove_empty_string_keys(json_obj)
    print(new_json_obj)


    new_obj = {
    "project_name": "ddd",
    "project_english_name": "cg00123",
    "address": "测试地址",
    "province_id": 11,
    "city": "南京市",
    "counties_id": 117,
    "compant_name": None,
    "compant_code": None,
    "industry_id": 1,
    "connect_time": "2024-04-16",
    "images": "https://weathernew.pae.baidu.com/weathernew/pc?query=%E5%8C%97%E4%BA%AC%E5%A4%A9%E6%B0%94&srcid=4982&forecast=long_day_forecast",
    "organization_id": 1,
    "manager": "测试经理",
    "manager_phone": 16638678890,
    "price_type": 1,
    "_type": 2,
    "level": 2,
    "station_info": [
        {
            "name": "常规站001",
            "english_name": "cg001013",
            "meter_position": 1,
            "transformer_capacity": 200,
            "mode": 1,
            "equipment_info": [
                {
                    "name": "常规站001",
                    "english_name": "cg001013",
                    "int_num": "**********",
                    "app": "zx11",
                    "emq_user": "user",
                    "emq_pwd": "12332saf",
                    "emq_clid": "123asfr1231e123",
                    "model": 2,
                    "is_account": 1,
                    "meter_number": "",
                    "account_start": "2024-05-01 00:00:00",
                    "account_end": "",
                    "unit_info": [
                        {
                            "name": "储能单元1",
                            "english_name": "PCS94926079-31de-88",
                            "versions": "3.0"
                        },
                        {
                            "name": "储能单元2",
                            "english_name": "PCS94926079-31de-89",
                            "versions": "3.0"
                        }
                    ]
                }
            ]
        }
    ]
    }

    print(remove_empty_string_keys(new_obj))

