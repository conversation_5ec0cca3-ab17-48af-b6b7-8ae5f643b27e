package com.robestec.dailyproduce.project.dto.sim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.util.Date;

/**
 * 5G专用卡信息更新DTO
 */
@Data
@ApiModel("5G专用卡信息更新DTO")
public class ProjectSimUpdateDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("卡号（唯一）")
    private String cardNumber;

    @ApiModelProperty("运营商（枚举：中国移动、中国联通、中国电信、其他）")
    private String operator;

    @ApiModelProperty("有效日期")
    private Date expiryDate;

    @ApiModelProperty("安装位置")
    private String installLocation;

    @ApiModelProperty("保管人")
    private String keeper;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("备注")
    private String remark;
}
