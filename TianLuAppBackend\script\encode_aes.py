import base64

from Crypto.Cipher import AES


class AESEncrypt:
    def __init__(self, key):
        self.key = key
        self.length = AES.block_size
        self.aes = AES.new(self.key, AES.MODE_ECB)
        self.delete_pad = lambda date: date[0:-ord(date[-1])]

    def add_pad(self, text):
        # 待加密数据长度
        count = len(text.encode('utf-8'))
        # 距离16的倍数差几位数
        add = self.length - (count % self.length)
        # 填充成16的倍数
        filling_text = text + (chr(add) * add)
        return filling_text

    def encrypt(self, encrypt_data):
        # 填充字符串
        # b'{"x":82.66666666666667,"y":5}\x03\x03\x03'
        plaintext = self.add_pad(text=encrypt_data).encode("utf8")
        # aes加密
        # b'\x859\xdb\xd4\x10\x04\x96:9\x8c,7V\xa9\x87\xf4\xa5k\xdc9\xc0\x9e\xf8\xd3\xc8\x92\x9cX\x84HH\x9d'
        result = self.aes.encrypt(plaintext=plaintext)
        # base64加密
        # 'hTnb1BAEljo5jCw3VqmH9KVr3DnAnvjTyJKcWIRISJ0='
        result = str(base64.b64encode(result), encoding="utf8")
        return result

    def decrypt(self, decrypt_data):
        # base64解密
        # b'\x859\xdb\xd4\x10\x04\x96:9\x8c,7V\xa9\x87\xf4\xa5k\xdc9\xc0\x9e\xf8\xd3\xc8\x92\x9cX\x84HH\x9d'
        result = base64.decodebytes(decrypt_data.encode("utf8"))
        # aes解密
        # '{"x":82.66666666666667,"y":5}\x03\x03\x03'
        result = self.aes.decrypt(result).decode("utf8")
        # 消除字符串
        # "{"x":82.66666666666667,"y":5}"
        return self.delete_pad(result)


key = "ZRPH8iALbJDkqOCY".encode(encoding="utf8")
aes = AESEncrypt(key=key)
res = aes.encrypt('{"x":82.66666666666667,"y":5}')

print(res)
print(aes.decrypt(res))
