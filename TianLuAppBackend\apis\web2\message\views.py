import datetime
import json
import traceback

import pymysql
from dbutils.persistent_db import PersistentDB
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response

from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from common import common_response_code
from apis.app2.utils import paging
from apis.statistics_apis.models import StrategyApplyHistory
from apis.user.models import PeakValleyNew, StationActicNew, FormerActicNew, StationDetails, MessageCenter, MaterStation
from settings.types_dict import locale_keys
from LocaleTool.common import redis_pool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG

class MessageList(APIView):
    """
    消息中心列表
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        is_read = request.query_params.get('is_read')  # 0：未读，不传该字段则为查询全部
        message_type = request.query_params.get('message_type')  # 0：策略确认消息；-1：策略确认结果消息；1：告警推送消息；2：运行分析推送消息
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 10))
        user_id = request.user["user_id"]
        message_res = MessageCenter.objects.filter(user_id=user_id)
        if is_read:  # 未读列表
            message_res = message_res.filter(is_read=0)
        if message_type:
            if message_type == '-1':
                message_res = message_res.filter(type=0, is_handle=2)
            elif message_type == '0':
                message_res = message_res.filter(type=0, is_handle__lte=2)
            else:
                message_res = message_res.filter(type=int(message_type))
        message_res = message_res.order_by('is_read', '-create_time').all()
        page_res = paging(page, size, message_res)  # 分页器
        res = page_res.get('data')
        data = []  # 返回的消息信息
        now_time = datetime.datetime.now()
        for i in res:
            time_difference = now_time - i.create_time  # 抄送经过时间
            if i.type == 0:
                if i.is_handle == 2:
                    type_ = -1
                else:
                    type_ = 0
            else:
                type_ = i.type

            if lang == 'zh':

                if time_difference.total_seconds() < 60 * 60:  # 小于一小时
                    _time = f'{int(time_difference.seconds / 60)}分钟前'
                elif time_difference.days < 1:  # 小于一天
                    _time = f'{int(time_difference.seconds / 60 / 60)}小时前'
                elif time_difference.days <= 30:  # 小于一个月)
                    _time = f'{time_difference.days}天前'
                elif (now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month < 12:  # 小于一年
                    _time = f'{(now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month}月前'
                elif (now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month >= 12:  # 大于一年
                    _time = f'{int(((now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month) / 12)}年前'
                else:
                    _time = f'1分钟前'

                data.append({
                    'id': i.id,
                    'title': i.title,
                    'type': type_,
                    'create_time': i.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_verify': i.is_verify,
                    'is_read': i.is_read,
                    'is_handle': i.is_handle,
                    'strategy_name': '策略确认消息' if i.is_handle < 2 else '策略确认结果消息',
                    'time': _time,
                })
            else:
                if time_difference.total_seconds() < 60 * 60:  # 小于一小时
                    _time = f'{int(time_difference.seconds / 60)} minutes ago'
                elif time_difference.days < 1:  # 小于一天
                    _time = f'{int(time_difference.seconds / 60 / 60)} hours ago'
                elif time_difference.days <= 30:  # 小于一个月)
                    _time = f'{time_difference.days} days ago'
                elif (now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month < 12:  # 小于一年
                    _time = f'{(now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month} months ago'
                elif (now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month >= 12:  # 大于一年
                    _time = f'{int(((now_time.year - i.create_time.year) * 12 + now_time.month - i.create_time.month) / 12)} years ago'
                else:
                    _time = f'1 minutes ago'

                data.append({
                    'id': i.id,
                    'title': i.en_title,
                    'type': type_,
                    'create_time': i.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_verify': i.is_verify,
                    'is_read': i.is_read,
                    'is_handle': i.is_handle,
                    'strategy_name': 'Strategy Confirmation Message' if i.is_handle < 2 else 'Strategy Confirmation Result Message',
                    'time': _time,
                })

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
                "total": page_res.get('total'),
                "pageNum": page,
                "pageSize": size
            }
        )


class MessageRead(APIView):
    """
    标记已读
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 消息ID
        user_id = request.user["user_id"]
        if id:
            MessageCenter.objects.filter(id=int(id), user_id=user_id).update(is_read=1)
        else:
            MessageCenter.objects.filter(user_id=user_id).update(is_read=1)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "标记已读成功！" if lang == 'zh' else "Marked as read successfully!", "detail": []},
            }
        )


class MessageStrategyDetail(APIView):
    """
    抄送策略详情
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def _handle_strategy_hours(self, charge_config, rl_list, pv_list):
        """相同时间内的阈值、充放电标识、峰谷标识 数据合并"""
        compare_list = zip(charge_config, rl_list, pv_list)
        last_info = None
        _i = 0
        t = '00:00'
        data = []
        for i, v in enumerate(compare_list):
            if i != 0:
                if v == last_info:
                    _i += 1
                else:
                    end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
                    start_time = datetime.datetime.strptime(t, '%H:%M')
                    end_time = end_time(start_time).strftime('%H:%M')

                    d = {
                        'start_time': t,
                        'end_time': end_time,
                        'rl': last_info[1],
                        'charge_config': last_info[0],
                        'pv': last_info[2],
                        'explain': '',
                    }
                    data.append(d)
                    t = end_time
                    _i = 1
                    last_info = v
            else:
                _i += 1
                last_info = v

        end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
        start_time = datetime.datetime.strptime(t, '%H:%M')
        end_time = (end_time(start_time) - datetime.timedelta(minutes=1)).strftime('%H:%M')
        d = {
            'start_time': t,
            'end_time': end_time,
            'rl': last_info[1],
            'charge_config': last_info[0],
            'pv': last_info[2],
            'explain': '',
        }
        data.append(d)

        return data


    def _get_pv_status_new(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        station_instance = PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).all()
        if station_instance:
            if settings.FORMATEF:
                for i in station_instance:
                    pv.append(i.pv)
            else:
                for i in station_instance[::4]:
                    pv.append(i.pv)
            return pv
        else:
            return pv

    def _get_pool(self):
        # 连接数据库
        pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
        return pool

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        id = request.query_params.get('id')  # 消息ID
        user_id = request.user["user_id"]
        message_obj = MessageCenter.objects.filter(user_id=user_id)
        message_obj = message_obj.filter(id=id, type=0).first()
        if not message_obj:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "参数错误！" if lang == 'zh' else "Parameter error!", "detail": []},
                }
            )
        # 建立数据库链接
        pool = self._get_pool()
        conn = pool.connection()
        cursor = conn.cursor()
        brfore = {}  # 修改前：策略上次下发记录
        now_month = datetime.datetime.now().month
        next_month = now_month + 1 if now_month != 12 else 1
        # 查询本次下发时间
        delivery_strategy = StrategyApplyHistory.objects.filter(station_id=message_obj.station.id,is_delete=0).order_by(
            '-id').first()
        delivery_time = delivery_strategy.create_time
        # 查询此次下发30分钟前的当前策略信息
        sql = f"""
                        SELECT
                               time, station_name, device, slave, pack, type, ots, rlh1p, rlh2p, rlh3p, rlh4p, rlh5p, rlh6p, rlh7p, rlh8p, rlh9p, 
                                rlh10p, rlh11p, rlh12p, rlh13p, rlh14p, rlh15p, rlh16p, rlh17p, rlh18p, rlh19p, rlh20p, rlh21p, rlh22p, rlh23p, rlh24p,
                                 rlh1f, rlh2f, rlh3f, rlh4f, rlh5f, rlh6f, rlh7f, rlh8f, rlh9f, rlh10f, rlh11f, rlh12f, rlh13f, rlh14f, rlh15f, rlh16f, 
                                 rlh17f, rlh18f, rlh19f, rlh20f, rlh21f, rlh22f, rlh23f, rlh24f, rlh25p, rlh26p, rlh27p, rlh28p, rlh29p, rlh30p, rlh31p, 
                                 rlh32p, rlh33p, rlh34p, rlh35p, rlh36p, rlh37p, rlh38p, rlh39p, rlh40p, rlh41p, rlh42p, rlh43p, rlh44p, rlh45p, rlh46p,
                                  rlh47p, rlh48p, rlh25f, rlh26f, rlh27f, rlh28f, rlh29f, rlh30f, rlh31f, rlh32f, rlh33f, rlh34f, rlh35f, rlh36f, rlh37f,
                                   rlh38f, rlh39f, rlh40f, rlh41f, rlh42f, rlh43f, rlh44f, rlh45f, rlh46f, rlh47f, rlh48f 
                        FROM
                            `dwd_measure_ems_data_storage_tscale` 
                        WHERE
                            station_name = '{message_obj.station.english_name}' 
                            AND time <= '{delivery_time - datetime.timedelta(minutes=30)}' 
                            AND device='EMS'
                        ORDER BY
                            time DESC 
                            LIMIT 1
                        """
        try:
            cursor.execute(sql)
            # 获取查询结果
            result = cursor.fetchone()
        except Exception as e:
            error_log.error("查询当前策略历史表数据失败！", e)
        pv_list = None
        if result:
            if result.get('rlh25p'):
                strategy_time_title = [48]
                strategy_time_title.extend([i for i in range(1, 48)])
                cycle_number = 2  # 半小时
            else:
                strategy_time_title = [24]
                strategy_time_title.extend([i for i in range(1, 24)])
                cycle_number = 4  # 一小时
            charge_config = []
            rl_list = []
            for t in strategy_time_title:
                for i in range(cycle_number):
                    charge_config.append(int(result.get(f'rlh{t}f')) if result.get(f'rlh{t}f') else "--")
                    rl_list.append(int(float(result.get(f'rlh{t}p')) * 100) if result.get(f'rlh{t}p') else "--")
            station = message_obj.station
            pv_list = self._get_pv_status_new(station.province, station.type, station.level, now_month)
            brfore_obj = self._handle_strategy_hours(charge_config, rl_list, pv_list)
            brfore[str(now_month)] = brfore_obj

        # 展示当前月和下月的策略信息
        after = {}  # 修改后
        months = []  # 有修改内容的月份
        if message_obj.strategy:  # 自定义策略
            after_strategy = message_obj.strategy.userstrategyhours_set.first()  # 本次下发的内容
            if after_strategy:
                for info in json.loads(after_strategy.data):
                    if now_month in info.get('months'):
                        after[str(now_month)] = info.get('data')
                    if next_month in info.get('months'):
                        after[str(next_month)] = info.get('data')
                    else:
                        continue

        else:  # 默认策略
            station = message_obj.station
            for month in [now_month, next_month]:
                pv_list = self._get_pv_status_new(station.province, station.type, station.level, month)
                former_actic_ids = StationActicNew.objects.filter(station_id=station.id).all()
                former_actic_ids = [i.former_actic_id for i in former_actic_ids]
                year_month = f"{datetime.datetime.now().year}-{month}" if int(month) >= 10 else f"{datetime.datetime.now().year}-0{month}"
                former_res = FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
                if not former_res:
                    return Response(
                        {
                            "code": common_response_code.ERROR,
                            "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理" if lang == 'zh' else
                            "The current query time has no policy information, please contact the administrator to handle.",
                                     "detail": {}},
                        }
                    )
                rl_list = []
                charge_config = []
                for info in former_res:
                    rl_list.append(round(info.power_value / info.power * 100, 2))
                    charge_config.append(info.mark)
                after_obj = self._handle_strategy_hours(charge_config, rl_list, pv_list)
                after[str(now_month)] = after_obj

        for i in [now_month]:
            if brfore and (after[str(i)] != brfore[str(i)]):
                months.append(i)
        months.append(next_month)
        if lang == 'zh':
            data = {
                'id': message_obj.id,
                'title': message_obj.title,
                'name': f'{message_obj.station.project.name}策略确认消息' if message_obj.is_handle < 2 else f'{message_obj.station.project.name}策略确认结果消息',
                'time': message_obj.create_time.strftime('%Y年%m月%d日'),
                'is_verify': message_obj.is_verify,
                'is_handle': message_obj.is_handle,
                'opinion': message_obj.opinion,
                'files_dict': json.loads(message_obj.files) if message_obj.files else {},
                'after': after,
                'brfore': brfore,
                'months': months,
                'controls': True if user_id == message_obj.user_id else False
                }
        else:
            data = {
                'id': message_obj.id,
                'title': message_obj.en_title,
                'name': f'{message_obj.station.project.name} Strategy Confirmation Message' if message_obj.is_handle < 2 else f'{message_obj.station.project.name} Strategy Confirmation Result Message',
                'time': message_obj.create_time.strftime('%Y-%m-%d'),
                'is_verify': message_obj.is_verify,
                'is_handle': message_obj.is_handle,
                'opinion': message_obj.en_opinion,
                'files_dict': json.loads(message_obj.files) if message_obj.files else {},
                'after': after,
                'brfore': brfore,
                'months': months,
                'controls': True if user_id == message_obj.user_id else False
            }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
            }
        )


class MessageFeedback(APIView):
    """
    策略确认、策略反馈
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        策略确认
        """
        lang = request.headers.get("lang", 'zh')
        try:
            _id = request.query_params.get('id')  # 消息ID
            # 1 确认状态
            info = MessageCenter.objects.filter(id=_id).first()
            info.is_handle = 1
            info.save()
            title = f'客户 确认{info.station.project.name}策略无误！'
            en_title = 'Customer Confirmation of ' + info.station.project.name + ' Policy is Correct!'
            ins = MessageCenter.objects.create(title=title, en_title=en_title, type=-1, is_read=0, is_verify=1, station=info.station,
                                         user_id=info.issue_user, issue_user=info.user_id,
                                         strategy_id=info.strategy_id, is_handle=2)

        except Exception as e:
            print(422, traceback.print_exc())
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "参数错误！" if lang == 'zh' else 'Parameter error.', "detail": []},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "确认成功！" if lang == 'zh' else 'Confirmation successful.', "detail": []},
            }
        )

    def post(self, request):
        """
        策略反馈
        """
        lang = request.headers.get("lang", 'zh')
        _id = request.data.get('id')  # 消息ID
        opinion = request.data.get('opinion')  # 反馈意见
        files = request.data.get('files')  # 上传附件信息
        try:
            # 2 已反馈状态
            info = MessageCenter.objects.filter(id=_id).first()
            info.is_handle = 1
            info.opinion = opinion
            info.files = json.dumps(files)
            info.save()
            title = f"客户 对调整的{info.station.project.name}策略进行了反馈：{opinion}"
            en_title = 'Customer Feedback on Adjustment of ' + info.station.project.name + ' Policy: ' + opinion
            ins = MessageCenter.objects.create(title=title, en_title=en_title, type=0, is_read=0, is_verify=1, station=info.station,
                                         user_id=info.issue_user, issue_user=info.user_id,
                                         strategy_id=info.strategy_id, is_handle=2, opinion=opinion,
                                         en_opinion=opinion,
                                         files=json.dumps(files))

            # 异步翻译
            pdr_data = {'id': ins.id,
                        'table': 't_message_center',
                        'update_data': {'title': title}}

            if opinion:
                pdr_data['update_data']['opinion'] = opinion

            redis_pool.publish(locale_keys[lang], json.dumps(pdr_data))

        except Exception as e:
            error_log.error(f'策略抄送反馈失败：{e}')
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "参数错误！" if lang == 'zh' else 'Parameter error.', "detail": []},
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "反馈成功！" if lang == 'zh' else 'Feedback successful.', "detail": []},
            }
        )


class MessageSendCopy(APIView):
    """
    抄送项目校验
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        station_ids = request.data.get('station_ids')
        _type = request.data.get('type')  #实时策略下发时候传该参数；值为1
        if _type and _type == 1:
            station_instance = MaterStation.objects.filter(is_delete=0, id__in=station_ids).all()
        else:
            station_instance = StationDetails.objects.filter(is_delete=0, id__in=station_ids).all()
        if station_instance:
            for station in station_instance:
                project = station.project
                if not project.contact_person or not project.contact_person_phone:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error",
                                     "detail": "当前项目未找到客户联系人，请添加该项目客户联系人后，再选择将策略抄送给客户！" if
                                     lang == 'zh' else 'The current project has not found the customer contact person,'
                                                       ' please add the customer contact person of the project and then'
                                                       ' choose to copy the strategy to the customer.',
                                     "details": []},
                        }
                    )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": [],
                },
            }
        )


class MessageMapping(APIView):
    """
    消息中心下拉框字典
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')

        message_type_dict = {
            '策略确认消息': '0',
            '策略确认结果消息': '-1',
            '告警推送消息': '1',
            '运行分析推送消息': '2',
            '电池电压分析推送消息': '3',
            '电池温度分析推送消息': '4',
            '电池过放风险推送消息': '5'
        }

        en_message_type_dict = {
            'Strategy Confirmation Message': '0',
            'Strategy Confirmation Result Message': '-1',
            'Alarm Push Message': '1',
            'Run analysis push message': '2',
            'Battery Voltage Analysis Push Message': '3',
            'Battery Temperature Analysis Push Message': '4',
            'Risk of Battery Over Discharge': '5'
        }

        data = {
            'message_type_dict': message_type_dict if lang == 'zh' else en_message_type_dict,
        }
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data},
            }
        )