#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-01-25 17:15:02
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Tools\Utils\mimio_tool.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-04-10 15:00:18
import io
from datetime import timedelta
from minio.error import S3Error
from minio import Minio
# 文件服务器配置
MINIO_CONF = {
        #'endpoint': '192.168.1.101:9006',  内网
        # 'endpoint': '120.132.33.184:9006',  公网
        'endpoint': 'minio.robestec.cn',
        'access_key': 'transmit',
        'secret_key': 'RHbess9ol.',
        'secure': True  # True HTTPS  FALSE   HTTP
    }


class MinioTool:
    def __init__(self):
        # 使用endpoint、access key和secret key来初始化minioClient对象。
        self.minioClient = Minio(**MINIO_CONF)

    def create_bucket(self, bucket_name='rhyc'):
        # 调用make_bucket来创建一个存储桶。
        if self.minioClient.bucket_exists(bucket_name):
            print('Bucket：', bucket_name, '已存在！')
            return
        self.minioClient.make_bucket(bucket_name, location="us-east-1")
        return bucket_name

    def upload_local_file(self, file_name, file_path, bucket_name='rhyc'):
        '''
        file_name:存储文件名
        file_path：文件路径
        bucket_name：桶名称
        '''
        self.minioClient.fput_object(bucket_name, file_name, file_path)
        # print('文件上传成功！')
        download_url = self.minioClient.presigned_get_object(bucket_name, file_name, expires=timedelta(days=7))
        return download_url

    def upload_object(self, file, bucket_name='rhyc'):
        '''
        file:二进制文件，含文件名和大小
        bucket_name：桶名称
        '''
        self.minioClient.put_object(bucket_name, file.name, file, file.size)
        # print('文件上传成功！')
        download_url = self.minioClient.presigned_get_object(bucket_name, file.name)
        return download_url

    def get_download_url(self, bucket_name, object_name):
        '''
        bucket_name：桶名称
        object_name:存储的文件名
        '''
        download_url = self.minioClient.presigned_get_object(bucket_name, object_name)
        return download_url


# 将二进制文件存储
minio_client = Minio(
    "minio.robestec.cn",
    access_key="transmit",
    secret_key="RHbess9ol.",
    secure=True
)

# 替换为你要上传的二进制数据、存储桶名称和对象名称
binary_data = b'your-binary-data'
bucket_name = "your-bucket-name"
object_name = "your-object-name"

def upload_file(binary_data, bucket_name, object_name):
    try:
        if len(binary_data) > 1048576 * 101:  # 判断附件大小是否大于101MB
             return False
        # 确保存储桶存在
        if not minio_client.bucket_exists(bucket_name):
            minio_client.make_bucket(bucket_name)

        # 上传二进制数据
        binary_stream = io.BytesIO(binary_data)
        minio_client.put_object(bucket_name, object_name, binary_stream, len(binary_data))

        # 返回存储地址
        # storage_url = minio_client.presigned_get_object(bucket_name, object_name,expires=timedelta(weeks=10))
        storage_url = minio_client.presigned_get_object(bucket_name, object_name, expires=timedelta(days=7))

        return storage_url

    except S3Error as exc:
        print("上传文件失败，错误信息：", exc)


# minioClient = MinioTool()

if __name__ == '__main__':
    minioClient = MinioTool()
    # minioClient.create_bucket('rhyc')
    # url = minioClient.upload_local_file('山西省_SOP.png', './山西省SOP流程图.png')
    # print('文件地址为【文件在浏览器打开会直接下载，放到index.html 中使用img引入查看】：\n', url)
    u = minioClient.get_download_url('side','山西省_SOP.png')
    print (u)