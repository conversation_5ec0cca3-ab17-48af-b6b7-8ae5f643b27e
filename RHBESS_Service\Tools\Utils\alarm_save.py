#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:34:43
#@FilePath     : \RHBESS_Service\Tools\Utils\alarm_save.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 15:49:28

from sqlalchemy.sql import func, or_
from Application.Models.His.r_ACDMS import HisSDA, HisTCXR
from Tools.DB.mysql_user import user_session
# from Application.Models.PointTable.t_status import StatusPT
from Application.Models.User.event import Event
# from Tools.DB.ygqn_his import sygqn7_session, sygqn8_session, tcxr_session, tcxr1_session, tcxr2_session, tcxr3_session, tcxr4_session
from Tools.DB.tcxr_his import tcxr_session, tcxr1_session, tcxr2_session, tcxr3_session, tcxr4_session
from Tools.Utils.time_utils import timeUtils
import dm2016
# s_c = dm2016.StatusMgr()
# s_ids= []
#
# #  哈伦电站
# # bj = scada_session.query(StatusPT).filter(StatusPT.name.like('tpSttaicang%'),StatusPT.descr.like('%报警%'),StatusPT.descr.not_like('%备用%')).all()
# # for b in bj:
# #     s_ids.append(b.id)
# # gz = scada_session.query(StatusPT).filter(StatusPT.name.like('tpSttaicang%'),StatusPT.descr.like('%故障%'),StatusPT.descr.not_like('%备用%'),StatusPT.id.not_in(s_ids)).all()
# # db_houma=[shoumaa1_session,shoumaa2_session,shoumab1_session,shoumab2_session]
# # db_ku=[0,sdatong1_session,sdatong2_session,sdatong3_session,sdatong4_session]
# # db_ku=[0,sguizhou1_session,sguizhou2_session,sguizhou3_session,sguizhou4_session,sguizhou5_session,sguizhou6_session,sguizhou7_session,sguizhou8_session]
# # db_ku=[0,sygqn7_session,sygqn8_session,tcxr_session,tcxr1_session,tcxr2_session,tcxr3_session,tcxr4_session]
# db_ku=[0,sygqn7_session,sygqn8_session,tcxr_session,tcxr1_session,tcxr2_session,tcxr3_session,tcxr4_session]
#
# #  永臻调峰电站
# # bj = scada_session.query(StatusPT).filter(StatusPT.name.like('tfStzgtian%'),StatusPT.descr.like('%报警%'),StatusPT.descr.not_like('%备用%')).all()
# # for b in bj:
# #     s_ids.append(b.id)
# # gz = scada_session.query(StatusPT).filter(StatusPT.name.like('tfStzgtian%'),StatusPT.descr.like('%故障%'),StatusPT.descr.not_like('%备用%'),StatusPT.id.not_in(s_ids)).all()
# # for g in gz:
# #     s_ids.append(b.id)
# # yichang = scada_session.query(StatusPT).filter(StatusPT.name.like('tfStzgtian%'),StatusPT.descr.like('%异常%'),StatusPT.descr.not_like('%备用%'),StatusPT.id.not_in(s_ids)).all()
#
# table_1 = 't_status'
# HisTable_1 = HisSDA(table_1)
#
# for bb in range(2,8):  # 每次修改
#     db_con = db_ku[bb]  # 获取具体数据库链接
#     bj = db_con.query(HisTable_1).filter(HisTable_1.name.like('ygqn.d%'),HisTable_1.descr.like('%报警%'),HisTable_1.descr.not_like('%备用%')).all()
#     for b in bj:
#         s_ids.append(b.id)
#     gz = db_con.query(HisTable_1).filter(HisTable_1.name.like('ygqn.d%'),HisTable_1.descr.like('%故障%'),HisTable_1.descr.not_like('%备用%'),HisTable_1.id.not_in(s_ids)).all()
#     for g in gz:
#         s_ids.append(g.id)
#     yichang = db_con.query(HisTable_1).filter(HisTable_1.name.like('ygqn.d%'),HisTable_1.descr.like('%异常%'),HisTable_1.descr.not_like('%备用%'),HisTable_1.id.not_in(s_ids)).all()
#     db_con.close()
# print (2323232323232323232323232323232)
#
#
# ids = user_session.query(func.max(Event.id)).first()
# ids = ids[0]
# print (len(bj),len(gz),len(yichang))
#
#
# for j in gj:
#     ids = ids+1
#     snn = s_c.getByName(j.name)
#     if snn:
#         p = Event(id=ids,name=j.name,descr=j.descr,index=snn.index(),type=2,type_name='status',class_id=1,type_id=5,is_use=1,user_id=1,op_ts=timeUtils.getNewTimeStr(),opt1='#',
#         opt2='#',opt3='#',opt4='#',opt5='#',opt6='#',opt7='#',opt8='#',opt9='#',opt10='#',station='taicgxr')
#         user_session.add(p)
#
# for b in gz:
#     ids = ids+1
#     snn = s_c.getByName(b.name)
#     if snn:
#         p = Event(id=ids,name=b.name,descr=b.descr,index=snn.index(),type=2,type_name='status',class_id=1,type_id=3,is_use=1,user_id=1,op_ts=timeUtils.getNewTimeStr(),opt1='#',
#         opt2='#',opt3='#',opt4='#',opt5='#',opt6='#',opt7='#',opt8='#',opt9='#',opt10='#',station='guizhou')
#         user_session.add(p)
#
# for b in yichang:
#     ids = ids+1
#     snn = s_c.getByName(b.name)
#     if snn:
#         p = Event(id=ids,name=b.name,descr=b.descr,index=snn.index(),type=2,type_name='status',class_id=1,type_id=4,is_use=1,user_id=1,op_ts=timeUtils.getNewTimeStr(),opt1='#',
#         opt2='#',opt3='#',opt4='#',opt5='#',opt6='#',opt7='#',opt8='#',opt9='#',opt10='#',station='guizhou')
#         user_session.add(p)
#
# user_session.commit()
# print (1212121212121212)
# user_session.close()




def create_event(name, databse_list, type, station):
    """
    name: 点表名称：如：taicgxr.EMS.
    databse_list: 数据库链接列表
    type： 1:告警；2:故障；3:异常（判断是否计算该类型的数据）
    station：并网点标识
    """
    s_ids = []
    s_c = dm2016.StatusMgr()
    table_1 = 't_status'
    HisTable_1 = HisTCXR(table_1)
    name = 'taicgxr.EMS.%' if name == 'taicgxr.EMS.' else name
    for db_con in databse_list:
        gj = db_con.query(HisTable_1).filter(HisTable_1.name.like(name),or_(HisTable_1.descr.like('%告警%'), HisTable_1.descr.like('%报警%')),
                                             HisTable_1.descr.not_like('%备用%'), HisTable_1.store_flag == 1).all()
        ids = user_session.query(func.max(Event.id)).first()
        ids = ids[0]
        for j in gj:
            s_ids.append(j.id)

            ids = ids + 1
            snn = s_c.getByName(j.name)
            if snn:
                p = Event(id=ids, name=j.name, descr=j.descr, index=snn.index(), type=2, type_name='status', class_id=1,
                          type_id=3, is_use=1, user_id=1, op_ts=timeUtils.getNewTimeStr(), opt1='#',
                          opt2='#', opt3='#', opt4='#', opt5='#', opt6='#', opt7='#', opt8='#', opt9='#', opt10='#',
                          station=station)
                user_session.add(p)


        if type >= 2:
            gz = db_con.query(HisTable_1).filter(HisTable_1.name.like(name), HisTable_1.descr.like('%故障%'),
                                                 HisTable_1.descr.not_like('%备用%'), HisTable_1.id.not_in(s_ids),HisTable_1.store_flag == 1).all()
            for g in gz:
                s_ids.append(g.id)

                ids = ids + 1
                snn = s_c.getByName(g.name)
                if snn:
                    p = Event(id=ids, name=g.name, descr=g.descr, index=snn.index(), type=2, type_name='status',
                              class_id=1, type_id=3, is_use=1, user_id=1, op_ts=timeUtils.getNewTimeStr(), opt1='#',
                              opt2='#', opt3='#', opt4='#', opt5='#', opt6='#', opt7='#', opt8='#', opt9='#', opt10='#',
                              station=station)
                    user_session.add(p)


        if type == 3:
            yichang = db_con.query(HisTable_1).filter(HisTable_1.name.like(name), HisTable_1.descr.like('%异常%'),
                                                      HisTable_1.descr.not_like('%备用%'), HisTable_1.id.not_in(s_ids),HisTable_1.store_flag == 1).all()

            for b in yichang:
                ids = ids + 1
                snn = s_c.getByName(b.name)
                if snn:
                    p = Event(id=ids, name=b.name, descr=b.descr, index=snn.index(), type=2, type_name='status',
                              class_id=1, type_id=4, is_use=1, user_id=1, op_ts=timeUtils.getNewTimeStr(), opt1='#',
                              opt2='#', opt3='#', opt4='#', opt5='#', opt6='#', opt7='#', opt8='#', opt9='#', opt10='#',
                              station=station)
                    user_session.add(p)

        db_con.close()
        user_session.commit()
    user_session.close()

    return 1


if __name__ == '__main__':
    create_event('taicgxr.EMS.', [tcxr_session], 3, 'taicgxr')