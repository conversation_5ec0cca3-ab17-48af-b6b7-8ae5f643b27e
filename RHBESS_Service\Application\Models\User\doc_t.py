#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-27 09:45:14
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\income_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-06 14:34:35


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Tools.Utils.time_utils import timeUtils

class Tdoc(user_Base):
    u'帮助文档数据'
    __tablename__ = "t_doc"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    doc_number = Column(VARCHAR(255), nullable=False, comment=u"文档编号")
    doc_name = Column(VARCHAR(255), nullable=False, comment=u"文档名称")
    en_doc_name = Column(VARCHAR(255), nullable=False, comment=u"文档名称-英文")
    doc_format = Column(VARCHAR(255), nullable=False,comment=u"文档格式")
    owning_organization = Column(VARCHAR(255), nullable=False,comment=u"归属组织")
    en_owning_organization = Column(VARCHAR(255), nullable=False,comment=u"归属组织-英文")
    description = Column(VARCHAR(500), nullable=True, comment=u"描述")
    en_description = Column(VARCHAR(500), nullable=True, comment=u"描述-英文")
    date_upload = Column(VARCHAR(20), nullable=False,comment=u"上传日期")
    uploader = Column(VARCHAR(255), nullable=False, comment=u"上传人员")
    en_uploader = Column(VARCHAR(255), nullable=False, comment=u"上传人员-英文")

    doc_url = Column(VARCHAR(255), nullable=True, comment=u"文档存放的绝对地址")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    file_name = Column(VARCHAR(255), nullable=False,comment=u"上传文件名")
    en_file_name = Column(VARCHAR(500), nullable=False,comment=u"上传文件名-英文")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):

        bean = "{'id':%s,'doc_number':'%s','doc_name':'%s','doc_format':'%s','owning_organization':'%s','description':'%s','date_upload':'%s','uploader':'%s','doc_url':'%s','is_use':'%s','file_name':'%s'," \
               "'en_doc_name':%s,'en_owning_organization':%s,'en_description':%s,'en_uploader':%s,'en_file_name':%s}" % (
            self.id, self.doc_number, self.doc_name, self.doc_format, self.owning_organization, self.description, self.date_upload, self.uploader,
             self.doc_url, self.is_use,self.file_name,self.en_doc_name,self.en_owning_organization,self.en_description,self.en_uploader,self.en_file_name)
        return bean.replace("None", '')