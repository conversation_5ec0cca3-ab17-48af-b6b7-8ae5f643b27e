﻿from django.urls import path
from apis.app2.monitor2 import views

urlpatterns = [
    path('station/status/', views.StationStatusView.as_view()),  # 电站状态统计
    path('station/list/', views.StationListView.as_view()),  # 并网点列表
    path('station/signboard/', views.StationSignboardView.as_view()),  # 数据看板
    path('station/chart/', views.StationChartView.as_view()),  # 数据图表
    # 储能单元清单
    path('station/unit_list/', views.StationUnitListView.as_view()),  # 储能单元清单
    path('unit_switch/', views.UnitSwitchSMSCodeCheckView.as_view()),  # 单元控制下发
    path('unit_switch_send_smscode/', views.UnitSwitchSendSmsCodeView.as_view()),  # 单元开关控制下发短信
    path('unit_power/', views.UnitPowerSMSCodeCheckView.as_view()),  # 单元功率下发
    path('unit_power_send_smscode/', views.UnitPowerSendSmsCodeView.as_view()),  # 单元功率下发发送短信
    path('reset_fault/sendsms/', views.ResetFaultSendSmsCodeView.as_view()),  # 故障复位发送短信
    path('reset_fault/', views.ResetFaultSMSCodeCheckView.as_view()),  # 故障复位下发
    path('alarm/detail/', views.AlarmDetailView.as_view()),  # 判断是否显示故障复位
    path('get_unit_running_type_smscode', views.UnitSwitchRunningTypeSendSmsCodeView.as_view()),  # 单元切换运行状态下发短信
    path('switch_unit_running_type', views.UnitSwitchRunningTypeView.as_view()),  # 单元换运行状态
    # 连接跳转
    path('price_power/', views.PricePower.as_view()),  # 单位电价
    path('station/failure/list/', views.StationFault.as_view()),  # 故障列表
    path('station/failure/detail/<str:pk>', views.StationFaultDetail.as_view()),  # 故障详情
    path('station/failure/mappings/', views.StationFaultMapping.as_view()),  # 故障字典
    path('station/notice/', views.StationNoticeView.as_view()),  # 维护须知
    # 遥控
    path('control_list/', views.ControlListView.as_view()),  # 就地控制发列表
    path('control_send_smscode/', views.ControlSendSmsCodeView.as_view()),  # 就地控制发送短信
    path('control_check_smscode/', views.ControlSMSCodeCheckView.as_view()),  # 就地控制下发
    path('power_send_smscode/', views.PowerSendSmsCodeView.as_view()),  # 全站功率下发发送短信
    path('power_check_smscode/', views.PowerSMSCodeCheckView.as_view()),  # 全站功率下发
    path('power_plan_send_smscode/', views.PowerPlanSendSmsCodeView.as_view()),  # 计划下发发送短信
    path('power_plan_check_smscode/', views.PowerPlanSMSCodeCheckView.as_view()),  # 功率计划下发
    path('power_plan_history/', views.PowerPlanHistoryView.as_view()),  # 功率计划下发历史
    path('power_plan_history/edit/', views.PowerPlanHistoryEditView.as_view()),  # 功率计划修改
    path('virtually_send_smscode/', views.VirtuallySendSmsCodeView.as_view()),  # 需量下发发送短信
    path('virtually_check_smscode/', views.VirtuallySMSCodeCheckView.as_view()),  # 需量下发
    path('virtually_check_param/', views.VirtuallyCheckParamView.as_view()),  # 需量下发回显
    # path("user_strategy/add", views.UserStrategyView.as_view()),                                    # 用户自动控制策略：新增
    # path("user_strategy/update/<int:pk>", views.UserStrategyView.as_view()),                        # 用户自动控制策略：修改
    path("user_strategy/list", views.UserStrategyView.as_view()),                                   # 用户自动控制策略：列表
    # path("user_strategy/delete/<int:pk>", views.UserStrategyDeleteView.as_view()),                  # 用户自动控制策略：删除
    # path("user_strategy/check_month/<int:pk>", views.UserStrategyCheckMonthView.as_view()),         # 用户自动控制策略：月份校验
    # path("user_strategy/save/<int:pk>", views.UserStrategySaveToOtherView.as_view()),               # 用户自动控制策略：另存为
    # path("user_strategy/apply", views.UserStrategyApplyView.as_view()),                             # 用户自动控制策略：下发
    # path("user_strategy/current", views.CurrentStrategyView.as_view()),                             # 用户自动控制策略：当前策略
    # path("user_strategy/default", views.DefaultStrategyView.as_view()),                             # 用户自动控制策略：默认策略
    path("user_strategy/realtime", views.RealTimeStationStrategyView.as_view()),                    # 用户自动控制策略：实时策略
    # path("user_strategy/compare", views.CompareStationStrategyView.as_view()),                      # 用户自动控制策略：默认策略比较
    # path("user_strategy/compare/customize", views.CustomizeStationStrategyView.as_view()),          # 用户自动控制策略：自定义策略比较
    # path("user_strategy/customize/list/<int:strategy_id>", views.UserStrategyCustomizeView.as_view()),  # 用户自动控制策略-自定义策略列表
    # path("automatic/sendsms/", views.AutomaticControlSendSmsView.as_view()),  # 自动控制模式发送短信

]

