#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/9/12 上午9:30
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import json
import datetime
import re
import time

from django.db.models import Q, Func, DateTimeField
from django.conf import settings
from decimal import Decimal
import pandas as pd
import io
from apis.user.models import UserDetails
from common.common_response_code import CommonStatusMappings, LoadAnalysisStatusTypes, AnalysisLoadCompMap

from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)

from django.db import connections, transaction
from rest_framework.views import APIView
from rest_framework.response import Response
from common import common_response_code
from apis.web import models
from dbutils.persistent_db import PersistentDB
import pymysql
import logging
from apis.user.models import MaterStation, StationDetails, PeakValleyNew
from tools.minio_tool import MinioTool
from django.db.models import Min, Max, Avg
from apis.statistics_apis.models import UserStrategy, UserStrategyCategoryNew, Month
from apis.user.models import Project
from openpyxl import Workbook, load_workbook
from io import BytesIO
from apis.user.models import CompantInfo
import math
from settings.types_dict import LOAD_REGIONS

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


def can_be_parsed_as_list(json_str):
    """
    判断变量是否可以转换成list
    :param json_str:
    :return:
    """
    try:
        result = json.loads(json_str)
        return isinstance(result, list)
    except json.JSONDecodeError:
        return False


def get_is_have_data(station_id):
    # 针对防逆流电表未采集正向有功电能、负向有功电能的项目，不显示“电量”选项，即没有电量预测功能。
    # 查询dwd_rhyc库 dwd_cumulant_load_data_storage表，epae,enae，查询条件当前两天（包含今天）
    # 为null的值，0值也算有值
    conn = pool.connection()
    cursor = conn.cursor()

    # 获取今天的日期
    today = datetime.datetime.now().date()

    # 获取昨天的日期
    yesterday = today - datetime.timedelta(days=1)
    # 获取昨天的开始日期
    yesterday_start = datetime.datetime.combine(yesterday, datetime.datetime.min.time())
    # 获取今天的结束日期
    today_end = datetime.datetime.combine(today, datetime.datetime.max.time())
    try:
        # 查询站点名称
        station_data = MaterStation.objects.get(id=station_id, is_delete=0)
        if not station_data:
            return False
    except Exception as e:
        error_log.error(e)

    try:
        # 执行SQL查询
        sql = """	SELECT *  
                    FROM {}  
                    WHERE forecast_time BETWEEN '{}' AND '{}'  
                      AND (  
                        (DATE(forecast_time) < DATE(NOW()) AND t_level = 0) OR  
                        (DATE(forecast_time) = DATE(NOW()) AND forecast_time >= NOW() AND t_level = 1) OR  
                        (DATE(forecast_time) > DATE(NOW()) AND t_level = 2)  
                      ) AND compant_code in '{}';""".format(
            'ads_model_response_load_value', station_data.english_name, yesterday_start, today_end
        )
        error_log.error(sql)
        cursor.execute(sql)
        result = cursor.fetchall()
        if result:
            # 有值
            return True
        else:
            return False
    except Exception as e:
        error_log.error(e)
    finally:
        cursor.close()
        conn.close()


def is_over_by_days(begin_time_str, end_time_str, days):
    # 解析日期字符串
    begin_time = datetime.datetime.strptime(begin_time_str, '%Y-%m-%d %H:%M').date()
    end_time = datetime.datetime.strptime(end_time_str, '%Y-%m-%d %H:%M').date()

    # 计算时间差
    delta = (end_time - begin_time).days
    if delta > int(days):
        return True
    return False

def is_more_than_one_days_away(end_time):
    end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M')
    # 获取当前日期
    today = datetime.datetime.now().date()

    # 计算当前日期1天后的日期
    one_days_later = today + datetime.timedelta(days=1)

    # 将 end_time 转换为日期
    end_date = end_time.date()

    # 比较 end_date 是否大于当前日期1天后的日期
    if end_date > one_days_later:
        return True
    else:
        return False


def get_user_role(user_id):
    # 先获取当前登录用户的角色
    user_instance = UserDetails.objects.filter(id=user_id).first()
    user_roles = list(user_instance.roles.all()) if user_instance else []
    if len(user_roles) > 0:
        user_roles = [role.role_name for role in user_roles]
    return user_roles

def mask_string(s):
    # 获取字符串长度
    length = len(s)

    # 处理字符串
    if length < 8:
        # 如果字符串长度不足8位，前四位用 "*" 补齐
        masked_s = '*' * 4 + s[-4:]
    else:
        # 如果字符串长度大于等于8位，展示前四位和后四位，中间部分用 "*" 替代
        masked_s = s[:4] + '*' * (length - 8) + s[-4:]

    return masked_s


def judge_is_date(judge_time, time_format='%Y/%m/%d %H:%M'):
    """
    判断日期格式是否正确
    :param time_format:
    :param judge_time:
    :return:
    """
    try:
        # 尝试将字符串转换为 datetime 对象
        datetime.datetime.strptime(str(judge_time).strip(), time_format)
        return True
    except ValueError as e:
        logging.error(e)
        return False


def str_to_datetime(s):
    """
    将字符串时间转换为 datetime 对象
    """
    return datetime.datetime.strptime(s, '%Y-%m-%d %H:%M:%S')


def datetime_to_str(dt):
    """
    将 datetime 对象转换为字符串
    """
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def find_nearest_key(key, keys):
    """
    找到最近的值
    """
    key_dt = str_to_datetime(key)
    nearest_key = None
    min_diff = float('inf')  # 初始化为无穷大
    for k in keys:
        k_dt = str_to_datetime(k)
        diff = abs((k_dt - key_dt).total_seconds())  # 计算时间差的秒数
        if diff < min_diff:
            min_diff = diff
            nearest_key = k
    return nearest_key



class LoadRegionProvinceView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        获取负荷分析下的省份列表
        :param request:
        :return:
        """
        lang = request.headers.get('lang', 'zh')
        province_name = request.query_params.get('province_name', None)  # 省份名称
        try:
            province_list = LOAD_REGIONS['provinces'][lang]
        except Exception as e:
            error_log.error("负荷分析省份配置不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "省份列表信息不存在." if lang == 'zh' else
                             'Province list information does not exist.'},
                }
            )
        if province_name and province_name != '':
            province_list = [province for province in province_list if province_name in province['name']]

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": province_list,
            },
        })


class LoadAnalysisDataView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        获取负荷分析的数据列表
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        province_id = request.query_params.get("province_id", None)  # 省份id
        compant_name = request.query_params.get("compant_name", None)  # 户名
        start_time = request.query_params.get('start_time', None)  # 开始时间
        end_time = request.query_params.get('end_time', None)  # 结束时间
        compant_code = request.query_params.get('compant_code', None)  # 户号
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size", 20))
        if not province_id or not start_time or not end_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败！" if lang == 'zh' else 'Field verification failed.'},
                }
            )

        if not judge_is_date(start_time, '%Y-%m-%d %H:%M') or not judge_is_date(end_time, '%Y-%m-%d %H:%M'):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "时间格式错误！" if lang == 'zh' else 'Time format error.'},
                }
            )

        if is_over_by_days(start_time, end_time, 7):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "请将时间范围控制在7天以内！" if lang == 'zh' else
                             'Please set the time range within 7 days.'},
                }
            )

        if is_more_than_one_days_away(end_time):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "结束时间不合法！" if lang == 'zh' else 'End time is illegal.'},
                }
            )
        user_id = request.user['user_id']
        user_roles = get_user_role(user_id)
        user_role_type = 0
        if '分析员' in user_roles:
            user_role_type = 1
        compant_codes = []
        sql_str = ''
        compant_codes = self._get_company_codes_by_province(province_id)

        if compant_name or compant_code:
            if compant_code:
                company_data = CompantInfo.objects.filter(compant_code__icontains=compant_code).values_list('compant_code', flat=True)
                compant_codes_new = list(company_data)
                # 将列表转换为集合
                set1 = set(compant_codes)
                set2 = set(compant_codes_new)

                # 计算交集
                intersection = set1.intersection(set2)

                # 将结果转换回列表
                compant_codes = list(intersection)

            if compant_name:
                if lang == 'zh':
                    sql_str = " compant_name like '%{}%'".format(compant_name)
                else:
                    keyword = compant_name
                    for k, v in AnalysisLoadCompMap.items():
                        if compant_name in v[lang]:
                            keyword = k
                            break
                    sql_str = " compant_name like '%{}%'".format(keyword)
        if len(compant_codes) == 0:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": []},
                    "total": 0,
                    "totalpage": 0,
                    "page": page,
                    "page_size": page_size,
                    "user_role_type": user_role_type
                }
            )

        # 获取数据
        data, count = self._execute_custom_sql_query(start_time, end_time, compant_codes, page, page_size, sql_str)
        res_list = []
        for item in data:
            res_list.append(
                {
                    "compant_code": item['compant_code'] if user_role_type else mask_string(item['compant_code']) ,
                    "compant_name": AnalysisLoadCompMap[item['compant_name']][lang] if user_role_type else None,
                    "forecast_time": item['forecast_time'].strftime('%Y-%m-%d'),
                    "forecast_hour_min": item['forecast_time'].strftime('%H:%M'),
                    "status": LoadAnalysisStatusTypes[item['cd_status']][lang],
                    "p_load": round(item['real_value'],1) if item['real_value'] else None,
                    "base_load": round(item['base_value'],1) if item['base_value'] else None,
                    "peak_status": CommonStatusMappings[item['peak_status']][lang],
                    "peak_cap": item['peak_cap'],
                    "valley_status": CommonStatusMappings[item['valley_status']][lang],
                    "valley_cap": item['valley_cap'],
                }
            )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": res_list},
                "total": count,
                "totalpage": math.ceil(count / page_size),
                "page": page,
                "page_size": page_size,
                "user_role_type": user_role_type
            }
        )



    def _execute_custom_sql_query(self, start_time, end_time, compant_codes, page, page_size, sql_str=''):
        offset = page_size * (page - 1)
        in_clause = ', '.join([f"'{code}'" for code in compant_codes])
        sql_pj = ''
        if compant_codes:
            sql_pj += f" AND compant_code IN ({in_clause})"
        if sql_str != '':
            sql_pj+= f" AND {sql_str}"
        sql_query = """	SELECT *  
                            FROM {}  
                            WHERE forecast_time BETWEEN '{}' AND '{}'  
                              AND (  
                                (forecast_time <= NOW() AND t_level = 0) OR  
                                (DATE(forecast_time) = DATE(NOW()) AND forecast_time > NOW() AND t_level = 1) OR  
                                (DATE(forecast_time) > DATE(NOW()) AND t_level = 2)  
                              ) {} ORDER BY compant_code asc, forecast_time asc limit {} OFFSET {};""".format(
            'ads_model_response_load_value', start_time, end_time, sql_pj, page_size, offset
        )

        sql_count_sql = """	SELECT count(1)  
                            FROM {}  
                            WHERE forecast_time BETWEEN '{}' AND '{}'  
                              AND (  
                                (forecast_time <= NOW() AND t_level = 0) OR  
                                (DATE(forecast_time) = DATE(NOW()) AND forecast_time > NOW() AND t_level = 1) OR  
                                (DATE(forecast_time) > DATE(NOW()) AND t_level = 2)  
                              ) {};""".format(
            'ads_model_response_load_value', start_time, end_time, sql_pj)

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                # 获取查询结果
                ads_cursor.execute(sql_query)
                columns = [col[0] for col in ads_cursor.description]
                results = [
                    dict(zip(columns, row))
                    for row in ads_cursor.fetchall()
                ]
                ads_cursor.execute(sql_count_sql)
                count = ads_cursor.fetchone()[0]
                return results, count
            except Exception as e:
                error_log.error(e)
                return False


    def _get_company_codes_by_province(self, province_id):
        # 查询指定省份的所有户号
        projects = Project.objects.filter(is_used=1, province_id=province_id)

        # 收集所有匹配的 company_code
        company_codes = []
        for project in projects:
            # 查找匹配的 CompanyInfo 记录
            company_info = CompantInfo.objects.filter(project_en_name=project.english_name).first()
            if company_info:
                company_codes.append(company_info.compant_code)

        return company_codes


class DownLoadAnalysisDataView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        获取负荷分析的数据下载地址
        :param request:
        :return:
        """
        lang = request.headers.get("lang", "zh")
        province_id = request.query_params.get("province_id", None)  # 省份id
        compant_name = request.query_params.get("compant_name", None)  # 户名
        start_time = request.query_params.get('start_time', None)  # 开始时间
        end_time = request.query_params.get('end_time', None)  # 结束时间
        compant_code = request.query_params.get('compant_code', None)  # 户号
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size", 20))
        if not province_id or not start_time or not end_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败！" if lang == 'zh' else 'Field verification failed.'},
                }
            )

        if not judge_is_date(start_time, '%Y-%m-%d %H:%M') or not judge_is_date(end_time, '%Y-%m-%d %H:%M'):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "时间格式错误！" if lang == 'zh' else 'Time format error.'},
                }
            )

        if is_over_by_days(start_time, end_time, 7):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "请将时间范围控制在7天以内！" if lang == 'zh' else 'Please keep the time range within 7 days.'},
                }
            )

        if is_more_than_one_days_away(end_time):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "结束时间不合法！" if lang == 'zh' else 'End time is illegal.'},
                }
            )
        compant_codes = []
        sql_str = ''

        compant_codes = self._get_company_codes_by_province(province_id)
        if len(compant_codes) == 0:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "该省份下无数据！" if lang == 'zh' else 'There is no data in this province.'},
                }
            )
        if compant_name or compant_code:
            if compant_code:
                company_data = CompantInfo.objects.filter(compant_code__icontains=compant_code).values_list(
                    'compant_code', flat=True)
                compant_codes_new = list(company_data)
                # 将列表转换为集合
                set1 = set(compant_codes)
                set2 = set(compant_codes_new)

                # 计算交集
                intersection = set1.intersection(set2)

                # 将结果转换回列表
                compant_codes = list(intersection)
            if compant_name:
                if lang == 'zh':
                    sql_str = " compant_name like '%{}%'".format(compant_name)
                else:
                    keyword = compant_name
                    for k, v in AnalysisLoadCompMap.items():
                        if compant_name in v[lang]:
                            keyword = k
                            break
                    sql_str = " compant_name like '%{}%'".format(keyword)
        if len(compant_codes) == 0:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "该筛选条件无数据！" if lang == 'zh' else 'There is no data under this filter.'},
                }
            )
        user_id = request.user['user_id']
        user_roles = get_user_role(user_id)
        user_role_type = 0
        if '分析员' in user_roles:
            user_role_type = 1
        # 获取数据
        data = self._execute_custom_sql_query(start_time, end_time, compant_codes, page, page_size, sql_str)
        res_list = []
        if lang == 'zh':
            title_list = ['户号', '日期', '时刻', '储能系统充放策略', '实时负荷kW', '基线负荷kW', '是否有削峰空间',
                          '削峰空间kW', '是否有填谷空间', '填谷空间kW']
        else:
            title_list = ['Account Number', 'Date', 'Time', 'Storage System Charging/Discharging Strategy', 'Real Load (kW)',
                          'Baseline load (kW)', 'Is there a space for peak reduction',
                          'Peak reduction space(kW)', 'Is there a space for valley filling', 'Valley filling space (kW)']
        if user_role_type == 1:
            if lang == 'zh':
                title_list.insert(0, '户名')
            else:
                title_list.insert(0, 'Account Name')
        data_list = []
        data_list.append(title_list)
        for item in data:
            compant_code = item['compant_code'] if user_role_type == 1 else mask_string(item['compant_code'])
            item_list=[compant_code, item['forecast_time'].strftime('%Y-%m-%d'),
                              item['forecast_time'].strftime('%H:%M'), LoadAnalysisStatusTypes[item['cd_status']][lang], item['real_value'],
                              item['base_value'], CommonStatusMappings[item['peak_status']][lang], item['peak_cap'],
                              CommonStatusMappings[item['valley_status']][lang], item['valley_cap']]
            if user_role_type == 1:
                item_list.insert(0, AnalysisLoadCompMap[item['compant_name']][lang])
            data_list.append(item_list)
        # 创建一个BytesIO对象，用于保存Excel文件的二进制数据
        excel_buffer = io.BytesIO()

        # 明确指定列名列表
        column_names = data_list[0]

        # 使用 data_list[2:] 作为数据，跳过第一行和第二行
        df = pd.DataFrame(data_list[1:], columns=column_names)

        # 将DataFrame写入Excel文件
        df.to_excel(excel_buffer, index=False)

        # 将BytesIO对象的位置重置到开始，以便从头读取数据
        excel_buffer.seek(0)

        # 加载Excel工作簿
        wb = load_workbook(filename=excel_buffer)

        # 获取活动工作表
        ws = wb.active

        # 将修改后的Excel文件写回BytesIO对象
        excel_buffer = BytesIO()
        wb.save(excel_buffer)

        # 将BytesIO对象的位置重置到开始，以便从头读取数据
        excel_buffer.seek(0)

        # 将二进制数据转换为字节
        binary_data = excel_buffer.read()
        file_size = len(binary_data)
        if file_size > 100 * 1024 * 1024:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "文件过大，请减少并网点或缩短时间范围" if lang == 'zh' else
                             'The file is too large, please reduce the number of points or shorten the time range'},
                }
            )

        # 定义存储桶名称和对象名称（即文件名）
        bucket_name = 'download'

        if lang == 'zh':
            object_name = (str(start_time) + "到" + str(end_time) + "负荷分析数据" + str(time.time()) + ".xlsx")
        else:
            object_name = (str(start_time) + "~" + str(end_time) + "_LoadAnalysisData_" + str(time.time()) + ".xlsx")

        # 调用upload_file方法上传Excel文件
        minio_client = MinioTool()
        storage_url = minio_client.upload_file(binary_data, bucket_name, object_name)
        storage_url = storage_url.split('?', 1)[0]
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": storage_url},
            }
        )


    def _execute_custom_sql_query(self, start_time, end_time, compant_codes, page, page_size, sql_str=''):
        in_clause = ', '.join([f"'{code}'" for code in compant_codes])
        sql_pj = ''
        if compant_codes:
            sql_pj += f" AND compant_code IN ({in_clause})"
        if sql_str != '':
            sql_pj+= f" AND {sql_str}"
        sql_query = """	SELECT *  
                            FROM {}  
                            WHERE forecast_time BETWEEN '{}' AND '{}'  
                              AND (  
                                (forecast_time <= NOW() AND t_level = 0) OR  
                                (DATE(forecast_time) = DATE(NOW()) AND forecast_time > NOW() AND t_level = 1) OR  
                                (DATE(forecast_time) > DATE(NOW()) AND t_level = 2)  
                              ) {} ORDER BY compant_code asc, forecast_time asc""".format(
            'ads_model_response_load_value', start_time, end_time, sql_pj
        )

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                # 获取查询结果
                ads_cursor.execute(sql_query)
                columns = [col[0] for col in ads_cursor.description]
                results = [
                    dict(zip(columns, row))
                    for row in ads_cursor.fetchall()
                ]
                return results
            except Exception as e:
                error_log.error(e)
                return False


    def _get_company_codes_by_province(self, province_id):
        # 查询指定省份的所有户号
        projects = Project.objects.filter(is_used=1, province_id=province_id)

        # 收集所有匹配的 company_code
        company_codes = []
        for project in projects:
            # 查找匹配的 CompanyInfo 记录
            company_info = CompantInfo.objects.filter(project_en_name=project.english_name).first()
            if company_info:
                company_codes.append(company_info.compant_code)

        return company_codes
