from rest_framework import serializers


class WebPowerPlanHistorySerializer(serializers.Serializer):
    project = serializers.CharField(required=True)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    type = serializers.Char<PERSON>ield(required=False, max_length=32)


class WebSocSerializer(serializers.Serializer):
    project = serializers.CharField(required=True)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    type = serializers.Char<PERSON>ield(required=False, max_length=32)


class WebPowerSerializer(serializers.Serializer):
    project = serializers.CharField(required=False)
    time = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=False)
    type = serializers.Char<PERSON>ield(required=False, max_length=32)
