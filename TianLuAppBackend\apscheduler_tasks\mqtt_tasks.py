import json

import datetime

import logging
import paho.mqtt.client as mqtt
from apis.user.models import StationDetails
from django_redis import get_redis_connection
from TianLuAppBackend import settings


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    conn = get_redis_connection("3")
    topic = msg.topic.split('/')
    months = eval(msg.payload).get('body')[0].get('body') if eval(msg.payload).get('body') else None
    if months:
        month = list(months.keys())[0].split('H')
        month = list(months.keys())[0][1:2] if len(month[0]) == 2 else list(months.keys())[0][1:3]
        conn.set('{}-{}-mqtt'.format(topic[-2], month), msg.payload)
    conn.set('cs','3333')
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")

def on_disconnect(mqtt_client, userdata, rc):
    print(f"关闭mqtt连接")

def mtqq_station_strategy_task():
    # 循环更新并网点当前策略
    station_res = StationDetails.objects.filter(is_delete=0).all()
    month = int(datetime.datetime.now().month)
    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
    client.connect(
        host=settings.MQTT_SERVER,
        port=settings.MQTT_PORT,
        keepalive=settings.MQTT_KEEPALIVE,
    )
    for station_instance in station_res:
        station_app = station_instance.app
        station_english_name = station_instance.english_name

        temp_list = list()
        for i in range(0, 48):
            temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'F')
            temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'P')

        import time
        now_time = str(int(time.time()))

        message = {
            "time": now_time,
            "body": [
                {
                    "device": "EMS",
                    "datatype": "measure",
                    "totalcall": "0",
                    "body": temp_list
                }
            ]
        }

        req_topic = f"req/database/realtime/{station_english_name}/{station_app}"
        res_topic = f"res/database/realtime/{station_english_name}/{station_app}"
        json_message = json.dumps(message)
        client.loop_start()
        # 发布
        client.publish(req_topic, json_message)
        # 获取订阅结果
        client.subscribe(res_topic)
        logging.warning(f'{station_instance.station_name}--执行完成')
        client.loop_stop()

    client.disconnect()
    return client