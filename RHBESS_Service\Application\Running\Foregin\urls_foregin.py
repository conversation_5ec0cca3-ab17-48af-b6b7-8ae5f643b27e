#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_Service\Application\Running\Foregin\urls_foregin.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-16 16:17:50


# -*- coding=utf-8 -*-
from tornado.routing import Rule, PathMatches
from tornado.web import url

from Application.EqAccount.Foreign.zhilRealHandle import ZhiLRealHandleIntetface
from Application.EqAccount.Foreign.zhilHisHandle import ZhiLHisHandleIntetface
from Application.EqAccount.Foreign.zhilBmsHandle import ZhiLBmsHandleIntetface


routes = [
    # 智锂物联所需接口
    url(r"/RealData/(\w+)", ZhiLRealHandleIntetface),
    url(r"/HisData/(\w+)", ZhiLHisHandleIntetface),
    url(r"/BmsData/(\w+)", ZhiLBmsHandleIntetface),
      

]
