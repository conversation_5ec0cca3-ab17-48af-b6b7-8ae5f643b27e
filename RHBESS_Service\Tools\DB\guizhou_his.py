#!/usr/bin/env python
# coding=utf-8
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")

GUIZHOU_HOSTNAME = model_config.get('mysql', "GUIZHOU_HOSTNAME")
GUIZHOU_PORT = model_config.get('mysql', "GUIZHOU_PORT")

GUIZHOU_HOSTNAMES = model_config.get('mysql', "GUIZHOU_HOSTNAMES")
GUIZHOU_PORTS = model_config.get('mysql', "GUIZHOU_PORTS")

SGUIZHOU1_DATABASE = model_config.get('mysql', "SGUIZHOU1_DATABASE")
SGUIZHOU2_DATABASE = model_config.get('mysql', "SGUIZHOU2_DATABASE")
SGUIZHOU3_DATABASE = model_config.get('mysql', "SGUIZHOU3_DATABASE")
SGUIZHOU4_DATABASE = model_config.get('mysql', "SGUIZHOU4_DATABASE")
SGUIZHOU5_DATABASE = model_config.get('mysql', "SGUIZHOU5_DATABASE")
SGUIZHOU6_DATABASE = model_config.get('mysql', "SGUIZHOU6_DATABASE")
SGUIZHOU7_DATABASE = model_config.get('mysql', "SGUIZHOU7_DATABASE")
SGUIZHOU8_DATABASE = model_config.get('mysql', "SGUIZHOU8_DATABASE")

GUIZHOU1_DATABASE = model_config.get('mysql', "GUIZHOU1_DATABASE")
GUIZHOU2_DATABASE = model_config.get('mysql', "GUIZHOU2_DATABASE")
GUIZHOU3_DATABASE = model_config.get('mysql', "GUIZHOU3_DATABASE")
GUIZHOU4_DATABASE = model_config.get('mysql', "GUIZHOU4_DATABASE")
GUIZHOU5_DATABASE = model_config.get('mysql', "GUIZHOU5_DATABASE")
GUIZHOU6_DATABASE = model_config.get('mysql', "GUIZHOU6_DATABASE")
GUIZHOU7_DATABASE = model_config.get('mysql', "GUIZHOU7_DATABASE")
GUIZHOU8_DATABASE = model_config.get('mysql', "GUIZHOU8_DATABASE")

GUIZHOU_USERNAME = model_config.get('mysql', "GUIZHOU_USERNAME")
GUIZHOU_PASSWORD = model_config.get('mysql', "GUIZHOU_PASSWORD")

GUIZHOU_USERNAMES = model_config.get('mysql', "GUIZHOU_USERNAMES")
GUIZHOU_PASSWORDS = model_config.get('mysql', "GUIZHOU_PASSWORDS")


shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU1_DATABASE
)
sguizhou1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou1_session = scoped_session(sessionmaker(sguizhou1_engine,autoflush=True))
sguizhou1_Base = declarative_base(sguizhou1_engine)
sguizhou1_session = _sguizhou1_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU2_DATABASE
)
sguizhou2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou2_session = scoped_session(sessionmaker(sguizhou2_engine,autoflush=True))
sguizhou2_Base = declarative_base(sguizhou2_engine)
sguizhou2_session = _sguizhou2_session()



shisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU3_DATABASE
)
sguizhou3_engine = create_engine(shisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou3_session = scoped_session(sessionmaker(sguizhou3_engine,autoflush=True))
sguizhou3_Base = declarative_base(sguizhou3_engine)
sguizhou3_session = _sguizhou3_session()


shisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU4_DATABASE
)
sguizhou4_engine = create_engine(shisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou4_session = scoped_session(sessionmaker(sguizhou4_engine,autoflush=True))
sguizhou4_Base = declarative_base(sguizhou4_engine)
sguizhou4_session = _sguizhou4_session()


shisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU5_DATABASE
)
sguizhou5_engine = create_engine(shisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou5_session = scoped_session(sessionmaker(sguizhou5_engine,autoflush=True))
sguizhou5_Base = declarative_base(sguizhou5_engine)
sguizhou5_session = _sguizhou5_session()

shisdb6_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU6_DATABASE
)
sguizhou6_engine = create_engine(shisdb6_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou6_session = scoped_session(sessionmaker(sguizhou6_engine,autoflush=True))
sguizhou6_Base = declarative_base(sguizhou6_engine)
sguizhou6_session = _sguizhou6_session()

shisdb7_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU7_DATABASE
)
sguizhou7_engine = create_engine(shisdb7_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou7_session = scoped_session(sessionmaker(sguizhou7_engine,autoflush=True))
sguizhou7_Base = declarative_base(sguizhou7_engine)
sguizhou7_session = _sguizhou7_session()

shisdb8_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAMES,
    GUIZHOU_PASSWORDS,
    GUIZHOU_HOSTNAMES,
    GUIZHOU_PORTS,
    SGUIZHOU8_DATABASE
)
sguizhou8_engine = create_engine(shisdb8_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sguizhou8_session = scoped_session(sessionmaker(sguizhou8_engine,autoflush=True))
sguizhou8_Base = declarative_base(sguizhou8_engine)
sguizhou8_session = _sguizhou8_session()



hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU1_DATABASE
)
guizhou1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou1_session = scoped_session(sessionmaker(guizhou1_engine,autoflush=True))
guizhou1_Base = declarative_base(guizhou1_engine)
guizhou1_session = _guizhou1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU2_DATABASE
)
guizhou2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou2_session = scoped_session(sessionmaker(guizhou2_engine,autoflush=True))
guizhou2_Base = declarative_base(guizhou2_engine)
guizhou2_session = _guizhou2_session()



hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU3_DATABASE
)
guizhou3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou3_session = scoped_session(sessionmaker(guizhou3_engine,autoflush=True))
guizhou3_Base = declarative_base(guizhou3_engine)
guizhou3_session = _guizhou3_session()


hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU4_DATABASE
)
guizhou4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou4_session = scoped_session(sessionmaker(guizhou4_engine,autoflush=True))
guizhou4_Base = declarative_base(guizhou4_engine)
guizhou4_session = _guizhou4_session()


hisdb5_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU5_DATABASE
)
guizhou5_engine = create_engine(hisdb5_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou5_session = scoped_session(sessionmaker(guizhou5_engine,autoflush=True))
guizhou5_Base = declarative_base(guizhou5_engine)
guizhou5_session = _guizhou5_session()

hisdb6_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU6_DATABASE
)
guizhou6_engine = create_engine(hisdb6_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou6_session = scoped_session(sessionmaker(guizhou6_engine,autoflush=True))
guizhou6_Base = declarative_base(guizhou6_engine)
guizhou6_session = _guizhou6_session()

hisdb7_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU7_DATABASE
)
guizhou7_engine = create_engine(hisdb7_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou7_session = scoped_session(sessionmaker(guizhou7_engine,autoflush=True))
guizhou7_Base = declarative_base(guizhou7_engine)
guizhou7_session = _guizhou7_session()

hisdb8_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    GUIZHOU_USERNAME,
    GUIZHOU_PASSWORD,
    GUIZHOU_HOSTNAME,
    GUIZHOU_PORT,
    GUIZHOU8_DATABASE
)
guizhou8_engine = create_engine(hisdb8_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_guizhou8_session = scoped_session(sessionmaker(guizhou8_engine,autoflush=True))
guizhou8_Base = declarative_base(guizhou8_engine)
guizhou8_session = _guizhou8_session()









