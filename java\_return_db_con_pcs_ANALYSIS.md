# `_return_db_con_pcs`方法详细解析

## 方法签名
```python
def _return_db_con_pcs(self, db, d):
    '''返回数据库链接'''
```

## 核心功能
根据电站名称(`db`)和设备标识(`d`)，返回对应的数据库连接对象。这是一个**数据库连接路由器**。

## 数据库配置结构 (`db_`)
```python
db_ = {
    "his": [[his_engine], [his_session], [HIS_DATABASE], None],
    'halun': [[shalun_session], [halun_session], [0]],
    "binhai": [[sbinhai1_session, sbinhai2_session], [binhai1_session, binhai2_session], [0, 1]],
    "taicang": [[staicang_session], [taicang_session], [0]],
    "ygzhen": [[sygzhen1_session, sygzhen2_session], [ygzhen1_session, ygzhen2_session], [0, 1]],
    "zgtian": [[szgtian1_session, szgtian2_session, szgtian3_session, szgtian4_session, smzgtian_session], 
               [zgtian1_session, zgtian2_session, zgtian3_session, zgtian4_session, mzgtian_session], 
               [0, 1, 2, 3, 4]],
    "dongmu": [[mqtt_session], [dongmu_session], [0]],
    "baodian": [[sbaodian1_session, sbaodian2_session, sbaodian3_session, sbaodian4_session, sbaodian5_session], 
                [baodian1_session, baodian2_session, baodian3_session, baodian4_session, baodian5_session], 
                [0, 1, 2, 3, 4]],
    "houma": [[shoumaa1_session, shoumaa2_session, shoumab1_session, shoumab2_session], 
              [houmaa1_session, houmaa2_session, houmab1_session, houmab2_session], 
              [0, 1, 2, 3]],
    "datong": [[sdatong1_session, sdatong2_session, sdatong3_session, sdatong4_session], 
               [datong1_session, datong2_session, datong3_session, datong4_session], 
               [0, 1, 2, 3]],
    "guizhou": [[sguizhou1_session, sguizhou2_session, sguizhou3_session, sguizhou4_session, sguizhou5_session, sguizhou6_session, sguizhou7_session, sguizhou8_session], 
                [guizhou1_session, guizhou2_session, guizhou3_session, guizhou4_session, guizhou5_session, guizhou6_session, guizhou7_session, guizhou8_session], 
                []],
    "ygqn": [[sygqn7_session, sygqn8_session], [ygqn7_session, ygqn8_session], []],
    "shgyu": [[sshgyu_session], [shgyu_session], []],
    'taicgxr': [[tcxr_history_session, tcxr1_history_session, tcxr2_history_session, tcxr3_history_session, tcxr4_history_session]],
    'tczj': [[tczj_history_session, tczj1_history_session]], 
    'sikly': [[sikly_history_session, sikly1_history_session]]
}
```

### 数组结构说明：
- `db_[station][0]` - SCADA系统数据库连接列表
- `db_[station][1]` - 历史数据库连接列表  
- `db_[station][2]` - 数据库索引列表

## 逻辑分支详解

### 1. **his电站** (历史数据库)
```python
if db == 'his':
    conn = db_[db][1][0]  # 返回历史数据库的第一个连接
```

### 2. **简单单连接电站**
```python
elif db == 'dongmu' or db == 'taicang' or db == 'halun':
    conn = db_[db][1][0]  # 返回历史数据库的第一个连接
```

### 3. **houma电站** (特殊设备标识路由)
```python
elif db == 'houma':
    if d == 'A1':
        conn = db_[db][1][0]  # houmaa1_session
    elif d == 'A2':
        conn = db_[db][1][1]  # houmaa2_session
    elif d == 'B1':
        conn = db_[db][1][2]  # houmab1_session
    elif d == 'B2':
        conn = db_[db][1][3]  # houmab2_session
```

### 4. **taicgxr电站** (SCADA系统)
```python
elif db == 'taicgxr':
    conn = db_[db][0][0]  # 返回SCADA系统的第一个连接
```

### 5. **tczj和sikly电站** (索引路由)
```python
elif db in ['tczj', 'sikly']:
    conn = db_[db][0][d]  # 根据d索引选择SCADA连接
```

### 6. **其他多连接电站** (数字索引路由)
```python
else:
    conn = db_[db][1][int(d) - 1]  # 根据d-1索引选择历史数据库连接
```

## 电站类型分类

### 🔹 **单数据库电站**
- `his`, `dongmu`, `taicang`, `halun` - 只有一个数据库连接

### 🔹 **多数据库电站**
- `binhai` (2个数据库)
- `ygzhen` (2个数据库) 
- `zgtian` (5个数据库)
- `baodian` (5个数据库)
- `houma` (4个数据库，特殊A1/A2/B1/B2路由)
- `datong` (4个数据库)
- `guizhou` (8个数据库)

### 🔹 **特殊路由电站**
- `taicgxr` - 使用SCADA系统连接 `db_[db][0][0]`
- `tczj`, `sikly` - 使用SCADA系统，根据d索引路由
- `houma` - 使用字符串标识(A1/A2/B1/B2)路由

## Java转换实现

```java
@Component
public class DatabaseConnectionRouter {
    
    // 数据库连接配置映射
    private Map<String, DatabaseConfig> dbConfigs;
    
    /**
     * 严格对应Python中的_return_db_con_pcs方法
     * 
     * @param db 电站名称
     * @param d 设备标识
     * @return 数据库连接
     */
    public DataSource getDbConnection(String db, String d) {
        DataSource conn = null;
        
        if ("his".equals(db)) {
            conn = dbConfigs.get(db).getHistoryConnections().get(0);
        } else if ("dongmu".equals(db) || "taicang".equals(db) || "halun".equals(db)) {
            conn = dbConfigs.get(db).getHistoryConnections().get(0);
        } else if ("houma".equals(db)) {
            switch (d) {
                case "A1":
                    conn = dbConfigs.get(db).getHistoryConnections().get(0);
                    break;
                case "A2":
                    conn = dbConfigs.get(db).getHistoryConnections().get(1);
                    break;
                case "B1":
                    conn = dbConfigs.get(db).getHistoryConnections().get(2);
                    break;
                case "B2":
                    conn = dbConfigs.get(db).getHistoryConnections().get(3);
                    break;
            }
        } else if ("taicgxr".equals(db)) {
            conn = dbConfigs.get(db).getScadaConnections().get(0);
        } else if ("tczj".equals(db) || "sikly".equals(db)) {
            int index = Integer.parseInt(d);
            conn = dbConfigs.get(db).getScadaConnections().get(index);
        } else {
            // 其他多连接电站
            int index = Integer.parseInt(d) - 1;
            conn = dbConfigs.get(db).getHistoryConnections().get(index);
        }
        
        return conn;
    }
}

@Data
public class DatabaseConfig {
    private List<DataSource> scadaConnections;     // 对应db_[station][0]
    private List<DataSource> historyConnections;   // 对应db_[station][1]
    private List<Integer> indexes;                  // 对应db_[station][2]
}
```

## 使用场景

这个方法主要用于：
1. **历史数据查询** - 根据电站和设备选择正确的数据库连接
2. **多数据库路由** - 大型电站有多个数据库实例，需要路由到正确的实例
3. **设备分组** - 不同设备的数据可能存储在不同的数据库中
4. **负载均衡** - 将查询分散到不同的数据库实例

## 关键特点

✅ **电站特异性** - 每个电站有自己的数据库配置  
✅ **设备路由** - 根据设备标识选择数据库  
✅ **多实例支持** - 支持单个电站的多数据库实例  
✅ **特殊处理** - 为特定电站提供定制化路由逻辑  

这个方法是整个历史数据查询系统的**数据库连接核心**，确保每个查询都能连接到正确的数据库实例！
