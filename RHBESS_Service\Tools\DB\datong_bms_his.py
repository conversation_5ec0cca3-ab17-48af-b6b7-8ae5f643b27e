#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql




DATONG_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
DATONG_PORT = model_config.get('mysql', "HIS_PORT")
DATONG_DATABASE1 =  model_config.get('mysql', "BDATONG1_DATABASE")
DATONG_DATABASE2 =  model_config.get('mysql', "BDATONG2_DATABASE")
DATONG_DATABASE3 =  model_config.get('mysql', "BDATONG3_DATABASE")
DATONG_DATABASE4 =  model_config.get('mysql', "BDATONG4_DATABASE")
DATONG_USERNAME = model_config.get('mysql', "HIS_USERNAME")
DATONG_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")



hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG_DATABASE1
)
datong1_g_bms_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong1_g_bms_session = sessionmaker(datong1_g_bms_engine,autoflush=True)
datong1_g_bms_Base = declarative_base(datong1_g_bms_engine)
datong1_g_bms_session = _datong1_g_bms_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG_DATABASE2
)
datong2_g_bms_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong2_g_bms_session = sessionmaker(datong2_g_bms_engine,autoflush=True)
datong2_g_bms_Base = declarative_base(datong2_g_bms_engine)
datong2_g_bms_session = _datong2_g_bms_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
   DATONG_USERNAME,
   DATONG_PASSWORD,
   DATONG_HOSTNAME,
   DATONG_PORT,
   DATONG_DATABASE3
)
datong3_g_bms_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong3_g_bms_session = sessionmaker(datong3_g_bms_engine,autoflush=True)
datong3_g_bms_Base = declarative_base(datong3_g_bms_engine)
datong3_g_bms_session = _datong3_g_bms_session()


hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG_DATABASE4
)
datong4_g_bms_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong4_g_bms_session = sessionmaker(datong4_g_bms_engine,autoflush=True)
datong4_g_bms_Base = declarative_base(datong4_g_bms_engine)
datong4_g_bms_session = _datong4_g_bms_session()


