package com.robestec.dailyproduce.telecontrol.vo;

import lombok.Data;

/**
 * 通用响应结果VO
 */
@Data
public class CommonResultVO<T> {
    private Integer code;
    private String msg;
    private T data;

    public static <T> CommonResultVO<T> success(T data) {
        CommonResultVO<T> result = new CommonResultVO<>();
        result.setCode(200);
        result.setMsg("操作成功");
        result.setData(data);
        return result;
    }

    public static <T> CommonResultVO<T> success(String msg, T data) {
        CommonResultVO<T> result = new CommonResultVO<>();
        result.setCode(200);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    public static <T> CommonResultVO<T> error(String msg) {
        CommonResultVO<T> result = new CommonResultVO<>();
        result.setCode(500);
        result.setMsg(msg);
        return result;
    }

    public static <T> CommonResultVO<T> error(Integer code, String msg) {
        CommonResultVO<T> result = new CommonResultVO<>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
} 