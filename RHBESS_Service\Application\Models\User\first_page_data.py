#!/usr/bin/env python
# coding=utf-8
#@Information: 首页所属数据冻结表
#<AUTHOR> WYJ
#@Date         : 2022-08-10 14:28:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report_f.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-30 11:52:43

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class FirstPageDatas(user_Base):
    u'报表冻结数据'
    __tablename__ = "f_first_page_data"
    name = Column(VARCHAR(250), nullable=False,primary_key=True,comment=u"名称")
    value = Column(VARCHAR(256), nullable=True,comment=u"数值")
    op_ts = Column(DateTime, nullable=False,primary_key=True,comment=u"录入时间")
    cause = Column(CHAR(2), nullable=False,comment=u"1：日数据2月数据")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'name':'%s','value':'%s','op_ts':'%s','cause':%s}" % (
            self.name,self.value,self.op_ts,self.cause)
        
   