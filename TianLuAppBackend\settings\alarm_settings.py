# alarm_detail = {
#     "Online":  {"detail": "设备在线状态", "type": 1, 0: "离线", 1: "在线"},
#     # 2.0 EMS
#     "TransfiniteFault": {"detail": "变压器超限故障", "type": 1, 0: "已恢复", 1: "未恢复警告", "is_send_message": 1},
#     "MCGSA": {"detail": "组态自动化运行模式", "type": 3, 0: "停止", 1: "运行"},
#     "MCGSP": {"detail": "组态计划调度模式", "type": 3, 0: "停止", 1: "运行"},
#     "AEn": {"detail": "功能策略启停", "type": 3, 0: "停止", 1: "运行"},
#     "AEnC": {"detail": "接收云端调度", "type": 3, 0: "停止", 1: "运行"},
#     "PStse": {"detail": "通道3 虚拟点 PCS停机状态", "type": 3, 0: "停止", 1: "运行"},
#     "PChaon": {"detail": "通道3 虚拟点 PCS充电运行", "type": 3, 0: "停止", 1: "运行"},
#     "PDison": {"detail": "通道3 虚拟点 PCS放电运行", "type": 3, 0: "停止", 1: "运行"},
#     "PRun": {"detail": "通道3 虚拟点 PCS运行", "type": 3, 0: "停止", 1: "运行"},
#     "VEquse": {"detail": "通道3 虚拟点 设备状态", "type": 3, 0: "停止", 1: "运行"},
#     "LFZT": {"detail": "组态自动化运行负荷跟随", "type": 3, 0: "停止", 1: "运行"},
#     "LFZTP": {"detail": "组态计划运行负荷跟随", "type": 3, 0: "停止", 1: "运行"},
#     "LF": {"detail": "云端负荷跟随调度", "type": 3, 0: "停止", 1: "运行"},
#     "PStan": {"detail": "通道3 虚拟点 PCS待机状态", "type": 3, 0: "停止", 1: "运行"},
#     "ZPOn": {"detail": "通道3 虚拟点 PCS零功率运行", "type": 3, 0: "停止", 1: "运行"},
#     "CloudAuto": {"detail": "云端自动化模式", "type": 3, 0: "停止", 1: "运行"},
#     "FBIOS": {"detail": "网关机电池故障", "type": 1, 0: "无故障", 1: "有故障", "is_send_email": 1, "is_send_message": 1},
#     "ARFF": {"detail": "防逆流电表通讯故障", "type": 1, 1: "无故障", 0: "有故障", "is_send_message": 1},
#
#
#     # 2.0 PCS（含部分3.0）
#     "Ase1": {"detail": "告警字储能电池极性反接故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase2": {"detail": "告警字储能电池电压异常", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase3": {"detail": "告警字直流侧半母线硬件过压", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase4": {"detail": "告警字交流硬件过流", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase5": {"detail": "告警字IGBT A 相故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase6": {"detail": "告警字IGBT B 相故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase7": {"detail": "告警字IGBT C 相故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase26": {"detail": "告警字直流开关故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase27": {"detail": "告警字IGBT 风机故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase31": {"detail": "告警字半母线电压偏差过大", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase40": {"detail": "告警字输出缺相故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase48": {"detail": "告警字IGBT 模块 A 相过温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase49": {"detail": "告警字IGBT 模块 B 相过温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase50": {"detail": "告警字IGBT 模块 C 相过温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase51": {"detail": "告警字IGBT 模块 A 相低温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase52": {"detail": "告警字IGBT 模块 B 相低温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase53": {"detail": "告警字IGBT 模块 C 相低温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase57": {"detail": "告警字绝缘电阻偏低", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase60": {"detail": "告警字BMS 通讯故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase79": {"detail": "告警字EMS 通讯故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase106": {"detail": "告警字BMS 系统故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase110": {"detail": "告警字急停告警", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "Ase41": {"detail": "告警字防雷器故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase58": {"detail": "告警字绝缘检测失败", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase62": {"detail": "告警字并机通讯故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase63": {"detail": "告警字EEPROM 故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase64": {"detail": "告警字SPI 故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase69": {"detail": "告警字CBC 过流", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase70": {"detail": "告警字设备过载", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase71": {"detail": "告警字电网电压反序", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase72": {"detail": "告警字母线软启动失败", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase73": {"detail": "告警字交流侧脱扣器故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase74": {"detail": "告警字交流侧开关故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase75": {"detail": "告警字载波同步故障", "type": 2, 0: "已恢复", 1: "未恢复报警"},
#     "Ase29": {"detail": "告警字直流侧全母线软件过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase30": {"detail": "告警字直流侧全母线软件欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase33": {"detail": "告警字电网交流过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase34": {"detail": "告警字电网交流欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase35": {"detail": "告警字离网交流过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase36": {"detail": "告警字离网交流欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase37": {"detail": "告警字电网交流过频", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase38": {"detail": "告警字电网交流欠频", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase46": {"detail": "告警字环境过温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase47": {"detail": "告警字环境低温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase96": {"detail": "告警字电池电压过高", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase97": {"detail": "告警字电池电压过低", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase109": {"detail": "告警字电池欠压告警", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase8": {"detail": "告警字交流侧 A 相霍尔断线（+15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase9": {"detail": "告警字交流侧 A 相霍尔断线（-15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase10": {"detail": "告警字交流侧 A 相霍尔断线（IR）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase11": {"detail": "告警字交流侧 A 相霍尔断线（GND）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase12": {"detail": "告警字交流侧 B 相电流霍尔断线（+15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase13": {"detail": "告警字交流侧 B 相霍尔断线（-15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase14": {"detail": "告警字交流侧 B 相霍尔断线（IR）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase15": {"detail": "告警字交流侧 B 相霍尔断线（GND）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase16": {"detail": "告警字交流侧 C 相电流霍尔断线（+15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase17": {"detail": "告警字交流侧 C 相霍尔断线（-15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase18": {"detail": "告警字交流侧 C 相霍尔断线（IR）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase19": {"detail": "告警字交流侧 C 相霍尔断线（GND）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase20": {"detail": "告警字直流侧电流霍尔断线（+15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase21": {"detail": "告警字直流侧霍尔断线（-15V）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase22": {"detail": "告警字直流侧霍尔断线（IR）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase23": {"detail": "告警字直流侧霍尔断线（GND）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase43": {"detail": "告警字电流控制偏差过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase44": {"detail": "告警字电网电压不平衡", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase45": {"detail": "告警字交流侧电流直流分量超限", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase54": {"detail": "告警字交流开关合闸电压不匹配", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase56": {"detail": "告警字AD 零漂过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase59": {"detail": "告警字HMI 通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase61": {"detail": "告警字EMS 连接超时 1（外部总线）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase65": {"detail": "告警字物联网通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase66": {"detail": "告警字客户后台通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase76": {"detail": "告警字EMS 连接超时 1（内部通讯）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase77": {"detail": "告警字EMS 连接超时 2（外部总线）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase78": {"detail": "告警字EMS 连接超时 2（内部通讯）", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase80": {"detail": "告警字产品版本号不匹配", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase81": {"detail": "告警字检测板型号不匹配", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase87": {"detail": "告警字RTC 故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase99": {"detail": "告警字黑匣子存储故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase100": {"detail": "告警字电源盒检测异常", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase101": {"detail": "告警字主机异常停机", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase103": {"detail": "告警字锁相异常", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase107": {"detail": "告警字直流侧中点采样断线", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Ase108": {"detail": "告警字不满足并网运行条件", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "Fault": {"detail": " PCS 故障状态 ", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#
#     "Fse1": {"detail": "故障字储能电池极性反接故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse2": {"detail": "故障字储能电池电压异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse3": {"detail": "故障字直流侧半母线硬件过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse4": {"detail": "故障字交流硬件过流", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse5": {"detail": "故障字IGBT A 相故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse6": {"detail": "故障字IGBT B 相故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse7": {"detail": "故障字IGBT C 相故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse26": {"detail": "故障字直流开关故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse27": {"detail": "故障字IGBT 风机故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse29": {"detail": "故障字直流侧全母线软件过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse30": {"detail": "故障字直流侧全母线软件欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse31": {"detail": "故障字半母线电压偏差过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse33": {"detail": "故障字电网交流过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse34": {"detail": "故障字电网交流欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse35": {"detail": "故障字离网交流过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse36": {"detail": "故障字离网交流欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse37": {"detail": "故障字电网交流过频", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse38": {"detail": "故障字电网交流欠频", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse40": {"detail": "故障字输出缺相故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse46": {"detail": "故障字环境过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse47": {"detail": "故障字环境低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse48": {"detail": "故障字IGBT 模块 A 相过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse49": {"detail": "故障字IGBT 模块 B 相过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse50": {"detail": "故障字IGBT 模块 C 相过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse51": {"detail": "故障字IGBT 模块 A 相低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse52": {"detail": "故障字IGBT 模块 B 相低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse53": {"detail": "故障字IGBT 模块 C 相低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse97": {"detail": "故障字电池电压过低", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse106": {"detail": "故障字BMS 系统故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse8": {"detail": "故障字交流侧 A 相霍尔断线（+15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse9": {"detail": "故障字交流侧 A 相霍尔断线（-15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse10": {"detail": "故障字交流侧 A 相霍尔断线（IR）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse11": {"detail": "故障字交流侧 A 相霍尔断线（GND）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse12": {"detail": "故障字交流侧 B 相电流霍尔断线（+15V）", "type": 1, 0: "已恢复", 1: "未恢复故障",
#               "is_send_email": 1},
#     "Fse13": {"detail": "故障字交流侧 B 相霍尔断线（-15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse14": {"detail": "故障字交流侧 B 相霍尔断线（IR）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse15": {"detail": "故障字交流侧 B 相霍尔断线（GND）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse16": {"detail": "故障字交流侧 C 相电流霍尔断线（+15V）", "type": 1, 0: "已恢复", 1: "未恢复故障",
#               "is_send_email": 1},
#     "Fse17": {"detail": "故障字交流侧 C 相霍尔断线（-15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse18": {"detail": "故障字交流侧 C 相霍尔断线（IR）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse19": {"detail": "故障字交流侧 C 相霍尔断线（GND）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse20": {"detail": "故障字直流侧电流霍尔断线（+15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse21": {"detail": "故障字直流侧霍尔断线（-15V）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse22": {"detail": "故障字直流侧霍尔断线（IR）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse23": {"detail": "故障字直流侧霍尔断线（GND）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse41": {"detail": "故障字防雷器故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse43": {"detail": "故障字电流控制偏差过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse44": {"detail": "故障字电网电压不平衡", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse45": {"detail": "故障字交流侧电流直流分量超限", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse54": {"detail": "故障字交流开关合闸电压不匹配", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse56": {"detail": "故障字AD 零漂过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse57": {"detail": "故障字绝缘电阻偏低", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse58": {"detail": "故障字绝缘检测失败", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse59": {"detail": "故障字HMI 通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse60": {"detail": "故障字BMS 通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse61": {"detail": "故障字EMS 连接超时 1（外部总线）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse62": {"detail": "故障字并机通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse63": {"detail": "故障字EEPROM 故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse64": {"detail": "故障字SPI 故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse65": {"detail": "故障字物联网通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse66": {"detail": "故障字客户后台通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse69": {"detail": "故障字CBC 过流", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse70": {"detail": "故障字设备过载", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse71": {"detail": "故障字电网电压反序", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse72": {"detail": "故障字母线软启动失败", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse73": {"detail": "故障字交流侧脱扣器故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse74": {"detail": "故障字交流侧开关故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse75": {"detail": "故障字载波同步故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse76": {"detail": "故障字EMS 连接超时 1（内部通讯）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse77": {"detail": "故障字EMS 连接超时 2（外部总线）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse78": {"detail": "故障字EMS 连接超时 2（内部通讯）", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse79": {"detail": "故障字EMS 通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse80": {"detail": "故障字产品版本号不匹配", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse81": {"detail": "故障字检测板型号不匹配", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse87": {"detail": "故障字RTC 故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse96": {"detail": "故障字电池电压过高", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse99": {"detail": "故障字黑匣子存储故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse100": {"detail": "故障字电源盒检测异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse101": {"detail": "故障字主机异常停机", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse103": {"detail": "故障字锁相异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "Fse107": {"detail": "故障字直流侧中点采样断线", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#
#     "DCse": {"detail": " 直流接触器状态", "type": 3, 0: "断开", 1: "闭合"},
#     "ESBse": {"detail": " 急停按钮状态", "type": 3, 0: "正常", 1: "急停"},
#     "IFse": {"detail": " IGBT 风机状态 ", "type": 3, 0: "运行", 1: "停转"},
#     "ITCse": {"detail": " 绝缘检测接触器状态", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "LPDse": {"detail": " 防雷器状态 ", "type": 3, 0: "正常", 1: "故障", "is_send_email": 1},
#     "PEquse": {"detail": "PCS与EMS 通讯状态", "type": 3, 0: "中断", 1: "正常", "is_send_email": 1},
#     "DO": {"detail": " 降额运行 ", "type": 3, 0: "未降额", 1: "间隔", "is_send_email": 1},
#     "PCStu": {"detail": "PCS 开关", "type": 3, 0: "运行", 1: "停机", "is_send_email": 1},
#
#
#     # 2.0 BMS
#     "FFau1": {"detail": "一级故障1总压过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau2": {"detail": "一级故障2总压欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau3": {"detail": "一级故障3单体过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau4": {"detail": "一级故障4单体欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau5": {"detail": "一级故障5单体过温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1, "is_send_message": 1},
#     "FFau6": {"detail": "一级故障6单体低温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau7": {"detail": "一级故障7压差过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau8": {"detail": "一级故障8温差过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1, "is_send_message": 1},
#     "FFau9": {"detail": "一级故障9电流过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau10": {"detail": "一级故障10高压异常", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau11": {"detail": "一级故障11主从通讯", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau12": {"detail": "一级故障12单体电压排线", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau13": {"detail": "一级故障13单体", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau15": {"detail": "一级故障15SOC过低", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau16": {"detail": "一级故障16绝缘漏电", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau17": {"detail": "一级故障17粘连", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau18": {"detail": "一级故障18预充状态", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau19": {"detail": "一级故障19供电过高", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau20": {"detail": "一级故障20供电过低", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau21": {"detail": "一级故障21T1高温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau22": {"detail": "一级故障22T1低温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau23": {"detail": "一级故障23T2高温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau24": {"detail": "一级故障24T2低温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau27": {"detail": "一级故障27MSD故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau28": {"detail": "一级故障28电流异常", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau29": {"detail": "一级故障29极柱高温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau32": {"detail": "一级故障32PCS故障状态", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau33": {"detail": "一级故障33EMS故障状态", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau34": {"detail": "一级故障34保险丝状态", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau40": {"detail": "一级故障40反馈异常", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau42": {"detail": "一级故障42极限故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau43": {"detail": "一级故障43开路", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau44": {"detail": "一级故障44急停", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau45": {"detail": "一级故障45消防火警", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau46": {"detail": "一级故障46控制指令超时", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau49": {"detail": "一级故障49电池箱过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau50": {"detail": "一级故障50电池箱欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau52": {"detail": "一级故障52水冷机通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau54": {"detail": "一级故障54水浸故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau57": {"detail": "一级故障消防喷洒", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau58": {"detail": "一级故障消防设备故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau59": {"detail": "一级故障59水冷机自身故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "FFau60": {"detail": "一级故障60电表通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau1": {"detail": "二级故障1总压过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau2": {"detail": "二级故障2总压欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau3": {"detail": "二级故障3单体过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau4": {"detail": "二级故障4单体欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau5": {"detail": "二级故障5单体过温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1, "is_send_message": 1},
#     "SFau6": {"detail": "二级故障6单体低温", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau7": {"detail": "二级故障7压差过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau8": {"detail": "二级故障8温差过大", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1, "is_send_message": 1},
#     "SFau15": {"detail": "二级故障15SOC过低", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau49": {"detail": "二级故障49电池箱过压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau50": {"detail": "二级故障50电池箱欠压", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau52": {"detail": "二级故障52水冷机通讯故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "SFau54": {"detail": "二级故障54水浸故障", "type": 0, 0: "已恢复", 1: "未恢复警告", "is_send_email": 1},
#     "GFault": {"detail": "总故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#
#     "TFau1": {"detail": "三级故障1总压过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau2": {"detail": "三级故障2总压欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau3": {"detail": "三级故障3单体过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau4": {"detail": "三级故障4单体欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau5": {"detail": "三级故障5单体过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1, "is_send_message": 1},
#     "TFau6": {"detail": "三级故障6单体低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau7": {"detail": "三级故障7压差过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau8": {"detail": "三级故障8温差过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau9": {"detail": "三级故障9电流过大", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau10": {"detail": "三级故障10高压异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau11": {"detail": "三级故障11主从通讯", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau12": {"detail": "三级故障12单体电压排线", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau13": {"detail": "三级故障13单体温感排线", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau15": {"detail": "三级故障15SOC过低", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau16": {"detail": "三级故障16绝缘漏电", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau17": {"detail": "三级故障17粘连", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau18": {"detail": "三级故障18预充状态", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau19": {"detail": "三级故障19供电过高", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau20": {"detail": "三级故障20供电过", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau21": {"detail": "三级故障21T1高温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau22": {"detail": "三级故障22T1低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau23": {"detail": "三级故障23T2高温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau24": {"detail": "三级故障24T2低温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau25": {"detail": "三级均衡过温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau26": {"detail": "三级预留故障位", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau27": {"detail": "三级故障27MSD故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau28": {"detail": "三级故障28电流异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau29": {"detail": "三级故障29极柱高温", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau32": {"detail": "三级故障32PCS故障状态", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau33": {"detail": "三级故障33EMS故障状态", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau34": {"detail": "三级故障34保险丝状态", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau35": {"detail": "三级模拟前端故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau36": {"detail": "三级EEPROM故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau37": {"detail": "三级RTC故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau38": {"detail": "三级ADC故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau39": {"detail": "三级SD卡故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau40": {"detail": "三级故障40反馈异常", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau41": {"detail": "三级温升过快", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau42": {"detail": "三级故障42极限故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau43": {"detail": "三级故障43开路", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau44": {"detail": "三级故障44急停", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau45": {"detail": "三级故障45消防火警", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau46": {"detail": "三级故障46控制指令超时", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau49": {"detail": "三级故障49电池箱过压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau50": {"detail": "三级故障50电池箱欠压", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau51": {"detail": "三级防雷器故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau52": {"detail": "三级故障52水冷机通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障"},
#     "TFau53": {"detail": "三级UPS故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau54": {"detail": "三级故障54水浸故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau55": {"detail": "三级门禁故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau56": {"detail": "三级其他传感器故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau57": {"detail": "三级故障消防喷洒", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau58": {"detail": "三级故障消防设备故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_message": 1},
#     "TFau59": {"detail": "三级故障59水冷机自身故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     "TFau60": {"detail": "三级故障60电表通讯故障", "type": 1, 0: "已恢复", 1: "未恢复故障", "is_send_email": 1},
#     # "alarm": {"detail": " PCS 警告状态 ", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     # "GAlarm": {"detail": "总告警", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#
#     "SFau9": {"detail": "二级故障9电流过大", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau10": {"detail": "二级故障10高压异常", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau11": {"detail": "二级故障11主从通讯", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau12": {"detail": "二级故障12单体电压排线", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau13": {"detail": "二级故障13单体温感排线", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau16": {"detail": "二级故障16绝缘漏电", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau17": {"detail": "二级故障17粘连", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau18": {"detail": "二级故障18预充状态", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau19": {"detail": "二级故障19供电过高", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau20": {"detail": "二级故障20供电过低", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau21": {"detail": "二级故障21T1高温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau22": {"detail": "二级故障22T1低温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau23": {"detail": "二级故障23T2高温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau24": {"detail": "二级故障24T2低温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau27": {"detail": "二级故障27MSD故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau28": {"detail": "二级故障28电流异常", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau29": {"detail": "二级故障29极柱高温", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau32": {"detail": "二级故障32PCS故障状态", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau33": {"detail": "二级故障33EMS故障状态", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau34": {"detail": "二级故障34保险丝状态", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau40": {"detail": "二级故障40反馈异常", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau42": {"detail": "二级故障42极限故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau43": {"detail": "二级故障43开路", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau44": {"detail": "二级故障44急停", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau45": {"detail": "二级故障45消防火警", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau46": {"detail": "二级故障46控制指令超时", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau57": {"detail": "二级故障消防喷洒", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau58": {"detail": "二级故障消防设备故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau59": {"detail": "二级故障59水冷机自身故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#     "SFau60": {"detail": "二级故障60电表通讯故障", "type": 2, 0: "已恢复", 1: "未恢复报警", "is_send_email": 1},
#
#     "rela1": {"detail": "继电器总正", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela2": {"detail": "继电器总负", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela3": {"detail": "继电器预充", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela4": {"detail": "继电器断路器分励", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela5": {"detail": "继电器PCS干接点", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela6": {"detail": "继电器故障灯", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela7": {"detail": "继电器运行", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#     "rela8": {"detail": "继电器直流供电控制", "type": 3, 0: "断开", 1: "闭合", "is_send_email": 1},
#
#     "ChaSe": {"detail": "充电状态", "type": 3, 0: "允许充电", 1: "禁止充电"},
#     "DisSe": {"detail": "放电状态", "type": 3, 0: "允许放电", 1: "禁止放电"},
#     "HPCCse": {"detail": "高压闭合状态", "type": 3, 0: "断开", 1: "闭合"},
#     "HVPOI": {"detail": "高压上下电指令", "type": 3, 0: "高压下电 ", 1: "高压上电"},
#     "PTSse": {"detail": "PTC温度开关状态", "type": 3, 0: "断开", 1: "闭合"},
#     "WaPS": {"detail": "水泵开关", "type": 3, 0: "断开", 1: "闭合"},
#     "BEquse": {"detail": "通道2 bms 设备状态", "type": 3, 0: "中断", 1: "正常"},
#     "BCUMLS": {"detail": "BCU主回路状态", "type": 3, 0: "断开", 1: "闭合"},
#     "BCULCI": {"detail": "BCU回路闭合指令", "type": 3, 0: "断开", 1: "闭合"},
#     "BORLCI": {"detail": "BCU是否允许闭合主回路", "type": 3, 0: "不允许", 1: "允许"},
#
#     # "TCLoadFollow": {"detail": "云端自动化模式下负荷跟随", "type": 3, 0: "停止", 1: "运行"},
#
#     # 3.0 A版本 EMS
#     "FAU1": {"detail": "FPGA硬件故障-A相硬件过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU2": {"detail": "FPGA硬件故障-B相硬件过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU3": {"detail": "FPGA硬件故障-C相硬件过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU4": {"detail": "FPGA硬件故障-N相硬件过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU5": {"detail": "FPGA硬件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU6": {"detail": "FPGA硬件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU7": {"detail": "FPGA硬件故障-单元直压故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU8": {"detail": "FPGA硬件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU9": {"detail": "FPGA硬件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU10": {"detail": "FPGA硬件故障-开关电源欠压", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU11": {"detail": "FPGA硬件故障-N相IGBT故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU12": {"detail": "FPGA硬件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU13": {"detail": "ARM软件故障-A相输出过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU14": {"detail": "ARM软件故障-A相输出速断", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU15": {"detail": "ARM软件故障-B相输出过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU16": {"detail": "ARM软件故障-B相输出速断", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU17": {"detail": "ARM软件故障-C相输出过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU18": {"detail": "ARM软件故障-C相输出速断", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU19": {"detail": "ARM软件故障-N相输出速断", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU20": {"detail": "ARM软件故障-N相输出速断", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU21": {"detail": "ARM软件故障-电压THDU超限", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU22": {"detail": "ARM软件故障-系统过频率", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU23": {"detail": "ARM软件故障-系统欠频率", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU24": {"detail": "ARM软件故障-直流充电过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU25": {"detail": "ARM软件故障-直流放电过流", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU26": {"detail": "ARM软件故障-孤岛保护", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1, "is_send_message": 1},
#     "FAU27": {"detail": "ARM软件故障-交流主接合闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU28": {"detail": "ARM软件故障-交流主接分闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU29": {"detail": "ARM软件故障-交流软启合闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU30": {"detail": "ARM软件故障-交流软启分闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU31": {"detail": "ARM软件故障-直流主接合闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU32": {"detail": "ARM软件故障-直流主接分闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU33": {"detail": "ARM软件故障-直流软启合闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU34": {"detail": "ARM软件故障-直流软启合闸故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU35": {"detail": "ARM软件故障-铁电参数存储错误", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU36": {"detail": "ARM软件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU37": {"detail": "ARM软件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU38": {"detail": "ARM软件故障-起机条件不满足", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU39": {"detail": "ARM软件故障-逆变启动超时", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU40": {"detail": "ARM软件故障-参数下发设置错误", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU41": {"detail": "ARM软件故障-BMS温度异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU42": {"detail": "ARM软件故障-BMS跳机", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU43": {"detail": "ARM软件故障-BMS电池告警", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU44": {"detail": "ARM软件故障-DCDC通讯故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU45": {"detail": "ARM软件故障-急停或熔芯故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1, "is_send_message": 1},
#     "FAU46": {"detail": "ARM软件故障-PCS光纤通讯故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU47": {"detail": "ARM软件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU48": {"detail": "ARM软件故障-母线半直压过压", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU49": {"detail": "ARM软件故障-DCDC启动超时", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "FAU50": {"detail": "ARM软件故障-保留", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "FAU51": {"detail": "ARM软件故障-交流漏电流保护", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#
#
#     # 3.0 A版本 PCS
#
#
#     # 3.0 A版本 BMS
#
#
#     # 3.0 B版本 EMS
#
#
#     # 3.0 B版本 PCS
#     "TLT1": {"detail": "CANA通信故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT10": {"detail": "辅助电源故障1", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT11": {"detail": "辅助电源故障2", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT12": {"detail": "辅助电源故障3", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT13": {"detail": "模块过温1", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT14": {"detail": "模块过温2", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT15": {"detail": "模块过温降额", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT16": {"detail": "环境过温降额", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT17": {"detail": "控制器采样不一致", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT18": {"detail": "电池电量不足", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT19": {"detail": "绝缘检测异常1", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT2": {"detail": "CANB通信故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT20": {"detail": "绝缘检测异常2", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT21": {"detail": "模块限流告警1", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT22": {"detail": "模块限流告警2", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT23": {"detail": "模块限流告警3", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT24": {"detail": "模块限流告警4", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT25": {"detail": "模块限流告警5", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT26": {"detail": "模块限流告警6", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT27": {"detail": "模块限流告警7", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT28": {"detail": "模块电流异常1", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT29": {"detail": "模块电流异常2", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT3": {"detail": "CANC通信故障", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT30": {"detail": "模块电流异常3", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT31": {"detail": "模块电流异常4", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT32": {"detail": "模块电流异常5", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT33": {"detail": "模块电流异常6", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT34": {"detail": "模块电流异常7", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT35": {"detail": "直流过载报警", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT36": {"detail": "直流过载超时", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT37": {"detail": "直流输入软启动失败", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT38": {"detail": "直流输入电控开关开路", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT39": {"detail": "直流输入电控开关短路", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT4": {"detail": "同步信号1故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT40": {"detail": "直流母线电压不平衡", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT41": {"detail": "直流母线软启动失败", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT42": {"detail": "BMS关机故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT43": {"detail": "交流母线电压异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT44": {"detail": "孤岛保护", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1, "is_send_message": 1},
#     "TLT45": {"detail": "交流过载超时", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT46": {"detail": "交流母线禁止接入", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT47": {"detail": "交流母线缺N", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT48": {"detail": "离网电压震荡", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT49": {"detail": "并离网切换错误", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT5": {"detail": "辅助控制板通信故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT50": {"detail": "交流电控开关开路", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT51": {"detail": "交流电控开关短路", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT52": {"detail": "紧急停机", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1, "is_send_message": 1},
#     "TLT53": {"detail": "校准参数异常", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT54": {"detail": "采样零点异常", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT55": {"detail": "Flash异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT56": {"detail": "DSP初始化异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT57": {"detail": "监控初始化异常", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT58": {"detail": "DSP版本异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT59": {"detail": "CPLD版本异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT6": {"detail": "SPI通信故障", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT60": {"detail": "硬件版本异常", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT61": {"detail": "重号故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT62": {"detail": "主机冲突故障", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT63": {"detail": "DSP参数设置不匹配", "type": 1, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT64": {"detail": "监控参数设置不匹配", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT65": {"detail": "Drm0 关机", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT66": {"detail": "模块数量异常", "type": 0, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT7": {"detail": "BMS通信连接超时", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#     "TLT8": {"detail": "电网电表通信超时", "type": 2, 0: "正常", 1: "故障", "is_send_email": 0},
#     "TLT9": {"detail": "模块风扇故障1", "type": 1, 0: "正常", 1: "故障", "is_send_email": 1},
#
#
#     # 3.0 B版本 BMS
#
#
# }


alarm_detail = {'DCse': {'detail': '直流接触器状态', 'type': 3, 0: '断开', 1: '闭合'},
                'ESBse': {'detail': '急停按钮状态', 'type': 3, 0: '正常', 1: '急停'},
                'IFse': {'detail': 'IGBT 风机状态', 'type': 3, 0: '运行', 1: '停转'},
                'Fault': {'detail':  'PCS 故障状态', 'type': 1, 0: '无故障', 1: '有故障'},
                'alarm': {'detail': 'PCS 警告状态', 'type': 2, 0: '无告警', 1: '有告警'},
                'Fse1': {'detail': '故障字储能电池极性反接故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse2': {'detail': '故障字储能电池电压异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse3': {'detail': '故障字直流侧半母线硬件过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse4': {'detail': '故障字交流硬件过流', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse5': {'detail': '故障字IGBT A 相故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse6': {'detail': '故障字IGBT B 相故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse7': {'detail': '故障字IGBT C 相故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse26': {'detail': '故障字直流开关故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse27': {'detail': '故障字IGBT 风机故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse29': {'detail': '故障字直流侧全母线软件过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse30': {'detail': '故障字直流侧全母线软件欠压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse31': {'detail': '故障字半母线电压偏差过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse33': {'detail': '故障字电网交流过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse34': {'detail': '故障字电网交流欠压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse35': {'detail': '故障字离网交流过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse36': {'detail': '故障字离网交流欠压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse37': {'detail': '故障字电网交流过频', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse38': {'detail': '故障字电网交流欠频', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse40': {'detail': '故障字输出缺相故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse46': {'detail': '故障字环境过温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse47': {'detail': '故障字环境低温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse48': {'detail': '故障字IGBT 模块 A 相过温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse49': {'detail': '故障字IGBT 模块 B 相过温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse50': {'detail': '故障字IGBT 模块 C 相过温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse51': {'detail': '故障字IGBT 模块 A 相低温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse52': {'detail': '故障字IGBT 模块 B 相低温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse53': {'detail': '故障字IGBT 模块 C 相低温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse97': {'detail': '故障字电池电压过低', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse106': {'detail': '故障字BMS 系统故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Ase1': {'detail': '告警字储能电池极性反接故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase2': {'detail': '告警字储能电池电压异常', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase3': {'detail': '告警字直流侧半母线硬件过压', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase4': {'detail': '告警字交流硬件过流', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase5': {'detail': '告警字IGBT A 相故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase6': {'detail': '告警字IGBT B 相故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase7': {'detail': '告警字IGBT C 相故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase26': {'detail': '告警字直流开关故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase27': {'detail': '告警字IGBT 风机故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase29': {'detail': '告警字直流侧全母线软件过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase30': {'detail': '告警字直流侧全母线软件欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase31': {'detail': '告警字半母线电压偏差过大', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase33': {'detail': '告警字电网交流过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase34': {'detail': '告警字电网交流欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase35': {'detail': '告警字离网交流过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase36': {'detail': '告警字离网交流欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase37': {'detail': '告警字电网交流过频', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase38': {'detail': '告警字电网交流欠频', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase40': {'detail': '告警字输出缺相故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase46': {'detail': '告警字环境过温', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase47': {'detail': '告警字环境低温', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase48': {'detail': '告警字IGBT 模块 A 相过温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase49': {'detail': '告警字IGBT 模块 B 相过温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase50': {'detail': '告警字IGBT 模块 C 相过温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase51': {'detail': '告警字IGBT 模块 A 相低温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase52': {'detail': '告警字IGBT 模块 B 相低温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase53': {'detail': '告警字IGBT 模块 C 相低温', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase57': {'detail': '告警字绝缘电阻偏低', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase60': {'detail': '告警字BMS 通讯故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase79': {'detail': '告警字EMS 通讯故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase96': {'detail': '告警字电池电压过高', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase97': {'detail': '告警字电池电压过低', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase106': {'detail': '告警字BMS 系统故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase109': {'detail': '告警字电池欠压告警', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase110': {'detail': '告警字急停告警', 'type': 2, 0: '无告警', 1: '有告警'},
                'GAlarm': {'detail': '总告警', 'type': 2, 0: '无告警', 1: '有告警'},
                'GFault': {'detail': '总故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'ITCse': {'detail': '绝缘检测接触器状态', 'type': 3, 0: '断开', 1: '闭合', 'is_send_email': 1},
                'LPDse': {'detail': '防雷器状态', 'type': 3, 0: '正常', 1: '故障'},
                'PEquse': {'detail': 'PCS与EMS 通讯状态', 'type': 3, 0: '中断', 1: '正常', 'is_send_email': 1},
                'DO': {'detail': '降额运行', 'type': 3, 0: '未降额', 1: '间隔', 'is_send_email': 1},
                'Fse8': {'detail': '故障字交流侧 A 相霍尔断线（+15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse9': {'detail': '故障字交流侧 A 相霍尔断线（-15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse10': {'detail': '故障字交流侧 A 相霍尔断线（IR）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse11': {'detail': '故障字交流侧 A 相霍尔断线（GND）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse12': {'detail': '故障字交流侧 B 相电流霍尔断线（+15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse13': {'detail': '故障字交流侧 B 相霍尔断线（-15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse14': {'detail': '故障字交流侧 B 相霍尔断线（IR）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse15': {'detail': '故障字交流侧 B 相霍尔断线（GND）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse16': {'detail': '故障字交流侧 C 相电流霍尔断线（+15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse17': {'detail': '故障字交流侧 C 相霍尔断线（-15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse18': {'detail': '故障字交流侧 C 相霍尔断线（IR）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse19': {'detail': '故障字交流侧 C 相霍尔断线（GND）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse20': {'detail': '故障字直流侧电流霍尔断线（+15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse21': {'detail': '故障字直流侧霍尔断线（-15V）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse22': {'detail': '故障字直流侧霍尔断线（IR）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse23': {'detail': '故障字直流侧霍尔断线（GND）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse41': {'detail': '故障字防雷器故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse43': {'detail': '故障字电流控制偏差过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse44': {'detail': '故障字电网电压不平衡', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse45': {'detail': '故障字交流侧电流直流分量超限', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse54': {'detail': '故障字交流开关合闸电压不匹配', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse56': {'detail': '故障字AD 零漂过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse57': {'detail': '故障字绝缘电阻偏低', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse58': {'detail': '故障字绝缘检测失败', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse59': {'detail': '故障字HMI 通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse60': {'detail': '故障字BMS 通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse61': {'detail': '故障字EMS 连接超时 1（外部总线）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse62': {'detail': '故障字并机通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse63': {'detail': '故障字EEPROM 故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse64': {'detail': '故障字SPI 故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse65': {'detail': '故障字物联网通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse66': {'detail': '故障字客户后台通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse69': {'detail': '故障字CBC 过流', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse70': {'detail': '故障字设备过载', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse71': {'detail': '故障字电网电压反序', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse72': {'detail': '故障字母线软启动失败', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse73': {'detail': '故障字交流侧脱扣器故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse74': {'detail': '故障字交流侧开关故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse75': {'detail': '故障字载波同步故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse76': {'detail': '故障字EMS 连接超时 1（内部通讯）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse77': {'detail': '故障字EMS 连接超时 2（外部总线）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse78': {'detail': '故障字EMS 连接超时 2（内部通讯）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse79': {'detail': '故障字EMS 通讯故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse80': {'detail': '故障字产品版本号不匹配', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse81': {'detail': '故障字检测板型号不匹配', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse87': {'detail': '故障字RTC 故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse96': {'detail': '故障字电池电压过高', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse99': {'detail': '故障字黑匣子存储故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse100': {'detail': '故障字电源盒检测异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse101': {'detail': '故障字主机异常停机', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse103': {'detail': '故障字锁相异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Fse107': {'detail': '故障字直流侧中点采样断线', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'Ase8': {'detail': '告警字交流侧 A 相霍尔断线（+15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase9': {'detail': '告警字交流侧 A 相霍尔断线（-15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase10': {'detail': '告警字交流侧 A 相霍尔断线（IR）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase11': {'detail': '告警字交流侧 A 相霍尔断线（GND）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase12': {'detail': '告警字交流侧 B 相电流霍尔断线（+15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase13': {'detail': '告警字交流侧 B 相霍尔断线（-15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase14': {'detail': '告警字交流侧 B 相霍尔断线（IR）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase15': {'detail': '告警字交流侧 B 相霍尔断线（GND）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase16': {'detail': '告警字交流侧 C 相电流霍尔断线（+15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase17': {'detail': '告警字交流侧 C 相霍尔断线（-15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase18': {'detail': '告警字交流侧 C 相霍尔断线（IR）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase19': {'detail': '告警字交流侧 C 相霍尔断线（GND）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase20': {'detail': '告警字直流侧电流霍尔断线（+15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase21': {'detail': '告警字直流侧霍尔断线（-15V）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase22': {'detail': '告警字直流侧霍尔断线（IR）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase23': {'detail': '告警字直流侧霍尔断线（GND）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase41': {'detail': '告警字防雷器故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase43': {'detail': '告警字电流控制偏差过大', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase44': {'detail': '告警字电网电压不平衡', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase45': {'detail': '告警字交流侧电流直流分量超限', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase54': {'detail': '告警字交流开关合闸电压不匹配', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase56': {'detail': '告警字AD 零漂过大', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase58': {'detail': '告警字绝缘检测失败', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase59': {'detail': '告警字HMI 通讯故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase61': {'detail': '告警字EMS 连接超时 1（外部总线）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase62': {'detail': '告警字并机通讯故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase63': {'detail': '告警字EEPROM 故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase64': {'detail': '告警字SPI 故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase65': {'detail': '告警字物联网通讯故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase66': {'detail': '告警字客户后台通讯故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase69': {'detail': '告警字CBC 过流', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase70': {'detail': '告警字设备过载', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase71': {'detail': '告警字电网电压反序', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase72': {'detail': '告警字母线软启动失败', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase73': {'detail': '告警字交流侧脱扣器故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase74': {'detail': '告警字交流侧开关故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase75': {'detail': '告警字载波同步故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'Ase76': {'detail': '告警字EMS 连接超时 1（内部通讯）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase77': {'detail': '告警字EMS 连接超时 2（外部总线）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase78': {'detail': '告警字EMS 连接超时 2（内部通讯）', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase80': {'detail': '告警字产品版本号不匹配', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase81': {'detail': '告警字检测板型号不匹配', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase87': {'detail': '告警字RTC 故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase99': {'detail': '告警字黑匣子存储故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase100': {'detail': '告警字电源盒检测异常', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase101': {'detail': '告警字主机异常停机', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase103': {'detail': '告警字锁相异常', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase107': {'detail': '告警字直流侧中点采样断线', 'type': 0, 0: '无告警', 1: '有告警'},
                'Ase108': {'detail': '告警字不满足并网运行条件', 'type': 0, 0: '无告警', 1: '有告警'},
                'rela1': {'detail': '继电器总正', 'type': 3, 0: '断开', 1: '闭合'},
                'rela2': {'detail': '继电器总负', 'type': 3, 0: '断开', 1: '闭合'},
                'rela3': {'detail': '继电器预充', 'type': 3, 0: '断开', 1: '闭合'},
                'rela4': {'detail': '继电器断路器分励', 'type': 3, 0: '断开', 1: '闭合'},
                'rela5': {'detail': '继电器运行灯', 'type': 3, 0: '断开', 1: '闭合'},
                'rela7': {'detail': '继电器风扇', 'type': 3, 0: '断开', 1: '闭合'},
                'rela8': {'detail': '继电器直流供电控制', 'type': 3, 0: '断开', 1: '闭合'},
                'PCStu': {'detail': 'PCS 开关', 'type': 3, 0: '运行', 1: '停机'},
                'FFau1': {'detail': '一级故障1总压过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau2': {'detail': '一级故障2总压欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau3': {'detail': '一级故障3单体过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau4': {'detail': '一级故障4单体欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau5': {'detail': '一级故障5单体过温', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau6': {'detail': '一级故障6单体低温', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau7': {'detail': '一级故障7压差过大', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau8': {'detail': '一级故障8温差过大', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau9': {'detail': '一级故障9电流过大', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau15': {'detail': '一级故障15SOC过低', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau16': {'detail': '一级故障16绝缘漏电', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau19': {'detail': '一级故障19供电过高', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau20': {'detail': '一级故障20供电过低', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau21': {'detail': '一级故障21T1高温', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau22': {'detail': '一级故障22T1低温', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau23': {'detail': '一级故障23T2高温', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau24': {'detail': '一级故障24T2低温', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau29': {'detail': '一级故障29极柱高温', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau32': {'detail': '一级故障32PCS故障状态', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau33': {'detail': '一级故障33EMS故障状态', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau49': {'detail': '一级故障49电池箱过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau50': {'detail': '一级故障50电池箱欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau57': {'detail': '一级故障57预留故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau58': {'detail': '一级故障58预留故障', 'type': 0, 0: '无告警', 1: '有告警'},
                'FFau59': {'detail': '一级故障59水冷机自身故障', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'FFau60': {'detail': '一级故障60电表通讯故障', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau1': {'detail': '二级故障1总压过压', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau2': {'detail': '二级故障2总压欠压', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau3': {'detail': '二级故障3单体过压', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau4': {'detail': '二级故障4单体欠压', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau5': {'detail': '二级故障5单体过温', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau6': {'detail': '二级故障6单体低温', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau7': {'detail': '二级故障7压差过大', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau8': {'detail': '二级故障8温差过大', 'type': 0, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau9': {'detail': '二级故障9电流过大', 'type': 2, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau15': {'detail': '二级故障15SOC过低', 'type': 0, 0: '无告警', 1: '有告警'},
                'SFau16': {'detail': '二级故障16绝缘漏电', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau19': {'detail': '二级故障19供电过高', 'type': 2, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau20': {'detail': '二级故障20供电过低', 'type': 2, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'SFau21': {'detail': '二级故障21T1高温', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau22': {'detail': '二级故障22T1低温', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau23': {'detail': '二级故障23T2高温', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau24': {'detail': '二级故障24T2低温', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau29': {'detail': '二级故障29极柱高温', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau32': {'detail': '二级故障32PCS故障状态', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau33': {'detail': '二级故障33EMS故障状态', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau49': {'detail': '二级故障49电池箱过压', 'type': 0, 0: '无告警', 1: '有告警'},
                'SFau50': {'detail': '二级故障50电池箱欠压', 'type': 0, 0: '无告警', 1: '有告警'},
                'SFau57': {'detail': '二级故障57预留故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau58': {'detail': '二级故障58预留故障', 'type': 2, 0: '无告警', 1: '有告警'},
                'SFau59': {'detail': '二级故障59水冷机自身故障', 'type': 2, 0: '无告警', 1: '有告警', 'is_send_email': 1},
                'TFau1': {'detail': '三级故障1总压过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau2': {'detail': '三级故障2总压欠压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau3': {'detail': '三级故障3单体过压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau4': {'detail': '三级故障4单体欠压', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau5': {'detail': '三级故障5单体过温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau6': {'detail': '三级故障6单体低温', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau7': {'detail': '三级故障7压差过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau8': {'detail': '三级故障8温差过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau9': {'detail': '三级故障9电流过大', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau10': {'detail': '三级故障10高压异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau11': {'detail': '三级故障11主从通讯', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau12': {'detail': '三级故障12单体电压排线', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau13': {'detail': '三级故障13单体温感排线', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau15': {'detail': '三级故障15SOC过低', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau16': {'detail': '三级故障16绝缘漏电', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau17': {'detail': '三级故障17粘连', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau18': {'detail': '三级故障18预充状态', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau19': {'detail': '三级故障19供电过高', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau20': {'detail': '三级故障20供电过低', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau21': {'detail': '三级故障21T1高温', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau22': {'detail': '三级故障22T1低温', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau23': {'detail': '三级故障23T2高温', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau24': {'detail': '三级故障24T2低温', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau25': {'detail': '三级故障25预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau26': {'detail': '三级故障26预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau27': {'detail': '三级故障27MSD故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau28': {'detail': '三级故障28电流异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau29': {'detail': '三级故障29极柱高温', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau32': {'detail': '三级故障32PCS故障状态', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau33': {'detail': '三级故障33EMS故障状态', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau34': {'detail': '三级故障34保险丝状态', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau35': {'detail': '三级故障35预留故障（模拟前端故障）', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau36': {'detail': '三级故障36预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau37': {'detail': '三级故障37预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau38': {'detail': '三级故障38预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau39': {'detail': '三级故障39预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau40': {'detail': '三级故障40反馈异常', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1,'is_send_message': 1}, 
                'TFau41': {'detail': '三级故障41预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau42': {'detail': '三级故障42极限故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'TFau43': {'detail': '三级故障43开路', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1,'is_send_message': 1},
                'TFau44': {'detail': '三级故障44急停', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1,'is_send_message': 1},
                'TFau45': {'detail': '三级故障45消防火警', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1,'is_send_message': 1}, 
                'TFau46': {'detail': '三级故障46控制指令超时', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau49': {'detail': '三级故障49电池箱过压', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau50': {'detail': '三级故障50电池箱欠压', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau51': {'detail': '三级故障51预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau52': {'detail': '三级故障52水冷机通讯故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau53': {'detail': '三级故障53预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau54': {'detail': '三级故障54水浸故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1,'is_send_message': 1}, 
                'TFau55': {'detail': '三级故障55预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau56': {'detail': '三级故障56预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau57': {'detail': '三级故障57预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau58': {'detail': '三级故障58预留故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TFau59': {'detail': '三级故障59水冷机自身故障', 'type': 1, 0: '无故障', 1: '有故障', 'is_send_email': 1},
                'ChaSe': {'detail': '充电状态', 'type': 3, 0: '允许充电', 1: '禁止充电 '},
                'DisSe': {'detail': '放电状态', 'type': 3, 0: '允许放电', 1: '禁止放电 '},
                'HPCCse': {'detail': '高压闭合状态', 'type': 3, 0: '断开', 1: '闭合'},
                'HVPOI': {'detail': '高压上下电指令', 'type': 3, 0: '高压下电', 1: '高压上电'},
                'PTSse': {'detail': 'PTC温度开关状态', 'type': 3, 0: '断开', 1: '闭合'},
                'WaPS': {'detail': '水泵开关', 'type': 3, 0: '断开', 1: '闭合'},
                'BEquse': {'detail': '通道2 bms 设备状态', 'type': 3, 0: '中断', 1: '正常'},
                'BCUMLS': {'detail': 'BCU主回路状态', 'type': 3, 0: '断开', 1: '闭合'},
                'BCULCI': {'detail': 'BCU回路闭合指令', 'type': 3, 0: '断开', 1: '闭合'},
                'BORLCI': {'detail': 'BCU是否允许闭合主回路', 'type': 3, 0: '不允许', 1: '允许'},
                'MCGSA': {'detail': '组态自动化运行模式', 'type': 3, 0: '停止', 1: '运行'},
                'MCGSP': {'detail': '组态计划调度模式', 'type': 3, 0: '停止', 1: '运行'},
                'AEn': {'detail': '功能策略启停', 'type': 3, 0: '停止', 1: '运行'},
                'AEnC': {'detail': '接收云端调度', 'type': 3, 0: '停止', 1: '运行'},
                'LFZT': {'detail': '组态自动化运行负荷跟随', 'type': 3, 0: '停止', 1: '运行'},
                'LFZTP': {'detail': '组态计划运行负荷跟随', 'type': 3, 0: '停止', 1: '运行'},
                'LF': {'detail': '云端负荷跟随调度', 'type': 3, 0: '停止', 1: '运行'},
                'PStse': {'detail': '通道3 虚拟点 PCS停机状态', 'type': 3, 0: '停止', 1: '运行'},
                'PStan': {'detail': '通道3 虚拟点 PCS待机状态', 'type': 3, 0: '停止', 1: '运行'},
                'PChaon': {'detail': '通道3 虚拟点 PCS充电运行', 'type': 3, 0: '停止', 1: '运行'},
                'PDison': {'detail': '通道3 虚拟点 PCS放电运行', 'type': 3, 0: '停止', 1: '运行'},
                'ZPOn': {'detail': '通道3 虚拟点 PCS零功率运行', 'type': 3, 0: '停止', 1: '运行'},
                'PRun': {'detail': '通道3 虚拟点 PCS运行', 'type': 3, 0: '停止', 1: '运行'},
                'VEquse': {'detail': '通道3 虚拟点 设备状态', 'type': 3, 0: '停止', 1: '运行'},
                'CloudAuto': {'detail': '云端自动化模式', 'type': 3, 0: '停止', 1: '运行'},
                'TCLoadFollow': {'detail': '云端自动化模式下负荷跟随', 'type': 3, 0: '停止', 1: '运行'},
                'TransfiniteFault': {'detail': '变压器超限故障', 'type': 1, 0: '无故障', 1: '有故障'},
                'TransfiniteAlarm': {'detail': '变压器超限告警', 'type': 2, 0: '无告警', 1: '有告警'},
                # 'ARFF': {'detail': '防逆流通讯故障', 'type': 1, 1: '有故障', 0: '无故障', 'is_send_email': 1, 'is_send_message': 1},
                # 'TMSFC': {'detail': '水机故障码', 'type': None, 0: None, 1: None},
                'TMSFCl': {'detail': '水机故障等级', 'type': 1, 0: '无故障', 1: '一级故障', 2: '二级故障', 3: '三级故障'}
            }
