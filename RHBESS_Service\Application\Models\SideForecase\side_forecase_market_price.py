#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_market_price.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-24 14:28:51


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince

class ForecaseMarketPrice(user_Base):
    u'市场价格'
    __tablename__ = "t_side_forecase_market_price"
    day = Column(VARCHAR(20), nullable=False,primary_key=True,comment=u"数据时间，年月日")
    moment = Column(VARCHAR(10), nullable=False, primary_key=True,comment=u"时刻，时分")
    plan_value = Column(VARCHAR(20), nullable=False, comment=u"计划数据")
    real_value = Column(VARCHAR(20), nullable=True, comment=u"实际数据")
    value_type = Column(CHAR(2), nullable=False,primary_key=True,comment=u"数据类型，1电价，2电量")
    province_id = Column(Integer, ForeignKey("t_side_forecase_province.id"),nullable=False, comment=u"所属省份")
    province_market_price = relationship("ForecaseProvince",backref='province_market_price',foreign_keys=[province_id])
   
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
                
    def __repr__(self):
        
        return "{'day':'%s','moment':'%s','plan_value':'%s','real_value':'%s','value_type':%s}" % (
            self.day,self.moment,self.plan_value,self.real_value,self.value_type)
        
    