import datetime
import json
import os
import uuid
import pandas as pd
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,)
import geocoder
from django.db.models import Q
from rest_framework.response import Response
from rest_framework.views import APIView
from apis.user import models
from common import common_response_code
from serializers import user_serializers
from django.conf import settings
from django.db import transaction
from middlewares.authentications import JWTHeaderAuthentication, DenyAuthentication, JwtParamAuthentication
from apis.statistics_apis.forms import UploadFileForm
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG

class OrganizationViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''查询组织'''
    def post(self, request):
        name = request.data.get("name",'')#组织名称

        if name:
            organization_ins = models.Organization.objects.filter(Q(name__contains=name),is_used=1).order_by('id')
        else:
            organization_ins = models.Organization.objects.filter(is_used=1).order_by('id')
        organizations_ins = organization_ins.values("id", "name")

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": organizations_ins},
            }
        )

class OrganizationAddViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    '''添加组织'''
    @transaction.atomic
    def post(self, request):
        try:
            name = request.data.get("name")
            remark = request.data.get("remark", '')
            # 如果请求中包含文件，则将其保存到服务器上
            name_ = models.Organization.objects.filter(name=name).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "组织已存在！"},
                })
            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():
                uploaded_file = form.cleaned_data['file']
                uploaded_file_instance = models.Organization(file=uploaded_file)
                uploaded_file_instance.file_name = uploaded_file.name
                uploaded_file_instance.uuid = uuid.uuid4()
                uploaded_file_instance.name = name
                uploaded_file_instance.remark = remark if remark else ''
                uploaded_file_instance.save()
                download_url = uploaded_file_instance.get_download_url()  # 获取文件的下载链接
                uploaded_file_instance.url = download_url
                uploaded_file_instance.save()
            else:
                organization_ins = models.Organization.objects.create(
                    file='picture/融合元储log原始版.png',
                    file_name = '融合元储log原始版.png',
                    uuid = '07240d1e-5c90-4770-af33-175d51768891',
                    name = name,
                    remark = remark if remark else '',
                    url = '/media/picture/%E8%9E%8D%E5%90%88%E5%85%83%E5%82%A8log%E5%8E%9F%E5%A7%8B%E7%89%88.png'
                )
                organization_ins.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "添加成功!"},
            })
        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "添加失败！"},
            })




class OrganizationDeViews(APIView):#删除
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def post(self, request):
        p_id = request.data.get('id')
        if p_id:
            obj = models.Organization.objects.get(id=p_id)
            obj.is_used = 2
            obj.save()
            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "删除成功！"},
            })
        else:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "id为必填项"},
            })

class OrganizationInfoViews(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    ''' 展示与修改组织'''
    def get(self, request, *args, **kwargs):
        p_id = self.kwargs['id']#id
        try:
            organization_ins = models.Organization.objects.get(id=p_id,is_used=1)
        except:
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "未查询到"},
            })
        organization_dic = {
            'name': organization_ins.name,
            'remark': organization_ins.remark,
            'file_name': organization_ins.file_name,
            'url': organization_ins.url
        }

        return Response(organization_dic)

    def put(self, request, *args, **kwargs):
        try:
            data = request.data
            p_id = data['id']
            name = request.data.get("name")
            remark = request.data.get("remark",'')

            name_ = models.Organization.objects.filter(name=name).exclude(id=p_id).first()
            if name_:
                return Response({
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "组织已存在！"},
                })
            # 修改组织
            organization_ins = models.Organization.objects.get(id=p_id)
            form = UploadFileForm(request.data, request.FILES)
            if form.is_valid():
                uploaded_file = form.cleaned_data['file']
                organization_ins.file = uploaded_file
                organization_ins.file_name = uploaded_file.name
                organization_ins.uuid = uuid.uuid4()
                organization_ins.name = name
                organization_ins.remark = remark if remark else ''
                organization_ins.save()
                download_url = organization_ins.get_download_url()  # 获取文件的下载链接
                organization_ins.url = download_url
                organization_ins.save()

            else:
                organization_ins.file='picture/融合元储log原始版.png'
                organization_ins.file_name='融合元储log原始版.png'
                organization_ins.uuid='07240d1e-5c90-4770-af33-175d51768891'
                organization_ins.name=name
                organization_ins.remark=remark if remark else ''
                organization_ins.url = '/media/picture/%E8%9E%8D%E5%90%88%E5%85%83%E5%82%A8log%E5%8E%9F%E5%A7%8B%E7%89%88.png'
                organization_ins.save()

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "修改成功！"},
            })

        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "修改失败！"},
            })


