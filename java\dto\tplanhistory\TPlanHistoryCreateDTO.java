package com.robestec.analysis.dto.tplanhistory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 计划历史记录创建DTO
 */
@Data
@ApiModel("计划历史记录创建DTO")
public class TPlanHistoryCreateDTO {

    @ApiModelProperty(value = "计划名称", required = true)
    @NotBlank(message = "计划名称不能为空")
    private String name;

    @ApiModelProperty(value = "状态: 1-已保存, 2-已下发, 3-执行中, 4-已完成, 5-下发失败, 6-已停止", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "功率值")
    private Double power;

    @ApiModelProperty(value = "计划类型: 1-自定义, 2-周期性, 3-节假日", required = true)
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "描述")
    private String descr;

    @ApiModelProperty(value = "电站名称")
    private String station;

    @ApiModelProperty(value = "是否使用: 1-使用, 0-不使用")
    private Integer isUse;
}
