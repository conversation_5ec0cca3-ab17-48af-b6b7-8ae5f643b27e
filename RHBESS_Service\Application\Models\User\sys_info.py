#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-06-08 11:36:07
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\sys_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-07-21 13:41:49


from multiprocessing.sharedctypes import Value
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.user import User
from Application.Models.User.event_alarm_type import EventAlarmType
from Tools.Utils.time_utils import timeUtils

class SysInfo(user_Base):
    u'linux监控基础配置表'
    __tablename__ = "t_sys_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"名称")
    descr = Column(VARCHAR(256), nullable=False,comment=u"描述")
    cmd = Column(VARCHAR(256), nullable=False,comment=u"执行指令")
    refresh = Column(CHAR(1), nullable=False,server_default='0',comment=u"是否需要刷新0否1是")
    unit = Column(VARCHAR(10), nullable=True,server_default='',comment=u"单位")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        tim = timeUtils.getNewTimeStr()
        sn = 1
        user_session.merge(SysInfo(id=sn,name='cpu_count',descr=u'CPU逻辑个数',cmd='psutil.cpu_count()',unit='个',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='cpu_count1',descr=u'CPU物理个数',cmd='psutil.cpu_count(logical=False)',unit='个',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='cpu_uniq',descr=u'cpu型号',cmd='commands.getoutput("cat /proc/cpuinfo | grep name | cut -f2 -d: | uniq -c").strip()',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='memory_total',descr=u'物理内存',cmd='num_retain(psutil.virtual_memory().total / (1024.0**3))',unit='Gb',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='boot_time',descr=u'开机时间',cmd='timeUtils.ssTtimes(psutil.boot_time())',op_ts=tim));sn += 1
        disks = cls.disk_percent() # 所有磁盘位置
        for disk in disks:
            # 总容量
            user_session.merge(SysInfo(id=sn,name='%s_total'%disk,descr=u'磁盘 %s 总容量'%disk,cmd='num_retain(psutil.disk_usage("{}").total / (1024.0**2))'.format(disk),unit='Mb',op_ts=tim));
            sn += 1
            # 剩余
            user_session.merge(SysInfo(id=sn,name='%s_free'%disk,descr=u'磁盘 %s 剩余容量'%disk,cmd='num_retain(psutil.disk_usage("{}").free / (1024.0**2))'.format(disk),refresh=1,unit='Mb',op_ts=tim));
            sn += 1
            # 使用率
            user_session.merge(SysInfo(id=sn,name='%s_percent'%disk,descr=u'磁盘 %s 使用率'%disk,cmd='num_retain(psutil.disk_usage("{}").percent)'.format(disk),refresh=1,unit='%',op_ts=tim));
            sn += 1

        user_session.merge(SysInfo(id=sn,name='cpu_percent',descr=u'cpu使用率',cmd='psutil.cpu_percent(1)',refresh=1,unit='%',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='memory_free',descr=u'剩余物理内存',cmd='num_retain(psutil.virtual_memory().free / (1024.0**3))',refresh=1,unit='Gb',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='memory_percent',descr=u'物理内存使用率',cmd='num_retain((psutil.virtual_memory().total - psutil.virtual_memory().free) / float(psutil.virtual_memory().total)*100)',refresh=1,unit='%',op_ts=tim));sn += 1
        user_session.merge(SysInfo(id=sn,name='users_list',descr=u'连接用户列表',cmd='",".join([u.name for u in psutil.users()])',refresh=1,op_ts=tim))
        
        user_session.commit()
        user_session.close()
        
    @classmethod
    def disk_percent(cls):
        '''统计磁盘各个位置'''
        import re
        import subprocess
        all = []
        # 磁盘信息
        disk = subprocess.Popen('df -h',shell=True,stdout=subprocess.PIPE)
        info = disk.stdout.read()
        # list2 = re.findall("(/{0,1}\w{0,10}/\w{0,10})\n",info)
        for i in info.split('\n'):
            ii = i.split(' ')[-1]
            if ii and '/' in ii:
                all.append(ii)
        
        return all

    def __repr__(self):
        return "{'id':%s,'descr':'%s','name':'%s','cmd':'%s','refresh':'%s','op_ts':'%s'}" % (
            self.id,self.descr,self.name,self.cmd,self.refresh,self.op_ts)
        

        
    def deleteSysInfo(self,id):
        try:
            user_session.query(SysInfo).filter(SysInfo.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False