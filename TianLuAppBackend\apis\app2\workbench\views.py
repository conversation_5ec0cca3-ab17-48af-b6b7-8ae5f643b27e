# import ast
import datetime
import json
# import random
# import time
import uuid
import calendar

import math
# from django.db import transaction
from django.db.models import Sum, F, Min
# from django_redis import get_redis_connection
# import paho.mqtt.client as mqtt
from rest_framework.response import Response
from rest_framework.views import APIView

# from TianLuAppBackend import settings
from apis.app2 import error_log, success_log
from apis.app2.workbench.serializer import AddIncomesSerializer, \
    PeakValleySerializer, CustomizationAddSerializer, UserStrategySerializer, ResponseUserStrategySerializer
# from apis.statistics_apis.main import mtqq_station_strategy
from apis.statistics_apis.models import UserStrategy
from apis.user import models
from apis.web2.project_account.CustomPriceSerialzier import CustomizationDetSerializer, CustomizationUpdateSerializer
from common import common_response_code
from common.database_pools import ads_db_tool
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
# from serializers import monitor_serializers
# from tools.aly_send_smscode import Sample
from LocaleTool.common import redis_pool
from tools.units_conversion import charge_discharge_conversion_sum
from django.db import connections
from common.constant import EMPTY_STR_LIST
# Create your views here.

"""
充放电量明细
"""""


peak_valley_types = {
    "1": "peak",
    "-1": "valley",
    "0": "flat",
    "2": "pointed",
    "-2": "dvalley",
    "all": "all"
}


class ChargeAndDischargeDetailsView(APIView):
    """充放电明细"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        query_dict = request.data
        user_id = request.user["user_id"]
        date_str = query_dict.get("date", None)
        master_station_english_names = query_dict.get("station_english_name_set", [])
        peak_valley_type = query_dict.get("peak_valley_type", None)

        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        now = datetime.date.today()

        if peak_valley_type and str(peak_valley_type) not in peak_valley_types.keys():
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": f"峰谷标识不存在！！！"},
                }
            )

        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        if master_station_english_names:
            master_stations = master_stations.filter(
                is_delete=0,
                english_name__in=master_station_english_names
            )

        all_slave_stations = models.StationDetails.objects.filter(master_station__in=master_stations, is_delete=0).all()
        en_name_set = [station.english_name for station in all_slave_stations]
        total_dict = {}

        # accu_charge, accu_discharge = 0, 0
        # for slave_station in all_slave_stations:
        #     if slave_station.slave != 0:
        #         accu_charge_, accu_discharge_ = get_station_accu_charge_discharge(slave_station, now)
        #         accu_charge += accu_charge_
        #         accu_discharge += accu_discharge_
        #
        # total_dict['accu_charge'] = list(charge_discharge_conversion_sum(accu_charge))
        # total_dict['accu_discharge'] = list(charge_discharge_conversion_sum(accu_discharge))

        # 默认查询所有历史数据
        if not date_str:
            success_log.info("充放电量明细:无查询条件获取所有历史数据")
            # total_dict["year"] = now.year
            # total_dict["datetime"] = str(now.year) + '年'
            total_dict['total_sum_charge'] = 0
            total_dict['total_sum_discharge'] = 0

            detail_list = []
            # report = models.StationIncome.objects.filter(master_station__in=master_stations)

            # year = now.year

            # is_need_stop = False

            # while year:
            if peak_valley_type is None or peak_valley_type == "all":
                # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                select_sql = ("SELECT day, sum(v_chag) as charge, sum(v_disg) as discharge FROM ads_report_chag_disg_union_1d"
                                " where station in %s and station_type<=1 GROUP BY day ORDER BY day DESC")
            else:
                charge_s = peak_valley_types[str(peak_valley_type)] + '_chag'
                discharge_s = peak_valley_types[str(peak_valley_type)] + '_disg'
                # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                select_sql = (f"SELECT day, sum({charge_s}) as charge, sum({discharge_s}) as discharge FROM "
                                f"ads_report_chag_disg_union_1d where station in %s and station_type<=1 GROUP BY day ORDER BY day DESC")
            results = ads_db_tool.select_many(select_sql, en_name_set)
            # if not results:
            #     break
            month_data = {}
            for item in results:
                month_key = item.get("day").strftime('%Y-%m')
                if month_key in month_data:
                    month_data[month_key].append(item)
                else:
                    month_data[month_key] = [item]
            for key,results in month_data.items():
                date_obj = datetime.datetime.strptime(key, "%Y-%m")
                current_month_ = date_obj.month
                return_dic = {
                    "month": current_month_,
                    "year": date_obj.year,
                    "datetime": str(date_obj.year) + '-' + str(current_month_),
                }
                if peak_valley_type is None or peak_valley_type == "all":
                    from copy import deepcopy
                    results_copy = deepcopy(list(results))
                    results = sorted(results, key=lambda x: x['day'], reverse=True)
                    total_charge_list = []
                    total_discharge_list = []
                    for i in results:
                        # 每日充放
                        day_charge = i["charge"]
                        day_discharge = i["discharge"]

                        # 尖峰平谷充放电＜1时置0
                        # if peak_valley_type is not None and peak_valley_type != "all":
                        #     day_charge = day_charge if day_charge >= 1 else 0
                        #     day_discharge = day_discharge if day_discharge >= 1 else 0

                        i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                        i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                        total_charge_list.append(day_charge)
                        total_discharge_list.append(day_discharge)

                    return_dic["day"] = list(results)

                    return_dic["sum_month_charge"] = list(
                        charge_discharge_conversion_sum(sum(total_charge_list)))
                    return_dic["sum_month_discharge"] = list(
                        charge_discharge_conversion_sum(sum(total_discharge_list)))

                    total_dict['total_sum_charge'] += sum(total_charge_list)
                    total_dict['total_sum_discharge'] += sum(total_discharge_list)

                    detail_list.append(return_dic)
                else:
                    from copy import deepcopy
                    results_copy = deepcopy(list(results))
                    results = sorted(results, key=lambda x: x['day'], reverse=True)
                    total_charge_list = []
                    total_discharge_list = []
                    for i in results:
                        # 每日充放
                        day_charge = i["charge"]
                        day_discharge = i["discharge"]

                        # 尖峰平谷充放电＜1时置0
                        # if peak_valley_type is not None and peak_valley_type != "all":
                        #     day_charge = day_charge if day_charge >= 1 else 0
                        #     day_discharge = day_discharge if day_discharge >= 1 else 0

                        i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                        i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                        total_charge_list.append(day_charge)
                        total_discharge_list.append(day_discharge)

                    if peak_valley_type is None or peak_valley_type == "all":
                        return_dic["day"] = list(results)

                    # 每天的尖峰、峰充电，谷放电数值介于-1到1之间，置为0（其他时段电量显示规则是多少显示多少，例如：平放0.9，则显示0.9）
                    else:
                        for j in results_copy:
                            # 每日充放
                            if int(peak_valley_type) == 2 or int(peak_valley_type) == 1:
                                day_charge = j["charge"] if not -1 < j['charge'] < 1 else 0
                                day_discharge = j["discharge"]
                            elif int(peak_valley_type) == -1:
                                day_charge = j["charge"]
                                day_discharge = j["discharge"] if not -1 < j['discharge'] < 1 else 0
                            else:
                                day_charge = j["charge"]
                                day_discharge = j["discharge"]

                            j["charge"] = list(charge_discharge_conversion_sum(day_charge))
                            j["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                        return_dic["day"] = results_copy

                    return_dic["sum_month_charge"] = list(charge_discharge_conversion_sum(sum(total_charge_list)))
                    return_dic["sum_month_discharge"] = list(charge_discharge_conversion_sum(sum(total_discharge_list)))

                    total_dict['total_sum_charge'] += sum(total_charge_list)
                    total_dict['total_sum_discharge'] += sum(total_discharge_list)

                    detail_list.append(return_dic)

            # year -= 1
            total_dict['total_sum_charge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_charge']))
            total_dict['total_sum_discharge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_discharge']))
                # 原逻辑
            #     months = now.month if year == now.year else 12
            #     current_month_ = months
            #     # for i in range(1, months + 1):
            #     while months >= current_month_ >= 1:
            #
            #         date_str_d = str(year) + "-0" + str(current_month_) if current_month_ < 10 else str(year) + "-" + str(current_month_)
            #         return_dic = {
            #             "month": current_month_,
            #             "year": year,
            #             "datetime": str(year) + '-' + str(current_month_),
            #         }
            #         # month_ins = income_ins.filter(income_date__month=i, income_date__year=now.year)
            #         if peak_valley_type is None or peak_valley_type == "all":
            #             # 改查新综合过结算表和计量表的新1d冻结表：表名未定
            #             select_sql = ("SELECT day, sum(v_chag) as charge, sum(v_disg) as discharge FROM ads_report_chag_disg_union_1d"
            #                           " where station in %s and station_type<=1 and day >= %s and day <= %s GROUP BY day ORDER BY day")
            #         else:
            #             charge_s = peak_valley_types[str(peak_valley_type)] + '_chag'
            #             discharge_s = peak_valley_types[str(peak_valley_type)] + '_disg'
            #             # 改查新综合过结算表和计量表的新1d冻结表：表名未定
            #             select_sql = (f"SELECT day, sum({charge_s}) as charge,"
            #                           f" sum({discharge_s}) as discharge FROM"
            #                           f" ads_report_chag_disg_union_1d where station in %s and station_type<=1 and day >= %s and day <= %s"
            #                           f" GROUP BY day ORDER BY day")
            #
            #         start_time = datetime.date(int(date_str_d.split('-')[0]), int(date_str_d.split('-')[1]), 1).strftime('%Y-%m-%d 00:00:00')
            #         end_time = datetime.date(int(date_str_d.split('-')[0]), int(date_str_d.split('-')[1]), calendar.monthrange(int(date_str_d.split('-')[0]), int(date_str_d.split('-')[1]))[1]).strftime('%Y-%m-%d 23:59:59')
            #         results = ads_db_tool.select_many(select_sql, en_name_set, start_time, end_time)
            #
            #         return_dic["sum_month_charge"] = [0.00, 'kWh']
            #         return_dic["sum_month_discharge"] = [0.00, 'kWh']
            #         # return_dic["day"] = day_ins
            #         if results:
            #             if peak_valley_type is None or peak_valley_type == "all":
            #                 from copy import deepcopy
            #                 results_copy = deepcopy(list(results))
            #                 results = sorted(results, key=lambda x: x['day'], reverse=True)
            #                 total_charge_list = []
            #                 total_discharge_list = []
            #                 for i in results:
            #                     # 每日充放
            #                     day_charge = i["charge"]
            #                     day_discharge = i["discharge"]
            #
            #                     # 尖峰平谷充放电＜1时置0
            #                     # if peak_valley_type is not None and peak_valley_type != "all":
            #                     #     day_charge = day_charge if day_charge >= 1 else 0
            #                     #     day_discharge = day_discharge if day_discharge >= 1 else 0
            #
            #                     i["charge"] = list(charge_discharge_conversion_sum(day_charge))
            #                     i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))
            #
            #                     total_charge_list.append(day_charge)
            #                     total_discharge_list.append(day_discharge)
            #
            #                 return_dic["day"] = list(results)
            #
            #                 return_dic["sum_month_charge"] = list(
            #                     charge_discharge_conversion_sum(sum(total_charge_list)))
            #                 return_dic["sum_month_discharge"] = list(
            #                     charge_discharge_conversion_sum(sum(total_discharge_list)))
            #
            #                 total_dict['total_sum_charge'] += sum(total_charge_list)
            #                 total_dict['total_sum_discharge'] += sum(total_discharge_list)
            #
            #                 detail_list.append(return_dic)
            #             else:
            #                 from copy import deepcopy
            #                 results_copy = deepcopy(list(results))
            #                 results = sorted(results, key=lambda x: x['day'], reverse=True)
            #                 total_charge_list = []
            #                 total_discharge_list = []
            #                 for i in results:
            #                     # 每日充放
            #                     day_charge = i["charge"]
            #                     day_discharge = i["discharge"]
            #
            #                     # 尖峰平谷充放电＜1时置0
            #                     # if peak_valley_type is not None and peak_valley_type != "all":
            #                     #     day_charge = day_charge if day_charge >= 1 else 0
            #                     #     day_discharge = day_discharge if day_discharge >= 1 else 0
            #
            #                     i["charge"] = list(charge_discharge_conversion_sum(day_charge))
            #                     i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))
            #
            #                     total_charge_list.append(day_charge)
            #                     total_discharge_list.append(day_discharge)
            #
            #                 if peak_valley_type is None or peak_valley_type == "all":
            #                     return_dic["day"] = list(results)
            #
            #                 # 每天的尖峰、峰充电，谷放电数值介于-1到1之间，置为0（其他时段电量显示规则是多少显示多少，例如：平放0.9，则显示0.9）
            #                 else:
            #                     for j in results_copy:
            #                         # 每日充放
            #                         if int(peak_valley_type) == 2 or int(peak_valley_type) == 1:
            #                             day_charge = j["charge"] if not -1 < j['charge'] < 1 else 0
            #                             day_discharge = j["discharge"]
            #                         elif int(peak_valley_type) == -1:
            #                             day_charge = j["charge"]
            #                             day_discharge = j["discharge"] if not -1 < j['discharge'] < 1 else 0
            #                         else:
            #                             day_charge = j["charge"]
            #                             day_discharge = j["discharge"]
            #
            #                         j["charge"] = list(charge_discharge_conversion_sum(day_charge))
            #                         j["discharge"] = list(charge_discharge_conversion_sum(day_discharge))
            #
            #                     return_dic["day"] = results_copy
            #
            #                 return_dic["sum_month_charge"] = list(charge_discharge_conversion_sum(sum(total_charge_list)))
            #                 return_dic["sum_month_discharge"] = list(charge_discharge_conversion_sum(sum(total_discharge_list)))
            #
            #                 total_dict['total_sum_charge'] += sum(total_charge_list)
            #                 total_dict['total_sum_discharge'] += sum(total_discharge_list)
            #
            #                 detail_list.append(return_dic)
            #
            #         else:
            #             is_need_stop = True
            #             break
            #
            #         current_month_ -= 1
            #
            #     if is_need_stop:
            #         break
            #
            #     year -= 1
            #
            # total_dict['total_sum_charge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_charge']))
            # total_dict['total_sum_discharge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_discharge']))

        # 条件查询
        else:
            success_log.info("充放电明细:条件查询获取数据")
            detail_list = []

            # 2023
            if len(date_str) == 1:
                year = date_str[0]
                total_dict["year"] = year
                total_dict["datetime"] = str(year) + '年'
                total_dict['total_sum_charge'] = 0
                total_dict['total_sum_discharge'] = 0

                # months = 12
                # 当前年月
                # if year == now.year:
                #     months = now.month

            # for i in range(1, months + 1):
                # date_str_ = str(year) + "-0" + str(i) if i < 10 else str(year) + "-" + str(i)
                # return_dic = {
                #     "month": i,
                #     "year": year,
                #     "datetime": str(year) + '-' + str(i),
                # }

                if peak_valley_type is None or peak_valley_type == "all":
                    # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                    select_sql = (
                        "SELECT day, sum(v_chag) as charge, sum(v_disg) as discharge FROM ads_report_chag_disg_union_1d"
                        " where station in %s and station_type<=1 and YEAR(day) = %s GROUP BY day ORDER BY day")
                else:
                    charge_s = peak_valley_types[str(peak_valley_type)] + '_chag'
                    discharge_s = peak_valley_types[str(peak_valley_type)] + '_disg'
                    # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                    select_sql = (f"SELECT day, sum({charge_s}) as charge,"
                                    f" sum({discharge_s}) as discharge FROM"
                                    f" ads_report_chag_disg_union_1d where station in %s and station_type<=1 and YEAR(day) = %s "
                                    f" GROUP BY day ORDER BY day")
                _results = ads_db_tool.select_many(select_sql, en_name_set, year)
                month_data = {}
                for item in _results:
                    month_key = item.get("day").strftime('%Y-%m')
                    if month_key in month_data:
                        month_data[month_key].append(item)
                    else:
                        month_data[month_key] = [item]
                for key,results in month_data.items():
                    date_obj = datetime.datetime.strptime(key, "%Y-%m")
                    current_month_ = date_obj.month
                    return_dic = {
                        "month": current_month_,
                        "year": date_obj.year,
                        "datetime": str(date_obj.year) + '-' + str(current_month_),
                    }
                    if peak_valley_type is None or peak_valley_type == "all":
                        # from copy import deepcopy
                        # results_copy = deepcopy(list(results))
                        results = sorted(results, key=lambda x: x['day'], reverse=True)
                        total_charge_list = []
                        total_discharge_list = []
                        for i in results:
                            # 每日充放
                            day_charge = i["charge"]
                            day_discharge = i["discharge"]

                            # 尖峰平谷充放电＜1时置0
                            # if peak_valley_type is not None and peak_valley_type != "all":
                            #     day_charge = day_charge if day_charge >= 1 else 0
                            #     day_discharge = day_discharge if day_discharge >= 1 else 0

                            i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                            i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                            total_charge_list.append(day_charge)
                            total_discharge_list.append(day_discharge)

                        # return_dic["day"] = list(results)

                        return_dic["sum_month_charge"] = list(
                            charge_discharge_conversion_sum(sum(total_charge_list)))
                        return_dic["sum_month_discharge"] = list(
                            charge_discharge_conversion_sum(sum(total_discharge_list)))

                        total_dict['total_sum_charge'] += sum(total_charge_list)
                        total_dict['total_sum_discharge'] += sum(total_discharge_list)

                        # detail_list.append(return_dic)
                    else:
                        results = sorted(results, key=lambda x: x['day'], reverse=True)
                        total_charge_list = []
                        total_discharge_list = []
                        for i in results:
                            # 每日充放
                            day_charge = i["charge"]
                            day_discharge = i["discharge"]

                            # 尖峰平谷充放电＜1时置0
                            # if peak_valley_type is not None and peak_valley_type != "all":
                            #     day_charge = day_charge if day_charge >= 1 else 0
                            #     day_discharge = day_discharge if day_discharge >= 1 else 0

                            i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                            i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                            total_charge_list.append(day_charge)
                            total_discharge_list.append(day_discharge)

                        # return_dic["day"] = list(results)
                        return_dic["sum_month_charge"] = list(charge_discharge_conversion_sum(sum(total_charge_list)))
                        return_dic["sum_month_discharge"] = list(
                            charge_discharge_conversion_sum(sum(total_discharge_list)))

                        total_dict['total_sum_charge'] += sum(total_charge_list)
                        total_dict['total_sum_discharge'] += sum(total_discharge_list)
                    detail_list.append(return_dic)
                total_dict['total_sum_charge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_charge']))
                total_dict['total_sum_discharge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_discharge']))
                detail_list.reverse()

            # 2023-01
            if len(date_str) == 2:
                year = date_str[0]
                month = date_str[1]
                total_dict["datetime"] = str(year) + '年' + str(month) + '月'
                total_dict['total_sum_charge'] = 0
                total_dict['total_sum_discharge'] = 0

                date_str_ = str(year) + "-0" + str(month) if int(month) < 10 else str(year) + "-" + str(month)
                return_dic = {
                    "income_date__month": month,
                    "income_date__year": year,
                    "datetime": str(year) + '年' + str(month) + '月'
                }
                if peak_valley_type is None or peak_valley_type == "all":
                    # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                    select_sql = (
                        "SELECT day, sum(v_chag) as charge, sum(v_disg) as discharge FROM ads_report_chag_disg_union_1d"
                        " where station in %s and station_type<=1 and year(day) = %s and month(day) = %s GROUP BY day ORDER BY day")
                else:
                    charge_s = peak_valley_types[str(peak_valley_type)] + '_chag'
                    discharge_s = peak_valley_types[str(peak_valley_type)] + '_disg'
                    # 改查新综合过结算表和计量表的新1d冻结表：表名未定
                    select_sql = (f"SELECT day, sum({charge_s}) as charge,"
                                  f" sum({discharge_s}) as discharge FROM"
                                  f" ads_report_chag_disg_union_1d where station in %s and station_type<=1 and year(day) = %s and month(day) = %s"
                                  f" GROUP BY day ORDER BY day")

                start_time = datetime.date(int(date_str_.split('-')[0]), int(date_str_.split('-')[1]), 1).strftime(
                    '%Y-%m-%d 00:00:00')
                end_time = datetime.date(int(date_str_.split('-')[0]), int(date_str_.split('-')[1]),
                                         calendar.monthrange(int(date_str_.split('-')[0]),
                                                             int(date_str_.split('-')[1]))[1]).strftime(
                    '%Y-%m-%d 23:59:59')
                results = ads_db_tool.select_many(select_sql, en_name_set, year, month)

                return_dic["sum_month_charge"] = [0.00, 'kWh']
                return_dic["sum_month_discharge"] = [0.00, 'kWh']
                # return_dic["day"] = day_ins
                if results:
                    if peak_valley_type is None or peak_valley_type == "all":
                        # from copy import deepcopy
                        # results_copy = deepcopy(list(results))
                        results = sorted(results, key=lambda x: x['day'], reverse=True)
                        total_charge_list = []
                        total_discharge_list = []
                        for i in results:
                            # 每日充放
                            day_charge = i["charge"]
                            day_discharge = i["discharge"]

                            # 尖峰平谷充放电＜1时置0
                            # if peak_valley_type is not None and peak_valley_type != "all":
                            #     day_charge = day_charge if day_charge >= 1 else 0
                            #     day_discharge = day_discharge if day_discharge >= 1 else 0

                            i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                            i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                            total_charge_list.append(day_charge)
                            total_discharge_list.append(day_discharge)

                        return_dic["day"] = list(results)

                        return_dic["sum_month_charge"] = list(
                            charge_discharge_conversion_sum(sum(total_charge_list)))
                        return_dic["sum_month_discharge"] = list(
                            charge_discharge_conversion_sum(sum(total_discharge_list)))

                        total_dict['total_sum_charge'] += sum(total_charge_list)
                        total_dict['total_sum_discharge'] += sum(total_discharge_list)

                        # detail_list.append(return_dic)
                    else:
                        from copy import deepcopy
                        results_copy = deepcopy(results)

                        results = sorted(results, key=lambda x: x['day'], reverse=True)
                        total_charge_list = []
                        total_discharge_list = []
                        for i in results:
                            # 每日充放
                            day_charge = i["charge"]
                            day_discharge = i["discharge"]

                            # 尖峰平谷充放电＜1时置0
                            # if peak_valley_type is not None and peak_valley_type != "all":
                            #     day_charge = day_charge if day_charge >= 1 else 0
                            #     day_discharge = day_discharge if day_discharge >= 1 else 0

                            i["charge"] = list(charge_discharge_conversion_sum(day_charge))
                            i["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                            total_charge_list.append(day_charge)
                            total_discharge_list.append(day_discharge)

                        if peak_valley_type is None or peak_valley_type == "all":
                            return_dic["day"] = list(results)

                        # 每天的尖峰、峰充电，谷放电数值介于-1到1之间，置为0（其他时段电量显示规则是多少显示多少，例如：平放0.9，则显示0.9）
                        else:
                            for j in results_copy:

                                # 每日充放
                                if int(peak_valley_type) == 2 or int(peak_valley_type) == 1:
                                    day_charge = j["charge"] if not -1 < j['charge'] < 1 else 0
                                    day_discharge = j["discharge"]
                                elif int(peak_valley_type) == -1:
                                    day_charge = j["charge"]
                                    day_discharge = j["discharge"] if not -1 < j['discharge'] < 1 else 0
                                else:
                                    day_charge = j["charge"]
                                    day_discharge = j["discharge"]

                                j["charge"] = list(charge_discharge_conversion_sum(day_charge))
                                j["discharge"] = list(charge_discharge_conversion_sum(day_discharge))

                            return_dic["day"] = results_copy

                        return_dic["sum_month_charge"] = list(charge_discharge_conversion_sum(sum(total_charge_list)))
                        return_dic["sum_month_discharge"] = list(
                            charge_discharge_conversion_sum(sum(total_discharge_list)))

                        total_dict['total_sum_charge'] += sum(total_charge_list)
                        total_dict['total_sum_discharge'] += sum(total_discharge_list)
                else:
                    return_dic["day"] = []

                total_dict['total_sum_charge'] = list(charge_discharge_conversion_sum(total_dict['total_sum_charge']))
                total_dict['total_sum_discharge'] = list(
                    charge_discharge_conversion_sum(total_dict['total_sum_discharge']))
                detail_list = return_dic

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": detail_list, "total_dict": total_dict},
            }
        )


"""
收益明细
"""""


class IncomeDetailsView(APIView):
    """收益明细"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        query_dict = request.data
        user_id = request.user["user_id"]
        date_str = query_dict.get("date", None)
        master_station_english_names = query_dict.get("station_english_name_set", [])
        income_type = query_dict.get("income_type", None)

        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        now = datetime.date.today()

        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        if master_station_english_names:
            master_stations = master_stations.filter(
                is_delete=0,
                english_name__in=master_station_english_names
            )

        all_slave_stations = models.StationDetails.objects.filter(is_delete=0, master_station__in=master_stations).all()
        if not all_slave_stations:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )
        en_name_set = [station.english_name for station in all_slave_stations]
        if len(en_name_set) == 1:
            en_name_set.append(-1)
        # 默认查询当年数据
        if not date_str:
            success_log.info("收益明细:无查询条件获取该年数据")
            total_dict = {}
            total_dict['total_sum'] = 0

            income_sql = f"""SELECT
                                 SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(day_income) as day_income,
                                    sum(demand_side_response) as demand_side_response
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                     station in {tuple(en_name_set)} 
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY DESC
                                """

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                try:
                    ads_cursor.execute(income_sql)
                    income_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error("工作台-收益明细查询失败：", e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！',
                            }
                        }
                    )

            all_income = 0  # 总收益
            res = {}
            for i in income_res:
                peak_load_shiftings = i[1] if i[1] not in EMPTY_STR_LIST else 0
                demand_side_responses = i[2] if i[2] not in EMPTY_STR_LIST else 0
                all_income += (peak_load_shiftings + demand_side_responses)
                t = i[0][:7]
                if res.get(t):
                    res[t]['day'].append({
                        'income_date': i[0],
                        'demand_side_responses': round(demand_side_responses,
                                                       2) if demand_side_responses != 0 else 0,
                        'peak_load_shiftings': round(peak_load_shiftings, 2) if peak_load_shiftings != 0 else 0
                    })
                    res[t]['sum_month'] += (peak_load_shiftings + demand_side_responses)
                else:
                    res[t] = {
                        'income_date__month': int(t.split('-')[1]),
                        'income_date__year': int(t.split('-')[0]),
                        'sum_month': (peak_load_shiftings + demand_side_responses),
                        'day': [
                            {
                                'income_date': i[0],
                                'demand_side_responses': round(demand_side_responses, 2) if demand_side_responses != 0 else 0,
                                'peak_load_shiftings': round(peak_load_shiftings, 2) if peak_load_shiftings != 0 else 0
                            }
                        ]

                    }
            detail_list = []
            for k, info in res.items():
                detail_list.append(info)
            total_dict = {'total_sum': round(float(all_income), 2)} if all_income != 0 else '--'


        # 条件查询
        else:
            success_log.info("收益明细:条件查询获取数据")
            total_dict = {}
            if len(date_str) == 1:
                start_time = f"{date_str[0]}-01-01"
                end_time = f"{date_str[0]}-12-31"
                total_dict["year"] = date_str[0]
                total_dict["datetime"] = str(date_str[0]) + '年'
                total_dict['total_sum'] = '--'
            else:
                start_time = f"{date_str[0]}-{date_str[1]}-01"
                s_time = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                end_time = ((s_time + datetime.timedelta(days=31)).replace(day=1) - datetime.timedelta(
                    days=1)).strftime('%Y-%m-%d')
                total_dict["datetime"] = str(date_str[0]) + '年' + str(date_str[1]) + '月'
                total_dict['total_sum'] = '--'

            income_sql = f"""SELECT
                                 SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(day_income) as day_income,
                                    sum(demand_side_response) as demand_side_response
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                     station in {tuple(en_name_set)} AND
                                     day <= '{end_time}' and day >= '{start_time}' 
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY DESC
                                """

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                try:
                    ads_cursor.execute(income_sql)
                    income_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error("工作台-收益明细查询失败：", e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！',
                            }
                        }
                    )

            all_income = 0  # 总收益
            res = {}
            for i in income_res:
                peak_load_shiftings = i[1] if i[1] not in EMPTY_STR_LIST else 0
                demand_side_responses = i[2] if i[2] not in EMPTY_STR_LIST else 0
                all_income += (peak_load_shiftings + demand_side_responses)
                t = i[0][:7]
                if res.get(t):
                    res[t]['day'].append({
                        'income_date': i[0],
                        'demand_side_responses': round(demand_side_responses,
                                                       2) if demand_side_responses != 0 else 0,
                        'peak_load_shiftings': round(peak_load_shiftings, 2) if peak_load_shiftings != 0 else 0
                    })
                    res[t]['sum_month'] += (peak_load_shiftings + demand_side_responses)
                else:
                    res[t] = {
                        'income_date__month': int(t.split('-')[1]),
                        'income_date__year': int(t.split('-')[0]),
                        'sum_month': (peak_load_shiftings + demand_side_responses),
                        'day': [
                            {
                                'income_date': i[0],
                                'demand_side_responses': round(demand_side_responses,
                                                               2) if demand_side_responses != 0 else 0,
                                'peak_load_shiftings': round(peak_load_shiftings, 2) if peak_load_shiftings != 0 else 0
                            }
                        ]
                    }
                    if len(date_str) == 2:
                        res[t]['datetime'] = str(date_str[0]) + '年' + str(date_str[1]) + '月'
            detail_list = []
            for k, info in res.items():
                detail_list.append(info)
            total_dict['total_sum'] =  round(float(all_income), 2) if all_income != 0 else '--'


        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": detail_list, "total_dict": total_dict},
            }
        )


class IncomeAddView(APIView):
    """添加收益明细"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = AddIncomesSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("添加收益明细:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        datas = ser.validated_data["data"]

        for data in datas:
            english_name = data["english_name"]

            master_stations = models.MaterStation.objects.filter(english_name=english_name, is_delete=0)
            if master_stations.exists():
                master_station = master_stations.first()
            else:
                return Response(
                                    {
                                        "code": common_response_code.NO_DATA,
                                        "data": {"message": "success", "detail": f"(主)电站{english_name}不存在"},
                                    }
                                )

            notes = data.get("notes")
            date = data["date"]
            income_type = data["income_type"]
            day_income = data["day_income"]

            income_ins_exist = models.StationIncome.objects.filter(income_date=date,
                                                                   master_station=master_station).exists()
            if not income_ins_exist:
                if income_type == 1:
                    models.StationIncome.objects.create(
                        master_station=master_station,
                        income_date=date,
                        peak_load_shifting=day_income,
                        notes=notes,
                        record=1,
                    )
                if income_type == 2:
                    models.StationIncome.objects.create(
                        master_station=master_station,
                        income_date=date,
                        demand_side_response=day_income,
                        notes=notes,
                        record=1,
                    )
            if income_ins_exist:
                if income_type == 1:
                    models.StationIncome.objects.filter(master_station=master_station, income_date=date).update(
                        peak_load_shifting=day_income, record=1
                    )
                if income_type == 2:
                    models.StationIncome.objects.filter(master_station=master_station, income_date=date).update(
                        demand_side_response=day_income, record=1
                    )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "收益添加成功"},
            }
        )


"""
度电收益明细
"""""


class PreIncomeDetailsView(APIView):
    """度电收益明细"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @staticmethod
    def calculate_monthly_summary(day_ins, results):
        month_income_list = []
        month_discharge_list = []
        if results:
            for item in results:
                if item["discharge"]:
                    month_discharge_list.append(item["discharge"])
        for i in day_ins:
            # 每日收益
            day_income = i["peak_load_shiftings"] + i["demand_side_responses"]

            if results:
                for result in results:
                    if result["day"] == i['income_date']:
                        day_discharge = result["discharge"]
                        if day_discharge:
                            i["pre_income"] = round(day_income / day_discharge, 3)
                            # month_discharge_list.append(day_discharge)
                            i['day_discharge'] = day_discharge
                        else:
                            i["pre_income"] = '--'
                            i['day_discharge'] = '--'
                        break
                else:
                    i["pre_income"] = '--'
                    i['day_discharge'] = '--'

            else:
                i["pre_income"] = '--'
                i['day_discharge'] = '--'

            month_income_list.append(day_income)

        return day_ins, month_income_list, month_discharge_list

    def post(self, request):
        query_dict = request.data
        user_id = request.user["user_id"]
        date_str = query_dict.get("date", None)
        master_station_english_names = query_dict.get("station_english_name_set", [])
        # income_type = query_dict.get("income_type", None)

        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        now = datetime.date.today()

        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        if master_station_english_names:
            master_stations = master_stations.filter(
                is_delete=0,
                english_name__in=master_station_english_names
            )

        all_slave_stations = models.StationDetails.objects.filter(is_delete=0, master_station__in=master_stations).all()
        en_name_set = [station.english_name for station in all_slave_stations]
        if len(en_name_set) == 1:
            en_name_set.append(-1)

        # 默认查询当年数据
        if not date_str:
            success_log.info("度电收益明细:无查询条件获取该年数据")
            income_sql = f"""SELECT
                                incomes.day, ele.v_disg, incomes.day_income
                            FROM
                                (  select 
                                   SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(day_income) as day_income
                                FROM
                                    ads_report_station_income_1d 
                                WHERE
                                     station in {tuple(en_name_set)}
                                GROUP BY
                                DAY 
                                ORDER BY
                                DAY 
                                ) AS incomes
                                LEFT JOIN (
                                SELECT  SUBSTRING( day, 1, 10 ) as DAY,
                                    sum(v_disg) as v_disg
                                FROM
                                    ads_report_chag_disg_union_1d 
                                WHERE
                                     station in {tuple(en_name_set)} AND
                                     station_type <= 1 
                                GROUP BY
                                DAY 
                                ORDER BY
                            DAY 
                                ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day DESC"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                try:

                    ads_cursor.execute(income_sql)
                    income_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error("工作台-度电收益查询失败：", e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！',
                            }
                        }
                    )

            all_income, all_disg = 0, 0  # 总收益、总放电量
            res = {}
            for i in income_res:
                income = i[2] if i[2] not in EMPTY_STR_LIST else 0
                disg = i[1] if i[1] not in EMPTY_STR_LIST else 0
                all_income += income
                all_disg += disg
                t = i[0][:7]
                if res.get(t):
                    res[t]['day'].append({
                                'income_date': i[0],
                                'pre_income': round(income / disg, 3) if disg != 0 else  '--'
                            })
                    res[t]['income_month'] += income
                    res[t]['disg_month'] += disg
                else:
                    res[t] = {
                                'income_date__month': int(t.split('-')[1]),
                                'income_date__year': int(t.split('-')[0]),
                                'income_month': income,
                                'disg_month': disg,
                                'sum_month': '--',
                                'day': [
                                    {
                                        'income_date': i[0],
                                        'pre_income': round(income / disg, 3) if disg != 0 else '--'
                                    }
                                ]

                            }
            detail_list = []
            for k, info in res.items():
                info['sum_month'] = round(info['income_month'] / info['disg_month'], 3) if info['disg_month'] != 0 else '--'
                detail_list.append(info)


            total_dict = {'total_sum': round(all_income / all_disg, 3)} if all_disg != 0 else '--'

        # 条件查询
        else:
            success_log.info("收益明细:条件查询获取数据")
            total_dict = {}
            if len(date_str) == 1:
                start_time = f"{date_str[0]}-01-01"
                end_time = f"{date_str[0]}-12-31"
                total_dict["year"] = date_str[0]
                total_dict["datetime"] = str(date_str[0]) + '年'
                total_dict['total_sum'] = '--'
            else:
                start_time = f"{date_str[0]}-{date_str[1]}-01"
                s_time = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                end_time = ((s_time + datetime.timedelta(days=31)).replace(day=1) - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                total_dict["datetime"] = str(date_str[0]) + '年' + str(date_str[1]) + '月'
                total_dict['total_sum'] = '--'
            income_sql = f"""SELECT
                                            incomes.day, ele.v_disg, incomes.day_income
                                        FROM
                                            (  select 
                                               SUBSTRING( day, 1, 10 ) as DAY,
                                                sum(day_income) as day_income
                                            FROM
                                                ads_report_station_income_1d 
                                            WHERE
                                                 station in {tuple(en_name_set)} AND
                                                 day <= '{end_time}' and day >= '{start_time}' 
                                            GROUP BY
                                            DAY 
                                            ORDER BY
                                            DAY 
                                            ) AS incomes
                                            LEFT JOIN (
                                            SELECT  SUBSTRING( day, 1, 10 ) as DAY,
                                                sum(v_disg) as v_disg
                                            FROM
                                                ads_report_chag_disg_union_1d 
                                            WHERE
                                                 station in {tuple(en_name_set)} AND
                                                 day <= '{end_time}' and day >= '{start_time}' AND
                                                 station_type <= 1 
                                            GROUP BY
                                            DAY 
                                            ORDER BY
                                        DAY 
                                            ) AS ele ON ele.DAY = incomes.DAY ORDER BY incomes.day DESC"""

            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                try:
                    ads_cursor.execute(income_sql)
                    income_res = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error("工作台-度电收益查询失败：", e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！',
                            }
                        }
                    )

            all_income, all_disg = 0, 0  # 总收益、总放电量
            res = {}
            for i in income_res:
                income = i[2] if i[2] not in EMPTY_STR_LIST else 0
                disg = i[1] if i[1] not in EMPTY_STR_LIST else 0
                all_income += income
                all_disg += disg
                t = i[0][:7]
                if res.get(t):
                    res[t]['day'].append({
                        'income_date': i[0],
                        'pre_income': round(income / disg, 3) if disg != 0 else '--'
                    })
                    res[t]['income_month'] += income
                    res[t]['disg_month'] += disg
                else:
                    res[t] = {
                        'income_date__month': int(t.split('-')[1]),
                        'income_date__year': int(t.split('-')[0]),
                        'income_month': income,
                        'disg_month': disg,
                        'sum_month': '--',
                        'day': [
                            {
                                'income_date': i[0],
                                'pre_income': round(income / disg, 3) if disg != 0 else '--'
                            }
                        ]
                    }
                    if len(date_str) == 2:
                        res[t]['datetime'] = str(date_str[0]) + '年' + str(date_str[1]) + '月'

            detail_list = []
            for k, info in res.items():
                info['sum_month'] = round(info['income_month'] / info['disg_month'], 3) if info[
                                                                                               'disg_month'] != 0 else '--'
                detail_list.append(info)

            total_dict['total_sum'] = round(all_income / all_disg, 3) if all_disg != 0 else '--'



        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": detail_list, "total_dict": total_dict},
            }
        )


"""
单位电价配置
"""""


class CustomizationView(APIView):
    """代理购电价格查询下拉选项"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        provinces = models.ElectricityProvince.objects.all()
        result = {}
        for province in provinces:
            month = datetime.datetime.now().strftime('%Y-%m')
            # provinces_ins = province.peakvalley_set.filter(year_month=month).values("type", "level").annotate(
            #     province=F("province"))
            provinces_ins = models.PeakValleyNew.objects.filter(year_month=month, province=province).values("province","type", "level").distinct()
            ser = PeakValleySerializer(instance=provinces_ins, many=True)
            result[province.name] = ser.data
        return Response({"code": common_response_code.SUCCESS, "data": result})


class CustomizationDetailView(APIView):
    """当月电价时刻查询"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        # ser = CustomizationDetailSerializer(data=request.data)
        # if not ser.is_valid():
        #     return Response(
        #         {
        #             "code": common_response_code.FIELD_ERROR,
        #             "data": {"message": "error", "detail": ser.errors},
        #         }
        #     )
        # query_dic = request.data
        # province_ins = models.ElectricityProvince.objects.filter(id=ser.validated_data["id"]).first()
        # ser.validated_data.pop("id")
        # query_dic.pop("id")
        # query_dic['year_month'] = datetime.datetime.now().month
        #
        # price_ins = models.PeakValley.objects.filter(province=province_ins, **query_dic).first()
        # ins_ser = CustomizationDetailSerializer(instance=price_ins)
        lang = request.headers.get("lang", 'zh')
        query_dic = request.data
        now = datetime.datetime.now()
        id = query_dic.get('id')
        type_ = query_dic.get('type')
        level = query_dic.get('level')
        month = query_dic.get('year_month', now.month)

        try:
            month_str = now.replace(month=month).strftime('%Y-%m')
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": 'error', "detail": "参数校验不通过，请检查月份参数" if lang == 'zh' else
                    "Parameter validation failed, please check."},
                }
            )

        if not all([id, type_, level, month]):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": 'error', "detail": "参数校验不通过，请检查" if lang == 'zh' else
                    "Parameter validation failed, please check."},
                }
            )

        province_ins = models.ElectricityProvince.objects.filter(id=id).first()
        if not province_ins:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": 'error', "detail": "省份不存在" if lang == 'zh' else
                    "Province does not exist."},
                }
            )

        price_instances = models.PeakValleyNew.objects.filter(province=province_ins, type=type_, level=level,
                                                              year_month=month_str, day=1, moment__contains=':00').all()

        detail = {"price_spike": '--',  # 尖峰
                  "price_peak": '--',  # 峰
                  "price_flat": '--',  # 平
                  "price_valley": '--',  # 谷
                  "price_dvalley": '--'}

        if price_instances.exists():
            for i in price_instances:
                key = f'h{int(i.moment.split(":")[0])}'
                value = i.price
                detail[key] = value

                if i.pv == 2:
                    detail['price_spike'] = value
                elif i.pv == 1:
                    detail['price_peak'] = value
                elif i.pv == 0:
                    detail['price_flat'] = value
                elif i.pv == -1:
                    detail['price_valley'] = value
                else:
                    detail['price_dvalley'] = value

        return Response({"code": common_response_code.SUCCESS, "data": detail})


        return Response({"code": common_response_code.SUCCESS, "data": ins_ser.data})


class CustomizationAddView(APIView):
    """单位电价：添加"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["station"] = json.loads(request.body.decode()).get("station", None)

    def post(self, request):
        start = request.data["start"]
        end = request.data["end"]
        name = request.data["name"]

        try:

            if not name:
                raise Exception("单位电价名称不能为空")

            if not all([start, end]):
                raise Exception("开始时间和结束时间不能为空")
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )

        ser = CustomizationAddSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "有参数为空，请重新选择或输入", "detail": "有参数为空，请重新选择或输入"},
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )

        stations_list = ser.validated_data.pop("stations_list")
        stations_names = [models.MaterStation.objects.get(id=station_id, is_delete=0).name for station_id in stations_list]
        stations_names_str = ','.join(stations_names)

        uu = str(uuid.uuid4())
        r = ''
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)

        # 关联并网点不为空时
        if stations_list:
            for station_id in stations_list:
                m_station = models.MaterStation.objects.get(id=station_id, is_delete=0)

                ins = models.UnitPrice.objects.create(station=m_station, user=user_ins, uid=uu,
                                                      stations_name=stations_names_str,
                                                      project=m_station.project, en_name=ser.validated_data.get('name'),
                                                      en_note=ser.validated_data.get('note'), **ser.validated_data)

                # 异步翻译
                pdr_data = {'id': ins.id,
                            'table': 't_unit_price',
                            'update_data': {'name': ser.validated_data.get('name')}}
                if ser.validated_data.get('note'):
                    pdr_data['update_data']['note'] = ser.validated_data.get('note')

                pub_name = 'en_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

                r = ins.uid
        # 未选择关联的并网点则新建一条不关联的单位电价
        else:
            ins = models.UnitPrice.objects.create(user=user_ins, uid=uu, **ser.validated_data)
            r = ins.uid
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": "单位电价电价添加成功", "uid": r},
            }
        )


class CustomizationHistoryView(APIView):
    """单位电价列表记录"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        search_name = request.query_params.get("search_name", None)
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size")) if request.query_params.get("page_size") else None

        # user_id = request.user["user_id"]
        # user_ins = models.UserDetails.objects.get(id=user_id)

        # if search_name:
        #     all_queryset = models.UnitPrice.objects.filter(name__contains=search_name, delete=0).all()
        # else:
        #     all_queryset = models.UnitPrice.objects.filter(delete=0).all()

        # 第一步：获取每个 uid 对应的最新 create_time
        latest_time_subquery = models.UnitPrice.objects.filter(
            delete=0  # 确保只查找未删除的记录
        ).values('uid').annotate(latest_create_time=Min('create_time'))

        # 第二步：将最新的 create_time 与原表进行关联，获取对应的完整记录
        all_queryset = models.UnitPrice.objects.filter(
            uid__in=[item['uid'] for item in latest_time_subquery],
            create_time__in=[item['latest_create_time'] for item in latest_time_subquery]
        ).order_by('-create_time')

        # 如果需要进一步过滤（如search_name），可以在主查询中添加过滤条件
        if search_name:
            all_queryset = all_queryset.filter(name__contains=search_name)

        detail_dic = (
            all_queryset.values(
                "name",
                "start",
                "end",
                "spike_chag_price",
                "peak_chag_price",
                "flat_chag_price",
                "valley_chag_price",
                "dvalley_chag_price",
                "spike_disg_price",
                "peak_disg_price",
                "flat_disg_price",
                "valley_disg_price",
                "dvalley_disg_price",
                "uid",
                "stations_name",
            )
            .distinct()
        )

        for item in detail_dic:
            unit_prices = models.UnitPrice.objects.filter(delete=0, uid=item["uid"]).all()
            if unit_prices.exists():
                stations_list = [i.station_id for i in unit_prices]
                item["stations_list"] = stations_list

        if page_size:
            # 手动分页
            total_pages = math.ceil(len(detail_dic) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(detail_dic) + 1
            detail_dic_ = detail_dic[start_index:end_index]

            return Response({"code": common_response_code.SUCCESS, "data": {"detail": detail_dic_, "message": "ok"},
                             "paginator_info": {
                            "page": page,
                            "page_size": page_size,
                            "pages": total_pages,
                            "total_count": len(detail_dic)}})
        else:
            return Response({"code": common_response_code.SUCCESS, "data": {"detail": detail_dic, "message": "ok"}})


class CustomizationUpdateDetailView(APIView):
    """单位电价：更新、详情"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request):
        """详情"""""
        uid = request.query_params.get('uid')

        unit_prices = models.UnitPrice.objects.filter(delete=0, uid=uid).all()
        if unit_prices.exists():
            stations_list = [i.station_id for i in unit_prices]

            unit_price = unit_prices.first()
            ser = CustomizationDetSerializer(instance=unit_price)
            return_dict = ser.data
            return_dict.update(stations_list=stations_list)
            return Response({"code": common_response_code.SUCCESS, "data": {"detail": return_dict, "message": "ok"}})
        else:
            return Response({"code": common_response_code.NO_DATA, "data": {"message": "uid查询不到数据"}})

    def post(self, request):
        """修改"""""
        user = request.user.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)

        uid = request.data['uid']
        start = request.data["start"]
        end = request.data["end"]
        name = request.data["name"]

        try:
            if not uid:
                raise Exception("参数Uid缺失")

            if not models.UnitPrice.objects.filter(uid=uid, delete=0).exists():
                raise Exception("单位电价不存在")

            if not name:
                raise Exception("单位电价名称不能为空")

            if not all([start, end]):
                raise Exception("开始时间和结束时间不能为空")
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )

        ser = CustomizationUpdateSerializer(data=request.data, context=self.serializer_context)
        try:
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "有参数为空，请重新选择或输入", "detail": ser.errors},
                    }
                )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message":  e.args[0], "detail": e.args[0]},
                }
            )
        stations_list = ser.validated_data.pop("stations_list")
        stations_names = [models.MaterStation.objects.get(id=station_id, is_delete=0).name for station_id in stations_list]
        stations_names_str = ','.join(stations_names)

        uid = ser.validated_data.get("uid")
        has_unit_prices = models.UnitPrice.objects.filter(uid=uid, delete=0).all()

        # 针对已有的uid关联的配置处理：删除不在列表中的配置，更新在列表中的配置
        for has_unit_price in has_unit_prices:
            if has_unit_price.station_id not in stations_list:
                has_unit_price.delete = 1
                has_unit_price.save()
            else:
                has_unit_price.user = user_ins
                has_unit_price.name = ser.validated_data.get("name")
                has_unit_price.en_name = ser.validated_data.get("name")
                has_unit_price.start = ser.validated_data.get("start")
                has_unit_price.end = ser.validated_data.get("end")
                has_unit_price.spike_chag_price = ser.validated_data.get("spike_chag_price")
                has_unit_price.peak_chag_price = ser.validated_data.get("peak_chag_price")
                has_unit_price.flat_chag_price = ser.validated_data.get("flat_chag_price")
                has_unit_price.valley_chag_price = ser.validated_data.get("valley_chag_price")
                has_unit_price.dvalley_chag_price = ser.validated_data.get("dvalley_chag_price")
                has_unit_price.spike_disg_price = ser.validated_data.get("spike_disg_price")
                has_unit_price.peak_disg_price = ser.validated_data.get("peak_disg_price")
                has_unit_price.flat_disg_price = ser.validated_data.get("flat_disg_price")
                has_unit_price.valley_disg_price = ser.validated_data.get("valley_disg_price")
                has_unit_price.dvalley_disg_price = ser.validated_data.get("dvalley_disg_price")
                has_unit_price.note = ser.validated_data.get("note")
                has_unit_price.en_note = ser.validated_data.get("note")
                has_unit_price.stations_name = stations_names_str

                has_unit_price.save()

                # 异步翻译
                pdr_data = {'id': has_unit_price.id,
                            'table': 't_unit_price',
                            'update_data': {'name': ser.validated_data.get('name')}}
                if ser.validated_data.get('note'):
                    pdr_data['update_data']['note'] = ser.validated_data.get('note')

                pub_name = 'en_translate_pub'
                redis_pool.publish(pub_name, json.dumps(pdr_data))

        # ser.validated_data.pop('stations_list')
        # 针对新增的uid关联的并网点处理：新增配置关联到uid
        if stations_list:
            for station_id in stations_list:
                station_unit_price = models.UnitPrice.objects.filter(uid=uid, station=station_id, delete=0).all()
                if station_unit_price:
                    continue
                m_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
                models.UnitPrice.objects.create(station=m_station, user=user_ins,
                                                      stations_name=stations_names_str,
                                                      project=m_station.project, **ser.validated_data)
        # 未选择关联的并网点则新建一条不关联的单位电价
        else:
            models.UnitPrice.objects.create(user=user_ins, **ser.validated_data)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "单位电价修改成功", "detail": uid},
            }
        )


class CustomizationDeleteView(APIView):
    """单位电价：删除"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        user_id = request.user["user_id"]
        uid = request.data.get("uid")
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "uid为必传参数"},
                }
            )

        price_ins = models.UnitPrice.objects.filter(uid=uid, delete=0).all()
        if not price_ins.exists():
            error_log.error("单位电价删除:uid 不存在或已删除")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "uid 不存在或已删除"},
                }
            )
        for p in price_ins.all():
            p.delete = 1
            p.delete_user_id = user_id
            p.save()

        return Response({"code": common_response_code.SUCCESS, "data": "单位电价记录删除成功"})

# """
# 自动控制策略管理
# """""
#
#
# def on_connect(mqtt_client, userdata, flags, rc):
#     if rc == 0:
#         print("Connected successfully")
#         mqtt_client.subscribe("django/mqtt")  # 订阅主题
#     else:
#         print("Bad connection. Code:", rc)
#
#
# def on_message(mqtt_client, userdata, msg):
#     print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")
#
#
class UserStrategyView(APIView):
    """新.用户自动模式配置：添加/修改/查询列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def post(self, request):
        """
        添加新用户策略
        """""
        try:
            ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
            if not ser.is_valid():
                error_log.error(f"用户自动模式配置添加: 字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )

            strategy = ser.save()
            e = strategy.id
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "用户自动模式配置: 添加成功",
                        "strategy_id": e,
                    },
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置: 添加报错：{}".format(e.args))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '用户自动模式配置：添加失败!'},
                }
            )

    def get(self, request):
        """
        获取用户所具有的策略
        """""
        try:
            query = request.query_params.get("query", None)
            station = request.query_params.get("station", None)

            user_id = request.user["user_id"]
            user_ins = models.UserDetails.objects.get(id=user_id)
            details = UserStrategy.objects.filter(
                user=user_ins,
                is_delete=0
            )

            if query:
                details = details.filter(
                    name__contains=query
                )

            for detail in details:
                months_count = detail.month_set.filter(is_valid=0).count()
                detail = detail.__dict__
                detail['status'] = 0
                if months_count == 12:
                    detail['is_show'] = 1
                else:
                    detail['is_show'] = 0
            if station:
                record = models.Record.objects.filter(user=user_id, station=station).order_by('-id').first()
                if record:
                    for detail in details:
                        detail = detail.__dict__
                        if record.strategy and detail.get('id') == record.strategy.id:
                            detail['status'] = 1
                        else:
                            detail['status'] = 0

            ser = ResponseUserStrategySerializer(instance=details, many=True)

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": ser.data},
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置: 查询报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '用户自动模式配置：查询失败!'},
                }
            )

    def put(self, request, pk):
        """
        更新用户策略
        """""
        try:
            ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
            if not ser.is_valid():
                error_log.error(f"用户自动模式配置添加:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
            user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
            au_instance = UserStrategy.objects.filter(id=pk, user=user_ins)
            if not au_instance:
                error_log.error(f"用户自动模式配置更新:策略不存在")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动模式配置更新:策略不存在."},
                    }
                )
            ser.update(instance=au_instance, validated_data=ser.validated_data)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "用户自动模式配置更新: 更新成功",
                        "strategy_id": au_instance[0].id,
                    },
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置: 更新报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '用户自动模式配置：更新失败!'},
                }
            )
#
#
# class UserStrategyDeleteView(APIView):
#     """新.用户自动模式配置：删除"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         try:
#             obj = UserStrategy.objects.get(id=pk)
#             obj.is_delete = 1
#             obj.save()
#
#             # 删除策略的月份
#             month_instances = Month.objects.filter(strategy=obj)
#             for month_instance in month_instances:
#                 month_instance.delete()
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": '用户自动模式配置删除：删除成功'},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动模式配置删除: 删除报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '模式配置删除：删除失败!'},
#                 }
#             )
#
#
# class UserStrategyCheckMonthView(APIView):
#     """新.用户自动模式配置：校验策略的所有月份是否均配置"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def get(self, request, pk):
#         try:
#             strategy_ins = UserStrategy.objects.get(id=pk)
#             months = Month.objects.filter(strategy=strategy_ins).all()
#
#             valid_months = [month.month_number for month in months if month.is_valid]
#             no_valid_months = [month.month_number for month in months if not month.is_valid]
#
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": {"valid_months": valid_months, "no_valid_months": no_valid_months,
#                                    "count": len(valid_months)},
#                     },
#                 }
#             )
#
#         except Exception as e:
#             error_log.error("策略月份校验：校验报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '策略月份校验：校验失败!'},
#                 }
#             )
#
#
# class UserStrategyCategoryView(APIView):
#     """新.用户自动模式配置-季节：添加/修改/查询列表"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request):
#         """
#         添加新用户策略-季节
#         """""
#         # try:
#         ser = UserStrategyCategorySerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动控制策略-季节: 字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.get(id=user_id)
#         strategy = UserStrategy.objects.filter(id=ser.validated_data.get('strategy_id'), user=user_ins)
#         if not strategy:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-季节：控制策略不存在!'},
#                 }
#             )
#
#         category = ser.save()
#         e = category.id
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-季节：添加成功",
#                     "category_id": e,
#                 },
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-季节: 添加报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-季节：添加失败!'},
#         #         }
#         #     )
#
#     def get(self, request, strategy_id):
#         """
#         获取策略所具有的季节列表
#         """""
#         # try:
#         #     query = request.query_params.get("query", None)
#             # station = request.query_params.get("station", None)
#
#         try:
#             strategy_ins = UserStrategy.objects.get(id=strategy_id)
#         except Exception as e:
#             error_log.error("用户自动控制策略-季节: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-季节：控制策略不存在!'},
#                 }
#             )
#
#         category_instances = strategy_ins.userstrategycategory_set.filter(is_delete=0).all()
#
#         temp_detail = dict()
#         for detail in category_instances:
#             month_instances = detail.month_set.all()
#             months = [month.month_number for month in month_instances]
#             temp_detail[detail.id] = months
#
#             if detail.charge_config:
#                 detail.charge_config = eval(detail.charge_config)
#             if detail.rl_list:
#                 detail.rl_list = eval(detail.rl_list)
#
#         ser = UserStrategyCategorySerializer(instance=category_instances, many=True)
#
#         details = ser.data
#         for detail in details:
#             detail['months'] = temp_detail[detail['id']]
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": details},
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-季节: 查询报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-季节：查询失败!'},
#         #         }
#         #     )
#
#     def put(self, request, pk):
#         # try:
#         try:
#             category_instance = UserStrategyCategory.objects.get(id=pk)
#         except Exception as e:
#             error_log.error("用户自动控制策略-季节:资源不存在: {}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-季节: 资源不存在."},
#                 }
#             )
#         ser = UpdateUserStrategyCategorySerializer(category_instance, data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"用户自动控制策略-季节:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         ser.save()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-季节: 更新成功",
#                     "category_id": category_instance.id,
#                 },
#             }
#         )
#         # except Exception as e:
#         #     error_log.error("用户自动控制策略-季节: 更新报错：{}".format(e))
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "fail", "detail": '用户自动控制策略-季节：更新失败!'},
#         #         }
#         #     )
#
#
# class UserStrategyCategoryUpdateMonthView(APIView):
#     """新.用户自动模式配置-季节：单独修改月份"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         """
#         添加新用户策略-季节
#         """""
#         try:
#             category_instance = UserStrategyCategory.objects.get(id=pk)
#         except Exception as e:
#             error_log.error("用户自动控制策略-季节:资源不存在: {}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-季节: 资源不存在."},
#                 }
#             )
#
#         months = request.data.get('months')
#         if not months:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略-季节: 缺失参数：months."},
#                 }
#             )
#
#         strategy = UserStrategy.objects.get(id=category_instance.strategy_id)
#         all_valid_strategy_months = Month.objects.filter(strategy=strategy, is_valid=1).all()
#         old_cate_months = Month.objects.filter(user_Strategy_Category=category_instance).all()
#         old_cate_months_numbers = [valid_month.month_number for valid_month in old_cate_months]
#         all_valid_strategy_months_numbers = [valid_month.month_number for valid_month in all_valid_strategy_months]
#
#         for month in months:
#             if month not in all_valid_strategy_months_numbers and month not in old_cate_months_numbers:
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": "用户自动控制策略-季节：{}月份已被其他季节选择！".format(month)},
#                     }
#                 )
#
#         # 保存月份
#         all_strategy_months = Month.objects.filter(strategy=strategy).all()
#         for month in all_strategy_months:
#             if month.month_number in months:
#                 month.is_valid = False
#                 month.user_Strategy_Category = category_instance
#                 month.save()
#         for month_ in old_cate_months:
#             if month_.month_number not in months:
#                 month_.is_valid = True
#                 month_.user_Strategy_Category = None
#                 month_.save()
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "用户自动控制策略-季节: 月份更新成功",
#                     "category_id": category_instance.id,
#                 },
#             }
#         )
#
#
# class UserStrategyCategory2View(APIView):
#     """新.用户自动模式配置-季节：删除/查询详情"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def post(self, request, pk):
#         """
#         删除新用户策略-季节
#         """""
#         try:
#             obj = UserStrategyCategory.objects.get(id=pk)
#             obj.is_delete = 1
#             obj.save()
#             months = obj.month_set.all()
#             for month in months:
#                 month.is_valid = True
#                 month.save()
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": '用户自动控制策略-季节：删除成功'},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动控制策略-季节: 删除报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-季节：删除失败!'},
#                 }
#             )
#
#     def get(self, request, pk):
#         """
#         获取策略-季节详情
#         """""
#         try:
#             try:
#                 category_ins = UserStrategyCategory.objects.get(id=pk)
#             except Exception as e:
#                 error_log.error("用户自动控制策略-季节: 查询报错：{}".format(e))
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {"message": "fail", "detail": '用户自动控制策略-季节：不存在!'},
#                     }
#                 )
#
#             month_instances = category_ins.month_set.all()
#             months = [month.month_number for month in month_instances]
#
#             if category_ins.charge_config:
#                 category_ins.charge_config = eval(category_ins.charge_config)
#             if category_ins.rl_list:
#                 category_ins.rl_list = eval(category_ins.rl_list)
#
#             ser = UserStrategyCategorySerializer(instance=category_ins)
#             detail = ser.data
#             detail['months'] = months
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": detail},
#                 }
#             )
#         except Exception as e:
#             error_log.error("用户自动控制策略-季节: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '用户自动控制策略-季节：查询详情失败!'},
#                 }
#             )
#
#
# class CurrentStrategyView(APIView):
#     """新-获取当前策略"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         query = request.query_params
#
#         ser = CurrentStrategySerializers(data=query)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station = ser.validated_data["station"]
#         # station_ins = models.StationDetails.objects.filter(english_name=station).first()
#         master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station).first()
#         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#         record = models.Record.objects.filter(station=station_instance).last()
#         if record:
#             strategy_ins = record.strategy
#
#             if strategy_ins:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "success", "detail": {
#                             "strategy_id": strategy_ins.id,
#                             "strategy_name": strategy_ins.name
#                         }},
#                     }
#                 )
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "no strategy has apply!!!", "detail": "当前站无下发策略策略"},
#                     }
#                 )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "no record for strategy has apply!!!", "detail": "当前站无下发指令记录"},
#             }
#         )
#
#
# class RealTimeStationStrategyView(APIView):
#     """新-实时策略"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         conn = get_redis_connection("3")
#         client = mtqq_station_strategy(station_id, month)
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(datetime.datetime.now().month) if not month else month
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         if not request.data.get('station'):
#             error_log.error(f"实时策略:字段校验不通过 => station字段未填写")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "station字段为必填项"},
#                 }
#             )
#
#         station_english_name = request.data['station']
#         # station_instance = models.StationDetails.objects.filter(english_name=station_english_name).first()
#         master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station_english_name).first()
#         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#
#         station_type = station_instance.type
#         station_level = station_instance.level
#         # 目标月电价的峰谷标识
#
#         month = request.data.get('month')
#         pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level, month)
#
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#         current_datetime = datetime.datetime.now()
#         formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
#
#         data = self._get_data(station_instance.id, station_instance.english_name, month)
#         policy = {}
#         policy['id'] = 0
#         policy['is_delete'] = 0
#         policy['months'] = [month if month else datetime.datetime.now().month]
#         policy['create_time'] = formatted_datetime
#         policy['name'] = station_instance.province.name + common_response_code.ConfLevType.TYPE[station_type] + \
#                      common_response_code.ConfLevType.LEVEL[station_level] + '默认运行策略'
#
#         policy['is_follow'] = data.get('WLoadFollowTC', 0)
#         charge_config = [int(data.get(key, 0)) for key in current_month_charge_keys]
#         rl_list = dict()
#         for key in current_month_rl_keys:
#             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(data.get(key, 0)) * 100, 2)
#
#         policy['rl_list'] = rl_list
#         policy['charge_config'] = charge_config
#
#         for nu in range(0, 24):
#             policy[f"pv{str(nu)}"] = pv_list[nu]
#         return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {
#                             "message": "success",
#                             "unit_name": station_instance.station_name,
#                             "detail": policy,
#                         },
#                     }
#                 )
#
#
# class UserStrategySaveToOtherView(APIView):
#     """新.用户自动控制策略：另存为"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     @transaction.atomic
#     def post(self, request, pk):
#         """
#         用户策略-另存为
#         """""
#         new_strategy_name = request.data.get('new_name')
#         type_ = int(request.data.get('type', 3))   # 1: 实时；2：默认，3：自定义
#         user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
#
#         strategy = UserStrategy.objects.filter(user=user_ins, name=new_strategy_name, is_delete=0)
#         if strategy.exists():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户控制策略另存为: 名称已存在"},
#                 }
#             )
#         try:
#             save_id = transaction.savepoint()
#             if type_ == 3:
#                 try:
#                     strategy_instance = UserStrategy.objects.get(id=pk)
#                 except Exception as e:
#                     error_log.error("用户控制策略另存为:策略不存在：{}".format(e))
#
#                     return Response(
#                         {
#                             "code": common_response_code.FIELD_ERROR,
#                             "data": {"message": "error", "detail": "用户控制策略另存为:策略不存在"},
#                         }
#                     )
#
#                 old_category_instances = strategy_instance.userstrategycategory_set.all()
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     Month.objects.create(strategy=new_strategy, month_number=i)
#
#                  # 创建新季节
#                 for old_category_instance in old_category_instances:
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name=old_category_instance.name,
#                                                                                 charge_config=old_category_instance.charge_config,
#                                                                                 is_follow=old_category_instance.is_follow,
#                                                                                 rl_list=old_category_instance.rl_list,
#                                                                                 pv0=old_category_instance.pv0,
#                                                                                 pv1=old_category_instance.pv1,
#                                                                                 pv2=old_category_instance.pv2,
#                                                                                 pv3=old_category_instance.pv3,
#                                                                                 pv4=old_category_instance.pv4,
#                                                                                 pv5=old_category_instance.pv5,
#                                                                                 pv6=old_category_instance.pv6,
#                                                                                 pv7=old_category_instance.pv7,
#                                                                                 pv8=old_category_instance.pv8,
#                                                                                 pv9=old_category_instance.pv9,
#                                                                                 pv10=old_category_instance.pv10,
#                                                                                 pv11=old_category_instance.pv11,
#                                                                                 pv12=old_category_instance.pv12,
#                                                                                 pv13=old_category_instance.pv13,
#                                                                                 pv14=old_category_instance.pv14,
#                                                                                 pv15=old_category_instance.pv15,
#                                                                                 pv16=old_category_instance.pv16,
#                                                                                 pv17=old_category_instance.pv17,
#                                                                                 pv18=old_category_instance.pv18,
#                                                                                 pv19=old_category_instance.pv19,
#                                                                                 pv20=old_category_instance.pv20,
#                                                                                 pv21=old_category_instance.pv21,
#                                                                                 pv22=old_category_instance.pv22,
#                                                                                 pv23=old_category_instance.pv23
#                                                                                 )
#                     new_category_instance.save()
#                     tem_months = old_category_instance.month_set.all()
#                     tem_months_numbers = [month.month_number for month in tem_months]
#                     for month in tem_months_numbers:
#                         month_ins = Month.objects.filter(strategy=new_strategy, month_number=month).first()
#                         month_ins.is_valid = False
#                         month_ins.user_Strategy_Category = new_category_instance
#                         month_ins.save()
#
#             elif type_ == 2:
#                 master_station = models.MaterStation.objects.filter(is_delete=0, id=pk).first()
#                 # station = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#                 # station = models.StationDetails.objects.get(id=pk)
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#
#                 default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#                 default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#                 station_actic = models.StationActic.objects.filter(station_id=station.id).all()
#                 former_actic_ids = [i.former_actic_id for i in station_actic]
#                 former_actic = models.FormerActic.objects.filter(id__in=former_actic_ids).all()
#
#                 for info in former_actic:
#                     info = info.__dict__
#                     rl_list = dict()
#                     for key in default_month_rl_keys:
#                         rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(info.get(key, 0) / info.get('power') * 100, 2)
#                     charge_config = [int(info.get(key, 0)) for key in default_month_charge_keys]
#                     pv_list = self._get_pv_status(station.province, station.type, station.level, int(info.get('year_month').split('-')[-1]))
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name='月份{}'.format(info.get('year_month').split('-')[-1]),
#                                                                                 charge_config=charge_config,
#                                                                                 is_follow=1,
#                                                                                 rl_list=rl_list,
#                                                                                 pv0=pv_list[0],
#                                                                                 pv1=pv_list[1],
#                                                                                 pv2=pv_list[2],
#                                                                                 pv3=pv_list[3],
#                                                                                 pv4=pv_list[4],
#                                                                                 pv5=pv_list[5],
#                                                                                 pv6=pv_list[6],
#                                                                                 pv7=pv_list[7],
#                                                                                 pv8=pv_list[8],
#                                                                                 pv9=pv_list[9],
#                                                                                 pv10=pv_list[10],
#                                                                                 pv11=pv_list[11],
#                                                                                 pv12=pv_list[12],
#                                                                                 pv13=pv_list[13],
#                                                                                 pv14=pv_list[14],
#                                                                                 pv15=pv_list[15],
#                                                                                 pv16=pv_list[16],
#                                                                                 pv17=pv_list[17],
#                                                                                 pv18=pv_list[18],
#                                                                                 pv19=pv_list[19],
#                                                                                 pv20=pv_list[20],
#                                                                                 pv21=pv_list[21],
#                                                                                 pv22=pv_list[22],
#                                                                                 pv23=pv_list[23])
#                     new_category_instance.save()
#                     Month.objects.create(strategy=new_strategy, month_number=int(info.get('year_month').split('-')[-1]), is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#             elif type_ == 1:
#                 # station = models.StationDetails.objects.get(id=pk)
#                 master_station = models.MaterStation.objects.filter(id=pk, is_delete=0).first()
#                 # station = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#
#                 conn = get_redis_connection("3")
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     datas = conn.get('{}-{}-mqtt'.format(station.english_name, i))
#                     data = {}
#                     current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#                     current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#                     charge_config = [0 for i in range(24)]
#                     rl_list = {'RL' + str(current_month_rl_keys.index(key) + 1): 0 for key in current_month_rl_keys}
#                     pv_list = self._get_pv_status(station.province, station.type, station.level, i)
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     if datas:
#                         datas = eval(datas)
#                         datas = datas.get('body')[0].get('body')
#                         for y in range(1, 25):
#                             data[f'RLH{y}F'] = datas.get(f'M{i}H{y}F')
#                             data[f'RLH{y}P'] = datas.get(f'M{i}H{y}P')
#                         charge_config = [int(data.get(key, 0)) for key in current_month_charge_keys]
#                         for key in current_month_rl_keys:
#                             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(
#                                 float(data.get(key, 0)) * 100, 2)
#
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name='月份{}'.format(i),
#                                                                                 charge_config=charge_config,
#                                                                                 is_follow=1,
#                                                                                 rl_list=rl_list,
#                                                                                 pv0=pv_list[0],
#                                                                                 pv1=pv_list[1],
#                                                                                 pv2=pv_list[2],
#                                                                                 pv3=pv_list[3],
#                                                                                 pv4=pv_list[4],
#                                                                                 pv5=pv_list[5],
#                                                                                 pv6=pv_list[6],
#                                                                                 pv7=pv_list[7],
#                                                                                 pv8=pv_list[8],
#                                                                                 pv9=pv_list[9],
#                                                                                 pv10=pv_list[10],
#                                                                                 pv11=pv_list[11],
#                                                                                 pv12=pv_list[12],
#                                                                                 pv13=pv_list[13],
#                                                                                 pv14=pv_list[14],
#                                                                                 pv15=pv_list[15],
#                                                                                 pv16=pv_list[16],
#                                                                                 pv17=pv_list[17],
#                                                                                 pv18=pv_list[18],
#                                                                                 pv19=pv_list[19],
#                                                                                 pv20=pv_list[20],
#                                                                                 pv21=pv_list[21],
#                                                                                 pv22=pv_list[22],
#                                                                                 pv23=pv_list[23]
#                                                                                 )
#                     new_category_instance.save()
#                     Month.objects.create(strategy=new_strategy, month_number=i,
#                                          is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "detail": "控制策略类型错误",
#                         },
#                     }
#                 )
#             transaction.savepoint_commit(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "用户自动控制策略: 另存为保存成功",
#                         "new_strategy_id": new_strategy.id
#                     },
#                 }
#             )
#         except Exception as e:
#             error_log.error("另存策略失败：{}".format(e))
#             transaction.savepoint_rollback(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {"message": "error", "detail": "另存失败"},
#                 }
#             )
#
#
# class DefaultStrategyView(APIView):
#     """
#     默认策略
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(month)
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         ser = DefaultStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         now_time = datetime.datetime.now()
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_name, is_delete=0).first()
#         # station_info = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_info = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         month = request.data.get('month') if request.data.get('month') else now_time.month
#         # pv_list = self._get_pv_status(station_info[1], station_info[2], station_info[3], month)
#         pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#         default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#
#         station_id = station_info.id
#         former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
#         former_actic_ids = [i.former_actic_id for i in former_actic_ids]
#         former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
#         if not former_res:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
#                 }
#             )
#         policy = {}
#         policy['id'] = 0
#         policy['is_delete'] = 0
#         policy['months'] = [month if month else now_time.month]
#         policy['create_time'] = now_time.strftime("%Y-%m-%d %H:%M:%S")
#         policy['name'] = station_info.province.name + common_response_code.ConfLevType.TYPE[station_info.type] + \
#                          common_response_code.ConfLevType.LEVEL[station_info.level] + '默认运行策略'
#
#         former_res = former_res.__dict__
#         policy['is_follow'] = former_res.get('WLoadFollowTC', 0)
#         charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
#         rl_list = dict()
#         for key in default_month_rl_keys:
#             rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(former_res.get(key, 0) / former_res.get('power') * 100, 2)
#
#         policy['rl_list'] = rl_list
#         policy['charge_config'] = charge_config
#
#         for nu in range(0, 24):
#             policy[f"pv{str(nu)}"] = pv_list[nu]
#
#         return Response(
#                         {
#                             "code": common_response_code.SUCCESS,
#                             "data": {"message": "success", "detail": policy},
#                         }
#                     )
#
#
# class UserStrategyCustomizeView(APIView):
#     """自定义策略查询列表"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request, strategy_id):
#         """
#         获取策略所具有的季节列表
#         """""
#
#         try:
#             strategy_ins = UserStrategy.objects.get(id=strategy_id)
#         except Exception as e:
#             error_log.error("自定义控制策略: 查询报错：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "fail", "detail": '自定义控制策略：控制策略不存在!'},
#                 }
#             )
#
#         category_month_instances = strategy_ins.month_set.filter(is_valid=0).all()
#         temp_detail = []
#         for detail in category_month_instances:
#             info = UserStrategyCategory.objects.get(id=detail.user_Strategy_Category.id)
#             info = info.__dict__
#             info['month'] = detail.month_number
#             if info.get('charge_config'):
#                 info['charge_config'] = eval(info.get('charge_config'))
#             if info.get('rl_list'):
#                 info['rl_list'] = eval(info.get('rl_list'))
#             del info['_state']
#             temp_detail.append(info)
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": temp_detail},
#             }
#         )
#
#
# class CompareStationStrategyView(APIView):
#     """
#     默认策略比较
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         client = mtqq_station_strategy(station_id, month)
#         conn = get_redis_connection("3")
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#
#     def post(self, request):
#         ser = DefaultStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station_english_name = request.data['station']
#         now_time = datetime.datetime.now()
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_english_name, is_delete=0).first()
#         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         month = request.data.get('month') if request.data.get('month') else now_time.month
#
#         default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#         default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#
#
#         station_id = station_instance.id
#         former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
#         former_actic_ids = [i.former_actic_id for i in former_actic_ids]
#
#         month_list = [month] if month != '-1' else [str(i) for i in range(1, 13)]
#         for month in month_list:
#             pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level, month)
#             # 做处理为了对应1-24点的计时
#             pv_list.append(pv_list[0])
#             pv_list.pop(0)
#             # 默认策略
#             former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
#             if not former_res:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
#                     }
#                 )
#
#             former_res = former_res.__dict__
#             # 实时策略
#             current_data = self._get_data(station_instance.id, station_instance.english_name, month)
#
#             default_charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
#             current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]
#
#             default_rl_list = dict()
#             for key in default_month_rl_keys:
#                 default_rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(former_res.get(key, 0) / former_res.get('power') * 100, 2)
#
#             current_rl_list = dict()
#             for key in current_month_rl_keys:
#                 current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(current_data.get(key, 0)) * 100, 2)
#
#             res = []
#             rl_list = list(zip(current_rl_list.values(), default_rl_list.values()))
#             charge_config = list(zip(current_charge_config, default_charge_config))
#             for i in range(24):
#                 if charge_config[i][0] == 0 and charge_config[i][1] == 0:
#                     continue
#                 if len(set(rl_list[i])) > 1 or (len(set(charge_config[i])) > 1):
#                     data = {}
#                     data['hours'] = i + 1
#                     data['default_rl'] = rl_list[i][1]
#                     data['default_charge'] = charge_config[i][1]
#                     data['current_rl'] = rl_list[i][0]
#                     data['current_charge'] = charge_config[i][0]
#                     data['pv'] = pv_list[i]
#                     data['month'] = month
#                     res.append(data)
#
#             if res:
#                 return Response(
#                                 {
#                                     "code": common_response_code.SUCCESS,
#                                     "data": {"message": "success", "detail": res},
#                                 }
#                             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": []},
#             }
#         )
#
#
# class CustomizeStationStrategyView(APIView):
#     """
#     自定义策略比较
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#
#     def _get_data(self, station_id, station, month):
#         """
#         获取当前站的指定月份的自动策略控制信息
#         :return:
#         """
#         client = mtqq_station_strategy(station_id, month)
#         conn = get_redis_connection("3")
#         if month:
#             i = 0
#             n = 2 if int(month) < 10 else 3
#             data = {}
#             while i < 5:
#                 time.sleep(0.5)
#                 datas = conn.get('{}-{}-mqtt'.format(station, month))
#                 if datas:
#                     datas = eval(datas)
#                     datas = datas.get('body')[0].get('body')
#                     if not datas:
#                         break
#                     key = list(datas.keys())[0]
#                     if key[:n] == f'M{month}':
#                         for i in range(1, 25):
#                             data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F', 0)
#                             data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P', 0)
#                         i = 10
#                 else:
#                     i += 1
#         else:
#             data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
#             if data:
#                 data = json.loads(eval(data))
#             else:
#                 data = {}
#         client.disconnect()
#         return data
#
#
#     def post(self, request):
#         ser = CustomizeStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station_english_name = request.data['station']
#         strategy_id = int(request.data['strategy_id'])
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(english_name=station_name, is_delete=0).first()
#         station_info = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
#
#         # if station_info == -1:
#         #     return Response(
#         #         {
#         #             "code": common_response_code.ERROR,
#         #             "data": {"message": "站名不存在", "detail": {}},
#         #         }
#         #     )
#         detail = UserStrategy.objects.get(id=strategy_id)
#         if not detail:
#             error_log.error(f"用户自动控策略:策略不存在")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户自动控制策略:策略不存在."},
#                 }
#             )
#
#         current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#         current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#
#         month_list = [str(i) for i in range(1, 13)]
#         for month in month_list:
#             pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
#             # 做处理为了对应1-24点的计时
#             pv_list.append(pv_list[0])
#             pv_list.pop(0)
#             # 自定义策略
#             month_detail = detail.month_set.filter(is_valid=0, month_number=int(month)).first()
#             if not month_detail:
#                 error_log.error(f"用户自动控策略:策略配置不完整")
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": "用户自动控策略:策略配置不完整"},
#                     }
#                 )
#             strategy_category = UserStrategyCategory.objects.get(id=month_detail.user_Strategy_Category.id)
#             customize_change_config = eval(strategy_category.charge_config)
#             customize_rl_list = eval(strategy_category.rl_list)
#
#
#             # 实时策略
#             current_data = self._get_data(station_info.id, station_info.english_name, month)
#
#             current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]
#
#             current_rl_list = dict()
#             for key in current_month_rl_keys:
#                 current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(float(current_data.get(key, 0)) * 100, 2)
#
#             res = []
#             customize_list = [float(i) for i in customize_rl_list.values()]
#             rl_list = list(zip(current_rl_list.values(), customize_list))
#             charge_config = list(zip(current_charge_config, customize_change_config))
#             for i in range(24):
#                 if charge_config[i][0] == 0 and charge_config[i][1] == 0:
#                     continue
#                 if len(set(rl_list[i])) > 1 or len(set(charge_config[i])) > 1:
#                     data = {}
#                     data['hours'] = i + 1
#                     data['customize_rl'] = rl_list[i][1]
#                     data['customize_charge'] = charge_config[i][1]
#                     data['current_rl'] = rl_list[i][0]
#                     data['current_charge'] = charge_config[i][0]
#                     data['pv'] = pv_list[i]
#                     data['month'] = month
#                     res.append(data)
#
#             if res:
#                 return Response(
#                                 {
#                                     "code": common_response_code.SUCCESS,
#                                     "data": {"message": "success", "detail": res},
#                                 }
#                             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": []},
#             }
#         )
#
#
# class AutomaticControlSendSmsView(APIView):
#     """自动控制模式发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("自动控制模式发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("自动控制模式发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         """目前没有接入第三方发短信程序"""
#         conn = get_redis_connection("default")
#         conn.set("auto" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )
#
#
# # class UserStrategyApplyView(APIView):
# #     """新: 自动控制策略：下发"""""
# #
# #     authentication_classes = [
# #         JwtParamAuthentication,
# #         JWTHeaderAuthentication,
# #         DenyAuthentication,
# #     ]  # jwt认证
# #
# #     def initial(self, request, *args, **kwargs):
# #         super().initial(request, *args, **kwargs)
# #         self.serializer_context = {"user_id": request.user["user_id"]}
# #         self.serializer_context["station_id"] = json.loads(request.body.decode()).get("station_id", None)
# #         self.serializer_context["uid"] = uuid.uuid4()
# #
# #     def post(self, request):
# #         ser = UserStrategyApplySerializer(data=request.data, context=self.serializer_context)
# #         if not ser.is_valid():
# #             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
# #
# #             return Response(
# #                 {
# #                     "code": common_response_code.FIELD_ERROR,
# #                     "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！",
# #                              "details": ser.errors},
# #                 }
# #             )
# #         conn = get_redis_connection("default")
# #         conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
# #
# #         # 查询用户信息
# #         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
# #                                                           id=request.user["user_id"]).first()
# #         if not user_instance:
# #             error_log.error("自动控制模式下发:手机号与当前登录用户名不符")
# #             return Response(
# #                 {
# #                     "code": common_response_code.SUMMARY_CODE,
# #                     "data": {
# #                         "message": "error",
# #                         "detail": "手机号与当前登录用户名不符！"
# #                     },
# #                 }
# #             )
# #
# #         # 查询站信息
# #         station_id = self.serializer_context["station_id"]
# #         master_station = models.MaterStation.objects.filter(id=station_id, is_delete=0).first()
# #         # station_instance = models.StationDetails.objects.filter(id=station_id).first()
# #         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
# #         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
# #         if not station_instance:
# #             return Response(
# #                 {
# #                     "code": common_response_code.SUMMARY_CODE,
# #                     "data": {
# #                         "message": "error",
# #                         "detail": "自动控制策略下发: 站信息不存在！"
# #                     },
# #                 }
# #             )
# #         station_app = station_instance.app
# #         station_english_name = station_instance.english_name
# #
# #         # 查询策略信息
# #         try:
# #             strategy_ins = UserStrategy.objects.get(id=ser.validated_data.get('strategy_id'))
# #         except Exception as e:
# #             error_log.error("自动控制策略下发: 策略信息不存在：{}".format(e))
# #             return Response(
# #                 {
# #                     "code": common_response_code.SUMMARY_CODE,
# #                     "data": {
# #                         "message": "error",
# #                         "detail": "自动控制策略下发: 策略信息不存在！"
# #                     },
# #                 }
# #             )
# #
# #         # 查询策略-季节
# #         category_instances = strategy_ins.userstrategycategory_set.all()
# #         if not len(category_instances):
# #             return Response(
# #                 {
# #                     "code": common_response_code.SUMMARY_CODE,
# #                     "data": {
# #                         "message": "error",
# #                         "detail": "自动控制策略下发: 策略-季节为空！"
# #                     },
# #                 }
# #             )
# #
# #         temp_list = list()
# #         for category_instance in category_instances:
# #             category_months = category_instance.month_set.all()
# #             for category_month in category_months:
# #                 for i in range(0, 24):
# #                     temp_dict1 = dict()
# #                     temp_dict2 = dict()
# #                     temp_dict1['M' + str(category_month.month_number) + 'H' + str(i+1) + 'FC']\
# #                         = str(ast.literal_eval(category_instance.charge_config)[i])
# #                     temp_dict1['type'] = 'parameter'
# #                     temp_dict2['M' + str(category_month.month_number) + 'H' + str(i+1) + 'PC']\
# #                         = str(int(ast.literal_eval(category_instance.rl_list)['RL' + str(i+1)])/100)
# #                     temp_dict2['type'] = 'parameter'
# #                     temp_list.append(temp_dict1)
# #                     temp_list.append(temp_dict2)
# #         temp_list.append(
# #             {
# #                 "LoadFollowTC": str(category_instances[0].is_follow),
# #                 "type": "parameter",
# #             }
# #         )
# #
# #         # 准备下发策略
# #         topic = f"req/database/parameter/{station_english_name}/{station_app}"
# #         secret_key = settings.AES_KEY
# #         aes = EncryptDate(secret_key)
# #
# #         token = aes.encrypt(station_english_name)
# #
# #         message = {
# #             "time": str(int(time.time())),
# #             "token": token,
# #             "device": "EMS",
# #             "body": temp_list
# #         }
# #
# #         client = mqtt.Client()
# #         client.on_connect = on_connect
# #         client.on_message = on_message
# #         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
# #         client.connect(
# #             host=settings.MQTT_SERVER,
# #             port=settings.MQTT_PORT,
# #             keepalive=settings.MQTT_KEEPALIVE,
# #         )
# #
# #         json_message = json.dumps(message)
# #         try:
# #             # client.publish(topic, json_message)
# #             print("下发策略内容：", json_message)           # todo 无法真实下发调试，待测试或者上线再打开
# #         except Exception as e:
# #             error_log.error('自动控制策略下发: 失败: {}'.format(e))
# #             return Response(
# #                 {
# #                     "code": common_response_code.SUMMARY_CODE,
# #                     "data": {
# #                         "message": "error",
# #                         "detail": "自动控制策略下发: 下发失败！"
# #                     },
# #                 }
# #             )
# #
# #         success_log.info("云端计划下发成功===")
# #         success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
# #
# #         # 写入下发历史策略
# #         # plan_id = ser.validated_data.pop("plan_id")
# #         # plan_ins = models.UserAutomation.objects.get(id=plan_id)
# #         # strategy_id = ser.validated_data.get("strategy_id")
# #
# #         models.Record.objects.create(
# #             user=user_instance.id,
# #             topic=topic,
# #             message=message,
# #             station=station_instance,
# #             strategy=strategy_ins,
# #         )
# #         try:
# #             ser.save()
# #         except Exception as e:
# #             print(e)
# #         return Response(
# #             {
# #                 "code": common_response_code.SUCCESS,
# #                 "data": {
# #                     "message": "success",
# #                     "detail": "控制策略下发成功.",
# #                 },
# #             }
# #         )
#
#
# class StationStrategyView(APIView):
#     """新-站自动控制策略：发布+订阅"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["station_id"] = json.loads(request.body.decode()).get("station_id", None)
#         self.serializer_context["uid"] = uuid.uuid4()
#
#     def post(self, request):
#         station_id = request.data.get('station_id')
#         month = request.data.get('month')
#
#         station_instance = models.StationDetails.objects.filter(id=station_id, is_delete=0).first()
#         if not station_instance:
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 站信息不存在！"
#                     },
#                 }
#             )
#         station_app = station_instance.app
#         station_english_name = station_instance.english_name
#
#         temp_list = list()
#         for i in range(0, 24):
#             temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'F')
#             temp_list.append('M' + str(month) + 'H' + str(i + 1) + 'P')
#
#         import time
#         now_time = str(int(time.time()))
#
#         t10 = ['M10H1F', 'M10H2F', 'M10H3F', 'M10H4F', 'M10H5F', 'M10H6F', 'M10H7F', 'M10H8F',
#               'M10H9F', 'M10H10F', 'M10H11F', 'M10H12F', 'M10H13F', 'M10H14F', 'M10H15F', 'M10H16F', 'M10H17F', 'M10H18F',
#               'M10H19F', 'M10H20F', 'M10H21F', 'M10H22F', 'M10H23F', 'M10H24F',
#               'M10H1P', 'M10H2P', 'M10H3P', 'M10H4P', 'M10H5P', 'M10H6P', 'M10H7P', 'M10H8P',
#               'M10H9P', 'M10H10P', 'M10H11P', 'M10H12P', 'M10H13P', 'M10H14P', 'M10H15P', 'M10H16P', 'M10H17P', 'M10H18P',
#               'M10H19P', 'M10H20P', 'M10H21P', 'M10H22P', 'M10H23P', 'M10H24P']
#
#         t11 = ['M11H1F', 'M11H2F', 'M11H3F', 'M11H4F', 'M11H5F', 'M11H6F', 'M11H7F', 'M11H8F',
#               'M11H9F', 'M11H10F', 'M11H11F', 'M11H12F', 'M11H13F', 'M11H14F', 'M11H15F', 'M11H16F', 'M11H17F', 'M11H18F',
#               'M11H19F', 'M11H20F', 'M11H21F', 'M11H22F', 'M11H23F', 'M11H24F',
#               'M11H1P', 'M11H2P', 'M11H3P', 'M11H4P', 'M11H5P', 'M11H6P', 'M11H7P', 'M11H8P',
#               'M11H9P', 'M11H10P', 'M11H11P', 'M11H12P', 'M11H13P', 'M11H14P', 'M11H15P', 'M11H16P', 'M11H17P', 'M11H18P',
#               'M11H19P', 'M11H20P', 'M11H21P', 'M11H22P', 'M11H23P', 'M11H24P']
#
#         C11 = ['M11H1FC', 'M11H2FC', 'M11H3FC', 'M11H4FC', 'M11H5FC', 'M11H6FC', 'M11H7FC', 'M11H8FC',
#               'M11H9FC', 'M11H10FC', 'M11H11FC', 'M11H12FC', 'M11H13FC', 'M11H14FC', 'M11H15FC', 'M11H16FC', 'M11H17FC', 'M11H18FC',
#               'M11H19FC', 'M11H20FC', 'M11H21FC', 'M11H22FC', 'M11H23FC', 'M11H24FC',
#               'M11H1PC', 'M11H2PC', 'M11H3PC', 'M11H4PC', 'M11H5PC', 'M11H6PC', 'M11H7PC', 'M11H8PC',
#               'M11H9PC', 'M11H10PC', 'M11H11PC', 'M11H12PC', 'M11H13PC', 'M11H14PC', 'M11H15PC', 'M11H16PC', 'M11H17PC', 'M11H18PC',
#               'M11H19PC', 'M11H20PC', 'M11H21PC', 'M11H22PC', 'M11H23PC', 'M11H24PC']
#
#         t9 = ['M9H1F', 'M9H2F', 'M9H3F', 'M9H4F', 'M9H5F', 'M9H6F', 'M9H7F', 'M9H8F',
#               'M9H9F', 'M9H10F', 'M9H11F', 'M9H12F', 'M9H13F', 'M9H14F', 'M9H15F', 'M9H16F', 'M9H17F', 'M9H18F',
#               'M9H19F', 'M9H20F', 'M9H21F', 'M9H22F', 'M9H23F', 'M9H24F',
#               'M9H1P', 'M9H2P', 'M9H3P', 'M9H4P', 'M9H5P', 'M9H6P', 'M9H7P', 'M9H8P',
#               'M9H9P', 'M9H10P', 'M9H11P', 'M9H12P', 'M9H13P', 'M9H14P', 'M9H15P', 'M9H16P', 'M9H17P', 'M9H18P',
#               'M9H19P', 'M9H20P', 'M9H21P', 'M9H22P', 'M9H23P', 'M9H24P']
#
#         message = {
#             "time": now_time,
#             "body": [
#                 {
#                     "device": "EMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": t9
#                 }
#             ]
#         }
#
#         message1 = {
#             "time": now_time,
#             "body": [
#                 {
#                     "device": "EMS",
#                     "datatype": "parameter",
#                     "totalcall": "0",
#                     "body": C11
#                 }
#             ]
#         }
#
#         # 准备下发策略
#         req_topic = f"req/database/realtime/{station_english_name}/{station_app}"
#         res_topic = f"res/database/realtime/{station_english_name}/{station_app}"
#
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#         json_message1 = json.dumps(message1)
#
#         # 发布
#         client.publish(req_topic, json_message)
#         client.publish(req_topic, json_message1)
#
#
#         # time.sleep(3)
#         # 获取订阅结果
#         client.subscribe(res_topic)
#
#         client.loop_forever()
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "发布订阅成功",
#                 },
#             }
#         )