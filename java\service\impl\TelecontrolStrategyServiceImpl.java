package com.robestec.analysis.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.constant.TelecontrolConstants;
import com.robestec.analysis.dto.telecontrol.*;
import com.robestec.analysis.entity.*;
import com.robestec.analysis.exception.TelecontrolException;
import com.robestec.analysis.mapper.*;
import com.robestec.analysis.service.TelecontrolStrategyService;

import com.robestec.analysis.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 远程控制策略服务实现类
 * 对应Python中telecontrol_strategy.py的Java实现
 */
@Slf4j
@Service
public class TelecontrolStrategyServiceImpl extends SuperServiceImpl<TPowerDeliverRecordsMapper, TPowerDeliverRecords>
        implements TelecontrolStrategyService {

    @Autowired
    private TPowerDeliverRecordsServiceImpl powerDeliverRecordsService;

    @Autowired
    private TPlanHistoryServiceImpl planHistoryService;

    @Autowired
    private TPanLogsServiceImpl panLogsService;

    @Autowired
    private ProjectPackServiceImpl projectPackService;

    @Autowired
    private StationServiceImpl stationService;

    @Autowired
    private TPlanPowerRecordsServiceImpl planPowerRecordsService;

    /**
     * 策略模板下载
     * 对应Python中的StrategyTemplate方法
     */
    @Override
    public void downloadStrategyTemplate(HttpServletResponse response) {
        log.info("开始下载策略模板");
        
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("策略模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"开始时间", "结束时间", "PV", "充电配置", "RL"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建示例数据行
            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("00:00");
            dataRow.createCell(1).setCellValue("23:59");
            dataRow.createCell(2).setCellValue("100");
            dataRow.createCell(3).setCellValue("80");
            dataRow.createCell(4).setCellValue("50");
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + TelecontrolConstants.STRATEGY_TEMPLATE_FILE);
            
            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            
            log.info("策略模板下载完成");
            
        } catch (IOException e) {
            log.error("策略模板下载失败", e);
            throw new RuntimeException("策略模板下载失败: " + e.getMessage());
        }
    }

    /**
     * 策略模板解析导入
     * 对应Python中的StrategyImport方法
     */
    @Override
    public StrategyImportVO importStrategy(StrategyImportDTO importDTO) {
        log.info("开始解析策略模板，文件名: {}, 语言: {}",
            importDTO.getFiles().getOriginalFilename(), importDTO.getLang());

        try (InputStream inputStream = importDTO.getFiles().getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            List<StrategyImportVO.StrategyDataItemVO> dataList = new ArrayList<>();
            List<String> monthList = Arrays.asList(
                "1月", "2月", "3月", "4月", "5月", "6月",
                "7月", "8月", "9月", "10月", "11月", "12月"
            );

            // 跳过标题行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                StrategyImportVO.StrategyDataItemVO item = new StrategyImportVO.StrategyDataItemVO();

                // 读取各列数据
                item.setStartTime(getCellValueAsString(row.getCell(0)));
                item.setEndTime(getCellValueAsString(row.getCell(1)));
                item.setPv(getCellValueAsObject(row.getCell(2)));
                item.setChargeConfig(getCellValueAsObject(row.getCell(3)));
                item.setRl(getCellValueAsString(row.getCell(4)));

                dataList.add(item);
            }

            workbook.close();

            StrategyImportVO response = new StrategyImportVO();
            response.setName("导入策略_" + System.currentTimeMillis());
            response.setData(dataList);
            response.setMonthList(monthList);

            log.info("策略模板解析完成，共解析 {} 条数据", dataList.size());
            return response;

        } catch (Exception e) {
            log.error("策略模板解析失败", e);
            throw new TelecontrolException.FileProcessException("策略模板解析失败: " + e.getMessage());
        }
    }

    /**
     * 获取电站容量信息
     * 对应Python中的PowerPlanStations方法
     */
    @Override
    public List<TelecontrolResponse.StationCapacityResponse> getPowerPlanStations() {
        log.info("开始获取电站容量信息");

        // 使用StationService替代mapper调用
        List<Station> stationList = stationService.list();

        List<TelecontrolResponse.StationCapacityResponse> responseList = stationList.stream()
            .map(this::convertToStationCapacityResponse)
            .collect(Collectors.toList());

        log.info("获取电站容量信息完成，共 {} 个电站", responseList.size());
        return responseList;
    }

    /**
     * 刷新电站容量信息
     * 对应Python中的PowerPlanStationsRefresh方法
     */
    @Override
    public List<TelecontrolResponse.StationCapacityResponse> refreshPowerPlanStations(List<Long> stationIds) {
        log.info("开始刷新电站容量信息，电站ID: {}", stationIds);

        // 使用StationService替代mapper调用
        List<Station> stationList = stationService.listByIds(stationIds);

        List<TelecontrolResponse.StationCapacityResponse> responseList = stationList.stream()
            .map(this::convertToStationCapacityResponse)
            .collect(Collectors.toList());

        log.info("刷新电站容量信息完成，共 {} 个电站", responseList.size());
        return responseList;
    }

    /**
     * 功率计划下发列表
     * 对应Python中的PowerPlanList方法
     */
    @Override
    public IPage<TelecontrolResponse.PowerPlanListResponse> getPowerPlanList(PowerPlanRequest request) {
        log.info("开始查询功率计划列表，参数: {}", JSON.toJSONString(request));
        
        Page<TPowerDeliverRecords> page = new Page<>(
            request.getPageNum() != null ? request.getPageNum() : TelecontrolConstants.DEFAULT_PAGE_NUM,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE
        );
        
        // 使用PowerDeliverRecordsService替代mapper调用
        PageResult<TPowerDeliverRecordsVO> serviceResult = powerDeliverRecordsService.selectPowerPlanList(
            request.getPageNum() != null ? request.getPageNum() : TelecontrolConstants.DEFAULT_PAGE_NUM,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE,
            request.getPlanName(), request.getStatus(), request.getPlanType(),
            request.getStartTime(), request.getEndTime()
        );

        // 转换为IPage格式
        IPage<TPowerDeliverRecords> resultPage = new Page<>(
            serviceResult.getData().size() > 0 ? request.getPageNum() : 1,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE,
            serviceResult.getCount()
        );

        // 将VO转换为Entity（这里需要转换逻辑）
        List<TPowerDeliverRecords> records = serviceResult.getData().stream()
            .map(this::convertVOToEntity)
            .collect(Collectors.toList());
        resultPage.setRecords(records);
        
        // 转换为响应对象
        IPage<TelecontrolResponse.PowerPlanListResponse> responsePage = resultPage.convert(this::convertToPowerPlanListResponse);
        
        log.info("查询功率计划列表完成，共 {} 条记录", responsePage.getTotal());
        return responsePage;
    }

    /**
     * 新增计划功率
     * 对应Python中的PowerPlanAdd方法
     * 完整实现包括：参数验证、用户密码验证、功率数据验证、记录创建、异步翻译
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TelecontrolResponse.CommonResult<String> addPowerPlan(PowerPlanRequest request) {
        log.info("开始新增功率计划，参数: {}", JSON.toJSONString(request));

        try {
            // 1. 验证必填字段
            if (!StringUtils.hasText(request.getPlanName()) ||
                !StringUtils.hasText(request.getPowerList()) ||
                !StringUtils.hasText(request.getStationList()) ||
                request.getPlanType() == null) {
                return TelecontrolResponse.CommonResult.error("必填字段缺失");
            }

            // 2. 验证账号密码
            if (!StringUtils.hasText(request.getAccount()) || !StringUtils.hasText(request.getPassword())) {
                return TelecontrolResponse.CommonResult.error("请输入账号密码！");
            }

            // 3. 验证用户密码
            ValidationResult passwordValidation = validateUserPassword(request.getAccount(), request.getPassword());
            if (!passwordValidation.isValid()) {
                return TelecontrolResponse.CommonResult.error(passwordValidation.getMessage());
            }

            // 4. 验证下发账号与登录账号是否一致
            // TODO: 这里需要从session中获取当前登录用户进行验证
            // if (!currentUser.getAccount().equals(request.getAccount())) {
            //     return TelecontrolResponse.CommonResult.error("下发账号与登录账号不符");
            // }

            // 5. 解析powerList和stationList（对应Python中的literal_eval）
            List<Map<String, Object>> powerList = parseJsonToList(request.getPowerList());
            List<Map<String, Object>> stationList = parseJsonToList(request.getStationList());

            if (stationList.isEmpty()) {
                return TelecontrolResponse.CommonResult.error("电站信息不能为空！");
            }

            // 6. 验证功率数据（对应Python中的data_check_power）
            ValidationResult powerValidation = dataCheckPower(powerList, request.getPlanType(), stationList);
            if (!powerValidation.isValid()) {
                return TelecontrolResponse.CommonResult.error(powerValidation.getMessage());
            }

            // 7. 验证计划名称是否重复
            int count = powerDeliverRecordsService.countByNameAndUserId(request.getPlanName(), request.getUserId());
            if (count > 0) {
                return TelecontrolResponse.CommonResult.error("计划名称已存在");
            }

            // 8. 创建功率下发记录
            TPowerDeliverRecords record = new TPowerDeliverRecords();
            record.setName(request.getPlanName());
            record.setEnName(request.getPlanName());
            record.setPowerList(JSON.toJSONString(powerList)); // 转换为JSON字符串
            record.setStationList(JSON.toJSONString(stationList));
            record.setUserId(request.getUserId());
            record.setUserName(request.getAccount());
            record.setPlanType(request.getPlanType());
            record.setIsUse(TelecontrolConstants.UseFlag.USED);
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());

            boolean saveResult = powerDeliverRecordsService.save(record);
            if (!saveResult) {
                return TelecontrolResponse.CommonResult.error("保存功率计划失败");
            }

            // 9. 异步翻译任务
            publishTranslationTask(record.getId(), request.getPlanName(), request.getLang());

            log.info("新增功率计划成功，记录ID: {}", record.getId());
            return TelecontrolResponse.CommonResult.success("新增功率计划成功");

        } catch (Exception e) {
            log.error("新增功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("新增功率计划失败: " + e.getMessage());
        }
    }

    // 辅助方法
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((int) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private Object getCellValueAsObject(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            default:
                return null;
        }
    }

    private TelecontrolResponse.StationCapacityResponse convertToStationCapacityResponse(Station station) {
        TelecontrolResponse.StationCapacityResponse response = new TelecontrolResponse.StationCapacityResponse();
        response.setId(station.getId());
        response.setName(station.getName());
        response.setDescr(station.getDescr());
        // 注意：这里需要根据实际的Station实体字段进行调整
        // response.setElectricPower(station.getElectricPower());
        // response.setRealPower(station.getRealPower());
        return response;
    }

    private TelecontrolResponse.PowerPlanListResponse convertToPowerPlanListResponse(TPowerDeliverRecords record) {
        TelecontrolResponse.PowerPlanListResponse response = new TelecontrolResponse.PowerPlanListResponse();
        response.setId(record.getId());
        response.setName(record.getName());
        response.setEnName(record.getEnName());
        response.setPlanType(record.getPlanType());
        response.setPlanTypeName(getPlanTypeName(record.getPlanType()));
        response.setUserName(record.getUserName());
        response.setCreateTime(record.getCreateTime());
        return response;
    }

    /**
     * 功率计划详情
     * 对应Python中的PowerPlanDetail方法
     */
    @Override
    public TelecontrolResponse.PowerPlanDetailResponse getPowerPlanDetail(Long id) {
        log.info("开始查询功率计划详情，ID: {}", id);

        TPowerDeliverRecords record = powerDeliverRecordsService.getById(id);
        if (record == null) {
            throw new RuntimeException("功率计划不存在");
        }

        TelecontrolResponse.PowerPlanDetailResponse response = new TelecontrolResponse.PowerPlanDetailResponse();
        response.setId(record.getId());
        response.setName(record.getName());
        response.setEnName(record.getEnName());
        response.setPowerList(record.getPowerList());
        response.setStationList(record.getStationList());
        response.setPlanType(record.getPlanType());
        response.setPlanTypeName(getPlanTypeName(record.getPlanType()));
        response.setUserName(record.getUserName());
        response.setCreateTime(record.getCreateTime());

        log.info("查询功率计划详情完成");
        return response;
    }

    /**
     * 修改计划功率
     * 对应Python中的PowerPlanUpdate方法
     * 完整实现包括：更新主记录、删除关联记录、删除计划历史、异步翻译
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TelecontrolResponse.CommonResult<String> updatePowerPlan(PowerPlanRequest request) {
        log.info("开始修改功率计划，参数: {}", JSON.toJSONString(request));

        try {
            // 1. 验证记录是否存在
            TPowerDeliverRecords existRecord = powerDeliverRecordsService.getById(request.getId());
            if (existRecord == null) {
                return TelecontrolResponse.CommonResult.error("功率计划不存在");
            }

            // 2. 验证计划名称是否重复（排除当前记录）
            TPowerDeliverRecords duplicateRecord = powerDeliverRecordsService.selectByNameAndUserId(
                request.getPlanName(), request.getUserId());
            if (duplicateRecord != null && !duplicateRecord.getId().equals(request.getId())) {
                return TelecontrolResponse.CommonResult.error("计划名称已存在");
            }

            // 3. 更新主记录的基本信息
            existRecord.setName(request.getPlanName());
            existRecord.setEnName(request.getPlanName()); // 设置英文名称
            existRecord.setPowerList(request.getPowerList());
            existRecord.setStationList(request.getStationList());
            existRecord.setUserId(request.getUserId());
            existRecord.setUserName(request.getAccount());
            existRecord.setPlanType(request.getPlanType());
            existRecord.setUpdateTime(LocalDateTime.now());

            // 保存主记录更新
            boolean updateResult = powerDeliverRecordsService.updateById(existRecord);
            if (!updateResult) {
                return TelecontrolResponse.CommonResult.error("更新功率计划主记录失败");
            }

            // 4. 删除关联的计划功率记录 (对应Python中的PlanDeliverRecords)
            List<TPlanPowerRecords> planPowerRecords = planPowerRecordsService.selectByPowerId(request.getId());
            if (!planPowerRecords.isEmpty()) {
                // 获取所有关联的计划ID
                List<Long> planIds = planPowerRecords.stream()
                    .map(TPlanPowerRecords::getPlanId)
                    .distinct()
                    .collect(Collectors.toList());

                // 删除计划功率关联记录
                List<Long> powerRecordIds = planPowerRecords.stream()
                    .map(TPlanPowerRecords::getId)
                    .collect(Collectors.toList());
                planPowerRecordsService.removeByIds(powerRecordIds);

                // 5. 删除相关的计划历史记录 (对应Python中的StationPlanHistory)
                if (!planIds.isEmpty()) {
                    planHistoryService.removeByIds(planIds);
                    log.info("删除了 {} 条计划历史记录", planIds.size());
                }

                log.info("删除了 {} 条计划功率关联记录", powerRecordIds.size());
            }

            // 6. 异步翻译任务 (对应Python中的Redis发布)
            publishTranslationTask(request.getId(), request.getPlanName(), request.getLang());

            log.info("修改功率计划成功，记录ID: {}", request.getId());
            return TelecontrolResponse.CommonResult.success("修改功率计划成功");

        } catch (Exception e) {
            log.error("修改功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("修改功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 停止计划功率
     * 对应Python中的powerPlanStop方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TelecontrolResponse.CommonResult<String> stopPowerPlan(Long id, String lang) {
        log.info("开始停止功率计划，ID: {}, 语言: {}", id, lang);

        try {
            // 查询相关的计划历史记录
            List<TPlanHistory> historyList = planHistoryService.selectByPlanId(id);

            if (historyList.isEmpty()) {
                return TelecontrolResponse.CommonResult.error("未找到相关的计划历史记录");
            }

            // 更新所有相关历史记录的状态为已停止
            for (TPlanHistory history : historyList) {
                planHistoryService.updateStatus(history.getId(), TelecontrolConstants.PlanStatus.STOPPED);
            }

            log.info("停止功率计划成功，共更新 {} 条历史记录", historyList.size());
            return TelecontrolResponse.CommonResult.success("停止功率计划成功");

        } catch (Exception e) {
            log.error("停止功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("停止功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 删除功率计划
     * 对应Python中的powerPlanDelete方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TelecontrolResponse.CommonResult<String> deletePowerPlan(Long id, String account, String password) {
        log.info("开始删除功率计划，ID: {}, 账号: {}", id, account);

        try {
            // 验证记录是否存在
            TPowerDeliverRecords record = powerDeliverRecordsService.getById(id);
            if (record == null) {
                return TelecontrolResponse.CommonResult.error("功率计划不存在");
            }

            // TODO: 这里应该添加账号密码验证逻辑
            // validateAccountAndPassword(account, password);

            // 软删除功率下发记录
            powerDeliverRecordsService.softDeleteById(id);

            // 查询并软删除相关的计划历史记录
            List<TPlanHistory> historyList = planHistoryService.selectByPlanId(id);
            if (!historyList.isEmpty()) {
                List<Long> historyIds = historyList.stream()
                    .map(TPlanHistory::getId)
                    .collect(Collectors.toList());
                planHistoryService.softDeleteByIds(historyIds);
            }

            log.info("删除功率计划成功，记录ID: {}", id);
            return TelecontrolResponse.CommonResult.success("删除功率计划成功");

        } catch (Exception e) {
            log.error("删除功率计划失败", e);
            return TelecontrolResponse.CommonResult.error("删除功率计划失败: " + e.getMessage());
        }
    }

    /**
     * 查询下发记录列表
     * 对应Python中的GetPlanHis方法
     */
    @Override
    public IPage<TelecontrolResponse.PlanHistoryResponse> getPlanHistory(StrategyRequest.PlanHistoryRequest request) {
        log.info("开始查询下发记录列表，参数: {}", JSON.toJSONString(request));

        Page<TPanLogs> page = new Page<>(
            request.getPageNum() != null ? request.getPageNum() : TelecontrolConstants.DEFAULT_PAGE_NUM,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE
        );

        // 处理类型名称过滤
        List<String> typeNames = null;
        if (StringUtils.hasText(request.getTypeName())) {
            typeNames = Arrays.asList(request.getTypeName().split(","));
        }

        // 使用PanLogsService替代mapper调用
        PageResult<TPanLogsVO> serviceResult = panLogsService.selectPlanHistoryList(
            request.getPageNum() != null ? request.getPageNum() : TelecontrolConstants.DEFAULT_PAGE_NUM,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE,
            request.getStation(), request.getStatus(), typeNames,
            request.getStartTime(), request.getEndTime()
        );

        // 转换为IPage格式
        IPage<TPanLogs> resultPage = new Page<>(
            serviceResult.getData().size() > 0 ? request.getPageNum() : 1,
            request.getPageSize() != null ? request.getPageSize() : TelecontrolConstants.DEFAULT_PAGE_SIZE,
            serviceResult.getCount()
        );

        // 将VO转换为Entity
        List<TPanLogs> logs = serviceResult.getData().stream()
            .map(this::convertVOToEntity)
            .collect(Collectors.toList());
        resultPage.setRecords(logs);

        // 转换为响应对象
        IPage<TelecontrolResponse.PlanHistoryResponse> responsePage =
            resultPage.convert(this::convertToPlanHistoryResponse);

        log.info("查询下发记录列表完成，共 {} 条记录", responsePage.getTotal());
        return responsePage;
    }

    /**
     * 导出下发记录
     * 对应Python中的planHisExport方法
     */
    @Override
    public void exportPlanHistory(StrategyRequest.PlanHistoryExportRequest request, HttpServletResponse response) {
        log.info("开始导出下发记录，参数: {}", JSON.toJSONString(request));

        try {
            // 处理类型名称过滤
            List<String> typeNames = null;
            if (StringUtils.hasText(request.getTypeName())) {
                typeNames = Arrays.asList(request.getTypeName().split(","));
            }

            // 查询所有符合条件的记录
            List<TPanLogs> logsList = panLogsService.selectAllPlanHistory(
                request.getStation(), request.getStatus(), typeNames,
                request.getStartTime(), request.getEndTime()
            );

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("下发记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"电站", "类型名称", "描述", "状态", "时间", "用户名"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据行
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < logsList.size(); i++) {
                TPanLogs log = logsList.get(i);
                Row dataRow = sheet.createRow(i + 1);

                dataRow.createCell(0).setCellValue(log.getStation());
                dataRow.createCell(1).setCellValue(log.getTypeName());
                dataRow.createCell(2).setCellValue(log.getContent());
                dataRow.createCell(3).setCellValue(getStatusName(log.getStatus()));
                dataRow.createCell(4).setCellValue(log.getCreateTime().format(formatter));
                dataRow.createCell(5).setCellValue(log.getUserName());
            }

            // 设置响应头
            String fileName = "下发记录_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("导出下发记录完成，共导出 {} 条记录", logsList.size());

        } catch (Exception e) {
            log.error("导出下发记录失败", e);
            throw new RuntimeException("导出下发记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询下发类型
     * 对应Python中的GetIssuanceType方法
     */
    @Override
    public TelecontrolResponse.IssuanceTypeResponse getIssuanceType() {
        log.info("开始查询下发类型");

        TelecontrolResponse.IssuanceTypeResponse response = new TelecontrolResponse.IssuanceTypeResponse();
        response.setIssuanceTypes(TelecontrolConstants.ISSUANCE_TYPE);

        log.info("查询下发类型完成，共 {} 种类型", TelecontrolConstants.ISSUANCE_TYPE.size());
        return response;
    }

    /**
     * 另存项目包
     * 对应Python中的ProjectPackAdd方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TelecontrolResponse.CommonResult<String> addProjectPack(StrategyRequest.ProjectPackAddRequest request) {
        log.info("开始另存项目包，参数: {}", JSON.toJSONString(request));

        try {
            // 验证项目包名称是否重复
            List<ProjectPackVO> existPacks = projectPackService.getProjectPackByName(request.getName());
            boolean nameExists = existPacks.stream()
                .anyMatch(pack -> pack.getUserId().equals(request.getUserId()));
            if (nameExists) {
                return TelecontrolResponse.CommonResult.error("项目包名称已存在");
            }

            // 创建项目包记录
            ProjectPack projectPack = new ProjectPack();
            projectPack.setUserId(request.getUserId());
            projectPack.setName(request.getName());
            projectPack.setData(request.getData());
            projectPack.setIsUse(TelecontrolConstants.UseFlag.USED);
            projectPack.setCreateTime(LocalDateTime.now());
            projectPack.setUpdateTime(LocalDateTime.now());

            projectPackService.save(projectPack);

            log.info("另存项目包成功，项目包ID: {}", projectPack.getId());
            return TelecontrolResponse.CommonResult.success("另存项目包成功");

        } catch (Exception e) {
            log.error("另存项目包失败", e);
            return TelecontrolResponse.CommonResult.error("另存项目包失败: " + e.getMessage());
        }
    }

    /**
     * 加载项目包列表
     * 对应Python中的ProjectPackList方法
     */
    @Override
    public List<TelecontrolResponse.ProjectPackResponse> getProjectPackList(Long userId) {
        log.info("开始加载项目包列表，用户ID: {}", userId);

        List<ProjectPackVO> projectPackVOs = projectPackService.getProjectPackByUserId(userId);

        // 将VO转换为Entity，然后转换为Response
        List<TelecontrolResponse.ProjectPackResponse> responseList = projectPackVOs.stream()
            .map(this::convertVOToProjectPack)
            .map(this::convertToProjectPackResponse)
            .collect(Collectors.toList());

        log.info("加载项目包列表完成，共 {} 个项目包", responseList.size());
        return responseList;
    }

    // 辅助转换方法
    private TelecontrolResponse.PlanHistoryResponse convertToPlanHistoryResponse(TPanLogs log) {
        TelecontrolResponse.PlanHistoryResponse response = new TelecontrolResponse.PlanHistoryResponse();
        response.setId(log.getId());
        response.setStation(log.getStation());
        response.setTypeName(log.getTypeName());
        response.setDescr(log.getContent());
        response.setStatus(log.getStatus());
        response.setStatusName(getStatusName(log.getStatus()));
        response.setTime(log.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setUserName(log.getUserName());
        return response;
    }

    private TelecontrolResponse.ProjectPackResponse convertToProjectPackResponse(ProjectPack projectPack) {
        TelecontrolResponse.ProjectPackResponse response = new TelecontrolResponse.ProjectPackResponse();
        response.setName(projectPack.getName());

        // 解析JSON数据
        try {
            Object data = JSON.parse(projectPack.getData());
            response.setData(data);
        } catch (Exception e) {
            log.warn("解析项目包数据失败，项目包ID: {}", projectPack.getId(), e);
            response.setData(projectPack.getData());
        }

        return response;
    }

    private String getStatusName(Integer status) {
        if (status == null) return "";

        switch (status) {
            case TelecontrolConstants.DeliveryStatus.SUCCESS:
                return "成功";
            case TelecontrolConstants.DeliveryStatus.FAILURE:
                return "失败";
            default:
                return "未知";
        }
    }

    private String getPlanTypeName(Integer planType) {
        if (planType == null) return "";

        switch (planType) {
            case TelecontrolConstants.PlanType.CUSTOM:
                return "自定义";
            case TelecontrolConstants.PlanType.PERIODIC:
                return "周期性";
            case TelecontrolConstants.PlanType.HOLIDAY:
                return "节假日";
            default:
                return "未知";
        }
    }

    /**
     * 将TPowerDeliverRecordsVO转换为TPowerDeliverRecords实体
     */
    private TPowerDeliverRecords convertVOToEntity(TPowerDeliverRecordsVO vo) {
        TPowerDeliverRecords entity = new TPowerDeliverRecords();
        entity.setId(vo.getId());
        entity.setName(vo.getName());
        entity.setEnName(vo.getEnName());
        entity.setPowerList(vo.getPowerList());
        entity.setStationList(vo.getStationList());
        entity.setUserId(vo.getUserId());
        entity.setUserName(vo.getUserName());
        entity.setPlanType(vo.getPlanType());
        entity.setCreateTime(vo.getCreateTime());
        entity.setUpdateTime(vo.getUpdateTime());
        return entity;
    }

    /**
     * 将TPanLogsVO转换为TPanLogs实体
     */
    private TPanLogs convertVOToEntity(TPanLogsVO vo) {
        TPanLogs entity = new TPanLogs();
        entity.setId(vo.getId());
        entity.setProjectName(vo.getProjectName());
        entity.setStation(vo.getStation());
        entity.setUserId(vo.getUserId());
        entity.setUserName(vo.getUserName());
        entity.setTypeName(vo.getTypeName());
        entity.setContent(vo.getContent());
        entity.setStatus(vo.getStatus());
        entity.setCreateTime(vo.getCreateTime());
        entity.setUpdateTime(vo.getUpdateTime());
        return entity;
    }

    /**
     * 发布异步翻译任务
     * 对应Python中的Redis发布翻译任务
     */
    private void publishTranslationTask(Long id, String name, String lang) {
        try {
            // 构建翻译任务数据
            Map<String, Object> translationData = new HashMap<>();
            translationData.put("id", id);
            translationData.put("table", "t_power_deliver_records");

            Map<String, Object> updateData = new HashMap<>();
            updateData.put("name", name);
            translationData.put("update_data", updateData);

            // 确定发布的频道名称
            String channelName = "zh".equals(lang) ? "en_translate_pub" : "zh_translate_pub";

            // 发布到Redis (这里需要根据实际的Redis配置进行调整)
            String jsonData = JSON.toJSONString(translationData);
            log.info("发布翻译任务到Redis频道: {}, 数据: {}", channelName, jsonData);

            // TODO: 实际的Redis发布实现
            // redisTemplate.convertAndSend(channelName, jsonData);

        } catch (Exception e) {
            log.error("发布翻译任务失败", e);
            // 翻译任务失败不影响主流程，只记录日志
        }
    }

    /**
     * 验证功率计划时间是否重叠
     * 对应Python中的时间重叠验证逻辑
     */
    private boolean validatePowerPlanTimeOverlap(String powerListJson) {
        try {
            if (!StringUtils.hasText(powerListJson)) {
                return true;
            }

            // 解析功率计划列表
            List<Map<String, Object>> powerList = JSON.parseArray(powerListJson, Map.class);
            if (powerList == null || powerList.isEmpty()) {
                return true;
            }

            // 提取时间段列表
            List<TimeRange> timeRanges = new ArrayList<>();
            for (Map<String, Object> plan : powerList) {
                String startTime = (String) plan.get("start_time");
                String endTime = (String) plan.get("end_time");

                if (StringUtils.hasText(startTime) && StringUtils.hasText(endTime)) {
                    timeRanges.add(new TimeRange(startTime, endTime));
                }
            }

            // 检查时间重叠
            return !hasTimeOverlap(timeRanges);

        } catch (Exception e) {
            log.error("验证功率计划时间重叠失败", e);
            return false;
        }
    }

    /**
     * 检查时间范围是否有重叠
     */
    private boolean hasTimeOverlap(List<TimeRange> timeRanges) {
        if (timeRanges.size() <= 1) {
            return false;
        }

        // 按开始时间排序
        timeRanges.sort((a, b) -> a.getStartTime().compareTo(b.getStartTime()));

        // 检查相邻时间段是否重叠
        for (int i = 0; i < timeRanges.size() - 1; i++) {
            TimeRange current = timeRanges.get(i);
            TimeRange next = timeRanges.get(i + 1);

            if (current.getEndTime().compareTo(next.getStartTime()) > 0) {
                return true; // 有重叠
            }
        }

        return false; // 无重叠
    }

    /**
     * 验证电站列表是否有效
     */
    private boolean validateStationList(String stationListJson) {
        try {
            if (!StringUtils.hasText(stationListJson)) {
                return false;
            }

            // 解析电站列表
            List<String> stationList = JSON.parseArray(stationListJson, String.class);
            if (stationList == null || stationList.isEmpty()) {
                return false;
            }

            // 验证电站是否存在
            for (String stationName : stationList) {
                if (!StringUtils.hasText(stationName)) {
                    return false;
                }

                // 检查电站是否存在（这里可以根据实际需求调整验证逻辑）
                List<StationVO> stations = stationService.getStationByName(stationName);
                if (stations.isEmpty()) {
                    log.warn("电站不存在: {}", stationName);
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("验证电站列表失败", e);
            return false;
        }
    }

    /**
     * 验证用户密码
     * 对应Python中的validate_user_passwd方法
     */
    private ValidationResult validateUserPassword(String account, String password) {
        try {
            // TODO: 这里需要根据实际的用户表进行查询
            // 目前简化实现，实际应该查询用户表验证密码

            // 1. MD5加密密码（对应Python中的computeMD5）
            String encryptedPassword = computeMD5(password);

            // 2. 查询用户信息
            // User user = userService.findByAccountOrPhone(account);
            // if (user == null) {
            //     return new ValidationResult(false, "用户信息错误");
            // }
            //
            // if (!user.getPassword().equals(encryptedPassword)) {
            //     return new ValidationResult(false, "用户名或密码错误");
            // }

            // 临时实现：简单验证
            if (!StringUtils.hasText(account) || !StringUtils.hasText(password)) {
                return new ValidationResult(false, "账号或密码不能为空");
            }

            return new ValidationResult(true, "");

        } catch (Exception e) {
            log.error("验证用户密码失败", e);
            return new ValidationResult(false, "密码验证失败");
        }
    }

    /**
     * MD5加密
     * 对应Python中的computeMD5方法
     */
    private String computeMD5(String input) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            log.error("MD5加密失败", e);
            return input;
        }
    }

    /**
     * 解析JSON字符串为List
     * 对应Python中的ast.literal_eval方法
     */
    private List<Map<String, Object>> parseJsonToList(String jsonString) {
        try {
            if (!StringUtils.hasText(jsonString)) {
                return new ArrayList<>();
            }
            return JSON.parseArray(jsonString, Map.class);
        } catch (Exception e) {
            log.error("解析JSON字符串失败: {}", jsonString, e);
            return new ArrayList<>();
        }
    }

    /**
     * 功率数据验证
     * 对应Python中的data_check_power方法
     */
    private ValidationResult dataCheckPower(List<Map<String, Object>> powerList, Integer planType, List<Map<String, Object>> stationList) {
        try {
            Map<LocalDateTime, Map<String, Object>> powerDict = new HashMap<>(); // 排序用

            for (Map<String, Object> power : powerList) {
                String cronData = null;
                String startTimeStr = null;
                String endTimeStr = null;

                // 根据计划类型处理时间
                if (planType == 1) { // 自定义
                    startTimeStr = (String) power.get("start_time");
                    endTimeStr = (String) power.get("end_time");
                } else if (planType == 2) { // 周期性
                    cronData = (String) power.get("cron_data");
                    startTimeStr = (String) power.get("start_time");
                    endTimeStr = (String) power.get("end_time");
                } else { // 节假日
                    cronData = (String) power.get("cron_data");
                    startTimeStr = (String) power.get("start_time");
                    endTimeStr = (String) power.get("end_time");
                }

                Object option = power.get("option");
                Object limit = power.get("limit");
                Object isFollow = power.get("is_follow");

                // 验证执行时间不能为空
                if (!StringUtils.hasText(startTimeStr) || !StringUtils.hasText(endTimeStr)) {
                    return new ValidationResult(false, "执行时间不能空");
                }

                LocalDateTime startTime;
                LocalDateTime endTime;

                if (planType == 1) { // 自定义计划
                    try {
                        startTime = LocalDateTime.parse(startTimeStr.replace(" ", "T"));
                        endTime = LocalDateTime.parse(endTimeStr.replace(" ", "T"));
                    } catch (Exception e) {
                        return new ValidationResult(false, "时间格式错误");
                    }

                    // 验证时间范围
                    LocalDateTime now = LocalDateTime.now();
                    if (startTime.isBefore(now)) {
                        return new ValidationResult(false, "时间范围错误：开始时间不能小于当前时间！");
                    }

                    if (!endTime.isAfter(startTime)) {
                        return new ValidationResult(false, "时间范围错误：结束时间不能小于等于开始时间！");
                    }

                    // 检查时间重合
                    if (powerDict.containsKey(startTime)) {
                        return new ValidationResult(false, "任务时间重合，新增失败！");
                    } else {
                        powerDict.put(startTime, power);
                    }
                } else {
                    // 周期性和节假日计划
                    if (!StringUtils.hasText(cronData)) {
                        return new ValidationResult(false, "周期性、节假日的周期时间不能空");
                    }

                    // 计算执行日期（对应Python中的cron_tab_date）
                    String executionDate = cronTabDate(cronData);
                    startTimeStr = executionDate + " " + startTimeStr;
                    endTimeStr = executionDate + " " + endTimeStr;

                    try {
                        startTime = LocalDateTime.parse(startTimeStr.replace(" ", "T"));
                        endTime = LocalDateTime.parse(endTimeStr.replace(" ", "T"));
                    } catch (Exception e) {
                        return new ValidationResult(false, "时间格式错误");
                    }
                }

                // 检查电站计划重复（简化实现）
                for (Map<String, Object> station : stationList) {
                    String stationName = (String) station.get("station_name");
                    if (StringUtils.hasText(stationName)) {
                        // TODO: 这里应该查询TPlanHistory表检查是否有重复的计划
                        // 简化实现，暂时跳过
                    }
                }
            }

            return new ValidationResult(true, "");

        } catch (Exception e) {
            log.error("功率数据验证失败", e);
            return new ValidationResult(false, "功率数据验证失败: " + e.getMessage());
        }
    }

    /**
     * 根据cron表达式计算第一次执行日期
     * 对应Python中的cron_tab_date方法
     */
    private String cronTabDate(String cronTab) {
        try {
            // 简化实现：返回当前日期
            // 实际应该根据cron表达式计算下次执行时间
            LocalDate today = LocalDate.now();
            return today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            log.error("计算cron执行日期失败", e);
            return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
    }

    /**
     * 验证结果内部类
     */
    private static class ValidationResult {
        private boolean valid;
        private String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        private String startTime;
        private String endTime;

        public TimeRange(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getStartTime() {
            return startTime;
        }

        public String getEndTime() {
            return endTime;
        }
    }
}
