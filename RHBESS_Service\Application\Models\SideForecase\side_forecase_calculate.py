
from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, Text, Column, ForeignKey


class ForecaseCalcuate(user_Base):
    '经评计算输入、输出'
    __tablename__ = "t_side_forecase_calcuate"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True, comment=u"主键")
    input_json = Column(Text, nullable=True, comment=u"经评计算输入")
    output_json = Column(Text, nullable=True, comment=u"经评计算输出")
    p_id = Column(Integer, ForeignKey("t_side_forecase_project.id"), unique=True, nullable=False, comment=u"项目ID")


    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'input_json':'%s', 'output_json':'%s', 'p_id':'%s'}" % (self.id, self.input_json, self.output_json, self.p_id)
