#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-11-06 18:27:32
#@FilePath     : \RHBESS_Serviced:\emot_pjt_rh\TianLuAppBackend\apis\web\urls.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-07 18:00:38


from django.urls import path
from apis.web.views import control_views, report_views
from apis.web.views import organization_view, history_data_views, role_view, map_view, income_view, user_view, report_view, strategy_view, power_load_forecasting_views
from apis.web.strategy_upload import StrategyUploadViews, PriceUploadViews, StrategyListViews, StrategyDownloadViews, StrategyInfoViews, StrategyUpdateViews, StrategyDelViews, PriceDownloadViews, PriceListViews, \
    PriceInfoViews, PriceDelViews, StaitonViews, BaseIncomeView, MergeStrategy

# from web.views import user_view, income_view, map_view, control_views, history_data_views
urlpatterns = [
    # 集中监控--地图大屏--项目集控及项目首页
    path("username_password_login/", user_view.WebUsernamePasswordLoginView.as_view()),  # web账号登录
    # path("income/view/", income_view.WebIncomeView.as_view()),  # web大屏收益
    # path("map/auth_view/", map_view.AuthMapView.as_view()),  # web内部员工大屏地图
    # path("map/health/", map_view.MapHealthView.as_view()),  # web地图健康度
    # path("map/user_view/", map_view.UserMapView.as_view()),  # web 客户大屏地图
    # path("income/unit_capacity/", income_view.UnitCapacityIncomeView.as_view()),  # 单位容量收益
    # path("hours/day_utilization/", income_view.DayUtilizationView.as_view()),  # 日利用小时数
    # path("control/views/", control_views.ControlCardsView.as_view()),  # 项目集控卡片页面
    path("control/views/list", control_views.ControlCardsListView.as_view()),  # 项目集控卡片页面:列表
    path("projects", control_views.ProjectListView.as_view()),  # 项目列表
    # path("control/views/info/<int:pk>", control_views.ControlCardsInfoView.as_view()),  # 项目集控卡片页面：详情
    path("index/income/", control_views.IndexIncomeView.as_view()),  # 项目集控卡片收益                          en
    # path("index/projects/<int:pk>/", control_views.IndexStationsView.as_view()),  # 项目集控站点页面
    # path("index/alarms/<int:pk>/", control_views.WebAlarmDetailView.as_view()),  # web告警详情
    # path("index/electricity_count/day/<int:pk>/", control_views.WebElectricityDayCountView.as_view()),  # web逐时冲放电量
    # path("index/electricity_count/week/", control_views.WebElectricityWeekCountView.as_view()),  # web历史 7 放电量
    # path("index/soc/day/", control_views.SocHistoryView.as_view()),  # web历史 SOC
    # path("index/power_count/day/", control_views.DayPowerView.as_view()),  # 储能单元有功功率
    # path("index/rundays/<int:pk>/", control_views.IndexRunDaysView.as_view()),  # web运行天数
    # path("index/estimated_cap_dis/realtime/", control_views.RealTimeBatteryChargingDischargingView.as_view()),  # 电池充放电状态

    # web 2.0 监控管理--历史数据
    path("history/project/list/", history_data_views.GetProjectListViews.as_view()),  # 获取项目名称及状态  en
    path("history/point/list/", history_data_views.GetPointListViews.as_view()),  # 获取并网点   en
    path("history/dev/num/", history_data_views.GetDevNumViews.as_view()),  # 获取设备编号    en
    path("history/data/item/", history_data_views.GetDataItemViews.as_view()),  # 获取数据项    en
    path("history/dev/data/", history_data_views.GetHistoryDataViews.as_view()),  # 查询历史数据     en

    # web 2.0 系统管理--角色管理
    path("role/list/", role_view.RoleViews.as_view()),  # 查询角色      en
    path("role/add/", role_view.RoleAddViews.as_view()),  # 添加角色    en
    path("role/list/<str:id>/", role_view.RoleInfoViews.as_view()),  # 展示与修改角色     en
    path("role/del/", role_view.RoleDeViews.as_view()),  # 删除角色     en

    # # web 2.0 系统管理--权限管理
    path("role/permissions/list/", role_view.RolePermissionsViews.as_view()),  # 获取角色权限 en
    path("role/permissions/add/", role_view.RolePermissionsAddViews.as_view()),  # 保存权限     en

    # web 2.0 系统管理--用户管理
    path("user/add/", user_view.WebUsernameAddView.as_view()),  # 用户添加      en
    path("user/project/", user_view.WebProjectView.as_view()),  # 项目展示      en
    path("user/list/<str:id>/", user_view.UserInfoViews.as_view()),  # 展示与修改用户      en
    path("user/list/", user_view.UserViews.as_view()),  # 查询用户  en
    path("user/del/", user_view.UserDeViews.as_view()),  # 删除用户   en

    # web 2.0 系统管理--组织管理
    path("organization/list/", organization_view.OrganizationViews.as_view()),  # 查询组织      en
    path("organization/add/", organization_view.OrganizationAddViews.as_view()),  # 添加组织        en
    path("organization/list/<str:id>/", organization_view.OrganizationInfoViews.as_view()),  # 展示与修改组织      en
    path("organization/del/", organization_view.OrganizationDeViews.as_view()),  # 删除       en

    # web 2.0 监控管理--运行日报/周报
    path('report/day/carry_out/', report_view.ReportDayCarryOut.as_view()),  # 手动执行日报任务
    path('report/week/carry_out/', report_view.ReportWeekCarryOut.as_view()),  # 手动执行周报任务
    path('report/month/carry_out/', report_view.ReportMonthCarryOut.as_view()),  # 手动执行月报任务
    path('report/analyze/', report_view.ReportAnalyze.as_view()),  # 分析类型列表

    path("report/day/", report_view.ReportDayViews.as_view()),  # 日报--列表&详情&充放电量统计&下载       en
    path("report/day/update/", report_view.ReportDayUpdateViews.as_view()),  # 日报、周报--保存分析      en
    path("report/day/CgDg/", report_view.ReportDayCgDgViews.as_view()),  # 日报--逐时充放电量       no need
    path("report/day/Soc/", report_view.ReportSOCViews.as_view()),  # 日报--查看soc曲线           no need

    path("report/Week/", report_view.ReportWeekViews.as_view()),  # 周报--列表&详情&统计&下载     en
    path("report/Week/Day", report_view.ReportWeekDayViews.as_view()),   # 运行周报--逐日充放电量     en

    # web 2.0 监控管理--运行月报
    path("reports/list", report_views.RunningReportsListView.as_view()),  # 运行报告-列表     en
    path("reports/detail/<int:pk>", report_views.RunningReportDetailView.as_view()),  # 运行报告-详情，写入分析    en
    path("reports/capaacity_by/<int:pk>", report_views.RunningReportChargeDischargeListView.as_view()),  # 运行报告-逐时（日）充放电量    en
    path("reports/count/<int:pk>", report_views.RunningReportChargeDischargeCountListView.as_view()),  # 运行报告-充放电量统计   en
    # path("reports/soc/<int:pk>", report_views.RunningReportSOCbyDayView.as_view()),  # 运行报告-SOC   ====>没有使用
    path("reports/alerms/<int:pk>", report_views.RunningReportFaultAlartView.as_view()),  # 运行报告-告警记录（日报、周报及月报）     en
    path("reports/Export", report_views.ExportRunningReportsView.as_view()),  # 运行报告-导出     en

    # web 2.0 集中监控-系统监控
    path("system/monitor/projects", control_views.SystemMonitorProjectsView.as_view()),     # 集中监控-系统监控：项目列表  no need
    path("system/monitor/stations/<int:pk>", control_views.SystemMonitorStationsView.as_view()),  # 集中监控-系统监控：项目-站列表  no need
    path("system/monitor/<int:pk>", control_views.SystemMonitorView.as_view()),   # 集中监控-系统监控    no need

    # web 2.0 集中监控-查看版本号
    path("system/version/<int:pk>", control_views.VersionNumberView.as_view()),     # 集中监控-查看版本号  no need

    # web 2.0 监控管理-功率计划下发
    path("power/issued/list/", control_views.PowerIssudView.as_view()),                     # 监控管理-功率计划下发列表     en
    path("power/issued/add/", control_views.PowerIssudView.as_view()),                      # 监控管理-功率计划下发新增     en
    path("power/issued/update/", control_views.PowerIssudView.as_view()),                   # 监控管理-功率计划下发删除     en
    path("power/issued/delete/", control_views.PowerIssudView.as_view()),                   # 监控管理-功率计划下发修改     en
    path("power/issued/detail/", control_views.PowerIssudDetailView.as_view()),             # 监控管理-功率计划下发详情     en
    path("project/station/list/", control_views.ProjectStationView.as_view()),              # 项目并网点         en
    path("project/pack/add/", control_views.ProjectsPackrView.as_view()),                   # 另存项目包         en
    path("project/pack/list/", control_views.ProjectsPackrView.as_view()),                  # 加载项目包列表       en
    path("power/send_smscode/", control_views.SendSmscodeView.as_view()),                   # 功率下发手机短信验证    en
    path("project/flushed/", control_views.FlushedView.as_view()),                          # 功率下发并网点刷新     en
    path("power/issued/mapping/", control_views.IssuedMappingView.as_view()),               # 功率下发状态字典      en

    # web 2.0 + 小程序v2.0 用户自动控制模式管理
    path("user_strategy/category/add", strategy_view.UserStrategyCategoryView.as_view()),     # 用户自动控制策略：新增       en
    path("user_strategy/category/<int:pk>", strategy_view.UserStrategyCategoryView.as_view()),  # 用户自动控制策略：编辑      en
    path("user_strategy/category/list", strategy_view.UserStrategyCategoryView.as_view()),    # 用户自动控制策略：列表      en
    path("user_strategy/category/disposition/list", strategy_view.UserStrategyCategoryDispositionView.as_view()),  # 用户自动控制策略：配置下拉框列表      en
    path("user_strategy/del/<int:pk>", strategy_view.UserStrategyCategoryView.as_view()),   # 用户自动控制策略：删除       en
    path("user_strategy/echo/<int:pk>", strategy_view.UserStrategyEchoView.as_view()),      # 用户自动控制策略：编辑回显        en
    path("user_strategy/detail", strategy_view.UserStrategyEchoView.as_view()),              # 用户自动控制策略：详情         en
    path("user_strategy/station/list", strategy_view.StationListView.as_view()),      # 用户自动控制策略：用户策略关联并网点列表   en
    path('strategy_send_smscode/', strategy_view.StrategySendSmsCodeView.as_view()),     # 策略下发发送短信       en
    path("user_strategy/apply", strategy_view.UserStrategyApplyView.as_view()),          # 用户自动控制策略：自定义策略下发    en
    path("user_strategy/apply/default", strategy_view.UserStrategyDefaultApplyView.as_view()),        # 用户自动控制策略：默认策略下发     en
    path("user_strategy/apply/realtime", strategy_view.UserStrategyRealtimeApplyView.as_view()),        # 用户自动控制策略：实时策略下发     en
    path("project/list", strategy_view.UserProjectListView.as_view()),        # 用户自动控制策略：用户关联项目列表       no need
    path("user_strategy/apply/history", strategy_view.UserStrategyApplyHistoryView.as_view()),       # 用户自动控制策略：策略下发历史     en
    path("user_strategy/apply/history/import", strategy_view.UserStrategyApplyHistoryView.as_view()),       # 用户自动控制策略：策略下发历史导出        en
    path("user_strategy/save/<int:pk>", strategy_view.UserStrategySaveToOtherView.as_view()),           # 用户自动控制策略：另存为      en
    path("user_strategy/compare", strategy_view.CompareStationStrategyView.as_view()),     # 用户自动控制策略：策略比较     en
    path("user_strategy/template", strategy_view.UserStrategyTemplateView.as_view()),     # 用户自动控制策略：导入策略模板地址      no need
    path("user_strategy/import", strategy_view.UserStrategyImportView.as_view()),       # 用户自动控制策略：解析策略模板        en   未使用

    # web 2.0 负荷预测
    path("power_load/forecasting", power_load_forecasting_views.PowerLoadForecastingView.as_view()),   # 负荷预测       en
    path("power_load_model/list", power_load_forecasting_views.PowerLoadForecastingModelView.as_view()),   # 负荷预测模型列表&切换    en
    path("power_model_rate/list", power_load_forecasting_views.ForecastingModelRateView.as_view()),   # 模型准确率下载     en
    path("power_model_rate_view/list", power_load_forecasting_views.ForecastingAllModelRateView.as_view()),   # 模型准确率查看     en
    path("power_model_target/list", power_load_forecasting_views.ModelTargetView.as_view()),   # 模型指标       en
    path("recommend_forecast/creat", power_load_forecasting_views.CreatStrategyView.as_view()),   # 生成推荐策略      en
    path("power_target_model/list", power_load_forecasting_views.TargetModelView.as_view()),   # 根据指标获取模型       en
    path("station_by_projects/list", power_load_forecasting_views.StationListByBatchView.as_view()),   # 项目多选并网点列表      en
    path("strategy/upload", StrategyUploadViews.as_view()),  # 策略上传
    path("strategy/list", StrategyListViews.as_view()),  # 策略列表
    path("strategy/modle_download", StrategyDownloadViews.as_view()),  # 策略模板下载
    path("strategy/infos", StrategyInfoViews.as_view()),  # 策略详情
    path("strategy/update", StrategyUpdateViews.as_view()),  # 策略更新
    path("strategy/del", StrategyDelViews.as_view()),  # 策略删除
    path("price/upload", PriceUploadViews.as_view()),  # 电价上传
    path("price/modle_download", PriceDownloadViews.as_view()),  # 电价上传
    path("price/list", PriceListViews.as_view()),  # 电价列表
    path("price/infos", PriceInfoViews.as_view()),  # 电价列表
    path("price/del", PriceDelViews.as_view()),  # 电价列表
    path("stations", StaitonViews.as_view()),  # 用户并网点列表

    path('base_income_create', BaseIncomeView().as_view()),  # 补充基准收益
    path('merge_strategy', MergeStrategy().as_view()),  # 补充电价、基准、默认合并info

]
