#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-09-21 16:39:09
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_useele_ele_file.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 16:55:36


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseUseEleEleFile(user_Base):
    u'用电信息表--电费信息'
    __tablename__ = "t_side_forecase_useele_ele_file"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    file_name = Column(String(256), nullable=True, comment=u"文件名称")
    file_path = Column(String(256), nullable=False, comment=u"文件路径加自定义命名")
    ele_id = Column(Integer, ForeignKey("t_side_forecase_ele.id"),nullable=True, comment=u"用电分类")
    ele_name = Column(String(256), nullable=True, comment=u"用电分类名称")
    vol_id = Column(Integer, ForeignKey("t_side_forecase_vol.id"),nullable=True, comment=u"电压等级")
    vol_name = Column(String(256), nullable=True, comment=u"电压等级名称")
    sell_ele = Column(String(256), nullable=True, comment=u"购售电信息")
    demand_f = Column(String(256), nullable=True, comment=u"1按荣收费2按需收费",server_default='1')

    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    project_ele_file = relationship("ForecaseProject", backref="project_ele_file")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean =  "{'id':%s,'file_name':'%s','file_path':'%s','ele_id':'%s','ele_name':'%s','vol_id':'%s','vol_name':'%s','sell_ele':'%s','demand_f':'%s','op_ts':'%s'}" %(
            self.id,self.file_name,self.file_path,self.ele_id,self.ele_name,self.vol_id,self.vol_name,self.sell_ele,self.demand_f,self.op_ts)
        return bean.replace('None',"")

   