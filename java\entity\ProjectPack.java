package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目包表
 * 对应Python模型: ProjectPack
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_project_pack")
public class ProjectPack extends SuperEntity {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 项目包名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目包数据(JSON格式)
     */
    @TableField("data")
    private String data;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
