#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql



DATONG_HOSTNAME = model_config.get('mysql', "DATONG_HOSTNAME")
DATONG_HOSTNAME_SDA = model_config.get('mysql', "DATONG_HOSTNAME_SDA")
DATONG_PORT = model_config.get('mysql', "DATONG_PORT")
DATONG_PORT_SDA = model_config.get('mysql', "DATONG_PORT_SDA")
SDATONG1_DATABASE = model_config.get('mysql', "SDATONG1_DATABASE")
SDATONG2_DATABASE = model_config.get('mysql', "SDATONG2_DATABASE")
SDATONG3_DATABASE = model_config.get('mysql', "SDATONG3_DATABASE")
SDATONG4_DATABASE = model_config.get('mysql', "SDATONG4_DATABASE")

DATONG1_DATABASE = model_config.get('mysql', "DATONG1_DATABASE")
DATONG2_DATABASE = model_config.get('mysql', "DATONG2_DATABASE")
DATONG3_DATABASE = model_config.get('mysql', "DATONG3_DATABASE")
DATONG4_DATABASE = model_config.get('mysql', "DATONG4_DATABASE")

DATONG_USERNAME = model_config.get('mysql', "DATONG_USERNAME")
DATONG_USERNAME_SDA = model_config.get('mysql', "DATONG_USERNAME_SDA")
DATONG_PASSWORD = model_config.get('mysql', "DATONG_PASSWORD")
DATONG_PASSWORD_SDA = model_config.get('mysql', "DATONG_PASSWORD_SDA")


shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME_SDA,
    DATONG_PASSWORD_SDA,
    DATONG_HOSTNAME_SDA,
    DATONG_PORT_SDA,
    SDATONG1_DATABASE
)
sdatong1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sdatong1_session = scoped_session(sessionmaker(sdatong1_engine,autoflush=True))
sdatong1_Base = declarative_base(sdatong1_engine)
sdatong1_session = _sdatong1_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME_SDA,
    DATONG_PASSWORD_SDA,
    DATONG_HOSTNAME_SDA,
    DATONG_PORT_SDA,
    SDATONG2_DATABASE
)
sdatong2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sdatong2_session = scoped_session(sessionmaker(sdatong2_engine,autoflush=True))
sdatong2_Base = declarative_base(sdatong2_engine)
sdatong2_session = _sdatong2_session()



shisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME_SDA,
    DATONG_PASSWORD_SDA,
    DATONG_HOSTNAME_SDA,
    DATONG_PORT_SDA,
    SDATONG3_DATABASE
)
sdatong3_engine = create_engine(shisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sdatong3_session = scoped_session(sessionmaker(sdatong3_engine,autoflush=True))
sdatong3_Base = declarative_base(sdatong3_engine)
sdatong3_session = _sdatong3_session()


shisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME_SDA,
    DATONG_PASSWORD_SDA,
    DATONG_HOSTNAME_SDA,
    DATONG_PORT_SDA,
    SDATONG4_DATABASE
)
sdatong4_engine = create_engine(shisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sdatong4_session = scoped_session(sessionmaker(sdatong4_engine,autoflush=True))
sdatong4_Base = declarative_base(sdatong4_engine)
sdatong4_session = _sdatong4_session()




hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG1_DATABASE
)
datong1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong1_session = scoped_session(sessionmaker(datong1_engine,autoflush=True))
datong1_Base = declarative_base(datong1_engine)
datong1_session = _datong1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG2_DATABASE
)
datong2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong2_session = scoped_session(sessionmaker(datong2_engine,autoflush=True))
datong2_Base = declarative_base(datong2_engine)
datong2_session = _datong2_session()



hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG3_DATABASE
)
datong3_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong3_session = scoped_session(sessionmaker(datong3_engine,autoflush=True))
datong3_Base = declarative_base(datong3_engine)
datong3_session = _datong3_session()


hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    DATONG_USERNAME,
    DATONG_PASSWORD,
    DATONG_HOSTNAME,
    DATONG_PORT,
    DATONG4_DATABASE
)
datong4_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_datong4_session = scoped_session(sessionmaker(datong4_engine,autoflush=True))
datong4_Base = declarative_base(datong4_engine)
datong4_session = _datong4_session()














# MZGTIAN_HOSTNAME = model_config.get('mysql', "MZGTIAN_HOSTNAME")
# MZGTIAN_PORT = model_config.get('mysql', "MZGTIAN_PORT")
# MZGTIAN_DATABASE = model_config.get('mysql', "MZGTIAN_DATABASE")
# MZGTIAN_USERNAME = model_config.get('mysql', "MZGTIAN_USERNAME")
# MZGTIAN_PASSWORD = model_config.get('mysql', "MZGTIAN_PASSWORD")
#
#
#
#
# mhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
#     MZGTIAN_USERNAME,
#     MZGTIAN_PASSWORD,
#     MZGTIAN_HOSTNAME,
#     MZGTIAN_PORT,
#     MZGTIAN_DATABASE
# )
# mzgtian_engine = create_engine(mhisdb1_mysql_url,
#                        echo=False,
#                        max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
# _mzgtian_session = sessionmaker(mzgtian_engine,autoflush=True)
# mzgtian_Base = declarative_base(mzgtian_engine)
# mzgtian_session = _mzgtian_session()
#
#
# SMZGTIAN_HOSTNAME = model_config.get('mysql', "SMZGTIAN_HOSTNAME")
# SMZGTIAN_PORT = model_config.get('mysql', "SMZGTIAN_PORT")
# SMZGTIAN_DATABASE = model_config.get('mysql', "SMZGTIAN_DATABASE")
# SMZGTIAN_USERNAME = model_config.get('mysql', "SMZGTIAN_USERNAME")
# SMZGTIAN_PASSWORD = model_config.get('mysql', "SMZGTIAN_PASSWORD")
#
#
#
#
# smhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
#     SMZGTIAN_USERNAME,
#     SMZGTIAN_PASSWORD,
#     SMZGTIAN_HOSTNAME,
#     SMZGTIAN_PORT,
#     SMZGTIAN_DATABASE
# )
# smzgtian_engine = create_engine(smhisdb1_mysql_url,
#                        echo=False,
#                        max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
# _smzgtian_session = sessionmaker(smzgtian_engine,autoflush=True)
# smzgtian_Base = declarative_base(smzgtian_engine)
# smzgtian_session = _smzgtian_session()

