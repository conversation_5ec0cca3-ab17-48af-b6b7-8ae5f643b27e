#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class DataItem(user_Base):
    u'历史数据下载数据项'
    __tablename__ = "t_data_item"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"数据项")
    ty = Column(VARCHAR(100), nullable=False, comment=u"类型")
    table_ = Column(VARCHAR(50), nullable=False, comment=u"所属表")
    frequently = Column(VARCHAR(50), nullable=False, comment=u"常用数据项1")
    station = Column(VARCHAR(50), nullable=False, comment=u"所属站")
    is_use = Column(CHAR(2), nullable=False,server_default='1',comment=u"是否使用1是0否")
    name_la = Column(VARCHAR(256), nullable=False, comment=u"数据项点表")

    en_name = Column(VARCHAR(256), nullable=False, comment=u"数据项")
    en_ty = Column(VARCHAR(100), nullable=False, comment=u"类型")

    
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        bean =  "{'id':%s,'name':'%s','ty':'%s','table_':'%s','frequently':'%s','station':'%s','is_use':'%s','name_la':'%s','en_name':'%s','en_ty':'%s'}" % (
            self.id,self.name,self.ty,self.table_,self.frequently,self.station,self.is_use,self.name_la,self.en_name,self.en_ty)
        return bean.replace("None", '')
