from rest_framework import exceptions
from rest_framework.authentication import BaseAuthentication
from encryption.jwt_encryption import parse_payload
from common import common_response_code


class JWTHeaderAuthentication(BaseAuthentication):
    """请求头 jwt 校验"""

    def authenticate(self, request):
        authentication = request.META.get('HTTP_AUTHORIZATION')
        status, info_or_error = parse_payload(authentication)
        if not status:
            return
        return info_or_error, authentication

    def authenticate_header(self, request):
        return 'API realm="API"'


class JwtParamAuthentication(BaseAuthentication):
    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        # 1.读取请求头中的token
        authorization = request.query_params.get("token")

        # 2.token校验
        status, info_or_error = parse_payload(authorization)

        # 3.校验失败，继续往后走
        if not status:
            return

        # 4.校验成功，继续向后  request.user  request.auth
        return (info_or_error, authorization)

    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        return 'API realm="API"'


class DenyAuthentication(BaseAuthentication):
    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        raise exceptions.AuthenticationFailed({
            'code': common_response_code.AUTHENTICATION_FIELD,
            "data": {
                "message": "error",
                "detail": "认证失败",
            }})

    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        return 'API realm="API"'


# class MonitorJwtParamAuthentication(BaseAuthentication):
#     """监控页面请求头验证"""
#     def authenticate(self, request):
#         authentication = request.META.get('HTTP_MONITOR_AUTHORIZATION')
#         status, info_or_error = parse_payload(authentication)
#         if not status:
#             return
#         return info_or_error, authentication
#
#     def authenticate_header(self, request):
#         return 'API realm="API"'
