#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/6/13 上午10:42
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

from Tools.DB.mysql_user import user_Base, user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, \
    VARCHAR


class DeviceType(user_Base):
    u'设备类型表'
    __tablename__ = "t_device_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    is_use = Column(Integer, nullable=True, server_default='1', comment=u"是否使用1是  0否")
    op_ts = Column(DateTime, nullable=True, comment=u"录入时间")
    name = Column(VARCHAR(255), nullable=False, comment=u"类型名称")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'is_use':'%s','op_ts':'%s','name':'%s'}" % (self.id, self.is_use, self.op_ts, self.name)
