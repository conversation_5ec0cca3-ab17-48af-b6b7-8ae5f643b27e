#!/usr/bin/env python
# coding=utf-8
#储能电站项目信息配置功能

from Application.Models.User.device_del_table_t import DeviceDelTable
from Application.Models.User.device_library_t import DeviceLibrary
from Application.Models.User.equipmen_t import Equipment
from Application.Models.User.remote_control import RemoteControl
from Application.Models.User.remote_letter_t import RemoteLetter
from Application.Models.User.remote_modula import RemoteModula
from Application.Models.User.remote_tele_t import RemoteTele
from Application.Models.User.user import User
from sqlalchemy import  or_
from Tools.DB.mysql_scada import DEBUG
from Tools.DB.mysql_user import user_session
from Application.HistoryData.his_bams import *
from Application.Models.User.ele_price_t import ElePrice
from Application.Models.User.project_info_t import ProjectInfo
from Application.Models.User.province_c import Province
from Application.Models.User.city_c import City
import uuid

class ProjectIntetface(BaseHandler):
    ''' 项目信息配置功能汇总 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
            data = []
            if kt == 'GetVoltClass':#电压等级
                d = [{'id':'380V',"name":'380V'},{'id':'10kV',"name":'10kV'},{'id':'35kV',"name":'35kV'},{'id':'110kV',"name":'110kV'},{'id':'220kV',"name":'220kV'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetCommMode':#通讯方式
                d = [{'id':1,"name":'数据直采'},{'id':2,"name":'数据转发'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetProjectList': # 所有配置页面
                descr = self.get_argument('descr',None) #项目名称
                energy_storage = self.get_argument('energy_storage',None) #项目类型
                status = self.get_argument('status',None) #项目状态
                head_main = self.get_argument('head_main',None) #负责人
                own_name = self.get_argument('own_name',None) #业主
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                filter = [ProjectInfo.is_use == '1']
                if DEBUG:
                    logging.info('descr:%s,energy_storage:%s,status:%s,head_main:%s,own_name:%s,pageNum:%s,pageSize:%s'%(descr,energy_storage,status,head_main,own_name,pageNum,pageSize))
                if descr:
                    filter.append(ProjectInfo.descr.like('%' + descr + '%'))
                if energy_storage:
                    filter.append(ProjectInfo.energy_storage==energy_storage)
                if head_main:
                    filter.append(ProjectInfo.head_main == head_main)
                if own_name:
                    filter.append(ProjectInfo.own_name.like('%' + own_name + '%'))

                total_all = user_session.query(func.count(ProjectInfo.id)).filter(*filter).scalar()
                pages = user_session.query(ProjectInfo.id,ProjectInfo.descr,ProjectInfo.energy_storage,ProjectInfo.electric_power,ProjectInfo.volume,ProjectInfo.start_ts,ProjectInfo.head_main,ProjectInfo.own_name,ProjectInfo.data_acc_date,ProjectInfo.name)\
                    .filter(*filter).order_by(ProjectInfo.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                now_time = timeUtils.getNewDayStartStr()
                total = 0
                for pag in pages:
                    obj = {}
                    if pag[8] <= now_time:
                        pag = list(eval(str(pag)))
                        obj['state'] = '已接入'
                    else:
                        obj['state'] = '接入中'
                    obj['id'] = pag[0]
                    obj['descr'] = pag[1]
                    obj['energy_storage'] = pag[2]
                    obj['electric_power'] = pag[3]
                    obj['volume'] = pag[4]
                    obj['start_ts'] = pag[5]
                    obj['head_main'] = pag[6]
                    obj['own_name'] = pag[7]
                    obj['data_acc_date'] = pag[8]
                    obj['name'] = pag[9]
                    if status:
                        if  status == obj['state']:
                            total+=1
                            data.append(obj)
                    else:
                        data.append(obj)
                return self.returnTotalSuc(data,total if total else total_all)
            elif kt == 'GetProjectInfoList': # 获取项目详情
                id = self.get_argument('id', None)  # 项目id
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id ==id,DeviceLibrary.is_use == 1).first()
                page_ = user_session.query(ElePrice).filter(ElePrice.station == page.name,ElePrice.is_use == 1,ElePrice.years==datestart).order_by(ElePrice.id.asc()).all()
                if not id :
                    return self.customError('参数不完整')
                page1=eval(str(page))
                list1 = []
                aaa = {}
                bbb = {}
                if page_:
                    for p in page_:
                        if p.name in aaa.keys():
                            aaa[p.name].append({"start_time":p.start_time,"end_time":p.end_time,"descr": p.descr,"rate": p.rate})
                            bbb[p.name] = p.month
                        else:
                            aaa[p.name] = []
                            aaa[p.name].append({"start_time": p.start_time, "end_time": p.end_time, "descr": p.descr, "rate": p.rate})
                            bbb[p.name] = p.month

                if aaa:
                    for key in aaa.keys():
                        obj = {'name': '', 'months': '', 'time_': []}
                        obj['name'] = key
                        list_ = eval(bbb[key])
                        obj['months'] = list_
                        obj['time_'] = aaa[key]
                        list1.append(obj)
                page1['list1'] = list1

                return self.returnTypeSuc(page1)
            elif kt == 'GetProvinceList': # 所有省份
                data = ProvinceCity.ProvinceList()
                return self.returnTypeSuc(data)
            elif kt == 'GetOrganization': # 所有业主
                pages = user_session.query(User.id,User.name).filter(User.organization_id== 31).order_by(User.id.desc()).all()
                if pages:
                    for pag in pages:
                        obj = {}
                        obj['id'] = pag[0]
                        obj['name'] = pag[1]
                        data.append(obj)

                return self.returnTypeSuc(data)
            elif kt == 'GetCityList': # 所有省份下的市
                id = self.get_argument('id', None)  # 省id
                if not id :
                    return self.customError('参数不完整')
                data = ProvinceCity.CityList(id)
                return self.returnTypeSuc(data)
            elif kt == 'GetProjectDeviceList': # 获取项目下的设备详情
                id = self.get_argument('id', None)  # 项目id
                list1 = []
                obj = {}
                page1 = user_session.query(Equipment).filter(Equipment.parent_id ==id,Equipment.is_use =='1').all()#查询单元
                if page1:
                    for i in page1:
                        obj1 = {'PCS':[]}
                        if 'project_id' not in obj:
                            obj['project_id'] = id
                            obj['descr'] = i.descr
                            obj['name'] = i.station
                            data.append(obj)
                        obj1['unit'] = i.name
                        obj1['unit_id'] = i.id
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id,Equipment.is_use =='1').all()#查询PCS
                        if page2:
                            for ii in page2:
                                obj2 = {'dui':[]}
                                obj2['pcs_num'] = ii.name
                                obj2['pcs_num_id'] = ii.id
                                obj2['pcs_name'] = ii.pcs_name
                                obj2['pcs_ty_num'] = ii.pcs_ty_num
                                obj2['pcs_s_name'] = ii.pcs_s_name
                                obj2['act_acc_ene'] = ii.act_acc_ene
                                obj1['PCS'].append(obj2)
                                page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id,Equipment.is_use =='1').all()#查询堆
                                if page3:
                                    for iii in page3:
                                        page4 = user_session.query(Equipment).filter(Equipment.parent_id == iii.id,Equipment.is_use =='1').all()#查询簇
                                        if page4:
                                            for iiii in page4:
                                                obj3 = {'cu': []}
                                                obj3['dui_num'] = iii.name
                                                obj3['dui_num_id'] = iii.id
                                                obj2['dui'].append(obj3)
                                                obj4 = {'cell': []}
                                                obj4['cu_num'] = iiii.name
                                                obj4['cu_num_id'] = iiii.id
                                                obj4['cool_mode'] = iiii.cool_mode
                                                obj4['cell_type'] = iiii.cell_type
                                                obj4['cell_num'] = iiii.cell_num
                                                obj3['cu'].append(obj4)
                                                page4 = user_session.query(Equipment).filter(Equipment.parent_id == iiii.id,Equipment.is_use =='1').all()  # 查询电芯
                                                if page4:
                                                    for p in page4:
                                                        obj5 = {}
                                                        obj5['cell_ty_num'] = p.name
                                                        obj5['cell_ty_num_id'] = p.id
                                                        obj5['cell_s_ene'] = p.cell_s_ene
                                                        obj5['total_ene'] = p.total_ene
                                                        obj5['cell_s_na'] = p.cell_s_na
                                                        obj5['bms_num'] = p.bms_num
                                                        obj4['cell'].append(obj5)
                        list1.append(obj1)
                        obj['list1']=list1
                return self.returnTypeSuc(data)
            elif kt == 'GetPCSList': # 所有PCS型号
                pages = user_session.query(DeviceLibrary.device_mo,DeviceLibrary.id).filter(DeviceLibrary.device_ty=='PCS',DeviceLibrary.is_use == 1).order_by(DeviceLibrary.id.desc()).all()
                for pag in pages:
                    obj = {}
                    obj['name'] = pag[0]
                    obj['id'] = pag[1]
                    data.append(obj)
                return self.returnTypeSuc(data)
            elif kt == 'GetCellList': # 所有电芯型号
                pages = user_session.query(DeviceLibrary.device_mo,DeviceLibrary.id).filter(DeviceLibrary.device_ty=='电芯',DeviceLibrary.is_use == 1).order_by(DeviceLibrary.id.desc()).all()
                for pag in pages:
                    obj = {}
                    obj['name'] = pag[0]
                    obj['id'] = pag[1]
                    data.append(obj)
                return self.returnTypeSuc(data)

            elif kt == 'GetMeaList': # 所有测点表
                id = self.get_argument('id',None) #单元id
                name = self.get_argument('name',None) #设备名称
                num = self.get_argument('num',None) #编号
                mea_point_n = self.get_argument('mea_point_n',None) #测点名称
                type_ = self.get_argument('type_','1') #类型,1遥测2遥信3遥调4遥控
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not id or not type_:
                    return self.customError('参数不完整')
                filter1 = [RemoteTele.is_use == 1,RemoteTele.t_eq_id == id]
                filter2 = [RemoteLetter.is_use == 1,RemoteLetter.t_eq_id == id]
                filter3 = [RemoteModula.is_use == 1,RemoteModula.t_eq_id == id]
                filter4 = [RemoteControl.is_use == 1,RemoteControl.t_eq_id == id]

                if DEBUG:
                    logging.info('name:%s,num:%s,mea_point_n:%s,type_:%s'%(name,num,mea_point_n,type_))
                if name:
                    filter1.append(RemoteTele.name.like('%' + name + '%'))
                    filter2.append(RemoteLetter.name.like('%' + name + '%'))
                    filter3.append(RemoteModula.name.like('%' + name + '%'))
                    filter4.append(RemoteControl.name.like('%' + name + '%'))
                if num:
                   filter1.append(RemoteTele.num.like('%' + num + '%'))
                   filter2.append(RemoteLetter.num.like('%' + num + '%'))
                   filter3.append(RemoteModula.num.like('%' + num + '%'))
                   filter4.append(RemoteControl.num.like('%' + num + '%'))
                if mea_point_n:
                    filter1.append(RemoteTele.mea_point_n.like('%' + mea_point_n + '%'))
                    filter2.append(RemoteLetter.mea_point_n.like('%' + mea_point_n + '%'))
                    filter3.append(RemoteModula.mea_point_n.like('%' + mea_point_n + '%'))
                    filter4.append(RemoteControl.mea_point_n.like('%' + mea_point_n + '%'))
                total = 0
                if type_ == '1':
                    total = user_session.query(func.count(RemoteTele.id)).filter(*filter1).scalar()
                    pages = user_session.query(RemoteTele.id,RemoteTele.name,RemoteTele.num,RemoteTele.mea_point_n,RemoteTele.type_,RemoteTele.remarks).filter(*filter1).order_by(RemoteTele.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages)
                elif type_ == '2':
                    total = user_session.query(func.count(RemoteLetter.id)).filter(*filter2).scalar()
                    pages = user_session.query(RemoteLetter.id,RemoteLetter.name,RemoteLetter.num,RemoteLetter.mea_point_n,RemoteLetter.type_,RemoteLetter.remarks).filter(*filter2).order_by(RemoteLetter.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages)
                elif type_ == '3':
                    total = user_session.query(func.count(RemoteModula.id)).filter(*filter3).scalar()
                    pages = user_session.query(RemoteModula.id,RemoteModula.name,RemoteModula.num,RemoteModula.mea_point_n,RemoteModula.type_,RemoteModula.remarks).filter(*filter3).order_by(RemoteModula.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages)
                elif type_ == '4':
                    total = user_session.query(func.count(RemoteControl.id)).filter(*filter4).scalar()
                    pages = user_session.query(RemoteControl.id,RemoteControl.name,RemoteControl.num,RemoteControl.mea_point_n,RemoteControl.type_,RemoteControl.remarks).filter(*filter4).order_by(RemoteControl.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                    self.MeaReturns(data, pages)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetUnitList': # 项目单元下设备名称
                id = self.get_argument('id', None)  # 单元id
                if not id :
                    return self.customError('参数不完整')
                pages1 = user_session.query(Equipment.name,Equipment.id).filter(Equipment.parent_id==id).order_by(Equipment.id.desc()).all()
                if pages1:
                    for pag1 in pages1:
                        obj = {}
                        obj['name'] = pag1[0]
                        obj['id'] = pag1[1]
                        data.append(obj)
                        pages2 = user_session.query(Equipment.name, Equipment.id).filter(Equipment.parent_id == pag1.id).order_by(Equipment.id.desc()).all()
                        if pages2:
                            for pag2 in pages2:
                                obj2 = {}
                                obj2['name'] = pag2[0]
                                obj2['id'] = pag2[1]
                                data.append(obj2)
                                pages3 = user_session.query(Equipment.name, Equipment.id).filter(Equipment.parent_id == pag2.id).order_by(Equipment.id.desc()).all()
                                if pages3:
                                    for pag3 in pages3:
                                        obj3 = {}
                                        obj3['name'] = pag3[0]
                                        obj3['id'] = pag3[1]
                                        data.append(obj3)
                return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    def MeaReturns(self, data, pages):
        #返回测点表
        for pag in pages:
            obj = {}
            obj['id'] = pag[0]
            obj['name'] = pag[1]
            obj['num'] = pag[2]
            obj['mea_point_n'] = pag[3]
            obj['type_'] = pag[4]
            obj['remarks'] = pag[5]
            data.append(obj)

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'ProjectAdd':  # 添加电站项目
                descr = self.get_argument('descr',None)# "项目名称"
                com_name = self.get_argument('com_name',None)# "项目公司名称"
                name = self.get_argument('name',None)# "英文名称"
                short_name = self.get_argument('short_name',None)# "项目简称"
                energy_storage = self.get_argument('energy_storage',None)# "储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能"
                electric_power = self.get_argument('electric_power',None)# "额定功率"
                volume = self.get_argument('volume',None)# "电站容量"
                start_ts = self.get_argument('start_ts',None)# "投运日期：YYYY-mm-dd"
                volt_class = self.get_argument('volt_class',None)# "电压等级"
                country = self.get_argument('country',None)# "国家"
                province = self.get_argument('province',None)# "电站所在省份"
                city = self.get_argument('city',None)# "市"
                address = self.get_argument('address',None)# "地址"
                remarks = self.get_argument('remarks',None)# "基础信息备注"
                own_name = self.get_argument('own_name',None)# "业主名称"
                con_per = self.get_argument('con_per',None)# "运维合同期限"
                tar_inc_y = self.get_argument('tar_inc_y',None)# "年度运营目标收益"
                ser_pers = self.get_argument('ser_pers',None)# "服务人员"
                phone = self.get_argument('phone',None)# "联系电话"
                met_read_c = self.get_argument('met_read_c',None)# "抄表周期"
                met_read_d = self.get_argument('met_read_d',None)# "抄表日"
                y_fm_re = self.get_argument('y_fm_re',None)# "年度一次调频收益"
                y_agc_re = self.get_argument('y_agc_re',None)# "年度AGC收益"
                y_spot_re = self.get_argument('y_spot_re',None)# "年度现货收益"
                y_f_e_s_re = self.get_argument('y_f_e_s_re',None)# "年度火储能辅助服务收益"
                y_p_c_g_f_re = self.get_argument('y_p_c_g_f_re',None)# "年度削峰填谷收益"
                y_d_r_re = self.get_argument('y_d_r_re',None)# "年度需求侧响应收益"
                y_oth_re = self.get_argument('y_oth_re',None)# "年度其他收益"
                tar_ava_y = self.get_argument('tar_ava_y',None)# "目标年度系统可用率"
                tar_li_ch_ef = self.get_argument('tar_li_ch_ef',None)# "目标集电线路充电效率"
                tar_li_di_ef = self.get_argument('tar_li_di_ef',None)# "目标集电线路放电效率"
                tar_vo_ch_ef = self.get_argument('tar_vo_ch_ef',None)# "目标升压站充电效率"
                tar_vo_di_ef = self.get_argument('tar_vo_di_ef',None)# "目标升压站放电效率"
                tar_pcs_ch_ef = self.get_argument('tar_pcs_ch_ef',None)# "目标PCS充电效率"
                tar_pcs_di_ef = self.get_argument('tar_pcs_di_ef',None)# "目标PCS放电效率"
                tar_dc_ch_ef = self.get_argument('tar_dc_ch_ef',None)# "目标直流侧充电效率"
                tar_dc_di_ef = self.get_argument('tar_dc_di_ef',None)# "目标直流侧放电效率"
                head_main = self.get_argument('head_main',None)# "运维负责人"
                dev_ops = self.get_argument('dev_ops',None)# "运维人员"
                other_dev_ops = self.get_argument('other_dev_ops',None)# "其他运维人员"
                data_acc_date = self.get_argument('data_acc_date',None)# "数据接入日期"
                data_int = self.get_argument('data_int',None)# "数据间隔"
                commu_mode = self.get_argument('commu_mode',None)# "通讯方式"
                iot_card_num = self.get_argument('iot_card_num',None)# "物联卡编号"

                list1 = self.get_argument('list1', [])  # "电价list"

                remarks = ';'.join(remarks.split()) if remarks else ''
                other_dev_ops = ';'.join(other_dev_ops.split()) if other_dev_ops else ''

                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]

                if DEBUG:
                    logging.info('descr:%s,name:%s,short_name:%s,energy_storage:%s,electric_power:%s,volume:%s,start_ts:%s,province:%s,city:%s,address:%s,own_name:%s,con_per:%s,tar_inc_y:%s,head_main:%s,data_acc_date:%s'
                                 %(descr,name,short_name,energy_storage,electric_power,volume,start_ts,province,city,address,own_name,con_per,tar_inc_y,head_main,data_acc_date))
                if not descr or not name or not short_name or not energy_storage or not electric_power or not volume or not start_ts or not province or not city  or not own_name or not con_per or not tar_inc_y or not head_main or not data_acc_date :
                    return self.customError("请携带完整参数")

                page = user_session.query(ProjectInfo).filter(or_(ProjectInfo.descr==descr,ProjectInfo.name==name),ProjectInfo.is_use == '1').first()
                if page:
                    return self.customError("项目名称重复，请重新录入！")


                p = ProjectInfo(descr=descr,com_name=com_name,name=name,short_name=short_name,energy_storage=energy_storage,electric_power=electric_power,volume=volume,
                            start_ts=start_ts,volt_class=volt_class,country=country,province=province,city=city,address=address,
                            remarks=remarks,own_name=own_name,con_per=con_per,tar_inc_y=tar_inc_y,ser_pers=ser_pers,phone=phone,
                            met_read_c=met_read_c,met_read_d=met_read_d,y_fm_re=y_fm_re,y_agc_re=y_agc_re,y_spot_re=y_spot_re,y_f_e_s_re=y_f_e_s_re,
                            y_p_c_g_f_re=y_p_c_g_f_re,y_d_r_re=y_d_r_re,y_oth_re=y_oth_re,tar_ava_y=tar_ava_y,tar_li_ch_ef=tar_li_ch_ef,tar_li_di_ef=tar_li_di_ef,
                            tar_vo_ch_ef=tar_vo_ch_ef,tar_vo_di_ef=tar_vo_di_ef,tar_pcs_ch_ef=tar_pcs_ch_ef,tar_pcs_di_ef=tar_pcs_di_ef,tar_dc_ch_ef=tar_dc_ch_ef,tar_dc_di_ef=tar_dc_di_ef,head_main=head_main,dev_ops=dev_ops,other_dev_ops=other_dev_ops,data_acc_date=data_acc_date,
                            data_int=data_int,commu_mode=commu_mode,iot_card_num=iot_card_num)
                if list1:
                    list1 = json.loads(list1)
                    list_2 = [1,2,3,4,5,6,7,8,9,10,11,12]
                    for l in list1:
                        name_ = l['name']
                        month = l['months']
                        page_ = user_session.query(ElePrice).filter(ElePrice.name == name_, ElePrice.station == name,
                                                                    ElePrice.is_use == '1',
                                                                    ElePrice.years == datestart).first()
                        if page_:
                            return self.customError("数据已存在2")
                        a = int(month[0])
                        b = int(month[1])
                        months =0
                        datestart_ = 0
                        if a > b:#跨年
                            datestart_ = int(datestart) +1
                            months = ",".join(str(e) for e in list_2[a-1:]) #当年的时间
                            months_ = ",".join(str(e) for e in list_2[:b]) #第二年的时间
                        elif a == b:
                            months = a
                        else:
                            months = ",".join(str(e) for e in list_2[a-1:b])
                        if l['time_']:
                            for t in l['time_']:
                                start_time = t['start_time']
                                end_time = t['end_time']
                                descr = t['descr']
                                rate = t['rate']
                                p_ = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,rate=rate, months=months,month=str(month),years=datestart, station=name, op_ts=now_time)
                                user_session.add(p_)
                                if datestart_:
                                    p_1 = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months_, month=str(month), years=datestart_,
                                                  station=name, op_ts=now_time)
                                    user_session.add(p_1)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ProjectUpdate':  # 修改页面
                id = self.get_argument('id',None)#项目表id
                descr = self.get_argument('descr', None)  # "项目名称"
                com_name = self.get_argument('com_name', None)  # "项目公司名称"
                name = self.get_argument('name', None)  # "英文名称"
                short_name = self.get_argument('short_name', None)  # "项目简称"
                energy_storage = self.get_argument('energy_storage', None)  # "储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能"
                electric_power = self.get_argument('electric_power', None)  # "额定功率"
                volume = self.get_argument('volume', None)  # "电站容量"
                start_ts = self.get_argument('start_ts', None)  # "投运日期：YYYY-mm-dd"
                volt_class = self.get_argument('volt_class', None)  # "电压等级"
                country = self.get_argument('country', None)  # "国家"
                province = self.get_argument('province', None)  # "电站所在省份"
                city = self.get_argument('city', None)  # "市"
                address = self.get_argument('address', None)  # "地址"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                own_name = self.get_argument('own_name', None)  # "业主名称"
                con_per = self.get_argument('con_per', None)  # "运维合同期限"
                tar_inc_y = self.get_argument('tar_inc_y', None)  # "年度运营目标收益"
                ser_pers = self.get_argument('ser_pers', None)  # "服务人员"
                phone = self.get_argument('phone', None)  # "联系电话"
                met_read_c = self.get_argument('met_read_c', None)  # "抄表周期"
                met_read_d = self.get_argument('met_read_d', None)  # "抄表日"
                y_fm_re = self.get_argument('y_fm_re', None)  # "年度一次调频收益"
                y_agc_re = self.get_argument('y_agc_re', None)  # "年度AGC收益"
                y_spot_re = self.get_argument('y_spot_re', None)  # "年度现货收益"
                y_f_e_s_re = self.get_argument('y_f_e_s_re', None)  # "年度火储能辅助服务收益"
                y_p_c_g_f_re = self.get_argument('y_p_c_g_f_re', None)  # "年度削峰填谷收益"
                y_d_r_re = self.get_argument('y_d_r_re', None)  # "年度需求侧响应收益"
                y_oth_re = self.get_argument('y_oth_re', None)  # "年度其他收益"
                tar_ava_y = self.get_argument('tar_ava_y', None)  # "目标年度系统可用率"
                tar_li_ch_ef = self.get_argument('tar_li_ch_ef', None)  # "目标集电线路充电效率"
                tar_li_di_ef = self.get_argument('tar_li_di_ef', None)  # "目标集电线路放电效率"
                tar_vo_ch_ef = self.get_argument('tar_vo_ch_ef', None)  # "目标升压站充电效率"
                tar_vo_di_ef = self.get_argument('tar_vo_di_ef', None)  # "目标升压站放电效率"
                tar_pcs_ch_ef = self.get_argument('tar_pcs_ch_ef', None)  # "目标PCS充电效率"
                tar_pcs_di_ef = self.get_argument('tar_pcs_di_ef', None)  # "目标PCS放电效率"
                tar_dc_ch_ef = self.get_argument('tar_dc_ch_ef', None)  # "目标直流侧充电效率"
                tar_dc_di_ef = self.get_argument('tar_dc_di_ef', None)  # "目标直流侧放电效率"
                head_main = self.get_argument('head_main', None)  # "运维负责人"
                dev_ops = self.get_argument('dev_ops', None)  # "运维人员"
                other_dev_ops = self.get_argument('other_dev_ops', None)  # "其他运维人员"
                data_acc_date = self.get_argument('data_acc_date', None)  # "数据接入日期"
                data_int = self.get_argument('data_int', None)  # "数据间隔"
                commu_mode = self.get_argument('commu_mode', None)  # "通讯方式"
                iot_card_num = self.get_argument('iot_card_num', None)  # "物联卡编号"

                remarks = ';'.join(remarks.split()) if remarks else ''
                other_dev_ops = ';'.join(other_dev_ops.split()) if other_dev_ops else ''

                list1 = self.get_argument('list1', [])  # "电价list"


                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]

                if DEBUG:
                    logging.info(
                        'descr:%s,name:%s,short_name:%s,energy_storage:%s,electric_power:%s,volume:%s,start_ts:%s,province:%s,city:%s,address:%s,own_name:%s,con_per:%s,tar_inc_y:%s,head_main:%s,data_acc_date:%s'
                        % (descr, name, short_name, energy_storage, electric_power, volume, start_ts, province, city,
                           address, own_name, con_per, tar_inc_y, head_main, data_acc_date))
                if not descr or not name or not short_name or not energy_storage or not electric_power or not volume or not start_ts or not province or not city or not own_name or not con_per or not tar_inc_y or not head_main or not data_acc_date:
                    return self.customError("请携带完整参数")
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id==id).first()

                if not page:
                    return self.customError("无效id")
                pa = user_session.query(ProjectInfo).filter(ProjectInfo.descr==descr,ProjectInfo.name==name,ProjectInfo.is_use == '1').first()
                if pa and pa.id != int(id):
                    return self.customError("数据已存在1")

                if descr :
                    page.descr = descr
                if com_name:
                    page.com_name = com_name
                if name :
                    page.name = name
                if short_name:
                    page.short_name = short_name
                if energy_storage :
                    page.energy_storage = energy_storage
                if electric_power :
                    page.electric_power = electric_power
                if volume :
                    page.volume = volume
                if start_ts:
                    page.start_ts = start_ts
                if volt_class :
                    page.volt_class = volt_class
                if country :
                    page.country = country
                if province :
                    page.province = province
                if city :
                    page.city = city
                if address :
                    page.address = address
                if remarks :
                    page.remarks = remarks
                if own_name :
                    page.own_name = own_name
                if con_per :
                    page.con_per = con_per
                if tar_inc_y :
                    page.tar_inc_y = tar_inc_y
                if ser_pers :
                    page.ser_pers = ser_pers
                if phone :
                    page.phone = phone
                if met_read_c:
                    page.met_read_c = met_read_c
                if met_read_d :
                    page.met_read_d = met_read_d
                if y_fm_re :
                    page.y_fm_re = y_fm_re
                if y_agc_re :
                    page.y_agc_re = y_agc_re
                if y_spot_re :
                    page.y_spot_re = y_spot_re
                if y_f_e_s_re:
                    page.y_f_e_s_re = y_f_e_s_re
                if y_p_c_g_f_re :
                    page.y_p_c_g_f_re = y_p_c_g_f_re
                if y_d_r_re :
                    page.y_d_r_re = y_d_r_re
                if y_oth_re :
                    page.y_oth_re = y_oth_re
                if tar_ava_y:
                    page.tar_ava_y = tar_ava_y
                if tar_li_ch_ef :
                    page.tar_li_ch_ef = tar_li_ch_ef
                if tar_li_di_ef :
                    page.tar_li_di_ef = tar_li_di_ef
                if tar_vo_ch_ef :
                    page.tar_vo_ch_ef = tar_vo_ch_ef
                if tar_vo_di_ef :
                    page.tar_vo_di_ef = tar_vo_di_ef
                if tar_pcs_ch_ef:
                    page.tar_pcs_ch_ef = tar_pcs_ch_ef
                if tar_pcs_di_ef :
                    page.tar_pcs_di_ef = tar_pcs_di_ef
                if tar_dc_ch_ef :
                    page.tar_dc_ch_ef = tar_dc_ch_ef
                if tar_dc_di_ef :
                    page.tar_dc_di_ef = tar_dc_di_ef
                if head_main :
                    page.head_main = head_main
                if dev_ops :
                    page.dev_ops = dev_ops
                if other_dev_ops :
                    page.other_dev_ops = other_dev_ops
                if data_acc_date :
                    page.data_acc_date = data_acc_date
                if data_int :
                    page.data_int = data_int
                if commu_mode :
                    page.commu_mode = commu_mode
                if iot_card_num :
                    page.iot_card_num = iot_card_num

                user_session.query(ElePrice).filter(ElePrice.station == page.name).delete()

                if list1:
                    list1 = json.loads(list1)
                    list_2 = [1,2,3,4,5,6,7,8,9,10,11,12]
                    for l in list1:
                        name_ = l['name']
                        month = l['months']
                        page_ = user_session.query(ElePrice).filter(ElePrice.name == name_, ElePrice.station == name,
                                                                    ElePrice.is_use == '1',
                                                                    ElePrice.years == datestart).first()
                        if page_:
                            return self.customError("数据已存在2")
                        a = int(month[0])
                        b = int(month[1])
                        months =0
                        datestart_ = 0
                        if a > b:#跨年
                            datestart_ = int(datestart) +1
                            months = ",".join(str(e) for e in list_2[a-1:]) #当年的时间
                            months_ = ",".join(str(e) for e in list_2[:b]) #第二年的时间
                        elif a == b:
                            months = a
                        else:
                            months = ",".join(str(e) for e in list_2[a-1:b])
                        if l['time_']:
                            for t in l['time_']:
                                start_time = t['start_time']
                                end_time = t['end_time']
                                descr = t['descr']
                                rate = t['rate']
                                p_ = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,rate=rate, months=months,month=str(month),years=datestart, station=name, op_ts=now_time)
                                user_session.add(p_)
                                if datestart_:
                                    p_1 = ElePrice(name=name_, descr=descr, start_time=start_time, end_time=end_time,
                                                  rate=rate, months=months_, month=str(month), years=datestart_,
                                                  station=name, op_ts=now_time)
                                    user_session.add(p_1)

                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ProjectDelete':  # 删除报表配置
                id = self.get_argument('id',None)#项目表id
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(ProjectInfo).filter(ProjectInfo.id==id).first()
                if not page:
                    return self.customError("无效id")

                p = user_session.query(ElePrice).filter(ElePrice.station == page.name).all()
                if p:
                    for i in p:
                        i.is_use = '0'
                page.is_use = '0'
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'ProjectDeviceAdd': # 添加项目设备
                id = self.get_argument('id', None) #"项目id"
                descr = self.get_argument('descr', None) #"项目名称"
                name = self.get_argument('name', None) #"电站英文名"
                unit = self.get_argument('unit', None)  # "单元名称"
                list1 = self.get_argument('list1', []) #"单元信息"

                if not id or not descr or not name or not unit:
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,list1:%s,unit:%s'%(id,descr,name,list1,unit))
                page = user_session.query(Equipment).filter(Equipment.descr == descr, Equipment.station == name,Equipment.name == unit,Equipment.is_use == '1').first()
                if page:
                    return self.customError("数据已存在")

                PCS = {}
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            if ii['pcs_num'] in PCS:
                                return self.customError("数据已存在2")
                            else:
                                PCS = {ii['pcs_num']: ""}
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui.keys():
                                    ii['dui'][indx_1]['cu'].append(iii['cu'][0])#取相同堆名称里的簇放一起
                                else:
                                    dui = {iii['dui_num']: ""}
                                    indx_1= ii['dui'].index(iii)
                            for iii in ii['dui']:
                                cu = {}
                                for iiii in iii['cu']:
                                    if iiii['cu_num'] in cu.keys():
                                        return self.customError("数据已存在3")
                                    else:
                                        cu = {iiii['cu_num']: ""}

                p1 = Equipment(id=uuid.uuid1(), name=unit, parent_id=id, station=name, descr=descr, equ_ty=1)  # 保存储能单元
                user_session.add(p1)
                user_session.commit()

                if list1:
                    for i in list1:
                        for ii in i['PCS']:
                            # 保存PCS
                            p2 = Equipment(id=uuid.uuid1(),name=ii['pcs_num'], parent_id=p1.id, station=name, descr=descr,equ_ty=2, pcs_name=ii['pcs_name'], pcs_ty_num=ii['pcs_ty_num'],pcs_s_name=ii['pcs_s_name'], act_acc_ene=ii['act_acc_ene'])
                            user_session.add(p2)
                            user_session.commit()
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui:
                                    pass
                                else:
                                    dui = {iii['dui_num']: ""}
                                    # 保存堆
                                    p3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=p2.id, station=name, descr=descr,equ_ty=3)
                                    user_session.add(p3)
                                    user_session.commit()

                                    cu = {}
                                    for iiii in iii['cu']:
                                        if iiii['cu_num'] in cu:
                                            pass
                                        else:
                                            cu = {iiii['cu_num']: ""}
                                            #保存簇
                                            p4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=p3.id, station=name, descr=descr,equ_ty=4, cool_mode=iiii['cool_mode'],cell_type=iiii['cell_type'], cell_num=iiii['cell_num'])
                                            user_session.add(p4)
                                            user_session.commit()
                                            for iiiii in iiii['cell']:
                                                # 保存电芯
                                                p5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=p4.id, station=name,descr=descr, equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],total_ene=iiiii['total_ene'], cell_s_na=iiiii['cell_s_na'],bms_num=iiiii['bms_num'])
                                                user_session.add(p5)
                                                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'ProjectDeviceUpdate': # 修改项目设备
                id = self.get_argument('id', None) #"项目id"
                descr = self.get_argument('descr', None) #"项目名称"
                name = self.get_argument('name', None) #"电站英文名"
                unit = self.get_argument('unit', None) #"单元名称"
                unit_id = self.get_argument('unit_id', None) #"单元id"
                list1 = self.get_argument('list1', []) #"单元信息"
                if not id or not descr or not name or not unit or not unit_id:
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,descr:%s,name:%s,unit:%s,unit_id:%s,list1:%s'%(id,descr,name,unit,unit_id,list1))
                page = user_session.query(Equipment).filter(Equipment.id == unit_id).first()
                if not page:
                    return self.customError("无效id")
                pa = user_session.query(Equipment).filter(Equipment.descr == descr, Equipment.station == name,Equipment.name == unit,Equipment.is_use == '1').first()
                if pa and pa.id != unit_id:
                    return self.customError("数据已存在")

                PCS = {}
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        for ii in i['PCS']:
                            if ii['pcs_num'] in PCS:
                                return self.customError("数据已存在2")
                            else:
                                PCS = {ii['pcs_num']: ""}
                            dui = {}
                            for iii in ii['dui']:
                                if iii['dui_num'] in dui.keys():
                                    ii['dui'][indx_1]['cu'].append(iii['cu'][0])  # 取相同堆名称里的簇放一起
                                else:
                                    dui = {iii['dui_num']: ""}
                                    indx_1 = ii['dui'].index(iii)
                            for iii in ii['dui']:
                                cu = {}
                                for iiii in iii['cu']:
                                    if iiii['cu_num'] in cu.keys():
                                        return self.customError("数据已存在3")
                                    else:
                                        cu = {iiii['cu_num']: ""}

                if list1:
                    for i in list1:
                        for ii in i['PCS']:
                            if 'pcs_num_id' in ii:
                                page1 = user_session.query(Equipment).filter(Equipment.id == ii['pcs_num_id']).first()
                                if not page1:
                                    return self.customError("无效id_1")
                                page1.name = ii['pcs_num']
                                page1.pcs_name = ii['pcs_name']
                                page1.pcs_ty_num = ii['pcs_ty_num']
                                page1.pcs_s_name = ii['pcs_s_name']
                                page1.act_acc_ene = ii['act_acc_ene']
                                dui = {}
                                for iii in ii['dui']:
                                    if iii['dui_num'] in dui.keys():
                                        pass
                                    else:
                                        dui = {iii['dui_num']: ""}
                                        if 'dui_num_id' in iii:
                                            page2 = user_session.query(Equipment).filter(Equipment.id == iii['dui_num_id']).first()
                                            if not page2:
                                                return self.customError("无效id_2")
                                            page2.name = iii['dui_num']
                                            cu = {}
                                            for iiii in iii['cu']:
                                                if iiii['cu_num'] in cu.keys():
                                                    pass
                                                else:
                                                    cu = {iiii['cu_num']: ""}
                                                    if 'cu_num_id' in iiii:
                                                        page3 = user_session.query(Equipment).filter(Equipment.id == iiii['cu_num_id']).first()
                                                        if not page3:
                                                            return self.customError("无效id_3")
                                                        page3.name = iiii['cu_num']
                                                        page3.cool_mode = iiii['cool_mode']
                                                        page3.cell_type = iiii['cell_type']
                                                        page3.cell_num = iiii['cell_num']
                                                        for iiiii in iiii['cell']:
                                                            if 'cell_ty_num_id' in iiiii:
                                                                page4 = user_session.query(Equipment).filter(Equipment.id == iiiii['cell_ty_num_id']).first()
                                                                if not page4:
                                                                    return self.customError("无效id_4")
                                                                page4.name = iiiii['cell_ty_num']
                                                                page4.cell_s_ene = iiiii['cell_s_ene']
                                                                page4.total_ene = iiiii['total_ene']
                                                                page4.cell_s_na = iiiii['cell_s_na']
                                                                page4.bms_num = iiiii['bms_num']
                                                            else:
                                                                # 保存电芯
                                                                pppp5 = Equipment(name=iiiii['cell_ty_num'],
                                                                                 parent_id=iiii['cu_num_id'],
                                                                                 station=name, descr=descr,
                                                                                 equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],
                                                                                 total_ene=iiiii['total_ene'],
                                                                                 cell_s_na=iiiii['cell_s_na'],
                                                                                 bms_num=iiiii['bms_num'])
                                                                user_session.add(pppp5)
                                                                user_session.commit()
                                                    else:
                                                        ppp4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=iii['dui_num_id'],
                                                                        station=name, descr=descr, equ_ty=4,
                                                                        cool_mode=iiii['cool_mode'],
                                                                        cell_type=iiii['cell_type'],
                                                                        cell_num=iiii['cell_num'])
                                                        user_session.add(ppp4)
                                                        user_session.commit()
                                                        for iiiii in iiii['cell']:
                                                            # 保存电芯
                                                            ppp5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=ppp4.id,
                                                                            station=name, descr=descr,
                                                                            equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],
                                                                            total_ene=iiiii['total_ene'],
                                                                            cell_s_na=iiiii['cell_s_na'],
                                                                            bms_num=iiiii['bms_num'])
                                                            user_session.add(ppp5)
                                                            user_session.commit()
                                        else:
                                            pp3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=ii['pcs_num_id'],station=name, descr=descr, equ_ty=3)
                                            user_session.add(pp3)
                                            user_session.commit()
                                            for iiii in iii['cu']:
                                                # 保存簇
                                                pp4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=pp3.id, station=name,descr=descr, equ_ty=4, cool_mode=iiii['cool_mode'],cell_type=iiii['cell_type'],cell_num=iiii['cell_num'])
                                                user_session.add(pp4)
                                                user_session.commit()
                                                for iiiii in iiii['cell']:
                                                  # 保存电芯
                                                    pp5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=pp4.id, station=name,descr=descr, equ_ty=5,cell_s_ene=iiiii['cell_s_ene'], total_ene=iiiii['total_ene'],
                                                                   cell_s_na=iiiii['cell_s_na'], bms_num=iiiii['bms_num'])
                                                    user_session.add(pp5)
                                                    user_session.commit()
                            else:
                                p2 = Equipment(id=uuid.uuid1(),name=ii['pcs_num'], parent_id=unit_id, station=name, descr=descr, equ_ty=2,pcs_name=ii['pcs_name'], pcs_ty_num=ii['pcs_ty_num'],pcs_s_name=ii['pcs_s_name'], act_acc_ene=ii['act_acc_ene'])
                                user_session.add(p2)
                                user_session.commit()
                                for iii in ii['dui']:
                                    p3 = Equipment(id=uuid.uuid1(),name=iii['dui_num'], parent_id=p2.id, station=name, descr=descr,equ_ty=3)
                                    user_session.add(p3)
                                    user_session.commit()
                                    for iiii in iii['cu']:
                                        p4 = Equipment(id=uuid.uuid1(),name=iiii['cu_num'], parent_id=p3.id, station=name, descr=descr,equ_ty=4, cool_mode=iiii['cool_mode'],cell_type=iiii['cell_type'], cell_num=iiii['cell_num'])
                                        user_session.add(p4)
                                        user_session.commit()
                                        for iiiii in iiii['cell']:
                                            p5 = Equipment(id=uuid.uuid1(),name=iiiii['cell_ty_num'], parent_id=p4.id, station=name,descr=descr, equ_ty=5, cell_s_ene=iiiii['cell_s_ene'],total_ene=iiiii['total_ene'], cell_s_na=iiiii['cell_s_na'],bms_num=iiiii['bms_num'])
                                            user_session.add(p5)
                                            user_session.commit()

                return self.returnTypeSuc('')
            elif kt == 'LogicDeleteUnit':  # 逻辑删除储能单元
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page0 = user_session.query(Equipment).filter(Equipment.id == id).first()  # 单元
                if not page0:
                    return self.customError("无效id")
                page0.is_use = 0  # 单元

                page = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#PCS
                if page:
                    for p in page:
                        p.is_use = 0  # PCS
                        page1 = user_session.query(Equipment).filter(Equipment.parent_id == p.id).all()#堆
                        if page1:
                            for i in page1:
                                i.is_use = 0
                                page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 簇
                                if page2:
                                    for ii in page2:
                                        ii.is_use = 0
                                        page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id).all()  # 电芯
                                        if page3:
                                            for iii in page3:
                                                iii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'LogicDeletePCS':  # 逻辑删除PCS
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(Equipment).filter(Equipment.id == id).first()#PCS
                if not page:
                    return self.customError("无效id")
                page.is_use = 0  # PCS
                page1 = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#堆
                if page1:
                    for i in page1:
                        i.is_use = 0
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 簇
                        if page2:
                            for ii in page2:
                                ii.is_use = 0
                                page3 = user_session.query(Equipment).filter(Equipment.parent_id == ii.id).all()  # 电芯
                                if page3:
                                    for iii in page3:
                                        iii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'LogicDeleteDui':  # 逻辑删除堆
                id = self.get_argument('id', None)
                now_time = timeUtils.getNewDayStartStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id :
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(Equipment).filter(Equipment.id == id).first()#堆
                if not page:
                    return self.customError("无效id")
                page.is_use = 0
                page1 = user_session.query(Equipment).filter(Equipment.parent_id == id).all()#簇
                if page1:
                    for i in page1:
                        i.is_use = 0
                        page2 = user_session.query(Equipment).filter(Equipment.parent_id == i.id).all()  # 电芯
                        if page2:
                            for ii in page2:
                                ii.is_use = 0
                p = DeviceDelTable(name='t_project_info', del_id=id, del_user_id=user_id, del_time=now_time)
                user_session.add(p)
                user_session.commit()
            elif kt == 'AddMea':  # 添加测点
                name = self.get_argument('name', None)  # "设备名称"
                num = self.get_argument('num', None)  # "编号"
                descr = self.get_argument('descr', None)  # "描述"
                id = self.get_argument('id', None)  # "设备表单元id"
                mea_point_n = self.get_argument('mea_point_n', None)  # "测点名称"
                type_ = self.get_argument('type_', None)  # "类型"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                station = self.get_argument('station', None)  # "所属站"

                if not name or not id or not type_ or not station:
                    return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''

                if DEBUG:
                    logging.info('name:%s,num:%s,t_eq_id:%s,mea_point_n:%s,type_:%s,remarks:%s,station:%s' % (
                    name, num, id, mea_point_n, type_, remarks, station))
                if type_ == '1':
                    page = user_session.query(RemoteTele).filter(RemoteTele.mea_point_n == mea_point_n,RemoteTele.num == num,RemoteTele.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")
                    else:
                        p = RemoteTele(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)
                elif type_ == '2':
                    page = user_session.query(RemoteLetter).filter(RemoteLetter.mea_point_n == mea_point_n,RemoteLetter.num == num,RemoteLetter.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")
                    else:
                        p = RemoteLetter(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)
                elif type_ == '3':
                    page = user_session.query(RemoteModula).filter(RemoteModula.mea_point_n == mea_point_n,RemoteModula.num == num,RemoteModula.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")
                    else:
                        p = RemoteModula(name=name, num=num,t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)
                elif type_ == '4':
                    page = user_session.query(RemoteControl).filter(RemoteControl.mea_point_n == mea_point_n,RemoteControl.num == num,RemoteControl.is_use == '1').first()
                    if page:
                        return self.customError("数据已存在")
                    else:
                        p = RemoteControl(name=name, num=num, t_eq_id=id,mea_point_n=mea_point_n, type_=type_, remarks=remarks,station=station)

                user_session.add(p)
                user_session.commit()

                return self.returnTypeSuc('')
            elif kt == 'UpdateMea':  # 修改测点
                id = self.get_argument('id', None)  # "测点id
                name = self.get_argument('name', None)  # "设备名称"
                num = self.get_argument('num', None)  # "编号"
                t_eq_id = self.get_argument('t_eq_id', None)  # "设备表id"
                mea_point_n = self.get_argument('mea_point_n', None)  # "测点名称"
                type_ = self.get_argument('type_', None)  # "类型"
                remarks = self.get_argument('remarks', None)  # "基础信息备注"
                station = self.get_argument('station', None)  # "所属站"

                if not name  or not type_ or not station or not id :
                    return self.customError('参数不完整')
                remarks = ';'.join(remarks.split()) if remarks else ''

                if DEBUG:
                    logging.info('id:%s,name:%s,num:%s,t_eq_id:%s,mea_point_n:%s,type_:%s,remarks:%s,station:%s' % (
                    id,name, num,t_eq_id, mea_point_n, type_, remarks, station))
                if type_ == '1':
                    page = user_session.query(RemoteTele).filter(RemoteTele.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    pa = user_session.query(RemoteTele).filter(RemoteTele.mea_point_n == mea_point_n,RemoteTele.num == num,RemoteTele.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在")
                    self.UpdateMea( mea_point_n, name, num, page, remarks, station, t_eq_id, type_)
                elif type_ == '2':
                    page = user_session.query(RemoteLetter).filter(RemoteLetter.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    pa = user_session.query(RemoteLetter).filter(RemoteLetter.mea_point_n == mea_point_n,
                                                              RemoteLetter.num == num,RemoteLetter.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在")
                    self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)

                elif type_ == '3':
                    page = user_session.query(RemoteModula).filter(RemoteModula.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    pa = user_session.query(RemoteModula).filter(RemoteModula.mea_point_n == mea_point_n,RemoteModula.num == num,RemoteModula.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在")
                    self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)

                elif type_ == '4':
                    page = user_session.query(RemoteControl).filter(RemoteControl.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    pa = user_session.query(RemoteControl).filter(RemoteControl.mea_point_n == mea_point_n,RemoteControl.num == num,RemoteControl.is_use == '1').first()
                    if pa and pa.id != int(id):
                        return self.customError("数据已存在")
                    self.UpdateMea(mea_point_n, name, num, page, remarks, station, t_eq_id, type_)

                return self.returnTypeSuc('')
            elif kt == 'LogicDeleteMea':  # 逻辑删除测点表
                id = self.get_argument('id', None)
                type_ = self.get_argument('type_', None)  # "类型"
                now_time = timeUtils.getNewTimeStr()
                session = self.getOrNewSession()
                user_id = session.user['id']
                if not id or not type_ :
                    return self.customError('参数不完整')
                if DEBUG:
                    logging.info('id:%s,type_:%s' % (id,type_))

                if type_ == '1':
                    page = user_session.query(RemoteTele).filter(RemoteTele.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_tele', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '2':
                    page = user_session.query(RemoteLetter).filter(RemoteLetter.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_letter', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '3':
                    page = user_session.query(RemoteModula).filter(RemoteModula.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_modula', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)
                elif type_ == '4':
                    page = user_session.query(RemoteControl).filter(RemoteControl.id == id).first()
                    if not page:
                        return self.customError("无效id")
                    page.is_use = 0
                    p = DeviceDelTable(name='t_remote_control', del_id=id, del_user_id=user_id, del_time=now_time)
                    user_session.add(p)


                user_session.commit()
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()


    def UpdateMea(self, mea_point_n, name, num, page, remarks, station, t_eq_id, type_):
        #修改测点表
        if name:
            page.name = name
        if num:
            page.num = num
        if t_eq_id:
            page.t_eq_id = t_eq_id
        if mea_point_n:
            page.mea_point_n = mea_point_n
        if mea_point_n:
            page.mea_point_n = mea_point_n
        if type_:
            page.type_ = type_
        if remarks:
            page.remarks = remarks
        if station:
            page.station = station
        user_session.commit()

class ProvinceCity(BaseHandler):
    #获取省市数据
    @classmethod
    def ProvinceList(cls):# 所有省份
        data = []
        pages = user_session.query(Province.province, Province.id).order_by(Province.id.desc()).all()
        for pag in pages:
            obj = {}
            obj['name'] = pag[0]
            obj['id'] = pag[1]
            data.append(obj)
        return data

    @classmethod
    def CityList(cls,id):# 所有省份下的市
        data = []
        pages = user_session.query(City.city,City.id).filter(City.pro_id == id).order_by(City.id.desc()).all()
        for pag in pages:
            obj = {}
            obj['name'] = pag[0]
            obj['id'] = pag[1]
            data.append(obj)
        return data




#!/usr/bin/env python
# coding=utf-8
#储能电站收益管理

import json
import os
import logging
import tornado.web
from sqlalchemy import func
from Application.Models.User.income_r import RIncome
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import user_session,DEBUG
from Tools.Utils.time_utils import timeUtils
import uuid



class IncomeManagementIntetface(BaseHandler):
    ''' 收益管理 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
            data = []
            if kt == 'GetIncomeType':#收益类别
                d = [{'id':1,"name":'一次调频收益'},{'id':2,"name":'AGC收益'},{'id':3,"name":'现货收益'},{'id':4,"name":'辅助服务收益'},{'id':5,"name":'削峰填谷收益'},{'id':6,"name":'需求侧响应收益'},{'id':7,"name":'其他收益'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetIncomeList': # 所有收益记录
                descr = self.get_argument('descr',None)# 描述
                mon = self.get_argument('mon', None)  # 所在月份
                income_ty = self.get_argument('income_ty', None)  # 收益类别
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))

                filter = [RIncome.is_use==1,RIncome.in_ts==2]
                if DEBUG:
                    logging.info('descr:%s,mon:%s,income_ty:%s,pageNum:%s,pageSize:%s'%(descr,mon,income_ty,pageNum,pageSize))
                if descr:
                    filter.append(RIncome.descr==descr)
                if mon:
                    filter.append(RIncome.day==mon)
                if income_ty:
                    filter.append(RIncome.income_ty==income_ty)
                total = user_session.query(func.count(RIncome.id)).filter(*filter).scalar()
                pages = user_session.query(RIncome).filter(*filter).order_by(RIncome.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    o = eval(str(pag))
                    if o['annex']!='':
                        o['annex'] = json.loads(o['annex'])
                    if o['filename']!='':
                        o['filename'] = json.loads(o['filename'])
                    if o['annex_in']!='':
                        o['annex_in'] = json.loads(o['annex_in'])
                    data.append(o)
                return self.returnTotalSuc(data,total)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'AddIncome': # 添加收益
                db = self.get_argument('db', None)#电站
                descr = self.get_argument('descr', None)#描述
                income_ty = self.get_argument('income_ty', None)#收益类别
                mon = self.get_argument('mon', None)  # 所在月份
                time_frame = self.get_argument('time_frame', None)#时间范围
                income_yu = self.get_argument('income_yu', None)#收益(元)
                chag = self.get_argument('chag', None)#充电量
                disg = self.get_argument('disg', None)#放电量
                remark = self.get_argument('remark', None)#备注
                if not db or not descr or not mon or not income_ty or not income_yu:
                    return self.customError('参数不完整')
                remark = ';'.join(remark.split()) if remark else ''

                if DEBUG:
                    logging.info('mon:%s,time_frame:%s,income_yu:%s,descr:%s'%(mon,time_frame,income_yu,descr))

                p= self._income_exist(descr,mon,income_ty)
                if p:
                    logging.info("收益已重复")
                    return self.customError('收益已重复')

                files = self.request.files

                file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('files')
                path_all=[]
                filename_all=[]
                if imgs:
                    for i in imgs:
                        data = i.get('body')
                        filename = i.get('filename')
                        doc_format = str(os.path.splitext(filename)[1])  # 格式
                        uploadfilename = str(uuid.uuid1()) + doc_format
                        path = '%s/%s' % (file_path, uploadfilename)
                        path_all.append(path)
                        filename_all.append(filename)
                        file = open(path, 'wb')
                        file.write(data)
                        file.close()
                annex_in=[]
                if path_all:
                    for i in path_all:
                        indx = path_all.index(i)
                        annex_in.append(indx)
                path_all = json.dumps(path_all, ensure_ascii=False)
                filename_all = json.dumps(filename_all, ensure_ascii=False)
                annex_in = json.dumps(annex_in, ensure_ascii=False)

                page = user_session.query(RIncome).filter(RIncome.descr == descr, RIncome.is_use == 1, RIncome.day == mon[:4], RIncome.in_ts == 1).first()
                if page:
                    income_y=  float(page.income)+(float(income_yu)/10000) #年收益
                    page.income=income_y
                    page.income_yu=income_y*10000
                else:
                    income_y = float(income_yu) / 10000  # 年收益
                    p1 = RIncome(income=income_y, is_use='1',op_ts=timeUtils.getNewTimeStr(),descr=descr, day=mon[:4], in_ts='1', station_name=db,target_income=0,income_yu=0)
                    user_session.add(p1)
                p2 = RIncome(income=float(income_yu)/10000,annex=path_all,filename=filename_all,income_yu=income_yu,remark=remark,disg=disg,chag=chag,income_ty=income_ty,is_use='1',op_ts=timeUtils.getNewTimeStr(),time_frame=time_frame
                            ,descr=descr,day=mon,in_ts='2',station_name=db,annex_in=annex_in,target_income=0)
                user_session.add(p2)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'UpdateIncome': # 修改记录
                id = self.get_argument('id',None)
                db = self.get_argument('db', None)  # 电站
                descr = self.get_argument('descr', None)  # 描述
                income_ty = self.get_argument('income_ty', None)  # 收益类别
                mon = self.get_argument('mon', None)  # 所在月份
                time_frame = self.get_argument('time_frame', None)  # 时间范围
                income_yu = self.get_argument('income_yu', None)  # 收益(元)
                chag = self.get_argument('chag', None)  # 充电量
                disg = self.get_argument('disg', None)  # 放电量
                remark = self.get_argument('remark', None)  # 备注
                annex_in = self.get_argument('annex_in', [])  # 附件地址下标

                if not db or not descr or not mon or not income_ty or not income_yu:
                    return self.customError('参数不完整')
                remark = ';'.join(remark.split()) if remark else ''

                if DEBUG:
                    logging.info('mon:%s,time_frame:%s,income_yu:%s,descr:%s' % (mon, time_frame, income_yu, descr))

                p = self._income_exist(descr, mon,income_ty, id)
                if p:
                    logging.info("收益已重复")
                    return self.customError('收益已重复')

                page = user_session.query(RIncome).filter(RIncome.id==id).first()
                if not page:
                    return self.customError("无效id")
                files = self.request.files
                file_path = '/home/<USER>/income/' + timeUtils.getNewTimeStr()[:4]
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                Annexs = user_session.query(RIncome).filter(RIncome.id == id).first()
                annex_in = json.loads(annex_in)
                Annexs_in=[]
                filename_=[]
                annex_=[]
                if Annexs.annex_in:
                    Annexs_in = json.loads(Annexs.annex_in)
                if Annexs.filename:
                    filename_ = json.loads(Annexs.filename)
                if Annexs.annex:
                    annex_ = json.loads(Annexs.annex)
                indx_list=[]#删除下标的列表
                if Annexs_in!=[]:
                    for i in Annexs_in:
                        indx = Annexs_in.index(i)
                        if i not in annex_in:
                            indx_list.append(indx)
                if indx_list!=[]:
                    indx_list.reverse()
                    for l in indx_list:
                        del filename_[l]
                        del annex_[l]
                        user_session.commit()

                imgs = files.get('files')
                if imgs:
                    for i in imgs:
                        data = i.get('body')
                        filename = i.get('filename')
                        doc_format = str(os.path.splitext(filename)[1])  # 格式
                        uploadfilename = str(uuid.uuid1()) + doc_format
                        path = '%s/%s' % (file_path, uploadfilename)
                        file = open(path, 'wb')
                        file.write(data)
                        file.close()
                        filename_.append(filename)
                        annex_.append(path)
                annex_in_=[]
                if annex_:
                    for i in annex_:
                        indx = annex_.index(i)
                        annex_in_.append(indx)
                annex_ = json.dumps(annex_, ensure_ascii=False)
                filename_ = json.dumps(filename_, ensure_ascii=False)
                annex_in_ = json.dumps(annex_in_, ensure_ascii=False)

                Annexs.station_name = db
                Annexs.descr = descr
                Annexs.income_ty = income_ty
                Annexs.day = mon[:7]
                Annexs.time_frame = time_frame
                Annexs.income_yu = income_yu
                Annexs.chag = chag
                Annexs.disg = disg
                Annexs.remark = remark
                Annexs.annex_in = annex_in_
                Annexs.filename = filename_
                Annexs.annex = annex_
                Annexs.op_ts = timeUtils.getNewTimeStr()
                Annexs.income = float(income_yu) / 10000
                user_session.commit()
                page = user_session.query(RIncome).filter(RIncome.station_name == db, RIncome.is_use == 1,RIncome.day == mon[:4], RIncome.in_ts == 1).first()
                if page:
                    page_sum = user_session.query(func.sum(RIncome.income_yu)).filter(RIncome.station_name == db, RIncome.is_use == 1,RIncome.day.like(mon[:4]+'%'), RIncome.in_ts == 2).first()
                    income = float(page_sum[0])
                    page.income=income/10000
                    page.income_yu = income
                else:
                    income_y = (float(income_yu))/ 10000  # 年收益
                    p1 = RIncome(income=income_y, is_use='1', op_ts=timeUtils.getNewTimeStr(), descr=descr,day=mon[:4], in_ts=1, station_name=db,income_yu=income_y,target_income=0)
                    user_session.add(p1)
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'LogicDeleteIncome': # 逻辑删除
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))

                page = user_session.query(RIncome).filter(RIncome.id==id).first()
                if not page:
                    return self.customError("无效id")
                descr=page.descr
                day=page.day[:4]

                page_year = user_session.query(RIncome).filter(RIncome.descr ==descr, RIncome.is_use == 1,RIncome.day == day, RIncome.in_ts == 1).first()
                page_year.income=float(page_year.income)-float(page.income)
                page_year.income_yu=(float(page_year.income)-float(page.income))*10000

                if float(page.target_income)>0.01:
                    page_year.target_income=float(page_year.target_income)-float(page.target_income)

                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()


    def _income_exist(self, descr,mon,income_ty,id = None):
        '''判断收益是否存在'''
        filter = [RIncome.descr == descr,RIncome.income_ty==income_ty, RIncome.is_use == 1,RIncome.day==mon,RIncome.in_ts==2]
        if id:
            filter.append(RIncome.id != id)
        page = user_session.query(RIncome).filter(*filter).first()
        if page:
            return True
        else:
            return False







