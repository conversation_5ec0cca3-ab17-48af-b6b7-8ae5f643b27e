#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-08 17:28:09
#@FilePath     : \RHBESS_Service\Tools\DataEnDe\aes_cbc.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-02 15:47:06


from Crypto.Cipher import AES
import os,json
import base64
import hashlib
from Tools.DataEnDe.MD5 import MD5Tool
import requests

"""
aes加密算法
padding : PKCS7
"""

class AESUtil:

    __BLOCK_SIZE_16 = BLOCK_SIZE_16 = AES.block_size

    @staticmethod
    def encryt(str1, key, iv):
        cipher = AES.new(key, AES.MODE_CBC,iv)
        x = AESUtil.__BLOCK_SIZE_16 - (len(str1) % AESUtil.__BLOCK_SIZE_16)
        if x != 0:
            str1 = str1 + chr(x)*x
        str1 = str1.encode()
        msg = cipher.encrypt(str1)
        # msg = base64.urlsafe_b64encode(msg).replace('=', '')
        msg = base64.b64encode(msg)
        return msg

    @staticmethod
    def decrypt(enStr, key, iv):
        cipher = AES.new(key, AES.MODE_CBC, iv)
        # enStr += (len(enStr) % 4)*"="
        # decryptByts = base64.urlsafe_b64decode(enStr)
        decryptByts = base64.b64decode(enStr)
        msg = cipher.decrypt(decryptByts)
        paddingLen = msg[len(msg)-1]
        # print ('paddingLen:',paddingLen)
        st = str(msg[0:-paddingLen],encoding="utf-8")
        return st

if __name__ == "__main__":
    key_zj = b"RHBESS1101022022"
    iv_zj = b"1101020090002022"
    key = b"fastfuncn1234567"
    iv = b"fastfuncn1234567"
    openId_zj = b'yuanchu1'
    openId = b'RHBESS01'

    # value,time = [],[]
    # for i in range(20):
    #     value.append(i)
    #     time.append(i)
    # data = {"1": 3,"2": 4,"3": 4,"4": 0,"income_year":1198.82,"month_income":208.26,"total_income":2867.43}
    # # # data = {"workNet": "NBLS001", "type_": "measure","startTime":"2023-06-25 14:00:00","endTime":"2023-06-25 14:10:00"}
    # res = AESUtil.encryt(json.dumps(data), key_zj, iv_zj)  # 加密
    # print ('res:',res) # 
    # # # res = b'AflGkiBtd7ibX8D9T89tIoUk68pC8MOTEBfjcojgDOzIjAcpaY1iB8X6otBk6974'
    # # # # # # res = b'7LeTqp80qxWqXRzZoMMSMSm6/sLkXFKeybyjMotjUKK73AGO1h0nYMgQIfehIl1ENiLKx44RAOb7xy2/8K6kOXm9KQGMogVbPC5iMsbSlSLkI6O+lzHprsEnTbSRyRlsVlDCBweqm8zcYjCNdMrizy9SjUzr5FbBgpzTeEhn0K0='
    # md = str(res,encoding="utf-8")+str(openId,encoding="utf-8")
    # print ('md:',md)
    # md_sale = MD5Tool.get_str_md5(md)
    # print (str(md_sale))

    d = '+W1q9wOp2zHd5Msd8M5Q7EwL5Tok6oj+o4TvSR90+Y/aW9u3LwXAij33qerMO5FwEXgXBwiaqpoqpS3Acu5KvlsLPJt6t+pERuLseQcHQMR5GTKTYlM/Jkt4TL9KcfIcCyacxCbaoQhA7aLupyeMns6a879R/F2gZpPQ5h+SZqm3o+cX9rRX31ssChBmo7vjIcJlog/4qxXdyAZh7AEZGHzkMGRdiAKxNi2uznJVMf+Vo9szln/8m0hA9lczXBKyl5AKRk3PpHiNqpynGhZ2RIFH8qiV2UJYRw8ATfSo/RYc4/YfmPygtI2Q/QMJb29uvaHJXTvLcsAe3xYFVu0SAFt5zxqsZZqrq3WC7+YzvT2C4IAKXg5wfCfiLBq3Xcm0zyNf3fQAKAut9V1sVDvSGJwysNJ9b1o0XbhrVFNriW542hkj1OtfD1DBuiNs05AU0WmaIz+UUhSWXKlLewVOUg=='
    print (AESUtil.decrypt(d,key,iv))

    #  获取报告
    # header = {
    #     "accept":"*/*",
    #     "user-agent":"ApiPOST Runtime +https://www.apipost.cn",
    #     # "Content-Type": "application/x-www-form-urlencoed",
    #     "accept-encoding":"gzip, deflate, br",
    #     "accept-language":"zh-CN",
    #     "Connection":"keep-alive",
    #     "Bean":str(md_sale),
    # }
    # result = requests.post("https://rframe.robestec.cn/yc/HisData/getReportInfo",headers=header,data = {"data":str(res,encoding="utf-8")})
    # print(result.content.decode('utf-8'))
    
   

    

