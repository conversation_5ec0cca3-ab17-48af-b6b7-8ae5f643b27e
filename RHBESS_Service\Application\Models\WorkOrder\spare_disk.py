#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-03-14 17:07:55
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WorkOrder\spare_disk.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-16 11:44:56


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.spare_info import SpareInfo

class SpareDisk(user_Base):
    u'备件盘库表'
    __tablename__ = "t_spare_disk"
    id = Column(Integer, ForeignKey("t_spare_info.id"), primary_key=True,comment=u"主键")
    year_mon = Column(String(20), nullable=False,primary_key=True,comment=u"时间,只保存年月即可")
    before_num = Column(Integer, nullable=True,comment=u"前期末库存数量")
    now_in_num = Column(Integer, nullable=True,comment=u"当前入库数量")
    now_out_num = Column(Integer, nullable=True,comment=u"当前出库数量")
    now_num = Column(Integer, nullable=True,comment=u"当期末库存数量")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")

    spare_info_disk= relationship("SpareInfo", backref="spare_info_disk")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        bean = "{'id':%s,'before_num':'%s','now_in_num':'%s','now_out_num':'%s','now_num':'%s'}" % (self.id,self.before_num,self.now_in_num,self.now_out_num,self.now_num)
        return bean.replace("None",'')
       
        
