#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-18 10:04:59
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WebSocket\message.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-18 10:04:59

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text


class Message(user_Base):
    u'消息表'
    __tablename__ = "t_message"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    descr = Column(Text, nullable=False, comment=u"消息体")
    type = Column(VARCHAR(256), nullable=True, comment=u"消息类型，error/info/success")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    source = Column(Integer, nullable=True, comment=u"消息来源/如类型为order关联工单id/alarm关联告警/template模板/msg普通消息无关联")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()