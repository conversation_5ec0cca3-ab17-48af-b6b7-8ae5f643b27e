CREATE TABLE IF NOT EXISTS `t_point_type` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `type` SMALLINT NOT NULL COMMENT '类型',
  `device` VARCHAR(8) NOT NULL COMMENT '设备名称',
  `is_stand` SMALLINT NOT NULL DEFAULT 1 COMMENT '是否标准类型'
);


CREATE TABLE IF NOT EXISTS `t_point_measure` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `name` VARCHAR(16) NOT NULL COMMENT '点位英文',
  `description` VARCHAR(32) NOT NULL COMMENT '数据描述',
  `data_type` VARCHAR(8) COMMENT '数据类型',
  `unit` VARCHAR(8) COMMENT '单位',
  `point_type_id` INT COMMENT '点表类型外键',
  FOREIGN KEY (`point_type_id`) REFERENCES `t_point_type`(`id`) ON DELETE CASCADE
);



CREATE TABLE IF NOT EXISTS `t_point_status` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `name` VARCHAR(16) NOT NULL COMMENT '点位英文',
  `description` VARCHAR(32) NOT NULL COMMENT '数据描述',
  `data_type` VARCHAR(8) COMMENT '数据类型',
  `a_status_name` VARCHAR(32)  COMMENT '状态A',
  `b_status_name` VARCHAR(32)  COMMENT '状态B',
  `point_type_id` INT COMMENT '点表类型外键',
  FOREIGN KEY (`point_type_id`) REFERENCES `t_point_type`(`id`) ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS `t_point_discrete` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `name` VARCHAR(16) NOT NULL COMMENT '点位英文',
  `description` VARCHAR(32) NOT NULL COMMENT '数据描述',
  `data_type` VARCHAR(8) COMMENT '数据类型',
  `n_status` VARCHAR(16)  COMMENT '离散值',
  `desc_status` VARCHAR(32)  COMMENT '离散描述',
  `point_type_id` INT COMMENT '点表类型外键',
  FOREIGN KEY (`point_type_id`) REFERENCES `t_point_type`(`id`) ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS `t_point_cumulant` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `name` VARCHAR(16) NOT NULL COMMENT '点位英文',
  `description` VARCHAR(32) NOT NULL COMMENT '数据描述',
  `data_type` VARCHAR(8) COMMENT '数据类型',
  `unit` VARCHAR(8) COMMENT '单位',
  `point_type_id` INT COMMENT '点表类型外键',
  FOREIGN KEY (`point_type_id`) REFERENCES `t_point_type`(`id`) ON DELETE CASCADE
);






