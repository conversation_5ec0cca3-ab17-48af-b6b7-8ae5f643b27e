# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/27 16:01
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : demo.py
# @Software : PyCharm

from datetime import datetime


def get_last_12_months():
    """
    获取历史12个月（不包括本月）
    """""
    temp_list = []
    current_date = datetime.now()
    for i in range(1, 13):
        month = (current_date.month - i) % 12
        if month == 0:
            month = 12
        year = current_date.year if current_date.month > i else current_date.year - 1
        # print(f"{year}-{month:02d}")
        temp_list.append(f"{year}-{month:02d}")
    return temp_list


def get_last_12_months_v1():
    """
    获取历史12个月（不包括本月）
    """""
    temp_list = []
    current_date = datetime.now()
    for i in range(1, 13):
        month = (current_date.month - i) % 12
        if month == 0:
            month = 12
        year = current_date.year if current_date.month > i else current_date.year - 1
        # print(f"{year}-{month:02d}")
        temp_list.append(f"{year}{month:02d}")
    return temp_list


if __name__ == '__main__':
    print(get_last_12_months())
