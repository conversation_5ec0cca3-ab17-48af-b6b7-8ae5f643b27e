#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/10/14 下午3:15
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me


from rest_framework.response import Response
from common import common_response_code
from apis.algorithm.decorators import require_ip_whitelist
from rest_framework.views import APIView
from django.conf import settings
from apis.user import models
from apis.app2.utils import paging
from django.db import connections, transaction
import json
import datetime
from apis.web.models import DictModel
from dbutils.persistent_db import PersistentDB
import pymysql
from decimal import Decimal, getcontext
from dateutil.relativedelta import relativedelta

# 连接数据库
pool = PersistentDB(pymysql, 10, **{
    "host": '************',  # 数据库主机地址
    "user": 'work01',  # 数据库用户名
    "password": 'Worker001#',  # 数据库密码
    "database": 'ele_price_decision',  # 数据库名称
    "port": 9300,
    "cursorclass": pymysql.cursors.DictCursor
})

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
getcontext().prec = 8


class ProjectView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """获取项目及电站信息"""
        try:
            page = int(request.data.get("pageNum", 1))
            page_size = int(request.data.get("pageSize", 100))
            project_lists = models.Project.objects.prefetch_related('materstation_set__stationdetails_set').filter(is_used=1).all()
            page_res = paging(page, page_size, project_lists)
            result = []
            for a_instance in list(page_res.get('data')):
                child_list = list(a_instance.materstation_set.all())  # 转换为列表，避免在模板中多次查询数据库
                child_l = [{"station_name": i.name, "station_english_name": i.english_name, "meter_position": i.english_name, 'meter_position': list(i.stationdetails_set.all())[0].meter_position} for i in child_list if not i.is_delete]
                result.append({
                    'project_name': a_instance.name,
                    'project_english_name': a_instance.english_name,
                    'stations': child_l
                })
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": result},
                    "total": page_res.get('total'),
                    "totalpage": page_res.get('totalpage'),
                    "page": page_res.get('page'),
                    "page_size": page_size
                }
            )
        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class StationPloadsView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """
        获取并网点负荷值
        """
        try:
            stations = request.data.get("stations", None)  # 并网点列表
            start_time = request.data.get("startTime", None)  # 开始时间
            end_time = request.data.get("endTime", None)  # 结束时间
            if not stations or not start_time or not end_time:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,必传参数不可为空"}
                })
            # if not can_be_parsed_as_list(stations):
            #     return Response({
            #         "code": common_response_code.FIELD_ERROR,
            #         "data": {"message": "error", "detail": "参数错误,stations格式错误"}
            #     })
            if not judge_is_date(start_time, '%Y-%m-%d %H:%M:%S') or not judge_is_date(end_time, '%Y-%m-%d %H:%M:%S'):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间格式错误！"},
                    }
                )
            if is_over_by_days(start_time[0:10], end_time[0:10], 31):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间间隔不能超过31天！"},
                    }
                )
            # stations = json.loads(stations)
            in_clause = ', '.join(f'"{station}"' for station in stations)
            sql_condition = "time BETWEEN '{}' and '{}' and state_pcc = 0 and station in ({}) order by time asc".format(
                start_time, end_time, in_clause)


            load_true_data = get_ads_rhyc_data_by_sql("DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, p_load, station",
                                                         'ads_report_loading_data', sql_condition)
            # 获取所有日期时间列表
            total_dates = get_all_date_between(start_time[0:10], end_time[0:10])
            all_date_arr = {}
            for item in total_dates:
                date_arr = create_date_time_mapping(item)
                all_date_arr = {**all_date_arr, **date_arr}

            result = load_true_data
            # 转换查询结果为字典
            dict_data = {}
            for item in result:
                if item[2] in dict_data:
                    dict_data[item[2]].update({item[0]: item[1]})
                else:
                    dict_data[item[2]] = {item[0]: item[1]}
            result_dict = {}
            for item_key in dict_data.keys():
                merged_dict = {k: dict_data[item_key].get(k[0:16], all_date_arr[k]) for k in all_date_arr}
                result_dict[item_key] = list(map(lambda item: item[1], merged_dict.items()))
            data = {
                "data": result_dict,
                "time": all_date_arr.keys()
            }
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data}
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.SUMMARY_CODE,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class StationElectricQuantityView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """
        获取并网点电量值
        """
        try:
            stations = request.data.get("stations", None)  # 并网点列表
            start_time = request.data.get("startTime", None)  # 开始时间
            end_time = request.data.get("endTime", None)  # 结束时间
            if not stations or not start_time or not end_time:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,必传参数不可为空"}
                })
            # if not can_be_parsed_as_list(stations):
            #     return Response({
            #         "code": common_response_code.FIELD_ERROR,
            #         "data": {"message": "error", "detail": "参数错误,stations格式错误"}
            #     })
            if not judge_is_date(start_time, '%Y-%m-%d %H:%M:%S') or not judge_is_date(end_time, '%Y-%m-%d %H:%M:%S'):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间格式错误！"},
                    }
                )
            if is_over_by_days(start_time[0:10], end_time[0:10], 31):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间间隔不能超过31天！"},
                    }
                )
            # stations = json.loads(stations)
            in_clause = ', '.join(f'"{station}"' for station in stations)
            sql_condition = "time BETWEEN '{}' and '{}' and station in ({}) order by time asc".format(
                start_time, end_time, in_clause)


            load_true_data = get_ads_rhyc_data_by_sql("DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, diff, station",
                                                         'ads_report_loading_chag_data', sql_condition)
            # 获取所有日期时间列表
            total_dates = get_all_date_between(start_time[0:10], end_time[0:10])
            all_date_arr = {}
            for item in total_dates:
                date_arr = create_date_time_mapping(item)
                all_date_arr = {**all_date_arr, **date_arr}

            result = load_true_data
            # 转换查询结果为字典
            dict_data = {}
            for item in result:
                if item[2] in dict_data:
                    dict_data[item[2]].update({item[0]: item[1]})
                else:
                    dict_data[item[2]] = {item[0]: item[1]}
            result_dict = {}
            for item_key in dict_data.keys():
                merged_dict = {k: dict_data[item_key].get(k[0:16], all_date_arr[k]) for k in all_date_arr}
                result_dict[item_key] = list(map(lambda item: item[1], merged_dict.items()))
            data = {
                "data": result_dict,
                "time": all_date_arr.keys()
            }
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data}
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.SUMMARY_CODE,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class StationSaveForecastView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """
        接收预测的结果数据并保存
        """
        try:
        # if 1:
            project_english_name = request.data.get("project_english_name", None)  # 项目英文名称
            station_english_names = request.data.get("station_english_names", None)  # 并网点名称及数据集
            target_type = request.data.get("type", None)  # 指标类型 1负荷2电量
            model_name = request.data.get("model_name", None)  # 模型名称
            if not project_english_name or not station_english_names or not target_type or not model_name:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,必传参数不可为空"}
                })
            # if not can_be_parsed_as_list(station_english_names, dict):
            #     return Response({
            #         "code": common_response_code.FIELD_ERROR,
            #         "data": {"message": "error", "detail": "参数错误,station_english_names格式错误"}
            #     })
            if int(target_type) not in [1, 2, 20]:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,指标类型错误"}
                })
            # 获取模型id
            model_id = DictModel.objects.filter(name=model_name).values_list('id', flat=True).first()
            # 获取项目id
            project_id = models.Project.objects.filter(english_name=project_english_name).values_list('id', flat=True).first()
            # data_list = json.loads(str(station_english_names).strip())
            data_list = station_english_names
            # 循环组装入库数据
            insert_list = []
            insert_model_rate_list = []
            # model_rate_all = 0  # 当前总的模型准确率
            # model_rate_num = 0  # 当前总的模型准确率个数
            for item_name in data_list:
                # 获取并网点id
                station_id = models.MaterStation.objects.filter(english_name=item_name, project_id=project_id).values_list('id', flat=True).first()
                # 判断时间数量与预测数据数量是否一致
                item = data_list[item_name]
                # if len(item['time']) != len(item['data']):
                #     return Response({
                #         "code": common_response_code.FIELD_ERROR,
                #         "data": {"message": "error", "detail": "参数错误,请检查时间集合与数据集合的数量是否一致"}
                #     })
                if len(item['time']) != 96 or len(item['data']) != 96:
                    return Response({
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "参数错误,请检查时间集合与数据集合的数量是否为96"}
                    })
                # 如果之前有数据需要先逻辑删除
                up_sql = "update dwd_model_forecast_value SET is_use = 0 where target_id={} and mstation_id={} and model_id={} and forecast_time='{}'".format(int(target_type), station_id, model_id, item['day'])
                with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
                    try:
                        dwd_cursor.execute(up_sql)
                    except Exception as e:
                        error_log.error(e)
                # 准确率要逻辑删除前一天的
                if int(target_type) != 20:
                    now = datetime.datetime.now()
                    date_str = now.strftime('%Y-%m-%d')
                    up_rate_sql = ""
                    if item['day'] == date_str:
                        day_str = get_previous_day(item['day'])
                        up_rate_sql = "update dwd_dict_model_rate SET is_use=0 where target_id={} and mstation_id={} and model_id={} and forecast_time='{}'".format(int(target_type), station_id, model_id, day_str)
                    with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
                        try:
                            if item['day'] == date_str:
                                dwd_cursor.execute(up_rate_sql)
                        except Exception as e:
                            error_log.error(e)
                    model_target_rate = calculate_model_rate_by_day(target_type, model_id, station_id, item_name,
                                                                    item['data'], item['day'])
                    # model_rate_all += model_target_rate
                    # model_rate_num += 1
                    if model_target_rate != 0:
                        insert_model_rate_tuple = (
                        target_type, station_id, model_id, get_previous_day(item['day']), model_target_rate,
                        datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 1)
                        insert_model_rate_list.append(insert_model_rate_tuple)

                for i in range(len(item['time'])):
                    insert_tuple = (int(target_type), station_id, model_id,
                                  item['day'], item['time'][i],
                                  float(item['data'][i]), datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

                    insert_list.append(insert_tuple)

            insert_rate_sql = ""
            if int(target_type) != 20:
                insert_rate_sql = "INSERT INTO dwd_dict_model_rate (target_id, mstation_id, model_id, forecast_time, rate, create_time, is_use) VALUES (%s, %s, %s, %s, %s, %s, %s)"
            insert_sql = "INSERT INTO dwd_model_forecast_value (target_id, mstation_id, model_id, forecast_time, forecast_hour_min, value, create_time) VALUES (%s, %s, %s, %s, %s, %s, %s)"
            with connections['doris_dwd_rhyc'].cursor() as ads_cursor:
                try:
                    ads_cursor.executemany(insert_sql, insert_list)
                    if insert_model_rate_list and int(target_type) != 20:
                        ads_cursor.executemany(insert_rate_sql, insert_model_rate_list)
                except Exception as e:
                    error_log.error(e)
            if int(target_type) != 20:
                # 计算模型的总准确率
                # 获取当前时间
                now = datetime.datetime.now()

                # 获取一个月前的时间
                one_month_ago = now - relativedelta(months=1)

                # 格式化时间，只保留年月日
                start_date = one_month_ago.strftime('%Y-%m-%d')
                model_avg_rate_sql = "SELECT AVG(rate) AS avg_rate FROM dwd_dict_model_rate where model_id={} and is_use=1 and forecast_time>='{}' and rate>={} and rate<={};".format(int(model_id), start_date, 0, 100)
                with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
                    try:
                        dwd_cursor.execute(model_avg_rate_sql)
                        result = dwd_cursor.fetchone()
                    except Exception as e:
                        error_log.error(e)

                model_rate = round(result[0], 4) if result else 0
                # 获取指定 ID 的记录
                record = DictModel.objects.get(id=model_id)
                # 计算新的平均准确率
                if record:
                    new_average_rate = model_rate
                    if float(new_average_rate) > float(100000) :
                        new_average_rate = 100000
                    if float(new_average_rate) < float(-100000) :
                        new_average_rate = -100000

                    # 更新记录
                    with transaction.atomic():
                        record.rate = round(new_average_rate, 2)
                        record.save()

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": "",
                    "msg": "SUCCESS"
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.SUMMARY_CODE,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class StationWeatherView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """
        获取天气
        """
        try:
            project_name = request.data.get("project_name", None)  # 项目名
            start_time = request.data.get("startTime", None)  # 开始时间
            end_time = request.data.get("endTime", None)  # 结束时间
            if not project_name or not start_time or not end_time:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,必传参数不可为空"}
                })
            if not judge_is_date(start_time, '%Y-%m-%d') or not judge_is_date(end_time, '%Y-%m-%d'):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间格式错误！"},
                    }
                )
            if is_over_by_days(start_time[0:10], end_time[0:10], 31):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间间隔不能超过31天！"},
                    }
                )

            city_name = models.Project.objects.filter(english_name=project_name).values('city').first()['city']
            if not city_name:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "项目名错误！"},
                    }
                )

            weather_data = get_weather(city_name, start_time, end_time)
            new_weather_data = supplement_data(weather_data)
            # 获取所有日期时间列表
            total_dates = get_all_date_between(start_time[0:10], end_time[0:10])
            all_date_arr = {}
            for item in total_dates:
                date_arr = create_date_time_mapping(item)
                all_date_arr = {**all_date_arr, **date_arr}
            data = {
                "data": new_weather_data,
                "time": all_date_arr.keys()
            }
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data}
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.SUMMARY_CODE,
                "data": {"message": "error", "detail": "查询失败！"},
            })


class StationPriceView(APIView):
    @require_ip_whitelist()
    def post(self, request):
        """获取并网点电价数据"""
        try:
            stations = request.data.get("stations", None)  # 并网点列表
            start_time = request.data.get("startTime", None)  # 开始时间
            end_time = request.data.get("endTime", None)  # 结束时间
            if not stations or not start_time or not end_time:
                return Response({
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误,必传参数不可为空"}
                })
            # if not can_be_parsed_as_list(stations):
            #     return Response({
            #         "code": common_response_code.FIELD_ERROR,
            #         "data": {"message": "error", "detail": "参数错误,stations格式错误"}
            #     })
            if not judge_is_date(start_time, '%Y-%m-%d') or not judge_is_date(end_time, '%Y-%m-%d'):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间格式错误！"},
                    }
                )
            if is_greater_than_current_year_month(start_time) or is_greater_than_current_year_month(end_time):
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {"message": "error", "detail": "时间超过了当前年月！"},
                    }
                )
            if is_over_by_days(start_time[0:10], end_time[0:10], 31):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "时间间隔不能超过31天！"},
                    }
                )
            # stations = json.loads(stations)
            in_clause = ', '.join(f'"{station}"' for station in stations)

            station_price_data = get_station_price(start_time, end_time, stations)
            if not station_price_data:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {"message": "error", "detail": "没有查到电价数据！"},
                    }
                )

            # 获取所有日期时间列表
            total_dates = get_all_date_between(start_time[0:10], end_time[0:10])
            all_date_arr = {}
            for item in total_dates:
                date_arr = create_date_time_mapping(item)
                all_date_arr = {**all_date_arr, **date_arr}

            # 初始化结果字典
            station_result = {station: [] for station in station_price_data.keys()}

            # 遍历字典 all_date_arr 的键
            for key in all_date_arr.keys():
                year_month = key[:7]  # 提取年月部分
                moment = key[11:16]  # 提取时分部分

                for station, data in station_price_data.items():
                    if year_month in data:
                        for entry in data[year_month]:
                            if entry['moment'] == moment:
                                station_result[station].append(float(entry['price']))
            data = {
                "data": station_result,
                "time": all_date_arr.keys()
            }
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": data}
                }
            )

        except Exception as e:
            error_log.error(e)
            return Response({
                "code": common_response_code.SUMMARY_CODE,
                "data": {"message": "error", "detail": "查询失败！"},
            })


def get_ads_rhyc_data_by_sql(field_name_str, table_name, sql_condition):
    """
    获取负荷实测值 ads_report_loading_data
    获取电量实测值 ads_report_loading_chag_data
    """
    """
        创建查询doris下的ads_rhyc库下的数据sql并返回结果
        """
    # 执行SQL查询
    sql = """SELECT {}
                            FROM {}
                            WHERE {}
                            """.format(field_name_str, table_name, sql_condition)

    with connections['doris_ads_rhyc'].cursor() as ads_cursor:
        try:
            # 获取查询结果
            ads_cursor.execute(sql)
            result = ads_cursor.fetchall()
            return result
        except Exception as e:
            error_log.error(e)
    return result


def get_dwd_rhyc_data_by_sql(field_name_str, table_name, sql_condition):
    """
    获取预测数据
    创建查询doris下的dwd_rhyc库下的数据sql并返回结果
    """
    # 执行SQL查询
    sql = """SELECT {}
                            FROM {}
                            WHERE {}
                            """.format(field_name_str, table_name, sql_condition)

    with connections['doris_dwd_rhyc'].cursor() as ads_cursor:
        try:
            # 获取查询结果
            ads_cursor.execute(sql)
            result = ads_cursor.fetchall()
            return result
        except Exception as e:
            error_log.error(e)
    return result


def can_be_parsed_as_list(json_str, data_type = list):
    """
    判断变量是否可以转换成指定数据格式
    :param json_str:
    :param data_type
    :return:
    """
    try:
        result = json.loads(json_str)
        return isinstance(result, data_type)
    except json.JSONDecodeError:
        return False


def judge_is_date(judge_time, time_format='%Y/%m/%d %H:%M'):
    """
    判断日期格式是否正确
    :param time_format:
    :param judge_time:
    :return:
    """
    try:
        # 尝试将字符串转换为 datetime 对象
        datetime.datetime.strptime(str(judge_time).strip(), time_format)
        return True
    except ValueError as e:
        return False


def get_all_date_between(begin_time, end_time):
    """
    获取两个日期间的所有日期
    """
    start_date = datetime.datetime.strptime(begin_time, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_time, '%Y-%m-%d')

    # 计算日期范围内的所有日期
    date_range = [start_date + datetime.timedelta(days=i) for i in range((end_date - start_date).days + 1)]

    # 将这些日期存储在一个变量中
    total_dates = [date.strftime('%Y-%m-%d') for date in date_range]
    return total_dates


def create_date_time_mapping(date_time, m=15):
    """
    构造时间字典：默认15分钟间隔
    """
    res = {}
    start_time = datetime.datetime.strptime(str(date_time) + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
    total = 288 if m == 5 else 96  # 5分钟或15分钟
    for i in range(total):
        t = start_time + datetime.timedelta(minutes=i * m)
        t = t.strftime('%Y-%m-%d %H:%M:%S')
        res[f'{t}'] = '--'
    return res


def get_weather(city_name, start_time, end_time):
    # 先通过项目的city_name获取city_id
    conn = pool.connection()
    cursor = conn.cursor()

    try:
        sql = """	SELECT *  
                            FROM {}  
                            WHERE name like '%{}%' and is_use=1;""".format(
            'c_citys', city_name
        )
        cursor.execute(sql)
        city_res = cursor.fetchone()
    except Exception as e:
        error_log.error(e)
        return False
    finally:
        cursor.close()
        conn.close()
    if not city_res:
        return False
    city_id = city_res['id']

    conn = pool.connection()
    cursor = conn.cursor()
    # 判断开始时间和结束时间是否大于当前时间
    end_time = end_time + ' 23:59:59'
    start_time = start_time + ' 00:00:00'
    try:
        res = []
        if is_greater_than_current_time(start_time):
            # 取预测数据
            res = get_weather_data('r_weather_pre_zhejiang', city_id, start_time, end_time)
        elif not is_greater_than_current_time(start_time) and not is_greater_than_current_time(end_time):
            # 取真实数据
            res = get_weather_data('r_weather_real_zhejiang', city_id, start_time, end_time)
        else:
            # 需要分割取值
            # 获取当前时间
            current_time = datetime.datetime.now()

            # 格式化时间为 Y-m-d H:i:s 格式
            formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
            res1 = get_weather_data('r_weather_real_zhejiang', city_id, start_time, formatted_time)
            res2 = get_weather_data('r_weather_pre_zhejiang', city_id, formatted_time, end_time)
            res = res1 + res2
        return res
    except Exception as e:
        error_log.error(e)
        return False
    finally:
        cursor.close()
        conn.close()


def get_weather_data(table_name, city_id, start_time, end_time):
    conn = pool.connection()
    cursor = conn.cursor()
    try:
        sql = """	SELECT day, moment, tem, op_ts
                                FROM (
                                    SELECT 
                                        day, 
                                        moment, 
                                        tem, 
                                        op_ts,
                                        ROW_NUMBER() OVER (PARTITION BY day, SUBSTRING(moment, 1, 2) ORDER BY op_ts DESC) AS rn
                                    FROM {}
                                    WHERE CONCAT(day, ' ', moment) BETWEEN '{}' AND '{}' and city_id={} and county_id is null
                                ) AS subquery
                                WHERE rn = 1
                                ORDER BY day asc, moment asc;""".format(
            table_name, start_time, end_time, int(city_id)
        )

        cursor.execute(sql)
        result = cursor.fetchall()
        new_res = [
            {f"{item['day']} {item['moment']}": item['tem']}
            for item in result
        ]
        return new_res
    except Exception as e:
        error_log.error(e)
    finally:
        cursor.close()
        conn.close()


def get_station_price(start_time, end_time, station_names):
    """
    获取指定范围内的并网点电价
    """
    # 获取所有年月数据
    year_month_list = get_months_between(start_time, end_time)
    if not year_month_list:
        return False
    station_price_data = {}
    for station_name in station_names:
        first_station_detail = models.StationDetails.objects.filter(
            master_station__english_name=station_name
        ).order_by('id').first()
        if first_station_detail:
            station_price_data[station_name] = {}
            for year_month in year_month_list:
                # 获取并网点该年月的电价数据 PeakValleyNew
                price_data = models.PeakValleyNew.objects.filter(
                    province_id=first_station_detail.province_id,
                    year_month=year_month,
                    type=first_station_detail.type,
                    level=first_station_detail.level
                ).order_by('moment').values('year_month','moment','price')
                price_datas = list(price_data)
                station_price_data[station_name][str(year_month)] = price_datas
        else:
            return False
    return station_price_data

def get_months_between(start_time, end_time):
    """
    获取两个日期之间的所有年月的列表
    :param start_time: 开始日期，格式为 'YYYY-MM-DD'
    :param end_time: 结束日期，格式为 'YYYY-MM-DD'
    :return: 包含所有年月的列表，格式为 'YYYY-MM'
    """
    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_time, '%Y-%m-%d')

    # 确保 start_date 不晚于 end_date
    if start_date > end_date:
        raise False

    months = []
    current_date = start_date

    while current_date <= end_date:
        months.append(current_date.strftime('%Y-%m'))
        # 增加一个月
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1, day=1)

    return months


def is_greater_than_current_year_month(date_str):
    """
    判断给定日期的年月是否大于当前时间的年月

    :param date_str: 日期字符串，格式为 'YYYY-MM-DD'
    :return: 布尔值，True 表示大于当前时间的年月，False 表示不大于
    """
    given_date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    current_date = datetime.datetime.now()

    # 比较年月
    return (given_date.year, given_date.month) > (current_date.year, current_date.month)


def is_greater_than_current_time(date_str):
    """
    判断给定日期是否大于当前时间
    :return: 布尔值，True 表示大于当前时间，False 表示不大于
    """
    given_date = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    current_date = datetime.datetime.now()

    return given_date > current_date


def supplement_data(data):
    keys = [list(item.keys())[0] for item in data]
    new_result = []
    last_hour = None
    last_key = None
    last_val = None
    last_day = None
    the_end_key = keys[-1]
    for item in keys:
        hour = int(item[11:13])
        if last_hour is not None:
            if last_day != item[:10]:
                # 隔天了
                new_result.extend([last_val] * (int(24 - last_hour) * 4 - 1))
            else:
                new_result.extend([last_val] * (int(hour - last_hour) * 4 - 1))
        last_hour = hour
        last_key = item

        last_val = next((items[item] for items in data if item in items), None)
        new_result.append(last_val)
        if item == the_end_key:
            new_result.extend([last_val] * ((24 - hour) * 4 - 1))

        last_day = item[:10]
    return new_result


def is_over_by_days(begin_time_str, end_time_str, days):
    # 解析日期字符串
    begin_time = datetime.datetime.strptime(begin_time_str, '%Y-%m-%d').date()
    end_time = datetime.datetime.strptime(end_time_str, '%Y-%m-%d').date()

    # 计算时间差
    delta = (end_time - begin_time).days
    if delta > int(days):
        return True
    return False


def calculate_model_rate_by_day(target_type, model_id, station_id, station_name, data, day_str):
    """
    计算每天的模型+指标+并网点的预测准确率
    计算一天中每个时刻的准确率之后再取平均值
    """
    now = datetime.datetime.now()
    date_str = now.strftime('%Y-%m-%d')
    if day_str != date_str:
        return 0
    day_str = get_previous_day(day_str)
    if int(target_type) == 1:
        # 获取负荷实测值
        sql_condition = "time BETWEEN '{}' and '{}' and state_pcc = 0 and station = '{}' order by time asc".format(
            day_str + ' 00:00:00', day_str + ' 23:59:59', station_name)

        load_true_data = get_ads_rhyc_data_by_sql("DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, p_load, station",
                                                  'ads_report_loading_data', sql_condition)

    else:
        # 获取电量实测值
        sql_condition = "time BETWEEN '{}' and '{}' and station  = '{}' order by time asc".format(
            day_str + ' 00:00:00', day_str + ' 23:59:59', station_name)

        load_true_data = get_ads_rhyc_data_by_sql("DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, diff, station",
                                                  'ads_report_loading_chag_data', sql_condition)

    # 获取预测值
    sql_pre = "forecast_time = '{}' and is_use=1 and model_id={} and target_id={} and mstation_id={} order by forecast_hour_min asc".format(
        day_str, model_id, int(target_type), station_id
    )
    pre_data = get_dwd_rhyc_data_by_sql("forecast_time, forecast_hour_min, value", "dwd_model_forecast_value", sql_pre)

    forecast_result_dict = {
        f"{forecast_time} {(datetime.datetime.strptime(forecast_hour_min[0:5], '%H:%M')).strftime('%H:%M')}": value
        for forecast_time, forecast_hour_min, value in pre_data
    }


    # 获取所有日期时间列表
    total_dates = get_all_date_between(day_str, day_str)
    all_date_arr = {}
    for item in total_dates:
        date_arr = create_date_time_mapping(item)
        all_date_arr = {**all_date_arr, **date_arr}

    result = load_true_data
    # 转换查询结果为字典
    dict_data = {}
    for item in result:
        if item[2] in dict_data:
            dict_data[item[2]].update({item[0]: item[1]})
        else:
            dict_data[item[2]] = {item[0]: item[1]}
    true_data = []
    for item_key in dict_data.keys():
        merged_dict = {k: dict_data[item_key].get(k[0:16], all_date_arr[k]) for k in all_date_arr}
        true_data = list(map(lambda item: item[1], merged_dict.items()))

    merged_dict = {k: forecast_result_dict.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
    pre_data_list = list(map(lambda item: item[1], merged_dict.items()))

    model_target_station_rate = calculate_rate(pre_data_list, true_data)
    if model_target_station_rate == 0:
        return 0
    return round(model_target_station_rate*100, 3)


def calculate_rate(forecast_data, true_data):
    """
    计算准确率
    forecast_data:预测数据
    true_data:实际数据
    公式：单点预测准确率=1-（|负荷预测值-负荷实测值|/负荷实测值）改为
    单点预测准确率=1-abs(（负荷预测值-负荷实测值)/负荷实测值）
    """

    # 计算每个元素的准确率
    accuracy_list = []

    for pred_str, actual in zip(forecast_data, true_data):
        if pred_str == '--' or actual == '--' or actual == 0 or float(actual) < float(10):
            continue
        pred = Decimal(pred_str)
        accuracy = 1 - abs((pred - actual) / actual)
        accuracy_list.append(accuracy)

    # 计算平均准确率
    if len(accuracy_list) == 0:
        return 0
    average_accuracy = sum(accuracy_list) / len(accuracy_list)
    return average_accuracy


def get_previous_day(date_str):
    """
    获取指定日期的前一天的日期字符串。

    :param date_str: 指定日期的字符串，格式为 'YYYY-MM-DD'
    :return: 前一天的日期字符串，格式为 'YYYY-MM-DD'
    """
    # 将字符串转换为datetime对象
    date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')

    # 减去一天
    previous_date_obj = date_obj - datetime.timedelta(days=1)

    # 将datetime对象转换回字符串
    previous_date_str = previous_date_obj.strftime('%Y-%m-%d')

    return previous_date_str



