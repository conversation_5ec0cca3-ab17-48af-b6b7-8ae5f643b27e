package com.tianlu.controller;

import com.tianlu.service.MessageCenterService;
import com.tianlu.vo.MessageCenterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/messageCenter")
@RequiredArgsConstructor
@Api(tags = "消息中心管理API")
public class MessageCenterController {

    private final MessageCenterService messageCenterService;

    @GetMapping("/{id}")
    @ApiOperation("获取消息详情")
    public MessageCenterVO getMessageDetail(@PathVariable Long id) {
        return messageCenterService.getMessageDetail(id);
    }

    @PutMapping("/{id}/status")
    @ApiOperation("更新消息状态")
    public void updateMessageStatus(
            @PathVariable Long id,
            @RequestParam(required = false) Integer isRead,
            @RequestParam(required = false) Integer isHandle,
            @RequestParam(required = false) Integer isVerify) {
        messageCenterService.updateMessageStatus(id, isRead, isHandle, isVerify);
    }

    @PutMapping("/{id}/opinion")
    @ApiOperation("更新消息反馈意见")
    public void updateMessageOpinion(
            @PathVariable Long id,
            @RequestParam(required = false) String opinion,
            @RequestParam(required = false) String enOpinion) {
        messageCenterService.updateMessageOpinion(id, opinion, enOpinion);
    }
} 