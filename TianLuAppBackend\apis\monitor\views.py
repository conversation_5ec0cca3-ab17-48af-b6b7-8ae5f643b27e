import decimal
import json
import math
import random
import time
import requests
import datetime
import concurrent.futures

from apis.statistics_apis.db_link import time_range_by_cumulant
from common.constant import EMPTY_STR_LIST
from common.database_pools import dwd_tables, dwd_db_tool, ads_db_tool
from encryption.AES_symmetric_encryption import EncryptDate
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView
from common import common_response_code
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from apis.user import models
from serializers import monitor_serializers
from settings.meter_origin_values import OriginValuesDict
from tools.aly_send_smscode import Sample
from tools.count import unit_count, station_count, project_day_charge_count, unit_convert
import paho.mqtt.client as mqtt
from django.db.models import Q
from django.conf import settings
from settings.meter_settings import METER_DIC

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


# class ProjectListView(APIView):
#     """项目清单"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         user_id = request.user["user_id"]
#         station_name_include = request.query_params.get("station", None)
#         try:
#             user = models.UserDetails.objects.get(id=user_id)
#         except Exception as e:
#             return Response(
#             {
#                 "code": common_response_code.ERROR,
#                 "data": {
#                     "message": "error",
#                     "detail": "用户查询失败"
#                 },
#             }
#         )
#
#         url = "http://***********:9001/api/point/getRealtimeData"
#
#         # 主从站的逻辑
#         if station_name_include:
#             master_stations = user.master_stations.filter(
#                 userdetails__master_stations__name__contains=station_name_include).all()
#         else:
#             master_stations = user.master_stations.all()
#
#         master_stations_array = list()
#         if master_stations.exists():
#             for master_station in master_stations:
#                 master_station_dict = {
#                     "stations__id": master_station.id,
#                     "stations__station_name": master_station.name,
#                     "stations__address": '',
#                     "stations__app": '',
#                     "stations__english_name": master_station.english_name,
#                     "stations__rated_power": 0,  # 额定功率
#                     "stations__unit_number": 0,  # 储能单元数
#                     "stations__rated_power_unit": "kW",  # 额定功率单位
#                     "stations__rated_capacity_unit": "kWh",  # 额定容量单位
#                     "stations__rated_capacity": 0,  # 额定容量
#                     "stations__meter": 0,  # 电表
#                     "stations__cabinet": 0,  # 柜体
#                     "stations__pcs_number": 0,  # PCS 数量
#                     "stations__fire_fighting": 0,  # 消防
#                     "stations__battery_cluster": 0,  # 电池簇
#                     "stations__meter_count": None,  # 电表类型
#                     "stations__meter_type": None,  # 电表类型
#                     "up_limit": 0,
#                     "down_limit": 0,
#                     "stations__control_strategy": None,  # todo
#                     "stations__station_status": None,
#                     "day_charge": 0,
#                     "day_discharge": 0,
#                     "active_power": 0,
#                     "reactive_power": 0,
#                     "SOC": None,
#                     "SOH": None,
#                     "cycles": 0,
#                     "cycles_nmuber": 0,
#                     "count": 0
#                 }
#
#                 slave_stations = master_station.stationdetails_set.all()
#                 alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0,
#                                                                station__in=slave_stations).exists()
#                 master_station_Fault_list = []
#                 master_station_offline_list = []
#                 master_station_soc_list = []
#                 master_station_soh_list = []
#                 master_station_cycles_nmuber_list = []
#                 master_station_BCHCap_list = []  # 累计充电量
#                 master_station_BDHcap_list = []  # 累积放电量
#
#                 if slave_stations.exists():
#                     for slave_station in slave_stations:
#
#                         if slave_station.slave == 0 or slave_station.slave == -1:
#                             master_station_dict['stations__address'] = slave_station.address
#                             master_station_dict['stations__app'] = slave_station.app
#                             master_station_dict['stations__meter_count'] = slave_station.meter_count
#                             master_station_dict['stations__meter_type'] = slave_station.meter_type
#
#                         if slave_station.slave != 0:
#                             master_station_dict['stations__rated_power'] += float(slave_station.rated_power)
#                             master_station_dict['stations__unit_number'] += int(slave_station.unit_number)
#                             master_station_dict['stations__rated_capacity'] += float(slave_station.rated_capacity)
#                             master_station_dict['stations__meter'] += int(slave_station.meter)
#                             master_station_dict['stations__cabinet'] += int(slave_station.cabinet)
#                             master_station_dict['stations__pcs_number'] += int(slave_station.pcs_number)
#                             master_station_dict['stations__fire_fighting'] += int(slave_station.fire_fighting)
#                             master_station_dict['stations__battery_cluster'] += int(slave_station.battery_cluster)
#
#                             slave_station_meter_ins = METER_DIC[slave_station.meter_count]
#                             slave_station_up_limit = float(slave_station.rated_power)
#                             slave_station_down_limit = -float(slave_station.rated_power)
#
#                             master_station_dict['up_limit'] += slave_station_up_limit
#                             master_station_dict['down_limit'] += slave_station_down_limit
#
#                             slave_station.up_limit = slave_station_up_limit
#                             slave_station.down_limit = slave_station_down_limit
#
#                             units = models.Unit.objects.filter(station=slave_station).all()
#                             if units.exists():
#                                 slave_station_day_charge_list = []
#                                 slave_station_day_discharge_list = []
#                                 slave_station_active_power_list = []
#                                 slave_station_reactive_power_list = []
#                                 # slave_station_cycles_nmuber_list = []
#                                 # slave_station_soc_list = []
#                                 # slave_station_soh_list = []
#                                 # slave_station_BCHCap_list = []  # 累计充电量
#                                 # slave_station_BDHcap_list = []  # 累积放电量
#                                 slave_station_num = 0
#
#                                 for unit in units:
#
#                                     # 取电表初始值
#                                     if (slave_station.english_name in OriginValuesDict.keys() and unit.english_name in
#                                             OriginValuesDict[slave_station.english_name].keys()):
#                                         origin_value_dict = OriginValuesDict.get(slave_station.english_name).get(
#                                             unit.english_name)
#                                         charge_key = METER_DIC.get(slave_station.meter_count).get('charge')
#                                         discharge_key = METER_DIC.get(slave_station.meter_count).get('discharge')
#                                         origin_charge = origin_value_dict.get(slave_station.meter_count).get(
#                                             charge_key)
#                                         origin_discharge = origin_value_dict.get(slave_station.meter_count).get(
#                                             discharge_key)
#
#                                     else:
#                                         origin_charge = 0
#                                         origin_discharge = 0
#
#                                     if len(units) > 1:
#                                         slave_station_num += 1
#                                     else:
#                                         slave_station_num = ""
#                                     json_data = {
#                                         "app": slave_station.app,
#                                         "station": slave_station.english_name,
#                                         "body": [
#                                             {
#                                                 "device": unit.bms,
#                                                 "datatype": "measure",
#                                                 "totalcall": "0",
#                                                 "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
#                                             },
#                                             {
#                                                 "device": unit.pcs,
#                                                 "datatype": "measure",
#                                                 "totalcall": "0",
#                                                 "body": ["ChaD", "DisD", "P", "Q"],
#                                             },
#                                             {
#                                                 "device": unit.pcs,
#                                                 "datatype": "status",
#                                                 "totalcall": "0",
#                                                 "body": [
#                                                     "Fault",
#                                                     "alarm",
#                                                 ],
#                                             },
#                                             {
#                                                 "device": "EMS",
#                                                 "datatype": "status",
#                                                 "totalcall": "0",
#                                                 "body": ["AEnC", "AEn", "EAEn"],
#                                             },
#                                             {
#                                                 "device": unit.pcs,
#                                                 "datatype": "measure",
#                                                 "totalcall": "0",
#                                                 "body": ["PP1"],
#                                             },
#                                             {
#                                                 "device": f'{slave_station_meter_ins["device"].upper()}{slave_station_num}',
#                                                 "datatype": "cumulant",
#                                                 "totalcall": "0",
#                                                 "body": [slave_station_meter_ins["charge"], slave_station_meter_ins["discharge"]],
#                                             },
#                                             {
#                                                 "device": unit.bms,
#                                                 "datatype": "status",
#                                                 "totalcall": "0",
#                                                 "body": [
#                                                     "GFault",
#                                                     "GAlarm",
#                                                 ],
#                                             },
#                                         ],
#                                     }
#                                     response = requests.post(url=url, json=json_data)
#                                     return_dic = response.json()
#                                     # print(return_dic)
#                                     body = return_dic.get("body", None)
#                                     if not body:
#                                         error_log.error("http调用安昌数据库查询接口失败")
#                                         return Response(
#                                             {
#                                                 "code": common_response_code.ERROR,
#                                                 "data": {
#                                                     "message": "fail",
#                                                     "detail": "http调用安昌数据库查询接口失败",
#                                                 },
#                                             }
#                                         )
#
#                                     if len(body[2]["body"]) == 0:
#                                         master_station_offline_list.append(1)  # 离线状态
#
#                                     else:
#                                         GFault = body[6]["body"].get("GFault", -2)  # bms故障状态
#                                         # GAlarm = body[6]["body"].get("GAlarm", -2)  # bms警告状态
#                                         Fault = body[2]["body"].get("Fault", -2)  # pcs故障状态
#                                         # alarm = body[2]["body"].get("alarm", -2)  # pcs警告状态
#
#                                         if Fault and int(Fault) == 1:
#                                             master_station_Fault_list.append(1)
#                                         if int(GFault) == 1:
#                                             master_station_Fault_list.append(1)
#                                         if slave_station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                                             ChaD = body[0]["body"].get("ChaED", 0)  # 今日充电量
#                                             DisD = body[0]["body"].get("DisED", 0)  # 今日放电量
#                                         else:
#                                             ChaD, DisD = project_day_charge_count(slave_station, unit)
#                                         #     ChaD = body[0]["body"].get("ChaED", 0)  # 今日充电量
#                                         #     DisD = body[0]["body"].get("DisED", 0)  # 今日放电量
#
#                                         slave_station_day_charge_list.append(decimal.Decimal(ChaD))
#                                         BCHCap = abs(decimal.Decimal(body[5]["body"].get(slave_station_meter_ins["charge"], 0)))  # 累计充电量
#                                         slave_station_day_discharge_list.append(decimal.Decimal(DisD))
#                                         BDHcap = abs(decimal.Decimal(body[5]["body"].get(slave_station_meter_ins["discharge"], 0)))  # 累计放电量
#                                         if slave_station.english_name == "NBLS002" and slave_station.meter_count == 3:
#                                             BCHCap += 21007
#                                             BDHcap += 19108
#                                         master_station_BCHCap_list.append(float(decimal.Decimal(BCHCap)) - abs(origin_charge))
#                                         master_station_BDHcap_list.append(float(decimal.Decimal(BDHcap)) - abs(origin_discharge))
#
#                                         AEnC = body[3]["body"].get("AEn", 0)  # 控制策略
#                                         conn = get_redis_connection("default")
#
#                                         redis_key = str(
#                                             slave_station.english_name + "-" + slave_station.app + "_" + "ENAu")
#                                         redis_AEnC = conn.get(redis_key)
#                                         if redis_AEnC:
#                                             success_log.info("项目清单:当期数据为缓存数据")
#                                             AEnC = redis_AEnC.decode("utf-8")  # 开关状态
#                                         else:
#                                             AEnC = AEnC
#                                         slave_station.control_strategy = AEnC
#
#                                         P = body[1]["body"].get("P", 0)  # 实时功率
#
#                                         slave_station_active_power_list.append(float(P))
#                                         Q = body[1]["body"].get("Q", 0)  # 无功功率
#
#                                         slave_station_reactive_power_list.append(float(Q))
#                                         NBSC = body[0]["body"].get("NBSC", 0)  # 循环次数
#                                         SOC = body[0]["body"].get("SOC", 0)  # SOC
#                                         SOH = body[0]["body"].get("SOH", 0)  # SOH
#
#                                         master_station_cycles_nmuber_list.append(float(NBSC))
#                                         master_station_soc_list.append(float(SOC))
#                                         master_station_soh_list.append(float(SOH))
#
#
#
#                                 # from tools.count import unit_convert
#                                 slave_station.day_charge = unit_convert(int(sum(slave_station_day_charge_list)), 'kWh')  # 日充电量
#                                 slave_station.day_discharge = unit_convert(int(sum(slave_station_day_discharge_list)), 'kWh')  # 日放电量
#                                 slave_station.active_power = int(sum(slave_station_active_power_list))  # 有功功率
#                                 slave_station.reactive_power = int(sum(slave_station_reactive_power_list))  # 无功功率
#
#                                 master_station_dict['day_charge'] += slave_station.day_charge[0]
#                                 master_station_dict['day_discharge'] += slave_station.day_discharge[0]
#                                 master_station_dict['active_power'] += slave_station.active_power
#                                 master_station_dict['reactive_power'] += slave_station.reactive_power
#
#                 count = station_count(master_station_dict, sum(master_station_BCHCap_list), sum(master_station_BDHcap_list))
#                 master_station_dict['SOC'] = int(sum(master_station_soc_list) / master_station_dict['stations__unit_number']) if master_station_dict['stations__unit_number'] else 0  # soc
#                 master_station_dict['SOH'] = int(sum(master_station_soh_list) / master_station_dict['stations__unit_number']) if master_station_dict['stations__unit_number'] else 0  # soh
#                 master_station_dict['cycles'] = int(sum(master_station_cycles_nmuber_list) / master_station_dict['stations__unit_number']) if master_station_dict['stations__unit_number'] else 0  # 上报循环次数
#                 master_station_dict['cycles_nmuber'] = master_station_dict['count'] = count  # 计算循环次数
#
#                 master_station_dict['status'] = 1
#                 if alarm_exist:
#                     master_station_dict['status'] = 2
#                 if 1 in master_station_Fault_list:
#                     master_station_dict['status'] = 3
#                 if 1 in master_station_offline_list:
#                     master_station_dict['status'] = 4
#
#                 master_station_dict['stations__station_status'] = master_station_dict['status']
#
#                 # 响应格式还原原接口
#                 master_station_dict['day_charge'] = [master_station_dict['day_charge'], 'kWh']
#                 master_station_dict['day_discharge'] = [master_station_dict['day_discharge'], 'kWh']
#
#                 master_stations_array.append(master_station_dict)
#
#             # stations_obj += master_stations_array
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": master_stations_array,
#                 },
#             }
#         )


# class ControlSendSmsCodeView(APIView):
#     """就地控制发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("就地控制发送短信:参数校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("就地控制发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set("control" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class PowerSendSmsCodeView(APIView):
#     """全站功率发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("全站功率发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("全站功率发送短信:短信发送失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         """目前没有接入第三方发短信程序"""
#         conn = get_redis_connection("default")
#         conn.set("power" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class PowerPlanSendSmsCodeView(APIView):
#     """功率计划发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("功率计划发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("功率计划发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set("power_plan" + str(ser.validated_data["mobile"]), random_sms_code,
#                  ex=60 * 5)  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class ControlSMSCodeCheckView(APIView):
#     """就地控制下发指令"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.ControlCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("就地控制下发指令:参数校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("control" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         master_station_name = ser.validated_data["strategy"]
#         control_strategy = ser.validated_data["control_strategy"]
#         unit_id = ser.validated_data.get('unit_id', None)
#
#         # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
#         # app = app.get("app")
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         if not unit_id:
#             slave_station = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=0) | Q(slave=-1)).first()
#             topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
#             secret_key = settings.AES_KEY
#             aes = EncryptDate(secret_key)
#             token = aes.encrypt(slave_station.english_name)
#             message = {
#                 "time": str(int(time.time())),
#                 "token": token,
#                 "device": "EMS",
#                 "body": [
#                     # {"ENAu": str(control_strategy), "type": "parameter"},
#                     {"EAEn": str(control_strategy), "type": "parameter"}
#                 ],
#             }
#             client = mqtt.Client()
#             client.on_connect = on_connect
#             client.on_message = on_message
#             client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#             client.connect(
#                 host=settings.MQTT_SERVER,
#                 port=settings.MQTT_PORT,
#                 keepalive=settings.MQTT_KEEPALIVE,
#             )
#
#             json_message = json.dumps(message)
#             client.publish(topic, json_message)
#             success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
#             conn = get_redis_connection("default")
#             redis_key = str(slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
#             success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
#             conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)
#
#         else:
#             units = models.Unit.objects.filter(is_delete=0, id=unit_id, station__master_station__english_name=master_station_name)
#             if not units.exists():
#                 return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"单元{unit_id}不存在!",
#                     },
#                 }
#             )
#             unit = units.first()
#             topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
#             secret_key = settings.AES_KEY
#             aes = EncryptDate(secret_key)
#             token = aes.encrypt(unit.station.english_name)
#             message = {
#                 "time": str(int(time.time())),
#                 "token": token,
#                 "device": "EMS",
#                 "body": [
#                     # {"ENAu": str(control_strategy), "type": "parameter"},
#                     {"EAEn": str(control_strategy), "type": "parameter"}
#                 ],
#             }
#             client = mqtt.Client()
#             client.on_connect = on_connect
#             client.on_message = on_message
#             client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#             client.connect(
#                 host=settings.MQTT_SERVER,
#                 port=settings.MQTT_PORT,
#                 keepalive=settings.MQTT_KEEPALIVE,
#             )
#
#             json_message = json.dumps(message)
#             client.publish(topic, json_message)
#             success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
#             conn = get_redis_connection("default")
#             redis_key = str(unit.station.english_name + "-" + unit.station.app + "-" + "AEn")
#             success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
#             conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)
#
#         request_dic = {
#             "app": unit.station.app if unit_id else slave_station.app,
#             "time": str(time.time()),
#             "station": unit.station.english_name if unit_id else slave_station.english_name,
#             "body": [
#                 {
#                     "device": "BMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "EMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["AEnC", "AEn", "PRun", "PStse"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#             ],
#         }
#         url = "http://***********:9001/api/point/realtimeDataRelease"
#         time.sleep(5)
#         response = requests.post(url=url, json=request_dic)
#         code = response.json().get("code", "-1")
#         if str(code) != "200":
#             error_log.error("就地控制下发指令:最新数据拉取失败")
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "就地控制下发指令:最新数据拉取失败",
#                     },
#                 }
#             )
#         success_log.info("就地控制下发指令:最新数据拉取成功")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "控制策略下发成功",
#                 },
#             }
#         )


# class PowerSMSCodeCheckView(APIView):
#     """全站功率下发指令"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.PowerCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("全站功率下发指令:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("全站功率下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         master_station_name = ser.validated_data["strategy"]
#         power = ser.validated_data["power"]
#         # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
#         # app = app.get("app")
#
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         slave_station = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=0) | Q(slave=-1)).first()
#
#         topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#         token = aes.encrypt(slave_station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": "EMS",
#             "body": [{"EFPR": str(power), "type": "parameter"}],
#         }
#         success_log.info(f"全站功率下发指令:下发参数为{json.dumps(message)}")
#         json_message = json.dumps(message)
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#         client.publish(topic, json_message)
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#         request_dic = {
#             "app": slave_station.app,
#             "time": str(time.time()),
#             "station": slave_station.english_name,
#             "body": [
#                 {
#                     "device": "BMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "EMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["AEnC", "AEn", "PRun", "PStse"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#             ],
#         }
#         url = "http://***********:9001/api/point/realtimeDataRelease"
#         time.sleep(10)
#         response = requests.post(url=url, json=request_dic)
#         code = response.json().get("code", "-1")
#         if str(code) != "200":
#             error_log.error("全站功率下发指令:最新数据拉取失败")
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "控制策略下发失败,最新数据拉取失败",
#                     },
#                 }
#             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "功率指标下发成功",
#                 },
#             }
#         )


# class PowerPlanSMSCodeCheckView(APIView):
#     """计划下发下发指令"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.PowerPlanCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("计划下发下发指令:参数校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("power_plan" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#
#         if not user_instance:
#             error_log.error("计划下发下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         master_station_name = ser.validated_data["strategy"]
#         power_plans = ser.validated_data["power_plan"]
#         # station = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).first()
#
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
#
#         object_lists = []
#         for plan in power_plans:
#             history_instance = models.StationPlanHistory(
#                 user_id=request.user["user_id"],
#                 station=master_station,
#                 start_time=plan["start_time"],
#                 end_time=plan["end_time"],
#                 power_follow=plan["power_follow"],
#                 power=plan["power"],
#             )
#             object_lists.append(history_instance)
#         models.StationPlanHistory.objects.bulk_create(object_lists)  # 插入数据
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "功率计划下发成功",
#                 },
#             }
#         )


# class PowerPlanHistoryView(APIView):
#     """计划下发历史查询"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.StationNameSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("计划下发历史查询:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         user_id = request.user["user_id"]
#         master_station_name = ser.validated_data["strategy"]
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
#
#         history_instances = (
#             models.StationPlanHistory.objects.filter(user_id=user_id,
#                                                      station=master_station)
#             .values("start_time", "end_time", "power_follow", "power", "status", "unique")
#             .order_by("start_time")
#         )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": history_instances,
#                 },
#             }
#         )


# class UintView(APIView):
#     """储能单元清单"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.StationNameSerializer(data=request.data)
#
#         if not ser.is_valid():
#             error_log.error("储能单元清单:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         user_id = request.user["user_id"]
#         user_instance = models.UserDetails.objects.filter(id=user_id).first()
#
#         master_station_instances = models.MaterStation.objects.filter(userdetails=user_instance, is_delete=0,
#                                                                       english_name=ser.validated_data["strategy"]).all()
#
#         detail = []
#         if master_station_instances.exists():
#             master_station_instance = master_station_instances.first()
#             # is_v3 = master_station_instance.is_v3
#             slave_stations = master_station_instance.stationdetails_set.filter(is_delete=0).all()
#             for slave_station in slave_stations:
#                 english_name = slave_station.english_name
#                 meter_count = slave_station.meter_count
#                 meter_ins = METER_DIC.get(meter_count)
#                 app = slave_station.app
#                 unit_data = models.Unit.objects.filter(is_delete=0, station=slave_station).values(
#                     "id",
#                     "unit_name",
#                     "english_name",
#                     "unit_new_name",
#                     "bms",
#                     "pcs",
#                     "rated_power",
#                     "rated_power_unit",
#                     "rated_capacity",
#                     "rated_capacity_unit",
#                     "meter",
#                     "pcs_number",
#                     "fire_fighting",
#                     "cabinet",
#                     "battery_cluster",
#                     "station__rated_capacity",
#                     "v_number"
#                 )
#                 # 状态待优化成中文
#                 # url = "http://***********:9001/api/point/getRealtimeData"
#                 num = 0
#
#                 for unit_ in unit_data:
#                     if (slave_station.english_name in OriginValuesDict.keys() and unit_['english_name'] in
#                             OriginValuesDict[slave_station.english_name].keys()):
#                         # 取电表初始值
#                         origin_value_dict = OriginValuesDict.get(slave_station.english_name).get(
#                             unit_['english_name'])
#                         charge_key = METER_DIC.get(slave_station.meter_count).get('charge')
#                         discharge_key = METER_DIC.get(slave_station.meter_count).get('discharge')
#                         origin_charge = origin_value_dict.get(slave_station.meter_count).get(
#                             charge_key)
#                         origin_discharge = origin_value_dict.get(slave_station.meter_count).get(
#                             discharge_key)
#
#                     else:
#                         origin_charge = 0
#                         origin_discharge = 0
#
#                     Fault_list = []
#                     offline_list = []
#                     q_ins = Q(device=unit_["pcs"]) | Q(device=unit_["bms"])
#                     alarm_exists = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0,
#                                                                     station_id=slave_station.id).filter(
#                         q_ins).exists()
#                     if len(unit_data) > 1:
#                         num += 1
#                     else:
#                         num = ""
#                     up_limit = float(unit_["rated_power"])
#                     down_limit = -float(unit_["rated_power"])
#                     unit_["up_limit"] = up_limit
#                     unit_["down_limit"] = down_limit
#                     json_data = {
#                         "app": app,
#                         "station": english_name,
#                         "body": [
#                             {
#                                 "device": unit_["bms"],
#                                 "datatype": "measure",
#                                 "totalcall": "0",
#                                 "body": ["NBSC", "ChaED", "DisED"],
#                             },
#                             {
#                                 "device": unit_["bms"],
#                                 "datatype": "status",
#                                 "totalcall": "0",
#                                 "body": ["SQse", "GFault"],
#                             },
#                             {
#                                 "device": unit_["pcs"],
#                                 "datatype": "measure",
#                                 "totalcall": "0",
#                                 "body": [
#                                     "ChaD",
#                                     "DisD",
#                                     "P",
#                                 ],
#                             },
#                             {
#                                 "device": unit_["pcs"],
#                                 "datatype": "status",
#                                 "totalcall": "0",
#                                 "body": [
#                                     "Fault",
#                                     "alarm",
#                                     "PCStu",
#                                 ],
#                             },
#                             {
#                                 "device": "EMS",
#                                 "datatype": "status",
#                                 "totalcall": "0",
#                                 "body": ["PRun", "PStse"],
#                             },
#                             {
#                                 "device": f'{meter_ins["device"].upper()}{num}',
#                                 "datatype": "cumulant",
#                                 "totalcall": "0",
#                                 "body": [meter_ins["charge"], meter_ins["discharge"]],
#                             },
#                         ],
#                     }
#                     # response = requests.post(url=url, json=json_data)
#                     # return_dic = response.json()
#                     # body = return_dic.get("body", None)
#
#                     conn = get_redis_connection("3")
#
#                     key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name,
#                                                                    unit_['bms'])
#                     measure_bms = conn.get(key1)
#                     if measure_bms:
#                         measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
#                     else:
#                         measure_bms_dict = {}
#
#                     key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name,
#                                                                    unit_['pcs'])
#                     measure_pcs = conn.get(key2)
#                     if measure_pcs:
#                         measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
#                     else:
#                         measure_pcs_dict = {}
#
#                     key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name,
#                                                                    unit_['pcs'])
#                     status_pcs = conn.get(key3)
#                     if status_pcs:
#                         status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
#                     else:
#                         status_pcs_dict = {}
#
#                     key4 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name, 'EMS')
#                     status_ems = conn.get(key4)
#                     if status_ems:
#                         status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
#                     else:
#                         status_ems_dict = {}
#
#                     key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', slave_station.english_name,
#                                                                    f'{meter_ins["device"].upper()}{num}')
#                     cumulant_ = conn.get(key5)
#                     if cumulant_:
#                         cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
#                     else:
#                         cumulant_dict = {}
#
#                     key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name, unit_['bms'])
#                     status_bms = conn.get(key6)
#                     if status_bms:
#                         status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
#                     else:
#                         status_bms_dict = {}
#
#                     if not status_pcs_dict or not status_bms_dict:
#                         unit_["status"] = 4
#                         offline_list.append(1)  # 离线状态
#                         # unit_["stations__station_status"] = 4  # 离线状态
#                         conn = get_redis_connection("default")
#                         # unit_['is_v3'] = unit_['v_number']
#
#                         redis_key_ = str(english_name + "_" + app + "_" + unit_["pcs"] + "_" + "NSIMO")
#                         redis_NSIMO = conn.get(redis_key_)
#                         if redis_NSIMO:
#                             success_log.info("储能单元清单:当前数据为缓存数据")
#                             NSIMO = redis_NSIMO.decode("utf-8")  # 运行状态
#                         else:
#                             NSIMO = 0
#
#                         unit_['running_type'] = NSIMO or (int(status_pcs_dict.get('ASTIMN')) if status_pcs_dict.get('ASTIMN') and status_pcs_dict.get('ASTIMN') not in EMPTY_STR_LIST else 0)
#                     else:
#                         Fault = status_pcs_dict.get("Fault") if status_pcs_dict.get("Fault") and status_pcs_dict.get("Fault") not in EMPTY_STR_LIST else -2  # 故障状态
#                         GFault = status_bms_dict.get("GFault") if status_bms_dict.get("GFault") and status_bms_dict.get("GFault") not in EMPTY_STR_LIST else -2  # 故障状态
#                         if Fault == 1 or Fault == '1':
#                             Fault_list.append(1)
#                         if GFault == 1 or GFault == '1':
#                             Fault_list.append(1)
#
#                         unit_["status"] = 1
#                         if alarm_exists:
#                             unit_["status"] = 2
#                         if 1 in Fault_list:
#                             unit_["status"] = 3
#                         if 1 in offline_list:
#                             unit_["status"] = 4
#
#                         # PRun = body[4]['body'].get("PRun", None)  # 开关闭合状态
#                         # PStse = body[4]['body'].get("PStse", None)  # 开关关闭状态
#                     PStse = status_pcs_dict.get("PCStu", None)  # 开关关闭状态
#                     # print("PCStu", PStse)
#                     # PStse = body[4]['body'].get("PStse", None)  # 开关开启状态
#                     SQse = 1
#                     # if PRun == 1:
#                     #     SQse = 0
#                     if str(PStse) == "1":
#                         SQse = 0
#
#                     conn = get_redis_connection("default")
#                     redis_key = str(english_name + "_" + app + "_" + unit_["pcs"] + "_" + "EFPR")
#                     redis_SQse = conn.get(redis_key)
#                     if redis_SQse:
#                         success_log.info("储能单元清单:当前数据为缓存数据")
#                         SQse = redis_SQse.decode("utf-8")  # 开关状态
#                     else:
#                         SQse = SQse
#                     unit_["unit_status"] = SQse
#
#                     # unit_['is_v3'] = is_v3
#
#                     redis_key_ = str(english_name + "_" + app + "_" + unit_["pcs"] + "_" + "NSIMO")
#                     redis_NSIMO = conn.get(redis_key_)
#                     if redis_NSIMO:
#                         success_log.info("储能单元清单:当前数据为缓存数据")
#                         NSIMO = redis_NSIMO.decode("utf-8")  # 运行状态
#                     else:
#                         NSIMO = None
#
#                     unit_['running_type'] = NSIMO if NSIMO else (int(status_pcs_dict.get('ASTIMN')) if status_pcs_dict.get('ASTIMN') else 0)
#
#                     if slave_station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                         ChaD = int(decimal.Decimal(measure_bms_dict.get("ChaED", 0)))  # 今日充电量
#                         DisD = int(decimal.Decimal(measure_bms_dict.get("DisED", 0)))  # 今日放电量
#                     else:
#                         ChaD, DisD = project_day_charge_count(slave_station, unit_['bms'])
#
#                     unit_["day_charge"] = ChaD
#                     unit_["day_discharge"] = DisD
#                     P = measure_pcs_dict.get("P", 0)  # 实时功率
#                     unit_["now_power"] = P
#                     BCHCap = abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["charge"], 0)))) - abs(
#                         origin_charge)  # 累计充电量
#                     BDHcap = abs(float(decimal.Decimal(cumulant_dict.get(meter_ins["discharge"], 0)))) - abs(
#                         origin_discharge)  # 累计放电量
#                     if english_name == "NBLS002" and meter_count == 3:
#                         BCHCap += 21007
#                         BDHcap += 19108
#                     NBSC = unit_count(unit_, BCHCap, BDHcap)
#                     # NBSC = body[0]["body"].get("NBSC", None)  # 循环次数
#                     unit_["cycles_nmuber"] = int(NBSC)
#                     unit_['unit_name'] = unit_['unit_new_name']
#
#                 detail += unit_data
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": detail,
#                 },
#             }
#         )


# class UnitSwitchSendSmsCodeView(APIView):
#     """PCS开关发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("PCS开关发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("PCS开关发送短信:短信验证码下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set(
#             "unit_switch" + str(ser.validated_data["mobile"]),
#             random_sms_code,
#             ex=60 * 5,
#         )  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class UnitSwitchRunningTypeSendSmsCodeView(APIView):
#     """切换运行类型发送短信"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.query_params)
#         if not ser.is_valid():
#             error_log.error("切换运行类型发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("切换运行类型发送短信:短信验证码下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set(
#             "unit_switch_running_type" + str(ser.validated_data["mobile"]),
#             random_sms_code,
#             ex=60 * 5,
#         )  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class UnitSwitchSMSCodeCheckView(APIView):
#     """PCS开关下发指令"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.UnitSwitchCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("PCS开关下发指令:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("unit_switch" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("PCS开关下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         switch_dic = {
#             1: "CDSta",  # 启动
#             0: "CDSto",  # 停止
#         }
#         switch = ser.validated_data["switch"]
#         switch = switch_dic.get(int(switch))
#         # storage_name = ser.validated_data["strategy"]
#         # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
#         # app = app.get("app")
#
#         unit_ins = models.Unit.objects.get(english_name=ser.validated_data["unit"])
#         topic = f"req/database/parameter/{unit_ins.station.english_name}/{unit_ins.station.app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#
#         pcs = unit_ins.pcs
#         token = aes.encrypt(unit_ins.station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": pcs,
#             "body": [{switch: "1", "type": "parameter"}],
#         }
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#         json_message = json.dumps(message)
#         client.publish(topic, json_message)
#         success_log.info(f"PCS开关下发指令:下发指令{json_message}")
#         conn = get_redis_connection("default")
#         redis_key = str(unit_ins.station.english_name + "_" + unit_ins.station.app + "_" + pcs + "_" + "EFPR")
#         conn.set(redis_key, str(ser.validated_data["switch"]), ex=150)
#         success_log.info("PCS开关下发指令:写入缓存")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#
#         request_dic = {
#             "app": unit_ins.station.app,
#             "time": str(time.time()),
#             "station": unit_ins.station.english_name,
#             "body": [
#                 {
#                     "device": "BMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu"],
#                 },
#                 {
#                     "device": "EMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["AEnC", "AEn", "PRun", "PStse"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#             ],
#         }
#         url = "http://***********:9001/api/point/realtimeDataRelease"
#         if str(ser.validated_data["switch"]) == "1":
#             time.sleep(50)
#         else:
#             time.sleep(3)
#         response = requests.post(url=url, json=request_dic)
#         code = response.json().get("code", "-1")
#         if str(code) != "200":
#             error_log.error("PCS开关下发指令:最新数据拉取失败")
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "PCS开关下发指令,最新数据拉取失败",
#                     },
#                 }
#             )
#         success_log.info("PCS开关下发指令:最新数据拉取成功")
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "PCS开关下发指令成功",
#                 },
#             }
#         )


# class UnitSwitchRunningTypeView(APIView):
#     """切换运行类型下发指令"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.UnitSwitchSwitchRunningTypeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("切换运行类型下发指令:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("unit_switch_running_type" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("切换运行类型下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#
#         target_running_type = ser.validated_data["running_type"]
#
#         unit_ins = models.Unit.objects.get(english_name=ser.validated_data["unit"])
#         topic = f"req/database/parameter/{unit_ins.station.english_name}/{unit_ins.station.app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#
#         pcs = unit_ins.pcs
#         token = aes.encrypt(unit_ins.station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": pcs,
#             "body": [{'NSIMO': str(target_running_type), "type": "parameter"}],
#         }
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#         json_message = json.dumps(message)
#         client.publish(topic, json_message)
#         success_log.info(f"切换运行状态下发指令:下发指令{json_message}")
#         conn = get_redis_connection("default")
#         redis_key = str(unit_ins.station.english_name + "_" + unit_ins.station.app + "_" + pcs + "_" + "NSIMO")
#         conn.set(redis_key, str(target_running_type), ex=300)
#         success_log.info("切换运行状态下发指令:写入缓存")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#
#         request_dic = {
#             "app": unit_ins.station.app,
#             "time": str(time.time()),
#             "station": unit_ins.station.english_name,
#             "body": [
#                 {
#                     "device": "BMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
#                 },
#                 {
#                     "device": "EMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["AEnC", "AEn", "PRun", "PStse"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#             ],
#         }
#         url = "http://***********:9001/api/point/realtimeDataRelease"
#         if str(target_running_type) == "1":
#             time.sleep(10)
#         else:
#             time.sleep(3)
#         response = requests.post(url=url, json=request_dic)
#         code = response.json().get("code", "-1")
#         if str(code) != "200":
#             error_log.error("切换运行状态下发指令:最新数据拉取失败")
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "切换运行状态下发指令,最新数据拉取失败",
#                     },
#                 }
#             )
#         success_log.info("切换运行状态下发指令:最新数据拉取成功")
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "切换运行状态下发指令成功",
#                 },
#             }
#         )


# class UnitPowerSendSmsCodeView(APIView):
#     """pcs功率发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("pcs功率发送短信:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("pcs功率发送短信:短信验证码下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set("unit_power" + str(ser.validated_data["mobile"]), random_sms_code,
#                  ex=60 * 5)  # redis 数据格式 monitor_mobile
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class UnitPowerSMSCodeCheckView(APIView):
#     """pcs功率下发"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.UnitPowerCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("pcs功率下发:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("unit_power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("pcs功率下发:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         # storage_name = ser.validated_data["strategy"]
#         power = ser.validated_data["power"]
#         # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
#         # app = app.get("app")
#         unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
#         pcs = unit.pcs
#         topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#         token = aes.encrypt(unit.station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": pcs,
#             "body": [{"APS": str(power * 10), "type": "parameter"}],
#         }
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#         json_message = json.dumps(message)
#         client.publish(topic, json_message)
#         success_log.info(f"pcs功率下发:下发参数{json_message}")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#         request_dic = {
#             "app": unit.station.app,
#             "time": str(time.time()),
#             "station": unit.station.english_name,
#             "body": [
#                 {
#                     "device": "BMS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["NBSC", "BQ", "ChaED", "DisED"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["ChaD", "DisD", "P", "Q"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["Fault", "alarm"],
#                 },
#                 {
#                     "device": "EMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["AEnC", "AEn", "PRun", "PStse"],
#                 },
#                 {
#                     "device": "PCS",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS1",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS2",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "PCS3",
#                     "datatype": "measure",
#                     "totalcall": "0",
#                     "body": ["PP1"],
#                 },
#                 {
#                     "device": "BMS",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS1",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS2",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#                 {
#                     "device": "BMS3",
#                     "datatype": "status",
#                     "totalcall": "0",
#                     "body": ["SQse"],
#                 },
#             ],
#         }
#         url = "http://***********:9001/api/point/realtimeDataRelease"
#         time.sleep(10)
#         response = requests.post(url=url, json=request_dic)
#         code = response.json().get("code", "-1")
#         if str(code) != "200":
#             error_log.error("pcs功率下发:最新数据拉取失败")
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "pcs功率下发,最新数据拉取失败",
#                     },
#                 }
#             )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "pcs功率下发下发成功",
#                 },
#             }
#         )


# class StationDetailsView(APIView):
#     """站详情页"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         station_name = request.query_params.get("station", None)
#         if not station_name:
#             error_log.error("站详情页:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": {"station": ["改字段是必传项"]}},
#                 }
#             )
#         request_id = request.user["user_id"]
#         station_instances = (
#             models.StationDetails.objects.filter(is_delete=0, english_name=station_name, userdetails__id=request_id, slave=-1)
#             .values(
#                 "meter",
#                 "specifications",
#                 "pcs_number",
#                 "fire_fighting",
#                 "cabinet",
#                 "battery_cluster",
#             )
#         )
#
#         if station_instances.exists():
#             station_instance = station_instances.first()
#         else:
#             master_stations = models.MaterStation.objects.filter(english_name=station_name, userdetails__id=request_id,
#                                                                  is_delete=0)
#             if master_stations.exists():
#                 master_station = master_stations.first()
#                 meter = 0
#                 specifications = 0
#                 pcs_number = 0
#                 fire_fighting = 0
#                 cabinet = 0
#                 battery_cluster = 0
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).all().filter(slave__exact=0)
#                 for slave_station in slave_stations:
#                     meter += int(slave_station.meter)
#                     specifications += int(slave_station.specification)
#                     pcs_number += int(slave_station.pcs_number)
#                     fire_fighting += int(slave_station.fire_fighting)
#                     cabinet += int(slave_station.cabinet)
#                     battery_cluster += int(slave_station.battery_cluster)
#
#                 station_instance = {
#                     "meter": meter,
#                     "specifications": specifications,
#                     "pcs_number": pcs_number,
#                     "fire_fighting": fire_fighting,
#                     "cabinet": cabinet,
#                     "battery_cluster": battery_cluster
#                 }
#
#             else:
#                 station_instance = {}
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": station_instance,
#                 },
#             }
#         )


# class UnitDetailsView(APIView):
#     """单元详情页"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         station_name = request.query_params.get("station", None)
#         if not station_name:
#             error_log.error("单元详情页:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": {"station": ["该字段是必传项"]}},
#                 }
#             )
#         request_id = request.user["user_id"]
#         station_instances = (
#             models.StationDetails.objects.filter(is_delete=0, english_name=station_name, userdetails__id=request_id, slave=-1)
#             .values(
#                 "meter",
#                 "specifications",
#                 "pcs_number",
#                 "fire_fighting",
#                 "cabinet",
#                 "battery_cluster",
#             )
#         )
#         if station_instances.exists():
#             station_instance = station_instances.first()
#         else:
#             master_stations = models.MaterStation.objects.filter(english_name=station_name, userdetails__id=request_id,
#                                                                  is_delete=0)
#             if master_stations.exists():
#                 master_station = master_stations.first()
#                 meter = 0
#                 specifications = 0
#                 pcs_number = 0
#                 fire_fighting = 0
#                 cabinet = 0
#                 battery_cluster = 0
#                 slave_stations = master_station.stationdetails_set.filter(is_delete=0).all().filter(slave__exact=0)
#                 for slave_station in slave_stations:
#                     meter += int(slave_station.meter)
#                     specifications += int(slave_station.specification)
#                     pcs_number += int(slave_station.pcs_number)
#                     fire_fighting += int(slave_station.fire_fighting)
#                     cabinet += int(slave_station.cabinet)
#                     battery_cluster += int(slave_station.battery_cluster)
#
#                 station_instance = {
#                     "meter": meter,
#                     "specifications": specifications,
#                     "pcs_number": pcs_number,
#                     "fire_fighting": fire_fighting,
#                     "cabinet": cabinet,
#                     "battery_cluster": battery_cluster
#                 }
#
#             else:
#                 station_instance = {}
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": station_instance,
#                 },
#             }
#         )


# class PowerPlanHistoryEditView(APIView):
#     """计划下发历史记录修改"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.PowerPlanHistoryEditSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("计划下发历史记录修改:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         user_ins = models.UserDetails.objects.filter(id=request.user["user_id"]).first()
#
#         master_station_name = ser.validated_data["strategy"]
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
#
#         plan_history_ins = models.StationPlanHistory.objects.filter(
#             status=1,
#             station__userdetails=user_ins,
#             unique=ser.validated_data["unique"],
#             station=master_station,
#         ).first()
#         if not plan_history_ins:
#             error_log.error("计划下发历史记录修改:历史记录不存在或已下发")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "历史记录不存在或已下发"},
#                 }
#             )
#         plan_history_ins.power = ser.validated_data["power"]
#         plan_history_ins.save()
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "控制策略修改成功",
#                 },
#             }
#         )


# class VirtuallySendSmsCodeView(APIView):
#     """需量下发发送短信"""
#
#     # authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("需量下发发送短信:参数校验不通过")
#             if 'mobile' in ser.errors:
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": ser.errors["mobile"][0]},
#                     }
#                 )
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("就地控制发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set("virtually" + str(ser.validated_data["mobile"]), random_sms_code,
#                  ex=60 * 5)  # redis 数据格式 monitor_mobile
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class VirtuallySMSCodeCheckView(APIView):
#     """需量下发"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.VirtuallyCheckSMSCodeSerializer(data=request.data)
#         if not ser.is_valid():
#             msg = ''
#             if 'mobile' in ser.errors:
#                 if '必填项' in ser.errors["mobile"][0]:
#                     msg = "手机号是必填项,"
#                 else:
#                     msg = ser.errors["mobile"][0]+","
#             if 'code' in ser.errors:
#                 if '必填项' in ser.errors["code"][0]:
#                     msg += "验证码是必填项,"
#                 else:
#                     msg += ser.errors["code"][0]+","
#             if 'strategy' in ser.errors:
#                 if '必填项' in ser.errors["strategy"][0]:
#                     msg += "并网点站名是必填项,"
#                 else:
#                     msg += ser.errors["strategy"][0]+","
#             if 'virtually' in ser.errors:
#                 if '必填项' in ser.errors["virtually"][0]:
#                     msg += "下发需量是必填项,"
#                 else:
#                     msg += ser.errors["virtually"][0]+","
#             error_log.error(f"需量下发:参数校验不通过{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": msg.rstrip(',')},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("virtually" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#         master_station_name = ser.validated_data["strategy"]
#         # app = models.StationDetails.objects.filter(english_name=storage_name).values("app").first()
#         # app = app.get("app")
#
#         master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
#         if not master_stations.exists():
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"主站{master_station_name}不存在!",
#                     },
#                 }
#             )
#         master_station = master_stations.first()
#
#         slave_station = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=0) | Q(slave=-1)).first()
#
#         topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
#         virtually = ser.validated_data["virtually"]
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#         token = aes.encrypt(slave_station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": "EMS",
#             "body": [
#                 # {"ENAu": str(control_strategy), "type": "parameter"},
#                 {"FixDemandRef": str(virtually), "type": "parameter"}
#             ],
#         }
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#         client.publish(topic, json_message)
#         success_log.info(f"需量下发:下发参数为{json.dumps(message)}")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "需量下发下发成功",
#                 },
#             }
#         )


# class ResetFaultSendSmsCodeView(APIView):
#     """故障复位下发发送短信"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("故障复位下发发送短信:参数校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         random_sms_code = random.randint(100000, 999999)
#         code = Sample.main(ser.validated_data["mobile"], random_sms_code)
#         if code != 200:
#             error_log.error("故障复位下发发送短信:短信下发失败")
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "error", "detail": "短信验证码下发失败"},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.set("reset" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
#                 },
#             }
#         )


# class ResetFaultSMSCodeCheckView(APIView):
#     """故障复位下发"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         ser = monitor_serializers.ResetFaultSMSCodeCheckSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"故障复位下发:参数校验不通过{ser.errors}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("reset" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("故障复位下发:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "手机号与当前登录用户名不符",
#                     },
#                 }
#             )
#
#         # master_station_name = ser.validated_data["strategy"]
#         # app = models.StationDetails.objects.filter(english_name=storage_name).values("app").first()
#         # app = app.get("app")
#         try:
#             unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
#         except Exception as e:
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": f"储能单元不存在！",
#                     },
#                 }
#             )
#         topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#
#         pcs = unit.pcs
#         token = aes.encrypt(unit.station.english_name)
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": pcs,
#             "body": [{"REFau": "1", "type": "parameter"}],
#         }
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#         json_message = json.dumps(message)
#         client.publish(topic, json_message)
#         success_log.info(f"故障复位下发:下发指令{json_message}")
#         models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "故障复位下发指令成功",
#                 },
#             }
#         )


# class ProjectListNewView(APIView):
#     """项目清单"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_master_station_data(self, master_station, first_day_of_year, first_day_of_month, current_date):
#         # url = "http://***********:9001/api/point/getRealtimeData"
#         master_station_dict = {
#             "stations__id": master_station.id,
#             "stations__station_name": master_station.name,
#             "stations__address": '',
#             "stations__app": '',
#             "stations__english_name": master_station.english_name,
#             "stations__rated_power": 0,  # 额定功率
#             "stations__unit_number": 0,  # 储能单元数
#             "stations__rated_power_unit": "kW",  # 额定功率单位
#             "stations__rated_capacity_unit": "kWh",  # 额定容量单位
#             "stations__rated_capacity": 0,  # 额定容量
#             "stations__meter": 0,  # 电表
#             "stations__cabinet": 0,  # 柜体
#             "stations__pcs_number": 0,  # PCS 数量
#             "stations__fire_fighting": 0,  # 消防
#             "stations__battery_cluster": 0,  # 电池簇
#             "stations__meter_count": None,  # 电表类型
#             "stations__meter_type": None,  # 电表类型
#             "up_limit": 0,
#             "down_limit": 0,
#             "stations__control_strategy": None,  # todo
#             "stations__station_status": None,
#             "day_charge": 0,
#             "day_discharge": 0,
#             "active_power": 0,
#             "reactive_power": 0,
#             "SOC": None,
#             "SOH": None,
#             "cycles": 0,
#             "cycles_nmuber": 0,
#             "count": 0,
#             "month_charge": 0,
#             "month_discharge": 0,
#             "year_charge": 0,
#             "year_discharge": 0,
#             "BPCE": 0,
#             "BPDE": 0
#         }
#
#         slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
#         slave_stations_ids = [s.id for s in slave_stations]
#         alarm_exist = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0,
#                                                                              station_id__in=slave_stations_ids).exists()
#         master_station_Fault_list = []
#         master_station_offline_list = []
#         master_station_soc_list = []
#         master_station_soh_list = []
#         master_station_cycles_nmuber_list = []
#         master_station_BCHCap_list = []  # 累计充电量
#         master_station_BDHcap_list = []  # 累积放电量
#
#         master_station_month_charge_list = []
#         master_station_month_discharge_list = []
#         master_station_year_charge_list = []
#         master_station_year_discharge_list = []
#         master_station_BPCE_list = []
#         master_station_BPDE_list = []
#
#         temp_list = []
#         if slave_stations.exists():
#             for slave_station in slave_stations:
#
#                 if slave_station.slave == 0 or slave_station.slave == -1:
#                     master_station_dict['stations__address'] = slave_station.address
#                     master_station_dict['stations__app'] = slave_station.app
#                     master_station_dict['stations__meter_count'] = slave_station.meter_count
#                     master_station_dict['stations__meter_type'] = slave_station.meter_type
#
#                     # 查看主站及非主从站的就地控制状态
#                     conn_1 = get_redis_connection("default")
#
#                     redis_key_1 = str(
#                         slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
#                     redis_AEn = conn_1.get(redis_key_1)
#                     if redis_AEn:
#                         success_log.info("项目清单:当期数据为缓存数据")
#                         AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
#                     else:
#                         conn = get_redis_connection("3")
#                         key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name, 'EMS')
#                         measure_ems = conn.get(key1)
#                         if measure_ems:
#                             measure_ems_str = measure_ems.decode("utf-8")
#                             measure_ems_dict = json.loads(json.loads(measure_ems_str))
#
#                             if measure_ems_dict.get('AEn') and measure_ems_dict.get('AEn') not in EMPTY_STR_LIST:
#                                 AEn = int(measure_ems_dict.get('AEn'))
#                             else:
#                                 AEn = 0
#                         else:
#                             AEn = 0
#
#                     master_station_dict['stations__control_strategy'] = int(AEn)
#
#                 if slave_station.slave != 0:
#                     master_station_dict['stations__rated_power'] += float(slave_station.rated_power)
#                     master_station_dict['stations__unit_number'] += int(slave_station.unit_number)
#                     master_station_dict['stations__rated_capacity'] += float(slave_station.rated_capacity)
#                     master_station_dict['stations__meter'] += int(slave_station.meter)
#                     master_station_dict['stations__cabinet'] += int(slave_station.cabinet)
#                     master_station_dict['stations__pcs_number'] += int(slave_station.pcs_number)
#                     master_station_dict['stations__fire_fighting'] += int(slave_station.fire_fighting)
#                     master_station_dict['stations__battery_cluster'] += int(slave_station.battery_cluster)
#
#                     slave_station_meter_ins = METER_DIC[slave_station.meter_count]
#                     slave_station_up_limit = float(slave_station.rated_power)
#                     slave_station_down_limit = -float(slave_station.rated_power)
#
#                     master_station_dict['up_limit'] += slave_station_up_limit
#                     master_station_dict['down_limit'] += slave_station_down_limit
#
#                     slave_station.up_limit = slave_station_up_limit
#                     slave_station.down_limit = slave_station_down_limit
#
#                     units = models.Unit.objects.filter(is_delete=0, station=slave_station).all()
#                     # print(2504, slave_station.english_name, units)
#                     if units.exists():
#                         slave_station_day_charge_list = []
#                         slave_station_day_discharge_list = []
#                         slave_station_active_power_list = []
#                         slave_station_reactive_power_list = []
#                         # slave_station_cycles_nmuber_list = []
#                         # slave_station_soc_list = []
#                         # slave_station_soh_list = []
#                         # slave_station_BCHCap_list = []  # 累计充电量
#                         # slave_station_BDHcap_list = []  # 累积放电量
#                         slave_station_num = 0
#
#                         for unit in units:
#
#                             # 取电表初始值
#                             if (
#                                     slave_station.english_name in OriginValuesDict.keys() and unit.english_name in
#                                     OriginValuesDict[slave_station.english_name].keys()):
#                                 origin_value_dict = OriginValuesDict.get(slave_station.english_name).get(
#                                     unit.english_name)
#                                 charge_key = METER_DIC.get(slave_station.meter_count).get('charge')
#                                 discharge_key = METER_DIC.get(slave_station.meter_count).get('discharge')
#                                 origin_charge = origin_value_dict.get(slave_station.meter_count).get(
#                                     charge_key)
#                                 origin_discharge = origin_value_dict.get(slave_station.meter_count).get(
#                                     discharge_key)
#
#                             else:
#                                 origin_charge = 0
#                                 origin_discharge = 0
#
#                             if len(units) > 1:
#                                 slave_station_num += 1
#                             else:
#                                 slave_station_num = ""
#                             # json_data = {
#                             #     "app": slave_station.app,
#                             #     "station": slave_station.english_name,
#                             #     "body": [
#                             #         {
#                             #             "device": unit.bms,
#                             #             "datatype": "measure",
#                             #             "totalcall": "0",
#                             #             "body": ["NBSC", "BQ", "ChaED", "DisED", "SOC", "SOH"],
#                             #         },
#                             #         {
#                             #             "device": unit.pcs,
#                             #             "datatype": "measure",
#                             #             "totalcall": "0",
#                             #             "body": ["ChaD", "DisD", "P", "Q"],
#                             #         },
#                             #         {
#                             #             "device": unit.pcs,
#                             #             "datatype": "status",
#                             #             "totalcall": "0",
#                             #             "body": [
#                             #                 "Fault",
#                             #                 "alarm",
#                             #             ],
#                             #         },
#                             #         {
#                             #             "device": "EMS",
#                             #             "datatype": "status",
#                             #             "totalcall": "0",
#                             #             "body": ["AEnC", "AEn", "EAEn"],
#                             #         },
#                             #         {
#                             #             "device": unit.pcs,
#                             #             "datatype": "measure",
#                             #             "totalcall": "0",
#                             #             "body": ["PP1"],
#                             #         },
#                             #         {
#                             #             "device": f'{slave_station_meter_ins["device"].upper()}{slave_station_num}',
#                             #             "datatype": "cumulant",
#                             #             "totalcall": "0",
#                             #             "body": [slave_station_meter_ins["charge"],
#                             #                      slave_station_meter_ins["discharge"]],
#                             #         },
#                             #         {
#                             #             "device": unit.bms,
#                             #             "datatype": "status",
#                             #             "totalcall": "0",
#                             #             "body": [
#                             #                 "GFault",
#                             #                 "GAlarm",
#                             #             ],
#                             #         },
#                             #         {
#                             #             "device": unit.pcs,
#                             #             "datatype": "measure",
#                             #             "totalcall": "0",
#                             #             "body": ["BPCE", "BPDE"],
#                             #         }
#                             #     ],
#                             # }
#                             # response = requests.post(url=url, json=json_data)
#                             # return_dic = response.json()
#
#                             conn = get_redis_connection("3")
#
#                             key1 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name,
#                                                                            unit.bms)
#                             measure_bms = conn.get(key1)
#                             if measure_bms:
#                                 measure_bms_dict = json.loads(json.loads(measure_bms.decode("utf-8")))
#                             else:
#                                 measure_bms_dict = {}
#
#                             key2 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', slave_station.english_name,
#                                                                            unit.pcs)
#                             measure_pcs = conn.get(key2)
#                             if measure_pcs:
#                                 measure_pcs_dict = json.loads(json.loads(measure_pcs.decode("utf-8")))
#                             else:
#                                 measure_pcs_dict = {}
#
#                             key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name,
#                                                                            unit.pcs)
#                             status_pcs = conn.get(key3)
#                             if status_pcs:
#                                 status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
#                             else:
#                                 status_pcs_dict = {}
#
#                             key4 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name, 'EMS')
#                             status_ems = conn.get(key4)
#                             if status_ems:
#                                 status_ems_dict = json.loads(json.loads(status_ems.decode("utf-8")))
#                             else:
#                                 status_ems_dict = {}
#
#                             key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', slave_station.english_name,
#                                                                            f'{slave_station_meter_ins["device"].upper()}{slave_station_num}')
#                             cumulant_ = conn.get(key5)
#                             if cumulant_:
#                                 cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
#                             else:
#                                 cumulant_dict = {}
#
#                             key6 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name, unit.bms)
#                             status_bms = conn.get(key6)
#                             if status_bms:
#                                 status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
#                             else:
#                                 status_bms_dict = {}
#
#                             if not status_pcs_dict or not status_bms_dict:
#                                 master_station_offline_list.append(1)  # 离线a状态
#                                 conn_ = get_redis_connection("default")
#
#                                 redis_key_ = str(
#                                     slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
#                                 redis_AEn = conn_.get(redis_key_)
#                                 if redis_AEn:
#                                     success_log.info("项目清单:当期数据为缓存数据")
#                                     AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
#                                 else:
#                                     AEn = 0
#                                 temp_list.append({"title": unit.unit_new_name + '-就地控制（从）', "id": unit.id,
#                                                   "control_strategy": AEn})
#
#                             else:
#                                 GFault = status_bms_dict.get("GFault") if status_bms_dict.get("GFault") and status_bms_dict.get("GFault") not in EMPTY_STR_LIST else -2  # bms故障状态
#                                 # GAlarm = body[6]["body"].get("GAlarm", -2)  # bms警告状态
#                                 Fault = status_pcs_dict.get("Fault") if status_pcs_dict.get("Fault") and status_pcs_dict.get("Fault") not in EMPTY_STR_LIST else -2  # pcs故障状态
#                                 # alarm = body[2]["body"].get("alarm", -2)  # pcs警告状态
#
#                                 if Fault and int(Fault) == 1:
#                                     master_station_Fault_list.append(1)
#                                 if GFault and int(GFault) == 1:
#                                     master_station_Fault_list.append(1)
#
#                                 if slave_station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                                     ChaD = measure_bms_dict.get("ChaED") if measure_bms_dict.get("ChaED") and measure_bms_dict.get("ChaED") not in EMPTY_STR_LIST else 0  # 今日充电量
#                                     DisD = measure_bms_dict.get("DisED") if measure_bms_dict.get("DisED") and measure_bms_dict.get("DisED") not in EMPTY_STR_LIST else 0  # 今日放电量
#                                 else:
#                                     ChaD, DisD = project_day_charge_count(slave_station, unit.bms)
#                                 #     ChaD = body[0]["body"].get("ChaED", 0)  # 今日充电量
#                                 #     DisD = body[0]["body"].get("DisED", 0)  # 今日放电量
#
#                                 slave_station_day_charge_list.append(decimal.Decimal(ChaD))
#                                 BCHCap = abs(decimal.Decimal(
#                                     cumulant_dict.get(slave_station_meter_ins["charge"], 0)))  # 累计充电量
#                                 slave_station_day_discharge_list.append(decimal.Decimal(DisD))
#                                 BDHcap = abs(decimal.Decimal(
#                                     cumulant_dict.get(slave_station_meter_ins["discharge"], 0)))  # 累计放电量
#                                 if slave_station.english_name == "NBLS002" and slave_station.meter_count == 3:
#                                     BCHCap += 21007
#                                     BDHcap += 19108
#                                 master_station_BCHCap_list.append(
#                                     float(decimal.Decimal(BCHCap)) - abs(origin_charge))
#                                 master_station_BDHcap_list.append(
#                                     float(decimal.Decimal(BDHcap)) - abs(origin_discharge))
#
#                                 # AEnC = body[3]["body"].get("AEn", 0)  # 控制策略
#                                 conn_ = get_redis_connection("default")
#
#                                 redis_key_ = str(
#                                     slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
#                                 redis_AEn = conn_.get(redis_key_)
#                                 if redis_AEn:
#                                     success_log.info("项目清单:当期数据为缓存数据")
#                                     AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
#                                 else:
#                                     conn = get_redis_connection("3")
#                                     key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station.english_name,
#                                                                                    'EMS')
#                                     measure_ems_1 = conn.get(key2)
#                                     if measure_ems_1:
#                                         measure_ems_str_1 = measure_ems_1.decode("utf-8")
#                                         measure_ems_dict_1 = json.loads(json.loads(measure_ems_str_1))
#
#                                         AEn = measure_ems_dict_1.get('AEn') \
#                                             if measure_ems_dict_1.get('AEn') and measure_ems_dict_1.get('AEn') not in EMPTY_STR_LIST else 0
#                                     else:
#                                         AEn = 0
#                                     # slave_station.control_strategy = AEn
#
#                                 temp_list.append({"title": unit.unit_new_name + '-就地控制（从）', "id": unit.id,
#                                                   "control_strategy": int(AEn)})
#
#                                 P = measure_pcs_dict.get("P") if measure_pcs_dict.get("P") and measure_pcs_dict.get("P") not in EMPTY_STR_LIST else 0  # 实时功率
#
#                                 slave_station_active_power_list.append(float(P))
#                                 Q = measure_pcs_dict.get("Q") if measure_pcs_dict.get("Q") and measure_pcs_dict.get("Q") not in EMPTY_STR_LIST else 0  # 无功功率
#
#                                 slave_station_reactive_power_list.append(float(Q))
#                                 NBSC = measure_bms_dict.get("NBSC") if measure_bms_dict.get("NBSC") and measure_bms_dict.get("NBSC") not in EMPTY_STR_LIST else 0  # 循环次数
#                                 SOC = measure_bms_dict.get("SOC") if measure_bms_dict.get("SOC") and measure_bms_dict.get("SOC") not in EMPTY_STR_LIST else 0  # SOC
#                                 SOH = measure_bms_dict.get("SOH") if measure_bms_dict.get("SOH") and measure_bms_dict.get("SOH") not in EMPTY_STR_LIST else 0  # SOH
#
#                                 master_station_cycles_nmuber_list.append(float(NBSC))
#                                 master_station_soc_list.append(float(SOC))
#                                 master_station_soh_list.append(float(SOH))
#
#                                 BPCE = measure_pcs_dict.get("BPCE") if measure_pcs_dict.get("BPCE", 0) and measure_pcs_dict.get("BPCE", 0) not in EMPTY_STR_LIST else 0
#                                 BPDE = measure_pcs_dict.get("BPDE") if measure_pcs_dict.get("BPDE", 0) and measure_pcs_dict.get("BPDE", 0) not in EMPTY_STR_LIST else 0
#
#                                 master_station_BPCE_list.append(float(BPCE))
#                                 master_station_BPDE_list.append(float(BPDE))
#
#                             device_type = 'bms'
#                             device = getattr(unit, device_type)
#                             m_list = time_range_by_cumulant(slave_station.english_name,'cumulant',
#                                                                   device_type, device, first_day_of_month, current_date)
#                             sorter_datas = sorted(m_list, key=lambda x: x["time"])
#                             if sorter_datas:
#                                 first_pae = sorter_datas[0]["PAE"]
#                                 last_pae = sorter_datas[-1]["PAE"]
#                                 first_nae = sorter_datas[0]["NAE"]
#                                 last_nae = sorter_datas[-1]["NAE"]
#                             else:
#                                 error_log.error('未查到PAE，NAE数据，使用默认值0')
#                                 first_pae = 0
#                                 last_pae = 0
#                                 first_nae = 0
#                                 last_nae = 0
#                             try:
#                                 day_discharge = abs(decimal.Decimal(last_pae) - decimal.Decimal(first_pae))
#                                 day_charge = abs(decimal.Decimal(last_nae) - decimal.Decimal(first_nae))
#                             except Exception as e:
#                                 error_log.error(e)
#                                 raise e
#                             master_station_month_charge_list.append(decimal.Decimal(day_charge))
#                             master_station_month_discharge_list.append(decimal.Decimal(day_discharge))
#                             # 年
#                             y_list = time_range_by_cumulant(slave_station.english_name,'cumulant',
#                                                                   device_type, device, first_day_of_year, current_date)
#                             sorter_datas = sorted(y_list, key=lambda x: x["time"])
#                             if sorter_datas:
#                                 first_pae = sorter_datas[0]["PAE"]
#                                 last_pae = sorter_datas[-1]["PAE"]
#                                 first_nae = sorter_datas[0]["NAE"]
#                                 last_nae = sorter_datas[-1]["NAE"]
#                             else:
#                                 error_log.error('未查到PAE，NAE数据，使用默认值0')
#                                 first_pae = 0
#                                 last_pae = 0
#                                 first_nae = 0
#                                 last_nae = 0
#                             day_discharge = abs(decimal.Decimal(last_pae) - decimal.Decimal(first_pae))
#                             day_charge = abs(decimal.Decimal(last_nae) - decimal.Decimal(first_nae))
#                             master_station_year_charge_list.append(decimal.Decimal(day_charge))
#                             master_station_year_discharge_list.append(decimal.Decimal(day_discharge))
#
#                         # from tools.count import unit_convert
#                         slave_station.day_charge = unit_convert(int(sum(slave_station_day_charge_list)),
#                                                                 'kWh')  # 日充电量
#                         slave_station.day_discharge = unit_convert(
#                             int(sum(slave_station_day_discharge_list)), 'kWh')  # 日放电量
#                         slave_station.active_power = int(sum(slave_station_active_power_list))  # 有功功率
#                         slave_station.reactive_power = int(sum(slave_station_reactive_power_list))  # 无功功率
#
#                         master_station_dict['day_charge'] += slave_station.day_charge[0]
#                         master_station_dict['day_discharge'] += slave_station.day_discharge[0]
#                         master_station_dict['active_power'] += slave_station.active_power
#                         master_station_dict['reactive_power'] += slave_station.reactive_power
#
#         count = station_count(master_station_dict, sum(master_station_BCHCap_list),
#                               sum(master_station_BDHcap_list))
#         master_station_dict['SOC'] = int(
#             sum(master_station_soc_list) / master_station_dict['stations__unit_number']) if \
#             master_station_dict['stations__unit_number'] else 0  # soc
#         master_station_dict['SOH'] = int(
#             sum(master_station_soh_list) / master_station_dict['stations__unit_number']) if \
#             master_station_dict['stations__unit_number'] else 0  # soh
#         master_station_dict['cycles'] = int(
#             sum(master_station_cycles_nmuber_list) / master_station_dict['stations__unit_number']) if \
#             master_station_dict['stations__unit_number'] else 0  # 上报循环次数
#         master_station_dict['cycles_nmuber'] = master_station_dict['count'] = count  # 计算循环次数
#
#         # 新增 月充放电量 年充放电量
#         master_station_dict["month_charge"] = unit_convert(int(sum(master_station_month_charge_list)), 'kWh')  # 月充放电量
#         master_station_dict["month_discharge"] = unit_convert(int(sum(master_station_month_discharge_list)),
#                                                               'kWh')  # 月充放电量
#         master_station_dict["year_charge"] = unit_convert(int(sum(master_station_year_charge_list)), 'kWh')  # 年充放电量
#         master_station_dict["year_discharge"] = unit_convert(int(sum(master_station_year_discharge_list)),
#                                                              'kWh')  # 年充放电量
#         # 新增 可充 可放
#         master_station_dict["BPCE"] = unit_convert(round(sum(master_station_BPCE_list), 2),
#                                                    'kWh') if master_station_BPCE_list else unit_convert(0, 'kWh')
#         master_station_dict["BPDE"] = unit_convert(round(sum(master_station_BPDE_list), 2),
#                                                    'kWh') if master_station_BPDE_list else unit_convert(0, 'kWh')
#
#         master_station_dict['status'] = 1
#         if alarm_exist:
#             master_station_dict['status'] = 2
#         if 1 in master_station_Fault_list:
#             master_station_dict['status'] = 3
#         if 1 in master_station_offline_list:
#             master_station_dict['status'] = 4
#
#         master_station_dict['stations__station_status'] = master_station_dict['status']
#
#         # 响应格式还原原接口
#         master_station_dict['day_charge'] = [master_station_dict['day_charge'], 'kWh']
#         master_station_dict['day_discharge'] = [master_station_dict['day_discharge'], 'kWh']
#
#         if slave_stations.count() > 1:
#             master_station_dict['is_master'] = 1
#             master_station_dict['slave_control_strategys'] = temp_list
#         else:
#             master_station_dict['is_master'] = 0
#             master_station_dict['slave_control_strategys'] = []
#
#         return master_station_dict
#
#     def get(self, request):
#         user_id = request.user["user_id"]
#         user = models.UserDetails.objects.get(id=user_id)
#         station_name_include = request.query_params.get("station", None)
#
#         # 主从站的逻辑
#         if station_name_include:
#             master_stations = user.master_stations.filter(is_delete=0, name=station_name_include).all()
#         else:
#             master_stations = user.master_stations.filter(is_delete=0).all()
#
#         master_stations_array = list()
#         if master_stations.exists():
#             # 新增 月充放电量 年充放电量
#             # 获取当前日期
#             current_date = datetime.datetime.now()
#             # 获取本月第一天的日期
#             first_day_of_month = current_date.replace(day=1, hour=0, minute=0, second=0)
#             # 获取今年第一天的日期
#             first_day_of_year = current_date.replace(month=1, day=1, hour=0, minute=0, second=0)
#
#             with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
#                 futures = list()
#                 for master_station in master_stations:
#                     future = executor.submit(self._get_master_station_data, master_station, first_day_of_year,
#                                              first_day_of_month, current_date)
#                     futures.append(future)
#                 master_stations_array = [f.result() for f in concurrent.futures.as_completed(futures)]
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": master_stations_array,
#                 },
#             }
#         )


# class StationListView(APIView):
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         user_id = request.user["user_id"]
#         page = int(request.query_params.get('page')) if request.query_params.get('page') else None
#         page_size = int(request.query_params.get('page_size', 10))
#         user = models.UserDetails.objects.get(id=user_id)
#
#         master_stations = models.MaterStation.objects.filter(userdetails=user, is_delete=0).all()
#
#         total_pages = math.ceil(len(master_stations) / page_size)
#         if page:
#             page = total_pages if page > total_pages else page
#             start_index = (page-1) * page_size
#             end_index = page * page_size if page < total_pages else len(master_stations) + 1
#
#         # url = "http://***********:9001/api/point/getRealtimeData"
#         # urlv3 = "http://***********:9001/api/point/getHistoryDataV3"
#         temp_array = []
#         in_time_list = []
#
#         total_rated_power_all = []
#         total_rated_capacity_all = []
#
#         for master_station in master_stations:
#             rated_power_all_ = []
#             rated_capacity_all_ = []
#
#             stations = master_station.stationdetails_set.filter(is_delete=0).all()
#             if stations.exists():
#                 for station in stations:
#                     if station.slave != 0:
#                         # 计算总功率和容量
#                         rated_power_all_.append(float(station.rated_power))
#                         rated_capacity_all_.append(float(station.rated_capacity))
#             total_rated_power_all.append(round(sum(rated_power_all_), 2))
#             total_rated_capacity_all.append(round(sum(rated_capacity_all_), 2))
#
#             in_time_list.append(master_station.project.in_time)
#
#         for master_station in master_stations[start_index:end_index] if page else master_stations:
#             master_station_dict = {
#                 "id": master_station.id,
#                 "app": None,
#                 "english_name": master_station.english_name,
#                 "station_name": master_station.name,
#                 "address": None,
#                 "rated_power": 0,
#                 "rated_power_unit": 'kW',
#                 "rated_capacity": 0,
#                 "rated_capacity_unit": 'kWh',
#                 "db": None
#             }
#
#             rated_power_all = []
#             rated_capacity_all = []
#
#             Fault_list = []
#             offline_list = []
#
#             day_charge_list = []
#             day_discharge_list = []
#             conn = get_redis_connection("3")
#             stations = master_station.stationdetails_set.filter(is_delete=0).all()
#             if stations.exists():
#                 for station in stations:
#                     if station.slave == -1 or station.slave == 0:
#                         master_station_dict['app'] = station.app
#                         master_station_dict['address'] = station.address
#                         master_station_dict['db'] = station.db
#
#                     if station.slave != 0:
#                         # 计算总功率和容量
#                         rated_power_all.append(float(station.rated_power))
#                         rated_capacity_all.append(float(station.rated_capacity))
#
#                         # 单元
#                         units = models.Unit.objects.filter(is_delete=0, station__id=station.id).all()
#                         try:
#                             t_project_obj = models.Project.objects.get(is_used=1, english_name=station.db)
#                             run_start_date = t_project_obj.in_time
#                             in_time_list.append(run_start_date)
#                         except Exception as e:
#                             error_log.error('根据station的db信息，未找到对对应的project')
#                         num = 0
#                         for unit in units:
#
#                             # json_data = {
#                             #     "app": station.app,
#                             #     "station": station.english_name,
#                             #     "body": [
#                             #         {
#                             #             "device": unit.pcs,
#                             #             "datatype": "status",
#                             #             "totalcall": "0",
#                             #             "body": [
#                             #                 "Fault",
#                             #                 "alarm",
#                             #             ],
#                             #         },
#                             #         {
#                             #             "device": unit.bms,
#                             #             "datatype": "status",
#                             #             "totalcall": "0",
#                             #             "body": [
#                             #                 "GFault",
#                             #                 "GAlarm",
#                             #             ],
#                             #         },
#                             #     ],
#                             # }
#                             # response = requests.post(url=url, json=json_data)
#                             # return_dic = response.json()
#                             # # print(return_dic)
#                             # body = return_dic.get("body", None)
#                             # if not body:
#                             #     error_log.error("http调用安昌数据库查询接口失败")
#                             #     return Response(
#                             #         {
#                             #             "code": common_response_code.ERROR,
#                             #             "data": {
#                             #                 "message": "fail",
#                             #                 "detail": "http调用安昌数据库查询接口失败",
#                             #             },
#                             #         }
#                             #     )
#                             # print ('station-name is ',station,'---------',body[0]["body"])
#                             # if len(body[0]["body"]) == 0:
#                             #     offline_list.append(1)  # 离线状态
#                             # else:
#                             #     GFault = body[1]["body"].get("GFault", -2)  # bms故障状态
#                             #     # GAlarm = body[6]["body"].get("GAlarm", -2)  # bms警告状态
#                             #     Fault = body[0]["body"].get("Fault", -2)  # pcs故障状态
#                             #     # alarm = body[2]["body"].get("alarm", -2)  # pcs警告状态
#
#                             #     if Fault and int(Fault) == 1:
#                             #         Fault_list.append(1)
#                             #     if GFault and int(GFault) == 1:
#                             #         Fault_list.append(1)
#
#                             key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.bms)
#                             bms_status = conn.get(key1)
#                             key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, unit.pcs)
#                             pcs_status = conn.get(key2)
#                             if not bms_status or not pcs_status:  # 离线
#                                 offline_list.append(1)
#                             else:
#                                 bms_status_str = bms_status.decode("utf-8")
#                                 bms_status_dict = json.loads(json.loads(bms_status_str))
#
#                                 pcs_status_str = pcs_status.decode("utf-8")
#                                 pcs_status_dict = json.loads(json.loads(pcs_status_str))
#                                 # alarm = pcs_status_dict.get('alarm',0)
#                                 Fault = pcs_status_dict.get('Fault') if pcs_status_dict.get('Fault') and pcs_status_dict.get('Fault') not in EMPTY_STR_LIST else 0
#
#                                 GFault = bms_status_dict.get('GFault') if bms_status_dict.get('GFault') and bms_status_dict.get('GFault') not in EMPTY_STR_LIST else 0
#                                 # GAlarm = bms_status_dict.get('GAlarm',0)
#                                 if (Fault and Fault == '1') or GFault and GFault == '1':
#                                     Fault_list.append(1)
#
#                             if len(units) > 1:
#                                 num += 1
#                             else:
#                                 num = ""
#                             # json_data = {
#                             #     "app": station.app,
#                             #     "station": station.english_name,
#                             #     "body": [
#                             #         {
#                             #             "device": unit.bms,
#                             #             "datatype": "measure",
#                             #             "body": ["ChaED", "DisED"],
#                             #         }
#                             #     ],
#                             # }
#                             # response = requests.post(url=url, json=json_data)
#                             # return_dic = response.json()
#                             # # return_dic = {'msg': '成功', 'code': 200, 'body': [{'datatype': 'measure', 'body': {'DisED': '89.5', 'ChaED': '166.9'}, 'device': 'BMS'}]}
#                             # body = return_dic.get("body")
#                             # if not body:
#                             #     error_log.error("http调用安昌数据库查询接口失败")
#                             #     return Response(
#                             #         {
#                             #             "code": common_response_code.ERROR,
#                             #             "data": {
#                             #                 "message": "fail",
#                             #                 "detail": "http调用安昌数据库查询接口失败",
#                             #             },
#                             #         }
#                             #     )
#                             if station.english_name == "NBLS001":  # 德创单独计算日充放电量
#                                 key3 = "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station.english_name, unit.bms)
#                                 bms_measure = conn.get(key3)
#                                 if not bms_measure:
#                                     ChaD = 0  # 今日充电量
#                                     DisD = 0
#                                     offline_list.append(1)
#                                     continue
#                                 else:
#                                     bms_measure_str = bms_measure.decode("utf-8")
#                                     bms_measure_dict = json.loads(json.loads(bms_measure_str))
#                                     ChaD = bms_measure_dict.get("ChaED") if bms_measure_dict.get("ChaED") and bms_measure_dict.get("ChaED") not in EMPTY_STR_LIST else 0
#                                     DisD = bms_measure_dict.get("DisED") if bms_measure_dict.get("DisED") and bms_measure_dict.get("DisED") not in EMPTY_STR_LIST else 0
#                                     # ChaD = body[0]["body"].get("ChaED", 0)  # 今日充电量
#                                     # DisD = body[0]["body"].get("DisED", 0)  # 今日放电量
#                             else:
#                                 # today_timestamp = int(time.time())
#                                 # request_json = {
#                                 #     "time": str(int(today_timestamp)),
#                                 #     "datatype": "cumulant",
#                                 #     "app": station.app,
#                                 #     "station": station.english_name,
#                                 #     "body": [
#                                 #         {
#                                 #             "device": unit.bms,
#                                 #             "body": ["PAE", "NAE"],
#                                 #         }
#                                 #     ],  # 充电量  # 放电量
#                                 # }
#                                 # response = requests.post(url=urlv3, json=request_json)
#
#                                 today_str = time.strftime("%Y-%m-%d", time.localtime())
#
#                                 # 改成当今日充放电量大于等于额定容量的2.5倍时，从IDC冻结表中取今日的充放电量
#                                 # capacity = float(unit.rated_capacity)
#                                 #
#                                 # select_sql = f'select pae, nae, time, station_name from {dwd_tables["cumulant"]["bms"]} where station_name=%s and device=%s and time like %s order by time ASC'
#                                 #
#                                 # results = dwd_db_tool.select_many(select_sql, *(station.english_name, unit.bms,
#                                 #                                                 '%' + today_str + '%'))
#                                 #
#                                 # # datas = response["datas"]
#                                 # # result = response.json()
#                                 # # datas = result['datas']
#                                 # if results:
#                                 #     first_pae, first_nae, last_pae, last_nae = 0, 0, 0, 0
#                                 #     # sorter_datas = sorted(datas, key=lambda x: x["body"]["time"])
#                                 #     # if "PAE" in sorter_datas[0]["body"]["data"][0]:
#                                 #     first_pae = float(results[0]['pae']) if results[0]['pae'] else 0
#                                 #     first_nae = float(results[0]['nae']) if results[0]['nae'] else 0
#                                 #     # if "PAE" in sorter_datas[-1]["body"]["data"][0]:
#                                 #     last_pae = float(results[-1]['pae']) if results[-1]['pae'] else 0
#                                 #     last_nae = float(results[-1]['nae']) if results[-1]['nae'] else 0
#                                 #     DisD = abs(last_pae - first_pae)
#                                 #     ChaD = abs(last_nae - first_nae)
#                                 # else:
#                                 #     error_log.error(
#                                 #         f'根据 station:{station.english_name}，time:{today_str},device:{unit.bms},未找到对应数据')
#                                 #     ChaD = 0  # 今日充电量
#                                 #     DisD = 0
#                                 #
#                                 # if DisD > capacity * 2.5 or DisD > capacity * 2.5 or (
#                                 #         DisD == 0 and DisD == 0):
#
#                                 select_sql = (
#                                     "SELECT day, chag as charge, disg as discharge FROM ads_report_chag_disg_1d"
#                                     " where station=%s and station_type=2 and unit_name=%s and day=%s")
#                                 # 查询 ads_report_chag_disg_1d
#                                 result = ads_db_tool.select_one(select_sql, station.english_name, unit.bms, today_str)
#                                 if result:
#                                     ChaD = result["charge"]
#                                     DisD = result["discharge"]
#                                 else:
#                                     ChaD = 0
#                                     DisD = 0
#
#                             day_charge_list.append(decimal.Decimal(ChaD))
#                             day_discharge_list.append(decimal.Decimal(DisD))
#
#                 cha, c_uni = unit_convert(int(sum(day_charge_list)), 'kWh')
#                 dis, d_uni = unit_convert(int(sum(day_discharge_list)), 'kWh')
#                 if cha >= 1000:
#                     master_station_dict["day_charge"] = unit_convert(int(cha), c_uni)  # 日充电量
#                 else:
#                     master_station_dict["day_charge"] = [cha, c_uni]
#                 if dis >= 1000:
#                     master_station_dict["day_discharge"] = unit_convert(int(dis), d_uni)  # 日放电量
#                 else:
#                     master_station_dict["day_discharge"] = [dis, d_uni]
#
#                 master_station_dict["rated_power"] = round(sum(rated_power_all), 2)
#                 master_station_dict["rated_capacity"] = round(sum(rated_capacity_all), 2)
#
#                 stations_ids = [s.id for s in stations]
#                 alarms = models.FaultAlarm.objects.using('alarm_module').filter(type=2, status=0,
#                                                                                 station_id__in=stations_ids)
#                 if 1 in offline_list:
#                     master_station_dict["station_status"] = 4
#                 elif 1 in Fault_list:
#                     master_station_dict["station_status"] = 3
#                 elif alarms.exists():
#                     master_station_dict["station_status"] = 2
#                 else:
#                     master_station_dict["station_status"] = 1
#             # total_rated_power_all.append(master_station_dict["rated_power"])
#             # total_rated_capacity_all.append(master_station_dict["rated_capacity"])
#             temp_array.append(master_station_dict)
#
#         first_day = min(in_time_list) if in_time_list else datetime.datetime.now()
#         run_days = datetime.datetime.now() - first_day
#
#         if page:
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": temp_array,
#                         "station_count": len(master_stations),
#                         "rated_power_all": (unit_convert(int(sum(total_rated_power_all)), 'kW')),
#                         "rated_capacity_all": (unit_convert(int(sum(total_rated_capacity_all)), 'kWh')),
#                         "run_days": run_days.days
#                     },
#                     "paginator_info": {
#                         "page": page,
#                         "page_size": page_size,
#                         "pages": total_pages,
#                         "total_count": len(master_stations)
#                     }
#                 }
#             )
#         else:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {
#                         "message": "all data",
#                         "detail": temp_array,
#                         "station_count": len(master_stations),
#                         "rated_power_all": (unit_convert(int(sum(total_rated_power_all)), 'kW')),
#                         "rated_capacity_all": (unit_convert(int(sum(total_rated_capacity_all)), 'kWh')),
#                         "run_days": run_days.days
#                     },
#                     "paginator_info": {
#                         "page": page,
#                         "page_size": page_size,
#                         "pages": total_pages,
#                         "total_count": len(master_stations)
#                     }
#                 }
#             )


# class UnitCapacityIncome(APIView):
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request):
#         import datetime
#         from django.db.models import Max, Sum
#         user_id = request.user['user_id']
#         user_ins = models.UserDetails.objects.filter(id=user_id).first()
#         # projects = models.Project.objects.filter(user=user_ins).all()
#         master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0).all()
#         # stations_ins = models.StationDetails.objects.filter(project__user=user_ins, project__in=projects)
#         income_date = datetime.date.today()
#         detail_dic = {}
#         month_general_income = []
#         # last_month_general_income = []
#         capacity_count_this_month = []
#         # capacity_count_last_month = []
#
#         for master_station in master_stations:
#             stations = master_station.stationdetails_set.filter(is_delete=0).all()
#             if stations.exists():
#                 for station in stations:
#                     incomes_ins = models.StationIncome.objects.filter(station_id=station)
#                     # print(3631, incomes_ins)
#                     # 本月收益
#                     this_month_first_day = income_date.replace(day=1)
#                     in_this_month = models.StationIncome.objects.filter(
#                         station_id=station, income_date__range=(this_month_first_day, income_date)
#                     ).exists()
#                     if in_this_month:
#                         capacity_count_this_month.append(decimal.Decimal(station.rated_capacity))
#                     subquery_ = (
#                         incomes_ins.filter(income_date__range=(this_month_first_day, income_date))
#                         .values('income_date')
#                         .annotate(last_entry=Max('id'))
#                         .values('last_entry')
#                     )
#                     monthly_income_ = incomes_ins.filter(id__in=subquery_).aggregate(
#                         total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
#                     )
#                     if not monthly_income_['total_peak']:
#                         monthly_income_['total_peak'] = 0
#                     if not monthly_income_['total_demand']:
#                         monthly_income_['total_demand'] = 0
#                     total_month_ = monthly_income_.get('total_peak', 0) + monthly_income_.get('total_demand', 0)
#                     month_general_income.append(total_month_)
#
#                     # 上月收益
#                     # last_month_last_day = income_date.replace(day=1) - datetime.timedelta(days=1)
#                     # this_month = income_date.month
#                     # last_month_first_day = income_date.replace(month=this_month - 1, day=1)
#                     # in_last_month = models.StationIncome.objects.filter(
#                     #     station_id=station, income_date__range=(last_month_first_day, last_month_last_day)
#                     # ).exists()
#
#                     # if in_last_month:
#                     #     capacity_count_last_month.append(decimal.Decimal(station.rated_capacity))
#                     # subquery_ = (
#                     #     incomes_ins.filter(income_date__range=(last_month_first_day, last_month_last_day))
#                     #     .values('income_date')
#                     #     .annotate(last_entry=Max('id'))
#                     #     .values('last_entry')
#                     # )
#                     # monthly_income_ = incomes_ins.filter(id__in=subquery_).aggregate(
#                     #     total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
#                     # )
#                     # if not monthly_income_['total_peak']:
#                     #     monthly_income_['total_peak'] = 0
#                     # if not monthly_income_['total_demand']:
#                     #     monthly_income_['total_demand'] = 0
#             # total_month_ = monthly_income_.get('total_peak', 0) + monthly_income_.get('total_demand', 0)
#             # last_month_general_income.append(total_month_)
#         month_general_incomes = sum(month_general_income)
#         # last_month_general_incomes = sum(last_month_general_income)
#         count_this_month = sum(capacity_count_this_month)
#         # count_last_month = sum(capacity_count_last_month)
#
#         try:
#             this_month_unit_income = decimal.Decimal(
#                 decimal.Decimal(month_general_incomes) / (decimal.Decimal(count_this_month))).quantize(
#                 decimal.Decimal("0.0")
#             )
#         except Exception as e:
#             this_month_unit_income = 0
#         # try:
#         #     last_month_unit_income = decimal.Decimal(
#         #         decimal.Decimal(last_month_general_incomes) / (decimal.Decimal(count_last_month))
#         #     ).quantize(decimal.Decimal("0.0"))
#         # except Exception as e:
#         #     last_month_unit_income = 0
#         # print ('this_month_unit_income:-------',this_month_unit_income)
#         # print ('last_month_unit_income:------------',last_month_unit_income)
#         detail_dic['this_month_unit_income'] = this_month_unit_income
#         # detail_dic['last_this_month_diff'] = float(this_month_unit_income - last_month_unit_income)
#         # error_log.error(f'本月单位收益：{this_month_unit_income}, 上月单位收益：{last_month_unit_income}')
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": detail_dic,
#                 },
#             }
#         )


# class StationIsMasterStationView(APIView):
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         station_name = request.query_params.get("station")
#         if not station_name:
#             error_log.error("站详情页:字段校验不通过")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": {"station": ["该字段是必传项"]}},
#                 }
#             )
#
#         try:
#             master_station = models.MaterStation.objects.get(is_delete=0, english_name=station_name)
#         except Exception as e:
#             error_log.error(e)
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": {"station": ["查询站名失败"]}},
#                 }
#             )
#
#         slave_stations_count = master_station.stationdetails_set.filter(is_delete=0).all().count()
#         is_master = 1 if slave_stations_count > 1 else 0
#         if is_master:
#             units = models.Unit.objects.filter(is_delete=0, station__master_station=master_station).all().order_by('unit_new_name')
#             temp_list = []
#             for unit in units:
#                 title = unit.unit_new_name + '-就地控制（从）'
#                 temp_list.append()
#         return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {"message": "success", "detail": {"is_master": is_master, "options": {1: "主从站", 0: "非主从站"}}}
#                 }
#             )


class Ht(APIView):
    def get(self, request):
        en = request.query_params.get('station', 'HTHJ101')
        station = models.StationDetails.objects.filter(is_delete=0, english_name=en).first()
        # topic = f"req/database/parameter/{station.english_name}/{station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(en)
        return Response({'TOKEN': token})