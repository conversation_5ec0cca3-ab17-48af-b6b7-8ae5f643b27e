#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 09:43:30
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\Mongo\r_mongo.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-05-11 14:07:33

# from sqlalchemy.ext.declarative import declared_attr
# from sqlalchemy.engine.base import TwoPhaseTransaction

from Tools.DB.mongodb_con import mongo_db,MONGODB_HOSTNAME,MONGODB_PORT,MONGODB_DATABASE
from Tools.DB.mongodb_con import Document,StringField,IntField,FloatField,BooleanField,DateTimeField,ListField,DictField,ReferenceField,SequenceField,connect

connect(MONGODB_DATABASE,host=MONGODB_HOSTNAME,port=MONGODB_PORT)

class Demo(Document):
    u'权限静态配置表'
   
    # meta = {'collection': 'demo'}
    name = StringField(required=True,)
    age = IntField()
   
    # print meta
    # print '^^^^^'
    # print '****'
    # meta = {'collection' : 'demo2'}
    
   
