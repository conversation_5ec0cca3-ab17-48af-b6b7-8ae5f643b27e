# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/22 下午4:58
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : views.py
# @Software : PyCharm
import datetime
import os

import math
import pymysql
import concurrent.futures
from django.core.paginator import Paginator
from django.db import connections
from django.db.models import Sum
from rest_framework.response import Response
from rest_framework.views import APIView

from TianLuAppBackend import settings
from TianLuAppBackend.settings import BASE_DIR
from apis.app2 import error_log
from apis.user.models import Project, MaterStation, RunningAnalysis, MessageCenter, StationDetails, \
    UserDetails, FaultAlarm
from apis.web2.running_analysis import serializers
from apis.web2.running_analysis.serializers import AnalysisFeedbackSerializer
from common import common_response_code
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
# from serializers import monitor_serializers
from tools.gen_excel import create_excel, post2minio
from tools.hour_setting import create_time_mapping


class AnalysisListView(APIView):
    """分析列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def deal_item_data(self, ins, lang='zh'):
        ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M')
        if ins["end_time"]:
            ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M')
        try:
            ins['station_name'] = MaterStation.objects.get(id=ins['station'], is_delete=0).name
        except Exception as e:
            pass

        # 反馈内容
        analysis = RunningAnalysis.objects.get(id=ins['id'])
        if lang == 'zh':
            ins['feedback'] = analysis.feedback.content if analysis.feedback else ''
        else:
            ins['feedback'] = analysis.feedback.en_content if analysis.feedback else ''
            ins['keyword'] = ins['en_keyword']
            ins['ref_threshold'] = ins['en_ref_threshold']
            ins['topic'] = ins['en_topic']
        return ins

    def path_all(self, batch, lang='zh'):
        # batch,temp_list = args
        # station_ids = [item.get("station") for item in batch]
        # ids = [item.get("id") for item in batch]
        # analysis = RunningAnalysis.objects.filter(id__in=ids,is_delete=0).all()
        # id_feedback = {}
        # for item in analysis:
        #     id_feedback[item.id] = item.feedback.content if item.feedback else ''
        data = MaterStation.objects.filter(is_delete=0).all()
        id_name = {}
        for item in data:
            id_name[item.id] = item.name
        for item in batch:
            item['station_name'] = id_name.get(item.get("station"))

            # 反馈内容
            analysis = RunningAnalysis.objects.get(id=item['id'])
            if lang == 'zh':
                item['feedback'] = analysis.feedback.content if analysis.feedback else ''
            else:
                item['feedback'] = analysis.feedback.en_content if analysis.feedback else ''
                item['keyword'] = item['en_keyword']
                item['ref_threshold'] = item['en_ref_threshold']
                item['topic'] = item['en_topic']

            # if item.get("feedback"):
            #     feedback = RunningAnalysis.objects.filter(id=item.get("feedback"), is_delete=0).first()
            #     if feedback:
            #         feedback = feedback.content
            #     else:
            #         feedback = ""
            # else:
            #     feedback = ""
            # # item['feedback'] = item.get("feedback").content if item.get("feedback") else ''
            # item['feedback'] = feedback
            item["start_time"] = item["start_time"].strftime('%Y-%m-%d %H:%M')
            if item["end_time"]:
                item["end_time"] = item["end_time"].strftime('%Y-%m-%d %H:%M')
        # temp_list.extend(batch)
        return batch

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user = UserDetails.objects.get(id=request.user.get('user_id'))
        ser = serializers.AnalysisSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"运行分析:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("运行分析：字段校验不通过：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": e.args[0]}})

        start_time = ser.validated_data.get('start_time')
        end_time = ser.validated_data.get('end_time')
        topic = ser.validated_data.get('topic')
        status = ser.validated_data.get('status')
        station_id = ser.validated_data.get('station_id')
        station_name = ser.validated_data.get('station_name')
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 1000000000))

        # start = (int(page) - 1) * int(page_size)
        # end = start + int(page_size) + 1

        try:
            if station_id:
                stations_id_list = station_id.split(',')
                master_stations = MaterStation.objects.filter(id__in=stations_id_list, userdetails=user, is_delete=0).all()
            else:
                projects = Project.objects.filter(is_used=1, user=user).all()
                # master_stations = MaterStation.objects.filter(project__in=projects).all()
                master_stations = MaterStation.objects.filter(userdetails=user, project__in=projects, is_delete=0).all()
        except Exception as e:
            error_log.error("运行分析：并网点不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error",
                                                                    "detail": f"并网点: <{station_id}>不存在" if lang == 'zh' else f"station: <{station_id}> not exist"}})

        if station_name:
            master_stations = master_stations.filter(is_delete=0, name__contains=station_name).all()

        if start_time and end_time:
            start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
            end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'
            filters = {
                "start_time__gte": datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S"),
                "start_time__lte": datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
                }
        else:
            filters = {}

        if topic:
            if lang == 'zh':
                filters['topic'] = topic
            else:
                filters['en_topic'] = topic

        if status:
            filters['status'] = int(status)

        # if type_:
        #     if int(type_) == 4:
        #         filters['type__in'] = [4, 5]
        #     else:
        #         filters['type'] = type_
        # if keyword:
        #     filters['details__contains'] = keyword

        # stations = project_instance.stationdetails_set.all()

        # master_stations = project_instance.materstation_set.filter(is_delete=0).all()
        # master_station_name = master_station.name
        # slave_stations = master_station.stationdetails_set.all()

        # slave_stations = models.StationDetails.objects.filter(master_station__project=project_instance).all()

        # total_fault_Analysis_instances = []
        filters['station__in'] = master_stations

        total_analysis_instances = RunningAnalysis.objects.filter(**filters).values("id", "topic", "en_topic", "status",
                                                                                      "start_time",
                                                                                      "end_time",
                                                                                      "keyword", "ref_threshold",
                                                                                      "en_keyword", "en_ref_threshold",
                                                                                      "station", "feedback"
                                                                                      ).order_by("-start_time")

        # total_analysis_instances = RunningAnalysis.objects.filter(**filters).order_by("-start_time")
        #
        # total_analysis_instances_ = total_analysis_instances.values("id", "topic", "en_topic", "status",
        #                                                                               "start_time",
        #                                                                               "end_time",
        #                                                                               "keyword", "ref_threshold",
        #                                                                               "en_keyword", "en_ref_threshold",
        #                                                                               "station", "feedback")

        # total_count = total_analysis_instances.count()
        # if end > total_count:
        #     end = total_count + 1
        #
        # total_pages = math.ceil(total_count / page_size)

        # page_total_analysis_instances = total_analysis_instances_[start:end]

        # 分页
        paginator = Paginator(total_analysis_instances, page_size)
        tem_analysis_instances = paginator.get_page(page).object_list
        temp_list = []
        if len(tem_analysis_instances):
            temp_list.extend(self.path_all(tem_analysis_instances, lang))


        # temp_list = []
        # if len(page_total_analysis_instances):
        #     with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        #         futures = list()
        #         for item in page_total_analysis_instances:
        #             future = executor.submit(self.deal_item_data, item)
        #             futures.append(future)
        #
        #         for future in concurrent.futures.as_completed(futures):
        #             ins = future.result()
        #             temp_list.append(ins)

        # 分页
        # paginator = Paginator(list(temp_list), page_size)
        # tem_analysis_instances = paginator.get_page(page).object_list
        # todo 直接分页查询

        # paginator_info = {
        #     "page": page,
        #     "page_size": page_size,
        #     "pages": total_pages,
        #     "total_count": total_count
        # }
        paginator_info = {
            "page": page,
            "page_size": page_size,
            "pages": paginator.num_pages,
            "total_count": paginator.count
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    # "detail": sorted(temp_list, key=lambda x: x['start_time'], reverse=True),
                    "detail": temp_list,
                    "paginator_info": paginator_info,
                    # "stations_options": stations_options
                },
            }
        )


class AnalysisDownloadView(APIView):
    """
    运行分析：下载
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def deal_item_data(self, ins, lang='zh'):
        ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M')
        if ins["end_time"]:
            ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M')
        try:
            ins['station_name'] = MaterStation.objects.get(id=ins['station'], is_delete=0).name
        except Exception as e:
            pass

        # 将状态（0、1）转换成字符串
        if lang == 'zh':
            ins['status'] = '已恢复' if ins['status'] == 1 else '未恢复'
        else:
            ins['status'] = 'Recovered' if ins['status'] == 1 else 'Not recovered'

        # 反馈内容
        analysis = RunningAnalysis.objects.get(id=ins['id'])
        if lang == 'zh':
            ins['feedback'] = analysis.feedback.content if analysis.feedback else ''
        else:
            ins['feedback'] = analysis.feedback.en_content if analysis.feedback else ''
            ins['keyword'] = ins['en_keyword']
            ins['ref_threshold'] = ins['en_ref_threshold']
            ins['topic'] = ins['en_topic']
        return ins

    def path_all(self,batch, lang='zh'):
        # batch,temp_list = args
        # station_ids = [item.get("station") for item in batch]
        # ids = [item.get("id") for item in batch]
        # analysis = RunningAnalysis.objects.filter(id__in=ids,is_delete=0).all()
        # id_feedback = {}
        # for item in analysis:
        #     id_feedback[item.id] = item.feedback.content if item.feedback else ''
        data = MaterStation.objects.filter(is_delete=0).all()
        id_name = {}
        for item in data:
            id_name[item.id] = item.name
        for item in batch:
            # item['status'] = '已恢复' if item['status'] == 1 else '未恢复'
            item['station_name'] = id_name.get(item.get("station"))
            # item['feedback'] = item.get("feedback").content if item.get("feedback") else ''

            # 将状态（0、1）转换成字符串
            if lang == 'zh':
                item['status'] = '已恢复' if item['status'] == 1 else '未恢复'
            else:
                item['status'] = 'Recovered' if item['status'] == 1 else 'Not recovered'

            # 反馈内容
            analysis = RunningAnalysis.objects.get(id=item['id'])
            if lang == 'zh':
                item['feedback'] = analysis.feedback.content if analysis.feedback else ''
            else:
                item['feedback'] = analysis.feedback.en_content if analysis.feedback else ''
                item['keyword'] = item['en_keyword']
                item['ref_threshold'] = item['en_ref_threshold']
                item['topic'] = item['en_topic']

            item["start_time"] = item["start_time"].strftime('%Y-%m-%d %H:%M')
            if item["end_time"]:
                item["end_time"] = item["end_time"].strftime('%Y-%m-%d %H:%M')
        # temp_list.extend(batch)
        return batch

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        user = UserDetails.objects.get(id=request.user.get('user_id'))
        ser = serializers.AnalysisSerializer(data=request.query_params)
        try:
            if not ser.is_valid():
                error_log.error(f"运行分析:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"运行分析:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=90)

        start_time = ser.validated_data.get('start_time', default_start_day)
        end_time = ser.validated_data.get('end_time', default_end_day)
        status = ser.validated_data.get('status')
        topic = ser.validated_data.get('topic')
        # type_ = ser.validated_data.get('type')
        station_id = ser.validated_data.get('station_id')
        # keyword = ser.validated_data.get('keyword')
        # page = int(request.query_params.get('page', 1))
        # page_size = int(request.query_params.get('page_size', 1000000000))

        # start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
        # end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'

        try:
            if station_id:
                stations_id_list = station_id.split(',')
                master_stations = MaterStation.objects.filter(id__in=stations_id_list, userdetails=user, is_delete=0).all()
            else:
                projects = Project.objects.filter(is_used=1, user=user).all()
                master_stations = MaterStation.objects.filter(project__in=projects, userdetails=user, is_delete=0).all()
        except Exception as e:
            error_log.error("运行分析：并网点不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error",
                                                                    "detail": f"并网点: <{station_id}>不存在" if lang=='zh' else f"Station: <{station_id}> does not exist"}})

        if start_time and end_time:
            start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
            end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'
            filters = {
                "start_time__gte": datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S"),
                "start_time__lte": datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
                }
        else:
            filters = {}

        if topic:
            if lang == 'zh':
                filters['topic'] = topic
            else:
                filters['en_topic'] = topic

        if status:
            filters['status'] = int(status)

        filters['station__in'] = master_stations
        total_analysis_instances = RunningAnalysis.objects.filter(**filters).values("id", "topic", "en_topic", "status",
                                                                                    "start_time",
                                                                                    "end_time",
                                                                                    "keyword", "ref_threshold",
                                                                                    "en_keyword", "en_ref_threshold",
                                                                                    "station", "feedback"
                                                                                    ).order_by("-start_time")

        # temp_list = []
        # for ins in total_analysis_instances:
        #     ins['status'] = '已恢复' if ins['status'] == 1 else '未恢复'
        #     ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
        #     if ins["end_time"]:
        #         ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
        #     try:
        #         ins['station_name'] = MaterStation.objects.get(id=ins['station']).name
        #     except Exception as e:
        #         pass
        #
        #     # 反馈内容
        #     analysis = RunningAnalysis.objects.get(id=ins['id'])
        #     ins['feedback'] = analysis.feedback.content if analysis.feedback else ''
        #
        #     temp_list.append([ins['id'], ins['station_name'], ins['topic'], ins['status'], ins['start_time'],
        #                       ins['end_time'], ins['keyword'], ins['ref_threshold'], ins['feedback']])

        # temp_list = []
        # if len(total_analysis_instances):
        #     with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        #         futures = list()
        #         for item in total_analysis_instances:
        #             future = executor.submit(self.deal_item_data, item)
        #             futures.append(future)
        #
        #         for future in concurrent.futures.as_completed(futures):
        #             ins = future.result()
        #             temp_list.append([ins['id'], ins['station_name'], ins['topic'], ins['status'], ins['start_time'],
        #                                   ins['end_time'], ins['keyword'], ins['ref_threshold'], ins['feedback']])
        temp_all = []
        if len(total_analysis_instances):
            temp_all.extend(self.path_all(total_analysis_instances, lang))

        temp_list = []
        for ins in temp_all:
            # temp_list.append([ins['id'], ins['station_name'], ins['topic'], ins['status'], ins['start_time'],
            #                                 ins['end_time'], ins['keyword'], ins['ref_threshold'], ins['feedback']])

            if lang == 'zh':
                temp_list.append([ins['id'], ins['station_name'], ins['topic'], ins['status'], ins['start_time'],
                                  ins['end_time'], ins['keyword'], ins['ref_threshold'], ins['feedback']])
            else:
                temp_list.append([ins['id'], ins['station_name'], ins['en_topic'], ins['status'], ins['start_time'],
                                  ins['end_time'], ins['en_keyword'], ins['en_ref_threshold'], ins['feedback']])

        # 生成excel
        if lang == 'zh':
            if start_time and end_time:
                file_name = f"天禄项目{start_time.strftime('%Y-%m-%d')}~{end_time.strftime('%Y-%m-%d')}运行分析记录{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            else:
                file_name = f"天禄项目{list(total_analysis_instances)[-1]['start_time'][:10]}~{datetime.datetime.now().strftime('%Y-%m-%d')}运行分析记录{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        else:
            if start_time and end_time:
                file_name = f"Tianlu project{start_time.strftime('%Y-%m-%d')}~{end_time.strftime('%Y-%m-%d')}Operation Analysis Records{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            else:
                file_name = f"Tianlu project{list(total_analysis_instances)[-1]['start_time'][:10]}~{datetime.datetime.now().strftime('%Y-%m-%d')}Operation Analysis Records{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)

        if lang == 'zh':
            sheet1 = f'运行分析记录'
            sheet1_headers = ['编号', '并网点', '分析主题', '状态', '开始时间', '结束时间', '关键参数', '参考阈值',
                              '反馈内容']
        else:
            sheet1 = f'Run analysis records'
            sheet1_headers = ['ID', 'Installation', 'Subject', 'Status', 'Start', 'End', 'References',
                              'Threshold', 'Feedback']

        create_excel(file_path, sheet_name=sheet1, headers=sheet1_headers, data=temp_list)

        download_url = post2minio(file_path, object_name=file_name)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "url": download_url
            },
        })


class AnalysisFeedbackView(APIView):
    """
    运行分析：反馈
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["lang"] = request.headers.get("lang", 'zh')

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            analysis = RunningAnalysis.objects.get(id=id)
        except Exception as e:
            error_log.error("运行分析反馈:运行分析不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR,
                 "data": {"message": "error", "detail": "运行分析不存在" if lang == 'zh' else "Operation Analysis does not exist."}})

        feedback = analysis.feedback

        if feedback:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": {
                            "id": feedback.id,
                            "content": feedback.content if lang == 'zh' else feedback.en_content,
                            "is_dispatch_worker": feedback.is_dispatch_worker,
                            "worker_order": feedback.worker_order,
                            "question_type": feedback.question_type if lang == 'zh' else feedback.en_question_type,
                            "expected_closing_date": feedback.expected_closing_date,
                            "real_closing_date": feedback.real_closing_date,
                            "note": feedback.note if lang == 'zh' else feedback.en_note,
                        }
                    }
                }
            )

        else:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "该运行分析暂无反馈内容" if lang == 'zh' else
                        "There is no feedback content for this operation analysis."
                    }
                }
            )

    def post(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            analysis = RunningAnalysis.objects.get(id=id)
        except Exception as e:
            error_log.error("运行分析反馈:运行分析不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "运行分析不存在" if lang == 'zh' else "Operation Analysis does not exist."}})

        ser = AnalysisFeedbackSerializer(data=request.data, context=self.serializer_context)

        try:
            if not ser.is_valid():
                error_log.error("运行分析反馈:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
        except Exception as e:
            error_log.error("运行分析反馈:字段校验不通过")
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": e.args[0]}})

        feedback = ser.save()
        analysis.feedback = feedback
        analysis.save()

        return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f'反馈内容：写入成功.' if lang == 'zh' else f'Feedback: Write successfully.'
                },
            }
        )

    # def put(self, request, id):
    #     try:
    #         feedback = models.AggrFaultAnalysisFeedback.objects.get(id=id)
    #     except Exception as e:
    #         error_log.error("运行分析反馈:反馈不存在")
    #         return Response(
    #             {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "反馈不存在"}})
    #
    #     ser = AnalysisFeedbackSerializer(instance=feedback, data=request.data, context=self.serializer_context)
    #
    #     if not ser.is_valid():
    #         error_log.error("运行分析反馈:字段校验不通过")
    #         return Response(
    #             {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
    #
    #     ser.save()
    #
    #     return Response({
    #             "code": common_response_code.SUCCESS,
    #             "data": {
    #                 "message": "success",
    #                 "detail": f'反馈内容：{feedback.id}修改成功.'
    #             },
    #         }
    #     )


class AnalysisMessageView(APIView):
    """
    运行分析：推送至消息中心
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["lang"] = request.headers.get("lang", 'zh')

    def post(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            analysis = RunningAnalysis.objects.get(id=id)
        except Exception as e:
            error_log.error("运行分析推送至消息中心:运行分析不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "运行分析不存在" if lang == 'zh' else "Operation Analysis does not exist."}})

        station = analysis.station

        time_str = f'在{analysis.start_time.strftime("%m月%d日 %H:%M:%S")}-{analysis.end_time.strftime("%m月%d日 %H:%M:%S")}' if analysis.status == 1 else f'从{analysis.start_time.strftime("%m月%d日 %H:%M:%S")}开始'
        analysis_title = f"{station.name}{time_str}出现{'已恢复' if analysis.status == 1 else '未恢复'}的{analysis.topic}，建议您查看确认。"

        en_time_str = f'From {analysis.start_time.strftime("%m/%d/%Y %H:%M:%S")}-{analysis.end_time.strftime("%m/%d/%Y %H:%M:%S")}' if analysis.status == 1 else f'From {analysis.start_time.strftime("%m/%d/%Y %H:%M:%S")}'
        en_analysis_title = f"{station.name} {en_time_str} occur {'deactive' if analysis.status == 1 else 'active'} {analysis.en_topic}, please check and confirm."

        receivers = [69, 76, 88, 120, 182, request.user['user_id']]

        for receiver in list(set(receivers)):
            # 首先查询是否推送过，已推送则跳过
            if MessageCenter.objects.filter(related_id=analysis.id, user_id=receiver).exists():
                error_log.error(f"运行分析推送至消息中心: {receiver}已推送过, 不再推送")
                continue

            ins = MessageCenter.objects.create(
                title=analysis_title,
                en_title=en_analysis_title,
                type=2,
                is_read=0,
                is_verify=0,
                user_id=receiver,
                related_id=analysis.id
            )

            # 异步翻译
            # pdr_data = {'id': ins.id,
            #             'table': 't_role',
            #             'update_data': {'title': analysis_title}}
            #
            # pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            # redis_pool.publish(pub_name, json.dumps(pdr_data))

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": f'运行分析推送至消息中心: 推送成功.' if lang == 'zh' else 'Push successfully.'
                }
            }
        )

    def get(self, request, id):
        """
        查看消息：运行分析详情
        """""
        lang = request.headers.get("lang", 'zh')
        try:
            message = MessageCenter.objects.get(id=id)
        except Exception as e:
            error_log.error("消息中心:运行分析消息不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "消息中心:运行分析消息不存在" if lang == 'zh' else "Message center: The Operation Analysis message does not exist."}})

        if message.type == 2:
            analysis = RunningAnalysis.objects.filter(id=message.related_id).first()
            title = f"{analysis.station.name}项目运行分析" if lang == 'zh' else f'{analysis.station.name} Project Operation Analysis'
            description = message.title if lang == 'zh' else message.en_title
            receive_message_time = message.create_time.strftime("%Y-%m-%d %H:%M:%S")
            # related_project_id = aggr_Analysis.station.master_station.project.id
            # Analysis_type = aggr_Analysis.type
            # target_date = aggr_Analysis.start_time.strftime("%Y-%m-%d")
            # Analysis_details = []

            # related_Analysiss = aggr_Analysis.faultAnalysis_set.all().order_by('start_time')
            # for Analysis in related_Analysiss:
            #     Analysis_details.append({
            #         "id": Analysis.id,
            #         "station": Analysis.station.station_name,
            #         "desc": Analysis.details,
            #         "status": Analysis.note,
            #         "start_time": Analysis.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            #         "end_time": Analysis.end_time.strftime("%Y-%m-%d %H:%M:%S") if Analysis.end_time else "--",
            #     })

            if lang == 'zh':
                return_dict = {
                    "title": title,
                    "station_id": analysis.station.id,
                    "description": description,
                    "receive_message_time": receive_message_time,
                    "table": {"station": analysis.station.name,
                              "topic": analysis.topic,
                              "status": analysis.status,
                              "start_time": analysis.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                              "end_time": analysis.end_time.strftime(
                                  "%Y-%m-%d %H:%M:%S") if analysis.end_time else "--",
                              "keyword": analysis.keyword if lang == 'zh' else analysis.en_keyword,
                              "ref_threshold": analysis.ref_threshold if lang == 'zh' else analysis.en_ref_threshold,
                              "feedback": analysis.feedback.content if analysis.feedback else '--'},
                    }
            else:
                return_dict = {
                    "title": title,
                    "station_id": analysis.station.id,
                    "description": description,
                    "receive_message_time": receive_message_time,
                    "table": {"station": analysis.station.name,
                              "topic": analysis.en_topic,
                              "status": analysis.status,
                              "start_time": analysis.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                              "end_time": analysis.end_time.strftime(
                                  "%Y-%m-%d %H:%M:%S") if analysis.end_time else "--",
                              "keyword": analysis.en_keyword,
                              "ref_threshold": analysis.en_ref_threshold,
                              "feedback": analysis.feedback.en_content if analysis.feedback else '--'},
                }

            if analysis.topic != '离线':
                chart_dict = get_station_running_data(analysis.station, analysis.start_time.replace(hour=0,
                                                                                                    minute=0,
                                                                                                    second=0,
                                                                                                    microsecond=0),
                                                      analysis.start_time.replace(hour=23, minute=59, second=59,
                                                                                  microsecond=0), lang)
                return_dict['chart'] = chart_dict

            return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict
                },
            })

        else:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {
                    "message": "success",
                    "detail": "消息中心: 该消息非运行分析消息，无运行分析信息" if lang == 'zh' else
                    "Message center: The message is not a Operation Analysis message and"
                    " there is no Operation Analysis information."
                }
            })


class AnalysisAlarmDetailListView(APIView):
    """查看告警列表: 聚合告警"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context["lang"] = request.headers.get("lang", 'zh')

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        ser = serializers.AnalysisAlarmSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"告警列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"告警列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        now = datetime.datetime.now()

        start_time = ser.validated_data.get('start_time')
        end_time = ser.validated_data.get('end_time')

        try:
            analysis = RunningAnalysis.objects.get(id=id)
        except Exception as e:
            error_log.error("运行分析：分析记录不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error",
                                                                    "detail": f"运行分析：分析记录<id>不存在" if lang == 'zh'
                                                                    else "Operation Analysis:"
                                                                         " The analysis record does not exist."}})

        if analysis.topic == '离线':
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {
                    "message": "success",
                    "detail": "运行分析: 离线状态，无告警信息" if lang == 'zh' else
                    'Operation Analysis: Offline status, no alarm information'
                }
            })

        m_station = analysis.station
        stations = m_station.stationdetails_set.filter(is_delete=0).all()

        if start_time and end_time:
            # print(543, start_time, end_time, type(start_time), type(end_time))
            start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
            end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'
        else:
            start_time_ = analysis.start_time
            end_time_ = analysis.end_time if analysis.end_time else now

        stations_ids = [s.id for s in stations]
        filters = {"start_time__gte": start_time_,
                   "start_time__lte": end_time_,
                   'station_id__in': stations_ids}

        total_fault_alarm_instances = FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
                                                                                          "type", "start_time",
                                                                                          "end_time", "details", "id",
                                                                                          "note", "device",
                                                                                          "station_id", "joint_primary_key"
                                                                                          ).order_by("-start_time")
        ids = [item.get("station_id") for item in total_fault_alarm_instances]
        if ids:
            details = StationDetails.objects.filter(id__in=ids,is_delete=0).all()
            id_name = {item.id:item.station_name for item in details}
        else:
            id_name = {}
        for ins in total_fault_alarm_instances:
            ins["details"] = ins["device"] + ": " + ALARM_ZH_EN_MAP[ins["details"].strip()][lang]
            ins['note'] = ALARM_ZH_EN_MAP[ins["note"].strip().replace(" ", "")][lang]
            ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
            ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S') if ins["end_time"] else '--'
            ins['id'] = ins['joint_primary_key']
            try:
                # ins['station_name'] = StationDetails.objects.get(id=ins['station_id']).station_name
                ins['station_name'] = id_name.get(ins['station_id'])
            except Exception as e:
                pass

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": total_fault_alarm_instances,
                },
            }
        )


def get_station_running_data(m_station, start_time, end_time, topic, lang='zh'):
    """
    获取运行数据
    :param m_station:
    :param start_time:  一天的 00:00:00
    :param end_time:    同一天的23:59:59
    :return:
    """""
    connection = pymysql.connect(**{
        "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
        "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
        "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
        "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
        "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
        "cursorclass": pymysql.cursors.DictCursor
    })

    types_ = ['0', '1', '2']  # 0：电网侧功率； 1：SOC；2：有功功率；
    # 连接清洗库
    cursor = connection.cursor()

    return_dict = {}

    for type_ in types_:
        if type_ == '0' or type_ == '2':
            # slave_station = m_station.stationdetails_set.filter(Q(slave=-1)|Q(slave=0), Q(pack=-1)|Q(pack=0)).all()
            # 兼容标准站 & 标准主从站 & EMS级联主从站模式
            slave_station = m_station.stationdetails_set.filter(is_delete=0, english_name=m_station.english_name).all()
        else:
            slave_station = m_station.stationdetails_set.filter(is_delete=0).all()
        # slave_station = m_station.stationdetails_set.filter(~Q(slave=0)).all()
        bms_unit_list = []
        pcs_unit_list = []
        station_list = []
        m_station_frontloaded_list = []  # 电表前置列表
        m_station_frontloaded_pcs_list = []  # 电表前置pcs列表
        # 处理主从模式
        for s_info in slave_station:
            if s_info.meter_position == 1:
                m_station_frontloaded_list.append(s_info.english_name)
                units = s_info.unit_set.filter(is_delete=0).values('pcs')
                for unit in units:
                    m_station_frontloaded_pcs_list.append(unit.get('pcs'))
            station_list.append(s_info.english_name)
            # 处理所有单元
            units = s_info.unit_set.filter(is_delete=0).values('bms', 'pcs')
            for unit in units:
                bms_unit_list.append(unit.get('bms'))
                pcs_unit_list.append(unit.get('pcs'))

        # 处理开始结束时间
        # s_time = inquire_time + ' 00:00:00'
        # e_time = inquire_time + ' 23:59:59'

        # 时间节点，防止某时段数据为空不返回
        # SOC
        if type_ == '1' and topic != "反送电":
            sql = """SELECT  device, soc, DATE_FORMAT(time,'%H:%i') as time
                                      FROM dwd_measure_bms_data_storage_3
                                      WHERE 
                                      time 
                                      BETWEEN '{}'
                                      AND '{}'
                                      AND MOD(MINUTE(time), 5) = 0 """.format(start_time, end_time)
            if len(station_list) == 1:
                sql += "AND station_name = '{}'".format(station_list[0])
            else:
                sql += "AND station_name in {}".format(tuple(station_list))
            if len(bms_unit_list) == 1:
                sql += "AND device = '{}'".format(bms_unit_list[0])
            else:
                sql += 'AND device in {}'.format(tuple(bms_unit_list))
            try:
                # 获取查询结果
                cursor.execute(sql)
                result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )
            soc_res = {}
            for i in result:
                t = i.get('time')
                soc = i.get('soc')
                if soc_res.get(t):
                    soc_res[t].append(soc)
                else:
                    soc_res[t] = [soc]
            data = create_time_mapping()
            bms_count = len(bms_unit_list)
            for k, v in soc_res.items():
                if len(v) == bms_count:
                    if None not in v:
                        data[k] = round(sum(v) / len(v), 1)

            return_dict['soc_array'] = data

        # 电网侧功率
        elif type_ == '0':
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                        DATE_FORMAT(time,'%H:%i') as time, p_gird
                                    FROM
                                        ads_report_loading_data
                                    WHERE
                                        time
                                        BETWEEN '{}'
                                        AND '{}'
                                        AND state_pcc = 0
                                        """.format(start_time, end_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                p_sql += "ORDER BY time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                            }
                        }
                    )
            dwd_sql = """SELECT
                                        mfixdemandref,
                                        tfm,
                                        tprt,
                                        device,
                                        DATE_FORMAT(time,'%H:%i') as time
                                FROM dwd_measure_ems_data_storage
                                WHERE
                                    time
                                    BETWEEN '{}'
                                    AND '{}'
                                    AND device = 'EMS'
                                    AND MOD(MINUTE(time), 5) = 0                                    
                                     """.format(start_time, end_time)

            if len(station_list) == 1:
                dwd_sql += "AND station_name = '{}'".format(station_list[0])
            else:
                dwd_sql += "AND station_name in {}".format(tuple(station_list))

            try:
                # 获取查询结果
                cursor.execute(dwd_sql)
                dwd_result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                        }
                    }
                )

            data = {
                'required_power': create_time_mapping(),  # 需量功率
                'transformer_safety_capacity': create_time_mapping(),  # 变压器安全容量
                'grid_side_power': create_time_mapping(),  # 电网侧功率
            }

            dwd_res = {i.get('time'): [i.get('mfixdemandref'), i.get('tfm'), i.get('tprt')] for i in dwd_result}
            for k, v in dwd_res.items():
                if v[0] is not None:
                    if data['required_power'].get(k):
                        data['required_power'][k] = v[0]
                if v[2] is not None:
                    if data['transformer_safety_capacity'].get(k):
                        data['transformer_safety_capacity'][k] = v[2] * v[1]
            for i in p_result:
                t = i[0]
                if i[1] is not None:
                    if data['grid_side_power'].get(t):
                        data['grid_side_power'][t] = i[1]

            if topic == "反送电":
                del data['required_power']
                del data['transformer_safety_capacity']
                return_dict['power_array'] = data
            else:
                return_dict['power_array'] = data

        # 有功功率
        elif type_ == '2':
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                        DATE_FORMAT(time,'%H:%i') as time, sum(p_load) as p_load, sum(p) as p
                                    FROM
                                        ads_report_loading_data
                                    WHERE
                                        time
                                        BETWEEN '{}'
                                        AND '{}'
                                        AND state_pcc = 0
                                        """.format(start_time, end_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                p_sql += " GROUP BY time ORDER BY time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                            }
                        }
                    )
            data = {
                'pcc': create_time_mapping(),  # 负荷功率
                'p': create_time_mapping(),  # 储能功率
                'aim_power': create_time_mapping()  # 目标功率
            }

            # master_station = MaterStation.objects.filter(id=id).first()
            power = m_station.stationdetails_set.filter(is_delete=0).aggregate(p_sum=Sum('rated_power'))
            power = power.get('p_sum')
            tactics_sql = f"""
                                SELECT
                                       time, station_name, device, slave, pack, type, ots, rlh1p, rlh2p, rlh3p, rlh4p, rlh5p, rlh6p, rlh7p, rlh8p, rlh9p, 
                                        rlh10p, rlh11p, rlh12p, rlh13p, rlh14p, rlh15p, rlh16p, rlh17p, rlh18p, rlh19p, rlh20p, rlh21p, rlh22p, rlh23p, rlh24p,
                                         rlh1f, rlh2f, rlh3f, rlh4f, rlh5f, rlh6f, rlh7f, rlh8f, rlh9f, rlh10f, rlh11f, rlh12f, rlh13f, rlh14f, rlh15f, rlh16f, 
                                         rlh17f, rlh18f, rlh19f, rlh20f, rlh21f, rlh22f, rlh23f, rlh24f, rlh25p, rlh26p, rlh27p, rlh28p, rlh29p, rlh30p, rlh31p, 
                                         rlh32p, rlh33p, rlh34p, rlh35p, rlh36p, rlh37p, rlh38p, rlh39p, rlh40p, rlh41p, rlh42p, rlh43p, rlh44p, rlh45p, rlh46p,
                                          rlh47p, rlh48p, rlh25f, rlh26f, rlh27f, rlh28f, rlh29f, rlh30f, rlh31f, rlh32f, rlh33f, rlh34f, rlh35f, rlh36f, rlh37f,
                                           rlh38f, rlh39f, rlh40f, rlh41f, rlh42f, rlh43f, rlh44f, rlh45f, rlh46f, rlh47f, rlh48f
                                FROM
                                    `dwd_measure_ems_data_storage_tscale` 
                                WHERE
                                    station_name = '{m_station.english_name}' 
                                    AND time BETWEEN '{start_time}' 
                                    AND '{end_time}' 
                                    AND device='EMS'
                                    AND MOD(MINUTE(time), 5) = 0 
                                ORDER BY
                                    time DESC 
                                    LIMIT 1
                                """
            try:
                # 获取查询结果
                cursor.execute(tactics_sql)
                tactics_result = cursor.fetchone()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                        }
                    }
                )
            for i in p_result:
                t = i[0]
                if i[1] is not None:
                    if data['pcc'].get(t):
                        data['pcc'][t] = i[1]
                    # k1：小时；k2：分钟;
                    k1 = int(t[:2])
                    k1 = 24 if k1 == 0 else k1
                    k2 = int(t[3:5])
                    k = k1 if k2 <= 30 else k1 + 24  # 策略修改为半小时一个点位，通过判断分钟是否小于等于半小时，来计算取对应的点位
                    if tactics_result:
                        if k > 24 and tactics_result.get(f'rlh{k}p') is None:
                            k = k - 24
                        if tactics_result.get(f'rlh{k}p') is not None and tactics_result.get(f'rlh{k}f') is not None:
                            if data['aim_power'].get(t):
                                data['aim_power'][t] = float(tactics_result.get(f'rlh{k}p')) * float(
                                    tactics_result.get(f'rlh{k}f')) * power
                        else:
                            if data['aim_power'].get(t):
                                data['aim_power'][t] = '--'
                    else:
                        if data['aim_power'].get(t):
                            data['aim_power'][t] = '--'
                if i[2] is not None:
                    if data['p'].get(t):
                        data['p'][t] = i[2]

            return_dict['pcc_array'] = data

        else:
            pass

    cursor.close()
    connection.close()

    return_dict['target_day'] = start_time.strftime('%Y-%m-%d')
    return return_dict


class StationRunningDataviewView(APIView):
    """
    运行分析：查看运行数据
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
                                          )

    def post(self, request):
        """
        pcc_array
           p 储能功率
           pcc 负荷功率
           aim_power 目标功率
        power_array grid_side_power 电网侧功率
        """
        lang = request.headers.get("lang", 'zh')
        id = request.data.get('id')  # 分析记录ID
        target_day = request.data.get('target_day')  # 查询日期
        # types_ = ['0', '1', '2']  # 0：电网侧功率； 1：SOC；2：有功功率；
        request_id = request.user["user_id"]
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '运行分析：ID字段是必填项！' if lang == 'zh' else 'Operation Analysis: The ID field is required.',
                    }
                }
            )
        try:
            analysis = RunningAnalysis.objects.get(id=id)
            m_station = analysis.station
            topic = analysis.topic
        except MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '运行分析：该分析记录并网点不存在或当前用户没有权限，请检查参数！' if lang == 'zh' else
                        'Operation Analysis: The analysis record connection point does not exist'
                        ' or the current user does not have permissions, please check the parameters!',
                    }
                }
            )

        if analysis.topic == '离线':
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": '运行分析：离线状态不支持查看运行数据！' if lang == 'zh' else
                        'Operation Analysis: Offline status does not support viewing running data!',
                    }
                }
            )

        if target_day:
            start_time = datetime.datetime.strptime(target_day + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
            end_time = datetime.datetime.strptime(target_day + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
        else:
            start_time = analysis.start_time.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = analysis.start_time.replace(hour=23, minute=59, second=59, microsecond=0)

        return_dict = get_station_running_data(m_station, start_time, end_time, topic, lang)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": return_dict,
                }
            }
        )