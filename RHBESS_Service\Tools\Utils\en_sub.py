import json
import logging
from Tools.Utils.num_utils import Translate_cls, user_email
from Tools.DB.redis_con import r_real
from Tools.Utils.send_mail import sendMail_
# from Tools.DecisionDB.ele_base import user_session
from Tools.DB.mysql_user import user_session
# 中文转英文
t_cls = Translate_cls(2)

def sub():
    """
    订阅
    :return:
    """
    # 获取中译英翻译数据订阅
    pub = r_real.pubsub()
    pub.subscribe('en_translate_pub')
    # 监听
    msg_stream = pub.listen()
    for msg in msg_stream:
        if msg["type"] == "message":
            data = json.loads(msg['data'])
            id = data.get('id')
            table = data.get('table')
            info = data.get('update_data')
            sql = ''
            for k, v in info.items():
                sql += f"{'en_' + k} = '{json.dumps(t_cls.str_chinese(v))}',"
            if sql:
                sql = sql[:-1]
                u_sql = f"UPDATE `dm_user`.`{table}` SET {sql} WHERE `id` = {id};"
                try:
                    user_session.execute(u_sql)
                    user_session.commit()
                    print('执行完成')
                except Exception as e:
                    logging.error(f'中译英翻译写入数据失败：翻译表：{table}；数据ID：{id}, 错误信息：{e}')
                    sendMail_(f"您的异步翻译异常，请关注 数据表：{table}；数据ID：{id}, 错误信息：{e}", "异步翻译异常消息通知：中译英", "山海系统", "XXX", user_email)
        elif msg["type"] == "subscribe":
            print(str(msg["channel"]), "订阅成功")


if __name__ == '__main__':
    sub()