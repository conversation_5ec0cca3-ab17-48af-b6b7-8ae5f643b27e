package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.TUserStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户策略Mapper
 * 对应Python中的TUserStrategy相关数据库操作
 */
@Mapper
public interface TUserStrategyMapper extends BaseMapper<TUserStrategy> {

    /**
     * 根据用户ID查询策略列表
     */
    @Select("SELECT * FROM t_user_strategy " +
            "WHERE user_id = #{userId} AND is_delete = 0 AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TUserStrategy> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和策略名称查询策略
     */
    @Select("SELECT * FROM t_user_strategy " +
            "WHERE user_id = #{userId} AND name = #{name} AND is_delete = 0 AND is_use = 1")
    TUserStrategy selectByUserIdAndName(@Param("userId") Long userId, @Param("name") String name);

    /**
     * 软删除策略
     */
    @Update("UPDATE t_user_strategy SET is_delete = 1, update_time = NOW() WHERE id = #{id}")
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据策略名称模糊查询
     */
    @Select("SELECT * FROM t_user_strategy " +
            "WHERE name LIKE CONCAT('%', #{name}, '%') AND is_delete = 0 AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TUserStrategy> selectByNameLike(@Param("name") String name);

    /**
     * 统计用户策略数量
     */
    @Select("SELECT COUNT(*) FROM t_user_strategy " +
            "WHERE user_id = #{userId} AND is_delete = 0 AND is_use = 1")
    int countByUserId(@Param("userId") Long userId);
}
