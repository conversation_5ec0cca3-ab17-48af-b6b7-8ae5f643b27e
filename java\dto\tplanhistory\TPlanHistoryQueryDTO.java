package com.robestec.analysis.dto.tplanhistory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 计划历史记录查询DTO
 */
@Data
@ApiModel("计划历史记录查询DTO")
public class TPlanHistoryQueryDTO {

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("状态: 1-已保存, 2-已下发, 3-执行中, 4-已完成, 5-下发失败, 6-已停止")
    private Integer status;

    @ApiModelProperty("计划类型: 1-自定义, 2-周期性, 3-节假日")
    private Integer planType;

    @ApiModelProperty("是否跟随: 1-是, 0-否")
    private Integer isFollow;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
