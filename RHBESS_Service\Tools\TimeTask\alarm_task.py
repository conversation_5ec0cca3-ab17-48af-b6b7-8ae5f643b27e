#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/11/15 下午4:55
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import json
from Tools.DB.redis_con import r_real

from apscheduler.schedulers.blocking import BlockingScheduler
from Tools.DB.mysql_user import user_session
from Application.Models.User.event import Event
from sqlalchemy import func,or_
from Application.Models.User.alarm_r import AlarmR
from Application.Models.WorkOrder.dispatch_r import DispatchR



def get_station_alarm_dispatch_data():
    t_alarm = user_session.query(Event.station, func.count(Event.id)).filter(
        Event.type_id == 5, Event.type == 2,AlarmR.event_id == Event.id,
        or_(AlarmR.value_descr == '故障',AlarmR.value_descr == '报警')
    ).group_by(Event.station).all()
    if t_alarm:
        t_alarm_dict = [{item[0]: item[1]} for item in t_alarm]
        r_real.hset("monitor", "alarm_data", json.dumps(t_alarm_dict))

    dispatchR = user_session.query(DispatchR.station).filter(DispatchR.working_flag == '2').all()  # 工单数量
    if dispatchR:
        data_str = ";".join([",".join(item) for item in dispatchR])
        r_real.hset("monitor", "dispatch", data_str)


def get_dispatch_data():
    pass
    # stored_value = r_real.hget("monitor", "dispatch")
    # # 将字符串转换回数据列表
    # stored_data = [tuple(item.split(",")) for item in stored_value.decode('utf-8').split(";")]

def run():
    # 先执行一次保存到redis
    get_station_alarm_dispatch_data()
    scheduler = BlockingScheduler()
    scheduler.add_job(get_station_alarm_dispatch_data, 'interval', hours=12)  # 10分钟获取一次数据

    scheduler.start()

if __name__ == '__main__':
    run()
