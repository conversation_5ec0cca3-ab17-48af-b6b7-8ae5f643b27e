#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-27 09:45:14
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\income_r.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-06 14:34:35


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Tools.Utils.time_utils import timeUtils

class RIncome(user_Base):
    u'收益数据'
    __tablename__ = "r_income"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    income = Column(VARCHAR(50), nullable=False, comment=u"收益（万元）")
    is_use = Column(CHAR(1), nullable=False,comment=u"是否启用")
    station_name = Column(VARCHAR(50), nullable=False,comment=u"站点名称")
    in_ts = Column(CHAR(1), nullable=False,comment=u"1是年，2是月")
    op_ts = Column(DateTime, nullable=False,comment=u"时标")
    day = Column(VARCHAR(10), nullable=False,comment=u"时间：YYYY-mm-dd")
    target_income = Column(VARCHAR(50), nullable=True, comment=u"目标收益")
    descr = Column(VARCHAR(255), nullable=True, comment=u"描述")
    en_descr = Column(VARCHAR(255), nullable=True, comment=u"描述-英文")
    time_frame = Column(VARCHAR(50), nullable=True, comment=u"时间范围")
    income_ty = Column(VARCHAR(50), nullable=False, comment=u"收益类别")
    en_income_ty = Column(VARCHAR(50), nullable=False, comment=u"收益类别-英文")
    chag = Column(VARCHAR(50), nullable=True, comment=u"充电量")
    disg = Column(VARCHAR(50), nullable=True, comment=u"放电量")
    income_yu = Column(VARCHAR(50), nullable=False, comment=u"收益(元)")
    remark = Column(VARCHAR(255), nullable=True, comment=u"备注")
    en_remark = Column(VARCHAR(500), nullable=True, comment=u"备注-英文")
    annex = Column(VARCHAR(500), nullable=True, comment=u"附件地址")
    filename = Column(VARCHAR(500), nullable=True, comment=u"附件名称")
    en_filename = Column(VARCHAR(500), nullable=True, comment=u"附件名称-英文")
    annex_in = Column(VARCHAR(50), nullable=True, comment=u"附件地址下标")


    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        # now = timeUtils.getNewTimeStr()
        # user_session.merge(Station(id=1,name='halun',descr='哈伦6MW/3MWh储能电站',op_ts=now,index=3));
        # user_session.merge(Station(id=2,name='taicang',descr='太仓9MW/4.5MWh储能电站',op_ts=now,index=2));
        # user_session.merge(Station(id=3,name='binhai',descr='滨海18MW/9MWh储能电站',op_ts=now,index=1));
        # user_session.merge(Station(id=4,name='ygzhen',descr='永臻5MW/18MWh储能电站',op_ts=now,index=4));
        #
        # user_session.commit()
        # user_session.close()
        
    def __repr__(self):
        bean = "{'id':%s,'income':'%s','is_use':'%s','station_name':'%s','in_ts':'%s','op_ts':'%s','day':'%s','target_income':'%s','descr':'%s','time_frame':'%s','income_ty':'%s','chag':'%s','disg':'%s','income_yu':'%s','remark':'%s','annex':'%s','filename':'%s','annex_in':'%s'," \
               "'en_descr':'%s','en_income_ty':'%s','en_remark':'%s','en_filename':'%s'}" % \
               (self.id,self.income,self.is_use,self.station_name,self.in_ts,self.op_ts,self.day,self.target_income,self.descr,self.time_frame,self.income_ty,self.chag,self.disg,self.income_yu,self.remark,self.annex,self.filename,self.annex_in,
                self.en_descr,self.en_income_ty,self.en_remark,self.en_filename)
        return bean.replace("None",'')
        
    def deleteRIncome(self,id):
        try:
            user_session.query(RIncome).filter(RIncome.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False