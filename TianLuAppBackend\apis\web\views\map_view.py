import json
from decimal import Decimal
import concurrent.futures
from django.db.models import Count
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from apis.user import models
from common import common_response_code
from middlewares.authentications import JwtParamAuthentication, JW<PERSON>eaderAuthentication, DenyAuthentication
from tools.day_hours_used import pool

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# Create your views here.


class AuthMapView(APIView):
    """web内部人员大屏地图"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def _get_province_data(self, province, user_ins):

        conn = pool.connection()
        cursor = conn.cursor()

        offline_list = []
        Fault_list = []
        alarm_list = []

        province_ins = models.Project.objects.filter(province__name=province["province__name"], is_used=1).first()
        province["longitude"] = province_ins.longitude
        province["latitude"] = province_ins.latitude
        # station_instances = models.StationDetails.objects.filter(province__name=province["province__name"],
        #                                                          userdetails=user_ins)

        master_stations = models.MaterStation.objects.filter(project=province_ins,
                                                             userdetails=user_ins, is_delete=0).all()

        if master_stations.exists():
            for master_station in master_stations:
                units = models.Unit.objects.filter(is_delete=0, station__master_station=master_station).all()
                for unit in units:
                    conn_ = get_redis_connection("3")
                    key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name,
                                                                   unit.pcs)
                    status_pcs = conn_.get(key1)
                    if status_pcs:
                        status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                    else:
                        status_pcs_dict = {}

                    key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.station.english_name,
                                                                   unit.bms)
                    status_bms = conn_.get(key2)
                    if status_bms:
                        status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
                    else:
                        status_bms_dict = {}

                    # try:
                    #     result_3 = select_realtime_data_from_rhyc(cursor, unit.station.app, unit.station.station_name, 'status', unit.pcs, "Fault", "alarm",)
                    #     result_7 = select_realtime_data_from_rhyc(cursor, unit.station.app, unit.station.station_name, 'status', unit.bms, "GFault", "GAlarm",)
                    #
                    #
                    # except Exception as e:
                    #     error_log.error("select realtime data from rhyc：error: {}".format(e))
                    #     try:
                    #         cursor.close()
                    #         conn.close()
                    #     except:
                    #         pass
                    #     return Response(
                    #         {
                    #             "code": common_response_code.ERROR,
                    #             "data": {
                    #                 "message": "fail",
                    #                 "detail": "安昌数据库查询失败",
                    #             },
                    #         }
                    #     )

                    if not status_pcs_dict:
                        offline_list.append(1)  # 离线状态

                    else:
                        GFault = status_bms_dict.get("GFault", -2)  # bms故障状态
                        GAlarm = status_bms_dict.get("GAlarm", -2)  # bms警告状态
                        Fault = status_pcs_dict.get("Fault", -2)  # pcs故障状态
                        alarm = status_pcs_dict.get("alarm", -2)  # pcs警告状态
                        # print(station.english_name, GFault, GAlarm, Fault, alarm)
                        if Fault == '1' or Fault == 1:
                            Fault_list.append(1)
                        if GFault == '1' or GFault == 1:
                            Fault_list.append(1)
                        if alarm == '1' or alarm == 1:
                            alarm_list.append(1)
                        if GAlarm == '1' or GAlarm == 1:
                            alarm_list.append(1)
        province["status"] = 1
        if 1 in alarm_list:
            province["status"] = 2
        if 1 in Fault_list:
            province["status"] = 3
        if 1 in offline_list:
            province["status"] = 4

        try:
            cursor.close()
            conn.close()
        except Exception as e:
            pass
                    
        return province

    def get(self, request):
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)
        count_province = models.Project.objects.values("province__name").filter(user=user_ins, is_used=1).annotate(count=Count('id'))

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = list()
            for province in count_province:
                future = executor.submit(self._get_province_data, province, user_ins)
                futures.append(future)
            results = [f.result() for f in concurrent.futures.as_completed(futures)]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "stations": results,
                },
            }
        )


class MapHealthView(APIView):
    """地图大屏健康度"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]
        
        user_ins = models.UserDetails.objects.get(id=user_id)
        project_ins = models.Project.objects.filter(user=user_ins, is_used=1).all()
        stations_ins = models.StationDetails.objects.filter(is_delete=0, master_station__project__in=project_ins,
                                                            master_station__is_delete=0).all()

        unit_count = sum([Decimal(station_ins.battery_cluster) for station_ins in stations_ins])
        # 判断data中大于等于 90 的数量
        count_dic = {"count_gte_90": unit_count, "count_gte_80": 0, "count_gte_60": 0, "count_lte_60": 0}

        # 百分比计算
        count_dic["count_gte_90_per"] = Decimal(unit_count / unit_count * 100).quantize(Decimal("0.00"))

        count_dic["count_gte_80_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_gte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))
        count_dic["count_lte_60_per"] = Decimal(0 / unit_count * 100).quantize(Decimal("0.00"))

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "health": count_dic,
                },
            }
        )


class UserMapView(APIView):
    """web客户大屏地图"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_id = request.user["user_id"]
        user_ins = models.UserDetails.objects.get(id=user_id)
        projects = models.Project.objects.filter(user=user_ins, is_used=1).values(
            "province__name", "name", "city", "longitude", "latitude", "english_name"
        )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "projects": projects,
                },
            }
        )
