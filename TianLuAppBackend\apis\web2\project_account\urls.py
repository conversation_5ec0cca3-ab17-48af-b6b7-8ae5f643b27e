# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/4/1 14:08
# <AUTHOR> <PERSON><PERSON>ang
# @Project  : TianLuAppBackend
# @File     : urls.py
# @Software : PyCharm
from django.urls import path

from apis.web2.project_account.views import custom_price_views, account_views

urlpatterns = [
    path('account_manage', account_views.ProjectAccountView.as_view()),     # 结算管理表格接口      en
    path('account_details', account_views.ProjectAccountDetailView.as_view()),      # 充放电明细查询接口     en
    path('account_price', account_views.ProjectAccountPriceView.as_view()),      # 电价查询列表/弹框表格接口    en
    path('export_account_data', account_views.ExportProjectAccountExcelView.as_view()),      # 导出excel/pdf接口      en

    # 单位电价配置
    path("custom/options", custom_price_views.CustomizationOptionsView.as_view()),  # 代理购电价格查询下拉选项      en
    path("custom/price/detail", custom_price_views.CustomizationDetailView.as_view()),  # 代理购电电价查询      en
    path("custom/add", custom_price_views.CustomizationsView.as_view()),  # 单位电价添加      en
    path("custom/update", custom_price_views.CustomizationView.as_view()),  # 单位电价修改        en
    path("custom/detail", custom_price_views.CustomizationView.as_view()),  # 单位电价详情        en
    path("custom/list", custom_price_views.CustomizationsView.as_view()),  # 单位电价列表     en
    path("custom/delete", custom_price_views.CustomizationView.as_view()),  # 单位电价删除        en
    path("custom/stations", custom_price_views.CustomizationMasterStationsView.as_view()),  # 单位电价:电站列表     en

]