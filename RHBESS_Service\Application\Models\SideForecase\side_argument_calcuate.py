import datetime

from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, ForeignKey, Column, DateTime, Text, relationship


class ArgumentCalcuate(user_Base):
    u'经评计算历史参数'
    __tablename__ = "t_side_argument_calcuate"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_id = Column(Integer, ForeignKey("t_side_forecase_user.id"), nullable=False, comment="创建用户")
    create_time = Column(DateTime, nullable=False, comment="创建时间", default=datetime.datetime.now())
    data = Column(Text, nullable=True,comment=u"参数集合")
    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")

    project = relationship("ForecaseProject", backref="project")
    user = relationship("ForecaseUser", backref="user")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()


