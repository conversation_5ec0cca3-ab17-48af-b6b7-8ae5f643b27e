#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2025/1/14 下午3:51
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me


from Application.Models.User.outage_record import (OutageRecord, DeviceChildUnit, OutageRecordDictionary,
                                                   OutageEffectPeriodRecord, OutageSpareUseRecord, OutageEffectRange)
from Tools.DB.mysql_user import user_session
from Application.Models.User.device_unit_t import DeviceUnit
from sqlalchemy import or_, func
from Application.Models.User.station import Station

def initialize_affect_time():
    # 查询出所有没有影响时长的数据
    filters = [or_(
        OutageRecord.affect_time.is_(None),
        OutageRecord.affect_time == '',
        OutageRecord.record_no.is_(None),
        OutageRecord.record_no == ''
    ),OutageRecord.is_use == 1]
    out_record = user_session.query(OutageRecord).filter(*filters).order_by(OutageRecord.id.desc()).all()
    for item in out_record:
        record_no = get_record_no(item.occur_time, item.project_id, item.area)
        affect_time = get_affect_time(item.id)
        if record_no:
            item.record_no = record_no
        item.affect_time = affect_time
        user_session.commit()


def get_record_no(occur_time, project_id, area):
    """
    获取记录编号
    :param occur_time: 发生日期 yymmdd
    :param project_id: 项目id
    :param area: 分区
    :return:
    """
    station_info = user_session.query(Station).filter(
        Station.id == project_id).first()
    if not station_info:
        return False
    station_name =  station_info.name
    if area is not None:
        fault_unit = area.split('#')[0]  # 故障单元
    else:
        fault_unit = 'XXX'
    # 查询出当前故障单元已有几条记录 2024-05-07 16:16
    occur_time = occur_time.strftime("%Y-%m-%d %H:%M:%S")
    occur_start_time = occur_time[0:10] + ' 00:00:00'
    occur_end_time = occur_time[0:10] + ' 23:59:59'
    if fault_unit == 'XXX':
        record_count = user_session.query(func.count(OutageRecord.id)).filter(
            OutageRecord.occur_time.between(occur_start_time, occur_end_time),
            OutageRecord.area == None,
            OutageRecord.is_use == 1,
            OutageRecord.record_no.like('%' + fault_unit + '%')
        ).scalar()
    else:
        record_count = user_session.query(func.count(OutageRecord.id)).filter(
            OutageRecord.occur_time.between(occur_start_time, occur_end_time),
            OutageRecord.record_no.like('%' + fault_unit + '%'),
            OutageRecord.is_use == 1,
        ).scalar()
    # 故障编号规则项目点号（取自 “项目信息管理”-“英文名称”）+发生日期（yymmdd）+发生故障单元（取自对应故障记录的“分区”信息“#”及之前的字符组合，如C11#、D01#，04#）+自然序号（比如同一个单元在同一天发生2次故障记录，则其自然序号分别为01、02）
    record_no = station_name + str(occur_time[0:10]).replace('-', '') + fault_unit + "#" + str(record_count + 1).zfill(2)
    return record_no


def get_affect_time(outage_id):
    """
    计算影响时长
    """
    effect_periods_by_outage_id = user_session.query(OutageEffectPeriodRecord).filter(
        OutageEffectPeriodRecord.outage_id == int(outage_id), OutageEffectPeriodRecord.is_use == 1).all()
    total_effect_seconds = 0
    if effect_periods_by_outage_id:
        for item in effect_periods_by_outage_id:
            total_effect_seconds += (item.end_time - item.begin_time).total_seconds()
    return str(round(total_effect_seconds / 60))


if __name__ == '__main__':
    initialize_affect_time()