from django_filters import rest_framework as filters

from apis.user.models import UserDetails


class UserDetailsFilter(filters.FilterSet):
    user_name = filters.CharFilter(field_name='user_name')
    login_name = filters.CharFilter(field_name='login_name')
    id = filters.CharFilter(field_name='id')
    mobile = filters.CharFilter(field_name='mobile')
    gender = filters.CharFilter(field_name='gender')
    tissue = filters.CharFilter(field_name='tissue')
    email = filters.CharFilter(field_name='email')


    class Meta:
        model = UserDetails
        fields = ["id", "user_name", "login_name", "mobile", "gender", "tissue", "email"]

    def filter_by_(self, queryset, username,tissue):
        return queryset.filter(user_name__startswith=username,tissue=tissue)

    def filter_by_tissue(self, queryset, tissue):
        return queryset.filter(tissue__startswith=tissue)

    def filter_by_username(self, queryset, username):
        return queryset.filter(user_name__startswith=username)