#!/usr/bin/env python
# coding=utf-8
#@Information:广州保电项目
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql



HOUMAA1_HOSTNAME = model_config.get('mysql', "HOUMAA1_HOSTNAME")
HOUMAA1_PORT = model_config.get('mysql', "HOUMAA1_PORT")
HOUMAA1_DATABASE = model_config.get('mysql', "HOUMAA1_DATABASE")
HOUMAA1_USERNAME = model_config.get('mysql', "HOUMAA1_USERNAME")
HOUMAA1_PASSWORD = model_config.get('mysql', "HOUMAA1_PASSWORD")

HOUMAA2_HOSTNAME = model_config.get('mysql', "HOUMAA2_HOSTNAME")
HOUMAA2_PORT = model_config.get('mysql', "HOUMAA2_PORT")
HOUMAA2_DATABASE = model_config.get('mysql', "HOUMAA2_DATABASE")
HOUMAA2_USERNAME = model_config.get('mysql', "HOUMAA2_USERNAME")
HOUMAA2_PASSWORD = model_config.get('mysql', "HOUMAA2_PASSWORD")

HOUMAB1_HOSTNAME = model_config.get('mysql', "HOUMAB1_HOSTNAME")
HOUMAB1_PORT = model_config.get('mysql', "HOUMAB1_PORT")
HOUMAB1_DATABASE = model_config.get('mysql', "HOUMAB1_DATABASE")
HOUMAB1_USERNAME = model_config.get('mysql', "HOUMAB1_USERNAME")
HOUMAB1_PASSWORD = model_config.get('mysql', "HOUMAB1_PASSWORD")

HOUMAB2_HOSTNAME = model_config.get('mysql', "HOUMAB2_HOSTNAME")
HOUMAB2_PORT = model_config.get('mysql', "HOUMAB2_PORT")
HOUMAB2_DATABASE = model_config.get('mysql', "HOUMAB2_DATABASE")
HOUMAB2_USERNAME = model_config.get('mysql', "HOUMAB2_USERNAME")
HOUMAB2_PASSWORD = model_config.get('mysql', "HOUMAB2_PASSWORD")

hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMAA1_USERNAME,
    HOUMAA1_PASSWORD,
    HOUMAA1_HOSTNAME,
    HOUMAA1_PORT,
    HOUMAA1_DATABASE
)
houmaa1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_houmaa1_session = scoped_session(sessionmaker(houmaa1_engine,autoflush=True))
houmaa1_Base = declarative_base(houmaa1_engine)
houmaa1_session = _houmaa1_session()

hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMAA2_USERNAME,
    HOUMAA2_PASSWORD,
    HOUMAA2_HOSTNAME,
    HOUMAA2_PORT,
    HOUMAA2_DATABASE
)
houmaa2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houmaa2_session = scoped_session(sessionmaker(houmaa2_engine,autoflush=True))
houmaa2_Base = declarative_base(houmaa2_engine)
houmaa2_session = _houmaa2_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMAB1_USERNAME,
    HOUMAB1_PASSWORD,
    HOUMAB1_HOSTNAME,
    HOUMAB1_PORT,
    HOUMAB1_DATABASE
)
houmab1_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houmab1_session = scoped_session(sessionmaker(houmab1_engine,autoflush=True))
houmab1_Base = declarative_base(houmab1_engine)
houmab1_session = _houmab1_session()

hisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HOUMAB2_USERNAME,
    HOUMAB2_PASSWORD,
    HOUMAB2_HOSTNAME,
    HOUMAB2_PORT,
    HOUMAB2_DATABASE
)
houmab2_engine = create_engine(hisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_houmab2_session = scoped_session(sessionmaker(houmab2_engine,autoflush=True))
houmab2_Base = declarative_base(houmab2_engine)
houmab2_session = _houmab2_session()


SHOUMAA1_HOSTNAME = model_config.get('mysql', "SHOUMAA1_HOSTNAME")
SHOUMAA1_PORT = model_config.get('mysql', "SHOUMAA1_PORT")
SHOUMAA1_DATABASE = model_config.get('mysql', "SHOUMAA1_DATABASE")
SHOUMAA1_USERNAME = model_config.get('mysql', "SHOUMAA1_USERNAME")
SHOUMAA1_PASSWORD = model_config.get('mysql', "SHOUMAA1_PASSWORD")

SHOUMAA2_HOSTNAME = model_config.get('mysql', "SHOUMAA2_HOSTNAME")
SHOUMAA2_PORT = model_config.get('mysql', "SHOUMAA2_PORT")
SHOUMAA2_DATABASE = model_config.get('mysql', "SHOUMAA2_DATABASE")
SHOUMAA2_USERNAME = model_config.get('mysql', "SHOUMAA2_USERNAME")
SHOUMAA2_PASSWORD = model_config.get('mysql', "SHOUMAA2_PASSWORD")

SHOUMAB1_HOSTNAME = model_config.get('mysql', "SHOUMAB1_HOSTNAME")
SHOUMAB1_PORT = model_config.get('mysql', "SHOUMAB1_PORT")
SHOUMAB1_DATABASE = model_config.get('mysql', "SHOUMAB1_DATABASE")
SHOUMAB1_USERNAME = model_config.get('mysql', "SHOUMAB1_USERNAME")
SHOUMAB1_PASSWORD = model_config.get('mysql', "SHOUMAB1_PASSWORD")

SHOUMAB2_HOSTNAME = model_config.get('mysql', "SHOUMAB2_HOSTNAME")
SHOUMAB2_PORT = model_config.get('mysql', "SHOUMAB2_PORT")
SHOUMAB2_DATABASE = model_config.get('mysql', "SHOUMAB2_DATABASE")
SHOUMAB2_USERNAME = model_config.get('mysql', "SHOUMAB2_USERNAME")
SHOUMAB2_PASSWORD = model_config.get('mysql', "SHOUMAB2_PASSWORD")

shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHOUMAA1_USERNAME,
    SHOUMAA1_PASSWORD,
    SHOUMAA1_HOSTNAME,
    SHOUMAA1_PORT,
    SHOUMAA1_DATABASE
)
shoumaa1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_shoumaa1_session = scoped_session(sessionmaker(shoumaa1_engine,autoflush=True))
shoumaa1_Base = declarative_base(shoumaa1_engine)
shoumaa1_session = _shoumaa1_session()





shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHOUMAA2_USERNAME,
    SHOUMAA2_PASSWORD,
    SHOUMAA2_HOSTNAME,
    SHOUMAA2_PORT,
    SHOUMAA2_DATABASE
)
shoumaa2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_shoumaa2_session = scoped_session(sessionmaker(shoumaa2_engine,autoflush=True))
shoumaa2_Base = declarative_base(shoumaa2_engine)
shoumaa2_session = _shoumaa2_session()

hisdb3_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHOUMAB1_USERNAME,
    SHOUMAB1_PASSWORD,
    SHOUMAB1_HOSTNAME,
    SHOUMAB1_PORT,
    SHOUMAB1_DATABASE
)
shoumab1_engine = create_engine(hisdb3_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_shoumab1_session = scoped_session(sessionmaker(shoumab1_engine,autoflush=True))
shoumab1_Base = declarative_base(shoumab1_engine)
shoumab1_session = _shoumab1_session()

shisdb4_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHOUMAB2_USERNAME,
    SHOUMAB2_PASSWORD,
    SHOUMAB2_HOSTNAME,
    SHOUMAB2_PORT,
    SHOUMAB2_DATABASE
)
shoumab2_engine = create_engine(shisdb4_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_shoumab2_session = scoped_session(sessionmaker(shoumab2_engine,autoflush=True))
shoumab2_Base = declarative_base(shoumab2_engine)
shoumab2_session = _shoumab2_session()














# MZGTIAN_HOSTNAME = model_config.get('mysql', "MZGTIAN_HOSTNAME")
# MZGTIAN_PORT = model_config.get('mysql', "MZGTIAN_PORT")
# MZGTIAN_DATABASE = model_config.get('mysql', "MZGTIAN_DATABASE")
# MZGTIAN_USERNAME = model_config.get('mysql', "MZGTIAN_USERNAME")
# MZGTIAN_PASSWORD = model_config.get('mysql', "MZGTIAN_PASSWORD")
#
#
#
#
# mhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
#     MZGTIAN_USERNAME,
#     MZGTIAN_PASSWORD,
#     MZGTIAN_HOSTNAME,
#     MZGTIAN_PORT,
#     MZGTIAN_DATABASE
# )
# mzgtian_engine = create_engine(mhisdb1_mysql_url,
#                        echo=False,
#                        max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
# _mzgtian_session = sessionmaker(mzgtian_engine,autoflush=True)
# mzgtian_Base = declarative_base(mzgtian_engine)
# mzgtian_session = _mzgtian_session()
#
#
# SMZGTIAN_HOSTNAME = model_config.get('mysql', "SMZGTIAN_HOSTNAME")
# SMZGTIAN_PORT = model_config.get('mysql', "SMZGTIAN_PORT")
# SMZGTIAN_DATABASE = model_config.get('mysql', "SMZGTIAN_DATABASE")
# SMZGTIAN_USERNAME = model_config.get('mysql', "SMZGTIAN_USERNAME")
# SMZGTIAN_PASSWORD = model_config.get('mysql', "SMZGTIAN_PASSWORD")
#
#
#
#
# smhisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
#     SMZGTIAN_USERNAME,
#     SMZGTIAN_PASSWORD,
#     SMZGTIAN_HOSTNAME,
#     SMZGTIAN_PORT,
#     SMZGTIAN_DATABASE
# )
# smzgtian_engine = create_engine(smhisdb1_mysql_url,
#                        echo=False,
#                        max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
# _smzgtian_session = sessionmaker(smzgtian_engine,autoflush=True)
# smzgtian_Base = declarative_base(smzgtian_engine)
# smzgtian_session = _smzgtian_session()

