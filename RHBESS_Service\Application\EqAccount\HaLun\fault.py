#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-02-15 10:47:27
#@FilePath     : \RHBESS_Service\Application\EqAccount\HaLun\fault.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:55:10
import os
import logging

import tornado.web
from sqlalchemy import func,between,or_

from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.User.fault import Fault
from Tools.Utils.num_utils import translate_text
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.user import User
import pandas as pd
import numpy as np
import math
import datetime
file_title = ['故障设备名称（编号）','故障部位','故障问题','发生（发现）日期','发生（发现）时间','修复进度','完成日期','完成时间','修复方式','修复人','原因分析','是否重复或发生/设备通病']
class FaultIntetface(BaseHandler):
    ''' 故障统计、分析功能 '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        try:
        # if 1:
            data = []
            if kt == 'GetFaultList': # 所有故障记录
                fault_name = self.get_argument('name',None)
                repair_plan = self.get_argument('repair',None)
                start_time = self.get_argument('startTime',None)
                end_time = self.get_argument('endTime',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                db = self.get_argument('db',None)
                filter = [Fault.is_use==1]
                if DEBUG:
                    logging.info('fault_name:%s,pageNum:%s,pageSize:%s,repair_plan:%s,start_time:%s,end_time:%s,db:%s'%(fault_name,pageNum,pageSize,repair_plan,start_time,end_time,db))
                if fault_name:
                    if lang=='en':
                        filter.append(Fault.en_fault_name.like('%' + fault_name + '%'))
                    else:
                        filter.append(Fault.fault_name.like('%' + fault_name + '%'))
                if lang=='en':
                    if repair_plan and repair_plan != 'all':  # 修复进度
                        filter.append(Fault.en_repair_plan == repair_plan)
                else:
                    if repair_plan and repair_plan!='全部':  # 修复进度
                        filter.append(Fault.repair_plan == repair_plan)

                if start_time:  # 修复进度
                    filter.append(Fault.happen_time>=start_time)
                if end_time:  # 修复进度
                    filter.append(Fault.happen_time<=end_time)
                if db:
                    filter.append(Fault.station==db)
              
                total = user_session.query(func.count(Fault.id)).filter(*filter).scalar()
                pages = user_session.query(Fault).filter(*filter).order_by(Fault.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for pag in pages:
                    o = eval(str(pag))
                    if lang == 'en':
                        replaced_data = Fault.replace_en_fields(o, "")
                        o.update(replaced_data)
                        data.append(o)
                    else:
                        data.append(o)
                if lang == 'en':
                    return self.returnTotalSuc(data, total, lang='en')
                else:
                    return self.returnTotalSuc(data, total)
                # return self.returnTotalSuc(data,total)
            elif kt == 'GetFaultAnalysis':#故障分析
                # descr = self.get_argument('descr', None) #项目名称
                db = self.get_argument('db', None) #电站名称
                report_type = self.get_argument('report_type', None) #1周报，2半月板，3月报，4自定义时间
                startTime = self.get_argument('startTime', None)#开始时间
                endTime = self.get_argument('endTime', None)#结束时间
                if DEBUG:
                    logging.info('report_type:%s,startTime:%s,endTime:%s,db:%s'%(report_type,startTime,endTime,db))
                start_time = startTime + ' 00:00:00'
                end_time = endTime + ' 23:59:59'

                start_time_w = timeUtils.getAgoTimeS(startTime) #前一周的开始时间
                end_time_w = timeUtils.getAgoTimeS(endTime) #前一周的结束时间

                start_time_,end_time_ = timeUtils.getPreviousMonthDay(startTime) #前一月的第一天和最后一天
                start_time_m = start_time_ + ' 00:00:00'
                end_time_m = end_time_ + ' 23:59:59'

                start_time_f_ = start_time_m[0:8]+'15' #当入参是上半月，前半月开始时间
                end_time_f_ = end_time_m[0:8]+'16'#当入参是上半月，前半月结束时间
                start_time_f = start_time_f_ + ' 00:00:00'
                end_time_f = end_time_f_ + ' 23:59:59'


                data,data1,data2,data3,data4,data5,data6= {},{},{},[],[],[],[] # 数据合集，故障数据概览，环比，饼图，故障部位分布,故障部位平均修复时间，故障部位最大修复时间
                p1 = user_session.query(Fault.id, Fault.fault_part, Fault.happen_time, Fault.finish_time,
                                        Fault.repair_plan, Fault.en_fault_part).filter(Fault.is_use == 1, Fault.station == db) .filter(or_(Fault.happen_time.between(start_time, end_time),Fault.finish_time.between(start_time, end_time))).all()
                total_0 = 0#总数
                total_1 = 0  # 新发现总数
                unrepaired = 0 #未修复
                repaired = 0 #已修复
                repa = 0 # 开始时间不是当期的已修复
                repa_1 = 0  # 开始时间不是当期的未修复
                repaired_days = 0 #总修复时间（天）
                max_repair_days = 0 #最大修复天数

                d_1 = 0 #一天以内
                d_1_7 = 0 #1到7天
                w_1_2 = 0 #1到2周
                w_2_4 = 0 #2到4周
                w_4 = 0 #4周以上
                dict_ = {} #故障部位分布（先放到字典里）
                if p1:
                    for i in p1:
                        total_0 += 1
                        t = datetime.datetime.strptime(str(start_time), "%Y-%m-%d %H:%M:%S")
                        t_ = datetime.datetime.strptime(str(end_time), "%Y-%m-%d %H:%M:%S")
                        if t <= i[2] <= t_:
                            total_1 += 1
                        if i[4] == '已修复':
                            if str(i[2]) < str(i[3]):
                                repaired = repaired + 1   #已修复
                                #修复时间（单位天）
                                repaired_time = (timeUtils.timeSeconds(str(i[2]), str(i[3])))/3600.00/24.00
                                repaired_days = repaired_days + repaired_time

                                if repaired_time >= max_repair_days:
                                    max_repair_days = repaired_time
                                if repaired_time <= 1: #一天以内的条数
                                    d_1 += 1
                                elif 1 < repaired_time <=7:#7天以内的条数
                                    d_1_7 += 1
                                elif 7 < repaired_time <=14:
                                    w_1_2 += 1
                                elif 14 < repaired_time <=28:
                                    w_2_4 += 1
                                elif repaired_time > 28:
                                    w_4 += 1
                                if lang=='en':
                                    capitalized_text = str(i[5]).title()
                                    if capitalized_text not in dict_.keys():
                                        dict_.setdefault(capitalized_text,{'value1':1}) # 总修复条数
                                    if 'value2' not in dict_[capitalized_text].keys():
                                        repaired_days_2 = repaired_time  # 总修复时间（天）
                                        dict_[capitalized_text].setdefault('value2',repaired_days_2)
                                    if 'value3' not in dict_[capitalized_text].keys():
                                        dict_[capitalized_text].setdefault('value3',repaired_time)
                                    else:
                                        dict_[capitalized_text]['value1'] += 1
                                        dict_[capitalized_text]['value2'] += repaired_time #总修复时间（天）
                                        if repaired_time >= dict_[capitalized_text]['value3']:
                                            dict_[capitalized_text]['value3'] = repaired_time #最大修复天数
                                else:
                                    if str(i[1]) not in dict_.keys():
                                        dict_.setdefault(str(i[1]), {'value1': 1})  # 总修复条数
                                    if 'value2' not in dict_[str(i[1])].keys():
                                        repaired_days_2 = repaired_time  # 总修复时间（天）
                                        dict_[str(i[1])].setdefault('value2', repaired_days_2)
                                    if 'value3' not in dict_[str(i[1])].keys():
                                        dict_[str(i[1])].setdefault('value3', repaired_time)
                                    else:
                                        dict_[str(i[1])]['value1'] += 1
                                        dict_[str(i[1])]['value2'] += repaired_time  # 总修复时间（天）
                                        if repaired_time >= dict_[str(i[1])]['value3']:
                                            dict_[str(i[1])]['value3'] = repaired_time  # 最大修复天数
                                if i[2] < t:
                                    repa += 1

                        elif i[4] != '已修复':
                            unrepaired = unrepaired + 1
                            if i[2] < t:
                                repa_1 += 1
                leave_behind = repa_1 + repa # 当前遗留数量

                total = total_1 + leave_behind
                if total == 0:
                    repair_rate = '--'
                    Avg_repair_days = 0
                else:
                    repair_rate = '%.2f'% (float(repaired) / float(total) * 100 ) # 修复率
                    Avg_repair_days = float(repaired_days) / float(total) #平均修复天数

                for item in dict_.keys():#取出放分别放到列表里
                    for i in dict_[item].keys():
                        if i == 'value1':
                            m = float(dict_[item][i])/float(repaired)
                            data4.append({'name':item,'value':str('%.2f'% (m*100))+'%'})
                        elif i == 'value2':
                            m = float(dict_[item][i])/float(dict_[item]['value1'])
                            data5.append({'name':item,'value':float('%.2f'% m)})
                        elif i == 'value3':
                            data6.append({'name':item,'value':float('%.2f'% dict_[item][i])})

                data5.sort(key=lambda x:x['value'],reverse=True)
                data6.sort(key=lambda x: x['value'], reverse=True)

                data1['total'] = total  # 总数
                data1['unrepaired'] = unrepaired  # 未修复
                data1['repaired'] = repaired  # 已修复
                data1['repair_rate'] = repair_rate   # 修复率
                data1['max_repair_days'] = int(math.ceil(max_repair_days)) # 最大修复天数
                data1['Avg_repair_days'] = int(math.ceil(Avg_repair_days))  # 平均修复天数
                data['dict1'] = data1
                data3.append({'d_1': d_1})
                data3.append({'d_1_7': d_1_7})
                data3.append({'w_1_2': w_1_2})
                data3.append({'w_2_4': w_2_4})
                data3.append({'w_4': w_4})
                data['data3'] = data3
                data['data4'] = data4
                data['data5'] = data5
                data['data6'] = data6

                start_time_b = start_time
                end_time_b = end_time
                #判断类型
                if report_type == '1':
                    start_time_b = start_time_w + ' 00:00:00'
                    end_time_b = end_time_w + ' 23:59:59'
                elif report_type == '2': #2半月板
                    if startTime[-2:] == '01':
                        start_time_b = start_time_f
                        end_time_b = end_time_f
                    elif startTime[-2:] == '15':
                        start_time_b = startTime[0:8]+'16'
                        end_time_b = timeUtils.last_day_of_month(startTime[0:5],startTime[6:8])
                elif report_type == '3':
                    start_time_b = start_time_m
                    end_time_b = end_time_m
                if report_type != '4':
                    p2 = user_session.query(Fault.id,Fault.happen_time, Fault.finish_time,Fault.repair_plan).filter(Fault.is_use == 1, Fault.station == db) \
                        .filter(or_(Fault.happen_time.between(start_time_b, end_time_b),Fault.finish_time.between(start_time_b, end_time_b))).all()

                    total_2 = 0  # 前新发现总数
                    total_3 = 0  # 总数
                    repaired_2 = 0  # 已修复
                    unrepaired_2 = 0  # 未修复
                    repa_ = 0  # 开始时间不是当期的已修复
                    repa_2 = 0  # 开始时间不是当期的未修复
                    if p2:
                        for i in p2:
                            total_3 += 1
                            t = datetime.datetime.strptime(str(start_time_b), "%Y-%m-%d %H:%M:%S")
                            t_ = datetime.datetime.strptime(str(end_time_b), "%Y-%m-%d %H:%M:%S")
                            if t <= i[1] <= t_:
                                total_2 += 1
                            if str(i[1]) < str(i[2]):
                                if i[3] == '已修复':
                                    repaired_2 += 1  # 已修复
                                    if i[1] < t:
                                        repa_ += 1
                            elif i[3] != '已修复':
                                unrepaired_2 = unrepaired_2 + 1
                                if i[1] < t:
                                    repa_2 += 1
                    leave_behind_2 = repa_2 + repa_  # 前遗留数量
                    total_ = total_2 + leave_behind_2
                   
                    if total_ == 0:
                        repair_rate_ = '--'
                    else:
                        repair_rate_ = '%.2f'% (float(repaired_2) / float(total_) * 100 ) # 前修复率
                        # repair_rate_ = (float(repaired_2) / float(total_)) * 100  # 前修复率


                    data2['total_2'] = total_2  # 前新发现总数
                    data2['total_1'] = total_1  # 当新发现总数
                    data2['leave_behind_2'] = leave_behind_2  # 前遗留数量
                    data2['leave_behind'] = leave_behind  # 当遗留数量
                    data2['repair_rate_'] = repair_rate_  # 前修复率
                    data2['repair_rate'] = repair_rate  # 当修复率
                    data['data2'] = data2
                if lang == 'en':
                    return self.returnTypeSuc_en(data=data, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=data, info=None, lang=None)
                # return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        try:
            if kt == 'AddFault': # 添加故障记录
                station = self.get_argument('db',None)
                fault_name = self.get_argument('name',None)
                fault_part = self.get_argument('part',None)
                fault_problem = self.get_argument('problem',None)
                happen_time = self.get_argument('happen_time',None)
                finish_time = self.get_argument('finish_time',None)
                repair_way = self.get_argument('repairPlan',None)
                repair_use = self.get_argument('repairPeople',None)
                repair_plan = self.get_argument('repair',None)
                repet_flag = self.get_argument('repeat',None).capitalize()
                cause = self.get_argument('reason',None)
                remarks = self.get_argument('remarks',None)
                if not fault_name or not fault_part:
                    if lang=='en':
                        return self.customError('Parameter incompleteness')
                    else:
                        return self.customError('参数不完整')

                fault_name = ';'.join(fault_name.split())
                fault_part = ';'.join(fault_part.split())
                fault_problem = ';'.join(fault_problem.split()) if fault_problem else ''
                repair_plan = ';'.join(repair_plan.split()) if repair_plan else ''
                cause = ';'.join(cause.split()) if cause else ''
                repair_use = ';'.join(repair_use.split()) if repair_use else ''
                remarks = ';'.join(remarks.split()) if remarks else ''

                if DEBUG:
                    logging.info('station:%s,fault_name:%s,fault_part:%s,fault_problem:%s,happen_time:%s,finish_time:%s,repair_way:%s,repair_use:%s,repair_plan:%s,repet_flag:%s,cause:%s'%(station,
                                fault_name,fault_part,fault_problem,happen_time,finish_time,repair_way,repair_use,repair_plan,repet_flag,cause))
                if lang == 'en':
                    p= self._fault_exist(fault_name,fault_part,fault_problem,happen_time,finish_time,repair_way,repet_flag,cause,station,lang)
                else:
                    p = self._fault_exist(fault_name, fault_part, fault_problem, happen_time, finish_time, repair_way,repet_flag, cause, station)
                if p:
                    if lang=='en':
                        logging.info("Duplicate data item")
                    else:
                        logging.info("重复数据项")
                    return self.returnTypeSuc('')
                session = self.getOrNewSession()
                user_id = session.user['id']
                if lang=='en':
                    zh_fault_name = translate_text(fault_name, 1)
                    zh_fault_part = translate_text(fault_part, 1)
                    zh_fault_problem = translate_text(fault_problem, 1)
                    zh_repair_plan = translate_text(repair_plan, 1)
                    zh_cause = translate_text(cause, 1)
                    zh_repair_use = translate_text(repair_use, 1)
                    zh_remarks = translate_text(remarks, 1)
                    zh_repair_way = translate_text(repair_way, 1)

                    p = Fault(station=station,fault_name=zh_fault_name,fault_part=zh_fault_part,fault_problem=zh_fault_problem,en_fault_name=fault_name,en_fault_part=fault_part,en_fault_problem=fault_problem,happen_time=happen_time,
                              finish_time=finish_time,repair_way=zh_repair_way,repair_use=zh_repair_use,repair_plan=zh_repair_plan,en_repair_way=repair_way,en_repair_use=repair_use,en_repair_plan=repair_plan,
                              repet_flag=repet_flag,cause=zh_cause,user_id=user_id,op_ts=timeUtils.getNewTimeStr(),remarks=zh_remarks,en_cause=cause,en_remarks=remarks)
                else:
                    en_fault_name = translate_text(fault_name, 2)
                    en_fault_part = translate_text(fault_part, 2)
                    en_fault_problem = translate_text(fault_problem, 2)
                    en_repair_plan = translate_text(repair_plan, 2)
                    en_cause = translate_text(cause, 2)
                    en_repair_use = translate_text(repair_use, 2)
                    en_remarks = translate_text(remarks, 2)
                    en_repair_way = translate_text(repair_way, 2)

                    p = Fault(station=station, fault_name=fault_name, fault_part=fault_part,
                              fault_problem=fault_problem, en_fault_name=en_fault_name, en_fault_part=en_fault_part,
                              en_fault_problem=en_fault_problem, happen_time=happen_time,
                              finish_time=finish_time, repair_way=repair_way, repair_use=repair_use,
                              repair_plan=repair_plan, en_repair_way=en_repair_way, en_repair_use=en_repair_use,
                              en_repair_plan=en_repair_plan,
                              repet_flag=repet_flag, cause=cause, user_id=user_id, op_ts=timeUtils.getNewTimeStr(),
                              remarks=remarks, en_cause=en_cause, en_remarks=en_remarks)
                user_session.add(p)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UpdateFault': # 修改故障记录
                id = self.get_argument('id',None)
                station = self.get_argument('db',None)
                fault_name = self.get_argument('name',None)
                fault_part = self.get_argument('part',None)
                fault_problem = self.get_argument('problem',None)
                happen_time = self.get_argument('happen_time',None)
                finish_time = self.get_argument('finish_time',None)
                repair_way = self.get_argument('repairPlan',None)
                repair_use = self.get_argument('repairPeople',None)
                repair_plan = self.get_argument('repair',None)
                repet_flag = self.get_argument('repeat',None).capitalize()
                cause = self.get_argument('reason',None)
                remarks = self.get_argument('remarks',None)

                fault_name = ';'.join(fault_name.split())
                fault_part = ';'.join(fault_part.split())
                fault_problem = ';'.join(fault_problem.split()) if fault_problem else ''
                repair_plan = ';'.join(repair_plan.split()) if repair_plan else ''
                cause = ';'.join(cause.split()) if cause else ''
                repair_use = ';'.join(repair_use.split()) if repair_use else ''
                remarks = ';'.join(remarks.split()) if remarks else ''
                if DEBUG:
                    logging.info('id:%s,station:%s,fault_name:%s,fault_part:%s,fault_problem:%s,happen_time:%s,finish_time:%s,repair_way:%s,repair_use:%s,repair_plan:%s,repet_flag:%s,cause:%s'%(id,station,
                                fault_name,fault_part,fault_problem,happen_time,finish_time,repair_way,repair_use,repair_plan,repet_flag,cause))

                page = user_session.query(Fault).filter(Fault.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")

                page.station = station
                page.happen_time = happen_time
                page.finish_time = finish_time
                page.repet_flag = repet_flag
                if lang=='en':
                    page.fault_name = translate_text(fault_name, 1)
                    page.en_fault_name = fault_name
                    page.fault_part = translate_text(fault_part, 1)
                    page.en_fault_part = fault_part
                    page.fault_problem = translate_text(fault_problem, 1)
                    page.en_fault_problem = fault_problem
                    page.repair_way = translate_text(repair_way, 1)
                    page.en_repair_way = repair_way
                    page.repair_use = translate_text(repair_use, 1)
                    page.en_repair_use = repair_use
                    page.repair_plan = translate_text(repair_plan, 1)
                    page.en_repair_plan = repair_plan
                    page.cause = translate_text(cause, 1)
                    page.en_cause = cause
                    page.remarks = translate_text(remarks, 1)
                    page.en_remarks = remarks
                else:
                    page.fault_name = fault_name
                    page.en_fault_name = translate_text(fault_name, 2)
                    page.fault_part = fault_part
                    page.en_fault_part = translate_text(fault_part, 2)
                    page.fault_problem = fault_problem
                    page.en_fault_problem = translate_text(fault_problem, 2)
                    page.repair_way = repair_way
                    page.en_repair_way = translate_text(repair_way, 2)
                    page.repair_use = repair_use
                    page.en_repair_use = translate_text(repair_use, 2)
                    page.repair_plan = repair_plan
                    page.en_repair_plan = translate_text(repair_plan, 2)
                    page.cause = cause
                    page.en_cause = translate_text(cause, 2)
                    page.remarks = remarks
                    page.en_remarks = translate_text(remarks, 2)

                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'UploadFaultFile': # 上传故障记录表
                station = self.get_argument('db',None)
                files = self.request.files
                file_path = '/home/<USER>/excelfiles'
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('file')
                data = imgs[0].get('body')
                filename = imgs[0].get('filename')
                if not filename.endswith(('.xls','.xlsx')):
                    if lang=='en':
                        return self.customError('Upload form file format error! Please re-select the file, or use the "Template Download" button to download the standard format spreadsheet and enter the troubleshooting information in this template!')
                    else:
                        return self.customError('上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障处置信息！')
                path = '%s/%s' % (file_path,filename)
                file = open(path, 'wb')
                file.write(data)
                file.close()
                # print 'path:',path
                # path = '/home/<USER>/excelfiles/储能电站设备故障信息统计表(1)(1).xlsx'
                df = pd.read_excel(path,index_col=0)
                # print 'df-------',df
                values_arr = df.values  # 二维矩阵
                values_arr[pd.isna(values_arr)] = None
                # print '^^^^^^^^^^^^^^^^^',values_arr
                # 校验表头
                # print 'values_arr:',values_arr,len(values_arr),len(values_arr[0]),len(file_title)
                for i in range(len(file_title)):
                    if file_title[i] != values_arr[1][i]:
                        if lang=='en':
                            logging.error("Standard title:%s  Document title:%s" % (file_title[i], values_arr[1][i]))
                            return self.customError('Upload form file format error! Please re-select the file, or use the "Template Download" button to download the standard format spreadsheet and enter the troubleshooting information in this template!')
                        else:
                            logging.error("标准title:%s  文件title:%s"%(file_title[i],values_arr[1][i]))
                            return self.customError('上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障处置信息！')
                
                session = self.getOrNewSession()
                user_id = session.user['id']
                for v in range(2,len(values_arr)):  # 获得某一行的数据
                    value = values_arr[v]
                    happen_time = str(value[3])[:10] + ' ' + str(value[4])
                    finish_time = str(value[6])[:10] + ' ' + str(value[7])

                    if lang =='en':
                        fault_name = value[0]
                        zh_fault_name = translate_text(value[0], 1)
                        fault_part = value[1]
                        zh_fault_part = translate_text(value[1], 1)
                        fault_problem = value[2]
                        zh_fault_problem = translate_text(value[2], 1)
                        repair_plan = value[5]
                        zh_repair_plan = translate_text(value[5], 1)
                        repair_way = value[8]
                        zh_repair_way = translate_text(value[8], 1)
                        repair_use = str(value[9])
                        zh_repair_use = translate_text(str(value[9]), 1)
                        cause = value[10]
                        zh_cause = translate_text(value[10], 1)
                        remarks = value[12]
                        zh_remarks = translate_text(value[12], 1)
                        repet_flag = 'True' if value[11] == 'Y' or value[11] == 'YES' else 'False'
                        p = self._fault_exist(fault_name, fault_part, fault_problem, happen_time, finish_time,repair_way, repet_flag, cause, station,lang)
                        if p:  # 已保存
                            logging.info('line:%s is in DB' % v)
                            continue
                        else:
                            p = Fault(station=station, fault_name=zh_fault_name, fault_part=zh_fault_part,
                                      fault_problem=zh_fault_problem, happen_time=happen_time, finish_time=finish_time,
                                      repair_way=zh_repair_way, en_fault_name=fault_name, en_fault_part=fault_part,en_repair_way=repair_way,
                                      en_fault_problem=fault_problem,repair_use=zh_repair_use, repair_plan=zh_repair_plan,en_repair_use=repair_use, en_repair_plan=repair_plan, repet_flag=repet_flag,
                                      cause=zh_cause, user_id=user_id, op_ts=timeUtils.getNewTimeStr(), remarks=zh_remarks,en_cause=cause,en_remarks=remarks)
                            user_session.add(p)
                    else:
                        fault_name = value[0]
                        en_fault_name = translate_text(value[0], 2)
                        fault_part = value[1]
                        en_fault_part = translate_text(value[1], 2)
                        fault_problem = value[2]
                        en_fault_problem = translate_text(value[2], 2)
                        repair_plan = value[5]
                        en_repair_plan = translate_text(value[5], 2)
                        repair_way = value[8]
                        en_repair_way = translate_text(value[8], 2)
                        repair_use = str(value[9])
                        en_repair_use = translate_text(str(value[9]), 2)
                        cause = value[10]
                        en_cause = translate_text(value[10], 2)
                        remarks = value[12]
                        en_remarks = translate_text(value[12], 2)
                        repet_flag = 'True' if value[11] == '是' else 'False'
                        p = self._fault_exist(fault_name, fault_part, fault_problem, happen_time, finish_time,repair_way, repet_flag, cause, station)
                        if p:  # 已保存
                            logging.info('line:%s is in DB' % v)
                            continue
                        else:
                            p = Fault(station=station, fault_name=fault_name, fault_part=fault_part,
                                      fault_problem=fault_problem, happen_time=happen_time, finish_time=finish_time,
                                      repair_way=repair_way, en_fault_name=en_fault_name, en_fault_part=en_fault_part,
                                      en_repair_way=en_repair_way,en_fault_problem=en_fault_problem, repair_use=repair_use, repair_plan=repair_plan,
                                      en_repair_use=en_repair_use, en_repair_plan=en_repair_plan, repet_flag=repet_flag,
                                      cause=cause, user_id=user_id, op_ts=timeUtils.getNewTimeStr(), remarks=remarks,
                                      en_cause=en_cause, en_remarks=en_remarks)
                            user_session.add(p)
                    # print value[6],value[7],finish_time
                    # if v>2 and not pd.isnull(fault_name) and not pd.isnull(fault_part) and not pd.isnull(repair_use):  # 去掉表格的前两行 同时设备名和故障部位同时不为空
                    

                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            elif kt == 'LogicDeleteFault': # 逻辑删除故障记录
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))

                page = user_session.query(Fault).filter(Fault.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
               
                page.is_use = 0
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
    def _fault_exist(self,fault_name,fault_part,fault_problem,happen_time,finish_time,repair_way,repet_flag,cause,station,lang=None):
        '''判断故障是否存在'''
        if lang=='en':
            page = user_session.query(Fault).filter(Fault.en_fault_name==fault_name,Fault.en_fault_part==fault_part,Fault.en_fault_problem==fault_problem,Fault.happen_time==happen_time,
                                                Fault.finish_time==finish_time,Fault.en_repair_way==repair_way,Fault.repet_flag==repet_flag,Fault.en_cause==cause,Fault.is_use==1,Fault.station==station).first()
        else:
            page = user_session.query(Fault).filter(Fault.fault_name == fault_name, Fault.fault_part == fault_part,
                                                    Fault.fault_problem == fault_problem, Fault.happen_time == happen_time,
                                                    Fault.finish_time == finish_time, Fault.repair_way == repair_way,
                                                    Fault.repet_flag == repet_flag, Fault.cause == cause, Fault.is_use == 1,
                                                    Fault.station == station).first()
        if page:
            return True
        else:
            return False