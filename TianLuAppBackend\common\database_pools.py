#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2024-01-05 08:29:35
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\TianLuAppBackend\common\database_pools.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-05 09:30:00
import traceback

import pymysql
# from dbutils.persistent_db import PersistentDB
from dbutils.pooled_db import PooledDB

from TianLuAppBackend import settings
from common.get_custom_logger import get_logger

logger = get_logger('dwd_mysql')


class DWDDatabaseTool:

    def __init__(self):
        self.dwd_pool = PooledDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_single(self, sql):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql)
            result = cursor.fetchall()
            return result
        except Exception as e:
            print(traceback.print_exc())
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()


class DWSDatabaseTool:

    def __init__(self):
        self.dwd_pool = PooledDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dws_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dws_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dws_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dws_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dws_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()



class ADSDatabaseTool:

    def __init__(self):
        self.dwd_pool = PooledDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_ads_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_ads_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_ads_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_ads_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_ads_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()


dwd_db_tool = DWDDatabaseTool()
dws_db_tool = DWSDatabaseTool()
ads_db_tool = ADSDatabaseTool()

dwd_tables = {
    "measure": {
        "ems": 'dwd_measure_ems_data_storage',
        "ems_tscale": 'dwd_measure_ems_data_storage_tscale',
        "load": 'dwd_measure_load_data_storage',
        "bms": 'dwd_measure_bms_data_storage',
        "bms_1": 'dwd_measure_bms_data_storage_1',
        "bms_2": "dwd_measure_bms_data_storage_2",
        "bms_3": "dwd_measure_bms_data_storage_3",
        "pcs": 'dwd_measure_pcs_data_storage',
        "version": 'dwd_measure_version_data'
    },
    "status": {
        "ems": 'dwd_status_ems_data_storage',
        "bms": 'dwd_status_bms_data_storage',
        "pcs": 'dwd_status_pcs_data_storage'
    },
    "cumulant": {
        "ems": 'dwd_cumulant_ems_data_storage',
        "load": 'dwd_cumulant_load_data_storage',
        "bms": 'dwd_cumulant_bms_data_storage',
        "pcs": 'dwd_cumulant_pcs_data_storage'
    },
    "discrete": {
        "ems": 'dwd_discrete_ems_data_storage',
        "bms": 'dwd_discrete_bms_data_storage',
        "pcs": 'dwd_discrete_pcs_data_storage'
    }
}

# 电压
MU_kEYS = ['mu1', 'mu2', 'mu3', 'mu4', 'mu5', 'mu6', 'mu7',
     'mu8', 'mu9', 'mu10', 'mu11', 'mu12', 'mu13', 'mu14', 'mu15', 'mu16', 'mu17', 'mu18', 'mu19', 'mu20', 'mu21',
     'mu22', 'mu23', 'mu24', 'mu25', 'mu26', 'mu27', 'mu28', 'mu29', 'mu30', 'mu31', 'mu32', 'mu33', 'mu34', 'mu35',
     'mu36', 'mu37', 'mu38', 'mu39', 'mu40', 'mu41', 'mu42', 'mu43', 'mu44', 'mu45', 'mu46', 'mu47', 'mu48', 'mu49',
     'mu50', 'mu51', 'mu52', 'mu53', 'mu54', 'mu55', 'mu56', 'mu57', 'mu58', 'mu59', 'mu60', 'mu61', 'mu62', 'mu63',
     'mu64', 'mu65', 'mu66', 'mu67', 'mu68', 'mu69', 'mu70', 'mu71', 'mu72', 'mu73', 'mu74', 'mu75', 'mu76', 'mu77',
     'mu78', 'mu79', 'mu80', 'mu81', 'mu82', 'mu83', 'mu84', 'mu85', 'mu86', 'mu87', 'mu88', 'mu89', 'mu90', 'mu91',
     'mu92', 'mu93', 'mu94', 'mu95', 'mu96', 'mu97', 'mu98', 'mu99', 'mu100', 'mu101', 'mu102', 'mu103', 'mu104',
     'mu105', 'mu106', 'mu107', 'mu108', 'mu109', 'mu110', 'mu111', 'mu112', 'mu113', 'mu114', 'mu115', 'mu116',
     'mu117', 'mu118', 'mu119', 'mu120', 'mu121', 'mu122', 'mu123', 'mu124', 'mu125', 'mu126', 'mu127', 'mu128',
     'mu129', 'mu130', 'mu131', 'mu132', 'mu133', 'mu134', 'mu135', 'mu136', 'mu137', 'mu138', 'mu139', 'mu140',
     'mu141', 'mu142', 'mu143', 'mu144', 'mu145', 'mu146', 'mu147', 'mu148', 'mu149', 'mu150', 'mu151', 'mu152',
     'mu153', 'mu154', 'mu155', 'mu156', 'mu157', 'mu158', 'mu159', 'mu160', 'mu161', 'mu162', 'mu163', 'mu164',
     'mu165', 'mu166', 'mu167', 'mu168', 'mu169', 'mu170', 'mu171', 'mu172', 'mu173', 'mu174', 'mu175', 'mu176',
     'mu177', 'mu178', 'mu179', 'mu180', 'mu181', 'mu182', 'mu183', 'mu184', 'mu185', 'mu186', 'mu187', 'mu188',
     'mu189', 'mu190', 'mu191', 'mu192', 'mu193', 'mu194', 'mu195', 'mu196', 'mu197', 'mu198', 'mu199', 'mu200',
     'mu201', 'mu202', 'mu203', 'mu204', 'mu205', 'mu206', 'mu207', 'mu208', 'mu209', 'mu210', 'mu211', 'mu212',
     'mu213', 'mu214', 'mu215', 'mu216', 'mu217', 'mu218', 'mu219', 'mu220', 'mu221', 'mu222', 'mu223', 'mu224',
     'mu225', 'mu226', 'mu227', 'mu228', 'mu229', 'mu230', 'mu231', 'mu232', 'mu233', 'mu234', 'mu235', 'mu236',
     'mu237', 'mu238', 'mu239', 'mu240', 'mu241', 'mu242', 'mu243', 'mu244', 'mu245', 'mu246', 'mu247', 'mu248',
     'mu249', 'mu250', 'mu251', 'mu252', 'mu253', 'mu254', 'mu255', 'mu256', 'mu257', 'mu258', 'mu259', 'mu260']

# 温度
MT_KEYS = ['mt1', 'mt2', 'mt3', 'mt4', 'mt5', 'mt6', 'mt7', 'mt8', 'mt9', 'mt10', 'mt11', 'mt12', 'mt13', 'mt14', 'mt15',
     'mt16', 'mt17', 'mt18', 'mt19', 'mt20', 'mt21', 'mt22', 'mt23', 'mt24', 'mt25', 'mt26', 'mt27', 'mt28', 'mt29',
     'mt30', 'mt31', 'mt32', 'mt33', 'mt34', 'mt35', 'mt36', 'mt37', 'mt38', 'mt39', 'mt40', 'mt41', 'mt42', 'mt43',
     'mt44', 'mt45', 'mt46', 'mt47', 'mt48', 'mt49', 'mt50', 'mt51', 'mt52', 'mt53', 'mt54', 'mt55', 'mt56', 'mt57',
     'mt58', 'mt59', 'mt60', 'mt61', 'mt62', 'mt63', 'mt64', 'mt65', 'mt66', 'mt67', 'mt68', 'mt69', 'mt70', 'mt71',
     'mt72', 'mt73', 'mt74', 'mt75', 'mt76', 'mt77', 'mt78', 'mt79', 'mt80', 'mt81', 'mt82', 'mt83', 'mt84', 'mt85',
     'mt86', 'mt87', 'mt88', 'mt89', 'mt90', 'mt91', 'mt92', 'mt93', 'mt94', 'mt95', 'mt96', 'mt97', 'mt98', 'mt99',
     'mt100', 'mt101', 'mt102', 'mt103', 'mt104', 'mt105', 'mt106', 'mt107', 'mt108', 'mt109', 'mt110', 'mt111',
     'mt112', 'mt113', 'mt114', 'mt115', 'mt116', 'mt117', 'mt118', 'mt119', 'mt120', 'mt121', 'mt122', 'mt123',
     'mt124', 'mt125', 'mt126', 'mt127', 'mt128', 'mt129', 'mt130', 'mt131', 'mt132', 'mt133', 'mt134', 'mt135',
     'mt136', 'mt137', 'mt138', 'mt139', 'mt140', 'mt141']

# 版本号
VERSION_KEYS = ['armv', 'dspv', 'hmiv', 'svn', 'rvn', 'hvn', 'wcsv', 'lpd', 'fmqtt', 'ctcp', 'crtu', 'simulator', 'script', 'akv', 'ftcp']

LOAD_KEYS = ['eua', 'eub', 'euc', 'euab', 'eubc', 'euca', 'eia', 'eib', 'eic', 'epa', 'epb', 'epc', 'eqa', 'eqb', 'eqc', 'eq',
     'cosa', 'cosb', 'cosc', 'pcos', 'ehz', 'etemp', 'pcc', 'geua', 'geub', 'geuc', 'geuab', 'geubc', 'geuca', 'geia',
     'geib', 'geic', 'gepa', 'gepb', 'gepc', 'geqa', 'geqb', 'geqc', 'geq', 'gcosa', 'gcosb', 'gcosc', 'gpcos', 'gehz',
     'getemp', 'seua', 'seub', 'seuc', 'seuab', 'seubc', 'seuca', 'seia', 'seib', 'seic', 'sepa', 'sepb', 'sepc',
     'seqa', 'seqb', 'seqc', 'seq', 'scosa', 'scosb', 'scosc', 'spcos', 'sehz', 'setemp', 'gpcc', 'spcc']

CUMU_LOAD_KEYS = ['epae', 'enae', 'epre', 'enre', 'gepae', 'genae', 'gepre', 'genre', 'sepae', 'senae', 'sepre', 'senre']

if __name__ == '__main__':
    pass
    # print(dwd_db_tool.select_many('SELECT * FROM dwd_measure_pcs_data_storage where station_name = %s limit 10', 'DNJK201'))
    # print(ads_db_tool.select_many('SELECT day, sum(pointed_chag) as charge, sum(valley_chag) as discharge FROM ads_report_peak_valley_chag_disg_1d group_by day where station_type=%s and day like %s',
    #                                1, '%2024-01%'))
