package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.telecontrol.*;
import com.robestec.analysis.entity.TPowerDeliverRecords;
import com.robestec.analysis.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 远程控制策略服务接口
 * 对应Python中telecontrol_strategy.py的所有方法
 */
public interface TelecontrolStrategyService extends ISuperService<TPowerDeliverRecords> {

    /**
     * 策略模板下载
     * 对应Python中的StrategyTemplate方法
     *
     * @param response HTTP响应对象
     */
    void downloadStrategyTemplate(HttpServletResponse response);

    /**
     * 策略模板解析导入
     * 对应Python中的StrategyImport方法
     *
     * @param importDTO 导入DTO
     * @return 解析结果
     */
    StrategyImportVO importStrategy(StrategyImportDTO importDTO);

    /**
     * 获取电站容量信息
     * 对应Python中的PowerPlanStations方法
     *
     * @return 电站容量信息列表
     */
    List<StationCapacityVO> getPowerPlanStations();

    /**
     * 刷新电站容量信息
     * 对应Python中的PowerPlanStationsRefresh方法
     *
     * @param refreshDTO 刷新DTO
     * @return 刷新后的电站容量信息
     */
    List<StationCapacityVO> refreshPowerPlanStations(StationRefreshDTO refreshDTO);

    /**
     * 功率计划下发列表
     * 对应Python中的PowerPlanList方法
     *
     * @param queryDTO 查询请求参数
     * @return 分页查询结果
     */
    PageResult<PowerPlanVO> getPowerPlanList(PowerPlanQueryDTO queryDTO);

    /**
     * 新增计划功率
     * 对应Python中的PowerPlanAdd方法
     *
     * @param createDTO 新增请求参数
     * @return 新增记录ID
     */
    Long createPowerPlan(PowerPlanCreateDTO createDTO);

    /**
     * 功率计划详情
     * 对应Python中的PowerPlanDetail方法
     *
     * @param id 计划ID
     * @return 计划详情
     */
    PowerPlanVO getPowerPlanDetail(Long id);

    /**
     * 修改计划功率
     * 对应Python中的PowerPlanUpdate方法
     *
     * @param updateDTO 修改请求参数
     */
    void updatePowerPlan(PowerPlanUpdateDTO updateDTO);

    /**
     * 停止计划功率
     * 对应Python中的powerPlanStop方法
     *
     * @param operationDTO 操作DTO
     */
    void stopPowerPlan(PowerPlanOperationDTO operationDTO);

    /**
     * 删除功率计划
     * 对应Python中的powerPlanDelete方法
     *
     * @param operationDTO 操作DTO
     */
    void deletePowerPlan(PowerPlanOperationDTO operationDTO);

    /**
     * 查询下发记录列表
     * 对应Python中的GetPlanHis方法
     *
     * @param queryDTO 查询请求参数
     * @return 分页查询结果
     */
    PageResult<PlanHistoryVO> getPlanHistory(PlanHistoryQueryDTO queryDTO);

    /**
     * 导出下发记录
     * 对应Python中的planHisExport方法
     *
     * @param exportDTO 导出请求参数
     * @param response HTTP响应对象
     */
    void exportPlanHistory(PlanHistoryExportDTO exportDTO, HttpServletResponse response);

    /**
     * 查询下发类型
     * 对应Python中的GetIssuanceType方法
     *
     * @return 下发类型映射
     */
    Map<String, String> getIssuanceType();

    /**
     * 另存项目包
     * 对应Python中的ProjectPackAdd方法
     *
     * @param createDTO 项目包添加请求
     * @return 项目包ID
     */
    Long createProjectPack(ProjectPackCreateDTO createDTO);

    /**
     * 加载项目包列表
     * 对应Python中的ProjectPackList方法
     *
     * @param queryDTO 查询DTO
     * @return 项目包列表
     */
    PageResult<ProjectPackVO> getProjectPackList(ProjectPackQueryDTO queryDTO);
}
