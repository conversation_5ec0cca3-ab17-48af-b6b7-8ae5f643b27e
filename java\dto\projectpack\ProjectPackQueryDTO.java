package com.robestec.analysis.dto.projectpack;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目包查询DTO
 */
@Data
@ApiModel("项目包查询DTO")
public class ProjectPackQueryDTO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("项目包名称")
    private String name;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
