import re

from rest_framework import serializers
from django.core.validators import RegexValidator
from apis.user import models
from rest_framework import exceptions
from django_redis import get_redis_connection
from encryption.md5_encryption import md5_enc


class BaseMd5PasswordSerializer:
    def validate_password(self, value):
        """md5密码加密"""
        return md5_enc(value)


class BaseSMSSerializer:
    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if cache_mobile_code.decode('utf-8') != value:
            raise exceptions.ValidationError("验证码错误")
        return value


# class RegisterSerializer(serializers.ModelSerializer, BaseMd5PasswordSerializer, BaseSMSSerializer):
#     # 手机号校验
#     mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
#     # 验证码校验
#     code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
#     # 密码格式校验必须包含数字和字母，长度至少8位
#     password = serializers.CharField(
#         required=True, validators=[RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")]
#     )
#     # 确认密码校验
#     confirm_password = serializers.CharField(
#         required=True, validators=[RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")]
#     )
#
#     class Meta:
#         model = models.UserDetails
#         fields = ['login_name', 'mobile', 'code', 'password', 'confirm_password']
#
#     def validate_login_name(self, value):
#         """用户名是否存在校验"""
#         exists = models.UserDetails.objects.filter(login_name=value).exists()
#         if exists:
#             raise exceptions.ValidationError("用户名已存在")
#
#         return value
#
#     def validate_mobile(self, value):
#         """手机号是否存在校验"""
#         exists = models.UserDetails.objects.filter(mobile=value).exists()
#         if exists:
#             raise exceptions.ValidationError("手机号已注册")
#
#         return value

    # def validate_confirm_password(self, value):
    #     """校验两次密码是否一致"""
    #     password = self.initial_data.get("password")
    #     if value != password:
    #         raise exceptions.ValidationError("密码不一致")
    #     return value


class SendMobileMessageSerializer(serializers.Serializer):
    mobile = serializers.CharField(
        required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")], label="手机号", help_text="xxx"
    )


class SMSCodeLoginSerializer(BaseMd5PasswordSerializer, serializers.ModelSerializer):
    """手机号验证码登录"""

    # 手机号校验
    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])
    # password = serializers.CharField(required=True, max_length=32)
    # ty = serializers.CharField(required=True, max_length=32, write_only=True)

    class Meta:
        model = models.UserDetails
        fields = ["code", "mobile"]

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value
    # def validate_ty(self, value):
    #     if value and int(value) not in [0, 1]:
    #         raise exceptions.ValidationError("参数ty错误")
    #     return value


class PasswordLoginSerializer(BaseMd5PasswordSerializer, serializers.ModelSerializer):
    """用户名/手机号 + 密码"""""

    # 手机号校验
    mobile = serializers.CharField(required=False, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    password = serializers.CharField(required=False, max_length=32)
    login_name = serializers.CharField(required=False, max_length=32)

    # 验证码校验
    class Meta:
        model = models.UserDetails
        fields = ["mobile", "password", "login_name"]

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value,is_used=1).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value

    def validate_login_name(self, value):
        exists = models.UserDetails.objects.filter(login_name=value,is_used=1).exists()
        if not exists:
            raise exceptions.ValidationError("用户名不存在")
        return value

    def validate(self, attrs):
        mobile = attrs.get("mobile", None)
        login_name = attrs.get("login_name", None)
        if not mobile and not login_name:
            raise exceptions.ValidationError("手机号用户名不能同时不存在")
        if mobile and login_name:
            raise exceptions.ValidationError("手机号用户名不能同时存在")
        exist = models.UserDetails.objects.filter(**attrs).exists()
        if not exist:
            raise exceptions.ValidationError("密码错误")
        return attrs


class UsernamePasswordLoginSerializer(BaseMd5PasswordSerializer, serializers.ModelSerializer):
    """用户名密码校验"""

    # 手机号校验

    # 验证码校验
    class Meta:
        model = models.UserDetails
        fields = ["login_name", "password"]

    def validate_login_name(self, value):
        exists = models.UserDetails.objects.filter(login_name=value,is_used=1).exists()
        if not exists:
            raise exceptions.ValidationError("用户名不存在")
        return value


class ChangePasswordByMobilePasswordSerializer(serializers.ModelSerializer):
    """用户名密码校验"""

    # mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 密码格式校验必须包含数字和字母，长度至少8位
    # password = serializers.CharField(required=True, validators=[
    #     RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")])
    # 确认密码校验
    new_password = serializers.CharField(
        required=True, validators=[RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")]
    )
    # 确认密码
    confirm_new_password = serializers.CharField(
        required=True, validators=[RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")]
    )

    # 验证码校验
    class Meta:
        model = models.UserDetails
        # fields = ["mobile", "password", "new_password", "confirm_new_password"]
        fields = ["new_password", "confirm_new_password"]

    def validate_new_password(self, value):
        """md5密码加密"""
        return md5_enc(value)

    def validate_confirm_new_password(self, value):
        new_password = self.initial_data['new_password']
        if new_password != value:
            raise exceptions.ValidationError("密码不一致")
        return md5_enc(value)


class UserInfoSerializer(serializers.ModelSerializer):
    gender = serializers.CharField(source="get_gender_display")  # 将数据库中的数组展示成文字
    create_time = serializers.SerializerMethodField()

    class Meta:
        model = models.UserDetails
        exclude = ["password", "roles", "id"]

    def get_create_time(self, obj):
        # 时间格式处理
        format_time = obj.create_time.strftime("%Y-%m-%d")
        return format_time


class SMSRetrievePasswordSerializer(serializers.ModelSerializer, BaseMd5PasswordSerializer, BaseSMSSerializer):
    """手机号找回密码"""

    mobile = serializers.CharField(required=True, validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")])
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")])

    password = serializers.CharField(
        required=True, validators=[RegexValidator(r"^(?=.*\d)(?=.*[a-zA-Z]).{8,}$", message="密码格式不正确(必须包含数字和字母，长度至少8位)")]
    )

    class Meta:
        model = models.UserDetails
        fields = ['mobile', 'password', 'code']

    def validate_mobile(self, value):
        exists = models.UserDetails.objects.filter(mobile=value).exists()
        if not exists:
            raise exceptions.ValidationError("手机号不存在")
        return value


# class PeakValleySerializer(serializers.ModelSerializer):
#     """削峰填谷电价序列化器"""
#
#     type_display = serializers.SerializerMethodField()
#     level_display = serializers.SerializerMethodField()
#     province_id = serializers.SerializerMethodField()
#
#     # province_name = serializers.CharField(source='province.name', read_only=True)
#     # province_id = serializers.CharField(source='province.id', read_only=True)
#
#     class Meta:
#         model = models.PeakValley
#         fields = ["type_display", "level_display", "type", "level", "province_id"]
#
#     def get_province_id(self, obj):
#         return obj['province']
#
#     def get_level_display(self, obj):
#         LEVEL_CHOICE = dict(models.PeakValley.LEVEL_CHOICE)
#         return LEVEL_CHOICE.get(obj['level'], '')
#
#     def get_type_display(self, obj):
#         TYPE_CHOICE = dict(models.PeakValley.TYPE_CHOICE)
#         return TYPE_CHOICE.get(obj['type'], '')


# class CustomizationDetailSerializer(serializers.ModelSerializer):
#     id = serializers.IntegerField(required=True, write_only=True)
#     # province_id = serializers.IntegerField(required=True, write_only=True)
#     # level = serializers.IntegerField(required=True, write_only=True)
#     # type = serializers.IntegerField(required=True, write_only=True)
#
#     price_spike = serializers.SerializerMethodField()  # 尖峰
#
#     price_peak = serializers.SerializerMethodField()  # 峰
#     price_flat = serializers.SerializerMethodField()  # 平
#     price_valley = serializers.SerializerMethodField()  # 谷
#
#     class Meta:
#         model = models.PeakValley
#         fields = [
#             "price_spike",
#             "price_peak",
#             "price_flat",
#             "price_valley",
#             'id',
#             'h0',
#             'h1',
#             'h2',
#             'h3',
#             'h4',
#             'h5',
#             'h6',
#             'h7',
#             'h8',
#             'h9',
#             'h10',
#             'h11',
#             'h12',
#             'h13',
#             'h14',
#             'h15',
#             'h16',
#             'h17',
#             'h18',
#             'h19',
#             'h20',
#             'h21',
#             'h2',
#             'h23',
#         ]
#         extra_kwargs = {
#             'level': {'required': True, 'write_only': True},
#             'type': {'required': True, 'write_only': True},
#             'h0': {'read_only': True},
#             'h1': {'read_only': True},
#             'h2': {'read_only': True},
#             'h3': {'read_only': True},
#             'h4': {'read_only': True},
#             'h5': {'read_only': True},
#             'h6': {'read_only': True},
#             'h7': {'read_only': True},
#             'h8': {'read_only': True},
#             'h9': {'read_only': True},
#             'h10': {'read_only': True},
#             'h11': {'read_only': True},
#             'h12': {'read_only': True},
#             'h13': {'read_only': True},
#             'h14': {'read_only': True},
#             'h15': {'read_only': True},
#             'h16': {'read_only': True},
#             'h17': {'read_only': True},
#             'h18': {'read_only': True},
#             'h19': {'read_only': True},
#             'h20': {'read_only': True},
#             'h21': {'read_only': True},
#             'h22': {'read_only': True},
#             'h23': {'read_only': True},
#         }
#
#     def get_price_spike(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 2:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_peak(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 1:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_flat(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 0:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_valley(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if getattr(obj, f'pv{i}') == -1:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price


# class CustomizationAddSerializer(serializers.ModelSerializer):
#     '''削峰填谷客制化添加'''
#
#     stations_list = serializers.ListSerializer(child=serializers.IntegerField(), required=True)
#
#     class Meta:
#         model = models.UnitPrice
#         exclude = ["user", "stations"]
#         extra_kwargs = {"uid": {"required": False}}
#
#     # def create(self, validated_data):
#     #     user = self.context.get('user_id')
#     #     user_ins = models.UserDetails.objects.get(id=user)
#     #     obj = models.UnitPrice.objects.create(user=user_ins, **validated_data)
#     #     return obj
#
#     def validate_name(self, value):
#         user = self.context.get('user_id')
#         user_ins = models.UserDetails.objects.get(id=user)
#         exist = models.UnitPrice.objects.filter(name=value, delete=0, user=user_ins).exists()
#         if exist:
#             raise exceptions.ValidationError("定制化电价名称已存在")
#         return value
#
#     def validate(self, attrs):
#         start = attrs["start"]
#         end = attrs["end"]
#         name = attrs["name"]
#         user = self.context.get('user_id')
#         user_ins = models.UserDetails.objects.get(id=user)
#         if end <= start:
#             raise exceptions.ValidationError("起始时间不能大于等于结束时间")
#         for station_id in attrs["stations_list"]:
#             tim_ins = models.UnitPrice.objects.filter(stations=station_id, delete=0, user=user_ins).all().values("start", 'end', "name")
#             for tim in tim_ins:
#                 if tim["start"] <= start <= tim['end'] or tim['start'] <= end <= tim["end"]:
#                     raise exceptions.ValidationError(f"<{name}>时间段与已存在的<{tim['name']}>发生冲突")
#
#         return attrs


class WebUsernamePasswordLoginSerializer(BaseMd5PasswordSerializer, serializers.Serializer):
    """web 序列化器"""

    password = serializers.CharField(required=True, max_length=32)
    username = serializers.CharField(required=True, max_length=32)

    def validate_username(self, value):
        """
        用户名命或手机号校验
        :param value:
        :return: user 对象
        """
        md5_password = md5_enc(self.initial_data.get("password"))
        if re.match(r"^1[3-9]\d{9}$", value):
            try:
                models.UserDetails.objects.get(mobile=int(value))
            except models.UserDetails.DoesNotExist:
                raise exceptions.ValidationError("手机号不存在")
            try:
                user_ins = models.UserDetails.objects.get(mobile=int(value), password=md5_password)
            except models.UserDetails.DoesNotExist:
                raise exceptions.ValidationError("手机号或密码不正确")
        else:
            try:
                models.UserDetails.objects.get(login_name=value)
            except models.UserDetails.DoesNotExist:
                raise exceptions.ValidationError("用户名不存在")
            try:
                user_ins = models.UserDetails.objects.get(login_name=value, password=md5_password)

            except models.UserDetails.DoesNotExist:
                raise exceptions.ValidationError("用户名或密码不正确")

        return user_ins
