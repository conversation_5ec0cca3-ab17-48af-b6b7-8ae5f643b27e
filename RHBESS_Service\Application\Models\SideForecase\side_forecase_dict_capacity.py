#!/usr/bin/env python
# coding=utf-8
#@Information:容量
#<AUTHOR> WYJ
#@Date         : 2023-08-30 10:20:37
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_dict_capacity.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-31 12:40:59


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecaseDictCapacity(user_Base):
    '容量'
    __tablename__ = "t_side_forecase_dict_capacity"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"所属行业")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=True,comment=u"排序索引")
    type_ = Column(CHAR(3), nullable=True,comment=u"1，仿真结果速查，2，其他费用速查")

    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        return "{'id':%s,'name':'%s'}" % (self.id,self.name)
        
    