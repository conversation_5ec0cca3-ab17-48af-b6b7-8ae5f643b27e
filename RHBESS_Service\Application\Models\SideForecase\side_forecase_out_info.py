#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-31 08:58:09
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_out_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-31 11:28:22

from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseOutInfo(user_Base):
    u'项目测算输出条件暂存表'
    __tablename__ = "t_side_forecase_out_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    pattern_type = Column(CHAR(2), nullable=True,comment=u"模式，只有用户侧储能有模式1or模式2，存1,2，其余为空")
    data_info = Column(Text, nullable=True, comment=u"存储内容")
    ss_or_jx = Column(CHAR(1), nullable=False,comment=u"速算或精细计算1速算2精细")
    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    project_out_info = relationship("ForecaseProject", backref="project_out_info")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'ss_or_jx':'%s'}" %(self.id,self.ss_or_jx)

   