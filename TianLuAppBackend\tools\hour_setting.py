import datetime
def create_time_mapping(m=5, day=None, value='--'):
    """
    构造时间字典：默认5分钟间隔
    """
    res = {}
    start_time = datetime.datetime.strptime('00:00', '%H:%M')
    total = 288 if m == 5 else 96  # 5分钟或15分钟
    if day:
        for i in range(total):
            t = start_time + datetime.timedelta(minutes=i * m)
            t = t.strftime('%H:%M')
            res[f'{day} {t}'] = value
    else:
        for i in range(total):
            t = start_time + datetime.timedelta(minutes=i * m)
            t = t.strftime('%H:%M')
            res[f'{t}'] = value
    return res


def create_time_mapping_2(m=5):
    """
    构造时间字典：默认5分钟间隔
    """
    res = {}
    start_time = datetime.datetime.strptime('00:00', '%H:%M')
    total = 288 if m == 5 else 96  # 5分钟或15分钟
    for i in range(total):
        t = start_time + datetime.timedelta(minutes=i * m)
        t = t.strftime('%H:%M')
        res[f'{t}'] = {}
    return res


def create_time_mapping_3(start_time, end_time, m=5):
    """
    构造时间字典：默认5分钟间隔, 找出所有开始时间和结束时间范围内每隔5分钟的时间点，输出格式：%Y-%m-%d %H:%M
    """
    res = {}

    while start_time < end_time:
        t = datetime.datetime.fromtimestamp(start_time.timestamp() - start_time.timestamp() % 300).strftime('%Y-%m-%d %H:%M')
        res[f'{t}'] = {}
        start_time += datetime.timedelta(minutes=m)
    return res


def create_one_minute_list():
    """
    构造一天一分钟时间列表
    """
    start_time = datetime.datetime.strptime('00:00', '%H:%M')
    res = []
    # 一天1440分钟
    for i in range(1440):
        t = start_time + datetime.timedelta(minutes=i)
        t = t.strftime('%H:%M')
        res.append(t)
    return res
