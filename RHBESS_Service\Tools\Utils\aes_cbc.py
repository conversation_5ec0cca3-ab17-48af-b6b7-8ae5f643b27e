#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-20 13:53:58
#@FilePath     : \RHBESS_Service\Tools\Utils\aes_cbc.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-21 08:50:05


import sys
reload(sys)
sys.setdefaultencoding("utf-8")
import os,time
from Crypto import Random
import base64
import hashlib
import xlsxwriter as xw
import pandas as pd
from Tools.Utils.time_utils import timeUtils

"""
从excel读取数据，写入另一个excel
padding : PKCS7
"""

workbook = xw.Workbook('s2.xls')  # 创建工作簿
def xw_toExcel(data,shell):  # xlsxwriter库储存数据到excel
    worksheet1 = workbook.add_worksheet(shell)  # 创建子表
    worksheet1.activate()  # 激活表
    # title = ['BMS1总告警','BMS1总故障','BMS2总告警','BMS2总故障','BMS3总告警','BMS3总故障','PCS1 故障状态','PCS1 警告状态','PCS2 故障状态','PCS2 警告状态','PCS3 故障状态','PCS3 警告状态']  # 设置表头
    # title = ['电表1累计充电电量','电表1累计放电电量','电表2累计充电电量','电表2累计放电电量','电表3累计充电电量','电表3累计放电电量']
    # title = ['输出总有功功率/kW','输出总无功功率/kVar','A 相 IGBT 温度/℃','B 相 IGBT 温度/℃','C 相 IGBT 温度/℃','环境温度/℃','最高单体温度/℃','最低单体温度/℃','最高单体电压',
    #          '最低单体电压','出水温度/℃','回水温度/℃','环境温度/℃','进水口压力值/Pa','出水口压力值/Pa','并网点功率/kW']
    title = ['PCS累计充电量 Low/kWh','PCS累计放电量 Low/kWh','BMS累计充电电量/kWh','BMS累计放电电量/kWh','电表正向有功电能/kWh','电表负向有功电能/kWh']
    # title = ['空调当前模式']
    worksheet1.write_row('A1', title)  # 从A1单元格开始写入表头
    i = 2  # 从第二行开始写入数据
    
    for j in range(len(data[0])):
        insertData = []
        for a in range(len(data)):
            insertData.append(data[a][j])
        row = 'A' + str(i)
        worksheet1.write_row(row, insertData)
        i += 1
        print ('------------',time.time(),'*****',j)
# def xw_toExcel_dongmu(data,shell):  # xlsxwriter库储存数据到excel
#     worksheet1 = workbook.add_worksheet(shell)  # 创建子表
#     worksheet1.activate()  # 激活表
#     title = ['时间','BMS1累计放电量','BMS1累计充电量','BMS12累计放电量','BMS2累计充电量','BMS3累计放电量','BMS3累计充电量','BMS4累计放电量','BMS4累计充电量','BMS5累计放电量','BMS5累计充电量','BMS6累计放电量','BMS6累计充电量','BMS7累计放电量','BMS7累计充电量',]  # 设置表头
#     # title = ['累计充电电量','累计放电电量']
#     worksheet1.write_row('A1', title)  # 从A1单元格开始写入表头
#     i = 2  # 从第二行开始写入数据
    
#     for j in range(len(data[0])):
#         insertData = []
#         for a in range(len(data)):
#             insertData.append(data[a][j])
#         row = 'A' + str(i)
#         worksheet1.write_row(row, insertData)
#         i += 1
#         print ('------------',time.time(),'*****',j)
if __name__ == "__main__":
    # print self.request.headers.get('Bean')  # 接收消息头信息
    df = pd.read_excel("QNKX101.xlsx",header=None)
    values_arrs = df.values  # 将所有内容转换成二维矩阵
    
    all = []
    for i in range(3):
        all.append([])
   
    for val in values_arrs:
        datas = eval(val[5])
        bodys = datas['body']
        # all[0].append(timeUtils.ssTtimes(datas['time']))
    #     for dev in bodys:  # 东睦的
    #         if dev['device'] == 'BMS1':
    #             all[1].append(dev['CuDis'])
    #             all[2].append(dev['CuCha'])
    #         if dev['device'] == 'BMS2':
    #             all[3].append(dev['CuDis'])
    #             all[4].append(dev['CuCha'])
    #         if dev['device'] == 'BMS3':
    #             all[5].append(dev['CuDis'])
    #             all[6].append(dev['CuCha'])
    #         if dev['device'] == 'BMS4':
    #             all[7].append(dev['CuDis'])
    #             all[8].append(dev['CuCha'])
    #         if dev['device'] == 'BMS5':
    #             all[9].append(dev['CuDis'])
    #             all[10].append(dev['CuCha'])
    #         if dev['device'] == 'BMS6':
    #             all[11].append(dev['CuDis'])
    #             all[12].append(dev['CuCha'])
    #         if dev['device'] == 'BMS7':
    #             all[13].append(dev['CuDis'])
    #             all[14].append(dev['CuCha'])
    # xw_toExcel_dongmu(all,'title1')

         
        for dev in bodys:
            if dev['device'] == 'PCS1':
                all[0].append(dev['P'])
            if dev['device'] == 'PCS2':
                all[1].append(dev['P'])
           
            #     all[1].append(dev['Q'])
            #     all[2].append(dev['IGBTAT'])
            #     all[3].append(dev['TGBTBT'])
            #     all[4].append(dev['TGBTCT'])
            #     all[5].append(dev['PAT'])
            # if dev['device'] == 'BMS' or dev['device'] == 'BMS1':
            #     all[0].append(dev['PAE'])
            #     all[1].append(dev['NAE'])
           
            #     all[8].append(dev['MUMax'])
            #     all[9].append(dev['MUMin'])
            #     all[10].append(dev['ET'])
            #     all[11].append(dev['RWT'])
            #     all[12].append(dev['BAT'])
            #     all[13].append(dev['IPV'])
            #     all[14].append(dev['OPV'])
            if dev['device'] == 'EMS':
                all[2].append(dev['PCC'])
                

            #     if 'PAE' in dev.keys():
            #         all[0].append(dev['PAE'])
            #         all[1].append(dev['NAE'])
            #     else:
            #         all[0].append('--')
            #         all[1].append('--')
                
            # if dev['device'] == 'BMS2':
            #     if 'PAE' in dev.keys():
            #         all[2].append(dev['PAE'])
            #         all[3].append(dev['NAE'])
            #     else:
            #         all[2].append('--')
            #         all[3].append('--')
               
            # if dev['device'] == 'BMS3':
            #     if 'PAE' in dev.keys():
            #         all[4].append(dev['PAE'])
            #         all[5].append(dev['NAE'])
            #     else:
            #         all[4].append('--')
            #         all[5].append('--')


                # all[3].append(dev['CuDis'])
                # if 'PAE' in dev.keys():
                #     all[4].append(dev['PAE'])
                #     all[5].append(dev['NAE'])
                # else:
                #     all[4].append('--')
                #     all[5].append('--')
                
        #  100kW
            # if dev['device'] == 'BMS':
            #     all[0].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[1].append('无故障' if dev['GFault'] == '0' else '故障')
            # if dev['device'] == 'PCS':
            #     all[2].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[3].append('无告警' if dev['alarm'] == '0' else '告警')

        #  200kW
            # if dev['device'] == 'BMS1':
            #     all[0].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[1].append('无故障' if dev['GFault'] == '0' else '故障')
            # if dev['device'] == 'BMS2':
            #     all[2].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[3].append('无故障' if dev['GFault'] == '0' else '故障')

            # if dev['device'] == 'PCS1':
            #     all[4].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[5].append('无告警' if dev['alarm'] == '0' else '告警')
            # if dev['device'] == 'PCS2':
            #     all[6].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[7].append('无告警' if dev['alarm'] == '0' else '告警')
           
        #   300 kW
            # if dev['device'] == 'BMS1':
            #     all[0].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[1].append('无故障' if dev['GFault'] == '0' else '故障')
            # if dev['device'] == 'BMS2':
            #     all[2].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[3].append('无故障' if dev['GFault'] == '0' else '故障')
            # if dev['device'] == 'BMS3':
            #     all[4].append('无告警' if dev['GAlarm'] == '0' else '告警')
            #     all[5].append('无故障' if dev['GFault'] == '0' else '故障')

            # if dev['device'] == 'PCS1':
            #     all[6].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[7].append('无告警' if dev['alarm'] == '0' else '告警')
            # if dev['device'] == 'PCS2':
            #     all[8].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[9].append('无告警' if dev['alarm'] == '0' else '告警')
            # if dev['device'] == 'PCS3':
            #     all[10].append('无故障' if dev['Fault'] == '0' else '故障')
            #     all[11].append('无告警' if dev['alarm'] == '0' else '告警')
                   
    xw_toExcel(all,'title1')  # 添禄的



    
workbook.close()  # 关闭表