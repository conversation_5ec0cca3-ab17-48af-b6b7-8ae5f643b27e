package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.station.StationCreateDTO;
import com.robestec.analysis.dto.station.StationQueryDTO;
import com.robestec.analysis.dto.station.StationUpdateDTO;
import com.robestec.analysis.service.StationService;
import com.robestec.analysis.vo.StationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电站管理API
 */
@RestController
@RequestMapping("/station")
@RequiredArgsConstructor
@Api(tags = "电站管理API")
public class StationController {

    private final StationService stationService;

    @GetMapping
    @ApiOperation("分页查询电站")
    public PageResult<StationVO> queryStation(@Validated StationQueryDTO queryDTO) {
        return stationService.queryStation(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增电站")
    public Result<Long> createStation(@Validated @RequestBody StationCreateDTO createDTO) {
        return Result.succeed(stationService.createStation(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增电站")
    public Result createStationList(@Validated @RequestBody List<StationCreateDTO> createDTOList) {
        stationService.createStationList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改电站")
    public Result updateStation(@PathVariable Long id, @Validated @RequestBody StationUpdateDTO updateDTO) {
        updateDTO.setId(id);
        stationService.updateStation(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除电站")
    public Result deleteStation(@PathVariable Long id) {
        stationService.deleteStation(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取电站详情")
    public Result<StationVO> getStation(@PathVariable Long id) {
        return Result.succeed(stationService.getStation(id));
    }

    @GetMapping("/name/{name}")
    @ApiOperation("根据英文名称查询电站")
    public Result<List<StationVO>> getStationByName(@PathVariable String name) {
        return Result.succeed(stationService.getStationByName(name));
    }

    @GetMapping("/register/{register}")
    @ApiOperation("根据注册状态查询电站")
    public Result<List<StationVO>> getStationByRegister(@PathVariable Integer register) {
        return Result.succeed(stationService.getStationByRegister(register));
    }

    @GetMapping("/count/register/{register}")
    @ApiOperation("统计注册状态的电站数量")
    public Result<Long> countByRegister(@PathVariable Integer register) {
        return Result.succeed(stationService.countByRegister(register));
    }
}
