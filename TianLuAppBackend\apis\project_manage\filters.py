from django_filters import rest_framework as filters

from apis.user.models import Project


class CustomProjectsFilter(filters.FilterSet):
    TYPE_CHOICE = (
        (1, "大工业"),
        (2, "一般工商业"),
        (3, "单一制工商业用户"),
        (4, "大工业用电(两部制)"),
        (5, "单一制"),
        (6, "两部制"),
        (7, "两部制工商业用户"),
        (8, "100千伏安及以上(两部制)大工业"),
        (10, "100千伏安以下(单一制,含行政事业单位办公场所用电)"),
        ),

    LEVEL_CHOICE = (
        (1, "不满1千伏"),
        (2, "1-10千伏"),
        (3, "35千伏"),
        (4, "110千伏"),
        (5, "220千伏及以上"),
        (6, "35-110千伏（不含）"),
        (7, "110-220千伏（不含）"),
        (8, "35千伏及以上"),
        (9, "35-110千伏以下"),
        (10, "110-220千伏以下"),
        (11, "110 (66)千伏"),
        (12, "20千伏"),
        (13, "66 千伏"),
        (14, "220 千伏"),
        (15, "110千伏及以上"),
        (16, "20-35 千伏以下"),
        (17, "110-330 千伏"),
        (18, "330千伏及以上"),
        (19, "35-110千伏"),
        (20, "10 (20)千伏"),
        (21, "35 千伏以下"),
        (22, "10千伏"),
        (23, "1-10（20）千伏"),
        (24, "220 (330)千伏"),
        (25, "100千伏安及以下和公变接入用电"),
        (26, "10千伏高供低计（380V/220V计量"),
        (27, "10千伏高供低计"),
    )
    name = filters.CharFilter(field_name='name')
    province = filters.CharFilter(field_name='province')
    # type_ = filters.ChoiceFilter(field_name='type', choices=TYPE_CHOICE)
    # level = filters.ChoiceFilter(field_name='level', choices=LEVEL_CHOICE)

    class Meta:
        model = Project
        fields = ['name', 'province']