package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 计划历史记录VO
 */
@Data
@ApiModel("计划历史记录VO")
public class PlanHistoryVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("电站名称")
    private String station;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("描述")
    private String descr;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("状态: 1-成功, 2-失败")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("时间字符串")
    private String timeStr;
}
