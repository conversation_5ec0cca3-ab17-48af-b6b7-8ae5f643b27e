package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.projectpack.ProjectPackCreateDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackQueryDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackUpdateDTO;
import com.robestec.analysis.entity.ProjectPack;
import com.robestec.analysis.vo.ProjectPackVO;

import java.util.List;

/**
 * 项目包服务接口
 */
public interface ProjectPackService extends ISuperService<ProjectPack> {

    /**
     * 分页查询项目包
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<ProjectPackVO> queryProjectPack(ProjectPackQueryDTO queryDTO);

    /**
     * 创建项目包
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createProjectPack(ProjectPackCreateDTO createDTO);

    /**
     * 更新项目包
     * @param updateDTO 更新参数
     */
    void updateProjectPack(ProjectPackUpdateDTO updateDTO);

    /**
     * 删除项目包
     * @param id 记录ID
     */
    void deleteProjectPack(Long id);

    /**
     * 获取项目包详情
     * @param id 记录ID
     * @return 记录详情
     */
    ProjectPackVO getProjectPack(Long id);

    /**
     * 批量创建项目包
     * @param createDTOList 创建参数列表
     */
    void createProjectPackList(List<ProjectPackCreateDTO> createDTOList);

    /**
     * 根据用户ID查询项目包
     * @param userId 用户ID
     * @return 记录列表
     */
    List<ProjectPackVO> getProjectPackByUserId(Long userId);

    /**
     * 根据项目包名称查询项目包
     * @param name 项目包名称
     * @return 记录列表
     */
    List<ProjectPackVO> getProjectPackByName(String name);

    /**
     * 统计用户的项目包数量
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(Long userId);
}
