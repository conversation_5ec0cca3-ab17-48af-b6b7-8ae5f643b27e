package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.TPlanPowerRecords;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 功率计划关联记录Mapper
 * 对应Python中的TPlanPowerRecords相关数据库操作
 */
@Mapper
public interface TPlanPowerRecordsMapper extends BaseMapper<TPlanPowerRecords> {

    /**
     * 根据功率ID查询计划关联记录
     */
    @Select("SELECT * FROM t_plan_power_records " +
            "WHERE power_id = #{powerId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPlanPowerRecords> selectByPowerId(@Param("powerId") Long powerId);

    /**
     * 根据计划ID查询功率关联记录
     */
    @Select("SELECT * FROM t_plan_power_records " +
            "WHERE plan_id = #{planId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPlanPowerRecords> selectByPlanId(@Param("planId") Long planId);

    /**
     * 根据功率ID和计划ID查询关联记录
     */
    @Select("SELECT * FROM t_plan_power_records " +
            "WHERE power_id = #{powerId} AND plan_id = #{planId} AND is_use = 1")
    TPlanPowerRecords selectByPowerIdAndPlanId(@Param("powerId") Long powerId, @Param("planId") Long planId);

    /**
     * 根据功率ID列表软删除记录
     */
    @Update("<script>" +
            "UPDATE t_plan_power_records SET is_use = 0 " +
            "WHERE power_id IN " +
            "<foreach collection='powerIds' item='powerId' open='(' separator=',' close=')'>" +
            "  #{powerId}" +
            "</foreach>" +
            "</script>")
    int softDeleteByPowerIds(@Param("powerIds") List<Long> powerIds);

    /**
     * 根据计划ID列表软删除记录
     */
    @Update("<script>" +
            "UPDATE t_plan_power_records SET is_use = 0 " +
            "WHERE plan_id IN " +
            "<foreach collection='planIds' item='planId' open='(' separator=',' close=')'>" +
            "  #{planId}" +
            "</foreach>" +
            "</script>")
    int softDeleteByPlanIds(@Param("planIds") List<Long> planIds);

    /**
     * 根据序号查询记录
     */
    @Select("SELECT * FROM t_plan_power_records " +
            "WHERE serial_number = #{serialNumber} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<TPlanPowerRecords> selectBySerialNumber(@Param("serialNumber") Integer serialNumber);

    /**
     * 统计功率ID的关联记录数量
     */
    @Select("SELECT COUNT(*) FROM t_plan_power_records " +
            "WHERE power_id = #{powerId} AND is_use = 1")
    int countByPowerId(@Param("powerId") Long powerId);

    /**
     * 统计计划ID的关联记录数量
     */
    @Select("SELECT COUNT(*) FROM t_plan_power_records " +
            "WHERE plan_id = #{planId} AND is_use = 1")
    int countByPlanId(@Param("planId") Long planId);
}
