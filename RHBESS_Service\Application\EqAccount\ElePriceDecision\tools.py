import functools
import logging
from datetime import datetime, timedelta

import pymysql
from dbutils.persistent_db import PersistentDB
from Application.Cfg.decision_cfg import model_config


def merge_dicts(*lists):
    merged_dict = {}

    # 所有选择的选项对应的 keys 集合
    keys_ = []

    for lst in lists:
        for item in lst:
            name = item["datetime"]
            if name in merged_dict:
                merged_dict[name].update(item)
            else:
                merged_dict[name] = item

            #
            for k in item.keys():
                if k not in keys_:
                    keys_.append(k)

    merged_list = list(merged_dict.values())

    # 补齐缺值
    for item in merged_list:
        for k in keys_:
            if k not in item:
                item[k] = '--'

    return merged_list


def merge_dicts_with_default(*lists, default_value=''):
    merged_dict = {}

    items = []

    for lst in lists:
        for item in lst:
            name = item["datetime"]
            if name not in merged_dict:
                merged_dict[name] = {}

            # 使用字典的 setdefault 方法，如果字段不存在，则设置默认值
            for key, value in item.items():
                # merged_dict[name].setdefault(key, default_value)
                merged_dict[name][key] = value
                if key not in items:
                    items.append(key)

    items = list(set(items))
    for name, data in merged_dict.items():
        for item in items:
            if item not in data:
                merged_dict[name][item] = default_value

    merged_list = [{"datetime": name, **data} for name, data in merged_dict.items()]
    return merged_list


def split_time(time_str):
    time_obj = datetime.strptime(time_str, "%H:%M")
    times = [time_obj + timedelta(minutes=15*i) for i in range(4)]
    return [t.strftime("%H:%M") for t in times]


def log_exceptions(logger):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                # 将异常信息写入日志
                logger.error(f"Exception in {func.__name__}: {str(e)}")
                # 可以选择重新抛出异常，或者返回一个默认值，或者做其他处理
                # raise  # 重新抛出异常
                # return None  # 返回默认值
                # 其他处理逻辑
                return
        return wrapper
    return decorator


class DwsEsStationDatabaseTool:
    # 　查询大储大同&阳泉链各个站的累计充放电量使用
    DB_HOSTNAME_READ = model_config.get('mysql', "DB_HOSTNAME_READ")
    DB_PORT_READ = int(model_config.get('mysql', "DB_PORT_READ"))
    DB_DATABASE_READ = model_config.get('mysql', "DB_DATABASE_READ")
    DB_USERNAME_READ = model_config.get('mysql', "DB_USERNAME_READ")
    DB_PASSWORD_READ = model_config.get('mysql', "DB_PASSWORD_READ")

    def __init__(self):
        self.dwd_pool = PersistentDB(pymysql, 10, **{
            "host": self.DB_HOSTNAME_READ,  # 数据库主机地址
            "user": self.DB_USERNAME_READ,  # 数据库用户名
            "password": self.DB_PASSWORD_READ,  # 数据库密码
            "database": self.DB_DATABASE_READ,  # 数据库名称
            "port": self.DB_PORT_READ,
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()


dws_es_station_db_tool = DwsEsStationDatabaseTool()


class LocalDatabaseTool:
    DB_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
    DB_PORT = int(model_config.get('mysql', "DB_PORT"))
    DB_DATABASE = model_config.get('mysql', "DB_DATABASE")
    DB_USERNAME = model_config.get('mysql', "DB_USERNAME")
    DB_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

    def __init__(self):
        self.dwd_pool = PersistentDB(pymysql, 10, **{
            "host": self.DB_HOSTNAME,  # 数据库主机地址
            "user": self.DB_USERNAME,  # 数据库用户名
            "password": self.DB_PASSWORD,  # 数据库密码
            "database": self.DB_DATABASE,  # 数据库名称
            "port": self.DB_PORT,
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()


datatool = LocalDatabaseTool()

# 设置日志配置
logging.basicConfig(filename='sanxi_ElePriceError.log', level=logging.ERROR)

# 创建一个 logger 实例
logger = logging.getLogger(__name__)


# 补齐字典缺值
def fill_missing_values(table_data, keys):
    if table_data:
        for item in table_data.values():
            for k in keys:
                if k not in item:
                    item[k] = '--'
        return table_data
    else:
        return {}


if __name__ == '__main__':
    # 测试函数
    list1 = [
        {"name": "zjagm1", "age": 31},
        {"name": "zjagm2", "age": 32},
        {"name": "zjagm3", "age": 33},
        {"name": "zjagm5", "age": 35}
    ]

    list2 = [
        {"name": "zjagm1", "sex": 32},
        {"name": "zjagm2", "sex": 33},
        {"name": "zjagm3", "sex": 34},
        {"name": "zjagm4", "sex": 35}
    ]

    list3 = [
        {"name": "zjagm1", "city": "Beijing"},
        {"name": "zjagm2", "city": "Shanghai"},
        {"name": "zjagm4", "city": "Guangzhou"}
    ]

    # result = merge_dicts(list1, list2, list3)
    # print(result)

    print(split_time("09:00"))
