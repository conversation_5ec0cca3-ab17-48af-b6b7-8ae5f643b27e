from django.db import models

from common.base_models import BaseModel
from apis.user.models import MaterStation


# Create your models here.
class RunningReport(BaseModel):
    """运行报告-*报"""""

    report_type = models.SmallIntegerField(
        verbose_name="报告类型",
        choices=((1, "日报"), (2, "周边"), (3, "月报")),
        default=1,
    )
    datetime = models.CharField(verbose_name="(开始)日期", max_length=10)
    datetime_end = models.CharField(verbose_name="结束日期", max_length=10, null=True, blank=True)
    station_name = models.CharField(verbose_name="并网点名称", max_length=32)
    charge_cap = models.DecimalField(verbose_name="充电量", decimal_places=2, max_digits=32)
    discharge_cap = models.DecimalField(verbose_name="放电量", decimal_places=2, max_digits=32)
    comp_rate = models.DecimalField(verbose_name="充放电量完成率", decimal_places=2, max_digits=32)
    count = models.IntegerField(verbose_name="充放电次数", null=True, blank=True)

    theory_charge = models.DecimalField(verbose_name="基准充电量", decimal_places=2, max_digits=32)
    theory_charge_comp_rate = models.DecimalField(verbose_name="基准充电量完成率", decimal_places=2, max_digits=32)
    theory_discharge = models.DecimalField(verbose_name="基准放电量", decimal_places=2, max_digits=32)
    theory_discharge_comp_rate = models.DecimalField(verbose_name="基准放电量完成率", decimal_places=2, max_digits=32)
    theory_income_day = models.DecimalField(verbose_name="日基准收益", decimal_places=2, max_digits=32)
    income_day_reach_yield = models.CharField(verbose_name="日收益达成率（%）", max_length=10)
    theory_income_month = models.DecimalField(verbose_name="月基准收益", decimal_places=2, max_digits=32)
    income_month = models.DecimalField(verbose_name="月收益", decimal_places=2, max_digits=32)
    theory_income_year = models.DecimalField(verbose_name="年基准收益", decimal_places=2, max_digits=32)
    income_year = models.DecimalField(verbose_name="年收益", decimal_places=2, max_digits=32)
    theory_income_all = models.DecimalField(verbose_name="累计基准收益", decimal_places=2, max_digits=32)
    income_all = models.DecimalField(verbose_name="累计收益", decimal_places=2, max_digits=32)
    income_all_reach_yield = models.CharField(verbose_name="累计收益达成率（%）", max_length=10)
    star_level = models.SmallIntegerField(verbose_name="星级：1-5星")
    reach_the_standard = models.SmallIntegerField(verbose_name="日收益是否达标 日收益达成率”≥100%，则判断为“是:1”，否则判断为“否:0”")
    soc_init = models.DecimalField(verbose_name="SOC初始值", decimal_places=2, max_digits=32, null=True, blank=True)
    soc_final = models.DecimalField(verbose_name="SOC终值", decimal_places=2, max_digits=32, null=True, blank=True)
    soc_max = models.DecimalField(verbose_name="SOC最大值", decimal_places=2, max_digits=32, null=True, blank=True)
    soc_min = models.DecimalField(verbose_name="SOC最小值", decimal_places=2, max_digits=32, null=True, blank=True)

    spike_charge = models.DecimalField(verbose_name="尖峰充电量", decimal_places=2, max_digits=32)
    spike_discharge = models.DecimalField(verbose_name="尖峰放电量", decimal_places=2, max_digits=32)
    peak_charge = models.DecimalField(verbose_name="峰时充电量", decimal_places=2, max_digits=32)
    peak_discharge = models.DecimalField(verbose_name="峰时放电量", decimal_places=2, max_digits=32)
    flat_charge = models.DecimalField(verbose_name="平时充电量", decimal_places=2, max_digits=32)
    flat_discharge = models.DecimalField(verbose_name="平时放电量", decimal_places=2, max_digits=32)
    valley_charge = models.DecimalField(verbose_name="谷时充电量", decimal_places=2, max_digits=32)
    valley_discharge = models.DecimalField(verbose_name="谷时放电量", decimal_places=2, max_digits=32)
    dvalley_charge = models.DecimalField(verbose_name="深谷时充电量", decimal_places=2, max_digits=32)
    dvalley_discharge = models.DecimalField(verbose_name="深谷时放电量", decimal_places=2, max_digits=32)

    accu_charge = models.DecimalField(verbose_name="累计充电量", decimal_places=2, max_digits=32)
    accu_discharge = models.DecimalField(verbose_name="累计放电量", decimal_places=2, max_digits=32)
    effic = models.DecimalField(verbose_name="累计充放电效率", decimal_places=2, max_digits=32)

    income = models.DecimalField(verbose_name="收益", decimal_places=2, max_digits=32)
    fault = models.IntegerField(verbose_name="故障")
    alarm = models.IntegerField(verbose_name="告警")
    # 备注字段之前是分析字段（输入框）现在改为备注；分析改为下拉框，所以字段名有误
    analyse = models.CharField(verbose_name="备注", max_length=512, null=True, blank=True)
    en_analyse = models.CharField(verbose_name="备注", max_length=1024, blank=True, null=True)
    analyze_ids = models.CharField(verbose_name="分析类型ID, 多个用英文逗号隔开", max_length=256, null=True, blank=True)

    class Meta:
        db_table = "t_running_report"
        unique_together = ('report_type', 'datetime', 'station_name')

    def __str__(self):
        return str(self.station_name)


class AnalyzeDict(BaseModel):
    """运行日报分析字典"""
    name = models.CharField(verbose_name="分析类型", max_length=32)
    en_name = models.CharField(verbose_name="分析类型-英文", max_length=128, blank=True, null=True)
    is_delete =models.SmallIntegerField(verbose_name="是否删除", choices=((1, "删除"), (0, "不删除")), default=0,
                                         null=True, blank=True)


    class Meta:
        db_table = "t_analyze_dict"

class ProjectPack(BaseModel):
    """项目包"""""

    user_id = models.SmallIntegerField(verbose_name="用户ID")
    name = models.CharField(verbose_name="项目包名称", max_length=32)
    en_name = models.CharField(verbose_name="项目包名称", max_length=128, blank=True, null=True)
    data = models.TextField(verbose_name='项目包信息')

    class Meta:
        db_table = "t_project_pack"


class PowerDeliverRecords(BaseModel):
    """
    功率计划下发记录
    """
    name = models.CharField(verbose_name='任务名称', max_length=32)
    en_name = models.CharField(verbose_name='任务名称', max_length=128, blank=True, null=True)
    mobile = models.CharField(verbose_name='联系人电话', max_length=32)
    power_list = models.TextField(verbose_name='功率计划')
    station_list = models.TextField(verbose_name='关联并网点')
    user_id = models.SmallIntegerField(verbose_name='用户ID')

    class Meta:
        db_table = "t_power_deliver_records"


class PlanDeliverRecords(BaseModel):
    """
    功率计划下发与下发历史记录表
    """
    plan = models.ForeignKey(
        to="user.StationPlanHistory",
        verbose_name="下发历史记录",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )

    power = models.ForeignKey(
        to="PowerDeliverRecords",
        verbose_name="功率计划下发记录",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
    )
    serial_number = models.SmallIntegerField(verbose_name='功率计划序号')

    class Meta:
        db_table = "t_plan_power_records"


class DictModel(models.Model):
    """
    模型表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_use = models.SmallIntegerField(default=1, verbose_name='是否删除 1是 0否 默认1')
    name = models.CharField(max_length=50, verbose_name='类型名称')
    rate = models.FloatField(max_length=11, verbose_name='准确率，百分之，两位小数')
    is_active = models.SmallIntegerField(default=1, verbose_name='是否启用1是0否 默认1')
    type = models.IntegerField(verbose_name='1自己预测 2别人预测，默认1')

    class Meta:
        db_table = 't_dict_model'
        verbose_name = '模型表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class DictModelTarget(models.Model):
    """
    模型指标表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_use = models.SmallIntegerField(default=1, verbose_name='是否删除 1是 0否 默认1')
    name = models.CharField(max_length=50, verbose_name='类型名称')
    en_name = models.CharField(max_length=128, verbose_name='类型名称', blank=True, null=True)
    type = models.IntegerField(default=1, verbose_name='1 自己预测 2别人预测 默认1')

    class Meta:
        db_table = 't_dict_model_target'
        verbose_name = '模型指标表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class DictModelRate(models.Model):
    """
    模型准确率表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    target = models.ForeignKey(
        'DictModelTarget',
        on_delete=models.RESTRICT,
        verbose_name='模型指标id,外键'
    )
    mstation = models.ForeignKey(
        MaterStation,
        on_delete=models.RESTRICT,
        verbose_name='主站id，外键t_master_stations'
    )
    model = models.ForeignKey(
        'DictModel',
        on_delete=models.RESTRICT,
        verbose_name='模型id,外键'
    )
    forecast_time = models.DateField(verbose_name='预测时间')
    rate = models.FloatField(max_length=11, verbose_name='准确率，百分之，两位小数')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_use = models.SmallIntegerField(default=1, verbose_name='是否删除1是 0否 默认1')

    class Meta:
        db_table = 't_dict_model_rate'
        verbose_name = '模型准确率表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"Rate for {self.target.name} at {self.forecast_time}"


class DictTargetModel(models.Model):
    """
    模型指标和模型关联关系表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    target = models.ForeignKey(
        'DictModelTarget',
        on_delete=models.RESTRICT,
        verbose_name='模型指标id，外键'
    )
    model = models.ForeignKey(
        'DictModel',
        on_delete=models.RESTRICT,
        verbose_name='模型id,外键'
    )
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 't_dict_target_model'
        verbose_name = '模型指标和模型关联关系表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.target.name} - {self.model.name}"


class ModelForecastValue(models.Model):
    """
    预测值表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    target = models.ForeignKey(
        'DictModelTarget',
        on_delete=models.RESTRICT,
        verbose_name='模型指标id,外键'
    )
    mstation = models.ForeignKey(
        MaterStation,
        on_delete=models.RESTRICT,
        verbose_name='主站id，外键t_master_stations'
    )
    model = models.ForeignKey(
        'DictModel',
        on_delete=models.RESTRICT,
        verbose_name='模型id,外键'
    )
    forecast_time = models.DateField(verbose_name='预测时间')
    forecast_hour_min = models.TimeField(verbose_name='预测时间(时分)')
    value = models.FloatField(verbose_name='数值')

    class Meta:
        db_table = 't_model_forecast_value'
        verbose_name = '模型预测数据'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"Forecast Value for {self.target_id.name} at {self.forecast_time}"


class ModelResponseLoadValue(models.Model):
    """
    负荷预测数据存储表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    station_name = models.CharField(max_length=255, verbose_name='从站名称')
    model_id = models.IntegerField(verbose_name='模型id')
    forecast_time = models.DateField(verbose_name='预测时间')
    forecast_hour_min = models.TimeField(verbose_name='预测时间(时分)')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_use = models.SmallIntegerField(default=1, verbose_name='是否使用1是0否，默认1')
    value = models.FloatField(verbose_name='预测值')
    type = models.SmallIntegerField(verbose_name='类型，0总和，1基线负荷，2实际负荷，3预测负荷')

    class Meta:
        db_table = 't_model_response_load_value'
        verbose_name = '负荷预测数据存储表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"Forecast Load Value for {self.station_name} at {self.forecast_time}"


from django.db import models

class TModelChargeDecisionLoadValue(models.Model):
    """
    策略负荷、收益表
    """
    id = models.AutoField(primary_key=True, verbose_name='主键')
    station_name = models.CharField(max_length=255, verbose_name='从站名称')
    model_id = models.IntegerField(verbose_name='模型id')
    forecast_time = models.DateField(verbose_name='预测时间年-月-日')
    type = models.SmallIntegerField(verbose_name='数据类型1 策略负荷类 2策略收益类')
    forecast_hour_min = models.TimeField(null=True, blank=True, verbose_name='预测时间 时:分:秒')
    electricity_price = models.FloatField(null=True, blank=True, verbose_name='电价')
    default_strategy_identifying = models.SmallIntegerField(null=True, blank=True, verbose_name='默认策略标识')
    default_strategy_value = models.FloatField(null=True, blank=True, verbose_name='默认策略值')
    current_strategy_identifying = models.SmallIntegerField(null=True, blank=True, verbose_name='当前策略标识')
    current_strategy_value = models.FloatField(null=True, blank=True, verbose_name='当前策略值')
    algorithm_strategy_value = models.FloatField(null=True, blank=True, verbose_name='算法策略')
    load_forecasting_value = models.FloatField(null=True, blank=True, verbose_name='负荷预测值')
    load_reality_value = models.FloatField(null=True, blank=True, verbose_name='负荷实际值')
    transformer_capacity_max_value = models.FloatField(null=True, blank=True, verbose_name='变压器安全容量最大值')
    transformer_load_diff_value = models.FloatField(null=True, blank=True, verbose_name='实际负荷和安全容量差值')
    rated_power = models.FloatField(null=True, blank=True, verbose_name='额定功率值')
    default_strategy_profit = models.FloatField(null=True, blank=True, verbose_name='默认策略收益')
    algorithm_strategy_profit = models.FloatField(null=True, blank=True, verbose_name='算法策略收益')
    current_strategy_profit = models.FloatField(null=True, blank=True, verbose_name='当前策略收益')
    reality_profit = models.FloatField(null=True, blank=True, verbose_name='实际收益')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_use = models.SmallIntegerField(default=1, verbose_name='是否使用1是0否，默认1')

    class Meta:
        db_table = 't_model_chargedecision_load_value'
        verbose_name = '策略负荷、收益表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"Charge Decision Load Value for {self.station_name} at {self.forecast_time}"


