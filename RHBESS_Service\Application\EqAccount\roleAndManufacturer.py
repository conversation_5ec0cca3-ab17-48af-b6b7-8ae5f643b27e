# -*- coding:utf-8 -*-
import json
import logging
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.role import Role
from Application.Models.User.manufacturer import Manufacturer 
from Application.Models.User.organization_type import OrganizationType
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import Translate_cls
from Tools.DB.redis_con import r_real

class RoleAndManufacturerIntetface(BaseHandler):
    '''
    @description: 角色和厂商管理
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        try:
            if kt == 'GetAllRole':  # 获取所有角色
                data = []
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                ppageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,ppageSize:%s" %(descr, pageNum, ppageSize))
                if descr :
                    total = user_session.query(func.count(Role.id)).filter(Role.descr.like('%' + descr + '%')).scalar()
                    all = user_session.query(Role).filter(Role.descr.like('%' + descr + '%')).order_by(Role.id.desc()
                    ).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                else:
                    total = user_session.query(func.count(Role.id)).scalar()
                    all = user_session.query(Role).order_by(Role.id.asc()).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                for org in all:
                    d = eval(str(org))
                    d['station_id'] = eval(org.station_id)
                    if lang == 'en':
                        replaced_data = Role.replace_en_fields(d, "")
                        d.update(replaced_data)
                    data.append(d)
                return self.returnTotalSuc(data,total)
            
            elif kt == 'GetAllFacture':  # 获取所有厂商
                data = []
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                ppageSize = int(self.get_argument('pageSize',20))
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,ppageSize:%s"%(descr,pageNum,ppageSize))
                if descr :
                    total = user_session.query(func.count(Manufacturer.id)).filter(Manufacturer.descr.like('%' + descr + '%')).scalar()
                    all = user_session.query(Manufacturer).filter(Manufacturer.descr.like('%' + descr + '%')).order_by(Manufacturer.id.desc()
                    ).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                else:
                    total = user_session.query(func.count(Manufacturer.id)).scalar()
                    all = user_session.query(Manufacturer).order_by(Manufacturer.id.asc()).limit(ppageSize).offset((pageNum-1)*ppageSize).all()
                for org in all:
                    data.append(eval(str(org)))
                return self.returnTotalSuc(data,total)

            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close() 


    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        try:
        # if 1:
            if kt == 'DeleteRole':  # 角色及对应用户
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                org = user_session.query(Role).filter(Role.id==id).first()
                if not org:
                    return self.customError("id为空或无效") if lang == 'zh' else self.customError("Invalid ID")
                org.delete_role(id)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
        
            elif kt == 'AddRole':  # 添加角色
                descr = self.get_argument('descr',None)
                station_id = self.get_argument('station_id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s,station_id:%s"%(descr,station_id))
                if not descr:
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                elif user_session.query(Role).filter(Role.descr == descr).first():
                    return self.customError("角色已存在") if lang == 'zh' else self.customError("Role already exists")
                else:
                    ty = 1 if lang == 'en' else 2
                    t_cls = Translate_cls(ty)
                    t_res = t_cls.str_chinese(descr)
                    if ty == 2:
                        en_descr = t_res
                    else:
                        en_descr = descr
                        descr = t_res
                    org = Role(descr=descr,station_id=station_id,authority='[]',en_authority='[]',select_key='["Home"]', en_descr=en_descr)
                    user_session.add(org)
                    user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
               
            elif kt == 'DeleteFacture':  # 删除厂商
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(Manufacturer).filter(Manufacturer.id==id).first()
                if not delete_apex:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                delete_apex.delete_structure(id)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            
            elif kt == 'AddFacture':  # 添加厂商
                descr = self.get_argument('descr',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s"%(descr))
                if not descr:
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                elif user_session.query(Manufacturer).filter(Manufacturer.descr == descr).first():
                    return self.customError("厂商已存在") if lang == 'zh' else self.customError("Manufacturer already exists")
                else:
                    org = Manufacturer(descr=descr,op_ts=timeUtils.getNewTimeStr())
                    user_session.add(org)
                    user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
                
            elif kt == 'UpdateRole':  # 修改角色
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                station_id = self.get_argument('station_id',None)
                authority = self.get_argument('authority',None)
                select_key = self.get_argument('select_key',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s,descr:%s,station_id:%s,authority:%s,select_key:%s"%(id,descr,station_id,authority,select_key))
                
                to_update = user_session.query(Role).filter(Role.id==id).first()
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                if not to_update:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                if descr:
                    t_res = t_cls.str_chinese(descr)
                    if ty == 2:
                        en_descr = t_res
                    else:
                        en_descr = descr
                        descr = t_res
                    to_update.descr = descr
                    to_update.en_descr = en_descr
                if authority:
                    authority = str(authority).replace('false', 'False').replace('true', 'True')
                    # t_res = t_cls.str_chinese(authority)
                    if ty == 2:
                        to_update.authority = authority
                    else:
                        to_update.en_authority = authority
                    # to_update.en_authority = json.dumps(en_authority)
                    # to_update.authority = authority
                if station_id:
                    if list(to_update.station_id) != list(station_id):
                        to_update.authority = '[]'
                    to_update.station_id = station_id
                # else:
                #     if authority:
                #         authority = str(authority).replace('false','False').replace('true','True')
                #         to_update.authority = authority
                if select_key:
                    to_update.select_key = select_key
                
                user_session.commit()
                # if authority:
                #     # 发布翻译数据
                #     pdr_data = {'id': id,
                #                 'table': 'c_user_role',
                #                 'update_data': {'authority': authority}}
                #     pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                #     r_real.publish(pub_name, json.dumps(pdr_data))
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)

            elif kt == 'UpdateFacture':  # 修改厂商
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                descr = self.get_argument('descr',None)
                if DEBUG:
                    logging.info("id:%s,descr:%s"%(id,descr))
                to_update = user_session.query(Manufacturer).filter(Manufacturer.id==id).first()

                if not descr:
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                if not to_update:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                manufacturer = user_session.query(Manufacturer).filter(Manufacturer.descr == descr).first()
                if manufacturer and manufacturer.id != int(id):
                    return self.customError("厂商已存在") if lang == 'zh' else self.customError("Manufacturer already exists")
                to_update.descr = descr
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
              
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()

        # user_session.rollback()
        # user_session.close()

    
