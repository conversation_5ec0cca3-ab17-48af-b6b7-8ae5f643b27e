import logging


def get_logger(name='default'):
    # 创建一个名为my_logger的日志记录器
    logger = logging.getLogger(name)

    # 设置日志级别为DEBUG
    logger.setLevel(logging.DEBUG)

    # 创建一个日志处理器，将日志输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)

    # 创建一个日志处理器，将日志输出到文件
    file_handler = logging.FileHandler(f'{name}.log')
    file_handler.setLevel(logging.DEBUG)

    # 创建一个日志格式器，定义日志的输出格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # 创建一个日志格式器，定义日志的输出格式，包括文件名和行号
    formatter_ = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s - [%(filename)s:%(lineno)d]')

    # 将日志格式器添加到日志处理器中
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter_)

    # 将日志处理器添加到日志记录器中
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger


if __name__ == '__main__':
    my_logger = get_logger('test')
    # 使用日志记录器记录一条日志
    my_logger.debug('这是一条debug级别的日志')
    my_logger.info('这是一条info级别的日志')
    my_logger.warning('这是一条warning级别的日志')
    my_logger.error('这是一条error级别的日志')
    my_logger.critical('这是一条critical级别的日志')
