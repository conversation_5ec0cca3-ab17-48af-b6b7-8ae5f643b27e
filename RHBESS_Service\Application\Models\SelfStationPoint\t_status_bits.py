#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-07 14:16:00
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_status_bits.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:22:32

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_status import StatusPT

class StatusBitsPT(mqtt_Base):
    ''' 状态分解说明表 '''
    __tablename__ = "t_status_bits"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    status_id = Column(Integer, ForeignKey("t_status.id"),nullable=False, comment=u"所属状态量")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    desc_on = Column(String(256), nullable=True,comment=u"合闸描述")
    desc_off = Column(String(256), nullable=True,comment=u"分闸描述")
    bits = Column(Integer, nullable=True,comment=u"位数0-15")

    status_bit = relationship("StatusPT",backref='status_bit')
    en_desc_on = Column(String(256), nullable=True, comment=u"合闸描述")
    en_desc_off = Column(String(256), nullable=True, comment=u"分闸描述")
    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','desc_on':'%s','desc_off':'%s','bits':%s,'en_desc_on':'%s','en_desc_off':'%s'}" % (self.id,self.name,self.descr,self.desc_on,self.desc_off,self.bits,self.en_desc_on,self.en_desc_off)
    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}