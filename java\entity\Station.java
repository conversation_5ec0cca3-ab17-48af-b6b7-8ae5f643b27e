package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 电站表
 * 对应Python模型: Station
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_station")
public class Station extends SuperEntity {

    /**
     * 英文名称
     */
    @TableField("name")
    private String name;

    /**
     * 中文名称
     */
    @TableField("descr")
    private String descr;

    /**
     * 是否注册: 1-是, 0-否
     */
    @TableField("register")
    private Integer register;

    /**
     * 索引
     */
    @TableField("index")
    private Integer index;

    /**
     * 操作时间戳
     */
    @TableField("op_ts")
    private LocalDateTime opTs;
}
