import json
import os
import concurrent.futures
import traceback

import math
from django.db import connections
from django_redis import get_redis_connection
from rest_framework.views import APIView
from rest_framework.response import Response
import openpyxl
from openpyxl.styles import Alignment
from TianLuAppBackend.settings import BASE_DIR
from apis.user import models
from apis.user.models import AggrFaultAlarm
from common import common_response_code
from middlewares.authentications import JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication
from django.db.models import Sum
import datetime

from serializers import monitor_serializers
from serializers.mqtt_serializers import AlarmSerializer, UnAlarmSerializer
from serializers.monitor_serializers import AlarmSendSMSSerializer, WebAlarmSendSMSSerializer, AlarmFeedbackSerializer
from django.conf import settings
from common.database_pools import dwd_db_tool
from settings.alarm_zh_en_mapping import ALARM_ZH_EN_MAP
from tools.gen_excel import create_excel, post2minio
from tools.send_mail import sendMail_
from tools.units_conversion import to_tuple
from django.db.models import Q
from settings.alarm_settings import alarm_detail as alarm_detail_v2
from settings.alarm_settings_v3 import alarm_detail as alarm_detail_v3

from settings.ocs_settings import alarm_tigger_point_v2,alarm_report_point_v2,alarm_tigger_point_v3,alarm_report_point_v3
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


class IncomeView(APIView):
    """收益总视图"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0).all()
        income_date = datetime.date.today()
        detail_dic = {
            "today_general_income": 0,
            "month_general_income": 0,
            "year_general_income": 0,
            "all_general_income": 0,
        }  # 今日总收益  # 本月总收益  # 本年总收益  # 历史总收益
        today_general_income = []
        year_general_income = []
        month_general_income = []
        all_general_income = []
        for station in master_stations:
            incomes_ins = models.StationIncome.objects.filter(master_station=station)

            # 日总收益
            day_incomes = incomes_ins.filter(income_date=income_date).all()
            # t_peak_income = 0
            # t_demand_income = 0

            if day_incomes.exists():
                for d_income in day_incomes:
                    t_peak_income = d_income.peak_load_shifting if hasattr(d_income, "peak_load_shifting") else 0
                    t_demand_income = d_income.demand_side_response if hasattr(d_income, "demand_side_response") else 0

                    t_income = t_peak_income + t_demand_income
                    today_general_income.append(t_income)
            # 月收益
            first_day = income_date.replace(day=1)

            # subquery = (
            #     incomes_ins.filter(income_date__range=(first_day, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )
            monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )

            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0

            total_month = monthly_income.get('total_peak', 0) + monthly_income.get('total_demand', 0)
            month_general_income.append(total_month)

            # 年收益
            year_start = datetime.datetime(datetime.datetime.now().year, 1, 1).date()

            # year_subquery = (
            #     incomes_ins.filter(income_date__range=(year_start, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )

            year_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not year_income['total_peak']:
                year_income['total_peak'] = 0
            if not year_income['total_demand']:
                year_income['total_demand'] = 0

            total_year = year_income['total_peak'] + year_income['total_demand']
            year_general_income.append(total_year)

            # 所有收益
            all_start = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()

            # all_subquery = (
            #     incomes_ins.filter(income_date__range=(all_start, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )

            all_income = incomes_ins.filter(income_date__range=(all_start, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not all_income['total_peak']:
                all_income['total_peak'] = 0
            if not all_income['total_demand']:
                all_income['total_demand'] = 0

            total_all = all_income['total_peak'] + all_income['total_demand']
            all_general_income.append(total_all)

        detail_dic["today_general_income"] = sum(today_general_income)
        detail_dic["year_general_income"] = sum(year_general_income)
        detail_dic["month_general_income"] = sum(month_general_income)
        detail_dic["all_general_income"] = sum(all_general_income)
        if detail_dic["today_general_income"] >= 10000:
            detail_dic["today_general_income"] = [str(round(detail_dic["today_general_income"] / 10000, 2)), "万"]
        else:
            detail_dic["today_general_income"] = [str(detail_dic["today_general_income"]), "元"]
        if detail_dic["month_general_income"] >= 10000:
            detail_dic["month_general_income"] = [str(round(detail_dic["month_general_income"] / 10000, 2)), "万"]
        else:
            detail_dic["month_general_income"] = [str(detail_dic["month_general_income"]), "元"]
        if detail_dic["year_general_income"] >= 10000:
            detail_dic["year_general_income"] = [str(round(detail_dic["year_general_income"] / 10000, 2)), "万"]
        else:
            detail_dic["year_general_income"] = [str(detail_dic["year_general_income"]), "元"]

        if detail_dic["all_general_income"] >= 10000:
            detail_dic["all_general_income"] = [str(round(detail_dic["all_general_income"] / 10000, 2)), "万"]
        else:
            detail_dic["all_general_income"] = [str(detail_dic["all_general_income"]), "元"]

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": detail_dic,
                },
            }
        )


class IncomeDayView(APIView):
    """日收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        income_date = datetime.date.today()
        tomorrow = income_date - datetime.timedelta(days=1)
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        master_stations_ins = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        detail_list = []
        for station in master_stations_ins:
            detail_dic = {}
            # 今日收益
            incomes_ins = models.StationIncome.objects.filter(master_station=station)
            day_incomes = incomes_ins.filter(income_date=income_date).all()

            t_income = 0
            if day_incomes.exists():
                for income in day_incomes:
                    t_peak_income = income.peak_load_shifting if hasattr(income, "peak_load_shifting") else 0
                    t_demand_income = income.demand_side_response if hasattr(income, "demand_side_response") else 0

                    t_income = t_peak_income + t_demand_income
            detail_dic["day_income"] = t_income
            detail_dic["station_id__station_name"] = station.name
            # 昨日收益
            y_incomes = incomes_ins.filter(income_date=tomorrow).all()
            y_income = 0
            if y_incomes:
                for income in y_incomes:
                    y_peak_income = income.peak_load_shifting
                    y_demand_income = income.demand_side_response
                    y_income = y_peak_income + y_demand_income
            detail_dic["increase"] = False
            if t_income > y_income:
                detail_dic["increase"] = True
            detail_list.append(detail_dic)
        response_obj = {
            "code": common_response_code.SUCCESS,
            "data": {"message": "success", "detail": detail_list},
        }

        return Response(response_obj)


class IncomeMonthView(APIView):
    """月收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        # 用户id
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        income_date = datetime.date.today()
        # 月收益
        first_day = income_date.replace(day=1)
        detail_list = []
        for station in master_stations:
            detail_dic = {}
            incomes_ins = models.StationIncome.objects.filter(master_station=station)
            detail_dic["station_id__station_name"] = station.name

            # subquery = (
            #     incomes_ins.filter(income_date__range=(first_day, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )
            monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0

            total_month = monthly_income['total_peak'] + monthly_income['total_demand']
            detail_dic["day_income__sum"] = total_month
            detail_list.append(detail_dic)
        response_obj = {"code": common_response_code.SUCCESS, "data": {"message": "success"}}
        response_obj['data']["detail"] = detail_list
        return Response(response_obj)


class IncomeYearView(APIView):
    """年收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        # 用户id
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        income_date = datetime.date.today()
        # 月收益
        first_day = datetime.datetime(datetime.datetime.now().year, 1, 1).date()
        detail_list = []
        for station in master_stations:
            detail_dic = {}
            incomes_ins = models.StationIncome.objects.filter(master_station=station)
            detail_dic["station_id__station_name"] = station.name

            # subquery = (
            #     incomes_ins.filter(income_date__range=(first_day, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )

            monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0
            total_month = monthly_income['total_peak'] + monthly_income['total_demand']
            detail_dic["day_income__sum"] = total_month
            detail_list.append(detail_dic)
        response_obj = {"code": common_response_code.SUCCESS, "data": {"message": "success"}}
        response_obj['data']["detail"] = detail_list
        return Response(response_obj)


class IncomeTotalView(APIView):
    """总收益"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user['user_id']
        user_ins = models.UserDetails.objects.filter(id=user_id).first()
        master_stations = models.MaterStation.objects.filter(userdetails=user_ins, is_delete=0)
        income_date = datetime.date.today()
        # 月收益
        first_day = datetime.datetime(datetime.datetime.now().year - 10, 1, 1).date()
        detail_list = []
        for station in master_stations:
            detail_dic = {}
            incomes_ins = models.StationIncome.objects.filter(master_station=station)
            detail_dic["station_id__station_name"] = station.name

            # subquery = (
            #     incomes_ins.filter(income_date__range=(first_day, income_date))
            #     .values('income_date')
            #     .annotate(last_entry=Max('id'))
            #     .values('last_entry')
            # )
            monthly_income = incomes_ins.filter(income_date__range=(first_day, income_date)).aggregate(
                total_peak=Sum('peak_load_shifting'), total_demand=Sum('demand_side_response')
            )
            if not monthly_income['total_peak']:
                monthly_income['total_peak'] = 0
            if not monthly_income['total_demand']:
                monthly_income['total_demand'] = 0
            total_month = monthly_income['total_peak'] + monthly_income['total_demand']
            detail_dic["day_income__sum"] = total_month
            detail_list.append(detail_dic)
        response_obj = {"code": common_response_code.SUCCESS, "data": {"message": "success"}}
        response_obj['data']["detail"] = detail_list
        return Response(response_obj)


class AlarmAddView(APIView):
    """告警上报入库"""

    def post(self, request):
        # print(request.data)
        ser = AlarmSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("告警上报入库:字段校验不通过")
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
        ser.save()
        station = ser.validated_data["station_name"]
        station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
        detail_list = ser.validated_data["data_info"]["body"]
        time_stamp = ser.validated_data["utime"]
        dt_object = datetime.datetime.fromtimestamp(int(time_stamp))
        formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        for point in detail_list:
            device = point.pop("device")
            keys = point.keys()
            for key in keys:
                if key in models.FaultAlarm.setting_dic.keys():
                    data_dic = {
                        "details": models.FaultAlarm.setting_dic[key]["detail"],
                        "type": models.FaultAlarm.setting_dic[key]["type"],
                        "start_time": formatted_time,
                        "device": device,
                        "note": models.FaultAlarm.setting_dic[key][1],
                    }
                    exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins,
                                                             device=device).exists()
                    if not exist:
                        if device != 'EMS':
                            units = station_ins.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
                            if units.exists():
                                unit = units.first()
                                unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                                device_another_name = device[:3] + unit_num
                            else:
                                device_another_name = device
                        else:
                            device_another_name = device

                        models.FaultAlarm.objects.using('alarm_module').create(
                            status=0,
                            station=station_ins,
                            point=key,
                            device_another_name=device_another_name,
                            **data_dic,
                        )
                        success_log.info('告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                                  formatted_time))
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功"}})


class AlarmAddViewV1(APIView):
    """告警上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            ser = AlarmSerializer(data=request.data)

            if not ser.is_valid():
                error_log.error("告警上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()

            station = ser.validated_data["station_name"]
            station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            dt_object = datetime.datetime.fromtimestamp(int(time_stamp))
            formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
            query_time = dt_object.strftime('%Y-%m-%d')
            start_time = query_time + ' 00:00:00'
            end_time = query_time + ' 23:59:59'
            now = datetime.datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            alarm_detail = alarm_detail_v3 if station_ins.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2
            for point in detail_list:
                device = point.pop("device")
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        data_dic = {
                            "details": alarm_detail[key]["detail"],
                            "type": alarm_detail[key]["type"],
                            "start_time": formatted_time,
                            "device": device,
                            "note": alarm_detail[key][1],
                        }

                        # 聚合告警：首先查询当天是否存在相同的聚合告警信息：有则关联；没有则查询当天以前是否有未恢复的告警：有则关联，没有则新创建
                        today_aggr_alarms = (models.AggrFaultAlarm.objects.using('alarm_module').filter(station=station_ins, device=device,
                                                                                  point=key, type=data_dic["type"],
                                                                                  start_time__gte=start_time, start_time__lte=end_time)
                                             .all())

                        # 当天不存在相同的聚合告警信息
                        if not today_aggr_alarms.exists():
                            before_exist_alarms = (models.AggrFaultAlarm.objects.using('alarm_module').filter(status=0, station=station_ins,
                                                                                        device=device, type=data_dic["type"],
                                                                                        point=key, start_time__lt=today_start)
                                                   .all())

                            # 当天以前存在未恢复的告警，则创建告警并关联至该聚合告警
                            if before_exist_alarms.exists():
                                before_alarm = before_exist_alarms.last()

                                # 更新故障时长
                                # before_alarm.duration = round((datetime.datetime.now() - before_alarm.start_time).total_seconds()
                                #                               / 3600, 2)
                                # before_alarm.save()

                                # 创建告警
                                exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins,
                                                                         device=device).exists()
                                if not exist:
                                    if device != 'EMS':
                                        units = station_ins.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
                                        if units.exists():
                                            unit = units.first()
                                            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                                            device_another_name = device[:3] + unit_num
                                        else:
                                            device_another_name = device
                                    else:
                                        device_another_name = device

                                    models.FaultAlarm.objects.using('alarm_module').create(
                                        status=0,
                                        station=station_ins,
                                        point=key,
                                        device_another_name=device_another_name,
                                        aggr_alarm=before_alarm,
                                        **data_dic,
                                    )
                                    success_log.info(
                                        '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                             formatted_time))

                            # 当天以前也不存在未恢复的告警，则先创建聚合告警，再创建聚合告警，并关联至该聚合告警
                            else:
                                exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins,
                                                                         type=data_dic["type"], device=device).exists()
                                if not exist:
                                    if device != 'EMS':
                                        units = station_ins.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
                                        if units.exists():
                                            unit = units.first()
                                            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                                            device_another_name = device[:3] + unit_num
                                        else:
                                            device_another_name = device
                                    else:
                                        device_another_name = device

                                    # 创建聚合告警
                                    new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').create(
                                        status=0,
                                        station=station_ins,
                                        point=key,
                                        duration=0,
                                        device_another_name=device_another_name,
                                        **data_dic)

                                    # 创建告警
                                    models.FaultAlarm.objects.using('alarm_module').create(
                                        status=0,
                                        station=station_ins,
                                        point=key,
                                        device_another_name=device_another_name,
                                        aggr_alarm=new_aggr_alarm,
                                        **data_dic,
                                    )
                                    success_log.info(
                                        '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                             formatted_time))

                        # 当天存在相同的聚合告警信息，则创建告警并关联至该聚合告警
                        else:
                            today_aggr_alarm = today_aggr_alarms.last()

                            # 更新聚合告警故障时长及状态等
                            # today_aggr_alarm.duration = round((datetime.datetime.now() - today_aggr_alarm.start_time).total_seconds()
                            #                                   / 3600, 2)
                            today_aggr_alarm.note = alarm_detail[key][1]
                            today_aggr_alarm.end_time = None
                            today_aggr_alarm.status = 0
                            today_aggr_alarm.save()

                            # 创建告警
                            exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins,
                                                                     device=device).exists()
                            if not exist:
                                if device != 'EMS':
                                    units = station_ins.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
                                    if units.exists():
                                        unit = units.first()
                                        unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                                        device_another_name = device[:3] + unit_num
                                    else:
                                        device_another_name = device
                                else:
                                    device_another_name = device

                                models.FaultAlarm.objects.using('alarm_module').create(
                                    status=0,
                                    station=station_ins,
                                    point=key,
                                    device_another_name=device_another_name,
                                    aggr_alarm=today_aggr_alarm,
                                    **data_dic,
                                )
                                success_log.info('告警上报内容：station--{},body--{},utime--{}'.format(station,
                                                                                                      detail_list,
                                                                                                      formatted_time))
        except Exception as e:
            error_log.error(f'告警上报异常：{e}')
            return  Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据上报失败"}})
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功"}})


class AlarmAddViewV4(APIView):
    """告警上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            conn = get_redis_connection("default")
            ser = AlarmSerializer(data=request.data)

            if not ser.is_valid():
                error_log.error("告警上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()

            station = ser.validated_data["station_name"]
            station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            dt_object = datetime.datetime.fromtimestamp(int(time_stamp))
            formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')

            alarm_detail = alarm_detail_v3 if station_ins.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2

            for point in detail_list:
                device = point.pop("device")
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        data_dic = {
                            "details": alarm_detail[key]["detail"],
                            "type": alarm_detail[key]["type"],
                            "start_time": formatted_time,
                            "device": device,
                            "note": alarm_detail[key][1],
                        }

                        # 写入 redis
                        key_str = f"{station_ins.english_name}_{device}_{key}"
                        key_str_aggr = f"{station_ins.english_name}_{device}_{key}_aggr"

                        # redis 中不存在该 key，则分别写入 doris 和 redis
                        if not conn.exists(key_str):
                            # 先写入 doris 中
                            if device != 'EMS':
                                unit = station_ins.unit_set.filter(is_delete=0).filter(
                                    Q(bms=device) | Q(pcs=device)).first()
                                if unit:
                                    unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                                    device_another_name = device[:3] + unit_num
                                else:
                                    device_another_name = device
                            else:
                                device_another_name = device

                            # 再判断 redis 中是否已存在该聚合告警 key，有则说明已存在聚合告警，没有则写入
                            if not conn.exists(key_str_aggr):
                                # 创建聚合告警
                                models.AggrFaultAlarm.objects.using('alarm_module').create(
                                    status=0,
                                    station_id=station_ins.id,
                                    point=key,
                                    duration=0,
                                    device_another_name=device_another_name,
                                    **data_dic)

                                new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').filter(station_id=station_ins.id,
                                                                                                            device=device,
                                                                                                            point=key,
                                                                                                            start_time=formatted_time,
                                                                                                            status=0).first()

                                if new_aggr_alarm:
                                    conn.set(key_str_aggr, new_aggr_alarm.id)

                                models.FaultAlarm.objects.using('alarm_module').create(
                                    status=0,
                                    station_id=station_ins.id,
                                    point=key,
                                    device_another_name=device_another_name,
                                    aggr_alarm_id=new_aggr_alarm.id,
                                    **data_dic,
                                )

                                success_log.info(
                                    '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                         formatted_time))

                                new_alarm = models.FaultAlarm.objects.using('alarm_module').filter(
                                    station_id=station_ins.id,
                                    device=device,
                                    point=key,
                                    start_time=formatted_time,
                                    aggr_alarm_id=new_aggr_alarm.id,
                                    status=0).first()

                                conn.set(key_str, new_alarm.id)

                            # 已存在聚合告警，则关联到聚合告警: 先将聚合告警改为未恢复状态，并将redis中的聚合告警的key的过期时间设置为-1
                            else:
                                # 先将聚合告警改为"未恢复"状态
                                new_aggr_alarm_id = conn.get(key_str_aggr)

                                # 创建告警记录，并关联于该聚合告警
                                models.FaultAlarm.objects.using('alarm_module').create(
                                    status=0,
                                    station_id=station_ins.id,
                                    point=key,
                                    device_another_name=device_another_name,
                                    aggr_alarm_id=new_aggr_alarm_id,
                                    **data_dic,
                                )
                                success_log.info(
                                    '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                         formatted_time))

                                new_alarm = models.FaultAlarm.objects.using('alarm_module').filter(
                                    station_id=station_ins.id,
                                    device=device,
                                    point=key,
                                    start_time=formatted_time,
                                    aggr_alarm_id=new_aggr_alarm_id,
                                    status=0).first()

                                conn.set(key_str, new_alarm.id)

                                # 修改聚合告警状态
                                aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').filter(id=new_aggr_alarm_id).first()
                                aggr_alarm.status = 0
                                aggr_alarm.end_time = None
                                aggr_alarm.note = alarm_detail[key][1]
                                aggr_alarm.save()

                                # 去除过期时间： 用expire 命令 设置为-1 未生效==> 直接重新设置key
                                conn.set(key_str_aggr, new_aggr_alarm_id)

        except Exception as e:
            error_log.error(f'告警上报异常：{e}')
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据上报失败"}})
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功"}})


class AlarmAddViewV2(APIView):
    """告警上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            ser = AlarmSerializer(data=request.data)

            if not ser.is_valid():
                error_log.error("告警上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()
            conn = get_redis_connection("1")
            if conn.get('station_unit_data'):
                station_data = json.loads(conn.get('station_unit_data'))
            else:
                station_res = models.StationDetails.objects.filter(is_delete=0).all()
                station_data = {}
                for s_info in station_res:
                    unit_res = s_info.unit_set.filter(is_delete=0).all()
                    if unit_res:
                        station_data[s_info.english_name] = {'id': s_info.id, 'units': {'EMS': {'v_number': s_info.unit_set.filter(is_delete=0).order_by('-v_number').first().v_number, 'EMS': 'EMS'}}}
                        for unit in unit_res:
                            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                            station_data[s_info.english_name]['units'][unit.bms] = {'v_number': unit.v_number, unit.bms: unit.bms[:3] + unit_num}
                            station_data[s_info.english_name]['units'][unit.pcs] = {'v_number': unit.v_number, unit.pcs: unit.pcs[:3] + unit_num}
                conn.set('station_unit_data', json.dumps(station_data), 60 * 60 * 12)
            station = ser.validated_data["station_name"]
            # station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            station_ins = station_data.get(station)
            if not station_ins:
                return Response({"code": common_response_code.NO_DATA, "data": {"message": "error", "detail": "并网带数据匹配失败，请更新redis后重试"}})
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            dt_object = datetime.datetime.fromtimestamp(int(time_stamp))
            formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')

            now = datetime.datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            seven_days_ago = today_start - datetime.timedelta(days=7)

            for point in detail_list:
                device = point.pop("device")
                alarm_detail = alarm_detail_v3 if station_ins['units'][device]['v_number'] == 3 else alarm_detail_v2
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        redis_key = f'{station}_{device}_{key}'
                        aggr_redis_key = f'{station}_{device}_{key}_aggr'
                        if not conn.get(redis_key):
                            data_dic = {
                                "details": alarm_detail[key]["detail"],
                                "type": alarm_detail[key]["type"],
                                "start_time": formatted_time,
                                "device": device,
                                "note": alarm_detail[key][1],
                            }

                            if device != 'EMS':
                                # units = station_ins.unit_set.filter(is_delete=0).filter(Q(bms=device) | Q(pcs=device)).all()
                                unit = station_ins['units'][device][device]
                                if unit:
                                    device_another_name = unit
                                else:
                                    device_another_name = device
                            else:
                                device_another_name = device
                            joint_primary_key = f"{station_ins['id']}_{device}_{alarm_detail[key]['type']}_{key}_{formatted_time}"
                            if not conn.exists(aggr_redis_key):
                                # 创建聚合告警
                                models.AggrFaultAlarm.objects.using('alarm_module_write').create(
                                    joint_primary_key=joint_primary_key,
                                    status=0,
                                    station_id=station_ins['id'],
                                    point=key,
                                    duration=0,
                                    device_another_name=device_another_name,
                                    **data_dic)

                                # 创建告警
                                # new_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(
                                #     station_id=station_ins['id'], device_another_name=device_another_name, point=key,
                                #     duration=0, status=0, **data_dic).first()
                                conn.set(aggr_redis_key, joint_primary_key)
                                models.FaultAlarm.objects.using('alarm_module_write').create(
                                    status=0,
                                    station_id=station_ins['id'],
                                    point=key,
                                    device_another_name=device_another_name,
                                    aggr_alarm_id=joint_primary_key,
                                    joint_primary_key=joint_primary_key,
                                    **data_dic,
                                )
                                # fault_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(point=key,
                                #                                                                      status=0,
                                #                                                                      station_id=
                                #                                                                      station_ins['id'],
                                #                                                                      device_another_name=device_another_name,
                                #                                                                      aggr_alarm_id=new_aggr_alarm.id,
                                #                                                                      **data_dic, ).first()
                                conn.set(redis_key, joint_primary_key)
                                success_log.info(
                                    '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                   formatted_time))

                            else:
                                # 先将聚合告警改为"未恢复"状态
                                new_aggr_alarm_id = conn.get(aggr_redis_key)
                                if isinstance(new_aggr_alarm_id, bytes):
                                    new_aggr_alarm_id = new_aggr_alarm_id.decode()
                                # 创建告警记录，并关联于该聚合告警
                                models.FaultAlarm.objects.using('alarm_module_write').create(
                                    joint_primary_key=new_aggr_alarm_id,
                                    status=0,
                                    station_id=station_ins['id'],
                                    point=key,
                                    device_another_name=device_another_name,
                                    aggr_alarm_id=new_aggr_alarm_id,
                                    **data_dic,
                                )
                                success_log.info(
                                    '告警上报内容：station--{},body--{},utime--{}'.format(station, detail_list,
                                                                                   formatted_time))

                                # new_alarm = models.FaultAlarm.objects.using('alarm_module_write').filter(
                                #     station_id=station_ins['id'],
                                #     device=device,
                                #     point=key,
                                #     start_time=formatted_time,
                                #     aggr_alarm_id=new_aggr_alarm_id,
                                #     status=0).first()

                                conn.set(redis_key, joint_primary_key)

                                # 修改聚合告警状态
                                aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(
                                    joint_primary_key=new_aggr_alarm_id).first()
                                aggr_alarm.status = 0
                                aggr_alarm.end_time = None
                                aggr_alarm.duration = 0
                                aggr_alarm.note = alarm_detail[key][1]
                                aggr_alarm.save()

                                conn.set(aggr_redis_key, new_aggr_alarm_id)

        except Exception as e:
            error_log.error(f'告警上报异常：{e}')
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据上报失败"}})
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功"}})


class UnAlarmAddView(APIView):
    """告警解除上报入库"""

    def post(self, request):
        # print(request.data)
        ser = UnAlarmSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("告警解除上报入库:字段校验不通过")
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
        ser.save()
        station = ser.validated_data["station_name"]
        station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
        detail_list = ser.validated_data["data_info"]["body"]
        time_stamp = ser.validated_data["utime"]
        formatted_time = datetime.datetime.fromtimestamp(int(time_stamp)).strftime('%Y-%m-%d %H:%M:%S')
        success_log.info('解除告警上报内容：station--{},body--{},utime--{}'.format(station,detail_list,formatted_time))
        for point in detail_list:
            device = point.pop("device")
            keys = point.keys()
            for key in keys:
                if key in models.FaultAlarm.setting_dic.keys():
                    # data_dic = {"details": models.FaultAlarm.setting_dic[key]["detail"],
                    #             "type": models.FaultAlarm.setting_dic[key]["type"], "start_time": formatted_time, }
                    exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins, device=device).exists()
                    if exist:
                        ins = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins, device=device).last()
                        ins.status = 1
                        ins.note = models.FaultAlarm.setting_dic[key][0]
                        ins.end_time = formatted_time
                        ins.save()
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功"}})


class UnAlarmAddViewV1(APIView):
    """告警解除上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            ser = UnAlarmSerializer(data=request.data)
            if not ser.is_valid():
                error_log.error("告警解除上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()
            station = ser.validated_data["station_name"]
            station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            formatted_time = datetime.datetime.fromtimestamp(int(time_stamp)).strftime('%Y-%m-%d %H:%M:%S')
            success_log.info('解除告警上报内容：station--{},body--{},utime--{}'.format(station,detail_list,formatted_time))

            now = datetime.datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            alarm_detail = alarm_detail_v3 if station_ins.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2
            for point in detail_list:
                device = point.pop("device")
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        # data_dic = {"details": alarm_detail[key]["detail"],
                        #             "type": alarm_detail[key]["type"], "start_time": formatted_time, }
                        exist = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins, device=device).exists()
                        if exist:
                            ins = models.FaultAlarm.objects.using('alarm_module').filter(status=0, point=key, station=station_ins, device=device).last()
                            ins.status = 1
                            ins.note = alarm_detail[key][0]
                            ins.end_time = formatted_time
                            ins.save()

                        exist_aggr_alarms = models.AggrFaultAlarm.objects.using('alarm_module').filter(status=0, station=station_ins,
                                                                                  device=device, point=key).all()
                        if exist_aggr_alarms.exists():
                            last_aggr_alarm = exist_aggr_alarms.last()
                            last_aggr_alarm.status = 1
                            last_aggr_alarm.end_time = formatted_time
                            last_aggr_alarm.note = alarm_detail[key][0]
                            last_aggr_alarm.duration = round((datetime.datetime.now() - last_aggr_alarm.start_time).total_seconds() / 3600, 2)
                            last_aggr_alarm.save()
        except Exception as e:
            error_log.error(f'告警解除上报异常：{e}')
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据解除上报失败"}})

        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功2"}})


class UnAlarmAddViewV4(APIView):
    """告警解除上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            conn = get_redis_connection("default")
            ser = UnAlarmSerializer(data=request.data)
            if not ser.is_valid():
                error_log.error("告警解除上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()
            station = ser.validated_data["station_name"]
            station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            formatted_time = datetime.datetime.fromtimestamp(int(time_stamp)).strftime('%Y-%m-%d %H:%M:%S')
            success_log.info('解除告警上报内容：station--{},body--{},utime--{}'.format(station,detail_list,formatted_time))

            now = datetime.datetime.now()
            today_end = now.replace(hour=23, minute=59, second=59, microsecond=0)
            expire_time = (today_end - now).total_seconds()

            alarm_detail = alarm_detail_v3 if station_ins.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2
            for point in detail_list:
                device = point.pop("device")
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        # 写入 redis
                        key_str = f"{station_ins.english_name}_{device}_{key}"
                        key_str_aggr = f"{station_ins.english_name}_{device}_{key}_aggr"

                        # redis 中存在该 key，则恢复并删除 redis key
                        if conn.exists(key_str):
                            alarm_id = conn.get(key_str)
                            alarm_ins = models.FaultAlarm.objects.using('alarm_module').filter(id=alarm_id).first()
                            if alarm_ins:
                                alarm_ins.status = 1
                                alarm_ins.note = alarm_detail[key][0]
                                alarm_ins.end_time = formatted_time
                                alarm_ins.save()

                                conn.delete(key_str)

                            # 查询 聚合告警：一般都是存在的，直接更新；redis中的 key 则增加一个过期时间，过了当晚 过期删除
                            aggr_alarm_id = alarm_ins.aggr_alarm_id
                            exist_aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').filter(id=aggr_alarm_id).first()

                            if exist_aggr_alarm:
                                # 计算 告警时长
                                exist_aggr_alarm.duration = round((datetime.datetime.now() - exist_aggr_alarm.start_time).total_seconds() / 3600, 2)
                                related_alarm_list = models.FaultAlarm.objects.using('alarm_module').filter(aggr_alarm_id=exist_aggr_alarm.id).all()
                                duration = 0
                                for alarm in related_alarm_list:
                                    if alarm.status == 1:
                                        duration += (alarm.end_time - alarm.start_time).total_seconds()
                                    else:
                                        duration += (datetime.datetime.now() - alarm.start_time).total_seconds()

                                exist_aggr_alarm.status = 1
                                exist_aggr_alarm.end_time = formatted_time
                                exist_aggr_alarm.note = alarm_detail[key][0]
                                exist_aggr_alarm.duration = round(duration / 3600, 2)
                                exist_aggr_alarm.save()

                                # 给聚合告警的 key 设置过期时间
                                if conn.exists(key_str_aggr):
                                    if exist_aggr_alarm.start_time.date() == now.date():
                                        conn.expire(key_str_aggr, int(expire_time))
                                    else:
                                        conn.delete(key_str_aggr)

        except Exception as e:
            print(traceback.print_exc())
            error_log.error(f'告警解除上报异常：{e}')
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据解除上报失败"}})

        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功2"}})


class UnAlarmAddViewV2(APIView):
    """告警解除上报入库"""""

    def post(self, request):
        # print(request.data)
        try:
            ser = UnAlarmSerializer(data=request.data)
            if not ser.is_valid():
                error_log.error("告警解除上报入库:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
            ser.save()
            conn = get_redis_connection("1")
            if conn.get('station_unit_data'):
                station_data = json.loads(conn.get('station_unit_data'))
            else:
                station_res = models.StationDetails.objects.filter(is_delete=0).all()
                station_data = {}
                for s_info in station_res:
                    unit_res = s_info.unit_set.filter(is_delete=0).all()
                    if unit_res:
                        station_data[s_info.english_name] = {'id': s_info.id, 'units': {'EMS': {'v_number': s_info.unit_set.filter(is_delete=0).order_by('-v_number').first().v_number, 'EMS': 'EMS'}}}
                        for unit in unit_res:
                            unit_num = unit.unit_new_name[-1] if unit.unit_new_name else '1'
                            station_data[s_info.english_name]['units'][unit.bms] = {'v_number': unit.v_number,
                                                                                    unit.bms: unit.bms[:3] + unit_num}
                            station_data[s_info.english_name]['units'][unit.pcs] = {'v_number': unit.v_number,
                                                                                    unit.pcs: unit.pcs[:3] + unit_num}
                conn.set('station_unit_data', json.dumps(station_data), 60 * 60 * 12)
            station = ser.validated_data["station_name"]
            # station_ins = models.StationDetails.objects.filter(is_delete=0, english_name=station).first()
            station_ins = station_data.get(station)
            if not station_ins:
                return Response({"code": common_response_code.NO_DATA, "data": {"message": "error", "detail": "并网带数据匹配失败，请更新redis后重试"}})
            detail_list = ser.validated_data["data_info"]["body"]
            time_stamp = ser.validated_data["utime"]
            formatted_time = datetime.datetime.fromtimestamp(int(time_stamp)).strftime('%Y-%m-%d %H:%M:%S')
            success_log.info('解除告警上报内容：station--{},body--{},utime--{}'.format(station,detail_list,formatted_time))

            now = datetime.datetime.now()
            today_end = now.replace(hour=23, minute=59, second=59, microsecond=0)
            # alarm_detail = alarm_detail_v3 if station_ins.unit_set.filter(is_delete=0).first().v_number == 3 else alarm_detail_v2

            # key过期时间
            times = (datetime.datetime.now() + datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0)

            for point in detail_list:
                device = point.pop("device")
                alarm_detail = alarm_detail_v3 if station_ins['units'][device]['v_number'] == 3 else alarm_detail_v2
                keys = point.keys()
                for key in keys:
                    if key in alarm_detail.keys():
                        # data_dic = {"details": alarm_detail[key]["detail"],
                        #             "type": alarm_detail[key]["type"], "start_time": formatted_time, }
                        exist = models.FaultAlarm.objects.using('alarm_module_write').filter(status=0, point=key, station_id=station_ins['id'], device=device).exists()
                        if exist:
                            ins = models.FaultAlarm.objects.using('alarm_module_write').filter(status=0, point=key, station_id=station_ins['id'], device=device).last()
                            ins.status = 1
                            ins.note = alarm_detail[key][0]
                            ins.end_time = formatted_time
                            ins.save()

                        exist_aggr_alarms = models.AggrFaultAlarm.objects.using('alarm_module_write').filter(status=0, station_id=station_ins['id'],
                                                                                  device=device, point=key).all()
                        if exist_aggr_alarms.exists():
                            last_aggr_alarm = exist_aggr_alarms.last()
                            last_aggr_alarm.status = 1
                            last_aggr_alarm.end_time = formatted_time
                            last_aggr_alarm.note = alarm_detail[key][0]
                            last_aggr_alarm.duration = round((datetime.datetime.now() - last_aggr_alarm.start_time).total_seconds() / 3600, 2)
                            last_aggr_alarm.save()
                            ex_time = (today_end - now).total_seconds()
                            aggr_redis_key = f'{station}_{device}_{key}_aggr'
                            if conn.exists(aggr_redis_key):
                                if last_aggr_alarm.start_time.date() == datetime.datetime.now().date():
                                    conn.expire(aggr_redis_key, int(ex_time))
                                else:
                                    conn.delete(aggr_redis_key)
                        redis_key = f'{station}_{device}_{key}'
                        if conn.get(redis_key):
                            conn.delete(redis_key)


        except Exception as e:
            error_log.error(f'告警解除上报异常：{e}')
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "success", "detail": "数据解除上报失败"}})

        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "数据上报成功2"}})


# class AlarmDetailView(APIView):
#     """小程序端告警详情"""""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request):
#         params = request.query_params
#         if not params:
#             today = datetime.date.today()
#             query_ins = (
#                 models.FaultAlarm.objects.using('alarm_module').filter(start_time__date=today)
#                 .values("status", "type", "start_time", "end_time", "details", "id", "note", "device")
#                 .order_by("-start_time")
#             )
#         else:
#             new_dic = {}
#             pcs = params.get("device", None)
#             if pcs:
#                 unit_ins = models.Unit.objects.filter(is_delete=0, english_name=pcs).first()
#                 pcs_ = unit_ins.pcs
#                 bms_ = unit_ins.bms
#                 ins = models.FaultAlarm.objects.using('alarm_module').filter(Q(device=pcs_) | Q(device=bms_))
#             else:
#                 ins = models.FaultAlarm.objects
#             for k in params.keys():
#                 if k == "device":
#                     pass
#                 elif k == "station__id":
#                     master_station_id = params.get("station__id")
#                     master_stations = models.MaterStation.objects.filter(id=master_station_id, is_delete=0)
#                     if master_stations.exists():
#                         master_station = master_stations.first()
#                         slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
#                         ids = [s.id for s in slave_stations]
#                         new_dic['station_id__in'] = ids
#                 else:
#                     new_dic[k] = params[k]
#             query_ins = (
#                 ins.filter(**new_dic)
#                 .values("status", "type", "start_time", "end_time", "details", "id", "note", "device",
#                         "device_another_name")
#                 .order_by("-start_time")
#             )
#         warn = []
#         fault = []
#         event = []
#         for query in query_ins[:1000]:
#             query["details"] = query["device_another_name"] + ":" + query["details"] if query["device_another_name"]\
#                 else query["device"] + ":" + query["details"]
#             if query['type'] == 1:
#                 fault.append(query)
#             elif query['type'] == 3:
#                 event.append(query)
#             else:
#                 warn.append(query)
#         return Response({"code": common_response_code.SUCCESS, "data": {"message": "success",
#                                                                         # "detail": query_ins,
#                                                                         "warn": warn,
#                                                                         "fault": fault,
#                                                                         "event": event,
#                                                                         }})


# class WebAlarmDetailListView(APIView):
#     """Web端告警详情列表"""""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def get(self, request, id):
#         ser = monitor_serializers.WebAlarmMonitorSerializer(data=request.query_params)
#         try:
#             if not ser.is_valid():
#                 error_log.error(f"告警列表:字段校验不通过 =>{ser.errors}")
#                 return Response(
#                     {
#                         "code": common_response_code.FIELD_ERROR,
#                         "data": {"message": "error", "detail": ser.errors},
#                     }
#                 )
#         except Exception as e:
#             error_log.error(f"告警列表:字段校验不通过 =>{e}")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": e.args[0]},
#                 }
#             )
#
#         today = datetime.date.today()
#         default_end_day = today
#         default_start_day = today - datetime.timedelta(days=365)
#
#         start_time = ser.validated_data.get('start_time', default_start_day)
#         end_time = ser.validated_data.get('end_time', default_end_day)
#         status = ser.validated_data.get('status')
#         type_ = ser.validated_data.get('type')
#         # station_id = ser.validated_data.get('station_id')
#         keyword = ser.validated_data.get('keyword')
#         page = int(request.query_params.get('page', 1))
#         page_size = int(request.query_params.get('page_size', 1000000000))
#
#         start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
#         end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'
#
#         try:
#             project_instance = models.Project.objects.get(id=id)
#         except Exception as e:
#             error_log.error("告警监控：项目不存在：{}".format(e))
#             return Response(
#                 {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "项目不存在"}})
#
#         filters = {
#             "start_time__gte": datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S"),
#             "start_time__lte": datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
#         }
#         if status:
#             filters['status'] = int(status)
#         if type_:
#             if int(type_) == 4:
#                 filters['type__in'] = [4, 5]
#             else:
#                 filters['type'] = type_
#         if keyword:
#             filters['details__contains'] = keyword
#
#         # stations = project_instance.stationdetails_set.filter(is_delete=0).all()
#
#         master_stations = project_instance.materstation_set.filter(is_delete=0).all()
#         # master_station_name = master_station.name
#         # slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
#
#         slave_stations = models.StationDetails.objects.filter(master_station__project=project_instance).all()
#
#         # total_fault_alarm_instances = []
#         filters['station__in'] = slave_stations
#         total_fault_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
#                                                                                           "type", "start_time",
#                                                                                           "end_time", "details", "id",
#                                                                                           "note", "device",
#                                                                                           "station"
#                                                                                           ).order_by("-start_time")
#
#         # if station_id and int(station_id) != 0:
#         #     filters['station_id'] = station_id
#         #     total_fault_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
#         #                                                                                       "type", "start_time",
#         #                                                                                       "end_time", "details",
#         #                                                                                       "id",
#         #                                                                                       "note", "device",
#         #                                                                                       "station"
#         #                                                                                       ).order_by("-start_time")
#         # else:
#         #     for ind, station in enumerate(slave_stations):
#         #         filters['station_id'] = station.id
#         #         target_fault_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
#         #                                                                                           "type", "start_time",
#         #                                                                                           "end_time", "details", "id",
#         #                                                                                           "note", "device",
#         #                                                                                           "station"
#         #                                                                                           ).order_by("-start_time")
#
#                 # total_fault_alarm_instances += target_fault_alarm_instances
#         for ins in total_fault_alarm_instances:
#             ins["details"] = ins["device"] + ":" + ins["details"]
#             ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
#             if ins["end_time"]:
#                 ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
#             try:
#                 ins['station_name'] = models.StationDetails.objects.get(id=ins['station']).station_name
#             except Exception as e:
#                 pass
#
#         # 分页
#         paginator = Paginator(list(total_fault_alarm_instances), page_size)
#         tem_fault_alarm_instances = paginator.get_page(page).object_list
#         # todo 直接分页查询
#
#         paginator_info = {
#             "page": page,
#             "page_size": page_size,
#             "pages": paginator.num_pages,
#             "total_count": paginator.count
#         }
#
#         stations_options = [
#             {
#                 "key": 0,
#                 "value": "全部"
#             }
#         ]
#         for station in master_stations:
#             stations_options.append(
#                 {
#                     "key": station.id,
#                     "value": station.name
#                 }
#             )
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": tem_fault_alarm_instances,
#                     "paginator_info": paginator_info,
#                     "stations_options": stations_options
#                 },
#             }
#         )


class WebAlarmDetailListViewV2(APIView):
    """Web端告警详情列表: 聚合告警"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def deal_item_data(self, ins, lang='zh'):
        # ins["details"] = ins["device"] + ":" + ALARM_ZH_EN_MAP[ins["details"].strip()][lang]
        # ins["note"] = ALARM_ZH_EN_MAP[ins["note"].strip().replace(' ', '')][lang]
        ins["details"] = ins["device"] + ":" + ALARM_ZH_EN_MAP[ins["details"].strip()][lang] if ALARM_ZH_EN_MAP.get(ins["details"].strip()) else ''
        ins["note"] = ALARM_ZH_EN_MAP[ins["note"].strip().replace(' ', '')][lang] if ALARM_ZH_EN_MAP.get(ins["note"].strip().replace(' ', '')) else ''
        ins["start_time"] = ins["start_time"].strftime('%Y-%m-%d %H:%M:%S')
        if ins["end_time"]:
            ins["end_time"] = ins["end_time"].strftime('%Y-%m-%d %H:%M:%S')
        try:
            ins['station_name'] = models.StationDetails.objects.get(id=ins['station_id']).station_name
        except Exception as e:
            pass

        # 计算告警时长
        # aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').get(id=ins['id'])
        related_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(aggr_alarm_id=ins['joint_primary_key']).order_by('-start_time')
        duration = 0
        for alarm_instance in related_alarm_instances:
            if alarm_instance.end_time:
                duration += (alarm_instance.end_time - alarm_instance.start_time).total_seconds()
            else:
                duration += (datetime.datetime.now() - alarm_instance.start_time).total_seconds()
        ins['duration'] = round(duration / 3600, 2)

        # 反馈内容
        feedback = models.AggrFaultAlarmFeedback.objects.filter(id=ins['feedback_id']).first()
        if lang == 'zh':
            ins['feedback'] = feedback.content if feedback else ''
        else:
            ins['feedback'] = feedback.en_content if feedback else ''
        ins['id'] = ins['joint_primary_key']
        return ins

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebAlarmMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"告警列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"告警列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=90)

        start_time = ser.validated_data.get('start_time', default_start_day)
        end_time = ser.validated_data.get('end_time', default_end_day)
        status = ser.validated_data.get('status')
        type_ = ser.validated_data.get('type')
        # station_id = ser.validated_data.get('station_id')
        keyword = ser.validated_data.get('keyword')
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 1000000000))

        start = (int(page) - 1) * int(page_size)
        end = start + int(page_size) + 1

        try:
            project_instance = models.Project.objects.get(id=id)
        except Exception as e:
            error_log.error("告警监控：项目不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "station不存在"}})
        filters = {}
        if start_time and end_time:
            start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
            end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'
            filters = {
                "start_time__gte": datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S"),
                "start_time__lte": datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
            }

        if status:
            filters['status'] = int(status)
        if type_:
            if int(type_) == 4:
                filters['type__in'] = [4, 5]
            else:
                filters['type'] = type_
        if keyword:
            if lang == 'zh':
                filters['details__contains'] = keyword
            else:
                for k,v in ALARM_ZH_EN_MAP.items():
                    if keyword in v[lang]:
                        keyword = k
                        break
                filters['details__contains'] = keyword

        # stations = project_instance.stationdetails_set.filter(is_delete=0).all()

        master_stations = project_instance.materstation_set.filter(is_delete=0).all()
        # master_station_name = master_station.name
        # slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()

        slave_stations = models.StationDetails.objects.filter(master_station__project=project_instance, is_delete=0).all()

        # total_fault_alarm_instances = []
        filters['station_id__in'] = [s.id for s in slave_stations]
        total_fault_alarm_instances = models.AggrFaultAlarm.objects.using('alarm_module').filter(**filters).values("status",
                                                                                          "type", "start_time",
                                                                                          "end_time", "details", "joint_primary_key",
                                                                                          "note", "device",
                                                                                          "station_id", "feedback_id"
                                                                                          ).order_by("-start_time")

        total_count = total_fault_alarm_instances.count()
        if end > total_count:
            end = total_count + 1

        total_pages = math.ceil(total_count / page_size)

        tem_fault_alarm_instances = total_fault_alarm_instances[start:end]

        temp_list = []
        if len(total_fault_alarm_instances):

            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = list()
                for item in tem_fault_alarm_instances:
                    future = executor.submit(self.deal_item_data, item, lang)
                    futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    i = future.result()
                    temp_list.append(i)

                connections.close_all()

        # 分页
        # paginator = Paginator(list(temp_list), page_size)
        # tem_fault_alarm_instances = paginator.get_page(page).object_list
        # todo 直接分页查询

        paginator_info = {
            "page": page,
            "page_size": page_size,
            "pages": total_pages,
            "total_count": total_count
        }

        stations_options = [
            {
                "key": 0,
                "value": "全部" if lang == 'zh' else "All"
            }
        ]
        for station in master_stations:
            stations_options.append(
                {
                    "key": station.id,
                    "value": station.name if lang == 'zh' else station.en_name
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": temp_list,
                    "paginator_info": paginator_info,
                    "stations_options": stations_options
                },
            }
        )


class AlarmDownloadView(APIView):
    """
    告警：下载
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebAlarmMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"告警列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error("告警监控：字段校验不通过：{}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=90)

        start_time = ser.validated_data.get('start_time', default_start_day)
        end_time = ser.validated_data.get('end_time', default_end_day)
        status = ser.validated_data.get('status')
        type_ = ser.validated_data.get('type')
        # station_id = ser.validated_data.get('station_id')
        keyword = ser.validated_data.get('keyword')
        # page = request.query_params.get('page', 1)
        # page_size = request.query_params.get('page_size', 1000000000)

        start_time_ = start_time.strftime('%Y-%m-%d') + ' 00:00:00'
        end_time_ = end_time.strftime('%Y-%m-%d') + ' 23:59:59'

        try:
            project_instance = models.Project.objects.get(id=id)
        except Exception as e:
            error_log.error("告警监控：项目不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "station不存在" if
                lang == 'zh' else "The project does not exist."}})

        total_fault_alarms = []
        slave_stations = models.StationDetails.objects.filter(project=project_instance, is_delete=0).all()
        stations_ids = [s.id for s in slave_stations]

        filters = {
            "station_id__in": stations_ids,
            "start_time__gte": datetime.datetime.strptime(start_time_, "%Y-%m-%d %H:%M:%S"),
            "start_time__lte": datetime.datetime.strptime(end_time_, "%Y-%m-%d %H:%M:%S")
        }
        if status:
            filters['status'] = int(status)
        if type_:
            if int(type_) == 4:
                filters['type__in'] = [4, 5]
            else:
                filters['type'] = type_
        if keyword:
            if lang == 'zh':
                filters['details__contains'] = keyword
            else:
                for k, v in ALARM_ZH_EN_MAP.items():
                    if keyword in v[lang]:
                        keyword = k
                        break
                filters['details__contains'] = keyword

        total_fault_alarm_instances = models.AggrFaultAlarm.objects.using('alarm_module').filter(**filters).order_by("-start_time").all()

        if lang == 'zh':
            type_mapping = {1: "故障", 0: "警告", 2: "报警", 3: "事件", 4: "离线", 5: "通讯异常"}

            for ins in total_fault_alarm_instances:
                related_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(aggr_alarm_id=ins.joint_primary_key).order_by('-start_time')
                for alarm_instance in related_alarm_instances:
                    station = models.StationDetails.objects.filter(id=alarm_instance.station_id).first()

                    total_fault_alarms.append(
                        [
                            alarm_instance.joint_primary_key,
                            station.station_name if station else '--',
                            alarm_instance.device + ": " + ALARM_ZH_EN_MAP[alarm_instance.details.strip()][lang],
                            type_mapping[alarm_instance.type],
                            alarm_instance.note,
                            alarm_instance.start_time,
                            alarm_instance.end_time
                        ]
                    )

            # 生成excel
            if start_time.strftime('%Y-%m-%d') == end_time.strftime('%Y-%m-%d'):
                file_name = f"{project_instance.name}{start_time.strftime('%Y-%m-%d')}告警信息{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            else:
                file_name = f"{project_instance.name}{start_time.strftime('%Y-%m-%d')}-{end_time.strftime('%Y-%m-%d')}告警信息{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

            file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)

            sheet1 = f'{project_instance.name}'

            sheet1_headers = ['编号', '站名', '点描述', '类型', '状态', '发生时间', '恢复时间']
        else:
            type_mapping = {1: "Faulty", 0: "Warning", 2: "Alarm", 3: "Event", 4: "Offline", 5: "Com. Error"}

            for ins in total_fault_alarm_instances:
                related_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(
                    aggr_alarm_id=ins.joint_primary_key).order_by('-start_time')
                for alarm_instance in related_alarm_instances:
                    station = models.StationDetails.objects.filter(id=alarm_instance.station_id).first()
                    total_fault_alarms.append(
                        [
                            alarm_instance.joint_primary_key,
                            station.station_name if station else '--',
                            alarm_instance.device + ": " + ALARM_ZH_EN_MAP[alarm_instance.details.strip()][lang],
                            type_mapping[alarm_instance.type],
                            ALARM_ZH_EN_MAP[alarm_instance.note.strip().replace(' ', '')][lang],
                            alarm_instance.start_time,
                            alarm_instance.end_time
                        ]
                    )

            # 生成excel
            if start_time.strftime('%Y-%m-%d') == end_time.strftime('%Y-%m-%d'):
                file_name = f"{project_instance.name}_{start_time.strftime('%Y-%m-%d')} Alarm Information {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            else:
                file_name = f"{project_instance.name}_{start_time.strftime('%Y-%m-%d')}-{end_time.strftime('%Y-%m-%d')} Alarm Information {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"


            file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)

            sheet1 = f'{project_instance.name}'

            sheet1_headers = ['Number', 'Installation', 'Point Description', 'Type', 'Status', 'From', 'To']

        create_excel(file_path, sheet_name=sheet1, headers=sheet1_headers, data=total_fault_alarms)

        file_path = post2minio(file_path, object_name=file_name)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "url": file_path
            },
        })


class AlarmTimesView(APIView):
    """
    查看告警时段
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def generate_time_series(self, time_ranges, seconds):
        result = []
        min_time = min([t[0] for t in time_ranges])
        max_time = max([t[1] for t in time_ranges])

        min_time = datetime.datetime.combine(min_time, datetime.time.min)
        max_time = datetime.datetime.combine(max_time, datetime.time.max)

        current_time = min_time
        while current_time <= max_time:
            is_in_range = any([current_time >= t[0] and current_time <= t[1] for t in time_ranges])
            result.append({current_time.strftime("%Y-%m-%d %H:%M:%S"): 1 if is_in_range else 0})
            current_time += datetime.timedelta(seconds=seconds)

        return result

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            aggr_alarm_instance = models.AggrFaultAlarm.objects.using('alarm_module').get(joint_primary_key=id)
        except Exception as e:
            error_log.error("查看告警时段:告警不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "告警不存在" if lang == 'zh' else "Alarm does not exist."}})

        # related_alarm_instances = aggr_alarm_instance.faultalarm_set.all().order_by('start_time')
        related_alarm_instances = models.FaultAlarm.objects.using('alarm_module').filter(aggr_alarm_id=aggr_alarm_instance.joint_primary_key).order_by('start_time').all()

        times = []

        if related_alarm_instances:
            for alarm_instance in related_alarm_instances:
                start_time = alarm_instance.start_time
                if alarm_instance.end_time:
                    end_time = alarm_instance.end_time
                else:
                    end_time = datetime.datetime.now()
                times.append((start_time, end_time))

        time_series = self.generate_time_series(times, 300)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": time_series
                },
            }
        )


class AlarmFeedbackView(APIView):
    """
    告警：反馈
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').get(joint_primary_key=id)
        except Exception as e:
            error_log.error("告警反馈:告警不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR,
                 "data": {"message": "error", "detail": "告警不存在" if lang == 'zh' else "The alarm does not exist."}})

        # feedback = aggr_alarm.feedback
        feedback = models.AggrFaultAlarmFeedback.objects.filter(id=aggr_alarm.feedback_id).first()

        if feedback:
            # ser = AlarmFeedbackDetailSerializer(instance=feedback)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": {
                            "id": feedback.id,
                            "content": feedback.content if lang == 'zh' else feedback.en_content,
                            "is_dispatch_worker": feedback.is_dispatch_worker,
                            "worker_order": feedback.worker_order,
                            "question_type": feedback.question_type if lang == 'zh' else feedback.en_question_type,
                            "expected_closing_date": feedback.expected_closing_date,
                            "real_closing_date": feedback.real_closing_date,
                            "note": feedback.note if lang == 'zh' else feedback.en_note,
                        }
                    }
                }
            )

        else:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "该告警暂无反馈内容" if lang == 'zh' else "There is no feedback content for this alarm."
                    }
                }
            )

    def post(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').get(joint_primary_key=id)
        except Exception as e:
            error_log.error("告警反馈:告警不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "告警不存在"}})

        ser = AlarmFeedbackSerializer(data=request.data, context=self.serializer_context)

        try:
            if not ser.is_valid():
                error_log.error("告警反馈:字段校验不通过")
                return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
        except Exception as e:
            error_log.error("告警反馈:字段校验不通过")
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": e.args[0]}})

        if aggr_alarm.type not in [1, 4, 5]:
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail":
                '只有"故障"、"离线"、"通讯异常”方可反馈！' if lang == 'zh' else
                "Only 'fault', 'offline', and 'communication exception' can be feedback!"}})

        ser.save()
        feedback = models.AggrFaultAlarmFeedback.objects.filter(**ser.validated_data,
                                                            feedback_user_id=self.serializer_context["user_id"]).first()

        aggr_alarm.feedback_id = feedback.id if feedback else None
        aggr_alarm.save()

        return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f'反馈内容：写入成功.' if lang == 'zh' else f'Feedback content: Write successfully.'
                },
            }
        )

    def put(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            feedback = models.AggrFaultAlarmFeedback.objects.get(id=id)
        except Exception as e:
            error_log.error("告警反馈:反馈不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "反馈不存在" if
                lang == 'zh' else "The feedback does not exist."}})

        ser = AlarmFeedbackSerializer(instance=feedback, data=request.data, context=self.serializer_context)

        try:
            if not ser.is_valid():
                error_log.error("告警反馈:字段校验不通过")
                return Response(
                    {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
        except Exception as e:
            error_log.error("告警反馈:字段校验不通过")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": e.args[0]}})

        ser.save()

        return Response({
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f'反馈内容：{feedback.id}修改成功.' if lang == 'zh' else f'Feedback content: {feedback.id} modified successfully.'
                },
            }
        )


class RelatedAlarmFeedbackView(APIView):
    """
    告警：7日内相同告警的反馈内容
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').get(joint_primary_key=id)
        except Exception as e:
            error_log.error("告警反馈:告警不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR,
                 "data": {"message": "error", "detail": "告警不存在" if lang == 'zh' else "Alarm does not exist."}})

        # feedback = aggr_alarm.feedback
        feedback = models.AggrFaultAlarmFeedback.objects.filter(id=aggr_alarm.feedback_id).first()

        if not feedback:

            related_alarms = models.AggrFaultAlarm.objects.using('alarm_module').filter(
                station_id=aggr_alarm.station_id,
                point=aggr_alarm.point,
                type=aggr_alarm.type,
                details=aggr_alarm.details,
                create_time__gte=aggr_alarm.create_time - datetime.timedelta(days=7)
            ).all()

            if related_alarms.exists():
                for related_alarm in related_alarms:
                    if related_alarm.feedback_id:
                        # feedback = related_alarm.feedback
                        feedback = models.AggrFaultAlarmFeedback.objects.filter(
                            id=related_alarm.feedback_id).first()
                        if feedback:
                            return Response(
                                {
                                    "code": common_response_code.SUCCESS,
                                    "data": {
                                        "message": "success",
                                        "tag": "related",
                                        "detail": {
                                            "id": feedback.id,
                                            "content": feedback.content if lang == 'zh' else feedback.en_content,
                                            "is_dispatch_worker": feedback.is_dispatch_worker,
                                            "worker_order": feedback.worker_order,
                                            "question_type": feedback.question_type if lang == 'zh' else feedback.en_question_type,
                                            "expected_closing_date": feedback.expected_closing_date,
                                            "real_closing_date": feedback.real_closing_date,
                                            "note": feedback.note if lang == 'zh' else feedback.en_note
                                        }
                                    }
                                }
                            )
                        else:
                            return Response(
                                {
                                    "code": common_response_code.NO_DATA,
                                    "data": {
                                        "message": "no data",
                                        "detail": "该告警7日内暂无反馈内容" if lang == 'zh' else "No feedback content within 7 days."
                                    }
                                }
                            )
                    else:
                        return Response(
                            {
                                "code": common_response_code.NO_DATA,
                                "data": {
                                    "message": "no data",
                                    "detail": "该告警7日内暂无反馈内容" if lang == 'zh' else "No feedback content within 7 days."
                                }
                            }
                        )

                else:
                    return Response(
                        {
                            "code": common_response_code.NO_DATA,
                            "data": {
                                "message": "no data",
                                "detail": "该告警7日内暂无反馈内容" if lang == 'zh' else "No feedback content within 7 days."
                            }
                        }
                    )

        else:
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": {
                            "id": feedback.id,
                            "content": feedback.content if lang == 'zh' else feedback.en_content,
                            "is_dispatch_worker": feedback.is_dispatch_worker,
                            "worker_order": feedback.worker_order,
                            "question_type": feedback.question_type if lang == 'zh' else feedback.en_question_type,
                            "expected_closing_date": feedback.expected_closing_date,
                            "real_closing_date": feedback.real_closing_date,
                            "note": feedback.note if lang == 'zh' else feedback.en_note
                        }
                    }
                }
            )


class AlarmMessageView(APIView):
    """
    告警：推送至消息中心
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}
        self.serializer_context['lang'] = request.headers.get("lang", 'zh')

    def post(self, request, id):
        lang = request.headers.get("lang", 'zh')
        try:
            aggr_alarm = models.AggrFaultAlarm.objects.using('alarm_module').get(joint_primary_key=id)
        except Exception as e:
            error_log.error("告警推送至消息中心:告警不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "告警不存在" if
                lang == 'zh' else "Alarm does not exist."}})


        if aggr_alarm.type == 1:
            m = "故障"
            en_m = "Faulty"
        elif aggr_alarm.type == 4:
            m = "离线"
            en_m = "OffLine"
        else:
            m = "通讯异常"
            en_m = "Com. Error"

        time_str = f'{aggr_alarm.start_time.strftime("%m月%d日 %H:%M:%S")}-{aggr_alarm.end_time.strftime("%m月%d日 %H:%M:%S")}' if aggr_alarm.status == 1 else f'从{aggr_alarm.start_time.strftime("%m月%d日 %H:%M:%S")}'
        station = models.StationDetails.objects.filter(id=aggr_alarm.station_id).first()
        if station:
            project = station.master_station.project
            alarm_title = f"{project.name}项目{time_str}出现{aggr_alarm.note}{m}: {ALARM_ZH_EN_MAP[aggr_alarm.details.strip()]['zh']}。"

            en_time_str = f'{aggr_alarm.start_time.strftime("%m-%d %H:%M:%S")}-{aggr_alarm.end_time.strftime("%m-%d %H:%M:%S")}' if aggr_alarm.status == 1 else f'from {aggr_alarm.start_time.strftime("%m-%d %H:%M:%S")}'

            en_alarm_title = f"The project {project.name} {en_time_str} appear {ALARM_ZH_EN_MAP[aggr_alarm.note.strip().replace(' ', '')]['en']} {en_m}: {ALARM_ZH_EN_MAP[aggr_alarm.details.strip()]['en']}."

            receivers = [69, 76, 88, 120, 182, request.user['user_id']]

            for receiver in list(set(receivers)):
                # 首先查询是否推送过，已推送则跳过
                if models.MessageCenter.objects.filter(alarm=aggr_alarm, user_id=receiver).exists():
                    error_log.error(f"告警推送至消息中心: {receiver}已推送过, 将再次推送")
                    # continue

                models.MessageCenter.objects.create(
                    title=alarm_title,
                    en_title=en_alarm_title,
                    type=1,
                    is_read=0,
                    is_verify=0,
                    user_id=receiver,
                    related_id=aggr_alarm.joint_primary_key
                )

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": f'告警推送至消息中心: 推送成功.' if lang == 'zh' else 'Alarm message has been pushed.'
                }
            }
        )

    def get(self, request, id):
        """
        查看消息：告警详情
        """""
        lang = request.headers.get("lang", 'zh')
        try:
            message = models.MessageCenter.objects.get(id=id)
        except Exception as e:
            error_log.error("消息中心:告警消息不存在")
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "消息中心:告警消息不存在"
                if lang == 'zh' else "Alarm message does not exist."}})

        if message.type == 1:
            aggr_alarm = AggrFaultAlarm.objects.using('alarm_module').filter(joint_primary_key=message.related_id).first()
            if aggr_alarm:
                station = models.StationDetails.objects.filter(id=aggr_alarm.station_id).first()
                title = f"{station.master_station.project.name}项目{aggr_alarm.start_time.strftime('%m月%d日')}告警信息" \
                    if lang == 'zh' else f"The project {station.master_station.project.name} {aggr_alarm.start_time.strftime('%m-%d')} alarm information"
                description = message.title if lang == 'zh' else message.en_title
                receive_message_time = message.create_time.strftime("%Y-%m-%d %H:%M:%S")
                related_project_id = station.master_station.project.id
                alarm_type = aggr_alarm.type
                target_date = aggr_alarm.start_time.strftime("%Y-%m-%d")
                alarm_details = []

                # related_alarms = aggr_alarm.faultalarm_set.all().order_by('start_time')
                related_alarms = (models.FaultAlarm.objects.using('alarm_module').filter(aggr_alarm_id=aggr_alarm.joint_primary_key).
                                  order_by('-start_time').all())
                for alarm in related_alarms:
                    alarm_details.append({
                        "id": alarm.joint_primary_key,
                        "station": station.station_name if lang == 'zh' else station.station_name,
                        "desc": ALARM_ZH_EN_MAP[alarm.details.strip()][lang],
                        "status": ALARM_ZH_EN_MAP[alarm.note.strip().replace(' ', '')][lang],
                        "start_time": alarm.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "end_time": alarm.end_time.strftime("%Y-%m-%d %H:%M:%S") if alarm.end_time else "--",
                    })

                return_dict = {
                    "title": title,
                    "description": description,
                    "receive_message_time": receive_message_time,
                    "related_project_id": related_project_id,
                    "alarm_type": alarm_type,
                    "alarm_status": aggr_alarm.status,
                    "target_date": target_date,
                    "alarm_details": alarm_details
                }

                return Response({
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": return_dict
                    },
                })

            else:
                return Response({
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "success",
                        "detail": "消息中心: 该消息非告警消息，无告警信息" if lang == 'zh' else "Alarm message is not an alarm message, no alarm information."
                    }
                })
        else:
            return Response({
                "code": common_response_code.NO_DATA,
                "data": {
                    "message": "success",
                    "detail": "消息中心: 该消息非告警消息，无告警信息" if lang == 'zh' else "Alarm message is not an alarm message, no alarm information."
                }
            })


# class AlarmSendEmailView(APIView):
#     """小程序端告警错误发送短信"""
#
#     authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证
#
#     def post(self, request):
#         ser = AlarmSendSMSSerializer(data=request.data)
#         if not ser.is_valid():
#             error_log.error("告警错误发送短信:字段校验不通过")
#             return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})
#         try:
#             # station_ins = models.StationDetails.objects.get(english_name=ser.validated_data['station'])
#             master_station = models.MaterStation.objects.get(english_name=ser.validated_data['station'], is_delete=0)
#             master_station_name = master_station.name
#             slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
#         except Exception as e:
#             error_log.error("告警错误发送短信:Station不存在")
#             return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "station不存在"}})
#         sms_message = f"{datetime.datetime.today().strftime('%Y-%m-%d   %H:%M:%S')},  {master_station_name}  出现以下故障告警记录:\n"
#         for i in ser.validated_data["ids"]:
#             try:
#                 stations_ids = [s.id for s in slave_stations]
#                 ins = models.FaultAlarm.objects.using('alarm_module').get(id=i, station_id__in=stations_ids)
#             except Exception as e:
#                 error_log.error("告警错误发送短信:ID不存在")
#                 return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "id不存在"}})
#             detail = ins.details
#             start_time = ins.start_time
#             point = ins.point
#             mes = f"故障发生时间:  {start_time},故障点位:  {point},  故障详情:  {detail}。 \n"
#             sms_message += mes
#         user = request.user["user_id"]
#         user_ins = models.UserDetails.objects.get(id=user)
#         user_name = user_ins.user_name
#         phone = user_ins.mobile
#         sms_message += f"请及时联系业主，确认报修内容及服务时间。\n 业主姓名:  {user_name}   手机号:  {phone}"
#
#         Subject = f'{datetime.datetime.today().strftime("%Y-%m-%d")}来自{master_station_name}的报修消息'
#         to_addrs = ['<EMAIL>', "<EMAIL>"]
#         success_log.info("告警错误发送短信:短信发送成功")
#         sender_show = "白泽添禄报修邮件"
#         recipient_show = "白泽添禄运维人员"
#         sendMail_(sms_message, Subject, sender_show, recipient_show, to_addrs)
#         return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "报修成功"}})


class WebAlarmSendEmailView(APIView):
    """web端 告警错误发送短信"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = WebAlarmSendSMSSerializer(data=request.data, context=self.serializer_context)
        if not ser.is_valid():
            error_log.error("告警错误发送短信:字段校验不通过")
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": ser.errors}})

        # ids_str = ser.validated_data["ids"]
        # try:
        #     import ast
        #     ids = ast.literal_eval(ids_str)
        #     if not len(ids):
        #         error_log.error("发送短信失败:ids 参数不能为空")
        #         return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail":
        #             "发送短信失败:ids 参数不能为空"}})
        # except Exception as e:
        #     error_log.error("发送短信失败:ids 参数有误：{}".format(e))
        #     return Response({"code": common_response_code.FIELD_ERROR,
        #                      "data": {"message": "error", "detail": "发送短信失败:ids 参数有误"}})

        try:
            project_ins = models.Project.objects.get(english_name=ser.validated_data['project'], is_used=1)
            project_name = project_ins.name
            # stations = project_ins.stationdetails_set.filter(is_delete=0).all()

            # master_station = models.MaterStation.objects.get(english_name=ser.validated_data['station'], is_delete=0)
            # master_station_name = master_station.name
            slave_stations = models.StationDetails.objects.filter(master_station__project=project_ins).all()

        except Exception as e:
            error_log.error("告警错误发送短信:project 不存在：{}".format(e))
            return Response({"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail":
                            "project 不存在" if lang == 'zh' else 'The project does not exist.'}})
        sms_message = f"{datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')},  {project_name}  出现以下故障告警记录:\n"\
            if lang == 'zh' else f"The following fault alarm records have occurred in the project: {project_name} at {datetime.datetime.today().strftime('%Y-%m-%d %H:%M:%S')}"

        is_need_flag = 0
        for station in slave_stations:
            instances = models.FaultAlarm.objects.using('alarm_module').filter(status=0, station_id=station.id)
            station_name = station.station_name
            if instances.exists():
                for instance in instances:
                    detail = ALARM_ZH_EN_MAP[instance.details.strip()][lang]
                    start_time = instance.start_time
                    point = instance.point
                    mes = f"故障发生时间:  {start_time}, 故障站点：{station_name}, 故障点位:  {point},  故障详情:  {detail}。 \n"\
                        if lang == 'zh' else \
                        (f"Fault occurred at:  {start_time}, Fault station:  {station_name}, Fault point:"
                         f"  {point}, Fault details:  {detail}。 \n")
                    sms_message += mes
                is_need_flag = 1

        if is_need_flag:
            user = request.user["user_id"]
            user_ins = models.UserDetails.objects.get(id=user)
            user_name = user_ins.user_name if lang == 'zh' else user_ins.en_user_name
            phone = user_ins.mobile
            sms_message += f"请及时联系业主，确认报修内容及服务时间。\n 业主姓名:  {user_name}   手机号:  {phone}" if lang == 'zh' else \
                (f"Please contact the owner as soon as possible to confirm the content of the repair and the service time. \n "
                 f" Owner name:  {user_name}   Phone number:  {phone} \n")

            Subject = f'{datetime.datetime.today().strftime("%Y-%m-%d")}来自{project_name}的报修消息' if lang == 'zh' else \
                f'{datetime.datetime.today().strftime("%Y-%m-%d")} from {project_name} report message.'
            to_addrs = ['<EMAIL>', "<EMAIL>", "<EMAIL>"]
            # to_addrs = ["<EMAIL>"]
            success_log.info("告警邮件发送成功")
            sender_show = "白泽添禄报修邮件" if lang == 'zh' else "Tianlu Report Email."
            recipient_show = "白泽添禄运维人员" if lang == 'zh' else "Tianlu Operation Personnel."
            try:
                # print(2208, sms_message, Subject, sender_show, recipient_show, to_addrs)
                sendMail_(sms_message, Subject, sender_show, recipient_show, to_addrs)
                return Response({"code": common_response_code.SUCCESS, "data": {"message": "success", "detail": "报修成功"
                                if lang == 'zh' else 'Report successfully.'}})
            except Exception as e:
                error_log.error("报警失败：邮件发送失败！！！")
                return Response({"code": common_response_code.FIELD_ERROR,
                                 "data": {"message": "error", "detail": "报警失败：邮件发送失败！！！" if lang == 'zh' else
                                 'Alarm failed: Email sending failed!'}})
        else:
            return Response(
                {"code": common_response_code.SUCCESS, "data": {"message": "no_need", "detail": "检查成功: 当前项目不需要报修."
                 if lang == 'zh' else 'Check success: The current project does not need to report.'}})

class OcsUploadView(APIView):
    """查看故障录波列表"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def deal_item_data(self, ins,alarm_tigger_point, lang='zh'):
       
        ins["detail"] = alarm_tigger_point[ins["tigger_point"]] if alarm_tigger_point.get(ins["tigger_point"]) else '--'  # 点描述
        ins["time_"] = ins["time"].strftime('%Y-%m-%d %H:%M:%S')

        stat = models.StationDetails.objects.filter(english_name=ins['english_name'],is_delete=0).first()
        master_station_bean = stat.master_station  # 主站内容
        if master_station_bean.name == stat.station_name:  # 主站，从站名称一样标准站
            ins["station_name"] = stat.station_name
        else:
            ins["station_name"] = f"{master_station_bean.name}:{stat.station_name}"
        ins["master_id"] = master_station_bean.id
        return ins

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def get(self, request, id):
       
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebOcsMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"录波列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"录波列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=30)

        start_time = ser.validated_data.get('start_time', default_start_day)
        end_time = ser.validated_data.get('end_time', default_end_day)
        detail = ser.validated_data.get('detail',None)
        station_names = ser.validated_data.get('english_name',[]) # 主站
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))

        start = (int(page) - 1) * int(page_size)
        end = start + int(page_size) 

        try:
            project_instance = models.Project.objects.get(id=id)
        except Exception as e:
            error_log.error("告警监控：项目不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "项目不存在"}})
    
        if station_names:  # 指定并网点
            master_stations = project_instance.materstation_set.filter(english_name__in= eval(station_names[0]),is_delete=0).all()
            slave_stations = models.StationDetails.objects.filter(master_station__in=[s.id for s in master_stations],is_delete=0).all()
           
        else: #项目下所有并网点
            slave_stations = models.StationDetails.objects.filter(master_station__project=project_instance, is_delete=0).all()
            
        station_arr = [s.english_name for s in slave_stations]
        point_arr= ['','']  # 要查询的点集合
        # 确定是2.0还是3.0
        # print (station_arr,len(station_arr),type(station_arr),'***************')
        s_ids = [s.id for s in slave_stations]
        units = models.Unit.objects.filter(station__in=s_ids,is_delete=0).first()
        alarm_tigger_point = alarm_tigger_point_v2 if units and units.v_number <= 2 else alarm_tigger_point_v2  # 默认按天禄3.0的触发点
        alarm_report_point = alarm_report_point_v2 if units and units.v_number <= 2 else alarm_report_point_v2  # 默认按天禄3.0的录波点
        select_sql_1 = f"""select ROW_NUMBER() over() row_number,station english_name,e_device as tiggr_device,tigger_point, trigger_time time from dwd_oscupload_data_storage  
            where station in {to_tuple(station_arr)} and trigger_time >='{start_time}'  and trigger_time<='{end_time}' """
        end_sql = "group by station,e_device,tigger_point,trigger_time order by trigger_time desc"
        if detail: # 传入了描述
            for k,v in alarm_tigger_point.items():
                if detail in v:
                    point_arr.append(k)
            select_sql_1 = select_sql_1 + f""" and tigger_point in {to_tuple(point_arr)} """
           
        select_sql_1 = select_sql_1 + end_sql
        results_1 = dwd_db_tool.select_many(select_sql_1)
        total_count = len(results_1)
        if end > total_count:
            end = total_count + 1

        ocs_alarm_instances = results_1[start:end]
        # total_pages = math.ceil(total_count / page_size)
        # temp_list = []
        if len(ocs_alarm_instances):
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                for item in ocs_alarm_instances:
                    executor.submit(self.deal_item_data, item,alarm_tigger_point)
        #             futures.append(future)
        #         print('futures:******************************',futures)
        #         for future in concurrent.futures.as_completed(futures):
        #             i = future.result()
        #             print ('&&&&&&&&&&&&',i)
        #             temp_list.append(i)

        #         connections.close_all()

        paginator_info = {
            "page": page,
            "page_size": page_size,
            "total_count": total_count
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ocs_alarm_instances,
                    "paginator_info": paginator_info,
                    "alarm_report_point":alarm_report_point,
                    "v_number":units.v_number,
                    
                },
            }
        )


class OcsUploadDownloadView(APIView):
    """
    录波：下载
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def get(self, request, id):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebOcsMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"录波列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"录波列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        today = datetime.date.today()
        default_end_day = today
        default_start_day = today - datetime.timedelta(days=30)

        start_time = ser.validated_data.get('start_time', default_start_day)
        end_time = ser.validated_data.get('end_time', default_end_day)
        detail = ser.validated_data.get('detail',None)
        station_names = ser.validated_data.get('english_name',[]) # 主站

        try:
            project_instance = models.Project.objects.get(id=id)
        except Exception as e:
            error_log.error("告警监控：项目不存在：{}".format(e))
            return Response(
                {"code": common_response_code.FIELD_ERROR, "data": {"message": "error", "detail": "项目不存在"}})
    
        

        if station_names:  # 指定并网点
            master_stations = project_instance.materstation_set.filter(english_name__in= eval(station_names[0]),is_delete=0).all()
            slave_stations = models.StationDetails.objects.filter(master_station__in=[s.id for s in master_stations],is_delete=0).all()
           
        else: #项目下所有并网点
            slave_stations = models.StationDetails.objects.filter(master_station__project=project_instance, is_delete=0).all()
        

        station_arr = [s.english_name for s in slave_stations]
        point_arr= ['','']  # 要查询的点集合
        # 确定是2.0还是3.0
        s_ids = [s.id for s in slave_stations]
        units = models.Unit.objects.filter(station__in=s_ids,is_delete=0).first()
        alarm_tigger_point = alarm_tigger_point_v2 if units and units.v_number <= 2 else alarm_tigger_point_v2  # 默认按天禄3.0的触发点
        alarm_report_point = alarm_report_point_v2 if units and units.v_number <= 2 else alarm_report_point_v2  # 默认按天禄3.0的录波点
        
        select_sql_1 = f"""select ROW_NUMBER() over() row_number,station english_name,e_device as tiggr_device,tigger_point, trigger_time time from dwd_oscupload_data_storage  
            where station in {to_tuple(station_arr)} and trigger_time >='{start_time}'  and trigger_time<='{end_time}' """
        end_sql = "group by station,e_device,tigger_point,trigger_time  order by trigger_time desc"
        if detail: # 传入了描述
            for k,v in alarm_tigger_point.items():
                if detail in v:
                    point_arr.append(k)
            select_sql_1 = select_sql_1 + f""" and tigger_point in {to_tuple(point_arr)} """
           
        select_sql_1 = select_sql_1 + end_sql
        ocs_alarm_instances = dwd_db_tool.select_many(select_sql_1)
        
        # 创建一个工作簿
        workbook = openpyxl.Workbook()
        alignment_center = Alignment(horizontal='center', vertical='center')
        file_name = f"{project_instance.name}{start_time.strftime('%Y-%m-%d %H:%M:%S')}-{end_time.strftime('%Y-%m-%d %H:%M:%S')}故障录波数据{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)
        
        for ind,ocs in enumerate(ocs_alarm_instances):
            stat = models.StationDetails.objects.filter(english_name=ocs['english_name'],is_delete=0).first()
            station_name = stat.master_station.name  # 并网点名称
            detail = alarm_tigger_point[ocs["tigger_point"]] if alarm_tigger_point.get(ocs["tigger_point"]) else '--'  # 点描述
            time_ = ocs["time"].strftime('%Y-%m-%d %H:%M:%S')  # 触发时间
            ocs_device = ocs.get("tiggr_device") if ocs.get("tiggr_device") else ''
            title_ = f'{station_name}-{ocs_device+detail}_{ind}'  # 工作表内容
            if ind == 0:  # 第一个工作表
                sheet1 = workbook.active
                sheet1.title = title_
            else:
                sheet1 = workbook.create_sheet(title=title_)
            # 前两行A_F列合并
            sheet1.merge_cells('A1:F1')
            sheet1.merge_cells('A2:F2')
            if station_name == stat.station_name:  # 主站，从站名称一样标准站
                sheet1['A1'] = f"{station_name}-{detail}-{ocs_device}"
            else:
                sheet1['A1'] = f"{station_name}:{stat.station_name}-{detail}-{ocs_device}"
            sheet1['A2'] = '触发时间：'+ time_
            sheet1['A1'].alignment = alignment_center
            sheet1['A2'].alignment = alignment_center

            sql = "select device,report_point,times,`values` from dwd_oscupload_data_storage  where station =%s and e_device = %s and tigger_point=%s and trigger_time=%s order by device asc,report_point asc " # 
            datas = dwd_db_tool.select_many(sql, *(ocs['english_name'],ocs_device,ocs['tigger_point'],time_ ))
         
            arr_data = []
            time_arr = []
            # 将数据写入矩阵中
            for data in datas:
                time_arr = data['times']
                device = data['device']
                point = alarm_report_point[data["report_point"]] if alarm_report_point.get(data["report_point"]) else '--'  # 点描述
                arr = [device+'-'+point,json.dumps(['%.3f'%float(x) for x in json.loads(data['values'])])]
                arr_data.append(arr)
                
            arr_data.insert(0,['时间',time_arr])
            transposed_A = list(zip(*arr_data))  # 行列互换
            for row_num, row_data in enumerate(transposed_A, 3):  # 写入表格
                for col_num, cell_data in enumerate(row_data, 1):
                    if '[' in cell_data:
                        for a,b in enumerate(eval(cell_data)):
                            sheet1.cell(row=row_num+a, column=col_num).value = b
                    else:
                        sheet1.cell(row=row_num, column=col_num).value = cell_data

        workbook.save(file_path)
        file_path = post2minio(file_path, object_name=file_name)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "url": file_path
            },
        })
    
class OcsUploadInfosView(APIView):
    """
    录波：详情
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]
    def deal_item_data(self, ins,alarm_report_point):
        detail = alarm_report_point[ins["report_point"]] if alarm_report_point.get(ins["report_point"]) else '--'  # 点描述
        ins['point'] = ins['device']+'-'+detail
        if ins['datatype'] == 'measure':
            ins['values'] = json.dumps(['%.3f'%float(x) for x in json.loads(ins['values'])])
        else:
            ins['values'] = json.dumps(['%.0f'%float(x) for x in json.loads(ins['values'])])
        return ins

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebOcsInfosMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"录波详情列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"录波详情列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        
        tiggr_devices = ser.validated_data.get('tiggr_devices', [])  # 设备集合
        tiggr_devices = tiggr_devices if isinstance(tiggr_devices,list) else eval(tiggr_devices)
        ocs_points = ser.validated_data.get('ocs_points', [])  # 录波点集合
        ocs_points = ocs_points if isinstance(ocs_points,list) else eval(ocs_points)
        ocs_devices = ser.validated_data.get('ocs_devices', [])  # 录波设备集合
        ocs_devices = ocs_devices if isinstance(ocs_devices,list) else eval(ocs_devices)
        time_ = ser.validated_data.get('time_')  #触发时间
        v_number = ser.validated_data.get('v_number') #设备版本号
        station = ser.validated_data.get('english_name')  # 从站
        tiggr_point = ser.validated_data.get('tiggr_point')   # 触发点

        # print('***************',tiggr_devices,type(tiggr_devices))
        # print('________________',ocs_points,type(ocs_points))
        
        # alarm_tigger_point = alarm_tigger_point_v2 if v_number == 2 else alarm_tigger_point_v2  # 默认按天禄3.0的触发点
        alarm_report_point = alarm_report_point_v2 if v_number <= 2 else alarm_report_point_v2  # 默认按天禄3.0的录波点

        select_sql = f"""select datatype,device,report_point,times,`values`  from dwd_oscupload_data_storage where station ='{station}' and tigger_point ='{tiggr_point}' 
        and trigger_time ='{time_}' """
        end_sql = """ order by device asc,report_point asc """
        if tiggr_devices:
            select_sql = select_sql + f""" and e_device in {to_tuple(tiggr_devices)} """
        if ocs_points:
            select_sql = select_sql + f""" and report_point in {to_tuple(ocs_points)} """
        if ocs_devices:
            select_sql = select_sql + f""" and device in {to_tuple(ocs_devices)} """
        select_sql = select_sql + end_sql
        ocs_alarm_instances = dwd_db_tool.select_many(select_sql)

        if len(ocs_alarm_instances):
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                for item in ocs_alarm_instances:
                    executor.submit(self.deal_item_data, item, alarm_report_point)
                #     futures.append(future)

                # for future in concurrent.futures.as_completed(futures):
                #     i = future.result()
                #     temp_list.append(i)

                # connections.close_all()
        
        
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ocs_alarm_instances,
                    "time":ocs_alarm_instances[0]['times'] if ocs_alarm_instances else []
                },
            }
        )

class OcsUploadInfosDownloadView(APIView):
    """
    录波：详情下载
    """""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]
    def deal_item_data(self, ins,alarm_report_point):
        detail = alarm_report_point[ins["report_point"]] if alarm_report_point.get(ins["report_point"]) else '--'  # 点描述
        ins['point'] = ins['device']+'-'+detail
        if ins['datatype'] == 'measure':
            ins['values'] = json.dumps(['%.3f'%float(x) for x in json.loads(ins['values'])])
        else:
            ins['values'] = json.dumps(['%.0f'%float(x) for x in json.loads(ins['values'])])
        return ins

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"lang": request.headers.get("lang", 'zh')}

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.WebOcsInfosMonitorSerializer(data=request.query_params, context=self.serializer_context)
        try:
            if not ser.is_valid():
                error_log.error(f"录波详情列表:字段校验不通过 =>{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"录波详情列表:字段校验不通过 =>{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        tiggr_devices = ser.validated_data.get('tiggr_devices', [])  # 设备集合
        tiggr_devices = tiggr_devices if isinstance(tiggr_devices,list) else eval(tiggr_devices)
        ocs_points = ser.validated_data.get('ocs_points', [])  # 录波点集合
        ocs_points = ocs_points if isinstance(ocs_points,list) else eval(ocs_points)
        ocs_devices = ser.validated_data.get('ocs_devices', [])  # 录波设备集合
        ocs_devices = ocs_devices if isinstance(ocs_devices,list) else eval(ocs_devices)
        time_ = ser.validated_data.get('time_')  #触发时间
        v_number = ser.validated_data.get('v_number') #设备版本号
        station = ser.validated_data.get('english_name')  # 从站
        tiggr_point = ser.validated_data.get('tiggr_point')   # 触发点
        station_name = ser.validated_data.get('station_name')   # 并网点名称
        
        alarm_tigger_point = alarm_tigger_point_v2 if v_number <= 2 else alarm_tigger_point_v2  # 默认按天禄3.0的触发点
        alarm_report_point = alarm_report_point_v2 if v_number <= 2 else alarm_report_point_v2  # 默认按天禄3.0的录波点


        select_sql = f"""select datatype,device,report_point,times,`values`  from dwd_oscupload_data_storage where station ='{station}' and tigger_point ='{tiggr_point}' 
        and trigger_time ='{time_}' """
        end_sql = """ order by device asc,report_point asc """
        if tiggr_devices:
            select_sql = select_sql + f""" and e_device in {to_tuple(tiggr_devices)} """
        if ocs_points:
            select_sql = select_sql + f""" and report_point in {to_tuple(ocs_points)} """
        if ocs_devices:
            select_sql = select_sql + f""" and device in {to_tuple(ocs_devices)} """
        select_sql = select_sql + end_sql
        ocs_alarm_instances = dwd_db_tool.select_many(select_sql)

        if len(ocs_alarm_instances):
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                for item in ocs_alarm_instances:
                    executor.submit(self.deal_item_data, item, alarm_report_point)
                #     futures.append(future)

                # for future in concurrent.futures.as_completed(futures):
                #     i = future.result()
                #     temp_list.append(i)

                # connections.close_all()
        tiggr_detail = alarm_tigger_point[tiggr_point] if alarm_tigger_point.get(tiggr_point) else '--'
        workbook = openpyxl.Workbook()
        alignment_center = Alignment(horizontal='center', vertical='center')
        file_name = f"{station_name}{tiggr_detail}{str(time_)}录波数据{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        file_path = os.path.join(os.path.join(BASE_DIR, 'static'), file_name)
        sheet1 = workbook.active
        if ":" in station_name:
            sheet1.title = station_name.split(':')[0]+'-'+tiggr_detail  # 工作表名
        else:
            sheet1.title = station_name+'-'+tiggr_detail  # 工作表名
        # 前两行A_F列合并
        sheet1.merge_cells('A1:F1')
        sheet1.merge_cells('A2:F2')
        sheet1['A1'] = station_name+'-'+tiggr_detail+'-'+'-'.join(tiggr_devices)
        sheet1['A2'] = '触发时间：'+ str(time_)
        sheet1['A1'].alignment = alignment_center  # 居中
        sheet1['A2'].alignment = alignment_center
        arr_data = []
        time_arr= []
        for ocs in ocs_alarm_instances:
            # 将数据写入矩阵中
            time_arr = ocs['times']
            device = ocs['device']
            point = alarm_report_point[ocs["report_point"]] if alarm_report_point.get(ocs["report_point"]) else '--'  # 点描述
            arr = [device+'-'+point, ocs['values']]
            arr_data.append(arr)
        if arr_data:
            arr_data.insert(0,['时间',time_arr])
            transposed_A = list(zip(*arr_data))  # 行列互换
            for row_num, row_data in enumerate(transposed_A, 3):  # 写入表格
                for col_num, cell_data in enumerate(row_data, 1):
                    if '[' in cell_data:
                        for a,b in enumerate(eval(cell_data)):
                            sheet1.cell(row=row_num+a, column=col_num).value = b
                    else:
                        sheet1.cell(row=row_num, column=col_num).value = cell_data

        workbook.save(file_path)
        file_path = post2minio(file_path, object_name=file_name)

        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "url": file_path
            },
        })

        
   
