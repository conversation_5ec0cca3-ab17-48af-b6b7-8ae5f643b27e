package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.Station;
import com.robestec.analysis.entity.StationR;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 电站信息Mapper
 * 对应Python中的Station相关数据库操作
 */
@Mapper
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 查询电站容量信息
     * 对应Python中PowerPlanStations方法的查询逻辑
     */
    @Select("SELECT s.id, s.name, s.descr, " +
            "COALESCE(sr.electric_power, 0) as electric_power, " +
            "COALESCE(sr.electric_power, 0) as real_power " +
            "FROM t_station s " +
            "LEFT JOIN t_station_relation sr ON s.name = sr.station_name " +
            "WHERE s.register = 1 " +
            "ORDER BY s.index ASC")
    List<Map<String, Object>> selectStationCapacityList();

    /**
     * 根据电站ID列表查询电站信息
     * 对应Python中PowerPlanStationsRefresh方法的查询逻辑
     */
    @Select("<script>" +
            "SELECT s.id, s.name, s.descr, " +
            "COALESCE(sr.electric_power, 0) as electric_power " +
            "FROM t_station s " +
            "LEFT JOIN t_station_relation sr ON s.name = sr.station_name " +
            "WHERE s.id IN " +
            "<foreach collection='stationIds' item='stationId' open='(' separator=',' close=')'>" +
            "  #{stationId}" +
            "</foreach>" +
            "</script>")
    List<Map<String, Object>> selectStationsByIds(@Param("stationIds") List<Long> stationIds);

    /**
     * 根据电站名称查询电站信息
     */
    @Select("SELECT * FROM t_station WHERE name = #{name}")
    Station selectByName(@Param("name") String name);

    /**
     * 查询已注册的电站列表
     */
    @Select("SELECT * FROM t_station WHERE register = 1 ORDER BY index ASC")
    List<Station> selectRegisteredStations();

    /**
     * 根据描述模糊查询电站
     */
    @Select("SELECT * FROM t_station " +
            "WHERE descr LIKE CONCAT('%', #{descr}, '%') " +
            "ORDER BY index ASC")
    List<Station> selectByDescrLike(@Param("descr") String descr);

    /**
     * 统计已注册电站数量
     */
    @Select("SELECT COUNT(*) FROM t_station WHERE register = 1")
    int countRegisteredStations();
}

/**
 * 电站关系Mapper
 */
@Mapper
interface StationRMapper extends BaseMapper<StationR> {

    /**
     * 根据电站名称查询电站关系信息
     */
    @Select("SELECT * FROM t_station_relation WHERE station_name = #{stationName}")
    StationR selectByStationName(@Param("stationName") String stationName);

    /**
     * 查询所有电站关系信息
     */
    @Select("SELECT * FROM t_station_relation ORDER BY station_name ASC")
    List<StationR> selectAllStationRelations();

    /**
     * 根据省份查询电站关系
     */
    @Select("SELECT * FROM t_station_relation " +
            "WHERE province = #{province} " +
            "ORDER BY station_name ASC")
    List<StationR> selectByProvince(@Param("province") String province);

    /**
     * 根据电功率范围查询电站关系
     */
    @Select("SELECT * FROM t_station_relation " +
            "WHERE electric_power BETWEEN #{minPower} AND #{maxPower} " +
            "ORDER BY electric_power DESC")
    List<StationR> selectByPowerRange(
            @Param("minPower") Double minPower,
            @Param("maxPower") Double maxPower
    );
}
