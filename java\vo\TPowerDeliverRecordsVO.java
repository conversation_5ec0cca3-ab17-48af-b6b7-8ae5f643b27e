package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 功率计划下发记录VO
 */
@Data
@ApiModel("功率计划下发记录VO")
public class TPowerDeliverRecordsVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("英文任务名称")
    private String enName;

    @ApiModelProperty("联系人电话")
    private String mobile;

    @ApiModelProperty("功率计划列表(JSON格式)")
    private String powerList;

    @ApiModelProperty("关联并网点列表(JSON格式)")
    private String stationList;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("计划类型: 1-自定义, 2-周期性, 3-节假日")
    private Integer planType;

    @ApiModelProperty("计划类型名称")
    private String planTypeName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
