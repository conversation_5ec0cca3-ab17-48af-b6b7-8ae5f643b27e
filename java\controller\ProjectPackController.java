package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.projectpack.ProjectPackCreateDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackQueryDTO;
import com.robestec.analysis.dto.projectpack.ProjectPackUpdateDTO;
import com.robestec.analysis.service.ProjectPackService;
import com.robestec.analysis.vo.ProjectPackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目包管理API
 */
@RestController
@RequestMapping("/project-pack")
@RequiredArgsConstructor
@Api(tags = "项目包管理API")
public class ProjectPackController {

    private final ProjectPackService projectPackService;

    @GetMapping
    @ApiOperation("分页查询项目包")
    public PageResult<ProjectPackVO> queryProjectPack(@Validated ProjectPackQueryDTO queryDTO) {
        return projectPackService.queryProjectPack(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增项目包")
    public Result<Long> createProjectPack(@Validated @RequestBody ProjectPackCreateDTO createDTO) {
        return Result.succeed(projectPackService.createProjectPack(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增项目包")
    public Result createProjectPackList(@Validated @RequestBody List<ProjectPackCreateDTO> createDTOList) {
        projectPackService.createProjectPackList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改项目包")
    public Result updateProjectPack(@PathVariable Long id, @Validated @RequestBody ProjectPackUpdateDTO updateDTO) {
        updateDTO.setId(id);
        projectPackService.updateProjectPack(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除项目包")
    public Result deleteProjectPack(@PathVariable Long id) {
        projectPackService.deleteProjectPack(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取项目包详情")
    public Result<ProjectPackVO> getProjectPack(@PathVariable Long id) {
        return Result.succeed(projectPackService.getProjectPack(id));
    }

    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询项目包")
    public Result<List<ProjectPackVO>> getProjectPackByUserId(@PathVariable Long userId) {
        return Result.succeed(projectPackService.getProjectPackByUserId(userId));
    }

    @GetMapping("/name/{name}")
    @ApiOperation("根据项目包名称查询项目包")
    public Result<List<ProjectPackVO>> getProjectPackByName(@PathVariable String name) {
        return Result.succeed(projectPackService.getProjectPackByName(name));
    }

    @GetMapping("/count/user/{userId}")
    @ApiOperation("统计用户的项目包数量")
    public Result<Long> countByUserId(@PathVariable Long userId) {
        return Result.succeed(projectPackService.countByUserId(userId));
    }
}
