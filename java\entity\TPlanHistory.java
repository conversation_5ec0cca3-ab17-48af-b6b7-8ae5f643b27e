package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 计划历史记录表
 * 对应Python模型: TPlanHistory
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_plan_history")
public class TPlanHistory extends SuperEntity {

    /**
     * 计划名称
     */
    @TableField("name")
    private String name;

    /**
     * 状态: 1-已保存, 2-已下发, 3-执行中, 4-已完成, 5-下发失败, 6-已停止
     */
    @TableField("status")
    private Integer status;

    /**
     * 功率值
     */
    @TableField("power")
    private Double power;

    /**
     * 计划类型: 1-自定义, 2-周期性, 3-节假日
     */
    @TableField("plan_type")
    private Integer planType;

    /**
     * 计划开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 计划结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 是否跟随: 1-是, 0-否
     */
    @TableField("is_follow")
    private Integer isFollow;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 类型名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 描述
     */
    @TableField("descr")
    private String descr;

    /**
     * 电站名称
     */
    @TableField("station")
    private String station;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
