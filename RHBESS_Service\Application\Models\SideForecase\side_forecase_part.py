#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-10-12 09:27:05


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean


class ForecasePart(user_Base):
    u'用户侧预算用电分类部制度'
    __tablename__ = "t_side_forecase_part"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False, comment=u"用电分类部制度")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
   
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(ForecasePart(id=1,name='两部制',is_use=1));
       
        user_session.commit()
        user_session.close()
        
    def __repr__(self):
        
        return "{'id':%s,'name':'%s','is_use':%s}" % (
            self.id,self.name,self.is_use)
        
    