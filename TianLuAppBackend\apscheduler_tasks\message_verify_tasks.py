import datetime

from apis.user.models import Message<PERSON>enter


def message_verify_task():
    """消息确认定时任务：超过48小时自动确认"""
    now_time = datetime.datetime.now()
    verify_time = now_time - datetime.timedelta(days=2)
    # 查询所有超过48小时的策略消息，置为已读、已确认状态
    info = MessageCenter.objects.filter(is_verify=0, create_time__lte=verify_time, type=0, is_handle=0).all()
    for i in info:
        title = f'系统 确认{i.station.project.name}策略无误！'
        en_title = f'The system confirms that the {i.station. project. name} strategy is correct!'
        i.is_handle = 1
        i.save()
        MessageCenter.objects.create(title=title, en_title=en_title, type=0, is_read=0, is_verify=1, station=i.station, user_id=i.issue_user, issue_user=i.user_id,
                                     strategy_id=i.strategy_id, is_handle=2)

    return 1