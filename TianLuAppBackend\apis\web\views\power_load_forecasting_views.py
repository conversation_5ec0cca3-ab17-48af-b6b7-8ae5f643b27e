#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/8/19 下午3:39
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

import json
import datetime
import re
import math

from django.conf import settings
from apis.app2.utils import paging
from django.db import connections, transaction
from openpyxl import load_workbook
from io import BytesIO
import pandas as pd
import io

from LocaleTool.common import redis_pool
from tools.minio_tool import MinioTool
from decimal import Decimal
from django.db.models import Q, Sum

from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)

from rest_framework.views import APIView
from rest_framework.response import Response
from common import common_response_code
from apis.web import models
from dbutils.persistent_db import PersistentDB
import pymysql
import logging
from apis.user.models import MaterStation, StationDetails, PeakValleyNew
from apis.web.serializers import get_redis_connection
from apis.statistics_apis.models import UserStrategy, UserStrategyCategoryNew, Month, UserStrategyHours
from django.db.models import F, Value, CharField
from django.db.models.functions import Cast, Replace
from settings.types_dict import FORECASTING_MODEL_ID_MAPPING

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")


def can_be_parsed_as_list(json_str):
    """
    判断变量是否可以转换成list
    :param json_str:
    :return:
    """
    try:
        result = json.loads(json_str)
        return isinstance(result, list)
    except json.JSONDecodeError:
        return False


def get_is_have_data(station_id, start_time, end_time):
    # 针对防逆流电表未采集正向有功电能、负向有功电能的项目，不显示“电量”选项，即没有电量预测功能。
    # 查询ads_rhyc库 ads_report_loading_chag_data表，epae,enae，查询条件指定时间范围
    # 为null的值，0值也算有值

    # 获取今天的日期
    # today = datetime.datetime.now().date()
    #
    # # 获取昨天的日期
    # yesterday = today - datetime.timedelta(days=1)
    # # 获取昨天的开始日期
    # yesterday_start = datetime.datetime.combine(yesterday, datetime.datetime.min.time())
    # # 获取今天的结束日期
    # today_end = datetime.datetime.combine(today, datetime.datetime.max.time())
    try:
        # 查询站点名称
        station_data = MaterStation.objects.get(id=station_id, is_delete=0)
        if not station_data:
            return False
    except Exception as e:
        error_log.error(e)
        return False

    # 执行SQL查询
    sql = """SELECT count(*)
                    FROM {}
                    WHERE station='{}'
                    and time BETWEEN '{}' AND '{}' 
                    and (epae!=0 or enae!=0) 
                    GROUP BY time 
                    ORDER BY time ASC
                    """.format(
        'ads_report_loading_chag_data', station_data.english_name, start_time, end_time + " 23:59:59"
    )

    with connections['doris_ads_rhyc'].cursor() as ads_cursor:
        try:
            # 获取查询结果
            ads_cursor.execute(sql)
            result = ads_cursor.fetchone()
        except Exception as e:
            error_log.error(e)
            return True

    if result:
        # 有值
        return True
    else:
        return False


def is_over_by_days(begin_time_str, end_time_str, days):
    # 解析日期字符串
    begin_time = datetime.datetime.strptime(begin_time_str, '%Y-%m-%d').date()
    end_time = datetime.datetime.strptime(end_time_str, '%Y-%m-%d').date()

    # 计算时间差
    delta = (end_time - begin_time).days
    if delta > int(days):
        return True
    return False


def judge_is_date(judge_time, time_format='%Y/%m/%d %H:%M'):
    """
    判断日期格式是否正确
    :param time_format:
    :param judge_time:
    :return:
    """
    try:
        # 尝试将字符串转换为 datetime 对象
        datetime.datetime.strptime(str(judge_time).strip(), time_format)
        return True
    except ValueError as e:
        logging.error(e)
        return False


def create_date_time_mapping(date_time, m=15):
    """
    构造时间字典：默认15分钟间隔
    """
    res = {}
    start_time = datetime.datetime.strptime(str(date_time) + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
    total = 288 if m == 5 else 96  # 5分钟或15分钟
    for i in range(total):
        t = start_time + datetime.timedelta(minutes=i * m)
        t = t.strftime('%Y-%m-%d %H:%M:%S')
        res[f'{t}'] = '--'
    return res


def get_all_date_between(begin_time, end_time):
    """
    获取两个日期间的所有日期
    """
    start_date = datetime.datetime.strptime(begin_time, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_time, '%Y-%m-%d')

    # 计算日期范围内的所有日期
    date_range = [start_date + datetime.timedelta(days=i) for i in range((end_date - start_date).days + 1)]

    # 将这些日期存储在一个变量中
    total_dates = [date.strftime('%Y-%m-%d') for date in date_range]
    return total_dates


def get_redis_strategy_data(one_date, conn, english_name, now_strategy_value, rated_power):
    """
    从redis中获取当前策略
    """
    the_month = str(int(one_date[5:7]))
    datas = conn.get('{}-{}-mqtt'.format(english_name, the_month))
    # 当前策略为空 需要召唤策略 从redis中获取，一个月只有一个值
    if datas:
        datas = eval(datas)
        datas = datas.get('body')[0].get('body')
        count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
        for y in [24, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
                  23]:
            _hour = y + 24 if count == 2 else y
            now_strategy_value.extend([
                Decimal((datas.get(f'M{the_month}H{y}P', 0))) * Decimal(
                    datas.get(f'M{the_month}H{y}F', 0)) * Decimal(rated_power),
                Decimal((datas.get(f'M{the_month}H{y}P', 0))) * Decimal(
                    datas.get(f'M{the_month}H{y}F', 0)) * Decimal(rated_power),
                Decimal((datas.get(f'M{the_month}H{_hour}P', 0))) * Decimal(
                    datas.get(f'M{the_month}H{_hour}F', 0)) * Decimal(rated_power),
                Decimal((datas.get(f'M{the_month}H{_hour}P', 0))) * Decimal(
                    datas.get(f'M{the_month}H{_hour}F', 0)) * Decimal(rated_power),
            ])
    else:
        now_strategy_value.extend(['--'] * 96)


def transpose_2d_list(matrix):
    """
    转换为新形式的二维列表
    """
    return [list(t) for t in zip(*matrix)]


def get_best_model_id(target_id, mstation_id, start_time, end_time):
    """
    获取预测准确率最高的模型
    """
    # 查询所有符合条件的model_id
    model_ids = models.DictModel.objects.filter(is_active=1, is_use=1).values_list('id', flat=True)

    in_clause = ', '.join(map(str, model_ids))
    # 先看时间段内是否有准确率数据
    sql_rate = """SELECT count(id)
                            FROM dwd_dict_model_rate
                            WHERE target_id = {}
                              AND mstation_id = {}
                              AND forecast_time BETWEEN '{}' AND '{}'
                              AND is_use = 1
                              AND model_id IN ({})""".format(target_id, mstation_id, start_time, end_time, in_clause)

    queryset = None

    with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
        try:
            dwd_cursor.execute(sql_rate)
            rate_res = dwd_cursor.fetchone()
            if rate_res[0]:
                sql = """WITH filtered_data AS (
                                SELECT *
                                FROM dwd_dict_model_rate
                                WHERE target_id = {}
                                  AND mstation_id = {}
                                  AND forecast_time BETWEEN '{}' AND '{}'
                                  AND is_use = 1
                                  AND model_id IN ({})
                            )
                            SELECT model_id, AVG(rate) AS avg_rate
                            FROM filtered_data
                            GROUP BY model_id
                            ORDER BY avg_rate DESC
                            LIMIT 1;""".format(target_id, mstation_id, start_time, end_time, in_clause)
            else:
                sql = """SELECT model_id, rate AS avg_rate
                                FROM dwd_dict_model_rate
                                WHERE target_id = {}
                                  AND mstation_id = {}
                                  AND is_use = 1
                                  AND model_id IN ({})
                                ORDER BY forecast_time DESC, rate DESC
                                LIMIT 1;""".format(target_id, mstation_id, in_clause)
            # 获取查询结果
            dwd_cursor.execute(sql)
            queryset = dwd_cursor.fetchone()
        except Exception as e:
            error_log.error(e)
            return None, target_id

    # 获取结果
    if queryset:
        best_model_id = queryset[0]
    else:
        best_model_id = None
    keys_list = list(FORECASTING_MODEL_ID_MAPPING.keys())
    if str(best_model_id) in keys_list:
        return int(FORECASTING_MODEL_ID_MAPPING[str(best_model_id)]), 1
    # 因为目前王玲霞三个模型还没有接入推荐策略 所以所有推荐策略都是小丽模型的 后期接入后放开这里：
    # return None
    return best_model_id, 20


def creat_recommend_strategy_data(date_data, mstation_id, rated_power, target_id, best_target_id):
    """
    创建推荐策略(根据日期)
    """
    model_id = date_data['best_model_id']
    select_date = date_data['select_date']

    # strategy_list = models.ModelForecastValue.objects.filter(model=model_id, target=1, mstation=mstation_id,
    #                                                          forecast_time=select_date)
    # strategy_list = strategy_list.values_list(
    #     "forecast_hour_min",
    #     "value"
    # ).order_by('forecast_hour_min')

    sql_condition = "target_id={} AND model_id = {} AND mstation_id={} AND forecast_time = '{}' AND is_use=1 order by forecast_hour_min asc".format(
        best_target_id, model_id, int(mstation_id), select_date)

    strategy_list = get_power_load_forecast_data('forecast_hour_min, value', sql_condition, 'dwd_model_forecast_value')

    if len(strategy_list) == 0:
        return False, False
    forecast_result_dict = {
        f"{select_date} {(datetime.datetime.strptime(forecast_hour_min[0:5], '%H:%M')).strftime('%H:%M')}:00": value
        for forecast_hour_min, value in strategy_list
    }

    # if not strategy_list:
    #     return False, False
    # 将查询结果转换为字典形式
    # forecast_result_dict = {
    #     f"{select_date} {forecast_hour_min.strftime('%H:%M:%S')}": value
    #     for forecast_hour_min, value in strategy_list
    # }


    result = {}
    if len(forecast_result_dict) != 96:
        # 不是96个值 则需要补全
        date_for_15_min = create_date_time_mapping(select_date, 15)
        for key in date_for_15_min.keys():
            if key in forecast_result_dict:
                result[key] = forecast_result_dict[key]
            else:
                nearest_key = find_nearest_key(key, forecast_result_dict.keys())
                result[key] = forecast_result_dict[nearest_key]
    else:
        result = forecast_result_dict
    charge_config = []
    rl_list = []
    # 获取并网点额定功率和
    total_rated_power = StationDetails.objects.filter(master_station_id=mstation_id
    ).aggregate(
        total_rated_power=Sum('rated_power')
    )['total_rated_power']

    for item in result.values():
        rl = int((abs(item) / float(total_rated_power))*100)
        if rl > 100:
            rl = 100
        rl_list.append(rl)
        # 判断充放电
        if Decimal(item) > Decimal(0):
            charge_config.append(1)
        elif Decimal(item) < Decimal(0):
            charge_config.append(-1)
        else:
            charge_config.append(0)
    return charge_config, rl_list


def add_recommend_strategy(strategy, name, charge_config, rl_list, pv_list, month, lang='zh'):
    """
    保存推荐策略
    """
    category_obj = UserStrategyCategoryNew.objects.create(strategy=strategy, name=name,
                                                          en_name=name,
                                                          charge_config=str(charge_config),
                                                          is_follow=1,
                                                          rl_list=str(rl_list),
                                                          pv_list=str(pv_list),
                                                          remark='生成推荐策略生成',
                                                          en_remark='Generate recommendation strategy')

    # 异步翻译
    pdr_data = {'id': category_obj.id,
                'table': 't_user_strategy_category_new',
                'update_data': {'name': name}}

    pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
    redis_pool.publish(pub_name, json.dumps(pdr_data))

    # 保存月份
    Month.objects.create(strategy=strategy, month_number=month, is_valid=False,
                         user_Strategy_Category=category_obj)


def str_to_datetime(s):
    """
    将字符串时间转换为 datetime 对象
    """
    return datetime.datetime.strptime(s, '%Y-%m-%d %H:%M:%S')


def datetime_to_str(dt):
    """
    将 datetime 对象转换为字符串
    """
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def find_nearest_key(key, keys):
    """
    找到最近的值
    """
    key_dt = str_to_datetime(key)
    nearest_key = None
    min_diff = float('inf')  # 初始化为无穷大
    for k in keys:
        k_dt = str_to_datetime(k)
        diff = abs((k_dt - key_dt).total_seconds())  # 计算时间差的秒数
        if diff < min_diff:
            min_diff = diff
            nearest_key = k
    return nearest_key


def get_present_strategy_data(data_by_date, one_date, conn, rated_data, now_strategy_value):
    """
    获取指定日期当前策略
    """
    if data_by_date:
        if data_by_date['rlh1p'] is None:
            get_redis_strategy_data(one_date, conn, rated_data.english_name, now_strategy_value,
                                    rated_data.rated_power)

        if data_by_date['rlh1p'] is not None and data_by_date['rlh25p'] is not None:
            # 此时有48个值，只需要补齐15分钟的数据
            for i in range(24):
                if i == 0:
                    # 计算出0点的值，则0点和0点15都是这个值
                    rlh24f = data_by_date['rlh24f'] if data_by_date['rlh24f'] else 0
                    rlh24p = data_by_date['rlh24p'] if data_by_date['rlh24p'] else 0
                    rlh48f = data_by_date['rlh48f'] if data_by_date['rlh48f'] else 0
                    rlh48p = data_by_date['rlh48p'] if data_by_date['rlh48p'] else 0
                    strategy_value = Decimal(rlh24f) * Decimal(rlh24p) * Decimal(rated_data.rated_power)
                    now_strategy_value.append(strategy_value)  # 0点
                    now_strategy_value.append(strategy_value)  # 15分
                    # 计算半点 和 45的值
                    strategy_value_a = Decimal(rlh48f) * Decimal(rlh48p) * Decimal(rated_data.rated_power)
                    now_strategy_value.append(strategy_value_a)  # 30分
                    now_strategy_value.append(strategy_value_a)  # 45分
                else:
                    whole_f_key = 'rlh' + str(i) + 'f'
                    whole_p_key = 'rlh' + str(i) + 'p'
                    half_f_key = 'rlh' + str(i + 24) + 'f'
                    half_p_key = 'rlh' + str(i + 24) + 'p'
                    whole_f_val = data_by_date[whole_f_key] if data_by_date[whole_f_key] else 0
                    whole_p_val = data_by_date[whole_p_key] if data_by_date[whole_p_key] else 0
                    half_f_val = data_by_date[half_f_key] if data_by_date[half_f_key] else 0
                    half_p_val = data_by_date[half_p_key] if data_by_date[half_p_key] else 0

                    strategy_value = (Decimal(whole_f_val) * Decimal(whole_p_val)
                                      * Decimal(rated_data.rated_power))
                    strategy_value_a = (Decimal(half_f_val) * Decimal(half_p_val)
                                        * Decimal(rated_data.rated_power))
                    now_strategy_value.extend(
                        [strategy_value, strategy_value, strategy_value_a, strategy_value_a])
        if data_by_date['rlh1p'] is not None and data_by_date['rlh25p'] is None:
            # 此时只有24个值 需要补齐15分 30分 45分的值
            for i in range(24):
                if i == 0:
                    # 计算出0点的值，则0点和0点15、30、45都是这个值
                    rlh24f = data_by_date['rlh24f'] if data_by_date['rlh24f'] else 0
                    rlh24p = data_by_date['rlh24p'] if data_by_date['rlh24p'] else 0
                    strategy_value = Decimal(rlh24f) * Decimal(rlh24p) * Decimal(rated_data.rated_power)
                    now_strategy_value.extend([strategy_value, strategy_value, strategy_value, strategy_value])
                else:
                    whole_f_key = 'rlh' + str(i) + 'f'
                    whole_p_key = 'rlh' + str(i) + 'p'
                    whole_f_val = data_by_date[whole_f_key] if data_by_date[whole_f_key] else 0
                    whole_p_val = data_by_date[whole_p_key] if data_by_date[whole_p_key] else 0
                    strategy_value = (Decimal(whole_f_val) * Decimal(whole_p_val) *
                                      Decimal(rated_data.rated_power))
                    now_strategy_value.extend(
                        [strategy_value, strategy_value, strategy_value, strategy_value])
    else:
        get_redis_strategy_data(one_date, conn, rated_data.english_name, now_strategy_value,
                                rated_data.rated_power)


def get_power_load_forecast_data(field_name_str, sql_condition, table_name):
    """
    创建查询doris下的ads_rhyc库下的数据sql并返回结果
    """
    # 执行SQL查询
    sql = """SELECT {}
                        FROM {}
                        WHERE {}
                        """.format(
        field_name_str,table_name, sql_condition
    )
    result = []

    with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
        try:
            # 获取查询结果
            dwd_cursor.execute(sql)
            result = dwd_cursor.fetchall()
            return result
        except Exception as e:
            error_log.error(e)
    return result


def get_all_model_names():
    """
    获取所有模型名称 返回需要格式的数据
    """
    # 查询数据
    queryset = models.DictModel.objects.all()

    # 将查询结果转换为列表
    rows = list(queryset.values('id', 'name'))

    # 生成所需的列表
    data = {row['id']: row['name'] for row in rows}
    return data


def get_all_target_names():
    """
    获取所有指标名称 返回需要格式的数据
    """
    # 查询数据
    queryset = models.DictModelTarget.objects.all()

    # 将查询结果转换为列表
    rows = list(queryset.values('id', 'name', 'en_name'))

    # 生成所需的列表
    data = {row['id']: {'zh': row['name'], 'en': row['en_name']} for row in rows}
    return data


def get_station_data():
    queryset = MaterStation.objects.filter(is_delete=0).all()
    # 将查询结果转换为列表
    rows = list(queryset.values('id', 'name', 'project__name'))

    # 生成所需的列表
    data = {row['id']: [row['name'], row['project__name']] for row in rows}
    return data


class PowerLoadForecastingView(APIView):
    """
    负荷预测接口类
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        lang = request.headers.get("lang", 'zh')
        mstation_id = request.query_params.get("mstation_id", None)  # 并网点id
        target_id = request.query_params.get("target_id", 1)  # 预测指标
        start_time = request.query_params.get('start_time')  # 开始时间
        end_time = request.query_params.get('end_time')  # 结束时间
        model_ids = request.query_params.get('model_ids')  # 模型id集合
        download_status = request.query_params.get('download_status', 0)  # 1下载
        if not mstation_id or not target_id or not start_time or not end_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败！" if lang == 'zh' else 'Field verification failed.'},
                }
            )
        target = models.DictModelTarget.objects.filter(id=int(target_id), is_use=1).first()
        if not target:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "指标不存在！" if lang == 'zh' else 'Target does not exist.'},
                }
            )
        if not judge_is_date(start_time, '%Y-%m-%d') or not judge_is_date(end_time, '%Y-%m-%d'):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "时间格式错误！" if lang == 'zh' else 'Time format error.'},
                }
            )
        target_name = target.name if lang == 'zh' else target.en_name
        if int(target_id) != 3:
            if not model_ids:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "字段校验失败！" if lang == 'zh' else 'Field verification failed.'},
                    }
                )

            if not can_be_parsed_as_list(model_ids):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "模型传参错误" if lang == 'zh' else 'Model parameter error.'},
                    }
                )
            model_ids = json.loads(model_ids)

        if int(target_id) == 3 and is_over_by_days(start_time, end_time, 31):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "请将时间范围控制在31天以内！" if lang == 'zh' else
                    'Please control the time range to be within 31 days.'},
                }
            )
        else:
            if is_over_by_days(start_time, end_time, 90):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "请将时间范围控制在90天以内！" if lang == 'zh' else
                        'Please control the time range to be within 90 days.'},
                    }
                )
        try:
            station_data = MaterStation.objects.get(id=mstation_id, is_delete=0)
        except Exception as e:
            transaction.rollback()
            error_log.error("该并网点不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "该并网点不存在" if lang == 'zh' else 'The station does not exist.'},
                }
            )
        # 获取所有日期时间列表
        total_dates = get_all_date_between(start_time, end_time)
        all_date_arr = {}
        for item in total_dates:
            date_arr = create_date_time_mapping(item)
            all_date_arr = {**all_date_arr, **date_arr}
        data = {}
        data['time'] = all_date_arr.keys()
        data['measured_value'] = []
        station_name = station_data.english_name
        if int(target_id) == 1:
            # 获取负荷实测值
            load_true_data = self._get_load_true_data(station_name, start_time, end_time)
            # if not load_true_data:
            #     return Response(
            #         {
            #             "code": common_response_code.ERROR,
            #             "data": {"message": "error", "detail": "查询失败！"},
            #         }
            #     )
            result = load_true_data
            # 转换查询结果为字典
            result_dict = dict(result)
            merged_dict = {k: result_dict.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
            measurement_list = list(map(lambda item: item[1], merged_dict.items()))
            if len(measurement_list) > 0:
                data['measured_value'] = measurement_list  # 负荷实测值
            else:
                data['measured_value'] = all_date_arr.values()
        elif int(target_id) == 2:
            # 获取电量实测值
            chag_disg_data = self._get_chag_disg_data(station_name, start_time, end_time)
            # if not chag_disg_data:
                # return Response(
                #     {
                #         "code": common_response_code.ERROR,
                #         "data": {"message": "error", "detail": "查询失败！"},
                #     }
                # )
            result = chag_disg_data
            # 转换查询结果为字典
            result_dict = dict(result)
            merged_dict = {k: result_dict.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
            measurement_list = list(map(lambda item: item[1], merged_dict.items()))
            if len(measurement_list) > 0:
                data['measured_value'] = measurement_list  # 电量实测值
            else:
                data['measured_value'] = all_date_arr.values()
        elif int(target_id) == 3:
            # 获取负荷实测值
            load_true_data = self._get_load_true_data(station_name, start_time, end_time)
            # if not load_true_data:
            #     return Response(
            #         {
            #             "code": common_response_code.ERROR,
            #             "data": {"message": "error", "detail": "查询失败！"},
            #         }
            #     )
            result = load_true_data
            # 转换查询结果为字典
            result_dict = dict(result)
            merged_dict = {k: result_dict.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
            measurement_list = list(map(lambda item: item[1], merged_dict.items()))
            if len(measurement_list) > 0:
                data['measured_value'] = measurement_list  # 负荷实测值
            else:
                data['measured_value'] = all_date_arr.values()

            # 获取最大需量关联的模型id
            model_ids = models.DictTargetModel.objects.filter(
                target=int(target_id), target__name='最大需量').values('model_id').first()
            if model_ids:
                model_ids = [model_ids['model_id']]
            else:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "该指标暂不支持！" if lang == 'zh' else 'The indicator does not support.'},
                    }
                )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "该指标暂不支持！" if lang == 'zh' else 'The indicator does not support.'},
                }
            )

        # 根据指标和模型获取预测值
        # models_lists = models.ModelForecastValue.objects.filter(target=int(target_id), model__in=model_ids,
        #                                                         mstation=int(mstation_id))
        # models_lists = models_lists.filter(forecast_time__range=(start_time, end_time))
        # models_lists = models_lists.values_list(
        #     "forecast_time",
        #     "forecast_hour_min",
        #     "value",
        #     "model__name"
        # ).order_by('forecast_time', 'forecast_hour_min')

        # 获取所有模型名称数据
        model_names = get_all_model_names()

        # 构建 IN 条件
        in_clause = ', '.join(map(str, model_ids))
        sql_condition = "target_id={} AND model_id in ({}) AND forecast_time BETWEEN '{}' AND '{}' AND mstation_id={} AND is_use=1 ORDER BY {}".format(target_id, in_clause, start_time, end_time + " 23:59:59", mstation_id, 'forecast_time asc, forecast_hour_min asc')

        models_lists = get_power_load_forecast_data('forecast_time, forecast_hour_min, value, model_id',
                                     sql_condition, 'dwd_model_forecast_value')
        # 将查询结果转换为字典形式
        forecast_result_dict = {
            f"{model_names[model_id]} {forecast_time} {(datetime.datetime.strptime(forecast_hour_min[0:5], '%H:%M')).strftime('%H:%M')}": value
            for forecast_time, forecast_hour_min, value, model_id in models_lists
        }

        # 将查询结果转换为字典形式
        # forecast_result_dict = {
        #     f"{model__name} {forecast_time} {forecast_hour_min.strftime('%H:%M')}": value
        #     for forecast_time, forecast_hour_min, value, model__name in models_lists
        # }
        # print(forecast_result_dict)

        # 使用字典推导式按名称分组
        grouped_data = {
            name: {key[len(name) + 1:]: value for key, value in forecast_result_dict.items() if key.startswith(name)}
            for name in set(key.split()[0] for key in forecast_result_dict.keys())
        }

        # 构建最终结果列表
        model_group_result = [
            {"name": name, "data": data}
            for name, data in grouped_data.items()
        ]
        for item in model_group_result:
            item_data = item['data']
            merged_dict = {k: item_data.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
            value_list = list(map(lambda item: item[1], merged_dict.items()))
            if len(value_list) > 0:
                data[item['name']] = value_list
            else:
                data[item['name']] = all_date_arr
        # 获取所有选择的模型名称
        results = models.DictModel.objects.filter(id__in=model_ids, is_use=1)

        for result in results:
            if result.name not in data:
                data[result.name] = all_date_arr.values()
        if int(download_status) == 1:
            # 下载导出数据
            hebing_title = [''] * len(model_ids)
            # file_name = '预测.xlsx'
            if target_name == "电量" or target_name == "Electricity level":
                new_list = []
                title_list = [station_data.name + target_name + "预测", ''] if lang == 'zh' else [station_data.name + ' ' + target_name + " forecast", '']
                title_list.extend(hebing_title)
                data_list = []
                data_list.append(title_list)
                excel_time = list(data['time'])
                excel_time.insert(0, '时间' if lang == 'zh' else 'Time')
                excel_measured = list(data['measured_value'])
                excel_measured.insert(0, (target_name+'实测值'+'(kWh)') if lang == 'zh' else target_name + ' measured Value(kWh)')
                new_list.append(excel_time)
                new_list.append(excel_measured)
                for result in results:
                    name_data = list(data[result.name])
                    name_data.insert(0, (result.name + '预测值(kWh)') if lang == 'zh' else (result.name + ' predictive Value(kWh)'))
                    new_list.append(name_data)
                transposed_list = transpose_2d_list(new_list)
                data_list.extend(transposed_list)
                if lang == 'zh':
                    file_name = station_data.name + start_time + '到' + end_time + target_name + '预测.xlsx'
                else:
                    file_name = station_data.name + ' ' + start_time + '~' + end_time + ' ' + target_name +' forecast.xlsx'
            elif target_name == "功率" or target_name == "Power":
                new_list = []
                title_list = [station_data.name + target_name + "预测", ''] if lang == 'zh' else [station_data.name + ' ' + target_name + " forecast", '']
                title_list.extend(hebing_title)
                data_list = []
                data_list.append(title_list)
                excel_time = list(data['time'])
                excel_time.insert(0, '时间' if lang == 'zh' else 'Time')
                excel_measured = list(data['measured_value'])
                excel_measured.insert(0, ('负荷实测值' + '(kW)') if lang == 'zh' else 'Load measured value(kW)')
                new_list.append(excel_time)
                new_list.append(excel_measured)
                for result in results:
                    name_data = list(data[result.name])
                    name_data.insert(0, (result.name + '预测值(kW)') if lang == 'zh' else (result.name + ' predictive value(kW)') )
                    new_list.append(name_data)
                transposed_list = transpose_2d_list(new_list)
                data_list.extend(transposed_list)

                if lang == 'zh':
                    file_name = station_data.name + start_time + '到' + end_time + target_name + '预测.xlsx'
                else:
                    file_name = station_data.name + ' ' + start_time + '~' + end_time + ' ' + target_name + ' forecast.xlsx'
            elif target_name == "最大需量" or target_name == "Maximum demand":
                new_list = []
                title_list = [station_data.name + target_name + "预测", ''] if lang == 'zh' else [station_data.name + ' ' + target_name + " forecast", '']
                title_list.extend(hebing_title)
                data_list = []
                data_list.append(title_list)
                excel_time = list(data['time'])
                excel_time.insert(0, '时间' if lang == 'zh' else 'Time')
                excel_measured = list(data['measured_value'])
                excel_measured.insert(0, '负荷实测值(kW)' if lang == 'zh' else 'Load measured value(kW)')
                new_list.append(excel_time)
                new_list.append(excel_measured)
                for result in results:
                    name_data = list(data[result.name])
                    name_data.insert(0, '需量预测值(kW)' if lang == 'zh' else 'Demand predictive value(kW)')
                    new_list.append(name_data)
                transposed_list = transpose_2d_list(new_list)
                data_list.extend(transposed_list)

                if lang == 'zh':
                    file_name = station_data.name + start_time[:7] + target_name + '预测.xlsx'
                else:
                    file_name = station_data.name + ' ' + start_time[:7] + ' ' + target_name + ' forecast.xlsx'
            else:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "该指标暂不支持！" if lang == 'zh' else "This indicator does not support!"},
                    }
                )
            excel_buffer = io.BytesIO()

            # 明确指定列名列表
            column_names = data_list[0]

            # 使用 data_list[2:] 作为数据，跳过第一行和第二行
            df = pd.DataFrame(data_list[1:], columns=column_names)

            # 将DataFrame写入Excel文件
            df.to_excel(excel_buffer, index=False)

            # 将BytesIO对象的位置重置到开始，以便从头读取数据
            excel_buffer.seek(0)

            # 加载Excel工作簿
            wb = load_workbook(filename=excel_buffer)

            # 获取活动工作表
            ws = wb.active

            # 合并第一行的所有单元格
            ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(df.columns))

            # 将修改后的Excel文件写回BytesIO对象
            excel_buffer = BytesIO()
            wb.save(excel_buffer)

            # 将BytesIO对象的位置重置到开始，以便从头读取数据
            excel_buffer.seek(0)

            # 将二进制数据转换为字节
            binary_data = excel_buffer.read()

            # 定义存储桶名称和对象名称（即文件名）
            bucket_name = 'rhyc'

            object_name = (file_name)

            # 调用upload_file方法上传Excel文件
            minio_client = MinioTool()
            storage_url = minio_client.upload_file(binary_data, bucket_name, object_name)
            storage_url = storage_url.split('?', 1)[0]
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": {'url': storage_url}}
                }
            )

        # 推荐策略数据获取
        if target_name != "最大需量" and target_name != "Maximum demand":
            # 先获取所选模型中准确率最高的模型出来，多个日期的则取平均值比较
            best_model_id, best_target_id = get_best_model_id(1, mstation_id, start_time, end_time)
            if best_model_id:
                # 从预测数据表中取出最好模型的数据
                sql_condition = ("target_id={} AND model_id = {} AND mstation_id={} AND is_use=1 and "
                                 "forecast_time >= '{}' and forecast_time <= '{}' ORDER BY {}").format(
                    best_target_id, best_model_id, int(mstation_id), start_time, end_time+" 23:59:59", 'forecast_time asc, forecast_hour_min asc')

                models_lists = get_power_load_forecast_data('forecast_time, forecast_hour_min, value',
                                                            sql_condition, 'dwd_model_forecast_value')
                if len(models_lists)<=0:  # 没有推荐策略默认查XGBoost-Prophet得推荐策略
                    sql_condition = ("target_id=20 AND model_id = 3 AND mstation_id={} AND is_use=1 and "
                                 "forecast_time >= '{}' and forecast_time <= '{}' ORDER BY {}").format(
                    int(mstation_id), start_time, end_time+" 23:59:59", 'forecast_time asc, forecast_hour_min asc')

                    models_lists = get_power_load_forecast_data('forecast_time, forecast_hour_min, value',
                                                            sql_condition, 'dwd_model_forecast_value')
                # 将查询结果转换为字典形式
                forecast_result_dict = {
                    f"{forecast_time} {(datetime.datetime.strptime(forecast_hour_min[0:5], '%H:%M')).strftime('%H:%M')}": f"{value:.2f}"
                    for forecast_time, forecast_hour_min, value in models_lists
                }

                # 将查询结果转换为字典形式

                merged_dict = {k: forecast_result_dict.get(k[0:16], all_date_arr[k]) for k in all_date_arr}
                value_list = list(map(lambda item: item[1], merged_dict.items()))
                if len(value_list) > 0:
                    data['recommend_strategy_value'] = value_list
                else:
                    data['recommend_strategy_value'] = all_date_arr.values()
            else:
                data['recommend_strategy_value'] = all_date_arr.values()
            # 获取当前策略信息
            now_strategy_value = []
            # 获取t_stations表的rated_power额定功率值
            master_station = models.MaterStation.objects.get(id=int(mstation_id), is_delete=0)
            rated_data = master_station.stationdetails_set.filter(english_name=master_station.english_name).first()
            # rated_data = StationDetails.objects.get(master_station=int(mstation_id))
            conn = get_redis_connection("3")
            # 查出选择时间范围内每天的最后一条记录
            sql_strategy = """
                            WITH daily_last_rows AS (
                                    SELECT
                                        DATE_FORMAT(time, '%Y-%m-%d') as day_date, time, station_name, device, slave, pack, type, ots, rlh1p, rlh2p, rlh3p, rlh4p, rlh5p, rlh6p, rlh7p, rlh8p, rlh9p, 
                                            rlh10p, rlh11p, rlh12p, rlh13p, rlh14p, rlh15p, rlh16p, rlh17p, rlh18p, rlh19p, rlh20p, rlh21p, rlh22p, rlh23p, rlh24p,
                                             rlh1f, rlh2f, rlh3f, rlh4f, rlh5f, rlh6f, rlh7f, rlh8f, rlh9f, rlh10f, rlh11f, rlh12f, rlh13f, rlh14f, rlh15f, rlh16f, 
                                             rlh17f, rlh18f, rlh19f, rlh20f, rlh21f, rlh22f, rlh23f, rlh24f, rlh25p, rlh26p, rlh27p, rlh28p, rlh29p, rlh30p, rlh31p, 
                                             rlh32p, rlh33p, rlh34p, rlh35p, rlh36p, rlh37p, rlh38p, rlh39p, rlh40p, rlh41p, rlh42p, rlh43p, rlh44p, rlh45p, rlh46p,
                                              rlh47p, rlh48p, rlh25f, rlh26f, rlh27f, rlh28f, rlh29f, rlh30f, rlh31f, rlh32f, rlh33f, rlh34f, rlh35f, rlh36f, rlh37f,
                                               rlh38f, rlh39f, rlh40f, rlh41f, rlh42f, rlh43f, rlh44f, rlh45f, rlh46f, rlh47f, rlh48f, 
                                        ROW_NUMBER() OVER (PARTITION BY DATE(time), station_name ORDER BY time DESC) AS rn
                                    FROM
                                        dwd_measure_ems_data_storage_tscale
                                    WHERE
                                        time BETWEEN '{}' AND '{}'
                                        AND station_name = '{}'
                                )
                                SELECT
                                    *
                                FROM
                                    daily_last_rows
                                WHERE
                                    rn = 1 ORDER BY day_date ASC;
                            """.format(start_time + ' 00:00:00', end_time + ' 23:59:59',
                                       station_name)
            with connections['doris_dwd_rhyc'].cursor() as dwd_cursor:
                try:
                    # 获取查询结果
                    dwd_cursor.execute(sql_strategy)
                    # 获取字段名
                    columns = [desc[0] for desc in dwd_cursor.description]
                    # 获取查询结果
                    result_strategy = dwd_cursor.fetchall()
                except Exception as e:
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！' if lang == 'zh' else 'Query failed.',
                            }
                        }
                    )
            # 将结果转换为字典
            result_dicts = []
            if result_strategy:
                result_dicts = [dict(zip(columns, row)) for row in result_strategy]
                date_dict = {d['day_date']: d for d in result_dicts}
                for one_date in total_dates:
                    data_by_date = date_dict.get(one_date)
                    get_present_strategy_data(data_by_date, one_date, conn, rated_data, now_strategy_value)
            else:
                for one_date in total_dates:
                    get_redis_strategy_data(one_date, conn, rated_data.english_name, now_strategy_value,
                                            rated_data.rated_power)
            data['now_strategy_value'] = now_strategy_value  # 当前策略储能功率

            # 根据时间区间获取预测模型有预测数据的起止时间作为生成策略时可选择的时间范围
            # queryset = models.ModelForecastValue.objects.filter(
            #     target=1,
            #     mstation=mstation_id,
            #     model__in=model_ids,
            #     forecast_time__range=(start_time, end_time)
            # ).aggregate(
            #     min_forecast_time=Min('forecast_time'),
            #     max_forecast_time=Max('forecast_time')
            # )

            in_clause = ', '.join(map(str, model_ids))
            sql_condition = "target_id=1 AND is_use=1 AND model_id in ({}) AND mstation_id={} AND forecast_time between '{}' and '{}'".format(
                in_clause, int(mstation_id), start_time, end_time)

            queryset = get_power_load_forecast_data('min(forecast_time) as min_forecast_time, max(forecast_time) as max_forecast_time',
                                                        sql_condition, 'dwd_model_forecast_value')

            data['forecast'] = {}
            if len(queryset)>0:
                data['forecast']['start_date'] = queryset[0][0].strftime("%Y-%m-%d") if queryset[0][0] else ""
                data['forecast']['end_date'] = queryset[0][1].strftime("%Y-%m-%d") if queryset[0][1] else ""
            else:
                data['forecast']['start_date'] = ""
                data['forecast']['end_date'] = ""

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": data}
            }
        )


    def _get_load_true_data(self, station_name, start_time, end_time):
        """
        获取负荷实测值
        """
        sql = """
                    SELECT DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, p_load
                    FROM ads_report_loading_data
                    WHERE time  BETWEEN '{}'
                    AND '{}' 
                    AND state_pcc = 0
                    AND station = '{}' order by time asc
                    """.format(start_time + ' 00:00:00', end_time + ' 23:59:59', station_name)

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                # 获取查询结果
                ads_cursor.execute(sql)
                result = ads_cursor.fetchall()
                return result
            except Exception as e:
                error_log.error(e)
                return False


    def _get_chag_disg_data(self, station_name, start_time, end_time):
        # 获取电量实测值
        sql = """
                                SELECT DATE_FORMAT(time, '%Y-%m-%d %H:%i') AS time, diff
                                FROM ads_report_loading_chag_data
                                WHERE time BETWEEN '{}'
                                AND '{}' 
                                AND station = '{}' order by time asc
                                """.format(start_time, end_time + " 23:59:59", station_name)

        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            try:
                # 获取查询结果
                ads_cursor.execute(sql)
                result = ads_cursor.fetchall()
                return result
            except Exception as e:
                error_log.error(e)
                return False


class PowerLoadForecastingModelView(APIView):
    """
    负荷预测模型管理接口类
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request):
        """
        负荷预测模型列表
        """
        lang = request.headers.get("lang", "zh")
        try:
            search_ids = request.query_params.get("search_ids", None)
            page = int(request.query_params.get("page", 1))
            page_size = int(request.query_params.get("page_size", 10))
            if search_ids:
                if not can_be_parsed_as_list(search_ids):
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field check failed.'},
                        }
                    )

            # target_names_q = Q(name__in=['功率', '电量'])
            target_names_q = Q(name__icontains='功率') | Q(name__icontains='电量')

            # 查询符合条件的数据
            models_lists = models.DictModel.objects.filter(
                id__in=models.DictTargetModel.objects.filter(
                    target_id__in=models.DictModelTarget.objects.filter(target_names_q)
                ).values_list('model_id', flat=True),
                is_use=1
            )

            # models_lists = models.DictModel.objects.filter(is_use=1, type=2)
            if search_ids:
                search_ids = json.loads(search_ids)
                models_lists = models_lists.filter(id__in=search_ids)
            models_lists = models_lists.values("id", "name", "rate", "is_active").order_by('-create_time').distinct()
            page_res = paging(page, page_size, models_lists)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": list(page_res.get('data'))},
                    "total": page_res.get('total'),
                    "totalpage": page_res.get('totalpage'),
                    "page": page_res.get('page'),
                    "page_size": page_size
                }
            )
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！" if lang == 'zh' else 'Query failed.'},
            })

    @transaction.atomic
    def put(self, request):
        """
        更新模型状态
        """
        lang = request.headers.get("lang", "zh")
        model_id = request.data.get("model_id", None)
        is_active = request.data.get("is_active", None)
        if not model_id or is_active is None:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field check failed.'},
                }
            )
        if not re.match(r"^-?\d+$", str(model_id)) or not re.match(r"^-?\d+$", str(is_active)):
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field check failed.'},
                }
            )
        try:
            model_res = models.DictModel.objects.get(id=int(model_id))
            model_res.is_active = int(is_active)
            model_res.save()
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "更新成功" if lang == 'zh' else 'Update success.',
                    },
                }
            )
        except Exception as e:
            error_log.error("预测模型:资源不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "模型不存在" if lang == 'zh' else 'Model does not exist.'},
                }
            )


class ForecastingAllModelRateView(APIView):
    """
    模型准确率查看
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        """
        模型准确率列表
        """
        lang = request.headers.get("lang", "zh")
        try:
            model_id = request.data.get("model_id", None)
            page = int(request.data.get("page", 1))
            page_size = int(request.data.get("page_size", 10))
            mstation_ids = request.data.get("mstation_ids", None)
            target_id = request.data.get("target_id", 1)
            begin_forecast_time = request.data.get("begin_forecast_time", None)
            end_forecast_time = request.data.get("end_forecast_time", None)

            if not model_id:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field check failed.'},
                    }
                )
            # models_lists = models.DictModelRate.objects.filter(is_use=1, model=model_id, target=int(target_id))
            sql_condition = "is_use=1 AND model_id={} AND target_id={}".format(model_id, target_id)

            if mstation_ids:
                if not can_be_parsed_as_list(mstation_ids):
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "并网点传参错误" if lang == 'zh' else 'Parameter is wrong.'},
                        }
                    )
                mstation_ids = json.loads(mstation_ids)
                in_clause = ', '.join(map(str, mstation_ids))
                sql_condition += " AND mstation_id in ({})".format(in_clause)
                # models_lists = models_lists.filter(mstation__in=mstation_ids)
            if not begin_forecast_time or not end_forecast_time:
                # 获取当天日期
                begin_forecast_time = datetime.datetime.now().strftime("%Y-%m-%d")
                end_forecast_time = datetime.datetime.now().strftime("%Y-%m-%d")
            # models_lists = models_lists.filter(forecast_time__range=(begin_forecast_time, end_forecast_time))
            offset = page_size * (page - 1)
            sql_count_condition = sql_condition + " AND forecast_time BETWEEN '{}' AND '{}'".format(begin_forecast_time, end_forecast_time)
            sql_condition += " AND forecast_time BETWEEN '{}' AND '{}' order by forecast_time asc limit {} OFFSET {}".format(begin_forecast_time, end_forecast_time, page_size, offset)
            # models_lists = models_lists.values(
            #     "id",
            #     "mstation_id",
            #     "forecast_time",
            #     "rate",
            #     "target__name",
            #     "mstation__name",
            #     "mstation__project__name"
            # ).order_by('-create_time')
            # page_res = paging(page, page_size, models_lists)

            target_name = get_all_target_names()
            station_data = get_station_data()
            data_lists = get_power_load_forecast_data('id, mstation_id,forecast_time, rate, target_id, mstation_id',
                                                        sql_condition, 'dwd_dict_model_rate')
            count_data = get_power_load_forecast_data('count(id) as count', sql_count_condition, 'dwd_dict_model_rate')
            detail = []
            for item in data_lists:
                detail.append(
                    {
                        "id": item[0],
                        "mstation_id": item[1],
                        "forecast_time": item[2],
                        "rate": item[3],
                        "target__name": target_name[item[4]][lang] if item[4] in target_name else '',
                        "mstation__name": station_data[item[5]][0] if item[5] in station_data else '',
                        "mstation__project__name": station_data[item[5]][1] if item[5] in station_data else '',
                    }
                )
            total_count = count_data[0][0] if count_data else 0
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": detail},
                    "total": total_count,
                    "totalpage": math.ceil(total_count / page_size),
                    "page": page,
                    "page_size": page_size
                }
            )
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！" if lang == 'zh' else 'Query failed.'},
            })


class ForecastingModelRateView(APIView):
    """
    模型准确率查看
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        """
        模型准确率导出
        """
        lang = request.headers.get("lang", "zh")
        model_id = request.data.get("model_id", None)
        mstation_ids = request.data.get("mstation_ids", None)
        target_id = request.data.get("target_id", 1)
        begin_forecast_time = request.data.get("begin_forecast_time", None)
        end_forecast_time = request.data.get("end_forecast_time", None)
        if not model_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'Parameter is wrong.'},
                }
            )
        # models_lists = models.DictModelRate.objects.filter(is_use=1, model=model_id, target=int(target_id))
        sql_condition = "is_use=1 AND model_id={} AND target_id={}".format(model_id, target_id)

        if mstation_ids:
            if not can_be_parsed_as_list(mstation_ids):
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "并网点传参错误" if lang == 'zh' else 'Parameter is wrong.'},
                    }
                )
            mstation_ids = json.loads(mstation_ids)
            in_clause = ', '.join(map(str, mstation_ids))
            sql_condition += " AND mstation_id in ({})".format(in_clause)
            # models_lists = models_lists.filter(mstation__in=mstation_ids)
        if not begin_forecast_time or not end_forecast_time:
            # 获取当天日期
            begin_forecast_time = datetime.datetime.now().strftime("%Y-%m-%d")
            end_forecast_time = datetime.datetime.now().strftime("%Y-%m-%d")
        # models_lists = models_lists.filter(forecast_time__range=(begin_forecast_time, end_forecast_time))
        # models_lists = models_lists.values(
        #     "id",
        #     "mstation_id",
        #     "forecast_time",
        #     "rate",
        #     "target__name",
        #     "mstation__name",
        #     "mstation__project__name"
        # ).order_by('-create_time')

        sql_condition += " AND forecast_time BETWEEN '{}' AND '{}' order by forecast_time asc".format(
            begin_forecast_time, end_forecast_time)

        # 获取模型数据
        try:
            model_record = models.DictModel.objects.get(id=model_id)
        except Exception as e:
            error_log.error("模型不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "模型不存在" if lang == 'zh' else 'Model does not exist.'},
                }
            )

        target_name = get_all_target_names()
        station_data = get_station_data()
        data_lists = get_power_load_forecast_data('id, mstation_id,forecast_time, rate, target_id, mstation_id',
                                                  sql_condition, 'dwd_dict_model_rate')
        models_lists = []
        for item in data_lists:
            models_lists.append(
                {
                    "id": item[0],
                    "mstation_id": item[1],
                    "forecast_time": item[2],
                    "rate": item[3],
                    "target__name": target_name[item[4]][lang] if item[4] in target_name else '',
                    "mstation__name": station_data[item[5]][0] if item[5] in station_data else '',
                    "mstation__project__name": station_data[item[5]][1] if item[5] in station_data else '',
                }
            )

        if lang == 'zh':
            data_list = [[model_record.name + "模型预测准确率", '', '', '', ''], ['预测指标', '项目名称', '并网点名称', '时间', '准确率（%）']]
        else:
            data_list = [[model_record.name + "Model prediction accuracy", '', '', '', ''], ['Prediction indicator', 'Project', 'Installation', 'Time', 'Accuracy(%)']]

        if models_lists:
            target_name = ''
            for item in list(models_lists):
                target_name = item['target__name']
                forecast_time = item['forecast_time'].strftime('%Y/%m/%d')
                data_list.append([item['target__name'], item['mstation__project__name'], item['mstation__name'],
                                  forecast_time, item['rate']])

            # 将数据转换为DataFrame

            # 创建一个BytesIO对象，用于保存Excel文件的二进制数据
            excel_buffer = io.BytesIO()

            # 明确指定列名列表
            column_names = data_list[0]

            # 使用 data_list[2:] 作为数据，跳过第一行和第二行
            df = pd.DataFrame(data_list[1:], columns=column_names)

            # 将DataFrame写入Excel文件
            df.to_excel(excel_buffer, index=False)

            # 将BytesIO对象的位置重置到开始，以便从头读取数据
            excel_buffer.seek(0)

            # 加载Excel工作簿
            wb = load_workbook(filename=excel_buffer)

            # 获取活动工作表
            ws = wb.active

            # 合并第一行的所有单元格
            ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(df.columns))

            # 将修改后的Excel文件写回BytesIO对象
            excel_buffer = BytesIO()
            wb.save(excel_buffer)

            # 将BytesIO对象的位置重置到开始，以便从头读取数据
            excel_buffer.seek(0)

            # 将二进制数据转换为字节
            binary_data = excel_buffer.read()

            # 定义存储桶名称和对象名称（即文件名）
            bucket_name = 'rhyc'

            if lang == 'zh':
                object_name = (model_record.name + str(begin_forecast_time.replace('-', '')) + "到" +
                               str(end_forecast_time.replace('-', '')) + target_name + "预测准确率" + ".xlsx")
            else:
                object_name = (model_record.name + ' ' + str(begin_forecast_time.replace('-', '')) + " to " +
                               str(end_forecast_time.replace('-', '')) + ' ' + target_name + " prediction accuracy" + ".xlsx")

            # 调用upload_file方法上传Excel文件
            minio_client = MinioTool()
            storage_url = minio_client.upload_file(binary_data, bucket_name, object_name)
            storage_url = storage_url.split('?', 1)[0]
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "down_url": storage_url},
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "没有数据可导出" if lang == 'zh' else 'No data to export.'},
                }
            )


class ModelTargetView(APIView):
    """
    模型准确率查看
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        """
        根据模型获取关联的预测指标列表
        """
        lang = request.headers.get("lang", 'zh')
        try:
            page_type = request.query_params.get("page_type", 1)
            if not page_type:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field verification failed.'},
                    }
                )
            models_lists = None
            if int(page_type) == 2:
                models_lists = models.DictTargetModel.objects.filter(target__is_use=1)
                model_id = request.query_params.get("model_id", None)
                if not model_id:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field verification failed.'},
                        }
                    )
                # 负荷预测模型管理页面的
                models_lists = models_lists.filter(model_id=model_id)
                models_lists = models_lists.exclude(target__name="最大需量")
                models_lists = models_lists.values(
                    "target__name",
                    "target__en_name",
                    "target__id",
                )
            if int(page_type) == 1:
                mstation_id = request.query_params.get("mstation_id", None)
                start_time = request.query_params.get('start_time')  # 开始时间
                end_time = request.query_params.get('end_time')  # 结束时间
                if not mstation_id or not start_time or not end_time:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field verification failed.'},
                        }
                    )
                models_lists = models.DictModelTarget.objects.filter(type=2, is_use=1)
                # 负荷预测页面 需要判断是否显示电量选项
                if not get_is_have_data(mstation_id, start_time, end_time):
                    models_lists = models_lists.exclude(name="电量")
                models_lists = models_lists.values(
                    "name",
                    "en_name",
                    "id",
                )

            if not models_lists:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {"message": "success", "detail": []},
                    }
                )

            if lang == 'en':
                for i in models_lists:
                    i['name'] = i['en_name']

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": list(models_lists)}
                }
            )
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！" if lang == 'zh' else 'Query failed.'},
            })


class CreatStrategyView(APIView):
    """
    生成策略
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    @transaction.atomic
    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        mstation_id = request.data.get("mstation_id", None)
        target_id = request.data.get("target_id", None)
        dates = request.data.get("dates", None)
        forecast_name = request.data.get("forecast_name", None)
        if not mstation_id or not target_id or not forecast_name or not dates:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "字段校验失败！" if lang == 'zh' else 'Field verification failed.'},
                }
            )
        target = models.DictModelTarget.objects.filter(id=int(target_id), is_use=1).first()
        if not target:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "指标不存在！" if lang == 'zh' else 'The target does not exist.'},
                }
            )
        if not can_be_parsed_as_list(dates):
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "日期传参错误" if lang == 'zh' else 'Date parameter error.'},
                }
            )
        try:
            station_data = MaterStation.objects.get(id=int(mstation_id), is_delete=0)
        except Exception as e:
            error_log.error("该并网点不存在: {}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "该并网点不存在" if lang == 'zh' else 'The and branch does not exist.'},
                }
            )
        try:
            strategy = UserStrategy.objects.filter(name=forecast_name, user_id=request.user["user_id"],
                                                   is_delete=0).first()
            if strategy:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "error", "detail": "策略名称重复" if lang == 'zh' else 'The strategy name is repeated.'},
                    }
                )

            dates_list = json.loads(dates)
            if len(dates_list) == 0:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "error", "detail": "请选择策略生成日期" if lang == 'zh' else 'Please select the strategy generation date.'},
                    }
                )
            all_month = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
            select_month = []
            select_data = {}
            for date_item in dates_list:
                # 先获取所选模型中准确率最高的模型出来，多个日期的则取平均值比较
                best_model_id, best_target_id = get_best_model_id(1, mstation_id, date_item, date_item)
                if not best_model_id or not best_target_id:
                    return Response(
                        {
                            "code": common_response_code.FIELD_ERROR,
                            "data": {"message": "error", "detail": "该日期的推荐策略为空，请选择其他日期" if lang == 'zh' else 'The recommended strategy for this date is empty, please select another date.'},
                        }
                    )
                month = int(date_item.split("-")[1])
                select_month.append(month)
                select_data[month] = {}
                select_data[month]['select_date'] = date_item
                select_data[month]['best_model_id'] = best_model_id

            # # 计算差集
            # diff_month = list(set(all_month) - set(select_month))
            strategy = UserStrategy.objects.create(name=forecast_name, en_name=forecast_name, user_id=request.user["user_id"])
            # 异步翻译
            pdr_data = {'id': strategy.id,
                        'table': 't_user_strategy',
                        'update_data': {'name': forecast_name}}

            pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
            redis_pool.publish(pub_name, json.dumps(pdr_data))

            # 循环插入策略
            conn = get_redis_connection("3")
            master_station = models.MaterStation.objects.get(id=int(mstation_id))
            rated_data = master_station.stationdetails_set.filter(english_name=master_station.english_name).first()
            # 提前判断选择的日期是否都有记录
            for month in all_month:
                if month in select_month:
                    charge_config, rl_list = creat_recommend_strategy_data(select_data[month], mstation_id, station_data.project.rated_power, target_id, best_target_id)
                    if not charge_config or not rl_list:
                        return Response(
                            {
                                "code": common_response_code.ERROR,
                                "data": {"message": "error", "detail": "选择的日期没有预测数据" if lang == 'zh' else 'There is no prediction data for the selected date.'},
                            }
                        )
            category_list = []  # 小时策略表
            for month in all_month:
                pv_list = self._get_pv_status_new(rated_data.province, rated_data.type, rated_data.level, month)
                if month in select_month:
                    # 查看该月份的推荐策略 生成数据
                    charge_config, rl_list = creat_recommend_strategy_data(select_data[month], mstation_id, station_data.project.rated_power, target_id, best_target_id)
                    _category = self._handle_strategy_hours(charge_config, rl_list, pv_list, month)
                    category_list.append(_category)
                    add_recommend_strategy(strategy, "月份" + str(month) if lang == 'zh' else 'Month ' + str(month),
                                           charge_config, rl_list, pv_list, month, lang)
                else:
                    # 从redis召唤指定月份当前策略
                    datas = conn.get('{}-{}-mqtt'.format(rated_data.english_name, str(month)))
                    charge_config = []
                    rl_list = []
                    if datas:
                        datas = eval(datas)
                        datas = datas.get('body')[0].get('body')
                        count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
                        for y in [24, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
                                  23]:
                            _hour = y + 24 if count == 2 else y
                            rl_list.extend([
                                int(float(datas.get(f'M{month}H{y}P', 0)) * 100),
                                int(float(datas.get(f'M{month}H{y}P', 0)) * 100),
                                int(float(datas.get(f'M{month}H{_hour}P', 0)) * 100),
                                int(float(datas.get(f'M{month}H{_hour}P', 0)) * 100),
                            ])
                            charge_config.extend([
                                int(datas.get(f'M{month}H{y}F', 0)),
                                int(datas.get(f'M{month}H{y}F', 0)),
                                int(datas.get(f'M{month}H{_hour}F', 0)),
                                int(datas.get(f'M{month}H{_hour}F', 0)),
                            ])
                    _category = self._handle_strategy_hours(charge_config, rl_list, pv_list, month)
                    category_list.append(_category)
                    add_recommend_strategy(strategy, "月份" + str(month) if lang == 'zh' else 'Month ' + str(month),
                                           charge_config, rl_list, pv_list,
                                           month, lang)
            UserStrategyHours.objects.create(strategy=strategy, data=json.dumps(category_list)).save()
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "推荐策略: 保存成功" if lang == 'zh' else 'Recommended strategy: saved successfully.',
                        "new_strategy_id": strategy.id
                    },
                }
            )
        except Exception as e:
            transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "生成推荐策略失败！" if lang == 'zh' else 'Failed to generate recommended strategy!'},
            })

    def _get_pv_status_new(self, province, type_, level, month):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        station_instance = PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).all()
        if station_instance:
            if settings.FORMATEF:
                for i in station_instance:
                    pv.append(i.pv)
            else:
                for i in station_instance[::4]:
                    pv.append(i.pv)
            return pv
        else:
            return pv

    def _handle_strategy_hours(self, charge_config, rl_list, pv_list, month):
        """
        相同时间内的阈值、充放电标识、峰谷标识 数据合并
        """
        compare_list = zip(charge_config, rl_list, pv_list)
        last_info = None
        _i = 0
        t = '00:00'
        data = []
        for i, v in enumerate(compare_list):
            if i != 0:
                if v == last_info:
                    _i += 1
                else:
                    end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
                    start_time = datetime.datetime.strptime(t, '%H:%M')
                    end_time = end_time(start_time).strftime('%H:%M')

                    d = {
                        'start_time': t,
                        'end_time': end_time,
                        'rl': last_info[1],
                        'charge_config': last_info[0],
                        'pv': last_info[2],
                        'explain': '',
                    }
                    data.append(d)
                    t = end_time
                    _i = 1
                    last_info = v
            else:
                _i += 1
                last_info = v

        end_time = lambda x: x + datetime.timedelta(minutes=15 * _i)
        start_time = datetime.datetime.strptime(t, '%H:%M')
        end_time = (end_time(start_time) - datetime.timedelta(minutes=1)).strftime('%H:%M')
        d = {
            'start_time': t,
            'end_time': end_time,
            'rl': last_info[1],
            'charge_config': last_info[0],
            'pv': last_info[2],
            'explain': '',
        }
        data.append(d)

        res = {
            'name': f'月份{month}',
            'months': [month],
            'is_follow': 1,
            'data': data

        }
        return res


class TargetModelView(APIView):
    """
    模型指标
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request):
        """
        根据指标获取关联的模型信息列表
        """
        lang = request.headers.get("lang", 'zh')
        try:
            target_id = request.query_params.get("target_id", 1)
            if not target_id:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "字段校验失败" if lang == 'zh' else 'Field verification failed.'},
                    }
                )
            models_lists = models.DictTargetModel.objects.filter(target=int(target_id),
                                                                 target__is_use=1, model__is_active=1, model__is_use=1)

            models_lists = models_lists.annotate(
                formatted_create_time=Replace(Cast(F("model__create_time"), CharField()), Value('T'), Value(' '))
            )
            models_lists = models_lists.values(
                "model__name",
                "model__id",
                start_time=F("formatted_create_time")
            )

            if not models_lists:
                return Response(
                    {
                        "code": common_response_code.NO_DATA,
                        "data": {"message": "success", "detail": []},
                    }
                )
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": list(models_lists)}
                }
            )
        except Exception as e:
            # transaction.rollback()
            error_log.error(e)
            return Response({
                "code": common_response_code.ERROR,
                "data": {"message": "error", "detail": "查询失败！" if lang == 'zh' else 'Failed to query!'},
            })


class StationListByBatchView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        """
        项目多选并网点列表
        :param request:
        :return:
        """
        lang = request.headers.get("lang", 'zh')
        # id = request.query_params.get('ids', None)  # 项目ID
        id = request.data.get("ids", None)
        if not id:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'Parameter error.'}
            })
        if not can_be_parsed_as_list(id):
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'Parameter error.'}
            })
        ids = json.loads(id)
        if len(ids) == 0:
            return Response({
                "code": common_response_code.FIELD_ERROR,
                "data": {"message": "error", "detail": "参数错误" if lang == 'zh' else 'Parameter error.'}
            })

        master_stations = MaterStation.objects.filter(project__in=ids, is_delete=0).values('id', 'name', 'en_name', 'english_name').all()

        if lang == 'en':
            for item in master_stations:
                item['name'] = item['en_name']
        return Response({
            "code": common_response_code.SUCCESS,
            "data": {
                "message": "success",
                "detail": master_stations,
            },
        })
