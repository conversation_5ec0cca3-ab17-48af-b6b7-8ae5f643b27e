package com.robestec.analysis.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryCreateDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryQueryDTO;
import com.robestec.analysis.dto.tuserstrategycategory.TUserStrategyCategoryUpdateDTO;
import com.robestec.analysis.service.TUserStrategyCategoryService;
import com.robestec.analysis.vo.TUserStrategyCategoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户策略分类管理API
 */
@RestController
@RequestMapping("/user-strategy-category")
@RequiredArgsConstructor
@Api(tags = "用户策略分类管理API")
public class TUserStrategyCategoryController {

    private final TUserStrategyCategoryService tUserStrategyCategoryService;

    @GetMapping
    @ApiOperation("分页查询用户策略分类")
    public PageResult<TUserStrategyCategoryVO> queryTUserStrategyCategory(@Validated TUserStrategyCategoryQueryDTO queryDTO) {
        return tUserStrategyCategoryService.queryTUserStrategyCategory(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增用户策略分类")
    public Result<Long> createTUserStrategyCategory(@Validated @RequestBody TUserStrategyCategoryCreateDTO createDTO) {
        return Result.succeed(tUserStrategyCategoryService.createTUserStrategyCategory(createDTO));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增用户策略分类")
    public Result createTUserStrategyCategoryList(@Validated @RequestBody List<TUserStrategyCategoryCreateDTO> createDTOList) {
        tUserStrategyCategoryService.createTUserStrategyCategoryList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改用户策略分类")
    public Result updateTUserStrategyCategory(@PathVariable Long id, @Validated @RequestBody TUserStrategyCategoryUpdateDTO updateDTO) {
        updateDTO.setId(id);
        tUserStrategyCategoryService.updateTUserStrategyCategory(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除用户策略分类")
    public Result deleteTUserStrategyCategory(@PathVariable Long id) {
        tUserStrategyCategoryService.deleteTUserStrategyCategory(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取用户策略分类详情")
    public Result<TUserStrategyCategoryVO> getTUserStrategyCategory(@PathVariable Long id) {
        return Result.succeed(tUserStrategyCategoryService.getTUserStrategyCategory(id));
    }

    @GetMapping("/strategy/{strategyId}")
    @ApiOperation("根据策略ID查询用户策略分类")
    public Result<List<TUserStrategyCategoryVO>> getTUserStrategyCategoryByStrategyId(@PathVariable Long strategyId) {
        return Result.succeed(tUserStrategyCategoryService.getTUserStrategyCategoryByStrategyId(strategyId));
    }

    @GetMapping("/name/{name}")
    @ApiOperation("根据分类名称查询用户策略分类")
    public Result<List<TUserStrategyCategoryVO>> getTUserStrategyCategoryByName(@PathVariable String name) {
        return Result.succeed(tUserStrategyCategoryService.getTUserStrategyCategoryByName(name));
    }

    @GetMapping("/count/strategy/{strategyId}")
    @ApiOperation("统计策略ID的分类数量")
    public Result<Long> countByStrategyId(@PathVariable Long strategyId) {
        return Result.succeed(tUserStrategyCategoryService.countByStrategyId(strategyId));
    }
}
