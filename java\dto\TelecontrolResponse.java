package com.robestec.analysis.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 远程控制相关响应DTO
 */
public class TelecontrolResponse {

    /**
     * 通用响应结果
     */
    @Data
    public static class CommonResult<T> {
        private Integer code;
        private String msg;
        private T data;

        public static <T> CommonResult<T> success(T data) {
            CommonResult<T> result = new CommonResult<>();
            result.setCode(200);
            result.setMsg("操作成功");
            result.setData(data);
            return result;
        }

        public static <T> CommonResult<T> success(String msg, T data) {
            CommonResult<T> result = new CommonResult<>();
            result.setCode(200);
            result.setMsg(msg);
            result.setData(data);
            return result;
        }

        public static <T> CommonResult<T> error(String msg) {
            CommonResult<T> result = new CommonResult<>();
            result.setCode(500);
            result.setMsg(msg);
            return result;
        }

        public static <T> CommonResult<T> error(Integer code, String msg) {
            CommonResult<T> result = new CommonResult<>();
            result.setCode(code);
            result.setMsg(msg);
            return result;
        }
    }

    /**
     * 分页响应结果
     */
    @Data
    public static class PageResult<T> {
        private List<T> records;
        private Long total;
        private Integer pageNum;
        private Integer pageSize;

        public PageResult(List<T> records, Long total, Integer pageNum, Integer pageSize) {
            this.records = records;
            this.total = total;
            this.pageNum = pageNum;
            this.pageSize = pageSize;
        }
    }

    /**
     * 电站容量信息响应
     */
    @Data
    public static class StationCapacityResponse {
        private Long id;
        private String name;
        private String descr;
        private Double electricPower;
        private Double realPower;
    }

    /**
     * 功率计划列表响应
     */
    @Data
    public static class PowerPlanListResponse {
        private Long id;
        private String name;
        private String enName;
        private Integer status;
        private String statusName;
        private Integer planType;
        private String planTypeName;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String userName;
        private LocalDateTime createTime;
    }

    /**
     * 功率计划详情响应
     */
    @Data
    public static class PowerPlanDetailResponse {
        private Long id;
        private String name;
        private String enName;
        private String powerList;
        private String stationList;
        private Integer planType;
        private String planTypeName;
        private String userName;
        private LocalDateTime createTime;
    }

    /**
     * 策略模板导入响应
     */
    @Data
    public static class StrategyImportResponse {
        private String name;
        private List<StrategyDataItem> data;
        private List<String> monthList;

        @Data
        public static class StrategyDataItem {
            private String startTime;
            private String endTime;
            private Object pv;
            private Object chargeConfig;
            private String rl;
        }
    }

    /**
     * 计划历史记录响应
     */
    @Data
    public static class PlanHistoryResponse {
        private Long id;
        private String station;
        private String typeName;
        private String descr;
        private Integer status;
        private String statusName;
        private String time;
        private String userName;
    }

    /**
     * 项目包响应
     */
    @Data
    public static class ProjectPackResponse {
        private String name;
        private Object data;
    }

    /**
     * 文件导出响应
     */
    @Data
    public static class FileExportResponse {
        private String fileName;
        private String filePath;
    }

    /**
     * 下发类型响应
     */
    @Data
    public static class IssuanceTypeResponse {
        private Map<String, String> issuanceTypes;
    }
}
