#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-11-06 08:51:51
#@FilePath     : \RHBESS_Serviced:\emot_pjt_rh\TianLuAppBackend\apis\statistics_apis\urls.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-07 16:44:19


from django.urls import path
from . import views

urlpatterns = [
    # 小程序
    # path("electricity_count/week/", views.ElectricityWeekCountView.as_view()),  # 历史 7 放电量               # done
    # path("electricity_count/day/", views.ElectricityDayCountView.as_view()),  # 逐时冲放电量                   # done
    # path("electricity_count/type/", views.ElectricityTypeCountView.as_view()),  # 冲放电量统计                 # done
    # path("power_count/day/", views.DayPowerView.as_view()),  # 历史二十四小时功率                               # done    1
    # path("soc/day/", views.SocHistoryView.as_view()),  # 历史 SOC                                            # done   1
    # path("load/day/", views.LoadDayView.as_view()),  # 负荷功率                                               # done    1
    # path("soc/day/v2/", views.SocHistoryV2View.as_view()),  # 历史 SOC                                       # done    注释
    # path("incom/details/", views.IncomeDetailsView.as_view()),  # 月份及收益                                  # done
    # path("incom/add/", views.IncomeAddView.as_view()),  # 月份及收益添加                                       # done
    # path("automatic/control/add/", views.AutomaticControlView.as_view()),  # 自动控制模式下发       (废弃）
    # path("automatic/plan/", views.AutomaticPlanView.as_view()),  # 用户控制策略                    （废弃）
    # path("automatic/get/", views.AutomaticGetView.as_view()),  # 官方电价获取                                    # done
    # path("automatic/current_plan/", views.AutomaticCurrentView.as_view()),  # 当前策略             (废弃）
    # path("automatic/sendsms/", views.AutomaticControlSendSmsView.as_view()),  # 自动控制模式发送短信
    path("report/cell/", views.ReportCellView.as_view()),  # 电芯报告                                           # done
    # path("automatic/plan_del/", views.AutomaticPlanDelView.as_view()),  # 用户控制策略删除           (废弃）
    # path("power_acting/day/", views.PowerActingView.as_view()),  # 当日有功/无功功率折线图                         # done
    # path("three_phase_current/day/", views.ThreePhaseCurrentView.as_view()),  # 当日三相电流                     # done
    # path("phase_voltage/day/", views.PhaseVoltageView.as_view()),  # 当日三相电压                                # done     1
    # path("estimated_cap_dis/day/", views.EstimatedCapacityDischargingView.as_view()),  # 当日预估可充可放         # done
    # path("realtime_station_policy/", views.RealTimeStationPolicyView.as_view()),  # 获取实时策略     (废弃）

    # path("electricity_count/load_curve/", views.ElectricityLoadCurveView.as_view()),  # 负载曲线                # done      1

    # web 2.0 web端--收益管理
    path("electricity_count/station_names", views.ListStationNamesView.as_view()),    # 收益录入：电站下拉选择名称    no need
    path("custom_income/add/", views.CustomIncomeView.as_view()),                     # 收益录入: 新增             en
    path("custom_income/update/<int:id>", views.CustomIncomeView.as_view()),          # 收益录入：更新             en
    path("custom_income/list", views.CustomIncomesView.as_view()),                    # 收益录入：列表             en
    path("custom_income/detail/<int:id>", views.CustomIncomeView.as_view()),          # 收益录入：查询             en
    path("custom_income/delete/<int:id>", views.CustomIncomeDelView.as_view()),       # 收益录入：删除             en
    # path("custom_income/upload", views.FileUploadView.as_view()),                     # 收益录入：上传附件          en
    # path("custom_income/download/<str:file_id>", views.FileDownloadView.as_view()),

    # web 2.0 web端--PCS/电源监测
    path("station/monitor/titles", views.PcsMonitorTitlesView.as_view()),               # PCS/电源监测数据：标头集合      no need
    path("station/monitor/data", views.MonitorView.as_view()),                          # PCS/电源监测数据：实时遥测数据/实时遥信数据    no need

    # web端--运行数据监测
    # path("station/monitor/oprate_data", views.OperatingDataMonitorView.as_view()),      # 运行监控                                    # done     1

    # 小程序 1.0
    # path("user_strategy/add", views.UserStrategyView.as_view()),                                    # 用户自动控制策略：新增
    # path("user_strategy/update/<int:pk>", views.UserStrategyView.as_view()),                        # 用户自动控制策略：修改
    # path("user_strategy/list", views.UserStrategyView.as_view()),                                   # 用户自动控制策略：列表
    # path("user_strategy/delete/<int:pk>", views.UserStrategyDeleteView.as_view()),                  # 用户自动控制策略：删除
    # path("user_strategy/check_month/<int:pk>", views.UserStrategyCheckMonthView.as_view()),         # 用户自动控制策略：月份校验
    # path("user_strategy/save/<int:pk>", views.UserStrategySaveToOtherView.as_view()),               # 用户自动控制策略：另存为
    # path("user_strategy/apply", views.UserStrategyApplyView.as_view()),                             # 用户自动控制策略：下发
    # path("user_strategy/current", views.CurrentStrategyView.as_view()),                             # 用户自动控制策略：当前策略
    # path("user_strategy/default", views.DefaultStrategyView.as_view()),                             # 用户自动控制策略：默认策略
    # path("user_strategy/realtime", views.RealTimeStationStrategyView.as_view()),                    # 用户自动控制策略：实时策略
    # path("user_strategy/compare", views.CompareStationStrategyView.as_view()),                    # 用户自动控制策略：默认策略比较
    # path("user_strategy/compare/customize", views.CustomizeStationStrategyView.as_view()),        # 用户自动控制策略：自定义策略比较
    # path("user_strategy/customize/list/<int:strategy_id>", views.UserStrategyCustomizeView.as_view()),  # 用户自动控制策略-自定义策略列表
    # path("station_strategy/month", views.StationStrategyView.as_view()),                            # 用户自动控制策略：某月的策略, mtqq
    #
    # path("user_strategy/category/add", views.UserStrategyCategoryView.as_view()),                       # 用户自动控制策略-分类：新增
    # path("user_strategy/category/update/<int:pk>", views.UserStrategyCategoryView.as_view()),           # 用户自动控制策略-分类：修改
    # path("user_strategy/category/update_month/<int:pk>", views.UserStrategyCategoryUpdateMonthView.as_view()),
    #                                                                                                     # 用户自动控制策略-分类：修改月份
    #
    # path("user_strategy/category/list/<int:strategy_id>", views.UserStrategyCategoryView.as_view()),    # 用户自动控制策略-分类：列表
    # path("user_strategy/category/delete/<int:pk>", views.UserStrategyCategory2View.as_view()),          # 用户自动控制策略-分类：删除
    # path("user_strategy/category/detail/<int:pk>", views.UserStrategyCategory2View.as_view()),          # 用户自动控制策略-分类：详情


]
