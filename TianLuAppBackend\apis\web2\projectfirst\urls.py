from django.urls import path
from apis.web2.projectfirst import views

urlpatterns = [
    # 项目首页
    path('weather/', views.Weather.as_view()),  # 项目所在地天气信息         en
    path('project/message/', views.MessageView.as_view()),  # 项目首页信息        en
    path('station/list/', views.StationListView.as_view()),  # 项目并网点列表      en
    path('station/message/', views.StationMessageView.as_view()),  # 项目并网点信息    en
    path('control_list/', views.ControlListView.as_view()),  # 就地控制发列表      en
    path('control_check_smscode/', views.ControlSMSCodeCheckView.as_view()),  # 就地控制下发      en
    path('control_send_smscode/', views.ControlSendSmsCodeView.as_view()),  # 就地控制发送短信     en
    path('station/unit_list/', views.StationUnitListView.as_view()),  # 储能单元清单      en
    path('station/grid_side_power/', views.StationGridSidePowerView.as_view()),  # 电网侧电表    en
    path('station/unit_detail/', views.StationUnitDetailView.as_view()),  # 储能单元详情      en
    path('alarm/detail/', views.AlarmDetailView.as_view()),  # 判断是否显示故障复位       en
    path('reset_fault/sendsms/', views.ResetFaultSendSmsCodeView.as_view()),  # 故障复位发送短信        en
    path('reset_fault/', views.ResetFaultSMSCodeCheckView.as_view()),  # 故障复位下发         en
    path('unit_switch/', views.UnitSwitchSMSCodeCheckView.as_view()),  # 单元控制下发     en
    path('unit_switch_send_smscode/', views.UnitSwitchSendSmsCodeView.as_view()),  # 单元开关控制下发短信     en
    path('unit_power/', views.UnitPowerSMSCodeCheckView.as_view()),  # 单元功率下发       en
    path('unit_power_send_smscode/', views.UnitPowerSendSmsCodeView.as_view()),  # 单元功率下发发送短信       en
    path('virtually_send_smscode/', views.VirtuallySendSmsCodeView.as_view()),  # 需量下发发送短信      en
    path('virtually_check_smscode/', views.VirtuallySMSCodeCheckView.as_view()),  # 需量下发        en
    path('virtually_check_param/', views.VirtuallyCheckParamView.as_view()),  # 需量下发回显      en
    path('power_send_smscode/', views.PowerSendSmsCodeView.as_view()),  # 全站功率下发发送短信        en
    path('power_check_smscode/', views.PowerSMSCodeCheckView.as_view()),  # 全站功率下发      en
    path('station/overview/', views.StationoverviewView.as_view()),  # 生产概览     en

    path("project_area/message/", views.ProjectAreaViews.as_view()),  # 台区项目信息详情
    path("station_area/message/", views.StationAreaViews.as_view()),  # 台区并网点信息详情
    path("station_area/grid_side_power/", views.StationAreaGridSidePower.as_view()),  # 采集点电表详情
    path("station_area/overview/", views.StationAreaOverview.as_view()),  # 监测数据折线图信息
    path("station_area/chag_disg_days/", views.StationAreaChagDisg.as_view()),  # 逐日充放电量信息
    path("station_area/overviewload/", views.StationAreaOverviewLoad.as_view()),  # 监测数据下载
    path("tran_area/overview/", views.TrabAreaOverview.as_view()),  # 变压器监测数据折线图
    path("tran_real_area/overview/", views.TrabRealAreaOverview.as_view()),  # 变压器实时数据监测信息
    path("tran_area/overviewload/", views.TrabAreaOverviewLoad.as_view()),  # 变压器监测数据下载
    path("pcs_area/overview/", views.PCSAreaOverview.as_view()),  # PCS监测数据折线图
    path("pcs_real_area/overview/", views.PCSRealAreaOverview.as_view()),  # PCS实时数据监测信息
    path("pcs_area/overviewload/", views.PCSRealAreaOverviewLoad.as_view()),  # PCS实监测数据下载

]