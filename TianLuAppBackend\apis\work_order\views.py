import datetime
import json
import os
import re
import traceback
from io import BytesIO

import requests
from django.conf import settings
from django.db.models import Q
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from rest_framework.mixins import *
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from serializers import work_order_serializers
from django.db import transaction
from common.common_response_code import SUCCESS
from common.common_response_code import ERROR
from common.common_response_code import AUTHENTICATION_FIELD
from common.common_response_code import OrderAuthority
from common.common_response_code import OrderStatus
from apis.work_order import models
from apis.user.models import UserDetails
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from collections import OrderedDict

from serializers.work_order_serializers import WorkOrderStationInfoSerializer
from tools.minio_tool import MinioTool
from .filters import <PERSON><PERSON>derFilter
from .models import WorkOrderStationInfoModel
from .tasks import send_email

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# ordered转换为dict
def convert_ordered_dict(obj):
    if isinstance(obj, OrderedDict):
        return {k: convert_ordered_dict(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_ordered_dict(item) for item in obj]
    else:
        return obj


# Create your views here.
# 分页配置信息
class Pagination(PageNumberPagination):
    # 默认每页显示10条数据
    page_size = 10

    # 根据'page_size'关键字自定义每页显示多少条数据
    page_size_query_param = 'page_size'

    # 自定义'pag'关键字查询具体某一页的数据（默认为'page'）
    # 如：?pag=3 即查询第三页的数据
    page_query_param = 'pag'


@csrf_exempt
def upload_file(request):
    """
    文件上传
    """
    if request.method == 'POST':
        img_objs = request.FILES.getlist('picture')
        new_name = request.POST['name']
        if img_objs and new_name:
            path_list = []
            for index, img_obj in enumerate(img_objs):
                now = datetime.datetime.now()
                img_name = img_obj.name  # 获取上传文件的字符串类型名称/
                suffix = re.findall(r'\..*', img_name)[0]
                file_name = new_name + f"{now.year}{now.month}{now.day}{now.hour}{now.minute}{now.second}" + '-' + str(
                    index + 1) + suffix
                path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

                with open(path, mode='wb') as f:
                    for content in img_obj.chunks():  # 读取上传文件的内容
                        f.write(content)  # 存储图像文件

                try:
                    minio_client = MinioTool()
                    minio_client.create_bucket('worker')
                    minio_client.upload_local_image(file_name, path, 'worker')
                    os.remove(path)
                except Exception as e:
                    success_log.error(e)
                    error_log.error(traceback.print_exc())
                    return HttpResponse(json.dumps({
                        "code": ERROR,
                        "data": {
                            "message": "error",
                            "detail": "上传失败",
                        },
                    }))

                path_list.append(file_name)

            return HttpResponse(json.dumps({
                "code": SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "上传成功",
                    "file_name_list": path_list
                },
            }))
        else:
            return HttpResponse(json.dumps({
                "code": ERROR,
                "data": {
                    "message": "error",
                    "detail": "上传失败",
                },
            }))


class WorkOrderDownloadView(APIView):
    """获取工单文件下载链接"""""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        file_name = request.query_params.get('file_name')
        try:
            url = MinioTool().get_download_url('worker', file_name)
            return Response({
                "code": SUCCESS,
                "data": {
                    "message": "success",
                    "detail": url
                },
            })
        except Exception as e:
            print(e)
            error_log.error(traceback.print_exc())
            return Response({
                "code": ERROR,
                "data": {
                    "message": "error",
                    "detail": "获取文件链接失败或文件不存在",
                },
            })


def _data_grep(data):
    """提交工单对数据分组"""
    order = [field.name for field in models.WorkOrderModel._meta.get_fields()]
    custom = [field.name for field in models.CustomInfoModel._meta.get_fields()]
    project = [field.name for field in models.ProjectInfoModel._meta.get_fields()]
    result = {'order': {}, 'custom': {}, 'project': {}}
    for k, v in data.items():
        if k in order:
            result['order'][k] = v
        if k in custom:
            result['custom'][k] = v
        if k in project:
            result['project'][k] = v
    return result


def return_response(code, message='', detail=''):
    """封装返回信息"""
    # message = ''
    # detail = ''
    if code == AUTHENTICATION_FIELD:
        message = "authentication_field" if message == '' else message
        detail = "无访问权限" if detail == '' else detail
    if code == SUCCESS:
        message = "success" if message == '' else message
        detail = "操作成功" if detail == '' else detail
    if code == ERROR:
        message = "error" if message == '' else message
        detail = "操作失败" if detail == '' else detail

    return Response({
        "code": code,
        "data": {
            "message": message,
            "detail": detail,
        },
    })


class WorkOrder(APIView):
    """工单资源"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, *args, **kwargs):
        """获取工单列表"""
        auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
        queryset = models.WorkOrderModel.objects.all()
        filter = WorkOderFilter(queryset=queryset, data=request.query_params)
        work_orders = filter.qs
        if auth_query_dic:
            unit_group = auth_query_dic.get().unit_group
            auth_level = auth_query_dic.get().user_authority
            if auth_level == OrderAuthority.RECEIVING:
                q1 = Q(status=OrderStatus.PENDING_ORDERS)
                q2 = Q(status=OrderStatus.PENDING_EXECUTED)
                q3 = Q(executor_unit=unit_group)
                work_order = work_orders.filter((q1 | q2) & q3).order_by('-create_time')
            elif auth_level == OrderAuthority.READONLY:
                q1 = Q(status=OrderStatus.STOP)
                q2 = Q(status=OrderStatus.FINISH)
                work_order = work_orders.filter(q1 | q2).order_by('-create_time')
            elif auth_level == OrderAuthority.DISPATCH:
                work_order = work_orders.filter(user_id=request.user.get('user_id')).order_by('-create_time')
            else:
                work_order = work_orders.order_by('-create_time')

            serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order, many=True).data
            return Response(serialized_work_order)
        else:
            return return_response(AUTHENTICATION_FIELD)

    def post(self, request):
        """新增工单"""
        auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
        if not auth_query_dic:
            return return_response(AUTHENTICATION_FIELD)

        auth_level = auth_query_dic.get().user_authority
        if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
            try:
                # 在这里执行数据库操作，事务将在此代码块结束后自动提交或回滚
                with transaction.atomic():
                    # 删除草稿箱
                    if request.data.get('draft_id'):
                        draft = models.DraftWorkOrderModel.objects.filter(id=request.data.get('draft_id'))
                        if draft:
                            draft.delete()
                    # 完成时间
                    finish_date = _data_grep(request.data)['order']['finish_date']
                    fd_date_obj = datetime.datetime.strptime(finish_date, "%Y-%m-%d")
                    fd_timestamp = fd_date_obj.timestamp()  # 期望完成时间戳
                    today_date = datetime.date.today()
                    td_timestamp = datetime.datetime(today_date.year, today_date.month, today_date.day).timestamp()
                    if fd_timestamp < td_timestamp:
                        return Response({
                            "code": ERROR,
                            "data": {
                                "message": 'error',
                                "detail": '添加失败，期望完成时间不能小于当前时间',
                            },
                        })
                    # 保存客户信息
                    custom_info = models.CustomInfoModel.objects.create(
                        **_data_grep(request.data)['custom']
                    )
                    custom_info.save()
                    # 保存工程信息
                    project_info = models.ProjectInfoModel.objects.create(
                        **_data_grep(request.data)['project']
                    )
                    project_info.save()

                    # 保存工单执行人信息（针对公司售后）
                    c_executor_id = request.data.get('comp_executor_id')
                    if int(request.data.get('executor_unit')) == 0:
                        # 工单指定方为“公司售后”时，该"工单执行人"字段为必填项
                        if not c_executor_id:
                            return Response({
                                "code": ERROR,
                                "data": {
                                    "message": 'error',
                                    "detail": '添加失败：工单指定方为“公司售后”时，“工单执行人”字段为必填',
                                },
                            })
                    c_executor = None
                    if c_executor_id:
                        try:
                            c_executor = models.WorkOrderCompExecutorModel.objects.get(id=c_executor_id)
                        except Exception as e:
                            error_log.error(e)
                            return Response({
                                "code": ERROR,
                                "data": {
                                    "message": 'error',
                                    "detail": '添加失败：工单执行人信息不存在',
                                },
                            })

                    order_num = f'{datetime.datetime.now()}'.replace(' ', '').replace('-', '').replace(':', '').replace('.',
                                                                                                                        '')
                    # 保存工单信息
                    work_order = models.WorkOrderModel.objects.create(
                        **_data_grep(request.data)['order'],
                        order_num=order_num,
                        project_id=project_info,
                        user_id=UserDetails.objects.filter(id=request.user.get('user_id')).first(),
                        custom_id=custom_info,
                        c_executor=c_executor
                    )
                    work_order.save()

                    # 优化方案v2.0: 修改成配置多个并网点信息
                    stations = request.data.get("stations")
                    if not stations or not len(stations):
                        return Response({
                            "code": ERROR,
                            "data": {
                                "message": 'error',
                                "detail": '添加失败: 工程信息参数缺失或为空',
                            },
                        })
                    for station in stations:
                        WorkOrderStationInfoModel.objects.create(station_name=station.get('station_name'),
                                                                 vol=station.get('vol'),
                                                                 dev_count=station.get('dev_count'),
                                                                 level=station.get('level'),
                                                                 combination=station.get('combination'),
                                                                 is_ems=station.get('is_ems'),
                                                                 work_order=work_order)

                    # 保存工单生命周期信息
                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=order_num,
                        order_status='创建工单',
                        name=UserDetails.objects.get(id=request.user.get('user_id')).user_name,
                        is_process=1
                    )
                    work_order_schedule.save()
                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=order_num,
                        order_status='预审',
                        user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                    )
                    work_order_schedule.save()
                transaction.set_autocommit(True)  # 手动提交事务
                # 发送邮件
                send_email(order_num, '预审')
                # return return_response(SUCCESS)
                return Response({
                    "code": SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "操作成功",
                        "id": work_order.id
                    },
                })

            except Exception as e:
                error_log.error(e)
                error_log.error(traceback.print_exc())
                transaction.rollback()  # 手动回滚事务
                return return_response(ERROR)
        else:
            return return_response(AUTHENTICATION_FIELD)


class WorkOrderInfo(APIView):
    """工单详情"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, *args, **kwargs):
        work_order = models.WorkOrderModel.objects.filter(id=self.kwargs['id'])
        work_order.update(see=1)
        serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order, many=True).data
        if serialized_work_order:
            work_order_schedule = models.WorkOrderScheduleModel.objects.filter(
                order_num=serialized_work_order[0]['order_num'], name__isnull=False).order_by(
                'create_time')
            serialized_work_order_schedule = work_order_serializers.WorkOrderScheduleSerializer(work_order_schedule,
                                                                                                many=True).data
            serialized_work_order[0]['work_order_schedule'] = serialized_work_order_schedule
        else:
            return return_response(ERROR)
        return Response(serialized_work_order[0])

    def put(self, request, *args, **kwargs):
        auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
        if not auth_query_dic:
            return return_response(AUTHENTICATION_FIELD)

        auth_level = auth_query_dic.get().user_authority
        if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
            work_order = models.WorkOrderModel.objects.filter(id=self.kwargs['id'])
            serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order, many=True).data
            if serialized_work_order:

                try:
                    # 在这里执行数据库操作，事务将在此代码块结束后自动提交或回滚
                    with transaction.atomic():
                        if request.data.get('draft_id'):
                            draft = models.DraftWorkOrderModel.objects.filter(id=request.data.get('draft_id'))
                            if draft:
                                draft.delete()
                        work_order_object = models.WorkOrderModel.objects.get(id=self.kwargs['id'])

                        order = _data_grep(request.data)['order']
                        if 'status' in order.keys():
                            order.pop('status')

                        # 保存工单执行人信息（针对公司售后）
                        c_executor_id = request.data.get('comp_executor_id')
                        if int(request.data.get('executor_unit')) == 0:
                            # 工单指定方为“公司售后”时，该"工单执行人"字段为必填项
                            if not c_executor_id:
                                return Response({
                                    "code": ERROR,
                                    "data": {
                                        "message": 'error',
                                        "detail": '添加失败：工单指定方为“公司售后”时，"工单执行人"字段为必填',
                                    },
                                })
                        c_executor = None
                        if c_executor_id:
                            try:
                                c_executor = models.WorkOrderCompExecutorModel.objects.get(id=c_executor_id)
                            except Exception as e:
                                error_log.error(e)
                                return Response({
                                    "code": ERROR,
                                    "data": {
                                        "message": 'error',
                                        "detail": '添加失败：工单执行人信息不存在',
                                    },
                                })

                        # 修改工单信息
                        work_order.update(
                            **order,
                            c_executor=c_executor,
                            status=OrderStatus.FIRST_EXAMINE
                        )

                        # 修改客户信息
                        custom_info = models.CustomInfoModel.objects.filter(id=work_order_object.custom_id.id)
                        custom_info.update(
                            **_data_grep(request.data)['custom']
                        )
                        # 修改工程信息
                        project_info = models.ProjectInfoModel.objects.filter(id=work_order_object.project_id.id)
                        project_info.update(
                            **_data_grep(request.data)['project']
                        )
                        # 优化方案v2.0: 修改成配置多个并网点信息
                        stations = request.data.get("stations")
                        if not stations or not len(stations):
                            return Response({
                                "code": ERROR,
                                "data": {
                                    "message": 'error',
                                    "detail": '修改工单失败: 工程信息参数缺失或为空',
                                },
                            })
                        for station in stations:
                            id_ = station.get('id', None)
                            # 修改已有的并网点配置
                            if id_:
                                try:
                                    station_ins = WorkOrderStationInfoModel.objects.get(id=id_)
                                except Exception as e:
                                    error_log.error(e)
                                    return Response({
                                        "code": ERROR,
                                        "data": {
                                            "message": 'error',
                                            "detail": '修改工单失败: 并网点信息 ID 不存在',
                                        },
                                    })
                                station_ins.station_name = station.get('station_name', station_ins.station_name)
                                station_ins.vol = station.get('vol', station_ins.vol)
                                station_ins.dev_count = station.get('dev_count', station_ins.dev_count)
                                station_ins.level = station.get('level', station_ins.level)
                                station_ins.combination = station.get('combination', station_ins.combination)
                                station_ins.is_ems = station.get('is_ems', station_ins.is_ems)
                                station_ins.save()

                            # 新增的并网点配置
                            else:
                                WorkOrderStationInfoModel.objects.create(station_name=station.get('station_name'),
                                                                         vol=station.get('vol'),
                                                                         dev_count=station.get('dev_count'),
                                                                         level=station.get('level'),
                                                                         combination=station.get('combination'),
                                                                         is_ems=station.get('is_ems'),
                                                                         work_order=work_order.first()
                                                                         )

                        # 修改工单信息
                        # work_order.update(
                        #     **_data_grep(request.data)['order'],
                        #     status=OrderStatus.FIRST_EXAMINE
                        # )
                        # 修改工单流程
                        # order_schedule = models.WorkOrderScheduleModel.objects.filter(
                        #     order_num=serialized_work_order[0]['order_num'], order_status='修改工单').order_by(
                        #     '-create_time').first()
                        # if order_schedule:
                        #     order_schedule.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                        #     order_schedule.is_process = 1
                        #     order_schedule.save()
                        # else:
                        work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                            order_num=serialized_work_order[0]['order_num'],
                            is_process=1,
                            name=UserDetails.objects.get(id=request.user.get('user_id')).user_name,
                            order_status='修改工单'
                        )

                        has_ys_work_order_schedules = models.WorkOrderScheduleModel.objects.filter(
                            order_num=serialized_work_order[0]['order_num'], order_status='预审', is_process=0)

                        if has_ys_work_order_schedules:
                            for has_ys_work_order_schedule in has_ys_work_order_schedules:
                                has_ys_work_order_schedule.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                                has_ys_work_order_schedule.is_process = -2

                        work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                            order_num=serialized_work_order[0]['order_num'],
                            order_status='预审',
                            user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                        )
                        work_order_schedule.save()
                    transaction.set_autocommit(True)  # 手动提交事务
                    # 发送邮件
                    send_email(serialized_work_order[0]['order_num'], '预审')
                    return return_response(SUCCESS, f"工单：{self.kwargs['id']}已被{UserDetails.objects.get(id=request.user.get('user_id')).user_name}修改成功")

                except Exception as e:
                    error_log.error(traceback.print_exc())
                    transaction.rollback()  # 手动回滚事务
                    return return_response(ERROR)
            else:
                return return_response(ERROR)
        else:
            return return_response(AUTHENTICATION_FIELD)


class WorOrderStationView(APIView):
    """工单详情：查询列表和删除某一个并网点信息配置"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request, pk):
        """
        工单详情：获取某一个工单的所有并网点信息配置
        """""
        try:
            station_objs = WorkOrderStationInfoModel.objects.filter(work_order_id=pk)
            if station_objs.exists():
                ser = WorkOrderStationInfoSerializer(station_objs.all(), many=True)
                return Response(
                    {
                        "code": SUCCESS,
                        "data": {"message": "success", "detail": ser.data},
                    }
                )
            else:
                return Response(
                    {
                        "code": ERROR,
                        "data": {"message": "no data", "detail": []},
                    }
                )
        except Exception as e:
            error_log.error("工单详情：查询并网点信息报错：{}".format(e))
            return Response(
                {
                    "code": ERROR,
                    "data": {"message": "fail", "detail": '工单详情：查询并网点信息报错：查询失败!'},
                }
            )

    def post(self, request, pk):
        """
        工单详情：删除某一个并网点信息配置
        """""
        try:
            obj = WorkOrderStationInfoModel.objects.get(id=pk)
            obj.delete()
            return Response(
                {
                    "code": SUCCESS,
                    "data": {"message": "success", "detail": '工单详情：删除某一个并网点信息配置：删除成功'},
                }
            )
        except Exception as e:
            error_log.error("工单详情：删除某一个并网点信息配置: 删除报错：{}".format(e))
            return Response(
                {
                    "code": ERROR,
                    "data": {"message": "fail", "detail": '工单详情：删除某一个并网点信息配置：删除失败!'},
                }
            )


class DraftWorkOrderViewSet(GenericViewSet, CreateModelMixin, UpdateModelMixin, ListModelMixin, RetrieveModelMixin,
                            DestroyModelMixin):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    """工单资源草稿功能"""
    queryset = models.DraftWorkOrderModel.objects.all().order_by('-create_time')
    serializer_class = work_order_serializers.DraftWorkOrderSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(
            models.DraftWorkOrderModel.objects.all().filter(user_id=request.user.get('user_id'))
        )
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        try:
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority
            if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
                with transaction.atomic():
                    serialized_work_order = {}
                    if request.data.get('order_id'):
                        work_order = models.WorkOrderModel.objects.filter(id=request.data.get('order_id'))
                        serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order, many=True).data
                    history = ""
                    if serialized_work_order:
                        work_order_schedule = models.WorkOrderScheduleModel.objects.filter(
                            order_num=serialized_work_order[0]['order_num']).order_by(
                            'create_time')
                        serialized_work_order_schedule = work_order_serializers.WorkOrderScheduleSerializer(
                            work_order_schedule,
                            many=True).data
                        serialized_work_order[0]['work_order_schedule'] = serialized_work_order_schedule
                        history = convert_ordered_dict(serialized_work_order[0])
                    draft_order = models.DraftWorkOrderModel.objects.create(
                        user_id=request.user.get('user_id'),
                        order_id=request.data.get('order_id') if request.data.get('order_id') else None,
                        history=json.dumps(history),
                        new_data=request.data.get('new_data')
                    )
                    draft_order.save()
                transaction.set_autocommit(True)
                return return_response(SUCCESS)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            transaction.rollback()
            return return_response(ERROR)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        result = {
            'id': serializer.data['id'],
            'create_time': serializer.data['create_time'],
            'user_id': serializer.data['user_id'],
            'order_id': serializer.data['order_id'],
            'history': json.loads(serializer.data['history']) if serializer.data['history'] else '',
            'new_data': json.loads(serializer.data['new_data']) if serializer.data['new_data'] else ''
        }
        return Response(result)

    def update(self, request, *args, **kwargs):
        try:
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority
            if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
                with transaction.atomic():
                    partial = kwargs.pop('partial', False)
                    instance = self.get_object()
                    serializer = self.get_serializer(instance, data=request.data, partial=partial)
                    serializer.is_valid(raise_exception=True)
                    self.perform_update(serializer)

                    if getattr(instance, '_prefetched_objects_cache', None):
                        # If 'prefetch_related' has been applied to a queryset, we need to
                        # forcibly invalidate the prefetch cache on the instance.
                        instance._prefetched_objects_cache = {}
                transaction.set_autocommit(True)

                return return_response(SUCCESS)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            transaction.rollback()
            return return_response(ERROR)


class ReceiveOrderViewSet(GenericViewSet, CreateModelMixin, UpdateModelMixin):
    """接单"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    queryset = models.ExecutorModel.objects.all().order_by('-create_time')
    serializer_class = work_order_serializers.ExecutorSerializer

    def create(self, request, *args, **kwargs):
        try:
            user = UserDetails.objects.get(id=request.user.get('user_id'))
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority
            if auth_level in [OrderAuthority.SUPER, OrderAuthority.RECEIVING]:
                work_order = models.WorkOrderModel.objects.filter(id=self.request.data['order_id']).first()

                if (work_order.c_executor and work_order.c_executor.name != user.user_name) and auth_level != OrderAuthority.SUPER:
                    return return_response(ERROR, "非工单指定的接单人或超级管理员无法接单，如有疑问请联系管理员.")

                with transaction.atomic():

                    # 新增：判断接单人是否为指定的接单人或者超级管理员，否则拒绝其接单
                    serializer = self.get_serializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    self.perform_create(serializer)

                    # 删除草稿箱
                    if request.data.get('draft_id'):
                        draft = models.DraftWorkOrderModel.objects.filter(id=request.data.get('draft_id'))
                        if draft:
                            draft.delete()
                    if request.data.get('order_id'):
                        draft_query_dic = models.DraftWorkOrderModel.objects.filter(order_id=request.data.get('order_id'))
                        if draft_query_dic:
                            draft_query_dic.delete()

                    # work_order.update(
                    #     executor_id=serializer.instance,
                    #     status=OrderStatus.PENDING_EXECUTED,
                    #     see=0
                    # )
                    work_order.executor_id = serializer.instance
                    work_order.status = OrderStatus.PENDING_EXECUTED
                    work_order.see = 0
                    work_order.save()

                    serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order).data

                    work_order_schedule = models.WorkOrderScheduleModel.objects.filter(
                        order_num=serialized_work_order['order_num'],
                        order_status='接单'
                    ).order_by('-create_time').first()
                    if work_order_schedule:
                        work_order_schedule.name = user.user_name
                        work_order_schedule.exec_id = serializer.instance
                        work_order_schedule.is_process = 1
                        work_order_schedule.create_time = datetime.datetime.now()
                        work_order_schedule.save()

                    else:
                        work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                            order_num=serialized_work_order['order_num'],
                            order_status='接单',
                            name=user.user_name,
                            exec_id=serializer.instance,
                            is_process=1,
                            user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                        )

                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=serialized_work_order['order_num'],
                        order_status='执行',
                        user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                    )
                    work_order_schedule.save()
                transaction.set_autocommit(True)
                return return_response(SUCCESS)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            error_log.error(traceback.format_exc())
            transaction.rollback()
            return return_response(ERROR)


class ToExamine(APIView):
    """审核"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        try:
            user = UserDetails.objects.get(id=request.user.get('user_id'))
            work_order = models.WorkOrderModel.objects.filter(id=request.data['order_id']).first()
            serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order).data
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority

            if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
                with transaction.atomic():
                    work_order.status = request.data['status']

                    # 查找是否有审核阶段的流程
                    order_schedule_zs = models.WorkOrderScheduleModel.objects.filter(
                        order_num=serialized_work_order['order_num'],
                        order_status='审核').order_by('-create_time').first()
                    # v2.0.4: 终审的审核人员增加“赵震”，即增加后有“张劭乾”和“赵震”两个审核人，需要串联审核（赵震优先）
                    if order_schedule_zs:
                        # 还未初审
                        if order_schedule_zs.first_check_name is None:
                            if user.user_name != '赵震':
                                return return_response(ERROR, "error", "数据工程师还未审核，请先等待数据工程师审核完成！！！")
                            else:
                                order_schedule_zs.name = user.user_name

                        # 已经初审
                        else:
                            order_schedule_zs.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                            order_schedule_zs.save()

                    else:
                        # 没有的话说明还在初审阶段
                        order_schedule_ys = models.WorkOrderScheduleModel.objects.filter(
                            order_num=serialized_work_order['order_num'], order_status='预审').order_by(
                            '-create_time').first()

                        if order_schedule_ys:
                            order_schedule_ys.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                            order_schedule_ys.save()

                    # 审核驳回
                    if int(request.data['status']) == OrderStatus.REJECT:
                        if order_schedule_zs:
                            if order_schedule_zs.first_check_name == '赵震':      # 已初审被驳回
                                # 判断是否为终审，终审流程修改状态修改为 -1 表示被驳回
                                order_schedule_zs.check_comments = request.data.get('check_comments')
                                order_schedule_zs.custom_comments = request.data.get('custom_comments')
                                order_schedule_zs.other_file = request.data.get('other_file')
                                order_schedule_zs.check_type = request.data.get('check_type') if request.data.get(
                                    'check_type') else order_schedule_zs.check_type
                                order_schedule_zs.is_process = -1
                                order_schedule_zs.check_usr = 0
                                order_schedule_zs.create_time = datetime.datetime.now()
                                order_schedule_zs.save()
                            else:       # 未初审被驳回
                                order_schedule_zs.first_check_name = user.user_name
                                order_schedule_zs.first_check_comments = request.data.get('check_comments')
                                order_schedule_zs.check_comments = request.data.get('check_comments')
                                order_schedule_zs.first_check_usr = 0
                                order_schedule_zs.check_usr = 0
                                order_schedule_zs.is_process = -1
                                order_schedule_zs.first_check_time = datetime.datetime.now()
                                order_schedule_zs.create_time = datetime.datetime.now()
                                order_schedule_zs.save()

                            # 流程增加 接单，回到接单操作
                            work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                                order_num=serialized_work_order['order_num'],
                                order_status='接单',
                                user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                            )
                            work_order_schedule.save()
                            # 根据最终驳回给定的 执行方和期望完成时间修改工单，工单状态修改为待接单
                            # work_order.update(
                            #     status=OrderStatus.PENDING_ORDERS,
                            #     executor_unit=request.data['executor_unit'],
                            #     finish_date=request.data['finish_date'],
                            #     see=0
                            # )
                            work_order.status = OrderStatus.PENDING_ORDERS
                            work_order.executor_unit = request.data['executor_unit']
                            work_order.finish_date = request.data['finish_date'] if request.data['finish_date'] else work_order.finish_date
                            work_order.see = 0
                            work_order.save()

                        else:
                            # work_order.update(status=OrderStatus.REJECT, see=0)
                            work_order.status = OrderStatus.REJECT
                            work_order.see = 0
                            work_order.save()
                            # 审核结果是驳回，新增驳回流程
                            order_schedule_ys.check_comments = request.data.get('check_comments')
                            order_schedule_ys.custom_comments = request.data.get('custom_comments')
                            order_schedule_ys.other_file = request.data.get('other_file')
                            order_schedule_ys.is_process = -1
                            order_schedule_ys.check_usr = 0
                            order_schedule_ys.create_time = datetime.datetime.now()
                            order_schedule_ys.save()
                            work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                                order_num=serialized_work_order['order_num'],
                                order_status='修改工单',
                                user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                            )
                            work_order_schedule.save()
                        # return return_response(SUCCESS)
                    elif int(request.data['status']) == OrderStatus.STOP:
                        # work_order.update(status=OrderStatus.STOP, todo=1, see=0)
                        work_order.status = OrderStatus.STOP
                        work_order.todo = 1
                        work_order.see = 0
                        work_order.save()
                        if order_schedule_zs:
                            if order_schedule_zs.first_check_name == '赵震':
                                order_schedule_zs.check_comments = request.data.get('check_comments')
                                order_schedule_zs.custom_comments = request.data.get('custom_comments')
                                order_schedule_zs.other_file = request.data.get('other_file')
                                order_schedule_zs.check_type = request.data.get('check_type') if request.data.get(
                                    'check_type') else order_schedule_zs.check_type
                                order_schedule_zs.is_process = -2
                                order_schedule_zs.check_usr = 0
                                order_schedule_zs.create_time = datetime.datetime.now()
                                order_schedule_zs.save()
                            else:
                                order_schedule_zs.first_check_name = user.user_name
                                order_schedule_zs.first_check_comments = request.data.get('check_comments')
                                order_schedule_zs.check_comments = request.data.get('check_comments')
                                order_schedule_zs.first_check_usr = 0
                                order_schedule_zs.check_usr = 0
                                order_schedule_zs.is_process = -2
                                order_schedule_zs.first_check_time = datetime.datetime.now()
                                order_schedule_zs.create_time = datetime.datetime.now()
                                order_schedule_zs.save()
                            # 发送邮件
                            send_email(serialized_work_order['order_num'], '抄送')
                        else:
                            order_schedule_ys.check_comments = request.data.get('check_comments')
                            order_schedule_ys.custom_comments = request.data.get('custom_comments')
                            order_schedule_ys.other_file = request.data.get('other_file')
                            order_schedule_ys.is_process = -2
                            order_schedule_ys.check_usr = 0
                            order_schedule_ys.create_time = datetime.datetime.now()
                            order_schedule_ys.save()
                            # order_schedule_jd = models.WorkOrderScheduleModel.objects.filter(
                            #     order_num=serialized_work_order['order_num'], order_status='修改工单').order_by(
                            #     '-create_time').first()
                            # if order_schedule_jd:
                            #     order_schedule_jd.delete()
                        # return return_response(SUCCESS)
                    elif int(request.data['status']) == OrderStatus.PENDING_ORDERS:
                        # work_order.update(status=OrderStatus.PENDING_ORDERS, see=0)
                        work_order.status = OrderStatus.PENDING_ORDERS
                        work_order.see = 0
                        work_order.save()

                        order_schedule_ys.is_process = 1
                        order_schedule_ys.user_id = UserDetails.objects.filter(id=request.user.get('user_id')).first()
                        order_schedule_ys.check_comments = request.data.get('check_comments')
                        order_schedule_ys.custom_comments = request.data.get('custom_comments')
                        order_schedule_ys.other_file = request.data.get('other_file')
                        order_schedule_ys.check_usr = 1
                        order_schedule_ys.create_time = datetime.datetime.now()
                        order_schedule_ys.save()
                        work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                            order_num=serialized_work_order['order_num'],
                            order_status='接单',
                            user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                        )
                        work_order_schedule.save()
                        # return return_response(SUCCESS)
                    else:
                        # work_order.update(status=OrderStatus.FINISH, todo=1, see=0)
                        if order_schedule_zs.first_check_name == '赵震':
                            work_order.status = OrderStatus.FINISH
                            work_order.todo = 1
                            work_order.see = 0
                            work_order.save()

                            order_schedule_zs.is_process = 1
                            order_schedule_zs.user_id = UserDetails.objects.filter(
                                id=request.user.get('user_id')).first()
                            order_schedule_zs.check_comments = request.data.get('check_comments')
                            order_schedule_zs.custom_comments = request.data.get('custom_comments')
                            order_schedule_zs.check_type = request.data.get('check_type') if request.data.get(
                                'check_type') else order_schedule_zs.check_type
                            order_schedule_zs.other_file = request.data.get('other_file')
                            order_schedule_zs.check_usr = 1
                            order_schedule_zs.create_time = datetime.datetime.now()
                            order_schedule_zs.save()

                            send_email(serialized_work_order['order_num'], '抄送')

                        else:
                            work_order.status = OrderStatus.EXAMINE
                            work_order.todo = 1
                            work_order.see = 0
                            work_order.save()

                            order_schedule_zs.is_process = 2
                            order_schedule_zs.first_check_name = user.user_name
                            order_schedule_zs.user_id = user
                            order_schedule_zs.check_comments = request.data.get('check_comments')
                            order_schedule_zs.first_check_comments = request.data.get('check_comments')
                            order_schedule_zs.first_check_usr = 1
                            order_schedule_zs.first_check_time = datetime.datetime.now()
                            order_schedule_zs.save()

                            send_email(serialized_work_order['order_num'], '终审')

                transaction.set_autocommit(True)  # 手动提交事务
                return return_response(SUCCESS)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            error_log.error(traceback.format_exc())
            transaction.rollback()  # 手动回滚事务
            return return_response(ERROR, '提交失败', detail=e.args[0])


class ExecContentViewSet(GenericViewSet, CreateModelMixin, UpdateModelMixin):
    """执行"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    queryset = models.ExecContentModel.objects.all()
    serializer_class = work_order_serializers.ExecContentSerializer

    def create(self, request, *args, **kwargs):
        try:
            user = UserDetails.objects.get(id=request.user.get('user_id'))
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority
            if auth_level in [OrderAuthority.SUPER, OrderAuthority.RECEIVING]:
                work_order = models.WorkOrderModel.objects.filter(id=self.request.data['order_id']).first()
                is_need_offline = request.data.get('is_need_offline') or False
                if (work_order.c_executor and work_order.c_executor.name != user.user_name) and auth_level != OrderAuthority.SUPER:
                    return return_response(ERROR, "非工单指定的接单人或超级管理员无法接单，如有疑问请联系管理员.")

                with transaction.atomic():
                    # 新增：判断接单人是否为指定的接单人或者超级管理员，否则拒绝其接单
                    serializer = self.get_serializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    self.perform_create(serializer)

                    # 删除草稿箱
                    if request.data.get('draft_id'):
                        draft = models.DraftWorkOrderModel.objects.filter(id=request.data.get('draft_id'))
                        if draft:
                            draft.delete()
                    if request.data.get('order_id'):
                        draft_query_dic = models.DraftWorkOrderModel.objects.filter(order_id=request.data.get('order_id'))
                        if draft_query_dic:
                            draft_query_dic.delete()
                    # serializer = self.get_serializer(data=request.data)
                    # serializer.is_valid(raise_exception=True)
                    # self.perform_create(serializer)
                    # work_order = models.WorkOrderModel.objects.filter(id=self.request.data['order_id'])
                    # work_order.update(
                    #     status=OrderStatus.LAST_EXAMINE,
                    #     see=0
                    # )
                    work_order.status = OrderStatus.LAST_EXAMINE
                    work_order.see = 0
                    work_order.save()

                    serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order).data
                    work_order_schedule = models.WorkOrderScheduleModel.objects.filter(
                        order_num=serialized_work_order['order_num'],
                        order_status='执行'
                    ).order_by('-create_time').first()

                    if work_order_schedule:
                        work_order_schedule.exec_id = models.ExecutorModel.objects.filter(
                            id=serialized_work_order['executor_id']['id']).first()
                        work_order_schedule.exec_content_id = serializer.instance
                        work_order_schedule.is_process = 1
                        work_order_schedule.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                        work_order_schedule.create_time = datetime.datetime.now()
                        work_order_schedule.save()

                    else:

                        work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                            order_num=serialized_work_order['order_num'],
                            order_status='执行',
                            exec_id=models.ExecutorModel.objects.filter(
                                id=serialized_work_order['executor_id']['id']).first(),
                            exec_content_id=serializer.instance,
                            is_process=1,
                            name=UserDetails.objects.get(id=request.user.get('user_id')).user_name)

                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=serialized_work_order['order_num'],
                        order_status='审核',
                        user_id=UserDetails.objects.get(id=request.user.get('user_id')),
                    )
                    work_order_schedule.save()
                transaction.set_autocommit(True)
                # 发送邮件
                send_email(serialized_work_order['order_num'], '初审')
                if is_need_offline:
                    stations = models.WorkOrderStationInfoModel.objects.filter(work_order=work_order).all()
                    stations_str = '、'.join([station.station_name for station in stations])
                    send_email(serialized_work_order['order_num'], '下线抄送', stations_str)
                return return_response(SUCCESS)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            error_log.error(traceback.format_exc())
            transaction.rollback()
            return return_response(ERROR)

    def update(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                partial = kwargs.pop('partial', False)
                instance = self.get_object()
                serializer = self.get_serializer(instance, data=request.data, partial=partial)
                serializer.is_valid(raise_exception=True)
                self.perform_update(serializer)
                work_order = models.WorkOrderModel.objects.filter(id=self.request.data['order_id'])
                work_order.update(status=OrderStatus.LAST_EXAMINE, see=0)
                serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order, many=True).data
                work_order_schedule = models.WorkOrderScheduleModel.objects.filter(
                    order_num=serialized_work_order[0]['order_num'],
                    order_status='执行信息修改'
                ).order_by('-create_time').first()
                work_order_schedule.exec_content_id = serializer.instance
                work_order_schedule.is_process = 1
                work_order_schedule.name = UserDetails.objects.get(id=request.user.get('user_id')).user_name
                work_order_schedule.create_time = datetime.datetime.now()
                work_order_schedule.save()
                work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                    order_num=serialized_work_order[0]['order_num'],
                    order_status='审核'
                )
                work_order_schedule.save()

                if getattr(instance, '_prefetched_objects_cache', None):
                    # If 'prefetch_related' has been applied to a queryset, we need to
                    # forcibly invalidate the prefetch cache on the instance.
                    instance._prefetched_objects_cache = {}

            transaction.set_autocommit(True)

            return return_response(serializer.data)
        except Exception as e:
            error_log.error(e)
            transaction.rollback()
            return return_response(ERROR)


class GetUserGroupViewSet(GenericViewSet, ListModelMixin, CreateModelMixin):
    """获取权限信息"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    serializer_class = work_order_serializers.WorkOrderAuthoritySerializer

    def list(self, request, *args, **kwargs):
        queryset = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
        # queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        try:
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority
            if auth_level == OrderAuthority.SUPER:
                user_auth = request.data.get('user_authority')
                unit_group = request.data.get('unit_group')
                user_name = request.data.get('user_name')
                if user_auth and unit_group and user_name:
                    with transaction.atomic():
                        user_obj = models.UserDetails.objects.filter(user_name=user_name).first()
                        auth_obj = models.WorkOrderAuthorityModel.objects.create(
                            user_id=user_obj,
                            user_authority=user_auth,
                            unit_group=unit_group
                        )
                        auth_obj.save()
                    transaction.set_autocommit(True)
                    return return_response(SUCCESS)
                else:
                    return return_response(ERROR)
            else:
                return return_response(AUTHENTICATION_FIELD)
        except Exception as e:
            error_log.error(e)
            transaction.rollback()
            return return_response(ERROR)


class RevokeView(APIView):
    """
    撤回
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        try:
            user = UserDetails.objects.get(id=request.user.get('user_id'))
            work_order = models.WorkOrderModel.objects.filter(id=request.data['order_id']).first()
            serialized_work_order = work_order_serializers.WorkOrderSerializer(work_order).data
            auth_query_dic = models.WorkOrderAuthorityModel.objects.filter(user_id=request.user.get('user_id'))
            if not auth_query_dic:
                return return_response(AUTHENTICATION_FIELD)
            auth_level = auth_query_dic.get().user_authority

            if auth_level in [OrderAuthority.SUPER, OrderAuthority.DISPATCH, OrderAuthority.EXAMINE]:
                with transaction.atomic():
                    # 工单状态修改为："待预审"
                    work_order.status = OrderStatus.REJECT
                    work_order.save()

                    # 将未处理的流程改为“已处理”
                    has_order_schedules = models.WorkOrderScheduleModel.objects.filter(
                        order_num=serialized_work_order['order_num'],
                        is_process=0
                    ).all()
                    if has_order_schedules.exists():
                        for schedule in has_order_schedules:
                            schedule.is_process = 1
                            schedule.save()

                    # 流程增加 "撤回"，回到"修改"操作
                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=serialized_work_order['order_num'],
                        order_status='撤回',
                        is_process=1,
                        name=user.user_name
                    )
                    work_order_schedule = models.WorkOrderScheduleModel.objects.create(
                        order_num=serialized_work_order['order_num'],
                        order_status='修改工单',
                        user_id=UserDetails.objects.get(id=request.user.get('user_id'))
                    )
                    work_order_schedule.save()
                transaction.set_autocommit(True)
                return return_response(SUCCESS, "撤回成功")
            else:
                return return_response(AUTHENTICATION_FIELD, "无权限")
        except Exception as e:
            error_log.error(e)
            error_log.error(traceback.format_exc())
            transaction.rollback()
            return return_response(ERROR, "撤回失败")


class ExecutorCompsView(APIView):
    """公司"""""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        try:
            # executor_unit = request.query_params.get('executor_unit', 0)

            comps = models.WorkOrderCompModel.objects.all()

            serializer = work_order_serializers.WorkOrderCompSerializer(comps, many=True)

            return Response({"code": 0, "data": {"message": "success", "detail": serializer.data}})

        except Exception as e:
            error_log.error(traceback.print_exc())
            return return_response(ERROR, "查询失败")


class CompExecutorsView(APIView):
    """
    公司售后人员名单
    """""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        try:
            executor_unit = request.query_params.get('executor_unit', 0)

            comp_executors = models.WorkOrderCompExecutorModel.objects.filter(executor_unit=executor_unit).all()

            serializer = work_order_serializers.WorkOrderCompExecutorSerializer(comp_executors, many=True)

            return Response({"code": 0, "data": {"message": "success", "detail": serializer.data}})

        except Exception as e:
            error_log.error(traceback.print_exc())
            return return_response(ERROR, "查询失败")
