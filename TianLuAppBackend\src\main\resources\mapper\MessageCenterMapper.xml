<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianlu.mapper.MessageCenterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tianlu.entity.MessageCenter">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="is_read" property="isRead"/>
        <result column="is_handle" property="isHandle"/>
        <result column="is_verify" property="isVerify"/>
        <result column="files" property="files"/>
        <result column="opinion" property="opinion"/>
        <result column="user_id" property="userId"/>
        <result column="alarm_id" property="alarmId"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="station_id" property="stationId"/>
        <result column="related_id" property="relatedId"/>
        <result column="issue_user" property="issueUser"/>
        <result column="en_title" property="enTitle"/>
        <result column="en_opinion" property="enOpinion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, title, type, is_read, is_handle, is_verify, files, opinion,
        user_id, alarm_id, strategy_id, station_id, related_id, issue_user, en_title, en_opinion
    </sql>

</mapper> 