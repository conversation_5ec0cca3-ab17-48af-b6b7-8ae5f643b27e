# -*- coding: utf-8 -*-
import os
import mmap
import contextlib
import sys
import time
import logging
import inspect
import threading
import configparser

from Tools.Cfg.get_cnf import work_dir
from logging.handlers import RotatingFileHandler


def createHandlers(handlers):
    logLevels = handlers.keys()
    for level in logLevels:
        path = os.path.abspath(handlers[level])
        handlers[level] = RotatingFileHandler(path, maxBytes=10000, backupCount=2, encoding='utf-8')
    return handlers


# 创建存储模块文件夹
def create_dir_not_exist(path):
    if not os.path.exists(path):
        os.mkdir(path)


class MyConfigParser(configparser.ConfigParser):
    def __init__(self, defaults=None):
        configparser.ConfigParser.__init__(self, defaults=defaults)

    def get_config_str(self):
        config_str = ''
        for section in self._sections:
            config_str += self._get_my_section(section, self._sections[section].items())
        return config_str

    def _get_my_section(self, section_name, section_items):
        config_str = ''
        config_str += "[{0}]\n".format(section_name)
        for key, value in section_items:
            value = self._interpolation.before_write(self, section_name, key, value)
            if value is not None or not self._allow_no_value:
                value = ' = ' + str(value).replace('\n', '\n\t')
            else:
                value = ""
            config_str += "{0}{1}\n".format(key, value)
        config_str += "\n"
        return config_str


class TNLog(object):

    def __init__(self, handlers, modular, level_int=10):
        """
        : handlers 模块名字
        : level_int 日志级别
         1. CRITICAL:50,
         2. ERROR:40,
         3. WARNING:30,
         4. INFO: 20,
         5. DEBUG: 10
        """
        self.__loggers = {}
        self.name = modular
        self.level_int = level_int
        logLevels = handlers.keys()
        for level in logLevels:
            logger = logging.getLogger(str(level))
            # 如果不指定level，获得的handler似乎是同一个handler?
            logger.addHandler(handlers[level])
            logger.setLevel(level_int)
            print (level_int, logger)

            self.__loggers.update({level: logger})

    def change_level(self, level_int=10):
        self.level_int = level_int
        loggers = self.__loggers
        if loggers:
            for l in loggers:
                loggers[l].setLevel(level_int)

    def printfNow(self):
        return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())

    def getLogMessage(self, level, message):
        frame, filename, lineNo, functionName, code, unknowField = inspect.stack()[2]

        '''日志格式：[时间] [记录代码] 信息'''

        return "[%s]  [%s - %s - %s] %s" % (self.printfNow(), filename, lineNo, functionName, message)

    def info(self, message):
        message = self.getLogMessage("info", message)
        self.__loggers[logging.INFO].info(message)

    def error(self, message):
        message = self.getLogMessage("error", message)
        self.__loggers[logging.ERROR].error(message)

    def warning(self, message):
        message = self.getLogMessage("warning", message)
        self.__loggers[logging.WARNING].warning(message)

    def debug(self, message):
        message = self.getLogMessage("debug", message)
        self.__loggers[logging.DEBUG].debug(message)

    def critical(self, message):
        message = self.getLogMessage("critical", message)
        self.__loggers[logging.CRITICAL].critical(message)


# 日志工厂统一管理日志
class LogFactory(object):
    _instance_lock = threading.Lock()
    log_dict = {}

    def __new__(cls, *args, **kwargs):
        if not hasattr(LogFactory, "_instance"):
            with LogFactory._instance_lock:
                if not hasattr(LogFactory, "_instance"):
                    LogFactory._instance = object.__new__(cls)
        return LogFactory._instance

    def __init__(self):
        pass

    def change_modular_level(self, modular, level_int=10):

        """
        修改模块日志等级
        : modular 日志模块名字 传None修改全部
        : level_int 等级
         1. CRITICAL:50,
         2. ERROR:40,
         3. WARNING:30,
         4. INFO: 20,
         5. DEBUG: 10
         return 成功->True,失败->False
        """
        if not modular:
            for key in self.log_dict:
                self.log_dict[key].change_level(level_int)
            return True
        else:
            if modular not in self.log_dict.keys():
                return False
            self.log_dict[modular].change_level(level_int)
            return True

    def add_log(self, modular, level_int=20):
        """ 创建日志实例
        :modular : 模块名称
        :level_int : 日志等级
         1. CRITICAL:50,
         2. ERROR:40,
         3. WARNING:30,
         4. INFO: 20,
         5. DEBUG: 10
        """
        # 读取共享内存配置
        parser = MyConfigParser()
        memory_config = read_config_file()
        parser.read_string(memory_config.decode('utf-8'))
        if not parser.has_section(modular):
            parser.add_section(modular)
            parser.set(modular, 'level_int', str(level_int))
        # 如果配置文件有则使用配置文件配置
        level_int = int(parser.get(modular, 'level_int').encode('utf8'))
        if modular in self.log_dict:
            raise Exception('创建日志模块已存在')
        project = sys.argv[0].split('.')[0]
        path = os.path.join(work_dir, 'log', project, modular)
        project_path = os.path.join(work_dir, 'log', project)
        create_dir_not_exist(project_path)
        create_dir_not_exist(path)
        dir_time = time.strftime('%Y-%m-%d', time.localtime())
        handlers = {logging.DEBUG: os.path.join(work_dir, 'log', project, modular, 'debug_%s.log' % dir_time),
                    logging.INFO: os.path.join(work_dir, 'log', project, modular, 'info_%s.log' % dir_time),
                    logging.WARNING: os.path.join(work_dir, 'log', project, modular, 'warning_%s.log' % dir_time),
                    logging.ERROR: os.path.join(work_dir, 'log', project, modular, 'error_%s.log' % dir_time),
                    logging.CRITICAL: os.path.join(work_dir, 'log', project, modular, 'critical_%s.log' % dir_time),
                    }
        handlers = createHandlers(handlers)
        log = TNLog(handlers, modular, level_int)
        self.log_dict[modular] = log
        write_config_file(parser.get_config_str())
        return log


log_config_path = os.path.join(work_dir, 'Tools', 'Cfg/LogConfig.ini')


# 写入配置文件
def write_config_file(config):
    with open(log_config_path, 'r+') as f:
        with contextlib.closing(mmap.mmap(f.fileno(), 1024, access=mmap.ACCESS_WRITE)) as m:
            m.seek(0)
            m.write('\x00' * 1024)
            m.seek(0)
            m.write(config)
            m.flush()


# 读取配置内容
def read_config_file():
    try:
        with open(log_config_path, 'r') as f:
            with contextlib.closing(mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)) as m:
                s = m.read(-1).replace('\x00', '')
                return s
    except Exception as E:
        # 写入默认配置
        with open(log_config_path, 'w') as f:
            f.write('\x00' * 1024)
        write_config_file('[whole] \nlevel_int = 20 \n')
        with open(log_config_path, 'r') as f:
            with contextlib.closing(mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)) as m:
                s = m.read(-1).replace('\x00', '')
                return s




log_manager = LogFactory()


def init_config():
    parser = MyConfigParser()
    memory_config = read_config_file()
    parser.read_string(memory_config.decode('utf-8'))
    for i in log_manager.log_dict:
        if parser.has_section(i):
            level = parser.get(i, 'level_int')
            if str(level) != str(log_manager.log_dict[i].level_int):
                log_manager.log_dict[i].change_level(int(level))










