#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-01-04 08:59:49
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WorkOrder\dispatch_model.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-07 09:57:30


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.WorkOrder.dispatch_type import DispatchType
from Application.Models.User.user import User
from Application.Models.User.organization import Organization

class DispatchModel(user_Base):
    u'工单模板表'
    __tablename__ = "t_dispatch_model"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"模板名称")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    create_user = Column(Integer, nullable=False,comment=u"创建者")
    organization_id = Column(Integer, nullable=False,comment=u"所属组织id")
    dispatch_type = Column(Integer,ForeignKey("t_dispatch_type.id"), nullable=False,comment=u"模板类型id")
    remarks = Column(String(256), nullable=True, comment=u"备注")
    imgs = Column(String(256), nullable=True, comment=u"图片，多个用#号分割")
    files = Column(String(256), nullable=True, comment=u"文件，多个用#号分割")
    working_flag = Column(Integer, nullable=True,comment=u"工单模板类型，1工程服务申请单,2工作流程3计划性工作")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")

    en_descr = Column(String(256), nullable=False, comment=u"英文模板名称")
    en_remarks = Column(String(256), nullable=True, comment=u"备注")

    model_type= relationship("DispatchType", backref="model_type")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        CU = user_session.query(User).filter(User.id==self.create_user,User.unregister==1).first()
        org = user_session.query(Organization).filter(Organization.id==self.organization_id).first()
        create_descr = CU.name if CU else ''
        organization_descr = org.descr if org else ''
        dispatch_type_descr = self.model_type.descr if self.model_type else ''
       
        bean = "{'id':%s,'descr':'%s','op_ts':'%s','create_user':%s,'create_descr':'%s','organization_id':%s,'organization_descr':'%s','dispatch_type':%s,'dispatch_type_descr':'%s',\
            'imgs':'%s','files':'%s','isUse':%s,'working_flag':%s,'en_descr':'%s','en_remarks':'%s'}" % (self.id,self.descr,self.op_ts,self.create_user,create_descr,self.organization_id,organization_descr,self.dispatch_type,
            dispatch_type_descr,self.imgs,self.files,self.is_use,self.working_flag,self.en_descr,self.en_remarks)
        return bean.replace("None",'')
        
    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}