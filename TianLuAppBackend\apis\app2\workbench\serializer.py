import json

from rest_framework import serializers, exceptions

from apis.statistics_apis.models import UserStrategy, Month
from apis.user import models
from LocaleTool.common import redis_pool


class ResponseUserStrategySerializer(serializers.ModelSerializer):
    """用户控制策略"""""
    is_show = serializers.IntegerField(write_only=False, read_only=False)
    status = serializers.IntegerField(write_only=False, read_only=False)
    class Meta:
        model = UserStrategy
        # exclude = ['user']
        fields = "__all__"

class UserStrategySerializer(serializers.ModelSerializer):
    """用户控制策略"""""
    # name = serializers.CharField(required=True, max_length=128)
    # is_delete = serializers.IntegerField(write_only=False, read_only=False)

    class Meta:
        model = UserStrategy
        # exclude = ['user']
        fields = "__all__"
        extra_kwargs = {
            "name": {"required": True, },
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }
        # read_only_fields = ('id', 'name')

    def validate_name(self, value):
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        strategy = UserStrategy.objects.filter(user=user_ins, name=value, is_delete=0)
        if strategy:
            raise exceptions.ValidationError("策略名已存在")
        if value == '当前策略':
            raise exceptions.ValidationError("策略名称违规！")
        return value

    def update(self, instance, validated_data):
        lang = self.context.get('lang', 'zh')
        instance = instance.update(en_name=validated_data.get('name'), **validated_data)

        # 异步翻译
        pdr_data = {'id': instance.id,
                    'table': 't_user_strategy',
                    'update_data': {'name': validated_data.get('name')}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        # instance.save()
        return instance

    def create(self, validated_data):
        lang = self.context.get('lang', 'zh')
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        obj = UserStrategy.objects.create(user=user_ins, **validated_data)

        # 异步翻译
        pdr_data = {'id': obj.id,
                    'table': 't_user_strategy',
                    'update_data': {'name': validated_data.get('name')}}

        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
        redis_pool.publish(pub_name, json.dumps(pdr_data))

        for i in range(1, 13):
            Month.objects.create(strategy=obj, month_number=i)

        return obj


class CurrentStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)

    def validate_station(self, value):
        # exist = models.StationDetails.objects.filter(english_name=value)
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=value)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value


class DefaultStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)

    def validate_station(self, value):
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=value)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value

        #     return -1
        # return exist.id, exist.province, exist.type, exist.level, exist.rated_power


class CustomizeStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)
    strategy_id = serializers.IntegerField(required=True, write_only=True)

    def validate_station(self, value):
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=value)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value

        # exist = models.StationDetails.objects.filter(english_name=station).first()
        # if not exist:
        #     return -1
        # return exist.id, exist.province, exist.type, exist.level, exist.rated_power


class IncomeSerializer(serializers.Serializer):
    date = serializers.DateTimeField(input_formats=['%Y-%m-%d'], required=True)
    income_type = serializers.IntegerField(required=False)
    day_income = serializers.IntegerField(required=True)
    english_name = serializers.CharField(required=True)
    notes = serializers.CharField(required=False, default=None)


class AddIncomesSerializer(serializers.Serializer):
    data = IncomeSerializer(many=True)


class PeakValleySerializer(serializers.ModelSerializer):
    """削峰填谷电价序列化器"""

    type_display = serializers.SerializerMethodField()
    level_display = serializers.SerializerMethodField()
    province_id = serializers.SerializerMethodField()

    # province_name = serializers.CharField(source='province.name', read_only=True)
    # province_id = serializers.CharField(source='province.id', read_only=True)

    class Meta:
        model = models.PeakValleyNew
        fields = ["type_display", "level_display", "type", "level", "province_id"]

    def get_province_id(self, obj):
        return obj['province']

    def get_level_display(self, obj):
        LEVEL_CHOICE = dict(models.PeakValleyNew.LEVEL_CHOICE)
        return LEVEL_CHOICE.get(obj['level'], '')

    def get_type_display(self, obj):
        TYPE_CHOICE = dict(models.PeakValleyNew.TYPE_CHOICE)
        return TYPE_CHOICE.get(obj['type'], '')


# class CustomizationDetailSerializer(serializers.ModelSerializer):
#     id = serializers.IntegerField(required=True, write_only=True)
#     # province_id = serializers.IntegerField(required=True, write_only=True)
#     # level = serializers.IntegerField(required=True, write_only=True)
#     # type = serializers.IntegerField(required=True, write_only=True)
#
#     price_spike = serializers.SerializerMethodField()  # 尖峰
#
#     price_peak = serializers.SerializerMethodField()  # 峰
#     price_flat = serializers.SerializerMethodField()  # 平
#     price_valley = serializers.SerializerMethodField()  # 谷
#
#     class Meta:
#         model = models.PeakValleyNew
#         fields = [
#             "price_spike",
#             "price_peak",
#             "price_flat",
#             "price_valley",
#             'id',
#             'h0',
#             'h1',
#             'h2',
#             'h3',
#             'h4',
#             'h5',
#             'h6',
#             'h7',
#             'h8',
#             'h9',
#             'h10',
#             'h11',
#             'h12',
#             'h13',
#             'h14',
#             'h15',
#             'h16',
#             'h17',
#             'h18',
#             'h19',
#             'h20',
#             'h21',
#             'h2',
#             'h23',
#         ]
#         extra_kwargs = {
#             'level': {'required': True, 'write_only': True},
#             'type': {'required': True, 'write_only': True},
#             'h0': {'read_only': True},
#             'h1': {'read_only': True},
#             'h2': {'read_only': True},
#             'h3': {'read_only': True},
#             'h4': {'read_only': True},
#             'h5': {'read_only': True},
#             'h6': {'read_only': True},
#             'h7': {'read_only': True},
#             'h8': {'read_only': True},
#             'h9': {'read_only': True},
#             'h10': {'read_only': True},
#             'h11': {'read_only': True},
#             'h12': {'read_only': True},
#             'h13': {'read_only': True},
#             'h14': {'read_only': True},
#             'h15': {'read_only': True},
#             'h16': {'read_only': True},
#             'h17': {'read_only': True},
#             'h18': {'read_only': True},
#             'h19': {'read_only': True},
#             'h20': {'read_only': True},
#             'h21': {'read_only': True},
#             'h22': {'read_only': True},
#             'h23': {'read_only': True},
#         }
#
#     def get_price_spike(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 2:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_peak(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 1:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_flat(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if int(getattr(obj, f'pv{i}')) == 0:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price
#
#     def get_price_valley(self, obj):
#         price = None
#         for i in range(0, 23):
#             if price:
#                 return price
#             if getattr(obj, f'pv{i}') == -1:
#                 price = getattr(obj, f'h{i}')
#         if not price:
#             price = "___"
#         return price


class CustomizationAddSerializer(serializers.ModelSerializer):
    """
    削峰填谷客制化添加
    """""

    stations_list = serializers.ListSerializer(child=serializers.IntegerField(), required=False)

    class Meta:
        model = models.UnitPrice
        exclude = ["user", "station"]
        extra_kwargs = {"uid": {"required": False}}

    # def create(self, validated_data):
    #     user = self.context.get('user_id')
    #     user_ins = models.UserDetails.objects.get(id=user)
    #     obj = models.UnitPrice.objects.create(user=user_ins, **validated_data)
    #     return obj

    def validate_name(self, value):
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        exist = models.UnitPrice.objects.filter(name=value, delete=0, user=user_ins).exists()
        if exist:
            raise Exception("单位电价名称已存在")
        return value

    def validate(self, attrs):
        start = attrs["start"]
        end = attrs["end"]
        name = attrs["name"]
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        # 校验时间
        if end < start:
            raise Exception("结束时间不能早于起始时间")

        if start == end:
            raise Exception("开始时间和结束时间不能选择同一天")

        for station_id in attrs["stations_list"]:
            tim_ins = models.UnitPrice.objects.filter(station=station_id, delete=0, user=user_ins).all().values("start", 'end', "name")
            
            for tim in tim_ins:
                if tim["start"] <= start <= tim['end'] or tim['start'] <= end <= tim["end"]:
                    raise Exception(f"<{name}>时间段与已存在的<{tim['name']}>发生冲突")

        return attrs