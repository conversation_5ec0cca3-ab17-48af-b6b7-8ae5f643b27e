#!/usr/bin/env python
# coding=utf-8
#@Information:  备件管理
#<AUTHOR> WYJ
#@Date         : 2023-03-15 15:12:44
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WorkOrder\workOrderHandle copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-15 15:12:47


import os
import tornado.web
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.WorkOrder.spare_info import SpareInfo
from Application.Models.WorkOrder.spare_in import SpareIn
from Application.Models.WorkOrder.spare_out import SpareOut
from Tools.Utils.num_utils import Translate_cls
from Application.Models.WorkOrder.spare_disk import SpareDisk
import logging,json
from sqlalchemy import func
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
import pandas as pd

file_path = '/home/<USER>/workorderfiles'
file_title = ['序号','备件名称','规格型号','厂家','主设备','单位','初始数量']
en_file_title = ['Serial Number', 'Spare part name', 'Specification and model', 'Manufacturer',
                 'Master', 'unit', 'Initial quantity']
class SpareManagerIntetface(BaseHandler):
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
            if kt == 'GetSpareInfos':  # 备件查询
                name = self.get_argument('name',None)  # 备件名
                manufacturer = self.get_argument('acc',None) # 厂家
                spec = self.get_argument('type',None)  # 规格型号
                device = self.get_argument('mainStation',None)   # 主设备
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                station = self.get_argument('db','')   # 所属站
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'

                if DEBUG:
                    logging.info('name:%s,acc:%s,type:%s,mainStation:%s,pageNum:%s,pageSize:%s,station:%s'%(name,manufacturer,spec,device,pageNum,pageSize,station))
                filter,data = [SpareInfo.is_use==1],[]
                if lang == 'zh':
                    if name:
                        filter.append(SpareInfo.name.like('%' + name + '%'))
                    if manufacturer:
                        filter.append(SpareInfo.manufacturer.like('%' + manufacturer + '%'))
                    if spec:
                        filter.append(SpareInfo.spec.like('%' + spec + '%'))
                    if device:
                        filter.append(SpareInfo.device.like('%' + device + '%'))
                else:
                    if name:
                        filter.append(SpareInfo.en_name.like('%' + name + '%'))
                    if manufacturer:
                        filter.append(SpareInfo.en_manufacturer.like('%' + manufacturer + '%'))
                    if spec:
                        filter.append(SpareInfo.en_spec.like('%' + spec + '%'))
                    if device:
                        filter.append(SpareInfo.en_device.like('%' + device + '%'))
                if station:
                    filter.append(SpareInfo.station==station)

                total = user_session.query(func.count(SpareInfo.id)).filter(*filter).scalar()
                pages = user_session.query(SpareInfo).filter(*filter).order_by(SpareInfo.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                if lang == 'en':
                    for pag in pages:
                        pag = pag.to_dict()
                        pag['name'] = pag.get('en_name')
                        pag['acc'] = pag.get('en_manufacturer')
                        pag['mainStation'] = pag.get('en_device')
                        pag['unit'] = pag.get('en_unit')
                        pag['create_descr'] = pag.get('en_create_descr')
                        pag['remarks'] = pag.get('en_remarks')
                        pag['station'] = pag.get('en_station')
                        pag['type'] = pag.get('en_spec')
                        data.append(pag)
                else:
                    for pag in pages:
                        data.append(pag.to_dict())
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetSpareInfoIns':  # 查询入库详情
                # startTime = self.get_argument('startTime', None)  # 开始时间YYYY-mm-dd
                # endTime = self.get_argument('endTime', None)  # 截止时间YYYY-mm-dd
                Time = eval(self.get_argument('timeLine', '[]'))  # 时间
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                filter,data = [SpareIn.is_use==1],[]
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if Time:
                    filter.append(SpareIn.in_time.between(Time[0],Time[1]))

                total = user_session.query(func.count(SpareIn.id)).filter(*filter).scalar()
                pages = user_session.query(SpareIn).filter(*filter).order_by(SpareIn.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if lang == 'en':
                    for pag in pages:
                        p = eval(str(pag))
                        p['name'] = pag.spare_info_in.en_name
                        p['acc'] = pag.spare_info_in.en_manufacturer
                        p['type'] = pag.spare_info_in.spec
                        p['mainStation'] = pag.spare_info_in.en_device
                        p['unit'] = pag.spare_info_in.en_unit
                        p['source'] = pag.en_in_source
                        p['descr'] = pag.en_in_descr
                        p['remark'] = pag.en_remarks
                        data.append(p)
                else:
                    for pag in pages:
                        p = eval(str(pag))
                        p['name'] = pag.spare_info_in.name
                        p['acc'] = pag.spare_info_in.manufacturer
                        p['type'] = pag.spare_info_in.spec
                        p['mainStation'] = pag.spare_info_in.device
                        p['unit'] = pag.spare_info_in.unit
                        data.append(p)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetSpareInfoOuts':  # 查询出库详情
                # startTime = self.get_argument('startTime', None)  # 开始时间YYYY-mm-dd
                # endTime = self.get_argument('endTime', None)  # 截止时间YYYY-mm-dd
                Time = eval(self.get_argument('timeLine', '[]'))  # 时间
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                filter,data = [SpareOut.is_use==1],[]
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'

                if Time:
                    filter.append(SpareOut.out_time.between(Time[0],Time[1]))
                total = user_session.query(func.count(SpareOut.id)).filter(*filter).scalar()
                pages = user_session.query(SpareOut).filter(*filter).order_by(SpareOut.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if lang == 'en':
                    for pag in pages:
                        p = eval(str(pag))
                        p['name'] = pag.spare_info_out.en_name
                        p['acc'] = pag.spare_info_out.en_manufacturer
                        p['type'] = pag.spare_info_out.spec
                        p['mainStation'] = pag.spare_info_out.en_device
                        p['unit'] = pag.spare_info_out.en_unit
                        p['source'] = pag.en_out_source
                        p['descr'] = pag.en_out_descr
                        p['remark'] = pag.en_remarks
                        data.append(p)
                else:
                    for pag in pages:
                        p = eval(str(pag))
                        p['name'] = pag.spare_info_out.name
                        p['acc'] = pag.spare_info_out.manufacturer
                        p['type'] = pag.spare_info_out.spec
                        p['mainStation'] = pag.spare_info_out.device
                        p['unit'] = pag.spare_info_out.unit
                        data.append(p)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetSpareInfoDisk':  # 库存盘点
                Time = self.get_argument('dtime', None)  # 时间YYYY-mm
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                station = self.get_argument('db','')   # 所属站
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not Time:
                    return self.customError("入参不完整！") if lang == 'zh' else self.customError("Incomplete input parameters!")
                Time = Time[:7]
                startM = '%s-01 00:00:00'%Time  # 每月的开始时间
                endM = '%s 23:59:59'%timeUtils.last_day_of_month(int(Time[:4]),int(Time[5:]))  # 每月截止时间
                total = user_session.query(func.count(SpareInfo.id)).filter(SpareInfo.is_use==1,SpareInfo.station==station).scalar()
                pages = user_session.query(SpareInfo).filter(SpareInfo.is_use==1,SpareInfo.station==station).order_by(SpareInfo.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                data = []
                if lang == 'en':
                    for pag in pages:
                        p = eval(str(pag))
                        p['before_num'] = 0
                        p['now_num'] = 0
                        p['acc'] = pag.en_manufacturer
                        p['create_descr'] = pag.en_create_descr
                        p['mainStation'] = pag.en_device
                        p['mainStation'] = pag.en_device
                        p['station'] = p.get('en_station')
                        p['unit'] = pag.en_unit
                        p['name'] = pag.en_name
                        p['type'] = pag.en_spec
                        disk = user_session.query(SpareDisk).filter(SpareDisk.id == pag.id, SpareDisk.year_mon == Time,
                                                                    SpareDisk.is_use == 1).first()
                        if disk and disk.before_num:
                            p['before_num'] = disk.before_num
                        spin = user_session.query(SpareIn.num.label('num'), SpareIn.en_remarks.label('remarks')).filter(
                            SpareIn.id == pag.id, SpareIn.in_time.between(startM, endM), SpareIn.is_use == 1).all()
                        spout = user_session.query(SpareOut.num.label('num'), SpareOut.en_remarks.label('remarks')).filter(
                            SpareOut.id == pag.id, SpareOut.out_time.between(startM, endM), SpareOut.is_use == 1).all()
                        in_, out_, remarks = 0, 0, ''

                        for i in spin:
                            in_ = in_ + i.num
                            remarks = remarks + i.remarks + ';' if i.remarks else remarks
                        for j in spout:
                            out_ = out_ + j.num
                            remarks = remarks + j.remarks + ';' if j.remarks else remarks
                        p['now_in_num'] = in_
                        p['now_out_num'] = out_
                        p['remarks'] = remarks
                        pas = user_session.query(SpareInfo).filter(SpareInfo.id == pag.id, SpareInfo.op_ts <= endM,
                                                                   SpareInfo.is_use == 1).order_by(
                            SpareInfo.op_ts.desc()).first()
                        if pas:
                            p['now_num'] = pas.num

                        data.append(p)
                else:
                    for pag in pages:
                        p = eval(str(pag))
                        p['before_num'] = 0
                        p['now_num'] = 0
                        disk = user_session.query(SpareDisk).filter(SpareDisk.id==pag.id,SpareDisk.year_mon==Time,SpareDisk.is_use==1).first()
                        if disk and disk.before_num:
                            p['before_num'] = disk.before_num
                        spin = user_session.query(SpareIn.num.label('num'),SpareIn.remarks.label('remarks')).filter(SpareIn.id==pag.id,SpareIn.in_time.between(startM,endM),SpareIn.is_use==1).all()
                        spout = user_session.query(SpareOut.num.label('num'),SpareOut.remarks.label('remarks')).filter(SpareOut.id==pag.id,SpareOut.out_time.between(startM,endM),SpareOut.is_use==1).all()
                        in_,out_,remarks = 0,0,''

                        for i in spin:
                            in_ = in_+i.num
                            remarks = remarks+i.remarks+';' if i.remarks else remarks
                        for j in spout:
                            out_ = out_+j.num
                            remarks = remarks+j.remarks+';' if j.remarks else remarks
                        p['now_in_num'] = in_
                        p['now_out_num'] = out_
                        p['remarks'] = remarks
                        pas = user_session.query(SpareInfo).filter(SpareInfo.id==pag.id,SpareInfo.op_ts<=endM,SpareInfo.is_use==1).order_by(SpareInfo.op_ts.desc()).first()
                        if pas:
                            p['now_num'] = pas.num

                        data.append(p)

                return self.returnTotalSuccess(data,total)

        except Exception as E:
            user_session.close()
            logging.error(E)
            return self.requestError()
        finally:
            pass

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            # db = self.get_argument('db','his')  # 所属站
            if kt == 'AddSpareInfos':  # 添加备件
                name = self.get_argument('name',None)  # 备件名
                manufacturer = self.get_argument('acc',None) # 厂家
                spec = self.get_argument('type',None)  # 规格型号
                device = self.get_argument('mainStation',None)   # 主设备
                num = self.get_argument('RealNum',0)   # 数量
                unit = self.get_argument('unit',None)   # 单位
                remarks = self.get_argument('remarks','')   # 备注
                station = self.get_argument('db','')   # 所属站
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('name:%s,acc:%s,type:%s,mainStation:%s,num:%s,unit:%s,remarks:%s,station:%s'%(name,manufacturer,spec,device,num,unit,remarks,station))
                if not name:
                    return self.customError("入参不完整！") if lang == 'zh' else self.customError("Incomplete input parameters!")

                page = user_session.query(SpareInfo).filter(SpareInfo.name==name,SpareInfo.manufacturer==manufacturer,SpareInfo.spec==spec,SpareInfo.device==device,SpareInfo.is_use==1,SpareInfo.station==station).first()

                if page:  # 重复
                    logging.info("add data is in db")
                    return self.customError('数据已存在') if lang == 'zh' else self.customError("Data already exists")
                remarks = ';'.join(remarks.split()) if remarks else ''
                session = self.getOrNewSession()
                us = session.user['name']

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                name_res = t_cls.str_chinese(name)
                manufacturer_res = t_cls.str_chinese(manufacturer)
                device_res = t_cls.str_chinese(device) if device else None
                unit_res = t_cls.str_chinese(unit)
                create_descr_res = t_cls.str_chinese(us)
                remarks_res = t_cls.str_chinese(remarks)
                if ty == 2:
                    en_name = name_res
                    en_manufacturer = manufacturer_res
                    en_device = device_res
                    en_unit = unit_res
                    en_create_descr = create_descr_res
                    en_remarks = remarks_res
                else:
                    en_name = name
                    name = name_res
                    en_manufacturer = manufacturer
                    manufacturer = manufacturer_res
                    en_device = device
                    device = device_res
                    en_unit = unit
                    unit = unit_res
                    en_create_descr = us
                    us = create_descr_res
                    en_remarks = remarks
                    remarks = remarks_res

                p = SpareInfo(name=name,manufacturer=manufacturer,spec=spec,device=device,num=num,unit=unit,remarks=remarks,create_descr=us,station=station,
                        op_ts=timeUtils.getNewTimeStr(),en_name=en_name,en_manufacturer=en_manufacturer,en_device=en_device,
                              en_unit=en_unit,en_create_descr=en_create_descr,en_remarks=en_remarks)
                user_session.add(p)
                user_session.commit()
                # disk = SpareDisk(id=p.id,year_mon=timeUtils.getNewTimeStr()[:7],now_num=num)
                # user_session.add(disk)
                # user_session.commit()
                return self.returnTypeSuc_en(lang=lang)

            elif kt == 'UpdateSpareInfos':  # 修改备件
                id = self.get_argument('id',None)  # 步骤id
                name = self.get_argument('name',None)  # 备件名
                manufacturer = self.get_argument('acc',None) # 厂家
                spec = self.get_argument('type',None)  # 规格型号
                device = self.get_argument('mainStation',None)   # 主设备
                num = self.get_argument('RealNum',0)   # 数量
                unit = self.get_argument('unit',None)   # 单位
                remarks = self.get_argument('remarks','')   # 备注
                station = self.get_argument('db','')   # 所属站
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info('id:%s,name:%s,acc:%s,type:%s,mainStation:%s,num:%s,unit:%s,remarks:%s,station:%s'%(id,name,manufacturer,spec,device,num,unit,remarks,station))
                if not id or not name :
                    return self.customError("入参不完整！") if lang == 'zh' else self.customError("Incomplete input pa!")
                page = user_session.query(SpareInfo).filter(SpareInfo.id==id).first()
                # disk = user_session.query(SpareDisk).filter(SpareDisk.id==id).first()
                if not page:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")

                pa = user_session.query(SpareInfo).filter(SpareInfo.name==name,SpareInfo.manufacturer==manufacturer,SpareInfo.spec==spec,SpareInfo.device==device,SpareInfo.is_use==1).first()
                if pa and pa.id != int(id):  # 有重复的
                    logging.info("update data is in db")
                    return self.returnTypeSuc_en(lang=lang)
                remarks = ';'.join(remarks.split()) if remarks else ''

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                name_res = t_cls.str_chinese(name)
                manufacturer_res = t_cls.str_chinese(manufacturer)
                device_res = t_cls.str_chinese(device)
                unit_res = t_cls.str_chinese(unit)
                remarks_res = t_cls.str_chinese(remarks)
                if ty == 2:
                    en_name = name_res
                    en_manufacturer = manufacturer_res
                    en_device = device_res
                    en_unit = unit_res
                    en_remarks = remarks_res
                else:
                    en_name = name
                    name = name_res
                    en_manufacturer = manufacturer
                    manufacturer = manufacturer_res
                    en_device = device
                    device = device_res
                    en_unit = unit
                    unit = unit_res
                    en_remarks = remarks
                    remarks = remarks_res

                if name :
                    page.name = name
                    page.en_name = en_name
                if manufacturer :
                    page.manufacturer = manufacturer
                    page.en_manufacturer = en_manufacturer
                if spec :
                    page.spec = spec
                if device :
                    page.device = device
                    page.en_device = en_device

                page.num = num
                if unit :
                    page.unit = unit
                    page.en_unit = en_unit
                if remarks :
                    page.remarks = remarks
                    page.en_remarks = en_remarks
                if station :
                    page.station = station

                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)

            elif kt == 'DeleteSpareInfos':  # 删除备件
                id = self.get_argument('id',None)
                page = user_session.query(SpareInfo).filter(SpareInfo.id==id).first()
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not page:
                    return self.customError("无效id")  if lang == 'zh' else self.customError("Invalid ID")
                page.is_use = 0
                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)
            elif kt == 'UploadSpareInfosFile': # 批量上传备件记录表
                station = self.get_argument('db','')   # 所属站
                files = self.request.files
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('file')

                data = imgs[0].get('body')
                filename = imgs[0].get('filename')

                if not filename.endswith(('.xls','.xlsx')):
                    return self.customError('上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障处置信息！') if lang == 'zh'\
                        else self.customError("Upload table file format error! Please reselect the file, or use the "
                                              "”Template Download“ button to download a standard format spreadsheet "
                                              "and enter troubleshooting information in this template!")
                path = '%s/%s' % (file_path,filename)
                file = open(path, 'wb')
                file.write(data)
                file.close()
                # print 'path:',path
                # path = '/home/<USER>/excelfiles/储能电站设备故障信息统计表(1)(1).xlsx'
                df = pd.read_excel(path)
                # print 'df-------',df
                values_arr = df.values  # 二维矩阵
                values_arr[pd.isna(values_arr)] = None
                # 校验表头
                # print 'values_arr:',values_arr,len(values_arr),values_arr[1][0],values_arr[1][1]
                if lang == 'en':
                    for i in range(len(en_file_title)):
                        if en_file_title[i] != values_arr[1][i]:
                            logging.error("标准title:%s  文件title:%s" % (en_file_title[i], values_arr[1][i]))
                            return self.customError(
                                '上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障处置信息！') if lang == 'zh' \
                                else self.customError(
                                "Upload table file format error! Please reselect the file, or use the ”Template "
                                "Download“ button to download a standard format spreadsheet and enter troubleshooting "
                                "information in this template!")


                else:
                    for i in range(len(file_title)):
                        if file_title[i] != values_arr[1][i]:
                            logging.error("标准title:%s  文件title:%s"%(file_title[i],values_arr[1][i]))
                            return self.customError(
                                '上传表格文件格式错误！请重新选择文件，或者通过“模板下载”按键，下载标准格式的电子表格，并在此模板中录入故障处置信息！') if lang == 'zh' \
                                else self.customError(
                                "Upload table file format error! Please reselect the file, or use the ”Template "
                                "Download“ button to download a standard format spreadsheet and enter troubleshooting "
                                "information in this template!")

                session = self.getOrNewSession()
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                us = session.user['name']
                if ty == 2:
                    en_create_descr = t_cls.str_chinese(us)
                else:
                    en_create_descr = us
                    us = t_cls.str_chinese(us)
                for v in range(2,len(values_arr)):  # 获得某一行的数据
                    # print 'vvv',v,values_arr[v][1]
                    value = values_arr[v]
                    name = value[1]
                    spec = value[2]
                    manufacturer = value[3]
                    device = value[4]
                    unit = value[5]
                    num = value[6]

                    name_res = t_cls.str_chinese(name)
                    manufacturer_res = t_cls.str_chinese(manufacturer)
                    device_res = t_cls.str_chinese(device)
                    unit_res = t_cls.str_chinese(unit)
                    if ty == 2:
                        en_name = name_res
                        en_manufacturer = manufacturer_res
                        en_device = device_res
                        en_unit = unit_res
                        pa = user_session.query(SpareInfo).filter(SpareInfo.name == name,
                                                                  SpareInfo.manufacturer == manufacturer,
                                                                  SpareInfo.spec == spec, SpareInfo.device == device,
                                                                  SpareInfo.is_use == 1,
                                                                  SpareInfo.station == station).first()

                    else:
                        en_name = name
                        name = name_res
                        en_manufacturer = manufacturer
                        manufacturer = manufacturer_res
                        en_device = device
                        device = device_res
                        en_unit = unit
                        unit = unit_res
                        pa = user_session.query(SpareInfo).filter(SpareInfo.en_name == en_name,
                                                                  SpareInfo.en_manufacturer == manufacturer,
                                                                  SpareInfo.spec == spec, SpareInfo.en_device == en_device,
                                                                  SpareInfo.is_use == 1,
                                                                  SpareInfo.station == station).first()

                    if pa:  # 已保存
                        logging.info('line:%s is in DB'%v)
                        return self.customError('数据已存在') if lang == 'zh' else self.customError("Data already exists")
                    else:
                        p = SpareInfo(name=name,manufacturer=manufacturer,spec=spec,device=device,num=num,unit=unit,create_descr=us,op_ts=timeUtils.getNewTimeStr(),station=station,
                                      en_name=en_name,en_manufacturer=en_manufacturer,en_device=en_device,en_unit=en_unit,en_create_descr=en_create_descr)
                        user_session.add(p)
                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)

            elif kt == 'SpareInfoIn':  # 备件入库
                id = self.get_argument('id',None)  # 备件id
                num = int(self.get_argument('num',0))  # 入库数量
                in_source = self.get_argument('source',None)  # 入库来源
                in_time = self.get_argument('time',None)  # 入库时间
                in_descr = self.get_argument('descr',None)  # 入库登记人
                remarks = self.get_argument('remark',None)  # 备注
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not id or not num or not in_time or not in_descr :
                    return self.customError("入参不完整！") if lang == 'zh' else self.customError("Incomplete input parameters!")
                remarks = ';'.join(remarks.split()) if remarks else ''
                session = self.getOrNewSession()
                us = session.user['name']
                sp = user_session.query(SpareInfo).filter(SpareInfo.id == id).first()
                if not sp:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                cou = sp.num+num

                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                in_source_res = t_cls.str_chinese(in_source)
                in_descr_res = t_cls.str_chinese(in_descr)
                remarks_res = t_cls.str_chinese(remarks)
                create_descr_res = t_cls.str_chinese(us)
                if ty == 2:
                    en_in_source = in_source_res
                    en_in_descr = in_descr_res
                    en_remarks = remarks_res
                    en_create_descr = create_descr_res
                else:
                    en_in_source = in_source
                    in_source = in_source_res
                    en_in_descr = in_descr
                    in_descr = in_descr_res
                    en_remarks = remarks
                    remarks = remarks_res
                    en_create_descr = us
                    us = create_descr_res

                spin = SpareIn(id=id,num=num,in_source=in_source,in_time=in_time,in_descr=in_descr,remarks=remarks,create_descr=us,
                        op_ts=timeUtils.getNewTimeStr(),en_in_descr=en_in_descr,en_in_source=en_in_source,en_create_descr=en_create_descr,en_remarks=en_remarks)
                sp.num = cou
                user_session.add(spin)
                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)
            elif kt == 'SpareInfoOut':  # 备件出库
                id = self.get_argument('id',None)  # 备件id
                num = int(self.get_argument('num',0))  # 出库数量
                out_source = self.get_argument('source',None)  # 领取用途
                out_time = self.get_argument('time',None)  # 领用时间
                out_descr = self.get_argument('descr',None)  # 领用登记人
                remarks = self.get_argument('remark',None)  # 备注
                lang = self.get_argument('lang', None)
                if not lang:
                    lang = 'zh'
                if not id or not num or not out_source or not out_time or not out_descr:
                    return self.customError("入参不完整！") if lang == 'zh' else self.customError("Incomplete input parameters!")
                remarks = ';'.join(remarks.split()) if remarks else ''
                session = self.getOrNewSession()
                us = session.user['name']
                sp = user_session.query(SpareInfo).filter(SpareInfo.id == id).first()
                if not sp:
                    return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                if num>sp.num:
                    return self.customError("非法入参") if lang == 'zh' else self.customError("Illegal entry of ginseng")
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                out_source_res = t_cls.str_chinese(out_source)
                out_descr_res = t_cls.str_chinese(out_descr)
                remarks_res = t_cls.str_chinese(remarks)
                create_descr_res = t_cls.str_chinese(us)
                if ty == 2:
                    en_out_source = out_source_res
                    en_out_descr = out_descr_res
                    en_remarks = remarks_res
                    en_create_descr = create_descr_res
                else:
                    en_out_source = out_source
                    out_source = out_source_res
                    en_out_descr = out_descr
                    out_descr = out_descr_res
                    en_remarks = remarks
                    remarks = remarks_res
                    en_create_descr = us
                    us = create_descr_res

                spout = SpareOut(id=id,num=num,out_source=out_source,out_time=out_time,out_descr=out_descr,remarks=remarks,create_descr=us,
                        op_ts=timeUtils.getNewTimeStr(),en_out_descr=en_out_descr,en_out_source=en_out_source,en_create_descr=en_create_descr,en_remarks=en_remarks)
                sp.num = sp.num-num
                user_session.add(spout)
                user_session.commit()
                return self.returnTypeSuc_en(lang=lang)
            else:
                return self.pathError()

            
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError(lang)
        finally:
            user_session.close()

    