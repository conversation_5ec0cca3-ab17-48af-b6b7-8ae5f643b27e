#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 17:27:24
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_status.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:22:38



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_device import DevicePT

class StatusPT(mqtt_Base):
    ''' 状态说明表 '''
    __tablename__ = "t_status"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    device_id = Column(Integer, ForeignKey("t_device.id"),nullable=False, comment=u"所属设备")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    a_duration = Column(String(256), nullable=True,comment=u"告警状态持续时长")
    desc_on = Column(String(256), nullable=True,comment=u"合闸描述")
    desc_off = Column(String(256), nullable=True,comment=u"分闸描述")
    store_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否存盘（0否1是，默认1）")
    rep_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否上报（0否1是，默认1）")
    bites = Column(Integer, nullable=True,server_default='0',comment=u"是否是组合点（0否1是，默认0）")
    

    device_status = relationship("DevicePT",passive_deletes=True,backref='device_status')

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','a_duration':'%s','desc_on':'%s','desc_off':'%s','store_flag':'%s','rep_flag':'%s'}" % (
            self.id,self.name,self.descr,self.a_duration,self.desc_on,self.desc_off,self.store_flag,self.rep_flag)