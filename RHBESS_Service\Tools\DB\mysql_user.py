#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-27 10:31:47
#@FilePath     : \RHBESS_Service\Tools\DB\mysql_user.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:11:24

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
DEBUG = model_config.get('broker', "log_debug")
# user库连接 mysql

USER_HOSTNAME = model_config.get('mysql', "USER_HOSTNAME")
USER_PORT = model_config.get('mysql', "USER_PORT")
USER_DATABASE = model_config.get('mysql', "USER_DATABASE")
USER_USERNAME = model_config.get('mysql', "USER_USERNAME")
USER_PASSWORD = model_config.get('mysql', "USER_PASSWORD")

userdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    USER_USERNAME,
    USER_PASSWORD,
    USER_HOSTNAME,
    USER_PORT,
    USER_DATABASE
)
user_engine = create_engine(userdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=40, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_user_session = scoped_session(sessionmaker(user_engine,autoflush=True))

user_Base = declarative_base(user_engine)
user_session = _user_session()

TIANLU_HOSTNAME = model_config.get('mysql', "TIANLU_HOSTNAME")
TIANLU_PORT = model_config.get('mysql', "TIANLU_PORT")
TIANLU_DATABASE = model_config.get('mysql', "TIANLU_DATABASE")
TIANLU_USERNAME = model_config.get('mysql', "TIANLU_USERNAME")
TIANLU_PASSWORD = model_config.get('mysql', "TIANLU_PASSWORD")

tianludb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    TIANLU_USERNAME,
    TIANLU_PASSWORD,
    TIANLU_HOSTNAME,
    TIANLU_PORT,
    TIANLU_DATABASE
)
tianlu_engine = create_engine(tianludb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=40, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_tianlu_session = scoped_session(sessionmaker(tianlu_engine,autoflush=True))

tianlu_Base = declarative_base(tianlu_engine)
tinalu_session = _tianlu_session()



# USER_HOSTNAME_20 = model_config.get('mysql', "GUIZHOU_HOSTNAME")
# USER_PORT_20 = model_config.get('mysql', "GUIZHOU_PORT")
# USER_DATABASE_20 = model_config.get('mysql', "USER_DATABASE_20")
# USER_USERNAME_20 = model_config.get('mysql', "GUIZHOU_USERNAME")
# USER_PASSWORD_20 = model_config.get('mysql', "GUIZHOU_PASSWORD")
#
# userdb_mysql_url_20='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
#     USER_USERNAME_20,
#     USER_PASSWORD_20,
#     USER_HOSTNAME_20,
#     USER_PORT_20,
#     USER_DATABASE_20
# )
# user_engine_20 = create_engine(userdb_mysql_url_20,
#                        echo=False,
#                        max_overflow=5, pool_size=40, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
#
# _user_session_20 = scoped_session(sessionmaker(user_engine_20,autoflush=True))
#
# user_Base_20 = declarative_base(user_engine_20)
# user_session_20 = _user_session_20()

def check_db_connection(engine):
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        print(f"Database connection error: {e}")
        return False


def get_user_session():
    global user_engine
    if not check_db_connection(user_engine):
        # 如果连接中断，可以选择重新创建引擎或者采取其他措施
        user_engine = create_engine(userdb_mysql_url,
                                    echo=False,
                                    max_overflow=5, pool_size=40, pool_timeout=10, pool_pre_ping=True, pool_recycle=1800)
        _user_session.configure(bind=user_engine)
    return _user_session()



