#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\Running\HaLun\urls_aluns.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-01-16 09:45:26


# -*- coding=utf-8 -*-
from tornado.routing import Rule, PathMatches
from tornado.web import url, RequestHandler

from Application.EqAccount.HaLun.Index_monitor import IndexMonitorIntetface
from Application.EqAccount.HaLun.dashboard import DashboardIntetface
from Application.EqAccount.HaLun.income_management import IncomeManagementIntetface
from Application.EqAccount.HaLun.no_s import NoSIntetface
from Application.EqAccount.HaLun.project_info import ProjectIntetface
from Application.EqAccount.HaLun.report_new import ReportDayFormInterface, ReportWeekFormInterface, \
    ReportMonthFormInterface, ReportYearFormInterface
from Application.EqAccount.HaLun.telecontrol_strategy import PowerPlanDetail, PowerPlanList, \
    PowerPlanAdd, PowerPlanUpdate, GetPlanHis, powerPlanStop, powerPlanDelete, PowerPlanStations, \
    PowerPlanStationsRefresh, planHisExport, pcsRealDatas, strategyAdd, strategyList, strategyInfosById, sendToDevice, \
    strategyCompare, strategySave, strategyUpdate, strategyDel, strategySendToDevice, dispositionList, PowerLimit, \
    StrategyTemplate, StrategyImport, GetIssuanceType, ProjectPackAdd, ProjectPackList
from Application.EqAccount.HaLunS.reference_power import ReferencePowerInterface
from Application.EqAccount.organization import OrganizationIntetface
from Application.EqAccount.user import UserIntetface
from Application.EqAccount.roleAndManufacturer import RoleAndManufacturerIntetface
from Application.EqAccount.role_authority import RoleAuthorityIntetface
from Application.EqAccount.worker import UserLoginHandler
from Application.HistoryData.his_data_alluse import HisDataAllUseInterface_
from Application.EqAccount.HaLun.equipment_tree import EquipmentTreeIntetface
from Application.EqAccount.HaLun.outageRecord import OutageRecordIntetface

from Application.EqAccount.HaLun.main_frame import MainFrameCenterIntetface,SystemMonitorIntetface,BatMonitorIntetface,PcsMonitorIntetface,StatusBitIntetface
from Application.EqAccount.HaLun.custom import CustomIntetface
from Application.EqAccount.HaLun.charts_user import ChartsOfUserIntetface
from Application.EqAccount.HaLun.report import ReportFormInterface
from Application.EqAccount.HaLun.report_custom import ReportCustomInterface
from Application.HistoryData.his_bams import HisBamsInterface
from Application.EqAccount.HaLun.sysinfo import SysInfosIntetface
from Application.EqAccount.filehandle import FileHandleIntetface
from Application.EqAccount.HaLun.fault import FaultIntetface
from Application.HistoryData.his_bams_derive import DeriveIntetface
from Application.EqAccount.HaLun.knowledge_base import KnowledgeBaseIntetface
from Application.EqAccount.role_authority_new import RoleAuthorityNewIntetface
from Application.HistoryData.his_data_query import HisDataQueryInterface
from Application.EqAccount.Foreign.rankDashboardHandle import RankDashboardHandleIntetface

from Application.EqAccount.HaLun.fault_knowledge import FaultKnowledgeIntetface

class NotFoundHandler(RequestHandler):
    def prepare(self):
        self.set_status(404)
        self.write({"code": 404, "message": "资源不存在: %s" % self.request.path, "data":[]})
        self.finish()

routes = [
    # # 权限管理接口
    url(r"/UserAuthority/(\w+)", RoleAuthorityIntetface),
    # # 权限管理接口新需求
    url(r"/Authority/(\w+)", RoleAuthorityNewIntetface),
    # # 用户登录接口
    url(r"/UserLogin/(\w+)", UserLoginHandler),
    # # 角色和厂商管理
    url(r"/RoleAndManufacturer/(\w+)", RoleAndManufacturerIntetface),
    # 通用历史数据接口
    url(r"/History/(\w+)", HisDataAllUseInterface_),

    # 台账区域接口
    url(r"/Organization/(\w+)", OrganizationIntetface),
    # 用户管理接口
    url(r"/User/(\w+)", UserIntetface),

    # 获取设备层级结构，懒加载
    url(r"/Equipment/(\w+)", EquipmentTreeIntetface),
   
    # 自定义配置功能维护
    url(r"/Custom/(\w+)", CustomIntetface),
    # 获取首页居中数据
    url(r"/MainFrameCenter/List", MainFrameCenterIntetface),
   
    # 系统监控页数据
    url(r"/SystemMonitor/List", SystemMonitorIntetface),
    # 电池监控界面数据
    url(r"/BatMonitor/List", BatMonitorIntetface),
    # PCS监控界面数据
    url(r"/PcsMonitor/List", PcsMonitorIntetface),
    # 状态量具体每位的值
    url(r"/StatusBits/Infos", StatusBitIntetface),

    # 用户曲线数据
    url(r"/Charts/(\w+)", ChartsOfUserIntetface),

    # 报表数据
    url(r"/ReportForm/(\w+)", ReportFormInterface),
    # 自定义报表数据
    url(r"/ReportCustom/(\w+)", ReportCustomInterface),
    # bams数据
    url(r"/Bams/(\w+)", HisBamsInterface),
    # 系统监视数据
    url(r"/SysInfo/(\w+)", SysInfosIntetface),
    # 文件管理
    url(r"/FileHandle/(\w+)", FileHandleIntetface),
    # 故障统计管理
    url(r"/FaultHandle/(\w+)", FaultIntetface),
    # 停运记录
    url(r"/OutageRecord/(\w+)", OutageRecordIntetface),

    # 故障知识库
    url(r"/FaultKnowledge/(\w+)", FaultKnowledgeIntetface),

    # 地图大屏
    url(r"/Dashboard/(\w+)", DashboardIntetface),
    # 电芯数据导出
    url(r"/Derive/(\w+)", DeriveIntetface),
    # 知识库
    url(r"/Knowledge/(\w+)", KnowledgeBaseIntetface),
    # 项目信息配置
    url(r"/Project/(\w+)", ProjectIntetface),
    # 关键指标监测
    url(r"/IndexMonitor/(\w+)", IndexMonitorIntetface),
    # # 收益管理
    url(r"/IncomeManagement/(\w+)", IncomeManagementIntetface),
    # 历史数据查询
    url(r"/HisDataQuery/(\w+)", HisDataQueryInterface),
    # 对外对接
    url(r"/DashBoard/(\w+)", RankDashboardHandleIntetface),
    # 基准功率
    url(r"/ReferencePower/(\w+)", ReferencePowerInterface),

    url(r"/NoS/(\w+)", NoSIntetface),

    #运行报表接口拆分
    # 日报
    url(r"/Report/Day/(\w+)", ReportDayFormInterface),
    # 周报
    url(r"/Report/Week/(\w+)", ReportWeekFormInterface),
    # 月报
    url(r"/Report/Month/(\w+)", ReportMonthFormInterface),
    # 年报
    url(r"/Report/Year/(\w+)", ReportYearFormInterface),

    # 功率计划
    url("/control/pcsRealDatas", pcsRealDatas), # PCS列表 返回该站下所有pcs，及实时功率和开关状态
    url("/control/sendToDevice", sendToDevice),  # 指令下发
    url("/control/disposition/list", dispositionList),  # 自动控制模式配置 配置下拉框列表
    url("/control/strategyCompare", strategyCompare), # 自动控制模式配置 策略对比
    url("/control/strategySave/(\w+)", strategySave),  # 自动控制模式配置 策略另存为
    url("/control/strategyList", strategyList), # 自动控制模式管理 获取策略列表
    url("/control/PowerLimit", PowerLimit),  # 自动控制模式管理 遥控功率限值
    url("/control/strategyAdd", strategyAdd),  # 自动控制模式管理 新增自定义策略
    url("/control/strategyUpdate/(\w+)", strategyUpdate),  # 自动控制模式管理 修改自定义策略
    url("/control/strategyDel", strategyDel),  # 自动控制模式管理 删除自定义策略
    url("/control/strategyInfosById", strategyInfosById),  # 自动控制模式管理 查看自定义策略
    url("/control/strategySendToDevice", strategySendToDevice),  # 自动控制模式管理 下发自定义策略，策略配置时公用次接口
    url("/control/StrategyTemplate", StrategyTemplate),# 自动控制模式管理 模板下载
    url("/control/StrategyImport", StrategyImport),  # 自动控制模式管理 模板解析
    url("/control/PowerPlanStations", PowerPlanStations),  # 功率计划电站容量信息获取     en
    url("/control/PowerPlanStationsRefresh", PowerPlanStationsRefresh),  # 功率计划电站容量信息刷新
    url("/control/powerPlanList", PowerPlanList),  # 功率计划下发列表     en
    url("/power/PowerPlanAdd", PowerPlanAdd),  # 新增计划功率     en
    url("/power/PowerPlanDetail", PowerPlanDetail),  # 功率计划下发详情     en
    url("/power/PowerPlanUpdate", PowerPlanUpdate),  # 功率计划修改     en
    url("/power/powerPlanStop", powerPlanStop),  # 停止计划功率内容   en
    url("/power/PowerPlanDelete", powerPlanDelete),  # 功率计划删除     en
    url("/control/getPlanHis", GetPlanHis),  # 查询下发记录列表
    url("/control/planHisExport", planHisExport),  # 导出查询下发记录
    url("/power/ProjectPackAdd", ProjectPackAdd),  # 另存项目包
    url("/power/ProjectPackList", ProjectPackList),  # 加载项目包列表
    url("/power/GetIssuanceType", GetIssuanceType),  # 下发类型

    # 默认处理程序，处理未匹配的路由
    (r".*", NotFoundHandler),
]
