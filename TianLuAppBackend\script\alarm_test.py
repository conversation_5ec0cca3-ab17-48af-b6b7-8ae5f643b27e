from multiprocessing import Pool

import requests


def sent_alarm(i):
    url = "http://************:19190/stored_energy/unalarm/add/"
    point = f"test_alarm{i}"
    send_data = {
        "app_name": "TN002",
        "station_name": "SAMPLE1",
        "data_type": "status",
        "time": "1691193512",
        "utime": "1691193512",
        "data_info": {
            "utime": "1691193512",
            "time": "1691193512",
            "datatype": "status",
            "body": [{"device": "BMS", point: "3; 1"}],
        },
        "report_type": 1,
    }
    return_data = requests.post(url=url, json=send_data)
    print(return_data.json())


if __name__ == '__main__':
    pool = Pool(6)
    for i in range(1000):
        tasks = pool.apply_async(func=sent_alarm, args=(i,))

    pool.close()
    pool.join()
    print("end")
