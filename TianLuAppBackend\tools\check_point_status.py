# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/6/28 下午2:29
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : check_point_status.py
# @Software : PyCharm
import re

import pandas as pd
import pymysql

host = 'rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com'
user = 'tianlu_app'
password = 'P@ssw0rd!'
# database = 'db_tianluapp'
database = 'db_tianluapp_prd_v1.1'

conn = pymysql.connect(host=host, user=user, password=password, database=database)
cursor = conn.cursor()


def read_excel_to_list():
    file_path = 'demo.xlsx'
    sheet_name = 'Sheet1'  # 或者指定Sheet索引，例如0表示第一个Sheet
    # 读取Excel文件的指定Sheet
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 将每一行的key和value转换为字典中的键值对
    result_dict = {}
    for index, row in df.iterrows():
        key = row[1]  # 第一列作为键
        value = row[0]  # 第二列作为值
        result_dict[key] = value

    return result_dict

# result_dict = read_excel_to_list()
#
# print(result_dict)


def check_point_status():

    sql = "SELECT id, name, description FROM `t_point_status` WHERE `description` LIKE '%故障故障位%'"

    cursor.execute(sql)
    results = cursor.fetchall()

    result_dict = read_excel_to_list()

    for row in results:
        id = row[0]
        name = row[1]
        description = row[2]
        print(name, description)

        new_desc = result_dict.get(name)

        if new_desc:
            pattern = re.compile(r'(\d+)$')
            if pattern.search(name):
                new_desc = new_desc + pattern.search(description).group(1)

            sql = "UPDATE `t_point_status` SET `description` = '%s' WHERE `id` = %s" % (new_desc, id)
            cursor.execute(sql)
            conn.commit()


if __name__ == '__main__':
    check_point_status()
