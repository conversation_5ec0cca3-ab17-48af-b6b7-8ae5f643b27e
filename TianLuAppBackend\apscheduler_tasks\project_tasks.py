import geocoder
from apis.user.models import Project, ElectricityProvince
from apis.web2.project_dashboard.views.project_center_control import ControlCardsView
from tools.count import get_project_yesterday_charge_discharge_complete_percentage
def project_latitude_and_longitude_task():
    # 查询新增项目，富化其经纬度信息
    res = Project.objects.filter(longitude__isnull=True).all()
    for i in res:
        province_name = ElectricityProvince.objects.filter(id=i.province_id).first().name
        if province_name:
            g = geocoder.google("1403 Washington Ave, New Orleans, LA 70130")
            g = geocoder.arcgis(province_name)
            c = g.latlng
            i.longitude = c[1]
            i.latitude = c[0]
            i.save()
    return 1


def project_all_year_month_yesterday_income_task():
    # 项目收益,充放电完成率定时任务
    res = Project.objects.filter(is_used=1).all()
    for i in res:
        ControlCardsView()._get_project_income(i,'zh')  # 默认缓存中文
        get_project_yesterday_charge_discharge_complete_percentage(i)
    return 1