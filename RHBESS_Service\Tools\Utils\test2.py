#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-08 14:48:16
#@FilePath     : \RHBESS_Service\Tools\Utils\test2.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 13:09:17

from dbutils.persistent_db import PersistentDB
import pymysql




pool = PersistentDB(pymysql, 10, **{
    "host": '***********',  # 数据库主机地址
    "user": 'root',  # 数据库用户名
    "password": 'doris_123456',  # 数据库密码
    "database": 'rhyc',  # 数据库名称
    "port": 9030,
    "cursorclass": pymysql.cursors.DictCursor
})

conn = pool.connection()
cursor = conn.cursor()

sql = "select time from device_notify_cumulant_record  where station_name ='SAMPLE1' and   get_json_string(data_info,'$.body.PAE') is null"
cursor.execute(sql)
result = cursor.fetchall()

print ('result:',len(result))


for re in result:
    tim = re['time'].strftime("%Y-%m-%d %H:%M:%S")
    sql1 = "delete from device_notify_cumulant_record  where station_name ='SAMPLE1' and   time='%s'"%tim
    cursor.execute(sql1)
    print (tim,'is delete SUCCESS')
    conn.commit()
cursor.close()
conn.close()




