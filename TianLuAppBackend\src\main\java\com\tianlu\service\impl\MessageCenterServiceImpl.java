package com.tianlu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.central.common.exception.BusinessException;
import com.tianlu.entity.MessageCenter;
import com.tianlu.mapper.MessageCenterMapper;
import com.tianlu.service.MessageCenterService;
import com.tianlu.vo.MessageCenterVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class MessageCenterServiceImpl extends ServiceImpl<MessageCenterMapper, MessageCenter> implements MessageCenterService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public MessageCenterVO getMessageDetail(Long id) {
        MessageCenter message = this.getById(id);
        if (message == null) {
            return null;
        }
        MessageCenterVO vo = new MessageCenterVO();
        BeanUtils.copyProperties(message, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageStatus(Long id, Integer isRead, Integer isHandle, Integer isVerify) {
        MessageCenter message = this.getById(id);
        if (message == null) {
            return;
        }
        message.setIsRead(isRead);
        message.setIsHandle(isHandle);
        message.setIsVerify(isVerify);
        this.updateById(message);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageOpinion(Long id, String opinion, String enOpinion) {
        MessageCenter message = this.getById(id);
        if (message == null) {
            return;
        }
        message.setOpinion(opinion);
        message.setEnOpinion(enOpinion);
        this.updateById(message);
    }

    /**
     * 推送告警消息到消息中心
     * @param alarmId 告警ID
     * @param userId 当前用户ID
     * @param projectName 项目名称
     * @param stationName 站点名称
     * @param alarmType 告警类型
     * @param alarmNote 告警备注
     * @param alarmDetails 告警详情
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 告警状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushAlarmMessage(Long alarmId, Long userId, String projectName, String stationName,
                               Integer alarmType, String alarmNote, String alarmDetails,
                               LocalDateTime startTime, LocalDateTime endTime, Integer status) {
        // 构建告警类型描述
        String typeDesc;
        String enTypeDesc;
        if (alarmType == 1) {
            typeDesc = "故障";
            enTypeDesc = "Faulty";
        } else if (alarmType == 4) {
            typeDesc = "离线";
            enTypeDesc = "OffLine";
        } else {
            typeDesc = "通讯异常";
            enTypeDesc = "Com. Error";
        }

        // 构建时间字符串
        String timeStr = status == 1 
            ? String.format("%s-%s", startTime.format(DATE_TIME_FORMATTER), endTime.format(DATE_TIME_FORMATTER))
            : String.format("从%s", startTime.format(DATE_TIME_FORMATTER));
        
        String enTimeStr = status == 1
            ? String.format("%s-%s", startTime.format(DATE_TIME_FORMATTER), endTime.format(DATE_TIME_FORMATTER))
            : String.format("from %s", startTime.format(DATE_TIME_FORMATTER));

        // 构建消息标题
        String title = String.format("%s项目%s出现%s%s: %s。", 
            projectName, timeStr, alarmNote, typeDesc, alarmDetails);
        
        String enTitle = String.format("The project %s %s appear %s %s: %s.",
            projectName, enTimeStr, alarmNote, enTypeDesc, alarmDetails);

        // 设置接收者列表
        Set<Long> receivers = new HashSet<>(Arrays.asList(69L, 76L, 88L, 120L, 182L, userId));

        // 为每个接收者创建消息
        for (Long receiver : receivers) {
            // 检查是否已经推送过
            boolean exists = this.count(new LambdaQueryWrapper<MessageCenter>()
                .eq(MessageCenter::getAlarmId, alarmId)
                .eq(MessageCenter::getUserId, receiver)) > 0;

            if (exists) {
                continue;
            }

            // 创建新消息
            MessageCenter message = new MessageCenter();
            message.setTitle(title);
            message.setEnTitle(enTitle);
            message.setType(1); // 告警类型
            message.setIsRead(0);
            message.setIsVerify(0);
            message.setUserId(receiver);
            message.setRelatedId(String.valueOf(alarmId));
            message.setCreateTime(LocalDateTime.now());
            
            this.save(message);
        }
    }

    /**
     * 获取告警消息详情
     * @param messageId 消息ID
     * @return 告警消息详情
     */
    @Override
    public MessageCenterVO getAlarmMessageDetail(Long messageId) {
        MessageCenter message = this.getById(messageId);
        if (message == null || message.getType() != 1) {
            throw new BusinessException("该消息非告警消息，无告警信息");
        }

        MessageCenterVO vo = new MessageCenterVO();
        BeanUtils.copyProperties(message, vo);
        return vo;
    }
} 