#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-23 13:42:44
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_first_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 09:38:40


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseFirstInfo(user_Base):
    u'地图首页各省信息表'
    __tablename__ = "t_side_forecase_first_info"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_type = Column(VARCHAR(25), nullable=False, comment=u"用户类型；用户侧、独立储能")
    province_name = Column(VARCHAR(25),nullable=False, comment=u"省份名称")
    content1 = Column(Text, nullable=True, comment=u"内容1")
    content2 = Column(Text, nullable=True, comment=u"内容2")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    type = Column(CHAR(2), nullable=False, server_default='1',comment=u"1基本信息2最新曾策")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'user_type':'%s','province_name':'%s'}" %(self.id,self.user_type,self.province_name)

   