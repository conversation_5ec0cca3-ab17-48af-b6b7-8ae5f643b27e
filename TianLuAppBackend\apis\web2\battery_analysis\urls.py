# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/22 下午4:58
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : urls.py
# @Software : PyCharm

from django.urls import path

from apis.web2.battery_analysis import views

urlpatterns = [
    path('analysis_list', views.AnalysisListView.as_view()),  # 分析列表：支持筛选和分页        en
#     # path('analysis_download', views.AnalysisDownloadView.as_view()),  # 运行分析：下载
#     # path('analysis_alarms/<int:id>', views.AnalysisAlarmDetailListView.as_view()),  # 查看故障
    path('types_list', views.AnalysisTypesView.as_view()),      # 获取分析的报文类型         en
    path('analysis_battery_data', views.BatteryDataView.as_view()),      # 电池电压分析：操作：查看数据       en
    path('analysis_battery_tem_data', views.BatteryTemDataView.as_view()),      # 电池温度分析：操作：查看数据        en

    path('battery_analysis_effe_data_select', views.AnalysisAssessView.as_view()),  # 效果评估      en
    path('battery_analysis_effe_data', views.GetAnalysisAssessView.as_view()),  # 查看效果评估        en
    path('battery_analysis_effe_data_save', views.SaveAnalysisAssessView.as_view()),  # 保存效果评估      en
    path('battery_analysis_message', views.AnalysisMessageView.as_view()),  # 操作：推送至消息中心 & 消息详情     en
]
