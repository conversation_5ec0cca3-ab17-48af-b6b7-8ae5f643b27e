# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/3/26 9:39
# <AUTHOR> LiGang
# @Project  : TianLuAppBackend
# @File     : serializer.py
# @Software : PyCharm
import re

from django.core.validators import RegexValidator

from apis.user import models
from encryption.md5_encryption import md5_enc

from rest_framework import serializers, exceptions


class BaseMd5PasswordSerializer:
    @staticmethod
    def validate_password(value):
        """md5密码加密"""
        return md5_enc(value)


class UsernamePasswordLoginSerializer(BaseMd5PasswordSerializer, serializers.Serializer):
    """用户名+密码登录 序列化器"""""

    password = serializers.CharField(required=True, max_length=32)
    username = serializers.CharField(required=True, max_length=32)
    # 验证码校验
    code = serializers.CharField(required=False)

    def validate_code(self, value):
        """
        验证码校验
        :param value:
        :return:
        """""
        lang = self.context.get("lang", "zh")
        # 验证码校验
        if not re.match(r"^\d{6}$", value):
            raise Exception("验证码格式不正确" if lang == "zh" else "Verification code format is incorrect.")
        return value

    def validate_username(self, value):
        """
        用户名命或手机号校验
        :param value:
        :return: user 对象
        """""
        lang = self.context.get("lang", "zh")
        md5_password = md5_enc(self.initial_data.get("password"))

        # 手机号校验
        if re.match(r"^1[3-9]\d{9}$", value):
            try:
                models.UserDetails.objects.get(mobile=int(value),is_used=1)
            except models.UserDetails.DoesNotExist:
                raise Exception("手机号不存在" if lang == "zh" else "Mobile number does not exist.")
            try:
                user_ins = models.UserDetails.objects.get(mobile=int(value), password=md5_password,is_used=1)
            except models.UserDetails.DoesNotExist:
                raise Exception("手机号或密码不正确" if lang == "zh" else "Password is incorrect.")

        # 用户名校验
        else:
            try:
                models.UserDetails.objects.get(login_name=value,is_used=1)
            except models.UserDetails.DoesNotExist:
                raise Exception("用户名不存在" if lang == "zh" else "Username does not exist.")
            try:
                user_ins = models.UserDetails.objects.get(login_name=value, password=md5_password,is_used=1)

            except models.UserDetails.DoesNotExist:
                raise Exception("用户名或密码不正确" if lang == "zh" else "Password is incorrect.")

        return value


class WebSendMobileMessageSerializer(serializers.Serializer):
    mobile = serializers.CharField(
        required=True
    )

    def validate_mobile(self, value):
        lang = self.context.get("lang", "zh")

        if not value:
            raise Exception("手机号缺失" if lang == "zh" else "Mobile number missed.")

        # 手机号校验
        if not re.match(r"^1[3-9]\d{9}$", value):
            raise Exception("手机号格式不正确" if lang == "zh" else "Mobile number format is incorrect.")

        return value




