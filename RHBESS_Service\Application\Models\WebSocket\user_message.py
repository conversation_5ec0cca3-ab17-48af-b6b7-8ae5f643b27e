#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-18 10:04:59
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\WebSocket\user_message.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-18 10:07:14

from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.user import User
from Application.Models.WebSocket.message import Message


class UserMessage(user_Base):
    u'用户消息表'
    __tablename__ = "t_user_message"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    user_id = Column(Integer, ForeignKey("t_user.id"), nullable=False, comment=u"用户id")
    message_id = Column(Inte<PERSON>, <PERSON><PERSON>ey("t_message.id"), nullable=False, comment=u"消息id")
    read = Column(<PERSON><PERSON><PERSON>, nullable=False, comment=u"阅读状态", default=False)
    read_ts = Column(DateTime, nullable=True, comment=u"阅读时间")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()