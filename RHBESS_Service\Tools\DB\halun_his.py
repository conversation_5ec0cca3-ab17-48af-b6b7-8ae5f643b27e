#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-30 09:28:47
#@FilePath     : \RHBESS_Service\Tools\DB\halun_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 16:46:05


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysqlS


HALUN_HOSTNAME = model_config.get('mysql', "HALUN_HOSTNAME")
HALUN_PORT = model_config.get('mysql', "HALUN_PORT")
HALUN_DATABASE = model_config.get('mysql', "HALUN_DATABASE")
HALUN_USERNAME = model_config.get('mysql', "HALUN_USERNAME")
HALUN_PASSWORD = model_config.get('mysql', "HALUN_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    HALUN_USERNAME,
    HALUN_PASSWORD,
    HALUN_HOSTNAME,
    HALUN_PORT,
    HALUN_DATABASE
)
halun_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_halun_session = scoped_session(sessionmaker(halun_engine,autoflush=True))

halun_Base = declarative_base(halun_engine)
halun_session = _halun_session()

SHALUN_HOSTNAME = model_config.get('mysql', "SHALUN_HOSTNAME")
SHALUN_PORT = model_config.get('mysql', "SHALUN_PORT")
SHALUN_DATABASE = model_config.get('mysql', "SHALUN_DATABASE")
SHALUN_USERNAME = model_config.get('mysql', "SHALUN_USERNAME")
SHALUN_PASSWORD = model_config.get('mysql', "SHALUN_PASSWORD")

shisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SHALUN_USERNAME,
    SHALUN_PASSWORD,
    SHALUN_HOSTNAME,
    SHALUN_PORT,
    SHALUN_DATABASE
)
shalun_engine = create_engine(shisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_shalun_session = scoped_session(sessionmaker(shalun_engine,autoflush=True))

shalun_Base = declarative_base(shalun_engine)
shalun_session = _shalun_session()




