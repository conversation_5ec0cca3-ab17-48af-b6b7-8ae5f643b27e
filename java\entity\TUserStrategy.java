package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户策略表
 * 对应Python模型: TUserStrategy
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_user_strategy")
public class TUserStrategy extends SuperEntity {

    /**
     * 策略名称
     */
    @TableField("name")
    private String name;

    /**
     * 英文策略名称
     */
    @TableField("en_name")
    private String enName;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 是否删除: 1-删除, 0-不删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
