import logging
import traceback

import pandas as pd
import pymysql


def main():
    # 读取Excel表格中某个sheet的数据
    excel_file = '天禄新增点表.xls'

    sheet_name_type = "类型"
    sheet_name_measure = '测量'
    sheet_name_status = '状态'
    sheet_name_discrete = '离散'
    sheet_name_cumulant = '累积'
    # print('$'*100)
    df_type = pd.read_excel(excel_file, sheet_name=sheet_name_type, header=0)

    df_measure = pd.read_excel(excel_file, sheet_name=sheet_name_measure)
    df_measure = df_measure.where(df_measure.notnull(), None)

    df_status = pd.read_excel(excel_file, sheet_name=sheet_name_status)
    df_status = df_status.where(df_status.notnull(), None)

    df_discrete = pd.read_excel(excel_file, sheet_name=sheet_name_discrete)
    df_discrete = df_discrete.where(df_discrete.notnull(), None)

    df_cumulant = pd.read_excel(excel_file, sheet_name=sheet_name_cumulant)
    df_cumulant = df_cumulant.where(df_cumulant.notnull(), None)

    # 连接MySQL数据库
    # host = 'localhost'
    # user = 'root'
    # password = '159357'
    # database = 'demo'

    host = 'rm-8vbsb63v4hu3l83a8.mysql.zhangbei.rds.aliyuncs.com'
    user = 'tianlu_app'
    password = 'P@ssw0rd!'
    database = 'db_tianluapp'


    conn = pymysql.connect(host=host, user=user, password=password, database=database)
    cursor = conn.cursor()

    # 1
    # try:
    #     # 将数据写入MySQL数据库
    #     # print(df_type.head)
    #     for index, row in df_type.iterrows():
    #         sql = "INSERT INTO t_point_type (type, device, is_stand) VALUES (%s, %s, %s)"
    #
    #         values = (row['type'], row['device'], row['is_stand'])
    #         print(values)
    #         cursor.execute(sql, values)
    #     # 提交更改并关闭连接
    #     conn.commit()
    # except Exception as e:
    #     print(39, e)
    #     conn.rollback()

    # 2
    try:
        # 将数据写入MySQL数据库
        for index, row in df_measure.iterrows():
            if row['unit'] is None:
                sql = "INSERT INTO t_point_measure (name, description, data_type, point_type_id) VALUES (%s, %s, %s, %s)"
                values = (row['name'], row['desc'], row['data_type'], row['type_id'])
            else:
                sql = "INSERT INTO t_point_measure (name, description, data_type, unit, point_type_id) VALUES (%s, %s, %s, %s, %s)"
                values = (row['name'], row['desc'], row['data_type'], row['unit'], row['type_id'])
            # print(values)
            cursor.execute(sql, values)
        # 提交更改并关闭连接
        conn.commit()
    except Exception as e:
        print(51, e)
        print(traceback.print_exc())
        conn.rollback()

    # 3
    try:
        # 将数据写入MySQL数据库
        for index, row in df_status.iterrows():
            sql = "INSERT INTO t_point_status (name, description, data_type, a_status_name, b_status_name, point_type_id) VALUES (%s, %s, %s, %s, %s, %s)"
            values = (row['name'], row['desc'], row['data_type'], row['a_status_name'], row['b_status_name'], row['type_id'])
            cursor.execute(sql, values)
        # 提交更改并关闭连接
        conn.commit()
    except Exception as e:
        print(e)
        print(traceback.print_exc())
        conn.rollback()

    # 4
    try:
        # 将数据写入MySQL数据库
        for index, row in df_discrete.iterrows():
            sql = "INSERT INTO t_point_discrete (name, description, data_type, point_type_id) VALUES (%s, %s, %s, %s)"
            values = (row['name'], row['desc'], row['data_type'], row['type_id'])
            print(values)
            cursor.execute(sql, values)
        # 提交更改并关闭连接
        conn.commit()
    except Exception as e:
        print(traceback.print_exc())
        print(e)
        conn.rollback()

    # 5
    try:
        # 将数据写入MySQL数据库
        for index, row in df_cumulant.iterrows():
            sql = "INSERT INTO t_point_cumulant (name, description, data_type, unit, point_type_id) VALUES (%s, %s, %s, %s, %s)"
            values = (row['name'], row['desc'], row['data_type'], row['unit'], row['type_id'])
            cursor.execute(sql, values)
        # 提交更改并关闭连接
        conn.commit()
    except Exception as e:
        print(traceback.print_exc())
        print(e)
        conn.rollback()

    cursor.close()
    conn.close()


if __name__ == '__main__':
    main()
