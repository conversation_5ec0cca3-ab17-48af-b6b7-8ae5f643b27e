# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/5/30 14:34
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : LogRequestsMiddleware.py
# @Software : PyCharm


import logging
import traceback

from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
import json

from common.constant import EXCLUDED_PATHS

request_logger = logging.getLogger('run')
exception_logger = logging.getLogger('exception')


class RequestLoggingMiddleware(MiddlewareMixin):

    def __init__(self, get_response=None):
        self.get_response = get_response
        self.excluded_paths = EXCLUDED_PATHS

    def process_request(self, request):
        if self._should_log(request.path):
            request_logger.info("=="*100)
            request_logger.info(f"Request IP: {request.META['REMOTE_ADDR']}")
            request_logger.info(f"Request Path: {request.path}")
            request_logger.info(f"Request Method: {request.method}")
            request_logger.info(f"Request GET Params: {request.GET.dict()}")
            try:
                body = json.loads(request.body)
                request_logger.info(f"Request Body: {body}")
            except Exception as e:
                request_logger.info(f"Request Body: {request.body}")
            request_logger.info("*" * 100)
        return None

    def process_response(self, request, response):
        if self._should_log(request.path):
            request_logger.info(f"Response Status Code: {response.status_code}")
            try:
                response_body = json.loads(response.content)
                request_logger.info(f"Response Content: {response_body}")
            except json.JSONDecodeError:
                request_logger.info(f"Response Content: {response.content.decode('utf-8')}")
            request_logger.info("==" * 100)
        return response

    def process_exception(self, request, exception):
        if self._should_log(request.path):
            exception_logger.info("==" * 100)
            exception_logger.error(f"Request Path: {request.path}")
            exception_logger.info(f"Request Method: {request.method}")
            exception_logger.info(f"Request GET Params: {request.GET.dict()}")
            try:
                body = json.loads(request.body)
                exception_logger.info(f"Request Body: {body}")
            except json.JSONDecodeError:
                exception_logger.info(f"Request Body: {request.body.decode('utf-8')}")
            exception_logger.info("*" * 100)

            exception_logger.error(f"Exception occurred at path: {request.path}")
            exception_logger.error(f"Exception: {str(exception)}")
            exception_logger.error(f"Traceback: {traceback.format_exc()}")

        # 可以选择返回一个自定义响应或让 Django 继续处理异常
        # return None
        # 返回自定义的 JSON 响应
        response_data = {
            'code': '500',
            "data": {
                'message': '非常抱歉，服务器出现bug了，请联系技术人员排查。。。',
                'detail': str(exception)
            }

        }
        return JsonResponse(response_data, status=500)

    def _should_log(self, path):
        for excluded_path in self.excluded_paths:
            if path.startswith(excluded_path):
                return False
        return True

