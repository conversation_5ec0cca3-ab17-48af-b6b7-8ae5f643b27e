package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.station.StationCreateDTO;
import com.robestec.analysis.dto.station.StationQueryDTO;
import com.robestec.analysis.dto.station.StationUpdateDTO;
import com.robestec.analysis.entity.Station;
import com.robestec.analysis.vo.StationVO;

import java.util.List;

/**
 * 电站服务接口
 */
public interface StationService extends ISuperService<Station> {

    /**
     * 分页查询电站
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<StationVO> queryStation(StationQueryDTO queryDTO);

    /**
     * 创建电站
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createStation(StationCreateDTO createDTO);

    /**
     * 更新电站
     * @param updateDTO 更新参数
     */
    void updateStation(StationUpdateDTO updateDTO);

    /**
     * 删除电站
     * @param id 记录ID
     */
    void deleteStation(Long id);

    /**
     * 获取电站详情
     * @param id 记录ID
     * @return 记录详情
     */
    StationVO getStation(Long id);

    /**
     * 批量创建电站
     * @param createDTOList 创建参数列表
     */
    void createStationList(List<StationCreateDTO> createDTOList);

    /**
     * 根据英文名称查询电站
     * @param name 英文名称
     * @return 记录列表
     */
    List<StationVO> getStationByName(String name);

    /**
     * 根据注册状态查询电站
     * @param register 注册状态
     * @return 记录列表
     */
    List<StationVO> getStationByRegister(Integer register);

    /**
     * 统计注册状态的电站数量
     * @param register 注册状态
     * @return 记录数量
     */
    Long countByRegister(Integer register);
}
