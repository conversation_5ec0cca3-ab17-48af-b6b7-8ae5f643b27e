#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-05 08:23:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\bit.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-01 11:34:14

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Tools.Utils.time_utils import timeUtils

class Bit(user_Base):
    u'bie类型配置表'
    __tablename__ = "c_bit"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"类型描述")
    name = Column(String(300), nullable=True,comment=u"名称集合以#分割从小到大排")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    station = Column(String(256), nullable=True, comment=u"所属站，多个站用逗号隔开，默认his")

    bit_pageData = relationship("PageData",cascade="all, delete-orphan",passive_deletes=True,backref='bit_pageData')

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        now = timeUtils.getNewTimeStr()
        i = 1
        user_session.merge(Bit(id=i,descr='组系统故障字1',op_ts=now,name='SysFW1CelUT#SysFW1CelOT#SysFW1CelUV#SysFW1CelOV#SysFW1CelDT#SysFW1CelVMEr#SysFW1TepMEr#SysFW1RkUV#SysFW1RkOV#SysFW1RkCmF#SysFW1MdCmF#SysFW1RkChgOC#SysFW1RkDisOC#SysFW1SCPt#SysFW1RkPChgF#SysFW1RkCuMF',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组系统故障字2',op_ts=now,name='SysFW2CelDV#SysFW2DcCtF#SysFW2Sr2#SysFW2Sr3#SysFW2Sr4#SysFW2Sr5#SysFW2Sr6#SysFW2Sr7#SysFW2Sr8#SysFW2Sr9#SysFW2Sr10#SysFW2Sr11#SysFW2Sr12#SysFW2Sr13#SysFW2Sr14#SysFW2Sr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组系统报警字',op_ts=now,name='SysAWCelUT#SysAWCelOT#SysAWCelUV#SysAWCelOV#SysAWCelDV#SysAWRkUV#SysAWRkOV#SysAWRkChgOC#SysAWRkDisOC#SysAWSr9#SysAWSr10#SysAWSr11#SysAWSr12#SysAWCuDnFlw#SysAWDVA#SysAWMTcpA',station='his'));
        i = i+1
        for x in range(1,8):
            name1 = 'Rk{0}FW1CelUT#Rk{0}FW1CelOT#Rk{0}FW1CelUV#Rk{0}FW1CelOV#Rk{0}FW1CelDT#Rk{0}FW1CelVMEr#Rk{0}FW1TepMEr#Rk{0}FW1RkUV#Rk{0}FW1RkOV#Rk{0}FW1RkCmF#Rk{0}FW1MdCmF#Rk{0}FW1RkChgOC#Rk{0}FW1RkDisOC#Rk{0}FW1SCPt#Rk{0}FW1RkPChgF#Rk{0}FW1RkCuMF'.format(x)
            name2 = 'Rk{0}FW2CelDV#Rk{0}FW2Sr2#Rk{0}FW2DcCtF#Rk{0}FW2Sr3#Rk{0}FW2Sr4#Rk{0}FW2Sr5#Rk{0}FW2Sr6#Rk{0}FW2Sr7#Rk{0}FW2Sr8#Rk{0}FW2Sr9#Rk{0}FW2Sr10#Rk{0}FW2Sr11#Rk{0}FW2Sr12#Rk{0}FW2Sr13#Rk{0}FW2Sr14#Rk{0}FW2Sr15'.format(x)
            name3 = 'Rk{0}AWCelUT#Rk{0}AWCelOT#Rk{0}AWCelUV#Rk{0}AWCelOV#Rk{0}AWCelDV#Rk{0}AWRkUV#Rk{0}AWRkOV#Rk{0}AWRkChgOC#Rk{0}AWRkDisOC#Rk{0}AWSr9#Rk{0}AWSr10#Rk{0}AWSr11#Rk{0}AWSr12#Rk{0}AWSr13#Rk{0}AWSr14#Rk{0}AWSr15'.format(x)
            user_session.merge(Bit(id=i,descr='组簇%s故障字1'%x,op_ts=now,name=name1,station='his'));i = i+1;
            user_session.merge(Bit(id=i,descr='组簇%s故障字2'%x,op_ts=now,name=name2,station='his'));i = i+1;
            user_session.merge(Bit(id=i,descr='组簇%s报警字'%x,op_ts=now,name=name3,station='his'));i = i+1;
        
        user_session.merge(Bit(id=i,descr='组PCS状态字',op_ts=now,name='SWDcCont#SWAcCont#SWSr2#SWSysOnle#SWSr4#SWLocal#SWHtbt#SWShutdn#SWReady#SWInvtEnb#SWOrCmLost#SWSr11#SWSr12#SWSr13#SWSr14#SWSr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS故障字1',op_ts=now,name='LFt1PsLsuchg#LFt1AcUnedSts#LFt1AcOperFt#LFt1PsOvCurr#LFt1PsPUTemp#LFt1Sr5#LFt1Sr6#LFt1DcUnedSts#LFt1DcOperFt#LFt1PsFanFt#LFt1PsMaCont#LFt1PsShtCirt#LFt1PsInSysFt#LFt1PsNetLos#LFt1Sr14#LFt1Sr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS故障字2',op_ts=now,name='LFt2BtComLos#LFt2PsEthLek#LFt2PsSynFt#LFt2PsIvtFt#LFt2PsIvtCmLs#LFt2PsIvtWarn#LFt2Sr6#LFt2Sr7#LFt2KWDev#LFt2KVarDev#LFt2DcVoltH#LFt2DcVoltL#LFt2OvldCodn#LFt2OvTmDer#LFt2PsFanFt#LFt2Sr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS故障字3',op_ts=now,name='LFt3PsPUTemp#LFt3StartFt#LFt3AcBkOpUned#LFt3IvtInhdUned#LFt3IvtNoRunUned#LFt3DcBkOpUned#LFt3PsNetLos#LFt3Sr7#LFt3Sr8#LFt3Sr9#LFt3Sr10#LFt3Sr11#LFt3Sr12#LFt3OrCmLost#LFt3FireA1Ft#LFt3FireA2Ft',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS系统故障字1',op_ts=now,name='SFt1DcBkTripOp#SFt1CvtRmDrOp#SFt1TrsRmDrOp#SFt1EstpActive#SFt1SmokDetr#SFt1TrsTmpHFt#SFt1TrsDifProt#SFt1TrsPrtoRy#SFt1Sr8#SFt1TrsRmFan1#SFt1TrsRmFan2#SFt1PsRmFan3#SFt1Sr12#SFt1Sr13#SFt1TrsTmpH#SFt1Ac22BkOp',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS系统故障字2',op_ts=now,name='SFt2Sr0#SFt2AirTmpH#SFt2AirHumyH#SFt2Sr3#SFt2Sr4#SFt2Sr5#SFt2AirTmpL#SFt2Sr7#SFt2Sr8#SFt2Sr9#SFt2Sr10#SFt2Sr11#SFt2Sr12#SFt2Sr13#SFt2Sr14#SFt2Sr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组PCS系统故障字3',op_ts=now,name='SFt3Sr0#SFt3Sr1#SFt3AtIsldProt#SFt3Sr3#SFt3Sr4#SFt3Sr5#SFt3Sr6#SFt3Sr7#SFt3Sr8#SFt3Sr9#SFt3Sr10#SFt3Sr11#SFt3Sr12#SFt3Sr13#SFt3Sr14#SFt3Sr15',station='his'));
        i = i+1
        user_session.merge(Bit(id=i,descr='组系统模式状态',op_ts=now,name='SysMdSr0#SysMdSr1#SysMdSr2#SysMdSr3#SysMdSr4#SysMdSr5#SysMdSr6#SysMdSr7#SysMdChag#SysMdDisg#SysMdReady#SysMdIdle#SysMdOffline#SysMdSr13#SysMdSr14#SysMdSr15',station='his'));
        
        user_session.commit()
        user_session.close()
        

    def __repr__(self):
        return "{'id':%s,'descr':'%s','name':'%s','op_ts':'%s','station':'%s'}" % (self.id,self.descr,self.name,self.op_ts,self.station)
    
