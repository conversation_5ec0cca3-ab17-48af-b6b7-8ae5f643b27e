#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-02-20 14:14:54
#@FilePath     : \RHBESS_Service\Tools\DB\binhai_bms_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:08:13


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")


BINHAI_HOSTNAME = model_config.get('mysql', "HIS_HOSTNAME")
BINHAI_PORT = model_config.get('mysql', "HIS_PORT")
BINHAI_DATABASE1 = model_config.get('mysql', "BINHAI_DATABASE1")
BINHAI_DATABASE2 = model_config.get('mysql', "BINHAI_DATABASE2")
BINHAI_USERNAME = model_config.get('mysql', "HIS_USERNAME")
BINHAI_PASSWORD = model_config.get('mysql', "HIS_PASSWORD")

hisdb1='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BINHAI_USERNAME,
    BINHAI_PASSWORD,
    BINHAI_HOSTNAME,
    BINHAI_PORT,
    BINHAI_DATABASE1
)
BINHAI_engine1 = create_engine(hisdb1,echo=False,max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_BINHAI_session1 = scoped_session(sessionmaker(BINHAI_engine1,autoflush=True))
BINHAI_Base1 = declarative_base(BINHAI_engine1)
binhai_session1 = _BINHAI_session1()

hisdb2='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BINHAI_USERNAME,
    BINHAI_PASSWORD,
    BINHAI_HOSTNAME,
    BINHAI_PORT,
    BINHAI_DATABASE2
)
BINHAI_engine2 = create_engine(hisdb2,echo=False,max_overflow=5, pool_size=30, pool_timeout=8, pool_pre_ping=True,pool_recycle=1800)
_BINHAI_session2 = scoped_session(sessionmaker(BINHAI_engine2,autoflush=True))
BINHAI_Base2 = declarative_base(BINHAI_engine2)
binhai_session2 = _BINHAI_session2()


