#!/usr/bin/env python
# coding=utf-8
#@Information: 电站一天总有功功率，1分钟一个点
#<AUTHOR> WYJ
#@Date         : 2023-02-23 13:43:25
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\froze_report copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-23 13:43:27



from sqlalchemy import func
import sys,os,getopt
from Tools.Utils.num_utils import *
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session
from Tools.DB.mysql_his import  his_session ,his_engine,HIS_DATABASE
from Tools.DB.halun_his import halun_session,halun_engine,HALUN_DATABASE
from Tools.DB.taicang_his import taicang_session,taicang_engine,TAICANG_DATABASE
from Tools.DB.binhai1_his import binhai1_session,binhai1_engine,BINHAI1_DATABASE
from Tools.DB.binhai2_his import binhai2_session,binhai2_engine,BINHAI2_DATABASE
from Tools.DB.ygzhen_his import ygzhen1_engine,ygzhen2_engine,ygzhen1_session,ygzhen2_session,YGZHEN1_DATABASE,YGZHEN2_DATABASE
from Tools.DB.baodian_his import baodian1_engine,baodian1_session,BAODIAN1_DATABASE,baodian2_engine,baodian2_session,BAODIAN2_DATABASE,\
baodian3_engine,baodian3_session,BAODIAN3_DATABASE,baodian4_engine,baodian4_session,BAODIAN4_DATABASE,baodian5_engine,baodian5_session,BAODIAN5_DATABASE
from Tools.Utils.time_utils import timeUtils
from Application.Models.His.r_ACDMS import HisACDMS
from apscheduler.schedulers.blocking import BlockingScheduler
from Application.Models.User.station_relation import StationR
from Tools.DB.redis_con import r as redis_r
import numpy as np
import pandas as pd
# from apscheduler.schedulers.background import BackgroundScheduler
import logging
logging.basicConfig()
db_his = {"taicang":[taicang_session],"binhai":[binhai1_session,binhai2_session],"halun":[halun_session],"ygzhen":[ygzhen1_session,ygzhen2_session],
      "baodian":[baodian1_session,baodian2_session,baodian3_session,baodian4_session,baodian5_session]}
station = user_session.query(StationR.station_name,StationR.active_power_name).all()



def calculation():
    now_time= timeUtils.getNewTimeStr()
    # print now_time
    for st in station:
        arr = []
        s_name = st[0]
        power_names = eval(st[1])
        v = eval(redis_r.get(s_name + '_power'))
        for n in power_names:
            r_v = real_data('measure',n,'db')['value']
            arr.append(r_v)
        del v['value'][0]
        del v['time'][0]
        v['value'].append(np.round(np.sum(arr),2))
        v['time'].append(now_time)
        redis_r.set(s_name + '_power',str(v))
        # print s_name ,'*************'



def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(calculation, 'interval', minutes=1)  # 1分钟获取一次数据
    # scheduler.add_job(fault, 'interval', seconds=60*15)  # 每 15分钟执行一次
    # scheduler.add_job(calculation, 'cron', hour=1,misfire_grace_time=600)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.start()



def _select_get_his_value(d, tables,name, lastsec, nowsec, nowdate,lastdate, jiange):
    '''获取历史数据'''
    data = {'time': [], 'value': []}
    # HisTable_mm = HisACDMS(tables[0])
    for table in tables:
        HisTable_m = HisACDMS(table)
        values = d.query(HisTable_m.value, HisTable_m.ots).filter(HisTable_m.name==name,HisTable_m.dts_s.between(lastsec,nowsec)).order_by(HisTable_m.dts_s.asc()).all()  # 查询当前月
        for val in values:
            data['time'].append(str(val[1])[:19])
            data['value'].append(val[0])

    # 查询上一小时表里的数据
    HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
    values = d.query(HisTable_l.value, HisTable_l.ots).filter(HisTable_l.name==name,HisTable_l.dts_s.between(lastsec,nowsec)).order_by(HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
    for val in values:
        data['time'].append(str(val[1])[:19])
        data['value'].append(val[0])

    # value = d.query(HisTable_mm.value).filter(HisTable_mm.name==name,HisTable_mm.dts_s <= lastsec).order_by(HisTable_mm.dts_s.desc()).first()  # 查询当前月
    # print '初始数据-------',data
    if data['time']:
        data['time'].insert(0, lastdate)
        data['value'].insert(0, data['value'][0])
        data['time'].append(nowdate)
        data['value'].append(data['value'][-1])
    else:
        return data
    # 去重
    times = list(set(data['time']))
    times.sort()
    va = []
    for ti in times:
        ind = data['time'].index(ti)
        va.append(data['value'][ind])
    # print '标准返回格式……………………………………………………………………：',complete_data(data,'1T')
    data['time'] = times
    data['value'] = va
    return complete_data(data, jiange)
def _return_db_con(db,db_con,name):
    '''根据名称判定数据库链接'''
    if db =='halun' or db == 'taicang':
        return db_con[0]
    else:
        ind = int(name.split('.')[0][-1])
        return db_con[ind-1]
    

def get_station_first_data():
    '''首次获取电站数据'''

    for st in station:
        s_name = st[0]
        power_names = eval(st[1])
        v = redis_r.get(s_name + '_power')
        
        if not v:  # reis 中无数据
            nowdate = timeUtils.getNewTimeStr()
            nowsec = timeUtils.timeStrToTamp(nowdate)
            lastsec = nowsec - 24 * 3600
            lastdate = timeUtils.ssTtimes(lastsec)
            a = timeUtils.getBetweenMonth(lastdate, nowdate)  # 计算时间范围内的所有年月
            tables = 'r_measure' + pd.Series(a)
            station_obj = {'time':[],'value':[]}  # 数据返回结构
            station_data = []
            for cname in power_names:
                d = _return_db_con(s_name, db_his[s_name],cname)  # 获取数据库链接
                values = _select_get_his_value(d, tables, cname, lastsec, nowsec, lastdate, nowdate, '1T')
                if values['value']:
                    station_data.append(values['value'])
                if not station_obj['time'] and values['time']:
                    station_obj['time'] = values['time']
            ass = np.sum(station_data, axis=0)
            station_obj['value'] = np.round(ass,2).tolist()
            redis_r.set(s_name + '_power',str(station_obj))
        
            



if __name__ == '__main__':
    get_station_first_data()
    RunClearFileAndData()
    

        