import datetime
import logging
import os
from collections import defaultdict
from enum import Enum
from pathlib import Path

import math
import openpyxl
# import tornado.web

from Application.EqAccount.ElePriceDecision.tools import merge_dicts, merge_dicts_with_default
from Application.Models.base_handler import BaseHandler
from Application.Models.ElePriceDescision.models import *
from Tools.DecisionDB.ele_base import user_session
from Tools.Utils.mimio_tool import MinioTool


async def upload_file(file_name, file_path, bucket_name='rhyc'):
    minioClient = MinioTool()
    minioClient.create_bucket(bucket_name)
    url = minioClient.upload_local_file(file_name, file_path)
    print('文件地址为【文件在浏览器打开会直接下载，放到index.html 中使用img引入查看】：\n', url)
    return url


class GetCProvinces(BaseHandler):
    """省份列表"""""

    # @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)
        provinces = user_session.query(CProvince).all()
        provinces_ = [{"id": province.id, "name": province.name} for province in provinces if province.is_use == "1"
                      and province.name != "中国"]

        for province in provinces_:
            cities = user_session.query(CCity).filter(CCity.province_id == province["id"]).all()
            cities_ = [{"id": city.id, "name": city.name} for city in cities if city.is_use == "1"]
            province["cities"] = cities_
            for city in province["cities"]:
                counties = user_session.query(CCounty).filter(CCounty.city_id == city["id"]).all()
                counties_ = [{"id": county.id, "name": county.name} for county in counties if county.is_use == "1"]
                city["counties"] = counties_

        user_session.close()
        self.returnTypeSuc(provinces_)


class GetSOP(BaseHandler):
    """获取交易SOP流程图"""""

    # @tornado.web.authenticated
    def get(self):
        self.refreshSession()
        user = self.get_current_user()
        province_id = self.get_argument("province_id")
        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province = user_session.query(CProvince).filter(CProvince.id == province_id).first()
        if not province:
            return self.customError("省份不存在")

        province_name = province.name
        minioClient = MinioTool()
        sop_url = minioClient.get_download_url('rhyc', province_name + "_SOP.png")

        user_session.close()
        self.returnTypeSuc(sop_url)


class RealMeteorologicalSigns(Enum):
    tem = "温度",
    feel_tem = "体感温度",
    weather_con = "天气状况",
    wind_angle = "风向角",
    wind = "风向",
    wind_power = "风力等级",
    wind_speed = "风速",
    hum = "相对湿度",
    rain_hour = "小时累积降水量",
    rain_probab = "降水概率"
    air_press = "大气压强",
    see = "能见度",
    cloud_cover = "云量",
    dew_tem = "露点温度",


class PreMeteorologicalSigns(Enum):
    tem = "温度",
    weather_con = "天气状况",
    wind_angle = "风向角",
    wind = "风向",
    wind_power = "风力等级",
    wind_speed = "风速",
    hum = "相对湿度",
    rain_hour = "小时累积降水量",
    rain_probab = "降水概率"
    air_press = "大气压强",
    cloud_cover = "云量",
    dew_tem = "露点温度",


class GetMeteorologicalData(BaseHandler):
    """获取气象地图数据"""""

    def get(self):
        self.refreshSession()
        province_id = self.get_argument("province_id")
        city_id = self.get_argument("city_id", default=None)
        county_id = self.get_argument("county_id", default=None)
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        date_type = self.get_argument("date_type", default="real")
        sign = self.get_argument("sign", default="tem")

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        # 校验参数city_id
        if city_id:
            cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
            cities_id = [city.id for city in cities if city.is_use == "1"]
            if int(city_id) not in cities_id:
                return self.customError("城市/区ID参数错误")

            # 校验参数county_id
            if county_id:
                counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                counties_id = [county.id for county in counties if county.is_use == "1"]
                if int(county_id) not in counties_id:
                    return self.customError("县ID参数错误")

        if county_id and not city_id:
            return self.customError("城市/区ID参数缺失")

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数date_type
        if date_type not in ["real", "pre"]:
            return self.customError("参数date_type错误")

        # 校验参数sign
        if date_type == "real":
            if sign not in RealMeteorologicalSigns.__members__:
                return self.customError(f"实时气象数据无{sign}数据")
        else:
            if sign not in PreMeteorologicalSigns.__members__:
                return self.customError(f"预报天气数据无{sign}数据")

        # 查询省的数据
        if not county_id and not city_id:
            if date_type == "real":
                data = user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.flag == 2, RWeatherReal.province_id == int(province_id)).all()
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.flag == 2, RWeatherPre.province_id == int(province_id)).all()

        # 查询市的数据
        elif not county_id and city_id:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id))
                        .all())
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id)).all()

        # 查询县的数据
        else:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id),
                    RWeatherReal.county_id == int(county_id))
                        .all())
            else:
                data = (user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id),
                    RWeatherPre.county_id == int(county_id))
                        .all())

        # 处理返回数据
        temp_list = []
        if data:
            for d in data:
                print(154, d.tem, d.day, d.moment, d.longitude, d.latitude)
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "longitude": float(d.longitude),
                    "latitude": float(d.latitude),
                    "province": d.province.name,
                    "city": d.city.name,
                    "county": d.county.name if d.county else None,
                    "value": float(getattr(d, sign)) if not isinstance(getattr(d, sign), str) else getattr(d, sign)
                }
                temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        grouped_datas = defaultdict(list)
        for i in temp_list:
            grouped_datas[i['datetime']].append(i)

        new_data = []
        for k, v in grouped_datas.items():
            new_data.append({
                "datetime": k,
                "infos": v
            })

        user_session.close()
        self.returnTypeSuc(new_data)


class GetMeteorologicalDataForTable(BaseHandler):
    """获取气象表格数据"""""

    def get(self):
        self.refreshSession()
        province_id = self.get_argument("province_id")
        city_id = self.get_argument("city_id", default=None)
        county_id = self.get_argument("county_id", default=None)
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        date_type = self.get_argument("date_type", default="real")
        page = int(self.get_argument("page")) if self.get_argument("page", None) else None
        page_size = 33
        # sign = self.get_argument("sign", default="tem")

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        # 校验参数city_id
        if city_id:
            cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
            cities_id = [city.id for city in cities if city.is_use == "1"]
            if int(city_id) not in cities_id:
                return self.customError("城市/区ID参数错误")

            # 校验参数county_id
            if county_id:
                counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                counties_id = [county.id for county in counties if county.is_use == "1"]
                if int(county_id) not in counties_id:
                    return self.customError("县ID参数错误")

        if county_id and not city_id:
            return self.customError("城市/区ID参数缺失")

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数date_type
        if date_type not in ["real", "pre"]:
            return self.customError("参数date_type错误")

        # # 校验参数sign
        # if date_type == "real":
        #     if sign not in RealMeteorologicalSigns.__members__:
        #         return self.customError(f"实时气象数据无{sign}数据")
        # else:
        #     if sign not in PreMeteorologicalSigns.__members__:
        #         return self.customError(f"预报天气数据无{sign}数据")

        # 查询省的数据
        if not county_id and not city_id:
            if date_type == "real":
                data = user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.province_id == int(province_id)).all()
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.province_id == int(province_id)).all()

        # 查询市的数据
        elif not county_id and city_id:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id))
                        .all())
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id)).all()

        # 查询县的数据
        else:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id),
                    RWeatherReal.county_id == int(county_id))
                        .all())
            else:
                data = (user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id),
                    RWeatherPre.county_id == int(county_id))
                        .all())

        # 处理返回数据
        temp_list = []
        if data:
            if date_type == "real":
                for d in data:
                    temp_dict = {
                        "day": d.day.strftime("%Y-%m-%d"),
                        "moment": d.moment,
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                        "province": d.province.name,
                        "city": d.city.name,
                        "county": d.county.name if d.county else None,

                        "tem": float(d.tem),
                        "feel_tem": float(d.feel_tem),
                        "weather_con": d.weather_con,
                        "wind_angle": d.wind_angle,
                        "wind": d.wind,
                        "wind_power": d.wind_power,
                        "wind_speed": float(d.wind_speed),
                        "hum": d.hum,
                        "rain_hour": d.rain_hour,
                        "rain_probab": d.rain_probab,
                        "air_press": d.air_press,
                        "see": float(d.see),
                        "cloud_cover": d.cloud_cover,
                        "dew_tem": float(d.dew_tem)
                    }
                    temp_list.append(temp_dict)
            else:
                for d in data:
                    temp_dict = {
                        "day": d.day.strftime("%Y-%m-%d"),
                        "moment": d.moment,
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                        "province": d.province.name,
                        "city": d.city.name,
                        "county": d.county.name if d.county else None,

                        "tem": float(d.tem),
                        "weather_con": d.weather_con,
                        "wind_angle": d.wind_angle,
                        "wind": d.wind,
                        "wind_power": d.wind_power,
                        "wind_speed": float(d.wind_speed),
                        "hum": d.hum,
                        "rain_hour": d.rain_hour,
                        "rain_probab": d.rain_probab,
                        "air_press": d.air_press,
                        "cloud_cover": d.cloud_cover,
                        "dew_tem": float(d.dew_tem)
                    }
                    temp_list.append(temp_dict)

        # # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

        temp_map_dict = {}
        for i in RealMeteorologicalSigns:
            temp_map_dict[i.name] = i.value

        # 手动分页
        if page:
            total_pages = math.ceil(len(data) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(data) + 1



            data_ = {
                "total": len(data),
                "total_pages": total_pages,
                "page": page,
                "detail": data[start_index:end_index],
                "maps": temp_map_dict
            }
        else:
            data_ = {
                "total": len(data),
                "total_pages": 1,
                "page": page,
                "detail": data,
                "maps": temp_map_dict
            }

        user_session.close()
        self.returnTypeSuc(data_)


class ExportMeteorologicalDataForTable(BaseHandler):
    """导出气象表格数据"""""

    async def get(self):
        self.refreshSession()
        province_id = self.get_argument("province_id")
        city_id = self.get_argument("city_id", default=None)
        county_id = self.get_argument("county_id", default=None)
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        date_type = self.get_argument("date_type", default="real")
        # page = int(self.get_argument("page", default="1"))
        # page_size = 33
        # sign = self.get_argument("sign", default="tem")

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        # 校验参数city_id
        if city_id:
            cities = user_session.query(CCity).filter(CCity.province_id == int(province_id)).all()
            cities_id = [city.id for city in cities if city.is_use == "1"]
            if int(city_id) not in cities_id:
                return self.customError("城市/区ID参数错误")

            # 校验参数county_id
            if county_id:
                counties = user_session.query(CCounty).filter(CCounty.city_id == int(city_id)).all()
                counties_id = [county.id for county in counties if county.is_use == "1"]
                if int(county_id) not in counties_id:
                    return self.customError("县ID参数错误")

        if county_id and not city_id:
            return self.customError("城市/区ID参数缺失")

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数date_type
        if date_type not in ["real", "pre"]:
            return self.customError("参数date_type错误")

        # # 校验参数sign
        # if date_type == "real":
        #     if sign not in RealMeteorologicalSigns.__members__:
        #         return self.customError(f"实时气象数据无{sign}数据")
        # else:
        #     if sign not in PreMeteorologicalSigns.__members__:
        #         return self.customError(f"预报天气数据无{sign}数据")

        # 查询省的数据
        if not county_id and not city_id:
            if date_type == "real":
                data = user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.province_id == int(province_id)).all()
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.province_id == int(province_id)).all()

        # 查询市的数据
        elif not county_id and city_id:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id))
                        .all())
            else:
                data = user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id)).all()

        # 查询县的数据
        else:
            if date_type == "real":
                data = (user_session.query(RWeatherReal).filter(
                    RWeatherReal.day >= start_day, RWeatherReal.day <= end_day,
                    RWeatherReal.flag == 3, RWeatherReal.province_id == int(province_id),
                    RWeatherReal.city_id == int(city_id),
                    RWeatherReal.county_id == int(county_id))
                        .all())
            else:
                data = (user_session.query(RWeatherPre).filter(
                    RWeatherPre.day >= start_day, RWeatherPre.day <= end_day,
                    RWeatherPre.flag == 3, RWeatherPre.province_id == int(province_id),
                    RWeatherPre.city_id == int(city_id),
                    RWeatherPre.county_id == int(county_id))
                        .all())

        # 处理返回数据
        temp_list = []
        if data:
            if date_type == "real":
                for d in data:
                    temp_dict = {
                        "day": d.day.strftime("%Y-%m-%d"),
                        "moment": d.moment,
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                        "province": d.province.name,
                        "city": d.city.name,
                        "county": d.county.name if d.county else None,
                        "longitude": float(d.longitude),
                        "latitude": float(d.latitude),

                        "tem": float(d.tem),
                        "feel_tem": float(d.feel_tem),
                        "weather_con": d.weather_con,
                        "wind_angle": d.wind_angle,
                        "wind": d.wind,
                        "wind_power": d.wind_power,
                        "wind_speed": float(d.wind_speed),
                        "hum": d.hum,
                        "rain_hour": d.rain_hour,
                        "rain_probab": d.rain_probab,
                        "air_press": d.air_press,
                        "see": float(d.see),
                        "cloud_cover": d.cloud_cover,
                        "dew_tem": float(d.dew_tem)
                    }
                    temp_list.append(temp_dict)
            else:
                for d in data:
                    temp_dict = {
                        "day": d.day.strftime("%Y-%m-%d"),
                        "moment": d.moment,
                        "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                        "province": d.province.name,
                        "city": d.city.name,
                        "county": d.county.name if d.county else None,
                        "longitude": float(d.longitude),
                        "latitude": float(d.latitude),

                        "tem": float(d.tem),
                        "weather_con": d.weather_con,
                        "wind_angle": d.wind_angle,
                        "wind": d.wind,
                        "wind_power": d.wind_power,
                        "wind_speed": float(d.wind_speed),
                        "hum": d.hum,
                        "rain_hour": d.rain_hour,
                        "rain_probab": d.rain_probab,
                        "air_press": d.air_press,
                        "cloud_cover": d.cloud_cover,
                        "dew_tem": float(d.dew_tem)
                    }
                    temp_list.append(temp_dict)

        # # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []

        # 获取地区名称
        if not county_id and not city_id:
            location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
        elif city_id and not county_id:
            province = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
            location = province + '-' + user_session.query(CCity).filter(CCity.id == int(city_id)).first().name
        elif county_id:
            province = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
            city = province + '-' + user_session.query(CCity).filter(CCity.id == int(city_id)).first().name
            location = city + '-' + user_session.query(CCounty).filter(CCounty.id == int(county_id)).first().name
        else:
            location = ''

        # 上传并返回下载url
        workbook = openpyxl.Workbook()
        sheet1 = workbook.active

        BASE_DIR = Path(__file__).resolve().parent
        start_day_str = start_day.strftime("%Y-%m-%d")
        end_day_str = end_day.strftime("%Y-%m-%d")
        file_name = f"{start_day_str}~{end_day_str}{location}气象数据.xlsx"
        path = os.path.join(BASE_DIR, file_name)
        sheet1.title = '气象数据'

        if date_type == 'real':
            table_titles = ["日期", "时间", "地区", "经度", "纬度", "温度", "体感温度", "天气状况", "风向角", "风向",
                            "风力等级",
                            "风速", "相对湿度", "小时累积降水量", "降水概率", "大气压强", "能见度", "云量", "露点温度"]
        else:
            table_titles = ["日期", "时间", "地区", "经度", "纬度", "温度", "天气状况", "风向角", "风向", "风力等级",
                            "风速", "相对湿度", "小时累积降水量", "降水概率", "大气压强", "云量", "露点温度"]
        # sheet1 写入标头
        for col_num, header in enumerate(table_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet1[f'{col_letter}1'] = header

        # 准备数据
        data_array = list()
        if data:
            if date_type == 'real':
                for t in data:
                    li = [t['day'],
                          t['moment'],
                          t['county'] if t['county'] else t['city'],
                          t['longitude'],
                          t['latitude'],
                          t['tem'],
                          t['feel_tem'],
                          t['weather_con'],
                          t['wind_angle'],
                          t['wind'],
                          t['wind_power'],
                          t['wind_speed'],
                          t['hum'],
                          t['rain_hour'],
                          t['rain_probab'],
                          t['air_press'],
                          t['see'],
                          t['cloud_cover'],
                          t['dew_tem']]

                    data_array.append(li)
            else:
                for t in data:
                    li = [t['day'],
                          t['moment'],
                          t['county'] if t['county'] else t['city'],
                          t['longitude'],
                          t['latitude'],
                          t['tem'],
                          t['weather_con'],
                          t['wind_angle'],
                          t['wind'],
                          t['wind_power'],
                          t['wind_speed'],
                          t['hum'],
                          t['rain_hour'],
                          t['rain_probab'],
                          t['air_press'],
                          t['cloud_cover'],
                          t['dew_tem']]

                    data_array.append(li)
            # sorted(data_array, key=lambda x: x[0])

        # 写入工作表
        for row_num, row_data in enumerate(data_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}{row_num}'] = cell_value
        workbook.save(path)
        url = await upload_file(file_name, path)

        try:
            os.remove(path)
        except Exception as e:
            pass

        user_session.close()
        self.returnTypeSuc(url)


class MarketInfoDataTypes(Enum):
    current = "日前市场",
    realtime = "实时市场",
    overview = "总览",


class MarketInfoSigns(Enum):
    clear_data = "出清数据",
    new_energy = "新能源出力",
    total_load = "总负荷",


class MarketInfo(BaseHandler):
    """当前市场/实时市场/总览--出清数据/新能源出力/总负荷/总览数据...."""""

    @staticmethod
    async def get_series_data(start_day, end_day, province_id, table_name):
        dict_serie_id = (user_session.query(CDictSery).filter(CDictSery.name == table_name,
                                                              CDictSery.province_id == province_id,
                                                              CDictSery.is_use == "1", ).first()).id
        data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                             RSeriesDataShanxi.day <= end_day,
                                                             RSeriesDataShanxi.series_id == dict_serie_id,
                                                             RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "value1": d.value1,
                "value2": d.value2,
                "value3": d.value3,
                "name": d.name
            }
            temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        date_type = self.get_argument("date_type", default="real")
        sign = self.get_argument("sign", default=None)

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数date_type
        if date_type not in MarketInfoDataTypes.__members__:
            return self.customError(f"参数date_type: {date_type}错误")

        # 校验参数sign
        if sign and sign not in MarketInfoSigns.__members__:
            return self.customError(f"参数sign:{sign}错误")

        temp_array = []
        temp_map_dict = {}
        # 日前市场
        if date_type == "current":

            # 日前市场--出清数据
            if sign == MarketInfoSigns.clear_data.name:
                temp_map_dict = {
                    "value1": "日前出清电量",
                    "value2": "日前出清电价"
                }
                # 日前出清电量
                data1 = await self.get_series_data(start_day, end_day, province_id, "日前出清电量")
                if data1:
                    temp_list_1 = []
                    for i in range(len(data1)):
                        temp_dict = {
                            "datetime": data1[i]["datetime"],
                            "value1": float(data1[i]["value1"]),
                        }
                        temp_list_1.append(temp_dict)
                    temp_array.append(temp_list_1)

                # 日前出清电价
                data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息")
                if data2:
                    temp_list_2 = []
                    for i in range(len(data2)):
                        temp_dict = {
                            "datetime": data2[i]["datetime"],
                            "value2": eval(data2[i]["value2"])
                        }
                        temp_list_2.append(temp_dict)
                    temp_array.append(temp_list_2)

            # 日前市场--新能源出力
            elif sign == MarketInfoSigns.new_energy.name:
                temp_map_dict = {
                    "value1": "预测光伏出力",
                    "value2": "预测风电出力",
                    "value3": "预测新能源总出力"
                }
                # 预测光伏出力/预测风电出力/预测新能源总出力
                data = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测")
                if data:
                    temp_list = []
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "value1": eval(data[i]["value2"]),
                            "value2": float(data[i]["value1"]),
                            "value3": eval(data[i]["value3"])
                        }
                        temp_list.append(temp_dict)
                    temp_array.append(temp_list)

            # 日前市场--总负荷
            elif sign == MarketInfoSigns.total_load.name:
                temp_map_dict = {
                    "value1": "日前非市场化机组出力预测",
                    "value2": "机组检修总容量",
                    "value3": "日前联络线计划信息（加总）",
                    "value4": "预测新能源总出力",
                    "value5": "全省用电负荷预测信息"
                }
                # 日前非市场化机组出力预测
                data1 = await self.get_series_data(start_day, end_day, province_id, "日前非市场化机组出力预测")
                if data1:
                    temp_list_1 = []
                    for i in range(len(data1)):
                        temp_dict = {
                            "datetime": data1[i]["datetime"],
                            "value1": float(data1[i]["value1"]),
                        }
                        temp_list_1.append(temp_dict)
                    temp_array.append(temp_list_1)

                # 机组检修总容量
                data2 = await self.get_series_data(start_day, end_day, province_id, "机组检修总容量")
                if data2:
                    temp_list_2 = []
                    for i in range(len(data2)):
                        temp_dict = {
                            "datetime": data2[i]["datetime"],
                            "value2": float(data2[i]["value1"]),
                        }
                        temp_list_2.append(temp_dict)
                    temp_array.append(temp_list_2)

                # 日前联络线计划信息（加总）
                data3 = await self.get_series_data(start_day, end_day, province_id, "日前联络线计划信息（加总）")
                if data3:
                    temp_list_3 = []
                    for i in range(len(data3)):
                        temp_dict = {
                            "datetime": data3[i]["datetime"],
                            "value3": float(data3[i]["value1"]),
                        }
                        temp_list_3.append(temp_dict)
                    temp_array.append(temp_list_3)

                # 预测新能源总出力
                data4 = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测")
                if data4:
                    temp_list_4 = []
                    for i in range(len(data4)):
                        temp_dict = {
                            "datetime": data4[i]["datetime"],
                            "value4": eval(data4[i]["value3"]),
                        }
                        temp_list_4.append(temp_dict)
                    temp_array.append(temp_list_4)

                # 全省用电负荷预测信息
                data5 = await self.get_series_data(start_day, end_day, province_id, "全省用电负荷预测信息")

                if data5:
                    temp_list_5 = []
                    for i in range(len(data5)):
                        temp_dict = {
                            "datetime": data5[i]["datetime"],
                            "value5": float(data5[i]["value1"])
                        }
                        temp_list_5.append(temp_dict)
                    temp_array.append(temp_list_5)

        # 实时市场
        elif date_type == "realtime":
            # 实时市场--出清数据
            if sign == MarketInfoSigns.clear_data.name:
                temp_map_dict = {
                    "value1": "实时出清电量",
                    "value2": "实时出清价格"
                }
                # 实时出清电量
                data1 = await self.get_series_data(start_day, end_day, province_id, "实时出清电量")
                if data1:
                    temp_list_1 = []
                    for i in range(len(data1)):
                        temp_dict = {
                            "datetime": data1[i]["datetime"],
                            "value1": float(data1[i]["value1"]),
                        }
                        temp_list_1.append(temp_dict)
                    temp_array.append(temp_list_1)

                # 实时出清价格
                data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息")
                if data2:
                    temp_list_2 = []
                    for i in range(len(data2)):
                        temp_dict = {
                            "datetime": data2[i]["datetime"],
                            "value2": eval(data2[i]["value2"]),
                        }
                        temp_list_2.append(temp_dict)
                    temp_array.append(temp_list_2)

            # 实时市场--新能源出力
            elif sign == MarketInfoSigns.new_energy.name:
                temp_map_dict = {
                    "value1": "光伏实际值",
                    "value2": "风电实际值",
                    "value3": "新能源总加实际值"
                }
                # 光伏实际值/风电实际值/新能源总加实际值
                data = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力")
                if data:
                    temp_list = []
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "value1": eval(data[i]["value2"]),
                            "value2": float(data[i]["value1"]),
                            "value3": eval(data[i]["value3"])
                        }
                        temp_list.append(temp_dict)
                    temp_array.append(temp_list)

            # 实时市场---总负荷
            elif sign == MarketInfoSigns.total_load.name:
                temp_map_dict = {
                    "value1": "实时非市场化机组出力曲线",
                    "value2": "新能源总实时出力",
                    "value3": "实时联络线计划（加总）",
                    "value4": "水电总实时出力",
                    "value5": "系统负荷实际值"
                }
                # 实时非市场化机组出力曲线
                data1 = await self.get_series_data(start_day, end_day, province_id, "实时非市场化机组出力曲线")
                if data1:
                    temp_list_1 = []
                    for i in range(len(data1)):
                        temp_dict = {
                            "datetime": data1[i]["datetime"],
                            "value1": float(data1[i]["value1"]),
                        }
                        temp_list_1.append(temp_dict)
                    temp_array.append(temp_list_1)

                # 新能源总实时出力
                data2 = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力")
                if data2:
                    temp_list_2 = []
                    for i in range(len(data2)):
                        temp_dict = {
                            "datetime": data2[i]["datetime"],
                            "value2": eval(data2[i]["value3"]),
                        }
                        temp_list_2.append(temp_dict)
                    temp_array.append(temp_list_2)

                # 实时联络线计划（加总）
                data3 = await self.get_series_data(start_day, end_day, province_id, "实时联络线计划（加总）")
                if data3:
                    temp_list_3 = []
                    for i in range(len(data3)):
                        temp_dict = {
                            "datetime": data3[i]["datetime"],
                            "value3": float(data3[i]["value1"]),
                        }
                        temp_list_3.append(temp_dict)
                    temp_array.append(temp_list_3)

                # 水电总实时出力
                data4 = await self.get_series_data(start_day, end_day, province_id, "水电总实时出力")
                if data4:
                    temp_list_4 = []
                    for i in range(len(data4)):
                        temp_dict = {
                            "datetime": data4[i]["datetime"],
                            "value1": float(data4[i]["value1"]),
                        }
                        temp_list_4.append(temp_dict)
                    temp_array.append(temp_list_4)

                # 系统负荷实际值
                data5 = await self.get_series_data(start_day, end_day, province_id, "系统实时负荷频率备用情况")
                if data5:
                    temp_list_5 = []
                    for i in range(len(data5)):
                        temp_dict = {
                            "datetime": data5[i]["datetime"],
                            "value5": float(data5[i]["value1"]),
                        }
                        temp_list_5.append(temp_dict)
                    temp_array.append(temp_list_5)

        # 总览
        else:
            # 日前出清电量
            data1 = await self.get_series_data(start_day, end_day, province_id, "日前出清电量")
            if data1:
                temp_list_1 = []
                for i in range(len(data1)):
                    temp_dict = {
                        "datetime": data1[i]["datetime"],
                        "value1": float(data1[i]["value1"]),
                    }
                    temp_list_1.append(temp_dict)
                temp_array.append(temp_list_1)

            # 日前出清电价
            data2 = await self.get_series_data(start_day, end_day, province_id, "现货出清电价信息")
            if data2:
                temp_list_2 = []
                for i in range(len(data2)):
                    temp_dict = {
                        "datetime": data2[i]["datetime"],
                        "value2": float(data2[i]["value1"]),
                    }
                    temp_list_2.append(temp_dict)
                temp_array.append(temp_list_2)

            # 预测光伏出力/预测风电出力/预测新能源总出力
            data3 = await self.get_series_data(start_day, end_day, province_id, "日前新能源负荷预测")
            if data3:
                temp_list_3 = []
                for i in range(len(data3)):
                    temp_dict = {
                        "datetime": data3[i]["datetime"],
                        "value3": eval(data3[i]["value2"]),
                        "value4": float(data3[i]["value1"]),
                        "value5": eval(data3[i]["value3"]),
                    }
                    temp_list_3.append(temp_dict)
                temp_array.append(temp_list_3)

            # 日前非市场化机组出力预测
            data4 = await self.get_series_data(start_day, end_day, province_id, "日前非市场化机组出力预测")
            if data4:
                temp_list_4 = []
                for i in range(len(data4)):
                    temp_dict = {
                        "datetime": data4[i]["datetime"],
                        "value6": float(data4[i]["value1"]),
                    }
                    temp_list_4.append(temp_dict)
                temp_array.append(temp_list_4)

            # 机组检修总容量
            data5 = await self.get_series_data(start_day, end_day, province_id, "机组检修总容量")
            if data5:
                temp_list_5 = []
                for i in range(len(data5)):
                    temp_dict = {
                        "datetime": data5[i]["datetime"],
                        "value7": float(data5[i]["value1"]),
                    }
                    temp_list_5.append(temp_dict)
                temp_array.append(temp_list_5)

            # 日前联络线计划信息（加总）
            data6 = await self.get_series_data(start_day, end_day, province_id, "日前联络线计划信息（加总）")
            if data6:
                temp_list_6 = []
                for i in range(len(data6)):
                    temp_dict = {
                        "datetime": data6[i]["datetime"],
                        "value8": float(data6[i]["value1"]),
                    }
                    temp_list_6.append(temp_dict)
                temp_array.append(temp_list_6)

            # 全省用电负荷预测信息
            data7 = await self.get_series_data(start_day, end_day, province_id, "全省用电负荷预测信息")
            if data7:
                temp_list_7 = []
                for i in range(len(data7)):
                    temp_dict = {
                        "datetime": data7[i]["datetime"],
                        "value9": float(data7[i]["value1"]),
                    }
                    temp_list_7.append(temp_dict)
                temp_array.append(temp_list_7)

            # 实时出清电量
            data8 = await self.get_series_data(start_day, end_day, province_id, "实时出清电量")
            if data8:
                temp_list_8 = []
                for i in range(len(data8)):
                    temp_dict = {
                        "datetime": data8[i]["datetime"],
                        "value10": float(data8[i]["value1"]),
                    }
                    temp_list_8.append(temp_dict)
                temp_array.append(temp_list_8)

            # 实时出清价格
            # data9 = await self.get_series_data(start_day, end_day, province_id,  "现货出清电价信息")
            if data2:
                temp_list_2 = []
                for i in range(len(data2)):
                    temp_dict = {
                        "datetime": data2[i]["datetime"],
                        "value11": eval(data2[i]["value2"]),
                    }
                    temp_list_2.append(temp_dict)
                temp_array.append(temp_list_2)

            # 光伏实际值/风电实际值/新能源总加实际值
            data10 = await self.get_series_data(start_day, end_day, province_id, "新能源总实时出力")
            if data10:
                temp_list_10 = []
                for i in range(len(data10)):
                    temp_dict = {
                        "datetime": data10[i]["datetime"],
                        "value12": eval(data10[i]["value2"]),
                        "value13": float(data10[i]["value1"]),
                        "value14": eval(data10[i]["value3"]),
                    }
                    temp_list_10.append(temp_dict)
                temp_array.append(temp_list_10)

            # 实时非市场化机组出力曲线
            data11 = await self.get_series_data(start_day, end_day, province_id, "实时非市场化机组出力曲线")
            if data11:
                temp_list_11 = []
                for i in range(len(data11)):
                    temp_dict = {
                        "datetime": data11[i]["datetime"],
                        "value15": float(data11[i]["value1"]),
                    }
                    temp_list_11.append(temp_dict)
                temp_array.append(temp_list_11)

            # 新能源总实时出力
            # data12 = await self.get_series_data(start_day, end_day, province_id,  "新能源总实时出力")
            if data10:
                temp_list_10 = []
                for i in range(len(data10)):
                    temp_dict = {
                        "datetime": data10[i]["datetime"],
                        "value16": eval(data10[i]["value3"]),
                    }
                    temp_list_10.append(temp_dict)
                temp_array.append(temp_list_10)

            # 实时联络线计划（加总）
            data13 = await self.get_series_data(start_day, end_day, province_id, "实时联络线计划（加总）")
            if data13:
                temp_list_13 = []
                for i in range(len(data13)):
                    temp_dict = {
                        "datetime": data13[i]["datetime"],
                        "value17": float(data13[i]["value1"]),
                    }
                    temp_list_13.append(temp_dict)
                temp_array.append(temp_list_13)

            # 水电总实时出力
            data14 = await self.get_series_data(start_day, end_day, province_id, "水电总实时出力")
            if data14:
                temp_list_14 = []
                for i in range(len(data14)):
                    temp_dict = {
                        "datetime": data14[i]["datetime"],
                        "value18": float(data14[i]["value1"]),
                    }
                    temp_list_14.append(temp_dict)
                temp_array.append(temp_list_14)

            # 系统负荷实际值
            data15 = await self.get_series_data(start_day, end_day, province_id, "系统实时负荷频率备用情况")
            if data15:
                temp_list_15 = []
                for i in range(len(data15)):
                    temp_dict = {
                        "datetime": data15[i]["datetime"],
                        "value19": float(data15[i]["value1"]),
                    }
                    temp_list_15.append(temp_dict)
                temp_array.append(temp_list_15)

            temp_map_dict = {
                "value1": "日前出清电量",
                "value2": "日前出清电价",
                "value3": "预测光伏出力",
                "value4": "预测风电出力",
                "value5": "预测新能源总出力",
                "value6": "日前非市场化机组出力预测",
                "value7": "机组检修总容量",
                "value8": "日前联络线计划信息（加总）",
                "value9": "全省用电负荷预测信息",
                "value10": "实时出清电量",
                "value11": "实时出清电价",
                "value12": "光伏实际值",
                "value13": "风电实际值",
                "value14": "新能源总加实际值",
                "value15": "实时非市场化机组出力曲线",
                "value16": "新能源总实时出力",
                "value17": "实时联络线计划（加总）",
                "value18": "水电总实时出力",
                "value19": "系统负荷实际值",
            }

        result = merge_dicts(*temp_array)
        result = sorted(result, key=lambda x: x['datetime'])

        print(1245, result)

        user_session.close()
        data = {
            "value_maps": temp_map_dict,
            "detail": result
        }
        self.returnTypeSuc(data)


class SeriesDictData(BaseHandler):
    """时序字典数据"""""

    def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        data = (user_session.query(CDictSery).filter(CDictSery.province_id == province_id,
                                                     CDictSery.is_use == "1").all())
        temp_array = []
        if data:
            for d in data:
                if not d.parent_id:
                    temp_dict = {
                        "id": d.id,
                        "name": d.name,
                        "sub_items": []
                    }
                    temp_array.append(temp_dict)
                else:
                    temp_dict = {
                        "id": d.id,
                        "name": d.name,
                        "parent_id": d.parent_id
                    }
                    for i in temp_array:
                        if i["id"] == d.parent_id:
                            i["sub_items"].append(temp_dict)

        self.returnTypeSuc(temp_array)


class SeriesData(BaseHandler):
    """时序交易数据"""""

    @staticmethod
    async def get_series_data(start_day, end_day, series_id):
        data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                             RSeriesDataShanxi.day <= end_day,
                                                             RSeriesDataShanxi.series_id == series_id,
                                                             RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": float(d.value1),
                    "value2": eval(d.value2) if d.value2 else d.value2,
                    "value3": eval(d.value3) if d.value3 else d.value3,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        series_id_set = self.get_argument("series_id_set", default='[]')
        page = int(self.get_argument("page")) if self.get_argument("page", None) else None
        page_size = 33

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数series_id
        if not isinstance(eval(series_id_set), list):
            return self.customError("series_id_set参数格式错误")
        series_id_set = eval(series_id_set)

        if not series_id_set:
            data = (user_session.query(CDictSery).filter(CDictSery.province_id == province_id,
                                                         CDictSery.parent_id != None,
                                                         CDictSery.is_use == "1").all())
            for d in data:
                series_id_set.append(d.id)

        temp_array = []

        for series_id in series_id_set:
            data = await self.get_series_data(start_day, end_day, series_id)
            if data:
                temp_list = []
                if series_id == 3:  # 日前非市场化机组出力预测
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 4:  # 日前新能源负荷预测
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value2": data[i]["value1"],
                            "value3": data[i]["value2"],
                            "value4": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 5:  # 全省用电负荷预测信息
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value5": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 6:  # 日前出清电量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value6": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 7:  # 日前联络线计划信息（加总）
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value7": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 8:  # 机组检修总容量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value8": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 9:  # 实时非市场化机组出力曲线
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value9": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 10:  # 新能源总实时出力
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value10": data[i]["value1"],
                            "value11": data[i]["value2"],
                            "value12": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 11:  # 系统实时负荷频率备用情况
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value13": data[i]["value1"],
                            "value14": data[i]["value2"],
                            "value15": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 12:  # 实时出清电量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value16": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 13:  # 实时联络线计划（加总）
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value17": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 14:  # 水电总实时出力
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value18": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 15:  # 现货出清电价信息
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value19": data[i]["value1"],
                            "value20": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                temp_array.append(temp_list)

        temp_map_dict = {
            "value1": "非市场化机组出力预测(MW)",
            "value2": "预测风电出力(MW)",
            "value3": "预测光伏出力(MW)",
            "value4": "预测新能源总出力(MW)",
            "value5": "全省用电负荷预测信息 (电力值)",
            "value6": "现货日前出清电量(MWh)",
            "value7": "日前联络线计划信息（加总）(电力值)",
            "value8": "检修容量",
            "value9": "非市场化机组实际出力(MW)",
            "value10": "风电实际值(MW)",
            "value11": "光伏实际值(MW)",
            "value12": "新能源总加实际值(MW)",
            "value13": "系统负荷实际值(MW)",
            "value14": "频率实际值(MW)",
            "value15": "实际上旋备用",
            "value16": "现货实时出清电量(MWh)",
            "value17": "实时联络线计划（加总）电力值(MW)",
            "value18": "水电实际值(MW)",
            "value19": "日前价格",
            "value20": "实时价格",
        }

        result = merge_dicts(*temp_array)
        result = sorted(result, key=lambda x: x['datetime'])

        # 手动分页
        if page:
            total_pages = math.ceil(len(result) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(result) + 1

            data_ = {
                "total": len(result),
                "total_pages": total_pages,
                "page": page,
                "detail": result[start_index:end_index],
                "maps": temp_map_dict
            }
        else:
            data_ = {
                "total": len(result),
                "total_pages": 1,
                "page": page,
                "detail": result,
                "maps": temp_map_dict
            }

        user_session.close()
        self.returnTypeSuc(data_)


class ExportSeriesData(BaseHandler):
    """导出时序交易数据"""""

    @staticmethod
    async def get_series_data(start_day, end_day, series_id):
        data = (user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day >= start_day,
                                                             RSeriesDataShanxi.day <= end_day,
                                                             RSeriesDataShanxi.series_id == series_id,
                                                             RSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        for d in data:
            temp_dict = {
                "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                "day": d.day,
                "moment": d.moment,
                "value1": float(d.value1),
                "value2": eval(d.value2) if d.value2 else d.value2,
                "value3": eval(d.value3) if d.value3 else d.value3,
                "name": d.name
            }
            temp_list.append(temp_dict)

        # temp_list按照 datetime 字段分组
        # grouped_datas = defaultdict(list)
        # for i in temp_list:
        #     grouped_datas[i['datetime']].append(i)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        # date_type = self.get_argument("date_type", default="real")
        series_id_set = self.get_argument("series_id_set", default='[]')

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # # 校验参数date_type
        # if date_type not in MarketInfoDataTypes.__members__:
        #     return self.customError(f"参数date_type: {date_type}错误")
        #
        # # 校验参数sign
        # if sign not in MarketInfoSigns.__members__:
        #     return self.customError(f"参数sign:{sign}错误")

        # 校验参数series_id
        if not isinstance(eval(series_id_set), list):
            return self.customError("series_id_set参数格式错误")
        series_id_set = eval(series_id_set)

        temp_array = []

        for series_id in series_id_set:
            data = await self.get_series_data(start_day, end_day, series_id)
            if data:
                temp_list = []
                if series_id == 3:  # 日前非市场化机组出力预测
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 4:  # 日前新能源负荷预测
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value2": data[i]["value1"],
                            "value3": data[i]["value2"],
                            "value4": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 5:  # 全省用电负荷预测信息
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value5": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 6:  # 日前出清电量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value6": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 7:  # 日前联络线计划信息（加总）
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value7": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 8:  # 机组检修总容量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value8": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 9:  # 实时非市场化机组出力曲线
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value9": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 10:  # 新能源总实时出力
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value10": data[i]["value1"],
                            "value11": data[i]["value2"],
                            "value12": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 11:  # 系统实时负荷频率备用情况
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value13": data[i]["value1"],
                            "value14": data[i]["value2"],
                            "value15": data[i]["value3"],
                        }
                        temp_list.append(temp_dict)
                elif series_id == 12:  # 实时出清电量
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value16": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 13:  # 实时联络线计划（加总）
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value17": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 14:  # 水电总实时出力
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value18": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)
                elif series_id == 15:  # 现货出清电价信息
                    for i in range(len(data)):
                        temp_dict = {
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value19": data[i]["value1"],
                            "value20": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                temp_array.append(temp_list)

        temp_map_dict = {
            "value1": "非市场化机组出力预测(MW)",
            "value2": "预测风电出力(MW)",
            "value3": "预测光伏出力(MW)",
            "value4": "预测新能源总出力(MW)",
            "value5": "全省用电负荷预测信息 (电力值)",
            "value6": "现货日前出清电量(MWh)",
            "value7": "日前联络线计划信息（加总）(电力值)",
            "value8": "检修容量",
            "value9": "非市场化机组实际出力(MW)",
            "value10": "风电实际值(MW)",
            "value11": "光伏实际值(MW)",
            "value12": "新能源总加实际值(MW)",
            "value13": "系统负荷实际值(MW)",
            "value14": "频率实际值(MW)",
            "value15": "实际上旋备用",
            "value16": "现货实时出清电量(MWh)",
            "value17": "实时联络线计划（加总）电力值(MW)",
            "value18": "水电实际值(MW)",
            "value19": "日前价格",
            "value20": "实时价格",
        }

        result = merge_dicts_with_default(*temp_array) if len(temp_array) > 0 else []
        result = sorted(result, key=lambda x: x['datetime']) if len(temp_array) > 0 else []

        location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name

        # 上传并返回下载url
        workbook = openpyxl.Workbook()
        sheet1 = workbook.active

        BASE_DIR = Path(__file__).resolve().parent
        start_day_str = start_day.strftime("%Y-%m-%d")
        end_day_str = end_day.strftime("%Y-%m-%d")
        file_name = f"{start_day_str}~{end_day_str}{location}时序交易数据.xlsx"
        path = os.path.join(BASE_DIR, file_name)
        sheet1.title = '时序交易数据'

        table_titles = ["日期", "时间"]

        # 准备数据
        data_array = list()
        if result:
            for t in result:
                # 准备表头
                if result.index(t) == 0:
                    for k in t.keys():
                        if k in temp_map_dict.keys():
                            table_titles.append(temp_map_dict[k])

                li = []
                for k in t.keys():
                    if k != "datetime":
                        li.append(t[k])
                data_array.append(li)

        # sheet1 写入标头
        for col_num, header in enumerate(table_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet1[f'{col_letter}1'] = header

        # 写入工作表
        for row_num, row_data in enumerate(data_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}{row_num}'] = cell_value
        workbook.save(path)
        url = await upload_file(file_name, path)

        try:
            os.remove(path)
        except Exception as e:
            pass

        user_session.close()
        self.returnTypeSuc(url)


class NonSeriesDictData(BaseHandler):
    """非时序字典数据"""""

    def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        data = (user_session.query(CDictNonSery).filter(CDictNonSery.province_id == province_id,
                                                        CDictNonSery.is_use == "1").all())
        temp_array = []
        if data:
            for d in data:
                if not d.parent_id:
                    temp_dict = {
                        "id": d.id,
                        "name": d.name,
                    }
                    temp_array.append(temp_dict)

        self.returnTypeSuc(temp_array)


class NonSeriesData(BaseHandler):
    """非时序交易数据/导出非时序交易数据"""""

    @staticmethod
    async def get_non_series_data(start_day, end_day, non_series_id):   # 1、断面约束情况及影子价格
        data = (user_session.query(RNonSeriesDataShanxi).filter(RNonSeriesDataShanxi.day >= start_day,
                                                             RNonSeriesDataShanxi.day <= end_day,
                                                             RNonSeriesDataShanxi.non_series_id == non_series_id,
                                                             RNonSeriesDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": float(d.value1),
                    "value2": eval(d.value2) if d.value2 else d.value2,
                    "value3": eval(d.value3) if d.value3 else d.value3,
                    "value4": eval(d.value4) if d.value4 else d.value4,
                    "descr": d.descr,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRSectionsShadowDataShanxiData(start_day, end_day):     # 2、重要通道实际输电情况
        data = (user_session.query(RSectionsShadowDataShanxi).filter(RSectionsShadowDataShanxi.day >= start_day,
                                                             RSectionsShadowDataShanxi.day <= end_day,
                                                             RSectionsShadowDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealNodePriceShanxiData(start_day, end_day):  #　６、实时节点边际电价
        data = (user_session.query(RRealNodePriceShanxi).filter(RRealNodePriceShanxi.day >= start_day,
                                                             RRealNodePriceShanxi.day <= end_day,
                                                             RRealNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": float(d.ele_price),
                    "value2": float(d.block_price),
                    "node": d.node,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreNodePriceShanxiData(start_day, end_day):  # ７、日前节点边际电价
        data = (user_session.query(RPreNodePriceShanxi).filter(RPreNodePriceShanxi.day >= start_day,
                                                             RPreNodePriceShanxi.day <= end_day,
                                                             RPreNodePriceShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value1": float(d.ele_price),
                    "value2": float(d.block_price),
                    "node": d.node,
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealSectionsBlockDataShanxiData(start_day, end_day):      # 8、实时输电断面约束及阻塞
        data = (user_session.query(RRealSectionsBlockDataShanxi).filter(RRealSectionsBlockDataShanxi.day >= start_day,
                                                             RRealSectionsBlockDataShanxi.day <= end_day,
                                                             RRealSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreSectionsBlockDataShanxiData(start_day, end_day):      # 9、日前输电断面约束及阻塞
        data = (user_session.query(RPreSectionsBlockDataShanxi).filter(RPreSectionsBlockDataShanxi.day >= start_day,
                                                             RPreSectionsBlockDataShanxi.day <= end_day,
                                                             RPreSectionsBlockDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealCallWireDataShanxiData(start_day, end_day):      # 10、实时联络线计划（站点）
        data = (user_session.query(RRealCallWireDataShanxi).filter(RRealCallWireDataShanxi.day >= start_day,
                                                             RRealCallWireDataShanxi.day <= end_day,
                                                             RRealCallWireDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreCallWireDataShanxiData(start_day, end_day):      # 11、日前联络线计划信息（站点）
        data = (user_session.query(RPreCallWireDataShanxi).filter(RPreCallWireDataShanxi.day >= start_day,
                                                             RPreCallWireDataShanxi.day <= end_day,
                                                             RPreCallWireDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRRealUnitPowerDataShanxiData(start_day, end_day):      # 20、机组实时出力
        data = (user_session.query(RRealUnitPowerDataShanxi).filter(RRealUnitPowerDataShanxi.day >= start_day,
                                                             RRealUnitPowerDataShanxi.day <= end_day,
                                                             RRealUnitPowerDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "value": float(d.value),
                    "name": d.name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitOpenDataShanxiData(start_day, end_day):      # 21、日前必开机组
        data = (user_session.query(RPreUnitOpenDataShanxi).filter(RPreUnitOpenDataShanxi.day >= start_day,
                                                             RPreUnitOpenDataShanxi.day <= end_day,
                                                             RPreUnitOpenDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    @staticmethod
    async def getRPreUnitCloseDataShanxiData(start_day, end_day):      # 22、日前必停机组
        data = (user_session.query(RPreUnitCloseDataShanxi).filter(RPreUnitCloseDataShanxi.day >= start_day,
                                                             RPreUnitCloseDataShanxi.day <= end_day,
                                                             RPreUnitCloseDataShanxi.is_use == "1").all())
        temp_list = []
        if data:
            for d in data:
                temp_dict = {
                    "datetime": d.day.strftime("%Y-%m-%d") + " " + d.moment,
                    "day": d.day,
                    "moment": d.moment,
                    "mem_name": d.mem_name,
                    "unit_name": d.unit_name
                }
                temp_list.append(temp_dict)

        data = sorted(temp_list, key=lambda x: x['datetime']) if temp_list else []
        return data

    async def get_non_series_info(self, start_day, end_day, non_series_id):
        temp_list = []
        temp_map_dict = {}
        if non_series_id in [1, 2, 6, 7, 8, 9, 10, 11, 20, 21, 22]:
            if non_series_id == 1:  # 断面约束情况及影子价格
                data = await self.getRSectionsShadowDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "阻塞价格(元/MWh)"}

            elif non_series_id == 2:  # 重要通道实际输电情况
                data = await self.getRSectionsShadowDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "名称",
                                     "value": "潮流(MW)"}

            elif non_series_id == 6:  # 实时节点边际电价
                data = await self.getRRealNodePriceShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 7:  # 日前节点边际电价
                data = await self.getRPreNodePriceShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "node": data[i]['node'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value1"] + data[i]["value2"]
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     'node': "节点名称",
                                     "value1": "电能量价格(元/MWh)", "value2": "阻塞价格(元/MWh)",
                                     "value3": "节点电价(元/MWh)"}
            elif non_series_id == 8:  # 实时输电断面约束及阻塞
                data = await self.getRRealSectionsBlockDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 9:  # 日前输电断面约束及阻塞
                data = await self.getRPreSectionsBlockDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "断面名称",
                                     "value": "是否越限"}
            elif non_series_id == 10:  # 实时联络线计划（站点）
                data = await self.getRRealCallWireDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 11:  # 日前联络线计划信息（站点）
                data = await self.getRPreCallWireDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "通道类型",
                                     "value": "电力值(MW)"}
            elif non_series_id == 20:  # 机组实时出力
                data = await self.getRRealUnitPowerDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]["name"],
                            "value": data[i]["value"],
                            "descr": "停机/待机"
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'name': "机组名称",
                                     "value": "电力值(MW)", "descr": "机组状态"}
            elif non_series_id == 21:  # 日前必开机组
                data = await self.getRPreUnitOpenDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
            elif non_series_id == 22:  # 日前必停机组
                data = await self.getRPreUnitCloseDataShanxiData(start_day, end_day)
                if data:
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "mem_name": data[i]["mem_name"],
                            "unit_name": data[i]["unit_name"],
                        }
                        temp_list.append(temp_dict)
                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'mem_name': "市场成员名称",
                                     "unit_name": "机组名称"}
        else:
            data = await self.get_non_series_data(start_day, end_day, non_series_id)
            if data:
                if non_series_id == 3:  # 市场力分析指标
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value3"],
                            "value4": data[i]["value4"],
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "TOP4指标",
                                     "value2": "HHI指标", "value3": "MMR指标", "value4": "RSI指标"}

                elif non_series_id == 4:  # 实时电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'descr': "出清情况"}

                elif non_series_id == 5:  # 日前电能量市场出清概况
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'descr': "出清情况"}

                elif non_series_id == 12:  # 实时备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 13:  # 日前备用总量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "上旋最小值(MW)",
                                     "value2": "下旋最小值(MW)"}

                elif non_series_id == 14:  # 实时调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}
                elif non_series_id == 15:  # 日前调频容量里程价格
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频容量价格(元/MW)",
                                     "value2": "调频里程价格(元/MW)"}

                elif non_series_id == 16:  # 水电发电计划预测
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "电量(MWh)"}

                elif non_series_id == 17:  # 抽蓄电站蓄水水位
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"],
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'value1': "水位值", "descr": "描述"}

                elif non_series_id == 18:  # 调频辅助服务需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "调频需求容量(MW)"}

                elif non_series_id == 19:  # 输电通道容量
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "value1": data[i]["value1"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "moment": "时点", 'value1': "通道"}

                elif non_series_id == 23:  # 断面约束
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "value1": data[i]["value1"],
                            "value2": data[i]["value2"],
                            "value3": data[i]["value3"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "市场成员名称", "descr": "断面描述",
                                     "value1": "正向传输极限", 'value2': "反向传输极限", "value3": "断面名称"}

                elif non_series_id == 24:  # 日前正负备用需求
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "value1": data[i]["value1"],
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "市场成员名称", "descr": "类型",
                                     "value1": "备用负荷"}

                elif non_series_id == 25:  # 输变电设备检修计划信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "moment": data[i]['moment'],
                            "name": data[i]['name'],
                            "descr": data[i]['descr'],
                            "start_time": data[i]["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                            "end_time": data[i]["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", "name": "设备名称", "descr": "设备类型",
                                     "start_time": "设备检修开始时间", "end_time": "设备检修结束时间"}

                elif non_series_id == 26:  # 开机不满七天机组信息
                    for i in range(len(data)):
                        temp_dict = {
                            "id": i + 1,
                            "datetime": data[i]["datetime"],
                            "day": data[i]["day"].strftime("%Y-%m-%d"),
                            "descr": data[i]["descr"]
                        }
                        temp_list.append(temp_dict)

                    temp_map_dict = {"id": "序号", "day": "日期", 'descr': "出清情况"}

        result = sorted(temp_list, key=lambda x: x['datetime'])
        return result, temp_map_dict

    async def get(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        non_series_id = self.get_argument("non_series_id", default=None)
        page = int(self.get_argument("page")) if self.get_argument("page", None) else None
        page_size = 33

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数non_series_id
        if not non_series_id:
            return self.customError("non_series_id参数缺失")
        non_series_id = eval(non_series_id)

        result, temp_map_dict = await self.get_non_series_info(start_day, end_day, non_series_id)

        #分页
        # 手动分页
        if page:
            total_pages = math.ceil(len(result) / page_size)
            start_index = (page - 1) * page_size
            end_index = page * page_size if page < total_pages else len(result) + 1

            data_ = {
                "total": len(result),
                "total_pages": total_pages,
                "page": page,
                "detail": result[start_index:end_index],
                "maps": temp_map_dict
            }
        else:
            data_ = {
                "total": len(result),
                "total_pages": 1,
                "page": page,
                "detail": result,
                "maps": temp_map_dict
            }

        user_session.close()

        self.returnTypeSuc(data_)

    async def post(self):
        self.refreshSession()
        user = self.get_current_user()
        print(user)

        province_id = self.get_argument("province_id")
        start_day = self.get_argument("start_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_day = self.get_argument("end_day", default=datetime.datetime.now().strftime("%Y-%m-%d"))
        non_series_id = self.get_argument("non_series_id", default='[]')

        provinces = user_session.query(CProvince).all()
        provinces_id = [province.id for province in provinces if province.is_use == "1" and province.name != "中国"]

        # 校验参数province_id
        if not province_id or int(province_id) not in provinces_id:
            return self.customError("省份ID参数错误")

        province_id = int(province_id)

        # 校验参数start_day和end_day
        try:
            start_day = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            end_day = datetime.datetime.strptime(end_day, "%Y-%m-%d")
        except Exception as e:
            logging.error(e)
            return self.customError("日期参数错误")

        if start_day > datetime.datetime.now():
            return self.customError("开始日期不能大于当前日期")
        if end_day > datetime.datetime.now():
            return self.customError("结束日期不能大于当前日期")
        if start_day > end_day:
            return self.customError("开始日期不能大于结束日期")

        # 校验参数non_series_id
        if not non_series_id:
            return self.customError("non_series_id参数格式错误")
        non_series_id = eval(non_series_id)

        # 查询数据
        result, temp_map_dict = await self.get_non_series_info(start_day, end_day, non_series_id)

        location = user_session.query(CProvince).filter(CProvince.id == int(province_id)).first().name
        non_series_name = user_session.query(CDictNonSery).filter(CDictNonSery.id == non_series_id).first().name

        # 上传并返回下载url
        workbook = openpyxl.Workbook()
        sheet1 = workbook.active

        BASE_DIR = Path(__file__).resolve().parent
        start_day_str = start_day.strftime("%Y-%m-%d")
        end_day_str = end_day.strftime("%Y-%m-%d")
        file_name = f"{start_day_str}~{end_day_str}{location}非时序交易数据.xlsx"
        path = os.path.join(BASE_DIR, file_name)
        sheet1.title = non_series_name

        table_titles = []
        # 准备表头
        for k, v in temp_map_dict.items():
            table_titles.append(v)

        # 准备数据
        data_array = list()
        if result:
            for t in result:
                li = []
                for k in t.keys():
                    if k != "datetime":
                        li.append(t[k])
                data_array.append(li)

        # sheet1 写入标头
        for col_num, header in enumerate(table_titles, 1):
            col_letter = openpyxl.utils.get_column_letter(col_num)
            sheet1[f'{col_letter}1'] = header

        # 写入工作表
        for row_num, row_data in enumerate(data_array, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet1[f'{col_letter}{row_num}'] = cell_value
        workbook.save(path)
        url = await upload_file(file_name, path)

        try:
            os.remove(path)
        except Exception as e:
            pass

        user_session.close()
        self.returnTypeSuc(url)