SUCCESS = 1
ERROR = 0

# 字段校验失败
FIELD_ERROR = -1

# 整体错误
SUMMARY_CODE = -2

# 无数据
NO_DATA = -3

# 认证失败
AUTHENTICATION_FIELD = -4


class OrderUnitGroup:
    THIRD = 1  # 第三方
    COMPANY = 0  # 公司售后


class OrderAuthority:
    SUPER = 1
    DISPATCH = 2  # 派单
    RECEIVING = 3  # 接单
    READONLY = 4    # 只读
    EXAMINE = 5  # 审核


class OrderStatus:
    REJECT = -2  # 驳回、待预审
    STOP = -1  # 终止
    FIRST_EXAMINE = 0  # 预审
    FINISH = 1  # 完成
    PENDING_ORDERS = 2  # 待接单
    PENDING_EXECUTED = 3  # 待执行
    LAST_EXAMINE = 4  # 终审
    EXAMINE = 5     # 初审


class ConfLevType:
    TYPE = {
        'zh': {1: '大工业',
               2: '一般工商业',
               3: '一般工商业（单一制）',
               4: '大工业（两部制）',
               5: '单一制',
               6: '两部制',
               7: '一般工商业（两部制）',
               8: '两部制(大工业100kVA以上)',
               9: '两部制(非大工业100kVA以上)',
               10: '单一制(100kVA以下)',
               11: '大量工商业及其他用电（250kWh及以下/千伏安•月）',
               12: '大量工商业及其他用电（250kWh及以上/千伏安•月）',
               13: '高需求工商业及其他用电（400kWh及以下/千伏安•月）',
               14: '高需求工商业及其他用电（400kWh及以上/千伏安•月）',
               15: '充换电站工商业用户',
			   16: '电动汽车'},
        'en': {1: 'Large-scale industry',
               2: 'General industry and commerce',
               3: 'General industry and commerce (single system)',
               4: 'Large-scale industry (two-part system)',
               5: 'Unitary system',
               6: 'Two-part system',
               7: 'General industry and commerce (two-part system)',
               8: 'Two-part system (large industry above 100kVA)',
               9: 'Two-part system (not large industry above 100kVA)',
               10: 'Single system (below 100kVA)',
               11: 'Large amount of industrial, commercial and other electricity consumption (250kWh and below/kVA•month)',
               12: 'Large amount of industrial, commercial and other electricity consumption (250kWh and above/kVA•month)',
               13: 'High-demand industrial, commercial, and other electricity consumption (400kWh and below/kVA•month)',
               14: 'High-demand industrial, commercial, and other electricity consumption (400kWh and above/kVA•month)',
               15: 'Commercial and industrial users of charging and replacement stations',
			   16: 'electric_vehicle'},

    }

    LEVEL = {
        'zh': {
            1: '1千伏',
            2: '10千伏',
            3: '35千伏',
            4: '110千伏',
            5: '220千伏及以上',
            6: '35-110千伏（不含）',
            7: '110-220千伏（不含）',
            8: '35千伏及以上',
            9: '35-110千伏以下',
            10: '110-220千伏以下',
            11: '110 (66)千伏',
            12: '20千伏',
            13: '66千伏',
            14: '220千伏',
            15: '110千伏及以上',
            16: '20-35千伏以下',
            17: '110-330千伏',
            18: '330千伏及以上',
            19: '35-110千伏',
            20: '10 (20)千伏',
            21: '35千伏以下',
            22: '10千伏',
            23: '1-10(20)千伏',
            24: '220(330)千伏',
            25: '100千伏安及以下和公变接入用电',
            26: '10千伏高供低计（380V/220V计量）',
            27: '10千伏高供低计',
        },
        'en': {
            1: '1 kV',
            2: '10 kV',
            3: '35 kV',
            4: '110 kV',
            5: '220 kV and above',
            6: '35-110 kV (excluding)',
            7: '110-220 kV (excluding)',
            8: '35 kV and above',
            9: 'Below 35-110 kV',
            10: 'Below 110-220 kV',
            11: '110 (66) kV',
            12: '20 kV',
            13: '66 kV',
            14: '220 kV',
            15: '110 kV and above',
            16: 'Below 20-35 kV',
            17: '110-330 kV',
            18: '330 kV and above',
            19: '35-110 kV',
            20: '10 (20) kV',
            21: 'Below 35 kV',
            22: '10 kV',
            23: '1-10 (20) kV',
            24: '220 (330) kV',
            25: '100 kVA and below and public transformer access electricity',
            26: '10 kV high supply low metering (380V/220V metering)',
            27: '10 kV high supply low metering'
}


    }


LoadAnalysisStatusTypes = {
    "充电": {
        "zh": '充电',
        "en": 'Charge'
    },
    "放电": {
        "zh": '放电',
        "en": 'Discharge'
    },
    "静置": {
        "zh": '静置',
        "en": 'Standby'
    },
    "充电/放电": {
        "zh": '充电/放电',
        "en": 'Charge/Discharge'
    },
    "--": {
        "zh": '--',
        "en": '--'
    }
}

CommonStatusMappings = {
    "是": {
        "zh": '是',
        "en": 'Yes'
    },
    "否": {
        "zh": '否',
        "en": 'No'
    }
}


AnalysisLoadCompMap = {
	"温州市味美思食品有限公司": {
		"zh": "温州市味美思食品有限公司",
		"en": "Wenzhou Weimeisi Food Co., Ltd"
	},
	"嘉兴明锐科技股份有限公司": {
		"zh": "嘉兴明锐科技股份有限公司",
		"en": "Jiaxing Mingrui Technology Co., Ltd"
	},
	"乐清市金马电镀有限公司": {
		"zh": "乐清市金马电镀有限公司",
		"en": "Yueqing Jinma Electroplating Co., Ltd"
	},
	"浙江豪邦光学科技有限公司": {
		"zh": "浙江豪邦光学科技有限公司",
		"en": "Zhejiang Haobang Optical Technology Co., Ltd"
	},
	"宁波计氏金属新材料有限公司": {
		"zh": "宁波计氏金属新材料有限公司",
		"en": "Ningbo Jishi Metal New Materials Co., Ltd"
	},
	"乐清市宏枫电子有限公司": {
		"zh": "乐清市宏枫电子有限公司",
		"en": "Yueqing Hongfeng Electronics Co., Ltd"
	},
	"浙江互生纺织科技有限公司": {
		"zh": "浙江互生纺织科技有限公司",
		"en": "Zhejiang Husheng Textile Technology Co., Ltd"
	},
	"浙江金澳兰机床有限公司": {
		"zh": "浙江金澳兰机床有限公司",
		"en": "Zhejiang Jinaolan Machine Tool Co., Ltd"
	},
	"浙江荣亚工贸有限公司": {
		"zh": "浙江荣亚工贸有限公司",
		"en": "Zhejiang Rongya Industry and Trade Co., Ltd"
	},
	"瑞安市汇昌机械有限公司": {
		"zh": "瑞安市汇昌机械有限公司",
		"en": "Rui an Huichang Machinery Co., Ltd"
	},
	"温州市宝瑞新材料有限公司": {
		"zh": "温州市宝瑞新材料有限公司",
		"en": "Wenzhou Baorui New Materials Co., Ltd"
	},
	"瑞安市盛昌热处理有限公司": {
		"zh": "瑞安市盛昌热处理有限公司",
		"en": "Rui an Shengchang Heat Treatment Co., Ltd"
	},
	"浙江康利德科技股份有限公司": {
		"zh": "浙江康利德科技股份有限公司",
		"en": "Zhejiang Kanglide Technology Co., Ltd"
	},
	"瑞安市亿大锻造厂": {
		"zh": "瑞安市亿大锻造厂",
		"en": "Rui an Yida Forging Factory"
	},
	"温州市展宏畜牧有限公司": {
		"zh": "温州市展宏畜牧有限公司",
		"en": "Wenzhou Zhanhong Livestock Co., Ltd"
	},
	"浙江鑫泰阀门科技有限公司": {
		"zh": "浙江鑫泰阀门科技有限公司",
		"en": "Zhejiang Xintai Valve Technology Co., Ltd"
	},
	"瑞安市宏志钢业有限公司": {
		"zh": "瑞安市宏志钢业有限公司",
		"en": "Rui an Hongzhi Steel Industry Co., Ltd"
	},
	"温州恒荣五金有限公司": {
		"zh": "温州恒荣五金有限公司",
		"en": "Wenzhou Hengrong Hardware Co., Ltd"
	},
	"温州市逸嘉鞋材有限公司": {
		"zh": "温州市逸嘉鞋材有限公司",
		"en": "Wenzhou Yijia Shoe Material Co., Ltd"
	},
	"嵊州时光服装有限公司": {
		"zh": "嵊州时光服装有限公司",
		"en": "Shengzhou Time Clothing Co., Ltd"
	},
	"浙江安胜科技股份有限公司": {
		"zh": "浙江安胜科技股份有限公司",
		"en": "Zhejiang Ansheng Technology Co., Ltd"
	},
	"浙江隆生数码纺织科技有限公司": {
		"zh": "浙江隆生数码纺织科技有限公司",
		"en": "Zhejiang Longsheng Digital Textile Technology Co., Ltd"
	},
	"浙江正邦汽车模具有限公司": {
		"zh": "浙江正邦汽车模具有限公司",
		"en": "Zhejiang Zhengbang Automotive Mold Co., Ltd"
	},
	"温州市康信皮业有限公司": {
		"zh": "温州市康信皮业有限公司",
		"en": "Wenzhou Kangxin Leather Industry Co., Ltd"
	},
	"温州创大鞋业有限公司": {
		"zh": "温州创大鞋业有限公司",
		"en": "Wenzhou Chuangda Footwear Co., Ltd"
	},
	"温州市瓯海金瓯光学眼镜有限公司": {
		"zh": "温州市瓯海金瓯光学眼镜有限公司",
		"en": "Wenzhou Ouhai Jinou Optical Glasses Co., Ltd"
	},
	"浙江航佳工贸有限公司": {
		"zh": "浙江航佳工贸有限公司",
		"en": "Zhejiang Hangjia Industry and Trade Co., Ltd"
	},
	"新昌县新翔宇轴承有限公司": {
		"zh": "新昌县新翔宇轴承有限公司",
		"en": "Xinxiangyu Bearing Co., Ltd., Xinchang County"
	},
	"安吉毅薪五金工具有限公司": {
		"zh": "安吉毅薪五金工具有限公司",
		"en": "Anji Yixin Hardware Tools Co., Ltd"
	},
	"浙江中京鞋业有限公司": {
		"zh": "浙江中京鞋业有限公司",
		"en": "Zhejiang Zhongjing Footwear Co., Ltd"
	},
	"浙江中邦服饰有限公司": {
		"zh": "浙江中邦服饰有限公司",
		"en": "Zhejiang Zhongbang Clothing Co., Ltd"
	},
	"温州市乐伊汽车配件有限公司": {
		"zh": "温州市乐伊汽车配件有限公司",
		"en": "Wenzhou Leyi Automotive Parts Co., Ltd"
	},
	"温州冠峰锻造有限公司": {
		"zh": "温州冠峰锻造有限公司",
		"en": "Wenzhou Guanfeng Forging Co., Ltd"
	},
	"温州明净科技有限公司": {
		"zh": "温州明净科技有限公司",
		"en": "Wenzhou Mingjing Technology Co., Ltd"
	},
	"浙江雄泰家居用品股份有限公司": {
		"zh": "浙江雄泰家居用品股份有限公司",
		"en": "Zhejiang Xiongtai Household Products Co., Ltd"
	},
	"浙江鼎派实业有限公司": {
		"zh": "浙江鼎派实业有限公司",
		"en": "Zhejiang Dingpai Industrial Co., Ltd"
	},
	"浙江秀洲科技创业发展有限公司": {
		"zh": "浙江秀洲科技创业发展有限公司",
		"en": "Zhejiang Xiuzhou Technology Entrepreneurship Development Co., Ltd"
	},
	"温州市骏贸易有限公司": {
		"zh": "温州市骏贸易有限公司",
		"en": "Wenzhou Jun Trading Co., Ltd"
	}
}
