#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \HY_empowerd:\em_pjt_rh\RHBESS_empower\Application\Models\message.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-27 14:50:58



from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class Message(user_Base):
    u'消息表'
    __tablename__ = "t_message"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    descr = Column(VARCHAR(1280), nullable=False, comment=u"消息体")
    type = Column(VARCHAR(256), nullable=True, comment=u"消息类型，order工单/alarm告警/msg普通消息/template模板审批")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    source = Column(Integer, nullable=True, comment=u"消息来源/如类型为order关联工单步骤id/alarm关联告警/template模板/msg普通消息无关联")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()