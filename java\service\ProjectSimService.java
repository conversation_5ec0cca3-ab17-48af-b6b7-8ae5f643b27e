package com.robestec.dailyproduce.project.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimCreateDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimOperationDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimQueryDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimUpdateDTO;
import com.robestec.dailyproduce.project.model.ProjectSim;
import com.robestec.dailyproduce.project.vo.ProjectSimVO;

import java.util.List;

/**
 * 5G专用卡信息服务接口
 */
public interface ProjectSimService extends ISuperService<ProjectSim> {

    /**
     * 分页
     * @param queryDTO
     * @return
     */
    PageResult<ProjectSimVO> querySims(ProjectSimQueryDTO queryDTO);

    /**
     * 5G专用卡信息创建
     * @param createDTO
     * @return
     */
    Long createSim(ProjectSimCreateDTO createDTO);

    /**
     * 修改5G专用卡信息
     * @param updateDTO
     */
    void updateSim(ProjectSimUpdateDTO updateDTO);

    /**
     * 删除5G专用卡信息
     * @param id
     */
    void deleteSim(Long id);

    /**
     * 获取5G专用卡信息
     * @param id
     * @return
     */
    ProjectSimVO getSim(Long id);

    /**
     * 批量添加5G专用卡信息
     * @param projectSimCreateDTOList
     */
    void createSimList(List<ProjectSimCreateDTO> projectSimCreateDTOList);

    /**
     * 批量添加o更新5G专用卡信息
     * @param projectSimUpdateDTOList
     */
    void saveOrUpdateSimList(List<ProjectSimUpdateDTO> projectSimUpdateDTOList);

    /**
     * 根据项目ID删除5G专用卡信息
     * @param projectId
     */
    void deleteSimListByProjectId(Long projectId);

    /**
     * 根据项目ID获取5G专用卡信息
     * @param projectId
     * @return
     */
    List<ProjectSimVO> getProjectSimByProjectId(Long projectId);

    /**
     * 操作5G专用卡信息
     * @param id
     * @param projectSimOperationDTOS
     */
    void operationProjectSim(Long id, List<ProjectSimOperationDTO> projectSimOperationDTOS);
}