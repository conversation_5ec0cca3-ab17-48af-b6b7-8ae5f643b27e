#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-09-27 09:45:14
#@FilePath     : \RHBESS_Service\Application\Models\User\station_relation.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-24 10:16:04


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Tools.Utils.time_utils import timeUtils

class StationR(user_Base):
    u'电站分布'
    __tablename__ = "t_station_relation"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    station_name = Column(VARCHAR(10), nullable=False, comment=u"station名称")
    electric_power = Column(Integer, nullable=True,comment=u"电站电功率")
    running_state = Column(CHAR(1), nullable=True,comment=u"电站运行状态，1投运，2在建，3维修")
    province = Column(VARCHAR(10), nullable=True,comment=u"电站所在省份")
    energy_storage = Column(CHAR(1), nullable=True,comment=u"储能形式，1火储能，2工商业储能")
    active_power_name = Column(Text, nullable=True,comment=u"有功功率key")
    chag_capacity_name = Column(Text, nullable=True,comment=u"充电容量key")
    disg_capacity_name = Column(Text, nullable=True,comment=u"放电容量key")
    battery_cluster = Column(Integer, nullable=True, comment=u"电池簇个数")
    DisgCapy = Column(VARCHAR(256), nullable=True, comment=u"当日放电量")
    ChagCapy = Column(VARCHAR(256), nullable=True, comment=u"当日充电量")
    SOC = Column(VARCHAR(2024), nullable=True, comment=u"SOC key")
    monitor = Column(VARCHAR(1024), nullable=True, comment=u"监控页选项")
    en_monitor = Column(VARCHAR(2048), nullable=True, comment=u"监控页选项-英文")
    en_province = Column(VARCHAR(64), nullable=True,comment=u"电站所在省份-英文")



    @classmethod
    # def init(cls):
    #     user_Base.metadata.create_all()
    #     now = timeUtils.getNewTimeStr()
    #     user_session.merge(Station(id=1,name='halun',descr='哈伦6MW/3MWh储能电站',op_ts=now,index=3));
    #     user_session.merge(Station(id=2,name='taicang',descr='太仓9MW/4.5MWh储能电站',op_ts=now,index=2));
    #     user_session.merge(Station(id=3,name='binhai',descr='滨海18MW/9MWh储能电站',op_ts=now,index=1));
    #     user_session.merge(Station(id=4,name='ygzhen',descr='永臻5MW/18MWh储能电站',op_ts=now,index=4));
    #
    #     user_session.commit()
    #     user_session.close()
        
    def __repr__(self):
        bean = "{'id':%s,'station_name':'%s','electric_power':%s,'running_state':'%s','province':'%s','energy_storage':'%s','active_power_name':'%s','chag_capacity_name':'%s','disg_capacity_name':'%s','battery_cluster':'%s','DisgCapy':'%s','ChagCapy':'%s','SOC':'%s','monitor':'%s'," \
               "'en_monitor':'%s', 'en_province':'%s'}" % (
            self.id,self.station_name,self.electric_power,self.running_state,self.province,self.energy_storage,self.active_power_name,self.chag_capacity_name,self.disg_capacity_name,self.battery_cluster,self.DisgCapy,self.ChagCapy,self.SOC,self.monitor,self.en_monitor,self.en_province)
        return bean.replace("None",'')
        
    def deleteStationR(self,id):
        try:
            user_session.query(StationR).filter(StationR.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False