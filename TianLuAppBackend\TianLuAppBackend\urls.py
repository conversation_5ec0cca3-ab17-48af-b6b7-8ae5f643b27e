#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-11-06 18:27:32
#@FilePath     : \RHBESS_Serviced:\emot_pjt_rh\TianLuAppBackend\TianLuAppBackend\urls.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-07 16:43:32


from django.conf.urls import url
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
# from apscheduler_tasks import temp_tasks
# from apscheduler_tasks import run

# from rest_framework.documentation import include_docs_urls
# from drf_yasg.views import get_schema_view
# from drf_yasg import openapi

# schema_view = get_schema_view(
#     openapi.Info(
#         title="接口文档",  # 必传
#         default_version="v1",  # 必传
#         description="天禄小程序接口文档",
#         terms_of_service="",
# contact=openapi.Contact(email="<EMAIL>"),
# license=openapi.License(name="BSD LICENSE")
# ),
# public=True,
# permission_classes=(permissions.)  # 权限类
# )
urlpatterns = [
    path('admin/', admin.site.urls),  # 用户模块,
    path('user/', include('apis.user.urls')),  # 用户模块,
    path('stored_energy/', include('apis.stored_energy.urls')),  # 收益模块,
    path('monitor/', include('apis.monitor.urls')),  # 监控模块,
    path('statistics/', include('apis.statistics_apis.urls')),  # 统计模块,
    path('chat/', include('apis.wb.urls')),
    path('web/', include('apis.web.urls')),  # 天禄 web
    path('projects_manage/', include('apis.project_manage.urls')),  # 天禄项目信息管理
    path('workorder/', include('apis.work_order.urls')),
    # path('docs/', include_docs_urls(title="天禄小程序接口文档")), # 接口文档
    # url(r"^docs/", include_docs_urls(title="My API title")),
    # path("swagger/", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger"),
    # path("redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),

    # 小程序v2.0
    path('app2/overview/', include('apis.app2.overview.urls')),  # app2.0-概览页模块,
    path('app2/monitor/', include('apis.app2.monitor2.urls')),  # app2.0-监控模块
    path('app2/my/', include('apis.app2.my.urls')),  # app2.0-我的模块
    path('app2/workbench/', include('apis.app2.workbench.urls')),  # app2.0-工作台模块

    # web2.0
    path('web2/project_dashboard/', include('apis.web2.project_dashboard.urls')),    # 地图大屏、项目集控
    path('web2/projectfirst/', include('apis.web2.projectfirst.urls')),  # 项目首页
    path('web2/projectproduct/', include('apis.web2.projectproduct.urls')),  # 运行数据
    path('web2/project_account/', include('apis.web2.project_account.urls')),  # 结算管理
    path('web2/message/', include('apis.web2.message.urls')),  # 消息中心
    path('web2/analysis/', include('apis.web2.running_analysis.urls')),  # 运行分析
    path('web2/battery_analysis/', include('apis.web2.battery_analysis.urls')),    # 电池电压分析
    path('web2/analysis_load/', include('apis.web2.analysis_load.urls')),  # 负荷分析

    # 负荷预测对接算法接口
    path('Algorithm/', include('apis.algorithm.urls'))

]


urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
