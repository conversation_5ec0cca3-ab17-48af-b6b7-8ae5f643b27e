#!/usr/bin/env python
# coding=utf-8
#@Information:用户侧现货交易价格查询
#<AUTHOR> WYJ
#@Date         : 2022-11-09 09:00:56
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WorkOrder\workOrderHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-12-06 10:58:56

import os,uuid
import traceback

import tornado.web
import base64

from Application.Models.SideForecase.side_forecase_dict_policy_infos import ForecaseDicPolicy
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.SideForecase.side_forecase_area import ForecaseArea
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_price import ForecasePrice
from Application.Models.SideForecase.side_forecase_customer import ForecaseCustomer
from Application.Models.SideForecase.side_forecase_proele import ForecaseProele
from Application.Models.SideForecase.side_forecase_group import ForecaseGroup
from Application.Models.SideForecase.side_forecase_role import ForecaseRole
from Application.Models.SideForecase.side_forecase_user import ForecaseUser
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Application.Models.SideForecase.side_forecase_authority import ForecaseAuthority
from Application.Models.SideForecase.side_forecase_organization import ForecaseOrganization
from Application.Models.SideForecase.side_forecase_rank import Rank
from Tools.DB.redis_con import r
import math
import logging,json
from sqlalchemy import func,or_
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import *
from Application.EqAccount.encryption.jwt_encryption import create_token
ele_desc_pro=[38,39,40,41,42,30,10,29]
class SideForecaseUserHandleIntetface(BaseHandler):

    #@tornado.web.authenticated
    def get(self, kt):
        lang = self.request.headers.get("lang", 'zh')
        try:
            if kt == 'GetAreas':  # 查询区域
                data = []
                pages = user_session.query(ForecaseArea).filter(ForecaseArea.is_use==1).order_by(ForecaseArea.index.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTypeSuc(data)
            elif kt == 'GetVols':  # 查询电压等级
                province_id = self.get_argument('province_id',None) # 省份id
                ele_id = self.get_argument('ele_id',None) # 用电分类id
                if not province_id or not ele_id:
                    return self.customError("参数不完成")
                province_id = int(province_id)
                ele_id = int(ele_id)
                if DEBUG:
                    logging.info('province_id:%s,ele_id:%s'%(province_id,ele_id))
                data = []
                page = user_session.query(ForecaseProele).filter(ForecaseProele.is_use==1,ForecaseProele.province_id==province_id,ForecaseProele.ele_id==ele_id).first()
                if not page:  # 无数据
                    return self.returnTypeSuc(data)
                if province_id in ele_desc_pro:  # 保证第一个电压等级是自定义的
                    pagess = user_session.query(ForecaseVol).filter(ForecaseVol.is_use==1,ForecaseVol.id.in_(page.vols.split(','))).order_by(ForecaseVol.id.desc()).all()
                else:
                    pagess = user_session.query(ForecaseVol).filter(ForecaseVol.is_use==1,ForecaseVol.id.in_(page.vols.split(','))).all()
                for page in pagess:
                    if province_id == 2 and ele_id==4 and page.id == 2:  # 天津大工业两部制1-10千伏
                        data.insert(0,eval(str(page)))
                    elif province_id == 36 and ele_id==1 and page.id == 23:  # 陕西（陕西电网）大工业1-10（20）千伏
                        data.insert(0,eval(str(page)))
                    elif province_id == 31 and ele_id==1 and page.id == 2:  # 云南大工业1-10千伏
                        data.insert(0,eval(str(page)))
                    else:
                        data.append(eval(str(page)))

                if lang == 'en':
                    data = [{'id': i['id'], 'name': i['en_name'], 'is_use': 1} for i in data]

                return self.returnTypeSuc(data)
          
            elif kt == 'GetEles':  # 查询用电类型
                logging.info('lang:%s' % (lang))
                data = []
                province_id = self.get_argument('province_id',None) # 省份id
                if not province_id:
                    return self.customError("参数不完成")
                if DEBUG:
                    logging.info('province_id:%s'%(province_id))
                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                sql = "select id,name,en_name,is_use from t_side_forecase_ele where find_in_set(%s,provinces) and is_use=1 order by index_ asc"%(province_id)
                cursor.execute(sql)
                result = cursor.fetchall()

                for nam in result:
                    if lang == 'zh':
                        data.append({'id':nam[0],'name':nam[1],'is_use':int(nam[3])})
                    else:
                        data.append({'id': nam[0], 'name': nam[2], 'is_use': int(nam[3])})
              
                conn.close()
                return self.returnTypeSuc(data)
            elif kt == 'GetProvinces':  # 查询省份
                area_id = self.get_argument('area_id',None)
                if DEBUG:
                    logging.info('area_id:%s'%area_id)
                data,filte = [],[ForecaseProvince.is_use==1]
                if area_id:
                    filte.append(ForecaseProvince.area_id==area_id)
                
                pages = user_session.query(ForecaseProvince).filter(*filte).order_by(ForecaseProvince.index.asc()).all()
                for page in pages:
                    logging.info(page)
                    data.append(eval(str(page)))
                if lang == 'en':
                    data = [{'id': i['id'], 'name': i['en_name'], 'power_grid':'State Grid', 'is_use': 1} for i in data]

                return self.returnTypeSuc(data)
            elif kt == 'GetPrices':  # 查询价格  部制度暂时按默认两部制
                province_id = self.get_argument('province_id',None) # 省份id
                ele_id = self.get_argument('ele_id',None) # 用电分类id
                vol_id = self.get_argument('vol_id',None) # 电压等级id
                year_month = self.get_argument('year_month',None) # 时间，格式必须YYYY-mm
                month_flag = int(self.get_argument('month_flag',0)) # 时间标识0，传year_month年月，不传默认当前月，为1是year_month为list月集合
                if DEBUG:
                    logging.info('province_id:%s,ele_id:%s,vol_id:%s,year_month:%s,month_flag:%s'%(province_id,ele_id,vol_id,year_month,month_flag))
                data,filte = [],[ForecasePrice.is_use==1,ForecasePrice.part_id==1]
                if province_id:
                    filte.append(ForecasePrice.province_id==province_id)
                if ele_id:
                    filte.append(ForecasePrice.ele_id==ele_id)
                if vol_id:
                    filte.append(ForecasePrice.vol_id==vol_id)
                if month_flag == 0 and year_month:
                    filte.append(ForecasePrice.year_month==year_month[:7])
                if month_flag == 1:
                    months = eval(year_month)  # 月份集合
                    dd = timeUtils.getNewTimeStr()[:4]
                    monthss = dd+'-'+pd.Series(months)
                    filte.append(ForecasePrice.year_month.in_(monthss))

                total = user_session.query(func.count(ForecasePrice.id)).filter(*filte).scalar()
                pages = user_session.query(ForecasePrice).filter(*filte).order_by(ForecasePrice.year_month.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTotalSuccess(data,total)
            elif kt=='GetCustomer':
                # name = self.get_argument('name',None) # 姓名
                # company = self.get_argument('company',None) # 公司名称
                # address = self.get_argument('address',None) # 地址
                startTime = self.get_argument('startTime',None) # 开始时间
                endTime = self.get_argument('endTime',None) # 截止时间
                if DEBUG:
                    logging.info("endTime:%s,startTime:%s"%(endTime,startTime))
                data,filte = [],[]
                if startTime:
                    filte.append(ForecaseCustomer.op_ts>=startTime)
                if endTime:
                    filte.append(ForecaseCustomer.op_ts<=endTime)
                total = user_session.query(func.count(ForecaseCustomer.id)).filter(*filte).scalar()
                pages = user_session.query(ForecaseCustomer).filter(*filte).order_by(ForecaseCustomer.op_ts.asc()).all()
                for page in pages:
                    data.append(eval(str(page)))
                return self.returnTotalSuccess(data,total)
            elif kt=='GetProvinces_new':
                data = {'浙江省': {'id': 12, 'type': {
                    3: {'name': '单一制大工业用电', 'power': {23: '1-10 (20)千伏', 8: '35千伏及以上', 1: '不满1千伏'}},
                    4: {'name': '两部制大工业用电',
                        'power': {23: '1-10 (20)千伏', 4: '110千伏', 5: '220千伏及以上', 3: '35千伏'}},
                    7: {'name': '两部制一般工商业用电',
                        'power': {23: '1-10 (20)千伏', 4: '110千伏', 5: '220千伏及以上', 3: '35千伏'}}},
                                'days': [300]}, '江苏省': {'id': 11, 'type': {
                    5: {'name': '单一制', 'power': {2: '1-10 (20)千伏', 16: '35千伏', 1: '不满1千伏'}},
                    6: {'name': '两部制', 'power': {2: '1-10 (20)千伏', 4: '110千伏', 5: '220千伏及以上', 9: '35千伏'}}},
                                                        'days': [310]},
                        '上海市': {'id': 10, 'type': {
                            4: {'name': '大工业用电两部制', 'power': {22: '10千伏', 4: '110千伏及以上', 3: '35千伏', 1: '不满1千伏'}},
                            3: {'name': '一般工商业用电单一制', 'power': {22: '10千伏', 3: '35千伏', 1: '不满1千伏'}},
                            7: {'name': '一般工商业用电两部制',
                                'power': {22: '10千伏', 4: '110千伏及以上', 3: '35千伏', 1: '不满1千伏'}}}, 'days': [310]},
                        '广东省恵州市': {'id': 39,
                                   'type': {5: {'name': '单一制', 'power': {20: '1-10 (20)千伏', 8: '35-110千伏', 1: '不满1千伏'}},
                                            6: {'name': '两部制',
                                                'power': {20: '1-10 (20)千伏', 5: '220千伏及以上', 19: '35-110千伏'}}},
                                   'days': [310]}, '广东省江门市': {'id': 40, 'type': {
                        5: {'name': '单一制', 'power': {20: '1-10 (20)千伏', 8: '35-110千伏', 1: '不满1千伏'}},
                        6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 5: '220千伏及以上', 19: '35-110千伏'}}},
                                                              'days': [310]},
                        '广东省珠三角五市': {'id': 38, 'type': {
                            5: {'name': '单一制', 'power': {20: '1-10 (20)千伏', 8: '35-110千伏', 1: '不满1千伏'}},
                            6: {'name': '两部制',
                                'power': {20: '1-10 (20)千伏', 19: '220千伏及以上', 5: '35-110千伏'}}},
                                     'days': [310]},
                        '深圳市': {'id': 44, 'type': {12: {'name': '大量工商业及其他用电（250kWh及以上/千伏安•月）',
                                                        'power': {27: '10千伏高供低计',
                                                                  26: '10千伏高供低计（380V/220V计量）', 4: '110千伏',
                                                                  5: '220千伏及以上'}},
                                                   11: {'name': '大量工商业及其他用电（250kWh及以下/千伏安•月）',
                                                        'power': {27: '10千伏高供低计',
                                                                  26: '10千伏高供低计（380V/220V计量）', 4: '110千伏',
                                                                  5: '220千伏及以上'}},
                                                   14: {'name': '高需求工商业及其他用电（400kWh及以上/千伏安•月）',
                                                        'power': {26: '10千伏高供低计',
                                                                  27: '10千伏高供低计（380V/220V计量）', 4: '110千伏',
                                                                  5: '220千伏及以上'}},
                                                   13: {'name': '高需求工商业及其他用电（400kWh及以下/千伏安•月）',
                                                        'power': {26: '10千伏高供低计',
                                                                  27: '10千伏高供低计（380V/220V计量）', 4: '110千伏',
                                                                  5: '220千伏及以上'}},
                                                   2: {'name': '一般工商业及其他用电',
                                                       'power': {25: '100千伏安及以下和公变接入用电'}}}, 'days': [310]},
                        '海南省': {'id': 29, 'type': {
                            8: {'name': '100千伏安及以上(两部制)大工业',
                                'power': {4: '110千伏', 5: '220千伏及以上', 3: '35千伏', 21: '35千伏以下'}},
                            10: {'name': '100千伏安以下(单一制,含行政事业单位办公场所用电)', 'power': {2: '1-10千伏', 1: '不满1千伏'}}},
                                'days': [320]},
                        '湖南省': {'id': 18, 'type': {
                            5: {'name': '单一制', 'power': {2: '1-10千伏', 15: '110千伏及以上', 3: '35千伏', 1: '不满1千伏'}},
                            6: {'name': '两部制', 'power': {4: '110千伏', 2: '1-10千伏', 5: '220千伏及以上', 3: '35千伏'}}},
                                'days': [320]}, '河南省': {'id': 16, 'type': {
                        5: {'name': '单一制', 'power': {2: '1-10千伏', 15: '110千伏及以上', 9: '35-110千伏以下', 1: '不满1千伏'}},
                        6: {'name': '两部制', 'power': {4: '110千伏', 2: '1-10千伏', 5: '220千伏及以上', 9: '35-110千伏以下'}}},
                                                        'days': [320]},
                        '重庆市': {'id': 19,
                                'type': {5: {'name': '单一制', 'power': {4: '110千伏', 2: '1-10千伏', 3: '35千伏', 1: '不满1千伏'}},
                                         6: {'name': '两部制',
                                             'power': {4: '110千伏', 2: '1-10千伏', 5: '220千伏及以上', 3: '35千伏'}}},
                                'days': [320]}, '湖北省': {'id': 17,
                                                        'type': {5: {'name': '单一制',
                                                                     'power': {2: '1-10千伏', 3: '35千伏', 1: '不满1千伏'}},
                                                                 6: {'name': '两部制',
                                                                     'power': {4: '110千伏', 2: '1-10千伏', 5: '220千伏及以上',
                                                                               3: '35千伏'}}},
                                                        'days': [320]}, '广东省东西两翼地区': {'id': 41, 'type': {
                        5: {'name': '单一制', 'power': {20: '1-10 (20)千伏', 8: '35-110千伏', 1: '不满1千伏'}},
                        6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 5: '220千伏及以上', 19: '35-110千伏'}}},
                                                                                      'days': [320]},
                        '深汕特别合作区': {'id': 45, 'type': {
                            1: {'name': '大工业用电', 'power': {20: '10 (20)千伏', 5: '220千伏及以上', 6: '35-110千伏'}},
                            2: {'name': '一般工商业及其他用电', 'power': {20: '10 (20)千伏', 8: '35千伏及以上', 1: '不满1千伏'}}},
                                    'days': [320]}, '安徽省': {'id': 13,
                                                            'type': {5: {'name': '单一制',
                                                                         'power': {2: '1-10千伏', 3: '35千伏', 1: '不满1千伏'}},
                                                                     6: {'name': '两部制',
                                                                         'power': {4: '110千伏', 2: '1-10千伏',
                                                                                   5: '220千伏及以上', 3: '35千伏'}}},
                                                            'days': [320]}, '广东省粤北山区': {'id': 42, 'type': {
                        5: {'name': '单一制', 'power': {20: '1-10 (20)千伏', 8: '35-110千伏', 1: '不满1千伏'}},
                        6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 5: '220千伏及以上', 19: '35-110千伏'}}},
                                                                                        'days': [330]}}

                return self.returnTypeSuc(data)

            elif kt=='GetProvinces_other_new':
                data = {'浙江': {'id': 12,
                               'type': {4: {'name': '两部制大工业',
                                            'power': {23: '1-10 (20)千伏', 3: '35千伏', 4: '110千伏', 5: '220千伏及以上'}},
                                        3: {'name': '单一制一般工商业用电',
                                            'power': {1: '不满1千伏', 23: '1-10 (20)千伏', 8: '35千伏及以上'}},
                                        7: {'name': '两部制一般工商业用电',
                                            'power': {23: '1-10 (20)千伏', 4: '110千伏', 5: '220千伏及以上'}}}},
                        '江苏': {'id': 11, 'type': {
                            6: {'name': '两部制', 'power': {'2': '1-10 (20)千伏', 9: '35千伏', 4: '110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 2: '1-10 (20)千伏', 16: '35千伏'}}}},
                        '上海': {'id': 10, 'type': {
                            4: {'name': '大工业用电(两部制)', 'power': {1: '不满1千伏', 22: '10千伏', 3: '35千伏', 4: '110千伏及以上'}},
                            7: {'name': '一般工商业及其他用电(两部制)',
                                'power': {1: '不满1千伏', 22: '10千伏', 3: '35千伏', 4: '110千伏及以上'}}}},
                        '广东（珠三角五市）': {'id': 38, 'type': {
                            6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 19: '35-110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 20: '1-10 (20)千伏', 8: '35-110千伏'}}}},
                        '广东（惠州市）': {'id': 39, 'type': {
                            6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 19: '35-110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 20: '1-10 (20)千伏', 8: '35-110千伏'}}}},
                        '广东（江门市）': {'id': 40, 'type': {
                            6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 19: '35-110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 20: '1-10 (20)千伏', 8: '35-110千伏'}}}},
                        '海南省': {'id': 29, 'type': {
                            10: {'name': '100千伏安以下(单一制,含行政事业单位办公场所用电)', 'power': {'1': '不满1千伏', 2: '1-10千伏'}},
                            8: {'name': '100千伏安及以上(两部制)大工业',
                                'power': {21: '35千伏以下', 3: '35千伏', 4: '110千伏', 5: '220千伏及以上'}}}}, '湖南省': {'id': 18,
                                                                                                          'type': {
                                                                                                              5: {
                                                                                                                  'name': '单一制',
                                                                                                                  'power': {
                                                                                                                      15: '110千伏及以上',
                                                                                                                      3: '35千伏',
                                                                                                                      2: '1-10千伏',
                                                                                                                      1: '不满1千伏'}},
                                                                                                              6: {
                                                                                                                  'name': '两部制',
                                                                                                                  'power': {
                                                                                                                      5: '220千伏及以上',
                                                                                                                      4: '110千伏',
                                                                                                                      3: '35千伏',
                                                                                                                      2: '1-10千伏'}}}},
                        '河南省': {'id': 16, 'type': {6: {'name': '两部制', 'power': {5: '220千伏及以上'}}}},
                        '重庆市': {'id': 19, 'type': {
                            5: {'name': '单一制', 'power': {2: '1-10千伏', 3: '35千伏', 4: '110千伏'}},
                            6: {'name': '两部制', 'power': {2: '1-10千伏', 3: '35千伏', 4: '110千伏', 5: '220千伏及以上'}}}},
                        '湖北省': {'id': 17, 'type': {
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 2: '1-10千伏', 3: '35千伏'}},
                            6: {'name': '两部制', 'power': {2: '1-10千伏', 3: '35千伏', 4: '110千伏', 5: '220千伏及以上'}}}},
                        '深汕特别合作区': {'id': 45,
                                    'type': {1: {
                                        'name': '大工业用电',
                                        'power': {
                                            20: '10 (20)千伏',
                                            6: '35-110千伏',
                                            5: '220千伏及以上'}},
                                        2: {
                                            'name': '一般工商业及其他用电',
                                            'power': {
                                                1: '不满1千伏',
                                                20: '10 (20)千伏',
                                                8: '35千伏及以上'}}}},
                        '广东省东西两翼地区': {'id': 41, 'type': {
                            6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 19: '35-110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 2: '1-10 (20)千伏', 8: '35-110千伏'}}}},
                        '安徽省': {'id': 13, 'type': {5: {'name': '单一制', 'power': {1: '不满1千伏', 2: '1-10千伏', 3: '35千伏'}},
                                                   6: {'name': '两部制',
                                                       'power': {2: '1-10千伏', 3: '35千伏', 4: '110千伏', 5: '220千伏及以上'}}}},
                        '广东省粤北山区': {'id': 42, 'type': {
                            6: {'name': '两部制', 'power': {20: '1-10 (20)千伏', 19: '35-110千伏', 5: '220千伏及以上'}},
                            5: {'name': '单一制', 'power': {1: '不满1千伏', 20: '1-10 (20)千伏', 8: '35-110千伏'}}}},
                        '深圳市': {'id': 44, 'type': {11: {'name': '大量工商业及其他用电（250kWh及以下/千伏安•月）',
                                                        'power': {26: '10千伏高供低计（380V/220V计量）', 27: '10千伏高供低计',
                                                                  4: '110千伏', 5: '220千伏及以上'}},
                                                   12: {'name': '大量工商业及其他用电（250kWh及以上/千伏安•月）',
                                                        'power': {26: '10千伏高供低计（380V/220V计量）', 27: '10千伏高供低计',
                                                                  4: '110千伏', 5: '220千伏及以上'}},
                                                   13: {'name': '高需求工商业及其他用电（400kWh及以下/千伏安•月）',
                                                        'power': {26: '10千伏高供低计（380V/220V计量）', 27: '10千伏高供低计',
                                                                  4: '110千伏', 5: '220千伏及以上'}},
                                                   14: {'name': '高需求工商业及其他用电（400kWh及以上/千伏安•月）',
                                                        'power': {26: '10千伏高供低计（380V/220V计量）', 27: '10千伏高供低计',
                                                                  4: '110千伏', 5: '220千伏及以上'}},
                                                   2: {'name': '一般工商业及其他用电', 'power': {25: '100千伏安及以下和公变接入用电'}}}}}

                return self.returnTypeSuc(data)


        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        # self.refreshSession()
        try:
            if kt=='AddCustomer':
                name = self.get_argument('name',None) # 姓名
                phone_no = self.get_argument('phone_no',None) # 手机号
                company = self.get_argument('company',None) # 公司名称
                address = self.get_argument('address',None) # 地址
                if DEBUG:
                    logging.info("name:%s,phone_no:%s,company:%s,address:%s"%(name,phone_no,company,address))
                if not name or not phone_no or not company:
                    return self.customError('参数不完成')
                if not judge_phone(phone_no):
                    return self.customError('请输入正确手机号')
                page = user_session.query(ForecaseCustomer).filter(ForecaseCustomer.phone_no==phone_no).first()
                if page:
                    return self.customError('手机号已存在')
                p = ForecaseCustomer(name=name,phone_no=phone_no,company=company,address=address,op_ts=timeUtils.getNewTimeStr())
                user_session.add(p)
                user_session.commit()

                return self.returnTypeSuc({"id":p.id})
          
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
        user_session.close()
    
class SideForecaseGroupUserHandle(BaseHandler):
    '''
    小组管理
    用户管理
    角色管理
    静态路由管理
    '''
    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'GetGroupInfoList':  # 小组
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                id = self.get_argument('id',1)
                data = []
                total = user_session.query(func.count(ForecaseGroup.id)).filter(ForecaseGroup.organization_id==id,ForecaseGroup.is_use==1).scalar()
                pages = user_session.query(ForecaseGroup).filter(ForecaseGroup.organization_id==id,ForecaseGroup.is_use==1).order_by(ForecaseGroup.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    e = eval(str(pag))
                    data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetUesrAuthority':  # 获取用户权限
                id = self.get_argument('id',None)  # 角色id
                if DEBUG:
                    logging.info("id:%s"%(id))
                all,datas = [],{}
                roleInfo = user_session.query(ForecaseRole).filter(ForecaseRole.id==id,ForecaseRole.is_use==1).first()
                if not roleInfo:
                    return self.customError("无效id")
                Rauthority = eval(roleInfo.authority)
                station = eval(roleInfo.project_id)
                stationInfos = []
                for s in station:
                    stat = user_session.query(ForecaseProject.name.label('descr')).filter(ForecaseProject.id==s,ForecaseProject.is_use==1).first()
                    stationInfos.append({'var':True,'label':stat.descr})
                if Rauthority:  # 有权限
                    datas = {"authority":Rauthority,"select_key":eval(roleInfo.select_key),'station_info':stationInfos,'station_key':station}
                else:
                    aus = user_session.query(ForecaseAuthority).filter(or_(ForecaseAuthority.parent_id==None,ForecaseAuthority.parent_id=='')).all()
                    for u in aus:
                        authority = True if u.authority == 1 else False
                        ob = {'label':u.label,'tags':eval(u.tags),'index':u.index,'authority':authority}
                        if u.icon:
                            ob['icon'] = u.icon
                        if authority:
                            ob['station'] = stationInfos
                        ob['disabled'] = True if u.disabled == 1 else False
                        if self.rolechile(u,stationInfos):
                            ob['children'] = self.rolechile(u,stationInfos)
                        all.append(ob)
                    datas = {"authority":all,"select_key":eval(roleInfo.select_key),'station_info':stationInfos,'station_key':station}

                return self.returnTypeSuc(datas)
            elif kt == 'GetRoleInfoList':  # 所有角色
                descr = self.get_argument('descr', None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                data = []
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,ppageSize:%s" % (descr, pageNum, pageSize))
                if descr:
                    total = user_session.query(func.count(ForecaseRole.id)).filter(ForecaseRole.name.like('%' + descr + '%'),ForecaseRole.is_use==1).scalar()
                    pages = user_session.query(ForecaseRole).filter(ForecaseRole.name.like('%' + descr + '%'),ForecaseRole.is_use==1).order_by(ForecaseRole.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                else:
                    total = user_session.query(func.count(ForecaseRole.id)).filter(ForecaseRole.is_use==1).scalar()
                    pages = user_session.query(ForecaseRole).filter(ForecaseRole.is_use==1).order_by(ForecaseRole.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    e = eval(str(pag))
                    data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetAllOrganList':  # 所有组织结构
                descr = self.get_argument('name',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                data = []
                filter = [ForecaseOrganization.is_use==1]
                if descr:
                    filter.append(ForecaseOrganization.descr.like('%'+descr+"%"))
                total = user_session.query(func.count(ForecaseOrganization.id)).filter(*filter).scalar()
                pages = user_session.query(ForecaseOrganization).filter(*filter).order_by(ForecaseOrganization.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    e = eval(str(pag))
                    data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetUserInfoList':  # 所有用户
                org = self.get_argument('org', [])  # 部门id
                name = self.get_argument('name', None)
                role_ids = self.get_argument('role_ids', [])
                rank_ids = self.get_argument('rank_ids', [])
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                org = eval(str(org))
                role_ids = eval(str(role_ids))
                rank_ids = eval(str(rank_ids))
                filter = [ForecaseUser.is_use=='1']
                if name:
                    filter.append(ForecaseUser.name.contains(name))
                if org:
                    filter.append(ForecaseUser.organization_id.in_(org))
                if role_ids:
                    filter.append(ForecaseUser.user_role_id.in_(role_ids))
                if rank_ids:
                    filter.append(ForecaseUser.rank_id.in_(rank_ids))
                data = []
                total = user_session.query(func.count(ForecaseUser.id)).filter(*filter).scalar()
                pages = user_session.query(ForecaseUser).filter(*filter).order_by(ForecaseUser.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if pages:
                    for pag in pages:
                        e = eval(str(pag))
                        rank = user_session.query(Rank).filter(Rank.id==e.get('rank_id')).first()
                        e['rank_name'] = rank.name if rank else None
                        data.append(e)
                return self.returnTotalSuc(data,total)
            elif kt == 'GetOrganByUserId':  # 获取用户组织及其下级组织
                data,filter = [],[]
                id = self.get_argument('id',None)
                descr = self.get_argument('name',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
            
                if DEBUG:
                    logging.info("id:%s,descr:%s,pageNum:%s,pageSize:%s"%(id,descr,pageNum,pageSize))
                if not id:
                    session = self.getOrNewSession()
                    id = session.user['organization_id']
                if descr:
                    filter.append(ForecaseOrganization.descr.like('%'+descr+"%"))
               
                org = user_session.query(ForecaseOrganization).filter(ForecaseOrganization.id==id).first()
                if not org:
                    return self.customError("无效id")
                get_end_node = org.get_lower_node()
                filter.append(ForecaseOrganization.id.in_(get_end_node))
                total = user_session.query(func.count(ForecaseOrganization.id)).filter(*filter).scalar()
                all = user_session.query(ForecaseOrganization).filter(*filter).order_by(ForecaseOrganization.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                
                for org in all:
                    o = eval(str(org))
                    p = None
                    if org.parent_id:
                        p = user_session.query(ForecaseOrganization).get(org.parent_id)
                    if p:
                        o['parent_descr'] = p.descr
                    data.append(o)
                self.returnTotalSuc(data,total)
            elif kt == 'GetRank':
                """职级列表"""
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 10))
                name = self.get_argument('name', None)
                filter = [Rank.is_use == '1']
                if name:
                    filter.append(Rank.name.contains(name))

                data = user_session.query(Rank).filter(*filter).order_by(Rank.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                total = user_session.query(func.count(Rank.id)).filter(*filter).scalar()

                d = []
                for info in data:
                    if info.name == '基层员工':
                        d.append(
                            {
                                'id': info.id,
                                'name': info.name,
                                'is_use': 1
                            }
                        )
                    else:
                        d.append(
                            {
                                'id': info.id,
                                'name': info.name
                            }
                        )

                return self.returnTotalSuc(d, total)
            else:
                return self.pathError()

        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'AddRole':  # 添加角色
                name = self.get_argument('name','')
                if DEBUG:
                    logging.info("name:%s"%name)
                if not name:
                    return self.customError("名称为空")
                elif user_session.query(ForecaseRole).filter(ForecaseRole.name == name,ForecaseRole.is_use==1).first():
                    return self.customError("角色已存在")
                FA = ForecaseRole(name=name,project_id='[]',authority='[]',select_key='["SYtab1"]',op_ts=timeUtils.getNewTimeStr())
                user_session.merge(FA)
                user_session.commit()
                return self.returnTypeSuc('')
            # elif kt == 'UpdateRole':  # 修改角色
            #     id = self.get_argument('id', None)
            #     name = self.get_argument('name', '')
            #     authority = self.get_argument('authority', '')
            #     select_key = self.get_argument('select_key', '')
            #
            #     if DEBUG:
            #         logging.info("id:%s,name:%s,authority:%s,select_key:%s" % (id,name,authority,select_key))
            #     if name:
            #         to_update_name=user_session.query(ForecaseRole).filter(ForecaseRole.name == name,ForecaseRole.is_use==1).first()
            #         if to_update_name:
            #             return self.customError("角色已存在")
            #         else:
            #             to_update_name.name = name
            #     to_update = user_session.query(ForecaseRole).filter(ForecaseRole.id == id,ForecaseRole.is_use==1).first()
            #     if authority:
            #         authority_=str(authority).replace('false', 'False').replace('true', 'True')
            #         to_update.authority = authority_
            #     if select_key:
            #         to_update.select_key = select_key
            #     if not to_update:
            #         return self.customError("id无效")
            #     user_session.commit()
            #     return self.returnTypeSuc('')
            elif kt == 'DeleteRole':  # 删除角色
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                org = user_session.query(ForecaseRole).filter(ForecaseRole.id==id,ForecaseRole.is_use==1).first()
                if not org:
                    return self.customError("id为空或无效")
                if user_session.query(ForecaseUser).filter(ForecaseUser.user_role_id==id, ForecaseUser.is_use==1).first():
                    return self.customError("当前角色下有用户绑定，请先解绑后再删除角色！")
                org.is_use=0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'AddGroup':  # 添加小组
                name = self.get_argument('name','')
                organization_id = self.get_argument('organization_id','')
                if DEBUG:
                    logging.info("name:%s,organization_id:%s"%(name,organization_id))
                if not name or not organization_id:
                    return self.customError("参数不完整")
                elif user_session.query(ForecaseGroup).filter(ForecaseGroup.name == name, ForecaseGroup.organization_id == int(organization_id)).first():
                    return self.customError("小组已存在")
                FG = ForecaseGroup(name=name,organization_id=organization_id,op_ts=timeUtils.getNewTimeStr())
                user_session.merge(FG)
                user_session.commit()
                return self.returnTypeSuc({"id":FG.id})
            elif kt == 'DeleteGroup':  # 删除小组
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                org = user_session.query(ForecaseGroup).filter(ForecaseGroup.id==id,ForecaseGroup.is_use==1).first()
                if user_session.query(ForecaseUser).filter(ForecaseUser.group_id==id, ForecaseUser.is_use==1).first():
                    return self.customError("当前小组下有用户绑定，请先解绑后再删除小组！")
                if not org:
                    return self.customError("id为空或无效")
                org.is_use=0
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'UpdateRole':  # 修改角色
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                project_id = self.get_argument('project_id',None)
                authority = self.get_argument('authority',None)
                select_key = self.get_argument('select_key',None)
                if DEBUG:
                    logging.info("id:%s,name:%s,project_id:%s,authority:%s,select_key:%s"%(id,name,project_id,authority,select_key))
                to_update = user_session.query(ForecaseRole).filter(ForecaseRole.id==id).first()
                if not to_update:
                    return self.customError("id无效")
                if name:
                    to_update.name = name
                if authority:
                    authority = str(authority).replace('false', 'False').replace('true', 'True')
                    to_update.authority = authority
                if project_id:
                    if list(to_update.project_id) != list(project_id):
                        to_update.authority = '[]'
                    to_update.project_id = project_id
                if select_key:
                    to_update.select_key = select_key
                
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'UpdateGroup':  # 修改小组
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)
                organization_id = self.get_argument('organization_id',None)
                if DEBUG:
                    logging.info("id:%s,name:%s,organization_id:%s"%(id,name,organization_id))
                if user_session.query(ForecaseGroup).filter(ForecaseGroup.is_use == 1, ForecaseGroup.name == name, ForecaseGroup.organization_id == int(organization_id), ForecaseGroup.id != int(id)).first():
                    return self.customError('小组已存在！')
                to_update = user_session.query(ForecaseGroup).filter(ForecaseGroup.id==id).first()
                if not to_update:
                    return self.customError("id无效")
                if name:
                    to_update.name = name
                if organization_id:
                    to_update.organization_id = organization_id
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'AddUser':  # 添加用户
                name = self.get_argument('name',None)  # 姓名
                phone_no = self.get_argument('phone_no',None)  # 手机号
                account = self.get_argument('account',None)  # 登录名
                email = self.get_argument('email',None)  # 邮箱
                passwd = self.get_argument('passwd','123456')  # 密码
                organization_id = self.get_argument('organization_id',None)  # 所属组织
                user_role_id = self.get_argument('user_role_id',None)  # 角色
                group_id = self.get_argument('group_id',None)  # 小组
                rank_id = self.get_argument('rank_id',None)  # 职级
                if DEBUG:
                    logging.info("name:%s,phone_no:%s,account:%s,passwd:%s,organization_id:%s,user_role_id:%s,group_id:%s,email:%s"%(name,phone_no,account,
                    passwd,organization_id,user_role_id,group_id,email))
                files = self.request.files
                file_path = '/home/<USER>/side'
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                imgs = files.get('head_img')
                img_path = ''
                if imgs:
                    data = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    img_path = '%s/%s' % (file_path, uploadfilename)
                    file = open(img_path, 'wb')
                    file.write(data)
                    file.close()
                        
                # print (img_path)
                if not name or not account or not organization_id or not user_role_id or not group_id or not rank_id:
                    return self.customError("入参不完整")
                elif not judge_phone(phone_no):
                    return self.customError("手机号无效")
                elif user_session.query(ForecaseUser).filter(ForecaseUser.account == account, ForecaseUser.is_use == 1).first():
                    return self.customError("用户名已存在")
                elif user_session.query(ForecaseUser).filter(ForecaseUser.phone_no == phone_no, ForecaseUser.is_use == 1).first():
                    return self.customError("手机号已存在")
                if email:
                    if not judge_email(email):
                        return self.customError("邮箱格式错误")
                passwd = computeMD5(passwd)
                
                user = ForecaseUser(name=name,account=account,passwd=passwd,organization_id=organization_id,user_role_id=user_role_id,
                    phone_no=phone_no,op_ts=timeUtils.getNewTimeStr(),group_id=group_id,email=email,head_img=img_path,rank_id=int(rank_id))
                user_session.add(user)
                user_session.commit()
                id = user.id
                query = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use!='0').order_by(ForecaseDicPolicy.id.desc()).all()
                for q in query:
                    q_1 = eval(str(q))
                    if (organization_id in q_1['see_org']) and (group_id in q_1['see_gro']):#部门和小组id 在政策情报中
                        q_1['see_users'].append(id)
                        q.see_users = str(q_1['see_users'])
                        user_session.commit()
                user_session.close()
                return self.returnTypeSuc({"id":id})
            elif kt == 'Modify':  # 修改用户信息
                id = int(self.get_argument('id',None))
                name = self.get_argument('name',None)  # 姓名
                phone_no = self.get_argument('phone_no',None)  # 手机号
                account = self.get_argument('account',None)  # 登录名
                email = self.get_argument('email',None)  # 邮箱
                # passwd = self.get_argument('passwd','123456')  # 密码
                organization_id = self.get_argument('organization_id',None)  # 所属组织
                user_role_id = self.get_argument('user_role_id',None)  # 角色
                group_id = self.get_argument('group_id',None)  # 小组
                rank_id = self.get_argument('rank_id',None)  # 小组
                if DEBUG:
                    logging.info("id:%s,name:%s,phone_no:%s,account:%s,organization_id:%s,user_role_id:%s,group_id:%s,email:%s"%(id,name,phone_no,account,
                    organization_id,user_role_id,group_id,email))
                to_update = user_session.query(ForecaseUser).filter(ForecaseUser.id==id).first()
               
                if not to_update:
                    return self.customError("id无效")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效")
                if account != to_update.account and user_session.query(ForecaseUser).filter(ForecaseUser.account == account).first():
                    return self.customError("登录名重复")
                if phone_no != to_update.phone_no and user_session.query(ForecaseUser).filter(ForecaseUser.phone_no == phone_no).first():
                    return self.customError("手机号已存在")
              
                to_update.name = name
                to_update.phone_no = phone_no
                to_update.account = account
                if email:
                    if not judge_email(email):
                        return self.customError("邮箱格式错误")
                    to_update.email = email
                to_update.organization_id = organization_id
                to_update.user_role_id = user_role_id
                to_update.group_id = group_id
                to_update.rank_id = rank_id
                user_session.commit()
                query = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use != '0').order_by(ForecaseDicPolicy.id.desc()).all()
                for q in query:
                    q_1 = eval(str(q))
                    if (organization_id in q_1['see_org']) and (group_id in q_1['see_gro']):#部门和小组id 在政策情报中
                        q_1['see_users'].append(id)
                        q.see_users=str(q_1['see_users'])
                        user_session.commit()
                    elif (organization_id not in q_1['see_org']) or (group_id not in q_1['see_gro']):#部门和小组id 在政策情报中
                        try:
                            q_1['see_users'].remove(int(id))
                            q.see_users=str(q_1['see_users'])
                            user_session.commit()
                        except:
                            pass
                user_session.close()
                return self.returnTypeSuc('')
            elif kt == 'UpdatePwd':  # 修改用户密码
                id = self.get_argument('id',None)
                passwd = self.get_argument('passwd',None)
                if DEBUG:
                    logging.info("id:%s,passwd:%s"%(id,passwd))
                to_update = user_session.query(ForecaseUser).filter(ForecaseUser.id==id).first()
                if not to_update or not passwd:
                    return self.customError("id无效或密码为空")
                if passwd == to_update.passwd:  # 原密码没修改
                    return self.customError("新旧密码一致")
                passwd = computeMD5(passwd)  # md5加密
                if passwd == to_update.passwd:  # 修改后和原密码一样
                    return self.customError("新旧密码一致")

                to_update.passwd = passwd
                user_session.commit()
                user_session.close() 
                return self.returnTypeSuc('')

            elif kt == 'UpdateInfo':
                id = self.get_argument('id',None)
                email = self.get_argument('email',None)
                phone_no = self.get_argument('phone_no',None)
                if DEBUG:
                    logging.info("id:%s,email:%s,phone_no:%s"%(id,email,phone_no))
                to_update = user_session.query(ForecaseUser).filter(ForecaseUser.id == id).first()
                if not to_update:
                    return self.customError("id不存在")
                if not email or not phone_no:
                    return self.customError("邮箱或者手机号未输入")
                if not judge_phone(phone_no):
                    return self.customError("手机号无效")
                if not judge_email(email):
                    return self.customError("邮箱格式错误")
                to_update.email = email
                to_update.phone_no = phone_no
                user_session.commit()
                user_session.close()
                return self.returnTypeSuc('')

            elif kt == 'DeleteUser':  # 删除用户
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(ForecaseUser).filter(ForecaseUser.id==id,ForecaseUser.is_use==1).first()
                if not delete_apex:
                    return self.customError("id无效")
                delete_apex.is_use=0
                query = user_session.query(ForecaseDicPolicy).filter(ForecaseDicPolicy.is_use != '0').order_by(ForecaseDicPolicy.id.desc()).all()
                for q in query:
                    q_1 = eval(str(q))
                    if int(id) in q_1['see_users']:#可见人里有用户id
                        q_1['see_users'].remove(int(id))
                        q.see_users = str(q_1['see_users'])
                        user_session.commit()
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'AddOrgan':  # 添加组织
                name = self.get_argument('name',None)  # 组织名称
                parent_id = self.get_argument('parent_id',None)  # 父节点id
                if DEBUG:
                    logging.info("name:%s,parent_id:%s"%(name,parent_id))

                if not name :
                    return self.customError("入参不完整")
                if user_session.query(ForecaseOrganization).filter(ForecaseOrganization.is_use == 1, ForecaseOrganization.descr == name).first():
                    return self.customError('部门名称重复！')
                user = ForecaseOrganization(descr=name, op_ts=timeUtils.getNewTimeStr())
                user_session.add(user)
                user_session.commit()
                return self.returnTypeSuc({"id":user.id})
            elif kt == 'UpdateOrgan':  # 修改组织
                id = self.get_argument('id',None)
                name = self.get_argument('name',None)  # 组织名称
                parent_id = self.get_argument('parent_id',None)  # 父节点id
                if DEBUG:
                    logging.info("id:%s,name:%s,parent_id:%s"%(id,name,parent_id))
                if user_session.query(ForecaseOrganization).filter(ForecaseOrganization.is_use == 1, ForecaseOrganization.descr == name, ForecaseOrganization.id != int(id)).first():
                    return self.customError('部门名称重复！')
                user_session.query(ForecaseOrganization).filter(ForecaseOrganization.id==id).update({'descr': name})
                # if not to_update:
                #     return self.customError("id无效")
                # to_update.name = name
                # to_update.parent_id = parent_id
                user_session.commit()
                return self.returnTypeSuc('')
            elif kt == 'DeleteOrgan':  # 删除组织
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                to_update = user_session.query(ForecaseOrganization).filter(ForecaseOrganization.id==id).first()
                if user_session.query(ForecaseUser).filter(ForecaseUser.organization_id==id, ForecaseUser.is_use==1).first():
                    return self.customError("当前组织下有用户绑定，请先解绑后再删除组织！")
                if not to_update:
                    return self.customError("id无效")
                to_update.is_use=0
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'AddRank':
                """新增职级"""
                name = self.get_argument('name')
                if not name:
                    return self.customError('参数错误！')
                if user_session.query(Rank).filter(Rank.is_use == 1, Rank.name == name).first():
                    return self.customError('职级名称重复！')
                info = Rank(name=name)
                user_session.add(info)
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'DeleteRank':
                """删除职级"""
                _id = self.get_argument('id')
                if not _id:
                    return self.customError('参数错误！')
                if user_session.query(ForecaseUser).filter(ForecaseUser.rank_id==_id, ForecaseUser.is_use==1).first():
                    return self.customError("当前职级下有用户绑定，请先解绑后再删除职级！")
                user_session.query(Rank).filter(Rank.id == int(_id)).update({'is_use': '0'})
                user_session.commit()
                return self.returnTypeSuc('')

            elif kt == 'EditRank':
                """更新职级"""
                _id = self.get_argument('id')
                name = self.get_argument('name')
                if not _id or not name:
                    return self.customError('参数错误！')
                if user_session.query(Rank).filter(Rank.is_use == 1, Rank.name == name, Rank.id != int(_id)).first():
                    return self.customError('职级名称重复！')
                user_session.query(Rank).filter(Rank.id == int(_id)).update({'name': name})
                user_session.commit()
                return self.returnTypeSuc('')

            else:
                pass


        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

    def rolechile(self, Bean,station):
        # 递归构建树状权限
        all = []
        auts = user_session.query(ForecaseAuthority).filter(ForecaseAuthority.parent_id==Bean.index).all()
        
        for u in auts:
            authority = True if u.authority == 1 else False
            ob = {'label':u.label,'tags':eval(u.tags),'index':u.index,'authority':authority}
            if u.icon:
                ob['icon'] = u.icon
            if authority:
                ob['station'] = station
            ob['disabled'] = True if u.disabled == 1 else False
            if self.rolechile(u,station):
                ob['children'] = self.rolechile(u,station)
            all.append(ob)
        return all


class SideForecaseUserLoginHandler(BaseHandler):
    u'用于用户登录'

    def get(self, kt):
       
        if kt == 'userNull':  # 用户未登录
            return self.userError()
        else:
            return self.pathError()
    
    def post(self,kt):
        try:
        # if 1:
        #     if kt == 'Login':  # 登录
        #         # print (self.request.remote_ip)
        #         Session = self.getOrNewSession()
        #         user = Session.user
        #         if user:
        #             logging.info("user is in session")
        #             return self.userExist("用户已登录",user)
        #         account = self.get_argument("account", None)  # 登录名
        #         passwd = self.get_argument("passwd", None)  # 密码
        #         if DEBUG:
        #             logging.info('account:%s,passwd:%s'%(account,passwd))
        #         if not account or not passwd:
        #             return self.customError("用户名或密码为空")
        #         passwd = computeMD5(passwd)
        #         user = user_session.query(ForecaseUser).filter(or_(ForecaseUser.account == account,ForecaseUser.phone_no == account),ForecaseUser.is_use == 1).first()
        #         if not user:
        #             Session.remove()
        #             return self.customError("用户名或密码错误")
        #         else:
        #             retry = r.get(str(user.account) + self.request.remote_ip)
        #             if retry and int(retry) > 3:
        #                 Session.remove()
        #                 disable = r.ttl(str(user.account) + self.request.remote_ip)
        #                 disable_minute = 0
        #                 if disable:
        #                     disable_minute = int(math.ceil(disable/60.0))
        #                 return self.customError("账户已锁定请"+str(disable_minute)+"分钟再试")
        #             if user.passwd != passwd:
        #                 r.incr(str(user.account)+self.request.remote_ip,)
        #                 r.expire(str(user.account)+self.request.remote_ip, 600)
        #                 retry = r.get(str(user.account) + self.request.remote_ip)
        #                 Session.remove()
        #                 if retry and int(retry) == 3:
        #                     return self.customError("还有一次机会账户被锁定10分钟")
        #                 elif retry and int(retry) >= 3:
        #                     return self.customError("账户已锁定")
        #                 return self.customError("用户名或密码错误")
        #
        #         r.delete(str(user.account) + self.request.remote_ip)
        #         u = eval(str(user))
        #         # if user.userRole.station_id:
        #         #     u['role_station_id'] = eval(user.userRole.station_id)
        #         user = u
        #         # station_id = user['station_id']
        #         # stations = user_session.query(Station).filter(Station.index.in_(list(station_id))).all()
        #         # station_names = []
        #         # for s in stations:
        #         #     station_names.append(s.name)
        #         # user['station_name'] = station_names
        #         Session.user = user
        #         self.updateSession(Session)
        #         user_session.commit()
        #
        #         # 将session归还连接池
        #         user_session.close()
        #         return self.returnTypeSuc(user)
            if kt == 'Login':  # 登录
                # print (self.request.remote_ip)
                # Session = self.getOrNewSession()
                # user = Session.user
                # if user:
                #     logging.info("user is in session")
                #     return self.userExist("用户已登录", user)
                account = self.get_argument("account", None)  # 登录名
                passwd = self.get_argument("passwd", None)  # 密码
                if DEBUG:
                    logging.info('account:%s,passwd:%s' % (account, passwd))
                if not account or not passwd:
                    return self.customError("用户名或密码为空")
                passwd = computeMD5(passwd)
                user = user_session.query(ForecaseUser).filter(
                    or_(ForecaseUser.account == account, ForecaseUser.phone_no == account),
                    ForecaseUser.is_use == 1).first()
                if not user:
                    # Session.remove()
                    return self.customError("用户名或密码错误")
                else:
                    retry = r.get(str(user.account) + self.request.remote_ip)
                    if retry and int(retry) > 3:
                        # Session.remove()
                        disable = r.ttl(str(user.account) + self.request.remote_ip)
                        disable_minute = 0
                        if disable:
                            disable_minute = int(math.ceil(disable / 60.0))
                        return self.customError("账户已锁定请" + str(disable_minute) + "分钟再试")
                    if user.passwd != passwd:
                        r.incr(str(user.account) + self.request.remote_ip, )
                        r.expire(str(user.account) + self.request.remote_ip, 600)
                        retry = r.get(str(user.account) + self.request.remote_ip)
                        # Session.remove()
                        if retry and int(retry) == 3:
                            return self.customError("还有一次机会账户被锁定10分钟")
                        elif retry and int(retry) >= 3:
                            return self.customError("账户已锁定")
                        return self.customError("用户名或密码错误")

                r.delete(str(user.account) + self.request.remote_ip)
                u = eval(str(user))
                # if user.userRole.station_id:
                #     u['role_station_id'] = eval(user.userRole.station_id)
                user = u
                success_token = create_token({"user_id": user.get('id')})
                if isinstance(success_token, bytes):
                    success_token = base64.b64encode(success_token).decode('utf-8')
                Session = self.getOrNewSession(success_token)
                Session.user = user
                # station_id = user['station_id']
                # stations = user_session.query(Station).filter(Station.index.in_(list(station_id))).all()
                # station_names = []
                # for s in stations:
                #     station_names.append(s.name)
                # user['station_name'] = station_names
                # Session.user = user
                self.updateSession(Session, success_token)
                user_session.commit()
                user['success_token'] = success_token

                # 将session归还连接池
                user_session.close()
                return self.returnTypeSuc(user)

            elif kt == 'LogOut':  # 退出
                session = self.getOrNewSession()
                if session.user:
                    session.user = None
                    self.updateSession(session)
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()
