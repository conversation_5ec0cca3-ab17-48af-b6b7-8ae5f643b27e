import ast
import datetime
import json
import random
import time
import uuid
import concurrent.futures

import pymysql
import decimal
import numpy as np
import paho.mqtt.client as mqtt
from django.conf import settings
from django.db import transaction, connections
from django.db.models import Q, Max
from django_redis import get_redis_connection
from rest_framework.response import Response
from rest_framework.views import APIView

from apis.app2.dict_mapping import ELE_DICTS, VOL_DICTS
from apis.app2.dict_mapping import FAULTSTATUS, FAULTANALYSISSOLUTION, MAINTENANCEINSTRUCTIONS

from apis.app2.utils import paging
from apis.statistics_apis.models import UserStrategy, Month, UserStrategyCategoryNew
from apis.statistics_apis.main import mtqq_station_strategy
from apis.statistics_apis.serializers import UserStrategySerializer, ResponseUserStrategySerializer, \
     CurrentStrategySerializers, DefaultStrategySerializers, CustomizeStrategySerializers
from apis.user import models
from common import common_response_code
from common.constant import EMPTY_STR_LIST
from encryption.AES_symmetric_encryption import EncryptDate
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from serializers import monitor_serializers
from apis.app2.monitor2.monitor2_serializers import VirtuallyCheckSMSCodeSerializer
from tools.aly_send_smscode import Sample
from tools.count import unit_convert
from decimal import Decimal
from settings.meter_settings import METER_DIC
from settings.meter_origin_values import OriginValuesDict
from tools.hour_setting import create_time_mapping
success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print("Connected successfully")
        mqtt_client.subscribe("django/mqtt")  # 订阅主题
    else:
        print("Bad connection. Code:", rc)


def on_message(mqtt_client, userdata, msg):
    print(f"Received message on topic: {msg.topic} with payload: {msg.payload}")


class StationListView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get_station_data(self, station, conn, project_res, station_res, station_address):
        station_dict = {}
        SOC = 0
        PCS = 0
        bms_count = 0
        discharge_status = 0  # 充放电状态
        # 查询主站下从站
        if models.StationStatus.objects.filter(station__in=station.stationdetails_set.all(), status=4).first():
            s_status = 4
        else:
            status_res = models.StationStatus.objects.filter(station__in=station.stationdetails_set.all()).aggregate(
                Max('status'))
            if status_res:
                s_status = status_res.get('status__max')
            else:
                s_status = 1

        for slave_name, units in station_res[station.id].items():
            for bms_name in units['unit']['bms']:
                bms = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave_name, bms_name))
                if not bms:
                    bms = {}
                else:
                    bms_count += 1
                    bms = eval(eval(bms))
                    if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST and SOC != '--':
                        SOC += float(bms.get('SOC'))
                    else:
                        SOC = '--'
            for pcs_name in units['unit']['pcs']:
                pcs = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave_name, pcs_name))
                if not pcs:
                    pcs = {}
                    discharge_status = -1  # -1：离线
                else:
                    pcs = eval(eval(pcs))
                if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST and PCS != '--':
                    PCS += float(pcs.get('P'))
                else:
                    PCS = '--'
            # 不离线的情况下判断充放电状态 1：充电；0：静置；2：放电
            for pcs_name in units['unit_all']['pcs']:
                # 判断单元充放电状态 -1：离线；1：充电；0：静置；2：放电
                pcs = conn.get(
                    'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave_name,
                                                            pcs_name))
                if not pcs:
                    discharge_status = -1
                    break
                else:
                    pcs = eval(eval(pcs))
                    p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
                    if p != '--':
                        if p > 1:
                            discharge_status = 2
                        elif p < -1:
                            discharge_status = 1
                        else:
                            discharge_status = 0 if discharge_status == 0 else discharge_status
                    else:
                        discharge_status = -1
                        break

        station_dict['station_name'] = station.name
        if bms_count > 0 and SOC != '--':
            station_dict['SOC'] = round(SOC / bms_count, 2) if SOC / bms_count <= 100 else '--'
        else:
            station_dict['SOC'] = '--'
        station_dict['power'] = round(PCS, 2) if PCS != '--' else PCS

        station_dict['discharge_status'] = discharge_status
        station_dict['english_name'] = station.english_name
        station_dict['id'] = station.id
        station_dict['longitude'] = project_res.get(station.project_id)['longitude']
        station_dict['latitude'] = project_res.get(station.project_id)['latitude']
        station_dict['rated_power'] = (unit_convert(project_res.get(station.project_id)['rated_power'], 'kW'))
        station_dict['rated_capacity'] = (unit_convert(project_res.get(station.project_id)['rated_capacity'], 'kWh'))
        station_dict['address'] = station_address[station.id]
        station_dict['station_status'] = s_status
        station_dict['efficiency'] = station.efficiency

        return station_dict
    def get_station_data_old(self, station, conn):

        rated_power_all = []
        rated_capacity_all = []
        station_dict = {}
        SOC = 0
        PCS = 0
        bms_count = 0
        discharge_status = 0  # 充放电状态
        # 查询主站下从站
        slave_data = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).all()
        # s_status = models.StationStatus.objects.filter(station=station.stationdetails_set.filter(Q(slave=0, pack=-1) | Q(slave=-1, pack=-1) | Q(slave=-1, pack=0)).first()).first().status
        s_status_res = models.StationStatus.objects.filter(station=station.stationdetails_set.filter(is_delete=0,
                                                                                                     english_name=station.english_name).first()).first()
        if s_status_res:
            s_status = s_status_res.status
        else:
            s_status = 1
        for slave in slave_data:
            # 计算总功率和容量
            rated_power_all.append(float(slave.rated_power))
            rated_capacity_all.append(float(slave.rated_capacity))
            status_res = models.StationStatus.objects.filter(station=slave).first()
            status = status_res.status if status_res else 1
            s_status = status if s_status < status and s_status != 4 else s_status
            units = models.Unit.objects.filter(is_delete=0, station__id=slave.id).all()  # 查询BMS, PCS 数据
            for unit in units:
                bms = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.bms))
                if not bms:
                    bms = {}
                else:
                    bms_count += 1
                    bms = eval(eval(bms))
                pcs = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.pcs))
                if not pcs:
                    pcs = {}
                    discharge_status = -1  # -1：离线
                else:
                    pcs = eval(eval(pcs))
                if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST and PCS != '--':
                    PCS += float(pcs.get('P'))
                else:
                    PCS = '--'
                if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST and SOC != '--':
                    SOC += float(bms.get('SOC'))
                else:
                    SOC = '--'
        # 查询地图打点净经纬度
        dodging = models.Project.objects.values('longitude', 'latitude').filter(is_used=1,
                                                                                id=station.project_id).first()
        if dodging:
            station_dict['latitude'] = dodging['latitude']  # 纬度
            station_dict['longitude'] = dodging['longitude']  # 经度
        station_dict['station_name'] = station.name
        if bms_count > 0 and SOC != '--':
            station_dict['SOC'] = round(SOC / bms_count, 2) if SOC / bms_count <= 100 else '--'
        else:
            station_dict['SOC'] = '--'
        station_dict['power'] = round(PCS, 2) if PCS != '--' else PCS
        # 不离线的情况下判断充放电状态 1：充电；0：静置；2：放电
        # 计算多个单元的边界值
        for unit in models.Unit.objects.filter(is_delete=0,
                                               station__in=station.stationdetails_set.filter(is_delete=0).all()):
            # 判断单元充放电状态 -1：离线；1：充电；0：静置；2：放电
            pcs = conn.get(
                'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.station.english_name,
                                                        unit.pcs))
            if not pcs:
                discharge_status = -1
                break
            else:
                pcs = eval(eval(pcs))
                p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
                if p != '--':
                    if p > 1:
                        discharge_status = 2
                    elif p < -1:
                        discharge_status = 1
                    else:
                        discharge_status = 0 if discharge_status == 0 else discharge_status
                else:
                    discharge_status = -1
                    break

        # if discharge_status != -1:
        #     if PCS != '--':
        #         if PCS >= unit_count:
        #             discharge_status = 2
        #         elif PCS <= -unit_count:
        #             discharge_status = 1
        #         else:
        #             discharge_status = 0
        #     else:
        #         discharge_status = 0
        station_dict['discharge_status'] = discharge_status
        station_dict['english_name'] = station.english_name
        station_dict['id'] = station.id
        station_dict['rated_power'] = (unit_convert(sum(rated_power_all), 'kW'))
        station_dict['rated_capacity'] = (unit_convert(sum(rated_capacity_all), 'kWh'))
        station_dict['address'] = slave.address
        station_dict['station_status'] = s_status
        station_dict['efficiency'] = station.efficiency

        return station_dict

    def get(self, request):
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 8))
        status = int(request.query_params.get('status', 0))  # 0：全部；1：正常；3：故障；4：离线
        name = request.query_params.get('name')
        request_id = request.user["user_id"]
        conn = get_redis_connection('3')  # 连接redis
        if size >= 999:
            if conn.exists('app_map_station_data'):  # 地图数据缓存：4分钟一次
                data = []
                station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0,
                                                             name__contains=name).order_by("-efficiency").all()
                cache_data = json.loads(conn.get('app_map_station_data'))
                for i in station_obj:
                    if cache_data.get(str(i.id)):
                        data.append(cache_data.get(str(i.id)))
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {
                            "message": "success",
                            "detail": sorted(data, key=lambda x: x['efficiency'], reverse=True),
                        },
                        "total": len(data),
                        "totalpage": 1,
                        "page": 1,
                    }
                )

        if status == 0:
            station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0,
                                                             name__contains=name).order_by("-efficiency").all()
        else:
            if status == 4:
                station_status = models.StationDetails.objects.values('master_station_id').annotate(
                    max_status=Max('stationstatus__status')).filter(max_status__in=[4, 5])
            else:
                station_status = models.StationDetails.objects.values('master_station_id').annotate(
                    max_status=Max('stationstatus__status')).filter(max_status=status)
            ids = [s.get('master_station_id') for s in station_status] if station_status else []
            station_obj = models.MaterStation.objects.filter(userdetails__id=request_id, is_delete=0,
                                                             id__in=ids, name__contains=name).order_by("-efficiency").all()

        page_res = paging(page, size, station_obj)  # 分页器
        station_data = page_res.get('data')
        data = []
        project_ids = []
        station_res = {}
        station_address = {}
        for master in station_data:
            station_res[master.id] = {}
            project_ids.append(master.project_id)
            for slave in master.stationdetails_set.filter(is_delete=0).all():
                station_address[master.id] = slave.address
                station_res[master.id][slave.english_name] = {
                    'unit': {
                        'bms': [],
                        'pcs': []
                    },
                    'unit_all': {
                        'bms': [],
                        'pcs': []
                    }
                }


                units = slave.unit_set.filter(is_delete=0).all()
                for u in units:
                    if slave.slave != 0 and slave.pack != 0:
                        station_res[master.id][slave.english_name]['unit']['bms'].append(u.bms)
                        station_res[master.id][slave.english_name]['unit']['pcs'].append(u.pcs)
                    station_res[master.id][slave.english_name]['unit_all']['bms'].append(u.bms)
                    station_res[master.id][slave.english_name]['unit_all']['pcs'].append(u.pcs)


        project_res = models.Project.objects.values('id', 'longitude', 'latitude', 'rated_power', 'rated_capacity').filter(is_used=1, id__in=project_ids).all()
        project_res = {i['id']: {'longitude': i['longitude'], 'latitude': i['latitude'], 'rated_power': i['rated_power'], 'rated_capacity': i['rated_capacity']} for i in project_res}

        # for station in station_data:
        #     if models.StationStatus.objects.filter(station__in=station.stationdetails_set.all(), status=4).first():
        #         status = 4
        #     else:
        #         status_res = models.StationStatus.objects.filter(station__in=station.stationdetails_set.all()).aggregate(Max('status'))
        #         if status_res:
        #             status = status_res.get('status__max')
        #         else:
        #             status = 1
        #     bms_count = 0
        #     PCS = 0
        #     SOC = 0
        #     slave_data = station.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).all()
        #     for slave in slave_data:
        #         units = models.Unit.objects.filter(is_delete=0, station__id=slave.id).all()
        #         ts = 0
        #         for unit in units:
        #
        #             bms = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.bms))
        #             if not bms:
        #                 bms = {}
        #             else:
        #                 bms_count += 1
        #                 bms = eval(eval(bms))
        #             pcs = conn.get('Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', slave.english_name, unit.pcs))
        #             if not pcs:
        #                 pcs = {}
        #                 discharge_status = -1  # -1：离线
        #             else:
        #                 pcs = eval(eval(pcs))
        #             if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST and PCS != '--':
        #                 PCS += float(pcs.get('P'))
        #             else:
        #                 PCS = '--'
        #             if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST and SOC != '--':
        #                 SOC += float(bms.get('SOC'))
        #             else:
        #                 SOC = '--'
        #     station_dict = {
        #         'longitude': project_res.get(station.project_id)['longitude'],
        #         'latitude': project_res.get(station.project_id)['latitude'],
        #         'station_name': station.name,
        #         'SOC': '--',
        #         'power': '--',
        #         'id': station.id,
        #         'rated_power': (unit_convert(project_res.get(station.project_id)['rated_power'], 'kW')),
        #         'rated_capacity': (unit_convert(project_res.get(station.project_id)['rated_capacity'], 'kWh')),
        #         'address': station.stationdetails_set.first().address,
        #         'station_status': status,
        #         'efficiency': station.efficiency,
        #     }
        #     if bms_count > 0 and SOC != '--':
        #         station_dict['SOC'] = round(SOC / bms_count, 2) if SOC / bms_count <= 100 else '--'
        #     else:
        #         station_dict['SOC'] = '--'
        #     station_dict['power'] = round(PCS, 2) if PCS != '--' else PCS
        #     data.append(station_dict)

        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            futures = list()

            for station in station_data:
                futures.append(executor.submit(self.get_station_data, station, conn, project_res, station_res, station_address))
            for future in concurrent.futures.as_completed(futures):
                station_dict = future.result()
                data.append(station_dict)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": sorted(data, key=lambda x: x['efficiency'], reverse=True),
                },
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )


class StationStatusView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    """
    电站状态统计
    """

    def get_station_status(self, station):
        slave_station = station.stationdetails_set.filter(is_delete=0).all()
        slave_sid = [sid.id for sid in slave_station]
        # 查询站的状态

        status_data = models.StationStatus.objects.filter(
            station__id__in=slave_sid).aggregate(Max('status'))
        if status_data.get('status__max'):
            return status_data.get('status__max')
        else:
            return 0

    def get(self, request):
        user_id = request.user['user_id']
        name = request.query_params.get("name")
        # 查询该用户下所有站(模糊匹配站名)
        station_obj = models.MaterStation.objects.filter(userdetails__id=user_id, is_delete=0,
                                                         name__contains=name).all()
        data = {
            '1': 0,
            '2': 0,
            '3': 0,
            '4': 0,
        }
        master_ids = [i.id for i in station_obj]
        if len(master_ids) > 1:
            pass
        else:
            master_ids.extend([-1, -2])  # 兼容查询数据空的情况
        master_ids = tuple(master_ids)
        res = models.StationStatus.objects.raw(f"""SELECT
                                            bwd.master_station_id as id,
                                            max( zt.`status` )  as status_max
                                        FROM
                                            t_station_status AS zt
                                            INNER JOIN t_stations bwd ON bwd.id = zt.station_id 
                                        WHERE
                                            bwd.is_delete = 0 and bwd.master_station_id in {master_ids}
                                        GROUP BY
                                            bwd.master_station_id""")
        for i in res:
            status = i.status_max
            if status == 0:
                continue
            elif status == 5:
                data['4'] += 1
            else:
                data[str(status)] += 1


        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                'count': station_obj.count()
            }
        )


class StationSignboardView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        id = request.query_params.get('id')
        request_id = request.user["user_id"]
        if not id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：字段校验失败！',
                    }
                }
            )
        try:
            station = models.MaterStation.objects.filter(is_delete=0, id=id, userdetails__id=request_id).first()
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )

        rated_power_all = []
        rated_capacity_all = []
        today = datetime.date.today()
        # slave_station = station.stationdetails_set.filter(~Q(slave=0), ~Q(pack=0)).all()
        slave_stations = station.stationdetails_set.filter(is_delete=0).all()

        station_ids = []
        station_name = [-1]  # 防止查询异常
        for i in slave_stations:
            station_ids.append(i.id)
            station_name.append(i.english_name)

        # 维护当前主站下所有故障信息，避免循环查询
        fault_res = models.FaultAlarm.objects.using('alarm_module').filter(Q(point='Fault') | Q(point='GFault'),
                                                                           station_id__in=station_ids, type=1).values(
            'start_time', 'end_time', 'device', 'station_id'
        )
        fault_dict = {}
        for i in fault_res:
            if fault_dict.get(i['station_id']):
                fault_dict[i['station_id']].append(i)
            else:
                fault_dict[i['station_id']] = [i]


        disg = 0  # 日放电
        chag = 0  # 日充电
        bpde = 0  # 可放电量
        nbsc = 0  # 电池累计循环次数
        pat_list = []  # 环境温度
        # chag_eff = 0  # 充电效率
        # disg_eff = 0  # 放电效率
        soc_list = []
        power = 0  # 储能功率
        pcc = 0  # 负荷功率
        discharge_status = 0  # 充放电状态， 默认静置
        # Redis
        conn = get_redis_connection('3')

        # 并网点所在周期时常 单位分钟
        duration = 0
        now_time = datetime.datetime.now()
        # 故障容量
        fault_capacity = 0
        # 故障时常 单位分钟
        fault_duration = 0
        fault_duration_pcs = 0
        fault_duration_bms = 0
        master_station_BCHCap_list = []  # 累计充电量
        master_station_BDHcap_list = []  # 累积放电量
        # 电表位置：1：前置；2：后置
        meter_position = 1
        station_ems = station.english_name

        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', station_ems, "EMS"))
        ems = eval(eval(ems)) if ems else {}
        if ems.get('PCC') and ems.get('PCC') not in EMPTY_STR_LIST:
            pcc = float(ems.get('PCC'))
        else:
            pcc = '--'
        with connections['doris_ads_rhyc'].cursor() as ads_cursor:
            # 切换查询IDC库
            discharg_sql = """
                             SELECT
                                 sum( v_chag ),
                                 sum( v_disg ),
                                 station
                             FROM
                                 ads_report_chag_disg_union_1d
                             WHERE
                                 station in {}
                                 AND day = '{}'
                                 AND station_type <= 1 GROUP BY station""".format(tuple(station_name), today)

            # 获取查询结果
            try:
                ads_cursor.execute(discharg_sql)
                res = ads_cursor.fetchall()
            except Exception as e:
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )
        electric_quantity = {}
        for i in res:
            electric_quantity[i[2]] = [i[0], i[1]]

        # 处理主从模式
        for s_info in slave_stations:
            # 判断前置后置电表
            meter_position = s_info.meter_position
            # 计算主站总功率和容量
            rated_power_all.append(float(s_info.rated_power))
            rated_capacity_all.append(float(s_info.rated_capacity))

            duration = (now_time - s_info.project.in_time).total_seconds() if (now_time - s_info.project.in_time).total_seconds() > duration else duration

            # 故障容量
            pcs_fault_list = []
            bms_fault_list = []
            if fault_dict.get(s_info.id):
                for info in fault_dict[s_info.id]:
                    if "PCS" in info.get('device'):
                        pcs_fault_list.append(info.get('device'))
                        # 累加故障间隔时间 秒
                        fault_duration_pcs += (info.get('end_time') - info.get('start_time')).total_seconds() if info.get(
                            'end_time') else (now_time - info.get('start_time')).total_seconds()
                    else:
                        bms_fault_list.append(info.get('device'))
                        # 累加故障间隔时间 秒
                        fault_duration_bms += (info.get('end_time') - info.get('start_time')).total_seconds() if info.get(
                            'end_time') else (now_time - info.get('start_time')).total_seconds()

            fault_capacity += len(set(pcs_fault_list)) * 232.96 if pcs_fault_list else len(set(bms_fault_list)) * 232.96

            ele_res = electric_quantity.get(s_info.english_name)
            if ele_res:
                disg += float(ele_res[1]) if ele_res[1] else 0
                chag += float(ele_res[0]) if ele_res[0] else 0

            slave_station_meter_ins = METER_DIC[s_info.meter_count]
            units = s_info.unit_set.filter(is_delete=0).all()
            slave_station_num = 0
            for unit in units:

                if len(units) > 1:
                    slave_station_num += 1
                else:
                    slave_station_num = ""
                pcs = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', s_info.english_name, unit.pcs))
                bms = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', s_info.english_name, unit.bms))
                if pcs:
                    pcs = eval(eval(pcs))
                else:
                    pcs = {}
                    discharge_status = -1
                bms = eval(eval(bms)) if bms else {}
                if pcs.get('BPDE') and pcs.get('BPDE') not in EMPTY_STR_LIST and bpde != '--':
                    bpde += float(pcs.get('BPDE'))
                else:
                    bpde = '--'
                p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
                if p != '--':
                    if p > 1:
                        discharge_status = 2
                    elif p < -1:
                        discharge_status = 1
                    else:
                        discharge_status = 0 if discharge_status == 0 else discharge_status
                else:
                    discharge_status = -1
                if p not in EMPTY_STR_LIST and power != '--':
                    power += p
                else:
                    power = '--'
                pat_list.append(float(bms.get('BAT')) if bms.get('BAT') and bms.get('BAT') not in EMPTY_STR_LIST else '--')

                # if bms.get('NBSC') is not None and nbsc != '--':
                #     nbsc += float(bms.get('NBSC'))
                # else:
                #     nbsc = '--'

                soc_list.append(float(bms.get('SOC')) if bms.get('SOC') and bms.get('SOC') not in EMPTY_STR_LIST else '--')

                # if s_info.english_name == "NBLS001":  # 德创单独计算日充放电量
                #     chag += float(bms.get("ChaED", 0))  # 今日充电量
                #     disg += float(bms.get("DisED", 0))  # 今日放电量

                key5 = "Business:TRANS_TIMING_{}_{}_{}".format('CUMULANT', s_info.english_name,
                                                               f'{slave_station_meter_ins["device"].upper()}{slave_station_num}')
                cumulant_ = conn.get(key5)
                if cumulant_:
                    cumulant_dict = json.loads(json.loads(cumulant_.decode("utf-8")))
                else:
                    cumulant_dict = {}
                BCHCap = abs(decimal.Decimal(
                    cumulant_dict.get(slave_station_meter_ins["charge"]))) if cumulant_dict.get(slave_station_meter_ins["charge"]) and cumulant_dict.get(slave_station_meter_ins["charge"]) not in EMPTY_STR_LIST else '--' # 累计充电量
                BDHcap = abs(decimal.Decimal(
                    cumulant_dict.get(slave_station_meter_ins["discharge"]))) if cumulant_dict.get(slave_station_meter_ins["discharge"]) and cumulant_dict.get(slave_station_meter_ins["discharge"]) not in EMPTY_STR_LIST else '--'  # 累计放电量
                # 取电表初始值
                if (
                        s_info.english_name in OriginValuesDict.keys() and unit.english_name in
                        OriginValuesDict[s_info.english_name].keys()):
                    origin_value_dict = OriginValuesDict.get(s_info.english_name).get(
                        unit.english_name)
                    charge_key = METER_DIC.get(s_info.meter_count).get('charge')
                    origin_charge = origin_value_dict.get(s_info.meter_count).get(
                        charge_key)
                    discharge_key = METER_DIC.get(s_info.meter_count).get('discharge')
                    origin_discharge = origin_value_dict.get(s_info.meter_count).get(
                        discharge_key)
                else:
                    origin_charge = 0
                    origin_discharge = 0
                if s_info.english_name == "NBLS002" and s_info.meter_count == 3 and BCHCap != '--' and BDHcap != '--':
                    BCHCap += 21007
                    BDHcap += 19108
                master_station_BCHCap_list.append(
                    float(decimal.Decimal(BCHCap)) - abs(origin_charge) if BCHCap != '--' else BDHcap)
                master_station_BDHcap_list.append(
                    float(decimal.Decimal(BDHcap)) - abs(origin_discharge) if BDHcap != '--' else BDHcap)


        # 计算主站当前状态
        status = models.StationDetails.objects.values('master_station_id').filter(master_station_id=id).annotate(
            max_status=Max('stationstatus__status'))[0]
        # discharge_efficiency = round(disg_eff / chag_eff * 100, 2) if chag_eff != 0 else 0  # 充放电效率

        # 转换时间为分钟
        fault_duration = fault_duration_pcs if fault_duration_pcs > 0 else fault_duration_bms
        fault_duration = fault_duration / 60
        duration = duration / 60
        # 计算投运率
        operational_rate = round(100 - fault_capacity * fault_duration / (sum(rated_capacity_all) * duration) * 100, 2)
        # 计算多个单元的边界值



        rated_capacity = (unit_convert(sum(rated_capacity_all), 'kWh'))
        if '--' not in master_station_BCHCap_list and '--' not in master_station_BDHcap_list:
            count = ((Decimal(sum(master_station_BCHCap_list)) * Decimal(sum(master_station_BDHcap_list))) ** Decimal(0.5)) / Decimal(sum(rated_capacity_all))
            count = Decimal(count).quantize(Decimal("0"))
        else:
            count = '--'


        if pcc != '--' and power != '--':
            gridSide_power = round(pcc - power, 2) if meter_position == 2 else round(pcc, 2),  # 电网侧功率
            pcc = round(pcc + power, 2) if meter_position == 1 else round(pcc, 2)
        else:
            gridSide_power = '--'
        data = {
            "days": (datetime.datetime.now() - station.project.in_time).days,  # 运行天数
            "rated_power": (unit_convert(sum(rated_power_all), 'kW')),  # 额定功率
            "rated_capacity": rated_capacity,  # 额定容量
            "station_status": status.get('max_status'),  # 运行状态
            "disg": (unit_convert(round(disg, 2), 'kWh')),
            "chag": (unit_convert(round(chag, 2), 'kWh')),
            "bpde": round(bpde, 2) if bpde != '--' else bpde,
            "nbsc": count,
            "power": round(power, 2) if power != '--' else power,
            "pcc": pcc,
            "pat": round(sum(pat_list) / len(pat_list), 2) if '--' not in pat_list else '--',
            "soc": round(sum(soc_list) / len(soc_list), 2) if '--' not in soc_list else '--',
            "discharge_efficiency": station.efficiency,
            "gridSide_power": gridSide_power,  # 电网侧功率
            "discharge_status": discharge_status,  # 充放电状态
            "operational_rate": operational_rate if operational_rate > 0 else 0
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationChartView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def __init__(self):
        self.connection = pymysql.connect(**{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        }
        )

    def post(self, request):
        id = request.data.get('id')
        inquire_time = request.data.get('inquire_time')  # 查询日期
        types = request.data.get('type', '0')  # 0：功率；1：SOC；2：电压；3：电流
        request_id = request.user["user_id"]
        if not id or not inquire_time:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：字段校验失败！',
                    }
                }
            )
        try:
            staiton = models.MaterStation.objects.filter(is_delete=0, id=id, userdetails__id=request_id).first()
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )
        # 连接清洗库
        connection = self.connection
        cursor = connection.cursor()
        if types == '0':
            slave_station = staiton.stationdetails_set.filter(is_delete=0, english_name=staiton.english_name).all()
        else:
            slave_station = staiton.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).all()
        bms_unit_list = []
        pcs_unit_list = []
        station_list = []
        staiton_frontloaded_list = []  # 电表前置列表
        staiton_frontloaded_pcs_list = []  # 电表前置pcs列表
        # 处理主从模式
        for s_info in slave_station:
            if s_info.meter_position == 1:
                staiton_frontloaded_list.append(s_info.english_name)
                units = s_info.unit_set.filter(is_delete=0).values('pcs')
                for unit in units:
                    staiton_frontloaded_pcs_list.append(unit.get('pcs'))
            station_list.append(s_info.english_name)
            # 处理所有单元
            units = s_info.unit_set.filter(is_delete=0).values('bms', 'pcs')
            for unit in units:
                bms_unit_list.append(unit.get('bms'))
                pcs_unit_list.append(unit.get('pcs'))

        # 处理开始结束时间
        s_time = inquire_time + ' 00:00:00'
        e_time = inquire_time + ' 23:59:59'

        # 时间节点，防止某时段数据为空不返回
        # SOC
        if types == '1':
            sql = """SELECT  device, soc, DATE_FORMAT(time,'%H:%i') as time
                              FROM dwd_measure_bms_data_storage_3
                              WHERE 
                              time 
                              BETWEEN '{}'
                              AND '{}'
                              AND MOD(MINUTE(time), 15) = 0 """.format(s_time, e_time)
            if len(station_list) == 1:
                sql += "AND station_name = '{}'".format(station_list[0])
            else:
                sql += "AND station_name in {}".format(tuple(station_list))
            if len(bms_unit_list) == 1:
                sql += "AND device = '{}'".format(bms_unit_list[0])
            else:
                sql += 'AND device in {}'.format(tuple(bms_unit_list))
            sql += 'ORDER BY time'

            try:
                # 获取查询结果
                cursor.execute(sql)
                result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )
            soc_res = {}
            for i in result:
                t = i.get('time')
                soc = i.get('soc')
                if soc_res.get(t):
                    soc_res[t].append(soc)
                else:
                    soc_res[t] = [soc]

            data = {}
            bms_count = len(bms_unit_list)
            index = 0

            for k, v in soc_res.items():
                if len(v) == bms_count:
                    if None not in v:
                        data[k] = round(sum(v) / len(v), 2)
                index += 1


        # 功率
        # elif types == '0':
        #     pcc_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(pcc) as pcc
        #                       FROM dwd_measure_ems_data_storage
        #                       WHERE
        #                       time
        #                       BETWEEN '{}'
        #                       AND '{}'""".format(s_time, e_time)
        #
        #     p_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(p) as p
        #                       FROM dwd_measure_pcs_data_storage
        #                       WHERE
        #                       time
        #                       BETWEEN '{}'
        #                       AND '{}'""".format(s_time, e_time)
        #
        #     required_power_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(mfixdemandref) as mfixdemandref
        #                       FROM dwd_measure_ems_data_storage
        #                       WHERE
        #                       time
        #                       BETWEEN '{}'
        #                       AND '{}'""".format(s_time, e_time)
        #
        #     transformer_capacity_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(tfm) as tfm
        #                       FROM dwd_measure_ems_data_storage
        #                       WHERE
        #                       time
        #                       BETWEEN '{}'
        #                       AND '{}'""".format(s_time, e_time)
        #
        #     tprt_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(tprt) as tprt
        #                       FROM dwd_measure_ems_data_storage
        #                       WHERE
        #                       time
        #                       BETWEEN '{}'
        #                       AND '{}'""".format(s_time, e_time)
        #
        #     if len(station_list) == 1:
        #         pcc_sql += "AND station_name = '{}'".format(station_list[0])
        #         p_sql += "AND station_name = '{}'".format(station_list[0])
        #         required_power_sql += "AND station_name = '{}'".format(station_list[0])
        #         transformer_capacity_sql += "AND station_name = '{}'".format(station_list[0])
        #         tprt_sql += "AND station_name = '{}'".format(station_list[0])
        #     else:
        #         pcc_sql += "AND station_name in {}".format(tuple(station_list))
        #         p_sql += "AND station_name in {}".format(tuple(station_list))
        #         required_power_sql += "AND station_name in {}".format(tuple(station_list))
        #         transformer_capacity_sql += "AND station_name in {}".format(tuple(station_list))
        #         tprt_sql += "AND station_name in {}".format(tuple(station_list))
        #
        #     if len(pcs_unit_list) == 1:
        #         p_sql += "AND device = '{}'".format(pcs_unit_list[0])
        #     else:
        #         p_sql += 'AND device in {}'.format(tuple(pcs_unit_list))
        #
        #     pcc_sql += "GROUP BY t ORDER BY t"
        #     p_sql += "GROUP BY t ORDER BY t"
        #     required_power_sql += "GROUP BY t ORDER BY t"
        #     transformer_capacity_sql += "GROUP BY t ORDER BY t"
        #     tprt_sql += "GROUP BY t ORDER BY t"
        #
        #     data = {
        #         'pcc': {},  # 负荷功率
        #         'p': {},  # 储能功率
        #         'required_power': {},  # 需量功率
        #         'transformer_capacity': {},  # 变压器容量
        #         'transformer_safety_capacity': {},  # 变压器安全容量
        #         'grid_side_power': {},  # 电网侧功率
        #     }
        #
        #     try:
        #         # 获取查询结果
        #         cursor.execute(pcc_sql)
        #         pcc_result = cursor.fetchall()
        #         cursor.execute(p_sql)
        #         p_result = cursor.fetchall()
        #         cursor.execute(required_power_sql)
        #         required_power_result = cursor.fetchall()
        #         cursor.execute(transformer_capacity_sql)
        #         transformer_capacity_result = cursor.fetchall()
        #         cursor.execute(tprt_sql)
        #         tprt_sql_result = cursor.fetchall()
        #     except Exception as e:
        #         cursor.close()
        #         connection.close()
        #         error_log.error(e)
        #         return Response(
        #             {
        #                 "code": common_response_code.SUMMARY_CODE,
        #                 "data": {
        #                     "message": "error",
        #                     "detail": '查询失败！',
        #                 }
        #             }
        #         )
        #
        #     pcc_res = {res.get('t'): res.get('pcc') for res in pcc_result}
        #     p_res = {res.get('t'): res.get('p') for res in p_result}
        #     required_power_res = {res.get('t'): res.get('mfixdemandref') for res in required_power_result}
        #     transformer_capacity_res = {res.get('t'): res.get('tfm') for res in transformer_capacity_result}
        #     tprt_sql_res = {res.get('t'): res.get('tprt') for res in tprt_sql_result}
        #
        #     for i, k in enumerate(time_dict.keys()):
        #         if pcc_res.get(k):
        #             data['pcc'][k] = round(pcc_res.get(k), 2)
        #         else:
        #             data['pcc'][k] = None
        #
        #         if p_res.get(k):
        #             data['p'][k] = round(p_res.get(k), 2)
        #         else:
        #             data['p'][k] = None
        #
        #         if required_power_res.get(k):
        #             data['required_power'][k] = round(required_power_res.get(k), 2)
        #         else:
        #             data['required_power'][k] = None
        #
        #         if transformer_capacity_res.get(k):
        #             data['transformer_capacity'][k] = round(transformer_capacity_res.get(k), 2)
        #         else:
        #             data['transformer_capacity'][k] = None
        #
        #         if tprt_sql_res.get(k):
        #             data['transformer_safety_capacity'][k] = data['transformer_capacity'][k] * round(
        #                 tprt_sql_res.get(k), 2)
        #         else:
        #             data['transformer_safety_capacity'][k] = None
        #
        #     #  电表前置单独处理
        #     f_p_sql_res = {}  # 默认无电表前置不计算储能系统功率
        #     if staiton_frontloaded_list:
        #         f_p_sql = """SELECT concat(DATE_FORMAT(time,'%H'), ':', FLOOR(DATE_FORMAT(time,'%i')/15)*15) as t, sum(p) as p
        #                           FROM dwd_measure_pcs_data_storage
        #                           WHERE
        #                           time
        #                           BETWEEN '{}'
        #                           AND '{}'""".format(s_time, e_time)
        #
        #         if len(staiton_frontloaded_list) == 1:
        #             f_p_sql += "AND station_name = '{}'".format(staiton_frontloaded_list[0])
        #         else:
        #             f_p_sql += "AND station_name in {}".format(tuple(staiton_frontloaded_list))
        #         if len(staiton_frontloaded_pcs_list) == 1:
        #             f_p_sql += "AND device = '{}'".format(staiton_frontloaded_pcs_list[0])
        #         else:
        #             f_p_sql += "AND device in {}".format(tuple(staiton_frontloaded_pcs_list))
        #         f_p_sql += "GROUP BY t ORDER BY t"
        #         try:
        #             # 获取查询结果
        #             cursor.execute(f_p_sql)
        #             f_p_sql_result = cursor.fetchall()
        #         except Exception as e:
        #             cursor.close()
        #             connection.close()
        #             error_log.error(e)
        #             return Response(
        #                 {
        #                     "code": common_response_code.SUMMARY_CODE,
        #                     "data": {
        #                         "message": "error",
        #                         "detail": '查询失败！',
        #                     }
        #                 }
        #             )
        #         f_p_sql_res = {res.get('t'): res.get('p') for res in f_p_sql_result}
        #     for i, k in enumerate(time_dict.keys()):
        #         i = str(i)
        #         if f_p_sql_res.get(k):
        #             data['grid_side_power'][k] = data['pcc'][k] - round(
        #                 f_p_sql_res.get(k), 2)
        #         else:
        #             data['grid_side_power'][k] = data['pcc'][k]
        # 功率 ADS数仓版
        elif types == '0':
            with connections['doris_ads_rhyc'].cursor() as ads_cursor:
                p_sql = """SELECT
                                DATE_FORMAT(time,'%H:%i') as time, p_load, p, p_gird
                            FROM
                                ads_report_loading_data
                            WHERE
                                time
                                BETWEEN '{}'
                                AND '{}'
                                AND
                                MOD(MINUTE(time), 15) = 0 
                                AND state_pcc = 0 """.format(s_time, e_time)



                dwd_sql = """SELECT
                                    mfixdemandref,
                                    tfm,
                                    tprt,
                                    device,
                                    DATE_FORMAT(time,'%H:%i') as time
                            FROM dwd_measure_ems_data_storage
                            WHERE
                                time
                                BETWEEN '{}'
                                AND '{}'
                                AND
                                MOD(MINUTE(time), 15) = 0
                                AND device = 'EMS'
                                 """.format(s_time, e_time)

                if len(station_list) == 1:
                    p_sql += "AND station = '{}'".format(station_list[0])
                    dwd_sql += "AND station_name = '{}'".format(station_list[0])
                else:
                    p_sql += "AND station in {}".format(tuple(station_list))
                    dwd_sql += "AND station_name in {}".format(tuple(station_list))

                p_sql += "ORDER BY time"
                dwd_sql += " ORDER BY time"
                try:
                    # 获取查询结果
                    ads_cursor.execute(p_sql)
                    p_result = ads_cursor.fetchall()
                    cursor.execute(dwd_sql)
                    dwd_result = cursor.fetchall()
                except Exception as e:
                    cursor.close()
                    connection.close()
                    error_log.error(e)
                    return Response(
                        {
                            "code": common_response_code.SUMMARY_CODE,
                            "data": {
                                "message": "error",
                                "detail": '查询失败！',
                            }
                        }
                    )

            data = {
                'pcc': create_time_mapping(15),  # 负荷功率
                'p': create_time_mapping(15),  # 储能功率
                'required_power': create_time_mapping(15),  # 需量功率
                'transformer_capacity': create_time_mapping(15),  # 变压器容量
                'transformer_safety_capacity': create_time_mapping(15),  # 变压器安全容量
                'grid_side_power': create_time_mapping(15),  # 电网侧功率
            }

            for i in p_result:
                t = i[0]
                if i[1] is not None:
                    if data['pcc'].get(t):
                        data['pcc'][t] = i[1]
                if i[2] is not None:
                    if data['p'].get(t):
                        data['p'][t] = i[2]
                if i[3] is not None:
                    if data['grid_side_power'].get(t):
                        data['grid_side_power'][t] = i[3]


            dwd_res = {i.get('time'): [i.get('mfixdemandref'), i.get('tfm'), i.get('tprt')] for i in dwd_result}
            for k, v in dwd_res.items():
                if v[0] is not None:
                    if data['required_power'].get(k):
                        data['required_power'][k] = v[0]
                if v[1] is not None:
                    if data['transformer_capacity'].get(k):
                        data['transformer_capacity'][k] = v[1]
                if v[2] is not None:
                    if data['transformer_safety_capacity'].get(k):
                        data['transformer_safety_capacity'][k] = v[2] * v[1]
                else:
                    if data['transformer_safety_capacity'].get(k):
                        data['transformer_safety_capacity'][k] = v[1]

            for k, v in data.items():
                if not v:
                    for t in range(24):
                        t = '0' + str(t) if t < 10 else str(t)
                        for h in range(0, 60, 15):
                            h = '0' + str(h) if h < 10 else str(h)
                            data[k][f'{t}:{h}'] = '--'


        # 电压
        elif types == '2':
            if staiton.english_name == 'HZDC101':  # HZDC101 单独处理
                pua, pub, puc = 'puab', 'pubc', 'puca'
            else:
                pua, pub, puc = 'pua', 'pub', 'puc'

            pu_sql = """SELECT 
                              {}, {}, {}, device, DATE_FORMAT(time,'%H:%i') as time
                              FROM dwd_measure_pcs_data_storage
                              WHERE 
                              time 
                              BETWEEN '{}'
                              AND '{}'
                              AND MOD(MINUTE(time), 15) = 0 """.format(pua, pub, puc, s_time, e_time)

            if len(station_list) == 1:
                pu_sql += "AND station_name = '{}'".format(station_list[0])
            else:
                pu_sql += "AND station_name in {}".format(tuple(station_list))
            if len(pcs_unit_list) == 1:
                pu_sql += "AND device = '{}'".format(pcs_unit_list[0])
            else:
                pu_sql += 'AND device in {}'.format(tuple(pcs_unit_list))
            pu_sql += ' ORDER BY time'
            try:
                # 获取查询结果
                cursor.execute(pu_sql)
                pu_result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )
            pu_res = {}
            for i in pu_result:
                t = i.get('time')
                if pu_res.get(t):
                    pu_res[t].append([i.get(pua), i.get(pub), i.get(puc)])
                else:
                    pu_res[t] = [[i.get(pua), i.get(pub), i.get(puc)]]

            data = {}
            index = 0
            pcs_count = len(pcs_unit_list)
            for k, v in pu_res.items():
                if len(v) == pcs_count:
                    if None not in v:
                        v = np.array(v).T
                        v = [0 if i is None else i for i in v]
                        data_list = []
                        for i in v:
                            i = [0 if _i is None else _i for _i in i]
                            data_list.append(i)
                        data[k] = [round(max(i), 2) for i in data_list]
                index += 1


        # 电流
        elif types == '3':

            pi_sql = """SELECT DATE_FORMAT(time,'%H:%i') as time,
                                ia, ib,  ic, device
                              FROM dwd_measure_pcs_data_storage
                              WHERE 
                              time 
                              BETWEEN '{}'
                              AND '{}'
                              AND MOD(MINUTE(time), 15) = 0 """.format(s_time, e_time)

            if len(station_list) == 1:
                pi_sql += "AND station_name = '{}'".format(station_list[0])
            else:
                pi_sql += "AND station_name in {}".format(tuple(station_list))
            if len(pcs_unit_list) == 1:
                pi_sql += "AND device = '{}'".format(pcs_unit_list[0])
            else:
                pi_sql += 'AND device in {}'.format(tuple(pcs_unit_list))
            pi_sql += ' ORDER BY time'
            try:
                # 获取查询结果
                cursor.execute(pi_sql)
                pi_result = cursor.fetchall()
            except Exception as e:
                cursor.close()
                connection.close()
                error_log.error(e)
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": "error",
                            "detail": '查询失败！',
                        }
                    }
                )
            pi_res = {}
            for i in pi_result:
                t = i.get('time')
                if pi_res.get(t):
                    pi_res[t].append([i.get('ia'), i.get('ib'), i.get('ic')])
                else:
                    pi_res[t] = [[i.get('ia'), i.get('ib'), i.get('ic')]]

            data = {}
            index = 0
            pcs_count = len(pcs_unit_list)
            for k, v in pi_res.items():
                if len(v) == pcs_count:
                    if None not in v:
                        v = np.array(v).T
                        v = [0 if i is None else i for i in v]
                        data_list = []
                        for i in v:
                            i = [0 if _i is None else _i for _i in i]
                            data_list.append(i)
                        data[k] = [round(sum(i), 2) for i in data_list]
                index += 1


        else:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": 'types参数值超过限制',
                    }
                }
            )


        cursor.close()
        connection.close()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail":  data,
                }
            }
        )


class StationUnitListView(APIView):
    """
    储能单元清单
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        station_id = request.query_params.get('id')
        request_id = request.user["user_id"]
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 8))
        if not station_id:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：字段校验失败！',
                    }
                }
            )

        try:
            staiton = models.MaterStation.objects.filter(is_delete=0, id=station_id, userdetails__id=request_id).first()
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '数据看板：该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )

        # Redis
        conn = get_redis_connection('3')

        data = []
        slave_stations = staiton.stationdetails_set.filter(is_delete=0).filter(~Q(slave=0), ~Q(pack=0)).all()
        # 查询该主站下所有主站的储能单元；slave=0不参与计算
        units_res = models.Unit.objects.filter(is_delete=0, station__in=slave_stations).values(
            'unit_new_name',
            'rated_power',
            'rated_capacity',
            'pcs',
            'bms',
            'id',
            'station__english_name',
            'station__id',
            'station__rated_power',
            'station__app',
            'english_name',
            'v_number'

        )

        # 查看主站及非主从站的就地控制状态
        conn_1 = get_redis_connection("default")
        # slave_station_new = staiton.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
        slave_station_new = staiton.stationdetails_set.filter(is_delete=0, english_name=staiton.english_name).first()
        redis_key_1 = str(
            slave_station_new.english_name + "-" + slave_station_new.app + "-" + "AEn")
        redis_AEn = conn_1.get(redis_key_1)
        if redis_AEn:
            success_log.info("项目清单:当期数据为缓存数据")
            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
        else:
            conn = get_redis_connection("3")
            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', slave_station_new.english_name, 'EMS')
            measure_ems = conn.get(key1)
            if measure_ems:
                measure_ems_str = measure_ems.decode("utf-8")
                measure_ems_dict = json.loads(json.loads(measure_ems_str))

                if measure_ems_dict.get('AEn') and measure_ems_dict.get('AEn') not in EMPTY_STR_LIST:
                    AEn = int(measure_ems_dict.get('AEn'))
                else:
                    AEn = '--'
            else:
                AEn = '--'

        station_status = {i.station_id: i.status for i in models.StationStatus.objects.filter(station__in=slave_stations)}

        units = paging(page, size, units_res)
        for unit in units.get('data'):
            unit['rated_power'] = unit_convert(float(unit.get('rated_power')), 'kW'),  # 额定功率
            unit['rated_capacity'] = unit_convert(float(unit.get('rated_capacity')), 'kWh'),  # 额定容量
            bms_status = conn.get(
                'Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', unit.get('station__english_name'), unit.get('bms')))
            pcs_status = conn.get(
                'Business:TRANS_TIMING_{}_{}_{}'.format('STATUS', unit.get('station__english_name'), unit.get('pcs')))
            # 判断单元运行状态 1：正常；3：故障；4：离线；5：通讯异常
            if station_status.get(unit.get('station__id')) == 4:
                unit['status'] = 4
            else:
                if not bms_status or not pcs_status or len(eval(eval(bms_status)).keys())<4 or len(eval(eval(pcs_status)).keys())<4:
                    unit['status'] = 5
                else:
                    if int(eval(eval(bms_status)).get('GFault', -2)) == 1 or int(
                            eval(eval(pcs_status)).get('Fault', -2)) == 1:
                        unit['status'] = 3
                    else:
                        unit['status'] = 1

            # 判断单元充放电状态 -1：离线；1：充电；0：静置；2：放电
            pcs = conn.get(
                'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.get('station__english_name'), unit.get('pcs')))
            if not pcs:
                unit['discharge_status'] = -1
                p = '--'
            else:
                pcs = eval(eval(pcs))
                p = float(pcs.get('P')) if pcs.get('P') and pcs.get('P') not in EMPTY_STR_LIST else '--'
                # 储能单元边界值
                if p != '--':
                    if p >= 1:
                        unit['discharge_status'] = 2
                    elif p <= -1:
                        unit['discharge_status'] = 1
                    else:
                        unit['discharge_status'] = 0
                else:
                    unit['discharge_status'] = -1
            # 实时功率
            unit['power'] = p

            bms = conn.get(
                "Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', unit.get('station__english_name'), unit.get('bms')))
            bms = eval(eval(bms)) if bms else {}
            # SOC
            unit['soc'] = float(bms.get("SOC")) if bms.get("SOC") and bms.get("SOC") not in EMPTY_STR_LIST else '--'
            # 电池温度
            unit['temperature'] = float(bms.get("AveT")) if bms.get("AveT") and bms.get("AveT") not in EMPTY_STR_LIST else '--'
            # 冷却液压力
            unit['ipv'] = float(bms.get("IPV")) if bms.get("IPV") and bms.get("IPV") not in EMPTY_STR_LIST else '--'
            # 单元运行状态

            redis_key = str(unit.get('station__english_name') + "_" + unit.get('station__app') + "_" + unit[
                "pcs"] + "_" + "EFPR")
            redis_EFPR = conn_1.get(redis_key)

            if redis_EFPR:
                unit['unit_status'] = redis_EFPR.decode("utf-8") if redis_EFPR else 0  # 0开启；1关闭 PCS开关
            else:
                if pcs_status:
                    # PCS开关
                    unit['unit_status'] = eval(eval(pcs_status)).get('PCStu') if (eval(eval(pcs_status)).get('PCStu')
                                                                                  and eval(eval(pcs_status)).get('PCStu') not in EMPTY_STR_LIST) else 0
                else:
                    unit['unit_status'] = 0



            redis_key_ = str(unit.get('station__english_name') + "_" + unit.get('station__app') + "_" + unit[
                "pcs"] + "_" + "NSIMO")
            redis_NSIMO = conn_1.get(redis_key_)

            if redis_NSIMO:
                success_log.info("储能单元清单:当前数据为缓存数据")
                unit['running_type'] = redis_NSIMO.decode("utf-8")  # 运行状态
            else:
                if pcs_status:
                    unit['running_type'] = eval(eval(pcs_status)).get('ASTIMN')
                else:
                    unit['running_type'] = 0



            redis_key_ = str(unit.get('station__english_name') + "-" + unit.get('station__app') + "-" + "AEn")
            redis_AEn = conn_1.get(redis_key_)
            if redis_AEn:
                success_log.info("项目清单:当期数据为缓存数据")
                unit_AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
            else:
                conn = get_redis_connection("3")
                key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', unit.get('station__english_name'),
                                                               'EMS')
                measure_ems_1 = conn.get(key2)
                if measure_ems_1:
                    measure_ems_str_1 = measure_ems_1.decode("utf-8")
                    measure_ems_dict_1 = json.loads(json.loads(measure_ems_str_1))

                    unit_AEn = int(measure_ems_dict_1.get('AEn')) \
                        if measure_ems_dict_1.get('AEn') and measure_ems_dict_1.get('AEn') not in EMPTY_STR_LIST else '--'
                else:
                    unit_AEn = 0
            unit['control_strategy'] = unit_AEn
            unit['down_limit'] = float(unit.get('station__rated_power'))
            unit['up_limit'] = -float(unit.get('station__rated_power'))
            unit['unit_status'] = 1 if int(unit['unit_status']) == 0 else 0  # 反转
            data.append(unit)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                "total": units.get('total'),
                "totalpage": units.get('totalpage'),
                "page": units.get('page'),
                "stations_control_strategy": AEn
            }
        )


class ResetFaultSendSmsCodeView(APIView):
    """故障复位下发发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("故障复位下发发送短信:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("故障复位下发发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("reset" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class ResetFaultSMSCodeCheckView(APIView):
    """故障复位下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.ResetFaultSMSCodeCheckSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error(f"故障复位下发:参数校验不通过{ser.errors}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("reset" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("故障复位下发:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )

        try:
            unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"储能单元不存在！",
                    },
                }
            )
        topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        pcs = unit.pcs
        token = aes.encrypt(unit.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{"REFau": "1", "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"故障复位下发:下发指令{json_message}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "故障复位下发指令成功",
                },
            }
        )


class UnitPowerSMSCodeCheckView(APIView):
    """pcs功率下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.UnitPowerCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("pcs功率下发:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )

        conn = get_redis_connection("default")
        conn.delete("unit_power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("pcs功率下发:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )

        power = ser.validated_data["power"]

        unit = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        pcs = unit.pcs
        topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(unit.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{"APS": str(power * 10), "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"pcs功率下发:下发参数{json_message}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略下发成功",
                },
            }
        )


class UnitPowerSendSmsCodeView(APIView):
    """pcs功率发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("pcs功率发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("pcs功率发送短信:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("unit_power" + str(ser.validated_data["mobile"]), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class UnitSwitchSendSmsCodeView(APIView):
    """PCS开关发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("PCS开关发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("PCS开关发送短信:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set(
            "unit_switch" + str(ser.validated_data["mobile"]),
            random_sms_code,
            ex=60 * 5,
        )  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class UnitSwitchSMSCodeCheckView(APIView):
    """PCS开关下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = monitor_serializers.UnitSwitchCheckSMSCodeSerializer(data=request.data, context={"lang": lang})
        try:
            if not ser.is_valid():
                error_log.error("PCS开关下发指令:字段校验不通过")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "error",
                        "detail": e.args[0],
                    }
                }
            )
        conn = get_redis_connection("default")
        conn.delete("unit_switch" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("PCS开关下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )
        switch_dic = {
            1: "CDSta",  # 启动
            0: "CDSto",  # 停止
        }
        switch = ser.validated_data["switch"]
        switch = switch_dic.get(int(switch))

        unit_ins = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        topic = f"req/database/parameter/{unit_ins.station.english_name}/{unit_ins.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        pcs = unit_ins.pcs
        token = aes.encrypt(unit_ins.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{switch: "1", "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"PCS开关下发指令:下发指令{json_message}")
        conn = get_redis_connection("default")
        redis_key = str(unit_ins.station.english_name + "_" + unit_ins.station.app + "_" + pcs + "_" + "EFPR")
        conn.set(redis_key, str(ser.validated_data["switch"]), ex=150)
        success_log.info("PCS开关下发指令:写入缓存")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        success_log.info("PCS开关下发指令:最新数据拉取成功")
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "PCS开关下发指令成功",
                },
            }
        )


class AlarmDetailView(APIView):
    """判断是否显示故障复位"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        params = request.query_params
        if not params:
            today = datetime.date.today()
            query_ins = (
                models.FaultAlarm.objects.using('alarm_module').filter(start_time__date=today)
                    .values("status", "type", "start_time", "end_time", "details", "id", "note", "device")
                    .order_by("-start_time")
            )
        else:
            new_dic = {}
            pcs = params.get("device", None)
            if pcs:
                unit_ins = models.Unit.objects.filter(is_delete=0, english_name=pcs).first()
                if unit_ins:
                    pcs_ = unit_ins.pcs
                    bms_ = unit_ins.bms
                    ins = models.FaultAlarm.objects.using('alarm_module').filter(Q(device=pcs_) | Q(device=bms_))
                else:
                    ins = models.FaultAlarm.objects
            else:
                ins = models.FaultAlarm.objects
            for k in params.keys():
                if k == "device":
                    pass
                elif k == "station__id":
                    master_station_id = params.get("station__id")
                    master_stations = models.MaterStation.objects.filter(id=master_station_id, is_delete=0)
                    if master_stations.exists():
                        master_station = master_stations.first()
                        slave_stations = master_station.stationdetails_set.filter(is_delete=0).all()
                        ids = [s.id for s in slave_stations]
                        new_dic['station_id__in'] = ids
                else:
                    new_dic[k] = params[k]
            if new_dic.get('token'):
                del new_dic['token']
            query_ins = (
                ins.filter(**new_dic)
                    .values("status", "type", "start_time", "end_time", "details", "id", "note", "device",
                            "device_another_name")
                    .order_by("-start_time")
            )
        warn = []
        fault = []
        event = []
        for query in query_ins[:1000]:
            query["details"] = query["device_another_name"] + ":" + query["details"] if query["device_another_name"] \
                else query["device"] + ":" + query["details"]
            if query['type'] == 1:
                fault.append(query)
            elif query['type'] == 3:
                event.append(query)
            else:
                warn.append(query)
        return Response({"code": common_response_code.SUCCESS, "data": {"message": "success",
                                                                        # "detail": query_ins,
                                                                        "warn": warn,
                                                                        "fault": fault,
                                                                        "event": event,
                                                                        }})


class PricePower(APIView):
    """
    电价与功率
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication
    ]  # jwt认证

    def get(self, request):
        station_id = request.query_params.get('id')
        request_id = request.user["user_id"]
        month = request.query_params.get('month')
        try:
            master_station = models.MaterStation.objects.filter(is_delete=0, id=station_id, userdetails__id=request_id).first()
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": '该站不存在或当前用户没有权限，请检查参数！',
                    }
                }
            )
        # station_info = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
        station_info = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
        station_base_ids = models.StationBaseNew.objects.filter(station_id=station_info.id).all()
        station_base_ids = [i.former_base_id for i in station_base_ids]
        month = f"{datetime.datetime.now().year}-{month}" if int(month) >= 10 else f"{datetime.datetime.now().year}-0{month}"
        former_res = models.FormerBaseNew.objects.filter(id__in=station_base_ids, year_month=month).all()
        if not former_res:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
                }
            )
        price_res = models.PeakValleyNew.objects.filter(province=station_info.province, year_month=month,
                                                     type=station_info.type, level=station_info.level).all()
        data = {
            'power': {},
            'price': {},
            'title': station_info.province.name + '-' + ELE_DICTS.get(str(station_info.type)) + VOL_DICTS.get(
                str(station_info.level)) + '单位单价'
        }

        for i, price in enumerate(price_res[::2]):
            data['price'][price.moment] = price.price
        for i, former in enumerate(former_res[::2]):
            data['power'][former.moment] = round(former.mark * former.power_value, 2)


        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationFault(APIView):
    """跳转连接-故障"""""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user.get('user_id')
        page = int(request.query_params.get('page', 1))
        size = int(request.query_params.get('size', 10))
        name = request.query_params.get('name')
        station_ids = request.query_params.get('station_ids')
        status = request.query_params.get('status')
        query_time = request.query_params.get('query_time')
        # 只筛选故障数据

        user_stations = models.StationDetails.objects.filter(master_station__userdetails__id=user_id, is_delete=0).all()
        user_stations_ids = [s.id for s in user_stations]
        fault_res = models.FaultAlarm.objects.using('alarm_module').filter(station_id__in=user_stations_ids, type=1)
        if station_ids:
            station_ids = station_ids.split(',')
            r_stations = models.StationDetails.objects.filter(master_station__id__in=station_ids, is_delete=0).all()
            r_stations_ids = [s.id for s in r_stations]
            fault_res = fault_res.filter(station_id__in=r_stations_ids)
        if name:
            fault_res = fault_res.filter(details__contains=name)
        if status:
            fault_res = fault_res.filter(status=status)
        if query_time:
            end_time = datetime.datetime.strptime(query_time, '%Y-%m-%d') + datetime.timedelta(days=1)
            fault_res = fault_res.filter(start_time__gte=query_time, start_time__lte=end_time)
        if status:
            fault_res = fault_res.order_by('-start_time').all()
        else:
            fault_res = fault_res.order_by('end_time', '-start_time').all()

        page_res = paging(page, size, fault_res)  # 分页器
        res = page_res.get('data')
        data = []
        fault_ids = [i.station_id for i in res]
        station_res = {i.id: i.master_station.name for i in models.StationDetails.objects.filter(id__in=fault_ids).all()}
        for fault in res:
            station = station_res.get(fault.station_id)
            data.append(
                {
                    'station_name': station if station else '--',
                    'device_name': fault.device,
                    'name': fault.details,
                    'start_time': datetime.datetime.strftime(fault.start_time, '%Y-%m-%d %H:%M:%S'),
                    'end_time': datetime.datetime.strftime(fault.end_time, '%Y-%m-%d %H:%M:%S') if fault.end_time else '',
                    'id': fault.joint_primary_key,
                    'status': fault.status
                }
            )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
                "total": page_res.get('total'),
                "totalpage": page_res.get('totalpage'),
                "page": page_res.get('page'),
            }
        )


class StationFaultMapping(APIView):
    """故障字典"""""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        user_id = request.user.get('user_id')
        station = models.MaterStation.objects.filter(is_delete=0, userdetails__id=user_id).all()
        s_data = []
        for info in station:
            s_data.append(
                {
                    'id': info.id,
                    'name': info.name
                }
            )
        data = {
            'fault_status': FAULTSTATUS,
            'station': s_data

        }
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class StationFaultDetail(APIView):
    """故障详情"""""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request, pk):

        try:
            fault = models.FaultAlarm.objects.using('alarm_module').get(joint_primary_key=pk)
        except models.FaultAlarm.DoesNotExist:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {
                        "message": "error",
                        "detail": f'故障数据不存在',
                    }
                }
            )
        k = fault.device[:3]
        name = fault.details
        data = FAULTANALYSISSOLUTION.get(k).get(name)
        if data:
            res = {
                'device_name': fault.device,
                'name': name,
                'analysis': data.get('analysis'),
                'solution': data.get('solution'),
            }
        else:
            res = {
                'device_name': fault.device,
                'name': name,
                'analysis': '',
                'solution': '',
            }
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": res,
                }
            }
        )


class StationNoticeView(APIView):
    """跳转连接-维护须知"""""
    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def get(self, request):
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": MAINTENANCEINSTRUCTIONS,
                }
            }
        )


class ControlListView(APIView):
    """就地控制列表"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        ser = monitor_serializers.StationSerializer(data=request.query_params)
        if not ser.is_valid():
            error_log.error("就地控制列表:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        stations = models.StationDetails.objects.filter(is_delete=0, master_station__english_name=ser.validated_data['station']).all()

        data = {
            'is_master': 1 if stations.count() > 1 else 0,
            'slave_control_strategys': []
        }
        conn = get_redis_connection("3")
        conn_ = get_redis_connection("default")

        master_station = models.MaterStation.objects.filter(is_delete=0, english_name=ser.validated_data['station']).first()
        m_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
        # 查看主站及非主从站的就地控制状态
        redis_key_1 = str(
            m_station.english_name + "-" + m_station.app + "-" + "AEn")
        redis_AEn = conn_.get(redis_key_1)
        if redis_AEn:
            success_log.info("项目清单:当期数据为缓存数据")
            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
        else:
            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', m_station.english_name, 'EMS')
            measure_ems = conn.get(key1)
            if measure_ems:
                measure_ems_str = measure_ems.decode("utf-8")
                measure_ems_dict = json.loads(json.loads(measure_ems_str))

                if measure_ems_dict.get('AEn') and measure_ems_dict.get('AEn') not in EMPTY_STR_LIST:
                    AEn = int(measure_ems_dict.get('AEn'))
                else:
                    AEn = '--'
            else:
                AEn = '--'

        data['stations__control_strategy'] = int(AEn) if AEn != '--' else AEn
        for station in stations:
            # if station.slave == 0 or station.slave == -1:
            #     # 查看主站及非主从站的就地控制状态
            #     redis_key_1 = str(
            #         station.english_name + "-" + station.app + "-" + "AEn")
            #     redis_AEn = conn_.get(redis_key_1)
            #     if redis_AEn:
            #         success_log.info("项目清单:当期数据为缓存数据")
            #         AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
            #     else:
            #         key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name, 'EMS')
            #         measure_ems = conn.get(key1)
            #         if measure_ems:
            #             measure_ems_str = measure_ems.decode("utf-8")
            #             measure_ems_dict = json.loads(json.loads(measure_ems_str))
            #
            #             if measure_ems_dict.get('AEn') is not None:
            #                 AEn = int(measure_ems_dict.get('AEn'))
            #             else:
            #                 AEn = '--'
            #         else:
            #             AEn = '--'
            #
            #     data['stations__control_strategy'] = int(AEn) if AEn != '--' else AEn
            if station.slave != -1 or station.pack != -1:
                units = station.unit_set.filter(is_delete=0).all()
                for unit in units:
                    key3 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                                   unit.pcs)
                    status_pcs = conn.get(key3)
                    if status_pcs:
                        status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
                    else:
                        status_pcs_dict = {}

                    if not status_pcs_dict:
                        redis_key_ = str(
                            station.english_name + "-" + station.app + "-" + "AEn")
                        redis_AEn = conn_.get(redis_key_)
                        if redis_AEn:
                            success_log.info("项目清单:当期数据为缓存数据")
                            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
                        else:
                            AEn = '--'
                    else:
                        redis_key_ = str(
                            station.english_name + "-" + station.app + "-" + "AEn")
                        redis_AEn = conn_.get(redis_key_)
                        if redis_AEn:
                            success_log.info("项目清单:当期数据为缓存数据")
                            AEn = int(redis_AEn.decode("utf-8"))  # 开关状态
                        else:
                            key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                                           'EMS')
                            measure_ems_1 = conn.get(key2)
                            if measure_ems_1:
                                measure_ems_str_1 = measure_ems_1.decode("utf-8")
                                measure_ems_dict_1 = json.loads(json.loads(measure_ems_str_1))

                                AEn = measure_ems_dict_1.get('AEn') \
                                    if measure_ems_dict_1.get('AEn') and measure_ems_dict_1.get('AEn') not in EMPTY_STR_LIST else '--'
                            else:
                                AEn = '--'

                    data['slave_control_strategys'].append({"title": unit.unit_new_name + '-就地控制（从）', "id": unit.id,
                                                      "control_strategy": AEn})

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                },
            }
        )

class ControlSendSmsCodeView(APIView):
    """就地控制发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("就地控制发送短信:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("就地控制发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("control" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class ControlSMSCodeCheckView(APIView):
    """就地控制下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.ControlCheckSMSCodeSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("就地控制下发指令:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("control" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        control_strategy = ser.validated_data["control_strategy"]
        unit_id = ser.validated_data.get('unit_id', None)

        # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
        # app = app.get("app")
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        if not unit_id:
            # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
            slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
            topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(slave_station.english_name)
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    # {"ENAu": str(control_strategy), "type": "parameter"},
                    {"EAEn": str(control_strategy), "type": "parameter"}
                ],
            }
            client = mqtt.Client()
            client.on_connect = on_connect
            client.on_message = on_message
            client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
            client.connect(
                host=settings.MQTT_SERVER,
                port=settings.MQTT_PORT,
                keepalive=settings.MQTT_KEEPALIVE,
            )

            json_message = json.dumps(message)
            client.publish(topic, json_message)
            success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
            conn = get_redis_connection("default")
            redis_key = str(slave_station.english_name + "-" + slave_station.app + "-" + "AEn")
            success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
            conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)

        else:
            units = models.Unit.objects.filter(is_delete=0, id=unit_id, station__master_station__english_name=master_station_name)
            if not units.exists():
                return Response(
                    {
                        "code": common_response_code.SUMMARY_CODE,
                        "data": {
                            "message": f"单元{unit_id}不存在!",
                        },
                    }
                )
            unit = units.first()
            topic = f"req/database/parameter/{unit.station.english_name}/{unit.station.app}"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(unit.station.english_name)
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    # {"ENAu": str(control_strategy), "type": "parameter"},
                    {"EAEn": str(control_strategy), "type": "parameter"}
                ],
            }
            client = mqtt.Client()
            client.on_connect = on_connect
            client.on_message = on_message
            client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
            client.connect(
                host=settings.MQTT_SERVER,
                port=settings.MQTT_PORT,
                keepalive=settings.MQTT_KEEPALIVE,
            )

            json_message = json.dumps(message)
            client.publish(topic, json_message)
            success_log.info(f"就地控制下发指令:下发参数为{json.dumps(message)}")
            conn = get_redis_connection("default")
            redis_key = str(unit.station.english_name + "-" + unit.station.app + "-" + "AEn")
            success_log.info("就地控制下发指令:就地控制开关状态写入缓存")
            conn.set(redis_key, str(ser.validated_data["control_strategy"]), ex=150)

        # request_dic = {
        #     "app": unit.station.app if unit_id else slave_station.app,
        #     "time": str(time.time()),
        #     "station": unit.station.english_name if unit_id else slave_station.english_name,
        #     "body": [
        #         {
        #             "device": "BMS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "EMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["AEnC", "AEn", "PRun", "PStse"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "BMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #     ],
        # }
        # url = "http://172.17.6.44:9001/api/point/realtimeDataRelease"
        # time.sleep(5)
        # response = requests.post(url=url, json=request_dic)
        # code = response.json().get("code", "-1")
        # if str(code) != "200":
        #     error_log.error("就地控制下发指令:最新数据拉取失败")
        #     return Response(
        #         {
        #             "code": common_response_code.SUCCESS,
        #             "data": {
        #                 "message": "success",
        #                 "detail": "就地控制下发指令:最新数据拉取失败",
        #             },
        #         }
        #     )
        success_log.info("就地控制下发指令:最新数据拉取成功")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "操作成功！",
                },
            }
        )


class PowerSendSmsCodeView(APIView):
    """全站功率发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("全站功率发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("全站功率发送短信:短信发送失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        """目前没有接入第三方发短信程序"""
        conn = get_redis_connection("default")
        conn.set("power" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class PowerSMSCodeCheckView(APIView):
    """全站功率下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.PowerCheckSMSCodeSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("全站功率下发指令:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("power" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("全站功率下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        power = ser.validated_data["power"]
        # app = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).values("app").first()
        # app = app.get("app")

        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
        slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(slave_station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": "EMS",
            "body": [{"EFPR": str(power), "type": "parameter"}],
        }
        success_log.info(f"全站功率下发指令:下发参数为{json.dumps(message)}")
        json_message = json.dumps(message)
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        client.publish(topic, json_message)
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        # request_dic = {
        #     "app": slave_station.app,
        #     "time": str(time.time()),
        #     "station": slave_station.english_name,
        #     "body": [
        #         {
        #             "device": "BMS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm"],
        #         },
        #         {
        #             "device": "EMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["AEnC", "AEn", "PRun", "PStse"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "BMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #     ],
        # }
        # url = "http://172.17.6.44:9001/api/point/realtimeDataRelease"
        # time.sleep(10)
        # response = requests.post(url=url, json=request_dic)
        # code = response.json().get("code", "-1")
        # if str(code) != "200":
        #     error_log.error("全站功率下发指令:最新数据拉取失败")
        #     return Response(
        #         {
        #             "code": common_response_code.SUCCESS,
        #             "data": {
        #                 "message": "success",
        #                 "detail": "控制策略下发失败,最新数据拉取失败",
        #             },
        #         }
        #     )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "功率指标下发成功",
                },
            }
        )


class PowerPlanSendSmsCodeView(APIView):
    """功率计划发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("功率计划发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("功率计划发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("power_plan" + str(ser.validated_data["mobile"]), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class PowerPlanSMSCodeCheckView(APIView):
    """计划下发下发指令"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.PowerPlanCheckSMSCodeSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("计划下发下发指令:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("power_plan" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()

        if not user_instance:
            error_log.error("计划下发下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        power_plans = ser.validated_data["power_plan"]
        # station = models.StationDetails.objects.filter(english_name=storage_name, slave=-1).first()

        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()

        object_lists = []
        for plan in power_plans:
            history_instance = models.StationPlanHistory(
                user_id=request.user["user_id"],
                station=master_station,
                start_time=plan["start_time"],
                end_time=plan["end_time"],
                power_follow=plan["power_follow"],
                power=plan["power"],
            )
            object_lists.append(history_instance)
        models.StationPlanHistory.objects.bulk_create(object_lists)  # 插入数据
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "功率计划下发成功",
                },
            }
        )


class PowerPlanHistoryView(APIView):
    """计划下发历史查询"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.StationNameSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("计划下发历史查询:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        user_id = request.user["user_id"]
        master_station_name = ser.validated_data["strategy"]
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()

        history_instances = (
            models.StationPlanHistory.objects.filter(user_id=user_id,
                                                     station=master_station)
                .values("start_time", "end_time", "power_follow", "power", "status", "unique")
                .order_by("start_time")
        )
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": history_instances,
                },
            }
        )


class PowerPlanHistoryEditView(APIView):
    """计划下发历史记录修改"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.PowerPlanHistoryEditSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("计划下发历史记录修改:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        user_ins = models.UserDetails.objects.filter(id=request.user["user_id"]).first()

        master_station_name = ser.validated_data["station"]
        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()

        plan_history_ins = models.StationPlanHistory.objects.filter(
            status=1,
            station__userdetails=user_ins,
            unique=ser.validated_data["unique"],
            station=master_station,
        ).first()
        if not plan_history_ins:
            error_log.error("计划下发历史记录修改:历史记录不存在或已下发")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "历史记录不存在或已下发"},
                }
            )
        plan_history_ins.power = ser.validated_data["power"]
        plan_history_ins.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "控制策略修改成功",
                },
            }
        )


class VirtuallySendSmsCodeView(APIView):
    """需量下发发送短信"""

    # authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("需量下发发送短信:参数校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("就地控制发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set("virtually" + str(ser.validated_data["mobile"]), random_sms_code,
                 ex=60 * 5)  # redis 数据格式 monitor_mobile

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class VirtuallyCheckParamView(APIView):
    """需量下发回显"""
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证
    def get(self, request):
        name = request.query_params.get('name')
        if not name:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": '参数不完整'},
                }
            )
        try:
            master_station = models.MaterStation.objects.filter(is_delete=0, english_name=name).first()
        except models.MaterStation.DoesNotExist as e:
            return Response(
                {
                    "code": common_response_code.NO_DATA,
                    "data": {"message": "error", "detail": '查询失败，数据不存在'},
                }
            )
        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
        conn = get_redis_connection("3")
        ems = conn.get("Business:TRANS_TIMING_{}_{}_{}".format('MEASURE', master_station.english_name, 'EMS'))
        if ems:
            ems = eval(eval(ems))
        else:
            ems = {}
        MFixDemandRef = ems.get('MFixDemandRef') if ems.get('MFixDemandRef') and ems.get('MFixDemandRef') not in EMPTY_STR_LIST else 0
        TFM = ems.get('TFM') if ems.get('TFM') and ems.get('TFM') not in EMPTY_STR_LIST else 0
        TPRT = ems.get('TPRT') if ems.get('TPRT') and ems.get('TPRT') not in EMPTY_STR_LIST else 0
        Pccth = ems.get('Pccth') if ems.get('Pccth') and ems.get('Pccth') not in EMPTY_STR_LIST else 0
        data = {
            'MFixDemandRef': MFixDemandRef,  # 需量功率
            'TFM': TFM,    # 变压器容量
            'TPRT': TPRT,   # 变压器有功比例
            'Pccth': Pccth   # 防逆流阈值
        }

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": data,
                }
            }
        )


class VirtuallySMSCodeCheckView(APIView):
    """需量下发"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        lang = request.headers.get("lang", 'zh')
        ser = VirtuallyCheckSMSCodeSerializer(data=request.data, context={"lang": lang})

        try:
            if not ser.is_valid():
                error_log.error(f"需量下发:参数校验不通过{ser.errors}")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
        except Exception as e:
            error_log.error(f"需量下发:参数校验不通过{e}")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": e.args[0]},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("virtually" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("就地控制下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )
        master_station_name = ser.validated_data["strategy"]
        # app = models.StationDetails.objects.filter(english_name=storage_name).values("app").first()
        # app = app.get("app")

        master_stations = models.MaterStation.objects.filter(is_delete=0, english_name=master_station_name).all()
        if not master_stations.exists():
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": f"主站{master_station_name}不存在!",
                    },
                }
            )
        master_station = master_stations.first()

        # slave_station = master_station.stationdetails_set.filter(Q(slave=0) | Q(slave=-1)).first()
        slave_station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        topic = f"req/database/parameter/{slave_station.english_name}/{slave_station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)
        token = aes.encrypt(slave_station.english_name)
        body = []
        if request.data.get('virtually'):
            body.append({'FixDemandRef': str(request.data.get('virtually')), "type": "parameter"})
        if request.data.get('capacity'):
            body.append({'TFMC': str(request.data.get('capacity')), "type": "parameter"})
        if request.data.get('proportion'):
            if not 0 <= float(request.data.get('proportion')) <= 1:
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {
                            "message": "error",
                            "detail": "变压器有功比例超过限制范围：0-1",
                        },
                    }
                )
            body.append({'TPRTC': str(request.data.get('proportion')), "type": "parameter"})
        if request.data.get('threshold'):
            body.append({'PccthC': str(request.data.get('threshold')), "type": "parameter"})
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": "EMS",
            "body": body,
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )

        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"需量下发:下发参数为{json.dumps(message)}")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "操作成功！",
                },
            }
        )


class UnitSwitchRunningTypeSendSmsCodeView(APIView):
    """切换运行类型发送短信"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.query_params)
        if not ser.is_valid():
            error_log.error("切换运行类型发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("切换运行类型发送短信:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        conn = get_redis_connection("default")
        conn.set(
            "unit_switch_running_type" + str(ser.validated_data["mobile"]),
            random_sms_code,
            ex=60 * 5,
        )  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class UnitSwitchRunningTypeView(APIView):
    """切换运行类型下发指令"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.UnitSwitchSwitchRunningTypeSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("切换运行类型下发指令:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        conn.delete("unit_switch_running_type" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
                                                          id=request.user["user_id"]).first()
        if not user_instance:
            error_log.error("切换运行类型下发指令:手机号与当前登录用户名不符")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {
                        "message": "手机号与当前登录用户名不符",
                    },
                }
            )

        target_running_type = ser.validated_data["running_type"]

        unit_ins = models.Unit.objects.get(english_name=ser.validated_data["unit"])
        topic = f"req/database/parameter/{unit_ins.station.english_name}/{unit_ins.station.app}"
        secret_key = settings.AES_KEY
        aes = EncryptDate(secret_key)

        pcs = unit_ins.pcs
        token = aes.encrypt(unit_ins.station.english_name)
        message = {
            "time": str(int(time.time())),
            "token": token,
            "device": pcs,
            "body": [{'NSIMO': str(target_running_type), "type": "parameter"}],
        }
        client = mqtt.Client()
        client.on_connect = on_connect
        client.on_message = on_message
        client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
        client.connect(
            host=settings.MQTT_SERVER,
            port=settings.MQTT_PORT,
            keepalive=settings.MQTT_KEEPALIVE,
        )
        json_message = json.dumps(message)
        client.publish(topic, json_message)
        success_log.info(f"切换运行状态下发指令:下发指令{json_message}")
        conn = get_redis_connection("default")
        redis_key = str(unit_ins.station.english_name + "_" + unit_ins.station.app + "_" + pcs + "_" + "NSIMO")
        conn.set(redis_key, str(target_running_type), ex=150)
        success_log.info("切换运行状态下发指令:写入缓存")
        models.Record.objects.create(user=request.user["user_id"], topic=topic, message=message)

        # request_dic = {
        #     "app": unit_ins.station.app,
        #     "time": str(time.time()),
        #     "station": unit_ins.station.english_name,
        #     "body": [
        #         {
        #             "device": "BMS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["NBSC", "BQ", "ChaED", "DisED"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["ChaD", "DisD", "P", "Q"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["Fault", "alarm", "PCStu", "ASTIMN"],
        #         },
        #         {
        #             "device": "EMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["AEnC", "AEn", "PRun", "PStse"],
        #         },
        #         {
        #             "device": "PCS",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS1",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS2",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "PCS3",
        #             "datatype": "measure",
        #             "totalcall": "0",
        #             "body": ["PP1"],
        #         },
        #         {
        #             "device": "BMS",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS1",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS2",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #         {
        #             "device": "BMS3",
        #             "datatype": "status",
        #             "totalcall": "0",
        #             "body": ["SQse"],
        #         },
        #     ],
        # }
        # url = "http://172.17.6.44:9001/api/point/realtimeDataRelease"
        # if str(target_running_type) == "1":
        #     time.sleep(10)
        # else:
        #     time.sleep(3)
        # response = requests.post(url=url, json=request_dic)
        # code = response.json().get("code", "-1")
        # if str(code) != "200":
        #     error_log.error("切换运行状态下发指令:最新数据拉取失败")
        #     return Response(
        #         {
        #             "code": common_response_code.SUCCESS,
        #             "data": {
        #                 "message": "success",
        #                 "detail": "切换运行状态下发指令,最新数据拉取失败",
        #             },
        #         }
        #     )
        success_log.info("切换运行状态下发指令:最新数据拉取成功")
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "切换运行状态下发指令成功",
                },
            }
        )


class UserStrategyView(APIView):
    """新.用户自动模式配置：添加/修改/查询列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def post(self, request):
        """
        添加新用户策略
        """""
        # try:
        ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
        if not ser.is_valid():
            error_log.error(f"用户自动模式配置添加: 字段校验失败{ser.errors}")

            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        strategy = ser.save()
        e = strategy.id
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "用户自动模式配置: 添加成功",
                    "strategy_id": e,
                },
            }
        )
        # except Exception as e:
        #     error_log.error("用户自动模式配置: 添加报错：{}".format(e.args))
        #     return Response(
        #         {
        #             "code": common_response_code.ERROR,
        #             "data": {"message": "fail", "detail": '用户自动模式配置：添加失败!'},
        #         }
        #     )

    def get(self, request):
        """
        获取用户所具有的策略
        """""
        try:
            query = request.query_params.get("query", None)
            station = request.query_params.get("station", None)

            user_id = request.user["user_id"]
            user_ins = models.UserDetails.objects.get(id=user_id)
            details = UserStrategy.objects.filter(
                user=user_ins,
                is_delete=0
            )

            if query:
                details = details.filter(
                    name__contains=query
                )

            for detail in details:
                months_count = detail.month_set.filter(is_valid=0).count()
                detail = detail.__dict__
                detail['status'] = 0
                if months_count == 12:
                    detail['is_show'] = 1
                else:
                    detail['is_show'] = 0
            if station:
                # station = models.StationDetails.objects.filter((Q(slave=-1) | Q(slave=0)), master_station__id=station).first()
                master_station = models.MaterStation.objects.filter(is_delete=0, id=station).first()
                station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
                record = models.Record.objects.filter(user=user_id, station=station).order_by('-id').first()
                if record:
                    for detail in details:
                        detail = detail.__dict__
                        if record.strategy and detail.get('id') == record.strategy.id:
                            detail['status'] = 1
                        else:
                            detail['status'] = 0

            ser = ResponseUserStrategySerializer(instance=details, many=True)

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": ser.data},
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置: 查询报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '用户自动模式配置：查询失败!'},
                }
            )

    def put(self, request, pk):
        """
        更新用户策略
        """""
        try:
            ser = UserStrategySerializer(data=request.data, context=self.serializer_context)
            if not ser.is_valid():
                error_log.error(f"用户自动模式配置添加:字段校验失败{ser.errors}")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
            user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
            au_instance = UserStrategy.objects.filter(id=pk, user=user_ins)
            if not au_instance:
                error_log.error(f"用户自动模式配置更新:策略不存在")

                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动模式配置更新:策略不存在."},
                    }
                )
            ser.update(instance=au_instance, validated_data=ser.validated_data)
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": "用户自动模式配置更新: 更新成功",
                        "strategy_id": au_instance[0].id,
                    },
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置: 更新报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '用户自动模式配置：更新失败!'},
                }
            )


class UserStrategyDeleteView(APIView):
    """新.用户自动模式配置：删除"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def post(self, request, pk):
        try:
            obj = UserStrategy.objects.get(id=pk)
            obj.is_delete = 1
            obj.save()

            # 删除策略的月份
            month_instances = Month.objects.filter(strategy=obj)
            for month_instance in month_instances:
                month_instance.delete()

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": '用户自动模式配置删除：删除成功'},
                }
            )
        except Exception as e:
            error_log.error("用户自动模式配置删除: 删除报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '模式配置删除：删除失败!'},
                }
            )


class UserStrategyCheckMonthView(APIView):
    """新.用户自动模式配置：校验策略的所有月份是否均配置"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.serializer_context = {"user_id": request.user["user_id"]}

    def get(self, request, pk):
        try:
            strategy_ins = UserStrategy.objects.get(id=pk)
            months = Month.objects.filter(strategy=strategy_ins).all()

            valid_months = [month.month_number for month in months if month.is_valid]
            no_valid_months = [month.month_number for month in months if not month.is_valid]

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": {"valid_months": valid_months, "no_valid_months": no_valid_months,
                                   "count": len(valid_months)},
                    },
                }
            )

        except Exception as e:
            error_log.error("策略月份校验：校验报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '策略月份校验：校验失败!'},
                }
            )


# class UserStrategySaveToOtherView(APIView):
#     """新.用户自动控制策略：另存为"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         station_instance = models.PeakValley.objects.filter(year_month=month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     @transaction.atomic
#     def post(self, request, pk):
#         """
#         用户策略-另存为
#         """""
#         new_strategy_name = request.data.get('new_name')
#         type = int(request.data.get('type', 3))  # 1: 实时；2：默认，3：自定义
#         user_ins = models.UserDetails.objects.get(id=request.user["user_id"])
#
#         strategy = UserStrategy.objects.filter(user=user_ins, name=new_strategy_name, is_delete=0)
#         if strategy.exists():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "用户控制策略另存为: 名称已存在"},
#                 }
#             )
#         try:
#             save_id = transaction.savepoint()
#             if type == 3:
#                 try:
#                     strategy_instance = UserStrategy.objects.get(id=pk)
#                 except Exception as e:
#                     error_log.error("用户控制策略另存为:策略不存在：{}".format(e))
#
#                     return Response(
#                         {
#                             "code": common_response_code.FIELD_ERROR,
#                             "data": {"message": "error", "detail": "用户控制策略另存为:策略不存在"},
#                         }
#                     )
#
#                 old_category_instances = strategy_instance.userstrategycategory_set.all()
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     Month.objects.create(strategy=new_strategy, month_number=i)
#
#                 # # 创建新分类
#                 # for old_category_instance in old_category_instances:
#                 #     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                 #                                                                 name=old_category_instance.name,
#                 #                                                                 charge_config=old_category_instance.charge_config,
#                 #                                                                 is_follow=old_category_instance.is_follow,
#                 #                                                                 rl_list=old_category_instance.rl_list,
#                 #                                                                 pv0=old_category_instance.pv0,
#                 #                                                                 pv1=old_category_instance.pv1,
#                 #                                                                 pv2=old_category_instance.pv2,
#                 #                                                                 pv3=old_category_instance.pv3,
#                 #                                                                 pv4=old_category_instance.pv4,
#                 #                                                                 pv5=old_category_instance.pv5,
#                 #                                                                 pv6=old_category_instance.pv6,
#                 #                                                                 pv7=old_category_instance.pv7,
#                 #                                                                 pv8=old_category_instance.pv8,
#                 #                                                                 pv9=old_category_instance.pv9,
#                 #                                                                 pv10=old_category_instance.pv10,
#                 #                                                                 pv11=old_category_instance.pv11,
#                 #                                                                 pv12=old_category_instance.pv12,
#                 #                                                                 pv13=old_category_instance.pv13,
#                 #                                                                 pv14=old_category_instance.pv14,
#                 #                                                                 pv15=old_category_instance.pv15,
#                 #                                                                 pv16=old_category_instance.pv16,
#                 #                                                                 pv17=old_category_instance.pv17,
#                 #                                                                 pv18=old_category_instance.pv18,
#                 #                                                                 pv19=old_category_instance.pv19,
#                 #                                                                 pv20=old_category_instance.pv20,
#                 #                                                                 pv21=old_category_instance.pv21,
#                 #                                                                 pv22=old_category_instance.pv22,
#                 #                                                                 pv23=old_category_instance.pv23
#                 #                                                                 )
#                 #     new_category_instance.save()
#                 #     tem_months = old_category_instance.month_set.all()
#                 #     tem_months_numbers = [month.month_number for month in tem_months]
#                 #     for month in tem_months_numbers:
#                 #         month_ins = Month.objects.filter(strategy=new_strategy, month_number=month).first()
#                 #         month_ins.is_valid = False
#                 #         month_ins.user_Strategy_Category = new_category_instance
#                 #         month_ins.save()
#
#                 # todo
#
#             elif type == 2:
#                 master_station = models.MaterStation.objects.filter(is_delete=0, id=pk).first()
#                 # station = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#                 # station = models.StationDetails.objects.get(id=pk)
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#                 default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#                 station_actic = models.StationActic.objects.filter(station_id=station.id).all()
#                 former_actic_ids = [i.former_actic_id for i in station_actic]
#                 former_actic = models.FormerActic.objects.filter(id__in=former_actic_ids).all()
#
#                 for info in former_actic:
#                     info = info.__dict__
#                     rl_list = dict()
#                     for key in default_month_rl_keys:
#                         rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(
#                             info.get(key, 0) / info.get('power') * 100, 2)
#                     charge_config = [int(info.get(key, 0)) for key in default_month_charge_keys]
#                     pv_list = self._get_pv_status(station.province, station.type, station.level,
#                                                   int(info.get('year_month').split('-')[-1]))
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                                                                                 name='月份{}'.format(
#                                                                                     info.get('year_month').split('-')[
#                                                                                         -1]),
#                                                                                 charge_config=charge_config,
#                                                                                 is_follow=1,
#                                                                                 rl_list=rl_list,
#                                                                                 pv0=pv_list[0],
#                                                                                 pv1=pv_list[1],
#                                                                                 pv2=pv_list[2],
#                                                                                 pv3=pv_list[3],
#                                                                                 pv4=pv_list[4],
#                                                                                 pv5=pv_list[5],
#                                                                                 pv6=pv_list[6],
#                                                                                 pv7=pv_list[7],
#                                                                                 pv8=pv_list[8],
#                                                                                 pv9=pv_list[9],
#                                                                                 pv10=pv_list[10],
#                                                                                 pv11=pv_list[11],
#                                                                                 pv12=pv_list[12],
#                                                                                 pv13=pv_list[13],
#                                                                                 pv14=pv_list[14],
#                                                                                 pv15=pv_list[15],
#                                                                                 pv16=pv_list[16],
#                                                                                 pv17=pv_list[17],
#                                                                                 pv18=pv_list[18],
#                                                                                 pv19=pv_list[19],
#                                                                                 pv20=pv_list[20],
#                                                                                 pv21=pv_list[21],
#                                                                                 pv22=pv_list[22],
#                                                                                 pv23=pv_list[23])
#                     new_category_instance.save()
#                     Month.objects.create(strategy=new_strategy, month_number=int(info.get('year_month').split('-')[-1]),
#                                          is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#             elif type == 1:
#                 # station = models.StationDetails.objects.get(id=pk)
#                 master_station = models.MaterStation.objects.filter(is_delete=0, id=pk).first()
#                 # station = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#                 station = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#
#                 conn = get_redis_connection("3")
#
#                 # 创建新策略
#                 new_strategy = UserStrategy.objects.create(name=new_strategy_name, user=user_ins)
#                 new_strategy.save()
#
#                 for i in range(1, 13):
#                     datas = conn.get('{}-{}-mqtt'.format(station.english_name, i))
#                     data = {}
#                     current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
#                     current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]
#                     charge_config = [0 for i in range(24)]
#                     rl_list = {'RL' + str(current_month_rl_keys.index(key) + 1): 0 for key in current_month_rl_keys}
#                     pv_list = self._get_pv_status(station.province, station.type, station.level, i)
#                     # 做处理为了对应1-24点的计时
#                     pv_list.append(pv_list[0])
#                     pv_list.pop(0)
#                     if datas:
#                         datas = eval(datas)
#                         datas = datas.get('body')[0].get('body')
#                         for y in range(1, 25):
#                             data[f'RLH{y}F'] = datas.get(f'M{i}H{y}F') if datas.get(f'M{i}H{y}F') and datas.get(f'M{i}H{y}F') not in EMPTY_STR_LIST else '--'
#                             data[f'RLH{y}P'] = datas.get(f'M{i}H{y}P') if datas.get(f'M{i}H{y}P') and datas.get(f'M{i}H{y}P') not in EMPTY_STR_LIST else '--'
#                         charge_config = [int(data.get(key, 0)) for key in current_month_charge_keys]
#                         for key in current_month_rl_keys:
#                             rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(
#                                 float(data.get(key, 0)) * 100, 2)
#
#                     # new_category_instance = UserStrategyCategory.objects.create(strategy=new_strategy,
#                     #                                                             name='月份{}'.format(i),
#                     #                                                             charge_config=charge_config,
#                     #                                                             is_follow=1,
#                     #                                                             rl_list=rl_list,
#                     #                                                             pv0=pv_list[0],
#                     #                                                             pv1=pv_list[1],
#                     #                                                             pv2=pv_list[2],
#                     #                                                             pv3=pv_list[3],
#                     #                                                             pv4=pv_list[4],
#                     #                                                             pv5=pv_list[5],
#                     #                                                             pv6=pv_list[6],
#                     #                                                             pv7=pv_list[7],
#                     #                                                             pv8=pv_list[8],
#                     #                                                             pv9=pv_list[9],
#                     #                                                             pv10=pv_list[10],
#                     #                                                             pv11=pv_list[11],
#                     #                                                             pv12=pv_list[12],
#                     #                                                             pv13=pv_list[13],
#                     #                                                             pv14=pv_list[14],
#                     #                                                             pv15=pv_list[15],
#                     #                                                             pv16=pv_list[16],
#                     #                                                             pv17=pv_list[17],
#                     #                                                             pv18=pv_list[18],
#                     #                                                             pv19=pv_list[19],
#                     #                                                             pv20=pv_list[20],
#                     #                                                             pv21=pv_list[21],
#                     #                                                             pv22=pv_list[22],
#                     #                                                             pv23=pv_list[23]
#                     #                                                             )
#                     # new_category_instance.save()
#                     # Month.objects.create(strategy=new_strategy, month_number=i,
#                     #                      is_valid=False, user_Strategy_Category=new_category_instance).save()
#
#                     # todo
#
#
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.ERROR,
#                         "data": {
#                             "message": "error",
#                             "detail": "控制策略类型错误",
#                         },
#                     }
#                 )
#             transaction.savepoint_commit(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUCCESS,
#                     "data": {
#                         "message": "success",
#                         "detail": "用户自动控制策略: 另存为保存成功",
#                         "new_strategy_id": new_strategy.id
#                     },
#                 }
#             )
#         except Exception as e:
#             error_log.error("另存策略失败：{}".format(e))
#             transaction.savepoint_rollback(save_id)
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {"message": "error", "detail": "另存失败"},
#                 }
#             )


# class UserStrategyApplyView(APIView):
#     """新: 自动控制策略：下发"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["station_id"] = json.loads(request.body.decode()).get("station_id", None)
#         self.serializer_context["uid"] = uuid.uuid4()
#
#     def post(self, request):
#         ser = UserStrategyApplySerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "策略下发：参数校验失败,请检查参数是否正确！",
#                              "details": ser.errors},
#                 }
#             )
#         conn = get_redis_connection("default")
#         conn.delete("auto" + str(ser.validated_data["mobile"]))  # 删除 redis 中的短信验证码
#
#         # 查询用户信息
#         user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"],
#                                                           id=request.user["user_id"]).first()
#         if not user_instance:
#             error_log.error("自动控制模式下发:手机号与当前登录用户名不符")
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "手机号与当前登录用户名不符！"
#                     },
#                 }
#             )
#
#         # 查询站信息
#         station_id = self.serializer_context["station_id"]
#         master_station = models.MaterStation.objects.filter(is_delete=0, id=station_id).first()
#         # station_instance = models.StationDetails.objects.filter(id=station_id).first()
#         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#         if not station_instance:
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 站信息不存在！"
#                     },
#                 }
#             )
#         station_app = station_instance.app
#         station_english_name = station_instance.english_name
#
#         # 查询策略信息
#         try:
#             strategy_ins = UserStrategy.objects.get(id=ser.validated_data.get('strategy_id'))
#         except Exception as e:
#             error_log.error("自动控制策略下发: 策略信息不存在：{}".format(e))
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 策略信息不存在！"
#                     },
#                 }
#             )
#
#         # 查询策略-分类
#         category_instances = strategy_ins.userstrategycategory_set.all()
#         if not len(category_instances):
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 策略-分类为空！"
#                     },
#                 }
#             )
#
#         temp_list = list()
#         for category_instance in category_instances:
#             category_months = category_instance.month_set.all()
#             for category_month in category_months:
#                 for i in range(0, 24):
#                     temp_dict1 = dict()
#                     temp_dict2 = dict()
#                     temp_dict1['M' + str(category_month.month_number) + 'H' + str(i + 1) + 'FC'] \
#                         = str(ast.literal_eval(category_instance.charge_config)[i])
#                     temp_dict1['type'] = 'parameter'
#                     temp_dict2['M' + str(category_month.month_number) + 'H' + str(i + 1) + 'PC'] \
#                         = str(int(ast.literal_eval(category_instance.rl_list)['RL' + str(i + 1)]) / 100)
#                     temp_dict2['type'] = 'parameter'
#                     temp_list.append(temp_dict1)
#                     temp_list.append(temp_dict2)
#         temp_list.append(
#             {
#                 "LoadFollowTC": str(category_instances[0].is_follow),
#                 "type": "parameter",
#             }
#         )
#
#         # 准备下发策略
#         topic = f"req/database/parameter/{station_english_name}/{station_app}"
#         secret_key = settings.AES_KEY
#         aes = EncryptDate(secret_key)
#
#         token = aes.encrypt(station_english_name)
#
#         message = {
#             "time": str(int(time.time())),
#             "token": token,
#             "device": "EMS",
#             "body": temp_list
#         }
#
#         client = mqtt.Client()
#         client.on_connect = on_connect
#         client.on_message = on_message
#         client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
#         client.connect(
#             host=settings.MQTT_SERVER,
#             port=settings.MQTT_PORT,
#             keepalive=settings.MQTT_KEEPALIVE,
#         )
#
#         json_message = json.dumps(message)
#         try:
#             client.publish(topic, json_message)
#             print("下发策略内容：", json_message)
#         except Exception as e:
#             error_log.error('自动控制策略下发: 失败: {}'.format(e))
#             return Response(
#                 {
#                     "code": common_response_code.SUMMARY_CODE,
#                     "data": {
#                         "message": "error",
#                         "detail": "自动控制策略下发: 下发失败！"
#                     },
#                 }
#             )
#
#         success_log.info("云端计划下发成功===")
#         success_log.info(f"自动控制模式下发:下发参数为{json.dumps(message)}")
#
#         # 写入下发历史策略
#         # plan_id = ser.validated_data.pop("plan_id")
#         # plan_ins = models.UserAutomation.objects.get(id=plan_id)
#         # strategy_id = ser.validated_data.get("strategy_id")
#
#         models.Record.objects.create(
#             user=user_instance.id,
#             topic=topic,
#             message=message,
#             station=station_instance,
#             strategy=strategy_ins,
#         )
#         try:
#             ser.save()
#         except Exception as e:
#             print(e)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {
#                     "message": "success",
#                     "detail": "控制策略下发成功.",
#                 },
#             }
#         )


# class CurrentStrategyView(APIView):
#     """新-获取当前策略"""""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         query = request.query_params
#
#         ser = CurrentStrategySerializers(data=query)
#         if not ser.is_valid():
#             error_log.error(f"自动控制模式下发:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         station = ser.validated_data["station"]
#         # station_ins = models.StationDetails.objects.filter(english_name=station).first()
#         master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station).first()
#         # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#         record = models.Record.objects.filter(station=station_instance).last()
#         if record:
#             strategy_ins = record.strategy
#
#             if strategy_ins:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "success", "detail": {
#                             "strategy_id": strategy_ins.id,
#                             "strategy_name": strategy_ins.name
#                         }},
#                     }
#                 )
#             else:
#                 return Response(
#                     {
#                         "code": common_response_code.SUCCESS,
#                         "data": {"message": "no strategy has apply!!!", "detail": "当前站无下发策略策略"},
#                     }
#                 )
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "no record for strategy has apply!!!", "detail": "当前站无下发指令记录"},
#             }
#         )


# class DefaultStrategyView(APIView):
#     """
#     默认策略
#     """
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def _get_pv_status(self, province, type_, level, month):
#         '''获取当前月峰谷标识'''
#         pv = [0] * 24
#         n_month = int(month)
#         station_instance = models.PeakValley.objects.filter(year_month=n_month, province=province, type=type_,
#                                                             level=level).values(
#             "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
#             "pv14", "pv15", "pv16", "pv17",
#             "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
#         if station_instance:
#             for i in range(24):
#                 pv[i] = station_instance['pv%s' % i]
#             return pv
#         else:
#             return pv
#
#     def post(self, request):
#         ser = DefaultStrategySerializers(data=request.data)
#         if not ser.is_valid():
#             error_log.error(f"默认策略:字段校验失败{ser.errors}")
#
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#
#         now_time = datetime.datetime.now()
#         station_name = ser.validated_data["station"]
#         master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station_name).first()
#         # station_info = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
#         station_info = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()
#
#         month = request.data.get('month') if request.data.get('month') else now_time.month
#
#         pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
#         # 做处理为了对应1-24点的计时
#         pv_list.append(pv_list[0])
#         pv_list.pop(0)
#
#         default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
#         default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
#
#         station_id = station_info.id
#         former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
#         former_actic_ids = [i.former_actic_id for i in former_actic_ids]
#         former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
#         if not former_res:
#             return Response(
#                 {
#                     "code": common_response_code.ERROR,
#                     "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
#                 }
#             )
#         policy = {}
#         policy['id'] = 0
#         policy['is_delete'] = 0
#         policy['months'] = [month if month else now_time.month]
#         policy['create_time'] = now_time.strftime("%Y-%m-%d %H:%M:%S")
#         policy['name'] = station_info.province.name + common_response_code.ConfLevType.TYPE['zh'][station_info.type] + \
#                          common_response_code.ConfLevType.LEVEL['zh'][station_info.level] + '默认运行策略'
#
#         former_res = former_res.__dict__
#         conn = get_redis_connection("3")
#         res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station_info.english_name))
#         if res_fllow:
#             res_fllow = json.loads(eval(res_fllow))
#         else:
#             res_fllow = {}
#         policy['is_follow'] = res_fllow.get('WLoadFollowTC') if res_fllow.get('WLoadFollowTC') and res_fllow.get('WLoadFollowTC') not in EMPTY_STR_LIST else '--'
#         charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
#         rl_list = dict()
#         for key in default_month_rl_keys:
#             rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(
#                 former_res.get(key, 0) / former_res.get('power') * 100, 2)
#
#         policy['rl_list'] = rl_list
#         policy['charge_config'] = charge_config
#
#         for nu in range(0, 24):
#             policy[f"pv{str(nu)}"] = pv_list[nu]
#
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": policy},
#             }
#         )


class RealTimeStationStrategyView(APIView):
    """新-实时策略"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def _get_data_nwe(self, station_id, station, month, hours):
        """
        获取当前站的指定月份的自动策略控制信息
        :return:
        """
        client = mtqq_station_strategy(station_id, month)
        conn = get_redis_connection("3")
        # if month:
        i = 0
        n = 2 if int(month) < 10 else 3
        charge_config = []
        rl_list = []
        while i < 5:
            time.sleep(0.5)
            datas = conn.get('{}-{}-mqtt'.format(station, month))
            if datas:
                datas = eval(datas)
                datas = datas.get('body')[0].get('body')
                if not datas:
                    break
                key = list(datas.keys())[0]
                if key[:n] == f'M{month}':
                    if settings.FORMATEF:  # 策略时间颗粒度 15分钟或者一小时
                        count = 2 if len(datas) == 96 else 1  # 一小时和半小时上报方式，补充为15分钟
                        for hour in hours:
                            hour = 24 if hour == 0 else hour
                            if count == 2:
                                _hour = 48 if hour == 0 else hour + 24
                            else:
                                _hour = 24 if hour == 0 else hour
                            _charge_config = [
                                float(datas.get(f'M{month}H{hour}F', 0)),
                                float(datas.get(f'M{month}H{hour}F', 0)),
                                float(datas.get(f'M{month}H{_hour}F', 0)),
                                float(datas.get(f'M{month}H{_hour}F', 0)),
                            ]
                            charge_config.extend(_charge_config)
                            _rl_list = [
                                float(datas.get(f'M{month}H{hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{_hour}P', 0)) * 100,
                                float(datas.get(f'M{month}H{_hour}P', 0)) * 100
                            ]
                            rl_list.extend(_rl_list)

                    else:
                        for hour in hours:
                                charge_config.append(float(datas.get(f'M{month}H{hour + 1}F', 0)))
                                rl_list.append(float(datas.get(f'M{month}H{hour + 1}P', 0)) * 100)

                    i = 10
            else:
                i += 1
        # else:
        #     data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
        #     if data:
        #         data = json.loads(eval(data))
        #     else:
        #         data = {}
        client.disconnect()
        return charge_config, rl_list, conn


    def _get_pv_status_new(self, province, type_, level, month, hours):
        '''获取当前月峰谷标识'''
        pv = []
        n_month = f'{datetime.datetime.now().year}-{month}' if int(month) >= 10 else f'{datetime.datetime.now().year}-0{month}'
        station_instance = models.PeakValleyNew.objects.filter(year_month=n_month, province=province, type=type_,
                                                            level=level).order_by('moment').all()
        if station_instance:
            for hour in hours:
                if settings.FORMATEF:
                    for i in station_instance:
                        h = int(i.moment[:2])
                        if h == hour:
                            pv.append(i.pv)
                else:
                    for i in station_instance[::4]:
                        h = int(i.moment[:2])
                        if h == hour:
                            pv.append(i.pv)
            return pv
        else:
            return pv


    def post(self, request):
        lang = request.headers.get('lang', 'zh')
        if not request.data.get('station'):
            error_log.error(f"实时策略:字段校验不通过 => station字段未填写")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "station字段为必填项" if lang == 'zh' else "The 'station' field is mandatory."},
                }
            )

        station_english_name = request.data['station']

        master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station_english_name).first()
        if not master_station:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "查询并网点资源不存在！" if lang == 'zh' else 'Querying and merging network resources does not exist.'},
                }
            )
        # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
        station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        station_type = station_instance.type
        station_level = station_instance.level
        # 目标月电价的峰谷标识

        month = request.data.get('month')
        current_datetime = datetime.datetime.now()
        formatted_datetime = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
        hours = request.data.get('hours')
        if hours:
            hours = [int(i) for i in hours.split(',')]
            if 24 in hours:
                hours[0] = 0
        else:
            hours = [i for i in range(24)]
        charge_config, rl_list, conn = self._get_data_nwe(station_instance.id, station_instance.english_name, month, hours)
        policy = {}
        policy['id'] = 0
        policy['is_delete'] = 0
        policy['months'] = [month if month else datetime.datetime.now().month]
        policy['create_time'] = formatted_datetime
        policy['name'] = station_instance.province.name + common_response_code.ConfLevType.TYPE[lang][station_type] + \
                         common_response_code.ConfLevType.LEVEL[lang][station_level] + '默认运行策略'

        res_fllow = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station_instance.english_name))
        if res_fllow:
            res_fllow = json.loads(eval(res_fllow))
        else:
            res_fllow = {}
        policy['is_follow'] = res_fllow.get('WLoadFollowTC', 0)

        pv_list = self._get_pv_status_new(station_instance.province, station_instance.type, station_instance.level,
                                          month, hours)

        policy['rl_list'] = rl_list
        policy['charge_config'] = charge_config
        policy['pv_list'] = pv_list
        policy['formatef'] = 1 if settings.FORMATEF else 0  # 1：15分钟；0：一小时


        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "unit_name": station_instance.station_name,
                    "detail": policy,
                },
            }
        )


class CompareStationStrategyView(APIView):
    """
    默认策略比较
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def _get_pv_status(self, province, type_, level, month):
        """获取当前月峰谷标识"""
        pv = [0] * 24
        station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
                                                            level=level).values(
            "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
            "pv14", "pv15", "pv16", "pv17",
            "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
        if station_instance:
            for i in range(24):
                pv[i] = station_instance['pv%s' % i]
            return pv
        else:
            return pv

    def _get_data(self, station_id, station, month):
        """
        获取当前站的指定月份的自动策略控制信息
        :return:
        """
        client = mtqq_station_strategy(station_id, month)
        conn = get_redis_connection("3")
        if month:
            i = 0
            n = 2 if int(month) < 10 else 3
            data = {}
            while i < 5:
                time.sleep(0.5)
                datas = conn.get('{}-{}-mqtt'.format(station, month))
                if datas:
                    datas = eval(datas)
                    datas = datas.get('body')[0].get('body')
                    if not datas:
                        break
                    key = list(datas.keys())[0]
                    if key[:n] == f'M{month}':
                        for i in range(1, 25):
                            data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F') if datas.get(f'M{month}H{i}F') and datas.get(f'M{month}H{i}F') not in EMPTY_STR_LIST else '--'
                            data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P') if datas.get(f'M{month}H{i}F') and datas.get(f'M{month}H{i}P') not in EMPTY_STR_LIST else '--'
                        i = 10
                else:
                    i += 1
        else:
            data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
            if data:
                data = json.loads(eval(data))
            else:
                data = {}
        client.disconnect()
        return data

    def post(self, request):
        ser = DefaultStrategySerializers(data=request.data)
        if not ser.is_valid():
            error_log.error(f"默认策略:字段校验失败{ser.errors}")

            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        station_english_name = request.data['station']
        now_time = datetime.datetime.now()
        master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station_english_name).first()
        # station_instance = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
        station_instance = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        month = request.data.get('month') if request.data.get('month') else now_time.month

        default_month_charge_keys = ['h' + str(i) + 'f' for i in range(1, 25)]
        default_month_rl_keys = ['h' + str(i) + 'p' for i in range(1, 25)]
        current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
        current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]

        station_id = station_instance.id
        former_actic_ids = models.StationActic.objects.filter(station_id=station_id).all()
        former_actic_ids = [i.former_actic_id for i in former_actic_ids]

        month_list = [month] if month != '-1' else [str(i) for i in range(1, 13)]
        for month in month_list:
            pv_list = self._get_pv_status(station_instance.province, station_instance.type, station_instance.level,
                                          month)
            # 做处理为了对应1-24点的计时
            pv_list.append(pv_list[0])
            pv_list.pop(0)
            # 默认策略
            former_res = models.FormerActic.objects.filter(id__in=former_actic_ids, year_month=month).first()
            if not former_res:
                return Response(
                    {
                        "code": common_response_code.ERROR,
                        "data": {"message": "当前查询时间下暂无策略信息，请联系管理员处理", "detail": {}},
                    }
                )

            former_res = former_res.__dict__
            # 实时策略
            current_data = self._get_data(station_instance.id, station_instance.english_name, month)

            default_charge_config = [int(former_res.get(key, 0)) for key in default_month_charge_keys]
            current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]

            default_rl_list = dict()
            for key in default_month_rl_keys:
                default_rl_list['RL' + str(default_month_rl_keys.index(key) + 1)] = round(
                    former_res.get(key, 0) / former_res.get('power') * 100, 2)

            current_rl_list = dict()
            for key in current_month_rl_keys:
                current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(
                    float(current_data.get(key, 0)) * 100, 2)

            res = []
            rl_list = list(zip(current_rl_list.values(), default_rl_list.values()))
            charge_config = list(zip(current_charge_config, default_charge_config))
            for i in range(24):
                if charge_config[i][0] == 0 and charge_config[i][1] == 0:
                    continue
                if len(set(rl_list[i])) > 1 or (len(set(charge_config[i])) > 1):
                    data = {}
                    data['hours'] = i + 1
                    data['default_rl'] = rl_list[i][1]
                    data['default_charge'] = charge_config[i][1]
                    data['current_rl'] = rl_list[i][0]
                    data['current_charge'] = charge_config[i][0]
                    data['pv'] = pv_list[i]
                    data['month'] = month
                    res.append(data)

            if res:
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {"message": "success", "detail": res},
                    }
                )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": []},
            }
        )


class CustomizeStationStrategyView(APIView):
    """
    自定义策略比较
    """
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def _get_pv_status(self, province, type_, level, month):
        """获取当前月峰谷标识"""
        pv = [0] * 24
        station_instance = models.PeakValley.objects.filter(year_month=int(month), province=province, type=type_,
                                                            level=level).values(
            "pv0", "pv1", "pv2", "pv3", "pv4", "pv5", "pv6", "pv7", "pv8", "pv9", "pv10", "pv11", "pv12", "pv13",
            "pv14", "pv15", "pv16", "pv17",
            "pv18", "pv19", "pv20", "pv21", "pv22", "pv23").first()
        if station_instance:
            for i in range(24):
                pv[i] = station_instance['pv%s' % i]
            return pv
        else:
            return pv

    def _get_data(self, station_id, station, month):
        """
        获取当前站的指定月份的自动策略控制信息
        :return:
        """
        client = mtqq_station_strategy(station_id, month)
        conn = get_redis_connection("3")
        if month:
            i = 0
            n = 2 if int(month) < 10 else 3
            data = {}
            while i < 5:
                time.sleep(0.5)
                datas = conn.get('{}-{}-mqtt'.format(station, month))
                if datas:
                    datas = eval(datas)
                    datas = datas.get('body')[0].get('body')
                    if not datas:
                        break
                    key = list(datas.keys())[0]
                    if key[:n] == f'M{month}':
                        for i in range(1, 25):
                            data[f'RLH{i}F'] = datas.get(f'M{month}H{i}F') if datas.get(f'M{month}H{i}F') and datas.get(f'M{month}H{i}F') not in EMPTY_STR_LIST else '--'
                            data[f'RLH{i}P'] = datas.get(f'M{month}H{i}P') if datas.get(f'M{month}H{i}F') and datas.get(f'M{month}H{i}P') not in EMPTY_STR_LIST else '--'
                        i = 10
                else:
                    i += 1
        else:
            data = conn.get('Business:TRANS_TIMING_MEASURE_{}_EMS'.format(station))
            if data:
                data = json.loads(eval(data))
            else:
                data = {}
        client.disconnect()
        return data

    def post(self, request):
        ser = CustomizeStrategySerializers(data=request.data)
        if not ser.is_valid():
            error_log.error(f"默认策略:字段校验失败{ser.errors}")

            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        strategy_id = int(request.data['strategy_id'])
        station_name = ser.validated_data["station"]
        master_station = models.MaterStation.objects.filter(is_delete=0, english_name=station_name).first()
        # station_info = master_station.stationdetails_set.filter(Q(slave=-1) | Q(slave=0)).first()
        station_info = master_station.stationdetails_set.filter(is_delete=0, english_name=master_station.english_name).first()

        detail = UserStrategy.objects.get(id=strategy_id)
        if not detail:
            error_log.error(f"用户自动控策略:策略不存在")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户自动控制策略:策略不存在."},
                }
            )

        current_month_charge_keys = ['RLH' + str(i) + 'F' for i in range(1, 25)]
        current_month_rl_keys = ['RLH' + str(i) + 'P' for i in range(1, 25)]

        month_list = [str(i) for i in range(1, 13)]
        for month in month_list:
            pv_list = self._get_pv_status(station_info.province, station_info.type, station_info.level, month)
            # 做处理为了对应1-24点的计时
            pv_list.append(pv_list[0])
            pv_list.pop(0)
            # 自定义策略
            month_detail = detail.month_set.filter(is_valid=0, month_number=int(month)).first()
            if not month_detail:
                error_log.error(f"用户自动控策略:策略配置不完整")
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": "用户自动控策略:策略配置不完整"},
                    }
                )
            strategy_category = UserStrategyCategoryNew.objects.get(id=month_detail.user_Strategy_Category.id)
            customize_change_config = eval(strategy_category.charge_config)
            customize_rl_list = eval(strategy_category.rl_list)

            # 实时策略
            current_data = self._get_data(station_info.id, station_info.english_name, month)

            current_charge_config = [int(current_data.get(key, 0)) for key in current_month_charge_keys]

            current_rl_list = dict()
            for key in current_month_rl_keys:
                current_rl_list['RL' + str(current_month_rl_keys.index(key) + 1)] = round(
                    float(current_data.get(key, 0)) * 100, 2)

            res = []
            customize_list = [float(i) for i in customize_rl_list.values()]
            rl_list = list(zip(current_rl_list.values(), customize_list))
            charge_config = list(zip(current_charge_config, customize_change_config))
            for i in range(24):
                if charge_config[i][0] == 0 and charge_config[i][1] == 0:
                    continue
                if len(set(rl_list[i])) > 1 or len(set(charge_config[i])) > 1:
                    data = {}
                    data['hours'] = i + 1
                    data['customize_rl'] = rl_list[i][1]
                    data['customize_charge'] = charge_config[i][1]
                    data['current_rl'] = rl_list[i][0]
                    data['current_charge'] = charge_config[i][0]
                    data['pv'] = pv_list[i]
                    data['month'] = month
                    res.append(data)

            if res:
                return Response(
                    {
                        "code": common_response_code.SUCCESS,
                        "data": {"message": "success", "detail": res},
                    }
                )

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": []},
            }
        )


class UserStrategyCustomizeView(APIView):
    """自定义策略查询列表"""""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request, strategy_id):
        """
        获取策略所具有的分类列表
        """""

        try:
            strategy_ins = UserStrategy.objects.get(id=strategy_id)
        except Exception as e:
            error_log.error("自定义控制策略: 查询报错：{}".format(e))
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "fail", "detail": '自定义控制策略：控制策略不存在!'},
                }
            )

        category_month_instances = strategy_ins.month_set.filter(is_valid=0).all()
        temp_detail = []
        for detail in category_month_instances:
            info = UserStrategyCategoryNew.objects.get(id=detail.user_Strategy_Category.id)
            info = info.__dict__
            info['month'] = detail.month_number
            if info.get('charge_config'):
                info['charge_config'] = eval(info.get('charge_config'))
            if info.get('rl_list'):
                info['rl_list'] = eval(info.get('rl_list'))
            del info['_state']
            temp_detail.append(info)

        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {"message": "success", "detail": temp_detail},
            }
        )


class AutomaticControlSendSmsView(APIView):
    """自动控制模式发送短信"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = monitor_serializers.MonitorSendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("自动控制模式发送短信:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("自动控制模式发送短信:短信下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )
        """目前没有接入第三方发短信程序"""
        conn = get_redis_connection("default")
        conn.set("auto" + str(ser.validated_data["mobile"]), random_sms_code, ex=60 * 5)  # redis 数据格式 monitor_mobile
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


