#!/usr/bin/env python
# coding=utf-8
#@Information:  用户曲线
#<AUTHOR> WYJ
#@Date         : 2022-05-26 10:00:15
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\HaLun\charts_user.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-05-26 10:00:59


import json
import logging
import tornado.web
from sqlalchemy import func
from Application.Models.base_handler import BaseHandler
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.Utils.num_utils import is_json,real_data
from Tools.Utils.time_utils import timeUtils
import pickle
from Application.Models.SelfStationPoint.t_status import StatusPT
from Tools.DB.mysql_scada import mqtt_session
from Application.Models.His.r_ACDMS_1 import HisACDMS_1
from Application.Models.SelfStationPoint.t_device import DevicePT
from Application.Models.SelfStationPoint.t_status_bits import StatusBitsPT


# 存放电站对应的监视曲线保存文件
char_db = {'his':["../../Cfg/charts.json","charts"],'taicang':["../../Cfg/taicang_charts.json",'taicang_charts'],'halun':["../../Cfg/halun_charts.json",'halun_charts'],
'binhai':["../../Cfg/binhai_charts.json","binhai_charts"],'ygzhen':["../../Cfg/ygzhen_charts.json","ygzhen_charts"],'baodian':["../../Cfg/baodian_charts.json","baodian_charts"],
           'dongmu':["../../Cfg/dongmu_charts.json","dongmu_charts"],'zgtian':["../../Cfg/zgtian_charts.json","zgtian_charts"]}

class ChartsOfUserIntetface(BaseHandler):
    ''' 
    获取当前用户曲线
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'GetCharts':  # 获取当前用户所有配置的名称
                session = self.getOrNewSession()
                db = self.get_argument('db','his')
                user = session.user
                obj = {}
                logging.info('db:%s'%db)
                charts = self._getChartsFromSession(db,user)
                for k in charts.keys():  # 所有区域
                    type_obj ={}
                    for port in charts[k].keys():  # 所有区域下的模块
                        arr = []
                        for type_name in charts[k][port]:  # 所有类型
                            if type_name == 'status':
                                table = 't_' + type_name + '_bits'
                            else:
                                table = 't_' + type_name

                            names = eval(charts[k][port][type_name])
                            if db == 'dongmu':
                                HisTable = HisACDMS_1(table)
                                for name in names:
                                    n = name.split('.')
                                    if type_name == 'discrete':
                                        device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == 'ReMod').all()
                                    else:
                                        device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == n[1]).all()
                                    for i in device_:
                                        if type_name == 'status':
                                            value_ = mqtt_session.query(StatusPT.id,StatusPT.name,StatusPT.descr).filter(StatusPT.device_id == i[0]).all()
                                            if i[0] !=22:
                                                for e in value_:
                                                    value = mqtt_session.query(HisTable.name, HisTable.descr).filter(HisTable.status_id == e[0]).all()
                                                    for v in value:
                                                        if v[0] == n[2]:
                                                            arr.append({'name': name, 'descr': v[1], 'type': type_name,'unit': ''})
                                            else:  # 遥控不是合并上报
                                                for e in value_:
                                                    if n[2] == e[1]:
                                                        arr.append({'name': name, 'descr': e[2], 'type': type_name,'unit': ''})
                                        else:
                                            if type_name == 'discrete':
                                                value = mqtt_session.query(HisTable.name, HisTable.descr).filter(HisTable.device_id == i[0]).all()
                                            else:
                                                value = mqtt_session.query(HisTable.name, HisTable.descr,HisTable.unit).filter(HisTable.device_id == i[0]).all()
                                            for v in value:
                                                if v[0] == n[2]:
                                                    if type_name == 'discrete':
                                                        arr.append({'name': name, 'descr': v[1], 'type': type_name,'unit': ''})
                                                    else:
                                                        arr.append({'name':name,'descr':v[1],'type':type_name,'unit':v[2]})
                            else:
                                for name in names:
                                    bean = real_data(type_name,name,'db')
                                    if bean['desc']:
                                        # bean = real_data(type_name,name,'ram')
                                        # logging.info('scada is not %s'%(name))
                                        arr.append({'name':name,'descr':bean['desc'],'type':type_name,'unit':bean['unit']})
                        type_obj[port] = arr
                    obj[k] = type_obj
                    mqtt_session.close()
                return self.returnTypeSuc(obj)
            elif kt == 'SaveCharts':  # 做持久化
                db = self.get_argument('db','his')
                logging.info('db:%s'%db)
                session = self.getOrNewSession()
                user = session.user
                charts = self._getChartsFromSession(db,user)
                bytejson = pickle.dumps(charts)
                ff = open(char_db[db][0],"wb")
                ff.write(bytejson)
                ff.close()
                msg = 'save user charts is [%s]'%(user['descr'])
                logging.info(msg)
                return self.returnTypeSuc('')
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            return self.requestError()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
        # if 1:
            if kt == 'ModifyCharts':  # 修改用户的监控配置项,没有即添加有即覆盖
                charts = self.get_argument('charts',[])  # 监视集合
                area = self.get_argument('area',None)  # 监视集合
                type_name = self.get_argument('type_name',None)  # 监视集合
                part = self.get_argument('part',None)  # 监视集合
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('charts:%s,area:%s,type_name:%s,part:%s,db:%s'%(charts,area,type_name,part,db))

                session = self.getOrNewSession()
                user = session.user
                char = self._getChartsFromSession(db,user)
                if area in char.keys():
                    if part in char[area].keys():
                        if type_name in char[area][part].keys():
                            char[area][part][type_name] = charts
                        else:
                            oo = char[area][part]
                            oo[type_name] = charts
                            char[area][part]=oo
                    else:
                        oo = char[area]
                        oo[part] = {type_name:charts}
                        char[area]=oo
                else:
                    obj1,obj2 = {},{}
                    obj2[type_name] = charts
                    obj1[part] = obj2
                    char[area]=obj1 
                user[char_db[db][1]] = str(char)
                self.updateSession(session)
                return self.returnTypeSuc('')
            elif kt == 'AddCharts':   # 添加用户的监控配置项
                charts = self.get_argument('charts',[])  # 监视集合
                area = self.get_argument('area',None)  # 监视集合
                type_name = self.get_argument('type_name',None)  # 监视集合
                part = self.get_argument('part',None)  # 监视集合
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('charts:%s,area:%s,type_name:%s,part:%s,db:%s'%(charts,area,type_name,part,db))
                session = self.getOrNewSession()
                user = session.user
                char = self._getChartsFromSession(db,user)
                if area in char.keys():
                    if part in char[area].keys():
                        if type_name in char[area][part].keys():
                            old_chart = char[area][part][type_name]
                            char[area][part][type_name] = str(list(set(eval(charts)+eval(old_chart))))
                        else:
                            oo = char[area][part]
                            oo[type_name] = charts
                            char[area][part]=oo
                    else:
                        oo = char[area]
                        oo[part] = {type_name:charts}
                        char[area]=oo
                else:
                    obj1,obj2 = {},{}
                    obj2[type_name] = charts
                    obj1[part] = obj2
                    char[area]=obj1 
                user[char_db[db][1]] = str(char)
               
                self.updateSession(session)
                return self.returnTypeSuc('')
            elif kt == 'ModifyChartsByName':  # 修改用户的指定监控配置项
                oldName = self.get_argument('oldName',None)  # 被替换名
                newName = self.get_argument('newName',None)  # 替换名
                type_name = self.get_argument('type_name',None)  # 被替换类型
                area = self.get_argument('area',None)  # 监视集合
                part = self.get_argument('part',None)  # 监视集合
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('oldName:%s,newName:%s,area:%s,type_name:%s,part:%s'%(oldName,newName,area,type_name,part))

                session = self.getOrNewSession()
                user = session.user
                char = self._getChartsFromSession(db,user)
                # char = eval(user['charts'])
                if area not in char.keys():
                    return self.customError('入参无效1')
                if part not in char[area].keys():
                    return self.customError('入参无效2')
                if type_name not in char[area][part].keys():
                    return self.customError('入参无效3')
                old_c = eval(char[area][part][type_name])
                new_c = [newName if i ==oldName else i for i in old_c]
                char[area][part][type_name]=str(list(set(new_c)))
                user[char_db[db][1]] = str(char)
                self.updateSession(session)
                return self.returnTypeSuc('')
            elif kt == 'DeleteChartsByName':  # 删除用户的指定监控配置项
                name = self.get_argument('name',None)  # 被删除名
                type_name = self.get_argument('type_name',None)  # 被替换类型
                area = self.get_argument('area',None)  # 监视集合
                part = self.get_argument('part',None)  # 监视集合
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('name:%s,area:%s,type_name:%s,part:%s,db:%s'%(name,area,type_name,part,db))

                session = self.getOrNewSession()
                user = session.user
                char = self._getChartsFromSession(db,user)
                # char = eval(user['charts'])
                if area not in char.keys():
                    return self.customError('入参无效1')
                if part not in char[area].keys():
                    return self.customError('入参无效2')
                if type_name not in char[area][part].keys():
                    return self.customError('入参无效3')
                old_c = eval(char[area][part][type_name])
                try:
                   old_c.remove(name) 
                except:
                    return self.customError('入参无效')
                char[area][part][type_name]=str(old_c)
                user[char_db[db][1]] = str(char)
                self.updateSession(session)
                return self.returnTypeSuc('')
            elif kt == 'DeleteChartsArea':  # 删除用户的指定监控区域
                area = self.get_argument('area',None)  # 监视集合
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('area:%s,db:%s'%(area,db))

                session = self.getOrNewSession()
                user = session.user
                char = self._getChartsFromSession(db,user)
                if area not in char.keys():
                    return self.customError('入参无效1')
                try:
                   del char[area]
                except:
                    return self.customError('入参无效')

                user[char_db[db][1]] = str(char)
                self.updateSession(session)
                return self.returnTypeSuc('')
            elif kt == 'ChartsRealData':  # 用户的监控实时值
                statusF = int(self.get_argument('statusF',1))  # 状态量
                measureF = int(self.get_argument('measureF',1))  # 测量量
                discreteF = int(self.get_argument('discreteF',1))  # 离散量
                cumulantF = int(self.get_argument('cumulantF',1))  # 累计量
                area = self.get_argument('area',None)  # 指定区域 不指定则全部
                part = self.get_argument('part',None)  # 指定区域下某个模块 不指定则全部
                db = self.get_argument('db','his')  # 电站
                if DEBUG:
                    logging.info('statusF:%s,measureF:%s,discreteF:%s,cumulantF:%s,area:%s,part:%s,db:%s'%(statusF,measureF,
                    discreteF,cumulantF,area,part,db))
                session = self.getOrNewSession()
                user = session.user
                charts = self._getChartsFromSession(db,user)
                
                type_arr = []
                if statusF:
                    type_arr.append('status')
                if measureF:
                    type_arr.append('measure')
                if discreteF:
                    type_arr.append('discrete')
                if cumulantF:
                    type_arr.append('cumulant')
                obj = self._getRealData(type_arr,charts,area,part,db)
                return self.returnTypeSuc(obj)

            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            return self.requestError()
        finally:
            mqtt_session.close()
        
    def _getRealData(self,type_arr,charts,area,part,db):
        '''获取实时值'''
        obj = {}
        if area in charts.keys():  #获取指定区域数据
            part_obj = {}
            if part in charts[area].keys():
                part_obj[part]=self._getRealByPart(type_arr,charts,area,part,db)
            elif not part:
                for part in charts[area].keys():
                    part_obj[part]=self._getRealByPart(type_arr,charts,area,part,db)
            obj[area] = part_obj
        elif not area:  # 区域为空，取所有
            for area in charts.keys():  # 所有区域
                part_obj = {}
                if part in charts[area].keys():
                    part_obj[part]=self._getRealByPart(type_arr,charts,area,part,db)
                elif not part:
                    for par in charts[area].keys():
                        part_obj[par]=self._getRealByPart(type_arr,charts,area,par,db)
                obj[area] = part_obj
        # obj['time'] = timeUtils.getNewTimeStr()
        return {'value':obj,'time':timeUtils.getNewTimeStr()}

    def _getRealByPart(self,type_arr,charts,area,part,db):
        '''获取指定区域下指定part的实时值'''
        type_obj = []
        for type_ in type_arr:
            if type_ == 'status':
                table = 't_' + type_ + '_bits'
            else:
                table = 't_' + type_
            v1 = real_data(type_, 'dongmu', 'db')
            if v1:
                # print v1
                e1 = v1['body']
            if type_ in charts[area][part].keys():
                names = eval(charts[area][part][type_])
                if db == "dongmu":
                    if names:
                        for name in names:
                            n = name.split('.')
                            HisTable = HisACDMS_1(table)
                            if type_ == 'discrete':
                                device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == 'ReMod').all()
                            else:
                                device_ = mqtt_session.query(DevicePT.id).filter(DevicePT.name == n[1]).all()
                            for i in device_:
                                if type_ == 'status':
                                    value_ = mqtt_session.query(StatusPT.id,StatusPT.name,StatusPT.descr).filter(StatusPT.device_id == i[0]).all()
                                    for e in value_:
                                        if e[1] == n[2]:
                                           for ed in e1:
                                            if ed['device'] == n[1]:
                                                value_obj = ed[n[2]]
                                                if value_obj:
                                                    type_obj.append({'name': name, 'descr': e[2], 'type': type_, 'unit': '','value': value_obj})
                                        else:
                                            value = mqtt_session.query(StatusBitsPT.name, StatusBitsPT.descr,StatusBitsPT.bits).filter(StatusBitsPT.status_id == e[0]).all()
                                            for v in value:
                                                if v[0] == n[2]:
                                                    for e in e1:
                                                        if e['device'] == name[11:15] or e['device'] == name[16:20]:
                                                            bit_n = '{:016b}'.format(int(e[name[21:24]]))  # 转成2进制，高位补零
                                                            bit_list = list(bit_n)  # 转数据，正好是反向的，需要整个反转
                                                            value_obj = bit_list[v[2]]
                                                            if value_obj:
                                                                type_obj.append({'name': name, 'descr': v[1], 'type': type_, 'unit': '','value': value_obj})
                               
                                else:
                                    if type_ == 'discrete':
                                        value = mqtt_session.query(HisTable.name, HisTable.descr).filter(HisTable.device_id == i[0]).all()
                                    else:
                                        value = mqtt_session.query(HisTable.name, HisTable.descr, HisTable.unit).filter(HisTable.device_id == i[0]).all()
                                    for v in value:
                                        if v[0] == n[2]:
                                            for e in e1:
                                                if n[2] in e.keys():
                                                    if e['device'] == n[1] :
                                                        value_obj = e[n[2]]
                                                        if value_obj:
                                                            if type_ == 'discrete':
                                                                type_obj.append({'name': name, 'descr': v[1], 'type': type_, 'unit': '','value': value_obj})
                                                            else:
                                                                type_obj.append({'name': name, 'descr': v[1], 'type': type_, 'unit': v[2],'value': value_obj})
                else:
                    for name in names:
                        bean = real_data(type_,name,'db')
                        if bean['desc']:
                            # bean = real_data(type_,name,'ram')
                            # logging.info('scada is not %s'%(name))
                            type_obj.append({'name':name,'descr':bean['desc'],'type':type_,'unit':bean['unit'],'value':bean['value']})
            mqtt_session.close()
                                    
        return type_obj

    def _getChartsFromSession(self,db,user):
        '''根据参数获取具体的曲线'''
        char = eval(user[char_db[db][1]])
        return char