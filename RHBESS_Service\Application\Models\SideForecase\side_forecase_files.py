#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-31 08:41:21
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_files.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-20 09:06:18

from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_base_file import ForecaseBaseFile
from Application.Models.SideForecase.side_forecase_project import ForecaseProject
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseFiles(user_Base):
    u'项目文件表--收资归档'
    __tablename__ = "t_side_forecase_files"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    file_name = Column(String(256), nullable=True, comment=u"文件名称")
    file_type = Column(String(256), nullable=True, comment=u"文件类型名称  输入内容")
    file_path = Column(String(256), nullable=True, comment=u"文件路径加自定义命名")
    remarks = Column(Text, nullable=True, comment=u"备注")
    base_file_id = Column(Integer, ForeignKey("t_side_forecase_base_file.id"),nullable=False, comment=u"文件分类id")
    project_id = Column(Integer, ForeignKey("t_side_forecase_project.id"),nullable=False, comment=u"项目id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    file_size = Column(String(32), nullable=True, comment=u"文件大小")
    base_file_files = relationship("ForecaseBaseFile", backref="base_file_files")
    project_files = relationship("ForecaseProject", backref="project_files")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'file_name':'%s','file_type':'%s','file_path':'%s','file_size':'%s','op_ts':'%s'}" %(self.id,self.file_name,self.file_type,self.file_path, self.file_size,self.op_ts)
        return bean.replace('None',"")

   