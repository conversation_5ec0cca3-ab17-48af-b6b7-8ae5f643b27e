#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:50:19
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_remote_ctl.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 15:00:45



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class RemoteCtlPT(scada_Base):
    ''' 远控配置表 '''
    __tablename__ = "t_remote_ctl"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    device = Column(Integer, nullable=False, comment=u"所属设备")
    no = Column(Integer, nullable=False,comment=u"编号")
    descr = Column(String(256), nullable=False,comment=u"名称")
    desc_preopen = Column(Integer, nullable=True,comment=u"")
    desc_preclose = Column(Integer, nullable=True,comment=u"系数")
    desc_close = Column(Integer, nullable=True,comment=u"变化值")
    desc_open = Column(Integer, nullable=True,comment=u"")
    comment = Column(String(256), nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','device':'%s','no':%s,'descr':'%s','desc_preopen':'%s','desc_preclose':'%s','desc_close':'%s','desc_open':'%s','comment':'%s'}" % (
            self.id,self.name,self.device,self.no,self.descr,self.desc_preopen,self.desc_preclose,self.desc_close,self.desc_open,self.comment)