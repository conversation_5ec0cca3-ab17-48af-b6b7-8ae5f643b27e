# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/5/7 8:50
# <AUTHOR> <PERSON><PERSON>ang
# @Project  : EnergyMonitorService
# @File     : gen_excel.py
# @Software : PyCharm
import os

import openpyxl

from tools.minio_tool import MinioTool


def create_excel(file_path, sheet_name, headers, data, sheet_name1=None, headers1=None, data1=None, sheet_name2=None, headers2=None, data2=None):

    # 创建一个工作簿
    workbook = openpyxl.Workbook()

    # 创建一个工作表
    sheet = workbook.active
    sheet.title = sheet_name

    # 写入表头
    for col_num, header in enumerate(headers, 1):
        sheet.cell(row=1, column=col_num).value = header

    # 写入数据
    for row_num, row_data in enumerate(data, 2):
        for col_num, cell_data in enumerate(row_data, 1):
            sheet.cell(row=row_num, column=col_num).value = cell_data

    if sheet_name1 and headers1 and data1:
        worksheet1 = workbook.create_sheet(sheet_name1)
        worksheet1.title = sheet_name1

        # 写入表头
        for col_num, header in enumerate(headers1, 1):
            worksheet1.cell(row=1, column=col_num).value = header

        # 写入数据
        for row_num, row_data in enumerate(data1, 2):
            for col_num, cell_data in enumerate(row_data, 1):
                worksheet1.cell(row=row_num, column=col_num).value = cell_data

    if sheet_name2 and headers2 and data2:
        worksheet2 = workbook.create_sheet(sheet_name2)
        worksheet2.title = sheet_name2

        # 写入表头
        for col_num, header in enumerate(headers2, 1):
            worksheet2.cell(row=1, column=col_num).value = header

        # 写入数据
        for row_num, row_data in enumerate(data2, 2):
            for col_num, cell_data in enumerate(row_data, 1):
                worksheet2.cell(row=row_num, column=col_num).value = cell_data

    # 保存文件
    workbook.save(file_path)

    return file_path


def post2minio(file_path, object_name, bucket_name='download'):
    minio_client = MinioTool()
    minio_client.create_bucket(bucket_name)
    url = minio_client.upload_local_file(object_name, file_path, bucket_name)
    return url


if __name__ == '__main__':
    # 示例用法
    file_name = "demo2.xlsx"
    sheet_name = "Sheet1"
    headers = ["姓名", "年龄", "城市"]
    data = [
        ["张三", 25, "北京"],
        ["李四", 30, "上海"],
        ["王五", 22, "深圳"],
    ]

    f = create_excel(file_name, sheet_name, headers, data)

    print(post2minio(f, file_name))

