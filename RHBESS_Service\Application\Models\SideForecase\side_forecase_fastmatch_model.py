#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-23 13:48:48
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_fastmatch_model.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-14 10:32:05


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Application.Models.SideForecase.side_forecase_ele import ForecaseEle
from Application.Models.SideForecase.side_forecase_vol import ForecaseVol
class ForecaseFastmatchModel(user_Base):
    u'仿真结果速查表'
    __tablename__ = "t_side_forecase_fastmatch_model"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    province_id = Column(Integer, ForeignKey("t_side_forecase_province.id"),nullable=False, comment=u"省份id")
    ele_id = Column(Integer, ForeignKey("t_side_forecase_ele.id"),nullable=False, comment=u"用电分类")
    vol_id = Column(Integer, ForeignKey("t_side_forecase_vol.id"),nullable=False, comment=u"电压等级")
    run_day = Column(Integer, nullable=True, comment=u"运行天数")
    power = Column(VARCHAR(25), nullable=True, comment=u"功率")
    dod = Column(VARCHAR(5), nullable=True, comment=u"dod 放电深度")
    capacity = Column(VARCHAR(25), nullable=True, comment=u"容量")
    eff_chag = Column(VARCHAR(25), nullable=True, comment=u"充电效率")
    eff_disg = Column(VARCHAR(25), nullable=True, comment=u"放电效率")

    weig_price = Column(VARCHAR(255), nullable=False, comment=u"加权电价列表[尖，峰，平，谷，深，总计]")
    year_chag_disg = Column(VARCHAR(255), nullable=False, comment=u"年充放电量列表[尖，峰，平，谷，深，总计]")
    shounian_taoli = Column(VARCHAR(25), nullable=True, comment=u"首年总套利")
    diff_price = Column(VARCHAR(25), nullable=True, comment=u"度电循环价差")
    day_diff_price = Column(VARCHAR(25), nullable=True, comment=u"日度电套利价差")
    recycle_after = Column(VARCHAR(25), nullable=True, comment=u"税后回收期")
    year_first_chag = Column(VARCHAR(25), nullable=True, comment=u"首年充电量")
    year_first_disg = Column(VARCHAR(25), nullable=True, comment=u"首年放电量")
    just_recycle_ele = Column(VARCHAR(25), nullable=True, comment=u"正循环电量")
    # lose_recycle_ele = Column(VARCHAR(25), nullable=True, comment=u"负循环电量")
    just_recycle_num = Column(VARCHAR(25), nullable=True, comment=u"正循环次数")
    # lose_recycle_num = Column(VARCHAR(25), nullable=True, comment=u"负循环次数")
    recycle_base_after= Column(VARCHAR(25), nullable=True, comment=u"税后基准回收期")
    recycle_bias = Column(VARCHAR(25), nullable=True, comment=u"回收期偏差")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    province_forecase_fastmatch_model = relationship("ForecaseProvince", backref="province_forecase_fastmatch_model")
    ele_fastmatch_model = relationship("ForecaseEle",backref='ele_fastmatch_model',foreign_keys=[ele_id])
    vol_fastmatch_model = relationship("ForecaseVol",backref='vol_fastmatch_model',foreign_keys=[vol_id])
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'shounian_taoli':'%s','diff_price':'%s','day_diff_price':'%s','recycle_after':'%s','year_first_chag':'%s','year_first_disg':'%s','just_recycle_ele':'%s',\
                'just_recycle_num':'%s','recycle_base_after':'%s','recycle_bias':'%s'}" %(self.shounian_taoli,self.diff_price,self.day_diff_price,self.recycle_after,
                self.year_first_chag,self.year_first_disg,self.just_recycle_ele,self.just_recycle_num,self.recycle_base_after,self.recycle_bias)

   