import logging

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.decision_cfg import model_config
# 入参打印到日志中
DEBUG = model_config.get('broker', "log_debug")
# user库连接 mysql

ELE_HOSTNAME = model_config.get('mysql', "DB_HOSTNAME")
ELE_PORT = model_config.get('mysql', "DB_PORT")
ELE_DATABASE = model_config.get('mysql', "DB_DATABASE")
ELE_USERNAME = model_config.get('mysql', "DB_USERNAME")
ELE_PASSWORD = model_config.get('mysql', "DB_PASSWORD")

userdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    ELE_USERNAME,
    ELE_PASSWORD,
    ELE_HOSTNAME,
    ELE_PORT,
    ELE_DATABASE
)

user_engine = create_engine(userdb_mysql_url,
                       echo=False,
                       max_overflow=-1, pool_size=40, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_user_session = scoped_session(sessionmaker(user_engine,autoflush=True))

user_Base = declarative_base(user_engine)
user_session = _user_session()


def check_db_connection(engine):
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        print(f"Database connection error: {e}")
        return False


def get_user_session():
    global user_engine
    # 判断数据库连接是否存在，重连五次
    i = 0
    while i < 5:
        if not check_db_connection(user_engine):
            logging.error('!!!!!!!!!!!!!!!!!!!数据库连接中断了。。。。。')
            # 如果连接中断，可以选择重新创建引擎或者采取其他措施
            user_engine = create_engine(userdb_mysql_url,
                                        echo=False,
                                        max_overflow=5, pool_size=40, pool_timeout=10, pool_pre_ping=True, pool_recycle=1800)
            i += 1
        else:
            i = 5
        _user_session = scoped_session(sessionmaker(user_engine, autoflush=True))

        user_Base = declarative_base(user_engine)
        user_session_ = _user_session()
        return user_session_
