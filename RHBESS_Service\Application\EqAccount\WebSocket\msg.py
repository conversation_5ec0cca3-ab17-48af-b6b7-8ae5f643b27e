# -*- coding:utf-8 -*-

import datetime
import json
import logging

import tornado.web
from sqlalchemy import and_, func

from Application.EqAccount.public.message_ygdl import push_role_message, push_user_message
from Application.Models.ygdl.account.message import Message
from Application.Models.ygdl.account.organization import Organization
from Application.Models.ygdl.account.user import User
from Application.Models.ygdl.account.user_message import UserMessage
from Tools.DB.pgsql_ygdl import sessionmaker, scada_engine, scada_session,DEBUG

# 台账
db_session = scada_session


from Application.Models.base_handler import WebSocketBaseHandler, BaseHandler


class MessageUnread(BaseHandler):

    '''
    @description: 获取未读的消息
    '''

    @tornado.web.authenticated
    def post(self):
        return self.pathError()

    @tornado.web.authenticated
    def get(self):
        try:
            session = self.getOrNewSession()
            user = session.user
            user = db_session.query(User).filter(User.id == user['id']).first()

            user_message_ids = db_session.query(UserMessage.message_id, UserMessage.read).filter(and_(
                UserMessage.user_id == user.id, UserMessage.read != True)).all()
            msg_dict = {i[0]: i[1] for i in user_message_ids}
            message_list = db_session.query(Message).filter(Message.id.in_([i[0] for i in user_message_ids])).order_by(
                -Message.id).all()
            message_data = [{'type': msg.type, 'msg': msg.descr, 'id': msg.id, 'isRead': msg_dict.get(msg.id),
                                      'source': msg.source,'timestamp': msg.op_ts.strftime("%Y-%m-%d %H:%M:%S")}
                            for msg in message_list]
            return self.returnTypeSuc(message_data)
        except Exception as E:
            logging.error(E)
            db_session.rollback()
            return self.requestError()


class MessageRead(BaseHandler):

    '''
    @description: 获取已读消息的消息
    '''

    @tornado.web.authenticated
    def post(self):
        return self.pathError()

    def get_select_condition(self, cls, msg, startTime, endTime, type_):
        """
        输入参数返回筛选条件
        """
        select = []
        if msg:
            select.append(cls.descr.like('%' + msg + '%'))
        if startTime:
            select.append(cls.op_ts >= startTime)
        if endTime:
            select.append(cls.op_ts < endTime)
        if type_:
            select.append(cls.type==type_)
        return select

    @tornado.web.authenticated
    def get(self):
        try:
            session = self.getOrNewSession()
            user = session.user
            msg = self.get_query_argument('msg', None)
            start_time = self.get_query_argument('startTime', None)
            end_time = self.get_query_argument('endTime', None)
            type_ = self.get_query_argument('type', None)
            try:
                pagenum = int(self.get_query_argument('pagenum', '1'))
                pagesize = int(self.get_query_argument('pagesize', '10'))
            except Exception:
                return self.customError('页码不合法')
            msg_select = self.get_select_condition(Message, msg, start_time, end_time,type_)
            user = db_session.query(User).filter(User.id == user['id']).first()
            user_message_ids = db_session.query(UserMessage.message_id, UserMessage.read).filter(and_(
                UserMessage.user_id == user.id, UserMessage.read==True)).all()
            msg_dict = {i[0]: i[1] for i in user_message_ids}
            message_list = db_session.query(Message).filter(and_(Message.id.in_([i[0] for i in user_message_ids]
                                                                                ), *msg_select)).order_by(
                -Message.id).limit(pagesize).offset((pagenum - 1) * pagesize).all()
            total = db_session.query(func.count(Message.id)).filter(and_(Message.id.in_([i[0] for i in user_message_ids]
                                                                                ), *msg_select)).scalar()
            message_data = [{'type': msg.type, 'msg': msg.descr, 'id': msg.id, 'isRead': msg_dict.get(msg.id),
                                      'source': msg.source,'timestamp': msg.op_ts.strftime("%Y-%m-%d %H:%M:%S")}
                            for msg in message_list]
            self.returnTotalSuc(message_data, total)
        except Exception as E:
            logging.error(E)
            db_session.rollback()
            return self.requestError()


class MessageModule(BaseHandler):

    '''
    @description: 修改消息已读状态
    '''

    def post(self, kt):
        self.refreshSession()  # 刷新session
        session = self.getOrNewSession()
        user = session.user
        user = db_session.query(User).filter(User.id == user['id']).first()
        try:
            if kt == 'read':
                msg_id = self.get_argument('id', None)
                if not msg_id:
                    return self.customError('携带完整参数')
                u_msg = db_session.query(UserMessage).filter(
                and_(UserMessage.user_id == user.id, UserMessage.message_id ==msg_id)).first()
                if not u_msg:
                    return self.customError('操作失败')
                u_msg.read = True
                u_msg.read_ts = datetime.datetime.now()
                db_session.commit()
                return self.returnTypeSuc('修改成功')
            elif kt == 'readAll':
                u_msg = db_session.query(UserMessage).filter(UserMessage.user_id == user.id).all()
                for u in u_msg:
                    u.read = True
                    u.read_ts = datetime.datetime.now()
                    db_session.commit()
                return self.returnTypeSuc('修改成功')

            elif kt == 'remove':
                msg_id = self.get_argument('id', None)

                if not msg_id:
                    return self.customError('携带完整参数')
                u_msg = db_session.query(UserMessage).filter(
                        and_(UserMessage.user_id == user.id, UserMessage.message_id ==
                             msg_id)).delete()
                db_session.commit()
                return self.returnTypeSuc('修改成功')
            elif kt == 'removeAll':
                u_msg = db_session.query(UserMessage).filter(UserMessage.user_id == user.id).delete()
                db_session.commit()
                return self.returnTypeSuc('修改成功')

            else:
                return self.pathError()
        except Exception as E:
            logging.info(E)
            db_session.rollback()
            return self.requestError()

    @tornado.web.authenticated
    def get(self, kt):
        if kt == 'add':
            mass = self.get_argument('mass', None)
            org_id = self.get_argument('org_id', None)
            role_id = self.get_argument('role_id', None)
            user_id = self.get_argument('user_id', None)
            msg = self.get_argument('msg', None)
            msg_type = self.get_argument('msg_type', None)
            source = self.get_argument('source', None)

            session = self.getOrNewSession()
            user = session.user
            user = db_session.query(User).filter(User.id == user['id']).first()
            org = db_session.query(Organization).filter(Organization.id == org_id).first()
            if org_id and role_id:
                if mass:
                    push_role_message(role_id, org_id, user.name+'给组织'+str(org.descr)+'及其一下群发了'+msg, 'msg',
                                      datetime.datetime.now(), '1', True)
                    return self.returnTypeSuc('成功')
                else:
                    push_role_message(role_id, org_id, user.name+'给组织'+str(org.descr)+'群发'+msg, 'msg',
                                      datetime.datetime.now(), '1', False)
                    return self.returnTypeSuc('成功')
            else:
                push_user_message(user_id=user_id, content=msg,
                                  msg_type=msg_type, op_ts=datetime.datetime.now().replace(microsecond=0), source=source)
                return self.returnTypeSuc('成功')

        else:
            return self.pathError()
