#!/usr/bin/env python
# coding=utf-8
#@Information:永臻项目
#<AUTHOR> WYJ
#@Date         : 2022-09-02 14:05:17
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\ygzhen_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-02 14:08:44

import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


YGZHEN1_HOSTNAME = model_config.get('mysql', "YGZHEN1_HOSTNAME")
YGZHEN1_PORT = model_config.get('mysql', "YGZHEN1_PORT")
YGZHEN1_DATABASE = model_config.get('mysql', "YGZHEN1_DATABASE")
YGZHEN1_USERNAME = model_config.get('mysql', "YGZHEN1_USERNAME")
YGZHEN1_PASSWORD = model_config.get('mysql', "YGZHEN1_PASSWORD")

YGZHEN2_HOSTNAME = model_config.get('mysql', "YGZHEN2_HOSTNAME")
YGZHEN2_PORT = model_config.get('mysql', "YGZHEN2_PORT")
YGZHEN2_DATABASE = model_config.get('mysql', "YGZHEN2_DATABASE")
YGZHEN2_USERNAME = model_config.get('mysql', "YGZHEN2_USERNAME")
YGZHEN2_PASSWORD = model_config.get('mysql', "YGZHEN2_PASSWORD")


hisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGZHEN1_USERNAME,
    YGZHEN1_PASSWORD,
    YGZHEN1_HOSTNAME,
    YGZHEN1_PORT,
    YGZHEN1_DATABASE
)
ygzhen1_engine = create_engine(hisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=6, pool_pre_ping=True,pool_recycle=1800)
_ygzhen1_session = scoped_session(sessionmaker(ygzhen1_engine,autoflush=True))
ygzhen1_Base = declarative_base(ygzhen1_engine)
ygzhen1_session = _ygzhen1_session()



hisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGZHEN2_USERNAME,
    YGZHEN2_PASSWORD,
    YGZHEN2_HOSTNAME,
    YGZHEN2_PORT,
    YGZHEN2_DATABASE
)
ygzhen2_engine = create_engine(hisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_ygzhen2_session = scoped_session(sessionmaker(ygzhen2_engine,autoflush=True))
ygzhen2_Base = declarative_base(ygzhen2_engine)
ygzhen2_session = _ygzhen2_session()



SYGZHEN1_HOSTNAME = model_config.get('mysql', "SYGZHEN1_HOSTNAME")
SYGZHEN1_PORT = model_config.get('mysql', "SYGZHEN1_PORT")
SYGZHEN1_DATABASE = model_config.get('mysql', "SYGZHEN1_DATABASE")
SYGZHEN1_USERNAME = model_config.get('mysql', "SYGZHEN1_USERNAME")
SYGZHEN1_PASSWORD = model_config.get('mysql', "SYGZHEN1_PASSWORD")

SYGZHEN2_HOSTNAME = model_config.get('mysql', "SYGZHEN2_HOSTNAME")
SYGZHEN2_PORT = model_config.get('mysql', "SYGZHEN2_PORT")
SYGZHEN2_DATABASE = model_config.get('mysql', "SYGZHEN2_DATABASE")
SYGZHEN2_USERNAME = model_config.get('mysql', "SYGZHEN2_USERNAME")
SYGZHEN2_PASSWORD = model_config.get('mysql', "SYGZHEN2_PASSWORD")


shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SYGZHEN1_USERNAME,
    SYGZHEN1_PASSWORD,
    SYGZHEN1_HOSTNAME,
    SYGZHEN1_PORT,
    SYGZHEN1_DATABASE
)
sygzhen1_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=6, pool_pre_ping=True,pool_recycle=1800)
_sygzhen1_session = scoped_session(sessionmaker(sygzhen1_engine,autoflush=True))
sygzhen1_Base = declarative_base(sygzhen1_engine)
sygzhen1_session = _sygzhen1_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SYGZHEN2_USERNAME,
    SYGZHEN2_PASSWORD,
    SYGZHEN2_HOSTNAME,
    SYGZHEN2_PORT,
    SYGZHEN2_DATABASE
)
sygzhen2_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)

_sygzhen2_session = scoped_session(sessionmaker(sygzhen2_engine,autoflush=True))
sygzhen2_Base = declarative_base(sygzhen2_engine)
sygzhen2_session = _sygzhen2_session()




