#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Tools\DataEnDe\MD5.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-21 15:54:29

import hashlib
import os


class MD5Tool(object):

    @classmethod
    def get_file_md5(cls, file_name):
        """
        计算文件的md5
        :param file_name:
        :return:
        """
        m = hashlib.md5()   # 创建md5对象
        with open(file_name, 'rb') as fobj:
            while True:
                data = fobj.read(4096)
                if not data:
                    break
                m.update(data)  # 更新md5对象
        return m.hexdigest()    # 返回md5对象

    @classmethod
    def get_str_md5(cls, content):
        """
        计算字符串md5
        :param content:
        :return:
        """
        m = hashlib.md5(content.encode("utf-8"))  # 创建md5对象
        return m.hexdigest()

    @classmethod
    def GetFileMd5(cls, filename):
        if not os.path.isfile(filename):
            return
        myhash = hashlib.md5()
        f = open(filename, 'rb')
        while True:
            b = f.read(8096)
            if not b:
                break
            myhash.update(b)
        f.close()
        return myhash.hexdigest()
if __name__ == "__main__":
    
    res = MD5Tool.get_str_md5('zOd0/EzXzAmz/w/VX5+jASYG8qB3qBDi3KBnQHic364wdvUhMaP9pVgPk7bAGt0goQt1gXMExxKUWaKOZ0pXXaWth9P4oZOjPazv2hNflensDfejnkyeij1dVsEvo5AIKfhaHW4WdQTwtVk4WERRSQv82tl+Xy6Y3Tniq/LeT+X+WTes60nATZyE95290Wk8yuanchu1')  # 加密
    print ('res:',res) # 
   