package com.robestec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.central.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下发日志记录表
 * 对应Python模型: TPanLogs
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_pan_logs")
public class TPanLogs extends SuperEntity {

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 电站名称
     */
    @TableField("station")
    private String station;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 类型名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 状态: 1-成功, 2-失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否使用: 1-使用, 0-不使用
     */
    @TableField("is_use")
    private Integer isUse;
}
