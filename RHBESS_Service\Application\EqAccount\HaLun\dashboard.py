#!/usr/bin/env python
# coding=utf-8
#@Information:

import ast
import tornado.web
from Application.Models.User.page_drop_down import PageDrop
from Application.Models.User.sda_pcs_cu_name import SdaPcsCu
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Tools.DB.redis_con import r as reids_r
from Tools.TimeTask import get_middle_task
from Tools.DB.redis_con import r_real
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import user_engine, user_session
from Application.Models.base_handler import BaseHandler
from Tools.Utils.num_utils import *
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.event import Event
from Application.Models.User.report_f import FReport
from Application.Models.User.income_r import RIncome
from Application.Models.User.event_r import EventR
from Application.Models.User.station_relation import StationR
from Application.Models.His.r_ACDMS import HisACDMS
from Application.Models.WorkOrder.dispatch_r import DispatchR
from Application.Models.User.alarm_r import AlarmR
from Application.Models.User.station import Station
from sqlalchemy import func,or_
import pandas as pd
from datetime import datetime
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
gz_name = ast.literal_eval(model_config.get('peizhi', 'gz_name') ) #故障名称
pcs_ = json.loads(model_config.get('peizhi', 'pcs'))  #电站名称（PCS）
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']
from Application.Models.User.page import Page
import pymysql
from dbutils.persistent_db import PersistentDB
from dateutil.relativedelta import relativedelta

list_datong = json.loads(model_config.get('peizhi', 'list_datong'))
r=0
from Application.Cfg.dir_cfg import model_config as model_config_base
pool = PersistentDB(pymysql, 10,**{
            "host": model_config_base.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config_base.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config_base.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config_base.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config_base.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })
class DashboardIntetface(BaseHandler):
    ''' 自定义功能汇总 '''
    @tornado.web.authenticated
    def get(self,kt):
        global r
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        session = self.getOrNewSession()
        station_names= session.user.get('station_name', '')
        if not station_names:
            self.redirect("/UserLogin/Login")
        user_id = str(session.user['id'])  # 当前用户id
        pcs = []
        for i in pcs_:
            for ii in station_names:
                if ii in i:
                    pcs.append(i)
                elif ii == 'baodian':
                    if 'bodian' in i:
                        pcs.append(i)
        try:
        # if 1:
            data = []
            if kt == 'GetLargeScreen':  # 大屏电站总数据
                returndata = {'1':0,'2':0,'3':0,'4':0}
                state = {'1': 0, '2': 0, '3': 0, '4': 0}#运行状态
                rundays = 0
                volume_total = {'1':0,'2':0,'3':0,'4':0}  # 总容量
                nowStr = timeUtils.getNewDayStartStr()
                pages = user_session.query(Station.name,Station.volume,Station.longitude,Station.dimension,StationR.energy_storage,
                                           StationR.running_state,Station.start_ts,StationR.electric_power).filter(Station.name == StationR.station_name).order_by(Station.index.asc()).all()#获取电站
                list_name=[]
                for pag in pages:
                    descr = {}
                    if pag[0] not in list_name:
                        descr['name'] = pag[0]
                        descr['volume'] = pag[1]
                        descr['longitude'] = pag[2]
                        descr['dimension'] = pag[3]
                        descr['energy_storage'] = pag[4]
                        descr['electric_power'] = pag[7]
                        data.append(descr)
                        list_name.append(pag[0])
                        if pag[4] in returndata.keys():
                            returndata[pag[4]] += 1
                            volume_total[pag[4]] += pag[1]
                        else:
                            returndata[pag[4]] = 1
                            volume_total[pag[4]] = pag[1]
                        if pag[5] in state.keys():
                            state[pag[5]] += 1
                        else:
                            state[pag[5]] = 1

                        if pag[6]:
                            dd = timeUtils.betweenDayNum(str(pag[6]), nowStr)
                            if dd > rundays:
                                rundays = dd
                res = user_session.query(func.date_format(EventR.op_ts, '%Y-%m-%d').label('date'),
                                         func.count('*').label('cnt')).filter(EventR.event_id == Event.id,
                                                                              Event.type_id == 5,
                                                                              EventR.station == 'his').group_by('date').all()
                rundays = rundays - len(res)
                data = {'data': data, "info": returndata, "rundays": rundays,"state":state,"volume_total":volume_total}
                return self.returnTypeSuc(data)
            elif kt == 'GetElectricQuantityData': #获取电量数据
                now_time = timeUtils.getAgoTime(1)
                old_time = timeUtils.getAgoTime(7)
                two_time_lists = timeUtils.dateToDataList(old_time, now_time)
                obj = {}
                two_time_lists_ = []
                data1, data2 ,data3 = [], [], [] # 放电,日效率,充电
                for day in two_time_lists:
                    freport = user_session.query(FReport).filter(FReport.day == day + ' 00:00:00',
                                                                 FReport.name.in_(pcs),
                                                                 FReport.cause == 1).all()
                    two_time_lists_.append(day[5:])
                    disg = 0 #放电量
                    chag = 0 #放电量
                    count = 0 #收益条数
                    sumratio = 0.0 #总收益
                    if freport:
                        for f in freport:
                            disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(
                                eval(f.pd_disg)) + np.sum(eval(f.gd_disg))

                            chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(
                                eval(f.pd_chag)) + np.sum(eval(f.gd_chag))

                            if f.ratio != 0:
                                sumratio += float(f.ratio)
                                count += 1
                        data1.append(('%.2f'% (disg/1000)))
                        data3.append(('%.2f'% (chag/1000)))
                        if count == 0:
                            data2.append(0)
                        else:
                            data2.append(('%.2f'%(sumratio / count)))
                    else:
                        data1.append('0.00')
                        data2.append(0)
                        data3.append('0.00')
                obj['day_disgCapy'] = data1  # 日放电
                obj['day_efficiency'] = data2#日效率
                obj['day_chagCapy'] = data3  # 日充电
                obj['time'] = two_time_lists_ #时间维度
                return self.returnTypeSuc(obj)
            elif kt == 'GetIncometData':  # 获取收益数据
                lang = self.get_argument('lang', None)  # 英文
                returndata = {}
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:7]
                data1 = []#半年收益
                now_month = timeUtils.getAgoMonth(1)
                old_month = timeUtils.getAgoMonth(6)
                two_month_list = timeUtils.getBetweenMonthYm(old_month,now_month)
                for month in two_month_list:
                    rincome_month = user_session.query(RIncome).filter(RIncome.day == month,
                                                                 RIncome.is_use == 1,RIncome.station_name.in_(station_names)).all()
                    income = 0.00
                    if rincome_month:
                        for r in rincome_month:
                            income += float(r.income)
                        data1.append(('%.2f'% income))
                    else:
                        data1.append(0)
                returndata['half_a_year_income'] = data1#半年收益
                returndata['income_time'] = two_month_list#时间维度
                filter2 = []
                filter2.append(RIncome.is_use == 1)
                filter2.append(RIncome.day == str(datestart))
                rincome = user_session.query(func.sum(RIncome.income)).filter( RIncome.in_ts == 2, RIncome.is_use == 1,RIncome.income != '0',RIncome.station_name.in_(station_names)).group_by(RIncome.day).order_by(RIncome.day.desc()).first()
                if rincome:
                    income = float(rincome[0])
                else:
                    income = 0
                if lang == 'en':
                    income=income*10000
                    if income < 0:
                        month_income_res = '--'
                        month_income_unit = 'k'
                    elif 0 <= income < 1000000:
                        month_income_res = ('%.1f' % (income/1000))
                        month_income_unit = 'k'
                    elif 1000000<= income < 100000000:
                        month_income_res = ('%.1f' % (income/1000000))
                        month_income_unit = 'M'
                    else:
                        month_income_res = ('%.1f' % (income / 1000000000))
                        month_income_unit = 'B'
                else:
                    if income < 0:
                        month_income_res = '--'
                        month_income_unit = '元'
                    elif 0 <= income < 1:
                        month_income_res = ('%.2f' % (income*10000))
                        month_income_unit = '元'
                    elif income >= 10000:
                        month_income_res = ('%.2f' % (income/10000))
                        month_income_unit = '亿元'
                    else:
                        month_income_res = ('%.2f' % income)
                        month_income_unit = '万元'
                returndata['month_income'] = month_income_res #前月收益
                returndata['month_income_unit'] = month_income_unit  # 前月收益单位
                yearstr = now_time[0:4]
                rincome_year = user_session.query(RIncome).filter(RIncome.day == str(yearstr),RIncome.is_use == 1,RIncome.station_name.in_(station_names)).all()
                income_year = 0.0#当年收益
                if rincome:
                    for r in rincome_year:
                        income_year += float(r.income)
                if lang=='en':
                    income_year = income_year * 10000
                    if income_year < 0:
                        income_year_res = '--'
                        income_year_unit = 'k'
                    elif 0 <= income_year < 1000000:
                        income_year_res = ('%.1f' % (income_year / 1000))
                        income_year_unit = 'k'
                    elif 1000000 <= income_year < 100000000:
                        income_year_res = ('%.1f' % (income_year / 1000000))
                        income_year_unit = 'M'
                    else:
                        income_year_res = ('%.1f' % (income_year / 1000000000))
                        income_year_unit = 'B'
                else:
                    if income_year < 0:
                        income_year_res = '--'
                        income_year_unit = '元'
                    elif 0 <= income_year < 1:
                        income_year_res = ('%.2f' % (income_year*10000))
                        income_year_unit = '元'
                    elif income_year >= 10000:
                        income_year_res = ('%.2f' % (income_year/10000))
                        income_year_unit = '亿元'
                    else:
                        income_year_res = ('%.2f' % income_year)
                        income_year_unit = '万元'
                returndata['income_year'] = income_year_res #当年收益
                returndata['income_year_unit'] = income_year_unit  # 单位
                #总收益
                total_income = user_session.query(func.sum(RIncome.income)).filter(RIncome.in_ts == 1,RIncome.station_name.in_(station_names)).first()
                if lang == 'en':
                    total_income = float(total_income[0]) * 10000
                    if total_income < 0:
                        total_income_res = '--'
                        total_income_unit = 'k'
                    elif 0 <= total_income < 1000000:
                        total_income_res = ('%.1f' % (total_income / 1000))
                        total_income_unit = 'k'
                    elif 1000000 <= total_income < 100000000:
                        total_income_res = ('%.1f' % (total_income / 1000000))
                        total_income_unit = 'M'
                    else:
                        total_income_res = ('%.1f' % (total_income / 1000000000))
                        total_income_unit = 'B'
                else:
                    if total_income[0] <0 :
                        total_income_res = '--'
                        total_income_unit = '元'
                    elif 0 <= total_income[0] < 1:
                        total_income_res = ('%.2f' % (total_income[0]*10000))
                        total_income_unit = '元'
                    elif total_income[0] >= 10000:
                        total_income_res = ('%.2f' % (total_income[0]/10000))
                        total_income_unit = '亿元'
                    else:
                        total_income_res = ('%.2f' % total_income[0])
                        total_income_unit = '万元'
                returndata['total_income'] = total_income_res  # 累计总收益
                returndata['total_income_unit'] = total_income_unit  # 累计总收益单位
                now_time = timeUtils.getNewTimeStr()
                datestart = []
                old_time1, old_time2 = timeUtils.getPreviousMonthDay(now_time)  # 前一个月
                datestart.append(old_time1[0:7])
                old_old_time1, old_old_time2 = timeUtils.getPreviousMonthDay(old_time1)  # 再前一个月
                datestart.append(old_old_time1[0:7])
                stationnameStr = "','".join(station_names)
                datestartStr = "','".join(datestart)
                sql = "select a.day,b.energy_storage,sum(a.income)/sum(c.volume)/100 from r_income a left join t_station_relation b on a.station_name = b.station_name " \
                      "left join t_station c on a.station_name = c.name where a.in_ts='2' and a.day in ('{0}') and a.is_use='1' and a.station_name in('{1}')" \
                      "group by b.energy_storage,a.day ".format(datestartStr, stationnameStr)
                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                cursor.execute(sql)
                result = cursor.fetchall()
                cursor.close()
                conn.close()
                data_1 = ['0.00','0.00','0.00','0.00']
                data_2 = ['0.00','0.00','0.00','0.00']
                station_list_energy_storage = {}
                if result:
                    for r in result:
                        if r[0] == datestart[1]:
                            if r[1] == '1':
                                data_1[0] = '%.1f' % (r[2]*1000)
                            elif r[1] == '2':
                                # data_1[1] = '0.02'
                                data_1[1] = '%.1f' % (r[2]*1000)
                            elif r[1] == '3':
                                data_1[2] = '%.1f' % (r[2]*1000)
                            elif r[1] == '4':
                                data_1[3] = '%.1f' % (r[2]*1000)
                        elif r[0] == datestart[0]:
                            if r[1] == '1':
                                data_2[0] = '%.1f' % (r[2]*1000)
                            elif r[1] == '2':
                                data_2[1] = '%.1f' % (r[2]*1000)
                            elif r[1] == '3':
                                data_2[2] = '%.1f' % (r[2]*1000)
                            elif r[1] == '4':
                                data_2[3] = '%.1f' % (r[2]*1000)
                station_list_energy_storage['data1'] = data_1
                station_list_energy_storage['data2'] = data_2
                station_list_energy_storage['time'] = datestart[::-1]
                returndata['station_list_energy_storage'] = station_list_energy_storage  # 单位容量收益
                return self.returnTypeSuc(returndata)
            elif kt == 'GetProvinceStation':  # 获取省份电站
                toal_volume = 0#总装机容量
                province = self.get_argument('province', None)
                lang = self.get_argument('lang', None)  # 英文
                filter = []
                if province:
                    filter.append(StationR.province==province)
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                #根据省份查询电站
                if user_id == '131':  # demo站用户
                    station = user_session.query(Station.descr,StationR.running_state,Station.volume,Station.id,Station.name,Station.longitude,Station.dimension,Station.en_descr).\
                                                            filter(Station.id == StationR.id,Station.id==37,StationR.running_state==1).filter(*filter).order_by(desc(StationR.electric_power)).all()
                else:
                    station = user_session.query(Station.descr,StationR.running_state,Station.volume,Station.id,Station.name,Station.longitude,Station.dimension,Station.en_descr).\
                                                            filter(StationR.id!=37,Station.id!=37,Station.name == StationR.station_name,Station.name.in_(station_names),StationR.running_state==1).filter(*filter).order_by(desc(StationR.electric_power)).all()
                for pag in station:
                    if lang=='en':
                        data.append({'name': pag[4], 'descr': pag[7], 'longitude': pag[5], 'dimension': pag[6], 'zh_descr':pag[0]})
                    else:
                        data.append({'name': pag[4], 'descr': pag[0],'longitude': pag[5],'dimension': pag[6], 'zh_descr':pag[0]})
                    if pag[2]:
                        toal_volume += pag[2]
                rincome = user_session.query(RIncome.income,RIncome.target_income,RIncome.descr,RIncome.en_descr,RIncome.station_name).filter(*filter).filter(RIncome.station_name == StationR.station_name, RIncome.in_ts == 1,
                    RIncome.is_use == 1,RIncome.day.like(datestart + '%'),RIncome.station_name.in_(station_names)).all()
                for d in data:
                    if rincome:
                        for r in rincome:
                            if lang == 'en':
                                r = eval(str(r))
                            zh_descr = r[2]
                            if lang=='en':
                                descr = r[3]
                            else:
                                descr = r[2]
                            if descr == d['descr'] or zh_descr == d['zh_descr']:
                                income = float(r[0])
                                target_income = float(r[1])
                                if target_income == 0:
                                    station_year_achieved = '0.00'
                                elif not income:
                                    station_year_achieved = '0.00'
                                elif income and target_income:
                                    try:
                                        station_year_achieved = ('%.2f' % (income / target_income * 100))
                                    except:
                                        station_year_achieved = '0.00'
                                d.update({'station_year_achieved': station_year_achieved})
                        if 'station_year_achieved' not in d.keys():
                            d['station_year_achieved'] = '0'
                    else:
                        d.update({'station_year_achieved': 0})
                data = {'data': data,  "toal_volume": ('%.2f' % toal_volume)}
                return self.returnTypeSuc(data)
            elif kt == 'GetProvinceHisData': #获取省历史数据
                province = self.get_argument('province', None)
                filter = []
                if province:
                    filter.append(StationR.province == province)
                now_time = timeUtils.getAgoTime(1)
                old_time = timeUtils.getAgoTime(7)
                returndata = {}
                count = 0
                station_name_list = [-1, -2] #电站列表 防止查询异常
                if user_id == '131': # demo账号
                    station_name = user_session.query(StationR.station_name).filter(*filter).filter(StationR.id==37).all()
                else:
                    station_name = user_session.query(StationR.station_name).filter(*filter).filter(StationR.station_name.in_(station_names),Station.id!=37,StationR.id!=37,
                                                                                                    Station.name==StationR.station_name).all()
                for s in station_name:
                    n = eval(str(s))
                    station_name_ = str(n[0])
                    station_name_list.append(station_name_)
                dws_conn = pool.connection()
                dws_cursor = dws_conn.cursor()
                sql = f"""SELECT
                            avg( ratio )  as ratio
                        FROM
                            `dws_rhyc_ES_station`.`dws_st_measure_cw_cm_cy`
                        WHERE
                            station_name in {tuple(station_name_list)}
                            AND date_type = 'year'
                        """
                dws_cursor.execute(sql)
                ratio_total = dws_cursor.fetchone()
                ratio_total = round(ratio_total['ratio'], 2) if ratio_total.get('ratio') else '--'

                if user_id == '131':
                    rincome = user_session.query(func.sum(RIncome.income)).filter(*filter).filter(RIncome.station_name == StationR.station_name,StationR.id == 37,RIncome.income != '0',RIncome.in_ts == 2,RIncome.is_use == 1,RIncome.station_name.in_(station_names)).group_by(RIncome.day).order_by(RIncome.day.desc()).first()
                else:
                    rincome = user_session.query(func.sum(RIncome.income)).filter(*filter).filter(RIncome.station_name == StationR.station_name,StationR.id != 37,RIncome.income != '0',RIncome.in_ts == 2,RIncome.is_use == 1,RIncome.station_name.in_(station_names)).group_by(RIncome.day).order_by(RIncome.day.desc()).first()
                if rincome:
                    month_income = float(rincome[0])
                else:
                    month_income = 0
                if lang=='en':
                    month_income = month_income * 10000
                    if month_income < 0:
                        month_income_res = '--'
                        month_income_unit = 'k'
                    elif 0 <= month_income < 1000000:
                        month_income_res = ('%.1f' % (month_income / 1000))
                        month_income_unit = 'k'
                    elif 1000000 <= month_income < 100000000:
                        month_income_res = ('%.1f' % (month_income / 1000000))
                        month_income_unit = 'M'
                    else:
                        month_income_res = ('%.1f' % (month_income / 1000000000))
                        month_income_unit = 'B'
                else:
                    if month_income < 0:
                        month_income_res = '--'
                        month_income_unit = '元'
                    elif 0 <= month_income < 1:
                        month_income_res = ('%.2f' % (month_income * 10000))
                        month_income_unit = '元'
                    elif month_income >= 10000:
                        month_income_res = ('%.2f' % (month_income / 10000))
                        month_income_unit = '亿元'
                    else:
                        month_income_res = ('%.2f' % month_income)
                        month_income_unit = '万元'
                returndata['month_income'] = month_income_res  # 月收益
                returndata['month_income_unit'] = month_income_unit  # 月收益单位
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                stationnameStr = "','".join(station_names)
                if province:
                    sql = "select b.station_name,b.energy_storage,COALESCE(sum( a.income )/ sum( c.volume )/ 100,0) from t_station_relation b left join r_income a on a.station_name = b.station_name AND a.in_ts = '1' AND a.day='{0}' and a.is_use='1'" \
                          "left join t_station c on a.station_name = c.name where b.province='{1}'" \
                          " and b.running_state='1' and b.station_name in('{2}')" \
                          "group by b.energy_storage,b.station_name order by b.energy_storage,sum(a.income)/sum(c.volume) desc".format(datestart,province,stationnameStr)
                else:
                    sql = "select b.station_name,b.energy_storage,COALESCE(sum( a.income )/ sum( c.volume )/ 100,0) from t_station_relation b left join r_income a on a.station_name = b.station_name AND a.in_ts = '1' AND a.day='{0}' and a.is_use='1'" \
                          "left join t_station c on a.station_name = c.name where " \
                          " b.running_state='1' and b.station_name in('{1}')" \
                          "group by b.energy_storage,b.station_name order by b.energy_storage,sum(a.income)/sum(c.volume) desc".format(
                        datestart, stationnameStr)
                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                cursor.execute(sql)
                result = cursor.fetchall()
                cursor.close()
                conn.close()
                station_list_energy_storage = []
                station_list_energy_storage_3 = []
                if result:
                    for r in result:
                        data2 = {'name': r[0], 'revenue_per_unit_capacity': '%.2f' % r[2]}
                        station_list_energy_storage.append({r[1]:data2})
                        station_list_energy_storage_3.append(data2)
                returndata['station_list_energy_storage'] = station_list_energy_storage  # 单位容量收益
                returndata['station_list_energy_storage_3'] = station_list_energy_storage_3  # 单位容量收益(vue3)
                # 查询总收益
                if user_id == '131':
                    total_income = user_session.query(func.sum(RIncome.income)).filter(*filter).filter(RIncome.station_name ==
                                                                                   StationR.station_name,StationR.id==37,RIncome.in_ts == 1,RIncome.station_name.in_(station_names)).all()
                else:
                    total_income = user_session.query(func.sum(RIncome.income)).filter(*filter).filter(RIncome.station_name ==
                                                                                   StationR.station_name,StationR.id!=37,RIncome.in_ts == 1,RIncome.station_name.in_(station_names)).all()
                if total_income[0][0]:
                    if lang=='en':
                        total_income = float(total_income[0][0]) * 10000
                        if total_income < 0:
                            total_income_res = '--'
                            total_income_unit = 'k'
                        elif 0 <= total_income < 1000000:
                            total_income_res = ('%.1f' % (total_income/ 1000))
                            total_income_unit = 'k'
                        elif 1000000 <= total_income < 100000000:
                            total_income_res = ('%.1f' % (total_income / 1000000))
                            total_income_unit = 'M'
                        else:
                            total_income_res = ('%.1f' % (total_income / 1000000000))
                            total_income_unit = 'B'
                    else:
                        if total_income[0][0] < 0:
                            total_income_res = '--'
                            total_income_unit = '元'
                        elif 0 <= total_income[0][0] < 1:
                            total_income_res = ('%.2f' % total_income[0][0] * 10000)
                            total_income_unit = '元'
                        elif total_income[0][0] >= 10000:
                            total_income_res = ('%.2f' % (total_income[0][0] / 10000))
                            total_income_unit = '亿元'
                        else:
                            total_income_res = ('%.2f' % total_income[0][0])
                            total_income_unit = '万元'
                    returndata['total_income'] = total_income_res  # 累计总收益
                    returndata['total_income_unit'] = total_income_unit  # 累计总收益单位
                returndata['overall_efficiency'] = ratio_total#整体效率
                return self.returnTypeSuc(returndata)   #
            elif kt == 'GetProvinceIncomeCom':  # 获取省收益达产率
                province = self.get_argument('province', None)
                filter = []
                if province:
                    filter.append(StationR.province == province)
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                returndata = {}
                time_list = timeUtils.getBetweenMonthYm(datestart+'-01',datestart+'-12')
                year_income = 0
                year_target_income = 0
                returndata['target_income'] = []
                returndata['mon_yield_achieved'] = []
                returndata['income'] = []
                stationnameStr = "','".join(station_names)
                if province:
                    sql = "select b.income,b.target_income,b.day,a.station_name,c.descr,c.en_descr from t_station_relation a " \
                          "left join r_income b on a.station_name = b.station_name " \
                          "and b.in_ts='2' and b.is_use='1' and b.day like '{0}' " \
                          "left join t_station c on c.name =a.station_name " \
                          "where a.station_name in('{1}') and a.id!=37 and c.id!=37  and a.province='{2}' order by day asc".format((datestart+'%'), stationnameStr, province)
                else:
                    sql = "select b.income,b.target_income,b.day,a.station_name,c.descr,c.en_descr from t_station_relation a " \
                          "left join r_income b on a.station_name = b.station_name " \
                          "and b.in_ts='2' and b.is_use='1' and b.day like '{0}' " \
                          "left join t_station c on c.name =a.station_name " \
                          "where a.station_name in('{1}') and a.id!=37 and c.id!=37  order by day asc".format((datestart + '%'),stationnameStr)
                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                cursor.execute(sql)
                result = cursor.fetchall()
                cursor.close()
                conn.close()
                rincome_ = user_session.query(func.sum(RIncome.income), func.sum(RIncome.target_income), RIncome.day).filter(*filter).filter(
                    RIncome.station_name == StationR.station_name, RIncome.in_ts == 2,RIncome.is_use == 1,
                    RIncome.day.like(datestart + '%'),RIncome.station_name.in_(station_names)).group_by(RIncome.day).order_by(RIncome.day.asc()).all()
                returndata['data'] = []#单电站实际收益
                aaa = {}#按单个单站分类，以电站名称为key
                if result:
                    for r in result:
                        if lang=='en':
                            descr = r[5]
                        else:
                            descr = r[4]
                        if r[2]:
                            day = r[2]
                            if r[0]:
                                year_income += float(r[0])
                            if r[1]:
                                year_target_income += float(r[1])

                            stationname = r[3]
                            if stationname not in aaa.keys():
                                aaa[stationname] = []
                            if lang == 'en':
                                aaa[stationname].append({'station_name':stationname,'en_descr':descr,'income':('%.2f' % float(r[0])),'day':day})
                            else:
                                aaa[stationname].append({'station_name': stationname, 'descr': descr, 'income': ('%.2f' % float(r[0])),'day': day})
                        elif r[2] == None:
                            stationname = r[3]
                            aaa[stationname] = []
                            if lang == 'en':
                                aaa[stationname].append({'station_name': stationname, 'en_descr': descr, 'income': '0', 'day': r[2]})
                            else:
                                aaa[stationname].append({'station_name': stationname, 'descr': descr, 'income': '0','day': r[2]})
                if aaa:
                    for station in aaa.keys():
                        incomes = []
                        descr = ''
                        bbb = aaa[station]#station电站1到12月份数据
                        for time in time_list:
                            if len(bbb)>time_list.index(time) and time == bbb[time_list.index(time)]['day']:
                                d = bbb[time_list.index(time)]
                                if not descr:
                                    if lang=='en':
                                        descr = d['en_descr']
                                    else:
                                        descr = d['descr']
                                if not d['income']:
                                    incomes.append('0.00')
                                else:
                                    incomes.append('%.2f' % float(d['income']))
                            else:
                                if lang == 'en':
                                    descr = bbb[0]['en_descr']
                                else:
                                    descr = bbb[0]['descr']
                                incomes.append('0.00')
                        returndata['data'].append({'descr':descr,'income':incomes})
                else:
                    returndata['data'] = []
                if rincome_:
                    for r in rincome_:
                        for time in time_list:
                            if time == r[2]:
                                if r[0]:
                                    returndata['income'].append(r[0])
                                else:
                                    returndata['income'].append(0)
                                if r[1]:
                                    returndata['target_income'].append(r[1])
                                else:
                                    returndata['target_income'].append(0)
                                if r[1] == 0:
                                    returndata['mon_yield_achieved'].append(0)
                                else:
                                    try:
                                        returndata['mon_yield_achieved'].append('%.2f' % (r[0] / r[1]* 100))  # 月达产率
                                    except:
                                        returndata['mon_yield_achieved'].append(0)
                    returndata['target_income'] = np.round(np.array(returndata['target_income']), 2).tolist()  # 月目标收益
                else:
                    returndata['income']=['0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00']
                    returndata['target_income'] =['0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00']
                    returndata['mon_yield_achieved']=['0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00']
                if year_target_income == 0:
                    returndata['year_yield_achieved'] = 0
                else:
                    try:
                        returndata['year_yield_achieved'] = '%.2f' % (year_income / year_target_income * 100) # 年达产率
                    except:
                        returndata['year_yield_achieved'] = 0
                if result:
                    returndata['time'] = time_list  # 时间
                else:
                    returndata['time'] = []  # 时间
                if lang == 'en':
                    return self.returnTypeSuc_en(data=returndata, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=returndata, info=None, lang=None)
            elif kt == 'GetProvinceStationRealTime':  # 省份电站实时数据
                province = self.get_argument('province', None)
                returndata = {}
                filter = []
                if province:
                    filter.append(StationR.province == province)
                if user_id == '131': # demo账号
                    datalist = user_session.query(StationR.active_power_name,StationR.chag_capacity_name,StationR.disg_capacity_name).filter(*filter).filter(StationR.running_state==1,StationR.id==37).all()
                else:
                    datalist = user_session.query(StationR.active_power_name,StationR.chag_capacity_name,StationR.disg_capacity_name).filter(*filter).filter(StationR.running_state==1,StationR.id!=37,StationR.station_name.in_(station_names)).all()
                negative_active_power_sum = 0.0  # 充电功率
                positive_active_power_sum = 0.0  # 放电功率
                rechargeable_capacity = 0.0 #充电容量
                discharge_capacity = 0.0  # 可放电量
                if province == '浙江省':
                    redisdata = real_data('measure', 'dongmu', 'db')
                    for ii in redisdata['body']:
                        if ii['device'][:3] == 'PCS':
                            actPo = ii['actPo']  # 有功
                            if float(actPo) < 0:
                                negative_active_power_sum += float(actPo)
                            elif float(actPo) >= 0:
                                positive_active_power_sum += float(actPo)
                        if ii['device'][:3] == 'BMS':
                            ChCap = ii['ChCap']  # 可充电量
                            DiCap = ii['DiCap']  # 可放电量
                            if float(ChCap) < 65535:
                                rechargeable_capacity += float(ChCap)
                            if float(DiCap) < 65535:
                                discharge_capacity += float(DiCap)
                else:
                    if datalist:
                        for data in datalist:
                            active_power_names = eval(str(data[0]))  # 计算有功功率
                            chag_capacity_names = eval(str(data[1]))  # 计算充电容量
                            negative_active_power_sum_ = 0.0  # 充电功率
                            positive_active_power_sum_ = 0.0  # 放电功率
                            rechargeable_capacity_ = 0.0  # 充电容量
                            discharge_capacity_ = 0.0  # 可放电量
                            if not active_power_names or not chag_capacity_names:
                                redisdata = real_data('measure', 'dongmu', 'db')
                                if redisdata:
                                    try:
                                        for ii in redisdata['body']:
                                            if ii['device'][:3] == 'PCS':
                                                actPo = ii['actPo']  # 有功
                                                if float(actPo) < 0:
                                                    negative_active_power_sum_ += float(actPo)
                                                elif float(actPo) >= 0:
                                                    positive_active_power_sum_ += float(actPo)
                                            if ii['device'][:3] == 'BMS':
                                                ChCap = ii['ChCap']  # 可充电量
                                                DiCap = ii['DiCap']  # 可放电量
                                                if float(ChCap) < 65535:
                                                    rechargeable_capacity_ += float(ChCap)
                                                if float(DiCap) < 65535:
                                                    discharge_capacity_ += float(DiCap)
                                    except:
                                        pass
                            else:
                                for active_power in active_power_names:
                                    o = real_data('measure', active_power, 'db')
                                    if o['value'] == '--':
                                        continue
                                    else:
                                        if o['value'] < 0:
                                            negative_active_power_sum += o['value']
                                        elif o['value'] >= 0:
                                            positive_active_power_sum += o['value']
                                for chage_capacity in chag_capacity_names:
                                    o = real_data('measure', chage_capacity, 'db')
                                    if o['value'] == '--':
                                        continue
                                    else:
                                        if o['value'] < 65535:
                                            rechargeable_capacity += o['value']
                                disg_capacity_names = eval(str(data[2]))  # 计算放电容量
                                for disg_capacity in disg_capacity_names:
                                    o = real_data('measure', disg_capacity, 'db')
                                    if o['value'] == '--':
                                        continue
                                    else:
                                        if o['value'] < 65535:
                                            discharge_capacity += o['value']
                                if negative_active_power_sum == '--' or positive_active_power_sum == '--' or rechargeable_capacity == '--' or discharge_capacity == '--':
                                    negative_active_power_sum = '--'
                                    positive_active_power_sum = '--'
                                    rechargeable_capacity = '--'
                                    discharge_capacity = '--'
                                else:
                                    negative_active_power_sum = negative_active_power_sum + negative_active_power_sum_
                                    positive_active_power_sum = positive_active_power_sum + positive_active_power_sum_
                                    rechargeable_capacity = rechargeable_capacity+rechargeable_capacity_
                                    discharge_capacity = discharge_capacity+discharge_capacity_
                returndata['negative_active_power_sum'] = ('%.2f' % abs(negative_active_power_sum)) if negative_active_power_sum != '--' else '--'  # 充电功率
                returndata['positive_active_power_sum'] = ('%.2f' % abs(positive_active_power_sum)) if positive_active_power_sum != '--' else '--' # 放电功率
                returndata['rechargeable_capacity'] = ('%.2f' % rechargeable_capacity) if rechargeable_capacity != '--' else '--'  # 充电量
                returndata['discharge_capacity'] = ('%.2f' % discharge_capacity) if discharge_capacity != '--' else '--'  # 放电量
                return self.returnTypeSuc(returndata)
            elif kt == 'GetProvinceStationHisPower':
                province = self.get_argument('province', None)
                if user_id == '131': # demo账号
                    station_name = user_session.query(StationR.station_name).filter(StationR.province == province,StationR.id==37).all()
                else:
                    station_name = user_session.query(StationR.station_name).filter(StationR.province == province,StationR.station_name.in_(station_names),StationR.id!=37).all()
                all_obj = {'time':[]}  # 数据返回结构
                for s in station_name:
                    s_n = s[0]
                    v = reids_r.get(s_n + '_power')
                    if not v:
                        all_obj[s_n] = []
                    else:
                        vv = eval(str(v))
                        all_obj[s_n] = vv['value']
                        if not all_obj['time']:
                            all_obj['time']=vv['time']
                return self.returnTypeSuc(all_obj)
            elif kt == 'GetRight': #日利用小时数
                data = {'1': {'day':0,'his': 0, 'stand': 0}, '2': {'day':0,'his': 0, 'stand': 0}, '3': {'day':0,'his': 0, 'stand':0},'4': {'day':'0.000','his': '0.000', 'stand':  '1.440'}}
                freport = user_session.query(StationR.energy_storage, func.sum(FReport.jf_chag),
                                             func.sum(FReport.jf_disg)).filter(FReport.name == StationR.station_name, FReport.name.in_(station_names), FReport.cause == 3).group_by(StationR.energy_storage).all()
                disg_chag_1 = 0  # #火储能充放电量
                disg_chag_2 = 0  # #工商业储能充放电量
                disg_chag_3 = 0  # #集中式储能充放电量
                disg_chag_4 = 0  # #新能源储能充放电量
                if freport:#历史充放电量
                    for f in freport:
                        if f[0] == '1':
                            disg_chag_1 = float(f[1])+float(f[2])
                        elif f[0] == '2':
                            disg_chag_2 = float(f[1])+float(f[2])
                        elif f[0] == '3':
                            disg_chag_3 = float(f[1])+float(f[2])
                        elif f[0] == '4':
                            disg_chag_4 = float(f[1])+float(f[2])
                energy_storage_day = user_session.query(StationR.energy_storage, StationR.station_name,StationR.ChagCapy,StationR.DisgCapy).filter(StationR.station_name.in_(station_names), StationR.running_state == '1').group_by(StationR.energy_storage).all()  # 获取电站类型
                disg_chag_1_day = 0  # #火储能充放电量
                disg_chag_2_day = 0  # #工商业储能充放电量
                disg_chag_3_day = 0  # #集中式储能充放电量
                disg_chag_4_day = 0  # #新能源储能充放电量
                #计算当日充放电量
                if energy_storage_day:
                    for e in energy_storage_day:
                        if e[0] == '1':
                            disg_chag_1_day +=  int(e[2])+ int(e[3])
                        elif e[0] == '2':
                            disg_chag_2_day+=  int(e[2])+ int(e[3])
                        elif e[0] == '3':
                            disg_chag_3_day+=  int(e[2])+ int(e[3])
                        elif e[0] == '4':
                            disg_chag_4_day+=  int(e[2])+ int(e[3])
                if user_id == '131':  # demo站用户
                    start_ts = user_session.query(StationR.energy_storage,Station.start_ts).filter(Station.name == StationR.station_name, Station.id==37,
                        StationR.running_state == '1').group_by(StationR.energy_storage).all()  # 查询开始运营时间
                    energy_storage = user_session.query(StationR.energy_storage,func.sum(StationR.electric_power)).filter( StationR.id==37,StationR.running_state == '1').group_by(StationR.energy_storage).all()  # 获取电站额定功率
                else:
                    start_ts = user_session.query(StationR.energy_storage,Station.start_ts).filter(Station.name == StationR.station_name, Station.name.in_(station_names),
                        StationR.running_state == '1',Station.id!=37,StationR.id!=37).group_by(StationR.energy_storage).all()  # 查询开始运营时间
                    energy_storage = user_session.query(StationR.energy_storage,func.sum(StationR.electric_power)).filter( StationR.station_name.in_(station_names),StationR.id!=37,StationR.running_state == '1').group_by(StationR.energy_storage).all()  # 获取电站额定功率
                day = timeUtils.getBeforeDay() # 获取前一天时间 YYYY-mm-dd
                dd_1 = 0 #火储能运行天数之和
                dd_2 = 0 #工商业储能运行天数之和
                dd_3 = 0 #集中式储能运行天数之和
                dd_4 = 0 #新能源储能运行天数之和
                if start_ts:
                    for i in start_ts:
                        if i[0] == '1':
                            dd_1 =dd_1 + (timeUtils.betweenDayNum(str(i[1]), day) + 1)
                        elif i[0] == '2':
                            dd_2 =dd_2 + (timeUtils.betweenDayNum(str(i[1]), day) + 1)
                        elif i[0] == '3':
                            dd_3 =dd_3 + (timeUtils.betweenDayNum(str(i[1]), day) + 1)
                        elif i[0] == '4':
                            dd_4 =dd_4 + (timeUtils.betweenDayNum(str(i[1]), day) + 1)
                #计算日均利用小时数
                if energy_storage:
                    for n in energy_storage:
                        if n[0] == '1':
                            data['1'] = {'day':('%.3f' % (disg_chag_1_day / (n[1] * 1000))),'his': ('%.3f' % (disg_chag_1 / (n[1] * 1000) / dd_1)), 'stand': '0.770'}
                        elif n[0] == '2':
                            data['2'] = {'day':('%.3f' % (disg_chag_2_day / (n[1] * 1000))),'his': ('%.3f' % (disg_chag_2 / (n[1] * 1000) / dd_2)), 'stand': '4.440'}
                        elif n[0] == '3':
                            data['3'] = {'day':('%.3f' % (disg_chag_3_day / (n[1] * 1000))),'his': ('%.3f' % (disg_chag_3 / (n[1] * 1000) / dd_3)), 'stand': '3.030'}
                        elif n[0] == '4':
                            data['4'] = {'day':('%.3f' % (disg_chag_4_day / (n[1] * 1000))),'his': ('%.3f' % (disg_chag_4 / (n[1] * 1000) / dd_4)), 'stand': '1.440'}
                return self.returnTypeSuc(data)
            if kt == 'GetBatteryCluster':  # 大屏电池健康度
                key = "battery_cluster_datas_all"  # 定义电池健康度从预警平台获取的原始数据
                BC = user_session.query(func.sum(StationR.battery_cluster)).filter(StationR.station_name.in_(station_names)).scalar()
                BH = r_real.get(key)
                if BH:
                    BH =  json.loads(BH)
                else:
                    headers = {'content-type': "application/json","starkcode":"S003"}
                    url = 'http://192.168.1.98:82/yc/HisData/healthScore'
                    reponse1 = requests.post(url=url,json=["binhai","taicang","ygzhen","zgtian","ygqn","datong","shgyu","taicgxr"],headers=headers)
                    BH = reponse1.json()['data']
                    r_real.setex(key, 72 * 3600, json.dumps(BH))  # 过期时间72小时
                bh_df = pd.DataFrame(BH)
                v1 = len(bh_df[bh_df[0]<=60])
                v2 = len(bh_df[(bh_df[0]>60) & (bh_df[0]<80)])
                v3 = len(bh_df[(bh_df[0]>=80) & (bh_df[0]<90)])
                v4 = len(bh_df[bh_df[0]>=90])
                v4 = v4 + int(BC)-len(BH)
                BC = float(BC)
                return self.returnTypeSuc({"rep1":round(v1/BC*100,2),"rep2":round(v2/BC*100,2),"rep3":round(v3/BC*100,2),"rep4":round(v4/BC*100,2),"num1":v1,"num2":v2,"num3":v3,"num4":v4})
            elif kt == 'GetMiddle':#地图大屏中间部分
                data_1 = [] #基本信息
                if user_id == '131': # demo账号
                    pages = user_session.query(Station.name.label('name'), Station.longitude.label('lg'), Station.dimension.label('ds'),StationR.running_state.label('state'),
                                           StationR.province.label('pro')).filter(Station.name == StationR.station_name,Station.id==37,StationR.running_state != 4).order_by(Station.index.asc()).all()  # 获取电站
                else:
                    pages = user_session.query(Station.name.label('name'), Station.longitude.label('lg'), Station.dimension.label('ds'),StationR.running_state.label('state'),
                                           StationR.province.label('pro')).filter(Station.name == StationR.station_name,Station.name.in_(station_names),StationR.id!=37,Station.id!=37,StationR.running_state != 4).order_by(Station.index.asc()).all()  # 获取电站
                obj = {}
                zj,ty = 0,0  # z在建；投运
                province_station = {}
                province_ = r_real.get(get_middle_task.key)  # redis有值直接在redis里取值，没有再去数据库查询
                if province_:
                    province_ = json.loads(province_)
                    for pag in pages:
                        if pag.pro not in obj.keys():  # 已存入
                            obj[pag.pro] = province_.get(pag.pro)
                else:
                    for pag in pages:
                        if pag.pro in obj.keys():  # 已存入
                            if pag.state == '1':  # 投运
                                obj[pag.pro][2] = obj[pag.pro][2]+1
                            elif pag.state == '2':  # 在建
                                obj[pag.pro][3] = obj[pag.pro][3]+1
                            province_station[pag.pro].append(pag.name)
                        else:
                            if pag.state == '1':  # 投运
                                ty = 1
                                zj = 0
                            elif pag.state == '2':  # 在建
                                zj = 1
                                ty = 0
                            province_station[pag.pro] = [pag.name]
                            obj[pag.pro] = [pag.lg,pag.ds,ty,zj,0]
                    for k,v in province_station.items():
                        for sta in v:  # 循环所有站
                            if sta in gz_name.keys() and sta != 'dongmu':
                                oo = gz_name[sta]  # 配置告警的对象
                                for name in oo['status']:
                                    bean = real_data('status', name, 'db')
                                    if bean['value'] == 2:  # 状态有告警
                                        obj[k][4] = 1
                                        break
                                for names in oo['discrete']:
                                    name = list(names.keys())[0]
                                    bean = real_data('discrete', name, 'db')
                                    if bean['value'] in names[name]:
                                        obj[k][4] = 1
                                        break
                            elif sta == 'baodian':
                                if 'bodian' in gz_name.keys() :
                                    oo = gz_name[sta]  # 配置告警的对象
                                    for names in oo['discrete']:
                                        name = list(names.keys())[0]
                                        bean = real_data('discrete', name, 'db')
                                        if bean['value'] in names[name]:
                                            obj[k][4] = 1
                                            break
                            elif sta == 'dongmu':
                                v2 = real_data('status', 'dongmu', 'db')
                                if v2:
                                    e2 = v2['body']
                                    for i in e2:
                                        if i['device'][:3] == 'PCS':
                                            bit_nn = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                            bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                            bit_list_n.reverse()
                                            if bit_list_n[14] == '1':  # pcs告警.故障（取同一个值）
                                                obj[k][4] = 1
                                                break

                                        elif i['device'][:3] == 'BMS':
                                            bit_c = '{:016b}'.format(int(i['ST1']))  # 转成2进制，高位补零
                                            bit_list_c = list(bit_c)  # 转数据，正好是反向的，需要整个反转
                                            bit_list_c.reverse()
                                            if bit_list_c[4] == '1':  # bms告警.故障
                                                obj[k][4] = 1
                                                break
                return self.returnTypeSuc(obj)
            elif kt == 'GetMonitor':
                # 项目集控
                lang_s = "zh" if lang is None else lang
                key = 'index_monitor_all_%s_%s' % (str(user_id), lang_s)
                name = self.get_argument('name', '')  # 储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能
                now_time = timeUtils.getNewDayStartStr()
                datestart = now_time[0:4]
                returndata = []
                db = []
                if name:
                    if user_id == '131':  # demo账号
                        pages = user_session.query(Station.name, Station.descr, Station.address,
                                                   StationR.energy_storage, StationR.running_state,
                                                   StationR.ChagCapy, StationR.DisgCapy, StationR.SOC,
                                                   StationR.active_power_name, StationR.monitor,
                                                   StationR.electric_power, Station.volume, Station.en_address,
                                                   Station.en_descr, StationR.en_monitor).filter(
                            Station.id == 37, Station.name == StationR.station_name,
                            StationR.energy_storage == name, StationR.running_state != 4).order_by(
                            Station.index.asc()).all()  # 获取电站
                    else:
                        pages = user_session.query(Station.name, Station.descr, Station.address,
                                                   StationR.energy_storage, StationR.running_state,
                                                   StationR.ChagCapy, StationR.DisgCapy, StationR.SOC,
                                                   StationR.active_power_name, StationR.monitor,
                                                   StationR.electric_power, Station.volume, Station.en_address,
                                                   Station.en_descr, StationR.en_monitor).filter(
                            Station.name.in_(station_names), Station.name == StationR.station_name,
                                                             StationR.id != 37, Station.id != 37,
                                                             StationR.energy_storage == name,
                                                             StationR.running_state != 4).order_by(
                            Station.index.asc()).all()  # 获取电站
                else:
                    if user_id == '131':
                        pages = user_session.query(Station.name, Station.descr, Station.address,
                                                   StationR.energy_storage,
                                                   StationR.running_state, StationR.ChagCapy, StationR.DisgCapy,
                                                   StationR.SOC, StationR.active_power_name, StationR.monitor,
                                                   StationR.electric_power, Station.volume, Station.en_address,
                                                   Station.en_descr, StationR.en_monitor).filter(Station.id == 37,
                                                                                                 Station.id == StationR.id,
                                                                                                 StationR.running_state != 4).order_by(
                            Station.index.asc()).all()  # 获取电站
                    else:
                        r_data = r_real.get(key)
                        if r_data:
                            data = json.loads(r_data)
                            if lang == 'en':
                                return self.returnTypeSuc_en(data=data, info=None, lang='en')
                            else:
                                return self.returnTypeSuc_en(data=data, info=None, lang=None)
                        pages = user_session.query(Station.name, Station.descr, Station.address,
                                                   StationR.energy_storage,
                                                   StationR.running_state, StationR.ChagCapy, StationR.DisgCapy,
                                                   StationR.SOC, StationR.active_power_name, StationR.monitor,
                                                   StationR.electric_power, Station.volume, Station.en_address,
                                                   Station.en_descr, StationR.en_monitor).filter(
                            Station.name.in_(station_names), StationR.id != 37, Station.id != 37,
                            Station.name == StationR.station_name, StationR.running_state != 4).order_by(
                            Station.index.asc()).all()  # 获取电站
                cd_day = {}
                pcs_1 = []
                for i in pcs_:
                    for ii in db:
                        if ii in i:
                            pcs_1.append(i)
                        elif ii == 'baodian':
                            if 'bodian' in i:
                                pcs_1.append(i)
                for pag in pages:
                    descr = {}
                    descr['name'] = pag[0]
                    descr['energy_storage'] = pag[3]
                    descr['running_state'] = pag[4]
                    descr['electric_power'] = str('%.0f' % (pag[10] * 1000))
                    descr['volume'] = str('%.0f' % (pag[11] * 1000))
                    if lang == 'en':
                        descr['address'] = pag[12]
                        descr['descr'] = pag[13]
                        descr['monitor'] = pag[14]
                    else:
                        descr['descr'] = pag[1]
                        descr['address'] = pag[2]
                        descr['monitor'] = pag[9]
                    returndata.append(descr)
                    db.append(pag[0])
                    ChagCapy = 0  # 当日充电量
                    DisgCapy = 0  # 当日放电量
                    SOC = 0  # SOC
                    n = 0
                    active_power = 0  # 有功功率
                    if pag[0] == 'dongmu':
                        # 测量量实时值
                        v1 = real_data('measure', 'dongmu', 'db')
                        if 'body' in v1:
                            if v1:
                                e1 = v1['body']
                            for i in e1:
                                if i['device'][:3] == 'PCS':
                                    # 如果获取值是 -- 则赋值几个变量为-- 且跳出循环
                                    if i['ccapd'] == '--' or i['dcapd'] == '--' or i['actPo'] == '--':
                                        ChagCapy = '--'
                                        DisgCapy = '--'
                                        active_power = '--'
                                        SOC = '--'
                                        break
                                    ChagCapy += float(i['ccapd']) / 1000
                                    DisgCapy += float(i['dcapd']) / 1000
                                    active_power = float(i['actPo'])
                                if i['device'][:3] == 'BMS':
                                    # 如果获取值是 -- 则赋值几个变量为-- 且跳出循环
                                    if i['CSOC'] == '--':
                                        ChagCapy = '--'
                                        DisgCapy = '--'
                                        active_power = '--'
                                        SOC = '--'
                                        break
                                    SOC += float(i['CSOC'])
                                    n += 1
                    else:
                        if pag[5]:
                            ChagCapy = int(pag[5]) / 1000
                        if pag[6]:
                            DisgCapy = int(pag[6]) / 1000
                        if pag[7]:
                            for s in eval(pag[7]):
                                o = real_data('measure', s, 'db')
                                # 如果获取值是 -- 则赋值几个变量为-- 且跳出循环
                                if o['value'] == '--':
                                    SOC = '--'
                                    break
                                else:
                                    if o['value'] > 101:
                                        o['value'] = 0
                                    SOC += o['value']
                                    n += 1
                        if pag[8]:  # 有功功率
                            for s in eval(pag[8]):
                                o = real_data('measure', s, 'db')
                                if o['value'] == '--':
                                    active_power = '--'
                                    break
                                else:
                                    active_power += o['value']
                    if active_power == '--':
                        descr['active_power'] = active_power  # 有功功率
                        descr['active_power_unit'] = 'KW'  # 有功功率
                    else:
                        if active_power > 10000 or active_power < 10000:
                            active_power = active_power / 1000
                            descr['active_power'] = ('%.2f' % active_power)  # 有功功率
                            descr['active_power_unit'] = 'MW'  # 有功功率
                        else:
                            descr['active_power'] = ('%.2f' % active_power)  # 有功功率
                            descr['active_power_unit'] = 'KW'  # 有功功率
                    descr['ChagCapy'] = ('%.2f' % ChagCapy)
                    descr['DisgCapy'] = ('%.2f' % DisgCapy)
                    cd_day[pag[0]] = {}
                    cd_day[pag[0]]['ChagCapy'] = ChagCapy
                    cd_day[pag[0]]['DisgCapy'] = DisgCapy
                    if SOC == '--':
                        descr['SOC'] = '--'
                    else:
                        if n == 0:
                            descr['SOC'] = '0'
                        else:
                            descr['SOC'] = ('%.2f' % (SOC / n))
                # t_alarm = user_session.query(Event.station, func.count(Event.id)).filter(Event.type_id == 5, Event.type == 2, AlarmR.event_id == Event.id, Event.station.in_(db),or_(AlarmR.value_descr == '故障', AlarmR.value_descr == '报警')).group_by(Event.station).all()
                t_alarm_re = r_real.hget("monitor", "alarm_data")
                t_alarm = json.loads(t_alarm_re) if t_alarm_re else None
                if t_alarm is None:
                    t_alarm = user_session.query(Event.station, func.count(Event.id)).filter(Event.type_id == 5,
                                                                                             Event.type == 2,
                                                                                             AlarmR.event_id == Event.id,
                                                                                             Event.station.in_(db),
                                                                                             or_(AlarmR.value_descr == '故障',
                                                                                                 AlarmR.value_descr == '报警')).group_by(
                        Event.station).all()
                else:
                    t_alarm = [(list(item.keys())[0], list(item.values())[0]) for item in t_alarm]
                stored_value = r_real.hget("monitor", "dispatch")
                # 将字符串转换回数据列表
                if stored_value:
                    dispatchR = [tuple(item.split(",")) for item in stored_value.decode('utf-8').split(";")]
                else:
                    dispatchR = user_session.query(DispatchR.station).filter(DispatchR.station.in_(db),DispatchR.working_flag == '2').all()  # 工单数量
                rincome_ = user_session.query(RIncome.station_name, RIncome.income, RIncome.target_income).filter(RIncome.in_ts == 1, RIncome.is_use == 1, RIncome.station_name.in_(db),RIncome.day == datestart).all()
                ratio = user_session.query(FReport).filter(FReport.name.in_(pcs_1), FReport.cause == 1,FReport.day.like(now_time[:8] + '%')).all()
                list_al = []
                station_name_list = [item['name'] for item in returndata if item['name'] not in ["dongmu", "halun"]]
                if station_name_list:
                    # 获取当前日期
                    in_clause = ', '.join(f'"{station}"' for station in station_name_list)
                    now = datetime.now()
                    formatted_date = now.strftime("%Y%m")
                    day = now.strftime("%Y-%m-%d")
                    # 获取当月充放电效率
                    conn = pool.connection()
                    cursor = conn.cursor()
                    sql = "select ratio,station_name from {} where date_value='{}' and date_type='{}' and station_name in ({}) ". \
                        format('dws_st_measure_cw_cm_cy', formatted_date, 'year_month', in_clause)
                    cursor.execute(sql)
                    month_ratio = cursor.fetchall()
                    # 获取当日充放电量
                    sql_chag_disg = ("select chag, disg, station_name from {} where station_name in ({}) and day='{}'".
                                     format('dws_st_measure_1d', in_clause, day))
                    cursor.execute(sql_chag_disg)
                    day_chag_disg = cursor.fetchall()
                    month_ratios = {}
                    morethan_95_station = []
                    for item in month_ratio:
                        month_ratios[item['station_name']] = float('%.2f' % (item['ratio']))
                        if item['ratio']>95:
                            morethan_95_station.append(item['station_name'])
                    if len(morethan_95_station) > 0:
                        # 大于95的获取上个月的值：
                        morethan_95_in_clause = ', '.join(f"'{station}'" for station in morethan_95_station)
                        last_month = now - relativedelta(months=1)
                        # 格式化日期为 YYYYmm
                        formatted_date_last = last_month.strftime("%Y%m")
                        sql_last_month = "select ratio, station_name from {} where date_value='{}' and date_type='{}' and station_name in ({}) ". \
                            format('dws_st_measure_cw_cm_cy', formatted_date_last, 'year_month', morethan_95_in_clause)
                        cursor.execute(sql_last_month)
                        last_month_ratio = cursor.fetchall()
                        for last_item in last_month_ratio:
                            month_ratios[last_item['station_name']] = float('%.2f' % (last_item['ratio'])) if last_item['ratio'] < 95 else float('%.2f' % (95))
                    cursor.close()
                    conn.close()
                    day_chag_disgs = {}
                    for item in day_chag_disg:
                        day_chag_disgs[item['station_name']] = {
                            "ChagCapy": float('%.2f' % (item['chag'] / 1000)) if item['chag'] else 0,
                            "DisgCapy": float('%.2f' % (item['disg'] / 1000)) if item['disg'] else 0
                        }

                for i in returndata:
                    n = 0
                    year_yield_achieved = 0
                    disg = 0  # 放电量
                    chag = 0  # 放电量
                    for al in t_alarm:
                        list_al.append(al[0])
                        if al[0] == i['name']:
                            i['alarm'] = al[1]
                    if i['name'] not in list_al:
                        i['alarm'] = 0
                    for t in dispatchR:
                        if t[0] == i['name']:
                            n += 1
                    i['dispatch'] = n  # 工单数量

                    for t in rincome_:
                        if t[0] == i['name']:
                            try:
                                year_yield_achieved = '%.2f' % (float(t[1]) / float(t[2]) * 100)  # 年达产率
                            except:
                                pass
                    i['year_yield_achieved'] = year_yield_achieved if  year_yield_achieved != 0 else '--' # 年达产率

                    station_name = i['name']
                    if station_name in exclude_station and station_name in ["dongmu", "halun"]:
                        if i['name'] == 'baodian':
                            station_name = 'bodian'
                        for f in ratio:
                            if station_name in f.name:
                                disg = disg + np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(eval(f.gd_disg))
                                chag = chag + np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(eval(f.gd_chag))
                        ratio_total = 0
                        for c in cd_day.keys():
                            if c == station_name:
                                chag_tal = chag + cd_day[c]['ChagCapy']
                                if chag_tal == 0:
                                    ratio_total = 0
                                else:
                                    ratio_total = float('%.2f' % (((disg + cd_day[c]['DisgCapy']) / chag_tal) * 100))
                                    if ratio_total > 100:
                                        ratio_total = 100
                        i['ratio'] = ratio_total if ratio_total != 0 else '--'
                    else:
                        i['ratio'] = float('%.2f' % (month_ratios[station_name])) if station_name in month_ratios else '--'
                        i['ChagCapy'] = float('%.2f' % (day_chag_disgs[station_name]['ChagCapy'])) if station_name in day_chag_disgs else 0
                        i['DisgCapy'] = float('%.2f' % (day_chag_disgs[station_name]['DisgCapy'])) if station_name in day_chag_disgs else 0
                        # 获取当前日期
                        # now = datetime.now()
                        # formatted_date = now.strftime("%Y%m")
                        # day = now.strftime("%Y-%m-%d")
                        # # 获取当月充放电效率
                        # conn = pool.connection()
                        # cursor = conn.cursor()
                        # sql = "select ratio from {} where date_value='{}' and date_type='{}' and station_name='{}' ". \
                        #     format('dws_st_measure_cw_cm_cy', formatted_date, 'year_month', station_name)
                        # cursor.execute(sql)
                        # result = cursor.fetchone()
                        # # 获取当日充放电量
                        # sql_chag_disg = ("select chag, disg from {} where station_name='{}' and day='{}'".
                        #                  format('dws_st_measure_1d', station_name, day))
                        # cursor.execute(sql_chag_disg)
                        # day_res = cursor.fetchone()
                        #
                        # i['ratio'] = float('%.2f' % (result['ratio'])) if result else '--'
                        # if result:
                        #     if i['ratio'] > 95:
                        #         last_month = now - relativedelta(months=1)
                        #         # 格式化日期为 YYYYmm
                        #         formatted_date_last = last_month.strftime("%Y%m")
                        #         sql_last_month = "select ratio from {} where date_value='{}' and date_type='{}' and station_name='{}' ". \
                        #             format('dws_st_measure_cw_cm_cy', formatted_date_last, 'year_month', station_name)
                        #         cursor.execute(sql_last_month)
                        #         last_result = cursor.fetchone()
                        #         if last_result:
                        #             i['ratio'] = float('%.2f' % (last_result['ratio'])) if last_result['ratio'] <95 else float('%.2f' %(95))
                        # cursor.close()
                        # conn.close()
                        # if day_res:
                        #     i['ChagCapy'] = float('%.2f' % (day_res['chag'] / 1000)) if day_res else 0
                        #     i['DisgCapy'] = float('%.2f' % (day_res['disg'] / 1000)) if day_res else 0

                r_real.setex(key, 5 * 60, json.dumps(returndata))
                if lang == 'en':
                    return self.returnTypeSuc_en(data=returndata, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=returndata, info=None, lang=None)
            elif kt == 'GetProjectList':  # 项目清单
                descr = self.get_argument('descr', '') #储能形式，1火储能，2工商业储能，3集中式储能，4新能源储能
                returndata = []
                if lang=='en':
                    if descr:
                        pages1 = user_session.query(StationR).all()  # 获取电站
                        for p1 in pages1:
                            pages = user_session.query(Station.en_descr,Station.id).filter(Station.name == p1.station_name,Station.descr.like('%' + descr + '%')).all()  # 获取电站
                            for p in pages:
                                obj={}
                                obj['name']=p[0]
                                obj['id']=p[1]
                                returndata.append(obj)
                    else:
                        pages1 = user_session.query(StationR).all()  # 获取电站
                        for p1 in pages1:
                            pages = user_session.query(Station.en_descr,Station.id).filter(Station.name == p1.station_name).all()  # 获取电站
                            for p in pages:
                                obj = {}
                                obj['name'] = p[0]
                                obj['id'] = p[1]
                                returndata.append(obj)
                else:
                    if descr:
                        pages1 = user_session.query(StationR).all()  # 获取电站
                        for p1 in pages1:
                            pages = user_session.query(Station.descr, Station.id).filter(
                                Station.name == p1.station_name, Station.descr.like('%' + descr + '%')).all()  # 获取电站
                            for p in pages:
                                obj = {}
                                obj['name'] = p[0]
                                obj['id'] = p[1]
                                returndata.append(obj)
                    else:
                        pages1 = user_session.query(StationR).all()  # 获取电站
                        for p1 in pages1:
                            pages = user_session.query(Station.descr, Station.id).filter(
                                Station.name == p1.station_name).all()  # 获取电站
                            for p in pages:
                                obj = {}
                                obj['name'] = p[0]
                                obj['id'] = p[1]
                                returndata.append(obj)
                if lang == 'en':
                    return self.returnTypeSuc_en(data=returndata, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=returndata, info=None, lang=None)
            elif kt == 'GetDropDownBoxList':  # 监控系列下拉框
                db = self.get_argument('db', '')  # 电站名称
                ty = self.get_argument('ty', '')  # sys系统监控,bms电池监控,pcsPCS监控,sysio系统通信,air空调0,fire消防00
                d=[]
                if db == 'guizhou':
                    if ty == 'sys':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.id <6).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if lang == 'en':
                                replaced_data = DispatchModel.replace_en_fields(o, "")
                                o.update(replaced_data)
                            obj = {'label': o['descr'], 'key': o['ky']}
                            d.append(obj)
                    elif ty == 'pcs':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.id <6).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.p_id == dd['key'],PageDrop.id >5,PageDrop.id <36).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'bms':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.id < 6).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.p_id == dd['key'],PageDrop.id >35,PageDrop.id <156).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'sysio':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.id < 6).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.p_id == dd['key'],PageDrop.id >360,PageDrop.id <376).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'fire':
                        pag_d_f = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.p_id == 6).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_f:
                            o = eval(str(pag))
                            if lang == 'en':
                                replaced_data = DispatchModel.replace_en_fields(o, "")
                                o.update(replaced_data)
                            obj = {'label': o['descr'], 'key': o['ky']}
                            d.append(obj)
                    elif ty == 'air':
                        pag_d_a = user_session.query(PageDrop).filter(PageDrop.station == 'guizhou',PageDrop.p_id == 0).order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_a:
                            o = eval(str(pag))
                            if lang == 'en':
                                replaced_data = DispatchModel.replace_en_fields(o, "")
                                o.update(replaced_data)
                            obj = {'label': o['descr'], 'key': o['ky']}
                            d.append(obj)
                    user_session.close()
                elif db == 'ygqn':
                    if ty == 'sys':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == dd['key'],PageDrop.id >=389,PageDrop.id <=408).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    if ty == 'pcs':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == dd['key'],PageDrop.id >=410,PageDrop.id <=623).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'bms':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == dd['key'],PageDrop.id >=438,PageDrop.id <=909).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['id']}
                                        dd['children'].append(obj_pcs)
                            for ddd in dd['children'] :
                                ddd['children']=[]
                                pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == ddd['key']).order_by(PageDrop.id.asc()).all()
                                if pag_d:
                                    for pp in pag_d:
                                        oo = eval(str(pp))
                                        if 'BMS' in oo['descr']:
                                            if lang == 'en':
                                                replaced_data = DispatchModel.replace_en_fields(oo, "")
                                                oo.update(replaced_data)
                                            obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                            ddd['children'].append(obj_pcs)
                    elif ty == 'sysio':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(
                            PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',
                                                                        PageDrop.p_id == dd['key'], PageDrop.id >= 923,
                                                                        PageDrop.id <= 941).order_by(
                                PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'fire':#消防
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统D区' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == dd['key'], PageDrop.id >= 942,PageDrop.id <= 954).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'air':#冷水机
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统D区' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',
                                                                        PageDrop.p_id == dd['key'], PageDrop.id >= 955,
                                                                        PageDrop.id <= 967).order_by(
                                PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    user_session.close()
                elif db == 'shgyu':
                    if ty == 'sys':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu',PageDrop.p_id == dd['key'],PageDrop.id >=970,PageDrop.id <=971).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    if ty == 'pcs':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu',PageDrop.p_id == dd['key'],PageDrop.id >=974,PageDrop.id <=983).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'bms':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'],'key': o['id']}
                                d.append(obj)
                        for dd in d :
                            dd['children']=[]
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu',PageDrop.p_id == dd['key'],PageDrop.id >=984,PageDrop.id <=1023).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['id']}
                                        dd['children'].append(obj_pcs)
                            for ddd in dd['children'] :
                                ddd['children']=[]
                                pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu',PageDrop.p_id == ddd['key']).order_by(PageDrop.id.asc()).all()
                                if pag_d:
                                    for pp in pag_d:
                                        oo = eval(str(pp))
                                        if 'BMS' in oo['descr']:
                                            if lang == 'en':
                                                replaced_data = DispatchModel.replace_en_fields(oo, "")
                                                oo.update(replaced_data)
                                            obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                            ddd['children'].append(obj_pcs)
                    elif ty == 'sysio':
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu').order_by(
                            PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'shgyu',
                                                                        PageDrop.p_id == dd['key'], PageDrop.id >= 972,PageDrop.id <= 973).order_by(
                                PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'PCS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'fire':#消防
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统D区' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',PageDrop.p_id == dd['key'], PageDrop.id >= 942,PageDrop.id <= 954).order_by(PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    elif ty == 'air':#冷水机
                        pag_d_s = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn').order_by(PageDrop.id.asc()).all()
                        for pag in pag_d_s:
                            o = eval(str(pag))
                            if '系统D区' in o['descr']:
                                if lang == 'en':
                                    replaced_data = DispatchModel.replace_en_fields(o, "")
                                    o.update(replaced_data)
                                obj = {'label': o['descr'], 'key': o['id']}
                                d.append(obj)
                        for dd in d:
                            dd['children'] = []
                            pag_d = user_session.query(PageDrop).filter(PageDrop.station == 'ygqn',
                                                                        PageDrop.p_id == dd['key'], PageDrop.id >= 955,
                                                                        PageDrop.id <= 967).order_by(
                                PageDrop.id.asc()).all()
                            if pag_d:
                                for pp in pag_d:
                                    oo = eval(str(pp))
                                    if 'BMS' in oo['descr']:
                                        if lang == 'en':
                                            replaced_data = DispatchModel.replace_en_fields(oo, "")
                                            oo.update(replaced_data)
                                        obj_pcs = {'label': oo['descr'], 'key': oo['ky']}
                                        dd['children'].append(obj_pcs)
                    user_session.close()
                elif db == 'houma':
                    if lang=='en':
                        if ty == 'sys':
                            d = [{'label': 'System zone A', 'key': '1',
                                  'children': [{'label': 'Zone A#1', 'key': '129'}, {'label': 'Zone A#2', 'key': '130'}]},
                                 {'label': 'System B area', 'key': '2',
                                  'children': [{'label': 'Zone B#1', 'key': '131'}, {'label': 'Zone B#2', 'key': '132'}]}]
                        elif ty == 'bms':
                            d = [{'label': 'System zone A', 'key': '1',
                                  'children': [{'label': 'Zone A#1', 'key': '1',
                                                'children': [{'label': '1#BMS', 'key': '133'},
                                                             {'label': '2#BMS', 'key': '134'},
                                                             {'label': '3#BMS', 'key': '135'},
                                                             {'label': '4#BMS', 'key': '136'},
                                                             {'label': '5#BMS', 'key': '137'},
                                                             {'label': '6#BMS', 'key': '138'},
                                                             {'label': '7#BMS', 'key': '139'},
                                                             {'label': '8#BMS', 'key': '140'},
                                                             {'label': '9#BMS', 'key': '141'},
                                                             {'label': '10#BMS', 'key': '142'},
                                                             {'label': '11#BMS', 'key': '143'},
                                                             {'label': '12#BMS', 'key': '144'},
                                                             {'label': '13#BMS', 'key': '145'},
                                                             {'label': '14#BMS', 'key': '146'}]},
                                               {'label': 'Zone A#2', 'key': '2',
                                                'children': [{'label': '15#BMS', 'key': '147'},
                                                             {'label': '16#BMS', 'key': '148'},
                                                             {'label': '17#BMS', 'key': '149'},
                                                             {'label': '18#BMS', 'key': '150'},
                                                             {'label': '19#BMS', 'key': '151'},
                                                             {'label': '20#BMS', 'key': '152'},
                                                             {'label': '21#BMS', 'key': '153'},
                                                             {'label': '22#BMS', 'key': '154'},
                                                             {'label': '23#BMS', 'key': '155'},
                                                             {'label': '24#BMS', 'key': '156'},
                                                             {'label': '25#BMS', 'key': '157'},
                                                             {'label': '26#BMS', 'key': '158'},
                                                             {'label': '27#BMS', 'key': '159'},
                                                             {'label': '28#BMS', 'key': '160'}]}]},
                                 {'label': 'System B area', 'key': '3',
                                  'children': [{'label': 'Zone B#1', 'key': '3',
                                                'children': [{'label': '1#BMS', 'key': '161'},
                                                             {'label': '2#BMS', 'key': '162'},
                                                             {'label': '3#BMS', 'key': '163'},
                                                             {'label': '4#BMS', 'key': '164'},
                                                             {'label': '5#BMS', 'key': '165'},
                                                             {'label': '6#BMS', 'key': '166'},
                                                             {'label': '7#BMS', 'key': '167'},
                                                             {'label': '8#BMS', 'key': '168'},
                                                             {'label': '9#BMS', 'key': '169'},
                                                             {'label': '10#BMS', 'key': '170'},
                                                             {'label': '11#BMS', 'key': '171'},
                                                             {'label': '12#BMS', 'key': '172'},
                                                             {'label': '13#BMS', 'key': '172'},
                                                             {'label': '14#BMS', 'key': '174'},
                                                             {'label': '15#BMS', 'key': '175'},
                                                             {'label': '16#BMS', 'key': '176'}]},
                                               {'label': 'Zone B#2', 'key': '4',
                                                'children': [{'label': '17#BMS', 'key': '177'},
                                                             {'label': '18#BMS', 'key': '178'},
                                                             {'label': '19#BMS', 'key': '179'},
                                                             {'label': '20#BMS', 'key': '180'},
                                                             {'label': '21#BMS', 'key': '181'},
                                                             {'label': '22#BMS', 'key': '182'},
                                                             {'label': '23#BMS', 'key': '183'},
                                                             {'label': '24#BMS', 'key': '184'},
                                                             {'label': '25#BMS', 'key': '185'},
                                                             {'label': '26#BMS', 'key': '186'},
                                                             {'label': '27#BMS', 'key': '187'},
                                                             {'label': '28#BMS', 'key': '188'},
                                                             {'label': '29#BMS', 'key': '189'},
                                                             {'label': '30#BMS', 'key': '190'},
                                                             {'label': '31#BMS', 'key': '191'},
                                                             {'label': '32#BMS', 'key': '192'}]}]}]
                        elif ty == 'pcs':
                            d = [{'label': 'System zone A', 'key': '1',
                                  'children': [{'label': 'Zone A#1', 'key': '1',
                                                'children': [{'label': '1#-4#PCS', 'key': '193'},
                                                             {'label': '5#-8#PCS', 'key': '194'},
                                                             {'label': '9#-12#PCS', 'key': '195'},
                                                             {'label': '13#-14#PCS', 'key': '196'}, ]},
                                               {'label': 'Zone A#2', 'key': '2',
                                                'children': [{'label': '15#-18#PCS', 'key': '197'},
                                                             {'label': '19#-22#PCS', 'key': '198'},
                                                             {'label': '23#-26#PCS', 'key': '199'},
                                                             {'label': '27#-28#PCS', 'key': '200'}, ]}]},
                                 {'label': 'System B area', 'key': '3',
                                  'children': [{'label': 'Zone B#1', 'key': '3',
                                                'children': [{'label': '1#-4#PCS', 'key': '201'},
                                                             {'label': '5#-8#PCS', 'key': '202'},
                                                             {'label': '9#-12#PCS', 'key': '203'},
                                                             {'label': '13#-16#PCS', 'key': '204'}]},
                                               {'label': 'Zone B#2', 'key': '4',
                                                'children': [{'label': '17#-20#PCS', 'key': '205'},
                                                             {'label': '21#-24#PCS', 'key': '206'},
                                                             {'label': '25#-28#PCS', 'key': '207'},
                                                             {'label': '29#-32#PCS', 'key': '208'}]}]}]
                        elif ty == 'sysio':
                            d = [{'label': 'System zone A', 'key': '209'}, {'label': 'System B area', 'key': '210'}]
                        elif ty == 'air':
                            d = [{'label': 'Zone A', 'key': '1',
                                  'children': [{'label': '#1', 'key': '129'}, {'label': '#2', 'key': '130'}]},
                                 {'label': 'Zone B', 'key': '2',
                                  'children': [{'label': '#1', 'key': '131'}, {'label': '#2', 'key': '132'}]}]
                        elif ty == 'fire':
                            d = [{'label': 'Zone A', 'key': '1',
                                  'children': [{'label': '#1', 'key': '129'}, {'label': '#2', 'key': '130'}]},
                                 {'label': 'Zone B', 'key': '2',
                                  'children': [{'label': '#1', 'key': '131'}, {'label': '#2', 'key': '132'}]}]
                    else:
                        if ty=='sys':
                            d =[{'label':'系统A区','key':'1','children':[{'label':'A区#1','key':'129'},{'label':'A区#2','key':'130'}]},{'label':'系统B区','key':'2','children':[{'label':'B区#1','key':'131'},
                                                                                                                                                                        {'label':'B区#2','key':'132'}]}]
                        elif ty=='bms':
                            d =[{'label':'系统A区','key':'1',
                                 'children':[{'label':'A区#1','key':'1','children':[{'label':'1#BMS','key':'133'},{'label':'2#BMS','key':'134'},{'label':'3#BMS','key':'135'},{'label':'4#BMS','key':'136'},
                                {'label':'5#BMS','key':'137'},{'label':'6#BMS','key':'138'},{'label':'7#BMS','key':'139'},{'label':'8#BMS','key':'140'},{'label':'9#BMS','key':'141'},{'label':'10#BMS','key':'142'},
                                {'label':'11#BMS','key':'143'},{'label':'12#BMS','key':'144'},{'label':'13#BMS','key':'145'},{'label':'14#BMS','key':'146'}]},
                                {'label':'A区#2','key':'2','children':[{'label':'15#BMS','key':'147'},{'label':'16#BMS','key':'148'},{'label':'17#BMS','key':'149'},{'label':'18#BMS','key':'150'},
                                {'label':'19#BMS','key':'151'},{'label':'20#BMS','key':'152'},{'label':'21#BMS','key':'153'},{'label':'22#BMS','key':'154'},{'label':'23#BMS','key':'155'},{'label':'24#BMS','key':'156'},
                                {'label':'25#BMS','key':'157'},{'label':'26#BMS','key':'158'},{'label':'27#BMS','key':'159'},{'label':'28#BMS','key':'160'}]}]},
                                {'label':'系统B区','key':'3',
                                 'children':[{'label':'B区#1','key':'3',
                                              'children':[{'label':'1#BMS','key':'161'},{'label':'2#BMS','key':'162'},{'label':'3#BMS','key':'163'},{'label':'4#BMS','key':'164'},
                                                {'label':'5#BMS','key':'165'},{'label':'6#BMS','key':'166'},{'label':'7#BMS','key':'167'},{'label':'8#BMS','key':'168'},{'label':'9#BMS','key':'169'},{'label':'10#BMS','key':'170'},
                                                {'label':'11#BMS','key':'171'},{'label':'12#BMS','key':'172'},{'label':'13#BMS','key':'172'},{'label':'14#BMS','key':'174'},{'label':'15#BMS','key':'175'},{'label':'16#BMS','key':'176'}]},
                                            {'label':'B区#2','key':'4',
                                                'children':[{'label':'17#BMS','key':'177'},{'label':'18#BMS','key':'178'},{'label':'19#BMS','key':'179'},{'label':'20#BMS','key':'180'},
                                                    {'label':'21#BMS','key':'181'},{'label':'22#BMS','key':'182'},{'label':'23#BMS','key':'183'},{'label':'24#BMS','key':'184'},{'label':'25#BMS','key':'185'},{'label':'26#BMS','key':'186'},
                                                    {'label':'27#BMS','key':'187'},{'label':'28#BMS','key':'188'},{'label':'29#BMS','key':'189'},{'label':'30#BMS','key':'190'},{'label':'31#BMS','key':'191'},{'label':'32#BMS','key':'192'}]}]}]
                        elif ty=='pcs':
                            d = [{'label': '系统A区', 'key': '1',
                                  'children': [{'label': 'A区#1', 'key': '1','children': [{'label': '1#-4#PCS', 'key': '193'},{'label': '5#-8#PCS', 'key': '194'},{'label': '9#-12#PCS', 'key': '195'},{'label': '13#-14#PCS', 'key': '196'},]},
                                {'label': 'A区#2', 'key': '2','children': [{'label': '15#-18#PCS', 'key': '197'},{'label': '19#-22#PCS', 'key': '198'},{'label': '23#-26#PCS', 'key': '199'},{'label': '27#-28#PCS', 'key': '200'},]}]},
                                 {'label': '系统B区', 'key': '3',
                                  'children': [{'label': 'B区#1', 'key': '3',
                                                'children': [{'label': '1#-4#PCS', 'key': '201'},{'label': '5#-8#PCS', 'key': '202'},{'label': '9#-12#PCS', 'key': '203'},{'label': '13#-16#PCS','key': '204'}]},
                                               {'label': 'B区#2', 'key': '4',
                                                'children': [{'label': '17#-20#PCS', 'key': '205'},{'label': '21#-24#PCS', 'key': '206'},{'label': '25#-28#PCS', 'key': '207'},{'label': '29#-32#PCS','key': '208'}]}]}]
                        elif ty=='sysio':
                            d =[{'label':'系统A区','key':'209'},{'label':'系统B区','key':'210'}]
                        elif ty=='air':
                            d =[{'label':'A区','key':'1','children':[{'label':'#1','key':'129'},{'label':'#2','key':'130'}]},{'label':'B区','key':'2','children':[{'label':'#1','key':'131'},{'label':'#2','key':'132'}]}]
                        elif ty=='fire':
                            d =[{'label':'A区','key':'1','children':[{'label':'#1','key':'129'},{'label':'#2','key':'130'}]},{'label':'B区','key':'2','children':[{'label':'#1','key':'131'},{'label':'#2','key':'132'}]}]
                else:
                    # 获取顶层菜单
                    parent_drop = user_session.query(PageDrop).filter(PageDrop.station == db, PageDrop.ky == 0,
                                                                      PageDrop.p_id == 0).order_by(
                        PageDrop.id.asc()).all()
                    if ty == 'sys':
                        select_area = []  # 区域选择集合
                        for drop_item in parent_drop:
                            # 查询子集
                            children = []
                            child_drop = user_session.query(PageDrop).join(Page, PageDrop.ky == Page.id).filter(
                                PageDrop.station == db, PageDrop.ky != 0,
                                PageDrop.p_id == drop_item.id, Page.descr.like('%系统监控%')).order_by(
                                PageDrop.id.asc()).all()
                            for child_item in child_drop:
                                children.append({
                                    'label': child_item.descr,
                                    'key': str(child_item.ky)
                                })
                            menu_area = {
                                'label': drop_item.en_descr if lang == 'en' else drop_item.descr,
                                'key': str(drop_item.id),
                                'children': children
                            }
                            select_area.append(menu_area)
                    if ty == 'pcs':
                        select_area = []  # 区域选择集合
                        for drop_item in parent_drop:
                            # 查询子集
                            children = []
                            child_drop = user_session.query(PageDrop).join(Page, PageDrop.ky == Page.id).filter(
                                PageDrop.station == db, PageDrop.ky != 0,
                                PageDrop.p_id == drop_item.id, Page.descr.like('%pcs监控%')).order_by(
                                PageDrop.id.asc()).all()
                            for child_item in child_drop:
                                children.append({
                                    'label': child_item.descr,
                                    'key': str(child_item.ky)
                                })
                            menu_area = {
                                'label': drop_item.en_descr if lang == 'en' else drop_item.descr,
                                'key': str(drop_item.id),
                                'children': children
                            }
                            select_area.append(menu_area)
                    elif ty == 'bms':
                        select_area = []  # 区域选择集合
                        for drop_item in parent_drop:
                            # 查询子集
                            children_one = []
                            child_one_drop = user_session.query(PageDrop).filter(PageDrop.station == db,
                                                                                 PageDrop.ky == 0,
                                                                                 PageDrop.p_id == drop_item.id).order_by(
                                PageDrop.id.asc()).all()
                            for child_item in child_one_drop:
                                children_two = []
                                child_two_drop = user_session.query(PageDrop).join(Page, PageDrop.ky == Page.id).filter(
                                    PageDrop.station == db, PageDrop.ky != 0,
                                    PageDrop.p_id == child_item.id, Page.descr.like('%电池监控%')).order_by(
                                    PageDrop.id.asc()).all()
                                for child_two_item in child_two_drop:
                                    children_two.append({
                                        'label': child_two_item.descr,
                                        'key': str(child_two_item.ky)
                                    })
                                children_one.append({
                                    'label': child_item.descr,
                                    'key': str(child_item.ky),
                                    'children': children_two
                                })
                            menu_area = {
                                'label': drop_item.en_descr if lang == 'en' else drop_item.descr,
                                'key': str(drop_item.id),
                                'children': children_one
                            }
                            select_area.append(menu_area)
                    elif ty == 'sysio':
                        select_area = []  # 区域选择集合
                        for drop_item in parent_drop:
                            # 查询子集
                            children = []
                            child_drop = user_session.query(PageDrop).join(Page, PageDrop.ky == Page.id).filter(
                                PageDrop.station == db, PageDrop.ky != 0,
                                PageDrop.p_id == drop_item.id, Page.descr.like('%系统通信%')).order_by(
                                PageDrop.id.asc()).all()
                            for child_item in child_drop:
                                children.append({
                                    'label': child_item.descr,
                                    'key': str(child_item.ky)
                                })
                            menu_area = {
                                'label': drop_item.en_descr if lang == 'en' else drop_item.descr,
                                'key': str(drop_item.id),
                                'children': children
                            }
                            select_area.append(menu_area)
                    elif ty == 'air':#冷水机
                        select_area = []  # 区域选择集合
                        for drop_item in parent_drop:
                            # 查询子集
                            children = []
                            child_drop = user_session.query(PageDrop).join(Page, PageDrop.ky == Page.id).filter(
                                PageDrop.station == db, PageDrop.ky != 0,
                                PageDrop.p_id == drop_item.id, Page.descr.like('%空调%')).order_by(
                                PageDrop.id.asc()).all()
                            for child_item in child_drop:
                                children.append({
                                    'label': child_item.descr,
                                    'key': str(child_item.ky)
                                })
                            menu_area = {
                                'label': drop_item.en_descr if lang == 'en' else drop_item.descr,
                                'key': str(drop_item.id),
                                'children': children
                            }
                            select_area.append(menu_area)
                    user_session.close()
                    d=select_area
                if lang == 'en':
                    return self.returnTypeSuc_en(data=d, info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data=d, info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            return self.requestError()
        finally:
            user_session.close()
    def disg_chag_day(self, disg_chag_day, e):
        names_ = model_config.get('peizhi', e[1])  # 充放电量name
        names = json.loads(names_)
        conf_name = get_conf_name(e[1])
        name_disg = model_config.get('peizhi', conf_name[8])  # PCS日放电
        name_chag = model_config.get('peizhi', conf_name[9])  # PCS日充电
        list2 = _pcs_day_disg_chag(e[1], names, name_chag, name_disg)
        if list2 != []:
            disg_ = 0
            chag_ = 0
            for l in list2:
                if l['disg'] == '--' or l['chag'] == '--':
                    return '--'
                disg_ += float(l['disg'])
                chag_ += float(l['chag'])
            disg_chag_day = disg_chag_day + disg_ + chag_
        return disg_chag_day
def _return_db_con(db,db_con,name):
    '''根据名称判定数据库链接'''
    if db =='halun' or db == 'taicang':
        return db_con[0]
    else:
        ind = int(name.split('.')[0][-1])
        return db_con[ind-1]
def _select_get_his_value(d, tables,name, lastsec, nowsec, nowdate,lastdate, jiange):
    '''获取历史数据'''
    data = {'time': [], 'value': []}
    for table in tables:
        HisTable_m = HisACDMS(table)
        values = d.query(HisTable_m.value, HisTable_m.ots).filter(HisTable_m.name==name,HisTable_m.dts_s.between(lastsec,nowsec)).order_by(HisTable_m.dts_s.asc()).all()  # 查询当前月
        for val in values:
            data['time'].append(str(val[1])[:19])
            data['value'].append(val[0])

    # 查询上一小时表里的数据
    HisTable_l = HisACDMS('r_measure%s' % timeUtils.getBeforeHouse())
    values = d.query(HisTable_l.value, HisTable_l.ots).filter(HisTable_l.name==name,HisTable_l.dts_s.between(lastsec,nowsec)).order_by(HisTable_l.dts_s.asc()).all()  # 查询上一时刻表
    for val in values:
        data['time'].append(str(val[1])[:19])
        data['value'].append(val[0])
    # print '初始数据-------',data
    if data['time']:
        data['time'].insert(0, lastdate)
        data['value'].insert(0, data['value'][0])
        data['time'].append(nowdate)
        data['value'].append(data['value'][-1])
    else:
        return data
    # 去重
    times = list(set(data['time']))
    times.sort()
    va = []
    for ti in times:
        ind = data['time'].index(ti)
        va.append(data['value'][ind])
    # print '标准返回格式……………………………………………………………………：',complete_data(data,'1T')
    data['time'] = times
    data['value'] = va
    return complete_data(data, jiange)

def _pcs_day_disg_chag(db, names, name_chag, name_disg, lang=None):
    list1 = []
    if db == 'dongmu':
        for i in range(7):
            redisdata = real_data('measure', 'dongmu', 'db')
            time_real = redisdata['utime']
            now_time = timeUtils.getNewTimeStr()
            now_time_ = timeUtils.timeStrToTamp(now_time)
            time_ss = now_time_ - time_real  # 实时值取出来的时间和现在时间的差值
            if time_ss > 125:
                list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': 0, 'chag': 0})
            else:
                for ii in redisdata['body']:
                    if ii['device'][:4] == ('PCS%s' % (i + 1)):
                        disg_ = ii[name_disg]  # PCS日放电
                        chag_ = ii[name_chag]  # PCS日充电
                        list1.append({'name': ('PCS-%s' % (i + 1)), 'disg': ('%.3f' % float(disg_)),
                                      'chag': ('%.3f' % float(chag_))})
    else:
        for name in names:
            v_pcs_name = user_session.query(SdaPcsCu.pcs_name).filter(SdaPcsCu.pcs_id == name,
                                                                      SdaPcsCu.station_name == db).first()
            logging.info('-------------name:%s' % name)
            PCS_name_f = v_pcs_name[0]

            o = real_data('measure', name + name_disg, 'db')
            disg_ = o['value']  # PCS日放电
            o = real_data('measure', name + name_chag, 'db')
            chag_ = o['value']  # PCS日充电
            list1.append({'name': PCS_name_f, 'disg': ('%.3f' % float(disg_)) if disg_ != '--' else '--', 'chag': ('%.3f' % float(chag_)) if chag_ != '--' else '--'})
    return list1
