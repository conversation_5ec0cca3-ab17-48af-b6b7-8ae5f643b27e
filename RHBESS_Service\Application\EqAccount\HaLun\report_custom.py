#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-11-01 16:46:23
#@FilePath     : \RHBESS_empowerd:\emot_pjt_rh\RHBESS_Service\Application\EqAccount\HaLun\report_custom.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-08-27 12:22:13
import copy

import numpy
from openpyxl import load_workbook
import tornado.web
import os
import uuid
import calendar
import datetime
import tornado.web
from Application.Models.base_handler import BaseHandler
from Application.Models.His.r_ACDMS import HisACDMS, HisDM
from Tools.DB.mysql_his import dongmu_session
from Tools.DB.zgtian_bms_his import zgtian1_bms_session,zgtian2_bms_session,zgtian3_bms_session,zgtian4_bms_session
from Tools.Utils.time_utils import timeUtils
from Tools.DB.mysql_user import  user_session
from Application.HistoryData.his_data_alluse import DEBUG
from Application.Models.User.report_f import FReport
from Application.Models.User.custom_report_t import ReportCustom
from Tools.DB.baodian_his import baodian1_session,baodian2_session,baodian3_session,baodian4_session,baodian5_session
from Tools.DB.binhai_bms_his import binhai_session1,binhai_session2
from Tools.DB.taicang_bms_his import taicang_session1
from Tools.DB.ygzhen_bms_his import ygzhen_session1,ygzhen_session2
from Tools.Utils.num_utils import *
from Application.Models.User.station import Station
from Application.Models.User.income_r import RIncome
import math
from Application.Models.User.fault import Fault
from Application.Models.User.availability_t import Availability
from sqlalchemy import func,or_,and_
from Tools.DB.zgtian_his import zgtian1_session,zgtian2_session,zgtian3_session,zgtian4_session
from Tools.DB.redis_con import r_real

bmsdb = {"taicang":[taicang_session1],"binhai":[binhai_session1,binhai_session2],"halun":[],
"ygzhen":[ygzhen_session1,ygzhen_session2],"zgtian":[zgtian1_bms_session,zgtian2_bms_session,zgtian3_bms_session,zgtian4_bms_session]}

baodian_siss = {"tfStbodian1":baodian1_session,"tfStbodian2":baodian2_session,"tfStbodian3":baodian3_session,"tfStbodian4":baodian4_session,"tfStbodian5":baodian5_session}
zgtiann_siss = {"tpStzgtian1":zgtian1_session,"tpStzgtian2":zgtian2_session,"tpStzgtian3":zgtian3_session,"tpStzgtian4":zgtian4_session}

# 所有设备筛选
device_arr={"binhai":[{"name":"全部",'value':"all"},{"name":"储能单元1",'value':'G1.H1.'},{"name":"储能单元2",'value':'G1.H2.'},{"name":"储能单元3",'value':'G2.H1.'},{"name":"储能单元4",'value':'G2.H2.'},
            {"name":"储能单元5",'value':'G3.H1.'},{"name":"储能单元6",'value':'G3.H2.'},{"name":"储能单元7",'value':'G4.H1.'},{"name":"储能单元8",'value':'G4.H2.'},{"name":"储能单元9",'value':'G5.H1.'},
            {"name":"储能单元10",'value':'G5.H2.'},{"name":"储能单元11",'value':'G6.H1.'},{"name":"储能单元12",'value':'G6.H2.'}],
            "taicang":[{"name":"全部",'value':"all"},{"name":"储能单元1",'value':'G1.H1.'},{"name":"储能单元2",'value':'G1.H2.'},{"name":"储能单元3",'value':'G2.H1.'},{"name":"储能单元4",'value':'G2.H2.'},
            {"name":"储能单元5",'value':'G3.H1.'},{"name":"储能单元6",'value':'G3.H2.'},{"name":"储能单元7",'value':'G4.H1.'},{"name":"储能单元8",'value':'G4.H2.'}],
            'ygzhen':[{"name":"全部",'value':"all"},{"name":"储能单元1",'value':'G1.H1.'},{"name":"储能单元2",'value':'G1.H2.'},{"name":"储能单元3",'value':'G2.H1.'},{"name":"储能单元4",'value':'G2.H2.'},
            {"name":"储能单元5",'value':'G3.H1.'},{"name":"储能单元6",'value':'G3.H2.'},{"name":"储能单元7",'value':'G4.H1.'},{"name":"储能单元8",'value':'G4.H2.'}],
            'baodian':[{"name":"全部",'value':"all"},{"name":"储能单元1",'value':'tfStbodian1'},{"name":"储能单元2",'value':'tfStbodian2'},{"name":"储能单元3",'value':'tfStbodian3'},{"name":"储能单元4",'value':'tfStbodian4'},
            {"name":"储能单元5",'value':'tfStbodian5'}],
            'halun':[],
            "zgtian":[{"name":"全部","value":"all"},{"name":"储能单元1","value":"G1.H1."},{"name":"储能单元2","value":"G1.H2."},{"name":"储能单元3","value":"G2.H1."},{"name":"储能单元4","value":"G2.H2."},{"name":"储能单元5","value":"G3.H1."},
                      {"name":"储能单元6","value":"G3.H2."},{"name":"储能单元7","value":"G4.H1."},{"name":"储能单元8","value":"G4.H2."},{"name":"储能单元9","value":"G5.H1."},{"name":"储能单元10","value":"G5.H2."},{"name":"储能单元11","value":"G6.H1."},
                      {"name":"储能单元12","value":"G6.H2."},{"name":"储能单元13","value":"G7.H1."},{"name":"储能单元14","value":"G7.H2."},{"name":"储能单元15","value":"G8.H1."},{"name":"储能单元16","value":"G8.H2."}]}

# 单元所有参数
device_obj = ['CmlChagCapy','CmlDisgCapy','Soh']
# 保电单元所有参数
device_obj_bd = ['.H.M.ChgQuant','.H.M.DchgQuant','.H.M.Soh']

# 电站名称（PCS）
pcs = ["tpSthalun.PcsSt1.Lp1.", "tpSthalun.PcsSt1.Lp2.", "tpSthalun.PcsSt2.Lp1.",
       "tpSthalun.PcsSt2.Lp2.", "tpSttaicang.PcsSt1.Lp1.", "tpSttaicang.PcsSt1.Lp2.",
       "tpSttaicang.PcsSt2.Lp1.", "tpSttaicang.PcsSt2.Lp2.",
       "tpSttaicang.PcsSt3.Lp1.", "tpSttaicang.PcsSt3.Lp2.", "tpSttaicang.PcsSt4.Lp1.",
       "tpSttaicang.PcsSt4.Lp2.", "tpStbinhai1.PcsSt1.Lp1.", "tpStbinhai1.PcsSt1.Lp2.",
       "tpStbinhai1.PcsSt2.Lp1.", "tpStbinhai1.PcsSt2.Lp2.", "tpStbinhai1.PcsSt3.Lp1.",
       "tpStbinhai1.PcsSt3.Lp2.",
       "tpStbinhai2.PcsSt4.Lp1.", "tpStbinhai2.PcsSt4.Lp2.", "tpStbinhai2.PcsSt5.Lp1.",
       "tpStbinhai2.PcsSt5.Lp2.", "tpStbinhai2.PcsSt6.Lp1.", "tpStbinhai2.PcsSt6.Lp2.",
       "tfStygzhen1.EMS.PCS1.Lp1.", "tfStygzhen1.EMS.PCS1.Lp2.", "tfStygzhen1.EMS.PCS1.Lp3.",
       "tfStygzhen1.EMS.PCS1.Lp4.", "tfStygzhen2.EMS.PCS2.Lp1.", "tfStygzhen2.EMS.PCS2.Lp2.",
       "tfStygzhen2.EMS.PCS2.Lp3.", "tfStygzhen2.EMS.PCS2.Lp4.",
       "tfStbodian1.Pc", "tfStbodian2.Pc", "tfStbodian3.Pc", "tfStbodian4.Pc", "tfStbodian5.Pc",
       "dongmu.PCS1","dongmu.PCS2","dongmu.PCS3","dongmu.PCS4","dongmu.PCS5","dongmu.PCS6","dongmu.PCS7","tfStzgtian1.EMS.PCS1.Lp1.","tfStzgtian1.EMS.PCS1.Lp2.","tfStzgtian1.EMS.PCS1.Lp3.","tfStzgtian1.EMS.PCS1.Lp4.",
       "tfStzgtian2.EMS.PCS2.Lp1.", "tfStzgtian2.EMS.PCS2.Lp2.", "tfStzgtian2.EMS.PCS2.Lp3.",
       "tfStzgtian2.EMS.PCS2.Lp4.","tfStzgtian3.EMS.PCS3.Lp1.","tfStzgtian3.EMS.PCS3.Lp2.","tfStzgtian3.EMS.PCS3.Lp3.","tfStzgtian3.EMS.PCS3.Lp4.",
       "tfStzgtian4.EMS.PCS4.Lp1.", "tfStzgtian4.EMS.PCS4.Lp2.", "tfStzgtian4.EMS.PCS4.Lp3.",
       "tfStzgtian4.EMS.PCS4.Lp4."]
# 冻结表，（关口表名称）
gateway={"ygzhen":["tfStygzhen1.EMS.MET."],"zgtian":["tfStzgtian1.EMS.MET."]}
from Application.Cfg.dir_cfg import model_config as model_config_base
import pymysql
from dbutils.persistent_db import PersistentDB
pool = PersistentDB(pymysql, 10,**{
            "host": model_config_base.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config_base.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config_base.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config_base.get('mysql', "IDCS_DATABASE"),  # 数据库名称
            "port":  int(model_config_base.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })

class ReportCustomInterface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        db = self.get_argument('db', 'his')
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            if kt == 'GetCustomReportWeek':
                year = self.get_argument('year', None)  # 年份
                month = self.get_argument('month', None)  # 月份
                week = self.get_argument('week', None)  # 周
                user = self.get_argument('user', None)  # 用户
                day = self.get_argument('day', None)  # 运行天数
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                ty_ = self.get_argument('ty_', 1)  # 类型（1pc,2移动）
                blur = self.get_argument('blur', None)  # 移动端模糊查询字段
                filter, data = [ReportCustom.station == db, ReportCustom.flag == 1], []
                filter_1 = [Availability.station == db]
                if ty_ == 1:
                    if year:
                        filter.append(ReportCustom.year.contains(year))
                        filter_1.append(Availability.date.contains(year))
                    if month:
                        filter.append(ReportCustom.month.contains(month))
                        filter_1.append(Availability.date.contains(month))
                    if week:
                        filter.append(ReportCustom.week.contains(week))
                        filter_1.append(Availability.week.contains(week))
                    if user:
                        if lang=='en':
                            filter.append(ReportCustom.en_user.contains(user))
                            filter_1.append(Availability.en_user.contains(user))
                        else:
                            filter.append(ReportCustom.user.contains(user))
                            filter_1.append(Availability.user.contains(user))
                    if day:
                        filter.append(ReportCustom.day == day)
                        filter_1.append(Availability.day == day)
                elif ty_ == '2':
                    if year:
                        filter.append(ReportCustom.year == year)
                        filter_1.append(Availability.year == year)
                    if month:
                        filter.append(ReportCustom.month == month)
                        filter_1.append(Availability.date.contains(month))
                    if week:
                        filter.append(ReportCustom.week == week)
                        filter_1.append(Availability.week.contains(week))
                    if user:
                        if lang == 'en':
                            filter.append(ReportCustom.en_user.contains(user))
                            filter_1.append(Availability.en_user.contains(user))
                        else:
                            filter.append(ReportCustom.user.contains(user))
                            filter_1.append(Availability.user.contains(user))
                    if day:
                        day = json.loads(day)
                        filter.append(ReportCustom.day.between(int(day[0]), int(day[1])))
                        filter_1.append(Availability.day.between(int(day[0]), int(day[1])))
                    if blur:
                        filter.append(ReportCustom.title.contains(blur))
                        filter_1.append(Availability.date.contains(blur))
                        filter_1.append(Availability.week.contains(blur))
                        filter_1.append(Availability.user.contains(blur))
                        filter_1.append(Availability.day.contains(blur))
                if DEBUG:
                    logging.info('year:%s,month:%s,week:%s,user:%s,day:%s,pageNum:%s,pageSize:%s' % (
                    year, month, week, user, day, pageNum, pageSize))
                total = user_session.query(func.count(ReportCustom.id)).filter(*filter).scalar()
                all = user_session.query(ReportCustom).filter(*filter).order_by(ReportCustom.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                a = user_session.query(Availability).filter(*filter_1).all()
                if lang=='en':
                    for org in all:
                        obj = {"id": org.id, 'sys_availty': org.sys_availty, 'AGC': org.agc, 'list': []}
                        if not org.en_table_list:
                            continue
                        if not is_valid_json(org.en_table_list):
                            continue
                        for i in json.loads(org.en_table_list):
                            ppp = i
                            if 'date' in i.keys():
                                for e in a:
                                    if i['date'] == e.en_date and (e.type_ == '1' or e.type_ == 1):
                                        ppp['descr'] = e.en_descr  # 描述
                                        ppp['list_2'] = e.table_list  #
                                    elif i['date'] == e.en_date and (e.type_ == '2' or e.type_ == 2):
                                        ppp['descr'] = e.en_descr  # 描述
                                        ppp['list_3'] = e.table_list  #
                            obj['list'].append(ppp)
                        for k, v in eval(str(org.en_title)).items():
                            obj[k] = v
                        data.append(obj)
                else:
                    for org in all:
                        obj = {"id": org.id, 'sys_availty': org.sys_availty, 'AGC': org.agc, 'list': []}
                        for i in eval(str(org.table_list)):
                            ppp = i
                            if 'date' in i.keys():
                                for e in a:
                                    if i['date'] == e.date and (e.type_ == '1' or e.type_ == 1):
                                        ppp['descr'] = e.descr  # 描述
                                        ppp['list_2'] = e.table_list  #

                                    elif i['date'] == e.date and (e.type_ == '2' or e.type_ == 2):
                                        ppp['descr'] = e.descr  # 描述
                                        ppp['list_3'] = e.table_list  #
                            obj['list'].append(ppp)
                        for k, v in eval(str(org.title)).items():
                            obj[k] = v
                        data.append(obj)
                self.returnTotalSuccess(data, total)
            elif kt == 'CheckCustomReportWeek':
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                week = self.get_argument('week', '')  # 周
                if DEBUG:
                    logging.info('year:%s,month:%s,week:%s' % (year, month, week))
                aa = user_session.query(ReportCustom).filter(ReportCustom.station == db, ReportCustom.flag == 1,
                                                             ReportCustom.year == year, ReportCustom.month == month,
                                                             ReportCustom.week == week).first()
                if aa:
                    return self.returnTypeSuc(True)
                else:
                    return self.returnTypeSuc(False)
            elif kt == 'LoadCustomReportMonth':  # 加载自定义月报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                obj = {}
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                st_time = year + '-' + month + '-01 00:00:00'
                en_time = str(timeUtils.last_day_of_month(int(year), int(month))) + ' 23:59:59'
                time_ = timeUtils.timeSeconds(st_time, en_time) / 3600  # 月时间
                year_month = year + '-' + month
                rrr = user_session.query(Availability).filter(Availability.date.like(year_month + '%'),
                                                              Availability.station == db).all()  #
                obj['sys_availty'] = '100.00'
                sys_availty, agc = self.sys_availty(rrr, time_, volume)
                if sys_availty:
                    obj['sys_availty'] = sys_availty  # 可用率
                days = len(timeUtils.getAllDaysByMonth((year_month)))
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.month == month,
                                                                                ReportCustom.flag == 1).first()  # 获取周报表里的AGC值
                AGC_2 = 0
                if AGC_zhi[0] != None:
                    AGC = AGC_zhi[0]
                    AGC_2 = ('%.2f' % (((AGC) / (60 * 24 * days)) * 100)) if AGC else None
                if type == '1':
                    if AGC_2:
                        obj['day'] = AGC_2  # AGC
                    else:
                        obj['day'] = 0  # AGC
                else:
                    obj['day'] = ''
                day = year + '-' + month
                data1, data2, data3 = [], [], []  # 放电,日效率,充电
                station = []

                name = ''
                if db == 'baodian':
                    name = 'bodian'
                else:
                    name = db
                for i in pcs:
                    if name in i:
                        station.append(i)
                starttime = day + '-01'
                datestart = datetime.datetime.strptime(starttime[0:10], '%Y-%m-%d')
                endtime = datetime.datetime(datestart.year, datestart.month,calendar.monthrange(datestart.year, datestart.month)[1])
                endtimestr = endtime.strftime('%Y-%m-%d')
                time_list = timeUtils.dateToDataList(starttime, endtimestr)

                for time in time_list:
                    freport = user_session.query(FReport).filter(FReport.day == time + ' 00:00:00',FReport.name.in_(station), FReport.cause == 1).all()
                    disg = 0  # 放电量
                    chag = 0  # 充电量
                    count = 0  # 收益条数
                    sumratio = 0.0  # 总收益
                    if freport:
                        for f in freport:
                            d = np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(
                                eval(f.gd_disg))
                            c = np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(
                                eval(f.gd_chag))
                            if d < 65535:
                                disg = disg + d
                            if c < 65535:
                                chag = chag + c
                            if f.ratio != 0:
                                sumratio += float(f.ratio)
                                count += 1
                        data1.append(('%.2f' % disg))
                        data3.append(('%.2f' % chag))
                        if count == 0:
                            data2.append(0)
                        else:
                            data2.append(('%.2f' % ((sumratio / count) * 100)))
                    else:
                        data1.append('0.00')
                        data2.append(0)
                        data3.append('0.00')
                if type == '2':
                    obj['day_disgCapy'] = data1  # 日放电
                    obj['day_efficiency'] = data2  # 日效率
                    obj['day_chagCapy'] = data3  # 日充电
                    obj['time'] = time_list  # 时间维度
                info = []
                pages = user_session.query(Fault.fault_problem, Fault.happen_time, Fault.finish_time, Fault.cause,
                                           Fault.repair_way,Fault.en_fault_problem, Fault.en_cause,Fault.en_repair_way).filter(Fault.station == db, Fault.is_use == 1).filter \
                    (or_(Fault.happen_time.like(day + '%'), Fault.finish_time.like(day + '%'),
                         and_(Fault.happen_time < endtimestr + ' 00:00:00', Fault.finish_time == None))).all()
                for pag in pages:
                    o = {'fault_problem': '', 'happen_time': '', 'finish_time': '', 'cause': '', 'repair_way': ''}
                    o['happen_time'] = str(pag[1])
                    o['finish_time'] = str(pag[2])
                    if lang=='en':
                        o['fault_problem'] = str(pag[5])
                        o['cause'] = str(pag[6])
                        o['repair_way'] = str(pag[7])
                    else:
                        o['fault_problem'] = str(pag[0])
                        o['cause'] = str(pag[3])
                        o['repair_way'] = str(pag[4])
                    info.append(o)
                # 故障需要实时翻译
                obj['fault_info'] = info  # 故障信息
                if db == 'dongmu':
                    s_time = year + '-' + month + '-' + '01 00:00:00'  # 月开始时间
                    e_time_ = timeUtils.last_day_of_month(int(year), int(month))
                    e_time = e_time_.strftime('%Y-%m-%d') + ' 23:59:59'  # 月结束时间
                    s_time_Stamp = timeUtils.timeStrToTamp(s_time)
                    e_time_Stamp = timeUtils.timeStrToTamp(e_time)
                    dm_table = HisDM('r_cumulant')
                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                        dm_table.time.between(s_time_Stamp, e_time_Stamp)).order_by(dm_table.time.asc()).all()
                    if not values_mong:
                        obj['chag_mon_DC'] = 0  # 直流测月总充电
                        obj['disg_mon_DC'] = 0  # 直流测月总放电
                        obj['chag_total_DC'] = 0  # 直流测累计总充电
                        obj['disg_total_DC'] = 0  # 直流测累计总放电
                    else:
                        values_1_s = {'datainfo': {}}
                        values_1_e = {'datainfo': {}}
                        values_1_s['datainfo'] = values_mong[0]['datainfo']
                        values_1_e['datainfo'] = values_mong[-1]['datainfo']
                        value_s = json.loads(values_1_s['datainfo'])['body']
                        value_e = json.loads(values_1_e['datainfo'])['body']
                        BDcap_s = 0  # 月初电池总放电量
                        BCCap_s = 0  # 月初电池总充电量

                        for ii in value_s:
                            if ii['device'][:3] == 'PCS':
                                BDcap_s = BDcap_s + float(ii['BDcap'])  # 电池总放电量
                                BCCap_s = BCCap_s + float(ii['BCCap'])  # 电池总充电量

                        BDcap_e = 0  # 月底电池总放电量
                        BCCap_e = 0  # 月底电池总充电量
                        for ii in value_e:
                            if ii['device'][:3] == 'PCS':
                                BDcap_e = BDcap_e + float(ii['BDcap'])  # 电池总放电量
                                BCCap_e = BCCap_e + float(ii['BCCap'])  # 电池总充电量
                        chag_mon_DC = BCCap_e - BCCap_s  # 直流测月总充电
                        disg_mon_DC = BDcap_e - BDcap_s  # 直流测月总放电
                        chag_total_DC = BDcap_e  # 直流测累计总充电
                        disg_total_DC = BCCap_e  # 直流测累计总充电
                        obj['chag_mon_DC'] = ('%.2f' % chag_mon_DC)  # 直流测月总充电
                        obj['disg_mon_DC'] = ('%.2f' % disg_mon_DC)  # 直流测月总放电
                        obj['chag_total_DC'] = ('%.2f' % chag_total_DC)  # 直流测累计总充电
                        obj['disg_total_DC'] = ('%.2f' % disg_total_DC)  # 直流测累计总放电

                    dm_table = HisDM('r_measure')
                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                        dm_table.time.between(s_time_Stamp, e_time_Stamp)).order_by(dm_table.time.asc()).all()
                    if not values_mong:
                        obj['SOH'] = 100.00  # SOH
                    else:
                        # 因SOH变化较小故可以采用收尾数字计算，其他变量不可采用此方法
                        soh_arr = []
                        value_1 = json.loads(values_mong[0]['datainfo'])['body']
                        for ii in value_1:
                            if ii['device'][:3] == 'BMS':
                                soh_arr.append(float(ii['CSOH']))
                        value_2 = json.loads(values_mong[-1]['datainfo'])['body']
                        for ii in value_2:
                            if ii['device'][:3] == 'BMS':
                                soh_arr.append(float(ii['CSOH']))
                        if soh_arr:
                            SOH = round(np.mean(soh_arr), 2)
                        else:
                            SOH = 100.00
                        obj['SOH'] = SOH  # SOH
                    dongmu_session.close()
                else:
                    # 计算时间范围内的所有年月
                    # table = 'r_measure' + year + month
                    # HisTable = HisACDMS("ods_r_measure1")
                    # a = []
                    # for i in device_arr[db][1:]:  # 去掉第一个值
                    #     a.append(i['value'])
                    # bams = a
                    # chag_mon_DC = 0  # 直流测月总充电
                    # disg_mon_DC = 0  # 直流测月总放电
                    # chag_total_DC = 0  # 直流测累计总充电
                    # disg_total_DC = 0  # 直流测累计总放电
                    # SOH = 0  # SOH
                    # num = 0  # 电站个数
                    # for d in bams:
                        # db_con = _return_db_con(db, d)  # 获取具体数据库链接
                        # if db == 'baodian':  # 保电项目
                        #     n0 = '%s%s' % (d, device_obj_bd[0])  # 累计总充电量
                        #     n1 = '%s%s' % (d, device_obj_bd[1])  # 累计总放电量
                        #     n2 = '%s%s' % (d, device_obj_bd[2])  # SOH
                        # else:
                        #     n0 = '%s%s' % (d, device_obj[0])  # 累计总充电量
                        #     n1 = '%s%s' % (d, device_obj[1])  # 累计总放电量
                        #     n2 = '%s%s' % (d, device_obj[2])  # SOH
                        # try:
                        #     value0 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n0,
                        #                                                                  HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                        #         HisTable.dts_s.desc()).first()  # 查询当前月表  累计总充电量最后一个值
                        #     value1 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n1,
                        #                                                                  HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                        #         HisTable.dts_s.desc()).first()  # 查询当前月表  累计总放电量最后一个值
                        # except:
                        #     obj['chag_mon_DC'] = ''  # 直流测月总充电
                        #     obj['disg_mon_DC'] = ''  # 直流测月总放电
                        #     obj['chag_total_DC'] = ''  # 直流测累计总充电
                        #     obj['disg_total_DC'] = ''  # 直流测累计总放电
                        #     obj['SOH'] = ''  # SOH
                        #     break
                        # if value0:
                        #     chag_total_DC += value0[0]  # 直流测累计总充电
                        # if value1:
                        #     disg_total_DC += value1[0]  # 直流测累计总放电
                        # value3 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n0,
                        #                                                              HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                        #     HisTable.dts_s.asc()).first()  # 查询当前月表 累计总充电量第一个值
                        # value4 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n1,
                        #                                                              HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                        #     HisTable.dts_s.asc()).first()  # 查询当前月表 累计总放电量第一个值
                        # if value0 and value3:
                        #     V3 = value0[0] - value3[0]  # 直流测月充电
                        # else:
                        #     V3 = 0
                        # if value1 and value4:
                        #     V4 = value1[0] - value4[0]  # 直流测月放电
                        # else:
                        #     V4 = 0
                        # chag_mon_DC += V3  # 直流测月总充电
                        # disg_mon_DC += V4  # 直流测月总放电
                        # value2 = db_con.query(HisTable.value).filter(HisTable.name == n2, HisTable.dts_s.between(st_time, en_time)).all()  # 查询当前月表
                        # for v in value2:
                        #     if v[0] != 0 and v[0] < 100:
                        #         num += 1
                        #         SOH += v[0]
                    # 从dws库取数据
                    conn = pool.connection()
                    cursor = conn.cursor()
                    # 直流测月总充电/直流测月总放电
                    year_month= str(year) + str(month)
                    chag_disg_month_DC_sql = ("select sum(chag) as chag_energy, sum(disg) as disg_energy  from {} where station_name='{}' and date_type='year_month' and date_value='{}'".
                                     format('dws_bc_measure_cw_cm_cy', db, year_month))
                    cursor.execute(chag_disg_month_DC_sql)
                    sum_chag_disg_month_DC = cursor.fetchone()

                    # 直流测累计总充电/直流测累计总放电
                    chag_disg_total_DC_sql = ("select sum(chag) as chag_energy, sum(disg) as disg_energy  from {} where station_name='{}' and date_type='year' and date_value='{}'".
                                     format('dws_bc_measure_cw_cm_cy', db, str(year)))
                    cursor.execute(chag_disg_total_DC_sql)
                    sum_chag_disg_total_DC = cursor.fetchone()

                    # SOH
                    sql_avg_soh = ("select avg(soh) as avg_soh from {} where station_name='{}' and day>='{}' and day<='{}'".
                                     format('dws_st_measure_win_15min', db, st_time,en_time))
                    cursor.execute(sql_avg_soh)
                    avg_soh = cursor.fetchone()
                    cursor.close()
                    conn.close()
                    # if num == 0:
                    #     SOH = 100
                    # elif SOH == 0:
                    #     SOH = 100
                    # else:
                    #     SOH = SOH / num
                    obj['chag_mon_DC'] = sum_chag_disg_month_DC['chag_energy'] if sum_chag_disg_month_DC['chag_energy'] else 0  # 直流测月总充电
                    obj['disg_mon_DC'] = sum_chag_disg_month_DC['disg_energy'] if sum_chag_disg_month_DC['disg_energy'] else 0  # 直流测月总放电
                    obj['chag_total_DC'] = sum_chag_disg_total_DC['chag_energy'] if sum_chag_disg_total_DC['chag_energy'] else 0  # 直流测累计总充电
                    obj['disg_total_DC'] = sum_chag_disg_total_DC['disg_energy'] if sum_chag_disg_total_DC['disg_energy'] else 0  # 直流测累计总放电
                    obj['SOH'] = float('%.2f' % (avg_soh['avg_soh'] if avg_soh['avg_soh'] else 100))  # SOH
                session = self.getOrNewSession()
                informant = session.user['name']
                obj['user'] = informant  # 填报人
                obj['income_mon'] = 0  # 月收益
                obj['chag_mon_meas'] = 0  # 计量测月总充电
                obj['disg_mon_meas'] = 0  # 计量测月总放电
                obj['income_year'] = 0  # 年收益
                obj['income_rate'] = 0  # 年收益完成率
                obj['chag_total_meas'] = 0  # 计量测累计充电
                obj['disg_total_meas'] = 0  # 计量测累计放电
                obj['overall_effic_mon'] = 0  # 月综合效率
                obj['overall_effic_total'] = 0  # 累计综合效率
                obj['availab_index'] = '99.00'  # 可用率指标
                income_year = 0
                target_income = 0
                obj['income_year'] = 0  # 年收益
                obj['income_rate'] = 0  # 年收益完成率
                filter = [RIncome.station_name == db, RIncome.is_use == 1]
                filter.append(RIncome.day.like(year + '%'))
                filter.append(RIncome.in_ts == '2')
                filter_3 = [RIncome.station_name == db, RIncome.is_use == 1]
                filter_2 = [RIncome.station_name == db, RIncome.is_use == 1]
                filter_3.append(RIncome.day == year + '-' + month)
                ricome_1 = user_session.query(RIncome.income_yu, RIncome.target_income, RIncome.day).filter(
                    *filter).group_by(RIncome.day).order_by(RIncome.day.desc()).all()  # 查询收益数据
                if ricome_1:
                    for r in ricome_1:
                        if r[2] <= year + '-' + month:
                            income_year += float(r[0]) if r[0] else '--'  # 年收益
                            target_income += float(r[1]) if r[1] else '--'  # 年收益完成率

                    obj['income_year'] = ('%.4f' % (income_year / 10000)) if income_year != '--' else '--'  # 年收益
                    try:
                        obj['income_rate'] = ('%.2f' % (((income_year / 10000) / target_income) * 100)) if target_income != '--' else '--' # 年收益完成率
                    except:
                        pass
                ricome_3 = user_session.query(func.sum(RIncome.income), func.sum(RIncome.target_income),
                                              func.sum(RIncome.chag), func.sum(RIncome.disg), RIncome.day).filter(
                    *filter_3).group_by(RIncome.day).order_by(RIncome.day.desc()).all()  # 查询收益数据
                if ricome_3:
                    for r in ricome_3:
                        if r[4] == year + '-' + month:
                            obj['income_mon'] = ('%.4f' % (r[0]))  # 月收益
                            obj['chag_mon_meas'] = r[2]  # 计量测月总充电
                            obj['disg_mon_meas'] = r[3]  # 计量测月总放电
                            if r[2] and r[3]:
                                obj['overall_effic_mon'] = ('%.2f' % ((r[3] / r[2]) * 100))  # 月综合效率
                ricome_2 = user_session.query(func.sum(RIncome.chag), func.sum(RIncome.disg)).filter(
                    *filter_2).order_by(RIncome.day.desc()).all()  # 查询电量数据
                if ricome_2[0][0] != None:
                    obj['chag_total_meas'] = ricome_2[0][0]  # 计量测累计充电
                    obj['disg_total_meas'] = ricome_2[0][1]  # 计量测累计放电
                    if ricome_2[0][0] and ricome_2[0][1]:
                        if ricome_2[0][0] != 0:
                            obj['overall_effic_total'] = ('%.2f' % ((ricome_2[0][1] / ricome_2[0][0]) * 100))  # 累计综合效率
                if type == '1':
                    obj['unit_k_value'] = 0  # 机组K值  value2
                    obj['unit_aver_quotation'] = 0  # 机组平均报价  value6
                    unit_k = []  # K值
                    unit_avg = []  # 平均报价
                    reportw = user_session.query(ReportCustom.table_list).filter(ReportCustom.station == db,
                                                                                 ReportCustom.year == year,
                                                                                 ReportCustom.month == month,
                                                                                 ReportCustom.flag == 1,
                                                                                 ReportCustom.is_use == 1).order_by(
                        ReportCustom.id.desc()).all()  # 获取周报表里机组值
                    if reportw:
                        for org in reportw:
                            for i in eval(str(org[0])):
                                if 'value2' in i.keys() or 'value6' in i.keys():
                                    try:
                                        unit_k.append(float(i['value2']))
                                    except:
                                        continue
                                    try:
                                        unit_avg.append(float(i['value6']))
                                    except:
                                        continue
                        obj['unit_k_value'] = round(numpy.average(unit_k), 2)  # 机组K值
                        obj['unit_aver_quotation'] = round(numpy.average(unit_avg), 2)  # 机组平均报价
                        if math.isnan(obj['unit_k_value']) is True:
                            obj['unit_k_value'] = 0  # 机组K值
                        if math.isnan(obj['unit_aver_quotation']) is True:
                            obj['unit_aver_quotation'] = 0  # 机组平均报价
                toall_chagCapy = obj['chag_mon_meas'] * obj['disg_mon_meas']
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站
                chag_disg_number = int(math.sqrt(toall_chagCapy) / (float(volume[0] * 1000)))  # 月充放电次数
                obj['chag_disg_number'] = chag_disg_number  # 月充放电次数
                toall_chagdig = obj['chag_total_meas'] * obj['disg_total_meas']  # 计量测累计充电量聚合值
                residue_degree = 5000 - int(math.sqrt(toall_chagdig) / (volume[0] * 1000))  # 剩余次数
                obj['residue_degree'] = residue_degree  # 剩余次数
                dongmu_session.close()
                return self.returnTypeSuc(obj)
            elif kt == 'GetCustomReportMonth':  # 查询月报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', None)  # 年份
                month = self.get_argument('month', None)  # 月份
                user = self.get_argument('user', None)  # 用户
                day = self.get_argument('day', None)  # 运行天数
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter, data = [ReportCustom.station == db, ReportCustom.flag == 2, ReportCustom.type == type,
                                ReportCustom.is_use == '1'], []
                if year:
                    filter.append(ReportCustom.year.contains(year))
                if month:
                    filter.append(ReportCustom.month.contains(month))
                if user:
                    if lang=='en':
                        filter.append(ReportCustom.en_user.contains(user))
                    else:
                        filter.append(ReportCustom.user.contains(user))
                if day:
                    filter.append(ReportCustom.day == day)
                if DEBUG:
                    logging.info('year:%s,month:%s,user:%s,day:%s,pageNum:%s,pageSize:%s' % (
                    year, month, user, day, pageNum, pageSize))
                total = user_session.query(func.count(ReportCustom.id)).filter(*filter).scalar()
                all = user_session.query(ReportCustom).filter(*filter).order_by(ReportCustom.id.desc()).limit(
                    pageSize).offset((pageNum - 1) * pageSize).all()
                for org in all:
                    obj = {"id": org.id, 'AGC': org.agc}
                    if lang=='en':
                        for k, v in eval(str(org.en_title)).items():
                            obj[k] = v
                    else:
                        for k, v in eval(str(org.title)).items():
                            obj[k] = v
                    if lang == 'en':
                        for k, v in eval(str(org.en_table_list)).items():
                            obj[k] = v
                    else:
                        for k, v in eval(str(org.table_list)).items():
                            obj[k] = v
                    data.append(obj)
                self.returnTotalSuccess(data, total)
            elif kt == 'GetCustomReportYear':  # 查询年报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', None)  # 年份
                user = self.get_argument('user', None)  # 用户
                day = self.get_argument('day', None)  # 运行天数
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter, data = [ReportCustom.station == db, ReportCustom.flag == 3, ReportCustom.type == type,
                                ReportCustom.is_use == '1'], []
                if year:
                    filter.append(ReportCustom.year.contains(year))
                if user:
                    if lang=='en':
                        filter.append(ReportCustom.en_user.contains(user))
                    else:
                        filter.append(ReportCustom.user.contains(user))
                if day:
                    filter.append(ReportCustom.table_list.day == day)
                if DEBUG:
                    logging.info('year:%s,user:%s,day:%s,pageNum:%s,pageSize:%s' % (year, user, day, pageNum, pageSize))
                total = user_session.query(func.count(ReportCustom.id)).filter(*filter).scalar()
                all = user_session.query(ReportCustom).filter(*filter).order_by(ReportCustom.id.desc()).limit(
                    pageSize).offset((pageNum - 1) * pageSize).all()
                if lang == 'en':
                    for org in all:
                        obj = {"id": org.id, "AGC": org.agc}
                        for k, v in eval(str(org.en_title)).items():
                            obj[k] = v
                        for k, v in eval(str(org.en_table_list)).items():
                            obj[k] = v
                        data.append(obj)
                else:
                    for org in all:
                        obj = {"id": org.id, "AGC": org.agc}
                        for k, v in eval(str(org.title)).items():
                            obj[k] = v
                        for k, v in eval(str(org.table_list)).items():
                            obj[k] = v
                        data.append(obj)
                self.returnTotalSuccess(data, total)
            elif kt == 'LoadCustomReportYear':  # 加载自定义年报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', '')  # 年份
                obj = {}
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站容量
                st_time = year + '-01-01 00:00:00'
                en_time = year + '-12-31 23:59:59'
                time_ = timeUtils.timeSeconds(st_time, en_time) / 3600  # 时间
                rrr = user_session.query(Availability).filter(Availability.date.contains(year),
                                                              Availability.station == db).all()  #
                sys_availty, agc = self.sys_availty(rrr, time_, volume)
                obj['sys_availty'] = '100.00'  # 可用率
                if sys_availty:
                    obj['sys_availty'] = sys_availty  # 可用率
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.flag == 1).first()  # 获取周报表里的AGC值
                AGC_2 = 0
                if AGC_zhi:
                    AGC = AGC_zhi[0]
                    AGC_2 = ('%.2f' % ((float(AGC) / (60 * 24 * 365)) * 100)) if AGC else None
                if type == '1':
                    if AGC_2:
                        obj['day'] = AGC_2  # AGC
                    else:
                        obj['day'] = 0  # AGC
                else:
                    obj['day'] = ''
                now_time = timeUtils.getNewTimeStr()
                now_year = now_time[0:4]
                now_month = now_time[5:7]
                time_list = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
                data1, data2, data3 = [], [], []  # 放电,日效率,充电
                station = []
                name = ''
                if db == 'baodian':
                    name = 'bodian'
                else:
                    name = db
                for i in pcs:
                    if name in i:
                        station.append(i)
                for time in time_list:
                    freport = user_session.query(FReport).filter(FReport.day.like(year + '-' + time + '%'),
                                                                 FReport.name.in_(station), FReport.cause == 1).all()
                    disg = 0  # 放电量
                    chag = 0  # 充电量
                    count = 0  # 收益条数
                    sumratio = 0.0  # 总收益
                    if freport:
                        for f in freport:
                            d = np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(
                                eval(f.gd_disg))
                            c = np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(
                                eval(f.gd_chag))
                            if d < 65535:
                                disg = disg + d
                            if c < 65535:
                                chag = chag + c
                            if f.ratio != 0:
                                sumratio += float(f.ratio)
                                count += 1
                        data1.append(('%.2f' % disg))
                        data3.append(('%.2f' % chag))
                        if count == 0:
                            data2.append(0)
                        else:
                            data2.append(('%.2f' % ((sumratio / count) * 100)))
                    else:
                        data1.append('0.00')
                        data2.append('0.00')
                        data3.append('0.00')
                if type == '2':
                    obj['mon_disgCapy'] = data1  # 月放电
                    obj['mon_efficiency'] = data2  # 月效率
                    obj['mon_chagCapy'] = data3  # 月充电
                    obj['time'] = time_list  # 时间维度
                toall_chagCapy = 0  # 交流侧年充电量聚合值
                for d in data3:
                    toall_chagCapy += float(d)
                if db == 'dongmu':
                    now_time = timeUtils.getNewTimeStr()[0:7]
                    now_year = now_time[0:4]
                    now_month = now_time[5:7]

                    s_time = year + '-' + '01' + '-' + '01 00:00:00'  # 年开始时间
                    e_time = str(int(year) + 1) + '-' + '01' + '-' + '01 00:00:00'  # 年结束时间
                    time_s = timeUtils.timeStrToTamp(s_time)
                    time_e = timeUtils.timeStrToTamp(e_time)
                    dm_table = HisDM('r_cumulant')
                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                        dm_table.time.between(time_s, time_e)).order_by(dm_table.time.asc()).all()
                    if not values_mong:
                        obj['chag_year_DC'] = 0  # 直流测年总充电
                        obj['disg_year_DC'] = 0  # 直流测年总放电
                        obj['chag_total_DC'] = 0  #
                        obj['disg_total_DC'] = 0  #
                    else:
                        values_1_e = {'datainfo': {}}
                        values_1_e['datainfo'] = values_mong[-1]['datainfo']
                        value_e = json.loads(values_1_e['datainfo'])['body']
                        DCapY_e = 0  # 当年放电量
                        CCapY_e = 0  # 当年充电量
                        BCCap_e = 0  # 累计充电量
                        BDcap_e = 0  # 累计放电量
                        for ii in value_e:
                            if ii['device'][:3] == 'PCS':
                                DCapY_e = DCapY_e + float(ii['DCapY'])  # 当年放电量
                                CCapY_e = CCapY_e + float(ii['CCapY'])  # 当年充电量
                                BDcap_e = BDcap_e + float(ii['BDcap'])  # 电池总放电量
                                BCCap_e = BCCap_e + float(ii['BCCap'])  # 电池总充电量
                        obj['chag_year_DC'] = ('%.2f' % CCapY_e)  # 直流测年总充电
                        obj['disg_year_DC'] = ('%.2f' % DCapY_e)  # 直流测年总放电
                        obj['chag_total_DC'] = ('%.2f' % BCCap_e)  #
                        obj['disg_total_DC'] = ('%.2f' % BDcap_e)  #
                    dm_table = HisDM('r_measure')
                    values_mong = dongmu_session.query(dm_table.datainfo).filter(
                        dm_table.time.between(time_s, time_e)).order_by(dm_table.time.asc()).all()
                    if not values_mong:
                        obj['SOH'] = 100.00  # SOH
                    else:
                        # 因SOH变化较小故可以采用收尾数字计算，其他变量不可采用此方法
                        soh_arr = []
                        value_1 = json.loads(values_mong[0]['datainfo'])['body']
                        for ii in value_1:
                            if ii['device'][:3] == 'BMS':
                                soh_arr.append(float(ii['CSOH']))
                        value_2 = json.loads(values_mong[-1]['datainfo'])['body']
                        for ii in value_2:
                            if ii['device'][:3] == 'BMS':
                                soh_arr.append(float(ii['CSOH']))
                        if soh_arr:
                            SOH = round(np.mean(soh_arr), 2)
                        else:
                            SOH = 100.00

                        obj['SOH'] = SOH  # SOH
                    dongmu_session.close()
                else:
                    now_time = timeUtils.getNewTimeStr()[0:7]
                    a = []
                    # for i in device_arr[db][1:]:  # 去掉第一个值
                    #     a.append(i['value'])
                    # bams = a
                    # chag_year_DC = 0  # 直流测年总充电
                    # disg_year_DC = 0  # 直流测年总放电
                    # chag_total_DC = 0  # 直流测累计总充电
                    # disg_total_DC = 0  # 直流测累计总放电
                    # SOH = 0  # SOH
                    # num = 0  # 电站个数
                    # for d in bams:
                    #     db_con = _return_db_con(db, d)  # 获取具体数据库链接
                    #     if db == 'baodian':  # 保电项目
                    #         n0 = '%s%s' % (d, device_obj_bd[0])  # 累计总充电量
                    #         n1 = '%s%s' % (d, device_obj_bd[1])  # 累计总放电量
                    #         n2 = '%s%s' % (d, device_obj_bd[2])  # SOH
                    #     else:
                    #         n0 = '%s%s' % (d, device_obj[0])  # 累计总充电量
                    #         n1 = '%s%s' % (d, device_obj[1])  # 累计总放电量
                    #         n2 = '%s%s' % (d, device_obj[2])  # SOH
                    #     try:
                    #         if int(now_year) > int(year):
                    #             # table_12 = 'r_measure' + year + '12'
                    #             HisTable = HisACDMS("ods_r_measure1")
                    #             value0 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n0,
                    #                                                                          HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                    #                 HisTable.dts_s.desc()).first()  # 查询当年最后一个月  累计总充电量最后一个值
                    #             value1 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n1,
                    #                                                                          HisTable.value != 0, HisTable.dts_s.between(st_time, en_time)).order_by(
                    #                 HisTable.dts_s.desc()).first()  # 查询当年最后一个月  累计总放电量最后一个值
                    #         else:
                    #             # table_now = 'r_measure' + timeUtils.getNewTimeStr()[0:7] 2024-11
                    #             # table_now = ''.join(table_now.split('-')) if table_now else ''
                    #             now_month_first_day = timeUtils.getNewTimeStr()[0:7] + '-01'  #当前月第一天
                    #             HisTable = HisACDMS("ods_r_measure1")
                    #             value0 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n0,
                    #                                                                          HisTable.value != 0, HisTable.dts_s>=now_month_first_day).order_by(
                    #                 HisTable.dts_s.desc()).first()  # 查询当年最近一个月  累计总充电量最后一个值
                    #             value1 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n1,
                    #                                                                          HisTable.value != 0, HisTable.dts_s>=now_month_first_day).order_by(
                    #                 HisTable.dts_s.desc()).first()  # 查询当年最近一个月  累计总放电量最后一个值
                    #     except:
                    #         obj['chag_year_DC'] = ''  # 直流测年总充电
                    #         obj['disg_year_DC'] = ''  # 直流测年总放电
                    #         obj['chag_total_DC'] = ''  # 直流测累计总充电
                    #         obj['disg_total_DC'] = ''  # 直流测累计总放电
                    #         obj['SOH'] = ''  # SOH
                    #         break
                    #     if value0:
                    #         chag_total_DC += value0[0]  # 直流测累计总充电
                    #     if value1:
                    #         disg_total_DC += value1[0]  # 直流测累计总放电
                    #     # time_list = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
                    #     # for t in time_list:
                    #         # table_1 = 'r_measure' + year + t
                    #     HisTable_1 = HisACDMS('ods_r_measure1')
                    #     try:
                    #         # 当前年第一个月起始时间
                    #         value3 = db_con.query(HisTable_1.value, HisTable_1.dts_s).filter(HisTable_1.name == n0,
                    #                                                                             HisTable_1.value != 0, HisTable_1.dts_s.between(st_time, en_time)).order_by(
                    #             HisTable_1.dts_s.asc()).first()  # 查询当前年第一个月表 累计总充电量第一个值
                    #         value4 = db_con.query(HisTable_1.value, HisTable_1.dts_s).filter(HisTable_1.name == n1,
                    #                                                                             HisTable_1.value != 0, HisTable_1.dts_s.between(st_time, en_time)).order_by(
                    #             HisTable_1.dts_s.asc()).first()  # 查询当前年第一个月表 累计总放电量第一个值
                    #     except:
                    #         continue
                    #     # if value3:
                    #     #     break
                    #     if value0 and value3:
                    #         V3 = value0[0] - value3[0]  # 直流测年充电
                    #     else:
                    #         V3 = 0
                    #     if value1 and value4:
                    #         V4 = value1[0] - value4[0]  # 直流测年放电
                    #     else:
                    #         V4 = 0
                    #     chag_year_DC += V3  # 直流测年总充电
                    #     disg_year_DC += V4  # 直流测年总放电
                    #     value2 = db_con.query(HisTable.value).filter(HisTable.name == n2).all()  # 查询当前月表
                    #
                    #     for v in value2:
                    #         if v[0] != 0 and v[0] < 100:
                    #             num += 1
                    #             SOH += v[0]
                    # if num == 0:
                    #     SOH = 100
                    # elif SOH == 0:
                    #     SOH = 100
                    # else:
                    #     SOH = SOH / num

                    # 从doris库中获取平均SOH
                    conn = pool.connection()
                    cursor = conn.cursor()
                    # 直流测年总充电/直流测年总放电
                    chag_disg_year_DC_sql = (
                        "select sum(chag) as chag_energy, sum(disg) as disg_energy  from {} where station_name='{}' and date_type='year' and date_value='{}'".
                        format('dws_bc_measure_cw_cm_cy', db, str(year)))
                    cursor.execute(chag_disg_year_DC_sql)
                    sum_chag_disg_year_DC = cursor.fetchone()

                    # 直流测累计总充电/直流测累计总放电
                    chag_disg_total_DC_sql = (
                        "select sum(chag) as chag_energy, sum(disg) as disg_energy  from {} where station_name='{}' and date_type='year'".
                        format('dws_bc_measure_cw_cm_cy', db))
                    cursor.execute(chag_disg_total_DC_sql)
                    sum_chag_disg_total_DC = cursor.fetchone()

                    # SOH
                    sql_avg_soh = (
                        "select avg(soh) as avg_soh from {} where station_name='{}' and day>='{}' and day<='{}'".
                        format('dws_st_measure_win_15min', db, st_time, en_time))
                    cursor.execute(sql_avg_soh)
                    avg_soh = cursor.fetchone()
                    cursor.close()
                    conn.close()

                    obj['chag_year_DC'] = sum_chag_disg_year_DC['chag_energy'] if sum_chag_disg_year_DC['chag_energy'] else 0  # 直流测年总充电
                    obj['disg_year_DC'] = sum_chag_disg_year_DC['disg_energy'] if sum_chag_disg_year_DC['disg_energy'] else 0  # 直流测年总放电
                    obj['chag_total_DC'] = sum_chag_disg_total_DC['chag_energy'] if sum_chag_disg_total_DC['chag_energy'] else 0  # 直流测累计总充电
                    obj['disg_total_DC'] = sum_chag_disg_total_DC['disg_energy'] if sum_chag_disg_total_DC['disg_energy'] else 0  # 直流测累计总放电
                    obj['SOH'] = float('%.2f' % (avg_soh['avg_soh'] if avg_soh['avg_soh'] else 100))  # SOH
                session = self.getOrNewSession()
                informant = session.user['name']
                obj['user'] = informant  # 填报人
                obj['chag_year_meas'] = 0  # 计量测年总充电
                obj['disg_year_meas'] = 0  # 计量测年总放电
                obj['income_year'] = 0  # 年收益
                obj['income_rate'] = 0  # 收益完成率
                obj['income_total'] = 0  # 历史累计收益
                obj['chag_total_meas'] = 0  # 计量测累计充电
                obj['disg_total_meas'] = 0  # 计量测累计放电
                obj['overall_effic_total'] = 0  # 累计综合效率
                obj['overall_effic_year'] = 0  # 年综合效率
                obj['availab_index'] = '99.00'  # 可用率指标
                obj['year_cut_fill_total'] = 0  # 削峰填谷总额（万元）
                obj['year_annual_demand_side_response'] = 0  # 需求侧响应总额（万元）
                obj['peak_filling_ratio'] = 0  # 削峰填谷比例（%）
                obj['demand_side_response_ratio'] = 0  # 需求侧响应比例（%）
                obj['annual_other_total'] = 0  # 其他总额（万元）
                obj['other_proportion'] = 0  # 其他比例（%）
                tatol_y_q = 0  # 年总额（万元）
                tatol_t_q = 0  # 累计总额（万元）
                target_income = 0  # 累计目标收益
                obj['year_KWH_income'] = 0  # 年度电收益
                obj['life_cycle_kilowatt_cost'] = 0  # 全寿命周期度电成本
                filter = [RIncome.station_name == db, RIncome.is_use == 1]
                filter_3 = [RIncome.station_name == db, RIncome.is_use == 1, RIncome.in_ts == 2,
                            RIncome.day.like(year + '%')]

                ricome_1 = user_session.query(func.sum(RIncome.income), func.sum(RIncome.target_income),
                                              func.sum(RIncome.income_yu), func.sum(RIncome.disg),
                                              func.sum(RIncome.chag), RIncome.day).filter(
                    *filter).group_by(RIncome.day).order_by(RIncome.day.desc()).all()  # 查询收益数据
                if ricome_1:
                    for r in ricome_1:
                        if len(r[5]) < 5:
                            if r[2]:
                                tatol_t_q += r[2]  # 累计总额
                            if r[1]:
                                target_income += r[1]
                        if r[3]:
                            obj['disg_total_meas'] += r[3]  # 计量测累计总放电
                        if r[4]:
                            obj['chag_total_meas'] += r[4]  # 计量测累计充电
                        if r[5] == year:
                            obj['income_year'] = ('%.4f' % (r[2] / 10000))  # 年收益
                    obj['income_total'] = float('%.4f' % (tatol_t_q / 10000))
                    if target_income != 0:
                        obj['income_rate'] = ('%.2f' % ((obj['income_total'] / target_income) * 100))  # 收益完成率
                    if obj['disg_total_meas'] != 0:
                        obj['life_cycle_kilowatt_cost'] = (
                                    '%.2f' % ((obj['income_total'] * 10000) / obj['disg_total_meas']))  # 全寿命周期度电成本
                    if obj['chag_total_meas'] != 0:
                        obj['overall_effic_total'] = (
                                    '%.2f' % ((obj['disg_total_meas'] / obj['chag_total_meas']) * 100))  # 累计综合效率
                ricome_3 = user_session.query(func.sum(RIncome.income_yu), func.sum(RIncome.disg),
                                              func.sum(RIncome.chag), RIncome.income_ty).filter(*filter_3).group_by(
                    RIncome.income_ty).order_by(RIncome.day.desc()).all()  # 查询收益数据
                if ricome_3:
                    for r in ricome_3:
                        if r[0]:
                            tatol_y_q += r[0]  # 年总额（万元）
                        if r[1]:
                            obj['disg_year_meas'] += r[1]  # 计量测年总放电
                        if r[2]:
                            obj['chag_year_meas'] += r[2]  # 计量测年总充电
                    for r in ricome_3:
                        if r[3] == '削峰填谷收益' and r[0]:
                            obj['year_cut_fill_total'] = ('%.4f' % (r[0] / 10000))  # 削峰填谷总额（万元）
                            obj['peak_filling_ratio'] = (
                                        '%.2f' % (((r[0] / 10000) / (tatol_y_q / 10000)) * 100))  # 削峰填谷比例（%）
                        elif r[3] == '需求侧响应收益' and r[0]:
                            obj['year_annual_demand_side_response'] = ('%.4f' % (r[0] / 10000))  # 需求侧响应总额（万元）
                            obj['demand_side_response_ratio'] = (
                                        '%.2f' % (r[0] / (tatol_y_q / 10000)) * 100)  # 需求侧响应比例（%）
                    aaa = float('%.4f' % (tatol_y_q / 10000))
                    bbb = float('%.4f' % (float(obj['year_cut_fill_total'])))
                    ccc = float('%.4f' % (float(obj['year_annual_demand_side_response'])))
                    obj['annual_other_total'] = float('%.4f' % (aaa - bbb - ccc))  # 其他总额（万元）
                    obj['other_proportion'] = (
                                '%.2f' % ((float(obj['annual_other_total']) / (tatol_y_q / 10000)) * 100))  # 其他比例（%）
                    if obj['disg_year_meas'] != 0:
                        obj['year_KWH_income'] = ('%.2f' % (tatol_y_q / obj['disg_total_meas']))  # 年度电收益
                    if obj['chag_year_meas'] != 0:
                        obj['overall_effic_year'] = (
                                    '%.2f' % ((obj['disg_year_meas'] / obj['chag_year_meas']) * 100))  # 年综合效率
                toall_chagCapy = obj['disg_year_meas'] * obj['chag_year_meas']
                volume = user_session.query(Station.volume).filter(Station.name == db).first()  # 获取电站
                chag_disg_number = int(math.sqrt(toall_chagCapy) / (float(volume[0] * 1000)))  # 月充放电次数
                obj['chag_disg_number'] = chag_disg_number  # 充放电次数
                toall_chagdig = obj['disg_total_meas'] * obj['chag_total_meas']  # 交流侧累计充电量聚合值
                residue_degree = 5000 - int(math.sqrt(toall_chagdig) / (volume[0] * 1000))  # 剩余次数
                obj['residue_degree'] = residue_degree  # 剩余次数
                return self.returnTypeSuc(obj)
            elif kt == 'LoadCustomReportWeek':  # 加载自定义周报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                start_Time = self.get_argument('start_Time', None)  # 开始时间
                end_Time = self.get_argument('end_Time', None)  # 结束时间
                obj = {}
                data1, data2, data3 = [], [], []  # 放电,日效率,充电
                station = []
                name = ''
                if db == 'baodian':
                    name = 'bodian'
                else:
                    name = db
                if db == "ygzhen" or db == "zgtian":
                    for i in gateway[db]:
                        if name in i:
                            station.append(i)
                time_list = timeUtils.dateToDataList(start_Time, end_Time)
                for time in time_list:
                    freport = user_session.query(FReport).filter(FReport.day == time + ' 00:00:00',
                                                                 FReport.name.in_(station), FReport.cause == 1).all()
                    disg = 0  # 放电量
                    chag = 0  # 充电量
                    ratio = 0  # 效率
                    if freport:
                        for f in freport:
                            disg = ('%.3f' % (float(f.jf_disg) / 1000))
                            chag = ('%.3f' % (float(f.jf_chag) / 1000))
                            ratio = ('%.3f' % (float(f.ratio) * 100))
                            if float(ratio) > 100:
                                ratio = "100"

                        data1.append(disg)
                        data3.append(chag)
                        data2.append(ratio)
                if type == '2':
                    obj['day_disgCapy'] = data1  # 日放电
                    obj['day_efficiency'] = data2  # 日效率
                    obj['day_chagCapy'] = data3  # 日充电
                    obj['time'] = time_list  # 时间维度
                return self.returnTypeSuc(obj)
            elif kt == 'LoadCustomReporDay':  # 加载自定义日报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                s_Time = self.get_argument('s_Time', None)  # 日期
                da_base_chag = self.get_argument('da_base_chag', None)  # 日基准充电量
                da_base_disg = self.get_argument('da_base_disg', None)  # 日基准放电量
                da_base_incom = self.get_argument('da_base_incom', None)  # 日基准收益
                da_incom = self.get_argument('da_incom', None)  # 当日收益
                yu_base_incom = self.get_argument('yu_base_incom', None)  # 月基准收益
                yu_incom = self.get_argument('yu_incom', None)  # 月累计收益
                devi_analysis = self.get_argument('devi_analysis', None)  # 偏差分析
                obj = {}
                if da_base_chag and da_base_disg and da_base_incom and da_base_incom and da_incom and yu_base_incom and yu_incom:
                    try:
                        da_base_disg = int(da_base_disg)  # 日基准放电量
                    except:
                        da_base_disg = float(da_base_disg)  # 日基准放电量
                    try:
                        da_base_chag = int(da_base_chag)  # 日基准充电量
                    except:
                        da_base_chag = float(da_base_chag)  # 日基准充电量
                    try:
                        da_base_incom = int(da_base_incom)  # 日基准收益
                    except:
                        da_base_incom = float(da_base_incom)  # 日基准收益
                    try:
                        da_incom = int(da_incom)  # 当日收益
                    except:
                        da_incom = float(da_incom)  # 当日收益
                    try:
                        yu_base_incom = int(yu_base_incom)  # 月基准收益
                    except:
                        yu_base_incom = float(yu_base_incom)  # 月基准收益
                    try:
                        yu_incom = int(yu_incom)  # 月累计收益
                    except:
                        yu_incom = float(yu_incom)  # 月累计收益
                else:
                    if lang=='en':
                        return self.customError(
                            "Required fields are not filled in. Please return to the modified report!")
                    else:
                        return self.customError("必填项未填写。请返回修改报表！")
                obj['da_base_lv'] = ('%.2f' % ((da_base_disg / da_base_chag) * 100))  # 日基准充放电效率
                obj['income_day_rate'] = float('%.2f' % ((float(da_incom) / float(da_base_incom)) * 100))  # 日收益达成率
                obj['income_day_rate_devi'] = float('%.2f' % (100 - obj['income_day_rate']))  # 日收益达成率偏差
                obj['income_mon_rate'] = float('%.2f' % ((yu_incom / yu_base_incom) * 100))  # 月收益达成率
                obj['income_mon_rate_devi'] = float('%.2f' % (100 - obj['income_mon_rate']))  # 月收益达成率偏差
                obj['da_base_incom'] = da_base_incom  # 日基准收益
                obj['da_incom'] = da_incom  # 当日收益
                obj['yu_base_incom'] = yu_base_incom  # 月基准收益
                obj['yu_incom'] = yu_incom  # 月累计收益
                if lang=='en':
                    obj['devi_analysis'] = devi_analysis  # 偏差分析
                else:
                    obj['devi_analysis'] = devi_analysis  # 偏差分析
                data1, data2, data3 = [], [], []  # 放电,日效率,充电
                station = []
                name = ''
                if db == 'baodian':
                    name = 'bodian'
                else:
                    name = db
                if db == "ygzhen" or db == "zgtian":
                    for i in gateway[db]:
                        if name in i:
                            station.append(i)
                freport = user_session.query(FReport).filter(FReport.day == s_Time[:10] + ' 00:00:00',
                                                             FReport.name.in_(station), FReport.cause == 1).all()
                disg = 0  # 放电量
                chag = 0  # 充电量
                ratio = 0  # 效率
                if freport:
                    for f in freport:
                        disg = disg + float('%.2f' % (float(f.jf_disg)))
                        chag = chag + float('%.2f' % (float(f.jf_chag)))
                        ratio = ('%.3f' % (float(f.ratio) * 100))
                        if float(ratio) > 100:
                            ratio = "100"
                    data1.append(disg)
                    data3.append(chag)
                    data2.append(ratio)
                if type == '2':
                    obj['day_disgCapy'] = float('%.2f' % (sum(data1)))  # 日放电
                    obj['day_chagCapy'] = float('%.2f' % (sum(data3)))  # 日充电
                    if obj['day_chagCapy'] == 0:
                        obj['day_efficiency'] = 0
                    else:
                        obj['day_efficiency'] = (
                                    '%.2f' % ((obj['day_disgCapy'] / obj['day_chagCapy']) * 100))  # 当日充放电完成率

                    if da_base_chag == '0':
                        obj['capy_base_lv'] = 0
                    else:
                        capy_base_lv = float('%.2f' % ((obj['day_chagCapy'] / da_base_chag) * 100))
                        obj['capy_base_lv'] = capy_base_lv  # 充电量电量达成率
                    obj['capy_base_deviation'] = float('%.2f' % (100 - obj['capy_base_lv']))  # 充电量达成率偏差

                    if da_base_disg == 0:
                        obj['disg_base_lv'] = 0
                    else:
                        disg_base_lv = float('%.2f' % ((obj['day_disgCapy'] / da_base_disg) * 100))
                        obj['disg_base_lv'] = disg_base_lv  # 放电量达成率
                    obj['disg_base_deviation'] = float('%.2f' % (100 - obj['disg_base_lv']))  # 充电量达成率偏差

                    session = self.getOrNewSession()
                    informant = session.user['name']
                    obj['user'] = informant  # 填报人
                    obj['chag_total_meas'] = 0  # 计量测累计充电
                    obj['disg_total_meas'] = 0  # 计量测累计放电
                    obj['overall_effic_total'] = 0  # 累计综合效率
                    fd, cd = 0, 0
                    disg = []
                    chag = []
                    now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                    freport_2 = user_session.query(FReport).filter(FReport.cause == 3).all()  # 查询电量
                    freport_3 = user_session.query(FReport).filter(FReport.name.in_(station),
                                                                   FReport.day.between(s_Time[:10], now),
                                                                   FReport.cause == 1).all()
                    if freport_3:
                        for re in freport_3:
                            # 放电数据
                            jf_disg = eval(re.jf_disg)
                            fd_disg = eval(re.fd_disg)
                            pd_disg = eval(re.pd_disg)
                            gd_disg = eval(re.gd_disg)
                            # 充电
                            jf_chag = eval(re.jf_chag)
                            fd_chag = eval(re.fd_chag)
                            pd_chag = eval(re.pd_chag)
                            gd_chag = eval(re.gd_chag)
                            fd = fd + np.sum(jf_disg + fd_disg + pd_disg + gd_disg)  # 放电
                            cd = cd + np.sum(jf_chag + fd_chag + pd_chag + gd_chag)  # 充电
                        disg.append(fd)
                        chag.append(cd)
                    if freport_2:
                        for f in freport_2:
                            if db in f.name:
                                obj['chag_total_meas'] = float(f.jf_chag) - float(sum(chag))  # 累计充电
                                obj['disg_total_meas'] = float(f.jf_disg) - float(sum(disg))  # 累计放电
                                if obj['chag_total_meas'] != 0:
                                    obj['overall_effic_total'] = ('%.2f' % (
                                                (obj['disg_total_meas'] / obj['chag_total_meas']) * 100))  # 累计综合效率

                    info = []
                    # pages = user_session.query(Fault.fault_problem, Fault.happen_time, Fault.finish_time, Fault.cause,
                    #                            Fault.repair_way).all()
                    pages = user_session.query(Fault.fault_problem, Fault.happen_time, Fault.finish_time, Fault.cause,
                                               Fault.repair_way,Fault.en_fault_problem, Fault.en_cause,Fault.en_repair_way).filter(Fault.station == db, Fault.is_use == 1). \
                        filter(or_(
                        and_(Fault.happen_time <= (s_Time[:10] + ' 23:59:59'),
                             Fault.happen_time >= (s_Time[:10] + ' 00:00:00')),
                        and_(Fault.finish_time >= (s_Time[:10] + ' 00:00:00'),
                             Fault.finish_time <= (s_Time[:10] + ' 23:59:59')))).order_by(Fault.id.desc()).all()
                    if pages:
                        n = 1
                        for pag in pages:
                            o = {'id': '', 'fault_problem': '', 'happen_time': '', 'finish_time': '', 'cause': ''}
                            o['id'] = n
                            o['fault_problem'] = str(pag[0])
                            o['happen_time'] = str(pag[1])
                            o['finish_time'] = str(pag[2])
                            if lang=='en':
                                o['cause'] = str(pag[6]) + '/' + str(pag[7])
                                o['fault_problem'] = str(pag[5])
                            else:
                                o['cause'] = str(pag[3]) + '/' + str(pag[4])
                                o['fault_problem'] = str(pag[0])
                            info.append(o)
                            n += 1
                    # 需要实时翻译
                    obj['fault_info'] = info  # 故障信息
                return self.returnTypeSuc(obj)
            elif kt == 'GetCustomReportDay':
                s_time = self.get_argument('s_Time', None)  # 时间
                user = self.get_argument('user', None)  # 用户
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter, data = [ReportCustom.station == db, ReportCustom.flag == 4, ReportCustom.is_use == '1'], []
                if s_time:
                    s_time = s_time.split(',')
                    filter.append(and_(s_time[0][:4] <= ReportCustom.year, s_time[0][5:7] <= ReportCustom.month,
                                       s_time[0][8:10] <= ReportCustom.day, ReportCustom.year <= s_time[1][:4],
                                       ReportCustom.month <= s_time[1][5:7], ReportCustom.day <= s_time[1][8:10]))
                if user:
                    if lang=='en':
                        filter.append(ReportCustom.en_user.contains(user))
                    else:
                        filter.append(ReportCustom.user.contains(user))
                if DEBUG:
                    logging.info('user:%s,s_time:%s,pageNum:%s,pageSize:%s' % (user, s_time, pageNum, pageSize))
                total = user_session.query(func.count(ReportCustom.id)).filter(*filter).scalar()
                all = user_session.query(ReportCustom).filter(*filter).order_by(ReportCustom.id.desc()).limit(
                    pageSize).offset((pageNum - 1) * pageSize).all()
                for org in all:
                    o=eval(str(org.title))
                    if lang == 'en':
                        replaced_data = ReportCustom.replace_en_fields(o, "")
                        o.update(replaced_data)
                    obj = {"id": org.id, 'date': '', 'dict_1': {}}
                    title = eval(str(org.title))
                    obj['date'] = title
                    obj['date_2'] = eval(str(org.title))['year'] + '-' + eval(str(org.title))['month'] + '-' + \
                                    eval(str(org.title))['day']
                    obj['dict_1'] = eval(str(org.table_list))
                    data.append(obj)
                sorted_lst = sorted(data, key=lambda d: d['date_2'], reverse=True)
                return self.returnTotalSuccess(sorted_lst, total)
            elif kt == 'GetCustomReportDayDownload':  # 下载日报
                id = self.get_argument('id', None)  #
                descr = self.get_argument('descr', None)  # 电站描述
                if lang=='en':
                    workbook = load_workbook(filename='/home/<USER>/Download/CustomRepor/template.xlsx')
                else:
                    workbook = load_workbook(filename='/home/<USER>/Download/CustomRepor/模板.xlsx')
                o = user_session.query(ReportCustom.year, ReportCustom.month, ReportCustom.day, ReportCustom.table_list,
                                       ReportCustom.user, ReportCustom.en_table_list,ReportCustom.en_user).filter(ReportCustom.id == id,
                                                                 ReportCustom.is_use == '1').all()
                if not o:
                    if lang == 'en':
                        return self.customError('Invalid id')
                    else:
                        return self.customError('无效id')

                date_1 = o[0][0] + '-' + o[0][1] + '-' + o[0][2]
                # 获取需要进行修改的工作表或单元格
                worksheet = workbook['Sheet1']  # 获取名为 "Sheet1" 的工作表
                cell1 = worksheet['A1']  # 获取 A1 单元格
                # 对工作表或单元格的内容进行修改
                cell1.value = descr  # 修改 A1 单元格内容
                cell2 = worksheet['A2']
                if lang=='en':
                    cell2.value = "Date:" + date_1
                else:
                    cell2.value = "日期:" + date_1
                cell0 = worksheet['F2']
                if lang=='en':
                    cell0.value = "Informant:" + o[0][6]
                else:
                    cell0.value = "填报人:" + o[0][4]
                if lang == 'en':
                    dict_1 = json.loads(o[0][5])
                else:
                    dict_1 = json.loads(o[0][3])
                devi_analysis = dict_1['devi_analysis']  # 偏差分析
                fault_info = dict_1['fault_info']  # 故障
                day_chagCapy = dict_1['day_chagCapy']  # 日充电
                da_base_chag = dict_1['da_base_chag']  # 日基准充电量
                da_base_incom = dict_1['da_base_incom']  # 日基准收益
                yu_base_incom = dict_1['yu_base_incom']  # 月基准收益
                day_disgCapy = dict_1['day_disgCapy']  # 日放电
                da_base_disg = dict_1['da_base_disg']  # 日基准放电量
                disg_base_lv = dict_1['disg_base_lv']  # 放电量达成率
                capy_base_lv = dict_1['capy_base_lv']  # 充电量电量达成率
                yu_incom = dict_1['yu_incom']  # 月累计收益
                day_efficiency = dict_1['day_efficiency']  # 当日充放电完成率
                da_base_lv = dict_1['da_base_lv']  # 日基准充放电完成率
                overall_effic_total = dict_1['overall_effic_total']  # 累计综合效率
                da_incom = dict_1['da_incom']  # 当日收益
                yu_incom = dict_1['yu_incom']  # 月累计收益
                income_day_rate = dict_1['income_day_rate']  # 日收益达成率
                income_mon_rate = dict_1['income_mon_rate']  # 月收益达成率
                cell3 = worksheet['B3']
                cell3.value = day_chagCapy
                cell4 = worksheet['B4']
                cell4.value = da_base_chag
                cell5 = worksheet['B5']
                cell5.value = str(capy_base_lv) + '%'
                cell6 = worksheet['B6']
                cell6.value = da_incom
                cell7 = worksheet['B7']
                cell7.value = yu_base_incom
                cell33 = worksheet['D3']
                cell33.value = day_disgCapy
                cell44 = worksheet['D4']
                cell44.value = da_base_disg
                cell55 = worksheet['D5']
                cell55.value = str(disg_base_lv) + '%'
                cell66 = worksheet['D6']
                cell66.value = da_incom
                cell77 = worksheet['D7']
                cell77.value = yu_incom
                cell333 = worksheet['F3']
                cell333.value = str(day_efficiency) + '%'
                cell444 = worksheet['F4']
                cell444.value = str(da_base_lv) + '%'
                cell555 = worksheet['F5']
                cell555.value = str(overall_effic_total) + '%'
                cell666 = worksheet['F6']
                cell666.value = str(income_day_rate) + '%'
                cell777 = worksheet['F7']
                cell777.value = str(income_mon_rate) + '%'
                if devi_analysis:
                    devi_analysis = devi_analysis.split('&')
                    for de in devi_analysis:
                        indx = devi_analysis.index(de)
                        tin = str(indx + 9)
                        cell = worksheet['A' + tin]
                        cell.value = de
                if fault_info:
                    for f in fault_info:
                        indx = fault_info.index(f)
                        tin = str(indx + 21)
                        id = f['id']
                        fault_problem = f['fault_problem']
                        happen_time = f['happen_time']
                        finish_time = f['finish_time']
                        cause = f['cause']
                        cell_A = worksheet['A' + tin]
                        cell_A.value = id
                        cell_B = worksheet['B' + tin]
                        # 设置值
                        cell_B.value = fault_problem
                        cell_D = worksheet['D' + tin]
                        cell_D.value = happen_time
                        cell_E = worksheet['E' + tin]
                        cell_E.value = finish_time
                        cell_F = worksheet['F' + tin]
                        cell_F.value = cause
                descr = ''.join(descr.split('/'))

                if lang=='en':
                    workbook.save(filename='/home/<USER>/Download/CustomRepor/' + descr + date_1 + 'Daily operation report.xlsx')
                    url_ = '/home/<USER>/Download/CustomRepor/' + descr + date_1 + 'Daily operation report.xlsx'
                else:
                    workbook.save(filename='/home/<USER>/Download/CustomRepor/' + descr + date_1 + '运营日报.xlsx')
                    url_ = '/home/<USER>/Download/CustomRepor/' + descr + date_1 + '运营日报.xlsx'
                return self.returnTypeSuc(url_)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()
    def sys_availty(self, rrr, time_, volume):
        '''计算系统可用率，AGC'''
        if rrr:
            sum_1 = 0  # 累加值
            AGC = 0  # AGC累加值
            for l in rrr:
                if l.type_ == '1':
                    for i in eval(l.table_list):
                        star_time = i['star_time']  # 开始时间（年月日时分）
                        end_time = i['end_time']  # 结束时间（年月日时分）
                        outage_volume = i['outage_volume']  # 停运容量
                        time = timeUtils.timeSeconds((star_time + ':00'), (end_time + ':59'))
                        sum_1 = sum_1 + (float(outage_volume) * (time / 3600))
                elif l.type_ == '2':
                    for i in eval(l.table_list):
                        input_time = i['input_time']  # 投运时长
                        if input_time:
                            AGC = AGC + int(input_time)
            sum_ = (volume[0] * 1000) * time_
            sys_availty = 100 - (sum_1 / sum_) * 100  # 系统可用率
        else:
            sys_availty = 100
            AGC = 100
        sys_availty = str('%.2f' % sys_availty)
        return sys_availty, AGC
    def method_name(self, HisTable, b, d, data_0, data_1, data_2, data_3, data_5, db_con, ed, n0, n1, n2, n3, st):
        value_0 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n0,
                                                                      HisTable.dts_s.between(st, ed),
                                                                      HisTable.value < 65535).order_by(
            HisTable.dts_s.asc()).all()
        value_1 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n1,
                                                                      HisTable.dts_s.between(st, ed),
                                                                      HisTable.value < 65535).order_by(
            HisTable.dts_s.asc()).all()
        value_2 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n2,
                                                                      HisTable.dts_s.between(st, ed),
                                                                      HisTable.value < 65535).order_by(
            HisTable.dts_s.asc()).all()
        value_3 = db_con.query(HisTable.value, HisTable.dts_s).filter(HisTable.name == n3,
                                                                      HisTable.dts_s.between(st, ed),
                                                                      HisTable.value < 101).order_by(
            HisTable.dts_s.asc()).all()
        for val in value_0:
            if val[0] >= 0:
                data_0['time'].append(timeUtils.ssTtimes(val[1]))
                data_0['value'].append(val[0])
            else:
                data_5['time'].append(timeUtils.ssTtimes(val[1]))
                data_5['value'].append(val[0])
        b.append({d: data_0})
        b.append({d: data_5})
        for val in value_1:
            data_1['time'].append(timeUtils.ssTtimes(val[1]))
            data_1['value'].append(val[0])
        b.append({d: data_1})
        for val in value_2:
            data_2['time'].append(timeUtils.ssTtimes(val[1]))
            data_2['value'].append(val[0])
        b.append({d: data_2})
        for val in value_3:
            data_3['time'].append(timeUtils.ssTtimes(val[1]))
            data_3['value'].append(val[0])
        b.append({d: data_3})
    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        db_con = self.get_argument('db', 'his')
        try:
        # if 1:
            if kt == 'AddCustomReportWeek':  # 保存自定义周报
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                week = self.get_argument('week', '')  # 周
                list = self.get_argument('list', [])  # 充电电量
                user = self.get_argument('user', '')  # 用户
                day = self.get_argument('day', '')  # 运行天数
                list1 = self.get_argument('list1', [])  # 可用率
                list3 = self.get_argument('list3', [])  # AGC
                title = {}
                title["year"] = year
                title["month"] = month
                title['week'] = week
                title['day'] = day
                en_title = {}
                en_title["year"] = year
                en_title["month"] = month
                en_title['week'] = week
                en_title['day'] = day
                if lang=='en':
                    translate_instance = Translate_cls(ty=1)
                    zh_user = translate_instance.str_chinese(user)
                    title['user'] = zh_user
                    en_title['user'] = user
                else:
                    translate_instance = Translate_cls(ty=2)
                    en_user = translate_instance.str_chinese(user)
                    title['user'] = user
                    en_title['user'] = en_user
                title = json.dumps(title, ensure_ascii=False)
                en_title = json.dumps(en_title, ensure_ascii=False)
                if DEBUG:
                    logging.info('title:%s,list3:%s' % (title, list3))
                a = user_session.query(ReportCustom).filter(ReportCustom.year == year, ReportCustom.month == month,
                                                            ReportCustom.week == week, ReportCustom.station == db_con,
                                                            ReportCustom.flag == 1, ReportCustom.is_use == '1').first()
                if a:
                    if lang=='en':
                        return self.customError("Weekly reports already exist and cannot be added. Please return to the modified report!")
                    else:
                        return self.customError("周报已存在,无法新增。请返回修改报表！")
                sum_2 = 0  # 累加值
                volume = user_session.query(Station.volume).filter(Station.name == db_con).first()  # 获取电站容量
                time_ = 7 * 24  # 周时间
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        descr = i['descr']  # 描述
                        db = i['db']  # 电站名称
                        date = i['date']  # 日期
                        list_2 = json.dumps(i['list_2'], ensure_ascii=False)
                        if lang=='en':
                            translate_instance = Translate_cls(ty=1)
                            zh_descr = translate_instance.str_chinese(descr)
                            r = Availability(en_user=user, user=zh_user, day=day, week=week, en_descr=descr, descr=zh_descr, station=db, date=date, type_=1,table_list=list_2)
                        else:
                            translate_instance = Translate_cls(ty=2)
                            en_descr = translate_instance.str_chinese(descr)
                            r = Availability(user=user, en_user=en_user, day=day, week=week, descr=descr, en_descr=en_descr, station=db, date=date, type_=1,table_list=list_2)
                        user_session.add(r)
                        user_session.commit()
                        # 发布翻译数据
                        pdr_data = {'id': r.id,
                                    'table': 't_availability',
                                    'update_data': {'table_list': list_2}}
                        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                        r_real.publish(pub_name, json.dumps(pdr_data))
                        list_2 = json.loads(list_2)
                        for l in list_2:
                            star_time = l['star_time']  # 开始时间（年月日时分）
                            end_time = l['end_time']  # 结束时间（年月日时分）
                            outage_volume = l['outage_volume']  # 停运容量
                            time = timeUtils.timeSeconds((star_time + ':00'), (end_time + ':00'))
                            sum_2 = sum_2 + (float(outage_volume) * (time / 3600))
                    sum_ = (volume[0] * 1000) * time_
                    sys_availty = 100 - (sum_2 / sum_) * 100  # 周系统可用率
                else:
                    sys_availty = 100
                sys_availty = str('%.2f' % sys_availty) + '%'
                AGC = 0  # AGC累加值
                if list3:
                    list3 = json.loads(list3)
                    for i in list3:
                        descr = i['descr']  # 描述
                        db = i['db']  # 电站名称
                        date = i['date_3']  # 日期
                        list_3 = json.dumps(i['list_3'], ensure_ascii=False)  #
                        if lang=='en':
                            translate_instance = Translate_cls(ty=1)
                            zh_descr = translate_instance.str_chinese(descr)
                            r = Availability(en_user=user, user=zh_user, day=day, week=week, en_descr=descr, descr=zh_descr, station=db, date=date, type_=2,table_list=list_3)
                        else:
                            translate_instance = Translate_cls(ty=2)
                            en_descr = translate_instance.str_chinese(descr)
                            r = Availability(user=user, en_user=en_user, day=day, week=week, descr=descr,  en_descr=en_descr, station=db, date=date, type_=2,table_list=list_3)
                        user_session.add(r)
                        user_session.commit()
                        # 发布翻译数据
                        pdr_data = {'id': r.id,
                                    'table': 't_availability',
                                    'update_data': {'table_list': list_3}}
                        pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                        r_real.publish(pub_name, json.dumps(pdr_data))
                        list_3 = json.loads(list_3)
                        for l in list_3:
                            input_time = l['input_time']  # 投运时长
                            if input_time:
                                AGC = AGC + int(input_time)
                else:
                    AGC = 0
                if lang == 'en':
                    rp = ReportCustom(title=title, op_ts=timeUtils.getNewTimeStr(), flag=1,
                                      station=db_con,sys_availty=sys_availty, agc=AGC, year=year, month=month, week=week, user=zh_user,
                                      day=day, is_use='1',en_user=user,en_title=en_title,en_table_list=list)
                else:
                    rp = ReportCustom(title=title, table_list=list, op_ts=timeUtils.getNewTimeStr(), flag=1, station=db_con,
                                      sys_availty=sys_availty,agc=AGC, year=year, month=month, week=week, user=user, day=day, is_use='1',en_user=en_user,en_title=en_title)
                user_session.add(rp)
                user_session.commit()
                # 发布翻译数据
                pdr_data = {'id': rp.id,
                            'table': 't_report_custom',
                            'update_data': {'table_list': list}}
                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                r_real.publish(pub_name, json.dumps(pdr_data))
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateCustomReportWeek':  # 修改自定义周报
                id = self.get_argument('id', None)
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                week = self.get_argument('week', '')  # 周
                list = self.get_argument('list', [])  # 充电电量
                user = self.get_argument('user', '')  # 用户
                day = self.get_argument('day', '')  # 运行天数
                list1 = self.get_argument('list1', [])  # 可用率
                list3 = self.get_argument('list3', [])  # AGC
                title = {}
                en_title = {}
                title["year"] = year
                title["month"] = month
                title['week'] = week
                title['day'] = day
                en_title["year"] = year
                en_title["month"] = month
                en_title['week'] = week
                en_title['day'] = day
                if lang=='en':
                    translate_instance = Translate_cls(ty=1)
                    zh_user = translate_instance.str_chinese(user)
                    title['user'] = zh_user
                    en_title['user'] = user
                else:
                    translate_instance = Translate_cls(ty=2)
                    en_user = translate_instance.str_chinese(user)
                    title['user'] = user
                    en_title['user'] = en_user
                title = json.dumps(title, ensure_ascii=False)
                en_title = json.dumps(en_title, ensure_ascii=False)
                if DEBUG:
                    logging.info('id:%s,title:%s,list:%s' % (id, title, list))
                o = user_session.query(ReportCustom).get(id)
                if not o:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError('无效id')
                sum_3 = 0  # 累加值
                volume = user_session.query(Station.volume).filter(Station.name == db_con).first()  # 获取电站容量
                time_ = 7 * 24  # 周时间
                if list1:
                    list1 = json.loads(list1)
                    for i in list1:
                        descr = i['descr']  # 描述
                        db = i['db']  # 电站名称
                        date = i['date']  # 日期
                        list_2 = json.dumps(i['list_2'], ensure_ascii=False)
                        rr = user_session.query(Availability).filter(Availability.date == date,
                                                                     Availability.station == db,
                                                                     Availability.type_ == 1,).first()
                        if rr:
                            rr.day = day
                            rr.week = week
                            rr.station = db
                            if lang=='en':
                                translate_instance = Translate_cls(ty=1)
                                zh_descr = translate_instance.str_chinese(descr)
                                rr.descr = zh_descr
                                rr.user = zh_user
                                rr.en_descr = descr
                                rr.en_user = user
                                rr.table_list = list_2
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': rr.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_2}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))
                            else:
                                translate_instance = Translate_cls(ty=2)
                                en_descr = translate_instance.str_chinese(descr)
                                rr.descr = descr
                                rr.user = user
                                rr.en_descr = en_descr
                                rr.en_user = en_user
                                rr.table_list = list_2
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': rr.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_2}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))
                        else:
                            if lang == 'en':
                                translate_instance = Translate_cls(ty=1)
                                zh_descr = translate_instance.str_chinese(descr)
                                r = Availability(user=zh_user, en_user=user, day=day, week=week, descr=zh_descr,
                                                 en_descr=descr, station=db, date=date, table_list=list_2, type_=1)
                                user_session.add(r)
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': r.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_2}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))
                            else:
                                translate_instance = Translate_cls(ty=2)
                                en_descr = translate_instance.str_chinese(descr)
                                r = Availability(user=user, en_user=en_user, day=day, week=week, descr=descr,
                                                 en_descr=en_descr, station=db, date=date, table_list=list_2, type_=1)
                                user_session.add(r)
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': r.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_2}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))
                        list_2 = json.loads(list_2)
                        for l in list_2:
                            star_time = l['star_time']  # 开始时间（年月日时分）
                            end_time = l['end_time']  # 结束时间（年月日时分）
                            outage_volume = l['outage_volume']  # 停运容量
                            time = timeUtils.timeSeconds((star_time + ':00'), (end_time + ':00'))
                            sum_3 = sum_3 + (float(outage_volume) * (time / 3600))
                    sum_ = (volume[0] * 1000) * time_
                    sys_availty = 100 - (sum_3 / sum_) * 100  # 周系统可用率
                else:
                    sys_availty = 100
                AGC = 0  # AGC累加值
                if list3:
                    list3 = json.loads(list3)
                    for i in list3:
                        descr = i['descr']  # 描述
                        db = i['db']  # 电站名称
                        date = i['date_3']  # 日期
                        list_3 = json.dumps(i['list_3'], ensure_ascii=False)  #
                        rr = user_session.query(Availability).filter(Availability.date == date,
                                                                     Availability.station == db,
                                                                     Availability.type_ == 2).first()  #
                        if rr:
                            rr.day = day
                            rr.week = week
                            rr.station = db
                            if lang == 'en':
                                translate_instance = Translate_cls(ty=1)
                                zh_descr = translate_instance.str_chinese(descr)
                                rr.descr = zh_descr
                                rr.user = zh_user
                                rr.en_descr = descr
                                rr.en_user = user
                                list_33 = json.dumps(i['list_3'], ensure_ascii=False)
                                rr.table_list = list_33
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': rr.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_33}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))

                            else:
                                translate_instance = Translate_cls(ty=2)
                                en_descr = translate_instance.str_chinese(descr)
                                rr.descr = descr
                                rr.user = user
                                rr.en_descr = en_descr
                                rr.en_user = en_user
                                list_33 = json.dumps(i['list_3'], ensure_ascii=False)
                                rr.table_list = list_33
                                user_session.commit()
                                # 发布翻译数据
                                pdr_data = {'id': rr.id,
                                            'table': 't_availability',
                                            'update_data': {'table_list': list_33}}
                                pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                                r_real.publish(pub_name, json.dumps(pdr_data))
                        else:
                            if lang == 'en':
                                translate_instance = Translate_cls(ty=1)
                                zh_descr = translate_instance.str_chinese(descr)
                                r = Availability(user=zh_user, en_user=user, day=day, week=week, descr=zh_descr,
                                                 en_descr=descr, station=db, date=date, table_list=list_3, type_=2)
                                user_session.add(r)
                                user_session.commit()
                            else:
                                translate_instance = Translate_cls(ty=2)
                                en_descr = translate_instance.str_chinese(descr)
                                r = Availability(user=user, en_user=en_user, day=day, week=week, descr=descr,
                                                 en_descr=en_descr, station=db, date=date, table_list=list_3, type_=2)
                                user_session.add(r)
                                user_session.add(r)
                                user_session.commit()
                        list_3 = json.loads(list_3)
                        for l in list_3:
                            input_time = l['input_time']  # 投运时长
                            if input_time:
                                AGC = AGC + int(input_time)
                sys_availty = str('%.2f' % sys_availty) + '%'
                o.station = db_con
                o.sys_availty = sys_availty
                o.year = year
                o.month = month
                o.week = week
                o.day = day
                o.agc = AGC
                if lang=='en':
                    o.title = title
                    o.en_title = en_title
                    o.en_table_list = list
                    o.user = zh_user
                    o.en_user = user
                    user_session.commit()
                    # 发布翻译数据
                    pdr_data = {'id': o.id,
                                'table': 't_report_custom',
                                'update_data': {'table_list': list}}
                    pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                    r_real.publish(pub_name, json.dumps(pdr_data))
                else:
                    o.title = title
                    o.en_title = en_title
                    o.table_list = list
                    o.user = user
                    o.en_user = en_user
                    user_session.commit()
                    # 发布翻译数据
                    pdr_data = {'id': o.id,
                                'table': 't_report_custom',
                                'update_data': {'table_list': list}}
                    pub_name = 'en_translate_pub' if lang == 'zh' else 'zh_translate_pub'
                    r_real.publish(pub_name, json.dumps(pdr_data))
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AddCustomReportMonth':  # 保存自定义月报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                user = self.get_argument('user', '')  # 填报人
                income_mon = self.get_argument('income_mon', '')  # 月收益
                income_year = self.get_argument('income_year', '')  # 本年累计收益
                income_rate = self.get_argument('income_rate', '')  # 本年收益完成率
                chag_mon_meas = self.get_argument('chag_mon_meas', '')  # 计量测月总充电
                disg_mon_meas = self.get_argument('disg_mon_meas', '')  # 计量测月总放电
                chag_total_meas = self.get_argument('chag_total_meas', '')  # 计量测累计总充电
                disg_total_meas = self.get_argument('disg_total_meas', '')  # 计量测累计总放电
                chag_mon_DC = self.get_argument('chag_mon_DC', '')  # 直流测月总充电
                disg_mon_DC = self.get_argument('disg_mon_DC', '')  # 直流测月总放电
                chag_total_DC = self.get_argument('chag_total_DC', '')  # 直流测累计总充电
                disg_total_DC = self.get_argument('disg_total_DC', '')  # 直流测累计总放电
                overall_effic_mon = self.get_argument('overall_effic_mon', '')  # 月综合效率
                overall_effic_total = self.get_argument('overall_effic_total', '')  # 累计综合效率
                chag_disg_number = self.get_argument('chag_disg_number', '')  # 月充放电次数
                residue_degree = self.get_argument('residue_degree', '')  # 剩余次数
                total_auxiliary_electric = self.get_argument('total_auxiliary_electric', '')  # 累计辅助用电量
                cumulative_sys_availab = self.get_argument('cumulative_sys_availab', '')  # 累计系统可用率
                availab_index = self.get_argument('availab_index', '')  # 可用率指标
                SOH = self.get_argument('SOH', '')  # SOH
                day = self.get_argument('day', '')  # 运行天数
                day_disgCapy = self.get_argument('day_disgCapy', [])  # pcs日放电
                day_efficiency = self.get_argument('day_efficiency', [])  # pcs日效率
                day_chagCapy = self.get_argument('day_chagCapy', [])  # pcs日充电
                time = self.get_argument('time', [])  # pcs时间维度
                fault_desc = self.get_argument('fault_desc', None)  # 故障说明
                unit_k_value = self.get_argument('unit_k_value', '')  # 机组K值
                unit_aver_quotation = self.get_argument('unit_aver_quotation', '')  # 机组平均报价
                day_operation = self.get_argument('day_operation', '')  # 机组投运天数
                mon_auxiliary_electric = self.get_argument('mon_auxiliary_electric', '')  # 月辅助用电量
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                rrr = user_session.query(Availability).filter(Availability.date.contains(year),
                                                              Availability.date.contains(month),
                                                              Availability.station == db_con).all()  #
                AGC = 0  # AGC累加值
                if rrr:
                    for l in rrr:
                        if l.type_ == '2':
                            pass
                else:
                    sys_availty = 100
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db_con,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.month == month,
                                                                                ReportCustom.flag == 1).first()  # 获取周报表里的AGC值
                if AGC_zhi:
                    AGC = AGC_zhi[0]
                title = {}
                title["year"] = year
                title["month"] = month
                data = {}
                title = json.dumps(title, ensure_ascii=False)
                fault_desc = ';'.join(fault_desc.split('\n')) if fault_desc else ''
                if DEBUG:
                    logging.info('title:%s,month:%s,user:%s' % (title, month, user))
                aaa = user_session.query(ReportCustom).filter(ReportCustom.year == year, ReportCustom.month == month,
                                                              ReportCustom.station == db_con, ReportCustom.flag == 2,
                                                              ReportCustom.type == type,
                                                              ReportCustom.is_use == '1').all()
                if aaa:
                    if lang=='en':
                        return self.customError("The monthly report already exists and cannot be added. Please return to the modified report!")
                    else:
                        return self.customError("月报已存在,无法新增。请返回修改报表！")
                files = self.request.files
                file_path = '/home/<USER>/' + db_con
                # file_path = '/home/<USER>/document/' + db_con
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                if files:
                    imgs = files.get('file')
                    data1 = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    path = '%s/%s' % (file_path, uploadfilename)
                    file = open(path, 'wb')
                    file.write(data1)
                    file.close()
                    data['path'] = path  # 图片路径
                data['income_mon'] = income_mon
                data['income_year'] = income_year
                data['income_rate'] = income_rate
                data['chag_mon_meas'] = chag_mon_meas
                data['disg_mon_meas'] = disg_mon_meas
                data['chag_total_meas'] = chag_total_meas
                data['disg_total_meas'] = disg_total_meas
                data['chag_mon_DC'] = chag_mon_DC
                data['disg_mon_DC'] = disg_mon_DC
                data['chag_total_DC'] = chag_total_DC
                data['disg_total_DC'] = disg_total_DC
                data['overall_effic_mon'] = overall_effic_mon
                data['overall_effic_total'] = overall_effic_total
                data['chag_disg_number'] = chag_disg_number
                data['residue_degree'] = residue_degree
                data['total_auxiliary_electric'] = total_auxiliary_electric
                data['cumulative_sys_availab'] = cumulative_sys_availab
                data['availab_index'] = availab_index
                data['SOH'] = SOH
                data['day'] = day
                data['user'] = user
                data['day_disgCapy'] = day_disgCapy
                data['day_efficiency'] = day_efficiency
                data['day_chagCapy'] = day_chagCapy
                data['time'] = time
                data['type'] = type
                data['fault_desc'] = fault_desc
                data['unit_k_value'] = unit_k_value
                data['unit_aver_quotation'] = unit_aver_quotation
                data['day_operation'] = day_operation
                data['mon_auxiliary_electric'] = mon_auxiliary_electric
                data['sys_availty'] = sys_availty
                if lang=='en':
                    data_2 = copy.copy(data)
                    for k, v in data_2.items():
                        data_2[k] = translate_text(v,1)
                        if data_2[k]==None:
                            data_2[k]=''
                        if k=='user':
                            zh_user=data_2[k]
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    self._addReportDatas(db=db_con, titel=title, flag=2, data=data_2,data_2=data, type=type, AGC=AGC, year=year, month=month, user=zh_user,en_user=user, day=day)
                else:
                    data_2 = copy.copy(data)
                    for k, v in data_2.items():
                        data_2[k] = translate_text(v,2)
                        if data_2[k]==None:
                            data_2[k]=''
                        if k=='user':
                            en_user=data_2[k]
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    self._addReportDatas(db=db_con, titel=title, flag=2, data=data,data_2=data_2, type=type, AGC=AGC, year=year, month=month, user=user,en_user=en_user, day=day)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateCustomReportMonth':  # 修改自定义月报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                id = self.get_argument('id', None)
                year = self.get_argument('year', '')  # 年份
                month = self.get_argument('month', '')  # 月份
                user = self.get_argument('user', '')  # 填报人
                income_mon = self.get_argument('income_mon', '')  # 月收益
                income_year = self.get_argument('income_year', '')  # 本年累计收益
                income_rate = self.get_argument('income_rate', '')  # 本年收益完成率
                chag_mon_meas = self.get_argument('chag_mon_meas', '')  # 计量测月总充电
                disg_mon_meas = self.get_argument('disg_mon_meas', '')  # 计量测月总放电
                chag_total_meas = self.get_argument('chag_total_meas', '')  # 计量测累计总充电
                disg_total_meas = self.get_argument('disg_total_meas', '')  # 计量测累计总放电
                chag_mon_DC = self.get_argument('chag_mon_DC', '')  # 直流测月总充电
                disg_mon_DC = self.get_argument('disg_mon_DC', '')  # 直流测月总放电
                chag_total_DC = self.get_argument('chag_total_DC', '')  # 直流测累计总充电
                disg_total_DC = self.get_argument('disg_total_DC', '')  # 直流测累计总放电
                overall_effic_mon = self.get_argument('overall_effic_mon', '')  # 月综合效率
                overall_effic_total = self.get_argument('overall_effic_total', '')  # 累计综合效率
                chag_disg_number = self.get_argument('chag_disg_number', '')  # 月充放电次数
                residue_degree = self.get_argument('residue_degree', '')  # 剩余次数
                total_auxiliary_electric = self.get_argument('total_auxiliary_electric', '')  # 累计辅助用电量
                cumulative_sys_availab = self.get_argument('cumulative_sys_availab', '')  # 累计系统可用率
                availab_index = self.get_argument('availab_index', '')  # 可用率指标
                SOH = self.get_argument('SOH', '')  # SOH
                day = self.get_argument('day', '')  # 运行天数
                day_disgCapy = self.get_argument('day_disgCapy', [])  # pcs日放电
                day_efficiency = self.get_argument('day_efficiency', [])  # pcs日效率
                day_chagCapy = self.get_argument('day_chagCapy', [])  # pcs日充电
                time = self.get_argument('time', [])  # pcs时间维度
                fault_desc = self.get_argument('fault_desc', None)  # 故障说明
                unit_k_value = self.get_argument('unit_k_value', '')  # 机组K值
                unit_aver_quotation = self.get_argument('unit_aver_quotation', '')  # 机组平均报价
                day_operation = self.get_argument('day_operation', '')  # 机组投运天数
                mon_auxiliary_electric = self.get_argument('mon_auxiliary_electric', '')  # 月辅助用电量
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                rrr = user_session.query(Availability).filter(Availability.date.contains(year),
                                                              Availability.date.contains(month),
                                                              Availability.station == db_con).all()  #
                AGC = 0  # AGC累加值
                if rrr:
                    for l in rrr:
                        if l.type_ == '2':
                            pass
                else:
                    sys_availty = 100
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db_con,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.month == month,
                                                                                ReportCustom.flag == 1).first()  # 获取周报表里的AGC值
                if AGC_zhi:
                    AGC = AGC_zhi[0]
                title = {}
                title["year"] = year
                title["month"] = month
                data = {}
                title = json.dumps(title, ensure_ascii=False)
                if DEBUG:
                    logging.info('title:%s' % (title))

                fault_desc = ';'.join(fault_desc.split('\n')) if fault_desc else ''
                o = user_session.query(ReportCustom).filter(ReportCustom.id == id, ReportCustom.is_use == '1').first()
                if not o:
                    if lang=='en':
                        return self.customError('Invalid id')
                    else:
                        return self.customError('无效id')
                files = self.request.files
                file_path = '/home/<USER>/' + db_con
                # file_path = '/home/<USER>/document/' + db_con
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                if files:
                    imgs = files.get('file')
                    data1 = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    path = '%s/%s' % (file_path, uploadfilename)
                    file = open(path, 'wb')
                    file.write(data1)
                    file.close()
                    data['path'] = path  # 图片路径
                data['income_mon'] = income_mon
                data['income_year'] = income_year
                data['income_rate'] = income_rate
                data['chag_mon_meas'] = chag_mon_meas
                data['disg_mon_meas'] = disg_mon_meas
                data['chag_total_meas'] = chag_total_meas
                data['disg_total_meas'] = disg_total_meas
                data['chag_mon_DC'] = chag_mon_DC
                data['disg_mon_DC'] = disg_mon_DC
                data['chag_total_DC'] = chag_total_DC
                data['disg_total_DC'] = disg_total_DC
                data['overall_effic_mon'] = overall_effic_mon
                data['overall_effic_total'] = overall_effic_total
                data['chag_disg_number'] = chag_disg_number
                data['residue_degree'] = residue_degree
                data['total_auxiliary_electric'] = total_auxiliary_electric
                data['cumulative_sys_availab'] = cumulative_sys_availab
                data['availab_index'] = availab_index
                data['SOH'] = SOH
                data['day'] = day
                data['user'] = user
                data['day_disgCapy'] = day_disgCapy
                data['day_efficiency'] = day_efficiency
                data['day_chagCapy'] = day_chagCapy
                data['time'] = time
                data['type'] = type
                data['fault_desc'] = fault_desc
                data['unit_k_value'] = unit_k_value
                data['unit_aver_quotation'] = unit_aver_quotation
                data['day_operation'] = day_operation
                data['mon_auxiliary_electric'] = mon_auxiliary_electric
                data['sys_availty'] = sys_availty
                if lang=='en':
                    data_2 = copy.copy(data)
                    translate_instance = Translate_cls(ty=1)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    o.table_list = data_2
                    o.en_table_list = data
                    o.user = translate_text(user,1)
                    o.en_user = user
                else:
                    data_2 = copy.copy(data)
                    translate_instance = Translate_cls(ty=2)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    o.table_list = data
                    o.en_table_list = data_2
                    o.user = user
                    o.en_user = translate_text(user, 2)
                o.title = title
                o.station = db_con
                o.agc = AGC
                o.day = day
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'CustomReportMonthDelete':  # 删除自定义月报
                id = self.get_argument('id', None)  # 月份id
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(ReportCustom).filter(ReportCustom.id == id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = '0'
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AddCustomReportYear':  # 保存自定义年报
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', '')  # 年份
                user = self.get_argument('user', '')  # 填报人
                income_year = self.get_argument('income_year', '')  # 年收益
                income_total = self.get_argument('income_total', '')  # 累计收益
                income_rate = self.get_argument('income_rate', '')  # 收益完成率
                chag_year_meas = self.get_argument('chag_year_meas', '')  # 计量测年总充电
                disg_year_meas = self.get_argument('disg_year_meas', '')  # 计量测年总放电
                chag_total_meas = self.get_argument('chag_total_meas', '')  # 计量测累计总充电
                disg_total_meas = self.get_argument('disg_total_meas', '')  # 计量测累计总放电
                chag_year_DC = self.get_argument('chag_year_DC', '')  # 直流测年总充电
                disg_year_DC = self.get_argument('disg_year_DC', '')  # 直流测年总放电
                chag_total_DC = self.get_argument('chag_total_DC', '')  # 直流测累计总充电
                disg_total_DC = self.get_argument('disg_total_DC', '')  # 直流测累计总放电
                overall_effic_year = self.get_argument('overall_effic_year', '')  # 年综合效率
                overall_effic_total = self.get_argument('overall_effic_total', '')  # 累计综合效率
                chag_disg_number = self.get_argument('chag_disg_number', '')  # 年充放电次数
                residue_degree = self.get_argument('residue_degree', '')  # 剩余次数
                year_auxiliary_electric = self.get_argument('year_auxiliary_electric', '')  # 年辅助用电量
                year_cumulative_sys_availab = self.get_argument('year_cumulative_sys_availab', '')  # 年系统可用率
                availab_index = self.get_argument('availab_index', '')  # 可用率指标
                SOH = self.get_argument('SOH', '')  # SOH
                day = self.get_argument('day', '')  # 运行天数
                year_decay_rate = self.get_argument('year_decay_rate', '')  # 年衰减率
                decay_rate_index = self.get_argument('decay_rate_index', '')  # 衰减率指标
                year_KWH_income = self.get_argument('year_KWH_income', '')  # 年度电收益
                life_cycle_kilowatt_cost = self.get_argument('life_cycle_kilowatt_cost', '')  # 全寿命周期度电成本
                KWH_net_income = self.get_argument('KWH_net_income', '')  # 度电净收益
                mon_disgCapy = self.get_argument('mon_disgCapy', [])  # pcs月放电
                mon_efficiency = self.get_argument('mon_efficiency', [])  # pcs月效率
                mon_chagCapy = self.get_argument('mon_chagCapy', [])  # pcs月充电
                time = self.get_argument('time', [])  # pcs时间维度
                fault_desc = self.get_argument('fault_desc', None)  # 故障说明
                peak_filling_ratio = self.get_argument('peak_filling_ratio', '')  # 削峰填谷比例
                demand_side_response_ratio = self.get_argument('demand_side_response_ratio', '')  # 需求侧响应比例
                other_proportion = self.get_argument('other_proportion', '')  # 其他比例
                year_cut_fill_total = self.get_argument('year_cut_fill_total', '')  # 年削峰填谷总额
                year_annual_demand_side_response = self.get_argument('year_annual_demand_side_response', '')  # 年需求侧响应总额
                annual_other_total = self.get_argument('annual_other_total', '')  # 年其他总额
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                rrr = user_session.query(Availability).filter(Availability.date.contains(year),
                                                              Availability.station == db_con).all()  #
                AGC = 0  # AGC累加值
                if rrr:

                    for l in rrr:
                        if l.type_ == '2':
                            pass
                else:
                    sys_availty = 100
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db_con,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.flag == 1).first()  # 获取周报表里的AGC值
                if AGC_zhi:
                    AGC = AGC_zhi[0]
                title = {}
                title["year"] = year
                data = {}
                title = json.dumps(title, ensure_ascii=False)
                fault_desc = ';'.join(fault_desc.split('\n')) if fault_desc else ''
                if DEBUG:
                    logging.info('year:%s,user:%s,mon_disgCapy:%s' % (year, user, mon_disgCapy))
                a = user_session.query(ReportCustom).filter(ReportCustom.year == year, ReportCustom.station == db_con,
                                                            ReportCustom.flag == 3, ReportCustom.type == type,
                                                            ReportCustom.is_use == '1').first()
                if a:
                    if lang=='en':
                        return self.customError("The annual report already exists and cannot be added. Please return to the modified report!")
                    else:
                        return self.customError("年报已存在,无法新增。请返回修改报表！")
                files = self.request.files
                file_path = '/home/<USER>/' + db_con
                # file_path = '/home/<USER>/document/' + db_con
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                if files:
                    imgs = files.get('file')
                    data1 = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    path = '%s/%s' % (file_path, uploadfilename)
                    file = open(path, 'wb')
                    file.write(data1)
                    file.close()
                    data['path'] = path  # 图片路径
                data['income_year'] = income_year
                data['income_total'] = income_total
                data['income_rate'] = income_rate
                data['chag_year_meas'] = chag_year_meas
                data['disg_year_meas'] = disg_year_meas
                data['chag_total_meas'] = chag_total_meas
                data['disg_total_meas'] = disg_total_meas
                data['chag_year_DC'] = chag_year_DC
                data['disg_year_DC'] = disg_year_DC
                data['chag_total_DC'] = chag_total_DC
                data['disg_total_DC'] = disg_total_DC
                data['overall_effic_year'] = overall_effic_year
                data['overall_effic_total'] = overall_effic_total
                data['chag_disg_number'] = chag_disg_number
                data['residue_degree'] = residue_degree
                data['year_auxiliary_electric'] = year_auxiliary_electric
                data['year_cumulative_sys_availab'] = year_cumulative_sys_availab
                data['availab_index'] = availab_index
                data['SOH'] = SOH
                data['day'] = day
                data['user'] = user
                data['year_decay_rate'] = year_decay_rate
                data['decay_rate_index'] = decay_rate_index
                data['year_KWH_income'] = year_KWH_income
                data['life_cycle_kilowatt_cost'] = life_cycle_kilowatt_cost
                data['KWH_net_income'] = KWH_net_income
                data['mon_disgCapy'] = mon_disgCapy
                data['mon_efficiency'] = mon_efficiency
                data['mon_chagCapy'] = mon_chagCapy
                data['time'] = time
                data['fault_desc'] = fault_desc
                data['peak_filling_ratio'] = peak_filling_ratio
                data['demand_side_response_ratio'] = demand_side_response_ratio
                data['other_proportion'] = other_proportion
                data['year_cut_fill_total'] = year_cut_fill_total
                data['year_annual_demand_side_response'] = year_annual_demand_side_response
                data['annual_other_total'] = annual_other_total
                data['sys_availty'] = sys_availty
                if lang =='en':
                    en_user = user
                    zh_user = translate_text(user, 1)
                    translate_instance = Translate_cls(ty=1)
                    data_2 = copy.copy(data)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    self._addReportDatas(db=db_con, titel=title, flag=3, data=data_2, data_2=data, type=type, AGC=AGC,
                                         year=year, user=zh_user, en_user=en_user, day=day)
                else:
                    zh_user = user
                    en_user = translate_text(user, 2)
                    translate_instance = Translate_cls(ty=2)
                    data_2 = copy.copy(data)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    self._addReportDatas(db=db_con, titel=title, flag=3, data=data, data_2=data_2, type=type, AGC=AGC,
                                         year=year, user=zh_user, en_user=en_user, day=day)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateCustomReportYear':  # 修改自定义年报
                id = self.get_argument('id', None)
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                year = self.get_argument('year', '')  # 年份
                user = self.get_argument('user', '')  # 填报人
                income_year = self.get_argument('income_year', '')  # 年收益
                income_total = self.get_argument('income_total', '')  # 累计收益
                income_rate = self.get_argument('income_rate', '')  # 收益完成率
                chag_year_meas = self.get_argument('chag_year_meas', '')  # 计量测年总充电
                disg_year_meas = self.get_argument('disg_year_meas', '')  # 计量测年总放电
                chag_total_meas = self.get_argument('chag_total_meas', '')  # 计量测累计总充电
                disg_total_meas = self.get_argument('disg_total_meas', '')  # 计量测累计总放电
                chag_year_DC = self.get_argument('chag_year_DC', '')  # 直流测年总充电
                disg_year_DC = self.get_argument('disg_year_DC', '')  # 直流测年总放电
                chag_total_DC = self.get_argument('chag_total_DC', '')  # 直流测累计总充电
                disg_total_DC = self.get_argument('disg_total_DC', '')  # 直流测累计总放电
                overall_effic_year = self.get_argument('overall_effic_year', '')  # 年综合效率
                overall_effic_total = self.get_argument('overall_effic_total', '')  # 累计综合效率
                chag_disg_number = self.get_argument('chag_disg_number', '')  # 年充放电次数
                residue_degree = self.get_argument('residue_degree', '')  # 剩余次数
                year_auxiliary_electric = self.get_argument('year_auxiliary_electric', '')  # 年辅助用电量
                year_cumulative_sys_availab = self.get_argument('year_cumulative_sys_availab', '')  # 年系统可用率
                availab_index = self.get_argument('availab_index', '')  # 可用率指标
                SOH = self.get_argument('SOH', '')  # SOH
                day = self.get_argument('day', '')  # 运行天数
                year_decay_rate = self.get_argument('year_decay_rate', '')  # 年衰减率
                decay_rate_index = self.get_argument('decay_rate_index', '')  # 衰减率指标
                year_KWH_income = self.get_argument('year_KWH_income', '')  # 年度电收益
                life_cycle_kilowatt_cost = self.get_argument('life_cycle_kilowatt_cost', '')  # 全寿命周期度电成本
                KWH_net_income = self.get_argument('KWH_net_income', '')  # 度电净收益
                mon_disgCapy = self.get_argument('mon_disgCapy', [])  # pcs月放电
                mon_efficiency = self.get_argument('mon_efficiency', [])  # pcs月效率
                mon_chagCapy = self.get_argument('mon_chagCapy', [])  # pcs月充电
                time = self.get_argument('time', [])  # pcs时间维度
                fault_desc = self.get_argument('fault_desc', None)  # 故障说明
                peak_filling_ratio = self.get_argument('peak_filling_ratio', '')  # 削峰填谷比例
                demand_side_response_ratio = self.get_argument('demand_side_response_ratio', '')  # 需求侧响应比例
                other_proportion = self.get_argument('other_proportion', '')  # 其他比例
                year_cut_fill_total = self.get_argument('year_cut_fill_total', '')  # 年削峰填谷总额
                year_annual_demand_side_response = self.get_argument('year_annual_demand_side_response', '')  # 年需求侧响应总额
                annual_other_total = self.get_argument('annual_other_total', '')  # 年其他总额
                sys_availty = self.get_argument('sys_availty', '')  # 系统可用率
                rrr = user_session.query(Availability).filter(Availability.date.contains(year),
                                                              Availability.station == db_con).all()  #
                AGC = 0  # AGC累加值
                if rrr:
                    for l in rrr:
                        if l.type_ == '2':
                            pass
                else:
                    sys_availty = 100
                AGC_zhi = user_session.query(func.sum(ReportCustom.agc)).filter(ReportCustom.station == db_con,
                                                                                ReportCustom.is_use == 1,
                                                                                ReportCustom.year == year,
                                                                                ReportCustom.flag == 3).first()  #
                if AGC_zhi:
                    AGC = AGC_zhi[0]
                title = {}
                title["year"] = year
                data = {}
                title = json.dumps(title, ensure_ascii=False)
                if DEBUG:
                    logging.info('title:%s' % (title))
                fault_desc = ';'.join(fault_desc.split('\n')) if fault_desc else ''
                o = user_session.query(ReportCustom).filter(ReportCustom.id == id, ReportCustom.is_use == '1').first()
                if not o:
                    if lang=='en':
                        return self.customError('Invalid id')
                    else:
                        return self.customError('无效id')
                files = self.request.files
                file_path = '/home/<USER>/' + db_con
                # file_path = '/home/<USER>/document/' + db_con
                if not os.path.exists(file_path):  # 判断文件夹是否存在，不存在就创建
                    os.makedirs(file_path)
                if files:
                    imgs = files.get('file')
                    data1 = imgs[0].get('body')
                    filename = imgs[0].get('filename')
                    doc_format = str(os.path.splitext(filename)[1])  # 格式
                    uploadfilename = str(uuid.uuid1()) + doc_format
                    path = '%s/%s' % (file_path, uploadfilename)
                    file = open(path, 'wb')
                    file.write(data1)
                    file.close()
                    data['path'] = path  # 图片路径
                data['income_year'] = income_year
                data['income_total'] = income_total
                data['income_rate'] = income_rate
                data['chag_year_meas'] = chag_year_meas
                data['disg_year_meas'] = disg_year_meas
                data['chag_total_meas'] = chag_total_meas
                data['disg_total_meas'] = disg_total_meas
                data['chag_year_DC'] = chag_year_DC
                data['disg_year_DC'] = disg_year_DC
                data['chag_total_DC'] = chag_total_DC
                data['disg_total_DC'] = disg_total_DC
                data['overall_effic_year'] = overall_effic_year
                data['overall_effic_total'] = overall_effic_total
                data['chag_disg_number'] = chag_disg_number
                data['residue_degree'] = residue_degree
                data['year_auxiliary_electric'] = year_auxiliary_electric
                data['year_cumulative_sys_availab'] = year_cumulative_sys_availab
                data['availab_index'] = availab_index
                data['SOH'] = SOH
                data['day'] = day
                data['user'] = user
                data['year_decay_rate'] = year_decay_rate
                data['decay_rate_index'] = decay_rate_index
                data['year_KWH_income'] = year_KWH_income
                data['life_cycle_kilowatt_cost'] = life_cycle_kilowatt_cost
                data['KWH_net_income'] = KWH_net_income
                data['mon_disgCapy'] = mon_disgCapy
                data['mon_efficiency'] = mon_efficiency
                data['mon_chagCapy'] = mon_chagCapy
                data['time'] = time
                data['fault_desc'] = fault_desc
                data['peak_filling_ratio'] = peak_filling_ratio
                data['demand_side_response_ratio'] = demand_side_response_ratio
                data['other_proportion'] = other_proportion
                data['year_cut_fill_total'] = year_cut_fill_total
                data['year_annual_demand_side_response'] = year_annual_demand_side_response
                data['annual_other_total'] = annual_other_total
                data['type'] = type
                data['sys_availty'] = sys_availty
                if lang == 'en':
                    translate_instance = Translate_cls(ty=1)
                    data_2 = copy.copy(data)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    o.table_list = data_2
                    o.en_table_list = data
                    o.user = translate_text(user, 1)
                    o.en_user = user
                else:
                    translate_instance = Translate_cls(ty=2)
                    data_2 = copy.copy(data)
                    data_2 = translate_instance.dict_chinese(data_2)
                    data = json.dumps(data, ensure_ascii=False)
                    data_2 = json.dumps(data_2, ensure_ascii=False)
                    o.table_list = data
                    o.en_table_list = data_2
                    o.user = user
                    o.en_user = translate_text(user, 2)
                o.title = title
                o.station = db_con
                o.agc = AGC
                o.year = year
                o.day = day
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'CustomReportYearDelete':  # 删除自定义年报
                id = self.get_argument('id', None)  # 年id
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(ReportCustom).filter(ReportCustom.id == id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = '0'
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AddCustomReportDay':  # 保存自定义日报
                s_Time = self.get_argument('s_Time', None)  # 日期
                user = self.get_argument('user', None)  #
                dict_1 = self.get_argument('dict_1', {})  # 组合
                title = {}
                title["year"] = s_Time[:4]
                title["month"] = s_Time[5:7]
                title['day'] = s_Time[8:10]
                en_title = {}
                en_title["year"] = s_Time[:4]
                en_title["month"] = s_Time[5:7]
                en_title['day'] = s_Time[8:10]
                if lang=='en':
                    en_title['user'] = user
                    zh_user=translate_text(user, 1)
                    title['user'] = zh_user
                    title = json.dumps(title, ensure_ascii=False)
                    en_title = json.dumps(en_title, ensure_ascii=False)
                    if DEBUG:
                        logging.info('title:%s,list3:%s' % (title, dict_1))
                    a = user_session.query(ReportCustom).filter(ReportCustom.year == s_Time[:4],
                                                                ReportCustom.month == s_Time[5:7],
                                                                ReportCustom.day == s_Time[8:10],
                                                                ReportCustom.station == db_con, ReportCustom.flag == 4,
                                                                ReportCustom.is_use == '1').first()
                    if a:
                        return self.customError("The daily newspaper already exists and cannot be added. Please return to the modified report!")
                    translate_instance = Translate_cls(ty=1)
                    dict_1 = json.loads(dict_1)
                    dict_2 = translate_instance.dict_chinese(dict_1)
                    dict_1 = json.dumps(dict_1, ensure_ascii=False)
                    dict_2 = json.dumps(dict_2, ensure_ascii=False)
                    rp = ReportCustom(title=title, table_list=dict_2, op_ts=timeUtils.getNewTimeStr(), flag=4,
                                      station=db_con, year=s_Time[:4], month=s_Time[5:7], user=zh_user, day=s_Time[8:10],
                                      is_use='1',en_title=en_title, en_table_list=dict_1, en_user=user)
                else:
                    title['user'] = user
                    en_user = translate_text(user, 2)
                    en_title['user'] = en_user
                    title = json.dumps(title, ensure_ascii=False)
                    en_title = json.dumps(en_title, ensure_ascii=False)
                    if DEBUG:
                        logging.info('title:%s,list3:%s' % (title, dict_1))
                    a = user_session.query(ReportCustom).filter(ReportCustom.year == s_Time[:4],
                                                                ReportCustom.month == s_Time[5:7],
                                                                ReportCustom.day == s_Time[8:10],
                                                                ReportCustom.station == db_con, ReportCustom.flag == 4,
                                                                ReportCustom.is_use == '1').first()
                    if a:
                        return self.customError("日报已存在,无法新增。请返回修改报表！")
                    translate_instance = Translate_cls(ty=2)
                    dict_1 = json.loads(dict_1)
                    dict_2 = translate_instance.dict_chinese(dict_1)
                    dict_1 = json.dumps(dict_1, ensure_ascii=False)
                    dict_2 = json.dumps(dict_2, ensure_ascii=False)
                    rp = ReportCustom(title=title, table_list=dict_1, op_ts=timeUtils.getNewTimeStr(), flag=4,
                                      station=db_con, year=s_Time[:4], month=s_Time[5:7], user=user, day=s_Time[8:10],
                                      is_use='1',en_title=en_title, en_table_list=dict_2, en_user=en_user)
                user_session.add(rp)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateCustomReportDay':  # 修改自定义日报
                id = self.get_argument('id', None)  #
                type = self.get_argument('type', '')  # 储能类型1火储2工商3集中式
                s_Time = self.get_argument('s_Time', None)  # 日期
                user = self.get_argument('user', None)  #
                dict_1 = self.get_argument('dict_1', {})  # 组合
                title = {}
                title["year"] = s_Time[:4]
                title["month"] = s_Time[5:7]
                title['day'] = s_Time[8:10]
                en_title = {}
                en_title["year"] = s_Time[:4]
                en_title["month"] = s_Time[5:7]
                en_title['day'] = s_Time[8:10]
                if lang == 'en':
                    en_title['user'] = user
                    zh_user = translate_text(user, 1)
                    title['user'] = zh_user
                else:
                    title['user'] = user
                    en_user = translate_text(user, 2)
                    en_title['user'] = en_user
                title = json.dumps(title, ensure_ascii=False)
                en_title = json.dumps(en_title, ensure_ascii=False)
                if DEBUG:
                    logging.info('title:%s,list3:%s' % (title, dict_1))
                o = user_session.query(ReportCustom).filter(ReportCustom.id == id, ReportCustom.is_use == '1').first()
                if not o:
                    if lang=='en':
                        return self.customError('Invalid id')
                    else:
                        return self.customError('无效id')
                a = user_session.query(ReportCustom).filter(ReportCustom.id != id, ReportCustom.year == s_Time[:4],
                                                            ReportCustom.month == s_Time[5:7],
                                                            ReportCustom.day == s_Time[8:10],
                                                            ReportCustom.station == db_con, ReportCustom.flag == 4,
                                                            ReportCustom.is_use == '1').first()
                if a:
                    if lang=='en':
                        return self.customError("The daily newspaper already exists and cannot be added. Please return to the modified report!")
                    else:
                        return self.customError("日报已存在,无法新增。请返回修改报表！")
                if lang == 'en':
                    translate_instance = Translate_cls(ty=1)
                    dict_1 = json.loads(dict_1)
                    dict_2 = translate_instance.dict_chinese(dict_1)
                    dict_1 = json.dumps(dict_1, ensure_ascii=False)
                    dict_2 = json.dumps(dict_2, ensure_ascii=False)
                    o.table_list = dict_1
                    o.en_table_list = dict_2
                    o.user = zh_user
                    o.en_user = user
                else:
                    translate_instance = Translate_cls(ty=2)
                    dict_1 = json.loads(dict_1)
                    dict_2 = translate_instance.dict_chinese(dict_1)
                    dict_1 = json.dumps(dict_1, ensure_ascii=False)
                    dict_2 = json.dumps(dict_2, ensure_ascii=False)
                    o.table_list = dict_1
                    o.en_table_list = dict_2
                    o.user = user
                    o.en_user = en_user
                o.title = title
                o.en_title = en_title
                o.station = db_con
                o.year = s_Time[:4]
                o.month = s_Time[5:7]
                o.day = s_Time[8:10]
                o.type = type
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'CustomReportDayDelete':  # 删除自定义日报
                id = self.get_argument('id', None)  # id
                if DEBUG:
                    logging.info('id:%s' % (id))
                page = user_session.query(ReportCustom).filter(ReportCustom.id == id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                page.is_use = '0'
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()
    def _addReportDatas(self, db=None, titel=None, flag=None, data=None,data_2=None, type=None, AGC=None, year=None, month=None, user=None,en_user=None, day=None):
        # 保存输入的数据
        rp = ReportCustom(title=titel, table_list=data, op_ts=timeUtils.getNewTimeStr(), flag=flag, station=db,
                          type=type, agc=AGC, year=year, month=month, user=user, day=day, is_use='1',en_table_list=data_2,en_user=en_user,en_title=titel)
        user_session.add(rp)
        user_session.commit()

def _return_db_con(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_siss[d]
    else:
        d = int(d[1:2])
        if db == 'binhai':  # 滨海电站
            if d < 4:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'taicang':
            return bmsdb[db][0]
        elif db == 'ygzhen' or db == 'zgtian':
            if d < 3:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]

def is_valid_json(json_str):
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        return False

