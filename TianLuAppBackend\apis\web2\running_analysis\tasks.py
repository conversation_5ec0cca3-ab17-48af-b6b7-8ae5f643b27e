# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/7/22 下午5:25
# <AUTHOR> Li<PERSON><PERSON>
# @Project  : TianLuAppBackend
# @File     : tasks.py
# @Software : PyCharm
import os
import sys
from pathlib import Path

# 获取项目根目录，根据实际情况调整层级
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "TianLuAppBackend.settings")

# 然后再导入 Django 并进行其他操作
import django
django.setup()

import datetime
import json
import traceback

from django.db.models import Q
from django_redis import get_redis_connection

from TianLuAppBackend.settings import SUCCESS_LOG
from apis.user.models import Unit, StationActicNew, FormerActicNew, MaterStation, RunningAnalysis
from apis.web2.running_analysis.tools import format_duration, get_time_period, en_format_duration
from common.database_pools import DWDDatabaseTool, dwd_db_tool, dwd_tables, ads_db_tool
from common.constant import EMPTY_STR_LIST

logger = SUCCESS_LOG


class RunningAnalysisTool:
    """
    运行分析
    """""

    def __init__(self, master_station):
        self.master_station = master_station
        self.redis_conn = get_redis_connection('3')
        self.is_online = True        # 站点是否在线
        self.discharge_status = 0       # 充放电状态 -1：离线；1：充电；0：静置；2：放电; -2:通讯异常
        self.is_overcapacity = False   # 是否超容
        self.re_chag_status = False   # 是否反送电
        self.unit_count = Unit.objects.filter(is_delete=0, station__in=self.master_station.stationdetails_set.filter(is_delete=0).all()).count()
        # 储能单元数量
        self.pccth = '--'   # 防逆流阈值
        self.cap_tfm = '--'  # 变压器容量
        self.v_tprt = '--'  # 变压器安全系数
        self.p_th = '--'  # 变压器安全容量
        self.p_ess = 0   # 储能系统实际功率
        self.p_target = 0   # 储能系统目标功率
        self.p_load = 0      # 厂区负荷功率
        self.p_load_meter = '--'  # 电表采集到的电表功率
        self.p_o = 0         # 并网点额定功率
        self.ratio = '--'       # 变压器容量占比
        self.soc = '--'          # 并网点SOC
        self.p_target_diff_abs = 0      # 系统目标功率变化量绝对值
        self.p_target_percent = 0       # 前一个时刻储能系统目标功率
        self.limit_v = 1                # 充放电限值

    def online_analysis(self):
        """
        离线分析: 只有ems无数据才是整个并网点离线
        :return:
        """""
        key = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', self.master_station.english_name, 'EMS')
        status_ems = self.redis_conn.get(key)

        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='离线', status=0,
                                                               is_delete=0).last()

        if status_ems:
            # 查询是否已经存在该站点的未恢复离线分析数据，如已存在，则修改状态为已恢复
            print(f"{self.master_station.name}当前在线状态：（恢复）在线")

            if exist_analysis:
                t = datetime.datetime.now() - exist_analysis.start_time
                exist_analysis.status = 1
                exist_analysis.end_time = datetime.datetime.now()
                exist_analysis.keyword = f"离线时长：{format_duration(t.total_seconds())}"
                exist_analysis.en_keyword = f"Offline Duration：{en_format_duration(t.total_seconds())}"
                exist_analysis.save()

        else:
            print(f"{self.master_station.name}当前在线状态：离线")
            self.is_online = False
            # 查询是否已经存在该站点的未恢复离线分析数据，如不存在，则新增一条离线分析数据
            if not exist_analysis:
                new_analysis = RunningAnalysis.objects.create(station=self.master_station, topic='离线', status=0,
                                                              en_topic='Offline',
                                                              start_time=datetime.datetime.now(),
                                                              keyword=f"离线时长：0分钟",
                                                              en_keyword=f"Offline Duration：0 minutes",
                                                              ref_threshold='--', is_delete=0)
            # 如已存在，则更新离线时长
            else:
                t = datetime.datetime.now() - exist_analysis.start_time
                exist_analysis.keyword = f"离线时长：{format_duration(t.total_seconds())}"
                exist_analysis.en_keyword = f"Offline Duration：{en_format_duration(t.total_seconds())}"
                exist_analysis.save()

    def get_limits_v(self):
        """
        获取并网点的当前策略配置的充放限值
        :return:
        """""
        now = datetime.datetime.now()
        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)

        current_hour = get_time_period(datetime.datetime.now()).split(':')[0] if get_time_period(datetime.datetime.now()).split(':')[0] != '0' else '24'
        ems_station = self.master_station.stationdetails_set.filter(is_delete=0,
            english_name=self.master_station.english_name).first()
        # EMS
        table_name = dwd_tables['measure']['ems_tscale']
        limit_v = '--'
        chag_tag = '--'

        key1 = f"rlh{int(current_hour)}p"
        key3 = f"rlh{int(current_hour)}f"

        select_sql = (
            f"SELECT {key1},{key3} FROM {table_name} WHERE station_name=%s and device=%s and time between %s and %s ORDER BY"
            f" time DESC limit 1")

        try:
            key2 = f"M{now.month}H{int(current_hour)}P"
            key4 = f"M{now.month}H{int(current_hour)}F"
            redis_conn = get_redis_connection('3')
            key = f"{ems_station.english_name}-{now.month}-mqtt"
            data = redis_conn.get(key)
            if data:
                dict_data = eval(data)
                if dict_data.get('body')[0].get('body').get(key2) is not None:
                    limit_v = float(dict_data.get('body')[0].get('body').get(key2))
                if dict_data.get('body')[0].get('body').get(key4) is not None:
                    chag_tag = float(dict_data.get('body')[0].get('body').get(key4))

            else:
                result = dwd_db_tool.select_one(select_sql, *(ems_station.english_name, 'EMS', start_time, now))
                if result:
                    limit_v = float(result[key1]) if result[key1] is not None else '--'
                    chag_tag = float(result[key3]) if result[key3] is not None else '--'

            return limit_v, chag_tag
        except Exception as e:
            print(e, traceback.print_exc())
            return '--', '--'

    def get_average_p_ess(self, start_time, end_time):
        """
        计算储能系统实际功率平均值
        """""
        m_station = self.master_station.stationdetails_set.filter(is_delete=0,
            english_name=self.master_station.english_name).first()
        s_stations = self.master_station.stationdetails_set.filter(is_delete=0).exclude(Q(slave=0) | Q(pack=0)).all()
        s_stations_en_names = [s.english_name for s in s_stations]
        # sql = """
        #         SELECT time,sum(p) as p FROM `dwd_measure_pcs_data_storage` where station_name in %s and time between %s and %s GROUP BY time
        # """
        sql = """ SELECT avg(p) as p FROM `dwd_measure_pcs_data_storage` where station_name in %s and time between %s and %s """
        dwd_tool = DWDDatabaseTool()
        try:
            results = dwd_tool.select_many(sql, s_stations_en_names, start_time, end_time)
            if results:
                p_ess_avg = round(results['p'] , 2)
                return p_ess_avg
            else:
                return '--'
        except Exception as e:
            print(e, traceback.print_exc())
            return '--'

    def pre_default_analysis(self):
        """
        前置检查：主要获取一些分析数据方便后续分析
        :return:
        """""
        now = datetime.datetime.now()
        if self.is_online:
            m_station = self.master_station.stationdetails_set.filter(is_delete=0,
                english_name=self.master_station.english_name).first()
            s_stations = self.master_station.stationdetails_set.filter(is_delete=0).exclude(Q(slave=0) | Q(pack=0)).all()

            try:
                # 查询变压器容量及安全系数，计算变压器安全容量
                ems_measure = self.redis_conn.get(
                    'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', m_station.english_name,
                                                            'EMS'))
                if ems_measure:
                    measure_ems_dict = json.loads(json.loads(ems_measure.decode("utf-8")))
                else:
                    measure_ems_dict = {}

                # 变压器容量
                self.cap_tfm = float(measure_ems_dict.get("TFM", 0)) if measure_ems_dict.get("TFM") not in EMPTY_STR_LIST else '--'
                # 变压器安全系数
                self.v_tprt = float(measure_ems_dict.get("TPRT")) if measure_ems_dict.get("TPRT") not in EMPTY_STR_LIST else '--'
                # 变压器安全容量
                self.p_th = self.cap_tfm * self.v_tprt if self.cap_tfm != '--' and self.v_tprt != '--' else '--'
                # 电表负荷数据
                self.p_load_meter = float(measure_ems_dict.get("PCC")) if measure_ems_dict.get("PCC") not in EMPTY_STR_LIST else '--'
                # 防逆流阈值
                self.pccth = float(measure_ems_dict.get("Pccth")) if measure_ems_dict.get("Pccth") not in EMPTY_STR_LIST else '--'
            except Exception as e:
                print(e)
                print(traceback.print_exc())

            try:
                # 电表后置时的储能系统功率，计算电网侧功率时需要减去
                p_less = 0
                t = datetime.datetime.now()
                # t = datetime.datetime.strptime('2024-09-09 02:13:00', '%Y-%m-%d %H:%M:%S')
                period = get_time_period(t)
                if len(period) == 4:
                    period = '0' + period
                pre_period = get_time_period(t - datetime.timedelta(minutes=5))
                if len(pre_period) == 4:
                    pre_period = '0' + pre_period

                for s_station in s_stations:
                    self.p_o += float(s_station.rated_power)

                    meter_position = s_station.meter_position
                    meter_position = 1 if meter_position is None else meter_position

                    # 查询储能系统目标功率, 如无则取额定容量
                    former_actic_ids = StationActicNew.objects.filter(station_id=s_station.id).all()
                    former_actic_ids = [i.former_actic_id for i in former_actic_ids]
                    year_month = datetime.datetime.now().strftime("%Y-%m")
                    formers = FormerActicNew.objects.filter(id__in=former_actic_ids, year_month=year_month).all()
                    if formers.exists():
                        now_former = formers.filter(moment=period).first()
                        pre_former = formers.filter(moment=pre_period).first()
                        if now_former:
                            self.p_target += now_former.power_value
                        if pre_former:
                            self.p_target_percent += pre_former.power_value
                    else:
                        self.p_target += int(float(s_station.rated_power))
                        self.p_target_percent += int(float(s_station.rated_power))

                    for unit in s_station.unit_set.filter(is_delete=0).all():
                        pcs_measure = self.redis_conn.get(
                            'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', s_station.english_name,
                                                                    unit.pcs))
                        if pcs_measure:
                            measure_pcs_dict = json.loads(json.loads(pcs_measure.decode("utf-8")))
                        else:
                            measure_pcs_dict = {}

                        p = float(measure_pcs_dict.get('P')) if measure_pcs_dict.get('P') not in EMPTY_STR_LIST else '--'
                        if p != '--':
                            self.p_ess += p
                            # 电表后置
                            if meter_position == 1:
                                p_less += p

                self.p_ess = round(self.p_ess, 2)

                # 系统目标功率变化量绝对值
                self.p_target_diff_abs = abs(self.p_target_percent - self.p_target)

                # 电网测功率，电表前置时：p_grid = p_load_meter；后置时：p_grid = p_load_meter - p_less
                self.p_load = round((self.p_load_meter + p_less), 2) if self.p_load_meter != '--' else '--'
                

                # 变压器容量占比
                self.ratio = round(self.p_load / self.p_th * 100,
                              1) if self.p_load and self.p_load != '--' and self.p_th != 0 and self.p_th != '--' else 0

                v, tag = self.get_limits_v()
                if v != '--':
                    self.limit_v = v

                if tag != '--':
                    if tag > 0:
                        self.discharge_status = 2
                    elif tag < 0:
                        self.discharge_status = 1
                    else:
                        self.discharge_status = 0
                else:
                    print(f'265---{self.master_station.name} chag_tag is --')

            except Exception as e:
                print(e)
                print(traceback.print_exc())

            soc_list = []
            try:
                for unit in Unit.objects.filter(is_delete=0, station__in=self.master_station.stationdetails_set.filter(is_delete=0).all()):
                    # 判断单元充放电状态
                    pcs_measure = self.redis_conn.get(
                        'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.station.english_name,
                                                                unit.pcs))

                    bms_measure = self.redis_conn.get(
                        'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', unit.station.english_name,
                                                                unit.bms))
                    if bms_measure:
                        measure_bms_dict = json.loads(json.loads(bms_measure.decode("utf-8")))
                        soc_ = float(measure_bms_dict.get('SOC')) if measure_bms_dict.get('SOC') not in EMPTY_STR_LIST else '--'
                        if soc_ != '--':
                            soc_list.append(soc_)

                    # if not pcs_measure:
                    #     self.discharge_status = -1
                    #     break
                    # if pcs_measure:
                    #     pcs = eval(eval(pcs_measure))
                    #     p = float(pcs.get('P')) if pcs.get('P') is not None else '--'
                    #     if p != '--':
                    #         if p > 1:
                    #             self.discharge_status = 2
                    #         elif p < -1:
                    #             self.discharge_status = 1
                    #         else:
                    #             self.discharge_status = 0 if self.discharge_status == 0 else self.discharge_status
                        # else:
                        #     self.discharge_status = -1
                        #     break

                        # if not bms_measure:
                        #     self.discharge_status = -2

                self.soc = round((sum(soc_list) / len(soc_list)), 1) if soc_list else '--'

            except Exception as e:
                print(e)
                print(traceback.print_exc())

            # print('*'*50)
            # print(f"{self.master_station.name}{now.strftime('%Y-%m-%d %H:%M:%S')}前置检查结果：\n是否在线：{self.is_online}，\n充放电状态：{self.discharge_status}，\n"
            #       f"储能单元数量：{self.unit_count}, \n防逆流阈值: {self.pccth}, \n变压器容量: {self.cap_tfm}, \n变压器安全系数: {self.v_tprt},\n"
            #       f"变压器安全容量: {self.p_th}, \n储能系统实际功率: {self.p_ess}, \n储能系统目标功率: {self.p_target},\n"
            #       f"厂区负荷功率: {self.p_load},\n电表采集到的电表功率: {self.p_load_meter}, \n并网点额定功率: {self.p_o},\n并网点SOC: {self.soc},\n"
            #       f"系统目标功率变化量绝对值: {self.p_target_diff_abs},\n前一个时刻储能系统目标功率: {self.p_target_percent}\n当前充放电限值: {self.limit_v}")
            logger.error('*'*100)
            logger.error(f"{self.master_station.name}{now.strftime('%Y-%m-%d %H:%M:%S')}前置检查结果：\n是否在线：{self.is_online}，\n充放电状态：{self.discharge_status}，\n"
                  f"储能单元数量：{self.unit_count}, \n防逆流阈值: {self.pccth}, \n变压器容量: {self.cap_tfm}, \n变压器安全系数: {self.v_tprt},\n"
                  f"变压器安全容量: {self.p_th}, \n储能系统实际功率: {self.p_ess}, \n储能系统目标功率: {self.p_target},\n"
                  f"厂区负荷功率: {self.p_load},\n电表采集到的电表功率: {self.p_load_meter}, \n并网点额定功率: {self.p_o},\n并网点SOC: {self.soc},\n"
                  f"系统目标功率变化量绝对值: {self.p_target_diff_abs},\n前一个时刻储能系统目标功率: {self.p_target_percent}\n当前充放电限值: {self.limit_v}\n电表位置：{self.meter_position}")
            print('*' * 50)

    def load_overcapacity_analysis(self):
        """
        负荷超容分析
        :return:
        """""
        # 查询是否已经存在该站点的未恢复超容分析数据
        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='负荷超容', status=0,
                                                        is_delete=0).last()

        # 只有电站在线状态时才需判定超容
        if self.is_online:
            # 负荷超容情况只出现在充电阶段，其它时段不考虑
            if self.discharge_status == 1:
                if self.p_load != '--' and self.p_th != '--':
                    # 超容时
                    if self.p_load > self.p_th:
                        self.is_overcapacity = True
                        print(f"{self.master_station.name}当前在充电阶段，且出现超容")
                        # 查询是否已经存在该站点的未恢复超容分析数据，如不存在，则新增一条超容分析数据
                        if not exist_analysis:
                            new_analysis = RunningAnalysis.objects.create(station=self.master_station, topic='负荷超容', en_topic='Overload',
                                                                          status=0,
                                                                          start_time=datetime.datetime.now(),
                                                                          keyword=f"变压器容量占比最大值: {self.ratio}%",
                                                                          en_keyword=f"Transformer Capacity Ratio Max: {self.ratio}%",
                                                                          ref_threshold=f"变压器容量: {self.cap_tfm}kVA;变压器安全系数: {self.v_tprt * 100}%",
                                                                          en_ref_threshold=f"Transformer Capacity: {self.cap_tfm}kVA;Transformer Safety Ratio: {self.v_tprt * 100}%",)
                        # 存在时，修改其关键字
                        else:
                            exist_analysis.keyword = f"变压器容量占比最大值: {self.ratio}%"
                            exist_analysis.en_keyword = f"Transformer Capacity Ratio Max: {self.ratio}%"
                            exist_analysis.save()

                    # 未超容时
                    else:
                        self.is_overcapacity = False
                        # 查询是否已经存在该站点的未恢复超容分析数据，如存在，则修改其状态
                        if exist_analysis:
                            exist_analysis.status = 1
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.save()

            # 当充电状态发生改变（下一刻不是充电状态）仍为超容状态，则将超容状态恢复正常，结束时间用状态发生改变的时间
            else:
                self.is_overcapacity = False
                if exist_analysis:
                    exist_analysis.status = 1
                    exist_analysis.end_time = datetime.datetime.now()
                    exist_analysis.save()

        # 电站状态离线了，将已经存在的未恢复超容分析数据修改为已恢复
        else:
            self.is_overcapacity = False
            if exist_analysis:
                exist_analysis.status = 1
                exist_analysis.end_time = datetime.datetime.now()
                exist_analysis.save()

    def reverse_power_transmission_analysis(self):
        """
        反送电分析
        :return:
        """""
        # 查询是否已经存在该站点的未恢复的反送电分析数据
        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='反送电', status=0,
                                                        is_delete=0).last()

        # 只有电站在线状态时才需判定反送电
        if self.is_online:
            # 反送电情况只出现在放电阶段，其它时段不考虑
            if self.discharge_status == 2:
                if self.p_load != '--':
                    if self.p_load < 0:
                        self.re_chag_status = True
                        print(f"{self.master_station.name}当前在放电阶段，且出现反送电！！！")

                        # 查询是否已经存在该站点的未恢复超容分析数据，如不存在，则新增一条超容分析数据
                        if not exist_analysis:
                            new_analysis = RunningAnalysis.objects.create(
                                topic='反送电',
                                en_topic='Back Flow',
                                station=self.master_station,
                                start_time=datetime.datetime.now(),
                                keyword=f"反送电最大功率：{self.p_load}kW",
                                en_keyword=f"Back Flow Max Power: {self.p_load}kW",
                                ref_threshold=f"防逆流阈值: {self.pccth}kW",
                                en_ref_threshold=f"Anti Reverse Flow Threshold: {self.pccth}kW",
                                note=str(self.p_load)
                            )

                        # 已存在时
                        else:
                            exist_analysis.keyword = f"反送电最大功率：{self.p_load if self.p_load < float(exist_analysis.note) else float(exist_analysis.note)}kW"
                            exist_analysis.en_keyword = f"Back Flow Max Power: {self.p_load if self.p_load < float(exist_analysis.note) else float(exist_analysis.note)}kW"
                            if self.p_load < float(exist_analysis.note):
                                exist_analysis.note = str(self.p_load)
                            exist_analysis.save()

                    # 未反送电时
                    else:
                        self.re_chag_status = False
                        # 查询是否已经存在该站点的未恢复反送电分析数据，如存在，则修改其状态
                        if exist_analysis:
                            exist_analysis.status = 1
                            # exist_analysis.keyword = f"反送电最大功率：{self.p_load if self.p_load < float(exist_analysis.note) else float(exist_analysis.note)}kW"
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.save()

            # 当充电状态发生改变（下一刻不是充电状态）仍为反送电状态，则将反送电状态恢复正常，结束时间用状态发生改变的时间
            else:
                self.re_chag_status = False
                if exist_analysis:
                    exist_analysis.status = 1
                    # exist_analysis.keyword = f"反送电最大功率：{self.p_load if self.p_load != '--' and self.p_load < float(exist_analysis.note) else float(exist_analysis.note)}kW"
                    exist_analysis.end_time = datetime.datetime.now()
                    exist_analysis.save()

        # 电站状态离线了，将已经存在的未恢复反送电分析数据修改为已恢复
        else:
            self.re_chag_status = False
            if exist_analysis:
                exist_analysis.status = 1
                # exist_analysis.keyword = f"反送电最大功率：{self.p_load if self.p_load != '--' and self.p_load < float(exist_analysis.note) else float(exist_analysis.note)}kW"
                exist_analysis.end_time = datetime.datetime.now()
                exist_analysis.save()

    @staticmethod
    def get_dwd_rhyc_enae(english_name,current_day):
        # 查询dwd_rhyc库 dwd_cumulant_load_data_storage表负向有功电能的anae
        # 获取今天的日期
        start_moment = f"{current_day} 00:00:00"
        end_moment = f"{current_day} 23:55:00"
        start_anae = None
        end_anae = None
        try:
            # 执行SQL查询
            sql = ("""SELECT time, station_name, enae  FROM {} WHERE DATE(time)='{}' and station_name='{}' ORDER BY time;"""
                .format('dwd_cumulant_load_data_storage', current_day, english_name))
            print(sql)
            result = dwd_db_tool.select_single(sql)
            if result:
                result_dict = {item.get("time").strftime('%Y-%m-%d %H:%M:%S'): float(item.get("enae")) for item in result}
                start_anae = result_dict.get(start_moment)
                end_anae = result_dict.get(end_moment)
            # print(start_anae,end_anae)
            return start_anae, end_anae
        except Exception as e:
            print(traceback.format_exc())
            logger.error(traceback.format_exc())

    @staticmethod
    def reverse_power(current_day,english_name,):
        # 根据ads_rhyc 库中 ads_report_loading_data中 p(输出总有功功率) > 0  p_gird(电网侧功率)小于0判断有反送电状态
        # 获取今天的日期
        is_reverse = False
        try:
            # 执行SQL查询
            sql = ("""SELECT COUNT(1) as min_p_load FROM ads_report_loading_data WHERE DATE(time) = '{}' and station='{}'
                    AND p > 5 AND p_gird < 0;"""
                .format(current_day, english_name))
            result = ads_db_tool.select_one(sql)
            print(result,result.get('min_p_load'))
            if result:
                if int(result.get('min_p_load')) != 0 :
                    is_reverse = True
            print(is_reverse)
            return is_reverse
        except Exception as e:
            print(traceback.format_exc())

    def reverse_power_transmission_analysis_day(self):
        """
        反送电分析--优化为一天执行一次
        :return:
        """""
        # 获取当天的日期
        today = datetime.datetime.now().date()
        print("当天日期:", today)
        # 获取前一天的日期
        yesterday = today - datetime.timedelta(days=1)
        print("前一天日期:", yesterday)
        # 获取当月第一天的日期
        first_day_of_month = today.replace(day=1)
        print("当月第一天日期：", first_day_of_month)
        # 判断是否有‘反送电’情况出现
        is_reverse = self.reverse_power(yesterday, self.master_station.english_name)
        if not is_reverse:
            return
        start_anae, end_anae = self.get_dwd_rhyc_enae(self.master_station.english_name,yesterday)
        month_first_anae, _= self.get_dwd_rhyc_enae(self.master_station.english_name, first_day_of_month)
        start_moment = f"{yesterday} 00:00:00"
        end_moment = f"{yesterday} 23:59:00"
        if start_anae is None or end_anae is None:
            day_anae = "--"
        else:
            day_anae = '{:.2f}'.format(end_anae - start_anae)
        if month_first_anae is not None:
            month_anae = '{:.2f}'.format(end_anae - month_first_anae)
        else:
            month_anae = "--"
        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='反送电', start_time__date=yesterday,
                                                        is_delete=0).first()

        # 查询是否已经存在该站点的未恢复超容分析数据，如不存在，则新增一条超容分析数据
        if not exist_analysis:
            print(f"当日反送电量：{day_anae}kWh,当月反送电量：{month_anae}kWh")
            try:
                # 查询变压器容量及安全系数，计算变压器安全容量
                ems_measure = self.redis_conn.get(
                    'Business:TRANS_TIMING_{}_{}_{}'.format('MEASURE', self.master_station.english_name,
                                                            'EMS'))
                if ems_measure:
                    measure_ems_dict = json.loads(json.loads(ems_measure.decode("utf-8")))
                else:
                    measure_ems_dict = {}
                # 防逆流阈值
                pccth = float(measure_ems_dict.get("Pccth")) if measure_ems_dict.get(
                    "Pccth") not in EMPTY_STR_LIST else '--'
            except Exception as e:
                pccth = "--"
                print(e)
                print(traceback.print_exc())
            new_analysis = RunningAnalysis.objects.create(
                topic='反送电',
                status=1,
                en_topic='Back Flow',
                station=self.master_station,
                start_time=start_moment,
                end_time=end_moment,
                keyword=f"当日反送电量：{day_anae}kWh\n当月反送电量：{month_anae}kWh",
                en_keyword=f"Reverse power transmission on the same day: {day_anae}kWh\nReverse power transmission for the current month: {month_anae}kWh",
                ref_threshold=f"防逆流阈值: {pccth}kW",
                en_ref_threshold=f"Anti Reverse Flow Threshold: {pccth}kW",
                note=str(day_anae)
            )


    def no_charging_as_targeted_analysis(self):
        """
        未按目标充电分析
        :return:
        """""
        # 查询是否已经存在该站点的未恢复的反送电分析数据
        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='未按目标充电', status=0,
                                                        is_delete=0).last()

        now = datetime.datetime.now()

        if self.is_online:
            if self.discharge_status == 1:
                if self.unit_count > 1:
                    # 判定阈值
                    x = (1.05 * (self.unit_count - 1) / self.unit_count)
                    # 系统SOC≥95 %
                    if self.soc <= 95:
                        if abs(self.p_ess) < x * abs(self.p_target) * self.limit_v:
                            if self.p_th != '--' and self.p_th - self.p_load >= self.p_o:
                                # 系统目标功率变化量绝对值≠0
                                if self.p_target_diff_abs == 0:
                                    print('!' * 100)
                                    print(f"{self.master_station.name}当前在充电阶段，且未按目标充电！！！")
                                    print(
                                        f"{self.master_station.name}{now.strftime('%Y-%m-%d %H:%M:%S')}前置检查结果：\n是否在线is_online：{self.is_online}，\n充放电状态discharge_status：{self.discharge_status}，\n"
                                        f"储能单元数量unit_count：{self.unit_count}, \n防逆流阈值pccth: {self.pccth}, \n变压器容量cap_tfm: {self.cap_tfm}, \n变压器安全系数v_tprt: {self.v_tprt},\n"
                                        f"变压器安全容量p_th: {self.p_th}, \n储能系统实际功率p_ess: {self.p_ess}, \n储能系统目标功率p_target: {self.p_target},\n"
                                        f"厂区负荷功率p_load: {self.p_load},\n电表采集到的电表功率p_load_meter: {self.p_load_meter}, \n并网点额定功率p_o: {self.p_o},\n并网点SOC: {self.soc},\n"
                                        f"系统目标功率变化量绝对值p_target_diff_abs: {self.p_target_diff_abs},\n前一个时刻储能系统目标功率p_target_percent: {self.p_target_percent}, \n充放电限值: {self.limit_v}")
                                    print('!' * 100)

                                    # 计算关键参数：平均功率，如新增则等于当前储能系统功率，如修改则等于开始时间到当前时间范围内的储能系统功率平均值
                                    # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如不存在，则新增一条未按目标充电分析数据
                                    if not exist_analysis:
                                        new_analysis = RunningAnalysis.objects.create(
                                            topic='未按目标充电',
                                            en_topic='Unexpected Charging',
                                            station=self.master_station,
                                            start_time=datetime.datetime.now(),
                                            keyword=f"平均功率：{self.p_ess}kW",
                                            en_keyword=f"Average Power：{self.p_ess}kW",
                                            ref_threshold=f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - self.p_load), 2)}kW",
                                            en_ref_threshold=f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - self.p_load), 2)}kW",
                                            note=str(self.p_load)
                                        )

                                    # 已存在时
                                    else:
                                        # 平均功率
                                        avg_p = self.get_average_p_ess(exist_analysis.start_time, now)
                                        # 时间范围内工厂负荷最小值
                                        min_p_load = float(exist_analysis.note) if float(exist_analysis.note) < self.p_load else self.p_load

                                        exist_analysis.note = min_p_load
                                        exist_analysis.keyword = f"平均功率：{avg_p}kW"
                                        exist_analysis.en_keyword = f"Average Power：{avg_p}kW"
                                        exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2)}kW"
                                        exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2)}kW"
                                        exist_analysis.save()
                                else:
                                    # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                                    if exist_analysis:
                                        min_p_load = float(exist_analysis.note) if float(
                                            exist_analysis.note) < self.p_load else self.p_load

                                        exist_analysis.note = min_p_load

                                        exist_analysis.status = 1
                                        exist_analysis.end_time = datetime.datetime.now()
                                        exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                        exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2)}kW"
                                        exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                        exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2)}kW"
                                        exist_analysis.save()
                            else:
                                # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                                if exist_analysis:
                                    min_p_load = float(exist_analysis.note) if float(
                                        exist_analysis.note) < self.p_load else self.p_load

                                    exist_analysis.note = min_p_load

                                    exist_analysis.status = 1
                                    exist_analysis.end_time = datetime.datetime.now()
                                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                    exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                    exist_analysis.save()
                        else:
                            # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                            if exist_analysis:
                                min_p_load = float(exist_analysis.note) if float(
                                    exist_analysis.note) < self.p_load else self.p_load

                                exist_analysis.note = min_p_load

                                exist_analysis.status = 1
                                exist_analysis.end_time = datetime.datetime.now()
                                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                exist_analysis.save()
                    else:
                        # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                        if exist_analysis:
                            min_p_load = float(exist_analysis.note) if float(
                                exist_analysis.note) < self.p_load else self.p_load

                            exist_analysis.note = min_p_load

                            exist_analysis.status = 1
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                            exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                            exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                            exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                            exist_analysis.save()
                else:
                    if self.soc <= 95:
                        if self.p_ess == 0:
                            if self.p_th != '--' and self.p_th - self.p_load >= self.p_o:
                                # 系统目标功率变化量绝对值≠0
                                if self.p_target_diff_abs == 0:
                                    print(f"{self.master_station.name}当前在充电阶段，且未按目标充电！！！")

                                    # 计算关键参数：平均功率，如新增则等于当前储能系统功率，如修改则等于开始时间到当前时间范围内的储能系统功率平均值
                                    # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如不存在，则新增一条未按目标充电分析数据
                                    if not exist_analysis:
                                        new_analysis = RunningAnalysis.objects.create(
                                            topic='未按目标充电',
                                            en_topic='Unexpected Charging',
                                            station=self.master_station,
                                            start_time=datetime.datetime.now(),
                                            keyword=f"平均功率：{self.p_ess}kW",
                                            en_keyword=f"Average Power：{self.p_ess}kW",
                                            ref_threshold=f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{self.p_th - self.p_load if self.p_th != '--' else '--'}kW",
                                            en_ref_threshold=f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {self.p_th - self.p_load if self.p_th != '--' else '--'}kW",
                                            note=str(self.p_load)
                                        )

                                    # 已存在时
                                    else:
                                        # 平均功率
                                        avg_p = self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))
                                        # 时间范围内工厂负荷最小值
                                        min_p_load = float(exist_analysis.note) if float(
                                            exist_analysis.note) < self.p_load else self.p_load

                                        exist_analysis.note = min_p_load
                                        exist_analysis.keyword = f"平均功率：{avg_p}kW"
                                        exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                        exist_analysis.en_keyword = f"Average Power：{avg_p}kW"
                                        exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                        exist_analysis.save()
                                else:
                                    # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                                    if exist_analysis:
                                        min_p_load = float(exist_analysis.note) if float(
                                            exist_analysis.note) < self.p_load else self.p_load

                                        exist_analysis.note = min_p_load

                                        exist_analysis.status = 1
                                        exist_analysis.end_time = datetime.datetime.now()
                                        exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                        exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                        exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                        exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                        exist_analysis.save()
                            else:
                                # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                                if exist_analysis:
                                    min_p_load = float(exist_analysis.note) if float(
                                        exist_analysis.note) < self.p_load else self.p_load

                                    exist_analysis.note = min_p_load

                                    exist_analysis.status = 1
                                    exist_analysis.end_time = datetime.datetime.now()
                                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                    exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                    exist_analysis.save()
                        else:
                            # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                            if exist_analysis:
                                min_p_load = float(exist_analysis.note) if float(
                                    exist_analysis.note) < self.p_load else self.p_load

                                exist_analysis.note = min_p_load

                                exist_analysis.status = 1
                                exist_analysis.end_time = datetime.datetime.now()
                                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                                exist_analysis.save()
                    else:
                        # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                        if exist_analysis:
                            min_p_load = float(exist_analysis.note) if float(
                                exist_analysis.note) < self.p_load else self.p_load

                            exist_analysis.note = min_p_load

                            exist_analysis.status = 1
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                            exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW ; 充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                            exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                            exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                            exist_analysis.save()
            else:
                # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                if exist_analysis:
                    min_p_load = float(exist_analysis.note) if float(
                        exist_analysis.note) < self.p_load else self.p_load

                    exist_analysis.note = min_p_load

                    exist_analysis.status = 1
                    exist_analysis.end_time = datetime.datetime.now()
                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                    exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW ; 充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                    exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                    exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                    exist_analysis.save()
        else:
            # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
            if exist_analysis:
                min_p_load = float(exist_analysis.note) if float(
                    exist_analysis.note) < self.p_load else self.p_load

                exist_analysis.note = min_p_load

                exist_analysis.status = 1
                exist_analysis.end_time = datetime.datetime.now()
                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW ; 充电最大空间：{round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                exist_analysis.en_keyword = f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Charging Maximum Capacity: {round((self.p_th - min_p_load), 2) if self.p_th != '--' else '--'}kW"
                exist_analysis.save()

    def no_discharging_as_targeted_analysis(self):
        """
        未按目标放电分析
        :return:
        """""
        # 查询是否已经存在该站点的未恢复的未按目标放电分析数据
        exist_analysis = RunningAnalysis.objects.filter(station=self.master_station, topic='未按目标放电', status=0,
                                                        is_delete=0).last()

        now = datetime.datetime.now()

        if self.is_online:
            if self.discharge_status == 2:
                # 储能单元unit_count>1时：根据公式进行判断
                if self.unit_count > 1:
                    # 判定阈值
                    x = (1.05 * (self.unit_count - 1) / self.unit_count)
                    # 系统SOC≥95 %
                    if self.soc >= 5:
                        if abs(self.p_ess) < x * abs(self.p_target) * self.limit_v:
                            if self.p_load >= self.p_o:
                                # 系统目标功率变化量绝对值≠0
                                if self.p_target_diff_abs == 0:
                                    print('!' * 100)
                                    print(f"{self.master_station.name}当前在放电阶段，且未按目标放电！！！")
                                    print(
                                        f"{self.master_station.name}{now.strftime('%Y-%m-%d %H:%M:%S')}前置检查结果：\n是否在线：{self.is_online}，\n充放电状态：{self.discharge_status}，\n"
                                        f"储能单元数量：{self.unit_count}, \n防逆流阈值: {self.pccth}, \n变压器容量: {self.cap_tfm}, \n变压器安全系数: {self.v_tprt},\n"
                                        f"变压器安全容量: {self.p_th}, \n储能系统实际功率: {self.p_ess}, \n储能系统目标功率: {self.p_target},\n"
                                        f"厂区负荷功率: {self.p_load},\n电表采集到的电表功率: {self.p_load_meter}, \n并网点额定功率: {self.p_o},\n并网点SOC: {self.soc},\n"
                                        f"系统目标功率变化量绝对值: {self.p_target_diff_abs},\n前一个时刻储能系统目标功率: {self.p_target_percent}, \n充放电限值: {self.limit_v}")
                                    print('!' * 100)

                                    # 计算关键参数：平均功率，如新增则等于当前储能系统功率，如修改则等于开始时间到当前时间范围内的储能系统功率平均值
                                    # 查询是否已经存在该站点的未恢复未按目标放电分析数据，如不存在，则新增一条未按目标放电分析数据
                                    if not exist_analysis:
                                        new_analysis = RunningAnalysis.objects.create(
                                            topic='未按目标放电',
                                            en_topic='Unexpected Discharging',
                                            station=self.master_station,
                                            start_time=datetime.datetime.now(),
                                            keyword=f"平均功率：{self.p_ess}kW",
                                            en_keyword=f"Average Power：{self.p_ess}kW",
                                            ref_threshold=f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                          f"{self.p_load}kW;防逆流阈值：{self.pccth}kW",
                                            en_ref_threshold=f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: {self.p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW",
                                            note=str(self.p_load)
                                        )

                                    # 已存在时
                                    else:
                                        # 平均功率
                                        avg_p = self.get_average_p_ess(exist_analysis.start_time, now)
                                        # 时间范围内工厂负荷最大值
                                        max_p_load = float(exist_analysis.note) if float(
                                            exist_analysis.note) > self.p_load else self.p_load

                                        exist_analysis.note = max_p_load
                                        exist_analysis.keyword = f"平均功率：{avg_p}kW"
                                        exist_analysis.ref_threshold = f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值：{max_p_load}kW;防逆流阈值：{self.pccth}kW"
                                        exist_analysis.en_keyword = f"Average Power：{avg_p}kW"
                                        exist_analysis.en_ref_threshold = f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: {max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW"
                                        print(539, '*'*100, exist_analysis)
                                        exist_analysis.save()
                                else:
                                    # 查询是否已经存在该站点的未恢复未按目标放电分析数据，如存在，则修改其状态
                                    if exist_analysis:
                                        # 时间范围内工厂负荷最大值
                                        max_p_load = float(exist_analysis.note) if float(
                                            exist_analysis.note) > self.p_load else self.p_load

                                        exist_analysis.note = max_p_load

                                        exist_analysis.status = 1
                                        exist_analysis.end_time = datetime.datetime.now()
                                        exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                        exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                        f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                        exist_analysis.en_keyword = (f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW")
                                        exist_analysis.en_ref_threshold = (f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                                                            f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                        print(539, '*' * 100, exist_analysis)
                                        exist_analysis.save()
                            else:
                                # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                                if exist_analysis:
                                    # 时间范围内工厂负荷最大值
                                    max_p_load = float(exist_analysis.note) if float(
                                        exist_analysis.note) > self.p_load else self.p_load

                                    exist_analysis.note = max_p_load

                                    exist_analysis.status = 1
                                    exist_analysis.end_time = datetime.datetime.now()
                                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                    f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                    exist_analysis.en_keyword = (
                                        f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW")
                                    exist_analysis.en_ref_threshold = (
                                        f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                        f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                    print(539, '*' * 100, exist_analysis)
                                    exist_analysis.save()
                        else:
                            # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                            if exist_analysis:
                                # 时间范围内工厂负荷最大值
                                max_p_load = float(exist_analysis.note) if float(
                                    exist_analysis.note) > self.p_load else self.p_load

                                exist_analysis.note = max_p_load

                                exist_analysis.status = 1
                                exist_analysis.end_time = datetime.datetime.now()
                                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                exist_analysis.en_keyword = (
                                    f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW")
                                exist_analysis.en_ref_threshold = (
                                    f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                    f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                print(539, '*' * 100, exist_analysis)
                                exist_analysis.save()
                    else:
                        # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                        if exist_analysis:
                            # 时间范围内工厂负荷最大值
                            max_p_load = float(exist_analysis.note) if float(
                                exist_analysis.note) > self.p_load else self.p_load

                            exist_analysis.note = max_p_load

                            exist_analysis.status = 1
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                            exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                            f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                            exist_analysis.en_keyword = (
                                f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW")
                            exist_analysis.en_ref_threshold = (
                                f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                            print(539, '*' * 100, exist_analysis)
                            exist_analysis.save()

                # 储能单元unit_count=1时：在系统放电&S0C>5%时，判断实际功率是否为0，若是为0，则提示未按目标放电。
                else:
                    if self.soc >= 5:
                        if self.p_ess == 0:
                            if self.p_load >= self.p_o:
                                print(662, f"{self.master_station.name}当前在放电阶段，且未按目标放电！！！")
                                # 计算关键参数：平均功率，如新增则等于当前储能系统功率，如修改则等于开始时间到当前时间范围内的储能系统功率平均值
                                # 查询是否已经存在该站点的未恢复未按目标放电分析数据，如不存在，则新增一条未按目标放电分析数据
                                if not exist_analysis:
                                    new_analysis = RunningAnalysis.objects.create(
                                        topic='未按目标放电',
                                        en_topic='Unexpected Discharging',
                                        station=self.master_station,
                                        start_time=datetime.datetime.now(),
                                        keyword=f"平均功率：{self.p_ess}kW",
                                        ref_threshold=f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值： {self.p_load}kW;防逆流阈值：{self.pccth}kW",
                                        en_keyword=f"Average Power：{self.p_ess}kW",
                                        en_ref_threshold=f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: {self.p_load}kW;Anti Reverse Flow Threshold：{self.pccth}kW",
                                        note=str(self.p_load)
                                    )

                                # 已存在时
                                else:
                                    # 平均功率
                                    avg_p = self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))
                                    # 时间范围内工厂负荷最大值
                                    max_p_load = float(exist_analysis.note) if float(
                                        exist_analysis.note) > self.p_load else self.p_load

                                    exist_analysis.note = max_p_load
                                    exist_analysis.keyword = f"平均功率：{avg_p}kW"
                                    exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                    f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                    exist_analysis.en_keyword = (
                                        f"Average Power：{avg_p}kW")
                                    exist_analysis.en_ref_threshold = (
                                        f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                        f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                    print(645, '*' * 100, exist_analysis)
                                    exist_analysis.save()
                            else:
                                # 查询是否已经存在该站点的未恢复未按目标放电分析数据，如存在，则修改其状态
                                if exist_analysis:
                                    # 时间范围内工厂负荷最大值
                                    max_p_load = float(exist_analysis.note) if float(
                                        exist_analysis.note) > self.p_load else self.p_load

                                    exist_analysis.note = max_p_load

                                    exist_analysis.status = 1
                                    exist_analysis.end_time = datetime.datetime.now()
                                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                                    exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                    f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                    exist_analysis.en_keyword = (
                                        f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW")
                                    exist_analysis.en_ref_threshold = (
                                        f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                        f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                    print(661, '*' * 100, exist_analysis)
                                    exist_analysis.save()
                        else:
                            # 查询是否已经存在该站点的未恢复未按目标放电分析数据，如存在，则修改其状态
                            if exist_analysis:
                                # 时间范围内工厂负荷最大值
                                max_p_load = float(exist_analysis.note) if float(
                                    exist_analysis.note) > self.p_load else self.p_load

                                exist_analysis.note = max_p_load

                                exist_analysis.status = 1
                                exist_analysis.end_time = datetime.datetime.now()
                                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                                exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                                f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                                exist_analysis.en_keyword = (
                                    f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW")
                                exist_analysis.en_ref_threshold = (
                                    f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                    f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                                print(661, '*' * 100, exist_analysis)
                                exist_analysis.save()

                    else:
                        # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                        if exist_analysis:
                            # 时间范围内工厂负荷最大值
                            max_p_load = float(exist_analysis.note) if float(
                                exist_analysis.note) > self.p_load else self.p_load

                            exist_analysis.note = max_p_load

                            exist_analysis.status = 1
                            exist_analysis.end_time = datetime.datetime.now()
                            exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                            exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                            f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                            exist_analysis.en_keyword = (
                                f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW")
                            exist_analysis.en_ref_threshold = (
                                f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                                f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                            print(539, '*' * 100, exist_analysis)
                            exist_analysis.save()
            else:
                # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
                if exist_analysis:
                    # 时间范围内工厂负荷最大值
                    max_p_load = float(exist_analysis.note) if float(
                        exist_analysis.note) > self.p_load else self.p_load

                    exist_analysis.note = max_p_load

                    exist_analysis.status = 1
                    exist_analysis.end_time = datetime.datetime.now()
                    exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW"
                    exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                    f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                    exist_analysis.en_keyword = (
                        f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now - datetime.timedelta(minutes=4))}kW")
                    exist_analysis.en_ref_threshold = (
                        f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                        f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                    print(539, '*' * 100, exist_analysis)
                    exist_analysis.save()
        else:
            # 查询是否已经存在该站点的未恢复未按目标充电分析数据，如存在，则修改其状态
            if exist_analysis:
                # 时间范围内工厂负荷最大值
                max_p_load = float(exist_analysis.note) if float(
                    exist_analysis.note) > self.p_load else self.p_load

                exist_analysis.note = max_p_load

                exist_analysis.status = 1
                exist_analysis.end_time = datetime.datetime.now()
                exist_analysis.keyword = f"平均功率：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW"
                exist_analysis.ref_threshold = (f"目标功率: {self.p_target * self.limit_v}kW;工厂负荷功率最大值："
                                                f"{max_p_load}kW;防逆流阈值：{self.pccth}kW")
                exist_analysis.en_keyword = (
                    f"Average Power：{self.get_average_p_ess(exist_analysis.start_time, now-datetime.timedelta(minutes=4))}kW")
                exist_analysis.en_ref_threshold = (
                    f"Expected Power: {self.p_target * self.limit_v}kW;Factory Load Power Maximum: "
                    f"{max_p_load}kW;Anti Reverse Flow Threshold: {self.pccth}kW")
                print(539, '*' * 100, exist_analysis)
                exist_analysis.save()

    def run(self):
        try:
            # 离线分析
            self.online_analysis()
            # 前置检查
            self.pre_default_analysis()
            # 超容分析
            self.load_overcapacity_analysis()
            # # 反送电分析
            # self.reverse_power_transmission_analysis()
            # 未按目标充电分析
            self.no_charging_as_targeted_analysis()
            # 未按目标放电分析
            self.no_discharging_as_targeted_analysis()
        except Exception as e:
            print(f"！！！！！！ {self.master_station} 分析失败！")
            print(traceback.print_exc())
            pass

    def run_day(self):
        try:
            # 反送电分析
            self.reverse_power_transmission_analysis_day()
        except Exception as e:
            print(f"！！！！！！ {self.master_station} 分析失败！")
            print(traceback.print_exc())
            pass


def all_stations_analysis():
    master_stations = MaterStation.objects.filter(is_delete=0).all()
    # master_stations = MaterStation.objects.filter(id=54).all()
    for master_station in master_stations:
        analysis_tool = RunningAnalysisTool(master_station)
        analysis_tool.run()

def all_stations_analysis_day():
    """反向送电改为一天跑一次 未按光伏得并网点和光伏为0"""
    master_stations = MaterStation.objects.filter(Q(is_delete=0) & Q(project__gf_cap__isnull=True) | Q(project__gf_cap=0)).all()
    # master_stations = MaterStation.objects.filter(id=54).all()
    for master_station in master_stations:
        analysis_tool = RunningAnalysisTool(master_station)
        analysis_tool.run_day()


if __name__ == '__main__':
    master_stations = MaterStation.objects.filter(
        Q(is_delete=0) & Q(project__gf_cap__isnull=True) | Q(project__gf_cap=0)).all()
    for master_station in master_stations:
        print(master_station.english_name)
    # master_station = MaterStation.objects.get(id=4, is_delete=0)
        analysis_tool = RunningAnalysisTool(master_station)
        analysis_tool.run_day()
