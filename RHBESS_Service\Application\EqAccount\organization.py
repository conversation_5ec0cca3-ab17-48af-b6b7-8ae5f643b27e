# -*- coding:utf8 -*-
import json
import logging
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import desc
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.base_handler import BaseHandler
from Application.Models.User.organization import Organization
from Application.Models.User.organization_type import OrganizationType
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.user import User
from Tools.Utils.num_utils import Translate_cls
import uuid


class OrganizationIntetface(BaseHandler):
    '''
    @description: 线路组织管理
    @param {*} self
    @param {*} kt
    @return {*}
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        try:
            if kt == 'GetAllType':  # 获取所有组织类型
                data = []
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s,pageNum:%s,pageSize:%s"%(descr,pageNum,pageSize))
                if descr :
                    total = user_session.query(func.count(OrganizationType.id)).filter(OrganizationType.descr.like('%' + descr + '%')).scalar()
                    all = user_session.query(OrganizationType).filter(OrganizationType.descr.like('%' + descr + '%')).order_by(OrganizationType.id.desc()
                    ).limit(pageSize).offset((pageNum-1)*pageSize).all()
                else:
                    total = user_session.query(func.count(OrganizationType.id)).scalar()
                    all = user_session.query(OrganizationType).order_by(OrganizationType.id.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for org in all:
                    org = eval(str(org))
                    if lang == 'en':
                        replaced_data = OrganizationType.replace_en_fields(org, "")
                        org.update(replaced_data)
                    data.append(org)
                self.returnTotalSuc(data,total)
            elif kt == 'GetAll':  # 返回所有组织数据
                data,filter = [],[]
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if not lang:
                    lang = 'zh'

                if DEBUG:
                    logging.info("id:%s,descr:%s,pageNum:%s,pageSize:%s"%(id,descr,pageNum,pageSize))
                if not id:
                    session = self.getOrNewSession()
                    id = session.user['organization_id']
                    name = session.user['descr']
                if descr:
                    filter.append(Organization.descr.like('%'+descr+"%"))
                if name == 'admin':
                    total = user_session.query(func.count(Organization.id)).filter(*filter).scalar()
                    all = user_session.query(Organization).filter(*filter).order_by(Organization.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                else:
                    org = user_session.query(Organization).filter(Organization.id==id).first()
                    if not org:
                        return self.customError("无效id") if lang == 'zh' else self.customError("Invalid ID")
                    get_end_node = org.get_lower_node()
                    filter.append(Organization.id.in_(get_end_node))
                    total = user_session.query(func.count(Organization.id)).filter(*filter).scalar()
                    all = user_session.query(Organization).filter(*filter).order_by(Organization.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()

                for org in all:
                    o = eval(str(org))
                    p = None
                    if org.parent_id:
                        p = user_session.query(Organization).get(org.parent_id)
                    if p:
                        o['parent_descr'] = p.descr if lang == 'zh' else p.en_descr
                    if lang == 'en':
                        o['descr'] = org.en_descr
                        o['type_descr'] = org.organizationType.en_descr
                    data.append(o)
                self.returnTotalSuc(data,total)
            elif kt == 'GetList':  # 返回组织树状结构
                apex = user_session.query(Organization).filter(Organization.parent_id == None).order_by(Organization.id.desc()).all()
                user_session.commit()
                all = []
                for b in apex:
                    o = eval(str(b))
                    o['children'] = self.structure(b)
                    all.append(o)
                return self.returnTypeSuc(all)
            elif kt == 'GetUsersList':  # 返回组织及用户树状结构
                id = self.get_argument('id',None)
                if id:
                    apex = user_session.query(Organization).filter(Organization.id == id).order_by(Organization.id.desc()).all()
                else:
                    apex = user_session.query(Organization).filter(Organization.parent_id == None).order_by(Organization.id.desc()).all()
                all = []
                if lang=='en':
                    for b in apex:
                        users = user_session.query(User).filter(User.organization_id == b.id).order_by(User.id.desc()).all()
                        o,us={"id":b.id,"label":b.en_descr,'key':str(uuid.uuid1())},[]
                        for u in users:
                            us.append({"id":u.id,"label":u.en_name,'key':str(uuid.uuid1())})
                        o['children'] = self.structure_users(b,us)
                        all.append(o)
                else:
                    for b in apex:
                        users = user_session.query(User).filter(User.organization_id == b.id).order_by(
                            User.id.desc()).all()
                        o, us = {"id": b.id, "label": b.descr, 'key': str(uuid.uuid1())}, []
                        for u in users:
                            us.append({"id": u.id, "label": u.name, 'key': str(uuid.uuid1())})
                        o['children'] = self.structure_users(b, us)
                        all.append(o)
                return self.returnTypeSuc(all)
            elif kt == 'GetOrgIdDescr':  # 只获取组织结构id和描述
                apex = user_session.query(Organization.id,Organization.descr).all()
                user_session.commit()
                all = []
                for a in apex:
                    all.append({'id':a[0],'descr':a[1]})
                return self.returnTypeSuc(all)

            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()


    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        lang = self.get_argument('lang', None)
        try:
            if kt == 'DeleteType':  # 删除组织类型及其下级所有组织
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info("id:%s"%(id))
                org = user_session.query(OrganizationType).filter(OrganizationType.id==id).first()
                if not lang:
                    lang = 'zh'
                if not id or not org:
                    return self.customError("id为空或无效") if lang == 'zh' else self.customError("ID is empty or invalid")
                org.deleteOrganizationType(id)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
               
            elif kt == 'UpdateType':  # 修改组织类型描述
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s,descr:%s"%(id,descr))
                if not descr:
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                org = user_session.query(OrganizationType).filter(OrganizationType.id==id).first()
                if not org:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                organizationType = user_session.query(OrganizationType).filter(OrganizationType.descr == descr).first()
                if organizationType and organizationType.id != int(id):
                    return self.customError("该类型已存在") if lang == 'zh' else self.customError("This type already exists")
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                t_res = t_cls.str_chinese(descr)
                if ty == 2:
                    en_descr = t_res
                else:
                    en_descr = descr
                    descr = t_res
                org.descr = descr
                org.en_descr = en_descr
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
               
            elif kt == 'AddType':  # 添加组织类型
                descr = self.get_argument('descr',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("descr:%s"%(descr))
                if not descr:
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                elif user_session.query(OrganizationType).filter(OrganizationType.descr == descr).first():
                    return self.customError("该机构已存在") if lang == 'zh' else self.customError("The institution already exists")
                else:
                    ty = 1 if lang == 'en' else 2
                    t_cls = Translate_cls(ty)
                    t_res = t_cls.str_chinese(descr)
                    if ty == 2:
                        en_descr = t_res
                    else:
                        en_descr = descr
                        descr = t_res
                    org = OrganizationType(descr=descr, en_descr=en_descr)
                    user_session.add(org)
                    user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            
            elif kt == 'GetChild':  # 获取区域子节点
                id = self.get_argument('id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                all = []
                apex = user_session.query(Organization).filter(Organization.id==id).first()
                if not apex:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                all = self.structure(apex)
                return self.returnTypeSuc(all)
        
            elif kt == 'Delete':  # 删除组织
                id = self.get_argument('id',0)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s"%(id))
                delete_apex = user_session.query(Organization).filter(Organization.id==id).first()
                if not id or not delete_apex:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                orglist = delete_apex.get_lower_node()
                for l in orglist:
                    delete_apex.deleteOrganization(l)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
            
            elif kt == 'Add':  # 添加组织
                parent_id = self.get_argument('parent_id',None)
                descr = self.get_argument('descr',None)
                type_id = self.get_argument('type_id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("parent_id:%s,descr:%s,type_id:%s"%(parent_id,descr,type_id))
                if not descr :
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                if not type_id:
                    return self.customError("组织类型为空") if lang == 'zh' else self.customError("The organization type is empty")
                if user_session.query(Organization).filter(Organization.descr == descr,Organization.type_id==type_id,Organization.parent_id==parent_id).first():
                    return self.customError("该组织已存在") if lang == 'zh' else self.customError("The organization already exists")
                if parent_id :
                    parent_id = parent_id
                ty = 1 if lang == 'en' else 2
                t_cls = Translate_cls(ty)
                t_res = t_cls.str_chinese(descr)
                if ty == 2:
                    en_descr = t_res
                else:
                    en_descr = descr
                    descr = t_res
                organization = Organization(descr=descr,en_descr=en_descr,parent_id=parent_id,type_id=type_id,op_ts=timeUtils.getNewTimeStr())
                user_session.add(organization)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
                
            elif kt == 'Modify':  # 修改组织信息
                id = self.get_argument('id',None)
                parent_id = self.get_argument('parent_id',None)
                descr = self.get_argument('descr',None)
                type_id = self.get_argument('type_id',None)
                if not lang:
                    lang = 'zh'
                if DEBUG:
                    logging.info("id:%s,parent_id:%s,descr:%s,type_id:%s"%(id,parent_id,descr,type_id))
                to_update = user_session.query(Organization).filter(Organization.id==id).first()
                if not descr :
                    return self.customError("名称为空") if lang == 'zh' else self.customError("Name is empty")
                if not type_id:
                    return self.customError("组织类型为空") if lang == 'zh' else self.customError("The organization type is empty")
                if not to_update:
                    return self.customError("id无效") if lang == 'zh' else self.customError("Invalid ID")
                if id == parent_id:
                    return self.customError("自身不可作为自身子节点") if lang == 'zh' else self.customError("Cannot be a child node of oneself")
                organization = user_session.query(Organization).filter(Organization.descr == descr,Organization.type_id==type_id,Organization.parent_id==parent_id).first()
                if organization and organization.id != int(id):
                    return self.customError("该组织已存在") if lang == 'zh' else self.customError("The organization already exists")
                to_update.parent_id = parent_id if parent_id else None
                if descr:
                    ty = 1 if lang == 'en' else 2
                    t_cls = Translate_cls(ty)
                    t_res = t_cls.str_chinese(descr)
                    if ty == 2:
                        en_descr = t_res
                    else:
                        en_descr = descr
                        descr = t_res
                    to_update.descr = descr
                    to_update.en_descr = en_descr
                if type_id:
                    to_update.type_id = type_id
                user_session.commit()
                self.modify_structure(to_update,type_id)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc('')
               
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
            # return self.requestError()
        finally:
            user_session.close()
                

    # 更新子节点
    def modify_structure(self, apex,type_id):
        rows = user_session.query(Organization).filter(Organization.parent_id == apex.id).all()
        for row in rows:
            row.type_id = type_id
            self.modify_structure(row,type_id)
        user_session.commit()

    def delete_structure(self, apex ):
        # 删除子节点
        
        apex.deleteOrganization(apex.id)
        rows = user_session.query(Organization).filter(Organization.parent_id == apex.id).all()
        for row in rows:
            row.deleteOrganization(row.id)
            self.delete_structure(row)
        user_session.query(Organization).filter(Organization.id == apex.id).delete()
        

    def structure(self, Bean):
        # 递归构建树状组织
        all = []
        chaild = user_session.query(Organization).filter(Organization.parent_id == Bean.id).all()
        for ch in chaild:
            cha = eval(str(ch))
            p = None
            if ch.parent_id:
                p = user_session.query(Organization).get(ch.parent_id)
            if p:
                cha['parent_descr'] = p.descr
            cha['children'] = self.structure(ch)
            all.append(cha)
        
        return all
    
    def structure_users(self, Bean,us):
        # 递归构建树状组织
        all = []
        all=all+us
        chaild = user_session.query(Organization).filter(Organization.parent_id == Bean.id).all()
        for ch in chaild:
            users = user_session.query(User).filter(User.organization_id == ch.id).order_by(User.id.desc()).all()
            cha,us={"id":ch.id,"label":ch.descr,'key':str(uuid.uuid1())},[]
            for u in users:
                us.append({"id":u.id,"label":u.name,'key':str(uuid.uuid1())})
            # cha = eval(str(ch))
            cha['children'] = self.structure_users(ch,us)
            all.append(cha)
        
        return all
   