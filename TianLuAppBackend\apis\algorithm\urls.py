#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/10/14 下午5:02
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me


from django.urls import path

from apis.algorithm.views import algorithm_views


urlpatterns = [
    # 算法需要接口
    path("Project/Infos", algorithm_views.ProjectView.as_view()),  # 项目及电站列表
    path("Stations/PLoads", algorithm_views.StationPloadsView.as_view()),  # 获取并网点负荷
    path("Stations/ElectricQuantity", algorithm_views.StationElectricQuantityView.as_view()),  # 获取并网点电量
    path("Stations/SaveForecast", algorithm_views.StationSaveForecastView.as_view()),  # 保存预测结果数据
    path("Stations/WeatherInfo", algorithm_views.StationWeatherView.as_view()),  # 获取天气信息
    path("Stations/ElePrice", algorithm_views.StationPriceView.as_view()),  # 电价信息
]