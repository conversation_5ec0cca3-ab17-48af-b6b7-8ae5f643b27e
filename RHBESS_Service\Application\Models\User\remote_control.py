#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-20 15:31:31
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-28 10:00:40


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class RemoteControl(user_Base):
    u'项目信息遥控表'
    __tablename__ = "t_remote_control"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    name = Column(VARCHAR(256), nullable=False,comment=u"设备名称")
    t_eq_id = Column(VARCHAR(100), nullable=False, comment=u"设备表id")
    num = Column(VARCHAR(50), nullable=True,comment=u"编号")
    mea_point_n = Column(VARCHAR(50), nullable=False,comment=u"测点名称")
    type_ = Column(CHAR(2), nullable=False,comment=u"类型,1遥测2遥信3遥调4遥控")
    remarks = Column(VARCHAR(256), nullable=True,comment=u"备注")
    is_use = Column(CHAR(2), nullable=False,server_default='1',comment=u"是否使用1是0否")
    station = Column(VARCHAR(50), nullable=False, comment=u"所属站")
    en_name = Column(VARCHAR(256), nullable=False, comment=u"设备名称")
    en_mea_point_n = Column(VARCHAR(50), nullable=False, comment=u"测点名称")
    en_remarks = Column(VARCHAR(256), nullable=True, comment=u"备注")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'name':'%s','t_eq_id':'%s','num':'%s','mea_point_n':%s,'type_':'%s','remarks':'%s','is_use':'%s','station':%s,'en_name':'%s','en_mea_point_n':'%s','en_remarks':'%s'}" % (
            self.id, self.name, self.t_eq_id, self.num, self.mea_point_n, self.type_, self.remarks, self.is_use,
            self.station, self.en_name, self.en_mea_point_n, self.en_remarks)
        return bean.replace("None", '')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}
   
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        
        bean =  "{'id':%s,'name':'%s','t_eq_id':'%s','num':'%s','mea_point_n':%s,'type_':'%s','remarks':'%s','is_use':'%s','station':%s}" % (
            self.id,self.name,self.t_eq_id,self.num,self.mea_point_n,self.type_,self.remarks,self.is_use,self.station)
        return bean.replace("None", '')
