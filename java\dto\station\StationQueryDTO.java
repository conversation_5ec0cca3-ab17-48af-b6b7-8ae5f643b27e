package com.robestec.analysis.dto.station;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电站查询DTO
 */
@Data
@ApiModel("电站查询DTO")
public class StationQueryDTO {

    @ApiModelProperty("英文名称")
    private String name;

    @ApiModelProperty("中文名称")
    private String descr;

    @ApiModelProperty("是否注册: 1-是, 0-否")
    private Integer register;

    @ApiModelProperty("索引")
    private Integer index;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
