package com.robestec.analysis.dto.tpowerdeliverrecords;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 功率计划下发记录查询DTO
 */
@Data
@ApiModel("功率计划下发记录查询DTO")
public class TPowerDeliverRecordsQueryDTO {

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("英文任务名称")
    private String enName;

    @ApiModelProperty("联系人电话")
    private String mobile;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("计划类型: 1-自定义, 2-周期性, 3-节假日")
    private Integer planType;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
