package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsCreateDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsQueryDTO;
import com.robestec.analysis.dto.tpowerdeliverrecords.TPowerDeliverRecordsUpdateDTO;
import com.robestec.analysis.entity.TPowerDeliverRecords;
import com.robestec.analysis.mapper.TPowerDeliverRecordsMapper;
import com.robestec.analysis.service.TPowerDeliverRecordsService;
import com.robestec.analysis.vo.TPowerDeliverRecordsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率计划下发记录服务实现类
 */
@Slf4j
@Service
public class TPowerDeliverRecordsServiceImpl extends SuperServiceImpl<TPowerDeliverRecordsMapper, TPowerDeliverRecords>
        implements TPowerDeliverRecordsService {

    @Override
    public PageResult<TPowerDeliverRecordsVO> queryTPowerDeliverRecords(TPowerDeliverRecordsQueryDTO queryDTO) {
        LambdaQueryWrapper<TPowerDeliverRecords> wrapper = new LambdaQueryWrapper<TPowerDeliverRecords>()
                .like(StringUtils.hasText(queryDTO.getName()), TPowerDeliverRecords::getName, queryDTO.getName())
                .like(StringUtils.hasText(queryDTO.getEnName()), TPowerDeliverRecords::getEnName, queryDTO.getEnName())
                .like(StringUtils.hasText(queryDTO.getMobile()), TPowerDeliverRecords::getMobile, queryDTO.getMobile())
                .eq(queryDTO.getUserId() != null, TPowerDeliverRecords::getUserId, queryDTO.getUserId())
                .like(StringUtils.hasText(queryDTO.getUserName()), TPowerDeliverRecords::getUserName, queryDTO.getUserName())
                .eq(queryDTO.getPlanType() != null, TPowerDeliverRecords::getPlanType, queryDTO.getPlanType())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), TPowerDeliverRecords::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), TPowerDeliverRecords::getCreateTime, queryDTO.getEndTime())
                .orderByDesc(TPowerDeliverRecords::getCreateTime);

        Page<TPowerDeliverRecords> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TPowerDeliverRecords> result = this.page(page, wrapper);

        List<TPowerDeliverRecordsVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPowerDeliverRecordsVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTPowerDeliverRecords(TPowerDeliverRecordsCreateDTO createDTO) {
        // 检查名称是否重复
        long count = this.count(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getName, createDTO.getName())
                .eq(TPowerDeliverRecords::getUserId, createDTO.getUserId()));
        if (count > 0) {
            throw new RuntimeException("计划名称已存在");
        }

        TPowerDeliverRecords entity = BeanUtil.copyProperties(createDTO, TPowerDeliverRecords.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTPowerDeliverRecords(TPowerDeliverRecordsUpdateDTO updateDTO) {
        TPowerDeliverRecords entity = BeanUtil.copyProperties(updateDTO, TPowerDeliverRecords.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTPowerDeliverRecords(Long id) {
        this.removeById(id);
    }

    @Override
    public TPowerDeliverRecordsVO getTPowerDeliverRecords(Long id) {
        TPowerDeliverRecords entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTPowerDeliverRecordsList(List<TPowerDeliverRecordsCreateDTO> createDTOList) {
        List<TPowerDeliverRecords> entityList = BeanUtil.copyToList(createDTOList, TPowerDeliverRecords.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<TPowerDeliverRecordsVO> getTPowerDeliverRecordsByUserId(Long userId) {
        List<TPowerDeliverRecords> entityList = this.list(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getUserId, userId)
                .orderByDesc(TPowerDeliverRecords::getCreateTime));
        return BeanUtil.copyToList(entityList, TPowerDeliverRecordsVO.class);
    }

    @Override
    public List<TPowerDeliverRecordsVO> getTPowerDeliverRecordsByPlanType(Integer planType) {
        List<TPowerDeliverRecords> entityList = this.list(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getPlanType, planType)
                .orderByDesc(TPowerDeliverRecords::getCreateTime));
        return BeanUtil.copyToList(entityList, TPowerDeliverRecordsVO.class);
    }

    @Override
    public Long countByUserId(Long userId) {
        return this.count(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getUserId, userId));
    }

    /**
     * 转换为VO对象
     */
    private TPowerDeliverRecordsVO convertToVO(TPowerDeliverRecords entity) {
        if (entity == null) {
            return null;
        }
        TPowerDeliverRecordsVO vo = BeanUtil.copyProperties(entity, TPowerDeliverRecordsVO.class);
        vo.setPlanTypeName(getPlanTypeName(entity.getPlanType()));
        return vo;
    }

    /**
     * 获取计划类型名称
     */
    private String getPlanTypeName(Integer planType) {
        if (planType == null) {
            return "";
        }
        switch (planType) {
            case 1:
                return "自定义";
            case 2:
                return "周期性";
            case 3:
                return "节假日";
            default:
                return "未知";
        }
    }

    // ==================== 从Mapper迁移的方法 ====================

    /**
     * 分页查询功率计划下发记录（复杂关联查询）
     * 对应Python中PowerPlanList方法的查询逻辑
     */
    public PageResult<TPowerDeliverRecordsVO> selectPowerPlanList(
            Integer pageNum, Integer pageSize, String name, Integer status,
            Integer planType, String startTime, String endTime) {

        // 使用MyBatis-Plus实现复杂关联查询
        Page<TPowerDeliverRecords> page = new Page<>(pageNum, pageSize);

        // 子查询：获取每个power_id的最新记录
        LambdaQueryWrapper<TPowerDeliverRecords> wrapper = new LambdaQueryWrapper<TPowerDeliverRecords>()
                .eq(TPowerDeliverRecords::getIsUse, 1)
                .like(StringUtils.hasText(name), TPowerDeliverRecords::getName, name)
                .eq(planType != null, TPowerDeliverRecords::getPlanType, planType)
                .orderByDesc(TPowerDeliverRecords::getId);

        Page<TPowerDeliverRecords> result = this.page(page, wrapper);

        List<TPowerDeliverRecordsVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<TPowerDeliverRecordsVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    /**
     * 根据名称和用户ID查询是否存在重复记录
     * 对应Python中的重复性检查逻辑
     */
    public int countByNameAndUserId(String name, Long userId) {
        return Math.toIntExact(this.count(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getName, name)
                .eq(TPowerDeliverRecords::getUserId, userId)
                .eq(TPowerDeliverRecords::getIsUse, 1)));
    }

    /**
     * 根据名称和用户ID查询记录
     * 对应Python中的重复性检查逻辑
     */
    public TPowerDeliverRecords selectByNameAndUserId(String name, Long userId) {
        return this.getOne(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getName, name)
                .eq(TPowerDeliverRecords::getUserId, userId)
                .eq(TPowerDeliverRecords::getIsUse, 1));
    }

    /**
     * 根据ID和用户ID查询记录
     * 对应Python中的权限验证查询
     */
    public TPowerDeliverRecords selectByIdAndUserId(Long id, Long userId) {
        return this.getOne(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getId, id)
                .eq(TPowerDeliverRecords::getUserId, userId)
                .eq(TPowerDeliverRecords::getIsUse, 1));
    }

    /**
     * 软删除记录
     * 对应Python中的删除逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean softDeleteById(Long id) {
        return this.update(Wrappers.<TPowerDeliverRecords>lambdaUpdate()
                .eq(TPowerDeliverRecords::getId, id)
                .set(TPowerDeliverRecords::getIsUse, 0));
    }

    /**
     * 根据用户ID查询记录列表
     */
    public List<TPowerDeliverRecords> selectByUserId(Long userId) {
        return this.list(Wrappers.<TPowerDeliverRecords>lambdaQuery()
                .eq(TPowerDeliverRecords::getUserId, userId)
                .eq(TPowerDeliverRecords::getIsUse, 1)
                .orderByDesc(TPowerDeliverRecords::getCreateTime));
    }

    /**
     * 更新记录信息
     * 对应Python中PowerPlanUpdate方法的更新逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePowerPlan(Long id, String name, String powerList,
                                 String stationList, Long userId, String userName, Integer planType) {
        return this.update(Wrappers.<TPowerDeliverRecords>lambdaUpdate()
                .eq(TPowerDeliverRecords::getId, id)
                .set(TPowerDeliverRecords::getName, name)
                .set(TPowerDeliverRecords::getPowerList, powerList)
                .set(TPowerDeliverRecords::getStationList, stationList)
                .set(TPowerDeliverRecords::getUserId, userId)
                .set(TPowerDeliverRecords::getUserName, userName)
                .set(TPowerDeliverRecords::getPlanType, planType));
    }
}
