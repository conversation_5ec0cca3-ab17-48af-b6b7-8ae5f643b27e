import datetime
import decimal
import re
import time

from django.conf import settings
import requests
from django.db import connections
from rest_framework.response import Response

from apis.user.models import MeterUseTime
from common import common_response_code
from apis.user import models
from common.database_pools import dwd_tables, dwd_db_tool, ads_db_tool
from settings.meter_settings import METER_DIC
from decimal import Decimal

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


class SocV3:
    """SOC"""

    def __init__(self, time_stamp):
        """
        SOC计算
        :param time_stamp: datetime对象
        """
        # self.url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
        self.time_stamp = time_stamp
        self.time_ins = time_stamp.strftime("%Y-%m-%d")

    def get_v3_request(self, unit):
        station_english_name = unit.station.english_name
        # station_app = unit.station.app
        bms = unit.bms

        # time_stamp = self.time_ins.timestamp()
        # url = self.url

        # request_json = {
        #     "time": str(int(time_stamp)),
        #     "datatype": "measure",
        #     "app": station_app,
        #     "station": station_english_name,
        #     "body": [{"device": bms, "body": ["SOC"]}],  # SOC
        # }
        # response = requests.post(url=url, json=request_json)
        # datas = response.json()["datas"]

        select_sql = f'select soc, time, station_name from {dwd_tables["measure"]["bms_3"]} where station_name=%s and device=%s and time >= %s and time <= %s order by time ASC'

        results = dwd_db_tool.select_many(select_sql, *(station_english_name, bms, self.time_ins + ' 00:00:00', self.time_ins + ' 23:59:59'))

        return results

    def get_unit(self, unit_instance):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        detail_ins = models.PeakValley.objects.filter(province=province_ins, year_month=self.time_stamp.month, level=1).first()
        results = self.get_v3_request(unit_instance)
        detail = []
        if results:
            for i in range(0, len(results)):
                signal = results[i]["time"].hour
                detail.append(
                    {
                        "SOC": float(results[i]['soc']),
                        "time": results[i]['time'].strftime('%H:%M'),
                        "type": getattr(detail_ins, f"pv{signal}"),
                    }
                )

        return detail

    def get_stations(self, station):
        """
        获取站的SOC
        :param station: 站模型对象
        :return:
        """
        units_inst = models.Unit.objects.filter(is_delete=0, station=station).all()
        province_ins = station.province
        detail_ins = models.PeakValley.objects.filter(province=province_ins, year_month=self.time_stamp.month).first()
        detail = []
        for unit in units_inst:
            results = self.get_v3_request(unit)
            if results:
                for i in range(0, len(results)):
                    try:
                        detail[i]['SOC'] += float(results[i]['soc'])/len(units_inst)
                    except:
                        signal = results[i]["time"].hour
                        detail.append(
                            {
                                "SOC": float(results[i]['soc'])/len(units_inst),
                                "time": results[i]['time'].strftime('%H:%M'),
                                "type": getattr(detail_ins, f"pv{signal}"),
                            }
                        )

                # new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
                # signal = 0
                # for i in range(0, len(datas) if len(datas) <= 48 else 48):
                #     try:
                #         last_soc = new_list_[i]["body"]["data"][0]["SOC"]
                #     except Exception as e:
                #         success_log.info("soc: 该天soc不存在数据 soc默认取 0")
                #         last_soc = 0
                #     try:
                #         detail[i]["SOC"] += decimal.Decimal(decimal.Decimal(last_soc) / len(units_inst)).quantize(decimal.Decimal("0.00"))
                #     except Exception as e:
                #         date_string = datetime.datetime.fromtimestamp(new_list_[i]["body"]["time"])
                #         formatted_time = date_string.strftime("%H:%M:%S")
                #         detail.append(
                #             {
                #                 "SOC": decimal.Decimal(decimal.Decimal(last_soc) / len(units_inst)).quantize(decimal.Decimal("0.00")),
                #                 "time": formatted_time,
                #                 "type": getattr(detail_ins, f"pv{signal}"),
                #             }
                #         )
                #     if signal != 0 and signal % 2 == 0:
                #         signal += 1
        # detail[i]["soc"] = abs(detail[i]["soc"])
        return detail


# class NewSocV3:
#     """SOC"""
#
#     def __init__(self, time_stamp):
#         """
#         SOC计算
#         :param time_stamp: datetime对象
#         """
#         self.url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#         self.time_ins = time_stamp
#
#     def get_v3_request(self, unit):
#         station_english_name = unit.station.english_name
#         station_app = unit.station.app
#         bms = unit.bms
#
#         time_stamp = self.time_ins.timestamp()
#         url = self.url
#
#         request_json = {
#             "time": str(int(time_stamp)),
#             "datatype": "measure",
#             "app": station_app,
#             "station": station_english_name,
#             "body": [{"device": bms, "body": ["SOC"]}],  # SOC
#         }
#         response = requests.post(url=url, json=request_json)
#         datas = response.json()["datas"]
#         return datas
#
#     def get_unit(self, unit_instance):
#         """
#         获取单元的冲放电量
#         :param unit: 单元模型对象
#         :return:
#         """
#
#         station_ins = unit_instance.station
#         province_ins = station_ins.province
#         detail_ins = models.PeakValley.objects.filter(province=province_ins, year_month=self.time_ins.month, level=1).first()
#         datas = self.get_v3_request(unit_instance)
#         detail = []
#         if datas:
#             new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#             signal = 0
#
#             for i in range(0, len(datas) if len(datas) <= 48 else 48):
#                 try:
#                     last_soc = new_list_[i]["body"]["data"][0]["SOC"]
#                 except Exception as e:
#                     success_log.info("soc: 该天soc不存在数据 soc默认取 0")
#                     last_soc = 0
#
#                 try:
#                     detail[i]["SOC"] += decimal.Decimal(last_soc)
#                     detail[i]["SOC"] = str(detail[i]["SOC"])
#                 except Exception as e:
#                     # date_string = datetime.datetime.fromtimestamp(str(new_list_[i]["timeHour"]), '%Y-%m-%d %H:%M:%S')
#                     date_string = datetime.datetime.fromtimestamp(new_list_[i]["body"]["time"])
#                     formatted_time = date_string.strftime("%H:%M")
#                     detail.append(
#                         {
#                             "SOC": str(round(float(last_soc), 1)),
#                             "time": formatted_time,
#                             "type": getattr(detail_ins, f"pv{signal}"),
#                         }
#                     )
#
#                 if signal != 0 and signal % 2 == 0:
#                     signal += 1
#
#             # detail[i]["soc"] = abs(detail[i]["soc"])
#         return detail
#
#     def get_stations(self, station):
#         """
#         获取站的SOC
#         :param station: 站模型对象
#         :return:
#         """
#         units_inst = models.Unit.objects.filter(is_delete=0, station=station).all()
#         province_ins = station.province
#         detail_ins = models.PeakValley.objects.filter(province=province_ins, year_month=self.time_ins.month).first()
#         detail = []
#         for unit in units_inst:
#             datas = self.get_v3_request(unit)
#             if datas:
#                 new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
#                 signal = 0
#                 for i in range(0, len(datas) if len(datas) <= 48 else 48):
#                     try:
#                         last_soc = new_list_[i]["body"]["data"][0]["SOC"]
#                     except Exception as e:
#                         success_log.info("soc: 该天soc不存在数据 soc默认取 0")
#                         last_soc = 0
#                     try:
#                         detail[i]["SOC"] += decimal.Decimal(decimal.Decimal(last_soc) / len(units_inst)).quantize(decimal.Decimal("0.00"))
#                     except Exception as e:
#                         date_string = datetime.datetime.fromtimestamp(new_list_[i]["body"]["time"])
#                         formatted_time = date_string.strftime("%H:%M:%S")
#                         detail.append(
#                             {
#                                 "SOC": decimal.Decimal(decimal.Decimal(last_soc) / len(units_inst)).quantize(decimal.Decimal("0.00")),
#                                 "time": formatted_time,
#                                 "type": getattr(detail_ins, f"pv{signal}"),
#                             }
#                         )
#                     if signal != 0 and signal % 2 == 0:
#                         signal += 1
#         # detail[i]["soc"] = abs(detail[i]["soc"])
#         return detail


class ChargeDischargeCount:
    """
    冲放电量,
    """

    def __init__(self, time_stamp=datetime.datetime.now()):
        """
        冲放电量计算
        :param time_stamp: datetime对象
        """
        # self.url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
        self.time_ins = time_stamp
        self.time_str = time_stamp.strftime("%Y-%m-%d")

    def get_v3_request(self, unit):
        station_english_name = unit.station.english_name
        station_app = unit.station.app
        bms = unit.bms
        meter_type = unit.station.meter_type
        meter_dic = METER_DIC.get(meter_type)
        charge = meter_dic.get("charge")
        discharge = meter_dic.get("discharge")
        device = meter_dic.get("device")
        bms_ = getattr(unit, device)

        # time_stamp = self.time_ins.timestamp()
        # url = self.url
        # request_json = {
        #     "time": str(int(time_stamp)),
        #     "datatype": "cumulant",
        #     "app": station_app,
        #     "station": station_english_name,
        #     "body": [{"device": bms_, "body": [charge, discharge]}],  # 充电量  # 放电量
        # }
        #
        # response = requests.post(url=url, json=request_json)
        # datas = response.json()["datas"]

        select_sql = f'select {charge.lower()}, {discharge.lower()}, time, station_name from {dwd_tables["cumulant"][device]} where station_name=%s and device=%s and time >= %s and time <= %s order by time ASC'

        results = dwd_db_tool.select_many(select_sql, *(station_english_name, bms_, self.time_str + ' 00:00:00', self.time_str + ' 23:59:59'))

        return results, charge, discharge

    def get_v3_request_(self, unit):
        station_english_name = unit.station.english_name
        # station_app = unit.station.app
        bms = unit.bms
        meter_type = unit.station.meter_type
        meter_dic = METER_DIC.get(meter_type)
        device = meter_dic.get("device")
        bms_ = getattr(unit, device)

        # time_stamp = self.time_ins.timestamp()
        # url = self.url
        # request_json_ = {
        #     "time": str(int(time_stamp)),
        #     "datatype": "measure",
        #     "app": station_app,
        #     "station": station_english_name,
        #     "body": [{"device": bms, "body": ["SOC"]}],  # SOC
        # }
        #
        # response_ = requests.post(url=url, json=request_json_)
        # datas_ = response_.json()["datas"]

        select_sql = f'select soc, time, station_name from {dwd_tables["cumulant"]["bms"]} where station_name=%s and device=%s  and time >= %s and time <= %s order by time ASC'

        results = dwd_db_tool.select_many(select_sql, *(station_english_name, bms, self.time_str + ' 00:00:00', self.time_str + ' 23:59:59'))

        return results

    def get_unit(self, unit_instance):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        type_ = station_ins.type
        level_ = station_ins.level
        detail_ins = models.PeakValley.objects.get(
            province=province_ins,
            year_month=self.time_ins.month,
            level=level_,
            type=type_,
        )
        detail = []
        f_reports = models.FReport.objects.filter(station=station_ins.english_name, station_type=2,
                                                  unit_name=unit_instance.bms, day=self.time_ins.date()).order_by('hour')
        if f_reports.exists():
            for i in f_reports:
                temp_dict = {
                                "charge": round(float(i.chag), 1),
                                "discharge": round(float(i.disg), 1),
                                "soc": round(abs(float(i.soc)), 1),
                                "soc_diff": round(abs(float(i.soc)), 1),
                                "time": i.day + ' ' + i.hour + ":00:00",
                                "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                            }
                detail.append(temp_dict)
        detail = sorted(detail, key=lambda x: x['time'])
        return detail

    def new_get_unit_by_day(self, unit_instance):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        type_ = station_ins.type
        level_ = station_ins.level

        select_sql = ("SELECT day, chag as charge, disg as discharge, chag_soc as chag_soc, disg_soc as disg_soc FROM ads_report_chag_disg_1d"
                      " where station=%s and unit_name=%s and station_type=2 and day=%s")

        detail_ins = models.PeakValley.objects.get(
            province=province_ins,
            year_month=self.time_ins.month,
            level=level_,
            type=type_,
        )
        detail = []
        # f_reports = models.FReport.objects.filter(station=station_ins.english_name, station_type=2,
        #                                           unit_name=unit_instance.bms, day=self.time_ins.date()).order_by('hour')
        # if f_reports.exists():
        #     for i in f_reports:
        #         temp_dict = {
        #                         "charge": round(float(i.chag), 1),
        #                         "discharge": round(float(i.disg), 1),
        #                         "soc": round(abs(float(i.soc)), 1),
        #                         "soc_diff": round(abs(float(i.soc)), 1),
        #                         "time": i.day + ' ' + i.hour + ":00:00",
        #                         "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
        #                     }
        #         detail.append(temp_dict)

        i = ads_db_tool.select_one(select_sql, unit_instance.station.english_name, unit_instance.bms, self.time_ins.date())
        if i:
            temp_dict = {
                "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
                "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
                "chag_soc": round(abs(float(i['chag_soc'])), 1) if i['chag_soc'] is not None else '--',
                "disg_soc": round(abs(float(i['disg_soc'])), 1) if i['disg_soc'] is not None else '--',
                "day": i['day'].strftime("%Y-%m-%d")
            }

            return temp_dict
        else:
            return None

    def new_get_unit(self, unit_instance, start_date, end_date):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        type_ = station_ins.type
        level_ = station_ins.level

        select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
                      " where station=%s and unit_name=%s and station_type=2 and day>=%s and day<=%s")

        detail_ins = models.PeakValley.objects.get(
            province=province_ins,
            year_month=self.time_ins.month,
            level=level_,
            type=type_,
        )
        detail = []
        # f_reports = models.FReport.objects.filter(station=station_ins.english_name, station_type=2,
        #                                           unit_name=unit_instance.bms, day=self.time_ins.date()).order_by('hour')
        # if f_reports.exists():
        #     for i in f_reports:
        #         temp_dict = {
        #                         "charge": round(float(i.chag), 1),
        #                         "discharge": round(float(i.disg), 1),
        #                         "soc": round(abs(float(i.soc)), 1),
        #                         "soc_diff": round(abs(float(i.soc)), 1),
        #                         "time": i.day + ' ' + i.hour + ":00:00",
        #                         "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
        #                     }
        #         detail.append(temp_dict)

        results = ads_db_tool.select_many(select_sql, unit_instance.station.english_name, unit_instance.bms, start_date, end_date)
        if results:
            for i in results:
                temp_dict = {
                    "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
                    "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
                    "soc": round(abs(float(i['soc'])), 1) if i['soc'] is not None else '--',
                    "soc_diff": round(abs(float(i['soc'])), 1) if i['soc'] is not None else '--',
                    "time": i['day'].strftime("%Y-%m-%d") + ' ' + i['hour'] + ":00:00",
                    "type": getattr(detail_ins, "pv" + i['hour'][1:] if i['hour'].startswith('0') else "pv" + i['hour'])
                }
                detail.append(temp_dict)

        return detail

    def get_stations(self, station):
        """
        获取主站的冲放电量
        :param station: 站模型对象
        :return:
        """
        units_inst = models.Unit.objects.filter(is_delete=0, station__master_station=station).all()

        detail = []
        temp_array = []
        for unit in units_inst:
            temp_detail = []
            province_ins = unit.station.province
            type_ = unit.station.type
            level_ = unit.station.level
            detail_ins = models.PeakValley.objects.get(
                province=province_ins,
                year_month=self.time_ins.month,
                type=type_,
                level=level_,
            )

            f_reports = models.FReport.objects.filter(station=unit.station.english_name, station_type=2,
                                                      unit_name=unit.bms, day=self.time_ins.date()).order_by(
                'hour')
            if f_reports.exists():
                for i in f_reports:
                    temp_dict = {
                        "charge": round(float(i.chag), 1),
                        "discharge": round(float(i.disg), 1),
                        "soc": round(abs(float(i.soc_2)) / len(units_inst), 1),
                        "soc_diff": round(abs(float(i.soc_2)) / len(units_inst), 1),
                        "time": i.day + ' ' + i.hour + ":00:00",
                        "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                    }
                    temp_detail.append(temp_dict)
            temp_array.append(temp_detail)

        if len(temp_array):
            for list_ in temp_array:
                if not detail:
                    detail = list_
                else:
                    for ind, item in enumerate(list_):
                        detail[ind]['charge'] += item['charge']
                        detail[ind]['discharge'] += item['discharge']
                        detail[ind]['soc'] += item['soc']
                        detail[ind]['soc_diff'] += item['soc_diff']
        return detail

    def new_get_stations(self, master_station, target_day):
        """
        获取主站的冲放电量
        :param station: 站模型对象
        :return:
        """
        # stations = models.StationDetails.objects.filter(master_station=master_station).all()

        detail = []
        temp_array = []

        # 改查新综合过结算表和计量表的新1h冻结表：表名未定
        select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_union_1h"
                      " where station=%s and station_type<=1 and day=%s")

        # select_sql_account = (
        #     "SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_ems_chag_disg_1h"
        #     " where station=%s and day>=%s and day<=%s")

        # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
        # meter_use_time = MeterUseTime.objects.filter(station=master_station, is_use=1).first()
        # is_use_account = (master_station.is_account and meter_use_time and start_date >=
        #                   meter_use_time.start_time.date() and end_date <= meter_use_time.end_time.date())

        # if not is_use_account:
        stations = master_station.stationdetails_set.filter(is_delete=0).all()
        for station in stations:
            temp_detail = []
            province_ins = station.province
            type_ = station.type
            level_ = station.level
            detail_ins = models.PeakValleyNew.objects.filter(
                province=province_ins,
                year_month=self.time_ins.strftime("%Y-%m"),
                type=type_,
                level=level_,
            )
            results = ads_db_tool.select_many(select_sql, station.english_name, target_day)
            if results:
                for i in results:
                    hour = i['hour']
                    hour_str = f"0{hour}:00" if hour<10 else f"{hour}:00"
                    detail_ins_ = detail_ins.filter(moment=hour_str).first()
                    temp_dict = {
                                "charge": round(float(i['charge']), 1) if i['charge'] is not None else i['charge'],
                                "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else i['discharge'],
                                "soc": round(abs(float(i['soc'])), 1 if i['soc'] is not None else i['soc']),
                                "soc_diff": round(abs(float(i['soc'])), 1) if i['soc'] is not None else i['soc'],
                                "time": i['day'].strftime("%Y-%m-%d") + ' ' + (str(i['hour']) if i['hour'] > 9 else '0'+ str(i['hour'])) + ":00:00",
                                # "type": getattr(detail_ins, "pv" + i['hour'][1:] if i['hour'].startswith('0') else "pv" + i['hour'])
                                "type": detail_ins_.pv if detail_ins_ else '--'
                            }
                    # hour 字段的类型不一致：上表是字符串，下表是数字
                    temp_detail.append(temp_dict)
            if temp_detail:
                temp_array.append(temp_detail)

        if len(temp_array):
            detail = get_common_results_2_from_ads_report_chag_disg_data(temp_array)

        # else:           # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
        #     station = master_station.stationdetails_set.filter(english_name=master_station.english_name).first()
        #     province_ins = station.province
        #     type_ = station.type
        #     level_ = station.level
        #     detail_ins = models.PeakValley.objects.get(
        #         province=province_ins,
        #         year_month=self.time_ins.month,
        #         type=type_,
        #         level=level_,
        #     )
        #
        #     results = ads_db_tool.select_many(select_sql_account, station.english_name, start_date, end_date)
        #     if results:
        #         for i in results:
        #             temp_dict = {
        #                 "charge": round(float(i['charge']), 1) if i['charge'] is not None else i['charge'],
        #                 "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else i[
        #                     'discharge'],
        #                 "soc": round(abs(float(i['soc'])), 1 if i['soc'] is not None else i['soc']),
        #                 "soc_diff": round(abs(float(i['soc'])), 1) if i['soc'] is not None else i['soc'],
        #                 "time": i['day'].strftime("%Y-%m-%d") + ' ' + (
        #                     str(i['hour']) if i['hour'] >= 10 else '0' + str(i['hour'])) + ":00:00",
        #                 "type": getattr(detail_ins, f"pv{i['hour']}")
        #             }
        #             temp_array.append(temp_dict)
        #     detail = temp_array

        detail = sorted(detail, key=lambda k: k['time'])
        return detail

    def new_get_stations_by_day(self, m_station):
        """
        获取主站的冲放电量
        :param station: 站模型对象
        :return:
        """

        detail = {}

        temp_array = []

        select_sql = ("SELECT day, chag as charge, disg as discharge, chag_soc as chag_soc, disg_soc as disg_soc FROM ads_report_chag_disg_1d"
                      " where station=%s and station_type=1 and day=%s")

        select_sql_account = (
            "SELECT day, chag as charge, disg as discharge, chag_soc as chag_soc, disg_soc as disg_soc FROM ads_report_ems_chag_disg_1d"
            " where station=%s and day=%s")

        # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
        meter_use_time = MeterUseTime.objects.filter(station=m_station, is_use=1).first()
        is_use_account = (m_station.is_account and meter_use_time and
                          meter_use_time.start_time.date() <= self.time_ins.date() <= meter_use_time.end_time.date())

        if not is_use_account:
            stations_inst = m_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
            for station in stations_inst:
                temp_detail = []
                province_ins = station.province
                type_ = station.type
                level_ = station.level
                # detail_ins = models.PeakValley.objects.get(
                #     province=province_ins,
                #     year_month=self.time_ins.month,
                #     type=type_,
                #     level=level_,
                # )

                # f_reports = models.FReport.objects.filter(station=unit.station.english_name, station_type=2,
                #                                           unit_name=unit.bms, day=self.time_ins.date()).order_by(
                #     'hour')
                # if f_reports.exists():
                #     for i in f_reports:
                #         temp_dict = {
                #             "charge": round(float(i.chag), 1),
                #             "discharge": round(float(i.disg), 1),
                #             "soc": round(abs(float(i.soc_2)) / len(units_inst), 1),
                #             "soc_diff": round(abs(float(i.soc_2)) / len(units_inst), 1),
                #             "time": i.day + ' ' + i.hour + ":00:00",
                #             "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                #         }
                #         temp_detail.append(temp_dict)
                # temp_array.append(temp_detail)

                i = ads_db_tool.select_one(select_sql, station.english_name, self.time_ins.date())

                if i:
                    temp_dict = {
                                "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
                                "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
                                "chag_soc": round(abs(float(i['chag_soc'])), 1) if i['chag_soc'] is not None else '--',
                                "disg_soc": round(abs(float(i['disg_soc'])), 1) if i['disg_soc'] is not None else '--',
                                "day": i['day'].strftime("%Y-%m-%d")
                    }
                    temp_array.append(temp_dict)

            if len(temp_array) == len(stations_inst):
                # for list_ in temp_array:
                #     if not detail:
                #         detail = list_
                #     else:
                #         for ind, item in enumerate(list_):
                #             detail[ind]['charge'] += item['charge']
                #             detail[ind]['discharge'] += item['discharge']
                #             detail[ind]['soc'] += item['soc']
                #             detail[ind]['soc_diff'] += item['soc_diff']

                # detail = get_common_results_2_from_ads_report_chag_disg_data(temp_array)
                # detail = sorted(detail, key=lambda k: k['time'])

                detail = {
                    "charge": round(sum([i['charge'] for i in temp_array]), 2) if not '--' in [i['charge'] for i in temp_array] else '--',
                    "discharge": round(sum(i['discharge'] for i in temp_array), 2) if not '--' in [i['discharge'] for i in temp_array] else '--',
                    "chag_soc": round(sum(i['chag_soc'] / len(stations_inst) for i in temp_array), 2) if not '--' in [i['chag_soc'] for i in temp_array] else '--',
                    "disg_soc": round(sum(i['disg_soc'] / len(stations_inst) for i in temp_array), 2) if not '--' in [i['disg_soc'] for i in temp_array] else '--',
                    "day": self.time_ins.date().strftime("%Y-%m-%d")
                }
        else:
            ems_station = m_station.stationdetails_set.filter(is_delete=0, english_name=m_station.english_name).first()
            i = ads_db_tool.select_one(select_sql_account, ems_station.english_name, self.time_ins.date())
            if i:
                detail = {
                    "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
                    "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
                    "chag_soc": round(abs(float(i['chag_soc'])), 1) if i['chag_soc'] is not None else '--',
                    "disg_soc": round(abs(float(i['disg_soc'])), 1) if i['disg_soc'] is not None else '--',
                    "day": i['day'].strftime("%Y-%m-%d")
                }
        return detail

    def get_projects(self, project):
        """
        获取项目的冲放电量
        :param station: 项目模型对象
        :return:
        """
        # stations_ins = models.StationDetails.objects.filter(project=project).all()
        master_stations = models.MaterStation.objects.filter(project=project, is_delete=0).all()
        detail = []
        charge_soc_diff_sum = 0
        discharge_soc_diff_sum = 0
        temp_array = []
        for station in master_stations:
            all_units = models.Unit.objects.filter(is_delete=0, station__project=project).all()
            units_inst = models.Unit.objects.filter(is_delete=0, station__master_station=station).all()

            for unit in units_inst:
                province_ins = unit.station.province
                type_ = unit.station.type
                level_ = unit.station.level
                detail_ins = models.PeakValley.objects.get(
                    province=province_ins,
                    year_month=self.time_ins.month,
                    type=type_,
                    level=level_,
                )

                unit_charge_soc_diff_sum = 0
                unit_discharge_soc_diff_sum = 0

                temp_detail = []
                province_ins = unit.station.province
                type_ = unit.station.type
                level_ = unit.station.level
                detail_ins = models.PeakValley.objects.get(
                    province=province_ins,
                    year_month=self.time_ins.month,
                    type=type_,
                    level=level_,
                )

                f_reports = models.FReport.objects.filter(station=unit.station.english_name, station_type=2,
                                                          unit_name=unit.bms, day=self.time_ins.date()).order_by(
                    'hour')
                if f_reports.exists():
                    for i in f_reports:
                        temp_dict = {
                            "charge": round(float(i.chag), 1),
                            "discharge": round(float(i.disg), 1),
                            "soc": round(abs(float(i.soc)) / len(units_inst), 1),
                            "soc_diff": round(abs(float(i.soc)) / len(units_inst), 1),
                            "time": i.day + ' ' + i.hour + ":00:00",
                            "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                        }
                        temp_detail.append(temp_dict)
                        if float(i.soc) > 0:
                            charge_soc_diff_sum += float(i.soc)
                        elif float(i.soc) < 0:
                            discharge_soc_diff_sum += float(i.soc)

                temp_array.append(temp_detail)

        if len(temp_array):
            for list_ in temp_array:
                if not detail:
                    detail = list_
                else:
                    for ind, item in enumerate(list_):
                        detail[ind]['charge'] += item['charge']
                        detail[ind]['discharge'] += item['discharge']
                        detail[ind]['soc'] += item['soc']
                        detail[ind]['soc_diff'] += item['soc_diff']

        return detail, charge_soc_diff_sum, discharge_soc_diff_sum

    # def new_get_projects(self, project):
    #     """
    #     获取项目的冲放电量
    #     :param station: 项目模型对象
    #     :return:
    #     """
    #     master_stations = models.MaterStation.objects.filter(project=project).all()
    #     detail = []
    #     temp_array = []
    #
    #     select_sql = (
    #         "SELECT day, chag as charge, disg as discharge, chag_soc as chag_soc, disg_soc as disg_soc FROM ads_report_chag_disg_1d"
    #         " where station=%s and station_type=1 and day=%s")
    #
    #     select_sql_account = (
    #         "SELECT day, chag as charge, disg as discharge, chag_soc as chag_soc, disg_soc as disg_soc FROM ads_report_ems_chag_disg_1d"
    #         " where station=%s and day=%s")
    #
    #     for master_station in master_stations:
    #         # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
    #         meter_use_time = MeterUseTime.objects.filter(station=master_station, is_use=1).first()
    #         is_use_account = (master_station.is_account and meter_use_time and
    #                           meter_use_time.start_time.date() <= self.time_ins.date() <= meter_use_time.end_time.date())
    #
    #         if not is_use_account:
    #             stations_inst = master_station.stationdetails_set.exclude(slave=0)
    #             temp_detail = []
    #             for station in stations_inst:
    #                 i = ads_db_tool.select_one(select_sql, station.english_name, self.time_ins.date())
    #
    #                 if i:
    #                     temp_dict = {
    #                         "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
    #                         "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
    #                         "chag_soc": round(abs(float(i['chag_soc'])), 1) if i['chag_soc'] is not None else '--',
    #                         "disg_soc": round(abs(float(i['disg_soc'])), 1) if i['disg_soc'] is not None else '--',
    #                         "day": i['day'].strftime("%Y-%m-%d")
    #                     }
    #                     temp_detail.append(temp_dict)
    #             if len(temp_detail) == len(stations_inst):
    #                 m_detail = {
    #                     "charge": round(sum([i['charge'] for i in temp_detail]), 2) if not '--' in [i['charge'] for i in temp_detail] else '--',
    #                     "discharge": round(sum(i['discharge'] for i in temp_detail), 2) if not '--' in [i['discharge'] for i in temp_detail] else '--',
    #                     "chag_soc": round(sum(i['chag_soc'] / len(stations_inst) for i in temp_detail), 2) if not '--' in [i['chag_soc'] for i in temp_detail] else '--',
    #                     "disg_soc": round(sum(i['disg_soc'] / len(stations_inst) for i in temp_detail), 2) if not '--' in [i['disg_soc'] for i in temp_detail] else '--',
    #                     "day": self.time_ins.date().strftime("%Y-%m-%d")
    #                 }
    #                 temp_array.append(m_detail)
    #         else:
    #             ems_station = master_station.stationdetails_set.filter(english_name=master_station.english_name).first()
    #             i = ads_db_tool.select_one(select_sql_account, ems_station.english_name, self.time_ins.date())
    #             if i:
    #                 temp_dict = {
    #                     "charge": round(float(i['charge']), 1) if i['charge'] is not None else '--',
    #                     "discharge": round(float(i['discharge']), 1) if i['discharge'] is not None else '--',
    #                     "chag_soc": round(abs(float(i['chag_soc'])), 1) if i['chag_soc'] is not None else '--',
    #                     "disg_soc": round(abs(float(i['disg_soc'])), 1) if i['disg_soc'] is not None else '--',
    #                     "day": i['day'].strftime("%Y-%m-%d")
    #                 }
    #                 temp_array.append(temp_dict)
    #
    #     if len(temp_array) == len(master_stations):
    #         detail = {
    #             "charge": round(sum([i['charge'] for i in temp_array]), 2) if not '--' in [i['charge'] for i in temp_array] else '--',
    #             "discharge": round(sum(i['discharge'] for i in temp_array), 2) if not '--' in [i['discharge'] for i in temp_array] else '--',
    #             "chag_soc": round(sum(i['chag_soc'] / len(master_stations) for i in temp_array), 2) if not '--' in [i['chag_soc'] for i in temp_array] else '--',
    #             "disg_soc": round(sum(i['disg_soc'] / len(master_stations) for i in temp_array), 2) if not '--' in [i['disg_soc'] for i in temp_array] else '--',
    #             "day": self.time_ins.date().strftime("%Y-%m-%d")
    #         }
    #
    #     return detail


class ChargeDischargeType:
    """
    冲放电量,
    """

    def __init__(self, time_stamp=datetime.date.today()):
        """
        冲放电量计算
        :param time_stamp: datetime对象
        """
        # self.url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
        self.time_ins = time_stamp

    # def get_v3_request(self, unit):
    #     """
    #     调取安昌 v3 接口
    #     :param unit:
    #     :return:
    #     """
    #     station_english_name = unit.station.english_name
    #     station_app = unit.station.app
    #     meter_type = unit.station.meter_type
    #     meter_dic = METER_DIC.get(meter_type)
    #     charge = meter_dic.get("charge")
    #     discharge = meter_dic.get("discharge")
    #     device = meter_dic.get("device")
    #     bms_ = getattr(unit, device)
    #
    #     time_stamp = self.time_ins.timestamp()
    #     url = self.url
    #     request_json = {
    #         "time": str(int(time_stamp)),
    #         "datatype": "cumulant",
    #         "app": station_app,
    #         "station": station_english_name,
    #         "body": [{"device": bms_, "body": [charge, discharge]}],  # 充电量  # 放电量
    #     }
    #
    #     response = requests.post(url=url, json=request_json)
    #     datas = response.json()["datas"]
    #     return datas, charge, discharge

    def get_unit(self, unit_instance):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        type_ = station_ins.type
        level_ = station_ins.level
        detail_ins = models.PeakValley.objects.get(
            province=province_ins,
            year_month=self.time_ins.month,
            level=level_,
            type=type_,
        )

        detail = []
        f_reports = models.FReport.objects.filter(station=station_ins.english_name, station_type=2,
                                                  unit_name=unit_instance.bms, day=self.time_ins.date()).order_by('hour')
        if f_reports.exists():
            for i in f_reports:
                temp_dict = {
                                "charge": round(float(i.chag), 2),
                                "discharge": round(float(i.disg), 2),
                                "time": i.day + ' ' + i.hour + ":00:00",
                                "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                            }
                detail.append(temp_dict)
        return detail

    def new_get_unit(self, unit_instance, start_date, end_date):
        """
        获取单元的冲放电量
        :param unit: 单元模型对象
        :return:
        """

        station_ins = unit_instance.station
        province_ins = station_ins.province
        type_ = station_ins.type
        level_ = station_ins.level

        select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
                      " where station=%s and unit_name=%s and station_type=2 and day>=%s and day<=%s")

        detail_ins = models.PeakValley.objects.get(
            province=province_ins,
            year_month=self.time_ins.month,
            level=level_,
            type=type_,
        )

        detail = []
        # f_reports = models.FReport.objects.filter(station=station_ins.english_name, station_type=2,
        #                                           unit_name=unit_instance.bms, day=self.time_ins.date()).order_by('hour')
        # if f_reports.exists():
        #     for i in f_reports:
        #         temp_dict = {
        #                         "charge": round(float(i.chag), 2),
        #                         "discharge": round(float(i.disg), 2),
        #                         "time": i.day + ' ' + i.hour + ":00:00",
        #                         "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
        #                     }
        #         detail.append(temp_dict)

        results = ads_db_tool.select_many(select_sql, unit_instance.station.english_name, unit_instance.bms,
                                          start_date, end_date)
        if results:
            for i in results:
                temp_dict = {
                    "charge": round(float(i['charge']), 2) if i['charge'] is not None else '--',
                    "discharge": round(float(i['discharge']), 2) if i['discharge'] is not None else '--',
                    "time": i['day'].strftime("%Y-%m-%d") + ' ' + i['hour'] + ":00:00",
                    "type": getattr(detail_ins, "pv" + i['hour'][1:] if i['hour'].startswith('0') else "pv" + i['hour'])
                }
                detail.append(temp_dict)

        return detail

    def get_stations(self, station):
        """
        获取站的冲放电量
        :param station: 站模型对象
        :return:
        """
        units_inst = models.Unit.objects.filter(is_delete=0, station__master_station=station).all()

        detail = []
        temp_array = []
        for unit in units_inst:
            temp_detail = []
            province_ins = unit.station.province
            type_ = unit.station.type
            level_ = unit.station.level
            detail_ins = models.PeakValley.objects.get(
                province=province_ins,
                year_month=self.time_ins.month,
                type=type_,
                level=level_,
            )

            f_reports = models.FReport.objects.filter(station=unit.station.english_name, station_type=2,
                                                      unit_name=unit.bms, day=self.time_ins.date()).order_by(
                'hour')
            if f_reports.exists():
                for i in f_reports:
                    temp_dict = {
                        "charge": float(i.chag),
                        "discharge": float(i.disg),
                        "time": i.day + ' ' + i.hour + ":00:00",
                        "type": getattr(detail_ins, "pv" + i.hour[1:] if i.hour.startswith('0') else "pv" + i.hour)
                    }
                    temp_detail.append(temp_dict)
            temp_array.append(temp_detail)

        if len(temp_array):
            for list_ in temp_array:
                if not detail:
                    detail = list_
                else:
                    for ind, item in enumerate(list_):
                        detail[ind]['charge'] += item['charge']
                        detail[ind]['discharge'] += item['discharge']
        return detail

    # def new_get_stations(self, m_station, start_date, end_date):
    #     """
    #     获取站的冲放电量
    #     :param station: 站模型对象
    #     :return:
    #     """
    #     stations = models.StationDetails.objects.filter(master_station=m_station).all()
    #
    #     # T改查新综合过结算表和计量表的新1d冻结表：表名未定
    #     select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
    #                   " where station=%s and station_type<=1 and day>=%s and day <= %s")
    #
    #     # select_sql_account = (
    #     #     "SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     #     " where station=%s and day>=%s and day<=%s")
    #
    #     detail = []
    #     temp_array = []
    #
    #     # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
    #     # meter_use_time = MeterUseTime.objects.filter(station=m_station, is_use=1).first()
    #     # is_use_account = (m_station.is_account and meter_use_time and start_date >=
    #     #                   meter_use_time.start_time.date() and end_date <= meter_use_time.end_time.date())
    #     #
    #     # if not is_use_account:
    #     stations = m_station.stationdetails_set.all()
    #
    #     for station in stations:
    #         temp_detail = []
    #         province_ins = station.province
    #         type_ = station.type
    #         level_ = station.level
    #         detail_ins = models.PeakValley.objects.get(
    #             province=province_ins,
    #             year_month=self.time_ins.month,
    #             type=type_,
    #             level=level_,
    #         )
    #
    #         results = ads_db_tool.select_many(select_sql, station.english_name, start_date, end_date)
    #         if results:
    #             for i in results:
    #                 temp_dict = {
    #                     "charge": round(float(i['charge']), 2),
    #                     "discharge": round(float(i['discharge']), 2),
    #                     "time": i['day'].strftime("%Y-%m-%d") + ' ' + i['hour'] + ":00:00",
    #                     # "type": getattr(detail_ins,
    #                     #                 "pv" + i['hour'][1:] if i['hour'].startswith('0') else "pv" + i['hour'])
    #                     "type": getattr(detail_ins, f"pv{i['hour']}")
    #                 }
    #                 temp_detail.append(temp_dict)
    #         temp_array.append(temp_detail)
    #
    #     if len(temp_array):
    #         detail = get_common_results_2_from_ads_report_chag_disg_data(temp_array)
    #
    #     # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
    #     # else:
    #     #     station = m_station.stationdetails_set.filter(english_name=m_station.english_name).first()
    #     #     province_ins = station.province
    #     #     type_ = station.type
    #     #     level_ = station.level
    #     #     detail_ins = models.PeakValley.objects.get(
    #     #         province=province_ins,
    #     #         year_month=self.time_ins.month,
    #     #         type=type_,
    #     #         level=level_,
    #     #     )
    #     #     results = ads_db_tool.select_many(select_sql_account, station.english_name, start_date, end_date)
    #     #     if results:
    #     #         for i in results:
    #     #             temp_dict = {
    #     #                 "charge": round(float(i['charge']), 2),
    #     #                 "discharge": round(float(i['discharge']), 2),
    #     #                 "time": i['day'].strftime("%Y-%m-%d") + ' ' + (
    #     #                     str(i['hour']) if i['hour'] >= 10 else '0' + str(i['hour'])) + ":00:00",
    #     #                 "type": getattr(detail_ins, f"pv{i['hour']}")
    #     #             }
    #     #             temp_array.append(temp_dict)
    #     #     detail = temp_array
    #
    #     detail = sorted(detail, key=lambda k: k['time'])
    #
    #     return detail


def classification(datas):
    """
    逐时冲放电量统计成峰平谷 (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")
    :param datas: 逐时冲放电量数据
    :return:
    """

    def except_zero(time_len):
        if time_len == 0:
            return 1
        return time_len

    return_dic = {
        "spike": {"total_charge": 0, "total_discharge": 0, "times": []},  # 尖峰
        "peak": {"total_charge": 0, "total_discharge": 0, "times": []},  # 峰
        "flat": {"total_charge": 0, "total_discharge": 0, "times": []},  # 平
        "valley": {"total_charge": 0, "total_discharge": 0, "times": []},  # 谷
    }

    for data in datas:
        if int(data["type"]) == 2:
            return_dic["spike"]["total_charge"] += round(Decimal(data["charge"]), 2)
            return_dic["spike"]["total_discharge"] += round(Decimal(data["discharge"]), 2)
            return_dic["spike"]["times"].append(data["time"])
        if int(data["type"]) == 1:
            return_dic["peak"]["total_charge"] += round(Decimal(data["charge"]), 2)
            return_dic["peak"]["total_discharge"] += round(Decimal(data["discharge"]), 2)
            return_dic["peak"]["times"].append(data["time"])
        if int(data["type"]) == 0:
            return_dic["flat"]["total_charge"] += round(Decimal(data["charge"]), 2)
            return_dic["flat"]["total_discharge"] += round(Decimal(data["discharge"]), 2)
            return_dic["flat"]["times"].append(data["time"])
        if int(data["type"]) == -1:
            return_dic["valley"]["total_charge"] += round(Decimal(data["charge"]), 2)
            return_dic["valley"]["total_discharge"] += round(Decimal(data["discharge"]), 2)
            return_dic["valley"]["times"].append(data["time"])
    return_dic["spike"]["charge_average"] = return_dic["spike"]["total_charge"] if return_dic["spike"]["total_charge"] >= 1 else 0
    return_dic["spike"]["discharge_average"] = return_dic["spike"]["total_discharge"] if return_dic["spike"]["total_discharge"] >= 1 else 0
    return_dic["peak"]["charge_average"] = return_dic["peak"]["total_charge"] if return_dic["peak"]["total_charge"] >= 1 else 0
    return_dic["peak"]["discharge_average"] = return_dic["peak"]["total_discharge"] if return_dic["peak"]["total_discharge"] >= 1 else 0
    return_dic["flat"]["charge_average"] = return_dic["flat"]["total_charge"] if return_dic["flat"]["total_charge"] >= 1 else 0
    return_dic["flat"]["discharge_average"] = return_dic["flat"]["total_discharge"] if return_dic["flat"]["total_discharge"] >= 1 else 0
    return_dic["valley"]["charge_average"] = return_dic["valley"]["total_charge"] if return_dic["valley"]["total_charge"] >= 1 else 0
    return_dic["valley"]["discharge_average"] = return_dic["valley"]["total_discharge"] if return_dic["valley"]["total_discharge"] >= 1 else 0
    return return_dic

def newclassification(datas):
    """
    逐时冲放电量统计成峰平谷 (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")
    :param datas: 逐时冲放电量数据
    :return:
    """

    def except_zero(time_len):
        if time_len == 0:
            return 1
        return time_len

    return_dic = {
        "spike": {"total_charge": 0, "total_discharge": 0, "time": []},  # 尖峰
        "peak": {"total_charge": 0, "total_discharge": 0, "time": []},  # 峰
        "flat": {"total_charge": 0, "total_discharge": 0, "time": []},  # 平
        "valley": {"total_charge": 0, "total_discharge": 0, "time": []},  # 谷
    }

    for data in datas:
        if int(data["type"]) == 2:
            return_dic["spike"]["total_charge"] += Decimal(data["charge"])
            return_dic["spike"]["total_discharge"] += Decimal(data["discharge"])
            return_dic["spike"]["time"].append(data["time"])
        if int(data["type"]) == 1:
            return_dic["peak"]["total_charge"] += Decimal(data["charge"])
            return_dic["peak"]["total_discharge"] += Decimal(data["discharge"])
            return_dic["peak"]["time"].append(data["time"])
        if int(data["type"]) == 0:
            return_dic["flat"]["total_charge"] += Decimal(data["charge"])
            return_dic["flat"]["total_discharge"] += Decimal(data["discharge"])
            return_dic["flat"]["time"].append(data["time"])
        if int(data["type"]) == -1:
            return_dic["valley"]["total_charge"] += Decimal(data["charge"])
            return_dic["valley"]["total_discharge"] += Decimal(data["discharge"])
            return_dic["valley"]["time"].append(data["time"])

    # 处理小于1的数据置零
    for k, v in return_dic.items():
        if v.get('total_charge') < 1:
            v['total_charge'] = 0
        else:
            v['total_charge'] = round(v['total_charge'], 1)
        if v.get('total_discharge') < 1:
            v['total_discharge'] = 0
        else:
            v['total_discharge'] = round(v['total_discharge'], 1)

    return_dic["spike"]["charge_average"] = return_dic["spike"]["total_charge"]
    return_dic["spike"]["discharge_average"] = return_dic["spike"]["total_discharge"]
    return_dic["peak"]["charge_average"] = return_dic["peak"]["total_charge"]
    return_dic["peak"]["discharge_average"] = return_dic["peak"]["total_discharge"]
    return_dic["flat"]["charge_average"] = return_dic["flat"]["total_charge"]
    return_dic["flat"]["discharge_average"] = return_dic["flat"]["total_discharge"]
    return_dic["valley"]["charge_average"] = return_dic["valley"]["total_charge"]
    return_dic["valley"]["discharge_average"] = return_dic["valley"]["total_discharge"]

    return return_dic


def get_type(type, datas):
    """
    削峰填谷筛选
    (-2, "深谷"), (-1, "谷"), (0, "平"), (1, "峰"), (2, "尖峰")
    :param self:
    :param type:
    :param datas:
    :return:
    """
    new_list = []
    for detail in datas:
        if int(detail["type"]) == int(type):
            new_list.append(detail)
        else:
            for key in detail.keys():
                if key == "time":
                    pass
                else:
                    detail[key] = "0"
            new_list.append(detail)
    return new_list


class GetHttpHistory:
    def __init__(self, start_time):
        self.start_time = start_time

    def v2_history_max_soc(self, end_time, station_app, station_english_name):
        """

        :param end_time: 结束时间
        :param station_app: app
        :param station_english_name:站名
        :return: 极差值
        """
        url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
        post_json = {
            "startTime": str(self.start_time),
            "endTime": str(end_time),
            "datatype": "measure",
            "app": station_app,
            "order": "DESC",
            "pageSize": 500,
            "station": station_english_name,
            "startPage": 1,
        }
        response = requests.post(url=url, json=post_json)
        total_list = []
        for i in range(len(response.json()["datas"]["list"])):
            d = re.search(
                '"SOC":"(.*?)"',
                response.json()["datas"]["list"][i]["dataInfo"],
            )
            total_list.append(float(d.group(1)))
        if total_list:
            return str(float(max(total_list)) - float(min(total_list)))
        else:
            return "0"


def get_project_sum_t_report_data(project, date_time):
    """
    获取项目的冻结表某一天的数据
    """""
    stations_ins = models.StationDetails.objects.filter(is_delete=0, project=project).all()
    detail = list()

    if stations_ins.exists():
        temp_array = list()
        for station in stations_ins:
            detail_ins = models.PeakValley.objects.get(
                province=station.province,
                year_month=date_time.month,
                type=station.type,
                level=station.level
            )

            day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
                                                        station=station.english_name,
                                                        station_type=1).all()
            if day_reports:
                station_detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
                           "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
                          day_reports]
                for ind, i in enumerate(detail):
                    i["type"] = getattr(detail_ins, f"pv{ind}")
                temp_array.append(station_detail)
        if len(temp_array):
            for ind, item in enumerate(temp_array):
                if ind == 0:
                    detail = item
                else:
                    for index, i in enumerate(item):
                        detail[index]['charge'] += i['charge']
                        detail[index]['discharge'] += i['discharge']
                        detail[index]['soc'] += i['soc']
    return detail


def get_common_results_from_ads_report_chag_disg_data(detail_array):
    """
    要求：1、把n个子列表的相同time的value进行求和计算，其中time不相同的直接舍弃；
         2、如第n个子列表的相同的time的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]
    
    """""
    if not detail_array:
        return []
    # 找到三个子列表中都存在的时间点
    common_times = set(detail_array[0][i]["time"] for i in range(len(detail_array[0])))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_times.intersection_update(entry["time"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for time in common_times:
        sum_dict = {"time": time}
        for field in set(entry_key for sublist in detail_array for entry in sublist if entry["time"] == time for entry_key in
                         entry.keys()):
            if field != "time":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["time"] == time]
                if field == "type":
                    sum_dict[field] = values[0]
                elif field == "hour":
                    continue
                else:
                    # 检查是否有 None 值，如果有，则将该字段设置为 None
                    sum_dict[field] = sum(
                        value for value in values if value is not None) if None not in values else '--'
        result_list.append(sum_dict)
    return result_list


def get_common_results_from_ads_report_chag_disg_1d(detail_array):
    """
    要求：1、把n个子列表的相同day的charge和discharge进行求和计算，其中day不相同的直接舍弃；
         2、如第n个子列表的相同的day的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]

    """""
    if not detail_array:
        return []
    # 找到三个子列表中都存在的时间点
    common_days = set(detail_array[0][i]["day"] for i in range(len(detail_array[0])))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_days.intersection_update(entry["day"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for day in common_days:
        sum_dict = {"day": day}
        for field in set(
                entry_key for sublist in detail_array for entry in sublist if entry["day"] == day for entry_key in
                entry.keys()):
            if field != "day":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["day"] == day]

                # 检查是否有 None 值，如果有，则将该字段设置为 None
                sum_dict[field] = sum(
                    value for value in values if value is not None) if None not in values else '--'
        result_list.append(sum_dict)
    return result_list


# def get_project_sum_t_data_from_ads_rhyc(project, date_time):
#     """
#     获取项目的冻结表某一天的数据
#     """""
#     master_station = models.MaterStation.objects.filter(project=project, is_delete=0).all()
#     select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
#                   " where station=%s and station_type=1 and day=%s ORDER BY hour ASC")
#     temp_array = list()
#     detail = list()
#     for m_station in master_station:
#         stations = m_station.stationdetails_set.all()
#         for station in stations:
#             detail_ins = models.PeakValley.objects.get(
#                 province=station.province,
#                 year_month=date_time.month,
#                 type=station.type,
#                 level=station.level
#             )
#
#             # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
#             meter_use_time = MeterUseTime.objects.filter(station=master_station, is_use=1).first()
#             is_use_account = (master_station.is_account and meter_use_time and
#                               meter_use_time.start_time.date() <= date_time <= meter_use_time.end_time.date())
#
#             if not is_use_account:
#                 results = ads_db_tool.select_many(select_sql, station.english_name, date_time.strftime('%Y-%m-%d'))
#                 if results:
#                     station_detail = [{"charge": i['charge'], "discharge": i['discharge'],
#                                       "soc": abs(float(i['soc'])), "time": f"{i['hour']}:00:00",
#                                        "hour": i['hour'] if not i['hour'].startswith('0') else i['hour'][1:]}
#                                       for i in results]
#                     for ind, i in enumerate(station_detail):
#                         i["type"] = getattr(detail_ins, f"pv{i['hour']}")
#                     temp_array.append(station_detail)
#             else:
#                 sql = """SELECT
#                                  day, hour, chag, disg, soc as soc
#                                  FROM ads_report_ems_chag_disg_1h
#                                  WHERE
#                                     station = '{}'
#                                     AND
#                                     day = '{}'
#                                      """.format(station.english_name, date_time.strftime('%Y-%m-%d'))
#                 with connections['doris_ads_rhyc'].cursor() as ads_cursor:
#                     try:
#                         # 获取查询结果
#                         ads_cursor.execute(sql)
#                         res = ads_cursor.fetchall()
#                     except Exception as e:
#                         error_log.error(e)
#                         return Response(
#                             {
#                                 "code": common_response_code.SUMMARY_CODE,
#                                 "data": {
#                                     "message": "error",
#                                     "detail": '查询失败！',
#                                 }
#                             }
#                         )
#                     if res:
#                         station_detail = [{"charge": i[2], "discharge": i[3],
#                                            "soc": abs(float(i[4])), "time": f"{i[1]}:00:00" if i[1] > 9 else f"0{i[1]}:00:00",
#                                            "hour": i[1]}
#                                           for i in res]
#                         for ind, i in enumerate(station_detail):
#                             i["type"] = getattr(detail_ins, f"pv{i['hour']}")
#                         temp_array.append(station_detail)
#     # stations_ins = models.StationDetails.objects.filter(project=project).all()
#     # detail = list()
#     #
#     # select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
#     #               " where station=%s and station_type=1 and day=%s ORDER BY hour ASC")
#     #
#     # if stations_ins.exists():
#     #     temp_array = list()
#     #     for station in stations_ins:
#     #         detail_ins = models.PeakValley.objects.get(
#     #             province=station.province,
#     #             year_month=date_time.month,
#     #             type=station.type,
#     #             level=station.level
#     #         )
#     #
#     #         # day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
#     #         #                                             station=station.english_name,
#     #         #                                             station_type=1).all()
#     #         # if day_reports:
#     #         #     station_detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
#     #         #                "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
#     #         #               day_reports]
#     #         #     for ind, i in enumerate(detail):
#     #         #         i["type"] = getattr(detail_ins, f"pv{ind}")
#     #         #     temp_array.append(station_detail)
#     #
#     #         results = ads_db_tool.select_many(select_sql, station.english_name, date_time.strftime('%Y-%m-%d'))
#     #         if results:
#     #             station_detail = [{"charge": i['charge'], "discharge": i['discharge'],
#     #                               "soc": abs(float(i['soc'])), "time": f"{i['hour']}:00:00",
#     #                                "hour": i['hour'] if not i['hour'].startswith('0') else i['hour'][1:]}
#     #                               for i in results]
#     #             for ind, i in enumerate(station_detail):
#     #                 i["type"] = getattr(detail_ins, f"pv{i['hour']}")
#     #             temp_array.append(station_detail)
#
#     if len(temp_array):
#         detail = get_common_results_from_ads_report_chag_disg_data(temp_array)
#
#     detail = sorted(detail, key=lambda x: x["time"])
#     return detail


def get_station_t_report_data(master_station, date_time):
    """
    获取站的冻结表某一天的数据
    """""
    slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
    detail = list()
    temp_array = list()
    for station_inst in slave_stations:
        detail_ins = models.PeakValley.objects.get(
            province=station_inst.province,
            year_month=date_time.month,
            type=station_inst.type,
            level=station_inst.level
        )

        day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
                                                    station=station_inst.english_name,
                                                    station_type=1).all()
        if day_reports:
            temp_detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
                       "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
                      day_reports]
            for ind, i in enumerate(temp_detail):
                i["type"] = getattr(detail_ins, f"pv{ind}")

            temp_array.append(temp_detail)
            # detail = classification(detail)
        # else:
        #     detail = []
    if len(temp_array):
        for ind, item in enumerate(temp_array):
            if ind == 0:
                detail = item
            else:
                for index, i in enumerate(item):
                    detail[index]['charge'] += i['charge']
                    detail[index]['discharge'] += i['discharge']
                    detail[index]['soc'] += i['soc']

    return detail


def new_get_station_t_report_data(master_station, date_time):
    """
    获取站的冻结表某一天的数据
    """""
    slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0).all()
    detail = list()
    temp_array = list()

    select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc as soc FROM ads_report_chag_disg_data"
                  " where station=%s and station_type=1 and day=%s ORDER BY hour ASC")

    for station_inst in slave_stations:
        detail_ins = models.PeakValley.objects.get(
            province=station_inst.province,
            year_month=date_time.month,
            type=station_inst.type,
            level=station_inst.level
        )

        # day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
        #                                             station=station_inst.english_name,
        #                                             station_type=1).all()
        # if day_reports:
        #     temp_detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
        #                "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
        #               day_reports]
        #     for ind, i in enumerate(temp_detail):
        #         i["type"] = getattr(detail_ins, f"pv{ind}")
        #
        #     temp_array.append(temp_detail)
        #     # detail = classification(detail)
        # # else:
        # #     detail = []

        results = ads_db_tool.select_many(select_sql, station_inst.english_name, date_time.strftime('%Y-%m-%d'))
        if results:
            station_detail = [{"charge": i['charge'], "discharge": i['discharge'],
                               "soc": abs(float(i['soc'])), "time": f"{i['hour']}:00:00"} for i in results]
            for ind, i in enumerate(station_detail):
                i["type"] = getattr(detail_ins, f"pv{ind}")
            temp_array.append(station_detail)

    if len(temp_array):
        for ind, item in enumerate(temp_array):
            if ind == 0:
                detail = item
            else:
                for index, i in enumerate(item):
                    detail[index]['charge'] += i['charge']
                    detail[index]['discharge'] += i['discharge']
                    detail[index]['soc'] += i['soc']

    return detail


def get_unit_t_report_data(unit_instance, date_time):
    """
    获取储能单元的冻结表某一天的数据
    """""
    detail_ins = models.PeakValley.objects.get(
        province=unit_instance.station.province,
        year_month=date_time.month,
        type=unit_instance.station.type,
        level=unit_instance.station.level,
    )

    day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
                                                station=unit_instance.station.english_name,
                                                station_type=2, unit_name=unit_instance.bms).all()
    if day_reports:
        detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
                   "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
                  day_reports]
        for ind, i in enumerate(detail):
            i["type"] = getattr(detail_ins, f"pv{ind}")
        # detail = classification(detail)
    else:
        detail = []

    return detail


def new_get_unit_t_report_data(unit_instance, date_time):
    """
    获取储能单元的冻结表某一天的数据
    """""
    detail_ins = models.PeakValley.objects.get(
        province=unit_instance.station.province,
        year_month=date_time.month,
        type=unit_instance.station.type,
        level=unit_instance.station.level,
    )

    select_sql = ("SELECT day, hour, chag as charge, disg as discharge, soc FROM ads_report_chag_disg_data"
                  " where station=%s and station_type=2 and unit_name=%s and day=%s ORDER BY hour ASC")

    # day_reports = models.FReport.objects.filter(day=date_time.strftime('%Y-%m-%d'),
    #                                             station=unit_instance.station.english_name,
    #                                             station_type=2, unit_name=unit_instance.bms).all()
    # if day_reports:
    #     detail = [{"charge": float(day_report.chag), "discharge": float(day_report.disg),
    #                "soc": abs(float(day_report.soc)), "time": f"{day_report.hour}:00:00"} for day_report in
    #               day_reports]
    #     for ind, i in enumerate(detail):
    #         i["type"] = getattr(detail_ins, f"pv{ind}")
    #     # detail = classification(detail)
    # else:
    #     detail = []

    results = ads_db_tool.select_many(select_sql, unit_instance.station.english_name, unit_instance.bms, date_time.strftime('%Y-%m-%d'))
    if results:
        detail = [{"charge": i['charge'], "discharge": i['discharge'],
                    "soc": abs(float(i['soc'])), "time": f"{i['hour']}:00:00"} for i in results]
        for ind, i in enumerate(detail):
            i["type"] = getattr(detail_ins, f"pv{ind}")
        # detail = classification(detail)
    else:
        detail = []

    return detail


def get_common_results_from_dwd_measure_pcs_data_storage(detail_array):
    """
    要求：1、把n个子列表的相同time的value进行求和计算，其中time不相同的直接舍弃；
         2、如第n个子列表的相同的time的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]

    """""
    if not detail_array:
        return []
    # 找到n个子列表中都存在的时间点
    common_times = set(detail_array[0][i]["time"] for i in range(len(detail_array[0])))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_times.intersection_update(entry["time"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for time in common_times:
        sum_dict = {"time": time}
        for field in set(
                entry_key for sublist in detail_array for entry in sublist if entry["time"] == time for entry_key in
                entry.keys()):
            if field != "time":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["time"] == time]
                # 检查是否有 None 值，如果有，则将该字段设置为 None
                sum_dict[field] = sum(
                    value for value in values if value is not None) if '--' not in values and None not in values else '--'
        result_list.append(sum_dict)
    result_list = sorted(result_list, key=lambda x: x["time"])
    return result_list


def get_common_results_for_soc_average_from_dwd_measure_bms_data_storage_3(detail_array):
    """
    要求：1、把n个子列表的相同time的value进行求和计算，其中time不相同的直接舍弃；
         2、如第n个子列表的相同的time的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]

    """""
    if not detail_array:
        return []
    # 找到n个子列表中都存在的时间点
    common_times = set(detail_array[0][i]["time"] for i in range(len(detail_array[0])))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_times.intersection_update(entry["time"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for time in common_times:
        sum_dict = {"time": time}
        for field in set(
                entry_key for sublist in detail_array for entry in sublist if entry["time"] == time for entry_key in
                entry.keys()):
            if field != "time":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["time"] == time]
                # 检查是否有 None 值，如果有，则将该字段设置为 None
                sum_dict[field] = round(sum(values)/len(values), 1) if None not in values else '--'
        result_list.append(sum_dict)
    result_list = sorted(result_list, key=lambda x: x["time"])
    return result_list


def get_common_results_for_soc_for_stations_from_dwd_measure_bms_data_storage_3(detail_array):
    """
    要求：1、把n个子列表的相同time的value进行求和计算，其中time不相同的直接舍弃；
         2、如第n个子列表的相同的time的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]

    """""
    if not detail_array:
        return []
    # 找到n个子列表中都存在的时间点
    common_times = set(detail_array[0][i]["time"] for i in range(len(detail_array[0])))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_times.intersection_update(entry["time"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for time in common_times:
        sum_dict = {"time": time}
        for field in set(
                entry_key for sublist in detail_array for entry in sublist if entry["time"] == time for entry_key in
                entry.keys()):
            if field != "time":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["time"] == time]
                # 检查是否有 None 值，如果有，则将该字段设置为 None
                sum_dict[field] = round(sum(values)/len(values), 1) if None not in values else '--'
        result_list.append(sum_dict)
    result_list = sorted(result_list, key=lambda x: x["time"])
    return result_list


def get_common_results_2_from_ads_report_chag_disg_data(detail_array):
    """
    要求：1、把n个子列表的相同time的value进行求和计算，其中time不相同的直接舍弃；
         2、如第n个子列表的相同的time的value值是None，则value字段不再进行求和计算, 而是直接置为‘--’；
         3、对于不需要求和计算的某个字段，比如type，直接单独处理
    :param detail_array:
     [
        [{"time": "03:00:00", "value": 1, "value2": 1},{"time": "04:00:00", "value": 1, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 1, "value2": 1},{"time": "06:00:00", "value": 1, "value2": 1}],
        [{"time": "01:00:00", "value": 2, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "02:00:00", "value": 4, "value2": 1},{"time": "03:00:00", "value": 5, "value2": 1}],
        [{"time": "02:00:00", "value": 3, "value2": 1},{"time": "03:00:00", "value": 3, "value2": 1},{"time": "04:00:00", "value": 3, "value2": 1, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1}],
        [{"time": "04:00:00", "value": 3, "value2": None, "value3": 1},{"time": "05:00:00", "value": 3, "value2": 1},{"time": "06:00:00", "value": 3, "value2": 1},{"time": "07:00:00", "value": 3, "value2": 1}]
    ]
    return: [{'time': '04:00:00', 'value': 10}]

    """""
    if not detail_array:
        return []
    # 找到三个子列表中都存在的时间点
    common_times = set(detail_array[0][i]["time"] for i in range(len(detail_array[0])))

    print(1758, len(detail_array))
    if len(detail_array) > 1:
        for sublist in detail_array[1:]:
            common_times.intersection_update(entry["time"] for entry in sublist)

    # 计算相同时间点的值的总和
    result_list = []
    for time in common_times:
        sum_dict = {"time": time}
        for field in set(
                entry_key for sublist in detail_array for entry in sublist if entry["time"] == time for entry_key in
                entry.keys()):
            if field != "time":
                values = [entry[field] for sublist in detail_array for entry in sublist if entry["time"] == time]
                if field == "type":
                    sum_dict[field] = values[0]
                elif field == "hour":
                    continue
                elif field == "soc":
                    sum_dict[field] = round(sum(values) / len(values), 1) if None not in values else '--'
                else:
                    # 检查是否有 None 值，如果有，则将该字段设置为 None
                    sum_dict[field] = round(sum([
                        value for value in values if value is not None]), 1) if None not in values else '--'
        result_list.append(sum_dict)
    return result_list
