package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 功率计划关联记录VO
 */
@Data
@ApiModel("功率计划关联记录VO")
public class TPlanPowerRecordsVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("计划历史记录ID")
    private Long planId;

    @ApiModelProperty("功率下发记录ID")
    private Long powerId;

    @ApiModelProperty("功率计划序号")
    private Integer serialNumber;

    @ApiModelProperty("是否使用: 1-使用, 0-不使用")
    private Integer isUse;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
