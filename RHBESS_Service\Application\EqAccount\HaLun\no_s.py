#!/usr/bin/env python
# coding=utf-8
#@Information: 不上线代码

import tornado.web
from Application.Models.User.custom_report_t import ReportCustom
from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
from Tools.DB.mysql_user import user_session
from Tools.DB.binhai1_his import binhai1_session
from Tools.DB.binhai2_his import binhai2_session
from Tools.DB.halun_his import halun_session
from Tools.DB.taicang_his import taicang_session
from Application.Models.base_handler import BaseHandler
from Tools.TimeTask import froze_report
from Tools.Utils import main_save
# from Tools.Utils.main_save import _name_join
from Tools.Utils.num_utils import *
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.event import Event
from Tools.DB.baodian_his import baodian1_session,baodian2_session,baodian3_session,baodian4_session,baodian5_session
from Tools.DB.ygzhen_his import ygzhen1_session,ygzhen2_session
class NoSIntetface(BaseHandler):
    ''' 自定义功能汇总 '''
    @tornado.web.authenticated
    def get(self,kt):
        global r
        self.refreshSession()
        # try:
        if 1:
            if kt == 'dongjie':
                from Tools.TimeTask import froze_report_idc
                list_3 = timeUtils.dateToDataList('2024-02-07','2024-02-07')
                for day in list_3:
                    froze_report.calculation(day)
                    # froze_report_2.calculation(day)
            elif kt == 'baocun':
                # from Application.Models.User.equipment_number_t import EquipmentNumbe
                # from Application.Models.User.data_item_t import DataItem
                # # #历史数据配置翻译
                # # descr = user_session.query(DataItem).filter(DataItem.id>3678).all()
                # # for i in descr:
                # #     i.en_name = translate_text(i.name,2)
                # #     user_session.commit()
                # # 工单翻译
                # ##t_dispatch_step
                # # all = user_session.query(DispatchStep).filter(DispatchStep.id>1856).order_by(DispatchStep.id).all()
                # # for org in all:
                # #     if org.descr != None:
                # #         org.en_descr = translate_text(org.descr, 2)
                # #     if org.content != None:
                # #         org.en_content = translate_text(org.content, 2)
                # #     if org.care_content != None:
                # #         org.en_care_content = translate_text(org.care_content, 2)
                # #     user_session.commit()
                #
                #
                #
                #
                # # # #r_dispatch_step
                # # all = user_session.query(DispatchStepR).filter(DispatchStepR.id>1435).order_by(DispatchStepR.id).all()
                # # for org in all:
                # #     print (11111111111,org)
                # #     if org.content and org.content != None:
                # #         org.en_content = translate_text(org.content, 2)
                # #         print (2222222, org.en_content)
                # #     if org.check_content and org.check_content != None:
                # #         org.en_check_content = translate_text(org.check_content, 2)
                # #     if org.care_content and org.care_content != None:
                # #         org.en_care_content = translate_text(org.care_content, 2)
                # #         print (444444444444, org.en_care_content)
                # #     if org.other_content and org.other_content != None:
                # #         org.en_other_content = translate_text(org.other_content, 2)
                # #         print (5555555555555, org.en_other_content)
                # #     user_session.commit()
                # #
                # # #t_dispatch_model
                # # all = user_session.query(DispatchModel).filter(DispatchModel.id>710).order_by(DispatchModel.id).all()
                # # for org in all:
                # #     if org.descr and org.descr != None:
                # #         org.en_descr = translate_text(org.descr, 2)
                # #     if org.remarks and org.remarks != None:
                # #         org.en_remarks = translate_text(org.remarks, 2)
                # #     user_session.commit()
                # # #
                # #t_dispatch_model
                # all = user_session.query(Event).filter(Event.id>21596).order_by(Event.id).all()
                # for org in all:
                #     if org.descr and org.descr != None:
                #         org.en_descr = translate_text(org.descr, 2)
                #     user_session.commit()
                # Event
                # all = user_session.query(DispatchR).filter(DispatchR.id > 815).order_by(DispatchR.id).all()
                # for org in all:
                #     print (1111111111111, org)
                #     if org.descr and org.descr != None:
                #         org.en_descr = translate_text(org.descr, 2)
                #         print (222222222222, org.en_descr)
                #     if org.content and org.content != None:
                #         org.en_content = translate_text(org.content, 2)
                #         print (333333333333, org.en_descr)
                #     if org.other  and org.other != None:
                #         ck_list = eval(str(org.other))
                #         for k, v in ck_list.items():
                #             if v and v != None:
                #                 ck_list[k] = translate_text(v, 2)
                #         org.en_other = str(ck_list)
                #         print (444444444444, org.en_other)
                #     user_session.commit()



                # all = user_session.query(Fault).filter(Fault.id > 437).order_by(Fault.id).all()
                # for org in all:
                #     if org.fault_name != None:
                #         org.en_fault_name = translate_text(org.fault_name, 2)
                #     if org.fault_part != None:
                #         org.en_fault_part = translate_text(org.fault_part, 2)
                #     if org.fault_problem != None:
                #         org.en_fault_problem = translate_text(org.fault_problem, 2)
                #     if org.repair_way != None:
                #         org.en_repair_way = translate_text(org.repair_way, 2)
                #     if org.repair_use != None:
                #         org.en_repair_use = translate_text(org.repair_use, 2)
                #         print (111111111111, org.repair_use, org.en_repair_use)
                #     if org.repair_plan != None:
                #         org.en_repair_plan = translate_text(org.repair_plan, 2)
                #     if org.cause != None:
                #         org.en_cause = translate_text(org.cause, 2)
                #     if org.remarks != None:
                #         org.en_remarks = translate_text(org.remarks, 2)
                #     user_session.commit()
                #
                # # 报表数据翻译
                all = user_session.query(ReportCustom).filter(ReportCustom.flag ==1).order_by(ReportCustom.id).all()
                for org in all:
                    print (777777777777, org.user)
                    if org.user and org.user != None:
                        org.en_user = translate_text(org.user, 2)
                    if org.title and org.title != None:
                        ck_list = eval(str(org.title))
                        for k, v in ck_list.items():
                            if v and v != None:
                                ck_list[k] = translate_text(v, 2)
                        org.en_title = str(ck_list)
                    if org.table_list and org.table_list != None:
                        ck_list = eval(str(org.table_list))
                        for i in ck_list:
                            for k, v in i.items():
                                if v and v != None:
                                    i[k] = translate_text(v, 2)
                        org.en_table_list = str(ck_list)
                    user_session.commit()

                #
                # all = user_session.query(City).filter(City.id > 1690).order_by(City.id).all()
                # all = user_session.query(City).order_by(City.id).all()
                # for org in all:
                #     org.en_city = translate_text(org.city, 2)
                #     user_session.commit()
                # all = mqtt_session.query(StatusBitsPT).order_by(StatusBitsPT.id).all()
                # for org in all:
                #     org.en_desc_on = translate_text(org.desc_on, 2)
                #     org.en_desc_off = translate_text(org.desc_off, 2)
                #     mqtt_session.commit()
                #
                # all = user_session.query(PageData).filter(PageData.id > 39144).order_by(PageData.id).all()
                # for org in all:
                #     org.en_descr = translate_text(org.descr, 2)
                #     user_session.commit()
                # all = user_session.query(DeviceAnnex).order_by(DeviceAnnex.id).all()
                # for org in all:
                #     org.en_file_name = translate_text(org.file_name, 2)
                #     user_session.commit()
                # all = user_session.query(EquipmentNumbe).filter(EquipmentNumbe.id > 1346).order_by(EquipmentNumbe.id).all()
                # for org in all:
                #     if org.ty != None:
                #         org.en_ty = translate_text(org.ty, 2)
                #     if org.name != None:
                #         org.en_name = translate_text(org.name, 2)
                #     user_session.commit()
            elif kt == 'baocun2':
                # r_dispatch_step
                all = user_session.query(DispatchStepR).filter(DispatchStepR.id>1204).order_by(DispatchStepR.id).all()
                for org in all:
                    if org.content != None:
                        org.en_content = translate_text(org.content, 2)
                    if org.check_content != None:
                        org.en_check_content = translate_text(org.check_content, 2)
                    if org.care_content != None:
                        org.en_care_content = translate_text(org.care_content, 2)
                    if org.other_content != None:
                        org.en_other_content = translate_text(org.other_content, 2)
                    user_session.commit()

                # t_dispatch_model
                # all = user_session.query(DispatchModel).order_by(DispatchModel.id).all()
                # for org in all:
                #     if org.descr != None:
                #         org.en_descr = translate_text(org.descr, 2)
                #     if org.remarks != None:
                #         org.en_remarks = translate_text(org.remarks, 2)
                #     user_session.commit()
                #
                # # t_dispatch_plan
                # all = user_session.query(DispatchPlan).order_by(DispatchPlan.id).all()
                # for org in all:
                #     if org.content != None:
                #         org.en_content = translate_text(org.content, 2)
                #     if org.device != None:
                #         org.en_device = translate_text(org.device, 2)
                #     user_session.commit()
                # all = user_session.query(Province).order_by(Province.id).all()
                # for org in all:
                #     org.en_province = translate_text(org.province, 2)
                #     user_session.commit()


                # all = user_session.query(Fault).filter(Fault.id > 292).order_by(Fault.id).all()
                # all = user_session.query(Fault).filter(Fault.id > 402).order_by(Fault.id).all()
                # for org in all:
                #     if org.fault_name != None:
                #         org.en_fault_name = translate_text(org.fault_name, 2)
                #     if org.fault_part != None:
                #         org.en_fault_part = translate_text(org.fault_part, 2)
                #     if org.fault_problem != None:
                #         org.en_fault_problem = translate_text(org.fault_problem, 2)
                #     if org.repair_way != None:
                #         org.en_repair_way = translate_text(org.repair_way, 2)
                #     if org.repair_use != None:
                #         org.en_repair_use = translate_text(org.repair_use, 2)
                #         print (111111111111, org.repair_use, org.en_repair_use)
                #     if org.repair_plan != None:
                #         org.en_repair_plan = translate_text(org.repair_plan, 2)
                #     if org.cause != None:
                #         org.en_cause = translate_text(org.cause, 2)
                #     if org.remarks != None:
                #         org.en_remarks = translate_text(org.remarks, 2)
                #     user_session.commit()
            elif kt == 'lishi':
                db={'binhai':{'ty_mun':[{"label":"Pcs站1-1#储能","name_la":"tpStbinhai1.PcsSt1.Lp1.","value":"BMS"},{"label":"Pcs站2-1#储能-组簇","name_la":"tpStbinhai1.PcsSt2.Lp1.","value":"BMS"}],
                              'data_item':[{"value":"PCS","label":"组充电容量总KWH","name_la":"KWH_ChagCapyTotl","secondVal":1,"secondLabel":"无聚合"},{"value":"BMS","label":"2单体最大温度","name_la":"CellTmax_Rk2","secondVal":1,"secondLabel":"无聚合"}],
                              'start_Time':'2023-09-01 00:00:00','end_Time':'2023-09-03 00:00:00'},
                    'halun':{'ty_mun':[{"label":"Pcs站1-1#储能","name_la":"tpSthalun.PcsSt1.Lp1.","value":"PCS"},{"label":"Pcs站1-2#储能-组簇","name_la":"tpSthalun.PcsSt1.Lp2.","value":"BMS"}],
                             'data_item':[{"value":"PCS","label":"组充电容量总KWH","name_la":"KWH_ChagCapyTotl","secondVal":1,"secondLabel":"无聚合"},{"value":"BMS","label":"2单体最大温度","name_la":"CellTmax_Rk2","secondVal":1,"secondLabel":"无聚合"}],
                             'start_Time':'2023-09-01 00:00:00','end_Time':'2023-09-03 00:00:00'},
                    'taicang':{'ty_mun':[{"label":"Pcs站1-1#储能","name_la":"tpSttaicang.PcsSt1.Lp1.","value":"PCS"},{"label":"Pcs站2-2#储能-组簇","name_la":"tpSttaicang.PcsSt2.Lp2.","value":"BMS"}],
                               'data_item':[{"value":"PCS","label":"组充电容量总KWH","name_la":"KWH_ChagCapyTotl","secondVal":1,"secondLabel":"无聚合"},{"value":"BMS","label":"2单体最大温度","name_la":"CellTmax_Rk2","secondVal":1,"secondLabel":"无聚合"}],
                               'start_Time':'2023-09-01 00:00:00','end_Time':'2023-09-03 00:00:00'},
                    'ygzhen':{'ty_mun':[{"label":"电表","name_la":"tfStygzhen1.EMS.MET.","value":"电表"}],
                              'data_item':[{"value":"电表","label":"总正向总电能","name_la":"Et_pos1","secondVal":1,"secondLabel":"无聚合"},{"value":"电表","label":"总反向总电能","name_la":"Et_neg1","secondVal":1,"secondLabel":"无聚合"}],
                              'start_Time':'2023-09-01 00:00:00','end_Time':'2023-09-03 00:00:00'},
                    'baodian': {'ty_mun': [{"label":"3#PCS  ","name_la":"tfStbodian3.Pcs.","value":"PCS"}],
                                'data_item': [{"value":"PCS","label":"总充电容量","name_la":"ChagCapyTotl","secondVal":1,"secondLabel":"无聚合"},{"value":"PCS","label":"总放电容量","name_la":"DisgCapyTotl","secondVal":1,"secondLabel":"无聚合"}],
                                'start_Time': '2023-10-13 00:00:00','end_Time': '2023-10-28 00:00:00'},
                    'datong':{'ty_mun':[{"label":"1 #PCS 单元1","name_la":"tc_datong1.EMS.Energy1.PCS.Lp1.","value":"PCS"},{"label":"7 #BMS 堆1","name_la":"tc_datong4.EMS.Energy7.BMS.Ay1.","value":"BMS"}],
                              'data_item':[{"value":"PCS","label":"累计充电量","name_la":"TolChagEle","secondVal":1,"secondLabel":"无聚合"},{"value":"BMS","label":"系统SOC","name_la":"SysSoc","secondVal":1,"secondLabel":"无聚合"}],
                              'start_Time':'2024-01-03 00:00:00','end_Time':'2024-01-07 00:00:00'},
                    'zgtian':{'ty_mun':[{"label":"表2的正向有功电度","name_la":"tfStzgtian1meter.Meter.","value":"电表"}],
                              'data_item':[{"value":"电表","label":"表2的正向有功电度","name_la":"MeasureA8_M2_PAT","secondVal":1,"secondLabel":"无聚合"},{"value":"电表","label":"表2的反向有功电度","name_la":"MeasureA8_M2_NAT","secondVal":1,"secondLabel":"无聚合"}],
                              'start_Time': '2024-01-01 00:00:00','end_Time': '2024-01-03 00:00:00'},
                    'houma':{'ty_mun':[{"label":"1#EMS.A502 1#PCS 单元1","name_la":"tfSthoumaA1.EMS.A502.PCS1.Lp1.","value":"PCS"}],'data_item':[{"value":"PCS","label":"总充电量","name_la":"ACTotlChag","secondVal":1,"secondLabel":"无聚合"}],
                             'start_Time':'2024-01-11 00:00:00','end_Time':'2024-01-15 00:00:00'},
                    'dongmu':{'ty_mun':[{"label":"PCS1","name_la":"","value":"PCS"},{"label":"BMS6","name_la":"","value":"BMS"}],
                              'data_item':[{"value":"PCS","label":"电池容量","name_la":"","secondVal":1,"secondLabel":"无聚合"},{"value":"BMS","label":"模组最低温度","name_la":"","secondVal":1,"secondLabel":"无聚合"}],
                              'start_Time': '2023-06-15 00:00:00', 'end_Time': '2023-06-17 00:00:00'},
                    'guizhou':{'ty_mun':[{"label":"1 #PCS 单元1","name_la":"guizhou1a.EMS.Energy1.PCS.Lp1.","value":"PCS"}],'data_item':[{"value":"PCS","label":"总充电量","name_la":"TotlChgEle","secondVal":1,"secondLabel":"无聚合"}],
                               'start_Time':'2024-01-11 00:00:00','end_Time':'2024-01-15 00:00:00'}}
                print(timeUtils.timeStrToTamp('%s %s' % ('2024-03-02', '00:00:00')),timeUtils.timeStrToTamp('%s %s' % (timeUtils.getNewTimeStr()[:10], '00:00:00')))
                while timeUtils.timeStrToTamp('%s %s' % ('2024-03-02', '00:00:00')) > timeUtils.timeStrToTamp('%s %s' % (timeUtils.getNewTimeStr()[:10], '00:00:00')):
                    for rr in range(1, 5):
                        r += 1
                        for k, v in db.items():
                            try:
                                data = main_save.calculation_3(k, v)
                                print('成功成功成功成功成功成功成功成功成功', k, r, data)
                                logging.info('成功成功成功成功成功成功成功成功成功成功成功成功', 'station', k, '次数', r, '数据集', data)
                            except Exception as E:
                                print (k, r, E)
                                logging.info('错误错误错误错误错误错误错误错误错误错误错误错误错误', 'station', k, '次数', r, '原因', E)
            #电芯簇中文配置
            # elif kt == 'dianxinpeiz':
            #     from Tools.TimeTask import froze_report_idc
            #     list_3 = timeUtils.dateToDataList('2024-03-10', '2024-03-11')
            #     for day in list_3:
            #         cu_name=_name_join(self, db, pcsSt, lp, bank, na)
            #
            #         froze_report_idc.calculation(day)
            #         # froze_report_2.calculation(day)

            else:
                return self.pathError()
        # except Exception as E:
        #     user_session.rollback()
        #     logging.error(E)
        #     return self.requestError()
        # finally:
        #     user_session.close()
        #     halun_session.close()
        #     taicang_session.close()
        #     binhai1_session.close()
        #     binhai2_session.close()
        #     ygzhen1_session.close()
        #     ygzhen2_session.close()
        #     baodian1_session.close()
        #     baodian2_session.close()
        #     baodian3_session.close()
        #     baodian4_session.close()
        #     baodian5_session.close()




