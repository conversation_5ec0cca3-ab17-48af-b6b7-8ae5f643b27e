#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\authority.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-27 13:44:04

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.User.station import Station
class Authority(user_Base):
    u'权限静态配置表'
    __tablename__ = "t_authority"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    path = Column(String(256), nullable=False, comment=u"路径")
    name = Column(String(256), nullable=False,comment=u"名称")
    redirect = Column(String(256), nullable=True,comment=u"跳转路径")
    hidden = Column(Integer, nullable=True, comment=u"")
    component = Column(String(256), nullable=True,comment=u"路由")
    title_ = Column(String(256), nullable=True,comment=u"标题内容")
    icon = Column(String(256), nullable=True,comment=u"图标")
    parent_id = Column(Integer, nullable=True,comment=u"父节点id")
    station_id = Column(Integer, nullable=True, comment=u"所属站")
   

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        i = 1
        user_session.merge(Authority(id=i,path='/',name='Cloud',redirect='/home',hidden=1,component='cloud',title_='根节点',icon=''));i = i+1;
        user_session.commit()
        user_session.merge(Authority(id=i,path='/home',name='Home',redirect='',hidden=1,component='home',title_='首页',icon='appstore',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/monitor',name='Monitor',redirect='/monitor/sysMonitor',hidden=1,component='monitor',title_='监测',icon='rise',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/alarm',name='Alarm',redirect='',hidden=1,component='alarm',title_='报警',icon='bell',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/event',name='Event',redirect='',hidden=1,component='event',title_='事件',icon='exception',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/chart',name='Chart',redirect='',hidden=1,component='chart',title_='曲线',icon='pie-chart',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/statement',name='Statement',redirect='',hidden=1,component='statement',title_='报表',icon='container',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/compute',name='Compute',redirect='',hidden=1,component='compute',title_='分析',icon='codepen',parent_id=1));i = i+1;
        user_session.merge(Authority(id=i,path='/regulate',name='Regulate',redirect='',hidden=1,component='regulate',title_='管理',icon='setting',parent_id=1));i = i+1;
        user_session.commit()
        user_session.merge(Authority(id=i,path='/monitor/sysMonitor',name='SysMonitor',redirect='',hidden=1,component='monitor/sys',title_='系统监控',icon='rise',parent_id=3));i = i+1;
        user_session.merge(Authority(id=i,path='/monitor/bms',name='BmsMonitor',redirect='',hidden=1,component='monitor/bms',title_='电池监控',icon='',parent_id=3));i = i+1;
        user_session.merge(Authority(id=i,path='/monitor/pcs',name='PcsMonitor',redirect='',hidden=1,component='monitor/pcs',title_='PCS监控',icon='',parent_id=3));i = i+1;
        user_session.merge(Authority(id=i,path='/monitor/sysIo',name='SysIo',redirect='',hidden=1,component='monitor/io',title_='系统通信',icon='',parent_id=3));i = i+1;
        user_session.merge(Authority(id=i,path='/monitor/parameter',name='Parameter',redirect='',hidden=1,component='monitor/parameter',title_='参数设置',icon='',parent_id=3));i = i+1;
        user_session.merge(Authority(id=i,path='/user',name='User',redirect='',hidden=0,component='user',title_='用户',icon='',parent_id=1));i = i+1;
        user_session.commit()
        user_session.close()

    def __repr__(self):
        return "{'id':%s,'path':'%s','name':'%s','redirect':'%s','hidden':%s,'component':'%s','title_':'%s','icon':'%s','parent_id':'%s','station_id':'%s'}" % (
            self.id,self.path,self.name,self.redirect,self.hidden,self.component,self.title_,self.icon,self.parent_id,self.station_id)