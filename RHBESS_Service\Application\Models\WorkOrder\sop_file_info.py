#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-06-20 16:48:46
#@FilePath     : \RHBESS_Service\Application\Models\WorkOrder\sop_file_info.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-30 13:58:52


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.WorkOrder.sop_label_info import SopLabelInfo

class SopFileInfo(user_Base):
    u'SOP文件表'
    __tablename__ = "t_sop_file_info"
    id = Column(String(256), nullable=False, primary_key=True,comment=u"主键")
    file_name = Column(String(256), nullable=True, comment=u"文件名称")
    file_url = Column(String(256), nullable=True, comment=u"文件路径")
    label_info_id = Column(Integer,ForeignKey("t_sop_label_info.id"), nullable=False,comment=u"SOP标签表id")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    is_use = Column(Integer, nullable=False,server_default='1',comment=u"是否使用，1是0否")

    sop_label_model= relationship("SopLabelInfo", backref="sop_label_model")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
       
        bean = "{'uid':'%s','name':'%s','url':'%s',}" % (self.id,self.file_name,self.file_url)
        return bean.replace("None",'')
        
