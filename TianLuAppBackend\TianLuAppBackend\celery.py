import os
from celery import Celery
from apis.monitor import celeryconfig

project_name = "TianLuAppBackend"
# 设置celery的环境变量和django-celery的工作目录
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TianLuAppBackend.settings')
# 实例化celery应用
app = Celery(project_name)
# 加载celery配置
app.config_from_object(celeryconfig)

# 如果在项目中，创建了task.py,那么celery就会沿着app去查找task.py来生成任务
app.autodiscover_tasks()


# 设置app自动加载任务
app.autodiscover_tasks([
    'apis.monitor',
])
