from django.db import models
from common.base_models import BaseModel
from apis.user.models import UserDetails


# Create your models here.
class CustomInfoModel(BaseModel):
    """客户信息"""
    unit_name = models.CharField(verbose_name="客户单位名称", max_length=128)
    unit_addr = models.CharField(verbose_name="客户单位地址", max_length=256, blank=True, null=True)
    name = models.CharField(verbose_name="联系人", max_length=12)
    tel = models.CharField(verbose_name="联系人电话", max_length=11)

    class Meta:
        db_table = "t_custom_info"


class ProjectInfoModel(BaseModel):
    """工程详情表"""
    service_content = models.CharField(verbose_name="服务内容", max_length=100, blank=True, null=True)
    # 优化方案v3.0: 服务内容增加数量
    service1 = models.BooleanField(verbose_name="储能柜调试", default=False)
    service2 = models.BooleanField(verbose_name="储能柜安装指导与业主对接", default=False)
    service3 = models.BooleanField(verbose_name="调试期间故障消缺", default=False)
    service4 = models.BooleanField(verbose_name="后期现场紧急故障处理", default=False)
    service5 = models.BooleanField(verbose_name="调试期间误工", default=False)
    count1 = models.IntegerField(verbose_name='储能柜调试台数', default=1)
    count3 = models.IntegerField(verbose_name='调试期间故障消缺项数', default=1)
    count4 = models.IntegerField(verbose_name='后期现场紧急故障处理项数', default=1)
    count5 = models.IntegerField(verbose_name='调试期间误工天数', default=1)

    comments = models.CharField(verbose_name="备注信息", max_length=512, blank=True, null=True)
    order_img = models.CharField(verbose_name="发货单", max_length=512)
    other_img = models.CharField(verbose_name="发货单", max_length=512, blank=True, null=True)

    class Meta:
        db_table = "t_project_info"


# 优化方案v2.0: 修改成配置多个并网点信息
class WorkOrderStationInfoModel(BaseModel):
    """工程详情表"""
    station_name = models.CharField(verbose_name="并网点名称", max_length=32)
    vol = models.IntegerField(verbose_name="容量")
    dev_count = models.IntegerField(verbose_name="设备数量")
    level = models.SmallIntegerField(verbose_name="等级", choices=(
        (1, "380 V"),
        (2, "10 kV"),
        (3, "35 kV"),
        (4, "110 kV"),
        (5, "220 kV")
    ))
    combination = models.SmallIntegerField(verbose_name="组合", choices=(
        (1, "一并联"),
        (2, "二并联"),
        (3, "三并联"),
        (4, "四并联")
    ))
    is_ems = models.SmallIntegerField(verbose_name="是否涉及ems", choices=(
        (1, "是"),
        (0, "否")
    ))

    work_order = models.ForeignKey(
        to="WorkOrderModel",
        verbose_name="工单",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    # service_content = models.CharField(verbose_name="服务内容", max_length=100)
    # comments = models.CharField(verbose_name="备注信息", max_length=512, blank=True, null=True)
    # order_img = models.CharField(verbose_name="发货单", max_length=512)
    # other_img = models.CharField(verbose_name="发货单", max_length=512, blank=True, null=True)

    class Meta:
        db_table = "t_work_order_station_info"


class ExecutorModel(BaseModel):
    """接单人信息表"""
    work_no = models.CharField(verbose_name="电工编号", max_length=32)
    traffic = models.CharField(verbose_name="交通方式", max_length=64)
    name = models.CharField(verbose_name="接单人", max_length=12)
    tel = models.CharField(verbose_name="接单人电话", max_length=11)
    comments = models.CharField(verbose_name="备注信息", max_length=512, blank=True)
    exec_time = models.DateTimeField(verbose_name="接单时间", auto_now_add=True)

    class Meta:
        db_table = "t_executor"


class WorkOrderCompModel(BaseModel):
    """接单人单位信息表"""""
    name = models.CharField(verbose_name="公司名称", max_length=32)

    class Meta:
        db_table = "t_work_order_comp"


class WorkOrderCompExecutorModel(BaseModel):
    """
    公司售后工单执行人
    """""
    id = models.IntegerField(verbose_name="执行人ID", primary_key=True)
    name = models.CharField(verbose_name="执行人姓名", max_length=16, blank=True, null=True)
    executor_unit = models.SmallIntegerField(verbose_name="执行人单位", choices=((0, "公司售后"),
                                                                            (1, "上海挚达科技发展有限公司"),
                                                                            (2, "电管家能源管理（上海）有限公司"),
                                                                            (9, "其他")), default=0)
    mobile = models.CharField(verbose_name="执行人联系方式", max_length=16)

    class Meta:
        db_table = "t_work_order_comp_executor"


class WorkOrderModel(BaseModel):
    """工单主体"""
    order_num = models.CharField(verbose_name="工单编号", max_length=32)
    status = models.SmallIntegerField(verbose_name="工单状态", choices=(
        (-2, "驳回/待预审"),
        (-1, "废止"),
        (0, "初次待审核"),
        (1, "完成"),
        (2, "待接单"),
        (3, "待执行"),
        (4, "结束待审核"),
        (5, "已初审"),
        (6, "其他")), default=0)
    dispatch_date = models.DateField(verbose_name="派发时间", auto_now_add=True)
    finish_date = models.DateField(verbose_name="预计完成时间")
    work_type = models.SmallIntegerField(verbose_name="工单类型", choices=((1, "支付"), (2, "售后")))
    user_id = models.ForeignKey(UserDetails, models.DO_NOTHING, db_column='user_id', verbose_name="创建人")
    custom_id = models.ForeignKey(CustomInfoModel, models.DO_NOTHING, db_column='custom_id', verbose_name="客户")
    executor_unit = models.SmallIntegerField(verbose_name="执行方",
                                             choices=((0, "公司售后"), (1, "上海挚达科技发展有限公司"),
                                                      (2, "电管家能源管理（上海）有限公司"), (9, "其他")))
    project_id = models.ForeignKey(ProjectInfoModel, models.DO_NOTHING, db_column='project_id', verbose_name="工程")
    c_executor = models.ForeignKey(WorkOrderCompExecutorModel, models.DO_NOTHING,
                                    verbose_name="指定工单执行人", default=None,
                                    blank=True, null=True)
    executor_id = models.ForeignKey(ExecutorModel, models.DO_NOTHING, db_column='executor_id', verbose_name="实际接单人",
                                    blank=True, null=True)
    is_delete = models.SmallIntegerField(verbose_name="逻辑删除", choices=((1, "存在"), (0, "删除")), default=1)
    todo = models.SmallIntegerField(verbose_name="是否执行", choices=((1, '执行完成'), (0, '待执行')), default=0)
    see = models.SmallIntegerField(verbose_name="是否执行", choices=((1, '已查看'), (0, '未查看')), default=0)
    order_weight = models.IntegerField(verbose_name="工单权重", default=0)

    class Meta:
        db_table = "t_work_order"


class ExecContentModel(BaseModel):
    """执行详情表"""
    finish_time = models.DateTimeField(verbose_name="完成时间")
    exec_desc = models.TextField(verbose_name="执行过程描述", blank=True, null=True)
    is_need_offline = models.SmallIntegerField(verbose_name="是否下线", choices=((1, "是"), (0, "否")), default=0)
    clothing = models.CharField(verbose_name="着装图片", max_length=512)
    area_img = models.CharField(verbose_name="场地图片", max_length=512)
    dev_img = models.CharField(verbose_name="设备情况图片", max_length=512)
    service_order_img = models.CharField(verbose_name="服务单图片", max_length=512, blank=True)
    other_img = models.CharField(verbose_name="其他图片", max_length=512, blank=True)
    comments = models.CharField(verbose_name="备注信息", max_length=512, blank=True)

    class Meta:
        db_table = "t_exec_content"


class WorkOrderScheduleModel(BaseModel):
    """工单进度记录表"""
    order_num = models.CharField(verbose_name="工单编号", max_length=32)
    order_status = models.CharField(verbose_name="工单状态", max_length=12)
    name = models.CharField(verbose_name="操作人姓名", max_length=12, blank=True, null=True)
    user_id = models.ForeignKey(UserDetails, models.DO_NOTHING, verbose_name="审核人", blank=True, null=True)
    check_usr = models.SmallIntegerField(verbose_name="审核结果", choices=((0, "驳回"), (1, "通过"), (2, "未审核")),
                                         blank=True, null=True)
    check_type = models.SmallIntegerField(verbose_name="工单维修类别", choices=((1, "大修"), (2, "小修")),
                                          blank=True, null=True)
    check_comments = models.CharField(verbose_name="审核意见", max_length=128, blank=True)
    custom_comments = models.CharField(verbose_name="客户意见", max_length=128, blank=True)
    other_file = models.CharField(verbose_name="附件", max_length=512, blank=True)
    exec_id = models.ForeignKey(ExecutorModel, models.DO_NOTHING, verbose_name="接单人", blank=True, null=True)
    is_process = models.SmallIntegerField(verbose_name="是否处理",
                                          choices=((0, "未处理"), (1, "已处理"), (2, "已初审"), (-1, "驳回"), (-2, "废止")),
                                          default=0)
    exec_content_id = models.ForeignKey(ExecContentModel, models.DO_NOTHING, verbose_name="执行详情", blank=True,
                                        null=True)
    first_check_name = models.CharField(verbose_name="初审人姓名", max_length=12, blank=True, null=True)
    first_check_usr = models.SmallIntegerField(verbose_name="初审结果", choices=((0, "驳回"), (1, "通过"), (2, "未审核")),
                                               blank=True, null=True)
    first_check_comments = models.CharField(verbose_name="初审意见", max_length=128, blank=True)
    first_check_time = models.DateTimeField(verbose_name="初审时间", blank=True, null=True)


    class Meta:
        db_table = "t_work_order_schedule"


class DraftWorkOrderModel(BaseModel):
    """
    草稿
    """""
    user_id = models.IntegerField(verbose_name="用户id", blank=True, null=True)
    order_id = models.IntegerField(verbose_name="工单id", blank=True, null=True)
    history = models.TextField(verbose_name="历史记录", blank=True, null=True)
    new_data = models.TextField(verbose_name='新输入', blank=True, null=True)

    class Meta:
        db_table = "t_work_order_draft"


class WorkOrderAuthorityModel(BaseModel):
    """
    权限
    """""
    user_id = models.ForeignKey(UserDetails, models.DO_NOTHING, verbose_name="用户信息")
    user_authority = models.SmallIntegerField(verbose_name="用户权限",
                                              choices=((1, "超级管理员"),
                                                       (2, "派单人"),
                                                       (3, "接单人"),
                                                       (4, "只读"),
                                                       (5, "审核")))
    unit_group = models.IntegerField(verbose_name="用户分组",
                                     choices=((1, "上海挚达科技发展有限公司"), (0, "公司售后"), (2, "电管家能源管理（上海）有限公司"), (9, "其他")))

    class Meta:
        db_table = "t_work_order_authority"
