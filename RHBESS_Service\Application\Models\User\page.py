#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-05 08:23:09
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\page.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-07-13 13:42:12

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class Page(user_Base):
    u'页面配置表'
    __tablename__ = "t_page"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    descr = Column(String(256), nullable=False, comment=u"页面名称")
    redirect = Column(String(256), nullable=True,comment=u"跳转路径")
    component = Column(String(256), nullable=True,comment=u"路由")
    icon = Column(String(256), nullable=True,comment=u"图标")
    op_ts = Column(DateTime, nullable=False,comment=u"时间")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
   
    page_pageData = relationship("PageData",cascade="all, delete-orphan",passive_deletes=True,backref='page_pageData')

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':%s,'descr':'%s','redirect':'%s','component':'%s','icon':'%s','op_ts':'%s','station':'%s'}" % (
            self.id,self.descr,self.redirect,self.component,self.icon,self.op_ts,self.station)
        
