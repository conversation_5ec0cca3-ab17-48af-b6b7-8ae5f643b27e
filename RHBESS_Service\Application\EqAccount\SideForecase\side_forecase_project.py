#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:51:12
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_project.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-09-21 16:20:26


from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_user import ForecaseUser
from Application.Models.User.province_c import Province
from Application.Models.User.city_c import City
from Application.Models.SideForecase.side_forecase_province import ForecaseProvince
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseProject(user_Base):
    u'项目信息表'
    __tablename__ = "t_side_forecase_project"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    sn = Column(VARCHAR(256), nullable=False, comment="项目编号自动生成")
    # flow = Column(VARCHAR(25), nullable=True, comment="开发流程，存文件路径")
    sop = Column(VARCHAR(256), nullable=False, comment="sop内容，存json")
    # model_file = Column(VARCHAR(25), nullable=True, comment="收资要求及模板下载")
    user_name = Column(VARCHAR(256), nullable=True, comment="客户经理名称")
    user_id = Column(VARCHAR(256), nullable=True, comment="客户经理id")
    province_name = Column(VARCHAR(50),nullable=False, comment="电价省份名称")
    city_name = Column(VARCHAR(50), nullable=False, comment="城市名称")
    owner_type = Column(VARCHAR(100), nullable=True, comment="企业性质二级下拉")
    run_type = Column(VARCHAR(100), nullable=False, comment="经营类二级下拉")
    indus = Column(VARCHAR(100), nullable=False, comment="所属行业二级下拉")
   
    owner_name = Column(VARCHAR(100), nullable=False, comment="企业名称")
    owner_short = Column(VARCHAR(25), nullable=False, comment="企业简称")
    own_status = Column(VARCHAR(25), nullable=False, comment="产权情况")
    other_status = Column(VARCHAR(100), nullable=True, comment="其他情况")
    own_year = Column(VARCHAR(25), nullable=False, comment="产权剩余年限")
    run_day = Column(VARCHAR(255), nullable=True, comment="生产经营天数，列表[1-12月加一个总数]")
    owner_introduce = Column(Text, nullable=True, comment="企业介绍")
    project_level = Column(VARCHAR(5), nullable=True, comment="项目紧急程度，分数")
    project_level_explain = Column(Text, nullable=True, comment="项目紧急程度说明")
    sign_status = Column(VARCHAR(255), nullable=True, comment="签约情况，下拉选择")
    sign_status_explain = Column(Text, nullable=True, comment="签约信息说明")
    # 商务信息
    owner_label = Column(VARCHAR(50), nullable=True, comment="项目类别")
    device_type = Column(VARCHAR(50), nullable=True, comment="设备型号")
    # expect_device_invest = Column(VARCHAR(50), nullable=True, comment="预期主设备报价")
    # expect_invest = Column(VARCHAR(50), nullable=True, comment="预期造价")
    expect_device_num = Column(VARCHAR(50), nullable=True, comment="预期设备数量")
    expect_emc_time = Column(VARCHAR(50), nullable=True, comment="预期EMC合同期限")
    expect_emc_share_type = Column(VARCHAR(50), nullable=True, comment="预期EMC分享模式")
    expect_emc_share = Column(VARCHAR(50), nullable=True, comment="预期EMC分享比例")
    special_content = Column(Text, nullable=True, comment="产品及商务特殊需求")
    area_flag = Column(VARCHAR(50), nullable=True, comment="区域标识")
    update_time = Column(DateTime, nullable=False, comment="更新时间")
    op_ts = Column(DateTime, nullable=False, comment="时间")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment="是否使用1是0否2暂存")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        bean = "{'id':%s,'sn':'%s','user_name':'%s','province_name':'%s','city_name':'%s','owner_name':'%s','own_year':'%s','sign_status':'%s','owner_short':'%s','project_level':'%s',\
            'expect_device_num':'%s','other_status':'%s','expect_emc_time':'%s','expect_emc_share_type':'%s','expect_emc_share':'%s','update_time':'%s','owner_label':'%s'}" %(
            self.id,self.sn,self.user_name,self.province_name,self.city_name,self.owner_name,self.own_year,self.sign_status,
            self.owner_short,self.project_level,self.expect_device_num,self.other_status,self.expect_emc_time,
            self.expect_emc_share_type,self.expect_emc_share,self.update_time,self.owner_label)
        return bean.replace('None','')

   