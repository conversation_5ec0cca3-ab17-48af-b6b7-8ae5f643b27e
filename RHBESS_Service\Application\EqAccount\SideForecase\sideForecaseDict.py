# 经评计算页面初始默认值
side_forecase_default_dict = {
    "project_life": "10年-不更换电池",
    "product_name": "天禄一体柜",
    "product_voltage": "400V接入",
    "product_MW": 0.125,
    "product_MWh": 0.250,
    "charge_eff": 92,
    "discharge_eff": 92,
    "equipment_czl": 5,
    "DOD": 95,
    "EOL": 80,
    "battery_czl": 5,
    "project_cost_all": 1.3,
    "product_cost": 1,
    "engineering_price": 0.3,
    "cost_of_labor": 0.07,
    "material_cost": 0.08,
    "machinery_cost": 0.05,
    "consulting_fee": 0,
    "ops_cost_of_labor": 0.14,
    "product_maintenance_cost": 0.005,
    "premium": 0.001,
    "AAGR": 1,
    "capital_ratio": 100,
    "construction_ratio": 7,
    "construction_cycle": 0.25,
    "construction_deductible_VAT": 0,
    "construction_mode_of_repayment": "到期置换",
    "operation_ratio": 5,
    "operation_cycle": 8,
    "operation_deductible_VAT": 0,
    "operation_mode_of_repayment": "等额本金",
    "use_ratio": 100,
    "EMC_share": "1.固定分享",
    "third_party": 90,
    "change_ratio": 1,
    "first_stage": [
        1,
        20,
        90
    ],
    "second_stage": [
        1,
        20,
        90
    ],
    "subsidy_model": "0.无补贴",
    "subsidy1": {
        "according_to": "按充电电量",
        "price": 0,
        "term": 0,
        "electricity_subsidy": 0,
        "upper_limit": 0,
        "earning_tax_rate": 0
    },
    "subsidy2": {
        "modality": "一次性补贴",
        "according_to": "直流容量",
        "Ac_side_subsidy": 0,
        "Dc_side_subsidy": 0,
        "earning_tax_rate": 0
    },
    "subsidy3": {
        "upper_limit": 0,
        "earning_tax_rate": 0
    }
}

# 经评计算下拉框
side_forecase_dict = {
    "product_dict": {
        "天禄一体柜": {
            "voltage": "400V接入",
            "MW": 0.125,
            "MWh": 0.250
        },
        "372kWh户外一体柜": {
            "voltage": "10kV/35kV接入",
            "MW": 0.15,
            "MWh": 0.372
        },
        "鲲鹏": {
            "voltage": "10kV/35kV接入",
            "MW": 2.5,
            "MWh": 5
        },
        "应龙": {
            "voltage": "10kV/35kV接入",
            "MW": 1.725,
            "MWh": 3.35
        }
    },
    "battery_dict": {
        "10年-不更换电池": {
            "frequency": 7000,
            "lifetime": 10,
            "EOL": "80%"
        },
        "20年-第11年更换电池": {
            "frequency": 7000,
            "lifetime": 10,
            "EOL": "80%"
        },
        "15年-不更换电池": {
            "frequency": 10000,
            "lifetime": 15,
            "EOL": "70%"
        },
        "15年-第8年更换电池": {
            "frequency": 7000,
            "lifetime": 8,
            "EOL": "80%"
        }
    },
    "EMC_list": [
        "1.固定分享",
        "2.回收阶梯分享",
        "3.自定义分享"
    ],
    "policy_subsidy_dict": {
        "0.无补贴": {},
        "1.度电补贴": {
            "according_to": [
                "按充电电量",
                "按放电电量",
                "按循环电量"
            ]
        },
        "2.投资补贴": {
            "modality": [
                "一次性补贴",
                "分年度平均"
            ],
            "according_to": [
                "直流容量",
                "交流容量"
            ]
        },
        "3.自定义补贴": {}
    }
}

# 优化定容下拉框
side_forecase_optimized_dict = {
        "tianl_3.0": {
            "name":"天禄 3.0",
            "power": 125,
            "capacity": 250
        },
        "yingl": {
            "name":"应龙",
            "power": 1675,
            "capacity": 3350
        },
        "yingl_2.0": {
            "name":"应龙 2.0",
            "power": 2500,
            "capacity":5000
        },
        "kunp": {
            "name":"鲲鹏",
            "power": 2500,
            "capacity":5000
        },
        "other": {
            "name":"其他",
            "power": "",
            "capacity":""
        }
}
