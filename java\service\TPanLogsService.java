package com.robestec.analysis.service;

import com.central.common.model.PageResult;
import com.central.common.service.ISuperService;
import com.robestec.analysis.dto.tpanlogs.TPanLogsCreateDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsQueryDTO;
import com.robestec.analysis.dto.tpanlogs.TPanLogsUpdateDTO;
import com.robestec.analysis.entity.TPanLogs;
import com.robestec.analysis.vo.TPanLogsVO;

import java.util.List;

/**
 * 下发日志记录服务接口
 */
public interface TPanLogsService extends ISuperService<TPanLogs> {

    /**
     * 分页查询下发日志记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TPanLogsVO> queryTPanLogs(TPanLogsQueryDTO queryDTO);

    /**
     * 创建下发日志记录
     * @param createDTO 创建参数
     * @return 记录ID
     */
    Long createTPanLogs(TPanLogsCreateDTO createDTO);

    /**
     * 更新下发日志记录
     * @param updateDTO 更新参数
     */
    void updateTPanLogs(TPanLogsUpdateDTO updateDTO);

    /**
     * 删除下发日志记录
     * @param id 记录ID
     */
    void deleteTPanLogs(Long id);

    /**
     * 获取下发日志记录详情
     * @param id 记录ID
     * @return 记录详情
     */
    TPanLogsVO getTPanLogs(Long id);

    /**
     * 批量创建下发日志记录
     * @param createDTOList 创建参数列表
     */
    void createTPanLogsList(List<TPanLogsCreateDTO> createDTOList);

    /**
     * 根据用户ID查询下发日志记录
     * @param userId 用户ID
     * @return 记录列表
     */
    List<TPanLogsVO> getTPanLogsByUserId(Long userId);

    /**
     * 根据电站名称查询下发日志记录
     * @param station 电站名称
     * @return 记录列表
     */
    List<TPanLogsVO> getTPanLogsByStation(String station);

    /**
     * 根据状态查询下发日志记录
     * @param status 状态
     * @return 记录列表
     */
    List<TPanLogsVO> getTPanLogsByStatus(Integer status);

    /**
     * 根据类型名称查询下发日志记录
     * @param typeName 类型名称
     * @return 记录列表
     */
    List<TPanLogsVO> getTPanLogsByTypeName(String typeName);

    /**
     * 统计用户的下发日志记录数量
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(Long userId);

    /**
     * 统计指定状态的下发日志记录数量
     * @param status 状态
     * @return 记录数量
     */
    Long countByStatus(Integer status);
}
