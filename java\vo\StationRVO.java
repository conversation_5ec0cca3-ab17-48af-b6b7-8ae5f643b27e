package com.robestec.analysis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 电站关系VO
 */
@Data
@ApiModel("电站关系VO")
public class StationRVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("电站名称")
    private String stationName;

    @ApiModelProperty("电功率")
    private Double electricPower;

    @ApiModelProperty("运行状态")
    private String runningState;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("储能")
    private String energyStorage;

    @ApiModelProperty("有功功率名称(JSON格式)")
    private String activePowerName;

    @ApiModelProperty("充电容量名称")
    private String chagCapacityName;

    @ApiModelProperty("放电容量名称")
    private String disgCapacityName;

    @ApiModelProperty("电池簇")
    private String batteryCluster;

    @ApiModelProperty("放电容量")
    private String disgCapy;

    @ApiModelProperty("充电容量")
    private String chagCapy;

    @ApiModelProperty("SOC")
    private String soc;

    @ApiModelProperty("监控")
    private String monitor;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;
}
