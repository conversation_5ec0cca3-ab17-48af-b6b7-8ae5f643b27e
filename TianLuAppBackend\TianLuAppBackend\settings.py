"""
Django settings for TianLuAppBackend project.

Generated by 'django-admin startproject' using Django 3.2.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import logging
from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-%t203^)gr&-pk^x8s$&0#hm15f@ft&_j#!+gspk9qji*v7b9*0"

JWT_SECRET_KEY = "django-insecure-%t203^)gr&-pk^x8s$&0#hm15f@ft&_j#!+gspk9qji*v7b9*1"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
# APPEND_SLASH = True  # url 可以不以 / 结束
ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_results",
    "django_celery_beat",
    "apis.user.apps.UserConfig",
    "apis.monitor.apps.MonitorConfig",
    "apis.app2.overview.apps.OverviewConfig",
    "apis.app2.monitor2.apps.Monitor2Config",
    "apis.statistics_apis.apps.StatisticsApisConfig",
    "apis.stored_energy.apps.StoredEnergyConfig",
    "apis.web.apps.WebConfig",
    "apis.project_manage.apps.ProjectManageConfig",
    'apis.work_order.apps.WorkOrderConfig',
    "rest_framework",
    'django_filters',
    'django_apscheduler',
    # 'drf_yasg',
    # "channels",
    "apis.web2.project_dashboard.apps.WebV2DashBoardConfig",
    "apis.web2.project_account.apps.ProjectAccountConfig",
    "apis.web2.message.apps.MessageConfig",
    "apis.web2.running_analysis.apps.RunningAnalysisConfig",
    "apis.web2.battery_analysis.apps.BatteryAnalysisConfig",
]

SYNC_TASK_TOOGLE = True

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "middlewares.LoggingMiddleware.LoggingUrlMiddleware",
    "middlewares.AuthMiddlewares.AuthMiddleware",
    "middlewares.LogRequestsMiddleware.RequestLoggingMiddleware"
]

ROOT_URLCONF = "TianLuAppBackend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "TianLuAppBackend.wsgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 3306,

        # 生产数据库
        "HOST": "*************",  # ip
        'NAME': 'db_tianluapp_prd_fz',  # 数据库名字
        "USER": "anchangdb",
        "PASSWORD": "DataBase@2025",
    },
    'dm_user': {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 3306,

        # 生产环境
        "HOST": "*************",  # ip
        'NAME': 'dm_shanhai_fz',  # 数据库名字
        "USER": "anchangdb",
        "PASSWORD": "DataBase@2025",

    },
    "doris_dwd_rhyc": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9300,
        "HOST": "************",  # ip
        'NAME': 'dwd_rhyc',  # 数据库名字
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "alarm_module": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9030,
        "HOST": "************",  # ip
        'NAME': 'alarm_module',  # 数据库名字  老集群数据库
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "alarm_module_write": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9030,
        "HOST": "************",  # ip
        'NAME': 'alarm_module',  # 数据库名字  老集群数据库
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "doris_dws_rhyc": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9300,
        "HOST": "************",  # ip
        'NAME': 'dws_rhyc',  # 数据库名字
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "doris_ads_rhyc": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9300,
        "HOST": "************",  # ip
        'NAME': 'ads_rhyc',  # 数据库名字
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "doris_origin_rhyc": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 9300,
        "HOST": "************",  # ip
        'NAME': 'rhyc',  # 数据库名字
        "USER": "ac_read",
        "PASSWORD": "Ac#read998",
    },
    "virtual_power_plant": {
        "ENGINE": "django.db.backends.mysql",
        "PORT": 3306,
        "HOST": "*************",  # ip
        'NAME': 'virtual_power_plant',  # 数据库名字
        "USER": "jsds",
        "PASSWORD": "Rhyc@js123",
    }
}

CACHES = {
    '1': {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://*************:6379/",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 1000, "encoding": "utf-8"},
            "PASSWORD": "rhyc@redis1",
        },
    },
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://*************:6379",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 1000, "encoding": "utf-8"},
            "PASSWORD": "rhyc@redis1",
        },
    },
    "3": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://*************:6379/3",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 1000, "encoding": "utf-8"},
            "PASSWORD": "rhyc@redis1",
        },
    }
}
# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static')]


MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# DRF 相关配置
REST_FRAMEWORK = {
    # 'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',  # 接口文档
    "UNAUTHENTICATED_USER": None,  # 匿名用户设置为 None
    "UNAUTHENTICATED_TOKEN": None,  # 匿名用户设置为 None
}

# mqtt 配置
MQTT_SERVER = "************"
# MQTT_SERVER = "************"
MQTT_PORT = 1883
MQTT_KEEPALIVE = 60
MQTT_USER = "work_998"
MQTT_PASSWORD = "work!2#$"

# websocket
ASGI_APPLICATION = "TianLuAppBackend.asgi.application"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
    }
}
# AES 加密秘钥
AES_KEY = "jhuijghdnijhygba"

# 避免时区的问题
CELERY_ENABLE_UTC = False
DJANGO_CELERY_BEAT_TZ_AWARE = False

BASE_LOG_DIR = BASE_DIR / "log"
BASE_LOG_DIR.mkdir(exist_ok=True)

###########
# LOGGING #
###########
import os

BASE_LOG_DIR = BASE_DIR / "log"
BASE_LOG_DIR.mkdir(exist_ok=True)

# The callable to use to configure logging
LOGGING_CONFIG = "logging.config.dictConfig"

# Custom logging configuration.
# 1. 定义字典
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,  # 删除已存在其他日志的Handler
    "formatters": {
        "standard": {
            "format": "{levelname} {asctime} {filename} {module} ：{lineno} {message}",
            "style": "{",
            "datefmt": "%Y-%m-%d %H:%M:%S %p",
        },
        "simple": {
            "format": "[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d] > %(message)s",
            "style": "%",
            "datefmt": "%Y-%m-%d %H:%M:%S %p",
        },
    },
    # "filters": {
    #     "dy": {
    #         "()": "django.utils.log.RequireDebugFalse"
    #     },
    #     "call": {
    #         "()": "django.utils.log.CallbackFilter",
    #         "callback": lambda record: len(record.msg) > 4
    #     }
    # },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "run": {
            # 运行日志，按天自动分割
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "standard",
            # 'filters': ["dy", 'call'],
            "filename": os.path.join(BASE_LOG_DIR, "success.log"),
            "when": "D",  # 根据天拆分日志
            "interval": 1,  # 1天
            "backupCount": 7,  # 保留备份
            "encoding": "utf-8",
        },
        "error": {
            # 错误日志，按照文件大小分割
            "class": "logging.handlers.RotatingFileHandler",
            "formatter": "standard",
            "filename": os.path.join(BASE_LOG_DIR, "error.log"),
            "maxBytes": 1024 * 1025 * 50,  # 根据文件大小拆分日志 50M
            "backupCount": 5,
            "encoding": "utf-8",
        },
        "exception": {
            # 错误日志，按照文件大小分割
            "class": "logging.handlers.RotatingFileHandler",
            "formatter": "standard",
            "filename": os.path.join(BASE_LOG_DIR, "exception.log"),
            "maxBytes": 1024 * 1025 * 50,  # 根据文件大小拆分日志 50M
            "backupCount": 5,
            "encoding": "utf-8",
        },
    },
    "loggers": {
        "run": {"handlers": ["run"], "level": "INFO", "propagate": True},  # >=20 则触发日志
        "error": {"handlers": ["console", "error"], "level": "ERROR", "propagate": False},  # >=40 则触发日志
        "exception": {"handlers": ["console", "error", "exception"], "level": "ERROR", "propagate": False},  # >=40 则触发日志
    },
    "root": {"handlers": ["console"], "level": "DEBUG", "propagate": True},
}
ERROR_LOG = logging.getLogger("error")
SUCCESS_LOG = logging.getLogger("run")
Exception_LOG = logging.getLogger("exception")

AES_REPORT_KEY_ENC = b"RHBESS1101022022"  # 电芯报告 aes 加密key
AES_REPORT_IV_ENC = b"1101020090002022"  # 电芯报告 aes  加密iv
MD5_OPENID = b"RHBESS01"
AES_REPORT_KEY_DEC = b"fastfuncn1234567"  # 电芯报告 aes 解密key
AES_REPORT_IV_DEC = b"fastfuncn1234567"  # 电芯报告 aes  解密iv


# 不需要记录日志的请求路径，用于排除需要记录日志的请求，例如: 不记录 /excluded-path/ 的请求日志
EXCLUDED_PATHS = [
    '/stored_energy/unalarm/add/',
    '/workorder/fileupload/',
    '/projects_manage/projects/upload/',
    '/statistics/custom_income/upload',
    '/projects_manage/projects/upload/'
]

IS_NEW_ELECTRIC_PRICE_CONFIG = True

# 策略数据粒度 True用15分钟的逻辑，False用1小时的逻辑
FORMATEF = True

# settings.py

