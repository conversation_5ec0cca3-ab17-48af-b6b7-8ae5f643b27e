from Tools.DB.mysql_user import user_Base
from Tools.DB.mysql_user import Integer, String, Column, Float


class ReferenceStandard(user_Base):
    u'定容参考标准'
    __tablename__ = "t_side_reference_standard"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(String(32), nullable=True, comment=u"省份名称")
    count = Column(Float(asdecimal=True), nullable=True, comment=u"日均循环此时")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()


