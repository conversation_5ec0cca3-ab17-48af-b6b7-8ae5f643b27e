#!/usr/bin/env python
# coding=utf-8
#@Information:智锂物联实时数据
#<AUTHOR> WYJ
#@Date         : 2022-10-24 15:55:43
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\Foreign\zhilRealHandle.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-03-03 09:18:02



import json,os
import logging
from Application.Models.base_handler import BaseHandler
import tornado.web
from Tools.Utils.num_utils import *
from Tools.DataEnDe.aes_cbc import AESUtil
from Tools.DataEnDe.MD5 import MD5Tool

gz_no ={2001:"FW1CelUT", 2002:"FW1CelOT", 2003:"FW1CelUV", 2004:"FW1CelOV", 2005:"FW1CelDT", 2006:"FW1RkUV", 2007:"FW1RkOV", 2008:"FW1RkChgOC", 2009:"FW1RkDisOC", 3001:"FW1SCPt", 
2012:"Rk1FW2CelDV", 3002:"Rk1FW2DcCtF", 1001:"AWCelUT", 1002:"AWCelOT", 1003:"AWCelUV", 1004:"AWCelOV", 1005:"AWCelDV", 1006:"AWRkUV", 1007:"AWRkOV", 1008:"AWRkChgOC", 1009:"AWRkDisOC"}
# 单体电压
cell_V = {'V138': '.SgV138', 'V139': '.SgV139', 'V212': '.SgV212', 'V132': '.SgV132', 'V133': '.SgV133', 'V130': '.SgV130', 'V131': '.SgV131', 'V136': '.SgV136', 'V137': '.SgV137', 'V134': '.SgV134', 'V135': '.SgV135', 'V216': '.SgV216', 'V128': '.SgV128', 'V239': '.SgV239', 'V238': '.SgV238', 'V235': '.SgV235', 'V234': '.SgV234', 'V237': '.SgV237', 'V236': '.SgV236', 'V231': '.SgV231', 'V230': '.SgV230', 'V233': '.SgV233', 'V232': '.SgV232', 'V69': '.SgV069', 'V68': '.SgV068', 'V67': '.SgV067', 'V66': '.SgV066', 'V65': '.SgV065', 'V64': '.SgV064', 'V63': '.SgV063', 'V62': '.SgV062', 'V61': '.SgV061', 'V60': '.SgV060', 'V274': '.SgV274', 'V191': '.SgV191', 'V265': '.SgV265', 'V248': '.SgV248', 'V161': '.SgV161', 'V160': '.SgV160', 'V240': '.SgV240', 'V241': '.SgV241', 'V242': '.SgV242', 'V243': '.SgV243', 'V244': '.SgV244', 'V245': '.SgV245', 'V246': '.SgV246', 'V247': '.SgV247', 'V78': '.SgV078', 'V79': '.SgV079', 'V74': '.SgV074', 'V75': '.SgV075', 'V76': '.SgV076', 'V77': '.SgV077', 'V70': '.SgV070', 'V71': '.SgV071', 'V72': '.SgV072', 'V73': '.SgV073', 'V259': '.SgV259', 'V190': '.SgV190', 'V267': '.SgV267', 'V168': '.SgV168', 'V219': '.SgV219', 'V41': '.SgV041', 'V40': '.SgV040', 'V43': '.SgV043', 'V42': '.SgV042', 'V45': '.SgV045', 'V44': '.SgV044', 'V47': '.SgV047', 'V46': '.SgV046', 'V49': '.SgV049', 'V48': '.SgV048', 'V251': '.SgV251', 'V250': '.SgV250', 'V257': '.SgV257', 'V256': '.SgV256', 'V255': '.SgV255', 'V254': '.SgV254', 'V109': '.SgV109', 'V211': '.SgV211', 'V108': '.SgV108', 'V125': '.SgV125', 'V124': '.SgV124', 'V127': '.SgV127', 'V126': '.SgV126', 'V121': '.SgV121', 'V120': '.SgV120', 'V123': '.SgV123', 'V122': '.SgV122', 'V129': '.SgV129', 'V105': '.SgV105', 'V266': '.SgV266', 'V221': '.SgV221', 'V264': '.SgV264', 'V104': '.SgV104', 'V210': '.SgV210', 'V263': '.SgV263', 'V260': '.SgV260', 'V261': '.SgV261', 'V268': '.SgV268', 'V269': '.SgV269', 'V56': '.SgV056', 'V57': '.SgV057', 'V54': '.SgV054', 'V55': '.SgV055', 'V52': '.SgV052', 'V53': '.SgV053', 'V50': '.SgV050', 'V51': '.SgV051', 'V58': '.SgV058', 'V59': '.SgV059', 'V222': '.SgV222', 'V223': '.SgV223', 'V220': '.SgV220', 'V150': '.SgV150', 'V151': '.SgV151', 'V152': '.SgV152', 'V153': '.SgV153', 'V154': '.SgV154', 'V155': '.SgV155', 'V156': '.SgV156', 'V157': '.SgV157', 'V158': '.SgV158', 'V159': '.SgV159', 'V213': '.SgV213', 'V227': '.SgV227', 'V271': '.SgV271', 'V270': '.SgV270', 'V273': '.SgV273', 'V272': '.SgV272', 'V275': '.SgV275', 'V224': '.SgV224', 'V276': '.SgV276', 'V225': '.SgV225', 'V258': '.SgV258', 'V23': '.SgV023', 'V22': '.SgV022', 'V21': '.SgV021', 'V20': '.SgV020', 'V27': '.SgV027', 'V26': '.SgV026', 'V25': '.SgV025', 'V24': '.SgV024', 'V29': '.SgV029', 'V28': '.SgV028', 'V143': '.SgV143', 'V142': '.SgV142', 'V141': '.SgV141', 'V140': '.SgV140', 'V147': '.SgV147', 'V146': '.SgV146', 'V145': '.SgV145', 'V144': '.SgV144', 'V149': '.SgV149', 'V148': '.SgV148', 'V252': '.SgV252', 'V204': '.SgV204', 'V205': '.SgV205', 'V206': '.SgV206', 'V207': '.SgV207', 'V200': '.SgV200', 'V201': '.SgV201', 'V202': '.SgV202', 'V203': '.SgV203', 'V208': '.SgV208', 'V209': '.SgV209', 'V30': '.SgV030', 'V31': '.SgV031', 'V32': '.SgV032', 'V33': '.SgV033', 'V34': '.SgV034', 'V35': '.SgV035', 'V36': '.SgV036', 'V37': '.SgV037', 'V38': '.SgV038', 'V39': '.SgV039', 'V215': '.SgV215', 'V218': '.SgV218', 'V214': '.SgV214', 'V178': '.SgV178', 'V179': '.SgV179', 'V176': '.SgV176', 'V177': '.SgV177', 'V174': '.SgV174', 'V175': '.SgV175', 'V172': '.SgV172', 'V173': '.SgV173', 'V170': '.SgV170', 'V171': '.SgV171', 'V118': '.SgV118', 'V119': '.SgV119', 'V114': '.SgV114', 'V115': '.SgV115', 'V116': '.SgV116', 'V117': '.SgV117', 'V110': '.SgV110', 'V111': '.SgV111', 'V112': '.SgV112', 'V113': '.SgV113', 'V194': '.SgV194', 'V195': '.SgV195', 'V196': '.SgV196', 'V197': '.SgV197', 'V89': '.SgV089', 'V88': '.SgV088', 'V192': '.SgV192', 'V193': '.SgV193', 'V85': '.SgV085', 'V84': '.SgV084', 'V87': '.SgV087', 'V86': '.SgV086', 'V81': '.SgV081', 'V80': '.SgV080', 'V83': '.SgV083', 'V82': '.SgV082', 'V198': '.SgV198', 'V199': '.SgV199', 'V169': '.SgV169', 'V1': '.SgV001', 'V2': '.SgV002', 'V3': '.SgV003', 'V4': '.SgV004', 'V5': '.SgV005', 'V6': '.SgV006', 'V7': '.SgV007', 'V8': '.SgV008', 'V9': '.SgV009', 'V163': '.SgV163', 'V162': '.SgV162', 'V165': '.SgV165', 'V164': '.SgV164', 'V167': '.SgV167', 'V166': '.SgV166', 'V92': '.SgV092', 'V93': '.SgV093', 'V90': '.SgV090', 'V91': '.SgV091', 'V96': '.SgV096', 'V97': '.SgV097', 'V94': '.SgV094', 'V95': '.SgV095', 'V107': '.SgV107', 'V106': '.SgV106', 'V98': '.SgV098', 'V99': '.SgV099', 'V103': '.SgV103', 'V102': '.SgV102', 'V101': '.SgV101', 'V100': '.SgV100', 'V187': '.SgV187', 'V186': '.SgV186', 'V185': '.SgV185', 'V184': '.SgV184', 'V183': '.SgV183', 'V182': '.SgV182', 'V181': '.SgV181', 'V180': '.SgV180', 'V228': '.SgV228', 'V229': '.SgV229', 'V189': '.SgV189', 'V188': '.SgV188', 'V18': '.SgV018', 'V19': '.SgV019', 'V249': '.SgV249', 'V12': '.SgV012', 'V13': '.SgV013', 'V10': '.SgV010', 'V11': '.SgV011', 'V16': '.SgV016', 'V17': '.SgV017', 'V14': '.SgV014', 'V15': '.SgV015', 'V262': '.SgV262', 'V217': '.SgV217', 'V226': '.SgV226', 'V253': '.SgV253','V277': '.SgV277', 'V278': '.SgV278', 'V279': '.SgV279', 'V280': '.SgV280', 'V281': '.SgV281', 'V282': '.SgV282', 'V283': '.SgV283', 'V284': '.SgV284', 'V285': '.SgV285', 'V286': '.SgV286', 'V287': '.SgV287', 'V288': '.SgV288', 'V289': '.SgV289', 'V290': '.SgV290', 'V291': '.SgV291', 'V292': '.SgV292', 'V293': '.SgV293', 'V294': '.SgV294', 'V295': '.SgV295', 'V296': '.SgV296', 'V297': '.SgV297', 'V298': '.SgV298', 'V299': '.SgV299', 'V300': '.SgV300', 'V301': '.SgV301', 'V302': '.SgV302', 'V303': '.SgV303', 'V304': '.SgV304', 'V305': '.SgV305', 'V306': '.SgV306', 'V307': '.SgV307', 'V308': '.SgV308', 'V309': '.SgV309', 'V310': '.SgV310', 'V311': '.SgV311', 'V312': '.SgV312', 'V313': '.SgV313', 'V314': '.SgV314', 'V315': '.SgV315', 'V316': '.SgV316', 'V317': '.SgV317', 'V318': '.SgV318', 'V319': '.SgV319', 'V320': '.SgV320', 'V321': '.SgV321', 'V322': '.SgV322', 'V323': '.SgV323', 'V324': '.SgV324', 'V325': '.SgV325', 'V326': '.SgV326', 'V327': '.SgV327', 'V328': '.SgV328', 'V329': '.SgV329', 'V330': '.SgV330', 'V331': '.SgV331', 'V332': '.SgV332', 'V333': '.SgV333', 'V334': '.SgV334', 'V335': '.SgV335', 'V336': '.SgV336', 'V337': '.SgV337', 'V338': '.SgV338', 'V339': '.SgV339', 'V340': '.SgV340', 'V341': '.SgV341', 'V342': '.SgV342', 'V343': '.SgV343', 'V344': '.SgV344', 'V345': '.SgV345', 'V346': '.SgV346', 'V347': '.SgV347', 'V348': '.SgV348', 'V349': '.SgV349', 'V350': '.SgV350', 'V351': '.SgV351', 'V352': '.SgV352', 'V353': '.SgV353', 'V354': '.SgV354', 'V355': '.SgV355', 'V356': '.SgV356', 'V357': '.SgV357', 'V358': '.SgV358', 'V359': '.SgV359', 'V360': '.SgV360', 'V361': '.SgV361', 'V362': '.SgV362', 'V363': '.SgV363', 'V364': '.SgV364', 'V365': '.SgV365', 'V366': '.SgV366', 'V367': '.SgV367', 'V368': '.SgV368', 'V369': '.SgV369', 'V370': '.SgV370', 'V371': '.SgV371', 'V372': '.SgV372', 'V373': '.SgV373', 'V374': '.SgV374', 'V375': '.SgV375', 'V376': '.SgV376', 'V377': '.SgV377', 'V378': '.SgV378', 'V379': '.SgV379', 'V380': '.SgV380', 'V381': '.SgV381', 'V382': '.SgV382', 'V383': '.SgV383', 'V384': '.SgV384', 'V385': '.SgV385', 'V386': '.SgV386', 'V387': '.SgV387', 'V388': '.SgV388', 'V389': '.SgV389', 'V390': '.SgV390', 'V391': '.SgV391', 'V392': '.SgV392', 'V393': '.SgV393', 'V394': '.SgV394', 'V395': '.SgV395', 'V396': '.SgV396', 'V397': '.SgV397', 'V398': '.SgV398', 'V399': '.SgV399', 'V400': '.SgV400', 'V401': '.SgV401', 'V402': '.SgV402', 'V403': '.SgV403', 'V404': '.SgV404', 'V405': '.SgV405', 'V406': '.SgV406', 'V407': '.SgV407', 'V408': '.SgV408', 'V409': '.SgV409', 'V410': '.SgV410', 'V411': '.SgV411', 'V412': '.SgV412', 'V413': '.SgV413', 'V414': '.SgV414', 'V415': '.SgV415', 'V416': '.SgV416'}
# 单体温度
cell_T={'T152': '.SgT152', 'T153': '.SgT153', 'T150': '.SgT150', 'T151': '.SgT151', 'T156': '.SgT156', 'T157': '.SgT157', 'T154': '.SgT154', 'T155': '.SgT155', 'T185': '.SgT185', 'T158': '.SgT158', 'T159': '.SgT159', 'T217': '.SgT217', 'T184': '.SgT184', 'T72': '.SgT072', 'T73': '.SgT073', 'T70': '.SgT070', 'T71': '.SgT071', 'T76': '.SgT076', 'T77': '.SgT077', 'T74': '.SgT074', 'T75': '.SgT075', 'T78': '.SgT078', 'T79': '.SgT079', 'T248': '.SgT248', 'T249': '.SgT249', 'T246': '.SgT246', 'T247': '.SgT247', 'T244': '.SgT244', 'T245': '.SgT245', 'T242': '.SgT242', 'T182': '.SgT182', 'T240': '.SgT240', 'T241': '.SgT241', 'T219': '.SgT219', 'T228': '.SgT228', 'T229': '.SgT229', 'T194': '.SgT194', 'T149': '.SgT149', 'T148': '.SgT148', 'T145': '.SgT145', 'T144': '.SgT144', 'T147': '.SgT147', 'T146': '.SgT146', 'T141': '.SgT141', 'T140': '.SgT140', 'T143': '.SgT143', 'T142': '.SgT142', 'T224': '.SgT224', 'T216': '.SgT216', 'T225': '.SgT225', 'T226': '.SgT226', 'T197': '.SgT197', 'T259': '.SgT259', 'T258': '.SgT258', 'T227': '.SgT227', 'T198': '.SgT198', 'T251': '.SgT251', 'T220': '.SgT220', 'T253': '.SgT253', 'T212': '.SgT212', 'T255': '.SgT255', 'T254': '.SgT254', 'T257': '.SgT257', 'T221': '.SgT221', 'T196': '.SgT196', 'T222': '.SgT222', 'T89': '.SgT089', 'T88': '.SgT088', 'T87': '.SgT087', 'T86': '.SgT086', 'T85': '.SgT085', 'T84': '.SgT084', 'T83': '.SgT083', 'T82': '.SgT082', 'T81': '.SgT081', 'T80': '.SgT080', 'T138': '.SgT138', 'T139': '.SgT139', 'T195': '.SgT195', 'T268': '.SgT268', 'T213': '.SgT213', 'T187': '.SgT187', 'T130': '.SgT130', 'T131': '.SgT131', 'T132': '.SgT132', 'T133': '.SgT133', 'T134': '.SgT134', 'T135': '.SgT135', 'T136': '.SgT136', 'T137': '.SgT137', 'T14': '.SgT014', 'T15': '.SgT015', 'T16': '.SgT016', 'T17': '.SgT017', 'T10': '.SgT010', 'T11': '.SgT011', 'T12': '.SgT012', 'T13': '.SgT013', 'T191': '.SgT191', 'T18': '.SgT018', 'T19': '.SgT019', 'T161': '.SgT161', 'T160': '.SgT160', 'T98': '.SgT098', 'T99': '.SgT099', 'T94': '.SgT094', 'T95': '.SgT095', 'T96': '.SgT096', 'T97': '.SgT097', 'T90': '.SgT090', 'T91': '.SgT091', 'T92': '.SgT092', 'T93': '.SgT093', 'T129': '.SgT129', 'T128': '.SgT128', 'T123': '.SgT123', 'T122': '.SgT122', 'T121': '.SgT121', 'T120': '.SgT120', 'T127': '.SgT127', 'T126': '.SgT126', 'T125': '.SgT125', 'T124': '.SgT124', 'T29': '.SgT029', 'T28': '.SgT028', 'T193': '.SgT193', 'T21': '.SgT021', 'T20': '.SgT020', 'T23': '.SgT023', 'T22': '.SgT022', 'T25': '.SgT025', 'T24': '.SgT024', 'T27': '.SgT027', 'T26': '.SgT026', 'T192': '.SgT192', 'T189': '.SgT189', 'T190': '.SgT190', 'T239': '.SgT239', 'T238': '.SgT238', 'T237': '.SgT237', 'T236': '.SgT236', 'T235': '.SgT235', 'T234': '.SgT234', 'T233': '.SgT233', 'T232': '.SgT232', 'T231': '.SgT231', 'T230': '.SgT230', 'T188': '.SgT188', 'T243': '.SgT243', 'T38': '.SgT038', 'T39': '.SgT039', 'T36': '.SgT036', 'T37': '.SgT037', 'T34': '.SgT034', 'T35': '.SgT035', 'T32': '.SgT032', 'T33': '.SgT033', 'T30': '.SgT030', 'T31': '.SgT031', 'T250': '.SgT250', 'T208': '.SgT208', 'T209': '.SgT209', 'T202': '.SgT202', 'T203': '.SgT203', 'T200': '.SgT200', 'T201': '.SgT201', 'T206': '.SgT206', 'T207': '.SgT207', 'T204': '.SgT204', 'T205': '.SgT205', 'T252': '.SgT252', 'T186': '.SgT186', 'T223': '.SgT223', 'T218': '.SgT218', 'T276': '.SgT276', 'T215': '.SgT215', 'T214': '.SgT214', 'T49': '.SgT049', 'T48': '.SgT048', 'T211': '.SgT211', 'T210': '.SgT210', 'T168': '.SgT168', 'T199': '.SgT199', 'T43': '.SgT043', 'T42': '.SgT042', 'T41': '.SgT041', 'T40': '.SgT040', 'T47': '.SgT047', 'T46': '.SgT046', 'T45': '.SgT045', 'T44': '.SgT044', 'T116': '.SgT116', 'T117': '.SgT117', 'T114': '.SgT114', 'T115': '.SgT115', 'T112': '.SgT112', 'T113': '.SgT113', 'T110': '.SgT110', 'T111': '.SgT111', 'T118': '.SgT118', 'T119': '.SgT119', 'T174': '.SgT174', 'T175': '.SgT175', 'T176': '.SgT176', 'T177': '.SgT177', 'T170': '.SgT170', 'T171': '.SgT171', 'T172': '.SgT172', 'T173': '.SgT173', 'T178': '.SgT178', 'T179': '.SgT179', 'T58': '.SgT058', 'T59': '.SgT059', 'T262': '.SgT262', 'T263': '.SgT263', 'T264': '.SgT264', 'T265': '.SgT265', 'T266': '.SgT266', 'T267': '.SgT267', 'T50': '.SgT050', 'T51': '.SgT051', 'T52': '.SgT052', 'T53': '.SgT053', 'T54': '.SgT054', 'T55': '.SgT055', 'T56': '.SgT056', 'T57': '.SgT057', 'T101': '.SgT101', 'T100': '.SgT100', 'T103': '.SgT103', 'T102': '.SgT102', 'T105': '.SgT105', 'T104': '.SgT104', 'T107': '.SgT107', 'T106': '.SgT106', 'T109': '.SgT109', 'T108': '.SgT108', 'T167': '.SgT167', 'T166': '.SgT166', 'T165': '.SgT165', 'T164': '.SgT164', 'T163': '.SgT163', 'T162': '.SgT162', 'T8': '.SgT008', 'T9': '.SgT009', 'T6': '.SgT006', 'T7': '.SgT007', 'T4': '.SgT004', 'T5': '.SgT005', 'T2': '.SgT002', 'T3': '.SgT003', 'T169': '.SgT169', 'T1': '.SgT001', 'T260': '.SgT260', 'T261': '.SgT261', 'T269': '.SgT269', 'T256': '.SgT256', 'T273': '.SgT273', 'T272': '.SgT272', 'T271': '.SgT271', 'T270': '.SgT270', 'T69': '.SgT069', 'T68': '.SgT068', 'T275': '.SgT275', 'T274': '.SgT274', 'T65': '.SgT065', 'T64': '.SgT064', 'T67': '.SgT067', 'T66': '.SgT066', 'T61': '.SgT061', 'T60': '.SgT060', 'T63': '.SgT063', 'T62': '.SgT062', 'T181': '.SgT181', 'T180': '.SgT180', 'T183': '.SgT183'}
# 单体soc
cell_SOC={'SOC79': '.SgSoc079', 'SOC78': '.SgSoc078', 'SOC248': '.SgSoc248', 'SOC71': '.SgSoc071', 'SOC70': '.SgSoc070', 'SOC73': '.SgSoc073', 'SOC72': '.SgSoc072', 'SOC75': '.SgSoc075', 'SOC74': '.SgSoc074', 'SOC77': '.SgSoc077', 'SOC76': '.SgSoc076', 'SOC188': '.SgSoc188', 'SOC189': '.SgSoc189', 'SOC184': '.SgSoc184', 'SOC185': '.SgSoc185', 'SOC186': '.SgSoc186', 'SOC187': '.SgSoc187', 'SOC180': '.SgSoc180', 'SOC181': '.SgSoc181', 'SOC182': '.SgSoc182', 'SOC183': '.SgSoc183', 'SOC84': '.SgSoc084', 'SOC85': '.SgSoc085', 'SOC86': '.SgSoc086', 'SOC87': '.SgSoc087', 'SOC80': '.SgSoc080', 'SOC81': '.SgSoc081', 'SOC82': '.SgSoc082', 'SOC83': '.SgSoc083', 'SOC88': '.SgSoc088', 'SOC89': '.SgSoc089', 'SOC225': '.SgSoc225', 'SOC224': '.SgSoc224', 'SOC227': '.SgSoc227', 'SOC226': '.SgSoc226', 'SOC221': '.SgSoc221', 'SOC220': '.SgSoc220', 'SOC223': '.SgSoc223', 'SOC176': '.SgSoc176', 'SOC229': '.SgSoc229', 'SOC228': '.SgSoc228', 'SOC146': '.SgSoc146', 'SOC274': '.SgSoc274', 'SOC68': '.SgSoc068', 'SOC69': '.SgSoc069', 'SOC209': '.SgSoc209', 'SOC62': '.SgSoc062', 'SOC63': '.SgSoc063', 'SOC60': '.SgSoc060', 'SOC61': '.SgSoc061', 'SOC66': '.SgSoc066', 'SOC67': '.SgSoc067', 'SOC64': '.SgSoc064', 'SOC65': '.SgSoc065', 'SOC199': '.SgSoc199', 'SOC198': '.SgSoc198', 'SOC171': '.SgSoc171', 'SOC193': '.SgSoc193', 'SOC192': '.SgSoc192', 'SOC191': '.SgSoc191', 'SOC190': '.SgSoc190', 'SOC197': '.SgSoc197', 'SOC196': '.SgSoc196', 'SOC195': '.SgSoc195', 'SOC194': '.SgSoc194', 'SOC113': '.SgSoc113', 'SOC112': '.SgSoc112', 'SOC111': '.SgSoc111', 'SOC110': '.SgSoc110', 'SOC117': '.SgSoc117', 'SOC116': '.SgSoc116', 'SOC115': '.SgSoc115', 'SOC114': '.SgSoc114', 'SOC119': '.SgSoc119', 'SOC118': '.SgSoc118', 'SOC210': '.SgSoc210', 'SOC211': '.SgSoc211', 'SOC212': '.SgSoc212', 'SOC170': '.SgSoc170', 'SOC214': '.SgSoc214', 'SOC201': '.SgSoc201', 'SOC216': '.SgSoc216', 'SOC217': '.SgSoc217', 'SOC218': '.SgSoc218', 'SOC219': '.SgSoc219', 'SOC200': '.SgSoc200', 'SOC213': '.SgSoc213', 'SOC208': '.SgSoc208', 'SOC275': '.SgSoc275', 'SOC236': '.SgSoc236', 'SOC166': '.SgSoc166', 'SOC167': '.SgSoc167', 'SOC164': '.SgSoc164', 'SOC165': '.SgSoc165', 'SOC162': '.SgSoc162', 'SOC163': '.SgSoc163', 'SOC160': '.SgSoc160', 'SOC161': '.SgSoc161', 'SOC207': '.SgSoc207', 'SOC206': '.SgSoc206', 'SOC205': '.SgSoc205', 'SOC204': '.SgSoc204', 'SOC203': '.SgSoc203', 'SOC202': '.SgSoc202', 'SOC168': '.SgSoc168', 'SOC169': '.SgSoc169', 'SOC104': '.SgSoc104', 'SOC276': '.SgSoc276', 'SOC105': '.SgSoc105', 'SOC106': '.SgSoc106', 'SOC215': '.SgSoc215', 'SOC107': '.SgSoc107', 'SOC238': '.SgSoc238', 'SOC239': '.SgSoc239', 'SOC100': '.SgSoc100', 'SOC237': '.SgSoc237', 'SOC101': '.SgSoc101', 'SOC102': '.SgSoc102', 'SOC103': '.SgSoc103', 'SOC269': '.SgSoc269', 'SOC57': '.SgSoc057', 'SOC56': '.SgSoc056', 'SOC55': '.SgSoc055', 'SOC54': '.SgSoc054', 'SOC53': '.SgSoc053', 'SOC52': '.SgSoc052', 'SOC51': '.SgSoc051', 'SOC50': '.SgSoc050', 'SOC272': '.SgSoc272', 'SOC273': '.SgSoc273', 'SOC270': '.SgSoc270', 'SOC271': '.SgSoc271', 'SOC179': '.SgSoc179', 'SOC178': '.SgSoc178', 'SOC59': '.SgSoc059', 'SOC58': '.SgSoc058', 'SOC35': '.SgSoc035', 'SOC34': '.SgSoc034', 'SOC37': '.SgSoc037', 'SOC36': '.SgSoc036', 'SOC31': '.SgSoc031', 'SOC30': '.SgSoc030', 'SOC33': '.SgSoc033', 'SOC32': '.SgSoc032', 'SOC108': '.SgSoc108', 'SOC39': '.SgSoc039', 'SOC38': '.SgSoc038', 'SOC109': '.SgSoc109', 'SOC7': '.SgSoc007', 'SOC6': '.SgSoc006', 'SOC5': '.SgSoc005', 'SOC4': '.SgSoc004', 'SOC3': '.SgSoc003', 'SOC2': '.SgSoc002', 'SOC1': '.SgSoc001', 'SOC9': '.SgSoc009', 'SOC8': '.SgSoc008', 'SOC140': '.SgSoc140', 'SOC144': '.SgSoc144', 'SOC141': '.SgSoc141', 'SOC142': '.SgSoc142', 'SOC40': '.SgSoc040', 'SOC41': '.SgSoc041', 'SOC42': '.SgSoc042', 'SOC43': '.SgSoc043', 'SOC44': '.SgSoc044', 'SOC45': '.SgSoc045', 'SOC46': '.SgSoc046', 'SOC47': '.SgSoc047', 'SOC48': '.SgSoc048', 'SOC49': '.SgSoc049', 'SOC263': '.SgSoc263', 'SOC262': '.SgSoc262', 'SOC265': '.SgSoc265', 'SOC264': '.SgSoc264', 'SOC267': '.SgSoc267', 'SOC145': '.SgSoc145', 'SOC26': '.SgSoc026', 'SOC27': '.SgSoc027', 'SOC24': '.SgSoc024', 'SOC25': '.SgSoc025', 'SOC22': '.SgSoc022', 'SOC23': '.SgSoc023', 'SOC20': '.SgSoc020', 'SOC21': '.SgSoc021', 'SOC147': '.SgSoc147', 'SOC28': '.SgSoc028', 'SOC29': '.SgSoc029', 'SOC148': '.SgSoc148', 'SOC149': '.SgSoc149', 'SOC254': '.SgSoc254', 'SOC255': '.SgSoc255', 'SOC256': '.SgSoc256', 'SOC257': '.SgSoc257', 'SOC250': '.SgSoc250', 'SOC251': '.SgSoc251', 'SOC252': '.SgSoc252', 'SOC253': '.SgSoc253', 'SOC258': '.SgSoc258', 'SOC259': '.SgSoc259', 'SOC235': '.SgSoc235', 'SOC159': '.SgSoc159', 'SOC158': '.SgSoc158', 'SOC157': '.SgSoc157', 'SOC156': '.SgSoc156', 'SOC155': '.SgSoc155', 'SOC154': '.SgSoc154', 'SOC153': '.SgSoc153', 'SOC152': '.SgSoc152', 'SOC151': '.SgSoc151', 'SOC150': '.SgSoc150', 'SOC13': '.SgSoc013', 'SOC12': '.SgSoc012', 'SOC11': '.SgSoc011', 'SOC10': '.SgSoc010', 'SOC17': '.SgSoc017', 'SOC16': '.SgSoc016', 'SOC15': '.SgSoc015', 'SOC14': '.SgSoc014', 'SOC19': '.SgSoc019', 'SOC18': '.SgSoc018', 'SOC268': '.SgSoc268', 'SOC260': '.SgSoc260', 'SOC243': '.SgSoc243', 'SOC175': '.SgSoc175', 'SOC241': '.SgSoc241', 'SOC240': '.SgSoc240', 'SOC247': '.SgSoc247', 'SOC246': '.SgSoc246', 'SOC245': '.SgSoc245', 'SOC174': '.SgSoc174', 'SOC249': '.SgSoc249', 'SOC233': '.SgSoc233', 'SOC177': '.SgSoc177', 'SOC128': '.SgSoc128', 'SOC129': '.SgSoc129', 'SOC122': '.SgSoc122', 'SOC123': '.SgSoc123', 'SOC120': '.SgSoc120', 'SOC121': '.SgSoc121', 'SOC126': '.SgSoc126', 'SOC127': '.SgSoc127', 'SOC124': '.SgSoc124', 'SOC125': '.SgSoc125', 'SOC266': '.SgSoc266', 'SOC222': '.SgSoc222', 'SOC173': '.SgSoc173', 'SOC242': '.SgSoc242', 'SOC143': '.SgSoc143', 'SOC172': '.SgSoc172', 'SOC234': '.SgSoc234', 'SOC131': '.SgSoc131', 'SOC130': '.SgSoc130', 'SOC133': '.SgSoc133', 'SOC261': '.SgSoc261', 'SOC132': '.SgSoc132', 'SOC136': '.SgSoc136', 'SOC135': '.SgSoc135', 'SOC244': '.SgSoc244', 'SOC134': '.SgSoc134', 'SOC139': '.SgSoc139', 'SOC138': '.SgSoc138', 'SOC99': '.SgSoc099', 'SOC98': '.SgSoc098', 'SOC232': '.SgSoc232', 'SOC137': '.SgSoc137', 'SOC230': '.SgSoc230', 'SOC231': '.SgSoc231', 'SOC93': '.SgSoc093', 'SOC92': '.SgSoc092', 'SOC91': '.SgSoc091', 'SOC90': '.SgSoc090', 'SOC97': '.SgSoc097', 'SOC96': '.SgSoc096', 'SOC95': '.SgSoc095', 'SOC94': '.SgSoc094'}
# 单体soh
cell_SOH={'SOH214': '.SgSoh214', 'SOH101': '.SgSoh101', 'SOH100': '.SgSoh100', 'SOH103': '.SgSoh103', 'SOH102': '.SgSoh102', 'SOH105': '.SgSoh105', 'SOH104': '.SgSoh104', 'SOH78': '.SgSoh078', 'SOH79': '.SgSoh079', 'SOH76': '.SgSoh076', 'SOH77': '.SgSoh077', 'SOH74': '.SgSoh074', 'SOH75': '.SgSoh075', 'SOH72': '.SgSoh072', 'SOH73': '.SgSoh073', 'SOH70': '.SgSoh070', 'SOH71': '.SgSoh071', 'SOH224': '.SgSoh224', 'SOH225': '.SgSoh225', 'SOH226': '.SgSoh226', 'SOH227': '.SgSoh227', 'SOH220': '.SgSoh220', 'SOH221': '.SgSoh221', 'SOH222': '.SgSoh222', 'SOH223': '.SgSoh223', 'SOH228': '.SgSoh228', 'SOH229': '.SgSoh229', 'SOH246': '.SgSoh246', 'SOH247': '.SgSoh247', 'SOH244': '.SgSoh244', 'SOH245': '.SgSoh245', 'SOH242': '.SgSoh242', 'SOH243': '.SgSoh243', 'SOH240': '.SgSoh240', 'SOH241': '.SgSoh241', 'SOH248': '.SgSoh248', 'SOH249': '.SgSoh249', 'SOH206': '.SgSoh206', 'SOH49': '.SgSoh049', 'SOH48': '.SgSoh048', 'SOH138': '.SgSoh138', 'SOH139': '.SgSoh139', 'SOH43': '.SgSoh043', 'SOH42': '.SgSoh042', 'SOH41': '.SgSoh041', 'SOH40': '.SgSoh040', 'SOH47': '.SgSoh047', 'SOH46': '.SgSoh046', 'SOH45': '.SgSoh045', 'SOH44': '.SgSoh044', 'SOH233': '.SgSoh233', 'SOH134': '.SgSoh134', 'SOH231': '.SgSoh231', 'SOH230': '.SgSoh230', 'SOH237': '.SgSoh237', 'SOH236': '.SgSoh236', 'SOH235': '.SgSoh235', 'SOH135': '.SgSoh135', 'SOH208': '.SgSoh208', 'SOH239': '.SgSoh239', 'SOH238': '.SgSoh238', 'SOH136': '.SgSoh136', 'SOH255': '.SgSoh255', 'SOH254': '.SgSoh254', 'SOH257': '.SgSoh257', 'SOH137': '.SgSoh137', 'SOH251': '.SgSoh251', 'SOH250': '.SgSoh250', 'SOH207': '.SgSoh207', 'SOH188': '.SgSoh188', 'SOH130': '.SgSoh130', 'SOH259': '.SgSoh259', 'SOH258': '.SgSoh258', 'SOH131': '.SgSoh131', 'SOH54': '.SgSoh054', 'SOH55': '.SgSoh055', 'SOH56': '.SgSoh056', 'SOH57': '.SgSoh057', 'SOH50': '.SgSoh050', 'SOH51': '.SgSoh051', 'SOH52': '.SgSoh052', 'SOH53': '.SgSoh053', 'SOH133': '.SgSoh133', 'SOH58': '.SgSoh058', 'SOH59': '.SgSoh059', 'SOH129': '.SgSoh129', 'SOH128': '.SgSoh128', 'SOH256': '.SgSoh256', 'SOH232': '.SgSoh232', 'SOH123': '.SgSoh123', 'SOH122': '.SgSoh122', 'SOH121': '.SgSoh121', 'SOH120': '.SgSoh120', 'SOH127': '.SgSoh127', 'SOH126': '.SgSoh126', 'SOH125': '.SgSoh125', 'SOH124': '.SgSoh124', 'SOH260': '.SgSoh260', 'SOH261': '.SgSoh261', 'SOH262': '.SgSoh262', 'SOH182': '.SgSoh182', 'SOH264': '.SgSoh264', 'SOH265': '.SgSoh265', 'SOH266': '.SgSoh266', 'SOH267': '.SgSoh267', 'SOH268': '.SgSoh268', 'SOH269': '.SgSoh269', 'SOH234': '.SgSoh234', 'SOH184': '.SgSoh184', 'SOH21': '.SgSoh021', 'SOH20': '.SgSoh020', 'SOH23': '.SgSoh023', 'SOH22': '.SgSoh022', 'SOH25': '.SgSoh025', 'SOH24': '.SgSoh024', 'SOH27': '.SgSoh027', 'SOH26': '.SgSoh026', 'SOH29': '.SgSoh029', 'SOH28': '.SgSoh028', 'SOH107': '.SgSoh107', 'SOH106': '.SgSoh106', 'SOH175': '.SgSoh175', 'SOH109': '.SgSoh109', 'SOH158': '.SgSoh158', 'SOH108': '.SgSoh108', 'SOH156': '.SgSoh156', 'SOH157': '.SgSoh157', 'SOH154': '.SgSoh154', 'SOH155': '.SgSoh155', 'SOH152': '.SgSoh152', 'SOH153': '.SgSoh153', 'SOH150': '.SgSoh150', 'SOH151': '.SgSoh151', 'SOH276': '.SgSoh276', 'SOH275': '.SgSoh275', 'SOH274': '.SgSoh274', 'SOH273': '.SgSoh273', 'SOH272': '.SgSoh272', 'SOH271': '.SgSoh271', 'SOH270': '.SgSoh270', 'SOH32': '.SgSoh032', 'SOH33': '.SgSoh033', 'SOH30': '.SgSoh030', 'SOH31': '.SgSoh031', 'SOH36': '.SgSoh036', 'SOH37': '.SgSoh037', 'SOH34': '.SgSoh034', 'SOH35': '.SgSoh035', 'SOH38': '.SgSoh038', 'SOH39': '.SgSoh039', 'SOH140': '.SgSoh140', 'SOH252': '.SgSoh252', 'SOH211': '.SgSoh211', 'SOH149': '.SgSoh149', 'SOH148': '.SgSoh148', 'SOH210': '.SgSoh210', 'SOH145': '.SgSoh145', 'SOH144': '.SgSoh144', 'SOH147': '.SgSoh147', 'SOH146': '.SgSoh146', 'SOH141': '.SgSoh141', 'SOH132': '.SgSoh132', 'SOH143': '.SgSoh143', 'SOH142': '.SgSoh142', 'SOH8': '.SgSoh008', 'SOH9': '.SgSoh009', 'SOH2': '.SgSoh002', 'SOH3': '.SgSoh003', 'SOH200': '.SgSoh200', 'SOH1': '.SgSoh001', 'SOH6': '.SgSoh006', 'SOH7': '.SgSoh007', 'SOH4': '.SgSoh004', 'SOH5': '.SgSoh005', 'SOH217': '.SgSoh217', 'SOH116': '.SgSoh116', 'SOH216': '.SgSoh216', 'SOH117': '.SgSoh117', 'SOH87': '.SgSoh087', 'SOH86': '.SgSoh086', 'SOH85': '.SgSoh085', 'SOH84': '.SgSoh084', 'SOH83': '.SgSoh083', 'SOH82': '.SgSoh082', 'SOH81': '.SgSoh081', 'SOH80': '.SgSoh080', 'SOH89': '.SgSoh089', 'SOH88': '.SgSoh088', 'SOH178': '.SgSoh178', 'SOH179': '.SgSoh179', 'SOH170': '.SgSoh170', 'SOH171': '.SgSoh171', 'SOH172': '.SgSoh172', 'SOH173': '.SgSoh173', 'SOH174': '.SgSoh174', 'SOH118': '.SgSoh118', 'SOH176': '.SgSoh176', 'SOH177': '.SgSoh177', 'SOH219': '.SgSoh219', 'SOH218': '.SgSoh218', 'SOH198': '.SgSoh198', 'SOH119': '.SgSoh119', 'SOH192': '.SgSoh192', 'SOH193': '.SgSoh193', 'SOH190': '.SgSoh190', 'SOH191': '.SgSoh191', 'SOH196': '.SgSoh196', 'SOH197': '.SgSoh197', 'SOH194': '.SgSoh194', 'SOH195': '.SgSoh195', 'SOH18': '.SgSoh018', 'SOH19': '.SgSoh019', 'SOH209': '.SgSoh209', 'SOH161': '.SgSoh161', 'SOH10': '.SgSoh010', 'SOH11': '.SgSoh011', 'SOH12': '.SgSoh012', 'SOH13': '.SgSoh013', 'SOH14': '.SgSoh014', 'SOH15': '.SgSoh015', 'SOH16': '.SgSoh016', 'SOH17': '.SgSoh017', 'SOH90': '.SgSoh090', 'SOH91': '.SgSoh091', 'SOH92': '.SgSoh092', 'SOH93': '.SgSoh093', 'SOH94': '.SgSoh094', 'SOH95': '.SgSoh095', 'SOH96': '.SgSoh096', 'SOH97': '.SgSoh097', 'SOH98': '.SgSoh098', 'SOH99': '.SgSoh099', 'SOH169': '.SgSoh169', 'SOH168': '.SgSoh168', 'SOH202': '.SgSoh202', 'SOH160': '.SgSoh160', 'SOH203': '.SgSoh203', 'SOH263': '.SgSoh263', 'SOH213': '.SgSoh213', 'SOH189': '.SgSoh189', 'SOH167': '.SgSoh167', 'SOH201': '.SgSoh201', 'SOH166': '.SgSoh166', 'SOH181': '.SgSoh181', 'SOH180': '.SgSoh180', 'SOH183': '.SgSoh183', 'SOH159': '.SgSoh159', 'SOH185': '.SgSoh185', 'SOH165': '.SgSoh165', 'SOH187': '.SgSoh187', 'SOH186': '.SgSoh186', 'SOH253': '.SgSoh253', 'SOH164': '.SgSoh164', 'SOH204': '.SgSoh204', 'SOH163': '.SgSoh163', 'SOH205': '.SgSoh205', 'SOH212': '.SgSoh212', 'SOH162': '.SgSoh162', 'SOH112': '.SgSoh112', 'SOH113': '.SgSoh113', 'SOH110': '.SgSoh110', 'SOH111': '.SgSoh111', 'SOH69': '.SgSoh069', 'SOH68': '.SgSoh068', 'SOH114': '.SgSoh114', 'SOH115': '.SgSoh115', 'SOH65': '.SgSoh065', 'SOH64': '.SgSoh064', 'SOH67': '.SgSoh067', 'SOH66': '.SgSoh066', 'SOH61': '.SgSoh061', 'SOH60': '.SgSoh060', 'SOH63': '.SgSoh063', 'SOH62': '.SgSoh062', 'SOH199': '.SgSoh199', 'SOH215': '.SgSoh215'}

# 基础的字段 堆
files_base = {'taicang':{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 滨海
            "binhai":{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 永臻
            'ygzhen':{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 中天
            "zgtian":{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 贵州
            "guizhou":{"tm":"opt","skVol":".Volt","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SigMxVol","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SigMiVol","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SigMxTem","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SigMiTem","skChagCapy":".ChgCap",
            "skDisgCapy":".DigCap","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 大同
            "datong":{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"},
            # 阳泉
            "ygqn":{"tm":"opt","skVol":".Vol","skCur":".Cur","skSOC":".Soc","skSOH":".Soh","skSgVmaxRkNm":".SgVmaxRkID","skSgVmaxPkNm":".SgVmaxCelID","skSgVmax":".SgVmax","skSgVminRkNm":".SgVminRkID","skSgVminPkNm":".SgVminCelID",
            "skSgVmin":".SgVmin","skSgTmaxRkNm":".SgTmaxRkID","skSgTmaxPkNm":".SgTmaxCelID","skSgTmax":".SgTmax","skSgTminRkNm":".SgTminRkID","skSgTminPkNm":".SgTminCelID","skSgTmin":".SgTmin","skChagCapy":".ChagCapy",
            "skDisgCapy":".DisgCapy","skCmlChagCapy":".CmlChagCapy","skCmlDisgCapy":".CmlDisgCapy"}
            }
# 簇基本信息
files_rk = {"taicang":{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"},
            "binhai":{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"},
            'ygzhen':{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"},
            'zgtian':{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"},
            # 贵州  没有绝缘电阻  每个簇需要最后动态拼接一个数字
            'guizhou':{"rkVol":".OutVolC","rkCur":".CurC","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".SocC","rkSOH":".SohC","rkSgVavg":".AvgSigVolC","rkSgVmaxNm":".MxVolPosC","rkSgVmax":".MxSigVolC","rkSgVminNm":".MiVolPosC","rkSgVmin":".MiSigVolC",
                      "rkSgTavg":".AvgSigTemC","rkSgTmaxNm":".MxTmPosC","rkSgTmax":".MxSigTemC","rkSgTminNm":".MiTmPosC","rkSgTmin":".MiSigTemC","rkCmlChagCapy":".TotlChgC","rkCmlDisgCapy":".TotlDigC"},
            'datong':{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"},
            'ygqn':{"rkVol":".Vol","rkCur":".Cur","rkInsoPos":".InsoPos","rkInsoNeg":".InsoNeg","rkSOC":".Soc","rkSOH":".Soh","rkSgVavg":".SgVavg","rkSgVmaxNm":".SgVmaxCelID","rkSgVmax":".SgVmax","rkSgVminNm":".SgVminCelID","rkSgVmin":".SgVmin",
                      "rkSgTavg":".SgTavg","rkSgTmaxNm":".SgTmaxCelID","rkSgTmax":".SgTmax","rkSgTminNm":".SgTminCelID","rkSgTmin":".SgTmin","rkCmlChagCapy":".CmlChagCapy","rkCmlDisgCapy":".CmlDisgCapy"}
            }


zhili_openId = 'yuanchu1'  # 智锂企业标识
zhili_key = b"fastfuncn1234567"
zhili_iv = b"fastfuncn1234567"
# 自身的加密密匙和向量
openId = 'RHBESS01'
key = b"RHBESS1101022022"  # 西城区地区编码
iv = b"1101020090002022"  #展览路街道

class ZhiLRealHandleIntetface(BaseHandler):
    # @tornado.web.authenticated
    def get(self, kt):
        # self.refreshSession()  # 刷新session
        try:
            f,data = self._verifyToken()
            if not f:
                return data
            db = data['db']  # 具体电站
            if kt == 'DcContStatus':  # 继电器状态
                if db=='datong':
                    name = '%s.Lp%s.Sw1DcBk' % (_station_name(db, data['pcsSt']), data['lp'])
                else:
                    name = '%s%s.Lp%s.DcContAllowRk%s'%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                # name = "%sPcsSt%s.Lp%s.DcContAllowRk%s"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                self._getValue('status',name)
                
            elif kt == 'ACInrTmp':  # 空调温度
                
                name = '%s%s.Lp%s.HVAC%s_Temp'%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                # name = "%sPcsSt%s.Lp%s.HVAC%s_Temp"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'])
                self._getValue('measure',name)
                
            elif kt == 'FaultStatus':  # 故障字
                if 'code' not in data.keys():
                    return self.customError('非法参数')
                elif data['code'] not in gz_no.keys():
                    return self.customError('参数不合规')
                if db=='datong':
                    name = '%s.Lp%s.Rk%s%s' % (_station_name(db,data['pcsSt']),data['lp'],data['bank'],gz_no[data['code']])
                else:
                    name = "%s%s.Lp%s.Rk%s%s"%(_station_name(db,data['pcsSt']),data['pcsSt'],data['lp'],data['bank'],gz_no[data['code']])
                # name = "tpSttaicang.PcsSt%s.Lp%s.Rk%s%s"%(data['pcsSt'],data['lp'],data['bank'],gz_no[data['code']])
                self._getValue('status',name)

            else:
                return self.pathError()
        except ValueError as E:
            return self.customError(str(E).encode('utf8'))
        except Exception as E:
            logging.info(E)
            return self.requestError()

    
    def _getValue(self,type,name):
        re = real_data(type,name,'db')
        if not re['desc']:
            re = real_data(type,name,'ram')
        value = re['value']
        dat = AESUtil.encryt(str(value), key, iv)
        dat = str(dat,encoding='utf-8')
        self.set_header('Bean', MD5Tool.get_str_md5(dat+openId))
        return self.returnTypeSuc(dat)

    
    def _verifyToken(self):
        #  验证token
        head = self.request.headers
        Bean = head.get('Bean',None)
        logging.info('Bean:%s'%Bean)
        data = self.get_argument('data',None)
        logging.info('密文---data-------:%s'%data)
        md5 = MD5Tool.get_str_md5(data+zhili_openId)
        if not Bean or md5 != Bean:  # 身份验证失败
            return False ,self.tokenError()

        data = eval(AESUtil.decrypt(data, zhili_key, zhili_iv))
        keys = data.keys()
        if 'pcsSt' not in keys or 'lp' not in keys or 'bank' not in keys or 'db' not in keys:
            return False ,self.customError('非法参数')
        elif data['pcsSt']<1 or data['pcsSt']>4:
            return False ,self.customError('参数不合规 PCS')
        elif data['lp']<1 or data['lp']>2:
            return False ,self.customError('参数不合规 lp')
        elif data['bank']<1 or data['bank']>6:
            return False ,self.customError('参数不合规 bank')
        logging.info('明文*****data*************:%s'%data)
        return True ,data
    
def _station_name(db,pcs):
    '''返回电站名称'''
    if db == 'binhai':
        if pcs>3:
            return 'tpStbinhai2.PcsSt'
        else:
            return 'tpStbinhai1.PcsSt'
    elif db == 'ygzhen':
        if pcs>1:
            return 'tfStygzhen2.EMS.PcsSt'
        else:
            return 'tfStygzhen1.EMS.PcsSt'
    elif db == 'zgtian':
        return 'tfStzgtian%s.EMS.PCS'%pcs
    elif db == 'datong':
        return 'tc_datong%s.EMS.Energy%s.PCS'%(pcs,pcs)
       
