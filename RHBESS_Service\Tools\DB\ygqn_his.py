#!/usr/bin/env python
# coding=utf-8
#<AUTHOR> WYJ
#@Date         : 2022-11-17 11:00:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\DB\zgtian_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-17 11:09:34

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")

YGQN_HOSTNAME = model_config.get('mysql', "YGQN_HOSTNAME")
YGQN_PORT = model_config.get('mysql', "YGQN_PORT")

YGQN_HOSTNAMES = model_config.get('mysql', "YGQN_HOSTNAMES")
YGQN_PORTS = model_config.get('mysql', "YGQN_PORTS")

SYGQN7_DATABASE = model_config.get('mysql', "SYGQN7_DATABASE")
SYGQN8_DATABASE = model_config.get('mysql', "SYGQN8_DATABASE")
YGQN7_DATABASE = model_config.get('mysql', "YGQN7_DATABASE")
YGQN8_DATABASE = model_config.get('mysql', "YGQN8_DATABASE")

YGQN_USERNAME = model_config.get('mysql', "YGQN_USERNAME")
YGQN_PASSWORD = model_config.get('mysql', "YGQN_PASSWORD")

YGQN_USERNAMES = model_config.get('mysql', "YGQN_USERNAMES")
YGQN_PASSWORDS = model_config.get('mysql', "YGQN_PASSWORDS")




shisdb1_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAMES,
    YGQN_PASSWORDS,
    YGQN_HOSTNAMES,
    YGQN_PORTS,
    SYGQN7_DATABASE
)
sygqn7_engine = create_engine(shisdb1_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sygqn7_session = scoped_session(sessionmaker(sygqn7_engine,autoflush=True))
sygqn7_Base = declarative_base(sygqn7_engine)
sygqn7_session = _sygqn7_session()



shisdb2_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAMES,
    YGQN_PASSWORDS,
    YGQN_HOSTNAMES,
    YGQN_PORTS,
    SYGQN8_DATABASE
)
sygqn8_engine = create_engine(shisdb2_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_sygqn8_session = scoped_session(sessionmaker(sygqn8_engine,autoflush=True))
sygqn8_Base = declarative_base(sygqn8_engine)
sygqn8_session = _sygqn8_session()


hisdb7_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN7_DATABASE
)
ygqn7_engine = create_engine(hisdb7_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn7_session = scoped_session(sessionmaker(ygqn7_engine,autoflush=True))
ygqn7_Base = declarative_base(ygqn7_engine)
ygqn7_session = _ygqn7_session()


hisdb8_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    YGQN_USERNAME,
    YGQN_PASSWORD,
    YGQN_HOSTNAME,
    YGQN_PORT,
    YGQN8_DATABASE
)
ygqn8_engine = create_engine(hisdb8_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)
_ygqn8_session = scoped_session(sessionmaker(ygqn8_engine,autoflush=True))
ygqn8_Base = declarative_base(ygqn8_engine)
ygqn8_session = _ygqn8_session()



