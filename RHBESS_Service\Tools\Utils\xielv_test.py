#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-27 09:06:45
#@FilePath     : \RHBESS_Service\Tools\Utils\xielv_test.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 10:12:12

import sys
reload(sys)
sys.setdefaultencoding("utf-8")

import numpy as np
import pandas as pd
from Application.Models.His.r_ACDMS import HisACDMS
from Tools.Utils.time_utils import timeUtils
from Tools.Utils.num_utils import complete_data
from Tools.DB.ygzhen_his import ygzhen1_session,ygzhen2_session
import xlsxwriter as xw
import time

# # from scipy.stats import ttest_ind,linregress
# # v1 = [0,0.003,0,0.005,0.002,0.012,0.006,0.036,0.021,0.009,0.004]  # 0.0016
# # v1 = [0.004,0,0.006,0.009,0.007,0.03,0.003,0.022,0.008,0.003,0]  # 0.0018
# # v1 = [0.003,0,0.003,0.006,0.005,0.011,0.006,0.002,0.001,0.013,0.004,0.057,0.021,0.002,0.006,0.005]  # 0.0009
# # v1 = [0.002,0.003,0.008,0.001,0.007,0.008,0.003,0.015,0.011,0.004,0.003,0.003,0.002,0.001,0.025,0.022,0.011,0.005,0.003]  # 0.0003
# # v1 = [0.006,0.002,0.008,0.005,0.014,0.009,0.002,0.004,0.002,0.004,0.002,0.002,0.002,0.056,0.011,0.009,0]  # 0.0005
# v1 = [0.114,0.015,0.013,0.011,0.008]  # -0.02
# v2=[]
# for v in range(len(v1)):
#     v2.append(v)
# # v1 = [-0.001,-0.001,-0.002,0,-0.036,-0.003,-0.003,-0.002,-0.004,0,-0.001]
# # v2 = [1,2,3,4,5,6,7,8,9,10,11]
# res1,res2 = ttest_ind(v1, v2)
# print res1,res2

# slope, intercept, r_value, p_value, std_err = linregress(v2, v1)
# print '********',slope, intercept, r_value, p_value, std_err

# df = pd.read_excel('test.xls',index_col=0)
# values_arr = df.values  # 二维矩阵
# # values_arr[pd.isna(values_arr)] = None

# # print values_arr
# data = []
# for v in range(len(values_arr)):
#     ti = timeUtils.ssTtimes(values_arr[v][1])
#     val = values_arr[v][0]
    
    
#     data.append({"time":ti,"value":val})
# st1 = 1679155200
st1 = '2023-04-20 00:00:00'
st2 = '2023-04-22 00:00:00'
s= [0,ygzhen1_session,ygzhen2_session]
socArr = ['tfStygzhen1.EMS.BMS1.Ay1.SysSOC','tfStygzhen1.EMS.BMS1.Ay2.SysSOC','tfStygzhen1.EMS.BMS1.Ay3.SysSOC','tfStygzhen1.EMS.BMS1.Ay4.SysSOC','tfStygzhen2.EMS.BMS2.Ay1.SysSOC',
       'tfStygzhen2.EMS.BMS2.Ay2.SysSOC','tfStygzhen2.EMS.BMS2.Ay3.SysSOC','tfStygzhen2.EMS.BMS2.Ay4.SysSOC']
powerArr = ['tfStygzhen1.EMS.PCS1.Lp1.Totl_RealPw','tfStygzhen1.EMS.PCS1.Lp2.Totl_RealPw','tfStygzhen1.EMS.PCS1.Lp3.Totl_RealPw','tfStygzhen1.EMS.PCS1.Lp4.Totl_RealPw',
           'tfStygzhen2.EMS.PCS2.Lp1.Totl_RealPw','tfStygzhen2.EMS.PCS2.Lp2.Totl_RealPw','tfStygzhen2.EMS.PCS2.Lp3.Totl_RealPw','tfStygzhen2.EMS.PCS2.Lp4.Totl_RealPw']

charArr = ['tfStygzhen1.EMS.PCS1.Lp1.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp2.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp3.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp4.AcChagCapyTotl',
           'tfStygzhen2.EMS.PCS2.Lp1.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp2.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp3.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp4.AcChagCapyTotl']

disgArr = ['tfStygzhen1.EMS.PCS1.Lp1.AcDisgCapyTotl','tfStygzhen1.EMS.PCS1.Lp2.AcDisgCapyTotl','tfStygzhen1.EMS.PCS1.Lp3.AcDisgCapyTotl','tfStygzhen1.EMS.PCS1.Lp4.AcDisgCapyTotl',
           'tfStygzhen2.EMS.PCS2.Lp1.AcDisgCapyTotl','tfStygzhen2.EMS.PCS2.Lp2.AcDisgCapyTotl','tfStygzhen2.EMS.PCS2.Lp3.AcDisgCapyTotl','tfStygzhen2.EMS.PCS2.Lp4.AcDisgCapyTotl']

# 充放电量的sql语句
# select name,max(value)-min(value) from r_measure202303 where name in ('tfStygzhen1.EMS.PCS1.Lp1.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp2.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp3.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp4.AcChagCapyTotl',
#           'tfStygzhen2.EMS.PCS2.Lp1.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp2.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp3.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp4.AcChagCapyTotl') and dts_s>=1678809600 and dts_s<=1679760000 and value>0 group by name;

# 按时间分组

# select name,date_format(from_unixtime(dts_s), '%Y-%m-%d') AS timedate ,max(value)-min(value) from r_measure202303 where name in ('tfStygzhen1.EMS.PCS1.Lp1.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp2.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp3.AcChagCapyTotl','tfStygzhen1.EMS.PCS1.Lp4.AcChagCapyTotl',
#         'tfStygzhen2.EMS.PCS2.Lp1.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp2.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp3.AcChagCapyTotl','tfStygzhen2.EMS.PCS2.Lp4.AcChagCapyTotl') and dts_s>=1678809600 and dts_s<=1679760000  group by name , timedate;

workbook = xw.Workbook('demo.xls')  # 创建工作簿
def xw_toExcel(data,shell):  # xlsxwriter库储存数据到excel
    
    worksheet1 = workbook.add_worksheet(shell)  # 创建子表
    worksheet1.activate()  # 激活表
    title = ['数值', '时间']  # 设置表头
    worksheet1.write_row('A1', title)  # 从A1单元格开始写入表头
    i = 2  # 从第二行开始写入数据
    for j in range(len(data['value'])):
        insertData = [data['value'][j], data['time'][j]]
        row = 'A' + str(i)
        worksheet1.write_row(row, insertData)
        i += 1

def _select_get_his_value(db_con,table,name,st,ed,startTime,endTime,jiange,vmax=65):
    '''获取历史数据'''
    db_con.commit()
    data ={'time':[],'value':[]}
    data_obj = {}
    HisTable_m = HisACDMS(table)
    
    values = db_con.query(HisTable_m.value,HisTable_m.dts_s).filter(HisTable_m.name==name,HisTable_m.dts_s.between(st,ed),HisTable_m.value<vmax).order_by(HisTable_m.dts_s.asc()).all()  # 查询当前月
    
    for val in values:

        # data_obj[timeUtils.ssTtimes(val[1])] = val[0]
        data['time'].append(timeUtils.ssTtimes(val[1]))
        data['value'].append(val[0])
    
    # # 方法1
    # data_obj[startTime] = values[0][0]
    # data_obj[endTime] = values[-1][0]
    # keys = sorted(data_obj.keys())
    # data ={'time':keys,'value':[data_obj[k] for k in keys]}
   
    data["time"].insert(0,startTime)
    data["value"].insert(0,data["value"][0])
    data["time"].append(endTime)
    data["value"].append(data["value"][-1])
    # 方法3
    df = pd.DataFrame(data)
    df = df.drop_duplicates(subset=["time"], keep="first")
    # 转换回字典
    data["time"] = df["time"].tolist()
    data["value"] = df["value"].tolist()
    print 44444444444,time.time()

    # 方法2
    # new_key, idx = np.unique(data["time"], return_index=True)
    # # # 删除重复的 value
    # new_value = np.delete(data["value"], np.setdiff1d(np.arange(len(data["value"])), idx))
    # # # 更新字典
    # data["time"] = new_key.tolist()
    # data["value"] = new_value.tolist()


    
    # data['time']=times
    # data['value']=va

    return complete_data(data,jiange)   

print 'SOC---start'
for soc in socArr:
    ind = socArr.index(soc)
    db_con = s[int(soc[10:11])]
    v = _select_get_his_value(db_con,'r_measure202304',soc,timeUtils.timeStrToTamp(st1),timeUtils.timeStrToTamp(st2),st1,st2,'5T',101)
    shell = 'soc%s'%ind
    xw_toExcel(v,shell)
print 'power------start'
for pw in powerArr:
    ind = powerArr.index(pw)
    db_con = s[int(pw[10:11])]
    print 'power-name is ',pw
    v = _select_get_his_value(db_con,'r_measure202304',pw,timeUtils.timeStrToTamp(st1),timeUtils.timeStrToTamp(st2),st1,st2,'5T',9999)
    shell = 'disg%s'%ind
    xw_toExcel(v,shell)

print 'PwTrasDCIN1----start'
v = _select_get_his_value(s[1],'r_measure202304','tfStygzhen1.EMS.SWT.PwTrasDCIN1',timeUtils.timeStrToTamp(st1),timeUtils.timeStrToTamp(st2),st1,st2,'5T',999999)  # 全场有功
shell = 'PwTrasDCIN1'
xw_toExcel(v,shell)
print 'PwTrasDCIN1-******success'
s[1].close()
workbook.close()  # 关闭表

