# !/usr/bin/env python
# coding:utf-8
# @Time     : 2024/10/25 下午2:26
# <AUTHOR> Li<PERSON>ang
# @Project  : TianLuAppBackend
# @File     : db_tool.py
# @Software : PyCharm
import traceback

import pymysql
from dbutils.persistent_db import PersistentDB

from LocaleTool import logger, settings


class DatabaseTool:

    def __init__(self):
        self.dwd_pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['default']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['default']['USER'],  # 数据库用户名
            "password": settings.DATABASES['default']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['default']['NAME'],  # 数据库名称
            "port": settings.DATABASES['default']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })

    def select_one(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchone()
            return result
        except Exception as e:
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_single(self, sql):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql)
            result = cursor.fetchall()
            return result
        except Exception as e:
            print(traceback.print_exc())
            logger.error(e)
        finally:
            cursor.close()
            conn.close()

    def select_many(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, args)
            result = cursor.fetchall()
            return result
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()

    def execute_sql(self, sql, *args):
        conn = self.dwd_pool.connection()
        cursor = conn.cursor()

        try:
            cursor.execute(sql, args)
            conn.commit()
            return cursor.lastrowid
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            cursor.close()
            conn.close()

