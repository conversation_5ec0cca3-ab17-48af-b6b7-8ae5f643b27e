[32m[2024-02-21 16:37:32 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-02-22 10:54:29 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-03-06 16:03:40 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[33m[2024-03-06 16:06:51 web.py:log_request:2243 WARNING](B[m 404 POST //UserLogin/Login (*************) 1.72ms
[31m[2024-03-06 16:07:45 user.py:post:486 ERROR](B[m Error 113 connecting to ***********:6379. No route to host.
[32m[2024-03-06 16:07:45 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 3011.96ms
[32m[2024-03-06 16:10:04 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[31m[2024-03-06 16:10:21 user.py:post:486 ERROR](B[m Erro<PERSON> 113 connecting to ***********:6379. No route to host.
[32m[2024-03-06 16:10:21 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 3014.90ms
[32m[2024-03-06 16:12:01 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-03-06 16:12:42 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[31m[2024-03-06 16:13:09 user.py:post:486 ERROR](B[m Error 113 connecting to ***********:6379. No route to host.
[32m[2024-03-06 16:13:09 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 3011.48ms
[32m[2024-03-06 16:13:32 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[31m[2024-03-06 16:13:38 web.py:log_exception:1793 ERROR](B[m Uncaught exception POST /UserLogin/Login (*************)
    HTTPServerRequest(protocol='http', host='*************37:19099', method='POST', uri='/UserLogin/Login', version='HTTP/1.1', remote_ip='*************')
    Traceback (most recent call last):
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 612, in connect
        lambda: self._connect(), lambda error: self.disconnect(error)
      File "/usr/local/python3/lib/python3.6/site-packages/redis/retry.py", line 46, in call_with_retry
        return do()
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 612, in <lambda>
        lambda: self._connect(), lambda error: self.disconnect(error)
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 677, in _connect
        raise err
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 665, in _connect
        sock.connect(socket_address)
    OSError: [Errno 113] No route to host
    
    During handling of the above exception, another exception occurred:
    
    Traceback (most recent call last):
      File "/usr/local/python3/lib/python3.6/site-packages/tornado/web.py", line 1702, in _execute
        result = method(*self.path_args, **self.path_kwargs)
      File "/home/<USER>/RHBESS_Service/Application/EqAccount/ElePriceDecision/user.py", line 405, in post
        Session = self.getOrNewSession()
      File "/home/<USER>/RHBESS_Service/Application/Models/base_handler.py", line 130, in getOrNewSession
        return s if s else session.newSession(self.request.remote_ip)
      File "/home/<USER>/RHBESS_Service/Application/Models/session.py", line 65, in newSession
        r.set(sid, pickle.dumps(s), ex=7200)
      File "/usr/local/python3/lib/python3.6/site-packages/redis/commands/core.py", line 2220, in set
        return self.execute_command("SET", *pieces, **options)
      File "/usr/local/python3/lib/python3.6/site-packages/redis/client.py", line 1235, in execute_command
        conn = self.connection or pool.get_connection(command_name, **options)
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 1387, in get_connection
        connection.connect()
      File "/usr/local/python3/lib/python3.6/site-packages/redis/connection.py", line 617, in connect
        raise ConnectionError(self._error_message(e))
    redis.exceptions.ConnectionError: Error 113 connecting to ***********:6379. No route to host.
[31m[2024-03-06 16:13:38 web.py:log_request:2243 ERROR](B[m 500 POST /UserLogin/Login (*************) 3038.55ms
[32m[2024-03-06 16:18:42 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-03-06 16:19:02 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 119.76ms
[32m[2024-03-06 16:19:07 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 403.76ms
[32m[2024-03-06 16:21:46 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-03-06 16:23:11 user.py:post:407 INFO](B[m user is in session
[32m[2024-03-06 16:23:11 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 36.96ms
[32m[2024-03-06 16:24:37 process.py:fork_processes:123 INFO](B[m Starting 4 processes
[32m[2024-03-06 16:24:39 user.py:post:407 INFO](B[m user is in session
[32m[2024-03-06 16:24:39 web.py:log_request:2243 INFO](B[m 200 POST /UserLogin/Login (*************) 10.25ms
