#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2025-03-24 15:08:10
#@FilePath     : \emot_pjt_rh\TianLuAppBackend\apis\stored_energy\urls.py
#@Email        : <EMAIL>
#@LastEditTime : 2025-03-28 14:03:02


from django.urls import path
from . import views

urlpatterns = [
    # path('income/view/', views.IncomeView.as_view()),  # 收益视图           废弃
    # path('income/month/', views.IncomeMonthView.as_view()),  # 月收益      废弃
    # path('income/day/', views.IncomeDayView.as_view()),  # 日收益          废弃
    # path('income/year/', views.IncomeYearView.as_view()),  # 年收益        废弃
    # path('income/total/', views.IncomeTotalView.as_view()),  # 总收益      废弃
    path('alarm/add/', views.AlarmAddViewV2.as_view()),  # 接收告警上报       en
    path('unalarm/add/', views.UnAlarmAddViewV2.as_view()),  # 告警解除上报       en
    # path('alarm/detail/', views.AlarmDetailView.as_view()),  # 告警详情
    # path('alarm/sendSMS/', views.AlarmSendEmailView.as_view()),  # 告警发送短信
    path('alarm/web/sendSMS/', views.WebAlarmSendEmailView.as_view()),  # Web端告警发送短信    en

    # path('alarm/list/<int:id>', views.WebAlarmDetailListView.as_view()),  # web端告警列表
    path('alarm/download/<int:id>', views.AlarmDownloadView.as_view()),  # web端告警下载     en
    # path('alarm/add/v2', views.AlarmAddViewV2.as_view()),  # 接收告警上报
    # path('unalarm/add/v2', views.UnAlarmAddViewV2.as_view()),  # 告警解除上报
    path('alarm/list/v2/<int:id>', views.WebAlarmDetailListViewV2.as_view()),  # web端告警列表   en
    path('alarm/times/<str:id>', views.AlarmTimesView.as_view()),  # web端查看告警时间段        en
    path('alarm/feedback/<str:id>', views.AlarmFeedbackView.as_view()),  # web端告警反馈：查看、新增&修改        en
    path('alarm/related_feedback/<str:id>', views.RelatedAlarmFeedbackView.as_view()),  # web端告警反馈：7日内相同告警的反馈内容     en
    path('alarm/message/<str:id>', views.AlarmMessageView.as_view()),  # web端告警推送至消息中心 & 告警详情       en

    path('ocsupload/list/<int:id>', views.OcsUploadView.as_view()),  # 查看故障录波列表
    path('ocsupload/downloadList/<int:id>', views.OcsUploadDownloadView.as_view()),  # 故障录波列表下载
    path('ocsupload/infos', views.OcsUploadInfosView.as_view()),  # 查看录波详情
    path('ocsupload/downloadInfos', views.OcsUploadInfosDownloadView.as_view()),  # 查看录波详情

]
