#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-29 14:09:37
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\PointTable\t_device.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-04-29 14:12:07



from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import scada_Base,scada_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean

class DevicePT(scada_Base):
    ''' 设备说明表 '''
    __tablename__ = "t_device"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    name = Column(String(256), nullable=False,comment=u"名称")
    no = Column(Integer, nullable=False,comment=u"")
    descr = Column(String(256), nullable=False,comment=u"名称")
    parent = Column(Integer, nullable=True,comment=u"")
    comment = Column(Integer, nullable=True,comment=u"")
   

    @classmethod
    def init(cls):
        scada_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','no':%s,'descr':'%s','parent':'%s','comment':'%s'}" % (
            self.id,self.name,self.no,self.descr,self.parent,self.comment)