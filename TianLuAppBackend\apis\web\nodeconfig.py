MEASURE = {
    "PCS": {
        "PFC": {"desc": "浮点	产品系统编码"},
        "ARMV": {"desc": "浮点	ARM版本"},
        "DSPV": {"desc": "浮点	DSP版本"},
        "HMIV": {"desc": "浮点	HMI版本"},
        "PCmax": {"desc": "浮点	最大充电功率"},
        "PDmax": {"desc": "浮点	最大放电功率"},
        "LOUPV": {"desc": "浮点	母线过压保护值"},
        "LUUPV": {"desc": "浮点	母线欠压保护值 "},
        "HLDPV": {"desc": "浮点	半母线偏差保护值"},
        "DCOIPV": {"desc": "浮点	直流侧过流保护值 "},
        "AOTV": {"desc": "浮点	环境过温值 "},
        "ROTV": {"desc": "浮点	电抗器过温值"},
        "IGBTOT": {"desc": "浮点	 IGBT 模块过温值"},
        "AUTV": {"desc": "浮点	 环境低温值"},
        "IGBTUT": {"desc": "浮点	 IGBT 模块低温值"},
        "RP": {"desc": "浮点	额定功率 "},
        "RU": {"desc": "浮点	额定电压 "},
        "RI": {"desc": "浮点	额定电流 "},
        "FCTU": {"desc": "浮点	功能码表版本 "},
        "LLOCU": {"desc": "浮点	离网线电压下限"},
        "ULOCU": {"desc": "浮点	离网线电压上限"},
        "CImax": {"desc": "浮点	最大充电电流 "},
        "DImax": {"desc": "浮点	最大放电电流"},
        "LLQ": {"desc": "浮点	无功功率下限"},
        "ULQ": {"desc": "浮点	无功功率上限 "},
        "LLOPU": {"desc": "浮点	 离网相电压下限 "},
        "ULOPU": {"desc": "浮点	 离网相电压上限"},
        "BU": {"desc": "浮点	电池电压"},
        "DCI": {"desc": "浮点	直流电流"},
        "UHLU": {"desc": "浮点	上半母线电压"},
        "LHLU": {"desc": "浮点	 下半母线电压"},
        "PF": {"desc": "浮点	电网频率"},
        "PU": {"desc": "浮点	 电网电压"},
        "ACI": {"desc": "浮点	 交流电流"},
        "OOLU": {"desc": "浮点	 离网输出线电压 "},
        "PUab": {"desc": "浮点	 AB 相电网线电压"},
        "PUbc": {"desc": "浮点	 BC 相电网线电压 "},
        "PUca": {"desc": "浮点	 CA 相电网线电压"},
        "Ia": {"desc": "浮点	A 相电流"},
        "Ib": {"desc": "浮点	B 相电流"},
        "Ic": {"desc": "浮点	C相电流 "},
        "COS": {"desc": "浮点	 输出功率因数 "},
        "LUab": {"desc": "浮点	 AB 相输出线电压"},
        "LUbc": {"desc": "浮点	BC 相输出线电压 "},
        "LUca": {"desc": "浮点	 CA 相输出线电压"},
        "P": {"desc": "浮点	 输出总有功功率 "},
        "DCP": {"desc": "浮点	 直流功率 "},
        "OTAP": {"desc": "浮点	 输出总视在功率"},
        "CPMTAP": {"desc": "浮点	当前允许最大视在功率"},
        "Q": {"desc": "浮点	 输出总无功功率 "},
        "ChaD": {"desc": "浮点	 今日充电量"},
        "DisD": {"desc": "浮点	今日放电量 "},
        "IGBTAT": {"desc": "浮点	 A 相 IGBT 温度 "},
        "TGBTBT": {"desc": "浮点	 B 相 IGBT 温度 "},
        "TGBTCT": {"desc": "浮点	C 相 IGBT 温度 "},
        "PAT": {"desc": "浮点	 环境温度"},
        "IITG": {"desc": "浮点	 对地绝缘阻抗 "},
        "PUa": {"desc": "浮点	A 相电网相电压"},
        "PUb": {"desc": "浮点	 B 相电网相电压"},
        "PUc": {"desc": "浮点	C 相电网相电压"},
        "OUa": {"desc": "浮点	 A 相输出相电压 "},
        "OUb": {"desc": "浮点	 B 相输出相电压"},
        "OUc": {"desc": "浮点	 C 相输出相电压 "},
        "BPRE": {"desc": "浮点	 电池组额定电量 "},
        "BPUUL": {"desc": "浮点	 电池组电压上限"},
        "BPULL": {"desc": "浮点	 电池组电压下限 "},
        "BPSOC": {"desc": "浮点	 电池组SOC "},
        "BPSOH": {"desc": "浮点	电池组SOH"},
        "BPU": {"desc": "浮点	 电池组总电压 "},
        "BPI": {"desc": "浮点	电池组总电流"},
        "BPCE": {"desc": "浮点	电池组可充电量"},
        "BPDE": {"desc": "浮点	电池组可放电量"},
        "BPMCI": {"desc": "浮点	电池组最大充电电流"},
        "BPMDI": {"desc": "浮点	电池组最大放电电流"},
        "BPMCP": {"desc": "浮点	电池组最大充电功率"},
        "BPMDP": {"desc": "浮点	电池组最大放电功率"},
        "MMaSOC": {"desc": "浮点	单体最高SOC"},
        "MMiSOC": {"desc": "浮点	单体最低SOC"},
        "MMaT": {"desc": "浮点	单体最高温度"},
        "MMiT": {"desc": "浮点	单体最低温度"},
        "MMaU": {"desc": "浮点	单体最高电压"},
        "MMiU": {"desc": "浮点	单体最低电压"},
        "BPChaD": {"desc": "浮点	电池组日充电量"},
        "BPDisD": {"desc": "浮点	电池组日放电量"},
        "OFSel": {"desc": "浮点	 离网频率选择 "},
        "OVSet": {"desc": "浮点	离网线电压设定"},
        "PSet": {"desc": "浮点	 有功功率设定 "},
        "QSet": {"desc": "浮点	 无功功率设定"},
        "COSSet": {"desc": "浮点	 功率因数设定"},
        "CVMVS": {"desc": "浮点	 恒压模式电压设定"},
        "CCMCS": {"desc": "浮点	 恒流模式电流设定 "},
        "OPVSe": {"desc": "浮点	 离网相电压设定 "},
        "VAPSet": {"desc": "浮点	 有功功率设定(VSG)"},
        "VRPSet": {"desc": "浮点	 无功功率设定(VSG) "},
        "UTOBV": {"desc": "浮点	电池电压上限阈值 "},
        "BVLT": {"desc": "浮点	 电池电压下限阈值"},
        "ALT": {"desc": "浮点	海拔高度"},
        "BSLLS": {"desc": "浮点	 电池 SOC 下限设定"},
        "BSULS": {"desc": "浮点	 电池 SOC 上限设定"},
        "BVLLS": {"desc": "浮点	 电池单体电压下限设定"},
        "BVULS": {"desc": "浮点	 电池单体电压上限设定"},
        "BTLLS": {"desc": "浮点	 电池单体温度下限设定 "},
        "BTULS": {"desc": "浮点	 电池单体温度上限设定"},
        "LSTY": {"desc": "浮点	 最近同步时间--年"},
        "LSTMD": {"desc": "浮点	 最近同步时间--月日 "},
        "LSTDM": {"desc": "浮点	最近同步时间--时分"},
    },
    "BMS": {
        "U": {"desc": "浮点	总电压"},
        "I": {"desc": "浮点	总电流"},
        "SOC": {"desc": "浮点	SOC"},
        "SOH": {"desc": "浮点	SOH"},
        "SOE": {"desc": "浮点	SOE"},
        "RTU": {"desc": "浮点	额定总压"},
        "RTCap": {"desc": "浮点	额定容量"},
        "SCap": {"desc": "浮点	剩余容量"},
        "RE": {"desc": "浮点	额定电量"},
        "SE": {"desc": "浮点	剩余电量"},
        "BSMT": {"desc": "浮点	从机总数(BMU)"},
        "BOSMT": {"desc": "浮点	在线从机总数(BMU)"},
        "BT": {"desc": "浮点	电池总数"},
        "OBT": {"desc": "浮点	在线电池总数"},
        "TTS": {"desc": "浮点	温感总数"},
        "OTST": {"desc": "浮点	在线温感总数"},
        "DIMaxA": {"desc": "浮点	最大允许放电电流"},
        "DPMaxA": {"desc": "浮点	最大允许放电功率"},
        "CIMaxA": {"desc": "浮点	最大允许充电电流"},
        "CPMaxA": {"desc": "浮点	最大允许充电功率"},
        "PIR": {"desc": "浮点	正极绝缘阻值"},
        "NIR": {"desc": "浮点	负极绝缘阻值"},
        "AveU": {"desc": "浮点	平均电压"},
        "UDMax": {"desc": "浮点	最大压差"},
        "MUMax": {"desc": "浮点	最高单体电压"},
        "MUMaxS": {"desc": "浮点	最高单体电压从机号"},
        "MUMaxN": {"desc": "浮点	最高单体电压编号"},
        "MUMin": {"desc": "浮点	最低单体电压"},
        "MUMinS": {"desc": "浮点	最低单体电压从机号"},
        "MUMinN": {"desc": "浮点	最低单体电压编号"},
        "AveT": {"desc": "浮点	平均温度"},
        "TDMax": {"desc": "浮点	最大温差"},
        "MTMax": {"desc": "浮点	最高单体温度"},
        "MTMaxS": {"desc": "浮点	最高单体温度从机号"},
        "MTMaxN": {"desc": "浮点	最高单体温度编号"},
        "MTMin": {"desc": "浮点	最低单体温度"},
        "MTMinS": {"desc": "浮点	最低单体温度从机号"},
        "MTMinN": {"desc": "浮点	最低单体温度编号"},
        "CCapD": {"desc": "浮点	日充电容量"},
        "ChaED": {"desc": "浮点	日充电电量"},
        "DCapD": {"desc": "浮点	日放电容量"},
        "DisED": {"desc": "浮点	日放电电量"},
        "CTimeD": {"desc": "浮点	日充电时间"},
        "DTimeD": {"desc": "浮点	日放电时间"},
        "NBSC": {"desc": "浮点	电池系统循环次数"},
        "CMUMax": {"desc": "浮点	最大允许充电单体电压"},
        "DMUMin": {"desc": "浮点	最小允许放电单体电压"},
        "CUMax": {"desc": "浮点	最大允许充电总压"},
        "DUMin": {"desc": "浮点	最小允许放电总压"},
        "SVN": {"desc": "浮点	软件版本号"},
        "RVN": {"desc": "浮点	修订版本号"},
        "HVN": {"desc": "浮点	硬件版本号"},
        "SU": {"desc": "浮点	采样总压"},
        "MU1": {"desc": "浮点	单体电压1#"},
        "MU2": {"desc": "浮点	单体电压2#"},
        "MU3": {"desc": "浮点	单体电压3#"},
        "MU4": {"desc": "浮点	单体电压4#"},
        "MU5": {"desc": "浮点	单体电压5#"},
        "MU6": {"desc": "浮点	单体电压6#"},
        "MU7": {"desc": "浮点	单体电压7#"},
        "MU8": {"desc": "浮点	单体电压8#"},
        "MU9": {"desc": "浮点	单体电压9#"},
        "MU10": {"desc": "浮点	单体电压10#"},
        "MU11": {"desc": "浮点	单体电压11#"},
        "MU12": {"desc": "浮点	单体电压12#"},
        "MU13": {"desc": "浮点	单体电压13#"},
        "MU14": {"desc": "浮点	单体电压14#"},
        "MU15": {"desc": "浮点	单体电压15#"},
        "MU16": {"desc": "浮点	单体电压16#"},
        "MU17": {"desc": "浮点	单体电压17#"},
        "MU18": {"desc": "浮点	单体电压18#"},
        "MU19": {"desc": "浮点	单体电压19#"},
        "MU20": {"desc": "浮点	单体电压20#"},
        "MU21": {"desc": "浮点	单体电压21#"},
        "MU22": {"desc": "浮点	单体电压22#"},
        "MU23": {"desc": "浮点	单体电压23#"},
        "MU24": {"desc": "浮点	单体电压24#"},
        "MU25": {"desc": "浮点	单体电压25#"},
        "MU26": {"desc": "浮点	单体电压26#"},
        "MU27": {"desc": "浮点	单体电压27#"},
        "MU28": {"desc": "浮点	单体电压28#"},
        "MU29": {"desc": "浮点	单体电压29#"},
        "MU30": {"desc": "浮点	单体电压30#"},
        "MU31": {"desc": "浮点	单体电压31#"},
        "MU32": {"desc": "浮点	单体电压32#"},
        "MU33": {"desc": "浮点	单体电压33#"},
        "MU34": {"desc": "浮点	单体电压34#"},
        "MU35": {"desc": "浮点	单体电压35#"},
        "MU36": {"desc": "浮点	单体电压36#"},
        "MU37": {"desc": "浮点	单体电压37#"},
        "MU38": {"desc": "浮点	单体电压38#"},
        "MU39": {"desc": "浮点	单体电压39#"},
        "MU40": {"desc": "浮点	单体电压40#"},
        "MU41": {"desc": "浮点	单体电压41#"},
        "MU42": {"desc": "浮点	单体电压42#"},
        "MU43": {"desc": "浮点	单体电压43#"},
        "MU44": {"desc": "浮点	单体电压44#"},
        "MU45": {"desc": "浮点	单体电压45#"},
        "MU46": {"desc": "浮点	单体电压46#"},
        "MU47": {"desc": "浮点	单体电压47#"},
        "MU48": {"desc": "浮点	单体电压48#"},
        "MU49": {"desc": "浮点	单体电压49#"},
        "MU50": {"desc": "浮点	单体电压50#"},
        "MU51": {"desc": "浮点	单体电压51#"},
        "MU52": {"desc": "浮点	单体电压52#"},
        "MU53": {"desc": "浮点	单体电压53#"},
        "MU54": {"desc": "浮点	单体电压54#"},
        "MU55": {"desc": "浮点	单体电压55#"},
        "MU56": {"desc": "浮点	单体电压56#"},
        "MU57": {"desc": "浮点	单体电压57#"},
        "MU58": {"desc": "浮点	单体电压58#"},
        "MU59": {"desc": "浮点	单体电压59#"},
        "MU60": {"desc": "浮点	单体电压60#"},
        "MU61": {"desc": "浮点	单体电压61#"},
        "MU62": {"desc": "浮点	单体电压62#"},
        "MU63": {"desc": "浮点	单体电压63#"},
        "MU64": {"desc": "浮点	单体电压64#"},
        "MU65": {"desc": "浮点	单体电压65#"},
        "MU66": {"desc": "浮点	单体电压66#"},
        "MU67": {"desc": "浮点	单体电压67#"},
        "MU68": {"desc": "浮点	单体电压68#"},
        "MU69": {"desc": "浮点	单体电压69#"},
        "MU70": {"desc": "浮点	单体电压70#"},
        "MU71": {"desc": "浮点	单体电压71#"},
        "MU72": {"desc": "浮点	单体电压72#"},
        "MU73": {"desc": "浮点	单体电压73#"},
        "MU74": {"desc": "浮点	单体电压74#"},
        "MU75": {"desc": "浮点	单体电压75#"},
        "MU76": {"desc": "浮点	单体电压76#"},
        "MU77": {"desc": "浮点	单体电压77#"},
        "MU78": {"desc": "浮点	单体电压78#"},
        "MU79": {"desc": "浮点	单体电压79#"},
        "MU80": {"desc": "浮点	单体电压80#"},
        "MU81": {"desc": "浮点	单体电压81#"},
        "MU82": {"desc": "浮点	单体电压82#"},
        "MU83": {"desc": "浮点	单体电压83#"},
        "MU84": {"desc": "浮点	单体电压84#"},
        "MU85": {"desc": "浮点	单体电压85#"},
        "MU86": {"desc": "浮点	单体电压86#"},
        "MU87": {"desc": "浮点	单体电压87#"},
        "MU88": {"desc": "浮点	单体电压88#"},
        "MU89": {"desc": "浮点	单体电压89#"},
        "MU90": {"desc": "浮点	单体电压90#"},
        "MU91": {"desc": "浮点	单体电压91#"},
        "MU92": {"desc": "浮点	单体电压92#"},
        "MU93": {"desc": "浮点	单体电压93#"},
        "MU94": {"desc": "浮点	单体电压94#"},
        "MU95": {"desc": "浮点	单体电压95#"},
        "MU96": {"desc": "浮点	单体电压96#"},
        "MU97": {"desc": "浮点	单体电压97#"},
        "MU98": {"desc": "浮点	单体电压98#"},
        "MU99": {"desc": "浮点	单体电压99#"},
        "MU100": {"desc": "浮点	单体电压100#"},
        "MU101": {"desc": "浮点	单体电压101#"},
        "MU102": {"desc": "浮点	单体电压102#"},
        "MU103": {"desc": "浮点	单体电压103#"},
        "MU104": {"desc": "浮点	单体电压104#"},
        "MU105": {"desc": "浮点	单体电压105#"},
        "MU106": {"desc": "浮点	单体电压106#"},
        "MU107": {"desc": "浮点	单体电压107#"},
        "MU108": {"desc": "浮点	单体电压108#"},
        "MU109": {"desc": "浮点	单体电压109#"},
        "MU110": {"desc": "浮点	单体电压110#"},
        "MU111": {"desc": "浮点	单体电压111#"},
        "MU112": {"desc": "浮点	单体电压112#"},
        "MU113": {"desc": "浮点	单体电压113#"},
        "MU114": {"desc": "浮点	单体电压114#"},
        "MU115": {"desc": "浮点	单体电压115#"},
        "MU116": {"desc": "浮点	单体电压116#"},
        "MU117": {"desc": "浮点	单体电压117#"},
        "MU118": {"desc": "浮点	单体电压118#"},
        "MU119": {"desc": "浮点	单体电压119#"},
        "MU120": {"desc": "浮点	单体电压120#"},
        "MU121": {"desc": "浮点	单体电压121#"},
        "MU122": {"desc": "浮点	单体电压122#"},
        "MU123": {"desc": "浮点	单体电压123#"},
        "MU124": {"desc": "浮点	单体电压124#"},
        "MU125": {"desc": "浮点	单体电压125#"},
        "MU126": {"desc": "浮点	单体电压126#"},
        "MU127": {"desc": "浮点	单体电压127#"},
        "MU128": {"desc": "浮点	单体电压128#"},
        "MU129": {"desc": "浮点	单体电压129#"},
        "MU130": {"desc": "浮点	单体电压130#"},
        "MU131": {"desc": "浮点	单体电压131#"},
        "MU132": {"desc": "浮点	单体电压132#"},
        "MU133": {"desc": "浮点	单体电压133#"},
        "MU134": {"desc": "浮点	单体电压134#"},
        "MU135": {"desc": "浮点	单体电压135#"},
        "MU136": {"desc": "浮点	单体电压136#"},
        "MU137": {"desc": "浮点	单体电压137#"},
        "MU138": {"desc": "浮点	单体电压138#"},
        "MU139": {"desc": "浮点	单体电压139#"},
        "MU140": {"desc": "浮点	单体电压140#"},
        "MU141": {"desc": "浮点	单体电压141#"},
        "MU142": {"desc": "浮点	单体电压142#"},
        "MU143": {"desc": "浮点	单体电压143#"},
        "MU144": {"desc": "浮点	单体电压144#"},
        "MU145": {"desc": "浮点	单体电压145#"},
        "MU146": {"desc": "浮点	单体电压146#"},
        "MU147": {"desc": "浮点	单体电压147#"},
        "MU148": {"desc": "浮点	单体电压148#"},
        "MU149": {"desc": "浮点	单体电压149#"},
        "MU150": {"desc": "浮点	单体电压150#"},
        "MU151": {"desc": "浮点	单体电压151#"},
        "MU152": {"desc": "浮点	单体电压152#"},
        "MU153": {"desc": "浮点	单体电压153#"},
        "MU154": {"desc": "浮点	单体电压154#"},
        "MU155": {"desc": "浮点	单体电压155#"},
        "MU156": {"desc": "浮点	单体电压156#"},
        "MU157": {"desc": "浮点	单体电压157#"},
        "MU158": {"desc": "浮点	单体电压158#"},
        "MU159": {"desc": "浮点	单体电压159#"},
        "MU160": {"desc": "浮点	单体电压160#"},
        "MU161": {"desc": "浮点	单体电压161#"},
        "MU162": {"desc": "浮点	单体电压162#"},
        "MU163": {"desc": "浮点	单体电压163#"},
        "MU164": {"desc": "浮点	单体电压164#"},
        "MU165": {"desc": "浮点	单体电压165#"},
        "MU166": {"desc": "浮点	单体电压166#"},
        "MU167": {"desc": "浮点	单体电压167#"},
        "MU168": {"desc": "浮点	单体电压168#"},
        "MU169": {"desc": "浮点	单体电压169#"},
        "MU170": {"desc": "浮点	单体电压170#"},
        "MU171": {"desc": "浮点	单体电压171#"},
        "MU172": {"desc": "浮点	单体电压172#"},
        "MU173": {"desc": "浮点	单体电压173#"},
        "MU174": {"desc": "浮点	单体电压174#"},
        "MU175": {"desc": "浮点	单体电压175#"},
        "MU176": {"desc": "浮点	单体电压176#"},
        "MU177": {"desc": "浮点	单体电压177#"},
        "MU178": {"desc": "浮点	单体电压178#"},
        "MU179": {"desc": "浮点	单体电压179#"},
        "MU180": {"desc": "浮点	单体电压180#"},
        "MU181": {"desc": "浮点	单体电压181#"},
        "MU182": {"desc": "浮点	单体电压182#"},
        "MU183": {"desc": "浮点	单体电压183#"},
        "MU184": {"desc": "浮点	单体电压184#"},
        "MU185": {"desc": "浮点	单体电压185#"},
        "MU186": {"desc": "浮点	单体电压186#"},
        "MU187": {"desc": "浮点	单体电压187#"},
        "MU188": {"desc": "浮点	单体电压188#"},
        "MU189": {"desc": "浮点	单体电压189#"},
        "MU190": {"desc": "浮点	单体电压190#"},
        "MU191": {"desc": "浮点	单体电压191#"},
        "MU192": {"desc": "浮点	单体电压192#"},
        "MU193": {"desc": "浮点	单体电压193#"},
        "MU194": {"desc": "浮点	单体电压194#"},
        "MU195": {"desc": "浮点	单体电压195#"},
        "MU196": {"desc": "浮点	单体电压196#"},
        "MU197": {"desc": "浮点	单体电压197#"},
        "MU198": {"desc": "浮点	单体电压198#"},
        "MU199": {"desc": "浮点	单体电压199#"},
        "MU200": {"desc": "浮点	单体电压200#"},
        "MU201": {"desc": "浮点	单体电压201#"},
        "MU202": {"desc": "浮点	单体电压202#"},
        "MU203": {"desc": "浮点	单体电压203#"},
        "MU204": {"desc": "浮点	单体电压204#"},
        "MU205": {"desc": "浮点	单体电压205#"},
        "MU206": {"desc": "浮点	单体电压206#"},
        "MU207": {"desc": "浮点	单体电压207#"},
        "MU208": {"desc": "浮点	单体电压208#"},
        "MU209": {"desc": "浮点	单体电压209#"},
        "MU210": {"desc": "浮点	单体电压210#"},
        "MU211": {"desc": "浮点	单体电压211#"},
        "MU212": {"desc": "浮点	单体电压212#"},
        "MU213": {"desc": "浮点	单体电压213#"},
        "MU214": {"desc": "浮点	单体电压214#"},
        "MU215": {"desc": "浮点	单体电压215#"},
        "MU216": {"desc": "浮点	单体电压216#"},
        "MU217": {"desc": "浮点	单体电压217#"},
        "MU218": {"desc": "浮点	单体电压218#"},
        "MU219": {"desc": "浮点	单体电压219#"},
        "MU220": {"desc": "浮点	单体电压220#"},
        "MU221": {"desc": "浮点	单体电压221#"},
        "MU222": {"desc": "浮点	单体电压222#"},
        "MU223": {"desc": "浮点	单体电压223#"},
        "MU224": {"desc": "浮点	单体电压224#"},
        "MU225": {"desc": "浮点	单体电压225#"},
        "MU226": {"desc": "浮点	单体电压226#"},
        "MU227": {"desc": "浮点	单体电压227#"},
        "MU228": {"desc": "浮点	单体电压228#"},
        "MU229": {"desc": "浮点	单体电压229#"},
        "MU230": {"desc": "浮点	单体电压230#"},
        "MU231": {"desc": "浮点	单体电压231#"},
        "MU232": {"desc": "浮点	单体电压232#"},
        "MU233": {"desc": "浮点	单体电压233#"},
        "MU234": {"desc": "浮点	单体电压234#"},
        "MU235": {"desc": "浮点	单体电压235#"},
        "MU236": {"desc": "浮点	单体电压236#"},
        "MU237": {"desc": "浮点	单体电压237#"},
        "MU238": {"desc": "浮点	单体电压238#"},
        "MU239": {"desc": "浮点	单体电压239#"},
        "MU240": {"desc": "浮点	单体电压240#"},
        "MU241": {"desc": "浮点	单体电压241#"},
        "MU242": {"desc": "浮点	单体电压242#"},
        "MU243": {"desc": "浮点	单体电压243#"},
        "MU244": {"desc": "浮点	单体电压244#"},
        "MU245": {"desc": "浮点	单体电压245#"},
        "MU246": {"desc": "浮点	单体电压246#"},
        "MU247": {"desc": "浮点	单体电压247#"},
        "MU248": {"desc": "浮点	单体电压248#"},
        "MU249": {"desc": "浮点	单体电压249#"},
        "MU250": {"desc": "浮点	单体电压250#"},
        "MU251": {"desc": "浮点	单体电压251#"},
        "MU252": {"desc": "浮点	单体电压252#"},
        "MU253": {"desc": "浮点	单体电压253#"},
        "MU254": {"desc": "浮点	单体电压254#"},
        "MU255": {"desc": "浮点	单体电压255#"},
        "MU256": {"desc": "浮点	单体电压256#"},
        "MU257": {"desc": "浮点	单体电压257#"},
        "MU258": {"desc": "浮点	单体电压258#"},
        "MU259": {"desc": "浮点	单体电压259#"},
        "MU260": {"desc": "浮点	单体电压260#"},
        "MT1": {"desc": "浮点	单体温度1#"},
        "MT2": {"desc": "浮点	单体温度2#"},
        "MT3": {"desc": "浮点	单体温度3#"},
        "MT4": {"desc": "浮点	单体温度4#"},
        "MT5": {"desc": "浮点	单体温度5#"},
        "MT6": {"desc": "浮点	单体温度6#"},
        "MT7": {"desc": "浮点	单体温度7#"},
        "MT8": {"desc": "浮点	单体温度8#"},
        "MT9": {"desc": "浮点	单体温度9#"},
        "MT10": {"desc": "浮点	单体温度10#"},
        "MT11": {"desc": "浮点	单体温度11#"},
        "MT12": {"desc": "浮点	单体温度12#"},
        "MT13": {"desc": "浮点	单体温度13#"},
        "MT14": {"desc": "浮点	单体温度14#"},
        "MT15": {"desc": "浮点	单体温度15#"},
        "MT16": {"desc": "浮点	单体温度16#"},
        "MT17": {"desc": "浮点	单体温度17#"},
        "MT18": {"desc": "浮点	单体温度18#"},
        "MT19": {"desc": "浮点	单体温度19#"},
        "MT20": {"desc": "浮点	单体温度20#"},
        "MT21": {"desc": "浮点	单体温度21#"},
        "MT22": {"desc": "浮点	单体温度22#"},
        "MT23": {"desc": "浮点	单体温度23#"},
        "MT24": {"desc": "浮点	单体温度24#"},
        "MT25": {"desc": "浮点	单体温度25#"},
        "MT26": {"desc": "浮点	单体温度26#"},
        "MT27": {"desc": "浮点	单体温度27#"},
        "MT28": {"desc": "浮点	单体温度28#"},
        "MT29": {"desc": "浮点	单体温度29#"},
        "MT30": {"desc": "浮点	单体温度30#"},
        "MT31": {"desc": "浮点	单体温度31#"},
        "MT32": {"desc": "浮点	单体温度32#"},
        "MT33": {"desc": "浮点	单体温度33#"},
        "MT34": {"desc": "浮点	单体温度34#"},
        "MT35": {"desc": "浮点	单体温度35#"},
        "MT36": {"desc": "浮点	单体温度36#"},
        "MT37": {"desc": "浮点	单体温度37#"},
        "MT38": {"desc": "浮点	单体温度38#"},
        "MT39": {"desc": "浮点	单体温度39#"},
        "MT40": {"desc": "浮点	单体温度40#"},
        "MT41": {"desc": "浮点	单体温度41#"},
        "MT42": {"desc": "浮点	单体温度42#"},
        "MT43": {"desc": "浮点	单体温度43#"},
        "MT44": {"desc": "浮点	单体温度44#"},
        "MT45": {"desc": "浮点	单体温度45#"},
        "MT46": {"desc": "浮点	单体温度46#"},
        "MT47": {"desc": "浮点	单体温度47#"},
        "MT48": {"desc": "浮点	单体温度48#"},
        "MT49": {"desc": "浮点	单体温度49#"},
        "MT50": {"desc": "浮点	单体温度50#"},
        "MT51": {"desc": "浮点	单体温度51#"},
        "MT52": {"desc": "浮点	单体温度52#"},
        "MT53": {"desc": "浮点	单体温度53#"},
        "MT54": {"desc": "浮点	单体温度54#"},
        "MT55": {"desc": "浮点	单体温度55#"},
        "MT56": {"desc": "浮点	单体温度56#"},
        "MT57": {"desc": "浮点	单体温度57#"},
        "MT58": {"desc": "浮点	单体温度58#"},
        "MT59": {"desc": "浮点	单体温度59#"},
        "MT60": {"desc": "浮点	单体温度60#"},
        "MT61": {"desc": "浮点	单体温度61#"},
        "MT62": {"desc": "浮点	单体温度62#"},
        "MT63": {"desc": "浮点	单体温度63#"},
        "MT64": {"desc": "浮点	单体温度64#"},
        "MT65": {"desc": "浮点	单体温度65#"},
        "MT66": {"desc": "浮点	单体温度66#"},
        "MT67": {"desc": "浮点	单体温度67#"},
        "MT68": {"desc": "浮点	单体温度68#"},
        "MT69": {"desc": "浮点	单体温度69#"},
        "MT70": {"desc": "浮点	单体温度70#"},
        "MT71": {"desc": "浮点	单体温度71#"},
        "MT72": {"desc": "浮点	单体温度72#"},
        "MT73": {"desc": "浮点	单体温度73#"},
        "MT74": {"desc": "浮点	单体温度74#"},
        "MT75": {"desc": "浮点	单体温度75#"},
        "MT76": {"desc": "浮点	单体温度76#"},
        "MT77": {"desc": "浮点	单体温度77#"},
        "MT78": {"desc": "浮点	单体温度78#"},
        "MT79": {"desc": "浮点	单体温度79#"},
        "MT80": {"desc": "浮点	单体温度80#"},
        "MT81": {"desc": "浮点	单体温度81#"},
        "MT82": {"desc": "浮点	单体温度82#"},
        "MT83": {"desc": "浮点	单体温度83#"},
        "MT84": {"desc": "浮点	单体温度84#"},
        "MT85": {"desc": "浮点	单体温度85#"},
        "MT86": {"desc": "浮点	单体温度86#"},
        "MT87": {"desc": "浮点	单体温度87#"},
        "MT88": {"desc": "浮点	单体温度88#"},
        "MT89": {"desc": "浮点	单体温度89#"},
        "MT90": {"desc": "浮点	单体温度90#"},
        "MT91": {"desc": "浮点	单体温度91#"},
        "MT92": {"desc": "浮点	单体温度92#"},
        "MT93": {"desc": "浮点	单体温度93#"},
        "MT94": {"desc": "浮点	单体温度94#"},
        "MT95": {"desc": "浮点	单体温度95#"},
        "MT96": {"desc": "浮点	单体温度96#"},
        "MT97": {"desc": "浮点	单体温度97#"},
        "MT98": {"desc": "浮点	单体温度98#"},
        "MT99": {"desc": "浮点	单体温度99#"},
        "MT100": {"desc": "浮点	单体温度100#"},
        "MT101": {"desc": "浮点	单体温度101#"},
        "MT102": {"desc": "浮点	单体温度102#"},
        "MT103": {"desc": "浮点	单体温度103#"},
        "MT104": {"desc": "浮点	单体温度104#"},
        "MT105": {"desc": "浮点	单体温度105#"},
        "MT106": {"desc": "浮点	单体温度106#"},
        "MT107": {"desc": "浮点	单体温度107#"},
        "MT108": {"desc": "浮点	单体温度108#"},
        "MT109": {"desc": "浮点	单体温度109#"},
        "MT110": {"desc": "浮点	单体温度110#"},
        "MT111": {"desc": "浮点	单体温度111#"},
        "MT112": {"desc": "浮点	单体温度112#"},
        "MT113": {"desc": "浮点	单体温度113#"},
        "MT114": {"desc": "浮点	单体温度114#"},
        "MT115": {"desc": "浮点	单体温度115#"},
        "MT116": {"desc": "浮点	单体温度116#"},
        "MT117": {"desc": "浮点	单体温度117#"},
        "MT118": {"desc": "浮点	单体温度118#"},
        "MT119": {"desc": "浮点	单体温度119#"},
        "MT120": {"desc": "浮点	单体温度120#"},
        "MT121": {"desc": "浮点	单体温度121#"},
        "MT122": {"desc": "浮点	单体温度122#"},
        "MT123": {"desc": "浮点	单体温度123#"},
        "MT124": {"desc": "浮点	单体温度124#"},
        "MT125": {"desc": "浮点	单体温度125#"},
        "MT126": {"desc": "浮点	单体温度126#"},
        "MT127": {"desc": "浮点	单体温度127#"},
        "MT128": {"desc": "浮点	单体温度128#"},
        "MT129": {"desc": "浮点	单体温度129#"},
        "MT130": {"desc": "浮点	单体温度130#"},
        "MT131": {"desc": "浮点	单体温度131#"},
        "MT132": {"desc": "浮点	单体温度132#"},
        "MT133": {"desc": "浮点	单体温度133#"},
        "MT134": {"desc": "浮点	单体温度134#"},
        "MT135": {"desc": "浮点	单体温度135#"},
        "MT136": {"desc": "浮点	单体温度136#"},
        "MT137": {"desc": "浮点	单体温度137#"},
        "MT138": {"desc": "浮点	单体温度138#"},
        "MT139": {"desc": "浮点	单体温度139#"},
        "MT140": {"desc": "浮点	单体温度140#"},
        "MT141": {"desc": "浮点	系统时间"},
        "DBUHSV": {"desc": "浮点	放电电池簇欠压（高温）轻微告警值"},
        "DBUHSL": {"desc": "浮点	放电电池簇欠压（高温）轻微告警释放值"},
        "DBUHGV": {"desc": "浮点	放电电池簇欠压（高温）一般告警值"},
        "DBUHGL": {"desc": "浮点	放电电池簇欠压（高温）一般告警释放值"},
        "DBUHBV": {"desc": "整形	放电电池簇欠压（高温）严重告警值"},
        "DBUHBL": {"desc": "浮点	放电电池簇欠压（高温）严重告警释放值"},
        "DBULSV": {"desc": "浮点	放电电池簇欠压（低温）轻微告警值"},
        "DBULSL": {"desc": "浮点	放电电池簇欠压（低温）轻微告警释放值"},
        "DBULGV": {"desc": "浮点	放电电池簇欠压（低温）一般告警值"},
        "DBULGL": {"desc": "浮点	放电电池簇欠压（低温）一般告警释放值"},
        "DBULBV": {"desc": "浮点	放电电池簇欠压（低温）严重告警值"},
        "DBULBL": {"desc": "浮点	放电电池簇欠压（低温）严重告警释放值"},
        "DMUHSV": {"desc": "浮点	放电单体欠压（高温）轻微告警值"},
        "DMUHSL": {"desc": "浮点	放电单体欠压（高温）轻微告警释放值"},
        "DMUHGV": {"desc": "浮点	放电单体欠压（高温）一般告警值"},
        "DMUHGL": {"desc": "浮点	放电单体欠压（高温）一般告警释放值"},
        "DMUHBV": {"desc": "整形	放电单体欠压（高温）严重告警值"},
        "DMUHBL": {"desc": "浮点	放电单体欠压（高温）严重告警释放值"},
        "DMULSV": {"desc": "浮点	放电单体欠压（低温）轻微告警值"},
        "DMULSL": {"desc": "浮点	放电单体欠压（低温）轻微告警释放值"},
        "DMULGV": {"desc": "浮点	放电单体欠压（低温）一般告警值"},
        "DMULGL": {"desc": "浮点	放电单体欠压（低温）一般告警释放值"},
        "DMULBV": {"desc": "浮点	放电单体欠压（低温）严重告警值"},
        "DMULBL": {"desc": "浮点	放电单体欠压（低温）严重告警释放值"},
        "DMOSV": {"desc": "浮点	放电单体过压轻微告警值"},
        "DMOSL": {"desc": "浮点	放电单体过压轻微告警释放值"},
        "DMOGV": {"desc": "浮点	放电单体过压一般告警值"},
        "DMOGL": {"desc": "浮点	放电单体过压一般告警释放值"},
        "DMOBV": {"desc": "浮点	放电单体过压严重告警值"},
        "DMOBL": {"desc": "浮点	放电单体过压严重告警释放值"},
        "DEOSV": {"desc": "浮点	放电过流轻微告警值"},
        "DEOSL": {"desc": "浮点	放电过流轻微告警释放值"},
        "DEOGV": {"desc": "浮点	放电过流一般告警值"},
        "DEOGL": {"desc": "浮点	放电过流一般告警释放值"},
        "DEOBV": {"desc": "浮点	放电过流严重告警值"},
        "DEOBL": {"desc": "浮点	放电过流严重告警释放值"},
        "DMOTSV": {"desc": "浮点	放电单体过温轻微告警值"},
        "DMOTSL": {"desc": "浮点	放电单体过温轻微告警释放值"},
        "DMOTGV": {"desc": "浮点	放电单体过温一般告警值"},
        "DMOTGL": {"desc": "浮点	放电单体过温一般告警释放值"},
        "DMOTBV": {"desc": "浮点	放电单体过温严重告警值"},
        "DMOTBL": {"desc": "浮点	放电单体过温严重告警释放值"},
        "DMUTSV": {"desc": "浮点	放电单体低温轻微告警值"},
        "DMUTSL": {"desc": "浮点	放电单体低温轻微告警释放值"},
        "DMUTGV": {"desc": "浮点	放电单体低温一般告警值"},
        "DMUTGL": {"desc": "浮点	放电单体低温一般告警释放值"},
        "DMUTBV": {"desc": "浮点	放电单体低温严重告警值"},
        "DMUTBL": {"desc": "浮点	放电单体低温严重告警释放值"},
        "DMPSV": {"desc": "浮点	放电单体压差轻微告警值"},
        "DMPSL": {"desc": "浮点	放电单体压差轻微告警释放值"},
        "DMPGV": {"desc": "浮点	放电单体压差一般告警值"},
        "DMPGL": {"desc": "浮点	放电单体压差一般告警释放值"},
        "DMPBV": {"desc": "浮点	放电单体压差严重告警值"},
        "DMPBL": {"desc": "浮点	放电单体压差严重告警释放值"},
        "DMTSV": {"desc": "浮点	放电单体温差轻微告警值"},
        "DMTSL": {"desc": "浮点	放电单体温差轻微告警释放值"},
        "DMTGV": {"desc": "浮点	放电单体温差一般告警值"},
        "DMTGL": {"desc": "浮点	放电单体温差一般告警释放值"},
        "DMTBV": {"desc": "浮点	放电单体温差严重告警值"},
        "DMTBL": {"desc": "浮点	放电单体温差严重告警释放值"},
        "DSLSV": {"desc": "浮点	放电SOC过低轻微告警值"},
        "DSLSL": {"desc": "浮点	放电SOC过低轻微告警释放值"},
        "DSLGV": {"desc": "浮点	放电SOC过低一般告警值"},
        "DSLGL": {"desc": "浮点	放电SOC过低一般告警释放值"},
        "DSLBV": {"desc": "浮点	放电SOC过低严重告警值"},
        "DSLBL": {"desc": "浮点	放电SOC过低严重告警释放值"},
        "CBOHSV": {"desc": "浮点	充电电池簇过压（高温）轻微告警值"},
        "CBOHSL": {"desc": "浮点	充电电池簇过压（高温）轻微告警释放值"},
        "CBOHGV": {"desc": "浮点	充电电池簇过压（高温）一般告警值"},
        "CBOHGL": {"desc": "浮点	充电电池簇过压（高温）一般告警释放值"},
        "CBOHBV": {"desc": "整形	充电电池簇过压（高温）严重告警值"},
        "CBOHBL": {"desc": "浮点	充电电池簇过压（高温）严重告警释放值"},
        "CBOLSV": {"desc": "浮点	充电电池簇过压（低温）轻微告警值"},
        "CBOLSL": {"desc": "浮点	充电电池簇过压（低温）轻微告警释放值"},
        "CBOLGV": {"desc": "浮点	充电电池簇过压（低温）一般告警值"},
        "CBOLGL": {"desc": "浮点	充电电池簇过压（低温）一般告警释放值"},
        "CBOLBV": {"desc": "浮点	充电电池簇过压（低温）严重告警值"},
        "CBOLBL": {"desc": "浮点	充电电池簇过压（低温）严重告警释放值"},
        "CMOHSV": {"desc": "浮点	充电单体过压（高温）轻微告警值"},
        "CMOHSL": {"desc": "浮点	充电单体过压（高温）轻微告警释放值"},
        "CMOHGV": {"desc": "浮点	充电单体过压（高温）一般告警值"},
        "CMOHGL": {"desc": "浮点	充电单体过压（高温）一般告警释放值"},
        "CMOHBV": {"desc": "整形	充电单体过压（高温）严重告警值"},
        "CMOHBL": {"desc": "浮点	充电单体过压（高温）严重告警释放值"},
        "CMOLSV": {"desc": "浮点	充电单体过压（低温）轻微告警值"},
        "CMOLSL": {"desc": "浮点	充电单体过压（低温）轻微告警释放值"},
        "CMOLGV": {"desc": "浮点	充电单体过压（低温）一般告警值"},
        "CMOLGL": {"desc": "浮点	充电单体过压（低温）一般告警释放值"},
        "CMOLBV": {"desc": "浮点	充电单体过压（低温）严重告警值"},
        "CMOLBL": {"desc": "浮点	充电单体过压（低温）严重告警释放值"},
        "CMUVSV": {"desc": "浮点	充电单体欠压轻微告警值"},
        "CMUVSL": {"desc": "浮点	充电单体欠压轻微告警释放值"},
        "CMUVGV": {"desc": "浮点	充电单体欠压一般告警值"},
        "CMUVGL": {"desc": "浮点	充电单体欠压一般告警释放值"},
        "CMUVBV": {"desc": "浮点	充电单体欠压严重告警值"},
        "CMUVBL": {"desc": "浮点	充电单体欠压严重告警释放值"},
        "CEOSV": {"desc": "浮点	充电过流轻微告警值"},
        "CEOSL": {"desc": "浮点	充电过流轻微告警释放值"},
        "CEOGV": {"desc": "浮点	充电过流一般告警值"},
        "CEOGL": {"desc": "浮点	充电过流一般告警释放值"},
        "CEOBV": {"desc": "浮点	充电过流严重告警值"},
        "CEOBL": {"desc": "浮点	充电过流严重告警释放值"},
        "CMOTSV": {"desc": "浮点	充电单体过温轻微告警值"},
        "CMOTSL": {"desc": "浮点	充电单体过温轻微告警释放值"},
        "CMOTGV": {"desc": "浮点	充电单体过温一般告警值"},
        "CMOTGL": {"desc": "浮点	充电单体过温一般告警释放值"},
        "CMOTBV": {"desc": "整形	充电单体过温严重告警值"},
        "CMOTBL": {"desc": "浮点	充电单体过温严重告警释放值"},
        "CMUTSV": {"desc": "浮点	充电单体低温轻微告警值"},
        "CMUTSL": {"desc": "浮点	充电单体低温轻微告警释放值"},
        "CMUTGV": {"desc": "浮点	充电单体低温一般告警值"},
        "CMUTGL": {"desc": "浮点	充电单体低温一般告警释放值"},
        "CMUTBV": {"desc": "浮点	充电单体低温严重告警值"},
        "CMUTBL": {"desc": "浮点	充电单体低温严重告警释放值"},
        "CMPSV": {"desc": "浮点	充电单体压差轻微告警值"},
        "CMPSL": {"desc": "浮点	充电单体压差轻微告警释放值"},
        "CMPGV": {"desc": "浮点	充电单体压差一般告警值"},
        "CMPGL": {"desc": "浮点	充电单体压差一般告警释放值"},
        "CMPBV": {"desc": "浮点	充电单体压差严重告警值"},
        "CMPBL": {"desc": "浮点	充电单体压差严重告警释放值"},
        "CMTSV": {"desc": "浮点	充电单体温差轻微告警值"},
        "CMTSL": {"desc": "浮点	充电单体温差轻微告警释放值"},
        "CMTGV": {"desc": "浮点	充电单体温差一般告警值"},
        "CMTGL": {"desc": "浮点	充电单体温差一般告警释放值"},
        "CMTBV": {"desc": "浮点	充电单体温差严重告警值"},
        "CMTBL": {"desc": "浮点	充电单体温差严重告警释放值"},
        "DRGSV": {"desc": "浮点	放电绝缘电阻正负对地轻微告警值"},
        "DRGSL": {"desc": "浮点	放电绝缘电阻正负对地轻微告警释放值"},
        "DRGGV": {"desc": "浮点	放电绝缘电阻正负对地一般告警值"},
        "DRGGL": {"desc": "浮点	放电绝缘电阻正负对地一般告警释放值"},
        "DRGBV": {"desc": "浮点	放电绝缘电阻正负对地严重告警值"},
        "DRGBL": {"desc": "浮点	放电绝缘电阻正负对地严重告警释放值"},
        "ET": {"desc": "浮点	出水温度"},
        "RWT": {"desc": "浮点	回水温度"},
        "BAT": {"desc": "浮点	环境温度"},
        "IPV": {"desc": "浮点	进水口压力值"},
        "OPV": {"desc": "浮点	出水口压力值"},
        "ACDCAU": {"desc": "浮点	ACDC_A电压"},
        "HPV": {"desc": "浮点	高压压力值"},
        "LPV": {"desc": "浮点	低压压力值"},
        "FPWM": {"desc": "浮点	风扇PWM"},
        "CP": {"desc": "浮点	压缩机功率"},
        "CI": {"desc": "浮点	压缩机电流"},
        "CRS": {"desc": "浮点	压缩机转速"},
        "HTT": {"desc": "浮点	加热目标温度"},
        "RTT": {"desc": "浮点	制冷目标温度"},
        "BUa": {"desc": "浮点	A相电压"},
        "BUb": {"desc": "浮点	B相电压"},
        "BUc": {"desc": "浮点	C相电压"},
        "BUab": {"desc": "浮点	AB线电压"},
        "BUbc": {"desc": "浮点	BC线电压"},
        "BUca": {"desc": "浮点	CA线电压"},
        "BIa": {"desc": "浮点	A相电流"},
        "BIb": {"desc": "浮点	B相电流"},
        "BIc": {"desc": "浮点	C相电流"},
        "BPA": {"desc": "浮点	A相有功功率"},
        "BPB": {"desc": "浮点	B相有功功率"},
        "BPC": {"desc": "浮点	C相有功功率"},
        "BP": {"desc": "浮点	总有功功率"},
        "BQa": {"desc": "浮点	A相无功功率"},
        "BQb": {"desc": "浮点	B相无功功率"},
        "BQc": {"desc": "浮点	C相无功功率"},
        "BQ": {"desc": "浮点	总无功功率"},
        "ATAP": {"desc": "浮点	A相视在功率"},
        "BTAP": {"desc": "浮点	B相视在功率"},
        "CTAP": {"desc": "浮点	C相视在功率"},
        "TTAP": {"desc": "浮点	总视在功率"},
        "BCOSA": {"desc": "浮点	A相功率因数"},
        "BCOSB": {"desc": "浮点	B相功率因数"},
        "BCOSC": {"desc": "浮点	C相功率因数"},
        "BCOS": {"desc": "浮点	总功率因数"},
        "BF": {"desc": "浮点	频率"},
        "BCUAD": {"desc": "浮点	BCU地址"},
        "TFM": {"desc": "浮点	变压器容量"},
        "Pccth": {"desc": "浮点	防逆流阈值"},
        "TP1": {"desc": "整型	1点计划充放电功率"},
        "TP2": {"desc": "整型	2点计划充放电功率"},
        "TP3": {"desc": "整型	3点计划充放电功率"},
        "TP4": {"desc": "整型	4点计划充放电功率"},
        "TP5": {"desc": "整型	5点计划充放电功率"},
        "TP6": {"desc": "整型	6点计划充放电功率"},
        "TP7": {"desc": "整型	7点计划充放电功率"},
        "TP8": {"desc": "整型	8点计划充放电功率"},
        "TP9": {"desc": "整型	9点计划充放电功率"},
        "TP10": {"desc": "整型	10点计划充放电功率"},
        "TP11": {"desc": "整型	11点计划充放电功率"},
        "TP12": {"desc": "整型	12点计划充放电功率"},
        "TP13": {"desc": "整型	13点计划充放电功率"},
        "TP14": {"desc": "整型	14点计划充放电功率"},
        "TP15": {"desc": "整型	15点计划充放电功率"},
        "TP16": {"desc": "整型	16点计划充放电功率"},
        "TP17": {"desc": "整型	17点计划充放电功率"},
        "TP18": {"desc": "整型	18点计划充放电功率"},
        "TP19": {"desc": "整型	19点计划充放电功率"},
        "TP20": {"desc": "整型	20点计划充放电功率"},
        "TP21": {"desc": "整型	21点计划充放电功率"},
        "TP22": {"desc": "整型	22点计划充放电功率"},
        "TP23": {"desc": "整型	23点计划充放电功率"},
        "TP24": {"desc": "整型	24点计划充放电功率"},
        "YS": {"desc": "整型	云端计划起始年"},
        "MS": {"desc": "整型	云端计划起始月"},
        "DS": {"desc": "整型	云端计划起始日"},
        "TS": {"desc": "整型	云端计划起始时"},
        "MinS": {"desc": "整型	云端计划起始分"},
        "YST": {"desc": "整型	云端计划截止年"},
        "MST": {"desc": "整型	云端计划截止月"},
        "MDT": {"desc": "整型	云端计划截止日"},
        "TST": {"desc": "整型	云端计划截止时"},
        "MinST": {"desc": "整型	云端计划截止分"},
        "TOC": {"desc": "浮点	云端目标功率需求"},
        "PP1": {"desc": "浮点	PCS1有功功率"},
        "PCmax1": {"desc": "浮点	PCS1最大充电功率"},
        "PDmax1": {"desc": "浮点	PCS1最大放电功率"},
        "SOC1": {"desc": "浮点	SOC1"},
        "PRf1": {"desc": "浮点	PCS1下发功率"},
        "PCC": {"desc": "浮点	并网点功率"},
        "STSMonthM": {"desc": "整型	云端夏季起始月份"},
        "STTSMonthM": {"desc": "整型	云端夏季截止月份"},
        "MTC1": {"desc": "整型	云端夏季1点充放电标志"},
        "MTC2": {"desc": "整型	云端夏季2点充放电标志"},
        "MTC3": {"desc": "整型	云端夏季3点充放电标志"},
        "MTC4": {"desc": "整型	云端夏季4点充放电标志"},
        "MTC5": {"desc": "整型	云端夏季5点充放电标志"},
        "MTC6": {"desc": "整型	云端夏季6点充放电标志"},
        "MTC7": {"desc": "整型	云端夏季7点充放电标志"},
        "MTC8": {"desc": "整型	云端夏季8点充放电标志"},
        "MTC9": {"desc": "整型	云端夏季9点充放电标志"},
        "MTC10": {"desc": "整型	云端夏季10点充放电标志"},
        "MTC11": {"desc": "整型	云端夏季11点充放电标志"},
        "MTC12": {"desc": "整型	云端夏季12点充放电标志"},
        "MTC13": {"desc": "整型	云端夏季13点充放电标志"},
        "MTC14": {"desc": "整型	云端夏季14点充放电标志"},
        "MTC15": {"desc": "整型	云端夏季15点充放电标志"},
        "MTC16": {"desc": "整型	云端夏季16点充放电标志"},
        "MTC17": {"desc": "整型	云端夏季17点充放电标志"},
        "MTC18": {"desc": "整型	云端夏季18点充放电标志"},
        "MTC19": {"desc": "整型	云端夏季19点充放电标志"},
        "MTC20": {"desc": "整型	云端夏季20点充放电标志"},
        "MTC21": {"desc": "整型	云端夏季21点充放电标志"},
        "MTC22": {"desc": "整型	云端夏季22点充放电标志"},
        "MTC23": {"desc": "整型	云端夏季23点充放电标志"},
        "MTC24": {"desc": "整型	云端夏季24点充放电标志"},
        "MTCW1": {"desc": "整型	云端非夏季1点充放电标志"},
        "MTCW2": {"desc": "整型	云端非夏季2点充放电标志"},
        "MTCW3": {"desc": "整型	云端非夏季3点充放电标志"},
        "MTCW4": {"desc": "整型	云端非夏季4点充放电标志"},
        "MTCW5": {"desc": "整型	云端非夏季5点充放电标志"},
        "MTCW6": {"desc": "整型	云端非夏季6点充放电标志"},
        "MTCW7": {"desc": "整型	云端非夏季7点充放电标志"},
        "MTCW8": {"desc": "整型	云端非夏季8点充放电标志"},
        "MTCW9": {"desc": "整型	云端非夏季9点充放电标志"},
        "MTCW10": {"desc": "整型	云端非夏季10点充放电标志"},
        "MTCW11": {"desc": "整型	云端非夏季11点充放电标志"},
        "MTCW12": {"desc": "整型	云端非夏季12点充放电标志"},
        "MTCW13": {"desc": "整型	云端非夏季13点充放电标志"},
        "MTCW14": {"desc": "整型	云端非夏季14点充放电标志"},
        "MTCW15": {"desc": "整型	云端非夏季15点充放电标志"},
        "MTCW16": {"desc": "整型	云端非夏季16点充放电标志"},
        "MTCW17": {"desc": "整型	云端非夏季17点充放电标志"},
        "MTCW18": {"desc": "整型	云端非夏季18点充放电标志"},
        "MTCW19": {"desc": "整型	云端非夏季19点充放电标志"},
        "MTCW20": {"desc": "整型	云端非夏季20点充放电标志"},
        "MTCW21": {"desc": "整型	云端非夏季21点充放电标志"},
        "MTCW22": {"desc": "整型	云端非夏季22点充放电标志"},
        "MTCW23": {"desc": "整型	云端非夏季23点充放电标志"},
        "MTCW24": {"desc": "整型	云端非夏季24点充放电标志"},
        "MFixDemandRef": {"desc": "整型	就地固定需量值"},
        "RL1M": {"desc": "浮点	夏季1-2点充放电最大额定限值"},
        "RL2M": {"desc": "浮点	夏季2-3点充放电最大额定限值"},
        "RL3M": {"desc": "浮点	夏季3-4点充放电最大额定限值"},
        "RL4M": {"desc": "浮点	夏季4-5点充放电最大额定限值"},
        "RL5M": {"desc": "浮点	夏季5-6点充放电最大额定限值"},
        "RL6M": {"desc": "浮点	夏季6-7点充放电最大额定限值"},
        "RL7M": {"desc": "浮点	夏季7-8点充放电最大额定限值"},
        "RL8M": {"desc": "浮点	夏季8-9点充放电最大额定限值"},
        "RL9M": {"desc": "浮点	夏季9-10点充放电最大额定限值"},
        "RL10M": {"desc": "浮点	夏季10-11点充放电最大额定限值"},
        "RL11M": {"desc": "浮点	夏季11-12点充放电最大额定限值"},
        "RL12M": {"desc": "浮点	夏季12-13点充放电最大额定限值"},
        "RL13M": {"desc": "浮点	夏季13-14点充放电最大额定限值"},
        "RL14M": {"desc": "浮点	夏季14-15点充放电最大额定限值"},
        "RL15M": {"desc": "浮点	夏季15-16点充放电最大额定限值"},
        "RL16M": {"desc": "浮点	夏季16-17点充放电最大额定限值"},
        "RL17M": {"desc": "浮点	夏季17-18点充放电最大额定限值"},
        "RL18M": {"desc": "浮点	夏季18-19点充放电最大额定限值"},
        "RL19M": {"desc": "浮点	夏季19-20点充放电最大额定限值"},
        "RL20M": {"desc": "浮点	夏季20-21点充放电最大额定限值"},
        "RL21M": {"desc": "浮点	夏季21-22点充放电最大额定限值"},
        "RL22M": {"desc": "浮点	夏季22-23点充放电最大额定限值"},
        "RL23M": {"desc": "浮点	夏季23-24点充放电最大额定限值"},
        "RL24M": {"desc": "浮点	夏季24-1点充放电最大额定限值"},
        "RLW1M": {"desc": "浮点	非夏季1-2点充放电最大额定限值"},
        "RLW2M": {"desc": "浮点	非夏季2-3点充放电最大额定限值"},
        "RLW3M": {"desc": "浮点	非夏季3-4点充放电最大额定限值"},
        "RLW4M": {"desc": "浮点	非夏季4-5点充放电最大额定限值"},
        "RLW5M": {"desc": "浮点	非夏季5-6点充放电最大额定限值"},
        "RLW6M": {"desc": "浮点	非夏季6-7点充放电最大额定限值"},
        "RLW7M": {"desc": "浮点	非夏季7-8点充放电最大额定限值"},
        "RLW8M": {"desc": "浮点	非夏季8-9点充放电最大额定限值"},
        "RLW9M": {"desc": "浮点	非夏季9-10点充放电最大额定限值"},
        "RLW10M": {"desc": "浮点	非夏季10-11点充放电最大额定限值"},
        "RLW11M": {"desc": "浮点	非夏季11-12点充放电最大额定限值"},
        "RLW12M": {"desc": "浮点	非夏季12-13点充放电最大额定限值"},
        "RLW13M": {"desc": "浮点	非夏季13-14点充放电最大额定限值"},
        "RLW14M": {"desc": "浮点	非夏季14-15点充放电最大额定限值"},
        "RLW15M": {"desc": "浮点	非夏季15-16点充放电最大额定限值"},
        "RLW16M": {"desc": "浮点	非夏季16-17点充放电最大额定限值"},
        "RLW17M": {"desc": "浮点	非夏季17-18点充放电最大额定限值"},
        "RLW18M": {"desc": "浮点	非夏季18-19点充放电最大额定限值"},
        "RLW19M": {"desc": "浮点	非夏季19-20点充放电最大额定限值"},
        "RLW20M": {"desc": "浮点	非夏季20-21点充放电最大额定限值"},
        "RLW21M": {"desc": "浮点	非夏季21-22点充放电最大额定限值"},
        "RLW22M": {"desc": "浮点	非夏季22-23点充放电最大额定限值"},
        "RLW23M": {"desc": "浮点	非夏季23-24点充放电最大额定限值"},
        "RLW24M": {"desc": "浮点	非夏季24-1点充放电最大额定限值"},
        "WLoadFollowTC": {"desc": "浮点	云端自动化模式下负荷跟随"},
        "TPRG": {"desc": "整形	通道3 虚拟点 变压器需量控制"},
        "TPRT": {"desc": "浮点	通道3 虚拟点 变压器有功比例"},
        "PCCR": {"desc": "浮点	通道3 虚拟点 并网点无功功率"},
        "PCCM": {"desc": "整形	通道3 虚拟点 并网点电表"},
    }
}

STATUS = {
    "PCS": {
        "DCse": {"desc": "整形	 直流接触器状态"},
        "ITCse": {"desc": "整形	 绝缘检测接触器状态"},
        "ESBse": {"desc": "整形	 急停按钮状态"},
        "IFse": {"desc": "整形	 IGBT 风机状态 "},
        "LPDse": {"desc": "整形	 防雷器状态 "},
        "Fault": {"desc": "整形	 PCS 故障状态 "},
        "alarm": {"desc": "整形	 PCS 警告状态 "},
        "ASTIMN": {"desc": "整形	 并离网切换初始模式"},
        "StaCd": {"desc": "整形	启动命令 "},
        "StoCd": {"desc": "整形	停机命令 "},
        "stanCd": {"desc": "整形	待机命令 "},
        "FauRe": {"desc": "整形	故障复位"},
        "PIDC": {"desc": "整形	被动绝缘检测命令"},
        "BPPF": {"desc": "整形	 BMS 参数保护功能"},
        "ETSSM": {"desc": "整形	 自启停模式使能 "},
        "OGPM": {"desc": "整形	 离网并机模式"},
        "ESBPRC": {"desc": "整形	通道1 pcs 故障字储能电池极性反接故障"},
        "ESBVIA": {"desc": "整形	通道1 pcs 故障字储能电池电压异常"},
        "DCHOSH": {"desc": "整形	通道1 pcs 故障字直流侧半母线硬件过压"},
        "ACHO": {"desc": "整形	通道1 pcs 故障字交流硬件过流"},
        "IGBTAF": {"desc": "整形	通道1 pcs 故障字IGBT A 相故障"},
        "IGBTBF": {"desc": "整形	通道1 pcs 故障字IGBT B 相故障"},
        "IGBTCF": {"desc": "整形	通道1 pcs 故障字IGBT C 相故障"},
        "DCSFau": {"desc": "整形	通道1 pcs 故障字直流开关故障"},
        "FFault": {"desc": "整形	通道1 pcs 故障字IGBT 风机故障"},
        "SysFau": {"desc": "整形	通道1 pcs 故障字BMS 系统故障"},
        "PEquse": {"desc": "整形	通道1 pcs 设备状态"},
        "DO": {"desc": "整形	 降额运行 "},
        "Fse1": {"desc": "整形	故障字储能电池极性反接故障"},
        "Fse2": {"desc": "整形	故障字储能电池电压异常"},
        "Fse3": {"desc": "整形	故障字直流侧半母线硬件过压"},
        "Fse4": {"desc": "整形	故障字交流硬件过流"},
        "Fse5": {"desc": "整形	故障字IGBT A 相故障"},
        "Fse6": {"desc": "整形	故障字IGBT B 相故障"},
        "Fse7": {"desc": "整形	故障字IGBT C 相故障"},
        "Fse8": {"desc": "整形	故障字交流侧 A 相霍尔断线（+15V）"},
        "Fse9": {"desc": "整形	故障字交流侧 A 相霍尔断线（-15V）"},
        "Fse10": {"desc": "整形	故障字交流侧 A 相霍尔断线（IR）"},
        "Fse11": {"desc": "整形	故障字交流侧 A 相霍尔断线（GND）"},
        "Fse12": {"desc": "整形	故障字交流侧 B 相电流霍尔断线（+15V）"},
        "Fse13": {"desc": "整形	故障字交流侧 B 相霍尔断线（-15V）"},
        "Fse14": {"desc": "整形	故障字交流侧 B 相霍尔断线（IR）"},
        "Fse15": {"desc": "整形	故障字交流侧 B 相霍尔断线（GND）"},
        "Fse16": {"desc": "整形	故障字交流侧 C 相电流霍尔断线（+15V）"},
        "Fse17": {"desc": "整形	故障字交流侧 C 相霍尔断线（-15V）"},
        "Fse18": {"desc": "整形	故障字交流侧 C 相霍尔断线（IR）"},
        "Fse19": {"desc": "整形	故障字交流侧 C 相霍尔断线（GND）"},
        "Fse20": {"desc": "整形	故障字直流侧电流霍尔断线（+15V）"},
        "Fse21": {"desc": "整形	故障字直流侧霍尔断线（-15V）"},
        "Fse22": {"desc": "整形	故障字直流侧霍尔断线（IR）"},
        "Fse23": {"desc": "整形	故障字直流侧霍尔断线（GND）"},
        "Fse26": {"desc": "整形	故障字直流开关故障"},
        "Fse27": {"desc": "整形	故障字IGBT 风机故障"},
        "Fse29": {"desc": "整形	故障字直流侧全母线软件过压"},
        "Fse30": {"desc": "整形	故障字直流侧全母线软件欠压"},
        "Fse31": {"desc": "整形	故障字半母线电压偏差过大"},
        "Fse33": {"desc": "整形	故障字电网交流过压"},
        "Fse34": {"desc": "整形	故障字电网交流欠压"},
        "Fse35": {"desc": "整形	故障字离网交流过压"},
        "Fse36": {"desc": "整形	故障字离网交流欠压"},
        "Fse37": {"desc": "整形	故障字电网交流过频"},
        "Fse38": {"desc": "整形	故障字电网交流欠频"},
        "Fse40": {"desc": "整形	故障字输出缺相故障"},
        "Fse41": {"desc": "整形	故障字防雷器故障"},
        "Fse43": {"desc": "整形	故障字电流控制偏差过大"},
        "Fse44": {"desc": "整形	故障字电网电压不平衡"},
        "Fse45": {"desc": "整形	故障字交流侧电流直流分量超限"},
        "Fse46": {"desc": "整形	故障字环境过温"},
        "Fse47": {"desc": "整形	故障字环境低温"},
        "Fse48": {"desc": "整形	故障字IGBT 模块 A 相过温"},
        "Fse49": {"desc": "整形	故障字IGBT 模块 B 相过温"},
        "Fse50": {"desc": "整形	故障字IGBT 模块 C 相过温"},
        "Fse51": {"desc": "整形	故障字IGBT 模块 A 相低温"},
        "Fse52": {"desc": "整形	故障字IGBT 模块 B 相低温"},
        "Fse53": {"desc": "整形	故障字IGBT 模块 C 相低温"},
        "Fse54": {"desc": "整形	故障字交流开关合闸电压不匹配"},
        "Fse56": {"desc": "整形	故障字AD 零漂过大"},
        "Fse57": {"desc": "整形	故障字绝缘电阻偏低"},
        "Fse58": {"desc": "整形	故障字绝缘检测失败"},
        "Fse59": {"desc": "整形	故障字HMI 通讯故障"},
        "Fse60": {"desc": "整形	故障字BMS 通讯故障"},
        "Fse61": {"desc": "整形	故障字EMS 连接超时 1（外部总线）"},
        "Fse62": {"desc": "整形	故障字并机通讯故障"},
        "Fse63": {"desc": "整形	故障字EEPROM 故障"},
        "Fse64": {"desc": "整形	故障字SPI 故障"},
        "Fse65": {"desc": "整形	故障字物联网通讯故障"},
        "Fse66": {"desc": "整形	故障字客户后台通讯故障"},
        "Fse69": {"desc": "整形	故障字CBC 过流"},
        "Fse70": {"desc": "整形	故障字设备过载"},
        "Fse71": {"desc": "整形	故障字电网电压反序"},
        "Fse72": {"desc": "整形	故障字母线软启动失败"},
        "Fse73": {"desc": "整形	故障字交流侧脱扣器故障"},
        "Fse74": {"desc": "整形	故障字交流侧开关故障"},
        "Fse75": {"desc": "整形	故障字载波同步故障"},
        "Fse76": {"desc": "整形	故障字EMS 连接超时 1（内部通讯）"},
        "Fse77": {"desc": "整形	故障字EMS 连接超时 2（外部总线）"},
        "Fse78": {"desc": "整形	故障字EMS 连接超时 2（内部通讯）"},
        "Fse79": {"desc": "整形	故障字EMS 通讯故障"},
        "Fse80": {"desc": "整形	故障字产品版本号不匹配"},
        "Fse81": {"desc": "整形	故障字检测板型号不匹配"},
        "Fse87": {"desc": "整形	故障字RTC 故障"},
        "Fse96": {"desc": "整形	故障字电池电压过高"},
        "Fse97": {"desc": "整形	故障字电池电压过低"},
        "Fse99": {"desc": "整形	故障字黑匣子存储故障"},
        "Fse100": {"desc": "整形	故障字电源盒检测异常"},
        "Fse101": {"desc": "整形	故障字主机异常停机"},
        "Fse103": {"desc": "整形	故障字锁相异常"},
        "Fse106": {"desc": "整形	故障字BMS 系统故障"},
        "Fse107": {"desc": "整形	故障字直流侧中点采样断线"},
        "Ase1": {"desc": "整形	告警字储能电池极性反接故障"},
        "Ase2": {"desc": "整形	告警字储能电池电压异常"},
        "Ase3": {"desc": "整形	告警字直流侧半母线硬件过压"},
        "Ase4": {"desc": "整形	告警字交流硬件过流"},
        "Ase5": {"desc": "整形	告警字IGBT A 相故障"},
        "Ase6": {"desc": "整形	告警字IGBT B 相故障"},
        "Ase7": {"desc": "整形	告警字IGBT C 相故障"},
        "Ase8": {"desc": "整形	告警字交流侧 A 相霍尔断线（+15V）"},
        "Ase9": {"desc": "整形	告警字交流侧 A 相霍尔断线（-15V）"},
        "Ase10": {"desc": "整形	告警字交流侧 A 相霍尔断线（IR）"},
        "Ase11": {"desc": "整形	告警字交流侧 A 相霍尔断线（GND）"},
        "Ase12": {"desc": "整形	告警字交流侧 B 相电流霍尔断线（+15V）"},
        "Ase13": {"desc": "整形	告警字交流侧 B 相霍尔断线（-15V）"},
        "Ase14": {"desc": "整形	告警字交流侧 B 相霍尔断线（IR）"},
        "Ase15": {"desc": "整形	告警字交流侧 B 相霍尔断线（GND）"},
        "Ase16": {"desc": "整形	告警字交流侧 C 相电流霍尔断线（+15V）"},
        "Ase17": {"desc": "整形	告警字交流侧 C 相霍尔断线（-15V）"},
        "Ase18": {"desc": "整形	告警字交流侧 C 相霍尔断线（IR）"},
        "Ase19": {"desc": "整形	告警字交流侧 C 相霍尔断线（GND）"},
        "Ase20": {"desc": "整形	告警字直流侧电流霍尔断线（+15V）"},
        "Ase21": {"desc": "整形	告警字直流侧霍尔断线（-15V）"},
        "Ase22": {"desc": "整形	告警字直流侧霍尔断线（IR）"},
        "Ase23": {"desc": "整形	告警字直流侧霍尔断线（GND）"},
        "Ase26": {"desc": "整形	告警字直流开关故障"},
        "Ase27": {"desc": "整形	告警字IGBT 风机故障"},
        "Ase29": {"desc": "整形	告警字直流侧全母线软件过压"},
        "Ase30": {"desc": "整形	告警字直流侧全母线软件欠压"},
        "Ase31": {"desc": "整形	告警字半母线电压偏差过大"},
        "Ase33": {"desc": "整形	告警字电网交流过压"},
        "Ase34": {"desc": "整形	告警字电网交流欠压"},
        "Ase35": {"desc": "整形	告警字离网交流过压"},
        "Ase36": {"desc": "整形	告警字离网交流欠压"},
        "Ase37": {"desc": "整形	告警字电网交流过频"},
        "Ase38": {"desc": "整形	告警字电网交流欠频"},
        "Ase40": {"desc": "整形	告警字输出缺相故障"},
        "Ase41": {"desc": "整形	告警字防雷器故障"},
        "Ase43": {"desc": "整形	告警字电流控制偏差过大"},
        "Ase44": {"desc": "整形	告警字电网电压不平衡"},
        "Ase45": {"desc": "整形	告警字交流侧电流直流分量超限"},
        "Ase46": {"desc": "整形	告警字环境过温"},
        "Ase47": {"desc": "整形	告警字环境低温"},
        "Ase48": {"desc": "整形	告警字IGBT 模块 A 相过温"},
        "Ase49": {"desc": "整形	告警字IGBT 模块 B 相过温"},
        "Ase50": {"desc": "整形	告警字IGBT 模块 C 相过温"},
        "Ase51": {"desc": "整形	告警字IGBT 模块 A 相低温"},
        "Ase52": {"desc": "整形	告警字IGBT 模块 B 相低温"},
        "Ase53": {"desc": "整形	告警字IGBT 模块 C 相低温"},
        "Ase54": {"desc": "整形	告警字交流开关合闸电压不匹配"},
        "Ase56": {"desc": "整形	告警字AD 零漂过大"},
        "Ase57": {"desc": "整形	告警字绝缘电阻偏低"},
        "Ase58": {"desc": "整形	告警字绝缘检测失败"},
        "Ase59": {"desc": "整形	告警字HMI 通讯故障"},
        "Ase60": {"desc": "整形	告警字BMS 通讯故障"},
        "Ase61": {"desc": "整形	告警字EMS 连接超时 1（外部总线）"},
        "Ase62": {"desc": "整形	告警字并机通讯故障"},
        "Ase63": {"desc": "整形	告警字EEPROM 故障"},
        "Ase64": {"desc": "整形	告警字SPI 故障"},
        "Ase65": {"desc": "整形	告警字物联网通讯故障"},
        "Ase66": {"desc": "整形	告警字客户后台通讯故障"},
        "Ase69": {"desc": "整形	告警字CBC 过流"},
        "Ase70": {"desc": "整形	告警字设备过载"},
        "Ase71": {"desc": "整形	告警字电网电压反序"},
        "Ase72": {"desc": "整形	告警字母线软启动失败"},
        "Ase73": {"desc": "整形	告警字交流侧脱扣器故障"},
        "Ase74": {"desc": "整形	告警字交流侧开关故障"},
        "Ase75": {"desc": "整形	告警字载波同步故障"},
        "Ase76": {"desc": "整形	告警字EMS 连接超时 1（内部通讯）"},
        "Ase77": {"desc": "整形	告警字EMS 连接超时 2（外部总线）"},
        "Ase78": {"desc": "整形	告警字EMS 连接超时 2（内部通讯）"},
        "Ase79": {"desc": "整形	告警字EMS 通讯故障"},
        "Ase80": {"desc": "整形	告警字产品版本号不匹配"},
        "Ase81": {"desc": "整形	告警字检测板型号不匹配"},
        "Ase87": {"desc": "整形	告警字RTC 故障"},
        "Ase96": {"desc": "整形	告警字电池电压过高"},
        "Ase97": {"desc": "整形	告警字电池电压过低"},
        "Ase99": {"desc": "整形	告警字黑匣子存储故障"},
        "Ase100": {"desc": "整形	告警字电源盒检测异常"},
        "Ase101": {"desc": "整形	告警字主机异常停机"},
        "Ase103": {"desc": "整形	告警字锁相异常"},
        "Ase106": {"desc": "整形	告警字BMS 系统故障"},
        "Ase107": {"desc": "整形	告警字直流侧中点采样断线"},
        "Ase108": {"desc": "整形	告警字不满足并网运行条件"},
        "Ase109": {"desc": "整形	告警字电池欠压告警"},
        "Ase110": {"desc": "整形	告警字急停告警"},
        "Ase111": {"desc": "整形	告警字不满足离网运行条件"},
        "QSetm": {"desc": "浮点	 无功功率设定模式 "},
        "PCStu": {"desc": "整形	PCS 开关"},
    },
    "BMS": {
        "relase": {"desc": "整形	继电器状态"},
        "SQse": {"desc": "整形	开关量状态"},
        "FFau1": {"desc": "整形	一级故障故障位1"},
        "FFau2": {"desc": "整形	一级故障故障位2"},
        "FFau3": {"desc": "整形	一级故障故障位3"},
        "FFau4": {"desc": "整形	一级故障故障位4"},
        "FFau5": {"desc": "整形	一级故障故障位5"},
        "FFau6": {"desc": "整形	一级故障故障位6"},
        "FFau7": {"desc": "整形	一级故障故障位7"},
        "FFau8": {"desc": "整形	一级故障故障位8"},
        "FFau9": {"desc": "整形	一级故障故障位9"},
        "FFau10": {"desc": "整形	一级故障故障位10"},
        "FFau11": {"desc": "整形	一级故障故障位11"},
        "FFau12": {"desc": "整形	一级故障故障位12"},
        "FFau13": {"desc": "整形	一级故障故障位13"},
        "FFau14": {"desc": "整形	一级故障故障位14"},
        "FFau15": {"desc": "整形	一级故障故障位15"},
        "FFau16": {"desc": "整形	一级故障故障位16"},
        "FFau17": {"desc": "整形	一级故障故障位17"},
        "FFau18": {"desc": "整形	一级故障故障位18"},
        "FFau19": {"desc": "整形	一级故障故障位19"},
        "FFau20": {"desc": "整形	一级故障故障位20"},
        "FFau21": {"desc": "整形	一级故障故障位21"},
        "FFau22": {"desc": "整形	一级故障故障位22"},
        "FFau23": {"desc": "整形	一级故障故障位23"},
        "FFau24": {"desc": "整形	一级故障故障位24"},
        "FFau25": {"desc": "整形	一级故障故障位25"},
        "FFau26": {"desc": "整形	一级故障故障位26"},
        "FFau27": {"desc": "整形	一级故障故障位27"},
        "FFau28": {"desc": "整形	一级故障故障位28"},
        "FFau29": {"desc": "整形	一级故障故障位29"},
        "FFau30": {"desc": "整形	一级故障故障位30"},
        "FFau31": {"desc": "整形	一级故障故障位31"},
        "FFau32": {"desc": "整形	一级故障故障位32"},
        "FFau33": {"desc": "整形	一级故障故障位33"},
        "FFau34": {"desc": "整形	一级故障故障位34"},
        "FFau35": {"desc": "整形	一级故障故障位35"},
        "FFau36": {"desc": "整形	一级故障故障位36"},
        "FFau37": {"desc": "整形	一级故障故障位37"},
        "FFau38": {"desc": "整形	一级故障故障位38"},
        "FFau39": {"desc": "整形	一级故障故障位39"},
        "FFau40": {"desc": "整形	一级故障故障位40"},
        "FFau41": {"desc": "整形	一级故障故障位41"},
        "FFau42": {"desc": "整形	一级故障故障位42"},
        "FFau43": {"desc": "整形	一级故障故障位43"},
        "FFau44": {"desc": "整形	一级故障故障位44"},
        "FFau45": {"desc": "整形	一级故障故障位45"},
        "FFau46": {"desc": "整形	一级故障故障位46"},
        "FFau47": {"desc": "整形	一级故障故障位47"},
        "FFau48": {"desc": "整形	一级故障故障位48"},
        "FFau49": {"desc": "整形	一级故障故障位49"},
        "FFau50": {"desc": "整形	一级故障故障位50"},
        "FFau51": {"desc": "整形	一级故障故障位51"},
        "FFau52": {"desc": "整形	一级故障故障位52"},
        "FFau53": {"desc": "整形	一级故障故障位53"},
        "FFau54": {"desc": "整形	一级故障故障位54"},
        "FFau55": {"desc": "整形	一级故障故障位55"},
        "FFau56": {"desc": "整形	一级故障故障位56"},
        "FFau57": {"desc": "整形	一级故障故障位57"},
        "FFau58": {"desc": "整形	一级故障故障位58"},
        "FFau59": {"desc": "整形	一级故障故障位59"},
        "FFau60": {"desc": "整形	一级故障故障位60"},
        "FFau61": {"desc": "整形	一级故障故障位61"},
        "FFau62": {"desc": "整形	一级故障故障位62"},
        "FFau63": {"desc": "整形	一级故障故障位63"},
        "FFau64": {"desc": "整形	一级故障故障位64"},
        "SFau1": {"desc": "整形	二级故障故障位1"},
        "SFau2": {"desc": "整形	二级故障故障位2"},
        "SFau3": {"desc": "整形	二级故障故障位3"},
        "SFau4": {"desc": "整形	二级故障故障位4"},
        "SFau5": {"desc": "整形	二级故障故障位5"},
        "SFau6": {"desc": "整形	二级故障故障位6"},
        "SFau7": {"desc": "整形	二级故障故障位7"},
        "SFau8": {"desc": "整形	二级故障故障位8"},
        "SFau9": {"desc": "整形	二级故障故障位9"},
        "SFau10": {"desc": "整形	二级故障故障位10"},
        "SFau11": {"desc": "整形	二级故障故障位11"},
        "SFau12": {"desc": "整形	二级故障故障位12"},
        "SFau13": {"desc": "整形	二级故障故障位13"},
        "SFau14": {"desc": "整形	二级故障故障位14"},
        "SFau15": {"desc": "整形	二级故障故障位15"},
        "SFau16": {"desc": "整形	二级故障故障位16"},
        "SFau17": {"desc": "整形	二级故障故障位17"},
        "SFau18": {"desc": "整形	二级故障故障位18"},
        "SFau19": {"desc": "整形	二级故障故障位19"},
        "SFau20": {"desc": "整形	二级故障故障位20"},
        "SFau21": {"desc": "整形	二级故障故障位21"},
        "SFau22": {"desc": "整形	二级故障故障位22"},
        "SFau23": {"desc": "整形	二级故障故障位23"},
        "SFau24": {"desc": "整形	二级故障故障位24"},
        "SFau25": {"desc": "整形	二级故障故障位25"},
        "SFau26": {"desc": "整形	二级故障故障位26"},
        "SFau27": {"desc": "整形	二级故障故障位27"},
        "SFau28": {"desc": "整形	二级故障故障位28"},
        "SFau29": {"desc": "整形	二级故障故障位29"},
        "SFau30": {"desc": "整形	二级故障故障位30"},
        "SFau31": {"desc": "整形	二级故障故障位31"},
        "SFau32": {"desc": "整形	二级故障故障位32"},
        "SFau33": {"desc": "整形	二级故障故障位33"},
        "SFau34": {"desc": "整形	二级故障故障位34"},
        "SFau35": {"desc": "整形	二级故障故障位35"},
        "SFau36": {"desc": "整形	二级故障故障位36"},
        "SFau37": {"desc": "整形	二级故障故障位37"},
        "SFau38": {"desc": "整形	二级故障故障位38"},
        "SFau39": {"desc": "整形	二级故障故障位39"},
        "SFau40": {"desc": "整形	二级故障故障位40"},
        "SFau41": {"desc": "整形	二级故障故障位41"},
        "SFau42": {"desc": "整形	二级故障故障位42"},
        "SFau43": {"desc": "整形	二级故障故障位43"},
        "SFau44": {"desc": "整形	二级故障故障位44"},
        "SFau45": {"desc": "整形	二级故障故障位45"},
        "SFau46": {"desc": "整形	二级故障故障位46"},
        "SFau47": {"desc": "整形	二级故障故障位47"},
        "SFau48": {"desc": "整形	二级故障故障位48"},
        "SFau49": {"desc": "整形	二级故障故障位49"},
        "SFau50": {"desc": "整形	二级故障故障位50"},
        "SFau51": {"desc": "整形	二级故障故障位51"},
        "SFau52": {"desc": "整形	二级故障故障位52"},
        "SFau53": {"desc": "整形	二级故障故障位53"},
        "SFau54": {"desc": "整形	二级故障故障位54"},
        "SFau55": {"desc": "整形	二级故障故障位55"},
        "SFau56": {"desc": "整形	二级故障故障位56"},
        "SFau57": {"desc": "整形	二级故障故障位57"},
        "SFau58": {"desc": "整形	二级故障故障位58"},
        "SFau59": {"desc": "整形	二级故障故障位59"},
        "SFau60": {"desc": "整形	二级故障故障位60"},
        "SFau61": {"desc": "整形	二级故障故障位61"},
        "SFau62": {"desc": "整形	二级故障故障位62"},
        "SFau63": {"desc": "整形	二级故障故障位63"},
        "SFau64": {"desc": "整形	二级故障故障位64"},
        "TFau1": {"desc": "整形	三级故障故障位1"},
        "TFau2": {"desc": "整形	三级故障故障位2"},
        "TFau3": {"desc": "整形	三级故障故障位3"},
        "TFau4": {"desc": "整形	三级故障故障位4"},
        "TFau5": {"desc": "整形	三级故障故障位5"},
        "TFau6": {"desc": "整形	三级故障故障位6"},
        "TFau7": {"desc": "整形	三级故障故障位7"},
        "TFau8": {"desc": "整形	三级故障故障位8"},
        "TFau9": {"desc": "整形	三级故障故障位9"},
        "TFau10": {"desc": "整形	三级故障故障位10"},
        "TFau11": {"desc": "整形	三级故障故障位11"},
        "TFau12": {"desc": "整形	三级故障故障位12"},
        "TFau13": {"desc": "整形	三级故障故障位13"},
        "TFau14": {"desc": "整形	三级故障故障位14"},
        "TFau15": {"desc": "整形	三级故障故障位15"},
        "TFau16": {"desc": "整形	三级故障故障位16"},
        "TFau17": {"desc": "整形	三级故障故障位17"},
        "TFau18": {"desc": "整形	三级故障故障位18"},
        "TFau19": {"desc": "整形	三级故障故障位19"},
        "TFau20": {"desc": "整形	三级故障故障位20"},
        "TFau21": {"desc": "整形	三级故障故障位21"},
        "TFau22": {"desc": "整形	三级故障故障位22"},
        "TFau23": {"desc": "整形	三级故障故障位23"},
        "TFau24": {"desc": "整形	三级故障故障位24"},
        "TFau25": {"desc": "整形	三级故障故障位25"},
        "TFau26": {"desc": "整形	三级故障故障位26"},
        "TFau27": {"desc": "整形	三级故障故障位27"},
        "TFau28": {"desc": "整形	三级故障故障位28"},
        "TFau29": {"desc": "整形	三级故障故障位29"},
        "TFau30": {"desc": "整形	三级故障故障位30"},
        "TFau31": {"desc": "整形	三级故障故障位31"},
        "TFau32": {"desc": "整形	三级故障故障位32"},
        "TFau33": {"desc": "整形	三级故障故障位33"},
        "TFau34": {"desc": "整形	三级故障故障位34"},
        "TFau35": {"desc": "整形	三级故障故障位35"},
        "TFau36": {"desc": "整形	三级故障故障位36"},
        "TFau37": {"desc": "整形	三级故障故障位37"},
        "TFau38": {"desc": "整形	三级故障故障位38"},
        "TFau39": {"desc": "整形	三级故障故障位39"},
        "TFau40": {"desc": "整形	三级故障故障位40"},
        "TFau41": {"desc": "整形	三级故障故障位41"},
        "TFau42": {"desc": "整形	三级故障故障位42"},
        "TFau43": {"desc": "整形	三级故障故障位43"},
        "TFau44": {"desc": "整形	三级故障故障位44"},
        "TFau45": {"desc": "整形	三级故障故障位45"},
        "TFau46": {"desc": "整形	三级故障故障位46"},
        "TFau47": {"desc": "整形	三级故障故障位47"},
        "TFau48": {"desc": "整形	三级故障故障位48"},
        "TFau49": {"desc": "整形	三级故障故障位49"},
        "TFau50": {"desc": "整形	三级故障故障位50"},
        "TFau51": {"desc": "整形	三级故障故障位51"},
        "TFau52": {"desc": "整形	三级故障故障位52"},
        "TFau53": {"desc": "整形	三级故障故障位53"},
        "TFau54": {"desc": "整形	三级故障故障位54"},
        "TFau55": {"desc": "整形	三级故障故障位55"},
        "TFau56": {"desc": "整形	三级故障故障位56"},
        "TFau57": {"desc": "整形	三级故障故障位57"},
        "TFau58": {"desc": "整形	三级故障故障位58"},
        "TFau59": {"desc": "整形	三级故障故障位59"},
        "TFau60": {"desc": "整形	三级故障故障位60"},
        "TFau61": {"desc": "整形	三级故障故障位61"},
        "TFau62": {"desc": "整形	三级故障故障位62"},
        "TFau63": {"desc": "整形	三级故障故障位63"},
        "TFau64": {"desc": "整形	三级故障故障位64"},
        "ChaSe": {"desc": "整形	充电状态"},
        "DisSe": {"desc": "整形	放电状态"},
        "HPCCse": {"desc": "整形	高压闭合状态"},
        "GAlarm": {"desc": "整形	总告警"},
        "GFault": {"desc": "整形	总故障"},
        "HVPOI": {"desc": "整形	高压上下电指令"},
        "PTSse": {"desc": "整形	PTC温度开关状态"},
        "WaPS": {"desc": "整形	水泵开关"},
        "ComFau": {"desc": "整形	压缩机故障"},
        "BEquse": {"desc": "整形	通道2 bms 设备状态"},
        "BCUMLS": {"desc": "整形	BCU主回路状态"},
        "BCULCI": {"desc": "整形	BCU回路闭合指令"},
        "BORLCI": {"desc": "整形	BCU是否允许闭合主回路"},
        "rela1": {"desc": "整形	继电器总正"},
        "rela2": {"desc": "整形	继电器总负"},
        "rela3": {"desc": "整形	继电器预充"},
        "rela4": {"desc": "整形	继电器断路器分励"},
        "rela5": {"desc": "整形	继电器运行灯"},
        "rela6": {"desc": "整形	继电器故障灯"},
        "rela7": {"desc": "整形	继电器风扇"},
        "rela8": {"desc": "整形	继电器直流供电控制"},
    },
    "EMS": {
        "MCGSA": {"desc": "整形	组态自动化运行模式"},
        "MCGSP": {"desc": "整形	组态计划调度模式"},
        "AEn": {"desc": "整形	功能策略启停"},
        "AEnC": {"desc": "整形	接收云端调度"},
        "LFZT": {"desc": "整形	组态自动化运行负荷跟随"},
        "LFZTP": {"desc": "整形	组态计划运行负荷跟随"},
        "LF": {"desc": "整形	云端负荷跟随调度"},
        "PStse": {"desc": "整形	通道3 虚拟点 PCS停机状态"},
        "PStan": {"desc": "整形	通道3 虚拟点 PCS待机状态"},
        "PChaon": {"desc": "整形	通道3 虚拟点 PCS充电运行"},
        "PDison": {"desc": "整形	通道3 虚拟点 PCS放电运行"},
        "PFause": {"desc": "整形	通道3 虚拟点 PCS故障状态"},
        "ZPOn": {"desc": "整形	通道3 虚拟点 PCS零功率运行"},
        "PAlase": {"desc": "整形	通道3 虚拟点 PCS告警状态"},
        "PRun": {"desc": "整形	通道3 虚拟点 PCS运行"},
        "VEquse": {"desc": "整形	通道3 虚拟点 设备状态"},
        "CloudAuto": {"desc": "整型	云端自动化模式"},
        "TCLoadFollow": {"desc": "整形	云端自动化模式下负荷跟随"},
        "TransfiniteFault": {"desc": "整型	变压器超限故障"},
        "TransfiniteAlarm": {"desc": "整形	变压器超限告警"},
    },
}


# 功率下发计划字典
PLANSTATUS = {
    "zh": {
        "1": "已保存",
        "2": "已下发",
        "3": "已执行",
        "4": "下发失败"
    },
    "en": {
        "1": "Saved",
        "2": "Issued",
        "3": "Executed",
        "4": "Issuance failed"
    }

}
