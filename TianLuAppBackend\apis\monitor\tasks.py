import datetime
import json
import time
import traceback

import paho.mqtt.client as mqtt
import requests
from django.conf import settings
from django_redis import get_redis_connection
from numpy import average
from dateutil.relativedelta import relativedelta

# from apis.user.models import MeterUseTime
from common.database_pools import ads_db_tool
from settings.meter_settings import METER_DIC
from tools.count import get_price, new_get_price, new_get_price_v2, get_station_price, get_station_price_optimize
from TianLuAppBackend.celery import app
import pymysql
from dbutils.persistent_db import PersistentDB
import logging

# 创建任务函数
from apis.user import models
from script.AES_symmetric_encryption import EncryptDate
from tools.send_mail import sendMail_
from tools.aly_send_smscode import Sample




success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG


# 连接数据库
pool = PersistentDB(pymysql, 10, **{
            "host": settings.DATABASES['doris_dwd_rhyc']['HOST'],  # 数据库主机地址
            "user": settings.DATABASES['doris_dwd_rhyc']['USER'],  # 数据库用户名
            "password": settings.DATABASES['doris_dwd_rhyc']['PASSWORD'],  # 数据库密码
            "database": settings.DATABASES['doris_dwd_rhyc']['NAME'],  # 数据库名称
            "port": settings.DATABASES['doris_dwd_rhyc']['PORT'],
            "cursorclass": pymysql.cursors.DictCursor
        })
error_log = logging.getLogger("error")

def mqtt_request_data(station_ins, datetime_):
    """
    动态获取 mqtt 请求参数
    :param station_ins:站实例
    :datetime_ 时间对象
    :return: requests 参数
    """
    timestamp = int(datetime_.timestamp())
    meter_type = station_ins.meter_type
    meter_dic = METER_DIC.get(meter_type)
    charge = meter_dic.get("charge")
    discharge = meter_dic.get("discharge")
    device = meter_dic.get("device").upper()
    request_json = {
        "time": str(timestamp),
        "datatype": "cumulant",
        "app": station_ins.app,
        "station": station_ins.english_name,
        "body": [],
    }

    unit_count = models.Unit.objects.filter(is_delete=0, station=station_ins).count()

    if unit_count == 1:
        request_json["body"].append({"device": device, "body": [charge, discharge]})
    else:
        for i in range(unit_count):
            request_json["body"].append({"device": f"{device}{i + 1}", "body": [charge, discharge]})
    return request_json, charge, discharge


def mqtt_income(station_ins, datas, current_date, peak_ins, charge, discharge):
    """
    动态获取 mqtt 收益
    :param station_ins: 站实例
    :param datas: mqtt 查询数据
    :param current_date: 日期
    :param peak_ins: 收益实例
    :param charge: 日冲
    :param discharge: 日放
    :return: 站收益
    """
    CuCha_list = []
    CuDis_list = []
    new_list_ = sorted(datas, key=lambda x: x["body"]["time"])
    new_list = new_list_[::2]
    unit_count = models.Unit.objects.filter(is_delete=0, station=station_ins).count()
    new_list.append(new_list_[-1])
    for i in range(len(new_list) - 1):
        price = get_price(station_ins, current_date, i)
        # print("自定义电价为:", price)
        if not price:
            price = float(peak_ins.values()[0][f"h{i}"])

        for j in range(unit_count):
            CuDis_list.append(
                abs(abs(float(new_list[i + 1]["body"]["data"][j][discharge])) - abs(
                    float(new_list[i]["body"]["data"][j][discharge])))
                * price
            )

            CuCha_list.append(
                abs(abs(float(new_list[i + 1]["body"]["data"][j][charge])) - abs(
                    float(new_list[i]["body"]["data"][j][charge]))) * price
            )
    total = sum(CuDis_list) - sum(CuCha_list)
    print(f"定时任务收益计算完成================ {station_ins.english_name}总收益为:{total}元")
    return total


def on_connect(mqtt_client, userdata, flags, rc):
    if rc == 0:
        print('Connected successfully')
        mqtt_client.subscribe('django/mqtt')  # 订阅主题
    else:
        print('Bad connection. Code:', rc)


def on_message(mqtt_client, userdata, msg):
    print(f'Received message on topic: {msg.topic} with payload: {msg.payload}')


# @app.task
def timing_income_v2_old():
    """实时收入计算 v2"""
    current_date = datetime.date.today()
    # current_date = datetime.date(year, month, day)

    dt = datetime.datetime.combine(current_date, datetime.time(hour=1))

    url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
    # stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    master_stations = models.MaterStation.objects.filter(is_delete=0).all()
    for master_station in master_stations:
        try:
            exists = models.StationIncome.objects.filter(
                master_station=master_station,
                income_date__day=current_date.day,
                income_date__month=current_date.month,
                income_date__year=current_date.year,
                record=1,
            )
            if not exists:
                slave_stations = master_station.stationdetails_set.filter(is_delete=0).exclude(slave=0)
                if slave_stations.exists():
                    total_income = 0
                    for slave_station in slave_stations:

                        request_json, charge, discharge = mqtt_request_data(slave_station, dt)
                        province_ins = slave_station.province
                        peak_ins = models.PeakValley.objects.filter(
                            year_month=current_date.month, province=province_ins, level=slave_station.level,
                            type=slave_station.type
                        )

                        response = requests.post(url=url, json=request_json)
                        datas = response.json()['datas']
                        if datas:
                            total = mqtt_income(slave_station, datas, current_date, peak_ins, charge, discharge)
                            print(f"定时任务收益计算完成================ 从站{slave_station.english_name}总收益为:{total}元")
                            if total < 0:
                                total = 0
                                print(f"定时任务收益计算完成================ 从站{slave_station.english_name}总收益为:{total}元 小于 0")
                            total_income += total

                        print(
                            f"定时任务收益计算完成================ 主站{master_station.english_name}总收益为:{total_income}元")

                        income_ins = models.StationIncome.objects.filter(
                            station_id=slave_station,
                            master_station=master_station,
                            income_date__day=current_date.day,
                            income_date__month=current_date.month,
                            income_date__year=current_date.year,
                            record=2,
                        )
                        if not income_ins:
                            models.StationIncome.objects.create(
                                peak_load_shifting=total_income, master_station=master_station, income_date=current_date,station_id=slave_station,
                                income_type=1, record=2
                            )
                        else:
                            income_ins.update(peak_load_shifting=total_income, income_type=1, record=2)
            else:
                print("已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(e)


def timing_income_v2():
    time_ins = datetime.datetime.now()
    time_str = time_ins.strftime("%Y-%m-%d")
    year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
    error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

    # 改查新综合过结算表和计量表的新1h冻结表：表名未定
    select_sql = (
        "SELECT day, hour, chag, disg, soc as soc FROM ads_report_chag_disg_union_1h"
        " where station=%s and station_type<=1 and day=%s order by hour")

    # select_sql_2 = (
    #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     " where station=%s and day=%s order by hour")

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        try:
            province_ins = station.province
            # 先判断是否有手动添加的收益记录
            is_exists_station_income = models.StationIncome.objects.filter(
                station_id=station,
                master_station=station.master_station,
                income_date__day=time_ins.day,
                income_date__month=time_ins.month,
                income_date__year=time_ins.year,
                record=1,
            ).exists()

            # 不存在手动添加的收益记录
            if not is_exists_station_income:

                total = 0

                results = ads_db_tool.select_many(select_sql, station.english_name, time_str)

                if results:
                    for i in results:
                        # 获取峰谷电表
                        moment = f'{int(i["hour"])}:00' if int(i["hour"]) >= 10 else f'0{int(i["hour"])}:00'
                        peak_ins = models.PeakValleyNew.objects.filter(
                            year_month=year_month, province=province_ins, level=station.level, type=station.type, moment=moment
                        ).first()
                        temp_dict = {
                            "charge": round(float(i["chag"]), 2),
                            "discharge": round(float(i["disg"]), 2),
                            "time": i["day"].strftime("%Y-%m-%d") + ' ' + str(i["hour"]) + ":00:00",
                            "type": getattr(peak_ins, 'pv')
                        }
                        hour = str(i["hour"])

                        chag_price, disg_price = new_get_price_v2(station, time_ins, peak_ins)
                        logging.info(f"!!!!!!{station.station_name}查询到自定义电价：{time_str}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
                        # if price is None:
                        #     price = float(getattr(peak_ins, "price"))
                        logging.info(f"######{station.station_name}计算计算使用电价为：{time_str}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
                        hour_income = temp_dict['discharge'] * disg_price - temp_dict['charge'] * chag_price
                        total += hour_income

                    error_log.error(f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{total}元")
                    if total < 0:
                        total = 0
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=time_ins.day,
                        income_date__month=time_ins.month,
                        income_date__year=time_ins.year,
                        record=2,
                    )
                    if not income_ins.exists():
                        try:
                            models.StationIncome.objects.create(
                                peak_load_shifting=total, station_id=station, income_date=time_ins, income_type=1,
                                record=2, master_station=station.master_station
                            )
                        except Exception as e:
                            raise e
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                else:
                    error_log.error(f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

            # 已存在手动添加的收入 不进行自动添加
            else:
                error_log.error(f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')

    error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")


def timing_income_v3():
    """
    电量：ads: union_30min;
    电价: t_price_new
    """""
    time_ins = datetime.datetime.now()
    time_str = time_ins.strftime("%Y-%m-%d")
    year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
    error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

    # 改查新综合过结算表和计量表的新1h冻结表：表名未定
    select_sql = (
        "SELECT day, start_time, chag, disg, soc as soc FROM ads_report_chag_disg_union_30min"
        " where station=%s and station_type<=1 and day=%s order by start_time")

    # select_sql_2 = (
    #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     " where station=%s and day=%s order by hour")

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        try:
            province_ins = station.province
            # 先判断是否有手动添加的收益记录
            is_exists_station_income = models.StationIncome.objects.filter(
                station_id=station,
                master_station=station.master_station,
                income_date__day=time_ins.day,
                income_date__month=time_ins.month,
                income_date__year=time_ins.year,
                record=1,
            ).exists()

            # 不存在手动添加的收益记录
            if not is_exists_station_income:

                total = 0

                results = ads_db_tool.select_many(select_sql, station.english_name, time_str)

                if results:
                    for i in results:
                        # 获取峰谷电表
                        # moment = f'{int(i["hour"])}:00' if int(i["hour"]) >= 10 else f'0{int(i["hour"])}:00'
                        moment = datetime.datetime.strptime(i["start_time"], "%Y-%m-%d %H:%M:%S").strftime("%H:%M")
                        peak_ins = models.PeakValleyNew.objects.filter(
                            year_month=year_month, province=province_ins, level=station.level, type=station.type, moment=moment
                        ).first()
                        temp_dict = {
                            "charge": round(float(i["chag"]), 2),
                            "discharge": round(float(i["disg"]), 2),
                            "time": i["start_time"],
                            "type": getattr(peak_ins, 'pv')
                        }

                        chag_price, disg_price = new_get_price_v2(station, time_ins.date(), peak_ins)
                        # logging.info(f"!!!!!!{station.station_name}查询到自定义电价：{temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}")
                        # if price is None:
                        #     price = float(getattr(peak_ins, "price"))
                        logging.info(f"######{station.station_name}计算计算使用电价为：{temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}")
                        moment_income = temp_dict['discharge'] * disg_price - temp_dict['charge'] * chag_price
                        total += moment_income

                    error_log.error(f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{total}元")
                    if total < 0:
                        total = 0
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=time_ins.day,
                        income_date__month=time_ins.month,
                        income_date__year=time_ins.year,
                        record=2,
                    )
                    if not income_ins.exists():
                        try:
                            models.StationIncome.objects.create(
                                peak_load_shifting=total, station_id=station, income_date=time_ins, income_type=1,
                                record=2, master_station=station.master_station
                            )
                        except Exception as e:
                            raise e
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                else:
                    error_log.error(f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

            # 已存在手动添加的收入 不进行自动添加
            else:
                error_log.error(f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')

    error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")


def timing_income_v4():
    """
    电量：ads: union_1d;
    电价: t_price_new
    """""
    time_ins = datetime.datetime.now()
    time_str = time_ins.strftime("%Y-%m-%d")
    year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
    error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

    # 改查新综合过结算表和计量表的新1h冻结表：表名未定
    select_sql = (
        "SELECT day, station, pointed_chag, pointed_disg, peak_chag, peak_disg, flat_chag, flat_disg, valley_chag, valley_disg, dvalley_chag, dvalley_disg FROM ads_report_chag_disg_union_1d"
        " where station_type<=1 and day=%s")

    # select_sql_2 = (
    #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     " where station=%s and day=%s order by hour")

    # 查询当月所有电价
    day_price_res = models.PeakValleyNew.objects.filter(year_month=time_ins.strftime("%Y-%m")).all()
    price_new_dict = {}
    for price in day_price_res:
        k = f"{price.province_id}-{price.type}-{price.level}"  # 定义唯一key
        pv_k = price.pv  # 小时峰谷标识
        if price_new_dict.get(k):
            if price_new_dict.get(k).get(pv_k):
                continue
            else:
                price_new_dict[k][pv_k] = price.price
        else:
            price_new_dict[k] = {
                pv_k: price.price
            }

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()

    # 查询所有电站的电量数据
    all_data = {}
    stations_data = ads_db_tool.select_many(select_sql, time_str)
    if stations_data:
        for s_data in stations_data:
            all_data[s_data['station']] = s_data

    for station in stations_ins:
        try:
            # province_ins = station.province
            # 先判断是否有手动添加的收益记录
            is_exists_station_income = models.StationIncome.objects.filter(
                station_id=station,
                master_station=station.master_station,
                income_date__day=time_ins.day,
                income_date__month=time_ins.month,
                income_date__year=time_ins.year,
                record=1,
            ).exists()

            # 不存在手动添加的收益记录
            if not is_exists_station_income:

                total = 0

                i = all_data.get(station.english_name)
                if i:

                    temp_dict = {
                        "pointed_chag": float(i["pointed_chag"]),
                        "pointed_disg": float(i["pointed_disg"]),
                        "peak_chag": float(i["peak_chag"]),
                        "peak_disg": float(i["peak_disg"]),
                        "flat_chag": float(i["flat_chag"]),
                        "flat_disg": float(i["flat_disg"]),
                        "valley_chag": float(i["valley_chag"]),
                        "valley_disg": float(i["valley_disg"]),
                        "dvalley_chag": float(i["dvalley_chag"]),
                        "dvalley_disg": float(i["dvalley_disg"])
                    }

                    price_dict = get_station_price_optimize(station, time_ins, price_new_dict)

                    print(1486, temp_dict, price_dict)

                    # error_log.error(
                    #     f"######{station.station_name}-{time_ins}计算收益使用电价为：尖峰电价：{price_dict['spike_chag_price']}--平峰电价：{price_dict['flat_price']}--谷峰电价：{price_dict['valley_price']}"){temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}, 峰谷标识：{temp_dict['type']}")

                    moment_income = (round(temp_dict['pointed_disg'] * price_dict['spike_disg_price'], 4) - round(
                        temp_dict['pointed_chag'] * price_dict['spike_chag_price'], 4) +
                                     round((temp_dict['peak_disg'] * price_dict['peak_disg_price']), 4) - round(
                                temp_dict['peak_chag'] * price_dict['peak_chag_price'], 4) +
                                     round((temp_dict['flat_disg'] * price_dict['flat_disg_price']), 4) - round(
                                temp_dict['flat_chag'] * price_dict['flat_chag_price'], 4) +
                                     round((temp_dict['valley_disg'] * price_dict['valley_disg_price']), 4) - round(
                                temp_dict['valley_chag'] * price_dict['valley_chag_price'], 4) +
                                     round((temp_dict['dvalley_disg'] * price_dict['dvalley_disg_price']), 4) - round(
                                temp_dict['dvalley_chag'] * price_dict['dvalley_chag_price'], 4)
                                     )

                    total = round(moment_income, 2)

                    error_log.error(
                        f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{round(total, 2)}元")
                    if total < 0:
                        total = 0
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=time_ins.day,
                        income_date__month=time_ins.month,
                        income_date__year=time_ins.year,
                        record=2,
                    )
                    if not income_ins.exists():
                        try:
                            models.StationIncome.objects.create(
                                peak_load_shifting=round(total, 2), station_id=station, income_date=time_ins,
                                income_type=1,
                                record=2, master_station=station.master_station
                            )
                        except Exception as e:
                            raise e
                    else:
                        income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
                else:
                    error_log.error(
                        f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

            # 已存在手动添加的收入 不进行自动添加
            else:
                error_log.error(
                    f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')
            print(traceback.print_exc())

    error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")


def timing_income_v2_for_yesterday():
    time_ins = datetime.datetime.now() - datetime.timedelta(days=1)
    time_str = time_ins.strftime("%Y-%m-%d")
    year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
    error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

    # 改查新综合过结算表和计量表的新1h冻结表：表名未定
    select_sql = (
        "SELECT day, hour, chag, disg, soc as soc FROM ads_report_chag_disg_union_1h"
        " where station=%s and station_type<=1 and day=%s order by hour")

    # select_sql_2 = (
    #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     " where station=%s and day=%s order by hour")

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        try:
            province_ins = station.province

            # 先判断是否有手动添加的收益记录
            is_exists_station_income = models.StationIncome.objects.filter(
                station_id=station,
                master_station=station.master_station,
                income_date__day=time_ins.day,
                income_date__month=time_ins.month,
                income_date__year=time_ins.year,
                record=1,
            ).exists()

            # 不存在手动添加的收益记录
            if not is_exists_station_income:

                total = 0

                # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
                # meter_use_time = MeterUseTime.objects.filter(station=station.master_station, is_use=1).first()
                # is_use_account = (station.master_station.is_account and meter_use_time and
                #                   meter_use_time.start_time <= time_ins <= meter_use_time.end_time)
                #
                # if not is_use_account:
                #     # 查询ads_report_chag_disg_data数据
                #     results = ads_db_tool.select_many(select_sql, station.english_name, time_str)
                #     # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
                # else:
                #     results = ads_db_tool.select_many(select_sql_2, station.english_name, time_str)

                results = ads_db_tool.select_many(select_sql, station.english_name, time_str)

                if results:
                    for i in results:
                        moment = f'{int(i["hour"])}:00' if int(i["hour"]) >= 10 else f'0{int(i["hour"])}:00'
                        # 获取峰谷电表
                        peak_ins = models.PeakValleyNew.objects.filter(
                            year_month=year_month, province=province_ins, level=station.level, type=station.type, moment=moment
                        ).first()

                        temp_dict = {
                            "charge": round(float(i["chag"]), 2),
                            "discharge": round(float(i["disg"]), 2),
                            "time": i["day"].strftime("%Y-%m-%d") + ' ' + str(i["hour"]) + ":00:00",
                            "type": getattr(peak_ins, 'pv')
                        }
                        hour = str(i["hour"])

                        chag_price, disg_price = new_get_price_v2(station, time_ins.date(), peak_ins)
                        logging.info(
                            f"!!!!!!{station.station_name}查询到自定义电价：{time_str}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
                        # if price is None:
                        #     price = float(getattr(peak_ins, "price"))
                        logging.info(
                            f"######{station.station_name}计算计算使用电价为：{time_str}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
                        hour_income = temp_dict['discharge'] * disg_price - temp_dict['charge'] * chag_price
                        total += hour_income

                    error_log.error(f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{total}元")
                    if total < 0:
                        total = 0
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{total}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=time_ins.day,
                        income_date__month=time_ins.month,
                        income_date__year=time_ins.year,
                        record=2,
                    )
                    if not income_ins.exists():
                        try:
                            models.StationIncome.objects.create(
                                peak_load_shifting=total, station_id=station, income_date=time_ins, income_type=1,
                                record=2, master_station=station.master_station
                            )
                        except Exception as e:
                            raise e
                    else:
                        income_ins.update(peak_load_shifting=total, income_type=1, record=2)
                else:
                    error_log.error(f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

            # 已存在手动添加的收入 不进行自动添加
            else:
                error_log.error(f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')

    error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")


def timing_income_v4_for_yesterday():
    time_ins = datetime.datetime.now() - datetime.timedelta(days=1)
    time_str = time_ins.strftime("%Y-%m-%d")
    year_month = f'{time_ins.year}-{time_ins.month}' if time_ins.month >= 10 else f'{time_ins.year}-0{time_ins.month}'
    error_log.error(f"========================开始计算所有电站{time_str}日收益=================================")

    # 改查新综合过结算表和计量表的新1h冻结表：表名未定
    select_sql = (
        "SELECT day,station, pointed_chag, pointed_disg, peak_chag, peak_disg, flat_chag, flat_disg, valley_chag, valley_disg, dvalley_chag, dvalley_disg FROM ads_report_chag_disg_union_1d"
        " where station_type<=1 and day=%s")

    # select_sql_2 = (
    #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
    #     " where station=%s and day=%s order by hour")

    # 查询当月所有电价
    day_price_res = models.PeakValleyNew.objects.filter(year_month=time_ins.strftime("%Y-%m")).all()
    price_new_dict = {}
    for price in day_price_res:
        k = f"{price.province_id}-{price.type}-{price.level}"  # 定义唯一key
        pv_k = price.pv  # 小时峰谷标识
        if price_new_dict.get(k):
            if price_new_dict.get(k).get(pv_k):
                continue
            else:
                price_new_dict[k][pv_k] = price.price
        else:
            price_new_dict[k] = {
                pv_k: price.price
            }

    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()

    # 查询所有电站的电量数据
    all_data = {}
    stations_data = ads_db_tool.select_many(select_sql, time_str)
    if stations_data:
        for s_data in stations_data:
            all_data[s_data['station']] = s_data

    for station in stations_ins:
        try:
            # province_ins = station.province
            # 先判断是否有手动添加的收益记录
            is_exists_station_income = models.StationIncome.objects.filter(
                station_id=station,
                master_station=station.master_station,
                income_date__day=time_ins.day,
                income_date__month=time_ins.month,
                income_date__year=time_ins.year,
                record=1,
            ).exists()

            # 不存在手动添加的收益记录
            if not is_exists_station_income:

                total = 0

                i = all_data.get(station.english_name)
                if i:

                    temp_dict = {
                        "pointed_chag": float(i["pointed_chag"]),
                        "pointed_disg": float(i["pointed_disg"]),
                        "peak_chag": float(i["peak_chag"]),
                        "peak_disg": float(i["peak_disg"]),
                        "flat_chag": float(i["flat_chag"]),
                        "flat_disg": float(i["flat_disg"]),
                        "valley_chag": float(i["valley_chag"]),
                        "valley_disg": float(i["valley_disg"]),
                        "dvalley_chag": float(i["dvalley_chag"]),
                        "dvalley_disg": float(i["dvalley_disg"])
                    }

                    price_dict = get_station_price_optimize(station, time_ins, price_new_dict)

                    # print(1486, temp_dict, price_dict)

                    # error_log.error(
                    #     f"######{station.station_name}-{time_ins}计算收益使用电价为：尖峰电价：{price_dict['spike_chag_price']}--平峰电价：{price_dict['flat_price']}--谷峰电价：{price_dict['valley_price']}"){temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}, 峰谷标识：{temp_dict['type']}")

                    moment_income = (round(temp_dict['pointed_disg'] * price_dict['spike_disg_price'], 4) - round(
                        temp_dict['pointed_chag'] * price_dict['spike_chag_price'], 4) +
                                     round((temp_dict['peak_disg'] * price_dict['peak_disg_price']), 4) - round(
                                temp_dict['peak_chag'] * price_dict['peak_chag_price'], 4) +
                                     round((temp_dict['flat_disg'] * price_dict['flat_disg_price']), 4) - round(
                                temp_dict['flat_chag'] * price_dict['flat_chag_price'], 4) +
                                     round((temp_dict['valley_disg'] * price_dict['valley_disg_price']), 4) - round(
                                temp_dict['valley_chag'] * price_dict['valley_chag_price'], 4) +
                                     round((temp_dict['dvalley_disg'] * price_dict['dvalley_disg_price']), 4) - round(
                                temp_dict['dvalley_chag'] * price_dict['dvalley_chag_price'], 4))

                    total = round(moment_income, 2)

                    error_log.error(
                        f"电站：{station.station_name}{time_str}日收益计算完成================ 总收益为:{round(total, 2)}元")
                    if total < 0:
                        total = 0
                        error_log.error(
                            f"电站：{station.station_name}{time_str}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
                    income_ins = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=time_ins.day,
                        income_date__month=time_ins.month,
                        income_date__year=time_ins.year,
                        record=2,
                    )
                    if not income_ins.exists():
                        try:
                            models.StationIncome.objects.create(
                                peak_load_shifting=round(total, 2), station_id=station, income_date=time_ins,
                                income_type=1,
                                record=2, master_station=station.master_station
                            )
                        except Exception as e:
                            raise e
                    else:
                        income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
                else:
                    error_log.error(
                        f"电站：{station.station_name}{time_str}日没有找到相关数据，不进行收益计算=====================")

            # 已存在手动添加的收入 不进行自动添加
            else:
                error_log.error(
                    f"电站：{station.station_name}{time_str}日已存在手动添加的收入 不进行自动添加=====================")
        except Exception as e:
            error_log.error(f'！！！！电站：{station.station_name}{time_str}日的收益计算报错:{e}，请排查原因并重新计算！')
            print(traceback.print_exc())

    error_log.error(f"=====================所有电站{time_str}日收益计算已完成==============================")


# @app.task
def timing_income():
    """实时收入计算"""
    current_date = datetime.date.today()
    # 生成24个小时的时间戳
    timestamps = []

    for i in range(24):
        # 构造具体的日期和时间
        dt = datetime.datetime.combine(current_date, datetime.time(hour=i))
        # 转换为时间戳（以秒为单位）
        timestamp = int(dt.timestamp())
        timestamps.append(timestamp)
    yesterday = current_date + datetime.timedelta(days=1)
    yesterday_time_stamp = datetime.datetime.combine(yesterday, datetime.time(hour=0))
    yesterday_time_stamp = int(yesterday_time_stamp.timestamp())
    timestamps.append(yesterday_time_stamp)
    ins = models.PeakValley.objects.filter(year_month__month=current_date.month, year_month__year=current_date.year)
    url = "http://172.17.6.44:9001/api/point/getHistoryDataV2"
    stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
    for station in stations_ins:
        exists = models.StationIncome.objects.filter(
            station_id=station,
            income_date__day=current_date.day,
            income_date__month=current_date.month,
            income_date__year=current_date.year,
            record=1,
        )
        if not exists:
            total = 0
            for i in range(len(timestamps) - 1):
                post_json = {
                    "startTime": str(int(timestamps[i])),
                    "endTime": str(int(timestamps[i + 1])),
                    "datatype": "cumulant",
                    "app": station.app,
                    "order": "DESC",
                    "pageSize": 1,
                    "station": station.english_name,
                    "startPage": 1,
                }
                _post_json = {
                    "startTime": str(int(timestamps[i])),
                    "endTime": str(int(timestamps[i + 1])),
                    "datatype": "cumulant",
                    "app": station.app,
                    "order": "ASC",
                    "pageSize": 1,
                    "station": station.english_name,
                    "startPage": 1,
                }
                response = requests.post(url=url, json=post_json)
                _response = requests.post(url=url, json=_post_json)
                if not response.json()['datas']:
                    CuCha = 0
                    CuDis = 0
                else:
                    data = response.json().get("datas").get("list")[0]["dataInfo"]
                    info = json.loads(data)
                    CuCha = info["body"][-1]["CuCha"]  # 累计充电量
                    CuDis = info["body"][-1]["CuDis"]  # 累放电量

                if not _response.json()['datas']:
                    _CuCha = 0
                    _CuDis = 0
                else:
                    data = _response.json().get("datas").get("list")[0]["dataInfo"]
                    info = json.loads(data)
                    _CuCha = info["body"][-1]["CuCha"]  # 累计充电量
                    _CuDis = info["body"][-1]["CuDis"]  # 累放电量

                CuCha_ = int(float(CuCha)) - int(float(_CuCha))
                CuDis_ = int(float(CuDis)) - int(float(_CuDis))

                income = int(float(CuDis_)) - int(float(CuCha_))
                price = ins.values(f"h{i}").first()[f"h{i}"]
                one_incom = float(price) * income
                total += one_incom
            if total < 0:
                total = 0
            income_ins = models.StationIncome.objects.filter(
                station_id=station,
                income_date__day=current_date.day,
                income_date__month=current_date.month,
                income_date__year=current_date.year,
                record=2,
            )
            if not income_ins:
                models.StationIncome.objects.create(
                    peak_load_shifting=total, station_id=station, income_date=current_date, income_type=1, record=2
                )
            else:
                income_ins.update(peak_load_shifting=total, income_type=1, record=2)
        else:
            print("已存在手动添加的收入 不进行自动添加=====================")


# @app.task
def plan_history():
    """计划功率下发"""
    time_now = datetime.datetime.now()
    time_5 = datetime.datetime.now() + datetime.timedelta(seconds=302)
    tasks = models.StationPlanHistory.objects.filter(
        start_time__range=[
            time_now,
            time_5,
        ],
        status=1,
    ).all()
    if tasks:
        for task in tasks:
            name = task.station.english_name
            # slave_station = models.StationDetails.objects.filter(english_name=name, slave=-1, pack=-1).first()
            # if slave_station:
            #     app = slave_station.app
            #     storage_name = slave_station.english_name
            # else:
            #     name = name + '_000'
            #     slave_station = models.StationDetails.objects.filter(english_name=name).first()
            #     app = slave_station.app
            #     storage_name = slave_station.english_name

            # 兼容标准站 & 标准主从站 & EMS级联主从站模式
            ems_station = models.StationDetails.objects.filter(is_delete=0, english_name=name).first()
            storage_name = ems_station.english_name
            app = ems_station.app

            start_time = task.start_time
            end_time = task.end_time
            topic = f"req/database/parameter/{storage_name}/{app}"
            # topic = f"req/database/parameter/SAMPLE1/TN002"
            secret_key = settings.AES_KEY
            aes = EncryptDate(secret_key)
            token = aes.encrypt(task.station.english_name)
            power = task.power
            follow = task.power_follow
            message_before = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    {"EAEn": "1", "type": "parameter"},  # 功能策略启停
                    # {"ENAu": "1", "type": "parameter"},  # 接受云端调度
                    # {"CPlan": "1", "type": "parameter"},  # 云端计划模式
                ],
            }
            message = {
                "time": str(int(time.time())),
                "token": token,
                "device": "EMS",
                "body": [
                    # {"Cplan": "1", "type": "parameter"},  # 云端计划模式
                    {"LLF": str(follow), "type": "parameter"},  # 符合跟随调度
                    {"YSCP": str(start_time.year), "type": "parameter"},  # 起始年
                    {"MSCP": str(start_time.month), "type": "parameter"},  # 起始月
                    {"CPDS": str(start_time.day), "type": "parameter"},  # 起始日
                    {"TSCP": str(start_time.hour), "type": "parameter"},  # 起始时
                    {"CPMS": str(start_time.minute), "type": "parameter"},  # 起始分
                    {"TYS": str(end_time.year), "type": "parameter"},  # 截止年
                    {"TMS": str(end_time.month), "type": "parameter"},  # 截止月
                    {"TMD": str(end_time.day), "type": "parameter"},  # 截止日
                    {"TTS": str(end_time.hour), "type": "parameter"},  # 截止时
                    {"STMin": str(end_time.minute), "type": "parameter"},  # 截止分
                    {"RCTO": str(float(power)), "type": "parameter"},  # 功率
                ],
            }

            json_message_before = json.dumps(message_before)
            json_message = json.dumps(message)
            print(f"下发参数为{json_message}", "=============================")
            client = mqtt.Client()
            client.on_connect = on_connect
            client.on_message = on_message
            client.username_pw_set(settings.MQTT_USER, settings.MQTT_PASSWORD)
            client.connect(host=settings.MQTT_SERVER, port=settings.MQTT_PORT, keepalive=settings.MQTT_KEEPALIVE)

            # json_message_before = json.dumps(json_message_before)
            # json_message = json.dumps(message)
            client.publish(topic, json_message_before)
            print("等待接收云端调度关闭===")

            time.sleep(5)
            client.publish(topic, json_message)
            print("历史任务下发成功", "=============================")
            task.status = 2
            task.save()
            # request_dic = {
            #     "app": app,
            #     "time": str(time.time()),
            #     "station": storage_name,
            #     "body": [
            #         {"device": "BMS", "datatype": "measure", "totalcall": "0",
            #          "body": ["NBSC", "BQ", "ChaED", "DisED"]},
            #         {"device": "BMS1", "datatype": "measure", "totalcall": "0",
            #          "body": ["NBSC", "BQ", "ChaED", "DisED"]},
            #         {"device": "BMS2", "datatype": "measure", "totalcall": "0",
            #          "body": ["NBSC", "BQ", "ChaED", "DisED"]},
            #         {"device": "BMS3", "datatype": "measure", "totalcall": "0",
            #          "body": ["NBSC", "BQ", "ChaED", "DisED"]},
            #         {"device": "PCS", "datatype": "measure", "totalcall": "0", "body": ["ChaD", "DisD", "P", "Q"]},
            #         {"device": "PCS1", "datatype": "measure", "totalcall": "0", "body": ["ChaD", "DisD", "P", "Q"]},
            #         {"device": "PCS2", "datatype": "measure", "totalcall": "0", "body": ["ChaD", "DisD", "P", "Q"]},
            #         {"device": "PCS3", "datatype": "measure", "totalcall": "0", "body": ["ChaD", "DisD", "P", "Q"]},
            #         {"device": "PCS", "datatype": "status", "totalcall": "0", "body": ["Fault", "alarm"]},
            #         {"device": "PCS1", "datatype": "status", "totalcall": "0", "body": ["Fault", "alarm"]},
            #         {"device": "PCS2", "datatype": "status", "totalcall": "0", "body": ["Fault", "alarm"]},
            #         {"device": "PCS3", "datatype": "status", "totalcall": "0", "body": ["Fault", "alarm"]},
            #         {"device": "EMS", "datatype": "status", "totalcall": "0", "body": ["AEnC", "AEn", "PRun", "PStse"]},
            #         {"device": "PCS", "datatype": "measure", "totalcall": "0", "body": ["PP1"]},
            #         {"device": "PCS1", "datatype": "measure", "totalcall": "0", "body": ["PP1"]},
            #         {"device": "PCS2", "datatype": "measure", "totalcall": "0", "body": ["PP1"]},
            #         {"device": "PCS3", "datatype": "measure", "totalcall": "0", "body": ["PP1"]},
            #         {"device": "BMS", "datatype": "status", "totalcall": "0", "body": ["SQse"]},
            #         {"device": "BMS1", "datatype": "status", "totalcall": "0", "body": ["SQse"]},
            #         {"device": "BMS2", "datatype": "status", "totalcall": "0", "body": ["SQse"]},
            #         {"device": "BMS3", "datatype": "status", "totalcall": "0", "body": ["SQse"]},
            #     ],
            # }
            # print(json.dumps(request_dic))
            # url = "http://172.17.6.44:9001/api/point/realtimeDataRelease"
            # time.sleep(5)
            # response = requests.post(url=url, json=request_dic)
            # print(response.json())

    else:
        print("计划查询定时任务下发成功 未发现任务=======================================")
    done_task = models.StationPlanHistory.objects.filter(start_time__lte=datetime.datetime.now(), status=2)
    for task in done_task:
        task.status = 3
        task.save()
    _done_task = models.StationPlanHistory.objects.filter(start_time__lte=datetime.datetime.now(), status=1)
    for task in _done_task:
        task.status = 4
        task.save()



# @app.task
def sed_email():

    gz_tel_list = ['13810897522', '18611739782', '13701673770', '18513185086', '18612300676', '15732622193',
                   '18811326855']
    # url = "http://172.17.6.44:9001/api/point/getRealtimeData"
    stations_obj = models.StationDetails.objects.filter(is_delete=0).all()

    for station in stations_obj:
        # 警告/报警推送
        to_addrs = [
            # "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>",
            "<EMAIL>",
        ]  # 报警推送人

        ins = models.FaultAlarm.objects.using('alarm_module').filter(station_id=station.id, type=2, status=0)
        if ins:
            for i in ins:
                # 将站点对应用户添加到发送邮件列表
                users = models.UserDetails.objects.filter(stations=station)
                for user in users:
                    if user.type == 1 and user.email:
                        to_addrs.append(user.email)

                mes = f"报警发生时间:  {i.start_time},报警点位:  {i.point},  报警详情:  {i.details}。 \n"
                Subject = f'{datetime.datetime.today().strftime("%Y-%m-%d")}来自{station.station_name}的报修消息'
                sender_show = "白泽添禄报警邮件"
                recipient_show = "白泽添禄报警邮件"
                sendMail_(mes, Subject, sender_show, recipient_show, to_addrs)

        unit_ins = models.Unit.objects.filter(is_delete=0, station=station).all()
        for unit in unit_ins:

            conn_ = get_redis_connection("3")
            key1 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                           unit.pcs)
            status_pcs = conn_.get(key1)
            if status_pcs:
                status_pcs_dict = json.loads(json.loads(status_pcs.decode("utf-8")))
            else:
                status_pcs_dict = {}


            key2 = "Business:TRANS_TIMING_{}_{}_{}".format('STATUS', station.english_name,
                                                           unit.bms)
            status_bms = conn_.get(key2)
            if status_bms:
                status_bms_dict = json.loads(json.loads(status_bms.decode("utf-8")))
            else:
                status_bms_dict = {}

            conn = get_redis_connection("default")
            # body = return_dic.get("body", None)
            # if not body:
            #     return "http调用安昌数据库查询接口失败"

            Fault = status_pcs_dict.get("Fault")  # 故障状态
            # Fault = "1"
            Fault_send_str = "Fault"

            GFault = status_bms_dict.get("GFault")  # ComFau压缩机故障
            GFault_send_str = "GFault"

            ComFau = status_bms_dict.get("ComFau")  # GFault pcs三级故障
            ComFau_send_str = "ComFau"

            Subject = '电站告警'
            sender_show = '天禄小程序自动预警'
            recipient_show = '天禄小程序自动预警'
            if str(Fault) == "0":
                conn.set(str(station.station_name + Fault_send_str), str(Fault))
            if str(Fault) == "0":
                conn.set(str(station.station_name + GFault_send_str), str(GFault))
            if str(Fault) == "0":
                conn.set(str(station.station_name + ComFau_send_str), str(ComFau))

            if str(Fault) == "1":
                print("出现故障===============")
                redis_str_Fault = conn.get(str(station.station_name + Fault_send_str))
                if not redis_str_Fault or str(redis_str_Fault.decode('utf-8')) != str(Fault):
                    message = (
                        f"天禄小程序故障警告  :站名  {station.station_name}   时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')} PCS发生故障"
                    )
                    for phone in gz_tel_list:
                        code = Sample.main(phone, 00, msg=message)
                        if code != 200:
                            error_log("就地控制发送短信:短信下发失败")

                    conn.set(str(station.station_name + Fault_send_str), str(Fault))  # redis 数据格式 monitor_mobile
                    print("故障状态发送完成================")

                if str(GFault) == "1":
                    redis_str_GFault = conn.get(str(station.station_name + GFault_send_str))
                    if not redis_str_GFault or str(redis_str_Fault.decode('utf-8')) != str(GFault):
                        message = (
                            f"天禄小程序故障警告  :  站名{station.station_name}  时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')} 压缩机发生故障"
                        )
                        # sendMail_(mes, Subject, sender_show, recipient_show, to_addrs)
                        for phone in gz_tel_list:
                            code = Sample.main(phone, 00, msg=message)
                            if code != 200:
                                error_log("就地控制发送短信:短信下发失败")

                        conn.set(
                            str(station.station_name + GFault_send_str),
                            str(GFault),
                        )  # redis 数据格式 monitor_mobile
                        print("压缩机故障状态发送完成================")
                if str(ComFau) == "1":
                    redis_str_ComFau = conn.get(str(station.station_name + ComFau_send_str))
                    if not redis_str_ComFau or str(redis_str_ComFau.decode('utf-8')) != str(ComFau):
                        message = f"天禄小程序故障警告  :  站名{station.station_name}  时间:{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')} 发生 BMS三级故障"
                        for phone in gz_tel_list:
                            code = Sample.main(phone, 00, msg=message)
                            if code != 200:
                                error_log("就地控制发送短信:短信下发失败")
                        conn.set(
                            str(station.station_name + ComFau_send_str),
                            str(ComFau),
                        )  # redis 数据格式 monitor_mobile
                        print("pcs三级故障状态发送完成================")
                else:
                    print("无故障 不需要发送短信")

