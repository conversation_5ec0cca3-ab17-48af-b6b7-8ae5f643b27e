package com.robestec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.central.common.model.PageResult;
import com.central.common.service.impl.SuperServiceImpl;
import com.robestec.analysis.dto.station.StationCreateDTO;
import com.robestec.analysis.dto.station.StationQueryDTO;
import com.robestec.analysis.dto.station.StationUpdateDTO;
import com.robestec.analysis.entity.Station;
import com.robestec.analysis.mapper.StationMapper;
import com.robestec.analysis.service.StationService;
import com.robestec.analysis.vo.StationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 电站服务实现类
 */
@Slf4j
@Service
public class StationServiceImpl extends SuperServiceImpl<StationMapper, Station>
        implements StationService {

    @Override
    public PageResult<StationVO> queryStation(StationQueryDTO queryDTO) {
        LambdaQueryWrapper<Station> wrapper = new LambdaQueryWrapper<Station>()
                .like(StringUtils.hasText(queryDTO.getName()), Station::getName, queryDTO.getName())
                .like(StringUtils.hasText(queryDTO.getDescr()), Station::getDescr, queryDTO.getDescr())
                .eq(queryDTO.getRegister() != null, Station::getRegister, queryDTO.getRegister())
                .eq(queryDTO.getIndex() != null, Station::getIndex, queryDTO.getIndex())
                .ge(StringUtils.hasText(queryDTO.getStartTime()), Station::getCreateTime, queryDTO.getStartTime())
                .le(StringUtils.hasText(queryDTO.getEndTime()), Station::getCreateTime, queryDTO.getEndTime())
                .orderByAsc(Station::getIndex) // 按索引升序排列
                .orderByDesc(Station::getCreateTime);

        Page<Station> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<Station> result = this.page(page, wrapper);

        List<StationVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<StationVO>builder()
                .data(voList)
                .code(0)
                .count(result.getTotal())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStation(StationCreateDTO createDTO) {
        // 检查英文名称是否重复
        long count = this.count(Wrappers.<Station>lambdaQuery()
                .eq(Station::getName, createDTO.getName()));
        if (count > 0) {
            throw new RuntimeException("电站英文名称已存在");
        }

        Station entity = BeanUtil.copyProperties(createDTO, Station.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStation(StationUpdateDTO updateDTO) {
        Station entity = BeanUtil.copyProperties(updateDTO, Station.class);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStation(Long id) {
        this.removeById(id);
    }

    @Override
    public StationVO getStation(Long id) {
        Station entity = this.getById(id);
        return convertToVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createStationList(List<StationCreateDTO> createDTOList) {
        List<Station> entityList = BeanUtil.copyToList(createDTOList, Station.class);
        this.saveBatch(entityList);
    }

    @Override
    public List<StationVO> getStationByName(String name) {
        List<Station> entityList = this.list(Wrappers.<Station>lambdaQuery()
                .like(Station::getName, name)
                .orderByDesc(Station::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<StationVO> getStationByRegister(Integer register) {
        List<Station> entityList = this.list(Wrappers.<Station>lambdaQuery()
                .eq(Station::getRegister, register)
                .orderByDesc(Station::getCreateTime));
        return entityList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long countByRegister(Integer register) {
        return this.count(Wrappers.<Station>lambdaQuery()
                .eq(Station::getRegister, register));
    }

    /**
     * 转换为VO对象
     */
    private StationVO convertToVO(Station entity) {
        if (entity == null) {
            return null;
        }
        StationVO vo = BeanUtil.copyProperties(entity, StationVO.class);
        vo.setRegisterName(getRegisterName(entity.getRegister()));
        return vo;
    }

    /**
     * 获取注册状态名称
     */
    private String getRegisterName(Integer register) {
        if (register == null) {
            return "";
        }
        return register == 1 ? "已注册" : "未注册";
    }
}
