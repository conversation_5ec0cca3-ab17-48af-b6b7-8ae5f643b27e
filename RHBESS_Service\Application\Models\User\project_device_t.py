#!/usr/bin/env python3
# encoding=utf-8
# @Time    : 2024/6/13 上午10:10
# <AUTHOR> huoyl
# Exp      : if you discover any questions, call for me

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
from Application.Models.User.station import Station
class ProjectDevice(user_Base):
    u'项目设备表'
    __tablename__ = "t_project_device"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"")
    op_ts = Column(DateTime, nullable=True, comment=u"录入时间")
    is_use = Column(Integer, nullable=True, server_default='1', comment=u"是否使用1是  0否")
    name = Column(VARCHAR(255), nullable=False, comment=u"单元名称")
    project_id = Column(Integer, nullable=True, comment=u"项目id")
    area = Column(VARCHAR(255), nullable=False, comment=u"区域")
    sn = Column(VARCHAR(255), nullable=True, comment=u"设备编号")
    type_id = Column(Integer, ForeignKey("t_device_type.id"), nullable=False,
                      comment=u"设备类型id")
    factory_id = Column(Integer, ForeignKey("t_manufacturer.id"), nullable=False,
                     comment=u"厂家id")
    device_no_id = Column(Integer, ForeignKey("t_device_no.id"), nullable=False,
                     comment=u"设备型号id")
    init_no = Column(VARCHAR(255), nullable=True, comment=u"设备出厂编号")
    init_cap = Column(Float, nullable=True, comment=u"设备额定容量")
    remark = Column(VARCHAR(255), nullable=True, comment=u"备注")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):

        bean = "{'id':'%s','op_ts':'%s','is_use':'%s','name':'%s','project_id':'%s','area':'%s','sn':'%s','type_id':'%s','factory_id':'%s','device_no_id':'%s','init_no':'%s','init_cap':'%s','remark':'%s',}" % (
            self.id, self.op_ts, self.is_use, self.name, self.project_id, self.area, self.sn, self.type_id, self.factory_id,self.device_no_id,self.init_no,self.init_cap,self.remark)
        return bean.replace("None", '')