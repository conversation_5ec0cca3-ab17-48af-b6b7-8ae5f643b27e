#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-10 14:28:06
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\report_f.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-09-30 11:52:43

from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Tools.Utils.time_utils import timeUtils

class FReport1(user_Base):
    u'报表冻结数据'
    __tablename__ = "f_report_copy1"
    name = Column(VARCHAR(250), nullable=False,primary_key=True,comment=u"名称")
    jf_chag = Column(VARCHAR(256), nullable=True,comment=u"尖峰充电电量值，[]")
    jf_disg = Column(VARCHAR(256), nullable=True,comment=u"尖峰放电电量值，[]")
    fd_chag = Column(VARCHAR(256), nullable=True,comment=u"峰段充电电量值，[]")
    fd_disg = Column(VARCHAR(256), nullable=True,comment=u"峰段放电电量值，[]")
    pd_chag = Column(VARCHAR(256), nullable=True,comment=u"平段充电电量值，[]")
    pd_disg = Column(VARCHAR(256), nullable=True,comment=u"平段放电电量值，[]")
    gd_chag = Column(VARCHAR(256), nullable=True,comment=u"谷段充电电量值，[]")
    gd_disg = Column(VARCHAR(256), nullable=True,comment=u"谷段放电电量值，[]")
    ratio = Column(Float, nullable=False,comment=u"效率")
    day = Column(DateTime, nullable=False,primary_key=True,comment=u"数据时间")
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    cause = Column(CHAR(2), nullable=False,comment=u"1：日数据2月数据")
    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'name':'%s','jf_chag':'%s','jf_disg':'%s','fd_chag':'%s','fd_disg':'%s','pd_chag':'%s','pd_disg':'%s','gd_chag':'%s','gd_disg':'%s','ratio':%s,'day':'%s','op_ts':'%s','cause':%s}" % (
            self.name,self.jf_chag,self.jf_disg,self.fd_chag,self.fd_disg,self.pd_chag,self.pd_disg,self.gd_chag,self.gd_disg,self.ratio,self.day,self.op_ts,self.cause)
        
   