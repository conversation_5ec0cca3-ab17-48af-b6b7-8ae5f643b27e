package com.robestec.analysis.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 远程控制策略相关常量
 * 对应Python中的常量定义
 */
public class TelecontrolConstants {

    /**
     * 下发类型映射
     * 对应Python中的ISSUANCE_TYPE
     */
    public static final Map<String, String> ISSUANCE_TYPE = new HashMap<>();
    
    static {
        ISSUANCE_TYPE.put("1", "整站控制模式");
        ISSUANCE_TYPE.put("2", "自动最大充电功率");
        ISSUANCE_TYPE.put("3", "自动最大放电功率");
        ISSUANCE_TYPE.put("4", "手动最大充放电功率");
        ISSUANCE_TYPE.put("5", "全站功率");
        ISSUANCE_TYPE.put("6", "需量目标功率");
        ISSUANCE_TYPE.put("7", "变压器容量");
        ISSUANCE_TYPE.put("8", "变压器安全系数");
        ISSUANCE_TYPE.put("9", "防逆流阈值");
        ISSUANCE_TYPE.put("10", "PCS控制模式");
        ISSUANCE_TYPE.put("11", "PCS开关");
        ISSUANCE_TYPE.put("12", "PCS功率");
        ISSUANCE_TYPE.put("13", "故障复位");
        ISSUANCE_TYPE.put("14", "电操状态");
        ISSUANCE_TYPE.put("15", "故障复归");
        ISSUANCE_TYPE.put("16", "分系统使能");
        ISSUANCE_TYPE.put("17", "功率计划下发");
        ISSUANCE_TYPE.put("18", "运行策略修改");
        ISSUANCE_TYPE.put("19", "策略下发");
    }

    /**
     * 计划状态
     */
    public static final class PlanStatus {
        public static final int SAVED = 1;          // 已保存
        public static final int DELIVERED = 2;      // 已下发
        public static final int EXECUTING = 3;      // 执行中
        public static final int COMPLETED = 4;      // 已完成
        public static final int FAILED = 5;         // 下发失败
        public static final int STOPPED = 6;        // 已停止
    }

    /**
     * 计划类型
     */
    public static final class PlanType {
        public static final int CUSTOM = 1;         // 自定义
        public static final int PERIODIC = 2;       // 周期性
        public static final int HOLIDAY = 3;        // 节假日
    }

    /**
     * 下发结果状态
     */
    public static final class DeliveryStatus {
        public static final int SUCCESS = 1;        // 成功
        public static final int FAILURE = 2;        // 失败
    }

    /**
     * 是否使用标识
     */
    public static final class UseFlag {
        public static final int USED = 1;           // 使用
        public static final int NOT_USED = 0;       // 不使用
    }

    /**
     * 是否跟随标识
     */
    public static final class FollowFlag {
        public static final int FOLLOW = 1;         // 跟随
        public static final int NOT_FOLLOW = 0;     // 不跟随
    }

    /**
     * 是否删除标识
     */
    public static final class DeleteFlag {
        public static final int DELETED = 1;        // 已删除
        public static final int NOT_DELETED = 0;    // 未删除
    }

    /**
     * 电站类型级别配置
     * 对应Python中的station_type_level
     */
    public static final Map<String, Map<String, Object>> STATION_TYPE_LEVEL = new HashMap<>();
    
    static {
        Map<String, Object> siklyConfig = new HashMap<>();
        siklyConfig.put("province_id", 10);
        siklyConfig.put("type", 4);
        siklyConfig.put("level", 3);
        STATION_TYPE_LEVEL.put("sikly", siklyConfig);

        Map<String, Object> tczjConfig = new HashMap<>();
        tczjConfig.put("province_id", 11);
        tczjConfig.put("type", 6);
        tczjConfig.put("level", 4);
        STATION_TYPE_LEVEL.put("tczj", tczjConfig);
    }

    /**
     * 状态字符串映射
     */
    public static final Map<String, String> STATUS_STRING_MAP = new HashMap<>();
    
    static {
        STATUS_STRING_MAP.put("1", "成功");
        STATUS_STRING_MAP.put("2", "失败");
    }

    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 策略模板文件名
     */
    public static final String STRATEGY_TEMPLATE_FILE = "策略模板.xlsx";

    /**
     * MinIO存储桶名称
     */
    public static final String MINIO_BUCKET_NAME = "tianlu";

    /**
     * Redis键前缀
     */
    public static final String REDIS_KEY_PREFIX_TELECONTROL_STRATEGY = "telecontrol_strategy_";

    /**
     * 空字符串列表
     */
    public static final String[] EMPTY_STR_LIST = {"None", "", "--", "null", null};
}
