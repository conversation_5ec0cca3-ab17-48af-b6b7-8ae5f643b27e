#!/usr/bin/env python
# coding=utf-8
# @Information:  bams历史数据

import ast

import json
import tornado.web
from Application.Models.base_handler import BaseHandler
from Tools.Cfg.DB_his import get_dhis
from Tools.DB.zgtian_bms_his import zgtian1_bms_session, zgtian2_bms_session, zgtian3_bms_session, zgtian4_bms_session
from Tools.Utils.time_utils import timeUtils
from Application.Models.His.r_ACDMS import HisACDMS
from Tools.DB.binhai_bms_his import binhai_session1, binhai_session2
from Tools.DB.taicang_bms_his import taicang_session1
from Tools.DB.ygzhen_bms_his import ygzhen_session1, ygzhen_session2
from Tools.Utils.num_utils import *
import numpy as np
import pandas as pd
from Tools.DB.mongodb_con import dongmu_mongodb_client, dongmu_mongo_db
import time as ttt
from Application.HistoryData.station_unit_cluster import station_unit_cluster_dict
model_config = configparser.ConfigParser()
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
path = basepath + "/Cfg/test.ini"
model_config.read(path, encoding='utf-8')
device_arr = ast.literal_eval(model_config.get('peizhi', 'device_arr'))  # 所有设备筛选
piece_arr = ast.literal_eval(model_config.get('peizhi', 'piece_arr'))  # 所有电池簇
piece_arr_gz = ast.literal_eval(model_config.get('peizhi', 'piece_arr_gz'))  # 所有电池簇
device_arr_en = ast.literal_eval(model_config.get('peizhi', 'device_arr_en'))  # 所有设备筛选
piece_arr_en = ast.literal_eval(model_config.get('peizhi', 'piece_arr_en'))  # 所有电池簇
piece_arr_en_gz = ast.literal_eval(model_config.get('peizhi', 'piece_arr_en_gz'))  # 所有电池簇
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu', 'tczj', 'sikly']
bmsdb, baodian_siss = get_dhis('his_bams')
# 单元所有参数
device_obj = ['Vol', 'Cur', 'Soc', 'SgVmax', 'SgVmin', 'SgTmax', 'SgTmin', 'SgVmaxRkID', 'SgVmaxCelID', 'SgVminRkID',
              'SgVminCelID', 'SgTmaxRkID', 'SgTmaxCelID', 'SgTminRkID', 'SgTminCelID', 'ChagCapy', 'DisgCapy']

# 太仓中集、赛科利等后续标准点位
bj_dwd_device_obj = {'systemVoltage': 'vol', 'systemCurrent': 'cur', 'SOC': 'soc', 'maxCellVoltage': 'max_vol', 'minCellVoltage': 'min_vol',
                     'aveCellVoltage': None, 'maxCellTemp': 'max_temp', 'minCellTemp': 'min_temp', 'aveCellTemp': None,
                     'maxVoltageCluster': 'max_vol_bc_rk', 'maxVoltageBox': None, 'maxVoltageNum': 'max_vol_cell_rk',
                     'minVoltageCluster': 'min_vol_bc_rk', 'minVoltageBox': None, 'minVoltageNum': 'min_vol_cell_rk',
                     'maxCellTempCluster': 'max_temp_bc_rk', 'maxCellTempBox': None, 'maxCellTempNum': 'max_temp_cell_rk',
                     'minCellTempCluster': 'min_temp_bc_rk', 'minCellTempBox': None, 'minCellTempNum': 'min_temp_cell_rk',
                     'maxChargingCurrent': None, 'maxDischargeCurrent': None
                     }
dwd_device_obj = ['Soc', 'SgVmax', 'SgVmin', 'SgTmax', 'SgTmin', 'SgVmaxRkID', 'SgVminRkID', 'SgTmaxRkID', 'SgTminRkID']
dwd_device_dict = {'Soc': 'soc', 'SgVmax': 'max_vol', 'SgVmin': 'min_vol', 'SgTmax': 'max_temp', 'SgTmin': 'min_temp', 'SgVmaxRkID': 'max_vol_bc_rk', 'SgVminRkID': 'min_vol_bc_rk', 'SgTmaxRkID': 'max_temp_bc_rk', 'SgTminRkID': 'min_temp_bc_rk'}

# 簇所有参数
piece_obj = ['SgVmax', 'SgVmin', 'SgVavg', 'SgTmax', 'SgTmin', 'SgTavg', 'SgVmaxCelID', 'SgVminCelID', 'SgTmaxCelID',
             'SgTminCelID']

# 所有电池电芯
cell_arr = 'SgVs%s%s%', 'SgTs%s%s%'

# 保电单元所有参数
device_obj_bd = ['.H.M.Vol', '.H.M.Cur', '.H.M.Soc', '.H.M.HestCelVol', '.H.M.LestCelVol', '.H.M.AvegCelVol',
                 '.H.M.HestCelTmp', '.H.M.LestCelTmp', '.H.M.AvegCelTmp', '.H.M.HestVCelRID', '.H.M.HestVCelPID',
                 '.H.M.LestVCelRID', '.H.M.LestVCelPID', '.H.M.HestTCelRID', '.H.M.HestTCelPID', '.H.M.LestTCelRID',
                 '.H.M.LestTCelPID', '.H.M.CanMaxChgCur', '.H.M.CanMaxDchgCur']
# 保电簇所有参数
piece_obj_bd = ['HestCelVol', 'LestCelVol', 'AvegCelVol', 'HestCelTmp', 'LestCelTmp', 'AvegCelTmp', 'HestVCelPID',
                'LestVCelPID', 'HestTCelPID', 'LestTCelPID']

paras_obj_bd = {'paras_1': ["HestCelVol", "HestCelTmp"],
                'paras_2': ["LestCelVol", "LestCelTmp"],
                'paras_3': ["AvegCelVol", "AvegCelTmp"]}  # 最大值；最小值；平均值

# 贵州单元所有参数

device_obj_gz = ['Volt', 'Cur', 'Soc', 'SigMxVol', 'SigMiVol', 'SigAvgVol', 'SigMxTem', 'SigMiTem', 'SigAvgTem',
                 'SgVmaxRkID', 'SgVmaxRkID', 'SgVmaxCelID', 'SgVminRkID', 'SgVminRkID', 'SgVminCelID',
                 'SgTmaxRkID', 'SgTmaxRkID', 'SgTmaxCelID', 'SgTminRkID', 'SgTminRkID', 'SgTminCelID', 'MxChgCur',
                 'MxDigCur']
# 贵州簇所有参数
piece_obj_gz = ['MxSigVol', 'MiSigVol', 'AvgSigVol', 'MxSigTem', 'MiSigTem', 'AvgSigTem', 'AvegCelTmp', 'MxVolPos',
                'MiVolPos', 'MxTmPos', 'MiTmPos']

paras_obj_gz = {'paras_1': ["MxSigVol", "MxSigTem"],
                'paras_2': ["MiSigVol", "MiSigTem"],
                'paras_3': ["AvgSigVol", "AvgSigTem"]}  # 极差，最大值；最小值；平均值
import pymysql
from dbutils.persistent_db import PersistentDB
from Application.Cfg.dir_cfg import model_config as model_config_base
pool = PersistentDB(pymysql, 10,**{
            "host": model_config_base.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config_base.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config_base.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config_base.get('mysql', "IDCD_DATABASE"),  # 数据库名称
            "port":  int(model_config_base.get('mysql', "IDC_PORT")),
            "cursorclass": pymysql.cursors.DictCursor
        })


class DeriveIntetface(BaseHandler):
    @tornado.web.authenticated
    def post(self, kt):
        self.refreshSession()  # 刷新session
        try:
        # if 1:
            if kt == 'BamsDatasDerive':
                db = self.get_argument('db', None)
                bams = self.get_argument('dui', [])  # 电池堆
                bank = self.get_argument('cu', [])  # 电池簇
                bamsName = self.get_argument('duiName', [])  # 电池堆名称
                bankName = self.get_argument('cuName', [])  # 电池簇名称
                time_ = self.get_argument('time', [])  # 时间数组
                name = self.get_argument('name', [])  # 储能单元，电池簇，电芯
                number = self.get_argument('number', [])  # 电芯编号
                lang = self.get_argument('lang', None)  # 英文网址

                logging.info('bams:%s,bank:%s,time_:%s,db:%s,bamsName:%s,bankName:%s,name:%s' % (
                bams, bank, time_, db, bamsName, bankName, name))
                conn = pool.connection()
                dwd_cursor = conn.cursor()
                bams = eval(bams)
                bank = eval(bank)
                if len(bams) > 5 or len(bank) > 5:
                    if lang == 'en':
                        return self.customError('Select up to 5, please re-select!')
                    else:
                        return self.customError('最多选择5个，请重新选择！')
                time_ = eval(time_)
                bamsName = eval(bamsName)
                bankName = eval(bankName)
                name = eval(name)
                if "电芯" in name and not number:
                    if lang == 'en':
                        return self.customError('the cell number is not filled in!')
                    else:
                        return self.customError('电芯编号未填写！')
                if number:
                    number = eval(number)
                    startN = number[0]
                    endnN = number[1]
                    # if int(endnN) - int(startN) > 5:
                    #     if lang == 'en':
                    #         return self.customError('Select up to 5, please re-select!')
                    #     else:
                    #         return self.customError('最多选择5个，请重新选择！')
                startTime = time_[0]
                endTime = time_[1]
                timelen = timeUtils.timeSeconds(startTime, endTime)
                coun = timelen // 50  # 秒
                st =startTime
                ed = endTime

                if len(name) == 1:
                    if name[0] == "储能单元" or name[0] == "Energy storage unit":  # 电池堆
                        if lang == 'en':
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                        else:
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                        his_datas_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                             startTime=startTime, endTime=endTime,
                                                             jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                        return self.returnTypeSuc(his_datas_bams)
                    elif name[0] == "电池簇" or name[0] == "Battery cluster":  # 电池簇
                        if lang == 'en':
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                            bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                        else:
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                            bank, bankName = self._for_all_bank(bank, bankName, db)
                        his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                             bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                             endTime=endTime, jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                        return self.returnTypeSuc(his_datas_bank)
                    elif name[0] == "电芯" or name[0] == "Batteries":  # 电芯
                        if lang == 'en':
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db, lang=lang)
                            else:
                                bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                        else:
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db)
                            else:
                                bank, bankName = self._for_all_bank(bank, bankName, db)
                        his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName,
                                                             startN=startN, endnN=endnN, st=st, ed=ed,
                                                             startTime=startTime, endTime=endTime,
                                                             jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                        return self.returnTypeSuc(his_datas_cell)
                elif len(name) == 2:
                    if lang == 'en':
                        if name[0] == "Energy storage unit" and name[1] == "Cluster":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                            bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                            his_data_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                                startTime=startTime, endTime=endTime,
                                                                jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                                 bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                                 endTime=endTime, jiange='5T', dwd_cursor=dwd_cursor)
                            data = self.bams_bank(his_data_bams, his_datas_bank)
                            return self.returnTypeSuc(data)
                        if name[0] == "Energy storage unit" and name[1] == "Batteries":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db, lang=lang)
                            else:
                                bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                            his_data_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                                startTime=startTime, endTime=endTime,
                                                                jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName,
                                                                 startN=startN, endnN=endnN, st=st, ed=ed,
                                                                 startTime=startTime, endTime=endTime,
                                                                 jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            data = []
                            column = []
                            columnName = []
                            for d in his_data_bams:
                                data1 = d['data']
                                column_1 = d['column']
                                columnName_1 = d['columnName']
                                name1 = d['name']
                                for c in bank:  # 具体簇
                                    c_i = bank.index(c)
                                    for f in his_datas_cell:  # 具体电芯
                                        data2 = f['data']
                                        column_ = f['column']
                                        columnName_ = f['columnName']
                                        name2 = f['name']
                                        if name1 == name2:
                                            if data1:
                                                for e in data1:
                                                    if data2:
                                                        e.update(data2[data1.index(e)])
                                            else:
                                                data1.extend(data2)
                                            columnName_1.extend(columnName_)
                                            column_1.extend(column_)
                                            column = sorted(set(column_1), key=column_1.index)
                                            columnName = sorted(set(columnName_1), key=columnName_1.index)
                                data.append(
                                    {'name': d['name'], 'column': column, 'columnName': columnName, 'data': data1})
                            return self.returnTypeSuc(data)
                        if name[0] == "Battery cluster" and name[1] == "Batteries":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                            bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                            his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                                 bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                                 endTime=endTime,  jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db, lang=lang)
                            his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName,
                                                                 startN=startN, endnN=endnN, st=st, ed=ed,
                                                                 startTime=startTime, endTime=endTime,
                                                                 jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据

                            data = []
                            column = []
                            columnName = []
                            for c in his_datas_bank:
                                data1 = c['data']
                                column_1 = c['column']
                                columnName_1 = c['columnName']
                                name1 = c['name']
                                for f in his_datas_cell:
                                    data2 = f['data']
                                    column_ = f['column']
                                    columnName_ = f['columnName']
                                    name2 = f['name']
                                    if name1 == name2:
                                        if data1:
                                            for e in data1:
                                                if data2:
                                                    e.update(data2[data1.index(e)])
                                        else:
                                            data1.extend(data2)
                                        columnName_1.extend(columnName_)
                                        column_1.extend(column_)
                                        column = sorted(set(column_1), key=column_1.index)
                                        columnName = sorted(set(columnName_1), key=columnName_1.index)
                                data.append(
                                    {'name': c['name'], 'column': column, 'columnName': columnName, 'data': data1})
                            return self.returnTypeSuc(data)
                    else:
                        if name[0] == "储能单元" and name[1] == "电池簇":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                            bank, bankName = self._for_all_bank(bank, bankName, db)
                            his_data_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                                startTime=startTime, endTime=endTime,
                                                                jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                                 bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                                 endTime=endTime,  jiange='5T', dwd_cursor=dwd_cursor)
                            data = self.bams_bank(his_data_bams, his_datas_bank)
                            return self.returnTypeSuc(data)
                        if name[0] == "储能单元" and name[1] == "电芯":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db)
                            else:
                                bank, bankName = self._for_all_bank(bank, bankName, db)
                            his_data_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                                startTime=startTime, endTime=endTime,
                                                                jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName,
                                                                 startN=startN, endnN=endnN, st=st, ed=ed,
                                                                 startTime=startTime, endTime=endTime,
                                                                 jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            data = []
                            column = []
                            columnName = []
                            for d in his_data_bams:
                                data1 = d['data']
                                column_1 = d['column']
                                columnName_1 = d['columnName']
                                name1 = d['name']
                                for c in bank:  # 具体簇
                                    c_i = bank.index(c)
                                    for f in his_datas_cell:  # 具体电芯
                                        data2 = f['data']
                                        column_ = f['column']
                                        columnName_ = f['columnName']
                                        name2 = f['name']
                                        if name1 == name2:
                                            if data1:
                                                for e in data1:
                                                    if data2:
                                                        e.update(data2[data1.index(e)])
                                            else:
                                                data1.extend(data2)
                                            columnName_1.extend(columnName_)
                                            column_1.extend(column_)
                                            column = sorted(set(column_1), key=column_1.index)
                                            columnName = sorted(set(columnName_1), key=columnName_1.index)
                                data.append(
                                    {'name': d['name'], 'column': column, 'columnName': columnName, 'data': data1})
                            return self.returnTypeSuc(data)
                        if name[0] == "电池簇" and name[1] == "电芯":
                            bams, bamsName = self._for_all_bams(bams, bamsName, db)
                            bank, bankName = self._for_all_bank(bank, bankName, db)
                            his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                                 bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                                 endTime=endTime, jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            if db == 'guizhou':
                                bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                                bank, bankName = self._for_all_bank_gz(bank, bankName, db)
                            his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName,
                                                                 startN=startN, endnN=endnN, st=st, ed=ed,
                                                                 startTime=startTime, endTime=endTime,
                                                                 jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                            data = []
                            column = []
                            columnName = []
                            for c in his_datas_bank:
                                data1 = c['data']
                                column_1 = c['column']
                                columnName_1 = c['columnName']
                                name1 = c['name']
                                for f in his_datas_cell:
                                    data2 = f['data']
                                    column_ = f['column']
                                    columnName_ = f['columnName']
                                    name2 = f['name']
                                    if name1 == name2:
                                        if data1:
                                            for e in data1:
                                                if data2:
                                                    e.update(data2[data1.index(e)])
                                        else:
                                            data1.extend(data2)
                                        columnName_1.extend(columnName_)
                                        column_1.extend(column_)
                                        column = sorted(set(column_1), key=column_1.index)
                                        columnName = sorted(set(columnName_1), key=columnName_1.index)
                                data.append(
                                    {'name': c['name'], 'column': column, 'columnName': columnName, 'data': data1})
                            return self.returnTypeSuc(data)
                elif len(name) == 3:
                    if lang == 'en':
                        bams, bamsName = self._for_all_bams(bams, bamsName, db, lang=lang)
                        bank, bankName = self._for_all_bank(bank, bankName, db, lang=lang)
                    else:
                        bams, bamsName = self._for_all_bams(bams, bamsName, db)
                        bank, bankName = self._for_all_bank(bank, bankName, db)
                    his_data_bams = self._his_data_bams(db=db, bams=bams, bamsName=bamsName, st=st, ed=ed,
                                                        startTime=startTime, endTime=endTime,
                                                        jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                    his_datas_bank = self._his_data_bank(db=db, bams=bams, bank=bank, bamsName=bamsName,
                                                         bankName=bankName, st=st, ed=ed, startTime=startTime,
                                                         endTime=endTime,  jiange='5T', dwd_cursor=dwd_cursor)
                    if db == 'guizhou':
                        bank = eval('R'.join(str(bank).split('C')) if str(bank) else '')
                        bank, bankName = self._for_all_bank_gz(bank, bankName, db)
                    his_datas_cell = self._his_data_cell(db=db, bams=bams, bank=bank, bamsName=bamsName,bankName=bankName, startN=startN,
                                                         endnN=endnN, st=st, ed=ed, startTime=startTime,
                                                         endTime=endTime, jiange='5T', dwd_cursor=dwd_cursor)  # 调用获取历史数据
                    data_d_c = self.bams_bank(his_data_bams, his_datas_bank)
                    data = []
                    column = []
                    columnName = []
                    for dc in data_d_c:  # 单元和簇的总共
                        data1 = dc['data']
                        column_1 = dc['column']
                        columnName_1 = dc['columnName']
                        name1 = dc['name']
                        for f in his_datas_cell:  # 具体电芯
                            data2 = f['data']
                            column_ = f['column']
                            columnName_ = f['columnName']
                            name2 = f['name']
                            if name1 == name2:
                                if data1:
                                    for e in data1:
                                        if data2:
                                            e.update(data2[data1.index(e)])
                                else:
                                    data1.extend(data2)
                                columnName_1.extend(columnName_)
                                column_1.extend(column_)
                                column = sorted(set(column_1), key=column_1.index)
                                columnName = sorted(set(columnName_1), key=columnName_1.index)
                        data.append({'name': dc['name'], 'column': column, 'columnName': columnName, 'data': data1})
                    return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            taicang_session1.close()
            binhai_session1.close()
            binhai_session2.close()
            ygzhen_session1.close()
            ygzhen_session2.close()
            zgtian1_bms_session.close()
            zgtian2_bms_session.close()
            zgtian3_bms_session.close()
            zgtian4_bms_session.close()
            for b in baodian_siss.values():
                b.close()
                logging.error(E)
                return self.requestError()
        finally:
            dwd_cursor.close()
            conn.close()
    def _for_all_bank(self, bank, bankName, db, lang=None):
        if 'all' in bank:
            if lang == 'en':
                return self.customError('Select up to 5, please re-select!')
            else:
                return self.customError('最多选择5个，请重新选择！')
        else:
            return bank, bankName


    def _for_all_bank_gz(self, bank, bankName, db, lang=None):
        if 'all' in bank:
            if lang == 'en':
                return self.customError('Select up to 5, please re-select!')
            else:
                return self.customError('最多选择5个，请重新选择！')
        else:
            return bank, bankName

    def _for_all_bams(self, bams, bamsName, db, lang=None):
        if 'all' in bams:
            if lang == 'en':
                return self.customError('Select up to 5, please re-select!')
            else:
                return self.customError('最多选择5个，请重新选择！')
        else:
            return bams, bamsName


    def _his_data_bams(self, db=None, bams=None, bank=None, bamsName=None, bankName=None, st=None, ed=None,
                       startTime=None, endTime=None, jiange=None, dwd_cursor=None):
        '''获取储能单元历史数据'''
        a = timeUtils.getBetweenMonth(startTime, endTime)  # 计算时间范围内的所有年月
        # t = timeUtils.getNowHouse()[:6]
        tables = 'r_measure' + pd.Series(a)
        table = 'ods_r_measure1'
        data1 = []
        timeall = []

        column = ['logtime', '系统电压', '系统电流', 'SOC', '最高单体电压', '最低单体电压', '平均单体电压', '最高单体温度', '最低单体温度',
                  '平均单体温度', '最高单体电压簇号', '最高单体电压箱号', '最高单体电压号', '最低单体电压簇号', '最低单体电压箱号', '最低单体电压号', '最高单体温度簇号',
                  '最高单体温度箱号', '最高单体温度号', '最低单体温度簇号', '最低单体温度箱号', '最低单体温度号', '最大允许充电电流', '最大允许放电电流']
        columnName = ['logtime', 'systemVoltage', 'systemCurrent', 'SOC', 'maxCellVoltage',
                      'minCellVoltage', 'aveCellVoltage',
                      'maxCellTemp', 'minCellTemp', 'aveCellTemp', 'maxVoltageCluster', 'maxVoltageBox',
                      'maxVoltageNum',
                      'minVoltageCluster', 'minVoltageBox', 'minVoltageNum', 'maxCellTempCluster',
                      'maxCellTempBox',
                      'maxCellTempNum', 'minCellTempCluster', 'minCellTempBox', 'minCellTempNum',
                      'maxChargingCurrent', 'maxDischargeCurrent']
        _column = ['logtime']
        _columnName = ['logtime']
        if db == 'ygqn' and 'D区' in bamsName[0]: # 阳泉D区查询历史数据
            db_ = get_dhis('his_data_query')
            db_con = db_[db][1][1]  # 获取阳泉D区数据库连接
            d_device_obj = ['SysVl', 'RkCr', 'RkSoc', 'SgMxVl', 'SgMiVl', 'SgMxTm', 'SgMiTm', 'SgMxVlId', 'SgMiVlId',
                          'SgMxTmId', 'SgMiTmId', 'DgLmtCr', 'DgLmtCr', 'MdMxVlId', 'MdMiVlId', 'MdMxTmId', 'MdMiTmId']
            k = bams[0]
            try:
                # 取出名字对应的带时间和值的键值对
                v0 = _select_get_his_value(db_con, table, k + d_device_obj[0], st, ed, startTime, endTime, jiange, 65535)  # 系统电压
                v1 = _select_get_his_value(db_con, table, k + d_device_obj[1], st, ed, startTime, endTime, jiange, 65535)  # 系统电流
                v2 = _select_get_his_value(db_con, table, k + d_device_obj[2], st, ed, startTime, endTime, jiange, 101)  # SOC

                v3 = _select_get_his_value(db_con, table, k + d_device_obj[3], st, ed, startTime, endTime, jiange)  # 最高单体电压
                v4 = _select_get_his_value(db_con, table, k + d_device_obj[4], st, ed, startTime, endTime, jiange)  # 最低单体电压
                v5 = _select_get_his_value(db_con, table, k + d_device_obj[5], st, ed, startTime, endTime, jiange)  # 最高单体温度
                v6 = _select_get_his_value(db_con, table, k + d_device_obj[6], st, ed, startTime, endTime, jiange)  # 最低单体温度

                v8 = _select_get_his_value(db_con, table, k + d_device_obj[7], st, ed, startTime, endTime, jiange)  # 最高单体电压号

                v10 = _select_get_his_value(db_con, table, k + d_device_obj[8], st, ed, startTime, endTime, jiange)  # 最低单体电压号

                v12 = _select_get_his_value(db_con, table, k + d_device_obj[9], st, ed, startTime, endTime, jiange)  # 最高单体温度号

                v14 = _select_get_his_value(db_con, table, k + d_device_obj[10], st, ed, startTime, endTime, jiange)  # 最低单体温度号
                v15 = _select_get_his_value(db_con, table, k + d_device_obj[11], st, ed, startTime, endTime, jiange)  # 最大允许充电电流
                v16 = _select_get_his_value(db_con, table, k + d_device_obj[12], st, ed, startTime, endTime, jiange)  # 最大允许放电电流
                v17 = _select_get_his_value(db_con, table, k + d_device_obj[13], st, ed, startTime, endTime, jiange)  # 最高单体电压箱号
                v18 = _select_get_his_value(db_con, table, k + d_device_obj[14], st, ed, startTime, endTime, jiange)  # 最低单体电压箱号
                v19 = _select_get_his_value(db_con, table, k + d_device_obj[15], st, ed, startTime, endTime, jiange)  # 最高单体温度箱号
                v20 = _select_get_his_value(db_con, table, k + d_device_obj[16], st, ed, startTime, endTime, jiange)  # 最低单体温度箱号
            except Exception as e:
                logging.error(e)
                db_con.close()
                return data1
            finally:
                db_con.close()
            vv = [v0, v1, v2, v3, v4, v5, v6, v8, v10, v12, v14, v15, v16, v17, v18, v19, v20]
            if not timeall:
                for v in vv:
                    if v:
                        timeall = v['time']
                        break
            b0 = v0['value']
            b1 = v1['value']
            b2 = v2['value']
            b3 = v3['value']
            b4 = v4['value']
            b5 = v5['value']
            b6 = v6['value']
            b17 = v17['value']
            b8 = v8['value']
            b18 = v18['value']
            b10 = v10['value']
            b19 = v19['value']
            b12 = v12['value']
            b20 = v20['value']
            b14 = v14['value']
            b15 = v15['value']
            b16 = v16['value']
            if b3 and b4:
                array_SgV_avg = (np.array(b3) + np.array(b4)) / 2
                aveCellVoltage = np.round(array_SgV_avg, 3).tolist()
            else:
                aveCellVoltage = []
            if b5 and b6:
                array_SgT_avg = (np.array(b5) + np.array(b6)) / 2
                aveCellTemp = np.round(array_SgT_avg, 3).tolist()
            else:
                aveCellTemp = []
            data2 = []
            if timeall:
                for time in timeall:
                    n_i = []
                    inx = timeall.index(time)
                    # cel_list = [b8, b10, b12, b14]
                    # for i in cel_list:
                    #     if i[inx] % 12 == 0:
                    #         n_i.append(divmod(i[inx], 12)[0])  # 包的下标
                    #     elif i[inx] % 12 != 0:
                    #         n_i.append(divmod(i[inx], 12)[0] + 1)
                    # b17, b18, b19, b20 = n_i[0], n_i[1], n_i[2], n_i[3]
                    data2.append({'logtime': time, 'systemVoltage': 0.00 if not b0 else b0[inx],
                                  'systemCurrent': 0.00 if not b1 else b1[inx], 'SOC': 0.00 if not b2 else b2[inx],
                                  'maxCellVoltage': 0.00 if not b3 else b3[inx],
                                  'minCellVoltage': 0.00 if not b4 else b4[inx],
                                  'aveCellVoltage': 0.00 if not aveCellVoltage else aveCellVoltage[inx],
                                  'maxCellTemp': 0.00 if not b5 else b5[inx],
                                  'minCellTemp': 0.00 if not b6 else b6[inx],
                                  'aveCellTemp': 0.00 if not aveCellTemp else aveCellTemp[inx],
                                  'maxVoltageCluster': 1,
                                  'maxVoltageBox': 0.00 if not b17 else b17,
                                  'maxVoltageNum': 0.00 if not b8 else b8[inx],
                                  'minVoltageCluster': 1,
                                  'minVoltageBox': 0.00 if not b18 else b18,
                                  'minVoltageNum': 0.00 if not b10 else b10[inx],
                                  'maxCellTempCluster': 1,
                                  'maxCellTempBox': 0.00 if not b19 else b19,
                                  'maxCellTempNum': 0.00 if not b12 else b12[inx],
                                  'minCellTempCluster': 1,
                                  'minCellTempBox': 0.00 if not b20 else b20,
                                  'minCellTempNum': 0.00 if not b14 else b14[inx],
                                  'maxChargingCurrent': 0.00 if not b15 else b15[inx],
                                  'maxDischargeCurrent': 0.00 if not b16 else b16[inx]})
            else:
                column = []
                columnName = []
            data1.append({'name': bamsName[0], 'column': column, 'columnName': columnName, 'data': data2})
            return data1
        for d in bams:
            d_i = bams.index(d)  # 堆下标
            _d = bamsName[d_i].split('储能单元')
            if db != 'ygqn':
                unit = int(_d[1])
            else:
                if _d[0] == 'A区':
                    unit = int(_d[1])
                elif _d[0] == 'B区':
                    unit = int(_d[1]) + 40
                else:
                    unit = int(_d[1]) + 80
            if db != 'dongmu' and db not in ['tczj', 'sikly']:
                db_con = _return_db_con(db, d)  # 获取具体数据库链接
            data2 = []
            if db == 'dongmu':  # 东睦项目
                timeall_p = []
                b0, b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, b13, b14, b15, b16, b17, b18 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []
                for table in tables:
                    mycol = dongmu_mongo_db[table]
                    values = mycol.find({'time': {'$gte': st, '$lte': ed}})
                    values_1 = {'datainfo': {}}
                    for i in values:
                        values_1['datainfo'] = i['datainfo']
                        value = json.loads(values_1['datainfo'])['body']
                        timeArray = ttt.localtime(i['time'])  # 秒数
                        otherStyleTime = ttt.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        timeall_p.append(otherStyleTime)  # BMS电池SOC
                        for ii in value:
                            if ii['device'][:4] == 'PCS%s' % d[-1]:
                                b0.append(float(ii['DCBV']))  # 系统电压
                                b1.append(float(ii['DCc']))  # 系统电流
                                b2.append(float(ii['BtSOC']))  # SOC
                                b17.append(float(ii['MaxCC']))  # 最大允许充电电流
                                b18.append(float(ii['MaxDC']))  # 最大允许放电电流

                            elif ii['device'][:4] == 'BMS%s' % d[-1]:
                                b3.append(float(ii['SMaxV']))  # 最高单体电压
                                b4.append(float(ii['SMinV']))  # 最低单体电压
                                b5.append(float(ii['AVOSV']))  # 平均单体电压
                                b6.append(float(ii['Smaxt']))  # 最高单体温度
                                b7.append(float(ii['sMint']))  # 最低单体温度
                                b8.append(float(ii['AVOSt']))  # 平均单体温度
                                b9.append(float(ii['MMxVN']))  # 最高单体电压簇号
                                b10.append(float(ii['SMxVN']))  # 最高单体电压号
                                b11.append(float(ii['MMiVN']))  # 最低单体电压簇号
                                b12.append(float(ii['SMinV']))  # 最低单体电压号
                                b13.append(float(ii['MmxtN']))  # 最高单体温度簇号
                                b14.append(float(ii['SMxtN']))  # 最高单体温度号
                                b15.append(float(ii['MmitN']))  # 最低单体温度簇号
                                b16.append(float(ii['sMitN']))  # 最低单体温度号

                if not timeall:
                    timeall = timeall_p

                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        data2.append({'logtime': time, 'systemVoltage': 0.00 if not b0 else b0[inx],
                                      'systemCurrent': 0.00 if not b1 else b1[inx], 'SOC': 0.00 if not b2 else b2[inx],
                                      'maxCellVoltage': 0.00 if not b3 else b3[inx],
                                      'minCellVoltage': 0.00 if not b4 else b4[inx],
                                      'aveCellVoltage': 0.00 if not b5 else b5[inx],
                                      'maxCellTemp': 0.00 if not b6 else b6[inx],
                                      'minCellTemp': 0.00 if not b7 else b7[inx],
                                      'aveCellTemp': 0.00 if not b8 else b8[inx],
                                      'maxVoltageCluster': 0.00 if not b9 else b9[inx],
                                      'maxVoltageBox': 0.00 if not b10 else b10[inx], 'maxVoltageNum': 0.00,
                                      'minVoltageCluster': 0.00 if not b11 else b11[inx],
                                      'minVoltageBox': 0.00 if not b12 else b12[inx], 'minVoltageNum': 0.00,
                                      'maxCellTempCluster': 0.00 if not b13 else b13[inx],
                                      'maxCellTempBox': 0.00 if not b14 else b14[inx], 'maxCellTempNum': 0.00,
                                      'minCellTempCluster': 0.00 if not b15 else b15[inx],
                                      'minCellTempBox': 0.00 if not b16 else b16[inx], 'minCellTempNum': 0.00,
                                      'maxChargingCurrent': 0.00 if not b17 else b17[inx],
                                      'maxDischargeCurrent': 0.00 if not b18 else b18[inx]})
                else:
                    column = []
                    columnName = []
                dongmu_mongodb_client.close()
            elif db == 'baodian' or db == 'guizhou':  # 保电项目
                if db == 'baodian':
                    device = device_obj_bd
                elif db == 'guizhou':
                    device = device_obj_gz
                # value对应的名字
                n0 = '%s%s' % (d, device[0])  # 系统电压
                n1 = '%s%s' % (d, device[1])  # 系统电流
                # n2 = '%s%s' % (d, device_obj[2])  # SOC
                n2 = 'soc'  # SOC
                n3 = 'max_vol'  # 最高单体电压
                n4 = 'min_vol'  # 最低单体电压
                n5 = 'max_temp'  # 最高单体温度
                n6 = 'min_temp'  # 最低单体温度
                n7 = 'max_vol_bc_rk'  # 最高单体电压簇号
                n10 = 'min_vol_bc_rk'  # 最低单体电压号
                n11 = 'max_temp_bc_rk'  # 最高单体温度簇号
                n13 = 'min_temp_bc_rk'  # 最低单体温度簇号
                # n3 = '%s%s' % (d, device_obj[3])  # 最高单体电压
                # n4 = '%s%s' % (d, device_obj[4])  # 最低单体电压
                # n5 = '%s%s' % (d, device_obj[5])  # 最高单体温度
                # n6 = '%s%s' % (d, device_obj[6])  # 最低单体温度
                # n7 = '%s%s' % (d, device_obj[7])  # 最高单体电压簇号
                n8 = '%s%s' % (d, device[8])  # 最高单体电压号
                n9 = '%s%s' % (d, device[9])  # 最低单体电压簇号
                # n10 = '%s%s' % (d, device_obj[10])  # 最低单体电压号
                # n11 = '%s%s' % (d, device_obj[11])  # 最高单体温度簇号
                n12 = '%s%s' % (d, device[12])  # 最高单体温度号
                # n13 = '%s%s' % (d, device_obj[13])  # 最低单体温度簇号
                n14 = '%s%s' % (d, device[14])  # 最低单体温度号
                n15 = '%s%s' % (d, device[15])  # 最大允许充电电流
                n16 = '%s%s' % (d, device[16])  # 最大允许放电电流
                # 取出名字对应的带时间和值的键值对
                v0 = _select_get_his_value(db_con, table, n0, st, ed, startTime, endTime, jiange, 65535)
                v1 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, jiange, 65535)
                # v2 = _select_get_his_value(db_con, table, n2, st, ed, startTime, endTime, jiange, 101)
                # v3 = _select_get_his_value(db_con, table, n3, st, ed, startTime, endTime, jiange)
                # v4 = _select_get_his_value(db_con, table, n4, st, ed, startTime, endTime, jiange)
                # v5 = _select_get_his_value(db_con, table, n5, st, ed, startTime, endTime, jiange)
                # v6 = _select_get_his_value(db_con, table, n6, st, ed, startTime, endTime, jiange)
                # v7 = _select_get_his_value(db_con, table, n7, st, ed, startTime, endTime, jiange)
                v8 = _select_get_his_value(db_con, table, n8, st, ed, startTime, endTime, jiange)
                v9 = _select_get_his_value(db_con, table, n9, st, ed, startTime, endTime, jiange)
                # v10 = _select_get_his_value(db_con, table, n10, st, ed, startTime, endTime, jiange)
                # v11 = _select_get_his_value(db_con, table, n11, st, ed, startTime, endTime, jiange)
                v12 = _select_get_his_value(db_con, table, n12, st, ed, startTime, endTime, jiange)
                # v13 = _select_get_his_value(db_con, table, n13, st, ed, startTime, endTime, jiange)
                v14 = _select_get_his_value(db_con, table, n14, st, ed, startTime, endTime, jiange)
                v15 = _select_get_his_value(db_con, table, n15, st, ed, startTime, endTime, jiange)
                v16 = _select_get_his_value(db_con, table, n16, st, ed, startTime, endTime, jiange)

                v2 = _select_get_dwd_his_value(dwd_cursor, db, unit, n2, st, ed, startTime, endTime, jiange, 101)
                v3 = _select_get_dwd_his_value(dwd_cursor, db, unit, n3, st, ed, startTime, endTime, jiange)
                v4 = _select_get_dwd_his_value(dwd_cursor, db, unit, n4, st, ed, startTime, endTime, jiange)
                v5 = _select_get_dwd_his_value(dwd_cursor, db, unit, n5, st, ed, startTime, endTime, jiange)
                v6 = _select_get_dwd_his_value(dwd_cursor, db, unit, n6, st, ed, startTime, endTime, jiange)
                v7 = _select_get_dwd_his_value(dwd_cursor, db, unit, n7, st, ed, startTime, endTime, jiange)
                v10 = _select_get_dwd_his_value(dwd_cursor, db, unit, n10, st, ed, startTime, endTime, jiange)
                v11 = _select_get_dwd_his_value(dwd_cursor, db, unit, n11, st, ed, startTime, endTime, jiange)
                v13 = _select_get_dwd_his_value(dwd_cursor, db, unit, n13, st, ed, startTime, endTime, jiange)
                vv = [v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12]
                if not timeall:
                    for v in vv:
                        if v:
                            timeall = v['time']
                            break
                b0 = v0['value']
                b1 = v1['value']
                b2 = v2['value']
                b3 = v3['value']
                b4 = v4['value']
                b5 = v5['value']
                b6 = v6['value']
                b7 = v7['value']
                b8 = v8['value']
                b9 = v9['value']
                b10 = v10['value']
                b11 = v11['value']
                b12 = v12['value']
                b13 = v13['value']
                b14 = v14['value']
                b15 = v15['value']
                b16 = v16['value']
                if b3 and b4:
                    array_SgV_avg = (np.array(b3) + np.array(b4)) / 2
                    aveCellVoltage = np.round(array_SgV_avg, 3).tolist()
                else:
                    aveCellVoltage = []
                if b5 and b6:
                    array_SgT_avg = (np.array(b5) + np.array(b6)) / 2
                    aveCellTemp = np.round(array_SgT_avg, 3).tolist()
                else:
                    aveCellTemp = []
                if timeall:
                    for time in timeall:
                        n_i = []
                        inx = timeall.index(time)
                        cel_list = [b8, b10, b12, b14]
                        for i in cel_list:
                            if i[inx] % 12 == 0:
                                n_i.append(divmod(i[inx], 12)[0])  # 包的下标
                            elif i[inx] % 12 != 0:
                                n_i.append(divmod(i[inx], 12)[0] + 1)
                        b17, b18, b19, b20 = n_i[0], n_i[1], n_i[2], n_i[3]
                        data2.append({'logtime': time, 'systemVoltage': 0.00 if not b0 else b0[inx],
                                      'systemCurrent': 0.00 if not b1 else b1[inx], 'SOC': 0.00 if not b2 else b2[inx],
                                      'maxCellVoltage': 0.00 if not b3 else b3[inx],
                                      'minCellVoltage': 0.00 if not b4 else b4[inx],
                                      'aveCellVoltage': 0.00 if not aveCellVoltage else aveCellVoltage[inx],
                                      'maxCellTemp': 0.00 if not b5 else b5[inx],
                                      'minCellTemp': 0.00 if not b6 else b6[inx],
                                      'aveCellTemp': 0.00 if not aveCellTemp else aveCellTemp[inx],
                                      'maxVoltageCluster': 0.00 if not b7 else b7[inx],
                                      'maxVoltageBox': 0.00 if not b17 else b17,
                                      'maxVoltageNum': 0.00 if not b8 else b8[inx],
                                      'minVoltageCluster': 0.00 if not b9 else b9[inx],
                                      'minVoltageBox': 0.00 if not b18 else b18,
                                      'minVoltageNum': 0.00 if not b10 else b10[inx],
                                      'maxCellTempCluster': 0.00 if not b11 else b11[inx],
                                      'maxCellTempBox': 0.00 if not b19 else b19,
                                      'maxCellTempNum': 0.00 if not b12 else b12[inx],
                                      'minCellTempCluster': 0.00 if not b13 else b13[inx],
                                      'minCellTempBox': 0.00 if not b20 else b20,
                                      'minCellTempNum': 0.00 if not b14 else b14[inx],
                                      'maxChargingCurrent': 0.00 if not b15 else b15[inx],
                                      'maxDischargeCurrent': 0.00 if not b16 else b16[inx]})
                else:
                    column = []
                    columnName = []
            elif db not in exclude_station:
                value_dict = {
                    "BMS1": [],
                    "BMS2": [],
                }
                column = column[1:]
                columnName = columnName[1:]
                for bms in range(1, 3):  # BMS
                    _column.extend([c+f'-BMS{bms}' for c in column])
                    _columnName.extend([c+f'-BMS{bms}' for c in columnName])
                    for device in device_obj:
                        if device in dwd_device_obj:
                            if device == 'Soc':
                                value_dict[f'BMS{bms}'].append(_select_get_dwd_his_value(dwd_cursor, db, unit + bms - 1, dwd_device_dict.get(device), st, ed, startTime, endTime, jiange, 101))
                            else:
                                value_dict[f'BMS{bms}'].append(_select_get_dwd_his_value(dwd_cursor, db, unit + bms - 1, dwd_device_dict.get(device), st, ed, startTime, endTime, jiange))
                        else:
                            n = f'{d}BMS{bms}.{device}'

                            if device == 'Vol' or device == 'Cur':
                                value_dict[f'BMS{bms}'].append(_select_get_his_value(db_con, table, n, st, ed, startTime, endTime, jiange, 65535))
                            elif device == 'Soc':
                                value_dict[f'BMS{bms}'].append(_select_get_his_value(db_con, table, n, st, ed, startTime, endTime, jiange, 101))
                            else:
                                value_dict[f'BMS{bms}'].append(_select_get_his_value(db_con, table, n, st, ed, startTime, endTime, jiange))

                if not timeall:
                    for v in value_dict['BMS1']:
                        if v:
                            timeall = v['time']
                            break

                if timeall:
                    for time in timeall:
                        n_i = []
                        inx = timeall.index(time)
                        _dict = {}
                        for k, v in value_dict.items():
                            v_list = []
                            for va in v:
                                v_list.append(va['value']) if va['value'] else v_list.append(0.0)
                            v = v_list
                            if v[3] and v[4]:
                                array_SgV_avg = (np.array(v[3]) + np.array(v[4])) / 2
                                aveCellVoltage = np.round(array_SgV_avg, 3).tolist()
                            else:
                                aveCellVoltage = []
                            if v[5] and v[6]:
                                array_SgT_avg = (np.array(v[5]) + np.array(v[6])) / 2
                                aveCellTemp = np.round(array_SgT_avg, 3).tolist()
                            else:
                                aveCellTemp = []
                            cel_list = [v[8], v[10], v[12], v[14]]
                            for i in cel_list:
                                if i != 0.0:
                                    if i[inx] % 12 == 0:
                                        n_i.append(divmod(i[inx], 12)[0])  # 包的下标
                                    elif i[inx] % 12 != 0:
                                        n_i.append(divmod(i[inx], 12)[0] + 1)
                                else:
                                    n_i.append(0.0)
                            # v[17], v[18], v[19], v[20] = n_i[0], n_i[1], n_i[2], n_i[3]
                            v.extend(n_i)
                            b = k  # 冗余代码

                            _d = {f'logtime': time, f'systemVoltage-{b}': 0.00 if not v[0] else v[0][inx],
                                          f'systemCurrent-{b}': 0.00 if not v[1] else v[1][inx],
                                          f'SOC-{b}': 0.00 if not v[2] else v[2][inx],
                                          f'maxCellVoltage-{b}': 0.00 if not v[3] else v[3][inx],
                                          f'minCellVoltage-{b}': 0.00 if not v[4] else v[4][inx],
                                          f'aveCellVoltage-{b}': 0.00 if not aveCellVoltage else aveCellVoltage[inx],
                                          f'maxCellTemp-{b}': 0.00 if not v[5] else v[5][inx],
                                          f'minCellTemp-{b}': 0.00 if not v[6] else v[6][inx],
                                          f'aveCellTemp-{b}': 0.00 if not aveCellTemp else aveCellTemp[inx],
                                          f'maxVoltageCluster-{b}': 0.00 if not v[7] else v[7][inx],
                                          f'maxVoltageBox-{b}': 0.00 if not v[17] else v[17],
                                          f'maxVoltageNum-{b}': 0.00 if not v[8] else v[8][inx],
                                          f'minVoltageCluster-{b}': 0.00 if not v[9] else v[9][inx],
                                          f'minVoltageBox-{b}': 0.00 if not v[18] else v[18],
                                          f'minVoltageNum-{b}': 0.00 if not v[10] else v[10][inx],
                                          f'maxCellTempCluster-{b}': 0.00 if not v[11] else v[11][inx],
                                          f'maxCellTempBox-{b}': 0.00 if not v[19] else v[19],
                                          f'maxCellTempNum-{b}': 0.00 if not v[12] else v[12][inx],
                                          f'minCellTempCluster-{b}': 0.00 if not v[13] else v[13][inx],
                                          f'minCellTempBox-{b}': 0.00 if not v[20] else v[20],
                                          f'minCellTempNum-{b}': 0.00 if not v[14] else v[14][inx],
                                          f'maxChargingCurrent-{b}': 0.00 if not v[15] else v[15][inx],
                                          f'maxDischargeCurrent-{b}': 0.00 if not v[16] else v[16][inx]}
                            for _k, _v in _d.items():
                                _dict[_k] = _v

                        data2.append(_dict)

                else:
                    column = []
                    columnName = []
                column = _column
                columnName = _columnName

            else:
                if db in ['tczj', 'sikly']:
                    value_dict = {}
                    for k, device in bj_dwd_device_obj.items():
                        if device != None:
                            if k == 'SOC':
                                value_dict[k] = _select_get_dwd_his_value(dwd_cursor, db, unit, device, st, ed, startTime, endTime, jiange, 101)
                            else:
                                value_dict[k] = _select_get_dwd_his_value(dwd_cursor, db, unit, device, st, ed, startTime, endTime, jiange)
                        else:
                            value_dict[k] = {'time': [], 'value': []}
                    if not timeall:
                        for v in value_dict.values():
                            if v:
                                timeall = v['time']
                                break
                    if value_dict['maxCellVoltage']['value'] and value_dict['minCellVoltage']['value']:  # 计算电压均值
                        array_SgV_avg = (np.array(value_dict['maxCellVoltage']['value']) + np.array(
                            value_dict['minCellVoltage']['value'])) / 2
                        value_dict['aveCellVoltage']['value'] = np.round(array_SgV_avg, 3).tolist()
                    if value_dict['maxCellTemp']['value'] and value_dict['minCellTemp']['value']:  # 计算温度均值
                        array_SgT_avg = (np.array(value_dict['maxCellTemp']['value']) + np.array(
                            value_dict['minCellTemp']['value'])) / 2
                        value_dict['aveCellTemp']['value'] = np.round(array_SgT_avg, 3).tolist()
                    b0, b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, b13, b14, b15, b16, b17, b18, b19, b20, b21, b22 = [
                        value_dict['systemVoltage']['value'], value_dict['systemCurrent']['value'],
                        value_dict['SOC']['value'], value_dict['maxCellVoltage']['value'],
                        value_dict['minCellVoltage']['value'], value_dict['aveCellVoltage']['value'],
                        value_dict['maxCellTemp']['value'], value_dict['minCellTemp']['value'],
                        value_dict['aveCellTemp']['value'], value_dict['maxVoltageCluster']['value'],
                        value_dict['maxVoltageBox']['value'], value_dict['maxVoltageNum']['value'],
                        value_dict['minVoltageCluster']['value'], value_dict['minVoltageBox']['value'],
                        value_dict['minVoltageNum']['value'], value_dict['maxCellTempCluster']['value'],
                        value_dict['maxCellTempBox']['value'], value_dict['maxCellTempNum']['value'],
                        value_dict['minCellTempCluster']['value'], value_dict['minCellTempBox']['value'],
                        value_dict['minCellTempNum']['value'], value_dict['maxChargingCurrent']['value'],
                        value_dict['maxDischargeCurrent']['value']]

                    if timeall:
                        for time in timeall:
                            n_i = [b11, b14, b17, b20]
                            inx = timeall.index(time)
                            cel_list = []
                            for i in cel_list:
                                if i[inx] % 12 == 0:
                                    n_i.append(divmod(i[inx], 12)[0])  # 包的下标
                                elif i[inx] % 12 != 0:
                                    n_i.append(divmod(i[inx], 12)[0] + 1)
                            b10, b13, b16, b19 = n_i[0], n_i[1], n_i[2], n_i[3]
                            data2.append({'logtime': time, 'systemVoltage': 0.00 if not b0 else b0[inx],
                                          'systemCurrent': 0.00 if not b1 else b1[inx],
                                          'SOC': 0.00 if not b2 else b2[inx],
                                          'maxCellVoltage': 0.00 if not b3 else b3[inx],
                                          'minCellVoltage': 0.00 if not b4 else b4[inx],
                                          'aveCellVoltage': 0.00 if not b5 else b5[inx],
                                          'maxCellTemp': 0.00 if not b6 else b6[inx],
                                          'minCellTemp': 0.00 if not b7 else b7[inx],
                                          'aveCellTemp': 0.00 if not b8 else b8[inx],
                                          'maxVoltageCluster': 0.00 if not b9 else b9[inx],
                                          'maxVoltageBox': 0.00 if not b10 else b10[inx],
                                          'maxVoltageNum': 0.00 if not b11 else b11[inx],
                                          'minVoltageCluster': 0.00 if not b12 else b12[inx],
                                          'minVoltageBox': 0.00 if not b13 else b13[inx],
                                          'minVoltageNum': 0.00 if not b14 else b14[inx],
                                          'maxCellTempCluster': 0.00 if not b15 else b15[inx],
                                          'maxCellTempBox': 0.00 if not b16 else b16[inx],
                                          'maxCellTempNum': 0.00 if not b17 else b17[inx],
                                          'minCellTempCluster': 0.00 if not b18 else b18[inx],
                                          'minCellTempBox': 0.00 if not b19 else b19[inx],
                                          'minCellTempNum': 0.00 if not b20 else b20[inx],
                                          'maxChargingCurrent': 0.00 if not b21 else b21[inx],
                                          'maxDischargeCurrent': 0.00 if not b22 else b22[inx]})
                    else:
                        column = []
                        columnName = []
                else:
                    # n0 = '%s%s' % (d, device_obj[0])  # 系统电压
                    # n1 = '%s%s' % (d, device_obj[1])  # 系统电流
                    n0 = 'vol'  # 系统电压
                    n1 = 'cur'  # 系统电流
                    # n2 = '%s%s' % (d, device_obj[2])  # SOC
                    n2 = 'soc'  # SOC
                    n3 = 'max_vol'  # 最高单体电压
                    n4 = 'min_vol'  # 最低单体电压
                    n5 = 'max_temp'   # 最高单体温度
                    n6 = 'min_temp'   # 最低单体温度
                    n7 = 'max_vol_bc_rk'   # 最高单体电压簇号
                    n10 = 'min_vol_cell_rk'   # 最低单体电压号
                    n11 = 'max_temp_bc_rk'   # 最高单体温度簇号
                    n13 = 'min_temp_bc_rk'   # 最低单体温度簇号
                    n8 = 'max_vol_cell_rk'   # 最高单体电压号
                    n9 = 'min_vol_bc_rk'   # 最低单体电压簇号
                    n12 = 'max_temp_cell_rk'   # 最高单体温度号
                    n14 = 'min_temp_cell_rk'   # 最低单体温度号

                    # n3 = '%s%s' % (d, device_obj[3])  # 最高单体电压
                    # n4 = '%s%s' % (d, device_obj[4])  # 最低单体电压
                    # n5 = '%s%s' % (d, device_obj[5])  # 最高单体温度
                    # n6 = '%s%s' % (d, device_obj[6])  # 最低单体温度
                    # n7 = '%s%s' % (d, device_obj[7])  # 最高单体电压簇号
                    # n8 = '%s%s' % (d, device_obj[8])  # 最高单体电压号
                    # n9 = '%s%s' % (d, device_obj[10])  # 最低单体电压簇号
                    # n10 = '%s%s' % (d, device_obj[10])  # 最低单体电压号
                    # n11 = '%s%s' % (d, device_obj[11])  # 最高单体温度簇号
                    # n12 = '%s%s' % (d, device_obj[12])  # 最高单体温度号
                    # n13 = '%s%s' % (d, device_obj[13])  # 最低单体温度簇号
                    # n14 = '%s%s' % (d, device_obj[14])  # 最低单体温度号
                    n15 = '%s%s' % (d, device_obj[15])  # 最大允许充电电流
                    n16 = '%s%s' % (d, device_obj[16])  # 最大允许放电电流
                    # 取出名字对应的带时间和值的键值对
                    # v0 = _select_get_his_value(db_con, table, n0, st, ed, startTime, endTime, jiange, 65535)
                    # v1 = _select_get_his_value(db_con, table, n1, st, ed, startTime, endTime, jiange, 65535)
                    # v2 = _select_get_his_value(db_con, table, n2, st, ed, startTime, endTime, jiange, 101)
                    # v3 = _select_get_his_value(db_con, table, n3, st, ed, startTime, endTime, jiange)
                    # v4 = _select_get_his_value(db_con, table, n4, st, ed, startTime, endTime, jiange)
                    # v5 = _select_get_his_value(db_con, table, n5, st, ed, startTime, endTime, jiange)
                    # v6 = _select_get_his_value(db_con, table, n6, st, ed, startTime, endTime, jiange)
                    # v7 = _select_get_his_value(db_con, table, n7, st, ed, startTime, endTime, jiange)
                    # v8 = _select_get_his_value(db_con, table, n8, st, ed, startTime, endTime, jiange)
                    # v9 = _select_get_his_value(db_con, table, n9, st, ed, startTime, endTime, jiange)
                    # v10 = _select_get_his_value(db_con, table, n10, st, ed, startTime, endTime, jiange)
                    # v11 = _select_get_his_value(db_con, table, n11, st, ed, startTime, endTime, jiange)
                    # v12 = _select_get_his_value(db_con, table, n12, st, ed, startTime, endTime, jiange)
                    # v13 = _select_get_his_value(db_con, table, n13, st, ed, startTime, endTime, jiange)
                    # v14 = _select_get_his_value(db_con, table, n14, st, ed, startTime, endTime, jiange)
                    v15 = _select_get_his_value(db_con, table, n15, st, ed, startTime, endTime, jiange)
                    v16 = _select_get_his_value(db_con, table, n16, st, ed, startTime, endTime, jiange)



                    v0 = _select_get_dwd_his_value(dwd_cursor, db, unit, n0, st, ed, startTime, endTime, jiange, 65535)
                    v1 = _select_get_dwd_his_value(dwd_cursor, db, unit, n1, st, ed, startTime, endTime, jiange, 65535)
                    v2 = _select_get_dwd_his_value(dwd_cursor, db, unit, n2, st, ed, startTime, endTime, jiange, 101)
                    v3 = _select_get_dwd_his_value(dwd_cursor, db, unit, n3, st, ed, startTime, endTime, jiange)
                    v4 = _select_get_dwd_his_value(dwd_cursor, db, unit, n4, st, ed, startTime, endTime, jiange)
                    v5 = _select_get_dwd_his_value(dwd_cursor, db, unit, n5, st, ed, startTime, endTime, jiange)
                    v6 = _select_get_dwd_his_value(dwd_cursor, db, unit, n6, st, ed, startTime, endTime, jiange)
                    v7 = _select_get_dwd_his_value(dwd_cursor, db, unit, n7, st, ed, startTime, endTime, jiange)
                    v8 = _select_get_dwd_his_value(dwd_cursor, db, unit, n8, st, ed, startTime, endTime, jiange)
                    v9 = _select_get_dwd_his_value(dwd_cursor, db, unit, n9, st, ed, startTime, endTime, jiange)
                    v10 = _select_get_dwd_his_value(dwd_cursor, db, unit, n10, st, ed, startTime, endTime, jiange)
                    v11 = _select_get_dwd_his_value(dwd_cursor, db, unit, n11, st, ed, startTime, endTime, jiange)
                    v12 = _select_get_dwd_his_value(dwd_cursor, db, unit, n12, st, ed, startTime, endTime, jiange)
                    v13 = _select_get_dwd_his_value(dwd_cursor, db, unit, n13, st, ed, startTime, endTime, jiange)
                    v14 = _select_get_dwd_his_value(dwd_cursor, db, unit, n14, st, ed, startTime, endTime, jiange)
                    vv = [v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12]
                    if not timeall:
                        for v in vv:
                            if v:
                                timeall = v['time']
                                break
                    b0 = v0['value']
                    b1 = v1['value']
                    b2 = v2['value']
                    b3 = v3['value']
                    b4 = v4['value']
                    b5 = v5['value']
                    b6 = v6['value']
                    b7 = v7['value']
                    b8 = v8['value']
                    b9 = v9['value']
                    b10 = v10['value']
                    b11 = v11['value']
                    b12 = v12['value']
                    b13 = v13['value']
                    b14 = v14['value']
                    b15 = v15['value']
                    b16 = v16['value']
                    if b3 and b4:
                        array_SgV_avg = (np.array(b3) + np.array(b4)) / 2
                        aveCellVoltage = np.round(array_SgV_avg, 3).tolist()
                    else:
                        aveCellVoltage = []
                    if b5 and b6:
                        array_SgT_avg = (np.array(b5) + np.array(b6)) / 2
                        aveCellTemp = np.round(array_SgT_avg, 3).tolist()
                    else:
                        aveCellTemp = []
                    if timeall:
                        for time in timeall:
                            n_i = []
                            inx = timeall.index(time)
                            cel_list = [b8, b10, b12, b14]
                            for i in cel_list:
                                if i[inx] % 12 == 0:
                                    n_i.append(divmod(i[inx], 12)[0])  # 包的下标
                                elif i[inx] % 12 != 0:
                                    n_i.append(divmod(i[inx], 12)[0] + 1)
                            b17, b18, b19, b20 = n_i[0], n_i[1], n_i[2], n_i[3]
                            data2.append({'logtime': time, 'systemVoltage': 0.00 if not b0 else b0[inx],
                                          'systemCurrent': 0.00 if not b1 else b1[inx], 'SOC': 0.00 if not b2 else b2[inx],
                                          'maxCellVoltage': 0.00 if not b3 else b3[inx],
                                          'minCellVoltage': 0.00 if not b4 else b4[inx],
                                          'aveCellVoltage': 0.00 if not aveCellVoltage else aveCellVoltage[inx],
                                          'maxCellTemp': 0.00 if not b5 else b5[inx],
                                          'minCellTemp': 0.00 if not b6 else b6[inx],
                                          'aveCellTemp': 0.00 if not aveCellTemp else aveCellTemp[inx],
                                          'maxVoltageCluster': 0.00 if not b7 else b7[inx],
                                          'maxVoltageBox': 0.00 if not b17 else b17,
                                          'maxVoltageNum': 0.00 if not b8 else b8[inx],
                                          'minVoltageCluster': 0.00 if not b9 else b9[inx],
                                          'minVoltageBox': 0.00 if not b18 else b18,
                                          'minVoltageNum': 0.00 if not b10 else b10[inx],
                                          'maxCellTempCluster': 0.00 if not b11 else b11[inx],
                                          'maxCellTempBox': 0.00 if not b19 else b19,
                                          'maxCellTempNum': 0.00 if not b12 else b12[inx],
                                          'minCellTempCluster': 0.00 if not b13 else b13[inx],
                                          'minCellTempBox': 0.00 if not b20 else b20,
                                          'minCellTempNum': 0.00 if not b14 else b14[inx],
                                          'maxChargingCurrent': 0.00 if not b15 else b15[inx],
                                          'maxDischargeCurrent': 0.00 if not b16 else b16[inx]})
                    else:
                        column = []
                        columnName = []
            data1.append({'name': bamsName[d_i], 'column': column, 'columnName': columnName, 'data': data2})
        return data1

    def _his_data_bank(self, db=None, bams=None, bank=None, bamsName=None, bankName=None, st=None, ed=None,
                       startTime=None, endTime=None, jiange=None, dwd_cursor=None):
        '''获取电池簇历史数据'''
        a = timeUtils.getBetweenMonth(startTime, endTime)  # 计算时间范围内的所有年月
        tables = 'r_measure' + pd.Series(a)
        table = 'ods_r_measure1'
        data1 = []
        if db == 'ygqn' and 'D区' in bamsName[0]:  # 阳泉D区查询历史数据
            if bankName[0] == '簇1':
                db_ = get_dhis('his_data_query')
                db_con = db_[db][1][1]  # 获取阳泉D区数据库连接
                k = bams[0]
                c = 1 # 只有一个簇
                columnName = []
                column = []
                data2 = []

                timeall = []
                name_0, name_1, name_2, name_3, name_4, name_5, name_6, name_7, name_8, name_9, = [], [], [], [], [], [], [], [], [], []
                b0, b1, b2, b3, b4, b5, b6, b7, b8, b9 = [], [], [], [], [], [], [], [], [], []
                try:
                    v0 = _select_get_his_value(db_con, table, k + 'SgMxVl', st, ed, startTime, endTime, jiange,
                                               65535)  # 最高单体电压
                    v1 = _select_get_his_value(db_con, table, k + 'SgMiVl', st, ed, startTime, endTime, jiange,
                                               65535)  # 最低单体电压
                    v2 = _select_get_his_value(db_con, table, k + 'SgVlAvg', st, ed, startTime, endTime, jiange,
                                               101)  # 平均电压

                    v3 = _select_get_his_value(db_con, table, k + 'SgMxTm', st, ed, startTime, endTime,
                                               jiange)  # 最高单体温度
                    v4 = _select_get_his_value(db_con, table, k + 'SgMiTm', st, ed, startTime, endTime,
                                               jiange)  # 最低单体温度
                    v5 = _select_get_his_value(db_con, table, k + 'SgTmAvg', st, ed, startTime, endTime,
                                               jiange)  # 平均温度
                    v6 = _select_get_his_value(db_con, table, k + 'SgMxVlId', st, ed, startTime, endTime,
                                               jiange)  # 最高单体电压号
                    v7 = _select_get_his_value(db_con, table, k + 'SgMiVlId', st, ed, startTime, endTime,
                                               jiange)  # 最低单体电压号
                    v8 = _select_get_his_value(db_con, table, k + 'SgMxTmId', st, ed, startTime, endTime,
                                               jiange)  # 最高单体温度号
                    v9 = _select_get_his_value(db_con, table, k + 'SgMiTmId', st, ed, startTime, endTime,
                                               jiange)  # 最低单体温度号
                except Exception as e:
                    logging.error(e)
                    db_con.close()
                    return data1
                finally:
                    db_con.close()
                vv = [v0, v1, v2, v3, v4, v5, v6, v7, v8, v9]
                if not timeall:
                    for v in vv:
                        if v:
                            timeall = v['time']
                            break
                b0.append(v0['value'])
                b1.append(v1['value'])
                b2.append(v2['value'])
                b3.append(v3['value'])
                b4.append(v4['value'])
                b5.append(v5['value'])
                b6.append(v6['value'])
                b7.append(v7['value'])
                b8.append(v8['value'])
                b9.append(v9['value'])

                name_0.append('cluster%dMaxVoltage' % (c))
                name_1.append('cluster%dMinVoltage' % (c))
                name_2.append('cluster%dAveVoltage' % (c))
                name_3.append('cluster%dMaxTemp' % (c))
                name_4.append('cluster%dMinTemp' % (c))
                name_5.append('cluster%dAveTemp' % (c))
                name_6.append('cluster%dMaxVoltageNum' % (c))
                name_7.append('cluster%dMinVoltageNum' % (c))
                name_8.append('cluster%dMaxTempNum' % (c))
                name_9.append('cluster%dMinTempNum' % (c))
                self._cu_table_list_(c, column, columnName)
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1), len(b2), len(b3), len(b4), len(b5), len(b6), len(b7), len(b8),
                                len(b9))
                        for i in range(b):
                            o[name_0[i]] = 0.00 if not b0[i] else b0[i][inx]
                            o[name_1[i]] = 0.00 if not b1[i] else b1[i][inx]
                            o[name_2[i]] = 0.00 if not b2[i] else b2[i][inx]
                            o[name_3[i]] = 0.00 if not b3[i] else b3[i][inx]
                            o[name_4[i]] = 0.00 if not b4[i] else b4[i][inx]
                            o[name_5[i]] = 0.00 if not b5[i] else b5[i][inx]
                            o[name_6[i]] = 0.00 if not b6[i] else b6[i][inx]
                            o[name_7[i]] = 0.00 if not b7[i] else b7[i][inx]
                            o[name_8[i]] = 0.00 if not b8[i] else b8[i][inx]
                            o[name_9[i]] = 0.00 if not b9[i] else b9[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
                data1.append({'name': bamsName[0], 'column': column, 'columnName': columnName, 'data': data2})

            return data1

        for d in bams:
            d_i = bams.index(d)  # 堆下标
            _d = bamsName[d_i].split('储能单元')
            if db != 'ygqn':
                unit = int(_d[1])
            else:
                if _d[0] == 'A区':
                    unit = int(_d[1])
                elif _d[0] == 'B区':
                    unit = int(_d[1]) + 40
                else:
                    unit = int(_d[1]) + 80
            # if db != 'dongmu':
            #     db_con = _return_db_con(db, d)  # 获取具体数据库链接
            columnName = []
            column = []
            data2 = []
            name_0, name_1, name_2, name_3, name_4, name_5, name_6, name_7, name_8, name_9, = [], [], [], [], [], [], [], [], [], []
            b0, b1, b2, b3, b4, b5, b6, b7, b8, b9 = [], [], [], [], [], [], [], [], [], []
            timeall = []
            if db == 'dongmu':  # 东睦项目
                for c in bank:  # 具体簇
                    c_i = int(c[-1])  # 第几簇
                    b10, b11, b12, b13, b14, b15, b16, b17, b18, b19 = [], [], [], [], [], [], [], [], [], []
                    for table in tables:
                        mycol = dongmu_mongo_db[table]
                        values = mycol.find({'time': {'$gte': st, '$lte': ed}})
                        values_1 = {'datainfo': {}}
                        for i in values:
                            values_1['datainfo'] = i['datainfo']
                            value = json.loads(values_1['datainfo'])['body']
                            timeArray = ttt.localtime(i['time'])  # 秒数
                            otherStyleTime = ttt.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                            timeall.append(otherStyleTime)  # BMS电池SOC
                            for ii in value:
                                if ii['device'][:4] == 'BMS%s' % c[-1]:
                                    b10.append(ii['MMaxV'])  # 最高电压
                                    b11.append(ii['MMinV'])  # 最低电压
                                    b12.append(ii['AVOSV'])  # 平均电压
                                    b13.append(ii['Mmaxt'])  # 最高温度
                                    b14.append(ii['Mmint'])  # 最低温度
                                    b15.append(ii['AVOSt'])  # 平均温度
                                    b16.append(ii['MMxVN'])  # 最高电压号
                                    b17.append(ii['MMiVN'])  # 最低电压号
                                    b18.append(ii['MmxtN'])  # 最高温度号
                                    b19.append(ii['MmitN'])  # 最低温度号
                    b0.append(b10)  # 最高电压
                    b1.append(b11)  # 最低电压
                    b2.append(b12)  # 平均电压
                    b3.append(b13)  # 最高温度
                    b4.append(b14)  # 最低温度
                    b5.append(b15)  # 平均温度
                    b6.append(b16)  # 最高电压号
                    b7.append(b17)  # 最低电压号
                    b8.append(b18)  # 最高温度号
                    b9.append(b19)  # 最低温度号

                    name_0.append('cluster%dMaxVoltage' % (c_i))
                    name_1.append('cluster%dMinVoltage' % (c_i))
                    name_2.append('cluster%dAveVoltage' % (c_i))
                    name_3.append('cluster%dMaxTemp' % (c_i))
                    name_4.append('cluster%dMinTemp' % (c_i))
                    name_5.append('cluster%dAveTemp' % (c_i))
                    name_6.append('cluster%dMaxVoltageNum' % (c_i))
                    name_7.append('cluster%dMinVoltageNum' % (c_i))
                    name_8.append('cluster%dMaxTempNum' % (c_i))
                    name_9.append('cluster%dMinTempNum' % (c_i))
                    self._cu_table_list_(c_i, column, columnName)

                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1), len(b2), len(b3), len(b4), len(b5), len(b6), len(b7), len(b8),
                                len(b9))
                        for i in range(b):
                            o[name_0[i]] = 0.00 if not b0[i] else b0[i][inx]
                            o[name_1[i]] = 0.00 if not b1[i] else b1[i][inx]
                            o[name_2[i]] = 0.00 if not b2[i] else b2[i][inx]
                            o[name_3[i]] = 0.00 if not b3[i] else b3[i][inx]
                            o[name_4[i]] = 0.00 if not b4[i] else b4[i][inx]
                            o[name_5[i]] = 0.00 if not b5[i] else b5[i][inx]
                            o[name_6[i]] = 0.00 if not b6[i] else b6[i][inx]
                            o[name_7[i]] = 0.00 if not b7[i] else b7[i][inx]
                            o[name_8[i]] = 0.00 if not b8[i] else b8[i][inx]
                            o[name_9[i]] = 0.00 if not b9[i] else b9[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
                dongmu_mongodb_client.close()
            elif db == 'baodian' or db == 'guizhou':  # 保电项目
                for c in bankName:  # 具体簇
                    c = int(c.split('簇')[1])
                    c_i = station_unit_cluster_dict.get(db)[unit][c - 1]  # 第几簇
                    v0 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v1 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v2 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v3 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v4 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v5 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v6 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v7 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v8 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v9 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    vv = [v0, v1, v2, v3, v4, v5, v6, v7, v8, v9]
                    if not timeall:
                        for v in vv:
                            if v:
                                timeall = v['time']
                                break
                    b0.append(v0['value'])
                    b1.append(v1['value'])
                    b2.append(v2['value'])
                    b3.append(v3['value'])
                    b4.append(v4['value'])
                    b5.append(v5['value'])
                    b6.append(v6['value'])
                    b7.append(v7['value'])
                    b8.append(v8['value'])
                    b9.append(v9['value'])
                    name_0.append('cluster%dMaxVoltage' % (c))
                    name_1.append('cluster%dMinVoltage' % (c))
                    name_2.append('cluster%dAveVoltage' % (c))
                    name_3.append('cluster%dMaxTemp' % (c))
                    name_4.append('cluster%dMinTemp' % (c))
                    name_5.append('cluster%dAveTemp' % (c))
                    name_6.append('cluster%dMaxVoltageNum' % (c))
                    name_7.append('cluster%dMinVoltageNum' % (c))
                    name_8.append('cluster%dMaxTempNum' % (c))
                    name_9.append('cluster%dMinTempNum' % (c))
                    self._cu_table_list_(c, column, columnName)
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1), len(b2), len(b3), len(b4), len(b5), len(b6), len(b7), len(b8),
                                len(b9))
                        for i in range(b):
                            o[name_0[i]] = 0.00 if not b0[i] else b0[i][inx]
                            o[name_1[i]] = 0.00 if not b1[i] else b1[i][inx]
                            o[name_2[i]] = 0.00 if not b2[i] else b2[i][inx]
                            o[name_3[i]] = 0.00 if not b3[i] else b3[i][inx]
                            o[name_4[i]] = 0.00 if not b4[i] else b4[i][inx]
                            o[name_5[i]] = 0.00 if not b5[i] else b5[i][inx]
                            o[name_6[i]] = 0.00 if not b6[i] else b6[i][inx]
                            o[name_7[i]] = 0.00 if not b7[i] else b7[i][inx]
                            o[name_8[i]] = 0.00 if not b8[i] else b8[i][inx]
                            o[name_9[i]] = 0.00 if not b9[i] else b9[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
            elif db not in exclude_station:
                cluster_count = 0  # 太仓鑫融有两台BMS，在数据表中对应两个储能单元；簇和单元编号不与页面对应要单独处理
                value_dict = {
                    "BMS1": {
                        'value':[],
                        'name':[]

                    },
                    "BMS2": {
                        'value': [],
                        'name': []
                    },
                }
                for bms in range(1, 3):  # BMS
                    unit = unit + bms - 1
                    if bms == 2:
                        cluster_count = len(station_unit_cluster_dict.get(db)[unit - 1])
                    for c in bank:  # 具体簇
                        _c_i = int(c[-2])
                        c_i = int(c[-2]) + cluster_count  # 第几簇
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol', st, ed, startTime, endTime,
                                                       jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol', st, ed, startTime, endTime,
                                                       jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_vol', st, ed, startTime, endTime,
                                                       jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_temp', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol_rk', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol_rk', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp_rk', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['value'].append(_select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp_rk', st, ed, startTime, endTime,
                                                       jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk'))
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MaxVoltage-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MinVoltage-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}AveVoltage-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MaxTemp-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MinTemp-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}AveTemp-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MaxVoltageNum-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MinVoltageNum-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MaxTempNum-BMS{bms}')
                        value_dict[f'BMS{bms}']['name'].append(f'cluster{_c_i}MinTempNum-BMS{bms}')
                        self._cu_table_list_new(_c_i, column, columnName, bms)
                if not timeall:
                    for v in value_dict['BMS1']['value']:
                        if v:
                            timeall = v['time']
                            break
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        _dict = {}
                        for k, v in value_dict.items():
                            v_list = []
                            for va in v['value']:
                                v_list.append(va['value'])
                            b = max(len(v_list[0]), len(v_list[1]), len(v_list[2]), len(v_list[3]), len(v_list[4]), len(v_list[5]), len(v_list[6]), len(v_list[7]), len(v_list[8]),
                                len(v_list[9]))
                            for i in range(len(v_list)):
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]
                                o[v['name'][i]] = 0.00 if not v_list[i] else v_list[i][inx]

                        # for k1, v1 in o.items():
                        #     _dict[k1] = v1

                        data2.append(o)
                else:
                    column = []
                    columnName = []

            else:
                for c in bankName:  # 具体簇
                    c = int(c.split('簇')[1])
                    c_i = station_unit_cluster_dict.get(db)[unit][c - 1] # 第几簇
                    # value对应的名字
                    # 取出名字对应的带时间和值的键值对
                    v0 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v1 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v2 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_vol', st, ed, startTime, endTime, jiange, 65, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v3 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v4 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v5 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'avg_temp', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v6 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_vol_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v7 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_vol_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v8 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'max_temp_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    v9 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'min_temp_rk', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')
                    vv = [v0, v1, v2, v3, v4, v5, v6, v7, v8, v9]
                    if not timeall:
                        for v in vv:
                            if v:
                                timeall = v['time']
                                break
                    b0.append(v0['value'])
                    b1.append(v1['value'])
                    b2.append(v2['value'])
                    b3.append(v3['value'])
                    b4.append(v4['value'])
                    b5.append(v5['value'])
                    b6.append(v6['value'])
                    b7.append(v7['value'])
                    b8.append(v8['value'])
                    b9.append(v9['value'])

                    name_0.append('cluster%dMaxVoltage' % (c))
                    name_1.append('cluster%dMinVoltage' % (c))
                    name_2.append('cluster%dAveVoltage' % (c))
                    name_3.append('cluster%dMaxTemp' % (c))
                    name_4.append('cluster%dMinTemp' % (c))
                    name_5.append('cluster%dAveTemp' % (c))
                    name_6.append('cluster%dMaxVoltageNum' % (c))
                    name_7.append('cluster%dMinVoltageNum' % (c))
                    name_8.append('cluster%dMaxTempNum' % (c))
                    name_9.append('cluster%dMinTempNum' % (c))
                    self._cu_table_list_(c, column, columnName)
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1), len(b2), len(b3), len(b4), len(b5), len(b6), len(b7), len(b8),
                                len(b9))
                        for i in range(b):
                            o[name_0[i]] = 0.00 if not b0[i] else b0[i][inx]
                            o[name_1[i]] = 0.00 if not b1[i] else b1[i][inx]
                            o[name_2[i]] = 0.00 if not b2[i] else b2[i][inx]
                            o[name_3[i]] = 0.00 if not b3[i] else b3[i][inx]
                            o[name_4[i]] = 0.00 if not b4[i] else b4[i][inx]
                            o[name_5[i]] = 0.00 if not b5[i] else b5[i][inx]
                            o[name_6[i]] = 0.00 if not b6[i] else b6[i][inx]
                            o[name_7[i]] = 0.00 if not b7[i] else b7[i][inx]
                            o[name_8[i]] = 0.00 if not b8[i] else b8[i][inx]
                            o[name_9[i]] = 0.00 if not b9[i] else b9[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
            data1.append({'name': bamsName[d_i], 'column': column, 'columnName': columnName, 'data': data2})
        return data1

    def _cu_table_list_(self, c_i, column, columnName):
        if columnName == []:
            columnName.append('logtime')
        if column == []:
            column.append('logtime')
        col = ['簇%d最高电压' % (c_i), '簇%d最低电压' % (c_i), '簇%d平均电压' % (c_i),
               '簇%d最高温度' % (c_i), '簇%d最低温度' % (c_i), '簇%d平均温度' % (c_i),
               '簇%d最高电压号' % (c_i), '簇%d最低电压号' % (c_i), '簇%d最高温度号' % (c_i),
               '簇%d最低温度号' % (c_i)]
        colName = ['cluster%dMaxVoltage' % (c_i), 'cluster%dMinVoltage' % (c_i),
                   'cluster%dAveVoltage' % (c_i),
                   'cluster%dMaxTemp' % (c_i), 'cluster%dMinTemp' % (c_i),
                   'cluster%dAveTemp' % (c_i), 'cluster%dMaxVoltageNum' % (c_i),
                   'cluster%dMinVoltageNum' % (c_i), 'cluster%dMaxTempNum' % (c_i),
                   'cluster%dMinTempNum' % (c_i)]
        columnName.extend(colName)
        column.extend(col)

    def _cu_table_list_new(self, c_i, column, columnName, bms):
        if columnName == []:
            columnName.append('logtime')
        if column == []:
            column.append('logtime')
        col = ['簇%d最高电压%s' % (c_i, f'-BMS{bms}'), '簇%d最低电压%s' % (c_i, f'-BMS{bms}'), '簇%d平均电压%s' % (c_i, f'-BMS{bms}'),
               '簇%d最高温度%s' % (c_i, f'-BMS{bms}'), '簇%d最低温度%s' % (c_i, f'-BMS{bms}'), '簇%d平均温度%s' % (c_i, f'-BMS{bms}'),
               '簇%d最高电压号%s' % (c_i, f'-BMS{bms}'), '簇%d最低电压号%s' % (c_i, f'-BMS{bms}'), '簇%d最高温度号%s' % (c_i, f'-BMS{bms}'),
               '簇%d最低温度号%s' % (c_i, f'-BMS{bms}')]
        colName = ['cluster%dMaxVoltage%s' % (c_i, f'-BMS{bms}'), 'cluster%dMinVoltage%s'% (c_i, f'-BMS{bms}'),
                   'cluster%dAveVoltage%s' % (c_i, f'-BMS{bms}'),
                   'cluster%dMaxTemp%s' % (c_i, f'-BMS{bms}'), 'cluster%dMinTemp%s' % (c_i, f'-BMS{bms}'),
                   'cluster%dAveTemp%s' % (c_i, f'-BMS{bms}'), 'cluster%dMaxVoltageNum%s' % (c_i, f'-BMS{bms}'),
                   'cluster%dMinVoltageNum%s' % (c_i, f'-BMS{bms}'), 'cluster%dMaxTempNum%s' % (c_i, f'-BMS{bms}'),
                   'cluster%dMinTempNum%s' % (c_i, f'-BMS{bms}')]
        columnName.extend(colName)
        column.extend(col)

    def _his_data_cell(self, db=None, bams=None, bank=None, bamsName=None,bankName=None, startN=None, endnN=None, st=None, ed=None,
                       startTime=None, endTime=None, jiange=None, dwd_cursor=None):
        '''获取电芯历史数据'''
        a = timeUtils.getBetweenMonth(startTime, endTime)  # 计算时间范围内的所有年月
        # t = timeUtils.getNowHouse()[:6]
        table = 'ods_r_measure1'
        data1 = []
        if db == 'ygqn' and 'D区' in bamsName[0]:
            return []
        for d in bams:
            d_i = bams.index(d)  # 堆下标
            _d = bamsName[d_i].split('储能单元')
            if db != 'ygqn':
                unit = int(_d[1])
            else:
                if _d[0] == 'A区':
                    unit = int(_d[1])
                elif _d[0] == 'B区':
                    unit = int(_d[1]) + 40
                else:
                    unit = int(_d[1]) + 80
            # db_con = _return_db_con(db, d)  # 获取具体数据库链接
            data2 = []
            columnName = []
            column = []
            if db == 'baodian':  # 保电项目
                timeall = []
                name_v, name_t = [], []
                b0, b1 = [], []
                for c in bankName:  # 具体簇
                    c = int(c.split('簇')[1])
                    c_i = station_unit_cluster_dict.get(db)[unit][c - 1]  # 第几簇
                    v0 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_vol_list', st, ed, startTime, endTime,
                                                   jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')  # 电压
                    v1 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_temp_list', st, ed, startTime, endTime,
                                                   jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')  # 温度
                    vv = [v0, v1]
                    if not timeall:
                        for v in vv:
                            if v:
                                timeall = v['time']
                                break
                    b0.extend(v0['value'][int(startN) - 1:int(endnN)])
                    b1.extend(v1['value'][int(startN) - 1:int(endnN)])
                    for f in range(int(startN), int(endnN) + 1):  # 具体电芯
                        name_t.append('cluster%dcell%sTemp' % (c, f))
                        name_v.append('cluster%dcell%sVoltage' % (c, f))
                        self.set_cel_table_list(c, column, columnName, f)  # 获取表头
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1))
                        for i in range(b):
                            o[name_v[i]] = 0.00 if i >= len(b0) or inx >= len(b0[i]) else b0[i][inx]
                            o[name_t[i]] = 0.00 if i >= len(b1) or inx >= len(b1[i]) else b1[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
            elif db not in exclude_station:
                cluster_count = 0  # 太仓鑫融有两台BMS，在数据表中对应两个储能单元；簇和单元编号不与页面对应要单独处理
                timeall = []
                name_v, name_t = [], []
                b0, b1 = [], []
                value_dict = {
                    'BMS1': [],
                    'BMS2': [],
                }
                for bms in range(1, 3):  # BMS
                    unit = unit + bms - 1
                    if bms == 2:
                        cluster_count = len(station_unit_cluster_dict.get(db)[unit - 1])
                    for c in bank:  # 具体簇
                        c_i = int(c[-2]) + cluster_count  # 第几簇
                        for f in range(int(startN), int(endnN) + 1):  # 具体电芯
                            v0 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_vol_list', st, ed, startTime,
                                                           endTime, jiange, table='dwd_bc_measure_5min',
                                                           k_name='unit_cluster_rk')  # 电压
                            v1 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_temp_list', st, ed, startTime,
                                                           endTime, jiange, table='dwd_bc_measure_5min',
                                                           k_name='unit_cluster_rk')  # 温度
                            vv = [v0, v1]
                            value_dict[f'BMS{bms}'].extend(vv)

                            if not timeall:
                                for v in vv:
                                    if v:
                                        timeall = v['time']
                                        break
                            b0.extend(v0['value'][int(startN) - 1:int(endnN)])
                            b1.extend(v1['value'][int(startN) - 1:int(endnN)])
                            name_t.append('cluster%dcell%sTemp%s' % (c_i - cluster_count, f, f'-BMS{bms}'))
                            name_v.append('cluster%dcell%sVoltage%s' % (c_i - cluster_count, f, f'-BMS{bms}'))
                            self.set_cel_table_list_new(c_i - cluster_count, column, columnName, f, bms)  # 获取表头
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1))
                        for i in range(b):
                            o[name_v[i]] = 0.00 if i >= len(b0) or inx >= len(b0[i]) else b0[i][inx]
                            o[name_t[i]] = 0.00 if i >= len(b1) or inx >= len(b1[i]) else b1[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
            else:
                timeall = []
                name_v, name_t = [], []
                b0, b1 = [], []
                for c in bankName:  # 具体簇
                    c = int(c.split('簇')[1])
                    c_i = station_unit_cluster_dict.get(db)[unit][c - 1]  # 第几簇
                    v0 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_vol_list', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')  # 电压
                    v1 = _select_get_dwd_his_value(dwd_cursor, db, c_i, 'cell_temp_list', st, ed, startTime, endTime, jiange, table='dwd_bc_measure_5min', k_name='unit_cluster_rk')  # 温度
                    vv = [v0, v1]
                    if not timeall:
                        for v in vv:
                            if v:
                                timeall = v['time']
                                break
                    b0.extend(v0['value'][int(startN)-1:int(endnN)])
                    b1.extend(v1['value'][int(startN)-1:int(endnN)])
                    for f in range(int(startN), int(endnN) + 1):  # 具体电芯
                        name_t.append('cluster%dcell%sTemp' % (c, f))
                        name_v.append('cluster%dcell%sVoltage' % (c, f))
                        self.set_cel_table_list(c, column, columnName, f)  # 获取表头
                if timeall:
                    for time in timeall:
                        inx = timeall.index(time)
                        o = {'logtime': time}
                        b = max(len(b0), len(b1))
                        for i in range(b):
                            o[name_v[i]] = 0.00 if i >= len(b0) or inx >= len(b0[i]) else b0[i][inx]
                            o[name_t[i]] = 0.00 if i >= len(b1) or inx >= len(b1[i]) else b1[i][inx]
                        data2.append(o)
                else:
                    column = []
                    columnName = []
            data1.append({'name': bamsName[d_i], 'columnName': columnName, 'column': column, 'data': data2})
        return data1

    def set_cel_table_list(self, c_i, column, columnName, f):
        if columnName == []:
            columnName.append('logtime')
        if column == []:
            column.append('logtime')
        colName = ['cluster%dcell%sTemp' % (c_i, f), 'cluster%dcell%sVoltage' % (c_i, f)]
        col = ['簇%d电芯%s温度' % (c_i, f), '簇%d电芯%s电压' % (c_i, f)]
        columnName.extend(colName)
        column.extend(col)

    def set_cel_table_list_new(self, c_i, column, columnName, f, bms):
        if columnName == []:
            columnName.append('logtime')
        if column == []:
            column.append('logtime')
        colName = ['cluster%dcell%sTemp%s' % (c_i, f, f'-BMS{bms}'), 'cluster%dcell%sVoltage%s' % (c_i, f, f'-BMS{bms}')]
        col = ['簇%d电芯%s温度%s' % (c_i, f, f'-BMS{bms}'), '簇%d电芯%s电压%s' % (c_i, f, f'-BMS{bms}')]
        columnName.extend(colName)
        column.extend(col)

    def bams_bank(self, his_data_bams, his_datas_bank):  # 拼储能单元和电池簇的数据
        data = []
        column = []
        columnName = []
        for d in his_data_bams:
            data1 = d['data']
            column_1 = d['column']
            columnName_1 = d['columnName']
            name1 = d['name']
            for c in his_datas_bank:
                data2 = c['data']
                column_ = c['column']
                columnName_ = c['columnName']
                name2 = c['name']
                if name1 == name2:
                    if data1:
                        for e in data1:
                            if data2:
                                e.update(data2[data1.index(e)])
                    else:
                        data1.extend(data2)
                    columnName_1.extend(columnName_)
                    column_1.extend(column_)
                    column = sorted(set(column_1), key=column_1.index)
                    columnName = sorted(set(columnName_1), key=columnName_1.index)
            data.append({'name': d['name'], 'column': column, 'columnName': columnName, 'data': data1})
        return data


def _select_get_dwd_his_value(db_con, db, unit, name, st, ed, startTime, endTime, jiange, vmax=None, table='dwd_unit_measure_5min', k_name='unit_rk'):
    '''dwd电池堆、簇数据'''
    data = {'time': [], 'value': []}
    if vmax:
        sql = f"""SELECT
                    start_time, {name} 
                FROM
                    {table} 
                WHERE
                    station_name = '{db}'
                    AND {k_name} = {unit} AND
                    {name} < {vmax} 
                    AND start_time BETWEEN '{st}' 
                    AND '{ed}' 
                ORDER BY
                    start_time"""
    else:
        sql = f"""SELECT
                      start_time, {name} 
                  FROM
                        {table} 
                  WHERE
                    station_name = '{db}'
                    AND {k_name} = {unit} AND
                     start_time BETWEEN '{st}' 
                      AND '{ed}' 
                  ORDER BY
                      start_time"""
    db_con.execute(sql)
    values = db_con.fetchall()
    if name in ['cell_vol_list', 'cell_temp_list']:
        for val in values:
            data['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
            data['value'].append(json.loads(val[name]))
    else:
        for val in values:
            data['time'].append(val['start_time'].strftime("%Y-%m-%d %H:%M:%S"))
            if val[name]:
                data['value'].append(val[name])
            else:
                data['value'].append(0)

    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        data['value'].insert(0, data['value'][0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])
    # 去重 否则pandas补全会报错
    times = list(set(data['time']))
    times.sort()
    va = []
    for ti in times:
        ind = data['time'].index(ti)
        va.append(data['value'][ind])
    # print '标准返回格式……………………………………………………………………：',complete_data(data,'1T')
    data['time'] = times
    data['value'] = va
    return complete_data(data, jiange)


def _select_get_his_value(db_con, table, name, st, ed, startTime, endTime, jiange, vmax=None):
    '''获取历史数据'''
    data = {'time': [], 'value': []}
    HisTable_m = HisACDMS(table)
    if vmax:
        values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                         HisTable_m.dts_s.between(st, ed),
                                                                         HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.asc()).all()  # 查询当前月
    else:
        values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                         HisTable_m.dts_s.between(st, ed)).order_by(
            HisTable_m.dts_s.asc()).all()  # 查询当前月
    for val in values:
        data['time'].append(val[1].strftime("%Y-%m-%d %H:%M:%S"))
        data['value'].append(val[0])

    if data['time']:  # 有值
        data['time'].insert(0, startTime)
        data['value'].insert(0, data['value'][0])
    else:
        return data
    data['time'].append(endTime)
    data['value'].append(data['value'][-1])
    # 去重 否则pandas补全会报错
    times = list(set(data['time']))
    times.sort()
    va = []
    for ti in times:
        ind = data['time'].index(ti)
        va.append(data['value'][ind])
    # print '标准返回格式……………………………………………………………………：',complete_data(data,'1T')
    data['time'] = times
    data['value'] = va
    return complete_data(data, jiange)

ygqn={"1":["G1","G2"],"2":["G3","G4"],"3":["G5","G6"],"4":["G7","G8"],"5":["G9","G10"],"6":["G11"]}
shgyu_dic={"1":["G1","G2"],"2":["G3","G4"],"3":["G5","G6"],"4":["G7"]}
def _return_db_con(db, d):
    '''返回数据库链接'''
    if db == 'baodian':
        return baodian_siss[d]
    elif db == 'guizhou':
        d = int(d.split('.')[0][-2])
        return bmsdb[db][d - 1]
    elif db == 'ygqn':
        d= d.split('.')[0]
        for k,v in ygqn.items():
            if d in v:
                d=int(k)
        return bmsdb[db][d-1]
    elif db == 'shgyu':
        d= d.split('.')[0]
        for k,v in shgyu_dic.items():
            if d in v:
                d=int(k)
        return bmsdb[db][d-1]
    elif db == 'taicgxr':
        d = int(d.split('.')[0][1])
        d = d // 2 if d % 2 == 0 else d // 2 + 1
        return bmsdb[db][0][d]
    else:
        d = int(d[1:2]) if db != 'dongmu' else 0
        if db == 'binhai':  # 滨海电站
            if d < 4:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'taicang' or db == 'dongmu':
            return bmsdb[db][0]
        elif db == 'ygzhen':
            if d < 3:
                return bmsdb[db][0]
            else:
                return bmsdb[db][1]
        elif db == 'zgtian' or db == 'datong':
            if d < 3:
                return bmsdb[db][0]
            elif d < 5:
                return bmsdb[db][1]
            elif d < 7:
                return bmsdb[db][2]
            else:
                return bmsdb[db][3]
        elif db == 'houma':
            if d < 3:
                return bmsdb[db][0]
            elif d < 5:
                return bmsdb[db][1]
            elif d < 7:
                return bmsdb[db][2]
            else:
                return bmsdb[db][3]