import json
from copy import deepcopy

from django.core.validators import RegexValidator
from django.db.models import Q
from django_redis import get_redis_connection
from rest_framework import serializers, exceptions

from apis.statistics_apis.models import UserStrategy, Month, StrategyApplyHistory, \
    StrategyApplyHistoryMonth, UserStrategyCategoryNew
from apis.user import models


class ResponseUserStrategySerializer(serializers.ModelSerializer):
    """用户控制策略"""""
    is_show = serializers.IntegerField(write_only=False, read_only=False)
    status = serializers.IntegerField(write_only=False, read_only=False)
    class Meta:
        model = UserStrategy
        # exclude = ['user']
        fields = "__all__"

class UserStrategySerializer(serializers.ModelSerializer):
    """用户控制策略"""""
    # name = serializers.CharField(required=True, max_length=128)
    # is_delete = serializers.IntegerField(write_only=False, read_only=False)

    class Meta:
        model = UserStrategy
        # exclude = ['user']
        fields = "__all__"
        extra_kwargs = {
            "name": {"required": True, },
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }
        # read_only_fields = ('id', 'name')

    def validate_name(self, value):
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        strategy = UserStrategy.objects.filter(user=user_ins, name=value, is_delete=0)
        if strategy:
            raise exceptions.ValidationError("策略名已存在")
        if value == '当前策略':
            raise exceptions.ValidationError("策略名称违规！")
        return value

    def update(self, instance, validated_data):
        instance = instance.update(**validated_data)
        # instance.save()
        return instance

    def create(self, validated_data):
        user = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user)
        obj = UserStrategy.objects.create(user=user_ins, **validated_data)
        for i in range(1, 13):
            Month.objects.create(strategy=obj, month_number=i)

        return obj


class UserStrategyCategorySerializer(serializers.ModelSerializer):
    """用户控制策略-分类"""""
    # name = serializers.CharField(required=True, max_length=128)
    # strategy = serializers.StringRelatedField(label='图书')
    strategy = serializers.PrimaryKeyRelatedField(label='策略', read_only=True)
    strategy_ = serializers.StringRelatedField(label='策略', read_only=True)
    strategy_id = serializers.IntegerField(required=True, write_only=True)
    months = serializers.ListSerializer(required=True, child=serializers.IntegerField(), write_only=True)

    charge_config = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    rl_list = serializers.DictField(required=False)

    class Meta:
        model = UserStrategyCategoryNew
        # exclude = ['is_delete']
        fields = '__all__'
        extra_kwargs = {
            "name": {"required": True, },
            "strategy_id": {"required", True, }
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }

    def validate_name(self, value):
        user_id = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user_id)
        strategy = UserStrategy.objects.filter(id=self.initial_data.get('strategy_id'), user=user_ins, is_delete=0)

        if not strategy.exists():
            raise exceptions.ValidationError("策略-分类：策略 ID 不存在")
        category = UserStrategyCategory.objects.filter(strategy=strategy.first(), name=value, is_delete=0)

        if category.exists():
            raise exceptions.ValidationError("策略-分类：已存在")
        return value

    def validate_strategy_id(self, value):
        user_id = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user_id)
        strategy = UserStrategy.objects.filter(id=self.initial_data.get('strategy_id'), user=user_ins)
        if not strategy:
            raise exceptions.ValidationError("策略ID：不存在")
        return value

    # def update(self, instance, validated_data):
        # charge_config = validated_data.get("charge_config", None)
        # rl_list = validated_data.get("rl_list", None)
        # if charge_config:
        #     validated_data["charge_config"] = str(charge_config)
        # if rl_list:
        #     validated_data["rl_list"] = json.dumps(rl_list)
        #
        # # instance.update(**validated_data)
        # instance.name = validated_data.get('name', instance.name)
        # instance.charge_config = charge_config
        # instance.is_follow = validated_data.get('is_follow', instance.is_follow)
        # instance.rl_list = rl_list
        # instance.pv0 = validated_data.get('pv0', instance.pv0)
        # instance.pv1 = validated_data.get('pv1', instance.pv1)
        # instance.pv2 = validated_data.get('pv2', instance.pv2)
        # instance.pv3 = validated_data.get('pv3', instance.pv3)
        # instance.pv4 = validated_data.get('pv4', instance.pv4)
        # instance.pv5 = validated_data.get('pv5', instance.pv5)
        # instance.pv6 = validated_data.get('pv6', instance.pv6)
        # instance.pv7 = validated_data.get('pv7', instance.pv7)
        # instance.pv8 = validated_data.get('pv8', instance.pv8)
        # instance.pv9 = validated_data.get('pv9', instance.pv9)
        # instance.pv10 = validated_data.get('pv10', instance.pv10)
        # instance.pv11 = validated_data.get('pv11', instance.pv11)
        # instance.pv12 = validated_data.get('pv12', instance.pv12)
        # instance.pv13 = validated_data.get('pv13', instance.pv13)
        # instance.pv14 = validated_data.get('pv14', instance.pv14)
        # instance.pv15 = validated_data.get('pv15', instance.pv15)
        # instance.pv16 = validated_data.get('pv16', instance.pv16)
        # instance.pv17 = validated_data.get('pv17', instance.pv17)
        # instance.pv18 = validated_data.get('pv18', instance.pv18)
        # instance.pv19 = validated_data.get('pv19', instance.pv19)
        # instance.pv20 = validated_data.get('pv20', instance.pv20)
        # instance.pv21 = validated_data.get('pv21', instance.pv21)
        # instance.pv22 = validated_data.get('pv22', instance.pv22)
        # instance.pv23 = validated_data.get('pv23', instance.pv23)
        #
        # # 保存月份
        # strategy = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        # all_strategy_months = Month.objects.filter(strategy=strategy).all()
        # tem_months = validated_data.get('months')
        # for month in all_strategy_months:
        #     if month.month_number in tem_months:
        #         month.is_valid = False
        #         month.user_Strategy_Category = instance
        #     else:
        #         month.is_valid = True
        #         month.user_Strategy_Category = None
        #     month.save()
        #
        # instance.save()
        # return instance

    def create(self, validated_data):
        # user = self.context.get('user_id')
        charge_config = validated_data.get("charge_config", None)
        rl_list = validated_data.get("rl_list", None)

        if charge_config:
            validated_data["charge_config"] = str(charge_config)
        if rl_list:
            validated_data["rl_list"] = json.dumps(rl_list)

        tem_months = deepcopy(validated_data.get('months'))
        validated_data.pop('months')

        strategy = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        category_obj = UserStrategyCategory.objects.create(strategy=strategy, **validated_data)

        # 保存月份
        for month in tem_months:
            month_ins = Month.objects.filter(strategy=strategy, month_number=month).first()
            month_ins.is_valid = False
            month_ins.user_Strategy_Category = category_obj
            month_ins.save()

        return category_obj


class UpdateUserStrategyCategorySerializer(serializers.ModelSerializer):
    """用户控制策略-分类: 更新"""""
    # name = serializers.CharField(required=True, max_length=128)
    # strategy = serializers.StringRelatedField(label='图书')
    strategy = serializers.PrimaryKeyRelatedField(label='策略', read_only=True)
    strategy_ = serializers.StringRelatedField(label='策略', read_only=True)
    strategy_id = serializers.IntegerField(required=True, write_only=True)
    months = serializers.ListSerializer(required=True, child=serializers.IntegerField(), write_only=True)

    charge_config = serializers.ListSerializer(required=False, child=serializers.IntegerField())
    rl_list = serializers.DictField(required=False)
    is_follow = serializers.IntegerField(required=False)

    class Meta:
        model = UserStrategyCategoryNew
        # exclude = ['is_delete']
        fields = '__all__'
        extra_kwargs = {
            "name": {"required": True, },
            "strategy_id": {"required", True, }
            # "name": {"required": True, "error_messages": {"unique": "策略名已存在"}},
        }

    def validate_months(self, value):
        strategy = UserStrategy.objects.get(id=self.initial_data.get('strategy_id'))
        all_valid_strategy_months = Month.objects.filter(strategy=strategy, is_valid=1).all()
        old_cate_months = Month.objects.filter(user_Strategy_Category=self.instance).all()
        old_cate_months_numbers = [valid_month.month_number for valid_month in old_cate_months]
        all_valid_strategy_months_numbers = [valid_month.month_number for valid_month in all_valid_strategy_months]

        for month in value:
            if month not in all_valid_strategy_months_numbers and month not in old_cate_months_numbers:
                raise exceptions.ValidationError("更新策略-分类：{}月份已被其他分类选择".format(month))
        return value

    def validate_name(self, value):
        user_id = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user_id)
        strategy = UserStrategy.objects.filter(id=self.initial_data.get('strategy_id'), user=user_ins, is_delete=0)

        if not strategy.exists():
            raise exceptions.ValidationError("策略-分类：策略 ID 不存在")

        category_instances = UserStrategyCategory.objects.filter(strategy=strategy.first(),
                                                                 name=value).exclude(id=self.instance.id)
        if category_instances.exists():
            raise exceptions.ValidationError("策略-分类：分类名称已存在")
        return value

    def validate_strategy_id(self, value):
        user_id = self.context.get('user_id')
        user_ins = models.UserDetails.objects.get(id=user_id)
        strategy = UserStrategy.objects.filter(id=self.initial_data.get('strategy_id'), user=user_ins)
        if not strategy:
            raise exceptions.ValidationError("策略ID：不存在")
        return value

    def update(self, instance, validated_data):
        charge_config = validated_data.get("charge_config", None)
        rl_list = validated_data.get("rl_list", None)
        if charge_config:
            validated_data["charge_config"] = str(charge_config)
        if rl_list:
            rl_list = json.dumps(rl_list)

        # instance.update(**validated_data)
        instance.name = validated_data.get('name', instance.name)
        instance.charge_config = charge_config
        instance.is_follow = validated_data.get('is_follow', instance.is_follow)
        instance.rl_list = rl_list
        instance.pv0 = validated_data.get('pv0', instance.pv0)
        instance.pv1 = validated_data.get('pv1', instance.pv1)
        instance.pv2 = validated_data.get('pv2', instance.pv2)
        instance.pv3 = validated_data.get('pv3', instance.pv3)
        instance.pv4 = validated_data.get('pv4', instance.pv4)
        instance.pv5 = validated_data.get('pv5', instance.pv5)
        instance.pv6 = validated_data.get('pv6', instance.pv6)
        instance.pv7 = validated_data.get('pv7', instance.pv7)
        instance.pv8 = validated_data.get('pv8', instance.pv8)
        instance.pv9 = validated_data.get('pv9', instance.pv9)
        instance.pv10 = validated_data.get('pv10', instance.pv10)
        instance.pv11 = validated_data.get('pv11', instance.pv11)
        instance.pv12 = validated_data.get('pv12', instance.pv12)
        instance.pv13 = validated_data.get('pv13', instance.pv13)
        instance.pv14 = validated_data.get('pv14', instance.pv14)
        instance.pv15 = validated_data.get('pv15', instance.pv15)
        instance.pv16 = validated_data.get('pv16', instance.pv16)
        instance.pv17 = validated_data.get('pv17', instance.pv17)
        instance.pv18 = validated_data.get('pv18', instance.pv18)
        instance.pv19 = validated_data.get('pv19', instance.pv19)
        instance.pv20 = validated_data.get('pv20', instance.pv20)
        instance.pv21 = validated_data.get('pv21', instance.pv21)
        instance.pv22 = validated_data.get('pv22', instance.pv22)
        instance.pv23 = validated_data.get('pv23', instance.pv23)

        # 保存月份
        strategy = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        old_months = Month.objects.filter(user_Strategy_Category=instance).all()
        all_strategy_months = Month.objects.filter(strategy=strategy).all()
        tem_months = validated_data.get('months')

        for month in all_strategy_months:
            if month.month_number in tem_months:
                month.is_valid = False
                month.user_Strategy_Category = instance
                month.save()
        for month_ in old_months:
            if month_.month_number not in tem_months:
                month_.is_valid = True
                month_.user_Strategy_Category = None
                month_.save()

        instance.save()
        return instance

    def create(self, validated_data):
        # user = self.context.get('user_id')
        charge_config = validated_data.get("charge_config", None)
        rl_list = validated_data.get("rl_list", None)

        if charge_config:
            validated_data["charge_config"] = str(charge_config)
        if rl_list:
            validated_data["rl_list"] = json.dumps(rl_list)

        tem_months = deepcopy(validated_data.get('months'))
        validated_data.pop('months')

        strategy = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        category_obj = UserStrategyCategoryNew.objects.create(strategy=strategy, **validated_data)

        # 保存月份
        for month in tem_months:
            month_ins = Month.objects.filter(strategy=strategy, month_number=month).first()
            month_ins.is_valid = False
            month_ins.user_Strategy_Category = category_obj
            month_ins.save()

        return category_obj


class UserStrategyApplySerializer(serializers.ModelSerializer):
    """自动控制策略：下发"""""
    # 手机号校验
    mobile = serializers.CharField(required=True,
                                   validators=[RegexValidator(r"^1[3-9]\d{9}$", message="手机号格式不正确")],
                                   write_only=True)
    # 验证码校验
    code = serializers.CharField(required=True, validators=[RegexValidator(r"^\d{6}$", message="验证码格式不正确")],
                                 write_only=True)

    strategy_id = serializers.IntegerField(required=True, write_only=True)
    station_id = serializers.IntegerField(required=True, write_only=True)

    class Meta:
        model = StrategyApplyHistory
        exclude = ['station', "user"]

    def create(self, validated_data):
        user = self.context.get('user_id')
        station_id = self.context.get('station_id')
        uid = self.context.get('uid')
        if not station_id:
            raise serializers.ValidationError("站 ID 不能为空")

        try:
            # station_ins = models.StationDetails.objects.get(id=station_id)
            master_station = models.MaterStation.objects.get(id=station_id, is_delete=0)
            station_ins = master_station.stationdetails_set.filter(is_delete=0).filter(Q(slave=-1) | Q(slave=0)).first()
        except Exception as e:
            raise serializers.ValidationError("站点不存在")

        validated_data.pop("mobile")
        validated_data.pop("code")

        old_strategy_ins = UserStrategy.objects.get(id=validated_data.get('strategy_id'))
        strategy_obj = StrategyApplyHistory.objects.create(user=user, name=old_strategy_ins.name, station=station_ins,
                                                     uid=uid)

        # old_category_objs = old_strategy_ins.userstrategycategory_set.all()
        # for old_category_obj in old_category_objs:
        #     category_obj = StrategyApplyHistoryCategory.objects.create(name=old_category_obj.name,
        #                                                                strategy=strategy_obj,
        #                                                                charge_config=old_category_obj.charge_config,
        #                                                                is_follow=old_category_obj.is_follow,
        #                                                                rl_list=old_category_obj.rl_list,
        #                                                                pv0=old_category_obj.pv0,
        #                                                                pv1=old_category_obj.pv1,
        #                                                                pv2=old_category_obj.pv2,
        #                                                                pv3=old_category_obj.pv3,
        #                                                                pv4=old_category_obj.pv4,
        #                                                                pv5=old_category_obj.pv5,
        #                                                                pv6=old_category_obj.pv6,
        #                                                                pv7=old_category_obj.pv7,
        #                                                                pv8=old_category_obj.pv8,
        #                                                                pv9=old_category_obj.pv9,
        #                                                                pv10=old_category_obj.pv10,
        #                                                                pv11=old_category_obj.pv11,
        #                                                                pv12=old_category_obj.pv12,
        #                                                                pv13=old_category_obj.pv13,
        #                                                                pv14=old_category_obj.pv14,
        #                                                                pv15=old_category_obj.pv15,
        #                                                                pv16=old_category_obj.pv16,
        #                                                                pv17=old_category_obj.pv17,
        #                                                                pv18=old_category_obj.pv18,
        #                                                                pv19=old_category_obj.pv19,
        #                                                                pv20=old_category_obj.pv20,
        #                                                                pv21=old_category_obj.pv21,
        #                                                                pv22=old_category_obj.pv22,
        #                                                                pv23=old_category_obj.pv23
        #                                                                )
            # old_category_months = old_category_obj.month_set.all()
            # for old_category_month in old_category_months:
            #     StrategyApplyHistoryMonth.objects.create(month_number=old_category_month.month_number,
            #                                              strategy=strategy_obj, user_Strategy_Category=category_obj,
            #                                              is_valid=False)

        # todo

        return strategy_obj

    def validate_code(self, value):
        """验证码"""
        mobile = self.initial_data.get("mobile")
        monitor_mobile = "auto" + str(mobile)
        conn = get_redis_connection("default")
        cache_mobile_code = conn.get(monitor_mobile)
        if not cache_mobile_code:
            raise exceptions.ValidationError("验证码过期或不存在")
        if cache_mobile_code.decode('utf-8') != value:
            raise exceptions.ValidationError("验证码错误")
        return value


class CurrentStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)

    def validate_station(self, value):
        # exist = models.StationDetails.objects.filter(english_name=value)
        master_stations = models.MaterStation.objects.filter(english_name=value, is_delete=0)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value


class DefaultStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)

    def validate_station(self, value):
        master_stations = models.MaterStation.objects.filter(english_name=value, is_delete=0)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value

        #     return -1
        # return exist.id, exist.province, exist.type, exist.level, exist.rated_power


class CustomizeStrategySerializers(serializers.Serializer):
    station = serializers.CharField(required=True, write_only=True)
    strategy_id = serializers.IntegerField(required=True, write_only=True)

    def validate_station(self, value):
        master_stations = models.MaterStation.objects.filter(english_name=value, is_delete=0)
        if not master_stations.exists():
            return exceptions.ValidationError("站名不存在")
        return value

        # exist = models.StationDetails.objects.filter(english_name=station).first()
        # if not exist:
        #     return -1
        # return exist.id, exist.province, exist.type, exist.level, exist.rated_power