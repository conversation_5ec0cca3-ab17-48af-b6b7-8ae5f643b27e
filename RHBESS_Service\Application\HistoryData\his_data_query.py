#!/usr/bin/env python
# coding=utf-8
#@Information:
'''
通用历史数据获取接口
'''
import numpy
from Application.HistoryData.his_bams import _return_db_con
from Application.Models.User.data_item_t import DataItem
from Application.Models.User.equipment_number_t import EquipmentNumbe
from Tools.Cfg.DB_his import get_dhis
from Tools.DB.mysql_user import user_session
from Application.Models.His.r_ACDMS import His<PERSON>DM<PERSON>,HisDM,HisACDMS_S_D
from Tools.Utils.num_utils import *
import tornado.web
from Tools.DB.mysql_his import dongmu_session
import time,datetime
from Application.Models.User.point_type import PointType
from Application.Models.User.point_table import PointTable
from Application.Models.User.data_item import DataItemV2
db_=get_dhis('his_data_query')
exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','binhai','datong','shgyu']
class HisDataQueryInterface(BaseHandler):
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            if kt == 'GetDeviceType':  # 设备类型
                db = self.get_argument('db', 'taicgxr')
                if db not in exclude_station:
                    if lang == 'zh':
                        data = user_session.query(PointType).filter(PointType.station == db).all()
                        d = [
                            {
                                'id': 0,
                                'name': "全部设备"
                            }
                        ]
                        for i in data:
                            d.append(
                                {
                                    'id': i.id,
                                    'name': i.equipment_name,
                                }
                            )
                    else:
                        data = user_session.query(PointType).filter(PointType.station == db).all()
                        d = [
                            {
                                'id': 0,
                                'name': "All devices"
                            }
                        ]
                        for i in data:
                            d.append(
                                {
                                    'id': i.id,
                                    'name': i.en_equipment_name,
                                }
                            )
                else:
                    if lang=='en':
                        d = [{'id': 0, "name": 'All equipment'}, {'id': 2, "name": 'Ammeter'}, {'id': 3, "name": 'Energy storage converter'},
                             {'id': 4, "name": 'Battery cluster'}, {'id': 5, "name": 'Other equipment'}]
                    else:
                        d = [{'id': 0, "name": '全部设备'}, {'id': 2, "name": '电量表'}, {'id': 3, "name": '储能变流器'},
                             {'id': 4, "name": '电池簇'},{'id': 5, "name": '其他设备'}]
                return self.returnTypeSuc(d)
            elif kt == 'GetDeviceMun':# 设备编号
                db = self.get_argument('db', None)
                ty = self.get_argument('ty', None)#设备类型,1全部设备,2电量表,3储能变流器,4电池簇,5其他设备
                name = self.get_argument('name', default='')  # 设备名称，模糊匹配
                data = []
                if db not in exclude_station:
                    if int(ty) == 0:
                        descr = user_session.query(DataItemV2).filter(DataItemV2.station == db)
                    else:
                        # filters.append(DataItemV2.point_type_id == int(ty))
                        filters = [DataItemV2.point_type_id == int(ty), DataItemV2.station == db]
                        descr = user_session.query(DataItemV2).filter(*filters)
                    if lang == 'zh':
                        if name:
                            descr = descr.filter(DataItemV2.descr.like('%' + name + '%'))
                        descr = descr.all()
                        for i in descr:
                            data.append(
                                {
                                    'label': i.descr,
                                    'value': i.point_type.en_equipment_name,
                                    'name_la': i.name
                                }
                            )
                    else:
                        if name:
                            descr = descr.filter(DataItemV2.en_descr.like('%' + name + '%'))
                        descr = descr.all()
                        for i in descr:
                            data.append(
                                {
                                    'label': i.en_descr,
                                    'value': i.point_type.en_equipment_name,
                                    'name_la': i.name
                                }
                            )
                else:
                    filter=[EquipmentNumbe.station==db]
                    if ty == '2':
                        filter.append(EquipmentNumbe.ty=='电表')
                    elif ty == '3':
                        filter.append(EquipmentNumbe.ty.like('%PCS'))
                    elif ty == '4':
                        filter.append(EquipmentNumbe.ty.like('%CU'))
                    elif ty == '5':
                        filter.append(EquipmentNumbe.ty.notlike('%PCS'))
                        filter.append(EquipmentNumbe.ty.notlike('%CU'))
                        filter.append(EquipmentNumbe.ty!='电表')
                    if name:
                        if lang == 'zh':
                            filter.append(EquipmentNumbe.name.like('%' + name + '%'))
                        else:
                            filter.append(EquipmentNumbe.en_name.like('%' + name + '%'))
                    if lang == 'en':
                        descr = user_session.query(EquipmentNumbe.en_name,EquipmentNumbe.en_ty,EquipmentNumbe.name,EquipmentNumbe.name_la,EquipmentNumbe.is_use=='1').filter(*filter).all()
                        if descr:
                            for i in descr:
                                data.append({'label': i[0], 'value': i[1], 'name': i[2],'name_la':i[3]})
                    else:
                        descr = user_session.query(EquipmentNumbe.name, EquipmentNumbe.ty,EquipmentNumbe.name_la,EquipmentNumbe.is_use=='1').filter(*filter).all()
                        if descr:
                            for i in descr:
                                data.append({'label':i[0],'value':i[1],'name_la':i[2]})
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceDataItem':# 设备数据项
                db = self.get_argument('db', None)
                ty = self.get_argument('ty', None)#设备类型,1全部设备,2电量表,3储能变流器,4电池簇,5其他设备
                table_ty = self.get_argument('table_ty', None)#1测量量，2状态量
                frequently = self.get_argument('frequently', None)#1常用数据项
                find = self.get_argument('find', None)#搜索项
                data= {}
                data_list = []
                data_list2 = []
                if db not in exclude_station:
                    filter = [PointTable.data_type == int(table_ty), PointTable.station == db]
                    if int(ty) != 0:
                        point_type = user_session.query(PointType).filter(PointType.id == int(ty)).first().en_equipment_name
                        filter.append(PointTable.device_name == point_type)
                    if find:
                        filter.append(PointTable.name.like('%' + find + '%'))
                    descr = user_session.query(PointTable).filter(*filter).all()
                    _data = []
                    if lang == 'zh':
                        for i in descr:
                            _data.append(
                                {
                                    'label': i.name,
                                    'value': i.device_name,
                                    'name_la': i.name_la,
                                }
                            )
                    else:
                        for i in descr:
                            _data.append(
                                {
                                    'label': i.en_name,
                                    'value': i.device_name,
                                    'name_la': i.name_la,
                                }
                            )
                    data['data_item'] = _data
                else:
                    filter = [DataItem.station == db, DataItem.is_use == '1']
                    filter2 = [DataItem.station == db, DataItem.is_use == '1']

                    if ty == '1':
                        self.table_ty_filter(filter, table_ty,frequently,filter2,find)
                    elif ty == '2':
                        filter.append(DataItem.ty == '电表')
                        filter2.append(DataItem.ty == '电表')
                        self.table_ty_filter(filter, table_ty,frequently,filter2,find)
                    elif ty == '3':
                        filter.append(DataItem.ty.like('%PCS'))
                        filter2.append(DataItem.ty.like('%PCS'))
                        self.table_ty_filter(filter, table_ty,frequently,filter2,find)
                    elif ty == '4':
                        filter.append(DataItem.ty .like('%CU'))
                        filter2.append(DataItem.ty .like('%CU'))
                        self.table_ty_filter(filter, table_ty,frequently,filter2,find)
                    elif ty == '5':
                        filter.append(DataItem.ty.notlike('%PCS'))
                        filter2.append(DataItem.ty.notlike('%PCS'))
                        filter.append(DataItem.ty.notlike('%CU'))
                        filter2.append(DataItem.ty.notlike('%CU'))
                        filter.append(DataItem.ty!='电表')
                        filter2.append(DataItem.ty!='电表')
                        self.table_ty_filter(filter, table_ty,frequently,filter2,find)
                    if lang == 'en':
                        descr = user_session.query(DataItem.name,DataItem.en_ty,DataItem.en_name,DataItem.name_la).filter(*filter).order_by(DataItem.id.asc()).all()
                        if descr:
                            for i in descr:
                                data_list.append({'label': i[2], 'value': i[1], 'name': i[0],'name_la':i[3]})
                        data['data_item']=data_list
                        if frequently:
                            descr = user_session.query(DataItem.name,DataItem.en_ty,DataItem.en_name,DataItem.name_la).filter(*filter2).order_by(DataItem.id.asc()).all()
                            if descr:
                                for i in descr:
                                    data_list2.append({'label':i[2],'value':i[1], 'name': i[0],'name_la':i[3]})
                            data['frequently'] = data_list2
                    else:
                        descr = user_session.query(DataItem.name, DataItem.ty,DataItem.name_la).filter(*filter).order_by(DataItem.id.asc()).all()
                        if descr:
                            for i in descr:
                                data_list.append({'label': i[0], 'value': i[1],'name_la':i[2]})
                        data['data_item'] = data_list
                        if frequently:
                            descr = user_session.query(DataItem.name, DataItem.ty,DataItem.name_la).filter(*filter2).order_by(DataItem.id.asc()).all()
                            if descr:
                                for i in descr:
                                    data_list2.append({'label': i[0], 'value': i[1],'name_la':i[2]})
                            data['frequently'] = data_list2
                return self.returnTypeSuc(data)
            elif kt == 'GetDeviceMunFind':  # 设备数据查找
                db = self.get_argument('db', None)  # 电站名称
                ty_mun = self.get_argument('ty_mun', [])#设备编号
                table_ty = self.get_argument('table_ty', None)#1测量量，2状态量
                data_item = self.get_argument('data_item', [])#数据项
                start_Time = self.get_argument('start_Time', None) # 开始时间
                end_Time = self.get_argument('end_Time', None) # 结束时间
                now = timeUtils.getNewTimeStr()[:10]  # 当前时间
                ty_mun = json.loads(ty_mun)
                data_item = json.loads(data_item)
                # if timeUtils.timeStrToTamp('%s %s' % (end_Time[:10], '00:00:00')) > timeUtils.timeStrToTamp('%s %s' % (now, '00:00:00')):
                #     end_Time = now + ' 00:00:00'
                data = {'time':[],'value':[]}
                if ty_mun != [] and data_item != []:
                    if db not in exclude_station:
                        st = start_Time
                        ed = end_Time
                        table_m = 'ods_r_measure1'
                        table_s = 'ods_r_status1'
                        if db == 'taicgxr':
                            dd = 0
                            db_con_ms = self._return_db_con_pcs(db, dd)  # 获取具体数据库链接
                            for t in ty_mun:
                                # 确定数据库链接
                                descr = []  # name描述
                                names = []  # name集合
                                # if t['value']=='BMS:' and 1:
                                #     pass
                                # else:
                                for d in data_item:
                                    if t['value'] == d['value']:
                                        names.append(t['name_la'] + '.' + d['name_la'])
                                        descr.append(t['label'] + ' ' + d['label'])

                                self.find_data_new(data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names,
                                               st, start_Time, table_m, table_s, table_ty)
                        elif db in ['tczj', 'sikly']:
                            for t in ty_mun:
                                # 确定数据库链接
                                descr = []  # name描述
                                names = []  # name集合
                                if t['name_la'].split('.')[1] == 'G':  # 查询G库
                                    db_con_ms = self._return_db_con_pcs(db, 1)  # 获取具体数据库链接
                                else:
                                    db_con_ms = self._return_db_con_pcs(db, 0)  # 获取具体数据库链接
                                for d in data_item:
                                    if t['value'] == d['value']:
                                        names.append(t['name_la'] + '.' + d['name_la'])
                                        descr.append(t['label'] + ' ' + d['label'])

                                self.find_data_new(data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names,
                                                   st, start_Time, table_m, table_s, table_ty)
                    else:
                        if db != 'dongmu':
                            st = start_Time
                            ed = end_Time
                            table_m = 'ods_r_measure1'
                            table_s = 'ods_r_status1'
                            for t in ty_mun:
                                # 确定数据库链接
                                descr = []  # name描述
                                names = []  # name集合
                                if t['value']=='电表':
                                    for d in data_item:
                                        if t['value'] == d['value']:
                                            names.append(t['name_la'] + d['name_la'])
                                            descr.append(t['label'] + ' ' + d['label'])
                                    if names == []:
                                        if lang == 'en':
                                            return self.customError("The device number and data item are incorrect. Please re-select!")
                                        else:
                                            return self.customError("设备编号和数据项选择错误，请重新选择!")
                                    else:
                                        for n in names:
                                            indx_n = names.index(n)
                                            time_table = []
                                            value_ = []
                                            if '.MeasureA8' in n:
                                                db_con_ms_d = self._return_db_con_pcs(db, 5)  # 获取具体数据库链接 中天电表
                                            elif 'MET.Et_pos1' in n or 'MET.Et_neg1' in n:  # 阳泉永臻贵州上虞电表
                                                dd=1
                                                db_con_ms_d = self._return_db_con_pcs(db, dd)  # 获取具体数据库链接
                                            descr2 = self._select_get_his_value_22(db_con_ms_d, table_m, n, st, ed, jiange=0,vmax=10731200000000,minV=0.01)  # 最大值小于65535
                                            time_table.extend(descr2['time'])
                                            value_.extend(descr2['value'])
                                            db_con_ms_d.close()
                                            if time_table != []:
                                                if data['time'] == []:
                                                    data['time'] = time_table
                                            else:
                                                t_list = [start_Time]
                                                for a in t_list:
                                                    n_t = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                                                    ti_1 = timeUtils.ssTtimes(n_t)
                                                    if ti_1 > end_Time:
                                                        break
                                                    else:
                                                        t_list.append(ti_1)
                                                data['time'] = t_list
                                            if lang == 'en':
                                                data['value'].append({'name1': descr[indx_n], 'name2': n, 'value': value_})
                                            else:
                                                data['value'].append({'name1': n, 'name2': descr[indx_n], 'value': value_})
                                else:
                                    if t['value']=='CU':
                                        for d in data_item:
                                            if t['value'] == d['value']:
                                                names.append(t['name_la'] + d['name_la'])
                                                descr.append(t['label'] + ' ' + d['label'])

                                        if db =='halun':
                                            dd = t['name_la'].split('.')[0][-1]
                                            db_con_ms = self._return_db_con_pcs(db, dd)  # 获取具体数据库链接
                                        else:
                                            dd = t['name_la']
                                            db_con_ms = _return_db_con(db, dd)  # 获取具体数据库链接
                                        if names == []:
                                            if lang == 'en':
                                                return self.customError(
                                                    "The device number and data item are incorrect. Please re-select!")
                                            else:
                                                return self.customError("设备编号和数据项选择错误，请重新选择!")
                                        else:
                                            self.find_data(data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names,
                                                           st, start_Time, table_m, table_s, table_ty)
                                    else:
                                        for d in data_item:
                                            if t['value'] == d['value']:
                                                names.append(t['name_la'] + d['name_la'])
                                                descr.append(t['label'] + ' ' + d['label'])
                                        if db == 'guizhou':
                                            dd = t['name_la'].split('.')[0][-2]
                                        elif db == 'houma':
                                            dd = t['name_la'].split('.')[0][-2:]
                                        elif db == 'ygqn':
                                            if 'ygqn.d' in t['name_la']:
                                                dd=2
                                            else:
                                                dd=1
                                        elif db == 'shgyu':
                                            dd = 1
                                        else:
                                            dd = t['name_la'].split('.')[0][-1]
                                        db_con_ms = self._return_db_con_pcs(db, dd)  # 获取具体数据库链接
                                        if names == []:
                                            if lang == 'en':
                                                return self.customError(
                                                    "The device number and data item are incorrect. Please re-select!")
                                            else:
                                                return self.customError("设备编号和数据项选择错误，请重新选择!")
                                        else:
                                            t1 = time.time()
                                            self.find_data(data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names,
                                                           st, start_Time, table_m, table_s, table_ty)
                                            t2 = time.time()
                                            print(t2-t1, '---------------------------------------------------')

                        elif db == 'dongmu':
                            table_dongmu_1 = 't_status_bits'
                            table_dongmu_2 = 't_device'
                            table_dongmu_4 = 't_status'
                            HisTable_dongmu_1 = HisACDMS_S_D(table_dongmu_1)
                            HisTable_dongmu_2 = HisACDMS_S_D(table_dongmu_2)
                            table_dongmu_4 = HisACDMS_S_D(table_dongmu_4)
                            bit = []  # 东睦
                            names=[]
                            descr=[]
                            if table_ty == '2':
                                for l in ty_mun:
                                    if lang != 'en':
                                        l['name'] = l['label']
                                    db_con = self._return_db_con_sda(db, 0)  # 获取具体数据库链接
                                    id_ = db_con.query(HisTable_dongmu_2.id).filter(
                                        HisTable_dongmu_2.name == l['name']).first()
                                    if id_:
                                        name = db_con.query(table_dongmu_4.name, table_dongmu_4.id).filter(
                                            table_dongmu_4.device_id == id_[0]).first()  # 取出设备表的name，id
                                        names.append(name[0])
                                        if data_item != []:
                                            for d in data_item:
                                                if lang != 'en':
                                                    d['name'] = d['label']
                                                bit_1 = []
                                                if l['value'] == d['value']:
                                                    bit_ = db_con.query(HisTable_dongmu_1.bits).filter(
                                                        HisTable_dongmu_1.status_id == name[1],
                                                        HisTable_dongmu_1.descr.like('%' + d['name'] + '%')).first()
                                                    if bit_ != [] and bit_ != None:
                                                        bit.append(bit_[0])
                                                        bit_1.append(bit_[0])
                                                    else:
                                                        break
                                    db_con.close()
                            else:
                                for t in ty_mun:
                                    if lang != 'en':
                                        t['name'] = t['label']
                                    for d in data_item:
                                        if lang != 'en':
                                            d['name'] = d['label']
                                        if t['value'] == d['value']:
                                            names.append(d['name_la'])
                                            descr.append(t['name'] + ' ' + d['name'])
                            if names == []:
                                if lang == 'en':
                                    return self.customError(
                                        "The device number and data item are incorrect. Please re-select!")
                                else:
                                    return self.customError("设备编号和数据项选择错误，请重新选择!")
                            else:
                                t_list = [start_Time]
                                for a in t_list:
                                    n = timeUtils.timeStrToTamp(a) + 900
                                    ti_1 = timeUtils.ssTtimes(n)
                                    if ti_1 > end_Time:
                                        break
                                    else:
                                        t_list.append(ti_1)
                                tables_d = 'r_measure'
                                tables2_d = 'r_status'
                                st = timeUtils.timeStrToTamp(start_Time)  # 起始时间绝对秒
                                ed = timeUtils.timeStrToTamp(end_Time)  # 截止时间绝对秒
                                data['time'] = []
                                if table_ty == '1':
                                    for n in names:
                                        timeall_1 = []
                                        dm_table = HisDM(tables_d)
                                        values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                            dm_table.time.between(st, ed)).order_by(dm_table.time.asc()).all()
                                        value_aaa = {}
                                        for value_mon in values_mong:
                                            data_info = json.loads(value_mon[0])
                                            value_mong = data_info['body']
                                            time_1 = data_info['utime']
                                            timeArray = time.localtime(time_1)  # 秒数
                                            otherStyleTime = time.strftime('%Y-%m-%d %H:%M:%S', timeArray)
                                            timeall_1.append(otherStyleTime)
                                            for v in value_mong:
                                                for t in ty_mun:
                                                    if v['device'] == t['label']:
                                                        if n in v.keys():
                                                            value_obj = v[n]  # 历史值
                                                            t_label = t['label']
                                                            if t_label not in value_aaa.keys():
                                                                value_aaa = {t_label: {'value': [value_obj], 'name': n}}
                                                            else:
                                                                value_aaa[t_label]['value'].append(value_obj)
                                        for aaa in value_aaa.keys():
                                            data12 = {}
                                            data12['time'] = timeall_1
                                            data12['value'] = value_aaa[aaa]['value']
                                            df = pd.DataFrame(data12)
                                            df = df.drop_duplicates(subset=["time"], keep="first")
                                            # 转换回字典
                                            data12["time"] = df["time"].tolist()
                                            data12["value"] = df["value"].tolist()
                                            for d in data_item:
                                                value_ = []
                                                timeall_ = []
                                                if d['secondVal'] == 1:
                                                    data22 = complete_data(data12, '15T') if data12['time'] else {}
                                                    if data22:
                                                        value_ = data22['value']
                                                        if timeall_1 != []:
                                                            timeall_ = data22['time']
                                                elif d['secondVal'] == 2:
                                                    value_ = self.count_avg(data12, start_Time, t_list)
                                                    if timeall_1 != []:
                                                        timeall_ = t_list
                                                elif d['secondVal'] == 3:
                                                    value_ = self.count_max(data12, start_Time, t_list)
                                                    if timeall_1 != []:
                                                        timeall_ = t_list
                                                elif d['secondVal'] == 4:
                                                    value_ = self.count_min(data12, start_Time, t_list)
                                                    if timeall_1 != []:
                                                        timeall_ = t_list
                                                elif d['secondVal'] == 5:
                                                    value_ = self.rount_range(data12, start_Time, t_list)
                                                    if timeall_1 != []:
                                                        timeall_ = t_list
                                                dongmu_session.close()
                                                data['time'] = timeall_
                                                if lang == 'en':
                                                    data['value'].append(
                                                        {'name1': aaa, 'name2': value_aaa[aaa]['name'], 'value': value_})
                                                else:
                                                    data['value'].append(
                                                        {'name1': value_aaa[aaa]['name'], 'name2': aaa, 'value': value_})
                                                data['value'] = sorted(data['value'], key=lambda x: x.get("name2"))
                                elif table_ty == '2':
                                    for n2 in names:
                                        indx_n = names.index(n2)
                                        value_ = []
                                        descr2 = {}
                                        ty_mun_2 = ty_mun[indx_n]['name']  # 设备编号
                                        bits = bit[indx_n]
                                        dm_table = HisDM(tables2_d)
                                        timeall_1 = []
                                        value_aa = []
                                        values_mong = dongmu_session.query(dm_table.datainfo).filter(
                                            dm_table.time.between(st, ed)).order_by(
                                            dm_table.time.asc()).all()
                                        for value_mon in values_mong:
                                            data_info = json.loads(value_mon[0])
                                            value_mong = data_info['body']
                                            time_1 = data_info['utime']
                                            timeArray = time.localtime(time_1)  # 秒数
                                            otherStyleTime = time.strftime('%Y-%m-%d %H:%M:%S', timeArray)
                                            timeall_1.append(otherStyleTime)
                                            for v in value_mong:
                                                if v['device'] == ty_mun_2:
                                                    if n2 in v.keys():
                                                        bit_nn = '{:016b}'.format(int(v[n2]))  # 转成2进制，高位补零
                                                        bit_list_n = list(bit_nn)  # 转数据，正好是反向的，需要整个反转
                                                        bit_list_n.reverse()
                                                        bin_aaa = bit_list_n[bits]
                                                        value_aa.append(int(bin_aaa))
                                        descr2['time'] = timeall_1
                                        descr2['value'] = value_aa
                                        self.status_max(db, descr2, start_Time, t_list, value_)
                                    data['time'] = t_list
                else:
                    if lang == 'en':
                        return self.customError("Device number, data item, start time, end time cannot be empty!")
                    else:
                        return self.customError("设备编号、数据项、开始时间、结束时间不能为空!")

                return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            user_session.close()
            logging.info(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()
            dongmu_session.close()

    def find_data(self, data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names, st, start_Time, table_m,
                  table_s, table_ty):
        if table_ty == '1':
            for n in names:
                indx_n = names.index(n)
                time_table = []
                value_ = []
                if 'TmaxInvt' in n or 'CellT' in n or 'Tem' in n or 'Tmp' in n or 'SOC' in n or 'Soc' in n or 'SOH' in n or 'Soh' in n or 'SgTmax' in n in n or 'SgTmin' in n in n \
                        or 'CellTmin_Rk' in n or 'CellTmax_Rk' in n:
                    if data_item[indx_n]['secondVal'] == 1:
                        descr2 = self._select_get_his_value_22(db_con_ms, table_m, n,
                                                               st, ed, jiange='15T',
                                                               vmax=100,
                                                               minV=0.01)  # 最大值小于100
                        time_table.extend(descr2['time'])
                        value_.extend(descr2['value'])
                    else:
                        t_list = [start_Time]
                        for a in t_list:
                            n = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                            ti_1 = timeUtils.ssTtimes(n)
                            if ti_1 > end_Time:
                                break
                            else:
                                t_list.append(ti_1)
                        if data_item[indx_n]['secondVal'] == 2:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=100,
                                                                   minV=0.01)  # 最大值小于100
                            value_ = self.count_avg(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 3:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=100,
                                                                   minV=0.01)  # 最大值小于100
                            value_ = self.count_max(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 4:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=100,
                                                                   minV=0.01)  # 最大值小于100
                            value_ = self.count_min(descr2, start_Time, t_list, )
                        elif data_item[indx_n]['secondVal'] == 5:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=100,
                                                                   minV=0.01)  # 最大值小于100
                            value_ = self.rount_range(descr2, start_Time, t_list)
                elif 'Invt' in n or 'Cur' in n or 'Pw' in n or 'Pow' in n or 'ReactP' in n or 'RealP' in n or 'AlmTrig' in n or 'PrtTrig' in n or 'AlmRetn' in n or 'PrtRetn' in n or 'PwLim' in n or 'SgVmax' in n or 'SgVmin' in n or 'Vol' in n \
                        or 'CellVmin_Rk' in n or 'CellVmax_Rk' in n or 'HestCelVol' in n or 'LestCelVol' in n or 'MaxCelVDif' in n or 'MaxCelTDif' in n:
                    if data_item[indx_n]['secondVal'] == 1:
                        descr2 = self._select_get_his_value_5(db_con_ms, table_m, n, st,
                                                              ed, jiange='15T',
                                                              vmax=999999)  # 有负数，所有不控制最小值
                        time_table.extend(descr2['time'])
                        value_.extend(descr2['value'])
                    else:
                        t_list = [start_Time]
                        for a in t_list:
                            n = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                            ti_1 = timeUtils.ssTtimes(n)
                            if ti_1 > end_Time:
                                break
                            else:
                                t_list.append(ti_1)
                        if data_item[indx_n]['secondVal'] == 2:
                            descr2 = self._select_get_his_value_5(db_con_ms, table_m, n,
                                                                  st, ed, jiange=0,
                                                                  vmax=999999)  # 有负数，所有不控制最小值
                            value_ = self.count_avg(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 3:
                            descr2 = self._select_get_his_value_5(db_con_ms, table_m, n,
                                                                  st, ed, jiange=0,
                                                                  vmax=999999)  # 有负数，所有不控制最小值
                            value_ = self.count_max(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 4:
                            descr2 = self._select_get_his_value_5(db_con_ms, table_m, n,
                                                                  st, ed, jiange=0,
                                                                  vmax=999999)  # 有负数，所有不控制最小值
                            value_ = self.count_min(descr2, start_Time, t_list, )
                        elif data_item[indx_n]['secondVal'] == 5:
                            descr2 = self._select_get_his_value_5(db_con_ms, table_m, n,
                                                                  st, ed, jiange=0,
                                                                  vmax=999999)  # 有负数，所有不控制最小值
                            value_ = self.rount_range(descr2, start_Time, t_list)
                else:
                    if data_item[indx_n]['secondVal'] == 1:
                        descr2 = self._select_get_his_value_22(db_con_ms, table_m, n,
                                                               st, ed, jiange='15T',
                                                               vmax=99999999,
                                                               minV=0.01)  # 最大值小于65535
                        time_table.extend(descr2['time'])
                        value_.extend(descr2['value'])
                    else:
                        t_list = [start_Time]
                        for a in t_list:
                            n = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                            ti_1 = timeUtils.ssTtimes(n)
                            if ti_1 > end_Time:
                                break
                            else:
                                t_list.append(ti_1)
                        if data_item[indx_n]['secondVal'] == 2:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=99999999,
                                                                   minV=0.01)  # 最大值小于65535
                            value_ = self.count_avg(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 3:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=99999999,
                                                                   minV=0.01)  # 最大值小于65535
                            value_ = self.count_max(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 4:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=99999999,
                                                                   minV=0.01)  # 最大值小于65535
                            value_ = self.count_min(descr2, start_Time, t_list)
                        elif data_item[indx_n]['secondVal'] == 5:
                            descr2 = self._select_get_his_value_22(db_con_ms, table_m,
                                                                   n, st, ed, jiange=0,
                                                                   vmax=99999999,
                                                                   minV=0.01)  # 最大值小于65535
                            value_ = self.rount_range(descr2, start_Time, t_list, )
                data_dci = {'value': value_, 'time': time_table}
                if data_dci['time']:  # 有值
                    data_dci['time'].insert(0, start_Time)
                    data_dci['value'].insert(0, data_dci['value'][0])

                    data_dci['time'].append(end_Time)
                    data_dci['value'].append(data_dci['value'][-1])

                    df = pd.DataFrame(data_dci)
                    df = df.drop_duplicates(subset=["time"], keep="first")
                    # 转换回字典
                    data_dci["time"] = df["time"].tolist()
                    data_dci["value"] = df["value"].tolist()

                    data_dci_ = complete_data(data_dci, '15T')
                    if data_dci_['time'] != []:
                        if data['time'] == []:
                            data['time'] = data_dci_['time']
                    else:
                        t_list = [start_Time]
                        for a in t_list:
                            n_t = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                            ti_1 = timeUtils.ssTtimes(n_t)
                            if ti_1 > end_Time:
                                break
                            else:
                                t_list.append(ti_1)
                        data['time'] = t_list
                    if lang == 'en':
                        data['value'].append({'name1': descr[indx_n], 'name2': n,
                                              'value': data_dci_['value']})
                    else:
                        data['value'].append({'name1': n, 'name2': descr[indx_n],
                                              'value': data_dci_['value']})
        elif table_ty == '2':
            t_list = [start_Time]
            for a in t_list:
                n = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                ti_1 = timeUtils.ssTtimes(n)
                if ti_1 > end_Time:
                    break
                else:
                    t_list.append(ti_1)
            for n2 in names:
                indx_n = names.index(n2)
                value_ = []
                if db == 'guizhou':
                    d = n2.split('.')[0][-2]
                elif db == 'houma':
                    d = n2.split('.')[0][-2:]
                elif db == 'ygqn':
                    if 'ygqn.d' in n2:
                        d = 2
                    else:
                        d = 1
                elif db == 'shgyu':
                    d = 1
                else:
                    d = n2.split('.')[0][-1]
                db_con_s = self._return_db_con_pcs(db, d)  # 获取具体数据库链接
                descr2 = self._select_get_his_value_5(db_con_s, table_s, n2, st, ed,
                                                      jiange=0, vmax=10)  # 有负数，所以不控制最小值
                self.status_max(db, descr2, start_Time, t_list, value_)
                if lang == 'en':
                    data['value'].append(
                        {'name': descr[indx_n], 'name2': n2, 'value': value_})
                else:
                    data['value'].append(
                        {'name': n2, 'name2': descr[indx_n], 'value': value_})
            data['time'] = t_list
        db_con_ms.close()


    def _select_get_his_value_new_optimize(self,db_con, table, names, st, ed,jiange=None):
        '''获取历史数据'''
        res = {}
        HisTable_m = HisACDMS(table)
        try:
            values = db_con.query(HisTable_m.value, HisTable_m.dts_s, HisTable_m.name).filter(HisTable_m.name.in_(names),
                                                                             HisTable_m.dts_s.between(st, ed)
                                                                            ).order_by(
                HisTable_m.dts_s.asc()).all()  # 查询当前月
            for val in values:
                data_time = val[1] if isinstance(val[1], datetime.datetime) else val[1]
                if res.get(val[2]):
                    res[val[2]]['time'].append(data_time)
                    res[val[2]]['value'].append(val[0])

                else:
                    res[val[2]] = {
                        'time': [data_time],
                        'value': [val[0]]
                    }
        except:
            values = []
        return_res = {}
        if jiange != 0:
            for name, data in res.items():
                if data:  # 有值
                    data['time'].insert(0, st)
                    data['value'].insert(0, data['value'][0])
                    data['time'].append(ed)
                    data['value'].append(data['value'][-1])
                    df = pd.DataFrame(data)
                    df = df.drop_duplicates(subset=["time"], keep="first")
                    # 转换回字典
                    data["time"] = df["time"].tolist()
                    data["value"] = df["value"].tolist()
                    data=complete_data(data, jiange)
                    return_res[name] = data

        return return_res

    def find_data_new(self, data, data_item, db, db_con_ms, descr, ed, end_Time, lang, names, st, start_Time, table_m,
                  table_s, table_ty):
        """
        太仓鑫融历史数据查询
        data:  {'time':[],'value':[]}
        data_item:  数据项
        db: 电站
        db_con_ms: 数据库链接
        descr: name描述
        ed: 结束时间
        end_Time: 结束时间
        lang: 中英文切换
        names: name集合
        st: 开始时间
        start_Time：开始时间
        table_m: 测量量数据表
        table_s: 状态栏数据表
        table_ty: 1：测量量；2：状态量
        """
        if table_ty == '1':  # 测量量
            res = self._select_get_his_value_new_optimize(db_con_ms, table_m, names,
                                                              st, ed, jiange='15T')
            for n in names:
                indx_n = names.index(n)
                # time_table = []
                # value_ = []

                descr2 = res.get(n)
                if descr2:
                    if descr2['time'] != []:
                        if data['time'] == []:
                            data['time'] = descr2['time']
                    else:
                        t_list = [start_Time]
                        for a in t_list:
                            n_t = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                            ti_1 = timeUtils.ssTtimes(n_t)
                            if ti_1 > end_Time:
                                break
                            else:
                                t_list.append(ti_1)
                        data['time'] = t_list
                    if lang == 'en':
                        data['value'].append({'name1': descr[indx_n], 'name2': n,
                                              'value': descr2['value']})
                    else:
                        data['value'].append({'name1': n, 'name2': descr[indx_n],
                                              'value': descr2['value']})

        elif table_ty == '2':
            t_list = [start_Time]
            for a in t_list:
                n = timeUtils.timeStrToTamp(a) + 900  # 时间转时间戳
                ti_1 = timeUtils.ssTtimes(n)
                if ti_1 > end_Time:
                    break
                else:
                    t_list.append(ti_1)

            res = self._select_get_his_value_new_optimize(db_con_ms, table_s, names,
                                                          st, ed, jiange='15T')
            for n2 in names:
                indx_n = names.index(n2)

                descr2 = res.get(n2)
                # self.status_max(db, descr2, start_Time, t_list, value_)
                if descr2:
                    if lang == 'en':
                        data['value'].append(
                            {'name': descr[indx_n], 'name2': n2, 'value': descr2['value']})
                    else:
                        data['value'].append(
                            {'name': n2, 'name2': descr[indx_n], 'value': descr2['value']})
            data['time'] = t_list
        db_con_ms.close()

    def status_max(self,db, descr2, start_Time, t_list, value_):
        '''状态量最大值'''
        if descr2['value'] != []:
            value_s = [[], []]
            for tt in t_list:
                indx_tt = t_list.index(tt)
                if tt == start_Time:
                    if descr2['value'][indx_tt] > 1:
                        value_.append(1)
                        value_s[0].append(1)
                    else:
                        value_.append(0)
                        value_s[0].append(0)
                elif indx_tt == 0:
                    continue
                for e in descr2['time']:
                    indx = descr2['time'].index(e)
                    if indx == 0:
                        continue
                    if t_list[indx_tt - 1] < e < tt:
                        value_s[1].append(descr2['value'][indx])
                    elif e >= tt:
                        if value_s[1] == []:
                            value_s[1].append(value_s[0][-1])
                        max_ = max(value_s[1])
                        if db == 'dongmu':
                            if max_ > 0:
                                value_.append(1)
                            else:
                                value_.append(0)
                        else:
                            if max_ > 1:
                                value_.append(1)
                            else:
                                value_.append(0)
                            value_s[0] = value_s[1]
                            value_s[1] = []
                        break
    def rount_range(self,descr2, start_Time, t_list):
        '''计算极差'''
        value_ = []
        if descr2['value'] != []:
            for tt in t_list:
                indx_tt = t_list.index(tt)
                value_s = [[], []]
                if tt == start_Time:
                    value_s[0].append(descr2['value'][indx_tt])
                    max_ = max(value_s[0])
                    min_ = min(value_s[0])
                    value_.append(max_ - min_)
                if indx_tt == 0:
                    continue
                for e in descr2['time']:
                    indx = descr2['time'].index(e)
                    if indx == 0:
                        continue
                    if t_list[indx_tt - 1] < e < tt:
                        value_s[1].append(descr2['value'][indx])
                    elif e >= tt:
                        if value_s[1] == []:
                            value_s[1].append(value_s[0][-1])
                        max_ = max(value_s[1])
                        min_ = min(value_s[1])
                        value_.append(max_ - min_)
                        value_s[0] = value_s[1]
                        value_s[1] = []
                        break
        return value_
    def count_min(self,descr2, start_Time, t_list):
        '''最小值'''
        value_ = []
        if descr2['value'] != []:
            for tt in t_list:
                indx_tt = t_list.index(tt)
                value_s = [[], []]
                if tt == start_Time:
                    value_.append(descr2['value'][indx_tt])
                    value_s[0].append(descr2['value'][indx_tt])
                if indx_tt == 0:
                    continue
                for e in descr2['time']:
                    indx = descr2['time'].index(e)
                    if indx == 0:
                        continue
                    if t_list[indx_tt - 1] < e < tt:
                        value_s[1].append(descr2['value'][indx])
                    elif e >= tt:
                        if value_s[1] == []:
                            value_s[1].append(value_s[0][-1])
                        min_ = min(value_s[1])
                        value_.append(min_)
                        value_s[0] = value_s[1]
                        value_s[1] = []
                        break
        return value_
    def count_max(self,descr2, start_Time, t_list):
        '''最大值'''
        value_ = []
        if descr2['value'] != []:
            for tt in t_list:
                indx_tt = t_list.index(tt)
                value_s = [[], []]
                if tt == start_Time:
                    value_.append(descr2['value'][indx_tt])
                    value_s[0].append(descr2['value'][indx_tt])
                if indx_tt == 0:
                    continue
                for e in descr2['time']:
                    indx = descr2['time'].index(e)
                    if indx == 0:
                        continue
                    if t_list[indx_tt - 1] < e < tt:
                        value_s[1].append(descr2['value'][indx])
                    elif e >= tt:
                        if value_s[1] == []:
                            value_s[1].append(value_s[0][-1])
                        max_ = max(value_s[1])
                        value_.append(max_)
                        value_s[0] = value_s[1]
                        value_s[1] = []
                        break
        return value_
    def count_avg(self,descr2, start_Time, t_list):
        '''平均值'''
        value_ = []
        if descr2['value'] != []:
            for tt in t_list:
                indx_tt = t_list.index(tt)
                value_s = [[], []]
                if tt == start_Time:
                    value_.append(descr2['value'][indx_tt])
                    value_s[0].append(descr2['value'][indx_tt])
                if indx_tt == 0:
                    continue
                for e in descr2['time']:
                    indx = descr2['time'].index(e)
                    if indx == 0:
                        continue
                    if t_list[indx_tt - 1] < e < tt:
                        value_s[1].append(descr2['value'][indx])
                    elif e >= tt:
                        if value_s[1] == []:
                            value_s[1].append(value_s[0][-1])
                        avg_ = round(numpy.average(value_s[1]), 2)
                        value_.append(avg_)
                        value_s[0] = value_s[1]
                        value_s[1] = []
                        break
        return value_
    def no_count(self,descr2):
        '''无聚合'''
        value_ = descr2['value']
        time_ = descr2['time']
        return time_, value_
    def table_ty_filter(self,filter, table_ty, frequently, filter2, find):
        '''表格查询条件添加'''
        if table_ty == '1':
            filter.append(DataItem.table_ == 'measure')
            if frequently:
                filter2.append(DataItem.frequently == '1')
                filter2.append(DataItem.table_ == 'measure')
            if find:
                filter.append(DataItem.name.like('%' + find + '%'))
        elif table_ty == '2':
            filter.append(DataItem.table_ == 'status')
            if frequently:
                filter2.append(DataItem.frequently == '1')
                filter2.append(DataItem.table_ == 'status')
            if find:
                filter.append(DataItem.name.like('%' + find + '%'))

    def _return_db_con_sda(self,db, d):
        '''返回sda数据库链接'''
        if db == 'taicang' or db == 'dongmu' or db == 'halun':
            conn = db_[db][0][0]
        else:
            conn = db_[db][0][d]
        return conn
    def _return_db_con_PB(self,db, d):
        '''返回PCS数据库链接'''
        conn = db_[db][1][d]
        return conn
    def _return_db_con_pcs(self,db, d):
        '''返回数据库链接'''
        conn = 0
        if db == 'his':
            conn = db_[db][1][0]
        else:
            if db == 'dongmu' or db == 'taicang' or db == 'halun':
                conn = db_[db][1][0]
            elif db == 'houma':
                if d == 'A1':
                    conn = db_[db][1][0]
                elif d == 'A2':
                    conn = db_[db][1][1]
                elif d == 'B1':
                    conn = db_[db][1][2]
                elif d == 'B2':
                    conn = db_[db][1][3]
            elif db == 'taicgxr':
                conn = db_[db][0][0]
            elif db in ['tczj', 'sikly']:
                conn = db_[db][0][d]
            else:
                conn = db_[db][1][int(d) - 1]
        return conn

    def _select_get_his_value_22(self,db_con, table, name, st, ed,jiange=None,vmax=65,minV=0.1):
        '''获取历史数据'''
        # db_con.commit()
        data = {'time': [], 'value': []}
        HisTable_m = HisACDMS(table)
        try:
            values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                             HisTable_m.dts_s.between(st, ed),
                                                                             HisTable_m.value.between(minV,vmax)).order_by(
                HisTable_m.dts_s.asc()).all()  # 查询当前月
            for val in values:
                if isinstance(val[1], datetime.datetime):
                    data['time'].append(val[1].strftime("%Y-%m-%d %H:%M:%S"))
                else:
                    data['time'].append(val[1])
                data['value'].append(val[0])
        except:
            values = []
        if jiange!=0:
            if data['time']:  # 有值
                data['time'].insert(0, st)
                data['value'].insert(0, data['value'][0])
            else:
                return data

            data['time'].append(ed)
            data['value'].append(data['value'][-1])
            df = pd.DataFrame(data)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data["time"] = df["time"].tolist()
            data["value"] = df["value"].tolist()
            data=complete_data(data, jiange)
        return data


    def _select_get_his_value_new(self,db_con, table, name, st, ed,jiange=None):
        '''获取历史数据'''
        # db_con.commit()
        data = {'time': [], 'value': []}
        HisTable_m = HisACDMS(table)
        try:
            values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                             HisTable_m.dts_s.between(st, ed)
                                                                            ).order_by(
                HisTable_m.dts_s.asc()).all()  # 查询当前月
            for val in values:
                if isinstance(val[1], datetime.datetime):
                    data['time'].append(val[1].strftime("%Y-%m-%d %H:%M:%S"))
                else:
                    data['time'].append(val[1])
                data['value'].append(val[0])
        except:
            values = []
        if jiange!=0:
            if data['time']:  # 有值
                data['time'].insert(0, st)
                data['value'].insert(0, data['value'][0])
            else:
                return data

            data['time'].append(ed)
            data['value'].append(data['value'][-1])
            df = pd.DataFrame(data)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data["time"] = df["time"].tolist()
            data["value"] = df["value"].tolist()
            data=complete_data(data, jiange)
        return data

    def _select_get_his_value_5(self,db_con, table, name, st, ed,jiange = None,vmax=65):
        '''获取历史数据'''
        # db_con.commit()
        data = {'time': [], 'value': []}
        HisTable_m = HisACDMS(table)
        values = db_con.query(HisTable_m.value, HisTable_m.dts_s).filter(HisTable_m.name == name,
                                                                         HisTable_m.dts_s.between(st, ed),
                                                                         HisTable_m.value < vmax).order_by(
            HisTable_m.dts_s.asc()).all()  # 查询当前月
        for val in values:
            if isinstance(val[1], datetime.datetime):
                data['time'].append(val[1].strftime("%Y-%m-%d %H:%M:%S"))
            else:
                data['time'].append(val[1])
            data['value'].append(val[0])

        if jiange != 0:
            if data['time']:  # 有值
                data['time'].insert(0, st)
                data['value'].insert(0, data['value'][0])
            else:
                return data

            data['time'].append(ed)
            data['value'].append(data['value'][-1])
            df = pd.DataFrame(data)
            df = df.drop_duplicates(subset=["time"], keep="first")
            # 转换回字典
            data["time"] = df["time"].tolist()
            data["value"] = df["value"].tolist()
            data = complete_data(data, jiange)
        return data
