from spyne import Application
from spyne import rpc
from spyne import ServiceBase
from spyne import Integer, String, Unicode, Iterable
from spyne.protocol.soap import Soap11
from spyne.server.wsgi import WsgiApplication


class HelloWorldService(ServiceBase):

    @rpc(Unicode, Integer, _returns=Iterable(Unicode))
    def say_hello(self, name, times):

        for i in range(times):
            yield u'Hello, %s' % name


# step2: Glue the service definition, input and output protocols
soap_app = Application([HelloWorldService], 'kk',
                       in_protocol=Soap11(validator='lxml'),
                       out_protocol=Soap11()
                       )


# step3: Wrap the Spyne application with its wsgi wrapper
wsgi_app = WsgiApplication(soap_app)

if __name__ == '__main__':
    import logging

    from wsgiref.simple_server import make_server

    # configure the python logger to show debugging output
    logging.basicConfig(level=logging.DEBUG)
    logging.getLogger('spyne.protocol.xml').setLevel(logging.DEBUG)

    logging.info("listening to http://127.0.0.1:8000")
    logging.info("wsdl is at: http://localhost:8000/wsdl")

    # step4:Deploying the service using Soap via Wsgi
    # register the WSGI application as the handler to the wsgi server, and run the http server
    server = make_server('127.0.0.1', 8089, wsgi_app)
    server.serve_forever()
