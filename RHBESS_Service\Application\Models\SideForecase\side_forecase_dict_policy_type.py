#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-05-30 17:42:37
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_policy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-30 17:50:36



from Tools.DB.mysql_user import user_Base,user_session
from Application.Models.SideForecase.side_forecase_policy_type import ForecasePolicyType
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean,Text
class ForecaseDicPolicyType(user_Base):
    u'政策箱包中心标签字典表'
    __tablename__ = "t_side_forecase_dic_police_type"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    name = Column(String(256), nullable=False, comment=u"类型名称")
    ty = Column(Integer, nullable=False, comment=u"标签类型")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()

    def __repr__(self):
        return "{'id':%s,'name':'%s','is_use':'%s','ty':%s}" %(self.id,self.name,self.is_use,self.ty)

   