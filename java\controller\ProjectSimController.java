package com.robestec.dailyproduce.project.controller;

import com.central.common.model.PageResult;
import com.central.common.model.Result;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimCreateDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimQueryDTO;
import com.robestec.dailyproduce.project.dto.sim.ProjectSimUpdateDTO;
import com.robestec.dailyproduce.project.service.ProjectSimService;
import com.robestec.dailyproduce.project.vo.ProjectSimVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sims")
@RequiredArgsConstructor
@Api(tags = "5G专用卡信息管理API")
public class ProjectSimController {

    private final ProjectSimService simService;

    @GetMapping
    @ApiOperation("分页查询5G专用卡")
    public PageResult<ProjectSimVO> querySims(@Validated ProjectSimQueryDTO queryDTO) {
        return simService.querySims(queryDTO);
    }

    @PostMapping
    @ApiOperation("新增5G专用卡")
    public Result<Long> createSim(@Validated @RequestBody ProjectSimCreateDTO createDTO) {
        return Result.succeed(simService.createSim(createDTO));
    }

    @PostMapping("/add")
    @ApiOperation("批量新增5G专用卡")
    public Result createSim(@Validated @RequestBody List<ProjectSimCreateDTO> createDTOList) {
        simService.createSimList(createDTOList);
        return Result.succeed();
    }

    @PutMapping("/{id}")
    @ApiOperation("修改5G专用卡信息")
    public Result updateSim(@PathVariable Long id, @Validated @RequestBody ProjectSimUpdateDTO updateDTO) {
        updateDTO.setId(id);
        simService.updateSim(updateDTO);
        return Result.succeed();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除5G专用卡")
    public Result deleteSim(@PathVariable Long id) {
        simService.deleteSim(id);
        return Result.succeed();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取5G专用卡详情")
    public Result<ProjectSimVO> getSim(@PathVariable Long id) {
        return Result.succeed(simService.getSim(id));
    }
}
