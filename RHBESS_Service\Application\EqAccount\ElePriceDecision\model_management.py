import copy
import io
import os
import time
import json
import logging
import pandas as pd
import tornado.web
import datetime
from sqlalchemy import func
from pathlib import Path
from Application.Models.ElePriceDescision.models import TModel, ModelFile, ModelUser, TUser, TRoleAuthority, CProvince, ApplicationMarket, TMainBody, TMainBodyUser, TUsersRole
from Application.Models.base_handler_ele import BaseHandler
from Tools.DecisionDB.ele_base import get_user_session
# from Tools.DecisionDB.ele_base import user_session
from Tools.Utils.mimio_tool import MinioTool
from Application.Models.ElePriceDescision.models import CDictSery, RSeriesDataShanxi
from openpyxl import Workbook
import re



class ModelManagement(BaseHandler):
    ''' 模型管理 '''

    def permission_required(self, permissions_id, user_session):
        """
        登录用户权限校验
        :param permissions_id:
        :return:
        """

        if not permissions_id:
            return 0
        session = self.getOrNewSession()
        user_id = session.user.get('id')
        role_id = user_session.query(TUser).filter(TUser.id == user_id).first().role_id
        permissions = user_session.query(TRoleAuthority).filter(TRoleAuthority.role_id == role_id,
                                                                TRoleAuthority.authority_id == permissions_id).first()

        if permissions:
            return role_id
        else:
            return 0

    @tornado.web.authenticated
    def get(self, kt):
        with get_user_session() as user_session:
            self.refreshSession()
            if kt == 'ModelsList':
                """查询列表"""
                pageNum = int(self.get_argument('pageNum', default='1'))
                pageSize = int(self.get_argument('pageSize', default='10'))
                name = self.get_argument('name')
                province_id = self.get_argument('province_id')
                application_market_id = self.get_argument('application_market_id')
                user_id = self.get_argument('user_id')
                filter = [TModel.is_use == 1]
                session = self.getOrNewSession()
                _id = session.user.get('id')
                try:
                    role_id = user_session.query(TUser).filter(TUser.id == _id).first().role_id
                    if role_id != 1:
                        model_users = user_session.query(ModelUser).filter(ModelUser.user_id == _id).all()
                        model_ids = [i.model_id for i in model_users]
                        filter.append(TModel.id.in_(model_ids))
                    if user_id:
                        filter.append(TModel.create_user == int(user_id))
                    if province_id:
                        filter.append(TModel.province_id == int(province_id))
                    if application_market_id:
                        filter.append(TModel.application_market_id == int(application_market_id))
                    if name:
                        filter.append(TModel.name.like('%' + name + '%'))
                    res = user_session.query(TModel).filter(*filter).order_by(
                        TModel.op_ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize)
                except Exception as e:
                    logging.error(e)
                    return self.customError('查询数据失败！')
                finally:
                    user_session.close()

                data = []
                for i in res:
                    model_user = user_session.query(ModelUser.user_id).filter(ModelUser.model_id == i.id).all()
                    data.append(
                        {
                            'id': i.id,
                            'name': i.name,
                            'status': i.status,
                            'accuracy_rate': i.accuracy_rate if i.accuracy_rate else 0,
                            'province_name': i.province.name,
                            'province_id': i.province.id,
                            'application_market_name': i.application_market.name,
                            'create_user_id': i.create_user,
                            'create_user_name': i.user.name,
                            'assigned_user': [u[0] for u in model_user]
                        }
                    )
                total = user_session.query(func.count(TModel.id)).filter(*filter).scalar()
                return self.returnTotalSuc(data, total)

            if kt == 'Mappings':
                '''省份、应用市场、人员字典'''
                try:
                    province_res = user_session.query(CProvince).filter(CProvince.id!=1).all()
                    application_market_res = user_session.query(ApplicationMarket).all()
                    user_res = user_session.query(TUser).filter(TUser.is_use==1).all()
                except Exception as e:
                    logging.error(e)
                    return self.customError('查询数据失败！')
                finally:
                    user_session.close()
                data = {}
                data['province'] = {i.name: i.id for i in province_res}
                data['application_market'] = {i.name: i.id for i in application_market_res}
                data['user'] = {i.name: i.id for i in user_res}

                return self.returnTypeSuc(data)

            if kt == 'GetPriceFile':
                """获取电价模板"""
                minioClient = MinioTool()
                url = minioClient.get_download_url('rhyc', '山西现货市场电价上传模板.xlsx')
                data = {
                    '山西现货市场电价上传模板.xlsx': url
                }
                return self.returnTypeSuc(data)

    @tornado.web.authenticated
    def post(self, kt):
        with get_user_session() as user_session:
            self.refreshSession()
            if kt == 'AddModel':
                """新增预测模型"""
                name = self.get_argument('name')
                province_id = self.get_argument('province_id')
                application_market_id = self.get_argument('application_market_id')
                if not all([name, province_id, application_market_id]):
                    return self.customError('参数填写不完整！')
                session = self.getOrNewSession()
                try:
                    model = user_session.query(TModel).filter(TModel.is_use == 1, TModel.name == name).first()
                    if model:
                        return self.customError('模型名称不允许重复！')
                    model_ = TModel(name=name, province_id=int(province_id), application_market_id=int(application_market_id),
                                    create_user=session.user.get('id'), op_ts=datetime.datetime.now())
                    user_session.add(model_)
                    user_session.commit()
                    model_id = user_session.query(TModel).filter(TModel.is_use == 1, TModel.name == name).first().id
                    model_user = ModelUser(user_id=session.user.get('id'), model_id=model_id)
                    user_session.add(model_user)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('新增失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')

            if kt == 'AssignedUser':
                """模型分配-可见人"""
                user_ids = self.get_argument('ids')
                _id = self.get_argument('id')
                session = self.getOrNewSession()
                user_id = session.user.get('id')
                permissions_id = 7  # 测算模型可见人权限ID
                role_id = self.permission_required(permissions_id, user_session)
                if not role_id:
                    return self.customError('当前用户没有该功能权限!')
                try:
                    user_session.query(ModelUser).filter(ModelUser.model_id == int(_id)).delete()
                    model_user_list = []
                    user_ids = user_ids.split(',')
                    for i in user_ids:
                        model_user_list.append(ModelUser(user_id=i, model_id=_id))
                    user_session.add_all(model_user_list)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('更新失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')

            if kt == 'FileUpload':
                """电价附件上传"""
                minioClient = MinioTool()
                files = self.request.files
                file = files.get('files')
                data = file[0].get('body')
                file_name = file[0].get('filename').split('.')
                t = str(int(time.time() * 1000))
                file_name = file_name[0] + '-' + t + '.' + file_name[1]
                # 上传二进制数据
                try:
                    binary_stream = io.BytesIO(data)
                    minioClient.minioClient.put_object('rhyc', file_name, binary_stream, len(data))

                    # 返回存储地址
                    storage_url = minioClient.minioClient.presigned_get_object('rhyc', file_name)
                except Exception as e:
                    logging.error(e)
                    return self.customError('上传附件失败！')
                finally:
                    user_session.close()
                data = {
                    'file_name': file_name,
                    'file_path': storage_url
                }
                return self.returnTypeSuc(data)

            if kt == 'EleFileUpload':
                """电价更新"""
                file_name = self.get_argument('file_name')
                file_path = self.get_argument('file_path')
                _id = self.get_argument('id')
                upload_time = self.get_argument('upload_time')
                is_exist = self.get_argument('is_exist')
                province_id = self.get_argument('province_id')
                if not all([file_name, file_path, _id, upload_time, province_id]):
                    return self.customError('参数填写不完整！')

                ids = _id.split(',')
                file_list = []
                file_ = user_session.query(ModelFile).filter(ModelFile.type == 1, ModelFile.model_id.in_(ids),
                                                             ModelFile.op_ts == upload_time, ModelFile.province_id == int(province_id)).first()
                if file_ and is_exist != '1':
                    return self.write({"code": 408, 'msg': '上传日期已有电价，是否覆盖？'})
                else:
                    if file_:
                        file_.is_use = 0
                try:
                    file_res = pd.read_excel(file_path, usecols=lambda x: x.upper() in ['时刻', '电价'])
                    if file_res.empty:
                        return self.customError('上传附件标题错误，正确标题为：时刻、电价')
                    file_res = file_res.values
                    price_data = []
                    # dict_serie_id = user_session.query(CDictSery).filter(CDictSery.name == '现货出清电价信息',
                    #                                                      CDictSery.province_id == province_id,
                    #                                                      CDictSery.is_use == "1", ).first().id
                    # if dict_serie_id == 13:
                    #     res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == upload_time,
                    #                                                        RSeriesDataShanxi.series_id == dict_serie_id,
                    #                                                        RSeriesDataShanxi.name == "总加",
                    #                                                        RSeriesDataShanxi.is_use == "1").order_by(
                    #         RSeriesDataShanxi.op_ts.desc()).all()
                    #
                    # else:
                    #     res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == upload_time,
                    #                                                        RSeriesDataShanxi.series_id == dict_serie_id,
                    #                                                        RSeriesDataShanxi.is_use == "1").order_by(
                    #         RSeriesDataShanxi.op_ts.desc()).all()
                    #
                    # serie_dict = {}
                    # for serie in res:
                    #     if serie_dict.get(serie.moment) or serie_dict.get(serie.moment) == 0:
                    #         continue
                    #     else:
                    #         serie_dict[serie.moment] = serie.value2


                    for i, v in enumerate(file_res):
                        if not isinstance(v[0], str):
                            return self.customError(f'第{i + 2}行-时刻数据错误，请检查！')
                        if not isinstance(v[1], float):
                            return self.customError(f'第{i + 2}行-电价数据错误，请检查！')


                        # accuracy_rate = None  # 时刻准确率
                        # if serie_dict.get(v[0]) and v[1]:
                        #     if serie_dict != 0 and v[1] != 0:
                        #         accuracy_rate = round(abs(float(serie_dict.get(v[0])) / float(v[1]) - 1), 2)

                        price_data.append(
                            {
                                'time': v[0],
                                'value': round(float(v[1]), 2)
                                # 'accuracy_rate': accuracy_rate
                            }
                        )
                except Exception as e:
                    logging.error(e)
                    return self.customError('excel解析失败！')
                for _id in ids:
                    file = ModelFile(model_id=_id, name=file_name, file_path=file_path, type=1,
                                     price_data=json.dumps(price_data), op_ts=upload_time, province_id=province_id,
                                     create_time=datetime.datetime.now())

                    file_list.append(file)
                try:
                    user_session.add_all(file_list)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('更新电价失败！')
                finally:
                    user_session.close()

                return self.returnTypeSuc('')

            if kt == 'StatusUpdate':
                """模型状态"""
                _id = self.get_argument('id')
                status = self.get_argument('status')
                if not all([_id, status]):
                    return self.customError('参数填写不完整！')
                session = self.getOrNewSession()
                user_id = session.user.get('id')

                permissions_id = 4  # 测算模型状态修改权限ID
                role_id = self.permission_required(permissions_id, user_session)
                if not role_id:
                    return self.customError('当前用户没有该功能权限!')
                try:
                    # if role_id != 1:
                    #     user_session.query(TModel).filter(TModel.id == int(_id), TModel.create_user == user_id).update(
                    #         {'status': int(status)})
                    # else:
                    # 产品需求变更：允许所有人操作
                    user_session.query(TModel).filter(TModel.id == int(_id)).update(
                        {'status': int(status)})
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('更新状态失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')


            if kt == 'StatusDelete':
                """删除模型"""
                _id = self.get_argument('id')
                if not _id:
                    return self.customError('参数填写不完整！')
                session = self.getOrNewSession()
                user_id = session.user.get('id')

                permissions_id = 6  # 测算模型状态删除权限ID
                role_id = self.permission_required(permissions_id, user_session)
                if not role_id:
                    return self.customError('当前用户没有该功能权限!')
                try:
                    if role_id != 1:
                        user_session.query(TModel).filter(TModel.id == int(_id), TModel.create_user == user_id).update(
                            {'is_use': 0})
                    else:
                        user_session.query(TModel).filter(TModel.id == int(_id)).update(
                            {'is_use': 0})

                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('删除失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')

            if kt == 'AttachmentDownload':
                """附件下载"""
                _id = self.get_argument('id')
                _type = self.get_argument('type')  # 1：电价；2：预测模型；决策模型
                upload_time = self.get_argument('upload_time')
                if not all([_id, _type, upload_time]):
                    return self.customError('参数填写不完整！')
                # permissions_id = 8  # 测算模型状导出附件权限ID
                # role_id = self.permission_required(permissions_id)
                # if not role_id:
                #     return self.customError('当前用户没有该功能权限!')
                try:
                    file = user_session.query(ModelFile).filter(ModelFile.model_id == int(_id), ModelFile.op_ts == upload_time,
                                                                ModelFile.type == int(_type), ModelFile.is_use == 1).first()
                    if file:
                        data = {
                            'file_path': file.file_path,
                            'file_name': file.name
                        }
                        return self.returnTypeSuc(data)
                    else:
                        return self.customError('该日期下没有附件！')

                except Exception as e:
                    logging.error(e)
                    return self.customError('附件下载失败！')
                finally:
                    user_session.close()

class MainBodyViews(BaseHandler):
    """主体管理"""

    @tornado.web.authenticated
    def get(self, kt):
        with get_user_session() as user_session:
            self.refreshSession()
            if kt == 'Getlist':
                """查询列表"""
                pageNum = int(self.get_argument('pageNum', default='1'))
                pageSize = int(self.get_argument('pageSize', default='10'))
                name = self.get_argument('name', None)
                session = self.getOrNewSession()
                _id = session.user.get('id')
                try:
                    # 查询角色ID
                    role_id = user_session.query(TUser).filter(TUser.id == _id).first().role_id
                    # role_ids = user_session.query(TUsersRole).filter(
                    #     TUsersRole.user_id == _id, TUsersRole.is_use == 1).all()
                    # role_id_list = [i.role_id for i in role_ids]
                    filter = [TMainBody.is_use == 1]
                    if 1 != role_id:
                        # 非管理员用户只可查看可见主体
                        users_res = user_session.query(TMainBodyUser).filter(TMainBodyUser.user_id == _id).all()
                        mainbody_ids = [i.mainbody_id for i in users_res]
                        filter.append(TMainBody.id.in_(mainbody_ids))
                    if name:
                        filter.append(TMainBody.name.like('%' + name + '%'))
                    res = user_session.query(TMainBody).filter(*filter).order_by(TMainBody.op_ts.desc()).limit(pageSize).offset((pageNum - 1) * pageSize)

                    data = []
                    for i in res:
                        mainbody_user = user_session.query(TMainBodyUser.user_id).filter(TMainBodyUser.mainbody_id == i.id).all()
                        data.append({
                            'id': i.id,
                            'power': i.power,
                            'capacity': i.capacity,
                            'chag_efficiency': i.chag_efficiency,
                            'disg_efficiency': i.disg_efficiency,
                            'dod': i.dod,
                            'threshold_value': i.threshold_value,
                            'name': i.name,
                            'create_user_name': i.user.name,
                            'create_user_id': i.user.id,
                            'assigned_user':  [u[0] for u in mainbody_user]
                        })
                    total = user_session.query(func.count(TMainBody.id)).filter(*filter).scalar()
                    return self.returnTotalSuc(data, total)
                except Exception as e:
                    logging.error(e)
                    return self.customError('查询数据失败！')
                finally:
                    user_session.close()

    @tornado.web.authenticated
    def post(self, kt):
        with get_user_session() as user_session:
            self.refreshSession()
            if kt == 'AddmainBody':
                """新建主体"""
                name = self.get_argument('name', None)   # 主体名称
                power = self.get_argument('power', None)   # 功率
                capacity = self.get_argument('capacity', None)   # 容量
                chag_efficiency = self.get_argument('chag_efficiency', None)   # 充电效率
                disg_efficiency = self.get_argument('disg_efficiency', None)   # 放电效率
                dod = self.get_argument('dod', None)   # DOD
                threshold_value = self.get_argument('threshold_value', None)  # 充放阈值
                if not all([name, power, capacity, chag_efficiency, disg_efficiency, dod, threshold_value]):
                    return self.customError('参数填写不完整！')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(disg_efficiency)):
                    return self.customError('放电效率请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(chag_efficiency)):
                    return self.customError('充电效率请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(dod)):
                    return self.customError('DOD请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(threshold_value)):
                    return self.customError('充放阈值请输入数字，支持小数点两位')

                if not 100 >= float(disg_efficiency) >= 0:
                    return self.customError('放电效率限值0-100！')
                if not 100 >= float(chag_efficiency) >= 0:
                    return self.customError('充电效率限值0-100！')
                if not 100 >= float(dod) >= 0:
                    return self.customError('DOD限值0-100！')

                session = self.getOrNewSession()
                try:
                    if user_session.query(TMainBody).filter(TMainBody.name == name, TMainBody.is_use == 1).first():
                        return self.customError('主体名称已存在！')
                    mainbody = TMainBody(name=name, create_user=session.user.get('id'), power=float(power), capacity=float(capacity),
                                       chag_efficiency=float(chag_efficiency), disg_efficiency=float(disg_efficiency), dod=float(dod),
                                       threshold_value=float(threshold_value), op_ts=datetime.datetime.now())
                    user_session.add(mainbody)
                    user_session.commit()
                    mainbody_id = user_session.query(TMainBody).filter(TMainBody.name == name, TMainBody.is_use == 1).first().id
                    mainbody_user = TMainBodyUser(user_id=session.user.get('id'), mainbody_id=mainbody_id)
                    user_session.add(mainbody_user)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('新增失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')

            if kt == "EidtmainBody":
                """编辑主体"""
                _id = self.get_argument('id', None)
                name = self.get_argument('name', None)   # 主体名称
                power = self.get_argument('power', None)   # 功率
                capacity = self.get_argument('capacity', None)   # 容量
                chag_efficiency = self.get_argument('chag_efficiency', None)   # 充电效率
                disg_efficiency = self.get_argument('disg_efficiency', None)   # 放电效率
                dod = self.get_argument('dod', None)   # DOD
                threshold_value = self.get_argument('threshold_value', None)  # 充放阈值
                if not all([name, power, capacity, chag_efficiency, disg_efficiency, dod, threshold_value, _id]):
                    return self.customError('参数填写不完整！')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(disg_efficiency)):
                    return self.customError('放电效率请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(chag_efficiency)):
                    return self.customError('充电效率请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(dod)):
                    return self.customError('DOD请输入数字，支持小数点两位')
                if not re.match(r'^\d+(\.\d{1,2})?$', str(threshold_value)):
                    return self.customError('充放阈值请输入数字，支持小数点两位')

                if not 100 >= float(disg_efficiency) >= 0:
                    return self.customError('充电效率限值0-100！')
                if not 100 >= float(chag_efficiency) >= 0:
                    return self.customError('放电效率限值0-100！')
                if not 100 >= float(dod) >= 0:
                    return self.customError('DOD限值0-100！')
                try:
                    if user_session.query(TMainBody).filter(TMainBody.name == name, TMainBody.is_use == 1, TMainBody.id != _id).first():
                        return self.customError('主体名称已存在！')
                    user_session.query(TMainBody).filter(TMainBody.id == _id).update(
                        {'name': name, 'power': power, 'capacity': capacity, 'chag_efficiency': chag_efficiency,
                         'disg_efficiency': disg_efficiency,  'dod': dod, 'threshold_value': threshold_value,}
                    )
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('更新失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')

            if kt == "DeletemainBody":
                """删除主体"""
                _id = self.get_argument('id', None)
                if not _id:
                    return self.customError('参数填写不完整！')
                session = self.getOrNewSession()
                user_id = session.user.get('id')
                try:
                    # role_id = user_session.query(TUser).filter(TUser.id == user_id).first().role_id
                    role_ids = user_session.query(TUsersRole).filter(
                        TUsersRole.user_id == _id, TUsersRole.is_use == 1).all()
                    role_id_list = [i.role_id for i in role_ids]
                    if 1 not in role_id_list:
                        if user_session.query(TMainBody).filter(TMainBody.id == int(_id), TMainBody.create_user == user_id).first():
                            user_session.query(TMainBody).filter(TMainBody.id == int(_id), TMainBody.create_user == user_id).update(
                                {'is_use': 0})
                        else:
                            return self.customError('当前用户没有删除权限！')
                    else:
                        user_session.query(TMainBody).filter(TMainBody.id == int(_id)).update(
                            {'is_use': 0})
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('删除失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')


            if kt == 'AssignedUser':
                """模型分配-可见人"""
                user_ids = self.get_argument('ids')
                _id = self.get_argument('id')
                session = self.getOrNewSession()
                user_id = session.user.get('id')
                try:
                    user_session.query(TMainBodyUser).filter(TMainBodyUser.mainbody_id == int(_id)).delete()

                    mainbody_user_list = []
                    user_ids = user_ids.split(',')
                    for i in user_ids:
                        mainbody_user_list.append(TMainBodyUser(user_id=i, mainbody_id=_id))
                    user_session.add_all(mainbody_user_list)
                    user_session.commit()
                except Exception as e:
                    logging.error(e)
                    return self.customError('更新失败！')
                finally:
                    user_session.close()
                return self.returnTypeSuc('')


class ForecastViews(BaseHandler):
    """电价预测"""
    @staticmethod
    def create_time_mapping( m=15):
        """
        构造时间字典：默认15分钟间隔
        """
        res = {}
        start_time = datetime.datetime.strptime('00:15', '%H:%M')
        total = 288 if m == 5 else 96  # 5分钟或15分钟
        d = 0
        for i in range(total):
            t = start_time + datetime.timedelta(minutes=i * m)
            t = t.strftime('%H:%M')
            t = t if t != '00:00' else '24:00'
            res[f'{t}'] = d
        return res

    # @staticmethod
    def get_series_data(self, day, province_id=None, table_name=None, dict_serie_id=None):
        user_session = get_user_session()
        # if dict_serie_id == None:
        #     dict_serie_id = user_session.query(CDictSery).filter(CDictSery.name == table_name,
        #                                                          CDictSery.province_id == province_id,
        #                                                          CDictSery.is_use == "1", ).first().id
        # if dict_serie_id == 13:
        #     res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == day,
        #                                                                  RSeriesDataShanxi.series_id == dict_serie_id,
        #                                                                  RSeriesDataShanxi.name == "总加",
        #                                                                  RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.op_ts.desc()).all()
        #
        # else:
        #     res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == day,
        #                                                                  RSeriesDataShanxi.series_id == dict_serie_id,
        #                                                                  RSeriesDataShanxi.is_use == "1").order_by(RSeriesDataShanxi.op_ts.desc()).all()
        dict_serie_ids = user_session.query(CDictSery).filter(CDictSery.name.in_(['日前非市场化机组出力预测','日前新能源负荷预测','日前出清电量','水电处理预测-日前预测', '日前联络计划（加总）',
                                                                                 '日前联络线计划信息（加总）','新能源总实时出力','现货出清电价信息','全省用电负荷预测信息']),
                                                          CDictSery.province_id == province_id,
                                                          CDictSery.is_use == "1", ).all()
        dict_serie_ids = [i.id for i in dict_serie_ids]
        res = user_session.query(RSeriesDataShanxi).filter(RSeriesDataShanxi.day == day,
                                                RSeriesDataShanxi.series_id.in_(dict_serie_ids),
                                                RSeriesDataShanxi.is_use == "1")\
            .order_by(RSeriesDataShanxi.op_ts.desc(), RSeriesDataShanxi.moment).all()
        data_key = {
            '日前非市场化机组出力预测': [],
            '日前新能源负荷预测': [],
            '日前出清电量': [],
            '日前联络线计划信息（加总）': [],
            '新能源总实时出力': [],
            '现货出清电价信息': [],
            '全省用电负荷预测信息': [],
            '实时节点边际电价': [],
            '水电处理预测-日前预测': [],
            '水电出力预测': []
        }
        data = {
            '日前非市场化机组出力预测': [],
            '日前新能源负荷预测': [],
            '日前出清电量': [],
            '日前联络线计划信息（加总）': [],
            '新能源总实时出力': [],
            '现货出清电价信息': [],
            '全省用电负荷预测信息': [],
            '水电处理预测-日前预测': [],
            '实时节点边际电价': [],
            '水电出力预测': []

        }
        for i in res:
            if i.moment not in data_key[i.series.name]:
                data[i.series.name].append(i)
                data_key[i.series.name].append(i.moment)
        return data

    @tornado.web.authenticated
    def get(self, kt):
        with get_user_session() as user_session:
            self.refreshSession()
            if kt == 'GetModelInfo':
                """获取模型下拉框接口"""
                name = self.get_argument('name')
                session = self.getOrNewSession()
                _id = session.user.get('id')
                try:
                    # 只查询山西数据
                    filter = [TModel.is_use == 1, TModel.province_id == 2]
                    role_id = user_session.query(TUser).filter(TUser.id == _id).first().role_id
                    if role_id != 1:
                        model_users = user_session.query(ModelUser).filter(ModelUser.user_id == _id).all()
                        model_ids = [i.model_id for i in model_users]
                        filter.append(TModel.id.in_(model_ids))
                    if name:
                       filter.append(TModel.name.like('%' + name + '%'))

                    res = user_session.query(TModel).filter(*filter).order_by(TModel.id.desc())
                except Exception as e:
                    logging.error(e)
                    return self.customError('查询数据失败！')
                finally:
                    user_session.close()
                data = []
                for i in res:
                    data.append(
                        {
                            i.name: i.id
                        }
                    )
                return self.returnTypeSuc(data)

            if kt == 'GetInfo':
                province_id = self.get_argument('province_id')
                day = self.get_argument('day')
                model_ids = self.get_argument('model_ids')  # 模型ID，多个以英文逗号隔开
                # _type = self.get_argument('type')  # 1：现货电价预测；2：风光数据；3：电网数据
                is_download = self.get_argument('is_download')  # 是否电价导出
                # if not _type:
                #     return self.customError('参数异常，查询类型未填写')

                if not all([province_id, day]):
                    return self.customError('参数填写不完整！')

                try:
                    # 现货电价预测
                    # if _type == '1':
                    data = {}
                    start_time = datetime.datetime.strptime('00:15', '%H:%M')
                    for i in range(96):
                        t = start_time + datetime.timedelta(minutes=i * 15)
                        t = t.strftime('%H:%M')
                        t = t if t != '00:00' else '24:00'
                        data[f'{t}'] = {
                                           'data1': {
                                            '日前出清电价': '--',
                                            '实时出清电价': '--'
                                           },
                                            'data2': {
                                            '日前出清电量': '--',
                                            '竞价机组空间': '--'
                                            },
                                            'data3': {
                                            '实时风电总加': '--',
                                            '实时光伏总加': '--',
                                            '日前风电总加': '--',
                                            '日前光伏总加': '--'
                                            }
                                        }
                    # 日前出清电价
                    res = self.get_series_data(day, province_id, '现货出清电价信息')
                    for d in res.get('现货出清电价信息'):
                        if data.get(d.moment):
                            data[d.moment]['data1']['日前出清电价'] = round(float(d.value1), 2) if d.value1 is not None else '--'
                    # 实时出清电价
                    for d in res.get('现货出清电价信息'):
                        if data.get(d.moment):
                            data[d.moment]['data1']['实时出清电价'] = round(float(d.value2), 2) if d.value2 is not None else '--'
                    # 查询模型上传电价
                    if model_ids:
                        model_ids = model_ids.split(',')
                        models = user_session.query(TModel).filter(TModel.id.in_(model_ids)).all()
                        for model in models:
                            price_res = user_session.query(ModelFile).filter(ModelFile.model_id == model.id, ModelFile.op_ts == day, ModelFile.is_use == 1).first()
                            if price_res:
                                price_data = json.loads(price_res.price_data)
                                price_dict = {p.get('time'): p.get('value') for p in price_data}

                                for _d in data.keys():
                                    if price_dict.get(_d) is not None:
                                        data[_d]['data1'][model.name] = round(price_dict.get(_d), 2) if price_dict.get(_d) is not None else '--'
                                    else:
                                        data[_d]['data1'][model.name] = '--'
                            else:
                                for _d in data.keys():
                                    data[_d]['data1'][model.name] = '--'
                    if is_download and is_download == '1':
                        # 电价导出
                        wb = Workbook()
                        wb.encoding = 'utf-8'  # 定义编码格式
                        st = wb.active  # 获取第一个工作表（sheet1）
                        st.title = '电价预测'
                        title = ['时间']
                        for t in data.get('00:15').get('data1').keys():
                            title.append(t)
                        for i in range(1, len(title) + 1):
                            st.cell(row=1, column=i).value = title[i - 1]
                        for k, v in data.items():
                            max_row = st.max_row + 1
                            st.cell(row=max_row, column=1).value = k
                            _i = 2
                            for _k, _v in v.get('data1').items():
                                st.cell(row=max_row, column=_i).value = _v
                                _i += 1
                        file_name = f"电价预测{datetime.datetime.now().strftime('%Y-%m-%d')}.xlsx"
                        BASE_DIR = Path(__file__).resolve().parent
                        path = os.path.join(BASE_DIR, file_name)
                        # 上传Minio
                        minio_client = MinioTool()
                        wb.save(path)
                        file_path = minio_client.upload_local_file(file_name, path)
                        return self.returnTypeSuc({'file_name': file_name, 'file_path': file_path})
                    # 风光数据

                    for d in res.get('新能源总实时出力'):
                        if data.get(d.moment):
                            data[d.moment]['data3']['实时风电总加'] = round(float(d.value1), 2) if d.value1 is not None else '--'
                            data[d.moment]['data3']['实时光伏总加'] = round(float(d.value2), 2) if d.value2 is not None else '--'

                    # 日前风光数据
                    for d in res.get('日前新能源负荷预测'):
                        if data.get(d.moment):
                            data[d.moment]['data3']['日前风电总加'] = round(float(d.value3), 2) if d.value3 is not None else '--'
                            data[d.moment]['data3']['日前光伏总加'] = round(float(d.value2), 2) if d.value2 is not None else '--'


                    # 日前出清电量
                    for d in res.get('日前出清电量'):
                        if data.get(d.moment):
                            data[d.moment]['data2']['日前出清电量'] = round(float(d.value1), 2) if d.value1 is not None else '--'

                    # 竞价机组空间--：预测发电总出力-日前非市场化机组出力预测-预测新能源总出力+日前联络线计划信息（加总）-----（数据源缺失按0计算）
                    # 机组竞价空间 = 全省用电负荷预测信息 - 水电出力预测 - 日前联络计划（加总） - 日前非市场化机组出力预测 - 日前新能源负荷预测
                    # 预测发电总力
                    data1 = self.create_time_mapping()
                    for d in res.get('全省用电负荷预测信息'):
                        if data1.get(d.moment) == 0:
                            data1[d.moment] = round(float(d.value1), 2) if d.value1 is not None else 0

                    # 水电出力预测
                    data2 = self.create_time_mapping()
                    for d in res.get('水电处理预测-日前预测'):
                        if data2.get(d.moment) == 0:
                            data2[d.moment] = round(float(d.value1), 2) if d.value1 is not None else 0
                    # 日前联络计划（加总）
                    data3 = self.create_time_mapping()
                    for d in res.get('日前联络线计划信息（加总）'):
                        if data3.get(d.moment) == 0:
                            data3[d.moment] = round(float(d.value1), 2) if d.value1 is not None else 0
                    # 日前非市场化机组出力预测
                    data4 = self.create_time_mapping()
                    for d in res.get('日前非市场化机组出力预测'):
                        if data4.get(d.moment) == 0:
                            data4[d.moment] = round(float(d.value1), 2) if d.value1 is not None else 0

                    # 日前新能源负荷预测
                    data5 = self.create_time_mapping()
                    for d in res.get('日前新能源负荷预测'):
                        if data5.get(d.moment) == 0:
                            data5[d.moment] = round(float(d.value1), 2) if d.value1 is not None else 0

                    for k, v in data1.items():
                        data[k]['data2']['竞价机组空间'] = round(data1[k] - data2[k] - data3[k] - data4[k] - data5[k], 2)

                except Exception as e:
                    logging.error(e)
                    return self.customError('查询数据失败！')
                finally:
                    user_session.close()

                return self.returnTypeSuc(data)






