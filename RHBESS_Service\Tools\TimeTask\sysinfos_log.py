#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-06-08 13:16:18
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Tools\TimeTask\sysinfos_log.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-06-08 13:16:51


import os
import logging
from logging.handlers import RotatingFileHandler
log_formatter = logging.Formatter('%(asctime)s %(levelname)s %(funcName)s(%(lineno)d) = %(message)s')
# LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
from Tools.Cfg.get_cnf import work_dir
path = os.path.join(work_dir, 'log/sysinfosave.log')


my_handler = RotatingFileHandler(path, mode='a', maxBytes=30*1024*1024, 
                                 backupCount=10, encoding=None, delay=0)
my_handler.setFormatter(log_formatter)
my_handler.setLevel(logging.INFO)

app_log = logging.getLogger('root')
app_log.setLevel(logging.INFO)
app_log.addHandler(my_handler)

# logging.basicConfig(filename=path, level=logging.INFO, format=LOG_FORMAT)