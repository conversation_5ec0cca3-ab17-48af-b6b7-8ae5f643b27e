#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-06-08 16:43:38
#@FilePath     : \RHBESS_Service\Application\EqAccount\HaLun\sysinfo.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-07-13 11:41:18


import json
import logging

from socket import AI_PASSIVE
import tornado.web
from sqlalchemy import func
from sqlalchemy.sql.expression import desc
from Application.Models.base_handler import BaseHandler
from Application.Models.User.sys_info import SysInfo
from Application.Models.User.sys_info_r import SysInfosR
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Tools.DB.redis_con import r
from Application.Models.User.meter_data_test import MeterDataTest
from Tools.Utils.time_utils import timeUtils

refresh_arr,r_obj = [],{}
class SysInfosIntetface(BaseHandler):
    ''' 
    获取系统监视数据
    '''
    @tornado.web.authenticated
    def get(self, kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文网址
        try:
        # if 1:
            if kt == 'Real':  # 获取当前值
                global r_obj
                all = []
                r_obj = r.hgetall('sysinfos')
                infos = user_session.query(SysInfo).all()
                for info in infos:
                    if int(info.refresh) == 1:  # 只存放需要刷新的变量
                        refresh_arr.append(info.name)
                    v = 0
                    if info.name in r_obj.keys():
                        v = r_obj[info.name]
                    all.append({'name':info.name,'descr':info.descr,'value':v,'refresh':info.refresh,'unit':info.unit})
                return self.returnTypeSuc(all)
            elif kt == 'GetGatherInfo':  # 获取采集基本信息
                if lang=='en':
                    all = user_session.query(MeterDataTest.en_device).filter(MeterDataTest.ntime == 2023070112).first()
                    return self.returnTypeSuc(eval(all[0]))
                else:
                    all = user_session.query(MeterDataTest.device).filter(MeterDataTest.ntime == 2023070112).first()
                    return self.returnTypeSuc(eval(all[0]))

        except Exception as E:
            logging.error(E)
            user_session.rollback()
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        try:
            if kt == 'RealRefresh':  # 实时值刷新
                # global r_obj
                all = {}
                names = self.get_argument('names',[])  # 名称集合
                if DEBUG:
                    logging.info('names:%s'%(names))

                if names:
                    names = eval(names)
                else:
                    names = refresh_arr
                for name in names:
                    if name in r_obj.keys():
                        all[name] = r_obj[name]

                return self.returnTypeSuc(all)
            elif kt == 'HisDatas':  # 系统监控的历史值
                names = self.get_argument('names',[])  # 名称集合
                startTime = self.get_argument('startTime',timeUtils.getAgoTime(1))  # 开始时间 默认1天前
                endTime = self.get_argument('endTime',timeUtils.getNewTimeStr())  # 截止时间

                if DEBUG:
                    logging.info('names:%s,startTime:%s,endTime:%s'%(names,startTime,endTime))
                if not names:
                    return self.customError('参数不完整')

                names = eval(names)
                obj,times = {},[]
                for name in names:
                    ind = names.index(name)
                    all = user_session.query(SysInfosR).filter(SysInfosR.name==name,SysInfosR.op_ts.between(startTime,endTime)).order_by(SysInfosR.op_ts.asc()).all()
                    arr = []
                    for a in all:
                        arr.append(a.value)
                        if ind == 1:
                            times.append(str(a.op_ts))
                    obj[name] = arr
                obj['time'] = times
                return self.returnTypeSuc(obj)
            elif kt == 'GatherInfoDatas':  # 获取采集设备数据
                name = self.get_argument('name',None)  # 采集设备名称
                descr = self.get_argument('descr',None)  # 采集设备中文名
                time_lin = self.get_argument('timeLin',[])  # 时间范围
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                if DEBUG:
                    logging.info('name:%s,descr：%s,time_lin:%s'%(name,descr,time_lin))
                if not name or not time_lin:
                    return self.customError('参数不完整')
                if time_lin:
                    time_lin = eval(time_lin)
                all,total = self._getMeteData(name,descr,time_lin,pageNum,pageSize)
                return self.returnTotalSuc(all,total)
            elif kt == 'DowloadGatherInfoDatas':  # 采集设备数据下载
                name = self.get_argument('name',None)  # 采集设备名称
                descr = self.get_argument('descr',None)  # 采集设备中文名
                time_lin = self.get_argument('timeLin',[])  # 时间范围
                # pageNum = int(self.get_argument('pageNum',1))
                # pageSize = int(self.get_argument('pageSize',20))
                vol = float(self.get_argument('vol',0))  # 电压
                rate = float(self.get_argument('rate',0))  # 倍率

                if DEBUG:
                    logging.info('name:%s,descr：%s,time_lin:%s，vol：%s,rate:%s'%(name,descr,time_lin,vol,rate))
                if not name or not time_lin or not rate or not vol:
                    return self.customError('参数不完整')
                if time_lin:
                    time_lin = eval(time_lin)
                all = self._getMeteData2(name,descr,time_lin,vol,rate)
                return self.returnTypeSuc(all)
            else:
                return self.pathError()
        except Exception as E:
            logging.error(E)
            user_session.rollback()
            return self.requestError()
        finally:
            user_session.close()

    def _getMeteData(self,name,descr,time_lin,pageNum,pageSize):
        all = []
        t = int(name[-1:])  # 截取最后一个字符0代表串口1,1代表串口2
        filter = [MeterDataTest.device==name[:-2],MeterDataTest.op_ts.between(time_lin[0],time_lin[1])]
        total = user_session.query(func.count(MeterDataTest.ntime)).filter(*filter).scalar()
        if t == 1:
            meterData = user_session.query(MeterDataTest.op_ts,MeterDataTest.i2a,MeterDataTest.i2b,MeterDataTest.i2c).filter(*filter).order_by(MeterDataTest.ntime.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
        else:
            meterData = user_session.query(MeterDataTest.op_ts,MeterDataTest.ia,MeterDataTest.ib,MeterDataTest.ic).filter(*filter).order_by(MeterDataTest.ntime.asc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
        for med in meterData:
            tim = str(med[0])
            all.append({"descr":descr,"date":tim[:10],"time":tim[11:16],"ia":med[1],"ib":med[2],"ic":med[3]})
        return all,total
    
    def _getMeteData2(self,name,descr,time_lin,vol,rate):
        all = []
        t = int(name[-1:])  # 截取最后一个字符0代表串口1,1代表串口2
        filter = [MeterDataTest.device==name[:-2],MeterDataTest.op_ts.between(time_lin[0],time_lin[1])]
        if t == 1:
            meterData = user_session.query(MeterDataTest.op_ts,MeterDataTest.i2a,MeterDataTest.i2b,MeterDataTest.i2c).filter(*filter).order_by(MeterDataTest.ntime.asc()).all()
        else:
            meterData = user_session.query(MeterDataTest.op_ts,MeterDataTest.ia,MeterDataTest.ib,MeterDataTest.ic).filter(*filter).order_by(MeterDataTest.ntime.asc()).all()
        # 需要计算功率的 vol和rate同时存在
        for med in meterData:
            tim = str(med[0])
            ia = med[1]
            ib = med[2]
            ic = med[3]
            pa = round(ia*vol*rate,2)
            pb = round(ib*vol*rate,2)
            pc = round(ic*vol*rate,2)
            p = round((pa+pb+pc)/1000,2)
            all.append({"descr":descr,"vol":vol,"rate":rate,"date":tim[:10],"time":tim[11:16],"ia":ia,"ib":ib,"ic":ic,"pa":pa,"pb":pb,"pc":pc,"p":p})
       
        return all
        


