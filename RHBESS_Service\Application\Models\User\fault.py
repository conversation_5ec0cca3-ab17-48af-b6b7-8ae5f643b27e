#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-02-15 10:58:18
#@FilePath     : \RHBESS_Service\Application\Models\User\fault.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-07 11:50:33


#!/usr/bin/env python
# coding=utf-8
#@Information:故障通统计
#<AUTHOR> WYJ
#@Date         : 2023-02-15 10:58:18
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\Models\User\event copy.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-02-15 10:58:20

from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Text
from Application.Models.User.user import User
from Application.Models.User.event_alarm_type import EventAlarmType
from Tools.Utils.time_utils import timeUtils

class Fault(user_Base):
    u'故障统计表'
    __tablename__ = "t_fault"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    fault_name = Column(VARCHAR(256), nullable=False, comment=u"故障设备名称")
    fault_part = Column(VARCHAR(256), nullable=False,comment=u"故障部位")
    fault_problem = Column(Text, nullable=True, comment=u"故障问题")

    happen_time = Column(DateTime, nullable=True,comment=u"发生时间")
    finish_time = Column(VARCHAR(50), nullable=True,comment=u"完成时间")
    repair_way = Column(VARCHAR(256), nullable=True,comment=u"修复方式")
    repair_use = Column(VARCHAR(50), nullable=True,comment=u"修复人，直接存名称")
    repair_plan = Column(VARCHAR(50), nullable=True,comment=u"修复进度，直接存名称")
    repet_flag = Column(VARCHAR(10), nullable=True,comment=u"是否重复，是或否")
    cause = Column(VARCHAR(256), nullable=True, comment=u"原因分析")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    user_id = Column(Integer,nullable=True,comment=u"用户id，记录是谁配置的")
    
    op_ts = Column(DateTime, nullable=False,comment=u"录入时间")
    station = Column(VARCHAR(50), nullable=True,comment=u"所属站")
    remarks = Column(Text, nullable=True, comment=u"备注")

    en_fault_name = Column(VARCHAR(256), nullable=False, comment=u"故障设备名称")
    en_fault_part = Column(VARCHAR(256), nullable=False, comment=u"故障部位")
    en_fault_problem = Column(Text, nullable=True, comment=u"故障问题")
    en_repair_way = Column(VARCHAR(256), nullable=True, comment=u"修复方式")
    en_repair_use = Column(VARCHAR(256), nullable=True, comment=u"修复人，直接存名称")
    en_repair_plan = Column(VARCHAR(50), nullable=True, comment=u"修复进度，直接存名称")
    en_cause = Column(Text,  nullable=True, comment=u"原因分析")
    en_remarks = Column(Text, nullable=True, comment=u"备注")

    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        
        
    def __repr__(self):
        happen_time = str(self.happen_time)
        finish_time = str(self.finish_time)
        bean = "{'id':%s,'name':'%s','part':'%s','problem':'%s','startDay':'%s','startTime':'%s','endDay':'%s','endTime':'%s','repairPlan':'%s','repairPeople':'%s','repair':'%s','repeat':%s,'reason':'%s','op_ts':'%s'," \
               "'happen_time':'%s','finish_time':'%s','remarks':'%s','en_name':'%s','en_part':'%s','en_problem':'%s','en_repair':'%s','en_repairPeople':'%s','en_repairPlan':'%s','en_reason':'%s','en_remarks':'%s'}"\
               % (self.id,self.fault_name,self.fault_part,self.fault_problem,happen_time[:10],happen_time[11:19],finish_time[:10],finish_time[11:19],self.repair_way,
                self.repair_use,self.repair_plan,self.repet_flag,self.cause,self.op_ts,happen_time,finish_time,self.remarks,self.en_fault_name,self.en_fault_part,self.en_fault_problem,self.en_repair_way,self.en_repair_use,
                  self.en_repair_plan,self.en_cause,self.en_remarks)
        return bean.replace("None",'')

    def replace_en_fields(data, new_key_prefix):
        return {new_key_prefix + k[3:]: v for k, v in data.items() if k.startswith("en_")}