import datetime
import decimal
import json
import os
import random
import re
import time
import traceback
import uuid
import numpy
import math
import openpyxl
import requests
from django.conf import settings
from django.db.models import F, Min
from django_redis import get_redis_connection
from rest_framework.views import APIView
from rest_framework.response import Response

from apis.user.models import Unit, TModelConfig
from common.constant import FEEDBACK_EMAIL_RECEIVERS
from common.database_pools import ads_db_tool
from serializers import user_serializers
from encryption import jwt_encryption
from common import common_response_code
from apis.user import models
from middlewares.authentications import (
    JwtParamAuthentication,
    JWTHeaderAuthentication,
    DenyAuthentication,
)
from tools.aly_send_smscode import Sample
from tools.count import mqtt_request_data, mqtt_income, unit_convert, new_get_price, new_get_price_v2, get_station_price
import pandas as pd
from script.day_count_excel import get_station_accu_data
from django.http import FileResponse

from tools.send_mail import sendMail_

success_log = settings.SUCCESS_LOG
error_log = settings.ERROR_LOG
from apis.statistics_apis.db_link import pool


class ExcelDownloadView(APIView):
    def get(self, request):
        data = request.query_params.get("t", None)
        line = int(request.query_params.get("line", 0))
        if not data:
            toady = datetime.date.today()
        else:
            toady = datetime.datetime.strptime(data, "%Y-%m-%d")
        # 站名；app名；站单元个数乘2减1；站中文名，电表负荷倍率
        station_detail_list = (
            ("NBLS001", "TN001", 1, "宁波朗盛001",0.001),
            ("NBLS002", "TN001", 1, "宁波朗盛002",0.001),
            ("LJQC001", "TL001", 1, "中伏能源001",1),
            ("GLTY201", "TC201", 5, "光隆天畅201",1),
            ("QXYJ201", "YJ201", 5, "千能逸嘉201",0.001),
            ("QNKX101", "KX101", 3, "千能康信101",0.001),
            ("QNKX001", "KX001", 1, "千能康信001",0.001),
            ("HZDC101", "DC101", 3, "杭州七章公园101",1),
            ("RHTBA001", "BA001", 1, "融和霆宝安公路001",1),
            ("RHTBA002", "BA002", 1, "融和霆宝安公路002",1),
            ("QNGS201", "GS201", 5, "千能国盛汽车201",1),
            ("QNGS202", "GS202", 5, "千能国盛汽车202",1),
            ("ZXFN301", "ZX301", 7, "中鑫富能",1),
            ("JSGQ001", "GQ001", 1, "江苏光启001",1),
            ("JSBD001", "BD001", 1, "江苏贝得001",1),
            ("JSXQ001", "XQ001", 1, "江苏兴齐001",1),
            ("JSJY001", "JY001", 1, "江苏京奕001",1),
            ("JSJY002", "JY001", 1, "江苏京奕002",1),
            ("JSJY101", "JY101", 3, "江苏京奕101",1),
            ("JSJY102", "JY101", 3, "江苏京奕102",1),
            ("QNKM301", "KM301", 7, "千能凯萌鞋材",1),
            ("NRTYN101", "NR101", 3, "南瑞太阳能",1),
            ("DCLR101", "LR101", 3, "德创-联瑞101",1),
            ("DCLR102", "LR101", 3, "德创-联瑞102",1),
            ("QNKLD001", "LD102", 1, "千能康利得001",1),
            ("QNKLD101", "LD101", 3, "千能康利得101",1),
            ("NFXR001", "NF001", 1, "宁峰-欣荣001",1),
            ("NBLS003", "TN001", 1, "宁波朗盛-和惠001",1),
            ("HFDZ101", "HF101", 3, "千能-宏枫电子101",1),
            ("TJBC001", "TL001", 1, "中伏能源-天津北辰001",1),
            ("JSJB001", "JS001", 1, "江苏聚变-好的001",1),
            ("QNYX001", "YX001", 1, "千能-毅薪001",1),
            ("QNYX002", "YX001", 1, "千能-毅薪002",1),
            ("RHRY101", "RY101", 3, "永康荣亚101",1),
            ("SAMPLE1", "TN002", 1, "常熟工厂001",1),
            ("QNGF101", "GF101", 3, "千能-冠峰101",1),
            ("QNJH001", "JH001", 1, "千能-骏华001",1),
            ("NBJM001", "JM001", 1, "宁波佳明001",1),
            ("NBJD001", "JD001", 1, "朗盛 胶点001",1),
            ("QNYI001", "QN001", 1, "千能 乐伊001",1),
            ("QNZB101", "ZB101", 3, "千能 中邦101",1),
            ("JTJC301", "JT301", 7, "久天建材301",1),
            ("JTJC302", "JT301", 7, "久天建材302",1),
            ("HYRX301", "RX301", 7, "华基投资 荣星301",1),
            ("QNHB001", "HB001", 1, "千能 豪邦001",1),
            ("QNHB101", "HB101", 3, "千能 豪邦101",1),
            ("NFHX001", "HX001", 1, "宁峰海达001",1),
            ("QNXT202", "XT202", 5, "千能鑫泰202",1),
            ("QNDP101", "DP101", 3, "千能鼎派101",1),
            ("SDTR301", "TR301", 7, "山东润泰301",1),
            ("FSHJ101", "HJ101", 3, "佛山和骏101",1),
            ("HZCS001", "CS001", 1, "惠州恒昌盛001",1),
            ("HZCS101", "CS101", 3, "惠州恒昌盛101",1),
            ("DNJK201", "JK201", 5, "电管家-金科201",1),
            ("DZND101", "ZN101", 3, "电管家-人工智能岛101",1),
            ("DZND102", "ZN102", 3, "电管家-人工智能岛102",1),
            ("SGFS201", "SG201", 5, "时光服饰201",1),
            ("CQGS001", "GS001", 1, "重庆高速绿能001",1),
            ("HTHJ101", "HJ101", 3, "浙江海特合金101",1),
            ("YKLC001", "LC001", 1, "永康绿创001",1),
            ("JSSY201", "SY201", 5, "千能金实塑业201",1),
            ("OHJO101", "JO101", 3, "千能瓯海金瓯101",1),
            ("YDDZ101", "DZ101", 3, "千能-亿大锻造101",1),
            ("MRKJ101", "KJ101", 3, "嘉兴明锐科技101",1),
            ("MJKJ201", "KJ201", 5, "千能-明净科技201",1),
            ("FYDX101", "KJ101", 3, "孚尧-大新101",1),
            ("HCGJ101", "GJ101", 3, "江苏华昌工具101",1),
            ("SHCY001", "CY001", 1, "孚尧-上海超煜001",1),
            ("CPZC301", "ZC301", 7, "盛熙-常平纸厂301",1),
            ("JSHZ301", "HZ301", 7, "江苏华智301",1),
            ("DFDR301", "DR301", 7, "东方电热301",1),
            ("DFDR302", "DR302", 7, "东方电热302",1),
            ("DFDR001", "DR001", 1, "东方电热001",1)
            
        )
        toady_tuple = toady.timetuple()
        today_time_stamp = int(time.mktime(toady_tuple))  # 今日凌晨
        yesterday = toady - datetime.timedelta(days=1)
        yesterday_tuple = yesterday.timetuple()
        yesterday_time_stamp = int(time.mktime(yesterday_tuple))  # 前一天凌晨

        start_time = datetime.datetime.combine(toady, datetime.datetime.min.time())
        end_time = datetime.datetime.combine(toady + datetime.timedelta(days=1),
                                             datetime.datetime.max.time()).replace(hour=0, minute=0, second=30,
                                                                                   microsecond=0)
        
        if not line:
            energy_col_dic = {
                "  ": [
                    "宁波朗盛001",
                    "宁波朗盛002",
                    "中伏能源001",
                    "光隆天畅201",
                    "千能逸嘉201",
                    "千能康信101",
                    "千能康信001",
                    "杭州七章公园101",
                    "融和霆宝安公路001",
                    "融和霆宝安公路002",
                    "千能国盛汽车201",
                    "千能国盛汽车202",
                    "中鑫富能301",
                    "江苏光启001",
                    "江苏贝得001",
                    "江苏兴齐001",
                    "江苏京奕001",
                    "江苏京奕002",
                    "江苏京奕101",
                    "江苏京奕102",
                    "千能凯萌鞋材301",
                    "南瑞太阳能101",
                    "德创-联瑞101",
                    "德创-联瑞102",
                    "千能康利得001",
                    "千能康利得101",
                    "宁峰-欣荣001",
                    "宁波朗盛-和惠001",
                    "千能-宏枫电子101",
                    "中伏能源-天津北辰001",
                    "江苏聚变-好的001",
                    "千能-毅薪001",
                    "千能-毅薪002",
                    "永康荣亚101",
                    "常熟工厂001",
                    "千能-冠峰101",
                    "千能-骏华001",
                    "宁波佳明001",
                    "朗盛 胶点001",
                    "千能 乐伊001",
                    "千能 中邦101",
                    "久天建材301",
                    "久天建材302",
                    "华基投资 荣星301",
                    "千能 豪邦001",
                    "千能 豪邦101",
                    "宁峰海达001",
                    "千能鑫泰202",
                    "千能鼎派101",
                    "山东润泰301",
                    "佛山和骏101",
                    "惠州恒昌盛001",
                    "惠州恒昌盛101",
                    "电管家-金科201",
                    "电管家-人工智能岛101",
                    "电管家-人工智能岛102",
                    "时光服饰201",
                    "重庆高速绿能001",
                    "浙江海特合金101",
                    "永康绿创001",
                    "千能金实塑业201",
                    "千能瓯海金瓯101",
                    "千能-亿大锻造101",
                    "嘉兴明锐科技101",
                    "千能-明净科技201",
                    "孚尧-大新101",
                    "江苏华昌工具101",
                    "孚尧-上海超煜001",
                    "盛熙-常平纸厂301",
                    "江苏华智301",
                    "东方电热301",
                    "东方电热302",
                    "东方电热001",
                ],
                "设备": [],
                "负向有功电能/kWh": [],
                "正向有功电能/kWh": [],
            }
            # 网关对应设备
            stationNum_dict = {
                "001": ["BMS"],
                "002": ["BMS"],
                "003": ["BMS"],
                "101": ["BMS1", "BMS2", "汇总"],
                "102": ["BMS1", "BMS2", "汇总"],
                "103": ["BMS1", "BMS2", "汇总"],
                "201": ["BMS1", "BMS2", "BMS3", "汇总"],
                "202": ["BMS1", "BMS2", "BMS3", "汇总"],
                "203": ["BMS1", "BMS2", "BMS3", "汇总"],
                "301": ["BMS1", "BMS2", "BMS3", "BMS4", "汇总"],
                "302": ["BMS1", "BMS2", "BMS3", "BMS4", "汇总"],
                "302": ["BMS1", "BMS2", "BMS3", "BMS4", "汇总"],
                "303": ["BMS1", "BMS2", "BMS3", "BMS4", "汇总"]
            }
            # 根据站名后三位补充设备
            energy_col_dic_new = {
                "  ": [],
                "设备": [],
                "负向有功电能/kWh": [],
                "正向有功电能/kWh": [],
            }

            # for item in energy_col_dic.get("  "):
            #     length = len(stationNum_dict.get(str(item[-3::])))
            #     if length > 1:
            #         for i in range(length):
            #             energy_col_dic_new["  "].append(item)
            #     else:
            #         energy_col_dic_new["  "].append(item)
            #     energy_col_dic_new['设备'].extend(stationNum_dict.get(str(item[-3:])))
            
            # energy_col_dic = energy_col_dic_new
            # http = GetHttpHistory(start_time, end_time, energy_col_dic)
            # for station in station_detail_list:
                # http.get_energy(station[0], station[2])
            # writer = pd.ExcelWriter(
            #     f"{toady.strftime('%Y-%m-%d')}.xlsx",
            # )
            # 调整energy_col_dic数据长度
            # 将数据长度处理一样
            # max_len = max([len(energy_col_dic[key]) for key in energy_col_dic.keys()])
            #
            # for key in energy_col_dic.keys():
            #     if len(energy_col_dic[key]) < max_len:
            #         energy_col_dic[key] += [" "] * int(max_len - len(energy_col_dic[key]))

            # 数据写入excel中
            # pd.DataFrame(energy_col_dic).to_excel(writer, sheet_name="累计充放电量", index=False)
            # print ('energy_col_dic-----------------------',energy_col_dic)
            # 故障
            # for station in station_detail_list:
            #     data_dic = http.alarm_fault(station[1], station[0], station[2])
            #     pd.DataFrame(data_dic).to_excel(writer, sheet_name=f"{station[3]}", index=False)
            # writer.close()

            file_name = f"{toady.strftime('%Y-%m-%d')}.xlsx"
            path = os.path.join(settings.STATICFILES_DIRS[0], file_name)

            workbook = openpyxl.Workbook()

            table_titles = ["电站名称", "设备名称", "负向有功电能/kWh", "正向有功电能/kWh"]
            sheet = workbook.active
            sheet.title = '累计充放电量'
            # sheet1 写入标头
            for col_num, header in enumerate(table_titles, 1):
                col_letter = openpyxl.utils.get_column_letter(col_num)
                sheet[f'{col_letter}1'] = header

            # 准备数据
            data_array = []
            stations = models.StationDetails.objects.filter(is_delete=0).all()
            for station in stations:
                s_list = get_station_accu_data(station, start_time, end_time)
                for ind, ins in enumerate(s_list):
                    li = [ins['station'], ins['device'], ins['accu_charge'], ins['accu_discharge']]
                    data_array.append(li)

            for row_num, row_data in enumerate(data_array, 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    sheet[f'{col_letter}{row_num}'] = cell_value
            workbook.save(path)

            file = open(path, "rb")
            response = FileResponse(file)
            response["Content-Type"] = "application/octet-stream"
            response["Content-Disposition"] = f'attachment;filename="{file_name}"'
            return response
        else:
            # 数据库连接
            conn = pool.connection()
            cursor = conn.cursor()
            writer = pd.ExcelWriter(f"{toady.strftime('%Y-%m-%d')}.xlsx",)
            s,s2 = '',''
            start_time = time.strftime("%Y-%m-%d %H:%M:%S", yesterday_tuple) 
            end_time = time.strftime("%Y-%m-%d %H:%M:%S", toady_tuple) 
            mon_ = int(start_time[5:7])
            if mon_ == 7 or mon_ == 8:
                for i in range(1,25):
                    s = s+',get_json_string(data_info,"$.body.MTC%s") as MTCW%s '%(i,i)
                    s2 = s2+',get_json_string(data_info,"$.body.TP%s") as MTCW%s '%(i,i)
            else:
                for i in range(1,25):
                    s = s+',get_json_string(data_info,"$.body.MTCW%s") as MTCW%s '%(i,i)
                    s2 = s2+',get_json_string(data_info,"$.body.TP%s") as MTCW%s '%(i,i)
            for stations in station_detail_list:
                data_all = {'时间':[],"实际负荷/kW":[],"电表负荷/kW":[],"策略":[],"输出功率/kW":[]}
                station_name = stations[0]
                app_name = stations[1]
                sql = """select time ,get_json_string(data_info,"$.body.PCC") as PCC, get_json_string(data_info,"$.body.BP") as BP, get_json_string(data_info,"$.body.P") as P """
                if station_name == 'NRTYN101': # 南瑞太阳能的单独处理
                    sql = sql +s2
                else:
                    sql = sql +s
                sql = sql + """ FROM device_notify_measure_record WHERE 1=1
                        and app_name='{}'
                        and station_name='{}'
                        and time BETWEEN '{}' AND '{}'
                        ORDER BY time ASC
                        """.format(app_name, station_name, start_time, end_time)
                cursor.execute(sql)
                # 获取查询结果
                result = cursor.fetchall()
                for re in result:
                    time_ = re['time'].strftime("%Y-%m-%d %H:%M:%S")
                    data_all['时间'].append(time_)
                    time_hour = int(time_[11:13])  # 截取整点值
                    if station_name == 'NRTYN101':  # 南瑞太阳能单独处理
                        v1 = float(json.loads(re['MTCW24'])[0])/200
                        if v1<0:
                            va = math.floor(v1)
                        else:
                            va = math.ceil(v1)
                        if time_hour == 0:
                            data_all['策略'].append(va)
                        else:
                            v2 = float(json.loads(re['MTCW%s'%time_hour])[0])/200
                            if v1<0:
                                data_all['策略'].append(math.floor(v2))
                            else:
                                data_all['策略'].append(math.ceil(v2))
                            
                    else:
                        if time_hour == 0:
                            data_all['策略'].append(float(json.loads(re['MTCW24'])[0]))
                        else:
                            data_all['策略'].append(float(json.loads(re['MTCW%s'%time_hour])[0]))
                    data_all['实际负荷/kW'].append(numpy.sum(list(map(float, json.loads(re['PCC'])))))
                    data_all['电表负荷/kW'].append(numpy.sum(list(map(float, json.loads(re['BP']))))*stations[4])
                    data_all['输出功率/kW'].append(numpy.sum(list(map(float, json.loads(re['P'])))))
                pd.DataFrame(data_all).to_excel(writer, sheet_name=f"{stations[3]}", index=False)
            writer.close()
            cursor.close()
            conn.close()
            file = open(f"./{toady.strftime('%Y-%m-%d')}.xlsx", "rb")
            response = FileResponse(file)
            response["Content-Type"] = "application/octet-stream"
            response["Content-Disposition"] = f'attachment;filename="{toady.strftime("%Y-%m-%d")}.xlsx"'
            return response
            

class SendSMSCode(APIView):
    """
    登录页面发送短信接口
    请求方式: post
    :param
    """

    def post(self, request):
        ser = user_serializers.SendMobileMessageSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("短信登录字段传参校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        random_sms_code = random.randint(100000, 999999)
        """目前没有接入第三方发短信程序"""
        code = Sample.main(ser.validated_data["mobile"], random_sms_code)
        if code != 200:
            error_log.error("验证码登录:短信验证码下发失败")
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "短信验证码下发失败"},
                }
            )

        conn = get_redis_connection("default")
        conn.set(ser.validated_data["mobile"], random_sms_code, 60)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}发送短信成功成功",
                },
            }
        )


class RegisterView(APIView):
    """用户注册"""

    def post(self, request):
        try:
            ser = user_serializers.RegisterSerializer(data=request.data)
            if not ser.is_valid():
                return Response(
                    {
                        "code": common_response_code.FIELD_ERROR,
                        "data": {"message": "error", "detail": ser.errors},
                    }
                )
            conn = get_redis_connection("default")
            conn.delete(ser.validated_data["mobile"])  # 删除 redis 中的短信验证码

            ser.validated_data.pop("code")
            ser.validated_data.pop("confirm_password")
            ser.save()
            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": f"用户{ser.validated_data.get('login_name')}注册成功",
                    },
                }
            )
        except Exception as e:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {"message": "error", "detail": "注册失败"},
                }
            )


class SMSCodeLoginView(APIView):
    """短信验证码登录"""""

    def post(self, request):
        ser = user_serializers.SMSCodeLoginSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error(f"传参格式不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        conn = get_redis_connection("default")
        tem_code = conn.get(ser.validated_data['mobile'])
        if not tem_code:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "验证码已过期",
                    },
                }
            )

        conn.delete(ser.validated_data["mobile"])  # 删除 redis 中的短信验证码
        user_instance = models.UserDetails.objects.filter(mobile=ser.validated_data["mobile"]).first()
        if not user_instance:
            error_log.error("手机号验证码登录:用户名或密码错误")
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "用户名或密码错误",
                    },
                }
            )
        if ser.validated_data['code'] == tem_code.decode('utf-8'):
            success_token = jwt_encryption.create_token({"user_id": user_instance.id})

            user_roles = user_instance.roles.all()
            temp_roles = list()
            for user_role in user_roles:
                if user_role.type == int(ser.validated_data['ty']):
                    temp_roles.append(user_role)

            # 查询用户的站信息
            project_ins = models.Project.objects.filter(user=user_instance, is_userd=1).all()
            stations_ins = models.StationDetails.objects.filter(project__user=user_instance,
                                                                project__in=project_ins, is_delete=0).all()
            power_count, power_unit = unit_convert(
                sum([decimal.Decimal(station_ins.rated_power) for station_ins in stations_ins]), "kW")
            capacity_count, capacity_unit = unit_convert(
                sum([decimal.Decimal(station_ins.rated_capacity) for station_ins in stations_ins]), "kWh"
            )
            stations_count_dic = dict()
            if models.Project.objects.filter(user=user_instance, is_used=1).count():
                max_time = models.Project.objects.filter(is_used=1, user=user_instance).aggregate(Min('in_time'))
                run_times = datetime.datetime.now() - max_time["in_time__min"]
                run_days = run_times.days
                stations_count_dic = {
                    "power_count": power_count,
                    "run_days": run_days,
                    "power_unit": power_unit,
                    "capacity_count": capacity_count,
                    "capacity_unit": capacity_unit,
                    "1-1": stations_ins.filter(rated_power=100).count(),
                    "1-2": stations_ins.filter(rated_power=200).count(),
                    "1-3": stations_ins.filter(rated_power=300).count(),
                    "1-4": stations_ins.filter(rated_power=400).count(),
                    "count": stations_ins.count(),
                    "pcs_num": sum([decimal.Decimal(station_ins.battery_cluster) for station_ins in stations_ins]),
                }

            roles = user_instance.roles.values("permissions__title", "permissions__url").distinct()
            role_list = []
            for role in temp_roles:
                role_list.append(
                    {
                        "id": role.id,
                        "role": role.role_name,
                    }
                )

            return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {
                        "message": "success",
                        "detail": f"用户({user_instance.user_name})登录成功",
                        "success_token": success_token,
                        "user_type": user_instance.type,
                        "roles": role_list,
                        "stations": stations_count_dic,
                    },
                }
            )
        else:
            return Response(
                {
                    "code": common_response_code.SUMMARY_CODE,
                    "data": {
                        "message": "验证码错误",
                    },
                }
            )


class SMSPasswordLoginView(APIView):
    """短信密码登录"""

    def post(self, request):
        ser = user_serializers.SMSPasswordLoginSerializer(data=request.data)

        if not ser.is_valid():
            error_log.error("短信密码:字段校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )

        ty = int(request.data.get('ty')) if request.data.get('ty') else 0

        user_instances = models.UserDetails.objects.filter(**ser.data).all()
        if not user_instances.exists():
            error_log.error("手机号或密码不正确")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "手机号或密码不正确"},
                }
            )

        user_instance = user_instances.first()
        user_roles = list(user_instance.roles.all()) if user_instance else []
        permissions = list()

        # 使用列表推导式来简化角色和权限的处理
        # temp_roles = [{"id": role.id, "role": role.role_name} for role in user_roles if role.type == ty]
        permissions.extend({"title": p.title, "url": p.url} for role in user_roles if role.type == ty for p in
                           role.permissions.all() if p.ty == ty)

        new_permissions = []
        temp_list = []
        for p in permissions:
            if p['title'] not in temp_list:
                temp_list.append(p['title'])
                new_permissions.append(p)

        # station_dic = models.StationDetails.objects.filter(userdetails=user_instance[0]).values(
        #     "station_name", "english_name", "address", "province__name", "id"
        # )
        master_stations = models.MaterStation.objects.filter(userdetails=user_instance, is_delete=0).all()
        station_array = []
        if master_stations.exists():
            for master_station in master_stations:
                master_station_dict = {
                    "station_name": master_station.name,
                    "english_name": master_station.english_name,
                    "id": master_station.id
                }
                stations = master_station.stationdetails_set.filter(is_delete=0).all()
                if stations.exists():
                    for station in stations:
                        if station.slave == -1 or station.slave == 0:
                            master_station_dict['address'] = station.address
                            master_station_dict['province__name'] = station.province.name

                station_array.append(master_station_dict)

        success_token = jwt_encryption.create_token({"user_id": user_instance.id})
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"手机号:{ser.validated_data.get('mobile')}密码登录成功",
                    "roles": new_permissions,
                    "success_token": success_token,
                    "station_detail": station_array,
                },
            }
        )


class UsernamePasswordLoginView(APIView):
    """用户名密码登录"""

    def post(self, request):
        ser = user_serializers.UsernamePasswordLoginSerializer(data=request.data)
        if not ser.is_valid():
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        user_instance = models.UserDetails.objects.filter(**ser.data).first()
        if not user_instance:
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": "用户名或密码不正确"},
                }
            )
        success_token = jwt_encryption.create_token({"user_id": user_instance.id})
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": f"用户名:{ser.validated_data.get('login_name')}密码登录成功",
                    "success_token": success_token,
                },
            }
        )


class ChangePasswordByMobilePasswordView(APIView):
    """手机号和密码修改密码"""

    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def post(self, request):
        ser = user_serializers.ChangePasswordByMobilePasswordSerializer(data=request.data)
        if not ser.is_valid():
            error_log.error("修改密码:数据格式校验不通过")
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        user_instance = models.UserDetails.objects.filter(id=request.user["user_id"]).first()  # 验证旧密码
        # if not user_instance:
        #     return Response({
        #         "code": common_response_code.FIELD_ERROR,
        #         "data": {
        #             "message": "error",
        #             "detail": "原密码错误"
        #         }
        #     })
        # new_password = ser.validated_data.pop("new_password")
        # ser.validated_data.pop("password")
        # ser.validated_data.pop("confirm_new_password")
        # ser.save(password=new_password, id=request.user["user_id"])
        user_instance.password = ser.validated_data["new_password"]
        user_instance.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "密码修改成功",
                },
            }
        )


class UserInfoView(APIView):
    authentication_classes = [
        JwtParamAuthentication,
        JWTHeaderAuthentication,
        DenyAuthentication,
    ]  # jwt认证

    def get(self, request):
        user_instance = models.UserDetails.objects.filter(id=request.user["user_id"]).first()
        ser = user_serializers.UserInfoSerializer(instance=user_instance, many=False)
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": ser.data,
                },
            }
        )


class RetrievePasswordView(APIView):
    def post(self, request):
        ser = user_serializers.SMSRetrievePasswordSerializer(data=request.data)
        if not ser.is_valid():
            return Response(
                {
                    "code": common_response_code.FIELD_ERROR,
                    "data": {"message": "error", "detail": ser.errors},
                }
            )
        ser.validated_data.pop("code")
        ser.save()
        return Response(
            {
                "code": common_response_code.SUCCESS,
                "data": {
                    "message": "success",
                    "detail": "密码修改成功",
                },
            }
        )


# class CustomizationView(APIView):
#     """削峰填谷客制化配置"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         provinces = models.ElectricityProvince.objects.all()
#         result = {}
#         for province in provinces:
#             month = datetime.datetime.now().month
#             provinces_ins = province.peakvalley_set.filter(year_month=month).values("type", "level").annotate(province=F("province"))
#             ser = user_serializers.PeakValleySerializer(instance=provinces_ins, many=True)
#             result[province.name] = ser.data
#         return Response({"code": common_response_code.SUCCESS, "data": result})


# class CustomizationAddView(APIView):
#     """削峰填谷客制化添加"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def initial(self, request, *args, **kwargs):
#         super().initial(request, *args, **kwargs)
#         self.serializer_context = {"user_id": request.user["user_id"]}
#         self.serializer_context["station"] = json.loads(request.body.decode()).get("station", None)
#
#     def post(self, request):
#         ser = user_serializers.CustomizationAddSerializer(data=request.data, context=self.serializer_context)
#         if not ser.is_valid():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         stations_list = ser.validated_data.pop("stations_list")
#         uu = uuid.uuid4()
#         for station_id in stations_list:
#             station_ins = models.StationDetails.objects.get(id=station_id)
#             user_id = request.user["user_id"]
#             user_ins = models.UserDetails.objects.get(id=user_id)
#             models.UnitPrice.objects.create(stations=station_ins, user=user_ins, uid=uu, **ser.validated_data)
#         return Response(
#             {
#                 "code": common_response_code.SUCCESS,
#                 "data": {"message": "success", "detail": "削峰填谷客制化电价添加成功"},
#             }
#         )


# class CustomizationGetView(APIView):
#     """削峰填谷客制获取夏季起始月份"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         station = request.query_params.get("station", None)
#         if not station:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "station为必传参数"},
#                 }
#             )
#         station_ins = models.StationDetails.objects.filter(english_name=station, is_delete=0).first()
#         if not station_ins:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "站名不存在"},
#                 }
#             )
#         ins_dic = models.SummerAutomation.objects.filter(stations=station_ins).last()
#         if ins_dic:
#             data = {
#                 "summer_start": ins_dic.summer_start,
#                 "summer_end": ins_dic.summer_end,
#                 "nosummer_start": ins_dic.nosummer_start,
#                 "nosummer_end": ins_dic.nosummer_end,
#                 "stations": station_ins.id,
#             }
#         else:
#             data = {
#                 "summer_start": 6,
#                 "summer_end": 8,
#                 "nosummer_start": 9,
#                 "nosummer_end": 5,
#                 "stations": station_ins.id,
#             }
#
#         return Response({"code": common_response_code.SUCCESS, "data": data})


# class CustomizationDetailView(APIView):
#     """当月电价时刻查询"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#
#         ser = user_serializers.CustomizationDetailSerializer(data=request.data)
#         month = datetime.datetime.now().month
#         if not ser.is_valid():
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": ser.errors},
#                 }
#             )
#         query_dic = request.data
#         province_ins = models.ElectricityProvince.objects.filter(id=ser.validated_data["id"]).first()
#         ser.validated_data.pop("id")
#         query_dic.pop("id")
#         price_ins = models.PeakValley.objects.filter(year_month=month, province=province_ins, **query_dic).first()
#         ins_ser = user_serializers.CustomizationDetailSerializer(instance=price_ins)
#         return Response({"code": common_response_code.SUCCESS, "data": ins_ser.data})


# class CustomizationHistoryView(APIView):
#     """定制化历史记录"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def get(self, request):
#         user_id = request.user["user_id"]
#         user_ins = models.UserDetails.objects.get(id=user_id)
#         detail_dic = (
#             models.UnitPrice.objects.filter(user=user_ins, delete=0)
#             .values(
#                 "name",
#                 "start",
#                 "end",
#                 "summer_start",
#                 "summer_end",
#                 "summer_spike",
#                 "summer_peak",
#                 "summer_flat",
#                 "summer_valley",
#                 "nosummer_start",
#                 "nosummer_end",
#                 "no_summer_spike",
#                 "no_summer_peak",
#                 "no_summer_flat",
#                 "no_summer_valley",
#                 "uid",
#                 "stations_name",
#             )
#             .distinct()
#         )
#
#         return Response({"code": common_response_code.SUCCESS, "data": detail_dic})


# class CustomizationDeleteView(APIView):
#     """定制化删除"""
#
#     authentication_classes = [
#         JwtParamAuthentication,
#         JWTHeaderAuthentication,
#         DenyAuthentication,
#     ]  # jwt认证
#
#     def post(self, request):
#         user_id = request.user["user_id"]
#         id = request.data.get("id", None)
#         if not id:
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "id为必传参数"},
#                 }
#             )
#
#         station_ins = models.UserAutomation.objects.filter(id=id, delete=0).first()
#         if not station_ins:
#             error_log.error("定制化删除:id 不正确")
#             return Response(
#                 {
#                     "code": common_response_code.FIELD_ERROR,
#                     "data": {"message": "error", "detail": "id不正确"},
#                 }
#             )
#         station_ins.delete = 1
#         station_ins.delete_user_id = user_id
#         station_ins.save()
#         return Response({"code": common_response_code.SUCCESS, "data": "定制化记录删除成功"})


# class TTView(APIView):
#     def get(self, request):
#         year = int(request.GET.get("year"))
#         month = int(request.GET.get("month"))
#         day = int(request.GET.get("day"))
#         current_date = datetime.date(year, month, day)
#         dt = datetime.datetime.combine(current_date, datetime.time(hour=1))
#         # 转换为时间戳（以秒为单位）
#         timestamp = int(dt.timestamp())
#         url = "http://172.17.6.44:9001/api/point/getHistoryDataV3"
#         stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
#         for station in stations_ins:
#             try:
#                 request_json, charge, discharge = mqtt_request_data(station, dt)
#                 province_ins = station.province
#                 peak_ins = models.PeakValley.objects.filter(
#                     year_month=current_date.month, province=province_ins, level=station.level, type=station.type
#                 )
#                 exists = models.StationIncome.objects.filter(
#                     station_id=station,
#                     master_station=station.master_station,
#                     income_date__day=current_date.day,
#                     income_date__month=current_date.month,
#                     income_date__year=current_date.year,
#                     record=1,
#                 )
#                 if not exists:
#                     response = requests.post(url=url, json=request_json)
#                     datas = response.json()['datas']
#                     if datas:
#                         total = mqtt_income(station, datas, current_date, peak_ins, charge, discharge)
#                         print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元")
#                         if total < 0:
#                             total = 0
#                             print(f"定时任务收益计算完成================ {station.english_name}总收益为:{total}元 小于 0")
#                         income_ins = models.StationIncome.objects.filter(
#                             station_id=station,
#                             master_station = station.master_station,
#                             income_date__day=current_date.day,
#                             income_date__month=current_date.month,
#                             income_date__year=current_date.year,
#                             record=2,
#                         )
#                         if not income_ins.exists():
#                             try:
#                                 models.StationIncome.objects.create(
#                                     peak_load_shifting=total, station_id=station, income_date=current_date, income_type=1, record=2,master_station = station.master_station
#                                 )
#                             except Exception as e:
#                                 print (e)
#                         else:
#                             income_ins.update(peak_load_shifting=total, income_type=1, record=2)
#                 else:
#                     print("已存在手动添加的收入 不进行自动添加=====================")
#             except Exception as e:
#                 error_log.error(f'！！！！电站：{station.station_name}的收益计算报错:{e}，请排查原因并重新计算！')
#
#         return Response("收益计算成功")


# class NewTTView(APIView):
#     """
#     电量：ads: union_1h;
#     电价: t_price
#     """""
#     def get(self, request):
#         day = request.GET.get("day")
#         station = request.GET.get("station")
#
#         if not re.match(r"\d{4}-\d{2}-\d{2}", day):
#             return Response({"code": common_response_code.FIELD_ERROR, "data": "day格式错误"})
#
#         time_ins = datetime.datetime.strptime(day, "%Y-%m-%d")
#
#         # 改查新综合过结算表和计量表的新1d冻结表：表名未定
#         select_sql = (
#             "SELECT day, hour, chag, disg, soc as soc FROM ads_report_chag_disg_union_1h"
#             " where station=%s and station_type<=1 and day=%s order by hour")
#
#         # select_sql_2 = (
#         #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
#         #     " where station=%s and station_type<=1 and day=%s order by hour")
#
#         stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
#         if station:
#             stations_ins = stations_ins.filter(english_name=station)
#         for station in stations_ins:
#             try:
#                 province_ins = station.province
#
#                 # 获取峰谷电表
#                 peak_ins = models.PeakValley.objects.filter(
#                     year_month=time_ins.month, province=province_ins, level=station.level, type=station.type
#                 ).first()
#
#                 # 先判断是否有手动添加的收益记录
#                 is_exists_station_income = models.StationIncome.objects.filter(
#                     station_id=station,
#                     master_station=station.master_station,
#                     income_date__day=time_ins.day,
#                     income_date__month=time_ins.month,
#                     income_date__year=time_ins.year,
#                     record=1,
#                 ).exists()
#
#                 # 不存在手动添加的收益记录
#                 if not is_exists_station_income:
#
#                     total = 0
#
#                     # 新增使用结算表的同时，只时间在设置的使用范围内使用结算表
#                     # meter_use_time = models.MeterUseTime.objects.filter(station=station.master_station,
#                     #                                                     is_use=1).first()
#                     # is_use_account = (station.master_station.is_account and meter_use_time and
#                     #                   meter_use_time.start_time.date() <= time_ins <= meter_use_time.end_time.date())
#
#                     # if not is_use_account:
#                     #     # 查询ads_report_chag_disg_data数据
#                     #     results = ads_db_tool.select_many(select_sql, station.english_name, day)
#                     # else:
#                     #     # 自持项目的充电量、放电量和收益数据源由计量电表切换为结算电表
#                     #     results = ads_db_tool.select_many(select_sql_2, station.english_name, day)
#                     #     print(1086, f"自持项目: {station.station_name} 的充电量、放电量和收益数据源由计量电表切换为结算电表")
#
#                     results = ads_db_tool.select_many(select_sql, station.english_name, day)
#
#                     if results:
#                         for i in results:
#
#                             # if not is_use_account:
#                             #     temp_dict = {
#                             #         "charge": round(float(i["chag"]), 2),
#                             #         "discharge": round(float(i["disg"]), 2),
#                             #         "time": i["day"].strftime("%Y-%m-%d") + ' ' + i["hour"] + ":00:00",
#                             #         "type": getattr(peak_ins,
#                             #                         "pv" + i["hour"][1:] if i["hour"].startswith('0') else "pv" + i["hour"])
#                             #     }
#                             #     hour = i["hour"][1:] if i["hour"].startswith('0') else i["hour"]
#                             # else:
#                             #     temp_dict = {
#                             #         "charge": round(float(i["chag"]), 2),
#                             #         "discharge": round(float(i["disg"]), 2),
#                             #         "time": i["day"].strftime("%Y-%m-%d") + ' ' + str(i["hour"]) + ":00:00",
#                             #         "type": getattr(peak_ins,
#                             #                         "pv" + str(i["hour"]))
#                             #     }
#                             #     hour = str(i["hour"])
#
#                             temp_dict = {
#                                 "charge": round(float(i["chag"]), 2),
#                                 "discharge": round(float(i["disg"]), 2),
#                                 "time": i["day"].strftime("%Y-%m-%d") + ' ' + str(i["hour"]) + ":00:00",
#                                 "type": getattr(peak_ins,
#                                                 "pv" + str(i["hour"]))
#                             }
#                             hour = str(i["hour"])
#
#                             # price = new_get_price_v2(station, time_ins, hour)
#                             price = new_get_price(station, time_ins, hour)
#                             print(1089, f"!!!!!!{station.station_name}查询到自定义电价：{hour}--{price}")
#
#                             if price is None:
#                                 price = float(getattr(peak_ins, f"h{hour}"))
#
#                             print(1094, f"@@@@{station.station_name}计算电价为：{hour}--{price}")
#                             hour_income = (temp_dict['discharge'] - temp_dict['charge']) * price
#                             total += hour_income
#
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日收益计算完成================ 总收益为:{total}元")
#                         if total < 0:
#                             total = 0
#                             error_log.error(
#                                 f"电站：{station.station_name}{day}日收益计算完成================ 收益为:{total}元 小于 0")
#                         income_ins = models.StationIncome.objects.filter(
#                             station_id=station,
#                             master_station=station.master_station,
#                             income_date__day=time_ins.day,
#                             income_date__month=time_ins.month,
#                             income_date__year=time_ins.year,
#                             record=2,
#                         )
#                         if not income_ins.exists():
#                             try:
#                                 models.StationIncome.objects.create(
#                                     peak_load_shifting=total, station_id=station, income_date=time_ins, income_type=1,
#                                     record=2, master_station=station.master_station
#                                 )
#                             except Exception as e:
#                                 raise e
#                         else:
#                             income_ins.update(peak_load_shifting=total, income_type=1, record=2)
#                     else:
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日没有找到相关数据，不进行收益计算=====================")
#
#                 # 已存在手动添加的收入 不进行自动添加
#                 else:
#                     error_log.error(
#                         f"电站：{station.station_name}{day}日已存在手动添加的收入 不进行自动添加=====================")
#             except Exception as e:
#                 error_log.error(f'！！！！电站：{station.station_name}{day}日的收益计算报错:{e}，请排查原因并重新计算！')
#                 print(traceback.print_exc())
#
#         error_log.error(f"=====================所有电站{day}日收益计算已完成==============================")
#
#         return Response("收益计算结束")


# class NewTTViewV2(APIView):
#     """
#     电量：ads: union_1h;
#     电价: t_price_new
#     """""
#     def get(self, request):
#         day = request.GET.get("day")
#         station = request.GET.get("station")
#
#         if not re.match(r"\d{4}-\d{2}-\d{2}", day):
#             return Response({"code": common_response_code.FIELD_ERROR, "data": "day格式错误"})
#
#         time_ins = datetime.datetime.strptime(day, "%Y-%m-%d")
#
#         year_month = time_ins.strftime("%Y-%m")
#
#         # 改查新综合过结算表和计量表的新1d冻结表：表名未定
#         select_sql = (
#             "SELECT day, hour, chag, disg, soc as soc FROM ads_report_chag_disg_union_1h"
#             " where station=%s and station_type<=1 and day=%s order by hour")
#
#         # select_sql_2 = (
#         #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
#         #     " where station=%s and station_type<=1 and day=%s order by hour")
#
#         stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
#         if station:
#             stations_ins = stations_ins.filter(english_name=station)
#         for station in stations_ins:
#             try:
#                 province_ins = station.province
#                 # 先判断是否有手动添加的收益记录
#                 is_exists_station_income = models.StationIncome.objects.filter(
#                     station_id=station,
#                     master_station=station.master_station,
#                     income_date__day=time_ins.day,
#                     income_date__month=time_ins.month,
#                     income_date__year=time_ins.year,
#                     record=1,
#                 ).exists()
#
#                 # 不存在手动添加的收益记录
#                 if not is_exists_station_income:
#
#                     total = 0
#
#                     results = ads_db_tool.select_many(select_sql, station.english_name, day)
#
#                     if results:
#                         for i in results:
#                             # 获取峰谷电表
#                             moment = f'{int(i["hour"])}:00' if int(i["hour"]) >= 10 else f'0{int(i["hour"])}:00'
#                             peak_ins = models.PeakValleyNew.objects.filter(
#                                 year_month=year_month, province=province_ins, level=station.level, type=station.type,
#                                 moment=moment
#                             ).first()
#                             temp_dict = {
#                                 "charge": round(float(i["chag"]), 2),
#                                 "discharge": round(float(i["disg"]), 2),
#                                 "time": i["day"].strftime("%Y-%m-%d") + ' ' + str(i["hour"]) + ":00:00",
#                                 "type": getattr(peak_ins, 'pv')
#                             }
#                             hour = str(i["hour"])
#
#                             chag_price, disg_price = new_get_price_v2(station, time_ins, peak_ins)
#                             error_log.error(
#                                 f"!!!!!!{station.station_name}查询到自定义电价：{day}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
#                             # if price is None:
#                             #     price = float(getattr(peak_ins, "price"))
#                             error_log.error(
#                                 f"######{station.station_name}计算计算使用电价为：{day}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
#                             hour_income = temp_dict['discharge'] * disg_price - temp_dict['charge'] * chag_price
#                             total += hour_income
#
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日收益计算完成================ 总收益为:{round(total, 2)}元")
#                         if total < 0:
#                             total = 0
#                             error_log.error(
#                                 f"电站：{station.station_name}{day}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
#                         income_ins = models.StationIncome.objects.filter(
#                             station_id=station,
#                             master_station=station.master_station,
#                             income_date__day=time_ins.day,
#                             income_date__month=time_ins.month,
#                             income_date__year=time_ins.year,
#                             record=2,
#                         )
#                         if not income_ins.exists():
#                             try:
#                                 models.StationIncome.objects.create(
#                                     peak_load_shifting=round(total, 2), station_id=station, income_date=time_ins, income_type=1,
#                                     record=2, master_station=station.master_station
#                                 )
#                             except Exception as e:
#                                 raise e
#                         else:
#                             income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
#                     else:
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日没有找到相关数据，不进行收益计算=====================")
#
#                 # 已存在手动添加的收入 不进行自动添加
#                 else:
#                     error_log.error(
#                         f"电站：{station.station_name}{day}日已存在手动添加的收入 不进行自动添加=====================")
#             except Exception as e:
#                 error_log.error(f'！！！！电站：{station.station_name}{day}日的收益计算报错:{e}，请排查原因并重新计算！')
#                 print(traceback.print_exc())
#
#         error_log.error(f"=====================所有电站{day}日收益计算已完成==============================")
#
#         return Response("手动收益计算结束")


# class NewTTViewV3(APIView):
#     """
#     电量：ads: union_30min;
#     电价: t_price_new
#     """""
#     def get(self, request):
#         day = request.GET.get("day")
#         station = request.GET.get("station")
#
#         if not re.match(r"\d{4}-\d{2}-\d{2}", day):
#             return Response({"code": common_response_code.FIELD_ERROR, "data": "day格式错误"})
#
#         time_ins = datetime.datetime.strptime(day, "%Y-%m-%d")
#
#         year_month = time_ins.strftime("%Y-%m")
#
#         # 改查新综合过结算表和计量表的新1d冻结表：表名未定
#         select_sql = (
#             "SELECT day, start_time, chag, disg, soc as soc FROM ads_report_chag_disg_union_30min"
#             " where station=%s and station_type<=1 and day=%s order by start_time")
#
#         # select_sql_2 = (
#         #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
#         #     " where station=%s and station_type<=1 and day=%s order by hour")
#
#         stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
#         if station:
#             stations_ins = stations_ins.filter(english_name=station)
#         for station in stations_ins:
#             try:
#                 province_ins = station.province
#                 # 先判断是否有手动添加的收益记录
#                 is_exists_station_income = models.StationIncome.objects.filter(
#                     station_id=station,
#                     master_station=station.master_station,
#                     income_date__day=time_ins.day,
#                     income_date__month=time_ins.month,
#                     income_date__year=time_ins.year,
#                     record=1,
#                 ).exists()
#
#                 # 不存在手动添加的收益记录
#                 if not is_exists_station_income:
#
#                     total = 0
#
#                     results = ads_db_tool.select_many(select_sql, station.english_name, day)
#                     t = []
#                     if results:
#                         for i in results:
#                             # 获取峰谷电表
#                             # moment = f'{int(i["hour"])}:00' if int(i["hour"]) >= 10 else f'0{int(i["hour"])}:00'
#                             moment = i["start_time"].strftime("%H:%M")
#                             peak_ins = models.PeakValleyNew.objects.filter(
#                                 year_month=year_month, province=province_ins, level=station.level, type=station.type,
#                                 moment=moment
#                             ).first()
#                             temp_dict = {
#                                 "charge": round(float(i["chag"]), 2),
#                                 "discharge": round(float(i["disg"]), 2),
#                                 "time": i["start_time"].strftime("%Y-%m-%d %H:%M"),
#                                 "type": getattr(peak_ins, 'pv')
#                             }
#                             # hour = str(i["hour"])
#
#                             chag_price, disg_price = new_get_price_v2(station, time_ins, peak_ins)
#                             # error_log.error(
#                             #     f"!!!!!!{station.station_name}查询到自定义电价：{day}:{hour}--充电电价：{chag_price}--放电电价：{disg_price}")
#                             # if price is None:
#                             #     price = float(getattr(peak_ins, "price"))
#                             error_log.error(
#                                 f"######{station.station_name}计算计算使用电价为：{temp_dict['time']}--充电电价：{chag_price}--放电电价：{disg_price}, {temp_dict['type']}")
#                             moment_income = temp_dict['discharge'] * disg_price - temp_dict['charge'] * chag_price
#                             t.append(temp_dict['type'])
#                             total += round(moment_income, 2)
#                         print(1371, t)
#
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日收益计算完成================ 总收益为:{round(total, 2)}元")
#                         if total < 0:
#                             total = 0
#                             error_log.error(
#                                 f"电站：{station.station_name}{day}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
#                         income_ins = models.StationIncome.objects.filter(
#                             station_id=station,
#                             master_station=station.master_station,
#                             income_date__day=time_ins.day,
#                             income_date__month=time_ins.month,
#                             income_date__year=time_ins.year,
#                             record=2,
#                         )
#                         if not income_ins.exists():
#                             try:
#                                 models.StationIncome.objects.create(
#                                     peak_load_shifting=round(total, 2), station_id=station, income_date=time_ins, income_type=1,
#                                     record=2, master_station=station.master_station
#                                 )
#                             except Exception as e:
#                                 raise e
#                         else:
#                             income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
#                     else:
#                         error_log.error(
#                             f"电站：{station.station_name}{day}日没有找到相关数据，不进行收益计算=====================")
#
#                 # 已存在手动添加的收入 不进行自动添加
#                 else:
#                     error_log.error(
#                         f"电站：{station.station_name}{day}日已存在手动添加的收入 不进行自动添加=====================")
#             except Exception as e:
#                 error_log.error(f'！！！！电站：{station.station_name}{day}日的收益计算报错:{e}，请排查原因并重新计算！')
#                 print(traceback.print_exc())
#
#         error_log.error(f"=====================所有电站{day}日收益计算已完成==============================")
#
#         return Response("手动收益计算结束")


class NewTTViewV4(APIView):
    """
    电量：ads: union_1d;
    电价: t_price_new
    """""
    def get(self, request):
        day = request.GET.get("day")
        end_day = request.GET.get('end_day')
        station = request.GET.get("station")

        if not re.match(r"\d{4}-\d{2}-\d{2}", day):
            return Response({"code": common_response_code.FIELD_ERROR, "data": "day格式错误"})

        today = datetime.datetime.now() if not end_day else datetime.datetime.strptime(end_day, "%Y-%m-%d")

        # time_ins = datetime.datetime.strptime(day, "%Y-%m-%d")

        start_day = datetime.datetime.strptime(day, "%Y-%m-%d")

        # 改查新综合过结算表和计量表的新1d冻结表：表名未定
        select_sql = (
            "SELECT day, pointed_chag, pointed_disg, peak_chag, peak_disg, flat_chag, flat_disg, valley_chag, valley_disg, dvalley_chag, dvalley_disg FROM ads_report_chag_disg_union_1d"
            " where station=%s and station_type<=1 and day=%s")

        # select_sql_2 = (
        #     "SELECT day, hour, chag, disg, soc as soc FROM ads_report_ems_chag_disg_1h"
        #     " where station=%s and station_type<=1 and day=%s order by hour")

        stations_ins = models.StationDetails.objects.filter(is_delete=0).all()
        if station:
            stations_ins = stations_ins.filter(master_station__english_name=station).all()
        while start_day <= today:
            print(1453, start_day)
            for station in stations_ins:
                try:
                    # province_ins = station.province
                    # 先判断是否有手动添加的收益记录
                    is_exists_station_income = models.StationIncome.objects.filter(
                        station_id=station,
                        master_station=station.master_station,
                        income_date__day=start_day.day,
                        income_date__month=start_day.month,
                        income_date__year=start_day.year,
                        record=1,
                    ).exists()

                    # 不存在手动添加的收益记录
                    if not is_exists_station_income:

                        # total = 0

                        i = ads_db_tool.select_one(select_sql, station.english_name, start_day.strftime("%Y-%m-%d"))
                        if i:

                            temp_dict = {
                                "pointed_chag": float(i["pointed_chag"]),
                                "pointed_disg": float(i["pointed_disg"]),
                                "peak_chag": float(i["peak_chag"]),
                                "peak_disg": float(i["peak_disg"]),
                                "flat_chag": float(i["flat_chag"]),
                                "flat_disg": float(i["flat_disg"]),
                                "valley_chag": float(i["valley_chag"]),
                                "valley_disg": float(i["valley_disg"]),
                                "dvalley_chag": float(i["dvalley_chag"]),
                                "dvalley_disg": float(i["dvalley_disg"])
                            }

                            price_dict = get_station_price(station, start_day)

                            print(1486, temp_dict, price_dict)

                            error_log.error(
                                f"######{station.station_name}-{start_day.strftime('%y-%m-%d')}计算收益使用电价为：尖时电价：{price_dict['spike_chag_price']}--峰电价：{price_dict['peak_chag_price']}--平峰电价：{price_dict['flat_chag_price']}--谷峰电价：{price_dict['valley_chag_price']}--深谷电价：{price_dict['dvalley_chag_price']}")

                            moment_income = (round(temp_dict['pointed_disg'] * price_dict['spike_disg_price'], 4) - round(temp_dict['pointed_chag'] * price_dict['spike_chag_price'], 4) +
                                             round((temp_dict['peak_disg'] * price_dict['peak_disg_price']), 4) - round(temp_dict['peak_chag'] * price_dict['peak_chag_price'], 4) +
                                             round((temp_dict['flat_disg'] * price_dict['flat_disg_price']), 4) - round(temp_dict['flat_chag'] * price_dict['flat_chag_price'], 4) +
                                             round((temp_dict['valley_disg'] * price_dict['valley_disg_price']), 4) - round(temp_dict['valley_chag'] * price_dict['valley_chag_price'], 4) +
                                             round(temp_dict['dvalley_disg'] * price_dict['dvalley_disg_price'], 4) - round(temp_dict['dvalley_chag'] * price_dict['dvalley_chag_price'], 4))

                            total = round(moment_income, 2)

                            error_log.error(
                                f"电站：{station.station_name}-{start_day.strftime('%Y-%m-%d')}日收益计算完成================ 总收益为:{round(total, 2)}元")
                            if total < 0:
                                total = 0
                                error_log.error(
                                    f"电站：{station.station_name}-{start_day.strftime('%Y-%m-%d')}日收益计算完成================ 收益为:{round(total, 2)}元 小于 0")
                            income_ins = models.StationIncome.objects.filter(
                                station_id=station,
                                master_station=station.master_station,
                                income_date__day=start_day.day,
                                income_date__month=start_day.month,
                                income_date__year=start_day.year,
                                record=2,
                            )
                            if not income_ins.exists():
                                try:
                                    models.StationIncome.objects.create(
                                        peak_load_shifting=round(total, 2), station_id=station, income_date=start_day, income_type=1,
                                        record=2, master_station=station.master_station
                                    )
                                except Exception as e:
                                    raise e
                            else:
                                income_ins.update(peak_load_shifting=round(total, 2), income_type=1, record=2)
                        else:
                            error_log.error(
                                f"电站：{station.station_name}{day}日没有找到相关数据，不进行收益计算=====================")

                    # 已存在手动添加的收入 不进行自动添加
                    else:
                        error_log.error(
                            f"电站：{station.station_name}{day}日已存在手动添加的收入 不进行自动添加=====================")
                except Exception as e:
                    error_log.error(f'！！！！电站：{station.station_name}-{start_day.strftime("%Y-%m-%d")}日的收益计算报错:{e}，请排查原因并重新计算！')
                    print(traceback.print_exc())

            start_day = start_day + datetime.timedelta(days=1)

        error_log.error(f"==================所有电站{day}至{today.strftime('%Y-%m-%d')}收益计算已完成====================")

        return Response("手动收益计算结束")


class FeedbackView(APIView):
    """小程序： 用户反馈"""

    authentication_classes = [JwtParamAuthentication, JWTHeaderAuthentication, DenyAuthentication]

    def post(self, request):
        try:
            user_id = request.user['user_id']
            user = models.UserDetails.objects.get(id=user_id)
        except Exception as e:
            error_log.error(e)
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户反馈：用户不存在"},
                }
            )

        feedback_content = request.data.get('content')
        if not feedback_content:
            return Response(
                {
                    "code": common_response_code.ERROR,
                    "data": {"message": "error", "detail": "用户反馈：反馈内容为空"},
                }
            )

        models.Feedback.objects.create(
            user_id=request.user['user_id'], content=feedback_content
        )

        tissue = user.tissue if user.tissue else ''
        Subject = f'{user.user_name}{datetime.datetime.today().strftime("%Y-%m-%d")}的天禄使用反馈'
        message = (f"{tissue}用户{user.user_name}在{datetime.datetime.now().strftime('%Y-%m-%d %H:%M')} "
                   f"针对天禄系列产品反馈以下内容：\n\n{feedback_content}")

        sender_show = "白泽添禄反馈邮件"
        recipient_show = "白泽添禄反馈邮件"

        try:
            sendMail_(message, Subject, sender_show, recipient_show, FEEDBACK_EMAIL_RECEIVERS)
        except Exception as e:
            error_log.error(f"邮件: {Subject} ===>发送失败")

        success_log.debug(f"邮件: {Subject} ===>发送成功")
        return Response(
                {
                    "code": common_response_code.SUCCESS,
                    "data": {"message": "success", "detail": "用户反馈：成功"},
                }
            )


class InitTModelUnitRelationView(APIView):
    """
    初始化电池温度分析--模型和设备关联表
    """""

    def get(self, request):
        units = Unit.objects.filter(is_delete=0).all()
        t_models = TModelConfig.objects.all()

        for unit in units:
            for t_model in t_models:
                exist = models.TModelUnitRelation.objects.filter(unit_id=unit.id, model_id=t_model.id).exists()
                if not exist:
                    models.TModelUnitRelation.objects.create(
                        unit_id=unit.id, model_id=t_model.id,
                    )
        return Response("ok")
