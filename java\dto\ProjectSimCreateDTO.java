package com.robestec.dailyproduce.project.dto.sim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;


/**
 * 5G专用卡信息创建DTO
 */
@Data
@ApiModel("5G专用卡信息创建DTO")
public class ProjectSimCreateDTO {

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "卡号（唯一）")
    private String cardNumber;

    @ApiModelProperty(value = "运营商（枚举：中国移动、中国联通、中国电信、其他）")
    private String operator;

    @ApiModelProperty(value = "有效日期")
    private Date expiryDate;

    @ApiModelProperty(value = "安装位置")
    private String installLocation;

    @ApiModelProperty(value = "保管人")
    private String keeper;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "备注")
    private String remark;
}