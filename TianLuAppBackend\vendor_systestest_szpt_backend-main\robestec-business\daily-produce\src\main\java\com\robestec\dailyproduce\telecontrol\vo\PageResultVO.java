package com.robestec.dailyproduce.telecontrol.vo;

import lombok.Data;
import java.util.List;

/**
 * 分页响应结果VO
 */
@Data
public class PageResultVO<T> {
    private List<T> records;
    private Long total;
    private Integer pageNum;
    private Integer pageSize;

    public PageResultVO(List<T> records, Long total, Integer pageNum, Integer pageSize) {
        this.records = records;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }
} 