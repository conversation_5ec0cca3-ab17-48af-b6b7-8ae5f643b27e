#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-04-06 17:19:17
#@FilePath     : \RHBESS_Service\Application\Models\SelfStationPoint\t_discrete.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-04-12 17:22:24


from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_scada import mqtt_Base,mqtt_session
from Tools.DB.mysql_scada import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SelfStationPoint.t_device import DevicePT

class DiscretePT(mqtt_Base):
    ''' 离散量说明表 '''
    __tablename__ = "t_discrete"
    id = Column(Integer, nullable=False, primary_key=True,comment=u"主键")
    device_id = Column(Integer, ForeignKey("t_device.id"),nullable=False, comment=u"所属设备")
    name = Column(String(256), nullable=False,comment=u"英文名称")
    descr = Column(String(256), nullable=False,comment=u"名称")
    a_duration = Column(String(256), nullable=True,comment=u"告警状态持续时长，用分号;隔开")
    a_status = Column(String(256), nullable=True,comment=u"只配置告警状态值，用分号;隔开")
    a_desc = Column(String(256), nullable=True,comment=u"告警状态值描述，用分号;隔开")
    n_status = Column(String(256), nullable=True,comment=u"各状态值，用分号;隔开")
    desc_status = Column(String(256), nullable=True,comment=u"各状态描述，用分号;隔开")
    store_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否存盘（0否1是，默认1）")
    rep_flag = Column(Integer, nullable=True,server_default='1',comment=u"是否上报（0否1是，默认1）")
    

    device_discrete = relationship("DevicePT",backref='device_discrete')

    @classmethod
    def init(cls):
        mqtt_Base.metadata.create_all()
        

    def __repr__(self):
        return "{'id':'%s','name':'%s','descr':'%s','a_duration':'%s','a_status':'%s','a_desc':'%s','n_status':'%s','desc_status':'%s','store_flag':'%s','rep_flag':'%s'}" % (
            self.id,self.name,self.descr,self.a_duration,self.a_status,self.a_desc,self.n_status,self.desc_status,self.store_flag,self.rep_flag)