#!/usr/bin/env python
# coding=utf-8
# @Information:
# <AUTHOR> WYJ报表冻结
# @Date         : 2022-08-04 08:37:09
# @FilePath     : \RHBESS_Service\Tools\TimeTask\froze_report.py
# @Email        : <EMAIL>
# @LastEditTime : 2023-04-26 14:24:21

from sqlalchemy import func
import datetime
import sys, getopt
from Application.Models.User.report_f import FReport
from Application.Models.User.station_relation import StationR
from Tools.DB.guizhou_his import guizhou1_engine, guizhou2_engine, guizhou3_engine, guizhou4_engine, guizhou5_engine, \
    guizhou7_engine, guizhou6_engine, guizhou8_engine, guizhou1_session, guizhou2_session, guizhou3_session, \
    guizhou4_session, guizhou5_session, guizhou6_session, guizhou7_session, guizhou8_session, GUIZHOU1_DATABASE, \
    GUIZHOU2_DATABASE, G<PERSON>ZHOU3_DATABASE, G<PERSON>ZHOU4_DATABASE, GUIZHOU5_DATABASE, GUIZHOU6_DATABASE, GUIZHOU7_DATABASE, \
    GUIZHOU8_DATABASE
from Tools.DB.houma_his import houmaa1_engine, HOUMAA1_DATABASE, houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, \
    houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houmab2_engine, houmab2_session, HOUMAB2_DATABASE, \
    houmaa1_session
from Tools.DB.shgyu_his import shgyu_session
from Tools.DB.ygqn_his import ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn8_engine, ygqn8_session, YGQN8_DATABASE
from Tools.DB.zgtian_his import zgtian1_engine, zgtian1_session,ZGTIAN1_DATABASE, zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, \
    zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE,mzgtian_engine,mzgtian_session,MZGTIAN_DATABASE
from Tools.Cfg.DB_his import get_dhis
from Tools.Utils.num_utils import *
from Tools.DB.mysql_user import  user_session
from Tools.DB.halun_his import halun_session, halun_engine, HALUN_DATABASE
from Tools.DB.taicang_his import taicang_session, taicang_engine, TAICANG_DATABASE
from Tools.DB.binhai1_his import binhai1_session, binhai1_engine, BINHAI1_DATABASE
from Tools.DB.binhai2_his import binhai2_session, binhai2_engine, BINHAI2_DATABASE
from Tools.DB.ygzhen_his import ygzhen1_engine, ygzhen2_engine, ygzhen1_session, ygzhen2_session, YGZHEN1_DATABASE, \
    YGZHEN2_DATABASE
from Tools.DB.datong_his import datong1_engine, datong1_session, datong2_engine, datong2_session, datong3_engine, \
    datong3_session, datong4_engine, datong4_session, DATONG1_DATABASE, DATONG2_DATABASE, DATONG3_DATABASE, \
    DATONG4_DATABASE
from Tools.DB.baodian_his import baodian1_engine, baodian1_session, BAODIAN1_DATABASE, baodian2_engine, \
    baodian2_session, BAODIAN2_DATABASE, \
    baodian3_engine, baodian3_session, BAODIAN3_DATABASE, baodian4_engine, baodian4_session, BAODIAN4_DATABASE, \
    baodian5_engine, baodian5_session, BAODIAN5_DATABASE
from Tools.Utils.time_utils import timeUtils
from Application.Models.User.report import Report
from Application.Models.His.r_ACDMS import HisACDMS,HisDM
from apscheduler.schedulers.blocking import BlockingScheduler
import json
import logging
import pymysql
from Tools.DB.mysql_his import dongmu_session,_dm_his_engine,HIS_DATABASE_
from dbutils.persistent_db import PersistentDB
from Application.Cfg.dir_cfg import model_config
logging.basicConfig()

# 连接数据库
pool = PersistentDB(pymysql, 10,**{
            "host": model_config.get('mysql', "IDC_HOSTNAME"),  # 数据库主机地址
            "user":  model_config.get('mysql', "IDC_USERNAME"),  # 数据库用户名
            "password":  model_config.get('mysql', "IDC_PASSWORD"),  # 数据库密码
            "database":  model_config.get('mysql', "IDCD_DATABASE"),  # 数据库名称
            "port":  9030,
            "cursorclass": pymysql.cursors.DictCursor
        })
# 所有需要冻结的变量
disg = 'KWH_DisgCapyTotl'  # 放电
chag = 'KWH_ChagCapyTotl'  # 补电
soc = 'Sys_Soc'  # SOC
# 哈伦
halun = ["tpSthalun.PcsSt1.Lp1.", "tpSthalun.PcsSt1.Lp2.", "tpSthalun.PcsSt2.Lp1.", "tpSthalun.PcsSt2.Lp2."]
# 太仓
taicang = ["tpSttaicang.PcsSt1.Lp1.", "tpSttaicang.PcsSt1.Lp2.", "tpSttaicang.PcsSt2.Lp1.", "tpSttaicang.PcsSt2.Lp2.",
           "tpSttaicang.PcsSt3.Lp1.", "tpSttaicang.PcsSt3.Lp2.", "tpSttaicang.PcsSt4.Lp1.", "tpSttaicang.PcsSt4.Lp2."]
# 滨海1
binhai1 = ["tpStbinhai1.PcsSt1.Lp1.", "tpStbinhai1.PcsSt1.Lp2.", "tpStbinhai1.PcsSt2.Lp1.", "tpStbinhai1.PcsSt2.Lp2.",
           "tpStbinhai1.PcsSt3.Lp1.", "tpStbinhai1.PcsSt3.Lp2."]
# 滨海2
binhai2 = ["tpStbinhai2.PcsSt4.Lp1.", "tpStbinhai2.PcsSt4.Lp2.", "tpStbinhai2.PcsSt5.Lp1.", "tpStbinhai2.PcsSt5.Lp2.",
           "tpStbinhai2.PcsSt6.Lp1.", "tpStbinhai2.PcsSt6.Lp2."]
# pcs永臻1
ygzhen1 = ["tfStygzhen1.EMS.PCS1.Lp1.", "tfStygzhen1.EMS.PCS1.Lp2.", "tfStygzhen1.EMS.PCS1.Lp3.",
           "tfStygzhen1.EMS.PCS1.Lp4."]
# pcs永臻2
ygzhen2 = ["tfStygzhen2.EMS.PCS2.Lp1.", "tfStygzhen2.EMS.PCS2.Lp2.", "tfStygzhen2.EMS.PCS2.Lp3.",
           "tfStygzhen2.EMS.PCS2.Lp4."]
# bms永臻1
ygzhen1_bms = ["tfStygzhen1.EMS.BMS1.Ay1.", "tfStygzhen1.EMS.BMS1.Ay2.", "tfStygzhen1.EMS.BMS1.Ay3.",
               "tfStygzhen1.EMS.BMS1.Ay4."]
# bms永臻2
ygzhen2_bms = ["tfStygzhen2.EMS.BMS2.Ay1.", "tfStygzhen2.EMS.BMS2.Ay2.", "tfStygzhen2.EMS.BMS2.Ay3.",
               "tfStygzhen2.EMS.BMS2.Ay4."]

# pcs中天1
zgtian1 = ["tfStzgtian1.EMS.PCS1.Lp1.", "tfStzgtian1.EMS.PCS1.Lp2.", "tfStzgtian1.EMS.PCS1.Lp3.",
           "tfStzgtian1.EMS.PCS1.Lp4."]
# pcs中天2
zgtian2 = ["tfStzgtian2.EMS.PCS2.Lp1.", "tfStzgtian2.EMS.PCS2.Lp2.", "tfStzgtian2.EMS.PCS2.Lp3.",
           "tfStzgtian2.EMS.PCS2.Lp4."]

# pcs中天3
zgtian3 = ["tfStzgtian3.EMS.PCS3.Lp1.", "tfStzgtian3.EMS.PCS3.Lp2.", "tfStzgtian3.EMS.PCS3.Lp3.",
           "tfStzgtian3.EMS.PCS3.Lp4."]
# pcs中天4
zgtian4 = ["tfStzgtian4.EMS.PCS4.Lp1.", "tfStzgtian4.EMS.PCS4.Lp2.", "tfStzgtian4.EMS.PCS4.Lp3.",
           "tfStzgtian4.EMS.PCS4.Lp4."]
# bms中天1
zgtian1_bms = ["tfStzgtian1.EMS.BMS1.Ay1.", "tfStzgtian1.EMS.BMS1.Ay2.", "tfStzgtian1.EMS.BMS1.Ay3.",
               "tfStzgtian1.EMS.BMS3.Ay4."]
# bms中天2
zgtian2_bms = ["tfStzgtian2.EMS.BMS2.Ay1.", "tfStzgtian2.EMS.BMS2.Ay2.", "tfStzgtian2.EMS.BMS2.Ay3.",
               "tfStygzhen2.EMS.BMS2.Ay4."]
# bms中天3
zgtian3_bms = ["tfStzgtian3.EMS.BMS3.Ay1.", "tfStzgtian3.EMS.BMS3.Ay2.", "tfStzgtian3.EMS.BMS3.Ay3.",
               "tfStzgtian3.EMS.BMS3.Ay4."]
# bms中天4
zgtian4_bms = ["tfStzgtian4.EMS.BMS4.Ay1.", "tfStzgtian4.EMS.BMS4.Ay2.", "tfStzgtian4.EMS.BMS4.Ay3.",
               "tfStzgtian4.EMS.BMS4.Ay4."]

# 保电1pcs  tfStbodian1.Pcs.ChagCapyTotl 充，tfStbodian1.Pcs.DisgCapyTotl 放
baodian1_pcs = ["tfStbodian1.Pc"]
# 保电2pcs
baodian2_pcs = ["tfStbodian2.Pc"]
# 保电3pcs
baodian3_pcs = ["tfStbodian3.Pc"]
# 保电4pcs
baodian4_pcs = ["tfStbodian4.Pc"]
# 保电5pcs
baodian5_pcs = ["tfStbodian5.Pc"]


# pcs侯马
houma1 = ["tfSthoumaA1.EMS.A502.PCS1.Lp1.","tfSthoumaA1.EMS.A502.PCS1.Lp2.","tfSthoumaA1.EMS.A502.PCS2.Lp1.","tfSthoumaA1.EMS.A502.PCS2.Lp2.","tfSthoumaA1.EMS.A502.PCS3.Lp1.","tfSthoumaA1.EMS.A502.PCS3.Lp2.","tfSthoumaA1.EMS.A502.PCS4.Lp1.","tfSthoumaA1.EMS.A502.PCS4.Lp2.","tfSthoumaA1.EMS.A502.PCS5.Lp1.","tfSthoumaA1.EMS.A502.PCS5.Lp2.","tfSthoumaA1.EMS.A502.PCS6.Lp1.","tfSthoumaA1.EMS.A502.PCS6.Lp2.","tfSthoumaA1.EMS.A503.PCS7.Lp1.","tfSthoumaA1.EMS.A503.PCS7.Lp2."]
houma2 =["tfSthoumaA2.EMS.A503.PCS8.Lp1.","tfSthoumaA2.EMS.A503.PCS8.Lp2.","tfSthoumaA2.EMS.A503.PCS9.Lp1.","tfSthoumaA2.EMS.A503.PCS9.Lp2.","tfSthoumaA2.EMS.A503.PCS10.Lp1.","tfSthoumaA2.EMS.A503.PCS10.Lp2.","tfSthoumaA2.EMS.A503.PCS11.Lp1.","tfSthoumaA2.EMS.A503.PCS11.Lp2.","tfSthoumaA2.EMS.A503.PCS12.Lp1.","tfSthoumaA2.EMS.A503.PCS12.Lp2.","tfSthoumaA2.EMS.A504.PCS13.Lp1.","tfSthoumaA2.EMS.A504.PCS13.Lp2.","tfSthoumaA2.EMS.A504.PCS14.Lp1.","tfSthoumaA2.EMS.A504.PCS14.Lp2."]
houma3 =["tfSthoumaB1.EMS.B504.PCS1.Lp1.","tfSthoumaB1.EMS.B504.PCS1.Lp2.","tfSthoumaB1.EMS.B504.PCS2.Lp1.","tfSthoumaB1.EMS.B504.PCS2.Lp2.","tfSthoumaB1.EMS.B504.PCS3.Lp1.","tfSthoumaB1.EMS.B504.PCS3.Lp2.","tfSthoumaB1.EMS.B504.PCS4.Lp1.","tfSthoumaB1.EMS.B504.PCS4.Lp2.","tfSthoumaB1.EMS.B505.PCS5.Lp1.","tfSthoumaB1.EMS.B505.PCS5.Lp2.","tfSthoumaB1.EMS.B505.PCS6.Lp1.","tfSthoumaB1.EMS.B505.PCS6.Lp2.","tfSthoumaB1.EMS.B505.PCS7.Lp1.","tfSthoumaB1.EMS.B505.PCS7.Lp2.","tfSthoumaB1.EMS.B505.PCS8.Lp1.","tfSthoumaB1.EMS.B505.PCS8.Lp2."]
houma4 =["tfSthoumaB2.EMS.B505.PCS9.Lp1.","tfSthoumaB2.EMS.B505.PCS9.Lp2.","tfSthoumaB2.EMS.B505.PCS10.Lp1.","tfSthoumaB2.EMS.B505.PCS10.Lp2.","tfSthoumaB2.EMS.B506.PCS11.Lp1.","tfSthoumaB2.EMS.B506.PCS11.Lp2.","tfSthoumaB2.EMS.B506.PCS12.Lp1.","tfSthoumaB2.EMS.B506.PCS12.Lp2.","tfSthoumaB2.EMS.B506.PCS13.Lp1.","tfSthoumaB2.EMS.B506.PCS13.Lp2.","tfSthoumaB2.EMS.B506.PCS14.Lp1.","tfSthoumaB2.EMS.B506.PCS14.Lp2.","tfSthoumaB2.EMS.B506.PCS15.Lp1.","tfSthoumaB2.EMS.B506.PCS15.Lp2.","tfSthoumaB2.EMS.B506.PCS16.Lp1.","tfSthoumaB2.EMS.B506.PCS16.Lp2."]

# bms侯马
houma1_bms = ["tfSthoumaA1.EMS.A502.BMS1.Ay1.","tfSthoumaA1.EMS.A502.BMS1.Ay2.","tfSthoumaA1.EMS.A502.BMS2.Ay1.","tfSthoumaA1.EMS.A502.BMS2.Ay2.","tfSthoumaA1.EMS.A502.BMS3.Ay1.","tfSthoumaA1.EMS.A502.BMS3.Ay2.","tfSthoumaA1.EMS.A502.BMS4.Ay1.","tfSthoumaA1.EMS.A502.BMS4.Ay2.","tfSthoumaA1.EMS.A502.BMS5.Ay1.","tfSthoumaA1.EMS.A502.BMS5.Ay2.","tfSthoumaA1.EMS.A502.BMS6.Ay1.","tfSthoumaA1.EMS.A502.BMS6.Ay2.","tfSthoumaA1.EMS.A503.BMS7.Ay1.","tfSthoumaA1.EMS.A503.BMS7.Ay2."]
houma2_bms =["tfSthoumaA2.EMS.A503.BMS8.Ay1.","tfSthoumaA2.EMS.A503.BMS8.Ay2.","tfSthoumaA2.EMS.A503.BMS9.Ay1.","tfSthoumaA2.EMS.A503.BMS9.Ay2.","tfSthoumaA2.EMS.A503.BMS10.Ay1.","tfSthoumaA2.EMS.A503.BMS10.Ay2.","tfSthoumaA2.EMS.A503.BMS11.Ay1.","tfSthoumaA2.EMS.A503.BMS11.Ay2.","tfSthoumaA2.EMS.A503.BMS12.Ay1.","tfSthoumaA2.EMS.A503.BMS12.Ay2.","tfSthoumaA2.EMS.A504.BMS13.Ay1.","tfSthoumaA2.EMS.A504.BMS13.Ay2.","tfSthoumaA2.EMS.A504.BMS14.Ay1.","tfSthoumaA2.EMS.A504.BMS14.Ay2."]
houma3_bms =["tfSthoumaB1.EMS.B504.BMS1.Ay1.","tfSthoumaB1.EMS.B504.BMS1.Ay2.","tfSthoumaB1.EMS.B504.BMS2.Ay1.","tfSthoumaB1.EMS.B504.BMS2.Ay2.","tfSthoumaB1.EMS.B504.BMS3.Ay1.","tfSthoumaB1.EMS.B504.BMS3.Ay2.","tfSthoumaB1.EMS.B504.BMS4.Ay1.","tfSthoumaB1.EMS.B504.BMS4.Ay2.","tfSthoumaB1.EMS.B505.BMS5.Ay1.","tfSthoumaB1.EMS.B505.BMS5.Ay2.","tfSthoumaB1.EMS.B505.BMS6.Ay1.","tfSthoumaB1.EMS.B505.BMS6.Ay2.","tfSthoumaB1.EMS.B505.BMS7.Ay1.","tfSthoumaB1.EMS.B505.BMS7.Ay2.","tfSthoumaB1.EMS.B505.BMS8.Ay1.","tfSthoumaB1.EMS.B505.BMS8.Ay2."]
houma4_bms =["tfSthoumaB2.EMS.B505.BMS9.Ay1.","tfSthoumaB2.EMS.B505.BMS9.Ay2.","tfSthoumaB2.EMS.B505.BMS10.Ay1.","tfSthoumaB2.EMS.B505.BMS10.Ay2.","tfSthoumaB2.EMS.B506.BMS11.Ay1.","tfSthoumaB2.EMS.B506.BMS11.Ay2.","tfSthoumaB2.EMS.B506.BMS12.Ay1.","tfSthoumaB2.EMS.B506.BMS12.Ay2.","tfSthoumaB2.EMS.B506.BMS13.Ay1.","tfSthoumaB2.EMS.B506.BMS13.Ay2.","tfSthoumaB2.EMS.B506.BMS14.Ay1.","tfSthoumaB2.EMS.B506.BMS14.Ay2.","tfSthoumaB2.EMS.B506.BMS15.Ay1.","tfSthoumaB2.EMS.B506.BMS15.Ay2.","tfSthoumaB2.EMS.B506.BMS16.Ay1.","tfSthoumaB2.EMS.B506.BMS16.Ay2."]

# pcs大同
datong1 = ["tc_datong1.EMS.Energy1.PCS.Lp1.","tc_datong1.EMS.Energy1.PCS.Lp2.","tc_datong1.EMS.Energy2.PCS.Lp1.","tc_datong1.EMS.Energy2.PCS.Lp2."]
datong2 =["tc_datong2.EMS.Energy3.PCS.Lp1.","tc_datong2.EMS.Energy3.PCS.Lp2.","tc_datong2.EMS.Energy4.PCS.Lp1.","tc_datong2.EMS.Energy4.PCS.Lp2."]
datong3 =["tc_datong3.EMS.Energy5.PCS.Lp1.","tc_datong3.EMS.Energy5.PCS.Lp2.","tc_datong3.EMS.Energy6.PCS.Lp1.","tc_datong3.EMS.Energy6.PCS.Lp2."]
datong4 =["tc_datong4.EMS.Energy7.PCS.Lp1.","tc_datong4.EMS.Energy7.PCS.Lp2.","tc_datong4.EMS.Energy8.PCS.Lp1.","tc_datong4.EMS.Energy8.PCS.Lp2."]

# bms大同
datong1_bms= ["tc_datong1.EMS.Energy1.BMS.Ay1.","tc_datong1.EMS.Energy1.BMS.Ay2.","tc_datong1.EMS.Energy2.BMS.Ay1.","tc_datong1.EMS.Energy2.BMS.Ay2."]
datong2_bms=["tc_datong2.EMS.Energy3.BMS.Ay1.","tc_datong2.EMS.Energy3.BMS.Ay2.","tc_datong2.EMS.Energy4.BMS.Ay1.","tc_datong2.EMS.Energy4.BMS.Ay2."]
datong3_bms=["tc_datong3.EMS.Energy5.BMS.Ay1.","tc_datong3.EMS.Energy5.BMS.Ay2.","tc_datong3.EMS.Energy6.BMS.Ay1.","tc_datong3.EMS.Energy6.BMS.Ay2."]
datong4_bms=["tc_datong4.EMS.Energy7.BMS.Ay1.","tc_datong4.EMS.Energy7.BMS.Ay2.","tc_datong4.EMS.Energy8.BMS.Ay1.","tc_datong4.EMS.Energy8.BMS.Ay2."]


# pcs贵州
guizhou1 =["guizhou1a.Energy1.Pcs.Lp1.","guizhou1a.Energy1.Pcs.Lp2.","guizhou1a.Energy2.Pcs.Lp1.","guizhou1a.Energy2.Pcs.Lp2.","guizhou1a.Energy3.Pcs.Lp1.","guizhou1a.Energy3.Pcs.Lp2.","guizhou1b.Energy4.Pcs.Lp1.","guizhou1b.Energy4.Pcs.Lp2.","guizhou1b.Energy5.Pcs.Lp1.","guizhou1b.Energy5.Pcs.Lp2.","guizhou1b.Energy6.Pcs.Lp1.","guizhou1b.Energy6.Pcs.Lp2.","guizhou1c.Energy7.Pcs.Lp1.","guizhou1c.Energy7.Pcs.Lp2.","guizhou1c.Energy8.Pcs.Lp1.","guizhou1c.Energy8.Pcs.Lp2."]
guizhou2 =["guizhou2a.Energy10.Pcs.Lp1.","guizhou2a.Energy10.Pcs.Lp2.","guizhou2a.Energy11.Pcs.Lp1.","guizhou2a.Energy11.Pcs.Lp2.","guizhou2b.Energy12.Pcs.Lp1.","guizhou2b.Energy12.Pcs.Lp2.","guizhou2a.Energy9.Pcs.Lp1.","guizhou2a.Energy9.Pcs.Lp2.","guizhou2b.Energy13.Pcs.Lp1.","guizhou2b.Energy13.Pcs.Lp2.","guizhou2b.Energy14.Pcs.Lp1.","guizhou2b.Energy14.Pcs.Lp2.","guizhou2c.Energy15.Pcs.Lp1.","guizhou2c.Energy15.Pcs.Lp2.","guizhou2c.Energy16.Pcs.Lp1.","guizhou2c.Energy16.Pcs.Lp2."]
guizhou3 =["guizhou3a.Energy17.Pcs.Lp1.","guizhou3a.Energy17.Pcs.Lp2.","guizhou3a.Energy18.Pcs.Lp1.","guizhou3a.Energy18.Pcs.Lp2.","guizhou3a.Energy19.Pcs.Lp1.","guizhou3a.Energy19.Pcs.Lp2.","guizhou3b.Energy20.Pcs.Lp1.","guizhou3b.Energy20.Pcs.Lp2.","guizhou3b.Energy21.Pcs.Lp1.","guizhou3b.Energy21.Pcs.Lp2.","guizhou3b.Energy22.Pcs.Lp1.","guizhou3b.Energy22.Pcs.Lp2.","guizhou3c.Energy23.Pcs.Lp1.","guizhou3c.Energy23.Pcs.Lp2.","guizhou3c.Energy24.Pcs.Lp1.","guizhou3c.Energy24.Pcs.Lp2."]
guizhou4 =["guizhou4a.Energy25.Pcs.Lp1.","guizhou4a.Energy25.Pcs.Lp2.","guizhou4a.Energy26.Pcs.Lp1.","guizhou4a.Energy26.Pcs.Lp2.","guizhou4a.Energy27.Pcs.Lp1.","guizhou4a.Energy27.Pcs.Lp2.","guizhou4b.Energy28.Pcs.Lp1.","guizhou4b.Energy28.Pcs.Lp2.","guizhou4b.Energy29.Pcs.Lp1.","guizhou4b.Energy29.Pcs.Lp2.","guizhou4b.Energy30.Pcs.Lp1.","guizhou4b.Energy30.Pcs.Lp2.","guizhou4c.Energy31.Pcs.Lp1.","guizhou4c.Energy31.Pcs.Lp2.","guizhou4c.Energy32.Pcs.Lp1.","guizhou4c.Energy32.Pcs.Lp2."]
guizhou5 =["guizhou5a.Energy33.Pcs.Lp1.","guizhou5a.Energy33.Pcs.Lp2.","guizhou5a.Energy34.Pcs.Lp1.","guizhou5a.Energy34.Pcs.Lp2.","guizhou5a.Energy35.Pcs.Lp1.","guizhou5a.Energy35.Pcs.Lp2.","guizhou5b.Energy36.Pcs.Lp1.","guizhou5b.Energy36.Pcs.Lp2.","guizhou5b.Energy37.Pcs.Lp1.","guizhou5b.Energy37.Pcs.Lp2.","guizhou5b.Energy38.Pcs.Lp1.","guizhou5b.Energy38.Pcs.Lp2.","guizhou5c.Energy39.Pcs.Lp1.","guizhou5c.Energy39.Pcs.Lp2.","guizhou5c.Energy40.Pcs.Lp1.","guizhou5c.Energy40.Pcs.Lp2."]
guizhou6 =["guizhou6a.Energy41.Pcs.Lp1.","guizhou6a.Energy41.Pcs.Lp2.","guizhou6a.Energy42.Pcs.Lp1.","guizhou6a.Energy42.Pcs.Lp2.","guizhou6a.Energy43.Pcs.Lp1.","guizhou6a.Energy43.Pcs.Lp2.","guizhou6b.Energy44.Pcs.Lp1.","guizhou6b.Energy44.Pcs.Lp2.","guizhou6b.Energy45.Pcs.Lp1.","guizhou6b.Energy45.Pcs.Lp2.","guizhou6b.Energy46.Pcs.Lp1.","guizhou6b.Energy46.Pcs.Lp2.","guizhou6c.Energy47.Pcs.Lp1.","guizhou6c.Energy47.Pcs.Lp2.","guizhou6c.Energy48.Pcs.Lp1.","guizhou6c.Energy48.Pcs.Lp2."]
guizhou7 =["guizhou7a.Energy49.Pcs.Lp1.","guizhou7a.Energy49.Pcs.Lp2.","guizhou7a.Energy50.Pcs.Lp1.","guizhou7a.Energy50.Pcs.Lp2.","guizhou7a.Energy51.Pcs.Lp1.","guizhou7a.Energy51.Pcs.Lp2.","guizhou7b.Energy52.Pcs.Lp1.","guizhou7b.Energy52.Pcs.Lp2.","guizhou7b.Energy53.Pcs.Lp1.","guizhou7b.Energy53.Pcs.Lp2.","guizhou7b.Energy54.Pcs.Lp1.","guizhou7b.Energy54.Pcs.Lp2.","guizhou7c.Energy55.Pcs.Lp1.","guizhou7c.Energy55.Pcs.Lp2.","guizhou7c.Energy56.Pcs.Lp1.","guizhou7c.Energy56.Pcs.Lp2."]
guizhou8 =["guizhou8a.Energy57.Pcs.Lp1.","guizhou8a.Energy57.Pcs.Lp2.","guizhou8a.Energy58.Pcs.Lp1.","guizhou8a.Energy58.Pcs.Lp2.","guizhou8b.Energy59.Pcs.Lp1.","guizhou8b.Energy59.Pcs.Lp2.","guizhou8b.Energy60.Pcs.Lp1.","guizhou8b.Energy60.Pcs.Lp2."]
# bms贵州
guizhou1_bms =["guizhou1a.Energy1.BMS1.","guizhou1a.Energy1.BMS2.","guizhou1a.Energy2.BMS1.","guizhou1a.Energy2.BMS2.","guizhou1a.Energy3.BMS1.","guizhou1a.Energy3.BMS2.","guizhou1b.Energy4.BMS1.","guizhou1b.Energy4.BMS2.","guizhou1b.Energy5.BMS1.","guizhou1b.Energy5.BMS2.","guizhou1b.Energy6.BMS1.","guizhou1b.Energy6.BMS2.","guizhou1c.Energy7.BMS1.","guizhou1c.Energy7.BMS2.","guizhou1c.Energy8.BMS1.","guizhou1c.Energy8.BMS2."]
guizhou2_bms  =["guizhou2a.Energy10.BMS1.","guizhou2a.Energy10.BMS2.","guizhou2a.Energy11.BMS1.","guizhou2a.Energy11.BMS2.","guizhou2b.Energy12.BMS1.","guizhou2b.Energy12.BMS2.","guizhou2a.Energy9.BMS1.","guizhou2a.Energy9.BMS2.","guizhou2b.Energy13.BMS1.","guizhou2b.Energy13.BMS2.","guizhou2b.Energy14.BMS1.","guizhou2b.Energy14.BMS2.","guizhou2c.Energy15.BMS1.","guizhou2c.Energy15.BMS2.","guizhou2c.Energy16.BMS1.","guizhou2c.Energy16.BMS2."]
guizhou3_bms  =["guizhou3a.Energy17.BMS1.","guizhou3a.Energy17.BMS2.","guizhou3a.Energy18.BMS1.","guizhou3a.Energy18.BMS2.","guizhou3a.Energy19.BMS1.","guizhou3a.Energy19.BMS2.","guizhou3b.Energy20.BMS1.","guizhou3b.Energy20.BMS2.","guizhou3b.Energy21.BMS1.","guizhou3b.Energy21.BMS2.","guizhou3b.Energy22.BMS1.","guizhou3b.Energy22.BMS2.","guizhou3c.Energy23.BMS1.","guizhou3c.Energy23.BMS2.","guizhou3c.Energy24.BMS1.","guizhou3c.Energy24.BMS2."]
guizhou4_bms  =["guizhou4a.Energy25.BMS1.","guizhou4a.Energy25.BMS2.","guizhou4a.Energy26.BMS1.","guizhou4a.Energy26.BMS2.","guizhou4a.Energy27.BMS1.","guizhou4a.Energy27.BMS2.","guizhou4b.Energy28.BMS1.","guizhou4b.Energy28.BMS2.","guizhou4b.Energy29.BMS1.","guizhou4b.Energy29.BMS2.","guizhou4b.Energy30.BMS1.","guizhou4b.Energy30.BMS2.","guizhou4c.Energy31.BMS1.","guizhou4c.Energy31.BMS2.","guizhou4c.Energy32.BMS1.","guizhou4c.Energy32.BMS2."]
guizhou5_bms  =["guizhou5a.Energy33.BMS1.","guizhou5a.Energy33.BMS2.","guizhou5a.Energy34.BMS1.","guizhou5a.Energy34.BMS2.","guizhou5a.Energy35.BMS1.","guizhou5a.Energy35.BMS2.","guizhou5b.Energy36.BMS1.","guizhou5b.Energy36.BMS2.","guizhou5b.Energy37.BMS1.","guizhou5b.Energy37.BMS2.","guizhou5b.Energy38.BMS1.","guizhou5b.Energy38.BMS2.","guizhou5c.Energy39.BMS1.","guizhou5c.Energy39.BMS2.","guizhou5c.Energy40.BMS1.","guizhou5c.Energy40.BMS2."]
guizhou6_bms  =["guizhou6a.Energy41.BMS1.","guizhou6a.Energy41.BMS2.","guizhou6a.Energy42.BMS1.","guizhou6a.Energy42.BMS2.","guizhou6a.Energy43.BMS1.","guizhou6a.Energy43.BMS2.","guizhou6b.Energy44.BMS1.","guizhou6b.Energy44.BMS2.","guizhou6b.Energy45.BMS1.","guizhou6b.Energy45.BMS2.","guizhou6b.Energy46.BMS1.","guizhou6b.Energy46.BMS2.","guizhou6c.Energy47.BMS1.","guizhou6c.Energy47.BMS2.","guizhou6c.Energy48.BMS1.","guizhou6c.Energy48.BMS2."]
guizhou7_bms  =["guizhou7a.Energy49.BMS1.","guizhou7a.Energy49.BMS2.","guizhou7a.Energy50.BMS1.","guizhou7a.Energy50.BMS2.","guizhou7a.Energy51.BMS1.","guizhou7a.Energy51.BMS2.","guizhou7b.Energy52.BMS1.","guizhou7b.Energy52.BMS2.","guizhou7b.Energy53.BMS1.","guizhou7b.Energy53.BMS2.","guizhou7b.Energy54.BMS1.","guizhou7b.Energy54.BMS2.","guizhou7c.Energy55.BMS1.","guizhou7c.Energy55.BMS2.","guizhou7c.Energy56.BMS1.","guizhou7c.Energy56.BMS2."]
guizhou8_bms  =["guizhou8a.Energy57.BMS1.","guizhou8a.Energy57.BMS2.","guizhou8a.Energy58.BMS1.","guizhou8a.Energy58.BMS2.","guizhou8b.Energy59.BMS1.","guizhou8b.Energy59.BMS2.","guizhou8b.Energy60.BMS1.","guizhou8b.Energy60.BMS2."]

# 东睦pcs
dongmu_pcs = ["dongmu.PCS1", "dongmu.PCS2", "dongmu.PCS3", "dongmu.PCS4", "dongmu.PCS5", "dongmu.PCS6", "dongmu.PCS7"]

# 所有查询数据的配置结合
all_datas = [[halun_engine, halun_session, HALUN_DATABASE, halun, 'halun', 1000],
             [taicang_engine, taicang_session, TAICANG_DATABASE, taicang, 'taicang', 1000],
             [binhai1_engine, binhai1_session, BINHAI1_DATABASE, binhai1, 'binhai', 1000],
             [binhai2_engine, binhai2_session, BINHAI2_DATABASE, binhai2, 'binhai', 1000]]

all_tf_data = [[ygzhen1_engine, ygzhen1_session, YGZHEN1_DATABASE, ygzhen1, 'ygzhen', 389700],
               [ygzhen2_engine, ygzhen2_session, YGZHEN2_DATABASE, ygzhen2, 'ygzhen', 389700],
               [zgtian1_engine, zgtian1_session, ZGTIAN1_DATABASE, zgtian1, 'zgtian', 389700],
               [zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, zgtian2, 'zgtian', 389700],
               [zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian3, 'zgtian', 389700],
               [zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE, zgtian4, 'zgtian', 389700],
               [houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE, houma1, 'houma', 38970000],
               [houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, houma2, 'houma', 38970000],
               [houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houma3, 'houma', 38970000],
               [houmab2_engine, houmab2_session, HOUMAB2_DATABASE, houma4, 'houma', 38970000]]


# 东睦
all_tf_data_ = [[_dm_his_engine, dongmu_session, HIS_DATABASE_, dongmu_pcs, 'dongmu']]

# 保电
all_baodian_data = [[baodian1_engine, baodian1_session, BAODIAN1_DATABASE, baodian1_pcs, 'baodian', 389700],
                    [baodian2_engine, baodian2_session, BAODIAN2_DATABASE, baodian2_pcs, 'baodian', 389700],
                    [baodian3_engine, baodian3_session, BAODIAN3_DATABASE, baodian3_pcs, 'baodian', 389700],
                    [baodian4_engine, baodian4_session, BAODIAN4_DATABASE, baodian4_pcs, 'baodian', 389700],
                    [baodian5_engine, baodian5_session, BAODIAN5_DATABASE, baodian5_pcs, 'baodian', 389700]]

all_bms_datas = [[ygzhen1_engine, ygzhen1_session, YGZHEN1_DATABASE, ygzhen1_bms, 'ygzhen', 389700],
                 [ygzhen2_engine, ygzhen2_session, YGZHEN2_DATABASE, ygzhen2_bms, 'ygzhen', 389700],
                 [zgtian1_engine, zgtian1_session, ZGTIAN1_DATABASE, zgtian1_bms, 'zgtian', 389700],
                 [zgtian2_engine, zgtian2_session, ZGTIAN2_DATABASE, zgtian2_bms, 'zgtian', 389700],
                 [zgtian3_engine, zgtian3_session, ZGTIAN3_DATABASE, zgtian3_bms, 'zgtian', 389700],
                 [zgtian4_engine, zgtian4_session, ZGTIAN4_DATABASE, zgtian4_bms, 'zgtian', 389700],
                 [houmaa1_engine, houmaa1_session, HOUMAA1_DATABASE, houma1_bms, 'houma', 38970000],
                 [houmaa2_engine, houmaa2_session, HOUMAA2_DATABASE, houma2_bms, 'houma', 38970000],
                 [houmab1_engine, houmab1_session, HOUMAB1_DATABASE, houma3_bms, 'houma', 38970000],
                 [houmab2_engine, houmab2_session, HOUMAB2_DATABASE, houma4_bms, 'houma', 38970000]]


#大同PCS
datong_data = [[datong1_engine, datong1_session, DATONG1_DATABASE, datong1, 'datong', 3897000],
               [datong2_engine, datong2_session, DATONG2_DATABASE, datong2, 'datong', 3897000],
               [datong3_engine, datong3_session, DATONG3_DATABASE, datong3, 'datong', 3897000],
               [datong4_engine, datong4_session, DATONG4_DATABASE, datong4, 'datong', 3897000]]

#大同BMS
datng_bms_datas = [[datong1_engine, datong1_session, DATONG1_DATABASE, datong1_bms, 'datong', 3897000],
                   [datong2_engine, datong2_session, DATONG2_DATABASE, datong2_bms, 'datong', 3897000],
                   [datong3_engine, datong3_session, DATONG3_DATABASE, datong3_bms, 'datong', 3897000],
                   [datong4_engine, datong4_session, DATONG4_DATABASE, datong4_bms, 'datong', 3897000]]

#贵州PCS
guizhou_data = [[guizhou1_engine, guizhou1_session, GUIZHOU1_DATABASE, guizhou1, 'guizhou', 3897000],
               [guizhou2_engine, guizhou2_session, GUIZHOU2_DATABASE, guizhou2, 'guizhou', 3897000],
               [guizhou3_engine, guizhou3_session, GUIZHOU3_DATABASE, guizhou3, 'guizhou', 3897000],
               [guizhou4_engine, guizhou4_session, GUIZHOU4_DATABASE, guizhou4, 'guizhou', 3897000],
               [guizhou5_engine, guizhou5_session, GUIZHOU5_DATABASE, guizhou5, 'guizhou', 3897000],
               [guizhou6_engine, guizhou6_session, GUIZHOU6_DATABASE, guizhou6, 'guizhou', 3897000],
               [guizhou7_engine, guizhou7_session, GUIZHOU7_DATABASE, guizhou7, 'guizhou', 3897000],
               [guizhou8_engine, guizhou8_session, GUIZHOU8_DATABASE, guizhou8, 'guizhou', 3897000]]

#贵州BMS
guizhou_bms_datas = [[guizhou1_engine, guizhou1_session, GUIZHOU1_DATABASE, guizhou1_bms, 'guizhou', 3897000],
               [guizhou2_engine, guizhou2_session, GUIZHOU2_DATABASE, guizhou2_bms, 'guizhou', 3897000],
               [guizhou3_engine, guizhou3_session, GUIZHOU3_DATABASE, guizhou3_bms, 'guizhou', 3897000],
               [guizhou4_engine, guizhou4_session, GUIZHOU4_DATABASE, guizhou4_bms, 'guizhou', 3897000],
               [guizhou5_engine, guizhou5_session, GUIZHOU5_DATABASE, guizhou5_bms, 'guizhou', 3897000],
               [guizhou6_engine, guizhou6_session, GUIZHOU6_DATABASE, guizhou6_bms, 'guizhou', 3897000],
               [guizhou7_engine, guizhou7_session, GUIZHOU7_DATABASE, guizhou7_bms, 'guizhou', 3897000],
               [guizhou8_engine, guizhou8_session, GUIZHOU8_DATABASE, guizhou8_bms, 'guizhou', 3897000]]

ygqn7_bms_b1 =["ygqn.abc.EMS.B.BMS.Energy1.BMS1.",
            "ygqn.abc.EMS.B.BMS.Energy1.BMS2.",
            "ygqn.abc.EMS.B.BMS.Energy2.BMS1.",
            "ygqn.abc.EMS.B.BMS.Energy2.BMS2."]

ygqn7,ygqn8,ygqn8_bms,ygqn7_bms_a,ygqn7_bms_b2,ygqn7_bms_c=get_dhis('forze_report')
shgyu_data,shgyu_bms_datas_a,shgyu_bms_datas_b=get_dhis('forze_report_shgyu')

# bms阳泉
ygqn7_bms_a=ygqn7_bms_a
ygqn7_bms_b1=ygqn7_bms_b1
ygqn7_bms_b2=ygqn7_bms_b2
ygqn7_bms_c=ygqn7_bms_c
ygqn8_bms=ygqn8_bms
#阳泉PCS
ygqn_data_7 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7, 'ygqn', 3897000]]
ygqn_data_8 = [[ygqn8_engine, ygqn8_session, YGQN8_DATABASE, ygqn8, 'ygqn', 3897000]]
#阳泉BMS
ygqn_bms_datas_a = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_a, 'ygqn', 3897000]]
ygqn_bms_datas_b1 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_b1, 'ygqn', 3897000]]
ygqn_bms_datas_b2 = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_b2, 'ygqn', 3897000]]
ygqn_bms_datas_c = [[ygqn7_engine, ygqn7_session, YGQN7_DATABASE, ygqn7_bms_c, 'ygqn', 3897000]]
ygqn_bms_datas_8 = [[ygqn8_engine, ygqn8_session, YGQN8_DATABASE, ygqn8_bms, 'ygqn', 3897000]]

# all_datas = [[his_engine,his_session,HIS_DATABASE,halun]]
shiduan = ['尖峰', '峰段', '平段', '谷段']
#电站
db_ = ['dongmu','ygzhen','bodian','halun','taicang','binhai','zgtian','houma','datong','guizhou','ygqn','shgyu']
# db_ = ['shgyu']


def _tableIsExist(db_conn, schema, tablename):
    '''判断表是否存在
    schema:数据库
    db_conn：原始连接
    tablename：表
    '''
    conn = db_conn.raw_connection()
    cursor = conn.cursor()
    sql = "select table_name from information_schema.TABLES where table_schema='{}' and table_name= '{}' order by table_name limit 1".format(
        schema, tablename)
    cursor.execute(sql)
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    if result:
        return True
    else:
        return False
def frozeDataDay(day, db_con, table, names, disg, chag, soc, F, db, maxV):
    # 冻结一天的数据
    soc_startT = timeUtils.timeStrToTamp('%s 00:00:01' % day)
    soc_endT = timeUtils.timeStrToTamp('%s 23:59:59' % day)
    soc_startT = day + ' 00:00:01'
    soc_endT = day + ' 23:59:59'
    year = int(day[:4])
    month = int(day[5:7])
    HisTable = HisACDMS(table)
    for name in names:
        chag_num, disg_num, data3 = 0, 0, []  # 放电数据集，补电数据集，soc数据集
        chag_jf, chag_fd, chag_pd, chag_gd = [], [], [], []  # 充电四个阶段数据集
        disg_jf, disg_fd, disg_pd, disg_gd = [], [], [], []  # 放电四个阶段数据集

        if F == 'TF':  # 调峰站
            sname = '%s%s' % (name.replace('PCS', 'BMS').replace('Lp', 'Ay'), soc)  # SOC
        elif F == 'guizhou':
            sname = '%s%s' % (name[:-4], soc)  # SOC
        else:
            sname = '%s%s' % (name, soc)  # SOC
        values = db_con.query(HisTable.value).filter(HisTable.name == sname,HisTable.dts_s.between(soc_startT, soc_endT)).order_by(HisTable.dts_s.asc()).all()

        for value in values:
            data3.append(value[0])
        for dl in shiduan:
            reports = user_session.query(Report).filter(Report.station == db, Report.is_use == 1, Report.descr == dl,
                                                        Report.years == year,func.find_in_set(int(month), Report.months)).order_by(Report.start_time.asc()).all()
            if dl == '尖峰':
                c = chag_jf
                d = disg_jf
            elif dl == '峰段':
                c = chag_fd
                d = disg_fd
            elif dl == '平段':
                c = chag_pd
                d = disg_pd
            elif dl == '谷段':
                c = chag_gd
                d = disg_gd

            for re in reports:
                data1, data2 = [], []
                startT = timeUtils.timeStrToTamp('%s %s' % (day, re.start_time))
                endT = timeUtils.timeStrToTamp('%s %s' % (day, re.end_time))
                cname = '%s%s' % (name, chag)  # 补电
                values = db_con.query(HisTable.value).filter(HisTable.name == cname,
                                                             HisTable.dts_s.between(startT, endT)).order_by(
                    HisTable.dts_s.asc()).all()
                for value in values:
                    if db == 'houma':
                        if value[0] < 65535:
                            data1.append(value[0])
                    else:
                        data1.append(value[0])

                dname = '%s%s' % (name, disg)  # 放电
                values = db_con.query(HisTable.value).filter(HisTable.name == dname,
                                                             HisTable.dts_s.between(startT, endT)).order_by(
                    HisTable.dts_s.asc()).all()
                for value in values:
                    if db == 'houma':
                        if value[0] < 65535:
                            data2.append(value[0])
                    else:
                        data2.append(value[0])
                if db =='houma':
                    if data1!=[]:
                        c.append(max(data1)-min(data1))
                    if data2 != []:
                        d.append(max(data2)-min(data2))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
                else:
                    c.append(list_sum_mm(data1, maxV))
                    d.append(list_sum_mm(data2, maxV))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
                # print('cccccccccccccccccccc',name,data1)
                # print('ddddddddddddddddd', name, data2)
        fd, bd = list_soc_mm(data3)  # 放电soc，补电soc
        if db == 'houma':
            if chag_num == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num
        else:
            if chag_num == 0 or fd == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num / fd * bd
        if lv > 1:
            lv = 1
        if lv < 0:
            lv = 0
        print ('table:', table, '--day:', day, '--name:', name,chag_jf,disg_jf,chag_fd,disg_fd,chag_pd,disg_pd,chag_gd,disg_gd,lv)

        user_session.merge(
            FReport(name=name, jf_chag=str(chag_jf), jf_disg=str(disg_jf), fd_chag=str(chag_fd), fd_disg=str(disg_fd),
                    pd_chag=str(chag_pd), pd_disg=str(disg_pd),
                    gd_chag=str(chag_gd), gd_disg=str(disg_gd), day=day, ratio=float(lv),
                    op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    user_session.close()
    db_con.close()
def frozeDataDay_10(day, db_con, table, names, disg, chag, soc, F, db, maxV):
    # 冻结一天的数据(每隔10分钟）
    soc_startT = day+ ' 00:00:01'
    soc_endT =day+ ' 23:59:59'
    chag_s, disg_s = 0, 0# 放电数据集，补电数据集，soc数据集
    for name in names:
        data1, data2 = [], []
        cname = '%s%s' % (name, chag)  # 补电
        v = _select_get_his_value(db_con, table, cname, soc_startT, soc_endT)
        if v != []:
            data1.append(v[0])

        dname = '%s%s' % (name, disg)  # 放电
        v = _select_get_his_value(db_con, table, dname,  soc_startT, soc_endT)
        if v !=[]:
            data2.append(v[0])
        chag_num = np.sum(data1)
        disg_num = np.sum(data2)
        chag_s = chag_s + chag_num
        disg_s = disg_s + disg_num
    user_session.close()
    db_con.close()
    return chag_s, disg_s
def _select_get_his_value(db_con, table, name, st, ed, vmax=65000000):
    '''获取历史数据'''
    db_con.commit()
    data = []
    # 查询当前小时表里的数据
    table_1='ods_r_measure1'
    HisTable = HisACDMS(table_1)
    try:
        values = db_con.query(HisTable.value).filter(HisTable.name == name,HisTable.dts_s.between(st, ed),
                                                                         HisTable.value < vmax).order_by(HisTable.dts_s.desc()).all()  # 当前小时表数据
    except:
        values = []
    if values != []:
        data.append(values[0][0])

    return data
def frozeDongMuDataDay_(day, db_con, table, names, db):
    # 冻结一天的数据（东睦）
    # startT = timeUtils.timeStrToTamp('%s 00:00:01' % day)
    # endT = timeUtils.timeStrToTamp('%s 23:59:59' % day)
    year = int(day[:4])
    month = int(day[5:7])
    for name in names:
        chag_num, disg_num = 0, 0  # 放电数据集，补电数据集，
        chag_jf, chag_fd, chag_pd, chag_gd = [], [], [], []  # 充电四个阶段数据集
        disg_jf, disg_fd, disg_pd, disg_gd = [], [], [], []  # 放电四个阶段数据集
        for dl in shiduan:
            reports = user_session.query(Report).filter(Report.station == db, Report.is_use == 1, Report.descr == dl,
                                                        Report.years == year,
                                                        func.find_in_set(int(month), Report.months)
                                                        ).order_by(Report.start_time.asc()).all()
            if dl == '尖峰':
                c = chag_jf
                d = disg_jf
            elif dl == '峰段':
                c = chag_fd
                d = disg_fd
            elif dl == '平段':
                c = chag_pd
                d = disg_pd
            elif dl == '谷段':
                c = chag_gd
                d = disg_gd
            for re in reports:
                data1, data2 = 0, 0
                startT_ = timeUtils.timeStrToTamp('%s %s' % (day, re.start_time))
                endT_ = timeUtils.timeStrToTamp('%s %s' % (day, re.end_time))

                dm_table = HisDM('r_measure')
                values_mong = dongmu_session.query(dm_table.datainfo).filter(dm_table.time.between(startT_, endT_)).order_by(dm_table.time.asc()).all()

                if values_mong:
                    values_2 = ''
                    values_2 = values_mong[-2]['datainfo']
                    if values_2:
                        value_2 = json.loads(values_2)['body']
                        for ii in value_2:
                            if ii['device'] == name[-4:]:
                                data1 = float(ii['ccapd'])  # 结束时间补电
                                data2 = float(ii['dcapd'])  # 结束时间放电

                        c.append(data1)
                        d.append(data2)
                        chag_num = chag_num + np.sum(c)
                        disg_num = disg_num + np.sum(d)
                    else:
                        chag_jf, chag_fd, chag_pd, chag_gd = [], [], [], []  # 充电四个阶段数据集
                        disg_jf, disg_fd, disg_pd, disg_gd = [], [], [], []  # 放电四个阶段数据集

            if chag_num == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num
            if lv > 1:
                lv = 1
            if lv < 0:
                lv = 0
        # else:
        #     lv = 1

        print ('table:', table, '--day:', day, '--name:', name)

        user_session.merge(
            FReport(name=name, jf_chag=str(chag_jf), jf_disg=str(disg_jf), fd_chag=str(chag_fd), fd_disg=str(disg_fd),
                    pd_chag=str(chag_pd), pd_disg=str(disg_pd),
                    gd_chag=str(chag_gd), gd_disg=str(disg_gd), day=day, ratio=float(lv),
                    op_ts=timeUtils.getNewTimeStr(), cause=1))

    user_session.commit()
    user_session.close()
    dongmu_session.close()
    print ('SUCCESS')
def frozeMeterDataDay(day, table, db, db_con, chag_l, disg_l, sname,ti=None):
    '''
    冻结关口表数据
    day:日期
    table:数据源表
    db_con:数据源连接session
    chag:总充电量
    disg:总放电量
    sname:需要存储的变量名，一般为充放电量共有的前缀名
    '''
    chag_dl, disg_dl, lv = 0, 0, 0
    b_time = day + ' 00:00:01'
    n_time = day + ' 23:59:59'
    hisBean = HisACDMS(table)
    print(
        'METER-----table:', table, '--start:', b_time, '--end:', n_time,'--sname:',sname)

    chag = chag_l
    disg = disg_l
    c1 = db_con.query(hisBean.value).filter(hisBean.name == chag, hisBean.dts_s <= b_time).order_by(
        hisBean.dts_s.desc()).first()
    if not c1:
        c1 = db_con.query(hisBean.value).filter(hisBean.name == chag, hisBean.dts_s >= b_time).order_by(
            hisBean.dts_s.asc()).first()
    c2 = db_con.query(hisBean.value).filter(hisBean.name == chag, hisBean.dts_s <= n_time).order_by(
        hisBean.dts_s.desc()).first()

    d1 = db_con.query(hisBean.value).filter(hisBean.name == disg, hisBean.dts_s <= b_time).order_by(
        hisBean.dts_s.desc()).first()
    if not d1:
        d1 = db_con.query(hisBean.value).filter(hisBean.name == disg, hisBean.dts_s >= b_time).order_by(
            hisBean.dts_s.asc()).first()
    d2 = db_con.query(hisBean.value).filter(hisBean.name == disg, hisBean.dts_s <= n_time).order_by(
        hisBean.dts_s.desc()).first()
    print('c1=', c1, '--c2=', c2, '--d1=', d1, '--d2=', d2)

    if db=='zgtian':
        if c2 and c1:
            chag_dl = round((c2[0] - c1[0]), 2)
        if d1 and d2:
            disg_dl = round((d2[0] - d1[0]), 2)
    elif db=='ygzhen':
        if c2 and c1:
            chag_dl = round((c2[0] - c1[0]) * 8000, 2)
        if d1 and d2:
            disg_dl = round((d2[0] - d1[0]) * 8000, 2)
    elif db == 'guizhou':
        if c2 and c1:
            chag_dl = round((c2[0] - c1[0]) * 1650000, 2)
        if d1 and d2:
            disg_dl = round((d2[0] - d1[0]) * 1650000, 2)
    elif db == 'ygqn':
        if c2 and c1:
            chag_dl = round((c2[0] - c1[0]) *2640000 , 2)
        if d1 and d2:
            disg_dl = round((d2[0] - d1[0]) *2640000 , 2)
    elif db == 'shgyu':
        if c2 and c1:
            chag_dl = round((c2[0] - c1[0]) *84000 , 2)
        if d1 and d2:
            disg_dl = round((d2[0] - d1[0]) *84000 , 2)

    if ti == 10:
        return chag_dl, disg_dl
    else:
        if chag_dl:
            lv = disg_dl / chag_dl
        if chag_dl < 0:
            chag_dl = 0
            lv = 0
        if disg_dl < 0:
            disg_dl = 0
            lv = 0
        if lv > 1:
            lv = 1
        user_session.merge(FReport(name=sname, jf_chag=chag_dl, jf_disg=disg_dl, fd_chag=0, fd_disg=0, pd_chag=0, pd_disg=0,
                                   gd_chag=0, gd_disg=0, day=day, ratio=float(lv), op_ts=timeUtils.getNewTimeStr(),
                                   cause=1))

        user_session.commit()
        user_session.close()
    db_con.close()
def frozeDayaByTime(startTime, endTime):
    days = timeUtils.dateToDataList(startTime, endTime)  # 计算间隔的天
    for day in days:
        calculation(day)

def calculation(day=None):
    # days = timeUtils.dateToDataList('2024-07-21', '2024-07-23')  # 计算间隔的天
    # for day in days:
    #     print (1111111111111,day)
    if not day:
        day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
        table = 'ods_r_measure1'
        # 永臻关口表数据；总充电；总放电
        frozeMeterDataDay(day, table, 'ygzhen', ygzhen1_session, 'tfStygzhen1.EMS.MET.Et_neg1',
                          'tfStygzhen1.EMS.MET.Et_pos1',
                          'tfStygzhen1.EMS.MET.')

        # 中天关口表数据；总充电；总放电
        frozeMeterDataDay(day, table, 'zgtian', mzgtian_session, 'tfStzgtian1meter.Meter.MeasureA8_M2_NAT',
                          'tfStzgtian1meter.Meter.MeasureA8_M2_PAT', 'tfStzgtian1.EMS.MET.')

        # 东睦
        if not day:
            day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
        logging.info('dongmu==================================================')
        for all_data in all_tf_data_:
            frozeDongMuDataDay_(day, all_data[1], 'r_measure', all_data[3], all_data[4])

        # 贵州关口表数据；总充电；总放电
        frozeMeterDataDay(day, table, 'guizhou', guizhou1_session, 'guizhou.EMS.MET.Et_neg1','guizhou.EMS.MET.Et_pos1','guizhou.EMS.MET.')
        # 阳泉关口表数据；总充电；总放电
        frozeMeterDataDay(day, table, 'ygqn', ygqn7_session, 'ygqn.EMS.MET.Et_neg1','ygqn.EMS.MET.Et_pos1','ygqn.EMS.MET.')
        #上虞关口表数据；总充电；总放电
        frozeMeterDataDay(day, table, 'shgyu', shgyu_session, 'shgyu.EMS.MET.Et_pos1', 'shgyu.EMS.MET.Et_neg1','shgyu.EMS.MET.')

        #年数据
        day_ = day[:4] + '-01-01'
        calculation_1(day_)
def calculation_10(day=None):
    '''隔10分钟执行'''
    now_time=timeUtils.getNewTimeStr()
    sh_=now_time[11:13]
    sh_list=['01','02','03','04','05']
    if sh_ not in sh_list:
        day = now_time[:10]
        table = 'ods_r_measure1'
        print (table,day)
        dic_1={}
        dic_list=[]
        for all_data in all_datas:
            chag_s, disg_s=frozeDataDay_10(day, all_data[1], table, all_data[3], 'DisgCapy', 'ChagCapy', soc, 'TP', all_data[4], all_data[5])
            if all_data[4] not in dic_1.keys():
                dic_1[all_data[4]] = {'chag':chag_s,'disg':disg_s}
            else:
                dic_1[all_data[4]]= {'chag':dic_1[all_data[4]]['chag']+chag_s,'disg':dic_1[all_data[4]]['disg']+disg_s}
        # 调峰电站
        for all_data in all_tf_data:
            if all_data[4]=='houma':
                chag_s, disg_s=frozeDataDay_10(day, all_data[1], table, all_data[3], 'ACDayDisgCap', 'ACDayChagCap', 'BmsBSOC', 'AC_TotlPF',
                             all_data[4], all_data[5])
                if all_data[4] not in dic_1.keys():
                    dic_1[all_data[4]] = {'chag': chag_s, 'disg': disg_s}
                else:
                    dic_1[all_data[4]] = {'chag': dic_1[all_data[4]]['chag'] + chag_s,
                                          'disg': dic_1[all_data[4]]['disg'] + disg_s}
        # 保电
        for all_data in all_baodian_data:
            chag_s, disg_s=frozeDataDay_10(day, all_data[1], table, all_data[3], 's.DisgCapyDaly', 's.ChagCapyDaly', 's.M1BatSoc', 'TP',all_data[4], all_data[5])
            if all_data[4] not in dic_1.keys():
                dic_1[all_data[4]] = {'chag': chag_s, 'disg': disg_s}
            else:
                dic_1[all_data[4]] = {'chag': dic_1[all_data[4]]['chag'] + chag_s,
                                      'disg': dic_1[all_data[4]]['disg'] + disg_s}

        # 大同PCS
        for all_data in datong_data:
            chag_s, disg_s = frozeDataDay_10(day, all_data[1], table, all_data[3], 'DayDisgEle','DayChagEle', 'SysSoc', 'TF',all_data[4], all_data[5])
            if all_data[4] not in dic_1.keys():
                dic_1[all_data[4]] = {'chag': chag_s, 'disg': disg_s}
            else:
                dic_1[all_data[4]] = {'chag': dic_1[all_data[4]]['chag'] + chag_s,'disg': dic_1[all_data[4]]['disg'] + disg_s}
        # 永臻关口表数据；总充电；总放电
        chag_s, disg_s=frozeMeterDataDay(day, table, 'ygzhen', ygzhen1_session, 'tfStygzhen1.EMS.MET.Et_pos1','tfStygzhen1.EMS.MET.Et_neg1','tfStygzhen1.EMS.MET.',ti=10)
        if 'ygzhen' not in dic_1.keys():
            dic_1['ygzhen'] = {'chag': chag_s, 'disg': disg_s}
        else:
            dic_1['ygzhen'] = {'chag': dic_1['ygzhen']['chag'] + chag_s,
                                  'disg': dic_1['ygzhen']['disg'] + disg_s}
        # 中天关口表数据；总充电；总放电
        chag_s, disg_s=frozeMeterDataDay(day, table, 'zgtian', mzgtian_session, 'tfStzgtian1meter.Meter.MeasureA8_M2_NAT','tfStzgtian1meter.Meter.MeasureA8_M2_PAT', 'tfStzgtian1.EMS.MET.',ti=10)
        if 'zgtian' not in dic_1.keys():
            dic_1['zgtian'] = {'chag': chag_s, 'disg': disg_s}
        else:
            dic_1['zgtian'] = {'chag': dic_1['zgtian']['chag'] + chag_s,
                                  'disg': dic_1['zgtian']['disg'] + disg_s}
        # 贵州关口表数据；总充电；总放电
        chag_s, disg_s=frozeMeterDataDay(day, table, 'guizhou', guizhou1_session, 'guizhou.EMS.MET.Et_pos1', 'guizhou.EMS.MET.Et_neg1','guizhou.EMS.MET.',ti=10)
        if 'guizhou' not in dic_1.keys():
            dic_1['guizhou'] = {'chag': chag_s, 'disg': disg_s}
        else:
            dic_1['guizhou'] = {'chag': dic_1['guizhou']['chag'] + chag_s,
                                  'disg': dic_1['guizhou']['disg'] + disg_s}
        # 阳泉关口表数据；总充电；总放电
        chag_s, disg_s=frozeMeterDataDay(day, table, 'ygqn', ygqn7_session, 'ygqn.EMS.MET.Et_pos1', 'ygqn.EMS.MET.Et_neg1','ygqn.EMS.MET.',ti=10)
        if 'ygqn' not in dic_1.keys():
            dic_1['ygqn'] = {'chag': chag_s, 'disg': disg_s}
        else:
            dic_1['ygqn'] = {'chag': dic_1['ygqn']['chag'] + chag_s,
                                  'disg': dic_1['ygqn']['disg'] + disg_s}
        # 上虞关口表数据；总充电；总放电
        chag_s, disg_s=frozeMeterDataDay(day, table, 'shgyu', shgyu_session, 'shgyu.EMS.MET.Et_pos1', 'shgyu.EMS.MET.Et_neg1','shgyu.EMS.MET.',ti=10)
        if 'shgyu' not in dic_1.keys():
            dic_1['shgyu'] = {'chag': chag_s, 'disg': disg_s}
        else:
            dic_1['shgyu'] = {'chag': dic_1['shgyu']['chag'] + chag_s,
                                  'disg': dic_1['shgyu']['disg'] + disg_s}

        dic_list.append(dic_1)
        pages = user_session.query(StationR).all()
        for d in list(dic_list[0].keys()):
            for p in pages:
                if d == p.station_name:
                    p.ChagCapy = ('%.0f' % dic_list[0][d]['chag'])
                    p.DisgCapy = ('%.0f' % dic_list[0][d]['disg'])
                    print ('table:', table, '--day:', day, '--name:', p.station_name, p.ChagCapy, p.DisgCapy)

        user_session.commit()
        user_session.close()
def calculation_1(day):
    # freport = user_session.query(FReport).filter(FReport.cause == 1).all()
    for d in db_:
        freport = get_report_data(d)
        if freport:
            year_3(d, freport,day)
    # if freport:
    #     for d in db_:
    #         if d == 'bodian':
    #             year_3(d, freport,day)
    #
    #         else:
    #             year_3(d, freport,day)
    user_session.commit()
    user_session.close()


def get_report_data(db):
    freport = user_session.query(FReport.jf_chag, FReport.jf_disg, FReport.fd_chag, FReport.fd_disg, FReport.pd_chag,
                                 FReport.pd_disg, FReport.gd_chag, FReport.gd_disg).filter(FReport.cause == 1, FReport.name.like('%' + db + '%')).all()
    freport = [
        {
            'jf_chag': row[0],
            'jf_disg': row[1],
            'fd_chag': row[2],
            'fd_disg': row[3],
            'pd_chag': row[4],
            'pd_disg': row[5],
            'gd_chag': row[6],
            'gd_disg': row[7]
        }
        for row in freport
    ]
    return freport

def year_3(d, freport,day):
    disg = 0.0  # #放电量
    dchag = 0.0  # #充电量
    for f in freport:
        disg_ = np.sum(eval(f['jf_disg'])) + np.sum(eval(f['fd_disg'])) + np.sum(eval(f['pd_disg'])) + np.sum(
            eval(f['gd_disg']))
        if disg_ < 100000:
            disg = disg + disg_

        dchag_ = np.sum(eval(f['jf_chag'])) + np.sum(eval(f['fd_chag'])) + np.sum(eval(f['pd_chag'])) + np.sum(
            eval(f['gd_chag']))
        if dchag_ < 100000:
            dchag = dchag + dchag_

    # for f in freport:
    #     if d in f.name:
    #         disg_ = np.sum(eval(f.jf_disg)) + np.sum(eval(f.fd_disg)) + np.sum(eval(f.pd_disg)) + np.sum(
    #             eval(f.gd_disg))
    #         if disg_ < 100000:
    #             disg = disg + disg_
    #
    #         dchag_ = np.sum(eval(f.jf_chag)) + np.sum(eval(f.fd_chag)) + np.sum(eval(f.pd_chag)) + np.sum(
    #             eval(f.gd_chag))
    #         if dchag_ < 100000:
    #             dchag = dchag + dchag_
    if dchag == 0:
        lv = 0
    else:
        lv = disg / dchag
        if lv > 1:
            lv = 1
    if d == 'bodian':
        user_session.merge(FReport(name='baodian', jf_chag=str(float('%.2f'%(dchag))), jf_disg=str(float('%.2f'%(disg))), fd_chag='0', fd_disg='0', pd_chag='0',
                                   pd_disg='0', gd_chag='0', gd_disg='0', day=day, ratio=float(lv),
                                   op_ts=timeUtils.getNewTimeStr(), cause='3'))
    else:
        user_session.merge(FReport(name=d, jf_chag=str(float('%.2f'%(dchag))), jf_disg=str(float('%.2f'%(disg))), fd_chag='0', fd_disg='0', pd_chag='0',
                        pd_disg='0', gd_chag='0', gd_disg='0', day=day, ratio=float(lv),
                        op_ts=timeUtils.getNewTimeStr(), cause='3'))
def last_day_of_month(year,month):
    """
    获取某个月的最后一天
    """
    any_day = datetime.date(year, month, 1)
    next_month = any_day.replace(day=28) + datetime.timedelta(days=4)  # this will never fail
    return next_month - datetime.timedelta(days=next_month.day)


#idc取数
def frozeDataDay_idc(day, db_con, table, names, disg, chag, soc, F, db, maxV):
    # 冻结一天的数据
    soc_startT = ('%s 00:00:01' % day)
    soc_endT =('%s 23:59:59' % day)
    year = int(day[:4])
    month = int(day[5:7])
    for name in names:
        chag_num, disg_num, data3 = 0, 0, []  # 放电数据集，补电数据集，soc数据集
        chag_jf, chag_fd, chag_pd, chag_gd = [], [], [], []  # 充电四个阶段数据集
        disg_jf, disg_fd, disg_pd, disg_gd = [], [], [], []  # 放电四个阶段数据集

        if F == 'TF':  # 调峰站
            sname = '%s%s' % (name.replace('PCS', 'BMS').replace('Lp', 'Ay'), soc)  # SOC
        else:
            sname = '%s%s' % (name, soc)  # SOC
        values = data_sql_(sname,table, soc_startT, soc_endT)
        if values:
            for value in values:
                data3.append(value['value'])
        for dl in shiduan:
            reports = user_session.query(Report).filter(Report.station == db, Report.is_use == 1, Report.descr == dl,
                                                        Report.years == year,func.find_in_set(int(month), Report.months)).order_by(Report.start_time.asc()).all()
            if dl == '尖峰':
                c = chag_jf
                d = disg_jf
            elif dl == '峰段':
                c = chag_fd
                d = disg_fd
            elif dl == '平段':
                c = chag_pd
                d = disg_pd
            elif dl == '谷段':
                c = chag_gd
                d = disg_gd

            for re in reports:
                data1, data2 = [], []
                startT = ('%s %s' % (day, re.start_time))
                endT = ('%s %s' % (day, re.end_time))
                cname = '%s%s' % (name, chag)  # 补电
                values = data_sql_( cname, table, startT, endT)
                for value in values:
                    if db == 'houma' or db == 'guizhou':
                        if value['value'] < 655350000:
                            data1.append(value['value'])
                    else:
                        data1.append(value['value'])
                dname = '%s%s' % (name, disg)  # 放电
                values = data_sql_( dname, table, startT, endT)
                for value in values:
                    if db == 'houma' or db == 'guizhou':
                        if value['value'] < 655350000:
                            data2.append(value['value'])
                    else:
                        data2.append(value['value'])
                if db =='houma' or db == 'guizhou':
                    if data1!=[]:
                        c.append(float('%.2f'%(max(data1)-min(data1))))
                    if data2 != []:
                        d.append(float('%.2f'%(max(data2)-min(data2))))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
                else:
                    c.append(float('%.2f'%(list_sum_mm(data1, maxV))))
                    d.append(float('%.2f'%(list_sum_mm(data2, maxV))))
                    chag_num = chag_num + np.sum(c)
                    disg_num = disg_num + np.sum(d)
        fd, bd = list_soc_mm(data3)  # 放电soc，补电soc

        if db == 'houma' or db == 'datong':
            if chag_num == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num
        else:
            if chag_num == 0 or fd == 0:
                lv = float(0.0)
            else:
                lv = disg_num / chag_num / fd * bd

        if lv > 1:
            lv = 1
        if lv < 0:
            lv = 0
        print ('table:', table, '--day:', day, '--name:', name,chag_jf,disg_jf,chag_fd,disg_fd,chag_pd,disg_pd,chag_gd,disg_gd,lv)

        user_session.merge(FReport(name=name, jf_chag=str(chag_jf), jf_disg=str(disg_jf), fd_chag=str(chag_fd), fd_disg=str(disg_fd),
                    pd_chag=str(chag_pd), pd_disg=str(disg_pd),
                    gd_chag=str(chag_gd), gd_disg=str(disg_gd), day=day, ratio=float(lv),
                    op_ts=timeUtils.getNewTimeStr(), cause=1))
    user_session.commit()
    user_session.close()
def data_sql_(sname,table, soc_startT, soc_endT):
    """查询数据"""""
    conn = pool.connection()
    cursor = conn.cursor()
    try:
        # 执行SQL查询
        sql = """SELECT value
                    FROM {}
                    where measure_id = '{}'
                    and dts_s BETWEEN '{}' AND '{}'
                    order by dts_s asc
                    """.format(table,sname, soc_startT, soc_endT)
        try:
            cursor.execute(sql)
        except Exception as e:
            logging.error(e)
            return []
        # 获取查询结果
        result = cursor.fetchall()
        return result
    except Exception as e:
        logging.error(e)
        raise
    finally:
        cursor.close()
        conn.close()
def calculation_idc(day=None):
    days = timeUtils.dateToDataList('2024-07-21', '2024-07-23')  # 计算间隔的天
    for day in days:
        print (1111111111111, day)
    # if not day:
        day = timeUtils.getBeforeDay()[:10]  # 获取前一天时间 YYYY-mm-dd
        for all_data in all_datas:
            table = 'dwd_' +all_data[-2]+'_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], disg, chag, soc, 'TP', all_data[4], all_data[5])
        # 调峰电站
        for all_data in all_tf_data:
            table = 'dwd_' +all_data[-2]+'_pcs_measure'
            if all_data[4]=='houma':
                frozeDataDay_idc(day, all_data[1], table, all_data[3], 'ACTotlDisg', 'ACTotlChag', 'BmsBSOC', 'TF',
                             all_data[4], all_data[5])
            else:
                frozeDataDay_idc(day, all_data[1], table, all_data[3], 'AcDisgCapyTotl', 'AcChagCapyTotl', 'SysSOC', 'TF',
                             all_data[4], all_data[5])
        # 永臻电站根据bms计算
        for all_data in all_bms_datas:
            table = 'dwd_' +all_data[-2]+'_bc_measure'
            if all_data[4] == 'houma':
                frozeDataDay_idc(day, all_data[1], table, all_data[3], 'SysAcuDisgCapy', 'SysAcuChagCapy', 'SysSOC', 'TF',
                             all_data[4], all_data[5])
            else:
                frozeDataDay_idc(day, all_data[1], table, all_data[3], 'SysAcuDisgCapy', 'SysAcuChagCapy', 'SysSOC', 'TP',
                             all_data[4], all_data[5])
        # 保电
        for all_data in all_baodian_data:
            table = 'dwd_' +all_data[-2]+'_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 's.DisgCapyTotl', 's.ChagCapyTotl', 's.M1BatSoc', 'TP',
                         all_data[4], all_data[5])
        # 大同PCS
        for all_data in datong_data:
            table = 'dwd_' +all_data[-2]+'_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TolDisgEle', 'TolChagEle', 'SysSoc', 'TF',
                         all_data[4], all_data[5])

        # 大同BMS
        for all_data in datng_bms_datas:
            table = 'dwd_' + all_data[-2] + '_bc_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TolDisgCpt', 'TolChagCpt', 'SysSOC',
                         'TF',
                         all_data[4], all_data[5])

        # 贵州PCS
        for all_data in guizhou_data:
            table = 'dwd_' + all_data[-2] + '_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TotlDigEle', 'TotlChgEle', 'Unit.AvgSoc', 'guizhou',
                         all_data[4], all_data[5])
        #
        # # 贵州BMS
        for all_data in guizhou_bms_datas:
            table = 'dwd_' + all_data[-2] + '_bc_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'CmlDisgCapy', 'CmlChagCapy', 'Soc', 'TF',
                         all_data[4], all_data[5])

        # 阳泉PCS abc
        for all_data in ygqn_data_7:
            table = 'dwd_' + all_data[-2] + '_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TlAcDg', 'TlAcCg', 'Soc', 'TF',
                         all_data[4], all_data[5])

        # 阳泉PCS d
        for all_data in ygqn_data_8:
            table = 'dwd_' + all_data[-2] + '_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'ElTlDg', 'ElTlCg', 'BmsSoc', 'TF',
                         all_data[4], all_data[5])

        # 阳泉BMS a
        for all_data in ygqn7_bms_a:
            table = 'dwd_' + all_data[-2] + '_bms_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'DgElTl', 'CgElTl', 'Soc',
                         'TF', all_data[4], all_data[5])

        # 阳泉BMS b1
        for all_data in ygqn7_bms_b1:
            table = 'dwd_' + all_data[-2] + '_bms_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TlDig', 'TlChg', 'Soc',
                         'TF', all_data[4], all_data[5])
        # 阳泉BMS b2
        for all_data in ygqn7_bms_b2:
            table = 'dwd_' + all_data[-2] + '_bms_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TlDgEl', 'TlCgEl', 'Soc',
                         'TF', all_data[4], all_data[5])
        # 阳泉BMS c
        for all_data in ygqn7_bms_c:
            table = 'dwd_' + all_data[-2] + '_bms_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'DgElTl', 'CgElTl', 'Soc',
                         'TF', all_data[4], all_data[5])
        # 阳泉BMS d
        for all_data in ygqn_bms_datas_8:
            table = 'dwd_' + all_data[-2] + '_bms_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'CmlDisgCapy', 'CmlChagCapy', 'RkSoc',
                         'TF', all_data[4], all_data[5])

        # 上虞PCS
        for all_data in shgyu_data:
            table = 'dwd_' + all_data[-2] + '_pcs_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'AcToDg', 'AcToCg', 'Soc', 'TF',
                             all_data[4], all_data[5])
        # 上虞BMS_a
        for all_data in shgyu_bms_datas_a:
            table = 'dwd_' + all_data[-2] + '_bc_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'TolDg', 'TolCg', 'Soc', 'TF',
                             all_data[4], all_data[5])
        # 上虞BMS_b
        for all_data in shgyu_bms_datas_b:
            table = 'dwd_' + all_data[-2] + '_bc_measure'
            frozeDataDay_idc(day, all_data[1], table, all_data[3], 'CmlDisgCapy', 'CmlChagCapy', 'Soc', 'TF',
                             all_data[4], all_data[5])


def RunClearFileAndData():
    scheduler = BlockingScheduler()
    scheduler.add_job(calculation_10, 'interval', seconds=60*10)  # 10秒获取一次数据
    # scheduler.add_job(fault, 'interval', seconds=60*15)  # 每 15分钟执行一次
    scheduler.add_job(calculation_idc, 'cron', hour=1,minute=5, misfire_grace_time=600)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    # scheduler.add_job(calculation, 'cron', hour=2,misfire_grace_time=6000)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.add_job(calculation, 'cron', hour=1, minute=30,misfire_grace_time=600)  # 每天1点执行  misfire_grace_time,超过60秒就不在执行
    scheduler.start()

if __name__ == '__main__':
    # print _tableIsExist(halun_engine,HALUN_DATABASE,'r_measure202207')
    try:
    # if 1:
        opts, args = getopt.getopt(sys.argv[1:], "h", ["help", "froze", "start=", "end="])
        cmd = None
        start = None
        end = None
        for opt, arg in opts:
            if opt == "--froze":
                cmd = "froze"
            elif opt == "--start":
                start = arg
            elif opt == "--end":
                end = arg
            else:
                print ('''%s 数据冻结工具
                选项
                    -h|--help 查看帮助
                    --froze 冻结数据
                    --start 起始时刻（含）。yyyy-mm-dd 
                    --end 结束时刻（含）。yyyy-mm-dd 

                ''') % sys.argv[0]
                quit()
        if not cmd:  # 采用原始的定时任务执行
            RunClearFileAndData()
            # calculation()
        elif cmd == "froze":
            if not start:
                print ("请指定开始时刻")
                quit()
            if not end:
                print ("请指定结束时刻")
                quit()
            frozeDayaByTime(start, end)
            print ('SUCCESS')

    except Exception as e:
        print (e)