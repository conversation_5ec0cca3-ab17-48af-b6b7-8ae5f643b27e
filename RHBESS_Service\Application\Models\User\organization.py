#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Application\Models\User\organization.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-06-25 16:20:13


from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR
from Application.Models.User.organization_type import OrganizationType
import pandas as pd
from Tools.Utils.time_utils import timeUtils

class Organization(user_Base):
    u'组织关系表'
    __tablename__ = "t_organization"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True)
    
    descr = Column(String(256), nullable=False, comment=u"描述")
    parent_id = Column(Integer, nullable=True, comment=u"父节点id")
    type_id = Column(Integer, ForeignKey("c_organization_type.id"),nullable=True, comment=u"父节点id")
    op_ts = Column(DateTime, nullable=False, comment=u"时间")

    organizationType = relationship("OrganizationType", backref="organizationType")

    en_descr = Column(String(256), nullable=False, comment=u"描述-英文")
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(Organization(id=1,descr='融和元储',type_id=1,op_ts=timeUtils.getNewTimeStr()))
        user_session.commit()
        user_session.close()

    def __repr__(self):
        o_descr = self.organizationType.descr if self.organizationType else ''
        return "{'id':%s,'descr':'%s','parent_id':%s,'type_id':%s,'type_descr':'%s','op_ts':'%s','en_descr':'%s'}" % \
            (self.id,self.descr,self.parent_id,self.organizationType.id,o_descr,self.op_ts,self.en_descr)

    def deleteOrganization(self,id):
        from Application.Models.User.user import User
        try:
            users = user_session.query(User).filter(User.organization_id == id).all()  # 
            for user in users:
                user.deleteUser(user.id)  # 委托用户模块处理
          
            user_session.query(Organization).filter(Organization.id == id).delete()
            return True
        except Exception as E:
            print (E)
            user_session.rollback()
            return False

    def get_end_node(self):

        """获取末端节点方便查找子站"""
        all_organization = user_session.query(Organization).all()
        pd_organization = pd.DataFrame([[i.id, i.descr, i.type_id, i.parent_id] for i in all_organization],
        columns=['id', 'descr', 'type_id', 'parent'])
        end_org_list = self.get_end_organization(pd_organization[pd_organization['id'].isin([self.id])],
                                     pd_organization)
        return end_org_list

    def get_lower_node(self):
        """
        获取该组织下端的所有组织id
        """
        all_organization = user_session.query(Organization).all()
        pd_organization = pd.DataFrame([[i.id, i.descr, i.type_id, i.parent_id] for i in all_organization],
                                       columns=['id', 'descr', 'type_id', 'parent'])
        end_org_list = self.get_end_organization(apex=pd_organization[pd_organization['id'].isin([self.id])],
                                                df= pd_organization, end=False)
        return end_org_list

   # 获取末端线路
    def get_end_organization(self, apex, df, org_list=None, end=True):
        if org_list == None:
            org_list = []
        for indx, row in apex.iterrows():
            isin = df[df['parent'].isin([row['id']])]
            if len(isin.index):
                child = self.get_end_organization(isin, df, org_list,end)
                if not end:
                    org_list.append((row['id']))
            else:
                org_list.append((row['id']))
        return org_list

    # 获取组织的顶端
    def get_start_node(self):
        if self.parent_id is None:
            return self
        else:
            return self.get_up_node(self.parent_id)


    def get_up_node(self,parent):
        up_node = user_session.query(Organization).filter(Organization.id == parent).first()
        if not up_node:
            return
        else:
            if up_node.parent_id is None:
                return up_node
            else:
                self.get_up_node(up_node.parent_id)


