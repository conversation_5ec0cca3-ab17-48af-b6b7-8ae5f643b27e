#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2023-08-24 17:15:36
#@FilePath     : \RHBESS_Service\Tools\tianlu_middle\doris_hisdata_bean.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-25 09:05:15

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.engine.base import TwoPhaseTransaction
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy.ext.declarative import declarative_base

def HisACDMS_DORIS(_BOOKNAME):
    Model = declarative_base()  # 生成一个SQLORM基类
 
    class table_model(Model):
        __tablename__ = _BOOKNAME
 
        app_name = Column(String(256), nullable=False,primary_key=True,comment=u"app名称")
        station_name = Column(String(256), nullable=False,comment=u"网关名称")
        type = Column(Integer, nullable=False, comment=u"类型")
        time = Column(DateTime, nullable=False,primary_key=True,comment=u"绝对秒")
        utime = Column(DateTime, nullable=False,primary_key=True,comment=u"毫秒")
        data_info = Column(String(256), nullable=False,comment=u"时间")
       

    return table_model
