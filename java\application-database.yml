# 数据库连接配置
# 严格对应Python中module.ini的配置参数

database:
  # SCADA系统数据库配置 - 对应Python中的DB_*配置
  scada:
    host: *************
    port: 3306
    username: ac_read
    password: Ac#read998
    
  # 历史数据库配置 - 对应Python中的HIS_*配置
  history:
    host: ************
    port: 9300
    username: ac_read
    password: Ac#read998

  # 连接池配置 - 对应Python中的SQLAlchemy连接池配置
  pool:
    maximum-pool-size: 30        # 对应pool_size=30
    minimum-idle: 5              # 对应max_overflow=5
    connection-timeout: 10000    # 对应pool_timeout=10
    idle-timeout: 600000         # 空闲超时时间
    max-lifetime: 1800000        # 对应pool_recycle=1800
    leak-detection-threshold: 60000  # 连接泄漏检测阈值

  # 各电站数据库名称配置 - 对应Python中的各种DATABASE配置
  stations:
    # HIS电站
    his:
      history-database: mysql
      
    # HALUN电站
    halun:
      scada-database: sda_halun
      history-database: ods_tpSthalun
      
    # TAICANG电站
    taicang:
      scada-database: sda_taicang
      history-database: ods_tpSttaicang
      
    # DONGMU电站
    dongmu:
      history-database: mysql
      
    # BINHAI电站
    binhai:
      scada-databases:
        - sda_binhai1
        - sda_binhai2
      history-databases:
        - ods_tpStbinhai1
        - ods_tpStbinhai2
        
    # YGZHEN电站
    ygzhen:
      scada-databases:
        - sda_ygzhen1
        - sda_ygzhen2
      history-databases:
        - ods_tfStygzhen1
        - ods_tfStygzhen2
        
    # ZGTIAN电站
    zgtian:
      scada-databases:
        - sda_zgtian1
        - sda_zgtian2
        - sda_zgtian3
        - sda_zgtian4
        - sda_mzgtian
      history-databases:
        - ods_tfStzgtian1
        - ods_tfStzgtian2
        - ods_tfStzgtian3
        - ods_tfStzgtian4
        - ods_mzgtian
        
    # BAODIAN电站
    baodian:
      scada-databases:
        - sda_bodian1
        - sda_bodian2
        - sda_bodian3
        - sda_bodian4
        - sda_bodian5
      history-databases:
        - ods_tfStbodian1
        - ods_tfStbodian2
        - ods_tfStbodian3
        - ods_tfStbodian4
        - ods_tfStbodian5
        
    # HOUMA电站
    houma:
      scada-databases:
        - sda_houmaA1
        - sda_houmaA2
        - sda_houmaB1
        - sda_houmaB2
      history-databases:
        - ods_tfSthoumaA1
        - ods_tfSthoumaA2
        - ods_tfSthoumaB1
        - ods_tfSthoumaB2
        
    # DATONG电站
    datong:
      scada-databases:
        - sda_tc_datong1
        - sda_tc_datong2
        - sda_tc_datong3
        - sda_tc_datong4
      history-databases:
        - ods_his_tc_datong1
        - ods_his_tc_datong2
        - ods_his_tc_datong3
        - ods_his_tc_datong4
        
    # GUIZHOU电站
    guizhou:
      scada-databases:
        - sda_tc_guizhou1
        - sda_tc_guizhou2
        - sda_tc_guizhou3
        - sda_tc_guizhou4
        - sda_tc_guizhou5
        - sda_tc_guizhou6
        - sda_tc_guizhou7
        - sda_tc_guizhou8
      history-databases:
        - ods_his_tc_guizhou1
        - ods_his_tc_guizhou2
        - ods_his_tc_guizhou3
        - ods_his_tc_guizhou4
        - ods_his_tc_guizhou5
        - ods_his_tc_guizhou6
        - ods_his_tc_guizhou7
        - ods_his_tc_guizhou8
        
    # YGQN电站
    ygqn:
      scada-databases:
        - sda_tc_ygqn7_ac
        - sda_tc_ygqn8_d
      history-databases:
        - ods_his_tc_ygqn7
        - ods_his_tc_ygqn8
        
    # SHGYU电站
    shgyu:
      scada-database: sda_tc_shgyu
      history-database: ods_his_tc_shgyu
      
    # TAICGXR电站 (太仓中集)
    taicgxr:
      history-databases:
        - odsHisTfYlTczj
        - odsHisTfYlTczjG
        
    # TCZJ电站 (太仓中集历史数据查询)
    tczj:
      history-databases:
        - odsHisTfYlTczj
        - odsHisTfYlTczjG
        
    # SIKLY电站 (赛科利历史数据查询)
    sikly:
      history-databases:
        - odsHisTfYlSikly
        - odsHisTfYlSiklyG

# 排除电站列表 - 对应Python中的exclude_station
exclude-stations:
  - NBLS001
  - SPECIAL_STATION_1
  - SPECIAL_STATION_2

# 特殊电站配置 - 对应Python中的特殊处理逻辑
special-stations:
  # NBLS001电站的特殊数据项映射
  nbls001:
    data-item-mapping:
      PAE: CuDis
      NAE: CuCha
      
  # HOUMA电站的特殊设备标识映射
  houma:
    device-mapping:
      A1: 0
      A2: 1
      B1: 2
      B2: 3
      
  # YGQN电站的特殊路由规则
  ygqn:
    routing-rules:
      contains-ygqn-d: 2
      default: 1

# 应用场景配置 - 对应Python中的application_scenario
application-scenarios:
  peak-valley-arbitrage: 0  # 峰谷套利，使用5分钟间隔
  frequency-regulation: 1   # 调频，使用1分钟间隔
  default: 1               # 默认使用1分钟间隔

# 时间间隔配置
time-intervals:
  peak-valley: 5    # 峰谷套利用5分钟
  default: 1        # 其他用1分钟

# 数据类型映射 - 对应Python中的ty参数
data-types:
  measure: 1      # 测量量
  status: 2       # 状态量
  cumulant: 3     # 累积量
  discrete: 4     # 离散量

# 表名映射 - 对应Python中的table_name逻辑
table-names:
  1: measure      # 测量量表
  2: status       # 状态量表
  3: cumulant     # 累积量表
  4: discrete     # 离散量表

# 聚合计算类型 - 对应Python中的compute参数
compute-types:
  none: 1           # 无聚合
  average: 2        # 算数平均值
  max: 3           # 最大值
  min: 4           # 最小值
  difference: 5     # 差值

# 设备类型映射 - 对应Python中的设备类型过滤
device-types:
  all: 0           # 全部设备
  ammeter: 2       # 电量表
  pcs: 3           # 储能变流器
  battery: 4       # 电池簇
  other: 5         # 其他设备

# 语言配置
languages:
  chinese: zh
  english: en
  default: zh

# MinIO配置 - 对应Python中的MinIO上传
minio:
  endpoint: http://minio.example.com
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: download
  region: us-east-1

# Excel文件配置
excel:
  max-rows: 1000000      # 最大行数
  timeout-seconds: 300   # 生成超时时间
  temp-directory: /tmp   # 临时目录

# 日志配置
logging:
  level:
    com.robestec.analysis.config.DatabaseConnectionRouter: DEBUG
    com.robestec.analysis.service.impl.HistoryDataQueryServiceImpl: DEBUG
