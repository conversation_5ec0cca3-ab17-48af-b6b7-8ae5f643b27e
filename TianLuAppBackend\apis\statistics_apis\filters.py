from django_filters import rest_framework as filters

from apis.user.models import CustomIncome


class CustomIncomeFilter(filters.FilterSet):
    project = filters.CharFilter(field_name='project')
    income_month = filters.CharFilter(field_name='income_month')
    income_type = filters.CharFilter(field_name='income_type')

    class Meta:
        model = CustomIncome
        fields = ['project', 'income_month', 'income_type']
