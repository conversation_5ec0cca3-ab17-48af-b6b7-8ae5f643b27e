#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-26 15:37:13
#@FilePath     : \RHBESS_Service\Application\Models\User\__init__.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-23 14:10:33


def create_all():
    u'初始化所有表'
    # 告警表
    from Application.Models.User.manufacturer import Manufacturer
    from Application.Models.User.organization_type import OrganizationType
    from Application.Models.User.organization import Organization
    from Application.Models.User.role import Role
    from Application.Models.User.authority import Authority
    from Application.Models.User.role_authority import RoleAuthority
    from Application.Models.User.user import User
    from Application.Models.User.page import Page
    from Application.Models.User.bit import Bit
    from Application.Models.User.page_data import PageData
    from Application.Models.User.event_alarm_type import EventAlarmType
    from Application.Models.User.event import Event
    from Application.Models.User.event_r import EventR
    from Application.Models.User.alarm_r import AlarmR
    from Application.Models.User.chars import Charts
    from Application.Models.User.sys_info import SysInfo
    from Application.Models.User.sys_info_r import SysInfosR
    from Application.Models.User.report import Report
    from Application.Models.User.report_f import FReport
    from Application.Models.User.station import Station
    from Application.Models.User.for_zhil_coupleback import ForCouplebackR
    from Application.Models.User.custom_report_t import ReportCustom
    from Application.Models.User.first_page_data import FirstPageDatas
   
    from Application.Models.User.fault import Fault
    from Application.Models.User.authority_new import AuthorityNew

    from Application.Models.User.ele_price_t import ElePrice
    from Application.Models.User.equipmen_t import Equipment
    from Application.Models.User.project_info_t import ProjectInfo
    from Application.Models.User.remote_control import RemoteControl
    from Application.Models.User.remote_letter_t import RemoteLetter
    from Application.Models.User.remote_modula import RemoteModula
    from Application.Models.User.remote_tele_t import RemoteTele
    from Application.Models.User.province_c import Province
    from Application.Models.User.city_c import City
    from Application.Models.User.device_library_t import DeviceLibrary
    from Application.Models.User.device_annex_t import DeviceAnnex
    from Application.Models.User.device_del_table_t import DeviceDelTable
    from Application.Models.User.report_bms_binhai_f import FReportBmsbinhai
    from Application.Models.User.report_pcs_f import FReportPcs
    from Application.Models.User.report_bms_baodian_f import FReportBmsBaodian
    from Application.Models.User.report_bms_dongmu_f import FReportBmsDongmu
    from Application.Models.User.report_bms_taicang_f import FReportBmsTaicang
    from Application.Models.User.report_bms_ygzhen_f import FReportBmsYgzhen
    from Application.Models.User.report_bms_zgtian_f import FReportBmsZgtian
    from Application.Models.User.report_bms_halun_f import FReportBmsHalun
    from Application.Models.User.data_item_t import DataItem
    from Application.Models.User.equipment_number_t import EquipmentNumbe
    from Application.Models.User.operation_report_t import ReportOperation
    from Application.Models.User.report_bms_houma_f import FReportBmsHouma



    Manufacturer.init()
    OrganizationType.init()
    Organization.init()
    Role.init()
    Authority.init()
    RoleAuthority.init()
    User.init()
    Page.init()
    Bit.init()
    PageData.init()
    EventAlarmType.init()
    Event.init()
    EventR.init()
    AlarmR.init()
    Charts.init()
    SysInfo.init()
    SysInfosR.init()
    Report.init()
    FReport.init()
    Station.init()
    ForCouplebackR.init()
    ReportCustom.init()
    FirstPageDatas.init()
    
    Fault.init()

    AuthorityNew.init()

    ElePrice.init()
    Equipment.init()
    ProjectInfo.init()
    RemoteControl.init()
    RemoteLetter.init()
    RemoteModula.init()
    RemoteTele.init()
    Province.init()
    City.init()
    DeviceLibrary.init()
    DeviceAnnex.init()
    DeviceDelTable.init()
    FReportBmsbinhai.init()
    FReportPcs.init()
    FReportBmsZgtian.init()
    FReportBmsDongmu.init()
    FReportBmsBaodian.init()
    FReportBmsYgzhen.init()
    FReportBmsTaicang.init()
    FReportBmsHalun.init()
    DataItem.init()
    EquipmentNumbe.init()
    ReportOperation.init()
    FReportBmsHouma.init()






    


