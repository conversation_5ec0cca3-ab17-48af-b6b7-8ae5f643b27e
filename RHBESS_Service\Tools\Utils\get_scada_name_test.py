#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-07-05 17:28:02
#@FilePath     : \RHBESS_Service\Tools\Utils\get_scada_name_test.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-05-24 18:36:33


from email.quoprimime import body_encode
from sqlalchemy import func
from Tools.DB.mysql_scada import sessionmaker, scada_engine, scada_session
import requests
import json
import urllib

from Application.Models.PointTable.t_status import StatusPT

def fun1():
    
    all = scada_session.query(StatusPT).filter(StatusPT.name.like('tpSthalun.PcsSt2.Lp1.SysFW2%')).all()
    name = ''
    for a in all:
        name = '%s#%s'%(name,a.name)
    print (name)

def post_test():
    header = {
        "accept":"*/*",
        "user-agent":"ApiPOST Runtime +https://www.apipost.cn",
        # "Content-Type": "application/x-www-form-urlencoed",
        "accept-encoding":"gzip, deflate, br",
        "accept-language":"zh-CN",
        "Connection":"keep-alive",
        "Bean":"bf05064fd590bcacee27bd089cf11ebd",
    }
    url = "http://***************:19094/HisData/DcContStatus"
    s = "8D/dEvOHIU2L01IoBlOcpYu8HZArHJEY1lloh2hie3+HyDifzFWEbIfDfu06EU4C"
    post_json = {"data":s}
    # payload = urlunsplit("http://***************:19094/RealData/DcContStatus?data="+str(post_json))
    # print 'payload:',payload
    s1 = "ofUlpJdbPZgqu0zj/Tnp6TIe0hUCMDJpJERTRrOD1sOyWcanbV8Dyir10yXiF7LPHCgKmTGSH5pBD+t1XaVULHNZUAvZ3NkL+hB9R1pE675vs+4miYYLGZQOQUnAKX+z5+ajkwuE2em4aaEiQjJasjDghmQ234A0zWzXbJPrFg0QSBOAXo0WOrnwGWNugBbcaNNTsBs5aJDH9daZbkEzFg=="
    zhi_data = {"data":s1}
    ss = urllib.urlencode(post_json)
    # r1 = requests.post("http://********:82/yc/HisData/faultIgnore",data=zhi_data,headers=header)
    # print 'r1********',r1.text
    
    r2 = requests.post(url,headers=header,data=post_json)
    print ('r2-----------',r2.text)


post_test()


