#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-04-28 13:33:54
#@FilePath     : \emot_pjt_rh\RHBESS_Service\Tools\TimeTask\timing_alarm_clear.py
#@Email        : <EMAIL>
#@LastEditTime : 2024-12-24 15:55:22

import sys,os,getopt
import json
import logging
logging.basicConfig()
basepath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
work_dir = os.path.dirname(basepath)
sys.path.append(work_dir)
from Tools.DB.mysql_user import user_session
from Application.Models.User.event_r import EventR
from Application.Models.User.alarm_r import AlarmR
from apscheduler.schedulers.blocking import BlockingScheduler

from timedtask_log import app_log

def RunClearFileAndData():
    scheduler = BlockingScheduler()
    # scheduler.add_job(calculation, 'interval', seconds=10)  # 10秒获取一次数据
    # scheduler.add_job(delExceed, 'interval', seconds=5,kwargs={'station':station})  # 每 15分钟执行一次 minutes

    scheduler.add_job(delExceed, 'cron',  hour=4)  # 每天4点执行
    scheduler.start()


def delExceed():
    '''
    删除超过一定数量的数据
    '''
    exclude_station =['halun','taicang','binhai','ygzhen','zgtian','houma','baodian','dongmu','ygqn','zhijiang','guizhou','datong','shgyu','taicgxr']
    for station in exclude_station:
        try:
            # 删除多余的事件记录
            child_event = user_session.query(EventR.id.label('id')).filter(EventR.station==station).order_by(EventR.id.desc()).limit(100).offset(800000).subquery()
            user_session.query(EventR).filter(EventR.id==child_event.c.id).delete()
            # 删除多余的告警记录
            child_alarm = user_session.query(AlarmR.id.label('id')).filter(AlarmR.station==station).order_by(AlarmR.id.desc()).limit(1000).offset(800000).subquery()
            user_session.query(AlarmR).filter(AlarmR.id==child_alarm.c.id).delete()
            
            # user_session.commit()
        except Exception as E:
            app_log.error(E)
            user_session.rollback()
    user_session.commit()
        
if __name__ == '__main__':
    RunClearFileAndData()
    
  
    

