package com.robestec.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.robestec.analysis.entity.ProjectPack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 项目包Mapper
 * 对应Python中的ProjectPack相关数据库操作
 */
@Mapper
public interface ProjectPackMapper extends BaseMapper<ProjectPack> {

    /**
     * 根据用户ID查询项目包列表
     * 对应Python中ProjectPackList方法的查询逻辑
     */
    @Select("SELECT * FROM t_project_pack " +
            "WHERE user_id = #{userId} AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<ProjectPack> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和名称查询项目包
     * 对应Python中的重复性检查逻辑
     */
    @Select("SELECT * FROM t_project_pack " +
            "WHERE user_id = #{userId} AND name = #{name} AND is_use = 1")
    ProjectPack selectByUserIdAndName(@Param("userId") Long userId, @Param("name") String name);

    /**
     * 根据用户ID统计项目包数量
     */
    @Select("SELECT COUNT(*) FROM t_project_pack " +
            "WHERE user_id = #{userId} AND is_use = 1")
    int countByUserId(@Param("userId") Long userId);

    /**
     * 根据名称模糊查询项目包
     */
    @Select("SELECT * FROM t_project_pack " +
            "WHERE name LIKE CONCAT('%', #{name}, '%') AND is_use = 1 " +
            "ORDER BY create_time DESC")
    List<ProjectPack> selectByNameLike(@Param("name") String name);

    /**
     * 根据ID和用户ID查询项目包（权限验证）
     */
    @Select("SELECT * FROM t_project_pack " +
            "WHERE id = #{id} AND user_id = #{userId} AND is_use = 1")
    ProjectPack selectByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
}
