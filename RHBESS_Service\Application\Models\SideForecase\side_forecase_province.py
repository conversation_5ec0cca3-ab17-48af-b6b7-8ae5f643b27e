#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-05-18 08:53:09
#@FilePath     : \RHBESS_Service\Application\Models\SideForecase\side_forecase_province.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-08-24 09:36:05


from multiprocessing.sharedctypes import Value
from sqlalchemy.engine.base import TwoPhaseTransaction
from Tools.DB.mysql_user import user_Base,user_session
from Tools.DB.mysql_user import Integer, String, Column, relationship, ForeignKey, DateTime, CHAR, Float, VARCHAR,Boolean
from Application.Models.SideForecase.side_forecase_area import ForecaseArea

class ForecaseProvince(user_Base):
    u'用户侧预算省份划分'
    __tablename__ = "t_side_forecase_province"
    id = Column(Integer, nullable=False, autoincrement=True, primary_key=True,comment=u"主键")
    province = Column(VARCHAR(256), nullable=False, comment=u"省份/直辖市")
    en_province = Column(VARCHAR(256), nullable=True, comment=u"省份/直辖市")
    area_id = Column(Integer, ForeignKey("t_side_forecase_area.id"),nullable=False, comment=u"所属区域")
    power_grid = Column(VARCHAR(256), nullable=True,comment=u"所属电网")
    is_use = Column(CHAR(1), nullable=False,server_default='1',comment=u"是否使用1是0否")
    index = Column(CHAR(3), nullable=False,comment=u"排序索引")
    area_province = relationship("ForecaseArea",backref='area_province',foreign_keys=[area_id])

    
    @classmethod
    def init(cls):
        user_Base.metadata.create_all()
        user_session.merge(ForecaseProvince(id=1,province='北京市',area_id=1,power_grid='国家电网',is_use=1,index=1));
        user_session.merge(ForecaseProvince(id=2,province='天津市',area_id=1,power_grid='国家电网',is_use=1,index=2));
        user_session.merge(ForecaseProvince(id=3,province='河北省',area_id=1,power_grid='国家电网',is_use=1,index=3));
        user_session.merge(ForecaseProvince(id=4,province='山西省',area_id=1,power_grid='国家电网',is_use=1,index=4));
        user_session.merge(ForecaseProvince(id=5,province='山东省',area_id=1,power_grid='国家电网',is_use=1,index=5));
        user_session.merge(ForecaseProvince(id=6,province='内蒙古（东部）',area_id=1,power_grid='国家电网',is_use=1,index=6));

        user_session.merge(ForecaseProvince(id=7,province='辽宁省',area_id=2,power_grid='国家电网',is_use=1,index=7));
        user_session.merge(ForecaseProvince(id=8,province='吉林省',area_id=2,power_grid='国家电网',is_use=1,index=8));
        user_session.merge(ForecaseProvince(id=9,province='黑龙江省',area_id=2,power_grid='国家电网',is_use=1,index=9));

        user_session.merge(ForecaseProvince(id=10,province='上海市',area_id=3,power_grid='国家电网',is_use=1,index=10));
        user_session.merge(ForecaseProvince(id=11,province='江苏省',area_id=3,power_grid='国家电网',is_use=1,index=11));
        user_session.merge(ForecaseProvince(id=12,province='浙江省',area_id=3,power_grid='国家电网',is_use=1,index=12));
        user_session.merge(ForecaseProvince(id=13,province='安徽省',area_id=3,power_grid='国家电网',is_use=1,index=13));
        user_session.merge(ForecaseProvince(id=14,province='福建省',area_id=3,power_grid='国家电网',is_use=1,index=14));

        user_session.merge(ForecaseProvince(id=15,province='江西省',area_id=4,power_grid='国家电网',is_use=1,index=15));
        user_session.merge(ForecaseProvince(id=16,province='河南省',area_id=4,power_grid='国家电网',is_use=1,index=16));
        user_session.merge(ForecaseProvince(id=17,province='湖北省',area_id=4,power_grid='国家电网',is_use=1,index=17));
        user_session.merge(ForecaseProvince(id=18,province='湖南省',area_id=4,power_grid='国家电网',is_use=1,index=18));

        user_session.merge(ForecaseProvince(id=19,province='重庆市',area_id=5,power_grid='国家电网',is_use=1,index=19));
        user_session.merge(ForecaseProvince(id=20,province='四川省',area_id=5,power_grid='国家电网',is_use=1,index=20));

        user_session.merge(ForecaseProvince(id=21,province='陕西省',area_id=6,power_grid='国家电网',is_use=1,index=21));
        user_session.merge(ForecaseProvince(id=22,province='甘肃省',area_id=6,power_grid='国家电网',is_use=1,index=22));
        user_session.merge(ForecaseProvince(id=23,province='青海省',area_id=6,power_grid='国家电网',is_use=1,index=23));
        user_session.merge(ForecaseProvince(id=24,province='宁夏省',area_id=6,power_grid='国家电网',is_use=1,index=24));
        user_session.merge(ForecaseProvince(id=25,province='新疆维吾尔自治区',area_id=6,power_grid='国家电网',is_use=1,index=25));
        user_session.merge(ForecaseProvince(id=26,province='西藏自治区',area_id=6,power_grid='国家电网',is_use=1,index=26));

        user_session.merge(ForecaseProvince(id=27,province='广东省',area_id=7,power_grid='南方电网',is_use=1,index=27));
        user_session.merge(ForecaseProvince(id=28,province='广西壮族自治区',area_id=7,power_grid='南方电网',is_use=1,index=28));
        user_session.merge(ForecaseProvince(id=29,province='海南省',area_id=7,power_grid='南方电网',is_use=1,index=29));
        user_session.merge(ForecaseProvince(id=30,province='贵州省',area_id=7,power_grid='南方电网',is_use=1,index=30));
        user_session.merge(ForecaseProvince(id=31,province='云南省',area_id=7,power_grid='南方电网',is_use=1,index=31));
        user_session.merge(ForecaseProvince(id=32,province='内蒙古（西部）',area_id=8,power_grid='蒙西电网',is_use=1,index=32));
       
        user_session.commit()
        user_session.close()
        
    
    def __repr__(self):
        
        return "{'id':%s,'name':'%s','en_name':'%s','power_grid':'%s','is_use':%s}" % (
            self.id,self.province,self.en_province,self.power_grid,self.is_use)
        
    