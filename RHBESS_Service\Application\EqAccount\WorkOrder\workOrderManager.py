#!/usr/bin/env python
# coding=utf-8
#@Information: 工单处理
#<AUTHOR> WYJ
#@Date         : 2022-10-26 13:40:44
#@FilePath     : \RHBESS_empowerd:\em_pjt_rh\RHBESS_Service\Application\EqAccount\WorkOrder\workOrderManager.py
#@Email        : <EMAIL>
#@LastEditTime : 2022-11-09 17:32:00

import tornado.web
import datetime
from Application.Models.User.station import Station
from Tools.Utils.num_utils import translate_text, Translate_cls
from Tools.Utils.send_mail import sendMail_
from Tools.Utils.time_utils import timeUtils
from Application.Models.base_handler import BaseHandler
from Application.Models.WorkOrder.dispatch_type import DispatchType
from Application.Models.WorkOrder.dispatch_model import DispatchModel
from Application.Models.WorkOrder.dispatch_step_t import DispatchStep
from Application.Models.WorkOrder.dispatch_r import DispatchR
from Application.Models.WorkOrder.dispatch_step_r import DispatchStepR
from Application.Models.WorkOrder.dispatch_plan import DispatchPlan
import logging,json
from sqlalchemy import func,or_
from Tools.DB.mysql_user import sessionmaker, user_engine, user_session,DEBUG
from Application.Models.User.user import User

from apscheduler.schedulers.blocking import BlockingScheduler
scheduler = BlockingScheduler()

# self_station = ['taicang','binhai','ygzhen','zgtian']  # 定义自持电站
self_station = ['taicang','binhai','datong','ygqn','shgyu']  # 定义自持电站
step_names = ['准备工作内容','准备工作初步审核','准备工作最终审核','此次工作汇总','工作总结审核']
en_step_names = ['Preparation work content','Preliminary review of preparation work','Preparation work final review','Summary of this work','Work summary review']

class WorkOrderManagerIntetface(BaseHandler):

    @tornado.web.authenticated
    def get(self,kt):
        self.refreshSession()
        session = self.getOrNewSession()
        lang = self.get_argument('lang', None)  # 英文
        user_id = str(session.user['id'])  # 当前用户id
        try:
        # if 1:
            if kt == 'GetCheckUsers':
                if lang=='en':
                    d = [{'id': 126, "label": "Deng Zhiyang"}, {'id': 98, "label": "Xue Yuxin"}, {'id': 68, "label": "Liu Ming"},
                         {'id': 113, "label": "Wu Xiaolong"}, {'id': 6, "label": "Wu Han"}, {'id': 7, "label": "Sun Zengfu"}, ]
                else:
                    d = [{'id':126,"label":"邓志杨"},{'id':98,"label":"薛钰歆"},{'id':68,"label":"刘明"},{'id':113,"label":"武霄龙"},{'id':6,"label":"吴晗"},{'id':7,"label":"孙增福"},]
                return self.returnTypeSuc(d)
            elif kt == 'GetWStationList': # 工单需要所有站
                # data = [{'id':1,'descr':'全部','name':''}]
                data = []
                descr = self.get_argument('descr', None)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                filter = []
                obj = {}
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s' % (descr, pageNum, pageSize))
                if descr:
                    if lang=='en':
                        filter.append(Station.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(Station.descr.like('%' + descr + '%'))
                if user_id == '131':  # demo站用户
                    total = user_session.query(func.count(Station.id)).filter(*filter).filter(Station.id==37).scalar()
                    pages = user_session.query(Station).filter(*filter).filter(Station.id==37).order_by(Station.index.asc()).limit(
                        pageSize).offset((pageNum - 1) * pageSize).all()#查询电站other_infos里的信息
                else:
                    total = user_session.query(func.count(Station.id)).filter(*filter).filter(Station.id!=37).scalar()
                    pages = user_session.query(Station).filter(*filter).filter(Station.id!=37).order_by(Station.index.asc()).limit(
                        pageSize).offset((pageNum - 1) * pageSize).all()#查询电站other_infos里的信息
                for pag in pages:
                    o = eval(str(pag))
                    if lang == 'en':
                        replaced_data = Station.replace_en_fields(o, "")
                        o.update(replaced_data)
                    if pag.other_infos:
                        for k, v in eval(str(pag.other_infos)).items():
                            o[k] = v
                    data.append(o)
                return self.returnTotalSuc(data, total)
            elif kt == 'GetTypeList':
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = self.get_argument('isUse', 1)
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,isUse:%s'%(descr,pageNum,pageSize,isUse))
                filter,data = [DispatchType.is_use==int(isUse)] if isUse else [],[]
                if descr:
                    if lang=='en':
                        filter.append(DispatchType.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(DispatchType.descr.like('%' + descr + '%'))
                total = user_session.query(func.count(DispatchType.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchType).filter(*filter).order_by(DispatchType.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    o=eval(str(pag))
                    if lang=='en':
                        if lang == 'en':
                            replaced_data = DispatchType.replace_en_fields(o, "")
                            o.update(replaced_data)
                    data.append(o)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetModelsList':  # 获得所有模板
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = self.get_argument('isUse')
                create_user = self.get_argument('create_user',None)
                organization_id = self.get_argument('organization_id',None)
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,isUse:%s,create_user：%s,organization_id:%s'%(descr,pageNum,pageSize,isUse,create_user,organization_id))
                filter,data = [DispatchModel.is_use==int(isUse)] if isUse else [],[]
                if not create_user:  #没有用户id默认当前用户
                    session = self.getOrNewSession()
                    create_user = session.user['id']
                if id:
                    filter.append(DispatchModel.id==id)
                if descr:
                    if lang=='en':
                        filter.append(DispatchModel.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(DispatchModel.descr.like('%' + descr + '%'))
                if create_user:
                    filter.append(DispatchModel.create_user==create_user)
                if organization_id:
                    filter.append(DispatchModel.organization_id==organization_id)
                total = user_session.query(func.count(DispatchModel.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchModel).filter(*filter).order_by(DispatchModel.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    o = eval(str(pag))
                    if lang == 'en':
                        replaced_data = DispatchModel.replace_en_fields(o, "")
                        o.update(replaced_data)
                    o['remarks'] = ''
                    if pag.working_flag and pag.remarks:
                        for k,v in eval(str(pag.remarks)).items():
                            o[k] = v
                    data.append(o)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetWorkerNoMonit':  # 工单指标监控
                db = self.get_argument('db', [])  # 电站名称
                report_type = self.get_argument('report_type', None)  # 1周报，2月报
                startTime = self.get_argument('startTime', None)  # 开始时间
                endTime = self.get_argument('endTime', None)  # 结束时间
                if db != []:
                    db = json.loads(db)
                    stationnameStr = "','".join(db)
                if DEBUG:
                    logging.info('report_type:%s,startTime:%s,endTime:%s,db:%s' % (report_type, startTime, endTime, db))
                start_time = startTime[:10] + ' 00:00:00'
                end_time_ = timeUtils.returnRealTime(endTime[:10])
                end_time = end_time_ + ' 23:59:59'

                end_time_str='D'+(datetime.datetime.strptime(end_time,"%Y-%m-%d %H:%M:%S")).strftime("%Y%m%d%H%M%S")#转换成工号格式
                start_timee_str='D'+(datetime.datetime.strptime(start_time,"%Y-%m-%d %H:%M:%S")).strftime("%Y%m%d%H%M%S")#转换成工号格式

                filter_1 = [DispatchR.working_flag == 1,DispatchR.working_no<=end_time_str]#工号服务申请
                filter_2 = [DispatchR.working_flag == 2,DispatchR.working_no<=end_time_str,Station.name==DispatchR.station]#工单状态
                if db!=[]:
                    filter_1.append(DispatchR.station.in_(db)) # 工号服务申请
                    filter_2.append(DispatchR.station.in_(db))  # 工单状态
                data= {}
                data1, data2, data3, data4, data5, data6 ,data7,data8= {}, {}, {}, [], {}, [] , [], [] # 工号服务申请情况，工单状态，工单完成数量，工单完成率，工单完成天数，工单平均完成天数，工单最长完成天数,进行中工单状态分布
                d1 = user_session.query(DispatchR.status,DispatchR.working_no).filter(*filter_1).all()#工号服务申请
                if user_id == '131':  # demo站用户
                    d2 = user_session.query(DispatchR.start_time,DispatchR.plan_time,DispatchR.finish_time,DispatchR.status,DispatchR.working_no,Station.descr,Station.en_descr).filter(*filter_2).filter(Station.id==37).all()#工单
                else:
                    d2 = user_session.query(DispatchR.start_time,DispatchR.plan_time,DispatchR.finish_time,DispatchR.status,DispatchR.working_no,Station.descr,Station.en_descr).filter(*filter_2).filter(Station.id!=37).all()#工单
                
                if db != []:
                    sql = "select a.descr,count(a.descr) from t_dispatch_step a left join r_dispatch_step b on a.id = b.dispatch_step_id " \
                          "left join r_dispatch c on b.dispatch_id = c.id where b.status='1' and c.working_no<='{0}' and c.working_flag='2' and c.station in('{1}') and c.status != 'off' and c.status != 'end' " \
                          "group by a.descr ".format(end_time_str,stationnameStr)
                else:
                    sql = "select a.descr,count(a.descr) from t_dispatch_step a left join r_dispatch_step b on a.id = b.dispatch_step_id " \
                          "left join r_dispatch c on b.dispatch_id = c.id where b.status='1' and c.working_no<='{0}' and c.working_flag='2' and c.status != 'off' and c.status != 'end' " \
                          "group by a.descr ".format(end_time_str)

                conn = user_engine.raw_connection()  # 拿原生的连接
                cursor = conn.cursor()
                cursor.execute(sql)
                result = cursor.fetchall()
                cursor.close()
                conn.close()

                s_total_1=0  # 历史工号服务申请总数
                s_run_1 = 0  # 历史工号服务申请进行中
                s_end_1 = 0  # 历史工号服务申请已结束
                s_off_1 = 0  # 历史工号服务申请已关闭

                s_run_1_Z= 0# 历史工号服务申请进行中占比
                s_end_1_Z= 0# 历史工号服务申请已结束占比
                s_off_1_Z= 0# 历史工号服务申请已关闭占比


                n_total_1 = 0# 新增工号服务申请总数
                n_run_1 = 0  # 新增工号服务申请进行中
                n_end_1 = 0  # 新增工号服务申请已结束
                n_off_1 = 0  # 新增工号服务申请已关闭

                n_run_1_Z = 0  # 新增工号服务申请进行中占比
                n_end_1_Z = 0  # 新增工号服务申请已结束占比
                n_off_1_Z = 0  # 新增工号服务申请已关闭占比

                s_total_2 = 0# 历史工单总数
                s_run_2 = 0  # 历史工单进行中
                s_end_2 = 0  # 历史工单已结束
                s_off_2 = 0  # 历史工单已关闭

                s_run_2_Z = 0  # 历史工单进行中进行中占比
                s_end_2_Z = 0  # 历史工单已结束已结束占比
                s_off_2_Z = 0  # 历史工单已关闭已关闭占比

                n_total_2 = 0# 新增工单总数
                n_run_2 = 0  # 新增工单进行中
                n_end_2 = 0  # 新增工单已结束
                n_off_2 = 0  # 新增工单已关闭

                n_run_2_Z = 0  # 新增工单进行中进行中占比
                n_end_2_Z = 0  # 新增工单已结束已结束占比
                n_off_2_Z = 0  # 新增工单已关闭已关闭占比

                data1['s_run_1'] = s_run_1
                data1['s_end_1'] = s_end_1
                data1['s_off_1'] = s_off_1
                data1['s_run_1_Z'] = s_run_1_Z
                data1['s_end_1_Z'] = s_end_1_Z
                data1['s_off_1_Z'] = s_off_1_Z
                data1['n_run_1'] = n_run_1
                data1['n_end_1'] = n_end_1
                data1['n_off_1'] = n_off_1
                data1['n_run_1_Z'] = n_run_1_Z
                data1['n_end_1_Z'] = n_end_1_Z
                data1['n_off_1_Z'] = n_off_1_Z
                data2['s_run_2'] = s_run_2
                data2['s_end_2'] = s_end_2
                data2['s_off_2'] = s_off_2
                data2['s_run_2_Z'] = s_run_2_Z
                data2['s_end_2_Z'] = s_end_2_Z
                data2['s_off_2_Z'] = s_off_2_Z
                data2['n_run_2'] = n_run_2
                data2['n_end_2'] = n_end_2
                data2['n_off_2'] = n_off_2
                data2['n_run_2_Z'] = n_run_2_Z
                data2['n_end_2_Z'] = n_end_2_Z
                data2['n_off_2_Z'] = n_off_2_Z

                if d1:
                    for i in d1:
                        s_total_1 += 1
                        if i[0]!= 'off' and i[0] != 'end':
                            s_run_1+=1
                        elif i[0] == 'end':
                            s_end_1+=1
                        elif i[0] == 'off':
                            s_off_1+=1
                        if start_timee_str<=i[1]<=end_time_str:
                            n_total_1 += 1
                            if i[0] != 'off' and i[0] != 'end':
                                n_run_1 += 1
                            elif i[0] == 'end':
                                n_end_1 += 1
                            elif i[0] == 'off':
                                n_off_1 += 1

                    if s_total_1!=0:
                        s_run_1_Z=str('%.2f' % ((s_run_1/s_total_1)*100))
                        s_end_1_Z=str('%.2f' % ((s_end_1/s_total_1)*100))
                        s_off_1_Z=str('%.2f' % ((s_off_1/s_total_1)*100))
                        data1['s_run_1']=s_run_1
                        data1['s_end_1']=s_end_1
                        data1['s_off_1']=s_off_1
                        data1['s_run_1_Z']=s_run_1_Z
                        data1['s_end_1_Z']=s_end_1_Z
                        data1['s_off_1_Z']=s_off_1_Z
                    if n_total_1!=0:
                        n_run_1_Z=str('%.2f'% ((n_run_1/s_total_1)*100))
                        n_end_1_Z=str('%.2f'% ((n_end_1/s_total_1)*100))
                        n_off_1_Z=str('%.2f'% ((n_off_1/s_total_1)*100))
                        data1['n_run_1']=n_run_1
                        data1['n_end_1']=n_end_1
                        data1['n_off_1']=n_off_1
                        data1['n_run_1_Z']=n_run_1_Z
                        data1['n_end_1_Z']=n_end_1_Z
                        data1['n_off_1_Z']=n_off_1_Z
                data['data1']=data1

                list4 = []
                list6 = []
                list7 = []
                data9 = {}
                if d2:
                    for i in d2:
                        s_total_2 += 1
                        if i[3] != 'off' and i[3] != 'end':
                            s_run_2 += 1
                            if lang=='en':
                                if i[6] not in data9.keys():
                                    data9[i[6]] = 1
                                else:
                                    data9[i[6]] += 1
                            else:
                                if i[5] not in data9.keys():
                                    data9[i[5]] = 1
                                else:
                                    data9[i[5]] += 1
                        elif i[3] == 'end':
                            s_end_2 += 1
                            #完成率
                            finish_day=float('%.2f' % ((timeUtils.timeSeconds(i[0].strftime("%Y-%m-%d %H:%M:%S"),i[2].strftime("%Y-%m-%d %H:%M:%S")))/86400))
                            if lang=='en':
                                if i[6] not in data5.keys():
                                    data5[i[6]] = []
                                    data5[i[6]].append(finish_day)
                                else:
                                    data5[i[6]].append(finish_day)
                                if i[2]<=i[1]:
                                    if i[6] not in data3.keys():
                                        data3[i[6]]=1
                                    else:
                                        data3[i[6]]+=1
                                if i[6] not in data9.keys():
                                    data9[i[6]] = 1
                                else:
                                    data9[i[6]] += 1
                            else:
                                if i[5] not in data5.keys():
                                    data5[i[5]] = []
                                    data5[i[5]].append(finish_day)
                                else:
                                    data5[i[5]].append(finish_day)
                                if i[2]<=i[1]:
                                    if i[5] not in data3.keys():
                                        data3[i[5]]=1
                                    else:
                                        data3[i[5]]+=1
                                if i[5] not in data9.keys():
                                    data9[i[5]] = 1
                                else:
                                    data9[i[5]] += 1
                        elif i[3] == 'off':
                            s_off_2 += 1
                            if lang=='en':
                                if i[6] not in data9.keys():
                                    data9[i[6]] = 1
                                else:
                                    data9[i[6]] += 1
                            else:
                                if i[5] not in data9.keys():
                                    data9[i[5]] = 1
                                else:
                                    data9[i[5]] += 1
                        if start_timee_str <= i[4] <= end_time_str:
                            n_total_2 += 1
                            if i[3] != 'off' and i[3] != 'end':
                                n_run_2 += 1
                            elif i[3] == 'end':
                                n_end_2 += 1
                            elif i[3] == 'off':
                                n_off_2 += 1
                    if data3:
                        for ii in data3:
                            for s in data9:
                                if ii==s:
                                    dict4={}
                                    dict4['name']=ii
                                    dict4['work_on_time']=data3[ii]
                                    dict4['Work_count']=data9[s]
                                    if data9[s]!=0:
                                        dict4['finish_2_lv']=float('%.2f' % ((data3[ii]/data9[s])*100))
                                    else:
                                        dict4['finish_2_lv'] = 0
                                    data4.append(dict4)
                        list4 = sorted(data4, key=lambda x: x.get("finish_2_lv"), reverse=True)
                        for l in list4:
                            l['finish_2_lv']=str(l['finish_2_lv'])+'%'
                    # data['data4'] = list4
                    if data5:
                        for iii in data5:
                            dict6={}
                            dict6['name']=iii
                            dict6['avg']=float('%.2f' % (sum(data5[iii])/len(data5[iii])))
                            dict7 = {}
                            dict7['name'] = iii
                            dict7['max'] = float('%.2f' % (max(data5[iii])))
                            data6.append(dict6)
                            data7.append(dict7)
                        list6 = sorted(data6, key=lambda x: x.get("avg"), reverse=True)
                        list7 = sorted(data7, key=lambda x: x.get("max"), reverse=True)


                    if s_total_2 != 0:
                        s_run_2_Z = str('%.2f' % ((s_run_2 / s_total_2) * 100))
                        s_end_2_Z = str('%.2f' % ((s_end_2 / s_total_2) * 100))
                        s_off_2_Z = str('%.2f' % ((s_off_2 / s_total_2) * 100))
                        data2['s_run_2'] = s_run_2
                        data2['s_end_2'] = s_end_2
                        data2['s_off_2'] = s_off_2
                        data2['s_run_2_Z'] = s_run_2_Z
                        data2['s_end_2_Z'] = s_end_2_Z
                        data2['s_off_2_Z'] = s_off_2_Z
                    if n_total_2 != 0:
                        n_run_2_Z = str('%.2f' % ((n_run_2 / s_total_2) * 100))
                        n_end_2_Z = str('%.2f' % ((n_end_2 / s_total_2) * 100))
                        n_off_2_Z = str('%.2f' % ((n_off_2 / s_total_2) * 100))
                        data2['n_run_2'] = n_run_2
                        data2['n_end_2'] = n_end_2
                        data2['n_off_2'] = n_off_2
                        data2['n_run_2_Z'] = n_run_2_Z
                        data2['n_end_2_Z'] = n_end_2_Z
                        data2['n_off_2_Z'] = n_off_2_Z
                data['data2'] = data2
                data['data4'] = list4
                data['data6'] = list6
                data['data7'] = list7

                if result:
                    for l in result:
                        dict8={}
                        r = list(l)
                        if lang == 'en':
                            if r[0] == '准备工作内容':
                                r[0] = 'Executing'
                            elif r[0] == '准备工作初步审核':
                                r[0] = 'Checking'
                            elif r[0] == '准备工作最终审核':
                                r[0] = 'Approving'
                            elif r[0] == '此次工作汇总':
                                r[0] = 'Summarizing'
                            elif r[0] == '工作总结审核':
                                r[0] = 'Reviewing'
                        else:
                            if r[0] == '准备工作内容':
                                r[0] = '准备工作'
                            elif r[0] == '准备工作初步审核':
                                r[0] = '初步审核'
                            elif r[0] == '准备工作最终审核':
                                r[0] = '最终审核'
                            elif r[0] == '此次工作汇总':
                                r[0] = '工作汇总'
                            elif r[0] == '工作总结审核':
                                r[0] = '总结审核'
                        dict8['status'] = r[0]
                        dict8['num'] = r[1]
                        data8.append(dict8)
                data['data8'] = data8
                return self.returnTypeSuc(data)
            elif kt == 'GetModelStepsById':  # 根据模板获得具体步骤
                id = self.get_argument('id',None)
                data = []
                if DEBUG:
                    logging.info('id:%s'%(id))
                if not id:
                    return self.customError("无效id")
                pages = user_session.query(DispatchStep).filter(DispatchStep.model_id==id).order_by(DispatchStep.id.asc()).all()
                
                for pag in pages:
                    data.append(eval(str(pag)))
                return self.returnTypeSuc(data)
            elif kt == 'GetDispatchList':  # 获得我创建的工单GetCustomReportWeek
                id = self.get_argument('id',None)  # 模板id
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                create_user = self.get_argument('create_user',None)
                # status = self.get_argument('status',None)  # off禁用状态，end执行完状态,run进行中
                status = self.get_argument('status',None)  # 1准备工作,2初步审核,3最终审核,4工作汇总,5总结审批,6已结束,7被制作
                startTime = self.get_argument('startTime',None)  # 开始时间
                endTime = self.get_argument('endTime',None)  # 截止时间
                db = self.get_argument('db',None)  # 截止时间
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,create_user：%s,status:%s'%(descr,pageNum,pageSize,create_user,status))
                filter,data = [DispatchR.working_flag!=1],[]
                if not create_user:  #没有用户id默认当前用户
                    session = self.getOrNewSession()
                    create_user = session.user['id']
                if id:
                    filter.append(DispatchR.model_id==id)
                if descr:
                    if lang=='en':
                        filter.append(DispatchR.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(DispatchR.descr.like('%' + descr + '%'))
                if create_user and int(create_user) != -1:
                    filter.append(DispatchR.create_user==create_user)
                if status:
                    if status == 'run':
                        filter.append(DispatchR.status!='off')
                        filter.append(DispatchR.status!='end')
                    else:
                        filter.append(DispatchR.status==status)
                if startTime:
                    filter.append(DispatchR.op_ts>=startTime)
                if endTime:
                    filter.append(DispatchR.op_ts<=endTime)
                if db:
                    filter.append(DispatchR.station==db)
                
                total = user_session.query(func.count(DispatchR.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchR).filter(*filter).order_by(DispatchR.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if lang=='en':
                    result_list = self.run_list(lang)  # 获取进行中的描述
                else:
                    result_list = self.run_list()#获取进行中的描述
                for pag in pages:
                    o = eval(str(pag))
                    if lang=='en':
                        replaced_data = DispatchR.replace_en_fields(o, "")
                        o.update(replaced_data)
                    o['other'] = ''
                    if pag.working_flag and pag.other:
                        for k,v in eval(str(pag.other)).items():
                            o[k] = v
                    o['dispatch_type'] = pag.dispatch_model.dispatch_type
                    o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                    data.append(o)
                n1,n2,n3 = self._getNum('2')  # 获取待处理，待审批，抄送我的数量
                for d in data:
                    for rr in result_list:
                        if d['id']==rr['id']:
                            d['status']=rr['status']
                datas = {"data":data,"total":total,"handle_num":n1,"check_num":n2,"copy_num":n3}
                return self.returnTypeSuc(datas)
            elif kt == 'GetWorkingNoList':  # 获得我创建的工号服务申请
                id = self.get_argument('id',None)  # 模板id
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                create_user = self.get_argument('create_user',None)
                status = self.get_argument('status',None)  # off禁用状态，end执行完状态,run进行中
                startTime = self.get_argument('startTime',None)  # 开始时间
                endTime = self.get_argument('endTime',None)  # 截止时间
                db = self.get_argument('db',None)  #
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,create_user：%s,status:%s'%(descr,pageNum,pageSize,create_user,status))
                filter,data = [DispatchR.working_flag==1],[]
                if not create_user:  #没有用户id默认当前用户
                    session = self.getOrNewSession()
                    create_user = session.user['id']
                if id:
                    filter.append(DispatchR.model_id==id)
                if descr:
                    if lang=='en':
                        filter.append(DispatchR.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(DispatchR.descr.like('%' + descr + '%'))
                if create_user and int(create_user) != -1:
                    filter.append(DispatchR.create_user==create_user)
                if status:
                    if status == 'run':
                        filter.append(DispatchR.status!='off')
                        filter.append(DispatchR.status!='end')
                    else:
                        filter.append(DispatchR.status==status)
                if startTime:
                    filter.append(DispatchR.op_ts>=startTime)
                if endTime:
                    filter.append(DispatchR.op_ts<=endTime)
                if db:
                    filter.append(DispatchR.station==db)

                total = user_session.query(func.count(DispatchR.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchR).filter(*filter).order_by(DispatchR.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    o = eval(str(pag))
                    if lang == 'en':
                        replaced_data = DispatchR.replace_en_fields(o, "")
                        o.update(replaced_data)
                    o['other'] = ''
                    if pag.working_flag and pag.other:
                        for k,v in eval(str(pag.other)).items():
                            o[k] = v
                    o['dispatch_type'] = pag.dispatch_model.dispatch_type
                    o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                    workingflag = user_session.query(DispatchR).filter(DispatchR.working_no==pag.working_no,DispatchR.working_flag==2,DispatchR.status!='end',DispatchR.status!='off').first()
                    if workingflag:
                        o['working_order'] = True
                    else:
                        o['working_order'] = False
                    data.append(o)
                n1,n2,n3 = self._getNum('1')  # 获取待处理，待审批，抄送我的数量
                datas = {"data":data,"total":total,"handle_num":n1,"check_num":n2,"copy_num":n3}
                return self.returnTypeSuc(datas)
            elif kt == 'GetWorkingNoListByWorkerOrder':  # 根据工号获取工号申请详情  工单处使用
                workerNo = self.get_argument('workerNo',None)  # 工号
                if DEBUG:
                    logging.info('workerNo:%s'%(workerNo))
                filter = [DispatchR.working_flag==1,DispatchR.working_no==workerNo]
                pag = user_session.query(DispatchR).filter(*filter).first()
                o={}
                if pag:
                    if lang=='en':
                        o = eval(str(pag))
                        replaced_data = DispatchR.replace_en_fields(o, "")
                        o.update(replaced_data)
                        o['other'] = ''
                        if pag.working_flag and pag.other:
                            for k,v in eval(str(pag.en_other)).items():
                                o[k] = v
                        o['dispatch_type'] = pag.dispatch_model.dispatch_type
                        o['dispatch_descr'] = pag.dispatch_model.model_type.en_descr
                    else:
                        o = eval(str(pag))
                        o['other'] = ''
                        if pag.working_flag and pag.other:
                            for k, v in eval(str(pag.other)).items():
                                o[k] = v
                        o['dispatch_type'] = pag.dispatch_model.dispatch_type
                        o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                return self.returnTypeSuc(o)
            elif kt == 'GetWorkingNoNoOrder':  # 获得我创建的工号服务申请没有派发工单
                id = self.get_argument('id',None)  # 模板id
                descr = self.get_argument('descr',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                create_user = self.get_argument('create_user',None)
                startTime = self.get_argument('startTime',None)  # 开始时间
                endTime = self.get_argument('endTime',None)  # 截止时间
                db = self.get_argument('db',None)
                if DEBUG:
                    logging.info('descr:%s,pageNum:%s,pageSize:%s,create_user：%s'%(descr,pageNum,pageSize,create_user))
                filter,data = [DispatchR.working_flag==1,DispatchR.status!='off'],[]
                if not create_user:  #没有用户id默认当前用户
                    session = self.getOrNewSession()
                    create_user = session.user['id']
                if id:
                    filter.append(DispatchR.model_id==id)
                if descr:
                    if lang=='en':
                        filter.append(DispatchR.en_descr.like('%' + descr + '%'))
                    else:
                        filter.append(DispatchR.descr.like('%' + descr + '%'))
                if create_user and int(create_user) != -1:
                    filter.append(DispatchR.create_user==create_user)
                if startTime:
                    filter.append(DispatchR.op_ts>=startTime)
                if endTime:
                    filter.append(DispatchR.op_ts<=endTime)
                if db:
                    filter.append(DispatchR.station==db)
                working_nos = user_session.query(DispatchR.working_no).filter(DispatchR.working_no!=None,DispatchR.working_flag!=1).all()
                working_list = []
                for w in working_nos:
                    working_list.append(w[0])
                filter.append(DispatchR.working_no.not_in(working_list))
                total = user_session.query(func.count(DispatchR.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchR).filter(*filter).order_by(DispatchR.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                for pag in pages:
                    o = eval(str(pag))
                    if lang == 'en':
                        replaced_data = DispatchR.replace_en_fields(o, "")
                        o.update(replaced_data)
                    o['other'] = ''
                    if pag.working_flag and pag.other:
                        for k,v in eval(str(pag.other)).items():
                            o[k] = v
                    o['dispatch_type'] = pag.dispatch_model.dispatch_type
                    o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                    data.append(o)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetDispatchStepsById':  # 获得指定工单详情
                id = self.get_argument('id',None)
                level = int(self.get_argument('level',0))  # 0代表一个数组返回1代表按步骤5个返回
                data,step_arr = [], [[],[],[],[],[]]
                if DEBUG:
                    logging.info('id:%s'%(id))
                if not id:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                pages = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_id==id).order_by(DispatchStepR.id.asc()).all()
                if lang == 'en':
                    for pag in pages:
                        p = eval(str(pag))
                        replaced_data = DispatchStepR.replace_en_fields(p, "")
                        p.update(replaced_data)
                        if not pag.read_time:
                            pag.read_time = timeUtils.getNewTimeStr()
                        pg = user_session.query(DispatchStep).filter(DispatchStep.id==pag.dispatch_step_id).first()
                        handle_users = pag.care_user if pag.care_user else pg.handle_users

                        p['handle_users'] = str(handle_users)
                        p['check_user'] = ''
                        p['check_descr'] = ''
                        if pg.check_user:
                            p['check_user'] = pg.check_user
                            p['check_descr'] = user_session.query(User).filter(User.id==pg.check_user,User.unregister==1).first().en_name
                        p['pcontent'] = pg.en_content
                        p['pdescr'] = pg.en_descr
                        if pg.is_team == 1:
                            HUS = user_session.query(User).filter(User.id.in_(eval(str(handle_users))),User.unregister==1).all()
                        else:
                            HUS = user_session.query(User).filter(User.id==handle_users,User.unregister==1).all()
                        handle_descrs = []
                        for u in HUS:
                            handle_descrs.append(u.en_name)
                        p['handle_descrs']=','.join(handle_descrs)
                        if level == 1:
                            ind = en_step_names.index(pg.en_descr)
                            step_arr[ind].append(p)
                        else:
                            data.append(p)
                else:
                    for pag in pages:
                        if not pag.read_time:
                            pag.read_time = timeUtils.getNewTimeStr()
                        pg = user_session.query(DispatchStep).filter(DispatchStep.id == pag.dispatch_step_id).first()
                        p = eval(str(pag))
                        handle_users = pag.care_user if pag.care_user else pg.handle_users
                        p['handle_users'] = str(handle_users)
                        p['check_user'] = ''
                        p['check_descr'] = ''
                        if pg.check_user:
                            p['check_user'] = pg.check_user
                            p['check_descr'] = user_session.query(User).filter(User.id == pg.check_user,
                                                                               User.unregister == 1).first().name
                        p['pcontent'] = pg.content
                        p['pdescr'] = pg.descr
                        if pg.is_team == 1:
                            HUS = user_session.query(User).filter(User.id.in_(eval(str(handle_users))),
                                                                  User.unregister == 1).all()
                        else:
                            HUS = user_session.query(User).filter(User.id == handle_users, User.unregister == 1).all()
                        handle_descrs = []
                        for u in HUS:
                            handle_descrs.append(u.name)
                        p['handle_descrs'] = ','.join(handle_descrs)
                        if level == 1:
                            ind = step_names.index(pg.descr)
                            step_arr[ind].append(p)
                        else:
                            data.append(p)
                user_session.commit()
                if level == 1:
                    return self.returnTypeSuc(step_arr)
                return self.returnTypeSuc(data)
            elif kt == 'GetStepIsMe':  # 获取当前需要我处理的工单
                session = self.getOrNewSession()
                uid = session.user['id']
                data,total = [],0
                pages = user_session.query(DispatchStep).filter(DispatchStepR.handle_time==None,DispatchStepR.dispatch_step_id==DispatchStep.id).all()
                if lang=='en':
                    result_list = self.run_list(lang)  # 获取进行中的描述
                else:
                    result_list = self.run_list()  # 获取进行中的描述
                for pag in pages:
                    if pag.care_user and str(pag.care_user) == str(uid):  # 转的
                        total = total+1
                        fi = user_session.query(DispatchR).filter(DispatchR.model_id==pag.model_id,DispatchR.status!='end',DispatchR.status!='off').first()
                        # 查询是否有未处理工号申请
                        work = user_session.query(DispatchR).filter(DispatchR.working_no==fi.working_no,DispatchR.working_flag==1,DispatchR.status!='end',DispatchR.status!='off').first()
                        o = eval(str(fi))
                        if lang == 'en':
                            replaced_data = DispatchR.replace_en_fields(o, "")
                            o.update(replaced_data)
                        o['other'] = ''
                        o['dispatch_type'] = fi.dispatch_model.dispatch_type
                        o['dispatch_descr'] = fi.dispatch_model.model_type.descr
                        if work:
                            o['working_noorder'] = True
                        else:
                            o['working_noorder'] = False
                        data.append(o)
                    elif (pag.is_team==0 and str(pag.handle_users) == str(uid)) or pag.is_team==1 and uid in eval(str(pag.handle_users)):
                        fi = user_session.query(DispatchR).filter(DispatchR.model_id==pag.model_id,DispatchR.status!='end',DispatchR.status!='off').first()
                        # 查询是否有未处理工号申请
                        total = total + 1
                        work = user_session.query(DispatchR).filter(DispatchR.working_no==fi.working_no,DispatchR.working_flag==1,DispatchR.status!='end',DispatchR.status!='off').first()
                        o = eval(str(fi))
                        if lang == 'en':
                            replaced_data = DispatchR.replace_en_fields(o, "")
                            o.update(replaced_data)
                        o['other'] = ''
                        o['dispatch_type'] = fi.dispatch_model.dispatch_type
                        o['dispatch_descr'] = fi.dispatch_model.model_type.descr
                        if work:
                            o['working_noorder'] = True
                        else:
                            o['working_noorder'] = False
                        data.append(o)
                for d in data:
                    for rr in result_list:
                        if d['id']==rr['id']:
                            d['status']=rr['status']
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetCheckIsMe':  # 获取当前需要我审批的工单
                session = self.getOrNewSession()
                uid = session.user['id']
                data,total = [],0
                working_flag = self.get_argument('working_flag',None)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                db = self.get_argument('db', None)
                if DEBUG:
                    logging.info('working_flag:%s'%(working_flag))
                filter = [DispatchStepR.check_flag==None,DispatchStep.check_user==uid,
                DispatchStepR.dispatch_step_id==DispatchStep.id,DispatchStepR.dispatch_id==DispatchR.id]
                if db:
                    filter.append(DispatchR.station==db)
                if str(working_flag) == '1':  # 工号服务申请
                    filter.append(DispatchR.working_flag==1)
                elif str(working_flag) == '2':  # 工单
                    filter.append(DispatchR.working_flag!=1)
                total = user_session.query(func.count(DispatchR.id)).filter(*filter).scalar()
                pages = user_session.query(DispatchR).filter(*filter).order_by(DispatchR.id.desc()).limit(pageSize).offset((pageNum - 1) * pageSize).all()
                if lang == 'en':
                    result_list = self.run_list(lang)  # 获取进行中的描述
                    for pag in pages:
                        o = eval(str(pag))
                        replaced_data = DispatchR.replace_en_fields(o, "")
                        o.update(replaced_data)
                        o['other'] = ''
                        o['station'] = o['en_station']
                        if pag.working_flag and pag.en_other and pag.en_other != 'None':
                            for k,v in eval(str(pag.en_other)).items():
                                o[k] = v
                        o['dispatch_type'] = pag.dispatch_model.dispatch_type
                        o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                        # 查询是否有未处理工号申请
                        work = user_session.query(DispatchR).filter(DispatchR.working_no==pag.working_no,DispatchR.working_flag==1,or_(DispatchR.status!='end',DispatchR.status!='off')).first()
                        if work:
                            o['working_noorder'] = True
                        else:
                            o['working_noorder'] = False
                        data.append(o)
                else:
                    result_list = self.run_list()  # 获取进行中的描述
                    for pag in pages:
                        o = eval(str(pag))
                        o['other'] = ''
                        if pag.working_flag and pag.other:
                            for k, v in eval(str(pag.other)).items():
                                o[k] = v
                        o['dispatch_type'] = pag.dispatch_model.dispatch_type
                        o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                        # 查询是否有未处理工号申请
                        work = user_session.query(DispatchR).filter(DispatchR.working_no == pag.working_no, DispatchR.working_flag == 1,
                                                                    or_(DispatchR.status != 'end', DispatchR.status != 'off')).first()
                        if work:
                            o['working_noorder'] = True
                        else:
                            o['working_noorder'] = False
                        data.append(o)
                for d in data:
                    for rr in result_list:
                        if d['id']==rr['id']:
                            d['status']=rr['status']
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetCopyIsMe':  # 获取当前抄送我的工单
                session = self.getOrNewSession()
                uid = session.user['id']
                u_name = session.user['name']
                working_flag = self.get_argument('working_flag',None)
                pageNum = int(self.get_argument('pageNum', 1))
                pageSize = int(self.get_argument('pageSize', 20))
                db = self.get_argument('db', None)
                data ,total= [],0
                filter = [DispatchR.copy_users!=None]
                if db:
                    filter.append(DispatchR.station==db)
                if str(working_flag) == '1':  # 工号服务申请
                    print ('..........')
                    filter.append(DispatchR.working_flag==1)
                elif str(working_flag) == '2':  # 工单
                    filter.append(DispatchR.working_flag!=1)
                pages = user_session.query(DispatchR).filter(*filter).order_by(DispatchR.id.desc()).all()
                if lang=='en':
                    result_list = self.run_list(lang)  # 获取进行中的描述
                    for pag in pages:
                        o = eval(str(pag))
                        if u_name in o['copy_descr']:
                            total+=1
                            if total > pageSize:
                                continue
                            replaced_data = DispatchR.replace_en_fields(o, "")
                            o.update(replaced_data)
                            o['other'] = ''
                            o['en_other'] = ''
                            o['copy_descr'] = o['en_copy_descr']
                            o['create_descr'] = o['en_create_descr']
                            o['station'] = o['descr']
                            if pag.working_flag and pag.en_other and pag.en_other != 'None':
                                for k, v in eval(str(pag.en_other)).items():
                                    o[k] = v
                            o['dispatch_type'] = pag.dispatch_model.dispatch_type
                            o['dispatch_descr'] = pag.dispatch_model.model_type.en_descr
                            data.append(o)
                else:
                    result_list = self.run_list()  # 获取进行中的描述
                    for pag in pages:
                        o = eval(str(pag))
                        if u_name in o['copy_descr']:
                            total += 1
                            if total > pageSize:
                                continue
                            o['other'] = ''
                            o['en_other'] = ''
                            if pag.working_flag and pag.other:
                                for k,v in eval(str(pag.other)).items():
                                    o[k] = v
                            o['dispatch_type'] = pag.dispatch_model.dispatch_type
                            o['dispatch_descr'] = pag.dispatch_model.model_type.descr
                            data.append(o)

                for d in data:
                    for rr in result_list:
                        if d['id']==rr['id']:
                            d['status']=rr['status']
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetAllDevices':  # 获取所有维修设备
                if lang=='en':
                    data = [{"id": "PCS", "name": "PCS"}, {"id": "air conditioner", "name": "air conditioner"}, {"id": "Battery holder", "name": "Battery holder"},
                            {"id": "Transformer", "name": "Transformer"},
                            {"id": "Control system", "name": "Control system"}, {"id": "Software system-EMS", "name": "Software system-EMS"},
                            {"id": "Fire fighting equipment", "name": "Fire fighting equipment"}, ]
                else:
                    data = [{"id":"PCS","name":"PCS"},{"id":"空调","name":"空调"},{"id":"电池仓","name":"电池仓"},{"id":"变压器","name":"变压器"},
                            {"id":"控制系统","name":"控制系统"},{"id":"软件系统-EMS","name":"软件系统-EMS"},{"id":"消防设备","name":"消防设备"},]
                return self.returnTypeSuc(data)
            elif kt == 'GetPlanWorkingList':  # 获得定期工单配置
                station = self.get_argument('db',None)
                device = self.get_argument('device',None)
                pageNum = int(self.get_argument('pageNum',1))
                pageSize = int(self.get_argument('pageSize',20))
                isUse = int(self.get_argument('isUse',1))
                create_descr = self.get_argument('create_descr',None)
                startTime = self.get_argument('startTime', None)  # 开始时间YYYY-mm-dd
                endTime = self.get_argument('endTime', None)  # 截止时间YYYY-mm-dd
                repet = self.get_argument('repet', None)  # 是否重复，0不重复1按天重复2按周重复3按月重复4按年重复
                if DEBUG:
                    logging.info('station:%s,pageNum:%s,pageSize:%s,isUse:%s,device:%s,create_descr:%s,startTime:%s,endTime:%s,repet:%s'%(station,pageNum,pageSize,isUse,device,create_descr,startTime,endTime,repet))
                filter,data = [DispatchPlan.is_use==isUse],[]
                # if not create_user:  #没有用户id默认当前用户
                #     session = self.getOrNewSession()
                #     create_user = session.user['id']
                if station:
                    filter.append(DispatchPlan.station==station)
                devicefilter = []
                if device:
                    devices = device.split(',')
                    for d in devices:
                        if lang=='en':
                            devicefilter.append(DispatchPlan.en_device.like('%' + d + '%'))
                        else:
                            devicefilter.append(DispatchPlan.device.like('%' + d + '%'))
                if create_descr:
                    if lang == 'en':
                        filter.extend([DispatchPlan.create_user==User.id,User.en_name.like('%' + create_descr + '%')])
                    else:
                        filter.extend([DispatchPlan.create_user == User.id, User.name.like('%' + create_descr + '%')])
                    # UserID = user_session.query(User.id).filter(User.name.like('%' + create_descr + '%')).first()
                    # filter.append(DispatchPlan.create_user==UserID[0])
                if startTime:
                    filter.append(DispatchPlan.start_time>=startTime)
                if endTime:
                    filter.append(DispatchPlan.end_time<=endTime)
                if repet:
                    filter.append(DispatchPlan.repet==repet)
                total = user_session.query(func.count(DispatchPlan.id)).filter(*filter).filter(or_(*devicefilter)).scalar()
                pages = user_session.query(DispatchPlan).filter(*filter).filter(or_(*devicefilter)).order_by(DispatchPlan.id.desc()).limit(pageSize).offset((pageNum-1)*pageSize).all()
                if lang == 'en':
                    for pag in pages:
                        o = eval(str(pag))
                        replaced_data = DispatchPlan.replace_en_fields(o, "")
                        o.update(replaced_data)
                        data.append(o)
                else:
                    for pag in pages:
                        content = pag.content
                        pag.content = content.replace('\n', '').replace('\r', '')
                        o = eval(str(pag))
                        o['content'] = content
                        data.append(o)
                return self.returnTotalSuccess(data,total)
            elif kt == 'GetPlanDashboard': #工单仪表盘
                create_user = self.get_argument('create_user', None)
                session = self.getOrNewSession()
                uid = session.user['id']

                filter_1 = [DispatchR.working_flag == 1, DispatchR.status != 'off']
                working_nos = user_session.query(DispatchR.working_no).filter(DispatchR.working_no != None,
                                                                              DispatchR.working_flag != 1).all()
                working_list = []
                for w in working_nos:
                    working_list.append(w[0])
                filter_1.append(DispatchR.working_no.not_in(working_list))
                total_1 = user_session.query(func.count(DispatchR.id)).filter(*filter_1).scalar()  # 获得工号服务申请没有派发工单数量

                filter_2 = [DispatchR.working_flag != 1]
                if not create_user:  # 没有用户id默认当前用户
                    session = self.getOrNewSession()
                    create_user = session.user['id']
                    filter_2.append(DispatchR.create_user==create_user)
                # 我创建的工单
                total_2 = user_session.query(func.count(DispatchR.id)).filter(*filter_2).scalar()

                # 已创建的工号服务申请
                total_3 = user_session.query(func.count(DispatchR.id)).filter(DispatchR.working_flag == 1).scalar()

                copym = 0  # 抄送我的
                copy_filter = []
                copy_filter.append(DispatchR.working_flag != 1)
                pages = user_session.query(DispatchR).filter(*copy_filter).all()
                for pag in pages:
                    if uid in eval(str(pag.copy_users)):
                        copym = copym + 1

                total_4 = user_session.query(func.count(DispatchR.id)).filter(DispatchR.working_flag != 1,DispatchR.status == 'off').scalar()  # 已关闭工单
                total_5 = user_session.query(func.count(DispatchR.id)).filter(DispatchR.working_flag != 1,DispatchR.status == 'end').scalar()  # 已结束工单
                total_6 = user_session.query(func.count(DispatchR.id)).filter(DispatchR.working_flag != 1,DispatchR.status != 'off' and DispatchR.status != 'end').scalar()  # 运行中工单

                data={'yiguanbi': total_4, 'jinxingzhong': total_6, 'yiwanjie': total_5, 'weifenpei': total_1, 'yichuangjian': total_3, 'chuangjian': total_2,
                      'chaosong': copym}
                return self.returnTypeSuc(data)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    def run_list(self,lang=None):
        '''获取进行中的描述'''
        sql = "select a.descr,c.id from t_dispatch_step a left join r_dispatch_step b on a.id = b.dispatch_step_id " \
              "left join r_dispatch c on b.dispatch_id = c.id where b.status='1' "
        conn = user_engine.raw_connection()  # 拿原生的连接
        cursor = conn.cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        cursor.close()
        conn.close()

        result_list = []
        if result:
            for r in result:
                result_dict = {}
                l = list(r)
                if lang == 'en':
                    if l[0] == '准备工作内容':
                        l[0] = 'Executing'
                    elif l[0] == '准备工作初步审核':
                        l[0] = 'Checking'
                    elif l[0] == '准备工作最终审核':
                        l[0] = 'Approving'
                    elif l[0] == '此次工作汇总':
                        l[0] = 'Summarizing'
                    elif l[0] == '工作总结审核':
                        l[0] = 'Reviewing'
                else:
                    if l[0] == '准备工作内容':
                        l[0] = '准备工作'
                    elif l[0] == '准备工作初步审核':
                        l[0] = '初步审核'
                    elif l[0] == '准备工作最终审核':
                        l[0] = '最终审核'
                    elif l[0] == '此次工作汇总':
                        l[0] = '工作汇总'
                    elif l[0] == '工作总结审核':
                        l[0] = '总结审核'
                result_dict['status'] = l[0]
                result_dict['id'] = l[1]
                result_list.append(result_dict)
        return result_list

    @tornado.web.authenticated
    def post(self,kt):
        self.refreshSession()
        lang = self.get_argument('lang', None)  # 英文
        try:
        # if 1:
            db = self.get_argument('db','his')  # 所属站
            if kt == 'AddType':  # 添加工单类型
                descr = self.get_argument('descr',None)
                if DEBUG:
                    logging.info('descr:%s'%(descr))
                pa = user_session.query(DispatchType).filter(DispatchType.descr==descr).first()
                if pa :
                    if lang=='en':
                        return self.customError("Name already exists")
                    else:
                        return self.customError("名称已存在")
                if lang=='en':
                    zh_descr=translate_text(descr, 1)
                    h = DispatchType(descr=zh_descr, en_descr=descr,op_ts=timeUtils.getNewTimeStr())
                else:
                    en_descr = translate_text(descr, 2)
                    h = DispatchType(descr=descr, en_descr=en_descr, op_ts=timeUtils.getNewTimeStr())

                user_session.merge(h)
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateType':  # 修改工单类型
                id = self.get_argument('id',None)
                descr = self.get_argument('descr',None)
                isUse = int(self.get_argument('isUse',1))
                if DEBUG:
                    logging.info('id:%s,descr:%s,isUse:%s'%(id,descr,isUse))
                page = user_session.query(DispatchType).filter(DispatchType.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                pa = user_session.query(DispatchType).filter(DispatchType.descr==descr).first()
                if pa and pa.id != int(id):
                    if lang=='en':
                        return self.customError("Name already exists")
                    else:
                        return self.customError("名称已存在")

                if descr:
                    if lang=='en':
                        zh_descr = translate_text(descr, 1)
                        page.descr = zh_descr
                        page.en_descr = descr
                    else:
                        en_descr = translate_text(descr, 2)
                        page.descr = descr
                        page.en_descr = en_descr
                page.is_use = isUse
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'DeleteType':  # 删除工单类型
                id = self.get_argument('id',None)
                lang = self.get_argument('lang', None)  # 英文
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(DispatchType).filter(DispatchType.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                pa = user_session.query(DispatchModel).filter(DispatchModel.dispatch_type==id).first()
                
                if pa :
                    if lang=='en':
                        return self.customError("A template is in use and cannot be deleted. Delete the template first")
                    else:
                        return self.customError("有模板已使用，不可删除。请先删除模板")

                user_session.query(DispatchType).filter(DispatchType.id==id).delete()
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'OffWorkerNo':  # 关闭工号服务申请
                id = self.get_argument('id',None) # 工号服务申请id
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(DispatchR).filter(DispatchR.id==id).first()
                if not page:
                    if lang=='en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                session = self.getOrNewSession()
                uid = session.user['id']
                if str(page.create_user) != str(uid):
                    if lang=='en':
                        return  self.customError("Insufficient authority")
                    else:
                        return  self.customError("权限不足")
                page.status = 'off'
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'AddWorkerNoService':  # 添加工号服务申请
                working_no = self.get_argument('working_no',None)  # 工号
                start_time = self.get_argument('start_time',None)  # 预计开始时间
                plan_time  = self.get_argument('plan_time',None)  #预计完成时间
                content = self.get_argument('content',None)  #服务内容概述
                project_name = self.get_argument('project_name',"")  # 项目名 o
                in_no = self.get_argument('in_no',"")  # 内部订单 o
                server_depart = self.get_argument('server_depart',"")  # 服务部门（选择） o
                delivery_time = self.get_argument('delivery_time',"")  # 项目交付日期 o
                plan_server  = self.get_argument('plan_server',"")  #计划服务人员（选择）o
                direct_customer_name  = self.get_argument('direct_customer_name',"")  #直接用户名o
                direct_customer_user  = self.get_argument('direct_customer_user',"")  #联系人 o
                direct_customer_tel  = self.get_argument('direct_customer_tel',"")  #联系电话o
                direct_customer_fax  = self.get_argument('direct_customer_fax',"")  #传真o
                final_customer_name  = self.get_argument('final_customer_name',"")  #最终用户名o
                final_customer_user  = self.get_argument('final_customer_user',"")  #联系人o
                final_customer_tel  = self.get_argument('final_customer_tel',"")  #联系电话o
                address  = self.get_argument('address',"")  #现场地址o
                address_fax  = self.get_argument('address_fax',"")  #现场传真o
                apply_user   = self.get_argument('apply_user',None)  #申请部门审批人（选择）
                dispatch_type = self.get_argument('dispatch_type',None)  #类型
                copy_users = str(self.get_argument('copy_users',[]))  #抄送人

                other = {"project_name":project_name,"in_no":in_no,"server_depart":server_depart,"delivery_time":delivery_time,"plan_server":plan_server,"direct_customer_name":direct_customer_name,
                "direct_customer_user":direct_customer_user,"direct_customer_tel":direct_customer_tel,"direct_customer_fax":direct_customer_fax,"final_customer_name":final_customer_name,"final_customer_user":final_customer_user,
                "final_customer_tel":final_customer_tel,"address":address,"address_fax":address_fax}

                if DEBUG:
                    logging.info('content:%s,working_no:%s,start_time:%s,plan_time:%s,apply_user:%s,other:%s,dispatch_type:%s,copy_users:%s'%(content,
                    working_no,start_time,plan_time,apply_user,other,dispatch_type,copy_users))
                if  not plan_time or not dispatch_type or not working_no or not content:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError("参数不完整")
                dr = user_session.query(DispatchR).filter(DispatchR.working_no==working_no).first()
                if dr :
                    if lang=='en':
                        return self.customError("The job ID already exists")
                    else:
                        return self.customError("工号已存在")
                if db=='ygzhen' or db=='zgtian':
                    apply_user = 126  # 邓志杨
                elif db in self_station:  # 如果是自持电站
                    if dispatch_type=='14':
                        apply_user = 0
                    else:
                        apply_user = 113  # 武霄龙审核
                else:
                    apply_user = 6  # 吴晗审核
                copy_users = eval(copy_users)
                if 68 not in copy_users:
                    copy_users.append(68)  # 默认全部抄送刘明
                if dispatch_type=='14':
                    pass
                else:
                    if 98 not in copy_users:
                        copy_users.append(98)  # 默认全部抄送薛钰歆
                    # apply_user = 9
                    # list(copy_users).append(9)  # 测试抄李智
                copy_users = str(copy_users)
                session = self.getOrNewSession()
                oid = session.user['organization_id']
                uid = session.user['id']
                a1 = content.split()
                if lang=='en':
                    if project_name!=None:
                        zh_project_name=translate_text(project_name, 1)
                    else:
                        zh_project_name=''
                    if a1!=None:
                        zh_content=translate_text(a1, 1)
                    else:
                        zh_content=''
                    if other!=None:
                        zh_other=other
                        for k, v in zh_other.items():
                            zh_other[k] = translate_text(v, 1)
                    else:
                        zh_other=''
                    model = DispatchModel(descr=u'%s 工单申请' % zh_project_name,en_descr=u'%s Work order application' % project_name,op_ts=timeUtils.getNewTimeStr(),create_user=uid,organization_id=oid,
                                          dispatch_type=dispatch_type,working_flag=1)
                    user_session.add(model)
                    user_session.commit()

                    dstep1 = DispatchStep(model_id=model.id,descr=u'工单申请审批人审批',en_descr=u'Approval by the work order applicant',content=u'请填写工单申请意见',en_content=u'Please fill in the work order request comments',
                                          is_team=0 ,check_user=apply_user,stage=1)
                    user_session.add(dstep1)
                    user_session.commit()

                    rd = DispatchR(descr=u'%s  工单申请'% zh_project_name,en_descr=u'%s  Work order application'% project_name,op_ts=timeUtils.getNewTimeStr(),model_id=model.id,start_time=start_time,plan_time=plan_time,
                                   create_user=uid,content=';'.join(zh_content),en_content=';'.join(a1),station=db,working_no=working_no,working_flag=1,status=dstep1.id,copy_users=copy_users,other=zh_other,en_other=other)
                    user_session.add(rd)
                    user_session.commit()

                    rdr = DispatchStepR(dispatch_id=rd.id,status=1,send_time=timeUtils.getNewTimeStr(),dispatch_step_id=dstep1.id,stage=1)
                    user_session.add(rdr)
                    user_session.commit()
                    rd.status=rdr.id
                    user_session.commit()
                else:
                    t_cls = Translate_cls(2)
                    if project_name != None:
                        en_project_name = t_cls.str_chinese(project_name)
                    else:
                        en_project_name = ''
                    if a1 != None:
                        en_content = t_cls.str_chinese(a1)
                    else:
                        en_content = ''
                    if other != None:
                        en_other = t_cls.str_chinese(other)
                    else:
                        en_other = ''
                    other = json.dumps(other, ensure_ascii=False)
                    model = DispatchModel(descr=u'%s 工单申请' % project_name,en_descr=u'%s Work order application' % en_project_name, op_ts=timeUtils.getNewTimeStr(),create_user=uid, organization_id=oid,
                                          dispatch_type=dispatch_type,working_flag=1)
                    user_session.add(model)
                    user_session.commit()

                    dstep1 = DispatchStep(model_id=model.id, descr=u'工单申请审批人审批', en_descr=u'Approval by the work order applicant',content=u'请填写工单申请意见',en_content=u'Please fill in the work order request comments',
                                          is_team=0,check_user=apply_user, stage=1)
                    user_session.add(dstep1)
                    user_session.commit()

                    rd = DispatchR(descr=u'%s 工单申请' % project_name,en_descr=u'%s  Work order application'% en_project_name, op_ts=timeUtils.getNewTimeStr(), model_id=model.id,start_time=start_time, plan_time=plan_time,
                                   create_user=uid, content=';'.join(a1), en_content=';'.join(en_content),station=db,working_no=working_no, working_flag=1, status=dstep1.id, copy_users=copy_users,other=other,en_other=en_other)
                    user_session.add(rd)
                    user_session.commit()
                    rdr = DispatchStepR(dispatch_id=rd.id, status=1, send_time=timeUtils.getNewTimeStr(),dispatch_step_id=dstep1.id, stage=1)
                    user_session.add(rdr)
                    user_session.commit()
                    rd.status = rdr.id
                    user_session.commit()

                emails = []
                HUS = user_session.query(User).filter(User.id==apply_user,User.unregister==1).first()
                if HUS:
                    emails.append(HUS.email)
                    logging.info('send users is %s'%emails)
                    if lang=='en':
                        sendMail_("You have a new work order service application to be reviewed, please log in to the system in time","Audit message notification","RHBESS","XXX",emails)
                    else:
                        sendMail_("您有新的待审核工单服务申请，请及时登录系统处理", "审核消息通知", "RHBESS", "XXX", emails)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdateWorkerNoService':  # 修改工号服务申请
                working_no = self.get_argument('working_no',None)  # 工号F
                start_time = self.get_argument('start_time',None)  # 预计开始时间
                plan_time  = self.get_argument('plan_time',None)  #预计完成时间
                content = self.get_argument('content',None)  #服务内容概述
                if DEBUG:
                    logging.info('content:%s,working_no:%s,start_time:%s,plan_time:%s,'%(content,working_no,start_time,plan_time))
                dr = user_session.query(DispatchR).filter(DispatchR.working_no==working_no,DispatchR.working_flag==1).first()  # 查询工号服务申请
                work = user_session.query(DispatchR).filter(DispatchR.working_no==working_no,DispatchR.working_flag==2,or_(DispatchR.status!='end',DispatchR.status!='off')).first()  # 查询工号服务申请
                if not work:  # 该工号申请对应的工单已结束或关闭，对应的工号申请不允许修改
                    if lang=='en':
                        return self.customError('The corresponding work order has been completed or closed, and cannot be modified')
                    else:
                        return self.customError('对应工单已结束或关闭，不允许修改')
                dr.start_time = start_time
                dr.plan_time = plan_time

                a1 = content.split()
                if lang=='en':
                    b1=translate_text(a1, 1)
                    dr.content = ';'.join(b1)
                    dr.en_content = ';'.join(a1)
                else:
                    b1 = translate_text(a1, 2)
                    dr.content = ';'.join(a1)
                    dr.en_content = ';'.join(b1)

                dr.status = 'run'
                dr.update_time = timeUtils.getNewTimeStr()

                dsp = user_session.query(DispatchStepR).filter(DispatchStepR.dispatch_id==dr.id).first()
                dsp.status = 1
                user_session.commit()
                HUS = user_session.query(User).filter(User.id==DispatchStep.check_user,User.unregister==1,DispatchR.model_id==DispatchStep.model_id,).first()
                if lang=='en':
                    sendMail_("You have a new work order service application to be reviewed, please log in to the system in time",
                        "Audit message notification", "RHBESS", "XXX", [HUS.email])
                else:
                    sendMail_("工号申请信息已更新，请及时登录系统重新审核","审核消息通知","RHBESS","XXX",[HUS.email])
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
                # return self.returnTypeSuc("")
            elif kt == 'AddPlanWorkerNoService':  # 添加计划工单内容
                start_time = self.get_argument('start_time',None)  # 开始时间 YYYY-mm-dd
                end_time = self.get_argument('end_time',None)  # 结束时间 YYYY-mm-dd
                examine_time = self.get_argument('examine_time',None)  # 检查时间，HH:MM
                plan_time  = self.get_argument('plan_time',None)  #每次巡检持续时长，天为单位
                content = self.get_argument('content',None)  #服务内容概述
                handle_user = self.get_argument('handle_user',None)  # 执行人
                copy_users = str(self.get_argument('copy_users',[]))  #抄送人
                station = self.get_argument('db',None)  # 所属站
                first_apply_user = self.get_argument('first_apply_user',None) # 准备工作第一个审核人
                second_apply_user = self.get_argument('second_apply_user',None) # 准备工作第二个审核人
                finall_apply_user = self.get_argument('finall_apply_user',None) # 工作总结审核人
                device = str(self.get_argument('device',[]))  # 计划任务的设备名称
                repet = int(self.get_argument('repet',0))  # 是否重复，0不重复1按天重复2按周重复3按月重复4按年重复
                # repet = int(self.get_argument('repet', 1))  # 是否重复，1不重复2重复
                repet_interval  = int(self.get_argument('repet_interval',0))  #间隔，整数 int
                repet_week  = str(self.get_argument('repet_week',[]))  #间隔周使用，每逢周几执行
                is_use = self.get_argument('is_use', "")  # 是否使用。1是0否
                # content = ';'.join(content.split()) if content else ''

                if DEBUG:
                    logging.info('content:%s,end_time:%s,start_time:%s,plan_time:%s,handle_user:%s,examine_time:%s,station:%s,copy_users:%s,\
                    first_apply_user:%s,second_apply_user:%s,finall_apply_user:%s,device:%s,repet:%s,repet_interval:%s,repet_week:%s'%(content,
                    end_time,start_time,plan_time,handle_user,examine_time,station,copy_users,first_apply_user,second_apply_user,finall_apply_user,device,repet,repet_interval,repet_week))
                if not start_time or not end_time or not examine_time or not plan_time or not content or not handle_user or not station or not device:
                    if lang=='en':
                        return self.customError("Parameter incompleteness")
                    else:
                        return self.customError("参数不完整")
                if repet and not repet_interval:
                    if lang=='en':
                        return self.customError("Illegal entry")
                    else:
                        return self.customError("非法入参")
                if repet == 2 and not repet_week:
                    if lang=='en':
                        return self.customError("参数不完整")
                    else:
                        return self.customError("Illegal entry2")

                pp = user_session.query(DispatchPlan).filter(DispatchPlan.station==db,DispatchPlan.device==device,DispatchPlan.repet==repet,DispatchPlan.repet_interval==repet_interval,DispatchPlan.repet_week==repet_week)\
                    .first()
                if pp:
                    if lang=='en':
                        return self.customError("Data already exists")
                    else:
                        return self.customError("数据已存在")
                if db=='ygzhen' or db=='zgtian':
                    apply_user = 126  # 邓志杨
                elif db in self_station:  # 如果是自持电站
                    apply_user = 113  # 武霄龙审核
                else:
                    apply_user = 6
                copy_users = eval(copy_users)

                if 98 not in copy_users:
                    copy_users.append(98)  # 默认全部抄送薛钰歆
                # apply_user = 9
                # list(copy_users).append(9)  # 测试抄李智

                copy_users = str(copy_users)
                session = self.getOrNewSession()
                oid = session.user['organization_id']
                uid = session.user['id']
                # a1 = content.split()
                if lang=='en':
                    t_cls = Translate_cls(1)
                    zh_content=t_cls.str_chinese(content)
                    if device!=None:
                        zh_device=t_cls.str_chinese(device)
                    else:
                        zh_device=''
                    plan = DispatchPlan(dispatch_type=2, start_time=start_time, end_time=end_time,
                                        examine_time=examine_time, op_ts=timeUtils.getNewTimeStr(), create_user=uid,
                                        organization_id=oid,
                                        en_content=content,content=zh_content, plan_time=plan_time, handle_user=handle_user,
                                        copy_users=copy_users, station=station, first_apply_user=apply_user,
                                        second_apply_user=apply_user,
                                        finall_apply_user=apply_user, en_device=device,device=zh_device, repet=repet,
                                        repet_interval=repet_interval, repet_week=repet_week)
                else:
                    t_cls = Translate_cls(2)
                    en_content = t_cls.str_chinese(content)
                    if device != None:
                        en_device = t_cls.str_chinese(device)
                    else:
                        en_device = ''
                    plan = DispatchPlan(dispatch_type=2,start_time=start_time,end_time=end_time,examine_time=examine_time,op_ts=timeUtils.getNewTimeStr(),create_user=uid,organization_id=oid,
                        content=content,en_content=en_content,plan_time=plan_time,handle_user=handle_user,copy_users=copy_users,station=station,first_apply_user=apply_user,second_apply_user=apply_user,
                        finall_apply_user=apply_user,en_device=en_device,device=device,repet=repet,repet_interval=repet_interval,repet_week=repet_week)
                user_session.add(plan)
                user_session.commit()
                Userid = []
                Userid.append(first_apply_user)
                Userid.append(handle_user)
                Userid.append(second_apply_user)
                Userid.append(finall_apply_user)
                emails = []
                HUS = user_session.query(User).filter(User.id == handle_user, User.unregister == 1).first()
                if HUS and HUS.email:
                    emails.append(HUS.email)
                    print ('send users is %s' % emails)
                    sendMail_("您有新的计划工单即将开始，请及时登录系统处理", "计划工单处理消息通知", "RHBESS", "XXX", emails)

                for c in eval(copy_users):
                    emails = []
                    HUS = user_session.query(User).filter(User.id == c, User.unregister == 1).first()
                    if HUS and HUS.email:
                        emails.append(HUS.email)
                        print ('send users is %s' % emails)
                        sendMail_("您有新的计划工单即将开始，请及时查看", "计划工单抄送消息通知", "RHBESS", "XXX", emails)
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'UpdatePlanWorkerNoService':  # 修改计划工单内容
                id = self.get_argument('id',None)
                start_time = self.get_argument('start_time',None)  # 开始时间 YYYY-mm-dd
                end_time = self.get_argument('end_time',None)  # 结束时间 YYYY-mm-dd
                examine_time = self.get_argument('examine_time',None)  # 检查时间，HH:MM
                plan_time  = self.get_argument('plan_time',None)  #每次巡检持续时长，天为单位
                content = self.get_argument('content',None)  #服务内容概述
                handle_user = self.get_argument('handle_user',None)  # 执行人
                copy_users = str(self.get_argument('copy_users',[]))  #抄送人
                station = self.get_argument('db',None)  # 所属站
                first_apply_user = self.get_argument('first_apply_user',None) # 准备工作第一个审核人
                second_apply_user = self.get_argument('second_apply_user',None) # 准备工作第二个审核人
                finall_apply_user = self.get_argument('finall_apply_user',None) # 工作总结审核人
                device = str(self.get_argument('device',[]))  # 计划任务的设备名称
                repet = int(self.get_argument('repet',0))  # 是否重复，0不重复1按天重复2按周重复3按月重复4按年重复
                repet_interval = self.get_argument('repet_interval',"")  #间隔，整数 int
                repet_week = str(self.get_argument('repet_week',[]))  #间隔周使用，每逢周几执行

                if DEBUG:
                    logging.info('content:%s,end_time:%s,start_time:%s,plan_time:%s,handle_user:%s,examine_time:%s,station:%s,copy_users:%s,\
                    first_apply_user:%s,second_apply_user:%s,finall_apply_user:%s,device:%s,repet:%s,repet_interval:%s,repet_week:%s'%(content,
                    end_time,start_time,plan_time,handle_user,examine_time,station,copy_users,first_apply_user,second_apply_user,finall_apply_user,device,repet,repet_interval,repet_week))

                if repet and not repet_interval:
                    if lang == 'en':
                        return self.customError("Illegal entry")
                    else:
                        return self.customError("非法入参")
                if repet == 2 and not repet_week:
                    if lang == 'en':
                        return self.customError("Illegal entry2")
                    else:
                        return self.customError("非法入参2")
                page = user_session.query(DispatchPlan).filter(DispatchPlan.id==id).first()
                if not page:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                pa = user_session.query(DispatchPlan).filter(DispatchPlan.station==station,DispatchPlan.device==device,DispatchPlan.repet==repet).first()
                if pa and pa.id != int(id):
                    if lang=='en':
                        return self.customError("Data already exists")
                    else:
                        return self.customError("数据已存在")

                session = self.getOrNewSession()
                oid = session.user['organization_id']
                uid = session.user['id']
                if page.create_user != uid:
                    if lang=='en':
                        return self.customError("No modification permission")
                    else:
                        return self.customError("无修改权限")

                if start_time:
                    page.start_time = start_time
                if end_time:
                    page.end_time = end_time
                if examine_time:
                    page.examine_time = examine_time
                if plan_time:
                    page.plan_time = plan_time
                if content:
                    if lang == 'en':
                        t_cls = Translate_cls(1)
                        zh_content = t_cls.str_chinese(content)
                        page.content = zh_content
                        page.en_content = content
                    else:
                        t_cls = Translate_cls(2)
                        en_content = t_cls.str_chinese(content)
                        page.content = content
                        page.en_content = en_content
                if handle_user:
                    page.handle_user = handle_user
                if station:
                    page.station = station
                if device:
                    if lang == 'en':
                        t_cls = Translate_cls(1)
                        zh_device = t_cls.str_chinese(device)
                        page.device = zh_device
                        page.en_device = device
                    else:
                        t_cls = Translate_cls(2)
                        en_device = t_cls.str_chinese(device)
                        page.device = device
                        page.en_device = en_device
                if repet:
                    page.repet = repet
                if first_apply_user:
                    page.first_apply_user = first_apply_user
                if second_apply_user:
                    page.second_apply_user = second_apply_user
                if finall_apply_user:
                    page.finall_apply_user = finall_apply_user
                if repet_interval:
                    page.repet_interval = repet_interval
                if repet_week:
                    page.repet_week = repet_week
                if copy_users:
                    page.copy_users = copy_users
                if db and db!='his':
                    page.station = db
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            elif kt == 'DeletePlanWorkerNoService':  # 删除
                id = self.get_argument('id',None)
                if DEBUG:
                    logging.info('id:%s'%(id))
                page = user_session.query(DispatchPlan).filter(DispatchPlan.id==id).first()
                if not page:
                    if lang == 'en':
                        return self.customError("Invalid id")
                    else:
                        return self.customError("无效id")
                session = self.getOrNewSession()
                uid = session.user['id']
                if page.create_user != uid:
                    if lang == 'en':
                        return self.customError("No deletion permission")
                    else:
                        return self.customError("无删除权限")

                user_session.query(DispatchPlan).filter(DispatchPlan.id==id).delete()
                user_session.commit()
                if lang == 'en':
                    return self.returnTypeSuc_en(data='', info=None, lang='en')
                else:
                    return self.returnTypeSuc_en(data='', info=None, lang=None)
            else:
                return self.pathError()
        except Exception as E:
            user_session.rollback()
            logging.error(E)
            if lang == 'en':
                return self.requestError('en')
            else:
                return self.requestError()
        finally:
            user_session.close()

    def _getNum(self,working_flag):
        session = self.getOrNewSession()
        uid = session.user['id']
        daichuli,shenpi,copym = 0,0,0  # 待我处理；待我审批；抄送我的
        # 待我处理
        pages = user_session.query(DispatchStep).filter(DispatchStepR.handle_time==None,DispatchStepR.dispatch_step_id==DispatchStep.id).all()
        # print 'pages:',len(pages),pages
        for pag in pages:
            if (pag.is_team==0 and str(pag.handle_users) == str(uid)) or pag.is_team==1 and uid in eval(str(pag.handle_users)):
                daichuli = daichuli+1
                
        # 待我审批
        filter = [DispatchStepR.check_flag==None,DispatchStep.check_user==uid,
        DispatchStepR.dispatch_step_id==DispatchStep.id,DispatchStepR.dispatch_id==DispatchR.id]
        if working_flag == '1':  # 工号服务申请
            filter.append(DispatchR.working_flag==1)
        elif working_flag == '2':  # 工单
            filter.append(DispatchR.working_flag!=1)
        pages = user_session.query(DispatchR).filter(*filter).all()
        shenpi = len(pages)

        # 抄送我的
        copy_filter = []
        if working_flag == '1':  # 工号服务申请
            copy_filter.append(DispatchR.working_flag==1)
        elif working_flag == '2':  # 工单
            copy_filter.append(DispatchR.working_flag!=1)
        
        pages = user_session.query(DispatchR).filter(*copy_filter).all()
        for pag in pages:
            if uid in eval(str(pag.copy_users)):
                copym = copym+1
                
        return daichuli,shenpi,copym

        
       

