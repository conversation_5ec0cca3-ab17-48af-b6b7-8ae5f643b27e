#!/usr/bin/env python
# coding=utf-8
#@Information:
#<AUTHOR> WYJ
#@Date         : 2022-08-05 10:21:04
#@FilePath     : \RHBESS_Service\Tools\DB\binhai1_his.py
#@Email        : <EMAIL>
#@LastEditTime : 2023-11-15 17:08:43


import os
from sqlalchemy import Column, String, DateTime, Boolean, VARCHAR, ForeignKeyConstraint, TIMESTAMP, \
    CHAR, DATE, Integer, ForeignKey,  MetaData, Table,Float, JSON, SmallInteger, DECIMAL,Text
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, relationship,scoped_session
from sqlalchemy.ext.declarative import declarative_base
from Application.Cfg.dir_cfg import model_config
# 入参打印到日志中
# DEBUG = model_config.get('broker', "log_debug")
# 哈伦项目历史库连接 mysql


BINHAI1_HOSTNAME = model_config.get('mysql', "BINHAI1_HOSTNAME")
BINHAI1_PORT = model_config.get('mysql', "BINHAI1_PORT")
BINHAI1_DATABASE = model_config.get('mysql', "BINHAI1_DATABASE")
BINHAI1_USERNAME = model_config.get('mysql', "BINHAI1_USERNAME")
BINHAI1_PASSWORD = model_config.get('mysql', "BINHAI1_PASSWORD")

hisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    BINHAI1_USERNAME,
    BINHAI1_PASSWORD,
    BINHAI1_HOSTNAME,
    BINHAI1_PORT,
    BINHAI1_DATABASE
)
binhai1_engine = create_engine(hisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_binhai1_session = scoped_session(sessionmaker(binhai1_engine,autoflush=True))

binhai1_Base = declarative_base(binhai1_engine)
binhai1_session = _binhai1_session()


SBINHAI1_HOSTNAME = model_config.get('mysql', "SBINHAI1_HOSTNAME")
SBINHAI1_PORT = model_config.get('mysql', "SBINHAI1_PORT")
SBINHAI1_DATABASE = model_config.get('mysql', "SBINHAI1_DATABASE")
SBINHAI1_USERNAME = model_config.get('mysql', "SBINHAI1_USERNAME")
SBINHAI1_PASSWORD = model_config.get('mysql', "SBINHAI1_PASSWORD")

shisdb_mysql_url='mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8&autocommit=True'.format(
    SBINHAI1_USERNAME,
    SBINHAI1_PASSWORD,
    SBINHAI1_HOSTNAME,
    SBINHAI1_PORT,
    SBINHAI1_DATABASE
)
sbinhai1_engine = create_engine(shisdb_mysql_url,
                       echo=False,
                       max_overflow=5, pool_size=30, pool_timeout=10, pool_pre_ping=True,pool_recycle=1800)


_sbinhai1_session = scoped_session(sessionmaker(sbinhai1_engine,autoflush=True))

sbinhai1_Base = declarative_base(sbinhai1_engine)
sbinhai1_session = _sbinhai1_session()




